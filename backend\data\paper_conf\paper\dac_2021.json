[{"primary_key": "2117483", "vector": [], "sparse_vector": [], "title": "Invited: Hardware/Software Co-Synthesis and Co-Optimization for Autonomous Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "With ever more complicated functionalities being integrated in modern autonomous systems, traditional design methods may not remain sufficient to deliver trusted and high-performance systems with stringent temporal, safety and cost efficiency requirements. In this paper, we discuss the limitations of the traditional design methods with the above requirements enforced, in which hardware and software design are often considered separately. To tackle these limitations, this paper presents a novel design solution that synthesizes both software-level and hardware-level design. First, we highlight and analyze the interconnections between software-level methods (e.g. priority assignment and task allocation) and hardware design (e.g. cache and memory management), in terms of the resulting system performance, e.g. latency. Second, by applying the identified interconnections, we propose an optimization framework to produce high-quality synthesized solutions of both software and hardware design based on a set of candidate design methods. In addition, we describe potential research directions derived from the work and major challenges that can be investigated jointly by engineers and researchers from embedded systems, system safety and programming languages communities.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586147"}, {"primary_key": "2117484", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Medical Image Segmentation via Hybrid Trusted Execution Environment.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, it is reported that the-state-of-the-art secure protocol is able to segment a three-dimensional heart CT scan in roughly 3,000 seconds, without revealing any sensitive information related to the parties involved in the computation. In this work, building upon the existing mix-protocol approach, we make use of the trusted execution environment (TEE) to implement a more efficient privacy-preserving medical image segmentation protocol. In the experiment, we show that by offloading the computations of single-party operators to trusted hardware, the latency for a round of privacy-preserving segmentation can be further reduced by 25×.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586198"}, {"primary_key": "2117485", "vector": [], "sparse_vector": [], "title": "TinyML: Current Progress, Research Challenges, and Future Roadmap.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "TinyML: tiny in size, BIG in impact!This paper highlights the current progress, challenges and open research opportunities in the domain of tinyML, benchmarking, and emerging applications for Edge-AI.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586232"}, {"primary_key": "2117486", "vector": [], "sparse_vector": [], "title": "Architecture-aware Precision Tuning with Multiple Number Representation Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Precision tuning trades accuracy for speed and energy savings, usually by reducing the data width, or by switching from floating point to fixed point representations. However, comparing the precision across different representations is a difficult task. We present a metric that enables this comparison, and employ it to build a methodology based on Integer Linear Programming for tuning the data type selection. We apply the proposed metric and methodology to a range of processors, demonstrating an improvement in performance (up to $9 \\times)$ with a very limited precision loss $(\\lt 2.8$% for 90% of the benchmarks) on the PolyBench benchmark suite.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586303"}, {"primary_key": "2117487", "vector": [], "sparse_vector": [], "title": "Optimized Polynomial Multiplier Architectures for Post-Quantum KEM Saber.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON> is one of the four finalists in the ongoing NIST post-quantum cryptography standardization project. A significant portion of <PERSON>ber's computation time is spent on computing polynomial multiplications in polynomial rings with powers-of-two moduli. We propose several optimization strategies for improving the performance of polynomial multiplier architectures for Saber, targeting different hardware platforms and diverse application goals. We propose two high-speed architectures that exploit the smallness of operand polynomials in Saber and can achieve great performance with a moderate area consumption. We also propose a lightweight multiplier that consumes only 541 LUTs and 301 FFs on a small Artix-7 FPGA.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586219"}, {"primary_key": "2117488", "vector": [], "sparse_vector": [], "title": "LolliRAM: A Cross-Layer Design to Exploit Data Locality in Oblivious RAM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Oblivious RAM (ORAM) conceals memory access pattern by translating a single read/write operation into the accesses to a set of randomized locations. The obliviousness is achieved by adding redundancy to the memory system, which comes at the expense of increased performance overhead. In memory systems, locality has always been a critical factor, as accessing the data with temporal or spatial locality would result in a performance gain. Although the two design considerations of obliviousness and locality may seem contradictory at first glance, combining them in a unified design can potentially hide long memory access latency in ORAM without sacrificing provable data security.This paper presents LolliRAM, a cross-layer design to exploit data locality in Oblivious RAM. LolliRAM optimizes ORAM system through two different layers: (i) the data structure in ORAM, and (ii) the fast and secure cache in ORAM controller. The reuse of redundant memory accesses at the layer of data structure in ORAM can effectively reduce memory footprints. Both temporal and spatial locality can be exploited through the elastic grouping of blocks and the optimization at the layer of ORAM controller. We conduct a set of experiments using realistic workloads that are generated from standard benchmarks. Experimental results show that LolliRAM can reduce access latency by 71.57% on average (up to 81.50%) with negligible space overhead in comparison with representative schemes.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586126"}, {"primary_key": "2117489", "vector": [], "sparse_vector": [], "title": "MELOPPR: Software/Hardware Co-design for Memory-efficient Low-latency Personalized PageRank.", "authors": ["<PERSON>xiang Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "Pan Li", "<PERSON><PERSON>"], "summary": "Personalized PageRank (PPR) is a graph algorithm that evaluates the importance of the surrounding nodes from a source node. Widely used in social network related applications such as recommender systems, PPR requires real-time responses (latency) for a better user experience. Existing works either focus on algorithmic optimization for improving precision while neglecting hardware implementations or focus on distributed global graph processing on large-scale systems for improving throughput rather than response time. Optimizing low-latency local PPR algorithm with a tight memory budget on edge devices remains unexplored. In this work, we propose a memory-efficient, low-latency PPR solution, namely MeLoPPR, with largely reduced memory requirement and a flexible trade-off between latency and precision. MeLoPPR is composed of stage decomposition and linear decomposition and exploits the node score sparsity: Through stage and linear decomposition, MeLoPPR breaks the computation on a large graph into a set of smaller sub-graphs, that significantly saves the computation memory; Through sparsity exploitation, MeLoPPR selectively chooses the sub-graphs that contribute the most to the precision to reduce the required computation. In addition, through software/hardware co-design, we propose a hardware implementation on a hybrid CPU and FPGA accelerating platform, that further speeds up the sub-graph computation. We evaluate the proposed MeLoPPR on memory-constrained devices including a personal laptop and Xilinx Kintex-7 KC705 FPGA using six real-world graphs. First, MeLoPPR demonstrates significant memory saving by $1. 5 \\times \\sim 13. 4 \\times$ on CPU and $73 \\times \\sim 8699 \\times$ on FPGA. Second, MeLoPPR allows flexible trade-offs between precision and execution time: when the precision is 80%, the speedup on CPU is up to $15\\times$ and up to $707\\times$ on FPGA; when the precision is around 90%, the speedup is up to $70\\times$ on FPGA.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586129"}, {"primary_key": "2117490", "vector": [], "sparse_vector": [], "title": "PrefixRL: Optimization of Parallel Prefix Circuits using Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we present a reinforcement learning (RL) based approach to designing parallel prefix circuits such as adders or priority encoders that are fundamental to high-performance digital design. Unlike prior methods, our approach designs solutions tabula rasa purely through learning with synthesis in the loop. We design a grid-based state-action representation and an RL environment for constructing legal prefix circuits. Deep Convolutional RL agents trained on this environment produce prefix adder circuits that Pareto-dominate existing baselines with up to 16.0% and 30.2% lower area for the same delay in the 32b and 64b settings respectively. We observe that agents trained with open-source synthesis tools and cell library can design adder circuits that achieve lower area and delay than commercial tool adders in an industrial cell library.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586094"}, {"primary_key": "2117491", "vector": [], "sparse_vector": [], "title": "I/O-GUARD: Hardware/Software Co-Design for I/O Virtualization with Guaranteed Real-time Performance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Yang", "Yun<PERSON> Ma", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "For safety-critical| computer systems, time-predictability and performance are usually required simultaneously in I/O virtualization. However, both requirements are challenging to achieve due to complex I/O access path and resource management at system level and lack of support from preemptive scheduling at I/O hardware level. In this paper, we propose a new framework, I/O-GUARD, which reconstructs the system architecture of I/O virtualization, bringing a dedicated hardware hypervisor to handle resource management throughout the system. The hypervisor improves system real-time performance by enabling preemptive scheduling in I/O virtualization with both analytical and experimental real-time guarantees. Specifically, I/O-GUARD is a First-of-Its-Kind framework for multi-/many-core I/O virtualization.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586156"}, {"primary_key": "2117492", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: An Effective Legalization Algorithm for Heterogeneous FPGAs with Complex Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "Haokai Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The modern FPGA placement problem has become much more challenging than ever with various emerging design constraints, such as the location (including relative location (RLOC) and location range) and chain constraints, which have not been considered in the literature. In this paper, we propose a combinatorial algorithm for FPGA legalization with location and chain constraints. We first identify a virtual range to cluster an instance with the RLOC constraint and formulate minimum cost integer linear programming. Besides, we use an adaptive algorithm to deal with the chain-aware legalization problem for better quality and runtime trade-offs. Finally, a legalization algorithm based on minimum cost maximum flow (MCMF) is used to improve the solution quality further. Compared with the state-of-the-art work, experimental results show that our proposed algorithm can achieve respectively 4.4% and 4.9% smaller average and maximum movements, 1.7% smaller routed wirelength, and 7.6% shorter routing runtime", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586104"}, {"primary_key": "2117493", "vector": [], "sparse_vector": [], "title": "LUT-Based Optimization For ASIC Design Flow.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Eleon<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Look-up Table (LUT) mapping and optimization is an important step in Field Programmable Gate Arrays (FPGAs) design. The effectiveness of LUT synthesis improved dramatically in the last decades, thanks to optimization and mapping innovations naturally tailored for FPGAs. In this paper, we develop a new LUT-based optimization flow that is tailored for the synthesis of Application-Specific Integrated Circuits (ASICs) rather than FPGAs. We enhance LUT mapping to consider the literal/AIG cost of LUT nodes. We extend traditional Boolean methods to simplify and re-shape LUT-networks, targeting the best AIG/mapped-network implementation, after decomposition. Intuitively, literal-driven LUT packing behaves as a powerful fanin-bound node elimination, unveiling higher-order Boolean simplification opportunities. We embed our proposed LUT-based optimization flow, area oriented, in a commercial synthesis tool. Using our methodology, we improve 12 of the best area results in the EPFL synthesis competition. Employed in a commercial EDA flow for ASICs, our LUT optimization reduces area by 1.80%, total negative slack by 0.39%, and switching power by 1.72%, after physical implementation, at 5% runtime cost.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586132"}, {"primary_key": "2117494", "vector": [], "sparse_vector": [], "title": "AID: Attesting the Integrity of Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Due to their crucial role in many decision-making tasks, Deep Neural Networks (DNNs) are common targets for a large array of integrity breaches. In this paper, we propose AID, a novel methodology to Attest the Integrity of DNNs. AID generates a set of test cases called edge-points that can reveal whether a model has been compromised. AID does not require access to parameters of the DNN and can work with a restricted black-box access to the model, which makes it applicable to most real life scenarios. Experimental results show that AID is highly effective and reliable. With at most four edge-points, AID is able to detect eight representative integrity breaches including backdoor, poisoning, and compression attacks, with zero false-positive.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586290"}, {"primary_key": "2117495", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Heterogeneous Circuit Layout Centerline Extraction for Mask Verification.", "authors": ["<PERSON><PERSON><PERSON> Bai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lichong Sun", "<PERSON><PERSON><PERSON>"], "summary": "With the continued feature-size shrinking in modern circuit designs, the layout performance estimation and parasitic import calculation based on the extracted centerline result play an important role in mask verification. Most previous works on layout centerline extraction focus on identifying the connectivity among the devices in a mask layout, with few ones collecting accurate centerline information for mask verification while considering design constraints. In this paper, we first formulate the centerline extraction problem as a Voronoi diagram to collect centerline points. Then, we present a graph-based invalid centerline removal algorithm to generate an initial centerline result. Finally, a complexity-driven centerline optimization method is proposed to further optimize the centerline while considering design constraints. Compared with the commercial 3D-RC parasitic parameter extraction tool RCExplorer and the 1st place in the 2019 EDA Elite Challenge Contest, experimental results show that our algorithm achieves the highest average precision ratio of 99.8% on centerline extraction while satisfying all design constraints in the shortest runtime.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586256"}, {"primary_key": "2117496", "vector": [], "sparse_vector": [], "title": "SPROUT - Smart Power ROUting Tool for Board-Level Exploration and Prototyping.", "authors": ["Rassul Bairamkulov", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The board-level power network design process is governed by system-level parameters such as the number of layers and the ball grid array (BGA) pattern. These parameters influence the characteristics of the resulting system, such as power, speed, and cost. Evaluating the impact of these parameters is, however, challenging. To estimate the reduction in impedance if, for example, additional BGA balls are dedicated to the power delivery system, adjustments to the board layout and an additional impedance extraction process are required. These processes are poorly automated, requiring significant time and labor. Automating power network exploration and prototyping can greatly enhance the power delivery design process by increasing the number of possible design options. With power network exploration and prototyping, the effects of the system parameters on the electrical characteristics can be better understood, providing valuable insight into the early design stages. SPROUT - an automated algorithm for prototyping printed circuit board (PCB) power networks - is presented here. This tool includes the first fully automated algorithm for board-level power network layout synthesis. Two board-level industrial power networks are synthesized using SPROUT where a ball grid array is connected with a power management IC and decoupling capacitors across four voltage domains. The impedance of the resulting layouts is in good agreement with manual PCB layouts while requiring 95% less design time.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586128"}, {"primary_key": "2117497", "vector": [], "sparse_vector": [], "title": "Invited: Security Beyond Bulk Silicon: Opportunities and Challenges of Emerging Devices.", "authors": ["<PERSON><PERSON><PERSON>", "Rosario Cammarota", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While traditional chips in bulk silicon technology are widely used for reliable and highly efficient systems, there are applications that call for devices in other technologies. On the one hand, novel device technologies need to be re-evaluated with respect to potential threats and attacks, and how these can be faced with existing and novel security solutions and methods. On the other hand, emerging device technologies bring opportunities for building the secure systems of the future. In this paper, we will give an overview of applications and security primitives developed in three important emerging device technologies, namely memristors, fully depleted silicon on insulator (FD-SOI) and flexible electronics.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9754222"}, {"primary_key": "2117498", "vector": [], "sparse_vector": [], "title": "Property-driven Automatic Generation of Reduced-ISA Hardware.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the diversity of computing workloads and customers continues to increase, so does the need to customize hardware at low cost for different computing needs. This work focuses on automatic customization of a given hardware, available as a soft or firm IP, through eliminating unneeded or undesired instruction set architecture (ISA) instructions. We present a property-based framework for automatically generating reduced-ISA hardware. Our framework directly operates on a given arbitrary RTL or gate-level netlist, uses property checking to identify gates that are guaranteed to not toggle if only a reduced ISA needs to be supported, and automatically eliminates these untoggleable gates to generate a new design. We show a 14% gate count reduction when the Ibex [19] core is optimized using our framework for the instructions required by a set of embedded (MiBench) workloads. Reduced-ISA versions generated by our framework that support a limited set of ISA extensions and which cannot be generated using Ibex's parameterization options provide 10%47% gate count reduction. For an obfuscated Cortex M0 netlist optimized to support the instructions in the MiBench benchmarks, we observe a 20% area reduction and 18% gate count reduction compared to the baseline core, demonstrating applicability of our framework to obfuscated designs. We demonstrate the scalability of our approach by applying our framework to a 100,000-gate RIDECORE [21] design, showing a 14%17% gate count reduction.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586090"}, {"primary_key": "2117499", "vector": [], "sparse_vector": [], "title": "Convergence of SoC architecture and semiconductor manufacturing through AI/ML systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "AI/ML systems have been deployed in both SoC design/architecture as well as in semiconductor manufacturing over the years. These systems have been independent of each other with process design kits (PDK) being the interface between design and manufacturing. Recent advances in packaging technology and the adoption of chiplet architecture has opened exciting avenues for merged AI/ML systems that bring SoC architecture and manufacturing together. This work explores this opportunity of enabling an AI/ML system that brings manufacturing and design information together for chiplet architecture exploration", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586243"}, {"primary_key": "2117500", "vector": [], "sparse_vector": [], "title": "RoboRun: A Robot Runtime to Exploit Spatial Heterogeneity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The limited onboard energy of autonomous mobile robots poses a tremendous challenge for practical deployment. Hence, efficient computing solutions are imperative. A crucial shortcoming of state-of-the-art computing solutions is that they ignore the robot's operating environment heterogeneity and make static, worst-case assumptions. As this heterogeneity impacts the system's computing payload, an optimal system must dynamically capture these changes in the environment and adjust its computational resources accordingly. This paper introduces RoboRun, a mobile-robot runtime that dynamically exploits the compute-environment synergy to improve performance and energy. We implement RoboRun in the Robot Operating System (ROS) and evaluate it on autonomous drones. We compare RoboRun against a state-of-the-art static design and show 4.5X and 4X improvements in mission time and energy, respectively, as well as a 36% reduction in CPU utilization.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586280"}, {"primary_key": "2117501", "vector": [], "sparse_vector": [], "title": "Scalable Pitch-Constrained Neural Processing Unit for 3D Integration with Event-Based Imagers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Event-based imagers are bio-inspired sensors presenting intrinsic High Dynamic Range and High Acquisition Speed properties. However, noisy pixels and asynchronous readout result in poor energy-efficiency and excessively large output data rates.In this work, we use Convolutional Spiking Neural Network filters to compensate these drawbacks and reduce output bandwidth by 10x.We designed a neuromorphic core as a distributable block that benefits from 3D integration technology with direct and parallel access to 32x32 pixels, enabling reduced frequency operation. Post-layout simulations depict a peak energy efficiency with 2.83pJ per Synaptic Operation (equivalent to 0.093fJ/event/pix) at the nominal literature input event rate.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586099"}, {"primary_key": "2117502", "vector": [], "sparse_vector": [], "title": "DNN-Opt: An RL Inspired Optimization for Analog Circuit Sizing using Deep Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Analog circuit sizing takes a significant amount of manual effort in a typical design cycle. With rapidly developing technology and tight schedules, bringing automated solutions for sizing has attracted great attention. This paper presents DNN-Opt, a Reinforcement Learning (RL) inspired Deep Neural Network (DNN) based black-box optimization framework for analog circuit sizing. The key contributions of this paper are a novel sample-efficient two-stage deep learning optimization framework leveraging RL actor-critic algorithms, and a recipe to extend it on large industrial circuits using critical device identification. Our method shows 5—30x sample efficiency compared to other black-box optimization methods both on small building blocks and on large industrial circuits with better performance metrics. To the best of our knowledge, this is the first application of DNN-based circuit sizing on industrial scale circuits.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586139"}, {"primary_key": "2117503", "vector": [], "sparse_vector": [], "title": "Simultaneous Pre- and Free-assignment Routing for Multiple Redistribution Layers with Irregular Vias.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In modern packaging technology, redistribution layers (RDLs) are often used to redistribute interconnections among multiple chips and between I/O pads and bump pads. For high-density RDL routing, irregular vias, where vias can be placed at arbitrary locations, are adopted to better utilize RDL resources to obtain desired routing solutions. As the problem size increases, however, using irregular vias may suffer from high computation overheads. Moreover, most previous works route pre-assignment (PA) and free-assignment (FA) nets in separate stages, incurring routing resource competition. To remedy these disadvantages, we propose a simultaneous PA and FA routing framework with irregular RDL via planning. In this paper, we first propose a novel partitioning method based on the Voronoi diagram to handle irregular via structures and derive a theoretical upper bound on the number of generated regions. We then propose a chord-based tile model and a net-sequence list to generate non-crossing guides for PA and FA nets on the same routing graph. Finally, we develop a novel geometry-based pattern routing to obtain the final solutions. Experimental results show that our work can achieve 100% routability and an average 30X speedup over the-state-of-the-art work.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586296"}, {"primary_key": "2117504", "vector": [], "sparse_vector": [], "title": "NeurFill: Migrating Full-Chip CMP Simulators to Neural Networks for Model-Based Dummy Filling Synthesis.", "authors": ["<PERSON>z<PERSON> Cai", "Chang<PERSON> Yan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dummy filling is widely applied to significantly improve the planarity of topographic patterns for the chemical mechanical polishing (CMP) process in VLSI manufacturing. This paper proposes a novel model-based dummy filling synthesis framework NeurFill, integrated with multiple starting points-sequential quadratic programming (MSP-SQP) optimization solver. Inside this framework, a full-chip CMP simulator is first migrated to the neural network, achieving $8134 \\times$ speedup on gradient calculation by backward propagation. Multi-modal starting points search is further applied in the framework to obtain satisfying filling quality optimums. The experimental results show that the proposed NeurFill outperforms existing rule- and model-based methods.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586325"}, {"primary_key": "2117505", "vector": [], "sparse_vector": [], "title": "DirectFuzz: Automated Test Generation for RTL Designs using Directed Graybox Fuzzing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A critical challenge in RTL verification is to generate effective test inputs. Recently, RFUZZ proposed to use an automated software testing technique, namely Graybox Fuzzing, to effectively generate test inputs to maximize the coverage of the whole hardware design. For a scenario where a tiny fraction of a large hardware design needs to be tested, the RFUZZ approach is extremely time consuming. In this work, we present DirectFuzz, a directed test generation mechanism. DirectFuzz uses Directed Graybox Fuzzing to generate test inputs targeted towards a module instance, which enables targeted testing. Our experimental results show that DirectFuzz covers the target sites up to 17.5 × faster (2.23 × on average) than RFUZZ on a variety of RTL designs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586289"}, {"primary_key": "2117506", "vector": [], "sparse_vector": [], "title": "HADFL: Heterogeneity-aware Decentralized Federated Learning Framework.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Federated learning (FL) supports training models on geographically distributed devices. However, traditional FL systems adopt a centralized synchronous strategy, putting high communication pressure and model generalization challenge. Existing optimizations on FL either fail to speedup training on heterogeneous devices or suffer from poor communication efficiency. In this paper, we propose HADFL, a framework that supports decentralized asynchronous training on heterogeneous devices. The devices train model locally with heterogeneity-aware local steps using local data. In each aggregation cycle, they are selected based on probability to perform model synchronization and aggregation. Compared with the traditional FL system, HADFL can relieve the central server's communication pressure, efficiently utilize heterogeneous computing power, and can achieve a maximum speedup of 3.15x than decentralized-FedAvg and 4.68x than Pytorch distributed training scheme, respectively, with almost no loss of convergence accuracy.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586101"}, {"primary_key": "2117507", "vector": [], "sparse_vector": [], "title": "SACReD: An Attack Framework on SAC Resistant Delay-PUFs leveraging Bias and Reliability Factors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Aritra Hazra"], "summary": "The S-PUF and S n -PUF designs (proposed in IN-DOCRYPT2019) are one of the contemporary composite strong PUF candidates of the Delay-PUF family that exhibit two distinguishing and notable attributes – (i) it is one of the few PUF constructions which is guided by theoretical analysis of the Strict Avalanche Criteria (SAC) property and not by ad-hoc choices; and (ii) though its construction is quite similar to XOR PUFs, it has very good reliability property unlike the former design due to the introduction of Maiorana-McFarland (M-M) Bent Function. These make S n -PUF to be a very good candidate for strong PUF proposals and an interesting target from the point of view of attackers. In this work, we testify that a novel reliability based machine learning attack can be launched in this architecture against the original authors' claim. Though it is challenging to launch a classical or reliability based ML attack directly, we leverage the bias introduced by the AND operation in the M-M bent function due to its non-linearity property. Our proposed novel attack framework, SACReD, is able to break $S_{8}, S_{10}$ and $S_{12}-$PUF designs, which were originally assumed to be secure, by taking only 400K Challenge-Response Pairs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586249"}, {"primary_key": "2117508", "vector": [], "sparse_vector": [], "title": "Universal Symmetry Constraint Extraction for Analog and Mixed-Signal Circuits with Graph Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiyuan Tang", "<PERSON>", "<PERSON>"], "summary": "Recent research trends in analog layout synthesis aim for a fully automated netlist-to-GDSII design flow with minimum human efforts. Due to the sensitiveness of analog circuit layouts, symmetry matching between critical building blocks and devices can significantly impact the overall circuit performance. Therefore, providing accurate symmetry constraints for automated layout synthesis tools is crucial to achieving high-quality layouts. This paper presents a novel graph-learning-based framework leveraging unsupervised learning to recognize circuit matching structures by making the most of numerous unlabeled circuits. The proposed framework supports both system-level and device-level symmetry constraints extraction for various large-scale analog/mixed-signal systems. Experimental results show that our framework outperforms state-of-the-art symmetry constraint detection algorithms with remarkable accuracy and runtime improvement.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586211"}, {"primary_key": "2117509", "vector": [], "sparse_vector": [], "title": "DyGNN: Algorithm and Architecture Support of Dynamic Pruning for Graph Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, graph neural networks (GNNs) have achieved great success for graph representation learning tasks. Enlightened by the fact that numerous message passing redundancies exist in GNNs, we propose DyGNN, which speeds up GNNs by reducing redundancies. DyGNN is supported by an algorithm and architecture co-design. The proposed algorithm can dynamically prune vertices and edges during execution without accuracy loss. An architecture is designed to support dynamic pruning and transform it into performance improvement. DyGNN opens new directions for accelerating GNNs by pruning vertices and edges. DyGNN gains average $2\\times$ speedup with accuracy improvement of 4% compared with state-of-the-art GNN accelerators.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586298"}, {"primary_key": "2117510", "vector": [], "sparse_vector": [], "title": "Pruning of Deep Neural Networks for Fault-Tolerant Memristor-based Accelerators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Hardware-level reliability is a major concern when deep neural network (DNN) models are mapped to neuromorphic accelerators such as memristor-based crossbars. Manufacturing defects and variations lead to hardware faults in the crossbar. Although memristor-based DNNs are inherently tolerant to these faults and many faults are benign for a given inferencing application, there is still a non-negligible number of critical faults (CFs) in the memristor crossbars that can lead to misclassification. It is therefore important to efficiently identify these CFs so that fault-tolerance solutions can focus on them. In this paper, we present an efficient technique based on machine learning to identify these CFs; CFs can be identified with over 98% accuracy and at a rate that is 20 times faster than a baseline using random fault injection. We next present a fault-tolerance technique that iteratively prunes a DNN by targeting weights that are mapped to CFs in the memristor crossbars. Our results for the CIFAR-10 data set and several benchmark DNNs show that the proposed pruning technique eliminates up to 95% of the CFs with less than 1% DNN inferencing accuracy loss. This reduction in the total number of CFs leads to a 99% savings in the hardware redundancy required for fault tolerance.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586269"}, {"primary_key": "2117511", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Novel Discrete Dynamic Filled Function Algorithm for Acyclic Graph Partitioning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Lichong Sun", "<PERSON>"], "summary": "A parallel simulation that partitions a large circuit into sub-circuits is widely used to reduce simulation runtime. To achieve higher simulation throughput, we shall consider signal directions, and thus the final partitioning solution must be acyclic. In this paper, we model a circuit as a directed graph and consider acyclic graph partitioning to minimize edge cuts. This problem differs from the traditional partitioning problem because of the additional acyclicity constraint. Unlike traditional heuristics that tend to be trapped in local minima, especially for large graphs, we present a novel discrete dynamic filled function algorithm for the acyclic graph partitioning problem. Our algorithm can guarantee convergence and effectively move from one discrete local minimizer to another better one. Experimental results show that our algorithm achieves 8% average cutsize reduction over the state-of-the-art works in a comparable runtime.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586307"}, {"primary_key": "2117512", "vector": [], "sparse_vector": [], "title": "Performance-Driven Simultaneous Partitioning and Routing for Multi-FPGA Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A multi-FPGA system consists of multiple FPGAs connected by physical wires, and a circuit is partitioned to fit each FPGA and routed on the system by such physical wires. Due to the limited numbers of input/output (I/O) pins in an FPGA, however, not all signals can be transmitted between FPGAs directly. Moreover, the routing resource may not be sufficient to accommodate many cross-FPGA signals from circuit partitioning. As a result, input/output time-division multiplexing (TDM) is introduced to send a group of cross-FPGA signals in a routing channel with a timing penalty. To optimize the performance of such a system, we shall develop a simultaneous partitioning and routing algorithm considering the timing penalty caused by I/O TDM. Considering the TDM delay penalty, we propose a simultaneous partitioning and routing algorithm to remedy the insufficiency of the two-stage flow of partitioning followed by routing. Our algorithm consists of two major steps: (1) a novel routing-aware partitioning framework to obtain an initial solution considering irregular, asymmetric connections, and (2) a partition-aware routing scheme to optimize routing in each partitioning pass. Experimental results show that our proposed algorithm can achieve better timing than the classical flow.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586225"}, {"primary_key": "2117513", "vector": [], "sparse_vector": [], "title": "A Finer-Grained Blocking Analysis for Parallel Real-Time Tasks with Spin-Locks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Real-time synchronization is one of the essential theories in real-time systems, and the recent booming of parallel real-time tasks has brought new challenges to the synchronization analysis. As the easy implementation and negligible overheads, spin-locks have received much interest since the study for sequential tasks. However, existing spin-based blocking analyses for parallel tasks are relied on execution-time inflation, and the substantially more accurate inflation-free analysis has not been fathomed yet. Moreover, existing analyses suffer an overrepresentation problem, which can be further exacerbated for parallel tasks with spin-locks. To overcome such pessimism, we propose an improved blocking analysis for non-preemptive spin-locks based on a finer-grained shared resource model. In particular, we consider individual length for each shared resource request and use the state-of-the-art linear optimization technique to achieve a pinpoint inflation-free analysis. Empirical evaluations show that the proposed analysis dominated other state-of-the-art analysis, which further shows the improved accuracy achieved by the proposed approach.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586270"}, {"primary_key": "2117514", "vector": [], "sparse_vector": [], "title": "Skew-Oblivious Data Routing for Data Intensive Applications on FPGAs with HLS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "FPGAs have become emerging computing infrastructures for accelerating applications in datacenters. Meanwhile, high-level synthesis (HLS) tools have been proposed to ease the programming of FPGAs. Even with HLS, irregular data-intensive applications require explicit optimizations, among which multiple processing elements (PEs) with each owning a private BRAM-based buffer are usually adopted to process multiple data per cycle. Data routing, which dynamically dispatches multiple data to designated PEs, avoids data replication in buffers compared to statically assigning data to PEs, hence saving BRAM usage. However, the workload imbalance among PEs vastly diminishes performance when processing skew datasets. In this paper, we propose a skew-oblivious data routing architecture that allocates secondary PEs and schedules them to share the workload of the overloaded PEs at run-time. In addition, we integrate the proposed architecture into a framework called Ditto to minimize the development efforts for applications that require skew handling. We evaluate <PERSON><PERSON> on five commonly used applications: histogram building, data partitioning, pagerank, heavy hitter detection and hyperloglog. The results demonstrate that the generated implementations are robust to skew datasets and outperform the state-of-the-art designs in both throughput and BRAM usage efficiency.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586184"}, {"primary_key": "2117515", "vector": [], "sparse_vector": [], "title": "Reptail: Cutting Storage Tail Latency with Inherent Redundancy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Mission-critical edge applications require both low latency and strict data safety. Although emerging ultra-dense solid-state drives (SSDs) can extend the amount of data edge servers can process, the reduced parallelism can worsen read tail latency and even violate the deadline of mission-critical edge applications. To cut ultra-dense SSDs' read tail latency, we propose Reptail, a co-design of host OS and SSD, that exploits the inherent redundancy in transactional systems. We use journaling file system to show how exposing SSD's internals to host OS's redundancy semantics can improve its read scheduling, thus reducing read tail latency. We evaluate Reptail with diverse workloads and find more than 20% latency improvements in the 95th and 99th percentile.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586218"}, {"primary_key": "2117516", "vector": [], "sparse_vector": [], "title": "Efficient <PERSON><PERSON><PERSON>r for Deep Neural Network Compression.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Power and area-efficient deep neural network (DNN) designs are key in edge applications. Compact DNNs, via compression or quantization, enable such designs by significantly reducing memory footprint. Lossless entropy coding can further reduce the size of networks. It is then critical to provide hardware support for such entropy coding module to fully benefit from the resulted reduced memory requirement. In this work, we introduce Tunstall coding to compress the quantized weights. Tunstall coding can achieve high compression ratio as well as very fast decoding speed on various deep networks. We present two hardware-accelerated decoding techniques that provide streamlined decoding capabilities. We synthesize these designs targeting on FPGA. Results show that we achieve up to 6× faster decoding time versus state-of-the-art decoding methods.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586173"}, {"primary_key": "2117517", "vector": [], "sparse_vector": [], "title": "Leveraging Noise and Aggressive Quantization of In-Memory Computing for Robust DNN Hardware Against Adversarial Input and Weight Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In-memory computing (IMC) substantially improves the energy efficiency of deep neural network (DNNs) hardware by activating many rows together and performing analog computing. The noisy analog IMC induces some amount of accuracy drop in hardware acceleration, which is generally considered as a negative effect. However, in this work, we discover that such hardware intrinsic noise can, on the contrary, play a positive role in enhancing adversarial robustness. To achieve that, we propose a new DNN training scheme that integrates measured IMC hardware noise and aggressive partial sum quantization at the IMC crossbar. We show that this effectively improves the robustness of IMC DNN hardware against both adversarial input and weight attacks. Against black-box adversarial input attacks and bit-flip weight attacks, DNN robustness has improved by up to 10.5% (CFAR-10 accuracy) and 33.6% (number of bit-flips), respectively, compared to conventional DNNs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586233"}, {"primary_key": "2117518", "vector": [], "sparse_vector": [], "title": "Application of Deep Reinforcement Learning to Dynamic Verification of DRAM Designs.", "authors": ["<PERSON><PERSON><PERSON>", "In Huh", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Ko", "Changwook Jeong", "Hyeonsik Son", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Younsik Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a deep neural network based test vector generation method for dynamic verification of memory devices. The proposed method is built on reinforcement learning framework, where the action is input stimulus on device pins and the reward is coverage score of target circuitry. The developed agent efficiently explores high-dimensional and large action space by using policy gradient method with Å-nearest neighbor search, transfer learning, and replay buffer. The generated test vectors attained the coverage score of 100% for fifteen representative circuit blocks of modern DRAM design. The output vector length was only 7% of the human-created vector length.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586282"}, {"primary_key": "2117519", "vector": [], "sparse_vector": [], "title": "DANCE: Differentiable Accelerator/Network Co-Exploration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work presents DANCE, a differentiable approach towards the co-exploration of hardware accelerator and network architecture design. At the heart of DANCE is a differentiable evaluator network. By modeling the hardware evaluation software with a neural network, the relation between the accelerator design and the hardware metrics becomes differentiable, allowing the search to be performed with backpropagation. Compared to the naive existing approaches, our method performs co-exploration in a significantly shorter time, while achieving superior accuracy and hardware cost metrics.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586121"}, {"primary_key": "2117520", "vector": [], "sparse_vector": [], "title": "VLSI Structure-aware Placement for Convolutional Neural Network Accelerator Units.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "AI-dedicated hardware designs are growing dramatically for various AI applications. These designs often contain highly connected circuit structures, reflecting the complicated structure in neural networks, such as convolutional layers and fully-connected layers. As a result, such dense interconnections incur severe congestion problems in physical design that cannot be solved by conventional placement methods. This paper proposes a novel placement framework for CNN accelerator units, which extracts kernels from the circuit and insert kernel-based regions to guide placement and minimize routing congestion. Experimental results show that our framework effectively reduces global routing congestion without wirelength degradation, significantly outperforming leading commercial tools.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586294"}, {"primary_key": "2117521", "vector": [], "sparse_vector": [], "title": "On The Efficiency of Sparse-Tiled Tensor Graph Processing For Low Memory Usage.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The memory space taken to host and process large tensor graphs is a limiting factor for embedded ConvNets. Even though many data-driven compression pipelines have proven their efficacy, this work shows there is still room for optimization at the intersection with compute-oriented optimizations. We demonstrate that tensor pruning via weight sparsification can cooperate with a model-agnostic tiling strategy, leading ConvNets towards a new feasible region of the solution space. The collected results show for the first time fast versions of MobileNets deployed at full scale on an ARM M7 core with 512KB of RAM and 2MB of FLASH memory.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586154"}, {"primary_key": "2117522", "vector": [], "sparse_vector": [], "title": "PRUID: Practical User Interface Distribution for Multi-surface Computing.", "authors": ["<PERSON><PERSON><PERSON>", "Mingsong Lv", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chuancai Gu", "<PERSON>", "<PERSON>"], "summary": "It becomes more and more common for people to have multiple mobile devices. This opens the opportunity of multi-surface computing in which users interact with an app using multiple devices simultaneously. Recently, a system called FLUID was developed, which can distribute User Interface (UI) elements of an app to multiple devices to support multi-surface computing. FLUID enables general, flexible and transparent multi-device interaction, which cannot be achieved by previous approaches such as screen mirroring, app migration, and customized app development on multiple devices. However, the practicality of FLUID is still severely limited because it requires that (1) the app source codes must be available and (2) the same app is pre-installed on all devices. This paper presents PRUID, a UI distribution system that is free from the above-mentioned limitations of FLUID. PRUID captures and extracts relevant information about UI elements to be distributed completely at run time, without requiring the app source code. An app-independent UI agent is designed to dock and render the UI components distributed to the guest device, so pre-installation of the app on guest devices is not required. We developed representative use cases to demonstrate the usage and evaluate the performance of PRUID. The evaluation results show that the extra overhead incurred due to the UI information extraction at run time is marginal and PRUID provides a smooth user experience.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586162"}, {"primary_key": "2117523", "vector": [], "sparse_vector": [], "title": "New Predictor-Based Attacks in Processors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The microarchitectural state held by predictors in modern processors can leak sensitive information. This is the first work to analyze the security of a special type of predictor, the value predictor, and demonstrate new security attacks. The new attacks bypass all the existing predictor defenses which have not yet considered value predictors as sources of vulnerabilities. This work further shows there are many value predictor attack variants, as derived using our new attack model. This paper highlights the importance of security analysis of processor features before they are realized in silicon, so the security is understood at the design time.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586089"}, {"primary_key": "2117524", "vector": [], "sparse_vector": [], "title": "Learning Pareto-Frontier Resource Management Policies for Heterogeneous SoCs: An Information-Theoretic Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Bhat", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile system-on-chips (SoCs) are growing in their complexity and heterogeneity (e.g., Arm's Big-Little architecture) to meet the needs of emerging applications, including games and artificial intelligence. This makes it very challenging to optimally manage the resources (e.g., controlling the number and frequency of different types of cores) at runtime to meet the desired trade-offs among multiple objectives such as performance and energy. This paper proposes a novel information-theoretic framework referred to as PaRMIS to create Pareto-optimal resource management policies for given target applications and design objectives. PaRMIS specifies parametric policies to manage resources and learns statistical models from candidate policy evaluation data in the form of target design objective values. The key idea is to select a candidate policy for evaluation in each iteration guided by statistical models that maximize the information gain about the true Pareto front. Experiments on a commercial heterogeneous SoC show that PaRMIS achieves better Pareto fronts and is easily usable to optimize complex objectives (e.g., performance per Watt) when compared to prior methods", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586283"}, {"primary_key": "2117525", "vector": [], "sparse_vector": [], "title": "Distributed Memory Guard: Enabling Secure Enclave Computing in NoC-based Architectures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emerging applications, like cloud services, are demanding more computational power, while also giving rise to various security and privacy challenges. Current multi-/many-core chip designs boost performance by using Networks-on-Chip (NoC) based architectures. Although NoC-based architectures significantly improve communication concurrency, they have thus far lack adequate security mechanisms such as enforceable process isolation. On the other hand, new security-aware architectures that protect applications and sensitive services in isolated execution environments, i.e., enclaves, have not been extended to provide comprehensive protection for NoC platforms. These enclave-based architectures (i) lack secure enclave-device interaction, (ii) cannot include unmodifiable third-party IP, or (iii) provide flexible enclave memory management.To address these design challenges, we introduce a new hardware security primitive, the Distributed Memory Guard, and design the first security architecture that protects sensitive services in NoC-based enclaves. We provide evaluation of this reference architecture and highlight the fact that one can design a scalable (i.e., NoC-based) and secure (i.e., enclave-based) architecture with minimal hardware complexity and system performance overhead.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586222"}, {"primary_key": "2117526", "vector": [], "sparse_vector": [], "title": "ISA Modeling with Trace Notation for Context Free Property Generation.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The scalable and extendable RISC-V ISA introduced a new level of flexibility in designing highly customizable processors. This flexibility in processor designs adds to the complexity of already complex functional verification process. Although formal methods are increasingly used to exhaustively verify the processors, the required manual effort and verification expertise become the major hurdles in industrial flows. Furthermore, efficient ISA modeling techniques are required that are scalable to multiple ISA extensions and to different architectural variants of a processor. This paper proposes a trace notation for ISA definition to capture the implicit execution behavior of a processor and specific characteristics. The proposed trace notation can be annotated with timing and hierarchy information to adapt the trace to any kind of processor architecture. From this trace notation, a complete set of properties are generated to detect all functional bugs in a processor implementation. The approach requires significantly less manual effort compared to the contemporary techniques. Its industry strength has been demonstrated by formally verifying a wide variety of RISC-V processor implementations with one or more ISA extensions (RV32I, C, Zicsr, M and custom extensions supporting AI acceleration and safety features).", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586264"}, {"primary_key": "2117527", "vector": [], "sparse_vector": [], "title": "SHORE: Hardware/Software Method for Memory Safety Acceleration on RISC-V.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Memory corruption vulnerabilities can lead to software attacks. Pointer-based memory safety protection has been shown as a promising solution covering both out-of-bounds and use-after-free errors. Software only approaches have significant performance overhead. Existing hardware/software implementations are largely limited to proprietary closed-source microprocessors, simulation-only studies or require changes to the input source code.In this paper, we present a novel hardware/software co-design methodology consisting of a RISC-V based processor extended with new instructions and microarchitecture enhancements, enabling faster memory safety checks. A compiler is instrumented to provide security operations taking into account the changes to the processor. The entire system is realized by enhancing a RISC-V Rocket-chip system-on-chip (SoC) 1 . The resultant processor SoC is implemented on an FPGA and evaluated with applications from SPEC 2006 (for generic applications), MiBench (for embedded applications), and Olden benchmark suites for performance. Our experiments show that the proposed approach achieves up to 3. 79X speedup (average 2. 6X) in comparison to the traditional software-based approach for SPEC2006 while possessing an overhead of 6.33% in terms of area. This speedup is better than the state-of-the-art approach. Our security coverage using the NIST Juliet test suite shows better coverage than the software only method.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586293"}, {"primary_key": "2117528", "vector": [], "sparse_vector": [], "title": "High-Performance FPGA-based Accelerator for Bayesian Neural Networks.", "authors": ["Hongxiang Fan", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Neural networks (NNs) have demonstrated their potential in a wide range of applications such as image recognition, decision making or recommendation systems. However, standard NNs are unable to capture their model uncertainty which is crucial for many safety-critical applications including healthcare and autonomous vehicles. In comparison, Bayesian neural networks (BNNs) are able to express uncertainty in their prediction via a mathematical grounding. Nevertheless, BNNs have not been as widely used in industrial practice, mainly because of their expensive computational cost and limited hardware performance. This work proposes a novel FPGA based hardware architecture to accelerate BNNs inferred through Monte Carlo Dropout. Compared with other state-of-the-art BNN accelerators, the proposed accelerator can achieve up to 4 times higher energy efficiency and 9 times better compute efficiency. Considering partial Bayesian inference, an automatic framework is proposed, which explores the trade-off between hardware and algorithmic performance. Extensive experiments are conducted to demonstrate that our proposed framework can effectively find the optimal points in the design space.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586137"}, {"primary_key": "2117529", "vector": [], "sparse_vector": [], "title": "Neuromorphic Algorithm-hardware Codesign for Temporal Pattern Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Neuromorphic computing and spiking neural networks (SNN) mimic the behavior of biological systems and have drawn interest for their potential to perform cognitive tasks with high energy efficiency. However, some factors such as temporal dynamics and spike timings prove critical for information processing but are often ignored by existing works, limiting the performance and applications of neuromorphic computing. On one hand, due to the lack of effective SNN training algorithms, it is difficult to utilize the temporal neural dynamics. Many existing algorithms still treat neuron activation statistically. On the other hand, utilizing temporal neural dynamics also poses challenges to hardware design. Synapses exhibit temporal dynamics, serving as memory units that hold historical information, but are often simplified as a connection with weight. Most current models integrate synaptic activations in some storage medium to represent membrane potential and institute a hard reset of membrane potential after the neuron emits a spike. This is done for its simplicity in hardware, requiring only a \"clear\" signal to wipe the storage medium, but destroys temporal information stored in the neuron.In this work, we derive an efficient training algorithm for Leaky Integrate and Fire neurons, which is capable of training a SNN to learn complex spatial temporal patterns. We achieved competitive accuracy on two complex datasets. We also demonstrate the advantage of our model by a novel temporal pattern association task. Codesigned with this algorithm, we have developed a CMOS circuit implementation for a memristor-based network of neuron and synapses which retains critical neural dynamics with reduced complexity. This circuit implementation of the neuron model is simulated to demonstrate its ability to react to temporal spiking patterns with an adaptive threshold.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586133"}, {"primary_key": "2117530", "vector": [], "sparse_vector": [], "title": "SGL: Spectral Graph Learning from Measurements.", "authors": ["<PERSON><PERSON>"], "summary": "This work introduces a highly-scalable spectral graph densification framework for learning resistor networks with linear measurements, such as node voltages and currents. We prove that given O(logN) pairs of voltage and current measurements, it is possible to recover ultra-sparse N-node resistor networks which can well preserve the effective resistance distances on the graph. In addition, the learned graphs also preserve the structural (spectral) properties of the original graph, which can potentially be leveraged in many circuit design and optimization tasks. We show that the proposed graph learning approach is equivalent to solving the classical graphical Lasso problems with Laplacian-like precision matrices. Through extensive experiments for a variety of real-world test cases, we show that the proposed approach is highly scalable for learning ultrasparse resistor networks without sacrificing solution quality.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586124"}, {"primary_key": "2117531", "vector": [], "sparse_vector": [], "title": "CoSPARSE: A Software and Hardware Reconfigurable SpMV Framework for Graph Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Dong-Hyeon Park", "<PERSON>", "<PERSON>", "<PERSON>", "Michael F. P. O&apos;<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Sparse matrix-vector multiplication (SpMV) is a critical building block for iterative graph analytics algorithms. Typically, such algorithms have a varying active vertex set across iterations. This variability has been used to improve performance by either dynamically switching algorithms between iterations (software) or designing custom accelerators (hardware) for graph analytics algorithms. In this work, we propose a novel framework, CoSPARSE, that employs hardware and software reconfiguration as a synergistic solution to accelerate SpMV-based graph analytics algorithms. Building on previously proposed general-purpose reconfigurable hardware, we implement CoSPARSE as a software layer, abstracting the hardware as a specialized SpMV accelerator. CoSPARSE dynamically selects software and hardware configurations for each iteration and achieves a maximum speedup of 2.0 × compared to the naïve implementation with no reconfiguration. Across a suite of graph algorithms, CoSPARSE outperforms a state-of-the-art shared memory framework, Ligra, on a Xeon CPU with up to 3.51 × better performance and 877 × better energy efficiency.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586114"}, {"primary_key": "2117532", "vector": [], "sparse_vector": [], "title": "Invited: Bambu: an Open-Source Research Framework for the High-Level Synthesis of Complex Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents the open-source high-level synthesis (HLS) research framework Bambu. Bambu provides a research environment to experiment with new ideas across HLS, high-level verification and debugging, FPGA/ASIC design, design flow space exploration, and parallel hardware accelerator design. The tool accepts as input standard C/C++ specifications and compiler intermediate representations (IRs) coming from the well-known Clang/LLVM and GCC compilers. The broad spectrum and flexibility of input formats allow the electronic design automation (EDA) research community to explore and integrate new transformations and optimizations. The easily extendable modular framework already includes many optimizations and HLS benchmarks used to evaluate the QoR of the tool against existing approaches [1]. The integration with synthesis and verification backends (commercial and open-source) allows researchers to quickly test any new finding and easily obtain performance and resource usage metrics for a given application. Different FPGA devices are supported from several different vendors: AMD/Xilinx, Intel/Altera, Lattice Semiconductor, and NanoXplore. Finally, integration with the OpenRoad open-source end-to-end silicon compiler perfectly fits with the recent push towards open-source EDA.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586110"}, {"primary_key": "2117533", "vector": [], "sparse_vector": [], "title": "General Chair&apos;s Message.", "authors": ["<PERSON>"], "summary": "Dear Colleagues, Welcome to the 58 th Design Automation Conference, and welcome back to the beautiful city by the bay— San Francisco! DAC has always been a unique event and this year is no exception. After all, DAC is the only event that brings together the entire design and design automation ecosystem, which includes academic and industrial researchers, electronic executives and managers, IC and systems designers and developers, educators, and of course vendors.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586182"}, {"primary_key": "2117534", "vector": [], "sparse_vector": [], "title": "Safety in Autonomous Driving: Can Tools Offer Guarantees?", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>"], "summary": "Persistent challenges in making autonomous vehicles safe and reliable have hampered their widespread deployment. We believe that formal methods will play an essential role in the enterprise of ensuring AV safety by providing tools for the modeling, verification, synthesis, and runtime assurance of AV systems. In this paper, we outline the progress we and others have made towards this goal, and the challenges that remain.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586292"}, {"primary_key": "2117535", "vector": [], "sparse_vector": [], "title": "InstantNet: Automated Generation and Deployment of Instantaneously Switchable-Precision Networks.", "authors": ["Yonggan Fu", "Zhongzhi Yu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The promise of Deep Neural Network (DNN) powered Internet of Thing (IoT) devices has motivated a tremendous demand for automated solutions to enable fast development and deployment of efficient (1) DNNs equipped with instantaneous accuracy-efficiency trade-off capability to accommodate the time-varying resources at IoT devices and (2) dataflows to optimize DNNs' execution efficiency on different devices. Therefore, we propose InstantNet to automatically generate and deploy instantaneously switchable-precision networks which operates at variable bit-widths. Extensive experiments show that the proposed InstantNet consistently outperforms state-of-the-art designs. Our codes are available at: https://github.com/RICE-EIC/InstantNet.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586254"}, {"primary_key": "2117536", "vector": [], "sparse_vector": [], "title": "A3C-S: Automated Agent Accelerator Co-Search towards Efficient Deep Reinforcement Learning.", "authors": ["Yonggan Fu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhongzhi Yu", "<PERSON><PERSON>"], "summary": "Driven by the explosive interest in applying deep reinforcement learning (DRL) agents to numerous real-time control and decision-making applications, there has been a growing demand to deploy DRL agents to empower daily-life intelligent devices, while the prohibitive complexity of DRL stands at odds with limited on-device resources. In this work, we propose an Automated Agent Accelerator Co-Search (A3C-S) framework, which to our best knowledge is the first to automatically co-search the optimally matched DRL agents and accelerators that maximize both test scores and hardware efficiency. Extensive experiments consistently validate the superiority of our A3C-S over state-of-the-art techniques.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586305"}, {"primary_key": "2117537", "vector": [], "sparse_vector": [], "title": "Topology Agnostic Virtual Channel Assignment and Protocol Level Deadlock Avoidance in a Network-on-Chip.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>"], "summary": "A Virtual Channel (VC) is a Time Division Multiplexed (TDM) slice of a physical channel/link. A crucial step of interconnect synthesis is to assign VCs to traffic that avoids deadlocks while meeting Power, Performance and Area (PPA) objectives. For a Network on Chip (NoC), VC assignment is tightly coupled with topology generation and routing. Inefficient VC assignment can lead to NoCs which may be an order of magnitude inferior in terms of PPA. However, both VC assignment and topology generation are combinatorially hard problems. Thus, combining VC assignment with topology generation makes it difficult to efficiently solve either.In this paper, we present a topology agnostic VC assignment approach for statically routed NoCs. This segregation enables us to solve the VC assignment problem first and then subsequently generate topology. By solving VC assignment first, we reduce the problem space of topology generation leading to NoCs with tighter PPA. We model the VC assignment problem as a Traffic Conflict Graph (TCG), capturing a global view of Quality of Service (QoS), Head-of-Line (HoL) conflicts, burstiness and external protocol level dependencies (e.g. PCIe root-complex). We apply combinatorial optimization techniques on TCG to arrive at an efficient VC assignment, that ensures deadlock free designs. The algorithm has been implemented in production NoC Synthesis tool. Results obtained on more than a dozen multi-million gate System-on-Chip (SoC) demonstrate an average improvement of 30% across metrics such as routers, resizers, power/clock domain converters, latencies etc. while meeting the performance vis-à-vis hand-tuned NoCs. The hand-tuned designs have been manually optimized over several man-months by expert designers, while the tool achieves better results in a few minutes.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586196"}, {"primary_key": "2117538", "vector": [], "sparse_vector": [], "title": "Bayesian Inference Based Robust Computing on Memristor Crossbar.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Yin", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Memristor based crossbars are a promising platform for neural network acceleration. To deploy a trained network model on a memristor crossbar, memristors need to be programmed to realize the trained weights of the network. However, due to process and dynamic variations, deviation of weights from the trained value is inevitable and inference accuracy thus degrades. In this paper, we propose a unified Bayesian inference based framework which connects hardware variations and algorithmic training together for robust computing on memristor crossbars. The framework incorporates different levels of variations into priori weight distribution, and transforms robustness optimization to Bayesian neural network training, where weights of neural networks are optimized to accommodate variations and minimize inference degradation. Simulation results with the proposed framework confirm stable inference accuracy under process and dynamic variations.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586160"}, {"primary_key": "2117539", "vector": [], "sparse_vector": [], "title": "Interactive Analog Layout Editing with Instant Placement Legalization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Analog layout design still relies heavily on manual efforts. Current fully automated flows are not yet able to satisfy the demands of versatile customization and not compatible to the existing manual flows. Interactive layout editing has the potential to bridge the gap between the manual flows and fully automated flows shooting for both performance and productivity. In this paper, we propose an interactive editing framework with instructions for both topological editing and detailed customization. We also propose an effective instant legalization algorithm for fast layout update during the real-time interaction with users.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586234"}, {"primary_key": "2117540", "vector": [], "sparse_vector": [], "title": "An Intelligent Video Processing Architecture for Edge-cloud Video Streaming.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work proposes an intelligent video processing architecture for bandwidth-efficient edge-cloud video streaming. On receiving the bandwidth-saving low-quality video streaming in compressed format, the proposed architecture can perform direct DNN-based video enhancement, e.g., super-resolution and motion-compensated frame interpolation (MCFI), on streams. By utilizing the metadata motion vectors and residuals extracted from the encoded video, our workflow will significantly eliminate the unnecessary pixels being processed by the video-enhancing DNNs, and greatly promote the execution efficiency. The evaluation results on popular datasets show that our architecture can reduce the edge-side processing latency of video-enhancing DNNs by 90% compared to the traditional flow while producing accurate and high-quality videos on edge.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586328"}, {"primary_key": "2117541", "vector": [], "sparse_vector": [], "title": "Gemmini: Enabling Systematic Deep-Learning Architecture Evaluation via Full-Stack Integration.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Ion <PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "DNN accelerators are often developed and evaluated in isolation without considering the cross-stack, system-level effects in real-world environments. This makes it difficult to appreciate the impact of Systemon-Chip (SoC) resource contention, OS overheads, and programming-stack inefficiencies on overall performance/energy-efficiency. To address this challenge, we present Gemmini, an open-source, full-stack DNN accelerator generator. Gemmini generates a wide design-space of efficient ASIC accelerators from a flexible architectural template, together with flexible programming stacks and full SoCs with shared resources that capture system-level effects. Gemmini-generated accelerators have also been fabricated, delivering up to three orders-of-magnitude speedups over high-performance CPUs on various DNN benchmarks.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586216"}, {"primary_key": "2117542", "vector": [], "sparse_vector": [], "title": "An Efficient Algorithm for Sparse Quantum State Preparation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Generating quantum circuits that prepare specific states is an essential part of quantum compilation. Algorithms that solve this problem for general states generate circuits at grow exponentially in the number of qubits. However, in contrast to general states, many practically relevant states are sparse in the standard basis. In this paper we show how sparsity can be used for efficient state preparation. We present a polynomial-time algorithm that generates polynomial-size quantum circuits (linear in the number of nonzero coefficients times number of qubits) that prepare given states, making computer-aided design of sparse state preparation scalable.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586240"}, {"primary_key": "2117543", "vector": [], "sparse_vector": [], "title": "Classifying Computations on Multi-Tenant FPGAs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern data centers leverage large FPGAs to provide low latency, high throughput, and low energy computation. FPGA multi-tenancy is an attractive option to maximize utilization, yet it opens the door to new security threats. In this work, we develop a remote classification pipeline that targets the confidentiality of multi-tenant cloud FPGA environments. We utilize an in-fabric voltage sensor that measures subtle changes in the power distribution network caused by co-located computations. The sensor measurements are given to a classification pipeline that is able to deduce information about co-located applications including the type of computation and its implementation. We study the importance of the trace length and other aspects that affect classification accuracy. Our results show that we can determine if another co-tenant is present with 96% accuracy. We can classify with 98% accuracy whether a power waster circuit is operating. Furthermore, we are able to determine if a cryptographic operation is occuring, differentiate between different cryptographic algorithms (AES and PRESENT) and microarchitectural implementations (Microblaze, ORCA, and PicoRV32).", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586098"}, {"primary_key": "2117544", "vector": [], "sparse_vector": [], "title": "MyML: User-Driven Machine Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Machine learning (ML) on resource-constrained edge devices is expensive and often requires offloading computation to the cloud, which may compromise the privacy of user data. In contrast, the type of data processed at edge devices is user specific and limited to few inference classes. In this work, we explore the opportunity of building smaller, user-specific machine learning models, rather than utilizing a generic, compute-intensive machine learning model that caters to a diverse range of users. We first present a hardware-friendly, light-weight pruning technique to create user-specific models directly on mobile platforms, while simultaneously executing inferences. The proposed technique leverages compute sharing between pruning and inference, customizes the retraining backward-pass and chooses a pruning granularity for efficient processing on edge. We then propose architectural support to prune user-specific models on a systolic edge ML inference accelerator. We demonstrate that user-specific models provide a speedup of $2.3\\times$ over the generic model on mobile CPUs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586320"}, {"primary_key": "2117545", "vector": [], "sparse_vector": [], "title": "A Provably Good and Practically Efficient Algorithm for Common Path Pessimism Removal in Large Designs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Common path pessimism removal (CPPR) is imperative for eliminating redundant pessimism during static timing analysis (STA). However, turning on CPPR can significantly increase the analysis runtime by $10-100\\times$ in large designs. Recent years have seen much research on improving the algorithmic efficiencies of CPPR, but most are architecturally constrained by either the speed-accuracy trade-off or design-specific pruning heuristics. In this paper, we introduce a novel CPPR algorithm that is provably good and practically efficient. We have evaluated our algorithm on large industrial designs and demonstrated promising performance over the current state-of-the-art. As an example, our algorithm outperforms the baseline by $36-135\\times$ faster when generating the top-10K post-CPPR critical paths on a million-gate design. At the extreme, our algorithm with one core is even $4-16\\times$ faster than the baseline with 8 cores.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586085"}, {"primary_key": "2117546", "vector": [], "sparse_vector": [], "title": "GPU-accelerated Path-based Timing Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Path-based Analysis (PBA) is an important step in the design closure flow for reducing slack pessimism. However, PBA is extremely time-consuming. Recent years have seen many parallel PBA algorithms, but most of them are architecturally constrained by the CPU parallelism and do not scale beyond a few threads. To overcome this challenge, we propose in this paper a new fast and accurate PBA algorithm by harnessing the power of graphics processing unit (GPU). We introduce GPU-efficient data structures, high-performance kernels, and efficient CPU-GPU task decomposition strateiges, to accelerate PBA to a new performance milestone. Experimental results show that our method can speed up the state-of-the-art algorithm by $543\\times$ on a design of 1.6 million gates with exact accuracy. At the extreme, our method of 1 CPU and 1 GPU outperforms the state-of-the-art algorithm of 40 CPUs by $25-45\\times$.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586316"}, {"primary_key": "2117547", "vector": [], "sparse_vector": [], "title": "GRAPHSPY: Fused Program Semantic Embedding through Graph Neural Networks for Memory Efficiency.", "authors": ["<PERSON><PERSON>", "Pengcheng Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Production software oftentimes suffers from unnecessary memory inefficiencies caused by inappropriate use of data structures, programming abstractions, or conservative compiler optimizations. Unfortunately, existing works often adopt a whole-program fine-grained monitoring method incurring incredibly high overhead. This work proposes a learning-aided approach to identify unnecessary memory operations, by applying several prevalent graph neural network models to extract program semantics with respect to program structure, execution semantics and dynamic states. Results show that the proposed approach captures memory inefficiencies with high accuracy of 95.27% and only around 17% overhead of the state-of-the-art.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586120"}, {"primary_key": "2117548", "vector": [], "sparse_vector": [], "title": "Ultrafast CPU/GPU Kernels for Density Accumulation in Placement.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Density accumulation is a widely-used primitive operation in physical design, especially for placement. Iterative invocation in the optimization flow makes it one of the runtime bottlenecks. Accelerating density accumulation is challenging due to data dependency and workload imbalance. In this paper, we propose efficient CPU/GPU kernels for density accumulation by decomposing the problem into two phases: constant-time density collection for each instance and a linear-time prefix sum. We develop CPU and GPU dedicated implementations, and demonstrate promising efficiency benefits on tasks from large-scale placement problems.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586149"}, {"primary_key": "2117549", "vector": [], "sparse_vector": [], "title": "Formulating Data-arrival Synchronizers in Integer Linear Programming for CGRA Mapping.", "authors": ["<PERSON><PERSON> Guo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Coarse-grained reconfigurable architecture (CGRA) is a promising programmable device with high performance and power efficiency. The CGRA compilation problem is to map an application onto a 3D time-space model of CGRA. Adding the circuitry of synchronizers can relax the mapping constraint for data alignment in time; and thus, it significantly influences the compilation performance. However, data-departure synchronizers may be infeasible, because an output value may be used multiple times and has a high fan-out. Instead, data-arrival synchronizers can further improve performance and mappability of CGRAs with acceptable overhead, compared to the synchronization methods based on detour routing, register files, and FIFO.In this work, we design two kinds of data-arrival synchronizers and formulate them in an integer linear programming (ILP) based mapping approach. The separate ILP formulations of placement and routing speed up the architecture exploration with synchronizers by up to 3.03x. The experimental study shows that data-arrival synchronizers improve CGRA performance by 19.8% on average. The results of our quantitative study show that synchronizers also improve the mapping success rate by 1.91x on average. In conclusion, CGRAs with appropriate synchronizers have better mappability while using fewer resources.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586267"}, {"primary_key": "2117550", "vector": [], "sparse_vector": [], "title": "Invited: Accelerating Fully Homomorphic Encryption with Processing in Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fully homomorphic encryption (FHE) provides a promising solution for future computing needs by allowing privacy-preserving computation. However, its practical use has been limited by the huge latency overhead it incurs while computing. This is primarily due to the huge size of encrypted data and intermediate processing required to compute on it. In this paper, we present insights into the benefits of accelerating FHE with processing in-memory (PIM). PIM is an excellent match for the FHE since it provides extensive parallelism, in-situ operations, and bit-level granularity. We present FHE-PIM, which implements basic polynomial primitives with PIM and uses them to accelerate key FHE operations in memory. This can significantly make the time-consuming procedure of FHE bootstrapping faster in memory. We compare the speedup of FHE-PIM for various FHE operations with their CPU implementations. FHE-PIM can achieve an estimated average throughput improvement of 88$,397 \\times$ as compared to CPU for FHE arithmetic operations.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586285"}, {"primary_key": "2117551", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Attention in Graph2Seq Neural Networks towards Push-Button Analog IC Placement.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, disruptive research using modern embedding techniques and an attention-based encoder-decoder deep learning (DL) model is conducted to automate analog layout synthesis. Unlike previous legacy-based placement automation mechanisms, the attention-based Graph2Seq model is inherently independent of the number of devices within a circuit topology and their order. Moreover, its unsupervised training does not rely on expensive legacy layout data but only on sizing solutions. Experimental results show that the proposed model generates placement solutions at push-button speed and can generalize to circuit topologies and technological nodes not used in training. Moreover, while being scalable, the model produces placement solutions that compete with highly optimized analog placements and other, order-dependent and non-scalable, DL models.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586177"}, {"primary_key": "2117552", "vector": [], "sparse_vector": [], "title": "BLOwing Trees to the Ground: Layout Optimization of Decision Trees on Racetrack Memory.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern distributed low power systems tend to integrate machine learning algorithms, which are directly executed on the distributed devices (on the edge). In resource constrained setups (e.g. battery driven sensor nodes), the execution of the machine learning models has to be optimized for execution time and energy consumption. Racetrack memory (RTM), an emerging non-volatile memory (NVM), promises to achieve these goals by offering unprecedented integration density, smaller access-latency and reduced energy consumption. However, in order to access data in RTM, it needs to be shifted to the access port first, resulting in latency and energy penalties. In this paper, we propose B.L.O. (Bidirectional Linear Ordering), a novel domain-specific approach for placing decision trees in RTMs. We reduce the total amount of shifts during inference by exploiting the tree structure and estimated access probabilities. We further apply the state-of-the-art methods to place data structures in RTM, without exploiting any domain-specific knowledge, to the decision trees and compare them to B. L.O. We formally prove that the B.L.O. solution has an approximation ratio of 4, i.e., its number of shifts is guaranteed to be at most 4 times the optimal number of shifts for a given decision tree. Throughout the experimental evaluation, we show that for the realistic use case B.L.O. empirically outperforms the state-of-the-art data placement method on average by 54.7% in terms of shifts, 19.2% in terms of runtime and 19.2% in terms of energy consumption.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586167"}, {"primary_key": "2117553", "vector": [], "sparse_vector": [], "title": "Building scalable variational circuit training for machine learning tasks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Parameterized quantum circuits (PQC) have emerged as a quantum analogue of deep neural networks and can be trained for discriminative or generative tasks and can be trained with gradient-based optimization on near-term quantum devices [1], [2], [3]. In the current era of quantum computing, known as the noisy intermediate scale quantum (NISQ) era [4], these devices contain a moderate number of qubits (< 100), and algorithmic performance is strongly impacted by hardware noise. Additionally, the training of PQCs are hybrid algorithms, in which the computational workflow is split between quantum and classical computing platforms.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586171"}, {"primary_key": "2117554", "vector": [], "sparse_vector": [], "title": "Secure Logic Locking with Strain-Protected Nanomagnet Logic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Varun V<PERSON>kat", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Prevention of integrated circuit counterfeiting through logic locking faces the fundamental challenge of securing an obfuscation key against both physical and algorithmic threats. Previous work has focused on strengthening the logic encryption to protect the key against algorithmic attacks, but failed to provide adequate physical security. In this work, we propose a logic locking scheme that leverages the non-volatility of the nanomagnet logic (NML) family to achieve both physical and algorithmic security. Polymorphic NML minority gates protect the obfuscation key against algorithmic attacks, while a strain-inducing shield surrounding the nanomagnets provides physical security via a self-destruction mechanism.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586258"}, {"primary_key": "2117555", "vector": [], "sparse_vector": [], "title": "Circuit Connectivity Inspired Neural Network for Analog Mixed-Signal Functional Modeling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Among different types of regression methods to model Analog/Mixed-Signal (AMS) circuits, the Artificial Neural Network (ANN) is a promising candidate due to its reasonable accuracy and fast evaluation. However, for complex AMS circuits with wide specification ranges, creating an ANN model requires a large training dataset. To reduce the required training dataset's volume, we have proposed a circuit-connectivity-inspired ANN (CCI-NN), including multiple sub-ANNs linked according to the actual circuit connections. For validation, we have employed CCI-NN to model a three-stage amplifier and a current-steering digital-to-analog converter. For a certain modeling accuracy, the training dataset requirement is reduced by 3.5x-7.6x.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586236"}, {"primary_key": "2117556", "vector": [], "sparse_vector": [], "title": "GCiM: A Near-Data Processing Accelerator for Graph Construction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph is widely utilized as a key data structure in many applications like social network and recommendation systems. However, real-world graph construction typically involves massive random memory accesses and distance calculation, resulting in considerable processing time and energy consumptions on CPUs and GPUs. In this work, we present GCiM, a specialized processing-in-memory architecture for efficient graph construction and update. By directly deploying the computing units on the logic layer of the 3D stacked memory, GCiM benefits from memory-level parallelism and further improves the memory access efficiency with both optimized processing ordering and data layout. According to our experiments, GCiM shows 634.64X and 53.29X speedup while consuming 1470.7X and 442.56X less energy compared to CPU and GPU respectively.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586221"}, {"primary_key": "2117557", "vector": [], "sparse_vector": [], "title": "TARe: Task-Adaptive in-situ ReRAM Computing for Graph Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "ReRAM-based Computing-in-Memory (CiM) architecture has been considered an ideal solution to neural networks, by conducting in-situ matrix multiplications without moving the neural parameters from memory cells. However, we found that keeping the parameters static in ReRAM cells, i.e. weight-static processing, is not the sole choice to implement emerging graph neural networks (GNNs) that operate on the input of ultra large graphs. Therefore, we propose TARe, a Task-Adaptive CiM architecture that supports multiple different in-situ computing modes for Graph Learning. With the proposed novel hybrid in-situ computing architecture, TARe achieves 451.98× speedup on average over the baseline in SOTA GNN workloads.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586193"}, {"primary_key": "2117558", "vector": [], "sparse_vector": [], "title": "PRID: Model Inversion Privacy Attacks in Hyperdimensional Learning Systems.", "authors": ["<PERSON>", "Rosario Cammarota", "<PERSON><PERSON><PERSON>"], "summary": "Hyperdimensional Computing (HDC) is introduced as a promising solution for robust and efficient learning on embedded devices with limited resources. Since HDC often runs in a distributed way, edge devices need to share their model with other parties. However, the learned model by itself may expose information of the train data, resulting in a serious privacy concern. This paper is the first effort to show the possibility of a model inversion attack in HDC and provide solutions to overcome the challenges. HDC performs learning tasks after mapping data points into high-dimensional space. We first show the vulnerability of the HDC encoding module by introducing techniques that decode the high-dimensional data back to the original space. Then, we exploit this invertibility to extract the HDC model's information and reconstruct the train data just by accessing the model. To address the privacy challenges we propose two iterative techniques which scrutinize HDC model from a privacy perspective: (i) intelligent noise injection that identifies and randomizes insignificant features of the model in the original space, and (ii) model quantization that removes model's recoverable information while teaches the model iteratively to compensate the possible quality loss. Our evaluation over a wide range of classification problems indicates that our solution reduces the information leakage by 92 %(66 %) while having less than 5 % (3%) impact on the learning accuracy.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586217"}, {"primary_key": "2117559", "vector": [], "sparse_vector": [], "title": "RegHD: Robust and Efficient Regression in Hyper-Dimensional Learning System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Yin", "<PERSON><PERSON><PERSON>"], "summary": "Machine learning (ML) algorithms are key enablers to effectively assimilate and extract information from many generated data in the Internet of Things. However, running ML algorithms often results in extremely slow processing speed and high energy consumption. To achieve real-time performance with high energy efficiency and robustness, we proposed RegHD, the first regression solution based on Hyperdimensional computing. RegHD redesign a regression algorithm using strategies that more closely model the ultimate efficient learning machine: the human brain. RegHD performs regression after mapping data points into high-dimensional space using similarity preserving encoding. Due to the encoder's non-linearity, RegHD learns a regression model in an efficient and linear way. RegHD creates two set of models: Input Model to cluster data points with high similarity, and Regression Model to generate a regression model for each clustered data. During prediction, RegHD computes the output value by the weighted accumulation of all regression models, considering the model confidence obtained during similarity search. To improve RegHD efficiency, we also proposed a framework that enables RegHD model quantization while having no impact on the learning accuracy. Our evaluation shows that RegHD provides 5.6 × and 12.3 × (2.9 × and 4.2 ×) faster and energy efficient training (inference) as compared to state-of-the-art regression algorithms, while providing similar quality of learning.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586284"}, {"primary_key": "2117560", "vector": [], "sparse_vector": [], "title": "TCL: an ANN-to-SNN Conversion with Trainable Clipping Layers.", "authors": ["Nguyen<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Spiking-neural-networks (SNNs) are promising at edge devices since the event-driven operations of SNNs provides significantly lower power compared to analog-neural-networks (ANNs). Although it is difficult to efficiently train SNNs, many techniques to convert trained ANNs to SNNs have been developed. However, after the conversion, a trade-off relation between accuracy and latency exists in SNNs, causing considerable latency in large size datasets such as ImageNet. We present a technique, named as TCL, to alleviate the trade-off problem, enabling the accuracy of 73.87% (VGG-16) and 70.37% (ResNet-34) for ImageNet with the moderate latency of 250 cycles in SNNs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586266"}, {"primary_key": "2117561", "vector": [], "sparse_vector": [], "title": "Approximate Equivalence Checking of Noisy Quantum Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yuan Feng", "<PERSON><PERSON><PERSON> Zhou", "Sanjiang Li"], "summary": "We study the fundamental design automation problem of equivalence checking in the NISQ (Noisy Intermediate-Scale Quantum) computing realm where quantum noise is present inevitably. The notion of approximate equivalence of (possibly noisy) quantum circuits is defined based on the Jamiolkowski fidelity which measures the average distance between output states of two super-operators when the input is chosen at random. By employing tensor network contraction, we present two algorithms, aiming at different situations where the number of noises varies, for computing the fidelity between an ideal quantum circuit and its noisy implementation. The effectiveness of our algorithms is demonstrated by experimenting on benchmarks of real NISQ circuits. When compared with the state-of-the-art implementation incorporated in Qiskit, experimental results show that the proposed algorithms outperform in both efficiency and scalability.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586214"}, {"primary_key": "2117562", "vector": [], "sparse_vector": [], "title": "A Bridge-based Compression Algorithm for Topological Quantum Circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>seng", "<PERSON><PERSON><PERSON>"], "summary": "The topological quantum error correction (TQEC) scheme is promising for scalable and reliable quantum computing. A TQEC circuit can be modeled by a three-dimensional diagram, and the implementation resource of a TQEC circuit is abstracted to its space-time volume. Implementing a quantum algorithm with a reasonable physical qubit number and reasonable computation time is challenging for large-scale practical problems. Therefore, minimizing the space-time volume of a TQEC circuit becomes a crucial issue. Previous work shows that bridge compression can greatly compress TQEC circuits, but it was performed only manually. It is desirable to develop automated compression techniques for TQEC circuits to achieve low-overhead, large-scale quantum computations. In this paper, we present the first work that can automatically perform bridge compression on TQEC circuits. Compared with the state-of-the-art method, experimental results show that our proposed algorithm can averagely reduce space-time volumes by 83%.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586322"}, {"primary_key": "2117563", "vector": [], "sparse_vector": [], "title": "Sensitivity Importance Sampling Yield Analysis and Optimization for High Sigma Failure Rate Estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Zuo<PERSON> Ye", "<PERSON>"], "summary": "The impact of process variation to advanced integrated circuits has become increasingly significant. Traditional sampling based yield analysis and optimization always require large amount of expensive simulations. This paper proposes an All Sensitivity Adversarial Importance Sampling (ASAIS) yield optimization method, which avoids samplings in outer optimization based on sensitivity. Moreover, Fast Sensitivity Importance Sampling (FSIS) yield analysis method is adopted as inner yield analysis to eliminate the sampling using transient sensitivity analysis. Experiments on SRAM show ASAIS generates more than 90X speedup of the entire yield optimization process, while FSIS speedup 3X-I5X over existing methods.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586136"}, {"primary_key": "2117564", "vector": [], "sparse_vector": [], "title": "ZeroBN: Learning Compact Neural Networks For Latency-Critical Edge Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> Subramaniam"], "summary": "Edge devices have been widely adopted to bring deep learning applications onto low power embedded systems, mitigating the privacy and latency issues of accessing cloud servers. The increasingly computational demand of complex neural network models leads to large latency on edge devices with limited resources. Many application scenarios are real-time and have a strict latency constraint, while conventional neural network compression methods are not latency-oriented. In this work, we propose a novel compact neural networks training method to reduce the model latency on latency-critical edge systems. A latency predictor is also introduced to guide and optimize this procedure. Coupled with the latency predictor, our method can guarantee the latency for a compact model by only one training process. The experiment results show that, compared to state-of-the-art model compression methods, our approach can well-fit the 'hard' latency constraint by significantly reducing the latency with a mild accuracy drop. To satisfy a 34ms latency constraint, we compact ResNet-50 with 0.82% of accuracy drop. And for GoogLeNet, we can even increase the accuracy by 0.3%", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586309"}, {"primary_key": "2117565", "vector": [], "sparse_vector": [], "title": "RASA: Efficient Register-Aware Systolic Array Matrix Engine for CPU.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sreenivas Subramoney", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As AI-based applications become pervasive, CPU vendors are starting to incorporate matrix engines within the datapath to boost efficiency. Systolic arrays have been the premier architectural choice as matrix engines in offload accelerators. However, we demonstrate that incorporating them inside CPUs can introduce under-utilization and stalls due to limited register storage to amortize the fill and drain times of the array. To address this, we propose RASA, Register-Aware Systolic Array. We develop techniques to divide an execution stage into several sub-stages and overlap instructions to hide overheads and run them concurrently. RASA-based designs improve performance significantly with negligible area and power overhead.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586257"}, {"primary_key": "2117566", "vector": [], "sparse_vector": [], "title": "Enabling On-Device Model Personalization for Ventricular Arrhythmias Detection by Generative Adversarial Networks.", "authors": ["<PERSON><PERSON>", "Feng Hong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jingtong Hu"], "summary": "Implantable Cardioverter Defibrillator (ICD) is an ultra-low-power device which monitors heart rate and delivers in-time defibrillation on detected ventricular arrhythmias (VAs). The parameters of VAs detection mechanism on each recipient's ICD are supposed to be fine-tuned to obtain accurate detection due to the individual's unique rhythm features. However, the process extremely relies on clinical expertise and thus must be conducted manually and routinely by cardiologists diagnosing massive amount of rhythm data. In this paper, we introduce a novel self-supervised on-device personalization of convolutional neural network (CNNs) for VAs detection. We first propose a computing framework consisting of an edge device and an ICD to enable efficient on-device CNNs personalization and real-time inference respectively. Then, we propose a generative model that learns to synthesize patient-specific intracardiac EGMs signals, which can then be used as personalized training data to improve patient-specific VAs detection performance on ICDs. Evaluations on three detection models show that the self-supervised on-device personalization significantly improve VAs detection performance under a patient-specific setting.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586123"}, {"primary_key": "2117567", "vector": [], "sparse_vector": [], "title": "TensorLib: A Spatial Accelerator Generation Framework for Tensor Algebra.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Tensor algebra finds applications in various domains, and these applications, especially when accelerated on spatial hardware accelerators, can deliver high performance and low power. Spatial hardware accelerator exhibits complex design space. Prior approaches based on manual implementation lead to low programming productivity, rendering thorough design space exploration impossible. In this paper, we propose TensorLib, a framework for generating spatial hardware accelerator for tensor algebra applications. TensorLib is motivated by the observation that, different dataflows share common hardware modules, which can be reused across different designs. To build such a framework, TensorLib first uses Space-Time Transformation to explore different dataflows, which can compactly represent the hardware dataflow using a simple transformation matrix. Next, we identify the common structures of different dataflows and build parameterized hardware module templates with <PERSON><PERSON>. Our generation framework can select the needed hardware modules for each dataflow, connect the modules using a specified interconnection pattern, and automatically generate the complete hardware accelerator design. TensorLib remarkably improves the productivity for the development and optimization of spatial hardware architecture, providing a rich design space with tradeoffs in performance, area, and power. Experiments show that TensorLib can automatically generate hardware designs with different dataflows and achieve 21% performance improvement on FPGA compared to the state-of-the-arts.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586329"}, {"primary_key": "2117568", "vector": [], "sparse_vector": [], "title": "UMOC: Unified Modular Ordering Constraints to Unify Cycle- and Register-Transfer-Level Modeling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose unified modular ordering constraints (UMOC), a novel approach that seamlessly unifies method-based cycle-level (CL) modeling and signal-based register-transfer-level (RTL) modeling. Motivated by the challenges in state-of-the-art CL modeling methodologies and existing CL/RTL composition attempts, UMOC successfully breaks the trade-off between model fidelity and scheduling modularity for CL modeling and provides seamless composition of CL and RTL models. Instead of requiring the designer to specify the global intra-cycle ordering of hardware processes, UMOC eliminates this burden using implicit local ordering constraints of RTL signals and explicit local ordering constraints of CL methods. We implement and evaluate UMOC in PyMTL3, a state-of-the-art open-source Python-based hardware modeling framework.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586130"}, {"primary_key": "2117569", "vector": [], "sparse_vector": [], "title": "TAIT: One-Shot Full-Integer Lightweight DNN Quantization via Tunable Activation Imbalance Transfer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hao Sun", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Both parameter quantization and depthwise convolution are essential measures to provide high-accuracy, lightweight, and resource-friendly solutions when deploying deep neural networks (DNNs) onto edge-AI devices. However, combining the two methodologies may lead to adverse effects: It either suffers from significant accuracy loss or long finetuning time. Besides, contemporary quantization methods are only selectively applied to weight and activation values but not bias and scaling factor values, making them less practical for ASIC/FPGA accelerators. To solve these issues, we propose a novel quantization framework that is effectively optimized for depthwise convolution networks. We discover that the uniformity of the value range within a tensor can serve as a predictor for the tensor's quantization error. Under the guidance of this predictor, we develop a mechanism called Tunable Activation Imbalance Transfer (TAIT), which tunes the value range uniformity between an activated feature map and its latter weights. Moreover, TAIT fully supports full-integer quantization. We demonstrate TAIT on SkyNet and deploy it on FPGA. Compared to the state-of-the-art, our quantization framework and system design achieve 2.2%+ IoU, $2.4 \\times$ speed, and $1.8 \\times$ energy efficiency improvements, without any requirement of finetuning.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586109"}, {"primary_key": "2117570", "vector": [], "sparse_vector": [], "title": "Quantifying Rowhammer Vulnerability for DRAM Security.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Rowhammer is a memory-based attack that leverages capacitive-coupling to induce faults in modern dynamic random-access memory (DRAM). Over the last decade, a significant number of Rowhammer attacks have been presented to reveal that it is a severe security issue capable of causing privilege escalations, launching distributed denial-of-service (DDoS) attacks, and even runtime attack such as control flow hijacking. Moreover, the Rowhammer vulnerability has also been identified and validated in both cloud computing and data center environments, threatening data security and privacy at a large scale. Various solutions have been proposed to counter Rowhammer attacks but existing methods lack a circuit-level explanation of the capacitive-coupling phenomenon in modern DRAMs, the key cause of Rowhammer attacks.In this paper, we develop an analytical model of capacitive-coupling vulnerabilities in DRAMs. We thoroughly analyze all parameters in the mathematical model contributing to the Rowhammer vulnerability and quantify them through real DRAM measurements. We validate the model with different attributions on a wide range of DRAM brands from various manufacturers. Through our model we re-evaluate existing Rowhammer attacks on both DDR3 and DDR4 memory, including the recently developed TRRespass attack. Our analysis presents a new Rowhammer attack insight and will guide future research in this area.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586119"}, {"primary_key": "2117571", "vector": [], "sparse_vector": [], "title": "EMGraph: Fast Learning-Based Electromigration Analysis for Multi-Segment Interconnect Using Graph Convolution Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Sheriff <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "Electromigration (EM) becomes a major concern for VLSI circuits as the technology advances in the nanometer regime. With <PERSON><PERSON><PERSON><PERSON> equations, EM assessment for VLSI circuits remains challenged due to the increasing integrated density. VLSI multisegment interconnect trees can be naturally viewed as graphs. Based on this observation, we propose a new graph convolution network (GCN) model, which is called EMGraph considering both node and edge embedding features, to estimate the transient EM stress of interconnect trees. Compared with recently proposed generative adversarial network (GAN) based stress image-generation method, EMGraph model can learn more transferable knowledge to predict stress distributions on new graphs without retraining via inductive learning. Trained on the large dataset, the model shows less than 1.5% averaged error compared to the ground truth results and is orders of magnitude faster than both COMSOL and state-of-the-art method. It also achieves smaller model size, $4\\times$ accuracy and $14\\times$ speedup over the GAN-based method.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586239"}, {"primary_key": "2117572", "vector": [], "sparse_vector": [], "title": "ST2 GPU: An Energy-Efficient GPU Design with Spatio-Temporal Shared-Thread Speculative Adders.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern GPUs employ thousands of cores, yielding higher performance but also higher power consumption. To meet performance targets while staying within a reasonable power budget, designers have to make these execution cores increasingly more power efficient. One way to increase their power efficiency is to employ power-efficient adders. In this paper, we observe that consecutive arithmetic computations from the same code location are highly correlated and propose ST 2 GPU, a GPU architecture that uses history-based speculative adders that produce guaranteed correct results while saving 70% of the nominal adder power. We estimate that ST 2 GPU saves 21% of the GPU chip energy with practically no performance and area overheads.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586093"}, {"primary_key": "2117573", "vector": [], "sparse_vector": [], "title": "FALCON Down: Breaking FALCON Post-Quantum Signature Scheme through Side-Channel Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes the first side-channel attack on FALCON—a NIST Round-3 finalist for the post-quantum digital signature standard. We demonstrate a known-plaintext attack that uses the electromagnetic measurements of the device to extract the secret signing keys, which then can be used to forge signatures on arbitrary messages. The proposed attack targets the unique floating-point multiplications within FALCON's Fast Fourier Transform through a novel extend-and-prune strategy that extracts the sign, mantissa, and exponent variables without false positives. The extracted floating-point values are then mapped back to the secret key's coefficients. Our attack, notably, does not require pre-characterizing the power profile of the target device or crafting special inputs. Instead, the statistical differences on obtained traces are sufficient to successfully execute our proposed differential electromagnetic analysis. The results on an ARM-Cortex-M4 running the FALCON NIST's reference software show that approximately 10k measurements are sufficient to extract the entire key.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586131"}, {"primary_key": "2117574", "vector": [], "sparse_vector": [], "title": "Rewrite to Reinforce: Rewriting the Binary to Apply Countermeasures against Fault Injection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fault injection attacks can cause errors in software for malicious purposes. Oftentimes, vulnerable points of a program are detected after its development. It is therefore critical for the user of the program to be able to apply last-minute security assurance to the executable file without having access to the source code. In this work, we explore two methodologies based on binary rewriting that aid in injecting countermeasures in the binary file. The first approach injects countermeasures by reassembling the disassembly whereas the second approach leverages a full translation to a high-level IR and lowering that back to the target architecture.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586278"}, {"primary_key": "2117575", "vector": [], "sparse_vector": [], "title": "CascadeHD: Efficient Many-Class Learning Framework Using Hyperdimensional Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The brain-inspired hyperdimensional computing (HDC) gains attention as a light-weight and extremely parallelizable learning solution alternative to deep neural networks. Prior research shows the effectiveness of HDC-based learning on less powerful systems such as edge computing devices. However, the many-class classification problem is beyond the focus of mainstream HDC research; the existing HDC would not provide sufficient quality and efficiency due to its coarse-grained training. In this paper, we propose an efficient many-class learning framework, called CascadeHD, which identifies latent high-dimensional patterns of many classes holistically while learning a hierarchical inference structure using a novel meta-learning algorithm for high efficiency. Our evaluation conducted on the NVIDIA Jetson device family shows that CascadeHD improves the accuracy for many-class classification by up to 18% while achieving 32% speedup compared to the existing HDC.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586235"}, {"primary_key": "2117576", "vector": [], "sparse_vector": [], "title": "Dynamic Chip Clustering and Task Allocation for Real-time Flash.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The goal of this paper is to provide worst-case timing guarantees for real-time I/O requests, while fully utilizing the potential bandwidth for non real-time I/O requests in NAND flash storage systems. We identify a trade-off between flash chip sharing and I/O workload isolation in terms of timing guarantees and bandwidth. By taking such a trade-off into account, we propose a new real-time I/O scheduling framework that enables dynamic isolation between real-time I/O requests to meet all timing constraints and co-scheduling of real-time and non realtime I/O requests to provide high bandwidth utilization. Our in-depth evaluation results show that the proposed approach outperforms existing isolation approaches significantly in terms of both schedulability and bandwidth.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586252"}, {"primary_key": "2117577", "vector": [], "sparse_vector": [], "title": "Micro-bumping, Hybrid Bonding, or Monolithic? A PPA Study for Heterogeneous 3D IC Options.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present three commercial-grade 3D IC designs based on state-of-the-art design technologies, specifically micro-bumping (3D die stacking), hybrid bonding (wafer-on-wafer bonding) and monolithic 3D IC (M3D). To highlight trade-offs present in these three designs, we perform analyses on power, performance, and area and the clock tree. We also model the tier-to-tier interconnection in each 3D IC methodology and analyze signal integrity to assess the reliability of each design. From our experiments, hybrid bonding design shows the best timing improvement of 81.4% when compared to its 2D counterpart, while micro-bumping shows the best reliability among 3D IC designs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586229"}, {"primary_key": "2117578", "vector": [], "sparse_vector": [], "title": "Securing Hardware via Dynamic Obfuscation Utilizing Reconfigurable Interconnect and Logic Blocks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Man<PERSON>j P. D.", "<PERSON><PERSON>"], "summary": "Maximizing profits while minimizing risk in a technologically advanced silicon industry has motivated the globalization of the fabrication process and electronic hardware supply chain. However, with the increasing magnitude of successful hardware attacks, the security of many hardware IPs has been compromised. Many existing security works have focused on resolving a single vulnerability while neglecting other threats. This motivated to propose a novel approach for securing hardware IPs during the fabrication process and supply chain via logic obfuscation by utilizing emerging spin-based devices. Our proposed dynamic obfuscation approach uses reconfigurable logic and interconnects blocks (RIL-Blocks), consisting of Magnetic Random Access Memory (MRAM)-based Look Up Tables and switch boxes flexibility and resiliency against state-of-the-art SAT-based attacks and power side-channel attacks while incurring a small overhead. The proposed Scan Enabled Obfuscation circuitry obfuscates the oracle circuit's responses and further fortifies the logic and routing obfuscation provided by the RIL-Blocks, resembling a defense-in-depth approach. The empirical evaluation of security provided by the proposed RIL-Blocks on the ISCAS benchmark and common evaluation platform (CEP) circuit shows that resiliency comes with reduced overhead while providing resiliency to various hardware security threats.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586242"}, {"primary_key": "2117579", "vector": [], "sparse_vector": [], "title": "Load-Step: A Precise TrustZone Execution Control Framework for Exploring New Side-channel Attacks Like Flush+Evict.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Trusted execution environments (TEEs) are imported into processors to protect sensitive programs against a potentially malicious operating system (OS), though, they are announced not effective in defending microarchitecture ($\\mu$ arch) side-channel attacks. Furthermore, TEE attackers often utilize their high privilege to strengthen attacks by interrupting the execution of victim programs. Maximum temporal resolution is achieved on the x86 platform, which interrupts and measures by every instruction. However, the capability of $\\mu$ arch side-channel attacks and the precision a kernel-privileged attacker can achieve in the TrustZone system are still unexplored. In this paper, we propose Load-Step, a precise framework that periodically interrupts the victim program in the TrustZone system and then conducts $\\mu$ arch side-channel attacks. Our self-designed benchmark shows that Load-Step can invoke interrupts with load-instruction precision. Based on Load-Step, we present Flush+Evict, a new side-channel attack detecting the Arm Cache Coherent Interconnect (ArmCCI). It outperforms Prime+Probe with much higher precision and 282 % of the profiling speed. When attacking the RSA decryption in the latest MbedTLS library, Load-Step can recover the full key by only a single trace in 7.5 seconds. Our work thus breaches the exponent blinding, which aims to defend RSA decryption against side-channel attacks in the MbedTLS library.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586226"}, {"primary_key": "2117580", "vector": [], "sparse_vector": [], "title": "Towards Improving the Trustworthiness of Hardware based Malware Detector using Online Uncertainty Estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Hardware-based Malware Detectors (HMDs) using Machine Learning (ML) models have shown promise in detecting malicious workloads. However, the conventional black-box based machine learning (ML) approach used in these HMDs fail to address the uncertain predictions, including those made on zero-day malware. The ML models used in HMDs are agnostic to the uncertainty that determines whether the model \"knows what it knows,\" severely undermining its trustworthiness. We propose an ensemble-based approach that quantifies uncertainty in predictions made by ML models of an HMD, when it encounters an unknown workload than the ones it was trained on. We test our approach on two different HMDs that have been proposed in the literature. We show that the proposed uncertainty estimator can detect > 90% of unknown workloads for the Power-management based HMD, and conclude that the overlapping benign and malware classes undermine the trustworthiness of the Performance Counter-based HMD.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586288"}, {"primary_key": "2117581", "vector": [], "sparse_vector": [], "title": "Statheros: Compiler for Efficient Low-Precision Probabilistic Programming.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As Edge and IoT computing devices process noisy data or make decisions in uncertain environments, they require frameworks for inexpensive, yet accurate probabilistic inference. Probabilistic programming has emerged as a powerful way for developers to write high-level programs, while abstracting away the implementation details of inference. However, the existing algorithms are slow and often assumed to require precise calculations. We present <PERSON><PERSON><PERSON>, the first compiler for low-level, fixed-point approximation of probabilistic programming. Statheros compiles programs to fixed-point inference procedures and is able to determine the optimal fixed-point type to use. We evaluate Statheros on 13 benchmarks and three embedded platforms. The results show that Statheros-generated code is 11. 5x (Arduino), 3. 8x (PocketBeagle), and 2. 2x (Raspberry Pi) faster than single-precision floating-point computation, with minimal accuracy loss.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586276"}, {"primary_key": "2117582", "vector": [], "sparse_vector": [], "title": "Ultra-Fast CGRA Scheduling to Enable Run Time, Programmable CGRAs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Coarse-Grained Reconfigurable Arrays (CGRAs) can offer both energy-efficiency and high-throughput for embedded systems today. But, one limitation of CGRAs is the extremely long mapping time that can take many hours to complete for a typical workload. This extended mapping time, coupled with the typical use of a fixed CGRA program configuration, significantly limits potential use cases as well as hinders the ability to achieve the required performance and efficiency targets.To overcome these limitations, we propose a new, low-complexity CGRA mapping algorithm that compiles applications in milliseconds instead of hours. This is achieved by the use of key instruction placement guidelines which enable speedups of up to 800,000 $\\times$ while maintaining comparable kernel performance. This result allows, for the first time, the ability to dynamically reconFigure CGRA accelerators to adapt to the scenario at hand, be it an important phase of an application, or a user-generated query or request. Overall, this compiler solution could lay the foundation for improved system throughput and efficiency.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586255"}, {"primary_key": "2117583", "vector": [], "sparse_vector": [], "title": "A Charge-Sharing based 8T SRAM In-Memory Computing for Edge DNN Acceleration.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jongsun Park"], "summary": "This paper presents a charge-sharing based customized 8T SRAM in-memory computing (IMC) architecture. In the proposed IMC approach, the multiply-accumulate (MAC) operation of multi-bit activations and weights is supported using the charge sharing between bit-line (BL) parasitic capacitances. The area-efficient customized 8T SRAM macro can achieve robust and voltage-scalable MAC operations due to the charge-domain computation. We also propose a split capacitor structure-based 5/6-bit reconfigurable successive approximation register analog-to-digital converter (SAR-ADC) to reduce the hardware cost of an analog readout circuit while supporting higher precision MAC operations. The proposed reconfigurable SAR-ADC has been exploited to implement layer-by-layer mixed bit-precisions in convolution layer for increasing energy efficiency with negligible accuracy loss. The 256×64 8T SRAM IMC macro has been implemented using 28nm CMOS process technology. The proposed SRAM macro achieves 11. 20-TOPS/W with a maximum clock frequency of 125MHz at 1. 0V. It also supports supply voltage scaling from 0.5V to 1.1V with the energy efficiency ranging from 8.3-TOPS/W to 35.4-TOPS/W within 1 % accuracy loss.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586103"}, {"primary_key": "2117584", "vector": [], "sparse_vector": [], "title": "Dataflow Mirroring: Architectural Support for Highly Efficient Fine-Grained Spatial Multitasking on Systolic-Array NPUs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present dataflow mirroring, architectural support for low-overhead fine-grained systolic array allocation which overcomes the limitations of prior coarse-grained spatial-multitasking Neural Processing Unit (NPU) architectures. The key idea of dataflow mirroring is to reverse the dataflows of co-located Neural Networks (NNs) in horizontal and/or vertical directions, allowing allocation boundaries to be set between any adjacent rows and columns of a systolic array and supporting up to four-way spatial multitasking. Our detailed experiments using MLPerf NNs and a dataflow-mirroring-augmented NPU prototype which extends Google's TPU with dataflow mirroring shows that dataflow mirroring can significantly improve the multitasking performance by up to 46.4%.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586312"}, {"primary_key": "2117585", "vector": [], "sparse_vector": [], "title": "Efficient Error-Correcting-Code Mechanism for High-Throughput Memristive Processing-in-Memory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Inefficient data transfer between computation and memory inspired emerging processing-in-memory (PIM) technologies. Many PIM solutions enable storage and processing using memristors in a crossbar-array structure, with techniques such as memristor-aided logic (MAGIC) used for computation. This approach provides highly-paralleled logic computation with minimal data movement. However, memristors are vulnerable to soft errors and standard error-correcting-code (ECC) techniques are difficult to implement without moving data outside the memory. We propose a novel technique for efficient ECC implementation along diagonals to support reliable computation inside the memory without explicitly reading the data. Our evaluation demonstrates an improvement of over eight orders of magnitude in reliability (mean time to failure) for an increase of about 26% in computation latency.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586324"}, {"primary_key": "2117586", "vector": [], "sparse_vector": [], "title": "Invited: Drug Discovery Approaches using Quantum Machine Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Congzhou M. Sha", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional drug discovery pipelines can require multiple years and billions of dollars of investment. Deep generative and discriminative models are widely adopted to assist in drug development. Classical machines cannot efficiently reproduce the atypical patterns of quantum computers, which may improve the quality of learned tasks. We propose a suite of quantum machine learning techniques: incorporating generative adversarial networks (GAN), convolutional neural networks (CNN) and variational auto-encoders (VAE) to generate small drug molecules, classify binding pockets in proteins, and generate large drug molecules, respectively.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586268"}, {"primary_key": "2117587", "vector": [], "sparse_vector": [], "title": "BHDL: A Lucid, Expressive, and Embedded Programming Language and System for PCB Designs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Graphical PCB design tools like KiCAD lack support for high-level abstraction such as functions and loops. To improve PCB design productivity, we hereby present BHDL, a programming framework for PCB designs. In its compact and declarative syntax, schematics and layouts can be modeled effectively and expressed concisely. Treating all circuits, even a resistor, as functions, BHDL naturally supports modularized development that builds a complex design up from smaller designs hierarchically. As an embedded Domain Specific Language (eDSL), BHDL allows users to leverage the full feature of the host language for customization and extension. Our Jupyter kernel supports web-based, REPL-style development and generates auto-placed PCBs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586086"}, {"primary_key": "2117588", "vector": [], "sparse_vector": [], "title": "Scaling Deep-Learning Inference with Chiplet-based Architecture and Photonic Interconnects.", "authors": ["Yuan Li", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Chiplet-based architectures have been proposed to scale computing systems for deep neural networks (DNNs). Prior work has shown that for the chiplet-based DNN accelerators, the electrical network connecting the chiplets poses a major challenge to system performance, energy consumption, and scalability. Some emerging interconnect technologies such as silicon photonics can potentially overcome the challenges facing electrical interconnects as photonic interconnects provide high bandwidth density, superior energy efficiency, and ease of implementing broadcast and multicast operations that are prevalent in DNN inference. In this paper, we propose a chiplet-based architecture named SPRINT for DNN inference. SPRINT uses a global buffer to simplify the data transmission between storage and computation, and includes two novel designs: (1) a reconfigurable photonic network that can support diverse communications in DNN inference with minimal implementation cost, and (2) a customized dataflow that exploits the ease of broadcast and multicast feature of photonic interconnects to support highly parallel DNN computations. Simulation studies using ResNet50 DNN model show that SPRINT achieves 46% and 61% execution time and energy consumption reduction, respectively, as compared to other state-of-the-art chiplet-based architectures with electrical or photonic interconnects.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586311"}, {"primary_key": "2117589", "vector": [], "sparse_vector": [], "title": "AppealNet: An Efficient and Highly-Accurate Edge/Cloud Collaborative Architecture for DNN Inference.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Li <PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents AppealNet, a novel edge/cloud collaborative architecture that runs deep learning (DL) tasks more efficiently than state-of-the-art solutions. For a given input, AppealNet accurately predicts on-the-fly whether it can be successfully processed by the DL model deployed on the resource-constrained edge device, and if not, appeals to the more powerful DL model deployed at the cloud. This is achieved by employing a two-head neural network architecture that explicitly takes inference difficulty into consideration and optimizes the tradeoff between accuracy and computation/communication cost of the edge/cloud collaborative architecture. Experimental results on several image classification datasets show up to more than 40% energy savings compared to existing techniques without sacrificing accuracy.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586176"}, {"primary_key": "2117590", "vector": [], "sparse_vector": [], "title": "MobileSwap: Cross-Device Memory Swapping for Mobile Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents MobileSwap, a cross-device memory swapping scheme for mobile devices. It exploits the unbalanced utilization of memory resources across devices. MobileSwap achieves comparable-to-local swapping performance based on existing network infrastructure. This is realized by two novel approaches: resource dedicated swapping for fast swapping among devices and app aware swapping for network connectivity considerations. MobileSwap is implemented and deployed on real mobile devices. Experimental results show that MobileSwap can enhance app caching capability by 2x compared with no swapping, and improve performance by 2.3x compared with state-of-the-art remote swapping. More importantly, local swapping induced read-write conflicts are largely removed.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586108"}, {"primary_key": "2117591", "vector": [], "sparse_vector": [], "title": "SpV8: Pursuing Optimal Vectorization and Regular Computation Pattern in SpMV.", "authors": ["Chenyang Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ning <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sparse Matrix-Vector Multiplication (SpMV) plays an important role in many scientific and industry applications, and remains a well-known challenge due to the high sparsity and irregularity. Most existing researches on SpMV try to pursue high vectorization efficiency. However, such approaches may suffer from non-negligible speculation penalty due to their irregular computation patterns. In this paper, we propose SpV8, a novel approach that optimizes both speculation and vectorization in SpMV. Specifically, SpV8 analyzes data distribution in different matrices and row panels, and accordingly applies optimization method that achieves the maximal vectorization with regular computation patterns. We evaluate SpV8 on Intel Xeon CPU and compare with multiple state-of-art SpMV algorithms using 71 sparse matrices. The results show that SpV8 achieves up to 10× speedup (average 2.8×) against the standard MKL SpMV routine, and up to 2.4× speedup (average 1.4×) against the best existing approach. Moreover, SpMV features very low preprocessing overhead in all compared approaches, which indicates SpV8 is highly-applicable in real-world applications.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586251"}, {"primary_key": "2117592", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Physical Adversarial Attacks of Diffractive Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Diffractive Deep Neural Network $(\\mathrm{D}^{2}$ NN) can work as a neural network with the diffraction of light and have demonstrated orders of magnitude performance improvements in computation speed and energy efficiency [1], [2]. As a result, there have been increasing interests in applying $\\mathrm{D}^{2}$ NNs into security-sensitive applications, such as security gate sensing, drug detection, etc. However, the comprehensive vulnerability and robustness of optical neural networks have never been studied. In this work, we develop the first adversarial attack formulations over optical physical meanings, and provide comprehensive analysis of adversarial robustness of $\\mathrm{D}^{2}$ NNs under practical adversarial threats over optical domains, i.e. Phase attack, Amplitude attack, and Complexdomain attack, which can be realized in $\\mathrm{D}^{2}$ NN system using amplitude and phase modulators. We demonstrate that the proposed Complex Fast Gradient Sign Method (Complex-FGSM) can successfully generate minimal-changed (small epsilon) physically feasible adversarial examples targeting pre-trained $\\mathrm{D}^{2}$ NNs model on MNIST-10 dataset, which bring down its accuracy to $\\le 20$% from 95.4%.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586204"}, {"primary_key": "2117593", "vector": [], "sparse_vector": [], "title": "Eco-feller: Minimizing the Energy Consumption of Random Forest Algorithm by an Eco-pruning Strategy over MLC NVRAM.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Random forest has been widely used to classifying objects recently because of its efficiency and accuracy. On the other hand, nonvolatile memory has been regarded as a promising candidate to be a part of a hybrid memory architecture. For achieving the higher accuracy, random forest tends to construct lots of decision trees, and then conducts some post-pruning methods to fell low contribution trees for increasing the model accuracy and space utilization. However, the cost of writing operations is always very high on non-volatile memory. Therefore, writing the to-be-pruned trees into non-volatile memory will significantly waste both energy and time. This work proposed a framework to ease such hurt of training a random forest model. The main spirit of this work is to evaluate the importance of trees before constructing it, and then adopts different writing modes to write the trees to the non-volatile memory space. The experimental results show the proposed framework can significantly mitigate the waste of energy with high accuracy.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586164"}, {"primary_key": "2117594", "vector": [], "sparse_vector": [], "title": "Move-On-Modify: An Efficient yet Crash-Consistent Update Strategy for Interlaced Magnetic Recording.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emerging Interlaced Magnetic Recording (IMR) technology delivers higher areal density for hard drives by interlacing tracks. However, to update the interlaced tracks overlapped by valid data, IMR must entail extra I/Os to rewrite tracks, and the typical read-modify-write (RMW) update strategy further amplifies extra I/Os and deteriorates write performance for ensuring crash consistency. In contrast to the state-of-the-art designs that focus on reducing the occurrence of RMW(s), this paper presents a novel efficient-yet-crash-consistent update strategy, namely move-on-modify (MOM), to completely substitute the typical RMW. By moving data upon updating tracks, MOM essentially circumvents the redundant data restorations of RMW with the ensured crash consistency, and simultaneously takes advantage of these data movements to properly migrate data for reducing the probability of incurring track rewrites without taking extra data migrations. Evaluation results show that MOM not only effectively boosts write performance by at least 77.89% via minimizing extra I/Os, but also greatly benefits the state-of-the-art designs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586091"}, {"primary_key": "2117595", "vector": [], "sparse_vector": [], "title": "Fortifying RTL Locking Against Oracle-Less (Untrusted Foundry) and Oracle-Guided Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Logic locking protects integrated circuits (IC) against intellectual property (IP) theft, IC overbuilding, and hardware Trojan insertion. Prior locking schemes operate after logic synthesis and cannot protect the semantic information embedded into the logic. Register-transfer level (RTL) locking can protect the sensitive IP semantics and are EDA tool-chain agnostic, allowing seamless integration into arbitrary design flows. State-of-the-art RTL locking protects against the untrusted foundry assuming no access to working chip (oracle). However, it does not protect against oracle-based attacks. In this work, we propose to fortify RTL locking to protect against all untrusted entities in the supply chain, including foundry for oracle-less attacks, and test facility and end users for oracle-guided attacks.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586314"}, {"primary_key": "2117596", "vector": [], "sparse_vector": [], "title": "CDAR-DRAM: An In-situ Charge Detection and Adaptive Data Restoration DRAM Architecture for Performance and Energy Efficiency Improvement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> He", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As the capacity of DRAM continues to grow, the refresh operation rapidly becomes the performance and power-efficiency bottleneck. Also, restore time, the time given for recharging cells post access, makes an increasingly large amount of negative impact on performance. To tackle these problems, in this paper, we propose an in-situ charge detection and adaptive data restoration DRAM (CDAR-DRAM) architecture, which can dynamically adjust the refresh rate and also relax the constraints on restore time. The proposed CDAR-DRAM employs a low-cost skewed-inverter-based detector, which can reduce the excessive timing margins that prior work added to guarantee the functionality of leaky DRAM cells under the worst-case temperature condition. Moreover, an adaptive DRAM refresh and restore scheme is proposed, which can switch automatically between two modes: (i) a refresh mode that supports adaptive refresh rate, and (ii) a restore mode that relaxes the constraints on restore time dynamically for cells having sufficient charge. With the transistor-and architecture-level simulations, we evaluate the CDAR-DRAM in an 8-core system across different workloads. Compared with the prior art, the proposed architecture achieves a 9.4% improvement in system performance and a 14.3% reduction in energy consumption, without requiring the time-consuming profiling process which many prior works employed.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586146"}, {"primary_key": "2117597", "vector": [], "sparse_vector": [], "title": "ADROIT: An Adaptive Dynamic Refresh Optimization Framework for DRAM Energy Saving In DNN Training.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiangyu Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To achieve high accuracy, DNN training usually consumes and generates myriads of data, which requires a large DRAM for efficient processing. The refresh power consumption in large DRAM has become a severe problem. Previous refresh energy saving methods have drawbacks on usability, flexibility or training supporting. We propose ADROIT, an adaptive dynamic refresh optimization framework for various DNNs and processing platforms. ADROIT dynamically adjusts the refresh rates for different types of data according to runtime loss feedback in DNN training. Data idle time, lifetime and size are taken into consideration to reduce the search space of refresh rate and remove most refresh operations. Experimental results show that ADROIT can reduce the refresh energy and total DRAM energy in DNN training by up to 98.9% and 24.7% respectively, while maintaining the accuracy. Moreover, ADROIT can automatically apply to different DNNs and hardware platforms without tedious manual configuration.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586265"}, {"primary_key": "2117598", "vector": [], "sparse_vector": [], "title": "A Complete PCB Routing Methodology with Concurrent Hierarchical Routing.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Trends in high pin density and an increasing number of routing layers complicate printed circuit board (PCB) routing, which is categorized as escape and area routing. Traditional escape routing research has focused on escape routing but has not considered the quality of area routing among chip components at the same time. In this work, we propose a complete PCB routing methodology, including simultaneous escape routing (SER), post-SER refinement, and gridless area routing. The SER completes the layer assignment of all nets and produces an escape order ensuring suitable escape and area routing on each layer. Length-matching constraints and differential pair routing are satisfied in each stage of the routing flow. The experiment results indicate that the proposed PCB routing method can complete routings for seven commercial PCB designs, whereas the commercial PCB tool cannot complete any of them.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586143"}, {"primary_key": "2117599", "vector": [], "sparse_vector": [], "title": "NAAS: Neural Accelerator Architecture Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Data-driven, automatic design space exploration of neural accelerator architecture is desirable for specialization and productivity. Previous frameworks focus on sizing the numerical architectural hyper-parameters while neglect searching the PE connectivities and compiler mappings. To tackle this challenge, we propose Neural Accelerator Architecture Search (NAAS) that holistically searches the neural network architecture, accelerator architecture and compiler mapping in one optimization loop. NAAS composes highly matched architectures together with efficient mapping. As a data-driven approach, NAAS rivals the human design Eyeriss by $4.4 \\times$ EDP reduction with 2.7% accuracy improvement on ImageNet under the same computation resource, and offers $1.4 \\times$ to $3.5 \\times$ EDP reduction than only sizing the architectural hyper-parameters.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586250"}, {"primary_key": "2117600", "vector": [], "sparse_vector": [], "title": "Bitwidth-Optimized Energy-Efficient FFT Design via Scaling Information Propagation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Fast Fourier Transform (FFT) is an efficient algorithm widely used in digital signal processing to transform between the time domain and the frequency domain. For fixed-point VLSI implementations, dynamic range growth inevitably occurs at each stage of the FFT operation. However, current methods either waste bitwidth or consume excessive resources when dealing with the dynamic range growth issue. To address this issue, we propose an efficient scaling method called Scaling Information Propagation (SIP) to alleviate the problem of dynamic range growth, which makes full use of bitwidth with much less extra area consumed than the state-of-the-art solutions. In two consecutive transform operations, the SIP method extracts scaling information and makes scaling decisions in the former transform, then executes those in the latter one. We implement the FFT's VLSI architecture in the orthogonal frequency division multiplexing (OFDM) and the holographic video compression (HVC) systems to verify the SIP method. Compared to the state-of-the-art, experimental results after VLSI synthesis show that our method achieves 9.38% energy reduction and 8.36% area savings when requiring 1.02 × 10 -7 bit error ratio (BER) of the OFDM system, and 33.47% energy reduction and 30.98% area savings when requiring 20dB signal-to-noise ratio (SNR) of the HVC system, respectively.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586168"}, {"primary_key": "2117601", "vector": [], "sparse_vector": [], "title": "REST: Constructing Rectilinear Steiner Minimum Tree via Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>. <PERSON>"], "summary": "Rectilinear Steiner Minimum Tree (RSMT) is the shortest way to interconnect a net's n pins using rectilinear edges only. Constructing the optimal RSMT is NP-complete and nontrivial. In this work, we design a reinforcement learning based algorithm called REST for RSMT construction. After training, REST constructs RSMT of $\\leq 0.36\\%$ length error on average for nets with $\\leq 50$ pins. The average time needed for one net is fewer than 1.9 ms, and is much faster than traditional heuristics of similar quality. This is also the first successful attempt to solve this problem using a machine learning approach.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586209"}, {"primary_key": "2117602", "vector": [], "sparse_vector": [], "title": "F3D: Accelerating 3D Convolutional Neural Networks in Frequency Space Using ReRAM.", "authors": ["<PERSON><PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "3D convolutional neural networks (CNNs) are widely deployed in video analysis. Fast algorithms such as fast Fourier transforms (FFTs) are gaining popularity in reducing computation complexity for their superior capability of replacing convolutions with simpler element-wise multiplications. Conventional frequency-domain dedicated accelerators employ memory hierarchy organization for high throughput but at the expensive costs of a significant amount of data movements and energy consumptions. This paper presents F3D, a processingin-memory frequency-domain accelerator using resistive random access memory (ReRAM). F3D supports frequency-domain complex number multiplications directly in ReRAM-based crossbar architecture. We alleviate the overheads of redundant data movements in ReRAM-based complex number multiplications by data reuse and the inherent symmetry of inputs in the frequency space. Evaluation results demonstrate that F3D outperforms state-of-the-art accelerators with significant improvements in performance and energy efficiency.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586135"}, {"primary_key": "2117603", "vector": [], "sparse_vector": [], "title": "MEGATRON: Software-Managed Device TLB for Shared-Memory FPGA Virtualization.", "authors": ["<PERSON><PERSON><PERSON>", "Jiacheng Ma", "<PERSON><PERSON>", "Linsheng Li", "<PERSON><PERSON>", "Haibing Guan"], "summary": "FPGAs are being virtualized to improve resource utilization in data centers. Memory access performance is essential to FPGA hypervisors for shared-memory FPGA platform, where accelerators access memory spontaneously. DMA remapping with IOMMU provides a handy solution; however, fixed IOMMU can not benefit from the reconfigurability of FPGAs. In this work, we propose MEGATRON, a hybrid address translation service consisting of a hardware TLB and a software page table walker. By integrating MEGATRON into an existing FPGA hypervisor, we conduct a comprehensive analysis of link performance of a multi-link CPU-FPGA platform, and demonstrate the competitiveness of the customizable translation service.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586197"}, {"primary_key": "2117604", "vector": [], "sparse_vector": [], "title": "Invited: Towards Fully Intelligent Transportation through Infrastructure-Vehicle Cooperative Autonomous Driving: Challenges and Opportunities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The infrastructure-vehicle cooperative autonomous driving approach relies on the cooperation between intelligent roads and intelligent vehicles. This approach is not only safer but also more economical compared to the traditional on-vehicle-only autonomous driving. In this paper, we introduce the real-world deployment experiences of infrastructure-vehicle cooperative autonomous driving by PerceptIn, where a three-stage development roadmap is taken: infrastructure-augmented autonomous driving (IAAD), infrastructure-guided autonomous driving (IGAD), and infrastructure-planned autonomous driving (IPAD). We then discuss the future research challenges and opportunities for such approach.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586317"}, {"primary_key": "2117605", "vector": [], "sparse_vector": [], "title": "PETRI: Reducing Bandwidth Requirement in Smart Surveillance by Edge-Cloud Collaborative Adaptive Frame Clustering and Pipelined Bidirectional Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Neural networks running on cloud servers have been widely used in smart surveillance, but they require high bandwidth to upload videos. Edge-cloud collaborative encoding based on ROI (Region-Of-Interest) can reduce bandwidth requirement, but it suffers from inaccurate ROI detection due to feedback latency and undetected new targets. To address the above challenges, we propose an object detection system named PETRI. It adopts a latency-hiding pipeline workflow with adaptive keyframe interval selection for different input videos, and utilizes a retro-tracking method to find undetected targets. While achieving negligible impact on model accuracy, the proposed PETRI can save up to 66.44% and 30.25% bandwidth compared with the cloud only method and the previous state-of-art work respectively.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586088"}, {"primary_key": "2117606", "vector": [], "sparse_vector": [], "title": "Automated Compensation Scheme Design for Operational Amplifier via Bayesian Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chang<PERSON> Yan", "<PERSON><PERSON>"], "summary": "Operational amplifier is a basic component for analog circuit design. The compensation network of an operational amplifier is crucial to improve the stability of the operational amplifier. In this paper, we present an automated compensation scheme design approach for operational amplifiers. We map the behavioral-level description of the operational amplifier to an acyclic graph and transfer the compensation design problem into a topology optimization problem. A feature mapping method is proposed to encode the graph and a bi-level Bayesian optimization approach is proposed to efficiently solve the topology optimization problem. Experimental results show that our proposed method can obtain competitive three-stage operational amplifiers compared to manual designs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586306"}, {"primary_key": "2117607", "vector": [], "sparse_vector": [], "title": "RL-Sizer: VLSI Gate Sizing for Timing Optimization using Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Gate sizing for timing optimization is performed extensively throughout electronic design automation (EDA) flows. However, increasing design sizes and time-to-market pressure force EDA tools to maintain pseudo-linear complexity, thereby limiting the global exploration done by the underlying sizing algorithms. Furthermore, high-performance low-power designs are pushing the envelope on power, performance and area (PPA), creating a need for last mile PPA closure using more powerful algorithms. Reinforcement learning (RL) is a disruptive paradigm that achieves high-quality optimization results beyond traditional algorithms. In this paper, we formulate gate sizing as an RL process, and propose RL-Sizer, an autonomous gate sizing agent, which performs timing optimization in an unsupervised manner. In the experiments, we demonstrate that RL-Sizer can improve the native sizing algorithms of an industry-leading EDA tool, Synopsys IC-Compiler II (ICC2), on 6 commercial designs in advanced process nodes (5 – 16nm). RL-Sizer delivers significantly better total negative slack (TNS) and number of violating endpoints (NVEs) on 4 designs with negligible power overhead, while achieving parity on athe others.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586138"}, {"primary_key": "2117608", "vector": [], "sparse_vector": [], "title": "A Framework for Optimizing CPU-iGPU Communication on Embedded Platforms.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Many modern programmable embedded devices contain CPUs and a GPU that share the same system memory on a single die. Such a unified memory architecture allows the explicit data copying between CPU and integrated GPU (iGPU) to be eliminated with the benefit of significantly improving performance and energy savings. However, to enable such a \"zero-copy\" communication model, many devices either implement intricate cache coherence protocols or they may disable the last level caches. This often leads to strong performance degradation of cache-dependent applications, for which CPU-iGPU data transfer based on standard copy remains the best solution. This paper presents a framework based on a performance model, a set of micro-benchmarks, and a novel zero-copy communication pattern to accurately estimate the potential speedup a CPU-iGPU application may have by considering different communication models (i.e., standard copy, unified memory, or pinned \"zerocopy\"). It shows how the framework can be combined with standard profiler information to efficiently drive the application tuning for a given programmable embedded device.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586304"}, {"primary_key": "2117609", "vector": [], "sparse_vector": [], "title": "DeepStrike: Remotely-Guided Fault Injection Attacks on DNN Accelerator in Cloud-FPGA.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As Field-programmable gate arrays (FPGAs) are widely adopted in clouds to accelerate Deep Neural Networks (DNN), such virtualization environments have posed many new security issues. This work investigates the integrity of DNN FPGA accelerators in clouds. It proposes DeepStrike, a remotely-guided attack based on power glitching fault injections targeting DNN execution. We characterize the vulnerabilities of different DNN layers against fault injections on FPGAs and leverage time-to-digital converter (TDC) sensors to precisely control the timing of fault injections. Experimental results show that our proposed attack can successfully disrupt the FPGA DSP kernel and misclassify the target victim DNN application.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586262"}, {"primary_key": "2117610", "vector": [], "sparse_vector": [], "title": "HDTest: Differential Fuzz Testing of Brain-Inspired Hyperdimensional Computing.", "authors": ["Dongning Ma", "<PERSON><PERSON><PERSON>", "Yu <PERSON>", "<PERSON><PERSON>"], "summary": "Brain-inspired hyperdimensional computing (HDC) is an emerging computational paradigm that mimics brain cognition and leverages hyperdimensional vectors with fully distributed holographic representation and (pseudo)randomness. Compared to other machine learning (ML) methods such as deep neural networks (DNNs), HDC offers several advantages including high energy efficiency, low latency, and one-shot learning, making it a promising alternative candidate on a wide range of applications. However, the reliability and robustness of HDC models have not been explored yet. In this paper, we design, implement, and evaluate HDTest to test HDC model by automatically exposing unexpected or incorrect behaviors under rare inputs. The core idea of HDTest is based on guided differential fuzz testing. Guided by the distance between query hypervector and reference hypervector in HDC, HDTest continuously mutates original inputs to generate new inputs that can trigger incorrect behaviors of HDC model. Compared to traditional ML testing methods, HDTest does not need to manually label the original input. Using handwritten digit classification as an example, we show that HDTest can generate thousands of adversarial inputs with negligible perturbations that can successfully fool HDC models. On average, HDTest can generate around 400 adversarial inputs within one minute running on a commodity computer. Finally, by using the HDTest-generated inputs to retrain HDC models, we can strengthen the robustness of HDC models. To the best of our knowledge, this paper presents the first effort in systematically testing this emerging brain-inspired computational model.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586169"}, {"primary_key": "2117611", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Polynomial Formal Verification of Fast Adders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Despite the recent success of formal verification methods, the computational complexity of most of them is still unknown. It raises serious questions regarding the scalability of the approaches. One of the most successful formal methods to prove the correctness of adders is Binary Decision Diagram (BDD)-based verification. It reports very good results for verification of different adder architectures. However, the computational complexity of BDD-based verification has not been yet fully investigated. In this paper, we calculate the complexity of verifying one of the fastest available adders, i.e., conditional sum adder. Then, we show that the verification of this architecture is possible in polynomial time. Finally, we confirm our theoretical calculations by experimental results.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586107"}, {"primary_key": "2117612", "vector": [], "sparse_vector": [], "title": "Synergically Rebalancing Parallel Execution via DCT and Turbo Boosting.", "authors": ["<PERSON><PERSON>", "Thiarles S. Medeiros", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The increasing use of cloud and HPC systems put more pressure on the efficient utilization of hardware resources to keep costs low. Many dynamic concurrency throttling (DCT) techniques have successfully used to tune the number of executing threads to better balance a parallel application according to its available scalability. Similarly, boosting frequency strategies have been used to speed up the sequential parts' execution. Given that, we propose Poseidon, the first transparent and automatic approach that cooperatively exploits both techniques to rebalance OpenMP applications without any preprocessing, with no code transformation, recompilation, or OS modification.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586201"}, {"primary_key": "2117613", "vector": [], "sparse_vector": [], "title": "SoCCAR: Detecting System-on-Chip Security Violations Under Asynchronous Resets.", "authors": ["<PERSON>ng<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern SoC designs include several reset domains that enable asynchronous partial resets while obviating complete system boot. Unfortunately, asynchronous resets can introduce security vulnerabilities that are difficult to detect through traditional validation. In this paper, we address this problem through a new security validation framework, SoCCCAR, that accounts for asynchronous resets. The framework involves (1) efficient extraction of reset-controlled events while avoiding combinatorial explosion, and (2) concolic testing for systematic exploration of the extracted design space. Our experiments demonstrate that SoCCAR can achieve almost perfect detection accuracy and verification time of a few seconds on realistic SoC designs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586291"}, {"primary_key": "2117614", "vector": [], "sparse_vector": [], "title": "INVITED: kCC-Net for Compression of Biomedical Image Segmentation Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Convolutional neural networks (CNNs) for biomedical image segmentation are often of very large size, resulting in high memory costs and high latency of operations. To ensure CNNs' accommodation of key computing resource constraints in specific applications, network compression is commonly used. However, time-consuming training/validation experiments are often involved when searching for a compressed CNN for a specific imaging application, in order to achieve a desired compromise between the network size and network accuracy. Recognizing that biomedical images tend to have relatively uniform target objects, we present kCC-Net, a framework to reduce the cost of compressing CNNs for biomedical image segmentation. kCC-Net first uses training data complexity and target network architecture to estimate the network accuracy degradation caused by compression and compute a layer-wise multiplier for generating a compressed network, referred to as CC-Net. To enhance kCC-Net's ability to extract rich hierarchical features, we incorporate a multi-scale approach by utilizing multiple submodules of CC-Net to generate a new network which is capable of extracting finer features. Verified using three public biomedical image segmentation datasets, our proposed kCC-Net framework is shown to be effective, retaining up to $\\sim 95$% of the full-sized networks' segmentation accuracy, while utilizing $\\sim 51 x$ fewer network trainable weights (average reduction) of the full-sized networks.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586113"}, {"primary_key": "2117615", "vector": [], "sparse_vector": [], "title": "SeMPE: Secure Multi Path Execution Architecture for Removing Conditional Branch Side Channels.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "One prevalent source of side channel vulnerabilities is the secret-dependent behavior of conditional branches (SDBCB). The state-of-the-art solution relies on Constant-Time Expressions, which require high programming effort and incur high performance overheads. In this paper, we propose SeMPE, an architecture support to eliminate SDBCB without requiring much programming effort while incurring low performance overheads. When a secret-dependent branch is encountered, SeMPE fetches, executes, and commits both paths of the branch, preventing the adversary from inferring secret values from the branching behavior of the program. SeMPE outperforms code generated by FaCT, a constant-time expression language, by up to 18×.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586183"}, {"primary_key": "2117616", "vector": [], "sparse_vector": [], "title": "A Formal Approach to Confidentiality Verification in SoCs at the Register Transfer Level.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a formal verification methodology to detect security-critical bugs in the hardware (HW) and in the hardware/firmware interface of SoCs. Our approach extends Unique Program Execution Checking (UPEC), originally proposed for detecting transient execution side channels, to also detect all functional design bugs that cause confidentiality violations, and to cover not only the processor but also its peripherals. The proposed methodology is particularly effective in capturing security vulnerabilities that are introduced based on cross-modular effects (integration and communication issues) or poorly understood hardware/firmware interaction. Such bugs are known to be hard to detect by previous methods.We demonstrate a compositional approach where vulnerabilities discovered by our method can be used to create restrictions for the software (SW). This supports design fixes not only at the HW but also at the SW level. We present experiments for the Pulpissimo platform (v4.0) where several security-critical bugs were identified (and confirmed), as well as for RocketChip.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586248"}, {"primary_key": "2117617", "vector": [], "sparse_vector": [], "title": "HLock: Locking IPs at the High-Level Language.", "authors": ["<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The introduction of the horizontal business model for the semiconductor industry has introduced trust issues for the integrated circuit supply chain. The most common vulnerabilities related to intellectual properties can be caused by untrusted third-party vendors and malicious foundries. Various techniques have been proposed to lock the design at the gate-level or RTL before sending it to the untrusted foundry for fabrication. However, such techniques have been proven to be easily broken using SAT attacks and machine learning-based attacks. In this paper, we propose HLock, a framework for ensuring hardware protection in the form of locking at the high-level description of the design. Our approach includes a formal analysis of design specifications, assets, and critical operations to determine points in which locking keys are inserted. The locked design is then synthesized using high-level synthesis, which has become an integral part of modern IP design due to its advantages on lesser development and verification efforts. The locking at the higher abstraction with the combination of multiple syntheses shows that HLock delivers superior performance considering attack resiliency (i.e., SAT attack, removal attacks, machine learning-based attacks) and overheads compared to conventional locking techniques. Additionally, HLock provides a dynamic/automatic locking solution for any high-level abstraction design based on performance constraints, attack resiliency, power, and area overheads as well as locking key size, and it is well suited for large-scale designs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586159"}, {"primary_key": "2117618", "vector": [], "sparse_vector": [], "title": "SLAP: A Supervised Learning Approach for Priority Cuts Technology Mapping.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently we have seen many works that leverage Machine Learning (ML) techniques in optimizing Electronic Design Automation (EDA) process. However, the uses of ML techniques are limited to learning forecasting models of existing EDA algorithms, instead of developing novel algorithms. In this work, we focus on designing an novel cut-based technology mapping algorithms assisted by ML techniques, which matches results of exhaustive cut exploration but preserving a small footprint of utilized cuts. The proposed approach has been demonstrated with a wide range of benchmarks with 24% reductions in number of cuts utilized compared to the state-of-the-art, while improving the circuit delay, and Area-Delay-Product (ADP), by average about 10%, 7%, respectively, with a 2% area penalty. Compared to the exhaustive approach, i.e., considering all the cuts, we achieve similar or better results while saving over than $2 \\times $ the number of considered cuts (runtime) on average. Finally, we provide a comprehensive explanation of heuristics learned by the ML model by feature ranking.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586230"}, {"primary_key": "2117619", "vector": [], "sparse_vector": [], "title": "Low-Cost and Effective Fault-Tolerance Enhancement Techniques for Emerging Memories-Based Deep Neural Networks.", "authors": ["Thai-<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) have been found to outperform conventional programming approaches in several applications such as computer vision and natural language processing. Efficient hardware architectures for deploying DNNs on edge devices have been actively studied. Emerging memory technologies with their better scalability, non-volatility, and good read performance are ideal candidates for DNNs which are trained once and deployed over many devices. Emerging memories have also been used in DNNs accelerators for efficient computations of dot-product. However, due to immature manufacturing and limited cell endurance, emerging resistive memories often result in reliability issues like stuck-at faults, which reduce the chip yield and pose a challenge to the accuracy of DNNs. Depending on the state, stuck-at faults may or may not cause error. Fault-tolerance of DNNs can be enhanced by reducing the impact of errors resulting from the stuck-at faults. In this work, we introduce simple and light-weight Intra-block Address remapping and weight encoding techniques to improve the fault-tolerance for DNNs. The proposed schemes effectively work at the network deployment time while preserving the network organization and the original values of the parameters. Experimental results on state-of-the-art DNN models indicate that, with a small storage overhead of just 0.98%, the proposed techniques achieve up to 300× stuck-at faults tolerance capability on Cifar10 dataset and 125× on Imagenet datatset, compared to the baseline DNNs without any fault-tolerance method. By integrating with the existing schemes, the proposed schemes can further enhance the fault resilience of DNNs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586112"}, {"primary_key": "2117620", "vector": [], "sparse_vector": [], "title": "DIALED: Data Integrity Attestation for Low-end Embedded Devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Verifying integrity of software execution in low-end microcontroller units (MCUs) is a well-known open problem. The central challenge is how to securely detect software exploits with minimal overhead, since these MCUs are designed for low cost, low energy and small size. Some recent work yielded inexpensive hardware/software co-designs for remotely verifying code and execution integrity. In particular, a means of detecting unauthorized code modifications and control-flow attacks were proposed, referred to as Remote Attestation (ℛA) and Control-Flow Attestation (CFA), respectively. Despite this progress, detection of data-only attacks remains elusive. Such attacks exploit software vulnerabilities to corrupt intermediate computation results stored in data memory, changing neither the program code nor its control flow. Motivated by lack of any current techniques (for low-end MCUs) that detect these attacks, in this paper we propose, implement and evaluate DIALED, the first Data-Flow Attestation (CFA) technique applicable to the most resource-constrained embedded devices (e.g., TI MSP430). DIALED works in tandem with a companion CFA scheme to detect all (currently known) types of runtime software exploits at fairly low cost.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586180"}, {"primary_key": "2117621", "vector": [], "sparse_vector": [], "title": "LENS: Layer Distribution Enabled Neural Architecture Search in Edge-Cloud Hierarchies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Edge-Cloud hierarchical systems employing intelligence through Deep Neural Networks (DNNs) endure the dilemma of workload distribution within them. Previous solutions proposed to distribute workloads at runtime according to the state of the surroundings, like the wireless conditions. However, such conditions are usually overlooked at design time. This paper addresses this issue for DNN architectural design by presenting a novel methodology, LENS, which administers multi-objective Neural Architecture Search (NAS) for two-tiered systems, where the performance objectives are refashioned to consider the wireless communication parameters. From our experimental search space, we demonstrate that LENS improves upon the traditional solution's Pareto set by 76.47% and 75% with respect to the energy and latency metrics, respectively.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586259"}, {"primary_key": "2117622", "vector": [], "sparse_vector": [], "title": "AutoSVA: Democratizing Formal Verification of RTL Module Interactions.", "authors": ["<PERSON><PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern SoC design relies on the ability to separately verify IP blocks relative to their own specifications. Formal verification (FV) using SystemVerilog Assertions (SVA) is an effective method to exhaustively verify blocks at unit-level. Unfortunately, FV has a steep learning curve and requires engineering effort that discourages hardware designers from using it during RTL module development. We propose AutoSVA, a framework to automatically generate FV testbenches that verify liveness and safety of control logic involved in module interactions. We demonstrate AutoSVA's effectiveness and efficiency on deadlock-critical modules of widely-used open-source hardware projects.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586118"}, {"primary_key": "2117623", "vector": [], "sparse_vector": [], "title": "Theory-Specific Proof Steps Witnessing Correctness of SMT Executions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Ensuring hardware and software correctness increasingly relies on the use of symbolic logic solvers, in particular for satisfiability modulo theories (SMT). However, building efficient and correct SMT solvers is difficult: even state-of-the-art solvers disagree on instance satisfiability. This work presents a system for witnessing unsatisfiability of instances of NP problems, commonly appearing in verification, in a way that is natural to SMT solving. Our implementation of the system seems to often result in significantly smaller witnesses, lower solving overhead, and faster checking time in comparison to existing proof formats that can serve a similar purpose.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586272"}, {"primary_key": "2117624", "vector": [], "sparse_vector": [], "title": "Designing a 2048-Chiplet, 14336-Core Waferscale Processor.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Subramanian S. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Waferscale processor systems can provide the large number of cores, and memory bandwidth required by today's highly parallel workloads. One approach to building waferscale systems is to use a chiplet-based architecture where pre-tested chiplets are integrated on a passive silicon-interconnect wafer. This technology allows heterogeneous integration and can provide significant performance and cost benefits. However, designing such a system has several challenges such as power delivery, clock distribution, waferscale-network design, design for testability and fault-tolerance. In this work, we discuss these challenges and the solutions we employed to design a 2048-chiplet, 14,336-core waferscale processor system.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586194"}, {"primary_key": "2117625", "vector": [], "sparse_vector": [], "title": "Reinforcement Learning-Assisted Cache Cleaning to Mitigate Long-Tail Latency in DM-SMR.", "authors": ["Yungang Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>z<PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "DM-SMR adopts Persistent Cache (PC) to accommodate non-sequential write operations. However, the PC cleaning process induces severe long-tail latency. In this paper, we propose to mitigate the tail latency of PC cleaning by using Reinforcement Learning (RL). Specifically, a real-time lightweight Q-learning model is built to analyze the idle window of I/O workloads, based on which PC cleaning is judiciously scheduled, thereby maximally utilizing the I/O idle window and effectively hiding the tail latency from regular requests. We implement our technique inside a Linux device driver with an emulated SMR drive. Experimental results show that our technique can reduce the tail latency by 57.65% at 99.9th percentile and the average response time by 46.11% compared to a typical SMR design.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586084"}, {"primary_key": "2117626", "vector": [], "sparse_vector": [], "title": "UPTPU: Improving Energy Efficiency of a Tensor Processing Unit through Underutilization Based Power-Gating.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The AI boom is bringing a plethora of domain-specific architectures for Neural Network computations. Google's Tensor Processing Unit (TPU), a Deep Neural Network (DNN) accelerator, has replaced the CPUs/GPUs in its data centers, claiming more than 15 × rate of inference. However, the unprecedented growth in DNN workloads with the widespread use of AI services projects an increasing energy consumption of TPU based data centers. In this work, we parametrize the extreme hardware underutilization in TPU systolic array and propose UPTPU: an intelligent, dataflow adaptive power-gating paradigm to provide a staggering 3.5 × – 6.5× energy efficiency to TPU for different input batch sizes.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586224"}, {"primary_key": "2117627", "vector": [], "sparse_vector": [], "title": "Noise-Robust Deep Spiking Neural Networks with Temporal Information.", "authors": ["Seongsik Park", "<PERSON><PERSON> Lee", "<PERSON><PERSON><PERSON>"], "summary": "Spiking neural networks (SNNs) have emerged as energy-efficient neural networks with temporal information. SNNs have shown a superior efficiency on neuromorphic devices, but the devices are susceptible to noise, which hinders them from being applied in real-world applications. Several studies have increased noise robustness, but most of them considered neither deep SNNs nor temporal information. In this paper, we investigate the effect of noise on deep SNNs with various neural coding methods and present a noise-robust deep SNN with temporal information. With the proposed methods, we have achieved a deep SNN that is efficient and robust to spike deletion and jitter.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586195"}, {"primary_key": "2117628", "vector": [], "sparse_vector": [], "title": "Scaling up HBM Efficiency of Top-K SpMV for Approximate Embedding Similarity on FPGAs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Top-K SpMV is a key component of similarity-search on sparse embeddings. This sparse workload does not perform well on general-purpose NUMA systems that employ traditional caching strategies. Instead, modern FPGA accelerator cards have a few tricks up their sleeve. We introduce a Top-KSpMV FPGA design that leverages reduced precision and a novel packet-wise CSR matrix compression, enabling custom data layouts and delivering bandwidth efficiency often unreachable even in architectures with higher peak bandwidth. With HBM-based boards, we are 100x faster than a multi-threaded CPU implementation and 2x faster than a GPU with 20% higher bandwidth, with 14.2x higher power-efficiency.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586203"}, {"primary_key": "2117629", "vector": [], "sparse_vector": [], "title": "qSeq: Full Algorithmic and Tool Support for Synthesizing Sequential Circuits in Superconducting SFQ Technology.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Synthesizing general nonlinear sequential circuits in superconducting Single Flux Quantum (SFQ) technology is a challenging task involving the proper leveling of cyclic digraphs, handling nested feedback loops, and ensuring the full path balancing property throughout the synthesis process. This paper presents a precise definition of the level of a node in a cyclic digraph and a polynomial time algorithm for the corresponding level assignment and full path balancing in sequential SFQ circuits, including SFQ Finite State Machines (FSMs). A case study is conducted on a 3-bit counter, as an FSM, which has a power consumption of $44. 7 \\mu W$ and $1. 4 \\mu W$ using rapid SFQ and energy-efficient rapid RSFQ cells, respectively, with the local clock frequency of 55GHz (throughput of 11GHz) which is significantly higher than the typical CMOS clock frequencies. More results on larger SFQ circuits are also presented.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586102"}, {"primary_key": "2117630", "vector": [], "sparse_vector": [], "title": "Optimal Memory Allocation and Scheduling for DMA Data Transfers under the LET Paradigm.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Logical Execution Time (LET) paradigm is increasingly used to achieve predictable communications in modern multicore automotive applications. Direct Memory Access (DMA) engines can perform the data copies that are needed in a LET implementation on behalf of the cores with improved parallelism and reduced overheads. However, each DMA transfer operates on contiguous memory areas, and the performance is strongly dependent on the allocation in memory of the variables to be copied. This paper proposes a protocol to perform LET communications with a DMA and presents an optimal memory allocation scheme and scheduling using a mixed-integer linear programming formulation. Experimental results are reported to compare the performance of different communication approaches.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586200"}, {"primary_key": "2117631", "vector": [], "sparse_vector": [], "title": "Heterogeneous Monolithic 3D ICs: EDA Solutions, and Power, Performance, Cost Tradeoffs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we present a novel heterogeneous design of Monolithic 3D ICs along with crucial design flow enhancements and better partitioning methods. The heterogeneous M3D ICs are designed with a combination of low-cost, low-power, and low-performance cells on one die and a higher-cost, power, and performance technology variant on the tier, for heterogeneity. These heterogeneous designs out-perform most 2D, 3D variants in Power-Delay Product and Cost metrics. Using 4 different netlists, we see up-to 23% improvement in Performance per Cost, and 16% improvement of Power Delay Product with heterogeneous M3D compared to the best 2D designs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586246"}, {"primary_key": "2117632", "vector": [], "sparse_vector": [], "title": "Requirement Specification, Analysis and Verification for Autonomous Systems.", "authors": ["<PERSON>"], "summary": "The environments and the goals that an autonomous system needs to be able to understand and act upon are not fully known and clearly identifiable at design time, especially for those systems that interact with the physical world. Still, for applications that require a certain level of assurance, requirements, which include the definition of how a system is supposed to interact with its context, need to be defined, analyzed, refined towards an implementation that can be shown to deliver certain guarantees. We first broadly discuss the importance of the early stages of the design process where requirements are defined, and we then discuss the formalisms needed to capture requirements and refine them towards implementation for autonomous systems.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586208"}, {"primary_key": "2117633", "vector": [], "sparse_vector": [], "title": "StocHD: Stochastic Hyperdimensional System for Efficient and Robust Learning from Raw Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hyperdimensional Computing (HDC) is a neurally-inspired computation model working based on the observation that the human brain operates on high-dimensional representations of data, called hypervector. Although HDC is significantly powerful in reasoning and association of the abstract information, it is weak on features extraction from complex data such as image/video. As a result, most existing HDC solutions rely on expensive pre-processing algorithms for feature extraction. In this paper, we propose StocHD, a novel end-to-end hyperdimensional system that supports accurate, efficient, and robust learning over raw data. Unlike prior work that used HDC for learning tasks, StocHD expands HDC functionality to the computing area by mathematically defining stochastic arithmetic over HDC hypervectors. StocHD enables an entire learning application (including feature extractor) to process using HDC data representation, enabling uniform, efficient, robust, and highly parallel computation. We also propose a novel fully digital and scalable Processing In-Memory (PIM) architecture that exploits the HDC memory-centric nature to support extensively parallel computation. Our evaluation over a wide range of classification tasks shows that StocHD provides, on average, 3.3x and 6.4x (52.3x and 143.Sx) faster and higher energy efficiency as compared to state-of-the-art HDC algorithm running on PIM (NVIDIA GPU), while providing 16x higher computational robustness.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586166"}, {"primary_key": "2117634", "vector": [], "sparse_vector": [], "title": "Cognitive Correlative Encoding for Genome Sequence Matching in Hyperdimensional System.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Pattern matching is one of the key algorithms in identifying and analyzing genomic data. In this paper, we propose HYPERS, a novel framework supporting highly efficient and parallel pattern matching based on HyperDimensional computing (HDC). HYPERS transforms inherent sequential processes of pattern matching to highly-parallelizable computation tasks using HDC. HYPERS exploits HDC memorization to encode and represent the genome sequences using high-dimensional vectors. Then, it combines the genome sequences to generate an HDC reference library. During the matching, HYPERS performs alignment by exact or approximate similarity check of an encoded query with the HDC reference library. HYPERS functionality is supported by theoretical proof, verified by software implementation, and extensively tested on the existing hardware platform. Our evaluation on FPGA shows that HYPERS provides, on average, $ 17.5\\times$ speedup and $ 39.4\\times$ energy efficiency as compared to the state-of-the-art pattern matching tools running on GTX 1080 GPU.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586253"}, {"primary_key": "2117635", "vector": [], "sparse_vector": [], "title": "SpikeDyn: A Framework for Energy-Efficient Spiking Neural Networks with Continual and Unsupervised Learning Capabilities in Dynamic Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Spiking Neural Networks (SNNs) bear the potential of efficient unsupervised and continual learning capabilities because of their biological plausibility, but their complexity still poses a serious research challenge to enable their energy-efficient design for resource-constrained scenarios (like embedded systems, IoT-Edge, etc.). We propose SpikeDyn, a comprehensive framework for energy-efficient SNNs with continual and unsupervised learning capabilities in dynamic environments, for both the training and inference phases. It is achieved through the following multiple diverse mechanisms: 1) reduction of neuronal operations, by replacing the inhibitory neurons with direct lateral inhibitions; 2) a memory- and energy-constrained SNN model search algorithm that employs analytical models to estimate the memory footprint and energy consumption of different candidate SNN models and selects a Pareto-optimal SNN model; and 3) a lightweight continual and unsupervised learning algorithm that employs adaptive learning rates, adaptive membrane threshold potential, weight decay, and reduction of spurious updates. Our experimental results show that, for a network with 400 excitatory neurons, our SpikeDyn reduces the energy consumption on average by 51% for training and by 37% for inference, as compared to the state-of-the-art. Due to the improved learning algorithm, SpikeDyn provides on avg. 21% accuracy improvement over the state-of-the-art, for classifying the most recently learned task, and by 8% on average for the previously learned tasks.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586281"}, {"primary_key": "2117636", "vector": [], "sparse_vector": [], "title": "SparkXD: A Framework for Resilient and Energy-Efficient Spiking Neural Network Inference using Approximate DRAM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Spiking Neural Networks (SNNs) have the potential for achieving low energy consumption due to their biologically sparse computation. Several studies have shown that the off-chip memory (DRAM) accesses are the most energy-consuming operations in SNN processing. However, state-of-the-art in SNN systems do not optimize the DRAM energy-per-access, thereby hindering achieving high energy-efficiency. To substantially minimize the DRAM energy-per-access, a key knob is to reduce the DRAM supply voltage but this may lead to DRAM errors (i.e., the so-called approximate DRAM). Towards this, we propose SparkXD, a novel framework that provides a comprehensive conjoint solution for resilient and energy-efficient SNN inference using low-power DRAMs subjected to voltage-induced errors. The key mechanisms of SparkXD are: (1) improving the SNN error tolerance through fault-aware training that considers bit errors from approximate DRAM, (2) analyzing the error tolerance of the improved SNN model to find the maximum tolerable bit error rate (BER) that meets the targeted accuracy constraint, and (3) energy-efficient DRAM data mapping for the resilient SNN model that maps the weights in the appropriate DRAM location to minimize the DRAM access energy. Through these mechanisms, SparkXD mitigates the negative impact of DRAM (approximation) errors, and provides the required accuracy. The experimental results show that, for a target accuracy within 1% of the baseline design (i.e., SNN without DRAM errors), SparkXD reduces the DRAM energy by ca. 40% on average across different network sizes.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586332"}, {"primary_key": "2117637", "vector": [], "sparse_vector": [], "title": "ASBP: Automatic Structured Bit-Pruning for RRAM-based NN Accelerator.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Network sparsity or pruning is an extensively studied method to optimize the computation efficiency of deep neural networks (DNNs) for CMOS-based accelerators, such as FPGAs and GPUs. Though the RRAM-based accelerator has demonstrated superior performance and energy efficiency for DNN tasks, deploying the sparse neural networks desires dedicated consideration to save resource consumption without introducing the expensive index overhead and sophisticated control. To exploit the potential of sparse neural network design on the RRAM-based accelerator, we propose an automatic structured bit-pruning design, ASBP, to harmonize the optimization objective of DNN sparsity with efficient RRAM deployment. Specifically, ASBP prunes the bits of weight which are split into different crossbars and thus, free the zero-value crossbar when mapping the neural network into RRAM-based accelerators without extra hardware modification. Meanwhile, ASBP employs the reinforcement learning (RL) approach to automatically select the best crossbar-aware bit-sparsity strategy for any given neural network without laborious human efforts. According to our experiments on a set of representative neural networks, ASBP saves up to 79.01% energy consumption and 54.79% area overhead compared to the baseline that deploys the original DNN on the RRAM-based accelerator. Besides, ASBP outperforms the state-of-the-art bit-sparsity design by 1.4x in terms of the energy reduction on the RRAM-based accelerator.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586105"}, {"primary_key": "2117638", "vector": [], "sparse_vector": [], "title": "SmartBoost: Lightweight ML-Driven Boosting for Thermally-Constrained Many-Core Processors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>dr", "<PERSON><PERSON><PERSON>"], "summary": "Dynamic voltage and frequency scaling (DVFS)-based boosting is indispensable for optimizing the performance of thermally-constrained many-core processors. State-of-the-art techniques employ the voltage/frequency (V/D sensitivity of the performance of an application as a boosting metric. This paper demonstrates that this leads to suboptimal boosting decisions because the sensitivities of power and temperature also play a profound impact and need to be included within the optimization. Therefore, we introduce a novel boosting metric that integrates all relevant metrics: the application-dependent V/f sensitivities of performance and power, and the core-dependent sensitivity of the temperature. This new boosting metric is derived at run-time using machine learning via a neural network (NN) model, which accurately estimates the V/f sensitivities of performance and power of a priori unknown applications with diverse and time-varying characteristics. This new metric enables to build a smart, yet lightweight, boosting technique to maximize the performance under a temperature constraint. The experimental results demonstrate a 21 % average improvement of the system performance over the state-of-the-art at a negligible run-time overhead of 0.8 %.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586287"}, {"primary_key": "2117639", "vector": [], "sparse_vector": [], "title": "Invited- NVCell: Standard Cell Layout in Advanced Technology Nodes with Reinforcement Learning.", "authors": ["Haoxing Ren", "<PERSON>"], "summary": "High quality standard cell layout automation in advanced technology nodes is still challenging in the industry today because of complex design rules. In this paper we introduce an automatic standard cell layout generator called NVCell that can generate layouts with equal or smaller area for over 90% of single row cells in an industry standard cell library on an advanced technology node. NVCell leverages reinforcement learning (RL) to fix design rule violations during routing and to generate efficient placements.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586188"}, {"primary_key": "2117640", "vector": [], "sparse_vector": [], "title": "Pruning In Time (PIT): A Lightweight Network Architecture Optimizer for Temporal Convolutional Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Temporal Convolutional Networks (TCNs) are promising Deep Learning models for time-series processing tasks. One key feature of TCNs is time-dilated convolution, whose optimization requires extensive experimentation. We propose an automatic dilation optimizer, which tackles the problem as a weight pruning on the time-axis, and learns dilation factors together with weights, in a single training. Our method reduces the model size and inference latency on a real SoC hardware target by up to 7.4x and 3x, respectively with no accuracy drop compared to a network without dilation. It also yields a rich set of Pareto-optimal TCNs starting from a single model, outperforming hand-designed solutions in both size and accuracy.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586187"}, {"primary_key": "2117641", "vector": [], "sparse_vector": [], "title": "On the Intrinsic Robustness of NVM Crossbars Against Adversarial Attacks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The increasing computational demand of Deep Learning has propelled research in special-purpose inference accelerators based on emerging non-volatile memory (NVM) technologies. Such NVM crossbars promise fast and energy-efficient in-situ Matrix Vector Multiplication (MVM) thus alleviating the long-standing <PERSON> bottleneck in today's digital hardware. However, the analog nature of computing in these crossbars is inherently approximate and results in deviations from ideal output values, which reduces the overall performance of Deep Neural Networks (DNNs) under normal circumstances. In this paper, we study the impact of these non-idealities under adversarial circumstances. We show that the non-ideal behavior of analog computing lowers the effectiveness of adversarial attacks, in both Black-Box and White-Box attack scenarios. In a non-adaptive attack, where the attacker is unaware of the analog hardware, we observe that analog computing offers a varying degree of intrinsic robustness, with a peak adversarial accuracy improvement of 35.34%, 22.69%, and 9.90% for white box PGD (ϵ=1/255, iter =30) for CIFAR-10, CIFAR-100, and ImageNet respectively. We also demonstrate \"Hardware-in-Loop\" adaptive attacks that circumvent this robustness by utilizing the knowledge of the NVM model.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586202"}, {"primary_key": "2117642", "vector": [], "sparse_vector": [], "title": "Shortest Path to Secured Hardware: Domain Oriented Masking with High-Level-Synthesis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Implementing hardware secure against side-channel attacks (SCA) demands significant time and expertise in hardware design. In this paper, we propose a simple and fast approach for synthesizing masked block cipher hardware from a C-code exploiting High-Level-Synthesis (HLS), which allows a very short design time. Compared to previous approaches, our proposal provides a systematic and general flow based on state-of-the-art Domain-Oriented Masking (DOM). We also present a fast security-validation flow for the synthesized circuits at the early design stages using commercial-off-the-shelf CAD tools. Efficacy of the proposed design-flow has been established over a set of representative benchmarks including a masked S-Box and a lightweight block-cipher.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586165"}, {"primary_key": "2117643", "vector": [], "sparse_vector": [], "title": "KV-SSD: What Is It Good For?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "An increasing concern that curbs the widespread adoption of KV-SSD is whether or not offloading host-side operations to the storage device changes device behavior, negatively affecting various applications' overall performance. In this paper, we systematically measure, quantify, and understand the performance of KV-SSD by studying the impact of its distinct components such as indexing, data packing, and key handling on I/O concurrency, garbage collection, and space utilization. Our experiments and analysis uncover that KV-SSD's behavior differs from well-known idiosyncrasies of block-SSD. Proper understanding of its characteristics will enable us to achieve better performance for random, read-heavy, and highly concurrent workloads.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586111"}, {"primary_key": "2117644", "vector": [], "sparse_vector": [], "title": "Invited: End-to-End Secure SoC Lifecycle Management.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The pursuit of manufacturing cost reduction reshaped the conventional system-on-chip (SoC) design and manufacturing flow into the horizontal business model. In this model, the design house loses control of the design during the manufacturing process. Therefore, this shift has introduced potential vulnerabilities at each stage of the flow and provides adversaries ample opportunities to cause piracy, security, and trust concerns. Further, SoCs deployed in IoT, smart, and mission-critical devices contain sensitive assets to perform security-critical applications, requiring an on-chip security engine (SE) to ensure protecting assets and secure operation throughout the lifecycle. In this paper, we present an end-to-end secure SoC lifecycle management flow that establishes trust at each stage of the manufacturing process, prevents potential security threats, provides secure provisioning schemes, and protects the chip from in-field and supply chain vulnerabilities.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586106"}, {"primary_key": "2117645", "vector": [], "sparse_vector": [], "title": "Synthesizing Barrier Certificates of Neural Network Controlled Continuous Systems via Approximations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yuzhe Ji", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The paper presents a barrier certificate based approach to verifying safety properties of closed-loop systems using neural networks as controllers. It deals with the verification problem in the infinite time horizon and exploits the approximated system of the original one to synthesize the candidate barrier certificates, where the behavior of a neural network controller is approximated by a polynomial with a bounded error. Satisfiability Modulo Theories solvers are then utilized to identify real barrier certificates from those candidates. As a barrier certificate can separate the over-approximation of the reachable set from the unsafe region, once it is constructed, the safety property gets proved. We show the advantage of our approach in barrier certificates synthesis by comparing it with the state-of-the-art work on a set of benchmarks.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586327"}, {"primary_key": "2117646", "vector": [], "sparse_vector": [], "title": "CuckoOnsai: An Efficient Memory Authentication Using Amalgam of Cuckoo Filters and Integrity Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The off-chip main memory data can be extracted or tampered by an adversary having physical access to a device. In modern secure designs, such tampering or data attacks can be prevented by storing the integrity tree built on top of encryption counters. However, such approaches have significant performance and on-chip storage overheads. This paper proposes CuckoOnsai, which uses the combination of a novel on-chip per core Cuckoo filter and an off-chip small integrity tree. Compared to the recent competing scheme, CuckoOnsai improves performance by 13.1% and reduces the space overheads and $\\mathrm{ED}^{2}$ by 50% and 26.1%, respectively.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586205"}, {"primary_key": "2117647", "vector": [], "sparse_vector": [], "title": "Enabling the Design of Behavioral Systems-on-Chip.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "High-Level Synthesis (HLS) dramatically facilitates the design and verification of individual components. These components are typically the dedicated hardware accelerators used within larger systems, e.g. image processing, DSP or encrypheterogeneoustion cores. Unfortunately, HLS is a single process (component) synthesis method. This implies that the integration of these accelerators are often done at the RT-Level, which implies that the system-level verification and co-design needs to be done at lower levels of abstraction. This work presents an approach that enables a path to generate complete SoCs at the behavioral level. Two main contributions that enable this are: First, an automatic bus generator that generates a synthesizable behavioral description of standard on-chip buses. Second, a library of synthesizable APIs that allow any component in the system to send or receive data through the bus. This approach has significant advantages over traditional approaches. (1) It enables the generation of fast cycle-accurate simulation models of the entire SoC. Experimental results for SoCs of different complexities show that an average speedup of $7.6\\times$ is achieved. (2) It allows to completely separate the bus implementation details from the developers view allowing the change between bus types and configuration with only minor changes in the designers' code. Finally, (3) by creating synthesizable SystemC code any commercial HLS tool can convert the given bus and bus interface code into efficient RTL, thus, making our proposed flow HLS tool agnostic.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586263"}, {"primary_key": "2117648", "vector": [], "sparse_vector": [], "title": "Reversible Gating Architecture for Rare Failure Detection of Analog and Mixed-Signal Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to the growing complexity and numerous manufacturing variation in safety-critical analog and mixed-signal (AMS) circuit design, rare failure detection in the high-dimensional variational space is one of the major challenges in AMS verification. Efficient AMS failure detection is very demanding with limited samples on account of high simulation and manufacturing cost. In this work, we combine a reversible network and a gating architecture to identify essential features from datasets and reduce feature dimension for fast failure detection. While reversible residual networks (RevNets) have been actively studied for its restoration ability from output to input without the loss of information, the gating network facilitates the RevNet to aim at effective dimension reduction. We incorporate the proposed reversible gating architecture into Bayesian optimization (BO) framework to reduce the dimensionality of BO embedding important features clarified by gating fusion weights so that the failure points can be efficiently located. Furthermore, we propose a conditional density estimation of important and non-important features to extract high-dimensional original input features from the low-dimension important features, improving the efficiency of the proposed methods. The improvements of our proposed approach on rare failure detection is demonstrated in AMS data under the high-dimensional process variations.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586153"}, {"primary_key": "2117649", "vector": [], "sparse_vector": [], "title": "Fault-free: A Fault-resilient Deep Neural Network Accelerator based on Realistic ReRAM Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Energy-efficient Resistive RAM (ReRAM) based deep neural network (DNN) accelerator suffers from severe Stuck-At-Fault (SAF) problem that drastically degrades the inference accuracy. The SAF problem gets even worse in realistic ReRAM devices with low cell resolution. To address the issue, we propose a fault-resilient DNN accelerator based on realistic ReRAM devices. We first analyze the SAF problem in a realistic ReRAM device and propose a 3-stage offline fault-resilient compilation and lightweight online compensation. The proposed work enables the reliable execution of DNN with only 5% area and 0.8% energy overhead from the ideal ReRAM-based DNN accelerator.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586286"}, {"primary_key": "2117650", "vector": [], "sparse_vector": [], "title": "Tamper-Resistant Optical Logic Circuits Based on Integrated Nanophotonics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A tamper-resistant logical operation method based on integrated nanophotonics is proposed focusing on electromagnetic side-channel attacks. In the proposed method, only the phase of each optical signal is modulated depending on its logical state, which keeps the power of optical signals in optical logic circuits constant. This provides logic-gate-level tamper resistance which is difficult to achieve with CMOS circuits. An optical implementation method based on electronically-controlled phase shifters is then proposed. The electrical part of proposed circuits achieves 300 times less instantaneous current change, which is proportional to intensity of the leaked electromagnetic wave, than a CMOS logic gate.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586142"}, {"primary_key": "2117651", "vector": [], "sparse_vector": [], "title": "A New, Computationally Efficient &quot;Blech Criterion&quot; for Immortality in General Interconnects.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "Sachin <PERSON>"], "summary": "Traditional methodologies for analyzing electromigration (EM) in VLSI circuits first filter immortal wires using <PERSON><PERSON><PERSON>'s criterion, and then perform detailed EM analysis on the remaining wires. However, <PERSON><PERSON><PERSON>'s criterion was designed for two-terminal wires and does not extend to general structures. This paper demonstrates a first-principles-based solution technique for determining the steady-state stress at all the nodes of a general interconnect structure, and develops an immortality test whose complexity is linear in the number of edges of an interconnect structure. The proposed model is applied to a variety of structures. The method is shown to match well with results from numerical solvers, to be scalable to large structures.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586127"}, {"primary_key": "2117652", "vector": [], "sparse_vector": [], "title": "In-Hardware Learning of Multilayer Spiking Neural Networks on a Neuromorphic Processor.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Although widely used in machine learning, backpropagation cannot directly be applied to SNN training and is not feasible on a neuromorphic processor that emulates biological neuron and synapses. This work presents a spike-based backpropagation algorithm with biological plausible local update rules and adapts it to fit the constraint in a neuromorphic hardware. The algorithm is implemented on Intel's Loihi chip enabling low power in-hardware supervised online learning of multilayered SNNs for mobile applications. We test this implementation on MNIST, Fashion-MNIST, CIFAR-10 and MSTAR datasets with promising performance and energy-efficiency, and demonstrate a possibility of incremental online learning with the implementation.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586323"}, {"primary_key": "2117653", "vector": [], "sparse_vector": [], "title": "On-device Malware Detection using Performance-Aware and Robust Collaborative Learning.", "authors": ["Sanket Shukla", "<PERSON> Man<PERSON>j P. D.", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The proliferation of the Internet-of-Things (IoT) devices has facilitated smart connectivity and enhanced computational capabilities. Lack of proper security protocols in such devices makes them vulnerable to cyber threats, especially malware attacks. Given the diversity and sophistication in malware samples, detecting them using traditional vendor database-based signature matching techniques is inefficient. This paper presents a collaborative machine learning (ML)-based malware detection framework. We introduce a) performance-aware precision-scaled federated learning (FL) to minimize the communication overheads with minimal device-level computations; and (2) a Robust and Active Protection with Intelligent Defense strategy against malicious activity (RAPID) at the device and network-level due to malware and other cyber-attacks. Deploying FL facilitates detecting malware attacks through collaborative learning and prevents data sharing, thus ensuring data security and privacy. RAPID denies the illegitimate user and aids in developing an effective collaborative malware detection model. A comprehensive analysis, results, and performance of the proposed technique are presented along with the communication overheads. An average accuracy of 94% is obtained with the proposed technique with 15% communication overhead, indicating 19% better performance than state-of-the-art techniques. Furthermore, the minimum accuracy drop of a model trained using RAPID is only 3% when 10% of devices are adversarial and 16% even when 40% of devices are adversarial.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586330"}, {"primary_key": "2117654", "vector": [], "sparse_vector": [], "title": "Exact Neural Networks from Inexact Multipliers via Fibonacci Weight Encoding.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Edge devices must support computationally demanding algorithms, such as neural networks, within tight area/energy budgets. While approximate computing may alleviate these constraints, limiting induced errors remains an open challenge. In this paper, we propose a hardware/software co-design solution via an inexact multiplier, reducing area/power-delay-product requirements by 73/43%, respectively, while still computing exact results when one input is a Fibonacci encoded value. We introduce a retraining strategy to quantize neural network weights to Fibonacci encoded values, ensuring exact computation during inference. We benchmark our strategy on Squeezenet 1.0, DenseNet-121, and ResNet-18, measuring accuracy degradations of only 0.4/1.1/1.7%.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586245"}, {"primary_key": "2117655", "vector": [], "sparse_vector": [], "title": "Prioritized Reinforcement Learning for Analog Circuit Optimization With Design Knowledge.", "authors": ["<PERSON><PERSON><PERSON> N. S<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Analog circuit design and optimization manifests as a critical phase in IC design, which still heavily relies on extensive and time-consuming manual designing by experienced experts. In recent years, the development of reinforcement learning (RL) algorithms draws attention with related techniques being introduced into the analog design field for circuit optimization. However, for robust and efficient analog circuit design, a smart and rapid search for high-quality design points is more desired than finding a globally optimal agent as in traditional RL applications, which was a point not fully considered in some previous works. In this work, we propose three techniques within the RL framework aiming at fast high-quality design point search in a data efficient manner. In particular, we (i) incorporate design knowledge from experienced designers into the critic network design to achieve a better reward evaluation with less data; (ii) guide the RL training with non-uniform sampling techniques prioritizing exploitation over high quality designs and exploration for poorly-trained space; (iii) leverage the trained critic network and limited additional circuit simulation for smart and efficient sampling to get high-quality design points. The experimental results demonstrate the effectiveness and efficiency of our proposed techniques.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586189"}, {"primary_key": "2117656", "vector": [], "sparse_vector": [], "title": "BRAHMS: Beyond Conventional RRAM-based Neural Network Accelerators Using Hybrid Analog Memory System.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Accelerating convolutional neural networks (CNNs) with resistive random-access memory (RRAM) based processing-in-memory systems has been recognized as a promising approach. However, conventional accelerators are usually mixed-signal circuits with digital-to-analog converters (DACs) and analog-to-digital converters (ADCs), which cause performance and energy efficiency degradation. In this work, we first analyze the problems in existing RRAM-based CNN accelerators and point out that there are redundant analog-to-digital (AD) conversions. To eliminate redundant AD conversions and also reduce AD conversion overhead, we propose the BRAHMS architecture, which is an RRAM-based CNN accelerator composed of reconfigurable RRAM crossbars and analog resistive content-addressable memory (ARCAM) arrays. We reorder the operations after a convolutional or fully-connected layer and form fused operators (FOPs), which are implemented as a whole by ARCAM arrays so that digital logic and ADCs are eliminated. BRAHMS realizes a mixed-signal pipeline which transmits data signals in the analog domain within an FOP and in the digital domain between FOPs to obtain high performance and energy efficiency. Detailed simulation results show that compared with an ISAAC-like architecture, BRAHMS improves the performance by several times and the energy efficiency by 10 + times on average.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586247"}, {"primary_key": "2117657", "vector": [], "sparse_vector": [], "title": "Dancing along Battery: Enabling Transformer with Run-time Reconfigurability on Mobile Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Bingbing Li", "<PERSON><PERSON><PERSON>", "Qingfeng Zhuge", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A pruning-based AutoML framework for run-time reconfigurability, namely RT 3 , is proposed in this work. This enables Transformer-based large Natural Language Processing (NLP) models to be efficiently executed on resource-constrained mobile devices and reconfigured (i.e., switching models for dynamic hardware conditions) at run-time. Such reconfigurability is the key to save energy for battery-powered mobile devices, which widely use dynamic voltage and frequency scaling (DVFS) technique for hardware reconfiguration to prolong battery life. In this work, we creatively explore a hybrid block-structured pruning (BP) and pattern pruning (PP) for Transformer-based models and first attempt to combine hardware and software reconfiguration to maximally save energy for battery-powered mobile devices. Specifically, RT 3 integrates two-level optimizations: First, it utilizes an efficient BP as the first-step compression for resource-constrained mobile devices; then, RT 3 heuristically generates a shrunken search space based on the first level optimization and searches multiple pattern sets with diverse sparsity for PP via reinforcement learning to support lightweight software reconfiguration, which corresponds to available frequency levels of DVFS (i.e., hardware reconfiguration). At run-time, RT 3 can switch the lightweight pattern sets within 45ms to guarantee the required real-time constraint at different frequency levels. Results further show that RT 3 can prolong battery life over $ 4\\times$ improvement with less than 1% accuracy loss for Transformer and 1.5% score decrease for DistilBERT.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586295"}, {"primary_key": "2117658", "vector": [], "sparse_vector": [], "title": "GNNerator: A Hardware/Software Framework for Accelerating Graph Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Graph Neural Networks (GNNs) apply deep learning to inputs represented as graphs. They use fully-connected layers to extract features from the nodes/edges of a graph and aggregate these features using message passing between nodes, thereby combining two distinct computational patterns: dense, regular computations and sparse, irregular computations. To address the computational challenges posed by GNNs, we propose GNNERATOR, an accelerator with heterogeneous compute engines optimized for these two patterns. Further, we propose feature-blocking, a novel GNN dataflow that beneficially trades off irregular memory accesses during aggregation for regular memory accesses during feature extraction. We show that GNNERATOR achieves speedups of 5.7-37x over an NVIDIA RTX 2080-Ti, and 2.3x-3.8x over HyGCN, a state-of-the-art GNN accelerator.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586122"}, {"primary_key": "2117659", "vector": [], "sparse_vector": [], "title": "Softermax: Hardware/Software Co-Design of an Efficient Softmax for Transformers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Transformers have transformed the field of natural language processing. Their superior performance is largely attributed to the use of stacked \"self-attention\" layers, each of which consists of matrix multiplies as well as softmax operations. As a result, unlike other neural networks, the softmax operation accounts for a significant fraction of the total run-time of Transformers. To address this, we propose Softermax, a hardware-friendly softmax design. Softermax consists of base replacement, low-precision softmax computations, and an online normalization calculation. We show Softermax results in 2.35x the energy efficiency at 0.90x the size of a comparable baseline, with negligible impact on network accuracy.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586134"}, {"primary_key": "2117660", "vector": [], "sparse_vector": [], "title": "CrossLight: A Cross-Layer Optimized Silicon Photonic Neural Network Accelerator.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Domain-specific neural network accelerators have seen growing interest in recent years due to their improved energy efficiency and performance compared to CPUs and GPUs. In this paper, we propose a novel cross-layer optimized neural network accelerator called CrossLight that leverages silicon photonics. CrossLight includes device-level engineering for resilience to process variations and thermal crosstalk, circuit-level tuning enhancements for inference latency reduction, and architecture-level optimizations to enable better resolution, energy-efficiency, and throughput. On average, CrossLight offers 9.5x lower energy-per-bit and 15.9x higher performance-per-watt than state-of-the-art photonic deep learning accelerators.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586161"}, {"primary_key": "2117661", "vector": [], "sparse_vector": [], "title": "AdEle: An Adaptive Congestion-and-Energy-Aware Elevator Selection for Partially Connected 3D NoCs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "By lowering the number of vertical connections in fully connected 3D networks-on-chip (NoCs), partially connected 3D NoCs (PC-3DNoCs) help alleviate reliability and fabrication issues. This paper proposes a novel, adaptive congestion- and energy-aware elevator-selection scheme called AdEle to improve the traffic distribution in PC-3DNoCs. AdEle employs an offline multi-objective simulated-annealing-based algorithm to find good elevator subsets and an online elevator selection policy to enhance elevator selection during routing. Compared to the state-of-the-art techniques under different real-application traffics and configuration scenarios, AdEle improves the network latency by 10.9% on average (up to 14.6%) with less than 6.9% energy consumption overhead.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586174"}, {"primary_key": "2117662", "vector": [], "sparse_vector": [], "title": "Invited: Independent Verification and Validation of Security-Aware EDA Tools and IP.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Secure silicon requires a seamless integration of new tools, new IP, and design flows to help designers protect integrated circuits from increasingly sophisticated attacks. Independent Validation and Verification (IV&V) of this integrated technology is important to ensure that the tools actually deliver on their security claims when used by independent parties (i.e., people who were not involved in designing the tools). This work discusses the principles and approaches for IV&V of such a complex design environment, including validation of the security strength of the various hardware security techniques, such as combinational and sequential logic locking, Trojan Detection, side-channel mitigation, and blockchain-based asset management. The main challenge in running an IV&V effort is to ensure that the process provides rigorous, methodical and provable evaluation of the claims of not only the component tools and IP, but whether such an integrated environment can produce security-hardened designs by a non-security expert. CCS Concepts • Hardware $\\rightarrow$ Very large scale integration design; Methodologies for EDA; • Security and privacy $\\rightarrow$ Security in hardware.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586302"}, {"primary_key": "2117663", "vector": [], "sparse_vector": [], "title": "ROLoad: Securing Sensitive Operations with Pointee Integrity.", "authors": ["<PERSON><PERSON>", "Yuan Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sensitive operations (e.g. control-flow transfers) are attractive targets for attackers. To protect them from being hijacked, we propose a new solution ROLoad to guarantee the integrity of their operands, which are loaded from (potentially corrupted) memory. We extend the RISC-V instruction set, implement an FPGA-based prototype of ROLoad, and then demonstrate two specific defense applications. Results show that this solution only costs few extra hardware resources (< 3.32%). However, it could enable many lightweight (e.g. with overheads less than 0.31%) defenses, and provide broader and stronger security guarantees than existing hardware solutions, e.g. ARM BTI and Intel CET.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586274"}, {"primary_key": "2117664", "vector": [], "sparse_vector": [], "title": "Towards Reliable Spatial Memory Safety for Embedded Software by Combining Checked C with Concolic Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we propose to combine the safe C dialect Checked C with concolic testing to obtain an effective methodology for attaining safer C code. Checked C is a modern and backward compatible extension to the C programming language which provides facilities for writing memory-safe C code. We utilize incremental conversions of unsafe C software to Checked C. After each increment, we leverage concolic testing, an effective test generation technique, to support the conversion process by searching for newly introduced and existing bugs.Our RISC-V experiments using the RIOT Operating System (OS) demonstrate the effectiveness of our approach. We uncovered 4 previously unknown bugs and 3 bugs accidentally introduced through our conversion process.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586170"}, {"primary_key": "2117665", "vector": [], "sparse_vector": [], "title": "Invited: Getting the Most out of your Circuits with Heterogeneous Logic Synthesis.", "authors": ["<PERSON>", "<PERSON>", "Ashton Snelgrove", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "High Level Synthesis (HLS) speeds hardware development and opens the door to non-expert designers, focusing on functionality rather than implementation. The expense and rigidity of commercial electronic design automation (EDA) tool-chains can be an obstacle for these users. LSOracle is an opensource logic synthesis tool which leverages multiple underlying data structures, including and-inverter graphs (AIGs), majority-inverter graphs (MIGs), and xor-and graphs (XAGs) to automatically optimize circuits using the best representation for each region of a design, without manual intervention. The use of MIGs and XAGs gives particularly strong performance in arithmetic logic, cryptography cores, and machine-learning accelerators; applications which may be of particular interest for HLS users. Here we present an overview of the approach and demonstrate an open-source HLS-to-GDS II workflow using LSOracle, Bambu, and OpenROAD. We test the integration on a small benchmark suite and show a reduction in delay of up to 31%.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586215"}, {"primary_key": "2117666", "vector": [], "sparse_vector": [], "title": "Invited: Trainable Discrete Feature Embeddings for Quantum Machine Learning.", "authors": ["<PERSON><PERSON><PERSON>", "Chayaphol Lortaraprasert", "<PERSON>"], "summary": "Quantum classifiers provide sophisticated embeddings of input data in Hilbert space promising quantum advantage. The advantage stems from quantum feature maps encoding the inputs into quantum states with variational quantum circuits. A recent work shows how to map discrete features with fewer quantum bits using Quantum Random Access Coding (QRAC) to encode binary strings into quantum states. We propose a new method to embed discrete features with trainable quantum circuits by combining QRAC and a recently proposed strategy for training quantum feature map called quantum metric learning. The proposed trainable embedding requires not only as few qubits as QRAC but also overcomes the limitations of QRAC to classify inputs whose classes are based on hard Boolean functions. We numerically demonstrate its use in variational quantum classifiers to achieve better performances to classify real-world datasets, and thus its possibility to use near-term quantum computers for machine learning.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586190"}, {"primary_key": "2117667", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Reinforcement Learning for Scalable Logic Optimization with Graph Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Logic optimization is an NP-hard problem commonly approached through hand-engineered heuristics. We propose to combine graph convolutional networks with reinforcement learning and a novel, scalable node embedding method to learn which local transforms should be applied to the logic graph. We show that this method achieves a similar size reduction as ABC on smaller circuits and outperforms it by $1. 5 - 1. 75 \\times $ on larger random graphs.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586206"}, {"primary_key": "2117668", "vector": [], "sparse_vector": [], "title": "Local Bayesian Optimization For Analog Circuit Sizing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposes a Bayesian Optimization (BO) algorithm to handle large-scale analog circuit sizing. The proposed approach uses a number of separate Gaussian Process (GP) models approximating the objective and constraint functions locally in the search space. Unlike mainstream BO approaches, it is able to traverse high dimensional problems with ease and provide multiple query points for parallel evaluation. To extend the method to large sample budgets, GP regression and sampling are enhanced by using kernel approximations and GPU acceleration. Experimental results demonstrate that the proposed method finds better solutions within given budgets of total evaluations compared to state-of-the-art approaches.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586172"}, {"primary_key": "2117669", "vector": [], "sparse_vector": [], "title": "Bit-Slicing the Hilbert Space: Scaling Up Accurate Quantum Circuit Simulation.", "authors": ["Yuan<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent advancements in quantum technologies shed light on viable quantum computation in near future. Quantum circuit simulation plays a key role in the toolchain of quantum hardware and software development. Due to the enormous Hilbert space of quantum states, simulating quantum circuits with classical computers is notoriously challenging. This work enhances quantum circuit simulation in two dimensions: accuracy (by representing complex numbers algebraically) and scalability (by bit-slicing number representation and achieving matrix-vector multiplication with symbolic Boolean manipulation). Experiments demonstrate the superiority of our method to the state-of-the-art tools over various quantum circuits with up to tens of thousands of qubits.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586191"}, {"primary_key": "2117670", "vector": [], "sparse_vector": [], "title": "RePIM: Joint Exploitation of Activation and Weight Repetitions for In-ReRAM DNN Acceleration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>sian<PERSON><PERSON><PERSON>"], "summary": "Eliminating redundant computations is a common approach to improve the performance of ReRAM-based DNN accelerators. While existing practical ReRAM-based accelerators eliminate part of the redundant computations by exploiting sparsity in inputs and weights or utilizing weight patterns of DNN models, they fail to identify all the redundancy, resulting in many unnecessary computations. Thus, we propose a practical design, RePIM, that is the first to jointly exploit the repetition of both inputs and weights. Our evaluation shows that RePIM is effective in eliminating unnecessary computations, achieving an average of $ 15.24\\times$ speedup and 96.07% energy savings over the state-of-the-art practical ReRAM-based accelerator.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586315"}, {"primary_key": "2117671", "vector": [], "sparse_vector": [], "title": "Subresolution Assist Feature Insertion by Variational Adversarial Active Learning and Clustering with Data Point Retrieval.", "authors": ["<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As the feature size keeps shrinking in the modern semiconductor manufacturing process, subresolution assist feature (SRAF) insertion is one promising resolution enhancement technique that can improve the printability and lithographic process window of target patterns. Model-based SRAF generation achieves a high accuracy but with a high computational cost, while rule-based SRAF insertion may require a huge rule table to handle complex patterns. Thus, state-of-the-art works resort to machine learning to reduce runtime but require abundant training samples to generalize the trained models and achieve high performance. Nevertheless, in advanced lithography, we may have a huge solution space of SRAF insertion but few labeled training samples. Therefore, in this work, we address SRAF insertion from a data efficiency perspective. We separate sample selection from SRAF probability learning and train a variational autoencoder and an adversarial network to discriminate between unlabeled and labeled data effectively. Second, we devise a region-based concentric circle area sampling representation to avoid information loss during feature extraction. Third, we determine the final placement of SRAFs by a novel clustering method based on retrieved data points. Experimental results show that, compared with state-of-the-art works, by using 40% training samples, our framework can achieve comparable or even better process variation bands and edge placement errors.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586238"}, {"primary_key": "2117672", "vector": [], "sparse_vector": [], "title": "QECOOL: On-Line Quantum Error Correction with a Superconducting Decoder for Surface Code.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the low error tolerance of a qubit, detecting and correcting errors on it is essential for fault-tolerant quantum computing. Surface code (SC) associated with its decoding algorithm is one of the most promising quantum error correction (QEC) methods. QEC needs to be very power-efficient since the power budget is limited inside of a dilution refrigerator for superconducting qubits by which one of the most successful quantum computers (QCs) is built. In this paper, we propose an online-QEC algorithm and its hardware implementation with SFQ based superconducting digital circuits. We design a key building block of the proposed hardware with an SFQ cell library and evaluate it by the SPICE-level simulation. Each logic element is composed of about 3000 Josephson junctions and power consumption is about $2.78 \\mu \\mathrm{W}$ when operating with 2 GHz clock frequency which meets the required decoding speed. Our decoder is simulated on a quantum error simulator for code distances 5 to 13 and achieves a 1.0% accuracy threshold.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586326"}, {"primary_key": "2117673", "vector": [], "sparse_vector": [], "title": "CLAppED: A Design Framework for Implementing Cross-Layer Approximation in FPGA-based Embedded Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rising variation and complexity of embedded work-loads, FPGA-based systems are being increasingly used for many applications. The reconfigurability and high parallelism offered by FPGAs are used to enhance the overall performance of these applications. However, the resource constraints of embedded platforms can limit the performance in multiple ways. In recent years, Approximate Computing has emerged as a viable tool for improving the performance by utilizing reduced precision data structures and resource-optimized high-performance arithmetic operators. However, most of the related state-of-the-art research has mainly focused on utilizing approximate computing principles individually on different layers of the computing stack. Nonetheless, approximations across different layers of computing stack can substantially enhance the system's performance. To this end, we present a framework to enable the intelligent exploration and highly accurate identification of the feasible design points in the large design space enabled by cross-layer approximations. Our framework proposes a novel polynomial regression-based method to model approximate arithmetic operators. The proposed method enables machine learning models to better correlate approximate operators with their impact on an application's output quality. We use a 2D convolution operator as a test case and present the results for FPGA- based approximate hardware accelerators.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586260"}, {"primary_key": "2117674", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Parallelizing Net Routing with cGANs.", "authors": ["<PERSON>", "<PERSON><PERSON>-Vaisband"], "summary": "Obstacle-avoiding multiterminal net routing approach is proposed. The approach is inspired by deep learning image processing. The key idea is based on training a conditional generative adversarial network (cGAN) to interpret a routing task as a graphical bitmap and consequently map it to an optimal routing solution represented by another bitmap. The system is implemented in Python/Keras, trained on synthetically generated data, evaluated on typical high-resolution benchmarks, and compared with state-of-the-art traditional deterministic and deep learning solutions. The proposed system yields between 10.75x and 83.33x speedup over the traditional router without wirelength overhead due to effective parallelization on GPU hardware.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586319"}, {"primary_key": "2117675", "vector": [], "sparse_vector": [], "title": "EImprove - Optimizing Energy and Comfort in Buildings based on Formal Semantics and Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Heating, ventilation, and air-conditioning (HVAC) system's supervisory control is crucial for energy-efficient thermal comfort in buildings. The control logic is usually specified as 'if-then-that-else' rules that capture the domain expertise of HVAC operators, but they often have conflicts that may lead to sub-optimal HVAC performance. We propose EImprove, a reinforcement-learning (RL) based framework that exploits these conflicts to learn a resolution policy. We evaluate EImprove through a co-simulation strategy involving EnergyPlus simulations of a real-world office setting and a formal requirement specifier. Our experiments show that EImprove learns 75% faster than a pure RL framework.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586313"}, {"primary_key": "2117676", "vector": [], "sparse_vector": [], "title": "Quantum Spectral Clustering of Mixed Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Spectral graph partitioning is a well known technique to estimate clusters in undirected graphs. Recent approaches explored efficient spectral algorithms for directed and mixed graphs utilizing various matrix representations. Despite its success in clustering tasks, classical spectral algorithms suffer from a cubic growth in runtime. In this paper, we propose a quantum spectral clustering algorithm for discovering clusters and properties of mixed graphs. Our experimental results based on numerical simulations demonstrate that our quantum spectral clustering outperforms classical spectral clustering techniques. Specifically, our approach leads to a linear growth in complexity, while state-of-the-art classical counterpart leads to cubic growth. In a case study, we apply our proposed algorithm to preform unsupervised machine learning using both real and simulated quantum computers. This work opens an avenue for efficient implementation of machine learning algorithms on directed as well as mixed graphs by making use of the inherent potential quantum speedup.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586308"}, {"primary_key": "2117677", "vector": [], "sparse_vector": [], "title": "Analyzing and Improving Fault Tolerance of Learning-Based Navigation Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Learning-based navigation systems are widely used in autonomous applications, such as robotics, unmanned vehicles and drones. Specialized hardware accelerators have been proposed for high-performance and energy-efficiency for such navigational tasks. However, transient and permanent faults are increasing in hardware systems and can catastrophically violate tasks safety. Meanwhile, traditional redundancy-based protection methods are challenging to deploy on resource-constrained edge applications. In this paper, we experimentally evaluate the resilience of navigation systems with respect to algorithms, fault models and data types from both RL training and inference. We further propose two efficient fault mitigation techniques that achieve $2 \\times$ success rate and 39% quality-of-flight improvement in learning-based navigation systems.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586116"}, {"primary_key": "2117678", "vector": [], "sparse_vector": [], "title": "An Automated and Process-Portable Generator for Phase-Locked Loop.", "authors": ["<PERSON><PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a bang-bang phase-locked loop (PLL) generator that encapsulates design methodologies for its circuit blocks and the complete PLL system. The generator is fully automated and parameterized, producing the layout and schematic based on process characterization and top-level specifications. Three 14GHz PLLs are instantiated in TSMC 16nm, GF 14nm and Intel 22nm technologies, demonstrating the process portability. The rapid generation time of less than four days enables fast PLL design and technology porting. The PLL design fabricated in TSMC 16nm shows RMS jitter of 565.4fs and power of 6.64mW from a 0.9V supply.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586318"}, {"primary_key": "2117679", "vector": [], "sparse_vector": [], "title": "Cocktail: Learn a Better Neural Network Controller from Multiple Experts via Adaptive Mixing and Robust Distillation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Neural networks are being increasingly applied to control and decision making for learning-enabled cyber-physical systems (LE-CPSs). They have shown promising performance without requiring the development of complex physical models; however, their adoption is significantly hindered by the concerns on their safety, robustness, and efficiency. In this work, we propose COCKTAIL, a novel design framework that automatically learns a neural network based controller from multiple existing control methods (experts) that could be either model-based or neural network based. In particular, COCKTAIL first performs reinforcement learning to learn an optimal system-level adaptive mixing strategy that incorporates the underlying experts with dynamically-assigned weights, and then conducts a teacher-student distillation with probabilistic adversarial training and regularization to synthesize a student neural network controller with improved control robustness (measured by a safe control rate metric with respect to adversarial attacks or measurement noises), control energy efficiency, and verifiability (measured by the computation time for verification). Experiments on three non-linear systems demonstrate significant advantages of our approach on these properties over various baseline methods.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586148"}, {"primary_key": "2117680", "vector": [], "sparse_vector": [], "title": "Two-Stage Neural Network Classifier for the Data Imbalance Problem with Application to Hotspot Detection.", "authors": ["<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The data imbalance problem often occurs in nanometer VLSI applications, where normal cases far outnumber error ones. Many imbalanced data handling methods have been proposed, such as oversampling minority class samples and downsampling majority class samples. However, existing methods focus on improving the quality of minority classes while causing quality deterioration of majority ones. In this paper, we propose a two-stage classifier to handle the data imbalance problem. We first develop an iterative neural network framework to reduce false alarms. Then the oversampling method on a final classification network is applied to predict the two classes better. As a result, the data imbalance problem is well handled, and the quality deterioration of majority classes is also reduced. Since the iterative stage does not change any existing network structure, any convolutional neural network can be used in the framework. Compared with the state-of-the-art imbalanced data handling methods, experimental results on the hotspot detection problem show that our two-stage classification method achieves the best prediction accuracy and reduces false alarms significantly.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586237"}, {"primary_key": "2117681", "vector": [], "sparse_vector": [], "title": "A Novel Machine-Learning based SoC Performance Monitoring Methodology under Wide-Range PVT Variations with Unknown Critical Paths.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Monitoring system-on-chip performance under process, voltage, and temperature (PVT) variations is very challenging, especially when the parasitic effects dominate the whole chip performance in advanced process nodes. Most of the previous works presented the performance monitoring methodologies based on known/predicted candidates of critical paths under different operating conditions. However, those methodologies may fail when the critical path is misrecognized or mispredicted. This paper proposes a novel machine-learning based chip performance monitoring methodology to accurately match the chip performance without requiring the information of critical paths under various PVT conditions. The experimental results based on measured chip performance show that the proposed methodology can achieve 98.5% accuracy in the worst case under wide-range PVT variations.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586155"}, {"primary_key": "2117682", "vector": [], "sparse_vector": [], "title": "PixelSieve: Towards Efficient Activity Analysis From Compressed Video Streams.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pixel-level data redundancy in video induces additional memory and computing overhead when neural networks are employed to mine spatiotemporal patterns, e.g. activity and event labels from video streams. This work proposes PixelSieve, to enable highly efficient CNN-based activity analysis directly from video data in compressed formats. Instead of recovering original RGB frames from compressed video, PixelSieve utilizes the built-in metadata in compressed video streams to distill only the critical pixels that render relevant spatiotemporal features, and then conducts efficient CNN inference with the condensed inputs. PixelSieve removes the overhead of video decoding and significantly improves the performance of CNN-based video analysis by 4.5x on average.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586310"}, {"primary_key": "2117683", "vector": [], "sparse_vector": [], "title": "Network-on-Interposer Design for Agile Neural-Network Processor Chip Customization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Chiplet based multi-die integration has been thought as a key enabler of the agile chip development flow. For 2.5D based multi-die system, Network on Interposer plays an essential role in the performance and the development cost of the chips. This work proposed a reusable NoI design for agile AI chip customization. The proposed NoI design can self-adapt to the inter-die communication patterns of various neural network applications, so the produced interposers can be reused across different AI chip specifications. Experimental results show the proposed NoI design brings 42.7%$\\sim$79.5% of total data communication latency reduction in different scenarios, and it also decreased the area overhead by 26.4%.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586261"}, {"primary_key": "2117684", "vector": [], "sparse_vector": [], "title": "OpenMem: Hardware/Software Cooperative Management for Mobile Memory System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Hybrid memory systems, comprised of emerging non-volatile memory (NVM) and DRAM, have been proposed to address the growing memory demand of current mobile applications. NVM technologies have higher capacity density, minimal static power consumption, but longer access latency and limited write endurance compared to DRAM. The different characteristics of these two memory classes, however, pose new challenges for memory system design. Ideally, pages shall be placed or migrated between the two types of memories according to the data objects' access properties. Prior works use the OS for placement and migration in these systems, but at the cost of high software latency incurred by related kernel processes. Hardware approaches can avoid these latencies, however, hardware's vision is constrained to a short time window of recently memory request, due to the limited on-chip resources.In this work, we propose OpenMem: a hardware-software cooperative approach to address placement and migration within hybrid memory systems, that combines the execution time advantages of pure hardware approaches with the data object properties in a global scope. We emulate OpenMem on an FPGA board with embedded ARM CPU, and run a set of benchmark applications from SPEC 2017 and PARSEC. Experimental results show that OpenMem reduces energy consumption by 44.6% with only a 16% performance degradation compared to an all-DRAM memory system. Further, writes to the NVM are reduced by 14% versus a hardware-only approach, extending the NVM device lifetime.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586186"}, {"primary_key": "2117685", "vector": [], "sparse_vector": [], "title": "New Regular Expressions on Old Accelerators.", "authors": ["<PERSON>", "Michael F. P. O&apos;<PERSON>"], "summary": "Regular expressions (regexes) play a key role in a wide range of systems including network intrusion detection. FPGA accelerators can provide power savings over CPUs by exploiting MISD parallelism inherent in regex processing. However, FPGA solutions are brittle, requiring hours to reprogram when rulesets change, while real-world security threats evolve rapidly.We present RXPSC (Regular eXPression Structural Compiler), a compiler designed to compile new regexes to existing regex accelerators. We use input-stream translation to enable fixed FPGA accelerators to accelerate new patterns with minimal overhead and little update delay. Compared to a solution where new regexes run on a CPU, RXPSC reduces CPU load by more than a factor of ten for 84% of unseen regexes in ANMLZoo benchmarks.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586095"}, {"primary_key": "2117686", "vector": [], "sparse_vector": [], "title": "Enabling On-Device Self-Supervised Contrastive Learning with Selective Data Contrast.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jingtong Hu"], "summary": "After a model is deployed on edge devices, it is desirable for these devices to learn from unlabeled data to continuously improve accuracy. Contrastive learning has demonstrated its great potential in learning from unlabeled data. However, the online input data are usually none independent and identically distributed (non-iid) and edge devices' storages are usually too limited to store enough representative data from different data classes. We propose a framework to automatically select the most representative data from the unlabeled input stream, which only requires a small data buffer for dynamic learning. Experiments show that accuracy and learning speed are greatly improved.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586228"}, {"primary_key": "2117687", "vector": [], "sparse_vector": [], "title": "SGX-FPGA: Trusted Execution Environment for CPU-FPGA Heterogeneous Architecture.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Trusted execution environments (TEEs), such as Intel SGX, have become a popular security primitive with minimum trusted computing base (TCB) and attack surface. However, the existing CPU-based TEEs do not support FPGAs, even though FPGA-based cloud computing services have been rapidly deployed with security vulnerabilities that are expected to be eliminated by TEEs. To fill the gap, we present SGX-FPGA, a trusted hardware isolation path enabling the first FPGA TEE by bridging SGX enclaves and FPGAs in the heterogeneous CPU-FPGA architecture. Our experiments on real CPU-FPGA hardware justify the high security and low performance overhead achieved by SGX-FPGA.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586207"}, {"primary_key": "2117688", "vector": [], "sparse_vector": [], "title": "Low-Cost Lithography Hotspot Detection with Active Entropy Sampling and Model Calibration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With feature size scaling and complexity increase of circuit designs, hotspot detection has become a significant challenge in the very-large-scale-integration (VLSI) industry. Traditional detection methods, such as pattern matching and machine learning, have been made a remarkable progress. However, the performance of classifiers relies heavily on reference layout libraries, leading to the high cost of lithography simulation. Querying and sampling qualified candidates from raw datasets make active learning-based strategies serve as an effective solution in this field, but existing relevant studies fail to take sufficient sampling criteria into account. In this paper, embedded in pattern sampling and hotspot detection framework, an entropy-based batch mode sampling strategy is proposed in terms of calibrated model uncertainty and data diversity to handle the hotspot detection problem. Redundant patterns can be effectively avoided, and the classifier can converge with high celerity. Experiment results show that our method outperforms previous works in both ICCAD2012 and ICCAD2016 Contest benchmarks, achieving satisfactory detection accuracy and significantly reduced lithography simulation overhead.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586273"}, {"primary_key": "2117689", "vector": [], "sparse_vector": [], "title": "Efficient Implementation of Finite Field Arithmetic for Binary Ring-LWE Post-Quantum Cryptography Through a Novel Lookup-Table-Like Method.", "authors": ["<PERSON><PERSON><PERSON>", "Pengzhou He", "<PERSON><PERSON><PERSON>"], "summary": "The recent advance in the post-quantum cryptography (PQC) field has gradually shifted from the theory to the implementation of the cryptosystem, especially on the hardware platforms. Following this trend, in this paper, we aim to present efficient implementations of the finite field arithmetic (key component) for the binary Ring-Learning-with-Errors (Ring-LWE) PQC through a novel lookup-table (LUT)-like method. In total, we have carried out four stages of interdependent efforts: (i) an algorithm-hardware co-design driven derivation of the proposed LUT-like method is provided detailedly for the key arithmetic of the BRLWE scheme; (ii) the proposed hardware architecture is then presented along with the internal structural description; (iii) we have also presented a novel hybrid size structure suitable for flexible operation, which is the first report in the literature; (iv) the final implementation and comparison processes have also been given, demonstrating that our proposed structures deliver significant improved performance over the state-of-the-art solutions. The proposed designs are highly efficient and are expected to be employed in many emerging applications.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586151"}, {"primary_key": "2117690", "vector": [], "sparse_vector": [], "title": "Obfuscated Priority Assignment to CAN-FD Messages with Dependencies: A Swapping-based and Affix-Matching Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Renfa Li", "<PERSON><PERSON>"], "summary": "CAN-FD (CAN with flexible data rate) has been developed to support automated driving as a high-bandwidth version of the conventional CAN (controller area network) bus protocol. Due to the complexity of the emerging automotive functionalities, there exist dependencies between the tasks and thus also between the CAN-FD messages. The current industrial practice is that the same application has exactly the same message transmission flow (i.e., the same ordered sequence of messages to be transmitted) across all vehicles. This renders large-scale attacks possible and potentially leads to millions of vehicles to be recalled, as one vehicle being compromised exposes all the others. To address this issue, an application could have different (obfuscated) message flows on individual vehicles. The challenge is to find a large number of available flows (i.e., flows that respect dependencies and meet application deadlines) within short time. For this purpose, we propose a novel priority assignment approach, which assigns the ordered positions in a flow (named priorities) to the messages. It dynamically generates new valid flows (i.e., flows with only dependencies respected and deadlines not considered) by message swapping, instead of exploring all valid flows as in the existing approaches. We apply pruning through affix-matching to further enhance the efficiency. That is, the prefix, infix, and suffix are all matched when determining whether a certain flow should be discarded without evaluating its availability, aiming for lower false positive rate (FSR) and false negative rate (FNR) than adfix-matching (only prefix and suffix are matched) in the state-of-the-art approach. Experimental results show that the proposed approach dominates the state-of-the-art approach, in the number of available flows found (up to 79x) and time consumption (up to 200x), most notably when the proportion of available flows is small. This work is an important step for obfuscated priority assignment to be deployed on practical CAN-FD messages.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586125"}, {"primary_key": "2117691", "vector": [], "sparse_vector": [], "title": "Mitigating Crosstalk in Quantum Computers through Commutativity-Based Instruction Reordering.", "authors": ["<PERSON><PERSON>", "Jidong Zhai", "<PERSON><PERSON>"], "summary": "Crosstalk is one of the major types of noise in quantum computers. To design high-fidelity quantum gates and large-scale quantum computers, effectively suppressing crosstalk is becoming increasingly important. Previous approaches to mitigate crosstalk rely on either hardware strategies, which are only applicable on limited platforms, or software techniques, which, however, cannot fully explore instruction parallelism. To address the above challenges, we propose a commutativity based compile-time instruction reordering approach. We first propose a complete set of generalized commutativity rules for main types of quantum gates, and then we design a two-level bidirectional reordering algorithm to reorder gates according to these rules for effective crosstalk mitigation. Our approach can not only capture both forward and backward instruction correlations but also reduce the effect of decoherence. We evaluate our approach with 117 quantum programs. Compared with the state-of-the-art, our approach can improve program fidelity by up to 120% (18% on average).", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586145"}, {"primary_key": "2117692", "vector": [], "sparse_vector": [], "title": "Helios: Heterogeneity-Aware Federated Learning with Dynamically Balanced Collaboration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As Federated Learning (FL) has been widely used for collaborative training, a considerable computational straggler issue emerged: when FL deploys identical neural network models to heterogeneous devices, the ones with weak computational capacities, referred to as stragglers, may significantly delay the synchronous parameter aggregation. Although discarding stragglers from the collaboration can relieve this issue to a certain extent, stragglers may keep unique and critical information learned from the non-identical dataset, and directly discarding will harm the overall collaboration performance. Therefore, in this paper, we propose <PERSON>lios – a heterogeneity-aware FL framework to tackle the straggler issue. <PERSON><PERSON><PERSON> identifies individual devices' heterogeneous training capability, and therefore the expected neural network model training volumes regarding the collaborative training pace. For straggling devices, a \"softtraining\" method is proposed to dynamically compress the original identical training model into the expected volume through a rotated neuron training approach. With extensive algorithm analysis and optimization schemes, stragglers can be accelerated while retaining the convergence for local training as well as federated collaboration. Experiments show that <PERSON><PERSON><PERSON> can provide up to $2.5\\times$ training acceleration and maximum 4.64% convergence accuracy improvement in various collaboration settings.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586241"}, {"primary_key": "2117693", "vector": [], "sparse_vector": [], "title": "gGuard: Enabling Leakage-Resilient Memory Isolation in GPU-accelerated Autonomous Embedded Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graphics processing units (GPUs) are being widely used as co-processors for performance acceleration in many autonomous embedded systems such as robotics and autonomous vehicles. However, current GPU hardware and systems software, including GPU device drivers, compilers, and operating systems, do not implement proper memory protection mechanisms due to performance and proprietary reasons, causing severe vulnerabilities such as information leakage. In this paper, we present gGuard, a leakage-resilient GPU memory management system with strong isolation. Based on the intrinsic characteristics of information leakage vulnerabilities on GPUs, gGuard develops a set of efficient and accurate data shredding techniques implemented at the compiler, library, and operating system levels, with the core idea of exploring the data access patterns and dependencies for efficient application-aware data shredding. Our implementation and evaluation show that gGuard can provide effective mitigation on GPU data leakage issues through efficient GPU data shredding while introducing less than 6% overhead in all tested scenarios.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586244"}, {"primary_key": "2117694", "vector": [], "sparse_vector": [], "title": "FIXAR: A Fixed-Point Deep Reinforcement Learning Platform with Quantization-Aware Training and Adaptive Parallelism.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Deep reinforcement learning (DRL) is a powerful technology to deal with decision-making problem in various application domains such as robotics and gaming, by allowing an agent to learn its action policy in an environment to maximize a cumulative reward. Unlike supervised models which actively use data quantization, DRL still uses the single-precision floating-point for training accuracy while it suffers from computationally intensive deep neural network (DNN) computations. In this paper, we present a deep reinforcement learning acceleration platform named FIXAR, which employs fixed-point data types and arithmetic units for the first time using a SW/HW co-design approach. We propose a quantization-aware training algorithm in fixed-point, which enables to reduce the data precision by half after a certain amount of training time without losing accuracy. We also design a FPGA accelerator that employs adaptive dataflow and parallelism to handle both inference and training operations. Its processing element has configurable datapath to efficiently support the proposed quantized-aware training. We validate our FIXAR platform, where the host CPU emulates the DRL environment and the FPGA accelerates the agent's DNN operations, by running multiple benchmarks in continuous action spaces based on a latest DRL algorithm called DDPG. Finally, the FIXAR platform achieves 25293.3 inferences per second (IPS) training throughput, which is 2.7 times higher than the CPU-GPU platform. In addition, its FPGA accelerator shows 53826.8 IPS and 2638.0 IPS/W energy efficiency, which are 5.5 times higher and 15.4 times more energy efficient than those of GPU, respectively. FIXAR also shows the best IPS throughput and energy efficiency among other state-of-the-art acceleration platforms using FPGA, even it targets one of the most complex DNN models.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586213"}, {"primary_key": "2117695", "vector": [], "sparse_vector": [], "title": "PIMGCN: A ReRAM-Based PIM Design for Graph Convolutional Network Acceleration.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> He", "Li <PERSON>"], "summary": "Graph Convolutional Network (GCN) is a promising but computing- and memory-intensive learning model. Processing-in-memory (PIM) architecture based on the ReRAM crossbar is a natural fit for GCN inference. It can reduce the data movements and compute the vector-matrix multiplication (VMM) in analog. However, it requires an unbearable crossbar cost to leverage the massive parallelism exhibited in GCNs. This paper explores the design space for GCN acceleration on ReRAM crossbars and presents the first PIM-based GCN accelerator named PIMGCN. PIMGCN employs dense data mapping and a search-execute architecture to take full advantage of the intra-vertex parallelisms with acceptable crossbars cost. We further propose two scheduling strategies for PIMGCN to maximize the inter-vertex parallelisms and optimize the pipeline. The optimal scheduling is reduced to a maximum independent set problem, which is solved by a novel node-grouping algorithm. Compared to the state-of-the-art software framework running on Intel Xeon CPU and NVIDIA RTX8000 GPU, PIMGCN achieves on average 11044× and 74.3× speedup, 6.13E+06× and 5.09E+03× energy reduction, respectively. Compared with ASIC accelerator HyGCN [1], PIMGCN achieves 219× speedup and 95.3× energy reduction.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586231"}, {"primary_key": "2117696", "vector": [], "sparse_vector": [], "title": "3D-Adv: Black-Box Adversarial Attacks against Deep Learning Models through 3D Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "The combination of deep learning techniques and commercial 3D sensors reveal a bright future as they provide a low cost and convenient method to collect and analyze depth information from the environment for various applications ranging from industrial modeling to mobile face recognition. Despite the abundant research devoted to the development of more accurate, flexible and efficient machine learning schemes as well as 3D sensors, security concerns related to these techniques remain largely untouched. In this paper, we propose a novel adversarial attack against this combination by showing that deep learning models with popular 3D sensors may misclassify real objects in the physical environment. Comparing to the existing attack algorithms against deep learning models developed for 3D data analysis that only consider digital point cloud data and single deep learning model, our attacks target popular commercial 3D sensors combined with various deep learning schemes in the black-box setting. Experimental results demonstrate that our 3D printed adversarial objects stay effective after scanned by a 3D sensor.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586275"}, {"primary_key": "2117697", "vector": [], "sparse_vector": [], "title": "Trust-Region Method with Deep Reinforcement Learning in Analog Design Space Exploration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces new perspectives on analog design space search. To minimize the time-to-market, this endeavor better cast as constraint satisfaction problem than global optimization defined in prior arts. We incorporate model-based agents, contrasted with model-free learning, to implement a trust-region strategy. As such, simple feed-forward networks can be trained with supervised learning, where the convergence is relatively trivial. Experiment results demonstrate orders of magnitude improvement on search iterations. Additionally, the unprecedented consideration of PVT conditions are accommodated. On circuits with TSMC 5/6nm process, our method achieve performance surpassing human designers. Furthermore, this framework is in production in industrial settings.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586087"}, {"primary_key": "2117698", "vector": [], "sparse_vector": [], "title": "GNN4IP: Graph Neural Network for Hardware Intellectual Property Piracy Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Aggressive time-to-market constraints and enormous hardware design and fabrication costs have pushed the semiconductor industry toward hardware Intellectual Properties (IP) core design. However, the globalization of the integrated circuits (IC) supply chain exposes IP providers to theft and illegal redistribution of IPs. Watermarking and fingerprinting are proposed to detect IP piracy. Nevertheless, they come with additional hardware overhead and cannot guarantee IP security as advanced attacks are reported to remove the watermark, forge, or bypass it. In this work, we propose a novel methodology, GNN4IP, to assess similarities between circuits and detect IP piracy. We model the hardware design as a graph and construct a graph neural network model to learn its behavior using the comprehensive dataset of register transfer level codes and gate-level netlists that we have gathered. GNN4IP detects IP piracy with 96% accuracy in our dataset and recognizes the original IP in its obfuscated version with 100% accuracy.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586150"}, {"primary_key": "2117699", "vector": [], "sparse_vector": [], "title": "BayesFT: Bayesian Optimization for Fault Tolerant Neural Network Architecture.", "authors": ["Nanyang Ye", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To deploy deep learning algorithms on resource-limited scenarios, an emerging device-resistive random access memory (ReRAM) has been regarded as promising via analog computing. However, the practicability of ReRAM is primarily limited due to the weight drifting of ReRAM neural networks due to multi-factor reasons, including manufacturing, thermal noises, and etc. In this paper, we propose a novel Bayesian optimization method for fault tolerant neural network architecture (BayesFT). For neural architecture search space design, instead of conducting neural architecture search on the whole feasible neural architecture search space, we first systematically explore the weight drifting tolerance of different neural network components, such as dropout, normalization, number of layers, and activation functions in which dropout is found to be able to improve the neural network robustness to weight drifting. Based on our analysis, we propose an efficient search space by only searching for dropout rates for each layer. Then, we use Bayesian optimization to search for the optimal neural architecture robust to weight drifting. Empirical experiments demonstrate that our algorithmic framework has outperformed the state-of-the-art methods by up to 10 times on various tasks, such as image classification and object detection.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586115"}, {"primary_key": "2117700", "vector": [], "sparse_vector": [], "title": "FedLight: Federated Reinforcement Learning for Autonomous Multi-Intersection Traffic Signal Control.", "authors": ["Yutong Ye", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Mingsong Chen"], "summary": "Although Reinforcement Learning (RL) has been successfully applied in traffic control, it suffers from the problems of high average vehicle travel time and slow convergence to optimized solutions. This is because, due to the scalability restriction, most existing RL-based methods focus on the optimization of individual intersections while the impact of their cooperation is neglected. Without taking all the correlated intersections as a whole into account, it is difficult to achieve global optimization goals for complex traffic scenarios. To address this issue, this paper proposes a novel federated reinforcement learning approach named FedLight to enable optimal signal control policy generation for multi-intersection traffic scenarios. Inspired by federated learning, our approach supports knowledge sharing among RL agents, whose models are trained using decentralized traffic data at intersections. Based on such model-level collaborations, both the overall convergence rate and control quality can be significantly improved. Comprehensive experimental results demonstrate that compared with the state-of-the-art techniques, our approach can not only achieve better average vehicle travel time for various multi-intersection configurations, but also converge to optimal solutions much faster.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586175"}, {"primary_key": "2117701", "vector": [], "sparse_vector": [], "title": "JPDHeap: A JVM Heap Design for PM-DRAM Memories.", "authors": ["Litong You", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Real-world e-commerce systems need large cache capacities. Persistent memory (PM) can be employed to enlarge JVMs' cache capacities, meanwhile they incur heavy write slowdowns and garbage collection overheads. This paper proposes JPDheap, a JVM heap design for PM-DRAM memories. A JPDheap is composed of a standard Java heap on DRAM and another heap on PM. The core insight is to separate heap objects and store them on DRAM or PM, allowing objects to be accessed much more efficiently. Our evaluation shows that JPDheap outperforms state-of-the-art heap designs by up to 115.96% in increasing applications' throughput and by up to 87.03% in decreasing the average latency.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586279"}, {"primary_key": "2117702", "vector": [], "sparse_vector": [], "title": "COSAIM: Counter-based Stochastic-behaving Approximate Integer Multiplier for Deep Neural Networks.", "authors": ["Shuyuan Yu", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "In this work, we propose a new counter-based stochastic-behaving approximate integer unsigned multiplier, called COSAIM, for many emerging error tolerant application workloads such as deep neural networks. Unlike existing approximate multipliers, which are based on some deterministic ad-hoc methods or mathematical formula, the new design is an improved stochastic multiplier, which performs improved sequential counting for multiplication operation in a deterministic way. In this work, we further improve the counting efficiency by introducing approximate schemes to significantly speed up the counting process, which leads to significant clock cycle reduction with no accuracy loss. COSAIM bears all the advantages of stochastic computing such as built-in configurability for progressive performance-accuracy trade-off. At the same time, it shows very small latency and high energy efficiency. Our evaluation shows that the COSAIM with error improvement operation can achieve very low error bias (0.06%), along with lower mean error (0.30% to 3.49%), and low peak errors (around 1.81%) with variance of 1. $47\\times 10^{-4}$ %. Experimental results obtained from Xilinx ISE show that compared with the 8-bit exact multiplier baseline, COSAIM can save up to 53.95%, 32.84%, 52.24%, 21.05% in area, power, energy and the product Area. 1/Throughput, respectively. Furthermore, by doing shared parallel design, COSAIM can further lead to improvements in area, power and energy reduction by 60.44%, 53.33% and 68.54%, respectively compared to the baseline. We also implement COSAIM in a Convolution Neural Network (CNN) and test it on CIFAR10 dataset and find that CNN with COSAIM delivers similar inference accuracy compared to some state of art approximate multipliers.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586097"}, {"primary_key": "2117703", "vector": [], "sparse_vector": [], "title": "Cross-Device Profiled Side-Channel Attacks using Meta-Transfer Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning (DL) based profiling side channel analysis (SCA) pose a great threat to embedded devices. An adversary can break the target encryption engine through physical leakage of power or electromagnetic (EM) emanations collected from a profiling device. However, creating a successful DL based SCA model relies on a large amount of data. This presents a large barrier to those interested in applying DL for SCA. In this paper, we propose a novel attack mechanism that adopts meta-transfer learning to transfer DL networks among target devices by judiciously extracting information from a profiling device even using different side-channel sources. Supported by our method, a cross-device and/or cross-domain SCA attack becomes possible among different designs. In comparison to previous attack methodologies, we significantly reduce training costs and the number of traces $(\\lt 3$ for power and $\\lt 8$ for EM) required for SCA attacks on both unprotected or masked Advanced Encryption Standard (AES) implementations.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586100"}, {"primary_key": "2117704", "vector": [], "sparse_vector": [], "title": "Optimizing ADC Utilization through Value-Aware Bypass in ReRAM-based DNN Accelerator.", "authors": ["HanCheon Yun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "ReRAM-based Processing-In-Memory (PIM) has been widely studied as a promising approach for Deep Neural Networks (DNN) accelerator with its energy-efficient analog operations. However, the domain conversion process for the analog operation requires frequent accesses to power-hungry Analog-to-Digital Converter (ADC), hindering the overall energy efficiency. Although previous research has been suggested to address this problem, the ADC cost has not been sufficiently reduced because of its unsuitable approach for ReRAM. In this paper, we propose mixed-signal-based value-aware bypass techniques to optimize the ADC utilization of the ReRAM-based PIM. By utilizing the property of bit-line (BL) level value distribution, the proposed work bypasses the redundant ADC operations depending on the magnitude of value. Evaluation results show that our techniques successfully reduce ADC access and improve overall energy efficiency by 2.48 × -3.07 × compared to ISAAC.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586140"}, {"primary_key": "2117705", "vector": [], "sparse_vector": [], "title": "Invited: Hardware-aware Real-time Myocardial Segmentation Quality Control in Contrast Echocardiography.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Haiyun Yuan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jingtong Hu", "<PERSON><PERSON>"], "summary": "Automatic myocardial segmentation of contrast echocardio-graphy has shown great potential in the quantification of myocardial perfusion parameters. Segmentation quality control is an important step to ensure the accuracy of segmentation results for quality research as well as its clinical application. Usually, the segmentation quality control happens after the data acquisition. At the data acquisition time, the operator could not know the quality of the segmentation results. On-the-fly segmentation quality control could help the operator to adjust the ultrasound probe or retake data if the quality is unsatisfied, which can greatly reduce the effort of time-consuming manual correction. However, it is infeasible to deploy state-of-the-art DNN-based models because the segmentation module and quality control module must fit in the limited hardware resource on the ultrasound machine while satisfying strict latency constraints. In this paper, we propose a hardware-aware neural architecture search framework for automatic myocardial segmentation and quality control of contrast echocardiography. We explicitly incorporate the hardware latency as a regularization term into the loss function during training. The proposed method searches the best neural network architecture for the segmentation module and quality prediction module with strict latency.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586158"}, {"primary_key": "2117706", "vector": [], "sparse_vector": [], "title": "Training Acceleration for Deep Neural Networks: A Hybrid Parallelization Strategy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) are widely investigated due to their striking performance in various applications of artificial intelligence. However, with DNNs becoming larger and deeper, the computing resource of a single hardware accelerator is insufficient to meet the training requirements of popular DNNs. Hence, it is required to train them using multiple accelerators in a distributed setting. For a better utilization of the accelerators and a faster training, it is necessary to partition the whole process into segments that can run in parallel. However, in this context, intra-layer parallelization techniques (i.e., data and model parallelization) often face communication and memory bottlenecks, while the performance and resource utilization of inter-layer parallelization techniques (i.e., using pipelining) depend on the partitioning possibilities of the model. We present EffTra, a synchronous hybrid parallelization strategy, that uses a combination of intra-layer and inter-layer parallelism to realize a distributed training of DNNs. EffTra employs the idea of dynamic programming to try to search for the optimal partitioning of a DNN model and assigns devices to the obtained partitions. Our evaluation shows that EffTra accelerates training by up to 2.0x and 1.78x compared to state-of-the-art inter-layer (i.e., GPipe) and intra-layer (i.e., data parallelism) parallelization techniques respectively.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586300"}, {"primary_key": "2117707", "vector": [], "sparse_vector": [], "title": "Control Variate Approximation for DNN Accelerators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we introduce a control variate approximation technique for low error approximate Deep Neural Network (DNN) accelerators. The control variate technique is used in Monte Carlo methods to achieve variance reduction. Our approach significantly decreases the induced error due to approximate multiplications in DNN inference, without requiring time-exhaustive retraining compared to state-of-the-art. Leveraging our control variate method, we use highly approximated multipliers to generate power-optimized DNN accelerators. Our experimental evaluation on six DNNs, for Cifar-10 and Cifar-100 datasets, demonstrates that, compared to the accurate design, our control variate approximation achieves same performance and 24% power reduction for a merely 0.16% accuracy loss.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586092"}, {"primary_key": "2117708", "vector": [], "sparse_vector": [], "title": "Max-PIM: Fast and Efficient Max/Min Searching in DRAM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, in-DRAM computing is becoming one promising technique to address the notorious 'memory-wall' issue for big data processing. In this work, for the first time, we propose a novel 'Min/Max-in-memory' algorithm based on iterative XNOR bit-wise comparison, which supports parallel inmemory searching for minimum and maximum of bulk data stored in DRAM as unsigned & signed integers, fixed-point and floating numbers. We then develop a new processing-in-DRAM architecture, called Max-PIM, that supports complete bit-wise Boolean logic and beyond. Differentiating from prior works, Max-PIM is optimized with one-cycle fast XNOR logicin-DRAM operation and in-memory data transpose, which are heavily used and keys to accelerate the proposed Min/Max-in-memory algorithm efficiently. Extensive experiments of utilizing Max-PIM in big data sorting and graph processing applications show that it could speed up ~ 50X and ~ 1000X than GPU and CPU, while only consuming 10% and 1% energy, respectively. Moreover, comparing with recent representative In-DRAM computing platforms, i.e., Ambit [1], DRISA [2], our design could speed up ~ 3X - 10X.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586096"}, {"primary_key": "2117709", "vector": [], "sparse_vector": [], "title": "PIM-Quantifier: A Processing-in-Memory Platform for mRNA Quantification.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Processing-in-memory (PIM) architecture has been considered as a promising solution for the \"memory-wall\" issue in many data-intensive applications, especially in bioinformatics. Recent works of developing PIM for genome alignment and assembling have achieved tremendous improvement, while another important genome analysis - mRNA quantification has not been explored. Efficient and accurate mRNA quantification is a crucial step for molecular signature identification, disease outcome prediction and drug development. In this paper, for the first time, we propose a SOT-MRAM based PIM platform, named PIM-Quantifier, for efficient mRNA quantification. A PIM-friendly alignment-free quantification algorithm is first proposed. Then, we present the optimized PIM architecture/circuit designs and mapping method to efficiently accelerate mRNA quantification. Extensive experiments show that PIM-Quantifier significantly improves mRNA quantification performance than CPU and recent other PIM platforms in efficiency defined as throughput/power.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586144"}, {"primary_key": "2117710", "vector": [], "sparse_vector": [], "title": "Towards Resilient Deployment of In-Memory Neural Networks with High Throughput.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Resistive computing systems (RCSs) promise exascale computing capabilities to inference engines for deep learning. However, the classification accuracy of the accelerated neural networks may be degraded by defects. While hardware-aware training schemes can restore the accuracy of convolutional neural networks (CNNs) with low throughput, the schemes are rendered futile when weights are replicated to improve throughput. On the other hand, we discover that weight replication provides new opportunities for data layout organization. In this paper, we propose a framework for resilient deployment of high throughput CNNs to RCSs. The framework is based on integrating a data layout organization step and a distribution guided training step into the flow for mapping CNNs to RCSs. The data layout organization step involves modifying the weight matrix to crossbar assignments using channel, pixel, and hybrid channel-pixel data layout transformations. The distribution guided training is focused on training CNNs with weights that are amenable for data layout organization. The experimental results demonstrate that the proposed techniques expand the average solution space for data layout organization with 1.4× 10 14 X. This translates into that CNNs with high throughput can be deployed onto RCS with up to 10% defects and still attain high classification accuracy.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586185"}, {"primary_key": "2117711", "vector": [], "sparse_vector": [], "title": "Deep Integration of Circuit Simulator and SAT Solver.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The paper addresses a key aspect of efficient computation in logic synthesis and formal verification, namely, the integration of a circuit simulator and a Boolean satisfiability solver. A novel way of interfacing these is proposed along with a fast preprocessing step to detect easy SAT instances and a new hybrid SAT solver, which is more robust for hardware designs than are state-of-the-art CNF-based solvers. The proposed integration enables a 10x speedup in essential computation engines widely used in industrial EDA tools, including SAT sweeping, combinational and sequential equivalence checking, and computing structural choices for technology mapping. The speedup does not lead to a loss in quality because the computed equivalences are canonical.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586331"}, {"primary_key": "2117712", "vector": [], "sparse_vector": [], "title": "A Unified DNN Weight Pruning Framework Using Reweighted Optimization Methods.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To address the large model size and intensive computation requirement of deep neural networks (DNNs), weight pruning techniques have been proposed and generally fall into two categories, i.e., static regularization-based pruning and dynamic regularization-based pruning. However, the former method currently suffers either complex workloads or accuracy degradation, while the latter one takes a long time to tune the parameters to achieve the desired pruning rate without accuracy loss. In this paper, we propose a unified DNN weight pruning framework with dynamically updated regularization terms bounded by the designated constraint. Our proposed method increases the compression rate, reduces the training time and reduces the number of hyper-parameters compared with state-of-the-art ADMM-based hard constraint method.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586152"}, {"primary_key": "2117713", "vector": [], "sparse_vector": [], "title": "PSC-TG: RTL Power Side-Channel Leakage Assessment with Test Pattern Generation.", "authors": ["<PERSON>", "Jungmin Park", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Power side-channel attacks (SCAs) exploit leakage from cryptographic implementations to recover secrets in a non-invasive manner. Existing power side-channel assessment techniques mostly focus on post-silicon stages, suffering from the extremely low flexibility in changing designs to address identified leakages. In this paper, we propose a framework called PSC-TG which supports side-channel leakage assessment at the earliest stage of design cycle, i.e., RTL, allowing the maximum flexibility for countermeasure deployment. The assessment starts with RTL information flow tracking to identify the most sensitive variables according to pre-defined SCA-aware properties. Then, formal assertions are generated based on these variables and the presumed attack model to derive the corresponding test patterns. Next, the sidechannel vulnerability (SCV) metric is calculated using the estimated power with as low as two patterns to quantify the first-order sidechannel leakage. Besides, PSC-TG can give pass/fail indication for masked implementations at higher orders with t-test. We experimentally evaluate the leakage of multiple non-protected benchmarks at RTL, and validate with gate-level and FPGA results. Also, the t-test results of the masked Simon implementation are consistent with the post-silicon findings.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586210"}, {"primary_key": "2117714", "vector": [], "sparse_vector": [], "title": "F-CAD: A Framework to Explore Hardware Accelerators for Codec Avatar Decoding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Ma", "<PERSON><PERSON>", "Yuecheng Li"], "summary": "Creating virtual avatars with realistic rendering is one of the most essential and challenging tasks to provide highly immersive virtual reality (VR) experiences. It requires not only sophisticated deep neural network (DNN) based codec avatar decoders to ensure high visual quality and precise motion expression, but also efficient hardware accelerators to guarantee smooth real-time rendering using lightweight edge devices, like untethered VR headsets. Existing hardware accelerators, however, fail to deliver sufficient performance and efficiency targeting such decoders which consist of multi-branch DNNs and require demanding compute and memory resources. To address these problems, we propose an automation framework, called F-CAD (Facebook Codec avatar Accelerator Design), to explore and deliver optimized hardware accelerators for codec avatar decoding. Novel technologies include 1) a new accelerator architecture to efficiently handle multi-branch DNNs; 2) a multi-branch dynamic design space to enable fine-grained architecture configurations; and 3) an efficient architecture search for picking the optimized hardware design based on both application-specific demands and hardware resource constraints. To the best of our knowledge, F-CAD is the first automation tool that supports the whole design flow of hardware acceleration of codec avatar decoders, allowing joint optimization on decoder designs in popular machine learning frameworks and corresponding customized accelerator design with cycle-accurate evaluation. Results show that the accelerators generated by F-CAD can deliver up to 122.1 frames per second (FPS) and 91.6% hardware efficiency when running the latest codec avatar decoder. Compared to the state-of-the-art designs, F-CAD achieves 4.0× and 2.8× higher throughput, 62.5% and 21.2% higher efficiency than DNNBuilder [1] and HybridDNN [2] by targeting the same hardware device.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586192"}, {"primary_key": "2117715", "vector": [], "sparse_vector": [], "title": "Attentional Transfer is All You Need: Technology-aware Layout Pattern Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>. <PERSON>"], "summary": "Having a set of comprehensive VLSI layout patterns is important in researches and applications of design for manufacturability (DFM). However, due to the complexity of the manufacturing process, a large and diverse layout pattern library is usually not available, especially during the early stages of the next technology generation, and this will slow down the technology node development. Many previous pattern generation methods rely on complex rule-based manual guidance or a massive number of existing patterns in the new technology node for learning, which are both costly and with limited availability. Instead of requiring these expensive resources, we propose an attentional transfer-based framework, named CUP-EUV, learning to reuse knowledge from previous technology nodes that should have reserved an enormous amount of layout resources. With the guidance of transferred knowledge, a pattern generation model can be trained by only a small number of patterns from the expensive EUV designs. Experiments show that our model can generate new patterns with much higher performance than the state-of-the-art approaches.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586227"}, {"primary_key": "2117716", "vector": [], "sparse_vector": [], "title": "A Lightweight Isolation Mechanism for Secure Branch Predictors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recently exposed vulnerabilities reveal that branch predictors shared by different processes leave the attackers with the opportunities for malicious training and perception. Instead of flush-based or physical isolation of hardware resources, we want to achieve isolation of the content in these hardware tables with some lightweight processing using randomization as follows. (1) Content encoding. We propose to use hardware-based thread-private random numbers to encode the contents of the branch predictor tables. It achieves a similar effect of logical isolation but adds little in terms of space or time overheads. (2) Index encoding. We propose a randomized index mechanism of the branch predictor. This disrupts the correspondence between the branch instruction address and the branch predictor entry, thus increases the noise for malicious perception attacks. Our analyses using an FPGA-based RISC-V processor prototype and additional auxiliary simulations suggest that the proposed mechanisms incur a very small performance cost while providing strong protection.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586178"}, {"primary_key": "2117717", "vector": [], "sparse_vector": [], "title": "SFLU: Synchronization-Free Sparse LU Factorization for Fast Circuit Simulation on GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sparse LU factorization is one of the key building blocks of sparse direct solvers and often dominates the computing time of circuit simulation programs. Existing GPU-accelerated sparse LU factorization methods either offload relatively small dense matrix-matrix multiplications to GPU cores, or extract level-set information to parallelize elimination operations in each level. However, because of the insufficient parallelism, neither of the methods can saturate a large amount of compute units on modern GPUs.We in this paper propose a synchronization-free sparse LU factorization algorithm called SFLU. To saturate GPU cores, our method lets each thread block eliminate a column and runs all the thread blocks at the same time. Through communicating dependency information stored on global memory, all the thread blocks either busy wait to run or get updated by their previous columns. Because elimination of all the columns work concurrently, our method avoids any barrier synchronization and saturates GPU resources. By benchmarking over 1000 sparse matrices on an NVIDIA Titan RTX GPU, our SFLU outperforms SuperLU and GLU by a factor of on average 155.71 and 8.21 (up to 3585.62 and 252.66), respectively.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586141"}, {"primary_key": "2117718", "vector": [], "sparse_vector": [], "title": "Neural Pruning Search for Real-Time Object Detection of Autonomous Vehicles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yuxuan <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Object detection plays an important role in self-driving cars for security development. However, mobile systems on self-driving cars with limited computation resources lead to difficulties for object detection. To facilitate this, we propose a compiler-aware neural pruning search framework to achieve high-speed inference on autonomous vehicles for 2D and 3D object detection. The framework automatically searches the pruning scheme and rate for each layer to find a best-suited pruning for optimizing detection accuracy and speed performance under compiler optimization. Our experiments demonstrate that for the first time, the proposed method achieves (close-to) real-time, 55ms and 97ms inference times for YOLOv4 based 2D object detection and PointPillars based 3D detection, respectively, on an off-the-shelf mobile phone with minor (or no) accuracy loss.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586163"}, {"primary_key": "2117719", "vector": [], "sparse_vector": [], "title": "A Compute-in-Memory Architecture Compatible with 3D NAND Flash that Parallelly Activates Multi-Layers.", "authors": ["<PERSON>", "Chu Yan", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Compute-In-Memory (CIM) architectures based on emerging non-volatile memories have demonstrated great potential in accelerating neural network computation for AI applications. However, the reliability challenges associated with multi-level cells and the lack of mature 3D-integration scheme have limited the model size and energy efficiency of these architectures. In this work, we propose a novel NAND-based architecture to efficiently accelerate the vector-matrix multiplication for deep neural networks. The proposed approach is fully compatible with 3D-NAND and allows multiple layers of wordline (WL) planes to be activated in parallel, as opposed to the previous layer-by-layer activation. The revolutionary linear-V T correction and positive-negative weights techniques help to achieve multilevel weight storage and better computing precision. The feasibility and accuracy of the proposed architecture have been verified using TCAD, SPICE and system-level simulations based on commercial 3D-NAND parameters. Major advantages of the approach include $16 \\sim32\\mathrm{x}$ increase of array utilization and $64 \\sim128\\mathrm{x}$ reduction of read power consumption.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586271"}, {"primary_key": "2117720", "vector": [], "sparse_vector": [], "title": "MAT: Processing In-Memory Acceleration for Long-Sequence Attention.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Attention-based machine learning is used to model long-term dependencies in sequential data. Processing these models on long sequences can be prohibitively costly because of the large memory consumption. In this work, we propose MAT, a processing in-memory (PIM) framework, to accelerate long-sequence attention models. MAT adopts a memory-efficient processing flow for attention models to process sub-sequences in a pipeline with much smaller memory footprint. MAT utilizes a reuse-driven data layout and an optimal sample scheduling to optimize the performance of PIM attention. We evaluate the efficiency of MAT on two emerging long-sequence tasks including natural language processing and medical image processing. Our experiments show that MAT is $2.7 \\times$ faster and $3.4 \\times$ more energy efficient than the state-of-the-art PIM acceleration. As compared to TPU and GPU, MAT is $5.1 \\times$ and $16.4 \\times$ faster while consuming $27.5 \\times$ and $41.0 \\times$ less energy.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586212"}, {"primary_key": "2117721", "vector": [], "sparse_vector": [], "title": "An Energy-Efficient Low-Latency 3D-CNN Accelerator Leveraging Temporal Locality, Full Zero-Skipping, and Hierarchical Load Balance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Three-dimensional convolutional neural network (3D-CNN) has demonstrated outstanding classification performance in video recognition compared to two-dimensional CNN (2D-CNN), since 3D-CNN not only learns the spatial features of each frame, but also learns the temporal features across all frames. However, 3D-CNN suffers from intensive computation and data movement. To solve these issues, an energy-efficient low-latency 3D-CNN accelerator is proposed. Temporal locality and small differential value dropout are used to increase the sparsity of activation. Furthermore, to fully utilize the sparsity of weight and activation, a full zero-skipping convolutional microarchitecture is proposed. A hierarchical load-balancing scheme is also introduced to improve resource utilization. With the proposed techniques, a 3D-CNN accelerator is designed in a 55-nm low-power CMOS technology, bringing in up to 9.89× speedup compared to the baseline implementation. Benchmarked with C3D, the proposed accelerator achieves an energy efficiency of 4.66 TOPS/W at 100 MHz and 1.08 V supply voltage.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586299"}, {"primary_key": "2117722", "vector": [], "sparse_vector": [], "title": "BlockGNN: Towards Efficient GNN Acceleration Using Block-Circulant Weight Matrices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Shi", "<PERSON><PERSON>", "Yijin Guan", "Guangyu Sun", "<PERSON><PERSON><PERSON>"], "summary": "In recent years, Graph Neural Networks (GNNs) appear to be state-of-the-art algorithms for analyzing non-euclidean graph data. By applying deep-learning to extract high-level representations from graph structures, GNNs achieve extraordinary accuracy and great generalization ability in various tasks. However, with the ever-increasing graph sizes, more and more complicated GNN layers, and higher feature dimensions, the computational complexity of GNNs grows exponentially. How to inference GNNs in real time has become a challenging problem, especially for some resource-limited edge-computing platforms.To tackle this challenge, we propose BlockGNN, a software-hardware co-design approach to realize efficient GNN acceleration. At the algorithm level, we propose to leverage block-circulant weight matrices to greatly reduce the complexity of various GNN models. At the hardware design level, we propose a pipelined CirCore architecture, which supports efficient block-circulant matrices computation. Basing on CirCore, we present a novel BlockGNN accelerator to compute various GNNs with low latency. Moreover, to determine the optimal configurations for diverse deployed tasks, we also introduce a performance and resource model that helps choose the optimal hardware parameters automatically. Comprehensive experiments on the ZC706 FPGA platform demonstrate that on various GNN tasks, BlockGNN achieves up to 8.3× speedup compared to the baseline HyGCN architecture and 111.9× energy reduction compared to the Intel Xeon CPU platform.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586181"}, {"primary_key": "2117723", "vector": [], "sparse_vector": [], "title": "Distilling Arbitration Logic from Traces using Machine Learning: A Case Study on NoC.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Arbitration logic is extensively used in modern computer architectures to dynamically determine how shared hardware resources are allocated or accessed. Recent work has shown that machine learning techniques can learn non-obvious yet effective arbitration policies, which in simulation demonstrate superior performance over human-designed heuristics. However, existing methods based on deep learning are too expensive to be directly implemented as an arbitration unit in hardware. While some prior efforts managed to manually analyze and reduce a deep learning model into relatively small circuits in certain cases, such ad hoc and labor-intensive approaches cannot easily generalize. In this work, we propose a new methodology to automatically \"distill\" the arbitration logic from simulation traces. Starting by training a deep learning model, we leverage tree-based models as a bridge to convert the more complex model to a compact logic implementation. This paper presents a case study of the proposed methodology on a network-on-chip port arbitration task. Compared with an array of combinational multipliers that exactly computes the neural network output, our arbitration logic achieves up to 282x area reduction without significant performance degradation. Under the training traffic, our arbitration logic achieves up to 64x reduction in average packet latency and up to 5% increase in network throughput over the FIFO arbitration policy. The distilled arbitration policy is also able to generalize to different injection rates and traffic patterns.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586301"}, {"primary_key": "2117724", "vector": [], "sparse_vector": [], "title": "Late Breaking Results: Incremental 3D Global Routing Considering Cell Movement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Placement and routing are two key problems in VLSI physical design. However, there may be out of sync between the two problems with congestion and routing resources. Therefore, it is desirable to design an efficient and highly coupled placement and routing engine. This paper proposes an incremental 3D global routing engine considering cell movement and complex routing constraints to relocate cells and reroute nets. We develop an efficient movement evaluation method to find desired locations and estimated routing resources for each cell. Then, we adopt an iterative approach to move cells to reduce routing resources. To reduce the time consumption of rerouting, we propose two technologies (searching space reduction and data structure optimization) to speed up the rerouting process. Compared with the participating teams at the 2020 CAD Contest at ICCAD based on the contest benchmarks, experiment results show that our proposed algorithm achieves the best runtime and routing resources while satisfying all the routing constraints.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586277"}, {"primary_key": "2117725", "vector": [], "sparse_vector": [], "title": "SEALing Neural Network Models in Encrypted Deep Learning Accelerators.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep learning (DL) accelerators suffer from a new security problem, i.e., being vulnerable to physical access based attacks. An adversary can easily obtain the entire neural network (NN) model by physically snooping the memory bus that connects the accelerator chip with DRAM memory. Therefore, memory encryption becomes important for DL accelerators to improve their security. Nevertheless, we observe that traditional memory encryption techniques that have been efficiently used in CPU systems cause significant performance degradation when directly used in DL accelerators, due to the big bandwidth gap between the memory bus and the encryption engine. To address this problem, our paper proposes SEAL, a Secure and Efficient Accelerator scheme for deep Learning to enhance the performance of encrypted DL accelerators by improving the data access bandwidth. Specifically, SEAL leverages a criticality-aware smart encryption scheme that identifies partial data having no impact on the security of NN models and allows them to bypass the encryption engine, thus reducing the amount of data to be encrypted without affecting security. Extensive experimental results demonstrate that, compared with existing memory encryption techniques, SEAL achieves 1.34 – 1.4× overall performance improvement.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586199"}, {"primary_key": "2117726", "vector": [], "sparse_vector": [], "title": "PAVFuzz: State-Sensitive Fuzz Testing of Protocols in Autonomous Vehicles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yu <PERSON>"], "summary": "The rapid development of in-vehicle networks and protocols brings efficient communication service but also increases the risk of attack. Any vulnerability may be leveraged to cause serious consequences. It is of vital importance to guarantee their security. However, the vulnerability detection efficiency of traditional techniques such as fuzzing is challenged by the complex relations among protocol states.In this paper, we propose PAVFuzz, a state-sensitive fuzz testing framework to secure those protocols used in autonomous vehicles. It automatically learns relations between two data elements in different protocol states. The relations will then be used to calculate and update the mutation weight of each data element continuously. Accordingly, PAVFuzz is able to select the target data elements and perform state-sensitive mutation to boost the efficiency. Experiments show that, compared with state-of-the-art fuzzers Peach and AFL, PAVFuzz increases branch coverage by averagely 22.51% and 369.19% within 24 hours. It has successfully exposed 12 serious previously unknown vulnerabilities among several protocols that are widely used in autonomous vehicles, such as RTPS and SOME/IP. We have reported them to the developers and corresponding patches have been released.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586321"}, {"primary_key": "2117727", "vector": [], "sparse_vector": [], "title": "A Resource Binding Approach to Logic Obfuscation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Logic locking has been proposed to counter security threats during IC fabrication. Such an approach restricts unauthorized use by injecting sufficient module level error to derail application level IC functionality. However, recent research has identified a trade-off between the error rate of logic locking and its resilience to a Boolean satisfiablity (SAT) attack. As a result, logic locking often cannot inject sufficient error to impact an IC while maintaining SAT resilience. In this work, we propose using architectural context available during resource binding to co-design architectures and locking configurations capable of high corruption and SAT resilience simultaneously. To do so, we propose 2 security-focused binding/locking algorithms and apply them to bind/lock 11 MediaBench benchmarks. The resulting circuits showed a 26x and 99x increase in the application errors of a fixed locking configuration while maintaining SAT resilience and incurring minimal overhead compared to other binding schemes. Locking applied post-binding could not achieve a high application error rate and SAT resilience simultaneously.", "published": "2021-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1109/DAC18074.2021.9586179"}]