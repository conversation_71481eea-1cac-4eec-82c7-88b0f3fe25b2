[{"primary_key": "4491030", "vector": [], "sparse_vector": [], "title": "Semi-partitioning under a Blocking-Aware Task Allocation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Semi-partitioned scheduling is a resource efficient scheduling approach compared to the conventional multiprocessor scheduling approaches in terms of system utilization and migration overhead. Semi-partitioned scheduling can better utilize processor bandwidth compared to the partitioned scheduling while introducing less overhead compared to the global scheduling. Various techniques have been proposed to schedule tasks in a semi-partitioned environment, however, they have used blocking-agnostic allocation mechanisms in presence of resource sharing protocols. Since, the allocation mechanism can highly affect the system schedulability, in this paper we provide a blocking-aware allocation mechanism for semi-partitioned scheduling framework under a suspension-based resource sharing protocol. We have applied new heuristics for sorting the tasks in the algorithm that shows improvements upon system schedulability. Finally, we present our preliminary results.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.48"}, {"primary_key": "4491031", "vector": [], "sparse_vector": [], "title": "Reducing the Implementation Overheads of IPCP and DFP.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Most resource control protocols such as IPCP (Immediate Priority Ceiling Protocol) require a kernel system call to implement the necessary control over any shared data. This call can be expensive, involving a potentially slow switch from CPU user-mode to kernel-mode (and back). In this paper we look at two anticipatory schemes (IPCP and DFP - Deadline Floor Protocol) and show how they can be implemented with the minimum number of calls on the kernel. Specifically, no kernel calls are needed when there is no contention, and only one when there is. A standard implementation would need two such calls. The protocols developed are verified by the use of model checking. A prototype implementation is described for POSIX pThreads (thus opening up improvements to a range of programming approaches). Experimental results demonstrate the effectiveness of the scheme, showing average case savings of 86%.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.35"}, {"primary_key": "4491032", "vector": [], "sparse_vector": [], "title": "Energy-Aware Task Allocation onto Unrelated Heterogeneous Multicore Platform for Mixed Criticality Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Heterogeneous multicore platforms have become an attractive choice to deploy mixed criticality systems demanding diverse computational requirements. One of the major challenges is to efficiently harness the computational power of these multicore platforms while deploying mixed criticality applications. The problem is acerbated with an additional demand of energy efficiency. It is particularly relevant for the battery powered embedded systems. We propose a partitioning algorithm for unrelated heterogeneous multicore platforms to map mixed criticality applications that ensures the timeliness property and reduces the energy consumption.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.46"}, {"primary_key": "4491033", "vector": [], "sparse_vector": [], "title": "Periodically-Scheduled Controller Analysis Using Hybrid Systems Reachability and Continuization.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Cyber-physical systems (CPS) consist of physical entities that obey dynamical laws and interact with software components. A typical CPS implementation includes a discrete controller, where software periodically samples physical state and produces actuation commands according to a real-time schedule. Such a hybrid system can be modeled formally as a hybrid automaton. However, reachability tools to verify specifications for hybrid automata do not perform well on such periodically-scheduled models. This is due to a combination of the large number of discrete jumps and the nondeterminism of the exact controller start time. In this paper, we demonstrate this problem and propose a solution, which is a validated abstraction mechanism where every behavior of the original sampled system is contained in the behaviors of a purely continuous system with an additive nondeterministic input. Reachability tools for hybrid automata can better handle such systems. We further improve the analysis by considering local analysis domains. We automate the proposed technique in the Hyst model transformation tool, and demonstrate its effectiveness in a case study analyzing the design of a yaw-damper for a jet aircraft.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.26"}, {"primary_key": "4491034", "vector": [], "sparse_vector": [], "title": "MC-Fluid: Simplified and Optimally Quantified.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The fluid scheduling model allows for schedules in which an individual task may be assigned a fraction of a processor at each time instant. These assignments are subject to the constraints that no fraction exceeds one and the sum of all the assigned fractions do not exceed the sum of the computing capacities of all the processors at any instant. An algorithm, MC-Fluid, has recently been proposed for scheduling systems of mixed-criticality implicit-deadline sporadic tasks under the fluid scheduling model. MC-Fluid has been shown to have a speedup bound no worse than (1 + √5)/2 or ≈ 1.618 for scheduling dual-criticality systems. We derive here a simplified variant of MC-Fluid called MCF, that has run-time linear in the number of tasks. We prove that this simplified variant has a speedup bound no worse than 4/3 for dual-criticality systems, and show that this implies that MC-Fluid, too, has a speedup bound no worse than 4/3. We know from prior results in uniprocessor mixed-criticality scheduling that no algorithm may have a speedup bound smaller than 4/3, allowing us to conclude that MCF and MC-Fluid are in fact speedup-optimal for dual-criticality scheduling.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.38"}, {"primary_key": "4491035", "vector": [], "sparse_vector": [], "title": "A Time-Predictable Model of Computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Effectiveness of timing analysis of real-time applications depends on the timing predictability of the underlying execution platform. Modern hardware architectures improve average case performance using complex features. However, these features make Worst Case Execution Time (WCET) analysis complicated often leading to pessimistic derived worst case execution time. We present the preliminary design of Synchronous Control Asynchronous Dataflow (SCAD) - a new model of computation targeting time-predictability and competitive performance. Inorder execution of instructions and capability to bypass memory accesses imparts timing predictability to SCAD computational model. We also motivate a code generation technique to optimally utilize the memory bypassing capability of SCAD and that results in increased instruction level parallelism.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.45"}, {"primary_key": "4491036", "vector": [], "sparse_vector": [], "title": "A Quadratic-Time Response Time Upper Bound with a Tightness Property.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The response time analysis (RTA) is one of the fundamental tools used to guarantee the schedulability of sets of real-time tasks scheduled by Fixed Priorities. Also, several analysis methods inspired by RTA have been successfully developed to address more sophisticated execution platforms (distributed systems, multiprocessor) and application models (DAGs). The major issue with RTA is its time complexity, which is NP-hard. Such a complexity shows up when the task set has high utilization and RTA needs to check all jobs until the first idle instant. In this paper, we propose a continuous upper bound to the response time with quadratic time complexity in the number of tasks. Such an upper bound is demonstrated to be tighter than previously proposed ones with linear time complexity. In addition, with two tasks only, we prove that the proposed bound is the tightest continuous function upper bounding the exact response time of sets of tasks with full utilization. Whether or not this property holds with more than two tasks is still an open problem.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.9"}, {"primary_key": "4491037", "vector": [], "sparse_vector": [], "title": "Using Entropy as a Parameter to Schedule Real-Time Tasks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The purpose of this paper is to present the mathematical background for using entropy in real-time scheduling as well as the relationship between entropy and utilization. We present a new scheduling algorithm based on entropy to schedule tasks in real-time systems. The goal is to minimize the uncertainty of the scheduling problem by executing the task with the highest entropy first without missing any deadline. The uncertainty measurement is based on the probability of the execution of a task during the hyper-period. This study aims to present entropy as a new parameter that can be used by researchers in different real-time systems fields.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.44"}, {"primary_key": "4491038", "vector": [], "sparse_vector": [], "title": "k2U: A General Framework from k-Point Effective Schedulability Analysis to Utilization-Based Tests.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To deal with a large variety of workloads in different application domains in real-time embedded systems, a number of expressive task models have been developed. For each individual task model, researchers tend to develop different types of techniques for deriving schedulability tests with different computation complexity and performance. In this paper, we present a general schedulability analysis framework, namely the k2U framework, that can be potentially applied to analyze a large set of real-time task models under any fixed-priority scheduling algorithm, on both uniprocessor and multiprocessor scheduling. The key to k2U is a k-point effective schedulability test, which can be viewed as a \"blackbox\" interface. For any task model, if a corresponding k-point effective schedulability test can be constructed, then a sufficient utilization-based test can be automatically derived. We show the generality of k2U by applying it to different task models, which results in new and improved tests compared to the state-of-the-art.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.18"}, {"primary_key": "4491039", "vector": [], "sparse_vector": [], "title": "Qduino: A Multithreaded Arduino System for Embedded Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Arduino is an open source platform that offers a clear and simple environment for physical computing. It is now widely used in modern robotics and Internet of Things (IoT) applications, due in part to its low-cost, ease of programming, and rapid prototyping capabilities. Sensors and actuators can easily be connected to the analog and digital I/O pins of an Arduino device, which features an on-board microcontroller programmed using the Arduino API. The increasing complexity of physical computing applications has now led to a series of Arduino-compatible devices with faster processors, increased flash storage, larger memories and more complicated I/O architectures. The Intel Galileo, for example, is designed to support the Arduino API on top of a Linux system, code-named Clanton. However, the standard API is restricted to the capabilities found on less powerful devices, lacking support for multithreaded programs, or specification of real-time requirements. In this paper, we present Qduino, a system developed for Arduino compatible boards. Qduino provides an extended Arduino API which, while backward-compatible with the original API, supports real-time multithreaded sketches and event handling. Experiments show the performance gains of Qduino compared to Clanton Linux.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.32"}, {"primary_key": "4491040", "vector": [], "sparse_vector": [], "title": "Cache Sharing and Isolation Tradeoffs in Multicore Mixed-Criticality Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In mixed-critical applications, tension exists between sharing and isolation with respect to hardware resources: while strong isolation might be required for highly critical tasks, somewhat permissive sharing might be reasonable for less critical tasks to improve throughput or average-case performance. In this paper, this tension is examined as it pertains to shared last-level caches (LLCs) on multicore platforms. In particular, criticality-aware optimization techniques based on linear programming are presented for allocating LLC areas in the context of the previously proposed MC2 (mixed-criticality on multicore) framework. Experiments are also presented that show that these techniques can result in significant schedulability improvements.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.36"}, {"primary_key": "4491041", "vector": [], "sparse_vector": [], "title": "Optimal Real-Time Scheduling on Two-Type Heterogeneous Multicore Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Insik Shin"], "summary": "Motivated by the cutting-edge two-type heterogeneous multicore chips, such as ARM's big.LITTLE, that offer a practical support for migration, this paper studies the global (or fully-migrative) approach to two-type heterogeneous multicore scheduling. Our goal is to design an optimal fully-migrative scheduling framework. To achieve this goal in an efficient and simple manner, we break the scheduling problem into two subproblems: workload assignment and schedule generation. We propose a per-cluster workload assignment algorithm, called Hetero-Split, that determines the fractions of workload of each task to be assigned to both clusters without losing feasibility with the complexity of O(n log n), where n is the number of tasks. Furthermore, it provides a couple of important properties (e.g., a dual property) that help to generate an optimal schedule efficiently. We also derive scheduling guidelines to design optimal schedulers for two-type heterogeneous multicore platforms, called Hetero-Fair. By tightly coupling the solutions of Hetero-Split and Hetero-Fair, we develop the first optimal two-type heterogeneous multicore scheduling algorithm, called Hetero-Wrap, that has the same complexity (O(n)) as in the identical multicore case. Finally, concerning a practical point of view, we derive the first bounds on the numbers of intra-and inter-cluster migrations under two-type heterogeneous multicore scheduling, respectively.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.19"}, {"primary_key": "4491042", "vector": [], "sparse_vector": [], "title": "Quantifying the Exact Sub-optimality of Non-preemptive Scheduling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Fixed priority scheduling is used in many real-time systems, however, both preemptive and non-preemptive variants (FP-P and FP-NP) are known to be sub-optimal when compared to an optimal uniprocessor scheduling algorithm such as preemptive Earliest Deadline First (EDF-P). In this paper, we investigate the sub-optimality of fixed priority non-preemptive scheduling. Specifically, we derive the exact processor speed-up factor required to guarantee the feasibility under FP-NP (i.e. schedulablability assuming an optimal priority assignment) of any task set that is feasible under EDF-P. As a consequence of this work, we also derive a lower bound on the sub-optimality of non-preemptive EDF (EDF-NP), which since it matches a recently published upper bound gives the exact sub-optimality for EDF-NP. It is known that neither preemptive, nor non-preemptive fixed priority scheduling dominates the other, i.e., there are task sets that are feasible on a processor of unit speed under FP-P that are not feasible under FP-NP and vice-versa. Hence comparing these two algorithms, there are non-trivial speedup factors in both directions. We derive the exact speed-up factor required to guarantee the FP-NP feasibility of any FP-P feasible task set. Further, we derive upper and lower bounds on the speed-up factor required to guarantee FP-P feasibility of any FP-NP feasible task set. Empirical evidence suggests that the lower bound may be tight, and hence equate to the exact speed-up factor in this case.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.17"}, {"primary_key": "4491043", "vector": [], "sparse_vector": [], "title": "Analyzing Real Time Linear Control Systems Using Software Verification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deployed embedded software interacts with sensors and actuators to control a physical environment. While the evolution of the control system is specified by Ordinary Differential Equations (ODEs), the embedded software periodically senses the state of the system, performs computation over the inputs, and initiates the actuators based on the result of computation. In this paper, we present a bounded time safety verification technique for periodically actuated linear control systems. The model considered in this paper takes into account that the control tasks are executed on a real time operating system and hence the task, in some instances misses the real time deadlines. Using matrix exponentiation, and symbolic evaluation of inputs, we reduce the verification problem of such systems into software verification with computation over reals. We compare different techniques for verifying such software, highlight the merits of each of the approaches, and present our experimental results.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.28"}, {"primary_key": "4491044", "vector": [], "sparse_vector": [], "title": "Improved DRAM Timing Bounds for Real-Time DRAM Controllers with Read/Write Bundling.", "authors": ["<PERSON>", "<PERSON>"], "summary": "As DRAMs become faster, the penalty to reverse the direction of their data buses increases. Yet, existing real-time memory controllers do not reorder read and write commands. Hence, timing bounds are computed by assuming an alternating pattern of reads and writes, thus accounting for several data bus direction reversals, consequently leading to suboptimal results. Therefore, in this paper, we propose a memory controller that reorders read and write commands, which minimizes reversals. Moreover, we prove through a detailed timing analysis that the effect of the reordering is bounded. Finally, we compare our approach analytically with a state-of-the-art real-time memory controller and show that our timing bounds are up to 27% better.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.13"}, {"primary_key": "4491045", "vector": [], "sparse_vector": [], "title": "Uniprocessor Feasibility of Sporadic Tasks Remains coNP-Complete under Bounded Utilization.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "A central problem in real-time scheduling theory is to decide whether a sporadic task system with constrained deadlines is feasible on a preemptive uniprocessor. It is known that this problem is strongly coNP-complete in the general case, but also that there exists a pseudo-polynomial time solution for instances with utilization bounded from above by any constant c, where 0 <; c <; 1. For a long time it has been unknown whether the bounded case also has a polynomial-time solution. We show that for any choice of the constant c, such that 0 <; c <; 1, the bounded feasibility problem is (weakly) coNP-complete, and thus that no polynomial-time solution exists for it, unless P = NP.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.16"}, {"primary_key": "4491046", "vector": [], "sparse_vector": [], "title": "Supporting Real-Time Computer Vision Workloads Using OpenVX on Multicore+GPU Platforms.", "authors": ["<PERSON>", "<PERSON><PERSON> Yang", "<PERSON>"], "summary": "In the automotive industry, there is currently great interest in supporting driver-assist and autonomouscontrol features that utilize vision-based sensing through cameras. The usage of graphics processing units (GPUs) can potentially enable such features to be supported in a cost-effective way, within an acceptable size, weight, and power envelope. OpenVX is an emerging standard for supporting computer vision workloads. OpenVX uses a graph-based software architecture designed to enable efficient computation on heterogeneous platforms, including those that use accelerators like GPUs. Unfortunately, in settings where real-time constraints exist, the usage of OpenVX poses certain challenges. For example, pipelining is difficult to support and processing graphs may have cycles. In this paper, graph transformation techniques are presented that enable these issues to be circumvented. Additionally, a case-study evaluation is presented involving an OpenVX implementation in which these techniques are applied. This OpenVX implementation runs atop a previously developed GPU-management framework called GPUSync. In this case study, the usage of GPUSync's GPU management techniques along with the proposed graph transformations enabled computer vision workloads specified using OpenVX to be supported in a predictable way.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.33"}, {"primary_key": "4491047", "vector": [], "sparse_vector": [], "title": "Modular Performance Analysis of Energy-Harvesting Real-Time Networked Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper studies the performance analysis problem of energy-harvesting real-time network systems in the Real-Time Calculus (RTC) framework. The behavior of an energy-harvesting node turns out to be a generalization of two known components in RTC: it behaves like an AND connector if the capacitor used to temporally store surplus energy has unlimited capacity and there is no energy loss, while it behaves like a greedy processing component (GPC) if the size of the capacitor is zero and thus surplus energy is lost or passed to other nodes immediately. In this paper, methods are developed to analyze the worst-case performance, in terms of delay and backlog, of energy-harvesting nodes as well as compute upper/lower bounds of their data and energy outputs. Moreover, with the proposed analysis methods, we disclose some interesting properties of the worst-case behaviors of energy-harvesting systems, which provide useful information to guide system design. Experiments are conducted to evaluate our theoretical contributions and also confirm that the disclosed properties are not just the result of our analysis, but indeed hold in realistic system behaviors.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.14"}, {"primary_key": "4491048", "vector": [], "sparse_vector": [], "title": "When Is CAN the Weakest Link? A Bound on Failures-in-Time in CAN-Based Real-Time Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Björn B. Brandenburg"], "summary": "A method to bound the Failures In Time (FIT) rate of a CAN-based real-time system, i.e., the expected number of failures in one billion operating hours, is proposed. The method leverages an analysis, derived in the paper, of the probability of a correct and timely message transmission despite host and network failures due to electromagnetic interference (EMI). For a given workload, the derived FIT rate can be used to find an optimal replication factor, which is demonstrated with a case study based on a message set taken from a simple mobile robot.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.31"}, {"primary_key": "4491049", "vector": [], "sparse_vector": [], "title": "Data Acquisition for Real-Time Decision-Making under Freshness Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ming Jin", "<PERSON><PERSON>", "Yi<PERSON><PERSON> Hu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Lu <PERSON>", "Amotz Bar-Noy", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The paper describes a novel algorithm for timely sensor data retrieval in resource-poor environments under freshness constraints. Consider a civil unrest, national security, or disaster management scenario, where a dynamic situation evolves and a decision-maker must decide on a course of action in view of latest data. Since the situation changes, so is the best course of action. The scenario offers two interesting constraints. First, one should be able to successfully compute the course of action within some appropriate time window, which we call the decision deadline. Second, at the time the course of action is computed, the data it is based on must be fresh (i.e., within some corresponding validity interval). We call it the freshness constraint. These constraints create an interesting novel problem of timely data retrieval. We address this problem in resource-scarce environments, where network resource limitations require that data objects (e.g., pictures and other sensor measurements pertinent to the decision) generally remain at the sources. Hence, one must decide on (i) which objects to retrieve and (ii) in what order, such that the cost of deciding on a valid course of action is minimized while meeting data freshness and decision deadline constraints. Such an algorithm is reported in this paper. The algorithm is shown in simulation to reduce the cost of data retrieval compared to a host of baselines that consider time or resource constraints. It is applied in the context of minimizing cost of finding unobstructed routes between specified locations in a disaster zone by retrieving data on the health of individual route segments.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.25"}, {"primary_key": "4491050", "vector": [], "sparse_vector": [], "title": "An Isolation Scheduling Model for Multicores.", "authors": ["<PERSON><PERSON><PERSON> Huang", "Georgia Giannopoulou", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Efficiently exploiting multicore processors for real-time applications is challenging because jobs that run concurrently on different cores can interfere on shared resources, severely complicating precise timing analysis. We propose a new scheduling model called Isolation Scheduling (IS), IS provides a framework to exploiting multicores for real-time applications where tasks are grouped in classes. IS enforces mutually exclusive execution among different task classes, thus avoiding inter-class interference by construction. We show that IS encompasses several recent advances in real-time scheduling as special cases and we propose global and partitioned scheduling algorithms based on this model. Specific results are provided if the task classes correspond to different safety criticality levels.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.21"}, {"primary_key": "4491051", "vector": [], "sparse_vector": [], "title": "Tradeoffs in Real-Time Robotic Task Design with Neuroevolution Learning for Imprecise Computation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a study on the tradeoffs between three design parameters for robotic task systems that function in partially unknown and unstructured environments, and under timing constraints. The design space of these robotic tasks must incorporate at least three dimensions: (1) the amount of training effort to teach the robot to perform the task, (2) the time available to complete the task from the point when the command is given to perform the task, and (3) the quality of the result from performing the task. This paper presents a tradeoff study in this design space for a common robotic task, specifically, grasping of unknown objects in unstructured environments. The imprecise computation model is used to provide a framework for this study. The results were validated with a real robot and contribute to the development of a systematic approach for designing robotic task systems that must function in environments like flexible manufacturing systems of the future.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.27"}, {"primary_key": "4491052", "vector": [], "sparse_vector": [], "title": "Platform-Specific Code Generation from Platform-Independent Timed Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many safety-critical real-time embedded systems need to meet stringent timing constraints such as preserving delay bounds between input and output events. In model-based development, a system is often implemented by using a code generator to automatically generate source code from system models, and integrating the generated source code with a platform. It is challenging to guarantee that the implemented systems preserve required timing constraints, because the timed behavior of the source code and the platform is closely intertwined. In this paper, we address this challenge by proposing a model transformation approach for the code generation. Our approach compensates the platform-processing delays by adjusting the timing parameters in system models, based on an Integer Linear Programming problem formulation. We demonstrate the usefulness of our approach via a case study of infusion pump systems. Experimental results show that the code generated using our approach can better preserve the timing constraints.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.15"}, {"primary_key": "4491053", "vector": [], "sparse_vector": [], "title": "SounDroid: Supporting Real-Time Sound Applications on Commodity Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Insik Shin"], "summary": "A variety of advantages from sounds such as measurement and accessibility introduces a new opportunity for mobile applications to offer broad types of interesting, valuable functionalities, supporting a richer user experience. However, in spite of the growing interests on mobile sound applications, few or no works have been done in focusing on managing an audio device effectively. More specifically, their low level of real-time capability for audio resources makes it challenging to satisfy tight timing requirements of mobile sound applications, e.g., a high sensing rate of acoustic sensing applications. To address this problem, this work presents the SounDroid framework, an audio device management framework for real-time audio requests from mobile sound applications. The design of SounDroid is based on the requirement analysis of audio requests as well as an understanding of the audio playback procedure including the audio request scheduling and dispatching on Android. It then incorporates both real-time audio request scheduling algorithms, called EDF-V and AFDS, and dispatching optimization techniques into mobile platforms, and thus improves the quality-of-service of mobile sound applications. Our experimental results with the prototype implementation of SounDroid demonstrate that it is able to enhance scheduling performance for audio requests, compared to traditional mechanisms (by up to 40% of improvement), while allowing deterministic dispatching latency.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.34"}, {"primary_key": "4491054", "vector": [], "sparse_vector": [], "title": "Modeling and Real-Time Scheduling of Large-Scale Batteries for Maximizing Performance.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern electric vehicles are equipped with an advanced battery management system, responsible for providing the necessary power efficiently from batteries to electric motors while maintaining the batteries within an operational condition. Because discharge-rate and temperature of batteries affect their health and efficiency significantly, batteries are managed to mitigate their discharge and thermal stresses. In this paper, we develop a real-time, efficient integrated management system for discharge-rate and temperature of batteries. To achieve this objective, we first construct a prognosis system predicting the likely states of batteries' capacity and capability. Based on prognostic estimates of the impact of temperature and discharge-rate on the performance, we solve an optimization problem to search for efficient discharging and cooling scheduling. Our experimentation and simulation demonstrate that the proposed management enhances system performance up to 85.3%.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.11"}, {"primary_key": "4491055", "vector": [], "sparse_vector": [], "title": "Distributed Deadline and Renewable Aware Electric Vehicle Demand Response in the Smart Grid.", "authors": ["Fanxin Kong", "<PERSON><PERSON>"], "summary": "Demand response is an important feature and functionality of the future smart grid. Electric vehicles are recognized as a particularly promising resource for demand response given their high charging demand and flexibility in demand management. Recently, researchers begun to apply market-based solutions to electric vehicle demand response. A clear vision, however, remains elusive because existing works overlook three key issues. (i) The hierarchy among electric vehicles (EVs), charging stations, and electric power companies (EPCs). Previous works assume direct interaction between EVs and EPCs and thus confine to single-level market designs. The designed mechanisms are inapplicable here due to ignoring the role of charging stations in the hierarchy. (ii) Temporal aspects of charging loads. Solely focusing on economic aspects makes significant demand reduction, but electric vehicles would end up with little allocated power due to overlooking their temporal constraints. (iii) Renewable generation co-located with charging stations. Market mechanisms that overlook the uncertainty of renewable would cause much inefficiency in terms of both the economic and temporal aspects. To address these issues, we study a new demand response scheme, i.e, hierarchical demand response for electric vehicles via charging stations. We propose that two-level marketing is suitable to this hierarchical scheme, and design a distributed market mechanism that is compatible with both the economic and temporal aspects of electric vehicle demand response. The market mechanism has a hierarchical decision-making structure by which the charging station leads the market and electric vehicles follow and respond to its actions. An appealing feature of the mechanism is the provable convergence to a unique equilibrium solution. At the equilibrium, neither the charging station or electric vehicles can improve their individual economic and/or temporal performance by changing their own strategies. Furthermore, we present a stochastic optimization based algorithm to optimize economic performance for the charging station at the equilibrium, given the predictions of the co-located renewable generation. The algorithm has provable robust performance guarantee in terms of the variance of the prediction errors. We finally evaluate the designed mechanism via detailed simulations. The results show the efficacy and validate the theoretical analysis for the mechanism.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.10"}, {"primary_key": "4491056", "vector": [], "sparse_vector": [], "title": "Dynamic Control for Mixed-Critical Networks-on-Chip.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Networks-on-Chip (NoCs) for future real-time systems must provide service guarantees for applications with different levels of criticality. In this work, we propose an efficient mechanism for supporting mixed-criticality which combines the global, work-conserving scheduling for the end to end guarantees with the local arbitration in routers. We introduce a dynamic control layer with a central Resource Manager (RM) synchronizing transmissions with a dedicated protocol. The proposed mechanism allows to improve over existing solutions through reducing hardware overhead compared to non-blocking routers with rate control as well as temporal overhead compared to Time-Division Multiplexing (TDM). By using formal analysis, we show that RMs provide efficient service guarantees to all synchronized applications. We validate experimentally, using benchmarks, these guarantees along with the performance of the mechanism and induced overhead.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.37"}, {"primary_key": "4491057", "vector": [], "sparse_vector": [], "title": "Static Probabilistic Timing Analysis for Multi-path Programs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper introduces an effective Static Probabilistic Timing Analysis (SPTA) for multi-path programs. The analysis estimates the temporal contribution of an evict-on-miss, random replacement cache to the probabilistic Worst-Case Execution Time (pWCET) distribution of multi-path programs. The analysis uses a conservative join function that provides a proper overapproximation of the possible cache contents and the pWCET distribution on path convergence, irrespective of the actual path followed during execution. Simple program transformations are introduced that reduce the impact of path indeterminism while ensuring sound pWCET estimates. Evaluation shows that the proposed method is efficient at capturing locality in the cache, and substantially outperforms the only prior approach to SPTA for multi-path programs based on path merging. The evaluation results show incomparability with analysis for an equivalent deterministic system using an LRU cache.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.41"}, {"primary_key": "4491058", "vector": [], "sparse_vector": [], "title": "Hardware Optimizations for Anytime Perception and Control.", "authors": ["Nischal K. N.", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Autonomous vehicles promise significant benefits to society, from reduced accident rates to greater mobility for the elderly. The biggest challenge in the design of autonomous vehicles comes from the uncertainty of the environment in which they will operate. Their control algorithms must be able to cope with driving events that occur on widely ranging time scales. For example, relaxed rural driving can accommodate planning actions every few seconds, while imminent collision avoidance requires planning and actuation on the order of a few milliseconds. Thus 'real-time' performance will imply different things depending on the context.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.49"}, {"primary_key": "4491059", "vector": [], "sparse_vector": [], "title": "Towards Realistic Core-Failure-Resilient Scheduling and Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "On uniprocessors, a failure of the single core means unavoidable system failure. However, on multicores, when a core fails, it is conceivable that the computation could continue on remaining cores in a degraded system mode indefinitely, until orderly shutdown and servicing can take place. This would be very desirable for critical applications but, apart from hardware and software support, it would require (i) a scheduling approach designed for providing such resilience and (ii) accompanying schedulability analysis, that derives offline the guarantees about the system meeting its deadlines at run-time, even if one core fails.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.47"}, {"primary_key": "4491060", "vector": [], "sparse_vector": [], "title": "Co-design of Anytime Computation and Robust Control.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Truong X<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Control software of autonomous robots has stringent real-time requirements that must be met to achieve the control objectives. One source of variability in the performance of a control system is the execution time and accuracy of the state estimator that provides the controller with state information. This estimator is typically perception-based (e.g., Computer Vision-based) and is computationally expensive. When the computational resources of the hardware platform become overloaded, the estimation delay can compromise control performance and even stability. In this paper, we define a framework for co-designing anytime estimation and control algorithms, in a manner that accounts for implementation issues like delays and inaccuracies. We construct an anytime perception-based estimator from standard off-the-shelf Computer Vision algorithms, and show how to obtain a trade-off curve for its delay vs estimate error behaviour. We use this anytime estimator in a controller that can use this trade-off curve at runtime to achieve its control objectives at a reduced energy cost. When the estimation delay is too large for correct operation, we provide an optimal manner in which the controller can use this curve to reduce estimation delay at the cost of higher inaccuracy, all the while guaranteeing basic objectives are met. We illustrate our approach on an autonomous hexrotor and demonstrate its advantage over a system that does not exploit co-design.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.12"}, {"primary_key": "4491061", "vector": [], "sparse_vector": [], "title": "Real-Time Support in the Proposal for Fine-Grained Parallelism in Ada.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Ada language has for long provided support for the development of reliable real-time systems, with a model of computation amenable for real-time analysis. To complement the already existent multiprocessor support in the language, an ongoing effort is underway to extend Ada with a fine-grained parallel programming model also suitable for real-time systems. This paper overviews the model which is being proposed, pointing out the main issues still open and road ahead.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.43"}, {"primary_key": "4491062", "vector": [], "sparse_vector": [], "title": "Schedulability Analysis under Graph Routing in WirelessHART Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chenyang Lu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wireless sensor-actuator networks are gaining ground as the communication infrastructure for process monitoring and control. Industrial applications demand a high degree of reliability and real-time guarantees in communication. Because wireless communication is susceptible to transmission failures in industrial environments, industrial wireless standards such as WirelessHART adopt reliable graph routing to handle transmission failures through retransmissions and route diversity. While these mechanisms are critical for reliable communication, they introduce substantial challenges in analyzing the schedulability of real-time flows. This paper presents the first worst-case end-to-end delay analysis for periodic real-time flows under reliable graph routing. The proposed analysis can be used to quickly assess the schedulability of real-time flows with stringent requirements on both reliability and latency. We have evaluated our schedulability analysis against experimental results on a wireless testbed of 69 nodes as well as simulations. Both experimental results and simulations show that our delay bounds are safe and enable effective schedulability tests under reliable graph routing.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.23"}, {"primary_key": "4491063", "vector": [], "sparse_vector": [], "title": "Response Time Analysis with Limited Carry-In for Global Earliest Deadline First Scheduling.", "authors": ["Youcheng Sun", "<PERSON>"], "summary": "We address the problem of schedulability analysis for a set of sporadic real-time tasks scheduled by the Global Earliest Deadline First (G-EDF) policy on a multiprocessor platform. State-of-the-art tests for schedulability analysis of multiprocessor global scheduling are often incomparable. That is, a task set that is judged not schedulable by a test may be verified to be schedulable by another test, and vice versa. In this paper, we first develop a new schedulability test that integrates the limited carry-in technique and Response Time Analysis (RTA) procedure for Global EDF schedulability analysis. Then, we provide an over-approximate variant of this test with better run-time efficiency. Later, we extend these two tests to self-suspending tasks. All schedulability tests proposed in the paper have provable dominance over their state-of-the-art counterparts. Finally, we conduct extensive comparisons among different schedulability tests. Our new tests show significant improvements for schedulability analysis of Global EDF.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.20"}, {"primary_key": "4491064", "vector": [], "sparse_vector": [], "title": "Inter-cell Channel Time-Slot Scheduling for Multichannel Multiradio Cellular Fieldbuses.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON>"], "summary": "Recently there is a growing interest of incorporating cellular architecture (with wired base stations and last-hop wireless connections) into fieldbuses to support mobile real-time applications. A promising trend is that such cellular fieldbuses will go multichannel multiradio, due to the wide availability of cheap multichannel commercial-off-the-shelf (COTS) wireless nodes, and the rise of 4G and future cellular technologies. For multichannel multiradio cellular fieldbuses, per-flow real-time schedulability guarantee in the inter-cell level has not yet been well studied. Particularly, unlike 3G cellular networks, which use static FDMA/CDMA to isolate cells, the multichannel multiradio feature allows neighboring cells to use the same radio frequency channel at different time-slots, or the same time-slot at different radio frequency channels. How to carry out channel time-slot scheduling is therefore the focus of this paper. To address this issue, we propose a greedy scheduling algorithm, together with a polynomial time closed-form schedulability test. The relationship between the schedulability test result, greedy scheduling schedulability, and schedulability is explored. We prove the equivalence of the three for chained cellular fieldbus topology, a typical topology with broad applications. This also implies the optimality of greedy scheduling, and the sufficiency and necessity of the schedulability test in the context of chained topology. To demonstrate and validate these schedulability theories, we carry out a case study on a classic admission planning problem. The schedulability test not only serves as a planning constraint, but also guides us to propose an approximation algorithm to solve the NP-hard admission planning problem. Comparisons to exhaustive search corroborate the validity of our schedulability theories.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.29"}, {"primary_key": "4491065", "vector": [], "sparse_vector": [], "title": "Reverse Flooding: Exploiting Radio Interference for Efficient Propagation Delay Compensation in WSN Clock Synchronization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Clock synchronization is a necessary component in modern distributed systems, especially Wirless Sensor Networks (WSNs). Despite the great effort and the numerous improvements, the existing synchronization schemes do not yet address the cancellation of propagation delays. Up to a few years ago, this was not perceived as a problem, because the time-stamping precision was a more limiting factor for the accuracy achievable with a synchronization scheme. However, the recent introduction of efficient flooding schemes based on constructive interference has greatly improved the achievable accuracy, to the point where propagation delays can effectively become the main source of error. In this paper, we propose a method to estimate and compensate for the network propagation delays. Our proposal does not require to maintain a spanning tree of the network, and exploits constructive interference even to transmit packets whose content are slightly different. To show the validity of the approach, we implemented the propagation delay estimator on top of the FLOPSYNC-2 synchronization scheme. Experimental results prove the feasibility of measuring propagation delays using off-the-shelf microcontrollers and radio transceivers, and show how the proposed solution allows to achieve sub-microsecond clock synchronization even for networks where propagation delays are significant.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.24"}, {"primary_key": "4491066", "vector": [], "sparse_vector": [], "title": "Relaxing Resource-Sharing Constraints for Improved Hardware Management and Schedulability.", "authors": ["<PERSON>"], "summary": "Modern computer architectures, particularly multicore systems, include shared hardware resources such as caches and interconnects that introduce timing-interference channels. Unmanaged access to such resources can adversely affect the execution time of other tasks, and lead to unpredictable execution times and associated analysis pessimism that can entirely negate the benefits of a multicore processor. To mitigate such effects, accesses to shared hardware resources should be managed, for example, by a real-time locking protocol. However, accesses to some hardware resources can be managed with more relaxed sharing constraints than mutual exclusion while still mitigating timing-interference channels. This paper presents two new classes of sharing constraints, preemptive mutual exclusion, and half-protected sharing, which are motivated by the sharing constraints of buses and caches, respectively. Synchronization algorithms are presented for both sharing constraints, where applicable, on both uni-and multi-processor systems. A fundamentally new analysis technique called idleness analysis is presented to account for the effects of blocking in globally scheduled multiprocessor systems. Experimental results suggest that these relaxed synchronization requirements and improved analysis techniques can improve schedulability by up to 250%. Furthermore, idleness analysis can be applied to existing locking protocols to improve schedulability in many cases.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.22"}, {"primary_key": "4491067", "vector": [], "sparse_vector": [], "title": "Global Real-Time Semaphore Protocols: A Survey, Unified Analysis, and Comparison.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Björn B. Brandenburg"], "summary": "All major real-time suspension-based locking protocols (or semaphore protocols) for global fixed-priority scheduling are reviewed and a new, unified response-time analysis framework applicable to all protocols is proposed. The newly proposed analysis, based on linear programming, is shown to be clearly preferable compared to all prior conventional approaches. Based on the new analysis, all protocols are directly compared with each other in a large-scale schedulability study. Interestingly, the Priority Inheritance Protocol (PIP) and the Flexible Multiprocessor Locking Protocol (FMLP), which are the two oldest and simplest of the considered protocols, are found to perform best.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.8"}, {"primary_key": "4491068", "vector": [], "sparse_vector": [], "title": "Precise Multi-level Inclusive Cache Analysis for WCET Estimation.", "authors": ["<PERSON><PERSON><PERSON> Zhang", "Xenofon D<PERSON>"], "summary": "Multi-level inclusive caches are often used in multi-core processors to simplify the design of cache coherence protocol. However, the use of such cache hierarchies poses great challenges to tight worst-case execution time (WCET) estimation due to the possible invalidation behavior. Traditionally, multi-level inclusive caches are analyzed in a level-by-level manner, and at each level three analyses (i.e. must, may, and persistence) are performed separately. At a particular level, conservative decisions need to be made when the behaviors of other levels are not available, which hurts analysis precision. In this paper, we propose an approach which analyzes a multi-level inclusive cache by integrating the three analyses for all levels together. The approach is based on the abstract interpretation of a concrete operational semantics defined for multi-level inclusive caches. We evaluate the proposed approach and also compare it with two state-of-the-art approaches. From the experimental results, we can observe the proposed approach can significantly improve the analysis precision under relatively small cache size configurations.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.40"}, {"primary_key": "4491069", "vector": [], "sparse_vector": [], "title": "Q-Offload: Quality Aware WiFi Offloading with Link Dynamics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Driven by the proliferation of mobile applications, the conflict between data communication requirement and limited battery capacity is becoming sharp on modern smartphones. Offloading mobile traffic from cellular to WiFi is widely recognized as a viable solution to improve the energy efficiency. However, through extensive field experiments, we find WiFi offloading is not always energy efficient and even consumes more energy than cellular network due to link quality variation. In addition, we also observe that practical data transmission deadline requirement and link utilization allows scheduling of data traffic to time periods with good link quality. Accordingly, we propose Q-offload, the first attempt towards energy efficient WiFi offloading with link dynamics. In Q-offload, we propose an iterative framework to achieve energy efficient WiFi offloading by exploiting good link quality while not affecting user experience. We evaluate the performance of Q-offload through both trace-driven analysis and real-world experiments. The results show that it can achieve 33.5%~55.7% energy efficiency improvement, compared with state-of-the-arts under different conditions.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.30"}, {"primary_key": "4491070", "vector": [], "sparse_vector": [], "title": "EPC: Extended Path Coverage for Measurement-Based Probabilistic Timing Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Measurement-based probabilistic timing analysis (MBPTA) computes trustworthy upper bounds to the execution time of software programs. MBPTA has the connotation, typical of measurement-based techniques, that the bounds computed with it only relate to what is observed in actual program traversals, which may not include the effective worst-case phenomena. To overcome this limitation, we propose Extended Path Coverage (EPC), a novel technique that allows extending the representativeness of the bounds computed by MBPTA. We make the observation data probabilistically path-independent by modifying the probability distribution of the observed timing behaviour so as to negatively compensate for any benefits that a basic block may draw from a path leading to it. This enables the derivation of trustworthy upper bounds to the probabilistic execution time of all paths in the program, even when the user-provided input vectors do not exercise the worst-case path. Our results confirm that using MBPTA with EPC produces fully trustworthy upper bounds with competitively small overestimation in comparison to state-of-the-art MBPTA techniques.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.39"}, {"primary_key": "4491071", "vector": [], "sparse_vector": [], "title": "Deferred Start: A Non-Work-Conserving Model for P-FRP Fixed Priority Task Scheduling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Yu <PERSON>"], "summary": "In real-time systems, FRP (Functional Reactive Programming) is playing and potentially going to play a more important role. Priority-based (preemptive) FRP (P-FRP), a variant of FRP with more real-time characteristics, demands more research in its scheduling and timing analysis. Its abort-andrestart nature indicates that reducing preemptions can be critical for improving system performance. In this paper, we present a non-work conserving scheduling model, Deferred Start, to reduce certain preemptions. Experiments show the improvement on schedulability and task response time.", "published": "2015-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2015.42"}]