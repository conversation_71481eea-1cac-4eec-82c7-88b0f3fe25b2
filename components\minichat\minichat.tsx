'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import MiniChatInputs from './minichat-inputs';
import { Button } from "@/components/ui/button";
import { Paper } from '@/components/survey/class-utils';
import 'katex/dist/katex.min.css';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { models, DEFAULT_MODEL, ModelConfig } from '@/lib/models';
import MiniChatMessagesList, { Message } from './minichat-messages-list';

interface MiniChatProps {
  papers: Paper[];
  isOpen: boolean;
  onClose: () => void;
  width: number;
  onWidthChange: (width: number) => void;
  searchQuery: string;
}

export default function MiniChat({ papers, isOpen, onClose, width, onWidthChange, searchQuery }: MiniChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [selectedModel, setSelectedModel] = useState<ModelConfig>(DEFAULT_MODEL);

  // 检测屏幕宽度
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 从本地存储加载模型选择
  useEffect(() => {
    const savedModel = localStorage.getItem('selectedModel');
    if (savedModel) {
      const model = models.find(m => m.id === savedModel);
      if (model) {
        setSelectedModel(model);
      }
    }
  }, []);

  // 保存模型选择到本地存储
  const handleModelChange = (modelId: string) => {
    const model = models.find(m => m.id === modelId);
    if (model) {
      setSelectedModel(model);
      localStorage.setItem('selectedModel', model.id);
    }
  };

  // 处理思考状态的切换
  const toggleThinking = (index: number) => {
    setMessages(prev => {
      const newMessages = [...prev];
      if (newMessages[index] && newMessages[index].thinking) {
        newMessages[index] = {
          ...newMessages[index],
          showThinking: !newMessages[index].showThinking
        };
      }
      return newMessages;
    });
  };

  // 用户鼠标移动时，调整聊天框宽度
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;
    const newWidth = window.innerWidth - e.clientX;
    if (newWidth >= 300 && newWidth <= 800) {
      onWidthChange(newWidth);
    }
  };

  // 用户鼠标按下时，开始拖拽
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    e.preventDefault(); // 阻止默认行为
  };

  // 用户鼠标松开时，停止拖拽
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 拖拽调整聊天框宽度
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  return (
    <div 
      className={`
        fixed transition-all duration-300 
        ${isOpen ? 'block' : 'hidden'}
        ${isMobile ? 'inset-0 z-50' : 'right-1 top-2 bottom-2 h-[calc(100vh-1rem)]'}
      `} 
      style={!isMobile ? { width: `${width}px` } : undefined}
    >
      <div className={`
        h-full bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 
        relative flex flex-col rounded-2xl shadow-lg border border-border/50
        ${isMobile ? 'w-full' : ''}
      `}>
        {/* 调整宽度 */}
        {!isMobile && (
          <div
            className="absolute left-0 top-0 bottom-0 w-3 cursor-ew-resize hover:bg-primary/20 active:bg-primary/30 transition-colors duration-200 z-50"
            onMouseDown={handleMouseDown}
            style={{ userSelect: 'none' }}
          />
        )}
        {/* 标题栏 */}
        <div className="absolute top-3 left-3 right-3 flex items-center justify-between z-10">
          <div className="flex items-center gap-2 ml-4">
            {/* 选择模型 */}
            <div className="relative">
              <Select value={selectedModel.id} onValueChange={handleModelChange}>
                <SelectTrigger className="min-w-[120px] w-fit h-8 border-0 bg-background hover:bg-muted focus:ring-0 focus:ring-offset-0 rounded-full shadow-sm">
                  <SelectValue>
                    <span className="text-sm whitespace-nowrap">{selectedModel.name}</span>
                  </SelectValue>
                </SelectTrigger>
                <SelectContent className="bg-background rounded-xl shadow-lg border border-border/50">
                  {models.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex flex-col">
                        <span>{model.name}</span>
                        <span className="text-xs text-muted-foreground">{model.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose} className="bg-background hover:bg-muted/50 rounded-full shadow-sm">
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* 聊天框 */}
        <div className="flex-1 overflow-hidden relative">
          {/* 聊天记录列表 */}
          <MiniChatMessagesList
            messages={messages}
            setMessages={setMessages}
            toggleThinking={toggleThinking}
          />

          {/* 输入区整体容器 */}
          <MiniChatInputs
            papers={papers}
            searchQuery={searchQuery}
            messages={messages}
            setMessages={setMessages}
            selectedModel={selectedModel}
          />
        </div>
      </div>
    </div>
  );
}
