[{"primary_key": "2239793", "vector": [], "sparse_vector": [], "title": "Separating words and trace reconstruction.", "authors": ["<PERSON>"], "summary": "We prove that for any distinct x,y ∈ {0,1}n, there is a deterministic finite automaton with O(n1/3) states that accepts x but not y. This improves <PERSON>'s 1989 bound of O(n2/5). Using a similar complex analytic technique, we improve the upper bound on worst case trace reconstruction, showing that any unknown string x ∈ {0,1}n can be reconstructed with high probability from exp(O(n1/5)) independently generated traces.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451118"}, {"primary_key": "2239794", "vector": [], "sparse_vector": [], "title": "Near-optimal learning of tree-structured distributions by <PERSON><PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "We provide finite sample guarantees for the classical Chow<PERSON><PERSON> algorithm (IEEE Trans. Inform. Theory, 1968) to learn a tree-structured graphical model of a distribution. For a distribution P on Σn and a tree T on n nodes, we say T is an ε-approximate tree for P if there is a T-structured distribution Q such that D(P || Q) is at most ε more than the best possible tree-structured distribution for P. We show that if P itself is tree-structured, then the Chow-Liu algorithm with the plug-in estimator for mutual information with O(|Σ|3 nε−1) i.i.d. samples outputs an ε-approximate tree for P with constant probability. In contrast, for a general P (which may not be tree-structured), Ω(n2ε−2) samples are necessary to find an ε-approximate tree. Our upper bound is based on a new conditional independence tester that addresses an open problem posed by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON> (STOC, 2018): we prove that for three random variables X,Y,Z each over Σ, testing if I(X; Y ∣ Z) is 0 or ≥ ε is possible with O(|Σ|3/ε) samples. Finally, we show that for a specific tree T, with O(|Σ|2nε−1) samples from a distribution P over Σn, one can efficiently learn the closest T-structured distribution in KL divergence by applying the add-1 estimator at each node.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451066"}, {"primary_key": "2239795", "vector": [], "sparse_vector": [], "title": "Almost optimal super-constant-pass streaming lower bounds for reachability.", "authors": ["<PERSON><PERSON><PERSON>", "Gillat Kol", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Huacheng Yu"], "summary": "We give an almost quadratic n2−o(1) lower bound on the space consumption of any o(√logn)-pass streaming algorithm solving the (directed) s-t reachability problem. This means that any such algorithm must essentially store the entire graph. As corollaries, we obtain almost quadratic space lower bounds for additional fundamental problems, including maximum matching, shortest path, matrix rank, and linear programming.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451038"}, {"primary_key": "2239796", "vector": [], "sparse_vector": [], "title": "Inverse-exponential correlation bounds and extremely rigid matrices from a new derandomized XOR lemma.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work we prove that there is a function f ∈ E NP such that, for every sufficiently large n and d = √n/logn, fn (f restricted to n-bit inputs) cannot be (1/2 + 2−d)-approximated by F2-polynomials of degree d. We also observe that a minor improvement (e.g., improving d to n1/2+ε for any ε > 0) over our result would imply E NP cannot be computed by depth-3 AC0-circuits of 2n1/2 + ε size, which is a notoriously hard open question in complexity theory.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451132"}, {"primary_key": "2239797", "vector": [], "sparse_vector": [], "title": "A new analysis of differential privacy&apos;s generalization guarantees (invited paper).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give a new proof of the \"transfer theorem\" underlying adaptive data analysis: that any mechanism for answering adaptively chosen statistical queries that is differentially private and sample-accurate is also accurate out-of-sample. Our new proof is elementary and gives structural insights that we expect will be useful elsewhere. We show: 1) that differential privacy ensures that the expectation of any query on the conditional distribution on datasets induced by the transcript of the interaction is close to its true value on the data distribution, and 2) sample accuracy on its own ensures that any query answer produced by the mechanism is close to its conditional expectation with high probability. This second claim follows from a thought experiment in which we imagine that the dataset is resampled from the conditional distribution after the mechanism has committed to its answers. The transfer theorem then follows by summing these two bounds. An upshot of our new proof technique is that the concrete bounds we obtain are substantially better than the best previously known bounds.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465358"}, {"primary_key": "2239798", "vector": [], "sparse_vector": [], "title": "Simple and fast derandomization from very hard functions: eliminating randomness at almost no cost.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Extending the classical \"hardness-to-randomness\" line-of-works, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (FOCS 2020) recently proved that derandomization with near-quadratic time overhead is possible, under the assumption that there exists a function in DTIME[2n] that cannot be computed by randomized SVN circuits of size 2(1−є)· n for a small є.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451059"}, {"primary_key": "2239799", "vector": [], "sparse_vector": [], "title": "Efficient and near-optimal algorithms for sampling connected subgraphs.", "authors": ["<PERSON>"], "summary": "We study the graphlet sampling problem: given an integer k ≥ 3 and a graph G=(V,E), sample a connected induced k-node subgraph of G (also called k-graphlet) uniformly at random. This is a fundamental graph mining primitive, with applications in social network analysis and bioinformatics. The two state-of-the-art techniques are random walks and color coding. The random walk is elegant, but the current upper bounds and lower bounds on its mixing time suffer a gap of Δk−1 where Δ is the maximum degree of G. Color coding is better understood, but requires a 2O(k) m-time preprocessing over the entire graph. Moreover, no efficient algorithm is known for sampling graphlets uniformly — random walks and color coding yield only є-uniform samples. In this work, we provide the following results:", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451042"}, {"primary_key": "2239800", "vector": [], "sparse_vector": [], "title": "A deterministic algorithm for the MST problem in constant rounds of congested clique.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we show that the Minimum Spanning Tree problem (MST) can be solved deterministically in O(1) rounds of the Congested Clique model. In the Congested Clique model there are n players that perform computation in synchronous rounds. Each round consist of a phase of local computation and a phase of communication, in which each pair of players is allowed to exchange O(logn) bit messages. The studies of this model began with the MST problem: in the paper by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [SPAA'03, SICOMP'05] that defines the Congested Clique model the authors give a deterministic O(loglogn) round algorithm that improved over a trivial O(logn) round adaptation of BorÅ¯vka's algorithm. There was a sequence of gradual improvements to this result: an O(logloglogn) round algorithm by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> [PODC'15], an O(log? n) round algorithm by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, [PODC'16] and an O(1) round algorithm by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, [SODA'18], but all those algorithms were randomized. Therefore, the question about the existence of any deterministic o(loglogn) round algorithms for the Minimum Spanning Tree problem remains open since the seminal paper by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> [SPAA'03, SICOMP'05]. Our result resolves this question and establishes that O(1) rounds is enough to solve the MST problem in the Congested Clique model, even if we are not allowed to use any randomness. Furthermore, the amount of communication needed by the algorithm makes it applicable to a variant of the MPC model using machines with local memory of size O(n).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451136"}, {"primary_key": "2239801", "vector": [], "sparse_vector": [], "title": "The metric relaxation for 0-extension admits an Ω(log2/3k) gap.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the 0-Extension problem, where we are given an undirected graph G=(V,E) equipped with non-negative edge weights w:E→ ℝ+, a collection T={ t1,…,tk}⊆ V of k special vertices called terminals, and a semi-metric D over T. The goal is to assign every non-terminal vertex to a terminal while minimizing the sum over all edges of the weight of the edge multiplied by the distance in D between the terminals to which the endpoints of the edge are assigned. 0-Extension admits two known algorithms, achieving approximations of O(logk) [C<PERSON><PERSON><PERSON>-<PERSON><PERSON>-Rabani SICOMP '05] and O(logk/loglogk) [Fakcharoenph<PERSON>-<PERSON><PERSON>-<PERSON>-<PERSON> SODA '03]. Both known algorithms are based on rounding a natural linear programming relaxation called the metric relaxation, in which D is extended from T to the entire of V. The current best known integrality gap for the metric relaxation is Ω (√logk). In this work we present an improved integrality gap of Ω(log2/3k) for the metric relaxation. Our construction is based on the randomized extension of one graph by another, a notion that captures lifts of graphs as a special case and might be of independent interest. Inspired by algebraic topology, our analysis of the gap instance is based on proving no continuous section (in the topological sense) exists in the randomized extension.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451071"}, {"primary_key": "2239802", "vector": [], "sparse_vector": [], "title": "Load balancing with dynamic set of balls and bins.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In dynamic load balancing, we wish to distribute balls into bins in an environment where both balls and bins can be added and removed. We want to minimize the maximum load of any bin but we also want to minimize the number of balls and bins that are affected when adding or removing a ball or a bin. We want a hashing-style solution where we given the ID of a ball can find its bin efficiently. We are given a user-specified balancing parameter c=1+?, where ?e (0,1). Let n and m be the current number of balls and bins. Then we want no bin with load above C= c n/m, referred to as the capacity of the bins. We present a scheme where we can locate a ball checking 1+O(log1/?) bins in expectation. When inserting or deleting a ball, we expect to move O(1/?) balls, and when inserting or deleting a bin, we expect to move O(C/?) balls. Previous bounds were off by a factor 1/?. The above bounds are best possible when C=O(1) but for larger C, we can do much better: We define f=? C when C? log1/?, f=?C· ?log(1/(?C)) when log1/? C<1/2?2, and f=1 when C? 1/2?2. We show that we expect to move O(1/f) balls when inserting or deleting a ball, and O(C/f) balls when inserting or deleting a bin. Moreover, when C? log1/?, we can search a ball checking only O(1) bins in expectation. For the bounds with larger C, we first have to resolve a much simpler probabilistic problem. Place n balls in m bins of capacity C, one ball at the time. Each ball picks a uniformly random non-full bin. We show that in expectation and with high probability, the fraction of non-full bins is (f). Then the expected number of bins that a new ball would have to visit to find one that is not full is (1/f). As it turns out, this is also the complexity of an insertion in our more complicated scheme where both balls and bins can be added and removed.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451107"}, {"primary_key": "2239803", "vector": [], "sparse_vector": [], "title": "Statistical query complexity of manifold estimation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper studies the statistical query (SQ) complexity of estimating d-dimensional submanifolds in ℝn. We propose a purely geometric algorithm called Manifold Propagation, that reduces the problem to three natural geometric routines: projection, tangent space estimation, and point detection. We then provide constructions of these geometric routines in the SQ framework. Given an adversarial STAT(τ) oracle and a target Hausdorff distance precision ε = Ω(τ2/(d+1)), the resulting SQ manifold reconstruction algorithm has query complexity O(n polylog(n) ε−d/2), which is proved to be nearly optimal. In the process, we establish low-rank matrix completion results for SQ’s and lower bounds for randomized SQ estimators in general metric spaces.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451135"}, {"primary_key": "2239804", "vector": [], "sparse_vector": [], "title": "Degree vs. approximate degree and Quantum implications of Huang&apo<PERSON>;s sensitivity theorem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Based on the recent breakthrough of <PERSON> (2019), we show that for any total Boolean function f,", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451047"}, {"primary_key": "2239805", "vector": [], "sparse_vector": [], "title": "Subcubic algorithms for Gomory-Hu tree in unweighted graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Every undirected graph G has a (weighted) cut-equivalent tree T, commonly named after <PERSON><PERSON><PERSON> and <PERSON> who discovered it in 1961. Both T and G have the same node set, and for every node pair s,t, the minimum (s,t)-cut in T is also an exact minimum (s,t)-cut in G.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451073"}, {"primary_key": "2239806", "vector": [], "sparse_vector": [], "title": "Computational thinking in programming language and compiler design (keynote).", "authors": ["<PERSON>"], "summary": "Abstractions and algorithms are at the heart of computational thinking. In this talk I will discuss the evolution of the theory and practice of programming language and compiler design through the lens of computational thinking. Many of the key concepts in this area were introduced at the ACM Symposium on the Theory of Computing.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465350"}, {"primary_key": "2239807", "vector": [], "sparse_vector": [], "title": "Fractionally log-concave and sector-stable polynomials: counting planar matchings and more.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Thuy-<PERSON>ng <PERSON>"], "summary": "We show fully polynomial time randomized approximation schemes (FPRAS) for counting matchings of a given size, or more generally sampling/counting monomer-dimer systems in planar, not-necessarily-bipartite, graphs. While perfect matchings on planar graphs can be counted exactly in polynomial time, counting non-perfect matchings was shown by <PERSON><PERSON><PERSON> (<PERSON> Phys 1987) to be #P-hard, who also raised the question of whether efficient approximate counting is possible. We answer this affirmatively by showing that the multi-site Glauber dynamics on the set of monomers in a monomer-dimer system always mixes rapidly, and that this dynamics can be implemented efficiently on downward-closed families of graphs where counting perfect matchings is tractable. As further applications of our results, we show how to sample efficiently using multi-site Glauber dynamics from partition-constrained strongly Rayleigh distributions, and nonsymmetric determinantal point processes. In order to analyze mixing properties of the multi-site Glauber dynamics, we establish two notions for generating polynomials of discrete set-valued distributions: sector-stability and fractional log-concavity. These notions generalize well-studied properties like real-stability and log-concavity, but unlike them robustly degrade under useful transformations applied to the distribution. We relate these notions to pairwise correlations in the underlying distribution and the notion of spectral independence introduced by <PERSON><PERSON> et al. (FOCS 2020), providing a new tool for establishing spectral independence based on geometry of polynomials. As a byproduct of our techniques, we show that polynomials avoiding roots in a sector of the complex plane must satisfy what we call fractional log-concavity; this generalizes a classic result established by <PERSON><PERSON>rding (<PERSON> <PERSON>ch 1959) who showed homogeneous polynomials that have no roots in a half-plane must be log-concave over the positive orthant.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451123"}, {"primary_key": "2239808", "vector": [], "sparse_vector": [], "title": "Kronecker products, low-depth circuits, and matrix rigidity.", "authors": ["<PERSON>"], "summary": "For a matrix M and a positive integer r, the rank r rigidity of M is the smallest number of entries of M which one must change to make its rank at most r. There are many known applications of rigidity lower bounds to a variety of areas in complexity theory, but fewer known applications of rigidity upper bounds. In this paper, we use rigidity upper bounds to prove new upper bounds in a few different models of computation. Our results include:", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451008"}, {"primary_key": "2239809", "vector": [], "sparse_vector": [], "title": "Adversarial laws of large numbers and optimal regret in online classification.", "authors": ["Noga Alon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "E<PERSON>"], "summary": "Laws of large numbers guarantee that given a large enough sample from some population, the measure of any fixed sub-population is well-estimated by its frequency in the sample. We study laws of large numbers in sampling processes that can affect the environment they are acting upon and interact with it. Specifically, we consider the sequential sampling model proposed by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2020), and characterize the classes which admit a uniform law of large numbers in this model: these are exactly the classes that are online learnable. Our characterization may be interpreted as an online analogue to the equivalence between learnability and uniform convergence in statistical (PAC) learning. The sample-complexity bounds we obtain are tight for many parameter regimes, and as an application, we determine the optimal regret bounds in online learning, stated in terms of Littlestone's dimension, thus resolving the main open question from <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (2009), which was also posed by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (2015).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451041"}, {"primary_key": "2239810", "vector": [], "sparse_vector": [], "title": "Boosting simple learners.", "authors": ["Noga Alon", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Boosting is a celebrated machine learning approach which is based on the idea of combining weak and moderately inaccurate hypotheses to a strong and accurate one. We study boosting under the assumption that the weak hypotheses belong to a class of bounded capacity. This assumption is inspired by the common convention that weak hypotheses are \"rules-of-thumbs\" from an \"easy-to-learn class\". (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> '12, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> '14.) Formally, we assume the class of weak hypotheses has a bounded VC dimension. We focus on two main questions: (i) Oracle Complexity: How many weak hypotheses are needed in order to produce an accurate hypothesis? We design a novel boosting algorithm and demonstrate that it circumvents a classical lower bound by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> ('95, '12). Whereas the lower bound shows that Ω(1/γ2) weak hypotheses with γ-margin are sometimes necessary, our new method requires only Õ(1/γ) weak hypothesis, provided that they belong to a class of bounded VC dimension. Unlike previous boosting algorithms which aggregate the weak hypotheses by majority votes, the new boosting algorithm uses more complex (\"deeper\") aggregation rules. We complement this result by showing that complex aggregation rules are in fact necessary to circumvent the aforementioned lower bound. (ii) Expressivity: Which tasks can be learned by boosting weak hypotheses from a bounded VC class? Can complex concepts that are \"far away\" from the class be learned? Towards answering the first question we identify a combinatorial-geometric parameter which captures the expressivity of base-classes in boosting. As a corollary we provide an affirmative answer to the second question for many well-studied classes, including half-spaces and decision stumps. Along the way, we establish and exploit connections with Discrepancy Theory.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451030"}, {"primary_key": "2239811", "vector": [], "sparse_vector": [], "title": "Discrepancy minimization via a self-balancing walk.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study discrepancy minimization for vectors in ℝn under various settings. The main result is the analysis of a new simple random process in high dimensions through a comparison argument. As corollaries, we obtain bounds which are tight up to logarithmic factors for online vector balancing against oblivious adversaries, resolving several questions posed by <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON> (STOC 2020), as well as a linear time algorithm for logarithmic bounds for the <PERSON><PERSON><PERSON> conjecture.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3450994"}, {"primary_key": "2239812", "vector": [], "sparse_vector": [], "title": "Log-concave polynomials IV: approximate exchange, tight mixing times, and near-optimal sampling of forests.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Thuy-<PERSON>ng <PERSON>"], "summary": "We prove tight mixing time bounds for natural random walks on bases of matroids, determinantal distributions, and more generally distributions associated with log-concave polynomials. For a matroid of rank k on a ground set of n elements, or more generally distributions associated with log-concave polynomials of homogeneous degree k on n variables, we show that the down-up random walk, started from an arbitrary point in the support, mixes in time O(klogk). Our bound has no dependence on n or the starting point, unlike the previous analyses of <PERSON><PERSON> et al. (STOC 2019), <PERSON><PERSON> et al. (FOCS 2019), and is tight up to constant factors. The main new ingredient is a property we call approximate exchange, a generalization of well-studied exchange properties for matroids and valuated matroids, which may be of independent interest. In particular, given a distribution µ over size-k subsets of [n], our approximate exchange property implies that a simple local search algorithm gives a kO(k)-approximation of maxS µ(S) when µ is generated by a log-concave polynomial, and that greedy gives the same approximation ratio when µ is strongly Rayleigh. As an application, we show how to leverage down-up random walks to approximately sample random forests or random spanning trees in a graph with n edges in time O(nlog2 n). The best known result for sampling random forest was a FPAUS with high polynomial runtime recently found by <PERSON><PERSON> et al. (STOC 2019), <PERSON><PERSON> et al. (FOCS 2019). For spanning tree, we improve on the almost-linear time algorithm by <PERSON><PERSON><PERSON> (STOC 2018). Our analysis works on weighted graphs too, and is the first to achieve nearly-linear running time for these problems. Our algorithms can be naturally extended to support approximately sampling from random forests of size between k1 and k2 in time O(n log2 n), for fixed parameters k1, k2.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451091"}, {"primary_key": "2239813", "vector": [], "sparse_vector": [], "title": "Log-concave polynomials in theory and applications (tutorial).", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Log-concave polynomials give rise to discrete probability distributions with several nice properties. In particular, log-concavity of the generating polynomial guarantees the existence of efficient algorithms for approximately sampling from a distribution and finding the size of its support. This class of distributions contains several important examples, including uniform measures over bases or independent sets of matroids, determinantal point processes and strongly Rayleigh measures, measures defined by mixed volumes in Mikowski sums, the random cluster model in certain regimes, and more. In this tutorial, we will introduce the theory and applications of log-concave polynomials and survey some of the recent developments in this area.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465351"}, {"primary_key": "2239814", "vector": [], "sparse_vector": [], "title": "A polynomial-time approximation algorithm for counting words accepted by an NFA (invited paper).", "authors": ["Marcelo <PERSON>s", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Counting the number of words of a certain length accepted by a non-deterministic finite automaton (NFA) is a fundamental problem, which has many applications in different areas such as graph databases, knowledge compilation, and information extraction. Along with this, generating such words uniformly at random is also a relevant problem, particularly in scenarios where returning varied outputs is a desirable feature.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465353"}, {"primary_key": "2239815", "vector": [], "sparse_vector": [], "title": "When is approximate counting for conjunctive queries tractable?", "authors": ["Marcelo <PERSON>s", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Conjunctive queries are one of the most common class of queries used in database systems, and the best studied in the literature. A seminal result of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (STOC 2001) demonstrates that for every class G of graphs, the evaluation of all conjunctive queries whose underlying graph is in G is tractable if, and only if, G has bounded treewidth. In this work, we extend this characterization to the counting problem for conjunctive queries. Specifically, for every class C of conjunctive queries with bounded treewidth, we introduce the first fully polynomial-time randomized approximation scheme (FPRAS) for counting answers to a query in C, and the first polynomial-time algorithm for sampling answers uniformly from a query in C. As a corollary, it follows that for every class G of graphs, the counting problem for conjunctive queries whose underlying graph is in G admits an FPRAS if, and only if, G has bounded treewidth (unless BPP is different from P). In fact, our FPRAS is more general, and also applies to conjunctive queries with bounded hypertree width, as well as unions of such queries.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451014"}, {"primary_key": "2239816", "vector": [], "sparse_vector": [], "title": "Chasing convex bodies with linear competitive ratio (invited paper).", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The problem of chasing convex functions is easy to state: faced with a sequence of convex functions f t over d-dimensional Euclidean spaces, the goal of the algorithm is to output a point x t at each time, so that the sum of the function costs f t (x t ), plus the movement costs ||x t − x t − 1 || is minimized. This problem generalizes questions in online algorithms such as caching and the k-server problem. In 1994, <PERSON> and <PERSON> posed the question of getting an algorithm with a competitive ratio that depends only on the dimension d. In this talk we give an O (d)-competitive algorithm, based on the notion of the Steiner point of a convex body.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465354"}, {"primary_key": "2239817", "vector": [], "sparse_vector": [], "title": "Graph streaming lower bounds for parameter estimation and property testing via a streaming XOR lemma.", "authors": ["<PERSON><PERSON><PERSON>", "V<PERSON><PERSON><PERSON>t N"], "summary": "We study space-pass tradeoffs in graph streaming algorithms for parameter estimation and property testing problems such as estimating the size of maximum matchings and maximum cuts, weight of minimum spanning trees, or testing if a graph is connected or cycle-free versus being far from these properties. We develop a new lower bound technique that proves that for many problems of interest, including all the above, obtaining a (1+є)-approximation requires either nΩ(1) space or Ω(1/є) passes, even on highly restricted families of graphs such as bounded-degree planar graphs. For multiple of these problems, this bound matches those of existing algorithms and is thus (asymptotically) optimal. Our results considerably strengthen prior lower bounds even for arbitrary graphs: starting from the influential work of [<PERSON><PERSON><PERSON>, <PERSON>; SODA 2011], there has been a plethora of lower bounds for single-pass algorithms for these problems; however, the only multi-pass lower bounds proven very recently in [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Yu; FOCS 2020] rules out sublinear-space algorithms with exponentially smaller o(log(1/є)) passes for these problems. One key ingredient of our proofs is a simple streaming XOR Lemma, a generic hardness amplification result, that we prove: informally speaking, if a p-pass s-space streaming algorithm can only solve a decision problem with advantage δ > 0 over random guessing, then it cannot solve XOR of ℓ independent copies of the problem with advantage much better than δℓ. This result can be of independent interest and useful for other streaming lower bounds as well.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451110"}, {"primary_key": "2239818", "vector": [], "sparse_vector": [], "title": "Flow time scheduling with uncertain processing time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of online scheduling on a single machine in order to minimize weighted flow time. The existing algorithms for this problem (STOC '01, SODA '03, FOCS '18) all require exact knowledge of the processing time of each job. This assumption is crucial, as even a slight perturbation of the processing time would lead to polynomial competitive ratio. However, this assumption very rarely holds in real-life scenarios.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451023"}, {"primary_key": "2239819", "vector": [], "sparse_vector": [], "title": "Settling the complexity of Nash equilibrium in congestion games.", "authors": ["<PERSON><PERSON>", "<PERSON>via<PERSON>"], "summary": "We consider (i) the problem of finding a (possibly mixed) Nash equilibrium in congestion games, and (ii) the problem of finding an (exponential precision) fixed point of the gradient descent dynamics of a smooth function f:[0,1]n → ℝ. We prove that these problems are equivalent. Our result holds for various explicit descriptions of f, ranging from (almost general) arithmetic circuits, to degree-5 polynomials. By a very recent result of [<PERSON><PERSON><PERSON> et al., STOC 2021], this implies that these problems are PPAD ∩ PLS-complete. As a corollary, we also obtain the following equivalence of complexity classes:", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451039"}, {"primary_key": "2239820", "vector": [], "sparse_vector": [], "title": "Improved Quantum data analysis.", "authors": ["<PERSON><PERSON><PERSON>", "Ryan <PERSON>&<PERSON>;Donnell"], "summary": "We provide more sample-efficient versions of some basic routines in quantum data analysis, along with simpler proofs. Particularly, we give a quantum ”Threshold Search” algorithm that requires only O((log2 m)/є2) samples of a d-dimensional state ρ. That is, given observables 0 ≤ A1, A2, …, Am ≤ 1 such that (ρ Ai) ≥ 1/2 for at least one i, the algorithm finds j with (ρ Aj) ≥ 1/2−є. As a consequence, we obtain a Shadow Tomography algorithm requiring only O((log2 m)(logd)/є4) samples, which simultaneously achieves the best known dependence on each parameter m, d, є. This yields the same sample complexity for quantum Hypothesis Selection among m states; we also give an alternative Hypothesis Selection method using O((log3 m)/є2) samples.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451109"}, {"primary_key": "2239821", "vector": [], "sparse_vector": [], "title": "Playing unique games on certified small-set expanders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>az <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give an algorithm for solving unique games (UG) instances whenever low-degree sum-of-squares proofs certify good bounds on the small-set-expansion of the underlying constraint graph via a hypercontractive inequality. Our algorithm is in fact more versatile, and succeeds even when the constraint graph is not a small-set expander as long as the structure of non-expanding small sets is (informally speaking) \"characterized\" by a low-degree sum-of-squares proof. Our results are obtained by rounding low-entropy solutions — measured via a new global potential function — to sum-of-squares (SoS) semidefinite programs. This technique adds to the (currently short) list of general tools for analyzing SoS relaxations for worst-case optimization problems.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451099"}, {"primary_key": "2239822", "vector": [], "sparse_vector": [], "title": "Robust linear regression: optimal rates in polynomial time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We obtain robust and computationally efficient estimators for learning several linear models that achieve statistically optimal convergence rate under minimal distributional assumptions. Concretely, we assume our data is drawn from a k-hypercontractive distribution and an є-fraction is adversarially corrupted. We then describe an estimator that converges to the optimal least-squares minimizer for the true distribution at a rate proportional to є2−2/k, when the noise is independent of the covariates. We note that no such estimator was known prior to our work, even with access to unbounded computation. The rate we achieve is information-theoretically optimal and thus we resolve the main open question in Klivans, Kothari and Meka [COLT'18].", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451001"}, {"primary_key": "2239823", "vector": [], "sparse_vector": [], "title": "How much data is sufficient to learn high-performing algorithms? generalization guarantees for data-driven algorithm design.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Algorithms often have tunable parameters that impact performance metrics such as runtime and solution quality. For many algorithms used in practice, no parameter settings admit meaningful worst-case bounds, so the parameters are made available for the user to tune. Alternatively, parameters may be tuned implicitly within the proof of a worst-case guarantee. Worst-case instances, however, may be rare or nonexistent in practice. A growing body of research has demonstrated that data-driven algorithm design can lead to significant improvements in performance. This approach uses a training set of problem instances sampled from an unknown, application-specific distribution and returns a parameter setting with strong average performance on the training set.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451036"}, {"primary_key": "2239824", "vector": [], "sparse_vector": [], "title": "k-forrelation optimally separates Quantum and classical query complexity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (SICOMP '18) showed that any partial function on N bits that can be computed with an advantage δ over a random guess by making q quantum queries, can also be computed classically with an advantage δ/2 by a randomized decision tree making Oq(N1−1/2qδ−2) queries. Moreover, they conjectured the k-Forrelation problem — a partial function that can be computed with q = ⌈ k/2 ⌉ quantum queries — to be a suitable candidate for exhibiting such an extremal separation.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451040"}, {"primary_key": "2239825", "vector": [], "sparse_vector": [], "title": "Near-linear time approximation schemes for Steiner tree and forest in low-dimensional spaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give an algorithm that computes a (1+є)-approximate Steiner forest in near-linear time n · 2(1/є)O(ddim2) (loglogn)2, where ddim is the doubling dimension of the metric space. This improves upon the best previous result due to <PERSON> et al. (SIAM J. Comput. 4 (2018)), who gave a runtime of about n2O(ddim) · 2(ddim/є)O(ddim) √logn. For Steiner tree our methods achieve an even better runtime n (logn)(1/є)O(ddim2).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451063"}, {"primary_key": "2239826", "vector": [], "sparse_vector": [], "title": "Learnability can be independent of set theory (invited paper).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A fundamental result in statistical learning theory is the equivalence of PAC learnability of a class with the finiteness of its Vapnik-Chervonenkis dimension. However, this clean result applies only to binary classification problems. In search for a similar combinatorial characterization of learnability in a more general setting, we discovered a surprising independence of set theory for some basic general notion of learnability. Consider the following statistical estimation problem: given a family F of real valued random variables over some domain X and an i.i.d. sample drawn from an unknown distribution P over X, find f in F such that its expectation w.r.t. P is close to the supremum expectation over all members of F. This Expectation Maximization (EMX) problem captures many well studied learning problems. Surprisingly, we show that the EMX learnability of some simple classes depends on the cardinality of the continuum and is therefore independent of the set theory ZFC axioms. Our results imply that that there exist no \"finitary\" combinatorial parameter that characterizes EMX learnability in a way similar to the VC-dimension characterization of binary classification learnability.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465360"}, {"primary_key": "2239827", "vector": [], "sparse_vector": [], "title": "Bipartite perfect matching as a real polynomial.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We obtain a description of the Bipartite Perfect Matching decision problem as a multilinear polynomial over the Reals. We show that it has full degree and (1−on(1))· 2n2 monomials with non-zero coefficients. In contrast, we show that in the dual representation (switching the roles of 0 and 1) the number of monomials is only exponential in Θ(n logn). Our proof relies heavily on the fact that the lattice of graphs which are \"matching-covered\" is Eulerian.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451002"}, {"primary_key": "2239828", "vector": [], "sparse_vector": [], "title": "A framework for dynamic matching in weighted graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a new framework for computing approximate maximum weight matchings. Our primary focus is on the fully dynamic setting, where there is a large gap between the guarantees of the best known algorithms for computing weighted and unweighted matchings. Indeed, almost all current weighted matching algorithms that reduce to the unweighted problem lose a factor of two in the approximation ratio. In contrast, in other sublinear models such as the distributed and streaming models, recent work has largely closed this weighted/unweighted gap.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451113"}, {"primary_key": "2239829", "vector": [], "sparse_vector": [], "title": "Decoding multivariate multiplicity codes on product sets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Madhu <PERSON>"], "summary": "The multiplicity <PERSON>-<PERSON><PERSON><PERSON> lemma bounds the total multiplicity of zeroes of a multivariate polynomial on a product set. This lemma motivates the multiplicity codes of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> [<PERSON><PERSON>, 2014], who showed how to use this lemma to construct high-rate locally-decodable codes. However, the algorithmic results about these codes crucially rely on the fact that the polynomials are evaluated on a vector space and not an arbitrary product set.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451027"}, {"primary_key": "2239830", "vector": [], "sparse_vector": [], "title": "Optimal inapproximability of satisfiable k-LIN over non-abelian groups.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A seminal result of <PERSON><PERSON><PERSON> (2001) shows that it is NP-hard to find an assignment that satisfies 1/|G|+ε fraction of the constraints of a given k-LIN instance over an abelian group, even if there is an assignment that satisfies (1−ε) fraction of the constraints, for any constant ε>0. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (2004) later showed that the same hardness result holds for k-LIN instances over any finite non-abelian group.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451003"}, {"primary_key": "2239831", "vector": [], "sparse_vector": [], "title": "Reconstruction algorithms for low-rank tensors and depth-3 multilinear circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give new and efficient black-box reconstruction algorithms for some classes of depth-3 arithmetic circuits. As a consequence, we obtain the first efficient algorithm for computing the tensor rank and for finding the optimal tensor decomposition as a sum of rank-one tensors when then input is a constant-rank tensor. More specifically, we provide efficient learning algorithms that run in randomized polynomial time over general fields and in deterministic polynomial time over and for the following classes: 1) Set-multilinear depth-3 circuits of constant top fan-in ((k) circuits). As a consequence of our algorithm, we obtain the first polynomial time algorithm for tensor rank computation and optimal tensor decomposition of constant-rank tensors. This result holds for d dimensional tensors for any d, but is interesting even for d=3. 2) Sums of powers of constantly many linear forms ((k) circuits). As a consequence we obtain the first polynomial-time algorithm for tensor rank computation and optimal tensor decomposition of constant-rank symmetric tensors. 3) Multilinear depth-3 circuits of constant top fan-in (multilinear (k) circuits). Our algorithm works over all fields of characteristic 0 or large enough characteristic. Prior to our work the only efficient algorithms known were over polynomially-sized finite fields (see. Karnin-Shpilka 09'). Prior to our work, the only polynomial-time or even subexponential-time algorithms known (deterministic or randomized) for subclasses of (k) circuits that also work over large/infinite fields were for the setting when the top fan-in k is at most 2 (see <PERSON><PERSON> 16' and <PERSON><PERSON> 20').", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451096"}, {"primary_key": "2239832", "vector": [], "sparse_vector": [], "title": "A framework for quadratic form maximization over convex sets through nonconvex relaxations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the approximability of the following optimization problem. The input is an n× n matrix A=(Aij) with real entries and an origin-symmetric convex body K⊂ ℝn that is given by a membership oracle. The task is to compute (or approximate) the maximum of the quadratic form ∑i=1n∑j=1n Aij xixj=⟨ x,Ax⟩ as x ranges over K. This is a rich and expressive family of optimization problems; for different choices of matrices A and convex bodies K it includes a diverse range of optimization problems like max-cut, Grothendieck/non-commutative Grothendieck inequalities, small set expansion and more. While the literature studied these special cases using case-specific reasoning, here we develop a general methodology for treatment of the approximability and inapproximability aspects of these questions.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451128"}, {"primary_key": "2239833", "vector": [], "sparse_vector": [], "title": "VC dimension and distribution-free sample-based testing.", "authors": ["<PERSON>", "<PERSON><PERSON>.", "<PERSON>"], "summary": "We consider the problem of determining which classes of functions can be tested more efficiently than they can be learned, in the distribution-free sample-based model that corresponds to the standard PAC learning setting. Our main result shows that while VC dimension by itself does not always provide tight bounds on the number of samples required to test a class of functions in this model, it can be combined with a closely-related variant that we call \"lower VC\" (or LVC) dimension to obtain strong lower bounds on this sample complexity.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451104"}, {"primary_key": "2239834", "vector": [], "sparse_vector": [], "title": "Entropy decay in the Swendsen-Wang dynamics on ℤd.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the mixing time of the Swends<PERSON>-<PERSON> dynamics for the ferromagnetic <PERSON><PERSON> and <PERSON>tts models on the integer lattice ℤd. This dynamics is a widely used Markov chain that has largely resisted sharp analysis because it is non-local, i.e., it changes the entire configuration in one step. We prove that, whenever strong spatial mixing (SSM) holds, the mixing time on any n-vertex cube in ℤd is O(logn), and we prove this is tight by establishing a matching lower bound. The previous best known bound was O(n). SSM is a standard condition corresponding to exponential decay of correlations with distance between spins on the lattice and is known to hold in d=2 dimensions throughout the high-temperature (single phase) region. Our result follows from a modified log-So<PERSON>ev inequality, which expresses the fact that the dynamics contracts relative entropy at a constant rate at each step. The proof of this fact utilizes a new factorization of the entropy in the joint probability space over spins and edges that underlies the Swends<PERSON>-<PERSON> dynamics, which extends to general bipartite graphs of bounded degree. This factorization leads to several additional results, including mixing time bounds for a number of natural local and non-local Markov chains on the joint space, as well as for the standard random-cluster dynamics.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451095"}, {"primary_key": "2239835", "vector": [], "sparse_vector": [], "title": "Breaking the quadratic barrier for matroid intersection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Danupon <PERSON>"], "summary": "The matroid intersection problem is a fundamental problem that has been extensively studied for half a century. In the classic version of this problem, we are given two matroids M1 = (V, I1) and M2 = (V, I2) on a comment ground set V of n elements, and then we have to find the largest common independent set S ∈ I1 ∩ I2 by making independence oracle queries of the form \"Is S ∈ I1?\" or \"Is S ∈ I2?\" for S ⊆ V. The goal is to minimize the number of queries.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451092"}, {"primary_key": "2239836", "vector": [], "sparse_vector": [], "title": "Optimal labelling schemes for adjacency, comparability, and reachability.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We construct asymptotically optimal adjacency labelling schemes for every hereditary class containing 2Ω(n2) n-vertex graphs as n→ ∞. This regime contains many classes of interest, for instance perfect graphs or comparability graphs, for which we obtain an adjacency labelling scheme with labels of n/4+o(n) bits per vertex. This implies the existence of a reachability labelling scheme for digraphs with labels of n/4+o(n) bits per vertex and comparability labelling scheme for posets with labels of n/4+o(n) bits per element. All these results are best possible, up to the lower order term.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451102"}, {"primary_key": "2239837", "vector": [], "sparse_vector": [], "title": "A theory of universal learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "How quickly can a given class of concepts be learned from examples? It is common to measure the performance of a supervised machine learning algorithm by plotting its \"learning curve\", that is, the decay of the error rate as a function of the number of training examples. However, the classical theoretical framework for understanding learnability, the PAC model of Vapnik<PERSON><PERSON><PERSON>kis and Valiant, does not explain the behavior of learning curves: the distribution-free PAC model of learning can only bound the upper envelope of the learning curves over all possible data distributions. This does not match the practice of machine learning, where the data source is typically fixed in any given scenario, while the learner may choose the number of training examples on the basis of factors such as computational resources and desired accuracy.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451087"}, {"primary_key": "2239838", "vector": [], "sparse_vector": [], "title": "Minimum cost flows, MDPs, and ℓ1-regression in nearly linear time for dense instances.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Thatchaphol <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we provide new randomized algorithms with improved runtimes for solving linear programs with two-sided constraints. In the special case of the minimum cost flow problem on n-vertex m-edge graphs with integer polynomially-bounded costs and capacities we obtain a randomized method which solves the problem in Õ(m + n1.5) time. This improves upon the previous best runtime of Õ(m √n) [<PERSON><PERSON>'14] and, in the special case of unit-capacity maximum flow, improves upon the previous best runtimes of m4/3 + o(1) [<PERSON><PERSON>'20, <PERSON><PERSON><PERSON>'20] and Õ(m √n) [<PERSON><PERSON>'14] for sufficiently dense graphs.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451108"}, {"primary_key": "2239839", "vector": [], "sparse_vector": [], "title": "New separations results for external information.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We obtain new separation results for the two-party external information complexity of Boolean functions. The external information complexity of a function f(x,y) is the minimum amount of information a two-party protocol computing f must reveal to an outside observer about the input. We prove an exponential separation between external and internal information complexity, which is the best possible; previously no separation was known. We use this result in order to then prove a near-quadratic separation between amortized zero-error communication complexity and external information complexity for total functions, disproving a conjecture of the first author. Finally, we prove a matching upper bound showing that our separation result is tight.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451044"}, {"primary_key": "2239840", "vector": [], "sparse_vector": [], "title": "Sparse nonnegative convolution is equivalent to dense nonnegative convolution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Computing the convolution A ⋆ B of two length-n vectors A,B is an ubiquitous computational primitive, with applications in a variety of disciplines. Within theoretical computer science, applications range from string problems to Knapsack-type problems, and from 3SUM to All-Pairs Shortest Paths. These applications often come in the form of nonnegative convolution, where the entries of A,B are nonnegative integers. The classical algorithm to compute A⋆ B uses the Fast Fourier Transform (FFT) and runs in time O(n logn).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451090"}, {"primary_key": "2239841", "vector": [], "sparse_vector": [], "title": "When is memorization of irrelevant training data necessary for high-accuracy learning?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Modern machine learning models are complex and frequently encode surprising amounts of information about individual inputs. In extreme cases, complex models appear to memorize entire input examples, including seemingly irrelevant information (social security numbers from text, for example). In this paper, we aim to understand whether this sort of memorization is necessary for accurate learning. We describe natural prediction problems in which every sufficiently accurate training algorithm must encode, in the prediction model, essentially all the information about a large subset of its training examples. This remains true even when the examples are high-dimensional and have entropy much higher than the sample size, and even when most of that information is ultimately irrelevant to the task at hand. Further, our results do not depend on the training algorithm or the class of models used for learning.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451131"}, {"primary_key": "2239842", "vector": [], "sparse_vector": [], "title": "Continuous LWE.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a continuous analogue of the Learning with Errors (LWE) problem, which we name CLWE. We give a polynomial-time quantum reduction from worst-case lattice problems to CLWE, showing that CLWE enjoys similar hardness guarantees to those of LWE. Alternatively, our result can also be seen as opening new avenues of (quantum) attacks on lattice problems. Our work resolves an open problem regarding the computational complexity of learning mixtures of Gaussians without separability assumptions (<PERSON><PERSON><PERSON><PERSON><PERSON> 2016, Moitra 2018). As an additional motivation, (a slight variant of) CLWE was considered in the context of robust machine learning (<PERSON><PERSON><PERSON><PERSON><PERSON> et al.~FOCS 2017), where hardness in the statistical query (SQ) model was shown; our work addresses the open question regarding its computational hardness (<PERSON><PERSON><PERSON> et al.~ICML 2019).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451000"}, {"primary_key": "2239843", "vector": [], "sparse_vector": [], "title": "Bridging the gap between tree and connectivity augmentation: unified and stronger approaches.", "authors": ["Federica Cecchetto", "<PERSON>", "<PERSON>"], "summary": "We consider the Connectivity Augmentation Problem (CAP), a classical problem in the area of Survivable Network Design. It is about increasing the edge-connectivity of a graph by one unit in the cheapest possible way. More precisely, given a k-edge-connected graph G=(V,E) and a set of extra edges, the task is to find a minimum cardinality subset of extra edges whose addition to G makes the graph (k+1)-edge-connected. If k is odd, the problem is known to reduce to the Tree Augmentation Problem (TAP)—i.e., G is a spanning tree—for which significant progress has been achieved recently, leading to approximation factors below 1.5 (the currently best factor is 1.458). However, advances on TAP did not carry over to CAP so far. Indeed, only very recently, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> (STOC 2020) managed to obtain the first approximation factor below 2 for CAP by presenting a 1.91-approximation algorithm based on a method that is disjoint from recent advances for TAP.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451086"}, {"primary_key": "2239844", "vector": [], "sparse_vector": [], "title": "Lower bounds for monotone arithmetic circuits via communication complexity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON> (1980) showed that general arithmetic circuits with negation can be exponentially more powerful than monotone ones. We give the first improvement to this classical result: we construct a family of polynomials Pn in n variables, each of its monomials has non-negative coefficient, such that Pn can be computed by a polynomial-size depth-three formula but every monotone circuit computing it has size 2Ω(n1/4/log(n)).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451069"}, {"primary_key": "2239845", "vector": [], "sparse_vector": [], "title": "Optimal mixing of Glauber dynamics: entropy factorization via high-dimensional expansion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We prove an optimal mixing time bound for the single-site update Markov chain known as the <PERSON><PERSON><PERSON> dynamics or Gibbs sampling in a variety of settings. Our work presents an improved version of the spectral independence approach of <PERSON><PERSON> et al. (2020) and shows O(nlogn) mixing time on any n-vertex graph of bounded degree when the maximum eigenvalue of an associated influence matrix is bounded. As an application of our results, for the hard-core model on independent sets weighted by a fugacity λ, we establish O(nlogn) mixing time for the <PERSON><PERSON><PERSON> dynamics on any n-vertex graph of constant maximum degree Δ when λ α Δ where α ≈ 1.763, and O(mlogn) mixing for generating random matchings of any graph with bounded degree and m edges.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451035"}, {"primary_key": "2239846", "vector": [], "sparse_vector": [], "title": "Algorithmic foundations for the diffraction limit.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For more than a century and a half it has been widely-believed (but was never rigorously shown) that the physics of diffraction imposes certain fundamental limits on the resolution of an optical system. However our understanding of what exactly can and cannot be resolved has never risen above heuristic arguments which, even worse, appear contradictory. In this work we remedy this gap by studying the diffraction limit as a statistical inverse problem and, based on connections to provable algorithms for learning mixture models, we rigorously prove upper and lower bounds on the statistical and algorithmic complexity needed to resolve closely spaced point sources. In particular we show that there is a phase transition where the sample complexity goes from polynomial to exponential. Surprisingly, we show that this does not occur at the Abbe limit, which has long been presumed to be the true diffraction limit.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451078"}, {"primary_key": "2239847", "vector": [], "sparse_vector": [], "title": "The limits of pan privacy and shuffle privacy for learning and estimation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "There has been a recent wave of interest in intermediate trust models for differential privacy that eliminate the need for a fully trusted central data collector, but overcome the limitations of local differential privacy. This interest has led to the introduction of the shuffle model (<PERSON><PERSON> et al., EUROCRYPT 2019; <PERSON><PERSON><PERSON><PERSON> et al., SODA 2019) and revisiting the pan-private model (<PERSON><PERSON> et al., ITCS 2010). The message of this line of work is that, for a variety of low-dimensional problems—such as counts, means, and histograms—these intermediate models offer nearly as much power as central differential privacy. However, there has been considerably less success using these models for high-dimensional learning and estimation problems.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3450995"}, {"primary_key": "2239848", "vector": [], "sparse_vector": [], "title": "Decremental all-pairs shortest paths in deterministic near-linear time.", "authors": ["<PERSON>"], "summary": "We study the decremental All-Pairs Shortest Paths (APSP) problem in undirected edge-weighted graphs. The input to the problem is an undirected n-vertex m-edge graph G with non-negative lengths on edges, that undergoes an online sequence of edge deletions. The goal is to support approximate shortest-paths queries: given a pair x,y of vertices of G, return a path P connecting x to y, whose length is within factor α of the length of the shortest x-y path, in time Õ(|E(P)|), where α is the approximation factor of the algorithm. APSP is one of the most basic and extensively studied dynamic graph problems.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451025"}, {"primary_key": "2239849", "vector": [], "sparse_vector": [], "title": "A quasipolynomial (2 + ε)-approximation for planar sparsest cut.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The (non-uniform) sparsest cut problem is the following graph-partitioning problem: given a \"supply\" graph, and demands on pairs of vertices, delete some subset of supply edges to minimize the ratio of the supply edges cut to the total demand of the pairs separated by this deletion. Despite much effort, there are only a handful of nontrivial classes of supply graphs for which constant-factor approximations are known.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451103"}, {"primary_key": "2239850", "vector": [], "sparse_vector": [], "title": "A new coreset framework for clustering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given a metric space, the (k,z)-clustering problem consists of finding k centers such that the sum of the of distances raised to the power z of every point to its closest center is minimized. This encapsulates the famous k-median (z=1) and k-means (z=2) clustering problems. Designing small-space sketches of the data that approximately preserves the cost of the solutions, also known as coresets, has been an important research direction over the last 15 years. In this paper, we present a new, simple coreset framework that simultaneously improves upon the best known bounds for a large variety of settings, ranging from Euclidean space, doubling metric, minor-free metric, and the general metric cases.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451022"}, {"primary_key": "2239851", "vector": [], "sparse_vector": [], "title": "Structure vs. randomness for bilinear maps.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We prove that the slice rank of a 3-tensor (a combinatorial notion introduced by <PERSON> in the context of the cap-set problem), the analytic rank (a Fourier-theoretic notion introduced by <PERSON><PERSON><PERSON> and <PERSON>), and the geometric rank (a recently introduced algebro-geometric notion) are all equivalent up to an absolute constant. As a corollary, we obtain strong trade-offs on the arithmetic complexity of a biased bililnear map, and on the separation between computing a bilinear map exactly and on average. Our result settles open questions of <PERSON><PERSON><PERSON> and <PERSON> [STOC 2010], and of <PERSON><PERSON> [Discrete Anal., 2019] for 3-tensors.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451007"}, {"primary_key": "2239852", "vector": [], "sparse_vector": [], "title": "Expander random walks: a Fourier-analytic approach.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work we ask the following basic question: assume the vertices of an expander graph are labelled by 0,1. What \"test\" functions f : { 0,1}t → {0,1} cannot distinguish t independent samples from those obtained by a random walk? The expander hitting property due to <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC 1987) is captured by the AND test function, whereas the fundamental expander Ch<PERSON>off bound due to <PERSON><PERSON> (SICOMP 1998), <PERSON><PERSON> (Computational Complexity 2008) is about test functions indicating whether the weight is close to the mean. In fact, it is known that all threshold functions are fooled by a random walk (<PERSON><PERSON><PERSON> and <PERSON>, Communications in Mathematical Physics 1986). Recently, it was shown that even the highly sensitive PARITY function is fooled by a random walk Ta-<PERSON>hma (STOC 2017).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451049"}, {"primary_key": "2239853", "vector": [], "sparse_vector": [], "title": "A full complexity dichotomy for immanant families.", "authors": ["<PERSON><PERSON>"], "summary": "Given an integer n ≥ 1 and an irreducible character χλ of Sn for some partition λ of n, the immanant immλ:ℂn× n→ℂ maps matrices A∈ℂn× n to immλ(A)=∑π∈ Snχλ(π)∏i=1nAi,π(i). Important special cases include the determinant and permanent, which are obtained from the sign and trivial character, respectively. It is known that immanants can be evaluated in polynomial time for characters that are “close” to the sign character: Given a partition λ of n with s parts, let b(λ):=n−s count the boxes to the right of the first column in the Young diagram of λ. For a family of partitions Λ, let b(Λ) := maxλ∈Λb(λ) and write Imm(Λ) for the problem of evaluating immλ(A) on input A and λ∈Λ. On the positive side, if b(Λ)<∞, then Imm(Λ) is known to be polynomial-time computable. This subsumes the case of the determinant. Conversely, if b(Λ)=∞, then previously known hardness results suggest that Imm(Λ) cannot be solved in polynomial time. However, these results only address certain restricted classes of families Λ. In this paper, we show that the assumption FPT≠ #W[1] from parameterized complexity rules out polynomial-time algorithms for Imm(Λ) for any computationally reasonable family of partitions Λ with b(Λ)=∞. We give an analogous result in algebraic complexity under the assumption VFPT≠ VW[1]. Furthermore, if b(λ) even grows polynomially in Λ, we show that Imm(Λ) is hard for #P and VNP. This concludes a series of partial results on the complexity of immanants obtained over the last 35 years.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451124"}, {"primary_key": "2239854", "vector": [], "sparse_vector": [], "title": "Learning Ising models from one or multiple samples.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There have been two main lines of work on estimating Ising models: (1) estimating them from multiple independent samples under minimal assumptions about the model's interaction matrix ; and (2) estimating them from one sample in restrictive settings. We propose a unified framework that smoothly interpolates between these two settings, enabling significantly richer estimation guarantees from one, a few, or many samples.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451074"}, {"primary_key": "2239855", "vector": [], "sparse_vector": [], "title": "Tight conditional lower bounds for approximating diameter in directed graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Among the most fundamental graph parameters is the Diameter, the largest distance between any pair of vertices in a graph. Computing the Diameter of a graph with m edges requires m2−o(1) time under the Strong Exponential Time Hypothesis (SETH), which can be prohibitive for very large graphs, so efficient approximation algorithms for Diameter are desired.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451130"}, {"primary_key": "2239856", "vector": [], "sparse_vector": [], "title": "Sample-optimal and efficient learning of tree Ising models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Qinxuan Pan"], "summary": "We show that n-variable tree-structured Ising models can be learned computationally-efficiently to within total variation distance є from an optimal O(n lnn/є2) samples, where O(·) hides an absolute constant which, importantly, does not depend on the model being learned—neither its tree nor the magnitude of its edge strengths, on which we place no assumptions. Our guarantees hold, in fact, for the celebrated <PERSON><PERSON> algorithm [1968], using the plug-in estimator for estimating mutual information. While this (or any other) algorithm may fail to identify the structure of the underlying model correctly from a finite sample, we show that it will still learn a tree-structured model that is є-close to the true one in total variation distance, a guarantee called \"proper learning.\"", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451006"}, {"primary_key": "2239857", "vector": [], "sparse_vector": [], "title": "The complexity of constrained min-max optimization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Manolis Zampetakis"], "summary": "Despite its important applications in Machine Learning, min-max optimization of objective functions that are nonconvex-nonconcave remains elusive. Not only are there no known first-order methods converging to even approximate local min-max equilibria (a.k.a. approximate saddle points), but the computational complexity of identifying them is also poorly understood. In this paper, we provide a characterization of the computational complexity as well as of the limitations of first-order methods in this problem. Specifically, we show that in linearly constrained min-max optimization problems with nonconvex-nonconcave objectives an approximate local min-max equilibrium of large enough approximation is guaranteed to exist, but computing such a point is PPAD-complete. The same is true of computing an approximate fixed point of the (Projected) Gradient Descent/Ascent update dynamics, which is computationally equivalent to computing approximate local min-max equilibria. An important byproduct of our proof is to establish an unconditional hardness result in the N<PERSON>irovsky-Yu<PERSON> 1983 oracle optimization model, where we are given oracle access to the values of some function f : P → [−1, 1] and its gradient ∇ f, where P ⊆ [0, 1]d is a known convex polytope. We show that any algorithm that uses such first-order oracle access to f and finds an ε-approximate local min-max equilibrium needs to make a number of oracle queries that is exponential in at least one of 1/ε, L, G, or d, where L and G are respectively the smoothness and Lipschitzness of f. This comes in sharp contrast to minimization problems, where finding approximate local minima in the same setting can be done with Projected Gradient Descent using O(L/ε) many queries. Our result is the first to show an exponential separation between these two fundamental optimization problems in the oracle model.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451125"}, {"primary_key": "2239858", "vector": [], "sparse_vector": [], "title": "Robust testing of low dimensional functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A natural problem in high-dimensional inference is to decide if a classifier f:ℝn → {−1,1} depends on a small number of linear directions of its input data. Call a function g: ℝn → {−1,1}, a linear k-junta if it is completely determined by some k-dimensional subspace of the input space. A recent work of the authors showed that linear k-juntas are testable. Thus there exists an algorithm to distinguish between: (1) f: ℝn → {−1,1} which is a linear k-junta with surface area s. (2) f is є-far from any linear k-junta with surface area (1+є)s. The query complexity of the algorithm is independent of the ambient dimension n.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451115"}, {"primary_key": "2239859", "vector": [], "sparse_vector": [], "title": "The communication complexity of multiparty set disjointness under product distributions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the multiparty number-in-hand set disjointness problem, we have k players, with private inputs X1,…,Xk ⊆ [n]. The players' goal is to check whether ∩ℓ=1k Xℓ = ∅. It is known that in the shared blackboard model of communication, set disjointness requires Ω(n logk + k) bits of communication, and in the coordinator model, it requires Ω(kn) bits. However, these two lower bounds require that the players' inputs can be highly correlated. We study the communication complexity of multiparty set disjointness under product distributions, and ask whether the problem becomes significantly easier, as it is known to become in the two-party case. Our main result is a nearly-tight bound of Θ̃(n1−1/k + k) for both the shared blackboard model and the coordinator model. This shows that in the shared blackboard model, as the number of players grows, having independent inputs helps less and less; but in the coordinator model, when k is very large, having independent inputs makes the problem much easier. Both our upper and our lower bounds use new ideas, as the original techniques developed for the two-party case do not scale to more than two players.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451106"}, {"primary_key": "2239860", "vector": [], "sparse_vector": [], "title": "Optimal testing of discrete distributions with high probability.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the problem of testing discrete distributions with a focus on the high probability regime. Specifically, given samples from one or more discrete distributions, a property P, and parameters 0< є, δ <1, we want to distinguish with probability at least 1−δ whether these distributions satisfy P or are є-far from P in total variation distance. Most prior work in distribution testing studied the constant confidence case (corresponding to δ = Ω(1)), and provided sample-optimal testers for a range of properties. While one can always boost the confidence probability of any such tester by black-box amplification, this generic boosting method typically leads to sub-optimal sample bounds.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3450997"}, {"primary_key": "2239861", "vector": [], "sparse_vector": [], "title": "Efficiently learning halfspaces with Tsybakov noise.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of PAC learning homogeneous halfspaces with Tsybakov noise. In the Tsybakov noise model, the label of every example is independently flipped with an adversarially controlled probability that can be arbitrarily close to 1/2 for a fraction of the examples. We give the first polynomial-time algorithm for this fundamental learning problem. Our algorithm learns the true halfspace within any desired accuracy and succeeds under a broad family of well-behaved distributions including log-concave distributions.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3450998"}, {"primary_key": "2239862", "vector": [], "sparse_vector": [], "title": "The communication complexity of payment computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let (f,P) be an incentive compatible mechanism where f is the social choice function and P is the payment function. In many important settings, f uniquely determines P (up to a constant) and therefore a common approach is to focus on the design of f and neglect the role of the payment function. <PERSON><PERSON><PERSON> and <PERSON><PERSON> [JET, 2009] question this approach by taking the lenses of communication complexity: can it be that the communication complexity of an incentive compatible mechanism that implements f (that is, computes both the output and the payments) is much larger than the communication complexity of computing the output? I.e., can it be that ccIC(f)>>cc(f)? <PERSON><PERSON><PERSON> and <PERSON><PERSON> show that for every f, ccIC(f)≤ exp(cc(f)). They also show that fully computing the incentive compatible mechanism is strictly harder than computing only the output: there exists a social choice function f such that ccIC(f)=cc(f)+1. In a follow-up work, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [EC'08] provide a social choice function f such that ccIC(f)=Θ(n· cc(f)), where n is the number of players. The question of whether the exponential upper bound of <PERSON><PERSON><PERSON> and <PERSON><PERSON> is tight remained wide open. In this paper we solve this question by explicitly providing a function f such that ccIC(f)= exp(cc(f)). In fact, we establish this via two very different proofs. In contrast, we show that if the players are risk-neutral and we can compromise on a randomized truthful-in-expectation implementation (and not on deterministic ex-post implementation) gives that ccTIE(f)=poly(n,cc(f)) for every function f, as long as the domain of f is single parameter or a convex multi-parameter domain. We also provide efficient algorithms for deterministic computation of payments in several important domains.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451083"}, {"primary_key": "2239863", "vector": [], "sparse_vector": [], "title": "A nearly-linear time algorithm for linear programs with small treewidth: a multiscale representation of robust central path.", "authors": ["<PERSON>", "<PERSON>", "G<PERSON><PERSON> Ye"], "summary": "Arising from structural graph theory, treewidth has become a focus of study in fixed-parameter tractable algorithms. Many NP-hard problems are known to be solvable in O(n · 2O(τ)) time, where τ is the treewidth of the input graph. Analogously, many problems in P should be solvable in O(n · τO(1)) time; however, due to the lack of appropriate tools, only a few such results are currently known. In our paper, we show this holds for linear programs: Given a linear program of the form minAx=b,ℓ ≤ x≤ u c⊤ x whose dual graph GA has treewidth τ, and a corresponding width-τ tree decomposition, we show how to solve it in time", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451056"}, {"primary_key": "2239864", "vector": [], "sparse_vector": [], "title": "Distributed weighted min-cut in nearly-optimal time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Danupon <PERSON>"], "summary": "Minimum-weight cut (min-cut) is a basic measure of a network's connectivity strength. While the min-cut can be computed efficiently in the sequential setting [Karger STOC'96], there was no efficient way for a distributed network to compute its own min-cut without limiting the input structure or dropping the output quality: In the standard CONGEST model, existing algorithms with nearly-optimal time (e.g. [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, DISC'13; Nanongkai, Su, DISC'14]) can guarantee a solution that is (1+?)-approximation at best while the exact O(n0.8D0.2 + n0.9)-time algorithm [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, SODA'20] works only on simple networks (no weights and no parallel edges). Throughout, n and D denote the network's number of vertices and hop-diameter, respectively. For the weighted case, the best bound was <PERSON>(n) [<PERSON><PERSON>, <PERSON>, <PERSON>, Sara<PERSON>rak, STOC'19]. In this paper, we provide an exact O(?n + D)-time algorithm for computing min-cut on weighted networks. Our result improves even the previous algorithm that works only on simple networks. Its time complexity matches the known lower bound up to polylogarithmic factors. At the heart of our algorithm are a routing trick and two structural lemmas regarding the structure of a minimum cut of a graph. These two structural lemmas considerably strengthen and generalize the framework of Mukhopadhyay-Nan<PERSON>kai [STOC'20] and can be of independent interest.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451020"}, {"primary_key": "2239865", "vector": [], "sparse_vector": [], "title": "Efficient two-sided markets with limited information.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A celebrated impossibility result by <PERSON><PERSON> and <PERSON> (1983) shows that any truthful mechanism for two-sided markets that maximizes social welfare must run a deficit, resulting in a necessity to relax welfare efficiency and the use of approximation mechanisms. Such mechanisms in general make extensive use of the Bayesian priors. In this work, we investigate a question of increasing theoretical and practical importance: how much prior information is required to design mechanisms with near-optimal approximations?", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451076"}, {"primary_key": "2239866", "vector": [], "sparse_vector": [], "title": "Outcome indistinguishability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "G<PERSON> <PERSON>na"], "summary": "Prediction algorithms assign numbers to individuals that are popularly understood as individual \"probabilities\"—what is the probability of 5-year survival after cancer diagnosis?—and which increasingly form the basis for life-altering decisions. Drawing on an understanding of computational indistinguishability developed in complexity theory and cryptography, we introduce Outcome Indistinguishability. Predictors that are Outcome Indistinguishable (OI) yield a generative model for outcomes that cannot be efficiently refuted on the basis of the real-life observations produced by .", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451064"}, {"primary_key": "2239867", "vector": [], "sparse_vector": [], "title": "Optimal error resilience of adaptive message exchange.", "authors": ["<PERSON><PERSON>", "Gillat Kol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the error resilience of the message exchange task: Two parties, each holding a private input, want to exchange their inputs. However, the channel connecting them is governed by an adversary that may corrupt a constant fraction of the transmissions. What is the maximum fraction of corruptions that still allows the parties to exchange their inputs?", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451077"}, {"primary_key": "2239868", "vector": [], "sparse_vector": [], "title": "The complexity of gradient descent: CLS = PPAD ∩ PLS.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study search problems that can be solved by performing Gradient Descent on a bounded convex polytopal domain and show that this class is equal to the intersection of two well-known classes: PPAD and PLS. As our main underlying technical contribution, we show that computing a Karush-Kuhn-Tucker (KKT) point of a continuously differentiable function over the domain [0,1]2 is PPAD ∩ PLS-complete. This is the first natural problem to be shown complete for this class. Our results also imply that the class CLS (Continuous Local Search) - which was defined by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> as a more \"natural\" counterpart to PPAD ∩ PLS and contains many interesting problems - is itself equal to PPAD ∩ PLS.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451052"}, {"primary_key": "2239869", "vector": [], "sparse_vector": [], "title": "Eliminating intermediate measurements in space-bounded Quantum computation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A foundational result in the theory of quantum computation, known as the \"principle of safe storage,\" shows that it is always possible to take a quantum circuit and produce an equivalent circuit that makes all measurements at the end of the computation. While this procedure is time efficient, meaning that it does not introduce a large overhead in the number of gates, it uses extra ancillary qubits, and so is not generally space efficient. It is quite natural to ask whether it is possible to eliminate intermediate measurements without increasing the number of ancillary qubits. We give an affirmative answer to this question by exhibiting a procedure to eliminate all intermediate measurements that is simultaneously space efficient and time efficient. In particular, this shows that the definition of a space-bounded quantum complexity class is robust to allowing or forbidding intermediate measurements. A key component of our approach, which may be of independent interest, involves showing that the well-conditioned versions of many standard linear-algebraic problems may be solved by a quantum computer in less space than seems possible by a classical computer.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451051"}, {"primary_key": "2239870", "vector": [], "sparse_vector": [], "title": "Sampling constraint satisfaction solutions in the local lemma regime.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a Markov chain based algorithm for sampling almost uniform solutions of constraint satisfaction problems (CSPs). Assuming a canonical setting for the Lovász local lemma, where each constraint is violated by a small number of forbidden local configurations, our sampling algorithm is accurate in a local lemma regime, and the running time is a fixed polynomial whose dependency on n is close to linear, where n is the number of variables. Our main approach is a new technique called state compression, which generalizes the \"mark/unmark\" paradigm of Moitra, and can give fast local-lemma-based sampling algorithms. As concrete applications of our technique, we give the current best almost-uniform samplers for hypergraph colorings and for CNF solutions.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451101"}, {"primary_key": "2239871", "vector": [], "sparse_vector": [], "title": "Revelation gap for pricing from samples.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Yingkai Li"], "summary": "This paper considers prior-independent mechanism design, in which a single mechanism is designed to achieve approximately optimal performance on every prior distribution from a given class. Most results in this literature focus on mechanisms with truthtelling equilibria, a.k.a., truthful mechanisms. <PERSON> and Hartline [FOCS 2018] introduce the revelation gap to quantify the loss of the restriction to truthful mechanisms. We solve a main open question left in Feng and Hartline [FOCS 2018]; namely, we identify a non-trivial revelation gap for revenue maximization.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451057"}, {"primary_key": "2239872", "vector": [], "sparse_vector": [], "title": "Clan embeddings into trees, and low treewidth graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In low distortion metric embeddings, the goal is to embed a host \"hard\" metric space into a \"simpler\" target space while approximately preserving pairwise distances. A highly desirable target space is that of a tree metric. Unfortunately, such embedding will result in a huge distortion. A celebrated bypass to this problem is stochastic embedding with logarithmic expected distortion. Another bypass is Ramsey-type embedding, where the distortion guarantee applies only to a subset of the points. However, both these solutions fail to provide an embedding into a single tree with a worst-case distortion guarantee on all pairs. In this paper, we propose a novel third bypass called clan embedding. Here each point x is mapped to a subset of points f(x), called a clan, with a special chief point χ(x)∈ f(x). The clan embedding has multiplicative distortion t if for every pair (x,y) some copy y′∈ f(y) in the clan of y is close to the chief of x: miny′∈ f(y)d(y′,χ(x))≤ t· d(x,y). Our first result is a clan embedding into a tree with multiplicative distortion O(logn/є) such that each point has 1+є copies (in expectation). In addition, we provide a \"spanning\" version of this theorem for graphs and use it to devise the first compact routing scheme with constant size routing tables.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451043"}, {"primary_key": "2239873", "vector": [], "sparse_vector": [], "title": "Approximating Nash social welfare under rado valuations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "László A. <PERSON>"], "summary": "We consider the problem of approximating maximum Nash social welfare (NSW) while allocating a set of indivisible items to n agents. The NSW is a popular objective that provides a balanced tradeoff between the often conflicting requirements of fairness and efficiency, defined as the weighted geometric mean of the agents' valuations. For the symmetric additive case of the problem, where agents have the same weight with additive valuations, the first constant-factor approximation algorithm was obtained in 2015. Subsequent work has obtained constant-factor approximation algorithms for the symmetric case under mild generalizations of additive, and O(n)-approximation algorithms for subadditive valuations and for the asymmetric case.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451031"}, {"primary_key": "2239874", "vector": [], "sparse_vector": [], "title": "Finding large induced sparse subgraphs in c&gt;t -free graphs in quasipolynomial time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For an integer t, a graph G is called C>t-free if G does not contain any induced cycle on more than t vertices. We prove the following statement: for every pair of integers d and t and a statement φ, there exists an algorithm that, given an n-vertex C>t-free graph G with weights on vertices, finds in time n(log3 n) a maximum-weight vertex subset S such that G[S] has degeneracy at most d and satisfies φ. The running time can be improved to n(log2 n) assuming G is Pt-free, that is, G does not contain an induced path on t vertices. This expands the recent results of the authors [FOCS 2020 and SOSA 2021] on the Maximum Weight Independent Set problem on Pt-free graphs in two directions: by encompassing the more general setting of C>t-free graphs, and by being applicable to a much wider variety of problems, such as Maximum Weight Induced Forest or Maximum Weight Induced Planar Graph.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451034"}, {"primary_key": "2239875", "vector": [], "sparse_vector": [], "title": "Fully dynamic approximation of LIS in polylogarithmic time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We revisit the problem of maintaining the longest increasing subsequence (LIS) of an array under (i) inserting an element, and (ii) deleting an element of an array. In a recent breakthrough, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [STOC 2020] designed an algorithm that maintains an O((1/є)O(1/є))-approximation of LIS under both operations with worst-case update time Õ(nє), for any constant є>0 (Õ hides factors polynomial in logn, where n is the length of the input). We exponentially improve on their result by designing an algorithm that maintains an (1+є) approximation of LIS under both operations with worst-case update time Õ(є−5). Instead of working with the grid packing technique introduced by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, we take a different approach building on a new tool that might be of independent interest: LIS sparsification.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451137"}, {"primary_key": "2239876", "vector": [], "sparse_vector": [], "title": "Indistinguishability obfuscation from circular security.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We show the existence of indistinguishability obfuscators (iO) for general circuits assuming subexponential security of: (a) the Learning with Errors (LWE) assumption (with subexponential modulus-to-noise ratio); (b) a circular security conjecture regarding the Gentry-Sahai-Waters' (GSW) encryption scheme and a Packed version of <PERSON><PERSON>'s encryption scheme. The circular security conjecture states that a notion of leakage-resilient security, that we prove is satisfied by GSW assuming LWE, is retained in the presence of an encrypted key-cycle involving GSW and Packed Regev.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451070"}, {"primary_key": "2239877", "vector": [], "sparse_vector": [], "title": "Hop-constrained oblivious routing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove the existence of an oblivious routing scheme that is poly(logn)-competitive in terms of (congestion + dilation), thus resolving a well-known question in oblivious routing.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451098"}, {"primary_key": "2239878", "vector": [], "sparse_vector": [], "title": "Sample-efficient proper PAC learning with approximate differential privacy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we prove that the sample complexity of properly learning a class of Littlestone dimension d with approximate differential privacy is Õ(d6), ignoring privacy and accuracy parameters. This result answers a question of <PERSON><PERSON> et al. (FOCS 2020) by improving upon their upper bound of 2O(d) on the sample complexity. Prior to our work, finiteness of the sample complexity for privately learning a class of finite Littlestone dimension was only known for improper private learners, and the fact that our learner is proper answers another question of <PERSON><PERSON> et al., which was also asked by <PERSON><PERSON><PERSON> et al. (NeurIPS 2020). Using machinery developed by <PERSON><PERSON><PERSON> et al., we then show that the sample complexity of sanitizing a binary hypothesis class is at most polynomial in its Littlestone dimension and dual Littlestone dimension. This implies that a class is sanitizable if and only if it has finite Littlestone dimension. An important ingredient of our proofs is a new property of binary hypothesis classes that we call irreducibility, which may be of independent interest.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451028"}, {"primary_key": "2239879", "vector": [], "sparse_vector": [], "title": "Hardness of learning DNFs using halfspaces.", "authors": ["Suprovat Ghoshal", "<PERSON><PERSON>"], "summary": "The problem of learning t-term DNF formulas (for t = O(1)) has been studied extensively in the PAC model since its introduction by <PERSON><PERSON> (STOC 1984). A t-term DNF can be efficiently learnt using a t-term DNF only if t = 1 i.e., when it is an AND, while even weakly learning a 2-term DNF using a constant term DNF was shown to be NP-hard by <PERSON><PERSON> and <PERSON> (FOCS 2008). On the other hand, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (FOCS 2009) showed the hardness of weakly learning a noisy AND using a halfspace – the latter being a generalization of an AND, while <PERSON><PERSON> and <PERSON> (STOC 2008) showed that an intersection of two halfspaces is hard to weakly learn using any function of constantly many halfspaces. The question of whether a 2-term DNF is efficiently learnable using 2 or constantly many halfspaces remained open.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451067"}, {"primary_key": "2239880", "vector": [], "sparse_vector": [], "title": "Efficient randomized DCAS.", "authors": ["<PERSON>", "Mehrdad Jafari Giv", "<PERSON>"], "summary": "Double Compare-And-Swap (DCAS) is a tremendously useful synchronization primitive, which is also notoriously difficult to implement efficiently from objects that are provided by hardware. We present a randomized implementation of DCAS with O(logn) expected amortized step complexity against the oblivious adversary, where n is the number of processes in the system. This is the only algorithm to-date that achieves sub-linear step complexity. We achieve that by first implementing two novel algorithms as building blocks. One is a mechanism that allows processes to repeatedly agree on a random value among multiple proposed ones, and the other one is a restricted bipartite version of DCAS.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451133"}, {"primary_key": "2239881", "vector": [], "sparse_vector": [], "title": "(Sub)Exponential advantage of adiabatic Quantum computation with no sign problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We demonstrate the possibility of (sub)exponential quantum speedup via a quantum algorithm that follows an adiabatic path of a gapped Hamiltonian with no sign problem. The Hamiltonian that exhibits this speed-up comes from the adjacency matrix of an undirected graph whose vertices are labeled by n-bit strings, and we can view the adiabatic evolution as an efficient O(poly(n))-time quantum algorithm for finding a specific \"EXIT\" vertex in the graph given the \"ENTRANCE\" vertex. On the other hand we show that if the graph is given via an adjacency-list oracle, there is no classical algorithm that finds the \"EXIT\" with probability greater than exp(−nδ) using at most exp(nδ) queries for δ= 1/5 − o(1). Our construction of the graph is somewhat similar to the \"welded-trees\" construction of <PERSON> et al., but uses additional ideas of <PERSON> for achieving a spectral gap and a short adiabatic path.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451060"}, {"primary_key": "2239882", "vector": [], "sparse_vector": [], "title": "Load balancing guardrails: keeping your heavy traffic on the road to low response times (invited paper).", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This talk is about scheduling and load balancing in a multi-server system, with the goal of minimizing mean response time in a general stochastic setting. We will specifically concentrate on the common case of a load balancing system, where a front-end load balancer (a.k.a. dispatcher) dispatches requests to multiple back-end servers, each with their own queue. Much is known about load balancing in the case where the scheduling at the servers is First-Come-First-Served (FCFS). However, to minimize mean response time, we need to use Shortest-Remaining-Processing-Time (SRPT) scheduling at the servers. Unfortunately, there is almost nothing known about optimal dispatching when SRPT scheduling is used at the servers. To make things worse, it turns out that the traditional dispatching policies that are used in practice with FCFS servers often have poor performance in systems with SRPT servers. In this talk, we devise a simple fix that can be applied to any dispatching policy. This fix, called \"guardrails\" ensures that the dispatching policy yields optimal mean response time under heavy traffic, when used in a system with SRPT servers. Any dispatching policy, when augmented with guardrails becomes heavy-traffic optimal. Our results also yield the first analytical bounds on mean response time for load balancing systems with SRPT scheduling at the servers. Load balancing and scheduling are highly studied both in the stochastic and the worst-case scheduling communities. One aim of this talk is to contrast some differences in the approaches of the two communities when tackling multi-server scheduling problems.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465359"}, {"primary_key": "2239883", "vector": [], "sparse_vector": [], "title": "Efficient list-decoding with constant alphabet and list sizes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an explicit and efficient algebraic construction of capacity-achieving list decodable codes with both constant alphabet and constant list sizes. More specifically, for any R ∈ (0,1) and є>0, we give an algebraic construction of an infinite family of error-correcting codes of rate R, over an alphabet of size (1/є)O(1/є2), that can be list decoded from a (1−R−є)-fraction of errors with list size at most exp(poly(1/є)). Moreover, the codes can be encoded in time poly(1/є, n), the output list is contained in a linear subspace of dimension at most poly(1/є), and a basis for this subspace can be found in time poly(1/є, n). Thus, both encoding and list decoding can be performed in fully polynomial-time poly(1/є, n), except for pruning the subspace and outputting the final list which takes time exp(poly(1/є)) · poly(n). In contrast, prior explicit and efficient constructions of capacity-achieving list decodable codes either required a much higher complexity in terms of 1/є (and were additionally much less structured), or had super-constant alphabet or list sizes.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451046"}, {"primary_key": "2239884", "vector": [], "sparse_vector": [], "title": "Capacity lower bounds via productization.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We give a sharp lower bound on the capacity of a real stable polynomial, depending only on the value of its gradient at x = 1. This result implies a sharp improvement to a similar inequality proved by <PERSON><PERSON><PERSON> in 2000, which was crucial to the analysis of their permanent approximation algorithm. Such inequalities have played an important role in the recent work on operator scaling and its generalizations and applications, and in fact we use our bound to construct a new scaling algorithm for real stable polynomials. Our bound is also quite similar to one used very recently by <PERSON><PERSON><PERSON> to give an improved approximation factor for metric TSP.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451105"}, {"primary_key": "2239885", "vector": [], "sparse_vector": [], "title": "Tree embeddings for hop-constrained network design.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network design problems aim to compute low-cost structures such as routes, trees and subgraphs. Often, it is natural and desirable to require that these structures have small hop length or hop diameter. Unfortunately, optimization problems with hop constraints are much harder and less well understood than their hop-unconstrained counterparts. A significant algorithmic barrier in this setting is the fact that hop-constrained distances in graphs are very far from being a metric.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451053"}, {"primary_key": "2239886", "vector": [], "sparse_vector": [], "title": "Universally-optimal distributed algorithms for known topologies.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many distributed optimization algorithms achieve existentially-optimal running times, meaning that there exists some pathological worst-case topology on which no algorithm can do better. Still, most networks of interest allow for exponentially faster algorithms. This motivates two questions:", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451081"}, {"primary_key": "2239887", "vector": [], "sparse_vector": [], "title": "Efficient randomized distributed coloring in CONGEST.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Distributed vertex coloring is one of the classic problems and probably also the most widely studied problems in the area of distributed graph algorithms. We present a new randomized distributed vertex coloring algorithm for the standard CONGEST model, where the network is modeled as an n-node graph G, and where the nodes of G operate in synchronous communication rounds in which they can exchange O(logn)-bit messages over all the edges of G. For graphs with maximum degree Δ, we show that the (Δ+1)-list coloring problem (and therefore also the standard (Δ+1)-coloring problem) can be solved in O(log5logn) rounds. Previously such a result was only known for the significantly more powerful LOCAL model, where in each round, neighboring nodes can exchange messages of arbitrary size. The best previous (Δ+1)-coloring algorithm in the CONGEST model had a running time of O(logΔ + log6logn) rounds. As a function of n alone, the best previous algorithm therefore had a round complexity of O(logn), which is a bound that can also be achieved by a na'ive folklore algorithm. For large maximum degree Δ, our algorithm hence is an exponential improvement over the previous state of the art.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451089"}, {"primary_key": "2239888", "vector": [], "sparse_vector": [], "title": "Fiber bundle codes: breaking the n1/2 polylog(n) barrier for Quantum LDPC codes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Ryan <PERSON>&<PERSON>;Donnell"], "summary": "We present a quantum LDPC code family that has distance Ω(N3/5/polylog(N)) and Θ(N3/5) logical qubits, where N is the code length. This is the first quantum LDPC code construction that achieves distance greater than N1/2 polylog(N). The construction is based on generalizing the homological product of codes to a fiber bundle.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451005"}, {"primary_key": "2239889", "vector": [], "sparse_vector": [], "title": "On codes decoding a constant fraction of errors on the BSC.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We strengthen the results from a recent work by the second author, achieving bounds on the weight distribution of binary linear codes that are successful under block-MAP (as well as bit-MAP) decoding on the BEC. We conclude that a linear code that is successful on the BEC can also decode over a range of binary memoryless symmetric (BMS) channels. In particular, applying the result of <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> from STOC 2016, we prove that a <PERSON> code of positive rate R decodes errors on the p with high probability if p < 1/2 − √2−R(1−2−R).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451015"}, {"primary_key": "2239890", "vector": [], "sparse_vector": [], "title": "Average-case hardness of NP from exponential worst-case hardness assumptions.", "authors": ["<PERSON><PERSON>"], "summary": "A long-standing and central open question in the theory of average-case complexity is to base average-case hardness of NP on worst-case hardness of NP. A frontier question along this line is to prove that PH is hard on average if UP requires (sub-)exponential worst-case complexity. The difficulty of resolving this question has been discussed from various perspectives based on technical barrier results, such as the limits of black-box reductions and the non-existence of worst-case hardness amplification procedures in PH.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451065"}, {"primary_key": "2239891", "vector": [], "sparse_vector": [], "title": "Fiat-Shamir via list-recoverable codes (or: parallel repetition of GMW is not zero-knowledge).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In a seminal work, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (CRYPTO '86) demonstrated the wide applicability of zero-knowledge proofs by constructing such a proof system for the NP-complete problem of graph 3-coloring. A long-standing open question has been whether parallel repetition of their protocol preserves zero knowledge. In this work, we answer this question in the negative, assuming a standard cryptographic assumption (i.e., the hardness of learning with errors (LWE)).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451116"}, {"primary_key": "2239892", "vector": [], "sparse_vector": [], "title": "Online stochastic matching, poisson arrivals, and the natural linear program.", "authors": ["<PERSON><PERSON><PERSON>", "Xinkai Shu"], "summary": "We study the online stochastic matching problem. Consider a bipartite graph with offline vertices on one side, and with i.i.d.online vertices on the other side. The offline vertices and the distribution of online vertices are known to the algorithm beforehand. The realization of the online vertices, however, is revealed one at a time, upon which the algorithm immediately decides how to match it. For maximizing the cardinality of the matching, we give a 0.711-competitive online algorithm, which improves the best previous ratio of 0.706. When the offline vertices are weighted, we introduce a 0.7009-competitive online algorithm for maximizing the total weight of the matched offline vertices, which improves the best previous ratio of 0.662.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451079"}, {"primary_key": "2239893", "vector": [], "sparse_vector": [], "title": "Neural tangent kernel: convergence and generalization in neural networks (invited paper).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Neural Tangent Kernel is a new way to understand the gradient descent in deep neural networks, connecting them with kernel methods. In this talk, I'll introduce this formalism and give a number of results on the Neural Tangent Kernel and explain how they give us insight into the dynamics of neural networks during training and into their generalization features.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465355"}, {"primary_key": "2239894", "vector": [], "sparse_vector": [], "title": "Indistinguishability obfuscation from well-founded assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Indistinguishability obfuscation, introduced by [<PERSON><PERSON> et. al. Crypto 2001], aims to compile programs into unintelligible ones while preserving functionality. It is a fascinating and powerful object that has been shown to enable a host of new cryptographic goals and beyond. However, constructions of indistinguishability obfuscation have remained elusive, with all other proposals relying on heuristics or newly conjectured hardness assumptions. In this work, we show how to construct indistinguishability obfuscation from subexponential hardness of four well-founded assumptions. We prove:", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451093"}, {"primary_key": "2239895", "vector": [], "sparse_vector": [], "title": "Perfectly sampling k ≥ (8/3 + o(1))Δ-colorings in graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a randomized algorithm which takes as input an undirected graph G on n vertices with maximum degree Δ, and a number of colors k ≥ (8/3 + oΔ(1))Δ, and returns – in expected time Õ(nΔ2logk) – a proper k-coloring of G distributed perfectly uniformly on the set of all proper k-colorings of G. Notably, our sampler breaks the barrier at k = 3Δ encountered in recent work of <PERSON><PERSON><PERSON> and Chakraborty [STOC 2020]. We also discuss how our methods may be modified to relax the restriction on k to k ≥ (8/3 − є0)Δ for an absolute constant є0 > 0.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451012"}, {"primary_key": "2239896", "vector": [], "sparse_vector": [], "title": "Vertex deletion parameterized by elimination distance and even less.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the parameterized complexity of various classic vertex-deletion problems such as Odd cycle transversal, Vertex planarization, and Chordal vertex deletion under hybrid parameterizations. Existing FPT algorithms for these problems either focus on the parameterization by solution size, detecting solutions of size k in time f(k) · nO(1), or width parameterizations, finding arbitrarily large optimal solutions in time f(w) · nO(1) for some width measure w like treewidth. We unify these lines of research by presenting FPT algorithms for parameterizations that can simultaneously be arbitrarily much smaller than the solution size and the treewidth.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451068"}, {"primary_key": "2239897", "vector": [], "sparse_vector": [], "title": "SNARGs for bounded depth computations and PPAD hardness from sub-exponential LWE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We construct a succinct non-interactive publicly-verifiable delegation scheme for any log-space uniform circuit under the sub-exponential Learning With Errors (LWE) assumption. For a circuit C:{0,1}N→{0,1} of size S and depth D, the prover runs in time poly(S), the communication complexity is D · polylog(S), and the verifier runs in time (D+N) ·polylog(S). To obtain this result, we introduce a new cryptographic primitive: a lossy correlation-intractable hash function family. We use this primitive to soundly instantiate the <PERSON>-<PERSON><PERSON><PERSON> transform for a large class of interactive proofs, including the interactive sum-check protocol and the GKR protocol, assuming the sub-exponential hardness of LWE.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451055"}, {"primary_key": "2239898", "vector": [], "sparse_vector": [], "title": "Near-linear time decoding of Ta-Shma&apos;s codes via splittable regularity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The <PERSON> bound non-constructively establishes the existence of binary codes of distance 1/2−є/2 and rate Ω(є2). In a breakthrough result, <PERSON><PERSON><PERSON><PERSON><PERSON> [STOC 2017] constructed the first explicit family of nearly optimal binary codes with distance 1/2−є/2 and rate Ω(є2+α), where α → 0 as є → 0. Moreover, the codes in <PERSON><PERSON><PERSON><PERSON><PERSON>'s construction are є-balanced, where the distance between distinct codewords is not only bounded from below by 1/2−є/2, but also from above by 1/2+є/2.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451126"}, {"primary_key": "2239899", "vector": [], "sparse_vector": [], "title": "Reducing isotropy and volume to KLS: an o*(n3ψ2) volume algorithm.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Santosh S<PERSON>"], "summary": "We show that the volume of a convex body in Rn in the general membership oracle model can be computed to within relative error ε using O(n3ψ2/ε2) oracle queries, where ψ is the KLS constant. With the current bound of ψ=O(no(1)), this gives an O(n3+o(1)/ε2) algorithm, the first improvement on the Lovász-Vempala O(n4/ε2) algorithm from 2003. The main new ingredient is an O(n3ψ2) algorithm for isotropic transformation, following which we can apply the O(n3/ε2) volume algorithm of Cousins and Vempala for well-rounded convex bodies. A positive resolution of the KLS conjecture would imply an O(n3/є2) volume algorithm. We also give an efficient implementation of the new algorithm for convex polytopes defined by m inequalities in Rn: polytope volume can be estimated in time O(mnc/ε2) where c<3.2 depends on the current matrix multiplication exponent and improves on the previous best bound.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451018"}, {"primary_key": "2239900", "vector": [], "sparse_vector": [], "title": "A faster algorithm for solving general LPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The fastest known LP solver for general (dense) linear programs is due to [<PERSON>, <PERSON> and <PERSON>'19] and runs in O*(nω +n2.5−α/2 + n2+1/6) time. A number of follow-up works [<PERSON>, <PERSON> and <PERSON>'19, <PERSON>'20, <PERSON> and <PERSON>'20] obtain the same complexity through different techniques, but none of them can go below n2+1/6, even if ω=2. This leaves a polynomial gap between the cost of solving linear systems (nω) and the cost of solving linear programs, and as such, improving the n2+1/6 term is crucial toward establishing an equivalence between these two fundamental problems. In this paper, we reduce the running time to O*(nω +n2.5−α/2 + n2+1/18) where ω and α are the fast matrix multiplication exponent and its dual. Hence, under the common belief that ω ≈ 2 and α ≈ 1, our LP solver runs in O*(n2.055) time instead of O*(n2.16).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451058"}, {"primary_key": "2239901", "vector": [], "sparse_vector": [], "title": "Towards tight bounds for spectral sparsification of hypergraphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cut and spectral sparsification of graphs have numerous applications, including e.g. speeding up algorithms for cuts and Laplacian solvers. These powerful notions have recently been extended to hypergraphs, which are much richer and may offer new applications. However, the current bounds on the size of hypergraph sparsifiers are not as tight as the corresponding bounds for graphs.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451061"}, {"primary_key": "2239902", "vector": [], "sparse_vector": [], "title": "A (slightly) improved approximation algorithm for metric TSP.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "For some > 10−36 we give a randomized 3/2− approximation algorithm for metric TSP.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451009"}, {"primary_key": "2239903", "vector": [], "sparse_vector": [], "title": "New cosystolic expanders from tensors imply explicit Quantum LDPC codes with Ω(√n logk n) distance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "In this work we introduce a new notion of expansion in higher dimensions that is stronger than the well studied cosystolic expansion notion, and is termed Collective-cosystolic expansion.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451029"}, {"primary_key": "2239904", "vector": [], "sparse_vector": [], "title": "Local concentration inequalities and Tomas<PERSON>wski&apos;s conjecture.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We prove <PERSON><PERSON><PERSON>'s conjecture (1986): Let f:{−1,1}n → ℝ be of the form f(x)= ∑i=1n ai xi. Then Pr[|f(x)| ≤ √Var[f]] ≥ 1/2. Our main novel tools are local concentration inequalities and an improved <PERSON><PERSON><PERSON><PERSON><PERSON> inequality for first-degree functions on the discrete cube. These tools are of independent interest, and may be useful in the study of linear threshold functions and of low degree Boolean functions.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451011"}, {"primary_key": "2239905", "vector": [], "sparse_vector": [], "title": "An improved derandomization of the switching lemma.", "authors": ["<PERSON><PERSON>"], "summary": "We prove a new derandomization of <PERSON><PERSON><PERSON>'s switching lemma, showing how to efficiently generate restrictions satisfying the switching lemma for DNF or CNF formulas of size m using only O(logm) random bits. Derandomizations of the switching lemma have been useful in many works as a key building-block for constructing objects which are in some way provably-pseudorandom with respect to AC0-circuits.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451054"}, {"primary_key": "2239906", "vector": [], "sparse_vector": [], "title": "The ghost in the radiation: robust encodings of the black hole interior (invited paper).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We reconsider the black hole firewall puzzle, emphasizing that quantum error-correction, computational complexity, and pseudorandomness are crucial concepts for understanding the black hole interior. We assume that the Hawking radiation emitted by an old black hole is pseudorandom, meaning that it cannot be distinguished from a perfectly thermal state by any efficient quantum computation acting on the radiation alone. We then infer the existence of a subspace of the radiation system which we interpret as an encoding of the black hole interior. This encoded interior is entangled with the late outgoing Hawking quanta emitted by the old black hole, and is inaccessible to computationally bounded observers who are outside the black hole. Specifically, efficient operations acting on the radiation, those with quantum computational complexity polynomial in the entropy of the remaining black hole, commute with a complete set of logical operators acting on the encoded interior, up to corrections which are exponentially small in the entropy. Thus, under our pseudorandomness assumption, the black hole interior is well protected from exterior observers as long as the remaining black hole is macroscopic. On the other hand, if the radiation is not pseudorandom, an exterior observer may be able to create a firewall by applying a polynomial-time quantum computation to the radiation.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465357"}, {"primary_key": "2239907", "vector": [], "sparse_vector": [], "title": "Simplicity creates inequity: implications for fairness, stereotypes, and interpretability (invited paper).", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Algorithms are increasingly used to aid, or in some cases supplant, human decision-making, particularly for decisions that hinge on predictions. As a result, two additional features in addition to prediction quality have generated interest: (i) to facilitate human interaction and understanding with these algorithms, we desire prediction functions that are in some fashion simple or interpretable; and (ii) because they influence consequential decisions, we also want them to produce equitable allocations. We develop a formal model to explore the relationship between the demands of simplicity and equity. Although the two concepts appear to be motivated by qualitatively distinct goals, we show a fundamental inconsistency between them. Specifically, we formalize a general framework for producing simple prediction functions, and in this framework we establish two basic results. First, every simple prediction function is strictly improvable: there exists a more complex prediction function that is both strictly more efficient and also strictly more equitable. Put another way, using a simple prediction function both reduces utility for disadvantaged groups and reduces overall welfare relative to other options. Second, we show that simple prediction functions necessarily create incentives to use information about individuals' membership in a disadvantaged group --- incentives that weren't present before simplification, and that work against these individuals. Thus, simplicity transforms disadvantage into bias against the disadvantaged group. Our results are not only about algorithms but about any process that produces simple models, and as such they connect to the psychology of stereotypes and to an earlier economics literature on statistical discrimination.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465356"}, {"primary_key": "2239908", "vector": [], "sparse_vector": [], "title": "Log-rank and lifting for AND-functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>qiang Yuan"], "summary": "Let f: {0, 1}n → {0, 1} be a boolean function, and let f∧(x, y) = f(x ∧ y) denote the AND-function of f, where x ∧ y denotes bit-wise AND. We study the deterministic communication complexity of f∧ and show that, up to a logn factor, it is bounded by a polynomial in the logarithm of the real rank of the communication matrix of f∧. This comes within a logn factor of establishing the log-rank conjecture for AND-functions with no assumptions on f. Our result stands in contrast with previous results on special cases of the log-rank conjecture, which needed significant restrictions on f such as monotonicity or low F2-degree. Our techniques can also be used to prove (within a logn factor) a lifting theorem for AND-functions, stating that the deterministic communication complexity of f∧ is polynomially related to the AND-decision tree complexity of f.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3450999"}, {"primary_key": "2239909", "vector": [], "sparse_vector": [], "title": "Improved dynamic algorithms for longest increasing subsequence.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study dynamic algorithms for the longest increasing subsequence (LIS) problem. A dynamic LIS algorithm maintains a sequence subject to operations of the following form arriving one by one: insert an element, delete an element, or substitute an element for another. After each update, the algorithm must report the length of the longest increasing subsequence of the current sequence.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451026"}, {"primary_key": "2239910", "vector": [], "sparse_vector": [], "title": "Contextual search in the presence of irrational agents.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study contextual search, a generalization of binary search in higher dimensions, which captures settings such as feature-based dynamic pricing. Standard game-theoretic formulations of this problem assume that agents act in accordance with a specific behavioral model. In practice, some agents may not subscribe to the dominant behavioral model or may act in ways that are seemingly arbitrarily irrational. Existing algorithms heavily depend on the behavioral model being (approximately) accurate for all agents and have poor performance even with a few arbitrarily irrational agents.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451120"}, {"primary_key": "2239911", "vector": [], "sparse_vector": [], "title": "How asymmetry helps buffer management: achieving optimal tail size in cup games.", "authors": ["<PERSON>"], "summary": "The cup game on n cups is a multi-step game with two players, a filler and an emptier. At each step, the filler distributes 1 unit of water among the cups, and then the emptier selects a single cup to remove (up to) 1 unit of water from.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451033"}, {"primary_key": "2239912", "vector": [], "sparse_vector": [], "title": "Sampling matrices from <PERSON><PERSON><PERSON><PERSON>-<PERSON> densities with applications to Quantum inference and differential privacy.", "authors": ["<PERSON>", "<PERSON>", "Nisheeth K. <PERSON>"], "summary": "Given two Hermitian matrices Y and Λ, the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (HCIZ) distribution is given by the density eTr(U Λ U*Y) with respect to the Haar measure on the unitary group. Random unitary matrices distributed according to the HCIZ distribution are important in various settings in physics and random matrix theory, but the problem of sampling efficiently from this distribution has remained open. We present two algorithms to sample matrices from distributions that are close to the HCIZ distribution. The first produces samples that are ξ-close in total variation distance, and the number of arithmetic operations required depends on poly(log1/ξ). The second produces samples that are ξ-close in infinity divergence, but with a poly(1/ξ) dependence. Our results have the following applications: 1) an efficient algorithm to sample from complex versions of matrix Langevin distributions studied in statistics, 2) an efficient algorithm to sample from continuous maximum entropy distributions over unitary orbits, which in turn implies an efficient algorithm to sample a pure quantum state from the entropy-maximizing ensemble representing a given density matrix, and 3) an efficient algorithm for differentially private rank-k approximation that comes with improved utility bounds for k>1.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451094"}, {"primary_key": "2239913", "vector": [], "sparse_vector": [], "title": "Climbing algorithms (invited talk).", "authors": ["<PERSON><PERSON>"], "summary": "NP (search) problems allow easy correctness tests for solutions. Climbing algorithms allow also easy assessment of how close to yielding the correct answer is the configuration at any stage of their run. This offers a great flexibility, as how sensible is any deviation from the standard procedures can be instantly assessed.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3457137"}, {"primary_key": "2239914", "vector": [], "sparse_vector": [], "title": "Deterministic mincut in almost-linear time.", "authors": ["<PERSON>"], "summary": "We present a deterministic (global) mincut algorithm for weighted, undirected graphs that runs in m1+o(1) time, answering an open question of <PERSON><PERSON> from the 1990s. To obtain our result, we de-randomize the construction of the skeleton graph in <PERSON><PERSON>'s near-linear time mincut algorithm, which is its only randomized component. In particular, we partially de-randomize the well-known <PERSON><PERSON><PERSON>-<PERSON> graph sparsification technique by random sampling, which we accomplish by the method of pessimistic estimators. Our main technical component is designing an efficient pessimistic estimator to capture the cuts of a graph, which involves harnessing the expander decomposition framework introduced in recent work by <PERSON><PERSON><PERSON> et al. (SODA 2021). As a side-effect, we obtain a structural representation of all approximate mincuts in a graph, which may have future applications.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451114"}, {"primary_key": "2239915", "vector": [], "sparse_vector": [], "title": "Settling SETH vs. approximate sparse directed unweighted diameter (up to (NU)NSETH).", "authors": ["<PERSON>"], "summary": "We prove several tight results on the fine-grained complexity of approximating the diameter of a graph. First, we prove that, for any ε>0, assuming the Strong Exponential Time Hypothesis (SETH), there are no near-linear time 2−ε-approximation algorithms for the Diameter of a sparse directed graph, even in unweighted graphs. This result shows that a simple near-linear time 2-approximation algorithm for Diameter is optimal under SETH, answering a question from a survey of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (SIGACT ’19) for the case of directed graphs.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451045"}, {"primary_key": "2239916", "vector": [], "sparse_vector": [], "title": "Vertex connectivity in poly-logarithmic max-flows.", "authors": ["<PERSON>", "Danupon <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>", "Sorrachai <PERSON>i"], "summary": "The vertex connectivity of an m-edge n-vertex undirected graph is the smallest number of vertices whose removal disconnects the graph, or leaves only a singleton vertex. In this paper, we give a reduction from the vertex connectivity problem to a set of maxflow instances. Using this reduction, we can solve vertex connectivity in (mα) time for any α ≥ 1, if there is a mα-time maxflow algorithm. Using the current best maxflow algorithm that runs in m4/3+o(1) time (<PERSON><PERSON><PERSON>, <PERSON> and Sid<PERSON>, FOCS 2020), this yields a m4/3+o(1)-time vertex connectivity algorithm. This is the first improvement in the running time of the vertex connectivity problem in over 20 years, the previous best being an Õ(mn)-time algorithm due to <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (FOCS 1996). Indeed, no algorithm with an o(mn) running time was known before our work, even if we assume an (m)-time maxflow algorithm.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451088"}, {"primary_key": "2239917", "vector": [], "sparse_vector": [], "title": "Approximate Gomory-Hu tree is faster than n - 1 max-flows.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Gomory-Hu tree or cut tree (<PERSON><PERSON><PERSON> and <PERSON>, 1961) is a classic data structure for reporting s−t mincuts (and by duality, the values of s−t maxflows) for all pairs of vertices s and t in an undirected graph. <PERSON><PERSON><PERSON> and <PERSON> showed that it can be computed using n−1 exact maxflow computations. Surprisingly, this remains the best algorithm for Gomory-Hu trees more than 50 years later, even for approximate mincuts. In this paper, we break this longstanding barrier and give an algorithm for computing a (1+є)-approximate Gomory-Hu tree using log(n) maxflow computations. Specifically, we obtain the runtime bounds we describe below.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451112"}, {"primary_key": "2239918", "vector": [], "sparse_vector": [], "title": "Constant approximating k-clique is w[1]-hard.", "authors": ["Bing<PERSON> Lin"], "summary": "For every graph G, let ω(G) be the largest size of complete subgraph in G. This paper presents a simple algorithm which, on input a graph G, a positive integer k and a small constant є>0, outputs a graph G′ and an integer k′ in 2Θ(k5)· |G|O(1)-time such that (1) k′≤ 2Θ(k5), (2) if ω(G)≥ k, then ω(G′)≥ k′, (3) if ω(G)<k, then ω(G′)< (1−є)k′. This implies that no f(k)· |G|O(1)-time algorithm can distinguish between the cases ω(G)≥ k and ω(G)<k/c for any constant c≥ 1 and computable function f, unless FPT= W[1].", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451016"}, {"primary_key": "2239919", "vector": [], "sparse_vector": [], "title": "Settling the robust learnability of mixtures of Gaussians.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This work represents a natural coalescence of two important lines of work – learning mixtures of Gaussians and algorithmic robust statistics. In particular we give the first provably robust algorithm for learning mixtures of any constant number of Gaussians. We require only mild assumptions on the mixing weights (bounded fractionality) and that the total variation distance between components is bounded away from zero. At the heart of our algorithm is a new method for proving dimension-independent polynomial identifiability through applying a carefully chosen sequence of differential operations to certain generating functions that not only encode the parameters we would like to learn but also the system of polynomial equations we would like to solve. We show how the symbolic identities we derive can be directly used to analyze a natural sum-of-squares relaxation.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451084"}, {"primary_key": "2239920", "vector": [], "sparse_vector": [], "title": "Cryptography from sublinear-time average-case hardness of time-bounded Kolmogorov complexity.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Let MKtP[s] be the set of strings x such that Kt(x) ≤ s(|x|), where Kt(x) denotes the t-bounded Kolmogorov complexity of the truthtable described by x. Our main theorem shows that for an appropriate notion of mild average-case hardness, for every ε>0, polynomial t(n) ≥ (1+ε)n, and every \"nice\" class F of super-polynomial functions, the following are equivalent: (i) the existence of some function T ∈ F such that T-hard one-way functions (OWF) exists (with non-uniform security); (ii) the existence of some function T ∈ F such that MKtP[T−1] is mildly average-case hard with respect to sublinear-time non-uniform algorithms (with running-time nδ for some 0<δ<1). For instance, existence of subexponentially-hard (resp. quasi-poly-nomially-hard) OWFs is equivalent to mild average-case hardness of MKtP[poly logn] (resp. MKtP[2O(√logn))]) w.r.t. sublinear-time non-uniform algorithms. We additionally note that if we want to deduce T-hard OWFs where security holds w.r.t. uniform T-time probabilistic attackers (i.e., uniformly-secure OWFs), it suffices to assume sublinear time hardness of MKtP w.r.t. uniform probabilistic sublinear-time attackers. We complement this result by proving lower bounds that come surprisingly close to what is required to unconditionally deduce the existence of (uniformly-secure) OWFs: MKtP[polylogn] is worst-case hard w.r.t. uniform probabilistic sublinear-time algorithms, and MKtP[n−logn] is mildly average-case hard for all O(t(n)/n3)-time deterministic algorithms.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451121"}, {"primary_key": "2239921", "vector": [], "sparse_vector": [], "title": "Pseudodeterministic algorithms and the structure of probabilistic time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We connect the study of pseudodeterministic algorithms to two major open problems about the structural complexity of BPTIME: proving hierarchy theorems and showing the existence of complete problems. Our main contributions can be summarised as follows.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451085"}, {"primary_key": "2239922", "vector": [], "sparse_vector": [], "title": "Greedy adversarial equilibrium: an efficient alternative to nonconvex-nonconcave min-max optimization.", "authors": ["<PERSON><PERSON>", "Nisheeth K. <PERSON>"], "summary": "Min-max optimization of an objective function f: ℝd × ℝd → ℝ is an important model for robustness in an adversarial setting, with applications to many areas including optimization, economics, and deep learning. In many applications f may be nonconvex-nonconcave, and finding a global min-max point may be computationally intractable. There is a long line of work that seeks computationally tractable algorithms for alternatives to the min-max optimization model. However, many of the alternative models have solution points which are only guaranteed to exist under strong assumptions on f, such as convexity, monotonicity, or special properties of the starting point. We propose an optimization model, the ε-greedy adversarial equilibrium, and show that it can serve as a computationally tractable alternative to the min-max optimization model. Roughly, we say that a point (x⋆, y⋆) is an ε-greedy adversarial equilibrium if y⋆ is an ε-approximate local maximum for f(x⋆,·), and x⋆ is an ε-approximate local minimum for a \"greedy approximation\" to the function maxz f(x, z) which can be efficiently estimated using second-order optimization algorithms. We prove the existence of such a point for any smooth function which is bounded and has Lipschitz Hessian. To prove existence, we introduce an algorithm that converges from any starting point to an ε-greedy adversarial equilibrium in a number of evaluations of the function f, the max-player's gradient ∇y f(x,y), and its Hessian ∇y2 f(x,y), that is polynomial in the dimension d, 1/ε, and the bounds on f and its <PERSON><PERSON><PERSON><PERSON> constant.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451097"}, {"primary_key": "2239923", "vector": [], "sparse_vector": [], "title": "Support of closed walks and second eigenvalue multiplicity of graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We show that the multiplicity of the second normalized adjacency matrix eigenvalue of any connected graph of maximum degree ?is bounded by O(n ?7/5/log1/5-o(1)n) for any ?, and improve this to O(nlog1/2d/log1/4-o(1)n) for simple d-regular graphs when d? log1/4n. In fact, the same bounds hold for the number of eigenvalues in any interval of width ?2/log?1-o(1)n containing the second eigenvalue ?2. The main ingredient in the proof is a polynomial (in k) lower bound on the typical support of a closed random walk of length 2k in any connected graph, which in turn relies on new lower bounds for the entries of the Perron eigenvector of submatrices of the normalized adjacency matrix.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451129"}, {"primary_key": "2239924", "vector": [], "sparse_vector": [], "title": "Improving <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>;s algorithm for subset sum via orthogonal vectors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an O∗(20.5n) time and O∗(20.249999n) space randomized algorithm for solving worst-case Subset Sum instances with n integers. This is the first improvement over the long-standing O∗(2n/2) time and O∗(2n/4) space algorithm due to <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (FOCS 1979).", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451024"}, {"primary_key": "2239925", "vector": [], "sparse_vector": [], "title": "Dynamic planar point location in optimal time.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper we describe a fully-dynamic data structure that supports point location queries in a connected planar subdivision with n edges. Our data structure uses O(n) space, answers queries in O(logn) time, and supports updates in O(logn) time. Our solution is based on a data structure for vertical ray shooting queries that supports queries and updates in O(logn) time.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451100"}, {"primary_key": "2239926", "vector": [], "sparse_vector": [], "title": "Combinatorial Bernoulli factories: matchings, flows, and other polytopes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A Bernoulli factory is an algorithmic procedure for exact sampling of certain random variables having only <PERSON><PERSON><PERSON> access to their parameters. <PERSON><PERSON><PERSON> access to a parameter p ∈ [0,1] means the algorithm does not know p, but has sample access to independent draws of a <PERSON><PERSON><PERSON> random variable with mean equal to p. In this paper, we study the problem of Bernoulli factories for polytopes: given <PERSON><PERSON><PERSON> access to a vector x∈ P for a given polytope P⊂ [0,1]n, output a randomized vertex such that the expected value of the i-th coordinate is exactly equal to xi. For example, for the special case of the perfect matching polytope, one is given <PERSON><PERSON><PERSON> access to the entries of a doubly stochastic matrix [xij] and asked to sample a matching such that the probability of each edge (i,j) be present in the matching is exactly equal to xij.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451072"}, {"primary_key": "2239927", "vector": [], "sparse_vector": [], "title": "Polynomial time deterministic identity testing algorithm for Σ[3]ΠΣΠ[2] circuits via <PERSON><PERSON><PERSON><PERSON> type theorem for quadratic polynomials.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this work we resolve conjectures of <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON> [BMS13] and <PERSON> [Gupta14], by proving an analog of a theorem of <PERSON><PERSON><PERSON> and <PERSON> for quadratic polynomials. As immediate corollary we obtain the first deterministic polynomial time black-box algorithm for testing zeroness of Σ[3]ΠΣΠ[2] circuits.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451013"}, {"primary_key": "2239928", "vector": [], "sparse_vector": [], "title": "Frozen 1-RSB structure of the symmetric Ising perceptron.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We prove, under an assumption on the critical points of a real-valued function, that the symmetric Ising perceptron exhibits the `frozen 1-RSB' structure conjectured by <PERSON><PERSON> and <PERSON><PERSON> in the physics literature; that is, typical solutions of the model lie in clusters of vanishing entropy density. Moreover, we prove this in a very strong form conjectured by <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>: a typical solution of the model is isolated with high probability and the Hamming distance to all other solutions is linear in the dimension. The frozen 1-RSB scenario is part of a recent and intriguing explanation of the performance of learning algorithms by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. We prove this structural result by comparing the symmetric Ising perceptron model to a planted model and proving a comparison result between the two models. Our main technical tool towards this comparison is an inductive argument for the concentration of the logarithm of number of solutions in the model.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451119"}, {"primary_key": "2239929", "vector": [], "sparse_vector": [], "title": "Information theoretic limits of cardinality estimation: <PERSON> meets <PERSON>.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Estimating the cardinality (number of distinct elements) of a large multiset is a classic problem in streaming and sketching, dating back to <PERSON><PERSON><PERSON><PERSON> and <PERSON>'s classic Probabilistic Counting (PCSA) algorithm from 1983.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451032"}, {"primary_key": "2239930", "vector": [], "sparse_vector": [], "title": "Strong co-nondeterministic lower bounds for NP cannot be proved feasibly.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show unconditionally that <PERSON>’s theory PV formalizing poly-time reasoning cannot prove, for any non-deterministic poly-time machine M defining a language L(M), that L(M) is inapproximable by co-nondeterministic circuits of sub-exponential size. In fact, our unprovability result holds also for a theory which supports a fragment of <PERSON><PERSON><PERSON><PERSON>’s theory of approximate counting APC1. We also show similar unconditional unprovability results for the conjecture of <PERSON><PERSON><PERSON> about the existence of super-bits.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451117"}, {"primary_key": "2239931", "vector": [], "sparse_vector": [], "title": "Stronger calibration lower bounds via sidestepping.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider an online binary prediction setting where a forecaster observes a sequence of T bits one by one. Before each bit is revealed, the forecaster predicts the probability that the bit is 1. The forecaster is called well-calibrated if for each p ∈ [0, 1], among the np bits for which the forecaster predicts probability p, the actual number of ones, mp, is indeed equal to p · np. The calibration error, defined as ∑p |mp − p np|, quantifies the extent to which the forecaster deviates from being well-calibrated. It has long been known that an O(T2/3) calibration error is achievable even when the bits are chosen adversarially, and possibly based on the previous predictions. However, little is known on the lower bound side, except an Ω(√T) bound that follows from the trivial example of independent fair coin flips.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451050"}, {"primary_key": "2239932", "vector": [], "sparse_vector": [], "title": "Automating algebraic proof systems is NP-hard.", "authors": ["Susanna F. de Rezende", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We show that algebraic proofs are hard to find: Given an unsatisfiable CNF formula F, it is NP-hard to find a refutation of F in the Nullstellensatz, Polynomial Calculus, or Sherali-<PERSON> proof systems in time polynomial in the size of the shortest such refutation. Our work extends, and gives a simplified proof of, the recent breakthrough of <PERSON><PERSON><PERSON> and <PERSON> (JACM 2020) that established an analogous result for Resolution.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451080"}, {"primary_key": "2239933", "vector": [], "sparse_vector": [], "title": "A (2 + ε)-approximation algorithm for preemptive weighted flow time on a single machine.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Weighted flow time is a fundamental and very well-studied objective function in scheduling. In this paper, we study the setting of a single machine with preemptions.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451075"}, {"primary_key": "2239934", "vector": [], "sparse_vector": [], "title": "Linear bandits with limited adaptivity and learning distributional optimal design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by practical needs such as large-scale learning, we study the impact of adaptivity constraints to linear contextual bandits, a central problem in online learning and decision making. We consider two popular limited adaptivity models in literature: batch learning and rare policy switches. We show that, when the context vectors are adversarially chosen in d-dimensional linear contextual bandits, the learner needs O(d logd logT) policy switches to achieve the minimax-optimal regret, and this is optimal up to poly(logd, loglogT) factors; for stochastic context vectors, even in the more restricted batch learning model, only O(loglogT) batches are needed to achieve the optimal regret. Together with the known results in literature, our results present a complete picture about the adaptivity constraints in linear contextual bandits. Along the way, we propose the distributional optimal design, a natural extension of the optimal experiment design, and provide a both statistically and computationally efficient learning algorithm for the problem, which may be of independent interest.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451004"}, {"primary_key": "2239935", "vector": [], "sparse_vector": [], "title": "Stronger bounds for weak epsilon-nets in higher dimensions.", "authors": ["<PERSON><PERSON>"], "summary": "Given a finite point set P in ℝd, and >0 we say that N⊆ ℝd is a weak -net if it pierces every convex set K with |K∩ P|≥ є |P|.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451062"}, {"primary_key": "2239936", "vector": [], "sparse_vector": [], "title": "Exponential communication separations between notions of selfishness.", "authors": ["<PERSON>via<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of implementing a fixed social choice function between multiple players (which takes as input a type ti from each player i and outputs an outcome f(t1,…, tn)), in which each player must be incentivized to follow the protocol. In particular, we study the communication requirements of a protocol which: (a) implements f, (b) implements f and computes payments that make it ex-post incentive compatible (EPIC) to follow the protocol, and (c) implements f and computes payments in a way that makes it dominant-strategy incentive compatible (DSIC) to follow the protocol.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451127"}, {"primary_key": "2239937", "vector": [], "sparse_vector": [], "title": "The randomized communication complexity of randomized auctions.", "authors": ["<PERSON>via<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the communication complexity of incentive compatible auction-protocols between a monopolist seller and a single buyer with a combinatorial valuation function over n items. Motivated by the fact that revenue-optimal auctions are randomized (as well as by an open problem of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>), we focus on the randomized communication complexity of this problem (in contrast to most prior work on deterministic communication). We design simple, incentive compatible, and revenue-optimal auction-protocols whose expected communication complexity is much (in fact infinitely) more efficient than their deterministic counterparts. We also give nearly matching lower bounds on the expected communication complexity of approximately-revenue-optimal auctions. These results follow from a simple characterization of incentive compatible auction-protocols that allows us to prove lower bounds against randomized auction-protocols. In particular, our lower bounds give the first approximation-resistant, exponential separation between communication complexity of incentivizing vs implementing a Bayesian incentive compatible social choice rule, settling an open question of <PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451111"}, {"primary_key": "2239938", "vector": [], "sparse_vector": [], "title": "Iterated lower bound formulas: a diagonalization-based approach to proof complexity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a diagonalization-based approach to several important questions in proof complexity. We illustrate this approach in the context of the algebraic proof system IPS and in the context of propositional proof systems more generally.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451010"}, {"primary_key": "2239939", "vector": [], "sparse_vector": [], "title": "Explicit uniquely decodable codes for space bounded channels that achieve list-decoding capacity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider codes for space bounded channels. This is a model for communication under noise that was introduced by <PERSON><PERSON><PERSON> and <PERSON> (J. <PERSON> 2016) and lies between the Shannon (random) and Hamming (adversarial) models. In this model, a channel is a space bounded procedure that reads the codeword in one pass, and modifies at most a p fraction of the bits of the codeword.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451048"}, {"primary_key": "2239940", "vector": [], "sparse_vector": [], "title": "An optimal separation of randomized and Quantum query complexity.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove that for every decision tree, the absolute values of the Fourier coefficients of given order t≥1 sum to at most (cd/t)t/2(1+logn)(t−1)/2, where n is the number of variables, d is the tree depth, and c>0 is an absolute constant. This bound is essentially tight and settles a conjecture due to <PERSON><PERSON> (arxiv 2019; FOCS 2020). The bounds prior to our work degraded rapidly with t, becoming trivial already at t=√d.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451019"}, {"primary_key": "2239941", "vector": [], "sparse_vector": [], "title": "Statistical physics of random CSPs (tutorial).", "authors": ["Nike Sun"], "summary": "I will describe recent progress in determination of asymptotic behavior in random constraint satisfaction problems, including the independent set problem on random graphs, random regular NAE-SAT, and random SAT. The results include sharp phase transitions and some understanding of solution geometry, particularly in the setting of the random regular NAE-SAT problem. In this lecture I will survey the physics heuristics, and explain how they lead to combinatorial models for the solution geometry, which form a basis of mathematical approaches to these problems. As time allows, I will discuss some of the mathematical techniques that have been introduced, particularly with regards to solving certain non-convex optimization problems that arise in moment method calculations.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3465352"}, {"primary_key": "2239942", "vector": [], "sparse_vector": [], "title": "A new algorithm for Euclidean shortest paths in the plane.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Given a set of pairwise disjoint polygonal obstacles in the plane, finding an obstacle-avoiding Euclidean shortest path between two points is a classical problem in computational geometry and has been studied extensively. Previously, <PERSON><PERSON><PERSON> and <PERSON><PERSON> [SIAM J. Comput. 1999] gave an algorithm of O(nlogn) time and O(nlogn) space, where n is the total number of vertices of all obstacles. Recently, by modifying <PERSON><PERSON><PERSON> and <PERSON><PERSON>'s algorithm, <PERSON> [SODA 2021] reduced the space to O(n) while the runtime of the algorithm is still O(nlogn). In this paper, we present a new algorithm of O(n+hlogh) time and O(n) space, provided that a triangulation of the free space is given, where h is the number of obstacles. Our algorithm builds a shortest path map for a source point s, so that given any query point t, the shortest path length from s to t can be computed in O(logn) time and a shortest s-t path can be produced in additional time linear in the number of edges of the path.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451037"}, {"primary_key": "2239943", "vector": [], "sparse_vector": [], "title": "Succinct blind Quantum computation using a random oracle.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In the universal blind quantum computation problem, a client wants to make use of a single quantum server to evaluate C|0⟩ where C is an arbitrary quantum circuit while keeping C secret. The client's goal is to use as few resources as possible. This problem, first raised by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [FOCS 2009], has become fundamental to the study of quantum cryptography, not only because of its own importance, but also because it provides a testbed for new techniques that can be later applied to related problems (for example, quantum computation verification). Known protocols on this problem are mainly either information-theoretically (IT) secure or based on trapdoor assumptions (public key encryptions). In this paper we study how the availability of symmetric-key primitives, modeled by a random oracle, changes the complexity of universal blind quantum computation. We give a new universal blind quantum computation protocol. Similar to previous works on IT-secure protocols (for example, Broadbent-<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>), our protocol can be divided into two phases. In the first phase the client prepares some quantum gadgets with relatively simple quantum gates and sends them to the server, and in the second phase the client is entirely classical — it does not even need quantum storage. Crucially, the protocol's first phase is succinct, that is, its complexity is independent of the circuit size. Given the security parameter κ, its complexity is only a fixed polynomial of κ, and can be used to evaluate any circuit (or several circuits) of size up to a subexponential of κ. In contrast, known schemes either require the client to perform quantum computations that scale with the size of the circuit, or require trapdoor assumptions.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325.3451082"}, {"primary_key": "2255676", "vector": [], "sparse_vector": [], "title": "STOC &apos;21: 53rd Annual ACM SIGACT Symposium on Theory of Computing, Virtual Event, Italy, June 21-25, 2021.", "authors": ["<PERSON><PERSON>", "Virginia Vassilevska Williams"], "summary": "We consider the problem of approximating maximum Nash social welfare (NSW) while allocating a set of indivisible items to n agents.The NSW is a popular objective that provides a balanced tradeoff between the often conflicting requirements of fairness and efficiency, defined as the weighted geometric mean of the agents' valuations.For the symmetric additive case of the problem, where agents have the same weight with additive valuations, the first constant-factor approximation algorithm was obtained in 2015.Subsequent work has obtained constant-factor approximation algorithms for the symmetric case under mild generalizations of additive, and O(n)-approximation algorithms for subadditive valuations and for the asymmetric case.In this paper, we make significant progress towards both symmetric and asymmetric NSW problems.We present the first constant-factor approximation algorithm for the symmetric case under Rado valuations.Rado valuations form a general class of valuation functions that arise from maximum cost independent matching problems, including as special cases assignment (OXS) valuations and weighted matroid rank functions.Furthermore, our approach also gives the first constant-factor approximation algorithm for the asymmetric case under Rado valuations, provided that the maximum ratio between the weights is bounded by a constant.", "published": "2021-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3406325"}]