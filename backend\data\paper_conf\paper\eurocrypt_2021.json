[{"primary_key": "2130535", "vector": [], "sparse_vector": [], "title": "Compactness of Hashing Modes and Efficiency Beyond Merkle Tree.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We revisit the classical problem of designing optimally efficient cryptographically secure hash functions. Hash functions are traditionally designed via applying modes of operation on primitives with smaller domains. The results of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (ICALP 2008), <PERSON><PERSON><PERSON> and <PERSON> (CRYPTO 2008), and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (CRYPTO 2012) show how to achieve optimally efficient designs of 2n-to-n-bit compression functions from non-compressing primitives with asymptotically optimal\\(2^{n/2-\\epsilon }\\)-query collision resistance. Designing optimally efficient and secure hash functions for larger domains (\\({>}{2n}\\)bits) is still an open problem. To enable efficiency analysis and comparison across hash functions built from primitives of different domain sizes, in this work we propose the newcompactnessefficiency notion. It allows us to focus on asymptotically optimally collision resistant hash function and normalize their parameters based on <PERSON><PERSON>’s bound from CRYPTO 2008 to obtain maximal efficiency. We then present two tree-based modes of operation as a design principle for compact, large domain, fixed-input-length hash functions. Our first construction is anAugmentedBinary Tree (ABR) mode. The design is a\\((2^{\\ell }+2^{\\ell -1} -1)n\\)-to-n-bit hash function making a total of\\((2^{\\ell }-1)\\)calls to 2n-to-n-bit compression functions for any\\(\\ell \\ge 2\\). Our construction is optimally compact with asymptotically (optimal)\\(2^{n/2-\\epsilon }\\)-query collision resistance in the ideal model. For a tree of height\\(\\ell \\), in comparison with Merkle tree, the\\(\\textsf {ABR}\\)mode processes additional\\((2^{\\ell -1}-1)\\)data blocks making the same number of internal compression function calls. With our second design we focus our attention on the indifferentiability security notion. While the\\(\\textsf {ABR}\\)mode achieves collision resistance, it fails to achieve indifferentiability from a random oracle within\\(2^{n/3}\\)queries.\\(\\textsf {ABR}^{+}\\)compresses only 1 less data block than\\(\\textsf {ABR}\\)with the same number of compression calls and achieves in addition indifferentiability up to\\(2^{n/2-\\epsilon }\\)queries. Both of our designs are closely related to the ubiquitous Merkle Trees and have the potential for real-world applicability where the speed of hashing is of primary interest.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_4"}, {"primary_key": "2130536", "vector": [], "sparse_vector": [], "title": "Abuse Resistant Law Enforcement Access Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The increasing deployment of end-to-end encrypted communications services has ignited a debate between technology firms and law enforcement agencies over the need for lawful access to encrypted communications. Unfortunately, existing solutions to this problem suffer from serious technical risks, such as the possibility of operator abuse and theft of escrow key material. In this work we investigate the problem of constructing law enforcement access systems that mitigate the possibility of unauthorized surveillance. We first define a set of desirable properties for an abuse-resistant law enforcement access system (ARLEAS), and motivate each of these properties. We then formalize these definitions in the Universal Composability (UC) framework, and present two main constructions that realize this definition. The first construction enablesprospectiveaccess, allowing surveillance only if encryption occurs after a warrant has been issued and activated. The second, more powerful construction, allowsretrospectiveaccess to communications that occurred prior to a warrant’s issuance. To illustrate the technical challenge of constructing the latter type of protocol, we conclude by investigating the minimal assumptions required to realize these systems.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_19"}, {"primary_key": "2130537", "vector": [], "sparse_vector": [], "title": "Non-interactive Zero Knowledge from Sub-exponential DDH.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zhengzhong Jin"], "summary": "We provide the first constructions of non-interactive zero-knowledge and Zap arguments for NP based on the sub-exponential hardness of <PERSON><PERSON> against polynomial time adversaries (without use of groups with pairings). Central to our results, and of independent interest, is a new notion ofinteractive trapdoor hashing protocols.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_1"}, {"primary_key": "2130538", "vector": [], "sparse_vector": [], "title": "Pre-computation Scheme of Window τNAF for Koblitz Curves Revisited.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Let\\(E_a/ \\mathbb {F}_{2}: y^2+xy=x^3+ax^2+1\\)be a Koblitz curve. The window\\(\\tau \\)-adic non-adjacent form (window\\(\\tau \\)NAF) is currently the standard representation system to perform scalar multiplications on\\(E_a/ \\mathbb {F}_{2^m}\\)utilizing the Frobenius map\\(\\tau \\). This work focuses on the pre-computation part of scalar multiplication. We first introduce\\(\\mu \\bar{\\tau }\\)-operations where\\(\\mu =(-1)^{1-a}\\)and\\(\\bar{\\tau }\\)is the complex conjugate of\\(\\tau \\). Efficient formulas of\\(\\mu \\bar{\\tau }\\)-operations are then derived and used in a novel pre-computation scheme. Our pre-computation scheme requires 6M\\(\\,+\\,6\\)S, 18M\\(\\,+\\,17\\)S, 44M\\(\\,+\\,32\\)S, and 88M\\(\\,+\\,62\\)S(\\(a=0\\)) and 6M\\(\\,+\\,6\\)S, 19M\\(\\,+\\,17\\)S, 46M\\(\\,+\\,32\\)S, and 90M\\(\\,+\\,62\\)S(\\(a=1\\)) for window\\(\\tau \\)NAF with widths from 4 to 7 respectively. It is about two times faster, compared to the state-of-the-art technique of pre-computation in the literature. The impact of our new efficient pre-computation is also reflected by the significant improvement of scalar multiplication. Traditionally, window\\(\\tau \\)NAF with width at most 6 is used to achieve the best scalar multiplication. Because of the dramatic cost reduction of the proposed pre-computation, we are able to increase the width for window\\(\\tau \\)NAF to 7 for a better scalar multiplication. This indicates that the pre-computation part becomes more important in performing scalar multiplication. With our efficient pre-computation and the new window width, our scalar multiplication runs in at least 85.2% the time of Kohel’s work (Eurocrypt’2017) combining the best previous pre-computation. Our results push the scalar multiplication of Koblitz curves, a very well-studied and long-standing research area, to a significant new stage.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_7"}, {"primary_key": "2130539", "vector": [], "sparse_vector": [], "title": "Post-Quantum Multi-Party Computation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We initiate the study of multi-party computation for classical functionalities in the plain model, with security against malicious quantum adversaries. We observe that existing techniques readily give a polynomial-round protocol, but our main result is a construction ofconstant-roundpost-quantum multi-party computation. We assume mildly super-polynomial quantum hardness of learning with errors (LWE), and quantum polynomial hardness of an LWE-based circular security assumption. Along the way, we develop the following cryptographic primitives that may be of independent interest: A spooky encryption scheme for relations computable by quantum circuits, from the quantum hardness of (a circular variant of) the LWE problem. This immediately yields the first quantum multi-key fully-homomorphic encryption scheme with classical keys. A constant-round post-quantum non-malleable commitment scheme, from the mildly super-polynomial quantum hardness of LWE. To prove the security of our protocol, we develop a new straight-line non-black-box simulation technique against parallel sessions that does not clone the adversary’s state. This technique may also be relevant to the classical setting.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_16"}, {"primary_key": "2130540", "vector": [], "sparse_vector": [], "title": "A 2n/2-Time Algorithm for $\\sqrt{n}$-SVP and $\\sqrt{n}$-Hermite SVP, and an Improved Time-Approximation Tradeoff for (H)SVP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We show a\\(2^{n/2+o(n)}\\)-time algorithm that, given as input a basis of a lattice\\(\\mathcal {L}\\subset \\mathbb {R}^n\\), finds a (non-zero) vector in whose length is at most\\(\\widetilde{O}(\\sqrt{n})\\cdot \\min \\{\\lambda _1(\\mathcal {L}), \\det (\\mathcal {L})^{1/n}\\}\\), where\\(\\lambda _1(\\mathcal {L})\\)is the length of a shortest non-zero lattice vector and\\(\\det (\\mathcal {L})\\)is the lattice determinant. <PERSON><PERSON> showed that\\(\\lambda _1(\\mathcal {L}) \\le \\sqrt{n} \\det (\\mathcal {L})^{1/n}\\)and that there exist lattices with\\(\\lambda _1(\\mathcal {L}) \\ge \\varOmega (\\sqrt{n}) \\cdot \\det (\\mathcal {L})^{1/n}\\), so that our algorithm finds vectors that are as short as possible relative to the determinant (up to a polylogarithmic factor). The main technical contribution behind this result is new analysis of (a simpler variant of) a\\(2^{n/2 + o(n)}\\)-time algorithm from [ADRS15], which was only previously known to solve less useful problems. To achieve this, we rely crucially on the “reverse Minkowski theorem” (conjectured by Dadush [DR16] and proven by [RS17]), which can be thought of as a partial converse to the fact that\\(\\lambda _1(\\mathcal {L}) \\le \\sqrt{n} \\det (\\mathcal {L})^{1/n}\\). Previously, the fastest known algorithm for finding such a vector was the\\(2^{.802n + o(n)}\\)-time algorithm due to [LWXZ11], which actually found a non-zero lattice vector with length\\(O(1) \\cdot \\lambda _1(\\mathcal {L})\\). Though we do not show how to find lattice vectors with this length in time\\(2^{n/2+o(n)}\\), we do show that our algorithm suffices for the most important application of such algorithms: basis reduction. In particular, we show a modified version of Gama and Nguyen’s slide-reduction algorithm [GN08], which can be combined with the algorithm above to improve the time-length tradeoff for shortest-vector algorithms in nearly all regimes—including the regimes relevant to cryptography.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_17"}, {"primary_key": "2130541", "vector": [], "sparse_vector": [], "title": "On Bounded Distance Decoding with Predicate: Breaking the &quot;<PERSON><PERSON><PERSON>&quot; for the Hidden Number Problem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Lattice-based algorithms in cryptanalysis often search for a target vector satisfying integer linear constraints as a shortest or closest vector in some lattice. In this work, we observe that these formulations may discard non-linear information from the underlying application that can be used to distinguish the target vector even when it is far from being uniquely close or short. We formalize lattice problems augmented with a predicate distinguishing a target vector and give algorithms for solving instances of these problems. We apply our techniques to lattice-based approaches for solving the Hidden Number Problem, a popular technique for recovering secret DSA or ECDSA keys in side-channel attacks, and demonstrate that our algorithms succeed in recovering the signing key for instances that were previously believed to be unsolvable using lattice approaches. We carried out extensive experiments using our estimation and solving framework, which we also make available with this work.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_19"}, {"primary_key": "2130542", "vector": [], "sparse_vector": [], "title": "Analysing the HPKE Standard.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "TheHybrid Public Key Encryption(HPKE) scheme is an emerging standard currently under consideration by the Crypto Forum Research Group (CFRG) of the IETF as a candidate for formal approval. Of the four modes of HPKE, we analyse the authenticated mode\\(\\mathsf {HPKE}_\\mathsf {Auth}\\)in its single-shot encryption form as it contains what is, arguably, the most novel part of HPKE. \\(\\mathsf {HPKE}_\\mathsf {Auth}\\)’s intended application domain is captured by a new primitive which we call Authenticated Public Key Encryption (APKE). We provide syntax and security definitions for APKE schemes, as well as for the related Authenticated Key Encapsulation Mechanisms (AKEMs). We prove security of the AKEM scheme\\(\\mathsf {DH}\\hbox {-}\\mathsf {AKEM}\\)underlying\\(\\mathsf {HPKE}_\\mathsf {Auth}\\)based on the Gap Diffie-Hellman assumption and provide general AKEM/DEM composition theorems with which to argue about\\(\\mathsf {HPKE}_\\mathsf {Auth}\\)’s security. To this end, we also formally analyse\\(\\mathsf {HPKE}_\\mathsf {Auth}\\)’s key schedule and key derivation functions. To increase confidence in our results we use the automatic theorem proving tool CryptoVerif. All our bounds are quantitative and we discuss their practical implications for\\(\\mathsf {HPKE}_\\mathsf {Auth}\\). As an independent contribution we propose the new framework ofnominal groupsthat allows us to capture abstract syntactical and security properties of practical elliptic curves, including the Curve25519 and Curve448 based groups (which do not constitute cyclic groups).", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_4"}, {"primary_key": "2130543", "vector": [], "sparse_vector": [], "title": "Three Third Generation Attacks on the Format Preserving Encryption Scheme FF3.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Format-Preserving Encryption (FPE) schemes accept plaintexts from any finite set of values (such as social security numbers or birth dates) and produce ciphertexts that belong to the same set. They are extremely useful in practice since they make it possible to encrypt existing databases or communication packets without changing their format. Due to industry demand, NIST had standardized in 2016 two such encryption schemes called FF1 and FF3. They immediately attracted considerable cryptanalytic attention with decreasing attack complexities. The best currently known attack on the Feistel construction FF3 has data and memory complexity of\\({O}(N^{11/6})\\)and time complexity of\\({O}(N^{17/6})\\), where the input belongs to a domain of size\\(N \\times N\\). In this paper, we present and experimentally verify three improved attacks on FF3. Our best attack achieves the tradeoff curve\\(D=M=\\tilde{O}(N^{2-t})\\),\\(T=\\tilde{O}(N^{2+t})\\)for all\\(t \\le 0.5\\). In particular, we can reduce the data and memory complexities to the more practical\\(\\tilde{O}(N^{1.5})\\), and at the same time, reduce the time complexity to\\(\\tilde{O}(N^{2.5})\\). We also identify another attack vector against FPE schemes, therelated-domainattack. We show how one can mount powerful attacks when the adversary is given access to the encryption under the same key in different domains, and show how to apply it to efficiently distinguish FF3 and FF3-1 instances.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_5"}, {"primary_key": "2130544", "vector": [], "sparse_vector": [], "title": "Towards Accountability in CRS Generation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It is well known that several cryptographic primitives cannot be achieved without a common reference string (CRS). Those include, for instance, non-interactive zero-knowledge for NP, or maliciously secure computation in fewer than four rounds. The security of those primitives heavily relies upon on the assumption that the trusted authority, who generates the CRS, does not misuse the randomness used in the CRS generation. However, we argue that there is no such thing as an unconditionally trusted authority and every authority must be held accountable for any trust to be well-founded. Indeed, a malicious authority can, for instance, recover private inputs of honest parties given transcripts of the protocols executed with respect to the CRS it has generated. While eliminating trust in the trusted authority may not be entirely feasible, can we at least move towards achieving some notion of accountability? We propose a new notion in which, if the CRS authority releases the private inputs of protocol executions to others, we can then provide a publicly-verifiable proof that certifies that the authority misbehaved. We study the feasibility of this notion in the context of non-interactive zero knowledge and two-round secure two-party computation.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_10"}, {"primary_key": "2130545", "vector": [], "sparse_vector": [], "title": "Unbounded Multi-party Computation from Learning with Errors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zhengzhong Jin", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of round-optimalunbounded MPC: in the first round, parties publish a message that depends only on their input. In the second round, any subset of parties can jointly and securely compute any functionfover their inputs in a single round of broadcast. We do not impose any a-priori bound on the number of parties nor on the size of the functions that can be computed. Our main result is a semi-honest two-round protocol for unbounded MPC in the plain model from the hardness of the standard learning with errors (LWE) problem. Prior work in the same setting assumes the hardness of problems over bilinear maps. Thus, our protocol is the first example of unbounded MPC that is post-quantum secure. The central ingredient of our protocol is a new scheme of attribute-based secure function evaluation (AB-SFE) withpublic decryption. Our construction combines techniques from the realm of homomorphic commitments with delegation of lattice basis. We believe that such a scheme may find further applications in the future.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_26"}, {"primary_key": "2130546", "vector": [], "sparse_vector": [], "title": "Secure Software Leasing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Rolando L. La Placa"], "summary": "Formulating cryptographic definitions to protect against software piracy is an important research direction that has not received much attention. Since natural definitions using classical cryptography are impossible to achieve (as classical programs can always be copied), this directs us towards using techniques from quantum computing. The seminal work of <PERSON><PERSON> [CCC’09] introduced the notion of quantum copy-protection precisely to address the problem of software anti-piracy. However, despite being one of the most important problems in quantum cryptography, there are no provably secure solutions of quantum copy-protection known foranyclass of functions. We formulate an alternative definition for tackling software piracy, called secure software leasing (SSL). While weaker than quantum copy-protection, SSL is still meaningful and has interesting applications in software anti-piracy. We present a construction of SSL for a subclass of evasive circuits (that includes natural implementations of point functions, conjunctions with wild cards, and affine testers) based on concrete cryptographic assumptions. Our construction is the first provably secure solution, based on concrete cryptographic assumptions, for software anti-piracy. To complement our positive result, we show, based on cryptographic assumptions, that there is a class of quantum unlearnable functions for which SSL does not exist. In particular, our impossibility result also rules out quantum copy-protection [<PERSON><PERSON> CCC’09] for an arbitrary class of quantum unlearnable functions; resolving an important open problem on the possibility of constructing copy-protection for arbitrary quantum unlearnable circuits.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_17"}, {"primary_key": "2130547", "vector": [], "sparse_vector": [], "title": "Dynamic Ad Hoc Clock Synchronization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Clock synchronization allows parties to establish a common notion of global time by leveraging a weaker synchrony assumption, i.e., local clocks with approximately the same speed. Despite intensive investigation of the problem in the fault-tolerant distributed computing literature, existing solutions do not apply to settings where participation is unknown, e.g., the ad hoc model of Beimelet al.[EUROCRYPT 17], or is dynamically shifting over time, e.g., the fluctuating/sleepy/dynamic-availability models of Garayet al.[CRYPTO 17], Pass and Shi [ASIACRYPT 17] and Badertscheret al.[CCS 18]. We show how to apply and extend ideas from the blockchain literature to devise synchronizers that work in such dynamic ad hoc settings and tolerate corrupted minorities under the standard assumption that local clocks advance at approximately the same speed. We discuss both the setting of honest-majority hashing power and that of a PKI with honest majority. Our main result is a synchronizer that is directly integrated with a new proof-of-stake (PoS) blockchain protocol, Ouroboros Chronos, which we construct and prove secure; to our knowledge, this is the first PoS blockchain protocol to rely only onlocalclocks, while tolerating worst-case corruption and dynamically fluctuating participation. We believe that this result might be of independent interest.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_14"}, {"primary_key": "2130548", "vector": [], "sparse_vector": [], "title": "Automatic Search of Meet-in-the-Middle Preimage Attacks on AES-like Hashing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Dong", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>wei Sun", "<PERSON><PERSON>"], "summary": "The Meet-in-the-Middle (MITM) preimage attack is highly effective in breaking the preimage resistance of many hash functions, including but not limited to the fullMD5,HAVAL, andTiger, and reducedSHA-0/1/2. It was also shown to be a threat to hash functions built on block ciphers likeA<PERSON><PERSON> in 2011. Recently, such attacks onAEShashing modes evolved from merely using the freedom of choosing the internal state to also exploiting the freedom of choosing the message state. However, detecting such attacks especially those evolved variants is difficult. In previous works, the search space of the configurations of such attacks is limited, such that manual analysis is practical, which results in sub-optimal solutions. In this paper, we remove artificial limitations in previous works, formulate the essential ideas of the construction of the attack in well-defined ways, and translate the problem of searching for the best attacks into optimization problems under constraints in Mixed-Integer-Linear-Programming (MILP) models. The MILP models capture a large solution space of valid attacks; and the objectives of the MILP models are attack configurations with the minimized computational complexity. With such MILP models and using the off-the-shelf solver, it is efficient to search for the best attacks exhaustively. As a result, we obtain the first attacks against the full (5-round) and an extended (5.5-round) version ofHaraka-512 v2, and 8-roundAES-128 hashing modes, as well as improved attacks covering more rounds ofHaraka-256 v2and other members ofAESandRijndaelhashing modes.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_27"}, {"primary_key": "2130549", "vector": [], "sparse_vector": [], "title": "TARDIS: A Foundation of Time-Lock Puzzles in UC.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Time-based primitives like time-lock puzzles (TLP) are finding widespread use in practical protocols, partially due to the surge of interest in the blockchain space where TLPs and related primitives are perceived to solve many problems. Unfortunately, the security claims are often shaky or plainly wrong since these primitives are used under composition. One reason is that TLPs are inherently not UC secure and time is tricky to model and use in the UC model. On the other hand, just specifying standalone notions of the intended task, left alone correctly using standalone notions like non-malleable TLPs only, might be hard or impossible for the given task. And even when possible a standalone secure primitive is harder to apply securely in practice afterwards as its behavior under composition is unclear. The ideal solution would be a model of TLPs in the UC framework to allow simple modular proofs. In this paper we provide a foundation for proving composable security of practical protocols using time-lock puzzles and related timed primitives in the UC model. We construct UC-secure TLPs based on random oracles and show that using random oracles isnecessary. In order to prove security, we provide a simple and abstract way to reason about time in UC protocols. Finally, we demonstrate the usefulness of this foundation by constructing applications that are interesting in their own right, such as UC-secure two-party computation with output-independent abort.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_15"}, {"primary_key": "2130550", "vector": [], "sparse_vector": [], "title": "Order-C Secure Multiparty Computation for Highly Repetitive Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Running secure multiparty computation (MPC) protocols with hundreds or thousands of players would allow leveraging large volunteer networks (such as blockchains and Tor) and help justify honest majority assumptions. However, most existing protocols have at least a linear (multiplicative) dependence on the number of players, making scaling difficult. Known protocols with asymptotic efficiency independent of the number of parties (excluding additive factors) require expensive circuit transformations that induce large overheads. We observe that the circuits used in many important applications of MPC such as training algorithms used to create machine learning models have ahighly repetitive structure. We formalize this class of circuits and propose an MPC protocol that achieves\\(O(|\\mathsf {C}|)\\)total complexity for this class. We implement our protocol and show that it is practical and outperforms\\(O(n|\\mathsf {C}|)\\)protocols for modest numbers of players.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_23"}, {"primary_key": "2130551", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of the GPRS Encryption Algorithms GEA-1 and GEA-2.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents the first publicly available cryptanalytic attacks on theGEA-1andGEA-2algorithms. Instead of providing full 64-bit security, we show that the initial state ofGEA-1can be recovered from as little as 65 bits of known keystream (with at least 24 bits coming from one frame) in time\\(2^{40}\\)GEA-1evaluations and using 44.5 GiB of memory. The attack onGEA-1is based on an exceptional interaction of the deployed LFSRs and the key initialization, which is highly unlikely to occur by chance. This unusual pattern indicates that the weakness is intentionally hidden to limit the security level to 40 bit by design. In contrast, forGEA-2we did not discover the same intentional weakness. However, using a combination of algebraic techniques and list merging algorithms we are still able to breakGEA-2in time\\(2^{45.1}\\)GEA-2evaluations. The main practical hurdle is the required knowledge of 1600 bytes of keystream.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_6"}, {"primary_key": "2130552", "vector": [], "sparse_vector": [], "title": "On the Power of Expansion: More Efficient Constructions in the Random Probing Model.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The random probing model is a leakage model in which each wire of a circuit leaks with a given probabilityp. This model enjoys practical relevance thanks to a reduction to the noisy leakage model, which is admitted as the right formalization for power and electromagnetic side-channel attacks. In addition, the random probing model is much more convenient than the noisy leakage model to prove the security of masking schemes. In a recent work, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (CRYPTO 2018) introduce a nice expansion strategy to construct random probing secure circuits. Their construction tolerates a leakage probability of\\(2^{-26}\\), which is the first quantified achievable leakage probability in the random probing model. In a follow-up work, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>in, and <PERSON><PERSON> (CRYPTO 2020) generalize their idea and put forward a complete and practical framework to generate random probing secure circuits. The so-called expanding compiler can bootstrap simple base gadgets as long as they satisfy a new security notion calledrandom probing expandability(RPE). They further provide an instantiation of the framework which tolerates a\\(2^{-8}\\)leakage probability in complexity\\(\\mathcal {O}(\\kappa ^{7.5})\\)where\\(\\kappa \\)denotes the security parameter. In this paper, we provide an in-depth analysis of the RPE security notion. We exhibit the first upper bounds for the main parameter of a RPE gadget, which is known as theamplification order. We further show that the RPE notion can be made tighter and we exhibit strong connections between RPE and thestrong non-interference(SNI) composition notion. We then introduce the first generic constructions of gadgets achieving RPE for any number of shares and with nearly optimal amplification orders and provide an asymptotic analysis of such constructions. Last but not least, we introduce new concrete constructions of small gadgets achieving maximal amplification orders. This allows us to obtain much more efficient instantiations of the expanding compiler: we obtain a complexity of\\(\\mathcal {O}(\\kappa ^{3.9})\\)for a slightly better leakage probability, as well as\\(\\mathcal {O}(\\kappa ^{3.2})\\)for a slightly lower leakage probability.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_11"}, {"primary_key": "2130553", "vector": [], "sparse_vector": [], "title": "Large Scale, Actively Secure Computation from LPN and Free-XOR Garbled Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We (MPC) protocol based on garbled circuits which is both actively secure and supports the free-XOR technique, and which has communication complexityO(n) per party. This improves on a protocol of Ben<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and Omri which only achieved passive security, without support for free-XOR. Our construction is based on a new variant of LPN-based encryption, but has the drawback of requiring a rather expensive garbling phase. To address this issue we present a second protocol that assumes at leastn/cof the parties are honest (for an arbitrary fixed valuec). This second protocol allows for a significantly lighter preprocessing, at the cost of a small sacrifice in online efficiency. We demonstrate the practicality of our evaluation phase with an implementation.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_2"}, {"primary_key": "2130554", "vector": [], "sparse_vector": [], "title": "A Deeper Look at Machine Learning-Based Cryptanalysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "At CRYPTO’19, <PERSON><PERSON> proposed a new cryptanalysis strategy based on the utilisation of machine learning algorithms. Using deep neural networks, he managed to build a neural based distinguisher that surprisingly surpassed state-of-the-art cryptanalysis efforts on one of the versions of the well studied NSA block cipherSPECK(this distinguisher could in turn be placed in a larger key recovery attack). While this work opens new possibilities for machine learning-aided cryptanalysis, it remains unclear how this distinguisher actually works and what information is the machine learning algorithm deducing. The attacker is left with a black-box that does not tell much about the nature of the possible weaknesses of the algorithm tested, while hope is thin as interpretability of deep neural networks is a well-known difficult task. In this article, we propose a detailed analysis and thorough explanations of the inherent workings of this new neural distinguisher. First, we studied the classified sets and tried to find some patterns that could guide us to better understand <PERSON><PERSON>’s results. We show with experiments that the neural distinguisher generally relies on the differential distribution on the ciphertext pairs, but also on the differential distribution in penultimate and antepenultimate rounds. In order to validate our findings, we construct a distinguisher forSPECKcipher based on pure cryptanalysis, without using any neural network, that achieves basically the same accuracy as <PERSON><PERSON>’s neural distinguisher and with the same efficiency (therefore improving over previous non-neural based distinguishers). Moreover, as another approach, we provide a machine learning-based distinguisher that strips down <PERSON><PERSON>’s deep neural network to a bare minimum. We are able to remain very close to <PERSON><PERSON>’s distinguishers’ accuracy using simple standard machine learning tools. In particular, we show that Gohr’s neural distinguisher is in fact inherently building a very good approximation of the Differential Distribution Table (DDT) of the cipher during the learning phase, and using that information to directly classify ciphertext pairs. This result allows a full interpretability of the distinguisher and represents on its own an interesting contribution towards interpretability of deep neural networks. Finally, we propose some method to improve over Gohr’s work and possible new neural distinguishers settings. All our results are confirmed with experiments we have been conducted onSPECKblock cipher (source code available online).", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_28"}, {"primary_key": "2130555", "vector": [], "sparse_vector": [], "title": "Multiparty Reusable Non-interactive Secure Computation from LWE.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by the goal of designing versatile and flexible secure computation protocols that at the same time require as little interaction as possible, we present new multiparty reusable Non-Interactive Secure Computation (mrNISC) protocols. This notion, recently introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON> (TCC 2020), is essentially two-round Multi-Party Computation (MPC) protocols where the first round of messages serves as a reusable commitment to the private inputs of participating parties. Using these commitments, any subset of parties can later compute any function of their choice on their respective inputs by just sending a single message to a stateless evaluator, conveying the result of the computation but nothing else. Importantly, the input commitments can be computed without knowing anything about other participating parties (neither their identities nor their number) and they are reusable across any number of desired computations. We give a construction of mrNISC that achieves standard simulation security, as classical multi-round MPC protocols achieve. Our construction relies on the Learning With Errors (LWE) assumption with polynomial modulus, and on the existence of a pseudorandom function (PRF) in\\(\\mathsf {NC}^1\\). We achieve semi-malicious security in the plain model and malicious security by further relying on trusted setup (which is unavoidable for mrNISC). In comparison, the only previously known constructions of mrNISC were either using bilinear maps or using strong primitives such as program obfuscation. We use our mrNISC to obtain new Multi-Key FHE (MKFHE) schemes with threshold decryption: In the CRS model, we obtain threshold MKFHE for\\(\\mathrm {NC}^1 \\)based on LWE with onlypolynomialmodulus and PRFs in\\(\\mathsf {NC}^1\\), whereas all previous constructions rely on LWE with super-polynomial modulus-to-noise ratio. In the plain model, we obtain threshold levelled MKFHE for\\(\\mathrm {P} \\)based on LsWE withpolynomialmodulus, PRF in\\(\\mathrm {NC}^1 \\), and NTRU, and another scheme for constant number of parties from LWE with sub-exponential modulus-to-noise ratio. The only known prior construction of threshold MKFHE (Ananth et al., TCC 2020) in the plain model restricts the set of parties who can compute together at the onset.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_25"}, {"primary_key": "2130556", "vector": [], "sparse_vector": [], "title": "On the (in)security of ROS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an algorithm solving the ROS (Random inhomogeneities in aOverdeterminedSolvable system of linear equations) problem modpin polynomial time for\\(\\ell > \\log p\\)dimensions. Our algorithm can be combined with <PERSON>’s attack, and leads to a sub-exponential solution for any dimension\\(\\ell \\)with best complexity known so far. When concurrent executions are allowed, our algorithm leads to practical attacks against unforgeability of blind signature schemes such as Schnorr and Okamoto–Schnorr blind signatures, threshold signatures such as GJKR and the original version of FROST, multisignatures such as CoSI and the two-round version of MuSig, partially blind signatures such as <PERSON>, and conditional blind signatures such as ZGP17. Schemes for e-cash and anonymous credentials (such as Anonymous Credentials Light) inspired from the above are also affected.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_2"}, {"primary_key": "2130557", "vector": [], "sparse_vector": [], "title": "Improved Cryptanalysis of UOV and Rainbow.", "authors": ["<PERSON>"], "summary": "The contributions of this paper are twofold. First, we simplify the description of the Unbalanced Oil and Vinegar scheme (UOV) and its Rainbow variant, which makes it easier to understand the scheme and the existing attacks. We hope that this will make UOV and Rainbow more approachable for cryptanalysts. Second, we give two new attacks against the UOV and Rainbow signature schemes; the intersection attack that applies to both UOV and Rainbow and the rectangular MinRank attack that applies only to Rainbow. Our attacks are more powerful than existing attacks. In particular, we estimate that compared to previously known attacks, our new attacks reduce the cost of a key recovery by a factor of\\(2^{17}\\),\\(2^{53}\\), and\\(2^{73}\\)for the parameter sets submitted to the second round of the NIST PQC standardization project targeting the security levels I, III, and V respectively. For the third round parameters, the cost is reduced by a factor of\\(2^{20}\\),\\(2^{40}\\), and\\(2^{55}\\)respectively. This means all these parameter sets fall short of the security requirements set out by NIST.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_13"}, {"primary_key": "2130558", "vector": [], "sparse_vector": [], "title": "Dummy Shuffling Against Algebraic Attacks in White-Box Implementations.", "authors": ["<PERSON>", "<PERSON>ek<PERSON>"], "summary": "At CHES 2016, <PERSON><PERSON> et al. showed that most of existing white-box implementations are easily broken by standard side-channel attacks. A natural idea to apply the well-developed side-channel countermeasure - linear masking schemes - leaves implementations vulnerable to linear algebraic attacks which exploit absence of noise in the white-box setting and are applicable for any order of linear masking. At ASIACRYPT 2018, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proposed a security model (BU-model for short) for protection against linear algebraic attacks and a newquadraticmasking scheme which is provably secure in this model. However, countermeasures against higher-degree attacks were left as an open problem. In this work, we study the effectiveness of another well-known side-channel countermeasure - shuffling - against linear and higher-degree algebraic attacks in the white-box setting. First, we extend the classic shuffling to include dummy computation slots and show that this is a crucial component for protecting against the algebraic attacks. We quantify and prove the security of dummy shuffling against the linear algebraic attack in the BU-model. We introduce arefreshingtechnique for dummy shuffling and show that it allows to achieve close to optimal protection in the model for arbitrary degrees of the attack, thus solving the open problem of protection against the algebraic attack in the BU-model. Furthermore, we describe an interesting proof-of-concept construction that makes the slot function public (while keeping the shuffling indexes private).", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_8"}, {"primary_key": "2130559", "vector": [], "sparse_vector": [], "title": "Fast Verification of Masking Schemes in Characteristic Two.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We revisit the matrix model for non-interference (NI) probing security of masking gadgets introduced by <PERSON><PERSON><PERSON><PERSON> al.at CRYPTO 2017. This leads to two main results. 1) We generalise the theorems on which this model is based, so as to be able to apply them to masking schemes over any finite field—in particular\\(\\mathbb {F}_2\\)—and to be able to analyse thestrongnon-interference (SNI) security notion. We also follow <PERSON>et al.(TCHES 2018) to additionally consider arobustprobing model that takes hardware defects such as glitches into account. 2) We exploit this improved model to implement a very efficient verification algorithm that improves the performance of state-of-the-art software by three orders of magnitude. We show applications to variants of NI and SNI multiplication gadgets from Bartheet al.(EUROCRYPT 2017) which we verify to be secure up to order 11 after a significant parallel computation effort, whereas the previous largest proven order was 7; SNI refreshing gadgets (ibid.); and NI multiplication gadgets from Großet al.(TIS@CCS 2016) secure in presence of glitches. We also reduce the randomness cost of some existing gadgets, notably for the implementation-friendly case of 8 shares, improving here the previous best results by 17% (resp. 19%) for SNI multiplication (resp. refreshing).", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_10"}, {"primary_key": "2130560", "vector": [], "sparse_vector": [], "title": "Efficient Bootstrapping for Approximate Homomorphic Encryption with Non-sparse Keys.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>Pastor<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a bootstrapping procedure for the full-RNS variant of the approximate homomorphic-encryption scheme of <PERSON><PERSON> et al., CKKS (Asiacrypt 17, SAC 18). Compared to the previously proposed procedures (Eurocrypt 18 & 19, CT-RSA 20), our bootstrapping procedure is more precise, more efficient (in terms of CPU cost and number of consumed levels), and is more reliable and 128-bit-secure. Unlike the previous approaches, it does not require the use of sparse secret-keys. Therefore, to the best of our knowledge, this is the first procedure that enables a highly efficient and precise bootstrapping with a low probability of failure for parameters that are 128-bit-secure under the most recent attacks on sparse R-LWE secrets. We achieve this efficiency and precision by introducing three novel contributions: (i) We propose a generic algorithm for homomorphic polynomial-evaluation that takes into account the approximate rescaling and is optimal in level consumption. (ii) We optimize the key-switch procedure and propose a new technique for linear transformations (double hoisting). (iii) We propose a systematic approach to parameterize the bootstrapping, including a precise way to assess its failure probability. We implemented our improvements and bootstrapping procedure in the open-source Lattigo library. For example, bootstrapping a plaintext in\\(\\mathbb {C}^{32768}\\)takes 18 s, has an output coefficient modulus of 505 bits, a mean precision of 19.1 bits, and a failure probability of\\(2^{-15.58}\\). Hence, we achieve 14.1\\(\\times \\)improvement in bootstrapped throughput (plaintext-bit per second), with respect to the previous best results, and we have a failure probability 468\\(\\times \\)smaller and ensure 128-bit security.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_21"}, {"primary_key": "2130561", "vector": [], "sparse_vector": [], "title": "Function Secret Sharing for Mixed-Mode and Fixed-Point Secure Computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>et al.(TCC 2019) proposed a new approach for secure computation in thepreprocessing modelbuilding onfunction secret sharing(FSS), where a gategis evaluated using an FSS scheme for the relatedoffset family\\(g_r(x)=g(x+r)\\). They further presented efficient FSS schemes based on any pseudorandom generator (PRG) for the offset families of several useful gatesgthat arise in “mixed-mode” secure computation. These include gates for zero test, integer comparison, ReLU, and spline functions. The FSS-based approach offers significant savings in online communication and round complexity compared to alternative techniques based on garbled circuits or secret sharing. In this work, we improve and extend the previous results of Boyleet al.by making the following three kinds of contributions: Improved Key Size.The preprocessing and storage costs of the FSS-based approach directly depend on the FSS key size. We improve the key size of previous constructions through two steps. First, we obtain roughly\\(4\\times \\)reduction in key size for Distributed Comparison Function (DCF), i.e., FSS for the family of functions\\(f^{<}_{\\alpha ,\\beta }(x)\\)that output\\(\\beta \\)if\\(x < \\alpha \\)and 0 otherwise. DCF serves as a central building block in the constructions of <PERSON><PERSON> al.. Second, we improve the number of DCF instances required for realizing useful gatesg. For example, whereas previous FSS schemes for ReLU andm-piece spline required 2 and 2mDCF instances, respectively, ours require only asingle instance of DCFin both cases. This improves the FSS key size by\\(6-22\\times \\)for commonly used gates such as ReLU and sigmoid. New Gates.We present the first PRG-based FSS schemes for arithmetic and logical shift gates, as well as for bit-decomposition where both the input and outputs are shared over\\(\\mathbb {Z}_{2^n}\\). These gates are crucial for many applications related to fixed-point arithmetic and machine learning. A Barrier.The above results enable a 2-round PRG-based secure evaluation of “multiply-then-truncate,” a central operation in fixed-point arithmetic, by sequentially invoking FSS schemes for multiplication and shift. We identify a barrier to obtaining a 1-round implementation via a single FSS scheme, showing that this would require settling a major open problem in the area of FSS: namely, a PRG-based FSS for the class of bit-conjunction functions.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_30"}, {"primary_key": "2130562", "vector": [], "sparse_vector": [], "title": "The Mother of All Leakages: How to Simulate Noisy Leakages via Bounded Leakage (Almost) for Free.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that the most common flavors of noisy leakage can be simulated in the information-theoretic setting using a single query of bounded leakage, up to a small statistical simulation error and a slight loss in the leakage parameter. The latter holds true in particular for one of the most used noisy-leakage models, where the noisiness is measured using the conditional average min-entropy (<PERSON><PERSON> and <PERSON><PERSON><PERSON>, CRYPTO’09 and SICOMP’12). Our reductions between noisy and bounded leakage are achieved in two steps. First, we put forward a new leakage model (dubbed thedense leakagemodel) and prove that dense leakage can be simulated in the information-theoretic setting using a single query of bounded leakage, up to small statistical distance. Second, we show that the most common noisy-leakage models fall within the class of dense leakage, with good parameters. Third, we prove lower bounds on the amount of bounded leakage required for simulation with sub-constant error, showing that our reductions are nearly optimal. In particular, our results imply that useful general simulation of noisy leakage based on statistical distance and mutual information is impossible. We also provide a complete picture of the relationships between different noisy-leakage models. Our result finds applications to leakage-resilient cryptography, where we are often able to lift security in the presence of bounded leakage to security in the presence of noisy leakage, both in the information-theoretic and in the computational setting. Additionally, we show how to use lower bounds in communication complexity to prove that bounded-collusion protocols (<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, FOCS’19) for certain functions do not only require long transcripts, but also necessarily need to reveal enough information about the inputs.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_14"}, {"primary_key": "2130563", "vector": [], "sparse_vector": [], "title": "Delay Encryption.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce a new primitive named Delay Encryption, and give an efficient instantiation based on isogenies of supersingular curves and pairings. Delay Encryption is related to Time-lock Puzzles and Verifiable Delay Functions, and can be roughly described as “time-lock identity based encryption”. It has several applications in distributed protocols, such as sealed bid Vickrey auctions and electronic voting. We give an instantiation of Delay Encryption by modifying <PERSON><PERSON> and Frankiln’s IBE scheme, where we replace the master secret key by a long chain of isogenies, as in the isogeny VDF of De Feo, Masson, Petit and Sanso. Similarly to the isogeny-based VDF, our Delay Encryption requires a trusted setup before parameters can be safely used; our trusted setup is identical to that of the VDF, thus the same parameters can be generated once and shared for many executions of both protocols, with possibly different delay parameters. We also discuss several topics around delay protocols based on isogenies that were left untreated by De <PERSON> al., namely: distributed trusted setup, watermarking, and implementation issues.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_11"}, {"primary_key": "2130564", "vector": [], "sparse_vector": [], "title": "Message-Recovery Laser Fault Injection Attack on the Classic McEliece Cryptosystem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Code-based public-key cryptosystems are promising candidates for standardization as quantum-resistant public-key cryptographic algorithms. Their security is based on the hardness of the syndrome decoding problem. Computing the syndrome in a finite field, usually\\(\\mathbb {F}_{2}\\), guarantees the security of the constructions. We show in this article that the problem becomes considerably easier to solve if the syndrome is computed in\\(\\mathbb {N}\\)instead. By means of laser fault injection, we illustrate how to compute the matrix-vector product in\\(\\mathbb {N}\\)by corrupting specific instructions, and validate it experimentally. To solve the syndrome decoding problem in\\(\\mathbb {N}\\), we propose a reduction to an integer linear programming problem. We leverage the computational efficiency of linear programming solvers to obtain real-time message recovery attacks against the code-based proposal to the NIST Post-Quantum Cryptography standardization challenge. We perform our attacks in the worst-case scenario,i.e.considering random binary codes, and retrieve the initial message within minutes on a desktop computer. Our attack targets the reference implementation of the Niederreiter cryptosystem in the NIST PQC competition finalistClassic McElieceand is practically feasible for all proposed parameters sets of this submission. For example, for the 256-bit security parameters sets, we successfully recover the message in a couple of seconds on a desktop computer Finally, we highlight the fact that the attack is still possible if only a fraction of the syndrome entries are faulty. This makes the attack feasible even though the fault injection does not have perfect repeatability and reduces the computational complexity of the attack, making it even more practical overall.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_15"}, {"primary_key": "2130565", "vector": [], "sparse_vector": [], "title": "On the Compressed-Oracle Technique, and Post-Quantum Security of Proofs of Sequential Work.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tai<PERSON><PERSON><PERSON>"], "summary": "We revisit the so-called compressed oracle technique, introduced by <PERSON><PERSON><PERSON> for analyzing quantum algorithms in the quantum random oracle model (QROM). To start off with, we offer a concise exposition of the technique, which easily extends to the parallel-query QROM, where in each query-round the considered algorithm may make several queries to the QROM in parallel. This variant of the QROM allows for a more fine-grained query-complexity analysis. Our main technical contribution is a framework that simplifies the use of (the parallel-query generalization of) the compressed oracle technique for proving query complexity results. With our framework in place, whenever applicable, it is possible to prove quantum query complexity lower bounds by means of purely classical reasoning. More than that, for typical examples the crucial classical observations that give rise to the classical bounds are sufficient to conclude the corresponding quantum bounds. We demonstrate this on a few examples, recovering known results but also obtaining new results. Our main target is the hardness of finding aq-chain with fewer thanqparallel queries, i.e., a sequence\\(x_0, x_1,\\ldots , x_q\\)with\\(x_i = H(x_{i-1})\\)for all\\(1 \\le i \\le q\\). The above problem of finding a hash chain is of fundamental importance in the context of proofs of sequential work. Indeed, as a concrete cryptographic application of our techniques, we prove quantum security of the “Simple Proofs of Sequential Work” by <PERSON> and <PERSON>\n.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_21"}, {"primary_key": "2130566", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON> Garbled Circuits and Ad Hoc Secure Computation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Garbled Circuits (GCs) represent fundamental and powerful tools in cryptography, and many variants of GCs have been considered since their introduction. An important property of the garbled circuits is that they can be evaluated securely if and only if exactly 1 key for each input wire is obtained: no less and no more. In this work we study the case when: 1) some of the wire-keys are missing, but we are still interested in computing the output of the garbled circuit and 2) the evaluator of the GC might have both keys for a constant number of wires. We start to study this question in terms of non-interactive multi-party computation (NIMPC) which is strongly connected with GCs. In this notion there is a fixed number of parties (n) that can get correlated information from a trusted setup. Then these parties can send an encoding of their input to an evaluator, which can compute the output of the function. Similarly to the notion ofad hoc secure computationproposed by <PERSON><PERSON><PERSON> et al. [ITCS 2016], we consider the case when less thannparties participate in the online phase, and in addition we let these parties colluding with the evaluator. We refer to this notion asThreshold NIMPC. In addition, we show that when the number of parties participating in the online phase is a fixed threshold\\(l\\le n\\)then it is possible to securely evaluate any\\(l\\)-input function. We build our result on top of a new secret-sharing scheme (which can be of independent interest) and on the results proposed by <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON> [Crypto 2017]. Our protocol can be used to compute any function in\\(NC^1\\)in the information-theoretic setting and any function inPassuming one-way functions. As a second (and main) contribution, we consider a slightly different notion of security in which the number of parties that can participate in the online phase is not specified, and can be any numbercabove the threshold\\(l\\)(in this case the evaluator cannot collude with the other parties). We solve an open question left open by Beimel, Ishai and Kushilevitz [Eurocrypt 2017] showing how to build a secure protocol for the case whencis constant, under the Learning with Errors assumption.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_3"}, {"primary_key": "2130567", "vector": [], "sparse_vector": [], "title": "Sieving for Twin Smooth Integers with Solutions to the Prouhet-Tarry-Escott Problem.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give a sieving algorithm for finding pairs of consecutive smooth numbers that utilizes solutions to the Prouhet-Tarry-Escott (PTE) problem. Any such solution induces two degree-npolynomials,a(x) andb(x), that differ by a constant integerCand completely split into linear factors in\\(\\mathbb {Z}[x]\\). It follows that for any\\(\\ell \\in \\mathbb {Z}\\)such that\\(a(\\ell ) \\equiv b(\\ell ) \\equiv 0 \\bmod {C}\\), the two integers\\(a(\\ell )/C\\)and\\(b(\\ell )/C\\)differ by 1 and necessarily containnfactors of roughly the same size. For a fixed smoothness boundB, restricting the search to pairs of integers that are parameterized in this way increases the probability that they areB-smooth. Our algorithm combines a simple sieve with parametrizations given by a collection of solutions to the PTE problem. The motivation for finding largetwin smoothintegers lies in their application to compact isogeny-based post-quantum protocols. The recent key exchange scheme B-SIDH and the recent digital signature scheme SQISign both require large primes that lie between two smooth integers; finding such a prime can be seen as a special case of finding twin smooth integers under the additional stipulation that their sum is a primep. When searching for cryptographic parameters with\\(2^{240} \\le p <2^{256}\\), an implementation of our sieve found primespwhere\\(p+1\\)and\\(p-1\\)are\\(2^{15}\\)-smooth; the smoothest prior parameters had a similar sized prime for which\\(p-1\\)and\\(p+1\\)were\\(2^{19}\\)-smooth. In targeting higher security levels, our sieve found a 376-bit prime lying between two\\(2^{21}\\)-smooth integers, a 384-bit prime lying between two\\(2^{22}\\)-smooth integers, and a 512-bit prime lying between two\\(2^{28}\\)-smooth integers. Our analysis shows that using previously known methods to find high-security instances subject to these smoothness bounds is computationally infeasible.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_10"}, {"primary_key": "2130568", "vector": [], "sparse_vector": [], "title": "Efficient Range Proofs with Transparent Setup from Bounded Integer Commitments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new approach for constructing range proofs. Our approach is modular, and leads to highly competitive range proofs under standard assumption, using less communication and (much) less computation than the state of the art methods, and without relying on a trusted setup. Our range proofs can be used as a drop-in replacement in a variety of protocols such as distributed ledgers, anonymous transaction systems, and many more, leading to significant reductions in communication and computation for these applications. At the heart of our result is a new method to transform any commitment over a finite field into a commitment scheme which allows to commit to and efficiently prove relations aboutbounded integers. Combining these new commitments with a classical approach for range proofs based on square decomposition, we obtain several new instantiations of a paradigm which was previously limited to RSA-based range proofs (with high communication and computation, and trusted setup). More specifically, we get: Under the discrete logarithm assumption, we obtain the most compact and efficient range proof among all existing candidates (with or without trusted setup). Our proofs are\\(12\\%\\)to\\(20\\%\\)shorter than the state of the art Bulletproof (<PERSON><PERSON><PERSON> et al., CRYPTO’18) for standard choices of range size and security parameter, and are more efficient (both for the prover and the verifier) by more than an order of magnitude. Under the LWE assumption, we obtain range proofs that improve over the state of the art in a batch setting when at least a few dozen range proofs are required. The amortized communication of our range proofs improves by up to two orders of magnitudes over the state of the art when the number of required range proofs grows. Eventually, under standard class group assumptions, we obtain the first concretely efficient standard integer commitment scheme (without bounds on the size of the committed integer) which does not assume trusted setup.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_9"}, {"primary_key": "2130569", "vector": [], "sparse_vector": [], "title": "Breaking the Circuit Size Barrier for Secure Computation Under Quasi-Polynomial LPN.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this work we introduce a new (circuit-dependent)homomorphic secret sharing(HSS) scheme for all\\(\\log /\\log \\log \\)-local circuits, with communication proportional only to the width of the circuit, and polynomial computation, assuming the super-polynomial hardness of learning parity with noise (\\(\\mathsf {LPN}\\)). At the heart of our new construction is apseudorandom correlation generator(PCG), which allows two partie to locally stretch, from short seeds, pseudorandom instances of an arbitrary\\(\\log /\\log \\log \\)-local additive correlation. Our main application, and the main motivation behind this work, is a generic two-party secure computation protocol for every layered (boolean or arithmetic) circuit of sizeswith total communication\\(O(s/\\log \\log s)\\)and polynomial computation, assuming the super-polynomial hardness of the standard learning parity with noise assumption (a circuit is layered if its nodes can be partitioned in layers, such that any wire connects adjacent layers). This expands the set of assumptions under which the ‘circuit size barrier’ can be broken, for a large class of circuits. The strength of the underlying assumption is tied to the sublinearity factor: we achieve communicationO(s/k(s)) under the\\(s^{2^{k(s)}}\\)-hardness of LPN, for any\\(k(s) \\le \\log \\log s / 4\\). Previously, the set of assumptions known to imply aPCGfor correlations of degree\\(\\omega (1)\\)or generic secure computation protocols with sublinear communication was restricted to LWE, DDH, and a circularly secure variant of DCR.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_29"}, {"primary_key": "2130570", "vector": [], "sparse_vector": [], "title": "Improved Linear Approximations to ARX Ciphers and Attacks Against ChaCha.", "authors": ["<PERSON><PERSON><PERSON>", "Tertuliano C. <PERSON>o"], "summary": "In this paper, we present a new technique which can be used to find better linear approximations in ARX ciphers. Using this technique, we present the first explicitly derived linear approximations for 3 and 4 rounds of ChaCha and, as a consequence, it enables us to improve the recent attacks against ChaCha\n. Additionally, we present new differentials for 3 and 3.5 rounds of ChaCha that, when combined with the proposed technique, lead to further improvement in the complexity of the Differential-Linear attacks against ChaCha.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_25"}, {"primary_key": "2130571", "vector": [], "sparse_vector": [], "title": "Decentralized Multi-authority ABE for DNFs from LWE.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We construct the first decentralized multi-authority attribute-based encryption (\\(\\mathsf {MA}\\text {-}\\mathsf {ABE}\\)) scheme for a non-trivial class of access policies whose security is based (in the random oracle model) solely on the Learning With Errors (LWE) assumption. The supported access policies are ones described by\\(\\mathsf {DNF}\\)formulas. All previous constructions of\\(\\mathsf {MA}\\text {-}\\mathsf {ABE}\\)schemes supporting any non-trivial class of access policies were proven secure (in the random oracle model) assuming various assumptions on bilinear maps. In our system, any party can become an authority and there is no requirement for any global coordination other than the creation of an initial set of common reference parameters. A party can simply act as a standardABEauthority by creating a public key and issuing private keys to different users that reflect their attributes. A user can encrypt data in terms of any\\(\\mathsf {DNF}\\)formulas over attributes issued from any chosen set of authorities. Finally, our system does not require any central authority. In terms of efficiency, when instantiating the scheme with a global boundson the size of access policies, the sizes of public keys, secret keys, and ciphertexts, all grow withs. Technically, we develop new tools for building ciphertext-policyABE(\\(\\mathsf {CP}\\text {-}\\mathsf {ABE}\\)) schemes usingLWE. Along the way, we construct the first provably secure\\(\\mathsf {CP}\\text {-}\\mathsf {ABE}\\)scheme supporting access policies in\\(\\mathsf {NC}^1\\)under theLWEassumption that avoids the generic universal-circuit-based key-policy to ciphertext-policy transformation. In particular, our construction relies on linear secret sharing schemes with new properties and in some sense is more similar to\\(\\mathsf {CP}\\text {-}\\mathsf {ABE}\\)schemes that rely on bilinear maps. While our\\(\\mathsf {CP}\\text {-}\\mathsf {ABE}\\)construction is not more efficient than existing ones, it is conceptually intriguing and further we show how to extend it to get the\\(\\mathsf {MA}\\text {-}\\mathsf {ABE}\\)scheme described above.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_7"}, {"primary_key": "2130572", "vector": [], "sparse_vector": [], "title": "The Nested Subset Differential Attack - A Practical Direct Attack Against LUOV Which Forges a Signature Within 210 Minutes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In 2017, <PERSON>.submitted Lifted Unbalanced Oil and Vinegar [3], which is a modification to the Unbalanced Oil and Vinegar Scheme by <PERSON>arin. Previously, <PERSON><PERSON> al.proposed the Subfield Differential Attack [22] which prompted a change of parameters by the authors of LUOV for the second round of the NIST post quantum standardization competition [4]. In this paper we propose a modification to the Subfield Differential Attack called the Nested Subset Differential Attack which fully breaks half of the parameter sets put forward. We also show by experimentation that this attack is practically possible to do in under 210 min for the level I security parameters and not just a theoretical attack. The Nested Subset Differential attack is a large improvement of the Subfield differential attack which can be used in real world circumstances. Moreover, we will only use what is called the “lifted” structure of LUOV, and our attack can be thought as a development of solving “lifted” quadratic systems.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_12"}, {"primary_key": "2130573", "vector": [], "sparse_vector": [], "title": "Cryptanalytic Applications of the Polynomial Method for Solving Multivariate Equation Systems over GF(2).", "authors": ["<PERSON><PERSON>"], "summary": "At SODA 2017 <PERSON><PERSON><PERSON><PERSON> et al. presented the first worst-case algorithms with exponential speedup over exhaustive search for solving polynomial equation systems of degreedinnvariables over finite fields. These algorithms were based on the polynomial method in circuit complexity which is a technique for proving circuit lower bounds that has recently been applied in algorithm design. Subsequent works further improved the asymptotic complexity of polynomial method-based algorithms for solving equations over the field\\(\\mathbb {F}_2\\). However, the asymptotic complexity formulas of these algorithms hide significant low-order terms, and hence they outperform exhaustive search only for very large values ofn. In this paper, we devise a concretely efficient polynomial method-based algorithm for solving multivariate equation systems over\\(\\mathbb {F}_2\\). We analyze our algorithm’s performance for solving random equation systems, and bound its complexity by about\\(n^2 \\cdot 2^{0.815n}\\)bit operations for\\(d = 2\\)and\\(n^2 \\cdot 2^{\\left( 1 - 1/2.7d\\right) n}\\)for any\\(d \\ge 2\\). We apply our algorithm in cryptanalysis of recently proposed instances of the Picnic signature scheme (an alternate third-round candidate in NIST’s post-quantum standardization project) that are based on the security of the LowMC block cipher. Consequently, we show that 2 out of 3 new instances do not achieve their claimed security level. As a secondary application, we also improve the best-known preimage attacks on several round-reduced variants of the Keccak hash function. Our algorithm combines various techniques used in previous polynomial method-based algorithms with new optimizations, some of which exploit randomness assumptions about the system of equations. In its cryptanalytic application to Picnic, we demonstrate how to further optimize the algorithm for solving structured equation systems that are constructed from specific cryptosystems.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_14"}, {"primary_key": "2130574", "vector": [], "sparse_vector": [], "title": "Ciminion: Symmetric Encryption Based on Toffoli-Gates over Large Finite Fields.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by new applications such as secure Multi-Party Computation (MPC), Fully Homomorphic Encryption (FHE), and Zero-Knowledge proofs (ZK), the need for symmetric encryption schemes that minimize the number of field multiplications in their natural algorithmic description is apparent. This development has brought forward many dedicated symmetric encryption schemes that minimize the number of multiplications in\\( \\mathbb {F}_{2^n} \\)or\\( \\mathbb {F}_{p} \\), withpbeing prime. These novel schemes have lead to new cryptanalytic insights that have broken many of said schemes. Interestingly, to the best of our knowledge, all of the newly proposed schemes that minimize the number of multiplications use those multiplications exclusively in S-boxes based on a power mapping that is typically\\(x^3\\)or\\(x^{-1}\\). Furthermore, most of those schemes rely on complex and resource-intensive linear layers to achieve a low multiplication count. In this paper, we presentCiminion, an encryption scheme minimizing the number of field multiplications in large binary or prime fields, while using a very lightweight linear layer. In contrast to other schemes that aim to minimize field multiplications in\\( \\mathbb {F}_{2^n} \\)or\\( \\mathbb {F}_{p} \\),Ciminionrelies on the Toffoli gate to improve the non-linear diffusion of the overall design. In addition, we have tailored the primitive for the use in a Farfalle-like construction in order to minimize the number of rounds of the used primitive, and hence, the number of field multiplications as far as possible.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_1"}, {"primary_key": "2130575", "vector": [], "sparse_vector": [], "title": "Leakage Resilient Value Comparison with Application to Message Authentication.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Side-channel attacks are a threat to secrets stored on a device, especially if an adversary has physical access to the device. As an effect of this, countermeasures against such attacks for cryptographic algorithms are a well-researched topic. In this work, we deviate from the study of cryptographic algorithms and instead focus on the side-channel protection of a much more basic operation, the comparison of a known attacker-controlled value with a secret one. Comparisons sensitive to side-channel leakage occur in tag comparisons during the verification of message authentication codes (MACs) or authenticated encryption, but are typically omitted in security analyses. Besides, also comparisons performed as part of fault countermeasures might be sensitive to side-channel attacks. In this work, we present a formal analysis on comparing values in a leakage resilient manner by utilizing cryptographic building blocks that are typically part of an implementation anyway. Our results indicate that there is no need to invest additional resources into implementing a protected comparison operation itself if a sufficiently protected implementation of a public cryptographic permutation, or a (tweakable) block cipher, is already available. We complement our contribution by applying our findings to the SuKS message authentication code used by lightweight authenticated encryption scheme ISAP, and to the classical Hash-then-PRF construction.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_13"}, {"primary_key": "2130576", "vector": [], "sparse_vector": [], "title": "Advanced <PERSON><PERSON><PERSON> Sieving on GPUs, with Tensor Cores.", "authors": ["Léo Du<PERSON>", "<PERSON>", "<PERSON><PERSON> P<PERSON>"], "summary": "In this work, we study GPU implementations of various state-of-the-art sieving algorithms for lattices (Becker-Gama-Jo<PERSON> 2015, Becker-Ducas-Gama-Laarhoven 2016, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 2017) inside the General Sieve Kernel (G6K, Albrechtet al.2019). In particular, we extensively exploit the recently introduced Tensor Cores – originally designed for raytracing and machine learning – and demonstrate their fitness for the cryptanalytic task at hand. We also propose a newdual-hashtechnique for efficient detection of ‘lift-worthy’ pairs to accelerate a key ingredient of G6K: finding short lifted vectors. We obtain new computational records, reaching dimension 180 for the SVP Darmstadt Challenge improving upon the previous record for dimension 155. This computation ran for 51.6 days on a server with 4 NVIDIA Turing GPUs and 1.5TB of RAM. This corresponds to a gain of about two orders of magnitude over previous records both in terms of wall-clock time and of energy efficiency.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_9"}, {"primary_key": "2130577", "vector": [], "sparse_vector": [], "title": "Alibi: A Flaw in Cuckoo-Hashing Based Hierarchical ORAM Schemes and a Solution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There once was a table of hashes That held extra items in stashes It all seemed like bliss But things went amiss When the stashes were stored in the caches The first Oblivious RAM protocols introduced the “hierarchical solution,” (STOC ’90) where the server stores a series of hash tables of geometrically increasing capacities. Each ORAM query would read a small number of locations from each level of the hierarchy, and each level of the hierarchy would be reshuffled and rebuilt at geometrically increasing intervals to ensure that no single query was ever repeated twice at the same level. This yielded an ORAM protocol with polylogarithmic overhead. Future works extended and improved the hierarchical solution, replacing traditional hashing with cuckoo hashing (ICALP ’11) and cuckoo hashing with a combined stash (<PERSON><PERSON> et al. SODA ’12). In this work, we identify a subtle flaw in the protocol of <PERSON><PERSON> et al. (SODA ’12) that uses cuckoo hashing with a stash in the hierarchical ORAM solution. We give a concrete distinguishing attack against this type of hierarchical ORAM that uses cuckoo hashing with acombinedstash. This security flaw has propagated to at least 5 subsequent hierarchical ORAM protocols, including the recent optimal ORAM scheme, OptORAMa (Eurocrypt ’20). In addition to our attack, we identify a simple fix that does not increase the asymptotic complexity. We note, however, that our attack only affects more recenthierarchical ORAMs, but does not affect the early protocols that predate the use of cuckoo hashing, or other types of ORAM solutions (e.g. Path ORAM or Circuit ORAM).", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_12"}, {"primary_key": "2130578", "vector": [], "sparse_vector": [], "title": "Password Hashing and Preprocessing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "How does the cryptanalytic effort needed to compromisetout ofminstances of hashed passwords scale with the number of users when arbitrary preprocessing information on the hash function is available? We provide a formal treatment of this problem in themulti-instance setting with auxiliary information. A central contribution of our work is an (arguably simple) transcript-counting argument that allows us to resolve a fundamental question left open by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> (<PERSON><PERSON>; CRYPTO 2012) in multi-instance security. We leverage this proof technique to formally justify unrecoverability of hashed salted passwords in the presence of auxiliary information in the random-oracle model. To this end we utilize the recent pre-sampling techniques for dealing with auxiliary information developed by <PERSON> et al. (CRYPTO 2018). Our bounds closely match those commonly assumed in practice. Besides hashing of passwords through a monolithic random oracle, we consider the effect of iteration, a technique that is used in classical mechanisms, such as bcrypt and PBKDF2, to slow down the rate of guessing. Building on the work of BRT, we formulate a notion of KDF security, also in the presence of auxiliary information, and prove an appropriate composition theorem for it.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_3"}, {"primary_key": "2130579", "vector": [], "sparse_vector": [], "title": "Generic Compiler for Publicly Verifiable Covert Multi-Party Computation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Covert security has been introduced as a compromise between semi-honest and malicious security. In a nutshell, covert security guarantees that malicious behavior can be detected by the honest parties with some probability, but in case detection fails all bets are off. While the security guarantee offered by covert security is weaker than full-fledged malicious security, it comes with significantly improved efficiency. An important extension of covert security introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (ASIACRYPT’12) ispublic verifiability, which allows the honest parties to create a publicly verifiable certificate of malicious behavior. Public verifiability significantly strengthen covert security as the certificate allows punishment via an external party, e.g., a judge. Most previous work on publicly verifiable covert (PVC) security focuses on the two-party case, and the multi-party case has mostly been neglected. In this work, we introduce a novel compiler for multi-party PVC secure protocols with no private inputs. The class of supported protocols includes the preprocessing of common multi-party computation protocols that are designed in the offline-online model. Our compiler leverages time-lock encryption to offer high probability of cheating detection (often also called deterrence factor) independent of the number of involved parties. Moreover, in contrast to the only earlier work that studies PVC in the multi-party setting (CRYPTO’20), we provide the first full formal security analysis.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_27"}, {"primary_key": "2130580", "vector": [], "sparse_vector": [], "title": "Robust Property-Preserving Hash Functions for Hamming Distance and More.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Robust property-preserving hash (PPH) functions, recently introduced by <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [ITCS 2019], compress large inputsxandyinto short digestsh(x) andh(y) in a manner that allows for computing a predicatePonxandywhile only having access to the corresponding hash values. In contrast to locality-sensitive hash functions, a robust PPH function guarantees to correctly evaluate a predicate onh(x) andh(y) even ifxa<PERSON><PERSON> chosen adversariallyafterseeingh. Our main result is a robust PPH function for the exact hamming distance predicate whered(x,y) is the hamming-distance betweenxandy. Our PPH function compressesn-bit strings into\\(\\mathcal {O}(t \\lambda )\\)-bit digests, where\\(\\lambda \\)is the security parameter. The construction is based on the q-strong bilinear discrete logarithm assumption. Along the way, we construct a robust PPH function for the set intersection predicate which compresses setsXandYof sizenwith elements from some arbitrary universeUinto\\(\\mathcal {O}(t\\lambda )\\)-bit long digests. This PPH function may be of independent interest. We present an almost matching lower bound of\\(\\varOmega (t \\log t)\\)on the digest size of any PPH function for the intersection predicate, which indicates that our compression rate is close to optimal. Finally, we also show how to extend our PPH function for the intersection predicate to more than two inputs.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_11"}, {"primary_key": "2130581", "vector": [], "sparse_vector": [], "title": "Black-Box Non-interactive Non-malleable Commitments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "There has been recent exciting progress on building non-interactive non-malleable commitments from judicious assumptions. All proposed approaches proceed in two steps. First, obtain simple “base” commitment schemes for very small tag/identity spaces based on a various sub-exponential hardness assumptions. Next, assuming sub-exponential non-interactive witness indistinguishable proofs (NIWIs), and variants of keyless collision resistant hash functions, construct non-interactive compilers that convert tag-based non-malleable commitments for a small tag space into tag-based non-malleable commitments for a larger tag space. We propose the first black-box construction of non-interactive non-malleable commitments. Our key technical contribution is a novel implementation of the non-interactive proof of consistency required for tag amplification. Prior to our work, the only known approach to tag amplification without setup and with black-box use of the base scheme (<PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>, FOCS 2012) added multiple rounds of interaction. Our construction satisfies the strongest known definition of non-malleability, i.e., CCA (chosen commitment attack) security. In addition to being black-box, our approach dispenses with the need for sub-exponential NIWIs, that was common to all prior work. Instead of NIWIs, we rely on sub-exponential hinting PRGs which can be obtained based on a broad set of assumptions such as sub-exponential CDH or LWE.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_6"}, {"primary_key": "2130582", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation from Simple-to-State Hard Problems: New Assumptions, New Techniques, and Simplification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we study the question of what set of simple-to-state assumptions suffice for constructing functional encryption and indistinguishability obfuscation (\\(i\\mathcal {O}\\)), supporting all functions describable by polynomial-size circuits. Our work improves over the state-of-the-art work of <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> (Eurocrypt 2019) in multiple dimensions. New Assumption:Previous to our work, all constructions of\\(i\\mathcal {O}\\)from simple assumptions required novel pseudorandomness generators involving LWE samples and constant-degree polynomials over the integers, evaluated on the error of the LWE samples. In contrast, Boolean pseudorandom generators (PRGs) computable by constant-degree polynomials have been extensively studied since the work of <PERSON><PERSON><PERSON> (2000). (<PERSON><PERSON>ich and follow-up works study Boolean pseudorandom generators with constant-locality, which can be computed by constant-degree polynomials.) We show how to replace the novel pseudorandom objects over the integers used in previous works, with appropriate Boolean pseudorandom generators with sufficient stretch, when combined with LWE with binary error over suitable parameters. Both binary error LWE and constant degree Goldreich PRGs have been a subject of extensive cryptanalysis since much before our work and thus we back the plausibility of our assumption with security against algorithms studied in context of cryptanalysis of these objects. New Techniques:we introduce a number of new techniques: – We show how to build partially-hidingpublic-keyfunctional encryption, supporting degree-2 functions in the secret part of the message, and arithmetic\\(\\mathsf {NC^1}\\)functions over the public part of the message, assuming only standard assumptions over asymmetric pairing groups. – We construct single-ciphertext secret-key functional encryption for all circuits withlinearkey generation, assuming only the LWE assumption. Simplification:Unlike prior works, our new techniques furthermore let us constructpublic-keyfunctional encryption for polynomial-sized circuits directly (without invoking any bootstrapping theorem, nor transformation from secret-key to public key FE), and based only on thepolynomial hardnessof underlying assumptions. The functional encryption scheme satisfies a strong notion of efficiency where the size of the ciphertext grows only sublinearly in the output size of the circuit and not its size. Finally, assuming that the underlying assumptions are subexponentially hard, we can bootstrap this construction to achieve\\(i\\mathcal {O}\\).", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_4"}, {"primary_key": "2130583", "vector": [], "sparse_vector": [], "title": "Structured Encryption and Dynamic Leakage Suppression.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Structured encryption (STE) schemes encrypt data structures in such a way that they can be privately queried. Special cases of STE include searchable symmetric encryption (SSE) and graph encryption. Like all sub-linear encrypted search solutions, STE leaks information about queries against persistent adversaries. To address this, a line of work onleakage suppressionwas recently initiated that focuses on techniques to mitigate the leakage of STE schemes. A notable example is the query equality suppression framework (<PERSON><PERSON> et al.CRYPTO’18) which transforms dynamic STE schemes that leak the query equality into new schemes that do not. Unfortunately, this framework can only produce static schemes and it was left as an open problem to design a solution that could yield dynamic constructions. In this work, we propose a dynamic query equality suppression framework that transforms volume-hiding semi-dynamic or mutable STE schemes that leak the query equality into newfully-dynamicconstructions that do not. We then use our framework to design three new fully-dynamic STE schemes that are “almost\" and fully zero-leakage which, under natural assumptions on the data and query distributions, are asymptotically more efficient than using black-box ORAM simulation. These are the first constructions of their kind.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_13"}, {"primary_key": "2130584", "vector": [], "sparse_vector": [], "title": "On the Power of Multiple Anonymous Messages: Frequency Estimation and Selection in the Shuffle Model of Differential Privacy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It is well-known that general secure multi-party computation can in principle be applied to implement differentially private mechanisms over distributed data with utility matching the curator (a.k.a. central) model. In this paper we study the power of protocols running on top of a much weaker primitive: A non-interactive anonymous channel, known as theshufflemodel in the differential privacy literature. Such protocols are implementable in a scalable way using known cryptographic methods and are known to enable non-interactive, differentially private protocols with error much smaller than what is possible in the local model. We study fundamental counting problems in the shuffle model and obtain tight, up to polylogarithmic factors, bounds on the error and communication in several settings. For the classic problem offrequency estimationfornusers and a domain of sizeB, we obtain: A nearly tight lower bound of\\(\\tilde{\\varOmega }( \\min (\\root 4 \\of {n}, \\sqrt{B}))\\)on the\\(\\ell _\\infty \\)error in thesingle-messageshuffle model. This implies that the protocols obtained from the amplification via shuffling work of <PERSON><PERSON><PERSON><PERSON> et al. (SODA 2019) and <PERSON><PERSON> et al. (Crypto 2019) are nearly optimal for single-message protocols. Protocols in themulti-messageshuffle model with\\(\\mathrm {poly}(\\log {B}, \\log {n})\\)bits of communication per user and\\(\\ell _\\infty \\)error at most\\(\\mathrm {poly}(\\log B, \\log n)\\), which provide an exponential improvement on the error compared to what is possible with single-message algorithms. This implies protocols with similar error and communication guarantees for several well-studied problems such as heavy hitters,d-dimensional range counting, M-estimation of the median and quantiles, and more generally sparse non-adaptive statistical query algorithms. For theselectionproblem on a domain of size\\(B\\), we prove: A nearly tight lower bound of\\(\\varOmega (B)\\)on the number of users in the single-message shuffle model. This significantly improves on the\\(\\varOmega (B^{1/17})\\)lower bound obtained by Cheu et al. (Eurocrypt 2019). A key ingredient in our lower bound proofs is a lower bound on the error oflocally-private frequency estimation in the low-privacy (a.k.a. high\\(\\varepsilon \\)) regime. For this we develop new tools to improve the results of Duchi et al. (FOCS 2013; JASA 2018) and Bassily & Smith (STOC 2015), whose techniques only gave tight bounds in the high-privacy setting.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_16"}, {"primary_key": "2130585", "vector": [], "sparse_vector": [], "title": "The More the Merrier: Reducing the Cost of Large Scale MPC.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Secure multi-party computation (MPC) allows multiple parties to perform secure joint computations on their private inputs. Today, applications for MPC are growing with thousands of parties wishing to build federated machine learning models or trusted setups for blockchains. To address such scenarios we propose a suite of novel MPC protocols that maximize throughput when run with large numbers of parties. In particular, our protocols have both communication and computation complexity that decrease with the number of parties. Our protocols buildon prior protocolsbased on packed secret-sharing, introducing new techniques to build more efficient computation for general circuits. Specifically, we introduce a new approach for handlinglinear attacksthat arise in protocols using packed secret-sharing and we propose a method for unpacking shared multiplication triples without increasing the asymptotic costs. Compared with prior work, we avoid the\\(\\log |C|\\)overhead required when generically compiling circuits of size |C| for use in a SIMD computation, and we improve over folklore “committee-based” solutions by a factor ofO(s), the statistical security parameter. In practice, our protocol is up to 10Xfaster than any known construction, under a reasonable set of parameters.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_24"}, {"primary_key": "2130586", "vector": [], "sparse_vector": [], "title": "Multi-source Non-malleable Extractors and Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a natural generalization of two-source non-malleable extractors (<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, TCC 2014) called asmulti-source non-malleable extractors. Multi-source non-malleable extractors are special independent source extractors which satisfy an additional non-malleability property. This property requires that the output of the extractor remains close to uniform even conditioned on its output generated by tamperingseveral sources together. We formally define this primitive, give a construction that is secure against a wide class of tampering functions, and provide applications. More specifically, we obtain the following results: For any\\(s \\ge 2\\), we give an explicit construction of as-source non-malleable extractor for min-entropy\\(\\varOmega (n)\\)and error\\(2^{-n^{\\varOmega (1)}}\\)in theoverlapping joint tampering model. This means that each tampered source could depend on any strict subset of all the sources and the sets corresponding to each tampered source could be overlapping in a way that we define. Prior to our work, there were no known explicit constructions that were secure even against disjoint tampering (where the sets are required to be disjoint without any overlap). We adapt the techniques used in the above construction to give at-out-of-nnon-malleable secret sharing scheme (<PERSON><PERSON> and <PERSON>, STOC 2018) for any\\(t \\le n\\)in thedisjoint tampering model. This is the first general construction of a threshold non-malleable secret sharing (NMSS) scheme in the disjoint tampering model. All prior constructions had a restriction that the size of the tampered subsets could not be equal. We further adapt the techniques used in the above construction to give at-out-of-nnon-malleable secret sharing scheme (Goyal and Kumar, STOC 2018) for any\\(t \\le n\\)in theoverlapping joint tampering model. This is the first construction of a threshold NMSS in the overlapping joint tampering model. We show that a stronger notion ofs-source non-malleable extractor that is multi-tamperable against disjoint tampering functions gives a single round network extractor protocol (Kalai et al., FOCS 2008) with attractive features. Plugging in with a new construction of multi-tamperable, 2-source non-malleable extractors provided in our work, we get a network extractor protocol for min-entropy\\(\\varOmega (n)\\)that tolerates anoptimumnumber (\\(t = p-2\\)) of faulty processors and extracts random bits foreveryhonest processor. The prior network extractor protocols could only tolerate\\(t = \\varOmega (p)\\)faulty processors and failed to extract uniform random bits for a fraction of the honest processors.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_16"}, {"primary_key": "2130587", "vector": [], "sparse_vector": [], "title": "Oblivious Transfer Is in MiniQCrypt.", "authors": ["Alex <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "MiniQCrypt is a world where quantum-secure one-way functions exist, and quantum communication is possible. We construct an oblivious transfer (OT) protocol in MiniQCrypt that achieves simulation-security in the plain model against malicious quantum polynomial-time adversaries, building on the foundational work of <PERSON><PERSON><PERSON><PERSON> and <PERSON> (FOCS 1988) and <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> (CRYPTO 1991). Combining the OT protocol with prior works, we obtain secure two-party and multi-party computation protocols also in MiniQCrypt. This is in contrast to the classical world, where it is widely believed that one-way functions alone do not give us OT. In the common random string model, we achieve aconstant-rounduniversally composable (UC) OT protocol.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_18"}, {"primary_key": "2130588", "vector": [], "sparse_vector": [], "title": "Aggregatable Distributed Key Generation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we introduce a distributed key generation (DKG) protocol with aggregatable and publicly-verifiable transcripts. Compared with priorpublicly-verifiableapproaches, our DKG reduces the size of the final transcript and the time to verify it from\\(\\mathcal {O}(n^2)\\)to\\(\\mathcal {O}(n \\log {n})\\), wherendenotes the number of parties. As compared with prior non-publicly-verifiable approaches, our DKG leveragesgossiprather thanall-to-all communicationto reduce verification and communication complexity. We also revisit existing DKG security definitions, which are quite strong, and propose new and natural relaxations. As a result, we can prove the security of our aggregatable DKG as well as that of several existing DKGs, including the popular Pedersen variant. We show that, under these new definitions, these existing DKGs can be used to yield secure threshold variants of popular cryptosystems such as El-Gamal encryption and BLS signatures. We also prove that our DKG can be securely combined with a new efficient verifiable unpredictable function (VUF), whose security we prove in the random oracle model. Finally, we experimentally evaluate our DKG and show that the per-party overheads scale linearly and are practical. For 64 parties, it takes 71 ms to share and 359 ms to verify the overall transcript, while for 8192 parties, it takes 8 s and 42.2 s respectively.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_6"}, {"primary_key": "2130589", "vector": [], "sparse_vector": [], "title": "sf LogStack: Stacked Garbling with O(b log b) Computation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Secure two party computation (2PC) of arbitrary programs can be efficiently achieved using garbled circuits (GC). Until recently, it was widely believed that a GC proportional to the entire program, including parts of the program that are entirely discarded due to conditional branching, must be transmitted over a network. Recent work shows that this belief isfalse, and that communication proportional only to the longest program execution path suffices (<PERSON> and <PERSON>nikov, CRYPTO 20, [HK20a]). Although this recent work reduces needed communication, itincreasescomputation. For a conditional withbbranches, the players use\\(O(b^2)\\)computation (traditional GC uses onlyO(b)). Our scheme\\(\\textsf {LogStack}\\)reduces stacked garbling computation from\\(O(b^2)\\)to\\(O(b \\log b)\\)withnoincrease in communication over [HK20a]. The cause of [HK20a]’s increased computation is the oblivious collection ofgarbage labelsthat emerge during the evaluation of inactive branches. Garbage is collected by amultiplexerthat is costly to generate. At a high level, we redesign stacking and garbage collection to avoid quadratic scaling. Our construction is also morespace efficient: [HK20a] algorithms requireO(b) space, while ours use only\\(O(\\log b)\\)space. This space efficiency allows even modest setups to handle large numbers of branches. [HK20a] assumes a random oracle (RO). We track the source of this need, formalize a simple and natural added assumption on the base garbling scheme, and remove reliance on RO:\\(\\textsf {LogStack}\\)is secure in the standard model. Nevertheless,\\(\\textsf {LogStack}\\)can be instantiated with typical GC tricks based on non-standard assumptions, such as free XOR and half-gates, and hence can be implemented with high efficiency. We implemented\\(\\textsf {LogStack}\\)(in the RO model, based on half-gates garbling) and report performance. In terms of wall-clock time and for fewer than 16 branches, our performance is comparable to [HK20a]’s; for larger branching factors, our approach clearly outperforms [HK20a]. For example, given 1024 branches, our approach is\\(31{\\times }\\)faster.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_1"}, {"primary_key": "2130590", "vector": [], "sparse_vector": [], "title": "Tightly-Secure Authenticated Key Exchange, Revisited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce new tightly-secure authenticated key exchange (AKE) protocols that are extremely efficient, yet have only aconstantsecurity loss and can be instantiated in the random oracle model both from the standard DDH assumption and a subgroup assumption over RSA groups. These protocols can be deployed with optimal parameters, independent of the number of users or sessions, without the need to compensate a security loss with increased parameters and thus decreased computational efficiency. We use the standard “Single-Bit-Guess” AKE security (with forward secrecy and state corruption) requiring all challenge keys to be simultaneously pseudo-random. In contrast, most previous papers on tightly secure AKE protocols (<PERSON> et al., TCC 2015; Gjøsteen and Jager, CRYPTO 2018; <PERSON> et al., ASIACRYPT 2020) concentrated on a non-standard “Multi-Bit-Guess” AKE security which is known not to compose tightly with symmetric primitives to build a secure communication channel. Our key technical contribution is a new generic approach to construct tightly-secure AKE protocols based on non-committing key encapsulation mechanisms. The resulting DDH-based protocols are considerably more efficient than all previous constructions.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_5"}, {"primary_key": "2130591", "vector": [], "sparse_vector": [], "title": "Public-Coin Statistical Zero-Knowledge Batch Verification Against Malicious Verifiers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Suppose that a problem\\(\\varPi \\)has a statistical zero-knowledge (\\(\\mathsf {SZK}\\)) proof with communication complexitym. The question of batch verification for\\(\\mathsf {SZK}\\)asks whether one can prove thatkinstances\\(x_1,\\dots ,x_k\\)all belong to\\(\\varPi \\)with a statistical zero-knowledge proof whose communication complexity is better than\\(k \\cdot m\\)(which is the complexity of the trivial solution of executing the original protocol independently on each input). In a recent work, <PERSON><PERSON><PERSON><PERSON> al.(TCC, 2020) constructed such a batch verification protocol for any problem having anon-interactive\\(\\mathsf {SZK}\\)(\\(\\mathsf {NISZK}\\)) proof-system. Two drawbacks of their result are that their protocol is private-coin and is only zero-knowledge with respect to the honest verifier. In this work, we eliminate these two drawbacks by constructing a public-coin malicious-verifier\\(\\mathsf {SZK}\\)protocol for batch verification of\\(\\mathsf {NISZK}\\). Similarly to the aforementioned prior work, the communication complexity of our protocol is\\(\\big (k+{\\mathsf {poly}}(m) \\big ) \\cdot \\mathrm {polylog}(k,m)\\).", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_8"}, {"primary_key": "2130592", "vector": [], "sparse_vector": [], "title": "Round-Optimal Blind Signatures in the Plain Model from Classical and Quantum Standard Assumptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Blind signatures, introduced by <PERSON><PERSON> (Crypto’82), allows a user to obtain a signature on a message without revealing the message itself to the signer. Thus far, all existing constructions of round-optimal blind signatures are known to require one of the following: a trusted setup, an interactive assumption, or complexity leveraging. This state-of-the-affair is somewhat justified by the few known impossibility results on constructions of round-optimal blind signatures in the plain model (i.e., without trusted setup) from standard assumptions. However, since all of these impossibility results only holdunder some conditions, fully (dis)proving the existence of such round-optimal blind signatures has remained open. In this work, we provide an affirmative answer to this problem and construct the first round-optimal blind signature scheme in the plain model from standard polynomial-time assumptions. Our construction is based on various standard cryptographic primitives and also on new primitives that we introduce in this work, all of which are instantiable fromclassical and post-quantumstandard polynomial-time assumptions. The main building block of our scheme is a new primitive called a blind-signature-conforming zero-knowledge (ZK) argument system. The distinguishing feature is that the ZK property holds by using a quantum polynomial-time simulator against non-uniform classical polynomial-time adversaries. Syntactically one can view this as a delayed-input three-move ZK argument with a reusable first message, and we believe it would be of independent interest.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_15"}, {"primary_key": "2130593", "vector": [], "sparse_vector": [], "title": "Mind the Middle Layer: The HADES Design Strategy Revisited.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The HADES design strategy combines the classical SPN construction with the Partial SPN (PSPN) construction, in which at every encryption round, the non-linear layer is applied to only a part of the state. In a HADES design, a middle layer that consists of PSPN rounds is surrounded by outer layers of SPN rounds. The security arguments of HADES with respect to statistical attacks use only the SPN rounds, disregarding the PSPN rounds. This allows the designers to not pose any restriction on the MDS matrix used as the linear mixing operation. In this paper we show that the choice of the MDS matrix significantly affects the security level provided by HADES designs. If the MDS is chosen properly, then the security level of the scheme against differential and linear attacks is significantly higher than claimed by the designers. On the other hand, weaker choices of the MDS allow for extremely large invariant subspaces that pass the entire middle layer without activating any non-linear operation (a.k.a. S-box). We showcase our results on the Starkad and Poseidon instantiations of HADES. For Poseidon, we significantly improve the lower bounds on the number of active S-boxes with respect to both differential and linear cryptanalysis provided by the designers – for example, from 28 to 60 active S-boxes for the\\(t=6\\)variant. For Starkad, we show that for any variant witht(i.e., the number of S-boxes in each round) divisible by 4, the cipher admits a huge invariant subspace that passesany numberof PSPN rounds without activating any S-box (e.g., a subspace of size\\(2^{1134}\\)for the\\(t=24\\)variant). Furthermore, for various choices of the parameters, this invariant subspace can be used to mount a preimage attack on the hash function that breakes its security claims. On the other hand, we show that the problem can be fixed easily by replacingtwith any value that is not divisible by four. Following our paper, the designers of Starkad and Poseidon amended their design, by adding requirements which ensure that the MDS matrix is chosen properly.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_2"}, {"primary_key": "2130594", "vector": [], "sparse_vector": [], "title": "Non-interactive Distributional Indistinguishability (NIDI) and Non-malleable Commitments.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We introducenon-interactive distributionally indistinguishable arguments(NIDI) to address a significant weakness of NIWI proofs: namely, the lack of meaningful secrecy when proving statements about\\(\\mathsf {NP}\\)languages with unique witnesses. NIDI arguments allow a prover\\({\\mathcal P} \\)to send a single message to verifier\\({\\mathcal V} \\), from which\\({\\mathcal V} \\)obtains a sampledfrom a (secret) distribution\\({\\mathcal D} \\), together with a proof of membership ofdin an NP language\\({\\mathcal L} \\). The soundness guarantee is that if the sampledobtained by the verifier\\({\\mathcal V} \\)is not in\\({\\mathcal L} \\), then\\({\\mathcal V} \\)outputs\\(\\bot \\). The privacy guarantee is that secrets about the distribution remain hidden: for every pair of (sufficiently) hard-to-distinguish distributions\\({\\mathcal D} _0\\)and\\({\\mathcal D} _1\\)with support in NP language\\({\\mathcal L} \\), a NIDI that outputs samples from\\({\\mathcal D} _0\\)with proofs of membership in\\({\\mathcal L} \\)is indistinguishable from one that outputs samples from\\({\\mathcal D} _1\\)with proofs of membership in\\({\\mathcal L} \\). We build NIDI arguments for superpolynomially hard-to-distinguish distributions, assuming sub-exponential indistinguishability obfuscation and sub-exponentially secure (variants of) one-way functions. We demonstrate preliminary applications of NIDI and of our techniques to obtaining the first (relaxed) non-interactive constructions in the plain model, from well-founded assumptions, of: Commit-and-prove that provably hides the committed message CCA-secure commitments against non-uniform adversaries. The commit phase of our commitment schemes consists of a single message from the committer to the receiver, followed by a randomized output by the receiver (that need not be returned to the committer).", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_7"}, {"primary_key": "2130595", "vector": [], "sparse_vector": [], "title": "One-Way Functions and Malleability Oracles: Hidden Shift Attacks on Isogeny-Based Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Supersingular isogeny Di<PERSON><PERSON>-<PERSON> key exchange (SIDH) is a post-quantum protocol based on the presumed hardness of computing an isogeny between two supersingular elliptic curves given some additional torsion point information. Unlike other isogeny-based protocols, SIDH has been widely believed to be immune to subexponential quantum attacks because of the non-commutative structure of the endomorphism rings of supersingular curves. We contradict this commonly believed misconception in this paper. More precisely, we highlight the existence of an abelian group action on the SIDH key space, and we show that for sufficientlyunbalancedandoverstretchedSIDH parameters, this action can be efficiently computed (heuristically) using the torsion point information revealed in the protocol. This reduces the underlying hardness assumption to a hidden shift problem instance which can be solved in quantum subexponential time. We formulate our attack in a new framework allowing the inversion of one-way functions in quantum subexponential time provided a malleability oracle with respect to some commutative group action. This framework unifies our new attack with earlier subexponential quantum attacks on isogeny-based protocols, and it may be of further interest for cryptanalysis.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_9"}, {"primary_key": "2130596", "vector": [], "sparse_vector": [], "title": "Compact, Efficient and UC-Secure Isogeny-Based Oblivious Transfer.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Guilhem"], "summary": "Oblivious transfer (OT) is an essential cryptographic tool that can serve as a building block for almost all secure multiparty functionalities. The strongest security notion against malicious adversaries is universal composability (UC-secure). An important goal is to have post-quantum OT protocols. One area of interest for post-quantum cryptography is isogeny-based crypto. Isogeny-based cryptography has some similarities to <PERSON><PERSON><PERSON><PERSON><PERSON>, but lacks some algebraic properties that are needed for discrete-log-based OT protocols. Hence it is not always possible to directly adapt existing protocols to the isogeny setting. We propose the first practical isogeny-based UC-secure oblivious transfer protocol in the presence of malicious adversaries. Our scheme uses the CSIDH framework and does not have an analogue in the <PERSON><PERSON><PERSON><PERSON> setting. The scheme consists of a constant number of isogeny computations. The underlying computational assumption is a problem that we call the computational reciprocal CSIDH problem, and that we prove polynomial-time equivalent to the computational CSIDH problem.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_8"}, {"primary_key": "2130597", "vector": [], "sparse_vector": [], "title": "New Lattice Two-Stage Sampling Technique and Its Applications to Functional Encryption - Stronger Security and Smaller Ciphertexts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This work proposes a new lattice two-stage sampling technique, generalizing the prior two-stage sampling method of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (STOC ’08). By using our new technique as a key building block, we can significantly improve security and efficiency of the current state of the arts of simulation-based functional encryption. Particularly, our functional encryption achieves\\((Q,\\mathsf {poly})\\)simulation-based semi-adaptive security that allows arbitrary pre- and post-challenge key queries, and has succinct ciphertexts with only an additiveO(Q) overhead. Additionally, our two-stage sampling technique can derive new feasibilities of indistinguishability-based adaptively-secure\\(\\mathsf {IB} \\)-\\(\\mathsf {FE} \\)for inner products and semi-adaptively-secure\\(\\mathsf {AB} \\)-\\(\\mathsf {FE} \\)for inner products, breaking several technical limitations of the recent work by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (Asiacrypt ’20).", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_18"}, {"primary_key": "2130598", "vector": [], "sparse_vector": [], "title": "High-Precision Bootstrapping of RNS-CKKS Homomorphic Encryption Using Optimal Minimax Polynomial Approximation and Inverse Sine Function.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Approximate homomorphic encryption with the residue number system (RNS), called RNS-variant <PERSON><PERSON><PERSON><PERSON> (RNS-CKKS) scheme [12,13], is a fully homomorphic encryption scheme that supports arithmetic operations for real or complex number data encrypted. Although the RNS-CKKS scheme is a fully homomorphic encryption scheme, most of the applications with the RNS-CKKS scheme use it as the only leveled homomorphic encryption scheme because of the lack of the practicality of the bootstrapping operation of the RNS-CKKS scheme. One of the crucial problems of the bootstrapping operation is its poor precision. While other basic homomorphic operations ensure sufficiently high precision for practical use, the bootstrapping operation only supports about 20-bit fixed-point precision at best, which is not high precision enough to be used for the reliable large-depth homomorphic computations until now. In this paper, we improve the message precision in the bootstrapping operation of the RNS-CKKS scheme. Since the homomorphic modular reduction process is one of the most important steps in determining the precision of the bootstrapping, we focus on the homomorphic modular reduction process. Firstly, we propose a fast algorithm of obtaining the optimal minimax approximate polynomial of modular reduction function and the scaled sine/cosine function over the union of the approximation regions, called an improved multi-interval Remez algorithm. In fact, this algorithm derives the optimal minimax approximate polynomial of any continuous functions over any union of the finite number of intervals. Next, we propose the composite function method using the inverse sine function to reduce the difference between the scaling factor used in the bootstrapping and the default scaling factor. With these methods, we reduce the approximation error in the bootstrapping of the RNS-CKKS scheme by 1/1176–1/42 (5.4–10.2-bit precision improvement) for each parameter setting. While the bootstrapping without the composite function method has 27.2–30.3-bit precision at maximum, the bootstrapping with the composite function method has 32.6–40.5-bit precision.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_22"}, {"primary_key": "2130599", "vector": [], "sparse_vector": [], "title": "New Representations of the AES Key Schedule.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we present a new representation of the AES key schedule, with some implications to the security of AES-based schemes. In particular, we show that the AES-128 key schedule can be split into four independent parallel computations operating on 32 bits chunks, up to linear transformation. Surprisingly, this property has not been described in the literature after more than 20 years of analysis of AES. We show two consequences of our new representation, improving previous cryptanalysis results of AES-based schemes. First, we observe that iterating an odd number of key schedule rounds results in a function with short cycles. This explains an observation of <PERSON><PERSON><PERSON><PERSON> on mix<PERSON><PERSON>, a second-round candidate in the NIST lightweight competition. Our analysis actually shows that his forgery attack on mix<PERSON><PERSON> succeeds with probability 0.44 (with data complexity 220 GB), breaking the scheme in practice. The same observation also leads to a novel attack on ALE, another AES-based AEAD scheme. Our new representation also gives efficient ways to combine information from the first subkeys and information from the last subkeys, in order to reconstruct the corresponding master keys. In particular we improve previous impossible differential attacks against AES-128.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_3"}, {"primary_key": "2130600", "vector": [], "sparse_vector": [], "title": "On the Security of Homomorphic Encryption on Approximate Numbers.", "authors": ["Baiyu Li", "<PERSON><PERSON>"], "summary": "We present passive attacks against CKKS, the homomorphic encryption scheme for arithmetic on approximate numbers presented at Asiacrypt 2017. The attack is both theoretically efficient (running in expected polynomial time) and very practical, leading to complete key recovery with high probability and very modest running times. We implemented and tested the attack against major open source homomorphic encryption libraries, includingHEAAN,SEAL,HElibandPALISADE, and when computing several functions that often arise in applications of the CKKS scheme to machine learning on encrypted data, like mean and variance computations, and approximation of logistic and exponential functions using their <PERSON><PERSON><PERSON> series. The attack shows that the traditional formulation of\\(\\textsf {IND}\\hbox {-}\\textsf {CPA}\\)security (or indistinguishability against chosen plaintext attacks) achieved by CKKS does not adequately capture security against passive adversaries when applied to approximate encryption schemes, and that a different, stronger definition is required to evaluate the security of such schemes. We provide a solid theoretical basis for the security evaluation of homomorphic encryption on approximate numbers (against passive attacks) by proposing new definitions, that naturally extend the traditional notion of\\(\\textsf {IND}\\hbox {-}\\textsf {CPA}\\)security to the approximate computation setting. We propose both indistinguishability-based and simulation-based variants, as well as restricted versions of the definitions that limit the order and number of adversarial queries (as may be enforced by some applications). We prove implications and separations among different definitional variants, and discuss possible modifications to CKKS that may serve as a countermeasure to our attacks.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_23"}, {"primary_key": "2130601", "vector": [], "sparse_vector": [], "title": "Bifurcated Signatures: Folding the Accountability vs. Anonymity Dilemma into a Single Private Signing Scheme.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Over the development of modern cryptography, often, alternative cryptographic schemes are developed to achieve goals that in some important respect are orthogonal. Thus, we have to choose either a scheme which achieves the first goal and not the second, or vice versa. This results in two types of schemes that compete with each other. In the basic area of user privacy, specifically in anonymous (multi-use credentials) signing, such an orthogonality exists between anonymity and accountability. The conceptual contribution of this work is to reverse the above orthogonality by design, which essentially typifies the last 25 years or so, and to suggest an alternative methodology where the opposed properties are carefully folded into a single scheme. The schemes will support both opposing properties simultaneously in a bifurcated fashion, where: First, based on rich semantics expressed over the message’s context and content, the user, etc., the relevant property is applied point-wise per message operation depending on a predicate; and Secondly, at the same time, the schemes provide what we call “branch-hiding;” namely, the resulting calculated value hides from outsiders which property has actually been locally applied. Specifically, we precisely define and give the first construction and security proof of a “Bifurcated Anonymous Signature” (BiAS): A scheme which supports either absolute anonymity or anonymity with accountability, based on a specific contextual predicate, while being branch-hiding. This novel signing scheme has numerous applications not easily implementable or not considered before, especially because: (i) the conditional traceability doesnotrely on a trusted authority as it is (non-interactively) encapsulated into signatures; and (ii) signersknowthe predicate value and can make a conscious choice at each signing time. Technically, we realize BiAS from homomorphic commitments for a general family of predicates that can be represented by bounded-depth circuits. Our construction is generic and can be instantiated in the standard model from lattices and, more efficiently, from bilinear maps. In particular, the signature length is independent of the circuit size when we use commitments with suitable efficiency properties.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_18"}, {"primary_key": "2130602", "vector": [], "sparse_vector": [], "title": "Rotational Cryptanalysis from a Differential-Linear Perspective - Practical Distinguishers for Round-Reduced FRIET, Xoodoo, and Alzette.", "authors": ["<PERSON><PERSON>", "<PERSON>wei Sun", "<PERSON>"], "summary": "The differential-linear attack, combining the power of the two most effective techniques for symmetric-key cryptanalysis, was proposed by <PERSON><PERSON> and <PERSON> at CRYPTO 1994. From the exact formula for evaluating the bias of a differential-linear distinguisher (JoC 2017), to the differential-linear connectivity table (DLCT) technique for dealing with the dependencies in the switch between the differential and linear parts (EUROCRYPT 2019), and to the improvements in the context of cryptanalysis of ARX primitives (CRYPTO 2020), we have seen significant development of the differential-linear attack during the last four years. In this work, we further extend this framework by replacing the differential part of the attack by rotational-xor differentials. Along the way, we establish the theoretical link between the rotational-xor differential and linear approximations, revealing that it is nontrivial to directly apply the closed formula for the bias of ordinary differential-linear attack to rotational differential-linear cryptanalysis. We then revisit the rotational cryptanalysis from the perspective of differential-linear cryptanalysis and generalize <PERSON><PERSON><PERSON> et al.’s technique for analyzingKeccak, which leads to a practical method for estimating the bias of a (rotational) differential-linear distinguisher in the special case where the output linear mask is a unit vector. Finally, we apply the rotational differential-linear technique to the permutations involved inFRIET,Xoodoo,Alzette, andSipHash. This gives significant improvements over existing cryptanalytic results, or offers explanations for previous experimental distinguishers without a theoretical foundation. To confirm the validity of our analysis, all distinguishers with practical complexities are verified experimentally.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_26"}, {"primary_key": "2130603", "vector": [], "sparse_vector": [], "title": "Leakage-Resilience of the Shamir Secret-Sharing Scheme Against Physical-Bit Leakages.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Efficient Reed-Solomon code reconstruction algorithms, for example, by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC–2016), translate into local leakage attacks on Shamir secret-sharing schemes over characteristic-2 fields. However, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (CRYPTO–2018) showed that the Shamir secret sharing scheme over prime-fields is leakage resilient to one-bit local leakage if the reconstruction threshold is roughly 0.87 times the total number of parties. In several application scenarios, like secure multi-party multiplication, the reconstruction threshold must be at most half the number of parties. Furthermore, the number of leakage bits that the Shamir secret sharing scheme is resilient to is also unclear. Towards this objective, we study the Shamir secret-sharing scheme’s leakage-resilience over a prime-fieldF. The parties’ secret-shares, which are elements in the finite fieldF, are naturally represented as\\(\\lambda \\)-bit binary strings representing the elements\\(\\{0,1,\\dotsc ,p-1\\}\\). In our leakage model, the adversary can independently probembit-locations from each secret share. The inspiration for considering this leakage model stems from the impact that the study of oblivious transfer combiners had on general correlation extraction algorithms, and the significant influence of protecting circuits from probing attacks has on leakage-resilient secure computation. Consider arbitrary reconstruction threshold\\(k\\geqslant 2\\), physical bit-leakage parameter\\(m\\geqslant 1\\), and the number of parties\\(n\\geqslant 1\\). We prove that Shamir’s secret-sharing scheme with random evaluation places is leakage-resilient with high probability when the order of the fieldFis sufficiently large; ignoring polylogarithmic factors, one needs to ensure that\\(\\log \\left|F \\right| \\geqslant n/k\\). Our result, excluding polylogarithmic factors, states that Shamir’s scheme is secure as long as the total amount of leakage\\(m\\cdot n\\)is less than the entropy\\(k\\cdot \\lambda \\)introduced by the Shamir secret-sharing scheme. Note that our result holds even for small constant values of the reconstruction thresholdk, which is essential to several application scenarios. To complement this positive result, we present a physical-bit leakage attack for\\(m=1\\)physical bit-leakage from\\(n=k\\)secret shares and any prime-fieldFsatisfying\\(\\left|F \\right|=1\\mod k\\). In particular, there are (roughly)\\(\\left|F \\right|^{n-k+1}\\)such vulnerable choices for then-tuple of evaluation places. We lower-bound the advantage of this attack for small values of the reconstruction threshold, like\\(k=2\\)and\\(k=3\\), and any\\(\\left|F \\right|=1\\mod k\\). In general, we present a formula calculating our attack’s advantage for everykas\\(\\left|F \\right|\\rightarrow \\infty .\\) Technically, our positive result relies on Fourier analysis, analytic properties of proper rank-rgeneralized arithmetic progressions, and Bézout ’s theorem to bound the number of solutions to an equation over finite fields. The analysis of our attack relies on determining the “discrepancy” of the Irwin-Hall distribution. A probability distribution’s discrepancy is a new property of distributions that our work introduces, which is of potential independent interest.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_12"}, {"primary_key": "2130604", "vector": [], "sparse_vector": [], "title": "The Rise of Paillier: Homomorphic Secret Sharing and Public-Key Silent OT.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We describe a simple method for solving the distributed discrete logarithm problem in Paillier groups, allowing two parties to locally convert multiplicative shares of a secret (in the exponent) into additive shares. Our algorithm is perfectly correct, unlike previous methods with an inverse polynomial error probability. We obtain the following applications and further results. Homomorphic secret sharing.We construct homomorphic secret sharing for branching programs withnegligiblecorrectness error and supportingexponentially largeplaintexts, with security based on the decisional composite residuosity (DCR) assumption. Correlated pseudorandomness.Pseudorandom correlation functions (PCFs), recently introduced by <PERSON> et al. (FOCS 2020), allow two parties to obtain a practically unbounded quantity of correlated randomness, given a pair of short, correlated keys. We construct PCFs for the oblivious transfer (OT) and vector oblivious linear evaluation (VOLE) correlations, based on the quadratic residuosity (QR) or DCR assumptions, respectively. We also construct a pseudorandom correlation generator (for producing a bounded number of samples, all at once) for general degree-2 correlations including OLE, based on a combination of (DCR or QR) and the learning parity with noise assumptions. Public-key silent OT/VOLE.We upgrade our PCF constructions to have apublic-key setup, where after independently posting a public key, each party can locally derive its PCF key. This allows completelysilent generationof an arbitrary amount of OTs or VOLEs, without any interaction beyond a PKI, based on QR, DCR, a CRS and a random oracle. The public-key setup is based on a novel non-interactive vector OLE protocol, which can be seen as a variant of the Bellare-Micali oblivious transfer protocol.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_24"}, {"primary_key": "2130605", "vector": [], "sparse_vector": [], "title": "On the Ideal Shortest Vector Problem over Random Rational Primes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Any non-zero ideal in a number field can be factored into a product of prime ideals. In this paper we report a surprising connection between the complexity of the shortest vector problem (SVP) of prime ideals in number fields and their decomposition groups. When applying the result to number fields popular in lattice based cryptosystems, such as power-of-two cyclotomic fields, we show that a majority of rational primes lie under prime ideals admitting a polynomial time algorithm for SVP. Although the shortest vector problem of ideal lattices underpins the security of the Ring-LWE cryptosystem, this work does not break Ring-LWE, since the security reduction is from the worst case ideal SVP to the average case Ring-LWE, and it is one-way.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77870-5_20"}, {"primary_key": "2130606", "vector": [], "sparse_vector": [], "title": "Constant-Overhead Unconditionally Secure Multiparty Computation Over Binary Fields.", "authors": ["<PERSON>gon<PERSON> Polychron<PERSON>", "<PERSON><PERSON>"], "summary": "We study the communication complexity of unconditionally secure multiparty computation (MPC) protocols in the honest majority setting. Despite tremendous efforts in achieving efficient protocols for binary fields under computational assumptions, there are no efficient unconditional MPC protocols in this setting. In particular, there are non-party protocols with constant overhead admitting communication complexity ofO(n) bits per gate. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (CRYPTO 2018) were the first ones to achieve such an overhead in the amortized setting by evaluating\\(O(\\log n)\\)copies of the same circuit in the binary field in parallel. In this work, we construct the first unconditional MPC protocol secure against a malicious adversary in the honest majority setting evaluating just asingleboolean circuit with amortized communication complexity ofO(n) bits per gate.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_28"}, {"primary_key": "2130607", "vector": [], "sparse_vector": [], "title": "VOLE-PSI: Fast OPRF and Circuit-PSI from Vector-OLE.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this work we present a new construction for a batched Oblivious Pseudorandom Function (OPRF) based on Vector-OLE and the PaXoS data structure. We then use it in the standard transformation for achieving Private Set Intersection (PSI) from an OPRF. Our overall construction is highly efficient withO(n) communication and computation. We demonstrate that our protocol can achieve malicious security at only a very small overhead compared to the semi-honest variant. For input sizes\\(n = 2^{20}\\), our malicious protocol needs 6.2 s and less than 59 MB communication. This corresponds to under 450 bits per element, which is the lowest number for any published PSI protocol (semi-honest or malicious) to date. Moreover, in theory our semi-honest (resp. malicious) protocol can achieve as low as 219 (resp. 260) bits per element for\\(n=2^{20}\\)at the added cost of interpolating a polynomial overnelements. As a second contribution, we present an extension where the output of the PSI is secret-shared between the two parties. This functionality is generally referred to as Circuit-PSI. It allows the parties to perform a subsequent MPC protocol on the secret-shared outputs, e.g., train a machine learning model. Our circuit PSI protocol builds on our OPRF construction along with another application of the PaXoS data structure. It achieves semi-honest security and allows for a highly efficient implementation, up to 3x faster than previous work.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_31"}, {"primary_key": "2130608", "vector": [], "sparse_vector": [], "title": "Security Analysis of Quantum Lightning.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Quantum lightning is a new cryptographic object that gives a strong form of quantum money. <PERSON><PERSON><PERSON> recently defined quantum lightning and proposed a construction of it based on superpositions of low-rank matrices. The scheme is unusual, so it is difficult to base the scheme’s security on any widespread computational assumptions. Instead, <PERSON><PERSON><PERSON> proposed a new hardness assumption that, if true, could be used to prove security. In this work, we show that <PERSON><PERSON><PERSON>’s hardness assumption is in fact false, so the proof of security does not hold. However, we note that the proposal for quantum lightning has not been proveninsecure. This work is the first step in analyzing the security of [3]’s proposal and moving toward a scheme that we can prove to be secure.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_19"}, {"primary_key": "2130609", "vector": [], "sparse_vector": [], "title": "Non-Interactive Anonymous Router.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Anonymous routing is one of the most fundamental online privacy problems and has been studied extensively for decades. Almost all known approaches for anonymous routing (e.g., mix-nets, DC-nets, and others) rely on multiple servers or routers to engage in someinteractiveprotocol; and anonymity is guaranteed in thethresholdmodel, i.e., if one or more of the servers/routers behave honestly. Departing from all prior approaches, we propose a novelnon-interactiveabstraction called a Non-Interactive Anonymous Router (NIAR), which works even with asingle untrusted router. In a NIAR scheme, suppose thatnsenders each want to talk to a distinct receiver. A one-time trusted setup is performed such that each sender obtains a sending key, each receiver obtains a receiving key, and the router receives atokenthat “encrypts” the permutation mapping the senders to receivers. In every time step, each sender can encrypt its message using its sender key, and the router can use its token to convert thenciphertexts received from the senders tontransformed ciphertexts. Each transformed ciphertext is delivered to the corresponding receiver, and the receiver can decrypt the message using its receiver key. Imprecisely speaking, security requires that the untrusted router, even when colluding with a subset of corrupt senders and/or receivers, should not be able to compromise the privacy of honest parties, including who is talking to who, and the message contents. We show how to construct a communication-efficient NIAR scheme with provable security guarantees based on the standard Decision Linear assumption in suitable bilinear groups. We show that a compelling application of NIAR is to realize a Non-Interactive Anonymous Shuffler (NIAS), where an untrusted server or data analyst can only decrypt a permuted version of the messages coming fromnsenders where the permutation is hidden. NIAS can be adopted to construct privacy-preserving surveys, differentially private protocols in the shuffle model, and pseudonymous bulletin boards. Besides this main result, we also describe a variant that achieves fault tolerance when a subset of the senders may crash. Finally, we further explore a paranoid notion of security called full insider protection, and show that if we additionally assume sub-exponentially secure Indistinguishability Obfuscation and as sub-exponentially secure one-way functions, one can construct a NIAR scheme with paranoid security.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_17"}, {"primary_key": "2130610", "vector": [], "sparse_vector": [], "title": "Classical Proofs of Quantum Knowledge.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We define the notion of aproof of knowledgein the setting where the verifier is classical, but the prover is quantum, and where the witness that the prover holds is in general a quantum state. We establish simple properties of our definition, including that, if anondestructiveclassical proof of quantum knowledge exists for some state, then that state can be cloned by an unbounded adversary, and that, under certain conditions on the parameters in our definition, a proof of knowledge protocol for a hard-to-clone state can be used as a (destructive) quantum money verification protocol. In addition, we provide two examples of protocols (both inspired by private-key classical verification protocols for quantum money schemes) which we can show to be proofs of quantum knowledge under our definition. In so doing, we introduce techniques for the analysis of such protocols which build on results from the literature on nonlocal games. Finally, we show that, under our definition, the verification protocol introduced by <PERSON><PERSON><PERSON> (FOCS 2018) is a classicalargumentof quantum knowledge for QMA relations. In all cases, we construct an explicit quantum extractor that is able to produce a quantum witness given black-box quantum (rewinding) access to the prover, the latter of which includes the ability to coherently execute the prover’s black-box circuit controlled on a superposition of messages from the verifier.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_22"}, {"primary_key": "2130611", "vector": [], "sparse_vector": [], "title": "Candidate Obfusca<PERSON> via Oblivious LW<PERSON> Sampling.", "authors": ["<PERSON><PERSON><PERSON>e", "<PERSON>"], "summary": "We present a new, simple candidate construction of indistinguishability obfuscation (iO). Our scheme is inspired by lattices and learning-with-errors (LWE) techniques, but we are unable to prove security under a standard assumption. Instead, we formulate a new falsifiable assumption under which the scheme is secure. Furthermore, the scheme plausibly achieves post-quantum security. Our construction is based on the recent “split FHE” framework of Brakerski, Döttling, Garg, and Malavolta (EUROCRYPT ’20), and we provide a new instantiation of this framework. As a first step, we construct an iO scheme that is provably secure assuming that LWE holdsandthat it is possible to obliviously generate LWE samples without knowing the corresponding secrets. We define a precise notion of oblivious LWE sampling that suffices for the construction. It is known how to obliviously sample from any distribution (in a very strong sense) using iO, and our result provides a converse, showing that the ability to obliviously sample from the specific LWE distribution (in a much weaker sense) already also implies iO. As a second step, we give a heuristic contraction of oblivious LWE sampling. On a very high level, we do this by homomorphically generating pseudorandom LWE samples using an encrypted pseudorandom function.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77883-5_5"}, {"primary_key": "2130612", "vector": [], "sparse_vector": [], "title": "Classical vs Quantum Random Oracles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we study relationship between security of cryptographic schemes in the random oracle model (ROM) and quantum random oracle model (QROM). First, we introduce a notion of aproof of quantum access to a random oracle(PoQRO), which is a protocol to prove the capability to quantumly access a random oracle to a classical verifier. We observe that a proof of quantumness recently proposed by <PERSON><PERSON><PERSON><PERSON> et al. (TQC ’20) can be seen as a PoQRO. We also give a construction of a publicly verifiable PoQRO relative to a classical oracle. Based on them, we construct digital signature and public key encryption schemes that are secure in the ROM but insecure in the QROM. In particular, we obtain the first examples of natural cryptographic schemes that separate the ROM and QROM under a standard cryptographic assumption. On the other hand, we give lifting theorems from security in the ROM to that in the QROM for certain types of cryptographic schemes and security notions. For example, our lifting theorems are applicable to Fiat-Shamir non-interactive arguments, Fiat-Shamir signatures, and Full-Domain-Hash signatures etc. We also discuss applications of our lifting theorems to quantum query complexity.", "published": "2021-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-030-77886-6_20"}]