{"name": "ai-chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write"}, "dependencies": {"@ai-sdk/openai": "1.0.0-canary.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/react-visually-hidden": "^1.1.0", "@types/katex": "^0.16.7", "@types/prismjs": "^1.26.5", "@types/react-datepicker": "^7.0.0", "@types/react-syntax-highlighter": "^15.5.13", "@vercel/analytics": "^1.3.1", "@vercel/blob": "^0.24.1", "ai": "4.0.0-canary.9", "bcrypt-ts": "^5.0.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "dexie": "^4.0.10", "diff-match-patch": "^1.0.5", "dotenv": "^16.4.5", "file-saver": "^2.0.5", "framer-motion": "^11.3.19", "geist": "^1.3.1", "jszip": "^3.10.1", "katex": "^0.16.22", "lucide-react": "^0.446.0", "nanoid": "^5.0.8", "next": "15.0.3-canary.2", "next-auth": "5.0.0-beta.25", "next-themes": "^0.3.0", "orderedmap": "^2.1.1", "pdfjs-dist": "^4.8.69", "prismjs": "^1.30.0", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.4.0", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.23.0", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.34.3", "react": "18.2.0", "react-datepicker": "^7.5.0", "react-day-picker": "8.10.1", "react-dom": "18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "server-only": "^0.0.1", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "uuid": "^11.0.3", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/typography": "^0.5.15", "@types/d3-scale": "^4.0.8", "@types/file-saver": "^2.0.7", "@types/node": "^22.8.6", "@types/pdf-parse": "^1.1.4", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8.56.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-tailwindcss": "^3.17.5", "postcss": "^8", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "typescript": "^5.6.3"}}