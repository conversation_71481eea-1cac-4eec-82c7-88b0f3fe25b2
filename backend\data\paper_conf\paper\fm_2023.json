[{"primary_key": "1139359", "vector": [], "sparse_vector": [], "title": "SMPT: A Testbed for Reachability Methods in Generalized Petri Nets.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "SMPT (for Satisfiability Modulo Petri Net) is a model checker for reachability problems in Petri nets. It started as a portfolio of methods to experiment with symbolic model checking, and was designed to be easily extended. Some distinctive features are its ability to benefit from structural reductions and to generate verdict certificates. Our tool is quite mature and performed well compared to other state-of-the-art tools in the Model Checking Contest.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_25"}, {"primary_key": "1139360", "vector": [], "sparse_vector": [], "title": "The ScalaFix Equation Solver.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We presentScalaFix, a modular library for solving equation systems by iterative methods.ScalaFiximplements several solvers, involving iteration strategies from plain <PERSON>leen<PERSON>’s iteration to more complex ones based on a hierarchical ordering of the unknowns. It works with finite and infinite equation systems and supports widening, narrowing and warrowing operators. It also allows intertwining ascending and descending chains and other advanced techniques such as localized widening.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_10"}, {"primary_key": "1139361", "vector": [], "sparse_vector": [], "title": "veriFIRE: Verifying an Industrial, Learning-Based Wildfire Detection System.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this short paper, we present our ongoing work on the veriFIRE project—a collaboration between industry and academia, aimed at using verification for increasing the reliability of a real-world, safety-critical system. The system we target is an airborne platform for wildfire detection, which incorporates two deep neural networks. We describe the system and its properties of interest, and discuss our attempts to verify the system’s consistency, i.e., its ability to continue and correctly classify a given input, even if the wildfire it describes increases in intensity. We regard this work as a step towards the incorporation of academic-oriented verification tools into real-world systems of interest.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_38"}, {"primary_key": "1139362", "vector": [], "sparse_vector": [], "title": "The Octatope Abstract Domain for Verification of Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Efficient verification algorithms for neural networks often depend on various abstract domains such asintervals,zonotopes, andlinear star sets. The choice of the abstract domain presents an expressiveness vs. scalability trade-off: simpler domains are less precise but yield faster algorithms. This paper investigates theoctatopeabstract domain in the context of neural net verification. Octatopes are affine transformations ofn-dimensional octagons—sets of unit-two-variable-per-inequality (utvpi) constraints. Octatopes generalize the idea of zonotopes which can be viewed as an affine transformation of a box. On the other hand, octatopes can be considered as a restriction of linear star set, which are affine transformations of arbitrary\\(\\mathcal {H}\\)-Polytopes. This distinction places octatopes firmly between zonotopes and star sets in their expressive power, but what about the efficiency of decision procedures? An important analysis problem for neural networks is theexact range computationproblem that asks to compute the exact set of possible outputs given a set of possible inputs. For this, three computational procedures are needed: 1) optimization of a linear cost function; 2) affine mapping; and 3) over-approximating the intersection with a half-space. While zonotopes allow an efficient solution for these approaches, star sets solves these procedures via linear programming. We show that these operations are faster for octatopes than the more expressive linear star sets. For octatopes, we reduce these problems to min-cost flow problems, which can be solved in strongly polynomial time using the Out-of-Kilter algorithm. Evaluating exact range computation on several ACAS Xu neural network benchmarks, we find that octatopes show promise as a practical abstract domain for neural network verification.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_26"}, {"primary_key": "1139363", "vector": [], "sparse_vector": [], "title": "A Runtime Environment for Contract Automata.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Contract automata have been introduced for specifying applications through behavioural contracts and for synthesising their orchestrations as finite state automata. This paper addresses the realisation of applications from contract automata specifications. We presentCARE, a new runtime environment to coordinate services implementing contracts that guarantees the adherence of the implementation to its contract. We discuss howCAREcan be adopted to realise contract-based applications, its formal guarantees, and we identify the responsibilities of the involved business actors. Experiments show the benefits of adoptingCAREwith respect to manual implementations.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_31"}, {"primary_key": "1139364", "vector": [], "sparse_vector": [], "title": "Monitoring the Internet Computer.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Internet Computer (IC) is a distributed platform for Web3 applications, spanning over 1,200 nodes worldwide. We present results on applying runtime monitoring to the IC. We use the MonPoly monitor and its expressive policy language with quantifiers over infinite domains, aggregations, and past and future operators. We formalize complex policies that cover common kinds of production incidents and IC-specific protocol properties, including malicious behaviors and infrastructure outages. Using these policies, we evaluate MonPoly’s performance in a large-scale case study that includes logs from both production and testing environments. We find, for example, that MonPoly performs well on testing logs, and that half of our policies applicable to production logs can be monitored in an online setting. Overall, our policies and IC traces constitute a new benchmark for first-order temporal logic monitors.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_22"}, {"primary_key": "1139365", "vector": [], "sparse_vector": [], "title": "symQV: Automated Symbolic Verification of Quantum Programs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We presentsymQV, a symbolic execution framework for writing and verifying quantum computations in the quantum circuit model.symQVcan automatically verify that a quantum program complies with a first-order specification. We formally introduce a symbolic quantum program model. This allows to encode the verification problem in an SMT formula, which can then be checked with a\\(\\mathbf \\delta \\)-complete decision procedure. We also propose an abstraction technique to speed up the verification process. Experimental results show that the abstraction improvessymQV’s scalability by an order of magnitude to quantum programs with 24 qubits (a\\( 2^{24}\\)-dimensional state space).", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_12"}, {"primary_key": "1139366", "vector": [], "sparse_vector": [], "title": "Can We Communicate? Using Dynamic Logic to Verify Team Automata.", "authors": ["<PERSON>", "Guillermina Cledou", "<PERSON>", "<PERSON>"], "summary": "Team automata describe networks of automata with input and output actions, extended with synchronisation policies guiding how many interacting components can synchronise on a shared input/output action. Given such a team automaton, we can reason over communication properties such asreceptiveness(sent messages must be received) andresponsiveness(pending receives must be satisfied). Previous work focused on how toidentifythese communication properties. However, automatically verifying these properties is non-trivial, as it may involve traversing networks of interacting automata with large state spaces. This paper investigates (1) how tocharacterisecommunication properties for team automata (and subsumed models) using test-free propositional dynamic logic, and (2) how to use this characterisation toverifycommunication properties by model checking. A prototype tool supports the theory, using a transformation to interact with the mCRL2 tool for model checking.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_9"}, {"primary_key": "1139367", "vector": [], "sparse_vector": [], "title": "Word Equations in Synergy with Regular Constraints.", "authors": ["Frantisek <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We argue that in string solving, word equations and regular constraints are better mixed together than approached separately as in most current string solvers. We propose a fast algorithm, complete for the fragment of chain-free constraints, in which word equations and regular constraints are tightly integrated and exchange information, efficiently pruning the cases generated by each other and limiting possible combinatorial explosion. The algorithm is based on a novel language-based characterisation of satisfiability of word equations with regular constraints. We experimentally show that our prototype implementation is competitive with the best string solvers and even superior in that it is the fastest on difficult examples and has the least number of timeouts.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_23"}, {"primary_key": "1139368", "vector": [], "sparse_vector": [], "title": "A Coq Formalization of Lebesgue Induction Principle and Tonelli&apos;s Theorem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Lebesgue integration is a well-known mathematical tool, used for instance in probability theory, real analysis, and numerical mathematics. Thus, its formalization in a proof assistant is to be designed to fit different goals and projects. Once the Lebesgue integral is formally defined and the first lemmas are proved, the question of the convenience of the formalization naturally arises. To check it, a useful extension is <PERSON><PERSON><PERSON>’s theorem, stating that the (double) integral of a nonnegative measurable function of two variables can be computed by iterated integrals, and allowing to switch the order of integration. This article describes the formal definition and proof inCoqof product sigma-algebras, product measures and their uniqueness, the construction of iterated integrals, up to <PERSON><PERSON><PERSON>’s theorem. We also advertise theLebesgue induction principleprovided by an inductive type for nonnegative measurable functions.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_4"}, {"primary_key": "1139369", "vector": [], "sparse_vector": [], "title": "VeyMont: Parallelising Verified Programs Instead of Verifying Parallel Programs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present VeyMont: a deductive verification tool that aims to make reasoning about functional correctness and deadlock freedom of parallel programs (relatively complex) as easy as that of sequential programs (relatively simple). The novelty of VeyMont is that it “inverts the workflow”: it supports a new method to parallelise verified programs, in contrast to existing methods to verify parallel programs. Inspired by methods for distributed systems, VeyMont targets coarse-grained parallelism among threads (i.e., whole-program parallelisation) instead of fine-grained parallelism among tasks (e.g., loop parallelisation).", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_19"}, {"primary_key": "1139370", "vector": [], "sparse_vector": [], "title": "A Decision Diagram Operation for Reachability.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Saturation is considered the state-of-the-art method for computing fixpoints with decision diagrams. We present a relatively simple decision diagram operation calledReachthat also computes fixpoints. In contrast to saturation, it does not require a partitioning of the transition relation. We give sequential algorithms implementing the new operation for both binary and multi-valued decision diagrams, and moreover provide parallel counterparts. We implement these algorithms and experimentally compare their performance against saturation on 692 model checking benchmarks in different languages. The results show that theReachoperation often outperforms saturation, especially on transition relations with low locality. In a comparison between parallelized versions ofReachand saturation we find thatReachobtains comparable speedups up to 16 cores, although falls behind saturation at 64 cores. Finally, in a comparison with the state-of-the-art model checking tool ITS-tools we find thatReachoutperforms ITS-tools on 29% of models, suggesting thatReachcan be useful as a complementary method in an ensemble tool.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_29"}, {"primary_key": "1139371", "vector": [], "sparse_vector": [], "title": "Verifying Feedforward Neural Networks for Classification in Isabelle/HOL.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Neural networks are being used successfully to solve classification problems, e.g., for detecting objects in images. It is well known that neural networks are susceptible if small changes applied to their input result in misclassification. Situations in which such a slight input change, often hardly noticeable by a human expert, results in a misclassification are called adversarial examples. If such inputs are used for adversarial attacks, they can be life-threatening if, for example, they occur in image classification systems used in autonomous cars or medical diagnosis. Systems employing neural networks, e.g., for safety or security-critical functionality, are a particular challenge for formal verification, which usually expects a formal specification (e.g., given as source code in a programming language for which a formal semantics exists). Such a formal specification does, per se, not exist for neural networks. In this paper, we address this challenge by presenting a formal embedding of feedforward neural networks into Isabelle/HOL and discussing desirable properties for neural networks in critical applications. Our Isabelle-based prototype can import neural networks trained in TensorFlow, and we demonstrate our approach using a neural network trained for the classification of digits on a dot-matrix display.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_24"}, {"primary_key": "1139372", "vector": [], "sparse_vector": [], "title": "Formal and Executable Semantics of the Ethereum Virtual Machine in Dafny.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Ethereum protocol implements a replicated state machine. The network participants keep track of the system state by: 1) agreeing on the sequence of transactions to be processed and 2) computing the state transitions that correspond to the sequence of transactions. Ethereum transactions are programs, calledsmart contracts, and computing a state transition requires executing some code. The Ethereum Virtual Machine (EVM) provides this capability and can execute programs written in EVMbytecode. We present a formal and executable semantics of the EVM written in the verification-friendly languageDafny: it provides (i) a readable, formal and verified specification of the semantics of the EVM; (ii) a framework to formally reason about bytecode.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_32"}, {"primary_key": "1139373", "vector": [], "sparse_vector": [], "title": "Minimisation of Spatial Models Using Branching Bisimilarity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Spatial logic and spatial model checking have great potential for traditional computer science domains and beyond. Reasoning about space involves two different conditional reachability modalities: a forward reachability, similar to that used in temporal logic, and a backward modality representing that a point can be reached from another point, under certain conditions. Since spatial models can be huge, suitable model minimisation techniques are crucial for efficient model checking. An effective minimisation method for the recent notion of spatial Compatible Path (CoPa)-bisimilarity is proposed, and shown to be correct. The core of our method is the encoding of Closure Models as Labelled Transition Systems, enabling minimisation algorithms for branching bisimulation to compute CoPa equivalence classes. Initial validation via benchmark examples demonstrates a promising speed-up in model checking of spatial properties for models of realistic size.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_16"}, {"primary_key": "1139374", "vector": [], "sparse_vector": [], "title": "A Fine-Grained Se<PERSON><PERSON> for Arrays and Pointers Under Weak Memory Models.", "authors": ["<PERSON>"], "summary": "Developers of concurrent code for multicore architectures must navigate weak memory models (wmms) – either directly at the hardware/assembly level or at a somewhat generalised software level – making the verification of concurrent code an even more difficult task. Semantic models based on a system-wide partial-ordering on events have been developed to define the behaviour of code executing under wmms, but typically require specialised assertion languages and inference techniques to reason about, and often apply to only rudimentary programming constructs. In this paper we present a generic but versatile abstract imperative language “IMP+ptr” which includes pointers and arrays, from which can be built high-level imperative programming constructs for verifying abstract algorithmic logic, or low-level microassembly for, e.g., investigating hardware security vulnerabilities. The base language carefully controls the syntax of atomic instructions to allow program-level, algebraic reasoning about the additional nondeterminism inherent in programs executing under wmms. We show how arrays of pointers, aliasing, and linked lists may be affected by wmms, establishing a base from where we apply pre-existing verification results and techniques for sequential programs with nested parallelism.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_18"}, {"primary_key": "1139375", "vector": [], "sparse_vector": [], "title": "Backdoor Mitigation in Deep Neural Networks via Strategic Retraining.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep Neural Networks (DNN) are becoming increasingly more important in assisted and automated driving. Using such entities which are obtained using machine learning is inevitable: tasks such as recognizing traffic signs cannot be developed reasonably using traditional software development methods. DNN however do have the problem that they are mostly black boxes and therefore hard to understand and debug. One particular problem is that they are prone to hiddenbackdoors. This means that the DNN misclassifies its input, because it considers properties that should not be decisive for the output. Backdoors may either be introduced by malicious attackers or by inappropriate training. In any case, detecting and removing them is important in the automotive area, as they might lead to safety violations with potentially severe consequences. In this paper, we introduce a novel method to remove backdoors. Our method works for both intentional as well as unintentional backdoors. We also do not require prior knowledge about the shape or distribution of backdoors. Experimental evidence shows that our method performs well on several medium-sized examples.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_37"}, {"primary_key": "1139376", "vector": [], "sparse_vector": [], "title": "Energy Büchi Problems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>C<PERSON><PERSON>"], "summary": "We show how to efficiently solve energy Büchi problems in finite weighted automata and in one-clock weighted timed automata. Solving the former problem is our main contribution and is handled by a modified version of Bellman-Ford interleaved with <PERSON><PERSON><PERSON><PERSON>’s algorithm. The latter problem is handled via a reduction to the former relying on the corner-point abstraction. All our algorithms are freely available and implemented in a tool based on the open-source platforms TChecker and Spot.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_14"}, {"primary_key": "1139377", "vector": [], "sparse_vector": [], "title": "A Systematic Approach to Automotive Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a holistic methodology for designing automotive systems that consider security a central concern at every design stage. During the concept design, we model the system architecture and define the security attributes of its components. We perform threat analysis on the system model to identify structural security issues. From that analysis, we derive attack trees that define recipes describing steps to successfully attack the system’s assets and propose threat prevention measures. The attack tree allows us to derive a verification and validation (V &V) plan, which prioritizes the testing effort. In particular, we advocate using learning for testing approaches for the black-box components. It consists of inferring a finite state model of the black-box component from its execution traces. This model can then be used to generate new relevant tests, model check it against requirements, and compare two different implementations of the same protocol. We illustrate the methodology with an automotive infotainment system example. Using the advocated approach, we could also document unexpected and potentially critical behavior in our example systems.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_34"}, {"primary_key": "1139379", "vector": [], "sparse_vector": [], "title": "Runtime Monitoring for Out-of-Distribution Detection in Object Detection Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Runtime monitoring provides a more realistic and applicable alternative to verification in the setting of real neural networks used in industry. It is particularly useful for detecting out-of-distribution (OOD) inputs, for which the network was not trained and can yield erroneous results. We extend a runtime-monitoring approach previously proposed for classification networks to perception systems capable of identification and localization of multiple objects. Furthermore, we analyze its adequacy experimentally on different kinds of OOD settings, documenting the overall efficacy of our approach.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_36"}, {"primary_key": "1139380", "vector": [], "sparse_vector": [], "title": "Tableaux for Realizability of Safety Specifications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a tableau decision method for deciding realizability of specifications expressed in a safety fragment of LTL that includes bounded future temporal operators. Tableau decision procedures for temporal and modal logics have been thoroughly studied for satisfiability and for translating temporal formulae into equivalent Büchi automata, and also for model checking, where a specification and system are provided. However, to the best of our knowledge no tableau method has been studied for the reactive synthesis problem. Reactive synthesis starts from a specification where propositional variables are split into those controlled by the environment and those controlled by the system, and consists on automatically producing a system that guarantees the specification for all environments. Realizability is the decision problem of whether there is one such system. In this paper, we present a method to decide realizability of safety specifications, from which we can also extract (i.e., synthesize) a correct system (in case the specification is realizable). The main novelty of a tableau method is that it can be easily extended to handle richer domains (integers, etc.) and bounds in the temporal operators in ways that automata approaches for synthesis cannot.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_28"}, {"primary_key": "1139381", "vector": [], "sparse_vector": [], "title": "Intelligent and Dependable Decision-Making Under Uncertainty.", "authors": ["<PERSON><PERSON>"], "summary": "This talk highlights our vision of foundational and application-driven research toward safety, dependability, and correctness in artificial intelligence (AI). We take a broad stance on AI that combines formal methods, machine learning, and control theory. As part of this research line, we study problems inspired by autonomous systems, planning in robotics, and industrial applications. We consider reinforcement learning (RL) as a specific machine learning technique for decision-making under uncertainty. RL generally learns to behave optimally via trial and error. Consequently, and despite its massive success in the past years, RL lacks mechanisms to ensure safe and correct behavior. Formal methods, in particular formal verification, is a research area that provides formal guarantees of a system’s correctness and safety based on rigorous methods and precise specifications. Yet, fundamental challenges have obstructed the effective application of verification to reinforcement learning. Our main objective is to devise novel, data-driven verification methods that tightly integrate with RL. In particular, we develop techniques that address real-world challenges to the safety of AI systems in general: Scalability, expressiveness, and robustness against the uncertainty that occurs when operating in the real world. The overall goal is to advance the real-world deployment of reinforcement learning.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_3"}, {"primary_key": "1139383", "vector": [], "sparse_vector": [], "title": "Formal Modelling of Safety Architecture for Responsibility-Aware Autonomous Vehicle via Event-B Refinement.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Ensuring the safety of autonomous vehicles (AVs) is the key requisite for their acceptance in society. This complexity is the core challenge in formally proving their safety conditions with AI-based black-box controllers and surrounding objects under various traffic scenarios. This paper describes our strategy and experience in modelling, deriving, and proving the safety conditions of AVs with the Event-B refinement mechanism to reduce complexity. Our case study targets the state-of-the-art model of goal-aware responsibility-sensitive safety to argue over interactions with surrounding vehicles. We also employ the Simplex architecture to involve advanced black-box AI controllers. Our experience has demonstrated that the refinement mechanism can be effectively used to gradually develop the complex system over scenario variations.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_30"}, {"primary_key": "1139384", "vector": [], "sparse_vector": [], "title": "Railway Scheduling Using Boolean Satisfiability Modulo Simulations.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Railway scheduling is a problem that exhibits both non-trivial discrete and continuous behavior. In this paper, we model this problem using a combination of SAT and ordinary differential equations (SAT modulo ODE). In addition, we adapt our existing method for solving such problems in such a way that the resulting solver is competitive with methods based on dedicated railway simulators while being more general and extensible.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_5"}, {"primary_key": "1139385", "vector": [], "sparse_vector": [], "title": "Symbolic Computation in Automated Program Reasoning.", "authors": ["<PERSON>"], "summary": "We describe applications of symbolic computation towards automating the formal analysis of while-programs implementing polynomial arithmetic. We combine methods from static analysis, symbolic summation and computer algebra to derive polynomial loop invariants, yielding a finite representation of all polynomial equations that are valid before and after each loop execution. While deriving polynomial invariants is in general undecidable, we identify classes of loops for which we automatically can solve the problem of invariant synthesis. We further generalize our work to the analysis of probabilistic program loops. Doing so, we compute higher-order statistical moments over (random) program variables, inferring this way quantitative invariants of probabilistic program loops. Our results yield computer-aided solutions in support of formal software verification, compiler optimization, and probabilistic reasoning.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_1"}, {"primary_key": "1139387", "vector": [], "sparse_vector": [], "title": "Shifting Left for Early Detection of Machine-Learning Bugs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Computational notebooks are widely used for machine learning (ML). However, notebooks raise new correctness concerns beyond those found in traditional programming environments. ML library APIs are easy to misuse, and the notebook execution model raises entirely new problems concerning reproducibility. It is common to use static analyses to detect bugs and enforce best practices in software applications. However, when configured with new types of rules tailored to notebooks, these analyses can also detect notebook-specific problems. We present our initial efforts in understanding how static analysis for notebooks differs from analysis of traditional application software. We created six new rules for the CodeGuru Reviewer based on discussions with ML practitioners. We ran the tool on close to 10,000 experimentation notebooks, resulting in an average of approximately 1 finding per 7 notebooks. Approximately 60% of the findings that we reviewed are real notebook defects. (Due to confidentiality limitations, we cannot disclose the exact number of notebook files and findings.)", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_33"}, {"primary_key": "1139388", "vector": [], "sparse_vector": [], "title": "Efficient SMT-Based Network Fault Tolerance Verification.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Network control planes are highly sophisticated, resulting in networks that are difficult and error-prone to configure. Although several network verification tools have been developed to assist network operators, they are limited and inefficient in handling fault-tolerance policies. In this paper, we propose a novel SMT encoding to speed up control plane fault tolerance verification by pruning failed topologies. This encoding exploits the observation that the verifier has to check failures only for the links lying on a set of best paths which can be computed by a recursive algorithm. We implemented our technique in Minesweeper, a state-of-the-art SMT-based verifier. Our evaluation shows that the new encoding speeds up verification by the factor of 3.1–26.9X.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_7"}, {"primary_key": "1139389", "vector": [], "sparse_vector": [], "title": "Specification-Guided Critical Scenario Identification for Automated Driving.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To test automated driving systems, we present a case study for finding critical scenarios in driving environments guided by formal specifications. To that aim, we devise a framework for critical scenario identification, which we base on open-source libraries that combine scenario specification, testing, formal methods, and optimization.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_35"}, {"primary_key": "1139390", "vector": [], "sparse_vector": [], "title": "sfPFL: A Probabilistic Logic for Fault Trees.", "authors": ["<PERSON>", "Milan Lopuhaä-Zwakenberg", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Safety-critical infrastructures must operate in a safe and reliable way. Fault tree analysis is a widespread method used for risk assessment of these systems: fault trees (FTs) are required by, e.g., the Federal Aviation Administration and the Nuclear Regulatory Commission. In spite of their popularity, little work has been done on formulating structural queries about\\(\\textsc {ft} \\textrm{s}\\)and analyzing these, e.g., when evaluating potential scenarios, and to give practitioners instruments to formulate queries on\\(\\textsc {ft} \\textrm{s}\\)in an understandable yet powerful way. In this paper, we aim to fill this gap by extending\\( BFL \\)[37], a logic that reasons about Boolean\\(\\textsc {ft} \\textrm{s}\\). To do so, we introduce a Probabilistic Fault tree Logic (\\(\\textsf{PFL}\\)).\\(\\textsf{PFL}\\)is a simple, yet expressive logic that supports easier formulation of complex scenarios and specification of FT properties that comprise probabilities. Alongside\\(\\textsf{PFL}\\), we present\\(\\textsf{LangPFL}\\), a domain specific language to further ease property specification. We showcase\\(\\textsf{PFL}\\)and\\(\\textsf{LangPFL}\\)by applying them to a COVID-19 related FT and to a FT for an oil/gas pipeline. Finally, we present theory and model checking algorithms based on binary decision diagrams (BDDs).", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_13"}, {"primary_key": "1139391", "vector": [], "sparse_vector": [], "title": "Verifying Functional Correctness Properties at the Level of Java Bytecode.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The breakneck evolution of modern programming languages aggravates the development of deductive verification tools, which struggle to timely and fully support all new language features. To address this challenge, we presentByteBack: a verification technique that works on Java bytecode. Compared to high-level languages, intermediate representations such as bytecode offer a much more limited and stable set of features; hence, they may help decouple the verification process from changes in the source-level language. ByteBackoffers a library to specify functional correctness properties at the level of the source code, so that the bytecode is only used as an intermediate representation that the end user does not need to work with. Then,ByteBackreconstructs some of the information about types and expressions that is erased during compilation into bytecode but is necessary to correctly perform verification. Our experiments with an implementation ofByteBackdemonstrate that it can successfully verify bytecode compiled from different versions of Java, and including several modern language features that even state-of-the-art Java verifiers (such as KeY and OpenJML) do not directly support—thus revealing howByteBack’s approach can help keep up verification technology with language evolution.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_20"}, {"primary_key": "1139392", "vector": [], "sparse_vector": [], "title": "SMT Sampling via Model-Guided Approximation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We investigate the domain of satisfiable formulas in satisfiability modulo theories (SMT), in particular, automatic generation of a multitude of satisfying assignments to such formulas. Despite the long and successful history of SMT in model checking and formal verification, this aspect is relatively under-explored. Prior work exists for generating such assignments, orsamples, for Boolean formulas and for quantifier-free first-order formulas involving bit-vectors, arrays, and uninterpreted functions (QF_AUFBV). We propose a new approach that is suitable for a theoryTof integer arithmetic and toTwith arrays and uninterpreted functions. The approach involves reducing the general sampling problem to a simpler instance of sampling from a set of independent intervals, which can be done efficiently. Such reduction is carried out by expanding a single model—aseed—using top-down propagation of constraints along the original first-order formula.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_6"}, {"primary_key": "1139394", "vector": [], "sparse_vector": [], "title": "Program Semantics and Verification Technique for AI-Centred Programs.", "authors": ["Solofomampionona Fortunat Rajaona", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give a general-purpose programming language in which programs can reason about their own knowledge. To specify what these intelligent programs know, we define a “program epistemic” logic, akin to a dynamic epistemic logic for programs. Our logic properties are complex, including programs introspecting into future state of affairs, i.e., reasoning now about facts that hold only after they and other threads will execute. To model aspects anchored in privacy, our logic is interpreted over partial observability of variables, thus capturing that each thread can “see” only a part of the global space of variables. We verify program-epistemic properties on such AI-centred programs. To this end, we give a sound translation of the validity of our program-epistemic logic into first-order validity, using a new weakest-precondition semantics and a book-keeping of variable assignment. We implement our translation and fully automate our verification method for well-established examples using SMT solvers.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_27"}, {"primary_key": "1139395", "vector": [], "sparse_vector": [], "title": "Abstract Alloy Instances.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Alloy is a textual modeling language for structures and behaviors of software designs. One of the reasons for <PERSON>oy to become a popular light-weight formal method is its support for automated, bounded analyses, which is provided through the Analyzer toolset. The Analyzer provides the means to compute, visualize, and browse instances that either satisfy a model or violate an assertion. Understanding instances for the given analysis often requires much effort and there is no guarantee on the order or level of information of computed instances. To help address this, we introduce the concept of abstract Alloy instances, which abstract information common to all instances, while preserving information specific to the analysis. Our abstraction is based on introducing lower and upper bounds for elements that may appear in <PERSON><PERSON>’s instances. We evaluate computation times and sizes of abstract instances on a set of benchmark <PERSON>oy models.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_21"}, {"primary_key": "1139396", "vector": [], "sparse_vector": [], "title": "QMaude: Quantitative Specification and Verification in Rewriting Logic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In formal verification, qualitative and quantitative aspects are both relevant, and high-level formalisms are convenient to naturally specify the systems under study and their properties. In this paper, we present a framework for describing probabilistic models on top of nondeterministic specifications in the highly-expressive language Maude, based on rewriting logic. Quantitative properties can be checked and calculated on them using both probabilistic and statistical methods with external tools like PRISM, Storm, MultiVeSta, and custom implementations as backends. At the same time, the underlying nondeterministic system can be verified using the qualitative model-checking and deductive tools already available in Maude.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_15"}, {"primary_key": "1139397", "vector": [], "sparse_vector": [], "title": "The Next Big Thing: From Embedded Systems to Embodied Actors.", "authors": ["<PERSON>"], "summary": "Traditional engineering is coming to a junction fromembedded systemstoembodied actors, and with assuring the beneficial and robust behavior of dynamic federations of situation-aware, intent-driven, explorative, ever-evolving, and increasingly autonomous actors in uncertain and largely unpredictable real-world contexts. In our quest for a meaningful deployment of embodied actors in our societal fabric we are deriving central design challenges. A particular emphasis thereby is put on the role of formal methods for designing embodied systems in which we actually may put our trust.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_2"}, {"primary_key": "1139399", "vector": [], "sparse_vector": [], "title": "HHLPy: Practical Verification of Hybrid Systems Using Hoare Logic.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a tool for verification of hybrid systems expressed in the sequential fragment of HCSP (Hybrid Communicating Sequential Processes). The tool permits annotating HCSP programs with pre- and postconditions, invariants, and proof rules for reasoning about ordinary differential equations. Verification conditions are generated from the annotations following the rules of a Hoare logic for hybrid systems. We designed labeling and highlighting mechanisms to distinguish and visualize different verification conditions. The tool is implemented in Python and has a web-based user interface. We evaluated the effectiveness of the tool on translations of Simulink/Stateflow models and on KeYmaera X benchmarks.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_11"}, {"primary_key": "1139400", "vector": [], "sparse_vector": [], "title": "Formalising the Prevention of Microarchitectural Timing Channels by Operating Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Microarchitectural timing channels are a well-known mechanism for information leakage.Time protectionhas recently been demonstrated as an operating-system mechanism able to prevent them. However, established theories of information-flow security are insufficient for verifying time protection, which must distinguish between (legal) overt and (illegal) covert flows. We provide a machine-checked formalisation of time protection via a dynamic, observer-relative, intransitive nonleakage property over a careful model of the state elements that cause timing channels. We instantiate and prove our property over a generic model of OS interaction with its users, demonstrating for the first time the feasibility of proving time protection for OS implementations.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_8"}, {"primary_key": "1139402", "vector": [], "sparse_vector": [], "title": "Reasoning About Promises in Weak Memory Models with Event Structures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern processors such as ARMv8 and RISC-V allow executions in which independent instructions within a process may be reordered. To cope with such phenomena, so calledpromisingsemantics have been developed, which permit threads to read values that have not yet been written. Each promise is a speculative update that is later validated (fulfilled) by an actual write. Promising semantics are operational, providing a pathway for developing proof calculi. In this paper, we develop an incorrectness-style logic, resulting in a framework for reasoning about state reachability. Like incorrectness logic, our assertions areunderapproximating, since the set of all valid promises are not known at the start of execution. Our logic usesevent structuresas assertions to compactly represent the ordering among events such as promised and fulfilled writes. We prove soundness and completeness of our proof calculus and demonstrate its applicability by proving reachability properties of standard weak memory litmus tests.", "published": "2023-01-01", "category": "fm", "pdf_url": "", "sub_summary": "", "source": "fm", "doi": "10.1007/978-3-031-27481-7_17"}]