[{"primary_key": "3834367", "vector": [], "sparse_vector": [], "title": "The homomorphism problem for regular graph patterns.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The evaluation of conjunctive regular path queries - which form the navigational core of the query languages for graph databases - raises challenges in the context of the homomorphism problem that are not fully addressed by existing techniques. We start a systematic investigation of such challenges using a notion of homomorphism for regular graph patterns (RGPs). We observe that the RGP homomorphism problem cannot be reduced to known instances of the homomorphism problem, and new techniques need to be developed for its study. We first show that the non-uniform version of the problem is computationally harder than for the usual homomorphism problem. By establishing a connection between both problems, in turn, we postulate a dichotomy conjecture, analogous to the algebraic dichotomy conjecture held in CSP. We also look at which structural restrictions on left-hand side instances of the RGP homomorphism problem ensure efficiency. We study restrictions based on the notion of bounded treewidth modulo equivalence, which characterizes tractability for the usual homomorphism notion. We propose two such notions, based on different interpretations of RGP equivalence, and show that they both ensure the efficiency of the RGP homomorphism problem.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005106"}, {"primary_key": "3834368", "vector": [], "sparse_vector": [], "title": "The pebbling comonad in Finite Model Theory.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Pebble games are a powerful tool in the study of finite model theory, constraint satisfaction and database theory. Monads and comonads are basic notions of category theory which are widely used in semantics of computation and in modern functional programming. We show that existential k-pebble games have a natural comonadic formulation. Winning strategies for Duplicator in the k-pebble game for structures A and B are equivalent to morphisms from A to B in the coK<PERSON><PERSON><PERSON> category for this comonad. This leads on to comonadic characterisations of a number of central concepts in Finite Model Theory: · Isomorphism in the co-Kleisli category characterises elementary equivalence in the k-variable logic with counting quantifiers. · Symmetric games corresponding to equivalence in full k-variable logic are also characterized. · The treewidth of a structure A is characterised in terms of its coalgebra number: the least k for which there is a coalgebra structure on A for the k-pebbling comonad. · Co-Kleisli morphisms are used to characterize strong consistency, and to give an account of a Cai-<PERSON><PERSON><PERSON>-<PERSON> construction. · The k-pebbling comonad is also used to give semantics to a novel modal operator. These results lay the basis for some new and promising connections between two areas within logic in computer science which have largely been disjoint: (1) finite and algorithmic model theory, and (2) semantics and categorical structures of computation.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005129"}, {"primary_key": "3834369", "vector": [], "sparse_vector": [], "title": "Cut-free completeness for modal mu-calculus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present two finitary cut-free sequent calculi for the modal μ-calculus. One is a variant of <PERSON><PERSON>'s axiomatisation in which cut is replaced by a strengthening of the induction rule for greatest fixed point. The second calculus derives annotated sequents in the style of <PERSON>'s `tableau proof system with names' (2014) and features a generalisation of the ν-regeneration rule that allows discharging open assumptions. Soundness and completeness for the two calculi is proved by establishing a sequence of embeddings between proof systems, starting at <PERSON>'s tableau-proofs and ending at the original axiomatisation of the μ-calculus due to <PERSON><PERSON>. As a corollary we obtain a new, constructive, proof of completeness for <PERSON><PERSON>'s axiomatisation which avoids the usual detour through automata and games.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005088"}, {"primary_key": "3834370", "vector": [], "sparse_vector": [], "title": "Descriptive Complexity for counting complexity classes.", "authors": ["Marcelo <PERSON>s", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Descriptive Complexity has been very successful in characterizing complexity classes of decision problems in terms of the properties definable in some logics. However, descriptive complexity for counting complexity classes, such as FP and #P, has not been systematically studied, and it is not as developed as its decision counterpart. In this paper, we propose a framework based on Weighted Logics to address this issue. Specifically, by focusing on the natural numbers we obtain a logic called Quantitative Second Order Logics (QSO), and show how some of its fragments can be used to capture fundamental counting complexity classes such as FP, #P and FPSPACE, among others. We also use QSO to define a hierarchy inside #P, identifying counting complexity classes with good closure and approximation properties, and which admit natural complete problems. Finally, we add recursion to QSO, and show how this extension naturally captures lower counting complexity classes such as #L.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005150"}, {"primary_key": "3834371", "vector": [], "sparse_vector": [], "title": "Gödel logic: From natural deduction to parallel computation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Francesco A<PERSON>"], "summary": "Propositional Gödel logic G extends intuitionistic logic with the non-constructive principle of linearity (A → B) ∨ (B → A). We introduce a Curry-<PERSON> correspondence for G and show that a simple natural deduction calculus can be used as a typing system. The resulting functional language extends the simply typed λ-calculus via a synchronous communication mechanism between parallel processes, which increases its expressive power. The normalization proof employs original termination arguments and proof transformations implementing forms of code mobility. Our results provide a computational interpretation of G, thus proving <PERSON><PERSON>'s 1991 thesis.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005076"}, {"primary_key": "3834372", "vector": [], "sparse_vector": [], "title": "The clocks are ticking: No more delays!", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Guarded recursion in the sense of <PERSON><PERSON><PERSON> allows general recursive types and terms to be added to type theory without breaking consistency. Recent work has demonstrated applications of guarded recursion such as programming with codata, reasoning about coinductive types, as well as constructing and reasoning about denotational models of general recursive types. Guarded recursion can also be used as an abstract form of step-indexing for reasoning about programming languages with advanced features. Ultimately, we would like to have an implementation of a type theory with guarded recursion in which all these applications can be carried out and machine-checked. In this paper, we take a step towards this goal by devising a suitable reduction semantics. We present Clocked Type Theory, a new type theory for guarded recursion that is more suitable for reduction semantics than the existing ones. We prove confluence, strong normalisation and canonicity for its reduction semantics, constructing the theoretical basis for a future implementation. We show how coinductive types as exemplified by streams can be encoded, and derive that the type system ensures productivity of stream definitions.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005097"}, {"primary_key": "3834373", "vector": [], "sparse_vector": [], "title": "Domains and event structures for fusions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Stable event structures, and their duality with prime algebraic domains (arising as partial orders of configurations), are a landmark of concurrency theory, providing a clear characterisation of causality in computations. They have been used for defining a concurrent semantics of several formalisms, from Petri nets to linear graph rewriting systems, which in turn lay at the basis of many visual frameworks. Stability however is restrictive for dealing with formalisms where a computational step can merge parts of the state, like graph rewriting systems with non-linear rules, which are needed to cover some relevant applications (such as the graphical encoding of calculi with name passing). We characterise, as a natural generalisation of prime algebraic domains, a class of domains that is well-suited to model the semantics of formalisms with fusions. We then identify a corresponding class of event structures, that we call connected event structures, via a duality result formalised as an equivalence of categories.We show that connected event structures are exactly the class of event structures that arise as the semantics of nonlinear graph rewriting systems. Interestingly, the category of general unstable event structures coreflects into our category of domains, so that our result provides a characterisation of the partial orders of configurations of such event structures.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005135"}, {"primary_key": "3834374", "vector": [], "sparse_vector": [], "title": "Data structures for quasistrict higher categories.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present new data structures for quasistrict higher categories, in which associativity and unit laws hold strictly. Our approach has low axiomatic complexity compared to traditional algebraic approaches, and gives a practical method for performing calculations in quasistrict 4-categories. It is amenable to computer implementation, and we exploit this to give a machine-verified algebraic proof that every adjunction of 1-cells in a quasistrict 4-category can be promoted to a coherent adjunction satisfying the butterfly equations.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005147"}, {"primary_key": "3834375", "vector": [], "sparse_vector": [], "title": "The equivalence of two dichotomy conjectures for infinite domain constraint satisfaction problems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "There exist two conjectures for constraint satisfaction problems (CSPs) of reducts of finitely bounded homogeneous structures: the first one states that tractability of the CSP of such a structure is, when the structure is a model-complete core, equivalent to its polymorphism clone satisfying a certain non-trivial linear identity modulo outer embeddings. The second conjecture, challenging the approach via model-complete cores by reflections, states that tractability is equivalent to the linear identities (without outer embeddings) satisfied by its polymorphisms clone, together with the natural uniformity on it, being non-trivial. We prove that the identities satisfied in the polymorphism clone of a structure allow for conclusions about the orbit growth of its automorphism group, and apply this to show that the two conjectures are equivalent. We contrast this with a counterexample showing that ω-categoricity alone is insufficient to imply the equivalence of the two conditions above in a model-complete core. Taking a different approach, we then show how the Ramsey property of a homogeneous structure can be utilized for obtaining a similar equivalence under different conditions. We then prove that any polymorphism of sufficiently large arity which is totally symmetric modulo outer embeddings of a finitely bounded structure can be turned into a non-trivial system of linear identities, and obtain non-trivial linear identities for all tractable cases of reducts of the rational order, the random graph, and the random poset. Finally, we provide a new and short proof, in the language of monoids, of the theorem stating that every ω-categorical structure is homomorphically equivalent to a model-complete core.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005128"}, {"primary_key": "3834376", "vector": [], "sparse_vector": [], "title": "Untwisting two-way transducers in elementary time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Functional transductions realized by two-way transducers (equivalently, by streaming transducers and by MSO transductions) are the natural and standard notion of \"regular\" mappings from words to words. It was shown recently (LICS'13) that it is decidable if such a transduction can be implemented by some one-way transducer, but the given algorithm has non-elementary complexity. We provide an algorithm of different flavor solving the above question, that has double exponential space complexity. We further apply our technique to decide whether the transduction realized by a two-way transducer can be implemented by a sweeping transducer, with either known or unknown number of passes.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005138"}, {"primary_key": "3834377", "vector": [], "sparse_vector": [], "title": "Polynomial automata: Zeroness and applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a generalisation of weighted automata over a field, called polynomial automata, and we analyse the complexity of the Zeroness Problem in this model, that is, whether a given automaton outputs zero on all words. While this problem is non-primitive recursive in general, we highlight a subclass of polynomial automata for which the Zeroness Problem is primitive recursive. Refining further, we identify a subclass of affine VAS for which coverability is in 2EXPSPACE. We also use polynomial automata to obtain new proofs that equivalence of streaming string transducers is decidable, and that equivalence of copyless streaming string transducers is in PSPACE.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005101"}, {"primary_key": "3834378", "vector": [], "sparse_vector": [], "title": "Equivalence of inductive definitions and cyclic proofs under arithmetic.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A cyclic proof system, called CLKID-omega, gives us another way of representing inductive definitions and efficient proof search. The 2011 paper by <PERSON><PERSON> and <PERSON> showed that the provability of CLKID-omega includes the provability of the classical system of <PERSON><PERSON><PERSON><PERSON>'s inductive definitions, called LKID, and conjectured the equivalence. By this year the equivalence has been left an open question. In general, the conjecture was proved to be false in FoSSaCS 2017 paper by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. However, if we restrict both systems to only the natural number inductive predicate and add Peano arithmetic to both systems, the conjecture was proved to be true in FoSSaCS 2017 paper by <PERSON>. This paper shows that if we add arithmetic to both systems, they become equivalent, namely, the conjecture holds. The result of this paper includes that of the paper by <PERSON> as a special case. In order to construct a proof of LKID for a given cyclic proof, this paper shows every bud in the cyclic proof is provable in LKID, by cutting the cyclic proof into subproofs such that in each subproof the conclusion is a companion and the assumptions are buds. The global trace condition gives some induction principle, by using an extension of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> termination theorem from well-foundedness to induction schema. In order to prove this extension, this paper also shows that infinite Ramsey theorem is formalizable in Peano arithmetic.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005114"}, {"primary_key": "3834379", "vector": [], "sparse_vector": [], "title": "Strategy logic with imperfect information.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce an extension of Strategy logic for the imperfect-information setting, called SL ii , and study its model-checking problem. As this logic naturally captures multi-player games with imperfect information, the problem turns out to be undecidable. We introduce a syntactical class of \"hierarchical instances\" for which, intuitively, as one goes down the syntactic tree of the formula, strategy quantifications are concerned with finer observations of the model. We prove that model-checking SL ii restricted to hierarchical instances is decidable. This result, because it allows for complex patterns of existential and universal quantification on strategies, greatly generalises previous ones, such as decidability of multi-player games with imperfect information and hierarchical observations, and decidability of distributed synthesis for hierarchical systems. To establish the decidability result, we introduce and study QCTL ii *, an extension of QCTL (itself an extension of CTL with second-order quantification over atomic propositions) by parameterising its quantifiers with observations. The simple syntax of QCTL ii * allows us to provide a conceptually neat reduction of SL ii to QCTL ii * that separates concerns, allowing one to forget about strategies and players and focus solely on second-order quantification. While the model-checking problem of QCTL ii * is, in general, undecidable, we identify a syntactic fragment of hierarchical formulas and prove, using an automata-theoretic approach, that it is decidable. The decidability result for SL ii follows since the reduction maps hierarchical instances of SL ii to hierarchical formulas of QCTL ii *.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005136"}, {"primary_key": "3834380", "vector": [], "sparse_vector": [], "title": "Fully abstract encodings of λ-calculus in HOcore through abstract machines.", "authors": ["Malgorzata Biernacka", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present fully abstract encodings of the call-by-name λ-calculus into HOcore, a minimal higher-order process calculus with no name restriction. We consider several equivalences on the λ-calculus side - normal-form bisimilarity, applicative bisimilarity, and contextual equivalence - that we internalize into abstract machines in order to prove full abstraction.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005118"}, {"primary_key": "3834381", "vector": [], "sparse_vector": [], "title": "Foundational nonuniform (Co)datatypes for higher-order logic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Nonuniform (or \"nested\" or \"heterogeneous\") datatypes are recursively defined types in which the type arguments vary recursively. They arise in the implementation of finger trees and other efficient functional data structures. We show how to reduce a large class of nonuniform datatypes and codatatypes to uniform types in higher-order logic. We programmed this reduction in the Isabelle<PERSON>HOL proof assistant, thereby enriching its specification language. Moreover, we derive (co)induction and (co)recursion principles based on a weak variant of parametricity.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005071"}, {"primary_key": "3834382", "vector": [], "sparse_vector": [], "title": "Logics for continuous reachability in Petri nets and vector addition systems with states.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper studies sets of rational numbers definable by continuous Petri nets and extensions thereof. First, we identify a polynomial-time decidable fragment of existential FO(ℚ,+,<;) and show that the sets of rationals definable in this fragment coincide with reachability sets of continuous Petri nets. Next, we introduce and study continuous vector addition systems with states (CVASS), which are vector addition systems with states in which counters may hold non-negative rational values, and in which the effect of a transition can be scaled by a positive rational number smaller or equal to one. This class strictly generalizes continuous Petri nets by additionally allowing for discrete control-state information. We prove that reachability sets of CVASS are equivalent to the sets of rational numbers definable in existential FO(ℚ,+,<;) from which we can conclude that reachability in CVASS is NP-complete. Finally, our results explain and yield as corollaries a number of polynomial-time algorithms for decision problems that have recently been studied in the literature.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005068"}, {"primary_key": "3834383", "vector": [], "sparse_vector": [], "title": "An interpretation of system F through bar recursion.", "authors": ["<PERSON><PERSON>"], "summary": "There are two possible computational interpretations of second-order arithmetic: <PERSON><PERSON><PERSON>'s system F or <PERSON><PERSON><PERSON>'s bar recursion and its variants. While the logic is the same, the programs obtained from these two interpretations have a fundamentally different computational behavior and their relationship is not well understood. We make a step towards a comparison by defining the first translation of system F into a simply-typed total language with a variant of bar recursion. This translation relies on a realizability interpretation of second-order arithmetic. Due to <PERSON><PERSON><PERSON>'s incompleteness theorem there is no proof of termination of system F within second-order arithmetic. However, for each individual term of system F there is a proof in second-order arithmetic that it terminates, with its realizability interpretation providing a bound on the number of reduction steps to reach a normal form. Using this bound, we compute the normal form through primitive recursion. Moreover, since the normalization proof of system F proceeds by induction on typing derivations, the translation is compositional. The flexibility of our method opens the possibility of getting a more direct translation that will provide an alternative approach to the study of polymorphism, namely through bar recursion.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005066"}, {"primary_key": "3834384", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> property, finite quasi-Herbrand models, and a Chandra-<PERSON> theorem for quantified conjunctive queries.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A structure enjoys the Herb<PERSON> property if, whenever it satisfies an equality between some terms, these terms are unifiable. On such structures the expressive power of equalities becomes trivial, as their semantic satisfiability is reduced to a purely syntactic check. In this work, we introduce the notion of Herbrand property and develop it in a finite model-theoretic perspective. We provide, indeed, a canonical realization of the new concept by what we call quasi-Herbrand models and observe that, in stark contrast with the naive implementation of the property via standard Herbrand models, their universe can be finite even in presence of functions in the vocabulary. We exploit this feature to decide and collapse the general and finite version of the satisfiability and entailment problems for previously unsettled fragments of first-order logic. We take advantage of the <PERSON><PERSON> property also to establish novel and tight complexity results for the aforementioned decision questions. In particular, we show that the finite containment problem for quantified conjunctive queries is NPTIME-complete, tightening along two dimensions the known 3EXPTIME upper bound for the general version of the problem (<PERSON>, <PERSON>, and <PERSON>, LICS'08). We finally present an alternative view on this result by generalizing to such queries the classic characterization of conjunctive query containment via polynomial-time verifiable homomorphisms (<PERSON> and <PERSON>, STOC'77).", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005073"}, {"primary_key": "3834385", "vector": [], "sparse_vector": [], "title": "The real projective spaces in homotopy type theory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Homotopy type theory is a version of Martin-<PERSON><PERSON> type theory taking advantage of its homotopical models. In particular, we can use and construct objects of homotopy theory and reason about them using higher inductive types. In this article, we construct the real projective spaces, key players in homotopy theory, as certain higher inductive types in homotopy type theory. The classical definition of ℝP n , as the quotient space identifying antipodal points of the n-sphere, does not translate directly to homotopy type theory. Instead, we define ℝP n by induction on n simultaneously with its tautological bundle of 2-element sets. As the base case, we take ℝP -1 to be the empty type. In the inductive step, we take ℝP n+1 to be the mapping cone of the projection map of the tautological bundle of ℝP n , and we use its universal property and the univalence axiom to define the tautological bundle on ℝP n+1 . By showing that the total space of the tautological bundle of ℝP n is the n-sphere S n , we retrieve the classical description of ℝP n+1 as ℝP n with an (n + 1)-disk attached to it. The infinite dimensional real projective space ℝP ∞ , defined as the sequential colimit of ℝP n with the canonical inclusion maps, is equivalent to the Eilenberg-MacLane space K(ℤ/2ℤ, 1), which here arises as the subtype of the universe consisting of 2-element types. Indeed, the infinite dimensional projective space classifies the 0-sphere bundles, which one can think of as synthetic line bundles. These constructions in homotopy type theory further illustrate the utility of homotopy type theory, including the interplay of type theoretic and homotopy theoretic ideas.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005146"}, {"primary_key": "3834386", "vector": [], "sparse_vector": [], "title": "Constraint satisfaction problems over semilattice block Mal&apos;tsev algebras.", "authors": ["<PERSON>"], "summary": "There are two well known types of algorithms for solving CSPs: local propagation and generating a basis of the solution space. For several years the focus of the CSP research has been on `hybrid' algorithms that somehow combine the two approaches. In this paper we present a new method of such hybridization that allows us to solve certain CSPs that has been out of reach for a quite a while. We consider these method on a fairly restricted class of CSPs given by algebras we will call semilattice block Mal'tsev. An algebra A is called semilattice block Mal'tsev if it has a binary operation f, a ternary operation m, and a congruence σ such that the quotient A/ σ with operation f is a semilattice, f is a projection on every block of σ, and every block of σ is a Mal'tsev algebra with Mal'tsev operation m. We show that the constraint satisfaction problem over a semilattice block Mal'tsev algebra is solvable in polynomial time.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005149"}, {"primary_key": "3834387", "vector": [], "sparse_vector": [], "title": "A crevice on the Crane Beach: Finite-degree predicates.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "First-order logic (FO) over words is shown to be equiexpressive with FO equipped with a restricted set of numerical predicates, namely the order, a binary predicate MSB 0 , and the finite-degree predicates: FO[A RB ] = FO[≤, MSB 0 , F IN ]. The Crane Beach Property (CBP), introduced more than a decade ago, is true of a logic if all the expressible languages admitting a neutral letter are regular. Although it is known that FO[A RB ] does not have the CBP, it is shown here that the (strong form of the) CBP holds for both FO[≤, F IN ] and FO[≤, MSB 0 ]. Thus FO[≤, F IN ] exhibits a form of locality and the CBP, and can still express a wide variety of languages, while being one simple predicate away from the expressive power of FO[A RB ]. The counting ability of FO[≤, F IN ] is studied as an application.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005148"}, {"primary_key": "3834388", "vector": [], "sparse_vector": [], "title": "Common knowledge and multi-scale locality analysis in Cayley structures.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We investigate multi-agent epistemic modal logic with common knowledge modalities for groups of agents and obtain <PERSON> style model-theoretic characterisations, in terms of bisimulation invariance of classical first-order logic over the non-elementary classes of (finite or arbitrary) common knowledge Kripke frames. The fixpoint character of common knowledge modalities and the rôle that reachability and transitive closures play for the derived accessibility relations take our analysis beyond classical model-theoretic terrain and technically pose a novel challenge to the analysis of model-theoretic games. Over and above the more familiar locality-based techniques we exploit a specific structure theory for specially adapted Cayley groups: through the association of agents with sets of generators, all epistemic frames can be represented up to bisimilarity by suitable Cayley groups with specific acyclicity properties; these support a locality analysis at different levels of granularity as induced by distance measures w.r.t. various coalitions of agents.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005072"}, {"primary_key": "3834389", "vector": [], "sparse_vector": [], "title": "Understanding the complexity of #SAT using knowledge compilation.", "authors": ["Florent <PERSON>"], "summary": "Two main techniques have been used so far to solve the #P-hard problem #SAT. The first one, used in practice, is based on an extension of DPLL for model counting called exhaustive DPLL. The second approach, more theoretical, exploits the structure of the input to compute the number of satisfying assignments by usually using a dynamic programming scheme on a decomposition of the formula. In this paper, we make a first step toward the separation of these two techniques by exhibiting a family of formulas that can be solved in polynomial time with the first technique but needs an exponential time with the second one. We show this by observing that both techniques implicitly construct a very specific Boolean circuit equivalent to the input formula. We then show that every β-acyclic formula can be represented by a polynomial size circuit corresponding to the first method and exhibit a family of β-acyclic formulas which cannot be represented by polynomial size circuits corresponding to the second method. This result sheds a new light on the complexity of #SAT and related problems on β-acyclic formulas. As a byproduct, we give new handy tools to design algorithms on β-acyclic hypergraphs.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005121"}, {"primary_key": "3834390", "vector": [], "sparse_vector": [], "title": "The continuity of monadic stream functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>'s continuity principle states that all functions from infinite sequences of naturals to naturals are continuous, that is, for every sequence the result depends only on a finite initial segment. It is an intuitionistic axiom that is incompatible with classical mathematics. Recently <PERSON> proved that it is also inconsistent in type theory. This shows that we cannot internalize the meta-theoretical observation that every definable function is continuous. However, we can adapt <PERSON><PERSON><PERSON>'s ideas to an important class of functions and propose a reformulation of the continuity principle that is internally provable. We note that <PERSON><PERSON><PERSON> talked about functions on choice sequences, which are described as free progressions of values not necessarily generated by a rule. The functions must produce their results independently of how the sequences are generated. We formalize them as monadic streams, potentially unending sequences of values produced by steps triggered by a monadic action, possibly involving side effects. We consider functions on them that are uniform, in the sense that they operate in the same way independently of the particular monad that provides the specific side effects. Formally this is done by requiring a form of naturality in the monad. Functions on monadic streams have not only a foundational importance, but have also practical applications in signal processing and reactive programming. We give algorithms to determine the modulus of continuity of monadic stream functions and to generate dialogue trees for them (trees whose nodes and branches describe the interaction of the process with the environment).", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005119"}, {"primary_key": "3834391", "vector": [], "sparse_vector": [], "title": "Foundation for a series of efficient simulation algorithms.", "authors": ["<PERSON><PERSON>"], "summary": "Compute the coarsest simulation preorder included in an initial preorder is used to reduce the resources needed to analyze a given transition system. This technique is applied on many models like Kripke structures, labeled graphs, labeled transition systems or even word and tree automata. Let (Q, $\\rightarrow$) be a given transition system and <PERSON>init be an initial preorder over Q. Until now, algorithms to compute Rsim , the coarsest simulation included in Rinit , are either memory efficient or time efficient but not both. In this paper we propose the foundation for a series of efficient simulation algorithms with the introduction of the notion of maximal transitions and the notion of stability of a preorder with respect to a coarser one. As an illustration we solve an open problem by providing the first algorithm with the best published time complexity, O(|Psim |.|$\\rightarrow$|), and a bit space complexity in O(|Psim |^2. log(|Psim |) + |Q|. log(|Q|)), with Psim the partition induced by Rsim.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005069"}, {"primary_key": "3834392", "vector": [], "sparse_vector": [], "title": "On shift-invariant maximal filters and hormonal cellular automata.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper deals with the construction of shift-invariant maximal filters on ℤ and their relation to hormonal cellular automata, a generalization of the cellular automata computation model with some information about the global state shared among all the cells. We first design shift-invariant maximal filters in order to define this new model of computation. Starting from different assumptions, we show how to construct such filters, and analyze the computation power of the induced cellular automata computation model.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005145"}, {"primary_key": "3834393", "vector": [], "sparse_vector": [], "title": "Verification of randomized security protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of verifying the security of finitely many sessions of a protocol that tosses coins in addition to standard cryptographic primitives against a Dolev-Yao adversary. Two properties are investigated here - secrecy, which asks if no adversary interacting with a protocol P can determine a secret sec with probability > 1 - p; and indistinguishability, which asks if the probability observing any sequence 0̅ in P 1 is the same as that of observing 0̅ in P 2 , under the same adversary. Both secrecy and indistinguishability are known to be coNP-complete for non-randomized protocols. In contrast, we show that, for randomized protocols, secrecy and indistinguishability are both decidable in coNEXPTIME. We also prove a matching lower bound for the secrecy problem by reducing the non-satisfiability problem of monadic first order logic without equality.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005126"}, {"primary_key": "3834394", "vector": [], "sparse_vector": [], "title": "Register automata with linear arithmetic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a novel automata model over the alphabet of rational numbers, which we call register automata over the rationals (RA ℚ ). It reads a sequence of rational numbers and outputs another rational number. RA ℚ is an extension of the well-known register automata (RA) over infinite alphabets, which are finite automata equipped with a finite number of registers/variables for storing values. Like in the standard RA, the RA ℚ model allows both equality and ordering tests between values. It, moreover, allows to perform linear arithmetic between certain variables. The model is quite expressive: in addition to the standard RA, it also generalizes other well-known models such as affine programs and arithmetic circuits. The main feature of RA ℚ is that despite the use of linear arithmetic, the so-called invariant problem-a generalization of the standard non-emptiness problem-is decidable. We also investigate other natural decision problems, namely, commutativity, equivalence, and reachability. For deterministic RA ℚ , commutativity and equivalence are polynomial-time inter-reducible with the invariant problem.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005111"}, {"primary_key": "3834395", "vector": [], "sparse_vector": [], "title": "The logic of counting query answers.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of counting the number of answers to a first-order formula on a finite structure. We present and study an extension of first-order logic in which algorithms for this counting problem can be naturally and conveniently expressed, in senses that are made precise and that are motivated by the wish to understand tractable cases of the counting problem.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005085"}, {"primary_key": "3834396", "vector": [], "sparse_vector": [], "title": "Timed pushdown automata and branching vector addition systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove that non-emptiness of timed register pushdown automata is decidable in doubly exponential time. This is a very expressive class of automata, whose transitions may involve state and top-of-stack clocks with unbounded differences. It strictly subsumes pushdown timed automata of <PERSON><PERSON><PERSON><PERSON><PERSON> et al., dense-timed pushdown automata of <PERSON><PERSON> et al., and orbit-finite timed register pushdown automata of Clement<PERSON> and Lasota. Along the way, we prove two further decidability results of independent interest: for non-emptiness of least solutions to systems of equations over sets of integers with addition, union and intersections with ℕ and -ℕ, and for reachability in one-dimensional branching vector addition systems with states and subtraction, both in exponential time.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005083"}, {"primary_key": "3834397", "vector": [], "sparse_vector": [], "title": "Logic and regular cost functions.", "authors": ["<PERSON>"], "summary": "Regular cost functions offer a toolbox for automatically solving problems of existence of bounds, in a way similar to the theory of regular languages. More precisely, it allows to test the existence of bounds for quantities that can be defined in cost monadic second-order logic (a quantitative variant of monadic second-order logic) with inputs that range over finite words, infinite words, finite trees, and (sometimes) infinite trees. Though the initial results date from the works of <PERSON><PERSON><PERSON> in the early eighties, it is during the last decade that the theory took its current shape and that many new results and applications have been established. In this tutorial, two connections linking logic with the theory of regular cost functions will be described. The first connection is a proof of a result of <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> stating that it is decidable whether the fixpoint of a monadic secondorder formula is reached within a bounded number of iterations over the class of infinite trees. The second connection is how nonstandard models (and more precisely non-standard analysis) give rise to a unification of the theory of regular cost functions with the one of regular languages.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005061"}, {"primary_key": "3834398", "vector": [], "sparse_vector": [], "title": "Perfect half space games.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce perfect half space games, in which the goal of Player 2 is to make the sums of encountered multi-dimensional weights diverge in a direction which is consistent with a chosen sequence of perfect half spaces (chosen dynamically by Player 2). We establish that the bounding games of <PERSON><PERSON><PERSON> et al. (ICALP 2015) can be reduced to perfect half space games, which in turn can be translated to the lexicographic energy games of <PERSON><PERSON><PERSON> and <PERSON>wi\\'nski, and are positionally determined in a strong sense (Player 2 can play without knowing the current perfect half space). We finally show how perfect half space games and bounding games can be employed to solve multi-dimensional energy parity games in pseudo-polynomial time when both the numbers of energy dimensions and of priorities are fixed, regardless of whether the initial credit is given as part of the input or existentially quantified. This also yields an optimal 2-EXPTIME complexity with given initial credit, where the best known upper bound was non-elementary.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005105"}, {"primary_key": "3834399", "vector": [], "sparse_vector": [], "title": "Stack semantics of type theory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We give a model of dependent type theory with one univalent universe and propositional truncation interpreting a type as a stack, generalizing the groupoid model of type theory. As an application, we show that countable choice cannot be proved in dependent type theory with one univalent universe and propositional truncation.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005130"}, {"primary_key": "3834400", "vector": [], "sparse_vector": [], "title": "Regular separability of one counter automata.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The regular separability problem asks, for two given languages, if there exists a regular language including one of them but disjoint from the other. Our main result is decidability, and PSPACE-completeness, of the regular separability problem for languages of one counter automata without zero tests (also known as one counter nets). This contrasts with undecidability of the regularity problem for one counter nets, and with undecidability of the regular separability problem for one counter automata, which is our second result.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005079"}, {"primary_key": "3834401", "vector": [], "sparse_vector": [], "title": "Definability of semidefinite programming and lasserre lower bounds for CSPs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that the ellipsoid method for solving semidefinite programs (SDPs) can be expressed in fixed-point logic with counting (FPC). This generalizes an earlier result that the optimal value of a linear program can be expressed in this logic. As an application, we establish lower bounds on the number of levels of the Lasserre hierarchy required to solve many optimization problems, namely those that can be expressed as finite-valued constraint satisfaction problems (VCSPs). In particular, we establish a dichotomy on the number of levels of the Lasserre hierarchy that are required to solve the problem exactly. We show that if a finite-valued constraint problem is not solved exactly by its basic linear programming relaxation, it is also not solved exactly by any sub-linear number of levels of the Lasserre hierarchy. The lower bounds are established through logical undefinability results. We show that the SDP corresponding to any fixed level of the Lasserre hierarchy is interpretable in a VCSP instance by means of FPC formulas. Our definability result of the ellipsoid method then implies that the solution of this SDP can be expressed in this logic. Together, these results give a way of translating lower bounds on the number of variables required in counting logic to express a VCSP into lower bounds on the number of levels required in the Lasserre hierarchy to eliminate the integrality gap. As a special case, we obtain the same dichotomy for the class of MAXCSP problems, generalizing earlier Lasserre lower bound results by <PERSON><PERSON><PERSON><PERSON> [17]. Recently, and independently of the work reported here, a similar linear lower bound in the Lasserre hierarchy for general-valued CSPs has also been announced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [20], using different techniques.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005108"}, {"primary_key": "3834402", "vector": [], "sparse_vector": [], "title": "Constructive completeness for the linear-time μ-calculus.", "authors": ["<PERSON><PERSON>"], "summary": "We give a new proof of completeness for the linear-time μ-calculus w.<PERSON><PERSON><PERSON><PERSON>'s axiomatization. Our proof has the advantage of being constructive, i.e., it builds a proof for every valid formula.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005075"}, {"primary_key": "3834403", "vector": [], "sparse_vector": [], "title": "Typability in bounded dimension.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Recently (authors, POPL 2017), a notion of dimensionality for intersection types was introduced, and it was shown that the bounded-dimensional inhabitation problem is decidable under a non-idempotent interpretation of intersection and undecidable in the standard set-theoretic model. In this paper we study the typability problem for bounded-dimensional intersection types and prove that the problem is decidable in both models. We establish a number of bounding principles depending on dimension. In particular, it is shown that dimensional bound on derivations gives rise to a bounded width property on types, which is related to a generalized subformula property for typings of arbitrary terms. Using the bounded width property we can construct a nondeterministic transformation of the typability problem to unification, and we prove that typability in the set-theoretic model is PSPACE-complete, whereas it is in NP in the multiset model.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005127"}, {"primary_key": "3834404", "vector": [], "sparse_vector": [], "title": "Differentiation in logical form.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a logical theory of differentiation for a real-valued function on a finite dimensional real Euclidean space. A real-valued continuous function is represented by a localic approximable mapping between two semi-strong proximity lattices, representing the two stably locally compact Euclidean spaces for the domain and the range of the function. Similarly, the Clarke subgradient, equivalently the L-derivative, of a locally Lipschitz map, which is non-empty, compact and convex valued, is represented by an approximable mapping. Approximable mappings of the latter type form a bounded complete domain isomorphic with the function space of Scott continuous functions of a real variable into the domain of non-empty compact and convex subsets of the finite dimensional Euclidean space partially ordered with reverse inclusion. Corresponding to the notion of a single-tie of a locally Lipschitz function, used to derive the domain-theoretic L-derivative of the function, we introduce the dual notion of a single-knot of approximable mappings which gives rise to Lipschitzian approximable mappings. We then develop the notion of a strong single-tie and that of a strong knot leading to a Stone duality result for locally Lipschitz maps and Lipschitzian approximable mappings. The strong single-knots, in which a Lipschitzian approximable mapping belongs, are employed to define the Lipschitzian derivative of the approximable mapping. The latter is dual to the <PERSON> subgradient of the corresponding locally Lipschitz map defined domain-theoretically using strong single-ties. A stricter notion of strong single-knots is subsequently developed which captures approximable mappings of continuously differentiable maps providing a gradient Stone duality for these maps. Finally, we derive a calculus for Lipschitzian derivative of approximable mapping for some basic constructors and show that it is dual to the calculus satisfied by the Clarke subgradient.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005143"}, {"primary_key": "3834405", "vector": [], "sparse_vector": [], "title": "Static analysis of deterministic negotiations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Negotiation diagrams are a model of concurrent computation akin to workflow Petri nets. Deterministic negotiation diagrams, equivalent to the much studied and used free-choice workflow Petri nets, are surprisingly amenable to verification. Soundness (a property close to deadlock-freedom) can be decided in PTIME. Further, other fundamental questions like computing summaries or the expected cost, can also be solved in PTIME for sound deterministic negotiation diagrams, while they are PSPACE-complete in the general case.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005144"}, {"primary_key": "3834406", "vector": [], "sparse_vector": [], "title": "Algorithms for some infinite-state MDPs and stochastic games.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "I will survey a body of work, developed over the past decade or so, on algorithms for, and the computational complexity of, analyzing and model checking some important families of countably infinite-state Markov chains, Markov decision processes (MDPs), and stochastic games. These models arise by adding natural forms of recursion, branching, or a counter, to finite-state models, and they correspond to probabilistic/control/game extensions of classic automata-theoretic models like pushdown automata, context-free grammars, and one-counter automata. They subsume some classic stochastic processes such as multi-type branching processes and quasi-birth-death processes. They also provide a natural model for probabilistic procedural programs with recursion.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005063"}, {"primary_key": "3834407", "vector": [], "sparse_vector": [], "title": "On delay and regret determinization of max-plus automata.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Decidability of the determinization problem for weighted automata over the semiring (ℤ∪{−∞}, max; +), WA for short, is a long-standing open question. We propose two ways of approaching it by constraining the search space of deterministic WA: k-delay and r-regret. A WA N is k-delay determinizable if there exists a deterministic automaton D that defines the same function as N and for all words α in the language of N, the accepting run of D on α is always at most k-away from a maximal accepting run of N on α. That is, along all prefixes of the same length, the absolute difference between the running sums of weights of the two runs is at most k. A WA N is r-regret determinizable if for all words α in its language, its non-determinism can be resolved on the fly to construct a run of N such that the absolute difference between its value and the value assigned to α by N is at most r. We show that a WA is determinizable if and only if it is k-delay determinizable for some k. Hence deciding the existence of some k is as difficult as the general determinization problem. When k and r are given as input, the k-delay and r-regret determinization problems are shown to be EXPTIME-complete. We also show that determining whether a WA is r-regret determinizable for some r is in EXPTIME.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005096"}, {"primary_key": "3834408", "vector": [], "sparse_vector": [], "title": "A type-theoretical definition of weak ω-categories.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce a dependent type theory whose models are weak ω-categories, generalizing <PERSON><PERSON><PERSON>'s definition of ω-groupoids. Our type theory is based on the definition of ω-categories given by <PERSON><PERSON><PERSON><PERSON><PERSON>, himself inspired by <PERSON><PERSON><PERSON><PERSON><PERSON>'s approach to the definition of ω-groupoids. In this setup, ω-categories are defined as presheaves preserving globular colimits over a certain category, called a coherator. The coherator encodes all operations required to be present in an ω-category: both the compositions of pasting schemes as well as their coherences. Our main contribution is to provide a canonical type-theoretical characterization of pasting schemes as contexts which can be derived from inference rules. Finally, we present an implementation of a corresponding proof system.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005124"}, {"primary_key": "3834409", "vector": [], "sparse_vector": [], "title": "Unrestricted stone duality for Markov processes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Stone duality relates logic, in the form of Boolean algebra, to spaces. Stone-type dualities abound in computer science and have been of great use in understanding the relationship between computational models and the languages used to reason about them. Recent work on probabilistic processes has established a Stone-type duality for a restricted class of Markov processes. The dual category was a new notion-Aumann algebras-which are Boolean algebras equipped with countable family of modalities indexed by rational probabilities. In this article we consider an alternative definition of Aumann algebra that leads to dual adjunction for Markov processes that is a duality for many measurable spaces occurring in practice. This extends a duality for measurable spaces due to <PERSON><PERSON><PERSON>. In particular, we do not require that the probabilistic modalities preserve a distinguished base of clopen sets, nor that morphisms of Markov processes do so. The extra generality allows us to give a perspicuous definition of event bisimulation on Aumann algebras.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005152"}, {"primary_key": "3834410", "vector": [], "sparse_vector": [], "title": "Quantifiers on languages and codensity monads.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper contributes to the techniques of topoalgebraic recognition for languages beyond the regular setting as they relate to logic on words. In particular, we provide a general construction on recognisers corresponding to adding one layer of various kinds of quantifiers and prove a related Reute<PERSON>uer-type theorem. Our main tools are codensity monads and duality theory. Our construction yields, in particular, a new characterisation of the profinite monad of the free S-semimodule monad for finite and commutative semirings S, which generalises our earlier insight that the Vietoris monad on Boolean spaces is the codensity monad of the finite powerset functor.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005140"}, {"primary_key": "3834411", "vector": [], "sparse_vector": [], "title": "Lean and full congruence formats for recursion.", "authors": ["<PERSON>"], "summary": "In this paper I distinguish two (pre)congruence requirements for semantic equivalences and preorders on processes given as closed terms in a system description language with a recursion construct. A lean congruence preserves equivalence when replacing closed subexpressions of a process by equivalent alternatives. A full congruence moreover allows replacement within a recursive specification of subexpressions that may contain recursion variables bound outside of these subexpressions. I establish that bisimilarity is a lean (pre)congruence for recursion for all languages with a structural operational semantics in the ntyft/ntyxt format. Additionally, it is a full congruence for the tyft/tyxt format.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005142"}, {"primary_key": "3834412", "vector": [], "sparse_vector": [], "title": "Descriptive complexity of linear equation systems and applications to propositional proof complexity.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We prove that the solvability of systems of linear equations and related linear algebraic properties are definable in a fragment of fixed-point logic with counting that only allows polylogarithmically many iterations of the fixed-point operators. This enables us to separate the descriptive complexity of solving linear equations from full fixed-point logic with counting by logical means. As an application of these results, we separate an extension of first-order logic with a rank operator from fixed-point logic with counting, solving an open problem due to <PERSON><PERSON> [21]. We then draw a connection from this work in descriptive complexity theory to graph isomorphism testing and propositional proof complexity. Answering an open question from [7], we separate the strength of certain algebraic graph-isomorphism tests. This result can also be phrased as a separation of the algebraic propositional proof systems \"Nullstellensatz\" and \"monomial PC\".", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005081"}, {"primary_key": "3834413", "vector": [], "sparse_vector": [], "title": "Learning first-order definable concepts over structures of small degree.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider a declarative framework for machine learning where concepts and hypotheses are defined by formulas of a logic over some \"background structure\". We show that within this framework, concepts defined by first-order formulas over a background structure of at most polylogarithmic degree can be learned in polylogarithmic time in the \"probably approximately correct\" learning sense.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005080"}, {"primary_key": "3834414", "vector": [], "sparse_vector": [], "title": "Capturing polynomial time using Modular Decomposition.", "authors": ["<PERSON><PERSON>"], "summary": "The question of whether there is a logic that captures polynomial time is one of the main open problems in descriptive complexity theory and database theory. In 2010 <PERSON><PERSON><PERSON> showed that fixed point logic with counting captures polynomial time on all classes of graphs with excluded minors. We now consider classes of graphs with excluded induced subgraphs. For such graph classes, an effective graph decomposition, called modular decomposition, was introduced by <PERSON><PERSON><PERSON> in 1976. The graphs that are non-decomposable with respect to modular decomposition are called prime. We present a tool, the Modular Decomposition Theorem, that reduces (definable) canonization of a graph class C to (definable) canonization of the class of prime graphs of C that are colored with binary relations on a linearly ordered set. By an application of the Modular Decomposition Theorem, we show that fixed point logic with counting also captures polynomial time on the class of permutation graphs. As a side effect of the Modular Decomposition Theorem, we further obtain that the modular decomposition tree is computable in logarithmic space. It follows that cograph recognition and cograph canonization is computable in logarithmic space.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005123"}, {"primary_key": "3834415", "vector": [], "sparse_vector": [], "title": "Computing quantiles in Markov chains with multi-dimensional costs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Probabilistic systems that accumulate quantities such as energy or cost are naturally modelled by cost chains, which are Markov chains whose transitions are labelled with a vector of numerical costs. Computing information on the probability distribution of the total accumulated cost is a fundamental problem in this model. In this paper, we study the so-called cost problem, which is to compute quantiles of the total cost, such as the median cost or the probability of large costs. While it is an open problem whether such probabilities are always computable or even rational, we present an algorithm that allows to approximate the probabilities with arbitrary precision. The algorithm is simple to state and implement, and exploits strong results from graph theory such as the so-called BEST theorem for efficiently computing the number of Eulerian circuits in a directed graph. Moreover, our algorithm enables us to show that a decision version of the cost problem lies in the counting hierarchy, a counting analogue to the polynomial-time hierarchy that contains the latter and is included in PSPACE. Finally, we demonstrate the applicability of our algorithm by evaluating it experimentally.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005090"}, {"primary_key": "3834416", "vector": [], "sparse_vector": [], "title": "Decidability, complexity, and expressiveness of first-order logic over the subword ordering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider first-order logic over the subword ordering on finite words where each word is available as a constant. Our first result is that the Σ 1 theory is undecidable (already over two letters). We investigate the decidability border by considering fragments where all but a certain number of variables are alternation bounded, meaning that the variable must always be quantified over languages with a bounded number of letter alternations. We prove that when at most two variables are not alternation bounded, the Σ 1 fragment is decidable, and that it becomes undecidable when three variables are not alternation bounded. Regarding higher quantifier alternation depths, we prove that the Σ 2 fragment is undecidable already for one variable without alternation bound and that when all variables are alternation bounded, the entire first-order theory is decidable.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005141"}, {"primary_key": "3834417", "vector": [], "sparse_vector": [], "title": "Foundations of information integration under bag semantics.", "authors": ["<PERSON>", "Phokion <PERSON>"], "summary": "During the past several decades, the database theory community has successfully investigated several different facets of the principles of database systems, including the development of various data models, the systematic exploration of the expressive power of database query languages, and, more recently, the study of the foundations of information integration via schema mappings. For the most part, all these investigations have been carried out under set semantics, that is, both the database relations and the answers to database queries are sets. In contrast, SQL deploys bag (multiset) semantics and, as a result, theory and practice diverge at this crucial point. Our main goal in this paper is to embark on the development of the foundations of information integration under bag semantics, thus taking the first step towards bridging the gap between theory and practice in this area. Our first contribution is conceptual, namely, we give rigorous bag semantics to GLAV mappings and to the certain answers of conjunctive queries in the context of data exchange and data integration. In fact, we introduce and explore two different versions of bag semantics that, intuitively, correspond to the maximum-based union of bags and to the sum-based union of bags. After this, we establish a number of technical results, including results about the computational complexity of the certain answers of conjunctive queries under bag semantics and about the existence and computation of universal solutions under these two versions of bag semantics. Our results reveal that the adoption of more realistic semantics comes at a price, namely, algorithmic problems in data exchange and data integration that were tractable under set semantics become intractable under bag semantics.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005104"}, {"primary_key": "3834418", "vector": [], "sparse_vector": [], "title": "A convenient category for higher-order probability theory.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Higher-order probabilistic programming languages allow programmers to write sophisticated models in machine learning and statistics in a succinct and structured way, but step outside the standard measure-theoretic formalization of probability theory. Programs may use both higher-order functions and continuous distributions, or even define a probability distribution on functions. But standard probability theory does not handle higher-order functions well: the category of measurable spaces is not cartesian closed. Here we introduce quasi-Borel spaces. We show that these spaces: form a new formalization of probability theory replacing measurable spaces; form a cartesian closed category and so support higher-order functions; form a well-pointed category and so support good proof principles for equational reasoning; and support continuous probability distributions. We demonstrate the use of quasi-Borel spaces for higher-order functions and probability by: showing that a well-known construction of probability theory involving random functions gains a cleaner expression; and generalizing <PERSON>'s theorem, that is a crucial theorem in probability theory, to quasi-Borel spaces.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005137"}, {"primary_key": "3834419", "vector": [], "sparse_vector": [], "title": "Model-checking for successor-invariant first-order formulas on graph classes of bounded expansion.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A successor-invariant first-order formula is a formula that has access to an auxiliary successor relation on a structure's universe, but the model relation is independent of the particular interpretation of this relation. It is well known that successor-invariant formulas are more expressive on finite structures than plain first-order formulas without a successor relation. This naturally raises the question whether this increase in expressive power comes at an extra cost to solve the model-checking problem, that is, the problem to decide whether a given structure together with some (and hence every) successor relation is a model of a given formula. It was shown earlier that adding successor-invariance to first-order logic essentially comes at no extra cost for the model-checking problem on classes of finite structures whose underlying Gaifman graph is planar [1], excludes a fixed minor [2] or a fixed topological minor [3], [4]. In this work we show that the model-checking problem for successor-invariant formulas is fixed-parameter tractable on any class of finite structures whose underlying Gaifman graphs form a class of bounded expansion. Our result generalises all earlier results and comes close to the best tractability results on nowhere dense classes of graphs currently known for plain first-order logic.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005115"}, {"primary_key": "3834420", "vector": [], "sparse_vector": [], "title": "Linear combinations of unordered data vectors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Data vectors generalise finite multisets: they are finitely supported functions into a commutative monoid. We study the question whether a given data vector can be expressed as a finite sum of others, only assuming that 1) the domain is countable and 2) the given set of base vectors is finite up to permutations of the domain. Based on a succinct representation of the involved permutations as integer linear constraints, we derive that positive instances can be witnessed in a bounded subset of the domain. For data vectors over a group we moreover study when a data vector is reversible, that is, if its inverse is expressible using only nonnegative coefficients. We show that if all base vectors are reversible then the expressibility problem reduces to checking membership in finitely generated subgroups. Moreover, checking reversibility also reduces to such membership tests. These questions naturally appear in the analysis of counter machines extended with unordered data: namely, for data vectors over (ℤ d , +) expressibility directly corresponds to checking state equations for Coloured Petri nets where tokens can only be tested for equality. We derive that in this case, expressibility is in NP, and in P for reversible instances. These upper bounds are tight: they match the lower bounds for standard integer vectors (over singleton domains).", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005065"}, {"primary_key": "3834421", "vector": [], "sparse_vector": [], "title": "A cartesian-closed category for higher-order model checking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In previous work we have described the construction of an abstract lattice from a given Büchi automaton. The abstract lattice is finite and has the following key properties. (i) There is a Galois insertion between it and the lattice of languages of finite and infinite words over a given alphabet. (ii) The abstraction is faithful with respect to acceptance by the automaton. (iii) Least fixpoints and ω-iterations (but not in general greatest fixpoints) can be computed on the level of the abstract lattice. This allows one to decide whether finite and infinite traces of first-order recursive boolean programs are accepted by the automaton and can further be used to derive a type-and-effect system for infinitary properties. In this paper, we show how to derive from the abstract lattice a cartesian-closed category with fixpoint operator in such a way that the interpretation of a higher-order recursive program yields precisely the abstraction of its set of finite and infinite traces and thus provides a new algorithm for the higher-order model checking problem for trace properties. All previous algorithms for higher-order model checking [2], [16] work inherently on arbitrary tree properties and no apparent simplification appears when instantiating them with trace properties. The algorithm presented here, while necessarily having the same asymptotic complexity, is considerably simpler since it merely involves the interpretation of the program in a cartesian-closed category. The construction of the cartesian closed category from a lattice is new as well and may be of independent interest.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005120"}, {"primary_key": "3834422", "vector": [], "sparse_vector": [], "title": "On the extension of computable real functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate interrelationships among different notions from mathematical analysis, effective topology, and classical computability theory. Our main object of study is the class of computable functions defined over an interval with the boundary being a left-c.e. real number. We investigate necessary and sufficient conditions under which such functions can be computably extended. It turns out that this depends on the behavior of the function near the boundary as well as on the class of left-c.e. real numbers to which the boundary belongs, that is, how it can be constructed. Of particular interest a class of functions is investigated: sawtooth functions constructed from computable enumerations of c.e. sets.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005067"}, {"primary_key": "3834423", "vector": [], "sparse_vector": [], "title": "Enumeration reducibility in closure spaces with applications to logic and algebra.", "authors": ["<PERSON>"], "summary": "In many instances in first order logic or computable algebra, classical theorems show that many problems are undecidable for general structures, but become decidable if some rigidity is imposed on the structure. For example, the set of theorems in many finitely axiomatisable theories is nonrecursive, but the set of theorems for any finitely axiomatisable complete theory is recursive. Finitely presented groups might have an nonrecursive word problem, but finitely presented simple groups have a recursive word problem.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005086"}, {"primary_key": "3834424", "vector": [], "sparse_vector": [], "title": "Succinct progress measures for solving parity games.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The recent breakthrough paper by <PERSON><PERSON> et al. has given the first algorithm for solving parity games in quasi-polynomial time, where previously the best algorithms were mildly subexponential. We devise an alternative quasi-polynomial time algorithm based on progress measures, which allows us to reduce the space required from quasi-polynomial to nearly linear. Our key technical tools are a novel concept of ordered tree coding, and a succinct tree coding result that we prove using bounded adaptive multi-counters, both of which are interesting in their own right.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005092"}, {"primary_key": "3834425", "vector": [], "sparse_vector": [], "title": "A weakest pre-expectation semantics for mixed-sign expectations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a weakest-precondition-style calculus for reasoning about the expected values (pre-expectations) of mixed-sign unbounded random variables after execution of a probabilistic program. The semantics of a while-loop is defined as the limit of iteratively applying a functional to a zero-element just as in the traditional weakest pre-expectation calculus, even though a standard least fixed point argument is not applicable in our semantics. A striking feature of our semantics is that it is always well-defined, even if the expected values do not exist. We show that the calculus is sound and allows for compositional reasoning. Furthermore, we present an invariant-based approach for reasoning about pre-expectations of loops.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005153"}, {"primary_key": "3834426", "vector": [], "sparse_vector": [], "title": "A monad for full ground reference cells.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a denotational account of dynamic allocation of potentially cyclic memory cells using a monad on a functor category. We identify the collection of heaps as an object in a different functor category equipped with a monad for adding hiding/encapsulation capabilities to the heaps. We derive a monad for full ground references supporting effect masking by applying a state monad transformer to the encapsulation monad. To evaluate the monad, we present a denotational semantics for a call-by-value calculus with full ground references, and validate associated code transformations.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005109"}, {"primary_key": "3834427", "vector": [], "sparse_vector": [], "title": "Dual-context calculi for modal logic.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>"], "summary": "We show how to derive natural deduction systems for the necessity fragment of various constructive modal logics by exploiting a pattern found in sequent calculi. The resulting systems are dual-context systems, in the style pioneered by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and others. This amounts to a full extension of the <PERSON><PERSON><PERSON> correspondence to the necessity fragments of a constructive variant of the modal logics K, K4, GL, T, and S4. We investigate the metatheory of these calculi, as well as their categorical semantics. Finally, we speculate on their computational interpretation.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005089"}, {"primary_key": "3834428", "vector": [], "sparse_vector": [], "title": "Large scale geometries of infinite strings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce geometric consideration into the theory of formal languages. We aim to shed light on our understanding of global patterns that occur on infinite strings. We utilise methods of geometric group theory. Our emphasis is on large scale geometries. Two infinite strings have the same large scale geometry if there are colour preserving bi-Lipschitz maps with distortions between the strings. Call these maps quasi-isometries. Introduction of large scale geometries poses several questions. The first question asks to study the partial order induced by quasi-isometries. This partial order compares large scale geometries; as such it presents an algebraic tool for classification of global patterns. We prove there is a greatest large scale geometry and infinitely many minimal large scale geometries. The second question is related to understanding the quasi-isometric maps on various classes of strings. The third question investigates the sets of large scale geometries of strings accepted by computational models, e.g. <PERSON><PERSON><PERSON> automata. We provide an algorithm that describes large scale geometries of strings accepted by Büchi automata. This links large scale geometries with automata theory. The fourth question studies the complexity of the quasi-isometry problem. We show the problem is Σ 3 0 -complete thus providing a bridge with computability theory. Finally, the fifth question asks to build algebraic structures that are invariants of large scale geometries. We invoke asymptotic cones, a key concept in geometric group theory, defined via model-theoretic notion of ultra-product. Partly, we study asymptotic cones of algorithmically random strings thus connecting the topic with algorithmic randomness.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005078"}, {"primary_key": "3834429", "vector": [], "sparse_vector": [], "title": "Parity objectives in countable MDPs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study countably infinite MDPs with parity objectives, and special cases with a bounded number of colors in the Mostowski hierarchy (including reachability, safety, <PERSON><PERSON><PERSON> and co<PERSON><PERSON><PERSON><PERSON>).", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005100"}, {"primary_key": "3834430", "vector": [], "sparse_vector": [], "title": "On strong determinacy of countable stochastic games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study 2-player turn-based perfect-information stochastic games with countably infinite state space. The players aim at maximizing/minimizing the probability of a given event (i.e., measurable set of infinite plays), such as reachability, Büchi, ω-regular or more general objectives. These games are known to be weakly determined, i.e., they have value. However, strong determinacy of threshold objectives (given by an event ε and a threshold c ∈ [0,1]) was open in many cases: is it always the case that the maximizer or the minimizer has a winning strategy, i.e., one that enforces, against all strategies of the other player, that ε is satisfied with probability ≥ c (resp. <; c)? We show that almost-sure objectives (where c = 1) are strongly determined. This vastly generalizes a previous result on finite games with almost-sure tail objectives. On the other hand we show that ≥ 1/2 (co-)Biichi objectives are not strongly determined, not even if the game is finitely branching. Moreover, for almost-sure reachability and almost-sure Biichi objectives in finitely branching games, we strengthen strong determinacy by showing that one of the players must have a memory less deterministic (MD) winning strategy.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005134"}, {"primary_key": "3834431", "vector": [], "sparse_vector": [], "title": "The Weisfe<PERSON>-<PERSON> dimension of planar graphs is at most 3.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We prove that the Wei<PERSON><PERSON><PERSON>-<PERSON>man (WL) dimension of the class of all finite planar graphs is at most 3. In particular, every finite planar graph is definable in first-order logic with counting using at most 4 variables. The previously best known upper bounds for the dimension and number of variables were 14 and 15, respectively. First we show that, for dimension 3 and higher, the WL-algorithm correctly tests isomorphism of graphs in a minor-closed class whenever it determines the orbits of the automorphism group of any arc-colored 3-connected graph belonging to this class. Then we prove that, apart from several exceptional graphs (which have WL-dimension at most 2), the individualization of two correctly chosen vertices of a colored 3-connected planar graph followed by the 1-dimensional WL-algorithm produces the discrete vertex partition. This implies that the 3-dimensional WL-algorithm determines the orbits of a colored 3-connected planar graph. As a byproduct of the proof, we get a classification of the 3-connected planar graphs with fixing number 3.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005107"}, {"primary_key": "3834432", "vector": [], "sparse_vector": [], "title": "A categorical semantics for causal structure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a categorical construction for modelling both definite and indefinite causal structures within a general class of process theories that include classical probability theory and quantum theory. Unlike prior constructions within categorical quantum mechanics, the objects of this theory encode fine-grained causal relationships between subsystems and give a new method for expressing and deriving consequences for a broad class of causal structures. To illustrate this point, we show that this framework admits processes with definite causal structures, namely one-way signalling processes, non-signalling processes, and quantum n-combs, as well as processes with indefinite causal structure, such as the quantum switch and the process matrices of Oreshkov, Costa, and Brukner. We furthermore give derivations of their operational behaviour using simple, diagrammatic axioms.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005095"}, {"primary_key": "3834433", "vector": [], "sparse_vector": [], "title": "First-order logic with counting.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce the logic FOCN(P) which extends first-order logic by counting and by numerical predicates from a set P, and which can be viewed as a natural generalisation of various counting logics that have been studied in the literature.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005133"}, {"primary_key": "3834434", "vector": [], "sparse_vector": [], "title": "Effectful applicative bisimilarity: Monads, relators, and Howe&apos;s method.", "authors": ["Ugo <PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study <PERSON><PERSON>'s applicative bisimilarity abstractly, in the context of call-by-value λ-calculi with algebraic effects. We first of all endow a computational λ-calculus with a monadic operational semantics. We then show how the theory of relators provides precisely what is needed to generalise applicative bisimilarity to such a calculus, and to single out those monads and relators for which applicative bisimilarity is a congruence, thus a sound methodology for program equivalence. This is done by studying <PERSON>'s method in the abstract.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005117"}, {"primary_key": "3834435", "vector": [], "sparse_vector": [], "title": "The geometry of concurrent interaction: Handling multiple ports by way of multiple tokens.", "authors": ["Ugo <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a geometry of interaction model for <PERSON><PERSON>'s multiport interaction combinators, a graph-theoretic formalism which is able to faithfully capture concurrent computation as embodied by process algebras like the π-calculus. The introduced model is based on token machines in which not one but multiple tokens are allowed to traverse the underlying net at the same time. We prove soundness and adequacy of the introduced model. The former is proved as a simulation result between the token machines one obtains along any reduction sequence. The latter is obtained by a fine analysis of convergence, both in nets and in token machines.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005112"}, {"primary_key": "3834436", "vector": [], "sparse_vector": [], "title": "On the axiomatizability of quantitative algebras.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Abstract—Quantitative algebras (QAs) are algebras over metric spaces defined by quantitative equational theories as introduced by us in 2016. They provide the mathematical foundation for metric semantics of probabilistic, stochastic and other quantitative systems. This paper considers the issue of axiomatizability of QAs. We investigate the entire spectrum of types of quantitative equations that can be used to axiomatize theories: (i) simple quantitative equations; (ii) Horn clauses with no more than c equations between variables as hypotheses, where c is a cardinal and (iii) the most general case of Horn clauses. In each case we characterize the class of QAs and prove variety/quasivariety theorems that extend and generalize classical results from model theory for algebras and first-order structures.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005102"}, {"primary_key": "3834437", "vector": [], "sparse_vector": [], "title": "MDPs with energy-parity objectives.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Energy-parity objectives combine ω-regular with quantitative objectives of reward MDPs. The controller needs to avoid to run out of energy while satisfying a parity objective. We refute the common belief that, if an energy-parity objective holds almost-surely, then this can be realised by some finite memory strategy. We provide a surprisingly simple counterexample that only uses coBuchi conditions. We introduce the new class of bounded (energy) storage objectives that, when combined with parity objectives, preserve the finite memory property. Based on these, we show that almostsure and limit-sure energy-parity objectives, as well as almostsure and limit-sure storage parity objectives, are in NP ∩ coNP and can be solved in pseudo-polynomial time for energy-parity MDPs.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005131"}, {"primary_key": "3834438", "vector": [], "sparse_vector": [], "title": "Higher-order parity automata.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We introduce a notion of higher-order parity automaton which extends to infinitary simply-typed λ-terms the traditional notion of parity tree automaton on infinitary ranked trees. Our main result is that the acceptance of an infinitary λ-term by a higher-order parity automaton A is decidable, whenever the infinitary λ-term is generated by a finite and simply-typed λY-term. The decidability theorem is established by combining ideas coming from linear logic, from denotational semantics and from infinitary rewriting theory.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005077"}, {"primary_key": "3834439", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> logic for Markov processes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We investigate a modal logic for expressing properties of Markov processes whose semantics is real-valued, rather than Boolean, and based on the mathematical theory of Riesz spaces. We use the duality theory of Riesz spaces to provide a connection between Markov processes and the logic. This takes the form of a duality between the category of coalgebras of the Radon monad (modeling Markov processes) and the category of a new class of algebras (algebraizing the logic) which we call modal Riesz spaces. As a result, we obtain a sound and complete axiomatization of the Riesz Modal logic.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005091"}, {"primary_key": "3834440", "vector": [], "sparse_vector": [], "title": "Partial derivatives on graphs for Kleene allegories.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> and <PERSON><PERSON> showed at LICS 2015 that the equational theory of identity-free relational Kleene lattices (a fragment of Kleene allegories) is decidable in EXPSPACE. In this paper, we show that the equational theory of Kleene allegories is decidable, and is EXPSPACE-complete, answering the first open question posed by their work. The proof proceeds by designing partial derivatives on graphs, which are generalizations of partial derivatives on strings for regular expressions, called <PERSON><PERSON><PERSON>'s partial derivatives. The partial derivatives on graphs give a finite automata construction algorithm as with the partial derivatives on strings.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005132"}, {"primary_key": "3834441", "vector": [], "sparse_vector": [], "title": "Quantitative semantics of the lambda calculus: Some generalisations of the relational model.", "authors": ["C.<PERSON><PERSON><PERSON>"], "summary": "We present an overview of some recent work on the quantitative semantics of the λ-calculus. Our starting point is the fundamental degenerate model of linear logic, the relational model MRel. We show that three quantitative semantics of the simply-typed λ-calculus are equivalent: the relational semantics, HO/N game semantics, and the Taylor expansion semantics. We then consider two recent generalisations of the relational model: first, R-weighted relational models where R is a complete commutative semiring, as studied by <PERSON><PERSON> et al.; secondly, generalised species of structures, as introduced by <PERSON><PERSON> et al. In each case, we briefly discuss some applications to quantitative analysis of higher-order programs.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005064"}, {"primary_key": "3834442", "vector": [], "sparse_vector": [], "title": "LICS 2017 foreword.", "authors": ["<PERSON><PERSON>"], "summary": "This volume contains the proceedings of the 2017 32nd Annual ACM/IEEE Symposium on Logic in Computer Science (LICS), held at Reykjavík University in Iceland from 20 to 23 June 2017. LICS is an annual international forum on the broad range of topics that lie at the intersection of computer science and mathematical logic. In addition to the main symposium, seven workshops were co-located with LICS 2017: • INFINITY: Verification of Infinite-State Systems • LearnAut: Learning and Automata • LCC: Logic and Computational Complexit • LMW: Logic Mentoring Workshop • LOLA: Syntax and Semantics of Low-Level Languages • Metafinite model theory and definability and complexity of numeric graph parameters • WiL: Women in Logic", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005058"}, {"primary_key": "3834443", "vector": [], "sparse_vector": [], "title": "Symbolic execution and probabilistic reasoning.", "authors": ["Corina S<PERSON>"], "summary": "Summary form only given. Symbolic execution is a systematic program analysis technique which explores multiple program behaviors all at once by collecting and solving symbolic path conditions over program paths. The technique has been recently extended with probabilistic reasoning. This approach computes the conditions to reach target program events of interest and uses model counting to quantify the fraction of the input domain satisfying these conditions thus computing the probability of event occurrence. This probabilistic information can be used for example to compute the reliability of an aircraft controller under different wind conditions (modeled probabilistically) or to quantify the leakage of sensitive data in a software system, using information theory metrics such as Shannon entropy. In this talk we review recent advances in symbolic execution and probabilistic reasoning and we discuss how they can be used to ensure the safety and security of software systems.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005062"}, {"primary_key": "3834444", "vector": [], "sparse_vector": [], "title": "Quotients in monadic programming: Projective algebras are equivalent to coalgebras.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In monadic programming, datatypes are presented as free algebras, generated by data values, and by the algebraic operations and equations capturing some computational effects. These algebras are free in the sense that they satisfy just the equations imposed by their algebraic theory, and remain free of any additional equations. The consequence is that they do not admit quotient types. This is, of course, often inconvenient. Whenever a computation involves data with multiple representatives, and they need to be identified according to some equations that are not satisfied by all data, the monadic programmer has to leave the universe of free algebras, and resort to explicit destructors. We characterize the situation when these destructors are preserved under all operations, and the resulting quotients of free algebras are also their subalgebras. Such quotients are called projective. Although popular in universal algebra, projective algebras did not attract much attention in the monadic setting, where they turn out to have a surprising avatar: for any given monad, a suitable category of projective algebras is equivalent with the category of coalgebras for the comonad induced by any monad resolution. For a monadic programmer, this equivalence provides a convenient way to implement polymorphic quotients as coalgebras. The dual correspondence of injective coalgebras and all algebras leads to a different family of quotient types, which seems to have a different family of applications. Both equivalences also entail several general corollaries concerning monadicity and comonadicity.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005110"}, {"primary_key": "3834445", "vector": [], "sparse_vector": [], "title": "An effectful way to eliminate addiction to dependence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We define a monadic translation of type theory, called the weaning translation, that allows for a large range of effects in dependent type theory-such as exceptions, non-termination, non-determinism or writing operations. Through the light of a call-by-push-value decomposition, we explain why the traditional approach fails with type dependency and justify our approach. Crucially, the construction requires that the universe of algebras of the monad forms itself an algebra. The weaning translation applies to a version of the Calculus of Inductive Constructions (CIC) with a restricted version of dependent elimination. Finally, we show how to recover a translation of full CIC by mixing parametricity techniques with the weaning translation. This provides the first effectful version of CIC.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005113"}, {"primary_key": "3834446", "vector": [], "sparse_vector": [], "title": "Separation for dot-depth two.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The dot-depth hierarchy of <PERSON><PERSON><PERSON><PERSON> and <PERSON> is a classification of all first-order definable languages. It rose to prominence following the work of <PERSON>, who established an exact correspondence with the quantifier alternation hierarchy of first-order logic: each level contains languages that can be defined with a prescribed number of quantifier blocks. One of the most famous open problems in automata theory is to obtain membership algorithms for all levels in this hierarchy. For a fixed level, the membership problem asks whether an input regular language belongs to this level. Despite a significant research effort, membership by itself has only been solved for low levels. Recently, a breakthrough was made by replacing membership with a more general problem called separation. This problem asks whether, for two input languages, there exists a third language in the investigated level containing the first language and disjoint from the second. The motivation for looking at separation is threefold: (1) while more difficult, it is more rewarding; (2) being more general, it provides a more convenient framework, and (3) all recent membership algorithms are actually reductions to separation for lower levels. This paper presents a separation algorithm for dot-depth 2. A crucial point is that while dot-depth 2 is our main application, we prove a much more general theorem. Indeed, dot-depth belongs to a family of hierarchies which all share the same generic construction process: starting from an initial class of languages called the basis, one applies generic operations to build new levels. We prove that for any such hierarchy whose basis is a finite class, level 1 has decidable separation. In the special case of dot-depth, this generic result can easily be lifted to level 2.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005070"}, {"primary_key": "3834447", "vector": [], "sparse_vector": [], "title": "Revisiting reachability in timed automata.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We revisit a fundamental result in real-time verification, namely that the binary reachability relation between configurations of a given timed automaton is definable in linear arithmetic over the integers and reals. In this paper we give a new and simpler proof of this result, building on the well-known reachability analysis of timed automata involving difference bound matrices. Using this new proof, we give an exponential-space procedure for model checking the reachability fragment of the logic parametric TCTL. Finally we show that the latter problem is NEXPTIME-hard.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005098"}, {"primary_key": "3834448", "vector": [], "sparse_vector": [], "title": "Bar induction: The good, the bad, and the ugly.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an extension of the computation system and logic of the Nuprl proof assistant with intuitionistic principles, namely versions of <PERSON><PERSON><PERSON>'s bar induction principle, which is equivalent to transfinite induction. We have substantially extended the formalization of <PERSON><PERSON><PERSON>'s type theory within the Coq proof assistant to show that two such bar induction principles are valid w.r.t. Nuprl's semantics (the Good): one for sequences of numbers that involved only minor changes to the system, and a more general one for sequences of name-free (the Ugly) closed terms that involved adding a limit constructor to Nuprl's term syntax in our model of Nuprl's logic. We have proved that these additions preserve Nuprl's key metatheoretical properties such as consistency. Finally, we show some new insights regarding bar induction, such as the non-truncated version of bar induction on monotone bars is intuitionistically false (the Bad).", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005074"}, {"primary_key": "3834449", "vector": [], "sparse_vector": [], "title": "Bounded time computation on metric spaces and Banach spaces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We extend <PERSON><PERSON><PERSON> and <PERSON>'s framework for computational complexity for operators in analysis. This model is based on second-order complexity theory for functionals on the Baire space, which is lifted to metric spaces via representations. Time is measured in the length of the input encodings and the output precision. We propose the notions of complete and regular representations. Completeness is proven to ensure that any computable function has a time bound. Regularity relaxes <PERSON><PERSON><PERSON> and <PERSON>'s notion of a second-order representation, while still guaranteeing fast computability of the length of encodings. We apply these notions to investigate relationships between metric properties of a space and existence of representations that render the metric bounded-time computable. We show that time bounds for the metric can straightforwardly be translated into size bounds of compact subsets of the space. Conversely, for compact spaces and for Banach spaces we construct admissible complete regular representations admitting fast computation of the metric and short encodings. Here it is necessary to trade time bounds off against length of encodings.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005139"}, {"primary_key": "3834450", "vector": [], "sparse_vector": [], "title": "The primitivity of operators in the algebra of binary relations under conjunctions of containments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The algebra of binary relations provides union and composition as basic operators, with the empty set as neutral element for union and the identity relation as neutral element for composition. The basic algebra can be enriched with additional features. We consider the diversity relation, the full relation, intersection, set difference, projection, coprojection, converse, and transitive closure. It is customary to express boolean queries on binary relational structures as finite conjunctions of containments. We investigate which features are primitive in this setting, in the sense that omitting the feature would allow strictly less boolean queries to be expressible. Our main result is that, modulo a finite list of elementary interdependencies among the features, every feature is indeed primitive.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005122"}, {"primary_key": "3834451", "vector": [], "sparse_vector": [], "title": "The limits of SDP relaxations for general-valued CSPs.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "It has been shown that for a general-valued constraint language Γ the following statements are equivalent: (1) any instance of VCSP(Γ) can be solved to optimality using a constant level of the Sherali-Adams LP hierarchy; (2) any instance of VCSP(Γ) can be solved to optimality using the third level of the Sherali-Adams LP hierarchy; (3) the support of Γ satisfies the \"bounded width condition\", i.e., it contains weak near-unanimity operations of all arities.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005087"}, {"primary_key": "3834452", "vector": [], "sparse_vector": [], "title": "Generalised species of rigid resource terms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "C.<PERSON><PERSON><PERSON>"], "summary": "This paper introduces a variant of the resource calculus, the rigid resource calculus, in which a permutation of elements in a bag is distinct from but isomorphic to the original bag. It is designed so that the Taylor expansion within it coincides with the interpretation by generalised species of <PERSON><PERSON> et al., which generalises both <PERSON><PERSON>'s combinatorial species and <PERSON><PERSON><PERSON>'s normal functors, and which can be seen as a proof-relevant extension of the relational model. As an application, we prove the commutation between computing Böhm trees and (standard) Taylor expansions for a particular nondeterministic calculus.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005093"}, {"primary_key": "3834453", "vector": [], "sparse_vector": [], "title": "Uniform, integral and efficient proofs for the determinant identities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give a uniform and integral version of the short propositional proofs for the determinant identities demonstrated over GF(2) in Hrubeš-Tzam<PERSON>t [9]. Specifically, we show that the multiplicativity of the determinant function over the integers is provable in the bounded arithmetic theory VNC 2 , which is a first-order theory corresponding to the complexity class NC 2 . This also establishes the existence of uniform polynomial-size and O(log 2 n)-depth Circuit-Frege (equivalently, Extended Frege) proofs over the integers, of the basic determinant identities (previous proofs hold only over GF(2)). In doing so, we give uniform NC 2 -algorithms for homogenizing algebraic circuits, balancing algebraic circuits (given as input an upper bound on the syntactic-degree of the circuit), and converting circuits with divisions into circuits with a single division gate-all (Σ 1 B -) definable in VNC 2 . This also implies an NC 2 -algorithm for evaluating algebraic circuits of any depth.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005099"}, {"primary_key": "3834454", "vector": [], "sparse_vector": [], "title": "Fibred fibration categories.", "authors": ["<PERSON><PERSON>"], "summary": "We introduce fibred type-theoretic fibration categories which are fibred categories between categorical models of Martin-<PERSON><PERSON> type theory. Fibred type-theoretic fibration categories give a categorical description of logical predicates for identity types. As an application, we show a relational parametricity result for homotopy type theory. As a corollary, it follows that every closed term of type of polymorphic endofunctions on a loop space is homotopic to some iterated concatenation of a loop.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005084"}, {"primary_key": "3834455", "vector": [], "sparse_vector": [], "title": "Categorical liveness checking by corecursive algebras.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Final coalgebras as \"categorical greatest fixed points\" play a central role in the theory of coalgebras. Somewhat analogously, most proof methods studied therein have focused on greatest fixed-point properties like safety and bisimilarity. Here we make a step towards categorical proof methods for least fixed-point properties over dynamical systems modeled as coalgebras. Concretely, we seek a categorical axiomatization of well-known proof methods for liveness, namely ranking functions (in nondeterministic settings) and ranking supermartingales (in probabilistic ones). We find an answer in a suitable combination of coalgebraic simulation (studied previously by the authors) and corecursive algebra as a classifier for (non-)well-foundedness.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005151"}, {"primary_key": "3834456", "vector": [], "sparse_vector": [], "title": "Infinitary intersection types as sequences: A new answer to Klop&apos;s problem.", "authors": ["<PERSON>"], "summary": "We provide a type-theoretical characterization of weakly-normalizing terms in an infinitary lambda-calculus. We adapt for this purpose the standard quantitative (with non-idempotent intersections) type assignment system of the lambda-calculus to our infinite calculus. Our work provides a positive answer to a semi-open question known as <PERSON><PERSON>'s Problem, namely, finding out if there is a type system characterizing the set of hereditary head-normalizing (HHN) lambda-terms. <PERSON><PERSON><PERSON> showed in 2007 that HHN could not be characterized by a finite type system. We prove that an infinitary type system endowed with a validity condition called approximability can achieve it. As it turns out, approximability cannot be expressed when intersection is represented by means of multisets. Multisets are then replaced coinductively by sequences of types indexed by integers, thus defining a type system called System S.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005103"}, {"primary_key": "3834457", "vector": [], "sparse_vector": [], "title": "A fine-grained hierarchy of hard problems in the separated fragment.", "authors": ["<PERSON>"], "summary": "Recently, the separated fragment (SF) has been introduced and proved to be decidable. Its defining principle is that universally and existentially quantified variables may not occur together in atoms. The known upper bound on the time required to decide SF's satisfiability problem is formulated in terms of quantifier alternations: Given an SF sentence ∃z⃗∀x⃗ 1 ∃y⃗ 1 ...∀x⃗ n ∃y⃗ n .ψ in which ψ is quantifier free, satisfiability can be decided in non-deterministic n-fold exponential time. In the present paper, we conduct a more fine-grained analysis of the complexity of SF-satisfiability. We derive an upper and a lower bound in terms of the degree ∂ of interaction of existential variables (short: degree)-a novel measure of how many separate existential quantifier blocks in a sentence are connected via joint occurrences of variables in atoms. Our main result is the k-NEXPTIME-completeness of the satisfiability problem for the set SF ∂≤k of all SF sentences that have degree k or smaller. Consequently, we show that SF-satisfiability is non-elementary in general, since SF is defined without restrictions on the degree. Beyond trivial lower bounds, nothing has been known about the hardness of SF-satisfiability so far.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005094"}, {"primary_key": "3834458", "vector": [], "sparse_vector": [], "title": "The complexity of minimal inference problem for conservative constraint languages.", "authors": ["<PERSON><PERSON>"], "summary": "We study the complexity of the inference problem for propositional circumscription (the minimal inference problem) over arbitrary finite domains. The problem is of fundamental importance in nonmonotonic logics and commonsense reasoning. The complexity of the problem for the two-element domain has been completely classified [<PERSON><PERSON>, <PERSON>, and <PERSON>, Trichotomy in the complexity of minimal inference, LICS 2009]. In this paper, we classify the complexity of the problem over all conservative languages. We consider a version of the problem parameterized by a set of relations (a constraint language), from which we are allowed to build a knowledge base, and where a linear order used to compare models is a part of an input. We show that in this setting the problem is either Π 2 P -complete, coNP-complete, or in P. The classification is based on a coNP-hardness proof for a new class of languages, an analysis of languages that do not express any member of the class and a new general polynomial-time algorithm solving the minimal inference problem for a large class of languages.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005116"}, {"primary_key": "3834459", "vector": [], "sparse_vector": [], "title": "Definability of summation problems for Abelian groups and semigroups.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the descriptive complexity of summation problems in Abelian groups and semigroups. In general, an input to the summation problem consists of an Abelian semigroup G, explicitly represented by its multiplication table, and a subset X of G. The task is to determine the sum over all elements of X. Algorithmically this is a very simple problem. If the elements of X come in some order, then we can process these elements along that order and calculate the sum in a trivial way. However, what makes this fundamental problem so interesting for us is that from the viewpoint of logical definability its tractability is much more delicate. If we consider the semigroup G as an abstract structure and X as an abstract set, without a linear order and hence without a canonical way to process the elements one by one, then it is unclear how to define the sum in any logic that does not have the power to quantify over a linear order. Indeed the trivial summation algorithm cannot be expressed in any polynomial-time logic or, in fact, in any computational model which works on abstract mathematical structures in an isomorphism-invariant way without violating polynomial resource bounds. The surprising difficulty, in terms of logical definability, of this basic mathematical problem is the reason why <PERSON> asked, more than ten years ago, whether it can be expressed in the logic Choiceless Polynomial Time with counting (CPT). Note that, to date, CPT is one of the most powerful known candidates for a logic that might be capable of defining every polynomial-time property of finite structures. In this paper we clarify the status of the definability for the summation problem for Abelian groups and semigroups in important polynomial-time logics. In our first main result we show that the problem can be defined in fixed-point logic with counting (FPC). Since FPC is contained in <PERSON>T this settles <PERSON>man's question. Our proof is based on a dynamic programming approach and heavily uses the counting mechanism of FPC. In our second main result we give a matching lower bound and show that the use of counting operators cannot be avoided: the summation problem, even over Abelian groups, cannot be defined in pure fixed-point logic without counting. Our proof is based on a probabilistic argument.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005082"}, {"primary_key": "3834460", "vector": [], "sparse_vector": [], "title": "Games with costs and delays.", "authors": ["<PERSON>"], "summary": "We demonstrate the usefulness of adding delay to infinite games with quantitative winning conditions. In a delay game, one of the players may delay her moves to obtain a lookahead on her opponent's moves. We show that determining the winner of delay games with winning conditions given by parity automata with costs is EXPTIME-complete and that exponential bounded lookahead is both sufficient and in general necessary. Thus, although the parity condition with costs is a quantitative extension of the parity condition, our results show that adding costs does not increase the complexity of delay games with parity conditions. Furthermore, we study a new phenomenon that appears in quantitative delay games: lookahead can be traded for the quality of winning strategies and vice versa. We determine the extent of this tradeoff. In particular, even the smallest lookahead allows to improve the quality of an optimal strategy from the worst possible value to almost the smallest possible one. Thus, the benefit of introducing lookahead is twofold: not only does it allow the delaying player to win games she would lose without, but lookahead also allows her to improve the quality of her winning strategies in games she wins even without lookahead.", "published": "2017-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS.2017.8005125"}]