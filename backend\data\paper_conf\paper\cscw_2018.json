[{"primary_key": "3305532", "vector": [], "sparse_vector": [], "title": "Digital Human Models in Human Factors and Ergonomics Evaluation of Gesture Interfaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Gesture-based interfaces are becoming a widely used interaction modality in many industrial applications. Therefore, it is important to guarantee usable and ergonomic interfaces for workers. The purpose of this study was to investigate whether the use of digital human models (DHMs) by human factors/ergonomics (HFE) experts can complement the user evaluation of gesture interface prototypes. Two case studies were conducted, in which gesture-based systems for remote robot control were evaluated. The results indicate that the use of DHMs supports the findings from self-reported HFE evaluations. However, digital human modeling still has some limitations. For example, in this study, it was not possible to evaluate small muscle groups (e.g. fingers). We argue that adaptation of the DHMs could be a rapid and simple alternative for supporting the HFE design of gestures.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229088"}, {"primary_key": "3305529", "vector": [], "sparse_vector": [], "title": "Hackers, Computers, and Cooperation: A Critical History of Logo and Constructionist Learning.", "authors": ["Morgan G. Ames"], "summary": "This paper examines the history of the learning theory \"constructionism\" and its most well-known implementation, Logo, to examine beliefs involving both \"C's\" in CSCW: computers and cooperation. Tracing the tumultuous history of one of the first examples of computer-supported cooperative learning (CSCL) allows us to question some present-day assumptions regarding the universal appeal of learning to program computers that undergirds popular CSCL initiatives today, including the Scratch programming environment and the \"FabLab\" makerspace movement. Furthermore, teasing out the individualistic and anti-authority threads in this project and its links to present day narratives of technology development exposes the deeply atomized and even oppositional notions of collaboration in these projects and others under the auspices of CSCW today that draw on early notions of 'hacker culture.' These notions tend to favor a limited view of work, learning, and practice-an invisible constraint that continues to inform how we build and evaluate CSCW technologies.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274287"}, {"primary_key": "3305541", "vector": [], "sparse_vector": [], "title": "MechanicalHeart: A Human-Machine Framework for the Classification of Phonocardiograms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Listening to heart sounds is an important first step in evaluating the cardiovascular system and is important in the early detection of cardiovascular disease. We present and evaluate a framework for combining machine learning algorithms, crowd workers, and experts in the classification of heart sound recordings. The development of a hybrid human-machine framework is motivated by the past success in utilizing human computation to solve problems in medicine and the use of human-machine frameworks in other domains. We describe the methods that decide when and how to escalate the analysis of heart sounds to different resources and incorporate their decision into a final classification. Our framework was tested with a combination of machine classifiers and crowd workers from Amazon's Mechanical Turk. The results indicate a hybrid approach achieves greater performance than a baseline classifier alone, utilizing less expert resources while achieving similar performance, compared to a framework without the crowd.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274297"}, {"primary_key": "3305565", "vector": [], "sparse_vector": [], "title": "Gender, Feedback, and Learners&apos; Decisions to Share Their Creative Computing Projects.", "authors": ["Emilia F. Gan", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Although informal online learning communities are made possible by users' decisions to share their creations, participation by females and other marginalized groups remains stubbornly low in technical communities. Using descriptive statistics and a unique dataset of shared and unshared projects from over 1.1 million users of Scratch-a collaborative programming community for young people-we show that while girls share less initially, this trend flips among experienced users. Using Bayesian regression analyses, we show that this relationship can largely be attributed to differences in the way boys and girls participate. We also find that while prior positive feedback is correlated with increased sharing among inexperienced users, this effect also reverses with experience or with the addition of controls. Our findings provide a description of the dynamics behind online learners' decisions to share, open new research questions, and point to several lessons for system designers.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274323"}, {"primary_key": "3305573", "vector": [], "sparse_vector": [], "title": "KAVE: Building Kinect Based CAVE Automatic Virtual Environments, Methods for Surround-Screen Projection Management, Motion Parallax and Full-Body Interaction Support.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> i Badia"], "summary": "While CAVE Automatic Virtual Environments (CAVE) have been around for over 2 decades they remain complex to setup, unaffordable to most, and generally limited to data and model visualization applications for academia and industry. In this paper, we present a solution to create a monocular CAVE using the Unity 3D game engine by adding motion parallax and full-body interaction support via the use of a Kinect V2 low-cost sensor. More importantly, we provide a functional and easy to use plugin for that effect, the KAVE, and its configuration tool. Here, we describe our own low-cost CAVE setup, a range of alternative configurations to CAVE systems using this technology and example applications. Finally, we discuss the potential of such an approach considering the current advancements in VR and gaming.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229092"}, {"primary_key": "3305599", "vector": [], "sparse_vector": [], "title": "The TUIO 2.0 Protocol: An Abstraction Framework for Tangible Interactive Surfaces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Since its introduction in 2005, the TUIO protocol has been widely employed within a multitude of usage contexts in tangible and multi-touch interaction. While its simple and versatile design still covers the core functionality of interactive tabletop systems, the conceptual and technical developments of the past decade also led to a variety of ad-hoc extensions and modifications for specific scenarios. In this paper, we present an analysis of the strengths and shortcomings of TUIO 1.1, leading to the constitution of an extended abstraction model for tangible interactive surfaces and the specification of the second-generation TUIO 2.0 protocol, along with several example encodings of existing tangible interaction concepts.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229090"}, {"primary_key": "3305634", "vector": [], "sparse_vector": [], "title": "Djnn/Smala: A Conceptual Framework and a Language for Interaction-Oriented Programming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The persistent difficulty to develop and maintain interactive software has unveiled the inadequacy of traditional imperative programming languages. In the recent years, several solutions have been proposed to enrich the existing languages with constructs dedicated to interaction. In this paper, we propose a different approach that takes interaction as the primary concern to build a new programming language. We present Djnn, a conceptual framework based on the concepts of process and process activation, then we introduce Smala a programming language derived from this framework. We propose a solution for the unification of the concepts of event and data-flow, and for the derivation of complex control structures from a small set of basic ones. We detail the syntax and the semantics of Smala. Finally, we illustrate through a real-size application how it enables building all parts of an interactive software. Djnn and Smala may offer designers and programmers usable means to think of interactions and translate them into running code.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229094"}, {"primary_key": "3305670", "vector": [], "sparse_vector": [], "title": "Aiding Collaborative Reuse of Computational Notebooks with Annotated Cell Folding.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Computational notebooks aim to support collaborative data analysis by combining code, visualizations, and text in a single easily shared document. Yet, as notebooks evolve and grow they often become difficult to navigate or understand, discouraging sharing and reuse. We present the design and evaluation of a Jupyter Notebook extension providing facilities for annotated cell folding. Through a lab study and multi-week deployment we find cell folding aids notebook navigation and comprehension, not only by the original author, but also by collaborators viewing the notebook in a meeting or revising it on their own. However, in some cases cell folding encouraged collaborators to overlook folded sections or spend longer reviewing a notebook before editing it. These findings extend our understanding of code folding's trade-offs to a new medium and demonstrate its benefits for everyday collaboration. We conclude by discussing how dynamic reorganization can support sharing and reuse of computational notebooks.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274419"}, {"primary_key": "3305678", "vector": [], "sparse_vector": [], "title": "Applications of Social Identity Theory to Research and Design in Computer-Supported Cooperative Work.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Research in computer-supported cooperative work has historically focused on behaviors of individuals at scale, using frames of interpersonal interaction such as <PERSON><PERSON><PERSON>'s theories of self-presentation. These framings result in research detailing characteristics, personal identities, and behaviors of large numbers of connected and interacting individuals, while the social identity concepts that lead to intra- and inter-group dynamics have received less attention. We argue that the emergent properties of self-categorization and social identity, which are particularly fluid and complex in online spaces, provide a complementary perspective with which to re-examine traditional topics in social computing. We discuss the applicability of the Social Identity Perspective to established and new research domains in CSCW, proposing alternative perspectives on self-presentation, social support, collaboration, conflict, and leadership. We note methodological considerations emerging from this theory. Finally, we consider how broad concepts and lessons from the Social Identity Perspective might inspire CSCW work in the future.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274771"}, {"primary_key": "3305705", "vector": [], "sparse_vector": [], "title": "Effect of Manipulated Amplitude and Frequency of Human Voice on Dominance and Persuasiveness in Audio Conferences.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hideaki <PERSON>", "<PERSON>"], "summary": "We propose to artificially manipulate participants' vocal cues, amplitude and frequency, in real time to adjust their dominance and persuasiveness during audio conferences. We implemented a prototype system and conducted two experiments. The first experiment investigated the effect of vocal cue manipulation on the perception of dominance. The results showed that participants perceived higher dominance while listening to a voice with a high amplitude and low frequency. The second experiment investigated the effect of vocal cue manipulation on persuasiveness. The results indicated that a person with a low amplitude and low frequency voice had greater persuasiveness in conversations with biased dominance, while there was no statistically significant difference in persuasiveness in conversations with unbiased dominance.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274446"}, {"primary_key": "3305721", "vector": [], "sparse_vector": [], "title": "Characterizing Online Public Discussions through Patterns of Participant Interactions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Public discussions on social media platforms are an intrinsic part of online information consumption. Characterizing the diverse range of discussions which can arise is crucial for these platforms, as they may seek to organize and curate them. This paper introduces a framework to characterize public discussions, relying on a representation that captures a broad set of social patterns which emerge from the interactions between interlocutors, comments, and audience reactions. We apply our framework to study public discussions on Facebook at two complementary scales. First, at the level of individual discussions, we use it to predict a discussion's future trajectory, anticipating future antisocial actions (such as participants blocking each other) and forecasting the discussion's growth. Second, we systematically analyze the variation of discussions across thousands of Facebook sub-communities, revealing subtle differences (and unexpected similarities) in how people interact when discussing online content. We further show that this variation is driven more by participant tendencies than by the content triggering these discussions.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274467"}, {"primary_key": "3305526", "vector": [], "sparse_vector": [], "title": "Exploring Real-Time Collaboration in Crowd-Powered Systems Through a UI Design Tool.", "authors": ["<PERSON>", "<PERSON>", "Sun Young Park", "<PERSON>", "<PERSON><PERSON>", "Stephanie D. O&apos;Ke<PERSON>e", "<PERSON>"], "summary": "Real-time collaboration between a requester and crowd workers expands the scope of tasks that crowdsourcing can be used for by letting requesters and crowd workers interactively create various artifacts (e.g., a sketch prototype, writing, or program code). In such systems, it is increasingly common to allow requesters to verbally describe their requests, receive responses from workers, and provide immediate and continuous feedback to enhance the overall outcome of the two groups' real-time collaboration. This work is motivated by the lack of a deep understanding of the challenges that end users of such systems face in their communication with workers and the need of design implications that can address such challenges for other similar systems. In this paper, we investigate how requesters verbally communicate and collaborate with crowd workers to solve a complex task. Using a crowd-powered UI design tool, we conducted a qualitative user study to explore how requesters with varying expertise communicate and collaborate with crowd workers. Our work also identifies the unique challenges that collaborative crowdsourcing systems pose: potential expertise differences between requesters and crowd workers, the asymmetry of two-way communication (e.g., speech versus text), and the shared artifact's concurrent modification by two disparate groups. Finally, we make design recommendations that can inform the design of future real-time collaboration processes in crowdsourcing systems.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274373"}, {"primary_key": "3305527", "vector": [], "sparse_vector": [], "title": "Assembling Strategic Narratives: Information Operations as Collaborative Work within an Online Community.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Social media are becoming sites of information operations-activities that seek to undermine information systems and manipulate civic discourse [26,36,44,47]. Through a mixed methods approach, our research extends investigations of online activism to examine the \"work\" of online information operations conducted on Twitter. In particular, we analyze the English-language conversation surrounding the reemergence of <PERSON><PERSON><PERSON> (the \"Aleppo Boy\") on Syrian state television, almost a year after his family's home was bombed in an airstrike conducted by the Syrian government. We uncover: a network of clustered users that contributes to a contested and politicized information space surrounding <PERSON><PERSON><PERSON>'s story; the presence of undermining narratives that serve to disrupt the mainstream media's narrative and confuse the audience; and the techniques used when promoting, defending, or undermining narratives. In the current climate of increasing polarization in online social spaces, this work contributes an improved understanding of information operations online and of the collaborations that take shape around and through them.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274452"}, {"primary_key": "3305528", "vector": [], "sparse_vector": [], "title": "Designing for Collaborative Infrastructuring: Supporting Resonance Activities.", "authors": ["<PERSON>", "Volkmar Pi<PERSON>k", "<PERSON>"], "summary": "'Infrastructuring' as a concept draws attention to the way in which a designed artefact or system is not the end of the development process. Rather, technology development takes place up to, and including, its successful establishment in an associated body of practices. This places emphasis upon the role of practitioners as designers of their own practice environments. While the activities of professional technology development have been reasonably well discussed and conceptualized, activities that practitioners perform to make things work for them are less acknowledged as a systematic contribution to the successful establishment of the use of IT. This paper considers in particular the so-called 'point of infrastructuring': the moment at which practitioners become aware of opportunities to (re-)design their infrastructures, usually initiated by breakdowns or the recognition of potential innovations and reconsideration of current infrastructural use. From this point onwards, end users themselves may start configuring, tailoring or developing new conventions until a point has been reached in which a new technology usage has been successfully established. However, points of infrastructuring do not only provoke end-user driven in-situ design (or 'infrastructuring') activities. They also evoke so-called 'resonance activities'. These encompass all of the observations and communications that attend upon a point of infrastructure as it becomes visible within a work environment. Examination of resonance activities can be a starting point for a better understanding of the practitioner's activities beyond single infrastructural changes, providing a way of highlighting the relationship between different points of infrastructuring. How to capture and understand resonance activities and how to design technological support for them is still an open question. Based on previous work outlined in the literature and an empirical study that looked at collaborative appropriation activities (the most important aspect of infrastructuring) during 3D printing processes, we outline the concept of sociable technologies as a technological approach for capturing and supporting resonance activities and thus enabling infrastructuring activities more broadly.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274382"}, {"primary_key": "3305530", "vector": [], "sparse_vector": [], "title": "Testing Waters, Sending Clues: Indirect Disclosures of Socially Stigmatized Experiences on Social Media.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Indirect disclosure strategies include hinting about an experience or a facet of one's identity or relaying information explicitly but through another person. These strategies lend themselves to sharing stigmatized or sensitive experiences such as a pregnancy loss, mental illness, or abuse. Drawing on interviews with women in the U.S. who use social media and experienced pregnancy loss, we investigated factors guiding indirect disclosure decisions on social media. Our findings include 1) a typology of indirect disclosure strategies based on content explicitness, original content creator, and content sharer, and 2) an examination of indirect disclosure decision factors related to the self, audience, platform affordances, and temporality. We identify how people intentionally adapt social media and indirect disclosures to meet psychological (e.g., keeping a personal record) and social (e.g., feeling out the audience) needs associated with loss. We discuss implications for design and research, including features that support disclosures through proxy, and relevance for algorithmic detection and intervention. CAUTION: This paper includes quotes about pregnancy loss.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274288"}, {"primary_key": "3305531", "vector": [], "sparse_vector": [], "title": "Acting the Part: Examining Information Operations Within #BlackLivesMatter Discourse.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This research examines how Russian disinformation actors participated in a highly charged online conversation about the #BlackLivesMatter movement and police-related shootings in the USA during 2016. We first present high-level dynamics of this conversation on Twitter using a network graph based on retweet flows that reveals two structurally distinct communities. Next, we identify accounts in this graph that were suspended by Twitter for being affiliated with the Internet Research Agency, an entity accused of conducting information operations in support of Russian political interests. Finally, we conduct an interpretive analysis that consolidates observations about the activities of these accounts. Our findings have implications for platforms seeking to develop mechanisms for determining authenticity---by illuminating how disinformation actors enact authentic personas and caricatures to target different audiences. This work also sheds light on how these actors systematically manipulate politically active online communities by amplifying diverging streams of divisive content.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274289"}, {"primary_key": "3305533", "vector": [], "sparse_vector": [], "title": "With Few Eyes, All Hoaxes are Deep.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Quality control is critical to open production communities like Wikipedia. Wikipedia editors enact border quality control with edits (counter-vandalism) and new article creations (new page patrolling) shortly after they are saved. In this paper, we describe a long-standing set of inefficiencies that have plagued new page patrolling by drawing a contrast to the more efficient, distributed processes for counter-vandalism. Further, to address this issue, we demonstrate an effective automated topic model based on a labeling strategy that leverages a folksonomy developed by subject specific working groups in Wikipedia (WikiProject tags) and a flexible ontology (WikiProjects Directory) to arrive at a hierarchical and uniform label set. We are able to attain very high fitness measures (macro ROC-AUC: 95.2%, macro PR-AUC: 74.5%) and real-time performance using word2vec-based features. Finally, we present a proposal for how incorporating this model into current tools will shift the dynamics of new article review positively.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274290"}, {"primary_key": "3305534", "vector": [], "sparse_vector": [], "title": "Framing Effects: Choice of Slogans Used to Advertise Online Experiments Can Boost Recruitment and Lead to Sample Biases.", "authors": ["Tal August", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online experimentation with volunteers relies on participants' non-financial motivations to complete a study, such as to altruistically support science or to compare oneself to others. Researchers rely on these motivations to attract study participants and often use incentives, like performance comparisons, to encourage participation. Often, these study incentives are advertised using a slogan (e.g., \"What is your thinking style?''). Research on framing effects suggests that advertisement slogans attract people with varying demographics and motivations. Could the slogan advertisements for studies risk attracting only specific users? To investigate the existence of potential sample biases, we measured how different slogan frames affected which participants self-selected into studies. We found that slogan frames impact recruitment significantly; changing the slogan frame from a 'supporting science' frame to a 'comparing oneself to others' frame lead to a 9% increase in recruitment for some studies. Additionally, slogans framed as learning more about oneself attract participants significantly more motivated by boredom compared to other slogan frames. We discuss design implications for using frames to improve recruitment and mitigate sources of sample bias in online research with volunteers.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274291"}, {"primary_key": "3305535", "vector": [], "sparse_vector": [], "title": "Using TEMPEST: End-User Programming of Web-Based Ecological Momentary Assessment Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> aan het R<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Researchers who perform Ecological Momentary Assessment (EMA) studies tend to rely on informatics experts to set up and administer their data collection protocols with digital media. Contrary to standard surveys and questionnaires that are supported by widely available tools, setting up an EMA protocol is a substantial programming task. Apart from constructing the survey items themselves, researchers also need to design, implement, and test the timing and the contingencies by which these items are presented to respondents. Furthermore, given the wide availability of smartphones, it is becoming increasingly important to execute EMA studies on user-owned devices, which presents a number of software engineering challenges pertaining to connectivity, platform independence, persistent storage, and back-end control. We discuss TEMPEST, a web-based platform that is designed to support non-programmers in specifying and executing EMA studies. We discuss the conceptual model it presents to end-users, through an example of use, and its evaluation by 18 researchers who have put it to real-life use in 13 distinct research studies.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3179428"}, {"primary_key": "3305536", "vector": [], "sparse_vector": [], "title": "Departing and Returning: Sense of Agency as an Organizing Concept for Understanding Social Media Non/use Transitions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent work has identified a variety of motivations for various forms of technology use and non-use. However, less work has closely examined relationships between those motivations and the experiences of transiting among these different forms of use and non-use. This paper fills that gap by conducting a qualitative interview- and diary-based study where participants were asked to deactivate their Facebook account. An abductive analysis suggests participants' experiences can be organized under the conceptual umbrella of sense of agency, which refers to an individual's perception that their actions are under their own control. The analysis shows how, across disparate motivations, all participants took actions that increased their own subjective sense of agency, regardless of whether they returned to Facebook or not. The discussion applies this conceptual lens to prior studies of technology use and non-use. Doing so shows how sense of agency may provide an organizing orientation for understanding subjective experiences of use and non-use.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274292"}, {"primary_key": "3305537", "vector": [], "sparse_vector": [], "title": "Efficient Crowd Exploration of Large Networks: The Case of Causal Attribution.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Accurately and efficiently crowdsourcing complex, open-ended tasks can be difficult, as crowd participants tend to favor short, repetitive \"microtasks\". We study the crowdsourcing of large networks where the crowd provides the network topology via microtasks. Crowds can explore many types of social and information networks, but we focus on the network of causal attributions, an important network that signifies cause-and-effect relationships. We conduct experiments on Amazon Mechanical Turk (AMT) testing how workers propose and validate individual causal relationships and introduce a method for independent crowd workers to explore large networks. The core of the method, Iterative Pathway Refinement, is a theoretically-principled mechanism for efficient exploration via microtasks. We evaluate the method using synthetic networks and apply it on AMT to extract a large-scale causal attribution network, then investigate the structure of this network as well as the activity patterns and efficiency of the workers who constructed this network. Worker interactions reveal important characteristics of causal perception and the network data they generate can improve our understanding of causality and causal inference.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274293"}, {"primary_key": "3305538", "vector": [], "sparse_vector": [], "title": "#pray4victims: Consistencies in Response to Disaster on Twitter.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper studies commonalities in response across disasters in online social networks (OSNs), specifically Twitter. After presenting an algorithm for extracting vocabularies across disasters, we extract type-specific vocabularies for terrorist attacks, earthquakes, and climate-related disasters between 2012 and 2017. Disaster type drives vocabulary similarity: terrorism responses mention an \"attack\" and law enforcement, earthquake responses mention the quake and its magnitude, and climate-related responses include safety and requests for aid. Across disaster types, tweets regularly mention victims/affected and prayer, consistent with communal coping and social support in crisis aftermath. Using disaster-type vocabularies, we study Twitter as a proxy for severity, correlating casualties to frequencies in Twitter. These vocabularies better correlate with casualties than baseline crisis lexica, especially in western countries. Twitter response and casualties diverge at the maximum, and Twitter response is stronger in Western countries, suggesting perceived severity is driven by additional factors.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274294"}, {"primary_key": "3305539", "vector": [], "sparse_vector": [], "title": "Supporting Collaboratively Constructed Independence: A Study of Spinal Cord Injury.", "authors": ["<PERSON>yse G. <PERSON>ü<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Independence is a central concern for people in the care of many chronic conditions. It is often viewed as a goal that can be facilitated with the use of patient data. It is also viewed, especially from the medical side, as something an individual achieves. However, in the lived experience, independence is often a set of collaborative practices. In this paper, we unpack findings from an interview-based study of Spinal Cord Injury (SCI) self-care. We found that independence - both functional and in the form of effecting agency - must be co-constructed by the choices and activities of the care network, including the person with disability, caregivers, and clinicians. This collaboratively shaped independence also affects potential collection and use of data in support of self-care. We describe how collaboratively shaped independence informs requirements and constraints for the design of sensor-based networks for self-care in long-term chronic disability.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274295"}, {"primary_key": "3305540", "vector": [], "sparse_vector": [], "title": "Investigating Crowd Creativity in Online Music Communities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Crowd creativity is typically associated with peer-production communities focusing on artistic products like animations, video games, and music, but less frequently to Open Source Software (OSS), despite the fact that also developers must be creative to come up with new solutions to their technical challenges. In this paper, we conduct a study to further the understanding of which factors from prior work in both OSS and art communities are predictive of successful collaboration - defined as reuse of previous songs - in three different songwriting communities, namely Songtree, Splice, and ccMixter. The main findings from this study confirm that the success of collaborations is associated with high community status of recognizable authors and low degree of derivativity of songs.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274296"}, {"primary_key": "3305542", "vector": [], "sparse_vector": [], "title": "G-Gene: A Gene Alignment Method for Online Partial Stroke Gestures Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The large availability of touch-sensitive screens fostered the research in gesture recognition. The Machine Learning community focused mainly on accuracy and robustness to noise, creating classifiers that precisely recognize gestures after their performance. Instead, the User Interface Engineering community developed compositional gesture descriptions that model gestures and their sub-parts. They are suitable for building guidance systems, but they lack a robust and accurate recognition support. In this paper, we establish a compromise between the accuracy and the provided information introducing G-Gene, a method for transforming compositional stroke gesture definitions into profile Hidden Markov Models (HMMs), able to provide both a good accuracy and information on gesture sub-parts. It supports online recognition without using any global feature, and it updates the information while receiving the input stream, with an accuracy useful for prototyping the interaction. We evaluated the approach in a user interface development task, showing that it requires less time and effort for creating guidance systems with respect to common gesture classification approaches.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229095"}, {"primary_key": "3305543", "vector": [], "sparse_vector": [], "title": "UMI3D: A Unity3D Toolbox to Support CSCW Systems Properties in Generic 3D User Interfaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "UMI3D is a recent model which allows the generic description of 3D environments through a network. These generic environments can run outside of any device, on a server or on a separate computer. A unique dedicated application that we have named UMI3D client allows a VR, AR or MR device to interact with all the existing UMI3D environments. Moreover, the client-server architecture allows these environments to be collaborative. In this paper, we introduce a toolbox which allows the creation and running of UMI3D collaborative environments using Unity 3D. It contains tools to address the main concerns of computer-supported cooperative work (CSCW) systems in UMI3D environments. After introducing the contribution of this toolbox to the design of 3D CSCW systems, we present a user evaluation of an environment designed with this toolbox. Finally, we discuss the limits of the UMI3D model and the improvements that could be made to this model.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274298"}, {"primary_key": "3305544", "vector": [], "sparse_vector": [], "title": "Complex and Ambiguous: Understanding Sticker Misinterpretations in Instant Messaging.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Cha", "<PERSON><PERSON>", "Sangkeun Park", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Stickers, though similar in appearance to emoji, have distinct characteristics because they often contain animation, diverse gestures, and multiple characters and objects. Stickers can convey richer meaning than emoji, but their complexity and placement constraint may result in miscommunication. In this paper, we aim to understand how people perceive emotion in stickers, as well as how miscommunication related to sticker occurs in actual chat contexts. Toward this goal, we conducted an online survey (n = 87) and in-depth interviews (n = 28) in South Korea. We found emotional and contextual aspects of sticker misinterpretation. In particular, emotion misinterpretation mostly happened due to stickers' ambiguous (multiple) facial/bodily expressions and different perception of dynamism in gestures. In real chat settings, there were also contextual misinterpretations where senders and receivers differently interpret stickers' visual representation/reference, or/and corresponding textual messages. Based on these findings, we provide several practical design implications such as context awareness support in sticker interaction.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274299"}, {"primary_key": "3305545", "vector": [], "sparse_vector": [], "title": "SOLVENT: A Mixed Initiative System for Finding Analogies between Research Papers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Scientific discoveries are often driven by finding analogies in distant domains, but the growing number of papers makes it difficult to find relevant ideas in a single discipline, let alone distant analogies in other domains. To provide computational support for finding analogies across domains, we introduce SOLVENT, a mixed-initiative system where humans annotate aspects of research papers that denote their background (the high-level problems being addressed), purpose (the specific problems being addressed), mechanism (how they achieved their purpose), and findings (what they learned/achieved), and a computational model constructs a semantic representation from these annotations that can be used to find analogies among the research papers. We demonstrate that this system finds more analogies than baseline information-retrieval approaches; that annotators and annotations can generalize beyond domain; and that the resulting analogies found are useful to experts. These results demonstrate a novel path towards computationally supported knowledge sharing in research communities.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274300"}, {"primary_key": "3305546", "vector": [], "sparse_vector": [], "title": "The Internet&apos;s Hidden Rules: An Empirical Study of Reddit Norm Violations at Micro, Meso, and Macro Scales.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Norms are central to how online communities are governed. Yet, norms are also emergent, arise from interaction, and can vary significantly between communities---making them challenging to study at scale. In this paper, we study community norms on Reddit in a large-scale, empirical manner. Via 2.8M comments removed by moderators of 100 top subreddits over 10 months, we use both computational and qualitative methods to identify three types of norms: macro norms that are universal to most parts of Reddit; meso norms that are shared across certain groups of subreddits; and micro norms that are specific to individual, relatively unique subreddits. Given the size of Reddit's user base---and the wide range of topics covered by different subreddits---we argue this represents the first large-scale census of the norms in broader internet culture. In other words, these findings shed light on what Reddit values, and how widely-held those values are. We conclude by discussing implications for the design of new and existing online communities.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274301"}, {"primary_key": "3305547", "vector": [], "sparse_vector": [], "title": "Data-Driven Proactive Policy Assurance of Post Quality in Community q&amp;a Sites.", "authors": ["Chunyang Chen", "<PERSON>", "Ji<PERSON>u Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To ensure the post quality, Q&amp;A sites usually develop a list of quality assurance guidelines for \"dos and don'ts\", and adopt collaborative editing mechanism to fix quality violations. Quality guidelines are mostly high-level principles, and many tacit and context-sensitive aspects of the expected quality cannot be easily enforced by a set of explicit rules. Collaborative editing is a reactive mechanism after low-quality posts have been posted. Our study of collaborative editing data on Stack Overflow suggests that tacit and context-sensitive quality-assurance knowledge is manifested in the editing patterns of large numbers of collaborative edits. Inspired by this observation, we develop and evaluate a Convolutional Neural Network based approach to learn editing patterns from historical post edits for predicting the need of editing a post. Our approach provides a proactive policy assurance mechanism that warns users potential quality issues in a post before it is posted.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274302"}, {"primary_key": "3305548", "vector": [], "sparse_vector": [], "title": "Beyond Lingua Franca: System-Facilitated Language Switching Diversifies Participation in Multiparty Multilingual Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "When multiple non-native speakers (NNSs) who share the same native language join a group discussion with native speakers (NSs) of the common language used in the discussion, they sometimes switch back and forth between common language and their native language to reach common ground. However, such code-switching makes others feel excluded and thus not considered appropriate during formal meetings. To offer NNSs more flexibility to code-switch in a group discussion while minimizing the cost of excluding others, we introduced a language support tool that automatically detects a user's spoken language, and then transcribe as well as translate them into another language (common language or NNS's native language). In a within-subject study involving 19 quads (two Japanese and two Chinese) in a collocated setting, participants were asked to perform a series of decision-making tasks with and without the tool. Results showed that the language support tool encouraged diverse use of language during a meeting, resulting in more participation from NNSs - they increased active initiating behaviors from NNSs. Although the perceived quality of collaboration became lower, it also elicited helping behaviors among the NNS pairs.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274303"}, {"primary_key": "3305549", "vector": [], "sparse_vector": [], "title": "&apos;Staged for Living&apos;: Negotiating Objects and their Values over a Porous Boundary.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We describe more than 19 months of ethnographic fieldwork with people who have embraced minimalism, a lifestyle movement focused on reducing modern life's clutter. We found that for minimalists, the home is a central but porous site for making and staging their values around objects. The home's porous boundary allows minimalists to reinforce their values around objects for others and themselves, but it also necessitates adopting strategies to assert their values when objects---entangled with others' values---move in and out the boundaries of the home. Drawing from our fieldwork, we introduce the concept of the porous boundary. \"Porosity\" impels us to consider the coupling of objects with values as we do boundary practices for the home. A porous boundary perspective, we argue, can open new design spaces in the development of novel technologies for the home.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274305"}, {"primary_key": "3305550", "vector": [], "sparse_vector": [], "title": "Crowd Coach: Peer Coaching for Crowd Workers&apos; Skill Growth.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Traditional employment usually provides mechanisms for workers to improve their skills to access better opportunities. However, crowd work platforms like Amazon Mechanical Turk (AMT) generally do not support skill development (i.e., becoming faster and better at work). While researchers have started to tackle this problem, most solutions are dependent on experts or requesters willing to help. However, requesters generally lack the necessary knowledge, and experts are rare and expensive. To further facilitate crowd workers' skill growth, we present Crowd Coach, a system that enables workers to receive peer coaching while on the job. We conduct a field experiment and real world deployment to study Crowd Coach in the wild. Hundreds of workers used Crowd Coach in a variety of tasks, including writing, doing surveys, and labeling images. We find that Crowd Coach enhances workers' speed without sacrificing their work quality, especially in audio transcription tasks. We posit that peer coaching systems hold potential for better supporting crowd workers' skill development while on the job. We finish with design implications from our research.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274306"}, {"primary_key": "3305551", "vector": [], "sparse_vector": [], "title": "Will Too Many Editors Spoil The Tag?: Conflicts and Alignment in Q&amp;A Categorization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Q&amp;A websites compile useful knowledge through user-generated questions and responses. Many Q&amp;As use collaborative tagging systems to improve search and discovery while distributing the work of categorizing and organization throughout the community. Although early work on collaborative tagging questioned whether consistent categorization schemes could emerge from large groups with little to no coordination, empirical studies have found surprising coherence among users' tags. We build on this research by testing whether coherence emerges in tag usage on Q&amp;As, a more challenging context, focusing in particular on mismatches in the specificity of tags (basic level disagreement). We found that some users shifted toward more specific tag usage over time slightly increasing conflict, but that moderators were instrumental in helping to resolve some of this conflict. This study highlights the importance of learning and moderation in the development of coherence in collaborative tagging systems.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274307"}, {"primary_key": "3305552", "vector": [], "sparse_vector": [], "title": "Living in the Present: Understanding Long-Term Content Referencing in Enterprise Online Communities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Successful online communities accumulate large amounts of long-term content. However there has been little quantitative, theoretically-motivated exploration of how communities organize such content, nor which community members take responsibility for active organization. We examine one aspect of long-term content organization through link behavior, also exploring role differences between enterprise community leaders and members in the context of life-cycle community models. We first classify how content is linked within posts, identifying usage patterns that organize information within and outside communities. We next present an exploratory quantitative analysis of 2,010 communities including 428,476 posts and 1,246,570 links. We show paradoxically that although mature communities accumulate substantial content, organizing that content using links decreases over time. Further analyses suggest that this arises from a recency bias, with communities being focused on current content. Our results also challenge descriptive lifecycle community models, which propose that regular community members adopt greater responsibility over time. We explore explanations for our findings and implications including new tools that encourage responsibility for active organization, as well as methods for members to revisit critical content.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274308"}, {"primary_key": "3305553", "vector": [], "sparse_vector": [], "title": "Spacetime Characterization of Real-Time Collaborative Editing.", "authors": ["Gabriele D&apos;Angelo", "Angelo <PERSON>", "<PERSON>"], "summary": "Real-Time Collaborative Editing (RTCE) is a popular way of instrumenting cooperative work on documents, in particular on the Web. Little is known in the literature yet about RTCE usage patterns in the real world. In this paper we study how a popular RTCE editor (Etherpad) is used in the wild, digging into the edit histories of a large collection of documents (about 14 000 pads), retrieved from one of the most popular public instances of the platform, hosted by the Wikimedia Foundation. The pad analysis is supported by a novel conceptual model that allows to label edit operations as \"collaborative\" or not depending on their distance-in edit position (space), edit time, or spacetime (both)-from edits made by other authors. The model is applied to classify all edits from the pad corpus. Classification results are further used to characterize the collaboration behavior of pad authors. Findings show that: 1) about half of the pads have a single author and hence witnessed no collaboration; 2) collaboration on common document parts happens often, but it happens asynchronously with authors taking turns in editing; and 3) simultaneous editing of common document parts happens very rarely. These findings help in revisiting early RTCE design decisions (e.g., the granularity of conflict management in RTCE protocols) and give insights on how to address novel needs (e.g., end-to-end encryption and offline editing).", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274310"}, {"primary_key": "3305554", "vector": [], "sparse_vector": [], "title": "Evaluating Crowdworkers as a Proxy for Online Learners in Video-Based Learning Contexts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Crowdsourcing has emerged as an effective method of scaling-up tasks previously reserved for a small set of experts. Accordingly, researchers in the large-scale online learning space have begun to employ crowdworkers to conduct research about large-scale, open online learning. We here report results from a crowdsourcing study (N=135) to evaluate the extent to which crowdworkers and MOOC learners behave comparably on lecture viewing and quiz tasks---the most utilized learning activities in MOOCs. This serves to (i) validate the assumption of previous research that crowdworkers are indeed reliable proxies of online learners and (ii) address the potential of employing crowdworkers as a means of online learning environment testing. Overall, we observe mixed results---in certain contexts (quiz performance and video watching behavior) crowdworkers appear to behave comparably to MOOC learners, and in other situations (interactions with in-video quizzes), their behaviors appear to be disparate. We conclude that future research should be cautious if employing crowdworkers to carry out learning tasks, as the two populations do not behave comparably on all learning-related activities.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274311"}, {"primary_key": "3305555", "vector": [], "sparse_vector": [], "title": "&apos;Too Gay for Facebook&apos;: Presenting LGBTQ+ Identity Throughout the Personal Social Media Ecosystem.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Most US social media users engage regularly with multiple platforms. For LGBTQ+ people, this means making self-presentation decisions not just on one platform, but many. These choices are made in the face of sometimes-overlapping platform environments, which can have consequentially different norms, audiences, and affordances. Moreover, many LGBTQ+ users face high stakes in online self-presentation, due to the risk of stigmatization of their LGBTQ+ identity, increasing the importance of self-presentation decisions that enable them to achieve their goals and avoid stigmatization. This combination of environmental complexity and high stakes is not adequately accounted for in existing work on self-presentation, but doing so is important to support and understand the experiences of LGBTQ+ and other potentially stigmatized users. We adopt an ecological approach to an interview and cognitive mapping study of 20 LGBTQ+ social media users. We find that participants employ the platforms, audiences, affordances, and norms within what we call their \"personal social media ecosystems\" to avoid stigmatization while still allowing for expression of their LGBTQ+ identity and the flexibility to adjust their presentation over time.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274313"}, {"primary_key": "3305556", "vector": [], "sparse_vector": [], "title": "Coloring in the Links: Capturing Social Ties as They are Perceived.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The richness that characterizes relationships is often absent when they are modeled using computational methods in network science. Typically, relationships are represented simply as links, perhaps with weights. The lack of finer granularity is due in part to the fact that, aside from linkage and strength, no fundamental or immediately obvious dimensions exist along which to categorize relationships. Here we propose a set of dimensions that capture major components of many relationships -- derived both from relevant academic literature and people's everyday descriptions of their relationships. We first review prominent findings in sociology and social psychology, highlighting dimensions that have been widely used to categorize social relationships. Next, we examine the validity of these dimensions empirically in two crowd-sourced experiments. Ultimately, we arrive at a set of ten major dimensions that can be used to categorize relationships: similarity, trust, romance, social support, identity, respect, knowledge exchange, power, fun, and conflict. These ten dimensions, while not dispositive, offer higher resolution than existing models. Indeed, we show that one can more accurately predict missing links in a social graph by using these dimensions than by using a state-of-the-art link embeddedness method. We also describe tinghy.org, an online platform we built to collect data about how social media users perceive their online relationships, allowing us to examine these dimensions at scale. Overall, by proposing a new way of modeling social graphs, our work aims to contribute both to theory in network science and practice in the design of social-networking applications.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274312"}, {"primary_key": "3305557", "vector": [], "sparse_vector": [], "title": "Score-Group Framing Negatively Impacts Peer Evaluations.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "How does group membership framing affect the feedback students provide learners? This paper presents two between-subjects experiments investigating the effect of Ingroup/Outgroup membership on effort spent in peer evaluations, and whether the group membership criterion type affects quality and stringency of evaluation. Two peer-review assignments were implemented in two separate classes. In the first study, students were nominally grouped by location they sat in class and non-nominally grouped by current class score; each was asked to review an Ingroup and Outgroup peer assignment. A second study randomly assigned students to one of four group types (random, score, motivation, and location); student reviewed two Ingroup assignments. In both studies, score-grouped students graded their peers more stringently than students grouped by location. These studies illustrate for system designers the impacts of group framing - and the disclosure of that-in peer review tasks.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274314"}, {"primary_key": "3305558", "vector": [], "sparse_vector": [], "title": "Understanding Gender Equity in Author Order Assignment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Women remain underrepresented in many fields in computer science, particularly at higher levels. In academia, success and promotion are influenced by a researcher's publication record. In many fields, including computer science, multi-author papers are the norm. Evidence from other fields shows that author order norms can influence the assignment of credit. We conduct interviews of students and faculty in human-computer interaction (HCI) and machine learning (ML) to determine factors related to assignment of author order in collaborative publication. The outcomes of these interviews then informed metrics of interest for a bibliometric analysis of gender and collaboration in research papers published from 1996 to 2016 in three top HCI and ML conferences. Based on our findings, we make recommendations for assignment of credit in multi-author papers and interpretation of author order, particularly in regard to how this area affects women.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274315"}, {"primary_key": "3305559", "vector": [], "sparse_vector": [], "title": "Shifting Expectations: Understanding Youth Employees&apos; Handoffs in a 3D Print Shop.", "authors": ["<PERSON>", "Foa<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As digital fabrication technology has become mainstream, the increased demand for 3D printed objects has created a new market for professional outsourcing. Given that most of this work does not require advanced training, and is an appropriate entry-level manufacturing job, there is an exciting opportunity to employ youth already skilled in \"making\" and interested in technology to do this work as an after-school job. The combination of this new technology and workforce calls for new workflows that streamline client-driven digital manufacturing. However, the limitations of current digital fabrication technology and youth schedules require that this work be spread between multiple shifts, necessitating employees to coordinate and handoff their work. We investigated the collaborative practices between youth employees while working on client jobs in a 3D print shop during one year of field work. In this paper, we describe instances where youth employees successfully, and unsuccessfully, handed off work between shifts and identify techniques utilized by youth to support successful handoffs, including: counting physical artifacts, using asynchronous chat programs, and documenting work. We then discuss the impact of the print shop manager's presence, physical characteristics of 3D prints, and youth perspectives of work on the selection of and effectiveness of these techniques. Finally, we offer lessons learned from successful handoffs in the print shop and recommendations for supporting youth in collaborative work environments.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274316"}, {"primary_key": "3305560", "vector": [], "sparse_vector": [], "title": "Do Students&apos; Learning Behaviors Differ when they Collaborate in Open-Ended Learning Environments?", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Al<PERSON>"], "summary": "Researchers have long recognized the importance of using technology to support students' collaboration in learning and problem solving tasks. Recently, there has been a lot of research in capturing and characterizing student discourse and how they regulate each other when they perform learning tasks in pairs or in small groups. In this paper, our goal is to dive a little deeper into how students collaborate, and the learning behaviors they exhibit when working in pairs on a learning by modeling task, while also teaching a virtual agent in the Betty's Brain system. We report the results of a quasi-experimental study, where students were divided into two groups: one group worked in pairs and the other group worked individually. The results illustrate that students in the collaborative group built more correct causal maps than those working individually, and their pre-post test results show significantly higher learning gains in the science content. A differential sequence mining algorithm applied to their action sequences captured in log files showed differences in the learning behaviors between the two groups. The differences imply that the collaborative groups were better at debugging their evolving causal maps than the students who worked individually.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274318"}, {"primary_key": "3305561", "vector": [], "sparse_vector": [], "title": "Watch Me Code: Programming Mentorship Communities on Twitch.tv.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Live streaming-an emerging practice of broadcasting video of oneself in real time to an online audience-is often used by people to portray themselves engaged in a craft such as programming. Viewers of these 'creative streams' gather to watch the streamer at work and to interact with the streamer and other audience members. However, little is known about how creative streamers engage with their audience, how their viewership communities form and operate, and how creative streams may support learning. In this study, we used a participant-observer method to study game development streams on the live streaming site Twitch.tv. We found that live streams support the growth of learning-focused communities that mentor both the streamer and each other during and after streams. We show the influence of streamers in creating a space for learning and motivating learners. Finally, we discuss implications for online education and communities of practice.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274319"}, {"primary_key": "3305562", "vector": [], "sparse_vector": [], "title": "Beyond the Coded Gaze: Analyzing Expression of Mental Health and Illness on Instagram.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In CSCW and HCI, work examining expression of mental health and illness on social media frequently aims to classify content, quantify visual trends, and predict user states. This approach to analysis is a form of the coded gaze, a type of algorithmic 'way of seeing' coined with respect to artificial intelligence techniques. The coded gaze classifies content through researcher- and machine-labeled categories, relying on a series of theoretical assumptions that influence how values pertaining to mental health and illness become inscribed in data. In this paper, we build upon this research to support alternative methods of data interpretation. We join manual collection of Instagram posts with semi-structured interviews and digital ethnography over six months to understand how Instagram users express their experiences with mental health and illness. We argue that individuals negotiate claims to mental health and illness through visibility and signaling, the boundaries between mental health and illness are porous and blurred, and reposting and remix are a form of participation. We discuss practical and ethical implications for studying the expression of mental health and illness online.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274320"}, {"primary_key": "3305563", "vector": [], "sparse_vector": [], "title": "Engaging with Health Data: The Interplay Between Self-Tracking Activities and Emotions in Fertility Struggles.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Self-tracking data is often seen as a means to reflect and achieve a goal, usually focusing on positive insights and actions. Lately, some studies have discussed the negative consequences of self-tracking, suggesting that people interact with personal data in different ways. We explored how self-tracking activities and the emotional context characterize how people engage with personal health data through the analysis of a complex and emotionally-loaded use case: fertility self-tracking. We qualitatively analyzed patient-generated content in an online health community dedicated to fertility. We found five distinct types of engagement with data: positive, burdened, obsessive, trapped, and abandoning. Each of them is composed of an action and an emotional component that mutually influence each other. We discuss how the interplay of these components characterize a person's engagement with data, how the online forum made these issues visible, and how they are embedded in the self-tracking culture. We also provide insights into the implications of these issues for self-tracking tools. Finally, we hypothesize how people transition through the types of relationships with data, suggesting directions for future research in the area.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274309"}, {"primary_key": "3305564", "vector": [], "sparse_vector": [], "title": "Women (Still) Ask For Less: Gender Differences in Hourly Rate in an Online Labor Marketplace.", "authors": ["Eureka Foong", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In many traditional labor markets, women earn less on average compared to men. However, it is unclear whether this discrepancy persists in the online gig economy, which bears important differences from the traditional labor market (e.g., more flexible work arrangements, shorter-term engagements, reputation systems). In this study, we collected self-determined hourly bill rates from the public profiles of 48,019 workers in the United States (48.8% women) on Upwork, a popular gig work platform. The median female worker set hourly bill rates that were 74% of the median man's hourly bill rates, a gap than cannot be entirely explained by online and offline work experience, education level, and job category. However, in some job categories, we found evidence of a more complex relationship between gender and earnings: women earned more overall than men by working more hours, outpacing the effect of lower hourly bill rates. To better support equality in the rapidly growing gig economy, we encourage continual evaluation of the complex gender dynamics on these platforms and discuss whose responsibility it is to address inequalities.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274322"}, {"primary_key": "3305566", "vector": [], "sparse_vector": [], "title": "Ease on Down the Code: Complex Collaborative Qualitative Coding Simplified with &apos;Code Wizard&apos;.", "authors": ["<PERSON>", "Man<PERSON>", "<PERSON>"], "summary": "This paper describes the design and development of a preliminary qualitative coding tool as well as a method to improve the process of achieving inter-coder reliability (ICR) in small teams. Software applications that support qualitative coding do not sufficiently assist collaboration among coders and overlook some fundamental issues related to ICR. We propose a new dimension of collaborative coding called 'coders' certainty\" and demonstrate its ability to illustrate valuable code disagreements that are missing from existing approaches. Through a case study, we describe the utility of our tool, Code Wizard, and how it helped a group of researchers effectively collaborate to code naturalistic observation data. We report the valuable lessons we learned from the development of our tool and method: (1) identifying coders' certainty constitutes an important part of determining the quality of data analysis and facilitates identifying overlapping and ambiguous codes, (2) making the details of coding process visible helps streamline the coding process and leads to a sense of ownership of the research results, and (3) there is valuable information hidden in coding disagreements that can be leveraged for improving the process of data analysis.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274401"}, {"primary_key": "3305567", "vector": [], "sparse_vector": [], "title": "To Label or Not to Label: The Effect of Stance and Credibility Labels on Readers&apos; Selection and Perception of News Articles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Wai-Tat Fu"], "summary": "Social media sites use different labels to help users find and select news feeds. For example, Blue Feed, Red Feed, a news feed created by the Wall Street Journal, use stance labels to separate news articles with opposing political ideologies to help people explore diverse opinions. To combat the spread of fake news, Facebook has experimented with putting credibility labels on news articles to help readers decide whether the content is trustworthy. To systematically understand the effects of stance and credibility labels on online news selection and consumption, we conducted a controlled experiment to study how these labels influence the selection, perceived extremeness, and level of agreement of news articles. Results show that stance labels may intensify selective exposure - a tendency for people to look for agreeable opinions -- and make people more vulnerable to polarized opinions and fake news. We found, however, that the effect of credibility labels on reducing selective exposure and recognizing fake news is limited. Although originally designed to encourage exposure to opposite viewpoints, stance labels can make fake news articles look more trustworthy, and they may lower people's perception of the extremeness of fake news articles. Our results have important implications on the subtle effects of stance and credibility labels on online news consumption.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274324"}, {"primary_key": "3305568", "vector": [], "sparse_vector": [], "title": "Quotidian Report: Grassroots Data Practices to Address Public Safety.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We examine the local data practices of citizens in Mexico who use Facebook sites as a platform to report crimes and share safety-related information. We conducted 14 interviews with a variety of participants who collaborate as administrators and contributors of these online communities. The communities we examined have two central components: the citizens who crowd-source data about instances of crime in different neighborhoods in and around Mexico City, and the administrators of the Facebook sites who use the crowd-sourced data to intervene and collaborate with other stakeholders. From our interviews, we identify the community, data, and action practices used by group administrators to collect, curate, and publish information about public safety that would otherwise go unreported. The combination of these practices improves the reputation of the groups on Facebook, increases trust, and encourages sustained participation from citizens. These practices also legitimize data gathered by group members as an important grassroots tool for responding to issues of public safety that would otherwise not be reported or acted upon. Our findings contribute a growing body of work that aims to understand how social media enable political action in contexts where people are not being served by existing institutions.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274286"}, {"primary_key": "3305569", "vector": [], "sparse_vector": [], "title": "Social Photo-Elicitation: The Use of Communal Production of Meaning to Hear a Vulnerable Population.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We report on an initial ethnographic exploration of the situation of sex-trafficking survivors in Nepal. In the course of studying trafficking survivors in a protected-living situation created by a non-governmental organization in Nepal, we adapted photo-elicitation to hear the voices of the survivors by making the technique more communal. Bringing sociality to the forefront of the method reduced the pressure on survivors to assert voices as individuals, allowing them to speak. We make three contributions to research. First, we propose a communal form of photo-elicitation as a method to elicit values in sensitive settings. Second, we present the complex circumstances of the survivors as they undergo rehabilitation and move towards life with a \"new normal\". Third, our work adds to HCI and CSCW literature on understanding specific concerns of trafficking survivors and aims to inform designs that can support reintegration of survivors in society. The values that the survivors hold and their notion of future opportunities suggest possession of limited but important social capital in some domains that could be leveraged to aid reintegration.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274325"}, {"primary_key": "3305570", "vector": [], "sparse_vector": [], "title": "Eight Observations and 24 Research Questions About Open Source Projects: Illuminating New Realities.", "authors": ["<PERSON>", "<PERSON> J. <PERSON>", "<PERSON>", "<PERSON>"], "summary": "The rapid acceleration of corporate engagement with open source projects is drawing out new ways for CSCW researchers to consider the dynamics of these projects. Research must now consider the complex ecosystems within which open source projects are situated, including issues of for-profit motivations, brokering foundations, and corporate collaboration. Localized project considerations cannot reveal broader workings of an open source ecosystem, yet much empirical work is constrained to a local context. In response, we present eight observations from our eight-year engaged field study about the changing nature of open source projects. We ground these observations through 24 research questions that serve as primers to spark research ideas in this new reality of open source projects. This paper contributes to CSCW in social and crowd computing by delivering a rich and fresh look at corporately-engaged open source projects with a call for renewed focus and research into newly emergent areas of interest.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274326"}, {"primary_key": "3305571", "vector": [], "sparse_vector": [], "title": "Fake Cures: User-centric Modeling of Health Misinformation in Social Media.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Social media's unfettered access has made it an important venue for health discussion and a resource for patients and their loved ones. However, the quality of information available, as well as the motivations of its posters, has been questioned. This work examines the individuals on social media that are posting questionable health-related information, and in particular promoting cancer treatments which have been shown to be ineffective (making it a kind of misinformation, willful or not). Using a multi-stage user selection process, we study 4,212 Twitter users who have posted about one of 139 such \"treatments\", and compare them to a baseline of users generally interested in cancer. Considering features capturing user attributes, writing style, and sentiment, we build a classifier which is able to identify users prone to propagate such misinformation at an accuracy of over 90%, providing a potential tool for public health officials to identify such individuals for preventive intervention.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274327"}, {"primary_key": "3305572", "vector": [], "sparse_vector": [], "title": "GuessTheKarma: A Game to Assess Social Rating Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Popularity systems, like Twitter retweets, Reddit upvotes, and Pinterest pins have the potential to guide people toward posts that others liked. That, however, creates a feedback loop that reduces their informativeness: items marked as more popular get more attention, so that additional upvotes and retweets may simply reflect the increased attention and not independent information about the fraction of people that like the items. How much information remains? For example, how confident can we be that more people prefer item A to item B if item A had hundreds of upvotes on Reddit and item B had only a few? We investigate using an Internet game called GuessTheKarma that collects independent preference judgments (N=20,674) for 400 pairs of images, approximately 50 per pair. Unlike the rating systems that dominate social media services, GuessTheKarma is devoid of social and ranking effects that influence ratings. Overall, Reddit scores were not very good predictors of the true population preferences for items as measured by GuessTheKarma: the image with higher score was preferred by a majority of independent raters only 68% of the time. However, when one image had a low score and the other was one of the highest scoring in its subreddit, the higher scoring image was preferred nearly 90% of the time by the majority of independent raters. Similarly, Imgur view counts for the images were poor predictors except when there were orders of magnitude differences between the pairs. We conclude that popularity systems marked by feedback loops may convey a strong signal about population preferences, but only when comparing items that received vastly different popularity scores.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274328"}, {"primary_key": "3305574", "vector": [], "sparse_vector": [], "title": "Personalized Motivation-supportive Messages for Increasing Participation in Crowd-civic Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In crowd-civic systems, citizens form groups and work towards shared goals, such as discovering social issues or reforming official policies. Unfortunately, many real-world systems have been unsuccessful in continually motivating large numbers of citizens to participate voluntarily, despite various approaches such as gamification and persuasion techniques. In this paper, we examine the influence of personalized messages designed to support motivation as asserted by the Self-Determination Theory (SDT). We designed a crowd-civic platform for collecting community issues with personalized motivation-supportive messages and conducted two studies: a pair-comparison experiment with 150 participants on Amazon's Mechanical Turk and a live deployment study with 120 university members. Results of the pair-comparison study indicate applicability of SDT's perspective in crowd-civic systems. While applying it in the live system surfaced several challenges, including recruiting participants without interfering with general motivations, the collected data exhibited similar promising trends.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274329"}, {"primary_key": "3305575", "vector": [], "sparse_vector": [], "title": "Navigating the Healthcare Service &quot;Black Box&quot;: Individual Competence and Fragmented System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "CSCW research has investigated how people at workplace and organizational settings gain knowledge required for work, but less is known regarding how \"organizational outsiders\" obtain knowledge about organizations and organizational landscapes that provide a service. Gaining knowledge about service landscapes is particularly difficult because they are often complex, non-transparent, and fragmented. We address this gap through an interview study of 19 U.S. parents regarding how they navigated health services for their young children, and how they gained competence in navigation practices. We describe a similar process all participants went through including four inherently iterative stages: seeking and integrating knowledge, decision-making, encountering breakdowns, and repairing and reflecting. We further elucidate what constitutes navigational competence, or the creation of resources about how to navigate, for our participants. We discuss how our study could advance understanding of navigation practices, and the importance for HCI design to support these complex yet essential navigation practices and the accumulation of navigational competence.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274330"}, {"primary_key": "3305576", "vector": [], "sparse_vector": [], "title": "Productivity in an Era of Multi-Teaming: The Role of Information Dashboards and Shared Cognition in Team Performance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "When and why do information dashboards enhance team productivity? We explore this question in the context of project teams whose members have to work across multiple teams at the same time. We expected multi-teaming, especially that involving a high variety in team members, would have a negative effect on team performance by impairing a team's transactive memory system (TMS), a critical form of shared cognition allowing members to keep track of who knows what. Further, we expected the use of an information dashboard with member skills could combat the harmful effects of high-variety multi-teaming. To test our hypotheses experimentally, we developed an online game (Knowledge Worker's Dilemma) that simulates the coordination decisions that professionals face in app development teams. Results show multi-teaming hurt team performance by reducing TMS; use of an information dashboard benefited high-variety teams and, surprisingly, hurt the performance of low-variety teams. Potential explanations and implications are discussed.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274331"}, {"primary_key": "3305577", "vector": [], "sparse_vector": [], "title": "Social Media as Social Transition Machinery.", "authors": ["<PERSON>"], "summary": "Social media, and people's online self-presentations and social networks, add complexity to people's experiences managing changing identities during life transitions. I use gender transition as a case study to understand how people experience liminality on social media. I qualitatively analyzed data from transition blogs on Tumblr (n=240), a social media blogging site on which people document their gender transitions, and in-depth interviews with transgender bloggers (n=20). I apply ethnographer <PERSON>'s liminality framework to a social media context and contribute a new understanding of liminality by arguing that reconstructing one's online identity during life transitions is a rite of passage. During life transitions, people present multiple identities simultaneously on different social media sites that together comprise what I call social transition machinery. Social transition machinery describes the ways that, for people facing life transitions, multiple social media sites and networks o\"en remain separate, yet work together to facilitate life transitions.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274332"}, {"primary_key": "3305578", "vector": [], "sparse_vector": [], "title": "Bot Detection in Wikidata Using Behavioral and Other Informal Cues.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Bots have been important to peer production's success. Wikipedia, OpenStreetMap, and Wikidata all have taken advantage of automation to perform work at a rate and scale exceeding that of human contributors. Understanding the ways in which humans and bots behave in these communities is an important topic, and one that relies on accurate bot recognition. Yet, in many cases, bot activities are not explicitly flagged and could be mistaken for human contributions. We develop a machine classifier to detect previously unidentified bots using implicit behavioral and other informal editing characteristics. We show that this method yields a high level of fitness under both formal evaluation (PR-AUC: 0.845, ROC-AUC: 0.985) and a qualitative analysis of \"anonymous\" contributor edit sessions. We also show that, in some cases, unflagged bot activities can significantly misrepresent human behavior in analyses. Our model has the potential to support future research and community patrolling activities.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274333"}, {"primary_key": "3305579", "vector": [], "sparse_vector": [], "title": "Sociocultural Practices that Make Microfinance Work: A Case Study from Sri Lanka.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Microfinance is an inherently social process. Over the years, it has emerged as an essential means for providing financial services to the \"underbanked\" population in developing countries. This paper presents a qualitative study focused on understanding existing cooperative practices associated with microfinance in rural Sri Lanka. Through semi-structured interviews, group discussions and visits to microfinance centres, we found that microfinancing involves much more than financial transactions, rather it is strongly habituated in the sociocultural fabric of communities. Our findings show that three factors affect the cooperative process of microfinancing: trust and credibility, community support, and familial assistance. Using examples from the field, this paper discusses the importance of these factors in microfinance activities and contributes an elaborated account of cooperative practices that support microfinance processes. We also highlight how the local sociocultural structures and practices shape the way microfinance processes are being handled. We contribute towards an in-depth understanding of such practices that can be useful for microfinance institutions (MFIs) and technology designers. Consequently, we advocate for an intermediary path for technology interventions where technology and people work together rather than technology replacing people.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274334"}, {"primary_key": "3305580", "vector": [], "sparse_vector": [], "title": "Going Beyond Obscurity: Organizational Approaches to Data Anonymization.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Anonymization is viewed as a solution to over-exposure of personal information in a data-driven society. Yet how organizations apply anonymization techniques to data for regulatory, ethical or commercial reasons remains underexplored. We investigate how such measures are applied in organizations, asking whether anonymization practices are used, what approaches are considered practical and adequate, and how decisions are made to protect the privacy of data subjects while preserving analytical value. Our findings demonstrate that anonymization is applied to data far less pervasively than expected. Organizations that do employ anonymization often view their practices as sensitive and resort to anonymity by obscurity alongside technical means. Rather than being a purely technical question of applying the right algorithms, anonymization in practice is a complex socio-technical process that relies on multi-stakeholder collaborations. Organizational decision-making about appropriate approaches and the management of responsibility can result in workarounds necessary to negotiate the technical complexit", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274335"}, {"primary_key": "3305581", "vector": [], "sparse_vector": [], "title": "Who is Addressed in this Comment?: Automatically Classifying Meta-Comments in News Comments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "User comments have become an essential part of online journalism. However, newsrooms are often overwhelmed by the vast number of diverse comments, for which a manual analysis is barely feasible. Identifying meta-comments that address or mention newsrooms, individual journalists, or moderators and that may call for reactions is particularly critical. In this paper, we present an automated approach to identify and classify meta-comments. We compare comment classification based on manually extracted features with an end-to-end learning approach. We develop, optimize, and evaluate multiple classifiers on a comment dataset of the large German online newsroom SPIEGEL Online and the 'One Million Posts' corpus of DER STANDARD, an Austrian newspaper. Both optimized classification approaches achieved encouraging $F_{0.5}$ values between 76% and 91%. We report on the most significant classification features with the results of a qualitative analysis and discuss how our work contributes to making participation in online journalism more constructive.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274336"}, {"primary_key": "3305582", "vector": [], "sparse_vector": [], "title": "Structure or Nurture?: The Effects of Team-Building Activities and Team Composition on Team Outcomes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "How can instructors group students into teams that interact and learn effectively together? One strand of research advocates for grouping students into teams with \"good\" compositions such as skill diversity. Another strand argues for deploying team-building activities to foster interpersonal relations like psychological safety. Our work synthesizes these two strands of research. We describe an experiment (N=249) that compares how team composition vs. team-building activities affect student team outcomes. In two university courses, we composed student teams either randomly or using a criteria-based team formation tool. Teams further performed team-building activities that promoted either team or task outcomes. We collected project scores, and used surveys to measure psychological safety, perceived performance, and team satisfaction. Surprisingly, the criteria-based teams did not statistically differ from the random teams on any of the measures taken, despite having compositions that better satisfied the criteria defined by the instructor. Our findings argue that, for instructors deploying a team formation tool, creating an expectation among team members that their team can perform well is as important as tuning the criteria in the tool. We also found that student teams reported high levels of psychological safety, but these levels appeared to develop organically and were not affected by the activities or compositional strategies tested. We distill these and other findings into implications for the design and deployment of team formation tools for learning environments.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274337"}, {"primary_key": "3305583", "vector": [], "sparse_vector": [], "title": "What I See is What You Don&apos;t Get: The Effects of (Not) Seeing Emoji Rendering Differences across Platforms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Emoji are popular in digital communication, but they are rendered differently on different viewing platforms (e.g., iOS, Android). It is unknown how many people are aware that emoji have multiple renderings, or whether they would change their emoji-bearing messages if they could see how these messages render on recipients' devices. We developed software to expose the multi-rendering nature of emoji and explored whether this increased visibility would affect how people communicate with emoji. Through a survey of 710 Twitter users who recently posted an emoji-bearing tweet, we found that at least 25% of respondents were unaware that the emoji they posted could appear differently to their followers. Additionally, after being shown how one of their tweets rendered across platforms, 20% of respondents reported that they would have edited or not sent the tweet. These statistics reflect millions of potentially regretful tweets shared per day because people cannot see emoji rendering differences across platforms. Our results motivate the development of tools that increase the visibility of emoji rendering differences across platforms, and we contribute our cross-platform emoji rendering software to facilitate this effort.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274393"}, {"primary_key": "3305584", "vector": [], "sparse_vector": [], "title": "Emotional Biosensing: Exploring Critical Alternatives.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Emotional biosensing is rising in daily life: Data and categories claim to know how people feel and suggest what they should do about it, while CSCW explores new biosensing possibilities. Prevalent approaches to emotional biosensing are too limited, focusing on the individual, optimization, and normative categorization. Conceptual shifts can help explore alternatives: toward materiality, from representation toward performativity, inter-action to intra-action, shifting biopolitics, and shifting affect/desire. We contribute (1) synthesizing wide-ranging conceptual lenses, providing analysis connecting them to emotional biosensing design, (2) analyzing selected design exemplars to apply these lenses to design research, and (3) offering our own recommendations for designers and design researchers. In particular we suggest humility in knowledge claims with emotional biosensing, prioritizing care and affirmation over self-improvement, and exploring alternative desires. We call for critically questioning and generatively re-imagining the role of data in configuring sensing, feeling, 'the good life,' and everyday experience.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274338"}, {"primary_key": "3305585", "vector": [], "sparse_vector": [], "title": "Technology to Support Immigrant Access to Social Capital and Adaptation to a New Country.", "authors": ["<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The key to successful adaptation for immigrants in a new country is their social capital, or those resources embedded in their social networks. Research suggests that information and communication technologies (ICTs) foster immigrants' social capital and facilitate their adaptation. However, it is unclear how recent immigrants use ICTs to develop social capital and how this supports their adaptation needs. We performed semi-structured interviews with thirteen recent immigrants and five long-term immigrants. We found that ICTs and technology-mediated connections: (1) easily addressed immigrants' settlement needs, (2) minimally addressed their financial and cultural needs, and (3) were not used to address their emotional needs. To support recent immigrants' adaptation, we suggest ways for ICTs to (1) reduce uncertainty about meeting local-born populations, (2) foster reciprocity among immigrant communities, and (3) facilitate safe resource exchanges.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274339"}, {"primary_key": "3305586", "vector": [], "sparse_vector": [], "title": "Making a Living My Way: Necessity-driven Entrepreneurship in Resource-Constrained Communities.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Entrepreneurship has long been used to create self-employment opportunities to guard against career uncertainty. Yet, little is known about how social technologies impact the day-to-day work of entrepreneurs in resource-constrained contexts. We performed a qualitative study involving interviews with 26 micro-entrepreneurs in Detroit and observations of entrepreneurship events. We found that micro-entrepreneurs in Detroit are often pushed into entrepreneurship in response to unexpected life disruptions, barriers to employment, and desire to benefit the community. Their resource-constrained contexts shape how they use social technologies, such as sharing economy tools and social media groups, particularly with respect to privacy, safety, and professional agency. We expand the discussion in CSCW around what it means to be an entrepreneur and provide implications for how social technologies can be designed to better meet the employment needs of people in resource-constrained communities.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274340"}, {"primary_key": "3305587", "vector": [], "sparse_vector": [], "title": "Debiasing Desire: Addressing Bias &amp; Discrimination on Intimate Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Solon <PERSON>", "<PERSON>"], "summary": "Designing technical systems to be resistant to bias and discrimination represents vital new terrain for researchers, policymakers, and the anti-discrimination project more broadly. We consider bias and discrimination in the context of popular online dating and hookup platforms in the United States, which we call intimate platforms. Drawing on work in social-justice-oriented and Queer HCI, we review design features of popular intimate platforms and their potential role in exacerbating or mitigating interpersonal bias. We argue that focusing on platform design can reveal opportunities to reshape troubling patterns of intimate contact without overriding users' decisional autonomy. We identify and address the difficult ethical questions that nevertheless come along with such intervention, while urging the social computing community to engage more deeply with issues of bias, discrimination, and exclusion in the study and design of intimate platforms.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274342"}, {"primary_key": "3305588", "vector": [], "sparse_vector": [], "title": "Deliberation and Resolution on Wikipedia: A Case Study of Requests for Comments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Resolving disputes in a timely manner is crucial for any online production group. We present an analysis of Requests for Comments (RfCs), one of the main vehicles on Wikipedia for formally resolving a policy or content dispute. We collected an exhaustive dataset of 7,316 RfCs on English Wikipedia over the course of 7 years and conducted a qualitative and quantitative analysis into what issues affect the RfC process. Our analysis was informed by 10 interviews with frequent RfC closers. We found that a major issue affecting the RfC process is the prevalence of RfCs that could have benefited from formal closure but that linger indefinitely without one, with factors including participants' interest and expertise impacting the likelihood of resolution. From these findings, we developed a model that predicts whether an RfC will go stale with 75.3% accuracy, a level that is approached as early as one week after dispute initiation.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274343"}, {"primary_key": "3305589", "vector": [], "sparse_vector": [], "title": "Engaging Solidarity in Data Collection Practices for Community Health.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Researchers and practitioners engaged in global health are increasingly turning to data-driven approaches. However, the institutionalized roles and responsibilities---as well as motivations and workflows---of those involved in data collection are not well understood. To attain a deeper understanding of the practices of frontline health workers engaged in data collection, we conducted fieldwork in underserved contexts of Delhi, India. In particular, we interviewed these workers and observed their interactions with 200 households. Our analysis takes a postcolonial feminist perspective to examine how these frontline health workers navigate the multiple demands placed on them by their families, society, local residents, and health organizations. We also discuss how they practice feminist solidarity, and distill lessons for improved data collection across global health initiatives.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274345"}, {"primary_key": "3305590", "vector": [], "sparse_vector": [], "title": "Bridging Disconnected Knowledges for Community Health.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We conducted a field study of Mohalla Clinics in Delhi (India), drawing on <PERSON><PERSON>'s work to highlight the importance of considering disparate, partial perspectives in the ecologies of such clinics. We emphasized that inadequate attention given to patients' and health workers' perspectives, and the low preparedness of the doctors at the clinics, resulted in the clinics being unable to address the needs of target patient groups. We concluded by contributing to ongoing, important conversations in the fields of ICTD, CSCW, and HCI towards redesigning healthcare interventions, revisiting patient empowerment, and redefining the role of ASHAs as infomediaries.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274344"}, {"primary_key": "3305591", "vector": [], "sparse_vector": [], "title": "Did they login?: Patterns of Anonymous Contributions in Online Communities.", "authors": ["<PERSON>", "<PERSON>", "Carsten S. Ø<PERSON>lund"], "summary": "Researchers studying user behaviors in online communities often conduct analyses of user interaction data recorded in system logs e.g., an edit in Wikipedia. Such analysis relies on collating interactions by a unique identifier such as a user ID. However, if users can contribute without being logged-in (i.e., anonymously) analysis of interaction data omit part of a user's experience. Problematically, anonymous traces are unlikely to be randomly distributed, so their omission can change statistical conclusions, with implications for both research and practice. To understand the impacts on conclusions of leaving out anonymous traces, we conducted an analysis of system logs from two online citizen science projects. Attributing anonymous traces with user IDs, we found that (1) many users contribute anonymously, though with varied patterns; and (2) attributing anonymous traces diminishes empirical evidence used to support theory and change the results of system algorithms. These results suggest anonymous traces have implications for research on user behaviors and the practices associated with using such data to tailor user experiences in online communities.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274346"}, {"primary_key": "3305592", "vector": [], "sparse_vector": [], "title": "MyPath: Investigating Breast Cancer Patients&apos; Use of Personalized Health Information.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Following a cancer diagnosis, patients must cope with numerous physical, emotional, and practical challenges. While health information exists to help patients learn how to manage these challenges, health information seeking often declines over time, recalling information is difficult, and limited time with healthcare providers can leave patients feeling uninformed about their illness. We designed MyPath to overcome these information access challenges. The mobile system offers personalized, dynamic, and trusted health information recommendations to help patients learn about and manage their cancer. Through a seven-month deployment study with breast cancer patients, we found that use of the application encouraged proactive health management behaviors, and identified factors that motivated technology adoption and abandonment. We discuss the implications of these results for facilitating use of mHealth tools by a rural patient population and the importance of scaling support to a large range of information needs. We use this work to demonstrate the value of personalized health information systems and to motivate future CSCW research developing personalized support systems for other health situations with complex information access models.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274347"}, {"primary_key": "3305593", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, S*** Post<PERSON>, and the Search for the Next BitcoinTM: Collective Sensemaking in Cryptocurrency Discussions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Esteban <PERSON>", "Alex &apos;Sandy&apos; Pentland"], "summary": "Participants in cryptocurrency markets are in constant communication with each other about the latest coins and news releases. Do these conversations build hype through the contagiousness of excitement, help the community process information, or play some other role? Using a novel dataset from a major cryptocurrency forum, we conduct an exploratory study of the characteristics of online discussion around cryptocurrencies. Through a regression analysis, we find that coins with more information available and higher levels of technical innovation are associated with higher quality discussion. People who talk about \"serious\" coins tend to participate in discussion displaying signatures of collective intelligence and information processing, while people who talk about \"less serious\" coins tend to display signatures of hype and naïvety. Interviews with experienced forum members also confirm these quantitative findings. These results highlight the varied roles of discussion in the cryptocurrency ecosystem and suggest that discussion of serious coins may be oriented towards earnest, perhaps more accurate, attempts at discovering which coins are likely to succeed.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274348"}, {"primary_key": "3305594", "vector": [], "sparse_vector": [], "title": "Tending Unmarked Graves: Classification of Post-mortem Content on Social Media.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "User-generated content is central to social computing scholarship. However, researchers and practitioners often presume that these users are alive. Failing to account for mortality is problematic in social media where an increasing number of profiles represent those who have died. Identifying mortality can empower designers to better manage content and support the bereaved, as well as promote high-quality data science. Based on a computational linguistic analysis of post-mortem social media profiles and content, we report on classifiers developed to detect mortality and show that mortality can be determined after the first few occurrences of post-mortem content. Applying our classifiers to content from two other platforms also provided good results. Finally, we discuss trade-offs between models that emphasize pre- vs. post-mortem precision in this sensitive context. These results mark a first step toward identifying mortality at scale, and show how designers and scientists can attend to mortality in their work.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274350"}, {"primary_key": "3305595", "vector": [], "sparse_vector": [], "title": "&apos;The Perfect One&apos;: Understanding Communication Practices and Challenges with Animated GIFs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Animated GIFs are increasingly popular in text-based communication. Finding the perfect GIF can make conversations funny, interesting, and engaging, but GIFs also introduce potentials for miscommunication. Through 24 in-depth qualitative interviews, this empirical, exploratory study examines the nuances of communication practices with animated GIFs to better understand why and how GIFs can send unintentional messages. We find participants leverage contexts like source material and interpersonal relationship to find the perfect GIFs for different communication scenarios, while these contexts are also the primary reason for miscommunication and some technical usability issues in GIFs. This paper concludes with a discussion of the important role that different types of context play in the use and interpretations of GIFs, and argues that nonverbal communication tools should account for complex contexts and common ground that communication media rely on.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274349"}, {"primary_key": "3305596", "vector": [], "sparse_vector": [], "title": "Linguistic Signals under Misinformation and Fact-Checking: Evidence from User Comments on Social Media.", "authors": ["Shan <PERSON>", "<PERSON><PERSON>"], "summary": "Misinformation and fact-checking are opposite forces in the news environment: the former creates inaccuracies to mislead people, while the latter provides evidence to rebut the former. These news articles are often posted on social media and attract user engagement in the form of comments. In this paper, we investigate linguistic (especially emotional and topical) signals expressed in user comments in the presence of misinformation and fact-checking. We collect and analyze a dataset of 5,303 social media posts with 2,614,374 user comments from Facebook, Twitter, and YouTube, and associate these posts to fact-check articles from Snopes and PolitiFact for veracity rulings (i.e., from true to false). We find that linguistic signals in user comments vary significantly with the veracity of posts, e.g., we observe more misinformation-awareness signals and extensive emoji and swear word usage with falser posts. We further show that these signals can help to detect misinformation. In addition, we find that while there are signals indicating positive effects after fact-checking, there are also signals indicating potential \"backfire\" effects.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274351"}, {"primary_key": "3305597", "vector": [], "sparse_vector": [], "title": "Neighbourhood Data: Exploring the Role of Open Data in Locally Devolved Policymaking Processes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "There is a growing public, political, and academic discourse around the idea that data has the potential to empower citizens. In particular, evidence-based policymaking is at the centre of national and regional planning processes. At the same time a shift towards Localism in planning means that while citizens and civic groups are centrally involved in decision-making about their communities, they lack the skills, resources and access to data that might inform their decision-making. There is a need to establish new ways of supporting deliberation and decision-making in local planning. We report a study that explores the role of data in the complex processes involved in consultation events and the broader collaborative processes surrounding them. In doing so we highlight the need for the integration of dialogic forms of participation with other locally produced data, and for this to be shared in ways that position data as a resource for action.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274352"}, {"primary_key": "3305598", "vector": [], "sparse_vector": [], "title": "Digestif: Promoting Science Communication in Online Experiments.", "authors": ["<PERSON><PERSON><PERSON>", "Blue A. Jo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online experiments allow researchers to collect data from large, demographically diverse global populations. Unlike in-lab studies, however, online experiments often fail to inform participants about the research to which they contribute. This paper is the first to investigate barriers that prevent researchers from providing such science communication in online experiments. We found that the main obstacles preventing researchers from including such information are assumptions about participant disinterest, limited time, concerns about losing anonymity, and concerns about experimental bias. Researchers also noted the dearth of tools to help them close the information loop with their study participants. Based on these findings, we formulated design requirements and implemented Digestif, a new web-based tool that supports researchers in providing their participants with science communication pages. Our evaluation shows that Digestif's scaffolding, examples, and nudges to focus on participants make researchers more aware of their participants' curiosity about research and more likely to disclose pertinent research information.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274353"}, {"primary_key": "3305600", "vector": [], "sparse_vector": [], "title": "&apos;We can go anywhere&apos;: Understanding Independence through a Case Study of Ride-hailing Use by People with Visual Impairments in metropolitan India.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Sile O&apos;Modhrain", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Vidhya Y", "Jacki O&apos;Neill"], "summary": "Ride-hailing services have received attention as part of the growing work around the sharing economy, but the focus of these studies has largely been on drivers. In this paper, we examine how ride-hailing is transforming the transportation practices of one group of passengers - people with visual impairments in metropolitan India. Through a qualitative study consisting of interviews and observations, we examined the use and impact of these services on our target population, who otherwise contend with chaotic, unreliable, and largely inaccessible modes of transportation. We found that ride-hailing services positively affects participants' notions of independence, and we tease out how independence for our participants is not just about 'doing things alone, without help' but is also situated, social and relative. Furthermore, we show how accessibility, in the case of ride-hailing in India, is a socio-technical and collaborative achievement, involving interactions between the passenger, the driver, and the technology.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274354"}, {"primary_key": "3305601", "vector": [], "sparse_vector": [], "title": "Editors&apos; Message.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It is our great pleasure to welcome you to this issue of the Proceedings of the ACM on Human Computer Interaction, the second to focus on the contributions from the research community Computer-Supported Cooperative Work and Social Computing (CSCW). This journal model allows for rapid publication of papers shortly after acceptance as well as enabling automatic indexing services, such as Institute for Scientific Information (ISI) indexing. The aim is to increase the visibility of the work of authors in this community, and allow for better comparability with fields outside of Computer Science. This issue contains papers accepted over two iterations of the review process. The call for papers for this second issue, with an initial deadline in Spring 2018, attracted 722 submissions, from all around the world. After the first round of reviewing, 326 (45,2%) papers were invited to the Revise and Resubmit phase. The program committee worked hard over August 2018 to arrive at final decisions, with an online editorial committee meeting held to allow for collective deliberation. In the end, 184 papers (25,5%) were accepted. For some of those papers, authors received further shepherding and guidance by a senior committee member. This shows the commitment of the CSCW community to not only ensure high quality contributions, but also to educate and enable authors to write and present their best work for this community. This issue exists because of the dedicated volunteer effort of 132 senior editorial committee members who served as Associate Chairs (ACs), and 837 external expert reviewers to ensure high quality and insightful reviews for all papers in both rounds.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274285"}, {"primary_key": "3305602", "vector": [], "sparse_vector": [], "title": "Creating Better Action Plans for Writing Tasks via Vocabulary-Based Planning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While having a step-by-step breakdown for a task-an action plan-helps people complete tasks, prior work has shown that people prefer not to make action plans for their own tasks. Getting planning support from others could be beneficial, but it is limited by how much domain knowledge people have about the task and how available they are. Our goal is to incorporate the benefits of having action plans in the complex domain of writing, while mitigating the time and effort costs of creating plans. To mitigate these costs, we introduce a vocabulary-a finite set of functions pertaining to writing tasks-as a cognitive scaffold that enables people with necessary context (e.g. collaborators) to generate action plans for others. We develop this vocabulary by analyzing 264 comments, and compare plans created using it with those created without any aid, in an online study with 768 comments (N=145) and a lab study with 96 comments (N=8). We show that using a vocabulary reduces planning time and effort and improves plan quality compared to unstructured planning, and opens the door for automation and task sharing for complex tasks.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274355"}, {"primary_key": "3305603", "vector": [], "sparse_vector": [], "title": "&apos;It&apos;s More Like a Letter&apos;: An Exploration of Mediated Conversational Effort in Message Builder.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Communication technologies for maintaining close personal relationships are often designed to be lightweight and easy to use. While these properties allow for relationships to be maintained with speed and efficiency, they may come at the expense of more effortful messages that are constructed with thought, time and care. This raises the question of how communication technologies might be designed to provoke moments of effortful maintenance from their users. To explore this question, we designed and implemented Message Builder, a text-based communication system that encourages relational partners to send increasingly long messages. We report findings from a field trial in which 14 dyads used Message Builder for everyday relational maintenance. While some of the effort-provoking features of Message Builder were described as problematic, we found that the system had value in guiding users towards authentic and meaningful effort investments that were valuable within their individual relationships.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274356"}, {"primary_key": "3305604", "vector": [], "sparse_vector": [], "title": "The Misgendering Machines: Trans/HCI Implications of Automatic Gender Recognition.", "authors": ["<PERSON><PERSON>"], "summary": "Automatic Gender Recognition (AGR) is a subfield of facial recognition that aims to algorithmically identify the gender of individuals from photographs or videos. In wider society the technology has proposed applications in physical access control, data analytics and advertising. Within academia, it is already used in the field of Human-Computer Interaction (HCI) to analyse social media usage. Given the long-running critiques of HCI for failing to consider and include transgender (trans) perspectives in research, and the potential implications of AGR for trans people if deployed, I sought to understand how AGR and HCI understand the term \"gender\", and how HCI describes and deploys gender recognition technology. Using a content analysis of papers from both fields, I show that AGR consistently operationalises gender in a trans-exclusive way, and consequently carries disproportionate risk for trans people subject to it. In addition, I use the dearth of discussion of this in HCI papers that apply AGR to discuss how HCI operationalises gender, and the implications that this has for the field's research. I conclude with recommendations for alternatives to AGR, and some ideas for how HCI can work towards a more effective and trans-inclusive treatment of gender.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274357"}, {"primary_key": "3305605", "vector": [], "sparse_vector": [], "title": "Managing Organizational Culture in Online Group Mergers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Research in social computing has typically conceptualized community growth as a process through which a group welcomes newcomers individually. However, online communities also grow through formal and informal mergers, where groups of newcomers with shared experiences join in batches. To understand this process, we conducted a six month, comparative ethnography of two mergers of World of Warcraft raid guilds. While one merger led to a single, thriving community, the other led to the dissolution of both pre-merger groups. Analysis of our ethnographic data suggests that differences in managing organizational culture (a concept drawn from organization studies) led the successful and failed cases to diverge. The study contributes to our understanding of why some attempts to integrate members of different communities are more successful than others. We outline several ways that community leaders, researchers, and designers can effectively take organizational culture into account.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274358"}, {"primary_key": "3305606", "vector": [], "sparse_vector": [], "title": "Interface Cues to Promote Disclosure and Build Community: An Experimental Test of Crowd and Connectivity Cues in an Online Sexual Health Forum.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Health forums and support groups depend on participant self-disclosure for their success, but the sensitive nature of personal health concerns raises privacy concerns that may constrain what users are willing to reveal. To address this issue, we explore the impact of visual cues designed to convey (1) two facets of social influence-crowd size and social network connectivity-and (2) provide a frame designed to enhance the forum's sense of community. A 3 (Cue type: Crowd, Connectivity, None) x 2 (<PERSON>aming) factorial experiment (N = 218) showed that cues implying greater crowd size and connectivity lead to more self-disclosure of sensitive information, and higher intentions to revisit the community. Further, user belief in the community-building heuristic positively predicts self-disclosure and intentions, while also moderating the effect of the connectivity cue in a direction which implies that the cue encourages disclosure by triggering the community-building heuristic. Implications for the design of online groups are discussed.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274359"}, {"primary_key": "3305607", "vector": [], "sparse_vector": [], "title": "Reciprocity and Donation: How Article Topic, Quality and Dwell Time Predict Banner Donation on Wikipedia.", "authors": ["Rafal Kocielnik", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Donation-based support for open, peer production projects such as Wikipedia is an important mechanism for preserving their integrity and independence. For this reason understanding donation behavior and incentives is crucial in this context. In this work, using a dataset of aggregated donation information from Wikimedia's 2015 fund-raising campaign, representing nearly 1 million pages from English and French language versions of Wikipedia, we explore the relationship between the properties of contents of a page and the number of donations on this page. Our results suggest the existence of a reciprocity mechanism, meaning that articles that provide more utility value attract a higher rate of donation. We discuss these and other findings focusing on the impact they may have on the design of banner-based fundraising campaigns. Our findings shed more light on the mechanisms that lead people to donate to Wikipedia and the relation between properties of contents and donations.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274360"}, {"primary_key": "3305608", "vector": [], "sparse_vector": [], "title": "Caring for IT Security: Accountabilities, Moralities, and Oscillations in IT Security Practices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Despite being considered a fundamental issue in the design, use, and appropriation of digital technologies, IT security has found but little attention in CSCW so far. Approaches in Human-Computer Interaction and Software Engineering do not account appropriately for the weave of dispersed practices that it takes to 'do' IT security---practices that involve a heterogeneous set of actors and unfold at diverse sites and across organizational, legal, and professional boundaries. In this paper we propose to conceive of IT security through the lens of care, a notion that we draw from Science and Technology Studies. Caring for IT security requires continuous, often invisible work that relies upon tinkering and experimentation and addresses perennial oscillations between in-/securities. Caring for IT security, then, engages with established accountabilities and cultivates a moral stance that refrains from blaming insecurities upon single actors. We conclude with outlining a caring approach to IT security for CSCW.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274361"}, {"primary_key": "3305609", "vector": [], "sparse_vector": [], "title": "Identifying Cognitive Assistance with Mobile Electroencephalography: A Case Study with In-Situ Projections for Manual Assembly.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Manual assembly at production is a mentally demanding task. With rapid prototyping and smaller production lot sizes, this results in frequent changes of assembly instructions that have to be memorized by workers. Assistive systems compensate this increase in mental workload by providing \"just-in-time\" assembly instructions through in-situ projections. The implementation of such systems and their benefits to reducing mental workload have previously been justified with self-perceived ratings. However, there is no evidence by objective measures if mental workload is reduced by in-situ assistance. In our work, we showcase electroencephalography (EEG) as a complementary evaluation tool to assess cognitive workload placed by two different assistive systems in an assembly task, namely paper instructions and in-situ projections. We identified the individual EEG bandwidth that varied with changes in working memory load. We show, that changes in the EEG bandwidth are found between paper instructions and in-situ projections, indicating that they reduce working memory compared to paper instructions. Our work contributes by demonstrating how design claims of cognitive demand can be validated. Moreover, it directly evaluates the use of assistive systems for delivering context-aware information. We analyze the characteristics of EEG as real-time assessment for cognitive workload to provide insights regarding the mental demand placed by assistive systems.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229093"}, {"primary_key": "3305610", "vector": [], "sparse_vector": [], "title": "Entangled with Numbers: Quantified Self and Others in a Team-Based Online Game.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Quantification is a process that produces and communicates numbers, imbued with the expectation of generating knowledge and optimizing human behavior and social process. In this paper, we explore how quantification mediates virtual teamwork through an ethnographic study of quantification in League of Legends, a popular team-based online game with a highly competitive culture. In the game, rich statistics about each individual player's gaming history and performance are publicly available, analyzed and displayed on numerous third-party sites. We describe how players were entangled with numbers. They derived knowledge from numbers but struggled with proper ways of interpretation. They utilized numbers to quantify teammates and opponents, but in-game tensions and conflicts easily ensued. They noticed how quantification became burdensome and stressed the importance of proper use. We discuss how this case of quantified self and others manifests complex relationships between self-knowledge, numerical authority, and virtual teamwork.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274362"}, {"primary_key": "3305611", "vector": [], "sparse_vector": [], "title": "&quot;What do you recommend a complete beginner like me to practice?&quot;: Professional Self-Disclosure in an Online Community.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "CSCW scholarship has previously addressed how professionals use digital technologies for learning and communication, but limited attention has been paid to professional self-disclosure on social media. Acts of self-disclosure-intentionally revealing personal information to others-are often considered beneficial for communication and formation of relationships, and describing the role of disclosure in professional communication is important to advance CSCW research that focuses on occupations or organizational settings. In this paper, we present a mixed-methods study of professional self-disclosure in an online community focused on user experience design (UX), documenting how acts of self-disclosure may support professional development. We found that self-disclosure was frequently used as an effective rhetorical and content-focused strategy to provoke discussions and request assistance with the goal of developing or maintaining professional competence. Through the identification of these self-disclosure strategies, we discuss professional self-disclosure in relation to professional identity development in online communities.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274363"}, {"primary_key": "3305612", "vector": [], "sparse_vector": [], "title": "Rotating Online Behavior Change Interventions Increases Effectiveness But Also Increases Attrition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Behavior change systems help people manage their time online and achieve many other goals. These systems typically consist of a single static intervention, such as a timer or site blocker, to persuade users to behave in ways consistent with their stated goals. However, static interventions decline in effectiveness over time as users begin to ignore them. In this paper, we compare the effectiveness of static interventions to a rotation strategy, where users experience different interventions over time. We built and deployed a browser extension called HabitLab, which features many interventions that the user can enable across social media and other web sites to control their time spent browsing. We ran three in-the-wild field experiments on HabitLab to compare static interventions to rotated interventions. We found that rotating between interventions increased effectiveness as measured by time on site, but also increased attrition: more users uninstalled HabitLab. To minimize attrition, we introduced a just-in-time information design about rotation. This design reduced attrition rates by half. With this research, we suggest that interaction design, paired with rotation of behavior change interventions, can help users gain control of their online habits.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274364"}, {"primary_key": "3305613", "vector": [], "sparse_vector": [], "title": "Complimenting Invisible Work: Identifying Hidden Employee Contributions through a Voluntary, Positive, and Open Work Review System.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Invisible work is an important CSCW research agenda, and also integral to operations within companies. To reveal these hidden practices, studies to date have suggested that companies use IT systems to share location, task progress, and enquiry information among employees, or to conduct research work which identifies informal practices. In this paper, we examine a work review system, in the form of a smartphone app feature, to identify hidden employees' contributions. This Complimenting Feature, developed in April 2017 by an air cargo handling company in Hong Kong, allows its terminal staff to compliment other employees-anyone they like, and anytime they like. In December 2017, we conducted semi-structured interviews with 19 terminal staff regarding their experiences using the app. Our analysis of the interviews found a wide range of invisible work being identified despite the existence of a similar paper review form. We have also found evidence of increased employees' motivation and social learning.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274365"}, {"primary_key": "3305614", "vector": [], "sparse_vector": [], "title": "Combining Crowd and Machines for Multi-predicate Item Screening.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper discusses how crowd and machine classifiers can be efficiently combined to screen items that satisfy a set of predicates. We show that this is a recurring problem in many domains, present machine-human (hybrid) algorithms that screen items efficiently and estimate the gain over human-only or machine-only screening in terms of performance and cost. We further show how, given a new classification problem and a set of classifiers of unknown accuracy for the problem at hand, we can identify how to manage the cost-accuracy trade off by progressively determining if we should spend budget to obtain test data (to assess the accuracy of the given classifiers), or to train an ensemble of classifiers, or whether we should leverage the existing machine classifiers with the crowd, and in this case how to efficiently combine them based on their estimated characteristics to obtain the classification. We demonstrate that the techniques we propose obtain significant cost/accuracy improvements with respect to the leading classification algorithms.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274366"}, {"primary_key": "3305615", "vector": [], "sparse_vector": [], "title": "Towards Informed Practice in HCI for Development.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Our paper provides an enriched understanding of the relationship between research and practice through the study of practitioners variously engaged in field research on technology interventions in the context of global development. By conducting a qualitative inquiry with 33 practitioners from 26 global development organizations, we highlight how these practitioners have different goals, work practices, incentive structures, and expectations than researchers, making it challenging to co-create and coordinate productive and effective partnerships. Despite these challenges, practitioners in global development do appear to value and engage with research as they strive for positive impact across their target communities. Our analysis suggests that the domain of human-computer interaction for development (HCI4D) might benefit from engaging in \"informed practice\" through alignment with design-based implementation research (DBIR), which unites researchers and practitioners in their shared commitment to both research and practice, even if research and practice respectively remain their primary commitments. Our work provides CSCW and HCI4D researchers with new ways to conceptualize and navigate the above research-practice divide. We also emphasize the contribution such an approach might make to CSCW researchers beyond the context of global development, and more broadly concerned with making a positive societal impact with their work.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274368"}, {"primary_key": "3305616", "vector": [], "sparse_vector": [], "title": "Uber in Bangladesh: The Tangled Web of Mobility and Justice.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ridesharing services have been viewed as heralding the next generation of mobility and recognized for their potential to provide an alternate and more flexible model of work. These services have also been critiqued for their treatment of employees, low wages, and other concerns. In this paper, we present a qualitative investigation of the introduction of Uber in Dhaka, Bangladesh. Using interview data from drivers and riders, and content analysis of riders' Facebook posts, we highlight how Uber's introduction into Dhaka's existing transportation infrastructure influenced experiences and practices of mobility in the city. Drawing on <PERSON>'s theory of justice, we demonstrate how the introduction of Uber in Dhaka reinforces existing modes of oppression and introduces new ones, even as it generates room for creative modes of resistance. Finally, we underline algorithms' opacity and veneer of objectivity as a potential source of oppression, call for deepening the postcolonial computing perspective, and make a case for stronger connections between technology interventions and policy.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274367"}, {"primary_key": "3305617", "vector": [], "sparse_vector": [], "title": "Member-Owned Alternatives: Exploring Participatory Forms of Organising with Cooperatives.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cooperatives are member-owned organisations, run for the common benefit of their members. While cooperatives are a longstanding way of organising, they have received little attention in CSCW. In this paper, through interviews with 26 individuals from 24 different cooperatives, our focus is an exploratory inquiry on how cooperatives could expand thinking into what future economies can look like and the part technologies may play in them. We discuss (1) the work to make the co-op work, that is, the special effort involved in managing an enterprise in a democratic and inclusive way, (2) the multiple purposes that cooperatives can serve for their members, well beyond financial benefit, and (3) ICT usage within cooperatives as a site of tension and dialogue. We conclude by discussing the meaning and measures of success in alternative economies, and lessons learned for CSCW scholarship on civic and societal organisations.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274369"}, {"primary_key": "3305618", "vector": [], "sparse_vector": [], "title": "How Much is &apos;Too Much&apos;?: The Role of a Smartphone Addiction Narrative in Individuals&apos; Experience of Use.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In a mixed methods study of parents and teens (n=200), 87% (n=174) of participants used language consistent with smartphone addiction narratives when asked about their smartphone feelings and use. Mental health researchers and clinicians do not consistently agree about whether smartphone addiction exists nor what it would look like if it does. Our goal in this study was to explore the patterns of responses that people invoked when talking about the role of smartphones in their lives and the lives of those around them. Responses suggested that both parents and teens are aware of and potentially influenced by a narrative that smartphones are addictive and can lead to negative, though largely undefined, consequences. We examine potential origins of this narrative, including media coverage, and examine the critical need for a deeper examination in the CSCW community of how this narrative could be influencing well-being, sense of self, and sensemaking around smartphone use.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274370"}, {"primary_key": "3305619", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>, Are You Listening?: Privacy Perceptions, Concerns and Privacy-seeking Behaviors with Smart Speakers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart speakers with voice assistants, like Amazon Echo and Google Home, provide benefits and convenience but also raise privacy concerns due to their continuously listening microphones. We studied people's reasons for and against adopting smart speakers, their privacy perceptions and concerns, and their privacy-seeking behaviors around smart speakers. We conducted a diary study and interviews with seventeen smart speaker users and interviews with seventeen non-users. We found that many non-users did not see the utility of smart speakers or did not trust speaker companies. In contrast, users express few privacy concerns, but their rationalizations indicate an incomplete understanding of privacy risks, a complicated trust relationship with speaker companies, and a reliance on the socio-technical context in which smart speakers reside. Users trade privacy for convenience with different levels of deliberation and privacy resignation. Privacy tensions arise between primary, secondary, and incidental users of smart speakers. Finally, current smart speaker privacy controls are rarely used, as they are not well-aligned with users' needs. Our findings can inform future smart speaker designs; in particular we recommend better integrating privacy controls into smart speaker interaction.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274371"}, {"primary_key": "3305620", "vector": [], "sparse_vector": [], "title": "Negotiating Relation Work with Telehealth Home Care Companionship Technologies that Support Aging in Place.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In response to a perceived caregiver shortage and need to support aging in place, telehealth home care systems are being developed to provide remote care and monitoring to older people. Though research has examined the experiences of teleoperators delivering care through these systems, we know less about the experiences of older adults receiving this care. We report findings from a three-month study of a tablet-based telehealth home care system that provides support for aging in place. We find that there is a mismatch between the designer's conception of how care should be delivered and the ways that participants were interested in using the system. This mismatch exists in four areas: participants' interest in getting to know the human teleoperators and rejecting the virtual avatar, interrogating the interface to figure out the inner workings of the platform, pushing for a more symmetrical relationship, and negotiating the relation work that they were willing to perform in the \"sacred space\" of their homes. We draw on the concept of heteromation to understand the political dimensions of telehealth aging in place technologies. We also provide implications and future directions for technologies requiring relation work as well as the design of avatar-based remote companionship.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274372"}, {"primary_key": "3305621", "vector": [], "sparse_vector": [], "title": "CalcuCafé: Designing for Collaboration Among Coffee Farmers to Calculate Costs of Production.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Zhu", "<PERSON>-<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many smallholder coffee farmers in Latin America join cooperatives for increased access to global markets. This requires them to understand their costs relative to a complex sustainable coffee production process. To that end, we designed CalcuCafé, a web-based application for cooperative technicians and coffee farmers to calculate a farmer's costs of coffee production. We iteratively developed and evaluated CalcuCafé's design with members of two coffee cooperatives in Peru. Our research uncovered different expectations about the application between technicians and farmers, stemming from differing backgrounds, goals, and perspectives. Learning to use the application in a group setting helped overcome these differences and facilitated collaboration, resulting in a strong buy-in for the application. Our paper contributes a research and design effort to support smallholder coffee farmers, an underrecognized group at the intersection of HCI for sustainable agriculture and HCI for development.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274418"}, {"primary_key": "3305622", "vector": [], "sparse_vector": [], "title": "Out of Site: Empowering a New Approach to Online Boycotts.", "authors": ["<PERSON><PERSON>", "Bodhi Alarcon", "<PERSON>", "<PERSON>"], "summary": "GrabYourWallet, #boycottNRA and other online boycott campaigns have attracted substantial public interest in recent months. However, a number of significant challenges are preventing online boycotts from reaching their potential. In particular, complex webs of brands and subsidiaries can make it difficult for participants to conform to the goals of a boycott. Similarly, participants and organizers have limited visibility into a boycott's progress. This affects their ability to use sociotechnical innovations from social computing to incentivize participation. To address these challenges, this paper makes a system contribution: a new boycott tool called Out of Site . Out of Site uses lightweight automation to remove obstacles to successful online boycotts. We describe the design challenges associated with Out of Site and report results from two phases of deployment with the GrabYourWallet and Stop Animal Testing boycott communities. Our findings highlight the potential of boycott-assisting technologies and inform the design of this new class of technologies. Finally, like is the case for many systems in social computing, while we designed Out of Site for pro-social uses, there are a number of easily predictable ways in which the system can be leveraged for anti-social purposes (e.g. exacerbating filter bubble issues, empowering boycotts of businesses owned by racial, ethnic, and religious minorities). As such, we developed for this project a new, very straightforward design approach that treats preventing these anti-social uses as a top-tier design concern. This approach stands in contrast to the status quo of ignoring potential anti-social uses and/or considering them to be a secondary design priority. We discuss how our simple approach may help other research projects reduce their potential negative impacts with minimal burden.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274375"}, {"primary_key": "3305623", "vector": [], "sparse_vector": [], "title": "When SNS Privacy Settings Become Granular: Investigating Users&apos; Choices, Rationales, and Influences on Their Social Experience.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Privacy researchers have suggested various granular audience control techniques for users to manage the access to their disclosed information on social network sites. However, it is unclear how users adopt and utilize such techniques in daily use and how these techniques may impact social interactions with others over time. In this study, we examine users' experience during everyday use of granular audience control techniques in WeChat, one of the most popular social network applications in China. Through an interview study with 24 WeChat users, we find that users adjust their configurations and develop rationales for these configurations over time. They also perceive mixed impacts of WeChat's privacy settings on their information disclosure and social interactions, which brings new challenges to privacy design. We discuss the implications of these findings and make suggestions for the future design of privacy settings in SNSs.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274377"}, {"primary_key": "3305624", "vector": [], "sparse_vector": [], "title": "Tell Me Before You Stream Me: Managing Information Disclosure in Video Game Live Streaming.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Live streaming is a new media format that simultaneously records and broadcasts in real time, in multiple channels including video, audio, and text. A new application area of live streaming is in the video game community, where players stream their gameplay. Since most of the streamed games are team-based, one's live streaming may involve other players' gameplay and disclosure. In the present paper, we aim to understand the attitudes and strategies of players who have been streamed by others in video game live streaming. From an interview study with 20 World of Warcraft (WoW) players, we found that participants had concerns about being streamed due to different factors, and adopted individual and collaborative strategies to cope with their concerns. We discuss privacy challenges in live streaming and present design implications for live streaming tools to improve privacy management.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274376"}, {"primary_key": "3305625", "vector": [], "sparse_vector": [], "title": "CrowdIA: Solving Mysteries with Crowdsourced Sensemaking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The increasing volume of text data is challenging the cognitive capabilities of expert analysts. Machine learning and crowdsourcing present new opportunities for large-scale sensemaking, but we must overcome the challenge of modeling the overall process so that many distributed agents can contribute to suitable components asynchronously and meaningfully. In this paper, we explore how to crowdsource the sensemaking process via a pipeline of modularized steps connected by clearly defined inputs and outputs. Our pipeline restructures and partitions information into \"context slices\" for individual workers. We implemented CrowdIA, a software platform to enable unsupervised crowd sensemaking using our pipeline. With CrowdIA, crowds successfully solved two mysteries, and were one step away from solving the third. The crowd's intermediate results revealed their reasoning process and provided evidence that justifies their conclusions. We suggest broader possibilities to optimize each component, as well as to evaluate and refine previous intermediate analyses to improve the final result.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274374"}, {"primary_key": "3305626", "vector": [], "sparse_vector": [], "title": "Design and Intervention in the Age of &quot;No Alternative&quot;.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper explores the relationship between design and intervention, and how scholars of computing and design might strengthen their repertoire of intervention amidst a pervasive sense of there being \"no alternative\" to structures of inequality and capitalist expansion. Drawing from the authors' long-term ethnographic research on maker, entrepreneurship, and innovation cultures as well as their engagements with professional communities of computing and design, this paper introduces and theorizes modes of intervention that do not fit familiar images of political action such as the countercultural hero or localized resistance. The paper contributes by expanding the analytical repertoire of an interventionist-oriented social computing scholarship. Specifically, it offers three inter-related analytical sensibilities from feminist and critical race studies-\"noticing differently,\" \"walking alongside\" and \"parasitic resistance\"- to support political and activist approaches in CSCW and related fields", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274378"}, {"primary_key": "3305627", "vector": [], "sparse_vector": [], "title": "ReactionBot: Exploring the Effects of Expression-Triggered Emoji in Text Messages.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we present ReactionBot, a system that attaches emoji based on users' facial expressions to text messages on Slack. Through a study of 16 dyads, we found that ReactionBot was able to help communicate participants' affect, reducing the need for participants to self-react with emoji during conversations. However, contrary to our hypothesis, ReactionBot reduced social presence (behavioral interdependence) between dyads. Post study interviews suggest that the emotion feedback through ReactionBot indeed provided valuable nonverbal cues: offered more genuine feedback, and participants were more aware of their own emotions. However, this can come at the cost of increasing anxiety from concerns about negative emotion leakage. Further, the more active role of the system in facilitating the conversation can also result in unwanted distractions and may have attributed to the reduced sense of behavioral interdependence. We discuss implications for utilizing this type of cues in text-based communication.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274379"}, {"primary_key": "3305628", "vector": [], "sparse_vector": [], "title": "MDE in Support of Visualization Systems Design: a Multi-Staged Approach Tailored for Multiple Roles.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visualization systems such as dashboards are commonly used to analyze data and support users in their decision making, in communities as different as medical care, transport and software engineering. The increasing amount of data produced and continuous development of new visualizations exacerbate the difficulty of designing such dashboards, while the visualization need is broaden to specialist and non-specialist final users. In this context, we offer a multi-user approach, based on Model Driven Engineering (MDE). The idea is for the designer to express the visualization need by characterization, according to a given taxonomy. We provide a Domain Specific Language (DSL) to design the system and a Software Product Line (SPL) to capture the technological variability of visualization widgets. We performed a user study, using a software project management use case, to validate if dashboard users and designers are able to use a taxonomy to express their visualization need.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229096"}, {"primary_key": "3305629", "vector": [], "sparse_vector": [], "title": "Fact or Fiction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Subjective reporting polarizes competing viewpoints. However, helping readers to recognize subjective content leads to more impartial discussions. Towards this end, we develop machine learning models that classify sentence objectivity. We contribute a set of linguistic rules for determining sentence objectivity collated from previous work. We also develop a labeled dataset with over 5000 sentences retrieved from various news sources. Further, we evaluate traditional machine learning classification models and artificial neural networks on our dataset. The best performing model, a convolutional neural network, achieved an accuracy of 85% and an AUC of 0.933. Using our subjective-objective sentence classification model, we implement Fact-or-Fiction, an end-to-end web system that highlights objective sentences in user text. Fact-or-Fiction provides additional information, such as links to related web pages and related previous submissions.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274380"}, {"primary_key": "3305630", "vector": [], "sparse_vector": [], "title": "StreamWiki: Enabling Viewers of Knowledge Sharing Live Streams to Collaboratively Generate Archival Documentation for Effective In-Stream and Post Hoc Learning.", "authors": ["<PERSON><PERSON><PERSON>", "Seongkook Heo", "<PERSON>"], "summary": "Knowledge-sharing live streams are distinct from traditional educational videos, at least because of the large concurrently-viewing audience and the real-time discussions between viewers and the streamer. Though this creates unique opportunities for interactive learning, it also brings a challenge for creating a useful archive for post hoc learning. This paper presents the results of interviews with knowledge sharing streamers, their moderators, and viewers to understand current experiences and needs for sharing and learning knowledge through live streaming. Based on those findings, we built StreamWiki, a tool which leverages the viewers during live streams to produce useful archives of the interactive learning experience. On StreamWiki, moderators initiate tasks that viewers complete by conducting microtasks, such as writing a summary, commenting, and voting for informative comments. As a result, a summary document is built in real time. Through the tests of our prototype with streamers and viewers, we found that StreamWiki could help understanding the content and the context of the stream, during the stream and for post hoc learning.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274381"}, {"primary_key": "3305631", "vector": [], "sparse_vector": [], "title": "TableChat: Mobile Food Journaling to Facilitate Family Support for Healthy Eating.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Support from family members is an important determinant of health. In this work, we probe opportunities for facilitating family support with TableChat, a chat-based mobile application for food journaling. Leveraging food as a test case of family support, TableChat virtually extends the experience of bonding over the dinner table. We surveyed 158 people about their existing family support practices and deployed TableChat with 10 families in the field. We found that tangible support was the most common form of support shared in TableChat and also the most appreciated by participants. However, we found that participants valued not only supportive actions taken by their family members, but also those deliberately not taken (e.g., not buying junk food). Finally, families reported that journaling meals eaten apart aided the exchange of support, satisfied curiosity, and provided a \"check-in\" that everything was alright, whereas journaling meals eaten together felt redundant. We conclude with a framework that illustrates how informatics tools can be designed to complement rather than compete with existing family interactions.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274383"}, {"primary_key": "3305632", "vector": [], "sparse_vector": [], "title": "When Crowds Give You Lemons: Filtering Innovative Ideas using a Diverse-Bag-of-Lemons Strategy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Following successful crowd ideation contests, organizations in search of the \"next big thing\" are left with hundreds of ideas. Expert-based idea filtering is lengthy and costly; therefore, crowd-based strategies are often employed. Unfortunately, these strategies typically (1) do not separate the mediocre from the excellent, and (2) direct all the attention to certain idea concepts, while others starve. We introduce DBLemons - a crowd-based idea filtering strategy that addresses these issues by (1) asking voters to identify the worst rather than the best ideas using a \"bag of lemons'' voting approach, and (2) by exposing voters to a wider idea spectrum, thanks to a dynamic diversity-based ranking system balancing idea quality and coverage. We compare DBLemons against two state-of-the-art idea filtering strategies in a real-world setting. Results show that DBLemons is more accurate, less time-consuming, and reduces the idea space in half while still retaining 94% of the top ideas.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274384"}, {"primary_key": "3305633", "vector": [], "sparse_vector": [], "title": "Professional Medical Advice at your Fingertips: An empirical study of an online &quot;Ask the Doctor&quot; platform.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Timely access to professional medical advice is crucial for patient health outcomes. Traditional offline, one-on-one patient-provider interactions are time consuming and costly. As a result, online \"Ask the doctor\" (AtD) services have become increasingly popular, where patients and caregivers can obtain advice from medical professionals at a lower information and transaction cost. In this paper, we present an empirical study of Fenda, an innovative AtD platform recently introduced in China where patients and caregivers can consult a wide variety of healthcare professionals for a small fee. Using qualitative research methods, we analyzed how patients and caregivers interact with medical professionals on this platform, focusing on the nature of the questions asked and user strategies to optimize the usage of the platform. We further derived implications for designing better online AtD services connecting patients and caregivers to medical professionals.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274385"}, {"primary_key": "3305635", "vector": [], "sparse_vector": [], "title": "Opinion Conflicts: An Effective Route to Detect Incivility in Twitter.", "authors": ["<PERSON><PERSON>", "Aishik <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In Twitter, there is a rising trend in abusive behavior which often leads to incivility. This trend is affecting users mentally and as a result they tend to leave Twitter and other such social networking sites thus depleting the active user base. In this paper, we study factors associated with incivility. We observe that the act of incivility is highly correlated with the opinion differences between the account holder (i.e., the user writing the incivil tweet) and the target (i.e., the user for whom the incivil tweet is meant for or targeted), toward a named entity. We introduce a character level CNN model and incorporate the entity-specific sentiment information for efficient incivility detection which significantly outperforms multiple baseline methods achieving an impressive accuracy of 93.3% (4.9% improvement over the best baseline). In a post-hoc analysis, we also study the behavioral aspects of the targets and account holders and try to understand the reasons behind the incivility incidents. Interestingly, we observe that there are strong signals of repetitions in incivil behavior. In particular, we find that there are a significant fraction of account holders who act as repeat offenders - attacking the targets even more than 10 times. Similarly, there are also targets who get targeted multiple times. In general, the targets are found to have higher reputation scores than the account holders.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274386"}, {"primary_key": "3305636", "vector": [], "sparse_vector": [], "title": "Collaborative Design-in-use: An Instrumental Genesis Lens in Multi-device Environments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The fast-growing proliferation of multi-device systems has been reshaping the contexts in which collaborative activity takes place. The evolving materialisation of multi-device environments (MDEs) is likely to have an impact on foundational CSCW research, in ways that go beyond studying cross-device interaction. Designers, developers and researchers are reporting emerging challenges in understanding, supporting and designing for complex collaborative activity in MDEs. We argue that the theoretical perspective of Instrumental Genesis can help unveil the complex, dynamic relationships between design, people, tools, tasks and activities in technology-rich MDEs. In this paper, we use extracts from our research on collaborative work in an MDE to illustrate how ideas from the theory of Instrumental Genesis can help reveal important aspects of change and stability. We show how collaborative design-in-use contributes to the joint evolution of MDEs and the working practices unfolding within them.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274387"}, {"primary_key": "3305637", "vector": [], "sparse_vector": [], "title": "Endorsements on Social Media: An Empirical Study of Affiliate Marketing Disclosures on YouTube and Pinterest.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Online advertisements that masquerade as non-advertising content pose numerous risks to users. Such hidden advertisements appear on social media platforms when content creators or \"influencers\" endorse products and brands in their content. While the Federal Trade Commission (FTC) requires content creators to disclose their endorsements in order to prevent deception and harm to users, we do not know whether and how content creators comply with the FTC's guidelines. In this paper, we studied disclosures within affiliate marketing, an endorsement-based advertising strategy used by social media content creators. We examined whether content creators follow the FTC's disclosure guidelines, how they word the disclosures, and whether these disclosures help users identify affiliate marketing content as advertisements. To do so, we first measured the prevalence of and identified the types of disclosures in over 500,000 YouTube videos and 2.1 million Pinterest pins. We then conducted a user study with 1,791 participants to test the efficacy of these disclosures. Our findings reveal that only about 10% of affiliate marketing content on both platforms contains any disclosures at all. Further, users fail to understand shorter, non-explanatory disclosures. Based on our findings, we make various design and policy suggestions to help improve advertising disclosure practices on social media platforms.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274388"}, {"primary_key": "3305638", "vector": [], "sparse_vector": [], "title": "Crafting Policy Discussion Prompts as a Task for Newcomers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Inspired by policy deliberation methods and iterative writing in crowdsourcing, we developed and evaluated a task in which newcomers to an online policy discussion, before entering the discussion, generate prompts that encourage existing commenters to engage with each other. In an experiment with 453 Amazon Mechanical Turk (AMT) crowd workers, we found that newcomers can often craft acceptable prompts, especially when given guidance on prompt-writing and balanced opinions between the comments they synthesize. However, crafting these prompts had little effect on the quality of comments they posted to a simulated discussion forum following the prompt task, as measured by the reasoning and topic coherence of comments. Our results inform best practices and pose questions for the design of discussion systems, both in general and for online policy discussion in particular.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274390"}, {"primary_key": "3305639", "vector": [], "sparse_vector": [], "title": "How Features of a Civic Design Competition Influences the Collective Understanding of a Problem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> (Tone) Xu", "<PERSON>"], "summary": "From Fortune 500 companies to local communities, organizations often strive to build a shared understanding about complex problems. Design competitions provide a compelling approach to create incentives and infrastructure for gathering insights about a problem-space. In this paper, we present an analysis of a two-month civic design competition focused on transportation challenges in a major US city. We examine how the event structure, discussion platform, and participant interactions affected how a community collectively discussed design constraints and proposals. Ninety-two participants took part in the competition's online discussion, hosted on Slack. Applying a mixed-methods analysis, we found that participants shared less as they settled into teams and, due to the discussion system, had difficulty seeing how topics connected across channels; we also learned that certain messages led participants to add depth to existing topics. Based on the findings we provide recommendations for civic competitions aimed at building knowledge around a problem.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274389"}, {"primary_key": "3305640", "vector": [], "sparse_vector": [], "title": "How Social Ties Influence Hurricane Evacuation Behavior.", "authors": ["Danaë Metaxa-Kakavouli", "<PERSON>", "<PERSON>"], "summary": "Natural disasters bring enormous costs every year, both in terms of lives and materials. Evacuation from potentially affected areas stands out among the most critical factors that can reduce mortality and vulnerability to crisis. We know surprisingly little about the factors that drive this important and often life-saving behavior, though recent work has suggested that social capital may play a critical and previously underestimated role in natural disaster preparedness. Moving beyond retrospective self-reporting and vehicle count estimates, we use social media data from a large number of Facebook users to examine connections between levels of social capital and evacuation behavior. This work is the first of its kind, examining these phenomena across three major U.S. disasters-Hurricane Harvey, Hurricane Irma, and Hurricane Maria-with data on over 1.5 million social media users. Our analysis confirms that, holding confounding factors constant, several aspects of social capital are correlated with whether or not an individual evacuates. Higher levels of bridging and linking social ties correlate strongly with evacuation. However, these social capital related factors are not significantly associated with the rate of return after evacuation.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274391"}, {"primary_key": "3305641", "vector": [], "sparse_vector": [], "title": "Data Handling in Knowledge Infrastructures: A Case Study from Oil Exploration.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Offshore oil exploration is concerned with subsea geological reservoirs that are numerous kilometers below the seabed. These reservoirs are knowable only through a knowledge infrastructure of interconnected technologies that are applied to diverse instrument-generated data. Noise, holes, and inaccuracies are inherent in the data, which depend on the technology producing it. We conducted an interpretative case study of data handling work in the exploration unit of a European oil company. Our findings show how data handling involves the skills needed for managing data identities and ownership, a variety of technologies, and contingent negotiations of data needs. We use the notion of repair to analyze this data handling work and discuss how the concept of repair in data handling involves keeping the knowledge infrastructure navigable and attending to countless details. Our research contributes to the literature on repairing infrastructures by considering how repair relates to data work.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274392"}, {"primary_key": "3305642", "vector": [], "sparse_vector": [], "title": "&apos;Welcome&apos; Changes?: Descriptive and Injunctive Norms in a Wikipedia Sub-Community.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Open online communities rely on social norms for behavior regulation, group cohesion, and sustainability. Research on the role of social norms online has mainly focused on one source of influence at a time, making it difficult to separate different normative influences and understand their interactions. In this study, we use the Focus Theory to examine interactions between several sources of normative influence in a Wikipedia sub-community: local descriptive norms, local injunctive norms, and norms imported from similar sub-communities. We find that exposure to injunctive norms has a stronger effect than descriptive norms, that the likelihood of performing a behavior is higher when both injunctive and descriptive norms are congruent, and that conflicting social norms may negatively impact pro-normative behavior. We contextualize these findings through member interviews, and discuss their implications for both future research on normative influence in online groups and the design of systems that support open collaboration.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274321"}, {"primary_key": "3305643", "vector": [], "sparse_vector": [], "title": "Co-Designing a Device for Behaviour-Based Energy Reduction in a Large Organisation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Decreasing our demand for energy is an important component of the global effort to reduce carbon emissions. Energy consumption, both at home and in the workplace, is greatly influenced by human behaviour. Our research investigates how technological interventions could support behaviour-based energy reduction. This paper reports a Living Lab study on the co-design of a device for improving energy efficiency via behaviour change in a large public-sector organisation. We emphasise the importance of taking a broad view of organisational behaviour, highlighting the complex and indirect ways that it influences energy use. Methods and findings are reported from a series of insight and innovation workshops that we held with staff, and which led to the development of a prototype device - LILEE: The Little Interface for Lab Equipment Efficiency. We describe LILEE and discuss how it embodies wider lessons for the design of effective energy-related behaviour change interventions.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274394"}, {"primary_key": "3305644", "vector": [], "sparse_vector": [], "title": "Public WiFi is for Men and Mobile Internet is for Women: Interrogating Politics of Space and Gender around WiFi Hotspots.", "authors": ["<PERSON><PERSON>"], "summary": "Public WiFi networks are increasingly viewed as last-mile Internet solutions for rural areas given the infrastructure intensive nature of fibre optic broadband connectivity, but how inclusive are they? This study reports on interviews, observations, and practices around WiFi access points in public spaces in a rural community in India. It illustrates the ways in which Internet savvy women may continue to experience exclusions in Internet access and use. Bound by social norms that restrict their movements and tether them to spotty mobile data, women's Internet usage is limited in comparison to men, whose relatively unconstrained mobility permits them access and use of the free WiFi in the community. Additionally, interviews with a commercial WiFi provider reveals naive assumptions about women's Internet habits and gendered mobilities influencing access. The findings suggest that in certain contexts, women may remain invisible as potential customers despite their desire and ability to pay for WiFi access.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274395"}, {"primary_key": "3305645", "vector": [], "sparse_vector": [], "title": "Digitizing Monetary Ecologies: Intended and Unintended Consequences of Introducing a Financial Management App in a Low-Resource Setting.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Jacki O&apos;Neill"], "summary": "This paper reports on the changes in the monetary ecology around loan payments shortly after the introduction of a mobile app in Karnataka, India. The app was designed to be used by a social enterprise working with auto-rickshaw drivers by enabling them to take out loans to buy their auto-rickshaws. The app was intended to provide timely loan information for the drivers and support the collaborative work of loan collection and payment. We report on the initial experiences with the app, exploring both its intended and its unintended consequences. We do this by comparing the workflows before and after introduction of the app through the lens of the three phases of moneywork: that is, what changed during pre-, at-, and posttransaction moments. Whilst the app certainly streamlined the workflows, unintended consequences arose from making previously hidden work visible, as well the shifting of control towards the back office, reducing field agents’ flexibility.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274341"}, {"primary_key": "3305646", "vector": [], "sparse_vector": [], "title": "Personal Informatics in Interpersonal Contexts: Towards the Design of Technology that Supports the Social Ecologies of Long-Term Mental Health Management.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Personal informatics systems for supporting health largely grew out of a \"self\"-centric orientation: self-tracking, self-reflection, self-knowledge, self-experimentation, self-improvement. Health management, however, even when self-driven, is inherently social and depends on a person's direct relationships and broader sociocultural contexts, as an emerging line of research is coming to recognize, study, and support. This is particularly true in the case of mental health. In this paper, we engage with individuals managing the serious mental illness bipolar disorder and members of their support circles to (a) identify key social relations and the roles they play in condition management, (b) characterize patients' complex interactions with these relations (e.g., positive or negative, direct or peripheral, steady or unstable), and (c) understand how personal informatics mediates these recovery relations. Based on these insights, we offer a model of this social ecology, along with design implications for personal informatics systems that are sensitive to these interpersonal contexts.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274396"}, {"primary_key": "3305647", "vector": [], "sparse_vector": [], "title": "The Influence of Friends and Experts on Privacy Decision Making in IoT Scenarios.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As increasingly many Internet-of-Things (IoT) devices collect personal data, users face more privacy decisions. Personal privacy assistants can provide social cues and help users make informed decisions by presenting information about how others have decided in similar cases. To better understand which social cues are relevant and whose recommendations users are more likely to follow, we presented 1000 online participants with nine IoT data-collection scenarios. Some participants were told the percentage of experts or friends who allowed data collection in each scenario, while other participants were provided no social cue. At the conclusion of each scenario, participants were asked whether they would allow the described data collection. Our results help explain under what circumstances users are more or less likely to be swayed by the reported behavior of others in similar scenarios. For example, our results indicate that when friends denied data collection, our participants were more influenced than when friends allowed data collection. On the other hand, participants were more influenced by experts when they allowed data collection. We also observed that influence could get stronger or wear off when participants were exposed to a sequence of scenarios. For example, when experts and friends repeatedly allowed data collection in scenarios with clear risk or denied it in scenarios with clear benefits, participants were less likely to be influenced by them in subsequent scenarios.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274317"}, {"primary_key": "3305648", "vector": [], "sparse_vector": [], "title": "Domino: A Descriptive Framework for Hybrid Collaboration and Coupling Styles in Partially Distributed Teams.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Domino, a descriptive framework for hybrid collaboration and hybrid coupling styles in partially distributed teams. Domino enables researchers to describe, analyze, and understand real-world hybrid collaboration practices, i.e., collaborative practices that involve simultaneous co-located and remote collaboration with phases of both synchronous and asynchronous work that spans multiple groupware applications and devices. It also helps to categorize collaborative activities based on yet undocumented hybrid coupling styles between the members of multiple partially distributed or co-located subgroups. Our Domino framework was derived from initial observations of real-world practice and refined by the detailed analysis of participants' behavior and working styles during a simulation of a complex hybrid collaboration task with six partially distributed teams of four users in our lab. The resulting framework allows researchers to view collaboration through a new analytical lens, use new analytical tools, and also derive implications for the design of collaborative tools.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274397"}, {"primary_key": "3305649", "vector": [], "sparse_vector": [], "title": "You Hacked and Now What?: - Exploring Outcomes of a Corporate Hackathon.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Time bounded events such as hackathons, data dives, codefests, hack-days, sprints or edit-a-thons have increasingly gained attention from practitioners and researchers. Existing research, however, has mainly focused on the event itself, while potential outcomes of hackathons have received limited attention. Furthermore, most research around hackathons focuses on collegiate or civic events. Research around hackathons internal to tech companies, which are nearly ubiquitous, and present significant organizational, cultural, and managerial challenges, remains scarce. In this paper we address this gap by presenting findings from a case study of five teams which participated in a large scale corporate hackathon. Most team members voiced their intentions to continue the projects their worked on during the hackathon, but those whose projects did get continued were characterized by meticulous preparation, a focus on executing a shared vision during the hackathon, extended dissemination activities afterwards and a fit to existing product lines. Such teams were led by individuals who perceived the hackathon as an opportunity to bring their idea to life and advance their careers, and who recruited teams who had a strong interest in the idea and in learning the skills necessary to contribute efficiently. Our analysis also revealed that individual team members perceived hackathon participation to have positive effects on their career parts, networks and skill development.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274398"}, {"primary_key": "3305650", "vector": [], "sparse_vector": [], "title": "The Exchange in StackExchange: Divergences between Stack Overflow and its Culturally Diverse Participants.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Nazareno Andrade", "<PERSON><PERSON><PERSON>"], "summary": "StackExchange is a network of Question &amp; Answer (Q&amp;A) sites that support collaborative knowledge exchange on a variety of topics. Prior research found a significant imbalance between those who contribute content to Q&amp;A sites (predominantly people from Western countries) and those who passively use the site (the so-called \"lurkers\"). One possible explanation for such participation differences between countries could be a mismatch between culturally related preferences of some users and the values ingrained in the design of the site. To examine this hypothesis, we conducted a value-sensitive analysis of the design of the StackExchange site Stack Overflow and contrasted our findings with those of participants from societies with varying cultural backgrounds using a series of focus groups and interviews. Our results reveal tensions between collectivist values, such as the openness for social interactions, and the performance-oriented, individualist values embedded in Stack Overflow's design and community guidelines. This finding confirms that socio-technical sites like Stack Overflow reflect the inherent values of their designers, knowledge that can be leveraged to foster participation equity.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274399"}, {"primary_key": "3305651", "vector": [], "sparse_vector": [], "title": "Creating Guided Code Explanations with chat.codes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Effective communication is crucial for instructors and students in programming courses. However, communicating about code can be difficult --- particularly in asynchronous settings where an instructor authors an explanation meant to be read and understood by a student later on. Communicating about code is uniquely difficult for two reasons. First, because of the dichotomous nature of the explanation, which consists of fragments of code and natural language descriptions. Second, instructors' explanations of code often involve modifying code throughout their explanation. This paper introduces chat.codes, a new tool for creating guided explanations about code. chat.codes introduces two features that make it easier to communicate about code. First, it adds deictic code references that allows instructors to write messages that reference specific regions of code. Second, it tracks and summarizes code edits in-line with messages, allowing instructors to create explanations in stages. An evaluation showed that these features were beneficial for both instructors and students.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274400"}, {"primary_key": "3305652", "vector": [], "sparse_vector": [], "title": "People Tend to Wind Down, Not Up, When They Browse Social Media.", "authors": ["<PERSON>"], "summary": "Researchers have focused intensively on the emotional effects of browsing social media, with many emphasizing possible negative effects and others suggesting the positive emotions in status updates are contagious. Despite this focus, however, very few studies have investigated the actual emotional experience of browsing social media in the moment, and none with more than a few emotions, making it difficult to understand the effects research should endeavor to explain. To address this gap, I use experience sampling with diverse samples of Facebook (N = 362) and Twitter (N = 416) users, assessing the browsing experience across a wide range of emotions. Surprisingly, results provide little evidence of robust positive or negative effects, suggesting instead that the primary effect of browsing social media is a lessening of arousal. That is, contrary to stereotype, people tend to wind down - feel more relaxed, sleepy, bored and so on - not wind up.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274402"}, {"primary_key": "3305653", "vector": [], "sparse_vector": [], "title": "CampusWatch: Exploring Communitysourced Patrolling with Pervasive Mobile Technology.", "authors": ["Sangkeun Park", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Community policing to collaboratively maintain community safety and order in conjunction with law enforcement is becoming increasingly popular and efficient with the use of mobile technologies. Beyond sharing information about local problems such as crime via online discussion forums, there has been an increased focus on the impact of mobile crowdsourcing systems on community policing. In this study, we designed a novel crowdsourced patrolling campaign in which community members schedule their own patrol times and routes, then perform bike-based patrolling with video capturing using their smartphones. We conducted a four-week field study (n=20) on a university campus to verify the campaign's feasibility and observe users' behavior. Our results show key findings about users' task scheduling, event capturing and reporting behaviors, factors affecting task selection and execution and user motivation and engagement. Finally, we discuss several practical design implications in building crowdsourcing systems for community policing.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274403"}, {"primary_key": "3305654", "vector": [], "sparse_vector": [], "title": "Real-Time Anomaly Detection in Elderly Behavior with the Support of Task Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With today's technology, elderly can be supported in living independently in their own homes for a prolonged period of time. Monitoring and analyzing their behavior in order to find possible unusual situation helps to provide the elderly with health warnings at the proper time. Current studies are focusing on the elderly daily activity and the detection of anomalous behaviors aiming to provide the older people with remote support. To this aim, we propose a real-time solution which models the user daily routine using a task model specification and detects relevant contextual events occurred in their life through a context manager. In addition, by a systematic validation through a system that automatically generates wrong sequences of tasks, we show that our algorithm is able to find behavioral deviations from the expected behavior at different times by considering the extended classification of the possible deviations with good accuracy.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229097"}, {"primary_key": "3305655", "vector": [], "sparse_vector": [], "title": "Information Needs in Contemporary Code Review.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Contemporary code review is a widespread practice used by software engineers to maintain high software quality and share project knowledge. However, conducting proper code review takes time and developers often have limited time for review. In this paper, we aim at investigating the information that reviewers need to conduct a proper code review, to better understand this process and how research and tool support can make developers become more effective and efficient reviewers. Previous work has provided evidence that a successful code review process is one in which reviewers and authors actively participate and collaborate. In these cases, the threads of discussions that are saved by code review tools are a precious source of information that can be later exploited for research and practice. In this paper, we focus on this source of information as a way to gather reliable data on the aforementioned reviewers' needs. We manually analyze 900 code review comments from three large open-source projects and organize them in categories by means of a card sort. Our results highlight the presence of seven high-level information needs, such as knowing the uses of methods and variables declared/modified in the code under review. Based on these results we suggest ways in which future code review tools can better support collaboration and the reviewing task.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274404"}, {"primary_key": "3305656", "vector": [], "sparse_vector": [], "title": "Trust in Data Science: Collaboration, Translation, and Accountability in Corporate Data Science Projects.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The trustworthiness of data science systems in applied and real-world settings emerges from the resolution of specific tensions through situated, pragmatic, and ongoing forms of work. Drawing on research in CSCW, critical data studies, and history and sociology of science, and six months of immersive ethnographic fieldwork with a corporate data science team, we describe four common tensions in applied data science work: (un)equivocal numbers, (counter)intuitive knowledge, (in)credible data, and (in)scrutable models. We show how organizational actors establish and re-negotiate trust under messy and uncertain analytic conditions through practices of skepticism, assessment, and credibility. Highlighting the collaborative and heterogeneous nature of real-world data science, we show how the management of trust in applied corporate data science settings depends not only on pre-processing and quantification, but also on negotiation and translation. We conclude by discussing the implications of our findings for data science research and practice, both within and beyond CSCW.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274405"}, {"primary_key": "3305657", "vector": [], "sparse_vector": [], "title": "Mind Your POV: Convergence of Articles and Editors Towards Wikipedia&apos;s Neutrality Norm.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Wikipedia has a strong norm of writing in a \"neutral point of view\" (NPOV). Articles that violate this norm are tagged, and editors are encouraged to make corrections. But the impact of this tagging system has not been quantitatively measured. Does NPOV tagging help articles to converge to the desired style? Do NPOV corrections encourage editors to adopt this style? We study these questions using a corpus of NPOV-tagged articles and a set of lexicons associated with biased language. An interrupted time series analysis shows that after an article is tagged for NPOV, there is a significant decrease in biased language in the article, as measured by several lexicons. However, for individual editors, NPOV corrections and talk page discussions yield no significant change in the usage of words in most of these lexicons, including Wikipedia's own list of \"words to watch.\" This suggests that NPOV tagging and discussion does improve content, but has less success enculturating editors to the site's linguistic norms.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274406"}, {"primary_key": "3305658", "vector": [], "sparse_vector": [], "title": "Operating at a Distance - How a Teleoperated Surgical Robot Reconfigures Teamwork in the Operating Room.", "authors": ["<PERSON>", "<PERSON>", "Malte F. <PERSON>", "<PERSON>"], "summary": "This paper investigates how a teleoperated surgical robot reconfigures teamwork in the operating room by spatially redistributing team members. We report on findings from two years of fieldwork at two hospitals, including interviews and video data. We find that while in non-robotic cases team members huddle together, physically touching, introduction of a surgical robot increases physical and sensory distance between team members. This spatial rearrangement has implications for both cognitive and affective dimensions of collaborative surgical work. Cognitive distance is increased, necessitating new efforts to maintain situation awareness and common ground. Moreover, affective distance is introduced, decreasing sensitivity to shared and non-shared affective states and leading to new practices aimed at restoring affective connection within the team. We describe new forms of physical, cognitive, and affective distance associated with teleoperated robotic surgery, and the effects these have on power distribution, practice, and collaborative experience within the surgical team.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274407"}, {"primary_key": "3305659", "vector": [], "sparse_vector": [], "title": "Differential Vulnerabilities and a Diversity of Tactics: What Toolkits Teach Us about Cybersecurity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Richmond Y<PERSON>"], "summary": "We investigate cybersecurity toolkits, collections of public facing materials intended to help users achieve security online. Through a qualitative analysis of 41 online toolkits, we present a set of key design dimensions: agentive scale (who is responsible for security), achievability (can security be achieved), and interventional stage (when are security measures taken). Recognizing toolkits as socially and culturally situated, we surface ways in which toolkits construct security as a value and, in so doing, how they construct people as (in)secure users. We center the notion of differential vulnerabilities, an understanding of security that recognizes safety as socially contingent, adversaries as unstable figures, and risk as differentially applied based on markers of relational position (e.g. class, race, religion, gender, geography, experience). We argue that differential vulnerabilities provides a key design concern in future security resources, and a critical concept for security discourses.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274408"}, {"primary_key": "3305660", "vector": [], "sparse_vector": [], "title": "How Latino Children in the U.S. Engage in Collaborative Online Information Problem Solving with their Families.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Approximately 8 million U.S. children have at least one immigrant parent. Lower-socioeconomic (SES) immigrant parents often rely on their children's language skills to problem-solve family needs-a practice known as brokering. Yet it is unknown how children use their language and digital literacy skills to search and broker information online. This paper examines how children with lower-SES immigrant parents search and broker information online. We focused on Latino families as they are the fastest growing U.S. minority group. We conducted in-home interviews and observations of search tasks with 23 parent-child dyads. We demonstrate: (1) how Online Search and Brokering (OSB) is impacted by familial values and resources at an individual, family, community, and digital infrastructure level, and (2) through search vignettes, how parent-child dyads problem-solve family needs through OSB. Our work demonstrates a different purpose of technology use in families: intergenerational, bilingual, and online co-searching to problem-solve family needs.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274409"}, {"primary_key": "3305661", "vector": [], "sparse_vector": [], "title": "Who Models the World?: Collaborative Ontology Creation and User Roles in Wikidata.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Wikidata is a collaborative knowledge graph which is central to many academic and industry IT projects. Its users are responsible for maintaining the schema that organises this knowledge into classes, properties, and attributes, which together form the Wikidata 'ontology'. In this paper, we study the relationship between different Wikidata user roles and the quality of the Wikidata ontology. To do so we first propose a framework to evaluate the ontology as it evolves. We then cluster editing activities to identify user roles in monthly time frames. Finally, we explore how each role impacts the ontology. Our analysis shows that the Wikidata ontology has uneven breadth and depth. We identified two user roles: contributors and leaders. The second category is positively associated to ontology depth, with no significant effect on other features. Further work should investigate other dimensions to define user profiles and their influence on the knowledge graph.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274410"}, {"primary_key": "3305662", "vector": [], "sparse_vector": [], "title": "Normative Tensions in Shared Augmented Reality.", "authors": ["Lev <PERSON>ki", "<PERSON>", "<PERSON><PERSON>"], "summary": "Novel collaborative technologies afford new modes of behavior, which are often not regulated by established social norms. In particular, shared augmented reality (AR) - where multiple users can create, attach, and interact with the same virtual elements embedded into the physical environment - has the potential to interrupt current social norms of behavior. The objective of our study is to shed light on the ways in which shared AR challenges existing behavioral expectations. Using a simulated lab experimental design, we performed a study of users' interactions in a shared AR setting. Content analysis of participants' interviews reveals users' concerns over the preservation of their self- and social identity, as well as concerns related to personal space and the sense of psychological ownership over one's body and belongings. Our findings also point to the need for regulation of shared AR spaces and design of the technology's control mechanisms.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274411"}, {"primary_key": "3305663", "vector": [], "sparse_vector": [], "title": "Making Open Data Work for Civic Advocacy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The value of data in supporting citizen participation in processes of place-making and community building is widely recognised. While the open data movement now permits citizens to acquire governmental data relating to their communities, little to no effort is made to ensure that these datasets are accessible and interpretable by non-professionals. Through a series of community engagements spanning an 18-month period, we co-designed Data:In Place, an open source web tool which supports citizens in accessing, interpreting and making sense of open data. Leveraging visual map-based querying, citizens can access official statistics about their community, interrogate the data, and map their own data sources to create data visualisations. Reflecting on the participatory design process and the designed technology, we provide a framing to make open data work for civic advocacy.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274412"}, {"primary_key": "3305664", "vector": [], "sparse_vector": [], "title": "The Spirit of the City: Using Social Media to Capture Neighborhood Ambiance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Place ambiance has a huge influence over how we perceive places. Despite its importance, ambiance has been crucially overlooked by urban planners and scientists alike, not least because it is difficult to record and analyze at scale. We explored the possibility of using social media data to reliably map the ambiance of neighborhoods in the entire city of London. To this end, we collected geo-referenced picture tags from Flickr and matched those tags with the words in a newly created ambiance dictionary. In so doing, we made four main contributions: i) map the ambiance of London neighborhoods; ii) ascertain that such a mapping meets residents' expectations, which are derived from a survey we conducted; iii) show that computer vision techniques upon geo-referenced pictures are of predictive power for neighborhood ambiance; and iv) explain each prediction of a neighborhood's ambiance by identifying the picture that best reflects the meaning of that ambiance (e.g., artsy) in that neighborhood (e.g., South Kensington---the richest and most traditional neighborhood---and Shoreditch---among the most progressive and hipster neighborhoods in the city---are both 'artsy' but in very different ways). The combination of the predictive power of mapping ambiance from images and the ability to explain those predictions makes it possible to discover hidden gems across the city at an unprecedented scale.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274413"}, {"primary_key": "3305665", "vector": [], "sparse_vector": [], "title": "Socio-spatial Self-organizing Maps: Using Social Media to Assess Relevant Geographies for Exposure to Social Processes.", "authors": ["<PERSON>nal <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Social media offers a unique window into attitudes like racism and homophobia, exposure to which are important, hard to measure and understudied social determinants of health. However, individual geo-located observations from social media are noisy and geographically inconsistent. Existing areas by which exposures are measured, like Zip codes, average over irrelevant administratively-defined boundaries. Hence, in order to enable studies of online social environmental measures like attitudes on social media and their possible relationship to health outcomes, first there is a need for a method to define the collective, underlying degree of social media attitudes by region. To address this, we create the Socio-spatial-Self organizing map, \"SS-SOM\" pipeline to best identify regions by their latent social attitude from Twitter posts. SS-SOMs use neural embedding for text-classification, and augment traditional SOMs to generate a controlled number of non-overlapping, topologically-constrained and topically-similar clusters. We find that not only are SS-SOMs robust to missing data, the exposure of a cohort of men who are susceptible to multiple racism and homophobia-linked health outcomes, changes by up to 42% using SS-SOM measures as compared to using Zip code-based measures.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274414"}, {"primary_key": "3305666", "vector": [], "sparse_vector": [], "title": "Exploring Cooperative Fitness Tracking to Encourage Physical Activity among Office Workers.", "authors": ["Xipei Ren", "<PERSON>", "Yuan Lu", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents a field study on using peer-based cooperative fitness tracking (PCFT) to promote workplace fitness. The social bonding achieved through a collective fitness goal and the sharing of fitness data between two co-workers has been explored as a motivational factor that can encourage physical activity. The study involved 10 dyads of co-workers in two groups (a distributed vs. a co-located group) based on their proximity at work. The effectiveness of the proposed PCFT was examined by comparing fitness data over a period of three weeks: the baseline week, the PCFT intervention week, and the post-intervention week. The proximity effects on PCFT were investigated by comparing the fitness data, goal commitment, and interview results between the two groups. The quantitative results showed that the physical activity of participants in the co-located group improved significantly after the PCFT intervention. The qualitative results suggested that PCFT may improve the awareness of being physically active, stimulate exchange of knowledge to support active lifestyles and facilitate including fitness breaks in the daily work routine. Based on these findings, we discuss design implications for the future development of the PCFT-based applications and their potential contribution to increased office vitality.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274415"}, {"primary_key": "3305667", "vector": [], "sparse_vector": [], "title": "Fostering Civil Discourse Online: Linguistic Behavior in Comments of #MeToo Articles across Political Perspectives.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Linguistic style and affect shape how users perceive and assess political content on social media. Using linguistic methods to compare political discourse on far-left, mainstream and alt-right news articles covering the #MeToo movement, we reveal rhetorical similarities and differences in commenting behavior across the political spectrum. We employed natural language processing techniques and qualitative methods on a corpus of approximately 30,000 Facebook comments from three politically distinct news publishers. Our findings show that commenting behavior reflects how social movements are framed and understood within a particular political orientation. Surprisingly, these data reveal that the structural patterns of discourse among commenters from the two alternative news sites are similar in terms of their relationship to those from the mainstream - exhibiting polarization, generalization, and othering of perspectives in political conversation. These data have implications for understanding the possibility for civil discourse in online venues and the role of commenting behavior in polarizing media sources in undermining such discourse.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274416"}, {"primary_key": "3305668", "vector": [], "sparse_vector": [], "title": "Auditing Partisan Audience Bias within Google Search.", "authors": ["<PERSON>", "Shan <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "There is a growing consensus that online platforms have a systematic influence on the democratic process. However, research beyond social media is limited. In this paper, we report the results of a mixed-methods algorithm audit of partisan audience bias and personalization within Google Search. Following <PERSON>'s inauguration, we recruited 187 participants to complete a survey and install a browser extension that enabled us to collect Search Engine Results Pages (SERPs) from their computers. To quantify partisan audience bias, we developed a domain-level score by leveraging the sharing propensities of registered voters on a large Twitter panel. We found little evidence for the \"filter bubble'' hypothesis. Instead, we found that results positioned toward the bottom of Google SERPs were more left-leaning than results positioned toward the top, and that the direction and magnitude of overall lean varied by search query, component type (e.g. \"answer boxes\"), and other factors. Utilizing rank-weighted metrics that we adapted from prior work, we also found that Google's rankings shifted the average lean of SERPs to the right of their unweighted average.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274417"}, {"primary_key": "3305669", "vector": [], "sparse_vector": [], "title": "INFEX: A Unifying Framework for Cross-Device Information Exploration and Exchange.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Beat Signer"], "summary": "In our daily lives we are witnessing a proliferation of digital devices including tablets, smartphones, digital cameras or wearable appliances. A major effort has been made to enable these devices to exchange information in intelligent spaces and collaborative settings. However, the arising technical challenges often manifest themselves to end users as limitations, inconsistencies or added complexity. A wide range of existing and emerging devices cannot be used with existing solutions for cross-device information exchange due to restrictions in terms of the supported communication protocols, hardware or media types. We present INFEX, a general and extensible framework for cross-device information exploration and exchange. While existing solutions often support a restricted set of devices and networking protocols, our unifying and extensible INFEX framework enables information exchange and exploration across arbitrary devices and also supports devices that cannot run custom software or do not offer their own I/O modalities. The plug-in based INFEX architecture allows developers to provide custom but consistent user interfaces for information exchange and exploration across a heterogeneous set of devices.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3179427"}, {"primary_key": "3305671", "vector": [], "sparse_vector": [], "title": "Hive: Collective Design Through Network Rotation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Collectives gather online around challenges they face, but frequently fail to envision shared outcomes to act on together. Prior work has developed systems for improving collective ideation and design by exposing people to each others' ideas and encouraging them to intermix those ideas. However, organizational behavior research has demonstrated that intermixing ideas does not result in meaningful engagement with those ideas. In this paper, we introduce a new class of collective design system that intermixes people instead of ideas: instead of receiving mere exposure to others' ideas, participants engage deeply with other members of the collective who represent those ideas, increasing engagement and influence. We thus present Hive: a system that organizes a collective into small teams, then intermixes people by rotating team membership over time. At a technical level, Hive must balance two competing forces: (1) networks are better at connecting diverse perspectives when network efficiency is high, but (2) moving people diminishes tie strength within teams. Hive balances these two needs through network rotation: an optimization algorithm that computes who should move where, and when. A controlled study compared network rotation to alternative rotation systems which maximize only tie strength or network efficiency, finding that network rotation produced higher-rated proposals. Hive has been deployed by Mozilla for a real-world open design drive to improve Firefox accessibility.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274420"}, {"primary_key": "3305672", "vector": [], "sparse_vector": [], "title": "&apos;The Government Spies Using Our Webcams&apos;: The Language of Conspiracy Theories in Online Discussions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Conspiracy theories are omnipresent in online discussions---whether to explain a late-breaking event that still lacks official report or to give voice to political dissent. Conspiracy theories evolve, multiply, and interconnect, further complicating efforts to understand them and to limit their propagation. It is therefore crucial to develop scalable methods to examine the nature of conspiratorial discussions in online communities. What do users talk about when they discuss conspiracy theories online? What are the recurring elements in their discussions? What do these elements tell us about the way users think? This work answers these questions by analyzing over ten years of discussions in r/conspiracy---an online community on Reddit dedicated to conspiratorial discussions. We focus on the key elements of a conspiracy theory: the conspiratorial agents, the actions they perform, and their targets. By computationally detecting agent?action?target triplets in conspiratorial statements, and grouping them into semantically coherent clusters, we develop a notion of narrative-motif to detect recurring patterns of triplets. For example, a narrative-motif such as \"governmental agency-controls-communications\" represents the various ways in which multiple conspiratorial statements denote how governmental agencies control information. Thus, narrative-motifs expose commonalities between multiple conspiracy theories even when they refer to different events or circumstances. In the process, these representations help us understand how users talk about conspiracy theories and offer us a means to interpret what they talk about. Our approach enables a population-scale study of conspiracy theories in alternative news and social media with implications for understanding their adoption and combating their spread.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274421"}, {"primary_key": "3305673", "vector": [], "sparse_vector": [], "title": "Data Bites Man: The Production of Malaria by Technology.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Malaria surveillance is a practice concerned with collection and analysis of data. This paper presents an ethnographical account of an international team of researchers producing data about malaria in the Zanzibar archipelago. We show that malaria is increasingly constituted by data, and inextricably interwoven with the practices of data workers using ICT tools. Through the practices we document here malaria: 1) becomes a problem to be managed by asymptomatic, as well as symptomatic individuals, 2) increases its geographical incidence through surveillance data and articulation work, and 3) becomes more certain, through coordination mechanisms enabled by ICT. As electronic data, malaria builds and mobilizes diverse human, organizational, and infrastructural worlds around it, who must now be dedicated to its production, management, and care.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274422"}, {"primary_key": "3305674", "vector": [], "sparse_vector": [], "title": "Resolvable vs. Irresolvable Disagreement: A Study on Worker Deliberation in Crowd Work.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Crowdsourced classification of data typically assumes that objects can be unambiguously classified into categories. In practice, many classification tasks are ambiguous due to various forms of disagreement. Prior work shows that exchanging verbal justifications can significantly improve answer accuracy over aggregation techniques. In this work, we study how worker deliberation affects resolvability and accuracy using case studies with both an objective and a subjective task. Results show that case resolvability depends on various factors, including the level and reasons for the initial disagreement, as well as the amount and quality of deliberation activities. Our work reinforces the finding that deliberation can increase answer accuracy and the importance of verbal discussion in this process. We contribute a new public data set on worker deliberation for text classification tasks, and discuss considerations for the design of deliberation workflows for classification.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274423"}, {"primary_key": "3305675", "vector": [], "sparse_vector": [], "title": "Safe Spaces and Safe Places: Unpacking Technology-Mediated Experiences of Safety and Harm with Transgender People.", "authors": ["<PERSON>", "<PERSON>", "Foa<PERSON>"], "summary": "Transgender individuals in the United States face significant threats to interpersonal safety; however, there has as yet been relatively little research in the HCI and CSCW communities to document transgender individuals' experiences of technology-mediated safety and harm. In this study, we interviewed 12 transgender and non-binary individuals to understand how they find, create, and navigate safe spaces using technology. Managing safety was a universal concern for our transgender participants, and they experienced complex manifestations of harm through technology. We found that harmful experiences for trans users could arise as targeted or incidental affronts, as sourced from outsiders or insiders, and as directed against individuals or entire communities.. Notably, some violations implicated technology design, while others tapped broader social dynamics. Reading our findings through the notions of 'space\" and 'place,\" we unpack challenges and opportunities for building safer futures with transfolk, other vulnerable users, and their allies.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274424"}, {"primary_key": "3305676", "vector": [], "sparse_vector": [], "title": "Participatory Design of Technologies to Support Recovery from Substance Use Disorders.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Substance use disorders, such as alcoholism and drug addiction, are a widespread and hazardous public health issue. Technology designed for the needs and values of people in recovery may be able to supplement traditional treatment options, enhance long-term abstinence maintenance, and create new opportunities for social support. We conducted a series of participatory design workshops with women in recovery from substance use disorders to identify design opportunities for supportive technologies that align with the specific values, practices and traditions of the recovery community. Through a data-driven inductive qualitative analysis, we identify five major themes that may be addressed with technology: 1) supporting twelve-step traditions and practices, 2) management of restlessness and moments of crisis, 3) agency and control over privacy and personal safety, 4) tracking progress and maintaining motivation, and 5) constructing a new normal. We connect these themes to specific implications for design.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274425"}, {"primary_key": "3305677", "vector": [], "sparse_vector": [], "title": "The Social Roles of Bots: Evaluating Impact of Bots on Discussions in Online Communities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Bots, or programs designed to engage in social spaces and perform automated tasks, are typically understood as automated tools or as social \"chatbots.\" In this paper, we consider their place alongside users in the emerging social ecosystem of audience participation platforms, through the application of Structural Role Theory. We perform a large-scale analysis of activity levels of user-designed bots on Twitch, finding that they communicate at a much greater rate than any other type of user. We build on a classification scheme derived from prior literature on bot functionalities to identify the roles bots play on Twitch, how these roles vary across different types of Twitch communities, and how users engage with them and vice versa. We conclude with a discussion of what roles are missing and where opportunities lie to re-conceptualize and re-design bots as social actors who help communities grow and evolve.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274426"}, {"primary_key": "3305679", "vector": [], "sparse_vector": [], "title": "Web5VR: A Flexible Framework for Integrating Virtual Reality Input and Output Devices on the Web.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The availability of consumer-level devices for both visualising and interacting with Virtual Reality (VR) environments opens the opportunity to introduce more immersive contents and experiences, even on the web. For reaching a wider audience, developing VR applications in a web environment requires a flexible adaptation to the different input and output devices that are currently available. This paper examines the required support and explores how to develop VR applications based on web technologies that can adapt to different VR devices. We summarize the main engineering challenges and we describe a flexible framework for integrating and exploiting various VR devices for both input and output. Using such framework, we describe how we re-implemented four manipulation techniques from the literature to enable them within the same application, providing details on how we adapted its parts for different input and output devices such as Kinect and Leap Motion. Finally, we briefly examine the usability of the final application using our framework.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3179429"}, {"primary_key": "3305680", "vector": [], "sparse_vector": [], "title": "Personal Curation in a Museum.", "authors": ["<PERSON>", "Rogers Hall"], "summary": "An established body of work in CSCW and related communities studies social and cooperative interaction in museums and cultural heritage sites. A separate and growing body of research in these same communities is developing ways to understand the design and use of social media from a curating perspective. A curating perspective focuses on how social media is designed and used by people to develop and manage their own digital archives. This paper uses a cultural heritage museum as the empirical basis and setting along with new information visualization methods we have developed to better integrate these bodies of work and introduce the concept of personal curation; a socio-technical practice in which people collect, edit, and share information using personal information devices and social media as they move through physical environments rich with meaning potential. In doing so this paper makes three contributions. First, it illustrates how to combine a spatial focus on people's movement and interaction through the physical environment with an analysis of social media use in order to gain a deeper understanding of practices such as personal curation. Second, it shows in greater detail how visitors to museums and cultural heritage sites use and link digital information with physical information to shape others' understandings of cultural heritage. Third, it suggests how museums and cultural heritage sites may leverage personal curation to support more expansive learning opportunities for visitors.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274427"}, {"primary_key": "3305681", "vector": [], "sparse_vector": [], "title": "Older Adults and Crowdsourcing: Android TV App for Evaluating TEDx Subtitle Quality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we describe the insights from an exploratory qualitative pilot study testing the feasibility of a solution that would encourage older adults to participate in online crowdsourcing tasks in a non-computer scenario. Therefore, we developed an Android TV application using Amara API to retrieve subtitles for TEDx talks which allows the participants to detect and categorize errors to support the quality of the translation and transcription processes. It relies on the older adults' innate skills as long-time native language users and the motivating factors of this socially and personally beneficial task. The study allowed us to verify the underlying concept of using Smart TVs as interfaces for crowdsourcing, as well as possible barriers, including the interface, configuration issues, topics and the process itself. We have also assessed the older adults' interaction and engagement with this TV-enabled online crowdsourcing task and we are convinced that the design of our setup addresses some key barriers to crowdsourcing by older adults. It also validates avenues for further research in this area focused on such considerations as autonomy and freedom of choice, familiarity, physical and cognitive comfort as well as building confidence and the edutainment value.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274428"}, {"primary_key": "3305682", "vector": [], "sparse_vector": [], "title": "&quot;I just let him cry...: Designing Socio-Technical Interventions in Families to Prevent Mental Health Disorders.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ferran <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Interventions that help children develop protective factors against mental health disorders are an inherently social endeavour, relying on a number of actors from within the family as well as the school context. Little work thus far in CSCW and HCI has examined the potential of technology to support or enhance such interventions. This paper provides the first steps to unpacking this socio-technical design space, focusing on emotional regulation (ER) as a specific instance of a protective factor. We combine a user-centred approach to understanding lived experiences of families (interviews, design workshops) with an expert-led understanding of what makes interventions psychologically effective. Our findings suggest the potential of technology to enable a shift in how prevention interventions are designed and delivered: empowering children and parents through a new model of 'child-led, situated interventions', where participants learn through actionable support directly within family life, as opposed to didactic in-person workshops and a subsequent 'skills application'. This conceptual model was then instantiated in a technology probe, which was deployed with 14 families. The promising field study findings provide an initial proof-of-concept validation of the proposed approach.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274429"}, {"primary_key": "3305683", "vector": [], "sparse_vector": [], "title": "Mapping Silences, Reconfiguring Loss: Practices of Damage Assessment &amp; Repair in Post-Earthquake Nepal.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the aftermath of major natural disasters, governments, aid agencies, and affected populations engage in practices of sense-making to gauge the extent and severity of the crisis, direct response activities, and coordinate recovery planning. To understand the conduct and implications of these practices, we examined the official damage assessment implemented by the Government of Nepal following the April 2015 earthquake. In addition, we undertook participatory mapping to examine the consequences of this assessment in the Langtang Valley, a severely-affected area of the country. We argue that the informatics of post-disaster damage assessment in Nepal played a primary role in narrating the events of the 2015 earthquake, legitimating particular paths toward recovery in the aftermath, and limiting opportunities for alternative configurations of social life that emerge during disasters. Our research demonstrates the ways that forms of sense-making afforded by information technologies play central roles in enacting repair-work following crisis and breakdown.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274430"}, {"primary_key": "3305684", "vector": [], "sparse_vector": [], "title": "Informating Crisis: Expanding Critical Perspectives in Crisis Informatics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the past 20 years, the practices of crisis preparedness, response, and recovery have become increasingly dependent on information and communication technology (ICT) to accomplish their work. More recently, crisis informatics has developed an analysis of these phenomena from social and computational perspectives. To further to assess the consequences and opportunities of technological developments in the field, we re-interpret the concept of informating, first developed by <PERSON><PERSON><PERSON> to describe the impacts of technological changes on the workplace during the 1980s. We draw on four contemporary examples of how ICT is changing the way we conceive of and respond to natural hazards to offer a new reading of the concept of informating in the growing field of crisis informatics. We then argue that this concept suggests the adoption of a more critical agenda for crisis informatics research to better respond to contemporary challenges presented by climate change and natural hazards.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274431"}, {"primary_key": "3305685", "vector": [], "sparse_vector": [], "title": "Crowdsourcing-Based Web Accessibility Evaluation with Golden Maximum Likelihood Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Web accessibility evaluation examines how well websites comply with accessibility guidelines which help people with disabilities to perceive, navigate and contribute to the Web. This demanding task usually requires manual assessment by experts with many years of training and experience. However, not enough experts are available to carry out the increasing number of evaluation projects while non-experts often have different opinions about the presence of accessibility barriers. Addressing these issues, we introduce a crowdsourcing system with a novel truth inference algorithm to derive reliable and accurate assessments from conflicting opinions of evaluators. Extensive evaluation on 23,901 complex tasks assessed by 50 people with and without disabilities shows that our approach outperforms state of the art approaches. In addition, we conducted surveys to identify frequent barriers that people with disabilities are facing in their daily lives and the difficulty to access Web pages when they encounter these barriers. The frequencies and severities of barriers correlate with their derived importance in our evaluation project.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274432"}, {"primary_key": "3305686", "vector": [], "sparse_vector": [], "title": "See No Evil, Hear No Evil: Audio-Visual-Textual Cyberbullying Detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emerging multimedia communication apps are allowing for more natural communication and richer user engagement. At the same time, they can be abused to engage in cyberbullying, which can cause significant psychological harm to those affected. Thus, with the growth in multimodal communication platforms, there is an urgent need to devise multimodal methods for cyberbullying detection and prevention. However, there are no existing approaches that use automated audio and video analysis to complement textual analysis. Based on the analysis of a human-labeled cyberbullying data-set of Vine \"media sessions' (six-second videos, with audio, and corresponding text comments), we report that: 1) multiple audio and visual features are significantly associated with the occurrence of cyberbullying, and 2) audio and video features complement textual features for more accurate and earlier cyberbullying detection. These results pave the way for more effective cyberbullying detection in emerging multimodal (audio, visual, virtual reality) social interaction spaces.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274433"}, {"primary_key": "3305687", "vector": [], "sparse_vector": [], "title": "360Anywhere: Mobile Ad-hoc Collaboration in Any Environment using 360 Video and Augmented Reality.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Haihua Zhang", "<PERSON>"], "summary": "360-degree video is increasingly used to create immersive user experiences; however, it is typically limited to a single user and not interactive. Recent studies have explored the potential of 360 video to support multi-user collaboration in remote settings. These studies identified several challenges with respect to 360 live streams, such as the lack of gaze awareness, out-of-sync views, and missed gestures. To address these challenges, we created 360Anywhere, a framework for 360 video-based multi-user collaboration that, in addition to allowing collaborators to view and annotate a 360 live stream, also supports projection of annotations in the 360 stream back into the real-world environment in real-time. This enables a range of collaborative augmented reality applications not supported with existing tools. We present the 360Anywhere framework and tools that allow users to generate applications tailored to specific collaboration and augmentation needs with support for remote collaboration. In a series of exploratory design sessions with users, we assess 360Anywhere's power and flexibility for three mobile ad-hoc scenarios. Using 360Anywhere, participants were able to set up and use fairly complex remote collaboration systems involving projective augmented reality in less than 10 minutes.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229091"}, {"primary_key": "3305688", "vector": [], "sparse_vector": [], "title": "XD-AR: Challenges and Opportunities in Cross-Device Augmented Reality Application Development.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Haihua Zhang", "<PERSON>", "<PERSON>"], "summary": "Augmented Reality (AR) developers face a proliferation of new platforms, devices, and frameworks. This often leads to applications being limited to a single platform and makes it hard to support collaborative AR scenarios involving multiple different devices. This paper presents XD-AR, a cross-device AR application development framework designed to unify input and output across hand-held, head-worn, and projective AR displays. XD-AR's design was informed by challenging scenarios for AR applications, a technical review of existing AR platforms, and a survey of 30 AR designers, developers, and users. Based on the results, we developed a taxonomy of AR system components and identified key challenges and opportunities in making them work together. We discuss how our taxonomy can guide the design of future AR platforms and applications and how cross-device interaction challenges could be addressed. We illustrate this when using XD-AR to implement two challenging AR applications from the literature in a device-agnostic way.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229089"}, {"primary_key": "3305689", "vector": [], "sparse_vector": [], "title": "Growing Tiny Publics: Small Farmers&apos; Social Movement Strategies.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Drawing from fieldwork of 14 small food farms in the Midwest, we describe the on-the-ground, practical challenges of doing and communicating sustainability when local food production is not well-supported. We illustrate how farmers enact learned and honed tactics of sustainability at key sites such as farmers' markets and the Internet with consumers. These tactics reveal tensions with dominant discourse from government, Big Ag, and popular culture. The success of these tactics depends on farmers having fortitude--control, resilience, and the wherewithal to be exemplars of sustainability. In our discussion, we highlight how the local farmers' social movement work constitutes loosely organized small groups connecting others to an amorphous idea of a sustainable society--one that sustains an environmental, economic, local, cultural, and physical way of life. Using <PERSON>'s concept of tiny publics, we identify design opportunities for supporting this less directed kind of social movement.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274434"}, {"primary_key": "3305690", "vector": [], "sparse_vector": [], "title": "Constructing Urban Tourism Space Digitally: A Study of Airbnb Listings in Two Berlin Neighborhoods.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Over the past decade, Airbnb has emerged as the most popular platform for renting out single rooms or whole apartments. The impact of Airbnb listings on local neighborhoods has been controversially discussed in many cities around the world. The platform's widespread adoption led to changes in urban life, and in particular urban tourism. We argue that urban tourism space can no longer be understood as a fixed, spatial entity. Instead, we follow a constructionist approach and argue that urban tourism space is (re-)produced digitally and collaboratively on online platforms such as Airbnb. We relate our work to a CSCW research direction that is concerned with the role of digital technologies in the production and appropriation of urban space and use the concept of representations as a theoretical lens for our empirical study. In that study, we qualitatively analyzed how the two Berlin neighborhoods Kreuzk\\\"olln and City West are digitally constructed by Airbnb hosts in their listing descriptions. Moreover, we quantitatively investigated to what extend mentioned places differ between Airbnb hosts and visitBerlin, the city's destination management organization (DMO). In our qualitative analysis, we found that hosts primarily focus on facilities and places in close proximity to their apartment. In the traditional urban tourism hotspot City West, hosts referred to many places also mentioned by the DMO. In the neighborhood of Kreuzk\\\"olln, in contrast, hosts reframed everyday places such as parks or an immigrant food market as the must sees in the area. We discuss how Airbnb hosts contribute to the discursive production of urban neighborhoods and thus co-produce them as tourist destinations. With the emergence of online platforms such as Airbnb, power relations in the construction of tourism space might shift from DMOs towards local residents who are now producing tourism space collaboratively.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274435"}, {"primary_key": "3305691", "vector": [], "sparse_vector": [], "title": "Welcome Letter.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Welcome to this issue of the Proceedings of the ACM on Human-Computer Interaction, which will focus on contributions from the research community Engineering Interactive Computing Systems (EICS). This diverse research community explores the methods, processes, techniques and tools that support specifying, designing, developing, deploying and verifying interactive systems. Building interactive systems is a multifaceted and challenging activity, involving a plethora of different actors and roles. This is particularly true in the domain of HCI, where we continuously push the edge of what is possible, where there is a crucial need for adequate processes, tools and methods to build reliable, useful and usable systems that help people cope with the ever-increasing complexity of work and life. The contents of this issue on EICS is the sum of four separate rounds of submissions, evenly spaced from July 2017 through May 2018. In total, the rounds attracted a total of 81 submissions from Asia, Canada, Australia, Europe, Africa, and the United States. Promising submissions in a round that were not accepted were invited to resubmit to a subsequent round, and 6 of the papers appearing in this issue were accepted after at least one round of resubmission. In each round, papers were subject to a rigorous reviewing process where they were reviewed by two EICS senior editors, as well as external reviewers. At the conclusion of each round, a Virtual Committee meeting was held to discuss all of the papers and arrive at final decisions. Ultimately, 14 papers were accepted over all rounds. This issue exists because of the dedicated volunteer effort of 20 senior editors who handled two to four papers each round, and 115 expert reviewers to ensure high quality and insightful reviews for all papers in all rounds. Reviewers and committee members were kept constant as much as possible for papers that were submitted to multiple rounds. Senior members of the editorial group also helped shepherd some papers, reflecting the deep commitment of this research community. We are excited by the detailed and insightful work that resulted in this PACMHCI EICS issue and look forward to equally high quality submissions in subsequent submission cycles over the coming year. For those interested in this area, this group holds their next annual conference June 19-22, 2018 in Paris, France. That conference will provide many opportunities to share ideas with other researchers and practitioners from institutions around the world.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3179426"}, {"primary_key": "3305692", "vector": [], "sparse_vector": [], "title": "&quot;It&apos;s <PERSON> of Boring Looking at Just the Face&quot;: How Teens Multitask During Mobile Videochat.", "authors": ["<PERSON><PERSON> (Mia) Suh", "<PERSON>", "<PERSON>"], "summary": "Mobile videochat use has been growing, especially for teens. To better understand teens' videochat practices, we conducted a two-week photo diary study with 16 teens. We found that most often, teens videochat with their closest friends from their bedrooms when they feel lonely or bored. Teens turned to videochat when understimulated but also felt understimulated during videochat. In order to manage this, they multitasked -teens moved from active chatting to co-presence while engaged in separate activities like scrolling social feeds or playing games. We uncovered social norms of reciprocity of attention, where teens match the attention level of the other and give leeway to briefly divert attention. Digital notifications did not feel disruptive to the videochat but family members' interruptions felt disruptive as teens' domestic context intruded into their virtual peer setting. We discuss these findings and their implications for research and design of videochat systems.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274436"}, {"primary_key": "3305693", "vector": [], "sparse_vector": [], "title": "Distinguishing Group Privacy From Personal Privacy: The Effect of Group Inference Technologies on Privacy Perceptions and Behaviors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Machine learning and data mining threaten personal privacy, and many tools exist to help users protect their privacy (e.g., available privacy settings on Facebook, anonymization and encryption of personal data, etc.). But such technologies also pose threats to \"group privacy,\" which is a concept scholars know relatively little about. Moreover, there are few tools to address the problem of protecting group privacy. This paper discusses an emerging class of software applications and services that pose new risks to group privacy by revealing group-level information based on individual information, such as social media postings or fitness app usage. The paper describes the results of two experiments that empirically establish the concept of group privacy and shows that it affects user perceptions of and interactions with information technology. The findings serve as a call to developers to design tools for group privacy protection.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274437"}, {"primary_key": "3305694", "vector": [], "sparse_vector": [], "title": "Making a Pecan Pie: Understanding and Supporting The Holistic Review Process in Admissions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Holistic reviews are a common practice employed by universities in the USA to make admissions decisions. It is an individualized review process where reviewers assess an applicant's potential by considering various criteria including academic metrics, adversities faced, and personal attributes. While the factors considered in such reviews are broadly known, a detailed walk-through of the process is absent in existing literature. This is important to understand what is done in practice and to identify opportunities for technological interventions to support the complex and changing process. We employed cognitive task analysis and a socio-organizational approach to understand the holistic review process at a highly-selective, private university. We found the process to be nuanced and complex owing its complexity both to the numerous variables involved and the reviewers' thought processes. We present a rigorous, structured characterization of the review process and suggest possible leverage points for applying visualization decision-support tools.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274438"}, {"primary_key": "3305695", "vector": [], "sparse_vector": [], "title": "Exploring Machine Autonomy and Provenance Data in Coffee Consumption: A Field Study of Bitbarista.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Technologies such as distributed ledgers and smart contracts are enabling the emergence of new autonomous systems, and providing enhanced systems to track the provenance of goods. A growing body of work in HCI is exploring the novel challenges of these systems, but there has been little attention paid to their impact on everyday activities. This paper presents a study carried out in 3 office environments for a 1-month period, which explored the impact of an autonomous coffee machine on the everyday activity of coffee consumption. The Bitbarista mediates coffee consumption through autonomous processes, presenting provenance data at the time of purchase while attempting to reduce intermediaries in the coffee trade. Through the report of interactions with and around the Bitbarista, we explore its implications for everyday life, and wider social structures and values. We conclude by offering recommendations for the design of community shared autonomous systems.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274439"}, {"primary_key": "3305696", "vector": [], "sparse_vector": [], "title": "Software Development and CSCW: Standardization and Flexibility in Large-Scale Agile Development.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Identifying which agile methods and processes are most effective depends on the goals and aims of an organisation. Agile development promotes an environment of continuous improvement and trust within self-organising teams. Therefore, it is important to allow teams to have the flexibility to customize and tailor their chosen methods. However, in a large-scale agile deployment, there needs to be a degree of process standardization across the organisation; otherwise, different teams will not be able to effectively share knowledge and best practices. This paper addresses the classic CSCW issue of the tensions that arise between process standardization and flexibility in a large-scale agile development through a case study at the British Broadcasting Corporation (BBC).", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274440"}, {"primary_key": "3305697", "vector": [], "sparse_vector": [], "title": "Efficient Information Sharing Techniques between Workers of Heterogeneous Tasks in 3D CVE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Collaboration between a helper and a worker in a 3D collaborative virtual environment usually requires real-time information sharing, since the worker relies on the timely assistance from the helper. In contrast, collaboration between workers requires them to shift their attention between independent tasks and dependent tasks. In worker-worker collaborations, a real-time updating technique could create excess information, which may be a distraction. In this paper, we compare different information sharing techniques and determine an efficient technique for the collaboration between workers. In our user experiment, participants performed a floor plan design task in a designer and engineer pairing on a desktop VR environment. The results showed that the proposed information sharing technique, in which objects are updated based on local users' actions, is more suitable than real-time updates. In addition, we discuss design implications that can be applied to different collaborative scenarios.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274441"}, {"primary_key": "3305698", "vector": [], "sparse_vector": [], "title": "Transforming Taxonomic Interfaces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "If scientific research can be described as \"arm's length'' cooperative work, then the sciences engaged in the creation and maintenance of cooperatively built classification systems might be said to require extremely long arms; workers must reach across great distances of time, space and awareness to independently contribute to the creation and maintenance of a conceptual infrastructure. This is particularly the case for the field of biological taxonomy, in which researchers across the globe have, for more than three hundred years, cooperatively developed a massive knowledge organization system describing all life on earth. As taxonomists move from paper-based workflows to born-digital, data-first modes of publishing, new tools and approaches are needed to support their work. In this paper, we present research conducted as part of the \"Transforming Taxonomic Interfaces\" project aimed at improving interfaces for taxonomic software. We describe biological taxonomy in the context of CSCW, and identify key strategies taxonomists deploy to facilitate loosely coupled cooperative work. We also contribute a discussion of semantic refactoring, a unique kind of \"articulation work'' entailed in developing, maintaining, and migrating classification systems. This work has implications for the design and study of knowledge organization systems and infrastructure, classification research, and for the development of general semantic web tools and software as well as those specifically for biological taxonomy.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274442"}, {"primary_key": "3305699", "vector": [], "sparse_vector": [], "title": "Augmented Reality Supported Modeling of Industrial Systems to Infer Software Configuration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes and evaluates an approach for building models of installed industrial Cyber-Physical Systems using augmented reality on smartphones. It proposes a visual language for annotating devices, containers, flows of liquids and networking connections in augmented reality. Compared to related work, it provides a more lightweight and flexible approach for building 3D models of industrial systems. The models are further used to automatically infer software configuration of controllable industrial products. This addresses a common problem of error-prone and time-consuming configuration of industrial systems in the current practice. The proposed approach is evaluated in a study with 16 domain experts. The study participants are involved in creating a model of an industrial system for water treatment. Their comments show that the approach can enable a less error-prone configuration for more complex systems. Opportunities for improvement in usability and reflections on the potential of the approach are discussed.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3229087"}, {"primary_key": "3305700", "vector": [], "sparse_vector": [], "title": "Learning from and with Menstrupedia: Towards Menstrual Health Education in India.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Menstruation has long remained a conversational taboo across India, resulting in inadequate dissemination of menstrual health education (MHE). Menstrupedia, a digital platform designed for an Indian audience, aims to bridge this information gap to impart MHE via its website and comic. We contribute a study of Menstrupedia---the information exchange on its website, the education it aims to provide, and the perceptions of its users. Using a combination of qualitative research methods, and engaging a feminist Human-Computer Interaction (HCI) lens, we critically analyze Menstrupedia's affordances and shortcomings. We also make recommendations for the design of technology-based dissemination of MHE, as well as additional sensitive and taboo topics.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274443"}, {"primary_key": "3305701", "vector": [], "sparse_vector": [], "title": "Facilitating Collaboration and Social Experiences with Videogames in Dementia: Results and Implications from a Participatory Design Study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Need for care and accordingly the prevalence of dementia is increasing rapidly. Information and communication technologies (ICT) may have the potential to support collaboration and cooperation between people with dementia and their caregivers and thus promote independency, wellbeing and quality of life of involved parties. In this context, the authors investigated the potential of an ICT-based videogame system to generate and facilitate social experiences of people with dementia and their caregivers. 26 people with dementia and their caregivers participated in a 16 month participatory design study. Results suggest that collaboration and cooperation among involved parties increased by using the system. Further, the study revealed positive and negative social experiences triggered by the videogame based system. This article will report on these results and provide implications that may support researchers to design similar systems with the potential to increase collaboration and cooperation among people with dementia and their caregivers.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274444"}, {"primary_key": "3305702", "vector": [], "sparse_vector": [], "title": "&apos;I Knew It Was Too Good to Be True&quot;: The Challenges Economically Disadvantaged Internet Users Face in Assessing Trustworthiness, Avoiding Scams, and Developing Self-Efficacy Online.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Mega Subramaniam", "<PERSON><PERSON>"], "summary": "In the U.S., consumers increasingly turn to the internet and mobile apps to complete essential personal transactions, ranging from financial payments to job applications. This shift to digital transactions can create challenges for those without reliable home internet connections or with limited digital literacy by requiring them to submit sensitive information on public computers or on unfamiliar websites. Using interviews with 52 families from high-poverty communities in the mid-Atlantic region of the U.S., we explore the compounding privacy and security challenges that economically disadvantaged individuals face when navigating online services. We describe the real, perceived, and unknown risks they face as they navigate online transactions with limited technical skills, as well as the strategies and heuristics they employ to minimize these risks. The findings highlight a complex relationship between participants' negative experiences and their general mistrust of sharing data through online channels. We also describe a range of strategies participants use to try and protect their personal information. Based on these findings, we offer design recommendations to inform the creation of educational resources that we will develop in the next phase of this project.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274445"}, {"primary_key": "3305703", "vector": [], "sparse_vector": [], "title": "CrowdNavi: Last-mile Outdoor Navigation for Pedestrians Using Mobile Crowdsensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Navigation services using digital maps make people's travel much easier. However, these services often fail to provide specific routes to those destinations that lack micro data in digital maps, such as a small laundry store in a shopping area. In this paper, we propose CrowdNavi, a last mile navigation service in outdoor environments using crowdsourcing based on the guider-follower model. First, we collect trajectories of guiders and images of reference objects along trajectories. To guide followers by reference objects along the route, we design a Semantic Crowd Navigation model to generate fine-grained maps by integrating guiders' data. Second, we design two score functions to fulfill two main requirements and plan hints. Last, we provide context-aware navigation for followers based on the fine-grained map and detect deviation in real-time. Real world experiments conducted in three different areas show that our proposed system in combination with images of reference objects is efficient.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274448"}, {"primary_key": "3305704", "vector": [], "sparse_vector": [], "title": "Exploring Trade-Offs Between Learning and Productivity in Crowdsourced History.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Crowdsourcing more complex and creative tasks is seen as a desirable goal for both employers and workers, but these tasks traditionally require domain expertise. Employers can recruit only expert workers, but this approach does not scale well. Alternatively, employers can decompose complex tasks into simpler micro-tasks, but some domains, such as historical analysis, cannot be easily modularized in this way. A third approach is to train workers to learn the domain expertise. This approach offers clear benefits to workers, but is perceived as costly or infeasible for employers. In this paper, we explore the trade-offs between learning and productivity in training crowd workers to analyze historical documents. We compare CrowdSCIM, a novel approach that teaches historical thinking skills to crowd workers, with two crowd learning techniques from prior work and a baseline. Our evaluation (n=360) shows that CrowdSCIM allows workers to learn domain expertise while producing work of equal or higher quality versus other conditions, but efficiency is slightly lower.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274447"}, {"primary_key": "3305706", "vector": [], "sparse_vector": [], "title": "Accessibility in Action: Co-Located Collaboration among Deaf and Hearing Professionals.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Although accessibility in academic and professional workplaces is a well-known issue, understanding how teams with different abilities communicate and coordinate in technology-rich workspaces is less well understood. When hearing people collaborate around computers, they rely on the ability to simultaneously see and hear as they start a shared document, talk to each other while editing, and gesture towards the screen. This interaction norm breaks down for teams of people with different sensory abilities, such as <PERSON><PERSON> and hearing collaborators, who rely on visual communication. Through interviews and observations, we analyze how Deaf-hearing teams collaborate on a variety of naturalistic tasks. Our findings reveal that Deaf-hearing teams create accessibility through their moment-to-moment co-located interaction and emerging team practices over time. We conclude with a discussion of how studying co-located Deaf-hearing interaction extends our understanding of accessibility in mixed-ability teams and provides new insights for groupware systems.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274449"}, {"primary_key": "3305707", "vector": [], "sparse_vector": [], "title": "Privacy Unraveling Around Explicit HIV Status Disclosure Fields in the Online Geosocial Hookup App Grindr.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "mHealth applications (\"apps\") must be searched for and downloaded prior to use, creating a potential barrier to uptake. Integrating health interventions into existing online social environments removes this barrier. However, little is known about the effects of linking sensitive health information to existing online identities. Our qualitative analysis of online comments (n=192) explores the user views of an HIV intervention integrated into the geosocial hookup app Grindr. We find some HIV positive users report keeping their status private to reduce their stigma exposure, whilst others report publicly disclosing their status to avoid being stigmatised by others. Where users keep their status private, we find concerns that social assumptions may develop around these non-disclosures, creating a privacy unraveling effect which restricts disclosure choice. Using <PERSON><PERSON><PERSON>'s four proposed limits to privacy unraveling, we develop a set of descriptive conceptual designs to explore the privacy respecting potential of these limits within this context and propose further research to address this privacy challenge.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274450"}, {"primary_key": "3305708", "vector": [], "sparse_vector": [], "title": "The Power of Bots: Characterizing and Understanding Bots in OSS Projects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Leveraging the pull request model of social coding platforms, Open Source Software (OSS) integrators review developers' contributions, checking aspects like license, code quality, and testability. Some projects use bots to automate predefined, sometimes repetitive tasks, thereby assisting integrators' and contributors' work. Our research investigates the usage and impact of such bots. We sampled 351 popular projects from GitHub and found that 93 (26%) use bots. We classified the bots, collected metrics from before and after bot adoption, and surveyed 228 developers and integrators. Our results indicate that bots perform numerous tasks. Although integrators reported that bots are useful for maintenance tasks, we did not find a consistent, statistically significant difference between before and after bot adoption across the analyzed projects in terms of number of comments, commits, changed files, and time to close pull requests. Our survey respondents deem the current bots as not smart enough and provided insights into the bots' relevance for specific tasks, challenges, and potential new features. We discuss some of the raised suggestions and challenges in light of the literature in order to help GitHub bot designers reuse and test ideas and technologies already investigated in other contexts.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274451"}, {"primary_key": "3305709", "vector": [], "sparse_vector": [], "title": "Participating Through Data: Charting Relational Tensions in Multiplatform Data Flows.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Engagements with social media today frequently involve the use of multiple platforms, which often exchange personal data about users. We explore individuals' experiences with the flow of personal data across platforms through interviews about dating apps, often requiring integration with a Facebook account. Inductive analysis revealed complex and, at times, conflicted ideas over what appropriate behaviors around personal data should look like in multiplatform engagements-central to these tensions were the multiple relationships that must be managed through participation in these ecosystems. For example, individuals talked of resignation in relation to specific platforms and the 'cost\" of using free services (people:platform), a lack of clarity and agency in defining what personal data might be shared between platforms (people:platform:platform), and, of course, the role of personal data in their efforts to establish and maintain relationships with other users in these 'social discovery\" ecosystems (people:people). We explore how these attitudes can be understood as morally charged, drawing attention to the ways in which personal data flows can enact expectations and obligations between various sociotechnical actors in personal data ecosystems and how these compound relationships reveal the complexity and texture of multiplatform participation.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274453"}, {"primary_key": "3305710", "vector": [], "sparse_vector": [], "title": "Investigating Separation of Territories and Activity Roles in Children&apos;s Collaboration around Tabletops.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Prior work has shown that children exhibit negative collaborative behaviors, such as blocking others' access to objects, when collaborating on interactive tabletop computers. We implemented previous design recommendations, namely separate physical territories and activity roles, which had been recommended to decrease these negative collaborative behaviors. We developed a multi-touch \"I-Spy\" picture searching application with separate territory partitions and activity roles. We conducted a deep qualitative analysis of how six pairs of children, ages 6 to 10, interacted with the application. Our analysis revealed that the collaboration styles differed for each pair, both in regards to the interaction with the task and with each other. Several pairs exhibited negative physical and verbal collaborative behaviors, such as nudging each other out of the way. Based on our analysis, we suggest that it is important for a collaborative task to offer equal opportunities for interaction, but it may not be necessary to strive for complete equity of collaboration. We examine the applicability of prior design guidelines and suggest open questions for future research to inform the design of tabletop applications to support collaboration for children.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274454"}, {"primary_key": "3305711", "vector": [], "sparse_vector": [], "title": "Soften the Pain, Increase the Gain: Enhancing Users&apos; Resilience to Negative Valence Feedback.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Content creators fear receiving unnecessarily harsh criticism when posting creative work in online platforms. We refer to feedback written in an unnecessary harsh tone as negative feedback. We conducted an online experiment to investigate the efficacy of three coping activities for mitigating the influence of negative feedback: self-affirmation, expressive writing, and distraction. Participants (N=480) received feedback sets with different balances of neutral and negative valence content and revised their essays after performing the assigned activity. We measured participants' affective states, extents of revision, and their perceptions of the feedback and its providers. Our results showed even a small amount of negativity had significant adverse effects on all the measures. For the coping activities, we found that expressive writing encouraged essay revision, distraction improved affective states and feedback provider perception, and self-affirmation had no significant effects on the measures. Our results contribute further empirical knowledge of how negative valence feedback impacts content creators and how the coping activities tested mitigate these effects. We also offer practical guidelines regarding when and how to use the activities tested in online feedback platforms.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274455"}, {"primary_key": "3305712", "vector": [], "sparse_vector": [], "title": "Mediating Color Filter Exploration with Color Theme Semantics Derived from Social Curation Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite the popularity of photo editors used to improve image attractiveness and expressiveness on social media, many users have trouble making sense of color filter effects and locating a preferred filter among a set of designer-crafted candidates. The problem gets worse when more computer-generated filters are introduced. To enhance filter findability, we semantically name and organize color effects leveraging data curated by creative communities online. We first model semantic mappings between color themes and keywords in everyday language. Next, we index and organize each filter by the derived semantic information. We conduct three separate studies to investigate the benefit of the semantic features on filter exploration. Our results indicate that color theme semantics constructed through social curation enhances filter findability, providing important implications into how to use the wisdom of the crowd to improve user experience with image editors.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274456"}, {"primary_key": "3305713", "vector": [], "sparse_vector": [], "title": "Understanding Motivations behind Inaccurate Check-ins.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Check-in data from social networks provide researchers a unique opportunity to model human dynamics at scale. However, it is unclear how indicative these check-in traces are of real human mobility. Prior work showed that significant amounts of Foursquare check-ins did not match with the physical mobility patterns of users, and suggested that misrepresented check-ins were incentivized by external rewards provided by the system. In this paper, our goal is to understand the root cause of inaccurate check-in data, by studying the validity of check-in traces in social media platforms without external rewards for check-ins. We conduct a data-driven analysis using an empirical check-in data trace of more than 276,000 users from WeChat Moments, with matching traces of their physical mobility. We develop a set of hypotheses on the underlying motivations behind people's inaccurate check-ins, and validate them using a detailed survey study. Our analysis reveals that there are surprisingly high amount of inaccurate check-ins even in the absence of rewards: 43% of total check-ins are inaccurate and 61% of survey participants report they have misrepresented their check-ins. We also find that inaccurate check-ins are often a result of user interface design as well as for convenience, self-advertisement and self-presentation.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274457"}, {"primary_key": "3305714", "vector": [], "sparse_vector": [], "title": "Shopping Over Distance through a Telepresence Robot.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Computer mediated-communication tools (CMC) support loved ones in maintaining connections with one another over distance, yet it can be difficult to do activities together. We studied the use of telepresence robots for supporting distance-separated loved ones in engaging in the joint activity of shopping over distance. One partner shopped in person while the other used either a telepresence robot or a tablet from a remote location. Compared to the tablet group, we found that when partners communicated through a telepresence robot, the remote partner's personality and presence were expressed through the movements and physicality of the medium. However, the use of the telepresence robot introduced tension between partners regarding responsibility, dependency, and contribution to the act of shopping. These results demonstrate the benefits of a mobile embodiment for remote partners, as well as the need for greater physical capabilities to support both physical connection and remote contribution to leisure activities.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274460"}, {"primary_key": "3305715", "vector": [], "sparse_vector": [], "title": "Our House: Living Long Distance with a Telepresence Robot.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Many couples live in long distance arrangements due to a variety of reasons from work opportunities to family obligations. However, current computer-mediated communication tools are not designed to support the entire range of communication behaviors that couples engage in during daily life. Our research explored how telepresence robots might fit into the array of tools that long distance couples use to communicate, given the factors of mobility and physicality that could support acts of interactivity and autonomy between partners. We found that telepresence robot communication facilitated interactions in five areas: participation in mundane everyday routines, feelings of sharing a home, connection with one's partner's family and friends, increased helpfulness, and the enjoyment of quiet companionship. However, telepresence robots also presented challenges related to privacy and asymmetry, as well as continued deficiencies in the level of interactivity. In response to these findings, we discuss the design implications for telepresence robots.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274459"}, {"primary_key": "3305716", "vector": [], "sparse_vector": [], "title": "Knowing That You Know What I Know Helps?: Understanding the Effects of Knowledge Transparency in Online Knowledge Transfer.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "To accomplish collaborative work, collaborators need to know one another's states of work for coordination. Similarly, in situations of knowledge transfer where experts need to instruct novices by passing implicit knowledge and personal experience about the task, one challenge lies in the knowledge gap between experts and novices. Both parties need to gain an awareness of each other's state of task knowledge for effective knowledge transfer. In this paper, we aim to investigate how online knowledge transfer is supported by a design called knowledge transparency of a shared document workspace, which allows explicitly annotating and sharing ones' knowledge state to make it visible to their collaborators. We conducted a laboratory study to examine how knowledge transparency affects knowledge transfer among experts and novices using an image editing task over an online document. The results shed light on how to support knowledge co-construction and communication for the purpose of knowledge transfer through interface design.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274458"}, {"primary_key": "3305717", "vector": [], "sparse_vector": [], "title": "Joint Media Engagement between Parents and Preschoolers in the U.S., China, and Taiwan.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Global app marketplaces make families in foreign countries easily accessible to developers, but most scholarship on joint media engagement (JME) between parents and children reports on data from participants in Western contexts. We conducted an observational lab study to examine how preschoolers (age 3-5) and parents (N=74) from three different regions of the world (communities in China, Taiwan, and the United States) engage with two types of tablet games: an instructional game with goals and an exploratory, open-ended game. We found systematic differences among groups and between games. For example, parents from China and Taiwan frequently picked up their child's hand and used it as a tool to engage with the screen, a practice parents in our U.S. sample did not employ. Dyads from all three samples exhibited more warmth when playing an instructional game than an exploratory one. Our results suggest that characteristics of the populations we sampled interact with design features, that is, the same design prompted opposing behaviors in different groups. We conclude that it may be useful to examine goal-free and goal-oriented JME as separate constructs, that design choices influence the roles parents adopt during JME, and that the range of behaviors we observed complicate the prevailing research narrative of what positive and productive JME looks like.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274461"}, {"primary_key": "3305718", "vector": [], "sparse_vector": [], "title": "Learning by Doing versus Learning by Viewing: An Empirical Study of Data Analyst Productivity on a Collaborative Platform at eBay.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We investigate how data-analyst productivity benefits from collaborative platforms that facilitate learning-by-doing (i.e. analysts learning by writing queries on their own) and learning-by-viewing (i.e. analysts learning by viewing queries written by peers). Learning is measured using a behavioral (productivity-improvement) approach. Productivity is measured using the time from creating an empty query to first executing it. Using a sample of 2,001 data analysts at eBay Inc. who have written 79,797 queries from 2014 to 2018, we find that: 1) learning-by-doing is associated with significant productivity improvement when the analyst's prior experience focuses on the focally queried database; 2) only learning-by-viewing queries that are authored by analysts with high output rate (average number of queries written per month) is associated with significant improvement in the viewer's productivity; 3) learning-by-viewing also depends on the \"social influence\" of the author of the viewed query, which we measure 'locally' based on the number of the author's direct viewers per month or 'globally' based on the how the author's queries propagate to peers in the overall collaboration network. Combining results 2 and 3, when segmenting analysts based on output rate and 'local' social influence, the viewing of queries authored by analysts with high output but low local influence is associated with the largest improvement in the viewer's productivity; whereas when segmenting based on output rate and 'global' social influence, the viewing of queries authored analysts with high output and high global influence is associated with the largest improvement in the viewer's productivity.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274462"}, {"primary_key": "3305719", "vector": [], "sparse_vector": [], "title": "From Situational Awareness to Actionability: Towards Improving the Utility of Social Media Data for Crisis Response.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "People are increasingly sharing information on social media during disaster events. This information could be valuable to emergency responders, but there remain challenges for using it to inform response efforts---including filtering relevant information from the large volumes of noise. Previous research has largely focused on identifying information that can contribute to a generalized concept of situational awareness. Our work explores the value of approaching this problem from a different perspective---one of actionablity---with the idea that information relevance may vary across responder role, domain, and other factors. This approach asks how we can get the right information to the right person at the right time? We interviewed and surveyed diverse responders to understand what \"actionable\" information is, allowing that actionability might differ from one responder to another. Through the findings, we (a) offer a nuanced understanding of actionability and differentiate it from situational awareness; (b) describe responders' perspective of what distinguishes good information when making rapid judgments; and (c) suggest opportunities for augmenting social media use to highlight information that needs immediate attention. We offer researchers an opportunity to frame different models of actionability to suit the requirements of a responding role.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274464"}, {"primary_key": "3305720", "vector": [], "sparse_vector": [], "title": "Making Sense of Group Chat through Collaborative Tagging and Summarization.", "authors": ["<PERSON>", "<PERSON>"], "summary": "While group chat is becoming increasingly popular for team collaboration, these systems generate long streams of unstructured back-and-forth discussion that are difficult to comprehend. In this work, we investigate ways to enrich the representation of chat conversations, using techniques such as tagging and summarization, to enable users to better make sense of chat. Through needfinding interviews with 15 active group chat users, who were shown mock-up alternative chat designs, we found the importance of structured representations, including signals such as discourse acts. We then developed Tilda, a prototype system that enables people to collaboratively enrich their chat conversation while conversing. From lab evaluations, we examined the ease of marking up chat using Tilda as well as the effectiveness of Tilda-enabled summaries for getting an overview. From a field deployment, we found that teams actively engaged with Tilda both for marking up their chat as well as catching up on chat.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274465"}, {"primary_key": "3305722", "vector": [], "sparse_vector": [], "title": "Coordination Mechanisms for Self-Organized Work in an Emergency Communication Center.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We describe an observational study of work coordination in an emergency communication center, where a collocated team of communication specialists engages in complex activities of communicating with pre-hospital medical teams, and coordinating patient care and transport. Unlike teams with clearly defined work roles and team structures that were introduced to increase work efficiency and minimize redundancy, the team we studied lacks the role differentiation. To better understand how complex work is accomplished under these conditions, we conducted in-situ observations in the center's control room and interviewed communication specialists. We found that communication specialists self-organized by using a mix of material and immaterial coordination mechanisms, including work schedules, computer systems, and tacit agreements to coordinate tasks. Using these findings, we then identified three features of self-organized, collocated and time-critical teamwork that require technology support: awareness of task ownership, task self-assignment, and informal team hierarchy. We conclude by discussing technology requirements to support these teamwork features.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274468"}, {"primary_key": "3305723", "vector": [], "sparse_vector": [], "title": "&quot;This is why we play&quot;: Characterizing Online Fan Communities of the NBA Teams.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Qin Lv"], "summary": "Professional sports constitute an important part of people's modern life. People spend substantial amounts of time and money supporting their favorite players and teams, and sometimes even riot after games. However, how team performance affects fan behavior remains understudied at a large scale. As almost every notable professional team has its own online fan community, these communities provide great opportunities for investigating this research question. In this work, we provide the first large-scale characterization of online fan communities of professional sports teams. Since user behavior in these online fan communities is inherently connected to game events and team performance, we construct a unique dataset that combines 1.5M posts and 43M comments in NBA-related communities on Reddit with statistics that document team performance in the NBA. We analyze the impact of team performance on fan behavior both at the game level and the season level. First, we study how team performance in a game relates to user activity during that game. We find that surprise plays an important role: the fans of the top teams are more active when their teams lose and so are the fans of the bottom teams in an unexpected win. Second, we study fan behavior over consecutive seasons and show that strong team performance is associated with fans of low loyalty, likely due to \"bandwagon fans.\" Fans of the bottom teams tend to discuss their team's future such as young talents in the roster, which may help them stay optimistic during adversity. Our results not only contribute to understanding the interplay between online sports communities and offline context but also provide significant insights into sports management.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274466"}, {"primary_key": "3305724", "vector": [], "sparse_vector": [], "title": "User Perceptions of Smart Home IoT Privacy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Smart home Internet of Things (IoT) devices are rapidly increasing in popularity, with more households including Internet-connected devices that continuously monitor user activities. In this study, we conduct eleven semi-structured interviews with smart home owners, investigating their reasons for purchasing IoT devices, perceptions of smart home privacy risks, and actions taken to protect their privacy from those external to the home who create, manage, track, or regulate IoT devices and/or their data. We note several recurring themes. First, users' desires for convenience and connectedness dictate their privacy-related behaviors for dealing with external entities, such as device manufacturers, Internet Service Providers, governments, and advertisers. Second, user opinions about external entities collecting smart home data depend on perceived benefit from these entities. Third, users trust IoT device manufacturers to protect their privacy but do not verify that these protections are in place. Fourth, users are unaware of privacy risks from inference algorithms operating on data from non-audio/visual devices. These findings motivate several recommendations for device designers, researchers, and industry standards to better match device privacy features to the expectations and preferences of smart home owners.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274469"}, {"primary_key": "3305725", "vector": [], "sparse_vector": [], "title": "Value-Sensitive Algorithm Design: Method, Case Study, and Lessons.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Most commonly used approaches to developing automated or artificially intelligent algorithmic systems are Big Data-driven and machine learning-based. However, these approaches can fail, for two notable reasons: (1) they may lack critical engagement with users and other stakeholders; (2) they rely largely on historical human judgments, which do not capture and incorporate human insights into how the world can be improved in the future. We propose and describe a novel method for the design of such algorithms, which we call Value Sensitive Algorithm Design. Value Sensitive Algorithm Design incorporates stakeholders' tacit knowledge and explicit feedback in the early stages of algorithm creation. This increases the chance to avoid biases in design choices or to compromise key stakeholder values. Generally, we believe that algorithms should be designed to balance multiple stakeholders' needs, motivations, and interests, and to help achieve important collective goals. We also describe a specific project \"Designing Intelligent Socialization Algorithms for WikiProjects in Wikipedia\" to illustrate our method. We intend this paper to contribute to the rich ongoing conversation concerning the use of algorithms in supporting critical decision-making in society.", "published": "2018-01-01", "category": "cscw", "pdf_url": "", "sub_summary": "", "source": "cscw", "doi": "10.1145/3274463"}]