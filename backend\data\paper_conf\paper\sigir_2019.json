[{"primary_key": "3114900", "vector": [], "sparse_vector": [], "title": "Training Streaming Factorization Machines with Alternating Least Squares.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Factorization Machines (FM) have been widely applied in industrial applications for recommendations. Traditionally FM models are trained in batch mode, which entails training the model with large datasets every few hours or days. Such training procedure cannot capture the trends evolving in real time with large volume of streaming data. In this paper, we propose an online training scheme for FM with the alternating least squares (ALS) technique, which has comparable performance with existing batch training algorithms. We incorporate an online update mechanism to the model parameters at the cost of storing a small cache. The mechanism also stabilizes the training error more than a traditional online training technique like stochastic gradient descent (SGD) as data points come in, which is crucial for real-time applications. Experiments on large scale datasets validate the efficiency and robustness of our method.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331374"}, {"primary_key": "3114901", "vector": [], "sparse_vector": [], "title": "Improving the Accuracy of System Performance Estimation by Using Shards.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We improve the measurement accuracy of retrieval system performance by better modeling the noise present in test collection scores. Our technique draws its inspiration from two approaches: one, which exploits the variable measurement accuracy of topics; the other, which randomly splits document collections into shards. We describe and theoretically analyze an ANOVA model able to capture the effects of topics, systems, and document shards as well as their interactions. Using multiple TREC collections, we empirically confirm theoretical results in terms of improved estimation accuracy and robustness of found significant differences. The improvements compared to widely used test collection measurement techniques are substantial. We speculate that our technique works because we do not assume that the topics of a test collection measure performance equally.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3338062"}, {"primary_key": "3114902", "vector": [], "sparse_vector": [], "title": "EXACT: Attributed Entity Extraction By Annotating Texts.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Chen", "<PERSON>", "<PERSON><PERSON>"], "summary": "Attributed entity is an entity defined by its structural attributes. Extracting attributed entities from textual documents is an important problem for a variety of big-data applications. We propose a system called EXACT for extracting attributed entities from textual documents by performing explorative annotation tasks, which create attributes and bind them to tag values. To support efficient annotation, we propose a novel tag recommendation technique based on a few-shot learning scheme which can suggest tags for new annotation tasks given very few human-annotated samples. We also propose a document recommendation scheme to provide run-time context for the user. Using a novel attribute index, the system can generate the task-relevant attributed entities on-the-fly. We demonstrate how these techniques can be integrated behind a novel user interface to enable productive and efficient extraction of attributed entities at limited cost in human annotation.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331391"}, {"primary_key": "3114903", "vector": [], "sparse_vector": [], "title": "Web Table Extraction, Retrieval and Augmentation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This tutorial synthesizes and presents research on web tables over the past two decades. We group the tasks into six main categories of information access tasks: (i) table extraction, (ii) table interpretation, (iii) table search, (iv) question answering on tables, (v) knowledge base augmentation, and (vi) table completion. For each category, we identify and introduce seminal approaches, present relevant resources, and point out interdependencies among the different tasks.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331385"}, {"primary_key": "3114904", "vector": [], "sparse_vector": [], "title": "Knowledge Tracing with Sequential Key-Value Memory Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Can machines trace human knowledge like humans? Knowledge tracing (KT) is a fundamental task in a wide range of applications in education, such as massive open online courses (MOOCs), intelligent tutoring systems, educational games, and learning management systems. It models dynamics in a student's knowledge states in relation to different learning concepts through their interactions with learning activities. Recently, several attempts have been made to use deep learning models for tackling the KT problem. Although these deep learning models have shown promising results, they have limitations: either lack the ability to go deeper to trace how specific concepts in a knowledge state are mastered by a student, or fail to capture long-term dependencies in an exercise sequence. In this paper, we address these limitations by proposing a novel deep learning model for knowledge tracing, namely Sequential Key-Value Memory Networks (SKVMN). This model unifies the strengths of recurrent modelling capacity and memory capacity of the existing deep learning KT models for modelling student learning. We have extensively evaluated our proposed model on five benchmark datasets. The experimental results show that (1) SKVMN outperforms the state-of-the-art KT models on all datasets, (2) SKVMN can better discover the correlation between latent concepts and questions, and (3) SKVMN can trace the knowledge state of students dynamics, and a leverage sequential dependencies in an exercise sequence for improved predication accuracy.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331195"}, {"primary_key": "3114907", "vector": [], "sparse_vector": [], "title": "A General Framework for Counterfactual Learning-to-Rank.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Implicit feedback (e.g., click, dwell time) is an attractive source of training data for Learning-to-Rank, but its naive use leads to learning results that are distorted by presentation bias. For the special case of optimizing average rank for linear ranking functions, however, the recently developed SVM-PropRank method has shown that counterfactual inference techniques can be used to provably overcome the distorting effect of presentation bias. Going beyond this special case, this paper provides a general and theoretically rigorous framework for counterfactual learning-to-rank that enables unbiased training for a broad class of additive ranking metrics (e.g., Discounted Cumulative Gain (DCG)) as well as a broad class of models (e.g., deep networks). Specifically, we derive a relaxation for propensity-weighted rank-based metrics which is subdifferentiable and thus suitable for gradient-based optimization. We demonstrate the effectiveness of this general approach by instantiating two new learning methods. One is a new type of unbiased SVM that optimizes DCG - called SVM PropDCG - and we show how the resulting optimization problem can be solved via the Convex Concave Procedure (CCP). The other is Deep PropDCG, where the ranking function can be an arbitrary deep network. In addition to the theoretical support, we empirically find that SVM PropDCG significantly outperforms existing linear rankers in terms of DCG. Moreover, the ability to train non-linear ranking functions via Deep PropDCG further improves performance.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331202"}, {"primary_key": "3114908", "vector": [], "sparse_vector": [], "title": "An Analysis of Query Reformulation Techniques for Precision Medicine.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Precision Medicine (PM) track at the Text REtrieval Conference (TREC) focuses on providing useful precision medicine-related information to clinicians treating cancer patients. The PM track gives the unique opportunity to evaluate medical IR systems using the same set of topics on two different collections: scientific literature and clinical trials. In the paper, we take advantage of this opportunity and we propose and evaluate state-of-the-art query expansion and reduction techniques to identify whether a particular approach can be helpful in both scientific literature and clinical trial retrieval. We present those approaches that are consistently effective in both TREC editions and we compare the results obtained with the best performing runs submitted to TREC PM 2017 and 2018.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331289"}, {"primary_key": "3114909", "vector": [], "sparse_vector": [], "title": "Context Attentive Document Ranking and Query Suggestion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a context-aware neural ranking model to exploit users' on-task search activities and enhance retrieval performance. In particular, a two-level hierarchical recurrent neural network is introduced to learn search context representation of individual queries, search tasks, and corresponding dependency structure by jointly optimizing two companion retrieval tasks: document ranking and query suggestion. To identify variable dependency structure between search context and users' ongoing search activities, attention at both levels of recurrent states are introduced. Extensive experiment comparisons against a rich set of baseline methods and an in-depth ablation analysis confirm the value of our proposed approach for modeling search context buried in search tasks.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331246"}, {"primary_key": "3114910", "vector": [], "sparse_vector": [], "title": "Contextual Dialogue Act Classification for Open-Domain Conversational Agents.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Classifying the general intent of the user utterance in a conversation, also known as Dialogue Act (DA), e.g., open-ended question, statement of opinion, or request for an opinion, is a key step in Natural Language Understanding (NLU) for conversational agents. While DA classification has been extensively studied in human-human conversations, it has not been sufficiently explored for the emerging open-domain automated conversational agents. Moreover, despite significant advances in utterance-level DA classification, full understanding of dialogue utterances requires conversational context. Another challenge is the lack of available labeled data for open-domain human-machine conversations. To address these problems, we propose a novel method, CDAC (Contextual Dialogue Act Classifier), a simple yet effective deep learning approach for contextual dialogue act classification. Specifically, we use transfer learning to adapt models trained on human-human conversations to predict dialogue acts in human-machine dialogues. To investigate the effectiveness of our method, we train our model on the well-known Switchboard human-human dialogue dataset, and fine-tune it for predicting dialogue acts in human-machine conversation data, collected as part of the Amazon Alexa Prize 2018 competition. The results show that the CDAC model outperforms an utterance-level state of the art baseline by 8.0% on the Switchboard dataset, and is comparable to the latest reported state-of-the-art contextual DA classification results. Furthermore, our results show that fine-tuning the CDAC model on a small sample of manually labeled human-machine conversations allows CDAC to more accurately predict dialogue acts in real users' conversations, suggesting a promising direction for future improvements.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331375"}, {"primary_key": "3114913", "vector": [], "sparse_vector": [], "title": "Third International Workshop on Recent Trends in News Information Retrieval (NewsIR&apos;19).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The journalism industry has undergone a revolution in the past decade, leading to new opportunities as well as challenges. News consumption, production and delivery have all been affected and transformed by technology Readers require new mechanisms to cope with the vast volume of information in order to be informed about news events. Reporters have begun to use natural language processing (NLP) and (IR) techniques for investigative work. Publishers and aggregators are seeking new business models, and new ways to reach and retain their audience. A shift in business models has led to a gradual shift in styles of journalism in attempts to increase page views; and, far more concerning, to real mis- and dis-information, alongside allegations of \"fake news\" threatening the journalistic freedom and integrity of legitimate news outlets. Social media platforms drive viewership, creating filter bubbles and an increasingly polarized readership. News documents have always been a part of research on information access and retrieval methods. Over the last few years, the IR community has increasingly recognized these challenges in journalism and opened a conversation about how we might begin to address them. Evidence of this recognition is the participation in the two previous editions of our NewsIR workshop, held in ECIR 2016 and 2018. One of the most important outcomes of those workshops is an increasing awareness in the community about the changing nature of journalism and the IR challenges it entails. To move yet another step forward, the goal of the third edition of our workshop will be to create a multidisciplinary venue that brings together news experts from both technology and journalism. This would take NewsIR from a European forum targeting mainly IR researchers, into a more inclusive and influential international forum. We hope that this new format will foster further understanding for both news professionals and IR researchers, as well as producing better outcomes for news consumers. We will address the possibilities and challenges that technology offers to the journalists, the challenges that new developments in journalism create for IR researchers, and the complexity of information access tasks for news readers.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331646"}, {"primary_key": "3114914", "vector": [], "sparse_vector": [], "title": "A Dataset of Systematic Review Updates.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Systematic reviews identify, summarise and synthesise evidence relevant to specific research questions. They are widely used in the field of medicine where they inform health care choices of both professionals and patients. It is important for systematic reviews to stay up to date as evidence changes but this is challenging in a field such as medicine where a large number of publications appear on a daily basis. Developing methods to support the updating of reviews is important to reduce the workload required and thereby ensure that reviews remain up to date. This paper describes a dataset of systematic review updates in the field of medicine created using 25 Cochrane reviews. Each review includes the Boolean query and relevance judgements for both the original and updated versions. The dataset can be used to evaluate approaches to study identification for review updates.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331358"}, {"primary_key": "3114915", "vector": [], "sparse_vector": [], "title": "Asking Clarifying Questions in Open-Domain Information-Seeking Conversations.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Users often fail to formulate their complex information needs in a single query. As a consequence, they may need to scan multiple result pages or reformulate their queries, which may be a frustrating experience. Alternatively, systems can improve user satisfaction by proactively asking questions of the users to clarify their information needs. Asking clarifying questions is especially important in conversational systems since they can only return a limited number of (often only one) result(s).", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331265"}, {"primary_key": "3114917", "vector": [], "sparse_vector": [], "title": "Social Knowledge Graph Explorer.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present SKG Explorer, an application for querying and browsing a social knowledge graph derived from Twitter that contains relationships between entities, links, and topics. A temporal dimension is also added for generating timelines for well-known events that allows the construction of stories in a wiki-like style. In this paper we describe the main components of the system and showcase some examples.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331410"}, {"primary_key": "3114919", "vector": [], "sparse_vector": [], "title": "Hate Speech Detection is Not as Easy as You May Think: A Closer Look at Model Validation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Hate speech is an important problem that is seriously affecting the dynamics and usefulness of online social communities. Large scale social platforms are currently investing important resources into automatically detecting and classifying hateful content, without much success. On the other hand, the results reported by state-of-the-art systems indicate that supervised approaches achieve almost perfect performance but only within specific datasets. In this work, we analyze this apparent contradiction between existing literature and actual applications. We study closely the experimental methodology used in prior work and their generalizability to other datasets. Our findings evidence methodological issues, as well as an important dataset bias. As a consequence, performance claims of the current state-of-the-art have become significantly overestimated. The problems that we have found are mostly related to data overfitting and sampling issues. We discuss the implications for current research and re-conduct experiments to give a more accurate picture of the current state-of-the art methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331262"}, {"primary_key": "3114920", "vector": [], "sparse_vector": [], "title": "Revisiting Online Personal Search Metrics with the User in Mind.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Traditional online quality metrics are based on search and browsing signals, such as position and time of the click. Such metrics typically model all users' behavior in exactly the same manner. Modeling individuals' behavior in Web search may be challenging as the user's historical behavior may not always be available (e.g., if the user is not signed into a given service). However, in personal search, individual users issue queries over their personal corpus (e.g. emails, files, etc.) while they are logged into the service. This brings an opportunity to calibrate online quality metrics with respect to an individual's search habits. With this goal in mind, the current paper focuses on a user-centric evaluation framework for personal search by taking into account variability of search and browsing behavior across individuals. The main idea is to calibrate each interaction of a user with respect to their historical behavior and search habits. To formalize this, a characterization of online metrics is proposed according to the relevance signal of interest and how the signal contributes to the computation of the gain in a metric. The proposed framework introduces a variant of online metrics called pMetrics (short for personalized metrics) that are based on the average search habits of users for the relevance signal of interest. Through extensive online experiments on a large population of GMail search users, we show that pMetrics are effective in terms of their sensitivity, robustness, and stability compared to their standard variants as well as baselines with different normalization factors.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331266"}, {"primary_key": "3114921", "vector": [], "sparse_vector": [], "title": "On Anonymous Commenting: A Greedy Approach to Balance Utilization and Anonymity for Instagram Users.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In many online services, anonymous commenting is not possible for the users; therefore, the users can not express their critical opinions without disregarding the consequences. As for now, naïve approaches are available for anonymous commenting which cause problems for analytical services on user comments. In this paper, we explore anonymous commenting approaches and their pros and cons. We also propose methods for anonymous commenting where it's possible to protect the user privacy while allowing sentimental analytics for service providers. Our experiments were conducted on a real dataset gathered from Instagram comments which indicate the effectiveness of our proposed methods in privacy protection and sentimental analytics. The proposed methods are independent of a particular website and can be utilized in various domains.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331364"}, {"primary_key": "3114922", "vector": [], "sparse_vector": [], "title": "Evaluating Variable-Length Multiple-Option Lists in Chatbots and Mobile Search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In recent years, the proliferation of smart mobile devices has lead to the gradual integration of search functionality within mobile platforms. This has created an incentive to move away from the \"ten blue links'' metaphor, as mobile users are less likely to click on them, expecting to get the answer directly from the snippets. In turn, this has revived the interest in Question Answering. Then, along came chatbots, conversational systems, and messaging platforms, where the user needs could be better served with the system asking follow-up questions in order to better understand the user's intent. While typically a user would expect a single response at any utterance, a system could also return multiple options for the user to select from, based on different system understandings of the user's intent. However, this possibility should not be overused, as this practice could confuse and/or annoy the user. How to produce good variable-length lists, given the conflicting objectives of staying short while maximizing the likelihood of having a correct answer included in the list, is an underexplored problem. It is also unclear how to evaluate a system that tries to do that. Here we aim to bridge this gap. In particular, we define some necessary and some optional properties that an evaluation measure fit for this purpose should have. We further show that existing evaluation measures from the IR tradition are not entirely suitable for this setup, and we propose novel evaluation measures that address it satisfactorily.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331308"}, {"primary_key": "3114923", "vector": [], "sparse_vector": [], "title": "Building Economic Models and Measures of Search.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Economics provides an intuitive and natural way to formally represent the costs and benefits of interacting with applications, interfaces and devices. By using economic models it is possible to reason about interaction, make predictions about how changes to the system will affect behavior, and measure the performance of people's interactions with the system. In this tutorial, we first provide an overview of relevant economic theories, before showing how they can be applied to formulate different ranking principles to provide the optimal ranking to users. This is followed by a session showing how economics can be used to model how people interact with search systems, and how to use these models to generate hypotheses about user behavior. The third session focuses on how economics has been used to underpin the measurement of information retrieval systems and applications using the CWL framework (which reports the expected utility, expected total utility, expected total cost, and so on) -- and how different models of user interaction lead to different metrics. We then show how information foraging theory can be used to measure the performance of an information retrieval system -- connecting the theory of how people search with how we measure it. The final session of the day will be spent building economic models and measures of search. Here sample problems will be provided to challenge participants, or participants can bring their own.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331379"}, {"primary_key": "3114924", "vector": [], "sparse_vector": [], "title": "cwl_eval: An Evaluation Tool for Information Retrieval.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a tool (\"cwl_eval\") which unifies many metrics typically used to evaluate information retrieval systems using test collections. In the CWL framework metrics are specified via a single function which can be used to derive a number of related measurements: Expected Utility per item, Expected Total Utility, Expected Cost per item, Expected Total Cost, and Expected Depth. The CWL framework brings together several independent approaches for measuring the quality of a ranked list, and provides a coherent user model-based framework for developing measures based on utility (gain) and cost. Here we outline the CWL measurement framework; describe the cwl_eval architecture; and provide examples of how to use it. We provide implementations of a number of recent metrics, including Time Biased Gain, U-Measure, Bejewelled Measure, and the Information Foraging Based Measure, as well as previous metrics such as Precision, Average Precision, Discounted Cumulative Gain, Rank-Biased Precision, and INST. By providing state-of-the-art and traditional metrics within the same framework, we promote a standardised approach to evaluating search effectiveness.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331398"}, {"primary_key": "3114925", "vector": [], "sparse_vector": [], "title": "CTRec: A Long-Short Demands Evolution Model for Continuous-Time Recommendation.", "authors": ["<PERSON>g <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In e-commerce, users' demands are not only conditioned by their profile and preferences, but also by their recent purchases that may generate new demands, as well as periodical demands that depend on purchases made some time ago. We call them respectively short-term demands and long-term demands. In this paper, we propose a novel self-attentive Continuous-Time Recommendation model (CTRec) for capturing the evolving demands of users over time. For modeling such time-sensitive demands, a Demand-aware Hawkes Process (DHP) framework is designed in CTRec to learn from the discrete purchase records of users. More specifically, a convolutional neural network is utilized to capture the short-term demands; and a self-attention mechanism is employed to capture the periodical purchase cycles of long-term demands. All types of demands are fused in DHP to make final continuous-time recommendations. We conduct extensive experiments on four real-world commercial datasets to demonstrate that CTRec is effective for general sequential recommendation problems, including next-item and next-session/basket recommendations. We observe in particular that CTRec is capable of learning the purchase cycles of products and estimating the purchase time of a product given a user.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331199"}, {"primary_key": "3114926", "vector": [], "sparse_vector": [], "title": "Transparent, Scrutable and Explainable User Models for Personalized Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Most recommender systems base their recommendations on implicit or explicit item-level feedback provided by users. These item ratings are combined into a complex user model, which then predicts the suitability of other items. While effective, such methods have limited scrutability and transparency. For instance, if a user's interests change, then many item ratings would usually need to be modified to significantly shift the user's recommendations. Similarly, explaining how the system characterizes the user is impossible, short of presenting the entire list of known item ratings. In this paper, we present a new set-based recommendation technique that permits the user model to be explicitly presented to users in natural language, empowering users to understand recommendations made and improve the recommendations dynamically. While performing comparably to traditional collaborative filtering techniques in a standard static setting, our approach allows users to efficiently improve recommendations. Further, it makes it easier for the model to be validated and adjusted, building user trust and understanding.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331211"}, {"primary_key": "3114927", "vector": [], "sparse_vector": [], "title": "Decoding The Style And <PERSON><PERSON> of Song Lyrics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The central idea of this paper is to gain a deeper understanding of song lyrics computationally. We focus on two aspects: style and biases of song lyrics. All prior works to understand these two aspects are limited to manual analysis of a small corpus of song lyrics. In contrast, we analyzed more than half a million songs spread over five decades. We characterize the lyrics style in terms of vocabulary, length, repetitiveness, speed, and readability. We have observed that the style of popular songs significantly differs from other songs. We have used distributed representation methods and WEAT test to measure various gender and racial biases in the song lyrics. We have observed that biases in song lyrics correlate with prior results on human subjects. This correlation indicates that song lyrics reflect the biases that exist in society. Increasing consumption of music and the effect of lyrics on human emotions makes this analysis important.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331363"}, {"primary_key": "3114929", "vector": [], "sparse_vector": [], "title": "Evaluating Risk-Sensitive Text Retrieval.", "authors": ["<PERSON><PERSON>"], "summary": "Search engines with a loyal user-base face the difficult task of improving overall effectiveness while maintaining the quality of existing work-flows. Risk-sensitive evaluation tools are designed to address that task, but, they currently do not support inference over multiple baselines. Our research objectives are to: 1) Survey and revisit risk evaluation, taking into account frequentist and Bayesian inference approaches for comparing against multiple baselines; 2) Apply that new approach, evaluating a novel web search technique that leverages previously run queries to improve the effectiveness of a new user query; and 3) Explore how risk-sensitive component interactions affect end-to-end effectiveness in a search pipeline.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331423"}, {"primary_key": "3114933", "vector": [], "sparse_vector": [], "title": "Network Embedding and Change Modeling in Dynamic Heterogeneous Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Network embedding learns the vector representations of nodes. Most real world networks are heterogeneous and evolve over time. There are, however, no network embedding approaches designed for dynamic heterogeneous networks so far. Addressing this research gap is beneficial for analyzing and mining real world networks. We develop a novel representation learning method, change2vec, which considers a dynamic heterogeneous network as snapshots of networks with different time stamps. Instead of processing the whole network at each time stamp, change2vec models changes between two consecutive static networks by capturing newly-added and deleted nodes with their neighbour nodes as well as newly-formed or deleted edges that caused core structural changes known as triad closure or open processes. Change2vec leverages metapath based node embedding and change modeling to preserve both heterogeneous and dynamic features of a network. Experimental results show that change2vec outperforms two state-of-the-art methods in terms of clustering performance and efficiency.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331273"}, {"primary_key": "3114935", "vector": [], "sparse_vector": [], "title": "ROME 2019: Workshop on Reducing Online Misinformation Exposure.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The spread of misinformation online is a challenge that may have an impact on society by misleading and undermining the trust of people in domains such as politics or public health. While fact-checking is one way to identify misinformation, it is a slow process and requires significant effort. Improving the efficiency of fact-checking by automating parts of the process or defining new processes to validate claims is a challenging task with a need for expertise from multiple disciplines. The aim of ROME 2019 is to bring together researchers from various fields such as Information Retrieval, Natural Language Processing, Semantic Web and Complex Networks to discuss these problems and define new directions in the area of automated fact-checking.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331645"}, {"primary_key": "3114936", "vector": [], "sparse_vector": [], "title": "Sparse Tensor Co-clustering as a Tool for Document Categorization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "To deal with document clustering, we usually rely on document-term matrices. However, from additional available information like keywords, co-authors, citations we might rather exploit a reorganization of the data in the form of a tensor. In this paper, we extend the use of the Sparse Poisson Latent Block Model to deal with sparse tensor data using jointly all information arising from documents. The proposed model is parsimonious and tailored for this kind of data. To estimate the parameters, we derive a suitable tensor co-clustering algorithm. Empirical results on several real-world text datasets highlight the advantages of our proposal which improves the clustering results of documents.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331360"}, {"primary_key": "3114939", "vector": [], "sparse_vector": [], "title": "Revisiting Approximate Metric Optimization in the Age of Deep Neural Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Learning-to-Rank is a branch of supervised machine learning that seeks to produce an ordering of a list of items such that the utility of the ranked list is maximized. Unlike most machine learning techniques, however, the objective cannot be directly optimized using gradient descent methods as it is either discontinuous or flat everywhere. As such, learning-to-rank methods often optimize a loss function that either is loosely related to or upper-bounds a ranking utility instead. A notable exception is the approximation framework originally proposed by <PERSON> et al. that facilitates a more direct approach to ranking metric optimization. We revisit that framework almost a decade later in light of recent advances in neural networks and demonstrate its superiority empirically. Through this study, we hope to show that the ideas from that work are more relevant than ever and can lay the foundation of learning-to-rank research in the age of deep neural networks.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331347"}, {"primary_key": "3114940", "vector": [], "sparse_vector": [], "title": "An Analysis of the Change in Discussions on Social Media with Bitcoin Price.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We develop a new approach to temporalizing word2vec-based topic modelling that determines which topics on social media vary with shifts in the phases of a time series to understand potential interactions. This is particularly relevant for the highly volatile bitcoin price with its distinct four phases across 2017-18. We statistically test which words change in frequency between the different stages and compare four word2vec models to assess their consistency in relating connected words in weighted, undirected graphs. For words that fall in frequency when prices shift from rising to falling, all eight topics are identified with the four approaches; for words rising in frequency, three out of the five topics remain constant. These topics are intuitive and match with actual events in the news.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331304"}, {"primary_key": "3114943", "vector": [], "sparse_vector": [], "title": "Similarity-Based Synthetic Document Representations for Meta-Feature Generation in Text Classification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>s", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose new solutions that enhance and extend the already very successful application of meta-features to text classification. Our newly proposed meta-features are capable of: (1) improving the correlation of small pieces of evidence shared by neighbors with labeled categories by means of synthetic document representations and (local and global) hyperplane distances; and (2) estimating the level of error introduced by these newly proposed and the existing meta-features in the literature, specially for hard-to-classify regions of the feature space. Our experiments with large and representative number of datasets show that our new solutions produce the best results in all tested scenarios, achieving gains of up to 12% over the strongest meta-feature proposal of the literature.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331239"}, {"primary_key": "3114944", "vector": [], "sparse_vector": [], "title": "Using Trails to Support Users with Tasks of Varying Scope.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A search trail is an interactive visualization of how a previous searcher approached a related task. Using search trails to assist users requires understanding aspects of the task, user, and trails. In this paper, we examine two questions. First, what are task characteristics that influence a user's ability to gain benefits from others' trails? Second, what is the impact of a \"mismatch\" between a current user's task and previous user's task which originated the trail? We report on a study that investigated the influence of two factors on participants' perceptions and behaviors while using search trails to complete tasks. Our first factor, task scope, focused on the scope of the task assigned to the participant (broad to narrow). Our manipulation of this factor involved varying the number of constraints associated with tasks. Our second factor, trail scope, focused on the scope of the task that originated the search trails given to participants. We investigated how task scope and trail scope affected participants' (RQ1) pre-task perceptions, (RQ2) post-task perceptions, and (RQ3) search behaviors. We discuss implications of our results for systems that use search trails to provide assistance.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331290"}, {"primary_key": "3114947", "vector": [], "sparse_vector": [], "title": "Enhanced News Retrieval: Passages Lead the Way!", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We observe that most relevant terms in unstructured news articles are primarily concentrated towards the beginning and the end of the document. Exploiting this observation, we propose a novel version of the classical BM25 weighting model, called BM25 Passage (BM25P), which scores query results by computing a linear combination of term statistics in the different portions of news articles. Our experimentation, conducted using three publicly available news datasets, demonstrates that BM25P markedly outperforms BM25 in term of effectiveness by up to 17.44% in [email protected] and 85% in [email protected]", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331373"}, {"primary_key": "3114948", "vector": [], "sparse_vector": [], "title": "Multiple Query Processing via Logic Function Factoring.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Some extensions to search systems require support for multiple query processing. This is the case with query variations, i.e., different query formulations of the same information need. The results of their processing can be fused together to improve effectiveness, but this requires to traverse more than once the query terms' posting lists, thus prolonging the multiple query processing time. In this work, we propose an approach to optimize the processing of query variations to reduce their overall response time. Similarly to the standard Boolean model, we firstly represent a group of query variations as a logic function where Boolean variables represent query terms. We then apply factoring to such function, in order to produce a more compact but logically equivalent representation. The factored form is used to process the query variations in a single pass over the inverted index. We experimentally show that our approach can improve by up to 1.95× the mean processing time of a multiple query with no statistically significant degradation in terms of [email protected]", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331297"}, {"primary_key": "3114949", "vector": [], "sparse_vector": [], "title": "Harvesting Drug Effectiveness from Social Media.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Drug effectiveness describes the capacity of a drug to cure a disease, which is of great importance for drug safety. To get this information, a number of real-world patient-oriented outcomes are required. However, current surveillance systems can only capture a small portion of them, and there is a time lag in processing the reported data. Since social media provides quantities of patient-oriented user posts in real-time, it is of great value to automatically extract drug effectiveness from these data. To this end, we build a dataset containing 25K tweets describing drug use, and further harvest drug effectiveness by performing Relation Extraction (RE) between chemicals and diseases. Most prior works about RE deal with mention pairs independently, which is not suitable for our task since interactions across mention pairs are widespread. In this paper, we propose a model regarding mention pairs as nodes connected by multiple types of edges. With the help of graph-based information transfers over time, it deals with all mention pairs simultaneously to capture their interactions. Besides, a novel idea is used to perform multiple instance learning, a big challenge in general RE tasks. Extensive experimental results show that our model outperforms previous work by a substantial margin.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331263"}, {"primary_key": "3114950", "vector": [], "sparse_vector": [], "title": "Neural Compatibility Ranking for Text-based Fashion Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "When shopping for fashion, customers often look for products which can complement their current outfit. For example, customers want to buy a jacket which can go well with their jeans and sneakers. To address the task of fashion matching, we propose a neural compatibility model for ranking fashion products based on the compatibility matching with the input outfit. The contribution of our work is twofold. First, we demonstrate that product descriptions contain rich information about product comparability which has not been fully utilized in the prior work. Secondly, we exploit such useful information from text data by taking advantages of semantic matching and lexical matching both of which are important for fashion matching. The proposed model is evaluated on a real-world fashion outfit dataset and achieves the state-of-the-art results by comparing to the competitive baselines. In the future work, we plan to extend the model by incorporating product images which are the major data source in the prior work on fashion matching.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331365"}, {"primary_key": "3114951", "vector": [], "sparse_vector": [], "title": "Developing Evaluation Metrics for Instant Search Using Mixed Methods Methods.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Instant search has become a popular search paradigm in which users are shown a new result page in response to every keystroke triggered. Over recent years, the paradigm has been widely adopted in several domains including personal email search, e-commerce, and music search. However, the topic of evaluation and metrics of such systems has been less explored in the literature thus far. In this work, we describe a mixed methods approach to understanding user expectations and evaluating an instant search system in the context of music search. Our methodology involves conducting a set of user interviews to gain a qualitative understanding of users' behaviors and their expectations. The hypotheses from user research are then extended and verified by a large-scale quantitative analysis of interaction logs. Using music search as a lens, we show that researchers and practitioners can interpret the behavior logs more effectively when accompanied by insights from qualitative research. Further, we also show that user research eliminates the guesswork involved in identifying users signals that estimate user satisfaction. Finally, we demonstrate that metrics identified using our approach are more sensitive than the commonly used click-through rate metric for instant search.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331293"}, {"primary_key": "3114952", "vector": [], "sparse_vector": [], "title": "Joint Workshop on Bibliometric-enhanced Information Retrieval and Natural Language Processing for Digital Libraries (BIRNDL 2019).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Drago<PERSON>", "<PERSON><PERSON><PERSON><PERSON>n"], "summary": "The deluge of scholarly publication poses a challenge for scholars find relevant research and policy makers to seek in-depth information and understand research impact. Information retrieval (IR), natural language processing (NLP) and bibliometrics could enhance scholarly search, retrieval and user experience, but their use in digital libraries is not widespread. To address this gap, we propose the 4th Joint Workshop on BIRNDL and the 5th CL-SciSumm Shared Task. We seek to foster collaboration among researchers in NLP, IR and Digital Libraries (DL), and to stimulate the development of new methods in NLP, IR, recommendation systems and scientometrics toward improved scholarly document understanding, analysis, and retrieval at scale.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331650"}, {"primary_key": "3114954", "vector": [], "sparse_vector": [], "title": "Personalized Fashion Recommendation with Visual Explanations based on Multimodal Attention Network: Towards Visually Explainable Recommendation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Hongteng Xu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Hongyuan Zha"], "summary": "Fashion recommendation has attracted increasing attention from both industry and academic communities. This paper proposes a novel neural architecture for fashion recommendation based on both image region-level features and user review information. Our basic intuition is that: for a fashion image, not all the regions are equally important for the users, i.e., people usually care about a few parts of the fashion image. To model such human sense, we learn an attention model over many pre-segmented image regions, based on which we can understand where a user is really interested in on the image, and correspondingly, represent the image in a more accurate manner. In addition, by discovering such fine-grained visual preference, we can visually explain a recommendation by highlighting some regions of its image. For better learning the attention model, we also introduce user review information as a weak supervision signal to collect more comprehensive user preference. In our final framework, the visual and textual features are seamlessly coupled by a multimodal attention network. Based on this architecture, we can not only provide accurate recommendation, but also can accompany each recommended item with novel visual explanations. We conduct extensive experiments to demonstrate the superiority of our proposed model in terms of Top-N recommendation, and also we build a collectively labeled dataset for evaluating our provided visual explanations in a quantitative manner.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331254"}, {"primary_key": "3114955", "vector": [], "sparse_vector": [], "title": "Reinforcement Learning for User Intent Prediction in Customer Service Bots.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A customer service bot is now a necessary component of an e-commerce platform. As a core module of the customer service bot, user intent prediction can help predict user questions before they ask. A typical solution is to find top candidate questions that a user will be interested in. Such solution ignores the inter-relationship between questions and often aims to maximize the immediate reward such as clicks, which may not be ideal in practice. Hence, we propose to view the problem as a sequential decision making process to better capture the long-term effects of each recommendation in the list. Intuitively, we formulate the problem as a Markov decision process and consider using reinforcement learning for the problem. With this approach, questions presented to users are both relevant and diverse. Experiments on offline real-world dataset and online system demonstrate the effectiveness of our proposed approach.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331370"}, {"primary_key": "3114956", "vector": [], "sparse_vector": [], "title": "Numeral Attachment with Auxiliary Tasks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we propose the task of numeral attachment to detect the attached target of a numeral. Compared with other kinds of named entities, numerals provide richer and more crucial information in some domains. Fine-grained understanding of the information embedded in numerals is a fundamental challenge. We develop NumAttach, a pilot dataset for the proposed task based on tweets. Two main challenges of this task include the informal writing style in tweets and the representation of numerals. To address these challenges, we present an embedding technique that considers word and numeral information simultaneously. Furthermore, we design a joint learning model with the capsule network to accomplish the proposed task. We also release NumAttach to the research community as a resource.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331361"}, {"primary_key": "3114957", "vector": [], "sparse_vector": [], "title": "Embedding Edge-attributed Relational Hierarchies.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Relational embedding methods encode objects and their relations as low-dimensional vectors. While achieving competitive performance on a variety of relational inference tasks, these methods fall short of preserving the hierarchies that are often formed in existing graph data, and ignore the rich edge attributes that describe the relation facts. In this paper, we propose a novel embedding method that simultaneously preserve the hierarchical property and the edge information in the edge-attributed relational hierarchies. The proposed method preserves the hierarchical relations by leveraging the non-linearity of hyperbolic vector translations, for which the edge attributes are exploited to capture the importance of each relation fact. Our experiment is conducted on the well-known Enron organizational chart, where the supervision relations between employees of the Enron company are accompanied with email-based attributes. We show that our method produces relational embeddings of higher quality than state-of-the-art methods, and outperforms a variety of strong baselines in reconstructing the organizational chart.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331278"}, {"primary_key": "3114958", "vector": [], "sparse_vector": [], "title": "Bayesian Personalized Feature Interaction Selection for Factorization Machines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Factorization Machines (FMs) are widely used for feature-based collaborative filtering tasks, as they are very effective at modeling feature interactions. Existing FM-based methods usually take all feature interactions into account, which is unreasonable because not all feature interactions are helpful: incorporating useless feature interactions will introduce noise and degrade the recommendation performance. Recently, methods that perform Feature Interaction Selection (FIS) have attracted attention because of their effectiveness at filtering out useless feature interactions. However, they assume that all users share the same feature interactions, which is not necessarily true, especially for collaborative filtering tasks. In this work, we address this issue and study Personalized Feature Interaction Selection (P-FIS) by proposing a Bayesian Personalized Feature Interaction Selection (BP-FIS) mechanism under the Bayesian Variable Selection (BVS) theory. Specifically, we first introduce interaction selection variables with hereditary spike and slab priors for P-FIS. Then, we form a Bayesian generative model and derive the Evidence Lower Bound (ELBO), which can be optimized by an efficient Stochastic Gradient Variational Bayes (SGVB) method to learn the parameters. Finally, because BP-FIS can be seamlessly integrated with different variants of FMs, we implement two FM variants under the proposed BP-FIS. We carry out experiments on three benchmark datasets. The empirical results demonstrate the effectiveness of BP-FIS for selecting personalized interactions and improving the recommendation performance.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331196"}, {"primary_key": "3114960", "vector": [], "sparse_vector": [], "title": "Answer-enhanced Path-aware Relation Detection over Knowledge Base.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Knowledge Based Question Answering (KBQA) is one of the most promising approaches to provide suitable answers for the queries posted by users. Relation detection that aims to take full advantage of the substantial knowledge contained in knowledge base (KB) becomes increasingly important. Significant progress has been made in performing relation detection over KB. However, recent deep neural networks that achieve the state of the art on KB-based relation detection task only consider the context information of question sentences rather than the relatedness between question and answer candidates, and exclusively extract the relation from KB triple rather than learn informative relational path. In this paper, we propose a Knowledge-driven Relation Detection network (KRD) to interactively learn answer-enhanced question representations and path-aware relation representations for relation detection. A Siamese LSTM is employed into a similarity matching process between the question representation and relation representation. Experimental results on the SimpleQuestions and WebQSP datasets demonstrate that KRD outperforms the state-of-the-art methods. In addition, a series of ablation test show the robust superiority of the proposed method.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331328"}, {"primary_key": "3114961", "vector": [], "sparse_vector": [], "title": "Information Cascades Modeling via Deep Multi-Task Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>ce Trajcevski", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Effectively modeling and predicting the information cascades is at the core of understanding the information diffusion, which is essential for many related downstream applications, such as fake news detection and viral marketing identification. Conventional methods for cascade prediction heavily depend on the hypothesis of diffusion models and hand-crafted features. Owing to the significant recent successes of deep learning in multiple domains, attempts have been made to predict cascades by developing neural networks based approaches. However, the existing models are not capable of capturing both the underlying structure of a cascade graph and the node sequence in the diffusion process which, in turn, results in unsatisfactory prediction performance. In this paper, we propose a deep multi-task learning framework with a novel design of shared-representation layer to aid in explicitly understanding and predicting the cascades. As it turns out, the learned latent representation from the shared-representation layer can encode the structure and the node sequence of the cascade very well. Our experiments conducted on real-world datasets demonstrate that our method can significantly improve the prediction accuracy and reduce the computational cost compared to state-of-the-art baselines.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331288"}, {"primary_key": "3114962", "vector": [], "sparse_vector": [], "title": "An Efficient Adaptive Transfer Neural Network for Social-aware Recommendation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Weizhi Ma", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many previous studies attempt to utilize information from other domains to achieve better performance of recommendation. Recently, social information has been shown effective in improving recommendation results with transfer learning frameworks, and the transfer part helps to learn users' preferences from both item domain and social domain. However, two vital issues have not been well-considered in existing methods: 1) Usually, a static transfer scheme is adopted to share a user's common preference between item and social domains, which is not robust in real life where the degrees of sharing and information richness are varied for different users. Hence a non-personalized transfer scheme may be insufficient and unsuccessful. 2) Most previous neural recommendation methods rely on negative sampling in training to increase computational efficiency, which makes them highly sensitive to sampling strategies and hence difficult to achieve optimal results in practical applications.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331192"}, {"primary_key": "3114963", "vector": [], "sparse_vector": [], "title": "One-Class Order Embedding for Dependency Relation Prediction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Philips Kokoh Prasetyo"], "summary": "Learning the dependency relations among entities and the hierarchy formed by these relations by mapping entities into some order embedding space can effectively enable several important applications, including knowledge base completion and prerequisite relations prediction. Nevertheless, it is very challenging to learn a good order embedding due to the existence of partial ordering and missing relations in the observed data. Moreover, most application scenarios do not provide non-trivial negative dependency relation instances. We therefore propose a framework that performs dependency relation prediction by exploring both rich semantic and hierarchical structure information in the data. In particular, we propose several negative sampling strategies based on graph-specific centrality properties, which supplement the positive dependency relations with appropriate negative samples to effectively learn order embeddings. This research not only addresses the needs of automatically recovering missing dependency relations, but also unravels dependencies among entities using several real-world datasets, such as course dependency hierarchy involving course prerequisite relations, job hierarchy in organizations, and paper citation hierarchy. Extensive experiments are conducted on both synthetic and real-world datasets to demonstrate the prediction accuracy as well as to gain insights using the learned order embedding.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331249"}, {"primary_key": "3114965", "vector": [], "sparse_vector": [], "title": "Explanatory and Actionable Debugging for Machine Learning: A TableQA Demonstration.", "authors": ["<PERSON><PERSON><PERSON>", "Gyeongbok Lee", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Question answering from tables (TableQA) extracting answers from tables from the question given in natural language, has been actively studied. Existing models have been trained and evaluated mostly with respect to answer accuracy using public benchmark datasets such as WikiSQL. The goal of this demonstration is to show a debugging tool for such models, explaining answers to humans, known as explanatory debugging. Our key distinction is making it \"actionable\" to allow users to directly correct models upon explanation. Specifically, our tool surfaces annotation and models errors for users to correct, and provides actionable insights.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331404"}, {"primary_key": "3114967", "vector": [], "sparse_vector": [], "title": "The SIGIR 2019 Open-Source IR Replicability Challenge (OSIRRC 2019).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The importance of repeatability, replicability, and reproducibility is broadly recognized in the computational sciences, both in supporting desirable scientific methodology as well as sustaining empirical progress. This workshop tackles the replicability challenge for ad hoc document retrieval, via a common Docker interface specification to support images that capture systems performing ad hoc retrieval experiments on standard test collections.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331647"}, {"primary_key": "3114969", "vector": [], "sparse_vector": [], "title": "Solr Integration in the Anserini Information Retrieval Toolkit.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Anserini is an open-source information retrieval toolkit built around Lucene to facilitate replicable research. In this demonstration, we examine different architectures for Solr integration in order to address two current limitations of the system: the lack of an interactive search interface and support for distributed retrieval. Two architectures are explored: In the first approach, Anserini is used as a frontend to index directly into a running Solr instance. In the second approach, Lucene indexes built directly with Anserini can be copied into a Solr installation and placed under its management. We discuss the tradeoffs associated with each architecture and report the results of a performance evaluation comparing indexing throughput. To illustrate the additional capabilities enabled by Anserini/Solr integration, we present a search interface built using the open-source Blacklight discovery interface.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331401"}, {"primary_key": "3114970", "vector": [], "sparse_vector": [], "title": "Information Retrieval Meets Scalable Text Analytics: Solr Integration with Spark.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Zeynep <PERSON>", "<PERSON>"], "summary": "Despite the broad adoption of both Apache Spark and Apache Solr, there is little integration between these two platforms to support scalable, end-to-end text analytics. We believe this is a missed opportunity, as there is substantial synergy in building analytical pipelines where the results of potentially complex faceted queries feed downstream text processing components. This demonstration explores exactly such an integration: we evaluate performance under different analytical scenarios and present three simple case studies that illustrate the range of possible analyses enabled by seamlessly connecting Spark to Solr.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331395"}, {"primary_key": "3114972", "vector": [], "sparse_vector": [], "title": "Unbiased Low-Variance Estimators for Precision and Related Information Retrieval Effectiveness Measures.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This work describes an estimator from which unbiased measurements of precision, rank-biased precision, and cumulative gain may be derived from a uniform or non-uniform sample of relevance assessments. Adversarial testing supports the theory that our estimator yields unbiased low-variance measurements from sparse samples, even when used to measure results that are qualitatively different from those returned by known information retrieval methods. Our results suggest that test collections using sampling to select documents for relevance assessment yield more accurate measurements than test collections using pooling, especially for the results of retrieval methods not contributing to the pool.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331355"}, {"primary_key": "3114973", "vector": [], "sparse_vector": [], "title": "Quantifying <PERSON><PERSON> and <PERSON><PERSON><PERSON> of System Rankings.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "When used to assess the accuracy of system rankings, <PERSON>'s tau and other rank correlation measures conflate bias and variance as sources of error. We derive from tau a distance between rankings in Euclidean space, from which we can determine the magnitude of bias, variance, and error. Using bootstrap estimation, we show that shallow pooling has substantially higher bias and insubstantially lower variance than probability-proportional-to-size sampling, coupled with the recently released dynAP estimator.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331356"}, {"primary_key": "3114974", "vector": [], "sparse_vector": [], "title": "Dynamic Sampling Meets Pooling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A team of six assessors used Dynamic Sampling (<PERSON><PERSON><PERSON> and <PERSON> 2018) and one hour of assessment effort per topic to form, without pooling, a test collection for the TREC 2018 Common Core Track. Later, official relevance assessments were rendered by NIST for documents selected by depth-10 pooling augmented by move-to-front (MTF) pooling (<PERSON><PERSON><PERSON> et al. 1998), as well as the documents selected by our Dynamic Sampling effort. MAP estimates rendered from dynamically sampled assessments using the xinfAP statistical evaluator are comparable to those rendered from the complete set of official assessments using the standard trec_eval tool. MAP estimates rendered using only documents selected by pooling, on the other hand, differ substantially. The results suggest that the use of Dynamic Sampling without pooling can, for an order of magnitude less assessment effort, yield information-retrieval effectiveness estimates that exhibit lower bias, lower error, and comparable ability to rank system effectiveness.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331354"}, {"primary_key": "3114975", "vector": [], "sparse_vector": [], "title": "Towards Better Support for Exploratory Search through an Investigation of Notes-to-self and Notes-to-share.", "authors": ["<PERSON>", "Yuan Li", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recently, there has been interest in integrating information retrieval systems more closely with users' knowledge development processes, especially to support exploratory search. In this work, we investigated how people organize and structure information they discover during exploratory searches. In a lab study, we asked 24 participants to take hand-written notes they could use in the future while they were completing an exploratory search. We then asked participants to organize their findings to share with someone else who wants to explore the topic. Finally, we conducted post-session interviews to gain insights into the types of information saved and how participants organized the information they found. In our qualitative analysis of the notes and interviews, we found that the notes included background information about the topic, key concepts, specific details, useful information sources, and information to help with the broader work task. Notes were primarily structured in lists, and reflected a combination of linear note-taking strategies and grouping by information source or topical themes. Participants changed the content and structure of the shared notes to make them easier to understand and to provide a more thorough overview of the topic. Our findings have implications for the design of search tools to help current searchers organize, structure, and synthesize information and to help future searchers engaged in similar information seeking tasks.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331309"}, {"primary_key": "3114976", "vector": [], "sparse_vector": [], "title": "The Importance of Interaction for Information Retrieval.", "authors": ["<PERSON><PERSON>"], "summary": "There has historically been a divide between the user-oriented and system-oriented research communities in information retrieval. In my opinion, this divide is based primarily on a difference in viewpoint about the relative importance of understanding how people search for information compared to developing new retrieval models and ranking algorithms. There is strong agreement, however, that the interaction between the user and the search engine is a fundamental part of the IR process. The IR field was one of the first in computer science to recognize the importance of the user-system interaction, which led to a number of core concepts such as relevance, ranking, result presentation, feedback, evaluation, and browsing. The key message of this talk is that effective information access requires interaction between the user and the system, where both play a role. Additionally, there is growing evidence that even more effective information access can be achieved by a system that actively supports interaction, particularly in the limited-bandwidth environments provided by mobile devices and voice-based assistants. In this talk, I will first give an overview of past IR research on user-system interaction. In much of this research, the system provides passive support for the retrieval process and much of the burden for effective retrieval stays with the user. There has been some research, however, that has attempted to actively support the interaction by designing expert intermediary systems. After this review, I will focus on two current areas of research where active support for interaction is crucial. These are question answering and conversational search. These areas have recently become popular in the NLP community but they have deep roots in IR. I will describe the specific lines of research we have followed at the Center for Intelligent Information Retrieval and RMIT, including interactive answer passage retrieval, studies of information-seeking dialogues, and neural models for selecting responses and answers. Although there are many aspects to this research, I will highlight the parts where interaction is important, how we have attempted to evaluate the research, and where significant progress needs to be made.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331185"}, {"primary_key": "3114977", "vector": [], "sparse_vector": [], "title": "User Attention-guided Multimodal Dialog Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As an intelligent way to interact with computers, the dialog system has been catching more and more attention. However, most research efforts only focus on text-based dialog systems, completely ignoring the rich semantics conveyed by the visual cues. Indeed, the desire for multimodal task-oriented dialog systems is growing with the rapid expansion of many domains, such as the online retailing and travel. Besides, few work considers the hierarchical product taxonomy and the users' attention to products explicitly. The fact is that users tend to express their attention to the semantic attributes of products such as color and style as the dialog goes on. Towards this end, in this work, we present a hierarchical User attention-guided Multimodal Dialog system, named UMD for short. UMD leverages a bidirectional Recurrent Neural Network to model the ongoing dialog between users and chatbots at a high level; As to the low level, the multimodal encoder and decoder are capable of encoding multimodal utterances and generating multimodal responses, respectively. The multimodal encoder learns the visual presentation of images with the help of a taxonomy-attribute combined tree, and then the visual features interact with textual features through an attention mechanism; whereas the multimodal decoder selects the required visual images and generates textual responses according to the dialog history. To evaluate our proposed model, we conduct extensive experiments on a public multimodal dialog dataset in the retailing domain. Experimental results demonstrate that our model outperforms the existing state-of-the-art methods by integrating the multimodal utterances and encoding the visual features based on the users' attribute-level attention.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331226"}, {"primary_key": "3114978", "vector": [], "sparse_vector": [], "title": "Deeper Text Understanding for IR with Contextual Neural Language Modeling.", "authors": ["Zhuyun Dai", "<PERSON>"], "summary": "Neural networks provide new possibilities to automatically learn complex language patterns and query-document relations. Neural IR models have achieved promising results in learning query-document relevance patterns, but few explorations have been done on understanding the text content of a query or a document. This paper studies leveraging a recently-proposed contextual neural language model, BERT, to provide deeper text understanding for IR. Experimental results demonstrate that the contextual text representations from BERT are more effective than traditional word embeddings. Compared to bag-of-words retrieval models, the contextual language model can better leverage language structures, bringing large improvements on queries written in natural languages. Combining the text understanding ability with search knowledge leads to an enhanced pre-trained BERT model that can benefit related search tasks where training data are limited.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331303"}, {"primary_key": "3114979", "vector": [], "sparse_vector": [], "title": "ECOM&apos;19: The SIGIR 2019 Workshop on eCommerce.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "eCommerce Information Retrieval is receiving increasing attention in the academic literature, and is an essential component of some of the largest web sites (such as eBay, Amazon, Airbnb, Alibaba, Taobao, Target, Facebook, Home Depot, and others). These kinds of organisations clearly value the importance of research into Information Retrieval. The purpose of this workshop is to bring together researchers and practitioners of eCommerce IR to discuss topics unique to it, to set a research agenda, to examine how to build data sets, and how evaluate algorithms for research into this fascinating topic. eCommerce IR is ripe for research and has a unique set of problems. For example, in eCommerce search there may be no hypertext links between documents (products); there is a click stream, but more importantly, there is often a buy stream. eCommerce problems are wide in scope and range from user interaction modalities through to dynamic updates of a rapidly changing collection on auction sites, and the experienceness of some products (such as Airbnb bookings). This workshop is a follow up to very successful workshops held at SIGIR 2017 and SIGIR 2018. This year we will be running a data challenge (sponsored by eBay) which will allow us to follow up on multiple aspects that were discussed in the previous workshops (in particular, deterministic rank orders and how to evaluate these).", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331643"}, {"primary_key": "3114980", "vector": [], "sparse_vector": [], "title": "Biomedical Heterogeneous Data Integration and Rank Retrieval using Data Bridges.", "authors": ["<PERSON><PERSON>"], "summary": "Digitized world demands data integration systems that combine data repositories from multiple data sources. Vast amounts of clinical and biomedical research data are considered a primary force enabling data-driven research toward advancing health research and for introducing efficiencies in healthcare delivery. Data-driven research may have many goals, including but not limited to improved diagnostics processes, novel biomedical discoveries, epidemiology, and education. However, finding and gaining access to relevant data remains an elusive goal. We identified these challenges and developed an Integrated Radiology Image Search (IRIS) framework that could be a step toward aiding data-driven research. We propose building data bridges to support retrieving ranked relevant documents from integrated repository. My research focuses on biomedical data integration and indexing systems and provide ranked document retrieval from an integrated repository. Though we currently focus on integrating biomedical data sources (for medical professionals), we believe that our proposed framework and methodologies can be used in other domains as well.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331417"}, {"primary_key": "3114982", "vector": [], "sparse_vector": [], "title": "ENT Rank: Retrieving Entities for Topical Information Needs through Entity-Neighbor-Text Relations.", "authors": ["<PERSON>"], "summary": "Related work has demonstrated the helpfulness of utilizing information about entities in text retrieval; here we explore the converse: Utilizing information about text in entity retrieval. We model the relevance of Entity-Neighbor-Text (ENT) relations to derive a learning-to-rank-entities model.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331257"}, {"primary_key": "3114985", "vector": [], "sparse_vector": [], "title": "A Scalable Virtual Document-Based Keyword Search System for RDF Datasets.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "RDF datasets are becoming increasingly useful with the development of knowledge-based web applications. SPARQL is the official structured query language to search and access RDF datasets. Despite its effectiveness, the language is often difficult to use for non-experts because of its syntax and the necessity to know the underlying data structure of the database queries. In this regard, keyword search enables non-expert users to access the data contained in RDF datasets intuitively. This work describes the TSA+VDP keyword search system for effective and efficient keyword search over large RDF datasets. The system is compared with other state-of-the-art methods on different datasets, both real-world and synthetic, using a new evaluation framework that is easily reproducible and sharable.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331284"}, {"primary_key": "3114986", "vector": [], "sparse_vector": [], "title": "Effective Online Evaluation for Web Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present you a program of a balanced mix between an overview of academic achievements in the field of online evaluation and a portion of unique industrial practical experience shared by both the leading researchers and engineers from global Internet companies. First, we give basic knowledge from mathematical statistics. This is followed by foundations of main evaluation methods such as A/B testing, interleaving, and observational studies. Then, we share rich industrial experiences on constructing of an experimentation pipeline and evaluation metrics (emphasizing best practices and common pitfalls). A large part of our tutorial is devoted to modern and state-of-the-art techniques (including the ones based on machine learning) that allow to conduct online experimentation efficiently. We invite software engineers, designers, analysts, and managers of web services and software products, as well as beginners, advanced specialists, and researchers to learn how to make web service development effectively data-driven.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331378"}, {"primary_key": "3114987", "vector": [], "sparse_vector": [], "title": "A Systematic Comparison of Methods for Finding Good Premises for Claims.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Research on computational argumentation has recently become very popular. An argument consists of a claim that is supported or attacked by at least one premise. Its intention is the persuasion of others. An important problem in this field is retrieving good premises for a designated claim from a corpus of arguments. Given a claim, oftentimes existing approaches' first step is finding textually similar claims. In this paper we compare 196 methods systematically for determining similar claims by textual similarity, using a large corpus of (claim, premise) pairs crawled from debate portals. We also evaluate how well textual similarity of claims can predict relevance of the associated premises.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331282"}, {"primary_key": "3114988", "vector": [], "sparse_vector": [], "title": "Fairness and Discrimination in Retrieval and Recommendation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fairness and related concerns have become of increasing importance in a variety of AI and machine learning contexts. They are also highly relevant to information retrieval and related problems such as recommendation, as evidenced by the growing literature in SIGIR, FAT*, RecSys, and special sessions such as the FATREC workshop and the Fairness track at TREC 2019; however, translating algorithmic fairness constructs from classification, scoring, and even many ranking settings into information retrieval and recommendation scenarios is not a straightforward task. This tutorial will help to orient IR researchers to algorithmic fairness, understand how concepts do and do not translate from other settings, and provide an introduction to the growing literature on this topic.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331380"}, {"primary_key": "3114989", "vector": [], "sparse_vector": [], "title": "M-HIN: Complex Embeddings for Heterogeneous Information Networks via Metagraphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xiao", "<PERSON><PERSON><PERSON>"], "summary": "To represent a complex network, paths are often employed for capturing relationships among node: random walks for (homogeneous) networks and metapaths for heterogeneous information networks (HINs). However, there is structural (and possibly semantic) information loss when using paths to represent the subgraph between two nodes, since a path is a linear structure and a subgraph often is not. Can we find a better alternative for network embeddings? We offer a novel mechanism to capture the features of HIN nodes via metagraphs, which retains more structural and semantic information than path-oriented models. Inspired by developments in knowledge graph embedding, we propose to construct HIN triplets using nodes and metagraphs between them. Metagraphs are generated by harnessing the GRAMI algorithm, which enumerates frequent subgraph patterns in a HIN. Subsequently, the Had<PERSON>rd function is applied to encode relationships between nodes and metagraphs, and the probability whether a HIN triplet can be evaluated. Further, to better distinguish between symmetric and asymmetric cases of metagraphs, we introduce a complex embedding scheme that is able to precisely express fine-grained features of HIN nodes. We evaluate the proposed model, M-HIN, on real-life datasets and demonstrate that it significantly and consistently outperforms state-of-the-art models.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331281"}, {"primary_key": "3114990", "vector": [], "sparse_vector": [], "title": "Intervention Harvesting for Context-Dependent Examination-Bias Estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Accurate estimates of examination bias are crucial for unbiased learning-to-rank from implicit feedback in search engines and recommender systems, since they enable the use of Inverse Propensity Score (IPS) weighting techniques to address selection biases and missing data. Unfortunately, existing examination-bias estimators are limited to the Position-Based Model (PBM), where the examination bias may only depend on the rank of the document. To overcome this limitation, we propose a Contextual Position-Based Model (CPBM) where the examination bias may also depend on a context vector describing the query and the user. Furthermore, we propose an effective estimator for the CPBM based on intervention harvesting. A key feature of the estimator is that it does not require disruptive interventions but merely exploits natural variation resulting from the use of multiple historic ranking functions. Real-world experiments on the ArXiv search engine and semi-synthetic experiments on the Yahoo Learning-To-Rank dataset demonstrate the superior effectiveness and robustness of the new approach.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331238"}, {"primary_key": "3114991", "vector": [], "sparse_vector": [], "title": "Retrieving Multi-Entity Associations: An Evaluation of Combination Modes for Word Embeddings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Word embeddings have gained significant attention as learnable representations of semantic relations between words, and have been shown to improve upon the results of traditional word representations. However, little effort has been devoted to using embeddings for the retrieval of entity associations beyond pairwise relations. In this paper, we use popular embedding methods to train vector representations of an entity-annotated news corpus, and evaluate their performance for the task of predicting entity participation in news events versus a traditional word cooccurrence network as a baseline. To support queries for events with multiple participating entities, we test a number of combination modes for the embedding vectors. While we find that even the best combination modes for word embeddings do not quite reach the performance of the full cooccurrence network, especially for rare entities, we observe that different embedding methods model different types of relations, thereby indicating the potential for ensemble methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331366"}, {"primary_key": "3114993", "vector": [], "sparse_vector": [], "title": "A study on the Interpretability of Neural Retrieval Models using DeepSHAP.", "authors": ["Z<PERSON> Trevor <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A recent trend in IR has been the usage of neural networks to learn retrieval models for text based adhoc search. While various approaches and architectures have yielded significantly better performance than traditional retrieval models such as BM25, it is still difficult to understand exactly why a document is relevant to a query. In the ML community several approaches for explaining decisions made by deep neural networks have been proposed -- including DeepSHAP which modifies the DeepLift algorithm to estimate the relative importance (shapley values) of input features for a given decision by comparing the activations in the network for a given image against the activations caused by a reference input. In image classification, the reference input tends to be a plain black image. While DeepSHAP has been well studied for image classification tasks, it remains to be seen how we can adapt it to explain the output of Neural Retrieval Models (NRMs). In particular, what is a good \"black\" image in the context of IR? In this paper we explored various reference input document construction techniques. Additionally, we compared the explanations generated by DeepSHAP to LIME (a model agnostic approach) and found that the explanations differ considerably. Our study raises concerns regarding the robustness and accuracy of explanations produced for NRMs. With this paper we aim to shed light on interesting problems surrounding interpretability in NRMs and highlight areas of future work.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331312"}, {"primary_key": "3114994", "vector": [], "sparse_vector": [], "title": "Find Relevant Cases in All Cases: Your Journey at Doctrine.", "authors": ["<PERSON>"], "summary": "Domain-specific Information Retrieval (IR) is generally challenging because of the rare datasets or benchmarks, niche vocabularies and more limited literature coverage. Legal IR is no exception and presents other obstacles, reinforcing the need for innovation and, sometimes, paradigm shifts. Doctrine, one of the largest Legaltech companies in Europe, dedicates an entire data science team to advance on these problems and identify new opportunities. In this presentation, we provide some intuition regarding the specificities of legal IR (e.g., what is relevance?), and we introduce some of the solutions currently used on doctrine.fr.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331441"}, {"primary_key": "3114995", "vector": [], "sparse_vector": [], "title": "USEing Transfer Learning in Retrieval of Statistical Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "DSSM-like models showed good results in retrieval of short documents that semantically match the query. However, these models require large collections of click-through data that are not available in some domains. On the other hand, the recent advances in NLP demonstrated the possibility to fine-tune language models and models trained on one set of tasks to achieve a state of the art results on a multitude of other tasks or to get competitive results using much smaller training sets. Following this trend, we combined DSSM-like architecture with USE (Universal Sentence Encoder) and BERT (Bidirectional Encoder Representations from Transformers) models in order to be able to fine-tune them on a small amount of click-through data and use them for information retrieval. This approach allowed us to significantly improve our search engine for statistical data.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331427"}, {"primary_key": "3114996", "vector": [], "sparse_vector": [], "title": "Learning Unsupervised Semantic Document Representation for Fine-grained Aspect-based Sentiment Analysis.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Document representation is the core of many NLP tasks on machine understanding. A general representation learned in an unsupervised manner reserves generality and can be used for various applications. In practice, sentiment analysis (SA) has been a challenging task that is regarded to be deeply semantic-related and is often used to assess general representations. Existing methods on unsupervised document representation learning can be separated into two families: sequential ones, which explicitly take the ordering of words into consideration, and non-sequential ones, which do not explicitly do so. However, both of them suffer from their own weaknesses. In this paper, we propose a model that overcomes difficulties encountered by both families of methods. Experiments show that our model outperforms state-of-the-art methods on popular SA datasets and a fine-grained aspect-based SA by a large margin.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331320"}, {"primary_key": "3114997", "vector": [], "sparse_vector": [], "title": "AgentBuddy: an IR System based on Bandit Algorithms to Reduce Cognitive Load for Customer Care Agents.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We describe a human-in-the loop system - AgentBuddy, that is helping Intuit improve the quality of search it offers to its internal Customer Care Agents (CCAs). AgentBuddy aims to reduce the cognitive effort on part of the CCAs while at the same time boosting the quality of our legacy federated search system. Under the hood, it leverages bandit algorithms to improve federated search and other ML models like LDA, Siamese networks to help CCAs zero in on high quality search results. An intuitive UI designed ground up working with the users (CCAs) is another key feature of the system. AgentBuddy has been deployed internally and initial results from User Acceptance Trials indicate a 4x lift in quality of highlights compared to the incumbent system.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331408"}, {"primary_key": "3114998", "vector": [], "sparse_vector": [], "title": "Sequence and Time Aware Neighborhood for Session-based Recommendations: STAN.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lovekesh Vig", "<PERSON><PERSON><PERSON>"], "summary": "Recent advances in sequence-aware approaches for session-based recommendation, such as those based on recurrent neural networks, highlight the importance of leveraging sequential information from a session while making recommendations. Further, a session based k-nearest-neighbors approach (SKNN) has proven to be a strong baseline for session-based recommendations. However, SKNN does not take into account the readily available sequential and temporal information from sessions. In this work, we propose Sequence and Time Aware Neighborhood (STAN), with vanilla SKNN as its special case. STAN takes into account the following factors for making recommendations: i) position of an item in the current session, ii) recency of a past session w.r.t. to the current session, and iii) position of a recommendable item in a neighboring session. The importance of above factors for a specific application can be adjusted via controllable decay factors. Despite being simple, intuitive and easy to implement, empirical evaluation on three real-world datasets shows that STAN significantly improves over SKNN, and is even comparable to the recently proposed state-of-the-art deep learning approaches. Our results suggest that STAN can be considered as a strong baseline for evaluating session-based recommendation algorithms in future.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331322"}, {"primary_key": "3114999", "vector": [], "sparse_vector": [], "title": "From Text to Sound: A Preliminary Study on Retrieving Sound Effects to Radio Stories.", "authors": ["Songwei Ge", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Sound effects play an essential role in producing high-quality radio stories but require enormous labor cost to add. In this paper, we address the problem of automatically adding sound effects to radio stories with a retrieval-based model. However, directly implementing a tag-based retrieval model leads to high false positives due to the ambiguity of story contents. To solve this problem, we introduce a retrieval-based framework hybridized with a semantic inference model which helps to achieve robust retrieval results. Our model relies on fine-designed features extracted from the context of candidate triggers. We collect two story dubbing datasets through crowdsourcing to analyze the setting of adding sound effects and to train and test our proposed methods. We further discuss the importance of each feature and introduce several heuristic rules for the trade-off between precision and recall. Together with the text-to-speech technology, our results reveal a promising automatic pipeline on producing high-quality radio stories.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331274"}, {"primary_key": "3115001", "vector": [], "sparse_vector": [], "title": "Informing the Design of Conversational IR Systems: Framework and Result Presentation.", "authors": ["Souvick Ghosh"], "summary": "Recent developments in conversational IR have raised questions about the nature of interactions which occur between the user and the system and the cognitive capabilities expected of such systems. In our research, we investigate the completeness of existing theoretical frameworks in explaining conversational search data propose modifications to such systems. The linear and transient nature of speech makes it cognitively challenging for the user to process a large amount of information. We propose a study to evaluate the users' preference of modalities when using conversational search systems. The study will help us to understand how results should be presented in a conversational search system. As we observe how users search using audio queries, interact with the intermediary, and process the results presented, we aim to develop an insight on how to present results more efficiently in a conversational search setting. We also plan on exploring the effectiveness and consistency of different media in a conversational search setting. Our observations will inform future designs and help to create a better understanding of such systems.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331422"}, {"primary_key": "3115002", "vector": [], "sparse_vector": [], "title": "Leveraging Emotional Signals for Credibility Detection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The spread of false information on the Web is one of the main problems of our society. Automatic detection of fake news posts is a hard task since they are intentionally written to mislead the readers and to trigger intense emotions to them in an attempt to be disseminated in the social networks. Even though recent studies have explored different linguistic patterns of false claims, the role of emotional signals has not yet been explored. In this paper, we study the role of emotional signals in fake news detection. In particular, we propose an LSTM model that incorporates emotional signals extracted from the text of the claims to differentiate between credible and non-credible ones. Experiments on real world datasets show the importance of emotional signals for credibility assessment.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331285"}, {"primary_key": "3115003", "vector": [], "sparse_vector": [], "title": "Ranking Robustness In Adversarial Retrieval Settings.", "authors": ["<PERSON>"], "summary": "The adversarial retrieval setting over the Web entails many challenges. Some of these are due to, essentially, ranking competitions between document authors who modify them in response to rankings induced for queries of interest so as to rank-promote the documents. One unwarranted consequence is that rankings can rapidly change due to small, almost indiscernible changes, of documents. In recent work, we addressed the issue of ranking robustness under (adversarial) document manipulations for feature-based learning-to-rank approaches. We presented a formalism of different notions of ranking robustness that gave rise to a few theoretical findings. Empirical evaluation provided support to these findings. Our next goals are to devise learning-to-rank techniques for optimizing relevance and robustness simultaneously, study the connections between ranking robustness and fairness, and to devise additional testbeds for evaluating ranking robustness.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331414"}, {"primary_key": "3115006", "vector": [], "sparse_vector": [], "title": "Mention Recommendation in Twitter with Cooperative Multi-Agent Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhou", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In Twitter-like social networking services, the \"@'' symbol can be used with the tweet to mention users whom the user wants to alert regarding the message. An automatic suggestion to the user of a small list of candidate names can improve communication efficiency. Previous work usually used several most recent tweets or randomly select historical tweets to make an inference about this preferred list of names. However, because there are too many historical tweets by users and a wide variety of content types, the use of several tweets cannot guarantee the desired results. In this work, we propose the use of a novel cooperative multi-agent approach to mention recommendation, which incorporates dozens of more historical tweets than earlier approaches. The proposed method can effectively select a small set of historical tweets and cooperatively extract relevant indicator tweets from both the user and mentioned users. Experimental results demonstrate that the proposed method outperforms state-of-the-art methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331237"}, {"primary_key": "3115007", "vector": [], "sparse_vector": [], "title": "Quantifying and Alleviating the Language Prior Problem in Visual Question Answering.", "authors": ["<PERSON><PERSON> Guo", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Benefiting from the advancement of computer vision, natural language processing and information retrieval techniques, visual question answering (VQA), which aims to answer questions about an image or a video, has received lots of attentions over the past few years. Although some progress has been achieved so far, several studies have pointed out that current VQA models are heavily affected by the language prior problem, which means they tend to answer questions based on the co-occurrence patterns of question keywords (e.g., how many) and answers (e.g., 2) instead of understanding images and questions. Existing methods attempt to solve this problem by either balancing the biased datasets or forcing models to better understand images. However, only marginal effects and even performance deterioration are observed for the first and second solution, respectively. In addition, another important issue is the lack of measurement to quantitatively measure the extent of the language prior effect, which severely hinders the advancement of related techniques.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331186"}, {"primary_key": "3115008", "vector": [], "sparse_vector": [], "title": "MatchZoo: A Learning, Practicing, and Developing System for Neural Text Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiang Ji", "<PERSON><PERSON><PERSON>"], "summary": "Text matching is the core problem in many natural language processing (NLP) tasks, such as information retrieval, question answering, and conversation. Recently, deep leaning technology has been widely adopted for text matching, making neural text matching a new and active research domain. With a large number of neural matching models emerging rapidly, it becomes more and more difficult for researchers, especially those newcomers, to learn and understand these new models. Moreover, it is usually difficult to try these models due to the tedious data pre-processing, complicated parameter configuration, and massive optimization tricks, not to mention the unavailability of public codes sometimes. Finally, for researchers who want to develop new models, it is also not an easy task to implement a neural text matching model from scratch, and to compare with a bunch of existing models. In this paper, therefore, we present a novel system, namely MatchZoo, to facilitate the learning, practicing and designing of neural text matching models. The system consists of a powerful matching library and a user-friendly and interactive studio, which can help researchers: 1) to learn state-of-the-art neural text matching models systematically, 2) to train, test and apply these models with simple configurable steps; and 3) to develop their own models with rich APIs and assistance.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331403"}, {"primary_key": "3115009", "vector": [], "sparse_vector": [], "title": "Deep Natural Language Processing for Search Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep learning models have been very successful in many natural language processing tasks. Search engine works with rich natural language data, e.g., queries and documents, which implies great potential of applying deep natural language processing on such data to improve search performance. Furthermore, it opens an unprecedented opportunity to explore more advanced search experience, such as conversational search and chatbot. This tutorial offers an overview on deep learning based natural language processing for search systems from an industry perspective. We focus on how deep natural language processing powers search systems in practice. The tutorial introduces basic concepts, elaborates associated challenges, reviews the state-of-the-art approaches, covers end-to-end tasks in search systems with examples, and discusses the future trend.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331381"}, {"primary_key": "3115010", "vector": [], "sparse_vector": [], "title": "Order-aware Embedding Neural Network for CTR Prediction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Guo", "Jianhua Han", "<PERSON>", "Yuzhou Zhang"], "summary": "Product based models, which represent multi-field categorical data as embedding vectors of features, then model feature interactions in terms of vector product of shared embedding, have been extensively studied and have become one of the most popular techniques for CTR prediction. However, if the shared embedding is applied: (1) the angles of feature interactions of different orders may conflict with each other, (2) the gradients of feature interactions of high-orders may vanish, which result in learned feature interactions less effective. To solve these problems, we propose a novel technique named Order-aware Embedding (i.e., multi-embeddings are learned for each feature, and different embeddings are applied for feature interactions of different orders), which can be applied to various models and generates feature interactions more effectively. We further propose a novel order-aware embedding neural network (OENN) based on this embedding technique for CTR prediction. Extensive experiments on three publicly available datasets demonstrate the effectiveness of Order-aware Embedding and show that our OENN outperforms the state-of-the-art models.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331332"}, {"primary_key": "3115011", "vector": [], "sparse_vector": [], "title": "FAQ Retrieval Using Attentive Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The task of ranking question-answer pairs in response to an input query, aka FAQ (Frequently Asked Question) Retrieval, has traditionally been focused mostly on extracting relevance signals between query and questions based on extensive manual feature engineering. In this paper we propose multiple deep learning architectures designed for FAQ Retrieval that eliminate the need for feature engineering and are able to elegantly combine both query-question and query-answer similarities. We present experimental results showing that models that effectively combine both query-question and query-answer representations using attention mechanisms in a hierarchical manner yield the best results from all proposed models. We further verify the effectiveness of attention mechanisms for FAQ Retrieval by conducting experiments on a completely different attention-based architecture, originally designed for question duplicate detection tasks, and observing equally impressive experimental ranking results.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331294"}, {"primary_key": "3115012", "vector": [], "sparse_vector": [], "title": "Searching for Communities: a Facebook Way.", "authors": ["Viet Ha-Thuc", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Giving people the power to build community is central to Facebook's mission. Technically, searching for communities poses very different challenges compared to the standard IR problems. First, there is a vocabulary mismatch problem since most of the content of the communities is private. Second, the common labeling strategies based on human ratings and clicks do not work well due to limited public content available to third-party raters and users at search time. Finally, community search has a dual objective of satisfying searchers and growing the number of active communities. While A/B testing is a well known approach for assessing the former, it is an open question on how to measure progress on the latter. This talk discusses these challenges in depth and describes our solution.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331426"}, {"primary_key": "3115013", "vector": [], "sparse_vector": [], "title": "Learning More From Less: Towards Strengthening Weak Supervision for Ad-Hoc Retrieval.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The limited availability of ground truth relevance labels has been a major impediment to the application of supervised methods to ad-hoc retrieval. As a result, unsupervised scoring methods, such as BM25, remain strong competitors to deep learning techniques which have brought on dramatic improvements in other domains, such as computer vision and natural language processing. Recent works have shown that it is possible to take advantage of the performance of these unsupervised methods to generate training data for learning-to-rank models. The key limitation to this line of work is the size of the training set required to surpass the performance of the original unsupervised method, which can be as large as $10^{13}$ training examples. Building on these insights, we propose two methods to reduce the amount of training data required. The first method takes inspiration from crowdsourcing, and leverages multiple unsupervised rankers to generate soft, or noise-aware, training labels. The second identifies harmful, or mislabeled, training examples and removes them from the training set. We show that our methods allow us to surpass the performance of the unsupervised baseline with far fewer training examples than previous works.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331272"}, {"primary_key": "3115014", "vector": [], "sparse_vector": [], "title": "Prototype-guided Attribute-wise Interpretable Scheme for Clothing Matching.", "authors": ["Xianjing Han", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, as an essential part of people's daily life, clothing matching has gained increasing research attention. Most existing efforts focus on the numerical compatibility modeling between fashion items with advanced neural networks, and hence suffer from the poor interpretation, which makes them less applicable in real world applications. In fact, people prefer to know not only whether the given fashion items are compatible, but also the reasonable interpretations as well as suggestions regarding how to make the incompatible outfit harmonious. Considering that the research line of the comprehensively interpretable clothing matching is largely untapped, in this work, we propose a prototype-guided attribute-wise interpretable compatibility modeling (PAICM) scheme, which seamlessly integrates the latent compatible/incompatible prototype learning and compatibility modeling with the Bayesian personalized ranking (BPR) framework. In particular, the latent attribute interaction prototypes, learned by the non-negative matrix factorization (NMF), are treated as templates to interpret the discordant attribute and suggest the alternative item for each fashion item pair. Extensive experiments on the real-world dataset have demonstrated the effectiveness of our scheme.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331245"}, {"primary_key": "3115015", "vector": [], "sparse_vector": [], "title": "Contextually Propagated Term Weights for Document Representation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Word embeddings predict a word from its neighbours by learning small, dense embedding vectors. In practice, this prediction corresponds to a semantic score given to the predicted word (or term weight). We present a novel model that, given a target word, redistributes part of that word's weight (that has been computed with word embeddings) across words occurring in similar contexts as the target word. Thus, our model aims to simulate how semantic meaning is shared by words occurring in similar contexts, which is incorporated into bag-of-words document representations. Experimental evaluation in an unsupervised setting against 8 state of the art baselines shows that our model yields the best micro and macro F1 scores across datasets of increasing difficulty.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331307"}, {"primary_key": "3115016", "vector": [], "sparse_vector": [], "title": "Unsupervised Neural Generative Semantic Hashing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fast similarity search is a key component in large-scale information retrieval, where semantic hashing has become a popular strategy for representing documents as binary hash codes. Recent advances in this area have been obtained through neural network based models: generative models trained by learning to reconstruct the original documents. We present a novel unsupervised generative semantic hashing approach, Ranking based Semantic Hashing (RBSH) that consists of both a variational and a ranking based component. Similarly to variational autoencoders, the variational component is trained to reconstruct the original document conditioned on its generated hash code, and as in prior work, it only considers documents individually. The ranking component solves this limitation by incorporating inter-document similarity into the hash code generation, modelling document ranking through a hinge loss. To circumvent the need for labelled data to compute the hinge loss, we use a weak labeller and thus keep the approach fully unsupervised. Extensive experimental evaluation on four publicly available datasets against traditional baselines and recent state-of-the-art methods for semantic hashing shows that RBSH significantly outperforms all other methods across all evaluated hash code lengths. In fact, RBSH hash codes are able to perform similarly to state-of-the-art hash codes while using 2-4x fewer bits.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331255"}, {"primary_key": "3115018", "vector": [], "sparse_vector": [], "title": "Finding Camouflaged Needle in a Haystack?: Pornographic Products Detection via Berrypicking Tree Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It is an important and urgent research problem for decentralized eCommerce services, e.g., eBay, eBid, and Taobao, to detect illegal products, e.g., unclassified pornographic products. However, it is a challenging task as some sellers may utilize and change camouflaged text to deceive the current detection algorithms. In this study, we propose a novel task to dynamically locate the pornographic products from very large product collections. Unlike prior product classification efforts focusing on textual information, the proposed model, BerryPIcking TRee MoDel (BIRD), utilizes both product textual content and buyers' seeking behavior information as berrypicking trees. In particular, the BIRD encodes both semantic information with respect to all branches sequence and the overall latent buyer intent during the whole seeking process. An extensive set of experiments have been conducted to demonstrate the advantage of the proposed model against alternative solutions. To facilitate further research of this practical and important problem, the codes and buyers' seeking behavior data have been made publicly available1.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331197"}, {"primary_key": "3115020", "vector": [], "sparse_vector": [], "title": "On the Effect of Low-Frequency Terms on Neural-IR Models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Low-frequency terms are a recurring challenge for information retrieval models, especially neural IR frameworks struggle with adequately capturing infrequently observed words. While these terms are often removed from neural models - mainly as a concession to efficiency demands - they traditionally play an important role in the performance of IR models. In this paper, we analyze the effects of low-frequency terms on the performance and robustness of neural IR models. We conduct controlled experiments on three recent neural IR models, trained on a large-scale passage retrieval collection. We evaluate the neural IR models with various vocabulary sizes for their respective word embeddings, considering different levels of constraints on the available GPU memory. We observe that despite the significant benefits of using larger vocabularies, the performance gap between the vocabularies can be, to a great extent, mitigated by extensive tuning of a related parameter: the number of documents to re-rank. We further investigate the use of subword-token embedding models, and in particular FastText, for neural IR models. Our experiments show that using FastText brings slight improvements to the overall performance of the neural IR models in comparison to models trained on the full vocabulary, while the improvement becomes much more pronounced for queries containing low-frequency terms.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331344"}, {"primary_key": "3115021", "vector": [], "sparse_vector": [], "title": "Implicit Entity Recognition, Classification and Linking in Tweets.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Linking phrases to knowledge base entities is a process known as entity linking and has already been widely explored for various content types such as tweets. A major step in entity linking is to recognize and/or classify phrases that can be disambiguated and linked to knowledge base entities, i.e., Named Entity Recognition and Classification. Unlike common entity recognition and linking systems, however, we aim to recognize, classify, and link entities which are implicitly mentioned, and hence lack a surface form, to appropriate knowledge base entries. In other words, the objective of our work is to recognize and identify core entities of a tweet when those entities are not explicitly mentioned; this process is referred to as Implicit Named Entity Recognition and Linking.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331416"}, {"primary_key": "3115022", "vector": [], "sparse_vector": [], "title": "Scalable Deep Multimodal Learning for Cross-Modal Retrieval.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cross-modal retrieval takes one type of data as the query to retrieve relevant data of another type. Most of existing cross-modal retrieval approaches were proposed to learn a common subspace in a joint manner, where the data from all modalities have to be involved during the whole training process. For these approaches, the optimal parameters of different modality-specific transformations are dependent on each other and the whole model has to be retrained when handling samples from new modalities. In this paper, we present a novel cross-modal retrieval method, called Scalable Deep Multimodal Learning (SDML). It proposes to predefine a common subspace, in which the between-class variation is maximized while the within-class variation is minimized. Then, it trains m modality-specific networks for m modalities (one network for each modality) to transform the multimodal data into the predefined common subspace to achieve multimodal learning. Unlike many of the existing methods, our method can train different modality-specific networks independently and thus be scalable to the number of modalities. To the best of our knowledge, the proposed SDML could be one of the first works to independently project data of an unfixed number of modalities into a predefined common subspace. Comprehensive experimental results on four widely-used benchmark datasets demonstrate that the proposed method is effective and efficient in multimodal learning and outperforms the state-of-the-art methods in cross-modal retrieval.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331213"}, {"primary_key": "3115023", "vector": [], "sparse_vector": [], "title": "Coarse-to-Fine Grained Classification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Fine-grained image classification and retrieval become topical in both computer vision and information retrieval. In real-life scenarios, fine-grained tasks tend to appear along with coarse-grained tasks when the observed object is coming closer. However, in previous works, the combination of fine-grained and coarse-grained tasks was often ignored. In this paper, we define a new problem called coarse-to-fine grained classification (C2FGC) which aims to recognize the classes of objects in multiple resolutions (from low to high). To solve this problem, we propose a novel Multi-linear Pooling with Hierarchy (MLPH) model. Specifically, we first design a multi-linear pooling module to include both trilinear and bilinear pooling, and then formulate the coarse-grained and fine-grained tasks within a unified framework. Experiments on two benchmark datasets show that our model achieves state-of-the-art results.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331336"}, {"primary_key": "3115024", "vector": [], "sparse_vector": [], "title": "Identifying Entity Properties from Text with Zero-shot Learning.", "authors": ["Wiradee Imrattanatrai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a method for identifying a set of entity properties from text. Identifying entity properties is similar to a relation extraction task that can be cast as a classification of sentences. Normally, this task can be achieved by distant supervised learning by automatically preparing training sentences for each property; however, it is impractical to prepare training sentences for every property. Therefore, we describe a zero-shot learning problem for this task and propose a neural network-based model that does not rely on a complete training set comprising training sentences for every property. To achieve this, we utilize embeddings of properties obtained from a knowledge graph embedding using different components of a knowledge graph structure. The embeddings of properties are combined with the model to enable identification of properties with no available training sentences. By using our newly constructed dataset as well as an existing dataset, experiments revealed that our model achieved a better performance for properties with no training sentences, relative to baseline results, even comparable to that achieved for properties with training sentences.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331220"}, {"primary_key": "3115026", "vector": [], "sparse_vector": [], "title": "To Model or to Intervene: A Comparison of Counterfactual and Online Learning to Rank from User Interactions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Learning to Rank (LTR) from user interactions is challenging as user feedback often contains high levels of bias and noise. At the moment, two methodologies for dealing with bias prevail in the field of LTR: counterfactual methods that learn from historical data and model user behavior to deal with biases; and online methods that perform interventions to deal with bias but use no explicit user models. For practitioners the decision between either methodology is very important because of its direct impact on end users. Nevertheless, there has never been a direct comparison between these two approaches to unbiased LTR. In this study we provide the first benchmarking of both counterfactual and online LTR methods under different experimental conditions. Our results show that the choice between the methodologies is consequential and depends on the presence of selection bias, and the degree of position bias and interaction noise. In settings with little bias or noise counterfactual methods can obtain the highest ranking performance; however, in other circumstances their optimization can be detrimental to the user experience. Conversely, online methods are very robust to bias and noise but require control over the displayed rankings. Our findings confirm and contradict existing expectations on the impact of model-based and intervention-based methods in LTR, and allow practitioners to make an informed decision between the two methodologies.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331269"}, {"primary_key": "3115028", "vector": [], "sparse_vector": [], "title": "Family History Discovery through Search at Ancestry.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "At Ancestry, we apply learning to rank algorithms to a new area to assist our customers in better understanding their family history. The foundation of our service is an extensive and unique collection of billions of historical records that we have digitized and indexed. Currently, our content collection includes 20 billion historical records. The record data consists of birth records, death records, marriage records, adoption records, census records, obituary records, among many others types. It is important for us to return relevant records from diversified record types in order to assist our customers to better understand their family history.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331430"}, {"primary_key": "3115029", "vector": [], "sparse_vector": [], "title": "Health Cards for Consumer Health Search.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper investigates the impact of health cards in consumer health search (CHS) - people seeking health advice online. Health cards are a concise presentations of a health concept shown along side search results to specific health queries; they have the potential to convey health information in easily digestible form for the general public. However, little evidence exists on how effective health cards actually are for users when searching health advice online, and whether their effectiveness is limited to specific health search intents. To understand the impact of health cards on CHS, we conducted a laboratory study to observe users completing CHS tasks using two search interface variants: one just with result snippets and one containing both result snippets and health cards. Our study makes the following contributions: (1) it reveals how and when health cards are beneficial to users in completing consumer health search tasks, and (2) it identifies the features of health cards that helped users in completing their tasks. This is the first study that thoroughly investigates the effectiveness of health cards in supporting consumer health search.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331194"}, {"primary_key": "3115030", "vector": [], "sparse_vector": [], "title": "Video Dialog via Multi-Grained Convolutional Self-Attention Context Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Yu<PERSON>"], "summary": "Video dialog is a new and challenging task, which requires an AI agent to maintain a meaningful dialog with humans in natural language about video contents. Specifically, given a video, a dialog history and a new question about the video, the agent has to combine video information with dialog history to infer the answer. And due to the complexity of video information, the methods of image dialog might be ineffectively applied directly to video dialog. In this paper, we propose a novel approach for video dialog called multi-grained convolutional self-attention context network, which combines video information with dialog history. Instead of using RNN to encode the sequence information, we design a multi-grained convolutional self-attention mechanism to capture both element and segment level interactions which contain multi-grained sequence information. Then, we design a hierarchical dialog history encoder to learn the context-aware question representation and a two-stream video encoder to learn the context-aware video representation. We evaluate our method on two large-scale datasets. Due to the flexibility and parallelism of the new attention mechanism, our method can achieve higher time efficiency, and the extensive experiments also show the effectiveness of our method.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331240"}, {"primary_key": "3115031", "vector": [], "sparse_vector": [], "title": "Item Recommendation by Combining Relative and Absolute Feedback Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "User preferences in the form of absolute feedback, s.a., ratings, are widely exploited in Recommender Systems (RSs). Recent research has explored the usage of preferences expressed with pairwise comparisons, which signal relative feedback. It has been shown that pairwise comparisons can be effectively combined with ratings, but, it is important to fine tune the technique that leverages both types of feedback. Previous approaches train a single model by converting ratings into pairwise comparisons, and then use only that type of data. However, we claim that these two types of preferences reveal different information about users interests and should be exploited differently. Hence, in this work, we develop a ranking technique that separately exploits absolute and relative preferences in a hybrid model. In particular, we propose a joint loss function which is computed on both absolute and relative preferences of users. Our proposed ranking model uses pairwise comparisons data to predict the user's preference order between pairs of items and uses ratings to push high rated (relevant) items to the top of the ranking. Experimental results on three different data sets demonstrate that the proposed technique outperforms competitive baseline algorithms on popular ranking-oriented evaluation metrics.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331295"}, {"primary_key": "3115034", "vector": [], "sparse_vector": [], "title": "Why do Users Issue Good Queries?: Neural Correlates of Term Specificity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite advances in the past few decades in studying what kind of queries users input to search engines and how to suggest queries for the users, the fundamental question of what makes human cognition able to estimate goodness of query terms is largely unanswered. For example, a person searching information about \"cats'' is able to choose query terms, such as \"housecat'', \"feline'', or \"animal'' and avoid terms like \"similar'', \"variety'', and \"distinguish''. We investigated the association between the specificity of terms occurring in documents and human brain activity measured via electroencephalography (EEG). We analyzed the brain activity data of fifteen participants, recorded in response to reading terms from Wikipedia documents. Term specificity was shown to be associated with the amplitude of evoked brain responses. The results indicate that by being able to determine which terms carry maximal information about, and can best discriminate between, documents, people have the capability to enter good query terms. Moreover, our results suggest that the effective query term selection process, often observed in practical search behavior studies, has a neural basis. We believe our findings constitute an important step in revealing the cognitive processing behind query formulation and evaluating informativeness of language in general.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331243"}, {"primary_key": "3115037", "vector": [], "sparse_vector": [], "title": "The Emotion Profile of Web Search.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Emotions are an essential part of most human activities, including decision-making. Emotions arise in response to information, e.g., presented in web pages, and are also expressed in the words used to convey that information in the first place. In this paper, we study the emotion profile of retrieved and clicked web search results towards the goal of better understanding the role of emotions in web search. Using click logs from a four-month period, up to the end of January 2019, we examine the emotions associated with search results and contrast them to the emotions of clicked results, taking rank and relevance into account. Emotions are assigned to web pages based on two lexicons: SentiWordNet (positive, negative and objective sentiments) and EmoLexData (afraid, amused, angry, annoyed, don't care, happy, inspired, and sad emotions). We look at the sentiment/emotion profiles of search results grouped around a set of controversial and mundane topics and hypothesise that users are more likely to click emotionally charged results than emotionless results, both in general, and in particular when their query relates to controversial topics.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331314"}, {"primary_key": "3115038", "vector": [], "sparse_vector": [], "title": "Expert-Guided Entity Extraction using Expressive Rules.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Knowledge Graph Construction (KGC) is an important problem that has many domain-specific applications, including semantic search and predictive analytics. As sophisticated KGC algorithms continue to be proposed, an important, neglected use case is to empower domain experts who do not have much technical background to construct high-fidelity, interpretable knowledge graphs. Such domain experts are a valuable source of input because of their (both formal and learned) knowledge of the domain. In this demonstration paper, we present a system that allows domain experts to construct knowledge graphs by writing sophisticated rule-based entity extractors with minimal training, using a GUI-based editor that offers a range of complex facilities.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331392"}, {"primary_key": "3115039", "vector": [], "sparse_vector": [], "title": "Cleaned Similarity for Better Memory-Based Recommenders.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Memory-based collaborative filtering methods like user or item k-nearest neighbors (kNN) are a simple yet effective solution to the recommendation problem. The backbone of these methods is the estimation of the empirical similarity between users/items. In this paper, we analyze the spectral properties of the Pearson and the cosine similarity estimators, and we use tools from random matrix theory to argue that they suffer from noise and eigenvalues spreading. We argue that, unlike the Pearson correlation, the cosine similarity naturally possesses the desirable property of eigenvalue shrinkage for large eigenvalues. However, due to its zero-mean assumption, it overestimates the largest eigenvalues. We quantify this overestimation and present a simple re-scaling and noise cleaning scheme. This results in better performance of the memory-based methods compared to their vanilla counterparts.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331310"}, {"primary_key": "3115041", "vector": [], "sparse_vector": [], "title": "Optimal Freshness Crawl Under Politeness Constraints.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A Web crawler is an essential part of a search engine that procures information subsequently served by the search engine to its users. As the Web is becoming increasingly more dynamic, in addition to discovering new web pages a crawler needs to keep revisiting those already in the search engine's index, in order to keep the index fresh by picking up the pages' changed content. Determining how often to recrawl pages requires making tradeoffs based on the pages' relative importance and change rates, subject to multiple resource constraints - the limited daily budget of crawl requests on the search engine's end and politeness constraints restricting the rate at which pages can be requested from a given host. In this paper, we introduce PoliteBinaryLambdaCrawl, the first optimal algorithm for freshness crawl scheduling in the presence of politeness constraints as well as non-uniform page importance scores and the crawler's own crawl request limit. We also propose an approximation for it, stating its theoretical optimality conditions and in the process discovering a connection to an approach previously thought of as a mere heuristic for freshness crawl scheduling. We explore the relative performance of PoliteBinaryLambdaCrawl and other methods for handling politeness constraints on a dataset collected by crawling over 18.5M URLs daily over 14 weeks.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331241"}, {"primary_key": "3115043", "vector": [], "sparse_vector": [], "title": "Help Me Search: Leveraging User-System Collaboration for Query Construction to Improve Accuracy for Difficult Queries.", "authors": ["Saar <PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we address the problem of difficult queries by using a novel strategy of collaborative query construction where the search engine would actively engage users in an iterative process to continuously revise a query. This approach can be implemented in any search engine to provide search support for users via a \"Help Me Search\" button, which a user can click on as needed. We focus on studying a specific collaboration strategy where the search engine and the user work together to iteratively expand a query. We propose a possible implementation for this strategy in which the system generates candidate terms by utilizing the history of interactions of the user with the system. Evaluation using a simulated user study shows the great promise of the proposed approach. We also perform a case study with three real users which further illustrates the potential effectiveness of the approach.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331362"}, {"primary_key": "3115044", "vector": [], "sparse_vector": [], "title": "Challenges in Search on Streaming Services: Netflix Case Study.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We discuss salient challenges of building a search experience for a streaming media service such as Netflix. We provide an overview of the role of recommendations within the search context to aid content discovery and support searches for unavailable (out-of-catalog) entities. We also stress the importance of keystroke-level Instant Search experience, and the technical challenges associated with implementing it across different devices and languages for a global audience.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331440"}, {"primary_key": "3115045", "vector": [], "sparse_vector": [], "title": "Name Entity Recognition with Policy-Value Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we propose a novel reinforcement learning based model for named entity recognition (NER), referred to as MM-NER. Inspired by the methodology of the AlphaGo Zero, MM-NER formalizes the problem of named entity recognition with a Monte-Carlo tree search (MCTS) enhanced Markov decision process (MDP) model, in which the time steps correspond to the positions of words in a sentence from left to right, and each action corresponds to assign an NER tag to a word. Two Gated Recurrent Units (GRU) are used to summarize the past tag assignments and words in the sentence. Based on the outputs of GRUs, the policy for guiding the tag assignment and the value for predicting the whole tagging accuracy of the whole sentence are produced. The policy and value are then strengthened with MCTS, which takes the produced raw policy and value as inputs, simulates and evaluates the possible tag assignments at the subsequent positions, and outputs a better search policy for assigning tags. A reinforcement learning algorithm is proposed to train the model parameters. Empirically, we show that MM-NER can accurately predict the tags thanks to the exploratory decision making mechanism introduced by MCTS. It outperformed the conventional sequence tagging baselines and performed equally well with the state-of-the-art baseline BLSTM-CRF.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331349"}, {"primary_key": "3115046", "vector": [], "sparse_vector": [], "title": "Hyperlink Classification via Structured Graph Embedding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We formally define a hyperlink classification problem in web search by classifying hyperlinks into three classes based on their roles: navigation, suggestion, and action. Real-world web graph datasets are generated for this task. We approach the hyperlink classification problem from a structured graph embedding perspective, and show that we can solve the problem by modifying the recently proposed knowledge graph embedding techniques. The key idea of our modification is to introduce a relation perturbation while the original knowledge graph embedding models only corrupt entities when generating negative triplets in training. To the best of our knowledge, this is the first study to apply the knowledge graph embedding idea to the hyperlink classification problem. We show that our model significantly outperforms the original knowledge graph embedding models in classifying hyperlinks on web graphs.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331325"}, {"primary_key": "3115047", "vector": [], "sparse_vector": [], "title": "A Study on Agreement in PICO Span Annotations.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In evidence-based medicine, relevance of medical literature is determined by predefined relevance conditions. The conditions are defined based on PICO elements, namely, Patient, Intervention, Comparator, and Outcome. Hence, PICO annotations in medical literature are essential for automatic relevant document filtering. However, defining boundaries of text spans for PICO elements is not straightforward. In this paper, we study the agreement of PICO annotations made by multiple human annotators, including both experts and non-experts. Agreements are estimated by a standard span agreement (i.e. matching both labels and boundaries of text spans), and two types of relaxed span agreement (i.e. matching labels without guaranteeing matching boundaries of spans). Based on the analysis, we report two observations: (i) Boundaries of PICO span annotations by individual annotators are very diverse. (ii) Despite the disagreement in span boundaries, general areas of the span annotations arebroadly agreed by annotators. Our results suggest that applying a standard agreement alone may undermine the agreement of PICO spans, and adopting both a standard and a relaxed agreements is more suitable for PICO span evaluation.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331352"}, {"primary_key": "3115048", "vector": [], "sparse_vector": [], "title": "Social Attentive Deep Q-network for Recommendation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While deep reinforcement learning has been successfully applied to recommender systems, it is challenging and unexplored to improve the performance of deep reinforcement learning recommenders by effectively utilizing the pervasive social networks. In this work, we develop a Social Attentive Deep Q-network (SADQN) agent, which is able to provide high-quality recommendations during user-agent interactions by leveraging social influence among users. Specifically, SADQN is able to estimate action-values not only based on the users' personal preferences, but also based on their social neighbors' preferences by employing a particular social attention layer. The experimental results on three real-world datasets demonstrate that SADQN significantly improves the performance of deep reinforcement learning agents that overlook social influence.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331302"}, {"primary_key": "3115049", "vector": [], "sparse_vector": [], "title": "Nobody Said it Would be Easy: A Decade of R&amp;D Projects in Information Access from Thomson over Reuters to Refinitiv.", "authors": ["<PERSON><PERSON>"], "summary": "In this talk, I survey a small, non-random sample of research projects in information access carried out as part of the Thomson Reuters family of companies over the course of a 10+-year period. I analyse into how these projects are similar and different when compared to academic research efforts and attempt a critical (and personal, so certainly subjective) assessment of what academia can do for industry, and what industry can do for research in terms of R&D efforts. I will conclude with some advice for academic-industry collaboration initiatives in several areas of vertical information services (legal, finance, pharma and regulatory/compliance) as well as news.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331444"}, {"primary_key": "3115050", "vector": [], "sparse_vector": [], "title": "Information Nutritional Label and Word Embedding to Estimate Information Check-Worthiness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>"], "summary": "Automatic fact-checking is an important challenge nowadays since anyone can write about anything and spread it in social media, no matter the information quality. In this paper, we revisit the information check-worthiness problem and propose a method that combines the \"information nutritional label\" features with POS-tags and word-embedding representations. To predict the information check-worthy claim, we train a machine learning model based on these features. We experiment and evaluate the proposed approach on the CheckThat! CLEF 2018 collection. The experimental result shows that our model that combines information nutritional label and word-embedding features outperforms the baselines and the official participants' runs of CheckThat! 2018 challenge.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331298"}, {"primary_key": "3115052", "vector": [], "sparse_vector": [], "title": "Multimodal Data Fusion with Quantum Inspiration.", "authors": ["<PERSON><PERSON>"], "summary": "Language understanding is multimodal. During human communication, messages are conveyed not only by words in textual form, but also through speech patterns, gestures or facial emotions of the speakers. Therefore, it is crucial to fuse information from different modalities to achieve a joint comprehension.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331419"}, {"primary_key": "3115053", "vector": [], "sparse_vector": [], "title": "Graph Intention Network for Click-through Rate Prediction in Sponsored Search.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON> Ren", "<PERSON>", "<PERSON><PERSON>"], "summary": "Estimating click-through rate (CTR) accurately has an essential impact on improving user experience and revenue in sponsored search. For CTR prediction model, it is necessary to make out user's real-time search intention. Most of the current work is to mine their intentions based on users' real-time behaviors. However, it is difficult to capture the intention when user behaviors are sparse, causing thebehavior sparsity problem. Moreover, it is difficult for user to jump out of their specific historical behaviors for possible interest exploration, namelyweak generalization problem. We propose a new approach Graph Intention Network (GIN) based on co-occurrence commodity graph to mine user intention. By adopting multi-layered graph diffusion, GIN enriches user behaviors to solve the behavior sparsity problem. By introducing co-occurrence relationship of commodities to explore the potential preferences, the weak generalization problem is also alleviated. To the best of our knowledge, the GIN method is the first to introduce graph learning for user intention mining in CTR prediction and propose end-to-end joint training of graph learning and CTR prediction tasks in sponsored search. At present, GIN has achieved excellent offline results on the real-world data of the e-commerce platform outperforming existing deep learning models, and has been running stable tests online and achieved significant CTR improvements.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331283"}, {"primary_key": "3115054", "vector": [], "sparse_vector": [], "title": "From Semantic Retrieval to Pairwise Ranking: Applying Deep Learning in E-commerce Search.", "authors": ["<PERSON><PERSON>", "Yunjiang Jiang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce deep learning models to the two most important stages in product search at JD.com, one of the largest e-commerce platforms in the world. Specifically, we outline the design of a deep learning system that retrieves semantically relevant items to a query within milliseconds, and a pairwise deep re-ranking system, which learns subtle user preferences. Compared to traditional search systems, the proposed approaches are better at semantic retrieval and personalized ranking, achieving significant improvements.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331434"}, {"primary_key": "3115055", "vector": [], "sparse_vector": [], "title": "Teach Machine How to Read: Reading Behavior Inspired Relevance Estimation.", "authors": ["Xiangsheng Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Retrieval models aim to estimate the relevance of a document to a certain query. Although existing retrieval models have gained much success in both deepening our understanding of information seeking behavior and constructing practical retrieval systems (e.g. Web search engines), we have to admit that the models work in a rather different manner than how humans make relevance judgments. In this paper, we aim to reexamine the existing models as well as to propose new ones based on the findings in how human read documents during relevance judgment. First, we summarize a number of reading heuristics from practical user behavior patterns, which are categorized into implicit and explicit heuristics. By reviewing a variety of existing retrieval models, we find that most of them only satisfy a part of these reading heuristics. To evaluate the effectiveness of each heuristic, we conduct an ablation study and find that most heuristics have positive impacts on retrieval performance. We further integrate all the effective heuristics into a new retrieval model named Reading Inspired Model (RIM). Specifically, implicit reading heuristics are incorporated into the model framework and explicit reading heuristics are modeled as a Markov Decision Process and learned by reinforcement learning. Experimental results on a large-scale public available benchmark dataset and two test sets from NTCIR WWW tasks show that RIM outperforms most existing models, which illustrates the effectiveness of the reading heuristics. We believe that this work contributes to constructing retrieval models with both higher retrieval performance and better explainability.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331205"}, {"primary_key": "3115056", "vector": [], "sparse_vector": [], "title": "A Capsule Network for Recommendation and Explaining What You Like and Dislike.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "User reviews contain rich semantics towards the preference of users to features of items. Recently, many deep learning based solutions have been proposed by exploiting reviews for recommendation. The attention mechanism is mainly adopted in these works to identify words or aspects that are important for rating prediction. However, it is still hard to understand whether a user likes or dislikes an aspect of an item according to what viewpoint the user holds and to what extent, without examining the review details. Here, we consider a pair of a viewpoint held by a user and an aspect of an item as a logic unit. Reasoning a rating behavior by discovering the informative logic units from the reviews and resolving their corresponding sentiments could enable a better rating prediction with explanation.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331216"}, {"primary_key": "3115057", "vector": [], "sparse_vector": [], "title": "Multi-view Embedding-based Synonyms for Email Search.", "authors": ["<PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>", "Hongbo Deng", "<PERSON>", "<PERSON>"], "summary": "Synonym expansion is a technique that adds related words to search queries, which may lead to more relevant documents being retrieved, thus improving recall. There is extensive prior work on synonym expansion for web search, however very few studies have tackled its application for email search. Synonym expansion for private corpora like emails poses several unique research challenges. First, the emails are not shared across users, which precludes us from directly employing query-document bipartite graphs, which are standard in web search synonym expansion. Second, user search queries are of personal nature, and may not be generalizable across users. Third, the size of the underlying corpora from which the synonyms may be mined is relatively small (i.e., user's private email inbox) compared to the size of the web corpus. Therefore, in this paper, we propose a solution tailored to the challenges of synonym expansion for email search. We formulate it as a multi-view learning problem, and propose a novel embedding-based model that joins information from multiple sources to obtain the optimal synonym candidates. To demonstrate the effectiveness of the proposed technique, we evaluate our model using both explicit human ratings as well as a live experiment using the Gmail Search service, one of the world's largest email search engines.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331250"}, {"primary_key": "3115059", "vector": [], "sparse_vector": [], "title": "Uncovering Insurance Fraud Conspiracy with Network Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yuan Qi"], "summary": "Fraudulent claim detection is one of the greatest challenges the insurance industry faces. Alibaba's return-freight insurance, providing return-shipping postage compensations over product return on the e-commerce platform, receives thousands of potentially fraudulent claims everyday. Such deliberate abuse of the insurance policy could lead to heavy financial losses. In order to detect and prevent fraudulent insurance claims, we developed a novel data-driven procedure to identify groups of organized fraudsters, one of the major contributions to financial losses, by learning network information. In this paper, we introduce a device-sharing network among claimants, followed by developing an automated solution for fraud detection based on graph learning algorithms, to separate fraudsters from regular customers and uncover groups of organized fraudsters. This solution applied at Alibaba achieves more than 80% precision while covering 44% more suspicious accounts compared with a previously deployed rule-based classifier after human expert investigations. Our approach can easily and effectively generalizes to other types of insurance.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331372"}, {"primary_key": "3115060", "vector": [], "sparse_vector": [], "title": "Adaptive Multi-Attention Network Incorporating Answer Information for Duplicate Question Detection.", "authors": ["<PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON> Zhang", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Community-based question answering (CQA), which provides a platform for people with diverse backgrounds to share information and knowledge, has become increasingly popular. With the accumulation of site data, methods to detect duplicate questions in CQA sites have attracted considerable attention. Existing methods typically use only questions to complete the task. However, the paired answers may also provide valuable information. In this paper, we propose an answer information- enhanced adaptive multi-attention network (AMAN) to perform this task. AMAN takes full advantage of the semantic information in the paired answers while alleviating the noise problem caused by adding the answers. To evaluate the proposed method, we use a CQADupStack set and the Quora question-pair dataset expanded with paired answers. Experimental results demonstrate that the proposed model can achieve state-of-the-art performance on the above two data sets.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331228"}, {"primary_key": "3115061", "vector": [], "sparse_vector": [], "title": "CROSS: Cross-platform Recommendation for Social E-Commerce.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social e-commerce, as a new concept of e-commerce, uses social media as a new prevalent platform for online shopping. Users are now able to view, add to cart, and buy products within a single social media app. In this paper, we address the problem of cross-platform recommendation for social e-commerce, i.e., recommending products to users when they are shopping through social media. To the best of our knowledge, this is a new and important problem for all e-commerce companies (e.g. Amazon, Alibaba), but has never been studied before.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331191"}, {"primary_key": "3115062", "vector": [], "sparse_vector": [], "title": "The Impact of Score Ties on Repeatability in Document Ranking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Document ranking experiments should be repeatable. However, the interaction between multi-threaded indexing and score ties during retrieval may yield non-deterministic rankings, making repeatability not as trivial as one might imagine. In the context of the open-source Lucene search engine, score ties are broken by internal document ids, which are assigned at index time. Due to multi-threaded indexing, which makes experimentation with large modern document collections practical, internal document ids are not assigned consistently between different index instances of the same collection, and thus score ties are broken unpredictably. This short paper examines the effectiveness impact of such score ties, quantifying the variability that can be attributed to this phenomenon. The obvious solution to this non-determinism and to ensure repeatable document ranking is to break score ties using external collection document ids. This approach, however, comes with measurable efficiency costs due to the necessity of consulting external identifiers during query evaluation.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331339"}, {"primary_key": "3115064", "vector": [], "sparse_vector": [], "title": "Example-based Search: a New Frontier for Exploratory Search.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Exploration is one of the primordial ways to accrue knowledge about the world and its nature. As we accumulate, mostly automatically, data at unprecedented volumes and speed, our datasets have become complex and hard to understand. In this context, exploratory search provides a handy tool for progressively gather the necessary knowledge by starting from a tentative query that can provide cues about the next queries to issue. An exploratory query should be simple enough to avoid complicate declarative languages (such as SQL) and convoluted mechanism, and at the same time retain the flexibility and expressiveness required to express complex information needs. Recently, we have witnessed a rediscovery of the so called example-based methods, in which the user, or the analyst circumvent query languages by using examples as input. This shift in semantics has led to a number of methods receiving as query a set of example members of the answer set. The search system then infers the entire answer set based on the given examples and any additional information provided by the underlying database. In this tutorial, we present an excursus over the main example-based methods for exploratory analysis. We show how different data types require different techniques, and present algorithms that are specifically designed for relational, textual, and graph data. We conclude by providing a unifying view of this query-paradigm and identify new exciting research directions.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331387"}, {"primary_key": "3115065", "vector": [], "sparse_vector": [], "title": "Evaluating Resource-Lean Cross-Lingual Embedding Models in Unsupervised Retrieval.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cross-lingual embeddings (CLE) facilitate cross-lingual natural language processing and information retrieval. Recently, a wide variety of resource-lean projection-based models for inducing CLEs has been introduced, requiring limited or no bilingual supervision. Despite potential usefulness in downstream IR and NLP tasks, these CLE models have almost exclusively been evaluated on word translation tasks. In this work, we provide a comprehensive comparative evaluation of projection-based CLE models for both sentence-level and document-level cross-lingual Information Retrieval (CLIR). We show that in some settings resource-lean CLE-based CLIR models may outperform resource-intensive models using full-blown machine translation (MT). We hope our work serves as a guideline for choosing the right model for CLIR practitioners.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331324"}, {"primary_key": "3115066", "vector": [], "sparse_vector": [], "title": "Characterizing the Stages of Complex Tasks.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Stage is an essential facet of task. At different stages of search, users' search strategies are often influenced by different search intentions, encountered problems, as well as knowledge states. In previous studies, information seeking and interactive IR researchers have developed and validated some frameworks for describing various task facets and features. However, few studies have explored how to depict and differentiate different stages or states of complex search tasks in a comprehensive, multidimensional manner. The existing theoretical models of search process offer limited contributions to search path evaluation and the design of system recommendations for users at different states. To address this issue at both theoretical and empirical levels, my dissertation aims to construct an explainable framework that can characterize the stages or states of complex search tasks over multiple dimensions and to apply the framework in proactive search path evaluation and recommendation.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331413"}, {"primary_key": "3115067", "vector": [], "sparse_vector": [], "title": "From Query Variations To Learned Relevance Modeling.", "authors": ["<PERSON><PERSON><PERSON> Liu"], "summary": "Thinking in terms of an information need instead of simply queries provides a rich set of new opportunities in improving the effectiveness of search [6]. User queries may vary a lot for a single information need [3, 9], as a query is often under-specified. Many techniques have been proposed to enrich a single query, for example relevance modeling [8]. These techniques focus on improving overall system performance but may fail in some occasions. Instead of optimizing for a single query, another direction is to use multiple query variations to represent an information need. With fusion techniques, query variations can improve system performance while failing fewer queries [2, 6].", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331418"}, {"primary_key": "3115068", "vector": [], "sparse_vector": [], "title": "Compositional Coding for Collaborative Filtering.", "authors": ["<PERSON><PERSON>", "Tao Lu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jianling Sun", "<PERSON>"], "summary": "Efficiency is crucial to the online recommender systems, especially for the ones which needs to deal with tens of millions of users and items. Because representing users and items as binary vectors for Collaborative Filtering (CF) can achieve fast user-item affinity computation in the Hamming space, in recent years, we have witnessed an emerging research effort in exploiting binary hashing techniques for CF methods. However, CF with binary codes naturally suffers from low accuracy due to limited representation capability in each bit, which impedes it from modeling complex structure of the data.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331206"}, {"primary_key": "3115069", "vector": [], "sparse_vector": [], "title": "NRPA: Neural Recommendation with Personalized Attention.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing review-based recommendation methods usually use the same model to learn the representations of all users/items from reviews posted by users towards items. However, different users have different preference and different items have different characteristics. Thus, the same word or the similar reviews may have different informativeness for different users and items. In this paper we propose a neural recommendation approach with personalized attention to learn personalized representations of users and items from reviews. We use a review encoder to learn representations of reviews from words, and a user/item encoder to learn representations of users or items from reviews. We propose a personalized attention model, and apply it to both review and user/item encoders to select different important words and reviews for different users/items. Experiments on five datasets validate our approach can effectively improve the performance of neural recommendation.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331371"}, {"primary_key": "3115071", "vector": [], "sparse_vector": [], "title": "Online Multi-modal Hashing with Dynamic Query-adaption.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Multi-modal hashing is an effective technique to support large-scale multimedia retrieval, due to its capability of encoding heterogeneous multi-modal features into compact and similarity-preserving binary codes. Although great progress has been achieved so far, existing methods still suffer from several problems, including: 1) All existing methods simply adopt fixed modality combination weights in online hashing process to generate the query hash codes. This strategy cannot adaptively capture the variations of different queries. 2) They either suffer from insufficient semantics (for unsupervised methods) or require high computation and storage cost (for the supervised methods, which rely on pair-wise semantic matrix). 3) They solve the hash codes with relaxed optimization strategy or bit-by-bit discrete optimization, which results in significant quantization loss or consumes considerable computation time. To address the above limitations, in this paper, we propose an Online Multi-modal Hashing with Dynamic Query-adaption (OMH-DQ) method in a novel fashion. Specifically, a self-weighted fusion strategy is designed to adaptively preserve the multi-modal feature information into hash codes by exploiting their complementarity. The hash codes are learned with the supervision of pair-wise semantic labels to enhance their discriminative capability, while avoiding the challenging symmetric similarity matrix factorization. Under such learning framework, the binary hash codes can be directly obtained with efficient operations and without quantization errors. Accordingly, our method can benefit from the semantic labels, and simultaneously, avoid the high computation complexity. Moreover, to accurately capture the query variations, at the online retrieval stage, we design a parameter-free online hashing module which can adaptively learn the query hash codes according to the dynamic query contents. Extensive experiments demonstrate the state-of-the-art performance of the proposed approach from various aspects.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331217"}, {"primary_key": "3115072", "vector": [], "sparse_vector": [], "title": "PSGAN: A Minimax Game for Personalized Search with Limited and Noisy Click Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Personalized search aims to adapt document ranking to user's personal interests. Traditionally, this is done by extracting click and topical features from historical data in order to construct a user profile. In recent years, deep learning has been successfully used in personalized search due to its ability of automatic feature learning. However, the small amount of noisy personal data poses challenges to deep learning models to learn the personalized classification boundary between relevant and irrelevant results. In this paper, we propose PSGAN, a Generative Adversarial Network (GAN) framework for personalized search. By means of adversarial training, we enforce the model to pay more attention to training data that are difficult to distinguish. We use the discriminator to evaluate personalized relevance of documents and use the generator to learn the distribution of relevant documents. Two alternative ways to construct the generator in the framework are tested: based on the current query or based on a set of generated queries. Experiments on data from a commercial search engine show that our models can yield significant improvements over state-of-the-art models.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331218"}, {"primary_key": "3115073", "vector": [], "sparse_vector": [], "title": "Answering Complex Questions by Joining Multi-Document Evidence with Quasi Knowledge Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Direct answering of questions that involve multiple entities and relations is a challenge for text-based QA. This problem is most pronounced when answers can be found only by joining evidence from multiple documents. Curated knowledge graphs (KGs) may yield good answers, but are limited by their inherent incompleteness and potential staleness. This paper presents QUEST, a method that can answer complex questions directly from textual sources on-the-fly, by computing similarity joins over partial results from different documents. Our method is completely unsupervised, avoiding training-data bottlenecks and being able to cope with rapidly evolving ad hoc topics and formulation style in user questions. QUEST builds a noisy quasi KG with node and edge weights, consisting of dynamically retrieved entity names and relational phrases. It augments this graph with types and semantic alignments, and computes the best answers by an algorithm for Group Steiner Trees. We evaluate QUEST on benchmarks of complex questions, and show that it substantially outperforms state-of-the-art baselines.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331252"}, {"primary_key": "3115074", "vector": [], "sparse_vector": [], "title": "Effects of User Negative Experience in Mobile News Streaming.", "authors": ["Hongyu Lu", "<PERSON>", "Weizhi Ma", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online news streaming services have been one of the major information acquisition resources for mobile users. In many cases, users click an article but find it cannot satisfy or even annoy them. Intuitively, these negative experiences will affect users' behaviors and satisfaction, but such effects have not been well understood. In this work, a retrospective analysis is conducted using real users' log data, containing user's explicit feedback of negative experiences, from a commercial news streaming application. Through multiple intra-session comparison experiments, we find that in current session, users will spend less time reading the content, lose activeness and leave sooner after having negative experiences. Later return and significant changes of user behaviors in the next session are also observed, which demonstrates the existence of inter-session effects of negative experiences.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331247"}, {"primary_key": "3115075", "vector": [], "sparse_vector": [], "title": "Learning to Rank in Theory and Practice: From Gradient Boosting to Neural Networks and Unbiased Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This tutorial aims to weave together diverse strands of modern Learning to Rank (LtR) research, and present them in a unified full-day tutorial. First, we will introduce the fundamentals of LtR, and an overview of its various sub-fields. Then, we will discuss some recent advances in gradient boosting methods such as LambdaMART by focusing on their efficiency/effectiveness trade-offs and optimizations. Subsequently, we will then present TF-Ranking, a new open source TensorFlow package for neural LtR models, and how it can be used for modeling sparse textual features. Finally, we will conclude the tutorial by covering unbiased LtR -- a new research field aiming at learning from biased implicit user feedback. The tutorial will consist of three two-hour sessions, each focusing on one of the topics described above. It will provide a mix of theoretical and hands-on sessions, and should benefit both academics interested in learning more about the current state-of-the-art in LtR, as well as practitioners who want to use LtR techniques in their applications.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3334824"}, {"primary_key": "3115076", "vector": [], "sparse_vector": [], "title": "KANDINSKY: Abstract Art-Inspired Visualization of Social Discussions.", "authors": ["<PERSON>", "So<PERSON>v S<PERSON>", "<PERSON>"], "summary": "Many social media sites allow users to upload text, images, and videos (collectively referred to as<PERSON><PERSON> post) for public consumption. These posts may attract hundreds of comments from many social users leading to social conversations (ie discussions). Tools that can facilitate user-friendly and effective understanding and analysis of large volumes of comments associated with anchor posts can be of great benefit to individuals and organizations. In this demonstration, we present a novel end-to-end visualization system called <PERSON><PERSON><PERSON> to supportmulti-faceted visualization of social discussions associated with an anchor post. In Kandinsky, the social discussion landscape is visualized using a collection of colorfulcircles andconcentric circles, which are inspired from the famous abstract arts called\"Squares with Concentric Circles\" and\"Several Circles\" by Russian painter <PERSON><PERSON><PERSON> (1866-1944). Intuitively, a circle and a concentric circle represent a social comment and a collection of comments in a discussion thread, respectively. We discuss various innovative features of <PERSON><PERSON><PERSON> and demonstrate its effectiveness.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331411"}, {"primary_key": "3115077", "vector": [], "sparse_vector": [], "title": "A Horizontal Patent Test Collection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We motivate the need for, and describe the contents of a novel patent research collection, publicly available and for free, covering multimodal and multilingual data from six patent authorities. The new patent test collection complements existing patent test collections, which are vertical (one domain or one authority over many years). Instead, the new collection is horizontal: it includes all technical domains from the major patenting authorities over the relatively short time span of two years. In addition to bringing together documents currently scattered across different test collections, the collection provides, for the first time, Korean documents, to complement those from Europe, US, Japan, and China. This new collection can be used on a variety of tasks beyond traditional information retrieval. We exemplify this with a task of high-relevance today: de-anonymisation.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331346"}, {"primary_key": "3115078", "vector": [], "sparse_vector": [], "title": "Hot Topic-Aware Retweet Prediction with Masked Self-attentive Model.", "authors": ["<PERSON><PERSON> Ma", "Xiangkun Hu", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yu-Gang <PERSON>"], "summary": "Social media users create millions of microblog entries on various topics each day. Retweet behaviour play a crucial role in spreading topics on social media. Retweet prediction task has received considerable attention in recent years. The majority of existing retweet prediction methods are focus on modeling user preference by utilizing various information, such as user profiles, user post history, user following relationships, etc. Yet, the users exposures towards real-time posting from their followees contribute significantly to making retweet predictions, considering that the users may participate into the hot topics discussed by their followees rather than be limited to their previous interests. To make efficient use of hot topics, we propose a novel masked self-attentive model to perform the retweet prediction task by perceiving the hot topics discussed by the users' followees. We incorporate the posting histories of users with external memory and utilize a hierarchical attention mechanism to construct the users' interests. Hence, our model can be jointly hot-topic aware and user interests aware to make a final prediction. Experimental results on a dataset collected from Twitter demonstrated that the proposed method can achieve better performance than state-of-the-art methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331236"}, {"primary_key": "3115079", "vector": [], "sparse_vector": [], "title": "π-Net: A Parallel Information-sharing Network for Shared-account Cross-domain Sequential Recommendations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sequential Recommendation (SR) is the task of recommending the next item based on a sequence of recorded user behaviors. We study SR in a particularly challenging context, in which multiple individual users share a single account (shared-account) and in which user behaviors are available in multiple domains (cross-domain). These characteristics bring new challenges on top of those of the traditional SR task. On the one hand, we need to identify different user behaviors under the same account in order to recommend the right item to the right user at the right time. On the other hand, we need to discriminate the behaviors from one domain that might be helpful to improve recommendations in the other domains.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331200"}, {"primary_key": "3115081", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>, Can You Help Me Shop?", "authors": ["<PERSON><PERSON>"], "summary": "In a previous keynote address [1], we have described how voice-enabled intelligent assistants are revolutionizing the way humans interact with machines and how their ubiquitous presence in homes, cars, offices are taking us closer to the old dream of ambient computing. We also explained how <PERSON><PERSON>, Amazon's intelligent assistant, is pioneering the domain of voice shopping, considering the customer's shopping journey in a holistic manner. In this talk, we will discuss in more details the key stages required in addressing customer's needs, namely (1) understanding customers, (2) satisfying their needs, and (3) predicting them. We will also provide a finer analysis of customers' needs, be they informational, transactional or navigational needs [2]. We will conclude by presenting some associated research challenges and encouraging the community to explore the unchartered area of voice-enabled product discovery.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331443"}, {"primary_key": "3115082", "vector": [], "sparse_vector": [], "title": "Ontology-Aware Clinical Abstractive Summarization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Automatically generating accurate summaries from clinical reports could save a clinician's time, improve summary coverage, and reduce errors. We propose a sequence-to-sequence abstractive summarization model augmented with domain-specific ontological information to enhance content selection and summary generation. We apply our method to a dataset of radiology reports and show that it significantly outperforms the current state-of-the-art on this task in terms of rouge scores. Extensive human evaluation conducted by a radiologist further indicates that this approach yields summaries that are less likely to omit important details, without sacrificing readability or accuracy.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331319"}, {"primary_key": "3115083", "vector": [], "sparse_vector": [], "title": "CEDR: Contextualized Embeddings for Document Ranking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although considerable attention has been given to neural ranking architectures recently, far less attention has been paid to the term representations that are used as input to these models. In this work, we investigate how two pretrained contextualized language models (ELMo and BERT) can be utilized for ad-hoc document ranking. Through experiments on TREC benchmarks, we find that several ex-sting neural ranking architectures can benefit from the additional context provided by contextualized language models. Furthermore, we propose a joint approach that incorporates BERT's classification vector into existing neural models and show that it outperforms state-of-the-art ad-hoc ranking baselines. We call this joint approach CEDR (Contextualized Embeddings for Document Ranking). We also address practical challenges in using these models for ranking, including the maximum input length imposed by BERT and runtime performance impacts of contextualized language models.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331317"}, {"primary_key": "3115084", "vector": [], "sparse_vector": [], "title": "Content-Based Weak Supervision for Ad-Hoc Re-Ranking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "One challenge with neural ranking is the need for a large amount of manually-labeled relevance judgments for training. In contrast with prior work, we examine the use of weak supervision sources for training that yield pseudo query-document pairs that already exhibit relevance (e.g., newswire headline-content pairs and encyclopedic heading-paragraph pairs). We also propose filtering techniques to eliminate training samples that are too far out of domain using two techniques: a heuristic-based approach and novel supervised filter that re-purposes a neural ranker. Using several leading neural ranking architectures and multiple weak supervision datasets, we show that these sources of training pairs are effective on their own (outperforming prior weak supervision techniques), and that filtering can further improve performance.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331316"}, {"primary_key": "3115085", "vector": [], "sparse_vector": [], "title": "Looking for Opportunities: Challenges in Procurement Search.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Procurement legislation stipulates that information about the goods, services, or works, that tax-funded authorities wish to purchase are made publicly available in a procurement contract notice. However, for businesses wishing to tender for such competitive opportunities, finding relevant procurement contract notices presents a challenging professional search task. In this talk, we will provide an overview of procurement search and then describe the challenges in addressing the related search and recommendation tasks.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331428"}, {"primary_key": "3115086", "vector": [], "sparse_vector": [], "title": "Addressing Vocabulary Gap in E-commerce Search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "E-commerce customers express their purchase intents in several ways, some of which may use a different vocabulary than that of the product catalog. For example, the intent for \"women maternity gown\" is often expressed with the query, \"ladies pregnancy dress\". Search engines typically suffer from poor performance on such queries because of low overlap between query terms and specifications of the desired products. Past work has referred to these queries as vocabulary gap queries. In our experiments, we show that our technique significantly outperforms strong baselines and also show its real-world effectiveness with an online A/B experiment.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331323"}, {"primary_key": "3115089", "vector": [], "sparse_vector": [], "title": "A Pipeline for Disaster Response and Relief Coordination.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Natural disasters such as floods, forest fires, and hurricanes can cause catastrophic damage to human life and infrastructure. We focus on response to hurricanes caused by both river water flooding and storm surge. Using models for storm surge simulation and flood extent prediction, we generate forecasts about areas likely to be highly affected by the disaster. Further, we overlay the simulation results with information about traffic incidents to correlate traffic incidents with other data modality. We present these results in a modularized, interactive map-based visualization, which can help emergency responders to better plan and coordinate disaster response.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331405"}, {"primary_key": "3115090", "vector": [], "sparse_vector": [], "title": "WestSearch Plus: A Non-factoid Question-Answering System for the Legal Domain.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a non-factoid QA system that provides legally accurate, jurisdictionally relevant, and conversationally responsive answers to user-entered questions in the legal domain. This commercially available system is entirely based on NLP and IR, and does not rely on a structured knowledge base. WestSearch Plus aims to provide concise one sentence answers for basic questions about the law. It is not restricted in scope to particular topics or jurisdictions. The corpus of potential answers contains approximately 22M documents classified to over 120K legal topics.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331397"}, {"primary_key": "3115091", "vector": [], "sparse_vector": [], "title": "Non-factoid Question Answering in the Legal Domain.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Non-factoid question answering in the legal domain must provide legally correct, jurisdictionally relevant, and conversationally responsive answers to user-entered questions. We present work done on a QA system that is entirely based on IR and NLP, and does not rely on a structured knowledge base. Our system retrieves concise one-sentence answers for basic questions about the law. It is not restricted in scope to particular topics or jurisdictions. The corpus of potential answers contains approximately 22M documents classified to over 120K legal topics.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331431"}, {"primary_key": "3115092", "vector": [], "sparse_vector": [], "title": "Demonstrating Requirement Search on a University Degree Search Application.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In many domains of information retrieval, we are required to retrieve documents that describe requirements on a predefined set of terms. A requirement is a relationship between a set of terms and the document. As requirements become more complex by catering for optional, alternative, and combinations of terms, efficiently retrieving documents becomes more challenging due to the exponential size of the search space. In this paper, we propose RevBoMIR, which utilizes a modified Boolean Model for Information Retrieval to retrieve requirements-based documents without sacrificing the expressiveness of requirements. Our proposed approach is particularly useful in domains where documents embed criteria that can be satisfied by mandatory, alternative or disqualifying terms to determine its retrieval. Finally, we present a graph model for representing document requirements, and demonstrate Requirement Search via a university degree search application.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331402"}, {"primary_key": "3115093", "vector": [], "sparse_vector": [], "title": "Understanding the Interpretability of Search Result Summaries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We examine the interpretability of search results in current web search engines through a lab user study. Particularly, we evaluate search result summary as an interpretable technique that informs users why the system retrieves a result and to which extent the result is useful. We collected judgments about 1,252 search results from 40 users in 160 sessions. Experimental results indicate that the interpretability of a search result summary is a salient factor influencing users' click decisions. Users are less likely to click on a result link if they do not understand why it was retrieved (low transparency) or cannot assess if the result would be useful based on the summary (low assessability). Our findings suggest it is crucial to improve the interpretability of search result summaries and develop better techniques to explain retrieved results to search engine users.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331306"}, {"primary_key": "3115094", "vector": [], "sparse_vector": [], "title": "Bridging Gaps: Predicting User and Task Characteristics from Partial User Information.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Interactive information retrieval (IIR) researchers often conduct laboratory studies to understand the relationship between people seeking information and information retrieval systems. They develop extensive data collection methods and tools create new understanding about the relationship between observable behaviors, searcher context, and underlying cognition, to better support people's information seeking. Yet aside from the problems of data size, realism, and demographics, laboratory studies are limited in the number and nature of phenomena they can study. Hence, data collected in laboratories contains different searcher populations and collects non-overlapping user and task characteristics. While research analyses and collection methods are isolated, how can we further IIR's mission of broad understanding? We approach this as a structure learning problem on incomplete data, determining the extent to which incomplete data can be used to predict user and task characteristics from interactions. In particular, we examine whether combining heterogeneous data sets is more effective than using a single data set alone in prediction. Our results indicate that adding external data significantly improves predictions of searcher characteristics, task characteristics, and behaviors, even when the data does not contain identical information about searchers.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331221"}, {"primary_key": "3115096", "vector": [], "sparse_vector": [], "title": "Learning to Quantify: Estimating Class Prevalence via Supervised Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Quantification (also known as \"supervised prevalence estimation\" [2], or \"class prior estimation\" [7]) is the task of estimating, given a set σ of unlabelled items and a set of classes C = c1, . . . , c |C| , the relative frequency (or \"prevalence\") p(ci ) of each class ci C, i.e., the fraction of items in σ that belong to ci . When each item belongs to exactly one class, since 0 ≤ p(ci ) ≤ 1 and Í ci C p(ci ) = 1, p is a distribution of the items in σ across the classes in C (the true distribution), and quantification thus amounts to estimating p (i.e., to computing a predicted distribution p?).", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331389"}, {"primary_key": "3115098", "vector": [], "sparse_vector": [], "title": "Automatic Curation of Content Tables for Educational Videos.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional forms of education are increasingly being replaced by online forms of learning. With many degrees being awarded without the requirement of co-location, it becomes necessary to build tools to enhance online learning interfaces. Online educational videos are often long and do not have enough metadata. Viewers trying to learn about a particular topic have to go through the entire video to find suitable content. We present a novel architecture to curate content tables for educational videos. We harvest text and acoustic properties of the videos to form a hierarchical content table (similar to a table of contents available in a textbook). We allow users to browse the video smartly by skipping to a particular portion rather than going through the entire video. We consider other text-based approaches as our baselines. We find that our approach beats the macro F1-score and micro F1-score of baseline by 39.45% and 35.76% respectively. We present our demo as an independent web page where the user can paste the URL of the video to obtain a generated hierarchical table of contents and navigate to the required content. In the spirit of reproducibility, we make our code public at https://goo.gl/Qzku9d and provide a screen cast to be viewed at https://goo.gl/4HSV1v.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331400"}, {"primary_key": "3115099", "vector": [], "sparse_vector": [], "title": "Fast Approximate Filtering of Search Results Sorted by Attribute.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Several Web search services enable their users with the possibility of sorting the list of results by a specific attribute, e.g., sort \"by price\" in e-commerce. However, sorting the results by attribute could bring marginally relevant results in the top positions thus leading to a poor user experience. This motivates the definition of the relevance-aware filtering problem. This problem asks to remove results from the attribute-sorted list to maximize its final overall relevance. Recently, an optimal solution to this problem has been proposed. However, it has strong limitations in the Web scenario due to its high computational cost. In this paper, we propose ϵ-Filtering: an efficient approximate algorithm with strong approximation guarantees on the relevance of the final list. More precisely, given an allowed approximation error ϵ, the proposed algorithm finds a(1-ϵ)\"optimal filtering, i.e., the relevance of its solution is at least (1-ϵ) times the optimum. We conduct a comprehensive evaluation of ϵ-Filtering against state-of-the-art competitors on two real-world public datasets. Experiments show that ϵ-Filtering achieves the desired levels of effectiveness with a speedup of up to two orders of magnitude with respect to the optimal solution while guaranteeing very small approximation errors.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331227"}, {"primary_key": "3115100", "vector": [], "sparse_vector": [], "title": "Analyzing and Predicting News Popularity in an Instant Messaging Service.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "With widespread use of mobile devices, instant messaging (IM) services have recently attracted a great deal of attention by millions of users. This has motivated news agencies to share their contents via such platforms in addition to their websites and popular social media. As a result, thousands of users nowadays follow the news agencies through their verified channels in IM services. However, user interactions with such platforms is relatively unstudied. In this paper, we provide an initial study to analyze and predict news popularity in an instant messaging service. To this aim, we focus on Telegram, a popular IM service with 200 million monthly active users. We explore the differences between news popularity analysis in Telegram and typical social media, such as Twitter, and highlight its unique characteristics. We perform our analysis on the data we collected from four diverse news agencies. Following our analysis, we study the task of news popularity prediction in Telegram and show that the performance of the prediction models can be substantially improved by learning from the data of multiple news agencies using multi-task learning. To foster research in this area, we have made the collected data publicly available.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331301"}, {"primary_key": "3115101", "vector": [], "sparse_vector": [], "title": "An Experimentation Platform for Precision Medicine.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Precision medicine - where data from patients, their genes, their lifestyles and the available treatments and their combination are taken into account for finding a suitable treatment - requires searching the biomedical literature and other resources such as clinical trials with the patients' information. The retrieved information could then be used in curating data for clinicians for decision-making. We present information retrieval researchers with an on-line system which enables experimentation in search for precision medicine within the framework provided by the TREC Precision Medicine (PM) track. A number of query and document processing and ranking approaches are provided. These include some ofthe most promising gene mention expansion methods, as well as learning-to-rank using neural networks.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331396"}, {"primary_key": "3115104", "vector": [], "sparse_vector": [], "title": "Workshop on Fairness, Accountability, Confidentiality, Transparency, and Safety in Information Retrieval (FACTS-IR).", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This workshop explores challenges in responsible information retrieval system development and deployment. The focus is on determining actionable research agendas on five key dimensions of responsible information retrieval: fairness, accountability, confidentiality, transparency, and safety. Rather than just a mini-conference, this workshop is an event during which participants are expected to work. The workshop brings together a diverse set of researchers and practitioners interested in contributing to the development of a technical research agenda for responsible information retrieval.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331644"}, {"primary_key": "3115105", "vector": [], "sparse_vector": [], "title": "TrecTools: an Open-source Python Library for Information Retrieval Practitioners Involved in TREC-like Campaigns.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces TrecTools, a Python library for assisting Information Retrieval (IR) practitioners with TREC-like campaigns. IR practitioners tasked with activities like building test collections, evaluating systems, or analysing results from empirical experiments commonly have to resort to use a number of different software tools and scripts that each perform an individual functionality - and at times they even have to implement ad-hoc scripts of their own. TrecTools aims to provide a unified environment for performing these common activities.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331399"}, {"primary_key": "3115106", "vector": [], "sparse_vector": [], "title": "Warm Up Cold-start Advertisements: Improving CTR Predictions via Learning to Learn ID Embeddings.", "authors": ["Feiyang Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Pingz<PERSON> Tang", "<PERSON> He"], "summary": "Click-through rate (CTR) prediction has been one of the most central problems in computational advertising. Lately, embedding techniques that produce low-dimensional representations of ad IDs drastically improve CTR prediction accuracies. However, such learning techniques are data demanding and work poorly on new ads with little logging data, which is known as the cold-start problem.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331268"}, {"primary_key": "3115108", "vector": [], "sparse_vector": [], "title": "Accelerated Query Processing Via Similarity Score Prediction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Processing top-k bag-of-words queries is critical to many information retrieval applications, including web-scale search. In this work, we consider algorithmic properties associated with dynamic pruning mechanisms. Such algorithms maintain a score threshold (the k th highest similarity score identified so far) so that low-scoring documents can be bypassed, allowing fast top-k retrieval with no loss in effectiveness. In standard pruning algorithms the score threshold is initialized to the lowest possible value. To accelerate processing, we make use of term- and query-dependent features to predict the final value of that threshold, and then employ the predicted value right from the commencement of processing. Because of the asymmetry associated with prediction errors (if the estimated threshold is too high the query will need to be re-executed in order to assure the correct answer), the prediction process must be risk-sensitive. We explore techniques for balancing those factors, and provide detailed experimental results that show the practical usefulness of the new approach.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331207"}, {"primary_key": "3115114", "vector": [], "sparse_vector": [], "title": "Argument Search: Assessing Argument Relevance.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We report on the first user study on assessing argument relevance. Based on a search among more than 300,000 arguments, four standard retrieval models are compared on 40 topics for 20 controversial issues: every issue has one topic with a biased stance and another neutral one. Following TREC, the top results of the different models on a topic were pooled and relevance-judged by one assessor per topic. The assessors also judged the arguments' rhetorical, logical, and dialectical quality, the results of which were cross-referenced with the relevance judgments. Furthermore, the assessors were asked for their personal opinion, and whether it matched the predefined stance of a topic. Among other results, we find that Terrier's implementations of DirichletLM and DPH are on par, significantly outperforming TFIDF and BM25. The judgments of relevance and quality hardly correlate, giving rise to a more diverse set of ranking criteria than relevance alone. We did not measure a significant bias of assessors when their stance is at odds with a topic's stance.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331327"}, {"primary_key": "3115115", "vector": [], "sparse_vector": [], "title": "Vertical Search Blending: A Real-world Counterfactual Dataset.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Blending of search results from several vertical sources became standard among web search engines. Similar scenarios appear in computational advertising, news recommendation, and other interactive systems. As such environments give only partial feedback, the evaluation of new policies conventionally requires expensive online A/B tests. Counterfactual approach is a promising alternative, nevertheless, it requires specific conditions for a valid off-policy evaluation. We release a large-scale, real-world vertical-blending dataset gathered bySeznam.cz web search engine. The dataset contains logged partial feedback with the corresponding propensity and is thus suited for counterfactual evaluation. We provide basic checks for validity and evaluate several learning methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331345"}, {"primary_key": "3115116", "vector": [], "sparse_vector": [], "title": "BERT with History Answer Embedding for Conversational Question Answering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Conversational search is an emerging topic in the information retrieval community. One of the major challenges to multi-turn conversational search is to model the conversation history to answer the current question. Existing methods either prepend history turns to the current question or use complicated attention mechanisms to model the history. We propose a conceptually simple yet highly effective approach referred to as history answer embedding. It enables seamless integration of conversation history into a conversational question answering (ConvQA) model built on BERT (Bidirectional Encoder Representations from Transformers). We first explain our view that ConvQA is a simplified but concrete setting of conversational search, and then we provide a general framework to solve ConvQA. We further demonstrate the effectiveness of our approach under this framework. Finally, we analyze the impact of different numbers of history turns under different settings to provide new insights into conversation history modeling in ConvQA.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331341"}, {"primary_key": "3115118", "vector": [], "sparse_vector": [], "title": "A Lightweight Representation of News Events on Social Media.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The sheer amount of newsworthy information published by users in social media platforms makes it necessary to have efficient and effective methods to filter and organize content. In this scenario, off-the-shelf methods fail to process large amounts of data, which is usually approached by adding more computational resources. Simple data aggregations can help to cope with space and time constraints, while at the same time improve the effectiveness of certain applications, such as topic detection or summarization. We propose a lightweight representation of newsworthy social media data. The proposed representation leverages microblog features, such as redundancy and re-sharing capabilities, by using surrogate texts from shared URLs and word embeddings. Our representation allows us to achieve comparable clustering results to those obtained by using the complete data, while reducing running time and required memory. This is useful when dealing with noisy and raw user-generated social media data.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331300"}, {"primary_key": "3115119", "vector": [], "sparse_vector": [], "title": "Adversarial Training for Review-Based Recommendations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent studies have shown that incorporating users' reviews into the collaborative filtering strategy can significantly boost the recommendation accuracy. A pressing challenge resides on learning how reviews influence users' rating behaviors. In this paper, we propose an Adversarial Training approach for Review-based recommendations, namely ATR. We design a neural architecture of sequence-to-sequence learning to calculate the deep representations of users' reviews on items following an adversarial training strategy. At the same time we jointly learn to factorize the rating matrix, by regularizing the deep representations of reviews with the user and item latent features. In doing so, our model captures the non-linear associations among reviews and ratings while producing a review for each user-item pair. Our experiments on publicly available datasets demonstrate the effectiveness of the proposed model, outperforming other state-of-the-art methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331313"}, {"primary_key": "3115120", "vector": [], "sparse_vector": [], "title": "Time-Limits and Summaries for Faster Relevance Assessing.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Relevance assessing is a critical part of test collection construction as well as applications such as high-recall retrieval that require large amounts of relevance feedback. In these applications, tens of thousands of relevance assessments are required and assessing costs are directly related to the speed at which assessments are made. We conducted a user study with 60 participants where we investigated the impact of time limits (15, 30, and 60 seconds) and document size (full length vs. short summaries) on relevance assessing. Participants were shown either full documents or document summaries that they had to judge within a 15, 30, or 60 seconds time constraint per document. We found that using a time limit as short as 15 seconds or judging document summaries in place of full documents could significantly speed judging without significantly affecting judging quality. Participants found judging document summaries with a 60 second time limit to be the easiest and best experience of the six conditions. While time limits may speed judging, the same speed benefits can be had with high quality document summaries while providing an improved judging experience for assessors.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331270"}, {"primary_key": "3115121", "vector": [], "sparse_vector": [], "title": "Ghosting: Contextualized Query Auto-Completion on Amazon Search.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Query auto-completion presents a ranked list of search queries as suggestions for a customer-entered prefix. Ghosting is the process of auto-completing a search recommendation by highlighting the suggested text inline i.e., within the search box. We present a behavioral recommendation model that uses customer search context to ghost on high-confidence queries. We tested ghosting on over 140 million search sessions. Session-context ghosting increased the acceptance of offered suggestions by 6.18%, reduced misspelled searches by 4.42% and improved net sales by 0.14%.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331432"}, {"primary_key": "3115122", "vector": [], "sparse_vector": [], "title": "WCIS 2019: 1st Workshop on Conversational Interaction Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The first workshop on Conversational Interaction Systems is held in Paris, France on July 25th, 2019, co-located with the ACM Special Interest Group on Information Retrieval (SIGIR). The goal of the workshop is to bring together researchers from academia and industry to discuss the challenges and future of conversational agents and interactive systems. The workshop has an exciting program that spans a number of subareas including: multi-modal conversational interfaces, dialogue accessibility, and scaling such systems. The program includes eight invited talks, a lively panel discussion on emerging topics, and presentation of original research papers.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331648"}, {"primary_key": "3115123", "vector": [], "sparse_vector": [], "title": "Lifelong Sequential Modeling with Personalized Memorization for User Response Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "User response prediction, which models the user preference w.r.t. the presented items, plays a key role in online services. With two-decade rapid development, nowadays the cumulated user behavior sequences on mature Internet service platforms have become extremely long since the user's first registration. Each user not only has intrinsic tastes, but also keeps changing her personal interests during lifetime. Hence, it is challenging to handle such lifelong sequential modeling for each individual user. Existing methodologies for sequential modeling are only capable of dealing with relatively recent user behaviors, which leaves huge space for modeling long-term especially lifelong sequential patterns to facilitate user modeling. Moreover, one user's behavior may be accounted for various previous behaviors within her whole online activity history, i.e., long-term dependency with multi-scale sequential patterns. In order to tackle these challenges, in this paper, we propose a Hierarchical Periodic Memory Network for lifelong sequential modeling with personalized memorization of sequential patterns for each user. The model also adopts a hierarchical and periodical updating mechanism to capture multi-scale sequential patterns of user interests while supporting the evolving user behavior logs. The experimental results over three large-scale real-world datasets have demonstrated the advantages of our proposed model with significant improvement in user response prediction performance against the state-of-the-arts.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331230"}, {"primary_key": "3115124", "vector": [], "sparse_vector": [], "title": "On Tradeoffs Between Document Signature Methods for a Legal Due Diligence Corpus.", "authors": ["<PERSON>", "<PERSON>"], "summary": "While document signatures are a well established tool in IR, they have primarily been investigated in the context of web documents. Legal due diligence documents, by their nature, have more similar structure and language than we may expect out of standard web collections. Moreover, many due diligence systems strive to facilitate real-time interactions and so time from document ingestion to availability should be minimal. Such constraints further limit the possible solution space when identifying near duplicate documents. We present an examination of the tradeoffs that document signature methods face in the due diligence domain. In particular, we quantify the trade-off between signature length, time to compute, number of hash collisions, and number of nearest neighbours for a 90,000 document due diligence corpus.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331311"}, {"primary_key": "3115125", "vector": [], "sparse_vector": [], "title": "Normalized Query Commitment Revisited.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We revisit the Normalized Query Commitment (NQC) query performance prediction (QPP) method. To this end, we suggest a scaled extension to a discriminative QPP framework and use it to analyze NQC. Using this analysis allows us to redesign NQC and suggest several options for improvement.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331334"}, {"primary_key": "3115126", "vector": [], "sparse_vector": [], "title": "Query Performance Prediction for Pseudo-Feedback-Based Retrieval.", "authors": ["<PERSON><PERSON><PERSON>", "Oren <PERSON>"], "summary": "The query performance prediction task (QPP) is estimating retrieval effectiveness in the absence of relevance judgments. Prior work has focused on prediction for retrieval methods based on surface level query-document similarities (e.g., query likelihood). We address the prediction challenge for pseudo-feedback-based retrieval methods which utilize an initial retrieval to induce a new query model; the query model is then used for a second (final) retrieval. Our suggested approach accounts for the presumed effectiveness of the initially retrieved list, its similarity with the final retrieved list and properties of the latter. Empirical evaluation demonstrates the clear merits of our approach.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331369"}, {"primary_key": "3115127", "vector": [], "sparse_vector": [], "title": "An Axiomatic Approach to Regularizing Neural Ranking Models.", "authors": ["Corby Rosset", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Axiomatic information retrieval (IR) seeks a set of principle properties desirable in IR models. These properties when formally expressed provide guidance in the search for better relevance estimation functions. Neural ranking models typically contain many learnable parameters. The training of these models involves a search for appropriate parameter values based on large quantities of labeled examples. Intuitively, axioms that can guide the search for better traditional IR models should also help in better parameter estimation for machine learning based rankers. This work explores the use of IR axioms to augment the direct supervision from labeled data for training neural ranking models. We modify the documents in our dataset along the lines of well-known axioms during training and add a regularization loss based on the agreement between the ranking model and the axioms on which version of the document---the original or the perturbed---should be preferred. Our experiments show that the neural ranking model achieves faster convergence and better generalization with axiomatic regularization.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331296"}, {"primary_key": "3115128", "vector": [], "sparse_vector": [], "title": "Selecting Discriminative Terms for Relevance Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pseudo-relevance feedback based on the relevance model does not take into account the inverse document frequency of candidate terms when selecting expansion terms. As a result, common terms are often included in the expanded query constructed by this model. We propose three possible extensions of the relevance model that address this drawback. Our proposed extensions are simple to compute and are independent of the base retrieval model. Experiments on several TREC news and web collections show that the proposed modifications yield significantly better MAP, precision, NDCG, and recall values than the original relevance model as well as its two recently proposed state-of-the-art variants.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331357"}, {"primary_key": "3115129", "vector": [], "sparse_vector": [], "title": "An Open-Access Platform for Transparent and Reproducible Structured Searching.", "authors": ["<PERSON>-<PERSON>", "<PERSON>"], "summary": "Knowledge workers such as patent agents, recruiters and legal researchers undertake work tasks in which search forms a core part of their duties. In these instances, the search task often involves formulation of complex queries expressed as Boolean strings. However, creating effective Boolean queries remains an ongoing challenge, often compromised by errors and inefficiencies. In this paper, we demonstrate a new approach to structured searching in which concepts are expressed as objects on a two-dimensional canvas. Interactive query suggestions are provided via an NLP services API, and support is offered for optimising, translating and sharing search strategies as executable artefacts. This eliminates many sources of error, makes the query semantics more transparent, and offers an open-access platform for sharing reproducible search strategies and best practices.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331394"}, {"primary_key": "3115130", "vector": [], "sparse_vector": [], "title": "Which Diversity Evaluation Measures Are &quot;Good&quot;?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This study evaluates 30 IR evaluation measures or their instances, of which nine are for adhoc IR and 21 are for diversified IR, primarily from the viewpoint of whether their preferences of one SERP (search engine result page) over another actually align with users' preferences. The gold preferences were contructed by hiring 15 assessors, who independently examined 1,127 SERP pairs and made preference assessments. Two sets of preference assessments were obtained: one based on a relevance question \"Which SERP is more relevant?'' and the other based on a diversity question \"Which SERP is likely to satisfy a higher number of users?'' To our knowledge, our study is the first to have collected diversity preference assessments in this way and evaluated diversity measures successfully. Our main results are that (a) Popular adhoc IR measures such as nDCG actually align quite well with the gold relevance preferences; and that (b) While the ♯-measures align well with the gold diversity preferences, intent-aware measures perform relatively poorly. Moreover, as by-products of our analysis of existing evaluation measures, we define new adhoc measures called iRBU (intentwise Rank-Biased Utility) and EBR (Expected Blended Ratio); we demonstrate that an instance of iRBU performs as well as nDCG when compared to the gold relevance preferences. On the other hand, the original RBU, a recently-proposed diversity measure, underperforms the best ♯-measures when compared to the gold diversity preferences.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331215"}, {"primary_key": "3115131", "vector": [], "sparse_vector": [], "title": "FAQ Retrieval using Query-Question Similarity and BERT-Based Query-Answer Relevance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Frequently Asked Question (FAQ) retrieval is an important task where the objective is to retrieve an appropriate Question-Answer (QA) pair from a database based on a user's query. We propose a FAQ retrieval system that considers the similarity between a user's query and a question as well as the relevance between the query and an answer. Although a common approach to FAQ retrieval is to construct labeled data for training, it takes annotation costs. Therefore, we use a traditional unsupervised information retrieval system to calculate the similarity between the query and question. On the other hand, the relevance between the query and answer can be learned by using QA pairs in a FAQ database. The recently-proposed BERT model is used for the relevance calculation. Since the number of QA pairs in FAQ page is not enough to train a model, we cope with this issue by leveraging FAQ sets that are similar to the one in question. We evaluate our approach on two datasets. The first one is localgovFAQ, a dataset we construct in a Japanese administrative municipality domain. The second is StackExchange dataset, which is the public dataset in English. We demonstrate that our proposed method outperforms baseline methods on these datasets.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331326"}, {"primary_key": "3115132", "vector": [], "sparse_vector": [], "title": "Event Information Retrieval from Text.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Events are an integral part of our day-to-day search needs. Users search for various kinds of events such as political events, organizational announcements, policy changes, personal events, criminal activity and so on. In linguistics, events are often thought of as discourse entities with associated complex structure and attributes.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331415"}, {"primary_key": "3115133", "vector": [], "sparse_vector": [], "title": "A Domain-Independent and Multilingual Approach for Crisis Event Detection and Understanding.", "authors": ["<PERSON><PERSON>"], "summary": "Most existing approaches that use social media for detecting and characterizing emerging crisis events are based on the analysis of messages obtained from social platforms using a predetermined set of keywords [2, 3]. In addition to keyword filters, messages must commonly be post-processed using supervised classification models to determine if messages are referring to a real-time crisis situation or not. However, keyword-based approaches have certain shortcomings; on the one hand they require specific domain knowledge of different crisis events to determine a set of keywords to filter relevant data about an emerging crisis situation; on the other hand, they require supervised methods to determine if the identified data actually corresponds to a new real-time crisis event. Hence, the creation of keyword-independent methods could also help generalize existing approaches so they can be used for cross-lingual events, since each language and culture can have its own particular terms to refer to a same event. The majority of these works also explain phenomenons just for English messages. This limitation avoids replication of methodologies in other languages and countries where emergency events often occur. For this reason, researchers recently have focused on creating domain-independent and multi-lingual approaches for detecting and classifying social media messages during crisis events [1, 4]. These approaches have exploited low-level lexical features with the goal of reaching domain-transfer among different crisis events and languages. Nonetheless, most studies focused on crisis-related messages without testing non related crisis messages such as sporting events or music festivals.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331425"}, {"primary_key": "3115134", "vector": [], "sparse_vector": [], "title": "Jointly Modeling Relevance and Sensitivity for Search Among Sensitive Content.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Current search engines are designed to find what we want. But unprocessed archival collections can't be made available for search if they contain sensitive content that needs to be protected. Traditionally, content if first examined through a sensitivity review process, which becomes more difficult and time-consuming as content volumes increase. To mitigate these costs and delays, search technology should be capable of providing access to relevant content while protecting sensitive content. This paper proposes an approach that leverages learning to rank techniques. We use learning to rank to optimize a loss function that balances the value of finding relevant content with the imperative to protect sensitive content. In the experiments, a LETOR benchmark dataset, OHSUMED, is used with a subset of the MeSH labels representing the sensitive documents. Results show the efficacy of the proposed approach in comparison with some simpler baselines.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331256"}, {"primary_key": "3115137", "vector": [], "sparse_vector": [], "title": "Automatic Understanding of the Visual World.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "One of the central problems of artificial intelligence is machine perception, i.e., the ability to understand the visual world based on input from sensors such as cameras. In this talk, I will present recent progress of my team in this direction. I will start with presenting results on how to generate additional training data using weak annotations, motion information and synthetic data. Next, I will discuss our results for action recognition in videos, where human tubelets have shown to be successful. Our tubelet approach moves away from state-of-the-art frame based approaches and improves classification and localization by relying on joint information from several frames. We show how to extend this type of method to weakly supervised learning of actions, which allows us to scale to large amounts of data with sparse manual annotation. Finally, I will present recent work on grasping with a robot arm based on learning long-horizon manipulations with a hierarchy of RL and imitation-based skills.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3340264"}, {"primary_key": "3115138", "vector": [], "sparse_vector": [], "title": "Length-adaptive Neural Network for Answer Selection.", "authors": ["Taihua <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Answer selection focuses on selecting the correct answer for a question. Most previous work on answer selection achieves good performance by employing an RNN, which processes all question and answer sentences with the same feature extractor regardless of the sentence length. These methods often encounter the problem of long-term dependencies. To address this issue, we propose a Length-adaptive Neural Network (LaNN) for answer selection that can auto-select a neural feature extractor according to the length of the input sentence. In particular, we propose a flexible neural structure that applies a BiLSTM-based feature extractor for short sentences and a Transformer-based feature extractor for long sentences. To the best of our knowledge, LaNN is the first neural network structure that can auto-select the feature extraction mechanism based on the input. We quantify the improvements of LaNN against several competitive baselines on the public WikiQA dataset, showing significant improvements over the state-of-the-art.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331277"}, {"primary_key": "3115139", "vector": [], "sparse_vector": [], "title": "Privacy-aware Document Ranking with Neural Signals.", "authors": ["Jinjin Shao", "Shiyu <PERSON>", "<PERSON>"], "summary": "The recent work on neural ranking has achieved solid relevance improvement, by exploring similarities between documents and queries using word embeddings. It is an open problem how to leverage such an advancement for privacy-aware ranking, which is important for top K document search on the cloud. Since neural ranking adds more complexity in score computation, it is difficult to prevent the server from discovering embedding-based semantic features and inferring privacy-sensitive information. This paper analyzes the critical leakages in interaction-based neural ranking and studies countermeasures to mitigate such a leakage. It proposes a privacy-aware neural ranking scheme that integrates tree ensembles with kernel value obfuscation and a soft match map based on adaptively-clustered term closures. The paper also presents an evaluation with two TREC datasets on the relevance of the proposed techniques and the trade-offs for privacy and storage efficiency.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331189"}, {"primary_key": "3115140", "vector": [], "sparse_vector": [], "title": "Towards Context-Aware Evaluation for Image Search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Compared to general web search, image search engines present results in a significantly different way, which leads to changes in user behavior patterns, and thus creates challenges for the existing evaluation mechanisms. In this paper, we pay attention to the context factor in the image search scenario. On the basis of a mean-variance analysis, we investigate the effects of context and find that evaluation metrics align with user satisfaction better when the returned image results have high variance. Furthermore, assuming that the image results a user has examined might affect her following judgments, we propose the Context-Aware Gain (CAG), a novel evaluation metric that incorporates the contextual effects within the well-known gain-discount framework. Our experiment results show that, with a proper combination of discount functions, the proposed context-aware evaluation metric can significantly improve the performances of offline metrics for image search evaluation, considering user satisfaction as the golden standard.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331343"}, {"primary_key": "3115144", "vector": [], "sparse_vector": [], "title": "Controlling Risk of Web Question Answering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Web question answering (QA) has become an indispensable component in modern search systems, which can significantly improve users' search experience by providing a direct answer to users' information need. This could be achieved by applying machine reading comprehension (MRC) models over the retrieved passages to extract answers with respect to the search query. With the development of deep learning techniques, state-of-the-art MRC performances have been achieved by recent deep methods. However, existing studies on MRC seldom address the predictive uncertainty issue, i.e., how likely the prediction of an MRC model is wrong, leading to uncontrollable risks in real-world Web QA applications. In this work, we first conduct an in-depth investigation over the risk of Web QA. We then introduce a novel risk control framework, which consists of a qualify model for uncertainty estimation using the probe idea, and a decision model for selectively output. For evaluation, we introduce risk-related metrics, rather than the traditional EM and F1 in MRC, for the evaluation of risk-aware Web QA. The empirical results over both the real-world Web QA dataset and the academic MRC benchmark collection demonstrate the effectiveness of our approach.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331261"}, {"primary_key": "3115145", "vector": [], "sparse_vector": [], "title": "Ensembles of Recurrent Networks for Classifying the Relationship of Fake News Titles.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, everyone can create and publish news and information anonymously online. However, the credibility of such news and information are not guaranteed. To differentiate fake news from genuine news, one can compare a recent news with earlier posted ones. Identified suspicious news can be debunked to stop the fake news from spreading further. In this paper, we investigate the advantages of recurrent neural networks-based language representations (e.g., BERT, BiLSTM) in order to build ensemble classifiers that can accurately predict if one news title is related to, and, additionally disagrees with an earlier news title. Our experiments, on a dataset of 321k news titles created for the WSDM 2019 challenge, show that the BERT-based models significantly outperform BiLSTM, which in-turn significantly outperforms a simpler embedding-based representation. Furthermore, even the state-of-the-art BERT approach can be enhanced when combined with a simple BM25 feature.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331305"}, {"primary_key": "3115146", "vector": [], "sparse_vector": [], "title": "Supervised Hierarchical Cross-Mo<PERSON>.", "authors": ["Changchang Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, due to the unprecedented growth of multimedia data, cross-modal hashing has gained increasing attention for the efficient cross-media retrieval. Typically, existing methods on cross-modal hashing treat labels of one instance independently but overlook the correlations among labels. Indeed, in many real-world scenarios, like the online fashion domain, instances (items) are labeled with a set of categories correlated by certain hierarchy. In this paper, we propose a new end-to-end solution for supervised cross-modal hashing, named HiCHNet, which explicitly exploits the hierarchical labels of instances. In particular, by the pre-established label hierarchy, we comprehensively characterize each modality of the instance with a set of layer-wise hash representations. In essence, hash codes are encouraged to not only preserve the layer-wise semantic similarities encoded by the label hierarchy, but also retain the hierarchical discriminative capabilities. Due to the lack of benchmark datasets, apart from adapting the existing dataset FashionVC from fashion domain, we create a dataset from the online fashion platform Ssense consisting of 15,696 image-text pairs labeled by 32 hierarchical categories. Extensive experiments on two real-world datasets demonstrate the superiority of our model over the state-of-the-art methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331229"}, {"primary_key": "3115147", "vector": [], "sparse_vector": [], "title": "DivGraphPointer: A Graph Pointer Network for Extracting Diverse Keyphrases.", "authors": ["Zhiqing Sun", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Keyphrase extraction from documents is useful to a variety of applications such as information retrieval and document summarization. This paper presents an end-to-end method called DivGraphPointer for extracting a set of diversified keyphrases from a document. DivGraphPointer combines the advantages of traditional graph-based ranking methods and recent neural network-based approaches. Specifically, given a document, a word graph is constructed from the document based on word proximity and is encoded with graph convolutional networks, which effectively capture document-level word salience by modeling long-range dependency between words in the document and aggregating multiple appearances of identical words into one node. Furthermore, we propose a diversified point network to generate a set of diverse keyphrases out of the word graph in the decoding process. Experimental results on five benchmark data sets show that our proposed method significantly outperforms the existing state-of-the-art approaches.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331219"}, {"primary_key": "3115149", "vector": [], "sparse_vector": [], "title": "Yelling at Your TV: An Analysis of Speech Recognition Errors and Subsequent User Behavior on Entertainment Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Millions of consumers issue voice queries through television-based entertainment systems such as the Comcast X1, the Amazon Fire TV, and Roku TV. Automatic speech recognition (ASR) systems are responsible for transcribing these voice queries into text to feed downstream natural language understanding modules. However, ASR is far from perfect, often producing incorrect transcriptions and forcing users to take corrective action. To better understand their impact on sessions, this paper characterizes speech recognition errors as well as subsequent user responses. We provide both quantitative and qualitative analyses, examining the acoustic as well as lexical attributes of the utterances. This work represents, to our knowledge, the first analysis of speech recognition errors from real users on a widely-deployed entertainment system.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331271"}, {"primary_key": "3115150", "vector": [], "sparse_vector": [], "title": "The FacT: Taming Latent Factor Models for Explainability with Factorization Trees.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Latent factor models have achieved great success in personalized recommendations, but they are also notoriously difficult to explain. In this work, we integrate regression trees to guide the learning of latent factor models for recommendation, and use the learnt tree structure to explain the resulting latent factors. Specifically, we build regression trees on users and items respectively with user-generated reviews, and associate a latent profile to each node on the trees to represent users and items. With the growth of regression tree, the latent factors are gradually refined under the regularization imposed by the tree structure. As a result, we are able to track the creation of latent profiles by looking into the path of each factor on regression trees, which thus serves as an explanation for the resulting recommendations. Extensive experiments on two large collections of Amazon and Yelp reviews demonstrate the advantage of our model over several competitive baseline algorithms. Besides, our extensive user study also confirms the practical value of explainable recommendations generated by our model.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331244"}, {"primary_key": "3115152", "vector": [], "sparse_vector": [], "title": "Event Tracker: A Text Analytics Platform for Use During Disasters.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Emergency management organisations currently rely on a wide range of disparate tools and technologies to support the monitoring and management of events during crisis situations. This has a number of disadvantages, in terms of training time for new staff members, reliance on external services, and a lack of integration (and hence poor transfer of information) between those services. On the other hand, Event Tracker is a new solution that aims to provide a unified view of an event, integrating information from emergency response officers, the public (via social media) and also volunteers from around the world. In particular, Event Tracker provides a series of novel functionalities to realise this unified view of the event, namely: real-time identification of critical information, automatic grouping of content by the information needs of response officers, as well as real-time volunteers management and communication. This is supported by an efficient and scalable back-end infrastructure designed to ingest and process high-volumes of real-time streaming data with low latency.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331406"}, {"primary_key": "3115153", "vector": [], "sparse_vector": [], "title": "Improving Collaborative Metric Learning with Efficient Negative Sampling.", "authors": ["Viet-Anh Tran", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Distance metric learning based on triplet loss has been applied with success in a wide range of applications such as face recognition, image retrieval, speaker change detection and recently recommendation with the CML model. However, as we show in this article, CML requires large batches to work reasonably well because of a too simplistic uniform negative sampling strategy for selecting triplets. Due to memory limitations, this makes it difficult to scale in high-dimensional scenarios. To alleviate this problem, we propose here a 2-stage negative sampling strategy which finds triplets that are highly informative for learning. Our strategy allows CML to work effectively in terms of accuracy and popularity bias, even when the batch size is an order of magnitude smaller than what would be needed with the default uniform sampling. We demonstrate the suitability of the proposed strategy for recommendation and exhibit consistent positive results across various datasets.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331337"}, {"primary_key": "3115154", "vector": [], "sparse_vector": [], "title": "Domain Adaptation for Enterprise Email Search.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the enterprise email search setting, the same search engine often powers multiple enterprises from various industries: technology, education, manufacturing, etc. However, using the same global ranking model across different enterprises may result in suboptimal search quality, due to the corpora differences and distinct information needs. On the other hand, training an individual ranking model for each enterprise may be infeasible, especially for smaller institutions with limited data. To address this data challenge, in this paper we propose a domain adaptation approach that fine-tunes the global model to each individual enterprise. In particular, we propose a novel application of the Maximum Mean Discrepancy (MMD) approach to information retrieval, which attempts to bridge the gap between the global data distribution and the data distribution for a given individual enterprise. We conduct a comprehensive set of experiments on a large-scale email search engine, and demonstrate that the MMD approach consistently improves the search quality for multiple individual domains, both in comparison to the global ranking model, as well as several competitive domain adaptation baselines including adversarial learning methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331204"}, {"primary_key": "3115155", "vector": [], "sparse_vector": [], "title": "Interact and Decide: Medley of Sub-Attention Networks for Effective Group Recommendation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes Medley of Sub-Attention Networks (MoSAN), a new novel neural architecture for the group recommendation task. Group-level recommendation is known to be a challenging task, in which intricate group dynamics have to be considered. As such, this is to be contrasted with the standard recommendation problem where recommendations are personalized with respect to a single user. Our proposed approach hinges upon the key intuition that the decision making process (in groups) is generally dynamic, i.e., a user's decision is highly dependent on the other group members. All in all, our key motivation manifests in a form of an attentive neural model that captures fine-grained interactions between group members. In our MoSAN model, each sub-attention module is representative of a single member, which models a user's preference with respect to all other group members. Subsequently, a Medley of Sub-Attention modules is then used to collectively make the group's final decision. Overall, our proposed model is both expressive and effective. Via a series of extensive experiments, we show that MoSAN not only achieves state-of-the-art performance but also improves standard baselines by a considerable margin.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331251"}, {"primary_key": "3115156", "vector": [], "sparse_vector": [], "title": "Adversarial Mahalanobis Distance-based Attentive Song Recommender for Automatic Playlist Continuation.", "authors": ["Thanh Tran", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we aim to solve the automatic playlist continuation (APC) problem by modeling complex interactions among users, playlists, and songs using only their interaction data. Prior methods mainly rely on dot product to account for similarities, which is not ideal as dot product is not metric learning, so it does not convey the important inequality property. Based on this observation, we propose three novel deep learning approaches that utilize Mahalanobis distance. Our first approach uses user-playlist-song interactions, and combines Mahalanobis distance scores between (i) a target user and a target song, and (ii) between a target playlist and the target song to account for both the user's preference and the playlist's theme. Our second approach measures song-song similarities by considering Mahalanobis distance scores between the target song and each member song (i.e., existing song) in the target playlist. The contribution of each distance score is measured by our proposed memory metric-based attention mechanism. In the third approach, we fuse the two previous models into a unified model to further enhance their performance. In addition, we adopt and customize Adversarial Personalized Ranking (APR) for our three approaches to further improve their robustness and predictive capabilities. Through extensive experiments, we show that our proposed models outperform eight state-of-the-art models in two large-scale real-world datasets.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331234"}, {"primary_key": "3115157", "vector": [], "sparse_vector": [], "title": "ABCPRec: Adaptively Bridging Consumer and Producer Roles for User-Generated Content Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In Web services dealing with user-generated content (UGC), a user can have two roles: a role of a consumer and that of a producer. Since most item recommendation models have only considered the role of a user as a consumer, how to leverage the two roles to improve UGC recommendation accuracy has been underexplored. In this paper, based on the state-of-the-art UGC recommendation method called CPRec (consumer and producer based recommendation), we propose ABCPRec (adaptively bridging CPRec). Unlike CPRec, which assumes that the two roles of a user are always related to each other, ABCPRec adaptively bridges the two roles according to the similarity between her nature as a consumer and that as a producer. This enables the model to learn each user's characteristics as both a consumer and a producer and to recommend items to each user more accurately. By using two real-world datasets, we showed that our proposed method significantly outperformed comparative methods in terms of AUC.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331335"}, {"primary_key": "3115158", "vector": [], "sparse_vector": [], "title": "Parrot: A Python-based Interactive Platform for Information Retrieval Research.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Tingting He"], "summary": "Open source softwares play an important role in information retrieval research. Most of the existing open source information retrieval systems are implemented in Java or C++ programming language. In this paper, we propose Parrot1, a Python-based interactive platform for information retrieval research. The proposed platform has mainly three advantages in comparison with the existing retrieval systems: (1) It is integrated with Jupyter Notebook, an interactive programming platform which has proved to be effective for data scientists to tackle big data and AI problems. As a result, users can interactively visualize and diagnose a retrieval model; (2) As an application written in Python, it can be easily used in combination with the popular deep learning frameworks such as Tersorflow and Pytorch; (3) It is designed especially for researchers. Less code is needed to create a new retrieval model or to modify an existing one. Our efforts have focused on three functionalists: good usability, interactive programming, and good interoperability with the popular deep learning frameworks. To confirm the performance of the proposed system, we conduct comparative experiments on a number of standard test collections. The experimental results show that the proposed system is both efficient and effective, providing a practical framework for researchers in information retrieval.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331393"}, {"primary_key": "3115159", "vector": [], "sparse_vector": [], "title": "Challenges and Opportunities in Understanding Spoken Queries Directed at Modern Entertainment Platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Modern in-home entertainment platforms---representing the evolution of the humble television of yesteryear---are packed with features and content: they offer a dizzying array of programs spanning hundreds of channels as well as a catalog of on-demand programs offering tens of thousands of options. Furthermore, the entertainment platform may serve as an in-home hub, providing capabilities ranging from playing music to controlling the home security system. At a high level, our goal is to provide natural speech-based access to these myriad features as an alternative to physical button entry on a remote control.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331433"}, {"primary_key": "3115160", "vector": [], "sparse_vector": [], "title": "Statistical Significance Testing in Information Retrieval: An Empirical Analysis of Type I, Type II and Type III Errors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Statistical significance testing is widely accepted as a means to assess how well a difference in effectiveness reflects an actual difference between systems, as opposed to random noise because of the selection of topics. According to recent surveys on SIGIR, CIKM, ECIR and TOIS papers, the t-test is the most popular choice among IR researchers. However, previous work has suggested computer intensive tests like the bootstrap or the permutation test, based mainly on theoretical arguments. On empirical grounds, others have suggested non-parametric alternatives such as the <PERSON><PERSON> test. Indeed, the question of which tests we should use has accompanied IR and related fields for decades now. Previous theoretical studies on this matter were limited in that we know that test assumptions are not met in IR experiments, and empirical studies were limited in that we do not have the necessary control over the null hypotheses to compute actual Type I and Type II error rates under realistic conditions. Therefore, not only is it unclear which test to use, but also how much trust we should put in them. In contrast to past studies, in this paper we employ a recent simulation methodology from TREC data to go around these limitations. Our study comprises over 500 million p-values computed for a range of tests, systems, effectiveness measures, topic set sizes and effect sizes, and for both the 2-tail and 1-tail cases. Having such a large supply of IR evaluation data with full knowledge of the null hypotheses, we are finally in a position to evaluate how well statistical significance tests really behave with IR data, and make sound recommendations for practitioners.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331259"}, {"primary_key": "3115161", "vector": [], "sparse_vector": [], "title": "A New Perspective on Score Standardization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In test collection based evaluation of IR systems, score standardization has been proposed to compare systems across collections and minimize the effect of outlier runs on specific topics. The underlying idea is to account for the difficulty of topics, so that systems are scored relative to it. <PERSON> et al. first proposed standardization through a non-linear transformation with the standard normal distribution, and recently <PERSON><PERSON> proposed a simple linear transformation. In this paper, we show that both approaches are actually special cases of a simple standardization which assumes specific distributions for the per-topic scores. From this viewpoint, we argue that a transformation based on the empirical distribution is the most appropriate choice for this kind of standardization. Through a series of experiments on TREC data, we show the benefits of our proposal in terms of score stability and statistical test behavior.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331315"}, {"primary_key": "3115162", "vector": [], "sparse_vector": [], "title": "How to Deal with <PERSON><PERSON><PERSON> in Answer Selection.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Addressing Question Answering (QA) tasks with complex neural networks typically requires a large amount of annotated data to achieve a satisfactory accuracy of the models. In this work, we are interested in simple models that can potentially give good performance on datasets with no or few annotations. First, we propose new unsupervised baselines that leverage distributed word and sentence representations. Second, we compare the ability of our neural network architectures to learn from few annotated samples and we demonstrate how these methods can benefit from a pre-training on an external dataset. With a particular emphasis on the reproducibility of our results, we show that our simple models can approach or reach state-of-the-art performance on four common QA datasets.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331291"}, {"primary_key": "3115163", "vector": [], "sparse_vector": [], "title": "Block-distributed Gradient Boosted Trees.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Gradient Boosted Tree (GBT) algorithm is one of the most popular machine learning algorithms used in production, for tasks that include Click-Through Rate (CTR) prediction and learning-to-rank. To deal with the massive datasets available today, many distributed GBT methods have been proposed. However, they all assume a row-distributed dataset, addressing scalability only with respect to the number of data points and not the number of features, and increasing communication cost for high-dimensional data. In order to allow for scalability across both the data point and feature dimensions, and reduce communication cost, we propose block-distributed GBTs. We achieve communication efficiency by making full use of the data sparsity and adapting the Quickscorer algorithm to the block-distributed setting. We evaluate our approach using datasets with millions of features, and demonstrate that we are able to achieve multiple orders of magnitude reduction in communication cost for sparse data, with no loss in accuracy, while providing a more scalable design. As a result, we are able to reduce the training time for high-dimensional data, and allow more cost-effective scale-out without the need for expensive network communication.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331331"}, {"primary_key": "3115165", "vector": [], "sparse_vector": [], "title": "LIRME: Locally Interpretable Ranking Model Explanation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Information retrieval (IR) models often employ complex variations in term weights to compute an aggregated similarity score of a query-document pair. Treating IR models as black-boxes makes it difficult to understand or explain why certain documents are retrieved at top-ranks for a given query. Local explanation models have emerged as a popular means to understand individual predictions of classification models. However, there is no systematic investigation that learns to interpret IR models, which is in fact the core contribution of our work in this paper. We explore three sampling methods to train an explanation model and propose two metrics to evaluate explanations generated for an IR model. Our experiments reveal some interesting observations, namely that a) diversity in samples is important for training local explanation models, and b) the stability of a model is inversely proportional to the number of parameters used to explain the model.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331377"}, {"primary_key": "3115167", "vector": [], "sparse_vector": [], "title": "Learning from Fact-checkers: Analysis and Generation of Fact-checking Language.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In fighting against fake news, many fact-checking systems comprised of human-based fact-checking sites (e.g., snopes.com and politifact.com) and automatic detection systems have been developed in recent years. However, online users still keep sharing fake news even when it has been debunked. It means that early fake news detection may be insufficient and we need another complementary approach to mitigate the spread of misinformation. In this paper, we introduce a novel application of text generation for combating fake news. In particular, we (1) leverage online users named fact-checkers, who cite fact-checking sites as credible evidences to fact-check information in public discourse; (2) analyze linguistic characteristics of fact-checking tweets; and (3) propose and build a deep learning framework to generate responses with fact-checking intention to increase the fact-checkers' engagement in fact-checking activities. Our analysis reveals that the fact-checkers tend to refute misinformation and use formal language (e.g. few swear words and Internet slangs). Our framework successfully generates relevant responses, and outperforms competing models by achieving up to 30% improvements. Our qualitative study also confirms that the superiority of our generated responses compared with responses generated from the existing models.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331248"}, {"primary_key": "3115168", "vector": [], "sparse_vector": [], "title": "Query-Task Mapping.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Several recent task-based search studies aim at splitting query logs into sets of queries for the same task or information need. We address the natural next step: mapping a currently submitted query to an appropriate task in an already task-split log. This query-task mapping can, for instance, enhance query suggestions---rendering efficiency of the mapping, besides accuracy, a key objective. Our main contributions are three large benchmark datasets and preliminary experiments with four query-task mapping approaches: (1) a Trie-based approach, (2) MinHash~LSH, (3) word movers distance in a Word2Vec setup, and (4) an inverted index-based approach. The experiments show that the fast and accurate inverted index-based method forms a strong baseline.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331286"}, {"primary_key": "3115170", "vector": [], "sparse_vector": [], "title": "Neural Graph Collaborative Filtering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Learning vector representations (aka. embeddings) of users and items lies at the core of modern recommender systems. Ranging from early matrix factorization to recently emerged deep learning based methods, existing efforts typically obtain a user's (or an item's) embedding by mapping from pre-existing features that describe the user (or the item), such as ID and attributes. We argue that an inherent drawback of such methods is that, the collaborative signal, which is latent in user-item interactions, is not encoded in the embedding process. As such, the resultant embeddings may not be sufficient to capture the collaborative filtering effect.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331267"}, {"primary_key": "3115171", "vector": [], "sparse_vector": [], "title": "SCSS-LIE: A Novel Synchronous Collaborative Search System with a Live Interactive Engine.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Synchronous collaborative search systems (SCSS) refer to systems which support two or more users with similar information need to search together simultaneously. Generally, SCSS provide a social engine to enable users to communicate. However, when the number of users in the social engine is insufficient to collaborate on the search task, the social engine will encounter the cold start problem and can not perform collaborative search well. In this paper, we present a novel Synchronous Collaborative Search System with a Live Interactive Engine (SCSS-LIE). SCSS-LIE proposes to apply a ring topology to add an intelligent auxiliary robot, Infobot, into the social engine to support real-time interaction between users and the search engine to address the cold start problem of the social engine. The reading comprehension model BiDAF (Bi-Directional Attention Flow) is employed in the Infobot in the process of interacting with the search engine to obtain answers to facilitate the acquisition of information. SCSS-LIE can not only allow users with similar information need to be grouped into one chat channel to communicate, but also enable them to conduct real-time interaction with the search engine to improve search efficiency.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331407"}, {"primary_key": "3115172", "vector": [], "sparse_vector": [], "title": "Dynamic Content Monitoring and Exploration using Vector Spaces.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This doctoral research project investigates using Quantum Theory (QT) to represent language, especially in some dynamic scenarios, e.g. when dealing with dynamic corpora or interactive tasks. The author plans to propose a quantum state driven framework for language problems and generalize it in a high-dimensional tensor space. Dynamics will be modeled by the formalism thereof of quantum evolution governing the update of quantum states. The author argues that this proposal will pave the way towards a new paradigm which may provide some novel insights about how to represent the language and its evolution in dynamic scenarios.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331412"}, {"primary_key": "3115173", "vector": [], "sparse_vector": [], "title": "Unified Collaborative Filtering over Graph Embeddings.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Collaborative Filtering (CF) by learning from the wisdom of crowds has become one of the most important approaches to recommender systems research, and various CF models have been designed and applied to different scenarios. However, a challenging task is how to select the most appropriate CF model for a specific recommendation task. In this paper, we propose a Unified Collaborative Filtering framework based on Graph Embeddings (UGrec for short) to solve the problem. Specifically, UGrec models user and item interactions within a graph network, and sequential recommendation path is designed as a basic unit to capture the correlations between users and items. Mathematically, we show that many representative recommendation approaches and their variants can be mapped as a recommendation path in the graph. In addition, by applying a carefully designed attention mechanism on the recommendation paths, UGrec can determine the significance of each sequential recommendation path so as to conduct automatic model selection. Compared with state-of-the-art methods, our method shows significant improvements for recommendation quality. This work also leads to a deeper understanding of the connection between graph embeddings and recommendation algorithms.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331224"}, {"primary_key": "3115174", "vector": [], "sparse_vector": [], "title": "Hierarchical Matching Network for Crime Classification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automatic crime classification is a fundamental task in the legal field. Given the fact descriptions, judges first determine the relevant violated laws, and then the articles. As laws and articles are grouped into a tree-shaped hierarchy (i.e., laws as parent labels, articles as children labels), this task can be naturally formalized as a two layers' hierarchical multi-label classification problem. Generally, the label semantics (i.e., definition of articles) and the hierarchical structure are two informative properties for judges to make a correct decision. However, most previous methods usually ignore the label structure and feed all labels into a flat classification framework, or neglect the label semantics and only utilize fact descriptions for crime classification, thus the performance may be limited. In this paper, we formalize crime classification problem into a matching task to address these issues. We name our model as Hierarchical Matching Network (HMN for short). Based on the tree hierarchy, HMN explicitly decomposes the semantics of children labels into the residual and alignment components. The residual components keep the unique characteristics of each individual children label, while the alignment components capture the common semantics among sibling children labels, which are further aggregated as the representation of their parent label. Finally, given a fact description, a co-attention metric is applied to effectively match the relevant laws and articles. Experiments on two real-world judicial datasets demonstrate that our model can significantly outperform the state-of-the-art methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331223"}, {"primary_key": "3115176", "vector": [], "sparse_vector": [], "title": "Context-Aware Intent Identification in Email Conversations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Email continues to be one of the most important means of online communication. People spend a significant amount of time sending, reading, searching and responding to email in order to manage tasks, exchange information, etc. In this paper, we study intent identification in workplace email. We use a large scale publicly available email dataset to characterize intents in enterprise email and propose methods for improving intent identification in email conversations. Previous work focused on classifying email messages into broad topical categories or detecting sentences that contain action items or follow certain speech acts. In this work, we focus on sentence-level intent identification and study how incorporating more context (such as the full message body and other metadata) could improve the performance of the intent identification models. We experiment with several models for leveraging context including both classical machine learning and deep learning approaches. We show that modeling the interaction between sentence and context can significantly improve the performance.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331260"}, {"primary_key": "3115177", "vector": [], "sparse_vector": [], "title": "Variance Reduction in Gradient Exploration for Online Learning to Rank.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>yun Wu", "<PERSON><PERSON>"], "summary": "Online Learning to Rank (OL2R) algorithms learn from implicit user feedback on the fly. The key to such algorithms is an unbiased estimate of gradients, which is often (trivially) achieved by uniformly sampling from the entire parameter space. Unfortunately, this leads to high-variance in gradient estimation, resulting in high regret during model updates, especially when the dimension of the parameter space is large.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331264"}, {"primary_key": "3115178", "vector": [], "sparse_vector": [], "title": "A Collaborative Session-based Recommendation Approach with Parallel Memory Modules.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Session-based recommendation is the task of predicting the next item to recommend when the only available information consists of anonymous behavior sequences. Previous methods for session-based recommendation focus mostly on the current session, ignoring collaborative information in so-called neighborhood sessions, sessions that have been generated previously by other users and reflect similar user intents as the current session. We hypothesize that the collaborative information contained in such neighborhood sessions may help to improve recommendation performance for the current session.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331210"}, {"primary_key": "3115179", "vector": [], "sparse_vector": [], "title": "Effective Medical Archives Processing Using Knowledge Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Medical archives processing is a very important task in a medical information system. It generally consists of three steps: medical archives recognition, feature extraction and text classification. In this paper, we focus on empowering the medical archives processing with knowledge graphs. We first build a semantic-rich medical knowledge graph. Then, we recognize texts from medical archives using several popular optical character recognition (OCR) engines, and extract keywords from texts using a knowledge graph based feature extraction algorithm. Third, we define a semantic measure based on knowledge graph to evaluate the similarity between medical texts, and perform the text classification task. This measure can value semantic relatedness between medical documents, to enhance the text classification. We use medical archives collected from real hospitals for validation. The results show that our algorithms can significantly outperform typical baselines that employs only term statistics.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331350"}, {"primary_key": "3115180", "vector": [], "sparse_vector": [], "title": "Encoding Syntactic Dependency and Topical Information for Social Emotion Classification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Minghua Xu"], "summary": "Social emotion classification is to estimate the distribution of readers' emotion evoked by an article. In this paper, we design a new neural network model by encoding sentence syntactic dependency and document topical information into the document representation. We first use a dependency embedded recursive neural network to learn syntactic features for each sentence, and then use a gated recurrent unit to transform the sentences' vectors into a document vector. We also use a multi-layer perceptron to encode the topical information of a document into a topic vector. Finally, a gate layer is used to compose the document representation from the gated summation of the document vector and the topic vector. Experiment results on two public datasets indicate that our proposed model outperforms the state-of-the-art methods in terms of better average Pearson correlation coefficient and MicroF1 performance.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331287"}, {"primary_key": "3115181", "vector": [], "sparse_vector": [], "title": "Online User Representation Learning Across Heterogeneous Social Networks.", "authors": ["<PERSON><PERSON>", "Hong<PERSON> Yin", "Xingzhong Du", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Accurate user representation learning has been proven fundamental for many social media applications, including community detection, recommendation, etc. A major challenge lies in that, the available data in a single social network are usually very limited and sparse. In real life, many people are members of several social networks in the same time. Constrained by the features and design of each, any single social platform offers only a partial view of a user from a particular perspective. In this paper, we propose MV-URL, a multi-view user representation learning model to enhance user modeling by integrating the knowledge from various networks. Different from the traditional network embedding frameworks where either the whole framework is single-network based or each network involved is a homogeneous network, we focus on multiple social networks and each network in our task is a heterogeneous network. It's very challenging to effectively fuse knowledge in this setting as the fusion depends upon not only the varying relatedness of information sources, but also the target application tasks. MV-URL focuses on two tasks: user account linkage (i.e., to predict the missing true user account linkage across social media) and user attribute prediction. Extensive evaluations have been conducted on two real-world collections of linked social networks, and the experimental results show the superiority of MV-URL compared with existing state-of-art embedding methods. It can be learned online, and is trivially parallelizable. These qualities make it suitable for real world applications.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331258"}, {"primary_key": "3115182", "vector": [], "sparse_vector": [], "title": "Document Gated Reader for Open-Domain Question Answering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Open-domain question answering focuses on using diverse information resources to answer any types of question. Recent years, with the development of large-scale data set and various deep neural networks models, some recent advances in open domain question answering system first utilize the distantly supervised dataset as the knowledge resource, then apply deep learning based machine comprehension techniques to generate the right answers, which achieves impressive results compared with traditional feature-based pipeline methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331190"}, {"primary_key": "3115184", "vector": [], "sparse_vector": [], "title": "Deep Collaborative Discrete Hashing with Semantic-Invariant Structure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing deep hashing approaches fail to fully explore semantic correlations and neglect the effect of linguistic context on visual attention learning, leading to inferior performance. This paper proposes a dual-stream learning framework, dubbed Deep Collaborative Discrete Hashing (DCDH), which constructs a discriminative common discrete space by collaboratively incorporating the shared and individual semantics deduced from visual features and semantic labels. Specifically, the context-aware representations are generated by employing the outer product of visual embeddings and semantic encodings. Moreover, we reconstruct the labels and introduce the focal loss to take advantage of frequent and rare concepts. The common binary code space is built on the joint learning of the visual representations attended by language, the semantic-invariant structure construction and the label distribution correction. Extensive experiments demonstrate the superiority of our method.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331275"}, {"primary_key": "3115186", "vector": [], "sparse_vector": [], "title": "Modeling Transferable Topics for Cross-Target Stance Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Targeted stance detection aims to classify the attitude of an opinionated text towards a pre-defined target. Previous methods mainly focus on in-target setting that models are trained and tested using data specific to the same target. In practical cases, the target we concern may have few or no labeled data, which restrains us from training a target-specific model. In this paper we study the problem of cross-target stance detection, utilizing labeled data of a source target to learn models that can be adapted to a destination target. To this end, we propose an effective method, the core intuition of which is to leverage shared latent topics between two targets as transferable knowledge to facilitate model adaptation. Our method acquires topic knowledge with neural variational inference, and further adopts adversarial training that encourages the model to learn target-invariant representations. Experimental results verify that our proposed method is superior to the state-of-the-art methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331367"}, {"primary_key": "3115187", "vector": [], "sparse_vector": [], "title": "Task Completion Detection: A Study in the Context of Intelligent Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "People can record their pending tasks using to-do lists, digital assistants, and other task management software. In doing so, users of these systems face at least two challenges: (1) they must manually mark their tasks as complete, and (2) when systems proactively remind them about their pending tasks, say, via interruptive notifications, they lack information on task completion status. As a result, people may not realize the full benefits of to-do lists (since these lists can contain both completed and pending tasks) and they may be reminded about tasks they have already done (wasting time and causing frustration). In this paper, we present methods to automatically detect task completion. These inferences can be used to deprecate completed tasks and/or suppress notifications for these tasks (or for other purposes, e.g., task prioritization). Using log data from a popular digital assistant, we analyze temporal dynamics in the completion of tasks and train machine-learned models to detect completion with accuracy exceeding 80% using a variety of features (time elapsed since task creation, task content, email, notifications, user history). The findings have implications for the design of intelligent systems to help people manage their tasks.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331187"}, {"primary_key": "3115188", "vector": [], "sparse_vector": [], "title": "Measuring Job Search Effectiveness.", "authors": ["<PERSON><PERSON>"], "summary": "Users of online job search websites interact with ranked lists of job summaries generated in response to queries, hoping to identify one or more jobs of interest. Hence, the quality of job search rankings becomes a primary factor that affects user satisfaction. In this work, we propose methodologies and measures for evaluating the quality of job search rankings from a user modeling perspective. We start by investigating job seekers' behavior when they are interacting with the generated rankings, leveraging job search interaction logs from Seek.com, a well-known Australasian job search website. The output of this investigation will be an accurate model of job seekers that will be incorporated into an effectiveness metric. Recent proposals for job search ranking models used using two types of metrics to evaluate the quality of the ranking generated by the models: (1) offline metrics, such as [email protected] (k is set to the number of job summaries shown in the first page), [email protected], or Mean Reciprocal Rank (MRR); and (2) online metrics, such as click-through rate and job application rate [3, 6].", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331421"}, {"primary_key": "3115189", "vector": [], "sparse_vector": [], "title": "Automatic Task Completion Flows from Web APIs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Web contains many APIs that could be combined in countless ways to enable Intelligent Assistants to complete all sorts of tasks. We propose a method to automatically produce task completion flows from a collection of these APIs by combining them in a graph and automatically extracting paths from the graph for task completion. These paths chain together API calls and use the output of executed APIs as inputs to others. We automatically extract these paths from an API graph in response to a user query and then rank the paths by the likelihood of them leading to user satisfaction. We apply our approach for task completion in the email and calendar domains and show how it can be used to automatically create task completion flows.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331318"}, {"primary_key": "3115190", "vector": [], "sparse_vector": [], "title": "Document Distance Metric Learning in an Interactive Exploration Process.", "authors": ["<PERSON>"], "summary": "Visualization of inter-document similarities is widely used for the exploration of document collections and interactive retrieval. However, similarity relationships between documents are multifaceted and measured distances by a given metric often do not match the perceived similarity of human beings. Furthermore, the user's notion of similarity can drastically change with the exploration objective or task at hand. Therefore, this research proposes to investigate online adjustments to the similarity model using feedback generated during exploration or exploratory search. In this course, rich visualizations and interactions will support users to give valuable feedback. Based on this, metric learning methodologies will be applied to adjust a similarity model in order to improve the exploration experience. At the same time, trained models are considered as valuable outcomes whose benefits for similarity-based tasks such as query-by-example retrieval or classification will be tested.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331420"}, {"primary_key": "3115191", "vector": [], "sparse_vector": [], "title": "One-Class Collaborative Filtering with the Queryable Variational Autoencoder.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Variational Autoencoder (VAE) based methods for Collaborative Filtering (CF) demonstrate remarkable performance for one-class (implicit negative) recommendation tasks by extending autoencoders with relaxed but tractable latent distributions. Explicitly modeling a latent distribution over user preferences allows VAEs to learn user and item representations that not only reproduce observed interactions, but also generalize them by leveraging learning from similar users and items. Unfortunately, VAE-CF can exhibit suboptimal learning properties; e.g., VAE-CFs will increase their prediction confidence as they receive more preferences per user, even when those preferences may vary widely and create ambiguity in the user representation. To address this issue, we propose a novel Queryable Variational Autoencoder (Q-VAE) variant of the VAE that explicitly models arbitrary conditional relationships between observations. The proposed model appropriately increases uncertainty (rather than reduces it) in cases where a large number of user preferences may lead to an ambiguous user representation. Our experiments on two benchmark datasets show that the Q-VAE generally performs comparably or outperforms VAE-based recommenders as well as other state-of-the-art approaches and is generally competitive across the user preference density spectrum, where other methods peak for certain preference density levels.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331292"}, {"primary_key": "3115192", "vector": [], "sparse_vector": [], "title": "Investigating Passage-level Relevance and Its Role in Document-level Relevance Judgment.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The understanding of the process of relevance judgment helps to inspire the design of retrieval models. Traditional retrieval models usually estimate relevance based on document-level signals. Recent works consider a more fine-grain, passage-level relevance information, which can further enhance retrieval performance. However, it lacks a detailed analysis of how passage-level relevance signals determine or influence the relevance judgment of the whole document. To investigate the role of passage-level relevance in the document-level relevance judgment, we construct an ad-hoc retrieval dataset with both passage-level and document-level relevance labels. A thorough analysis reveals that: 1) there is a strong correlation between the document-level relevance and the fractions of irrelevant passages to highly relevant passages; 2) the position, length and query similarity of passages play different roles in the determination of document-level relevance; 3) The sequential passage-level relevance within a document is a potential indicator for the document-level relevance. Based on the relationship between passage-level and document-level relevance, we also show that utilizing passage-level relevance signals can improve existing document ranking models. This study helps us better understand how users perceive relevance for a document and inspire the designing of novel ranking models leveraging fine-grain, passage-level relevance signals.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331233"}, {"primary_key": "3115193", "vector": [], "sparse_vector": [], "title": "A Neural Influence Diffusion Model for Social Recommendation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Precise user and item embedding learning is the key to building a successful recommender system. Traditionally, Collaborative Filtering (CF) provides a way to learn user and item embeddings from the user-item interaction history. However, the performance is limited due to the sparseness of user behavior data. With the emergence of online social networks, social recommender systems have been proposed to utilize each user's local neighbors' preferences to alleviate the data sparsity for better user embedding modeling. We argue that, for each user of a social platform, her potential embedding is influenced by her trusted users, with these trusted users are influenced by the trusted users' social connections. As social influence recursively propagates and diffuses in the social network, each user's interests change in the recursive process. Nevertheless, the current social recommendation models simply developed static models by leveraging the local neighbors of each user without simulating the recursive diffusion in the global social network, leading to suboptimal recommendation performance. In this paper, we propose a deep influence propagation model to stimulate how users are influenced by the recursive social diffusion process for social recommendation. For each user, the diffusion process starts with an initial embedding that fuses the related features and a free user latent vector that captures the latent behavior preference. The key idea of our proposed model is that we design a layer-wise influence propagation structure to model how users' latent embeddings evolve as the social diffusion process continues. We further show that our proposed model is general and could be applied when the user~(item) attributes or the social network structure is not available. Finally, extensive experimental results on two real-world datasets clearly show the effectiveness of our proposed model, with more than 13% performance improvements over the best baselines for top-10 recommendation on the two datasets.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331214"}, {"primary_key": "3115194", "vector": [], "sparse_vector": [], "title": "Noise Contrastive Estimation for One-Class Collaborative Filtering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous highly scalable One-Class Collaborative Filtering (OC-CF) methods such as Projected Linear Recommendation (PLRec) have advocated using fast randomized SVD to embed items into a latent space, followed by linear regression methods to learn personalized recommendation models per user. However, naive SVD embedding methods often exhibit a strong popularity bias that prevents them from accurately embedding less popular items, which is exacerbated by the extreme sparsity of implicit feedback matrices in the OC-CF setting. To address this deficiency, we leverage insights from Noise Contrastive Estimation (NCE) to derive a closed-form, efficiently computable \"depopularized\" embedding. We show that NCE item embeddings combined with a personalized user model from PLRec produces superior recommendations that adequately account for popularity bias. Further analysis of the popularity distribution of recommended items demonstrates that NCE-PLRec uniformly distributes recommendations over the popularity spectrum while other methods exhibit distinct biases towards specific popularity subranges. Empirically, NCE-PLRec produces highly competitive performance with run-times an order of magnitude faster than existing state-of-the-art approaches for OC-CF.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331201"}, {"primary_key": "3115195", "vector": [], "sparse_vector": [], "title": "Deep Chit-Chat: Deep Learning for Chatbots.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The tutorial is based on our long-term research on open domain conversation, rich hands-on experience on development of Microsoft XiaoIce, and our previous tutorials on EMNLP 2018 and the Web Conference 2019. It starts from a summary of recent achievement made by both academia and industry on chatbots, and then performs a thorough and systematic introduction to state-of-the-art methods for open domain conversation modeling including both retrieval-based methods and generation-based methods. In addition to these, the tutorial also covers some new progress on both groups of methods, such as transition from model design to model learning, transition from knowledge agnostic conversation to knowledge aware conversation, and transition from single-modal conversation to multi-modal conversation. The tutorial is ended by some promising future directions such as how to combine non-task-oriented dialogue systems with task-oriented dialogue systems and how to enhance language learning with chatbots.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331388"}, {"primary_key": "3115197", "vector": [], "sparse_vector": [], "title": "Reinforcement Knowledge Graph Reasoning for Explainable Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent advances in personalized recommendation have sparked great interest in the exploitation of rich structured information provided by knowledge graphs. Unlike most existing approaches that only focus on leveraging knowledge graphs for more accurate recommendation, we aim to conduct explicit reasoning with knowledge for decision making so that the recommendations are generated and supported by an interpretable causal inference procedure. To this end, we propose a method called Policy-Guided Path Reasoning (PGPR), which couples recommendation and interpretability by providing actual paths in a knowledge graph. Our contributions include four aspects. We first highlight the significance of incorporating knowledge graphs into recommendation to formally define and interpret the reasoning process. Second, we propose a reinforcement learning (RL) approach featured by an innovative soft reward strategy, user-conditional action pruning and a multi-hop scoring function. Third, we design a policy-guided graph search algorithm to efficiently and effectively sample reasoning paths for recommendation. Finally, we extensively evaluate our method on several large-scale real-world benchmark datasets, obtaining favorable results compared with state-of-the-art methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331203"}, {"primary_key": "3115198", "vector": [], "sparse_vector": [], "title": "Accelerating Exact Inner Product Retrieval by CPU-GPU Systems.", "authors": ["Long <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recommender systems are widely used in many applications, e.g., social network, e-commerce. Inner product retrieval IPR is the core subroutine in Matrix Factorization (MF) based recommender systems. It consists of two phases: i) inner product computation and ii) top-k items retrieval. The performance bottleneck of existing solutions is inner product computation phase. Exploiting Graphics Processing Units (GPUs) to accelerate the computation intensive workloads is the gold standard in data mining and machine learning communities. However, it is not trivial to apply CPU-GPU systems to boost the performance of IPR solutions due to the nature complex of the IPR problem. In this work, we analyze the time cost of each phase in IPR solutions at first. Second, we exploit the characteristics of CPU-GPU systems to improve performance. Specifically, the computation tasks of IPR solution are heterogeneously processed in CPU-GPU systems. Third, we demonstrate the efficiency of our proposal on four standard real datasets.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331376"}, {"primary_key": "3115200", "vector": [], "sparse_vector": [], "title": "AliISA: Creating an Interactive Search Experience in E-commerce Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Hongbo Deng", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Online shopping has been a habit of more and more people, while most users are unable to craft an informative query, and thus it often takes a long search session to satisfy their purchase intents. We present AliISA - a shopping assistant which offers users some tips to further specify their queries during a search session. With such an interactive search, users tend to find targeted items with fewer page requests, which often means a better user experience. Currently, AliISA assists tens of millions of users per day, earns more usage than existing systems, and consequently brings in a 5% improvement in CVR. In this paper, we present our system, describe the underlying techniques, and discuss our experience in stabilizing reinforcement learning under an E-commerce environment.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331409"}, {"primary_key": "3115201", "vector": [], "sparse_vector": [], "title": "Relational Collaborative Filtering: Modeling Multiple Item Relations for Recommendation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>"], "summary": "Existing item-based collaborative filtering (ICF) methods leverage only the relation of collaborative similarity - i.e., the item similarity evidenced by user interactions like ratings and purchases. Nevertheless, there exist multiple relations between items in real-world scenarios, e.g., two movies share the same director, two products complement with each other, etc. Distinct from the collaborative similarity that implies co-interact patterns from the user's perspective, these relations reveal fine-grained knowledge on items from different perspectives of meta-data, functionality, etc. However, how to incorporate multiple item relations is less explored in recommendation research.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331188"}, {"primary_key": "3115202", "vector": [], "sparse_vector": [], "title": "Revealing the Role of User Moods in Struggling Search Tasks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "User-centered approaches have been extensively studied and used in the area of struggling search. Related research has targeted key aspects of users such as user satisfaction or frustration, and search success or failure, using a variety of experimental methods including laboratory user studies, in-situ explicit feedback from searchers and by using crowdsourcing. Such studies are valuable in advancing the understanding of search difficulty from a user's perspective, and yield insights that can directly improve search systems and their evaluation. However, little is known about how user moods influence their interactions with a search system or their perception of struggling. In this work, we show that a user's own mood. can systematically bias the user's perception, and experience while interacting with a search system and trying to satisfy an information need. People who are in activated-(un)pleasant moods tend to issue more queries than people in deactivated or neutral moods. Those in an unpleasant mood perceive a higher level of difficulty. Our insights extend the current understanding of struggling search tasks and have important implications on the design and evaluation of search systems supporting such tasks.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331353"}, {"primary_key": "3115203", "vector": [], "sparse_vector": [], "title": "Multi-Level Matching Networks for Text Matching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Text matching aims to establish the matching relationship between two texts. It is an important operation in some information retrieval related tasks such as question duplicate detection, question answering, and dialog systems. Bidirectional long short term memory (BiLSTM) coupled with attention mechanism has achieved state-of-the-art performance in text matching. A major limitation of existing works is that only high level contextualized word representations are utilized to obtain word level matching results without considering other levels of word representations, thus resulting in incorrect matching decisions for cases where two words with different meanings are very close in high level contextualized word representation space. Therefore, instead of making decisions utilizing single level word representations, a multi-level matching network (MMN) is proposed in this paper for text matching, which utilizes multiple levels of word representations to obtain multiple word level matching results for final text level matching decision. Experimental results on two widely used benchmarks, SNLI and Scaitail, show that the proposed MMN achieves the state-of-the-art performance.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331276"}, {"primary_key": "3115205", "vector": [], "sparse_vector": [], "title": "Interpretable Fashion Matching with Rich Attributes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Understanding the mix-and-match relationships of fashion items receives increasing attention in fashion industry. Existing methods have primarily utilized the visual content to learn the visual compatibility and performed matching in a latent space. Despite their effectiveness, these methods work like a black box and cannot reveal the reasons that two items match well. The rich attributes associated with fashion items, e.g.,off-shoulder dress and black skinny jean, which describe the semantics of items in a human-interpretable way, have largely been ignored.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331242"}, {"primary_key": "3115206", "vector": [], "sparse_vector": [], "title": "Beyond Keyword Targeting: An End-to-End Ad Retrieval Framework for Sponsored Search.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As the main revenue source for search engines, sponsored search system retrieves and allocates ads to display on the search result pages. Keyword targeting is widely adopted by most sponsored search systems as the basic model for expressing the advertiser's business and retrieving related ads. In this targeting model, the advertiser should cautiously select lots of keywords relevant to their business to optimize their campaigns, and the sponsored search system retrieves ads based on the relevance between queries and keywords. However, since there is a huge inventory of possible queries and the new queries grow dramatically, it is a great challenge for advertisers to identify and collect lots of relevant bid keywords for their ads, and it also takes great effort to select and maintain high-quality keywords and set corresponding match types for them. In the meantime, the keyword targeting leads to a multi-stage retrieval architecture as it contains the matching between query and keywords and the matching between keywords and ads. The retrieval funnel based on keyword targeting cannot achieve straightforward and optimal matching between search queries and ads. Consequently, traditional keyword targeting method gradually becomes the bottleneck of optimizing advertisers' campaigns and improving the monetization of search ads.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331429"}, {"primary_key": "3115207", "vector": [], "sparse_vector": [], "title": "Text Retrieval Priors for Bayesian Logistic Regression.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Discriminative learning algorithms such as logistic regression excel when training data are plentiful, but falter when it is meager. An extreme case is text retrieval (zero training data), where discriminative learning is impossible and heuristics such as BM25, which combine domain knowledge (a topical keyword query) with generative learning (Naive Bay<PERSON>), are dominant. Building on past work, we show that BM25-inspired Gaussian priors for Bayesian logistic regression based on topical keywords provide better effectiveness than the usual L2 (zero mode, uniform variance) Gaussian prior. On two high recall retrieval datasets, the resulting models transition smoothly from BM25 level effectiveness to discriminative effectiveness as training data volume increases, dominating L2 regularization even when substantial training data is available.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331299"}, {"primary_key": "3115208", "vector": [], "sparse_vector": [], "title": "Critically Examining the &quot;Neural Hype&quot;: Weak Baselines and the Additivity of Effectiveness Gains from Neural Ranking Models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Is neural IR mostly hype? In a recent SIGIR Forum article, <PERSON> expressed skepticism that neural ranking models were actually improving ad hoc retrieval effectiveness in limited data scenarios. He provided anecdotal evidence that authors of neural IR papers demonstrate \"wins\" by comparing against weak baselines. This paper provides a rigorous evaluation of those claims in two ways: First, we conducted a meta-analysis of papers that have reported experimental results on the TREC Robust04 test collection. We do not find evidence of an upward trend in effectiveness over time. In fact, the best reported results are from a decade ago and no recent neural approach comes close. Second, we applied five recent neural models to rerank the strong baselines that <PERSON> used to make his arguments. A significant improvement was observed for one of the models, demonstrating additivity in gains. While there appears to be merit to neural IR approaches, at least some of the gains reported in the literature appear illusory.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331340"}, {"primary_key": "3115209", "vector": [], "sparse_vector": [], "title": "Local Matrix Approximation based on Graph Random Walk.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "How to decompose a large global matrix into many small local matrices has been recently researched a lot for matrix approximation. However, the distance computation in matrix decomposition is a challenging issue, as no prior knowledge about the most appropriate feature vectors and distance measures are available. In this paper, we propose a novel scheme for local matrix construction without involving distance computation. The basic idea is based on the application of convergence probabilities of graph random walk. At first, a user-item bipartite graph is constructed from the global matrix. After performing random walk on the bipartite graph, we select some user-item pairs as anchors. Then another random walk with restart is applied to construct the local matrix for each anchor. Finally, the global matrix approximation is obtained by averaging the prediction results of local matrices. Our experiments on the four real-world datasets show that the proposed solution outperforms the state-of-the-art schemes in terms of lower prediction errors and higher coverage ratios.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331338"}, {"primary_key": "3115210", "vector": [], "sparse_vector": [], "title": "Personal Knowledge Base Construction from Text-based Lifelogs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous work on lifelogging focuses on life event extraction from image, audio, and video data via wearable sensors. In contrast to wearing an extra camera to record daily life, people are used to log their life on social media platforms. In this paper, we aim to extract life events from textual data shared on Twitter and construct personal knowledge bases of individuals. The issues to be tackled include (1) not all text descriptions are related to life events, (2) life events in a text description can be expressed explicitly or implicitly, (3) the predicates in the implicit events are often absent, and (4) the mapping from natural language predicates to knowledge base relations may be ambiguous. A joint learning approach is proposed to detect life events in tweets and extract event components including subjects, predicates, objects, and time expressions. Finally, the extracted information is transformed to knowledge base facts. The evaluation is performed on a collection of lifelogs from 18 Twitter users. Experimental results show our proposed system is effective in life event extraction, and the constructed personal knowledge bases are expected to be useful to memory recall applications.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331209"}, {"primary_key": "3115211", "vector": [], "sparse_vector": [], "title": "Corpus-based Set Expansion with Lexical Features and Distributed Representations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Corpus-based set expansion refers to mining \"sibling\" entities of some given seed entities from a corpus. Previous works are limited to using either textual context matching or semantic matching to fulfill this task. Neither matching method takes full advantage of the rich information in free text. We present CaSE, an efficient unsupervised corpus-based set expansion framework that leverages lexical features as well as distributed representations of entities for the set expansion task. Experiments show that CaSE outperforms state-of-the-art set expansion algorithms in terms of expansion accuracy.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331359"}, {"primary_key": "3115212", "vector": [], "sparse_vector": [], "title": "Adversarial Collaborative Neural Network for Robust Recommendation.", "authors": ["Feng <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most of recent neural network(NN)-based recommendation techniques mainly focus on improving the overall performance, such as hit ratio for top-N recommendation, where the users' feedbacks are considered as the ground-truth. In real-world applications, those feedbacks are possibly contaminated by imperfect user behaviours, posing challenges on the design of robust recommendation methods. Some methods apply man-made noises on the input data to train the networks more effectively (e.g. the collaborative denoising auto-encoder). In this work, we propose a general adversarial training framework for NN-based recommendation models, improving both the model robustness and the overall performance. We apply our approach on the collaborative auto-encoder model, and show that the combination of adversarial training and NN-based models outperforms highly competitive state-of-the-art recommendation methods on three public datasets.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331321"}, {"primary_key": "3115213", "vector": [], "sparse_vector": [], "title": "SAIN: Self-Attentive Integration Network for Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the growing importance of personalized recommendation, numerous recommendation models have been proposed recently. Among them, Matrix Factorization (MF) based models are the most widely used in the recommendation field due to their high performance. However, MF based models suffer from cold start problems where user-item interactions are sparse. To deal with this problem, content based recommendation models which use the auxiliary attributes of users and items have been proposed. Since these models use auxiliary attributes, they are effective in cold start settings. However, most of the proposed models are either unable to capture complex feature interactions or not properly designed to combine user-item feedback information with content information. In this paper, we propose Self-Attentive Integration Network (SAIN) which is a model that effectively combines user-item feedback information and auxiliary information for recommendation task. In SAIN, a self-attention mechanism is used in the feature-level interaction layer to effectively consider interactions between multiple features, while the information integration layer adaptively combines content and feedback information. The experimental results on two public datasets show that our model outperforms the state-of-the-art models by 2.13%", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331342"}, {"primary_key": "3115214", "vector": [], "sparse_vector": [], "title": "On Topic Difficulty in IR Evaluation: The Effect of Systems, Corpora, and System Components.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Oren <PERSON>", "<PERSON>"], "summary": "In a test collection setting, topic difficulty can be defined as the average effectiveness of a set of systems for a topic. In this paper we study the effects on the topic difficulty of: (i) the set of retrieval systems; (ii) the underlying document corpus; and (iii) the system components. By generalizing methods recently proposed to study system component factor analysis, we perform a comprehensive analysis on topic difficulty and the relative effects of systems, corpora, and component interactions. Our findings show that corpora have the most significant effect on topic difficulty.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331279"}, {"primary_key": "3115215", "vector": [], "sparse_vector": [], "title": "Extracting, Mining and Predicting Users&apos; Interests from Social Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The abundance of user generated content on social networks provides the opportunity to build models that are able to accurately and effectively extract, mine and predict users' interests with the hopes of enabling more effective user engagement, better quality delivery of appropriate services and higher user satisfaction. While traditional methods for building user profiles relied on AI-based preference elicitation techniques that could have been considered to be intrusive and undesirable by the users, more recent advances are focused on a non-intrusive yet accurate way of determining users' interests and preferences. In this tutorial, we cover five important aspects related to the effective mining of user interests: (1) we introduce the information sources that are used for extracting user interests, (2) various types of user interest profiles that have been proposed in the literature, (3) techniques that have been adopted or proposed for mining user interests, (4) the scalability and resource requirements of the state of the art methods, and finally (5) the evaluation methodologies that are adopted in the literature for validating the appropriateness of the mined user interest profiles. We also introduce existing challenges, open research question and exciting opportunities for further work.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331383"}, {"primary_key": "3115216", "vector": [], "sparse_vector": [], "title": "Neural-Network Lexical Translation for Cross-lingual IR from Text and Speech.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose a neural network model to estimate word translation probabilities for Cross-Lingual Information Retrieval (CLIR). The model estimates better probabilities for word translations than automatic word alignments alone, and generalizes to unseen source-target word pairs. We further improve the lexical neural translation model (and subsequently CLIR), by incorporating source word context, and by encoding the character sequences of input source words to generate translations of out-of-vocabulary words. To be effective, neural network models typically need training on large amounts of data labeled directly on the final task, in this case relevance to queries. In contrast, our approach only requires parallel data to train the translation model, and uses an unsupervised model to compute CLIR relevance scores.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331222"}, {"primary_key": "3115217", "vector": [], "sparse_vector": [], "title": "Information Needs, Queries, and Query Performance Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Oren <PERSON>", "<PERSON><PERSON>"], "summary": "The query performance prediction (QPP) task is to estimate the effectiveness of a search performed in response to a query with no relevance judgments. Existing QPP methods do not account for the effectiveness of a query in representing the underlying information need. We demonstrate the far-reaching implications of this reality using standard TREC-based evaluation of QPP methods: their relative prediction quality patterns vary with respect to the effectiveness of queries used to represent the information needs. Motivated by our findings, we revise the basic probabilistic formulation of the QPP task by accounting for the information need and its connection to the query. We further explore this connection by proposing a novel QPP approach that utilizes information about a set of queries representing the same information need. Predictors instantiated from our approach using a wide variety of existing QPP methods post prediction quality that substantially transcends that of applying these methods, as is standard, using a single query representing the information need. Additional in-depth empirical analysis of different aspects of our approach further attests to the crucial role of query effectiveness in QPP.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331253"}, {"primary_key": "3115220", "vector": [], "sparse_vector": [], "title": "Table2Vec: Neural Word and Entity Embeddings for Table Population and Retrieval.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tables contain valuable knowledge in a structured form. We employ neural language modeling approaches to embed tabular data into vector spaces. Specifically, we consider different table elements, such caption, column headings, and cells, for training word and entity embeddings. These embeddings are then utilized in three particular table-related tasks, row population, column population, and table retrieval, by incorporating them into existing retrieval models as additional semantic similarity signals. Evaluation results show that table embeddings can significantly improve upon the performance of state-of-the-art baselines.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331333"}, {"primary_key": "3115221", "vector": [], "sparse_vector": [], "title": "Outline Generation: Understanding the Inherent Content Structure of Documents.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we introduce and tackle the Outline Generation (OG) task, which aims to unveil the inherent content structure of a multi-paragraph document by identifying its potential sections and generating the corresponding section headings. Without loss of generality, the OG task can be viewed as a novel structured summarization task. To generate a sound outline, an ideal OG model should be able to capture three levels of coherence, namely the coherence between context paragraphs, that between a section and its heading, and that between context headings. The first one is the foundation for section identification, while the latter two are critical for consistent heading generation. In this work, we formulate the OG task as a hierarchical structured prediction problem, i.e., to first predict a sequence of section boundaries and then a sequence of section headings accordingly. We propose a novel hierarchical structured neural generation model, named HiStGen, for the task. Our model attempts to capture the three-level coherence via the following ways. First, we introduce a Markov paragraph dependency mechanism between context paragraphs for section identification. Second, we employ a section-aware attention mechanism to ensure the semantic coherence between a section and its heading. Finally, we leverage a Markov heading dependency mechanism and a review mechanism between context headings to improve the consistency and eliminate duplication between section headings. Besides, we build a novel Wriptsize IKI OG dataset, a public collection which consists of over 1.75 million document-outline pairs for research on the OG task. Experimental results on our benchmark dataset demonstrate that our model can significantly outperform several state-of-the-art sequential generation models for the OG task.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331208"}, {"primary_key": "3115222", "vector": [], "sparse_vector": [], "title": "Syntax-Aware Aspect-Level Sentiment Classification with Proximity-Weighted Convolution Network.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It has been widely accepted that Long Short-Term Memory (LSTM) network, coupled with attention mechanism and memory module, is useful for aspect-level sentiment classification. However, existing approaches largely rely on the modelling of semantic relatedness of an aspect with its context words, while to some extent ignore their syntactic dependencies within sentences. Consequently, this may lead to an undesirable result that the aspect attends on contextual words that are descriptive of other aspects. In this paper, we propose a proximity-weighted convolution network to offer an aspect-specific syntax-aware representation of contexts. In particular, two ways of determining proximity weight are explored, namely position proximity and dependency proximity. The representation is primarily abstracted by a bidirectional LSTM architecture and further enhanced by a proximity-weighted convolution. Experiments conducted on the SemEval 2014 benchmark demonstrate the effectiveness of our proposed approach compared with a range of state-of-the-art models is available at https://github.com/GeneZC/PWCN.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331351"}, {"primary_key": "3115223", "vector": [], "sparse_vector": [], "title": "Cross-Modal Interaction Networks for Query-Based Moment Retrieval in Videos.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Query-based moment retrieval aims to localize the most relevant moment in an untrimmed video according to the given natural language query. Existing works often only focus on one aspect of this emerging task, such as the query representation learning, video context modeling or multi-modal fusion, thus fail to develop a comprehensive system for further performance improvement. In this paper, we introduce a novel Cross-Modal Interaction Network (CMIN) to consider multiple crucial factors for this challenging task, including (1) the syntactic structure of natural language queries; (2) long-range semantic dependencies in video context and (3) the sufficient cross-modal interaction. Specifically, we devise a syntactic GCN to leverage the syntactic structure of queries for fine-grained representation learning, propose a multi-head self-attention to capture long-range semantic dependencies from video context, and next employ a multi-stage cross-modal interaction to explore the potential relations of video and query contents. The extensive experiments demonstrate the effectiveness of our proposed method.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331235"}, {"primary_key": "3115224", "vector": [], "sparse_vector": [], "title": "SIGIR 2019 Tutorial on Explainable Recommendation and Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qingyao Ai"], "summary": "Explainable recommendation and search attempt to develop models or methods that not only generate high-quality recommendation or search results, but also intuitive explanations of the results for users or system designers, which can help to improve the system transparency, persuasiveness, trustworthiness, and effectiveness, etc. This is even more important in personalized search and recommendation scenarios, where users would like to know why a particular product, web page, news report, or friend suggestion exists in his or her own search and recommendation lists. The tutorial focuses on the research and application of explainable recommendation and search algorithms, as well as their application in real-world systems such as search engine, e-commerce and social networks. The tutorial aims at introducing and communicating explainable recommendation and search methods to the community, as well as gathering researchers and practitioners interested in this research direction for discussions, idea communications, and research promotions.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331390"}, {"primary_key": "3115225", "vector": [], "sparse_vector": [], "title": "Generic Intent Representation in Web Search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Corby Rosset", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents GEneric iNtent Encoder (GEN Encoder) which learns a distributed representation space for user intent in search. Leveraging large scale user clicks from Bing search logs as weak supervision of user intent, GEN Encoder learns to map queries with shared clicks into similar embeddings end-to-end and then fine-tunes on multiple paraphrase tasks. Experimental results on an intrinsic evaluation task - query intent similarity modeling - demonstrate GEN Encoder's robust and significant advantages over previous representation methods. Ablation studies reveal the crucial role of learning from implicit user feedback in representing user intent and the contributions of multi-task learning in representation generality. We also demonstrate that GEN Encoder alleviates the sparsity of tail search traffic and cuts down half of the unseen queries by using an efficient approximate nearest neighbor search to effectively identify previous queries with the same search intent. Finally, we demonstrate distances between GEN encodings reflect certain information seeking behaviors in search sessions.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331198"}, {"primary_key": "3115226", "vector": [], "sparse_vector": [], "title": "EnsembleGAN: Adversarial Learning for Retrieval-Generation Ensemble Model on Short-Text Conversation.", "authors": ["<PERSON><PERSON><PERSON>", "Chongyang Tao", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Generating qualitative responses has always been a challenge for human-computer dialogue systems. Existing dialogue systems generally derive from either retrieval-based or generative-based approaches, both of which have their own pros and cons. Despite the natural idea of an ensemble model of the two, existing ensemble methods only focused on leveraging one approach to enhance another, we argue however that they can be further mutually enhanced with a proper training strategy. In this paper, we propose ensembleGAN, an adversarial learning framework for enhancing a retrieval-generation ensemble model in open-domain conversation scenario. It consists of a language-model-like generator, a ranker generator, and one ranker discriminator. Aiming at generating responses that approximate the ground-truth and receive high ranking scores from the discriminator, the two generators learn to generate improved highly relevant responses and competitive unobserved candidates respectively, while the discriminative ranker is trained to identify true responses from adversarial ones, thus featuring the merits of both generator counterparts. The experimental results on a large short-text conversation data demonstrate the effectiveness of the ensembleGAN by the amelioration on both human and automatic evaluation metrics.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331193"}, {"primary_key": "3115228", "vector": [], "sparse_vector": [], "title": "EARS 2019: The 2nd International Workshop on ExplainAble Recommendation and Search.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Explainable recommendation and search attempt to develop models or methods that not only generate high-quality recommendation or search results, but also interpretability of the models or explanations of the results for users or system designers, which can help to improve the system transparency, persuasiveness, trustworthiness, and effectiveness, etc. This is even more important in personalized search and recommendation scenarios, where users would like to know why a particular product, web page, news report, or friend suggestion exists in his or her own search and recommendation lists. The workshop focuses on the research and application of explainable recommendation, search, and a broader scope of IR tasks. It will gather researchers as well as practitioners in the field for discussions, idea communications, and research promotions. It will also generate insightful debates about the recent regulations regarding AI interpretability, to a broader community including but not limited to IR, machine learning, AI, Data Science, and beyond.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331649"}, {"primary_key": "3115229", "vector": [], "sparse_vector": [], "title": "A Context-based Framework for Resource Citation Classification in Scientific Literatures.", "authors": ["<PERSON>", "Zhunchen Luo", "Chong Feng", "<PERSON><PERSON> Ye"], "summary": "In this paper, we introduce the task of resource citation classification for scientific literature using a context-based framework. This task is to analyze the purpose of citing an on-line resource in scientific text by modeling the role and function of each resource citation. It can be incorporated into resource indexing and recommendation systems to help better understand and classify on-line resources in scientific literature. We propose a new annotation scheme for this task and develop a dataset of 3,088 manually annotated resource citations. We adopt a neural-based model to build the classifiers and apply them on the large ARC dataset to examine the revolution of scientific resources from trends in their function over time.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331348"}, {"primary_key": "3115230", "vector": [], "sparse_vector": [], "title": "Gated Spectral Units: Modeling Co-evolving Patterns for Sequential Recommendation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Exploiting historical data of users to make future predictions lives at the heart of building effective recommender systems (RS). Recent approaches for sequential recommendations often render past actions of a user into a sequence, seeking to capture the temporal dynamics in the sequence to predict the next item. However, the interests of users evolve over time together due to their mutual influence, and most of existing methods lack the ability to utilize the rich coevolutionary patterns available in underlying data represented by sequential graphs. In order to capture the co-evolving knowledge for sequential recommendations, we start from introducing an efficient spectral convolution operation to discover complex relationships between users and items from the spectral domain of a graph, where the hidden connectivity information of the graph can be revealed. Then, the spectral convolution is generalized into an recurrent method by utilizing gated mechanisms to model sequential graphs. Experimentally, we demonstrate the advantages of modeling co-evolving patterns, and Gated Spectral Units (GSUs) achieve state-of-the-art performance on several benchmark datasets.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331329"}, {"primary_key": "3115231", "vector": [], "sparse_vector": [], "title": "Deep Distribution Network: Addressing the Data Sparsity Issue for Top-N Recommendation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Existing recommendation methods mostly learn fixed vectors for users and items in a low-dimensional continuous space, and then calculate the popular dot-product to derive user-item distances. However, these methods suffer from two drawbacks: (1) the data sparsity issue prevents from learning high-quality representations; and (2) the dot-product violates the crucial triangular inequality and therefore, results in a sub-optimal performance. In this work, in order to overcome the two aforementioned drawbacks, we propose Deep Distribution Network (DDN) to model users and items via Gaussian distributions. We argue that, compared to fixed vectors, distribution-based representations are more powerful to characterize users' uncertain interests and items' distinct properties. In addition, we propose a Wasserstein-based loss, in which the critical triangular inequality can be satisfied. In experiments, we evaluate DDN and comparative models on standard datasets. It is shown that DDN significantly outperforms state-of-the-art models, demonstrating the advantages of the proposed distribution-based representations and wassertein loss.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331330"}, {"primary_key": "3115232", "vector": [], "sparse_vector": [], "title": "Human Behavior Inspired Machine Reading Comprehension.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Machine Reading Comprehension (MRC) is one of the most challenging tasks in both NLP and IR researches. Recently, a number of deep neural models have been successfully adopted to some simplified MRC task settings, whose performances were close to or even better than human beings. However, these models still have large performance gaps with human beings in more practical settings, such as MS MARCO and DuReader datasets. Although there are many works studying human reading behavior, the behavior patterns in complex reading comprehension scenarios remain under-investigated. We believe that a better understanding of how human reads and allocates their attention during reading comprehension processes can help improve the performance of MRC tasks. In this paper, we conduct a lab study to investigate human's reading behavior patterns during reading comprehension tasks, where 32 users are recruited to take 60 distinct tasks. By analyzing the collected eye-tracking data and answers from participants, we propose a two-stage reading behavior model, in which the first stage is to search for possible answer candidates and the second stage is to generate the final answer through a comparison and verification process. We also find that human's attention distribution is affected by both question-dependent factors (e.g., answer and soft matching signal with questions) and question-independent factors (e.g., position, IDF and Part-of-Speech tags of words). We extract features derived from the two-stage reading behavior model to predict human's attention signals during reading comprehension, which significantly improves performance in the MRC task. Findings in our work may bring insight into the understanding of human reading and information seeking processes, and help the machine to better meet users' information needs.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331231"}, {"primary_key": "3115234", "vector": [], "sparse_vector": [], "title": "Legal Intelligence for E-commerce: Multi-task Learning by Leveraging Multiview Dispute Representation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Various e-commerce platforms produce millions of transactions per day with many transaction disputes. This generates the demand for effective and efficient dispute resolutions for e-commerce transactions. This paper proposes a novel research task of Legal Dispute Judgment (LDJ) prediction for e-commerce transactions, which connects two yet isolated domains, e-commerce data mining and legal intelligence. Different from traditional legal intelligence with the focus on textual evidence of the dispute itself, the new research utilizes multiview information such as past behavior information of seller and buyer as well as textual evidence of the current transaction. The multiview dispute representation is integrated into an innovative multi-task learning framework for predicting the legal result. An extensive set of experiments with a large dispute case dataset collected from a world leading e-commerce platform shows that the proposed model can more accurately characterize a dispute case through buyer, seller, and transaction viewpoints for legal judgment prediction against several alternatives.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331212"}, {"primary_key": "3115235", "vector": [], "sparse_vector": [], "title": "TDP: Personalized Taxi Demand Prediction Based on Heterogeneous Graph Embedding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yu<PERSON> Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Predicting users' irregular trips in a short term period is one of the crucial tasks in the intelligent transportation system. With the prediction, the taxi requesting services, such as <PERSON><PERSON> in China, can manage the transportation resources to offer better services. There are several different transportation scenes, such as commuting scene and entertainment scene. The origin and the destination of entertainment scene are more unsure than that of commuting scene, so both origin and destination should be predicted. Moreover, users' trips on Didi platform is only a part of their real life, so these transportation data are only few weak samples. To address these challenges, in this paper, we propose Taxi Demand Prediction (TDP) model in challenging entertainment scene based on heterogeneous graph embedding and deep neural predicting network. TDP aims to predict next possible trip edges that have not appeared in historical data for each user in entertainment scene. Experimental results on the real-world dataset show that TDP achieves significant improvements over the state-of-the-art methods.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331368"}, {"primary_key": "3115236", "vector": [], "sparse_vector": [], "title": "Triple-to-Text: Converting RDF Triples into High-Quality Natural Languages via Optimizing an Inverse KL Divergence.", "authors": ["<PERSON><PERSON>", "Juncheng Wan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Knowledge base is one of the main forms to represent information in a structured way. A knowledge base typically consists of Resource Description Frameworks (RDF) triples which describe the entities and their relations. Generating natural language description of the knowledge base is an important task in NLP, which has been formulated as a conditional language generation task and tackled using the sequence-to-sequence framework. Current works mostly train the language models by maximum likelihood estimation, which tends to generate lousy sentences. In this paper, we argue that such a problem of maximum likelihood estimation is intrinsic, which is generally irrevocable via changing network structures. Accordingly, we propose a novel Triple-to-Text (T2T) framework, which approximately optimizes the inverse Kullback-Leibler (KL) divergence between the distributions of the real and generated sentences. Due to the nature that inverse KL imposes large penalty on fake-looking samples, the proposed method can significantly reduce the probability of generating low-quality sentences. Our experiments on three real-world datasets demonstrate that T2T can generate higher-quality sentences and outperform baseline models in several evaluation metrics.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331232"}, {"primary_key": "3115237", "vector": [], "sparse_vector": [], "title": "Simulacra and Selection: Clothing Set Recommendation at Stitch Fix.", "authors": ["<PERSON>"], "summary": "Choosing a set of items from an inventory is a common problem faced by customers of nearly any retailer. Modern retailers aid and influence customer decisions, using techniques like recommender systems and market basket analysis to deliver personalized and contextual item suggestions. While such methods typically just augment a traditional browsing experience, Stitch Fix goes a step further by exclusively delivering curated selections of items, via algorithmically-assisted stylist recommendations. Our understanding of the human-in-the-loop element in this selection process is crucial to building effective tools to assist stylists in delivering personalized items. When stylists at Stitch Fix select an assortment of clothing for a client, they consider many nuanced relationships between the items, as well as with the client. For example, a stylist may fill a client's request for a work outfit by choosing pants and a blouse that pair well together, as well as fitting the client's stated preferences and her inferred sense of style. This can be described as a contextual subset selection problem, where the goal is to assemble a conditionally optimal set of items, given inventory constraints and context. I will discuss our nonlinear factorization approach", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331442"}, {"primary_key": "3115238", "vector": [], "sparse_vector": [], "title": "Investigating the Interplay Between Searchers&apos; Privacy Concerns and Their Search Behavior.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Privacy concerns are becoming a dominant focus in search applications, thus there is a growing need to understand implications of efforts to address these concerns. Our research investigates a search system with privacy warning labels, an approach inspired by decision making research on food nutrition labels. This approach is designed to alert users to potential privacy threats in their search for information as one possible avenue to address privacy concerns. Our primary goal is to understand the extent to which attitudes towards privacy are linked to behaviors that protect privacy. In the present study, participants were given a set of fact-based decision tasks from the domain of health search. Participants were rotated through variations of search engine results pages (SERPs) including a SERP with a privacy warning light system. Lastly, participants completed a survey to capture attitudes towards privacy, behaviors to protect privacy, and other demographic information. In addition to the comparison of interactive search behaviors of a privacy warning SERP with a control SERP, we compared self-report privacy measures with interactive search behaviors. Participants reported strong concerns around privacy of health information while simultaneously placing high importance on the correctness of this information. Analysis of our interactive experiment and self-report privacy measures indicate that 1) choice of privacy-protective browsers has a significant link to privacy attitudes and privacy-protective behaviors in a SERP and 2) there are no significant links between reported concerns towards privacy and recorded behavior in an information retrieval system with warnings that enable users to protect their privacy.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331280"}, {"primary_key": "3115239", "vector": [], "sparse_vector": [], "title": "Efficient and Effective Text-Annotation through Active Learning.", "authors": ["<PERSON>"], "summary": "The most commonly used active learning criterion is uncertainty sampling, where a supervised model is used to predict uncertain samples that should be labeled next by a human annotator. When using active learning, two problems are encountered: First, the supervised model has a cold-start problem in the beginning of the active learning process, and second, the human annotators might make labeling mistakes. In my Ph.D. research, I address the two problems with the development of an unsupervised method for the computation of informative samples. The informative samples are first manually labeled and then used for both: The training of the initial active learning model and the training of the human annotators in form of a learning-by-doing session. The planned unsupervised method will be based on word-embeddings and it will be limited to the area of text classification.", "published": "2019-01-01", "category": "sigir", "pdf_url": "", "sub_summary": "", "source": "sigir", "doi": "10.1145/3331184.3331424"}]