[{"primary_key": "583419", "vector": [], "sparse_vector": [], "title": "Jade: A High-throughput Concurrent Copying Garbage Collector.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Jin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>"], "summary": "Garbage collection (GC) pauses are a notorious issue threatening the latency of applications. To mitigate this problem, state-of-the-art concurrent copying collectors allow GC threads to run simultaneously with application threads (mutators) in nearly all GC phases. However, the design of concurrent copying collectors does not always lead to low application latency. To this end, this work studies the behaviors of mainstream concurrent copying collectors in OpenJDK and mainly focuses on long application pauses under heavy workloads. By analyzing the design of those collectors, this work uncovers that lengthy pre-reclamation cycles (including GC phases before actual memory release), high GC frequency, and large metadata maintenance overhead are major factors for long pauses. Therefore, this work proposes Jade, a concurrent copying collector aiming to achieve both short pauses and high GC efficiency. Compared with existing collectors, <PERSON> provides a group-wise collection mechanism to shorten pre-reclamation cycles while controlling GC frequency. It also embraces a generational heap layout and a single-phase algorithm to maximize young GC's throughput. The evaluation results on representative latency-critical applications show that Jade can reach sub-millisecond-level pauses even under heavy workloads and significantly improve applications' peak throughput compared with state-of-the-art concurrent collectors.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650087"}, {"primary_key": "583420", "vector": [], "sparse_vector": [], "title": "Core Graph: Exploiting Edge Centrality to Speedup the Evaluation of Iterative Graph Queries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "When evaluating an iterative graph query over a large graph, systems incur significant overheads due to repeated graph transfer across the memory hierarchy coupled with repeated (redundant) propagation of values over the edges in the graph. An approach for reducing these overheads combines the use of a small proxy graph and the large original graph in a two phase query evaluation. The first phase evaluates the query on the proxy graph incurring low overheads and producing mostly precise results. The second phase uses these mostly precise results to bootstrap query evaluation on the larger original graph producing fully precise results. The effectiveness of this approach depends upon the quality of the proxy graph. Prior methods find proxy graphs that are either large or produce highly imprecise results.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629571"}, {"primary_key": "583421", "vector": [], "sparse_vector": [], "title": "Characterization and Reclamation of Frozen Garbage in Managed FaaS Workloads.", "authors": ["Zim<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen", "<PERSON><PERSON>"], "summary": "FaaS (function-as-a-service) is becoming a popular workload in cloud environments due to its virtues such as auto-scaling and pay-as-you-go. High-level languages like JavaScript and Java are commonly used in FaaS for programmability, but their managed runtimes complicate memory management in the cloud. This paper first observes the issue of frozen garbage, which is caused by freezing cached function instances where their threads have been paused but the unused memory (e.g., garbage) is not reclaimed due to the semantic gap between FaaS and the managed runtime. This paper presents the first characterization of the negative effects induced by frozen garbage with various functions, which uncovers that it can occupy more than half of FaaS instances' memory resources on average. To this end, this paper proposes Desiccant, a freeze-aware memory manager for managed workloads in FaaS, which reclaims idle memory resources consumed by frozen garbage from managed runtime instances and thus notably improves memory efficiency. The evaluation on various FaaS workloads shows that Desiccant can reduce FaaS functions' peak memory consumption by up to 6.72×. Such saved memory consumption allows caching more FaaS instances to reduce the frequency of cold boots (creating instances before function execution) and p99 latency by up to 4.49× and 37.5%, respectively.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629579"}, {"primary_key": "583422", "vector": [], "sparse_vector": [], "title": "MTM: Rethinking Memory Profiling and Migration for Multi-Tiered Large Memory.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Multi-terabyte large memory systems are often characterized by more than two memory tiers with different latency and bandwidth. Multi-tiered large memory systems call for rethinking of memory profiling and migration because of the unique problems unseen in the traditional memory systems with smaller capacity and fewer tiers. We develop MTM, an application-transparent Multi-Tiered Memory management framework, based on three principles: (1) connecting the control of profiling overhead with the profiling mechanism for high-quality profiling; (2) building a universal page migration policy on the complex multi-tiered memory for high performance; and (3) introducing huge page awareness. We evaluate MTM using common big-data applications with realistic working sets (hundreds of GB to 1 TB). MTM outperforms seven solutions by up to 42% (17% on average).", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650075"}, {"primary_key": "583423", "vector": [], "sparse_vector": [], "title": "Blox: A Modular Toolkit for Deep Learning Schedulers.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deep Learning (DL) workloads have rapidly increased in popularity in enterprise clusters and several new cluster schedulers have been proposed in recent years to support these workloads. With rapidly evolving DL workloads, it is challenging to quickly prototype and compare scheduling policies across workloads. Further, as prior systems target different aspects of scheduling (resource allocation, placement, elasticity etc.), it is also challenging to combine these techniques and understand the overall benefits. To address these challenges we propose Blox, a modular toolkit which allows developers to compose individual components and realize diverse scheduling frameworks. We identify a set of core abstractions for DL scheduling, implement several existing schedulers using these abstractions, and verify the fidelity of these implementations by reproducing results from prior research. We also highlight how we can evaluate and compare existing schedulers in new settings: different workload traces, higher cluster load, change in DNN workloads and deployment characteristics. Finally, we showcase Blox's extensibility by composing policies from different schedulers, and implementing novel policies with minimal code changes. Blox is available at https://github.com/msr-fiddle/blox.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629583"}, {"primary_key": "583424", "vector": [], "sparse_vector": [], "title": "Unison: A Parallel-Efficient and User-Transparent Network Simulation Kernel.", "authors": ["<PERSON><PERSON> Bai", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Xi<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Discrete-event simulation (DES) is a prevalent tool for evaluating network designs. Although DES offers full fidelity and generality, its slow performance limits its application. To speed up DES, many network simulators employ parallel discrete-event simulation (PDES). However, adapting existing network simulation models to PDES requires complex reconfigurations and often yields limited performance improvement. In this paper, we address this gap by proposing a parallel-efficient and user-transparent network simulation kernel, Unison, that adopts fine-grained partition and load-adaptive scheduling optimized for network scenarios. We prototype Unison based on ns-3. Existing network simulation models of ns-3 can be seamlessly transitioned to Unison. Testbed experiments on commodity servers demonstrate that Unison can achieve a 40× speedup over DES using 24 CPU cores, and a 10× speedup compared with existing PDES algorithms under the same CPU cores.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629574"}, {"primary_key": "583425", "vector": [], "sparse_vector": [], "title": "Contigra: Graph Mining with Containment Constraints.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While graph mining systems employ efficient task-parallel strategies to quickly explore subgraphs of interest (or matches), they remain oblivious to containment constraints like maximality and minimality, resulting in expensive constraint checking on every explored match as well as redundant explorations that limit their scalability.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629589"}, {"primary_key": "583426", "vector": [], "sparse_vector": [], "title": "ZKML: An Optimizing System for ML Inference in Zero-Knowledge Proofs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Suppakit Waiwitlikhit", "Ion <PERSON>", "<PERSON>"], "summary": "Machine learning (ML) is increasingly used behind closed systems and APIs to make important decisions. For example, social media uses ML-based recommendation algorithms to decide what to show users, and millions of people pay to use ChatGPT for information every day. Because ML is deployed behind these closed systems, there are increasing calls for transparency, such as releasing model weights. However, these service providers have legitimate reasons not to release this information, including for privacy and trade secrets. To bridge this gap, recent work has proposed using zero-knowledge proofs (specifically a form called ZK-SNARKs) for certifying computation with private models but has only been applied to unrealistically small models.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650088"}, {"primary_key": "583427", "vector": [], "sparse_vector": [], "title": "Automatic Root Cause Analysis via Large Language Models for Cloud Incidents.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Minghua Ma", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Gao", "<PERSON><PERSON> Fan", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Ensuring the reliability and availability of cloud services necessitates efficient root cause analysis (RCA) for cloud incidents. Traditional RCA methods, which rely on manual investigations of data sources such as logs and traces, are often laborious, error-prone, and challenging for on-call engineers. In this paper, we introduce RCACopilot, an innovative on-call system empowered by the large language model for automating RCA of cloud incidents. RCACopilot matches incoming incidents to corresponding incident handlers based on their alert types, aggregates the critical runtime diagnostic information, predicts the incident's root cause category, and provides an explanatory narrative. We evaluate RCACopilot using a real-world dataset consisting of a year's worth of incidents from Microsoft. Our evaluation demonstrates that RCACopilot achieves RCA accuracy up to 0.766. Furthermore, the diagnostic information collection component of RCACopilot has been successfully in use at Microsoft for over four years.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629553"}, {"primary_key": "583428", "vector": [], "sparse_vector": [], "title": "DeTA: Minimizing Data Leaks in Federated Learning via Decentralized and Trustworthy Aggregation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Zhongshu Gu", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Federated learning (FL) relies on a central authority to oversee and aggregate model updates contributed by multiple participating parties in the training process. This centralization of sensitive model updates naturally raises concerns about the trustworthiness of the central aggregation server, as well as the potential risks associated with server failures or breaches, which could result in loss and leaks of model updates. Moreover, recent attacks have demonstrated that, by obtaining the leaked model updates, malicious actors can even reconstruct substantial amounts of private data belonging to training participants. This underscores the critical necessity to rethink the existing FL system architecture to mitigate emerging attacks in the evolving threat landscape. One straightforward approach is to fortify the central aggregator with confidential computing (CC), which offers hardware-assisted protection for runtime computation and can be remotely verified for execution integrity. However, a growing number of security vulnerabilities have surfaced in tandem with the adoption of CC, indicating that depending solely on this singular defense may not provide the requisite resilience to thwart data leaks.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650082"}, {"primary_key": "583429", "vector": [], "sparse_vector": [], "title": "Totoro: A Scalable Federated Learning Engine for the Edge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON>", "Liting Hu"], "summary": "Federated Learning (FL) is an emerging distributed machine learning (ML) technique that enables in-situ model training and inference on decentralized edge devices. We propose Totoro, a novel scalable FL engine, that enables massive FL applications to run simultaneously on edge networks. The key insight is to explore a distributed hash table (DHT)-based peer-to-peer (P2P) model to re-architect the centralized FL system design into a fully decentralized one. In contrast to previous studies where many FL applications shared one centralized parameter server, Totoro assigns a dedicated parameter server to each individual application. Any edge node can act as any application's coordinator, aggregator, client selector, worker (participant device), or any combination of the above, thereby radically improving scalability and adaptivity. Totoro introduces three innovations to realize its design: a locality-aware P2P multi-ring structure, a publish/subscribe-based forest abstraction, and a bandit-based exploitation-exploration path planning model. Real-world experiments on 500 Amazon EC2 servers show that Totoro scales gracefully with the number of FL applications and N edge nodes, speeds up the total training time by 1.2 × -14.0×, achieves O (logN) hops for model dissemination and gradient aggregation with millions of nodes, and efficiently adapts to the practical edge networks and churns.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629575"}, {"primary_key": "583430", "vector": [], "sparse_vector": [], "title": "Atlas: Hybrid Cloud Migration Advisor for Interactive Microservices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Hybrid cloud provides an attractive solution to microservices for better resource elasticity. A subset of application components can be offloaded from the on-premises cluster to the cloud, where they can readily access additional resources. However, the selection of this subset is challenging because of the large number of possible combinations. A poor choice degrades the application performance, disrupts the critical services, and increases the cost to the extent of making the use of hybrid cloud unviable. This paper presents <PERSON>, a hybrid cloud migration advisor. Atlas uses a data-driven approach to learn how each user-facing API utilizes different components and their network footprints to drive the migration decision. It learns to accelerate the discovery of high-quality migration plans from millions and offers recommendations with customizable trade-offs among three quality indicators: end-to-end latency of user-facing APIs representing application performance, service availability, and cloud hosting costs. Atlas continuously monitors the application even after the migration for proactive recommendations. Our evaluation shows that Atlas can achieve 21% better API performance (latency) and 11% cheaper cost with less service disruption than widely used solutions.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629587"}, {"primary_key": "583431", "vector": [], "sparse_vector": [], "title": "Validating Database System Isolation Level Implementations with Version Certificate Recovery.", "authors": ["<PERSON>", "Alastair F<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Transactions are a key feature of database systems and isolation levels specify the behavior of concurrently executing transactions. Ensuring their correct behavior is crucial. Recently, many isolation anomalies have been found in production database systems. Checkers can be used to validate that a particular execution conforms to a desired isolation level. However, state-of-the-art checkers cannot handle predicate operations, which are both common in real-world workloads and essential for distinguishing between the repeatable read and serializable isolation levels. In this work, we address this issue by proposing an efficient white-box checker, Emme. Our key idea is to use information that is easily provided by database systems to efficiently check the isolation level of a given transaction history. We present version certificate recovery, a method of recovering the version order and each operation's version from the database system under test. For efficiency, we also propose the concept of an expected serialization order, which obviates the need to define and recover a version certificate for many serializable concurrency control protocols. We have implemented version certificate recovery for three widely used database systems---PostgreSQL, CockroachDB, and TiDB. We demonstrate that Emme is 1.2-3.6× faster than Elle, a state-of-the-art checker. Using the expected serialization order, we obtain a further speedup of 34-430× compared to <PERSON><PERSON> when checking histories containing predicate operations. We show that our approach can identify invalid histories that cannot be detected by <PERSON> and also show that it can find realistic bugs purposely introduced by an engineer.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650080"}, {"primary_key": "583432", "vector": [], "sparse_vector": [], "title": "Polynima: Practical Hybrid Recompilation for Multithreaded Binaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The maintenance of software distributed in its binary form can become challenging over time, due to the lack of vendor support or obsolete build environments. This can be costly when dealing with critical security vulnerabilities that are difficult to fix on a binary level. Moreover, advances in compiler technologies of the past decades remain unavailable to the users of such legacy binaries for performing optimizations and transformations. Binary recompilers aim to bridge this divide by \"lifting\" binary executables to compiler-level intermediate representations (IR) and \"lowering\" them back again. But, current recompilers fail on that promise as they rely on unsound heuristics or impose high tracing overheads. Crucially, no existing recompiler addresses the specific challenges imposed by multithreaded programs that are ubiquitous in the modern software space.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650065"}, {"primary_key": "583433", "vector": [], "sparse_vector": [], "title": "Dashing and Star: Byzantine Fault Tolerance with Weak Certificates.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gang Di", "<PERSON><PERSON>"], "summary": "State-of-the-art Byzantine fault-tolerant (BFT) protocols assuming partial synchrony such as SBFT and HotStuff use regular certificates obtained from 2f + 1 (partial) signatures. We show that one can use weak certificates obtained from only f + 1 signatures to assist in designing more robust and more efficient BFT protocols. We design and implement two BFT systems: Dashing (a family of two HotStuff-style BFT protocols) and Star (a parallel BFT framework).", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650073"}, {"primary_key": "583434", "vector": [], "sparse_vector": [], "title": "FLOWS: Balanced MRC Profiling for Heterogeneous Object-Size Cache.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Hong Jiang", "Yaodong Han", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "While Miss Ratio Curve (MRC) profiling methods based on spatial sampling are effective in modeling cache behaviors, previous MRC studies lack in-depth analysis of profiling errors and primarily target homogeneous object-size scenarios. This has caused imbalanced errors of existing MRC approaches when employed in heterogeneous object-size caches. For instance, in CDN traces, the error of the Byte Miss Ratio Curve (BMRC) could be two orders of magnitude larger than that of the Object Miss Ratio Curve (OMRC).", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650078"}, {"primary_key": "583435", "vector": [], "sparse_vector": [], "title": "Just-In-Time Checkpointing: Low Cost Error Recovery from Deep Learning Training Failures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bhargav S. Gulavani", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep Learning training jobs process large amounts of training data using many GPU devices, often running for weeks or months. When hardware or software failures happen, these jobs need to restart, losing the memory state for the Deep Neural Network (DNN) model trained so far, unless checkpointing mechanisms are used to save training state periodically. However, for large models, periodic checkpointing incurs significant steady state overhead, and during recovery, a large number of GPUs need to redo work since the last checkpoint. This is especially problematic when failures are frequent for large DNN (such as Large Language Model) training jobs using many GPUs. In this paper, we present a novel approach of just-in-time checkpointing when failures happen, which enables recovery from failures with just a single minibatch iteration of work replayed by all GPUs. This reduces the cost of error recovery from several minutes to a few seconds per GPU, with nearly zero steady state overhead. This also avoids the guesswork of choosing a checkpointing frequency since failure rates usually have high variance. We discuss how just-in-time checkpointing can be enabled in training code, as well as design of key mechanisms for transparent just-in-time checkpointing without user code change. We analyze the wasted GPU work of just-in-time checkpointing and show that it is less than periodic checkpointing for large numbers of GPUs. We present results from our implementation in modern AI cluster infrastructure.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650085"}, {"primary_key": "583436", "vector": [], "sparse_vector": [], "title": "Optimus: Warming Serverless ML Inference via Inter-Function Model Transformation.", "authors": ["Zicong Hong", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Serverless ML inference is an emerging cloud computing paradigm for low-cost, easy-to-manage inference services. In serverless ML inference, each call is executed in a container; however, the cold start of containers results in long inference delays. Unfortunately, most existing works do not work well because they still need to load models into containers from scratch, which is the bottleneck based on our observations. Therefore, this paper proposes a low-latency serverless ML inference system called Optimus via a new container management scheme. Our key insight is that the loading of a new model can be significantly accelerated when using an existing model with a similar structure in a warm but idle container. We thus develop a novel idea of inter-function model transformation for serverless ML inference, which delves into models within containers at a finer granularity of operations, designs a set of in-container meta-operators for both CNN and transformer model transformation, and develops an efficient scheduling algorithm with linear complexity for a low-cost transformation strategy. Our evaluations on thousands of models show that Optimus reduces inference latency by 24.00% ~ 47.56% in both simulated and real-world workloads compared to state-of-the-art work.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629567"}, {"primary_key": "583437", "vector": [], "sparse_vector": [], "title": "CDMPP: A Device-Model Agnostic Framework for Latency Prediction of Tensor Programs.", "authors": ["<PERSON><PERSON><PERSON> Hu", "Junwei Su", "<PERSON><PERSON><PERSON>", "Yanghua Peng", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) have shown excellent performance in a wide range of machine learning applications. Knowing the latency of running a DNN model or tensor program on a specific device is useful in various tasks, such as DNN graph- or tensor-level optimization and device selection. Considering the large space of DNN models and devices that impedes direct profiling of all combinations, recent efforts focus on building a predictor to model the performance of DNN models on different devices. However, none of the existing attempts have achieved a cost model that can accurately predict the performance of various tensor programs while supporting both training and inference accelerators. We propose CDMPP, an efficient tensor program latency prediction framework for both cross-model and cross-device prediction. We design an informative but efficient representation of tensor programs, called compact ASTs, and a pre-order-based positional encoding method, to capture the internal structure of tensor programs. We develop a domain-adaption-inspired method to learn domain-invariant representations and devise a KMeans-based sampling algorithm, for the predictor to learn from different domains (i.e., different DNN operators and devices). Our extensive experiments on a diverse range of DNN models and devices demonstrate that CDMPP significantly outperforms state-of-the-art baselines with 14.03% and 10.85% prediction error for cross-model and cross-device prediction, respectively, and one order of magnitude higher training efficiency. The implementation and the expanded dataset are available at https://github.com/joapolarbear/cdmpp.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629572"}, {"primary_key": "583438", "vector": [], "sparse_vector": [], "title": "Accelerating Privacy-Preserving Machine Learning With GeniBatch.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cross-silo privacy-preserving machine learning (PPML) adopt; Partial Homomorphic Encryption (PHE) for secure data combination and high-quality model training across multiple organizations (e.g., medical and financial). However, PHE introduces significant computation and communication overheads due to data inflation. Batch optimization is an encouraging direction to mitigate the problem by compressing multiple data into a single ciphertext. While promising, it is impractical for a large number of cross-silo PPML applications due to the limited vector operations support and severe data corruption.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629563"}, {"primary_key": "583439", "vector": [], "sparse_vector": [], "title": "WiseGraph: Optimizing GNN with Joint Workload Partition of Graph and Operations.", "authors": ["<PERSON><PERSON><PERSON>", "Jidong Zhai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph Neural Network (GNN) has emerged as an important workload for learning on graphs. With the size of graph data and the complexity of GNN model architectures increasing, developing an efficient GNN system grows more important. As GNN has heavy neural computation workloads on a large graph, it is crucial to partition the entire workload into smaller parts for parallel execution and optimization. However, existing approaches separately partition graph data and GNN operations, resulting in inefficiency and large data movement overhead.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650063"}, {"primary_key": "583440", "vector": [], "sparse_vector": [], "title": "Dordis: Efficient Federated Learning with Dropout-Resilient Differential Privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) is increasingly deployed among multiple clients to train a shared model over decentralized data. To address privacy concerns, FL systems need to safeguard the clients' data from disclosure during training and control data leakage through trained models when exposed to untrusted domains. Distributed differential privacy (DP) offers an appealing solution in this regard as it achieves a balanced tradeoff between privacy and utility without a trusted server. However, existing distributed DP mechanisms are impractical in the presence of client dropout, resulting in poor privacy guarantees or degraded training accuracy. In addition, these mechanisms suffer from severe efficiency issues.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629559"}, {"primary_key": "583441", "vector": [], "sparse_vector": [], "title": "DynaPipe: Optimizing Multi-task Training through Dynamic Pipelines.", "authors": ["Chenyu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multi-task model training has been adopted to enable a single deep neural network model (often a large language model) to handle multiple tasks (e.g., question answering and text summarization). Multi-task training commonly receives input sequences of highly different lengths due to the diverse contexts of different tasks. Padding (to the same sequence length) or packing (short examples into long sequences of the same length) is usually adopted to prepare input samples for model training, which is nonetheless not space or computation efficient. This paper proposes a dynamic micro-batching approach to tackle sequence length variation and enable efficient multi-task model training. We advocate pipelineparallel training of the large model with variable-length micro-batches, each of which potentially comprises a different number of samples. We optimize micro-batch construction using a dynamic programming-based approach, and handle micro-batch execution time variation through dynamic pipeline and communication scheduling, enabling highly efficient pipeline training. Extensive evaluation on the FLANv2 dataset demonstrates up to 4.39x higher training throughput when training T5, and 3.25x when training GPT, as compared with packing-based baselines. DynaPipe's source code is publicly available at https://github.com/awslabs/optimizing-multitask-training-through-dynamic-pipelines.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629585"}, {"primary_key": "583442", "vector": [], "sparse_vector": [], "title": "Concealing Compression-accelerated I/O for HPC Applications through In Situ Task Scheduling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Lossy compression and asynchronous I/O are two of the most effective solutions for reducing storage overhead and enhancing I/O performance in large-scale high-performance computing (HPC) applications. However, current approaches have limitations that prevent them from fully leveraging lossy compression, and they may also result in task collisions, which restrict the overall performance of HPC applications. To address these issues, we propose an optimization approach for the task scheduling problem that encompasses computation, compression, and I/O. Our algorithm adaptively selects the optimal compression and I/O queue to minimize the performance degradation of the computation. We also introduce an intra-node I/O workload balancing mechanism that evenly distributes the workload across different processes. Additionally, we design a framework that incorporates fine-grained compression, a compressed data buffer, and a shared <PERSON><PERSON><PERSON> tree to fully benefit from our proposed task scheduling. Experimental results with up to 16 nodes and 64 GPUs from ORNL Summit, as well as real-world HPC applications, demonstrate that our solution reduces I/O overhead by up to 3.8× and 2.6× compared to non-compression and asynchronous I/O solutions, respectively.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629573"}, {"primary_key": "583443", "vector": [], "sparse_vector": [], "title": "FLOAT: Federated Learning Optimizations with Automated Tuning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Federated Learning (FL) has emerged as a powerful approach that enables collaborative distributed model training without the need for data sharing. However, FL grapples with inherent heterogeneity challenges leading to issues such as stragglers, dropouts, and performance variations. Selection of clients to run an FL instance is crucial, but existing strategies introduce biases and participation issues and do not consider resource efficiency. Communication and training acceleration solutions proposed to increase client participation also fall short due to the dynamic nature of system resources. We address these challenges in this paper by designing FLOAT, a novel framework designed to boost FL client resource awareness. FLOAT optimizes resource utilization dynamically for meeting training deadlines, and mitigates stragglers and dropouts through various optimization techniques; leading to enhanced model convergence and improved performance. FLOAT leverages multi-objective Reinforcement Learning with Human Feedback (RLHF) to automate the selection of the optimization techniques and their configurations, tailoring them to individual client resource conditions. Moreover, FLOAT seamlessly integrates into existing FL systems, maintaining non-intrusiveness and versatility for both asynchronous and synchronous FL settings. As per our evaluations, FLOAT increases accuracy by up to 53%, reduces client dropouts by up to 78×, and improves communication, computation, and memory utilization by up to 81×, 44×, and 20× respectively.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650081"}, {"primary_key": "583444", "vector": [], "sparse_vector": [], "title": "Pronghorn: Effective Checkpoint Orchestration for Serverless Hot-Starts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Serverless computing allows developers to deploy and scale stateless functions in ephemeral workers easily. As a result, serverless computing has been widely used for many applications, such as computer vision, video processing, and HTML generation. However, we find that the stateless nature of serverless computing wastes many of the important benefits modern language runtimes have to offer. A notable example is the extensive profiling and Just-in-Time (JIT) compilation effort that runtimes implement to achieve acceptable performance of popular high-level languages, such as Java, JavaScript, and Python. Unfortunately, when modern language runtimes are naively adopted in serverless computing, all of these efforts are lost upon worker eviction. Checkpoint-restore methods alleviate the problem by resuming workers from snapshots taken after initialization. However, production-grade language runtimes can take up to thousands of invocations to fully optimize a single function, thus rendering naive checkpoint-restore policies ineffective.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629556"}, {"primary_key": "583445", "vector": [], "sparse_vector": [], "title": "CCL-BTree: A Crash-Consistent Locality-Aware B+-Tree for Reducing XPBuffer-Induced Write Amplification in Persistent Memory.", "authors": ["<PERSON><PERSON><PERSON>", "Shuibing <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xuechen <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In persistent B+ -Tree, random updates of small key-value (KV) pairs will cause severe XPBuffer-induced write amplification (XBI-amplification) because CPU cacheline size is smaller than media access granularity in persistent memory (PM). We observe that XBI-amplification directly determines the application performance when the PM bandwidth is exhausted in multi-thread scenarios. However, none of the existing work can efficiently address the XBI-amplification issue while maintaining superior range query performance.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629582"}, {"primary_key": "583446", "vector": [], "sparse_vector": [], "title": "Astraea: Towards Fair and Efficient Learning-based Congestion Control.", "authors": ["Xudong Liao", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>nchen Wan", "<PERSON>"], "summary": "Recent years have witnessed a plethora of learning-based solutions for congestion control (CC) that demonstrate better performance over traditional TCP schemes. However, they fail to provide consistently good convergence properties, including fairness, fast convergence and stability, due to the mismatch between their objective functions and these properties. Despite being intuitive, integrating these properties into existing learning-based CC is challenging, because: 1) their training environments are designed for the performance optimization of single flow but incapable of cooperative multi-flow optimization, and 2) there is no directly measurable metric to represent these properties into the training objective function.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650069"}, {"primary_key": "583447", "vector": [], "sparse_vector": [], "title": "Halflife: An Adaptive Flowlet-based Load Balancer with Fading Timeout in Data Center Networks.", "authors": ["<PERSON>", "<PERSON><PERSON> Gao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Haiyang Xu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern data centers (DCs) employ various traffic load balancers to achieve high bisection bandwidth. Among them, flowlet switching has shown remarkable performance in both load balancing and upper-layer protocol (e.g., TCP) friendliness. However, flowlet-based load balancers suffer from the inflexibility of flowlet timeout value (FTV) and result in sub-optimal performance under various application workloads. To this end, we propose Halflife, a novel flowlet-based load balancer that leverages fading FTVs to reroute traffic promptly under different workloads without any prior knowledge. Halflife not only balances traffic better, but also avoids the performance degradation caused by frequent oscillation or shifting of lows between paths. Furthermore, Halflife's fading mechanism is not only compatible with most flowlet-based load balancers, such as CONGA and LetFlow, but also improves their performance when leveraging flowlet switching in RDMA network. Through testbed experiments and simulations, we prove that Halflife improves the performance of CONGA and LetFlow by 10% ~ 150%, and it outperforms other load balancers by 30% ~ 200% across most application workloads.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650062"}, {"primary_key": "583448", "vector": [], "sparse_vector": [], "title": "Improving Resource and Energy Efficiency for Cloud 3D through Excessive Rendering Reduction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The rise of cloud gaming makes interactive 3D applications an emerging type of data center workload. However, the excessive rendering in current cloud 3D systems leads to large gaps between the cloud and client frame rates (FPS, frames per second), thus wasting resources and power. Although FPS regulation can remove excessive rendering, due to the highly-varying frame processing time and the use of rendering delays, existing cloud FPS regulation solutions have low FPS and slow motion-to-photon (MtP) latency, causing violations of Quality-of-Service (QoS) requirements.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650064"}, {"primary_key": "583449", "vector": [], "sparse_vector": [], "title": "Aceso: Efficient Parallel DNN Training through Iterative Bottleneck Alleviation.", "authors": ["<PERSON><PERSON> Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ng Shi", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many parallel mechanisms, including data parallelism, tensor parallelism, and pipeline parallelism, have been proposed and combined together to support training increasingly large deep neural networks (DNN) on massive GPU devices. Given a DNN model and GPU cluster, finding the optimal configuration by combining these parallelism mechanisms is an NP-hard problem. Widely adopted mathematical programming approaches have been proposed to search in a configuration subspace, but they are still too costly when scaling to large models over numerous devices.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629554"}, {"primary_key": "583450", "vector": [], "sparse_vector": [], "title": "Serialization/Deserialization-free State Transfer in Serverless Workflows.", "authors": ["Fangming Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "Serialization and deserialization play a dominant role in the state transfer time of serverless workflows, leading to substantial performance penalties during workflow execution. We identify the key reason as a lack of ability to efficiently access the (remote) memory of another function. We propose RMMap, an OS primitive for remote memory map. It allows a serverless function to directly access the memory of another function, even if it is located remotely. RMMap is the first to completely eliminates serialization and deserialization when transferring states between any pairs of functions in (unmodified) serverless workflows. To make remote memory map efficient and feasible, we co-design it with fast networking (RDMA), OS, language runtime, and serverless platform. Evaluations using real-world serverless workloads show that integrating RMMap with Knative reduces the serverless workflow execution time on Knative by up to 2.6 × and improves resource utilizations by 86.3%.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629568"}, {"primary_key": "583451", "vector": [], "sparse_vector": [], "title": "SplitFT: Fault Tolerance for Disaggregated Datacenters via Remote Memory Logging.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce SplitFt, a new fault-tolerance approach for storage-centric applications in disaggregated data centers. SplitFt uses a novel split architecture, where large writes are directly performed on the underlying disaggregated storage system, while small writes are made fault-tolerant within the compute layer. The split architecture enables applications to achieve strong durability guarantees without compromising performance. SplitFt makes small writes fault-tolerant using a new abstraction called near-compute logs or Ncl, which leverages underutilized memory on remote nodes to log small writes in a fast, cheap, and transparent manner. We port three POSIX applications (RocksDB, Redis, and SQLite) to SplitFt and show that they offer strong guarantees compared to weak versions of the applications that can lose data; SplitFt applications do so while approximating weak versions' performance (only 0.1%-10% overhead under YCSB). Compared to strong versions, SplitFt improves performance significantly (2.5× to 27× under write-heavy workloads).", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629561"}, {"primary_key": "583452", "vector": [], "sparse_vector": [], "title": "Noctua: Towards Automated and Practical Fine-grained Consistency Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Feng Yan", "<PERSON>"], "summary": "Relaxing strong consistency plays a vital role in achieving scalability and availability for geo-replicated web applications. However, making relaxation correct in modern implementations, typically written in dynamic languages and utilizing high-level object-oriented database abstractions, remains a challenge, despite the existence of numerous proposed analysis tools.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629570"}, {"primary_key": "583453", "vector": [], "sparse_vector": [], "title": "Puddles: Application-Independent Recovery and Location-Independent Data for Persistent Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we argue that current work has failed to provide a comprehensive and maintainable in-memory representation for persistent memory. PM data should be easily mappable into a process address space, shareable across processes, shippable between machines, consistent after a crash, and accessible to legacy code with fast, efficient pointers as first-class abstractions.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629555"}, {"primary_key": "583454", "vector": [], "sparse_vector": [], "title": "Trinity: A Fast Compressed Multi-attribute Data Store.", "authors": ["Zim<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the proliferation of attribute-rich machine-generated data, emerging real-time monitoring, diagnosis, and visualization tools ingest and analyze such data across multiple attributes simultaneously. Due to the sheer volume of the data, applications need storage-efficient and performant data representations to analyze them efficiently.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650072"}, {"primary_key": "583455", "vector": [], "sparse_vector": [], "title": "Model Selection for Latency-Critical Inference Serving.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In an inference service system, model selection and scheduling (MS&S) schemes map inference queries to trained machine learning (ML) models, hosted on a finite set of workers, to solicit accurate predictions within strict latency targets. MS&S is challenged by both varying query load and stochastic query inter-arrival patterns; however, state-of-the-art MS&S approaches conservatively account for load exclusively.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629565"}, {"primary_key": "583456", "vector": [], "sparse_vector": [], "title": "Enoki: High Velocity Linux Kernel Scheduler Development.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Kernel task scheduling is important for application performance, adaptability to new hardware, and complex user requirements. However, developing, testing, and debugging new scheduling algorithms in Linux, the most widely used cloud operating system, is slow and difficult. We developed Enoki, a framework for high velocity development of Linux kernel schedulers. Enoki schedulers are written in safe Rust, and the system supports live upgrade of new scheduling policies into the kernel, userspace debugging, and bidirectional communication with applications. A scheduler implemented with Enoki achieved near identical performance (within 1% on average) to the default Linux scheduler CFS on a wide range of benchmarks. Enoki is also able to support a range of research schedulers, specifically the Shinjuku scheduler, a locality aware scheduler, and the Arachne core arbiter, with good performance.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629569"}, {"primary_key": "583457", "vector": [], "sparse_vector": [], "title": "Hoda: a High-performance Open vSwitch Dataplane with Multiple Specialized Data Paths.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Zhenyu Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Open vSwitch (OvS) has been widely used in cloud networks in view of its programmability and flexibility. However, we observe a huge performance drop when it loads practical cloud networking services (e.g., tunneling and firewalling). Our further analysis reveals that the root cause lies in the gap between the needs of supporting various selections of packet header fields and the one-size-fits-all data path in the vanilla OvS. Motivated by this, we design Hoda, a high-performance OvS dataplane with multiple specialized data paths. Specifically, Hoda constructs the specialized parser and microflow cache for each OpenFlow program so as to achieve lightweight parsing and caching. We also propose a configurable version of Hoda that introduces configuration knobs in the data path to ease specialization. The experiments with real-life OpenFlow rules show that Hoda achieves up to 1.7× speed up over the state-of-the-art OvS and 1.5× speed up over mSwitch. Hoda has also been deployed in a large cloud to serve various online services; the A/B test in the cloud reveals a 20% request process time reduction for Ngnix services.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629564"}, {"primary_key": "583458", "vector": [], "sparse_vector": [], "title": "ScaleCache: A Scalable Page Cache for Multiple Solid-State Drives.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ye<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Son"], "summary": "This paper presents a scalable page cache called ScaleCache for improving SSD scalability. Specifically, we first propose a concurrent data structure of page cache based on XArray (ccXArray) to enable access and update the page cache concurrently. Second, we introduce a direct page flush (dflush) which directly flushes pages to storage devices in a parallel and opportunistic manner. We implement ScaleCache with two techniques in the Linux kernel and evaluate it on a 64-core machine with eight NVMe SSDs. Our evaluations show that ScaleCache improves the performance of Linux file systems by up to 6.81× and 4.50× compared with the existing scheme and scalable scheme for multiple SSDs, respectively.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629588"}, {"primary_key": "583459", "vector": [], "sparse_vector": [], "title": "LSGraph: A Locality-centric High-performance Streaming Graph Engine.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ligang He", "<PERSON>", "<PERSON>", "Minzhi Cai", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Streaming graph has been broadly employed across various application domains. It involves updating edges to the graph and then performing analytics on the updated graph. However, existing solutions either suffer from poor data locality and high computation complexity for streaming graph analytics, or need high overhead to search and move graph data to ensure ordered neighbors during streaming graph update.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650076"}, {"primary_key": "583460", "vector": [], "sparse_vector": [], "title": "Erlang: Application-Aware Autoscaling for Cloud Microservices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As cloud applications shift from monoliths to loosely coupled microservices, application developers must decide how many compute resources (e.g., number of replicated containers) to assign to each microservice within an application. This decision affects both (1) the dollar cost to the application developer and (2) the end-to-end latency perceived by the application user. Today, individual microservices are autoscaled independently by adding VMs whenever per-microservice CPU or memory utilization crosses a configurable threshold. However, an application user's end-to-end latency consists of time spent on multiple microservices and each microservice might need a different number of VMs to achieve an overall end-to-end latency.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650084"}, {"primary_key": "583461", "vector": [], "sparse_vector": [], "title": "NeuroFlux: Memory-Efficient CNN Training Using Adaptive Local Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Efficient on-device Convolutional Neural Network (CNN) training in resource-constrained mobile and edge environments is an open challenge. Backpropagation is the standard approach adopted, but it is GPU memory intensive due to its strong inter-layer dependencies that demand intermediate activations across the entire CNN model to be retained in GPU memory. This necessitates smaller batch sizes to make training possible within the available GPU memory budget, but in turn, results in substantially high and impractical training time. We introduce NeuroFlux, a novel CNN training system tailored for memory-constrained scenarios. We develop two novel opportunities: firstly, adaptive auxiliary networks that employ a variable number of filters to reduce GPU memory usage, and secondly, block-specific adaptive batch sizes, which not only cater to the GPU memory constraints but also accelerate the training process. NeuroFlux segments a CNN into blocks based on GPU memory usage and further attaches an auxiliary network to each layer in these blocks. This disrupts the typical layer dependencies under a new training paradigm - 'adaptive local learning'. Moreover, NeuroFlux adeptly caches intermediate activations, eliminating redundant forward passes over previously trained blocks, further accelerating the training process. The results are twofold when compared to Backpropagation: on various hardware platforms, NeuroFlux demonstrates training speed-ups of 2.3× to 6.1× under stringent GPU memory budgets, and NeuroFlux generates streamlined models that have 10.9× to 29.4× fewer parameters.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650067"}, {"primary_key": "583462", "vector": [], "sparse_vector": [], "title": "TraceUpscaler: Upscaling Traces to Evaluate Systems at High Load.", "authors": ["Sultan <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Trace replay is a common approach for evaluating systems by rerunning historical traffic patterns, but it is not always possible to find suitable real-world traces at the desired level of system load. Experimenting with higher traffic loads requires upscaling a trace to artificially increase the load. Unfortunately, most prior research has adopted ad-hoc approaches for upscaling, and there has not been a systematic study of how the upscaling approach impacts the results. One common approach is to count the arrivals in a predefined time-interval and multiply these counts by a factor, but this requires generating new requests/jobs according to some model (e.g., a Poisson process), which may not be realistic. Another common approach is to divide all the timestamps in the trace by an upscaling factor to squeeze the requests into a shorter time period. However, this can distort temporal patterns within the input trace. This paper evaluates the pros and cons of existing trace upscaling techniques and introduces a new approach, TraceUpscaler, that avoids the drawbacks of existing methods. The key idea behind TraceUpscaler is to decouple the arrival timestamps from the request parameters/data and upscale just the arrival timestamps in a way that preserves temporal patterns within the input trace. Our work applies to open-loop traffic where requests have arrival timestamps that aren't dependent on previous request completions. We evaluate TraceUpscaler under multiple experimental settings using both real-world and synthetic traces. Through our study, we identify the trace characteristics that affect the quality of upscaling in existing approaches and show how TraceUpscaler avoids these pitfalls. We also present a case study demonstrating how inaccurate trace upscaling can lead to incorrect conclusions about a system's ability to handle high load.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629581"}, {"primary_key": "583463", "vector": [], "sparse_vector": [], "title": "ScheMoE: An Extensible Mixture-of-Experts Distributed Training System with Tasks Scheduling.", "authors": ["Shaohuai Shi", "Xinglin Pan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiaoz<PERSON> Ren", "Zhongzhe Hu", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In recent years, large-scale models can be easily scaled to trillions of parameters with sparsely activated mixture-of-experts (MoE), which significantly improves the model quality while only requiring a sub-linear increase in computational costs. However, MoE layers require the input data to be dynamically routed to a particular GPU for computing during distributed training. The highly dynamic property of data routing and high communication costs in MoE make the training system low scaling efficiency on GPU clusters. In this work, we propose an extensible and efficient MoE training system, ScheMoE, which is equipped with several features. 1) ScheMoE provides a generic scheduling framework that allows the communication and computation tasks in training MoE models to be scheduled in an optimal way. 2) ScheMoE integrates our proposed novel all-to-all collective which better utilizes intra- and inter-connect bandwidths. 3) ScheMoE supports easy extensions of customized all-to-all collectives and data compression approaches while enjoying our scheduling algorithm. Extensive experiments are conducted on a 32-GPU cluster and the results show that ScheMoE outperforms existing state-of-the-art MoE systems, Tutel and Faster-MoE, by 9%-30%.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650083"}, {"primary_key": "583464", "vector": [], "sparse_vector": [], "title": "Blaze: Holistic Caching for Iterative Data Processing.", "authors": ["Won Wook Song", "<PERSON><PERSON><PERSON><PERSON>", "Taegeon Um", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern data processing workloads, such as machine learning and graph processing, involve iterative computations to converge generated models into higher accuracy. An effective caching mechanism is vital to expedite iterative computations since the intermediate data that needs to be stored in memory grows larger over iterations, often exceeding the memory capacity. However, existing systems handle intermediate data through separate operational layers (e.g., caching, eviction, and recovery), with each layer working independently in a greedy or cost-agnostic manner. These layers typically rely on user annotations and past access patterns, failing to make globally optimal decisions for the workload.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629558"}, {"primary_key": "583465", "vector": [], "sparse_vector": [], "title": "Orion: Interference-aware, Fine-grained GPU Sharing for ML Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "GPUs are critical for maximizing the throughput-per-Watt of deep neural network (DNN) applications. However, DNN applications often underutilize GPUs, even when using large batch sizes and eliminating input data processing or communication stalls. DNN workloads consist of data-dependent operators, with different compute and memory requirements. While an operator may saturate GPU compute units or memory bandwidth, it often leaves other GPU resources idle. Despite the prevalence of GPU sharing techniques, current approaches are not sufficiently fine-grained or interference-aware to maximize GPU utilization while minimizing interference at the granularity of 10s of μs. We propose Orion, a system that transparently intercepts GPU kernel launches from multiple clients sharing a GPU. Orion schedules work on the GPU at the granularity of individual operators and minimizes interference by taking into account each operator's compute and memory requirements. We integrate Orion in PyTorch and demonstrate its benefits in various DNN workload collocation use cases. Orion significantly improves tail latency compared to state-of-the-art baselines for a high-priority inference job while collocating best-effort inference jobs to increase per-GPU request throughput by up to 7.3×, or while collocating DNN training, saving up to 1.49× in training costs compared to dedicated GPU allocation.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629578"}, {"primary_key": "583466", "vector": [], "sparse_vector": [], "title": "On the Limitations of Carbon-Aware Temporal and Spatial Workload Shifting in the Cloud.", "authors": ["Thanathorn Sukprasert", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cloud platforms have been focusing on reducing their carbon emissions by shifting workloads across time and locations to when and where low-carbon energy is available. Despite the prominence of this idea, prior work has only quantified the potential of spatiotemporal workload shifting in narrow settings, i.e., for specific workloads in select regions. In particular, there has been limited work on quantifying an upper bound on the ideal and practical benefits of carbon-aware spatiotemporal workload shifting for a wide range of cloud workloads. To address the problem, we conduct a detailed data-driven analysis to understand the benefits and limitations of carbon-aware spatiotemporal scheduling for cloud workloads. We utilize carbon intensity data from 123 regions, encompassing most major cloud sites, to analyze two broad classes of workloads---batch and interactive---and their various characteristics, e.g., job duration, deadlines, and SLOs. Our findings show that while spatiotemporal workload shifting can reduce workloads' carbon emissions, the practical upper bounds of these carbon reductions are currently limited and far from ideal. We also show that simple scheduling policies often yield most of these reductions, with more sophisticated techniques yielding little additional benefit. Notably, we also find that the benefit of carbon-aware workload scheduling relative to carbon-agnostic scheduling will decrease as the energy supply becomes \"greener.\"", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650079"}, {"primary_key": "583467", "vector": [], "sparse_vector": [], "title": "TTLs Matter: Efficient Cache Sizing with TTL-Aware Miss <PERSON><PERSON> and Working Set Sizes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In-memory caches play a pivotal role in optimizing distributed systems by significantly reducing query response times. Correctly sizing these caches is critical, especially considering that prominent organizations use terabytes and even petabytes of DRAM for these caches. The Miss Ratio Curve (MRC) and Working Set Size (WSS) are the most widely used tools for sizing these caches.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650066"}, {"primary_key": "583468", "vector": [], "sparse_vector": [], "title": "Finding Correctness Bugs in eBPF Verifier with Structured and Sanitized Program.", "authors": ["Hao Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Yu <PERSON>"], "summary": "eBPF is an inspiring technique in Linux that allows user space processes to extend the kernel by dynamically injecting programs. However, it poses security issues, since the untrusted user code is now executed in the kernel space. eBPF utilizes a verifier to validate the safety of the provided programs, thus its correctness is of paramount importance as attackers may exploit vulnerabilities within it to inject malicious programs. Bug-finding tools like kernel fuzzers currently can detect memory bugs in eBPF system calls, but they experience difficulties in finding correctness bugs in the verifier, e.g., incorrect validations that allow the loading of unsafe programs. Because, unlike detecting memory bugs, where sanitizers can capture such errors once observed, automatically uncovering correctness bugs is very difficult, without an effective test oracle that determines if the verifier behaves correctly for given programs.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629562"}, {"primary_key": "583469", "vector": [], "sparse_vector": [], "title": "SandTable: Scalable Distributed System Model Checking with Specification-Level State Exploration.", "authors": ["<PERSON><PERSON>", "Xudong Sun", "<PERSON>", "<PERSON><PERSON> Wei", "<PERSON><PERSON>", "<PERSON><PERSON> Ma"], "summary": "Implementation-level distributed system model checkers (DMCKs) have proven valuable in verifying the correctness of real distributed systems. However, they primarily focus on state space reduction, and often have a bottleneck on another crucial dimension: exploration speed. To scale DMCK, we introduce SandTable, a technique for lifting state-space exploration from the implementation level to the specification level, and confirming bugs at the implementation level. We made SandTable practical through a methodology consisting of four essential parts: (1) writing specifications that adhere to the implementation, (2) checking conformance to enhance specification quality and reduce false positives and false negatives, (3) exploring the state space with heuristics for effectiveness and efficiency, and (4) confirming bugs and verifying their fixes in the implementation.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650077"}, {"primary_key": "583470", "vector": [], "sparse_vector": [], "title": "Exploring Performance and Cost Optimization with ASIC-Based CXL Memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As memory-intensive applications continue to drive the need for advanced architectural solutions, Compute Express Link (CXL) has risen as a promising interconnect technology that enables seamless high-speed, low-latency communication between host processors and various peripheral devices. In this study, we explore the application performance of ASIC CXL memory in various data-center scenarios. We then further explore multiple potential impacts (e.g., throughput, latency, and cost reduction) of employing CXL memory via carefully designed policies and strategies. Our empirical results show the high potential of CXL memory, reveal multiple intriguing observations of CXL memory and contribute to the wide adoption of CXL memory in real-world deployment environments. Based on our benchmarks, we also develop an Abstract Cost Model that can estimate the cost benefit from using CXL memory.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650061"}, {"primary_key": "583471", "vector": [], "sparse_vector": [], "title": "Efficient Auditing of Event-driven Web Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "When a deployer of a web application puts that application on a server (on-prem or cloud), how can they be sure that the application is executing as intended? This paper studies how the deployer can efficiently check that the execution is faithful. We seek mechanisms that: (i) work with web applications that are built with modern event-driven web frameworks, (ii) impose tolerable computation and communication overheads on the web server, and (iii) are complete and sound. We exhibit such a mechanism, based on a new record-replay algorithm. We have implemented our algorithm in Karousos, a system that audits Node.js web applications.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650089"}, {"primary_key": "583472", "vector": [], "sparse_vector": [], "title": "Draconis: Network-Accelerated Scheduling for Microsecond-Scale Workloads.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present <PERSON><PERSON><PERSON>, a novel scheduler for workloads in the range of tens to hundreds of microseconds. <PERSON><PERSON><PERSON> challenges the popular belief that programmable switches cannot house the complex data structures, such as queues, needed to support an in-network scheduler. Using programmable switches, <PERSON>acon<PERSON> achieves the low scheduling tail latency and high throughput needed to support these microsecond-scale workloads on large clusters. Furthermore, Draconis supports a wide range of complex scheduling policies, including locality-aware scheduling, priority-based scheduling, and resource-based scheduling.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650060"}, {"primary_key": "583473", "vector": [], "sparse_vector": [], "title": "Wormhole Filters: Caching Your Hash on Persistent Memory.", "authors": ["Han<PERSON> Wang", "<PERSON>peng Dai", "<PERSON><PERSON>", "Youyou Lu", "<PERSON><PERSON><PERSON>", "Jingsong Dai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shuait<PERSON> Li", "<PERSON><PERSON><PERSON>"], "summary": "Approximate membership query (AMQ) data structures can approximately determine whether an element is in the set with high efficiency. They are widely used in distributed systems, database systems, bioinformatics, IoT applications, data stream mining, etc. However, the memory consumption of AMQ data structures grows rapidly as the data scale grows, which limits the system's ability to process a massive amount of data. The emerging persistent memory provides a close-to-DRAM access speed and terabyte-level capacity, facilitating AMQ data structures to handle massive data. Nevertheless, existing AMQ data structures perform poorly on persistent memory due to intensive random accesses and/or sequential writes. Therefore, we propose a novel AMQ data structure called wormhole filter, which achieves high performance on persistent memory by reducing random accesses and sequential writes. In addition, we reduce the number of log records for lower recovery overhead. Theoretical analysis and experimental results show that wormhole filters significantly outperform competitive state-of-the-art AMQ data structures. For example, wormhole filters achieve 23.26× insertion throughput, 1.98× positive lookup throughput, and 8.82× deletion throughput of the best competing baseline.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629590"}, {"primary_key": "583474", "vector": [], "sparse_vector": [], "title": "Bandle: Asynchronous State Machine Replication Made Efficient.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "State machine replication (SMR) uses consensus as its core component for reaching agreement among a group of processes, in order to provide fault-tolerant services. Most SMR protocols, such as Paxos and Raft, are designed in the partial synchrony model. Partially synchronous protocols rely on timing assumptions to elect a special role (such as the leader), which may become the performance bottleneck under a heavy workload. From an engineering perspective, partially synchronous protocols have to wait for a pre-defined period of time and implement a (complicated) failover mechanism in order to replace the faulty leader. In contrast, asynchronous protocols are immune to such problems.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650091"}, {"primary_key": "583475", "vector": [], "sparse_vector": [], "title": "Snatch: Online Streaming Analytics at the Network Edge.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In recent years, we have witnessed a growing trend of content hyper-giants deploying server infrastructure and services close to end-users, in \"eyeball\" networks. Still, one of the services that remained largely unaffected by this trend is online streaming analytics. This is despite the fact that most of the \"big data\" is received in real time and is most valuable at the time of arrival. The inability to process requests at the network edge is caused by a common setting where user profiles, necessary for analytics, are stored deep in the data center back-ends. This setting also carries privacy concerns as such user profiles are individually identifiable, yet the users are almost blind to what data is associated with their identities and how the data is analyzed. In this paper, we revise this arrangement, and plant encrypted semantic cookies at the user end. Without altering any of the existing protocols, this enables capturing and analytically pre-processing user requests soon after they are generated, at edge ISPs or content providers' off-nets. In addition, it ensures user anonymity perseverance during the analytics. We design and implement Snatch, a QUIC-based streaming analytics prototype, and demonstrate that it speeds up user analytics by up to 200x, and by 10-30x in the common case.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629577"}, {"primary_key": "583476", "vector": [], "sparse_vector": [], "title": "Occam: A Programming System for Reliable Network Management.", "authors": ["Jiarong Xing", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The complexity of large networks makes their management a daunting task. State-of-the-art network management tools use workflow systems for automation, but they do not adequately address the substantial challenges in operation reliability. This paper presents Occam, a programming system that simplifies the development of reliable network management tasks. We leverage the fact that most modern network management systems are backed with a source-of-truth database, and thus customize database techniques to the context of network management. Occam exposes an easy-to-use programming model for network operators to express the key management logic, while shielding them from reliability concerns, such as operational conflicts and task atomicity. Instead, the Occam runtime provides these reliability guardrails automatically. Our evaluation demonstrates Occam's effectiveness in simplifying management tasks, minimizing network vulnerable time and assisting with failure recovery.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650086"}, {"primary_key": "583477", "vector": [], "sparse_vector": [], "title": "Transparent Multicore Scaling of Single-Threaded Network Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>eyang Pan", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents NFOS, a programming model, runtime, and profiler for productively developing software network functions (NFs) that scale on multicore machines. Writing shared-state concurrent systems that are both correct and scalable is still a serious challenge, which is why NFOS insulates developers from writing concurrent code.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629591"}, {"primary_key": "583478", "vector": [], "sparse_vector": [], "title": "Minuet: Accelerating 3D Sparse Convolutions on GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sparse Convolution (SC) is widely used for processing 3D point clouds that are inherently sparse. Different from dense convolution, SC preserves the sparsity of the input point cloud by only allowing outputs to specific locations. To efficiently compute SC, prior SC engines first use hash tables to build a kernel map that stores the necessary General Matrix Multiplication (GEMM) operations to be executed (Map step), and then use a Gather-GEMM-Scatter process to execute these GEMM operations (GMaS step). In this work, we analyze the shortcomings of prior state-of-the-art SC engines, and propose Minuet, a novel memory-efficient SC engine tailored for modern GPUs. <PERSON><PERSON><PERSON> proposes to (i) replace the hash tables used in the Map step with a novel segmented sorting double-traversed binary search algorithm that highly utilizes the on-chip memory hierarchy of GPUs, (ii) use a lightweight scheme to autotune the tile size in the Gather and Scatter operations of the GMaS step, such that to adapt the execution to the particular characteristics of each SC layer, dataset, and GPU architecture, and (iii) employ a padding-efficient GEMM grouping approach that reduces both memory padding and kernel launching overheads. Our evaluations show that <PERSON><PERSON><PERSON> significantly outperforms prior SC engines by on average 1.74× (up to 2.22×) for end-to-end point cloud network executions. Our novel segmented sorting double-traversed binary search algorithm achieves superior speedups by 15.8× on average (up to 26.8×) over prior SC engines in the Map step. The source code of <PERSON><PERSON><PERSON> is publicly available at https://github.com/UofT-EcoSystem/Minuet.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629560"}, {"primary_key": "583479", "vector": [], "sparse_vector": [], "title": "GMorph: Accelerating Multi-DNN Inference via Model Fusion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mingcan <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "AI-powered applications often involve multiple deep neural network (DNN)-based prediction tasks to support application-level functionalities. However, executing multi-DNNs can be challenging due to the high resource demands and computation costs that increase linearly with the number of DNNs. Multi-task learning (MTL) addresses this problem by designing a multi-task model that shares parameters across tasks based on a single backbone DNN. This paper explores an alternative approach called model fusion: rather than training a single multi-task model from scratch as MTL does, model fusion fuses multiple task-specific DNNs that are pre-trained separately and can have heterogeneous architectures into a single multi-task model. We materialize model fusion in a software framework called GMorph to accelerate multi-DNN inference while maintaining task accuracy. GMorph features three main technical contributions: graph mutations to fuse multi-DNNs into resource-efficient multi-task models, search-space sampling algorithms, and predictive filtering to reduce the high search costs. Our experiments show that GMorph can outperform MTL baselines and reduce the inference latency of multi-DNNs by 1.1-3× while meeting the target task accuracy.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650074"}, {"primary_key": "583480", "vector": [], "sparse_vector": [], "title": "Adaptable Runtime Monitoring for Intermittent Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lorenzo <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Batteryless energy harvesting devices compute intermittently due to power failures that frequently interrupt the computational activity and lead to charging delays. To ensure functional correctness in intermittent computing, applications must exhibit several unique properties, such as guarantees for computational progress despite power failures and prevention of stale operations caused by charging delays. We observe that current software support for intermittent computing allows for checking only a fixed set of properties and leads to tightly coupled application and property-checking, thus hampering modularity, scalability, and maintainability.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650070"}, {"primary_key": "583481", "vector": [], "sparse_vector": [], "title": "Volley: Accelerating Write-Read Orders in Disaggregated Storage.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Youyou Lu"], "summary": "Modern data centers deploy disaggregated storage systems (e.g., NVMe over Fabrics, NVMe-oF) for fine-grained resource elasticity and high resource utilization. A client-side writeback cache is used to absorb writes and buffer frequently accessed data, thereby eliminating unnecessary remote storage accesses and improving performance. Yet, a cache miss on the full cache triggers an evict-and-fetch operation which evicts the old entries before new data blocks are fetched. Existing systems perform the evict-and-fetch operation by sequentially executing write and read I/O operations, which reduces the concurrency and makes it challenging to fully utilize the fast network and storage devices.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650090"}, {"primary_key": "583482", "vector": [], "sparse_vector": [], "title": "HD-IOV: SW-HW Co-designed I/O Virtualization with Scalability and Flexibility for Hyper-Density Cloud.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Haibing Guan"], "summary": "As the resource density of cloud servers increases, cloud providers deploy hundreds of VMs concurrently on a single server, requiring a high-performance, scalable, flexible and high-density I/O virtualization method. Hardware assisted virtualization such as device pass-through with SR-IOV can achieve near-native performance, however, at the expense of flexibility and a limited device count. Traditional software-based I/O virtualization systems tend to dedicate additional computing cores for higher performance, but suffer from critical scalability problems especially in high-density cloud.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629557"}, {"primary_key": "583483", "vector": [], "sparse_vector": [], "title": "HAP: SPMD DNN Training on Heterogeneous GPU Clusters with Automated Program Synthesis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Single-Program-Multiple-Data (SPMD) parallelism has recently been adopted to train large deep neural networks (DNNs). Few studies have explored its applicability on heterogeneous clusters, to fully exploit available resources for large model learning. This paper presents HAP, an automated system designed to expedite SPMD DNN training on heterogeneous clusters. HAP jointly optimizes the tensor sharding strategy, sharding ratios across heterogeneous devices and the communication methods for tensor exchanges for optimized distributed training with SPMD parallelism. We novelly formulate model partitioning as a program synthesis problem, in which we generate a distributed program from scratch on a distributed instruction set that semantically resembles the program designed for a single device, and systematically explore the solution space with an A-based search algorithm. We derive the optimal tensor sharding ratios by formulating it as a linear programming problem. Additionally, HAP explores tensor communication optimization in a heterogeneous cluster and integrates it as part of the program synthesis process, for automatically choosing optimal collective communication primitives and applying sufficient factor broadcasting technique. Extensive experiments on representative workloads demonstrate that HAP achieves up to 2.41x speed-up on heterogeneous clusters.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629580"}, {"primary_key": "583484", "vector": [], "sparse_vector": [], "title": "Improving GPU Energy Efficiency through an Application-transparent Frequency Scaling Policy with Performance Assurance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Power consumption is one of the top limiting factors in high-performance computing systems and data centers, and dynamic voltage and frequency scaling (DVFS) is an important mechanism to control power. Existing works using DVFS to improve GPU energy efficiency suffer from the limitation that their policies either impact performance too much or require offline application profiling or code modification, which severely limits their applicability on large clusters. To address this issue, we propose a novel GPU DVFS policy, GEEPAFS, which improves the energy efficiency of GPUs while providing performance assurance. GEEPAFS is application-transparent as it does not require any offline profiling or code modification on user applications. To achieve this, GEEPAFS models application performance online based on our quantitative analysis of a correlation between performance and GPU memory bandwidth utilization. Based on their relationship, GEEPAFS builds a fold-line frequency-performance model for applications being executed, and it applies the model to guide the setting of GPU frequency to maximize energy efficiency while ensuring the performance loss is bounded. Through experiments on NVIDIA V100 and A100 GPUs, we show that GEEPAFS is able to improve the energy efficiency by 26.7% and 20.2% on average. While achieving this improvement, the average performance loss is only 5.8%, and the worst-case performance loss is 12.5% among all 33 tested applications.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629584"}, {"primary_key": "583485", "vector": [], "sparse_vector": [], "title": "Effective Bug Detection with Unused Definitions.", "authors": ["<PERSON>", "Chengcheng Xiang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Unused definitions are values assigned to variables but not used. Since unused definitions are usually considered redundant code causing no severe consequences except for wasting CPU cycles, system developers usually treat them as mild warnings and simply remove them. In this paper, we reevaluate the effect of unused definitions and discover that some unused definitions could indicate non-trivial bugs like security issues or data corruption, which calls for more attention from developers.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629576"}, {"primary_key": "583486", "vector": [], "sparse_vector": [], "title": "Save the Bruised Striver: A Reliable Live Patching Framework for Protecting Real-World PLCs.", "authors": ["<PERSON>", "Haining <PERSON>", "<PERSON>", "Hong<PERSON><PERSON> Zhu", "<PERSON><PERSON>"], "summary": "Industrial Control Systems (ICS), particularly programmable logic controllers (PLCs) responsible for managing underlying physical infrastructures, often operate for extended periods without interruption. Thus, it is challenging to patch security vulnerabilities of ICS in a timely manner after disclosure because it often necessitates waiting for a rare downtime window. While live patching has been introduced to avoid downtime and maintenance costs, conventional live patching methods are not viable for closed-source PLCs. Without the source code, it is difficult to understand the system behaviors and determine binary patch equivalence. To address these challenges, we present a Reliable Live Patching framework called RLPatch for applying live patches to third-party binary without source code. We design RLPatch to capture real-time conditions and dynamic behaviors of PLCs, which enables DevOps engineers to identify major non-recoverable fault (MNRF) vulnerabilities and generate hot patches. The core of RLPatch is an update agent that inserts breakpoints over the original MNRF code and then directs execution to the patches. To ensure system reliability, we use the unique constraints of PLCs to integrate the update processes with the scan cycle. We leverage RLPatch to patch 20 real vulnerabilities in three widely used Rockwell PLCs. We evaluate RLPatch in a real-world gas pipeline, demonstrating its reliability and effectiveness in practice.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650068"}, {"primary_key": "583487", "vector": [], "sparse_vector": [], "title": "SmartNIC Security Isolation in the Cloud with S-NIC.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Modern smart NICs provide little isolation between the network functions belonging to different tenants. These NICs also do not protect network functions from the datacenter-provided management OS which runs on the smart NIC. We describe concrete attacks which allow a network function's state to leak to (or be modified by) another network function or the management OS. We then introduce S-NIC, a new hardware design for smart NICs that provides strong isolation guarantees. S-NIC pervasively virtualizes hardware accelerators, enforces single-owner semantics for each line in on-NIC cache and RAM, and provides dedicated bus bandwidth for each network function. Using this design, we eliminate side channels involving shared hardware state, and give each network function the illusion of having a private smart NIC. We show how these virtual NICs can be integrated with preexisting datacenter technologies for virtual LANs and trusted host-level computations like SGX enclaves. The overall result is that S-NIC enables strongly-isolated, NIC-accelerated datacenter applications; in these applications, network functions and host-level code receive hardware-guaranteed isolation from other applications and the datacenter provider.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3650071"}, {"primary_key": "583488", "vector": [], "sparse_vector": [], "title": "CSAL: the Next-Gen Local Disks for the Cloud.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cloud local disks are attractive for their affordable price and high performance. The recent advancement in CPUs motivates cloud vendors to further multiplex the computing resources to serve more users. Unfortunately, such proposals are constrained by the limited offerings of cloud local disks per server as the underlying storage devices are either large but slow (e.g., HDDs) or fast yet small (e.g., NVMe SSDs).", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629566"}, {"primary_key": "583489", "vector": [], "sparse_vector": [], "title": "Exploring the Asynchrony of Slow Memory Filesystem with EasyIO.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce EasyIO, a new approach to explore asynchronous I/O on filesystems designed for (disaggregated) nonvolatile memories to improve CPU efficiency. EasyIO offloads expensive memory movement operations to the on-chip DMA engine and harvests the unleashed CPU cycles by transparently interleaving asynchronous I/Os with fine-grained application tasks. We further adopt a completion buffer-centric design to improve EasyIO's efficiency and schedulability; internally, orderless file operation and two-level locking are incorporated to break the serial order between file metadata and data, thus accelerating read and write operations and defusing deadlock risks. EasyIO also introduces a traffic-aware channel manager to fulfill the diverse performance goals of applications. Extensive experimental results show that, compared to conventional synchronous filesystems, EasyIO significantly reduces CPU consumption (using less than 88% of cores at most) while achieving comparable peak bandwidth; EasyIO also achieves 1.03-2.3× speedups across eight real-world applications. When achieving these goals, EasyIO exhibits higher but tolerable latencies for read operations due to the task interleaving.", "published": "2024-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3627703.3629586"}]