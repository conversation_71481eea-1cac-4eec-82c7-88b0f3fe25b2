[{"primary_key": "2584183", "vector": [], "sparse_vector": [], "title": "A Dichotomy for Real Boolean Holant Problems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove a complexity dichotomy for <PERSON><PERSON> problems on the boolean domain with arbitrary sets of real-valued constraint functions. These constraint functions need not be symmetric nor do we assume any auxiliary functions. It is proved that for every set F of real-valued constraint functions, <PERSON>lant(F) is either P-time computable or #P-hard. The classification has an explicit criterion. This is a culmination of much research on this problem, and it uses many previous results and techniques. Dealing with some concrete functions plays an important role in this proof. In particular, two functions, called f6 and f8, and their associated families exhibit intriguing and extraordinary closure properties related to Bell states in quantum information theory.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00105"}, {"primary_key": "2584184", "vector": [], "sparse_vector": [], "title": "Pandora&apos;s Box with Correlations: Learning and Approximation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Pandora's Box problem and its extensions capture optimization problems with stochastic input where the algorithm can obtain instantiations of input random variables at some cost. To our knowledge, all previous work on this class of problems assumes that different random variables in the input are distributed independently. As such it does not capture many real-world settings. In this paper, we provide the first approximation algorithms for Pandora's Box-type problems with correlations. We assume that the algorithm has access to samples drawn from the joint distribution on input. Algorithms for these problems must determine an order in which to probe random variables, as well as when to stop and return the best solution found so far. In general, an optimal algorithm may make both decisions adaptively based on instantiations observed previously. Such fully adaptive (FA) strategies cannot be efficiently approximated to within any sub-linear factor with sample access. We therefore focus on the simpler objective of approximating partially adaptive (PA) strategies that probe random variables in a fixed predetermined order but decide when to stop based on the instantiations observed. We consider a number of different feasibility constraints and provide simple PA strategies that are approximately optimal with respect to the best PA strategy for each case. All of our algorithms have polynomial sample complexity. We further show that our results are tight within constant factors: better factors cannot be achieved even using the full power of FA strategies.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00116"}, {"primary_key": "2584185", "vector": [], "sparse_vector": [], "title": "Smoothing the gap between NP and ER.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study algorithmic problems that belong to the complexity class of the existential theory of the reals (ER). A problem is ER-complete if it is as hard as the problem ETR and if it can be written as an ETR formula. Traditionally, these problems are studied in the real RAM, a model of computation that assumes that the storage and comparison of real-valued numbers can be done in constant space and time, with infinite precision. The complexity class ER is often called a real RAM analogue of NP, since the problem ETR can be viewed as the real-valued variant of SAT. The real RAM assumption that we can represent and compare irrational values in constant space and time is not very realistic. Yet this assumption is vital, since some ER-complete problems have an \"exponential bit phenomenon\" where there exists an input for the problem, such that the witness of the solution requires geometric coordinates which need exponential word size when represented in binary. The problems that exhibit this phenomenon are NP-hard (since ETR is NP-hard) but it is unknown if they lie in NP. NP membership is often showed by using the famous Cook-Levin theorem which states that the existence of a polynomial-time verification algorithm for the problem witness is equivalent to NP membership. The exponential bit phenomenon prohibits a straightforward application of the Cook-Levin theorem. In this paper we first present a result which we believe to be of independent interest: we prove a real RAM analogue to the Cook-<PERSON> theorem which shows that ER membership is equivalent to having a verification algorithm that runs in polynomial-time on a real RAM. This gives an easy proof of ER-membership, as verification algorithms on a real RAM are much more versatile than ETR-formulas. We use this result to construct a framework to study ER-complete problems under smoothed analysis. We show that for a wide class of ER-complete problems, its witness can be represented with logarithmic input-precision by using smoothed analysis on its real RAM verification algorithm. This shows in a formal way that the boundary between NP and ER (formed by inputs whose solution witness needs high input-precision) consists of contrived input. We apply our framework to well-studied ER-complete recognition problems which have the exponential bit phenomenon such as the recognition of realizable order types or the Steinitz problem in fixed dimension. Interestingly our techniques also generalize to problems with a natural notion of resource augmentation (geometric packing, the art gallery problem).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00099"}, {"primary_key": "2584186", "vector": [], "sparse_vector": [], "title": "Fully Online Matching II: Beating Ranking and Water-filling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (STOC 1990) initiated the study of online bipartite matching, which has held a central role in online algorithms ever since. Of particular importance are the Ranking algorithm for integral matching and the Water-filling algorithm for fractional matching. Most algorithms in the literature can be viewed as adaptations of these two in the corresponding models. Recently, <PERSON> et al. (SODA 2019, JACM 2020) introduced a more general model called fully online matching, which considers general graphs and allows all vertices to arrive online. They also generalized Ranking and Water-filling to fully online matching and gave some tight analysis: Ranking is Ω ≈ 0.567-competitive on bipartite graphs where the Ω-constant satisfies Ωe Ω =1, and Water-filling is 2-√2 ≈ 0.585-competitive on general graphs. We propose fully online matching algorithms strictly better than Ranking and Water-filling. For integral matching on bipartite graphs, we build on the online primal dual analysis of Ranking and Water-filling to design a 0.569-competitive hybrid algorithm called Balanced Ranking. To our knowledge, it is the first integral algorithm in the online matching literature that successfully integrates ideas from Water-filling. For fractional matching on general graphs, we give a 0.592-competitive algorithm called Eager Water-filling, which may match a vertex on its arrival. By contrast, the original Water-filling algorithm always matches vertices at their deadlines. Our result for fractional matching further shows a separation between fully online matching and the general vertex arrival model by <PERSON> and <PERSON> (ICALP 2015), due to an upper bound of 0.5914 in the latter model by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (ESA 2017).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00130"}, {"primary_key": "2584187", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON>s in a Panorama.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Three decades ago, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (STOC 1990) defined the online matching problem and gave an optimal (1-1/e)-competitive (about 0.632) algorithm. Fifteen years later, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (FOCS 2005) introduced the first generalization called AdWords driven by online advertising and obtained the optimal (1-1/e) competitive ratio in the special case of small bids. It has been open ever since whether there is an algorithm for general bids better than the 0.5-competitive greedy algorithm. This paper presents a 0.5016-competitive algorithm for AdWords, answering this open question on the positive end. The algorithm builds on several ingredients, including a combination of the online primal dual framework and the configuration linear program of matching problems recently explored by <PERSON> and <PERSON> (STOC 2020), a novel formulation of AdWords which we call the panorama view, and a generalization of the online correlated selection by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> (FOCS 2020) which we call the panoramic online correlated selection.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00133"}, {"primary_key": "2584188", "vector": [], "sparse_vector": [], "title": "Cut-Equivalent Trees are Optimal for Min-Cut Queries.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Min-Cut queries are fundamental: Preprocess an undirected edge-weighted graph, to quickly report a minimum-weight cut that separates a query pair of nodes s, t. The best data structure known for this problem simply builds a cut-equivalent tree, discovered 60 years ago by <PERSON><PERSON><PERSON> and <PERSON>, who also showed how to construct it using n-1 minimum st-cut computations. Using state-of-the-art algorithms for minimum st-cut (<PERSON> and <PERSON>, FOCS 2014), one can construct the tree in time ~O(mn 3/2 ), which is also the preprocessing time of the data structure. (Throughout, we focus on polynomially-bounded edge weights, noting that faster algorithms are known for small/ u nit edge weights, and use n and m for the number of nodes and edges in the graph.) Our main result shows the following equivalence: Cut-equivalent trees can be constructed in near-linear time if and only if there is a data structure for Min-Cut queries with near-linear preprocessing time and polylogarithmic (amortized) query time, and even if the queries are restricted to a fixed source. That is, equivalent trees are an essentially optimal solution for Min-Cut queries. This equivalence holds even for every minor-closed family of graphs, such as bounded-treewidth graphs, for which a two-decade old data structure (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> 1998) implies the first near-linear time construction of cut-equivalent trees. Moreover, unlike all previous techniques for constructing cut-equivalent trees, ours is robust to relying on approximation algorithms. In particular, using the almost-linear time algorithm for ( 1+ε)-approximate minimum st-cut (Kelner, <PERSON>, Orecchia, and <PERSON><PERSON>, SODA 2014), we can construct a ( 1+ε)-approximate flow-equivalent tree (which is a slightly weaker notion) in time n 2+o(1) . This leads to the first ( 1+ε)-approximation for All-Pairs Max-Flow that runs in time n 2+o(1) , and matches the output size almost-optimally.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00019"}, {"primary_key": "2584189", "vector": [], "sparse_vector": [], "title": "Framework for ER-Completeness of Two-Dimensional Packing Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that many natural two-dimensional packing problems are algorithmically equivalent to finding real roots of multivariate polynomials. A two-dimensional packing problem is defined by the type of pieces, containers, and motions that are allowed. The aim is to decide if a given set of pieces can be placed inside a given container. The pieces must be placed so that in the resulting placement, they are pairwise interior-disjoint, and only motions of the allowed type can be used to move them there. We establish a framework which enables us to show that for many combinations of allowed pieces, containers, and motions, the resulting problem is ER-complete. This means that the problem is equivalent (under polynomial time reductions) to deciding whether a given system of polynomial equations and inequalities with integer coefficients has a real solution. A full version of this extended abstract is available on https://arxiv.org/abs/1704.06969.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00098"}, {"primary_key": "2584190", "vector": [], "sparse_vector": [], "title": "2D Generalization of Fractional Cascading on Axis-aligned Planar Subdivisions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fractional cascading is one of the influential and important techniques in data structures, as it provides a general framework for solving a common important problem: The iterative search problem. In the problem, the input is a graph G with constant degree. Also as input, we are given a set of values for every vertex of G. The goal is to preprocess G such that when we are given a query value q, and a connected subgraph pi of G, we can find the predecessor of q in all the sets associated with the vertices of pi. The fundamental result of fractional cascading, by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, is that there exists a data structure that uses linear space and it can answer queries in O(log n+ vert pi vert) time, at essentially constant time per predecessor [15]. While this technique has received plenty of attention in the past decades, an almost quadratic space lower bound for'two-dimensional fractional cascading' by <PERSON><PERSON><PERSON> and <PERSON> in STOC 2001 [17] has convinced the researchers that fractional cascading is fundamentally a one-dimensional technique. In two-dimensional fractional cascading, the input includes a planar subdivision for every vertex of G and the query is a point q and a subgraph pi and the goal is to locate the cell containing q in all the subdivisions associated with the vertices of pi. In this paper, we show that it is actually possible to circumvent the lower bound of <PERSON><PERSON><PERSON> and <PERSON> for axis-aligned planar subdivisions. We present a number of upper and lower bounds which reveal that in two-dimensions, the problem has a much richer structure. When G is a tree and pi is a path, then queries can be answered in O(log n+ vert pi vert + min {vert pi vert sqrt{log n},alpha(n) sqrt{vert pi vert} log n }) time using linear space where alpha is an inverse Ackermann function; surprisingly, we show both branches of this bound are tight, up to the inverse Ackermann factor. When G is a general graph or when pi is a general subgraph, then the query bound becomes O(log n+ vert pi vert sqrt{log n}) and this bound is once again tight in both cases.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00072"}, {"primary_key": "2584191", "vector": [], "sparse_vector": [], "title": "A constant rate non-malleable code in the split-state model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Non-malleable codes, introduced by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in ICS 2010, have emerged in the last few years as a fundamental object at the intersection of cryptography and coding theory. Non-malleable codes provide a useful message integrity guarantee in situations where traditional error-correction (and even error-detection) is impossible; for example, when the attacker can completely overwrite the encoded message. Informally, a code is non-malleable if the message contained in a modified codeword is either the original message, or a completely \"unrelated value\". The family which received the most attention is the family of tampering functions in the so called (2-part) split-state model: here the message x is encoded into two shares L and R, and the attacker is allowed to arbitrarily tamper with each L and R individually. In this work, we give a constant rate non-malleable code from the tampering family containing so called 2-lookahead functions and forgetful functions, and combined with the work of <PERSON><PERSON>, <PERSON><PERSON> and the authors from STOC 2015, this gives the first constant rate non-malleable code in the split-state model with negligible error. The full version of this paper can be found here: https://eprint.iacr.org/2019/1299.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00122"}, {"primary_key": "2584192", "vector": [], "sparse_vector": [], "title": "Subsets and Supermajorities: Optimal Hashing-based Set Similarity Search.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We formulate and optimally solve a new generalized Set Similarity Search problem, which assumes the size of the database and query sets are known in advance. By creating polylog copies of our data-structure, we optimally solve any symmetric Approximate Set Similarity Search problem, including approximate versions of Subset Search, Maximum Inner Product Search (MIPS), Jaccard Similarity Search, and Partial Match. Our algorithm can be seen as a natural generalization of previous work on Set as well as Euclidean Similarity Search, but conceptually it differs by optimally exploiting the information present in the sets as well as their complements, and doing so asymmetrically between queries and stored sets. Doing so we improve upon the best previous work: MinHash [J. Discrete Algorithms 1998], SimHash [STOC 2002], Spherical LSF [SODA 2016, 2017], and <PERSON><PERSON> Path [STOC 2017] by as much as a factor n{0.14} in both time and space; or in the near-constant time regime, in space, by an arbitrarily large polynomial factor. Turning the geometric concept, based on Boolean supermajority functions, into a practical algorithm requires ideas from branching random walks on mathbb{Z}{2}, for which we give the first non-asymptotic near tight analysis. Our lower bounds follow from new hypercontractive arguments, which can be seen as characterizing the exact family of similarity search problems for which supermajorities are optimal. The optimality holds for among all hashing based data structures in the random setting, and by reductions, for 1 cell and 2 cell probe data structures.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00073"}, {"primary_key": "2584193", "vector": [], "sparse_vector": [], "title": "High-precision Estimation of Random Walks in Small Space.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we provide a deterministic ~O(log N)-space algorithm for estimating random walk probabilities on undirected graphs, and more generally Eulerian directed graphs, to within inverse polynomial additive error (ε = 1/poly(N)) where N is the length of the input. Previously, this problem was known to be solvable by a randomized algorithm using space O(log N) (following <PERSON><PERSON><PERSON><PERSON> et al., FOCS '79) and by a deterministic algorithm using space O(log 3/2 N) (<PERSON><PERSON> and <PERSON>, FOCS '95 and JCSS '99), both of which held for arbitrary directed graphs but had not been improved even for undirected graphs. We also give improvements on the space complexity of both of these previous algorithms for non-Eulerian directed graphs when the error is negligible (ε = 1/N ω(1) ), generalizing what <PERSON><PERSON> and <PERSON><PERSON> (FOCS '18) recently showed for the special case of distinguishing whether a random walk probability is 0 or greater than ε. We achieve these results by giving new reductions between powering Eulerian random-walk matrices and inverting Eulerian Laplacian matrices, providing a new notion of spectral approximation for Eulerian graphs that is preserved under powering, and giving the first deterministic ~O(log N)-space algorithm for inverting Eulerian Laplacian matrices. The latter algorithm builds on the work of <PERSON><PERSON><PERSON> et al. (FOCS '17) that gave a deterministic ~O(log N)-space algorithm for inverting undirected Laplacian matrices, and the work of <PERSON> et al. (FOCS '19) that gave a randomized ~O(N)-time algorithm for inverting Eulerian Laplacian matrices. A running theme throughout these contributions is an analysis of \"cycle-lifted graphs,\" where we take a graph and \"lift\" it to a new graph whose adjacency matrix is the tensor product of the original adjacency matrix and a directed cycle (or variants of one).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00123"}, {"primary_key": "2584194", "vector": [], "sparse_vector": [], "title": "Algorithms and Hardness for Linear Algebra on Geometric Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "For a function K: R d × R d → R ≥0 , and a set P = {x 1 ,..., x n } ⊂ R d of n points, the K graph GP of P is the complete graph on n nodes where the weight between nodes i and j is given by K(x i , x j ). In this paper, we initiate the study of when efficient spectral graph theory is possible on these graphs. We investigate whether or not it is possible to solve the following problems in n 1+o(1) time for a K-graph GP when : (a) Multiply a given vector by the adjacency matrix or Laplacian matrix of GP (b) Find a spectral sparsifier of GP (c) Solve a Laplacian system in GP's Laplacian matrix For each of these problems, we consider all functions of the form K(u, v)=f(||u-v||2 2 ) for a function f: R→ R. We provide algorithms and comparable hardness results for many such K, including the Gaussian kernel, Neural tangent kernels, and more. For example, in dimension d=Ω(logn), we show that there is a parameter associated with the function f for which low parameter values imply n 1+o(1) time algorithms for all three of these problems and high parameter values imply the nonexistence of subquadratic time algorithms assuming Strong Exponential Time Hypothesis (SETH), given natural assumptions on f. As part of our results, we also show that the exponential dependence on the dimension d in the celebrated fast multi-pole method of <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> cannot be improved, assuming SETH, for a broad class of functions f. To the best of our knowledge, this is the first formal limitation proven about fast multipole methods.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00057"}, {"primary_key": "2584195", "vector": [], "sparse_vector": [], "title": "Isotropy and Log-Concave Polynomials: Accelerated Sampling and High-Precision Counting of Matroid Bases.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We define a notion of isotropy for discrete set distributions. If μ is a distribution over subsets S of a ground set [ n], we say that μ is in isotropic position if \\mathbbPS ~ μ[e ∈ S] is the same for all e ∈ [n]. We design a new approximate sampling algorithm that leverages isotropy for the class of distributions μ that have a log-concave generating polynomial; this class includes determinantal point processes, strongly Rayleigh distributions, and uniform distributions over matroid bases. We show that when μ is in approximately isotropic position, the running time of our algorithm depends polynomially on the size of the set S, and only logarithmically on n. When n is much larger than the size of S, this is significantly faster than prior algorithms, and can even be sublinear in n. We then show how to transform a non-isotropic μ into an equivalent approximately isotropic form with a polynomial-time pre-processing step, accelerating subsequent sampling times. The main new ingredient enabling our algorithms is a class of negative dependence inequalities that may be of independent interest. As an application of our results, we show how to approximately count bases of a matroid of rank k over a ground set of n elements to within a factor of 1+ε in time O((n+1/ε 2 ) ·poly(k,logn)). This is the first algorithm that runs in nearly linear time for fixed rank k, and achieves an inverse polynomially low approximation error. The full version of this paper is available at: https://arxiv.org/abs/2004.09079.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00126"}, {"primary_key": "2584196", "vector": [], "sparse_vector": [], "title": "Spectral Independence in High-Dimensional Expanders and Applications to the Hardcore Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We say a discrete probability distribution over subsets of a finite ground set is spectrally independent if an associated pairwise influence matrix has a bounded largest eigenvalue for the distribution and all of its conditional distributions. We prove that if a distribution is spectrally independent, then the corresponding high dimensional simplicial complex is a local spectral expander. Using a line of recent works on mixing time of high dimensional walks on simplicial complexes [KM17]; [DK17]; [KO18]; [AL20], this implies that the corresponding Glauber dynamics mixes rapidly and generates (approximate) samples from the given distribution. As an application, we show that natural Glauber dynamics mixes rapidly (in polynomial time) to generate a random independent set from the hardcore model up to the uniqueness threshold. This improves the quasi-polynomial running time of <PERSON><PERSON>'s deterministic correlation decay algorithm [Wei06] for estimating the hardcore partition function, also answering a long-standing open problem of mixing time of Glauber dynamics [LV97]; [LV99]; [DG00]; [Vig01]; [Eft+16].", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00125"}, {"primary_key": "2584197", "vector": [], "sparse_vector": [], "title": "Edit Distance in Near-Linear Time: it&apos;s a Constant Factor.", "authors": ["<PERSON><PERSON><PERSON>", "Negev <PERSON>"], "summary": "We present an algorithm for approximating the edit distance between two strings of length n in time n 1+ε , for any , up to a constant factor. Our result completes a research direction set forth in the recent breakthrough paper [1], which showed the first constant-factor approximation algorithm with a (strongly) sub-quadratic running time. The recent results [2], [3] have shown near-linear complexity only under the restriction that the edit distance is close to maximal (equivalently, there is a near-linear additive approximation). In contrast, our algorithm obtains a constant-factor approximation in near-linear running time for any input strings.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00096"}, {"primary_key": "2584198", "vector": [], "sparse_vector": [], "title": "Sample-efficient learning of quantum many-body systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of learning the Hamiltonian of a quantum many-body system given samples from its Gibbs (thermal) state. The classical analog of this problem, known as learning graphical models or Boltzmann machines, is a well-studied question in machine learning and statistics. In this work, we give the first sample-efficient algorithm for the quantum Hamiltonian learning problem. In particular, we prove that polynomially many samples in the number of particles (qudits) are necessary and sufficient for learning the parameters of a spatially local Hamiltonian in $\\ell_{2}$ -norm. Our main contribution is in establishing the strong convexity of the log-partition function of quantum many-body systems, which along with the maximum entropy estimation yields our sample-efficient algorithm. Classically, the strong convexity for partition functions follows from the Markov property of Gibbs distributions. This is, however, known to be violated in its exact form in the quantum case. We introduce several new ideas to obtain an unconditional result that avoids relying on the Markov property of quantum systems, at the cost of a slightly weaker bound. In particular, we prove a lower bound on the variance of quasi-local operators with respect to the Gibbs state, which might be of independent interest. Our work paves the way toward a more rigorous application of machine learning techniques to quantum many-body problems.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00069"}, {"primary_key": "2584199", "vector": [], "sparse_vector": [], "title": "Quantum Speedup for Graph Sparsification, Cut Approximation and Laplacian Solving.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Graph sparsification underlies a large number of algorithms, ranging from approximation algorithms for cut problems to solvers for linear systems in the graph Laplacian. In its strongest form, \"spectral sparsification\" reduces the number of edges to near-linear in the number of nodes, while approximately preserving the cut and spectral structure of the graph. The breakthrough work by <PERSON><PERSON><PERSON><PERSON> and <PERSON> (STOC'96) and <PERSON><PERSON><PERSON> and <PERSON> (STOC'04) showed that sparsification can be done optimally in time near-linear in the number of edges of the original graph. In this work we demonstrate a polynomial quantum speedup for spectral sparsification and many of its applications. In particular, we give a quantum algorithm that, given a weighted graph with n nodes and m edges, outputs a classical description of an ε-spectral sparsifier in sublinear time ~O(√{mn/ε). We prove that this is tight up to polylog-factors. The algorithm builds on a string of existing results, most notably sparsification algorithms by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (STOC'08) and <PERSON><PERSON><PERSON> and <PERSON> (TOPC'16), a spanner construction by <PERSON><PERSON> and <PERSON><PERSON> (STOC'01), a single-source shortest paths quantum algorithm by <PERSON><PERSON><PERSON> et al. (ICALP'04) and an efficient k-wise independent hash construction by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (STOC'15). Our algorithm implies a quantum speedup for solving Laplacian systems and for approximating a range of cut problems such as min cut and sparsest cut.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00065"}, {"primary_key": "2584200", "vector": [], "sparse_vector": [], "title": "The Round Complexity of Perfect MPC with Active Security and Optimal Resiliency.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In STOC 1988, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (BGW) established an important milestone in the fields of cryptography and distributed computing by showing that every functionality can be computed with perfect (information-theoretic and error-free) security at the presence of an active (aka Byzantine) rushing adversary that controls up to n/3 of the parties. We study the round complexity of general secure multiparty computation in the BGW model. Our main result shows that every functionality can be realized in only four rounds of interaction, and that some functionalities cannot be computed in three rounds. This completely settles the round-complexity of perfect actively-secure optimally-resilient MPC, resolving a long line of research. Our lower-bound is based on a novel round-reduction technique that allows us to lift existing three-round lower-bounds for verifiable secret sharing to four-round lower-bounds for general MPC. To prove the upper-bound, we develop new round-efficient protocols for computing degree-2 functionalities over large fields, and establish the completeness of such functionalities. The latter result extends the recent completeness theorem of <PERSON>, <PERSON> and <PERSON>sa<PERSON> (TCC 2018, Eurocrypt 2019) that was limited to the binary field.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00121"}, {"primary_key": "2584201", "vector": [], "sparse_vector": [], "title": "Multi-Pass Graph Streaming Lower Bounds for Cycle Counting, MAX-CUT, Matching Size, and Other Problems.", "authors": ["<PERSON><PERSON><PERSON>", "Gillat Kol", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Huacheng Yu"], "summary": "Consider the following gap cycle counting problem in the streaming model: The edges of a 2-regular n-vertex graph G are arriving one-by-one in a stream and we are promised that G is a disjoint union of either k-cycles or 2k-cycles for some small k; the goal is to distinguish between these two cases using a limited memory. <PERSON><PERSON><PERSON> and <PERSON> [SODA 2011] introduced this problem and showed that any single-pass streaming algorithm solving it requires n 1-Ω(1/k) space. This result and the proof technique behind it-the Boolean Hidden Hypermatching communication problem-has since been used extensively for proving streaming lower bounds for various problems, including approximating MAX-CUT, matching size, property testing, matrix rank and Schatten norms, streaming unique games and CSPs, and many others. Despite its significance and broad range of applications, the lower bound technique of <PERSON><PERSON><PERSON> and <PERSON> comes with a key weakness that is also inherited by all subsequent results: the Boolean Hidden Hypermatching problem is hard only if there is exactly one round of communication and, in fact, can be solved with logarithmic communication in two rounds. Therefore, all streaming lower bounds derived from this problem only hold for single-pass algorithms. Our goal in this paper is to remedy this state-of-affairs. We prove the first multi-pass lower bound for the gap cycle counting problem: Any p-pass streaming algorithm that can distinguish between disjoint union of k-cycles vs 2k-cycles-or even k-cycles vs one Hamiltonian cycle-requires n 1-1/kΩ(1/p) space. This makes progress on multiple open questions in this line of research dating back to the work of <PERSON><PERSON><PERSON> and <PERSON>. As a corollary of this result and by simple (or even no) modification of prior reductions, we can extend many of previous lower bounds to multi-pass algorithms. For instance, we can now prove that any streaming algorithm that ( 1+ε) -approximates the value of MAX-CUT, maximum matching size, or rank of an n-by- n matrix, requires either n Ω(1) space or Ω(log( 1 /ε)) passes. For all these problems, prior work left open the possibility of even an O(logn) space algorithm in only two passes.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00041"}, {"primary_key": "2584202", "vector": [], "sparse_vector": [], "title": "Near-Quadratic Lower Bounds for Two-Pass Graph Streaming Algorithms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove that any two-pass graph streaming algorithm for the s-t reachability problem in n-vertex directed graphs requires near-quadratic space of n 2-o(1) bits. As a corollary, we also obtain near-quadratic space lower bounds for several other fundamental problems including maximum bipartite matching and (approximate) shortest path in undirected graphs. Our results collectively imply that a wide range of graph problems admit essentially no non-trivial streaming algorithm even when two passes over the input is allowed. Prior to our work, such impossibility results were only known for single-pass streaming algorithms, and the best two-pass lower bounds only ruled out o(n 7/6 ) space algorithms, leaving open a large gap between (trivial) upper bounds and lower bounds.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00040"}, {"primary_key": "2584203", "vector": [], "sparse_vector": [], "title": "Circulation Control for Faster Minimum Cost Flow in Unit-Capacity Graphs.", "authors": ["Kyriakos Axiotis", "Aleksander Madry", "<PERSON>"], "summary": "We present an m 4/3+o(1) logW-time algorithm for solving the minimum cost flow problem in graphs with unit capacity, where W is the maximum absolute value of any edge weight. For sparse graphs, this improves over the best known running time for this problem and, by well-known reductions, also implies improved running times for the shortest path problem with negative weights, minimum cost bipartite b-matching when ||b||1=O(m), and recovers the running time of the currently fastest algorithm for maximum flow in graphs with unit capacities (<PERSON>, 2020). Our algorithm relies on developing an interior point method-based framework which acts on the space of circulations in the underlying graph. From the combinatorial point of view, this framework can be viewed as iteratively improving the cost of a suboptimal solution by pushing flow around circulations. These circulations are derived by computing a regularized version of the standard Newton step, which is partially inspired by previous work on the unit-capacity maximum flow problem (<PERSON>, 2019), and subsequently refined based on the very recent progress on this problem (<PERSON>, 2020). The resulting step problem can then be computed efficiently using the recent work on lp-norm minimizing flows (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 2019). We obtain our faster algorithm by combining this new step primitive with a customized preconditioning method, which aims to ensure that the graph on which these circulations are computed has sufficiently large conductance.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00018"}, {"primary_key": "2584204", "vector": [], "sparse_vector": [], "title": "Beyond Tree Embeddings - a Deterministic Framework for Network Design with Deadlines or Delay.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider network design problems with deadline or delay. All previous results for these models are based on randomized embedding of the graph into a tree (HST) and then solving the problem on this tree. We show that this is not necessary. In particular, we design a deterministic framework for these problems which is not based on embedding. This enables us to provide deterministic poly-log( n)-competitive algorithms for Steiner tree, generalized Steiner tree, node weighted Steiner tree, (non-uniform) facility location and directed Steiner tree with deadlines or with delay (where n is the number of nodes). Our deterministic algorithms also give improved guarantees over some previous randomized results. In addition, we show a lower bound of poly log(n) for some of these problems, which implies that our framework is optimal up to the power of the poly-log. Our algorithms and techniques differ significantly from those in all previous considerations of these problems.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00129"}, {"primary_key": "2584205", "vector": [], "sparse_vector": [], "title": "Communication complexity of Nash equilibrium in potential games (extended abstract).", "authors": ["<PERSON><PERSON>", "<PERSON>via<PERSON>"], "summary": "We prove communication complexity lower bounds for (possibly mixed) Nash equilibrium in potential games. In particular, we show that finding a Nash equilibrium requires poly(N) communication in two-player N×N potential games, and 2 poly(n) communication in n-player two-action games. To the best of our knowledge, these are the first results to demonstrate hardness in any model of (possibly mixed) Nash equilibrium in potential games.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00137"}, {"primary_key": "2584206", "vector": [], "sparse_vector": [], "title": "Testing Positive Semi-Definiteness via Random Submatrices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of testing whether a matrix A ∈ \\mathbbR n×n with bounded entries ( ||A||∞ ≤ 1) is positive semidefinite (PSD), or ε-far in Euclidean distance from the PSD cone, meaning that , where B\\succeq 0 denotes that B is PSD. Our main algorithmic contribution is a non-adaptive tester which distinguishes between these cases using only ~O(1/ε 4 ) queries to the entries of A. 11 Throughout the paper, ~O(·) hides log(1/ε) factors. If instead of the Eucledian norm we considered the distance in spectral norm, we obtain the \" l∞-gap problem\", where A is either PSD or satisfies . For this related problem, we give a ~O(1/ε 2 ) query tester, which we show is optimal up to log(1/ε) factors. Both our testers randomly sample a collection of principal sub-matrices and check whether these sub-matrices are PSD. Consequentially, our algorithms achieve one-sided error: whenever they output that A is not PSD, they return a certificate that A has negative eigenvalues. We complement our upper bound for PSD testing with Eucledian norm distance by giving a ~Ω(1/ε 2 ) lower bound for any non-adaptive algorithm. Our lower bound construction is general, and can be used to derive lower bounds for a number of spectral testing problems. As an example of the applicability of our construction, we obtain a new ~Ω(1/ε 4 ) sampling lower bound for testing the Schatten-1 norm with a εn 1.5 gap, extending a result of <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> [11]. In addition, our hard instance results in new sampling lower bounds for estimating the Ky-Fan Norm, and the cost of rank- k approximations, i.e. .", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00114"}, {"primary_key": "2584207", "vector": [], "sparse_vector": [], "title": "Robust and Sample Optimal Algorithms for PSD Low Rank Approximation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recently, <PERSON><PERSON> and <PERSON><PERSON> (FOCS, 2017) showed that given an n×n positive semidefinite (PSD) matrix A, it is possible to compute a (1+ε-approximate relative-error low-rank approximation to A by querying Õ(nk/ε 2.5 ) entries of A in time Õ(nk/ε 2.5 +nk ω-1 /ε 2(ω-1) ). They also showed that any relative-error low-rank approximation algorithm must query Ω(nk/ε) entries of A, this gap has since remained open. Our main result is to resolve this question by obtaining an optimal algorithm that queries Õ(nk/ε) entries of A and outputs a relative-error low-rank approximation in Õ(n·(k/ε) ω-1 ) time. Note, our running time improves that of <PERSON><PERSON> and <PERSON>, and matches the information-theoretic lower bound if the matrix-multiplication exponent ω is 2. We then extend our techniques to negative-type distance matrices. Here, our input is a pair-wise distance matrix A corresponding to a point set P={x 1 , x 2 , ..., x n } such that A i, j =||x i -x j ||2 2 . <PERSON><PERSON><PERSON> and <PERSON> (NeurIPS, 2018) showed a bi-criteria, relative-error low-rank approximation for negative-type metrics. Their algorithm queries Õ(nk/ε 2.5 ) entries and outputs a rank-( k+4) matrix. We show that the bi-criteria guarantee is not necessary and obtain an Õ(nk/ε) query algorithm, which is optimal. Our algorithm applies to all distance matrices that arise from metrics satisfying negative-type inequalities, including l 1 ,l 2 , spherical metrics, hypermetrics and effective resistances on a graph. We also obtain faster algorithms for ridge regression. Next, we introduce a new robust low-rank approximation model which captures PSD matrices that have been corrupted with noise. We assume that the Frobenius norm of the corruption is bounded. Here, we relax the notion of approximation to additive-error, since it is information-theoretically impossible to obtain a relative-error approximation in this setting. While a sample complexity lower bound precludes sublinear algorithms for arbitrary PSD matrices, we provide the first sublinear time and query algorithms when the corruption on the diagonal entries is bounded. As a special case, we show sample-optimal sublinear time algorithms for low-rank approximation of correlation matrices corrupted by noise.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00054"}, {"primary_key": "2584208", "vector": [], "sparse_vector": [], "title": "Outlier-Robust Clustering of Gaussians and Other Non-Spherical Mixtures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give the first outlier-robust efficient algorithm for clustering a mixture of k statistically separated d - dimensional Gaussians ( k-GMMs). Concretely, our algorithm takes input an ε-corrupted sample from a k-GMM and outputs an approximate clustering that misclassifies at most k O(k) (ε+η) fraction of the points whenever every pair of mixture components are separated by 1-exp(-poly(k/η)) in total variation distance. This is the statistically weakest possible notion of separation and allows, for e.g., clustering of mixtures with components with the same mean with covariances differing in a single unknown direction or separated in Frobenius distance. The running time of our algorithm is d poly(k/η) . Such results were not known prior to our work, even for k=2. More generally, our algorithms succeed for mixtures of any distribution that satisfies two well-studied analytic assumptions - sum-of-squares certifiable hypercontractivity and anti-concentration. As an immediate corollary, they extend to clustering mixtures of arbitrary affine transforms of the uniform distribution on the d-dimensional unit sphere. Even the information theoretic clusterability of separated distributions satisfying our analytic assumptions was not known and is likely to be of independent interest. Our algorithms build on the recent flurry of work relying on certifiable anti-concentration first introduced in [1], [2]. Our techniques expand the sum-of-squares toolkit to show robust certifiability of TV-separated Gaussian clusters in data. This involves giving a low-degree sum-of-squares proof of statements that relate parameter (i.e. mean and covariances) distance to total variation distance by relying only on hypercontractivity and anti-concentration.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00023"}, {"primary_key": "2584209", "vector": [], "sparse_vector": [], "title": "Distributed Lower Bounds for Ruling Sets.", "authors": ["Alki<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given a graph G=(V, E), an ( α,β) -ruling set is a subset S ⊆ V such that the distance between any two vertices in S is at least α, and the distance between any vertex in V and the closest vertex in S is at most β. We present lower bounds for distributedly computing ruling sets. More precisely, for the problem of computing a ( 2, β) - ruling set (and hence also any ( α,β) -ruling set with ) in the LOCAL model of distributed computing, we show the following, where n denotes the number of vertices, Δ the maximum degree, and c is some universal constant independent of n and Δ. · Any deterministic algorithm requires Ω(min{[(logΔ)/(β log log Δ)], log Δ n}) rounds, for all β ≤ c·min{√{[(log Δ)/(log log Δ)]}, log Δ n}. By optimizing Δ, this implies a deterministic lower bound of Ω(√{[log n/(β log log n)]}) for all β ≤ c 3 √{[log n/log log n]}. ·Any randomized algorithm requires Ω(min{[(log Δ)/(β log log Δ)], log log n}) rounds, for all β ≤ c·min{√{[(log Δ)/(log log Δ)]}, log log n}. By optimizing Δ, this implies a randomized lower bound of Ω(√{[log log n/(βlog log log n)]}) for all β ≤ c 3 √{[log log n/log log log n]}. For , this improves on the previously best lower bound of Ω(log*n) rounds that follows from the 30-year-old bounds of Linial [FOCS'87] and Naor [J.Disc.Math.'91] (resp. Ω(1) rounds if β ∈ ω(log*n)). For β = 1, i.e., for the problem of computing a maximal independent set (which is nothing else than a (2, 1)-ruling set), our results improve on the previously best lower bound of Ω(log*n) on trees, as our bounds already hold on trees.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00042"}, {"primary_key": "2584210", "vector": [], "sparse_vector": [], "title": "Pseudospectral Shattering, the Sign Function, and Diagonalization in Nearly Matrix Multiplication Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We exhibit a randomized algorithm which given a square matrix A ∈ \\mathbbC n×n with ||A|| ≤ 1 and , computes with high probability an invertible V and diagonal D such that ||A-VDV -1 || ≤ δ in O(TMM(n)log 2 (n/δ)) arithmetic operations on a floating point machine with O(log 4 (n/δ)logn) bits of precision. The computed similarity V additionally satisfies ||V||||V -1 || ≤ O(n 2.5 /δ). Here TMM(n) is the number of arithmetic operations required to multiply two n×n complex matrices numerically stably, known to satisfy TMM(n)=O(n ω+η ) for every where ω is the exponent of matrix multiplication [1]. The algorithm is a variant of the spectral bisection algorithm in numerical linear algebra [2] with a crucial Gaussian perturbation preprocessing step. Our running time is optimal up to polylogarithmic factors, in the sense that verifying that a given similarity diagonalizes a matrix requires at least matrix multiplication time. It significantly improves the previously best known provable running times of O(n 10 /δ 2 ) arithmetic operations for diagonalization of general matrices [3], and (with regards to the dependence on n) O(n 3 ) arithmetic operations for Hermitian matrices [4], and is the first algorithm to achieve nearly matrix multiplication time for diagonalization in any model of computation (real arithmetic, rational arithmetic, or finite arithmetic). The proof rests on two new ingredients. (1) We show that adding a small complex Gaussian perturbation to any matrix splits its pseudospectrum into n small well-separated components. In particular, this implies that the eigenvalues of the perturbed matrix have a large minimum gap, a property of independent interest in random matrix theory. (2) We give a rigorous analysis of Roberts' [5] Newton iteration method for computing the sign function of a matrix in finite arithmetic, itself an open problem in numerical analysis since at least 1986 [6]. This is achieved by controlling the evolution of the pseudospectra of the iterates using a carefully chosen sequence of shrinking contour integrals in the complex plane.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00056"}, {"primary_key": "2584211", "vector": [], "sparse_vector": [], "title": "Stochastic Weighted Matching: (Stochastic Weighted Matching: (1-ε) Approximation -\\varepsilon$) Approximation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Let G = (V, E) be a given edge-weighted graph and let its realization G be a random subgraph of G that includes each edge e ∈ E independently with probability p. We study a stochastic matching problem where the goal is to non-adaptively pick a sparse subgraph Q of G (without knowing the realization G), such that the maximum weight matching among the realized edges of Q (i.e. graph Q∩G) in expectation approximates the maximum weight matching of the whole realization G. In this paper, we prove that for any ε ∈ (0,1), every graph G has a subgraph Q that has maximum degree only Oε, p(1) and guarantees a ( 1-ε) -approximation. That is, the maximum degree of Q depends only on ε and p (both of which are known to be necessary) and not for example on the number of nodes in G, the edge-weights, etc. The stochastic matching problem has been studied extensively on both weighted and unweighted graphs. Previously, only existence of (close to) half-approximate subgraphs was known for weighted graphs [Yamaguchi and Maehara, SODA'18; <PERSON><PERSON><PERSON><PERSON> et al., SODA'19]. Our result substantially improves over these works, matches the state-of-the-art for unweighted graphs [<PERSON><PERSON><PERSON><PERSON> et al., STOC'20], and settles the approximation factor.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00131"}, {"primary_key": "2584212", "vector": [], "sparse_vector": [], "title": "A Tight Composition Theorem for the Randomized Query Complexity of Partial Functions: Extended Abstract.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We prove two new results about the randomized query complexity of composed functions. First, we show that the randomized composition conjecture is false: there are families of partial Boolean functions f and g such that R(f°g) ≪ R(f)R(g). In fact, we show that the left hand side can be polynomially smaller than the right hand side (though in our construction, both sides are polylogarithmic in the input size of f). Second, we show that for all f and g, R(f°g) = Ω(noisyR(f) R(g)), where noisyR(f) is a measure describing the cost of computing f on noisy oracle inputs. We show that this composition theorem is the strongest possible of its type: for any measure M(·) satisfying R(f°g)=Ω(M(f)R(g)) for all f and g, it must hold that noisyR(f)=Ω(M(f)) for all f. We also give a clean characterization of the measure noisyR(f): it satisfies noisyR(f)=Θ(R(f°GapMajn)/R(GapMajn)), where n is the input size of f and GapMajn is the √n-gap majority function on n bits.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00031"}, {"primary_key": "2584213", "vector": [], "sparse_vector": [], "title": "A New Minimax Theorem for Randomized Algorithms (Extended Abstract).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The celebrated minimax principle of <PERSON> (1977) says that for any Boolean-valued function f with finite domain, there is a distribution μ over the domain of f such that computing f to error ε against inputs from μ is just as hard as computing f to error ε on worst-case inputs. Notably, however, the distribution μ depends on the target error level ε: the hard distribution which is tight for bounded error might be trivial to solve to small bias, and the hard distribution which is tight for a small bias level might be far from tight for bounded error levels. In this work, we introduce a new type of minimax theorem which can provide a hard distribution μ that works for all bias levels at once. We show that this works for randomized query complexity, randomized communication complexity, some randomized circuit models, quantum query and communication complexities, approximate polynomial degree, and approximate logrank. We also prove an improved version of I<PERSON>ag<PERSON>zzo's hardcore lemma. Our proofs rely on two innovations over the classical approach of using <PERSON>'s minimax theorem or linear programming duality. First, we use <PERSON><PERSON>'s minimax theorem to prove a minimax theorem for ratios of bilinear functions representing the cost and score of algorithms. Second, we introduce a new way to analyze low-bias randomized algorithms by viewing them as \"forecasting algorithms\" evaluated by a certain proper scoring rule. The expected score of the forecasting version of a randomized algorithm appears to be a more fine-grained way of analyzing the bias of the algorithm. We show that such expected scores have many elegant mathematical properties: for example, they can be amplified linearly instead of quadratically. We anticipate forecasting algorithms will find use in future work in which a fine-grained analysis of small-bias algorithms is required.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00045"}, {"primary_key": "2584214", "vector": [], "sparse_vector": [], "title": "Symmetries, Graph Properties, and Quantum Speedups.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (2009) and <PERSON><PERSON><PERSON> (2018) showed that fully symmetric (partial) functions do not admit exponential quantum query speedups. This raises a natural question: how symmetric must a function be before it cannot exhibit a large quantum speedup? In this work, we prove that hypergraph symmetries in the adjacency matrix model allow at most a polynomial separation between randomized and quantum query complexities. We also show that, remarkably, permutation groups constructed out of these symmetries are essentially the only permutation groups that prevent super-polynomial quantum speedups. We prove this by fully characterizing the primitive permutation groups that allow super-polynomial quantum speedups. In contrast, in the adjacency list model for bounded-degree graphs-where graph symmetry is manifested differently-we exhibit a property testing problem that shows an exponential quantum speedup. These results resolve open questions posed by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (2010) and <PERSON><PERSON> and <PERSON> (2013).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00066"}, {"primary_key": "2584215", "vector": [], "sparse_vector": [], "title": "Proximity Gaps for Reed-Solomon Codes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Swas<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A collection of sets displays a proximity gap with respect to some property if for every set in the collection, either (i) all members are δ-close to the property in relative Hamming distance or (ii) only a tiny fraction of members are δ-close to the property. In particular, no set in the collection has roughly half of its members δ-close to the property and the others δ-far from it. We show that the collection of affine spaces displays a proximity gap with respect to Reed-Solomon (RS) codes, even over small fields, of size polynomial in the dimension of the code, and the gap applies to any δ smaller than the Johnson/<PERSON>wami-Sudan list-decoding bound of the RS code. We also show near-optimal gap results, over fields of (at least) linear size in the RS code dimension, for δ smaller than the unique decoding radius. Concretely, if δ is smaller than half the minimal distance of an RS code V ⊂ Fq n , every affine space is either entirely δ-close to the code, or alternatively at most an ( n/q)-fraction of it is δ-close to the code. Finally, we discuss several applications of our proximity gap results to distributed storage, multi-party cryptographic protocols, and concretely efficient proof systems. We prove the proximity gap results by analyzing the execution of classical algebraic decoding algorithms for Reed-Solomon codes (due to <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>) on a formal element of an affine space. This involves working with Reed-Solomon codes whose base field is an (infinite) rational function field. Our proofs are obtained by developing an extension (to function fields) of a strategy of Arora and Sudan for analyzing low-degree tests.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00088"}, {"primary_key": "2584216", "vector": [], "sparse_vector": [], "title": "Deterministic Decremental Reachability, SCC, and Shortest Paths via Directed Expanders and Congestion Balancing.", "authors": ["<PERSON>", "<PERSON>", "Thatchaphol <PERSON>"], "summary": "Let G=(V, E, w) be a weighted, directed graph subject to a sequence of adversarial edge deletions. In the decremental single-source reachability problem (SSR), we are given a fixed source s and the goal is to maintain a data structure that can answer path-queries s rightarrowtail v for any v in V. In the more general single-source shortest paths (SSSP) problem the goal is to return an approximate shortest path to v, and in the SCC problem the goal is to maintain strongly connected components of G and to answer path queries within each component. All of these problems have been very actively studied over the past two decades, but all the fast algorithms are randomized and, more significantly, they can only answer path queries if they assume a weaker model: They assume an oblivious adversary which is not adaptive and must fix the update sequence in advance. This assumption significantly limits the use of these data structures, most notably preventing them from being used as subroutines in static algorithms. All the above problems are notoriously difficult in the adaptive setting. In fact, the state-of-the-art is still the Even and Shiloach tree, which dates back all the way to 1981 [1] and achieves total update time O(mn). We present the first algorithms to break through this barrier. •deterministic decremental SSR/SSC with total update time mn{2/3+o(1)} •deterministic decremental SSSP with total update time n{2+2/3+o(1)} To achieve these results, we develop two general techniques for working with dynamic graphs. The first generalizes expander-based tools to dynamic directed graphs. While these tools have already proven very successful in undirected graphs, the underlying expander decomposition they rely on does not exist in directed graphs. We thus need to develop an efficient framework for using expanders in directed graphs, as well as overcome several technical challenges in processing directed expanders. We establish several powerful primitives that we hope will pave the way for other expander-based algorithms in directed graphs. The second technique, which we call congestion balancing, provides a new method for maintaining flow under adversarial deletions. The results above use this technique to maintain an embedding of an expander. The technique is quite general, and to highlight its power, we use it to achieve the following additional result: •The first near-optimal algorithm for decremental bipartite matching", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00108"}, {"primary_key": "2584217", "vector": [], "sparse_vector": [], "title": "Near-Optimal Decremental SSSP in Dense Weighted Digraphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the decremental Single-Source Shortest Path problem (SSSP), we are given a weighted directed graph G= (V, E, w) undergoing edge deletions and a source vertex r in V; let n= vert V vert, m= vert E vert and W be the aspect ratio of the graph. The goal is to obtain a data structure that maintains shortest paths from r to all vertices in V and can answer distance queries in O(1) time, as well as return the corresponding path P in O(vert P vert) time. This problem was first considered by <PERSON> and <PERSON><PERSON> [JACM'81], who provided an algorithm with total update time O(mn) for unweighted undirected graphs; this was later extended to directed weighted graphs [FOCS'95, STOC'99]. There are conditional lower bounds showing that O(mn) is in fact near-optimal [ESA'04, FOCS'14, STOC'15, STOC'20]. In a breakthrough result, <PERSON> et al. showed that total update time min {m{7/6}n{2/3+o(1)}, m{3/4}n{5/4+o(1)} } text{polylog}(W)= mn{0.9+o(1)} text{polylog} (W), is possible if the algorithm is allowed to return (1 + epsilon)-approximate paths, instead of exact ones [STOC'14, ICALP'15]. No further progress was made until <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [SODA'20] provided a new approach for the problem, which yields total time tilde{O}(min {m{2/3}n{4/3} log W, (mn){7/8} log W })= tilde{O}(min {n{8/3} log W, mn{3/4} log W }). Our result builds on this recent approach, but overcomes its limitations by introducing a significantly more powerful abstraction, as well as a different core subroutine. Our new framework yields a decremental (1+ epsilon)-approximate SSSP data structure with total update time tilde{O}(n{2} log{4}W epsilon). Our algorithm is thus near-optimal for dense graphs with polynomial edge-weights. Our framework can also be applied to sparse graphs to obtain total update time tilde{O}(mn{2/3} log{3}W epsilon). Combined, these data structures dominate all previous results. Like all previous o(mn) algorithms that can return a path (not just a distance estimate), our result is randomized and assumes an oblivious adversary. Our framework effectively allows us to reduce SSSP in general graphs to the same problem in directed acyclic graphs (DAGs). We believe that our framework has significant potential to influence future work on directed SSSP, both in the dynamic model and in others.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00107"}, {"primary_key": "2584218", "vector": [], "sparse_vector": [], "title": "Rigid Matrices From Rectangular PCPs or: Hard Claims Have Complex Proofs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Orr Paradise", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a variant of PCPs, that we refer to as rectangular PCPs, wherein proofs are thought of as square matrices, and the random coins used by the verifier can be partitioned into two disjoint sets, one determining the row of each query and the other determining the column. We construct PCPs that are efficient, short, smooth and (almost-)rectangular. As a key application, we show that proofs for hard languages in NTIME(2 n ), when viewed as matrices, are rigid infinitely often. This strengthens and simplifies a recent result of <PERSON><PERSON> and <PERSON> [FOCS, 2019] constructing explicit rigid matrices in FNP. Namely, we prove the following theorem: : There is a constant δ ∈ (0,1) such that there is an FNP-machine that, for infinitely many N, on input 1 N outputs N×N matrices with entries in F 2 that are δN 2 -far (in Hamming distance) from matrices of rank at most 2 logN/Ω(loglogN) . Our construction of rectangular PCPs starts with an analysis of how randomness yields queries in the Reed-Muller-based outer PCP of Ben-Sasson, Goldreich, Harsha, Sudan and Vadhan [SICOMP, 2006; CCC, 2005]. We then show how to preserve rectangularity under PCP composition and a smoothness-inducing transformation. This warrants refined and stronger notions of rectangularity, which we prove for the outer PCP and its transforms.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00084"}, {"primary_key": "2584219", "vector": [], "sparse_vector": [], "title": "Twin-width I: tractable FO model checking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Inspired by a width invariant defined on permutations by <PERSON><PERSON><PERSON><PERSON> and <PERSON> [SODA '14], we introduce the notion of twin-width on graphs and on matrices. Proper minor-closed classes, bounded rank-width graphs, map graphs, Kt - free unit d-dimensional ball graphs, posets with antichains of bounded size, and proper subclasses of dimension-2 posets all have bounded twin-width. On all these classes (except map graphs without geometric embedding) we show how to compute in polynomial time a sequence of d-contractions, witness that the twin-width is at most d. We show that FO model checking, that is deciding if a given first-order formula φ evaluates to true for a given binary structure G on a domain D, is FPT in |φ| on classes of bounded twin-width, provided the witness is given. More precisely, being given a d-contraction sequence for G, our algorithm runs in time f(d,|φ|)·|D| where f is a computable but non-elementary function. We also prove that bounded twin-width is preserved by FO interpretations and transductions (allowing operations such as squaring or complementing a graph). This unifies and significantly extends the knowledge on fixed-parameter tractability of FO model checking on non-monotone classes, such as the FPT algorithm on bounded-width posets by <PERSON><PERSON><PERSON><PERSON> et al. [FOCS '15].", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00062"}, {"primary_key": "2584220", "vector": [], "sparse_vector": [], "title": "Smoothed Complexity of 2-player Nash Equilibria.", "authors": ["Shan<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>via<PERSON>"], "summary": "We prove that computing a Nash equilibrium of a two-player ( n×n) game with payoffs in [-1, 1] is PPAD-hard (under randomized reductions) even in the smoothed analysis setting, smoothing with noise of constant magnitude. This gives a strong negative answer to conjectures of <PERSON><PERSON><PERSON> and <PERSON><PERSON> [ST06] and <PERSON>, <PERSON><PERSON>, and <PERSON>g [CDT09]. In contrast to prior work proving PPAD-hardness after smoothing by noise of magnitude 1/poly(n) [CDT09], our smoothed complexity result is not proved via hardness of approximation for Nash equilibria. This is by necessity, since Nash equilibria can be approximated to constant error in quasi-polynomial time [LMM03]. Our results therefore separate smoothed complexity and hardness of approximation for Nash equilibria in two-player games. The key ingredient in our reduction is the use of a random zero-sum game as a gadget to produce two-player games which remain hard even after smoothing. Our analysis crucially shows that all Nash equilibria of random zero-sum games are far from pure (with high probability), and that this remains true even after smoothing.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00034"}, {"primary_key": "2584221", "vector": [], "sparse_vector": [], "title": "Correlated Pseudorandom Functions from Variable-Density LPN.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Correlated secret randomness is a useful resource for many cryptographic applications. We initiate the study of pseudorandom correlation functions (PCFs) that offer the ability to securely generate virtually unbounded sources of correlated randomness using only local computation. Concretely, a PCF is a keyed function F {k} such that for a suitable joint key distribution (k {0}, k {1}), the outputs (f {k {0}}(x), f {k {1}}(x)) are indistinguishable from instances of a given target correlation. An essential security requirement is that indistinguishability hold not only for outsiders, who observe the pairs of outputs, but also for insiders who know one of the two keys. We present efficient constructions of PCFs for a broad class of useful correlations, including oblivious transfer and multiplication triple correlations, from a variable-density variant of the Learning Parity with Noise assumption (VDLPN). We also present several cryptographic applications that motivate our efficient PCF constructions. The VDLPN assumption is independently motivated by two additional applications. First, different flavors of this assumption give rise to weak pseudorandom function candidates in depth-2 text{AC}{0}[ oplus] that can be conjectured to have subexponential security, matching the best known learning algorithms for this class. This is contrasted with the quasipolynomial security of previous (higher-depth) text{AC}{0}[ oplus] candidates. We support our conjectures by proving resilience to several classes of attacks. Second, VDLPN implies simple constructions of pseudorandom generators and weak pseudorandom functions with security against XOR related-key attacks.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00103"}, {"primary_key": "2584222", "vector": [], "sparse_vector": [], "title": "Coded trace reconstruction in a constant number of traces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The coded trace reconstruction problem asks to construct a code C ⊂ {0,1} n such that any x ∈ C is recoverable from independent outputs (\"traces\") of x from a binary deletion channel (BDC). We present binary codes of rate 1-ε that are efficiently recoverable from exp(Oq(log 1/3 ([1/(ε)]))) (a constant independent of n) traces of a BDCq for any constant deletion probability q ∈ (0,1). We also show that, for rate 1 -ε binary codes, ~Ω(log 5/2 (1/ε)) traces are required. The results follow from a pair of black-box reductions that show that average-case trace reconstruction is essentially equivalent to coded trace reconstruction. We also show that there exist codes of rate 1 -ε over an Oε(1)-sized alphabet that are recoverable from O(log(1/ε)) traces, and that this is tight.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00052"}, {"primary_key": "2584223", "vector": [], "sparse_vector": [], "title": "Deterministic and Efficient Interactive Coding from Hard-to-Decode Tree Codes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The field of Interactive Coding studies how an interactive protocol can be made resilient to channel errors. Even though this field has received abundant attention since <PERSON><PERSON><PERSON>'s seminal paper (FOCS 92), constructing interactive coding schemes that are both deterministic and efficient, and at the same time resilient to adversarial errors (with constant information and error rates), remains an elusive open problem. An appealing approach towards resolving this problem is to construct an efficiently encodable and decodable combinatorial object called a tree code (<PERSON><PERSON><PERSON>, STOC 93). After a lot of effort in this direction, the current state of the art has deterministic constructions of tree codes that are efficiently encodable but require an alphabet of size logarithmic (instead of constant) in the depth of the tree code (<PERSON>, <PERSON>, and <PERSON><PERSON>, STOC 18). We emphasize that we still lack (even heuristic) candidate constructions that are efficiently decodable. In this work, we show that tree codes that are efficiently encodable, but not efficiently decodable, also imply deterministic and efficient interactive coding schemes that are resilient to adversarial errors. Our result immediately implies a deterministic and efficient interactive coding scheme with a logarithmic alphabet (i.e., 1/ log log rate). We show this result using a novel implementation of hashing through deterministic tree codes that is powerful enough to yield interactive coding schemes.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00049"}, {"primary_key": "2584224", "vector": [], "sparse_vector": [], "title": "Bipartite Matching in Nearly-linear Time on Moderately Dense Graphs.", "authors": ["<PERSON>", "<PERSON>", "Danupon <PERSON>", "<PERSON>", "Thatchaphol <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an ~O(m+n 1.5 )-time randomized algorithm for maximum cardinality bipartite matching and related problems (e.g. transshipment, negative-weight shortest paths, and optimal transport) on m-edge, n-node graphs. For maximum cardinality bipartite matching on moderately dense graphs, i.e. m=Ω(n 1.5 ), our algorithm runs in time nearly linear in the input size and constitutes the first improvement over the classic O(m√n)-time [<PERSON> 1970; <PERSON> 1971; <PERSON><PERSON> 1973] and ~O(n ω )-time algorithms [<PERSON>bar<PERSON><PERSON> 1981] (where currently ω ≈ 2.373). On sparser graphs, i.e. when m=n 9/8+δ for any constant , our result improves upon the recent advances of [<PERSON>ry 2013] and [Liu-<PERSON> 2020b, 2020a] which achieve an ~O(m 4/3+o(1) ) runtime. We obtain these results by combining and advancing recent lines of research in interior point methods (IPMs) and dynamic graph algorithms. First, we simplify and improve the IPM of [v.d.<PERSON>-<PERSON> 2020], providing a general primal-dual IPM framework and new sampling-based techniques for handling infeasibility induced by approximate linear system solvers. Second, we provide a simple sublinear-time algorithm for detecting and sampling high-energy edges in electric flows on expanders and show that when combined with recent advances in dynamic expander decompositions, this yields efficient data structures for maintaining the iterates of both [v.<PERSON><PERSON> et al.] and our new IPMs. Combining this general machinery yields a simpler ~O(n√m) time algorithm for matching based on the logarithmic barrier function, and our state-of-the-art ~O(m+n 1.5 ) time algorithm for matching based on the [Lee-Sidford 2014] barrier (as regularized in [v.d.Brand et al.]).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00090"}, {"primary_key": "2584225", "vector": [], "sparse_vector": [], "title": "Near Optimal Linear Algebra in the Online and Sliding Window Models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We initiate the study of numerical linear algebra in the sliding window model, where only the most recent W updates in a stream form the underlying data set. Although many existing algorithms in the sliding window model use or borrow elements from the smooth histogram framework (<PERSON><PERSON> and <PERSON><PERSON>, FOCS 2007), we show that many interesting linear-algebraic problems, including spectral and vector induced matrix norms, generalized regression, and lowrank approximation, are not amenable to this approach in the row-arrival model. To overcome this challenge, we first introduce a unified row-sampling based framework that gives randomized algorithms for spectral approximation, low-rank approximation/projection-cost preservation, and ℓ 1-subspace embeddings in the sliding window model, which often use nearly optimal space and achieve nearly input sparsity runtime. Our algorithms are based on \"reverse online\" versions of offline sampling distributions such as (ridge) leverage scores, ℓ 1 sensitivities, and <PERSON> weights to quantify both the importance and the recency of a row; our structural results on these distributions may be of independent interest for future algorithmic design. Although our techniques initially address numerical linear algebra in the sliding window model, our row-sampling framework rather surprisingly implies connections to the well-studied online model; our structural results also give the first sample optimal (up to lower order terms) online algorithm for low-rank approximation/projection-cost preservation. Using this powerful primitive, we give online algorithms for column/row subset selection and principal component analysis that resolves the main open question of <PERSON><PERSON><PERSON> et al. (FOCS 2019). We also give the first online algorithm for ℓ 1-subspace embeddings. We further formalize the connection between the online model and the sliding window model by introducing an additional unified framework for deterministic algorithms using a merge and reduce paradigm and the concept of online coresets, which we define as a weighted subset of rows of the input matrix that can be used to compute a good approximation to some given function on all of its prefixes. Our sampling based algorithms in the row-arrival online model yield online coresets, giving deterministic algorithms for spectral approximation, low-rank approximation/projection-cost preservation, and ℓ 1-subspace embeddings in the sliding window model that use nearly optimal space.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00055"}, {"primary_key": "2584226", "vector": [], "sparse_vector": [], "title": "The Coin Problem with Applications to Data Streams.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Consider the problem of computing the majority of a stream of n i.i.d. uniformly random bits. This problem, known as the coin problem, is central to a number of counting problems in different data stream models. We show that any streaming algorithm for solving this problem with large constant advantage must use Ω(log n) bits of space. We extend our lower bound to proving tight lower bounds for solving multiple, randomly interleaved copies of the coin problem, as well as for solving the OR of multiple copies of a variant of the coin problem. Our proofs involve new measures of information complexity that are well-suited for data streams. We use these lower bounds to obtain a number of new results for data streams. In each case there is an underlying d dimensional vector x with additive updates to its coordinates given in a stream of length m. The input streams arising from our coin lower bound have nice distributional properties, and consequently for many problems for which we only had lower bounds in general turnstile streams, we now obtain the same lower bounds in more natural models, such as the bounded deletion model, in which ||x|| 2 never drops by a constant fraction of what it was earlier, or in the random order model, in which the updates are ordered randomly. In particular, in the bounded deletion model, we obtain nearly tight lower bounds for approximating ||x|| ∞ up to additive error [1/(√k)]||x|| 2 , approximating ||x|| 2 up to a multiplicative ( 1+ε) factor (resolving a question of <PERSON><PERSON> and <PERSON> in PODS 2018), and solving the Point Query and ℓ 2 -Heavy Hitters Problems. In the random order model, we also obtain new lower bounds for the Point Query and ℓ 2 -Heavy Hitters Problems. We also give new algorithms complementing our lower bounds and illustrating the tightness of the models we consider, including an algorithm for approximating ||x|| ∞ up to additive error [1/(√k)]||x||2 in turnstile streams (resolving a question of Cormode in a 2006 IITK Workshop), and an algorithm for finding ℓ 2 -heavy hitters in randomly ordered insertion streams (which for random order streams, resolves a question of Nelson in a 2018 Warwick Workshop).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00038"}, {"primary_key": "2584227", "vector": [], "sparse_vector": [], "title": "QMA-hardness of Consistency of Local Density Matrices with Applications to Quantum Zero-Knowledge.", "authors": ["<PERSON>", "Alex <PERSON>"], "summary": "We provide several advances to the understanding of the class of Quantum Merlin-Arthur proof systems (QMA), the quantum analogue of NP. Our central contribution is proving a longstanding conjecture that the Consistency of Local Density Matrices (CLDM) problem is QMA-hard under Karp reductions. The input of CLDM consists of local reduced density matrices on sets of at most k qubits, and the problem asks if there is an n-qubit global quantum state that is locally consistent with all of the k-qubit local density matrices. The containment of this problem in QMA and the QMA-hardness under Turing reductions were proved by <PERSON> [APPROX-RANDOM 2006]. <PERSON> also conjectured that CLDM is QMA-hard under Karp reductions, which is desirable for applications, and we finally prove this conjecture. We establish this result using the techniques of simulatable codes of Grilo, Slofstra, and Yuen [FOCS 2019], simplifying their proofs and tailoring them to the context of OMA. In order to develop applications of CLDM, we propose a framework that we call locally simulatable proofs for QMA: this provides QMA proofs that can be efficiently verified by probing only k qubits and, furthermore, the reduced density matrix of any k-qubit subsystem of a good witness can be computed in polynomial time, independently of the witness. Within this framework, we show several advances in zero-knowledge in the quantum setting. We show for the first time a commit-and-open computational zero-knowledge proof system for all of QMA, as a quantum analogue of a \"sigma\" protocol. We then define a Proof of Quantum Knowledge, which guarantees that a prover is effectively in possession of a quantum witness in an interactive proof, and show that our zero-knowledge proof system satisfies this definition. Finally, we show that our proof system can be used to establish that QMA has a quantum non-interactive zero-knowledge proof system in the secret parameter setting. 11 The full version of this work can be found in https://arxiv.org/abs/1911.07782.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00027"}, {"primary_key": "2584228", "vector": [], "sparse_vector": [], "title": "Entanglement is Necessary for Optimal Quantum Property Testing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "There has been a surge of progress in recent years in developing algorithms for testing and learning quantum states that achieve optimal copy complexity [1]–[6]. Unfortunately, they require the use of entangled measurements across many copies of the underlying state and thus remain outside the realm of what is currently experimentally feasible. A natural question is whether one can match the copy complexity of such algorithms using only independent-but possibly adaptively chosen-measurements on individual copies. We answer this in the negative for arguably the most basic quantum testing problem: deciding whether a given $d$ -dimensional quantum state is equal to or $\\epsilon$ -far in trace distance from the maximally mixed state. While it is known how to achieve optimal $O(d/\\epsilon^{2})$ copy complexity using entangled measurements, we show that with independent measurements, $\\Omega(d^{4/3}/\\epsilon^{2})$ is necessary, even if the measurements are chosen adaptively. This resolves a question posed in [7]. To obtain this lower bound, we develop several new techniques, including a chain-rule style proof of <PERSON><PERSON><PERSON>'s lower bound for classical uniformity testing, which may be of independent interest.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00070"}, {"primary_key": "2584229", "vector": [], "sparse_vector": [], "title": "An Equivalence Between Private Classification and Online Prediction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We prove that every concept class with finite Littlestone dimension can be learned by an (approximate) differentially-private algorithm. This answers an open question of <PERSON><PERSON> et al. (STOC 2019) who proved the converse statement (this question was also asked by <PERSON><PERSON> et al. (FOCS 2019)). Together these two results yield an equivalence between online learnability and private PAC learnability. We introduce a new notion of algorithmic stability called \"global stability\" which is essential to our proof and may be of independent interest. We also discuss an application of our results to boosting the privacy and accuracy parameters of differentially-private learners.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00044"}, {"primary_key": "2584230", "vector": [], "sparse_vector": [], "title": "Dichotomy for Graph Homomorphisms with Complex Values on Bounded Degree Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The complexity of graph homomorphisms has been a subject of intense study [1], [2], [3], [4], [5], [6], [7], [8]. The partition function Z A (·) of graph homomorphism is defined by a symmetric matrix A over C. We prove that the complexity dichotomy of [7] extends to bounded degree graphs. More precisely, we prove that either G → Z A (G) is computable in polynomial-time for every G, or for some Δ > 0 it is #P-hard over (simple) graphs G with maximum degree Δ(G) ≤ Δ. The tractability criterion on A for this dichotomy is explicit, and can be decided in polynomial-time in the size of A. We also show that the dichotomy is effective in that either a P-time algorithm for, or a reduction from #SAT to, Z A (·) can be constructed from A, in the respective cases.cases.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00106"}, {"primary_key": "2584231", "vector": [], "sparse_vector": [], "title": "Mechanisms for a No-Regret Agent: Beyond the Common Prior.", "authors": ["Modibo K. Camara", "<PERSON>", "<PERSON><PERSON>"], "summary": "A rich class of mechanism design problems can be understood as incomplete-information games between a principal who commits to a policy and an agent who responds, with payoffs determined by an unknown state of the world. Traditionally, these models require strong and often-impractical assumptions about beliefs (a common prior over the state). In this paper, we dispense with the common prior. Instead, we consider a repeated interaction where both the principal and the agent may learn over time from the state history. We reformulate mechanism design as a reinforcement learning problem and develop mechanisms that attain natural benchmarks without any assumptions on the state-generating process. Our results make use of novel behavioral assumptions for the agent - based on counterfactual internal regret - that capture the spirit of rationality without relying on beliefs. 1 1 For the full version of this paper, see https://arxiv.org/abs/2009.05518.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00033"}, {"primary_key": "2584232", "vector": [], "sparse_vector": [], "title": "Coordinate Methods for Matrix Games.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We develop primal-dual coordinate methods for solving bilinear saddle-point problems of the form minx ∈ Xmaxy ∈ Yy T Ax which contain linear programming, classification, and regression as special cases. Our methods push existing fully stochastic sublinear methods and variance-reduced methods towards their limits in terms of per-iteration complexity and sample complexity. We obtain nearly-constant per-iteration complexity by designing efficient data structures leveraging Taylor approximations to the exponential and a binomial heap. We improve sample complexity via low-variance gradient estimators using dynamic sampling distributions that depend on both the iterates and the magnitude of the matrix entries. Our runtime bounds improve upon those of existing primal-dual methods by a factor depending on sparsity measures of the m by n matrix A. For example, when rows and columns have constant l1/l2 norm ratios, we offer improvements by a factor of m+n in the fully stochastic setting and √{m+n} in the variance-reduced setting. We apply our methods to computational geometry problems, i.e. minimum enclosing ball, maximum inscribed ball, and linear regression, and obtain improved complexity bounds. For linear regression with an elementwise nonnegative matrix, our guarantees improve on exact gradient methods by a factor of √{nnz(A)/(m+n)}.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00035"}, {"primary_key": "2584233", "vector": [], "sparse_vector": [], "title": "Hypergraph $k$-cut for fixed $k$ in deterministic polynomial time.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the Hypergraph- k-Cut problem. The input consists of a hypergraph G = (V, E) with nonnegative hyperedge-costs c:E→ \\mathbbR+ and a positive integer k. The objective is to find a least-cost subset F ⊆ E such that the number of connected components in G-F is at least k. An alternative formulation of the objective is to find a partition of V into k non-empty sets V1, V2, ..., Vk so as to minimize the cost of the hyperedges that cross the partition. Graph- k-Cut, the special case of Hypergraph- k-Cut obtained by restricting to graph inputs, has received considerable attention. Several different approaches lead to a polynomial-time algorithm for Graph- k-Cut when k is fixed, starting with the work of <PERSON><PERSON><PERSON><PERSON> and <PERSON> (1988) [1], [2]. In contrast, it is only recently that a randomized polynomial time algorithm for Hypergraph- k-Cut was developed [3] via a subtle generalization of <PERSON><PERSON>'s random contraction approach for graphs. In this work, we develop the first deterministic polynomial time algorithm for Hypergraph- k-Cut for all fixed k. We describe two algorithms both of which are based on a divide and conquer approach. The first algorithm is simpler and runs in n O(k2 ) time while the second one runs in n O(k) time. Our proof relies on new structural results that allow for efficient recovery of the parts of an optimum k-partition by solving minimum ( S, T) -terminal cuts. Our techniques give new insights even for Graph- k-Cut.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00080"}, {"primary_key": "2584234", "vector": [], "sparse_vector": [], "title": "Deterministic Distributed Expander Decomposition and Routing with Applications in Distributed Derandomization.", "authors": ["<PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "There is a recent exciting line of work in distributed graph algorithms in the CONGEST model that exploit expanders. All these algorithms so far are based on two tools: expander decomposition and expander routing. An ( ε, φ)-expander decomposition removes ε-fraction of the edges so that the remaining connected components have conductance at least φ, i.e., they are φ-expanders, and expander routing allows each vertex v in a φ-expander to very quickly exchange deg(v) messages with any other vertices, not just its local neighbors. In this paper, we give the first efficient deterministic distributed algorithms for both tools. We show that an ( ε, φ) -expander decomposition can be deterministically computed in poly (ε -1 )n o(1) rounds for φ = poly (ε)n -o(1) , and that expander routing can be performed deterministically in poly (φ -1 )n o(1) rounds. Both results match previous bounds of randomized algorithms by [<PERSON> and <PERSON>, PODC 2019] and [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and Su, PODC 2017] up to subpolynomial factors. Consequently, we derandomize existing distributed algorithms that exploit expanders. We show that a minimum spanning tree on n -o(1) -expanders can be constructed deterministically in n o(1) rounds, and triangle detection and enumeration on general graphs can be solved deterministically in O(n 0.58 ) and n 2/3+o(1) rounds, respectively. Using similar techniques, we also give the first polylogarithmic-round randomized algorithm for constructing an ( ε, φ) -expander decomposition in poly (ε -1 , logn) rounds for φ = 1/poly(ε -1 , logn). This algorithm is faster than the previous algorithm by [Chang and Saranurak, PODC 2019] in all regimes of parameters. The previous algorithm needs n Ω(1) rounds for any φ ≥ 1/polylogn.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00043"}, {"primary_key": "2584235", "vector": [], "sparse_vector": [], "title": "Faster Approximate Pattern Matching: A Unified Approach.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the approximate pattern matching problem, given a text T, a pattern P, and a threshold k, the task is to find (the starting positions of) all substrings of T that are at distance at most k from P. We consider the two most fundamental string metrics: Under the Hamming distance, we search for substrings of T that have at most k mismatches with P, while under the edit distance, we search for substrings of T that can be transformed to P with at most k edits. Exact occurrences of P in T have a very simple structure: If we assume for simplicity that |P| 2 ) k-mismatch occurrences of P in T, or both P and T are at Hamming distance O(k) from strings with a common string period of length O(m/k). We tighten this characterization by showing that there are O(k) k-mismatch occurrences in the non-periodic case, and we lift it to the edit distance setting, where we tightly bound the number of k-edit occurrences by O(k 2 ) in the non-periodic case. Our proofs are constructive and let us obtain a unified framework for approximate pattern matching for both considered distances. In particular, we provide meta-algorithms that only rely on a small set of primitive operations. We showcase the generality of our meta-algorithms with results for the fully compressed setting, the dynamic setting, and the standard setting.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00095"}, {"primary_key": "2584236", "vector": [], "sparse_vector": [], "title": "Kernel Density Estimation through Density Constrained Near Neighbor Search.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Paris Siminelakis"], "summary": "In this paper we revisit the kernel density estimation problem: given a kernel K(x, y) and a dataset of n points in high dimensional Euclidean space, prepare a data structure that can quickly output, given a query q, a (1+ ε)-approximation to μ:=[1/(|P|)]Σ p∈P K(p, q). First, we give a single data structure based on classical near neighbor search techniques that improves upon or essentially matches the query time and space complexity for all radial kernels considered in the literature so far. We then show how to improve both the query complexity and runtime by using recent advances in data-dependent near neighbor search. We achieve our results by giving an new implementation of the natural importance sampling scheme. Unlike previous approaches, our algorithm first samples the dataset uniformly (considering a geometric sequence of sampling rates), and then uses existing approximate near neighbor search techniques on the resulting smaller dataset to retrieve the sampled points that lie at an appropriate distance from the query. We show that the resulting sampled dataset has strong geometric structure, making approximate near neighbor search return the required samples much more efficiently than for worst case datasets of the same size. As an example application, we show that this approach yields a data structure that achieves query time μ -(1+0(1))/4 and space complexity μ -(1+0(1)) for the Gaussian kernel. Our data dependent approach achieves query time μ -0.173-0(1) and space μ -(1+0(1)) for the Gaussian kernel. The data dependent analysis relies on new techniques for tracking the geometric structure of the input datasets in a recursive hashing process that we hope will be of interest in other applications in near neighbor search.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00025"}, {"primary_key": "2584237", "vector": [], "sparse_vector": [], "title": "On the Existence of Algebraically Natural Proofs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For every constant , we show that there is a family {P<PERSON>, c} of polynomials whose degree and algebraic circuit complexity are polynomially bounded in the number of variables, that satisfies the following properties: For every family {fn} of polynomials in VP, where fn is an n variate polynomial of degree at most n c with bounded integer coefficients and for N=n c +nn, PN, c vanishes on the coefficient vector of fn. There exists a family {hn} of polynomials where hn is an n variate polynomial of degree at most n c with bounded integer coefficients such that for N=n c +nn, PN, c does not vanish on the coefficient vector of hn. In other words, there are efficiently computable equations for polynomials in VP that have small integer coefficients. In fact, we also prove an analogous statement for the seemingly larger class VNP. Thus, in this setting of polynomials with small integer coefficients, this provides evidence against a natural proof like barrier for proving algebraic circuit lower bounds, a framework for which was proposed in the works of Forbes, Shpilka and Volk [1], and <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON> [2]. Our proofs are elementary and rely on the existence of (non-explicit) hitting sets for VP (and VNP) to show that there are efficiently constructible, low degree equations for these classes and also extend to finite fields of small size. Our proofs are elementary and rely on the existence of (non-explicit) hitting sets for VP (and VNP) to show that there are efficiently constructible, low degree equations for these classes and also extend to finite fields of small size.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00085"}, {"primary_key": "2584238", "vector": [], "sparse_vector": [], "title": "Extractors and Secret Sharing Against Bounded Collusion Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In a recent work, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (FOCS 2019) introduced the notion of bounded collusion protocols (BCPs). BCPs are multiparty communication protocols in which N parties, holding n bits each, attempt to compute some joint function of their inputs, f:({0,1} n ) N →{0,1}. In each round, p parties (the collusion bound) work together to write a single bit on a public blackboard, and the protocol continues until every party knows the value of f. BCPs are a natural generalization of the well-studied number-in-hand (NIH) and number-on-forehead (NOF) models, which are just endpoints on this rich spectrum of protocols (corresponding to p=1 and p=N-1, respectively). In this work, we investigate BCPs more thoroughly, and answer questions about them in the context of communication complexity, randomness extractors, and secret sharing. 1.First, we provide explicit lower bounds against BCPs. Our lower bounds offer a tradeoff between collusion and complexity, and are of the form n Ω(1) when p=0.99N parties collude. This bound is independent of the relationship between N, n, whereas all previous bounds became trivial when . 2.Second, we provide explicit leakage-resilient extractors against BCPs. Also known as cylinder-intersection extractors, these objects are multi-source extractors of the form Ext: ({0,1} n ) N →{0,1}, whose output looks uniform even conditioned on the bits produced (\"leaked\") by a BCP executed over the inputs of the extractor. Our extractors work for sources with min-entropy k ≥ polylog(n) against BCPs with collusion p ≤ N-2. Previously, all such extractors required min-entropy k ≥ 0.99n even when p ≤ O(1). 3.Third, we provide efficient leakage-resilient secret sharing schemes against BCPs. These cryptographic primitives are standard t-out-of- N secret sharing schemes, equipped with an additional guarantee that the secret remains hidden even if the individuals participate in a BCP using their shares. Our schemes can handle collusion up to p ≤ O(t/logt), whereas the previous best scheme required p ≤ O(logN). Along the way, we also construct objects that are more general than those listed above (i.e., compilers), objects that are more specialized (and stronger) than those listed above, and resolve open questions posed by Goyal and Kumar (STOC 2018) and Kumar, Meka, and Sahai (FOCS 2019).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00117"}, {"primary_key": "2584239", "vector": [], "sparse_vector": [], "title": "Almost-Everywhere Circuit Lower Bounds from Non-Trivial Derandomization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In certain complexity-theoretic settings, it is notoriously difficult to prove complexity separations which hold almost everywhere, i.e., for all but finitely many input lengths. For example, a classical open question is whether NEXP is contained in i.o.-NP; that is, it is open whether nondeterministic exponential time computation can be simulated on infinitely many input lengths by an NP algorithm. This difficulty also applies to <PERSON>' algorithmic method for circuit lower bounds [<PERSON>, <PERSON> 2014]. [<PERSON> and <PERSON>, STOC 2018] proved that nondeterminstic quasi-polynomial time is not contained in ACC^0, while it remained an open problem to show that E^NP (2^O(n) time with an NP oracle) is not contained in i.o.-ACC^0. In this paper, we show how many infinitely-often circuit lower bounds proved by the algorithmic method can be adapted to establish almost-everywhere lower bounds. First, we show there is a function f in E^NP such that, for all sufficiently large input lengths n, f cannot be (1/2+exp(-n ∧ e))-approximated by exp(n^e)-size ACC^0 circuits on inputs of length n (for all small e), improving lower bounds in [<PERSON> and <PERSON>, STOC 2020] and [Viola, ECCC 2020]. Second, we construct rigid matrices in P^NP for all but finitely many inputs, rather than infinitely often as in [<PERSON><PERSON> and <PERSON>, FOCS 2019] and [<PERSON><PERSON><PERSON> et al. 2020]. Third, we show there is a positive c such that E^NP has constant-error probabilistic degree at least cn/(log^2 n) for all large enough n, improving an infinitely-often separation by [Viola, ECCC 2020]. Our key to proving almost-everywhere worst-case lower bounds is a new \"constructive\" proof of an NTIME hierarchy theorem proved by [Fortnow and Santhanam, CCC 2016], where we show for every \"weak\" nondeterminstic algorithm, a \"refuter algorithm\" exists that can construct \"bad\" inputs for the hard language. We use this refuter algorithm to construct an almost-everywhere hard function. To extend our lower bounds to the average case, we prove a new XOR Lemma based on approximate linear sums, and combine it with PCP of proximity ideas developed in [Chen and Williams, CCC 2019] and [Chen and Ren, STOC 2020]. As a byproduct of our new XOR Lemma, we obtain a nondeterministic pseudorandom generator for poly-size ACC^0 circuits with seed length polylog(n), which resolves an open question in [Chen and Ren, STOC 2020].", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00009"}, {"primary_key": "2584240", "vector": [], "sparse_vector": [], "title": "Fast Dynamic Cuts, Distances and Effective Resistances via Vertex Sparsifiers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Thatchaphol <PERSON>"], "summary": "We present a general framework of designing efficient dynamic approximate algorithms for optimization problems on undirected graphs. In particular, we develop a technique that, given any problem that admits a certain notion of vertex sparsifiers, gives data structures that maintain approximate solutions in sub-linear update and query time. We illustrate the applicability of our paradigm to the following problems. (1)A fully-dynamic algorithm that approximates all-pair maximum-flows/minimum-cuts up to a nearly logarithmic factor in ~O(n 2/3 ) 11 The ~O(·) notation is used in this paper to hide poly-logarithmic factors. amortized time against an oblivious adversary, and ~O(m 3/4 ) time against an adaptive adversary. (2)An incremental data structure that maintains O(1) - approximate shortest path in n o(1) time per operation, as well as fully dynamic approximate all-pair shortest path and transshipment in ~O(n 2/3 +o(1) ) amortized time per operation. (3)A fully-dynamic algorithm that approximates all-pair effective resistance up to an ( 1+ε) factor in ~O(n 2/3+o(1) ε -O(1) ) amortized update time per operation. The key tool behind result (1) is the dynamic maintenance of an algorithmic construction due to Madry [FOCS' 10], which partitions a graph into a collection of simpler graph structures (known as j-trees) and approximately captures the cut-flow and metric structure of the graph. The O(1)-approximation guarantee of (2) is by adapting the distance oracles by [Thorup-Zwick JACM '05]. Result (3) is obtained by invoking the random-walk based spectral vertex sparsifier by [Durfee et al. STOC '19] in a hierarchical manner, while carefully keeping track of the recourse among levels in the hierarchy. See https://arxiv.org/pdf/2005.02368.pdf for the full version of this paper.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00109"}, {"primary_key": "2584241", "vector": [], "sparse_vector": [], "title": "Near-linear Size Hypergraph Cut Sparsifiers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cuts in graphs are a fundamental object of study, and play a central role in the study of graph algorithms. The problem of sparsifying a graph while approximately preserving its cut structure has been extensively studied and has many applications. In a seminal work, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (1996) showed that given any n-vertex undirected weighted graph G and a parameter ε ∈ (0,1), there is a near-linear time algorithm that outputs a weighted subgraph G' of G of size Õ(n/ε 2 ) such that the weight of every cut in G is preserved to within a ( 1±ε)-factor in G'. The graph G' is referred to as a ( 1±ε)-approximate cut sparsifier of G. A natural question is if such cut-preserving sparsifiers also exist for hypergraphs. <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (2015) initiated a study of this question and showed that given any weighted hypergraph H where the cardinality of each hyperedge is bounded by r, there is a polynomial-time algorithm to find a ( 1±ε)-approximate cut sparsifier of H of size Õ([nr/(ε 2 )]). Since r can be as large as n, in general, this gives a hypergraph cut sparsifier of size Õ(n 2 /ε 2 ), which is a factor n larger than the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bound for graphs. It has been an open question whether or not <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bound is achievable on hypergraphs. In this work, we resolve this question in the affirmative by giving a new polynomial-time algorithm for creating hypergraph sparsifiers of size Õ(n/ε 2 ).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00015"}, {"primary_key": "2584242", "vector": [], "sparse_vector": [], "title": "Rapid Mixing of Glauber Dynamics up to Uniqueness via Contraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For general antiferromagnetic 2-spin systems, including the hardcore model on weighted independent sets and the antiferromagnetic Ising model, there is an FPTAS for the partition function on graphs of maximum degree Δ when the infinite regular tree lies in the uniqueness region by <PERSON> et al. (2013). Moreover, in the tree non-uniqueness region, <PERSON><PERSON> (2010) showed that there is no FPRAS to estimate the partition function unless NP=RP. The algorithmic results follow from the correlation decay approach due to <PERSON><PERSON> (2006) or the polynomial interpolation approach developed by <PERSON><PERSON><PERSON> (2016). However the running time is only polynomial for constant Δ. For the hardcore model, recent work of <PERSON><PERSON> et al. (2020) establishes rapid mixing of the simple single-site Markov chain known as the Glauber dynamics in the tree uniqueness region. Our work simplifies their analysis of the Glauber dynamics by considering the total pairwise influence of a fixed vertex v on other vertices, as opposed to the total influence of other vertices on v, thereby extending their work to all 2-spin models and improving the mixing time. More importantly our proof ties together the three disparate algorithmic approaches: we show that contraction of the so-called tree recursions with a suitable potential function, which is the primary technique for establishing efficiency of <PERSON><PERSON>'s correlation decay approach and <PERSON><PERSON><PERSON>'s polynomial interpolation approach, also establishes rapid mixing of the Glauber dynamics. We emphasize that this connection holds for all 2-spin models (both antiferromagnetic and ferromagnetic), and existing proofs for the correlation decay or polynomial interpolation approach immediately imply rapid mixing of the Glauber dynamics. Our proof utilizes that the graph partition function is a divisor of the partition function for <PERSON>tz's self-avoiding walk tree. This fact leads to new tools for the analysis of the influence of vertices, and may be of independent interest for the study of complex zeros.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00124"}, {"primary_key": "2584243", "vector": [], "sparse_vector": [], "title": "On Exponential-Time Hypotheses, Derandomization, and Circuit Lower Bounds: Extended Abstract.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "E<PERSON>"], "summary": "The Exponential-Time Hypothesis (ETH) is a strengthening of the P ≠ NP conjecture, stating that 3-SAT on n variables cannot be solved in (uniform) time 2 ε·n , for some . In recent years, analogous hypotheses that are \"exponentially-strong\" forms of other classical complexity conjectures (such as NP ⊄ eq BPP or coNP ⊄ eq NP) have also been introduced, and have become widely influential. In this work, we focus on the interaction of exponential-time hypotheses with the fundamental and closely-related questions of derandomization and circuit lower bounds. We show that even relatively-mild variants of exponential-time hypotheses have far-reaching implications to derandomization, circuit lower bounds, and the connections between the two. Specifically, we prove that: 1) The Randomized Exponential-Time Hypothesis (rETH) implies that BPP can be simulated on \"average-case\" in deterministic (nearly-)polynomial-time (i.e., in time 2 ~O(log(n)) =n loglog(n)O(1) ). The derandomization relies on a conditional construction of a pseudorandom generator with near-exponential stretch (i.e., with seed length ~O(log(n))); this significantly improves the state-of-the-art in uniform \"hardness-to-randomness\" results, which previously only yielded pseudorandom generators with sub-exponential stretch from such hypotheses. 2) The Non-Deterministic Exponential-Time Hypothesis (NETH) implies that derandomization of BPP is completely equivalent to circuit lower bounds against E, and in particular that pseudorandom generators are necessary for derandomization. In fact, we show that the foregoing equivalence follows from a very weak version of NETH, and we also show that this very weak version is necessary to prove a slightly stronger conclusion that we deduce from it. Lastly, we show that disproving certain exponential-time hypotheses requires proving breakthrough circuit lower bounds. In particular, if CireuitSAT for circuits over n bits of size poly(n) can be solved by probabilistic algorithms in time 2 n/polylog(n) , then BPε does not have circuits of quasilinear size.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00010"}, {"primary_key": "2584244", "vector": [], "sparse_vector": [], "title": "Combinatorial Group Testing and Sparse Recovery Schemes with Near-Optimal Decoding Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the long-studied problem of combinatorial group testing, one is asked to detect a set of k defective items out of a population of size n, using m ≪ n disjunctive measurements. In the non-adaptive setting, the most widely used combinatorial objects are disjunct and list-disjunct matrices, which define incidence matrices of test schemes. Disjunct matrices allow the identification of the exact set of defectives, whereas list disjunct matrices identify a small superset of the defectives. Apart from the combinatorial guarantees, it is often of key interest to equip measurement designs with efficient decoding algorithms. The most efficient decoders should run in sublinear time in n, and ideally near-linear in the number of measurements m. In this work, we give several constructions with an optimal number of measurements and near-optimal decoding time for the most fundamental group testing tasks, as well as for central tasks in the compressed sensing and heavy hitters literature. For many of those tasks, the previous measurement-optimal constructions needed time either quadratic in the number of measurements or linear in the universe size. Among our results are the following: a construction of disjunct matrices matching the best-known construction in terms of the number of rows m, but achieving nearly linear decoding time in m; a construction of list disjunct matrices with the optimal m=O(klog(n/k) number of rows and nearly linear decoding time in m; error-tolerant variations of the above constructions; a non-adaptive group testing scheme for the \"for-each\" model with m=O(klogn) measurements and O(m) decoding time; a streaming algorithm for the \"for-all\" version of the heavy hitters problem in the strict turnstile model with near-optimal query time, as well as a \"list decoding\" variant obtaining also near-optimal update time and O(klog(n/k)) space usage; an l2/l2 weak identification system for compressed sensing with nearly optimal sample complexity and nearly linear decoding time in the sketch length. Most of our results are obtained via a clean and novel approach that avoids list-recoverable codes or related complex techniques that were present in almost every state-of-the-art work on efficiently decodable constructions of such objects.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00115"}, {"primary_key": "2584245", "vector": [], "sparse_vector": [], "title": "List Decodable Mean Estimation in Nearly Linear Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Learning from data in the presence of outliers is a fundamental problem in statistics. Until recently, no computationally efficient algorithms were known to compute the mean of a high dimensional distribution under natural assumptions in the presence of even a small fraction of outliers. In this paper, we consider robust statistics in the presence of overwhelming outliers where the majority of the dataset is introduced adversarially. With only an fraction of \"in-liers\" (clean data) the mean of a distribution is unidentifiable. However, in their influential work, [1] introduces a polynomial time algorithm recovering the mean of distributions with bounded covariance by outputting a succinct list of O(1/α) candidate solutions, one of which is guaranteed to be close to the true distributional mean; a direct analog of `List Decoding' in the theory of error correcting codes. In this work, we develop an algorithm for list decodable mean estimation in the same setting achieving up to constants the information theoretically optimal recovery, optimal sample complexity, and in nearly linear time up to polylogarithmic factors in dimension. Our conceptual innovation is to design a descent style algorithm on a nonconvex landscape, iteratively removing minima to generate a succinct list of solutions. Our runtime bottleneck is a saddle-point optimization for which we design custom primal dual solvers for generalized packing and covering SDP's under Ky-Fan norms, which may be of independent interest. We refer the reader to [2] for the full version of this paper.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00022"}, {"primary_key": "2584246", "vector": [], "sparse_vector": [], "title": "Optimal Streaming Approximations for all Boolean Max-2CSPs and Max-ksat.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We prove tight upper and lower bounds on approximation ratios of all Boolean Max-2CSP problems in the streaming model. Specifically, for every type of Max-2CSP problem, we give an explicit constant α, s.t. for any (i) there is an ( α-ε)-streaming approximation using space O(logn); and (ii) any ( α+ε)-streaming approximation requires space Ω(√n). This generalizes the celebrated work of [<PERSON><PERSON><PERSON>, Khan<PERSON>, Sudan SODA 2015; <PERSON><PERSON><PERSON>, Krachun STOC 2019], who showed that the optimal approximation ratio for Max-CUT was 1/2. Prior to this work, the problem of determining this ratio was open for all other Max-2CSPs. Our results are quite surprising for some specific Max-2CSPs. For the Max-DICUT problem, there was a gap between an upper bound of 1/2 and a lower bound of 2/5 [<PERSON><PERSON>, <PERSON>, Velusamy APPROX 2017]. We show that neither of these bounds is tight, and the optimal ratio for Max-DICUT is 4/9. We also establish that the tight approximation for Max-2SAT is √2/2, and for Exact Max-2SAT it is 3/4. As a byproduct, our result gives a separation between space-efficient approximations for Max-2SAT and Exact Max-2SAT. This is in sharp contrast to the setting of polynomial-time algorithms with polynomial space, where the two problems are known to be equally hard to approximate. Finally, we prove that the tight streaming approximation for Max-kSAT is √2/2 for every k ≥ 2.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00039"}, {"primary_key": "2584247", "vector": [], "sparse_vector": [], "title": "Tight Quantum Time-Space Tradeoffs for Function Inversion.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In function inversion, we are given a function f:[N]→[N], and want to prepare some advice of size S, such that we can efficiently invert any image in time T. This is a well studied problem with profound connections to cryptography, data structures, communication complexity, and circuit lower bounds. Investigation of this problem in the quantum setting was initiated by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (2015), who proved a lower bound of ST 2 =Ω̃(N) for random permutations against classical advice, leaving open an intriguing possibility that <PERSON><PERSON>'s search can be sped up to time Õ(√{N/S}). Recent works by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (2019), and <PERSON>, <PERSON>, and <PERSON><PERSON> (2019) extended the argument for random functions and quantum advice, but the lower bound remains ST 2 =Ω̃(N). In this work, we prove that even with quantum advice, ST+ T 2 =Ω̃(N), is required for an algorithm to invert random functions. This demonstrates that <PERSON><PERSON>'s search is optimal for S=Õ(√N), ruling out any substantial speed-up for <PERSON><PERSON>'s search even with quantum advice. Further improvements to our bounds would imply new classical circuit lower bounds, as shown by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (2019). To prove this result, we develop a general framework for establishing quantum time-space lower bounds. We further demonstrate the power of our framework by proving the following results. (a) <PERSON>'s box problem: We prove a tight quantum time-space lower bound for classical advice. For quantum advice, we prove a first time-space lower bound using shadow tomography. These results resolve two open problems posted by <PERSON>yebi et al (2015). (b) Salted cryptography: We show that \"salting generically provably defeats preprocessing,\" a result shown by Coretti, Dodis, Guo, and Steinberger (2018), also holds in the quantum setting. In particular, we prove quantum time-space lower bounds for a wide class of salted cryptographic primitives in the quantum random oracle model. This yields the first quantum time-space lower bound for salted collision-finding, which in turn implies that PWPP O ⊈ FBQP O /qpoly relative to a random oracle O.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00068"}, {"primary_key": "2584248", "vector": [], "sparse_vector": [], "title": "A Deterministic Algorithm for Balanced Cut with Applications to Dynamic Connectivity, Flows, and Beyond.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Danupon <PERSON>", "<PERSON>", "Thatchaphol <PERSON>"], "summary": "We consider the classical Minimum Balanced Cut problem: given a graph G, compute a partition of its vertices into two subsets of roughly equal volume, while minimizing the number of edges connecting the subsets. We present the first deterministic, almost-linear time approximation algorithm for this problem. Specifically, our algorithm, given an n-vertex m-edge graph G and any parameter 1 ≤ r ≤ O(logn), computes a (logm) r2 -approximation for Minimum Balanced Cut in G, in time O(m 1+O(1/r)+o(1) ·(logm) O(r2 )). In particular, we obtain a (logm) 1/ε -approximation in time m 1+O(√{ε}) for any constant , and a (logm) f(m) -approximation in time m 1+o(1) , for any slowly growing function f(m). We obtain deterministic algorithms with similar guarantees for the Sparsest Cut and the Lowest-Conductance Cut problems. Our algorithm for the Minimum Balanced Cut problem in fact provides a stronger guarantee: it either returns a balanced cut whose value is close to a given target value, or it certifies that such a cut does not exist by exhibiting a large subgraph of G that has high conductance. We use this algorithm to obtain deterministic algorithms for dynamic connectivity and minimum spanning forest, whose worst-case update time on an n-vertex graph is n o(1) , thus resolving a major open problem in the area of dynamic graph algorithms. Our work also implies deterministic algorithms for a host of additional problems, whose time complexities match, up to subpolynomial in n factors, those of known randomized algorithms. The implications include almost-linear time deterministic algorithms for solving Laplacian systems and for approximating maximum flows in undirected graphs.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00111"}, {"primary_key": "2584249", "vector": [], "sparse_vector": [], "title": "Towards Better Approximation of Graph Crossing Number.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph Crossing Number is a fundamental and extensively studied problem with wide ranging applications. In this problem, the goal is to draw an input graph G in the plane so as to minimize the number of crossings between the images of its edges. The problem is notoriously difficult, and despite extensive work, non-trivial approximation algorithms are only known for bounded-degree graphs. Even for this special case, the best current algorithm achieves a Õ̃(√n)-approximation, while the best current negative results do not rule out constantfactor approximation. All current approximation algorithms for the problem build on the same paradigm, which is also used in practice: compute a set E' of edges (called a planarizing set) such that G \\ E' is planar; compute a planar drawing of G\\E'; then add the drawings of the edges of E' to the resulting drawing. Unfortunately, there are examples of graphs G, in which any implementation of this method must incur Ω(OPT 2 ) crossings, where OPT is the value of the optimal solution. This barrier seems to doom the only currently known approach to designing approximation algorithms for the problem, and to prevent it from yielding a better than O(√n)-approximation. In this paper we propose a new paradigm that allows us to overcome this barrier. We show an algorithm, that, given a bounded-degree graph G and a planarizing set E' of its edges, computes another planarizing edge set E'' with E' ⊆ E'', such that |E''| is relatively small, and there exists a nearoptimal drawing of G in which no edges of G \\ E'' participate in crossings. This allows us to reduce the Crossing Number problem to Crossing Number with Rotation System - a variant of the Crossing Number problem, in which the ordering of the edges incident to every vertex is fixed as part of input. In our reduction, we obtain an instance G' of this problem, where |E(G')| is roughly bounded by the crossing number of the original graph G. We show a randomized algorithm for this new problem, that allows us to obtain an O(n 1/2-ε )approximation for Graph Crossing Number on bounded-degree graphs, for some constant ε > 0.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00016"}, {"primary_key": "2584250", "vector": [], "sparse_vector": [], "title": "On Light Spanners, Low-treewidth Embeddings and Efficient Traversing in Minor-free Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Understanding the structure of minor-free metrics, namely shortest path metrics obtained over a weighted graph excluding a fixed minor, has been an important research direction since the fundamental work of <PERSON> and <PERSON>. A fundamental idea that helps both to understand the structural properties of these metrics and lead to strong algorithmic results is to construct a \"small-complexity\" graph that approximately preserves distances between pairs of points of the metric. We show the two following structural results for minor-free metrics: 1) Construction of a light subset spanner. Given a subset of vertices called terminals, and ε, in polynomial time we construct a sub graph that preserves all pairwise distances between terminals up to a multiplicative 1+ε factor, of total weight at most Oε(1) times the weight of the minimal Steiner tree spanning the terminals. 2) Construction of a stochastic metric embedding into low treewidth graphs with expected additive distortion εD. Namely, given a minor-free graph G = (V, E, w) of diameter D, and parameter ε, we construct a distribution D over dominating metric embeddings into treewidth- Oε(logn) graphs such that ∀u, v ∈ V, \\mathbbEf ~ D[dH(f(u), f(v))] ≤ dG(u, v)+εD. Our results have the following algorithmic consequences: (1) the first efficient approximation scheme for subset TSP in minor-free metrics; (2) the first approximation scheme for bounded-capacity vehicle routing in minor-free metrics; (3) the first efficient approximation scheme for bounded-capacity vehicle routing on bounded genus metrics. En route to the latter result, we design the first FPT approximation scheme for bounded-capacity vehicle routing on bounded-treewidth graphs (parameterized by the treewidth).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00061"}, {"primary_key": "2584251", "vector": [], "sparse_vector": [], "title": "Revisiting Tardos&apos;s Framework for Linear Programming: Faster Exact Solutions using Approximate Solvers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "László A. <PERSON>"], "summary": "In breakthrough work, <PERSON><PERSON><PERSON> (Oper. Res. '86) gave a proximity based framework for solving linear programming (LP) in time depending only on the constraint matrix in the bit complexity model. In <PERSON><PERSON><PERSON>'s framework, one reduces solving the LP min(c, x), Ax=b, x ≥ 0, A Z m×n , to solving O(nm) LPs in A having small integer coefficient objectives and right-hand sides using any exact LP algorithm. This gives rise to an LP algorithm in time poly (n, m log Δ A ), where Δ A is the largest subdeterminant of A. A significant extension to the real model of computation was given by <PERSON><PERSON><PERSON> and <PERSON> (Math. Prog. '96), giving a specialized interior point method that runs in time poly (n, m,log χ̑ A ), depending on <PERSON>'s χ̑ A , a well-studied condition number. In this work, we extend <PERSON><PERSON><PERSON>'s original framework to obtain such a running time dependence. In particular, we replace the exact LP solves with approximate ones, enabling us to directly leverage the tremendous recent algorithmic progress for approximate linear programming. More precisely, we show that the fundamental \"accuracy\" needed to exactly solve any LP in A is inverse polynomial in n and log χ̑ A . Plugging in the recent algorithm of <PERSON> (SODA '20), our method computes an optimal primal and dual solution using O(mn ω+1+0(1) log(χ̑ A +n)) arithmetic operations, outperforming the specialized interior point method of <PERSON><PERSON><PERSON> and Ye and its recent improvement by <PERSON><PERSON> et al (STOC '20). By applying the preprocessing algorithm of the latter paper, the dependence can also be reduced from χ̑ A to χ̑ A *, the minimum value of χ̑ AD attainable via column rescalings. Our framework is applicable to achieve the poly (n, m,log χ̑ A *) bound using essentially any weakly polynomial LP algorithm, such as the ellipsoid method. At a technical level, our framework combines together approximate LP solutions to compute exact ones, making use of constructive proximity theorems-which bound the distance between solutions of \"nearby\" LPs-to keep the required accuracy low.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00091"}, {"primary_key": "2584252", "vector": [], "sparse_vector": [], "title": "New Techniques for Proving Fine-Grained Average-Case Hardness.", "authors": ["<PERSON>", "<PERSON>", "Virginia Vassilevska Williams"], "summary": "The recent emergence of fine-grained cryptography strongly motivates developing an average-case analogue of Fine-Grained Complexity (FGC). Prior work [<PERSON>reich-Roth<PERSON>lum 2018, Boix<PERSON> et al. 2019, <PERSON> et al. 2017] developed worst-case to average-case fine-grained reductions (WCtoACFG) for certain algebraic and counting problems over natural distributions and used them to obtain a limited set of cryptographic primitives. To obtain stronger cryptographic primitives based on standard FGC assumptions, ideally, one would like to develop WCtoACFG reductions from the core hard problems of FGC, Orthogonal Vectors (OV), CNF-SAT, 3SUM, All-Pairs Shortest Paths (APSP) and zero- k clique. Unfortunately, it is unclear whether these problems actually are hard for any natural distribution. It is known, that e.g. OV can be solved quickly for very natural distributions [<PERSON><PERSON><PERSON> 2019], and in this paper we show that even counting the number of OV pairs on average has a fast algorithm. This paper defines new versions of OV, kSUM and zero- k-clique that are both worst-case and average-case fine-grained hard assuming the core hypotheses of FGC. We then use these as a basis for fine-grained hardness and average-case hardness of other problems. The new problems represent their inputs in a certain \"factored\" form. We call them \"factored\"-OV, \"factored\"-zero- k-clique and \"factored\"-3SUM. We show that factored- k-OV and factored kSUM are equivalent and are complete for a class of problems defined over Boolean functions. Factored zero- k-clique is also complete, for a different class of problems. Our hard factored problems are also simple enough that we can reduce them to many other problems, e.g. to edit distance, k-LCS and versions of Max-Flow. We further consider counting variants of the factored problems and give WCtoACFG reductions for them for a natural distribution. Through FGC reductions we then get average-case hardness for well-studied problems like regular expression matching from standard worst-case FGC assumptions. To obtain our WCtoACFG reductions, we formalize the framework of [Boix-Adserà et al. 2019] that was used to give a WCtoACFG reduction for counting k-cliques. We define an explicit property of problems such that if a problem has that property one can use the framework on the problem to get a WCtoACFG self reduction. We then use the framework to slightly extend Bolx-Adserà et al.'s average-case counting k-cliques result to average-case hardness for counting arbitrary subgraph patterns of constant size in -partite graphs. The fine-grained public-key encryption scheme of [LaVigne et al.'20] is based on an average-case hardness hypothesis for the decision problem, zero- k-clique, and the known techniques for building such schemes break down for algebraic/counting problems. Meanwhile, the WCtoACFG reductions so far have only been for counting problems. To bridge this gap, we show that for a natural distribution, an algorithm that detects a zero- k-clique with high enough probability also implies an algorithm that can count zero- k-cliques with high probability. This gives hope that the FGC cryptoscheme of [LaVigne et al.'20] can be based on standard FGC assumptions.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00077"}, {"primary_key": "2584253", "vector": [], "sparse_vector": [], "title": "Scheduling with Communication Delays via LP Hierarchies and Clustering.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the classic problem of scheduling jobs with precedence constraints on identical machines to minimize makespan, in the presence of communication delays. In this setting, denoted by P | prec, c | Cmax, if two dependent jobs are scheduled on different machines, then at least c units of time must pass between their executions. Despite its relevance to many applications, this model remains one of the most poorly understood in scheduling theory. Even for a special case where an unlimited number of machines is available, the best known approximation ratio is 2/3·(c+1), whereas <PERSON>'s greedy list scheduling algorithm already gives a ( c+1) -approximation in that setting. An outstanding open problem in the top-10 list by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> and its recent update by <PERSON><PERSON> asks whether there exists a constant-factor approximation algorithm. In this work we give a polynomial-time O(logc·logm)-approximation algorithm for this problem, where m is the number of machines and c is the communication delay. Our approach is based on a Sherali-Adams lift of a linear programming relaxation and a randomized clustering of the semimetric space induced by this lift. The full version of this paper is available on arXiv.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00081"}, {"primary_key": "2584254", "vector": [], "sparse_vector": [], "title": "Small Covers for Near-Zero Sets of Polynomials and Learning Latent Variable Models.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Let V be any vector space of multivariate degree- d homogeneous polynomials with co-dimension at most k, and S be the set of points where all polynomials in V nearly vanish. We establish a qualitatively optimal upper bound on the size of ε-covers for S, in the l2-norm. Roughly speaking, we show that there exists an ε-cover for S of cardinality M=(k/ε) Od(k1/d ). Our result is constructive yielding an algorithm to compute such an ε-cover that runs in time poly(M). Building on our structural result, we obtain significantly improved learning algorithms for several fundamental high-dimensional probabilistic models with hidden variables. These include density and parameter estimation for k-mixtures of spherical Gaussians (with known common covariance), PAC learning one-hidden-layer ReLU networks with k hidden units (under the Gaussian distribution), density and parameter estimation for k-mixtures of linear regressions (with Gaussian covariates), and parameter estimation for k-mixtures of hyperplanes. Our algorithms run in time quasi-polynomial in the parameter k. Previous algorithms for these problems had running times exponential in k Ω(1) . At a high-level our algorithms for all these learning problems work as follows: By computing the low-degree moments of the hidden parameters, we are able to find a vector space of polynomials that nearly vanish on the unknown parameters. Our structural result allows us to compute a quasi-polynomial sized cover for the set of hidden parameters, which we exploit in our learning algorithms.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00026"}, {"primary_key": "2584255", "vector": [], "sparse_vector": [], "title": "Nearly Optimal Pseudorandomness From Hardness.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Existing proofs that deduce BPP = P from circuit lower bounds convert randomized algorithms into deterministic algorithms with a large polynomial slowdown. We convert randomized algorithms into deterministic ones with little slowdown. Specifically, assuming exponential lower bounds against randomized single-valued nondeterministic (SVN) circuits, we convert any randomized algorithm over inputs of length n running in time t ≥ n to a deterministic one running in time t 2+α for an arbitrarily small constant . Such a slowdown is nearly optimal, as, under complexity-theoretic assumptions, there are problems with an inherent quadratic derandomization slowdown. We also convert any randomized algorithm that errs rarely into a deterministic algorithm having a similar running time (with pre-processing). The latter derandomization result holds under weaker assumptions, of exponential lower bounds against deterministic SVN circuits. Our results follow from a new, nearly optimal, explicit pseudorandom generator fooling circuits of size s with seed length (1 + α)log s, under the assumption that there exists a function f ϵ E that requires randomized SVN circuits of size at least 2 (1-α')n , where. α=O(α'). The construction uses, among other ideas, a new connection between pseudoentropy generators and locally list recoverable codes.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00102"}, {"primary_key": "2584256", "vector": [], "sparse_vector": [], "title": "An Improved Exponential-Time Approximation Algorithm for Fully-Alternating Games Against Nature.", "authors": ["<PERSON>"], "summary": "\"Games against Nature\" [1] are two-player games of perfect information, in which one player's moves are made randomly (here, uniformly); the final payoff to the non-random player is given by some bounded-value function of the move history. Estimating the value of such games under optimal play, and computing near-optimal strategies, is an important goal in the study of decision-making under uncertainty, and has seen significant research in AI and allied areas [2], with only experimental evaluation of most algorithms' performance. The problem's PSPACE-completeness does not rule out nontrivial algorithms. Improved algorithms with theoretical guarantees are known in various cases where the payoff function F has special structure, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [3] give a sampling-based improved algorithm for general F, for turn-orders which restrict the number of non-random player strategies. We study the case of general F for which the players strictly alternate with binary moves-for which the approach of [3] does not improve over brute force. We give a randomized algorithm to approximate the value of such games under optimal play, and to execute near-optimal strategies. Our algorithm, while exponential-time, achieves exponential savings over brute-force, and certifies a lower bound on the game value with exponentially small additive expected error. (On the downside, the constant of improvement in the base of the runtime exponent is tiny, and the algorithm uses exponential space.) Our algorithm is recursive, and bootstraps a \"base case\" algorithm for fixed-size inputs. The method of recursive composition used, the specific base-case guarantees needed, and the steps to establish these guarantees are interesting and, we feel, likely to find uses beyond the present work.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00104"}, {"primary_key": "2584257", "vector": [], "sparse_vector": [], "title": "Adjacency Labelling for Planar Graphs (and Beyond).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that there exists an adjacency labelling scheme for planar graphs where each vertex of an n-vertex planar graph G is assigned a (1+o(1))log 2 n-bit label and the labels of two vertices u and v are sufficient to determine if uv is an edge of G. This is optimal up to the lower order term and is the first such asymptotically optimal result. An alternative, but equivalent, interpretation of this result is that, for every positive integer n, there exists a graph Un with n 1+o(1) vertices such that every n-vertex planar graph is an induced subgraph of Un. These results generalize to a number of other graph classes, including bounded genus graphs, apex-minor-free graphs, bounded-degree graphs from minor closed families, and k-planar graphs.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00060"}, {"primary_key": "2584258", "vector": [], "sparse_vector": [], "title": "An O(log log m) Prophet Inequality for Subadditive Combinatorial Auctions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Prophet inequalities compare the expected performance of an online algorithm for a stochastic optimization problem to the expected optimal solution in hindsight. They are a major alternative to classic worst-case competitive analysis, of particular importance in the design and analysis of simple (posted-price) incentive compatible mechanisms with provable approximation guarantees. A central open problem in this area concerns subadditive combinatorial auctions. Here n agents with subadditive valuation functions compete for the assignment of m items. The goal is to find an allocation of the items that maximizes the total value of the assignment. The question is whether there exists a prophet inequality for this problem that significantly beats the best known approximation factor of O(log m). We make major progress on this question by providing an O(log log m) prophet inequality. Our proof goes through a novel primal-dual approach. It is also constructive, resulting in an online policy that takes the form of static and anonymous item prices that can be computed in polynomial time given appropriate query access to the valuations. As an application of our approach, we construct a simple and incentive compatible mechanism based on posted prices that achieves an O(log log m) approximation to the optimal revenue for subadditive valuations under an item-independence assumption.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00037"}, {"primary_key": "2584259", "vector": [], "sparse_vector": [], "title": "Binary Interactive Error Resilience Beyond ${{}{1}}\\!/\\!_{8}$ (or why $({{}{1}}\\!/\\!_{2}){3} &gt; {{}{1}}\\!/\\!_{8})$.", "authors": ["<PERSON><PERSON>", "Gillat Kol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Interactive error correcting codesInteractive error correcting codes are codes that encode a two party communication protocol to an error-resilient protocol that succeeds even if a constant fraction of the communicated symbols are adversarially corrupted, at the cost of increasing the communication by a constant factor. What is the largest fraction of corruptions that such codes can protect against? If the error-resilient protocol is allowed to communicate large (constant sized) symbols, <PERSON><PERSON> and <PERSON> (STOC, 2011) show that the maximum rate of corruptions that can be tolerated is 1 /4. They also give a binary interactive error correcting protocol that only communicates bits and is resilient to 1 /2 fraction of errors, but leave the optimality of this scheme as an open problem. We answer this question in the negative, breaking the 1 /8 barrier. Specifically, we give a binary interactive error correcting scheme that is resilient to 5 /39 > 1 /8 fraction of adversarial errors. Our scheme builds upon a novel construction of binary list-decodable interactive codes with small list size.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00051"}, {"primary_key": "2584260", "vector": [], "sparse_vector": [], "title": "Decodable quantum LDPC codes beyond the square root distance barrier using high dimensional expanders.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Constructing quantum LDPC codes with a minimum distance that grows faster than a square root of the length has been a major challenge of the field. With this challenge in mind, we investigate constructions that come from high-dimensional expanders, in particular Ramanujan complexes. These naturally give rise to very unbalanced quantum error correcting codes that have a large X-distance but a much smaller Z-distance. However, together with a classical expander LDPC code and a tensoring method that generalises a construction of Hastings and also the <PERSON><PERSON><PERSON> construction of quantum codes, we obtain quantum LDPC codes whose minimum distance exceeds the square root of the code length and whose dimension comes close to a square root of the code length. When the ingredient is a 3-dimensional Ramanujan complex, we show that its 2-systole behaves like a square of the log of the complex size, which results in an overall quantum code of minimum distance n 1/2 logn, and sets a new record for quantum LDPC codes. When we use a 2-dimensional Ramanujan complex, or the 2-skeleton of a 3-dimensional Ramanujan complex, we obtain a quantum LDPC code of minimum distance n 1/2 log 1/2 n. We then exploit the expansion properties of the complex to devise the first polynomial time algorithm that decodes above the square root barrier for quantum LDPC codes.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00029"}, {"primary_key": "2584261", "vector": [], "sparse_vector": [], "title": "Edge-Weighted Online Bipartite Matching.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Runzhou Tao", "<PERSON><PERSON><PERSON>"], "summary": "Online bipartite matching is one of the most fundamental problems in the online algorithms literature. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (STOC 1990) introduced an elegant algorithm for the unweighted bipartite matching that achieves an optimal competitive ratio of 1- 1 /e. <PERSON> et al. (SODA 2011) later generalized their algorithm and analysis to the vertex-weighted case. Little is known, however, about the most general edge-weighted problem aside from the trivial 1/ 2 -competitive greedy algorithm. In this paper, we present the first online algorithm that breaks the long-standing 1/ 2 barrier and achieves a competitive ratio of at least 0.5086. In light of the hardness result of <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (SODA 2013) that restricts beating a 1/ 2 competitive ratio for the more general problem of monotone submodular welfare maximization, our result can be seen as strong evidence that edge-weighted bipartite matching is strictly easier than submodular welfare maximization in the online setting. The main ingredient in our online matching algorithm is a novel subroutine called online correlated selection (OCS), which takes a sequence of pairs of vertices as input and selects one vertex from each pair. Instead of using a fresh random bit to choose a vertex from each pair, the OCS negatively correlates decisions across different pairs and provides a quantitative measure on the level of correlation. We believe our OCS technique is of independent interest and will find further applications in other online optimization problems.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00046"}, {"primary_key": "2584262", "vector": [], "sparse_vector": [], "title": "The complexity of approximating averages on bounded-degree graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove that, unless P=NP, there is no polynomial-time algorithm to approximate within some multiplicative constant the average size of an independent set in graphs of maximum degree 6. This is a special case of a more general result for the hard-core model defined on independent sets weighted by a parameter . In the general setting, we prove that, unless P=NP, for all Δ ≥ 3, all , there is no FPTAS which applies to all graphs of maximum degree Δ for computing the average size of the independent set in the Gibbs distribution, where λ c (Δ) is the critical point for the uniqueness/non-uniqueness phase transition on the Δ-regular tree. Moreover, we prove that for λ in a dense set of this non-uniqueness region the problem is NP-hard to approximate within some constant factor. Our work extends to the antiferromagnetic Ising model and generalizes to all 2-spin antiferromagnetic models, establishing hardness of computing the average magnetization in the tree non-uniqueness region. Previously, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (2015) showed that it is #P-hard to compute the average magnetization exactly, but no hardness of approximation results were known. Hardness results of <PERSON><PERSON> (2010) and <PERSON><PERSON> and <PERSON> (2014) for approximating the partition function do not imply hardness of computing averages. The new ingredient in our reduction is an intricate construction of pairs of rooted trees whose marginal distributions at the root agree but their derivatives disagree. The main technical contribution is controlling what marginal distributions and derivatives are achievable and using <PERSON><PERSON><PERSON>'s functional equation to argue existence of the gadgets. The full version of this paper with detailed proofs to all lemmas and theorems can be found out at https://arxiv.org/abs/2004.09238.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00127"}, {"primary_key": "2584263", "vector": [], "sparse_vector": [], "title": "Low-Degree Hardness of Random Optimization Problems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of finding nearly optimal solutions of optimization problems with random objective functions. Such problems arise widely in the theory of random graphs, theoretical computer science, and statistical physics. Two concrete problems we consider are (a) optimizing the Hamiltonian of a spherical or Ising p-spin glass model, and (b) finding a large independent set in a sparse Erdo<PERSON>-<PERSON> graph. Two families of algorithms are considered: (a) low-degree polynomials of the input-a general framework that captures methods such as approximate message passing and local algorithms on sparse graphs, among others; and (b) the Langevin dynamics algorithm, a canonical Monte Carlo analogue of the gradient descent algorithm (applicable only for the spherical p-spin glass Hamiltonian). We show that neither family of algorithms can produce nearly optimal solutions with high probability. Our proof uses the fact that both models are known to exhibit a variant of the overlap gap property (OGP) of near-optimal solutions. Specifically, for both models, every two solutions whose objective values are above a certain threshold are either close or far from each other. The crux of our proof is the stability of both algorithms: a small perturbation of the input induces a small perturbation of the output. By an interpolation argument, such a stable algorithm cannot overcome the OGP barrier. The stability of the Langevin dynamics is an immediate consequence of the well-posedness of stochastic differential equations. The stability of low-degree polynomials is established using concepts from Gaussian and Boolean Fourier analysis, including noise sensitivity, hypercontractivity, and total influence.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00021"}, {"primary_key": "2584264", "vector": [], "sparse_vector": [], "title": "Learning sums of powers of low-degree polynomials in the non-degenerate case.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We develop algorithms for writing a polynomial as sums of powers of low degree polynomials in the non-degenerate case. This problem generalizes symmetric tensor decomposition which is widely studied, having many applications in machine learning. Our algorithm for this more general problem allows us to solve the moment problem for mixtures of zero-mean Gaussians in the nondegenerate case. Our algorithm is based on a scheme for obtaining a learning algorithm for an arithmetic circuit model from lower bound for the same model, provided certain non-degeneracy conditions hold. The scheme reduces the learning problem to the problem of decomposing two vector spaces under the action of a set of linear operators, where the spaces and the operators are derived from the input circuit and the complexity measure used in a typical lower bound proof. The non-degeneracy conditions are certain restrictions on how the spaces decompose. Such a scheme is present in a rudimentary form in an earlier work of <PERSON><PERSON> and <PERSON><PERSON>. Here, we make it more general and detailed, and potentially applicable to learning other circuit models. An exponential lower bound on the representation above is known using the shifted partials measure. However, the number of linear operators in shifted partials is exponential and also the non-degeneracy condition emerging out of this measure is unlikely to be satisfied by a random such circuit when the number of variables is large with respect to the degree. We bypass this hurdle by proving a lower bound (which is nearly as strong as the previous bound) using a novel variant of the partial derivatives measure, namely affine projections of partials (APP). The non-degeneracy conditions appearing from this new measure are satisfied by a random circuit of the above kind. The APP measure could be of independent interest for proving other lower bounds.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00087"}, {"primary_key": "2584265", "vector": [], "sparse_vector": [], "title": "Independent Set on $\\mathrm{P}_{k}$-Free Graphs in Quasi-Polynomial Time.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present an algorithm that takes as input a graph G with weights on the vertices, and computes a maximum weight independent set S of G. If the input graph G excludes a path P k on k vertices as an induced subgraph, the algorithm runs in time n O(k2 log 3 n). Hence, for every fixed k our algorithm runs in quasi-polynomial time. This resolves in the affirmative an open problem of [<PERSON><PERSON><PERSON>, SODA'20 invited presentation]. Previous to this work, polynomial time algorithms were only known for P 4 -free graphs [<PERSON><PERSON><PERSON> et al., DAM'81], P 5 -free graphs [<PERSON><PERSON><PERSON><PERSON> et al., SODA'14], and P 6 -free graphs [<PERSON><PERSON><PERSON><PERSON> et al., SODA'19]. For larger values of t, only 2 O(√{knlogn}) time algorithms [<PERSON><PERSON><PERSON> et al., Algorithmica'19] and quasipolynomial time approximation schemes [<PERSON><PERSON><PERSON> et al., SODA'20] were known. Thus, our work is the first to offer conclusive evidence that Independent Set on P k - free graphs is not NP-complete for any integer k. Additionally we show that for every graph H, if there exists a quasi-polynomial time algorithm for Independent Seton C-free graphs for every connected component C of H, then there also exists a quasi-polynomial time algorithm for Independent Set on H-free graphs. This lifts our quasi-polynomial time algorithm to T k -free graphs, where T k has one component that is a P k , and k-1 components isomorphic to a fork (the unique 5-vertex tree with a degree 3 vertex).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00063"}, {"primary_key": "2584266", "vector": [], "sparse_vector": [], "title": "Sum-of-Squares Lower Bounds for Sherrington-Kirkpatrick via Planted Affine Planes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Sum-of-Squares (SoS) hierarchy is a semi-definite programming meta-algorithm that captures state-of-the-art polynomial time guarantees for many optimization problems such as Max- k-CSPs and Tensor PCA. On the flip side, a SoS lower bound provides evidence of hardness, which is particularly relevant to average-case problems for which NP-hardness may not be available. In this paper, we consider the following average case problem, which we call the Planted Affine Planes (PAP) problem: Given m random vectors d 1 , ..., d m in R n , can we prove that there is no vector v ∈ IR n such that for all u ∈ [m], 〈v, du〉 2 = 1? In other words, can we prove that m random vectors are not all contained in two parallel hyperplanes at equal distance from the origin? We prove that for m ≤ n 3/ 2-ε , with high probability, degree- n Ω(ε) SoS fails to refute the existence of such a vector v. When the vectors d 1 , ..., d m are chosen from the multivariate normal distribution, the PAP problem is equivalent to the problem of proving that a random n-dimensional subspace of R m does not contain a boolean vector. As shown by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [STOC 2020], a lower bound for this problem implies a lower bound for the problem of certifying energy upper bounds on the <PERSON><PERSON><PERSON>-<PERSON> Hamiltonian, and so our lower bound implies a degree- n Ω(ε) SoS lower bound for the certification version of the <PERSON><PERSON>ton-<PERSON>patrick problem.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00093"}, {"primary_key": "2584267", "vector": [], "sparse_vector": [], "title": "Resolving the Optimal Metric Distortion Conjecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the following metric distortion problem: there are two finite sets of points, V and C, that lie in the same metric space, and our goal is to choose a point in C whose total distance from the points in V is as small as possible. However, rather than having access to the underlying distance metric, we only know, for each point in V, a ranking of its distances to the points in C. We propose algorithms that choose a point in C using only these rankings as input and we provide bounds on their distortion (worst-case approximation ratio). A prominent motivation for this problem comes from voting theory, where V represents a set of voters, C represents a set of candidates, and the rankings correspond to ordinal preferences of the voters. A major conjecture in this framework is that the optimal deterministic algorithm has distortion 3. We resolve this conjecture by providing a polynomial-time algorithm that achieves distortion 3, matching a known lower bound. We do so by proving a novel lemma about matching voters to candidates, which we refer to as the ranking-matching lemma. This lemma induces a family of novel algorithms, which may be of independent interest, and we show that a special algorithm in this family achieves distortion 3. We also provide more refined, parameterized, bounds using the notion of decisiveness, which quantifies the extent to which a voter may prefer her top choice relative to all others. Finally, we introduce a new randomized algorithm with improved distortion compared to known results, and also provide improved lower bounds on the distortion of all deterministic and randomized algorithms.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00134"}, {"primary_key": "2584268", "vector": [], "sparse_vector": [], "title": "Polynomial Data Structure Lower Bounds in the Group Model.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Proving super-logarithmic data structure lower bounds in the static group model has been a fundamental challenge in computational geometry since the early 80's. We prove a polynomial (n Ω(1) ) lower bound for an explicit range counting problem of n 3 convex polygons in \\mathbbR 2 (each with n Õ̃(1) facets/semialgebraic-complexity), against linear storage arithmetic data structures in the group model. Our construction and analysis are based on a combination of techniques in Diophantine approximation, pseudorandomness, and compressed sensing-in particular, on the existence and partial derandomization of optimal binary compressed sensing matrices in the polynomial sparsity regime (k=n 1-δ ). As a byproduct, this establishes a (logarithmic) separation between compressed sensing matrices and the stronger RIP property.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00074"}, {"primary_key": "2584269", "vector": [], "sparse_vector": [], "title": "Isomorphism Testing for Graphs Excluding Small Minors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove that there is a graph isomorphism test running in time n polylog(h) on n-vertex graphs excluding some h-vertex graph as a minor. Previously known bounds were n poly(h) (<PERSON><PERSON><PERSON><PERSON>, 1988) and n polylog(n) (<PERSON><PERSON>, STOC 2016). For the algorithm we combine recent advances in the group-theoretic graph isomorphism machinery with new graph-theoretic arguments.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00064"}, {"primary_key": "2584270", "vector": [], "sparse_vector": [], "title": "Fully-Dynamic Submodular Cover with Bounded Recourse.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In submodular covering problems, we are given a monotone, nonnegative submodular function f:2 N → R + and wish to find the min-cost set S ⊆ N such that f(S)=f(N). When f is a coverage function, this captures Setcover as a special case. We introduce a general framework for solving such problems in a fully-dynamic setting where the function f changes over time, and only a bounded number of updates to the solution (a.k.a. recourse) is allowed. For concreteness, suppose a nonnegative monotone submodular integer-valued function gt is added or removed from an active set G (t) at each time t. If f (t) =Σ(g∈G (t) g) is the sum of all active functions, we wish to maintain a competitive solution to Submodularcover for f (t) as this active set changes, and with low recourse. For example, if each gt is the (weighted) rank function of a matroid, we would be dynamically maintaining a low-cost common spanning set for a changing collection of matroids. We give an algorithm that maintains an O(log(f max /f min )) - competitive solution, where f max , f min are the largest/smallest marginals of f (t) . The algorithm guarantees a total recourse of O(log(c max /c min )·Σ t≤Tgt (N)), where c max , c min are the largest/smallest costs of elements in N. This competitive ratio is best possible even in the offline setting, and the recourse bound is optimal up to the logarithmic factor. For monotone sub-modular functions that also have positive mixed third derivatives, we show an optimal recourse bound of O(Σ t≤Tgt (N)). This structured class includes set-coverage functions, so our algorithm matches the known O(log n)-competitiveness and O(1) recourse guarantees for fully-dynamic Setcover. Our work simultaneously simplifies and unifies previous results, as well as generalizes to a significantly larger class of covering problems. Our key technique is a new potential function inspired by Tsallis entropy. We also extensively use the idea of Mutual Coverage, which generalizes the classic notion of mutual information.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00110"}, {"primary_key": "2584271", "vector": [], "sparse_vector": [], "title": "Network Coding Gaps for Completion Times of Multiple Unicasts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study network coding gaps for the problem of makespan minimization of multiple unicasts. In this problem distinct packets at different nodes in a network need to be delivered to a destination specific to each packet, as fast as possible. The network coding gap specifies how much coding packets together in a network can help compared to the more natural approach of routing. While makespan minimization using routing has been intensely studied for the multiple unicasts problem, no bounds on network coding gaps for this problem are known. We develop new techniques which allow us to upper bound the network coding gap for the makespan of k unicasts, proving this gap is at most polylogarithmic in k. Complementing this result, we show there exist instances of k unicasts for which this coding gap is polylogarithmic in k. Our results also hold for average completion time, and more generally any lp norm of completion times.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00053"}, {"primary_key": "2584272", "vector": [], "sparse_vector": [], "title": "A Tight Lower Bound on Adaptively Secure Full-Information Coin Flip.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In a distributed coin-flipping protocol, <PERSON><PERSON> [ACM Transactions on Computer Systems '83], the parties try to output a common (close to) uniform bit, even when some adversarially chosen parties try to bias the common output. In an adaptively secure full-information coin flip, <PERSON><PERSON><PERSON> and <PERSON><PERSON> [FOCS '85], the parties communicate over a broadcast channel and a computationally unbounded adversary can choose which parties to corrupt along the protocol execution. <PERSON><PERSON><PERSON> and <PERSON><PERSON> proved that the n-party majority protocol is resilient to O(√n) corruptions (ignoring poly-logarithmic factors), and conjectured this is a tight upper bound for any n-party protocol (of any round complexity). Their conjecture was proved to be correct for single-turn (each party sends a single message) single-bit (a message is one bit) protocols Lichtenstein, Linial, and Saks [Combinatorica '89], symmetric protocols <PERSON><PERSON>, <PERSON><PERSON>, and Park [ICALP '15], and recently for (arbitrary message length) single-turn protocols <PERSON><PERSON>, <PERSON>, and Raz [DISC '18]. Yet, the question for many-turn protocols was left completely open. In this work we close the above gap, proving that no n-party protocol (of any round complexity) is resilient to ω(√n) (adaptive) corruptions.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00120"}, {"primary_key": "2584273", "vector": [], "sparse_vector": [], "title": "Benchmark Design and Prior-independent Optimization.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Yingkai Li"], "summary": "This paper compares two leading approaches for robust optimization in the models of online algorithms and mechanism design. Competitive analysis compares the performance of an online algorithm to an offline benchmark in worst-case over inputs, and prior-independent mechanism design compares the expected performance of a mechanism on an unknown distribution (of inputs, i.e., agent values) to the optimal mechanism for the distribution in worst case over distributions. For competitive analysis, a critical concern is the choice of benchmark. This paper gives a method for selecting a good benchmark. We show that optimal algorithm/mechanism for the optimal benchmark is equal to the prior-independent optimal algorithm/mechanism. We solve a central open question in prior-independent mechanism design, namely we identify the prior-independent revenue-optimal mechanism for selling a single item to two agents with i.i.d. and regularly distributed values. We use this solution to solve the corresponding benchmark design problem. Via this solution and the above equivalence of prior-independent mechanism design and competitive analysis (a.k.a. prior-free mechanism design) we show that the standard method for lower bounds of prior-free mechanisms is not generally tight for the benchmark design program.11For the full version of this work, see https://arxiv.org/abs/2001.10157.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00036"}, {"primary_key": "2584274", "vector": [], "sparse_vector": [], "title": "Optimal anytime regret for two experts.", "authors": ["<PERSON> J<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The multiplicative weights method is an algorithm for the problem of prediction with expert advice. It achieves the optimal regret asymptotically if the number of experts is large, and the time horizon is known in advance. Optimal algorithms are also known if there are exactly two, three or four experts, and the time horizon is known in advance. In the anytime setting, where the time horizon is not known in advance, algorithms can be obtained by the \"doubling trick\", but they are not optimal, let alone practical. No minimax optimal algorithm was previously known in the anytime setting, regardless of the number of experts. We design the first minimax optimal algorithm for minimizing regret in the anytime setting. We consider the case of two experts, and prove that the optimal regret γ√t/2 is at all time steps t, where γ is a natural constant that arose 35 years ago in studying fundamental properties of Brownian motion. The algorithm is designed by considering a continuous analogue of the regret problem, which is solved using ideas from stochastic calculus. This is the extended abstract of the paper. The full paper can be found in [arXiv:2002.08994].", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00132"}, {"primary_key": "2584275", "vector": [], "sparse_vector": [], "title": "Characterizing Average-Case Complexity of PH by Worst-Case Meta-Complexity.", "authors": ["<PERSON><PERSON>"], "summary": "We exactly characterize the average-case complexity of the polynomial-time hierarchy (PH) by the worst-case (meta-)complexity of GapMINKT PH , i.e., an approximation version of the problem of determining if a given string can be compressed to a short PH-oracle efficient program. Specifically, we establish the following equivalence: DistPH ⊆ AvgP ( i.e., PH is easy on average) ⇐⇒ GapMINKT PH ∈ P. In fact, our equivalence is significantly broad: A number of statements on several fundamental notions of complexity theory, such as errorless and one-sided-error average-case complexity, sublinear-time-bounded and polynomial-time-bounded Kolmogorov complexity, and PH-computable hitting set generators, are all shown to be equivalent. Our equivalence provides fundamentally new proof techniques for analyzing average-case complexity through the lens of meta-complexity of time-bounded Kolmogorov complexity and resolves, as immediate corollaries, questions of equivalence among different notions of average-case complexity of PH: low success versus high success probabilities (i.e., a hardness amplification theorem for DistPH against uniform algorithms) and errorless versus one-sided-error average-case complexity of PH. Our results are based on a sequence of new technical results that further develops the proof techniques of the author's previous work on the non-black-box worst-case to average-case reduction and unexpected hardness results for Kolmogorov complexity (FOCS'18, CCC'20, ITCS'20, STOC'20). Among other things, we prove the following. 1) GapMINKT NP ∈ P implies P = BPP. At the core of the proof is a new black-box hitting set generator construction whose reconstruction algorithm uses few random bits, which also improves the approximation quality of the nonblack-box worst-case to average-case reduction without using a pseudorandom generator. 2) GapMINKT PH ∈ P implies DistPH ⊆ AvgBPP = AvgP. 3) If MINKT PH ∈ P is easy on a 1/poly(n)-fraction of inputs, then GapMINKT PH ∈ P. This improves the error tolerance of the previous non-black-box worst-case to average-case reduction. The full version of the paper is available on ECCC.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00014"}, {"primary_key": "2584276", "vector": [], "sparse_vector": [], "title": "Point Location and Active Learning: Learning Halfspaces Almost Optimally.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Given a finite set X ⊂ R d and a binary linear classifier c: R d → {0,1}, how many queries of the form c(x) are required to learn the label of every point in X? Known as point location, this problem has inspired over 35 years of research in the pursuit of an optimal algorithm. Building on the prior work of <PERSON>, <PERSON>, and <PERSON> (ICALP 2018), we provide the first nearly optimal solution, a randomized linear decision tree of depth Õ(dlog(|X|)), improving on the previous best of Õ(d 2 log(|X|)) from <PERSON> and <PERSON> (Discrete and Computational Geometry, 2019). As a corollary, we also provide the first nearly optimal algorithm for actively learning halfspaces in the membership query model. En route to these results, building on the work of <PERSON><PERSON>, <PERSON>, and <PERSON> (J. Geometric Analysis 2004), as well as <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (STOC 2014), we prove a novel characterization of <PERSON><PERSON>'s <PERSON>rem (Inventiones Mathematicae, 1998) of independent interest. In particular, we show that X may be transformed into approximate isotropic position if and only if there exists no k-dimensional subspace with more than a k/d-fraction of X, and provide a similar characterization for exact isotropic position. The below is an extended abstract. The full work can be found at https://arxiv.org/abs/2004.11380.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00100"}, {"primary_key": "2584277", "vector": [], "sparse_vector": [], "title": "Subexponential LPs Approximate Max-Cut.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that for every ε > 0, the degree-n ε Sherali-Adams linear program (with exp(Õ(n ε )) variables and constraints) approximates the maximum cut problem within a factor of ([1/2]+ε ' ), for some ε ' (ε)>0. Our result provides a surprising converse to known lower bounds against all linear programming relaxations of Max-Cut [1], [2], and hence resolves the extension complexity of approximate Max-Cut for approximation factors close to [1/2] (up to the function ε ' (ε)). Previously, only semidefinite programs and spectral methods were known to yield approximation factors better than [1/2] for Max-Cut in time 2 o(n) . We also show that constant-degree Sherali-Adams linear programs (with poly(n) variables and constraints) can solve Max-Cut with approximation factor close to 1 on graphs of small threshold rank: this is the first connection of which we are aware between threshold rank and linear programming-based algorithms. Our results separate the power of Sherali-Adams versus Lovász-Schrijver hierarchies for approximating Max-Cut, since it is known [3] that ([1/2]+ε) approximation of Max Cut requires Ω ε (n) rounds in the Lovász-Schrijver hierarchy. We also provide a subexponential time approximation for <PERSON><PERSON>'s Unique Games problem [4]: we show that for every ε>0 the degree-(n ε log q) Sherali-Adams linear program distinguishes instances of Unique Games of value ≥ 1-ε ' from instances of value ≤ ε ' , for some ε ' (ε)>0, where q is the alphabet size. Such guarantees are qualitatively similar to those of previous subexponential-time algorithms for Unique Games but our algorithm does not rely on semidefinite programming or subspace enumeration techniques [5]-[6]-[7].", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00092"}, {"primary_key": "2584278", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms for Stochastic Minimum-Norm Combinatorial Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by the need for, and growing interest in, modeling uncertainty in data, we introduce and study stochastic minimum-norm optimization. We have an underlying combinatorial optimization problem where the costs involved are random variables with given distributions; each feasible solution induces a random multidimensional cost vector, and given a certain objective function, the goal is to find a solution (that does not depend on the realizations of the costs) that minimizes the expected objective value. For instance, in stochastic load balancing, jobs with random processing times need to be assigned to machines, and the induced cost vector is the machine-load vector. The choice of objective is typically the maximum- or sum-of the entries of the cost vector, or in some cases some other lp norm of the cost vector. Recently, in the deterministic setting, <PERSON><PERSON><PERSON><PERSON> and <PERSON> [7] considered a much broader suite of objectives, wherein we seek to minimize the f-norm of the cost vector under a given arbitrary monotone, symmetric norm f. In stochastic minimum-norm optimization, we work with this broad class of objectives, and seek a solution that minimizes the expected f-norm of the induced cost vector. The class of monotone, symmetric norms is versatile and includes lp-norms, and Topl-norms (sum of l largest coordinates in absolute value), and enjoys various closure properties; in particular, it can be used to incorporate multiple norm budget constraints, fl(x) ≤ B l , l = 1, ..., k. We give a general framework for devising algorithms for stochastic minimum-norm combinatorial optimization, using which we obtain approximation algorithms for the stochastic minimum-norm versions of the load balancing and spanning tree problems. We obtain the following concrete results. (a) An O(1)-approximation for stochastic minimum-norm load balancing on unrelated machines with: (i) arbitrary monotone symmetric norms and job sizes that are Bernoulli random variables; and (ii) Topl norms and arbitrary job-size distributions. (b) An O(log m/log log m)-approximation for the general stochastic minimum-norm load balancing problem, where m is the number of machines. (c) An O(1)-approximation for stochastic minimum-norm spanning tree with arbitrary monotone symmetric norms and arbitrary edge-weight distributions; this guarantee extends to the stochastic minimum-norm matroid basis problem. Two key technical contributions of this work are: (1) a structural result of independent interest connecting stochastic minimum-norm optimization to the simultaneous optimization of a (small) collection of expected Top l -norms; and (2) showing how to tackle expected Top l -norm minimization by leveraging techniques used to deal with minimizing the expected maximum, circumventing the difficulties posed by the non-separable nature of Top l norms.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00094"}, {"primary_key": "2584279", "vector": [], "sparse_vector": [], "title": "Constant Depth Formula and Partial Function Versions of MCSP are Hard.", "authors": ["<PERSON><PERSON>"], "summary": "Attempts to prove the intractability of the Minimum Circuit Size Problem (MCSP) date as far back as the 1950s and are well-motivated by connections to cryptography, learning theory, and average-case complexity. In this work, we make progress, on two fronts, towards showing MCSP is intractable under worst-case assumptions. While <PERSON><PERSON><PERSON> showed in the late 1970s that the version of MCSP for DNF formulas is NP-hard, extending this result to the case of depth-3 AND/OR formulas was open. We show that determining the minimum size of a depth- d formula computing a given Boolean function is N P-hard under quasipolynomial-time randomized reductions for all constant d ≥ 2. Our approach is based on a method to \"lift\" depth- d formula lower bounds to depth-( d+1). This method also implies the existence of a function with a 2 Ωd(n1/5 ) additive gap between its depth-d and depth-( d+1) formula complexity. We also make progress in the case of general, unrestricted circuits. We show that the version of MCSP where the input is a partial function (represented by a string in {0,1, ?}*) is not in P under the Exponential Time Hypothesis (ETH). Intriguingly, we formulate a notion of lower bound statements being (P/poly)-recognizable that is closely related to <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s definition of being (P/poly)-constructive. We show that unless there are subexponential-sized circuits computing SAT, the lower bound statements used to prove the correctness of our reductions cannot be (P/poly)-recognizable.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00047"}, {"primary_key": "2584280", "vector": [], "sparse_vector": [], "title": "Unique Decoding of Explicit $\\varepsilon$-balanced Codes Near the Gilbert-Varshamov Bound.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bound (non-constructively) establishes the existence of binary codes of distance 1/2-ε and rate Ω(ε 2 ) (where an upper bound of O(ε 2 log(1/ε)) is known). <PERSON><PERSON><PERSON><PERSON><PERSON> [STOC 2017] gave an explicit construction of ε-balanced binary codes, where any two distinct codewords are at a distance between 1/2-ε/2 and 1/2+ε/2, achieving a near optimal rate of Ω(ε 2+β ), where β→ 0 as ε→ 0. We develop unique and list decoding algorithms for (a slight modification of) the family of codes constructed by Ta-Shm<PERSON>, in the adversarial error model. We prove the following results for ε-balanced codes with block length N and rate Ω(ε 2+β ) in this family: -For all , there are explicit codes which can be uniquely decoded up to an error of half the minimum distance in time N Oε,β(1) . -For any fixed constant β independent of ε, there is an explicit construction of codes which can be uniquely decoded up to an error of half the minimum distance in time (log(1/ε)) O(1) ·N Oβ(1) . -For any , there are explicit ε-balanced codes with rate Ω(ε 2+β ) which can be list decoded up to error 1/2-ε ' in time N Oε,ε' ,β(1), where ε ' ,β→ 0 as ε→ 0. The starting point of our algorithms is the framework for list decoding direct-sum codes develop in <PERSON><PERSON> et al. [SODA 2020], which uses the Sum-of-Squares SDP hierarchy. The rates obtained there were quasipolynomial in ε. Here, we show how to overcome the far from optimal rates of this framework obtaining unique decoding algorithms for explicit binary codes of near optimal rate. These codes are based on simple modifications of Ta-Shma's construction.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00048"}, {"primary_key": "2584281", "vector": [], "sparse_vector": [], "title": "A Faster Interior Point Method for Semidefinite Programming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Semidefinite programs (SDPs) are a fundamental class of optimization problems with important recent applications in approximation algorithms, quantum complexity, robust learning, algorithmic rounding, and adversarial deep learning. This paper presents a faster interior point method to solve generic SDPs with variable size n ×n and m constraints in time Õ(√n(mn 2 +m ω +n ω )log(1/ε)), \\end{equation*} where ω is the exponent of matrix multiplication and ε is the relative accuracy. In the predominant case of m ≥ n, our runtime outperforms that of the previous fastest SDP solver, which is based on the cutting plane method [JLSW20]. Our algorithm's runtime can be naturally interpreted as follows: O(√nlog(1/ε)) is the number of iterations needed for our interior point method, mn 2 is the input size, and m ω +n ω is the time to invert the Hessian and slack matrix in each iteration. These constitute natural barriers to further improving the runtime of interior point methods for solving generic SDPs.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00089"}, {"primary_key": "2584282", "vector": [], "sparse_vector": [], "title": "Collaborative Top Distribution Identifications with Limited Interaction (Extended Abstract).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the following problem in this paper: given a set of n distributions, find the top- m ones with the largest means. This problem is also called top- m arm identifications in the literature of reinforcement learning, and has numerous applications. We study the problem in the collaborative learning model where we have multiple agents who can draw samples from the n distributions in parallel. Our goal is to characterize the tradeoffs between the running time of learning process and the number of rounds of interaction between agents, which is very expensive in various scenarios. We give optimal time-round tradeoffs, as well as demonstrate complexity separations between top-1 arm identification and top- m arm identifications for general m and between fixed-time and fixed-confidence variants. As a byproduct, we also give an algorithm for selecting the distribution with the m-th largest mean in the collaborative learning model.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00024"}, {"primary_key": "2584283", "vector": [], "sparse_vector": [], "title": "Unit Capacity Maxflow in Almost $O(m{4/3})$ Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an algorithm, which given any m-edge n-vertex directed graph with positive integer capacities at most U computes a maximum s-t flow for any vertices s and t in O(m 4/3+o(1) U 1/3 ) time. This improves upon the previous best running times of O(m 11/8+o(1) U 1/4 ) [1], Õ(m√nlogU) [2] and O(mn) [3] when the graph is not too dense and doesn't have large capacities. We build upon advances for sparse maxflow based on interior point methods [1], [4], [5]. Whereas these methods increase the energy of local ℓ 2 -norm minimizing electrical flows, we instead increase the Bregman divergence value of flows which minimize the Bregman divergence with respect to a weighted log barrier. This allows us to trace the central path with progress depending only on ℓ ∞ norm bounds on the congestion vector as opposed to the ℓ 4 norm, which arises in these prior works. Further, we show that smoothed ℓ 2 -ℓ p flows [6], [7] which were used to maximize energy [1] can also be used to efficiently maximize divergence, thereby yielding our desired runtimes. We believe our approach towards Bregman divergences of barriers may be of further interest.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00020"}, {"primary_key": "2584284", "vector": [], "sparse_vector": [], "title": "Towards a Proof of the Fourier-Entropy Conjecture?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The following topics are dealt with: computational complexity; approximation theory; polynomials; graph theory; set theory; matrix algebra; trees (mathematics); randomised algorithms; optimisation; data structures.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00032"}, {"primary_key": "2584285", "vector": [], "sparse_vector": [], "title": "Resolution of the Burrows-Wheeler Transform Conjecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Burrows-Wheeler Transform (BWT) is an invertible text transformation that permutes symbols of a text according to the lexicographical order of its suffixes. BWT is the main component of popular lossless compression programs (such as bzip2) as well as recent powerful compressed indexes (such as r-index [<PERSON><PERSON><PERSON> et al., J. <PERSON>, 2020]), central in modern bioinformatics. The compression ratio of BWT is quantified by the number r of equal-letter runs. Despite the practical significance of BWT, no non-trivial bound on the value of r is known. This is in contrast to nearly all other known compression methods, whose sizes have been shown to be either always within a polylogn factor (where n is the length of text) from z, the size of Lempel-Ziv (LZ77) parsing of the text, or significantly larger in the worst case (by a n ε factor for ). In this paper, we show that r=O(zlog 2 n) holds for every text. This result has numerous implications for text indexing and data compression; for example: (1) it proves that many results related to BWT automatically apply to methods based on LZ77, e.g., it is possible to obtain functionality of the suffix tree in O(zpolylog n) space; (2) it shows that many text processing tasks can be solved in the optimal time assuming the text is compressible using LZ77 by a sufficiently large polylogn factor; (3) it implies the first non-trivial relation between the number of runs in the BWT of the text and its reverse. In addition, we provide an O(z polylog n)-time algorithm converting the LZ77 parsing into the run-length compressed BWT. To achieve this, we develop a number of new data structures and techniques of independent interest. In particular, we introduce a notion of compressed string synchronizing sets (generalizing the recently introduced powerful technique of string synchronizing sets [STOC 2019]) and show how to efficiently construct them. Next, we propose a new variant of wavelet trees for sequences of long strings, establish a nontrivial bound on their size, and describe efficient construction algorithms. Finally, we describe new indexes that can be constructed directly from the LZ77-compressed text and efficiently support pattern matching queries on substrings of the text.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00097"}, {"primary_key": "2584286", "vector": [], "sparse_vector": [], "title": "Sublinear-Time Algorithms for Computing &amp; Embedding Gap Edit Distance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we design new sublinear-time algorithms for solving the gap edit distance problem and for embedding edit distance to Hamming distance. For the gap edit distance problem, we give a greedy algorithm that distinguishes in time ~O([n/k]+k 2 ) between length-n input strings with edit distance at most k and those with edit distance more than 4k 2 . This is an improvement and a simplification upon the main result of [<PERSON><PERSON>, <PERSON>, <PERSON>, FOCS 2019], where the k vs Θ(k 2 ) gap edit distance problem is solved in ~O([n/k]+k 3 ) time. We further generalize our result to solve the k vs αk gap edit distance problem in time ~O([n/(α)]+k 2 +[k/(α)]√{nk}), strictly improving upon the previously known bound ~O([n/(α)]+k 3 ). Finally, we show that if the input strings do not have long highly periodic substrings, then the gap edit distance problem can be solved in sublinear time within any factor . Specifically, if the strings contain no substring of length l with the shortest period of length at most 2k, then the k vs (1+ε)k gap edit distance problem can be solved in time ~O([n/(ε 2 k)]+k 2 l). We further give the first sublinear-time algorithm for the probabilistic embedding of edit distance to Hamming distance. Our ~O([n/p])-time procedure yields an embedding with distortion k 2 p, where k is the edit distance of the original strings. Specifically, the Hamming distance of the resultant strings is between [(k-p+1)/p] and k 2 with good probability. This generalizes the linear-time embedding of [Chakraborty, Goldenberg, Koucký, STOC 2016], where the resultant Hamming distance is between k and k 2 . Our algorithm is based on a random walk over samples, which we believe will find other applications in sublinear-time algorithms.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00112"}, {"primary_key": "2584287", "vector": [], "sparse_vector": [], "title": "Analysis of Two-variable Recurrence Relations with Application to Parameterized Approximations.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we introduce randomized branching as a tool for parameterized approximation and develop the mathematical machinery for its analysis. Our algorithms substantially improve the best known running times of parameterized approximation algorithms for Vertex Cover and 3-Hitting Set for a wide range of approximation ratios. The running times of our algorithms are derived from an asymptotic analysis of a broad class of two-variable recurrence relations. Our main theorem gives a simple formula for this asymptotics. The formula can be efficiently calculated by solving a simple numerical optimization problem, and provides the mathematical insight required for the algorithm design. To this end, we show an equivalence between the recurrence and a stochastic process. We analyze this process using the method of types, by introducing an adaptation of <PERSON><PERSON>'s theorem to our setting. We believe our novel analysis of recurrence relations which is of independent interest is a main contribution of this paper.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00076"}, {"primary_key": "2584288", "vector": [], "sparse_vector": [], "title": "An Adaptive Step Toward the Multiphase Conjecture.", "authors": ["Young Kun-Ko", "<PERSON><PERSON><PERSON>"], "summary": "In 2010, <PERSON><PERSON><PERSON><PERSON><PERSON> proposed a dynamic set-disjointness problem, known as the Multiphase problem, as a candidate for proving polynomial lower bounds on the operational time of dynamic data structures. He conjectured that any data structure for the Multiphase problem must make n ε cell-probes in either update or query phases, and showed that this would imply similar unconditional lower bounds on many important dynamic data structure problems. There has been almost no progress on this conjecture in the past decade since its introduction. We show an ~Ω(√n) cell-probe lower bound on the Multiphase problem for data structures with general (adaptive) updates, and queries with unbounded but \"layered\" adaptivity. This result captures all known set-intersection data structures and significantly strengthens previous Multiphase lower bounds, which only captured non-adaptive data structures. Our main technical result is a communication lower bound on a 4-party variant of <PERSON><PERSON><PERSON><PERSON><PERSON>'s Number-On-Forehead Multiphase game, using information complexity techniques. We then use this result to make progress on understanding the power of nonlinear gates in networks computing linear operators, a long-standing open problem in circuit complexity and network design: We show that any depth- d circuit that computes a random m×n linear operator x→ Ax using gates of degree k (width- k DNFs) must have Ω(m·n 1/2(d+k) ) wires. Finally, we show that a lower bound on <PERSON><PERSON><PERSON><PERSON><PERSON>'s original NOF game would imply a polynomial wire lower bound (n 1+Ω(1/d) ) for circuits with arbitrary gates computing a random linear operator. This suggests that the NOF conjecture is much stronger than its data structure counterpart.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00075"}, {"primary_key": "2584289", "vector": [], "sparse_vector": [], "title": "Tree-depth and the Formula Complexity of Subgraph Isomorphism.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "For a fixed \"pattern\" graph G, the colored G-subgraph isomorphism problem (denoted SUB(G)) asks, given an n-vertex graph H and a coloring V(H)→ V(G), whether H contains a properly colored copy of G. The complexity of this problem is tied to parameterized versions of P=? NP and L=? NL, among other questions. An overarching goal is to understand the complexity of SUB(G), under different computational models, in terms of natural invariants of the pattern graph G. In this paper, we establish a close relationship between the formula complexity of SUB(G) and an invariant known as tree-depth (denoted td ( G)). SUB(G) is known to be solvable by monotone AC 0 formulas of size O(n td(G) ). Our main result is an n ~Ω(td(G)1/3 ) lower bound for formulas that are monotone or have sub-logarithmic depth. This complements a lower bound of <PERSON>, <PERSON><PERSON><PERSON> and <PERSON> [8] relating tree-width and AC° circuit size. As a corollary, it implies a stronger homomorphism preservation theorem for first-order logic on finite structures [14]. The technical core of this result is an n Ω(k) lower bound in the special case where G is a complete binary tree of height k, which we establish using the pathset framework introduced in [15]. (The lower bound for general patterns follows via a recent excluded-minor characterization of tree-depth [4], [6].) Additional results of this paper extend the pathset framework and improve upon both, the best known upper and lower bounds on the average-case formula size of SUB(G) when G is a path.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00012"}, {"primary_key": "2584290", "vector": [], "sparse_vector": [], "title": "Deterministic Min-cut in Poly-logarithmic Max-flows.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We give a deterministic (global) min-cut algorithm for weighted undirected graphs that runs in time O(m 1+ε ) plus polylog ( n) max-flow computations. Using the current best max-flow algorithms, this results in an overall running time of ~O(m·min(√m, n 2/3 )) for weighted graphs, and m 4/3+o(1) for unweighted (multi)-graphs. This is the first improvement in the running time of deterministic algorithms for the min-cut problem on general (weighted/multi) graphs since the early 1990s when a running time bound of ~O(mn) was established for this problem.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00017"}, {"primary_key": "2584291", "vector": [], "sparse_vector": [], "title": "On One-way Functions and Kolmogorov Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We prove that the equivalence of two fundamental problems in the theory of computing. For every polynomial , the following are equivalent: · One-way functions exists (which in turn is equivalent to the existence of secure private-key encryption schemes, digital signatures, pseudorandom generators, pseudorandom functions, commitment schemes, and more); · t-time bounded Kolmogorov Complexity, K t , is mildly hard-on-average (i.e., there exists a polynomial such that no PPT algorithm can compute K t , for more than a 1-[1/p(n)] fraction of n-bit strings). In doing so, we present the first natural, and well-studied, computational problem characterizing the feasibility of the central private-key primitives and protocols in Cryptography.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00118"}, {"primary_key": "2584292", "vector": [], "sparse_vector": [], "title": "A Parameterized Approximation Scheme for Min $k$-Cut.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the Min k-cut problem, input is an edge weighted graph G and an integer k, and the task is to partition the vertex set into k non-empty sets, such that the total weight of the edges with endpoints in different parts is minimized. When k is part of the input, the problem is NP-complete and hard to approximate within any factor less than 2. Recently, the problem has received significant attention from the perspective of parameterized approximation. <PERSON> et al. [SODA 2018] initiated the study of FPT-approximation for the Min k-Cut problem and gave an 1.9997-approximation algorithm running in time 2 O(k6 )n O(1) . Later, the same set of authors [FOCS 2018] designed an ( 1+ε)-approximation algorithm that runs in time (k/ε) O(k) n k+O(1) , and a 1.81-approximation algorithm running in time 2 O(k2 )n O(1) . More, recently, <PERSON><PERSON><PERSON> and Lin [SODA 2020] gave a (5/3+ε)-approximation for Min k-Cut running in time 2 O(k2 logk)n O(1) . In this paper we give a parameterized approximation algorithm with best possible approximation guarantee, and best possible running time dependence on said guarantee (up to Exponential Time Hypothesis (ETH) and constants in the exponent). In particular, for every , the algorithm obtains a ( 1+ε)-approximate solution in time (k/ε) O(k) n O(1) . The main ingredients of our algorithm are: a simple sparsification procedure, a new polynomial time algorithm for decomposing a graph into highly connected parts, and a new exact algorithm with running time s O(k) n O(1) on unweighted (multi-) graphs. Here, s denotes the number of edges in a minimum k-cut. The latter two are of independent interest.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00079"}, {"primary_key": "2584293", "vector": [], "sparse_vector": [], "title": "Maximizing Determinants under Matroid Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "Aleksan<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Given a set of vectors v1, ... , v n ∈ R d and a matroid M=([n],I), we study the problem of finding a basis S of M such that det(Σ i ∈s v i v i T ) is maximized. This problem appears in a diverse set of areas, such as experimental design, fair allocation of goods, network design, and machine learning. The current best results include an e 2k -estimation for any matroid of rank k [8] and a (1+ε) d -approximation for a uniform matroid of rank k ≥ d+[d/(ε)] [30], where the rank k ≥ d denotes the desired size of the optimal set. Our main result is a new approximation algorithm for the general problem with an approximation guarantee that depends only on the dimension d of the vectors, and not on the size k of the output set. In particular, we show an (O(d)) d -estimation and an (O(d)) d3 -approximation for any matroid, giving a significant improvement over prior work when k ≫ d. Our result relies on showing that there exists an optimal solution to a convex programming relaxation for the problem which has sparse support; in particular, no more than O(d 2 ) variables of the solution have fractional values. The sparsity results rely on the interplay between the first order optimality conditions for the convex program and matroid theory. We believe that the techniques introduced to show sparsity of optimal solutions to convex programs will be of independent interest. We also give a new randomized rounding algorithm that crucially exploits the sparsity of solutions to the convex program. To show the approximation guarantee, we utilize recent works on strongly log-concave polynomials [8], [4] and show new relationships between different convex programs [33], [6] studied for the problem. Finally, we show how to use the estimation algorithm to give an efficient deterministic approximation algorithm. Once again, the algorithm crucially relies on sparsity of the fractional solution to guarantee that the approximation factor depends solely on the dimension d.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00059"}, {"primary_key": "2584294", "vector": [], "sparse_vector": [], "title": "Scheduling Precedence-Constrained Jobs on Related Machines with Communication Delay.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of scheduling precedence-constrained jobs on uniformly-related machines in the presence of an arbitrary, fixed communication delay. Communication delay is the amount of time that must pass between the completion of a job on one machine and the start of any successor of that job on a different machine. We consider a model that allows job duplication, i.e. processing of the same job on multiple machines, which, as we show, can reduce the length of a schedule (i.e., its makespan) by a logarithmic factor. Our main result is an approximation algorithm for makespan with approximation ratio polylogarithmic in the number of machines and the length of the communication delay, assuming the minimum makespan is at least the delay. Our algorithm is based on rounding a linear programming relaxation for the problem, which includes carefully designed constraints capturing the interaction among communication delay, precedence requirements, varying speeds, and job duplication. To derive a schedule from a solution to the linear program, we balance the benefits of duplication in satisfying precedence constraints early against its drawbacks in increasing overall system load. Our result builds on two previous lines of work, one with communication delay but identical machines (<PERSON><PERSON><PERSON>, <PERSON> 2002), and the other with uniformly-related machines but no communication delay (<PERSON><PERSON>, <PERSON> 1999). We next show that the integrality gap of our mathematical program is polylogarithmic in the communication delay. Our gap construction employs expander graphs and exploits a property of robust expansion and its generalization to paths of longer length, which may be of independent interest. Finally, we quantify the advantage of duplication in scheduling with communication delay. We show that the best schedule without duplication can have a larger makespan than the optimal with duplication by a logarithmic factor. Nevertheless, we present a polynomial time algorithm to transform any schedule to a schedule without duplication at the cost of an increase in makespan polylogarithmic in the number of jobs and machines. Together with our makespan approximation algorithm for schedules allowing duplication, this also yields a polylogarithmic-approximation algorithm for the setting where duplication is not allowed.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00082"}, {"primary_key": "2584295", "vector": [], "sparse_vector": [], "title": "Symbolic determinant identity testing (SDIT) is not a null cone problem; and the symmetries of algebraic varieties.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The object of study of this paper is the following multi-determinantal algebraic variety, SINGn, m, which captures the symbolic determinant identity testing (SDIT) problem (a canonical version of the polynomial identity testing (PIT) problem), and plays a central role in algebra, algebraic geometry and computational complexity theory. SINGn, m is the set of all m-tuples of n×n complex matrices which span only singular matrices. In other words, the determinant of any linear combination of the matrices in such a tuple vanishes. The algorithmic complexity of testing membership in SINGn, m is a central question in computational complexity. Having almost a trivial probabilistic algorithm, finding an efficient deterministic algorithm is a holy grail of derandomization, and to top it, will imply super-polynomial circuit lower bounds! A sequence of recent works suggests efficient deterministic \"geodesic descent\" algorithms for memberships in a general class of algebraic varieties, namely the null cones of (reductive) linear group actions. Can such algorithms be used for the problem above? Our main result is negative: SINGn, m is not the null cone of any such group action! This stands in stark contrast to a non-commutative analog of this variety (for which such algorithms work), and points to an inherent structural difficulty of SINGn, m. In other words, we provide a barrier for the attempts of derandomizing SDIT via these algorithms. To prove this result we identify precisely the group of symmetries of SINGn, m. We find this characterization, and the tools we introduce to prove it, of independent interest. Our characterization significantly generalizes a result of <PERSON><PERSON>nius for the special case m=1 (namely, computing the symmetries of the determinant). Our proof suggests a general method for determining the symmetries of general algebraic varieties, an algorithmic problem that was hardly studied and we believe is central to algebraic complexity.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00086"}, {"primary_key": "2584296", "vector": [], "sparse_vector": [], "title": "Quantum isomorphism is equivalent to equality of homomorphism counts from planar graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Over 50 years ago, <PERSON><PERSON><PERSON><PERSON> proved that two graphs are isomorphic if and only if they admit the same number of homomorphisms from any graph. Other equivalence relations on graphs, such as cospectrality or fractional isomorphism, can be characterized by equality of homomorphism counts from an appropriately chosen class of graphs. <PERSON><PERSON><PERSON><PERSON> [J. Graph Theory 2010] showed that taking this class to be the graphs of treewidth at most k yields a tractable relaxation of graph isomorphism known as k-dimensional Weisfeiler-Leman equivalence. Together with a famous result of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON> 1989], this shows that homomorphism counts from graphs of bounded treewidth do not determine a graph up to isomorphism. <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [ICALP 2018] raised the questions of whether homomorphism counts from planar graphs determine a graph up to isomorphism, and what is the complexity of the resulting relation. We answer the former in the negative by showing that the resulting relation is equivalent to the so-called quantum isomorphism [<PERSON><PERSON><PERSON> et al, ICALP 2017]. Using this equivalence, we further resolve the latter question, showing that testing whether two graphs have the same number of homomorphisms from any planar graph is, surprisingly, an undecidable problem, and moreover is complete for the class coRE (the complement of recursively enumerable problems). Quantum isomorphism is defined in terms of a one-round, two-prover interactive proof system in which quantum provers, who are allowed to share entanglement, attempt to convince the verifier that the graphs are isomorphic. Our combinatorial proof leverages the quantum automorphism group of a graph, a notion from noncommutative mathematics.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00067"}, {"primary_key": "2584297", "vector": [], "sparse_vector": [], "title": "LDPC Codes Achieve List Decoding Capacity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that <PERSON><PERSON><PERSON>'s ensemble of Low-Density Parity Check (LDPC) codes achieves list-decoding capacity with high probability. These are the first graph-based codes shown to have this property. This result opens up a potential avenue towards truly linear-time list-decodable codes that achieve list-decoding capacity. Our result on list decoding follows from a much more general result: any local property satisfied with high probability by a random linear code is also satisfied with high probability by a random LDPC code from <PERSON><PERSON><PERSON>'s distribution. Local properties are properties characterized by the exclusion of small sets of codewords, and include list-decoding, list-recovery and average-radius list-decoding. In order to prove our results on LDPC codes, we establish sharp thresholds for when local properties are satisfied by a random linear code. More precisely, we show that for any local property P, there is some R* so that random linear codes of rate slightly less than R* satisfy P with high probability, while random linear codes of rate slightly more than R* with high probability do not. We also give a characterization of the threshold rate R*. This is an extended abstract. The full version is available at https://arxiv.org/abs/1909.06430", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00050"}, {"primary_key": "2584298", "vector": [], "sparse_vector": [], "title": "Explicit near-fully X-Ramanujan graphs.", "authors": ["Ryan <PERSON>&<PERSON>;Donnell", "<PERSON><PERSON><PERSON>"], "summary": "Let p(Y1, ..., Yd, Z1, ..., Ze) be a self-adjoint noncommutative polynomial, with coefficients from C r×r , in the indeterminates Y1,..., Yd (considered to be self-adjoint), the indeterminates Z1, ..., Ze, and their adjoints Z1*, ..., Ze*. Suppose Y1, ..., Yd are replaced by independent random n x n matching matrices, and Z1, ..., Ze are replaced by independent random n x n permutation matrices. Assuming for simplicity that p's coefficients are 0-1 matrices, the result can be thought of as a kind of random rn-vertex graph G. As n goes to infinity, there will be a natural limiting infinite graph X that covers any finite outcome for G. A recent landmark result of <PERSON><PERSON><PERSON> and <PERSON> shows that for any , with high probability the spectrum of a random G will be eps-close in Hausdorff distance to the spectrum of X (once the suitably defined \"trivial\" eigenvalues are excluded). We say that G is \"eps-near fully X-Ramanujan\". Our work has two contributions: First we study and clarify the class of infinite graphs X that can arise in this way. Second, we derandomize the <PERSON><PERSON><PERSON><PERSON><PERSON> result: for any X, we provide explicit, arbitrarily large graphs G that are covered by X and that have (nontrivial) spectrum at Hausdorff distance at most eps from that of X. This significantly generalizes the recent work of <PERSON><PERSON> et al., which provided explicit near-Ramanujan graphs for every degree d (meaning d-regular graphs with all nontrivial eigenvalues bounded in magnitude by 2sqrt(d-1) + eps). As an application of our main technical theorem, we are also able to determine the \"eigenvalue relaxation value\" for a wide class of average-case degree-2 constraint satisfaction problems.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00101"}, {"primary_key": "2584299", "vector": [], "sparse_vector": [], "title": "Is it Easier to Prove Theorems that are Guaranteed to be True?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Consider the following two fundamental open problems in complexity theory: ; Does a hard-on-average language in NP imply the existence of one-way functions? : Does a hard-on-average language in NP imply a hard-on-average problem in TFNP (i.e., the class of total NP search problem)? Our main result is that the answer to (at least) one of these questions is yes. Both one-way functions and problems in TFNP can be interpreted as promise-true distributional NP search problems-namely, distributional search problems where the sampler only samples true statements. As a direct corollary of the above result, we thus get that the existence of a hard-on-average distributional NP search problem implies a hard-on-average promise-true distributional NP search problem. In other words, It is no easier to find witnesses (a.k.a. proofs) for efficiently-sampled statements (theorems) that are guaranteed to be true. This result follows from a more general study of interactive puzzles-a generalization of average-case hardness in NP- and in particular, a novel round-collapse theorem for computationally-sound protocols, analogous to <PERSON><PERSON><PERSON><PERSON>'s celebrated round-collapse theorem for information-theoretically sound protocols. As another consequence of this treatment, we show that the existence of O(1)-round public-coin non-trivial arguments (i.e., argument systems that are not proofs) imply the existence of a hard-on-average problem in NP/poly.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00119"}, {"primary_key": "2584300", "vector": [], "sparse_vector": [], "title": "KRW Composition Theorems via Lifting.", "authors": ["Susanna F. de Rezende", "Or Meir", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "One of the major open problems in complexity theory is proving super-logarithmic lower bounds on the depth of circuits (i.e., P⊈NC1 ). <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [13] suggested to approach this problem by proving that depth complexity behaves “as expected” with respect to the composition of functions f⋄g . They showed that the validity of this conjecture would imply that P⊈NC1 . Several works have made progress toward resolving this conjecture by proving special cases. In particular, these works proved the KRW conjecture for every outer function, but only for few inner functions. Thus, it is an important challenge to prove the KRW conjecture for a wider range of inner functions. In this work, we extend significantly the range of inner functions that can be handled. First, we consider the monotone version of the KRW conjecture. We prove it for every monotone inner function whose depth complexity can be lower bounded via a query-to-communication lifting theorem. This allows us to handle several new and well-studied functions such as the s−t -connectivity, clique, and generation functions. In order to carry this progress back to the non-monotone setting, we introduce a new notion of semi-monotone composition, which combines the non-monotone complexity of the outer function with the monotone complexity of the inner function. In this setting, we prove the KRW conjecture for a similar selection of inner functions, but only for a specific choice of the outer function f .", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00013"}, {"primary_key": "2584301", "vector": [], "sparse_vector": [], "title": "Lifting with Simple Gadgets and Applications to Circuit and Proof Complexity.", "authors": ["Susanna F. de Rezende", "Or Meir", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We significantly strengthen and generalize the theorem lifting <PERSON>ullstellensatz degree to monotone span program size by <PERSON><PERSON><PERSON> and <PERSON>(2018) so that it works for any gadget with high enough rank . We apply our generalizedtheorem to solve two open problems: We present the first result that demonstrates a separation in proof powerfor cutting planes with unbounded versus polynomially bounded coefficients . * We give the first explicit separation between monotonerary formulas andmonotone real formulas . Previously only anon-explicit separation was known . An important technical ingredient, which may be of independent interest, is that we show that the . standard decision treecomplexity and the parity decision tree complexity of the . corresponding decision . tree complexity are equal . In particular, this implies that the standard . decision tree complexity and . the . parity decision Tree complexity of . the correspondingfalsified clause search problem are equal are equal. In addition, we give an explicit family of functionsthat can be computed with monotronary formulas of nearly linear", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00011"}, {"primary_key": "2584302", "vector": [], "sparse_vector": [], "title": "Local Proofs Approaching the Witness Length [Extended Abstract].", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Interactive oracle proofs (IOPs) are a hybrid between interactive proofs and PCPs. In an IOP the prover is allowed to interact with a verifier (like in an interactive proof) by sending relatively long messages to the verifier, who in turn is only allowed to query a few of the bits that were sent (like in a PCP). Efficient IOPs are at the core of leading practical implementations of highly efficient proof-systems. In this work we construct, for a large class of N P relations, IOPs in which the communication complexity approaches the witness length. More precisely, for any N P relation for which membership can be decided in polynomial-time and bounded polynomial space (e.g., SAT, Hamiltonicity, Clique, Vertex-Cover, etc.) and for any constant , we construct an IOP with communication complexity (1+γ)·n, where n is the original witness length. The number of rounds, as well as the number of queries made by the IOP verifier, are constant. This result improves over prior works on short IOPs/PCPs in two ways. First, the communication complexity in these short IOPs is proportional to the complexity of verifying the NP witness, which can be polynomially larger than the witness size. Second, even ignoring the difference between witness length and non-deterministic verification time, prior works incur (at the very least) a large constant multiplicative overhead to the communication complexity. In particular, as a special case, we also obtain an IOP for CircuitSAT with communication complexity (1+γ)·t, for circuits of size t and any constant . This improves upon the prior state-of-the-art work of Ben Sasson et al. (ICALP, 2017) who construct an IOP for CircuitSAT with communication length c·t for a large (unspecified) constant c ≥ 1. Our proof leverages the local testability and (relaxed) local correctability of high-rate tensor codes, as well as their support of a sumcheck-like procedure. In particular, we bypass the barrier imposed by the low rate of multiplication codes (e.g., Reed-Solomon, Reed-Muller or AG codes) - a key building block of all known short PCP/IOP constructions.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00083"}, {"primary_key": "2584303", "vector": [], "sparse_vector": [], "title": "Counting Small Induced Subgraphs Satisfying Monotone Properties.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given a graph property Φ, we study the problem #INDSUB(Φ) which asks, on input a graph G and a positive integer k, to compute the number # IndSub(Φ, k→ G) of induced subgraphs of size k in G that satisfy Φ. The search for explicit criteria on Φ ensuring that # INDSUB(Φ) is hard was initiated by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [J. Comput. Syst. Sci. 15] and is part of the major line of research on counting small patterns in graphs. However, apart from an implicit result due to <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> [STOC 17] proving that a full classification into \"easy\" and \"hard\" properties is possible and some partial results on edge-monotone properties due to <PERSON><PERSON><PERSON> [Discret. Appl. Math. 16] and <PERSON><PERSON><PERSON><PERSON> et al. [MFCS 19], not much is known. In this work, we fully answer and explicitly classify the case of monotone, that is subgraph-closed, properties: We show that for any non-trivial monotone property Φ, the problem #INDSUB(Φ) cannot be solved in time f(k). |V(G)| o(k/log1/2 (k)) for any function f, unless the Exponential Time Hypothesis fails. By this, we establish that any significant improvement over the brute-force approach is unlikely; in the language of parameterized complexity, we also obtain a #W[1] - completeness result.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00128"}, {"primary_key": "2584304", "vector": [], "sparse_vector": [], "title": "Lazy Search Trees.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce the lazy search tree data structure. The lazy search tree is a comparison-based data structure on the pointer machine that supports order-based operations such as rank, select, membership, predecessor, successor, minimum, and maximum while providing dynamic operations insert, delete, change-key, split, and merge. We analyze the performance of our data structure based on a partition of current elements into a set of gaps Δ i based on rank. A query falls into a particular gap and splits the gap into two new gaps at a rank r associated with the query operation. If we define B=Σ i |Δ i |log 2 (n/|Δ i |), our performance over a sequence of n insertions and q distinct queries is O(B+min(n log log n, n log q)). We show B is a lower bound. Effectively, we reduce the insertion time of binary search trees from Θ(log n) to O(min(log(n/|Δ i |)+ log log|Δ i |, log q)), where Δ i is the gap in which the inserted element falls. Over a sequence of n insertions and q queries, a time bound of O(n log q+q log n) holds; better bounds are possible when queries are non-uniformly distributed. As an extreme case of non-uniformity, if all queries are for the minimum element, the lazy search tree performs as a priority queue with O(log log n) time insert and decrease-key operations. The same data structure supports queries for any rank, interpolating between binary search trees and efficient priority queues. Lazy search trees can be implemented to operate mostly on arrays, requiring only O(min(q, n)) pointers, suggesting smaller memory footprint, better constant factors, and better cache performance compared to many existing efficient priority queues or binary search trees. Via direct reduction, our data structure also supports the efficient access theorems of the splay tree, providing a powerful data structure for non-uniform element access, both when the number of accesses is small and large.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00071"}, {"primary_key": "2584305", "vector": [], "sparse_vector": [], "title": "Tight Limits on Nonlocality from Nontrivial Communication Complexity; a.k.a. Reliable Computation with Asymmetric Gate Noise.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "It has long been known that the existence of certain superquantum nonlocal correlations would cause communication complexity to collapse. The absurdity of a world in which any nonlocal binary function could be evaluated with a constant amount of communication in turn provides a tantalizing way to distinguish quantum mechanics from incorrect theories of physics; the statement \"communication complexity is nontrivial\" has even been conjectured to be a concise information-theoretic axiom for characterizing quantum mechanics. We directly address the viability of that perspective with two results. First, we exhibit a nonlocal game such that communication complexity collapses in any physical theory whose maximal winning probability exceeds the quantum value. Second, we consider the venerable CHSH game that initiated this line of inquiry. In that case, the quantum value is about 0.85 but it is known that a winning probability of approximately 0.91 would collapse communication complexity. We provide evidence that the 0.91 result is the best possible using a large class of proof strategies, suggesting that the communication complexity axiom is insufficient for characterizing CHSH correlations. Both results build on new insights about reliable classical computation. The first exploits our formalization of an equivalence between amplification and reliable computation, while the second follows from an upper bound on the threshold for reliable computation with formulas of noisy XOR and AND gates.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00028"}, {"primary_key": "2584306", "vector": [], "sparse_vector": [], "title": "Towards Optimal Separations between Quantum and Randomized Query Complexities.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The query model offers a concrete setting where quantum algorithms are provably superior to randomized algorithms. Beautiful results by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and others presented partial Boolean functions that can be computed by quantum algorithms making much fewer queries compared to their randomized analogs. To date, separations of O(1) vs. √N between quantum and randomized query complexities remain the state-of-the-art (where N is the input length), leaving open the question of whether O(1) vs. N 1/2+Ω(1) separations are possible? We answer this question in the affirmative. Our separating problem is a variant of the Aaronson-Ambainis k-fold Forrelation problem. We show that our variant: 1)Can be solved by a quantum algorithm making 2 O(k) queries to the inputs. 2)Requires at least ~Ω(N 2(k-1)/(3k-1) ) queries for any randomized algorithm. For any constant , this gives a O(1) vs. N 1/2-ε separation between the quantum and randomized query complexities of partial Boolean functions. Our proof is Fourier analytical and uses new bounds on the Fourier spectrum of classical decision trees, which could be of independent interest. Looking forward, we conjecture that the Fourier bounds could be further improved in a precise manner, and show that such conjectured bounds imply optimal O(1) vs. N 1-ε separations between the quantum and randomized query complexities of partial Boolean functions.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00030"}, {"primary_key": "2584307", "vector": [], "sparse_vector": [], "title": "Testing linear-invariant properties.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fix a prime p and a positive integer R. We study the property testing of functions \\mathbbFp n →[R]. We say that a property is testable if there exists an oblivious tester for this property with one-sided error and constant query complexity. Furthermore, a property is proximity oblivious-testable (PO-testable) if the test is also independent of the proximity parameter ε. It is known that a number of natural properties such as linearity and being a low degree polynomial are PO-testable. These properties are examples of linear-invariant properties, meaning that they are preserved under linear automorphisms of the domain. Following work of <PERSON> and <PERSON>, the study of linear-invariant properties has been an important problem in arithmetic property testing. A central conjecture in this field, proposed by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, is that a linear-invariant property is testable if and only if it is semi subspace-hereditary. We prove two results, the first resolves this conjecture and the second classifies PO-testable properties. 1) A linear-invariant property is testable if and only if it is semi subspace-hereditary. 2) A linear-invariant property is PO-testable if and only if it is locally characterized. Our innovations are two-fold. We give a more powerful version of the compactness argument first introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. This relies on a new strong arithmetic regularity lemma in which one mixes different levels of Gowers uniformity. This allows us to extend the work of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON> by removing the bounded complexity restriction in their work. Our second innovation is a novel recoloring technique called patching. This Ramsey-theoretic technique is critical for working in the linear-invariant setting and allows us to remove the translation-invariant restriction present in previous work.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00113"}, {"primary_key": "2584308", "vector": [], "sparse_vector": [], "title": "Monochromatic Triangles, Triangle Listing and APSP.", "authors": ["Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON> Xu"], "summary": "All-Pairs Shortest Paths (APSP) is one of the most basic problems in graph algorithms. Given an n-node directed or undirected graph with integer weights in {-n c , ..., n c } and no negative cycles, APSP asks to compute the shortest paths distance between every pair of vertices. The fastest known algorithm for APSP runs in n 3 /2 Θ(√{logn}) time [<PERSON>'14], and no truly subcubic time algorithms are known. One of the main hypotheses in fine-grained complexity is that APSP requires n 3-o(1) time. Another famous hypothesis in fine-grained complexity is that the 3SUM problem for n integers (which can be solved in O(n 2 ) time) requires n 2-o(1) time. Although there are no direct reductions between 3SUM and APSP, it is known that they are related: the (min, +)-convolution problem reduces in a fine-grained way to both, and both fine-grained reduce to the Exact Triangle problem. In this paper we find more relationships between these two problems and other basic problems. <PERSON><PERSON><PERSON><PERSON><PERSON> had shown that under the 3SUM hypothesis the All-Edges Sparse Triangle problem in m-edge graphs requires m 4/3-o(1) time. The latter problem asks to determine for every edge e, whether e is in a triangle. It is equivalent to the problem of listing m triangles in an m-edge graph where m=-Õ(n 1.5 ), and can be solved in O(m 1.41 ) time [<PERSON><PERSON> et al.'97] with the current matrix multiplication bounds, and in Õ(m 4/3 ) time if ω = 2. We show that one can reduce Exact Triangle to All-Edges Sparse Triangle, showing that All-Edges Sparse Triangle (and hence Triangle Listing) requires m 4/3-o(1) time also assuming the APSP hypothesis. This allows us to provide APSP-hardness for many dynamic problems that were previously known to be hard under the 3SUM hypothesis. We also consider the All-Edges Monochromatic Triangle problem. Via work of [Lincoln et al.'20], our result on All-Edges Sparse Triangle implies that if the All-Edges Monochromatic Triangle problem has an O(n 2.5-ε ) time algorithm for , then both the APSP and 3SUM hypotheses are false. The fastest algorithm for All-Edges Monochromatic Triangle runs in Õ(n (3+ω)/2 ) time [Vassilevska et al.'06], and our new reduction shows that if ω = 2, this algorithm is best possible, unless 3SUM or APSP can be solved faster. Besides 3SUM, previously the only problems known to be fine-grained reducible to All-Edges Monochromatic Triangle were the seemingly easier problems directed unweighted APSP and Min-Witness Product [Lincoln et al.'20]. Our reduction shows that this problem is much harder. We also connect the problem to other \"intermediate\" problems, whose runtimes are between O(n ω ) and O(n 3 ), such as the Max-Min product problem.", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00078"}, {"primary_key": "2584309", "vector": [], "sparse_vector": [], "title": "Sparse PCA: Algorithms, Adversarial Perturbations and Certificates.", "authors": ["Tommaso d&apos;<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study efficient algorithms for Sparse PCA in standard statistical models (spiked covariance in its Wishart form). Our goal is to achieve optimal recovery guarantees while being resilient to small perturbations. Despite a long history of prior works, including explicit studies of perturbation resilience, the best known algorithmic guarantees for Sparse PCA are fragile and break down under small adversarial perturbations. We observe a basic connection between perturbation resilience and certifying algorithms that are based on certificates of upper bounds on sparse eigenvalues of random matrices. In contrast to other techniques, such certifying algorithms, including the brute-force maximum likelihood estimator, are automatically robust against small adversarial perturbation. We use this connection to obtain the first polynomial-time algorithms for this problem that are resilient against additive adversarial perturbations by obtaining new efficient certificates for upper bounds on sparse eigenvalues of random matrices. Our algorithms are based either on basic semidefinite programming or on its low-degree sum-of-squares strengthening depending on the parameter regimes. Their guarantees either match or approach the best known guarantees of fragile algorithms in terms of sparsity of the unknown vector, number of samples and the ambient dimension. To complement our algorithmic results, we prove rigorous lower bounds matching the gap between fragile and robust polynomial-time algorithms in a natural computational model based on low-degree polynomials (closely related to the pseudo-calibration technique for sum-of-squares lower bounds) that is known to capture the best known guarantees for related statistical estimation problems. The combination of these results provides formal evidence of an inherent price to pay to achieve robustness. Beyond these issues of perturbation resilience, our analysis also leads to new algorithms for the fragile setting, whose guarantees improve over best previous results in some parameter regimes (e.g. if the sample size is polynomially smaller than the dimension).", "published": "2020-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS46700.2020.00058"}]