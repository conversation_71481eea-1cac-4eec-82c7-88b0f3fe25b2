[{"primary_key": "3866042", "vector": [], "sparse_vector": [], "title": "Low-Latency Analytics on Colossal Data Streams with SummaryStore.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "SummaryStore is an approximate time-series store, designed for analytics, capable of storing large volumes of time-series data (~1 petabyte) on a single node; it preserves high degrees of query accuracy and enables near real-time querying at unprecedented cost savings. SummaryStore contributes time-decayed summaries, a novel abstraction for summarizing data streams, along with an ingest algorithm to continually merge the summaries for efficient range queries; in conjunction, it returns reliable error estimates alongside the approximate answers, supporting a range of machine learning and analytical workloads. We successfully evaluated SummaryStore using real-world applications for forecasting, outlier detection, and Internet traffic monitoring; it can summarize aggressively with low median errors, 0.1 to 10%, for different workloads. Under range-query microbenchmarks, it stored 1PB synthetic stream data (10241TB streams), on a single node, using roughly 10 TB (100x compaction) with 95%-ile error below 5% and median cold-cache query latency of 1.3s (worst case latency under 70s).", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132758"}, {"primary_key": "3866045", "vector": [], "sparse_vector": [], "title": "Scaling a file system to many cores using an operation log.", "authors": ["Srivat<PERSON> S<PERSON> Bhat", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It is challenging to simultaneously achieve multicore scalability and high disk throughput in a file system. For example, even for commutative operations like creating different files in the same directory, current file systems introduce cache-line conflicts when updating an in-memory copy of the on-disk directory block, which limits scalability.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132779"}, {"primary_key": "3866046", "vector": [], "sparse_vector": [], "title": "Prochlo: Strong Privacy for Analytics in the Crowd.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The large-scale monitoring of computer users' software activities has become commonplace, e.g., for application telemetry, error reporting, or demographic profiling. This paper describes a principled systems architecture---Encode, Shuffle, Analyze (ESA)---for performing such monitoring with high utility while also protecting user privacy. The ESA design, and its Prochlo implementation, are informed by our practical experiences with an existing, large deployment of privacy-preserving software monitoring. (cont.; see the paper)", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132769"}, {"primary_key": "3866049", "vector": [], "sparse_vector": [], "title": "Verifying a high-performance crash-safe file system using a tree specification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Atalay Me<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "DFSCQ is the first file system that (1) provides a precise specification for fsync and fdatasync, which allow applications to achieve high performance and crash safety, and (2) provides a machine-checked proof that its implementation meets this specification. DFSCQ's specification captures the behavior of sophisticated optimizations, including log-bypass writes, and DFSCQ's proof rules out some of the common bugs in file-system implementations despite the complex optimizations.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132776"}, {"primary_key": "3866052", "vector": [], "sparse_vector": [], "title": "Resource Central: Understanding and Predicting Workloads for Improved Resource Management in Large Cloud Platforms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cloud research to date has lacked data on the characteristics of the production virtual machine (VM) workloads of large cloud providers. A thorough understanding of these characteristics can inform the providers' resource management systems, e.g. VM scheduler, power manager, server health manager. In this paper, we first introduce an extensive characterization of Microsoft Azure's VM workload, including distributions of the VMs' lifetime, deployment size, and resource consumption. We then show that certain VM behaviors are fairly consistent over multiple lifetimes, i.e. history is an accurate predictor of future behavior. Based on this observation, we next introduce Resource Central (RC), a system that collects VM telemetry, learns these behaviors offline, and provides predictions online to various resource managers via a general client-side library. As an example of RC's online use, we modify Azure's VM scheduler to leverage predictions in oversubscribing servers (with oversubscribable VM types), while retaining high VM performance. Using real VM traces, we then show that the prediction-informed schedules increase utilization and prevent physical resource exhaustion. We conclude that providers can exploit their workloads' characteristics and machine learning to improve resource management substantially.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132772"}, {"primary_key": "3866053", "vector": [], "sparse_vector": [], "title": "Komodo: Using verification to disentangle secure-enclave hardware from software.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Intel SGX promises powerful security: an arbitrary number of user-mode enclaves protected against physical attacks and privileged software adversaries. However, to achieve this, Intel extended the x86 architecture with an isolation mechanism approaching the complexity of an OS microkernel, implemented by an inscrutable mix of silicon and microcode. While hardware-based security can offer performance and features that are difficult or impossible to achieve in pure software, hardware-only solutions are difficult to update, either to patch security flaws or introduce new features.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132782"}, {"primary_key": "3866055", "vector": [], "sparse_vector": [], "title": "Automatically Repairing Network Control Planes Using an Abstract Representation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The forwarding behavior of computer networks is governed by the configuration of distributed routing protocols and access filters---collectively known as the network control plane. Unfortunately, control plane configurations are often buggy, causing networks to violate important policies: e.g., specific traffic classes (defined in terms of source and destination endpoints) should always be able to reach their destination, or always traverse a waypoint. Manually repairing these configurations is daunting because of their inter-twined nature across routers, traffic classes, and policies.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132753"}, {"primary_key": "3866056", "vector": [], "sparse_vector": [], "title": "Algorand: Scaling Byzantine Agreements for Cryptocurrencies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Algorand is a new cryptocurrency that confirms transactions with latency on the order of a minute while scaling to many users. Algorand ensures that users never have divergent views of confirmed transactions, even if some of the users are malicious and the network is temporarily partitioned. In contrast, existing cryptocurrencies allow for temporary forks and therefore require a long time, on the order of an hour, to confirm transactions with high confidence.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132757"}, {"primary_key": "3866057", "vector": [], "sparse_vector": [], "title": "MittOS: Supporting Millisecond Tail Tolerance with Fast Rejecting SLO-Aware OS Interface.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ce<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "MittOS provides operating system support to cut millisecond-level tail latencies for data-parallel applications. In MittOS, we advocate a new principle that operating system should quickly reject IOs that cannot be promptly served. To achieve this, MittOS exposes a fast rejecting SLO-aware interface wherein applications can provide their SLOs (e.g., IO deadlines). If MittOS predicts that the IO SLOs cannot be met, MittOS will promptly return EBUSY signal, allowing the application to failover (retry) to another less-busy node without waiting. We build MittOS within the storage stack (disk, SSD, and OS cache managements), but the principle is extensible to CPU and runtime memory managements as well. MittOS' no-wait approach helps reduce IO completion time up to 35% compared to wait-then-speculate approaches.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132774"}, {"primary_key": "3866059", "vector": [], "sparse_vector": [], "title": "SVE: Distributed Video Processing at Facebook Scale.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Videos are an increasingly utilized part of the experience of the billions of people that use Facebook. These videos must be uploaded and processed before they can be shared and downloaded. Uploading and processing videos at our scale, and across our many applications, brings three key requirements: low latency to support interactive applications; a flexible programming model for application developers that is simple to program, enables efficient processing, and improves reliability; and robustness to faults and overload. This paper describes the evolution from our initial monolithic encoding script (MES) system to our current Streaming Video Engine (SVE) that overcomes each of the challenges. SVE has been in production since the fall of 2015, provides lower latency than MES, supports many diverse video applications, and has proven to be reliable despite faults and overload.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132775"}, {"primary_key": "3866062", "vector": [], "sparse_vector": [], "title": "NetCache: Balancing Key-Value Stores with Fast In-Network Caching.", "authors": ["Xi<PERSON>", "Xiaozhou Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Ion <PERSON>"], "summary": "We present NetCache, a new key-value store architecture that leverages the power and flexibility of new-generation programmable switches to handle queries on hot items and balance the load across storage nodes. NetCache provides high aggregate throughput and low latency even under highly-skewed and rapidly-changing workloads. The core of NetCache is a packet-processing pipeline that exploits the capabilities of modern programmable switch ASICs to efficiently detect, index, cache and serve hot key-value items in the switch data plane. Additionally, our solution guarantees cache coherence with minimal overhead. We implement a NetCache prototype on Barefoot Tofino switches and commodity servers and demonstrate that a single switch can process 2+ billion queries per second for 64K items with 16-byte keys and 128-byte values, while only consuming a small portion of its hardware resources. To the best of our knowledge, this is the first time that a sophisticated application-level functionality, such as in-network caching, has been shown to run at line rate on programmable switches. Furthermore, we show that NetCache improves the throughput by 3-10x and reduces the latency of up to 40% of queries by 50%, for high-performance, in-memory key-value stores.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132764"}, {"primary_key": "3866063", "vector": [], "sparse_vector": [], "title": "Canopy: An End-to-End Performance Tracing And Analysis System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>;<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents Canopy, Facebook's end-to-end performance tracing infrastructure. Canopy records causally related performance data across the end-to-end execution path of requests, including from browsers, mobile applications, and backend services. Canopy processes traces in near real-time, derives user-specified features, and outputs to performance datasets that aggregate across billions of requests. Using Canopy, Facebook engineers can query and analyze performance data in real-time. Canopy addresses three challenges we have encountered in scaling performance analysis: supporting the range of execution and performance models used by different components of the Facebook stack; supporting interactive ad-hoc analysis of performance data; and enabling deep customization by users, from sampling traces to extracting and visualizing features. Canopy currently records and processes over 1 billion traces per day. We discuss how Canopy has evolved to apply to a wide range of scenarios, and present case studies of its use in solving various performance challenges.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132749"}, {"primary_key": "3866064", "vector": [], "sparse_vector": [], "title": "Lazy Diagnosis of In-Production Concurrency Bugs.", "authors": ["<PERSON><PERSON>", "Weidong Cui", "Xinyang Ge", "<PERSON>"], "summary": "Diagnosing concurrency bugs---the process of understanding the root causes of concurrency failures---is hard. Developers depend on reproducing concurrency bugs to diagnose them. Traditionally, systems that attempt to reproduce concurrency bugs record fine-grained thread schedules of events (e.g., shared memory accesses) that lead to failures. Recording schedules incurs high runtime performance overhead and scales poorly, making existing techniques unsuitable in production.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132767"}, {"primary_key": "3866065", "vector": [], "sparse_vector": [], "title": "Rocksteady: Fast Migration for Low-latency In-memory Storage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Scalable in-memory key-value stores provide low-latency access times of a few microseconds and perform millions of operations per second per server. With all data in memory, these systems should provide a high level of reconfigurability. Ideally, they should scale up, scale down, and rebalance load more rapidly and flexibly than disk-based systems. Rapid reconfiguration is especially important in these systems since a) DRAM is expensive and b) they are the last defense against highly dynamic workloads that suffer from hot spots, skew, and unpredictable load. However, so far, work on in-memory key-value stores has generally focused on performance and availability, leaving reconfiguration as a secondary concern.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132784"}, {"primary_key": "3866066", "vector": [], "sparse_vector": [], "title": "Atom: <PERSON><PERSON><PERSON> Strong Anonymity.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Atom is an anonymous messaging system that protects against traffic-analysis attacks. Unlike many prior systems, each Atom server touches only a small fraction of the total messages routed through the network. As a result, the system's capacity scales near-linearly with the number of servers. At the same time, each Atom user benefits from \"best possible\" anonymity: a user is anonymous among all honest users of the system, even against an active adversary who monitors the entire network, a portion of the system's servers, and any number of malicious users. The architectural ideas behind Atom have been known in theory, but putting them into practice requires new techniques for (1) avoiding heavy general-purpose multi-party computation protocols, (2) defeating active attacks by malicious servers at minimal performance cost, and (3) handling server failure and churn.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132755"}, {"primary_key": "3866067", "vector": [], "sparse_vector": [], "title": "Strata: A Cross Media File System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Current hardware and application storage trends put immense pressure on the operating system's storage subsystem. On the hardware side, the market for storage devices has diversified to a multi-layer storage topology spanning multiple orders of magnitude in cost and performance. Above the file system, applications increasingly need to process small, random IO on vast data sets with low latency, high throughput, and simple crash consistency. File systems designed for a single storage layer cannot support all of these demands together.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132770"}, {"primary_key": "3866069", "vector": [], "sparse_vector": [], "title": "Multiprogramming a 64kB Computer Safely and Efficiently.", "authors": ["<PERSON><PERSON>", "<PERSON> Campbell", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Low-power microcontrollers lack some of the hardware features and memory resources that enable multiprogrammable systems. Accordingly, microcontroller-based operating systems have not provided important features like fault isolation, dynamic memory allocation, and flexible concurrency. However, an emerging class of embedded applications are software platforms, rather than single purpose devices, and need these multiprogramming features. Tock, a new operating system for low-power platforms, takes advantage of limited hardware-protection mechanisms as well as the type-safety features of the Rust programming language to provide a multiprogramming environment for microcontrollers. Tock isolates software faults, provides memory protection, and efficiently manages memory for dynamic application workloads written in any language. It achieves this while retaining the dependability requirements of long-running applications.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132786"}, {"primary_key": "3866070", "vector": [], "sparse_vector": [], "title": "Eris: Coordination-Free Consistent Transactions Using In-Network Concurrency Control.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Dan R. K. <PERSON>s"], "summary": "Distributed storage systems aim to provide strong consistency and isolation guarantees on an architecture that is partitioned across multiple shards for scalability and replicated for fault tolerance. Traditionally, achieving all of these goals has required an expensive combination of atomic commitment and replication protocols -- introducing extensive coordination overhead. Our system, Eris, takes a different approach. It moves a core piece of concurrency control functionality, which we term multi-sequencing, into the datacenter network itself. This network primitive takes on the responsibility for consistently ordering transactions, and a new lightweight transaction protocol ensures atomicity.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132751"}, {"primary_key": "3866071", "vector": [], "sparse_vector": [], "title": "KV-Direct: High-Performance In-Memory Key-Value Store with Programmable NIC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>hen<PERSON>uan", "<PERSON><PERSON><PERSON>", "<PERSON>wei Lu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Performance of in-memory key-value store (KVS) continues to be of great importance as modern KVS goes beyond the traditional object-caching workload and becomes a key infrastructure to support distributed main-memory computation in data centers. Recent years have witnessed a rapid increase of network bandwidth in data centers, shifting the bottleneck of most KVS from the network to the CPU. RDMA-capable NIC partly alleviates the problem, but the primitives provided by RDMA abstraction are rather limited. Meanwhile, programmable NICs become available in data centers, enabling in-network processing. In this paper, we present KV-Direct, a high performance KVS that leverages programmable NIC to extend RDMA primitives and enable remote direct key-value access to the main host memory.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132756"}, {"primary_key": "3866073", "vector": [], "sparse_vector": [], "title": "NEVE: Nested Virtualization Extensions for ARM.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Nested virtualization, the ability to run a virtual machine inside another virtual machine, is increasingly important because of the need to deploy virtual machines running software stacks on top of virtualized cloud infrastructure. As ARM servers make inroads in cloud infrastructure deployments, supporting nested virtualization on ARM is a key requirement, which has been met recently with the introduction of nested virtualization support to the ARM architecture. We build the first hypervisor to use ARM nested virtualization support and show that despite similarities between ARM and x86 nested virtualization support, performance on ARM is much worse than on x86. This is due to excessive traps to the hypervisor caused by differences in non-nested virtualization support. To address this problem, we introduce a novel paravirtualization technique to rapidly prototype architectural changes for virtualization and evaluate their performance impact using existing hardware. Using this technique, we propose Nested Virtualization Extensions for ARM (NEVE), a set of simple architectural changes to ARM that can be used by software to coalesce and defer traps by logging the results of hypervisor instructions until the results are actually needed by the hypervisor or virtual machines. We show that NEVE allows hypervisors running real application workloads to provide an order of magnitude better performance than current ARM nested virtualization support and up to three times less overhead than x86 nested virtualization. NEVE will be included in ARMv8.4, the next version of the ARM architecture.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132754"}, {"primary_key": "3866074", "vector": [], "sparse_vector": [], "title": "CrystalNet: Faithfully Emulating Large Production Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sri Tallapragada", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lihua Yuan"], "summary": "Network reliability is critical for large clouds and online service providers like Microsoft. Our network is large, heterogeneous, complex and undergoes constant churns. In such an environment even small issues triggered by device failures, buggy device software, configuration errors, unproven management tools and unavoidable human errors can quickly cause large outages. A promising way to minimize such network outages is to proactively validate all network operations in a high-fidelity network emulator, before they are carried out in production. To this end, we present CrystalNet, a cloud-scale, high-fidelity network emulator. It runs real network device firmwares in a network of containers and virtual machines, loaded with production configurations. Network engineers can use the same management tools and methods to interact with the emulated network as they do with a production network. CrystalNet can handle heterogeneous device firmwares and can scale to emulate thousands of network devices in a matter of minutes. To reduce resource consumption, it carefully selects a boundary of emulations, while ensuring correctness of propagation of network changes. Microsoft's network engineers use CrystalNet on a daily basis to test planned network operations. Our experience shows that CrystalNet enables operators to detect many issues that could trigger significant outages.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132759"}, {"primary_key": "3866075", "vector": [], "sparse_vector": [], "title": "My VM is Lighter (and Safer) than your Container.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Containers are in great demand because they are lightweight when compared to virtual machines. On the downside, containers offer weaker isolation than VMs, to the point where people run containers in virtual machines to achieve proper isolation. In this paper, we examine whether there is indeed a strict tradeoff between isolation (VMs) and efficiency (containers). We find that VMs can be as nimble as containers, as long as they are small and the toolstack is fast enough.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132763"}, {"primary_key": "3866077", "vector": [], "sparse_vector": [], "title": "Hyperkernel: Push-Button Verification of an OS Kernel.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper describes an approach to designing, implementing, and formally verifying the functional correctness of an OS kernel, named Hyperkernel, with a high degree of proof automation and low proof burden. We base the design of Hyperkernel's interface on xv6, a Unix-like teaching operating system. Hyperkernel introduces three key ideas to achieve proof automation: it finitizes the kernel interface to avoid unbounded loops or recursion; it separates kernel and user address spaces to simplify reasoning about virtual memory; and it performs verification at the LLVM intermediate representation level to avoid modeling complicated C semantics.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132748"}, {"primary_key": "3866078", "vector": [], "sparse_vector": [], "title": "Secure Page Fusion with VUsion: https: //www.vusec.net/projects/VUsion.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To reduce memory pressure, modern operating systems and hypervisors such as Linux/KVM deploy page-level memory fusion to merge physical memory pages with the same content (i.e., page fusion). A write to a fused memory page triggers a copy-on-write event that unmerges the page to preserve correct semantics. While page fusion is crucial in saving memory in production, recent work shows significant security weaknesses in its current implementations. Attackers can abuse timing side channels on the unmerge operation to leak sensitive data such as randomized pointers. Additionally, they can exploit the predictability of the merge operation to massage physical memory for reliable Rowhammer attacks. In this paper, we present VUsion, a secure page fusion system. VUsion can stop all the existing and even new classes of attack, where attackers leak information by side-channeling the merge operation or massage physical memory via predictable memory reuse patterns. To mitigate information disclosure attacks, we ensure attackers can no longer distinguish between fused and non-fused pages. To mitigate memory massaging attacks, we ensure fused pages are always allocated from a high-entropy pool. Despite its secure design, our comprehensive evaluation shows that VUsion retains most of the memory saving benefits of traditional memory fusion with negligible performance overhead while maintaining compatibility with other advanced memory management features.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132781"}, {"primary_key": "3866079", "vector": [], "sparse_vector": [], "title": "Monotasks: Architecting for Performance Clarity in Data Analytics Frameworks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In today's data analytics frameworks, many users struggle to reason about the performance of their workloads. Without an understanding of what factors are most important to performance, users can't determine what configuration parameters to set and what hardware to use to optimize runtime. This paper explores a system architecture designed to make it easy for users to reason about performance bottlenecks. Rather than breaking jobs into tasks that pipeline many resources, as in today's frameworks, we propose breaking jobs into monotasks: units of work that each use a single resource. We demonstrate that explicitly separating the use of different resources simplifies reasoning about performance without sacrificing performance. Monotasks provide job completion times within 9% of Apache Spark for typical scenarios, and lead to a model for job completion time that predicts runtime under different hardware and software configurations with at most 28% error. Furthermore, separating the use of different resources allows for new optimizations to improve performance.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132766"}, {"primary_key": "3866080", "vector": [], "sparse_vector": [], "title": "DeepXplore: Automated Whitebox Testing of Deep Learning Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Cao", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning (DL) systems are increasingly deployed in safety- and security-critical domains including self-driving cars and malware detection, where the correctness and predictability of a system's behavior for corner case inputs are of great importance. Existing DL testing depends heavily on manually labeled data and therefore often fails to expose erroneous behaviors for rare inputs.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132785"}, {"primary_key": "3866081", "vector": [], "sparse_vector": [], "title": "ZygOS: Achieving Low Tail Latency for Microsecond-scale Networked Tasks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper focuses on the efficient scheduling on multicore systems of very fine-grain networked tasks, which are the typical building block of online data-intensive applications. The explicit goal is to deliver high throughput (millions of remote procedure calls per second) for tail latency service-level objectives that are a small multiple of the task size.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132780"}, {"primary_key": "3866082", "vector": [], "sparse_vector": [], "title": "PebblesDB: Building Key-Value Stores using Fragmented Log-Structured Merge Trees.", "authors": ["Pandian <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Abraham"], "summary": "Key-value stores such as LevelDB and RocksDB offer excellent write throughput, but suffer high write amplification. The write amplification problem is due to the Log-Structured Merge Trees data structure that underlies these key-value stores. To remedy this problem, this paper presents a novel data structure that is inspired by Skip Lists, termed Fragmented Log-Structured Merge Trees (FLSM). FLSM introduces the notion of guards to organize logs, and avoids rewriting data in the same level. We build PebblesDB, a high-performance key-value store, by modifying HyperLevelDB to use the FLSM data structure. We evaluate PebblesDB using micro-benchmarks and show that for write-intensive workloads, PebblesDB reduces write amplification by 2.4-3x compared to RocksDB, while increasing write throughput by 6.7x. We modify two widely-used NoSQL stores, MongoDB and HyperDex, to use PebblesDB as their underlying storage engine. Evaluating these applications using the YCSB benchmark shows that throughput is increased by 18-105% when using PebblesDB (compared to their default storage engines) while write IO is decreased by 35-55%.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132765"}, {"primary_key": "3866083", "vector": [], "sparse_vector": [], "title": "ffwd: delegation is (much) faster than you think.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We revisit the question of delegation vs. synchronized access to shared memory, and show through analysis and demonstration that delegation can be much faster than locking under a range of common circumstances. Starting from first principles, we propose fast, fly-weight delegation (ffwd). The highly optimized design of ffwd allows it to significantly outperform prior work on delegation, while retaining the scalability advantage.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132771"}, {"primary_key": "3866085", "vector": [], "sparse_vector": [], "title": "Optimizing Big-Data Queries Using Program Synthesis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Classical query optimization relies on a predefined set of rewrite rules to re-order and substitute SQL operators at a logical level. This paper proposes Blitz, a system that can synthesize efficient query-specific operators using automated program reasoning. <PERSON><PERSON> uses static analysis to identify sub-queries as potential targets for optimization. For each sub-query, it constructs a template that defines a large space of possible operator implementations, all restricted to have linear time and space complexity. <PERSON><PERSON> then employs program synthesis to instantiate the template and obtain a data-parallel operator implementation that is functionally equivalent to the original sub-query up to a bound on the input size.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132773"}, {"primary_key": "3866086", "vector": [], "sparse_vector": [], "title": "WatchIT: Who Watches Your IT Guy?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "System administrators have unlimited access to system resources. As the <PERSON><PERSON> case highlighted, these permissions can be exploited to steal valuable personal, classified, or commercial data. This problem is exacerbated when a third party administers the system. For example, a bank outsourcing its IT would not want to allow administrators access to the actual data. We propose WatchIT: a strategy that constrains IT personnel's view of the system and monitors their actions. To this end, we introduce the abstraction of perforated containers -- while regular Linux containers are too restrictive to be used by system administrators, by \"punching holes\" in them, we strike a balance between information security and required administrative needs. Following the principle of least privilege, our system predicts which system resources should be accessible for handling each IT issue, creates a perforated container with the corresponding isolation, and deploys it as needed for fixing the problem.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132752"}, {"primary_key": "3866087", "vector": [], "sparse_vector": [], "title": "The Efficient Server Audit Problem, Deduplicated Re-execution, and the Web.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "You put a program on a concurrent server, but you don't trust the server; later, you get a trace of the actual requests that the server received from its clients and the responses that it delivered. You separately get logs from the server; these are untrusted. How can you use the logs to efficiently _verify_ that the responses were derived from running the program on the requests? This is the _Efficient Server Audit Problem_, and it abstracts real-world scenarios, including running a web application on an untrusted provider. We give a solution based on several new techniques, including simultaneous replay and efficient verification of concurrent executions. We implement the solution for PHP web applications. For several applications, our verifier achieves 5.6--10.9x speedup versus simply re-executing, with less than 10 percent overhead for the server.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132760"}, {"primary_key": "3866088", "vector": [], "sparse_vector": [], "title": "LITE Kernel RDMA Support for Datacenter Applications.", "authors": ["Shin<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, there is an increasing interest in building data-center applications with RDMA because of its low-latency, high-throughput, and low-CPU-utilization benefits. However, RDMA is not readily suitable for datacenter applications. It lacks a flexible, high-level abstraction; its performance does not scale; and it does not provide resource sharing or flexible protection. Because of these issues, it is difficult to build RDMA-based applications and to exploit RDMA's performance benefits.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132762"}, {"primary_key": "3866089", "vector": [], "sparse_vector": [], "title": "Stadium: A Distributed Metadata-Private Messaging System.", "authors": ["Nirvan Tyagi", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Private communication over the Internet remains a challenging problem. Even if messages are encrypted, it is hard to deliver them without revealing metadata about which pairs of users are communicating. Scalable anonymity systems, such as Tor, are susceptible to traffic analysis attacks that leak metadata. In contrast, the largest-scale systems with metadata privacy require passing all messages through a small number of providers, requiring a high operational cost for each provider and limiting their deployability in practice.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132783"}, {"primary_key": "3866091", "vector": [], "sparse_vector": [], "title": "Drizzle: Fast and Adaptable Stream Processing at Scale.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Ion <PERSON>"], "summary": "Large scale streaming systems aim to provide high throughput and low latency. They are often used to run mission-critical applications, and must be available 24x7. Thus such systems need to adapt to failures and inherent changes in workloads, with minimal impact on latency and throughput. Unfortunately, existing solutions require operators to choose between achieving low latency during normal operation and incurring minimal impact during adaptation. Continuous operator streaming systems, such as Naiad and Flink, provide low latency during normal execution but incur high overheads during adaptation (e.g., recovery), while micro-batch systems, such as Spark Streaming and FlumeJava, adapt rapidly at the cost of high latency during normal operations.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132750"}, {"primary_key": "3866095", "vector": [], "sparse_vector": [], "title": "NOVA-Fortis: A Fault-Tolerant Non-Volatile Main Memory File System.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tamires Brito Da Silva", "<PERSON>", "<PERSON>"], "summary": "Emerging fast, persistent memories will enable systems that combine conventional DRAM with large amounts of non-volatile main memory (NVMM) and provide huge increases in storage performance. Fully realizing this potential requires fundamental changes in how system software manages, protects, and provides access to data that resides in NVMM. We address these needs by describing an NVMM-optimized file system called NOVA-Fortis that is both fast and resilient in the face of corruption due to media errors and software bugs. We identify and propose solutions for the unique challenges in adding fault tolerance to an NVMM file system, adapt state-of-the-art reliability techniques to an NVMM file system, and quantify the performance and storage overheads of these techniques. We find that NOVA-Fortis' reliability features consume 14.8% of the storage for redundancy and reduce application-level performance by between 2% and 38% compared to the same file system with the features removed. NOVA-Fortis outperforms DAX-aware file systems without reliability features by 1.5x on average. It outperforms reliable, block-based file systems running on NVMM by 3x on average.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132761"}, {"primary_key": "3866096", "vector": [], "sparse_vector": [], "title": "Sub-millisecond Stateful Stream Querying over Fast-evolving Linked Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "Applications like social networking, urban monitoring and market feed processing require stateful stream query: a query consults not only streaming data but also stored data to extract timely information; useful information from streaming data also needs to be continuously and consistently integrated into stored data to serve inflight and future queries. However, prior streaming systems either focus on stream computation, or are not stateful, or cannot provide low latency and high throughput to handle the fast-evolving linked data and increasing concurrency of queries.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132777"}, {"primary_key": "3866097", "vector": [], "sparse_vector": [], "title": "Pensieve: Non-Intrusive Failure Reproduction for Distributed Systems using the Event Chaining Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>g <PERSON>"], "summary": "Complex and unforeseen failures in distributed systems must be diagnosed and replicated in a development environment so that developers can understand the underlying problem and verify the resolution. System logs often form the only source of diagnostic information, and developers reconstruct a failure using manual guesswork. This is an unpredictable and time-consuming process which can lead to costly service outages while a failure is repaired.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132768"}, {"primary_key": "3866098", "vector": [], "sparse_vector": [], "title": "Log20: Fully Automated Optimal Placement of Log Printing Statements under Specified Overhead Threshold.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>g <PERSON>", "<PERSON><PERSON>"], "summary": "When systems fail in production environments, log data is often the only information available to programmers for postmortem debugging. Consequently, programmers' decision on where to place a log printing statement is of crucial importance, as it directly affects how effective and efficient postmortem debugging can be. This paper presents Log20, a tool that determines a near optimal placement of log printing statements under the constraint of adding less than a specified amount of performance overhead. Log20 does this in an automated way without any human involvement. Guided by information theory, the core of our algorithm measures how effective each log printing statement is in disambiguating code paths. To do so, it uses the frequencies of different execution paths that are collected from a production environment by a low-overhead tracing library. We evaluated Log20 on HDFS, HBase, Cassandra, and ZooKeeper, and observed that Log20 is substantially more efficient in code path disambiguation compared to the developers' manually placed log printing statements. Log20 can also output a curve showing the trade-off between the informativeness of the logs and the performance slowdown, so that a developer can choose the right balance.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/3132747.3132778"}, {"primary_key": "3885131", "vector": [], "sparse_vector": [], "title": "Proceedings of the 26th Symposium on Operating Systems Principles, Shanghai, China, October 28-31, 2017", "authors": [], "summary": "Welcome to the Proceedings of the 26th ACM Symposium on Operating Systems Principles (SOSP 2017), held at the Pudong Shangri-La Hotel in Shanghai, China. This is the first SOSP to take place outside of North America and Europe, reflecting the global community of researchers in software systems.", "published": "2017-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": ""}]