[{"primary_key": "690219", "vector": [], "sparse_vector": [], "title": "Fast American Option Pricing using Nonlinear Stencils.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the binomial, trinomial, and Black-Scholes-Merton models of option pricing. We present fast parallel discrete-time finite-difference algorithms for American call option pricing under the binomial and trinomial models and American put option pricing under the Black-Scholes-Merton model. For T-step finite differences, each algorithm runs in O (T log2 T)/p + T) time under a greedy scheduler on p processing cores, which is a significant improvement over the Θ (T2/p) + Ω (T log T) time taken by the corresponding state-of-the-art parallel algorithm. Even when run on a single core, the O (T log2 T) time taken by our algorithms is asymptotically much smaller than the Θ (T2) running time of the fastest known serial algorithms. Implementations of our algorithms significantly outperform the fastest implementations of existing algorithms in practice, e.g., when run for T ≈ 1000 steps on a 48-core machine, our algorithm for the binomial model runs at least 15× faster than the fastest existing parallel program for the same model with the speedup factor gradually reaching beyond 500× for T ≈ 0.5 × 106. It saves more than 80% energy when T ≈ 4000, and more than 99% energy for T > 60,000.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638506"}, {"primary_key": "690220", "vector": [], "sparse_vector": [], "title": "Memory Bounds for Concurrent Bounded Queues.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Concurrent data structures often require additional memory for handling synchronization issues in addition to memory for storing elements. Depending on the amount of this additional memory, implementations can be more or less memory-friendly. A memory-optimal implementation enjoys the minimal possible memory overhead, which, in practice, reduces cache misses and unnecessary memory reclamation.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638497"}, {"primary_key": "690221", "vector": [], "sparse_vector": [], "title": "Recurrence Analysis for Automatic Parallelization of Subscripted Subscripts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Introducing correct and optimal OpenMP parallelization directives in applications is a challenge. To parallelize a loop in an input application code automatically, parallelizing compilers need to disprove dependences with respect to variables across iterations of the loop. Performing such dependence analysis in the presence of index arrays or subscripted subscripts - a[b[i]] - has long been a challenge for automatic parallelizers. Loops with subscripted subscripts can be parallelized if the subscript array is known to possess a property such as monotonicity. This paper presents a compiletime algorithm that can analyze complex recurrence relations and determine irregular or intermittent monotonicity of one-dimensional and monotonicity of multi-dimensional subscript arrays. The new algorithm builds on a prior approach that is capable of analyzing simple recurrence relations and determining monotonic one-dimensional subscript arrays. Experimental results show that automatic parallelizers equipped with our new analysis techniques can substantially improve the performance of ten out of twelve or 83.33% of the benchmarks evaluated, 25--33.33% more than possible with state-of-the-art compile-time automatic parallelization techniques.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638493"}, {"primary_key": "690222", "vector": [], "sparse_vector": [], "title": "VERLIB: Concurrent Versioned Pointers.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Recent work has shown how to augment any CAS-based concurrent data structure to support taking a snapshot of the current memory state. Taking the snapshot, as well as loads and CAS (Compare and Swap) operations, take constant time. Importantly, such snapshotting can be used to easily implement linearizable queries, such as range queries, over any part of a data structure.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638501"}, {"primary_key": "690223", "vector": [], "sparse_vector": [], "title": "ConvStencil: Transform Stencil Computation to Matrix Multiplication on Tensor Cores.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>lin Bai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Tensor Core Unit (TCU) is increasingly integrated into modern high-performance processors to enhance matrix multiplication performance. However, constrained to its over-specification, its potential for improving other critical scientific operations like stencil computations remains untapped. This paper presents ConvStencil1, a novel stencil computing system designed to efficiently transform stencil computation to matrix multiplication on Tensor Cores. We first develop a performance model for ConvStencil to guide algorithm design and optimization on TCUs. Based on this model, we propose three techniques: (1) Memory-efficient Layout Transformation using the stencil2row method; (2) Computation-dense Compute Adaptation with Dual Tessellation and kernel fusion; and (3) Performance-boosting Conflict Removal using a Lookup Table and Dirty Bits Padding. ConvStencil outperforms other stencil optimization frameworks, achieving significant speedups compared to solutions like AMOS, cuDNN, Brick, DRStencil, and TCStencil. By transforming stencil computation on Tensor Cores, ConvStencil promises to improve the performance of various scientific and engineering applications.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638476"}, {"primary_key": "690224", "vector": [], "sparse_vector": [], "title": "FastFold: Optimizing AlphaFold Training and Inference on GPU Clusters.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Guangyang Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Wu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Protein structure prediction helps to understand gene translation and protein function, which is of growing interest and importance in structural biology. The AlphaFold model, which used transformer architecture to achieve atomic-level accuracy in protein structure prediction, was a significant breakthrough. However, training and inference of AlphaFold model are challenging due to its high computation and memory cost. In this work, we present FastFold, an efficient implementation of AlphaFold for both training and inference. We propose Dynamic Axial Parallelism (DAP) as a novel model parallelism method. Additionally, we have implemented a series of low-level optimizations aimed at reducing communication, computation, and memory costs. These optimizations include Duality Async Operations, highly optimized kernels, and AutoChunk (an automated search algorithm finds the best chunk strategy to reduce memory peaks). Experimental results show that FastFold can efficiently scale to more GPUs using DAP and reduces overall training time from 11 days to 67 hours and achieves 7.5 ~ 9.5× speedup for long-sequence inference. Furthermore, AutoChunk can reduce memory cost by over 80% during inference by automatically partitioning the intermediate tensors during the computation.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638465"}, {"primary_key": "690225", "vector": [], "sparse_vector": [], "title": "Shared Memory-contention-aware Concurrent DNN Execution for Diversely Heterogeneous System-on-Chips.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Two distinguishing features of state-of-the-art mobile and autonomous systems are: 1) There are often multiple workloads, mainly deep neural network (DNN) inference, running concurrently and continuously.2) They operate on shared memory System-on-Chips (SoC) that embed heterogeneous accelerators tailored for specific operations.State-of-the-art systems lack efficient performance and resource management techniques necessary to either maximize total system throughput or minimize end-to-end workload latency.In this work, we propose HaX-CoNN, a novel scheme that characterizes and maps layers in concurrently executing DNN inference workloads to a diverse set of accelerators within an SoC.Our scheme uniquely takes per-layer execution characteristics, shared memory (SM) contention, and inter-accelerator transitions into account to find optimal schedules.We evaluate HaX-CoNN on NVIDIA Orin, NVIDIA Xavier, and Qualcomm Snapdragon 865 SoCs.Our experimental results indicate that HaX-CoNN can minimize memory contention by up to 45% and improve total latency and throughput by up to 32% and 29%, respectively, compared to the state-of-the-art.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638502"}, {"primary_key": "690226", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Integer Sort: Theory and Practice.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Integer sorting is a fundamental problem in computer science. This paper studies parallel integer sort both in theory and in practice. In theory, we show tighter bounds for a class of existing practical integer sort algorithms, which provides a solid theoretical foundation for their widespread usage in practice and strong performance. In practice, we design a new integer sorting algorithm, DovetailSort, that is theoretically-efficient and has good practical performance.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638483"}, {"primary_key": "690227", "vector": [], "sparse_vector": [], "title": "Liger: Interleaving Intra- and Inter-Operator Parallelism for Distributed Large Model Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yutong Lu"], "summary": "Distributed large model inference is still in a dilemma where balancing cost and effect. The online scenarios demand intraoperator parallelism to achieve low latency and intensive communications makes it costly. Conversely, the inter-operator parallelism can achieve high throughput with much fewer communications, but it fails to enhance the effectiveness.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638466"}, {"primary_key": "690228", "vector": [], "sparse_vector": [], "title": "POSTER: Optimizing Sparse Tensor Contraction with Revisiting Hash Table Design.", "authors": ["<PERSON><PERSON> Feng", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON> Tan", "<PERSON><PERSON><PERSON>"], "summary": "Sparse tensor contraction (SpTC) serves as an essential operation in high-performance applications. The high dimensionality of sparse tensors makes SpTC fundamentally challenging in aspects such as costly multidimensional index search, extensive intermediate output data, and indirect addressing. Previous state-of-the-art work addresses some of these challenges through hash-table implementation. In this paper, we propose a hash-table based and fully optimized SpTC by providing a more carefully designed customized hash table design, proposing an architecture-aware algorithm for hash table selection with size prediction, applying cross-stage optimizations to exploit shared information and avoid redundant operations. Evaluating on a set of tensors extracted from the real world, our method can achieve superior speedup and reduce the memory footprint substantially compared to the current state-of-the-art work.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638500"}, {"primary_key": "690229", "vector": [], "sparse_vector": [], "title": "POSTER: Enabling Extreme-Scale Phase Field Simulation with In-situ Feature Extraction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "S<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present an integrated framework composed of a highly efficient phase field simulator and an in-situ feature extraction library. This novel framework enables us to conduct extreme-scale micro-structure evolution simulations while the characteristic features of each individual grain are extracted on the fly. After systematic design and optimization on the new generation Sunway supercomputer, the code scales up to 39 million cores and achieves 582 PFlops in double precision and 637 POps in mixed precision.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638486"}, {"primary_key": "690230", "vector": [], "sparse_vector": [], "title": "GraphCube: Interconnection Hierarchy-aware Graph Processing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Processing large-scale graphs with billions to trillions of edges requires efficiently utilizing parallel systems. However, current graph processing engines do not scale well beyond a few tens of computing nodes because they are oblivious to the communication cost variations across the interconnection hierarchy. We introduce GraphCube, a better approach to optimizing graph processing on large-scale parallel systems with complex interconnections. GraphCube features a new graph partitioning approach to achieve better load balancing and minimize communication overhead across multiple levels of the interconnection hierarchy. We evaluate GraphCube by applying it to fundamental graph operations performed on synthetic and real-world graph datasets. Our evaluation used up to 79,024 computing nodes and 1.2+ million processor cores. Our large-scale experiments show that GraphCube outperforms state-of-the-art parallel graph processing methods in throughput and scalability. Furthermore, GraphCube outperformed the top-ranked systems on the Graph 500 list.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638498"}, {"primary_key": "690231", "vector": [], "sparse_vector": [], "title": "Arrow Matrix Decomposition: A Novel Approach for Communication-Efficient Sparse Matrix Multiplication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Armon Carigiet", "Chio Ge", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a novel approach to iterated sparse matrix dense matrix multiplication, a fundamental computational kernel in scientific computing and graph neural network training. In cases where matrix sizes exceed the memory of a single compute node, data transfer becomes a bottleneck. An approach based on dense matrix multiplication algorithms leads to sub-optimal scalability and fails to exploit the sparsity in the problem. To address these challenges, we propose decomposing the sparse matrix into a small number of highly structured matrices called arrow matrices, which are connected by permutations. Our approach enables communication-avoiding multiplications, achieving a polynomial reduction in communication volume per iteration for matrices corresponding to planar graphs and other minor-excluded families of graphs. Our evaluation demonstrates that our approach outperforms a state-of-the-art method for sparse matrix multiplication on matrices with hundreds of millions of rows, offering near-linear strong and weak scaling.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638496"}, {"primary_key": "690232", "vector": [], "sparse_vector": [], "title": "POSTER: Pattern-Aware Sparse Communication for Scalable Recommendation Model Training.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jidong Zhai"], "summary": "Recommendation models are an important category of deep learning models whose size is growing enormous. They consist of a sparse part with TBs of memory footprint and a dense part that demands PFLOPs of computing capability to train. Unfortunately, the high sparse communication cost to re-organize data for different parallel strategies of the two parts impedes the scalability in training.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638481"}, {"primary_key": "690233", "vector": [], "sparse_vector": [], "title": "Training one DeePMD Model in Minutes: a Step towards Online Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Qiuchen Sha", "<PERSON><PERSON>", "Xiangyu Meng", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Tan", "<PERSON><PERSON>"], "summary": "Neural Network Molecular Dynamics (NNMD) has become a major approach in material simulations, which can speedup the molecular dynamics (MD) simulation for thousands of times, while maintaining ab initio accuracy, thus has a potential to fundamentally change the paradigm of material simulations. However, there are two time-consuming bottlenecks of the NNMD developments. One is the data access of ab initio calculation results. The other, which is the focus of the current work, is reducing the training time of NNMD model. The training of NNMD model is different from most other neural network training because the atomic force (which is related to the gradient of the network) is an important physical property to be fit. Tests show the traditional stochastic gradient methods, like the Adam algorithms, cannot efficiently deploy the multisample minibatch algorithm. As a result, a typical training (taking the Deep Potential Molecular Dynamics (DeePMD) as an example) can take many hours. In this work, we designed a heuristic minibatch quasi-Newtonian optimizer based on Extended Kalman Filter method. An early reduction of gradient and error is adopted to reduce memory footprint and communication. The memory footprint, communication and settings of hyper-parameters of this new method are analyzed in detail. Computational innovations such as customized kernels of the symmetry-preserving descriptor are applied to exploit the computing power of the heterogeneous architecture. Experiments are performed on 8 different datasets representing different real case situations, and numerical results show that our new method has an average speedup of 32.2 compared to the Reorganized Layer-wised Extended Kalman Filter with 1 GPU, reducing the absolute training time of one DeePMD model from hours to several minutes, making it one step toward online training.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638505"}, {"primary_key": "690234", "vector": [], "sparse_vector": [], "title": "POSTER: Optimizing Collective Communications with Error-bounded Lossy Compression for GPU Clusters.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Yu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "GPU-aware collective communication has become a major bottleneck for modern computing platforms as GPU computing power rapidly rises. To address this issue, traditional approaches integrate lossy compression directly into GPU-aware collectives, which still suffer from serious issues such as underutilized GPU devices and uncontrolled data distortion. In this paper, we propose GPU-LCC, a general framework that designs and optimizes GPU-aware, compression-enabled collectives with well-controlled error propagation. To validate our framework, we evaluate the performance on up to 64 NVIDIA A100 GPUs with real-world applications and datasets. Experimental results demonstrate that our GPU-LCC-accelerated collective computation (Allreduce), can outperform NCCL as well as Cray MPI by up to 3.4× and 18.7×, respectively. Furthermore, our accuracy evaluation with an image-stacking application confirms the high reconstructed data quality of our accuracy-aware framework.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638467"}, {"primary_key": "690235", "vector": [], "sparse_vector": [], "title": "OsirisBFT: Say No to Task Replication for Scalable Byzantine Fault Tolerant Analytics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a verification-based Byzantine Fault Tolerant processing system, called OsirisBFT, for distributed task-parallel applications. OsirisBFT treats computation tasks differently from state update tasks, allowing the application to scale independently from number of expected failures. OsirisBFT captures application-specific verification semantics via generic verification operators and employs lightweight verification strategies with little coordination during graceful execution. Evaluation across multiple applications and workloads shows that OsirisBFT delivers high processing throughput and scalability compared to replicated processing. Importantly, the scalable nature of OsirisBFT enables it to reduce the performance gap compared to baseline with no fault tolerance by simply scaling out.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638468"}, {"primary_key": "690236", "vector": [], "sparse_vector": [], "title": "Fast Kronecker Matrix-Matrix Multiplication on GPUs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Kronecker Matrix-Matrix Multiplication (Kron-Matmul) is the multiplication of a matrix with the Kronecker Product of several smaller matrices. Kron-Matmul is a core operation for many scientific and machine learning computations. State-of-the-art Kron-Matmul implementations utilize existing tensor algebra operations, such as matrix multiplication, transpose, and tensor matrix multiplication. However, this design choice prevents several Kron-Matmul specific optimizations, thus, leaving significant performance on the table.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638489"}, {"primary_key": "690237", "vector": [], "sparse_vector": [], "title": "POSTER: Accelerating High-Precision Integer Multiplication used in Cryptosystems with GPUs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "High-precision integer multiplication is crucial in privacy-preserving computational techniques but poses acceleration challenges on GPUs due to its complexity and the diverse bit lengths in cryptosystems. This paper introduces GIM, an efficient high-precision integer multiplication algorithm accelerated with GPUs. It employs a novel segmented integer multiplication algorithm that separates implementation details from bit length, facilitating code optimizations. We also present a computation diagram to analyze parallelization strategies, leading to a series of enhancements. Experiments demonstrate that this approach achieves a 4.47× speedup over the commonly used baseline.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638495"}, {"primary_key": "690238", "vector": [], "sparse_vector": [], "title": "Practical Hardware Transactional vEB Trees.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "van Emde Boas (vEB) trees are sequential data structures optimized for extremely fast predecessor and successor queries. Such queries are an important incentive to use ordered sets or maps such as vEB trees. All operations in a vEB tree are doubly logarithmic in the universe size. Attempts to implement concurrent vEB trees have either simplified their structure in a way that eliminated their ability to perform fast predecessor and successor queries, or have otherwise compromised on doubly logarithmic complexity. In this work, we leverage Hardware Transactional Memory (HTM) to implement vEB tree-based sets and maps in which operations are doubly logarithmic in the absence of contention. Our proposed concurrent vEB tree is the first to implement recursive summaries, the key algorithmic component of fast predecessor and successor operations. Through extensive experiments, we demonstrate that our algorithm outperforms state-of-the-art concurrent maps by an average of 5× in a moderately skewed workload, and the single-threaded C++ standard ordered map and its unordered map by 70% and 14%, respectively. And, it does so while using two orders of magnitude less memory than traditional vEB trees.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638504"}, {"primary_key": "690239", "vector": [], "sparse_vector": [], "title": "Are Your Epochs Too Epic? Batch Free Can Be Harmful.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Epoch based memory reclamation (EBR) is one of the most popular techniques for reclaiming memory in lock-free and optimistic locking data structures, due to its ease of use and good performance in practice. However, EBR is known to be sensitive to thread delays, which can result in performance degradation. Moreover, the exact mechanism for this performance degradation is not well understood.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638491"}, {"primary_key": "690241", "vector": [], "sparse_vector": [], "title": "POSTER: ParGNN: Efficient Training for Large-Scale Graph Neural Network on GPU Clusters.", "authors": ["<PERSON>nde Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Full-batch graph neural network (GNN) training is essential for interdisciplinary applications. Large-scale graph data is usually divided into subgraphs and distributed across multiple compute units to train GNN. The state-of-the-art load balancing method based on direct graph partition is too rough to effectively achieve true load balancing on GPU clusters. We propose ParGNN, which employs a profiler-guided load balance workflow in conjunction with graph repartition to alleviate load imbalance and minimize communication traffic. Experiments have verified that ParGNN has the capability to scale to larger clusters.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638488"}, {"primary_key": "690242", "vector": [], "sparse_vector": [], "title": "POSTER: RadiK: Scalable Radix Top-K Selection on GPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "By identifying the k largest or smallest elements in a set of data, top-k selection is critical for modern high-performance databases and machine learning systems, especially with large data volumes. However, previous studies on its GPU implementation are mostly merge-based and rely heavily on the high-speed but size-limited on-chip memory, thereby resulting in a restricted upper bound on k. This paper introduces RadiK, a highly optimized GPU-parallel radix top-k selection that is scalable with k, input length, and batch size. With a carefully designed optimization framework targeting high memory bandwidth and resource utilization, RadiK supports far larger k than the prior art, achieving up to 2.5× speedup for non-batch queries and up to 4.8× speedup for batch queries. We also propose a lightweight refinement that strengthens the robustness of RadiK against skewed distributions by adaptively scaling the input elements.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638478"}, {"primary_key": "690243", "vector": [], "sparse_vector": [], "title": "Exploiting Fine-Grained Redundancy in Set-Centric Graph Pattern Mining.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Chaoyang Shui", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Tan"], "summary": "Graph Pattern Mining (GPM) applications are memory intensive as they require a tremendous amount of edge checks. In recent years, the \"set-centric\" abstraction has gained attention for its powerful expressive abilities. By leveraging relational algebra, they optimized algorithms with methods like matching orders, early termination, automorphism-breaking, and result reuse to reduce redundancy. However, these approaches primarily address coarse-grained redundancy from exactly the same set formulas, neglecting that the data graph's inherent locality may lead to fine-grained duplicated edge checks. In fact, even unrelated set operations may check the same pair of vertices. This paper introduces the set union operation to the set-centric abstraction to fuse duplicated edge checks into one. It maintains the expressive power of relational algebra and previous optimizations while effectively avoids fine-grained redundancy in GPM tasks. Compared to state-of-the-art methods, our method achieves significant speedup on a V100 GPU cluster, demonstrating up to 305 × faster performance than the state-of-the-art GPM system G2Miner.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638507"}, {"primary_key": "690244", "vector": [], "sparse_vector": [], "title": "Parallel k-Core Decomposition with Batched Updates and Asynchronous Reads.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Maintaining a dynamic k-core decomposition is an important problem that identifies dense subgraphs in dynamically changing graphs. Recent work by <PERSON> et al. [SPAA 2022] presents a parallel batch-dynamic algorithm for maintaining an approximate k-core decomposition. In their solution, both reads and updates need to be batched, and therefore each type of operation can incur high latency waiting for the other type to finish. To tackle most real-world workloads, which are dominated by reads, this paper presents a novel hybrid concurrent-parallel dynamic k-core data structure where asynchronous reads can proceed concurrently with batches of updates, leading to significantly lower read latencies. Our approach is based on tracking causal dependencies between updates, so that causally related groups of updates appear atomic to concurrent readers. Our data structure guarantees linearizability and liveness for both reads and updates, and maintains the same approximation guarantees as prior work. Our experimental evaluation on a 30-core machine shows that our approach reduces read latency by orders of magnitude compared to the batch-dynamic algorithm, up to a (4.05 · 105)-factor. Compared to an unsynchronized (non-linearizable) baseline, our read latency overhead is only up to a 3.21-factor greater, while improving accuracy of coreness estimates by up to a factor of 52.7.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638508"}, {"primary_key": "690245", "vector": [], "sparse_vector": [], "title": "Tetris: Accelerating Sparse Convolution by Exploiting Memory Reuse on GPU.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhong<PERSON> Luan", "<PERSON><PERSON><PERSON>"], "summary": "Convolutional neural networks (CNNs) have achieved remarkable success in various application fields. Although model compression techniques mitigate the ever-increasing resource demands of large CNN models, the compressed models usually exhibit irregular memory access and unstructured sparsity, which are difficult for dominant operators such as sparse convolution to achieve expected performance speedup on popular inference platforms such as GPU. In this paper, we propose Tetris, an efficient sparse convolution approach optimized for GPU. Tetris first fully exploits the input reuse opportunity of sparse convolution to reduce the memory accesses to global memory. It then adopts a stride packed filter (SPF) format and a bank-sensing reorganization scheme to eliminate the irregular memory accesses caused by unstructured sparsity. It also leverages a filter group reorder technique to address load imbalance among threads, and a parameter tuning method to determine the optimal parameters of the sparse convolution implementation. The experiment results show that Tetris outperforms dense/sparse convolution libraries and cutting-edge implementations with promising performance speedup.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638471"}, {"primary_key": "690248", "vector": [], "sparse_vector": [], "title": "POSTER: FineCo: Fine-grained Heterogeneous Resource Management for Concurrent DNN Inferences.", "authors": ["Lixian Ma", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Tan"], "summary": "Co-locating multiple DNN servings to share GPU resource is widely used to improve resource utilization while guaranteeing user QoS. Existing GPU sharing mechanism is restricted to model level, and fluctuations in kernel-level resource demands highlight a suboptimal utilization of the current sharing mechanism. We design a multi-DNN serving system, FineCo, that leverages a novel fine-grained resource sharing mechanism to optimize concurrent inference without modifications to the hardware or operating system. Our prototype implementation demonstrates that FineCo achieves up to 40% throughput improvement over the state-of-the-art work.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638485"}, {"primary_key": "690249", "vector": [], "sparse_vector": [], "title": "ParlayANN: Scalable and Deterministic Parallel Graph-Based Approximate Nearest Neighbor Search Algorithms.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Approximate nearest-neighbor search (ANNS) algorithms are a key part of the modern deep learning stack due to enabling efficient similarity search over high-dimensional vector space representations (i.e., embeddings) of data. Among various ANNS algorithms, graph-based algorithms are known to achieve the best throughput-recall tradeoffs. Despite the large scale of modern ANNS datasets, existing parallel graph-based implementations suffer from significant challenges to scale to large datasets due to heavy use of locks and other sequential bottlenecks, which 1) prevents them from efficiently scaling to a large number of processors, and 2) results in non-determinism that is undesirable in certain applications.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638475"}, {"primary_key": "690250", "vector": [], "sparse_vector": [], "title": "Gallatin: A General-Purpose GPU Memory Manager.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dynamic memory management is critical for efficiently porting modern data processing pipelines to GPUs. However, building a general-purpose dynamic memory manager on GPUs is challenging due to the massive parallelism and weak memory coherence. Existing state-of-the-art GPU memory managers, Ouroboros and Reg-Eff, employ traditional data structures such as arrays and linked lists to manage memory objects. They build specialized pipelines to achieve performance for a fixed set of allocation sizes and fall back to the CUDA allocator for allocating large sizes. In the process, they lose general-purpose usability and fail to support critical applications such as streaming graph processing.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638499"}, {"primary_key": "690252", "vector": [], "sparse_vector": [], "title": "POSTER: OCToPus: Semantic-aware Concurrency Control for Blockchain Transactions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many blockchain implementations offer APIs to send and receive money between accounts exclusively. In this paper, we introduce OCToPus, a deterministic concurrency control scheme that uses a semantic-aware fast path and a GPU-accelerated directed acyclic graph-based fallback path to parallelize the execution of a block aggressively.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638494"}, {"primary_key": "690253", "vector": [], "sparse_vector": [], "title": "Language-Agnostic Static Deadlock Detection for Futures.", "authors": ["<PERSON>"], "summary": "Deadlocks, in which threads wait on each other in a cyclic fashion and can't make progress, have plagued parallel programs for decades. In recent years, as the parallel programming mechanism known as futures has gained popularity, interest in preventing deadlocks in programs with futures has increased as well. Various static and dynamic algorithms exist to detect and prevent deadlock in programs with futures, generally by constructing some approximation of the dependency graph of the program but, as far as we are aware, all are specialized to a particular programming language.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638487"}, {"primary_key": "690254", "vector": [], "sparse_vector": [], "title": "A Row Decomposition-based Approach for Sparse Matrix Multiplication on GPUs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sparse-Matrix Dense-Matrix Multiplication (SpMM) and Sampled Dense Dense Matrix Multiplication (SDDMM) are important sparse kernels in various computation domains. The uneven distribution of nonzeros in the sparse matrix and the tight data dependence between sparse and dense matrices make it a challenge to run sparse matrix multiplication efficiently on GPUs. By analyzing the aforementioned problems, we propose a row decomposition (RoDe)-based approach to optimize the two kernels on GPUs, using the standard Compressed Sparse Row (CSR) format. Specifically, RoDe divides the sparse matrix rows into regular parts and residual parts, to fully optimize their computations separately. We also devise the corresponding load balancing and finegrained pipelining technologies. Profiling results show that RoDe can achieve more efficient memory access and reduce warp stall cycles significantly. Compared to the state-of-the-art (SOTA) alternatives, RoDe achieves a speedup of up to 7.86× with a geometric mean of 1.45× for SpMM, and a speedup of up to 8.99× with a geometric mean of 1.49× for SDDMM; the dataset is SuiteSparse. RoDe also outperforms its counterpart in the deep learning dataset. Furthermore, its preprocessing overhead is significantly smaller, averaging only 16% of the SOTA.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638470"}, {"primary_key": "690255", "vector": [], "sparse_vector": [], "title": "Locks as a Resource: <PERSON><PERSON>uling Lock Occupation with CFL.", "authors": ["Jonggyu Park", "Young Ik Eom"], "summary": "In multi-container environments, applications oftentimes experience unexpected performance fluctuations due to undesirable interference among applications. Synchronization such as locks has been targeted as one of the reasons but still remains an uncontrolled resource while a large set of locks are still shared across applications. In this paper, we demonstrate that this lack of lock scheduling incurs significant real-world problems including performance unfairness and interference among applications. To address this problem, we propose a new synchronization design with an embedded scheduling capability, called CFL (Completely Fair Locking). CFL fairly distributes a fair amount of lock occupation time to applications considering their priorities and cgroup information. For scalability, CFL also considers the NUMA topology in the case of NUMA machines. Experimental results demonstrate that CFL significantly improves performance fairness while achieving comparable or sometimes even superior performance to state-of-the-art locks.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638477"}, {"primary_key": "690256", "vector": [], "sparse_vector": [], "title": "AGAThA: Fast and Efficient GPU Acceleration of Guided Sequence Alignment for Long Read Mapping.", "authors": ["Seongyeon Park", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the advance in genome sequencing technology, the lengths of deoxyribonucleic acid (DNA) sequencing results are rapidly increasing at lower prices than ever. However, the longer lengths come at the cost of a heavy computational burden on aligning them. For example, aligning sequences to a human reference genome can take tens or even hundreds of hours. The current de facto standard approach for alignment is based on the guided dynamic programming method. Although this takes a long time and could potentially benefit from high-throughput graphic processing units (GPUs), the existing GPU-accelerated approaches often compromise the algorithm's structure, due to the GPU-unfriendly nature of the computational pattern. Unfortunately, such compromise in the algorithm is not tolerable in the field, because sequence alignment is a part of complicated bioinformatics analysis pipelines. In such circumstances, we propose AGAThA, an exact and efficient GPU-based acceleration of guided sequence alignment. We diagnose and address the problems of the algorithm being unfriendly to GPUs, which comprises strided/redundant memory accesses and workload imbalances that are difficult to predict. According to the experiments on modern GPUs, AGAThA achieves 18.8× speedup against the CPU-based baseline, 9.6× against the best GPU-based baseline, and 3.6× against GPU-based algorithms with different heuristics.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638474"}, {"primary_key": "690257", "vector": [], "sparse_vector": [], "title": "INFINEL: An efficient GPU-based processing method for unpredictable large output graph queries.", "authors": ["Sungwoo Park", "<PERSON><PERSON><PERSON> Oh", "<PERSON><PERSON><PERSON>"], "summary": "With the introduction of GPUs, which are specialized for iterative parallel computations, the execution of computation-intensive graph queries using a GPU has seen significant performance improvements. However, due to the memory constraints of GPUs, there has been limited research on handling large-scale output graph queries with unpredictable output sizes on a GPU. Traditionally, two-phase methods have been used, where the query is re-executed after splitting it into sub-tasks while only considering the size of the output in a static manner. However, two-phase methods become highly inefficient when used with graph data with extreme skew, failing to maximize the GPU performance. This paper proposes INFINEL, which handles unpredictable large output graph queries in a one-phase method through chunk allocation per thread and kernel stop/restart methods. We also propose applicable optimization techniques due to the corresponding unique characteristics of operating with low time/space overhead and not heavily relying on the GPU output buffer size. Through extensive experiments, we demonstrate that our one-phase method of INFINEL improves the performance by up to 31.5 times over the conventional two-phase methods for triangle listing ULO query.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638490"}, {"primary_key": "690258", "vector": [], "sparse_vector": [], "title": "Pure: Evolving Message Passing To Better Leverage Shared Memory Within Nodes.", "authors": ["<PERSON>", "<PERSON><PERSON>-<PERSON>"], "summary": "Pure is a new programming model and runtime system explicitly designed to take advantage of shared memory within nodes in the context of a mostly message passing interface enhanced with the ability to use tasks to make use of idle cores. Pure leverages shared memory in two ways: (a) by allowing cores to steal work from each other while waiting on messages to arrive, and, (b) by leveraging efficient lock-free data structures in shared memory to achieve high-performance messaging and collective operations between the ranks within nodes. We use microbenchmarks to evaluate Pure's key messaging and collective features and also show application speedups up to 2.1× on the CoMD molecular dynamics and the miniAMR adaptive mesh refinement applications scaling up to 4,096 cores.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638503"}, {"primary_key": "690259", "vector": [], "sparse_vector": [], "title": "Towards Scalable Unstructured Mesh Computations on Shared Memory Many-Cores.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Wang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to data conflicts or data dependences, exploiting shared memory parallelism on unstructured mesh applications is highly challenging. The prior approaches are neither general nor scalable on emerging many-core processors. This paper presents a general and scalable shared memory approach for unstructured mesh computations. We recursively divide and reorder an unstructured mesh to construct a task dependency tree (TDT), where massive parallelism is exposed and data conflicts as well as data dependences are respected. We propose two recursion strategies to support popular programming models on both CPUs and GPUs for TDT. We evaluate our approach by applying it to an industrial unstructured Computational Fluid Dynamics (CFD) software. Experimental results show that our approach significantly outperforms the prior shared memory approaches, delivering up to 8.1× performance improvement over the engineer-tuned implementations.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638473"}, {"primary_key": "690260", "vector": [], "sparse_vector": [], "title": "Scaling Up Transactions with Slower Clocks.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Concurrency controls with optimistic read accesses and pessimistic write accesses are among the fastest in the literature. However, during write transactions these algorithms need to increment an atomic variable, the central clock, limiting parallelism and preventing scalability at high core counts.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638472"}, {"primary_key": "690262", "vector": [], "sparse_vector": [], "title": "Sparsity in Deep Neural Nets (Keynote).", "authors": ["<PERSON><PERSON>"], "summary": "Our brain executes very sparse computation, allowing for great speed and energy savings. Deep neural networks can also be made to exhibit high levels of sparsity without significant accuracy loss. As their size grows, it is becoming imperative that we use sparsity to improve their efficiency. This is a challenging task because the memory systems and SIMD operations that dominate todays CPUs and GPUs do not lend themselves easily to the irregular data patterns sparsity introduces. This talk will survey the role of sparsity in neural network computation, and the parallel algorithms and hardware features that nevertheless allow us to make effective use of it.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638568"}, {"primary_key": "690263", "vector": [], "sparse_vector": [], "title": "CPMA: An Efficient Batch-Parallel Compressed Set Without Pointers.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces the batch-parallel Compressed Packed Memory Array (CPMA), a compressed, dynamic, ordered set data structure based on the Packed Memory Array (PMA). Traditionally, batch-parallel sets are built on pointer-based data structures such as trees because pointer-based structures enable fast parallel unions via pointer manipulation. When compared with cache-optimized trees, PMAs were slower to update but faster to scan.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638492"}, {"primary_key": "690265", "vector": [], "sparse_vector": [], "title": "Extreme-scale Direct Numerical Simulation of Incompressible Turbulence on the Heterogeneous Many-core System.", "authors": ["<PERSON><PERSON><PERSON>", "Guangnan Feng", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Yutong Lu"], "summary": "Direct numerical simulation (DNS) is a technique that directly solves the fluid Navier-Stokes equations with high spatial and temporal resolutions, which has driven much research regarding the nature of turbulence. For high-Reynolds number (Re) incompressible turbulence of particular interest, where the nondimensional Re characterizes the flow regime, the application of DNS is hindered by the fact that the numerical grid size (i.e., the memory requirement) scales with Re3, while the overall computational cost scales with Re4. Recent studies have shown that developing efficient parallel methods for heterogeneous many-core systems is promising to solve this computational challenge.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638479"}, {"primary_key": "690266", "vector": [], "sparse_vector": [], "title": "A Holistic Approach to Automatic Mixed-Precision Code Generation and Tuning for Affine Programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Reducing floating-point (FP) precision is used to trade the quality degradation of a numerical program's output for performance, but this optimization coincides with type casting, whose overhead is undisclosed until a mixed-precision code version is generated. This uncertainty enforces the decoupled implementation of mixed-precision code generation and autotuning in prior work. In this paper, we present a holistic approach called PrecTuner that consolidates the mixed-precision code generator and the autotuner by defining one parameter. This parameter is first initialized by some automatically sampled values and used to generate several code variants, with various loop transformations also taken into account. The generated code variants are next profiled to solve a performance model formulated using the aforementioned parameter, possibly under a pre-defined quality degradation budget. The best-performing value of the defined parameter is finally predicted without evaluating all code variants. Experimental results of the PolyBench benchmarks on CPU demonstrate that PrecTuner outperforms LuIs by 3.28× while achieving smaller errors, and we also validate its effectiveness in optimizing a real-life large-scale application. In addition, PrecTuner also obtains a mean speedup of 1.81× and 1.52×-1.73× over Pluto on single- and multi-core CPU, respectively, and 1.71× over PPCG on GPU.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638484"}, {"primary_key": "690267", "vector": [], "sparse_vector": [], "title": "POSTER: LLM-PQ: Serving LLM on Heterogeneous Clusters with Phase-Aware Partition and Adaptive Quantization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yanghua Peng", "<PERSON><PERSON>"], "summary": "The immense sizes of Large-scale language models (LLMs) have led to high resource demand and cost for running the models. Though the models are largely served using uniform high-caliber GPUs nowadays, utilizing a heterogeneous cluster with a mix of available high- and low-capacity GPUs can potentially substantially reduce the serving cost. This paper proposes LLM-PQ, a system that advocates adaptive model quantization and phase-aware partition to improve LLM serving efficiency on heterogeneous GPU clusters. Extensive experiments on production inference workloads demonstrate throughput improvement in inference, showing great advantages over state-of-the-art works.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638480"}, {"primary_key": "690268", "vector": [], "sparse_vector": [], "title": "POSTER: StructMG: A Fast and Scalable Structured Multigrid.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Sicong Li", "<PERSON><PERSON>", "<PERSON>"], "summary": "Parallel multigrid is widely used as preconditioners in solving large-scale sparse linear systems. However, the current multigrid library still needs more satisfactory performance for structured grid problems regarding speed and scalability. To this end, we design and implement StructMG, a fast and scalable multigrid that constructs hierarchical grids automatically based on the original matrix. As a preconditioner, StructMG can achieve both low cost per iteration and good convergence. Two idealized and five real-world problems from four application fields, including radiation hydrodynamics, petroleum reservoir simulation, numerical weather prediction, and solid mechanics, are evaluated on ARM and X86 platforms. In comparison to hypre's multigrid preconditioners, StructMG achieves the fastest time-to-solutions in all cases with average speedups of 17.6x, 5.7x, 4.6x, 8.5x over SMG, PFMG, SysPFMG, and BoomerAMG, respectively. Additionally, StructMG significantly improves strong and weak scaling efficiencies in most tests.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638482"}, {"primary_key": "690269", "vector": [], "sparse_vector": [], "title": "POSTER: RELAX: Durable Data Structures with Swift Recovery.", "authors": ["Almog Zur", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent non-volatile main memory technology gave rise to an abundance of research on building persistent data structures, whose content can be recovered after a system crash. While there has been significant progress in making durable data structures efficient, shortening the length of the recovery phase after a crash has not received much attention. In this paper we present the RELAX general transformation. RELAX generates lock-free durable data structures that provide the best of both worlds: almost zero recovery time and high performance.", "published": "2024-01-01", "category": "ppopp", "pdf_url": "", "sub_summary": "", "source": "ppopp", "doi": "10.1145/3627535.3638469"}]