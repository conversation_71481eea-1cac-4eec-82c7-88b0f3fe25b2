[{"primary_key": "4165192", "vector": [], "sparse_vector": [], "title": "Device-free gesture tracking using acoustic signals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Device-free gesture tracking is an enabling HCI mechanism for small wearable devices because fingers are too big to control the GUI elements on such small screens, and it is also an important HCI mechanism for medium-to-large size mobile devices because it allows users to provide input without blocking screen view. In this paper, we propose LLAP, a device-free gesture tracking scheme that can be deployed on existing mobile devices as software, without any hardware modification. We use speakers and microphones that already exist on most mobile devices to perform device-free tracking of a hand/finger. The key idea is to use acoustic phase to get fine-grained movement direction and movement distance measurements. LLAP first extracts the sound signal reflected by the moving hand/finger after removing the background sound signals that are relatively consistent over time. LLAP then measures the phase changes of the sound signals caused by hand/finger movements and then converts the phase changes into the distance of the movement. We implemented and evaluated LLAP using commercial-off-the-shelf mobile phones. For 1-D hand movement and 2-D drawing in the air, LLAP has a tracking accuracy of 3.5 mm and 4.6 mm, respectively. Using gesture traces tracked by LLAP, we can recognize the characters and short words drawn in the air with an accuracy of 92.3% and 91.2%, respectively.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973764"}, {"primary_key": "4165193", "vector": [], "sparse_vector": [], "title": "A millimeter wave software defined radio platform with phased arrays: poster.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recently, there has been significant interest in performing research on millimeter wave (mmWave) communications. However, there do not exist any mmWave radio platforms with phased arrays available to the networking community. All existing mmWave platforms use horn antennas which require mechanical steering and are not suitable for non-static links or multi-user networks. We have built MiRa: a full-fledged mmWave radio with phased arrays capable of beam steering. MiRa operates as a daughterboard for the USRP software radio which enables easy manipulation of mmWave signals using standard GNU-radio software. With its reconfigurable architecture, steerable phased arrays and open SDR platform, MiRa can help advance mmWave research in the mobile and networking community.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985258"}, {"primary_key": "4165210", "vector": [], "sparse_vector": [], "title": "CSI feedback reduction by checking its validity period: poster.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jinsong Han", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multi-user MIMO (MU-MIMO) is proposed in 802.11ac to achieve more than 3x faster than 802.11n. In the real world no-one gets close to theoretical speeds. The primary reason for this anomaly are the various overheads of channel access and channel state information (CSI) feedback. In order to achieve concurrent data transmission, (CSI) feedback from users is required. However, this overhead can easily overwhelm the actual channel time spent on data transmission in large-scale network. Moreover, due to spontaneous uplink traffic, which makes the problem even more challenging.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985281"}, {"primary_key": "4165213", "vector": [], "sparse_vector": [], "title": "LTE in unlicensed spectrum: are we there yet?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we explore the potential and impact of unlicensed LTE on WiFi in unlicensed spectrum. Our experiments demonstrate that the large asymmetry in the channel access methodologies employed by WiFi and LTE (carrier sensing/notification in WiFi, energy sensing alone in LTE-U), can result LTE-U completely blocking WiFi transmissions, and causing significant degradation to either technologies from collisions.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973781"}, {"primary_key": "4165216", "vector": [], "sparse_vector": [], "title": "Low-cost wireless phase calibration that works on COTS RFID systems: poster.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Xiao<PERSON> Chen", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces a wireless phase calibration algorithm that can be applied on cheap commercial off-the-shelf (COTS) radio frequency identification (RFID) system and auto acquire an accurate radio frequency (RF) phase information without any offline training. The key observation is that the raw phase measurements even measured form different RFID tags contain a same set of unknown phase errors. With enough tags' phase measurements, we can determine all the unknown phase errors, since the number of known phase measurements is much larger than the number of unknown phase errors. Real-world experimental results demonstrate the effectiveness of the proposed method.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985267"}, {"primary_key": "4165217", "vector": [], "sparse_vector": [], "title": "Towards adversarial detection of mobile malware: poster.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Lihua Xu"], "summary": "Android malware has been found on various third-party online markets, which poses drastic threats to mobile users in terms of security and privacy. Machine learning is one of the promising approaches to discriminate the malicious applications from the benign ones. Despite its higher malware detection capability, a significant challenge remains: in adversarial environment, an attacker can adapt by maximally sabotaging classifiers by polluting training data. This paper proposes KuafuDet, a two-phase learning enhancing approach that adversarially detects the Android malware. Experiments on more than 50,000 Android applications demonstrate the effectiveness and scalability of our approach.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985246"}, {"primary_key": "4165218", "vector": [], "sparse_vector": [], "title": "Experience: accurate simulation of dense scenarios with hundreds of vehicular transmitters.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper reports on our methodology and experience from a multi-year effort to cross-validate a vehicular network experiment with four hundred Dedicated Short Range Communications IEEE 802.11p transmitters through ns-3 simulations. With most of these transmitters in communication range, this represents an extremely dense wireless configuration that challenges radio and interference models. Field test and simulations were conducted in tandem and iteratively to facilitate model selection and configuration as well as to allow a detailed evaluation of simulation accuracy. We have learned that 1) results were most sensitive to parameter choices in the propagation and receiver models, with simulator default parameters not providing a good match; 2) results could, however, be significantly improved by adapting, implementing, and calibrating the propagation models and receiver models from the literature, yielding 88% accuracy (in terms of packet error rate compared to the field test) in such a complex large-scale setting; 3) the process was helpful in identifying errors both in the simulation models and in the experimental code and points to opportunities for further research.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973779"}, {"primary_key": "4165220", "vector": [], "sparse_vector": [], "title": "Repeatable mobile networking research with phantomNet: demo.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We will demonstrate features and capabilities of the PhantomNet testbed. PhantomNet is a mobile testbed, at the University of Utah, aimed at enabling a broad range of mobile networking related research. PhantomNet is remotely accessible and open to the mobile networking research community.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985616"}, {"primary_key": "4165221", "vector": [], "sparse_vector": [], "title": "Acacia - context-aware edge computing for continuous interactive applications over mobile networks: demo.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose Acacia- a service abstraction framework that enables continuous interactive (CI) applications on edge clouds in mobile networks. We will demonstrate the Acacia architecture and illustrate its feasibility by using an augmented reality application as an example use case.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985623"}, {"primary_key": "4165225", "vector": [], "sparse_vector": [], "title": "Cell tower extension through drones: poster.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Internet connectivity on mobile devices is an essential commodity in today's world. While outdoors, most people connect through cellphone towers on 3G or 4G. However, cellphone tower coverage is not uniform and is affected by electromagnetic shadows cast by large structures, multipath, and absorption by various surfaces. Users with high data needs suffer in such locations due to insufficient network bandwidth. A similar insufficiency can also be felt by flash crowds in locations with otherwise moderate signal strength due to division of the available bandwidth.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985275"}, {"primary_key": "4165226", "vector": [], "sparse_vector": [], "title": "Preserving incumbent users&apos; privacy in exclusion-zone-based spectrum access systems: poster.", "authors": ["Yanzhi Dou", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic spectrum access (DSA) technique has emerged as a fundamental approach to mitigate the spectrum scarcity problem. As a key form of DSA, government is proposing to release more federal spectrum for sharing with commercial wireless users. However, the flourish of federal-commercial sharing hinges upon how the federal privacy is managed. In current DSA proposals, the sensitive exclusion zone (E-Zone) information of federal incumbent users (IUs) needs to be shared with a spectrum access system (SAS) to realize spectrum allocation. However, SAS is not necessarily trust-worthy for holding the sensitive IU E-Zone data, especially considering that FCC allows some industry third parties (e.g., Google) to operate SAS for better efficiency and scalability. Therefore, the current proposals dissatisfy the IUs' privacy requirement. To address the privacy issue, this paper presents an IU-privacy-preserving SAS (IP-SAS) design, which realizes the spectrum allocation process through secure computation over ciphertext based on homomorphic encryption.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985283"}, {"primary_key": "4165227", "vector": [], "sparse_vector": [], "title": "Martian - message broadcast via LED lights to heterogeneous smartphones: poster.", "authors": ["<PERSON><PERSON><PERSON>", "Junze Han", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Xiangyang Li"], "summary": "Visible light communication (VLC) has been shown to have several advantages over traditional wireless communication. We envision a LED-to-smartphone VLC protocol for delivering messages to a group of unsynchronized mobile device receivers. We carefully design and implement our protocol, Martian, which allows smooth communication from the LED lights to a group of camera-enabled mobile devices. Across several phone models, Martian can achieve data rate of about 1.6kbps even with NLOS-light. Our intensive evaluations indicate that, the data rate reaches 4.2kbps on iPhone 6. This is a significant improvement compared with the 88bps data rate claimed by state-of-art design.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985257"}, {"primary_key": "4165231", "vector": [], "sparse_vector": [], "title": "A framework for collaborative sensing and processing of mobile data streams: demo.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Emerging mobile applications involve continuous sensing and complex computations on sensed data streams. Examples include cognitive apps (e.g., speech recognition, natural language translation, as well as face, object, or gesture detection and recognition) and anticipatory apps that proactively track and provide services when needed. Unfortunately, today's mobile devices cannot keep pace with such apps, despite advances in hardware capability. Traditional approaches address this problem by computation offloading. One approach offloads by sending sensed streams to remote cloud servers via cellular networks or to cloudlets via Wi-Fi, where a clone of the app runs [2, 3, 4]. However, cloudlets may not be widely deployed and access to cloud infrastructure may yield high network delays and can be intermittent due to mobility. Morever, users might hesitate to upload private sensing data to the cloud or cloudlet. A second approach offloads to accelerators by rewriting code to use DSP or GPU within mobile devices. However, using accelerators requires substantial programming effort and produces varied benefits for diverse codes on heterogeneous devices.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985620"}, {"primary_key": "4165233", "vector": [], "sparse_vector": [], "title": "Appstract: on-the-fly app content semantics with better privacy.", "authors": ["<PERSON><PERSON>", "Oriana Riva", "<PERSON><PERSON>"], "summary": "Services like Google Now on Tap and Bing Snapp enable new user experiences by understanding the semantics of contents that users consume in their apps. These systems send contents of currently displayed app pages to the cloud to identify relevant entities (e.g., a movie) appearing in the current page and show information related to such entities (e.g., local theaters playing the movie). These new experiences come with privacy concerns as they can send sensitive on-screen data (bank details, medical data, etc.) to the cloud. We propose a novel approach that efficiently extracts app content semantics on the device, without exfiltrating user data. Our solution consists of two phases: an offline, user-agnostic, in-cloud phase that automatically annotates apps' UI elements with stable semantics, and a lightweight on-device phase that assigns semantics to captured app contents on the fly, by matching the annotations. With this automatic approach we annotated 100+ food, dining, and music apps, with accuracy over 80%. Our system implementation for Android and Windows Phone---Appstract---incurs minimal runtime overhead. We built eight use cases on the Appstract framework.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973770"}, {"primary_key": "4165235", "vector": [], "sparse_vector": [], "title": "Do open resources encourage entry into the millimeter wave cellular service market?: poster.", "authors": ["Fraida Fund", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The resource usage model for millimeter wave bands has been the subject of considerable debate. The massive bandwidth, highly directional antennas, high penetration loss and susceptibility to shadowing in these bands suggest certain advantages to spectrum and infrastructure sharing. In particular, resources that are \"open\", such as unlicensed spectrum or a deployment of base stations open to all service providers, may offer greater gains in mmWave bands than at conventional cellular frequencies. However, even when sharing is technically beneficial (as recent research in this area suggests that it is), it may not be profitable. In this paper, both the technical and economic implications of resource sharing in millimeter wave networks are studied. Millimeter wave service is considered in the economic framework of a network good, and detailed network simulations are used to understand data rates, profit, and demand for millimeter wave service, with and without open resources. The results suggest that \"open\" deployments of neutral small cells that serve subscribers of any service provider encourage market entry by making it easier for networks to reach critical mass, more than \"open\" (unlicensed) spectrum would.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985272"}, {"primary_key": "4165238", "vector": [], "sparse_vector": [], "title": "LEO: scheduling sensor inference algorithms across heterogeneous mobile processors and network resources.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile apps that use sensors to monitor user behavior often employ resource heavy inference algorithms that make computational offloading a common practice. However, existing schedulers/offloaders typically emphasize one primary offloading aspect without fully exploring complementary goals (e.g., heterogeneous resource management with only partial visibility into underlying algorithms, or concurrent sensor app execution on a single resource) and as a result, may overlook performance benefits pertinent to sensor processing.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973777"}, {"primary_key": "4165240", "vector": [], "sparse_vector": [], "title": "HTTP/2 performance in cellular networks: poster.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "HTTP/2 (h2) was standardized in 2015 as an improvement to HTTP/1.1 (h1) to achieve faster webpage load times (PLTs) [5]. Previous studies have shown both improvement and degradation in PLT when using h2 with respect to h1 [6, 8]. The disagreement about h2 performance from these studies motivates further investigation as to whether and under what conditions h2 brings the performance benefits that were originally envisioned [5].", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985264"}, {"primary_key": "4165241", "vector": [], "sparse_vector": [], "title": "A case for faster mobile web in cellular IPv6 networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The transition to IPv6 cellular networks creates uncertainty for content providers (CPs) and content delivery networks (CDNs) of whether and how to follow suit. Do CPs that update their CDN contracts to allow IPv6 hosting achieve better, or worse performance in mobile networks? Should CDNs continue to host mobile content over IPv4 networks, or persuade to their CP customers the performance benefits of IPv6 content delivery?", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973771"}, {"primary_key": "4165243", "vector": [], "sparse_vector": [], "title": "GENI wireless testbed: a flexible open ecosystem for wireless communications research: demo.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This demo presents the architecture of GENI (Global Environment of Network Innovations) [1] edge cloud computing network in the form of compute and storage resources, a mobile 4G LTE edge and a high speed campus network connecting these components. GENI's edge computing strategy proceeds by deploying self-contained packages of network, computing, storage resources, or GENI Racks [2] connected via high speed fiber to LTE BS(s) across twelve campuses in the US, all interconnected via a nationwide research network. The GENI mobile computing resource manager is based on the Orbit Management framework (OMF) [3] and provides seamless access to the edge computing resources via the GENI Portal for experimentation, scheduling, data collection and processing.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985627"}, {"primary_key": "4165245", "vector": [], "sparse_vector": [], "title": "Tracking drone orientation with multiple GPS receivers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Inertial sensors continuously track the 3D orientation of a flying drone, serving as the bedrock for maneuvers and stabilization. However, even the best inertial measurement units (IMU) are prone to various types of correlated failures. We consider using multiple GPS receivers on the drone as a fail-safe mechanism for IMU failures. The core challenge is in accurately computing the relative locations between each receiver pair, and translating these measurements into the drone's 3D orientation. Achieving IMU-like orientation requires the relative GPS distances to be accurate to a few centimeters -- a difficult task given that GPS today is only accurate to around 1-4 meters. Moreover, GPS-based orientation needs to be precise even under sharp drone maneuvers, GPS signal blockage, and sudden bouts of missing data. This paper designs SafetyNet, an off-the-shelf GPS-only system that addresses these challenges through a series of techniques, culminating in a novel particle filter framework running over multi-GNSS systems (GPS, GLONASS, and SBAS). Results from 11 sessions of 5-7 minute flights report median orientation accuracies of 2° even under overcast weather conditions. Of course, these improvements arise from an increase in cost due to the multiple GPS receivers, however, when safety is of interest, we believe that tradeoff is worthwhile.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973768"}, {"primary_key": "4165247", "vector": [], "sparse_vector": [], "title": "Automatic personal fitness assistance through wearable mobile devices: poster.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Acknowledging the powerful sensors on wearable mobile devices enabling various applications to improve users' life styles and qualities, this paper takes one step forward developing a automatic personal fitness assistance through wearable mobile devices to assess dynamic postures in workouts. In particular, our system recognizes different types of exercises and interprets fine-grained fitness data to an easy-to-understand exercise review score. The system has the ability to align the sensor readings from wearable devices to the earth coordinate system, ensuring the accuracy and robustness of the system. Experiments with 12 types of exercises involve multiple participants doing both anaerobic and aerobic exercises in indoors as well as outdoors. Our results demonstrate that the proposed system can provide meaningful review and recommendations to users by accurately measure their workout performance and achieve 93% accuracy for workout analysis.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985266"}, {"primary_key": "4165252", "vector": [], "sparse_vector": [], "title": "Enabling on-body transmissions with commodity devices: poster.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this poster, we show for the first time that commodity devices can be used to generate wireless data transmissions that are confined to the human body. Specifically, we show that commodity input devices such as fingerprint sensors and touchpads can be used to transmit information to only wireless receivers that are in contact with the body.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985273"}, {"primary_key": "4165261", "vector": [], "sparse_vector": [], "title": "An infrastructureless and self-deployable indoor navigation approach using semantic signatures: poster.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Semantic localization refers to the process of finding one's location with respect to visible or identifiable objects in the scene instead of finding position coordinates. In this research, we use textual signs and their geographical relationships inside a building as semantic signatures to design an infrastructureless indoor navigation system without a site survey. We propose a computer vision-based approach, which takes as an input a floor plan image and automatically infers the floor graph. The constructed graph is then used to estimate the shortest path, which is a sequence of store names, that the user needs to pass to reach her destination.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985270"}, {"primary_key": "4165263", "vector": [], "sparse_vector": [], "title": "Near-ultrasound communication for TV&apos;s 2nd screen services.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sun Hong Lim", "<PERSON> Shin", "<PERSON>", "<PERSON><PERSON><PERSON>ng <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose a near-ultrasound chirp signal-based communication for the TV's 2nd screen services. While near-ultrasound (with under 20 kHz frequency) communication has been developed for various applications recently, none of the previous work provides a perfect solution for 2nd screen services between TVs and smart devices. This is due mainly to the following real world challenges. The embedded signal in TV contents should be successfully received in a typical TV-watching environment by (i) delivering information at least at 15 bps with significantly low volume to avoid human perception, (ii) despite the presence of ambient noise, e.g., a tick, a snap, or a knock. To fulfill (i), we design chirp quaternary orthogonal keying (QOK) symbols. Especially, we aim to minimize inter-symbol interference (ISI) effects by symbol design in order to completely eliminate guard intervals. To resolve (ii), we propose the novel J-shape detection algorithms for both frame synchronization and carrier sensing. The proposed modem achieves almost zero frame error rate on a smartphone 2.7 m away from the TV even with minimal receive sound pressure level of 35 dBSPL, i.e., the noise level in a very quiet room. Moreover, throughout experiments and log analysis of 2nd screen service deployed in a nation-wide TV broadcasting system, J-shape detection algorithms are proven to achieve highly resilient performance for both frame synchronization and carrier sensing compared to previous schemes.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973774"}, {"primary_key": "4165269", "vector": [], "sparse_vector": [], "title": "HearHere: smartphone based audio localization using time difference of arrival: demo.", "authors": ["<PERSON>", "Seoyoon Park", "<PERSON>", "<PERSON><PERSON>"], "summary": "Recent advancements in audio recording on mobile devices have improved audio localization capabilities using phones. Previous research has shown that millimeter level accuracy is capable using an off-the-shelf smartphone. This work demonstrates that such valuable resources in smartphones are readily available for developing consumer applications. We develop a smartphone application called HearHere that utilizes geometric features of sound to categorize a tapping sound within a grid. The geometric features are based on the Time Difference of Arrival (TDoA) of the sound between two microphones. By using this classification, any off the shelf phone with stereo recording capability can be made into a music producing device. The application consists of three major components: system calibration, data collection, and audio localization engine based on TDoA. We demonstrate that we can accurately map the tapping sound six region on various solid surface and turn it into a MIDI controller with six different types of instruments.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985625"}, {"primary_key": "4165272", "vector": [], "sparse_vector": [], "title": "A mobility prediction system leveraging realtime location data streams: poster.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Location-based services today, exceedingly depend on user mobility prediction, in order to push context aware services ahead of time. Existing location forecasting techniques are driven by large volumes of data to train the prediction models in a centralised server. This amounts to considerably long waiting times before the model kicks in. Disclosing highly sensitive location information to third party entities also exposes the user to several privacy risks. To address these issues, we put forth a mobility prediction system, able to provide swift realtime predictions, evading the strenuous training procedure. We enable this by constantly adapting the model to substantive user mobility behaviours that facilitate accurate predictions even on marginal time bounded movements. In comparison to existing frameworks, we utilise less volumes of data to produce satisfactory prediction accuracies. This in turn lowers the computational complexity making implementation on mobile devices feasible and a step towards privacy preservation. Here, only the predicted location can be sent to such services to maintain the utility/privacy tradeoff. Our preliminary evaluations based on real world mobility traces corroborate our hypothesis.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985263"}, {"primary_key": "4165275", "vector": [], "sparse_vector": [], "title": "In-device, runtime cellular network information extraction and analysis: demo.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present the demonstration of MobileInsight, a software tool that collects, analyzes and exploits runtime information from operational cellular network. MobileInsight runs on commercial off-the-shelf phones without extra hardware or additional support from cellular network operators. It exposes cellular protocol messages from the 3G/4G chipset, and performs in-device protocol analysis. We demonstrate the in-device runtime cellular message collection, analysis of the protocol states, visualization of runtime wireless channel and mobility dynamics, and how mobile applications benefit from MobileInsight.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985622"}, {"primary_key": "4165277", "vector": [], "sparse_vector": [], "title": "Towards customer trouble tickets resolution automation in large cellular services: demo.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Customer care calls have served as a primary and direct channel for mobile users to communicate their needs with cellular service providers. These customer needs can be quite diverse and usually include provisioning/upgrade, account/billing, device and service performance related issues. The resolution process of these customer needs is largely manual and tedious. The resolution process not only affects the customer experience but could also entails cost on customer care and network operations of the mobile service providers. We present TREAT, which aims at automatically identifying opportunities for auto-resolving customer reported needs, and hence improving customer experiences and reducing cost for customer care and network operations.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985611"}, {"primary_key": "4165280", "vector": [], "sparse_vector": [], "title": "Mobileinsight: extracting and analyzing cellular network information on smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We design and implement MobileInsight, a software tool that collects, analyzes and exploits runtime network information from operational cellular networks. MobileInsight runs on commercial off-the-shelf phones without extra hardware or additional support from operators. It exposes protocol messages on both control plane and (below IP) data plane from the 3G/4G chipset. It provides in-device protocol analysis and operation logic inference. It further offers a simple API, through which developers and researchers obtain access to low-level network information for their mobile applications. We have built three showcases to illustrate how MobileInsight is applied to cellular network research.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973751"}, {"primary_key": "4165284", "vector": [], "sparse_vector": [], "title": "Sensing on ubiquitous surfaces via vibration signals: poster.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This work explores vibration-based sensing to determine the location of a touch on extended surface areas as well as identify the object touching the surface leveraging a single sensor. It supports a broad array of applications through either passive or active sensing using only a single sensor. In the passive sensing, the received vibration signals are determined by the location of the touch impact. This allows location discrimination of touches precise enough to enable emerging applications such as virtual keyboards on ubiquitous surfaces for mobile devices. Moreover, in the active mode, the received vibration signals carry richer information of the touching object's characteristics (e.g., weight, size, location and material). This further enables our work to match the signals to the trained profiles and allows it to differentiate personal objects in contact with any surface. We evaluated extensively in the use cases of localizing touches (i.e., virtual keyboards), object localization and identification. Our experimental results demonstrate that the proposed vibration-based solution can achieve high accuracy, over 95%, in all these use cases.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985260"}, {"primary_key": "4165285", "vector": [], "sparse_vector": [], "title": "VibKeyboard: virtual keyboard leveraging physical vibration: demo.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "VibKeyboard could accurately determine the location of a keystroke on extended surface areas leveraging a single vibration sensor. Unlike capacitive sensing, it does not require conductive materials and compared to audio sensing it is more robust to acoustic noise. In VibKeyboard, the received vibration signals are determined by the location of the touch impact. This allows location discrimination of touches precise enough to enable emerging applications such as virtual keyboards on ubiquitous surfaces for mobile devices. VibKeyboard seeks to extract unique features in frequency domain embedded in the vibration signal attenuation and interference and perform fine grained localization. Our experimental results demonstrate that VibKeyboard could accurately recognize keystrokes from close-by keys on a nearby virtual keyboard.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985624"}, {"primary_key": "4165286", "vector": [], "sparse_vector": [], "title": "Measuring and optimizing android smartwatch energy consumption: poster.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Smartwatches are operating under tight energy constraints. In this paper, we describe our on-going work on measuring and optimizing Android smartwatch energy consumption. We derived power models for commodity smartwatches, and then applied the power model to an IRB-approved user study involving 30 smartwatch users. We then propose research ideas on improving energy efficiencies for Android smartwatches.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985259"}, {"primary_key": "4165287", "vector": [], "sparse_vector": [], "title": "PIN number-based authentication leveraging physical vibration: poster.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we propose the first PIN number based authentication system, which can be deployed on ubiquitous surfaces, leveraging physical vibration signals. The proposed system aims to integrate PIN number, behavioral and physiological characteristics together to provide enhanced security. Different from the existing password-based approaches, the proposed system builds upon a touch sensing technique using vibration signals that can operate on any solid surface. In this poster, we explore the feasibility of using vibration signals for ubiquitous user authentication and develop algorithms that identify fine-grained finger inputs with different password secrets (e.g., PIN sequences). We build a prototype using a vibration transceiver that can be attached to any surface (e.g., a door or a desk) easily. Our experiments in office environments with multiple users demonstrate that we can achieve high authentication accuracy with a low false negative rate.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985261"}, {"primary_key": "4165288", "vector": [], "sparse_vector": [], "title": "Lasagna: towards deep hierarchical understanding and searching over mobile sensing data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiangyang Li", "<PERSON><PERSON>"], "summary": "The proliferation of mobile devices has enabled extensive mobile-data supported applications, e.g., exercise and activity recognition and quantification. Typically, these applications need predefined features and are only applicable to predefined activities. In this work, we address the issue of deep understanding of arbitrary activities and semantic searching of any activity over massive mobile sensing data. The challenges stem from the rich dynamics and the wide-spectrum of activities that a human being could perform. We propose a hierarchical activity representation, extract common bases of motion data in an unsupervised manner by leveraging the power of deep neural networks, and propose a universal multi-resolution representation for all activities without prior knowledge. Based on this representation, we design an innovative system Lasagna to manage and search motion data semantically. We implement a prototype system and our comprehensive evaluations show that our system can achieve highly accurate activity classification (with precision 98.9%) and search (with recall almost 100% and precision about 90%) over a diverse set of activities.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973752"}, {"primary_key": "4165292", "vector": [], "sparse_vector": [], "title": "3D real-time indoor localization via broadband nonlinear backscatter in passive devices with centimeter precision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Hui", "<PERSON>"], "summary": "We propose and demonstrate accurate 3D real-time indoor localization via broadband nonlinear backscatter in passive devices. The proposed method does not need any relative motion between a reader and a tag or the use of reference anchor nodes. In the conventional radio frequency identification (RFID) system, a passive tag responds to a reader by switching its antenna \"on\" and \"off\". The operation of such conventional backscatter is essentially \"linear\", since the reader-to-tag (downlink) and tag-to-reader (uplink) signals overlap on the same carrier frequency. Although linear backscatter is straightforward, the self-jamming problem caused by strong leakage signals from the transmitter to the receiver is notorious and poses many constraints on the received signal quality, operation bandwidth, modulation flexibility and system complexity. To enable high-accuracy real-time 3D indoor localization for passive devices, we show that nonlinear backscatter is more effective than linear backscatter. Nonlinear backscatter exploits nonlinear elements in passive devices to generate second or higher-order harmonics as the uplink response. Separation of downlink and uplink on different carriers allows immediate self-jamming cancellation and direct un-modulated carrier phase decoding, hence resulting in better received signal quality and broad bandwidth of operation, both of which are critical for the localization system. The broad bandwidth allows the design of an efficient phase-based ranging algorithm - heuristic multi-frequency continuous wave (HMFCW) ranging which resolves ambiguous phase cycles with heuristically optimized sparse carrier frequencies. HMFCW ranging can correctly pin down the phase cycle integer with 100% reliability as long as the phase error falls within ±90° × BW% (percentage bandwidth). In our present implementation, we achieved a median ranging error below 1 cm under phase error bounds of ±50°. We realized real-time 3D localization from differential ranging by nonlinear conjugate gradient (CG) search for hyperboloids intersection in a multi-static transceiving system with 1 Tx antenna and 4 Rx antennas. The measured 3D localization median error was 3.5 cm in the indoor environment. Presently, the measurement latency was less than 0.155 seconds. We will present system design, algorithms and a prototype with experimental evaluation.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973754"}, {"primary_key": "4165294", "vector": [], "sparse_vector": [], "title": "Migrating running applications across mobile edge clouds: poster.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile edge clouds (MECs) are small cloud-like infrastructures deployed in close proximity to users, allowing users to have seamless and low-latency access to cloud services. When users move across different locations, their service applications often need to be migrated to follow the user so that the benefit of MEC is maintained. In this paper, we propose a layered framework for migrating running applications that are encapsulated either in virtual machines (VMs) or containers. We evaluate the migration performance of various real applications under the proposed framework.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985265"}, {"primary_key": "4165295", "vector": [], "sparse_vector": [], "title": "Harnessing spectrum awareness to enhance mobile computing: poster.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "The well documented growth in mobile traffic is mainly driven by increasingly sophisticated smart phone applications. Simultaneously, user preference for lighter phones has resulted in more battery power constrained hand-helds that offload computations to resource intensive cloud. This second trend exacerbates the bandwidth crunch often experienced over wireless networks. Our idea (joint cognitive offloading and scheduling) is to use dynamic spectrum access and management concepts from wireless networking to effect computation offloading and scheduling solutions that achieves near optimal trade-offs between the mobile device and wireless resources. We use all radio available interfaces (e.g. WiFi, LTE) in multi-RAT enabled devices to schedule appropriate components of the application to run either on the mobile device or on the resource-rich cloud, while staying adaptive to the conditions of the wireless network.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985277"}, {"primary_key": "4165296", "vector": [], "sparse_vector": [], "title": "CAT: high-precision acoustic motion tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Video games, Virtual Reality (VR), Augmented Reality (AR), and Smart appliances (e.g., smart TVs) all call for a new way for users to interact and control them. This paper develops high-preCision Acoustic Tracker (CAT), which aims to replace a traditional mouse and let a user play games, interact with VR/AR headsets, and control smart appliances by moving a smartphone in the air. Achieving high tracking accuracy is essential to provide enjoyable user experience. To this end, we develop a novel system that uses audio signals to achieve mm-level tracking accuracy. It lets multiple speakers transmit inaudible sounds at different frequencies. Based on the received sound, our system continuously estimates the distance and velocity of the mobile with respect to the speakers to continuously track it. At its heart lies a distributed Frequency Modulated Continuous Waveform (FMCW) that can accurately estimate the absolute distance between a transmitter and a receiver that are separate and unsynchronized. We further develop an optimization framework to combine FMCW estimation with Doppler shifts and Inertial Measurement Unit (IMU) measurements to enhance the accuracy, and efficiently solve the optimization problem. We implement two systems: one on a desktop and another on a mobile phone. Our evaluation and user study show that our system achieves high tracking accuracy and ease of use using existing hardware.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973755"}, {"primary_key": "4165297", "vector": [], "sparse_vector": [], "title": "High-precision acoustic motion tracking: demo.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Video games, virtual reality, augmented reality, and smart appliances all call for a new way for users to interact and control them. This paper develops high-preCision Acoustic Tracker (CAT), which aims to replace a traditional mouse and let a user control various devices by moving a smartphone in the air. At its heart lies a distributed Frequency Modulated Continuous Waveform (FMCW) that can accurately estimate the distance between a transmitter and a receiver that are separate and unsynchronized. We further develop an optimization framework to combine FMCW estimation with Doppler shifts to enhance the accuracy. We implement CAT on a mobile phone. The performance evaluation and user study show that our system achieves high tracking accuracy and ease of use using existing hardware.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985617"}, {"primary_key": "4165299", "vector": [], "sparse_vector": [], "title": "JoyTag: a battery-less videogame controller exploiting RFID backscattering: demo.", "authors": ["Gaia Ma<PERSON>li", "<PERSON><PERSON>", "Giorgia Ramponi", "<PERSON><PERSON>"], "summary": "This demo presents our experiences in developing a joystick for videogames that uses RFID backscattering for battery-free operation. Specifically, we develop a system to gather data from a wireless and battery-less joystick, named JoyTag, while it interacts with a videogame console. Our system enables consumers to use JoyTag at every moment without caring about charging.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985628"}, {"primary_key": "4165300", "vector": [], "sparse_vector": [], "title": "Cross-layer MAC/PHY protocol to support IoT traffic in 5G: poster.", "authors": ["Siddarth Mathur", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With exponential growth of IoT devices [1], the 5G network will experience a variety of traffic patterns not prevalent in earlier applications. These, often will transmit short sporadic messages, which are not well suited to the connection-oriented modes associated with legacy 3GPP network resulting in high service latency and excessive control overhead. It is acknowledged that current 4G network could be overwhelmed by the surge in both traffic and control plane signaling load. For 5G, it is necessary to redesign the mobile network to provide a low delay, low control overhead IoT mode that will work efficiently for emerging application scenarios. The MAC layer has to be designed in such a way that IoT messages experience low access latency across both the radio access network and core network. The goal is to operate in the same band as current LTE, thus not requiring any separate bandwidth allocation and is backward compatible with the current 4G system. In this paper, we propose a cross-layer MAC and Physical layer solution for low power, low bitrate devices that require low access delay and long range for communication.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985280"}, {"primary_key": "4165302", "vector": [], "sparse_vector": [], "title": "MASHaBLE: mobile applications of secret handshakes over bluetooth LE.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present new applications for cryptographic secret handshakes between mobile devices on top of Bluetooth Low-Energy (LE). Secret handshakes enable mutual authentication, with the property that the parties learn nothing about each other unless they have been both issued credentials by a group administrator. This property provides strong privacy guarantees that enable interesting applications. One of them is proximity-based discovery for private communities. We introduce MASHaBLE, a mobile application that enables participants to discover and interact with nearby users if and only if they belong to the same secret community. We use direct peer-to-peer communication over Bluetooth LE, rather than relying on a central server. We discuss the specifics of implementing secret handshakes over Bluetooth LE and present our prototype implementation.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973778"}, {"primary_key": "4165303", "vector": [], "sparse_vector": [], "title": "QuickC: practical sub-millisecond transport for small cells.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cellular operators want to be able to deploy small cells with the same ease as WiFi access points, to quickly address traffic hotspots in dense urban areas. However, deploying small cells has not been easy. The reason is that due to scarcity of licensed spectrum, small cells need to use the same spectrum as the existing macro cells, and they need to explicitly coordinate their spectrum usage with each other to manage interference. The challenge is that this coordination needs to happen with latencies less than a millisecond, otherwise adding small cells does not help scale the overall network capacity. Implementing such tight coordination in dense urban deployments has not been easy in practice.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973773"}, {"primary_key": "4165306", "vector": [], "sparse_vector": [], "title": "Investigation of multi-device location spoofing attacks on air traffic control and possible countermeasures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multilateration techniques have been proposed to verify the integrity of unprotected location claims in wireless localization systems. A common assumption is that the adversary is equipped with only a single device from which it transmits location spoofing signals. In this paper, we consider a more advanced model where the attacker is equipped with multiple devices and performs a geographically distributed coordinated attack on the multilateration system. The feasibility of a distributed multi-device attack is demonstrated experimentally with a self-developed attack implementation based on multiple COTS software-defined radio (SDR) devices. We launch an attack against the OpenSky Network, an air traffic surveillance system that implements a time-difference-of-arrival (TDoA) multi-lateration method for aircraft localization based on ADS-B signals. Our experiments show that the timing errors for distributed spoofed signals are indistinguishable from the multilateration errors of legitimate aircraft signals, indicating that the threat of multi-device spoofing attacks is real in this and other similar systems. In the second part of this work, we investigate physical-layer features that could be used to detect multi-device attacks. We show that the frequency offset and transient phase noise of the attacker's radio devices can be exploited to discriminate between a received signal that has been transmitted by a single (legitimate) transponder or by multiple (malicious) spoofing sources. Based on that, we devise a multi-device spoofing detection system that achieves zero false positives and a false negative rate below 1%.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973763"}, {"primary_key": "4165309", "vector": [], "sparse_vector": [], "title": "Where the curb meets the cloud: urban innovation in the digital age.", "authors": ["<PERSON><PERSON>"], "summary": "Cities have benefited from the three greatest technological innovations of the past 200 years: the steam engine, electrification, and the automobile. But each advance has created its own challenges, including pollution, overcrowding, sprawl. As the digital revolution transforms cities once again, how can we make sure it improves quality of life while minimizing the downside? With population density comes the possibility of deploying network connectivity and wayfinding at lower cost, but density also increases complexity of deployment. How can digital technology reduce the bad friction of urban environments, such as congestion, cost, and complexity, while increasing good friction, such as all of the serendipitous interactions that cities encourage? Underlying all this change is ubiquitous connectivity and mobile technology, from the phones people carry with them to the supporting devices and network endpoints embedded in the urban infrastructure. Sidewalk Labs is an Alphabet company that works with cities to develop new technology that can improve urban life. We will discuss some of our discoveries and beliefs, and talk about our plans to use mobile technology to help cities take full advantage of the digital revolution.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2991038"}, {"primary_key": "4165310", "vector": [], "sparse_vector": [], "title": "WiART - visualize and interact with wireless networks using augmented reality: demo.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "With the increasing programmability and fast-paced dynamics of modern wireless systems, it has become more difficult to gain timely insights into wireless network operations. In this demonstration1 we present WiART, an augmented reality framework to help visualize and interact with wireless network activities in real time. WiART collects real-time radio and network statistics from participating network devices and depicts them on users' mobile devices in an intuitive way, leveraging the virtual information overlay of augmented reality. Specifically in our current implementation, WiART takes inputs from a cognitive radio link controlling beam-steerable reconfigurable antennas and annotates on a live mobile screen the active pre-measured radiation patterns. In the reverse flow, WiART enables users to select desired antenna radiation patterns directly in the mobile app and observe their effects on link performance in real time. These capabilities add an unprecedented level of instant visualization and interaction with wireless activities and provides valuable insights into the dynamics of a reconfigurable antenna-based cognitive radio network.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985626"}, {"primary_key": "4165315", "vector": [], "sparse_vector": [], "title": "An in-depth understanding of multipath TCP on mobile devices: measurement and system design.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Today's mobile devices are usually equipped with multiple wireless network interfaces that provide new opportunities for improving application performance. In this paper, we conduct an in-depth study of multipath for mobile settings, focusing on MPTCP, with the goal of developing key insights for evolving the mobile multipath design. First, we conduct to our knowledge the most in-depth and the longest user trial of mobile multipath that focuses not only on MPTCP performance, but also on cross-layer interactions. Second, we identify a new research problem of multipath-aware CDN server selection. We demonstrate its real-world importance and provide recommendations. Third, our measurement findings lead us to design and implement a flexible software architecture for mobile multipath called MPFlex, which strategically employs multiplexing to improve multipath performance (by up to 63% for short-lived flows). MPFlex decouples the high-level scheduling algorithm and the low-level OS protocol implementation, and enables developers to flexibly plug-in new multipath features. MPFlex also provides an ideal vantage point for flexibly realizing user-specified multipath policies and is friendly to middleboxes.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973769"}, {"primary_key": "4165324", "vector": [], "sparse_vector": [], "title": "Passive sensor tags: demo.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The sensing capabilities of an Internet-of-Things (IoT) network are usually fixed at deployment. Adding new sensing modalities is a cumbersome process because it requires altering the deployed hardware. We introduce passive sensor tags that allow to easily and seamlessly add new sensors to existing IoT deployments without requiring hardware modifications or additional energy sources. Passive sensor tags employ backscatter communication to generate transmissions that can be decoded by the radio transceivers present in today's IoT devices. Furthermore, unlike recent works, our approach does not require dedicated infrastructure to generate the unmodulated carrier used for backscatter communication.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985610"}, {"primary_key": "4165327", "vector": [], "sparse_vector": [], "title": "GonioSense: a wearable-based range of motion sensing and measurement system for body joints: poster.", "authors": ["Bozhao Qi", "<PERSON><PERSON>"], "summary": "This paper introduces GonioSense -- a wearable based system that can track and measure both active and passive joint motions. GonioSense leverages embedded inertial sensors to provide reliable and consistent measurement results. A key challenge for our system is how to efficiently detect body movement and accurately calculate range of joint motion. We built a preliminary version that can collect and analyze sensor data in real time on the Android platform. To test the system, we recruited five healthy volunteers and measured six kinds of joint motions. The same tools used by physical therapists, measurements gathered from a goniometer and an inclinometer is what we used as the ground truth data for calibrations and evaluation of our algorithm. In the initial version, GonioSense estimation errors are within ± 5% for more than 80% cases. By utilizing wearable technology, the benefits for both doctors and patients could bring forth an immense step forward in physical therapy treatment.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985268"}, {"primary_key": "4165330", "vector": [], "sparse_vector": [], "title": "Network analysis of the steam in-home streaming game system: poster.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper investigates one of the main game streaming services, the Steam In-Home Streaming, to analyze the generated network traffic. Gathered data can be used to model this type of traffic in simulations and to determine whether it could be supported by Internet connections around the world.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985284"}, {"primary_key": "4165332", "vector": [], "sparse_vector": [], "title": "SPREE: a spoofing resistant GPS receiver.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Global Positioning System (GPS) is used ubiquitously in a wide variety of applications ranging from navigation and tracking to modern smart grids and communication networks. However, it has been demonstrated that modern GPS receivers are vulnerable to signal spoofing attacks. For example, today it is possible to change the course of a ship or force a drone to land in a hostile area by simply spoofing GPS signals. Several countermeasures have been proposed in the past to detect GPS spoofing attacks. These counter-measures offer protection only against naive attackers. They are incapable of detecting strong attackers such as those capable of seamlessly taking over a GPS receiver, which is currently receiving legitimate satellite signals, and spoofing them to an arbitrary location. Also, there is no hardware platform that can be used to compare and evaluate the effectiveness of existing countermeasures in real-world scenarios.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973753"}, {"primary_key": "4165335", "vector": [], "sparse_vector": [], "title": "EasyGuard: enhanced context-aware adaptive access control system for android platform: poster.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Shuangxi Hong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Applications in Android often have the ability of accessing sensitive resources on mobile devices. These resources have different levels of security and usage constraint in scenarios which have different requirements for privacy. Therefore, it is in demand for users to have fine-grained privacy protection and resources usage constraint that taking the context information into account, which is not supported by inherent access control mechanism of Android. To address these issues, we designed and implemented an enhanced context-aware adaptive access control system (EasyGuard) in Android to provide adaptive access control automatically when the specific context is detected according to the pre-configured policies. In addition, we developed an application to facilitate users that have little domain knowledge in android to configure policies reasonably. Experimental results show that users can easily protect their privacy, security and save energy of mobile devices through this system.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985276"}, {"primary_key": "4165341", "vector": [], "sparse_vector": [], "title": "VALI - an SDN-based management framework for public wireless LANs: poster.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Usage of WiFi is becoming increasingly popular in public wireless LAN (WLAN) settings like malls, airports and train stations. Similarly to other prominent examples of WiFi usage like enterprise and home settings, public WLANs could also benefit from an SDN-based coordinated management framework that deals with issues like interference and mobility management. However, unlike these settings, public WLANs present a few differences in their characteristics, such as the need to offer location-aware services and dynamic categorization of users, and the consequent need to provide sophisticated association strategies. Motivated by this we propose VALI, an SDN management framework tailored to meet the needs of public WLAN settings. We give an overview of VALI and present initial results obtained using our prototype implementation deployed over a testbed that resembles a realistic public WLAN environment. Our results demonstrate that VALI is a promising solution that could be used to effectively manage public WLAN settings and enable location-aware WiFi access.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985269"}, {"primary_key": "4165344", "vector": [], "sparse_vector": [], "title": "FMNC - rapid and accurate wifi characterization: demo.", "authors": ["<PERSON><PERSON> Song", "<PERSON>"], "summary": "A key part of achieving a reasonable Quality of Experience (QoE) for the mobile user is the ability to properly assess the quality of the available network capacity. Unfortunately, most existing techniques are ill-suited to wireless characterization, either providing fast but grossly inaccurate results or providing accurate results at the cost of time and bandwidth. To meet this need, we developed Fast Mobile Network Characterization (FMNC). FMNC operates within a web fetch and provides an accurate available bandwidth assessment within 250 ms while consuming less than 100 KB of data. In our demo, we will show the mobile clients for FMNC including apps for Android / iOS, the server back end, and a REST API. We will also show visualization of our ongoing longitudinal dataset of nearly one hundred users.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985619"}, {"primary_key": "4165346", "vector": [], "sparse_vector": [], "title": "Assessing header impacts in soccer with smartball: poster.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>ang <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to the popularity of soccer and the purposeful use of the head during play, traumatic brain injury to soccer players has been a concern for decades. However, there is a sense of urgency now in understanding and preventing concussions better, due to raising public awareness. Towards that end, intra-oral devices such as Vector MouthGuards are being studied [3] to measure the athlete's head's linear and rotational accelerations from impacts experienced in practices and games. But given the players' natural distaste for such intra-oral devices, more palatable alternatives for head impact monitoring are being developed [2]. X2 Biosystems xPatchis an electronic skin patch thatis worn behind the ear. Reebok Checklight embeds the impact sensor in the back of a skullcap which can be worn with or without a helmet. Triax SIM-P is placed inside a headband for non-helmeted sports and a skullcap for helmeted sports. While all these devices are much more convenient to wear than intra-oral devices, it is yet to be seen whether they gain wider acceptance, particularly by the millions of amateur soccer players all over the world.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985262"}, {"primary_key": "4165347", "vector": [], "sparse_vector": [], "title": "Practical MU-MIMO user selection on 802.11ac commodity networks.", "authors": ["Sanjib Sur", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Multi-User MIMO, the hallmark of IEEE 802.11ac and the upcoming 802.11ax, promises significant throughput gains by supporting multiple concurrent data streams to a group of users. However, identifying the best-throughput MU-MIMO groups in commodity 802.11ac networks poses three major challenges: a) Commodity 802.11ac users do not provide full CSI feedback, which has been widely used for MU-MIMO grouping. b) Heterogeneous channel bandwidth users limit grouping opportunities. c) Limited-resource on APs cannot support computationally and memory expensive operations, required by existing algorithms. Hence, state-of-the-art designs are either not portable in 802.11ac APs, or perform poorly, as shown by our testbed experiments. In this paper, we design and implement MUSE, a lightweight user grouping algorithm, which addresses the above challenges. Our experiments with commodity 802.11ac testbeds show MUSE can achieve high throughput gains over existing designs.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973758"}, {"primary_key": "4165348", "vector": [], "sparse_vector": [], "title": "Proteus: a network service control platform for service evolution in a mobile software defined infrastructure.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present Proteus, a mobile network service control platform to enable safe and rapid evolution of services in a mobile software defined infrastructure (SDI). Proteus allows for network service and network component functionality to be specified in templates. These templates are used by the Proteus orchestrator to realize and modify service instances based on the specifics of a service creation request and the availability of resources in the mobile SDI and allows for service specific policies to be implemented. We evaluate our Proteus prototype in a realistic mobile networking testbed illustrating its ability to support service evolution.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973757"}, {"primary_key": "4165350", "vector": [], "sparse_vector": [], "title": "The darkLight rises: visible light communication in the dark.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Visible Light Communication (VLC) emerges as a new wireless communication technology with appealing benefits not present in radio communication. However, current VLC designs commonly require LED lights to emit shining light beams, which greatly limits the applicable scenarios of VLC (e.g., in a sunny day when indoor lighting is not needed). It also entails high energy overhead and unpleasant visual experiences for mobile devices to transmit data using VLC. We design and develop DarkLight, a new VLC primitive that allows light-based communication to be sustained even when LEDs emit extremely-low luminance. The key idea is to encode data into ultra-short, imperceptible light pulses. We tackle challenges in circuit designs, data encoding/decoding schemes, and DarkLight networking, to efficiently generate and reliably detect ultra-short light pulses using off-the-shelf, low-cost LEDs and photodiodes. Our DarkLight prototype supports 1.3-m distance with 1.6-Kbps data rate. By loosening up VLC's reliance on visible light beams, DarkLight presents an unconventional direction of VLC design and fundamentally broadens VLC's application scenarios.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973772"}, {"primary_key": "4165351", "vector": [], "sparse_vector": [], "title": "The darklight rises: visible light communication in the dark: demo.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Visible Light Communication (VLC) emerges as a new wireless communication technology with appealing benefits not present in radio communication. However, current VLC designs commonly require LED lights to emit perceptible light beams, which greatly limits the applicable scenarios of VLC (e.g., in a sunny day when indoor lighting is not needed), and brings high energy overhead and unpleasant visual experiences for mobile devices to transmit data using VLC. We design and develop DarkLight, a new VLC primitive that allows light-based communication to be sustained even when LEDs emit extremely-low luminance. The key idea is to encode data into ultra-short, imperceptible light pulses. We tackle challenges in circuit designs, data encoding/decoding schemes, and DarkLight networking, to efficiently generate and reliably detect ultra-short light pulses using off-the-shelf, low-cost LEDs and photodiodes. Our DarkLight prototype supports 1.3-m distance with 1.6-Kbps data rate. By loosening up VLC's reliance on visible light beams, DarkLight presents an unconventional direction of VLC and fundamentally broadens VLC's application scenarios.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2987384"}, {"primary_key": "4165358", "vector": [], "sparse_vector": [], "title": "LiFS: low human-effort, device-free localization with fine-grained subcarrier information.", "authors": ["<PERSON>", "Hongbo Jiang", "<PERSON><PERSON>", "<PERSON>", "Xiao<PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Device-free localization of people and objects indoors not equipped with radios is playing a critical role in many emerging applications. This paper presents an accurate model-based device-free localization system LiFS, implemented on cheap commercial off-the-shelf (COTS) Wi-Fi devices. Unlike previous COTS device-based work, LiFS is able to localize a target accurately without offline training. The basic idea is simple: channel state information (CSI) is sensitive to a target's location and by modelling the CSI measurements of multiple wireless links as a set of power fading based equations, the target location can be determined. However, due to rich multipath propagation indoors, the received signal strength (RSS) or even the fine-grained CSI can not be easily modelled. We observe that even in a rich multipath environment, not all subcarriers are affected equally by multipath reflections. Our pre-processing scheme tries to identify the subcarriers not affected by multipath. Thus, CSIs on the \"clean\" subcarriers can be utilized for accurate localization.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973776"}, {"primary_key": "4165359", "vector": [], "sparse_vector": [], "title": "Device-free gesture tracking using acoustic signals: demo.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this demo, we present LLAP, a hand tracking system that uses ultrasound to localize the hand of the user to enable device-free gesture inputs. LLAP utilizes speakers and microphones on Commercial-Off-The-Shelf (COTS) mobile devices to play and record sound waves that are inaudible to humans. By measuring the phase of the sound signal reflected by the hands or fingers of the user, we can accurately measure the gesture movements. With a single pair of speaker/microphone, LLAP can track hand movement with accuracy of 3.5 mm. For devices with two microphones, LLAP enables drawing-in-the air capability with tracking accuracy of 4.6 mm. Moreover, the latency for LLAP is smaller than 15 ms for both the Android and the iOS platforms so that LLAP can be used for real-time applications.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2987385"}, {"primary_key": "4165360", "vector": [], "sparse_vector": [], "title": "Messages behind the sound: real-time hidden acoustic signal capture with smartphones.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Lu <PERSON>"], "summary": "With the ever-increasing use of smart devices, recent research endeavors have led to unobtrusive screen-camera communication channel designs, which allow simultaneous screen viewing and hidden screen-camera communication. Such practices, albeit innovative and effective, require well-controlled alignment of camera and screen and obstacle-free access.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973765"}, {"primary_key": "4165361", "vector": [], "sparse_vector": [], "title": "Real-time hidden acoustic signal capture with smartphones: demo.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Lu <PERSON>"], "summary": "With the ever-increasing use of smart devices, recent research endeavors have led to unobtrusive screen-camera communication channel designs, which allow simultaneous screen viewing and hidden screen-camera communication. Such practices, albeit innovative and effective, require well-controlled alignment of camera and screen and obstacle-free access. In this demo, we present Dolphin, a novel form of real-time acoustics-based dual-channel communication, which uses a speaker and the microphones on off-the-shelf smartphones to achieve concurrent audible and hidden communication. By leveraging masking effects of the human auditory system and readily available audio signals in our daily lives, <PERSON><PERSON> ensures real-time unobtrusive speaker-microphone data communication, while, at the same time, it overcomes the main limitations of existing screen-camera links.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985618"}, {"primary_key": "4165365", "vector": [], "sparse_vector": [], "title": "Gyro in the air: tracking 3D orientation of batteryless internet-of-things.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "3D orientation tracking is an essential ingredient for many Internet-of-Things applications. Yet existing orientation tracking systems commonly require motion sensors that are only available on battery-powered devices. In this paper, we propose Tagyro, which attaches an array of passive RFID tags as orientation sensors on everyday objects. Tagyro uses a closed-form model to transform the run-time phase offsets between tags into orientation angle. To enable orientation tracking in 3D space, we found the key challenge lies in the imperfect radiation pattern of practical tags, caused by the antenna polarity, non-isotropic emission and electromagnetic coupling, which substantially distort phase measurement. We address these challenges by designing a set of phase sampling and recovery algorithms, which together enable reliable orientation sensing with 3 degrees of freedom. We have implemented a real-time version of Tagyro on a commodity RFID system. Our experiments show that Tagyro can track the 3D orientation of passive objects with a small error of 4°, at a processing rate of 37.7 samples per second.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973761"}, {"primary_key": "4165366", "vector": [], "sparse_vector": [], "title": "Tracking orientation of batteryless internet-of-things using RFID tags: demo.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Orientation tracking is an essential ingredient for many Internet-of-Things applications. In this work, we introduce Tagyro, which attaches an array of passive RFID tags as orientation sensors on everyday objects. Tagyro uses a closed-form model to transform the run-time phase offsets between tags into orientation angle, and addresses the unexpected deviation of phase measurement distorted by imperfect radiation pattern of practical tags. We have implemented a real-time version of Tagyro on a commodity RFID system. In this demo, we show that Tagyro can handle the phase distortion by sensing the effective layout tag array, an use the sensed layout to track the orientation of objects in high accuracy.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985613"}, {"primary_key": "4165369", "vector": [], "sparse_vector": [], "title": "ToneSense: communication across technologies through power-channel: poster.", "authors": ["Xianjin Xia", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents ToneSense, a new paradigm for communication between devices using different wireless technologies. ToneSense encodes information into the transmission power levels of regular frames. Receivers sense the power strength and decode as meaningful information bits. Evaluations show that ToneSense achieves ≤ 4% symbol error rate. Our work sheds light on wireless coexistence problems and cross-technology communications.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985271"}, {"primary_key": "4165371", "vector": [], "sparse_vector": [], "title": "RnB: rate and brightness adaptation for rate-distortion-energy tradeoff in HTTP adaptive streaming over mobile devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Video streaming is a prevalent mobile service that drains a significant amount of battery power. While various efforts have been made toward saving both video transfer and display energy, they are independently designed in an ad-hoc way and thereby can cause some non-apparent yet critical performance issues. To fill in this gap, this paper presents a fundamentally new design by jointly considering the end-to-end pipeline from the initial video encoding to the final mobile display. In essence, we shift the classic R-D tradeoff that has governed streaming system designs for decades to a fresh rate-distortion-energy (R-D-E) tradeoff specifically tailored for mobile devices. We present RnB, a video bitrate and display brightness adaptation platform that is standard-compliant, backward compatible, and device-neutral in order to achieve the proposed R-D-E tradeoff. RnB is empowered by some new discovery about the inherent relationship among bitrate, display brightness, and video quality as well as by an control-theoretic formulation to dynamically adapt the bitrate and scale the display brightness. Experimental results based on real-time implementation show that RnB can achieve an average of 19% energy reduction with final video quality comparable to conventional R-D based schemes.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973780"}, {"primary_key": "4165372", "vector": [], "sparse_vector": [], "title": "Making sense of mechanical vibration with COTS RFID systems: demo.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lin", "<PERSON>"], "summary": "Traditional vibration inspection systems, equipped with separated sensing and communication modules, are either very expensive (e.g. hundreds of dollars) and/or suffer from occlusion and narrow field of view (e.g. laser). This demo brings forward a concept of 'communication is sensing', which is to make sense of the world purely based on communication carrier rather than specialized sensors. In this demo, we present an RFID-based solution, called Tagbeat, to inspect mechanical vibration using COTS RFID tags and readers. We implement our system using a COTS RFID device and evaluate it with a commercial centrifugal machine. Empirical benchmarks with a prototype show that our system can inspect the vibration period with a mean accuracy of 0.36ms and a relative error rate of 0.03%.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985615"}, {"primary_key": "4165373", "vector": [], "sparse_vector": [], "title": "Making sense of mechanical vibration period with sub-millisecond accuracy using backscatter signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lin", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Traditional vibration inspection systems, equipped with separated sensing and communication modules, are either very expensive (e.g., hundreds of dollars) and/or suffer from occlusion and narrow field of view (e.g., laser). In this work, we present an RFID-based solution, Tagbeat, to inspect mechanical vibration using COTS RFID tags and readers. Making sense of micro and high-frequency vibration using random and low-frequency readings of tag has been a daunting task, especially challenging for achieving sub-millisecond period accuracy. Our system achieves these three goals by discerning the change pattern of backscatter signal replied from the tag, which is attached on the vibrating surface and displaced by the vibration within a small range. This work introduces three main innovations. First, it shows how one can utilize COTS RFID to sense mechanical vibration and accurately discover its period with a few periods of short and noisy samples. Second, a new digital microscope is designed to amplify the micro-vibration-induced weak signals. Third, Tagbeat introduces compressive reading to inspect high-frequency vibration with relatively low RFID read rate. We implement Tagbeat using a COTS RFID device and evaluate it with a commercial centrifugal machine. Empirical benchmarks with a prototype show that Tagbeat can inspect the vibration period with a mean accuracy of 0.36ms and a relative error rate of 0.03%. We also study three cases to demonstrate how to associate our inspection solution with the specific domain requirements.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973759"}, {"primary_key": "4165374", "vector": [], "sparse_vector": [], "title": "SALVE: server authentication with location verification.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Location Service (LCS) proposed by the telecommunication industry is an architecture that allows the location of mobile devices to be accessed in various applications. We explore the use of LCS in location-enhanced server authentication, which traditionally relies on certificates. Given recent incidents involving certificate authorities, various techniques to strengthen server authentication were proposed. They focus on improving the certificate validation process, such as pinning, revocation, or multi-path probing. In this paper, we propose using the server's geographic location as a second factor of its authenticity. Our solution, SALVE, achieves location-based server authentication by using secure DNS resolution and by leveraging LCS for location measurements. We develop a TLS extension that enables the client to verify the server's location in addition to its certificate. Successful server authentication therefore requires a valid certificate and the server's presence at a legitimate geographic location, e.g., on the premises of a data center. SALVE prevents server impersonation by remote adversaries with mis-issued certificates or stolen private keys of the legitimate server. We develop a prototype implementation and our evaluation in real-world settings shows that it incurs minimal impact to the average server throughput. Our solution is backward compatible and can be integrated with existing approaches for improving server authentication in TLS.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973766"}, {"primary_key": "4165377", "vector": [], "sparse_vector": [], "title": "DopEnc: acoustic-based encounter profiling using smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents DopEnc, an acoustic-based encounter profiling system on smartphones. DopEnc can automatically identify the persons that users interact with in the context of encountering. DopEnc performs encounter profiling in two major steps: (1) Doppler profiling to detect that two persons approach and stop in front of each other via an effective trajectory, and (2) voice profiling to confirm that they are thereafter engaged in an interactive conversation. DopEnc is further extended to support parallel acoustic exploration of many users by incorporating a unique multiple access scheme within the limited inaudible acoustic frequency band. All implementation of DopEnc is based on commodity sensors like speakers, microphones and accelerometers integrated on commercial-off-the-shelf smartphones. We evaluate DopEnc with detailed experiments and a real use-case study of 11 participants. Overall DopEnc achieves an accuracy of 6.9% false positive and 9.7% false negative in real usage.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973775"}, {"primary_key": "4165378", "vector": [], "sparse_vector": [], "title": "LiTell: robust indoor localization using unmodified light fixtures.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Owing to dense deployment of light fixtures and multipath-free propagation, visible light localization technology holds potential to overcome the reliability issue of radio localization. However, existing visible light localization systems require customized light hardware, which increases deployment cost and hinders near term adoption. In this paper, we propose LiTell, a simple and robust localization scheme that employs unmodified fluorescent lights (FLs) as location landmarks and commodity smartphones as light sensors. LiTell builds on the key observation that each FL has an inherent characteristic frequency which can serve as a discriminative feature. It incorporates a set of sampling, signal amplification and camera optimization mechanisms, that enable a smartphone to capture the extremely weak and high frequency ( > 80 kHz) features. We have implemented LiTell as a real-time localization and navigation system on Android. Our experiments demonstrate LiTell's high reliability in discriminating different FLs, and its potential to achieve sub-meter location granularity. Our user study in a multi-storey office building, parking lot and grocery store further validates LiTell as an accurate, robust and ready-to-use indoor localization system.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973767"}, {"primary_key": "4165379", "vector": [], "sparse_vector": [], "title": "LiTell: indoor localization using unmodified light fixtures: demo.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Owing to dense deployment of light fixtures and multipath-free propagation, visible light localization technology holds potential to overcome the reliability issue of radio localization. However, existing visible light localization systems require customized light hardware, which increases deployment cost and hinders near term adoption. We present LiTell, a simple and robust localization scheme that employs unmodified fluorescent lights (FLs) as location landmarks and commodity smartphones as light sensors. LiTell builds on the key observation that each FL has an inherent characteristic frequency, which can serve as a discriminative feature. It incorporates a set of sampling, signal amplification and camera optimization mechanisms, that enable a smartphone to capture the extremely weak and high frequency (> 80 kHz) features. We have implemented LiTell as a real-time localization and navigation system on Android. In our experiments, LiTell demonstrates high reliability in discriminating different FLs, and great potential to achieve sub-meter granularity.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985612"}, {"primary_key": "4165380", "vector": [], "sparse_vector": [], "title": "SEEM: simulation experimental environments for mobile applications in MANETs: poster.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In some special circumstances, such as earthquakes, tsunamis and floods, etc. Infrastructure communication facilities are damaged, all communications are interrupted. For the communication between peoples, Android smartphones can be used to construct Mobile Ad Hoc Networks (MANETs). To improve the work efficiency, it is necessary to run the Information Systems (Android Applications) in MANETs, therefore the distribution of information becomes convenient. However, it is very hard to get a real MANET environment to test Android Applications, and so far, we have not found any MANETs simulation environments which can be used to test actual Android Applications. Therefore, we propose a Simulation Experimental Environment for Android Applications in MANETs (SEEM). The test results show that the SEEM is practicable to test Android Applications in MANETs. We believe that the SEEM will be beneficial to the researchers and developers who need to develop and test actual Android Applications in MANETs.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985274"}, {"primary_key": "4165381", "vector": [], "sparse_vector": [], "title": "MSN: a mobility-enhanced satellite network architecture: poster.", "authors": ["<PERSON><PERSON><PERSON>", "Baokang Zhao", "<PERSON><PERSON><PERSON><PERSON>", "Wanrong Yu", "Chunqing Wu"], "summary": "The proposed MSN architecture is intended to directly address the challenge of mobility, which refers to the motion of users as well as the dynamics of the satellite constellation. A virtual access point layer consisting of fixed virtual satellite network attachment points is superimposed over the physical topology in order to hide the mobility of satellites from the mobile endpoints. Then the MSN enhances endpoint mobility by a clean separation of identity and logical network location through an identity-to-location resolution service, and taking full advantage of the user's geographical location information. Moreover, a SDN based implementation is presented to further illustrate the proposal.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985279"}, {"primary_key": "4165382", "vector": [], "sparse_vector": [], "title": "OpenMili: a 60 GHz software radio platform with a reconfigurable phased-array antenna.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The 60 GHz wireless technology holds great potential for multi-Gbps communications and high-precision radio sensing. But the lack of an accessible experimental platform has been impeding its progress. In this paper, we overcome the barrier with OpenMili, a reconfigurable 60 GHz radio architecture. OpenMili builds from off-the-shelf FPGA processor, data converters and 60 GHz RF front-end. It employs customized clocking, channelization and interfacing modules, to achieve Gsps sampling bandwidth, Gbps wireless bit-rate, and Gsps sample streaming from/to a PC host. It also incorporates the first programmable, electronically steerable 60 GHz phased-array antenna. OpenMili adopts programming models that ease development, through automatic parallelization inside signal processing blocks, and modular, rate-insensitive interfaces across blocks. It provides common reference designs to bootstrap the development of new network protocols and sensing applications. We verify the effectiveness of OpenMili through benchmark communication/sensing experiments, and showcase its usage by prototyping a pairwise phased-array localization scheme, and a learning-assisted real-time beam adaptation protocol.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973760"}, {"primary_key": "4165383", "vector": [], "sparse_vector": [], "title": "OpenMili: a 60 GHz software radio with a programmable phased-array antenna: demo.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The 60 GHz wireless technology holds great potential for multi-Gbps communications and high-precision radio sensing. But the lack of an accessible experimental platform has been impeding its progress. We propose to overcome the barrier with OpenMili, a reconfigurable 60 GHz radio architecture. OpenMili builds from off-the-shelf FPGA processor, data converters and 60 GHz RF front-end. It employs customized clocking, channelization and interfacing modules, to achieve Gsps sampling bandwidth, Gbps wireless bit-rate, and Gsps sample streaming from/to a PC host. It also incorporates the first programmable, electronically steerable 60 GHz phased-array antenna. OpenMili adopts programming models that ease development, through automatic parallelization inside signal processing blocks, and modular, rate-insensitive interfaces across blocks. In this demo, we will showcase OpenMili's hardware modules, and demonstrate example communication and sensing applications based on it.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985614"}, {"primary_key": "4165385", "vector": [], "sparse_vector": [], "title": "Robust respiration monitoring using low-cost doppler sensor and wireless network: poster.", "authors": ["<PERSON>"], "summary": "This work proposes a novel respiration monitoring system using Doppler signal from a low-cost motion sensor and received signal strength (RSS) measurements from a wireless network. We present the ambiguity problem of the Doppler monitoring system. We find that RSS from a wireless network is complimentary to Doppler signal, and we propose to integrate Doppler with RSS to make respiration monitoring more robust. Our experimental results show that the respiration rate estimation is more accurate by sensor fusion of these two radio frequency (RF) sensing modalities.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985282"}, {"primary_key": "4165386", "vector": [], "sparse_vector": [], "title": "Emotion recognition using wireless signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper demonstrates a new technology that can infer a person's emotions from RF signals reflected off his body. EQ-Radio transmits an RF signal and analyzes its reflections off a person's body to recognize his emotional state (happy, sad, etc.). The key enabler underlying EQ-Radio is a new algorithm for extracting the individual heartbeats from the wireless signal at an accuracy comparable to on-body ECG monitors. The resulting beats are then used to compute emotion-dependent features which feed a machine-learning emotion classifier. We describe the design and implementation of EQ-Radio, and demonstrate through a user study that its emotion recognition accuracy is on par with state-of-the-art emotion recognition systems that require a person to be hooked to an ECG monitor.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973762"}, {"primary_key": "4165388", "vector": [], "sparse_vector": [], "title": "BASIC: backbone-assisted successive interference cancellation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To meet the growing demand for wireless data, it is time to move away from the age-old paradigm of prohibiting interfering nodes from transmissions. Instead, through proactive management of interference among multiple colliding packets, we can design high throughput wireless systems. This is well explored in the Information Theory community and there are also a few implementational efforts that have been recently reported. The existing solutions are nontrivial to use in real systems as they require either tight time/frequency synchronization or exchange of data between transmitters prior to the transmissions. These requirements are hard to meet in practice especially for uplink transmissions. This paper proposes BASIC, a lightweight multi-user uplink transmission strategy that does not require tight synchronization or exchange of samples among nodes, which makes it an attractive alternative compared to its counterparts. BASIC exploits receiver diversity by controlling the data rates of the clients. A novel greedy algorithm is proposed for data rate selection. We implement BASIC on a software-defined radio platform. Our experiments on a real testbed show that BASIC outperforms TDMA by 48% in terms of overall throughput. Our trace-driven simulations show up to 4.8x gain in throughput with similar flow fairness.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2973756"}, {"primary_key": "4165390", "vector": [], "sparse_vector": [], "title": "A transfer kernel learning based strategy for adaptive localization in dynamic indoor environments: poster.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Li<PERSON> Xie", "<PERSON><PERSON>"], "summary": "Existing WiFi fingerprinting-based Indoor Positioning System (IPS) suffers from the vulnerability of environmental dynamics. To address this issue, we propose TKL-WinSMS as a systematic strategy, which is able to realize robust and adaptive localization in dynamic indoor environments. We developed a WiFi-based Non-intrusive Sensing and Monitoring System (WinSMS) that enables COTS WiFi routers as online reference points by extracting real-time RSS readings among them. With these online data and labeled source data from the offline calibrated radio map, we further combine the RSS readings from target mobile devices as unlabeled target data, to design a robust localization model using an emerging transfer learning algorithm, namely transfer kernel learning (TKL). It can learn a domain-invariant kernel by directly matching the source and target distributions in the reproducing kernel Hilbert space instead of the raw noisy signal space. By leveraging the resultant kernel as input for the SVR training, the trained localization model can inherit the information from online phase to adaptively enhance the offline calibrated radio map. Extensive experimental results verify the superiority of TKL-WinSMS in terms of localization accuracy compared with existing solutions in dynamic indoor environments.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/2973750.2985278"}, {"primary_key": "4210695", "vector": [], "sparse_vector": [], "title": "Proceedings of the 22nd Annual International Conference on Mobile Computing and Networking, MobiCom 2016, New York City, NY, USA, October 3-7, 2016", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The MobiCom conference series serves as a highly selective, premier international forum addressing networks, systems, algorithms, and applications that support mobile computers and wireless networks.", "published": "2016-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}]