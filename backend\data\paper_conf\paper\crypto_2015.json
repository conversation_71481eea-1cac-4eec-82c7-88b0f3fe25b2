[{"primary_key": "4387383", "vector": [], "sparse_vector": [], "title": "Secure Computation from Leaky Correlated Randomness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Correlated secret randomness is an essential resource for information-theoretic cryptography. In the context of secure two-party computation, the high level of efficiency achieved by information-theoretic protocols has motivated a paradigm of starting with correlated randomness, specifically random oblivious transfer (OT) correlations. This correlated randomness can be generated and stored during an offline preprocessing phase, long before the inputs are known. But what if some information about the correlated randomness is leaked to an adversary or to the other party? Can we still recover “fresh” correlated randomness after such leakage has occurred? This question is a direct analog of the classical question of privacy amplification, which addresses the case of asharedrandom secret key, in the setting ofcorrelatedrandom secrets. Remarkably, despite decades of study of OT-based secure computation, very little is known about this question. In particular, the question of how much leakage is tolerable when recovering OT correlations has remained wide open. In our work, we resolve this question. Prior to our work, the work of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (FOCS 2009) obtained an initial feasibility result, tolerating only a tiny constant leakage rate. In our work, we show that starting withnrandom OT correlations, where each party holds 2nbits, up to\\((1-\\epsilon )\\frac{n}{2}\\)bits of leakage are tolerable. This result is optimal, by known negative results on OT combiners. We then ask the same question for other correlations: is there a correlation that is more leakage-resilient than OT correlations, and also supports secure computation? We answer in the affirmative, by showing that there exists a correlation that can tolerate up to\\(1/2-\\epsilon \\)fractional leakage, for any\\(\\epsilon >0\\)(compared to the optimal 1/4 fractional leakage for OT correlations).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_34"}, {"primary_key": "4387384", "vector": [], "sparse_vector": [], "title": "An Algebraic Framework for Pseudorandom Functions and Applications to Related-Key Security.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we provide a new algebraic framework for pseudorandom functions which encompasses many of the existing algebraic constructions, including the ones by <PERSON><PERSON> and <PERSON><PERSON> (FOCS’97), by <PERSON><PERSON><PERSON> and <PERSON> (CCS’09), and by <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (CCS’10), as well as the related-key-secure pseudorandom functions by <PERSON><PERSON> and <PERSON> (Crypto’10) and by <PERSON><PERSON><PERSON><PERSON>.(Crypto’14). To achieve this goal, we introduce two versions of our framework. The first, termed linearly independent polynomial security, states that the values\\((g^{P_1(\\vec {a})}, \\ldots , g^{P_q({\\vec {a}})})\\)are indistinguishable from a random tuple of the same size, when\\(P_1, \\ldots , P_q\\)are linearly independent multivariate polynomials of the secret key vector\\({\\vec {a}}\\). The second, which is a natural generalization of the first framework, additionally deals with constructions based on the decision linear and matrix <PERSON><PERSON><PERSON><PERSON> assumptions. In addition to unifying and simplifying proofs for existing schemes, our framework also yields new results, such as related-key security with respect to arbitrary permutations of polynomials. Our constructions are in the standard model and do not require the existence of multilinear maps.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_19"}, {"primary_key": "4387385", "vector": [], "sparse_vector": [], "title": "Explicit Non-malleable Codes Against Bit-Wise Tampering and Permutations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A non-malleable code protects messages against various classes of tampering. Informally, a code is non-malleable if the message contained in a tampered codeword is either the original message, or a completely unrelated one. Although existence of such codes for various rich classes of tampering functions is known,explicitconstructions exist only for “compartmentalized” tampering functions: i.e. the codeword is partitioned intoa priori fixedblocks and each block canonly be tampered independently. The prominent examples of this model are the family of bit-wise independent tampering functions and the split-state model. In this paper, for the first time we construct explicit non-malleable codes against a natural class of non-compartmentalized tampering functions. We allow the tampering functions topermute the bitsof the codeword and (optionally) perturb them by flipping or setting them to 0 or 1. We construct an explicit, efficient non-malleable code for arbitrarily long messages in this model (unconditionally). We give an application of our construction to non-malleable commitments, as one of the first direct applications of non-malleable codes to computational cryptography. We show that non-malleablestringcommitments can be “entirely based on” non-malleablebitcommitments.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_26"}, {"primary_key": "4387386", "vector": [], "sparse_vector": [], "title": "Incoercible Multi-party Computation and Universally Composable Receipt-Free Voting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Composable notions of incoercibility aim to forbid a coercer from using anything beyond the coerced parties’ inputs and outputs to catch them when they try to deceive him. Existing definitions are restricted to weak coercion types, and/or are not universally composable. Furthermore, they often make too strong assumptions on the knowledge of coerced parties—e.g., they assume they known the identities and/or the strategies of other coerced parties, or those of corrupted parties—which makes them unsuitable for applications of incoercibility such as e-voting, where colluding adversarial parties may attempt to coerce honest voters, e.g., by offering them money for a promised vote, and use their own view to check that the voter keeps his end of the bargain. In this work we put forward the first universally composable notion of incoercible multi-party computation, which satisfies the above intuition and does not assume collusions among coerced parties or knowledge of the corrupted set. We define natural notions of UC incoercibility corresponding to standard coercion-types, i.e., receipt-freeness and resistance to full-active coercion. Importantly, our suggested notion has the unique property that it buildson topof the well studied UC framework by <PERSON><PERSON> instead of modifying it. This guarantees backwards compatibility, and allows us to inherit results from the rich UC literature. We then present MPC protocols which realize our notions of UC incoercibility given access to an arguably minimal setup—namely honestly generate tamper-proof hardware performing a very simple cryptographic operation—e.g., a smart card. This is, to our knowledge, the first proposed construction of an MPC protocol (for more than two parties) that is incoercibly secureanduniversally composable, and therefore the first construction of a universally composable receipt-free e-voting protocol.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_37"}, {"primary_key": "4387387", "vector": [], "sparse_vector": [], "title": "From Selective to Adaptive Security in Functional Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In a functional encryption (FE) scheme, the owner of the secret key can generate restricted decryption keys that allow users to learn specific functions of the encrypted messages and nothing else. In many known constructions of FE schemes, security is guaranteed only for messages that are fixed ahead of time (i.e., before the adversary even interacts with the system). This so-calledselective securityis too restrictive for many realistic applications. Achievingadaptive security(also calledfull security), where security is guaranteed even for messages that are adaptively chosen at any point in time, seems significantly more challenging. The handful of known adaptively-secure schemes are based on specifically tailored techniques that rely on strong assumptions (such as obfuscation or multilinear maps assumptions). We show that any sufficiently-expressiveselectively-secureFE scheme can be transformed into anadaptively-secureone without introducing any additional assumptions. We present a black-box transformation, for both public-key and private-key schemes, making novel use ofhybrid encryption, a classical technique that was originally introduced for improving the efficiency of encryption schemes. We adapt the hybrid encryption approach to the setting of functional encryption via a technique for embedding a “hidden execution thread” in the decryption keys of the underlying scheme, which will only be activated within the proof of security of the resulting scheme. As an additional application of this technique, we show how to construct functional encryption schemes for arbitrary circuits starting from ones for shallow circuits (NC1 or even TC0).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_32"}, {"primary_key": "4387388", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation from Compact Functional Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The arrival of indistinguishability obfuscation (\\(i\\mathrm {O}\\)) has transformed the cryptographic landscape by enabling several security goals that were previously beyond our reach. Consequently, one of the pressing goals currently is to construct\\(i\\mathrm {O}\\)from well-studied standard cryptographic assumptions. In this work, we make progress in this direction by presenting a reduction from\\(i\\mathrm {O}\\)to a natural form of public-key functional encryption (FE). Specifically, we construct\\(i\\mathrm {O}\\)for general functions from any single-key FE scheme for\\(\\mathsf {NC}^1\\)that achieves selective, indistinguishability security against sub-exponential time adversaries. Further, the FE scheme should becompact, namely, the running time of the encryption algorithm must only be a polynomial in the security parameter and the input message length (and not in the function description size or its output length). We achieve this result by developing a novelarity amplification techniqueto transform FE for single-ary functions into FE for multi-ary functions (aka multi-input FE). Instantiating our approach with known, non-compact FE schemes, we obtain the first constructions of multi-input FE for constant-ary functions based on standard assumptions. Finally, as a result of independent interest, we construct a compact FE scheme from randomized encodings for Turing machines and learning with errors assumption.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_15"}, {"primary_key": "4387389", "vector": [], "sparse_vector": [], "title": "PoW-Based Distributed Cryptography with No Trusted Setup.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by the recent success of Bitcoin we study the question of constructing distributed cryptographic protocols in a fully peer-to-peer scenario under the assumption that the adversary has limited computing power and there isnotrusted setup (like PKI, or an unpredictable beacon). We propose a formal model for this scenario and then we construct a broadcast protocol in it. This protocol is secure under the assumption that the honest parties have computing power that is some non-negligible fraction of computing power of the adversary (this fraction can be small, in particular it can be much less than 1 / 2), and a (rough) total bound on the computing power in the system is known. Using our broadcast protocol we construct a protocol for simulating any trusted functionality. A simple application of the broadcast protocol is also a scheme for generating an unpredictable beacon (that can later serve, e.g., as a genesis block for a new cryptocurrency). Under a stronger assumption that the majority of computing power is controlled by the honest parties we construct a protocol for simulating any trusted functionality with guaranteed termination (i.e. that cannot be interrupted by the adversary). This could in principle be used as a provably-secure substitute of the blockchain technology used in the cryptocurrencies. Our main tool for verifying the computing power of the parties are the Proofs of Work (Dwork and Naor, CRYPTO 92). Our broadcast protocol is built on top of the classical protocol of <PERSON><PERSON> and <PERSON> (SIAM J. on Comp. 1983).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_19"}, {"primary_key": "4387390", "vector": [], "sparse_vector": [], "title": "Implicit Zero-Knowledge Arguments and Applications to the Malicious Setting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We introduceimplicit zero-knowledgearguments (\\(\\mathsf{iZK }\\)) and simulation-sound variants thereof (\\(\\mathsf{SSiZK }\\)); these are lightweight alternatives to zero-knowledge arguments for enforcing semi-honest behavior. Our main technical contribution is a construction of efficient two-flow\\(\\mathsf{iZK }\\)and\\(\\mathsf{SSiZK }\\)protocols for a large class of languages under the (plain)\\(\\mathsf{DDH }\\)assumption in cyclic groups in the common reference string model. As an application of\\(\\mathsf{iZK }\\), we improve upon the round-efficiency of existing protocols for securely computing inner product under the\\(\\mathsf{DDH }\\)assumption. This new protocol in turn provides privacy-preserving biometric authentication with lower latency.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_6"}, {"primary_key": "4387391", "vector": [], "sparse_vector": [], "title": "On Reverse-Engineering S-Boxes with Hidden Design Criteria or Structure.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "S-Boxes are the key components of many cryptographic primitives and designing them to improve resilience to attacks such as linear or differential cryptanalysis is well understood. In this paper, we investigate techniques that can be used to reverse-engineer S-box design and illustrate those by studying the S-BoxFof the Skipjack block cipher whose design process so far remained secret. We first show that the linear properties ofFare far from random and propose a design criteria, along with an algorithm which generates S-Boxes very similar to that of Skipjack. Then we consider more general S-box decomposition problems and propose new methods for decomposing S-Boxes built from arithmetic operations or as a Feistel Network of up to 5 rounds. Finally, we develop an S-box generating algorithm which can fix a large number of DDT entries to the values chosen by the designer. We demonstrate this algorithm by embedding images into the visual representation of S-box’s DDT.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_6"}, {"primary_key": "4387392", "vector": [], "sparse_vector": [], "title": "Known-Key Distinguisher on Full PRESENT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this article, we analyse the known-key security of the standardizedPRESENTlightweight block cipher. Namely, we propose a known-key distinguisher on the fullPRESENT, both 80- and 128-bit key versions. We first leverage the very latest advances in differential cryptanalysis onPRESENT, which are as strong as the best linear cryptanalysis in terms of number of attacked rounds. Differential properties are much easier to handle for a known-key distinguisher than linear properties, and we use a bias on the number of collisions on some predetermined input/output bits as distinguishing property. In order to reach the fullPRESENT, we eventually introduce a new meet-in-the-middle layer to propagate the differential properties as far as possible. Our techniques have been implemented and verified on the small scale variant ofPRESENT. While the known-key security model is very generous with the attacker, it makes sense in practice sincePRESENThas been proposed as basic building block to design lightweight hash functions, where no secret is manipulated. Our distinguisher can for example apply to the compression function obtained by placingPRESENTin a Davies-Meyer mode. We emphasize that this is the very first attack that can reach the full number of rounds of thePRESENTblock cipher.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_22"}, {"primary_key": "4387393", "vector": [], "sparse_vector": [], "title": "Large-Scale Secure Computation: Multi-party Computation for (Parallel) RAM Programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present the first efficient (i.e., polylogarithmic overhead) method for securely and privately processing large data sets over multiple parties withparallel, distributed algorithms. More specifically, we demonstrate load-balanced, statistically secure computation protocols for computing Parallel RAM (PRAM) programs, handling\\((1/3 - \\epsilon )\\)fraction malicious players, while preserving up to polylogarithmic factors the computation, parallel time, and memory complexities of the PRAM program, aside from a one-time execution of a broadcast protocol per party. Additionally, our protocol has\\(\\mathsf{polylog}\\)communication locality—that is, each of thenparties speaks only with\\(\\mathsf{polylog}(n)\\)other parties.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_36"}, {"primary_key": "4387394", "vector": [], "sparse_vector": [], "title": "Quantum Homomorphic Encryption for Circuits of Low T-gate Complexity.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Fully homomorphic encryption is an encryption method with the property that any computation on the plaintext can be performed by a party having access to the ciphertext only. Here, we formally define and give schemes forquantumhomomorphic encryption, which is the encryption ofquantuminformation such thatquantumcomputations can be performed given the ciphertext only. Our schemes allow for arbitrary Clifford group gates, but become inefficient for circuits with large complexity, measured in terms of the non-Clifford portion of the circuit (we use the “\\(\\pi /8\\)” non-Clifford group gate, also known as the\\(\\mathsf{T}\\)-gate). More specifically, two schemes are proposed: the first scheme has a decryption procedure whose complexity scales with the square of thenumberof\\(\\mathsf{T}\\)-gates (compared with a trivial scheme in which the complexity scales with the total number of gates); the second scheme uses a quantum evaluation key of length given by a polynomial of degree exponential in the circuit’s\\(\\mathsf{T}\\)-gate depth, yielding a homomorphic scheme for quantum circuits with constant\\(\\mathsf{T}\\)-depth. Both schemes build on a classical fully homomorphic encryption scheme. A further contribution of ours is to formally define the security of encryption schemes for quantum messages: we definequantum indistinguishability under chosen plaintext attacksin both the public- and private-key settings. In this context, we show the equivalence of several definitions. Our schemes are the first of their kind that are secure under modern cryptographic definitions, and can be seen as a quantum analogue of classical results establishing homomorphic encryption for circuits with a limited number ofmultiplicationgates. Historically, such results appeared as precursors to the breakthrough result establishing classical fully homomorphic encryption.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_30"}, {"primary_key": "4387395", "vector": [], "sparse_vector": [], "title": "A Simpler Variant of Universally Composable Security for Standard Multiparty Computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present a simpler and more restricted variant of the universally composable security (UC) framework that is suitable for “standard” two-party and multiparty computation tasks. Many of the complications of the UC framework exist in order to enable more general tasks than classic secure computation. This generality may be a barrier to entry for those who are used to the stand-alone model of secure computation and wish to work with universally composable security but are overwhelmed by the differences. The variant presented here (called simplified universally composable security, or just SUC) is closer to the definition of security for multiparty computation in the stand-alone setting. The main difference is that a protocol in the SUC framework runs with a fixed set of parties, and machines cannot be added dynamically to the execution. As a result, the definitions of polynomial time and protocol composition are much simpler. In addition, the SUC framework has authenticated channels built in, as is standard in previous definitions of security, and all communication is done via the adversary in order to enable arbitrary scheduling of messages. Due to these differences, not all cryptographic tasks can be expressed in the SUC framework. Nevertheless, standard secure computation tasks (like secure function evaluation) can be expressed. Importantly, we show that for every protocol that can be represented in the SUC framework, the protocol is secure in SUC if and only if it is secure in UC. Therefore, the UC composition theorem holds and any protocol that is proven secure under SUC is secure under the general framework (with some technical changes to the functionality definition). As a result, protocols that are secure in the SUC framework are secure when an a priori unbounded number of concurrent executions of the protocols take place (relative to the same fixed set of parties).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_1"}, {"primary_key": "4387396", "vector": [], "sparse_vector": [], "title": "Concurrent Secure Computation with Optimal Query Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The multiple ideal query (MIQ) model [<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, Crypto’10] offers a relaxed notion of security for concurrent secure computation, where the simulator is allowed to query the ideal functionalitymultiple times per session(as opposed to just once in the standard definition). The model provides a quantitative measure for the degradation in security under concurrent self-composition, where the degradation is measured by the number of ideal queries. However, to date, all known MIQ-secure protocols guarantee only an overallaveragebound on the number of queries per session throughout the execution, thus allowing the adversary to potentially fully compromise some sessions of its choice. Furthermore, [<PERSON><PERSON> and <PERSON>, Eurocrypt’13] rule out protocols where the simulator makes only an adversary-independent constant number of ideal queries per session. We show the first MIQ-secure protocol with worst-case per-session guarantee. Specifically, we show a protocol for any functionality that matches the [GJ13] bound: The simulator makes only aconstantnumber of ideal queries ineverysession. The constant depends on the adversary but is independent of the security parameter. As an immediate corollary of our main result, we obtain the first password authenticated key exchange (PAKE) protocol for the fully concurrent, multiple password setting in the standard model with no set-up assumptions.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_3"}, {"primary_key": "4387397", "vector": [], "sparse_vector": [], "title": "Algebraic Decomposition for Probing Security.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The probing security model is very popular to prove the side-channel security of cryptographic implementations protected by masking. A common approach to secure nonlinear functions in this model is to represent them as polynomials over a binary field and to secure their nonlinear multiplications thanks to a method introduced by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> at Crypto 2003. Several schemes based on this approach have been published, leading to the recent proposal of <PERSON><PERSON>, <PERSON> and <PERSON> which is currently the best known method when no particular assumption is made on the algebraic structure of the function. In the present paper, we revisit this idea by trading nonlinear multiplications for low-degree functions. Specifically, we introduce an algebraic decomposition approach in which a nonlinear function is represented as a sequence of functions with low algebraic degrees. We therefore focus on the probing-secure evaluation of such low-degree functions and we introduce three novel methods to tackle this particular issue. The paper concludes with a comparative analysis of the proposals, which shows that our algebraic decomposition method outperforms the method of <PERSON><PERSON>, <PERSON> and <PERSON> in several realistic contexts.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_36"}, {"primary_key": "4387398", "vector": [], "sparse_vector": [], "title": "Programmable Hash Functions Go Private: Constructions and Applications to (Homomorphic) Signatures with Shorter Public Keys.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce the notion of asymmetric programmable hash functions (APHFs, for short), which adapts Programmable Hash Functions, introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> at Crypto 2008, with two main differences. First, an APHF works over bilinear groups, and it is asymmetric in the sense that, while onlysecretlycomputable, it admits an isomorphic copy which is publicly computable. Second, in addition to the usual programmability, APHFs may have an alternative property that we callprogrammable pseudorandomness. In a nutshell, this property states that it is possible to embed a pseudorandom value as part of the function’s output, akin to a random oracle. In spite of the apparent limitation of being only secretly computable, APHFs turn out to be surprisingly powerful objects. We show that they can be used to generically implement both regular and linearly-homomorphic signature schemes in a simple and elegant way. More importantly, when instantiating these generic constructions with our concrete realizations of APHFs, we obtain: (1) thefirstlinearly-homomorphic signature (in the standard model) whose public key issub-linearin both the dataset size and the dimension of the signed vectors; (2) short signatures (in the standard model) whose public key is shorter than those by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from Asiacrypt 2011, and essentially the same as those by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, (CT-RSA 2012).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_13"}, {"primary_key": "4387399", "vector": [], "sparse_vector": [], "title": "Distributions Attaining Secret Key at a Rate of the Conditional Mutual Information.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we consider the problem of extracting secret key from an eavesdropped source\\(p_{XYZ}\\)at a rate given by the conditional mutual information. We investigate this question under three different scenarios: (i) <PERSON> (X) and <PERSON> (<PERSON>) are unable to communicate but share common randomness with the eavesdropper <PERSON> (Z), (ii) <PERSON> and <PERSON> are allowed one-way public communication, and (iii) <PERSON> and <PERSON> are allowed two-way public communication. Distributions having a key rate of the conditional mutual information are precisely those in which a “helping” <PERSON> offers <PERSON> and <PERSON> no greater advantage for obtaining secret key than a fully adversarial one. For each of the above scenarios, strong necessary conditions are derived on the structure of distributions attaining a secret key rate ofI(X:Y|Z). In obtaining our results, we completely solve the problem of secret key distillation under scenario (i) and identifyH(S|Z) to be the optimal key rate using shared randomness, whereSis the Gács-Körner Common Information. We thus provide an operational interpretation of the conditional Gács-Körner Common Information. Additionally, we introduce simple example distributions in which the rateI(X:Y|Z) is achievable if and only if two-way communication is allowed.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_22"}, {"primary_key": "4387400", "vector": [], "sparse_vector": [], "title": "Constant-Round Concurrent Zero-Knowledge from Indistinguishability Obfuscation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a constant-round concurrent zero-knowledge protocol for\\({\\mathsf {NP}} \\). Our protocol relies on the existence of families of collision-resistant hash functions, one-way permutations, and indistinguishability obfuscators for\\(\\mathbf{P}/poly\\)(with slightly super-polynomial security).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_14"}, {"primary_key": "4387401", "vector": [], "sparse_vector": [], "title": "Multi-identity and Multi-key Leveled FHE from Learning with Errors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON> recently presented the first (leveled) identity-based fully homomorphic (IBFHE) encryption scheme (CRYPTO 2013). Their scheme however only works in the single-identity setting; that is, homomorphic evaluation can only be performed on ciphertexts created with the same identity. In this work, we extend their results to the multi-identity setting and obtain a multi-identity IBFHE scheme that is selectively secure in the random oracle model under the hardness of Learning with Errors (LWE). We also obtain a multi-key fully-homomorphic encryption (FHE) scheme that is secure under LWE in the standard model. This is the first multi-key FHE based on a well-established assumption such as standard LWE. The multi-key FHE of López<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC 2012) relied on a non-standard assumption, referred to as the Decisional Small Polynomial Ratio assumption.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_31"}, {"primary_key": "4387402", "vector": [], "sparse_vector": [], "title": "Tweaking Even-Mansour Ciphers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study how to construct efficient tweakable block ciphers in the Random Permutation model, where all parties have access to public random permutation oracles. We propose a construction that combines, more efficiently than by mere black-box composition, the CLRW construction (which turns a traditional block cipher into a tweakable block cipher) of Landeckeret al.(CRYPTO 2012) and the iterated Even-Mansour construction (which turns a tuple of public permutations into a traditional block cipher) that has received considerable attention since the work of Bogdanovet al.(EUROCRYPT 2012). More concretely, we introduce the (one-round)tweakable Even-Mansour(TEM) cipher, constructed from a singlen-bit permutationPand a uniform and almost XOR-universal family of hash functions\\((H_k)\\)from some tweak space to\\(\\{0,1\\}^n\\), and defined as\\((k,t,x)\\mapsto H_k(t)\\oplus P(H_k(t)\\oplus x)\\), wherekis the key,tis the tweak, andxis then-bit message, as well as its generalization obtained by cascadingrindependently keyed rounds of this construction. Our main result is a security bound up to approximately\\(2^{2n/3}\\)adversarial queries against adaptive chosen-plaintext and ciphertext distinguishers for the two-round TEM construction, using <PERSON><PERSON><PERSON>’s H-coefficients technique. We also provide an analysis based on the coupling technique showing that asymptotically, as the number of roundsrgrows, the security provided by ther-round TEM construction approaches the information-theoretic bound of\\(2^n\\)adversarial queries.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_9"}, {"primary_key": "4387403", "vector": [], "sparse_vector": [], "title": "Zeroizing Without Low-Level Zeroes: New MMAP Attacks and their Limitations.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We extend the recent zeroizing attacks of <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> (Eurocrypt’15) on multilinear maps to settings where no encodings of zero below the maximal level are available. Some of the new attacks apply to the CLT13 scheme (resulting in a total break) while others apply to (a variant of) the GGH13 scheme (resulting in a weak-DL attack). We also note the limits of these zeroizing attacks.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_12"}, {"primary_key": "4387404", "vector": [], "sparse_vector": [], "title": "New Multilinear Maps Over the Integers.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the last few years, cryptographic multilinear maps have proved their tremendous potential as building blocks for new constructions, in particular the first viable approach to general program obfuscation. After the first candidate construction by <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (GGH) based on ideal lattices, a second construction over the integers was described by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (CLT). However the CLT scheme was recently broken by <PERSON><PERSON> et al.; the attack works by computing the eigenvalues of a diagonalizable matrix over\\({\\mathbb Q}\\)derived from the multilinear map. In this paper we describe a new candidate multilinear map over the integers. Our construction is based on CLT but with a new arithmetic technique that makes the zero-testing element non-linear in the encoding, which prevents the <PERSON><PERSON> et al. attack. Our new construction is relatively practical as its efficiency is comparable to the original CLT scheme. Moreover the subgroup membership and decisional linear assumptions appear to hold in the new setting.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_13"}, {"primary_key": "4387405", "vector": [], "sparse_vector": [], "title": "New Attacks on Feistel Structures with Improved Memory Complexities.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Feistel structures are an extremely important and extensively researched type of cryptographic schemes. In this paper we describe improved attacks on Feistel structures with more than 4 rounds. We achieve this by a new attack that combines the main benefits of meet-in-the-middle attacks (which can reduce the time complexity by comparing only half blocks in the middle) and dissection attacks (which can reduce the memory complexity but have to guess full blocks in the middle in order to perform independent attacks above and below it). For example, for a 7-round Feistel structure onn-bit inputs with seven independent round keys ofn/ 2 bits each, a MITM attack can use (\\(2^{1.5n}\\),\\(2^{1.5n}\\)) time and memory, while dissection requires (\\(2^{2n}\\),\\(2^{n}\\)) time and memory. Our new attack requires only (\\(2^{1.5n}\\),\\(2^{n}\\)) time and memory, using a few known plaintext/ciphertext pairs. When we are allowed to use more known plaintexts, we develop new techniques which rely on the existence of multicollisions and differential properties deep in the structure in order to further reduce the memory complexity. Our new attacks are not just theoretical generic constructions — in fact, we can use them to improve the best known attacks on several concrete cryptosystems such as round-reduced CAST-128 (where we reduce the memory complexity from\\(2^{111} \\)to\\(2^{64}\\)) and full DEAL-256 (where we reduce the memory complexity from\\(2^{200}\\)to\\(2^{144}\\)), without affecting their time and data complexities. An extension of our techniques applies even to some non-Feistel structures — for example, in the case of FOX, we reduce the memory complexity of all the best known attacks by a factor of\\(2^{16}\\).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_21"}, {"primary_key": "4387406", "vector": [], "sparse_vector": [], "title": "Privacy with Imperfect Randomness.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Yan<PERSON> Yao"], "summary": "We revisit the impossibility of a variety of cryptographic tasks including privacy and differential privacy with imperfect randomness. For traditional notions of privacy, such as security of encryption, commitment or secret sharing schemes, dramatic impossibility results are known [MP90,DOPS04] for several concrete sources\\(\\mathcal {R}\\), including a (seemingly) very “nice and friendly” <PERSON><PERSON><PERSON> (SV) source. Somewhat surprisingly, <PERSON><PERSON> et al. [DLMV12] showed that non-trivialdifferentialprivacy is possible with the SV sources. This suggested a qualitative gap between traditional and differential privacy, and left open the question of whether differential privacy is possible with more realistic (i.e., less structured) sources than the SV sources. Motivated by this question, we introduce a new, modular framework for showing strong impossibility results for (both traditional and differential) privacy under ageneralimperfect source\\(\\mathcal {R}\\). As direct corollaries of our framework, we get the following new results: Existing, butquantitatively improved, impossibility results for traditional privacy, but under a wider variety of sources\\(\\mathcal {R}\\). First impossibility results fordifferentialprivacy for a variety of realistic sources\\(\\mathcal {R}\\)(including most “block sources”, but not the SV source). Any imperfect source allowing (either traditional or differential) privacy under\\(\\mathcal {R}\\)admits a certain type of deterministic bit extraction from\\(\\mathcal {R}\\).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_23"}, {"primary_key": "4387407", "vector": [], "sparse_vector": [], "title": "Efficient Pseudorandom Functions via On-the-Fly Adaptation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Pseudorandom functions (PRFs) are one of the most fundamental building blocks in cryptography with numerous applications such as message authentication codes and private key encryption. In this work, we propose a new framework to construct PRFs with the overall goal to build efficient PRFs from standard assumptions with an almost tight proof of security. The main idea of our framework is to start from a PRF for any small domain (i.e. poly-sized domain) and turn it into an\\(\\ell \\)-bounded pseudorandom function, i.e., into a PRF whose outputs are pseudorandom for the first\\(\\ell \\)distinct queries toF. In the second step, we apply a novel technique which we callon-the-fly adaptationthat turns any bounded PRF into a fully-fledged (large domain) PRF. Both steps of our framework have a tight security reduction, meaning that any successful attacker can be turned into an efficient algorithm for the underlying hard computational problem without any significant increase in the running time or loss of success probability. Instantiating our framework with specific number theoretic assumptions, we construct a PRF based onk-LIN (and thus DDH) that is faster than all known constructions, which reduces almost tightly to the underlying problem, and which has shorter keys.Instantiating our framework with general assumptions, we construct a PRF with very flat circuits whose security tightly reduces to the security of some small domain PRF.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_16"}, {"primary_key": "4387408", "vector": [], "sparse_vector": [], "title": "Proofs of Space.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Proofs of work (PoW) have been suggested by <PERSON><PERSON> and <PERSON><PERSON> (Crypto’92) as protection to a shared resource. The basic idea is to ask the service requestor to dedicate some non-trivial amount of computational work to every request. The original applications included prevention of spam and protection against denial of service attacks. More recently, PoWs have been used to prevent double spending in the Bitcoin digital currency system. In this work, we put forward an alternative concept for PoWs – so-calledproofs of space(PoS), where a service requestor must dedicate a significant amount of disk space as opposed to computation. We construct secure PoS schemes in the random oracle model (with one additional mild assumption required for the proof to go through), using graphs with high “pebbling complexity” and Merkle hash-trees. We discuss some applications, including follow-up work where a decentralized digital currency scheme called Spacecoin is constructed that uses PoS (instead of wasteful PoW like in Bitcoin) to prevent double spending. The main technical contribution of this work is the construction of (directed, loop-free) graphs onNvertices with in-degree\\(O(\\log \\log N)\\)such that even if one places\\(\\varTheta (N)\\)pebbles on the nodes of the graph, there’s a constant fraction of nodes that needs\\(\\varTheta (N)\\)steps to be pebbled (where in every step one can put a pebble on a node if all its parents have a pebble).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_29"}, {"primary_key": "4387409", "vector": [], "sparse_vector": [], "title": "Provably Weak Instances of Ring-LWE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Thering and polynomial learning with errorsproblems (Ring-LWE and Poly-LWE) have been proposed as hard problems to form the basis for cryptosystems, and various security reductions to hard lattice problems have been presented. So far these problems have been stated for general (number) rings but have only been closely examined for cyclotomic number rings. In this paper, we state and examine the Ring-LWE problem for general number rings and demonstrateprovably weak instancesof the Decision Ring-LWE problem. We construct an explicit family of number fields for which we have an efficient attack. We demonstrate the attack in both theory and practice, providing code and running times for the attack. The attack runs in time linear inq, whereqis the modulus. Our attack is based on the attack on Poly-LWE which was presented in [EHL]. We extend the EHL-attack to apply to a larger class of number fields, and show how it applies to attack Ring-LWE for a heuristically large class of fields. Certain Ring-LWE instances can be transformed into Poly-LWE instances without distorting the error too much, and thus provide the first weak instances of the Ring-LWE problem. We also provide additional examples of fields which are vulnerable to our attacks on Poly-LWE, including power-of-2 cyclotomic fields, presented using the minimal polynomial of\\(\\zeta _{2^n} \\pm 1\\).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_4"}, {"primary_key": "4387410", "vector": [], "sparse_vector": [], "title": "Higher-Order Differential Meet-in-the-middle Preimage Attacks on SHA-1 and BLAKE.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "At CRYPTO 2012, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> presented a differential formulation of advanced meet-in-the-middle techniques for preimage attacks on hash functions. They demonstrated the usefulness of their approach by significantly improving the previously best known attacks on SHA-1 from CRYPTO 2009, increasing the number of attacked rounds from a 48-roundone-block pseudo-preimage without paddingand a 48-roundtwo-block preimage without paddingto a 57-roundone-block preimage without paddingand a 57-roundtwo-block preimage with padding, out of 80 rounds for the full function. In this work, we exploit further the differential view of meet-in-the-middle techniques and generalize it to higher-order differentials. Despite being an important technique dating from the mid-90’s, this is the first time higher-order differentials have been applied to meet-in-the-middle preimages. We show that doing so may lead to significant improvements to preimage attacks on hash functions with a simple linear message expansion. We extend the number of attacked rounds on SHA-1 to give a 62-roundone-block preimage without padding, a 56-roundone-block preimage with padding, and a 62-roundtwo-block preimage with padding. We also apply our framework to the more recent SHA-3 finalist BLAKE and its newer variant BLAKE2, and give an attack for a 2.75-roundpreimage with padding, and a 7.5-roundpseudo-preimage on the compression function.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_33"}, {"primary_key": "4387411", "vector": [], "sparse_vector": [], "title": "Multi-prover Commitments Against Non-signaling Attacks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We reconsider the concept of two-prover (and more generally: multi-prover) commitments, as introduced in the late eighties in the seminal work by <PERSON><PERSON><PERSON><PERSON>. As was recently shown by <PERSON><PERSON><PERSON><PERSON><PERSON> al., the security of known two-prover commitment schemes not only relies on the explicit assumption that the two provers cannot communicate, but also depends on what their information processing capabilities are. For instance, there exist schemes that are secure against classical provers but insecure if the provers havequantuminformation processing capabilities, and there are schemes that resist such quantum attacks but become insecure when considering general so-callednon-signalingprovers, which are restrictedsolelyby the requirement that no communication takes place. This poses the natural question whether there exists a two-prover commitment scheme that is secure under thesoleassumption that no communication takes place, and that does not rely on any further restriction of the information processing capabilities of the dishonest provers; no such scheme is known. In this work, we give strong evidence for a negative answer: we show that any single-round two-prover commitment scheme can be broken by a non-signaling attack. Our negative result is as bad as it can get: for any candidate scheme that is (almost) perfectly hiding, there exists a strategy that allows the dishonest provers to open a commitment to an arbitrary bit (almost) as successfully as the honest provers can open an honestly prepared commitment, i.e., with probability (almost) 1 in case of a perfectly sound scheme. In the case of multi-round schemes, our impossibility result is restricted to perfectly hiding schemes. On the positive side, we show that the impossibility result can be circumvented by consideringthreeprovers instead: there exists a three-prover commitment scheme that is secure against arbitrary non-signaling attacks.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_20"}, {"primary_key": "4387412", "vector": [], "sparse_vector": [], "title": "Data Is a Stream: Security of Stream-Based Channels.", "authors": ["<PERSON>", "<PERSON>", "Giorgia Azzurra <PERSON>", "<PERSON>"], "summary": "The common approach to defining secure channels in the literature is to consider transportation of discrete messages provided via atomic encryption and decryption interfaces. This, however, ignores that many practical protocols (including TLS, SSH, and QUIC) offer streaming interfaces instead, moreover with the complexity that the network (possibly under adversarial control) may deliver arbitrary fragments of ciphertexts to the receiver. To address this deficiency, we initiate the study of stream-based channels and their security. We present notions of confidentiality and integrity for such channels, akin to the notions for atomic channels, but taking the peculiarities of streams into account. We provide a composition result for our setting, saying that combining chosen-plaintext confidentiality with integrity of the transmitted ciphertext stream lifts confidentiality of the channel to chosen-ciphertext security. Notably, for our proof of this theorem in the streaming setting we need an additional property, called error predictability. We finally give an AEAD-based construction that achieves our notion of a secure stream-based channel. The construction matches rather well the one used in TLS, providing validation of that protocol’s design.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_27"}, {"primary_key": "4387413", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of the Co-ACD Assumption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At ACM-CCS 2014, <PERSON><PERSON>, <PERSON> and <PERSON><PERSON> introduced a new number-theoretic assumption, the Co-Approximate Common Divisor (Co-ACD) assumption, based on which they constructed several cryptographic primitives, including a particularly fast additively homomorphic encryption scheme. For their proposed parameters, they found that their scheme was the “most efficient of those that support an additive homomorphic property”. Unfortunately, it turns out that those parameters, originally aiming at 128-bit security, can be broken in a matter of seconds. Indeed, this paper presents several lattice-based attacks against the Cheon–<PERSON>–Seo (CLS) homomorphic encryption scheme and of the underlying Co-ACD assumption that are effectively devastating for the proposed constructions. A few known plaintexts are sufficient to decrypt any ciphertext in the symmetric-key CLS scheme, and small messages can even be decrypted without any known plaintext at all. This breaks the security of both the symmetric-key and the public-key variants of CLS encryption as well as the underlying decisional Co-ACD assumption. Moreover, Coppersmith techniques can be used to solve the search variant of the Co-ACD problem and mount a full key recovery on the CLS scheme.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_27"}, {"primary_key": "4387414", "vector": [], "sparse_vector": [], "title": "Practical Round-Optimal Blind Signatures in the Standard Model.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Round-optimal blind signatures are notoriously hard to construct in the standard model, especially in the malicious-signer model, where blindness must hold under adversarially chosen keys. This is substantiated by several impossibility results. The only construction that can be termed theoretically efficient, by <PERSON><PERSON><PERSON> and <PERSON> (Eurocrypt’14), requires complexity leveraging, inducing an exponential security loss. We present a construction of practically efficient round-optimal blind signatures in the standard model. It is conceptually simple and builds on the recent structure-preserving signatures on equivalence classes (SPS-EQ) fromAsiacrypt’14. While the traditional notion of blindness follows from standard assumptions, we prove blindness under adversarially chosen keys under an interactive variant of DDH. However, we neither require non-uniform assumptions nor complexity leveraging. We then show how to extend our construction to partially blind signatures and to blind signatures on message vectors, which yield a construction of one-show anonymous credentials à la “anonymous credentials light” (CCS’13) in the standard model. Furthermore, we give the first SPS-EQ construction under non-interactive assumptions and show how SPS-EQ schemes imply conventional structure-preserving signatures, which allows us to apply optimality results for the latter to SPS-EQ.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_12"}, {"primary_key": "4387415", "vector": [], "sparse_vector": [], "title": "A Quasipolynomial Reduction for Generalized Selective Decryption on Trees.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Generalized Selective Decryption (GSD), introduced by <PERSON><PERSON><PERSON><PERSON> [TCC’07], is a game for a symmetric encryption scheme\\(\\mathsf{Enc}\\)that captures the difficulty of proving adaptive security of certain protocols, most notably the Logical Key Hierarchy (LKH) multicast encryption protocol. In the GSD game there arenkeys\\(k_1,\\ldots ,k_n\\), which the adversary may adaptively corrupt (learn); moreover, it can ask for encryptions\\(\\mathsf{Enc}_{k_i}(k_j)\\)of keys under other keys. The adversary’s task is to distinguish keys (which it cannot trivially compute) from random. Proving the hardness of GSD assuming only IND-CPA security of\\(\\mathsf{Enc}\\)is surprisingly hard. Using “complexity leveraging” loses a factor exponential inn, which makes the proof practically meaningless. We can think of the GSD game as building a graph onnvertices, where we add an edge\\(i\\rightarrow j\\)when the adversary asks for an encryption of\\(k_j\\)under\\(k_i\\). If restricted to graphs of depth\\(\\ell \\), <PERSON><PERSON><PERSON><PERSON> gave a reduction that loses only a factor exponential in\\(\\ell \\)(notn). To date, this is the only non-trivial result known for GSD. In this paper we give almost-polynomial reductions for large classes of graphs. Most importantly, we prove the security of the GSD game restricted to trees losing only a quasi-polynomial factor\\(n^{3\\log n+5}\\). Trees are an important special case capturing real-world protocols like the LKH protocol. Our new bound improves upon Panjwani’s on some LKH variants proposed in the literature where the underlying tree is not balanced. Our proof builds on ideas from the “nested hybrids” technique recently introduced by Fuchsbauer et al. [Asiacrypt’14] for proving the adaptive security of constrained PRFs.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_29"}, {"primary_key": "4387416", "vector": [], "sparse_vector": [], "title": "Cryptography with One-Way Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There is a large body of work on using noisy communication channels for realizing different cryptographic tasks. In particular, it is known that secure message transmission can be achieved unconditionally using onlyone-waycommunication from the sender to the receiver. In contrast, known solutions for more general secure computation tasks inherently require interaction, even when the entire input originates from the sender. We initiate a general study of cryptographic protocols over noisy channels in a setting where only one party speaks. In this setting, we show that the landscape of what a channel is useful for is much richer. Concretely, we obtain the following results. Relationships Between Channels.The binary erasure channel (BEC) and the binary symmetric channel (BSC), which are known to be securely reducible to each other in the interactive setting, turn out to be qualitatively different in the setting of one-way communication. In particular, a BEC cannot be implemented from a BSC, and while the erasure probability of a BEC can be manipulated in both directions, the crossover probability of a BSC can only be manipulated in one direction. Zero-knowledge Proofs and Secure Computation of Deterministic Functions.One-way communication over BEC or BSC is sufficient for securely realizing any deterministic (possibly reactive) functionality which takes its inputs from a sender and delivers its outputs to a receiver. This provides the first truly non-interactive solutions to the problem of zero-knowledge proofs. Secure Computation of Randomized Functions.One-way communication over BEC or BSCcannotbe used for realizing general randomized functionalities which take input from a sender and deliver output to a receiver. On the other hand, one-way communication over other natural channels, such as bursty erasure channels, can be used to realize such functionalities. This type of protocols can be used for distributing certified cryptographic keys without revealing the keys to the certification authority.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_10"}, {"primary_key": "4387417", "vector": [], "sparse_vector": [], "title": "Communication Complexity of Conditional Disclosure of Secrets and Attribute-Based Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We initiate a systematic treatment of the communication complexity of conditional disclosure of secrets (CDS), where two parties want to disclose a secret to a third party if and only if their respective inputs satisfy some predicate. We present a general upper bound and the first non-trivial lower bounds for conditional disclosure of secrets. Moreover, we achieve tight lower bounds for many interesting setting of parameters for CDS with linear reconstruction, the latter being a requirement in the application to attribute-based encryption. In particular, our lower bounds explain the trade-off between ciphertext and secret key sizes of several existing attribute-based encryption schemes based on the dual system methodology.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_24"}, {"primary_key": "4387418", "vector": [], "sparse_vector": [], "title": "The Exact PRF Security of Truncation: Tight Bounds for Keyed Sponges and Truncated CBC.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper studies the concrete security of PRFs and MACs obtained by keying hash functions based on the sponge paradigm. One such hash function is KECCAK, selected as NIST’s new SHA-3 standard. In contrast to other approaches like HMAC, the exact security of keyed sponges is not well understood. Indeed, recent security analyses delivered concrete security bounds which are far from existing attacks. This paper aims to close this gap. We prove (nearly) exact bounds on the concrete PRF security of keyed sponges using a random permutation. These bounds are tight for the most relevant ranges of parameters, i.e., for messages of length (roughly)\\(\\ell \\leqslant \\min \\{2^{n/4},2^r\\}\\)blocks, wherenis the state size andris the desired output length; and for\\(\\ell \\leqslant q\\)queries (to the construction or the underlying permutation). Moreover, we also improve standard-model bounds. As an intermediate step of independent interest, we prove tight bounds on the PRF security of thetruncatedCBC-MAC construction, which operates as plain CBC-MAC, but only returns a prefix of the output.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_18"}, {"primary_key": "4387419", "vector": [], "sparse_vector": [], "title": "Efficient Multi-party Computation: From Passive to Active Security via Secure SIMD Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>"], "summary": "A central problem in cryptography is that of converting protocols that offer security against passive (or semi-honest) adversaries into ones that offer security against active (or malicious) adversaries. This problem has been the topic of a large body of work in the area of secure multiparty computation (MPC). Despite these efforts, there are still big efficiency gaps between the best protocols in these two settings. In two recent works, <PERSON><PERSON> et al. (STOC 2014) and <PERSON><PERSON><PERSON> et al. (ePrint 2014) suggested the following new paradigm for efficiently transforming passive-secure MPC protocols into active-secure ones. They start by observing that in several natural information-theoretic MPC protocols, an arbitrary active attack on the protocol can be perfectly simulated in an ideal model that allows foradditiveattacks on the arithmetic circuit being evaluated. That is, the simulator is allowed to (blindly) modify the original circuit by adding an arbitrary field element to each wire. To protect against such attacks, the original circuit is replaced by a so-calledAMD circuit, which can offer protection against such attacks with constant multiplicative overhead to the size. Our motivating observation is that in the most efficient known information-theoretic MPC protocols, which are based on packed secret sharing, it isnotthe case that general attacks reduce to additive attacks. Instead, the corresponding ideal attack can include limited forms of linear combinations of wire values. We extend the AMD circuit methodology to so-calledsecure SIMD circuits,which offer protection against this more general class of attacks. We apply secure SIMD circuits to obtain several asymptotic and concrete efficiency improvements over the current state of the art. In particular, we improve the additive per-layer overhead of the current best protocols from\\(O(n^2)\\)toO(n), wherenis the number of parties, and obtain the first protocols based on packed secret sharing that “natively” achieve near-optimal security without incurring the high concrete cost of Bracha’s committee-based security amplification method. Our analysis is based on a new modular framework for proving reductions from general attacks to algebraic attacks. This framework allows us to reprove previous results in a conceptually simpler and more unified way, as well as obtain our new results.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_35"}, {"primary_key": "4387420", "vector": [], "sparse_vector": [], "title": "Key-Recovery Attack on the ASASA Cryptosystem with Expanding S-Boxes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a cryptanalysis of theASASApublic key cipher introduced atAsiacrypt2014 [3]. This scheme alternates three layers of affine transformationsAwith two layers of quadratic substitutionsS. We show that the partial derivatives of the public key polynomials contain information about the intermediate layer. This enables us to present a very simple distinguisher between anASASApublic key and random polynomials. We then expand upon the ideas of the distinguisher to achieve a full secret key recovery. This method uses only linear algebra and has a complexity dominated by the cost of computing the kernels of\\(2^{26}\\)small matrices with entries in\\(\\mathbb {F}_{16}\\).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_23"}, {"primary_key": "4387421", "vector": [], "sparse_vector": [], "title": "Predicate Encryption for Circuits from LWE.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "In predicate encryption, a ciphertext is associated with descriptive attribute valuesxin addition to a plaintext\\(\\mu \\), and a secret key is associated with a predicatef. Decryption returns plaintext\\(\\mu \\)if and only if\\(f(x) = 1\\). Moreover, security of predicate encryption guarantees that an adversary learns nothing about the attributexor the plaintext\\(\\mu \\)from a ciphertext, given arbitrary many secret keys that are not authorized to decrypt the ciphertext individually. We construct a leveled predicate encryption scheme for all circuits, assuming the hardness of the subexponential learning with errors (LWE) problem. That is, for any polynomial function\\(d = d(\\lambda )\\), we construct a predicate encryption scheme for the class of all circuits with depth bounded by\\(d(\\lambda )\\), where\\(\\lambda \\)is the security parameter.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_25"}, {"primary_key": "4387422", "vector": [], "sparse_vector": [], "title": "Constant-Round MPC with Fairness and Guarantee of Output Delivery.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the round complexity of multiparty computation with fairness and guaranteed output delivery, assuming existence of an honest majority. We demonstrate a new lower bound and a matching upper bound. Our lower bound rules out any two-round fair protocols in the standalone model, even when the parties are given access to a common reference string (CRS). The lower bound follows by a reduction to the impossibility result of virtual black box obfuscation of arbitrary circuits. Then we demonstrate a three-round protocol with guarantee of output delivery, which in general is harder than achieving fairness (since the latter allows the adversary to force a fair abort). We develop a new construction of a threshold fully homomorphic encryption scheme, with a new property that we call “flexible” ciphertexts. Roughly, our threshold encryption scheme allows parties to adapt flexible ciphertexts to the public keys of the non-aborting parties, which provides a way of handling aborts without adding any communication.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_4"}, {"primary_key": "4387423", "vector": [], "sparse_vector": [], "title": "Concurrent Secure Computation via Non-Black Box Simulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, <PERSON><PERSON> (STOC’13) proposed a new non-black box simulation techniques for fully concurrent zero knowledge with straight-line simulation. Unfortunately, so far this technique is limited to the setting of concurrent zero knowledge. The goal of this paper is to study what can be achieved in the setting of concurrent secure computation using non-black box simulation techniques, building upon the work of <PERSON><PERSON>. The main contribution of our work is a secure computation protocol in the fully concurrent setting with a straight-line simulator, that allows us to achieve several new results: We give first positive results for concurrent blind signatures and verifiable random functions in the plain modelas per the ideal/real world security definition. Our positive result is somewhat surprising in light of the impossibility result of <PERSON><PERSON> (STOC’03) for black-box simulation. We circumvent this impossibility using non-black box simulation. This gives us a quite natural example of a functionality in concurrent setting which is impossible to realize using black-box simulation but can be securely realized using non-black box simulation. Moreover, we expand the class of realizable functionalities in the concurrent setting. Our main theorem is a positive result for concurrent secure computation as long as the ideal world satisfies thebounded pseudo-entropy condition(BPC) of Goyal (FOCS’12). The BPC requires that in the ideal world experiment, the total amount of information learnt by the adversary (via calls to the ideal functionality) should have “bounded pseudoentropy”. We also improve the round complexity of protocols in the single-input setting of Goyal (FOCS’12) both qualitatively and quantitatively. In <PERSON><PERSON>’s work, the number of rounds depended on the length of honest party inputs. In our protocol, the round complexity depends only on the security parameter, and is completely independent of the length of the honest party inputs. Our results are based on a non-black box simulation technique using a new language (which allows the simulator to commit to an Oracle program that can access information with bounded pseudoentropy), and a simulation-sound version of the concurrent zero-knowledge protocol of Goyal (STOC’13). We assume the existence of collision resistant hash functions and constant round semi-honest oblivious transfer.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_2"}, {"primary_key": "4387424", "vector": [], "sparse_vector": [], "title": "Coded-BKW: Solving LWE Using Lattice Codes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we propose a new algorithm for solving the Learning With Errors (LWE) problem based on the steps of the famous <PERSON><PERSON><PERSON><PERSON><PERSON> (BKW) algorithm. The new idea is to introduce an additional procedure of mapping subvectors into codewords of a lattice code, thereby increasing the amount of positions that can be cancelled in each BKW step. The procedure introduces an additional noise term, but it is shown that by using a sequence of lattice codes with different rates the noise can be kept small. Developed theory shows that the new approach compares favorably to previous methods. It performs particularly well for thebinary-LWEcase, i.e., when the secret vector is sampled from\\(\\{0,1\\}^*\\).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_2"}, {"primary_key": "4387425", "vector": [], "sparse_vector": [], "title": "Parallel Hashing via List Recoverability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by the goal of constructing efficient hash functions, we investigate the possibility of hashing a long message by only making parallel, non-adaptive calls to a hash function on short messages. Our main result is a simple construction of a collision-resistant hash function\\(h:\\{0,1\\}^n\\rightarrow \\{0,1\\}^k\\)that makes a polynomial number of parallel calls to arandomfunction\\(f:\\{0,1\\}^k\\rightarrow \\{0,1\\}^k\\), for any polynomial\\(n=n(k)\\). This should be compared with the traditional use of a Merkle hash tree, that requires at least\\(\\log (n/k)\\)rounds of calls tof, and with a more complex construction of <PERSON><PERSON> and <PERSON><PERSON> [26] (Crypto 2007) that requires two rounds of calls tof. We also show that our hash functionhsatisfies a relaxed form of the notion of indifferentiability of <PERSON> et al. [27] (TCC 2004) that suffices for implementing the Fiat-Shamir paradigm. As a corollary, we get sublinear-communication non-interactive arguments for NP that only make two rounds of calls to a small random oracle. An attractive feature of our construction is thathcan be implemented by Boolean circuits that only contain parity gates in addition to the parallel calls tof. Thus, we get the first domain-extension scheme which isdegree-preservingin the sense that the algebraic degree ofhover the binary field is equal to that off. Our construction makes use oflist-recoverable codes, a generalization of list-decodable codes that is closely related to the notion of randomness condensers. We show that list-recoverable codes are necessary for any construction of this type.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_9"}, {"primary_key": "4387426", "vector": [], "sparse_vector": [], "title": "Reproducible Circularly-Secure Bit Encryption: Applications and Realizations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give generic constructions of several fundamental cryptographic primitives based on a new encryption primitive that combinescircular securityfor bit encryption with the so-calledreproducibility property(<PERSON><PERSON> et al. PK<PERSON> 2003). At the heart of our constructions is a novel technique which gives a way of de-randomizing reproducible public-key bit-encryption schemes and also a way of reducing one-wayness conditions of a constructed trapdoor-function family (TDF) to circular security of the base scheme. The main primitives that we build from our encryption primitive includek-wise one-wayTDFs (Rosen and Segev TCC 2009), CCA2-secure encryption and deterministic encryption. Our results demonstrate a new set of applications of circularly-secure encryption beyond fully-homomorphic encryption and symbolic soundness. Finally, we show the plausibility of our assumptions by showing that the DDH-based circularly-secure scheme of <PERSON><PERSON> et al. (Crypto 2008) and the subgroup indistinguishability based scheme of <PERSON><PERSON><PERSON><PERSON> and <PERSON>was<PERSON> (Crypto 2010) are both reproducible.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_11"}, {"primary_key": "4387427", "vector": [], "sparse_vector": [], "title": "Decaf: Eliminating Cofactors Through Point Compression.", "authors": ["<PERSON>"], "summary": "We propose a new unified point compression format for <PERSON>, <PERSON><PERSON><PERSON> and <PERSON> curves over large-characteristic fields, which effectively divides the curve’s cofactor by 4 at very little cost to performance. This allows cofactor-4 curves to efficiently implement prime-order groups.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_34"}, {"primary_key": "4387428", "vector": [], "sparse_vector": [], "title": "Online Authenticated-Encryption and its Nonce-Reuse Misuse-Resistance.", "authors": ["Viet Tung Hoang", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A definition ofonline authenticated-encryption(OAE), call it OAE1, was given by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (2012). It has become a popular definitional target because, despite allowing encryption to be online, security is supposed to be maintained even if nonces get reused. We argue that this expectation is effectively wrong. OAE1 security has also been claimed to capture best-possible security for any online-AE scheme. We claim that this understanding is wrong, too. So motivated, we redefine OAE-security, providing a radically different formulation, OAE2. The new notion effectivelydoescapture best-possible security for a user’s choice of plaintext segmentation and ciphertext expansion. It is achievable by simple techniques from standard tools. Yet even for OAE2, nonce-reuse can still be devastating. The picture to emerge is that no OAE definition can meaningfully tolerate nonce-reuse, but, at the same time, OAE security ought never have been understood to turn on this question.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_24"}, {"primary_key": "4387429", "vector": [], "sparse_vector": [], "title": "Efficient Zero-Knowledge Proofs of Non-algebraic Statements with Sublinear Amortized Cost.", "authors": ["<PERSON><PERSON>ng Hu", "<PERSON><PERSON>", "<PERSON>"], "summary": "We describe a zero-knowledge proof system in which a prover holds a large datasetM<PERSON> can repeatedly prove NP relations about that dataset. That is, for any (public) relationRandx, the prover can prove that\\(\\exists w: R(M,x,w)=1\\). After an initial setup phase (which depends only onM), each proof requires only a constant number of rounds and has communication/computation cost proportional to that of arandom-access machine (RAM)implementation ofR, up to polylogarithmic factors. In particular, the cost per proof in many applications is sublinear in |M|. Additionally, the storage requirement between proofs for the verifier is constant.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_8"}, {"primary_key": "4387430", "vector": [], "sparse_vector": [], "title": "Last Fall Degree, HFE, and Weil Descent Attacks on ECDLP.", "authors": ["<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Weil descent methods have recently been applied to attack the Hidden Field Equation (HFE) public key systems and solve the elliptic curve discrete logarithm problem (ECDLP) in small characteristic. However the claims of quasi-polynomial time attacks on the HFE systems and the subexponential time algorithm for the ECDLP depend on various heuristic assumptions. In this paper we introduce the notion of the last fall degree of a polynomial system, which is independent of choice of a monomial order. We then develop complexity bounds on solving polynomial systems based on this last fall degree. We prove that HFE systems have a small last fall degree, by showing that one can do division with remainder after Weil descent. This allows us to solve HFE systems unconditionally in polynomial time if the degree of the defining polynomial and the cardinality of the base field are fixed. For the ECDLP over a finite field of characteristic 2, we provide computational evidence that raises doubt on the validity of the first fall degree assumption, which was widely adopted in earlier works and which promises sub-exponential algorithms for ECDLP. In addition, we construct a Weil descent system from a set of summation polynomials in which the first fall degree assumption is unlikely to hold. These examples suggest that greater care needs to be exercised when applying this heuristic assumption to arrive at complexity estimates. These results taken together underscore the importance of rigorously bounding last fall degrees of Weil descent systems, which remains an interesting but challenging open problem.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_28"}, {"primary_key": "4387431", "vector": [], "sparse_vector": [], "title": "Capacity and Data Complexity in Multidimensional Linear Attack.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multidimensional linear attacks are one of the most powerful variants of linear cryptanalytic techniques now. However, there is no knowledge on the key-dependent capacity and data complexity so far. Their values were assumed to be close to the average value for a vast majority of keys. This assumption is not accurate. In this paper, under a reasonable condition, we explicitly formulate the capacity as a Gamma distribution and the data complexity as an Inverse Gamma distribution, in terms of the average linear probability and the dimension. The capacity distribution is experimentally verified on the 5-round PRESENT. Regarding to complexity, we solve the problem of estimating the average data complexity, which was difficult to estimate because of the existence of zero correlations. We solve the problem of using the median complexity in multidimensional linear attacks, which is an open problem since proposed in Eurocrypt 2011. We also evaluate the difference among the median complexity, the average complexity and a lower bound of the average complexity – the reciprocal of average capacity. In addition, we estimate more accurately the key equivalent hypothesis, and reveal the fact that the average complexity only provides an accurate estimate for less than half of the keys no matter how many linear approximations are involved. Finally, we revisit the so far best attack on PRESENT based on our theoretical result.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_7"}, {"primary_key": "4387432", "vector": [], "sparse_vector": [], "title": "Secure Computation with Minimal Interaction, Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Motivated by the goal of improving the concrete efficiency of secure multiparty computation (MPC), we revisit the question of MPC with only two rounds of interaction. We consider a minimal setting in which parties can communicate over secure point-to-point channels and where no broadcast channel or other form of setup is available. <PERSON> and <PERSON><PERSON><PERSON> (<PERSON><PERSON>o 2004) obtained negative results for such protocols with\\(n=2\\)parties. <PERSON><PERSON> et al. (Crypto 2010) showed that if only one party may be corrupted, then\\(n \\ge 5\\)parties can securely compute any function in this setting, with guaranteed output delivery, assuming one-way functions exist. In this work, we complement the above results by presenting positive and negative results for the cases where\\(n = 3\\)or\\(n = 4\\)and where there is asingle malicious party. When\\(n=3\\), we show a 2-round protocol which is secure with “selective abort” against a single malicious party. The protocol makes a black-box use of a pseudorandom generator or alternatively can offer unconditional security for functionalities in\\(\\mathrm {NC}^1\\). The concrete efficiency of this protocol is comparable to the efficiency of secure two-party computation protocols forsemi-honestparties based on garbled circuits. When\\(n= 4\\)in the setting described above, we show the following: Astatistical VSSprotocol that has a 1-round sharing phase and 1-round reconstruction phase. This improves over the state-of-the-art result of <PERSON><PERSON> et al. (Crypto 2009) whose VSS protocol required 2 rounds in the reconstruction phase. A 2-round statistically secure protocol forlinear functionalitieswith guaranteed output delivery. This implies a 2-round 4-party fair coin tossing protocol. We complement this by a negative result, showing that there is a (nonlinear) function for which there is no 2-round statistically secure protocol. A 2-round computationally secure protocol forgeneral functionalitieswith guaranteed output delivery, under the assumption that injective (one-to-one) one-way functions exist. A 2-round protocol for general functionalities with guaranteed output delivery in thepreprocessing model, whose correlated randomness complexity is proportional to the length of the inputs. This protocol makes a black-box use of a pseudorandom generator or alternatively can offer unconditional security for functionalities in\\(\\mathrm {NC}^1\\). Prior to our work, the feasibility results implied by our positive results were not known to hold even in the stronger MPC model considered by Gennaro et al. (Crypto 2002), where a broadcast channel is available.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_18"}, {"primary_key": "4387433", "vector": [], "sparse_vector": [], "title": "Arguments of Proximity - [Extended Abstract].", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "An interactive proof of proximity (\\(\\mathsf{{IPP}}\\)) is an interactive protocol in which a prover tries to convince asublinear-timeverifier that\\(x \\in \\mathcal {L}\\). Since the verifier runs in sublinear-time, following the property testing literature, the verifier is only required to reject inputs that arefarfrom\\(\\mathcal {L}\\). In a recent work, Rothblumet. al(STOC, 2013) constructed an\\(\\mathsf{{IPP}}\\)for every language computable by a low depth circuit. In this work, we study the computational analogue, where soundness is required to hold only against acomputationally boundedcheating prover. We call such protocolsinteractive arguments of proximity. Assuming the existence of a sub-exponentially secure\\(\\mathsf{{FHE}}\\)scheme, we construct aone-roundargument of proximity forevery languagecomputable in timet, where the running time of the verifier is\\(o(n) + \\mathsf{polylog}(t)\\)and the running time of the prover is\\(\\mathsf{poly}(t)\\). As our second result, assuming sufficiently hard cryptographic\\(\\mathsf{PRG }\\)s, we give a lower bound, showing that the parameters obtained both in the\\(\\mathsf{{IPP}}\\)s of <PERSON><PERSON><PERSON>t al., and in our arguments of proximity, are close to optimal. Finally, we observe that any one-round argument of proximity immediately yields a one-round delegation scheme (without proximity) where the verifier runs inlineartime.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_21"}, {"primary_key": "4387434", "vector": [], "sparse_vector": [], "title": "Practical Free-Start Collision Attacks on 76-step SHA-1.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we analyze the security of the compression function ofSHA-1against collision attacks, or equivalently free-start collisions on the hash function. While a lot of work has been dedicated to the analysis ofSHA-1in the past decade, this is the first time that free-start collisions have been considered for this function. We exploit the additional freedom provided by this model by using a new start-from-the-middle approach in combination with improvements on the cryptanalysis tools that have been developed forSHA-1in the recent years. This results in particular in better differential paths than the ones used for hash function collisions so far. Overall, our attack requires about\\(2^{50}\\)evaluations of the compression function in order to compute a one-block free-start collision for a 76-step reduced version, which is so far the highest number of steps reached for a collision on theSHA-1compression function. We have developed an efficient GPU framework for the highly branching code typical of a cryptanalytic collision attack and used it in an optimized implementation of our attack on recent GTX 970 GPUs. We report that a single cheap US$ 350 GTX 970 is sufficient to find the collision in less than 5 days. This showcases how recent mainstream GPUs seem to be a good platform for expensive and even highly-branching cryptanalysis computations. Finally, our work should be taken as a reminder that cryptanalysis onSHA-1continues to improve. This is yet another proof that the industry should quickly move away from using this function.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_30"}, {"primary_key": "4387435", "vector": [], "sparse_vector": [], "title": "Actively Secure OT Extension with Optimal Overhead.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We describe an actively secure OT extension protocol in the random oracle model with efficiency very close to the passively secure IKNP protocol of <PERSON><PERSON> et al. (Crypto 2003). For computational security parameter\\(\\kappa \\), our protocol requires\\(\\kappa \\)base OTs, and is the first practical, actively secure protocol to match the cost of the passive IKNP extension in this regard. The added communication cost is only additive in\\(O(\\kappa )\\), independent of the number of OTs being created, while the computation cost is essentially two finite field operations per extended OT. We present implementation results that show our protocol takes no more than 5 % more time than the passively secure IKNP extension, in both LAN and WAN environments, and thus is essentially optimal with respect to the passive protocol.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_35"}, {"primary_key": "4387436", "vector": [], "sparse_vector": [], "title": "Structure-Preserving Signatures from Standard Assumptions, Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "Structure-preserving signatures (SPS) are pairing-based signatures where all the messages, signatures and public keys are group elements, with numerous applications in public-key cryptography. We present new, simple and improved SPS constructions under standard assumptions via a conceptually different approach. Our constructions significantly narrow the gap between existing constructions from standard assumptions and optimal schemes in the generic group model.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_14"}, {"primary_key": "4387437", "vector": [], "sparse_vector": [], "title": "An Improved BKW Algorithm for LWE with Applications to Cryptography and Lattices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we study the Learning With Errors problem and its binary variant, where secrets and errors are binary or taken in a small interval. We introduce a new variant of the <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> algorithm, relying on a quantization step that generalizes and fine-tunes modulus switching. In general this new technique yields a significant gain in the constant in front of the exponent in the overall complexity. We illustrate this by solving within half a day a\\(\\mathsf {LWE}\\)instance with dimension\\(n=128\\), modulus\\(q=n^{2}\\), Gaussian noise\\(\\alpha =1/(\\sqrt{n/\\pi } \\log ^2 n)\\)and binary secret, using\\(2^{28}\\)samples, while the previous best result based on BKW claims a time complexity of\\(2^{74}\\)with\\(2^{60}\\)samples for the same parameters. We then introduce variants of\\(\\mathsf {BDD}\\),\\(\\mathsf {GapSVP}\\)and\\(\\mathsf {UniqueSVP}\\), where the target point is required to lie in the fundamental parallelepiped, and show how the previous algorithm is able to solve these variants in subexponential time. Moreover, we also show how the previous algorithm can be used to solve the\\(\\mathsf {BinaryLWE}\\)problem withnsamples in subexponential time\\(2^{(\\ln 2/2+o(1))n/\\log \\log n}\\). This analysis does not require any heuristic assumption, contrary to other algebraic approaches; instead, it uses a variant of an idea by Lyubashevsky to generate many samples from a small number of samples. This makes it possible to asymptotically and heuristically break the\\(\\mathsf {NTRU}\\)cryptosystem in subexponential time (without contradicting its security assumption). We are also able to solve subset sum problems in subexponential time for densityo(1), which is of independent interest: for such density, the previous best algorithm requires exponential time. As a direct application, we can solve in subexponential time the parameters of a cryptosystem based on this problem proposed at TCC 2010.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_3"}, {"primary_key": "4387438", "vector": [], "sparse_vector": [], "title": "Statistical Concurrent Non-malleable Zero-Knowledge from One-Way Functions.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Concurrent non-malleable zero-knowledge(\\(\\mathrm {CNMZK}\\)) protocols are zero-knowledge protocols that are secure even when the adversary interacts with multiple provers and verifiers simultaneously. Recently, the firststatistical\\(\\mathrm {CNMZK}\\)argument for\\(\\mathcal {NP}\\)was constructed by <PERSON><PERSON><PERSON> et al. (TCC’14) under the DDH assumption. In this paper, we construct a statistical\\(\\mathrm {CNMZK}\\)argument for\\(\\mathcal {NP}\\)assuming only the existence of one-way functions. The security is proven via black-box simulation, and the round complexity is\\(\\mathsf {poly}(n)\\). Under the existence of collision-resistant hash functions, the round complexity can be reduced to\\(\\omega (\\log n)\\), which is essentially optimal for black-box concurrent zero-knowledge.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_5"}, {"primary_key": "4387439", "vector": [], "sparse_vector": [], "title": "Observations on the SIMON Block Cipher Family.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we analyse the general class of functions underlying theSimonblock cipher. In particular, we derive efficiently computable and easily implementable expressions for the exact differential and linear behaviour ofSimon-like round functions. Following up on this, we use those expressions for a computer aided approach based on SAT/SMT solvers to find both optimal differential and linear characteristics forSimon. Furthermore, we are able to find all characteristics contributing to the probability of a differential forSimon32 and give better estimates for the probability for other variants. Finally, we investigate a large set ofSimonvariants using different rotation constants with respect to their resistance against differential and linear cryptanalysis. Interestingly, the default parameters seem to be not always optimal.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_8"}, {"primary_key": "4387440", "vector": [], "sparse_vector": [], "title": "Bilinear Entropy Expansion from the Decisional Linear Assumption.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We develop a technique inspired by pseudorandom functions that allows us to increase the entropy available for proving the security of dual system encryption schemes under the Decisional Linear Assumption. We show an application of the tool to Attribute-Based Encryption by presenting a Key-Policy ABE scheme that is fully-secure under DLIN with short public parameters.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_26"}, {"primary_key": "4387441", "vector": [], "sparse_vector": [], "title": "Sieving for Shortest Vectors in Lattices Using Angular Locality-Sensitive Hashing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "By replacing the brute-force list search in sieving algorithms with <PERSON><PERSON><PERSON>’s angular locality-sensitive hashing (LSH) method, we get both theoretical and practical speedups for solving the shortest vector problem (SVP) on lattices. Combining angular LSH with a variant of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>’s heuristic sieve algorithm, we obtain heuristic time and space complexities for solving SVP of\\(2^{0.3366n + o(n)}\\)and\\(2^{0.2075n + o(n)}\\)respectively, while combining the same hash family with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>’ GaussSieve algorithm leads to an algorithm with (conjectured) heuristic time and space complexities of\\(2^{0.3366n + o(n)}\\). Experiments with the GaussSieve-variant show that in moderate dimensions the proposed HashSieve algorithm already outperforms the GaussSieve, and the practical increase in the space complexity is much smaller than the asymptotic bounds suggest, and can be further reduced with probing. Extrapolating to higher dimensions, we estimate that a fully optimized and parallelized implementation of the GaussSieve-based HashSieve algorithm might need a few core years to solve SVP in dimension 130 or even 140.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_1"}, {"primary_key": "4387442", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of Full Sprout.", "authors": ["<PERSON><PERSON>", "<PERSON>Plase<PERSON>"], "summary": "A new method for reducing the internal state size of stream cipher registers has been proposed in FSE 2015, allowing to reduce the area in hardware implementations. Along with it, an instantiated proposal of a cipher was also proposed: Sprout. In this paper, we analyze the security of Sprout, and we propose an attack that recovers the whole key more than\\(2^{10}\\)times faster than exhaustive search and has very low data complexity. The attack can be seen as a divide-and-conquer evolved technique, that exploits the non-linear influence of the key bits on the update function. We have implemented the attack on a toy version of Sprout, that conserves the main properties exploited in the attack. The attack completely matches the expected complexities predicted by our theoretical cryptanalysis, which proves its validity. We believe that our attack shows that a more careful analysis should be done in order to instantiate the proposed design method.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_32"}, {"primary_key": "4387443", "vector": [], "sparse_vector": [], "title": "Short Group Signatures via Structure-Preserving Signatures: Standard Model Security from Simple Assumptions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Group signatures are a central cryptographic primitive which allows users to sign messages while hiding their identity within a crowd of group members. In the standard model (without the random oracle idealization), the most efficient constructions rely on the Groth-Sahai proof systems (Eurocrypt’08). The structure-preserving signatures of Abeet al.(Asiacrypt’12) make it possible to design group signatures based on well-established, constant-size number theoretic assumptions (a.k.a. “simple assumptions”) like the Symmetric eXternal Diffie-Hellman or Decision Linear assumptions. While much more efficient than group signatures built on general assumptions, these constructions incur a significant overhead w.r.t. constructions secure in the idealized random oracle model. Indeed, the best known solution based on simple assumptions requires 2.8 kB per signature for currently recommended parameters. Reducing this size and presenting techniques for shorter signatures are thus natural questions. In this paper, our first contribution is to significantly reduce this overhead. Namely, we obtain the first fully anonymous group signatures based on simple assumptions with signatures shorter than 2 kB at the 128-bit security level. In dynamic (resp. static) groups, our signature length drops to 1.8 kB (resp. 1 kB). This improvement is enabled by two technical tools. As a result of independent interest, we first construct a new structure-preserving signature based on simple assumptions which shortens the best previous scheme by\\(25\\,\\%\\). Our second tool is a method for attaining anonymity in the strongest sense using a new CCA2-secure encryption scheme which is also a Groth-Sahai commitment.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_15"}, {"primary_key": "4387444", "vector": [], "sparse_vector": [], "title": "Efficient Constant Round Multi-party Computation Combining BMR and SPDZ.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, there has been huge progress in the field of concretely efficient secure computation, even while providing security in the presence ofmalicious adversaries. This is especially the case in the two-party setting, where constant-round protocols exist that remain fast even over slow networks. However, in the multi-party setting, all concretely efficient fully-secure protocols, such as SPDZ, require many rounds of communication. In this paper, we present an MPC protocol that is fully-secure in the presence of malicious adversaries and for any number of corrupted parties. Our construction is based on the constant-round BMR protocol of <PERSON> et al., and is the first fully-secure version of that protocol that makes black-box usage of the underlying primitives, and is therefore concretely efficient. Our protocol includes an online phase that is extremely fast and mainly consists of each party locally evaluating a garbled circuit. For the offline phase we present both a generic construction (using any underlying MPC protocol), and a highly efficient instantiation based on the SPDZ protocol. Our estimates show the protocol to be considerably more efficient than previous fully-secure multi-party protocols.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_16"}, {"primary_key": "4387445", "vector": [], "sparse_vector": [], "title": "Relational Hash: Probabilistic Hash for Verifying Relations, Secure Against Forgery and More.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional cryptographic hash functions allow one to easily check whether the original plaintexts are equal or not, given a pair of hash values. Probabilistic hash functions extend this concept where given a probabilistic hash of a value and the value itself, one can efficiently check whether the hash corresponds to the given value. However, given distinct probabilistic hashes of the same value it is not possible to check whether they correspond to the same value. In this work we introduce a new cryptographic primitive calledRelational Hashusing which, given a pair of (relational) hash values, one can determine whether the original plaintexts were related or not. We formalize various natural security notions for the Relational Hash primitive - one-wayness, twin one-wayness, unforgeability and oracle simulatibility. We develop a Relational Hash scheme for discovering linear relations among bit-vectors (elements of\\(\\mathbb {F}_2^n\\)) and\\(\\mathbb {F}_p\\)-vectors. Using the linear Relational Hash schemes we develop Relational Hashes for detecting proximity in terms of hamming distance. The proximity Relational Hashing schemes can be adapted to a privacy preserving biometric identification scheme, as well as a privacy preserving biometric authentication scheme secure against passive adversaries.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_25"}, {"primary_key": "4387446", "vector": [], "sparse_vector": [], "title": "The Iterated Random Permutation Problem with Applications to Cascade Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce and study theiterated random permutation problem, which asks how hard it is to distinguish, in a black-box way, ther-th power of a random permutation from a uniformly random permutation of a set of sizeN. We show that this requires\\(\\varOmega (N)\\)queries (even for a two-sided, adaptive adversary). As a direct application of this result, we show that cascading a block cipher with the same key cannot degrade its security (as a pseudorandom permutation) more than negligibly.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_17"}, {"primary_key": "4387447", "vector": [], "sparse_vector": [], "title": "Multi-key Security: The Even-Mansour Construction Revisited.", "authors": ["<PERSON>", "Atul Luykx"], "summary": "At ASIACRYPT 1991, <PERSON> <PERSON> <PERSON><PERSON> introduced a block cipher construction based on a single permutation. Their construction has since been lauded for its simplicity, yet also criticized for not providing the same security as other block ciphers against generic attacks. In this paper, we prove that if a small number of plaintexts are encrypted under multiple independent keys, the Even-<PERSON><PERSON> construction surprisingly offers similar security as an ideal block cipher with the same block and key size. Note that thismulti-key settingis of high practical relevance, as real-world implementations often allow frequent rekeying. We hope that the results in this paper will further encourage the use of the Even-<PERSON><PERSON> construction, especially when a secure and efficient implementation of a key schedule would result in significant overhead.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_10"}, {"primary_key": "4387448", "vector": [], "sparse_vector": [], "title": "Bloom Filters in Adversarial Environments.", "authors": ["<PERSON><PERSON>", "E<PERSON>"], "summary": "Many efficient data structures use randomness, allowing them to improve upon deterministic ones. Usually, their efficiency and/or correctness are analyzed using probabilistic tools under the assumption that the inputs and queries areindependentof the internal randomness of the data structure. In this work, we consider data structures in a more robust model, which we call theadversarial model. Roughly speaking, this model allows an adversary to choose inputs and queriesadaptivelyaccording to previous responses. Specifically, we consider a data structure known as “Bloom filter” and prove a tight connection between Bloom filters in this model and cryptography. A Bloom filter represents a setSof elements approximately, by using fewer bits than a precise representation. The price for succinctness is allowing some errors: for any\\(x \\in S\\)it should always answer ‘Yes’, and for any\\(x \\notin S\\)it should answer ‘Yes’ only with small probability. In the adversarial model, we consider both efficient adversaries (that run in polynomial time) and computationally unbounded adversaries that are only bounded in the amount of queries they can make. For computationally bounded adversaries, we show that non-trivial (memory-wise) Bloom filters exist if and only if one-way functions exist. For unbounded adversaries we show that there exists a Bloom filter for sets of sizenand error\\(\\varepsilon \\), that is secure againsttqueries and uses only\\(O(n \\log {\\frac{1}{\\varepsilon }}+t)\\)bits of memory. In comparison,\\(n\\log {\\frac{1}{\\varepsilon }}\\)is the best possible under a non-adaptive adversary.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_28"}, {"primary_key": "4387449", "vector": [], "sparse_vector": [], "title": "Impossibility of Black-Box Simulation Against Leakage Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work, we show how to use the positive results on succinct argument systems to prove impossibility results on leakage-resilient black-box zero knowledge. This recently proposed notion of zero knowledge deals with an adversary that can make leakage queries on the state of the prover. Our result holds for black-box simulation only and we also give some insights on the non-black-box case. Additionally, we show that, for several functionalities, leakage-resilient multi-party computation is impossible (regardless of the number of players and even if just one player is corrupted). More in details, we achieve the above results by extending a technique of [<PERSON>, <PERSON>, <PERSON> – PKC13] to prove lower bounds for leakage-resilient security. Indeed, we use leakage queries to run an execution of a communication-efficient protocol in the head of the adversary. Moreover, to defeat the black-box simulator we connect the above technique for leakage resilience to security against reset attacks. Our results show that the open problem of [<PERSON><PERSON><PERSON>, <PERSON>yal, Pandey – Crypto 14] (i.e., continual leakage-resilient proofs without a common reference string) has a negative answer when security through black-box simulation is desired. Moreover our results close the open problem of [<PERSON> et al. – STOC 12] for the case of black-box simulation (i.e., the possibility of continual leakage-resilient secure computation without a leak-free interactive preprocessing).", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_7"}, {"primary_key": "4387450", "vector": [], "sparse_vector": [], "title": "Round-Optimal Black-Box Two-Party Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In [Eurocrypt 2004] <PERSON> and <PERSON><PERSON><PERSON> establish theexactround complexity of secure two-party computation with respect to black-box proofs of security. They prove that 5 rounds are necessary for secure two-party protocols (4-round are sufficient if only one party receives the output) and provide a protocol that matches such lower bound. The main challenge when designing such protocol is to parallelize the proofs of consistency provided by both parties – necessary when security against malicious adversaries is considered– in 4 rounds. Toward this goal they employ specific proofs in which the statement can be unspecified till the last round but that require non-black-box access to the underlying primitives. A rich line of work [1,9,11,13,24] has shown that the non-black-box use of the cryptographic primitive in secure two-party computation is not necessary by providing black-box constructions matching basically all the feasibility results that were previously demonstrated only via non-black-box protocols. All such constructions however are far from being round optimal. The reason is that they are based on cut-and-choose mechanisms where one party can safely take an action onlyafterthe other party has successfully completed the cut-and-choose phase, therefore requiring additional rounds. A natural question is whether round-optimal constructions do inherently require non-black-box access to the primitives, and whether the lower bound shown by <PERSON> and <PERSON><PERSON><PERSON> can only be matched by a non-black-box protocol. In this work we show that round-optimality is achievable even with only black-box access to the primitives. We provide the first 4-round black-box oblivious transfer based on any enhanced trapdoor permutation. Plugging a parallel version of our oblivious transfer into the black-box non-interactive secure computation protocol of [12] we obtain the first round-optimal black-box two-party protocol in the plain model for any functionality.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_17"}, {"primary_key": "4387451", "vector": [], "sparse_vector": [], "title": "Consolidating Masking Schemes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we investigate relations between several masking schemes. We show that the <PERSON>hai–<PERSON>–Wagner private circuits construction is closely related to Threshold Implementations and the Trichina gate. The implications of this observation are manifold. We point out a higher-order weakness in higher-order Threshold Implementations, suggest a mitigation and provide new sharings that use a lower number of input shares.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_37"}, {"primary_key": "4387452", "vector": [], "sparse_vector": [], "title": "Links Among Impossible Differential, Integral and Zero Correlation Linear Cryptanalysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Alkhzaimi", "<PERSON>"], "summary": "As two important cryptanalytic methods, impossible differential and integral cryptanalysis have attracted much attention in recent years. Although relations among other cryptanalytic approaches have been investigated, the link between these two methods has been missing. The motivation in this paper is to fix this gap and establish links between impossible differential cryptanalysis and integral cryptanalysis. Firstly, by introducing the concept of structure and dual structure, we prove that\\(a\\rightarrow b\\)is an impossible differential of a structure\\(\\mathcal E\\)if and only if it is a zero correlation linear hull of the dual structure\\(\\mathcal E^\\bot \\). Meanwhile, our proof shows that the automatic search tool presented by <PERSON> and <PERSON> could find all impossible differentials of both Feistel structures with SP-type round functions and SPN structures. Secondly, by establishing some boolean equations, we show that a zero correlation linear hull always indicates the existence of an integral distinguisher. With this observation we improve the number of rounds of integral distinguishers of Feistel structures, CAST-256, SMS4 and Camellia. Finally, we conclude that anr-round impossible differential of\\(\\mathcal E\\)always leads to anr-round integral distinguisher of the dual structure\\(\\mathcal E^\\bot \\). In the case that\\(\\mathcal E\\)and\\(\\mathcal E^\\bot \\)are linearly equivalent, we derive a direct link between impossible differentials and integral distinguishers of\\(\\mathcal E\\). Our results could help to classify different cryptanalytic tools and facilitate the task of evaluating security of block ciphers against various cryptanalytic approaches.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_5"}, {"primary_key": "4387453", "vector": [], "sparse_vector": [], "title": "Integral Cryptanalysis on Full MISTY1.", "authors": ["<PERSON><PERSON>"], "summary": "MISTY1 is a block cipher designed by <PERSON><PERSON> in 1997. It was well evaluated and standardized by projects, such as CRYPTREC, ISO/IEC, and NESSIE. In this paper, we propose a key recovery attack on the full MISTY1, i.e., we show that 8-round MISTY1 with 5 FL layers does not have 128-bit security. Many attacks against MISTY1 have been proposed, but there is no attack against the full MISTY1. Therefore, our attack is the first cryptanalysis against the full MISTY1. We construct a new integral characteristic by using the propagation characteristic of the division property, which was proposed in 2015. We first improve the division property by optimizing a public S-box and then construct a 6-round integral characteristic on MISTY1. Finally, we recover the secret key of the full MISTY1 with\\(2^{63.58}\\)chosen plaintexts and\\(2^{121}\\)time complexity. Moreover, if we can use\\(2^{63.994}\\)chosen plaintexts, the time complexity for our attack is reduced to\\(2^{107.9}\\). Note that our cryptanalysis is a theoretical attack. Therefore, the practical use of MISTY1 will not be affected by our attack.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_20"}, {"primary_key": "4387454", "vector": [], "sparse_vector": [], "title": "A Punctured Programming Approach to Adaptively Secure Functional Encryption.", "authors": ["<PERSON>"], "summary": "We propose the first construction for achieving adaptively secure functional encryption (FE) for poly-sized circuits (without complexity leveraging) from indistinguishability obfuscation (\\(i\\mathcal {O}\\)). Our reduction has polynomial loss to the underlying primitives. We develop a “punctured programming” approach to constructing and proving systems where outside of obfuscation we rely only on primitives realizable from pseudo random generators. Our work consists of two constructions. Our first FE construction is provably secure against any attacker that is limited to making all of its private key queriesafterit sees the challenge ciphertext. (This notion implies selective security.) Our construction makes use of an we introduce called puncturable deterministic encryption (PDE) which may be of independent interest. With this primitive in place we show a simple FE construction. We then provide a second construction that achieves adaptive security from indistinguishability obfuscation. Our central idea is to achieve an adaptively secure functional encryption by bootstrapping from a one-bounded FE scheme that is adaptively secure. By using bootstrapping we can use “selective-ish” techniques at the outer level obfuscation level and push down the challenge of dealing with adaptive security to the one-bounded FE scheme, where it has been already been solved. We combine our bootstrapping framework with a new “key signaling” technique to achieve our construction and proof. Altogether, we achieve the first construction and proof for adaptive security for functional encryption.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_33"}, {"primary_key": "4387455", "vector": [], "sparse_vector": [], "title": "(Almost) Optimal Constructions of UOWHFs from 1-to-1, Regular One-Way Functions and Beyond.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>"], "summary": "We revisit the problem of black-box constructions of universal one-way hash functions (UOWHFs) from several typical classes of one-way functions (OWFs), and give respective constructions that either improve or generalize the best previously known. For any 1-to-1 one-way function, we give an optimal construction of UOWHFs with key and output length\\(\\varTheta (n)\\)by making a single call to the underlying OWF. This improves the constructions of <PERSON><PERSON> and <PERSON> (STOC 1989) and <PERSON> and <PERSON> (Eurocrypt 1990) that need key length\\(O(n\\cdot \\omega ({\\log {n})})\\). For any known-(almost-)regular one-way function with known hardness, we give an optimal construction of UOWHFs with key and output length\\(\\varTheta (n)\\)and a single call to the one-way function. For any known-(almost-)regular one-way function, we give a construction of UOWHFs with key and output length\\(O(n{\\cdot }\\omega (1))\\)and by making\\(\\omega (1)\\)non-adaptive calls to the one-way function. This improves the construction of <PERSON><PERSON> and <PERSON> (Latincrypt 2012) that requires key and output length\\(O(n{\\cdot }\\omega (\\log {n}))\\)and\\(\\omega (\\log {n})\\)calls. For any weakly-regular one-way function introduced by <PERSON> et al. at TCC 2015 (i.e., the set of inputs with maximal number of siblings is of an\\(n^{-c}\\)-fraction for some constantc), we give a construction of UOWHFs with key length\\(O(n{\\cdot }{\\log }n)\\)and output length\\(\\varTheta (n)\\). This generalizes the construction of Ames et al. (Asiacrypt 2012) which requires an unknown-regular one-way function (i.e.,\\(c=0\\)). Along the way, we use several techniques that might be of independent interest. We show that almost 1-to-1 (except for a negligible fraction) one-way functions and known (almost-)regular one-way functions are equivalent in the known-hardness (or non-uniform) setting, by giving an optimal construction of the former from the latter. In addition, we show how to transform any one-way function that is far from regular (but only weakly regular on a noticeable fraction of domain) into an almost-regular one-way function.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-48000-7_11"}, {"primary_key": "4387456", "vector": [], "sparse_vector": [], "title": "Fast Correlation Attacks over Extension Fields, Large-Unit Linear Approximation and Cryptanalysis of SNOW 2.0.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Several improvements of fast correlation attacks have been proposed during the past two decades, with a regrettable lack of a better generalization and adaptation to the concrete involved primitives, especially to those modern stream ciphers based on word-based LFSRs. In this paper, we develop some necessary cryptanalytic tools to bridge this gap. First, a formal framework for fast correlation attacks over extension fields is constructed, under which the theoretical predictions of the computational complexities for both the offline and online/decoding phase can be reliably derived. Our decoding algorithm makes use of Fast Walsh Transform (FWT) to get a better performance. Second, an efficient algorithm to compute the large-unit distribution of a broad class of functions is proposed, which allows to find better linear approximations than the bitwise ones with low complexity in symmetric-key primitives. Last, we apply our methods to SNOW 2.0, an ISO/IEC 18033-4 standard stream cipher, which results in the significantly reduced complexities all below\\(2^{164.15}\\). This attack is more than\\(2^{49}\\)times better than the best published result at Asiacrypt 2008. Our results have been verified by experiments on a small-scale version of SNOW 2.0.", "published": "2015-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-662-47989-6_31"}]