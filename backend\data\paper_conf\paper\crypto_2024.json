[{"primary_key": "558463", "vector": [], "sparse_vector": [], "title": "Aggregating Falcon Signatures with LaBRADOR.", "authors": ["<PERSON>", "Diego F. <PERSON>a", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Several prior works have suggested to use non-interactive arguments of knowledge with short proofs to aggregate signatures of Falcon, which is part of the first post-quantum signatures selected for standardization by NIST. Especially LaBRADOR, based on standard structured lattice assumptions and published at CRYPTO’23, seems promising to realize this task. However, no prior work has tackled this idea in a rigorous way. In this paper, we thoroughly prove how to aggregate Falcon signatures using LaBRADOR. We start by providing the first complete knowledge soundness analysis for thenon-interactiveversion of LaBRADOR . Here, the multi-round and recursive nature of LaBRADOR requires a complex and thorough analysis. For this purpose, we introduce the notion ofpredicate special soundness (PSS). This is a general framework for evaluating the knowledge error of complex Fiat-Shamir arguments of knowledge protocols in a modular fashion, which we believe to be of independent interest. We then explain the exact steps to take in order to adapt the non-interactive LaBRADOR proof system for aggregating Falcon signatures and provide concrete proof size estimates. Additionally, we formalize the folklore approach of obtaining aggregate signatures from the class of hash-then-sign signatures through arguments of knowledge.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_3"}, {"primary_key": "558464", "vector": [], "sparse_vector": [], "title": "CDS Composition of Multi-round Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We revisit the <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Sc<PERSON>nmakers (CDS) approach for composing sigma protocols, and adapt it to a setting in which the underlying protocols have multiple rounds of interaction. The goal of CDS composition is to prove compound NP-relations by combining multiple “atomic” proof systems. Its key feature is that it interacts with the atomic proofs in a generic fashion, enabling simpler and more efficient implementation. Recent developments in multi-round protocols call for the adaptation of CDS composition beyond its original scope, which not only was restricted to three-move protocols but in fact fails in the multi-round case, as well as in the composition of so-calledk-special sound proofs. We propose a new method for multi-round composition in the plain model, in a soundness preserving way and with an “offline” zero-knowledge simulation property. The need for handling arbitrary monotone access structures in\\(\\textsf{mNC}^1\\), which is all Boolean function families represented by polynomial-size formulas over some fixed complete basis, leads us to identify a complexity theoretic problem of independent interest. Prior to our work, multi-round composition was either restricted to the random oracle model, or worked only for argument systems, and moreover required heavy “online” zero-knowledge simulation.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_12"}, {"primary_key": "558465", "vector": [], "sparse_vector": [], "title": "Malicious Security for SCALES - Outsourced Computation with Ephemeral Servers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "SCALES (Small Clients And Larger Ephemeral Servers) model is a recently proposed model for MPC (<PERSON><PERSON> et al., TCC 2022). While the SCALES model offers several attractive features for practical large-scale MPC, the result of <PERSON><PERSON> et al. only offered semi-honest secure protocols in this model. We present a new efficient SCALES protocol secure against malicious adversaries, for general Boolean circuits. We start with the base construction of <PERSON><PERSON> et al. and design and use a suite of carefully defined building blocks that may be of independent interest. The resulting protocol is UC-secure without honest majority, with a CRS and bulletin-board as setups, and allows publicly identifying deviations from correct execution.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_1"}, {"primary_key": "558466", "vector": [], "sparse_vector": [], "title": "Compressing Unit-Vector Correlations via Sparse Pseudorandom Generators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Aunit-vector (UV) correlationis an additive secret-sharing of a vector of lengthBthat contains 1 in a secret random position and 0’s elsewhere. UV correlations are a useful resource for many cryptographic applications, including low-communication secure multiparty computation and multi-server private information retrieval. However, current practical methods for securely generating UV correlations involve a significant communication costper instance, and become even more expensive when requiring security against malicious parties. In this work, we present a new approach for constructing apseudorandom correlation generator(PCG) for securely generatingnindependent instances of UV correlations of any polynomial lengthB. Such a PCG compresses thenUV instances into correlated seeds whose length is sublinear in the description size\\(n\\cdot \\log B\\). Our new PCGs apply in both the honest-majority and dishonest-majority settings, and are based on a variety of assumptions. In particular, in the honest-majority case they only require “unstructured” assumptions. Our PCGs give rise to secure end-to-end protocols for generatingninstances of UV correlations witho(n) bits of communication. This applies even to an authenticated variant of UV correlations, which is useful for security against malicious parties. Unlike previous theoretical solutions, some instances of our PCGs offer good concrete efficiency. Our technical approach is based on combining a low-degreesparse pseudorandom generator, mapping a sparse seed to a pseudorandom sparse output, with homomorphic secret sharing for low-degree polynomials. We then reduce such sparse PRGs tolocalPRGs over large alphabets, and explore old and new approaches for maximizing the stretch of such PRGs while minimizing their locality. Finally, towards further compressing the PCG seeds, we present a new PRG-based construction of a multiparty distributed point function (DPF), whose outputs are degree-1 Shamir-shares of a secret point function. This result is independently motivated by other DPF applications.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_11"}, {"primary_key": "558467", "vector": [], "sparse_vector": [], "title": "Attribute Based Encryption for Turing Machines from Lattices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We provide the first attribute based encryption (ABE) scheme for Turing machines supporting unbounded collusions from lattice assumptions. In more detail, the encryptor encodes an attribute\\(\\textbf{x}\\)together with a boundton the machine running time and a messageminto the ciphertext, the key generator embeds a Turing machineMinto the secret key and decryption returnsmif and only if\\(M(\\textbf{x})=1\\). Crucially, the input\\(\\textbf{x}\\)and machineMcan be of unbounded size, the time boundtcan be chosen dynamically for each input and decryption runs in input specific time. Previously the best known ABE for uniform computation supported only non-deterministic log space Turing machines (\\(\\textsf{NL})\\)from pairings (<PERSON> and <PERSON><PERSON>, Eurocrypt 2020). In the post-quantum regime, the state of the art supports non-deterministic finite automata from LWE in thesymmetrickey setting (Agrawal, Maitra and Yamada, Crypto 2019). In more detail, our results are: We construct the first ABE for\\(\\textsf{NL}\\)from the LWE, evasive LWE (Wee, Eurocrypt 2022 and Tsabary, Crypto 2022) and Tensor LWE (Wee, Eurocrypt 2022) assumptions. This yields the first (conjectured) post-quantum ABE for\\(\\textsf{NL}\\). Relying on LWE, evasive LWE and a new assumption calledcircular tensorLWE, we construct ABE for all Turing machines. At a high level, the circular tensor LWE assumption incorporates circularity into the tensor LWE (Wee, Eurocrypt 2022) assumption. Towards our ABE for Turing machines, we obtain the first CP-ABE for circuits of unbounded depth and size from the same assumptions – this may be of independent interest.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_11"}, {"primary_key": "558468", "vector": [], "sparse_vector": [], "title": "k-SUM in the Sparse Regime: Complexity and Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the average-casek-SUM problem, givenrintegers chosen uniformly at random from\\( \\{ 0,\\dots ,M-1 \\}\\), the objective is to find a “solution” set ofknumbers that sum to 0 moduloM. In thedenseregime of\\(M \\le r^k\\), where solutions exist with high probability, the complexity of these problems is well understood. Much less is known in thesparseregime of\\(M\\gg r^k\\), where solutions are unlikely to exist. Motivated by applications to cryptography, we initiate the study of thesparseregime fork-SUM and its variantk-XOR, especially theirplantedversions, where a random solution is planted in a randomly generated instance and has to be recovered. We provide evidence for the hardness of these problems and show applications to constructing public-key encryption schemes. Our contributions are summarized below. Complexity.We study the complexity of these problems in the sparse regime and show: Conditional Lower Bounds:Assuming established conjectures about the hardness of average-case (non-planted)k-SUM/k-XOR when\\(M = r^k\\), we provide non-trivial lower bounds on the running time of algorithms for plantedk-SUM when\\(r^k\\le M\\le r^{2k}\\). Hardness Amplification:We show that for any\\(M \\ge r^k\\), if an algorithm running in timeTsolves plantedk-SUM/k-XOR with success probability\\(\\varOmega (1/\\textrm{polylog}(r))\\), then there is an algorithm running in time\\(\\widetilde{{\\mathcal {O}}}(T)\\)that solves it with probability\\((1-o(1))\\). This enables us to use even relatively mild hardness ofk-SUM/k-XOR in our cryptographic constructions. Our primary technical contribution is the proof of this result, which departs significantly from existing approaches to hardness amplification. New Reductions and Algorithms.We provide reductions fork-SUM/k-XOR from search to decision, as well as worst-case and average-case reductions to the Subset Sum problem fromk-SUM. Additionally, we present a new algorithm for average-casek-XOR that is faster than known worst-case algorithms at low densities. Applications.We show that by additionally assuming mild hardness ofk-XOR, we can construct Public Key Encryption (PKE) from a weaker variant of the Learning Parity with Noise (LPN) problem than was known before. In particular, such LPN hardness does not appear to imply PKE on its own – this suggests thatk-XOR/k-SUM can be used to bridge “minicrypt” and “cryptomania” in some cases. We are optimistic that this technique will find other applications in cryptography", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_10"}, {"primary_key": "558469", "vector": [], "sparse_vector": [], "title": "Time-Lock Puzzles from Lattices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Time-lock puzzles (TLP) are a cryptographic tool that allow one to encrypt a message into the future, for a predetermined amount of timeT. At present, we have only two constructions with provable security: One based on the repeated squaring assumption and the other based on indistinguishability obfuscation (iO). Basing TLP onanyother assumption is a long-standing question, further motivated by the fact that known constructions are broken by quantum algorithms. In this work, we propose a new approach to construct time-lock puzzles based on lattices, and therefore with plausible post-quantum security. We obtain the following main results: In the preprocessing model, where a one-time public-coin preprocessing is allowed, we obtain a time-lock puzzle with encryption time\\(\\log (T)\\). In the plain model, where the encrypter does all the computation, we obtain a time-lock puzzle with encryption time\\(\\sqrt{T}\\). Both constructions assume the existence of any sequential functionf, and the hardness of the circular small-secret learning with errors (LWE) problem. At the heart of our results is a new construction of succinct randomized encodings (SRE) forT-folded repeated circuits, where the complexity of the encoding is\\(\\sqrt{T}\\). This is the first construction of SRE where the overall complexity of the encoding algorithm is sublinear in the runtimeT, and which is not based on iO. Using our SRE we directly obtain the first non-interactive RAM delegation scheme with sublinear complexity (in the number of stepsT), again without iO. Finally, we also propose a new heuristic construction of SREs, and consequently of TLPs, with fully-efficient encoding complexity\\(\\log (T)\\). Our scheme is inspired by iO techniques, but carefully sidesteps the regime of zeroizing attacks that plague lattice-based iO candidates.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_13"}, {"primary_key": "558470", "vector": [], "sparse_vector": [], "title": "Improved Alternating-Moduli PRFs and Post-quantum Signatures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We revisit the alternating moduli paradigm for constructing symmetric key primitives with a focus on constructing highly efficient protocols to evaluate them using secure multi-party computation (MPC). The alternating moduli paradigm of <PERSON><PERSON> et al. (TCC 2018) enables the construction of various symmetric key primitives with the common characteristic that the inputs are multiplied by two linear maps over different moduli, first over\\(\\mathbb {F}_2\\)and then over\\(\\mathbb {F}_3\\). The first contribution focuses on efficient two-party evaluation of alternating moduli PRFs, effectively building an oblivious pseudo random function. We present a generalization of the PRF proposed by <PERSON><PERSON> et al. (TCC 18) along with methods to lower the communication and computation. We then provide several variants of our protocols, with different computation and communication tradeoffs, for evaluating the PRF. Most are in the OT/VOLE hybrid model while one is based on specialized garbling. Our most efficient protocol effectively is about\\(3\\times \\)faster and requires\\(1.3\\times \\)less communication. Our next contribution is the efficient evaluation of the OWF\\(f(x)=\\textbf{B}\\cdot _3 (\\textbf{A}\\cdot _2 x)\\)proposed by <PERSON><PERSON> et al. (CRYPTO 21) where\\(\\textbf{A}\\in \\mathbb {F}^{m\\times n}_2, \\textbf{B}\\in \\mathbb {F}^{t\\times m}_3\\)and\\(\\cdot _p\\)is multiplication modp. This surprisingly simple OWF can be evaluated within MPC by secret sharing\\({\\llbracket x \\rrbracket }\\)over\\(\\mathbb {F}_2\\), locally computing\\({\\llbracket v \\rrbracket }=\\textbf{A}\\cdot _2 {\\llbracket x \\rrbracket }\\), performing a modulus switching protocol to\\(\\mathbb {F}_3\\)shares, followed by locally computing the output shares\\({\\llbracket y \\rrbracket }=B\\cdot _3 {\\llbracket v \\rrbracket }\\). We design a bespoke MPC-in-the-Head (MPCitH) signature scheme that evaluates the OWF, achieving state of art performance. The resulting signature has a size ranging from 4.0–5.5 KB, achieving between\\(2\\text {-}3\\times \\)reduction compared to Dinur et al. To the best of our knowledge, this is only\\(\\approx 5\\%\\)larger than the smallest signature based on symmetric key primitives, including the latest NIST PQC competition submissions. We additionally show that our core techniques can be extended to build very small post-quantum ring signatures for small-medium sized rings that are competitive with state-of-the-art lattice based schemes. Our techniques are in fact more generally applicable to set membership in MPCitH.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_9"}, {"primary_key": "558471", "vector": [], "sparse_vector": [], "title": "Formally Verifying Kyber - Episode V: Machine-Checked IND-CCA Security and Correctness of ML-KEM in EasyCrypt.", "authors": ["<PERSON>", "Santiago Arranz <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a formally verified proof of the correctness and IND-CCA security of ML-KEM, theKyber-based Key Encapsulation Mechanism (KEM) undergoing standardization by NIST. The proof is machine-checked in EasyCrypt and it includes: 1) A formalization of the correctness (decryption failure probability) and IND-CPA security of theKyberbase public-key encryption scheme, following <PERSON><PERSON> et al. at Euro S&P 2018; 2) A formalization of the relevant variant of the Fujisaki-Okamoto transform in the Random Oracle Model (ROM), which follows closely (but not exactly) <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON> at TCC 2017; 3) A proof that the IND-CCA security of the ML-KEM specification and its correctness as a KEM follows from the previous results; 4) Two formally verified implementations of ML-KEM written in Jasmin that are provably constant-time, functionally equivalent to the ML-KEM specification and, for this reason, inherit the provable security guarantees established in the previous points. The top-level theorems give self-contained concrete bounds for the correctness and security of ML-KEM down to (a variant of) Module-LWE. We discuss how they are built modularly by leveraging various EasyCrypt features.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_12"}, {"primary_key": "558472", "vector": [], "sparse_vector": [], "title": "MPC for Tech Giants (GMPC): <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> and the Lilliputians to Cooperate Amicably.", "authors": ["Bar Alon", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the current digital world, large organizations (sometimes referred to as tech giants) provide service to extremely large numbers of users. The service provider is often interested in computing various data analyses over the private data of its users, which in turn have their incentives to cooperate, but do not necessarily trust the service provider. In this work, we introduce the<PERSON><PERSON><PERSON> multi-party computation model(GMPC) to realistically capture the above scenario. The GMPC model considers a single highly powerful party, called theserverorGulliver, that is connected tonusers over a star topology network (alternatively formulated as a full network, where the server can block any message). The users are significantly less powerful than the server, and, in particular, should have both computation and communication complexities that are polylogarithmic inn. Protocols in the GMPC model should be secure against malicious adversaries that may corrupt a subset of the users and/or the server. Designing protocols in the GMPC model is a delicate task, since users can only hold information about\\(\\textrm{polylog}(n)\\)other users (and, in particular, can only communicate with\\(\\textrm{polylog}(n)\\)other users). In addition, the server can block any message between any pair of honest parties. Thus, reaching an agreement becomes a challenging task. Nevertheless, we design generic protocols in the GMPC model, assuming that at most\\(\\alpha <{1/8}\\)fraction of the users may be corrupted (in addition to the server). Our main contribution is a variant of <PERSON><PERSON>’s committee election protocol [FOCS 1999] that is secure in the GMPC model. Given this tool we show: Assuming fully homomorphic encryption (FHE), any computationally efficient function with\\(O\\left( n\\cdot \\textrm{polylog}(n)\\right) \\)-size output can be securely computed in the GMPC model. Any function that can be computed by a circuit of\\(O(\\textrm{polylog}(n))\\)depth,\\(O\\left( n\\cdot \\textrm{polylog}(n)\\right) \\)size, and bounded fan-in and fan-out can be securely computed in the GMPC model assuming vector commitment schemes (without assuming FHE). In particular,sortingcan be securely computed in the GMPC model assuming vector commitment schemes. This has important applications for theshuffle model of differential privacy, and resolves an open question of Bell et al. [CCS 2020].", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_3"}, {"primary_key": "558473", "vector": [], "sparse_vector": [], "title": "Constant-Round Arguments for Batch-Verification and Bounded-Space Computations from One-Way Functions.", "authors": ["Noga Amit", "<PERSON>"], "summary": "What are the minimal cryptographic assumptions that suffice for constructing efficient argument systems, and for which tasks? Recently, <PERSON><PERSON> and <PERSON><PERSON><PERSON> [STOC 2023] showed that one-way functions suffice for constructing constant-round arguments for bounded-depth computations. In this work we ask: what other tasks have efficient argument systems based only on one-way functions? We show two positive results: First, we construct a new argument system for batch-verification ofk\\(\\textsf{UP}\\)statements (\\(\\textsf{NP}\\)statements with a unique witness) for witness relations that are verifiable in depthD. TakingMto be the length of a single witness, the communication complexity is\\(O(\\log k) \\cdot (M + k \\cdot D \\cdot n^{\\sigma })\\), where\\(\\sigma > 0\\)is an arbitrarily small constant. In particular, the communication is quasi-linear in the length ofa single witness, so long as\\({k < M / (D \\cdot n^{\\sigma })}\\). The number of rounds is constant and the honest prover runs in polynomial time given witnesses for allkinputs’ membership in the language. Our second result is a constant-round doubly-efficient argument system for languages in\\(\\textsf{P}\\)that are computable by bounded-space Turing machines. For this class of computations, we obtain an exponential improvement in the trade-off between the number of rounds and the (exponent of the) communication complexity, compared to known unconditionally sound protocols [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, STOC 2016].", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_1"}, {"primary_key": "558474", "vector": [], "sparse_vector": [], "title": "A Modular Approach to Unclonable Cryptography.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We explore a new pathway to designing unclonable cryptographic primitives. We propose a new notion called unclonable puncturable obfuscation (UPO) and study its implications for unclonable cryptography. Using UPO, we present modular (and in some cases, arguably, simple) constructions of many primitives in unclonable cryptography, including, public-key quantum money, quantum copy-protection for many classes of functionalities, unclonable encryption, and single-decryption encryption. Notably, we obtain the following new results assuming the existence of UPO: We show that any cryptographic functionality can be copy-protected as long as it satisfies a notion of security, which we term puncturable security. Prior feasibility results focused on copy-protecting specific cryptographic functionalities. We show that copy-protection exists for any class of evasive functions as long as the associated distribution satisfies a preimage-sampleability condition. Prior works demonstrated copy-protection for point functions, which follows as a special case of our result. We put forward two constructions of UPO. The first construction satisfies two notions of security based on the existence of (post-quantum) sub-exponentially secure indistinguishability obfuscation, injective one-way functions, the quantum hardness of learning with errors, and the two versions of a new conjecture called the simultaneous inner product conjecture. The security of the second construction is based on the existence of unclonable-indistinguishable bit encryption, injective one-way functions, and quantum-state indistinguishability obfuscation.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_1"}, {"primary_key": "558475", "vector": [], "sparse_vector": [], "title": "Fully-Succinct Multi-key Homomorphic Signatures from Standard Assumptions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Multi-Key Homomorphic Signatures (MKHS) allow one to evaluate a function on data signed by distinct users while producing a succinct and publicly-verifiable certificate of the correctness of the result. All the constructions of MKHS in the state of the art achieve a weak level of succinctness where signatures are succinct in the total number of inputs butgrow linearly with the number of usersinvolved in the computation. The only exception is a SNARK-based construction which relies on a strong notion of knowledge soundness in the presence of signing oracles that not only requires non-falsifiable assumptions but also encounters some impossibility results. In this work, we present the first construction of MKHS that are fully succinct (also with respect to the number of users) while achieving adaptive security under standard falsifiable assumptions. Our result is achieved through a novel combination of batch arguments for NP (BARGs) and functional commitments (FC), and yields diverse MKHS instantiations for circuits of unbounded depth based on either pairing or lattice assumptions. Additionally, our schemes support efficient verification with pre-processing, and they can easily be extended to achieve multi-hop evaluation and context-hiding.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_10"}, {"primary_key": "558476", "vector": [], "sparse_vector": [], "title": "Stochastic Secret Sharing with 1-Bit Shares and Applications to MPC.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The problem of minimizing the share size of threshold secret-sharing schemes is a basic research question that has been extensively studied. Ideally, one strives for schemes in which the share size equals the secret size. While this is achievable for large secrets (<PERSON><PERSON><PERSON>, CACM ’79), no similar solutions are known for the case of binary, single-bit secrets. Current approaches often rely on so-called ramp secret sharing that achieves a constant share size at the expense of a slight gap between the privacy and the correctness thresholds. In the case of single-bit shares, this leads to a large gap which is typically unacceptable. The possibility of a meaningful notion of secret sharing scheme with 1-bit shares and almost optimal threshold has been left wide open. Of special interest is the case of threshold 0.5, which is motivated by information-theoretic honest-majority secure multiparty computation (MPC). In this work, we present a new stochastic model for secret-sharing where each party is corrupted by the adversary with probabilityp, independently of the other parties, and correctness and privacy are required to hold with high probability over the choice of the corrupt parties. We present new secret sharing schemes with single-bit shares that tolerate any constant corruption probability\\(p<0.5\\). Our construction is based on a novel connection between such stochastic secret-sharing schemes and error-correcting codes that achieve capacity over the binary erasure channel. Our schemes are linear and multiplicative. We demonstrate the usefulness of the model by using our new schemes to construct MPC protocols with security against an adversary that passively corrupts an arbitrary subset of 0.499nof the parties, where the online communication per party consists of a single bit per AND gate and zero communication per XOR gate. Unlike competing approaches for communication-efficient MPC, our solution is applicable even in a real-time model in which the parties should compute a Boolean circuit whose gates arrive in real-time, one at a time, and are not known in advance.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_11"}, {"primary_key": "558477", "vector": [], "sparse_vector": [], "title": "STIR: Reed-Solomon Proximity Testing with Fewer Queries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "E<PERSON>"], "summary": "We present STIR (Shift To Improve Rate), an interactive oracle proof of proximity (IOPP) for Reed–Solomon codes that achieves the best known query complexity of any concretely efficient IOPP for this problem. For\\(\\lambda \\)bits of security, STIR has query complexity\\(O(\\log d + \\lambda \\cdot {{\\,\\textrm{loglog}\\,}}d )\\), while FRI, a popular protocol, has query complexity\\(O(\\lambda \\cdot \\log d )\\)(including variants of FRI based on conjectured security assumptions). STIR relies on a new technique for recursively improving the rate of the tested Reed–Solomon code. We provide an implementation of STIR compiled to a SNARK. Compared to a highly-optimized implementation of FRI, STIR achieves an improvement in argument size that ranges from\\(1.25\\times \\)to\\(2.46\\times \\)depending on the chosen parameters, with similar prover and verifier running times. For example, in order to achieve 128 bits of security for degree\\(2^{26}\\)and rate 1/4, STIR has argument size 114 KiB, compared to 211 KiB for FRI.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_12"}, {"primary_key": "558478", "vector": [], "sparse_vector": [], "title": "Advancing Scalability in Decentralized Storage: A Novel Approach to Proof-of-Replication via Polynomial Evaluation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Proof-of-Replication (PoRep) plays a pivotal role in decentralized storage networks, serving as a mechanism to verify that provers consistently store retrievable copies of specific data. While PoRep’s utility is unquestionable, its implementation in large-scale systems, such as Filecoin, has been hindered by scalability challenges. Most existing PoRep schemes, such as <PERSON>sch’s (Eurocrypt 2019), face an escalating number of challenges and growing computational overhead as the number of stored files increases. This paper introduces a novel PoRep scheme distinctively tailored for expansive decentralized storage networks. At its core, our approach hinges on polynomial evaluation, diverging from the probabilistic checking prevalent in prior works. Remarkably, our design requires only a single challenge, irrespective of the number of files, ensuring both prover’s and verifier’s run-times remain manageable even as file counts soar. Our approach introduces a paradigm shift in PoRep designs, offering a blueprint for highly scalable and efficient decentralized storage solutions.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_1"}, {"primary_key": "558479", "vector": [], "sparse_vector": [], "title": "A Modular Approach to Registered ABE for Unbounded Predicates.", "authors": ["Nuttapong Attrapadung", "<PERSON><PERSON>"], "summary": "Registered attribute-based encryption (Reg-ABE), introduced by <PERSON><PERSON><PERSON><PERSON>(Eurocrypt’23), emerges as a pivotal extension of attribute-based encryption (ABE), aimed at mitigating the key-escrow problem. Although several Reg-ABE schemes with black-box use of cryptography have been proposed so far, there remains a significant gap in the class of achievable predicates between vanilla ABE and Reg-ABE. To narrow this gap, we propose a modular framework for constructing Reg-ABE schemes for a broader class of predicates. Our framework is a Reg-ABE analog of the predicate transformation framework for ABE introduced by Attrapadung (Eurocrypt’19) and later refined by Attrapadung and Tomida (Asiacrypt’20) to function under the standard MDDH assumption. As immediate applications, our framework implies the following new Reg-ABE schemes under the standard MDDH assumption: the first Reg-ABE scheme for (non-)monotone span programs with the traditional completely unbounded property. the first Reg-ABE scheme for general non-monotone span programs (also with the completely unbounded property) as defined in the case of vanilla ABE by Attrapadung and Tomida (Asiacrypt’20). Here, the term “completely unbounded” signifies the absence of restrictions on attribute sets for users and policies associated with ciphertexts. From a technical standpoint, we first substantially modify pair encoding schemes (PES), originally devised for vanilla ABE by <PERSON><PERSON>padung (Eurocrypt’14), to make them compatible with Reg-ABE. Subsequently, we present a series of predicate transformations through which we can construct complex predicates, particularly those with an “unbounded” characteristic, starting from simple ones. Finally, we define new properties of PES necessary for constructing Reg-ABE schemes and prove that these properties are preserved through the transformations. This immediately implies that we can obtain Reg-ABE schemes for any predicates derived via predicate transformations.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_9"}, {"primary_key": "558480", "vector": [], "sparse_vector": [], "title": "Time-Memory Trade-Offs Sound the Death Knell for GPRS and GSM.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces a practical TMTO-based attack against GSM (A5/3) and GPRS (GEA-3), which are both technologies used in 2G mobile networks. Although designed in the 80 s, these networks are still quite active today, especially for embedded systems. While active attacks against 2G networks with a fake base station were already known for a while, the attacks introduced in this paper rely on a passive attacker. We explain in this paper how to find material in GPRS and GSM communications to perform a TMTO attack. We performed validation experiments with off-the-shelf devices operated in real-life networks. We provide the success probability of the attack and its performances for several real-life scenarios. We optimized the implementation of KASUMI with AVX2 instructions, and designed a specific TMTO implementation to get around the SSD access latency. As a motivating example, an attacker passively eavesdropping a GSM communication between a target and a base station can decrypt any 2-h call with probability 0.43, in 14 min.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_7"}, {"primary_key": "558481", "vector": [], "sparse_vector": [], "title": "A Formal Treatment of End-to-End Encrypted Cloud Storage.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Users increasingly store their data in the cloud, thereby benefiting from easy access, sharing, and redundancy. To additionally guarantee security of the outsourced data even against a server compromise, some service providers have started to offer end-to-end encrypted (E2EE) cloud storage. With this cryptographic protection, only legitimate owners can read or modify the data. However, recent attacks on the largest E2EE providers have highlighted the lack of solid foundations for this emerging type of service. In this paper, we address this shortcoming by initiating the formal study of E2EE cloud storage. We give a formal syntax to capture the core functionality of a cloud storage system, capturing the real-world complexity of such a system’s constituent interactive protocols. We then define game-based security notions for confidentiality and integrity of a cloud storage system against a fully malicious server. We treat both selective and fully adaptive client compromises. Our notions are informed by recent attacks on E2EE cloud storage providers. In particular we show that our syntax is rich enough to capture the core functionality of MEGA and that recent attacks on it arise as violations of our security notions. Finally, we present an E2EE cloud storage system that provides all core functionalities and that is both efficient and provably secure with respect to our selective security notions. Along the way, we discuss challenges on the path towards bringing the security of cloud storage up to par with other end-to-end primitives, such as secure messaging and TLS.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_2"}, {"primary_key": "558482", "vector": [], "sparse_vector": [], "title": "Plaintext-Ciphertext Matrix Multiplication and FHE Bootstrapping: Fast and Fused.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Homomorphically multiplying a plaintext matrix with a ciphertext matrix (PC-MM) is a central task for the private evaluation of transformers, commonly used for large language models. We provide several RLWE-based algorithms for PC-MM that consist of multiplications of plaintext matrices (PP-MM) and comparatively cheap pre-processing and post-processing steps: for small and large dimensions compared to the RLWE ring degree, and with and without precomputation. For the algorithms with precomputation, we show how to perform a PC-MM with a single floating-point PP-MM of the same dimensions. This is particularly meaningful for practical purposes as a floating-point PP-MM can be implemented using high-performance BLAS libraries. The algorithms rely on the multi-secret variant of RLWE, which allows to represent multiple ciphertexts more compactly. We give algorithms to convert from usual shared-secret RLWE ciphertexts to multi-secret ciphertexts and back. Further, we show that this format is compatible with homomorphic addition, plaintext-ciphertext multiplication, and key-switching. This in turn allows us to accelerate the slots-to-coeffs and coeffs-to-slots steps of CKKS bootstrapping when several ciphertexts are bootstrapped at once. Combining batch-bootstrapping with efficient PC-MM results in MaMBo(Matrix Multiplication Bootstrapping), a bootstrapping algorithm that can perform a PC-MM for a limited overhead.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_12"}, {"primary_key": "558483", "vector": [], "sparse_vector": [], "title": "Towards Permissionless Consensus in the Standard Model via Fine-Grained Complexity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the feasibility ofpermissionlessconsensus (aka Byzantine agreement) under standard assumptions. A number of protocols have been proposed to achieve permissionless consensus, most notably based on the Bitcoin protocol; however, to date no protocol is known that can be provably instantiated outside of the random oracle model. In this work, we take the first steps towards achieving permissionless consensus in the standard model. In particular, we demonstrate that worst-case conjectures in fine-grained complexity, in particular the orthogonal vectors conjecture (implied by the Strong Exponential Time Hypothesis), imply permissionless consensus in the random beacon model—a setting where a fresh random value is delivered to all parties at regular intervals. This gives a remarkable win-win result:either permissionless consensus exists relative to a random beacon, or there are non-trivial worst-case algorithmic speed-ups for a host of natural algorithmic problems(including\\(\\textsf{SAT}\\)). Our protocol achieves resilience against adversaries that control an inverse-polynomial fraction of the honest computational power, i.e., adversarial power\\(A=T^{1-\\epsilon }\\)for some constant\\(\\epsilon >0\\), whereTdenotes the honest computational power. This relatively low threshold is a byproduct of the slack in the fine-grained complexity conjectures. One technical highlight is the construction of aSeeded Proof of Work: a Proof of Work where many (correlated) challenges can be derived from a single shortpublicseed, and yet still no non-trivial amortization is possible.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_4"}, {"primary_key": "558484", "vector": [], "sparse_vector": [], "title": "Polytopes in the Fiat-Shamir with Aborts Paradigm.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Fiat-Shamir with Aborts paradigm (FSwA) uses rejection sampling to remove a secret’s dependency on a given source distribution. Recent results revealed that unlike the uniform distribution in the hypercube, both the continuous Gaussian and the uniform distribution within the hyperball minimise the rejection rate and the size of the proof of knowledge. However, in practice both these distributions suffer from the complexity of their sampler. So far, those three distributions are the only available alternatives, but none of them offer the best of all worlds: competitive proof of knowledge size and rejection rate with a simple sampler. We introduce a new generic framework for FSwA using polytope based rejection sampling to enable a wider variety of constructions. As a matter of fact, this framework is the first to generalise these results to integral distributions. To complement the lack of alternatives, we also propose a new polytope construction, whose uniform sampler approaches in simplicity that of the hypercube. At the same time, it provides competitive proof of knowledge size compared to that obtained from the Gaussian distribution. Concurrently, we share some experimental improvements of our construction to further reduce the proof size. Finally, we propose a signature based on the FSwA paradigm using both our framework and construction. We prove it to be competitive with Haetae in signature size and with Dilithium on sampler simplicity.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_11"}, {"primary_key": "558485", "vector": [], "sparse_vector": [], "title": "Bare PAKE: Universally Composable Key Exchange from Just Passwords.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the past three decades, an impressive body of knowledge has been built around secure and private password authentication. In particular, secure password-authenticated key exchange (PAKE) protocols require only minimal overhead over a classical Diffie-Hellman key exchange. PAKEs are also known to fulfill strong composable security guarantees that capture many password-specific concerns such as password correlations or password mistyping, to name only a few. However, to enjoy both round-optimality and strong security, applications of PAKE protocols must provideuniquesession and participant identifiers. If such identifiers are not readily available, they must be agreed upon at the cost of additional communication flows, a fact which has been met with incomprehension among practitioners, and which hindered the adoption of provably secure password authentication in practice. In this work, we resolve this issue by proposing a new paradigm for trulypassword-onlyyet securely composable PAKE, calledbarePAKE. We formally prove that two prominent PAKE protocols, namely CPace and EKE, can be cast as bare PAKEs and hence do not require pre-agreement of anything else than a password. Our bare PAKE modeling further allows to investigate a novel “reusability” property of PAKEs, i.e., whether\\(n^2\\)pairwise keys can be exchanged from onlynmessages, just as the <PERSON><PERSON><PERSON>-<PERSON><PERSON> non-interactive key exchange can do in a public-key setting. As a side contribution, this add-on property of bare PAKEs leads us to observe that some previous PAKE constructions relied on unnecessarily strong, “reusable” building blocks. By showing that “non-reusable” tools suffice for standard PAKE, we open a new path towards round-optimal post-quantum secure password-authenticated key exchange.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_6"}, {"primary_key": "558486", "vector": [], "sparse_vector": [], "title": "The Algebraic FreeLunch: Efficient G<PERSON><PERSON>bner Basis Attacks Against Arithmetization-Oriented Primitives.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Morten Øygarden", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present a new type of algebraic attack that applies to many recent arithmetization-oriented families of permutations, such as those used inGriffin,Anemoi,ArionHash, andXHash8, whose security relies on the hardness of the constrained-input constrained-output (CICO) problem. We refer to the attack as the FreeLunch approach: the monomial ordering is chosen so that the natural polynomial system encoding the CICO problem alreadyisa <PERSON> basis. In addition, we present a new dedicated resolution algorithm for FreeLunch systems of complexity lower than current state-of-the-art resolution algorithms. We show that the FreeLunch approach challenges the security of full-round instances ofAnemoi,ArionandGriffin, and we experimentally confirm these theoretical results. In particular, combining the FreeLunch attack with a new technique to bypass 3 rounds ofGriffin, we recover a CICO solution for 7 out of 10 rounds ofGriffinin less than four hours on one core of AMD EPYC 7352 (2.3 GHz).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_5"}, {"primary_key": "558487", "vector": [], "sparse_vector": [], "title": "Secret Sharing with Certified Deletion.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Secret sharing allows a user to split a secret into many shares so that the secret can be recovered if, and only if, an authorized set of shares is collected. Although secret sharing typically does not require any computational hardness assumptions, its securitydoesrequire that an adversary cannot collect an authorized set of shares. Over long periods of time where an adversary can benefit from multiple data breaches, this may become an unrealistic assumption. We initiate the systematic study of secret sharingwith certified deletionin order to achieve securityeven against an adversary that eventually collects an authorized set of shares. In secret sharing with certified deletion, a (classical) secretsis split into quantum shares that can be destroyed in a manner verifiable by the dealer. We put forth two natural definitions of security.No-signaling securityroughly requires that if multiple non-communicating adversaries delete sufficiently many shares, then their combined view contains negligible information abouts, even if the total set of corrupted parties forms an authorized set.Adaptive securityrequires privacy ofsagainst an adversary that can continuously and adaptively corrupt new shares and delete previously-corrupted shares, as long as the total set of corrupted shares minus deleted shares remains unauthorized. Next, we show that these security definitions are achievable: we show how to construct (i) a secret sharing scheme with no-signaling certified deletion forany monotone access structure, and (ii) athresholdsecret sharing scheme with adaptive certified deletion. Our first construction uses <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>’s (CRYPTO 2023) 2-out-of-2 secret sharing scheme with certified deletion as a building block, while our second construction is built from scratch and requires several new technical ideas. For example, we significantly generalize the “XOR extractor” of Agarwal, Bartusek, Khurana, and <PERSON> (EUROCRYPT 2023) in order to obtain better seedless extraction from certain quantum sources of entropy, and show how polynomial interpolation can double as a high-rate randomness extractor in our context of threshold sharing with certified deletion.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_7"}, {"primary_key": "558488", "vector": [], "sparse_vector": [], "title": "Fine-Grained Non-interactive Key Exchange, Revisited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We revisit the construction of multiparty non-interactive key-exchange protocols with fine-grained security, which was recently studied in (<PERSON><PERSON><PERSON> et al., Eurocrypt 2023). Their work introduced a 4-party non-interactive key exchange with quadratic hardness, and proved it secure in <PERSON><PERSON><PERSON>’s generic group model. This positive result was complemented with a proof thatn-party non-interactive key exchange with superquadratic security cannot exist in <PERSON><PERSON>’s generic group model, for any\\(n\\ge 3\\). Because <PERSON><PERSON><PERSON>’s model is stronger than <PERSON><PERSON>’s model, this leaves a gap between the positive and the negative result, and their work left as an open question the goal of closing this gap, and of obtaining fine-grained non-interactive key exchange without relying on idealized models. In this work, we make significant progress on both questions. We obtain two main results: A 4-party non-interactive key exchange protocol with quadratic security gap, assuming the existence of exponentially secure injective pseudorandom generators, and the subexponential hardness of the computational Diffie-Hellman assumption. In addition, our scheme is conceptually simpler, and can be generalized to other settings (with more parties or from other assumptions). Assuming the existence of non-uniformly secure injective pseudorandom generators with exponential hardness, we further show that our protocol is secure in <PERSON><PERSON>’s model, albeit with a smaller hardness gap (up to\\(N^{1.6}\\)), making progress on filling the gap between the positive and the negative result of (<PERSON><PERSON><PERSON> et al., Eurocrypt 2023). Somewhat intriguingly, proving the security of our scheme in Maurer’s idealized model turns out to be significantly harder than proving its security in the standard model.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_9"}, {"primary_key": "558489", "vector": [], "sparse_vector": [], "title": "Cheater Identification on a Budget: MPC with Identifiable Abort from Pairwise MACs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Cheater identification in secure multi-party computation (MPC) allows the honest parties to agree upon the identity of a cheating party, in case the protocol aborts. In the context of a dishonest majority, this becomes especially critical, as it serves to thwart denial-of-service attacks and mitigate known impossibility results on ensuring fairness and guaranteed output delivery. In this work, we present a new, lightweight approach to achieving identifiable abort in dishonest majority MPC. We avoid all of the heavy machinery used in previous works, instead relying on a careful combination of lightweight detection mechanisms and techniques from state-of-the-art protocols secure with (non-identifiable) abort. At the core of our construction is a homomorphic, multi-receiver commitment scheme secure with identifiable abort. This commitment scheme can be constructed from cheap vector oblivious linear evaluation protocols based on learning parity with noise. To support cheater identification, we design a general compilation technique, similar to a compiler of <PERSON><PERSON> et al. (Crypto 2014), but avoid its requirement for adaptive security of the underlying protocol. Instead, we rely on a different (and seemingly easier to achieve) property we call online extractability, which may be of independent interest. Our MPC protocol can be viewed as a version of the BDOZ MPC scheme (<PERSON><PERSON> et al., Eurocrypt 2011) based on pairwise information-theoretic MACs, enhanced to support cheater identification and a highly efficient preprocessing phase, essentially as efficient as the non-identifiable protocol of Le Mans (Rachuri & Scholl, Crypto 2022).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_14"}, {"primary_key": "558490", "vector": [], "sparse_vector": [], "title": "Formal Security Proofs via Doeblin Coefficients: - Optimal Side-Channel Factorization from Noisy Leakage to Random Probing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Masking is one of the most popular countermeasures to side-channel attacks, because it can offer provable security. However, depending on the adversary’s model, useful security guarantees can be hard to provide. At first, masking has been shown secure againstt-threshold probing adversariesby Ishaiet al.atCrypto’03. It has then been shown secure in the more genericrandom probing modelby Ducet al.atEurocrypt’14. <PERSON><PERSON> and <PERSON><PERSON><PERSON> have introduced thenoisy leakage modelto capture more realistic leakage atEurocrypt’13. Reduction from noisy leakage to random probing has been introduced by <PERSON><PERSON><PERSON> al.atEurocrypt’14, and security guarantees were improved for both models by Prestet al.atCrypto’19, Ducet al.inEurocrypt’15/J. Cryptol’19, and Masure and Standaert atCrypto’23. Unfortunately, as it turns out, we found that previous proofs in either random probing or noisy leakage models are flawed, and such flaws do not appear easy to fix. In this work, we show that theDoeblin coefficientallows one to overcome these flaws. In fact, it yields optimal reductions from noisy leakage to random probing, thereby providing a correct and usable metric to properly ground security proofs. This shows the inherent inevitable cost of a reduction from the noisy leakages to the random probing model. We show that it can also be used to derivedirectformal security proofs using the subsequence decomposition of <PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_12"}, {"primary_key": "558491", "vector": [], "sparse_vector": [], "title": "Structural Lower Bounds on Black-Box Constructions of Pseudorandom Functions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We address the black-box complexity of constructing pseudorandom functions (PRF) from pseudorandom generators (PRG). The celebrated GGM construction of <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> 1984) provides such a construction, which (even when combined with <PERSON>’s domain-extension trick) has super-logarithmic depth. Despite many years and much effort, this remains essentially the best construction we have to date. On the negative side, one step is provided by the work of <PERSON> and <PERSON> (TCC 2011), which shows that a black-box construction which just calls the PRG once and outputs one of its output bits, cannot be a PRF. In this work, we make significant further progress: we rule out black-box constructions of PRF from PRG that follow certain structural constraints, but may call the PRG adaptively polynomially many times. In particular, we define “tree constructions” which generalize the GGM structure: they apply the PRGGalong a tree path, but allow for different choices of functions to compute the children of a node on the tree and to compute the next node on the computation path down the tree. We prove that a tree construction of logarithmic depth cannot be a PRF (while GGM is a tree construction of super-logarithmic depth). We also show several other results and discuss the special case of one-call constructions. Our main results in fact rule out evenweakPRF constructions withoneoutput bit. We use the oracle separation methodology introduced by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (F<PERSON><PERSON> 2001), and show that for any candidate black-box construction\\(F^G\\)fromG, there exists an oracle relative to whichGis a PRG, but\\(F^G\\)is not a PRF.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_16"}, {"primary_key": "558492", "vector": [], "sparse_vector": [], "title": "Certifying Private Probabilistic Mechanisms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In past years, entire research communities have arisen to address concerns of privacy and fairness in data analysis. At present, however, the public must trust that institutions will re-implement algorithms voluntarily to account for these social concerns. Due to additional cost, widespread adoption is unlikely without effective legal enforcement. A technical challenge for enforcement is that the methods proposed are oftenprobabilistic mechanisms, whose output must be drawn according to precise, and sometimes secret, distributions. The Differential Privacy (DP) case is illustrative: if a cheating curator answers queries according to an overly-accurate mechanism, privacy violations could go undetected. This raises our central question:Can we efficiently certify the output of a probabilistic mechanism enacted by an untrusted party?To this end: We introduce two new notions:Certified Probabilistic Mechanisms(CPM) andRandom Variable Commitment Schemes(RVCS). A CPM is an interactive protocol that forces a prover to enact a given probabilistic mechanism or be caught; importantly, the interaction does not reveal the mechanism’s secret parameters. An RVCS—a key primitive for constructing CPMs—is a commitment scheme where the verifier is convinced that the commitment is to an RV sampled according to an agreed-upon distribution, but learns nothing else. We instantiate the general notion of CPM for the special case of Certifying DP. We build a lightweight, doubly-efficent interactive proof system to certify arbitrary-predicate counting queries released via the DP Binomial mechanism. The construction relies on a commitment scheme with perfect hiding and additive homomorphic properties that can be used to release a broad class of queries about a committed database, constructed on top of Pedersen commitments. Finally, we demonstrate the immediate feasibility of Certified DP via a highly-efficient and scalable prototype implementation to answer counting queries of arbitrary predicates. The mechanism is composed of an offline and online stage, where the online phase allows for non-interactive certification of queries. For example, we show that CDP queries over a US Census Public Use Microdata Sample (PUMS) [24] (\\(n=7000\\)) can be completed in only 1.6 ms and verified in just38\\(\\mu \\)s. Our implementation is available in open source athttps://github.com/jlwatson/certified-dp.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_11"}, {"primary_key": "558493", "vector": [], "sparse_vector": [], "title": "Succinctly-Committing Authenticated Encryption.", "authors": ["<PERSON><PERSON>", "Viet Tung Hoang"], "summary": "Recent attacks and applications have led to the need for symmetric encryption schemes that, in addition to providing the usual authenticity and privacy, are also committing. In response, many committing authenticated encryption schemes have been proposed. However, all known schemes, in order to providesbits of committing security, suffer an expansion—this is the length of the ciphertext minus the length of the plaintext—of 2sbits. This incurs a cost in bandwidth or storage. (We typically want\\(s=128\\), leading to 256-bit expansion.) However, it has been considered unavoidable due to birthday attacks. We show how to bypass this limitation. We give authenticated encryption (AE) schemes that providesbits of committing security, yet suffer expansion only aroundsas long as messages are long enough, namely more thansbits. We call such schemes succinct. We do this via a generic, ciphertext-shortening transform called\\(\\textsf{SC}\\): given an AE scheme with 2s-bit expansion,\\(\\textsf{SC}\\)returns an AE scheme withs-bit expansion while preserving committing security.\\(\\textsf{SC}\\)is very efficient; an AES-based instantiation has overhead just two AES calls. As a tool,\\(\\textsf{SC}\\)uses a collision-resistant invertible PRF called\\(\\textsf{HtM}\\), that we design, and whose analysis is technically difficult. To add the committing security that\\(\\textsf{SC}\\)assumes to a base scheme, we also give a transform\\(\\textsf{CTY}\\)that improves <PERSON> and <PERSON><PERSON><PERSON>’s\\(\\textsf{CTX}\\). Our results hold in a general framework for authenticated encryption that includes both classical AEAD and AE2 (also called nonce-hiding AE) as special cases, so that we in particular obtain succinctly-committing AE schemes for both these settings.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_10"}, {"primary_key": "558494", "vector": [], "sparse_vector": [], "title": "Improved Algorithms for Finding Fixed-Degree Isogenies Between Supersingular Elliptic Curves.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Finding isogenies between supersingular elliptic curves is a natural algorithmic problem which is known to be equivalent to computing the curves’ endomorphism rings. When the isogeny is additionally required to have a specific known degreed, the problem appears to be somewhat different in nature, yet its hardness is also required for isogeny-based cryptography. Let\\(E_1,E_2\\)be supersingular elliptic curves over\\(\\mathbb {F}_{p^2}\\). We present improved classical and quantum algorithms that compute an isogeny of degreedbetween\\(E_1\\)and\\(E_2\\)if it exists. Let\\(d \\approx p^{1/2+ \\epsilon }\\)for some\\(\\epsilon >0\\). Our essentially memory-free algorithms have better time complexity than meet-in-the-middle algorithms, which require exponential memory storage, in the range\\(1/2\\le \\epsilon \\le 3/4\\)on a classical computer. For quantum computers, we improve the time complexity in the range\\(0<\\epsilon <5/2\\). Our strategy is to compute the endomorphism rings of both curves, compute the reduced norm form associated to\\(\\text {Hom}(E_1,E_2)\\)and try to represent the integerdas a solution of this form. We present multiple approaches to solving this problem which combine guessing certain variables exhaustively (or use <PERSON><PERSON><PERSON>s search in the quantum case) with methods for solving quadratic Diophantine equations such as <PERSON><PERSON><PERSON><PERSON>s algorithm and multivariate variants of <PERSON><PERSON>’s method. We provide implementations and experimental results for the different approaches. A solution to the norm form can then be efficiently translated to recover the sought-after isogeny using well-known techniques. As a consequence of our results we show that a recently introduced signature scheme from [3] does not reach NIST level I security.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_8"}, {"primary_key": "558495", "vector": [], "sparse_vector": [], "title": "CryptAttackTester: high-assurance attack analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Quantitative analyses of the costs of cryptographic attack algorithms play a central role in comparing cryptosystems, guiding the search for improved attacks, and deciding which cryptosystems to standardize. Unfortunately, these analyses often turn out to be wrong. Sometimes errors are not caught until years later. This paper introduces CryptAttackTester (CAT), a software framework for high-assurance quantification of attack effectiveness. CAT enforces complete definitions of attack algorithms all the way down through the model of computation, enforces complete definitions of probability predictions and cost predictions all the way down through the cost metric, and systematically tests the predictions on small-scale inputs. For example, CAT gives a fully defined meaning to the statement “the median cost of brute-force search for an AES-128 key is under\\(2^{141.89}\\)bit operations”, and provides clear, auditable reasons to believe that the statement is correct. This does not rule out all possible analysis errors, but with CAT it is no longer possible for bugs to hide inside ambiguous or untested security-level claims. The paper gives various examples of errors in the literature that survived typical informal testing practices and that would have been caught if CAT-enforced links had been in place. As an important case study, the bulk of the current CAT release consists of full definitions of a broad spectrum of algorithms for information-set decoding (ISD), along with cost/probability predictions for each algorithm. ISD is the top attack strategy against the McEliece cryptosystem. The predictions cover interactions between (1) high-level search strategies from <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>–<PERSON>, and <PERSON>–May–Meurer; (2) random walks from Omura, Canteaut–Chabaud, Canteaut–Sendrier, and <PERSON>–<PERSON>–<PERSON>; and (3) speedups in core subroutines such as linear algebra and sorting. The predictions also account for various attack overheads that were omitted from previous analyses. These gaps add up to roughly 10 bits, depending on parameters. CAT’s tests catch much smaller errors than this. The cost metric selected in CAT has a very simple definition, is a lower bound for the price-performance ratio of non-quantum special-purpose hardware (although the bound is loose for attacks bottlenecked by long-distance communication), and allows many optimization efforts to be shared with the design of cryptographic circuits.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_5"}, {"primary_key": "558496", "vector": [], "sparse_vector": [], "title": "Information-Theoretic Security with Asymmetries.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we study the problem of lower bounding any given cost function depending on the false positive and false negative probabilities of adversaries against indistinguishability security notions in symmetric-key cryptography. We take the cost model as an input, so that this becomes a purely information-theoretical question. We propose power bounds as an easy-to-use alternative for advantage bounds in the context of indistinguishability with asymmetric cost functions. We show that standard proof techniques such as hybrid arguments and the H-coefficient method can be generalized to the power model, and apply these techniques to the PRP-PRF switching lemma, the Even-Mansour (\\(\\textsf{EM}\\)) construction, and the sum-of-permutations (\\(\\textsf{SoP}\\)) construction. As the final and perhaps most useful contribution, we provide two methods to convert single-user power bounds into multi-user power bounds, and investigate their relation to the point-wise proximity method of <PERSON><PERSON> and <PERSON><PERSON> (Crypto 2016). These method are applied to obtain tight multi-user power bounds for\\(\\textsf{EM}\\)and\\(\\textsf{SoP}\\).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_15"}, {"primary_key": "558497", "vector": [], "sparse_vector": [], "title": "The Committing Security of MACs with Applications to Generic Composition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Message Authentication Codes (MACs) are ubiquitous primitives deployed in multiple flavors through standards such as HMAC, CMAC, GMAC, LightMAC, and many others Its versatility makes it an essential building block in applications necessitating message authentication and integrity checks, in authentication protocols, authenticated encryption schemes, or as a pseudorandom or key derivation function. Its usage in this variety of settings makes it susceptible to a broad range of attack scenarios. The latest attack trends leverage a lack of commitment or context-discovery security in AEAD schemes and these attacks are mainly due to the weakness in the underlying MAC part. However, these new attack models have been scarcely analyzed for MACs themselves. This paper provides a thorough treatment of MACs committing and context-discovery security. We reveal that commitment and context-discovery security of MACs have their own interest by highlighting real-world vulnerable scenarios. We formalize the required security notions for MACs, and analyze the security of standardized MACs for these notions. Additionally, as a constructive application, we analyze generic AEAD composition and provide simple and efficient ways to build committing and context-discovery secure AEADs.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_14"}, {"primary_key": "558498", "vector": [], "sparse_vector": [], "title": "Quantum Lattice Enumeration in Limited Depth.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In 2018, <PERSON><PERSON>et al.(ASIACRYPT 2018) proposed to use quantum backtracking algorithms (Montanaro, TOC 2018; <PERSON><PERSON><PERSON><PERSON> and <PERSON>, STOC 2017) to speedup lattice point enumeration. Quantum lattice sieving algorithms had already been proposed (<PERSON><PERSON><PERSON><PERSON><PERSON> al., PQCRYPTO 2013), being shown to provide an asymptotic speedup over classical counterparts, but also to lose competitiveness at dimensions relevant to cryptography if practical considerations on quantum computer architecture were taken into account (Albrechtet al., ASIACRYPT 2020). A<PERSON>et al.’s work argued that quantum walk speedups can be applied to lattice enumeration, achieving at least a quadratic asymptotic speedupà laGrover search while not requiring exponential amounts of quantum accessible classical memory, as it is the case for sieving. In this work, we explore how to lower bound the cost of using <PERSON><PERSON>et al.’s techniques on lattice enumeration with extreme cylinder pruning, assuming a limit to the maximum depth that a quantum computation can achieve without decohering, with the objective of better understanding the practical applicability of quantum backtracking in lattice cryptanalysis.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_3"}, {"primary_key": "558499", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of Algebraic Verifiable Delay Functions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>Plase<PERSON>", "<PERSON>"], "summary": "Verifiable Delay Functions (VDF) are a class of cryptographic primitives aiming to guarantee a minimum computation time, even for an adversary with massive parallel computational power. They are useful in blockchain protocols, and several practical candidates have been proposed based on exponentiation in a large finite field: Sloth++, <PERSON>eedo, MinRoot. The underlying assumption of these constructions is that computing an exponentiation\\(x^e\\)requires at least\\(\\log _2 e\\)sequential multiplications. In this work, we analyze the security of these algebraic VDF candidates. In particular, we show that the latency of exponentiation can be reduced using parallel computation, against the preliminary assumptions.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_14"}, {"primary_key": "558500", "vector": [], "sparse_vector": [], "title": "Robust Additive Randomized Encodings from IO and Pseudo-Non-linear Codes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Additive randomized encodings(ARE), introduced by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> (CRYPTO 2023), reduce the computation of ak-party function\\(f(x_1,\\dots ,x_k)\\)to locally computing encodings\\(\\hat{x}_i\\)of each input\\(x_i\\)and then adding them together over some Abelian group into an output encoding\\(\\hat{y} = \\sum \\hat{x}_i\\), which reveals nothing but the result. InrobustARE (RARE) the sum of any subset of\\(\\hat{x}_i\\), reveals only the residual function obtained by restricting the corresponding inputs. The appeal of (R)ARE comes from the simplicity of the interactive part of the computation, involving only addition, which yields for instance non-interactive multi-party computation in theshuffle modelwhere messages from different parties are anonymously shuffled. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> constructed ARE from standard assumptions and RARE in the ideal obfuscation model, leaving open the question of whether RARE can be constructed in the plain model. We construct RARE in the plain model from indistinguishability obfuscation, which is necessary, and a new primitive that we callpseudo-non-linear codes. We provide two constructions of this primitive assuming either Learning with <PERSON><PERSON><PERSON> or <PERSON> <PERSON><PERSON><PERSON>. A bonus feature of our construction is that it issuccinct. Specifically, encodings\\(\\hat{x}_i\\)can be decomposed to non-interactive parts\\(\\hat{z}_i\\), generated in time proportional to the input size, and sent directly to the evaluator, and group parts\\(\\hat{g}_i\\)that are added together, and whose size depends only on the security parameter.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_4"}, {"primary_key": "558501", "vector": [], "sparse_vector": [], "title": "Amplification of Non-interactive Zero Knowledge, Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In an\\((\\varepsilon _s,\\varepsilon _z)\\)-weak non-interactive zero knowledge (NIZK), the soundness error is at most\\(\\varepsilon _s\\)and the zero-knowledge error is at most\\(\\varepsilon _z\\). <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> (CRYPTO 2019) stated that if\\(\\varepsilon _s+\\varepsilon _z < 1\\)for some constants\\(\\varepsilon _s,\\varepsilon _z\\), then\\((\\varepsilon _s,\\varepsilon _z)\\)-weak NIZK can be turned into fully-secure NIZK, assumingsub-exponentially-secure public-key encryption. Later, however, they have discovered a gap in their proof. We revisit the problem of NIZK amplification: We amplify NIZK arguments assuming onlypolynomially-securepublic-key encryption, for any constants\\(\\varepsilon _s+\\varepsilon _z < 1\\). We amplify NIZK proofs assuming onlyone-way functions, for any constants\\(\\varepsilon _s+\\varepsilon _z < 1\\). When the soundness error\\(\\varepsilon _s\\)is negligible to begin with, we can also amplify NIZK arguments assuming only one-way functions. Our results take a different route than that of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. They are based on thehidden-bits paradigm, and can be viewed as a reduction from NIZK amplification to the better understood problem of pseudorandomness amplification.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_11"}, {"primary_key": "558502", "vector": [], "sparse_vector": [], "title": "Reusable Online-Efficient Commitments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Anonline-efficient commitmentis a succinct locally-openable commitment, where the bulk of the sender work is done offline, generating an encoding\\(\\tilde{x}\\)of the committed datax. In the online phase, both the sender, given random access to\\(\\tilde{x}\\), and receiver run in polylogarithmic time in the length ofx. Online-efficient commitments were recently constructed under the standard assumption of RingLWE by <PERSON>, Mook, and W<PERSON>s, but with a significant caveat:they are not reusable.Their commitments are privately verifiable and cease to be binding if a malicious sender can learn whether the receiver accepts or rejects in repeated decommitment requests. We construct the firstreusableonline-efficient commitment under a standard assumption, Ring LWE. A main component in our analysis is a leakage lemma by <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> (CRYPTO ‘11) introduced in the context ofstreaming delegation schemes.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_1"}, {"primary_key": "558503", "vector": [], "sparse_vector": [], "title": "Field-Agnostic SNARKs from Expand-Accumulate Codes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Efficient realizations of succinct non-interactive arguments of knowledge (SNARK) have gained popularity due to their practical applications in various domains. Among existing schemes, those based on error-correcting codes are of particular interest because of their good concrete efficiency, transparent setup, and plausible post-quantum security. However, many existing code-based SNARKs suffer from the disadvantage that they only work over specific finite fields. In this work, we construct a code-based SNARK that does not rely on any specific underlying field; i.e., it isfield-agnostic. Our construction follows the framework of Brakedown and builds a polynomial commitment scheme (and hence a SNARK) based on recently introducedexpand-accumulate codes. Our work generalizes these codes to arbitrary finite fields; our main technical contribution is showing that, with high probability, these codes have constant rate and constant relative distance (crucial properties for building efficient SNARKs), solving an open problem from prior work. As a result of our work we obtain a SNARK where, for a statement of sizeM, the prover time is\\(O(M\\log M)\\)and the proof size is\\(O(\\sqrt{M})\\). We demonstrate the concrete efficiency of our scheme empirically via experiments. Proving ECDSA verification on the secp256k1 curve requires only 0.23 s for proof generation, 2 orders of magnitude faster than SNARKs that are not field-agnostic. Compared to the original Brakedown result (which is also field-agnostic), we obtain proofs that are 1.9–2.8\\(\\times \\)smaller due to the good concrete distance of our underlying error-correcting code, while introducing only a small overhead of 1.2\\(\\times \\)in the prover time.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_9"}, {"primary_key": "558504", "vector": [], "sparse_vector": [], "title": "Traceable Secret Sharing: Strong Security and Efficient Constructions.", "authors": ["<PERSON>", "Aditi Partap", "<PERSON><PERSON>"], "summary": "Suppose <PERSON> uses at-out-of-nsecret sharing to store her secret key onnservers. Her secret key is protected as long astof them do not collude. However, what if a less-than-tsubset of the servers decides to offer the shares they have for sale? In this case, <PERSON> should be able to hold them accountable, or else nothing prevents them from selling her shares. With this motivation in mind, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (CRYPTO 21) introduced the concept oftraceable secret sharing. In such schemes, it is possible to provably trace the leaked secret shares back to the servers who leaked them. <PERSON><PERSON> et al. presented the first construction of a traceable secret sharing scheme. However, secret shares in their construction are quadratic in the secret size, and their tracing algorithm is quite involved as it relies on Goldreich-Levin decoding. In this work, we put forth new definitions and practical constructions for traceable secret sharing. In our model, some\\(f < t\\)servers output a reconstruction boxRthat may arbitrarily depend on their shares. Given additional\\(t-f\\)shares,Rreconstructs and outputs the secret. The task is to traceRback to the corrupted servers given black-box access toR. Unlike <PERSON><PERSON> et al., we do not assume that the tracing algorithm has any information on how the corrupted servers constructedRfrom the shares in their possession. We then present two very efficient constructions of traceable secret sharing based on two classic secret sharing schemes. In both of our schemes, shares are only twice as large as the secret, improving over the quadratic overhead of <PERSON><PERSON> et al. Our first scheme is obtained by presenting a new practical tracing algorithm for the widely-used Shamir secret sharing scheme. Our second construction is based on an extension of Blakley’s secret sharing scheme. Tracing in this scheme is optimally efficient, and requires just one successful query toR. We believe that our constructions are an important step towards bringing traceable secret-sharing schemes to practice. This work also raises several interesting open problems that we describe in the paper.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_9"}, {"primary_key": "558505", "vector": [], "sparse_vector": [], "title": "Accountability for Misbehavior in Threshold Decryption via Threshold Traitor Tracing.", "authors": ["<PERSON>", "Aditi Partap", "<PERSON><PERSON>"], "summary": "At-out-of-nthreshold decryption system assigns key shares tonparties so that anytof them can decrypt a well-formed ciphertext. Existing threshold decryption systems arenot securewhen these parties are rational actors: an adversary can offer to pay the parties for their key shares. The problem is that a quorum oftparties, working together, can sell the adversary a decryption key that reveals nothing about the identity of the traitor parties. This provides a risk-free profit for the parties since there is no accountability for their misbehavior—the information they sell to the adversary reveals nothing about their identity. This behavior can result in a complete break in many applications of threshold decryption, such as encrypted mempools, private voting, and sealed-bid auctions. In this work we propose a solution to this problem. Suppose a quorum oftor more parties construct a decoder algorithm\\(D(\\cdot )\\)that takes as input a ciphertext and outputs the corresponding plaintext or\\(\\bot \\). They sellDto the adversary. Our threshold decryption systems are equipped with a tracing algorithm that can traceDto members of the quorum that created it. The tracing algorithm is only given blackbox access toDand will identify some members of the misbehaving quorum. The parties can then be held accountable, which may discourage them from selling the decoderDin the first place. Our starting point is standard (non-threshold) traitor tracing, wherenparties each holds a secret key. Every party can decrypt a well-formed ciphertext on its own. However, if a subset of parties\\(\\mathcal{J} \\subseteq [n]\\)collude to create a pirate decoder\\(D(\\cdot )\\)that can decrypt well-formed ciphertexts, then it is possible to traceDto at least one member of\\(\\mathcal{J}\\)using only blackbox access to the decoderD. In this work we develop the theory of traitor tracing for threshold decryption, where now only a subset\\(\\mathcal{J} \\subseteq [n]\\)oftor more parties can collude to create a pirate decoder\\(D(\\cdot )\\). This problem has recently become quite important due to the real-world deployment of threshold decryption in encrypted mempools, as we explain in the paper. While there are several non-threshold traitor tracing schemes that we can leverage, adapting these constructions to the threshold decryption settings requires new cryptographic techniques. We present a number of constructions for traitor tracing for threshold decryption, and note that much work remains to explore the large design space.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_11"}, {"primary_key": "558506", "vector": [], "sparse_vector": [], "title": "Improving Generic Attacks Using Exceptional Functions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Over the past ten years, there have been many attacks on symmetric constructions using the statistical properties of random functions. Initially, these attacks targeted iterated hash constructions and their combiners, developing a wide array of methods based on internal collisions and on the average behavior of iterated random functions. More recently, <PERSON> et al. (EUROCRYPT 2023) introduced a forgery attack on so-calledduplex-basedAuthenticated Encryption modes which was based onexceptionalrandom functions, i.e., functions whose graph admits a large component with an exceptionally small cycle. In this paper, we expand the use of such functions in generic cryptanalysis with several new attacks. First, we improve the attack of <PERSON> et al. from\\(\\mathcal {O}(2^{3c/4})\\)to\\(\\mathcal {O}(2^{2c/3})\\), wherecis the capacity. This new attack uses a nested pair of functions with exceptional behavior, where the second function is defined over the cycle of the first one. Next, we introduce several new generic attacks against hash combiners, notably using small cycles to improve the complexities of the best existing attacks on the XOR combiner, Zipper Hash and Hash-Twice. Last but not least, we propose the first quantum second preimage attack against Hash-Twice, reaching a quantum complexity\\(\\mathcal {O}(2^{3n/7})\\).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_4"}, {"primary_key": "558507", "vector": [], "sparse_vector": [], "title": "That&apos;s Not My Signature! Fail-Stop Signatures for a Post-quantum World.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The <PERSON>den’s revelations kick-started a community-wide effort to develop cryptographic tools against mass surveillance. In this work, we propose to add another primitive to that toolbox: Fail-Stop Signatures (FSS) [49]. FSS are digital signatures enhanced with a forgery-detection mechanism that can protect a computationally bounded signer from more powerful attackers. Despite the fascinating concept, research in this area stalled after the ’90 s. However, the ongoing transition to post-quantum cryptography, with its hiccups due to the novelty of underlying assumptions, has become the perfect use case for FSS. This paper aims to reboot research on FSS with practical use in mind: Our framework for FSS includes “fine-grained” security definitions (that assume a powerful, but bounded adversary e.g.: can break 128-bit of security, but not 256-bit). As an application, we show new FSS constructions for the post-quantum setting. We show that FSS are equivalent to standard, provably secure digital signatures that do not require rewinding or programming random oracles, and that this implies lattice-based FSS. Our main construction is an FSS version of\\(\\textsf{SPHINCS}^+\\), which required building FSS versions of all its building blocks:\\(\\textsf{WOTS}^+\\),\\(\\textsf{XMSS}\\), and\\(\\textsf{FORS}\\). In the process, we identify and provide generic solutions for two fundamental issues arising when deriving a large number of private keys from a single seed, and when building FSS for Hash-and-Sign-based signatures.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_4"}, {"primary_key": "558508", "vector": [], "sparse_vector": [], "title": "Black-Box (and Fast) Non-malleable Zero Knowledge.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Non-malleable zero-knowledge (NMZK), originally introduced in the seminal work of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (STOC 91), is a fundamental concept for modeling the security of proof systems against man-in-the-middle attacks. Recently, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (CRYPTO 2022) presented the first efficient constant-round NMZK argument system based solely on symmetric-key cryptography. Their construction relies on a non-black-box use of the involved cryptographic primitives and on multiple executions of <PERSON>ger<PERSON> (CCS 2017) that affect both the round complexity and the computational efficiency of their protocol. Their work left open the natural important challenge of achieving NMZK using the underlying primitives only in a black-box fashion (regardless of the number of rounds and actual efficiency). In this paper, we solve the aforementioned open problem by presenting the first NMZK argument system based on the black-box use of cryptographic primitives. Our work is optimal in the use of primitives since we only need one-way functions, and asymptotically optimal in the number of rounds since we only require a constant number of rounds. Our argument system is non-malleable with respect to the strong “simulation-extractability” flavor of non-malleability. Furthermore, we also show that our construction can be efficiently instantiated in Minicrypt, significantly improving upon the work of <PERSON> et al., both in terms of round complexity and computational efficiency.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_14"}, {"primary_key": "558509", "vector": [], "sparse_vector": [], "title": "Collision Resistance from Multi-collision Resistance for All Constant Parameters.", "authors": ["<PERSON>", "<PERSON>"], "summary": "At-multi-collision-resistant hash function(t-MCRH) is a family of shrinking functions for which it is computationally hard to findtdistinct inputs mapped to the same output by a function sampled from this family. Several works have shown thatt-MCRHs are sufficient for many of the applications of collision-resistant hash functions (CRHs), which correspond to the special case of\\(t = 2\\). An important question is hence whethert-MCRHs for\\(t > 2\\)are fundamentally weaker objects than CRHs. As a first step towards resolving this question, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (CRYPTO ’22) recently gave non-black-box constructions of infinitely-often secure CRHs fromt-MCRHs for\\(t \\in \\{3,4\\}\\)assuming the MCRH is sufficiently shrinking. Earlier on, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (CRYPTO ’18) also showed thatt-MCRHs for any constanttimply the weaker notion of adistributionalCRH. In this paper, we remove the limitations of prior works, and completely resolve the question of the power oft-MCRHs for constanttin the infinitely-often regime, showing that the existence of such a function family always implies the existence of an infinitely-often secure CRH. As in the works mentioned above, our proof is non-black-box and non-constructive. We further give a new domain extension result for MCRHs that enables us to show that the underlying MCRH need only have arbitrarily small linear shrinkage (mapping\\((1 + \\epsilon )n\\)bits tonbits for any fixed\\(\\epsilon > 0\\)) to imply the existence of CRHs.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_15"}, {"primary_key": "558510", "vector": [], "sparse_vector": [], "title": "Quantum One-Wayness of the Single-Round Sponge with Invertible Permutations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Sponge hashing is a widely used class of cryptographic hash algorithms which underlies the current international hash function standard SHA-3. In a nutshell, a sponge function takes as input a bit-stream of any length and processes it via a simple iterative procedure: it repeatedly feeds each block of the input into a so-calledblock function, and then produces a digest by once again iterating the block function on the final output bits. While much is known about the post-quantum security of the sponge construction in the case when the block function is modeled as a random function or one-way permutation, the case of permutations allowinginversequeries, which more accurately models the construction underlying SHA-3, has so far remained a fundamental open problem. In this work, we make new progress towards overcoming this barrier and show several results. First, we prove the “double-sided zero-search” conjecture proposed by <PERSON><PERSON><PERSON> (eprint’ 2021) and show that finding zero-pairs in a random 2n-bit permutation requires at least\\(\\varOmega (2^{n/2})\\)queries—and this is tight due to <PERSON><PERSON>’s algorithm. At the core of our proof lies a novel “symmetrization argument” which uses insights from the theory of Young subgroups. Second, we consider more general variants of the double-sided search problem and show similar query lower bounds for them. As an application, we prove the quantum one-wayness of the single-round sponge with invertible permutations in the quantum random permutation model.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_7"}, {"primary_key": "558511", "vector": [], "sparse_vector": [], "title": "Limits of Black-Box Anamorphic Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "(Receiver) Anamorphic encryption, introduced by <PERSON><PERSON><PERSON>.at Eurocrypt 2022, considers the question of achieving private communication in a world where secret decryption keys are under the control of a dictator. The challenge here is to be able to establish a secret communication channel to exchange covert (i.e. anamorphic) messages on top of some already deployed public key encryption scheme. Over the last few years several works addressed this challenge by showing new constructions, refined notions and extensions. Most of these constructions, however, are either ad hoc, in the sense that they build upon specific properties of the underlying PKE, or impose severe restrictions on the size of the underlying anamorphic message space. In this paper we consider the question of whether it is possible to have realizations of the primitive that are both generic and allow for large anamorphic message spaces. We give strong indications that, unfortunately, this is not the case. Our first result shows thatany black-box realizationof the primitive, i.e. any realization that accesses the underlying PKE only via oracle calls,musthave an anamorphic message space of size at most\\(\\textsf{poly}(\\lambda )\\)(\\(\\lambda \\)security parameter). Even worse, if one aims at stronger variants of the primitive (and, specifically, the notion of asymmetric anamorphic encryption, recently proposed by <PERSON><PERSON><PERSON> al.) we show that such black-box realizations are plainly impossible, i.e. no matter how small the anamorphic message space is. Finally, we show that our impossibility results are rather tight: indeed, by making more specific assumptions on the underlying PKE, it becomes possible to build generic AE where the anamorphic message space is of size\\(\\varOmega (2^\\lambda )\\).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_11"}, {"primary_key": "558512", "vector": [], "sparse_vector": [], "title": "Pairing-Free Blind Signatures from CDH Assumptions.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present the first concurrently-secure blind signatures making black-box use of a pairing-free group for which unforgeability, in the random oracle model, can be provedwithoutrelying on the algebraic group model (AGM), thus resolving a long-standing open question. Prior pairing-free blind signatures without AGM proofs have only been proved secure for bounded concurrency, relied on computationally expensive non-black-box use of NIZKs, or had complexity growing with the number of signing sessions due to the use of boosting techniques. Our most efficient constructions rely on the chosen-target CDH assumption and can be seen as blind versions of signatures by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (EUROCRYPT ’03) and <PERSON><PERSON>lier-<PERSON><PERSON> (CRYPTO ’05). We also give a less efficient scheme with security based on (plain) CDH. The underlying signing protocols consist of four (in order to achieve regular unforgeability) or five moves (for strong unforgeability). All schemes are proved statistically blind in the random oracle model.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_6"}, {"primary_key": "558513", "vector": [], "sparse_vector": [], "title": "On the Practical CPAD Security of &quot;exact&quot; and Threshold FHE Schemes and Libraries.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Aymen <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In their Eurocrypt’21 seminal paper, <PERSON> and <PERSON><PERSON><PERSON><PERSON> presented a passive attack against the CKKS approximate FHE scheme and introduced the notion of\\(\\text {CPA}^{D}\\)security. The current status quo is that this line of attacks does not apply to “exact” FHE. In this paper, we challenge this status quo by exhibiting a\\(\\text {CPA}^{D}\\)key recovery attack on the linearly homomorphic Regev cryptosystem, which easily generalizes to other xHE schemes such as BFV, BGV and TFHE, showing that these cryptosystems are not\\(\\text {CPA}^{D}\\)secure in their basic form. We also show that existing threshold variants of BFV, BGV and CKKS are particularly exposed to\\(\\text {CPA}^{D}\\)attackers and would be\\(\\text {CPA}^{D}\\)-insecure without proper smudging noise addition after partial decryption. Finally, we successfully implement our attack against several mainstream FHE libraries and discuss a number of natural countermeasures as well as their consequences in terms of FHE practice, security and efficiency. The attack itself is quite practical as it typically takes less than an hour on an average laptop PC, requiring a few thousand ciphertexts as well as up to around a million evaluations/decryptions, to perform a full key recovery.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_1"}, {"primary_key": "558514", "vector": [], "sparse_vector": [], "title": "Leakage Certification Made Simple.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Side channel evaluations benefit from sound characterisations of adversarial leakage models, which are the determining factor for attack success. Two questions are of interest: can we define and estimate a quantity that captures the ideal adversary (who knows all the distributions that are involved in an attack), and can we define and estimate a quantity that captures a concrete adversary (represented by a given leakage model)? Existing work has led to a proliferation of custom quantities to measure both types of adversaries, which can be data intensive to estimate in the ideal case, even for discrete side channels and especially when the number of dimensions in the side channel traces grows. In this paper, we show how to define the mutual information between carefully chosen variables of interest and how to instantiate a recently suggested mutual information estimator for practical estimation. We apply our results to real-world data sets and are the first to provide a mutual information-based characterisation of ideal and concrete adversaries utilising up to 30 data points.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_13"}, {"primary_key": "558515", "vector": [], "sparse_vector": [], "title": "Pseudorandom Error-Correcting Codes.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We constructpseudorandom error-correcting codes(or simplypseudorandom codes), which are error-correcting codes with the property that any polynomial number of codewords are pseudorandom to any computationally-bounded adversary. Efficient decoding of corrupted codewords is possible with the help of a decoding key. We build pseudorandom codes that are robust to substitution and deletion errors, where pseudorandomness rests on standard cryptographic assumptions. Specifically, pseudorandomness is based on either\\(2^{O(\\sqrt{n})}\\)-hardness of LPN, or polynomial hardness of LPN and the planted XOR problem at low density. As our primary application of pseudorandom codes, we present an undetectable watermarking scheme for outputs of language models that is robust to cropping and a constant rate of random substitutions and deletions. The watermark is undetectable in the sense that any number of samples of watermarked text are computationally indistinguishable from text output by the original model. This is the first undetectable watermarking scheme that can tolerate a constant rate of errors. Our second application is to steganography, where a secret message is hidden in innocent-looking content. We present a constant-rate stateless steganography scheme with robustness to a constant rate of substitutions. Ours is the first stateless steganography scheme with provable steganographic security andanyrobustness to errors.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_10"}, {"primary_key": "558516", "vector": [], "sparse_vector": [], "title": "On Central Primitives for Quantum Cryptography with Classical Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent work has introduced the “Quantum-Computation Classical-Communication” (QCCC) (<PERSON> et al.) setting for cryptography. There has been some evidence that One Way Puzzles (\\(\\textsf{OWPuzz}\\)) are the natural central cryptographic primitive for this setting (<PERSON><PERSON><PERSON> and <PERSON><PERSON>). For a primitive to be considered central it should have several characteristics. It should be well behaved (which for this paper we will think of as having amplification, combiners, and universal constructions); it should be implied by a wide variety of other primitives; and it should be equivalent to some class of useful primitives. We present combiners, correctness and security amplification, and a universal construction for\\(\\textsf{OWPuzz}\\). Our proof of security amplification uses a new and cleaner construction of EFI from\\(\\textsf{OWPuzz}\\)(in comparison to the result of <PERSON><PERSON><PERSON> and <PERSON><PERSON>) that generalizes to weak\\(\\textsf{OWPuzz}\\)and is the most technically involved section of the paper. It was previously known that\\(\\textsf{OWPuzz}\\)are implied by other primitives of interest including commitments, symmetric key encryption, one way state generators (\\(\\textsf{OWSG}\\)), and therefore pseudorandom states (\\(\\textsf{PRS}\\)). However we are able to rule out\\(\\textsf{OWPuzz}\\)’s equivalence to many of these primitives by showing a black box separation between general\\(\\textsf{OWPuzz}\\)and a restricted class of\\(\\textsf{OWPuzz}\\)(those with efficient verification, which we call\\(\\mathsf {EV-OWPuzz}\\)). We then show that\\(\\mathsf {EV-OWPuzz}\\)are also implied by most of these primitives, which separates them from\\(\\textsf{OWPuzz}\\)as well. This separation also separates extending\\(\\textsf{PRS}\\)from highly compressing\\(\\textsf{PRS}\\)answering an open question of Ananth et al.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_8"}, {"primary_key": "558517", "vector": [], "sparse_vector": [], "title": "Universal Composable Transaction Serialization with Order Fairness.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Order fairness in the context of distributed ledgers has received recently significant attention due to a range of attacks that exploit the reordering and adaptive injection of transactions (violating what is known as “input causality”). To address such concerns an array of definitions for order fairness has been put forth together with impossibility and feasibility results highlighting the difficulty and multifaceted nature of fairness in transaction serialization. Motivated by this we present a comprehensive modeling of order fairness capitalizing on the universal composition (UC) setting. Our results capture the different flavors of sender order fairness and input causality (which is arguably one of the most critical aspects of ledger transaction processing with respect to serialization attacks) and we parametrically illustrate what are the limits of feasibility for realistic constructions via an impossibility result. Our positive result, a novel distributed ledger protocol utilizing trusted enclaves, complements tightly our impossibility result, hence providing anoptimalsender order fairness ledger construction that is also eminently practical.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_5"}, {"primary_key": "558518", "vector": [], "sparse_vector": [], "title": "Polynomial Commitments from Lattices: Post-quantum Security, Fast Verification and Transparent Setup.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "Polynomial commitment scheme allows a prover to commit to a polynomial\\(f \\in \\mathcal {R}[X]\\)of degreeL, and later prove that the committed function was correctly evaluated at a specified pointx; in other words\\(f(x)=u\\)for public\\(x,u \\in \\mathcal {R}\\). Most applications of polynomial commitments, e.g. succinct non-interactive arguments of knowledge (SNARKs), require that (i) both the commitment and evaluation proof are succinct (i.e., polylogarithmic in the degreeL) - with the latter being efficiently verifiable, and (ii) no pre-processing step is allowed. Surprisingly, as far as plausibly quantum-safe polynomial commitments are concerned, the currently most efficient constructions only rely on weak cryptographic assumptions, such as security of hash functions. Indeed, despite making use of the underlying algebraic structure, prior lattice-based polynomial commitments still seem to be much behind the hash-based ones. Moreover, security of the aforementioned lattice constructions against quantum adversaries was never formally discussed. In this work, we bridge the gap and propose the first (asymptotically and concretely) efficient lattice-based polynomial commitment with transparent setup and post-quantum security. Our interactive variant relies on the standard (Module-)SIS problem, and can be made non-interactive in the random oracle model using Fiat-Shamir transformation. In addition, we equip the scheme with a knowledge soundness proof against quantum adversaries which can be of independent interest. In terms of concrete efficiency, for\\(L=2^{20}\\)our scheme yields proofs of size 2X smaller than the hash-basedFRIcommitment (Block et al., Asiacrypt 2023), and 70X smaller than the very recent lattice-based construction by Albrecht et al. (Eurocrypt 2024).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_7"}, {"primary_key": "558519", "vector": [], "sparse_vector": [], "title": "Secure Multiparty Computation with Identifiable Abort via Vindicating Release.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the dishonest-majority setting, secure multiparty computation (MPC) withidentifiable abort(IA) guarantees that honest parties can identify and agree upon at least one cheating party if the protocol does not produce an output. Known MPC constructions with IA rely on generic zero-knowledge proofs, adaptively secure oblivious transfer (OT) protocols, or homomorphic primitives, and thus incur a substantial penalty with respect to protocols that abort without identifiability. We introduce a new, weaker notion of IA calledinput-revealingIA (IRIA), which can be constructed through selective revealing of committed input values—a technique we callvindicating release. We show that this weaker form of IA can be achieved with small concrete overheads for many interesting protocols in the literature, including the pre-processing protocols needed for several state-of-the-art MPC protocols. We next show how to assemble these IRIA components into an MPC protocol for any functionality withstandardIA. Such a realization differs minimally in terms of cost, techniques, and analysis from the equivalent realization that lacks identifiability, e.g., our total bandwidth overhead incurred is less than 2x, which is an asymptotic improvement over prior work on IA. On a practical level, we apply our techniques to the problem of threshold ECDSA, and show that the resulting protocol with standard IA is concretely efficient. On a theoretical level, we present a compiler that transforms any secure protocol into one with standard IA assuming only a variant of statically-corruptable ideal OT.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_2"}, {"primary_key": "558520", "vector": [], "sparse_vector": [], "title": "The One-Wayness of Jacobi Signatures.", "authors": ["<PERSON>-<PERSON>", "<PERSON>"], "summary": "We show that under a mild number-theoretic conjecture, recovering an integer from its Jacobi signature modulo\\(N = p^2 q\\), for primespandq, is as hard as factoringN. This relates, for the first time, the one-wayness of a pseudorandom generator that <PERSON><PERSON><PERSON><PERSON> proposed in 1988, to a standard number-theoretic problem. In addition, we show breaking the Jacobi pseudorandom function is no harder than factoring.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_1"}, {"primary_key": "558521", "vector": [], "sparse_vector": [], "title": "10-Party Sublinear Secure Computation from Standard Assumptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Secure computation enables mutually distrusting parties to jointly compute a function on their secret inputs, while revealing nothing beyond the function output. A long-running challenge is understanding the required communication complexity of such protocols–in particular, when communication can besublinearin the circuit representation size of the desired function. While several techniques have demonstrated the viability of sublinear secure computation in the two-party setting, known methods for the corresponding multi-party setting rely either on fully homomorphic encryption, non-standard hardness assumptions, or are limited to a small number of parties. In this work, we expand the study of multi-party sublinear secure computation by demonstrating sublinear-communication 10-party computation from various combinations of standard hardness assumptions. In particular, our contributions show: 8-party homomorphic secret sharing under the hardness of (DDH or DCR), the superpolynomial hardness of LPN, and the existence of constant-depth pseudorandom generators; A general framework for achieving\\((N+M)\\)-party sublinear secure computation usingM-party homomorphic secret sharing for\\(\\ensuremath {\\textsf{NC}} ^1\\)and correlated symmetric PIR. Together, our constructions imply the existence of a 10-party MPC protocol with sublinear computation. At the core of our techniques lies a novel series of computational approaches based on homomorphic secret sharing.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_2"}, {"primary_key": "558522", "vector": [], "sparse_vector": [], "title": "Fully Secure MPC and zk-FLIOP over Rings: New Constructions, Improvements and Extensions.", "authors": ["<PERSON> P. K<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We revisit the question of the overhead to achieve full security (i.e., guaranteed output delivery) in secure multiparty computation (MPC). Recent works have closed the gap between full security and semi-honest security, by introducing protocols where the parties first compute the circuit using a semi-honest protocol and then run a verification step with sublinear communication in the circuit size. However, in these works the number of interaction rounds in the verification step is also sublinear in the circuit’s size. Unlike communication, the round complexity of the semi-honest execution typically grows with the circuit’sdepthand not its size. Hence, for large but shallow circuits, this additional number of rounds incurs a significant overhead. Motivated by this gap, we make the following contributions: We present a new MPC framework to obtain full security, compatible with effectivelyanyring, that has an additive communication overhead of only\\(O(\\log |C|)\\), where |C| is the number of multiplication gates in the circuit, and aconstantnumber of additional rounds beyond the underlying semi-honest protocol. Our framework works with any linear secret sharing scheme and relies on a new to utilize the machinery ofzero-knowledge fully linear interactive oracle proofs(zk-FLIOP) in a black-box way. We present several instantiations to the building blocks of our compiler, from which we derive concretely efficient protocols in different settings. We present extensions to the zk-FLIOP primitive for very general settings. The first one is for proving statements over potentially non-commutative rings, where the only requirement is that the ring has a large enough set where (1) every element in the set commutes with every element in the ring, and (2) the difference between any two distinct elements is invertible. Our second zk-FLIOP extension is for proving statements over Galois Rings. For these rings, we present concrete improvements on the current state-of-the-art for the case of constant-round proofs, by making use ofReverse Multiplication Friendly Embeddings(RMFEs).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_5"}, {"primary_key": "558523", "vector": [], "sparse_vector": [], "title": "Lossy Cryptography from Code-Based Assumptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the past few decades, we have seen a proliferation of advanced cryptographic primitives with lossy or homomorphic properties built from various assumptions such as Quadratic Residuosity, Decision<PERSON> Di<PERSON>-<PERSON>, and Learning with <PERSON><PERSON><PERSON>. These primitives imply hard problems in the complexity class\\(\\mathcal {SZK}\\)(statistical zero-knowledge); as a consequence, they can only be based on assumptions that are broken in\\(\\mathcal {BPP}^{\\mathcal {SZK}}\\). This poses a barrier for building advanced cryptography from code-based assumptions such as Learning Parity with Noise (LPN), as LPN is only known to be in\\(\\mathcal {BPP}^{\\mathcal {SZK}}\\)under an extremely low noise rate\\(\\frac{\\log ^2 n}{n}\\), for which it is broken in quasi-polynomial time. In this work, we propose a new code-based assumption: Dense-Sparse LPN, that falls in the complexity class\\(\\mathcal {BPP}^{\\mathcal {SZK}}\\)and is conjectured to be secure against subexponential time adversaries. Our assumption is a variant of LPN that is inspired by <PERSON><PERSON><PERSON><PERSON><PERSON>’s cryptosystem and random\\(k\\text{- }\\)XOR in average-case complexity. Roughly, the assumption states that for a random (dense) matrix\\(\\textbf{T}\\), random sparse matrix\\(\\textbf{M}\\), and sparse noise vector\\(\\textbf{e}\\)drawn from the Bernoulli distribution with inverse polynomial noise probability. We leverage our assumption to build lossy trapdoor functions (Peikert-Waters STOC 08). This gives the first post-quantum alternative to the lattice-based construction in the original paper. Lossy trapdoor functions, being a fundamental cryptographic tool, are known to enable a broad spectrum of both lossy and non-lossy cryptographic primitives; our construction thus implies these primitives in a generic manner. In particular, we achieve collision-resistant hash functions with plausible subexponential security, improving over a prior construction from LPN with noise rate\\(\\frac{\\log ^2 n}{n}\\)that is only quasi-polynomially secure.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_2"}, {"primary_key": "558524", "vector": [], "sparse_vector": [], "title": "Non-interactive Zero-Knowledge from LPN and MQ.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhengzhong Jin"], "summary": "We give the first construction of non-interactive zero-knowledge (NIZK) arguments from post-quantum assumptions other than Learning with <PERSON>rro<PERSON>. In particular, we achieve NIZK under the polynomial hardness of the Learning Parity with Noise (LPN) assumption, and the exponential hardness of solving random underdetermined multivariate quadratic equations (MQ). We also construct NIZK satisfying statistical zero-knowledge assuming a new variant of LPN, Dense-Sparse LPN, introduced by <PERSON><PERSON> and <PERSON> (Crypto 2024), together with exponentially-hard MQ. The main technical ingredient of our construction is an extremely natural (but only in hindsight!) construction of correlation-intractable (CI) hash functions from MQ, for a NIZK-friendly sub-class of constant-degree polynomials that we call concatenated constant-degree polynomials. Under exponential security, this hash function also satisfies the stronger notion of approximate CI for concatenated constant-degree polynomials. The NIZK construction then follows from a prior blueprint of Brakerski-Ko<PERSON>ula-Mour (CRYPTO 2020). In addition, we show how to construct (approximate) CI hashing for degree-dfunctions from the (exponential) hardness of solving random degree-dequations, a natural generalization of MQ. To realize NIZK with statistical zero-knowledge, we design a lossy public-key encryption scheme with approximate linear decryption and inverse-polynomial decryption error from Dense-Sparse LPN. These constructions may be of independent interest. Our work therefore gives a new way to leverage MQ with uniformly random equations, which has found little cryptographic applications to date. Indeed, most applications in the context of encryption and signature schemes make use of structured variants of MQ, where the polynomials are not truly random but posses a hidden planted structure. We believe that the MQ assumption may plausibly find future use in the designing other advanced proof systems.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_10"}, {"primary_key": "558525", "vector": [], "sparse_vector": [], "title": "Adaptively Secure BLS Threshold Signatures from DDH and co-CDH.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Threshold signatures are one of the most important cryptographic primitives in distributed systems. A popular choice of threshold signature scheme is the BLS threshold signature introduced by <PERSON>yreva (PKC’03). Some attractive properties of Boldyreva’s threshold signature are that the signatures are unique and short, the signing process is non-interactive, and the verification process is identical to that of non-threshold BLS. These properties have resulted in its practical adoption in several decentralized systems. However, despite its popularity and wide adoption, up until recently, the Boldyreva scheme has been proven secure only against a static adversary. Very recently, <PERSON><PERSON> and Loss (CCS’22) presented the first proof of adaptive security for the Boldyreva scheme, but they have to rely on strong and non-standard assumptions such as the hardness of one-more discrete log (OMDL) and the Algebraic Group Model (AGM). In this paper, we present the first adaptively secure threshold BLS signature scheme that relies on the hardness of DDH and co-CDH in asymmetric pairing groups in the Random Oracle Model (ROM). Our signature scheme also has non-interactive signing, compatibility with non-threshold BLS verification, and practical efficiency like <PERSON><PERSON><PERSON>’s scheme. These properties make our protocol a suitable candidate for practical adoption with the added benefit of provable adaptive security.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_9"}, {"primary_key": "558526", "vector": [], "sparse_vector": [], "title": "Radical $\\root N \\of {\\mathrm {\\acute{e}lu}}$ Isogeny Formulae.", "authors": ["<PERSON>"], "summary": "We provide explicit radicalN-isogeny formulae for all odd integersN. The formulae are compact closed-form expressions which require oneNth root computation and\\(\\mathcal {O}(N)\\)basic field operations. The formulae are highly efficient to compute a long chain ofN-isogenies, and have the potential to be extremely beneficial for speeding up certain cryptographic protocols such as CSIDH. Unfortunately, the formulae are conjectured, but we provide ample supporting evidence which strongly suggests their correctness. For CSIDH-512, we notice an additional 35% speed-up when using radical isogenies up to\\(N=199\\), compared to the work by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON><PERSON>, which uses radical isogenies up to\\(N=19\\)only. The addition of our radical isogenies also speeds up the computation of larger class group actions in a comparable fashion.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_5"}, {"primary_key": "558527", "vector": [], "sparse_vector": [], "title": "Fully Malicious Authenticated PIR.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Authenticated PIR enables a server to initially commit to a database ofNitems, for which a client can later privately obtain individual items with complexity sublinear inN, with the added guarantee that the retrieved item is consistent with the committed database. A crucial requirement isprivacy with abort, i.e., the server should not learn anything about a queryevenif it learns whether the client aborts. This problem was recently considered by <PERSON> et al. (USENIX ’23), who proposed solutions secure under the assumption that the database is committed tohonestly. Here, we close this gap for their DDH-based scheme, and present a solution that tolerates fully malicious servers that provide potentially malformed commitments. Our scheme has communication and client computational complexity\\(\\mathcal {O}_{\\lambda }(\\sqrt{N})\\), does not require any additional assumptions, and does not introduce heavy machinery (e.g., generic succinct proofs). We do so by introducingvalidation queries, which, from the server’s perspective, are computationally indistinguishable from regular PIR queries. Provided that the server succeeds in correctly answering\\(\\kappa \\)such validation queries, the client is convinced with probability\\(1-\\frac{1}{2^\\kappa }\\)that the server is unable to break privacy with abort.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_4"}, {"primary_key": "558528", "vector": [], "sparse_vector": [], "title": "Compact Key Storage - A Modern Approach to Key Backup and Delegation.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "End-to-End (E2E) encrypted messaging, which prevents even the service provider from learning communication contents, is gaining popularity. Since users care about maintaining access to their data even if their devices are lost or broken or just replaced, these systems are often paired with cloud backup solutions: Typically, the user will encrypt their messages with a fixed key, and upload the ciphertexts to the server. Unfortunately, this naive solution has many drawbacks. First, it often undermines the fancy security guarantees of the core application, such as forward secrecy (FS) and post-compromise security (PCS), in case the single backup key is compromised. Second, they are wasteful for backing up conversations in large groups, where many users are interested in backing up the same sequence of messages. Instead, we formalize a new primitive calledCompact Key Storage(CKS) as the “right” solution to this problem. Such CKS scheme allows a mutable set of parties to delegate to a server storage of an increasing set of keys, while each client maintains only a small state. Clients update their state as they learn new keys (maintaining PCS), or whenever they want to forget keys (achieving FS), often without the need to interact with the server. Moreover, access to the keys (or some subset of them) can be efficiently delegated to new group members, who all efficiently share the same server’s storage. We carefully define syntax, correctness, privacy, and integrity of CKS schemes, and build two efficient schemes provably satisfying these notions. Ourline schemecovers the most basic “all-or-nothing” flavor of CKS, where one wishes to compactly store and delegate the entire history of past secrets. Thus, new users enjoy the efficiency and compactness properties of the CKS only after being granted access to the entire history of keys. In contrast, ourinterval schemeis only slightly less efficient but allows for finer-grained access, delegation, and deletion of past keys.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_3"}, {"primary_key": "558529", "vector": [], "sparse_vector": [], "title": "Sometimes You Can&apos;t Distribute Random-Oracle-Based Proofs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the conditions under which straight-line extractable NIZKs in the random oracle model (i.e. without a CRS) permit multiparty realizations that are black-box in the same random oracle. We show that even in the semi-honest setting, any MPC protocol to compute such a NIZK cannot make black-box use of the random oracle or a hash function instantiating it if security against all-but-one corruptions is desired, unless the number of queries made by the verifier to the oracle grows linearly with the number of parties. This presents a fundamental barrier to constructing efficient protocols to securely distribute the computation of NIZKs (and signatures) based on MPC-in-the-head, PCPs/IOPs, and sigma protocols compiled with transformations due to Fischlin, Pass, or Unruh. When the adversary is restricted to corrupt only a constant fraction of parties, we give a positive result by means of a tailored construction, which demonstrates that our impossibility does not extend to weaker corruption models in general.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_12"}, {"primary_key": "558530", "vector": [], "sparse_vector": [], "title": "On the (In)Security of the BUFF Transform.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The BUFF transform is a generic transformation for digital signature schemes, with the purpose of obtaining additional security properties beyond standard unforgeability, e.g.,exclusive ownershipandnon-resignability. In the call for additional post-quantum signatures, these were explicitly mentioned by the NIST as “additional desirable security properties”, and some of the submissions indeed refer to the BUFF transform with the purpose of achieving them, while some other submissions follow the design of the BUFF transform without mentioning it explicitly. In this work, we show the following negative results regarding the non-resignability property in general, and the BUFF transform in particular. In the plain model, we observe by means of a simple attack that any signature scheme for which the message has a high entropy given the signature does not satisfy the non-resignability property (while non-resignability is trivially not satisfied if the message can be efficiently computed from its signature). Given that the BUFF transform has high entropy in the message given the signature, it follows that the BUFF transform doesnotachieve non-resignability whenever the random oracle is instantiated with a hash function, no matter what hash function. When considering the random oracle model (ROM), the matter becomes slightly more delicate since prior works did not rigorously define the non-resignability property in the ROM. For the natural extension of the definition to the ROM, we observe that our impossibility result still holds, despite there having been positive claims about the non-resignability of the BUFF transform in the ROM. Indeed, prior claims of the non-resignability of the BUFF transform rely on faulty argumentation. On the positive side, we prove that asaltedversion of the BUFF transform satisfies a slightlyweakervariant of non-resignability in the ROM, covering both classical and quantum attacks,ifthe entropy requirement in the (weakened) definition of non-resignability is statistical; for the computational variant, we show yet another negative result.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_8"}, {"primary_key": "558531", "vector": [], "sparse_vector": [], "title": "Laconic Function Evaluation and ABE for RAMs from (Ring-)LWE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>e", "<PERSON>"], "summary": "Laconic function evaluation (LFE) allows us to compress a circuitfinto a short digest. Anybody can use this digest as a public-key to efficiently encrypt some inputx. Decrypting the resulting ciphertext reveals the outputf(x), while hiding everything else aboutx. In this work we consider LFE forRandom-Access Machines(RAM-LFE) where, instead of a circuitf, we have a RAM program\\(f_{\\textsf{DB}}\\)that potentially contains some large hard-coded data\\(\\textsf{DB}\\). The decryption run-time to recover\\(f_{\\textsf{DB}}(x)\\)from the ciphertext should be roughly the same as a plain evaluation of\\(f_{\\textsf{DB}}(x)\\)in the RAM model, which can be sublinear in the size of\\(\\textsf{DB}\\). Prior works constructed LFE for circuits under LWE, and RAM-LFE under indisitinguishability obfuscation (iO) and Ring-LWE. In this work, we construct RAM-LFE with essentially optimal encryption and decryption run-times from just Ring-LWE and a standard circular security assumption, without iO. RAM-LFE directly yields 1-key succinct functional encryption and reusable garbling for RAMs with similar parameters. If we only want anattribute-basedLFE for RAMs (RAM-AB-LFE), then we can replace Ring-LWE with plain LWE in the above. Orthogonally, if we only wantleveledschemes, where the encryption/decryption efficiency can scale with the depth of the RAM computation, then we can remove the need for a circular-security. Lastly, we also get a leveled many-keyattribute-based encryption for RAMs (RAM-ABE), from LWE.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_4"}, {"primary_key": "558532", "vector": [], "sparse_vector": [], "title": "Tight Characterizations for Preprocessing Against Cryptographic Salting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cryptography often considers the strongest yet plausible attacks in the real world. Preprocessing (a.k.a. non-uniform attack) plays an important role in both theory and practice: an efficient online attacker can take advantage of advice prepared by a time-consuming preprocessing stage. Salting is a heuristic strategy to counter preprocessing attacks by feeding a small amount of randomness to the cryptographic primitive. We present general and tight characterizations of preprocessing against cryptographic salting, with upper bounds matching the advantages of the most intuitive attack. Our result quantitatively strengthens the previous work by <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON> (EUROCRYPT’18). Our proof exploits a novel connection between the non-uniform security of salted games and direct product theorems for memoryless algorithms. For quantum adversaries, we give similar characterizations for property finding games, resolving an open problem of the quantum non-uniform security of salted collision resistant hash by <PERSON>, <PERSON>, <PERSON>, and <PERSON> (FOCS’20). Our proof extends the compressed oracle framework of Zhandry (CRYPTO’19) to prove quantum strong direct product theorems for property finding games in the average-case hardness.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_12"}, {"primary_key": "558533", "vector": [], "sparse_vector": [], "title": "Generic MitM Attack Frameworks on Sponge Constructions.", "authors": ["<PERSON><PERSON> Dong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes general meet-in-the-middle (MitM) attack frameworks for preimage and collision attacks on hash functions based on (generalized) sponge construction. As the first contribution, our MitM preimage attack framework covers a wide range of sponge-based hash functions, especially those with lower claimed security level for preimage compared to their output size. Those hash functions have been very widely standardized (e.g.,Ascon-Hash,PHOTON, etc.), but are rarely studied against preimage attacks. Even the recent MitM attack framework on sponge construction by <PERSON> et al. (EUROCRYPT 2023) cannot attack those hash functions. As the second contribution, our MitM collision attack framework shows a different tool for the collision cryptanalysis on sponge construction, while previous collision attacks on sponge construction are mainly based on differential attacks. Most of the results in this paper are the first third-party cryptanalysis results. If cryptanalysis previously existed, our new results significantly improve the previous results, such as improving the previous 2-round collision attack onAscon-Hashto the current 4 rounds, improving the previous 3.5-round quantum preimage attack on\\(\\text {SPHINCS}^+\\)-Harakato our 4-round classical preimage attack, etc.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_1"}, {"primary_key": "558534", "vector": [], "sparse_vector": [], "title": "Two-Round Threshold Signature from Algebraic One-More Learning with Errors.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Threshold signatures have recently seen a renewed interest due to applications in cryptocurrency while NIST has released a call for multi-party threshold schemes, with a deadline for submission expected for the first half of 2025. So far, all lattice-based threshold signatures requiring two-rounds or less are based on heavy tools such as (fully) homomorphic encryption (FHE) and homomorphic trapdoor commitments (HTDC). This is not unexpected considering that most efficient two-round signatures from classical assumptions either rely on idealized model such as algebraic group models or on one-more type assumptions, none of which we have a nice analogue in the lattice world. In this work, we construct the first efficient two-round lattice-based threshold signature without relying on FHE or HTDC. It has an offline-online feature where the first round can be preprocessed without knowing message or the signer sets, effectively making the signing phase non-interactive. The signature size is small and shows great scalability. For example, even for a threshold as large as 1024 signers, we achieve a signature size roughly 11 KB. At the heart of our construction is a new lattice-based assumption called thealgebraic one-more learning with errors(\\(\\textsf{AOM}\\text {-}\\textsf{MLWE} \\)) assumption. We believe this to be a strong inclusion to our lattice toolkits with an independent interest. We establish the selective security of\\(\\textsf{AOM}\\text {-}\\textsf{MLWE} \\)based on the standard\\(\\textsf{MLWE} \\)and\\(\\textsf{MSIS} \\)assumptions, and provide an in depth analysis of its adaptive security, which our threshold signature is based on.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_13"}, {"primary_key": "558535", "vector": [], "sparse_vector": [], "title": "Flood and Submerse: Distributed Key Generation and Robust Threshold Signature from Lattices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a new framework based onrandom submersions—that is projection over a random subspace blinded by a small Gaussian noise—for constructing verifiable short secret sharing and showcase it to construct efficient threshold lattice-based signatures in the hash-and-sign paradigm, when based onnoise flooding. This is, to our knowledge, the first hash-and-sign lattice-based threshold signature. Our threshold signature enjoys the very desirable property ofrobustness, including at key generation. In practice, we are able to construct a robust hash-and-sign threshold signature for threshold and provide a typical parameter set for threshold\\(T=16\\)and signature size 13kB. Our constructions are provably secure under standard\\(\\textsf{MLWE} \\)assumption in the\\(\\textsf{ROM}\\)and only require basic primitives as building blocks. In particular, we do not rely on FHE-type schemes.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_14"}, {"primary_key": "558536", "vector": [], "sparse_vector": [], "title": "Not Just Regular Decoding: Asymptotics and Improvements of Regular Syndrome Decoding Attacks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Cryptographic constructions often base security on structured problem variants to enhance efficiency or to enable advanced functionalities. This led to the introduction of the Regular Syndrome Decoding (RSD) problem, which guarantees that a solution to the Syndrome Decoding (SD) problem follows a particular block-wise structure. Despite recent attacks exploiting that structure by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (Eurocrypt ’23) and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (CCJ, Eurocrypt ’23), many questions about the impact of the regular structure on the problem hardness remain open. In this work we initiate a systematic study of the hardness of the RSD problem starting from its asymptotics. We classify different parameter regimes revealing large regimes for which RSD instances are solvable in polynomial time and on the other hand regimes that lead to particularly hard instances. Against previous perceptions, we show that a classification solely based on the uniqueness of the solution is not sufficient for isolating the worst case parameters. Further, we provide an in-depth comparison between SD and RSD in terms of reducibility and computational complexity, identifying regimes in which RSD instances are actuallyharderto solve. We provide the first asymptotic analyses of the algorithms presented by CCJ, establishing their worst case decoding complexities as\\(2^{0.141n}\\)and\\(2^{0.135n}\\), respectively. We then introduceregular-ISDalgorithms by showing how to tailor the whole machinery of advanced Information Set Decoding (ISD) techniques from attacking SD to the RSD setting. The fastest regular-ISD algorithm improves the worst case decoding complexity significantly to\\(2^{0.112n}\\). Eventually, we show that also with respect to suggested parameters regular-ISD outperforms previous approaches in most cases, reducing security levels by up to 30 bits.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_6"}, {"primary_key": "558537", "vector": [], "sparse_vector": [], "title": "Ring Signatures for Deniable AKEM: Gandalf&apos;s Fellowship.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Ring signatures, a cryptographic primitive introduced by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (ASIACRYPT 2001), offer signer anonymity within dynamically formed user groups. Recent advancements have focused on lattice-based constructions to improve efficiency, particularly for large signing rings. However, current state-of-the-art solutions suffer from significant overhead, especially for smaller rings. In this work, we present a novel NTRU-based ring signature scheme,\\(\\textsc {<PERSON><PERSON><PERSON>} \\), tailored towards small rings. Our post-quantum scheme achieves a 50% reduction in signature sizes compared to the linear ring signature scheme\\(\\textsc {<PERSON><PERSON>} \\)(ACNS 2019). When compared to the sublinear ring signature scheme\\(\\textsc {Smile} \\)(CRYPTO 2021), our signatures are more compact for rings of up to 26. In particular, for rings of size two, our ring signatures are only 1236 bytes. Additionally, we explore the use of ring signatures to obtain deniability in authenticated key exchange mechanisms (AKEMs), the primitive behind the recent HPKE standard used in MLS and TLS. We take a fine-grained approach at formalising sender deniability within AKEM and seek to define the strongest possible notions. Our contributions extend to a black-box construction of a deniable AKEM from a KEM and a ring signature scheme for rings of size two. Our approach attains the highest level of confidentiality and authenticity, while simultaneously preserving the strongest forms of deniability in two orthogonal settings. Finally, we present parameter sets for our schemes, and show that our deniable AKEM, when instantiated with our ring signature scheme, yields ciphertexts of 2004 bytes.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_10"}, {"primary_key": "558538", "vector": [], "sparse_vector": [], "title": "How to Prove Statements Obliviously?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cryptographic applications often require proving statements about hidden secrets satisfying certain circuit relations. Moreover, these proofs must often be generated obliviously, i.e., without knowledge of the secret. This work presents a new technique called—FRI on hidden values—for efficiently proving such statements. This technique enables a polynomial commitment scheme for values hidden inside linearly homomorphic primitives, such as linearly homomorphic encryption, linearly homomorphic commitment, group exponentiation, fully homomorphic encryption, etc. Building on this technique, we obtain the following results. An efficient SNARK for proving the honest evaluation of FHE ciphertexts. This allows for an efficiently verifiable private delegation of computation, where the client only needs to performlogarithmicmany FHE computations to verify the correctness of the computation. An efficient approach for privately delegating the computation of zkSNARKs to asingle untrusted server, without requiring the server to make any non-black-box use of cryptography. All prior works require multiple servers and the assumption that some subset of the servers are honest. A weighted threshold signature scheme that does not require any setup. In particular, parties may sample their own keys independently, and no distributed key generation (DKG) protocol is needed. Furthermore, the efficiency of our scheme iscompletely independent of the weights. Prior to this work, there wereno known black-box feasibility resultsforanyof these applications. We also investigate the use of this approach in the context of public proof aggregation. These are only a few representative applications that we explore in this paper. We expect our techniques to be widely applicable in many other scenarios.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_14"}, {"primary_key": "558539", "vector": [], "sparse_vector": [], "title": "Scalable Multiparty Computation from Non-linear Secret Sharing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A long line of work has investigated the design of scalable secure multiparty computation (MPC) protocols with computational and communication complexity independent of the number of parties (beyond any dependence on the circuit size). We present the first unconditionally-secure MPC protocols for arithmetic circuits overlarge fieldswith total computation\\(\\ensuremath {\\mathcal {O}\\left( {|C|\\log |F|}\\right) }\\), where |C| and |F| denote the circuit and field size, respectively. Prior work could either achieve similar complexity only incommunication, or required highly structured circuits, or expensive circuit transformations. To obtain our results, we depart from the prior approach of share packing in linear secret-sharing schemes; instead, we use an “unpacking” approach vianon-linearsecret sharing.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_12"}, {"primary_key": "558540", "vector": [], "sparse_vector": [], "title": "Threshold Encryption with Silent Setup.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We build a concretely efficient threshold encryption scheme where the joint public key of a set of parties is computed as adeterministicfunction of their locally computed public keys, enabling asilentsetup phase. By eliminating interaction from the setup phase, our scheme immediately enjoys several highly desirable features such as asynchronous setup, multiverse support, and dynamic threshold. Prior to our work, the only known constructions of threshold encryption with silent setup relied on heavy cryptographic machinery such as indistinguishability Obfuscation or witness encryption for all of\\(\\textsf{NP}\\). Our core technical innovation lies in building a special purpose witness encryption scheme for the statement “at leasttparties have signed a given message”. Our construction relies on pairings and is proved secure in the Generic Group Model. Notably, our construction, restricted to the special case of threshold\\(t=1\\), gives an alternative construction of the (flexible) distributed broadcast encryption from pairings, which has been the central focus of several recent works. We implement and evaluate our scheme to demonstrate its concrete efficiency. Both encryption and partial decryption are constant time, taking\\(<7\\,\\)ms and\\(<1\\,\\)ms, respectively. For a committee of 1024 parties, the aggregation of partial decryptions takes\\(<200\\,\\)ms, when all parties provide partial decryptions. The size of each ciphertext is\\(\\approx 8\\times \\)larger than an ElGamal ciphertext.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_12"}, {"primary_key": "558541", "vector": [], "sparse_vector": [], "title": "Reducing the CRS Size in Registered ABE Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Attribute-based encryption (ABE) is a generalization of public-key encryption that enables fine-grained access control to encrypted data. In (ciphertext-policy) ABE, a centraltrustedauthority issues decryption keys for attributesxto users. In turn, ciphertexts are associated with a decryption policy\\(\\ensuremath {\\mathcal {P}}\\). Decryption succeeds and recovers the encrypted message whenever\\(\\ensuremath {\\mathcal {P}}(x) = 1\\). Recently, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> (Eurocrypt 2023) introduced the notion ofregistered ABE, which is an ABE schemewithouta trusted central authority. Instead, users generate their own public/secret keys (just like in public-key encryption) and then register their keys (and attributes) with a key curator. The key curator is a transparent and untrusted entity. Currently, the best pairing-based registered ABE schemes support monotone Boolean formulas and an a priori bounded number of usersL. A major limitation of existing schemes is that they require a (structured) common reference string (CRS) of size\\(L^2 \\cdot |\\ensuremath {\\mathcal {U}}|\\)where\\(|\\ensuremath {\\mathcal {U}}|\\)is the size of the attribute universe. In other words, the size of the CRS scalesquadraticallywith the number of users and multiplicatively with the size of the attribute universe. The large CRS makes these schemes expensive in practice and limited to a small number of users and a small universe of attributes. In this work, we give two ways to reduce the CRS size in pairing-based registered ABE schemes. First, we introduce a combinatoric technique based onprogression-free setsthat enables registered ABE for the same class of policies but with a CRS whose size is sub-quadratic in the number of users. Asymptotically, we obtain a scheme where the CRS size isnearly linearin the number of usersL(i.e.,\\(L^{1 + o(1)}\\)). If we take a more concrete-efficiency-oriented focus, we can instantiate our framework to obtain a construction with a CRS of size\\(L^{\\log _2 3} \\approx L^{1.6}\\). For instance, in a scheme for 100,000 users, our approach reduces the CRS by a factor of over\\(115\\times \\)compared to previous approaches (and without incurring any overhead in encryption/decryption time). Our second approach for reducing the CRS size is to rely on a partitioning-based argument when arguing security of the registered ABE scheme. Previous approaches took a dual-system approach. Using a partitioning-based argument yields a registered ABE scheme where the size of the CRS isindependentof the size of the attribute universe. The cost is the resulting scheme satisfies a weaker notion of static security. Our techniques for reducing the CRS size can be combined, and taken together, we obtain a pairing-based registered ABE scheme that supports monotone Boolean formulas with a CRS size of\\(L^{1 + o(1)}\\). Notably, this is the first pairing-based registered ABE scheme that does not require imposing a bound on the size of the attribute universe during setup time. As an additional application, we also show how to apply our techniques based on progression-free sets to the batch argument (BARG) for\\(\\ensuremath {\\textsf{NP}} \\)scheme of Waters and Wu (Crypto 2022) to obtain a scheme with a nearly-linear CRSwithoutneeding to rely on non-black-box bootstrapping techniques.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_5"}, {"primary_key": "558542", "vector": [], "sparse_vector": [], "title": "Computation Efficient Structure-Aware PSI from Incremental Function Secret Sharing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Structure-Aware Private Set Intersection (sa-PSI), recently introduced by <PERSON><PERSON><PERSON><PERSON> et al. (Crypto’22), is a PSI variant where <PERSON>’s input set\\(S_A\\)has a publicly known structure (for example, interval, ball or union of balls) and <PERSON>’s input\\(S_B\\)is an unstructured set of elements. Prior work achievessa-PSIwhere the communication cost only scales with the description size of\\(S_A\\)instead of the set cardinality. However, the computation cost remains linear in the cardinality of\\(S_A\\), which could be prohibitively large. In this work, we present a new semi-honestsa-PSIframework where both computation and communication costsonly scale with the description sizeof\\(S_A\\). Our main building block is a new primitive that we introduce called Incremental Boolean Function Secret Sharing (ibFSS), which is a generalization of FSS that additionally allows for evaluation on input prefixes. We formalize definitions and construct a weakibFSSfor ad-dimensional ball with\\(\\ell _\\infty \\)norm, which may be of independent interest. Independently, we improve spatial hashing techniques (from prior work) when\\(S_A\\)has structure union ofd-dimensional balls in\\((\\{0,1\\} ^u)^d\\), each of diameter\\(\\delta \\), from\\(\\mathcal {O} (u \\cdot d \\cdot (\\log \\delta )^d)\\)to\\(\\mathcal {O} (\\log \\delta \\cdot d)\\)in terms of both computation and communication. Finally, we resolve the following open questions from prior work with communication and computation scaling with the description size of the structured set. Our PSI framework can handle a union of overlapping structures, while prior work strictly requires a disjoint union. We have a new construction that enables Bob with unstructured input\\(S_B\\)to learn the intersection. We extend to a richer class of functionalities like structure-aware PSI Cardinality and PSI-Sum of associated values.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_10"}, {"primary_key": "558543", "vector": [], "sparse_vector": [], "title": "Solving the Tensor Isomorphism Problem for Special Orbits with Low Rank Points: Cryptanalysis and Repair of an Asiacrypt 2023 Commitment Scheme.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Tensor Isomorphism Problem (TIP) has been shown equivalent to the matrix code equivalence problem, making it an interesting candidate on which to build post-quantum cryptographic primitives. These hard problems have already been used in protocol development. One of these, MEDS, is currently in Round 1 of NIST’s call for additional post-quantum digital signatures. In this work, we consider the TIP restricted to the orbits of a special class of tensors. The hardness of the decisional version of this problem is the foundation of a commitment scheme proposed by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (Asiacrypt 2023). We present polynomial-time algorithms for the decisional and computational versions of TIP for special orbits, which implies that the commitment scheme is not secure. The key observations of these algorithms are that these special tensors contain some low-rank points, and their stabilizer groups are not trivial. With these new developments in the security of TIP in mind, we give a new commitment scheme based on the general TIP that is non-interactive, post-quantum, and statistically binding, making no new assumptions. Such a commitment scheme does not currently exist in the literature.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_5"}, {"primary_key": "558544", "vector": [], "sparse_vector": [], "title": "Towards Achieving Asynchronous MPC with Linear Communication and Optimal Resilience.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Secure multi-party computation (MPC) allows a set ofnparties to jointly compute a function over their private inputs. The seminal works of Ben-<PERSON>, Canetti and Goldreich [STOC ’93] and Ben<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> [PODC ’94] settled the feasibility of MPC over asynchronous networks. Despite the significant line of work devoted to improving the communication complexity, current protocols with information-theoretic security and optimal resilience\\(t<n/3\\)communicate\\(\\varOmega (n^4C)\\)field elements for a circuit withCmultiplication gates. In contrast, synchronous MPC protocols with\\(\\varOmega (nC)\\)communication have long been known. In this work we make progress towards closing this gap. We provide a novel MPC protocol in the asynchronous setting with statistical security that makes black-box use of an asynchronous complete secret-sharing (ACSS) protocol. The cost per multiplication reduces to the cost of distributing a constant number of sharings via ACSS, improving a linear factor over the state of the art by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [IEEE Trans. Inf. Theory ’17]. With a recent concurrent work achieving ACSS with linear cost per sharing, we achieve an MPC withO(nC) communication.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_6"}, {"primary_key": "558545", "vector": [], "sparse_vector": [], "title": "On Sequential Functions and Fine-Grained Cryptography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Asequential functionis, informally speaking, a functionffor which a massively parallel adversary cannot compute “substantially” faster than an honest user with limited parallel computation power. Sequential functions form the backbone of many primitives that are extensively used in blockchains such as verifiable delay functions (VDFs) and time-lock puzzles. Despite this widespread practical use, there has been little work studying the complexity or theory of sequential functions. Our main result is a black-box oracle separation between sequential functions and one-way functions: in particular, we show the existence of an oracle\\(\\mathcal {O}\\)that implies a sequential function but not a one-way function. This seems surprising since sequential functions are typically constructed from very strong assumptions that imply one-way functions and also since time-lock puzzles are known to imply one-way functions (Bitanskyet al., ITCS ’16). We continue our exploration of the theory of sequential functions. We show that, informally speaking, the decisional, worst-case variant of a certain class of sequential function called acontinuous iterativesequential function (CISF) is PSPACE-complete. A CISF is, in a nutshell, a sequential functionfthat can be written in the form\\(f \\left( k, x \\right) = g^{k} \\left( x \\right) \\)for some functiongwherekis an input determining the number of “rounds” the function is evaluated. We then show that more general forms of sequential functions are not contained in PSPACE relative to a random oracle. Given these results, we then ask if it is possible to build any interesting cryptographic primitives from sequential functions that are not one-way. It turns out that even if we assume just the existence of a CISF that is not one-way, we can build certain “fine-grained” cryptographic primitives where security is defined similarly to traditional primitives with the exception that it is only guaranteed for some (generally polynomial) amount of time. In particular, we show how to build “fine-grained” symmetric key encryption and “fine-grained” MACs from a CISF. We also show how to build fine-grained public-key encryption from a VDF with a few extra natural properties and indistinguishability obfuscation (iO) for null circuits. We do not assume one-way functions. Finally, we define a primitive that we call a commutative sequential function–essentially a sequential function that can be computed in sequence to get the same output in two different ways–and show that it implies fine-grained key exchange.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_14"}, {"primary_key": "558546", "vector": [], "sparse_vector": [], "title": "How to Construct Quantum FHE, Generically.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct a (compact) quantum fully homomorphic encryption (QFHE) scheme starting fromany(compact) classical fully homomorphic encryption scheme with decryption in\\(\\textsf{NC}^{1}\\), together with a dual-mode trapdoor function family. Compared to previous constructions (<PERSON><PERSON><PERSON>, FOCS 2018; <PERSON><PERSON><PERSON><PERSON>, CRYPTO 2018) which made non-black-box use of similar underlying primitives, our construction provides a pathway to instantiations from different assumptions. Our construction uses the techniques of <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (CRYPTO 2016) and shows how to make the client in their QFHE scheme classical using dual-mode trapdoor functions. As an additional contribution, we show a new instantiation of dual-mode trapdoor functions from group actions.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_8"}, {"primary_key": "558547", "vector": [], "sparse_vector": [], "title": "Revisiting Differential-Linear Attacks via a Boomerang Perspective with Application to AES, Ascon, CLEFIA, SKINNY, PRESENT, KNOT, TWINE, WARP, LBlock, Simeck, and SERPENT.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In 1994, <PERSON><PERSON> and <PERSON> introduced differential-linear (DL) cryptanalysis, with the idea of decomposing the block cipherEinto two parts,\\(E_{u}\\)and\\(E_{\\ell }\\), such that\\(E_{u}\\)exhibits a high-probability differential trail, while\\(E_{\\ell }\\)has a high-correlation linear trail. Combining these trails forms a distinguisher forE, assuming independence between\\(E_{u}\\)and\\(E_{\\ell }\\). The dependency between the two parts of DL distinguishers remained unaddressed until EUROCRYPT 2019, where <PERSON><PERSON><PERSON> et al. [3] introduced theDLCTframework, resolving the issue up to one S-box layer. However, extending theDLCTframework to formalize the dependency between the two parts for multiple rounds remained an open problem. In this paper, we first tackle this problem from the perspective of boomerang analysis. By examining the relationships betweenDLCT,DDT, andLAT, we introduce a set of new tables facilitating the formulation of dependencies between the two parts of the DL distinguisher across multiple rounds. Then, we introduce a highly versatile and easy-to-use automatic tool for exploring DL distinguishers, inspired by automatic tools for boomerang distinguishers. This tool considers the dependency between differential and linear trails across multiple rounds. We apply our tool to various symmetric-key primitives, and in all applications, we either present the first DL distinguishers or enhance the best-known ones. We achieve successful results againstAscon,AES,SERPENT,PRESENT,SKINNY,TWINE,CLEFIA,WARP,LBlock,Simeck, andKNOT. Furthermore, we demonstrate that, in some cases, DL distinguishers outperform boomerang distinguishers significantly.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_2"}, {"primary_key": "558548", "vector": [], "sparse_vector": [], "title": "FRIDA: Data Availability Sampling from FRI.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As blockchains like Ethereum continue to grow, clients with limited resources can no longer store the entire chain. Light nodes that want to use the blockchain, without verifying that it is in a good state overall, can just download the block headers without the corresponding block contents. As those light nodes may eventually need some of the block contents, they would like to ensure that they are in principle available. Data availability sampling, introduced by <PERSON> et al., is a process that allows light nodes to check the availability of data without download it. In a recent effort, <PERSON><PERSON>, <PERSON>, and <PERSON> have introduced formal definitions and analyzed several constructions. While their work thoroughly lays the formal foundations for data availability sampling, the constructions are either prohibitively expensive, use a trusted setup, or have a download complexity for light clients scales with a square root of the data size. In this work, we make a significant step forward by proposing an efficient data availability sampling scheme without a trusted setup and only polylogarithmic overhead. To this end, we find a novel connection with interactive oracle proofs of proximity (IOPPs). Specifically, we prove that any IOPP meeting an additional consistency criterion can be turned into an erasure code commitment, and then, leveraging a compiler due to <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, into a data availability sampling scheme. This new connection enables data availability to benefit from future results on IOPPs. We then show that the widely used FRI IOPP satisfies our consistency criterion and demonstrate that the resulting data availability sampling scheme outperforms the state-of-the-art asymptotically and concretely in multiple parameters.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_9"}, {"primary_key": "558549", "vector": [], "sparse_vector": [], "title": "Quantum Complexity for Discrete Logarithms and Related Problems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies the quantum computational complexity of the discrete logarithm (DL) and related group-theoretic problems in the context of “generic algorithms”—that is, algorithms that do not exploit any properties of the group encoding. We establish the quantum generic group model and hybrid classical-quantum generic group model as quantum and hybrid analogs of their classical counterpart. This model counts the number of group operations of the underlying cyclic group\\(\\mathcal {G}\\)as a complexity measure. <PERSON><PERSON>’s algorithm for the discrete logarithm problem and related algorithms can be described in this model and make\\(O(\\log |\\mathcal {G}|)\\)group operations in their basic form. We show the quantum complexity lower bounds and (almost) matching algorithms of the discrete logarithm and related problems in these models. We prove that any quantum DL algorithm in the quantum generic group model must make\\(\\varOmega (\\log |\\mathcal G|)\\)depth of group operation queries. This shows that <PERSON><PERSON>’s algorithm that makes\\(O(\\log |\\mathcal G|)\\)group operations is asymptotically optimal among the generic quantum algorithms, even considering parallel algorithms. We observe that some (known) variations of <PERSON><PERSON>’s algorithm can take advantage of classical computations to reduce the number and depth of quantum group operations. We show that these variants are optimal among generic hybrid algorithms up to constant multiplicative factors: Any generic hybrid quantum-classical DL algorithm with a total number of (classical or quantum) group operationsQmust make\\(\\varOmega (\\log |\\mathcal G|/\\log Q)\\)quantum group operations of depth\\(\\varOmega (\\log \\log |\\mathcal G| - \\log \\log Q)\\). When the quantum memory can only storetgroup elements and use quantum random access classical memory (QRACM) ofrgroup elements, any generic hybrid quantum-classical algorithm must make either\\(\\varOmega (\\sqrt{|\\mathcal G|})\\)group operation queries in total or\\(\\varOmega (\\log |\\mathcal G|/\\log (tr))\\)quantum group operation queries. In particular, classical queries cannot reduce the number of quantum queries beyond\\(\\varOmega (\\log |\\mathcal G|/\\log (tr))\\). As a side contribution, we show a multiple discrete logarithm problem admits a better algorithm than solving each instance one by one, refuting a strong form of the quantum annoying property suggested in the context of password-authenticated key exchange protocol.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_1"}, {"primary_key": "558550", "vector": [], "sparse_vector": [], "title": "FuLeakage: Breaking FuLeeca by Learning Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> P<PERSON>"], "summary": "FuLeecais a signature scheme submitted to the recent NIST call for additional signatures. It is an efficient hash-and-sign scheme based on quasi-cyclic codes in the Lee metric and resembles the lattice-based signatureFalcon.Fu<PERSON>eecaproposes a so-called concentration step within the signing procedure to avoid leakage of secret-key information from the signatures. However,FuLeecais still vulnerable to learning attacks, which were first observed for lattice-based schemes. We present three full key-recovery attacks by exploiting the proximity of thecode-basedFuLeecascheme tolattice-based primitives. More precisely, we use a few signatures to extract ann/2-dimensional circulant sublattice from the given length-ncode, that still contains the exceptionally short secret-key vector. This significantly reduces the classical attack cost and, in addition, leads to a full key recovery in quantum-polynomial time. Furthermore, we exploit a bias in the concentration procedure to classically recover the full key for any security level with at most 175,000 signatures in less than an hour.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_8"}, {"primary_key": "558551", "vector": [], "sparse_vector": [], "title": "On Round Elimination for Special-Sound Multi-round Identification and the Generality of the Hypercube for MPCitH.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A popular way to build post-quantum signature schemes is by first constructing an identification scheme (IDS) and applying the Fiat-<PERSON>hamir transform to it. In this work we tackle two open questions related to the general applicability of techniques around this approach that together allow for efficient post-quantum signatures with optimal security bounds in the QROM. First, we consider a recent work by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (Asiacrypt’23) that showed that an optimal bound for three-round commit & open IDS by <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (Crypto’22) can be applied to the five-round Syndrome-Decoding in the Head (SDitH) IDS. For this, they first applied a transform that replaced the first three rounds by one. They left it as an open problem if the same approach applies to other schemes beyond SDitH. We answer this question in the affirmative, generalizing their round-elimination technique and giving a generic security proof for it. Our result applies to any IDS with\\(2n+1\\)rounds for\\(n>1\\). However, a scheme has to be suitable for the resulting bound to not be trivial. We find that IDS are suitable when they have a certain form of special-soundness which many commit & open IDS have. Second, we consider the hypercube technique by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> (Eurocrypt’23). An optimization that was proposed in the context of SDitH and is now used by several of the contenders in the NIST signature on-ramp. It was conjectured that the technique applies generically for the MPC-in-the-Head (MPCitH) technique that is used in the design of many post-quantum IDS if they use an additive secret sharing scheme but this was never proven. In this work we show that the technique generalizes to MPCitH IDS that use an additively homomorphic MPC protocol, and we prove that security is preserved. We demonstrate the application of our results to the identification scheme of RYDE, a contender in the recent NIST signature on-ramp. While RYDE was already specified with the hypercube technique applied, this gives the first QROM proof for RYDE with an optimally tight bound.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_12"}, {"primary_key": "558552", "vector": [], "sparse_vector": [], "title": "MPC in the Head Using the Subfield Bilinear Collision Problem.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we introduce the subfield bilinear collision problem and use it to construct an identification protocol and a signature scheme. This construction is based on the MPC-in-the-head paradigm and uses the Fiat-Shamir transformation to obtain a signature.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_2"}, {"primary_key": "558553", "vector": [], "sparse_vector": [], "title": "Concretely Efficient Lattice-Based Polynomial Commitment from Standard Assumptions.", "authors": ["In<PERSON>k <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Polynomial commitment is a crucial cryptographic primitive in constructing zkSNARKs. Most practical constructions to date are either vulnerable against quantum adversaries or lack homomorphic properties, which are essential for recursive proof composition and proof batching. Recently, lattice-based constructions have drawn attention for their potential to achieve all the desirable properties, though they often suffer from concrete inefficiency or rely on newly introduced assumptions requiring further cryptanalysis. In this paper, we propose a novel construction of a polynomial commitment scheme based on standard lattice-based assumptions. Our scheme achieves a square-root proof size and verification complexity, ensuring concrete efficiency in proof size, proof generation, and verification. Additionally, it features a transparent setup and publicly verifiability. When compared with Brakedown (CRYPTO 2023), a recent code-based construction, our scheme offers comparable performance across all metrics. Furthermore, its proof size is approximately 4.1 times smaller than SLAP (EUROCRYPT 2024), a recent lattice-based construction.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_13"}, {"primary_key": "558554", "vector": [], "sparse_vector": [], "title": "PIR with Client-Side Preprocessing: Information-Theoretic Constructions and Lower Bounds.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is well-known that classical Private Information Retrieval (PIR) schemes without preprocessing must suffer from linear server computation per query. Moreover, any such single-server PIR with sublinear bandwidth must rely on public-key cryptography. Several recent works showed that these barriers pertaining to classical PIR can be overcome by introducing a preprocessing phase where each client downloads a small hint that helps it make queries subsequently. Notably, the Piano PIR scheme (and subsequent improvements) showed that with such a client-side preprocessing, not only can we have PIR with sublinear computation and bandwidth per query, but somewhat surprisingly, we can also get it using only symmetric-key cryptography (i.e., one-way functions). In this paper, we take the question of minimizing cryptographic assumptions to an extreme. In particular, we are the first to explore the landscape ofinformation theoreticsingle-server preprocessing PIR. We make contributions on both the upper- and lower-bounds fronts. First, we show new information-theoretic constructions with various non-trivial performance tradeoffs between server computation, client space and bandwidth. Second, we prove a (nearly) tight lower bound on the tradeoff between the client space and bandwidth in information-theoretic constructions. Finally, we prove that any computational scheme that overcomes the information-theoretic lower bound and satisfies a natural syntactic requirement (which is met by all known constructions) would imply a hard problem in the complexity class SZK. In particular, this shows that <PERSON> achieves (nearly) optimal client space and bandwidth tradeoff subject to making a black-box use of a one-way function. Some of the techniques we use for the above results can be of independent interest.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_5"}, {"primary_key": "558555", "vector": [], "sparse_vector": [], "title": "Generic and Algebraic Computation Models: When AGM Proofs Transfer to the GGM.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (<PERSON><PERSON><PERSON> 2018) claim that (some) hardness results in the algebraic group model imply the same hardness results in the generic group model was recently called into question by <PERSON>, <PERSON>, and <PERSON> (Asiacrypt 2022). The latter gave an interpretation of the claim under which it is incorrect. We give an alternate interpretation under which it is correct, using natural frameworks for capturing generic and algebraic models for arbitrary algebraic structures. Most algebraic analyses in the literature can be captured by our frameworks, making the claim correct for them.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_2"}, {"primary_key": "558556", "vector": [], "sparse_vector": [], "title": "A Systematic Study of Sparse LWE.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we introduce the sparse LWE assumption, an assumption that draws inspiration from both Learning with E<PERSON><PERSON> (Regev JACM 10) and Sparse Learning Parity with Noise (<PERSON><PERSON><PERSON>ovich FOCS 02). Exactly like LWE, this assumption posits indistinguishability of\\((\\textbf{A}, \\textbf{s}\\textbf{A}+\\textbf{e} \\mod p)\\)from\\((\\textbf{A}, \\textbf{u})\\)for a random\\(\\textbf{u}\\)where the secret\\(\\textbf{s}\\), and the error vector\\(\\textbf{e}\\)is generated exactly as in LWE. However, the coefficient matrix\\(\\textbf{A}\\)in sparse LPN is chosen randomly from\\(\\ensuremath {\\mathbb {Z}}^{n\\times m}_{p}\\)so that each column has Hamming weight exactlykfor some smallk. We study the problem in the regime wherekis a constant or polylogarithmic. The primary motivation for proposing this assumption is efficiency. Compared to LWE, the samples can be computed and stored with roughlyO(n/k) factor improvement in efficiency. Our results can be summarized as: Foundations:We show several properties of sparse LWE samples, including: 1) The hardness of LWE/LPN with dimensionkimplies the hardness of sparse LWE/LPN with sparsitykand arbitrary dimension\\(n \\ge k\\). 2) When the number of samples\\(m=\\varOmega (n \\log p)\\), length of the shortest vector of a lattice spanned by rows of a random sparse matrix is large, close to that of a random dense matrix of the same dimension (up to a small constant factor). 3) Trapdoors with small polynomial norm exist for random sparse matrices with dimension\\(n \\times m = O(n \\log p)\\). 4) Efficient algorithms for sampling such matrices together with trapdoors exist when the dimension is\\(n \\times m = \\ensuremath {\\widetilde{\\mathcal {O}}}(n^2)\\). Cryptanalysis:We examine the suite of algorithms that have been used to break LWE and sparse LPN. While naively many of the attacks that apply to LWE do not exploit sparsity, we consider natural extensions that make use of sparsity. We propose a model to capture all these attacks. Using this model we suggest heuristics on how to identify concrete parameters. Our initial cryptanalysis suggests that concretely sparse LWE with a modestkand slightly bigger dimension than LWE will satisfy similar level of security as LWE with similar parameters. Applications:We show that the hardness of sparse LWE implies very efficient homomorphic encryption schemes for low degree computations. We obtain the first secret key Linearly Homomorphic Encryption (LHE) schemes withslightly super-constant, or evenconstant, overhead, which further has applications to private information retrieval, private set intersection, etc. We also obtain secret key homomorphic encryption for arbitrary constant-degree polynomials with slightly super-constant, or constant, overhead. We stress that our results are preliminary. However, our results make a strong case for further investigation of sparse LWE.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_7"}, {"primary_key": "558557", "vector": [], "sparse_vector": [], "title": "Linear-Communication Asynchronous Complete Secret Sharing with Optimal Resilience.", "authors": ["Xiaoyu Ji", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Secure multiparty computation (MPC) allows a set ofnparties to jointly compute a function on their private inputs. In this work, we focus on the information-theoretic MPC in theasynchronous networksetting with optimal resilience (\\(t<n/3\\)). The best-known result in this setting is achieved by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [<PERSON><PERSON> Cryptol ’23], which requires\\(O(n^4\\kappa )\\)bits per multiplication gate, where\\(\\kappa \\)is the size of a field element. An asynchronous complete secret sharing (ACSS) protocol allows a dealer to share a batch of Shamir sharings such that all parties eventually receive their shares. ACSS is an important building block in AMPC. The best-known result of ACSS is due to <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [J. Cryptol ’23], which requires\\(O(n^3\\kappa )\\)bits per sharing. On the other hand, in the synchronous setting, it is known that distributing Shamir sharings can be achieved with\\(O(n\\kappa )\\)bits per sharing. There is a gap of\\(n^2\\)in the communication between the synchronous setting and the asynchronous setting. Our work closes this gap by presenting the first ACSS protocol that achieves\\(O(n\\kappa )\\)bits per sharing. When combined with the compiler from ACSS to AMPC by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [IEEE Trans. Inf. Theory ’17], we obtain an AMPC with\\(O(n^2\\kappa )\\)bits per multiplication gate, improving the previously best-known result by a factor of\\(n^2\\). Moreover, with a concurrent work that improves the compiler by Choudhury and Patra by a factor ofn, we obtain the first AMPC with\\(O(n\\kappa )\\)bits per multiplication gate.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_13"}, {"primary_key": "558558", "vector": [], "sparse_vector": [], "title": "Pairing-Free Blind Signatures from Standard Assumptions in the ROM.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Blind Signatures are a useful primitive for privacy preserving applications such as electronic payments, e-voting, anonymous credentials, and more. However, existing practical blind signature schemes based on standard assumptions require either pairings or lattices. We present the first practical construction of a round-optimal blind signature in the random oracle model based on standard assumptions without resorting to pairings or lattices. In particular, our construction is secure under the strong RSA assumption and DDH (in pairing-free groups). For our construction, we provide a NIZK-friendly signature based on strong RSA, and efficiently instantiate a variant of <PERSON>sch<PERSON>’s generic framework (CRYPTO’06). Our Blind Signature scheme has signatures of size\\(4.28\\)KB and communication cost\\(10.98\\)KB. On the way, we develop techniques that might be of independent interest. In particular, we provide efficientrelaxedrange-proofs for large ranges with subversion zero-knowledge and compact commitments to elements of arbitrary groups.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_7"}, {"primary_key": "558559", "vector": [], "sparse_vector": [], "title": "Adaptively Secure 5 Round Threshold Signatures from MLWE/MSIS and DL with Rewinding.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "T-out-of-Nthreshold signatures have recently seen a renewed interest, with various types now available, each offering different tradeoffs. However, one property that has remained elusive isadaptivesecurity. When we target thresholdizing existing efficient signatures schemes based on the Fiat-Shamir paradigm such as Schnorr, the elusive nature becomes clear. This class of signature schemes typically rely on the forking lemma to prove unforgeability. That is, an adversary isrewound and run twicewithin the security game. Such a proof is at odds with adaptive security, as the reduction must be ready to answer\\(2(T - 1)\\)secret key shares in total, implying that it can reconstruct the full secret key. Indeed, prior works either assumed strong idealized models such as the algebraic group model (AGM) or modified the underlying signature scheme so as not to rely on rewinding based proofs. In this work, we propose a new proof technique to construct adaptively secure threshold signatures for existing rewinding-based Fiat-Shamir signatures. As a result, we obtain the following: The first adaptively secure 5 round lattice-based threshold signature under the\\(\\textsf{MLWE} \\)and\\(\\textsf{MSIS} \\)assumptions in the ROM. The resulting signature is a standard signature of\\(\\textsf{Raccoon}\\), a lattice-based signature scheme by <PERSON> et al., submitted to the additional NIST call for proposals. The first adaptively secure 5 round threshold signature under the\\(\\textsf{DL} \\)assumption in the ROM. The resulting signature is a standard Schnorr signature. To the best of our knowledge, this is the first adaptively secure threshold signature based on\\(\\textsf{DL} \\)even assuming stronger models like AGM. Our work is inspired by the recent statically secure lattice-based 3 round threshold signature by del Pino et al. (Eurocrypt 2024) based on\\(\\textsf{Raccoon}\\). While they relied on so-called one-time additive masks to solve lattice specific issues, we notice that these masks can also be a useful tool to achieve adaptive security. At a very high level, we use these masks throughout the signing protocol to carefully control the information the adversary can learn from the signing transcripts. Intuitively, this allows the reduction to return a total of\\(2(T-1)\\)randomly sampledsecret key shares to the adversary consistently and without being detected, resolving the above paradoxical situation. Lastly, by allowing the parties to maintain a simple state, we can compress our 5 round schemes into 4 rounds.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_15"}, {"primary_key": "558560", "vector": [], "sparse_vector": [], "title": "Round-Opti<PERSON>, <PERSON><PERSON> Secure Distributed Key Generation.", "authors": ["<PERSON>"], "summary": "Protocols for distributed (threshold) key generation (DKG) in the discrete-logarithm setting have received a tremendous amount of attention in the past few years. Several synchronous DKG protocols have been proposed, but most such protocols are notfully secure: they either allow corrupted parties tobiasthe key, or are notrobustand allow malicious parties to prevent successful generation of a key. We explore the round complexity of fully secure DKG in the honest-majority setting where it is feasible. We show the impossibility of one-round, statistically unbiased DKG protocols (even if not robust), regardless of any prior setup. On the positive side, we show various round-optimal protocols for fully secure DKG offering tradeoffs in terms of their efficiency, necessary setup, and required assumptions.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_10"}, {"primary_key": "558561", "vector": [], "sparse_vector": [], "title": "LATKE: A Framework for Constructing Identity-Binding PAKEs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Motivated by applications to the internet of things (IoT), Cremers, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> (CRYPTO ’22) recently considered a setting in which multiple parties share a common password and want to be able to pairwise authenticate. They observed that using standard password-authenticated key exchange (PAKE) protocols in this setting allows forcatastrophic impersonationattacks whereby compromise of a single party allows an attacker to impersonate any party to any other. To address this, they proposed the notion ofidentity-binding PAKE(iPAKE) and showed constructions of iPAKE protocol CHIP. We present LATKE, a framework for iPAKE that allows us to construct protocols with features beyond what CHIP achieves. In particular, we can instantiate the components of our framework to yield an iPAKE protocol with post-quantum security andidentity concealment, where one party hides its identity until it has authenticated the other. This is the first iPAKE protocol with either property. To demonstrate the concrete efficiency of our framework, we implement various instantiations and compare the resulting protocols to CHIP when run on commodity hardware. The performance of our schemes is very close to that of CHIP, while offering stronger security properties.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_7"}, {"primary_key": "558562", "vector": [], "sparse_vector": [], "title": "Exploring the Advantages and Challenges of Fermat NTT in FHE Acceleration.", "authors": ["<PERSON><PERSON>", "Ahmet Can Mert", "<PERSON><PERSON><PERSON>", "Aikata", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recognizing the importance of a fast and resource-efficient polynomial multiplication in homomorphic encryption, in this paper, we design amultiplier-lessnumber theoretic transform using a Fermat number as an auxiliary modulus. To make this algorithm scalable with the degree of polynomial, we apply a univariate to multivariate polynomial ring transformation. We develop an accelerator architecture for fully homomorphic encryption using these algorithmic techniques for efficient multivariate polynomial multiplication. For practical homomorphic encryption application benchmarks, the hardware accelerator achieves a 1,200\\(\\times \\)speed-up compared to software implementations. Finally, we conclude the paper by discussing the advantages and limitations of the proposed polynomial multiplication method.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_3"}, {"primary_key": "558563", "vector": [], "sparse_vector": [], "title": "Quantum Public-Key Encryption with Tamper-Resilient Public Keys from One-Way Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct quantum public-key encryption from one-way functions. In our construction, public keys are quantum, but ciphertexts are classical. Quantum public-key encryption from one-way functions (or weaker primitives such as pseudorandom function-like states) are also proposed in some recent works [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eprint:2022/1336; <PERSON><PERSON>gel<PERSON>, eprint:2023/282; Barooti-Grilo-Mal<PERSON>olta-<PERSON>, TCC 2023]. However, they have a huge drawback: they are secure only when quantum public keys can be transmitted to the sender (who runs the encryption algorithm) without being tampered with by the adversary, which seems to require unsatisfactory physical setup assumptions such as secure quantum channels. Our construction is free from such a drawback: it guarantees the secrecy of the encrypted messages even if we assume only unauthenticated quantum channels. Thus, the encryption is done with adversarially tampered quantum public keys. Our construction is the first quantum public-key encryption that achieves the goal of classical public-key encryption, namely, to establish secure communication over insecure channels, based only on one-way functions. Moreover, we show a generic compiler to upgrade security against chosen plaintext attacks (CPA security) into security against chosen ciphertext attacks (CCA security) only using one-way functions. As a result, we obtain CCA secure quantum public-key encryption based only on one-way functions.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_4"}, {"primary_key": "558564", "vector": [], "sparse_vector": [], "title": "Resettable Statistical Zero-Knowledge for $\\ensuremath {\\textsf{NP}}$.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Resettable statistical zero-knowledge [<PERSON><PERSON><PERSON><PERSON>, TCC 2012] is a strong privacy notion that guarantees statistical zero-knowledge even when the prover uses the same randomness in multiple proofs. In this paper, we show an equivalence of resettable statistical zero-knowledge arguments for\\(\\ensuremath {\\textsf{NP}}\\)and witness encryption schemes for\\(\\ensuremath {\\textsf{NP}}\\). Positive result: For any\\(\\ensuremath {\\textsf{NP}}\\)language\\({\\textbf {L}}\\), a resettable statistical zero-knowledge argument for\\({\\textbf {L}}\\)can be constructed from a witness encryption scheme for\\({\\textbf {L}}\\)under the assumption of the existence of one-way functions. Negative result: The existence of even resettable statistical witness-indistinguishable arguments for\\(\\ensuremath {\\textsf{NP}}\\)imply the existence of witness encryption schemes for\\(\\ensuremath {\\textsf{NP}}\\)under the assumption of the existence of one-way functions. The positive result is obtained by naturally extending existing techniques (and is likely to be already well-known among experts). The negative result is our main technical contribution. To explore workarounds for the negative result, we also consider resettable security in a model where the honest party’s randomness is only reused with fixed inputs. We show that resettable statistically hiding commitment schemes are impossible even in this model.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_9"}, {"primary_key": "558565", "vector": [], "sparse_vector": [], "title": "HyperNova: Recursive Arguments for Customizable Constraint Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduceHyperNova, a new recursive argument for proving incremental computations whose steps are expressed with CCS (<PERSON><PERSON> et al. ePrint 2023/552), a customizable constraint system that simultaneously generalizes Plonkish, R1CS, and AIR without overheads. HyperNova makes four contributions, each resolving a major problem in the area of recursive arguments. First, it provides a folding scheme for CCS where the prover’s cryptographic cost is asinglemulti-scalar multiplication (MSM) of size equal to the number of variables in the constraint system, which is optimal when using an MSM-based commitment scheme. The folding scheme can fold multiple instances at once, making it easier to build generalizations of IVC such as PCD. Second, when proving program executions on stateful machines (e.g., EVM, RISC-V), the cost of proving a step of a program is proportional only to the size of the circuit representing the instruction invoked by the program step (“a la carte” cost profile). Third, we show how to achieve zero-knowledge for “free” andwithoutthe need to employzero-knowledgeSNARKs: we use a folding scheme to “randomize” IVC proofs. This highlights a new application of folding schemes. Fourth, we show how to efficiently instantiate H<PERSON>Nova over a cycle of elliptic curves. For this, we provide a general technique, which we refer to as CycleFold, that applies to all modern folding-scheme-based recursive arguments.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_11"}, {"primary_key": "558566", "vector": [], "sparse_vector": [], "title": "Algebraic Structure of the Iterates of χ.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the map\\(\\chi :\\mathbb {F}_2^n\\rightarrow \\mathbb {F}_2^n\\)fornodd given by\\(y=\\chi (x)\\)with\\(y_i=x_i+x_{i+2}(1+x_{i+1})\\), where the indices are computed modulon. We suggest a generalization of the map\\(\\chi \\)which we call generalized\\(\\chi \\)-maps. We show that these maps form an Abelian group which is isomorphic to the group of units in\\(\\mathbb {F}_2[X]/(X^{(n+1)/2})\\). Using this isomorphism we easily obtain closed-form expressions for iterates of\\(\\chi \\)and explain their properties.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_13"}, {"primary_key": "558567", "vector": [], "sparse_vector": [], "title": "HAWKEYE - Recovering Symmetric Cryptography From Hardware Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present the first comprehensive approach for detecting and analyzing symmetric cryptographic primitives in gate-level descriptions of hardware. To capture both ASICs and FPGAs, we model the hardware as a directed graph, where gates become nodes and wires become edges. For modern chips, those graphs can easily consist of hundreds of thousands of nodes. More abstractly, we find subgraphs corresponding to cryptographic primitives in a potentially huge graph, thesea-of-gates, describing an entire chip. As we are particularly interested in unknown cryptographic algorithms, we cannot rely on searching for known parts such as S-boxes or round constants. Instead, we are looking for parts of the chip thatperform highly local computations. A major result of our work is that many symmetric algorithms can be reliably located and sometimes even identified by our approach, which we callHAWKEYE. Our findings are verified by extensive experimental results, which involve SPN, ARX, Feistel, and LFSR-based ciphers implemented for both FPGAs and ASICs. We demonstrate the real-world applicability ofHAWKEYEby evaluating it on OpenTitan’s Earl Grey chip, an open-source secure micro-controller design.HAWKEYElocates all major cryptographic primitives present in the netlist comprising 424 341 gates in 44.3 s.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_11"}, {"primary_key": "558568", "vector": [], "sparse_vector": [], "title": "How (not) to Build Quantum PKE in Minicrypt.", "authors": ["Longcheng Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The seminal work by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC’89) demonstrated the impossibility of constructing classical public key encryption (PKE) from one-way functions (OWF) in a black-box manner. Quantum information has the potential to bypass classical limitations, enabling the realization of seemingly impossible tasks such as quantum money, copy protection for software, and commitment without one-way functions. However, the question remains: can quantum PKE (QPKE) be constructed from quantumly secure OWF? A recent line of work has shown that it is indeed possible to build QPKE from OWF, but with one caveat. These constructions necessitate public keys being quantum and unclonable, diminishing the practicality of such “public” encryption schemes—public keys cannot be authenticated and reused. In this work, we re-examine the possibility of perfect complete QPKE in the quantum random oracle model (QROM), where OWF exists. Our first main result: QPKE with classical public keys, secret keys and ciphertext, does not exist in the QROM, if the key generation only makes classical queries. Therefore, a necessary condition for constructing such QPKE from OWF is to have the key generation classically “un-simulatable”. Previous results (<PERSON><PERSON><PERSON> et al. CRYPTO’22) on the impossibility of QPKE from OWF rely on a seemingly strong conjecture. Our work makes a significant step towards a complete and unconditional quantization of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>’s results. Our second main result extends to QPKE with quantum public keys. The second main result: QPKE with quantum public keys, classical secret keys and ciphertext, does not exist in the QROM, if the key generation only makes classical queries and the quantum public key is either pure or “efficiently clonable”. The result is tight due to these existing QPKEs with quantum public keys, classical secret keys, quantum/classical ciphertext and classical-query key generation require the public key to be mixed instead of pure; or require quantum-query key generation, if the public key is pure. Our result further gives evidence on why existing QPKEs lose reusability. We also explore other sufficient/necessary conditions to build QPKE from OWF. Along the way, we use a new argument based on conditional mutual information and Markov chain to reprove the classical result; leveraging the analog of quantum conditional mutual information and quantum Markov chain by Fawzi and Renner (Communications in Mathematical Physics), we extend it to the quantum case and prove all our results. We believe the techniques used in the work will find many other usefulness in separations in quantum cryptography/complexity.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_6"}, {"primary_key": "558569", "vector": [], "sparse_vector": [], "title": "Hintless Single-Server Private Information Retrieval.", "authors": ["Baiyu Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present two new constructions for private information retrieval (PIR) in the classical setting where the clients do not need to do any preprocessing or store any database dependent information, and the server does not need to store any client-dependent information. Our first construction (\\({\\textsf{Hintless}\\textsf{PIR}}\\)) eliminates the client preprocessing step from the recent LWE-based SimplePIR (<PERSON><PERSON><PERSON> et al., USENIX Security 2023) by outsourcing the “hint” related computation to the server, leveraging a new concept ofhomomorphic encryption with composable preprocessing. We realize this concept with RLWE encryption schemes, and by leveraging the composibility of this technique we are able to preprocess almost all the expensive parts of the homomorphic computation and reuse them across multiple protocol executions. As a concrete application, we propose highly efficient matrix vector multiplication that allows us to build\\({\\textsf{Hintless}\\textsf{PIR}}\\). For a database of size 8 GB,\\({\\textsf{Hintless}\\textsf{PIR}}\\)achieves throughput about 6.37 GB/s without requiring transmission of any client or server state. We additionally formalize the matrix vector multiplication protocol as a novel primitive that we call\\(\\textsf{LinPIR}\\), which may be of independent interest. In our second construction (\\(\\textsf{Tensor}\\textsf{PIR}\\)) we reduce the communication of\\({\\textsf{Hintless}\\textsf{PIR}}\\)from square root to cubic root in the database size. We show how to use RLWE encryption with preprocessing to outsource LWE decryption for ciphertexts generated by homomorphic multiplications. This allows the server to do more complex processing using a more compact query under LWE. We implement and benchmark\\({\\textsf{Hintless}\\textsf{PIR}}\\)which achieves better concrete costs than\\(\\textsf{Tensor}\\textsf{PIR}\\)for a large set of databases of interest. We show that it improves the communication of recent preprocessing constructions when clients do not have large numbers of queries or the database updates frequently. The computation cost for removing the hint is small and decreases as the database becomes larger, and it is always more efficient than other constructions with client hints such as Spiral PIR (Menon and Wu, S&P 2022). In the setting of anonymous queries we also improve on Spiral’s communication.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_6"}, {"primary_key": "558570", "vector": [], "sparse_vector": [], "title": "Doubly Efficient Cryptography: Commitments, Arguments and RAM MPC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Can a sender commit to a long input without even reading all of it? Can a prover convince a verifier that an NP statement holds without even reading the entire witness? Can a set of parties run a multiparty computation (MPC) protocol in the RAM model, without necessarily even reading their entire inputs? We show how to construct such “doubly efficient” schemes in a setting where parties can preprocess their input offline, but subsequently they can engage in many different protocol executions over this input in sublinear online time. We do so in the plain model, without any common setup. Our constructions rely ondoubly efficient private information retrieval (DEPIR)as a building block and can be instantiated based on Ring LWE. In more detail, we begin by constructingdoubly efficient (interactive) commitments, where the sender preprocesses the input offline, and can later commit to this input to arbitrary receivers in sublinear online time. Moreover, the sender can open individual bits of the committed input in sublinear time. We then use these commitments to implementdoubly succinct (interactive) arguments, where the prover preprocesses the statement/witness offline, and can subsequently run many proof protocoils to convince arbitrary verifiers of the statement’s validity in sublinear online time. Furthermore, we augment these to get adoubly efficient “commit, prove and locally open” protocol, where the prover can commit to a long preprocessed input, prove that it satisfies some global property, and locally open individual bits, all in sublinear time. Finally, we leverage these tools to construct aRAM-MPCwith malicious security in the plain model. Each party individually preprocesses its input offline, and can then run arbitrary MPC executions over this input with arbitrary other parties. The online run-time of each MPC execution is only proportional to the RAM run-time of the underlying program, that can be sublinear in the input size.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_8"}, {"primary_key": "558571", "vector": [], "sparse_vector": [], "title": "More Efficient Zero-Knowledge Protocols over $\\mathbb {Z}_{2k}$ via Galois Rings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yizhou Yao"], "summary": "A recent line of works on zero-knowledge (ZK) protocols with a vector oblivious linear function evaluation (VOLE)-based offline phase provides a new paradigm for scalable ZK protocols featuring fast proving and small prover memory. Very recently, <PERSON><PERSON> et al. (Crypto’23) proposed the VOLE-in-the-head technique, allowing such protocols to become publicly verifiable. Many practically efficient protocols for proving circuit satisfiability over any Galois field are implemented, while protocols over rings\\(\\mathbb {Z}_{2^k}\\)are significantly lagging behind, with only a proof-of-concept pioneering work called <PERSON><PERSON>nzeller to Brie (CCS’21) and a first proposal called Moz\\(\\mathbb {Z}_{2^k}\\)arella (Crypto’22). The ring\\(\\mathbb {Z}_{2^{32}}\\)or\\(\\mathbb {Z}_{2^{64}}\\), though highly important (it captures computation in real-life programming and the computer architectures such as CPU words), presents non-trivial difficulties because, for example, unlike Galois fields\\(\\mathbb {F}_{2^{k}}\\), the fraction of units in\\(\\mathbb {Z}_{2^{k}}\\)is 1/2. In this work, we first construct ZK protocols over a high degree Galois ring extension of\\(\\mathbb {Z}_{2^{k}}\\)(fraction of units close to 1) and then convert them to\\(\\mathbb {Z}_{2^k}\\)efficiently using amortization techniques. Our results greatly change the landscape of ZK protocols over\\(\\mathbb {Z}_{2^k}\\). We propose a competing ZK protocol that has many advantages over the state-of-the-art Moz\\(\\mathbb {Z}_{2^k}\\)arella. We remove the undesirable dependence of communication complexity on the security parameter, and achieve communication complexitystrictlylinear in the circuit size. Furthermore, our protocol has better concrete efficiency. For 40, 80 bits soundness on circuits over\\(\\mathbb {Z}_{2^{32}}\\)and\\(\\mathbb {Z}_{2^{64}}\\), we offer\\(1.15\\times \\)–\\(2.9\\times \\)improvements in communication. Inspired by the recently proposed interactive message authentication code technique (Weng et al., CCS’22), we construct a constant round ZK protocol over\\(\\mathbb {Z}_{2^k}\\)with sublinear (in the circuit size) communication complexity, which was previously achieved only over fields. We show that the pseudorandom correlation generator approach can be adapted to efficiently implement VOLE over Galois rings, with analysis of the hardness of underlying LPN assumptions over Galois rings. We adapt the VOLE-in-the-head technique to make it work for\\(\\mathbb {Z}_{2^k}\\), yieldingpublicly verifiablenon-interactive ZK protocols over\\(\\mathbb {Z}_{2^k}\\)which preserve most of the efficiency metrics of the VOLE-based ZK protocols.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_13"}, {"primary_key": "558572", "vector": [], "sparse_vector": [], "title": "Polymath: Groth16 Is Not the Limit.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Shortening the argument (three group elements or 1536 / 3072 bits over the BLS12-381/BLS24-509 curves) of the Groth16 zk-SNARK for R1CS is a long-standing open problem. We propose a zk-SNARK Polymath for the Square Arithmetic Programming constraint system using the KZG polynomial commitment scheme. <PERSON>ymath has a shorter argument (1408 / 1792 bits over the same curves) than Groth16. At 192-bit security, <PERSON><PERSON><PERSON>’s argument is nearly half the size, making it highly competitive for high-security future applications. Notably, we handle public inputs in a simple way. We optimized <PERSON><PERSON><PERSON>’s prover through an exhaustive parameter search. <PERSON><PERSON><PERSON>’s prover does not output\\(\\mathbb {G}_{2}\\)elements, aiding in batch verification, SNARK aggregation, and recursion. <PERSON><PERSON>ath’s properties make it highly suitable to be the final SNARK in SNARK compositions.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_6"}, {"primary_key": "558573", "vector": [], "sparse_vector": [], "title": "Memory-Sample Lower Bounds for LWE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we show memory-sample lower bounds for the fundamental problem of learning with error (LWE). In this problem, a learner tries to learn a uniformly sampled\\(x \\sim \\mathbb {Z}_q^n\\)from a stream of inner products plus errors sampled from discrete Gaussians of scale\\(\\sigma \\). Any learning algorithm requires either at least\\(\\varOmega (k^2 / \\log (q / \\sigma ))\\)bits of memory, or at least\\(2^{\\varOmega (k)}\\)many samples, where\\(k = \\varOmega (n \\log (1 / (1 - \\phi (q)/q)))\\). This matches with the information-theoretic upper bound whenqis prime. In addition to LWE, our bounds apply to a wide range of learning problems. Following the work of <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>l [STOC 2018], we describe a learning problem by a learning matrix\\(M :A \\times X \\rightarrow \\{0, 1, \\cdots , q-1\\}\\)together with an error matrix\\(E\\). The learner tries to learn\\(x \\sim X\\)from a stream of samples,\\((a_1, b_1), \\cdots , (a_m, b_m)\\), where for everyi,\\(a_i \\sim A\\), and\\(b_i \\leftarrow t\\)with probability\\(E_{M(a,x),t}\\). We characterize the learning problems that can have memory-sample lower bounds as “q-balanced”, which is a generalization of theL2-extractor in [GRT18]. We also show a reduction fromq-balanced toL2-extractor, which provides a general way to proveq-balanced and thus apply our bounds. Our proof builds on [GRT18] and the work of Garg, Kothari, Liu, Raz [APPROX/RANDOM 2021].", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_7"}, {"primary_key": "558574", "vector": [], "sparse_vector": [], "title": "Limits on the Power of Prime-Order Groups: Separating Q-Type from Static Assumptions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Subgroup decision techniques on cryptographic groups and pairings have been critical for numerous applications. Originally conceived in the composite-order setting, there is a large body of work showing how to instantiate subgroup decision techniques in the prime-order setting as well. In this work, we demonstrate the first barrier to this research program, by demonstrating an important setting where composite-order techniques cannot be replicated in the prime-order setting. In particular, we focus on the case ofq-type assumptions, which are ubiquitous in group- and pairing-based cryptography, but unfortunately are less desirable than the more well-understood static assumptions. Subgroup decision techniques have had great success in removingq-type assumptions, even allowingq-type assumptions to be generically based on static assumptions on composite-order groups. Our main result shows that the same likely doesnothold in the prime order setting. Namely, we show that a large class ofq-type assumptions, including the security definition of a number of cryptosystems, cannot be proven secure in a black box way from any static assumption.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_3"}, {"primary_key": "558575", "vector": [], "sparse_vector": [], "title": "Provable Security Against Decryption Failure Attacks from LWE.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In a recent work, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> introduced a new security proof for the Fujisaki-<PERSON> transform in the quantum-accessible random oracle model (QROM) used in post-quantum key encapsulation mechanisms. While having a smaller security loss due to decryption failures present in many constructions, it requires two new security properties of the underlying public-key encryption scheme (PKE). In this work, we show that one of the properties,Find Failing Plaintexts - Non Generic(FFP-NG) security, is achievable using a relatively efficient LWE-based PKE that does not have perfect correctness. In particular, we show that LWE reduces to breaking FFP-NG security of the PVW scheme, when all LWE errors are discrete Gaussian distributed. The reduction has an arbitrarily small constant multiplicative loss in LWE error size. For the proof, we make use of techniques by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> to analyze marginal and conditional distributions of sums of discrete Gaussians.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_14"}, {"primary_key": "558576", "vector": [], "sparse_vector": [], "title": "Robust Quantum Public-Key Encryption with Applications to Quantum Key Distribution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Quantum key distribution (QKD) allows <PERSON> and <PERSON> to agree on a shared secret key, while communicating over a public (untrusted) quantum channel. Compared to classical key exchange, it has two main advantages: (i) The key isunconditionallyhidden to the eyes of any attacker, and (ii) its security assumes only the existence of authenticated classical channels which, in practice, can be realized using Minicrypt assumptions, such as the existence of digital signatures. On the flip side, QKD protocols typically require multiple rounds of interactions, whereas classical key exchange can be realized with the minimal amount of two messages using public-key encryption. A long-standing open question is whether QKD requires more rounds of interaction than classical key exchange. In this work, we propose a two-message QKD protocol that satisfieseverlastingsecurity, assuming only the existence of quantum-secure one-way functions. That is, the shared key is unconditionally hidden, provided computational assumptions hold during the protocol execution. Our result follows from a new construction of quantum public-key encryption (QPKE) whose security, much like its classical counterpart, only relies on authenticatedclassicalchannels.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_5"}, {"primary_key": "558577", "vector": [], "sparse_vector": [], "title": "Adaptively Sound Zero-Knowledge SNARKs for UP.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study succinct non-interactive arguments (SNARGs) and succinct non-interactive arguments of knowledge (SNARKs) for the class\\(\\textsf{UP}\\)in the reusable designated verifier model.\\(\\textsf{UP}\\)is an expressive subclass of\\(\\textsf{NP}\\)consisting of all\\(\\textsf{NP}\\)languages where each instance has at most one witness; a designated verifier SNARG (dvSNARG) is one where verification of the SNARG proof requires a private verification key; and such a dvSNARG is reusable if soundness holds even against a malicious prover with oracle access to the (private) verification algorithm. Our main results are as follows. A reusably and adaptively sound zero-knowledge (zk) dvSNARG for\\(\\textsf{UP}\\), from subexponential LWE and evasive LWE (a relatively new but popular variant of LWE). Our SNARGs achieve very short proofs of length\\((1 + o(1)) \\cdot \\lambda \\)bits for\\(2^{-\\lambda }\\)soundness error. A generic transformation that lifts any “Sahai-Waters-like” (zk) SNARG, or more specifically, anywitness PRF-based(zk) SNARG, to an adaptively sound (zk) SNARG, in thedesignated-verifiersetting. In particular, this shows that both the Sahai-Waters SNARG for\\(\\textsf{NP}\\), and our SNARG for\\(\\textsf{UP}\\), are adaptively sound in the designated verifier setting, assuming subexponential hardness of the underlying assumptions. The resulting SNARG proofs have length\\((1 + o(1)) \\cdot \\lambda \\)bits for\\(2^{-\\lambda }\\)soundness error. Our result sidesteps the Gentry-Wichs barrier for adaptive soundness by employing a reduction to subexponential hardness assumptions. A generic transformation that lifts any SNARG for\\(\\textsf{UP}\\)to a SNARK for\\(\\textsf{UP}\\), while preserving zero-knowledge and adaptive soundness. The resulting SNARK achieves the strong notion of black-box extraction. There are barriers to achieving such SNARKs for all of\\(\\textsf{NP}\\)from falsifiable assumptions, so our restriction to\\(\\textsf{UP}\\)is, in a sense, necessary. Applying (3) to our SNARG for\\(\\textsf{UP}\\)from evasive LWE (1), we obtain a reusably and adaptively sound designated-verifier zero-knowledge SNARK for\\(\\textsf{UP}\\)from subexponential LWE and evasive LWE. Moreover, applying both (2) and (3) to the Sahai-Waters SNARG, we obtain the same result from LWE, subexponentially secure one-way functions, and subexponentially secure indistinguishability obfuscation. Both constructions have succinct proofs of size\\(\\textsf{poly}(\\lambda ).\\)These are the first SNARK constructions with adaptive soundness (even in the designated-verifier setting) for a non-trivial subset of\\(\\textsf{NP}\\)from (subexponentially) falsifiable assumptions.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_2"}, {"primary_key": "558578", "vector": [], "sparse_vector": [], "title": "Unconditionally Secure Commitments with Quantum Auxiliary Inputs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show the following unconditional results on quantum commitments in two related yet different models: We revisit the notion of quantum auxiliary-input commitments introduced by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (Comput. Complex. 2016) where both the committer and receiver take the same quantum state, which is determined by the security parameter, as quantum auxiliary inputs. We show that computationally-hiding and statistically-binding quantum auxiliary-input commitments exist unconditionally, i.e., without relying on any unproven assumption, while <PERSON><PERSON><PERSON> et al. assumed a complexity-theoretic assumption,\\(\\textbf{QIP}\\not \\subseteq \\textbf{QMA}\\). On the other hand, we observe that achieving both statistical hiding and statistical binding at the same time is impossible even in the quantum auxiliary-input setting. To the best of our knowledge, this is the first example of unconditionally proving computational security of any form of (classical or quantum) commitments for which statistical security is impossible. As intermediate steps toward our construction, we introduce and unconditionally construct post-quantum sparse pseudorandom distributions and quantum auxiliary-input EFI pairs which may be of independent interest. We introduce a new model which we call the common reference quantum state (CRQS) model where both the committer and receiver take the same quantum state that is randomly sampled by an efficient setup algorithm. We unconditionally prove that there exist statistically hiding and statistically binding commitments in the CRQS model, circumventing the impossibility in the plain model. We also discuss their applications to zero-knowledge proofs, oblivious transfers, and multi-party computations.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_3"}, {"primary_key": "558579", "vector": [], "sparse_vector": [], "title": "Quantum Advantage from One-Way Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Is quantum computing truly faster than classical computing? Demonstrating unconditional quantum computational advantage lies beyond the reach of the current complexity theory, and therefore we have to rely on some complexity assumptions. While various results on quantum advantage have been obtained, all necessitate relatively stronger or less standard assumptions in complexity theory or classical cryptography. In this paper, we show quantum advantage based on several fundamental assumptions, specifically relying solely on the existence of classically-secure one-way functions. Given the fact that one-way functions are necessary for almost all classical cryptographic primitives, our findings yield a surprising implication:if there is no quantum advantage, then there is no classical cryptography!More precisely, we introduceinefficient-verifier proofs of quantumness(IV-PoQ), and construct it from statistically-hiding and computationally-binding classical bit commitments. IV-PoQ is an interactive protocol between a verifier and a quantum polynomial-time prover consisting of two phases. In the first phase, the verifier is classical probabilistic polynomial-time, and it interacts with the quantum polynomial-time prover over a classical channel. In the second phase, the verifier becomes inefficient, and makes its decision based on the transcript of the first phase. If the quantum prover is honest, the inefficient verifier accepts with high probability, but any classical probabilistic polynomial-time malicious prover only has a small probability of being accepted by the inefficient verifier. In our construction, the inefficient verifier can be a classical deterministic polynomial-time algorithm that queries an\\(\\textbf{NP}\\)oracle. Our construction demonstrates the following results based on the known constructions of statistically-hiding and computationally-binding commitments from one-way functions or distributional collision-resistant hash functions: If one-way functions exist, then IV-PoQ exist. If distributional collision-resistant hash functions exist (which exist if hard-on-average problems in\\(\\textbf{SZK}\\)exist), then constant-round IV-PoQ exist. We also demonstrate quantum advantage based on worst-case-hard assumptions. We defineauxiliary-input IV-PoQ(AI-IV-PoQ) that only require that for any malicious prover, there exist infinitely many auxiliary inputs under which the prover cannot cheat. We construct AI-IV-PoQ from an auxiliary-input version of commitments in a similar way, showing that If auxiliary-input one-way functions exist (which exist if\\(\\textbf{CZK}\\not \\subseteq \\textbf{BPP}\\)), then AI-IV-PoQ exist. If auxiliary-input collision-resistant hash functions exist (which is equivalent to\\(\\textbf{PWPP}\\nsubseteq \\textbf{FBPP}\\)) or\\(\\textbf{SZK}\\nsubseteq \\textbf{BPP}\\), then constant-round AI-IV-PoQ exist. Finally, we also show that some variants of PoQ can be constructed from quantum-evaluation one-way functions (QE-OWFs), which are similar to classically-secure classical one-way functions except that the evaluation algorithm is not classical but quantum. QE-OWFs appear to be weaker than classically-secure classical one-way functions, and therefore it demonstrates quantum advantage based on assumptions even weaker than one-way functions.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_13"}, {"primary_key": "558580", "vector": [], "sparse_vector": [], "title": "QFESTA: Efficient Algorithms and Parameters for FESTA Using Quaternion Algebras.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In 2023, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> proposed FESTA (Fast Encryption from Supersingular Torsion Attacks), an isogeny-based public-key encryption (PKE) protocol that uses the SIDH attack for decryption. In the same paper, they proposed parameters for that protocol, but the parameters require high-degree isogeny computations. In this paper, we introduce QFESTA (Quaternion Fast Encapsulation from Supersingular Torsion Attacks), a new variant of FESTA that works with better parameters using quaternion algebras and achieves IND-CCA security under QROM. To realize our protocol, we construct a new algorithm to compute an isogeny of non-smooth degree using quaternion algebras and the SIDH attack. Our protocol relies solely on (2, 2)-isogeny and 3-isogeny computations, promising a substantial reduction in computational costs. In addition, our protocol has significantly smaller data sizes for public keys and ciphertexts, approximately half size of the original FESTA.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_4"}, {"primary_key": "558581", "vector": [], "sparse_vector": [], "title": "Towards Breaking the Half-Barrier of Local Leakage-Resilient Shamir&apos;s Secret Sharing.", "authors": ["<PERSON>"], "summary": "Advanced methods for repairing Reed-Solomon codes, exemplified by the work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC 2016), can be exploited to launch local leakage attacks against Shamir secret-sharing schemes over characteristic-2 fields. Conversely, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (CRYPTO 2018) proved that high-threshold instances of Shamir’s secret sharing over prime fields are resilient to one-bit local leakage. Since then, extensive efforts have been made to lower the threshold. However, even for simple leakage such as quadratic residue, it remains uncertain whether Shamir’s scheme is leakage-resilient when the reconstruction thresholdnis less than half the number of partiesk. As highlighted by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (CRYPTO 2021), the common approach fails to establish the leakage resilience of Shamir’s scheme against quadratic residue leakage when\\(k < n/2\\). In many applications, the threshold must not exceed half the number of parties. This work develops a new framework for studying the local leakage resilience of Shamir’s secret sharing over a finite field of prime orderp. Our work demonstrates that Shamir secret sharing remains leakage resilient against almost all 1-bit local leakages, including quadratic residue leakage, even when\\(k = cn\\)for any constantc, provided the prime field is sufficiently large. This result extends to any MDS code-based secret sharing. For the hardness of computation, we propose an explicit 2-bit leakage attack capable of determining the secret in Shamir secret sharing with a reconstruction threshold\\(k = O(\\sqrt{n})\\)when\\(p = \\varTheta (n)\\). Our attack translates into a repairing algorithm for Reed-Solomon codes. Technically, our framework relies on additive combinatorics and character sums, specifically higher-order Fourier analysis. These connections may be of independent interest.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_10"}, {"primary_key": "558582", "vector": [], "sparse_vector": [], "title": "Mangrove: A Scalable Framework for Folding-Based SNARKs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Nirvan Tyagi", "<PERSON>"], "summary": "We present a framework for building efficient folding-based SNARKs. First we develop a new “uniformizing” compiler for NP statements that converts any poly-time computation to a sequence of identical simple steps. The resulting uniform computation is especially well-suited to be processed by a folding-based IVC scheme. Second, we develop two optimizations to folding-based IVC. The first reduces the recursive overhead of the IVC by restructuring the relation to which folding is applied. The second employs a “commit-and-fold” strategy to further simplify the relation. Together, these optimizations result in a folding-based SNARK that has a number of attractive features. First, the scheme uses a constant-size transparent common reference string (CRS). Second, the prover has (i) low memory footprint, (ii) makes only two passes over the data, (iii) is highly parallelizable, and (iv) is concretely efficient. Microbenchmarks indicate that proving time is competitive with leading monolithic SNARKs, and significantly faster than other streaming SNARKs. For\\(2^{24}\\)(\\(2^{32}\\)) gates, the Mangrove prover is estimated to take 2 minutes (8 hours) with peak memory usage approximately 390 MB (800 MB) on a\\(\\text {laptop}^{1}\\)\\((^{1}\\)The full version of this work is available online at [43].).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_10"}, {"primary_key": "558583", "vector": [], "sparse_vector": [], "title": "Greyhound: Fast Polynomial Commitments from Lattices.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we proposeGreyhound, the first concretely efficient polynomial commitment scheme from standard lattice assumptions. At the core of our construction lies a simple three-round protocol for proving evaluations for polynomials of bounded degreeNwith verifier time complexity\\(O(\\sqrt{N})\\). By composing it with the LaBRADOR proof system (CRYPTO 2023), we obtain a succinct proof of polynomial evaluation (i.e. polylogarithmic inN) that admits a sublinear verifier runtime. To highlight practicality ofGreyhound, we provide implementation details including concrete sizes and runtimes. Notably, for large polynomials of degree at most\\(N=2^{30}\\), the scheme produces evaluation proofs of size 53KB, which is more than\\(10^4\\)times smaller than the recent lattice-based framework, calledSLAP(EUROCRYPT 2024), and around three orders of magnitude smaller than Ligero (CCS 2017) and Brakedown (CRYPTO 2023).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_8"}, {"primary_key": "558584", "vector": [], "sparse_vector": [], "title": "Speeding Up Preimage and Key-Recovery Attacks with Highly Biased Differential-Linear Approximations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>wei Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a framework for speeding up the search for preimages of candidate one-way functions based on highly biased differential-linear distinguishers. It is naturally applicable to preimage attacks on hash functions. Further, a variant of this framework applied to keyed functions leads to accelerated key-recovery attacks. Interestingly, our technique is able to exploitrelated-keydifferential-linear distinguishers in thesingle-keymodel without querying the target encryption oracle with unknown but related keys. This is in essence similar to how we speed up the key search based on the well known complementation property of DES, which calls for caution from the designers in building primitives meant to be secure in the single-key setting without a thorough cryptanalysis in the related-key model. We apply the method to sponge-based hash functionAscon-HASH, XOFsXOEsch/Ascon-XOFand AEADSchwaemm, etc. Accelerated preimage or key-recovery attacks are obtained. Note that all the differential-linear distinguishers employed in this work are highly biased and thus can be experimentally verified.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_3"}, {"primary_key": "558585", "vector": [], "sparse_vector": [], "title": "Improved Reductions from Noisy to Bounded and Probing Leakages via Hockey-Stick Divergences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "There exists a mismatch between the theory and practice of cryptography in the presence of leakage. On the theoretical front, thebounded leakagemodel, where the adversary learns bounded-length but noiseless information about secret components, and therandom probingmodel, where the adversary learns some internal values of a leaking implementation with some probability, are convenient abstractions to analyze the security of numerous designs. On the practical front, side-channel attacks produce long transcripts which are inherently noisy but provide information about all internal computations, and this noisiness is usually evaluated with closely related metrics like the mutual information or statistical distance. Ideally, we would like to claim that resilience to bounded leakage or random probing implies resilience to noisy leakage evaluated according to these metrics. However, prior work (<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, Eurocrypt 2014; <PERSON> et al., Eurocrypt 2021) has shown that proving such reductions with useful parameters is challenging. In this work, we study noisy leakage models stemming fromhockey-stick divergences, which generalize statistical distance and are also the basis of differential privacy. First, we show that resilience to bounded leakage and random probing implies resilience to our new noisy leakage model with improved parameters compared to models based on the statistical distance or mutual information. Second, we establish composition theorems for our model, showing that these connections extend to a setting where multiple leakages are obtained from a leaking implementation. We complement our theoretical results with a discussion of practical relevance, highlighting that (i) the reduction to bounded leakage applies to realistic leakage functions with noise levels that are decreased by several orders of magnitude compared to <PERSON> et al., and (ii) the reduction to random probing usefully generalizes the seminal work of <PERSON>c, Dziembowski, and <PERSON>, although it remains limited when the field size in which masking operates grows (i.e., hockey-stick divergences can better hide the field size dependency of the noise requirements, but do not annihilate it).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_14"}, {"primary_key": "558586", "vector": [], "sparse_vector": [], "title": "Oblivious Issuance of Proofs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of issuing zero-knowledge proofsobliviously. In this setting, a prover interacts with a verifier to produce a proof, known only to the verifier. The resulting proof cannot be linked back to the interaction that produced it, and can be verified non-interactively by anyone. This notion generalizes common approaches to designing blind signatures, which can be seen as the special case of proving “knowledge of a signing key”, and extends the seminal work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (’97). We propose two provably-secure constructions of oblivious proofs, and give three applications of our framework. First, we give a publicly verifiable version of the classical Diffie-Hellman based Oblivious PRF. This yields new constructions of blind signatures and publicly verifiable anonymous tokens. Second, we show how to “upgrade” keyed-verification anonymous credentials (<PERSON> et al., CCS’14) to also be concurrently secure blind signatures on the same set of attributes. Our upgrade maintains the performance and functionality of the credential in the keyed-verification setting, we only change issuance. Finally, we provide a variation of the U-Prove credential system that is provably one-more unforgeable with concurrent issuance sessions. This constitutes a fix for the attack illustrated by <PERSON><PERSON><PERSON><PERSON> et al. (EUROCRYPT’21). Beyond these example applications, as our results are quite general, we expect they may enable modular design of new primitives with concurrent security, a goal that has historically been challenging to achieve.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_8"}, {"primary_key": "558587", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Dijkstra.", "authors": ["<PERSON>"], "summary": "Given a graphG(V,E), represented as a secret-sharing of an adjacency list, we show how to obliviously convert it into an alternative, MPC-friendly secret-shared representation, so-calledd-normalized replicated adjacency list(which we abbreviate tod-normalized), where the size of our new data-structure is only 4x larger – compared to the original (secret-shared adjacency list) representation ofG. Yet, this new data structure enables us to execute oblivious graph algorithms that simultaneously improve underlying graph algorithms’ round, computation, and communication complexity. Ourd-normalizationproceeds in two steps: First, we show how for any graphG, given a secret-shared adjacency list, where vertices are arbitrary alphanumeric strings that fit into a single RAM memory word, we can efficiently and securely rename vertices to integers from 1 toVthat will appear in increasing order in the resulting secret-shared adjacency list. The secure renaming takes\\(O(\\log V)\\)rounds and\\(O((V+E)\\log V)\\)secure operations. Our algorithm also outputs in a secret-shared form two arrays: a mapping from integers to alphanumeric names and its sorted inverse. Of course, if the adjacency list is already in such an integer format, this step could be skipped. Second, given a secret-shared adjacency list for any graphG, where vertices are integers from 1 toVand are sorted, there exists ad-normalizationalgorithm that takesO(1) rounds and\\(O(V+E)\\)secure operations. We believe that both conversions are of independent interest. We demonstrate the power of our data structures by designing a privacy-preserving Dijkstra’s single-source shortest-path algorithm that achieves\\(O\\left( (V +E) \\log V \\right) \\)secure operations and\\(O(V \\cdot \\log V \\cdot \\log \\log \\log V)\\)rounds. Our secure Dijkstra algorithm works for any adjacency list representation as long as all vertex labels and weights can individually fit into RAM memory word. Our algorithms work for two or a constant number of servers in the honest but curious setting. The limitation of our result (to only a constant number of servers) is due to our reliance on linear work and constant-round secure shuffle.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_3"}, {"primary_key": "558588", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of Lattice-Based Sequentiality Assumptions and Proofs of Sequential Work.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This workcompletely breaksthe sequentiality assumption (and broad generalizations thereof) underlying the candidate lattice-based proof of sequential work (PoSW) recently proposed by <PERSON> and <PERSON><PERSON> at CRYPTO 2023. In addition, it breaks an essentially identical variant of the PoSW, which differs from the original in only an arbitrary choice that is immaterial to the design and security proof (under the falsified assumption). This suggests that whatever security the original PoSW may have is fragile, and further motivates the search for a construction based on a sound lattice-based assumption. Specifically, for sequentiality parameterTand SIS parameters\\(n,q,m = n \\log q\\), the attack on the sequentiality assumption finds a solution of quasipolynomial norm\\(m^{\\lceil \\log T\\rceil }\\)(or norm\\(O\\left( \\sqrt{m}\\right) ^{\\lceil \\log T\\rceil }\\)with high probability) in onlylogarithmic\\(\\tilde{O} _{n,q}(\\log T)\\)depth; this strongly falsifies the assumption that finding such a solution requires depthlinearinT. (The\\(\\tilde{O} \\)notation hides polylogarithmic factors in the variables appearing in its subscript.) Alternatively, the attack finds a solution of polynomial norm\\(m^{1/\\varepsilon }\\)in depth\\(\\tilde{O} _{n,q}(T^{\\varepsilon })\\), for any constant\\(\\varepsilon > 0\\). Similarly, the attack on the (slightly modified) PoSW constructs a valid proof inpolylogarithmic\\(\\tilde{O} _{n,q}(\\log ^2 T)\\)depth, thus strongly falsifying the expectation that doing so requires linear sequential work.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68388-6_6"}, {"primary_key": "558589", "vector": [], "sparse_vector": [], "title": "New Approaches for Estimating the Bias of Differential-Linear Distinguishers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Differential-linear cryptanalysis was introduced by <PERSON><PERSON> and <PERSON> in 1994 and has been extensively studied since then. In 2019, <PERSON><PERSON><PERSON> et al. presented the Differential-Linear Connectivity Table (DLCT), which connects the differential part and the linear part, thus an attacked cipher is divided to 3 subciphers: the differential part, the DLCT part, and the linear part. In this paper, we firstly present an accurate mathematical formula which establishes a relation between differential-linear and truncated differential cryptanalysis. Using the formula, the bias estimate of a differential-linear distinguisher can be converted to the probability calculations of a series of truncated differentials. Then, we propose a novel and natural concept, the TDT, which can be used to accelerate the calculation of the probabilities of truncated differentials. Based on the formula and the TDT, we propose two novel approaches for estimating the bias of a differential-linear distinguisher. We demonstrate the accuracy and efficiency of our new approaches by applying them to 5 symmetric-key primitives: Ascon, Serpent, KNOT, AES, and CLEFIA. For Ascon and Serpent, we update the best known differential-linear distinguishers. For KNOT, AES, and CLEFIA, for the first time we give the theoretical differential-linear biases for different rounds.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_6"}, {"primary_key": "558590", "vector": [], "sparse_vector": [], "title": "Public-Key Anamorphism in (CCA-Secure) Public-Key Encryption and Beyond.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The notion of (Receiver-) Anamorphic Encryption was put forth recently to show that a dictator (i.e., an overreaching government), which demands to get the receiver’s private key and even dictates messages to the sender, cannot prevent the receiver from getting an additional covert anamorphic message from a sender. The model required an initial private collaboration to share some secret. There may be settings though where an initial collaboration may be impossible or performance-wise prohibitive, or cases when we need an immediate message to be sent without private key generation (e.g., by any casual sender in need). This situation, to date, somewhat limits the applicability of anamorphic encryption. To overcome this, in this work, we put forth the new notion of “public-key anamorphic encryption,” where, without any initialization, any sender that has not coordinated in any shape or form with the receiver, can nevertheless, under the dictator control of the receiver’s private key, send the receiver an additional anamorphic secret message hidden from the dictator. We define the new notion with its unique new properties, and then prove that, quite interestingly, the known CCA-secure Koppula-Waters (KW) system is, in fact, public-key anamorphic. We then describe how a public-key anamorphic scheme can support a newhybrid anamorphicencapsulation mode (KDEM) where the public-key anamorphic part serves a bootstrapping mechanism to activate regular anamorphic messages in the same ciphertext, thus together increasing the anamorphic channel capacity. Looking at the state of research thus far, we observe that the initial system (Eurocrypt’22) that was shown to have regular anamorphic properties is the CCA-secure Naor-Yung (and other related schemes). Here we identify that the KW CCA-secure scheme also provides a new type of anamorphism. Thus, this situation is hinting that there may be a connection between some types of CCA-secure schemes and some type of anamorphic schemes (in spite of the fact that the goals of the two primitives are fundamentally different); this question is foundational in nature. Given this, we identify a sufficient condition for a “CCA-secure scheme which is black-box reduced from a CPA secure scheme” to directly give rise to an “anamorphic encryption scheme!” Furthermore, we identify one extra property of the reduction, that yields a public-key anamorphic scheme as defined here.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_13"}, {"primary_key": "558591", "vector": [], "sparse_vector": [], "title": "Ra<PERSON>on: A Masking-Friendly Signature Proven in the Probing Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents Raccoon, a lattice-based signature scheme submitted to the NIST 2022 call for additional post-quantum signatures. Ra<PERSON>on has the specificity of always being masked. Concretely, all sensitive intermediate values are shared intodparts. The main design rationale of Ra<PERSON>on is to be easy to mask at high orders, and this dictated most of its design choices, such as the introduction of new algorithmic techniques for sampling small errors. As a result, <PERSON><PERSON><PERSON> achieves a masking overhead\\(O(d \\log d)\\)that compares favourably with the overheads\\(O(d^2 \\log q)\\)observed when masking standard lattice signatures. In addition, we formally prove the security of <PERSON><PERSON><PERSON> in thet-probing model: an attacker is able to probe\\(t \\le d-1\\)shares during each execution of the main algorithms (key generation, signing, verification). While for most cryptographic schemes, the black-boxt-probing security can be studied in isolation, in Ra<PERSON>on this analysis is performed jointly. To that end, a bridge must be made between the black-box game-based EUF-CMA proof and the usual simulation proofs of the ISW model (CRYPTO 2003). We formalize an end-to-end masking proof by deploying the probing EUF-CMA introduced by <PERSON><PERSON> et al. (Eurocrypt 2018) and exhibiting the simulators of the non-interference properties (<PERSON><PERSON> et al. CCS 2016). The proof is divided into three novel parts: a simulation proof in the ISW model that allows to propagate the dependancy to a restricted number of inputs and random coins, a game-based proof showing that the security of Raccoon with probes can be reduced to an instance of Raccoon with smaller parameters, a parameter study to ensure that the smaller instance is secure, using a robust generalization of the Rényi divergence. While we apply our techniques to Raccoon, we expect that the algorithmic and proof techniques we introduce will be helpful for the design and analysis of future masking-friendly schemes.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_13"}, {"primary_key": "558592", "vector": [], "sparse_vector": [], "title": "Unconditionally Secure Quantum Commitments with Preprocessing.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We demonstrate how to build computationally secure commitment schemes with the aid of quantum auxiliary inputswithout unproven complexity assumptions. Furthermore, the quantum auxiliary input can be either sampled in uniform exponential time or prepared in at most doubly exponential time, without relying on an external trusted third party. Classically, this remains impossible without first proving\\(\\textsf{P} \\ne \\textsf{NP}\\).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68394-7_2"}, {"primary_key": "558593", "vector": [], "sparse_vector": [], "title": "Space-Efficient and Noise-Robust Quantum Factoring.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We provide two improvements to <PERSON><PERSON>’s quantum factoring algorithm (arXiv:2308.06572), addressing its space efficiency and its noise-tolerance. Our first contribution is to improve the quantum space efficiency of <PERSON><PERSON>’s algorithm while keeping the circuit size the same. Our main result constructs a quantum factoring circuit using\\(O(n \\log n)\\)qubits and\\(O(n^{3/2} \\log n)\\)gates. We achieve the best of <PERSON><PERSON> and <PERSON><PERSON> (upto a logarithmic factor in the space complexity): on the one hand, <PERSON><PERSON>’s circuit requires\\(O(n^{3/2})\\)qubits and\\(O(n^{3/2} \\log n)\\)gates, while <PERSON><PERSON>’s circuit requires\\(O(n^2 \\log n)\\)gates but onlyO(n) qubits. As with <PERSON><PERSON>, to factor ann-bit integerN, we run our circuit independently\\(\\approx \\sqrt{n}\\)times and apply <PERSON><PERSON>’s classical postprocessing procedure. Our optimization is achieved by implementing efficient and reversible exponentiation with Fibon<PERSON>ci numbers in the exponent, rather than the usual powers of 2, adapting work by <PERSON><PERSON> (arXiv:1711.02491) from the classical reversible setting to the quantum setting. This technique also allows us to perform quantum modular exponentiation that is efficient in both space and size without requiring significant precomputation, a result that may be useful for other quantum algorithms. A key ingredient of our exponentiation implementation is an efficient circuit for a function resemblingin-placequantum-quantum modular multiplication. This implementation works with only black-box access to any quantum circuit forout-of-placemodular multiplication, which we believe is yet another result of potentially broader interest. Our second contribution is to show that Regev’s classical postprocessing procedure can be modified to tolerate a constant fraction of the quantum circuit runs being corrupted by errors. In contrast, <PERSON>ev’s analysis of his classical postprocessing procedure requires all\\(\\approx \\sqrt{n}\\)runs to be successful. In a nutshell, we achieve this using lattice reduction techniques to detect and filter out corrupt samples.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_4"}, {"primary_key": "558594", "vector": [], "sparse_vector": [], "title": "Zero-Knowledge IOPs Approaching Witness Length.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Interactive Oracle Proofs (IOPs) allow a probabilistic verifier interacting with a prover to verify the validity of an NP statement while reading only few bits from the prover messages. IOPs generalize standard Probabilistically-Checkable Proofs (PCPs) to the interactive setting, and in the few years since their introduction have already exhibited major improvements in main parameters of interest (such as the proof length and prover and verifier running times), which in turn led to significant improvements in constructions of succinct arguments.Zero-Knowledge(ZK) IOPs additionally guarantee that the view of any query-bounded (possibly malicious) verifier can be efficiently simulated. ZK-IOPs are the main building block of succinctZKarguments which use the underlying cryptographic object (e.g., a collision-resistant hash function)as a black box. In this work, we construct the firstZK-IOPs approaching the witness lengthfor a natural NP problem. More specifically, we design constant-query and constant-round IOPs for 3SAT in which the total communication is\\((1+\\gamma )m\\), wheremis the number of variables and\\(\\gamma >0\\)is an arbitrarily small constant, and ZK holds against verifiers querying\\(m^\\beta \\)bits from the prover’s messages, for a constant\\(\\beta >0\\). This gives a ZK variant of a recent result of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (FOCS ‘20), who construct (non-ZK) IOPs approaching the witness length for a large class of NP languages. Previous constructions ofZK-IOPs incurred an (unspecified) large constant multiplicative overhead in the proof length, even when restricting to ZK against thehonestverifier. We obtain our ZK-IOPs by improving the two main building blocks underlying most ZK-IOP constructions, namely ZK codes and ZK-IOPs for sumcheck. More specifically, we give the first ZK-IOPs for sumcheck that achieve bothsublinearcommunication for sumchecking ageneraltensor code, and a ZK guarantee. We also show a strong ZK preservation property for tensors of ZK codes, which extends a recent result of Bootle, Chiesa, and Liu (EC ‘22). Given the central role of these objects in designing ZK-IOPs, these results might be of independent interest.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_4"}, {"primary_key": "558595", "vector": [], "sparse_vector": [], "title": "Accelerating SLH-DSA by Two Orders of Magnitude with a Single Hash Unit.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We report on efficient and secure hardware implementation techniques for the FIPS 205 SLH-DSA Hash-Based Signature Standard. We demonstrate that very significant overall performance gains can be obtained from hardware that optimizes the padding formats and iterative hashing processes specific to SLH-DSA. A prototype implementation,SLotH, contains Keccak/SHAKE, SHA2-256, and SHA2-512 cores and supports all 12 parameter sets of SLH-DSA.SLotHalso supports side-channel secure PRF computation and Winternitz chains.SLotHdrivers run on a small RISC-V control core, as is common in current Root-of-Trust (RoT) systems. The new features make SLH-DSA onSLotHmany times faster compared to similarly-sized general-purpose hash accelerators. Compared to unaccelerated microcontroller implementations, the performance ofSLotH’s SHAKE variants is up to\\(300\\times \\)faster; signature generation with 128f parameter set is 4,903,978 cycles, while signature verification with 128 s parameter set is only 179,603 cycles. The SHA2 parameter sets have approximately half of the speed of SHAKE parameter sets. We observe that the signature verification performance of SLH-DSA’s “s” parameter sets is generally better than that of accelerated ECDSA or Dilithium on similarly-sized RoT targets. The area of the fullSLotHsystem is small, from 63 kGE (SHA2, Cat 1 only) to 155 kGe (all parameter sets). Keccak Threshold Implementation adds another 130 kGE. We provide sensitivity analysis of SLH-DSA in relation to side-channel leakage. We show experimentally that an SLH-DSA implementation with CPU hashing will rapidly leak the\\(\\mathsf {SK.seed}\\)master key. We perform a 100,000-trace TVLA leakage assessment with a protectedSLotHunit.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_9"}, {"primary_key": "558596", "vector": [], "sparse_vector": [], "title": "On Cycles of Pairing-Friendly <PERSON> Varieties.", "authors": ["Maria <PERSON>-Real Santos", "<PERSON>", "<PERSON>"], "summary": "One of the most promising avenues for realising scalable proof systems relies on the existence of 2-cycles of pairing-friendly elliptic curves. Such a cycle consists of two elliptic curves\\(\\ensuremath {\\mathcal {E}}/\\ensuremath {\\mathbb {F}}_p\\)and\\(\\ensuremath {\\mathcal {E}}'/\\ensuremath {\\mathbb {F}}_q\\)that both have a low embedding degree and also satisfy\\(q = \\#\\ensuremath {\\mathcal {E}}(\\ensuremath {\\mathbb {F}}_p)\\)and\\(p = \\#\\ensuremath {\\mathcal {E}}'(\\ensuremath {\\mathbb {F}}_q)\\). These constraints turn out to be rather restrictive; in the decade that has passed since 2-cycles were first proposed for use in proof systems, no new constructions of 2-cycles have been found. In this paper, we generalise the notion of cycles of pairing-friendly elliptic curves to study cycles of pairing-friendlyabelian varieties, with a view towards realising more efficient pairing-based SNARKs. We show that considering abelian varieties of dimension larger than 1 unlocks a number of interesting possibilities for finding pairing-friendly cycles, and we give several new constructions that can be instantiated at any security level.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68400-5_7"}, {"primary_key": "558597", "vector": [], "sparse_vector": [], "title": "Is ML-Based Cryptanalysis Inherently Limited? Simulating Cryptographic Adversaries via Gradient-Based Methods.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given the recent progress in machine learning (ML), the cryptography community has started exploring the applicability of ML methods to the design of new cryptanalytic approaches. While current empirical results show promise, the extent to which such methods may outperform classical cryptanalytic approaches is still somewhat unclear. In this work, we initiate exploration of the theory of ML-based cryptanalytic techniques, in particular providing new results towards understanding whether they are fundamentally limited compared to traditional approaches. Whereas most classic cryptanalysis crucially relies on directly processing individual samples (e.g., plaintext-ciphertext pairs), modern ML methods thus far only interact with samples via gradient-based computations that average a loss function over all samples. It is, therefore, conceivable that such gradient-based methods are inherently weaker than classical approaches. We introduce a unifying framework for capturing both “sample-based” adversaries that are provided with direct access to individual samples and “gradient-based” ones that are restricted to issuing gradient-based queries that are averaged over all given samples via a loss function. Within our framework, we establish a general feasibility result showing that any sample-based adversary can be simulated by a seemingly-weaker gradient-based one. Moreover, the simulation exhibits a nearly optimal overhead in terms of the gradient-based simulator’s running time. Finally, we extend and refine our simulation technique to construct a gradient-based simulator that is fully parallelizable (crucial for avoiding an undesirable overhead for parallelizable cryptanalytic tasks), which is then used to construct a gradient-based simulator that executes the particular and highly useful gradient-descent method. Taken together, although the extent to which ML methods may outperform classical cryptanalytic approaches is still somewhat unclear, our results indicate that such gradient-based methods are not inherently limited by their seemingly restricted access to the provided samples.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68391-6_2"}, {"primary_key": "558598", "vector": [], "sparse_vector": [], "title": "Feistel-Like Structures Revisited: Classification and Cryptanalysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Zhengyi Dai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Longjiang Qu", "Shaojing Fu"], "summary": "In 2023, <PERSON><PERSON> <PERSON>.summarized the Feistel-like structures which use a single round function, and proposed a unified form of these structures which is named the unified structure. This paper focuses on the classification and cryptanalysis of a particular kind of unified structures which covers the vast majority of known situations and is namedregular unified structure. The main results are as follows: First of all, we give the definition ofAffine Equivalencebetween different structures, present a condition for two regular structures being affine equivalent, and give two normalized forms of a regular unified structure. Surprisingly, we find that a target-heavy generalised Feistel cipher is always affine equivalent to a source-heavy generalised Feistel cipher with the same round function, which shows these two structures always have almost the same cryptographic properties. Secondly, we give the definition of aSelf-Equivalentstructure, whose dual structure is affine equivalent to the structure itself. We prove that there is a large portion of the unified structures such as the SM4 and the Mars structures that are among the self-equivalent ones. For these structures, there is a one-to-one correspondence between the impossible differentials and the zero correlation linear hulls, which illustrates that the longest integral of a self-equivalent structure covers at least the rounds of the longest zero correlation linear hull/impossible differential. At last, we give the definition of theRefined Full-Diffusion Roundof a structure, and exploit the\\(\\epsilon \\)-\\(\\delta \\)technique to compute this value, which can be further used to give provable security evaluations of unified structures against impossible differential and zero correlation linear cryptanalysis. For example, we prove that both the longest impossible differential and zero correlation linear hull of thed-branch SM4-like structures cover exactly\\(3d-1\\)rounds. Our results could give new guidelines for both the cryptanalysis and the design of Feistel-like ciphers.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_9"}, {"primary_key": "558599", "vector": [], "sparse_vector": [], "title": "Game-Theoretically Fair Distributed Sampling.", "authors": ["<PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>’s celebrated result (STOC’86) showed that a strongly fair multi-party coin-toss is impossible against majority-sized coalitions. Recently, however, a fascinating line of work studied a relaxed fairness notion calledgame-theoretic fairness, which guarantees that no coalition should be incentivized to deviate from the prescribed protocol. A sequence of works has explored the feasibility of game-theoretic fairness fortwo-sidedcoin-toss, and demonstrated feasibility in the dishonest majority setting under standard cryptographic assumptions. However, this line of work only focused on uniform two-sided coin-toss or leader election. In this work, weinitiatethe comprehensive study of game-theoretic fairness for multi-partysampling from general distributions. In particular, for the case ofm-sideduniformcoin-toss we give a nearly complete characterization of the regime in which game-theoretic fairness is feasible. Interestingly, contrary to standard fairness notions in cryptography, the composition of game-theoretically fair two-sided coin-toss protocols does not necessarily yield game-theoretically fair multi-sided coins. To circumvent this, we introduce new techniques compatible with game-theoretic fairness. In particular, we give the following results: We give a protocol from standard cryptographic assumptions that achieves game-theoretic fairness for uniformm-sided coin-toss against half- or more-sized adversarial coalitions. To complement our protocol, we give a general impossibility result that establishes the optimality of our protocol for a broad range of parameters modulo an additive constant. Even in the worst-case, the gap between our protocol and our impossibility result is only a small constant multiplicative factor. We also present a game-theoretically fair protocol foranyefficiently sampleablem-outcome distribution in the dishonest majority setting. For instance, even for the case of\\(m=2\\)(i.e., two-sided coin-toss), our result implies a game-theoretically fair protocol for anarbitraryBernoulli coin. In contrast, the work of Wu, Asharov, and Shi only focussed on a Bernoulli coin with parameter 1/2.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68397-8_7"}, {"primary_key": "558600", "vector": [], "sparse_vector": [], "title": "Fine-Grained Non-interactive Key-Exchange Without Idealized Assumptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we study multi-party non-interactive key exchange (NIKE) in the fine-grained setting. More precisely, we propose three multi-party NIKE schemes in three computation models, namely, the bounded parallel-time, bounded time, and bounded storage models. Their security is based on a very mild assumption (e.g.,\\(\\mathsf {NC^1}\\subsetneq \\mathsf{\\oplus L/poly}\\)) or even without any complexity assumption. This improves the recent work of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (EUROCRYPT 2023) that requires idealized assumptions, such as random oracles or generic groups. Additionally, we show that all our constructions satisfy a natural desirable property that we refer to as extendability, and we give generic transformations from extendable multi-party NIKE to multi-party identity-based NIKEs in the fine-grained settings.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68379-4_8"}, {"primary_key": "558601", "vector": [], "sparse_vector": [], "title": "Adaptive Security in SNARGs via iO and Lossy Functions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We construct an adaptively sound SNARGs in the plain model with CRS relying on the assumptions of (subexponential) indistinguishability obfuscation (iO), subexponential one-way functions and a notion of lossy functions we call length parameterized lossy functions. Length parameterized lossy functions take in separate security and input length parameters and have the property that the function image size in lossy mode depends only on the security parameter. We then show a novel way of constructing such functions from the Learning with Errors (LWE) assumption. Our work provides an alternative path towards achieving adaptively secure SNARGs from the recent work of Waters and Wu [WW24]. Their work required the use of (essentially) perfectly re-randomizable one way functions (in addition to obfuscation). Such functions are only currently known to be realizable from assumptions such as discrete log or factoring that are known to not hold in a quantum setting.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_3"}, {"primary_key": "558602", "vector": [], "sparse_vector": [], "title": "Circuit ABE with sfpoly( depth,λ )-Sized Ciphertexts and Keys from Lattices.", "authors": ["<PERSON><PERSON><PERSON>e"], "summary": "We present new lattice-based attribute-based encryption (ABE) and laconic function evaluation (LFE) schemes for circuits withsublinearciphertext overhead. For depthdcircuits over\\(\\ell \\)-bit inputs, we obtain an ABE with ciphertext and secret key sizeO(1); a LFE with ciphertext size\\(\\ell + O(1)\\)and digest sizeO(1); an ABE with public key and ciphertext size\\(O(\\ell ^{2/3})\\)and secret key sizeO(1), where\\(O(\\cdot )\\)hides\\(\\textsf{poly}(d,\\lambda )\\)factors. The first two results achieve almost optimal ciphertext and secret key/digest sizes, up to the\\(\\textsf{poly}(d)\\)dependencies. The security of our schemes relies on\\(\\ell \\)-succinct LWE, a falsifiable assumption which is implied by evasive LWE. At the core of our results is a new technique for compressing LWE samples\\(\\textbf{s}(\\textbf{A}-\\textbf{x}\\otimes \\textbf{G})\\)as well as the matrix\\(\\textbf{A}\\).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68382-4_6"}, {"primary_key": "558603", "vector": [], "sparse_vector": [], "title": "BaseFold: Efficient Field-Agnostic Polynomial Commitment Schemes from Foldable Codes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This works introduces\\(\\texttt {BaseFold}\\), a newfield-agnosticPolynomial Commitment Scheme (PCS) for multilinear polynomials that has\\(O(\\log ^{2}(n))\\)verifier costs and\\(O(n \\log n)\\)prover time. An important application of a multilinear PCS is constructing Succinct Non-interactive Arguments (SNARKs) from multilinear polynomial interactive oracle proofs (PIOPs). Furthermore, field-agnosticism is a major boon to SNARK efficiency in applications that require (or benefit from) a certain field choice. Our inspiration for\\(\\texttt {BaseFold}\\)is the Fast Reed-Solomon Interactive-Oracle Proof of Proximity (\\(\\texttt {FRI}\\)IOPP), which leverages two properties of Reed-Solomon (RS) codes defined over “FFT-friendly” fields:\\(O(n \\log n)\\)encoding time, and a second property that we callfoldability. We first introduce a generalization of the\\(\\texttt {FRI}\\)IOPP that works over any foldable linear code in linear time. Second, we construct a new family of linear codes which we callrandom foldable codes, that are a special type of punctured Reed-Muller codes, and prove tight bounds on their minimum distance. Unlike RS codes, our new codes are foldable and have\\(O(n \\log n)\\)encoding time overanysufficiently large field. Finally, we construct a new multilinear PCS by carefully interleaving our IOPP with the classical sumcheck protocol, which also gives a new multilinear PCS from\\(\\texttt {FRI}\\). \\(\\texttt {BaseFold}\\)is 2–3 times faster than prior multilinear PCS constructions from\\(\\texttt {FRI}\\)when defined over the same finite field. More significantly, using Hyperplonk (Eurocrypt, 2022) as a multilinear PIOP backend for apples-to-apples comparison, we show that\\(\\texttt {BaseFold}\\)results in a SNARK that has better concrete efficiency across a range of field choices than with any prior multilinear PCS in the literature. Hyperplonk with\\(\\texttt {BaseFold}\\)has a proof size that is more than 10 times smaller than Hyperplonk with Brakedown and its verifier is over 30 times faster for circuits with more than\\(2^{20}\\)gates. Compared to\\(\\texttt {FRI}\\), Hyperplonk with\\(\\texttt {BaseFold}\\)retains efficiency overanysufficiently large field. For illustration, with\\(\\texttt {BaseFold}\\)we can prove ECDSA signature verification over the secp256k1 curve more than 20 times faster than Hyperplonk with\\(\\texttt {FRI}\\)and the verifier is also twice as fast. Proofs of signature verification have many useful applications, including offloading blockchain transactions and enabling anonymous credentials over the web.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68403-6_5"}, {"primary_key": "558604", "vector": [], "sparse_vector": [], "title": "Probabilistic Linearization: Internal Differential Collisions in up to 6 Rounds of SHA-3.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Mei<PERSON> Liu"], "summary": "The\\(\\texttt {SHA}\\text {-} \\texttt {3}\\)standard consists of four cryptographic hash functions, calledSHA3-224,SHA3-256,SHA3-384andSHA3-512, and two extendable-output functions (XOFs), calledSHAKE128and\\(\\texttt {SHAKE256}\\). In this paper, we study the collision resistance of the\\(\\texttt {SHA}\\text {-} \\texttt {3}\\)instances. By analyzing the nonlinear layer, we introduce the concept of maximum difference density subspace, and develop a new target internal difference algorithm by probabilistic linearization. We also exploit new strategies for optimizing the internal differential characteristic. Furthermore, we figure out the expected size of collision subsets in internal differentials, by analyzing the collision probability of the digests rather than the intermediate states input to the last nonlinear layer. These techniques enhance the analysis of internal differentials, leading to the best collision attacks on four round-reduced variants of the\\(\\texttt {SHA}\\text {-} \\texttt {3}\\)instances. In particular, the number of attacked rounds is extended to 5 from 4 forSHA3-384, and to 6 from 5 for\\(\\texttt {SHAKE256}\\).", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68385-5_8"}, {"primary_key": "558605", "vector": [], "sparse_vector": [], "title": "Loquat: A SNARK-Friendly Post-quantum Signature Based on the Legendre PRF with Applications in Ring and Aggregate Signatures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Dong<PERSON> Liu", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We design and implement a novel post-quantum signature scheme based on the Legendre PRF, namedLoquat. Prior to this work, efficient approaches for constructing post-quantum signatures with comparable security assumptions mainly used the MPC-in-the-head paradigm or hash trees. Our method departs from these paradigms and, notably, is SNARK-friendly, a feature not commonly found in earlier designs.Loquatrequires significantly fewer computational operations for verification than other symmetric-key-based post-quantum signature schemes that support stateless signing. Our Python implementation ofLoquatdemonstrate a signature size of 46KB, with a signing time of 5.04 s and a verification time of 0.21 s. Instantiating the random oracle with an algebraic hash function results in the R1CS constraints for signature verification being about 148K, 7 to 175 times smaller than those required for MPC-in-the-head-based signatures and 3 to 9 times less than those for SPHINCS+ [<PERSON> et al. CCS’19]. We explore two applications ofLoquat. First, we incorporate it into the ID-based ring signature scheme [<PERSON><PERSON> et al. ACNS’22], achieving a significant reduction in signature size from 1.9 MB to 0.9 MB with stateless signing and practical master key generation. Our second application presents a SNARK-based aggregate signature scheme. We use the implementations of Aurora [<PERSON><PERSON><PERSON> et al. EC’19] and Fractal [<PERSON><PERSON><PERSON> et al. EC’20] to benchmark our aggregate signature’s performance. Our findings show that aggregating 32Loquatsignatures using Aurora results in a proving time of about 7 min, a verification time of 66 s, and an aggregate signature size of 197 KB. Furthermore, by leveraging the recursive proof composition feature of Fractal, we achieve an aggregate signature with aconstantsize of 145 KB, illustrating Loquat’s potential for scalability in cryptographic applications.", "published": "2024-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-031-68376-3_1"}]