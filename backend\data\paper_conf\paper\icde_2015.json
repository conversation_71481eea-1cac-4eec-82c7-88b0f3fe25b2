[{"primary_key": "4429547", "vector": [], "sparse_vector": [], "title": "Demonstration of Taghreed: A system for querying, analyzing, and visualizing geotagged microblogs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper demonstrates Taghreed; a full-fledged system for efficient and scalable querying, analyzing, and visualizing geotagged microblogs, such as tweets. Taghreed supports a wide variety of queries on all microblogs attributes. In addition, it is able to manage a large number (billions) of microblogs for relatively long periods, e.g., months. Taghreed consists of four main components: (1) indexer, (2) query engine, (3) recovery manager, and (4) visualizer. Taghreed indexer efficiently digests incoming microblogs with high arrival rates in light main-memory indexes. When the memory becomes full, the memory contents are flushed to disk indexes which are managing billions of microblogs efficiently. On memory failure, the recovery manager restores the memory contents from backup copies. Taghreed query engine consists of two modules: a query optimizer and a query processor. The query optimizer generates an optimized query plan to be executed by the query processor to provide low query responses. Taghreed visualizer features to its users a wide variety of spatiotemporal queries and presents the answers on a map-based user interface that allows an interactive exploration. Taghreed is the first system that addresses all these challenges collectively for geotagged microblogs data. The system is demonstrated based on real system implementation through different scenarios that show system functionality and internals.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113390"}, {"primary_key": "4429548", "vector": [], "sparse_vector": [], "title": "Cleaning structured event logs: A graph repair approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Event data are often dirty owing to various recording conventions or simply system errors. These errors may cause many serious damages to real applications, such as inaccurate provenance answers, poor profiling results or concealing interesting patterns from event data. Cleaning dirty event data is strongly demanded. While existing event data cleaning techniques view event logs as sequences, structural information do exist among events. We argue that such structural information enhances not only the accuracy of repairing inconsistent events but also the computation efficiency. It is notable that both the structure and the names (labeling) of events could be inconsistent. In real applications, while unsound structure is not repaired automatically (which needs manual effort from business actors to handle the structure error), it is highly desirable to repair the inconsistent event names introduced by recording mistakes. In this paper, we propose a graph repair approach for 1) detecting unsound structure, and 2) repairing inconsistent event name.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113270"}, {"primary_key": "4429549", "vector": [], "sparse_vector": [], "title": "Dynamic physiological partitioning on a shared-nothing database cluster.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Traditional database management systems (DBMSs) running on powerful single-node servers are usually over-provisioned for most of their daily workloads and, because they do not show good-enough energy proportionality, waste a lot of energy while underutilized. A cluster of small (wimpy) servers, where its size can be dynamically adjusted to the current workload, offers better energy characteristics for those workloads. Yet, data migration, necessary to balance utilization among the nodes, is a non-trivial and time-consuming task that may consume the energy saved. For this reason, a sophisticated and easy to adjust partitioning scheme fostering dynamic reorganization is needed. In this paper, we adapt a technique originally created for SMP systems, called physiological partitioning, to distribute data among nodes that allows to easily repartition data without interrupting transactions. We dynamically partition DB tables based on the nodes' utilization and given energy constraints and compare our approach with physical partitioning and logical partitioning methods. To quantify possible energy saving and its conceivable drawback on query runtimes, we evaluate our implementation on an experimental cluster and compare the results w.r.t. performance and energy consumption. Depending on the workload, we can substantially save energy without sacrificing too much performance.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113359"}, {"primary_key": "4429550", "vector": [], "sparse_vector": [], "title": "Enabling generic keyword search over raw XML data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Classical XML keyword search based on the Lowest Common Ancestor (LCA) framework requires users to be well versed with data and semantic relationships between the query keywords to extract meaningful response, restricting its applicability. GKS (Generic Keyword Search), on the other hand, allows users to browse and navigate XML data without such constraints. GKS enables discovery of deeper insights (DI) in the XML data, found in the context of the search results. Such insights not only expose patterns hidden in the search results but also help users tune their queries, thus enabling the navigation of complex XML repositories with ease. We further show how, for a search query, different insights can be discovered from the data by varying a single parameter.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113410"}, {"primary_key": "4429551", "vector": [], "sparse_vector": [], "title": "On historical diagnosis of sensor streams.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "In this paper, we will examine the problem of historical storage and diagnosis of massive numbers of simultaneous streams. Such streams are common in very large sensor systems which collect many data streams simultaneously. For example, in a typical monitoring application, we may desire to determine specific abnormalities at sensor nodes or diagnose local regions of abnormal behavior. In other applications, a user may wish to query the streams for specific behavior of the data over arbitrary time horizons. This can be a very difficult task if it is not possible to store the voluminous sensor information at different nodes. In many cases, it is only possible to store aggregated data over different nodes. In this paper, we discuss the problem of storage-efficient monitoring and diagnosis of sensor networks with the use of summary representations. The goal of the summary representation is to providing worst-case guarantees on query functions computed over the sensor stream, while storing the streams compactly. We present experimental results on a number of real data sets showing the effectiveness of the approach.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113283"}, {"primary_key": "4429552", "vector": [], "sparse_vector": [], "title": "Big data: Old wine in new bottle?", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Without question, astounding advances in storage, communication, and computation have propelled us into a new world, the world of Big Data. The Big Data enthusiasts believe that it is a revolution that will transform how we live, work, and think, and it will underpin new waves of innovation and productivity growth.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113428"}, {"primary_key": "4429553", "vector": [], "sparse_vector": [], "title": "Data engineering in Asia: Unique technical challenges and opportunities.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Asia is in the midst of a historic transformation. Asia's per capita income is projected to rise sixfold and its share of global gross domestic product is expected to increase to 52 percent by 2050 [1]. Science and technology has been cited as one of the key pillars for the success of Asia's development [2].", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113429"}, {"primary_key": "4429554", "vector": [], "sparse_vector": [], "title": "How to stop under-utilization and love multicores.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Danica Poro<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hardware trends oblige software to overcome three major challenges against systems scalability: (1) taking advantage of the implicit/vertical parallelism within a core that is enabled through the aggressive micro-architectural features, (2) exploiting the explicit/horizontal parallelism provided by multicores, and (3) achieving predictively efficient execution despite the variability in communication latencies among cores on multisocket multicores. In this three hour tutorial, we shed light on the above three challenges and survey recent proposals to alleviate them. The first part of the tutorial describes the instruction- and data-level parallelism opportunities in a core coming from the hardware and software side. In addition, it examines the sources of under-utilization in a modern processor and presents insights and hardware/software techniques to better exploit the micro-architectural resources of a processor by improving cache locality at the right level of the memory hierarchy. The second part focuses on the scalability bottlenecks of database applications at the level of multicore and multisocket multicore architectures. It first presents a systematic way of eliminating such bottlenecks in online transaction processing workloads, which is based on minimizing unbounded communication, and shows several techniques that minimize bottlenecks in major components of database management systems. Then, it demonstrates the data and work sharing opportunities for analytical workloads, and reviews advanced scheduling mechanisms that are aware of non-uniform memory accesses and alleviate bandwidth saturation.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113419"}, {"primary_key": "4429555", "vector": [], "sparse_vector": [], "title": "The safest path via safe zones.", "authors": ["<PERSON><PERSON>", "Jianzhong Qi", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We define and study Euclidean and spatial network variants of a new path finding problem: given a set of safe zones, find paths that minimize the distance traveled outside the safe zones. In this problem, the entire space with the exception of the safe zones is unsafe, but passable, and it differs from problems that involve unsafe regions to be strictly avoided. As a result, existing algorithms are not effective solutions to the new problem.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113312"}, {"primary_key": "4429556", "vector": [], "sparse_vector": [], "title": "Executing queries over schemaless RDF databases.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent advances in Linked Data Management and the Semantic Web have led to a rapid increase in both the quantity as well as the variety of Web applications that rely on the SPARQL interface to query RDF data. Thus, RDF data management systems are increasingly exposed to workloads that are far more diverse and dynamic than what these systems were designed to handle. The problem is that existing systems rely on a workload-oblivious physical representation that has a fixed schema, which is not suitable for diverse and dynamic workloads. To address these issues, we propose a physical representation that is schemaless. The resulting flexibility enables an RDF dataset to be clustered based purely on the workload, which is key to achieving good performance through optimized I/O and cache utilization. Consequently, given a workload, we develop techniques to compute a good clustering of the database. We also design a new query evaluation model, namely, schemaless-evaluation that leverages this workload-aware clustering of the database whereby, with high probability, each tuple in the result set of a query is expected to be contained in at most one cluster. Our query evaluation model exploits this property to achieve better performance while ensuring fast generation of query plans without being hindered by the lack of a fixed physical schema.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113335"}, {"primary_key": "4429557", "vector": [], "sparse_vector": [], "title": "A comparison of adaptive radix trees and hash tables.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With prices of main memory constantly decreasing, people nowadays are more interested in performing their computations in main memory, and leave high I/O costs of traditional disk-based systems out of the equation. This change of paradigm, however, represents new challenges to the way data should be stored and indexed in main memory in order to be processed efficiently. Traditional data structures, like the venerable B-tree, were designed to work on disk-based systems, but they are no longer the way to go in main-memory systems, at least not in their original form, due to the poor cache utilization of the systems they run on. Because of this, in particular, during the last decade there has been a considerable amount of research on index data structures for main-memory systems. Among the most recent and most interesting data structures for main-memory systems there is the recently-proposed adaptive radix tree ARTful (ART for short). The authors of ART presented experiments that indicate that ART was clearly a better choice over other recent tree-based data structures like FAST and B+-trees. However, ART was not the first adaptive radix tree. To the best of our knowledge, the first was the Judy A<PERSON>y (<PERSON> for short), and a comparison between ART and Judy was not shown. Moreover, the same set of experiments indicated that only a hash table was competitive to ART. The hash table used by the authors of ART in their study was a chained hash table, but this kind of hash tables can be suboptimal in terms of space and performance due to their potentially high use of pointers. In this paper we present a thorough experimental comparison between ART, Judy, two variants of hashing via quadratic probing, and three variants of Cuckoo hashing. These hashing schemes are known to be very efficient. For our study we consider whether the data structures are to be used as a non-covering index (relying on an additional store), or as a covering index (covering key-value pairs). We consider both OLAP and OLTP scenarios. Our experiments strongly indicate that neither ART nor Judy are competitive to the aforementioned hashing schemes in terms of performance, and, in the case of ART, sometimes not even in terms of space.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113370"}, {"primary_key": "4429559", "vector": [], "sparse_vector": [], "title": "Understanding computer usage evolution.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The proliferation of computing devices in recent years has dramatically changed the way people work, play, communicate, and access information. The personal computer (PC) now has to compete with smartphones, tablets, and other devices for tasks it used to be the default device for. Understanding how PC usage evolves over time can help provide the best overall user experience for current customers, can help determine when they need brand new systems vs. upgraded components, and can inform future product design to better anticipate user needs.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113424"}, {"primary_key": "4429560", "vector": [], "sparse_vector": [], "title": "Online Frequent Episode Mining.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Chengkai Li", "<PERSON><PERSON>", "<PERSON> He"], "summary": "Frequent episode mining is a popular framework for discovering sequential patterns from sequence data. Previous studies on this topic usually process data offline in a batch mode. However, for fast-growing sequence data, old episodes may become obsolete while new useful episodes keep emerging. More importantly, in time-critical applications we need a fast solution to discovering the latest frequent episodes from growing data. To this end, we formulate the problem of Online Frequent Episode Mining (OFEM). By introducing the concept of last episode occurrence within a time window, our solution can detect new minimal episode occurrences efficiently, based on which all recent frequent episodes can be discovered directly. Additionally, a trie-based data structure, episode trie, is developed to store minimal episode occurrences in a compact way. We also formally prove the soundness and completeness of our solution and analyze its time as well as space complexity. Experiment results of both online and offline FEM on real data sets show the superiority of our solution.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113342"}, {"primary_key": "4429561", "vector": [], "sparse_vector": [], "title": "Transaction processing on confidential data using cipherbase.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cipherbase is a comprehensive database system that provides strong end-to-end data confidentiality through encryption. Cipherbase is based on a novel architecture that combines an industrial strength database engine (SQL Server) with lightweight processing over encrypted data that is performed in secure hardware. The overall architecture provides significant benefits over the state-of-the-art in terms of security, performance, and functionality. This paper presents a prototype of Cipherbase that uses FPGAs to provide secure processing and describes the system engineering details implemented to achieve competitive performance for transactional workloads. This includes hardware-software co-design issues (e.g. how to best offer parallelism), optimizations to hide the latency between the secure hardware and the main system, and techniques to cope with space inefficiencies. All these optimizations were carefully designed not to affect end-to-end data confidentiality. Our experiments with the TPC-C benchmark show that in the worst case when all data are strongly encrypted, Cipherbase achieves 40% of the throughput of plaintext SQL Server. In more realistic cases, if only critical data such as customer names are encrypted, the Cipherbase throughput is more than 90% of plaintext SQL Server.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113304"}, {"primary_key": "4429564", "vector": [], "sparse_vector": [], "title": "Time dependent transportation network models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Network data models are frequently used as a mechanism to solve wide range of problems typical for the GIS applications and transportation planning in particular. They do this by modelling the two most important aspects of such systems: the connectivity and the attribution. For a long time the attributes like the travel time, associated with a transportation network model have been considered static. With the advancement of the technology data vendors now have the capability to capture more accurate information about the speeds of streets at different times of the day and provide this data to customers. The network attributes are not static anymore but associated with a particular time instance (e.g time-dependent). In this paper we describe our time dependent network model tailored towards the need of transportation network modelling. Our solution is based on the existing database functionality (tables, joins, sorting algorithms) provided by a standard relational DBMS and has been implemented and tested and currently being shipped as a part of the ESRI ArcGIS 10.1 platform and all subsequent releases.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113383"}, {"primary_key": "4429565", "vector": [], "sparse_vector": [], "title": "Cache-oblivious scheduling of shared workloads.", "authors": ["<PERSON><PERSON>", "Lu<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Shared workload optimization is feasible if the set of tasks to be executed is known in advance, as is the case in updating a set of materialized views or executing an extract-transform-load workflow. In this paper, we consider data-intensive workloads with precedence constraints arising from data dependencies. While there has been previous work on identifying common subexpressions and task re-ordering to enable shared scans, in this paper we solve the problem of scheduling shared data-intensive workloads in a cache-oblivious way. Our solution relies on a novel formulation of precedence constrained scheduling with the additional constraint that once a data item is in the cache, all tasks that require this item should execute as soon as possible thereafter. We give an optimal algorithm using A* search over the space of possible orderings, and we propose efficient and effective heuristics that obtain nearly-optimal schedules in much less time. We present experimental results on real-life data warehouse workloads and the TCP-DS benchmark to validate our claims.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113339"}, {"primary_key": "4429566", "vector": [], "sparse_vector": [], "title": "In-memory BLU acceleration in IBM&apos;s DB2 and dashDB: Optimized for modern workloads and hardware architectures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Although the DRAM for main memories of systems continues to grow exponentially according to <PERSON>'s Law and to become less expensive, we argue that memory hierarchies will always exist for many reasons, both economic and practical, and in particular due to concurrent users competing for working memory to perform joins and grouping. We present the in-memory BLU Acceleration used in IBM's DB2 for Linux, UNIX, and Windows, and now also the dashDB cloud offering, which was designed and implemented from the ground up to exploit main memory but is not limited to what fits in memory and does not require manual management of what to retain in memory, as its competitors do. In fact, BLU Acceleration views memory as too slow, and is carefully engineered to work in higher levels of the system cache by keeping the data encoded and packed densely into bit-aligned vectors that can exploit SIMD instructions in processing queries. To achieve scalable multi-core parallelism, BLU assigns to each thread independent data structures, or partitions thereof, designed to have low synchronization costs, and doles out batches of values to threads. On customer workloads, BLU has improved performance on complex analytics queries by 10 to 50 times, compared to the legacy row-organized run-time, while also significantly simplifying database administration, shortening time to value, and improving data compression. UPDATE and DELETE performance was improved by up to 112 times with the new Cancun release of DB2 with BLU Acceleration, which also added Shadow Tables for high performance on mixed OLTP and BI analytics workloads, and extended DB2's High Availability Disaster Recovery (HADR) and SQL compatibility features to BLU's column-organized tables.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113372"}, {"primary_key": "4429567", "vector": [], "sparse_vector": [], "title": "Inferencing in information extraction: Techniques and applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Information extraction at Web scale has become one of the most important research topics in data management since major commercial search engines started incorporating knowledge in their search results a couple of years ago [1]. Users increasingly expect structured knowledge as answers to their search needs. Using Bing as an example, the result page for \"<PERSON>\" is full of structured knowledge facts, such as his birthday and awards. The research efforts towards improving the accuracy and coverage of such knowledge bases have led to significant advances in Information Extraction techniques [2], [3]. As the initial challenge of accurately extracting facts for popular entities are being addressed, more difficult challenges have emerged such as extending knowledge coverage to long tail entities and domains, understanding interestingness and usefulness of facts within a given context, and addressing information-seeking needs more directly and accurately. In this tutorial, we will survey the recent research efforts and provide an introduction to the techniques that address those challenges, and the applications that benefit from the adoption of those techniques. In particular, this tutorial will focus on a variety of techniques that can be broadly viewed as knowledge inferencing, i.e., combining multiple data sources and extraction techniques to verify existing knowledge and derive new knowledge. More specifically, we focus on four main categories of inferencing techniques: 1) deep natural language processing using machine learning techniques, 2) data cleaning using integrity constraints, 3) large-scale probabilistic reasoning, and 4) leveraging human expertise for domain knowledge extraction.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113420"}, {"primary_key": "4429569", "vector": [], "sparse_vector": [], "title": "The XDa-TA system for automated grading of SQL query assignments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Grading of student SQL queries is usually done by executing the query on sample datasets (which may be unable to catch many errors) and/or by manually comparing/checking a student query with the correct query (which can be tedious and error prone). In this demonstration we present the XDa-TA system which can be used by instructors and TAs for grading SQL query assignments automatically. Given one or more correct queries for an SQL assignment, the tool uses the XData system to automatically generate datasets that are designed specifically to catch common errors. The grading is then done by comparing the results of student queries with those of the correct queries against these generated datasets; instructors can optionally provide additional datasets for testing. The tool can also be used in a learning mode by students, where it can provide immediate feedback with hints explaining possible reasons for erroneous output. This tool could be of great value to instructors particularly, to instructors of MOOCs.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113403"}, {"primary_key": "4429570", "vector": [], "sparse_vector": [], "title": "ViSual: An HCI-inspired simulator for blending visual subgraph query construction and processing.", "authors": ["So<PERSON>v S<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In [3], we laid out the vision of a novel graph query processing paradigm, where visual subgraph query formulation is interleaved (or \"blended\") with query processing by exploiting the latency offered by the gui. Our recent attempts at implementing this vision [6], [7] do not provide any robust framework to systematically investigate the performance of this novel paradigm. This is because it is prohibitively expensive to engage a large number of users to formulate a large number of visual queries in order to measure the performance of blending query formulation with query processing. In this demonstration, we present a novel synthetic visual subgraph query simulator called ViSual that can evaluate the performance of this paradigm for a large number of visual subgraph queries without requiring a large number of users to formulate them. Specifically, it leverages principles from hci to quantify the gui latency that is necessary to realistically simulate blending of query formulation and query processing.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113406"}, {"primary_key": "4429572", "vector": [], "sparse_vector": [], "title": "Smooth Scan: Statistics-oblivious access paths.", "authors": ["Renata <PERSON>-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Query optimizers depend heavily on statistics representing column distributions to create efficient query plans. In many cases, though, statistics are outdated or non-existent, and the process of refreshing statistics is very expensive, especially for ad-hoc workloads on ever bigger data. This results in suboptimal plans that severely hurt performance. The main problem is that any decision, once made by the optimizer, is fixed throughout the execution of a query. In particular, each logical operator translates into a fixed choice of a physical operator at run-time. In this paper, we advocate for continuous adaptation and morphing of physical operators throughout their lifetime, by adjusting their behavior in accordance with the statistical properties of the data. We demonstrate the benefits of the new paradigm by designing and implementing an adaptive access path operator called Smooth Scan, which morphs continuously within the space of traditional index access and full table scan. Smooth Scan behaves similarly to an index scan for low selectivity; if selectivity increases, however, Smooth Scan progressively morphs its behavior toward a sequential scan. As a result, a system with Smooth Scan requires no access path decisions up front nor does it need accurate statistics to provide good performance. We implement Smooth Scan in PostgreSQL and, using both synthetic benchmarks as well as TPC-H, we show that it achieves robust performance while at the same time being statistics-oblivious.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113294"}, {"primary_key": "4429574", "vector": [], "sparse_vector": [], "title": "Advanced analytics on SAP HANA: Churn risk scoring using call network analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>ung", "<PERSON><PERSON>"], "summary": "We present a demonstration based on SAP HANA to show a novel approach to churn risk scoring. Our focus is customer retention within a telecommunications setting. The purpose of this demonstration is to help identify customers who should be targeted for a customer retention marketing campaign. The data analysis considers multiple factors - churn likelihood (based on incoming and outgoing communications), customer influence (based on social connections) and the average revenue per customer. The results are presented using skyline visualization and advanced UI techniques to easily and intuitively interpret the analysis.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113386"}, {"primary_key": "4429575", "vector": [], "sparse_vector": [], "title": "Open data challenges at Facebook.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "At Facebook, our data systems process huge volumes of data, ranging from hundreds of terabytes in memory to hundreds of petabytes on disk. We categorize our systems as \"small data\" or \"big data\" based on the type of queries they run. Small data refers to OLTP-like queries that process and retrieve a small amount of data, for example, the 1000s of objects necessary to render Facebook's personalized News Feed for each person. These objects are requested by their ids; indexes limit the amount of data accessed during a single query, regardless of the total volume of data. Big data refers to queries that process large amounts of data, usually for analysis: trouble-shooting, identifying trends, and making decisions. Big data stores are the workhorses for data analysis at Facebook. They grow by millions of events (inserts) per second and process tens of petabytes and hundreds of thousands of queries per day. In this tutorial, we will describe our data systems and the current challenges we face. We will lead a discussion on these challenges, approaches to solve them, and potential pitfalls. We hope to stimulate interest in solving these problems in the research community.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113415"}, {"primary_key": "4429576", "vector": [], "sparse_vector": [], "title": "Supporting hierarchical data in SAP HANA.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Managing hierarchies is an ever-recurring challenge for relational database systems. Through investigations of customer scenarios at SAP we found that today's RDBMSs still leave a lot to be desired in order to meet the requirements of typical applications. Our research puts a new twist on handling hierarchies in SQL-based systems. We present an approach for modeling hierarchical data natively, and we extend the SQL language with expressive constructs for creating, manipulating, and querying a hierarchy. The constructs can be evaluated efficiently by leveraging existing indexing and query processing techniques. We demonstrate the feasibility of our concepts with initial measurements on a HANA-based prototype.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113376"}, {"primary_key": "4429577", "vector": [], "sparse_vector": [], "title": "Towards a parameter-free and parallel itemset mining algorithm in linearithmic time.", "authors": ["<PERSON>", "<PERSON>.", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Extracting interesting patterns from large data stores efficiently is a challenging problem in many domains. In the data mining literature, pattern frequency has often been touted as a proxy for interestingness and has been leveraged as a pruning criteria to realize scalable solutions. However, while there exist many frequent pattern algorithms in the literature, all scale exponentially in the worst case, restricting their utility on very large data sets. Furthermore, as we theoretically argue in this article, the problem is very hard to approximate within a reasonable factor, with a polynomial time algorithm. As a counter point to this theoretical result, we present a practical algorithm called Localized Approximate Miner (LAM) that scales linearithmically with the input data. Instead of fully exploring the top of the search lattice to a user-defined point, as traditional mining algorithms do, we instead explore different parts of the complete lattice, efficiently. The key to this efficient exploration is the reliance on min-wise independent permutations to collect the data into highly similar subsets of a partition. It is straightforward to implement and scales to very large data sets. We illustrate its utility on a range of data sets, and demonstrate that the algorithm finds more patterns of higher utility in much less time than several state-of-the-art algorithms. Moreover, we realize a natural multi-level parallelization of LAM that further reduces runtimes by up to 193-fold when leveraging 256 CMP cores spanning 32 machines.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113357"}, {"primary_key": "4429578", "vector": [], "sparse_vector": [], "title": "Reasoning on web data: Algorithms and performance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Techniques for efficiently managing Semantic Web data have attracted significant interest from the data management and knowledge representation communities. A great deal of effort has been invested, especially in the database community, into algorithms and tools for efficient RDF query evaluation. However, the main interest of RDF lies in its blending of heterogeneous data and semantics. Simple RDF graphs can be seen as collections of facts, which may be further enriched with ontological schemas, or semantic constraints, based on which reasoning can be applied to infer new information. Taking into account this implicit information is crucial for answering queries.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113422"}, {"primary_key": "4429579", "vector": [], "sparse_vector": [], "title": "Fine-grained controversy detection in Wikipedia.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The advent of Web 2.0 gave birth to a new kind of application where content is generated through the collaborative contribution of many different users. This form of content generation is believed to generate data of higher quality since the \"wisdom of the crowds\" makes its way into the data. However, a number of specific data quality issues appear within such collaboratively generated data. Apart from normal updates, there are cases of intentional harmful changes known as vandalism as well as naturally occurring disagreements on topics which don't have an agreed upon viewpoint, known as controversies. While much work has focused on identifying vandalism, there has been little prior work on detecting controversies, especially at a fine granularity. Knowing about controversies when processing user-generated content is essential to understand the quality of the data and the trust that should be given to them. Controversy detection is a challenging task, since in the highly dynamic context of user updates, one needs to differentiate among normal updates, vandalisms and actual controversies. We describe a novel technique that finds these controversial issues by analyzing the edits that have been performed on the data over time. We apply the developed technique on Wikipedia, the world's largest known collaboratively generated database and we show that our approach has higher precision and recall than baseline approaches as well as is capable of finding previously unknown controversies.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113426"}, {"primary_key": "4429582", "vector": [], "sparse_vector": [], "title": "Making pattern queries bounded in big graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It is cost-prohibitive to find matches Q(G) of a pattern query Q in a big graph G. We approach this by fetching a small subgraph G Q of G such that Q(G Q ) = Q(G). We show that many practical patterns are effectively bounded under access constraints A commonly found in real life, such that G Q can be identified in time determined by Q and A only, independent of the size |G| of G. This holds no matter whether pattern queries are localized (e.g., via subgraph isomorphism) or non-localized (graph simulation). We provide algorithms to decide whether a pattern Q is effectively bounded, and if so, to generate a query plan that computes Q(G) by accessing G Q , in time independent of |G|. When Q is not effectively bounded, we give an algorithm to extend access constraints and make Q bounded in G. Using real-life data, we experimentally verify the effectiveness of the approach, e.g., about 60% of queries are effectively bounded for subgraph isomorphism, and for such queries our approach outperforms the conventional methods by 4 orders of magnitude.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113281"}, {"primary_key": "4429583", "vector": [], "sparse_vector": [], "title": "A hybrid private record linkage scheme: Separating differentially private synopses from matching records.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Private record linkage protocols allow multiple parties to exchange matching records, which refer to the same entities or have similar values, while keeping the non-matching ones secret. Conventional protocols are based on computationally expensive cryptographic primitives and therefore do not scale. To address these scalability issues, hybrid protocols have been recently proposed that combine differential privacy techniques with secure multiparty computation techniques. However, a drawback of such protocols is that they disclose to the parties both the matching records and the differentially private synopses of the datasets involved in the linkage. Consequently, differential privacy is no longer always satisfied. To address this issue, we propose a novel framework, which separates the private synopses from the matching records. The two parties do not access the synopses directly, but still use them to efficiently link records. We theoretically prove the security of our framework. In addition, we have developed a simple but effective strategy for releasing private synopses. Extensive experimental results show that our framework is superior to the existing methods in terms of both recall rate and efficiency.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113352"}, {"primary_key": "4429584", "vector": [], "sparse_vector": [], "title": "DiSCern: A diversified citation recommendation system for scientific queries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Seema Nagar"], "summary": "Performing literature survey for scholarly activities has become a challenging and time consuming task due to the rapid growth in the number of scientific articles. Thus, automatic recommendation of high quality citations for a given scientific query topic is immensely valuable. The state-of-the-art on the problem of citation recommendation suffers with the following three limitations. First, most of the existing approaches for citation recommendation require input in the form of either the full article or a seed set of citations, or both. Nevertheless, obtaining the recommendation for citations given a set of keywords is extremely useful for many scientific purposes. Second, the existing techniques for citation recommendation aim at suggesting prestigious and well-cited articles. However, we often need recommendation of diversified citations of the given query topic for many scientific purposes; for instance, it helps authors to write survey papers on a topic and it helps scholars to get a broad view of key problems on a topic. Third, one of the problems in the keyword based citation recommendation is that the search results typically would not include the semantically correlated articles if these articles do not use exactly the same keywords. To the best of our knowledge, there is no known citation recommendation system in the literature that addresses the above three limitations simultaneously. In this paper, we propose a novel citation recommendation system called DiSCern to precisely address the above research gap. DiSCern finds relevant and diversified citations in response to a search query, in terms of keyword(s) to describe the query topic, while using only the citation graph and the keywords associated with the articles, and no latent information. We use a novel keyword expansion step, inspired by community finding in social network analysis, in DiSCern to ensure that the semantically correlated articles are also included in the results. Our proposed approach primarily builds on the Vertex Reinforced Random Walk (VRRW) to balance prestige and diversity in the recommended citations. We demonstrate the efficacy of DiSCern empirically on two datasets: a large publication dataset of more than 1.7 million articles in computer science domain and a dataset of more than 29,000 articles in theoretical high-energy physics domain. The experimental results show that our proposed approach is quite efficient and it outperforms the state-of-the-art algorithms in terms of both relevance and diversity.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113314"}, {"primary_key": "4429586", "vector": [], "sparse_vector": [], "title": "Information at your Fingertips: Only a dream for enterprises?", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We review how the state of information technology has evolved for consumers vs. enterprises. We discuss some of the key challenges in enterprise search over structured data and suggest a few promising directions for the research community.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113266"}, {"primary_key": "4429588", "vector": [], "sparse_vector": [], "title": "Temporal Spatial-Keyword Top-k publish/subscribe.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Massive amount of data that are geo-tagged and associated with text information are being generated at an unprecedented scale. These geo-textual data cover a wide range of topics. Users are interested in receiving up-to-date tweets such that their locations are close to a user specified location and their texts are interesting to users. For example, a user may want to be updated with tweets near her home on the topic \"food poisoning vomiting.\" We consider the Temporal Spatial-Keyword Top-k Subscription (TaSK) query. Given a TaSK query, we continuously maintain up-to-date top-k most relevant results over a stream of geo-textual objects (e.g., geo-tagged Tweets) for the query. The TaSK query takes into account text relevance, spatial proximity, and recency of geo-textual objects in evaluating its relevance with a geo-textual object. We propose a novel solution to efficiently process a large number of TaSK queries over a stream of geotextual objects. We evaluate the efficiency of our approach on two real-world datasets and the experimental results show that our solution is able to achieve a reduction of the processing time by 70-80% compared with two baselines.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113289"}, {"primary_key": "4429589", "vector": [], "sparse_vector": [], "title": "Efficient metric indexing for similarity search.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The goal in similarity search is to find objects similar to a specified query object given a certain similarity criterion. Although useful in many areas, such as multimedia retrieval, pattern recognition, and computational biology, to name but a few, similarity search is not yet supported well by commercial DBMS. This may be due to the complex data types involved and the needs for flexible similarity criteria seen in real applications. We propose an efficient disk-based metric access method, the Space-filling curve and Pivot-based B + -tree (SPB-tree), to support a wide range of data types and similarity metrics. The SPB-tree uses a small set of so-called pivots to reduce significantly the number of distance computations, uses a space-filling curve to cluster the data into compact regions, thus improving storage efficiency, and utilizes a B + -tree with minimum bounding box information as the underlying index. The SPB-tree also employs a separate random access file to efficiently manage a large and complex data. By design, it is easy to integrate the SPB-tree into an existing DBMS. We present efficient similarity search algorithms and corresponding cost models based on the SPB-tree. Extensive experiments using real and synthetic data show that the SPB-tree has much lower construction cost, smaller storage size, and can support more efficient similarity queries with high accuracy cost models than is the case for competing techniques. Moreover, the SPB-tree scales sublinearly with growing dataset size.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113317"}, {"primary_key": "4429590", "vector": [], "sparse_vector": [], "title": "Answering why-not questions on spatial keyword top-k queries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Haibo Hu", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Large volumes of geo-tagged text objects are available on the web. Spatial keyword top-k queries retrieve k such objects with the best score according to a ranking function that takes into account a query location and query keywords. In this setting, users may wonder why some known object is unexpectedly missing from a result; and understanding why may aid users in retrieving better results. While spatial keyword querying has been studied intensively, no proposals exist for how to offer users explanations of why such expected objects are missing from results. We provide techniques that allow the revision of spatial keyword queries such that their results include one or more desired, but missing objects. In doing so, we adopt a query refinement approach to provide a basic algorithm that reduces the problem to a two-dimensional geometrical problem. To improve performance, we propose an index-based ranking estimation algorithm that prunes candidate results early. Extensive experimental results offer insight into design properties of the proposed techniques and suggest that they are efficient in terms of both running time and I/O cost.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113291"}, {"primary_key": "4429591", "vector": [], "sparse_vector": [], "title": "Data-driven crowdsourcing: Management, mining, and applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this 3-hour tutorial, we present the landscape of recent developments in data management and mining research, and survey a selected set of state-of-the-art works that significantly extended existing database reserach in order to incorporate and exploit the novel notion of \"crowdsourcing\" in a creative fashion. In particular, three speakers take turns to present the topics of human-powered database operations, crowdsourced data mining, and the application of crowdsourcing in social media, respectively.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113418"}, {"primary_key": "4429592", "vector": [], "sparse_vector": [], "title": "VENUS: Vertex-centric streamlined graph computation on a single PC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent studies show that disk-based graph computation on just a single PC can be as highly competitive as cluster-based computing systems on large-scale problems. Inspired by this remarkable progress, we develop VENUS, a disk-based graph computation system which is able to handle billion-scale problems efficiently on a commodity PC. VENUS adopts a novel computing architecture that features vertex-centric \"streamlined\" processing - the graph is sequentially loaded and the update functions are executed in parallel on the fly. VENUS deliberately avoids loading batch edge data by separating read-only structure data from mutable vertex data on disk. Furthermore, it minimizes random IOs by caching vertex data in main memory. The streamlined processing is realized with efficient sequential scan over massive structure data and fast feeding a large number of update functions. Extensive evaluation on large real-world and synthetic graphs has demonstrated the efficiency of VENUS. For example, VENUS takes just 8 minutes with hard disk for PageRank on the Twitter graph with 1.5 billion edges. In contrast, Spark takes 8.1 minutes with 50 machines and 100 CPUs, and GraphChi takes 13 minutes using fast SSD drive.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113362"}, {"primary_key": "4429593", "vector": [], "sparse_vector": [], "title": "Scalable parallelization of skyline computation for multi-core processors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The skyline is an important query operator for multi-criteria decision making. It reduces a dataset to only those points that offer optimal trade-offs of dimensions. In general, it is very expensive to compute. Recently, multicore CPU algorithms have been proposed to accelerate the computation of the skyline. However, they do not sufficiently minimize dominance tests and so are not competitive with state-of-the-art sequential algorithms. In this paper, we introduce a novel multicore skyline algorithm, Hybrid, which processes points in blocks. It maintains a shared, global skyline among all threads, which is used to minimize dominance tests while maintaining high throughput. The algorithm uses an efficiently-updatable data structure over the shared, global skyline, based on point-based partitioning. Also, we release a large benchmark of optimized skyline algorithms, with which we demonstrate on challenging workloads a 100-fold speedup over state-of-the-art multicore algorithms and a 10-fold speedup with 16 cores over state-of-the-art sequential algorithms.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113358"}, {"primary_key": "4429595", "vector": [], "sparse_vector": [], "title": "The solid-state drive technology, today and tomorrow.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Insoon Jo"], "summary": "This tutorial overviews flash memory solid-state drive technologies and recent advances in the field.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113416"}, {"primary_key": "4429596", "vector": [], "sparse_vector": [], "title": "Nearest neighborhood search in spatial databases.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a group version of the nearest neighbor (NN) query, called the nearest neighborhood (NNH) query, which aims to find the nearest group of points, instead of one nearest point. Given a set O of points, a query point q, and a ρ-radius circle C, the NNH query returns the nearest placement of C to q such that there are at least k points enclosed by C. We present a fast algorithm for processing the NNH query based on the incremental retrieval of nearest neighbors using the R-tree structure on O. Our solution includes several techniques, to efficiently maintain sets of retrieved nearest points and identify their validities in terms of the closeness constraint of their points. These techniques are devised from the unique characteristics of the NNH search problem. As a side product, we solve a new geometric problem, called the nearest enclosing circle (NEC) problem, which is of independent interest. We present a linear expected-time algorithm solving the NEC problem using the properties of the NEC similar to those of the smallest enclosing circle. We provide extensive experimental results, which show that our techniques can significantly improve the query performance.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113326"}, {"primary_key": "4429597", "vector": [], "sparse_vector": [], "title": "Searchlight: Context-aware predictive Continuous Querying of moving objects in symbolic space.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Increasingly, streaming positions from moving objects in blended indoor/outdoor spaces are used to deliver new types of real-time location-based services. To support such scenarios, this paper presents the Searchlight Graph (SLG) model and the associated Searchlight Continuous Query Processing Framework (CQPF) for (predictive) Continuous Query Processing (CQP) in symbolic indoor/outdoor spaces. The model captures both actual and predicted object movement, object-specific edge costs, and location/object context annotation with keywords, enabling context-aware (predictive) querying of both locations and objects. Furthermore, the paper proposes several types of continuous spatio-temporal queries, expressed in the declarative Searchlight Query Language (SLQL), along with novel query processing algorithms, and describes their implementation in the Searchlight CQPF. Finally, a novel location prediction algorithm is proposed. Extensive experimental studies show that Searchlight is scalable, efficient, and outperforms its main competitor.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113325"}, {"primary_key": "4429599", "vector": [], "sparse_vector": [], "title": "Personalized route recommendation using big trajectory data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When planning routes, drivers usually consider a multitude of different travel costs, e.g., distances, travel times, and fuel consumption. Different drivers may choose different routes between the same source and destination because they may have different driving preferences (e.g., time-efficient driving v.s. fuel-efficient driving). However, existing routing services support little in modeling multiple travel costs and personalization-they usually deliver the same routes that minimize a single travel cost (e.g., the shortest routes or the fastest routes) to all drivers. We study the problem of how to recommend personalized routes to individual drivers using big trajectory data. First, we provide techniques capable of modeling and updating different drivers' driving preferences from the drivers' trajectories while considering multiple travel costs. To recommend personalized routes, we provide techniques that enable efficient selection of a subset of trajectories from all trajectories according to a driver's preference and the source, destination, and departure time specified by the driver. Next, we provide techniques that enable the construction of a small graph with appropriate edge weights reflecting how the driver would like to use the edges based on the selected trajectories. Finally, we recommend the shortest route in the small graph as the personalized route to the driver. Empirical studies with a large, real trajectory data set from 52,211 taxis in Beijing offer insight into the design properties of the proposed techniques and suggest that they are efficient and effective.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113313"}, {"primary_key": "4429600", "vector": [], "sparse_vector": [], "title": "CDR-To-MoVis: Developing a Mobility Visualization System from CDR data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "CDR (Call Detail Records) data is more easily available than other network related data (such as GPS data) as most telecommunications service providers (TSPs) maintain such data. By analyzing it one can find mobility patterns of most of the population thus leading to efficient urban planning, disease and traffic control, etc. But its granularity is low as the latitude and longitude (lat-lon) of a cell tower is used as the current location of all mobile phones that are connected to the cell tower at that time. Granularity can range between 10s of metres to several kms depending on population density of a locality. This is one reason why, although there are many existing systems on visualizing mobility of people based on GPS data, there is hardly any existing system for CDR. We develop a Mobility Visualization System (MoVis) for visualizing mobility of people from their CDR records. First of all, given the CDR records of a user, we determine her stay regions (places where she stays for a significant amount of time). Trajectories of phone events (and lat-lon of cell towers) between stay regions are extracted as her trips. Start and end times of a trip are estimated using linear extrapolation. Based on the start and end times, temporal patterns are extracted. Trips with sufficient number of intermediate points are mapped to transport network that consists of train lines, bus routes and expressways. We use Kernel density estimation to visualize the most common path for a given origin and destination. Based on this we create a round-the-clock visualization of mobility of people over the entire city separately for weekdays and weekends. At the end we show the validation results.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113399"}, {"primary_key": "4429601", "vector": [], "sparse_vector": [], "title": "selP: Selective tracking and presentation of data provenance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Highly expressive declarative languages, such as Datalog, are now commonly used to model the operational logic of data-intensive applications. The typical complexity of such Datalog programs, and the large volume of data that they process, call for the tracking and presentation of data provenance. Provenance information is crucial for explaining and justifying the Datalog program results. However, the size of full provenance information is in many cases too large (and its concise representations are too complex) to allow its presentation to the user. To this end, we propose a demonstration of selP, a system that allows the selective presentation of provenance, based on user-specified top-k queries. We will demonstrate the usefulness of selP using a real-life program and data, in the context of Information Extraction.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113407"}, {"primary_key": "4429604", "vector": [], "sparse_vector": [], "title": "Scalable distributed transactions across heterogeneous stores.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Typical cloud computing systems provide highly scalable and fault-tolerant data stores that may sacrifice other features like general multi-item transaction support. Recently techniques to implement multi-item transactions in these types of systems have focused on transactions across homogeneous data stores. Since applications access data in heterogeneous storage systems for legacy or interoperability reasons, we propose an approach that enables multi-item transactions with snapshot isolation across multiple heterogeneous data stores using only a minimal set of commonly implemented features such as single item consistency, conditional updates, and the ability to store additional meta-data. We define an client-coordinated transaction commitment protocol that does not rely on a central coordinating infrastructure. The application can take advantage of the scalability and fault-tolerance characteristics of modern key-value stores and access existing data in them, and also have multi-item transactional access guarantees with little performance impact. We have implemented our design in a Java library called Cherry Garcia (CG), that supports data store abstractions to Windows Azure Storage (WAS), Google Cloud Storage (GCS) and our own high-performance key-value store called Tora.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113278"}, {"primary_key": "4429605", "vector": [], "sparse_vector": [], "title": "CliqueSquare in action: Flat plans for massively parallel RDF queries.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "RDF is an increasingly popular data model for many practical applications, leading to large volumes of RDF data; efficient RDF data management methods are crucial to allow applications to scale. We propose to demonstrate CliqueSquare, an RDF data management system built on top of a MapReduce-like infrastructure. The main technical novelty of CliqueSquare resides in its logical query optimization algorithm, guaranteed to find a logical plan as flat as possible for a given query, meaning: a plan having the smallest possible number of join operators on top of each other. CliqueSquare's ability to build flat plans allows it to take advantage of a parallel processing framework in order to shorten response times. We demonstrate loading and querying the data, with a particular focus on query optimization, and on the performance benefits of CliqueSquare's flat plans.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113394"}, {"primary_key": "4429606", "vector": [], "sparse_vector": [], "title": "Blind men and an elephant coalescing open-source, academic, and industrial perspectives on BigData.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This tutorial is organized in two parts. In the first half, we will present an overview of applications and services in the BigData ecosystem. We will use known distributed database and systems literature as landmarks to orient the attendees in this fast-evolving space. Throughout, we will contrast models of resource management, performance, and the constraints that shape the architectures of prominent systems. We will also discuss the role of academia and industry in the development of open-source infrastructure, with an emphasis on open problems and strategies for collaboration. We assume only basic familiarity with distributed systems. In the second half, we will delve into Apache Hadoop YARN. YARN (Yet Another Resource Negotiator) transformed Hadoop from a MapReduce engine to a general-purpose cluster scheduler. Since its introduction, it has been deployed in production and extended to support use cases beyond large-scale batch processing. The tutorial will present the active research and development supporting such heterogeneous workloads, with particular attention to multi-tenant scheduling. Topics include security, resource isolation, protocols, and preemption. This portion will be detailed, but accessible to anyone with a background in distributed systems and all attendees of the first half of the tutorial.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113417"}, {"primary_key": "4429608", "vector": [], "sparse_vector": [], "title": "Dynamic programming: The next step.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We fill two gaps in the literature. First, we give a comprehensive set of equivalences allowing reordering of grouping with non-inner joins. Second, we show how to incorporate the optimal placement of grouping into a state-of-the-art dynamic programming (DP)-based plan generator.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113343"}, {"primary_key": "4429609", "vector": [], "sparse_vector": [], "title": "A demonstration of Shahed: A MapReduce-based system for querying and visualizing satellite data.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Several space agencies such as NASA are continuously collecting datasets of earth dynamics-e.g., temperature, vegetation, and cloud coverage-through satellites. This data is stored in a publicly available archive for scientists and researchers and is very useful for studying climate, desertification, and land use change. The benefit of this data comes from its richness as it provides an archived history for over 15 years of satellite observations. Unfortunately, the use of such data is very limited due to the huge size of archives (> 500TB) and the limited capabilities of traditional applications. In this demo, we present Shah<PERSON>, an interactive system which provides an efficient way to index, query, and visualize satellite datasets available in NASA archive. Shah<PERSON> is composed of four main modules. The uncertainty module resolves data uncertainty imposed by the satellites. The indexing module organizes the data in a novel multi-resolution spatio-temporal index designed for satellite data. The querying module uses the indexes to answer both spatiotemporal selection and aggregate queries provided by the user. The visualization module generates images, videos, and multi-level images which gives an insight of data distribution and dynamics over time. This demo gives users a hands-on experience with <PERSON><PERSON> through a map-based web interface in which users can browse the available datasets using the map, issue spatiotemporal queries, and visualize the results as images or videos.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113397"}, {"primary_key": "4429610", "vector": [], "sparse_vector": [], "title": "SpatialHadoop: A MapReduce framework for spatial data.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper describes SpatialHadoop; a full-fledged MapReduce framework with native support for spatial data. SpatialHadoop is a comprehensive extension to Hadoop that injects spatial data awareness in each Hadoop layer, namely, the language, storage, MapReduce, and operations layers. In the language layer, SpatialHadoop adds a simple and expressive high level language for spatial data types and operations. In the storage layer, SpatialHadoop adapts traditional spatial index structures, Grid, R-tree and R+-tree, to form a two-level spatial index. SpatialHadoop enriches the MapReduce layer by two new components, SpatialFileSplitter and SpatialRecordReader, for efficient and scalable spatial data processing. In the operations layer, SpatialHadoop is already equipped with a dozen of operations, including range query, kNN, and spatial join. Other spatial operations are also implemented following a similar approach. Extensive experiments on real system prototype and real datasets show that SpatialHadoop achieves orders of magnitude better performance than Hadoop for spatial data processing.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113382"}, {"primary_key": "4429612", "vector": [], "sparse_vector": [], "title": "SHAHED: A MapReduce-based system for querying and visualizing spatio-temporal satellite data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Remote sensing data collected by satellites are now made publicly available by several space agencies. This data is very useful for scientists pursuing research in several applications including climate change, desertification, and land use change. The benefit of this data comes from its richness as it provides an archived history for over 15 years of satellite observations for natural phenomena such as temperature and vegetation. Unfortunately, the use of such data is very limited due to the huge size of archives (> 500TB) and the limited capabilities of traditional applications. This paper introduces SHAHED; a MapReduce-based system for querying, visualizing, and mining large scale satellite data. SHAHED considers both the spatial and temporal aspects of the data to provide efficient query processing at large scale. The core of SHAHED is composed of four main components. The uncertainty component recovers missing data in the input which comes from cloud coverage and satellite mis-alignment. The indexing component provides a novel multi-resolution quad-tree-based spatio-temporal index structure, which indexes satellite data efficiently with minimal space overhead. The querying component answers selection and aggregate queries in real-time using the constructed index. Finally, the visualization component uses MapReduce programs to generate heat map images and videos for user queries. A set of experiments running on a live system deployed on a cluster of machines show the efficiency of the proposed design. All the features supported by SHAHED are made accessible through an easy to use web interface that hides the complexity of the system and provides a nice user experience.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113427"}, {"primary_key": "4429613", "vector": [], "sparse_vector": [], "title": "Towards a web-scale data management ecosystem demonstrated by SAP HANA.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Over the years, data management has diversified and moved into multiple directions, mainly caused by a significant growth in the application space with different usage patterns, a massive change in the underlying hardware characteristics, and-last but not least-growing data volumes to be processed. A solution matching these constraints has to cope with a multidimensional problem space including techniques dealing with a large number of domain-specific data types, data and consistency models, deployment scenarios, and processing, storage, and communication infrastructures on a hardware level. Specialized database engines are available and are positioned in the market optimizing a particular dimension on the one hand while relaxing other aspects (e.g. web-scale deployment with relaxed consistency). Today it is common sense, that there is no single engine which can handle all the different dimensions equally well and therefore we have very good reasons to tackle this problem and optimize the dimensions with specialized approaches in a first step. However, we argue for a second step (reflecting in our opinion on the even harder problem) of a deep integration of individual engines into a single coherent and consistent data management ecosystem providing not only shared components but also a common understanding of the overall business semantics. More specifically, a data management ecosystem provides common \"infrastructure\" for software and data life cycle management, backup/recovery, replication and high availability, accounting and monitoring, and many other operational topics, where administrators and users expect a harmonized experience. More importantly from an application perspective however, customer experience teaches us to provide a consistent business view across all different components and the ability to seamlessly combine different capabilities. For example, within recent customer-based Internet of Things scenarios, a huge potential exists in combining graph-processing functionality with temporal and geospatial information and keywords extracted from high-throughput twitter streams. Using SAP HANA as the running example, we want to demonstrate what moving a set of individual engines and infra-structural components towards a holistic but also flexible data management ecosystem could look like. Although there are some solutions for some problems already visible on the horizon, we encourage the database research community in general to focus more on the Big Picture providing a holistic/integrated approach to efficiently deal with different types of data, with different access methods, and different consistency requirements-research in this field would push the envelope far beyond the traditional notion of data management.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113374"}, {"primary_key": "4429614", "vector": [], "sparse_vector": [], "title": "Asymmetric structure-preserving subgraph queries for large graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "So<PERSON>v S<PERSON>"], "summary": "One fundamental type of query for graph databases is subgraph isomorphism queries (a.k.a subgraph queries). Due to the computational hardness of subgraph queries coupled with the cost of managing massive graph data, outsourcing the query computation to a third-party service provider has been an economical and scalable approach. However, confidentiality is known to be an important attribute of Quality of Service (QoS) in Query as a Service (QaaS). In this paper, we propose the first practical private approach for subgraph query services, asymmetric structure-preserving subgraph query processing, where the data graph is publicly known and the query structure/topology is kept secret. Unlike other previous methods for subgraph queries, this paper proposes a series of novel optimizations that only exploit graph structures, not the queries. Further, we propose a robust query encoding and adopt the novel cyclic group based encryption so that query processing is transformed into a series of private matrix operations. Our experiments confirm that our techniques are efficient and the optimizations are effective.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113296"}, {"primary_key": "4429617", "vector": [], "sparse_vector": [], "title": "Accelerating aggregation using intra-cycle parallelism.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern CPUs have word width of 64 bits but real data values are usually represented using bits fewer than a CPU word. This underutilization of CPU at register level has motivated the recent development of bit-parallel algorithms that carry out data processing operations (e.g., filter scan) on CPU words packed with data values (e.g., 8 data values are packed into one 64-bit word). Bit-parallel algorithms fully unleash the intra-cycle parallelism of modern CPUs and they are especially attractive to main-memory column stores whose goal is to process data at the speed of the \"bare metal\". Main-memory column stores generally focus on analytical queries, where aggregation is a common operation. Current bit-parallel algorithms, however, have not covered aggregation yet. In this paper, we present a suite of bit-parallel algorithms to accelerate all standard aggregation operations: SUM, MIN, MAX, AVG, MEDIAN, COUNT. The algorithms are designed to fully leverage the intra-cycle parallelism in CPU cores when aggregating words of packed values. Experimental evaluation shows that our bit-parallel aggregation algorithms exhibit significant performance benefits compared with non-bit-parallel methods.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113292"}, {"primary_key": "4429618", "vector": [], "sparse_vector": [], "title": "STREAMCUBE: Hierarchical spatio-temporal hashtag clustering for event exploration over the Twitter stream.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Jiawei Han", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "What is happening around the world? When and where? Mining the geo-tagged Twitter stream makes it possible to answer the above questions in real-time. Although a single tweet can be short and noisy, proper aggregations of tweets can provide meaningful results. In this paper, we focus on hierarchical spatio-temporal hashtag clustering techniques. Our system has the following features: (1) Exploring events (hashtag clusters) with different space granularity. Users can zoom in and out on maps to find out what is happening in a particular area. (2) Exploring events with different time granularity. Users can choose to see what is happening today or in the past week. (3) Efficient single-pass algorithm for event identification, which provides human-readable hashtag clusters. (4) Efficient event ranking which aims to find burst events and localized events given a particular region and time frame. To support aggregation with different space and time granularity, we propose a data structure called STREAMCUBE, which is an extension of the data cube structure from the database community with spatial and temporal hierarchy. To achieve high scalability, we propose a divide-and-conquer method to construct the STREAMCUBE. To support flexible event ranking with different weights, we proposed a top-k based index. Different efficient methods are used to speed up event similarity computations. Finally, we have conducted extensive experiments on a real twitter data. Experimental results show that our framework can provide meaningful results with high scalability.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113425"}, {"primary_key": "4429619", "vector": [], "sparse_vector": [], "title": "Data crowdsourcing: Is it for real?", "authors": ["<PERSON>"], "summary": "The document was not made available for publication as part of the conference proceedings.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113267"}, {"primary_key": "4429620", "vector": [], "sparse_vector": [], "title": "Bump hunting in the dark: Local discrepancy maximization on graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of discrepancy maximization on graphs: given a set of nodes Q of an underlying graph G, we aim to identify a connected subgraph of G that contains many more nodes from Q than other nodes. This variant of the discrepancy-maximization problem extends the well-known notion of \"bump hunting\" in the Euclidean space. We consider the problem under two access models. In the unrestricted-access model, the whole graph G is given as input, while in the local-access model we can only retrieve the neighbors of a given node in G using a possibly slow and costly interface. We prove that the basic problem of discrepancy maximization on graphs is NP-hard, and empirically evaluate the performance of four heuristics for solving it. For the local-access model we consider three different algorithms that aim to recover a part of G large enough to contain an optimal solution, while using only a small number of calls to the neighbor-function interface. We perform a thorough experimental evaluation in order to understand the trade offs between the proposed methods and their dependencies on characteristics of the input graph.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113364"}, {"primary_key": "4429621", "vector": [], "sparse_vector": [], "title": "CliqueSquare: Flat plans for massively parallel RDF queries.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As increasing volumes of RDF data are being produced and analyzed, many massively distributed architectures have been proposed for storing and querying this data. These architectures are characterized first, by their RDF partitioning and storage method, and second, by their approach for distributed query optimization, i.e., determining which operations to execute on each node in order to compute the query answers. We present CliqueSquare, a novel optimization approach for evaluating conjunctive RDF queries in a massively parallel environment. We focus on reducing query response time, and thus seek to build flat plans, where the number of joins encountered on a root-to-leaf path in the plan is minimized. We present a family of optimization algorithms, relying on n-ary (star) equality joins to build flat plans, and compare their ability to find the flattest possibles. We have deployed our algorithms in a MapReduce-based RDF platform and demonstrate experimentally the interest of the flat plans built by our best algorithms.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113332"}, {"primary_key": "4429622", "vector": [], "sparse_vector": [], "title": "Size-Constrained Weighted Set Cover.", "authors": ["Lu<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we introduce a natural generalization of Weighted Set Cover and Maximum Coverage, called Size-Constrained Weighted Set Cover. The input is a collection of n elements, a collection of weighted sets over the elements, a size constraint k, and a minimum coverage fraction ŝ; the output is a sub-collection of up to k sets whose union contains at least ŝn elements and whose sum of weights is minimal. We prove the hardness of approximation of this problem, and we present efficient approximation algorithms with provable quality guarantees that are the best possible. In many applications, the elements are data records, and the set collection to choose from is derived from combinations (patterns) of attribute values. We provide optimization techniques for this special case. Finally, we experimentally demonstrate the effectiveness and efficiency of our solutions.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113341"}, {"primary_key": "4429623", "vector": [], "sparse_vector": [], "title": "Chronos: An elastic parallel framework for stream benchmark generation and simulation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the coming big data era, stress test to IT systems under extreme data volume is crucial to the adoption of computing technologies in every corner of the cyber world. Appropriately generated benchmark datasets provide the possibility for administrators to evaluate the capacity of the systems when real datasets hard obtained have not extreme cases. Traditional benchmark data generators, however, mainly target at producing relation tables of arbitrary size following fixed distributions. The output of such generators are insufficient when it is used to measure the stability of the architecture with extremely dynamic and heavy workloads, caused by complicated/hiden factors in the generation mechanism of real world, e.g. dependency between stocks in the trading market and collaborative human behaviors on the social network. In this paper, we present a new framework, called Chronos, to support new demands on streaming data benchmarking, by generating and simulating realistic and fast data streams in an elastic manner. Given a small group of samples with timestamps, Chronos reproduces new data streams with similar characteristics of the samples, preserving column-wise correlations, temporal dependency and order statistics of the snapshot distributions at the same time. To achieve such realistic requirements, we propose 1) a column decomposition optimization technique to partition the original relation table into small sub-tables with minimal correlation information loss, 2) a generative and extensible model based on Latent Dirichlet Allocation to capture temporal dependency while preserving order statistics of the snapshot distribution, and 3) a new generation and assembling method to efficiently build tuples following the expected distribution on the snapshots. To fulfill the vision of elasticity, we also present a new parallel stream data generation mechanism, facilitating distributed nodes to collaboratively generate tuples with minimal synchronization overhead and excellent load balancing. Our extensive experimental studies on real world data domains confirm the efficiency and effectiveness of Chronos on stream benchmark generation and simulation.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113276"}, {"primary_key": "4429624", "vector": [], "sparse_vector": [], "title": "EcoSky: Reducing vehicular environmental impact through eco-routing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Reduction in greenhouse gas emissions from transportation attracts increasing interest from governments, fleet managers, and individual drivers. Eco-routing, which enables drivers to use eco-friendly routes, is a simple and effective approach to reducing emissions from transportation. We present EcoSky, a system that annotates edges of a road network with time dependent and uncertain eco-weights using GPS data and that supports different types of eco-routing. Basic eco-routing returns the most eco-friendly routes; skyline eco-routing takes into account not only fuel consumption but also travel time and distance when computing eco-routes; and personalized eco-routing considers each driver's past behavior and accordingly suggests different routes to different drivers.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113389"}, {"primary_key": "4429625", "vector": [], "sparse_vector": [], "title": "Elaps: An efficient location-aware pub/sub system.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The prevalence of social networks and mobile devices has facilitated the real-time dissemination of local events such as sales, shows and exhibitions. To explore nearby events, mobile users can query a location based search engine for the desired data. However, operating under such a pull based model means that users may miss interesting events (because no explicit queries are issued) or processing/communication overheads may be high (because users have to continuously issue queries). In this demo, we present Elaps, an efficient location-aware publish/subscribe system that can effectively disseminate interesting events to moving users. Elaps is based on the push model and notifies mobile users instantly whenever there is a matching event around their locations. Through the demo, we will demonstrate that Elaps is scalable to a large number of subscriptions and events. Moreover, Elaps can effectively monitor the subscribers without missing any event matching, and incur low communication overhead.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113412"}, {"primary_key": "4429627", "vector": [], "sparse_vector": [], "title": "Predictive tree: An efficient index for predictive queries on road networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Predictive queries on moving objects offer an important category of location-aware services based on the objects' expected future locations. A wide range of applications utilize this type of services, e.g., traffic management systems, location-based advertising, and ride sharing systems. This paper proposes a novel index structure, named Predictive tree (P-tree), for processing predictive queries against moving objects on road networks. The predictive tree: (1) provides a generic infrastructure for answering the common types of predictive queries including predictive point, range, KNN, and aggregate queries, (2) updates the probabilistic prediction of the object's future locations dynamically and incrementally as the object moves around on the road network, and (3) provides an extensible mechanism to customize the probability assignments of the object's expected future locations, with the help of user defined functions. The proposed index enables the evaluation of predictive queries in the absence of the objects' historical trajectories. Based solely on the connectivity of the road network graph and assuming that the object follows the shortest route to destination, the predictive tree determines the reachable nodes of a moving object within a specified time window T in the future. The predictive tree prunes the space around each moving object in order to reduce computation, and increase system efficiency. Tunable threshold parameters control the behavior of the predictive trees by trading the maximum prediction time and the details of the reported results on one side for the computation and memory overheads on the other side. The predictive tree is integrated in the context of the iRoad system in two different query processing modes, namely, the precomputed query result mode, and the on-demand query result mode. Extensive experimental results based on large scale real and synthetic datasets confirm that the predictive tree achieves better accuracy compared to the existing related work, and scales up to support a large number of moving objects and heavy predictive query workloads.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113369"}, {"primary_key": "4429628", "vector": [], "sparse_vector": [], "title": "Robust clustering of multi-type relational data via a heterogeneous manifold ensemble.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "High-Order Co-Clustering (HOCC) methods have attracted high attention in recent years because of their ability to cluster multiple types of objects simultaneously using all available information. During the clustering process, HOCC methods exploit object co-occurrence information, i.e., inter-type relationships amongst different types of objects as well as object affinity information, i.e., intra-type relationships amongst the same types of objects. However, it is difficult to learn accurate intra-type relationships in the presence of noise and outliers. Existing HOCC methods consider the p nearest neighbours based on Euclidean distance for the intra-type relationships, which leads to incomplete and inaccurate intra-type relationships. In this paper, we propose a novel HOCC method that incorporates multiple subspace learning with a heterogeneous manifold ensemble to learn complete and accurate intra-type relationships. Multiple subspace learning reconstructs the similarity between any pair of objects that belong to the same subspace. The heterogeneous manifold ensemble is created based on two-types of intra-type relationships learnt using p-nearest-neighbour graph and multiple subspaces learning. Moreover, in order to make sure the robustness of clustering process, we introduce a sparse error matrix into matrix decomposition and develop a novel iterative algorithm. Empirical experiments show that the proposed method achieves improved results over the state-of-art HOCC methods for FScore and NMI.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113319"}, {"primary_key": "4429629", "vector": [], "sparse_vector": [], "title": "A location-aware publish/subscribe framework for parameterized spatio-textual subscriptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ji<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the rapid progress of mobile Internet and the growing popularity of smartphones, location-aware publish/subscribe systems have recently attracted significant attention. Different from traditional content-based publish/subscribe, subscriptions registered by subscribers and messages published by publishers include both spatial information and textual descriptions, and messages should be delivered to relevant subscribers whose subscriptions have high relevancy to the messages. To evaluate the relevancy between spatio-textual messages and subscriptions, we should combine the spatial proximity and textual relevancy. Since subscribers have different preferences - some subscribers prefer messages with high spatial proximity and some subscribers pay more attention to messages with high textual relevancy, it calls for new location-aware publish/subscribe techniques to meet various needs from different subscribers. In this paper, we allow subscribers to parameterize their subscriptions and study the location-aware publish/subscribe problem on parameterized spatio-textual subscriptions. One big challenge is to achieve high performance. To meet this requirement, we propose a filter-verification framework to efficiently deliver messages to relevant subscribers. In the filter step, we devise effective filters to prune large numbers of irreverent results and obtain some candidates. In the verification step, we verify the candidates to generate the answers. We propose three effective filters by integrating prefix filtering and spatial pruning techniques. Experimental results show our method achieves higher performance and better quality than baseline approaches.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113327"}, {"primary_key": "4429630", "vector": [], "sparse_vector": [], "title": "DualTable: A hybrid storage model for update optimization in Hive.", "authors": ["<PERSON><PERSON> Hu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Hive is the most mature and prevalent data warehouse tool providing SQL-like interface in the Hadoop ecosystem. It is successfully used in many Internet companies and shows its value for big data processing in traditional industries. However, enterprise big data processing systems as in Smart Grid applications usually require complicated business logics and involve many data manipulation operations like updates and deletes. Hive cannot offer sufficient support for these while preserving high query performance. Hive using the Hadoop Distributed File System (HDFS) for storage cannot implement data manipulation efficiently and Hive on HBase suffers from poor query performance even though it can support faster data manipulation. There is a project based on Hive issue Hive-5317 to support update operations, but it has not been finished in Hive's latest version. Since this ACID compliant extension adopts same data storage format on HDFS, the update performance problem is not solved. In this paper, we propose a hybrid storage model called DualTable, which combines the efficient streaming reads of HDFS and the random write capability of HBase. Hive on DualTable provides better data manipulation support and preserves query performance at the same time. Experiments on a TPC-H data set and on a real smart grid data set show that Hive on DualTable is up to 10 times faster than Hive when executing update and delete operations.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113381"}, {"primary_key": "4429631", "vector": [], "sparse_vector": [], "title": "Short text understanding through lexical-semantic analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Understanding short texts is crucial to many applications, but challenges abound. First, short texts do not always observe the syntax of a written language. As a result, traditional natural language processing methods cannot be easily applied. Second, short texts usually do not contain sufficient statistical signals to support many state-of-the-art approaches for text processing such as topic modeling. Third, short texts are usually more ambiguous. We argue that knowledge is needed in order to better understand short texts. In this work, we use lexical-semantic knowledge provided by a well-known semantic network for short text understanding. Our knowledge-intensive approach disrupts traditional methods for tasks such as text segmentation, part-of-speech tagging, and concept labeling, in the sense that we focus on semantics in all these tasks. We conduct a comprehensive performance evaluation on real-life data. The results show that knowledge is indispensable for short text understanding, and our knowledge-intensive approaches are effective in harvesting semantics of short texts.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113309"}, {"primary_key": "4429632", "vector": [], "sparse_vector": [], "title": "Answering regular path queries on workflow provenance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a novel approach for efficiently evaluating regular path queries over provenance graphs of workflows that may include recursion. The approach assumes that an execution g of a workflow G is labeled with query-agnostic reachability labels using an existing technique. At query time, given g, G and a regular path query R, the approach decomposes R into a set of subqueries R 1 , …, R k that are safe for G. For each safe subquery R i , G is rewritten so that, using the reachability labels of nodes in g, whether or not there is a path which matches R i between two nodes can be decided in constant time. The results of each safe subquery are then composed, possibly with some small unsafe remainder, to produce an answer to R. The approach results in an algorithm that significantly reduces the number of subqueries k over existing techniques by increasing their size and complexity, and that evaluates each subquery in time bounded by its input and output size. Experimental results demonstrate the benefit of this approach.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113299"}, {"primary_key": "4429633", "vector": [], "sparse_vector": [], "title": "PandaSearch: A fine-grained academic search engine for research documents.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the world of academia, research documents enable the sharing and dissemination of scientific discoveries. During these \"big data\" times, academic search engines are widely used to find the relevant research documents. Considering the domain of computer science, a researcher often inputs a query with a specific goal to find an algorithm or a theorem. However, to this date, the return result of most search engines is just as a list of related papers. Users have to browse the results, download the interesting papers and look for the desired information, which is obviously laborious and inefficient. In this paper, we present a novel academic search system, called PandaSearch, that returns the results with a fine-grained interface, where the results are well organized by different categories, such as definitions, theorems, lemmas, algorithms and figures. The key technical challenges in our system include the automatic identification and extraction of different parts in a research document, the discovery of the main topic phrases for a definition or a theorem, and the recommendation of related definitions or figures to elegantly satisfy the search intention of users. Based on this, we have built a user friendly search interface for users to conveniently explore the documents, and find the relevant information.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113388"}, {"primary_key": "4429634", "vector": [], "sparse_vector": [], "title": "SMART: A tool for analyzing and reconciling schema matching networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Tri Kurniawan Wijaya", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Schema matching supports data integration by establishing correspondences between the attributes of independently designed database schemas. In recent years, various tools for automatic pair-wise matching of schemas have been developed. Since the matching process is inherently uncertain, the correspondences generated by such tools are often validated by a human expert. In this work, we consider scenarios in which attribute correspondences are identified in a network of schemas and not only in a pairwise setting. Here, correspondences between different schemas are interrelated, so that incomplete and erroneous matching results propagate in the network and the validation of a correspondence by an expert has ripple effects. To analyse and reconcile such matchings in schema networks, we present the Schema Matching Analyzer and Reconciliation Tool (SMART). It allows for the definition of network-level integrity constraints for the matching and, based thereon, detects and visualizes inconsistencies of the matching. The tool also supports the reconciliation of a matching by guiding an expert in the validation process and by offering semi-automatic conflict-resolution techniques.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113408"}, {"primary_key": "4429636", "vector": [], "sparse_vector": [], "title": "Proof positive and negative in data cleaning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "One notoriously hard data cleaning problem is, given a database, how to precisely capture which value is correct (i.e., proof positive) or wrong (i.e., proof negative). Although integrity constraints have been widely studied to capture data errors as violations, the accuracy of data cleaning using integrity constraints has long been controversial. Overall they deem one fundamental problem: Given a set of data values that together forms a violation, there is no evidence of which value is proof positive or negative. Hence, it is known that integrity constraints themselves cannot guide dependable data cleaning. In this work, we introduce an automated method for proof positive and negative in data cleaning, based on Sherlock rules and reference tables. Given a tuple and reference tables, Sherlock rules tell us what attributes are proof positive, what attributes are proof negative and (possibly) how to update them. We study several fundamental problems associated with Sherlock rules. We also present efficient algorithms for cleaning data using Sherlock rules. We experimentally demonstrate that our techniques can not only annotate data with proof positive and negative, but also repair data when enough information is available.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113269"}, {"primary_key": "4429637", "vector": [], "sparse_vector": [], "title": "HaTen2: Billion-scale tensor decompositions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "How can we find useful patterns and anomalies in large scale real-world data with multiple attributes? For example, network intrusion logs, with (source-ip, target-ip, port-number, timestamp)? Tensors are suitable for modeling these multi-dimensional data, and widely used for the analysis of social networks, web data, network traffic, and in many other settings. However, current tensor decomposition methods do not scale for tensors with millions and billions of rows, columns and `fibers', that often appear in real datasets. In this paper, we propose HaTen2, a scalable distributed suite of tensor decomposition algorithms running on the MapReduce platform. By carefully reordering the operations, and exploiting the sparsity of real world tensors, HaTen2 dramatically reduces the intermediate data, and the number of jobs. As a result, using HaTen2, we analyze big real-world tensors that can not be handled by the current state of the art, and discover hidden concepts.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113355"}, {"primary_key": "4429638", "vector": [], "sparse_vector": [], "title": "Clustering to forecast sparse time-series data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Forecasting accurately is essential to successful inventory planning in retail. Unfortunately, there is not always enough historical data to forecast items individually- this is particularly true in e-commerce where there is a long tail of low selling items, and items are introduced and phased out quite frequently, unlike physical stores. In such scenarios, it is preferable to forecast items in well-designed groups of similar items, so that data for different items can be pooled together to fit a single model. In this paper, we first discuss the desiderata for such a grouping and how it differs from the traditional clustering problem. We then describe our approach which is a scalable local search heuristic that can naturally handle the constraints required in this setting, besides being capable of producing solutions competitive with well-known clustering algorithms. We also address the complementary problem of estimating similarity, particularly in the case of new items which have no past sales. Our solution is to regress the sales profile of items against their semantic features, so that given just the semantic features of a new item we can predict its relation to other items, in terms of as yet unobserved sales. Our experiments demonstrate both the scalability of our approach and implications for forecast accuracy.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113385"}, {"primary_key": "4429639", "vector": [], "sparse_vector": [], "title": "Finding top-k local users in geo-tagged social media data.", "authors": ["Jinling Jiang", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social network platforms and location-based services are increasingly popular in people's daily lives. The combination of them results in location-based social media where people are connected not only through the friendship in the social network but also by their geographical locations in reality. This duality makes it possible to query and make use of social media data in novel ways. In this work, we formulate a novel and useful problem called top-k local user search (TkLUS for short) from tweets with geo-tags. Given a location q, a distance r, and a set of keywords W, the TkLUS query finds the top-k users who have posted tweets relevant to the desired keywords in W at a place within the distance r from q. TkLUS queries are useful in many application scenarios such as friend recommendation, spatial decision, etc. We design a set of techniques to answer such queries efficiently. First, we propose two local user ranking methods that integrate text relevance and location proximity in a TkLUS query. Second, we construct a hybrid index under a scalable framework, which is aware of keywords as well as locations, to organize high volume geo-tagged tweets. Furthermore, we devise two algorithms for processing TkLUS queries. Finally, we conduct an experimental study using real tweet data sets to evaluate the proposed techniques. The experimental results demonstrate the efficiency, effectiveness and scalability of our proposals.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113290"}, {"primary_key": "4429640", "vector": [], "sparse_vector": [], "title": "Comprehensive and reliable crowd assessment algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Evaluating workers is a critical aspect of any crowdsourcing system. In this paper, we devise techniques for evaluating workers by finding confidence intervals on their error rates. Unlike prior work, we focus on \"conciseness\"-that is, giving as tight a confidence interval as possible. Conciseness is of utmost importance because it allows us to be sure that we have the best guarantee possible on worker error rate. Also unlike prior work, we provide techniques that work under very general scenarios, such as when not all workers have attempted every task (a fairly common scenario in practice), when tasks have non-boolean responses, and when workers have different biases for positive and negative tasks. We demonstrate conciseness as well as accuracy of our confidence intervals by testing them on a variety of conditions and multiple real-world datasets.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113284"}, {"primary_key": "4429641", "vector": [], "sparse_vector": [], "title": "Conservative or liberal? Personalized differential privacy.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Differential privacy is widely accepted as a powerful framework for providing strong, formal privacy guarantees for aggregate data analysis. A limitation of the model is that the same level of privacy protection is afforded for all individuals. However, it is common that the data subjects have quite different expectations regarding the acceptable level of privacy for their data. Consequently, differential privacy may lead to insufficient privacy protection for some users, while over-protecting others. We argue that by accepting that not all users require the same level of privacy, a higher level of utility can often be attained by not providing excess privacy to those who do not want it. We propose a new privacy definition called personalized differential privacy (PDP), a generalization of differential privacy in which users specify a personal privacy requirement for their data. We then introduce several novel mechanisms for achieving PDP. Our primary mechanism is a general one that automatically converts any existing differentially private algorithm into one that satisfies PDP. We also present a more direct approach for achieving PDP, inspired by the well-known exponential mechanism. We demonstrate our framework through extensive experiments on real and synthetic data.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113353"}, {"primary_key": "4429642", "vector": [], "sparse_vector": [], "title": "A framework for computation of popular paths from crowdsourced data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Directions and paths, as commonly provided by route guidance systems, are usually derived considering absolute metrics, e.g., finding the shortest path within the underlying road network. This demo presents a framework which uses crowdsourced geospatial data to obtain paths that do not only minimize travel time but also guide users along popular points of interest (POIs). By analyzing textual travel blog data and Flickr data, we define a measure for popularity of POIs. This measure is used as an additional cost criterion in the underlying road network graph. Furthermore, we propose an approach to reduce the problem of finding paths which maximize popularity while minimizing travel time to the computation of bicriterion pareto optimal paths. The presented framework allows users to specify origin and destination within a road network, returning the set of pareto optimal paths or a subset thereof if a desired number of POIs along the path has been specified. Each of the returned routes is enriched with representative Flickr images and textual information from travel blogs. The framework and its results show that the computed paths yield competitive solutions in terms of travel time while also providing more \"popular\" paths, making routing easier and more informative for the user.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113393"}, {"primary_key": "4429643", "vector": [], "sparse_vector": [], "title": "Meaningful keyword search in relational databases with large and complex schema.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Jaroslaw <PERSON>lich<PERSON>", "<PERSON><PERSON>"], "summary": "Keyword search over relational databases offers an alternative way to SQL to query and explore databases that is effective for lay users who may not be well versed in SQL or the database schema. This becomes more pertinent for databases with large and complex schemas. An answer in this context is a join tree spanning tuples containing the query's keywords. As there are potentially many answers to the query, and the user is often only interested in seeing the top-k answers, how to rank the answers based on their relevance is of paramount importance. We focus on the relevance of join as the fundamental means to rank answers. We devise means to measure relevance of relations and foreign keys in the schema over the information content of the database. This can be done offline with no need for external models. We compare the proposed measures against a gold standard we derive from a real workload over TPC-E and evaluate the effectiveness of our methods. Finally, we test the performance of our measures against existing techniques to demonstrate a marked improvement, and perform a user study to establish naturalness of the ranking of the answers.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113302"}, {"primary_key": "4429644", "vector": [], "sparse_vector": [], "title": "The DBMS - your big data sommelier.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "When addressing the problem of \"big\" data volume, preparation costs are one of the key challenges: the high costs for loading, aggregating and indexing data leads to a long data-to-insight time. In addition to being a nuisance to the end-user, this latency prevents real-time analytics on \"big\" data. Fortunately, data often comes in semantic chunks such as files that contain data items that share some characteristics such as acquisition time or location. A data management system that exploits this trait can significantly lower the data preparation costs and the associated data-to-insight time by only investing in the preparation of the relevant chunks. In this paper, we develop such a system as an extension of an existing relational DBMS (MonetDB). To this end, we develop a query processing paradigm and data storage model that are partial-loading aware. The result is a system that can make a 1.2 TB dataset (consisting of 4000 chunks) ready for querying in less than 3 minutes on a single server-class machine while maintaining good query processing performance.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113361"}, {"primary_key": "4429645", "vector": [], "sparse_vector": [], "title": "Bi-temporal Timeline Index: A data structure for Processing Queries on bi-temporal data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Following the adoption of basic temporal features in the SQL:2011 standard, there has been a tremendous interest within the database industry in supporting bi-temporal features, as a significant number of real-life workloads would greatly benefit from efficient temporal operations. However, current implementations of bi-temporal storage systems and operators are far from optimal. In this paper, we present the Bi-temporal Timeline Index, which supports a broad range of temporal operators and exploits the special properties of an in-memory column store database system. Comprehensive performance experiments with the TPC-BiH benchmark show that algorithms based on the Bi-temporal Timeline Index outperform significantly both existing commercial database systems and state-of-the-art data structures from research.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113307"}, {"primary_key": "4429647", "vector": [], "sparse_vector": [], "title": "Progressive diversification for column-based data exploration platforms.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "In Data Exploration platforms, diversification has become an essential method for extracting representative data, which provide users with a concise and meaningful view of the results to their queries. However, the benefits of diversification are achieved at the expense of an additional cost for the post-processing of query results. For high dimensional large result sets, the cost of diversification is further escalated due to massive distance computations required to evaluate the similarity between results. To address that challenge, in this paper we propose the Progressive Data Diversification (pDiverse) scheme. The main idea underlying pDiverse is to utilize partial distance computation to reduce the amount of processed data. Our extensive experimental results on both synthetic and real data sets show that our proposed scheme outperforms existing diversification methods in terms of both I/O and CPU costs.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113295"}, {"primary_key": "4429649", "vector": [], "sparse_vector": [], "title": "Efficient structural bulk updates on the Pre/Dist/Size XML encoding.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In order to manage XML documents, native XML databases use specific encodings that map the hierarchical structure of a document to a flat representation. Several encodings have been proposed that differ in terms of their support for certain query workloads. While some encodings are optimized for query processing, others focus on data manipulation. For example, the Pre/Dist/Size XML encoding has been designed to support queries over all XPath axes efficiently, but processing atomic updates in XML documents can be costly. In this paper, we present a technique, so-called structural bulk updates, that works in concert with the XQuery Update Facility to support efficient updates on the Pre/Dist/Size encoding. We demonstrate the benefits of our technique in a detailed performance evaluation based on the XMark benchmark.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113305"}, {"primary_key": "4429650", "vector": [], "sparse_vector": [], "title": "Seamlessly integrating disk and tape in a multi-tiered distributed file system.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The explosion of data volumes in enterprise environments and limited budgets have triggered the need for multi-tiered storage systems. With the bulk of the data being extremely infrequently accessed, tape is a natural fit for storing such data. In this paper we present our approach to a file storage system that seamlessly integrates disk and tape, enabling a bottomless and cost-effective storage architecture that can scale to accommodate Big Data requirements. The proposed system offers access to data through a POSIX filesystem interface under a single global namespace, optimizing the placement of data across disk and tape tiers. Using a self-contained, standardized and open filesystem format on the removable tape media, the proposed system avoids dependence on proprietary software and external metadata servers to access the data stored on tape. By internally managing the tape tier resources, such as tape drives and cartridges, the system relieves the user from the burden of dealing with the complexities of tape storage. Our implementation, which is based on the GPFS and LTFS filesystems, demonstrates the applicability of the proposed architecture in real-world environments. Our experimental evaluation has shown that this is a very promising approach in terms scalability, performance and manageability. The proposed system has been productized by IBM as LTFS Enterprise Edition.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113380"}, {"primary_key": "4429651", "vector": [], "sparse_vector": [], "title": "Generating reading orders over document collections.", "authors": ["Georgia Koutrika", "<PERSON><PERSON>", "<PERSON>"], "summary": "Given a document collection, existing systems allow users to browse the collection or perform searches that return lists of documents ranked based on their relevance to the user query. While these approaches work fine when a user is trying to locate specific documents, they are insufficient when users need to access the pertinent documents in some logical order, for example for learning or editorial purposes. We present a system that automatically organizes a collection of documents in a tree from general to more specific documents, and allows a user to choose a reading sequence over the documents. This a novel way to content consumption that departs from the typical ranked lists of documents based on their relevance to a user query and from static navigational interfaces. We present a set of algorithms that solve the problem and we evaluate their performance as well as the reading trees generated.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113310"}, {"primary_key": "4429653", "vector": [], "sparse_vector": [], "title": "Oracle Database In-Memory: A dual format in-memory database.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sanket Hase", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Oracle Database In-Memory Option allows Oracle to function as the industry-first dual-format in-memory database. Row formats are ideal for OLTP workloads which typically use indexes to limit their data access to a small set of rows, while column formats are better suited for Analytic operations which typically examine a small number of columns from a large number of rows. Since no single data format is ideal for all types of workloads, our approach was to allow data to be simultaneously maintained in both formats with strict transactional consistency between them.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113373"}, {"primary_key": "4429654", "vector": [], "sparse_vector": [], "title": "Evolving the architecture of SQL Server for modern hardware trends.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The basic architecture of SQL Server, as well as other major database systems, goes back to a time when main memories were (very) small, data lived on disk, machines had a single (slow) processor, and OLTP was the only workload that mattered. This is not an optimal design for today's environment with large main memories, plenty of cores, and where transactional and analytical processing are equally important. To adapt to these trends and take advantage of the opportunities they offer SQL Server has added support for column store indexes and in-memory tables over the last two releases. The two features are aimed at dramatically improving performance on analytical and transactional workloads, respectively. This paper gives an overview of the design of the two features and the performance improvements they provide.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113371"}, {"primary_key": "4429656", "vector": [], "sparse_vector": [], "title": "Real time personalized search on social networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Internet users are shifting from searching on traditional media to social network platforms (SNPs) to retrieve up-to-date and valuable information. SNPs have two unique characteristics: frequent content update and small world phenomenon. However, existing works are not able to support these two features simultaneously. To address this problem, we develop a general framework to enable real time personalized top-k query. Our framework is based on a general ranking function that incorporates time freshness, social relevance and textual similarity. To ensure efficient update and query processing, there are two key challenges. The first is to design an index structure that is update-friendly while supporting instant query processing. The second is to efficiently compute the social relevance in a complex graph. To address these challenges, we first design a novel 3D cube inverted index to support efficient pruning on the three dimensions simultaneously. Then we devise a cube based threshold algorithm to retrieve the top-k results, and propose several pruning techniques to optimize the social distance computation, whose cost dominates the query processing. Furthermore, we optimize the 3D index via a hierarchical partition method to enhance our pruning on the social dimension. Extensive experimental results on two real world large datasets demonstrate the efficiency and the robustness of our proposed solution.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113321"}, {"primary_key": "4429657", "vector": [], "sparse_vector": [], "title": "Scaling up copy detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent research shows that copying is prevalent for Deep-Web data and considering copying can significantly improve truth finding from conflicting values. However, existing copy detection techniques do not scale for large sizes and numbers of data sources, so truth finding can be slowed down by one to two orders of magnitude compared with the corresponding techniques that do not consider copying. In this paper, we study how to improve scalability of copy detection on structured data. Our algorithm builds an inverted index for each shared value and processes the index entries in decreasing order of how much the shared value can contribute to the conclusion of copying. We show how we use the index to prune the data items we consider for each pair of sources, and to incrementally refine our results in iterative copy detection. We also apply a sampling strategy with which we are able to further reduce copy-detection time while still obtaining very similar results as on the whole data set. Experiments on various real data sets show that our algorithm can reduce the time for copy detection by two to three orders of magnitude; in other words, truth finding can benefit from copy detection with very little overhead.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113275"}, {"primary_key": "4429659", "vector": [], "sparse_vector": [], "title": "PIE: Approximate interleaving event matching over sequences.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Most of the recent complex event semantics is based on regular expressions, extended with additional filters such as window constraints. We observe that many applications today require parallel (interleaving) event patterns. Moreover, we observe that this matching needs to be approximate in terms of event orders and missing events. We first propose the query semantics. Then we devise a foundation algorithm, on top of which two optimization techniques are proposed. Finally, we perform a comprehensive experimental evaluation using three real-world datasets in different domains and synthetic datasets.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113330"}, {"primary_key": "4429661", "vector": [], "sparse_vector": [], "title": "Growing the charging station network for electric vehicles with trajectory data analytics.", "authors": ["Yanhua Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Electric vehicles (EVs) have undergone an explosive increase over recent years, due to the unparalleled advantages over gasoline cars in green transportation and cost efficiency. Such a drastic increase drives a growing need for widely deployed publicly accessible charging stations. Thus, how to strategically deploy the charging stations and charging points becomes an emerging and challenging question to urban planners and electric utility companies. In this paper, by analyzing a large scale electric taxi trajectory data, we make the first attempt to investigate this problem. We develop an optimal charging station deployment (OCSD) framework that takes the historical EV taxi trajectory data, road map data, and existing charging station information as input, and performs optimal charging station placement (OCSP) and optimal charging point assignment (OCPA). The OCSP and OCPA optimization components are designed to minimize the average time to the nearest charging station, and the average waiting time for an available charging point, respectively. To evaluate the performance of our OCSD framework, we conduct experiments on one-month real EV taxi trajectory data. The evaluation results demonstrate that our OCSD framework can achieve a 26%–94% reduction rate on average time to find a charging station, and up to two orders of magnitude reduction on waiting time before charging, over baseline methods. Moreover, our results reveal interesting insights in answering the question: \"Super or small stations?\": When the number of deployable charging points is sufficiently large, more small stations are preferred; and when there are relatively few charging points to deploy, super stations is a wiser choice.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113384"}, {"primary_key": "4429662", "vector": [], "sparse_vector": [], "title": "Quick-motif: An efficient and scalable framework for exact motif discovery.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Z<PERSON>gu<PERSON> Gong"], "summary": "Discovering motifs in sequence databases has been receiving abundant attentions from both database and data mining communities, where the motif is the most correlated pair of subsequences in a sequence object. Motif discovery is expensive for emerging applications which may have very long sequences (e.g., million observations per sequence) or the queries arrive rapidly (e.g., per 10 seconds). Prior works cannot offer fast correlation computations and prune subsequence pairs at the same time, as these two techniques require different orderings on examining subsequence pairs. In this work, we propose a novel framework named Quick-Motif which adopts a two-level approach to enable batch pruning at the outer level and enable fast correlation calculation at the inner level. We further propose two optimization techniques for the outer and the inner level. In our experimental study, our method is up to 3 orders of magnitude faster than the state-of-the-art methods.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113316"}, {"primary_key": "4429663", "vector": [], "sparse_vector": [], "title": "MARS: A multi-aspect Recommender system for Point-of-Interest.", "authors": ["<PERSON><PERSON>", "G<PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the pervasive use of GPS-enabled smart phones, location-based services, e.g., Location Based Social Networking (LBSN) have emerged . Point-of-Interests (POIs) Recommendation, as a typical component in LBSN, provides additional values to both customers and merchants in terms of user experience and business turnover. Existing POI recommendation systems mainly adopt Collaborative Filtering (CF), which only exploits user given ratings (i.e., user overall evaluation) about a merchant while regardless of the user preference difference across multiple aspects, which exists commonly in real scenarios. Meanwhile, besides ratings, most LBSNs also provide the review function to allow customers to give their opinions when dealing with merchants, which is often overlooked in these recommender systems. In this demo, we present MARS, a novel POI recommender system based on multi-aspect user preference learning from reviews by using utility theory. We first introduce the organization of our system, and then show how the user preferences across multiple aspects are integrated into our system alongside several case studies of mining user preference and POI recommendations.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113395"}, {"primary_key": "4429665", "vector": [], "sparse_vector": [], "title": "On random walk based graph sampling.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Lu <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Random walk based graph sampling has been recognized as a fundamental technique to collect uniform node samples from a large graph. In this paper, we first present a comprehensive analysis of the drawbacks of three widely-used random walk based graph sampling algorithms, called re-weighted random walk (RW) algorithm, Metropolis-Hastings random walk (MH) algorithm and maximum-degree random walk (MD) algorithm. Then, to address the limitations of these algorithms, we propose two general random walk based algorithms, named rejection-controlled Metropolis-Hastings (RCMH) algorithm and generalized maximum-degree random walk (GMD) algorithm. We show that RCMH balances the tradeoff between the limitations of RW and MH, and GMD balances the tradeoff between the drawbacks of RW and MD. To further improve the performance of our algorithms, we integrate the so-called delayed acceptance technique and the non-backtracking random walk technique into RCMH and GMD respectively. We conduct extensive experiments over four real-world datasets, and the results demonstrate the effectiveness of the proposed algorithms.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113345"}, {"primary_key": "4429666", "vector": [], "sparse_vector": [], "title": "Network motif discovery: A GPU approach.", "authors": ["<PERSON><PERSON> Lin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The identification of network motifs has important applications in numerous domains, such as pattern detection in biological networks and graph analysis in digital circuits. However, mining network motifs is computationally challenging, as it requires enumerating subgraphs from a real-life graph, and computing the frequency of each subgraph in a large number of random graphs. In particular, existing solutions often require days to derive network motifs from biological networks with only a few thousand vertices. To address this problem, this paper presents a novel study on network motif discovery using Graphical Processing Units (GPUs). The basic idea is to employ GPUs to parallelize a large number of subgraph matching tasks in computing subgraph frequencies from random graphs, so as to reduce the overall computation time of network motif discovery. We explore the design space of GPU-based subgraph matching algorithms, with careful analysis of several crucial factors that affect the performance of GPU programs. Based on our analysis, we develop a GPU-based solution that (i) considerably differs from existing CPU-based methods, and (ii) exploits the strengths of GPUs in terms of parallelism while mitigating their limitations in terms of the computation power per GPU core. With extensive experiments on a variety of biological networks, we show that our solution is up to two orders of magnitude faster than the best CPU-based approach, and is around 20 times more cost-effective than the latter, when taking into account the monetary costs of the CPU and GPUs used.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113337"}, {"primary_key": "4429668", "vector": [], "sparse_vector": [], "title": "SMAS: A smart meter data analytics system.", "authors": ["<PERSON><PERSON><PERSON>", "Lu<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Smart electricity meters are replacing conventional meters worldwide and have enabled a new application domain: smart meter data analytics. In this paper, we introduce SMAS, our smart meter analytics system, which demonstrates the actionable insight that consumers and utilities can obtain from smart meter data. Notably, we implemented SMAS inside a relational database management system using open source tools: PostgreSQL and the MADLib machine learning toolkit. In the proposed demonstration, conference attendees will interact with SMAS as electricity providers, consultants and consumers, and will perform various analyses on real data sets.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113405"}, {"primary_key": "4429669", "vector": [], "sparse_vector": [], "title": "LearningAssistant: A novel learning resource recommendation system.", "authors": ["<PERSON><PERSON>", "Georgia Koutrika", "<PERSON><PERSON>"], "summary": "Reading online content for educational, learning, training or recreational purposes has become a very popular activity. While reading, people may have difficulty understanding a passage or wish to learn more about the topics covered by it, hence they may naturally seek additional or supplementary resources for the particular passage. These resources should be close to the passage both in terms of the subject matter and the reading level. However, using a search engine to find such resources interrupts the reading flow. It is also an inefficient, trial-and-error process because existing web search and recommendation systems do not support large queries, they do not understand semantic topics, and they do not take into account the reading level of the original document a person is reading. In this demo, we present LearningAssistant, a novel system that enables online reading material to be smoothly enriched with additional resources that can supplement or explain any passage from the original material for a reader on demand. The system facilitates the learning process by recommending learning resources (documents, videos, etc) for selected text passages of any length. The recommended resources are ranked based on two criteria (a) how they match the different topics covered within the selected passage, and (b) the reading level of the original text where the selected passage comes from. User feedback from students who use our system in two real pilots, one with a high school and one with a university, for their courses suggest that our system is promising and effective.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113392"}, {"primary_key": "4429671", "vector": [], "sparse_vector": [], "title": "Efficient secure similarity computation on encrypted trajectory data.", "authors": ["<PERSON>", "<PERSON>", "Lu <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Outsourcing database to clouds is a scalable and cost-effective way for large scale data storage, management, and query processing. Trajectory data contain rich spatio-temporal relationships and reveal many forms of individual sensitive information (e.g., home address, health condition), which necessitate them to be encrypted before being outsourced for privacy concerns. However, efficient query processing over encrypted trajectory data is a very challenging task. Though some achievements have been reported very recently for simple queries (e.g., SQL queries, kNN queries) on encrypted data, there is rather limited progress on secure evaluation of trajectory queries because they are more complex and need special treatment. In this paper, we focus on secure trajectory similarity computation that is the cornerstone of secure trajectory query processing. More specifically, we propose an efficient solution to securely compute the similarity between two encrypted trajectories, which reveals nothing about the trajectories, but the final result. We theoretically prove that our solution is secure against the semi-honest adversaries model as all the intermediate information in our protocols can be simulated in polynomial time. Finally we empirically study the efficiency of the proposed method, which demonstrates the feasibility of our solution.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113273"}, {"primary_key": "4429672", "vector": [], "sparse_vector": [], "title": "Bounded Quadrant System: Error-bounded trajectory compression on the go.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Long-term location tracking, where trajectory compression is commonly used, has gained high interest for many applications in transport, ecology, and wearable computing. However, state-of-the-art compression methods involve high space-time complexity or achieve unsatisfactory compression rate, leading to rapid exhaustion of memory, computation, storage and energy resources. We propose a novel online algorithm for error-bounded trajectory compression called the Bounded Quadrant System (BQS), which compresses trajectories with extremely small costs in space and time using convex-hulls. In this algorithm, we build a virtual coordinate system centered at a start point, and establish a rectangular bounding box as well as two bounding lines in each of its quadrants. In each quadrant, the points to be assessed are bounded by the convex-hull formed by the box and lines. Various compression error-bounds are therefore derived to quickly draw compression decisions without expensive error computations. In addition, we also propose a light version of the BQS version that achieves O(1) complexity in both time and space for processing each point to suit the most constrained computation environments. Furthermore, we briefly demonstrate how this algorithm can be naturally extended to the 3-D case. Using empirical GPS traces from flying foxes, cars and simulation, we demonstrate the effectiveness of our algorithm in significantly reducing the time and space complexity of trajectory compression, while greatly improving the compression rates of the state-of-the-art algorithms (up to 47%). We then show that with this algorithm, the operational time of the target resource-constrained hardware platform can be prolonged by up to 41%.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113350"}, {"primary_key": "4429673", "vector": [], "sparse_vector": [], "title": "Multi-Constrained Graph Pattern Matching in large-scale contextual social graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph Pattern Matching (GPM) plays a significant role in social network analysis, which has been widely used in, for example, experts finding, social community mining and social position detection. Given a pattern graph G Q and a data graph G D , a GPM algorithm finds those subgraphs, G M , that match G Q in G D . However, the existing GPM methods do not consider the multiple constraints on edges in G Q , which are commonly exist in various applications such as, crowdsourcing travel, social network based e-commerce and study group selection, etc. In this paper, we first conceptually extend Bounded Simulation to Multi-Constrained Simulation (MCS), and propose a novel NP-Complete Multi-Constrained Graph Pattern Matching (MC-GPM) problem. Then, to address the efficiency issue in large-scale MC-GPM, we propose a new concept called Strong Social Component (SSC), consisting of participants with strong social connections. We also propose an approach to identify SSCs, and propose a novel index method and a graph compression method for SSC. Moreover, we devise a heuristic algorithm to identify MC-GPM results effectively and efficiently without decompressing graphs. An extensive empirical study on five real-world large-scale social graphs has demonstrated the effectiveness, efficiency and scalability of our approach.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113297"}, {"primary_key": "4429674", "vector": [], "sparse_vector": [], "title": "High performance temporal indexing on modern hardware.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Transaction time databases can be put to a number of valuable uses, auditing, regulatory compliance, readable backups, and enabling multi-version concurrency control. While additional storage for retaining multiple versions is unavoidable, compression and the declining cost of disk storage largely removes that impediment to supporting multi-version data. Not clear has been whether effective indexing of historical versions, can be achieved at high performance. The current paper shows how temporal indexing can exploit the latch-free infrastructure provided for the Bw-tree by the LLAMA cache/storage subsystem to support high performance. Further, it demonstrates how the LLAMA mapping table can be exploited to simultaneously enable migration of historical data, e.g. to cloud storage, while overcoming the index node time splitting difficulty that has arisen in the past when historical nodes are migrated.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113368"}, {"primary_key": "4429675", "vector": [], "sparse_vector": [], "title": "Efficient and scalable trie-based algorithms for computing set containment relations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Computing containment relations between massive collections of sets is a fundamental operation in data management, for example in graph analytics and data mining applications. Motivated by recent hardware trends, in this paper we present two novel solutions for computing set-containment joins over massive sets: the Patricia Trie-based Signature Join (PTSJ) and PRETTI+, a Patricia trie enhanced extension of the state-of-the-art PRETTI join. The compact trie structure not only enables efficient use of main-memory, but also significantly boosts the performance of both approaches. By carefully analyzing the algorithms and conducting extensive experiments with various synthetic and real-world datasets, we show that, in many practical cases, our algorithms are an order of magnitude faster than the state-of-the-art.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113293"}, {"primary_key": "4429676", "vector": [], "sparse_vector": [], "title": "Piecewise linear approximation of streaming time series data with max-error guarantees.", "authors": ["Ge Luo", "Ke <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Given a time series S = ((x 1 , y 1 ), (x 2 , y 2 ), …) and a prescribed error bound ε, the piecewise linear approximation (PLA) problem with max-error guarantees is to construct a piecewise linear function f such that |f(x i )-y i | ≤ ε for all i. In addition, we would like to have an online algorithm that takes the time series as the records arrive in a streaming fashion, and outputs the pieces of f on-the-fly. This problem has applications wherever time series data is being continuously collected, but the data collection device has limited local buffer space and communication bandwidth, so that the data has to be compressed and sent back during the collection process. Prior work addressed two versions of the problem, where either f consists of disjoint segments, or f is required to be a continuous piecewise linear function. In both cases, existing algorithms can produce a function f that has the minimum number of pieces while meeting the prescribed error bound ε. However, we observe that neither minimizes the true representation size of f, i.e., the number of parameters required to represent f. In this paper, we design an online algorithm that generates the optimal PLA in terms of representation size while meeting the prescribed max-error guarantee. Our experiments on many real-world data sets show that our algorithm can reduce the representation size of f by around 15% on average compared with the current best methods, while still requiring O(1) processing time per data record and small space.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113282"}, {"primary_key": "4429677", "vector": [], "sparse_vector": [], "title": "PGWinFunc: Optimizing Window Aggregate Functions in PostgreSQL and its application for trajectory data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cheqing Jin", "<PERSON><PERSON><PERSON>"], "summary": "In modern cities, more and more people drive the vehicles, equipped with the GPS devices, which create a large scale of trajectories. Gathering and analyzing these large-scale trajectory data provide a new opportunity to understand the city dynamics and to reveal the hidden social and economic phenomena. This paper designs and implements a tool, named as PGWinFunc, to analyze trajectory data by extending a traditional relational database. Firstly we introduce some efficient query process and optimization methods for SQL Window Aggregate Functions in PostgreSQL. Secondly, we present how to mine the LBS (Location-Based Service) patterns, such as the average speed and traffic flow, from the large-scale trajectories with SQL expression with Window Aggregate Functions. Finally, the effectiveness and efficiency of the PGWinFunc tool are demonstrated and we also visualized the results by BAIDU MAP.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113398"}, {"primary_key": "4429679", "vector": [], "sparse_vector": [], "title": "LLAMA: Efficient graph analytics using Large Multiversioned Arrays.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "We present LLAMA, a graph storage and analysis system that supports mutability and out-of-memory execution. LLAMA performs comparably to immutable main-memory analysis systems for graphs that fit in memory and significantly outperforms existing out-of-memory analysis systems for graphs that exceed main memory. LLAMA bases its implementation on the compressed sparse row (CSR) representation, which is a read-only representation commonly used for graph analytics. We augment this representation to support mutability and persistence using a novel implementation of multi-versioned array snapshots, making it ideal for applications that receive a steady stream of new data, but need to perform whole-graph analysis on consistent views of the data. We compare LLAMA to state-of-the-art systems on representative graph analysis workloads, showing that LLAMA scales well both out-of-memory and across parallel cores. Our evaluation shows that LLAMA's mutability introduces modest overheads of 3-18% relative to immutable CSR for in-memory execution and that it outperforms state-of-the-art out-of-memory systems in most cases, with a best case improvement of 5x on breadth-first-search.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113298"}, {"primary_key": "4429681", "vector": [], "sparse_vector": [], "title": "Scalable SimRank join algorithm.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Similarity join finds all pairs of objects (i, j) with similarity score s(i, j) greater than some specified threshold θ. This is a fundamental query problem in the database research community, and is used in many practical applications, such as duplicate detection, merge/purge, record linkage, object matching, and reference conciliation. In this paper, we propose a scalable approximation algorithm with an arbitrary accuracy for the similarity join problem with the SimRank similarity measure. The algorithm consists of two phases: filter and verification. The filter phase enumerates similar pair candidates, and the similarity of each candidate is then assessed in the verification phase. The scalability of the proposed algorithm is experimentally verified for large real networks. The complexity depends only on the number of similar pairs, but does not depend on all pairs O(n 2 ). The proposed algorithm scales up to the network of 5M vertices and 70M edges. By comparing the state-of-the-art algorithms, it is about 10 times faster and it requires about 10 times smaller memory.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113318"}, {"primary_key": "4429683", "vector": [], "sparse_vector": [], "title": "Mining maximal cliques from an uncertain graph.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Srikanta Tirthapura"], "summary": "We consider mining dense substructures (maximal cliques) from an uncertain graph, which is a probability distribution on a set of deterministic graphs. For parameter 0 <; α <; 1, we consider the notion of an α-maximal clique in an uncertain graph. We present matching upper and lower bounds on the number of α-maximal cliques possible within a (uncertain) graph. We present an algorithm to enumerate α-maximal cliques whose worst-case runtime is near-optimal, and an experimental evaluation showing the practical utility of the algorithm.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113288"}, {"primary_key": "4429685", "vector": [], "sparse_vector": [], "title": "Configurable hardware-based streaming architecture using Online Programmable-Blocks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The limitations of traditional general-purpose processors have motivated the use of specialized hardware solutions (e.g., FPGAs) to achieve higher performance in stream processing. However, state-of-the-art hardware-only solutions have limited support to adapt to changes in the query workload.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113336"}, {"primary_key": "4429686", "vector": [], "sparse_vector": [], "title": "The power of both choices: Practical load balancing for distributed stream processing engines.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the problem of load balancing in distributed stream processing engines, which is exacerbated in the presence of skew. We introduce Partial Key Grouping (PKG), a new stream partitioning scheme that adapts the classical \"power of two choices\" to a distributed streaming setting by leveraging two novel techniques: key splitting and local load estimation. In so doing, it achieves better load balancing than key grouping while being more scalable than shuffle grouping. We test PKG on several large datasets, both real-world and synthetic. Compared to standard hashing, PKG reduces the load imbalance by up to several orders of magnitude, and often achieves nearly-perfect load balance. This result translates into an improvement of up to 60% in throughput and up to 45% in latency when deployed on a real Storm cluster.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113279"}, {"primary_key": "4429689", "vector": [], "sparse_vector": [], "title": "Ranking Candidate Networks of relations to improve keyword search over relational databases.", "authors": ["<PERSON><PERSON>", "Altigran <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Relational keyword search (R-KwS) systems based on schema graphs take the keywords from the input query, find the tuples and tables where these keywords occur and look for ways to \"connect\" these keywords using information on referential integrity constraints, i.e., key/foreign key pairs. The result is a number of expressions, called Candidate Networks (CNs), which join relations where keywords occur in a meaningful way. These CNs are then evaluated, resulting in a number of join networks of tuples (JNTs) that are presented to the user as ranked answers to the query. As the number of CNs is potentially very high, handling them is very demanding, both in terms of time and resources, so that, for certain queries, current systems may take too long to produce answers, and for others they may even fail to return results (e.g., by exhausting memory). Moreover, the quality of the CN evaluation may be compromised when a large number of CNs is processed. Based on observations made by other researchers and in our own findings on representative workloads, we argue that, although the number of possible Candidate Networks can be very high, only very few of them produce answers relevant to the user and are indeed worth processing. Thus, R-KwS systems can greatly benefit from methods for accessing the relevance of Candidate Networks, so that only those deemed relevant might be evaluated. We propose in this paper an approach for ranking CNs, based on their probability of producing relevant answers to the user. This relevance is estimated based on the current state of the underlying database using a probabilistic Bayesian model we have developed. Experiments that we performed indicate that this model is able to assign the relevant CNs among the top-4 in the ranking produced. In these experiments we also observed that processing only a few relevant CNs has a considerable positive impact, not only on the performance of processing keyword queries, but also on the quality of the results obtained.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113301"}, {"primary_key": "4429690", "vector": [], "sparse_vector": [], "title": "Interactive preference-aware query optimization.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "Pan<PERSON>", "<PERSON><PERSON>"], "summary": "PASQL is an extension to SQL that allows users of a distributed database to specify privacy constraints on an SQL query evaluation plan. However, privacy constraints can be difficult for users to specify, and worse yet, all possible situations that could lead to a privacy violation may not be known to the user a priori. To address these challenges, we propose a GUI-based interactive process for detecting such violations and generating appropriate constraints. In this work, we demonstrate two approaches to implementing such a GUI that provide different ways of analyzing and interactively optimizing a PASQL query plan.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113414"}, {"primary_key": "4429691", "vector": [], "sparse_vector": [], "title": "Understanding business trends from data evolution with Tornado.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vasile<PERSON><PERSON>"], "summary": "Nowadays, every company could understand how its business evolves from the data (deluge) generated by its activities. Roughly speaking, two types of data co-exist: historical data and real-time data from which business analysts have to take their decisions in a timely fashion. In this context, the notions of time (application time and transaction time) and traceability turn out to play a crucial role to understand what happened in the company and what is currently happening. Tornado offers a full-fledged platform to deal with such data and is based on two key features: 1) a bi-temporal DB specifically designed for handling historical and real-time data, 2) a GUI that aims to facilitate query formulation for business analysts. In this demonstration, we provide the key resources to let the visitors play with the Tornado functionalities to interact with predefined data.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113400"}, {"primary_key": "4429692", "vector": [], "sparse_vector": [], "title": "Accelerating Big Data analytics with Collaborative Planning in Teradata Aster 6.", "authors": ["Aditi Pandit", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The volume, velocity, and variety of Big Data necessitate the development of new and innovative data processing software. A multitude of SQL implementations on distributed systems have emerged in recent years to enable large-scale data analysis. User-Defined Table operators (written in procedural languages) embedded in these SQL implementations are a powerful mechanism to succinctly express and perform analytic operations typical in Big Data discovery workloads. Table operators can be easily customized to implement different processing models such as map, reduce and graph execution. Despite an inherently parallel execution model, the performance and scalability of these table operators is greatly restricted as they appear as a black box to a typical SQL query optimizer. The optimizer is not able to infer even the basic properties of table operators, prohibiting the application of optimization rules and strategies. In this paper, we introduce an innovative concept of \"Collaborative Planning\", which results in the removal of redundant operations and a more optimal rearrangement of query plan operators. The optimization of the query proceeds through a collaborative exchange between the planner and the table operator. Plan properties and context information of surrounding query plan operations are exchanged between the optimizer and the table operator. Knowing these properties also allows the author of the table operator to optimize its embedded logic. Our main contribution in this paper is the design and implementation of Collaborative Planning in the Teradata Aster 6 system. Using real-world workloads, we show that Collaborative Planning reduces query execution times as much as 90.0% in common use cases, resulting in a 24x speedup.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113378"}, {"primary_key": "4429693", "vector": [], "sparse_vector": [], "title": "Goals in Social Media, information retrieval and intelligent agents.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Georgia Koutrika", "<PERSON>"], "summary": "This tutorial provides a comprehensive and cohesive overview of goal modeling and recognition approaches by the Information Retrieval, the Artificial Intelligence and the Social Media communities. We will examine how these fields restrict the domain of study and how they capture notions easily perceived by humans' intuition but difficult to be formally defined and handled algorithmically. It is the purpose of this tutorial to provide a solid framework for placing existing work into perspective and highlight critical open challenges that will act as a springboard for researchers and practitioners in database systems, social data, and the Web, as well as developers of web-based, database-driven, and social applications, to work towards more user-centric systems and applications.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113421"}, {"primary_key": "4429694", "vector": [], "sparse_vector": [], "title": "Privacy-aware dynamic feature selection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Big data will enable the development of novel services that enhance a company's market advantage, competition, or productivity. At the same time, the utilization of such a service could disclose sensitive data in the process, which raises significant privacy concerns. To protect individuals, various policies, such as the Code of Fair Information Practices, as well as recent laws require organizations to capture only the minimal amount of data necessary to support a service. While this is a notable goal, choosing the minimal data is a non-trivial process, especially while considering privacy and utility constraints. In this paper, we introduce a technique to minimize sensitive data disclosure by focusing on privacy-aware feature selection. During model deployment, the service provider requests only a subset of the available features from the client, such that it can produce results with maximal confidence, while minimizing its ability to violate a client's privacy. We propose an iterative approach, where the server requests information one feature at a time until the client-specified privacy budget is exhausted. The overall process is dynamic, such that the feature selected at each step depends on the previously selected features and their corresponding values. We demonstrate our technique with three popular classification algorithms and perform an empirical analysis over three real world datasets to illustrate that, in almost all cases, classifiers that select features using our strategy have the same error-rate as state-of-the art static feature selection methods that fail to preserve privacy.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113274"}, {"primary_key": "4429695", "vector": [], "sparse_vector": [], "title": "Groupwise analytics via adaptive MapReduce.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Shared-nothing systems such as Hadoop vastly simplify parallel programming when processing disk-resident data whose size exceeds aggregate cluster memory. Such systems incur a significant performance penalty, however, on the important class of \"groupwise set-valued analytics\" (GSVA) queries in which the data is dynamically partitioned into groups and then a set-valued synopsis is computed for some or all of the groups. Key examples of synopses include top-k sets, bottom-k sets, and uniform random samples. Applications of GSVA queries include micro-marketing, root-cause analysis for problem diagnosis, and fraud detection. A naive approach to executing GSVA queries first reshuffles all of the data so that all records in a group are at the same node and then computes the synopsis for the group. This approach can be extremely inefficient when, as is typical, only a very small fraction of the records in each group actually contribute to the final groupwise synopsis, so that most of the shuffling effort is wasted. We show how to significantly speed up GSVA queries by slightly modifying the shared-nothing environment to allow tasks to occasionally access a small, common data structure; we focus on the Hadoop setting and use the \"Adaptive MapReduce\" infrastructure of Vernica et al. to implement the data structure. Our approach retains most of the advantages of a system such as Hadoop while significantly improving GSVA query performance, and also allows for incremental updating of query results. Experiments show speedups of up to 5x. Importantly, our new technique can potentially be applied to other shared-nothing systems with disk-resident data.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113356"}, {"primary_key": "4429696", "vector": [], "sparse_vector": [], "title": "DBMS on modern storage hardware.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the present tutorial we perform a cross-cut analysis of database systems from the perspective of modern storage technology, namely Flash memory. We argue that neither the design of modern DBMS, nor the architecture of Flash storage technologies are aligned with each other. The result is needlessly suboptimal DBMS performance and inefficient Flash utilisation as well as low Flash storage endurance and reliability. We showcase new DBMS approaches with improved algorithms and leaner architectures, designed to leverage the properties of modern storage technologies. We cover the area of transaction management and multi-versioning, putting a special emphasis on: (i) version organisation models and invalidation mechanisms in multi-versioning DBMS; (ii) Flash storage management especially on append-based storage in tuple granularity; (iii) Flash-friendly buffer management; as well as (iv) improvements in the searching and indexing models. Furthermore, we present our NoFTL approach to native Flash access that integrates parts of the Flash-management functionality into the DBMS yielding significant performance increase and simplification of the I/O stack. In addition, we cover the basics of building large Flash storage for DBMS and revisit some of the RAID techniques and principles.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113423"}, {"primary_key": "4429697", "vector": [], "sparse_vector": [], "title": "A general graph-based model for recommendation in event-based social networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Event-based social networks (EBSNs), such as Meetup and Plancast, which offer platforms for users to plan, arrange, and publish events, have gained increasing popularity and rapid growth. EBSNs capture not only the online social relationship, but also the offline interactions from offline events. They contain rich heterogeneous information, including multiple types of entities, such as users, events, groups and tags, and their interaction relations. Three recommendation tasks, namely recommending groups to users, recommending tags to groups, and recommending events to users, have been explored in three separate studies. However, none of the proposed methods can handle all the three recommendation tasks. In this paper, we propose a general graph-based model, called HeteRS, to solve the three recommendation problems on EBSNs in one framework. Our method models the rich information with a heterogeneous graph and considers the recommendation problem as a query-dependent node proximity problem. To address the challenging issue of weighting the influences between different types of entities, we propose a learning scheme to set the influence weights between different types of entities. Experimental results on two real-world datasets demonstrate that our proposed method significantly outperforms the state-of-the-art methods for all the three recommendation tasks, and the learned influence weights help understanding user behaviors.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113315"}, {"primary_key": "4429698", "vector": [], "sparse_vector": [], "title": "LDV: Light-weight database virtualization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a light-weight database virtualization (LDV) system that allows users to share and re-execute applications that operate on a relational database (DB). Previous methods for sharing DB applications, such as companion websites and virtual machine images (VMIs), support neither easy and efficient re-execution nor the sharing of only a relevant DB subset. LDV addresses these issues by monitoring application execution, including DB operations, and using the resulting execution trace to create a lightweight re-executable package. A LDV package includes, in addition to the application, either the DB management system (DBMS) and relevant data or, if the DBMS and/or data cannot be shared, just the application-DBMS communications for replay during re-execution. We introduce a linked DB-operating system provenance model and show how to infer data dependencies based on temporal information about the DB operations performed by the application's process(es). We use this model to determine the DB subset that needs to be included in a package in order to enable re-execution. We compare LDV with other sharing methods in terms of package size, monitoring overhead, and re-execution overhead. We show that LDV packages are often more than an order of magnitude smaller than a VMI for the same application, and have negligible re-execution overhead.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113366"}, {"primary_key": "4429699", "vector": [], "sparse_vector": [], "title": "Indexing and matching trajectories under inconsistent sampling rates.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Quantifying the similarity between two trajectories is a fundamental operation in analysis of spatio-temporal databases. While a number of distance functions exist, the recent shift in the dynamics of the trajectory generation procedure violates one of their core assumptions; a consistent and uniform sampling rate. In this paper, we formulate a robust distance function called Edit Distance with Projections (EDwP) to match trajectories under inconsistent and variable sampling rates through dynamic interpolation. This is achieved by deploying the idea of projections that goes beyond matching only the sampled points while aligning trajectories. To enable efficient trajectory retrievals using EDwP, we design an index structure called TrajTree. TrajTree derives its pruning power by employing the unique combination of bounding boxes with Lipschitz embedding. Extensive experiments on real trajectory databases demonstrate EDwP to be up to 5 times more accurate than the state-of-the-art distance functions. Additionally, TrajTree increases the efficiency of trajectory retrievals by up to an order of magnitude over existing techniques.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113351"}, {"primary_key": "4429700", "vector": [], "sparse_vector": [], "title": "Query-time record linkage and fusion over Web databases.", "authors": ["El Kindi Rezig", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Data-intensive Web applications usually require integrating data from Web sources at query time. The sources may refer to the same real-world entity in different ways and some may even provide outdated or erroneous data. An important task is to recognize and merge the records that refer to the same real world entity at query time. Most existing duplicate detection and fusion techniques work in the off-line setting and do not meet the online constraint. There are at least two aspects that differentiate online duplicate detection and fusion from its off-line counterpart. (i) The latter assumes that the entire data is available, while the former cannot make such an assumption. (ii) Several query submissions may be required to compute the \"ideal\" representation of an entity in the online setting. This paper presents a general framework for the online setting based on an iterative record-based caching technique. A set of frequently requested records is deduplicated off-line and cached for future reference. Newly arriving records in response to a query are deduplicated jointly with the records in the cache, presented to the user and appended to the cache. Experiments with real and synthetic data show the benefit of our solution over traditional record linkage techniques applied to an online setting.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113271"}, {"primary_key": "4429701", "vector": [], "sparse_vector": [], "title": "PerfAugur: Robust diagnostics for performance anomalies in cloud services.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Cloud platforms involve multiple independently developed components, often executing on diverse hardware configurations and across multiple data centers. This complexity makes tracking various key performance indicators (KPIs) and manual diagnosing of anomalies in system behavior both difficult and expensive. In this paper, we describe PerfAugur, an automated system for mining service logs to identify anomalies and help formulate data-driven hypotheses. PerfAugur includes a suite of efficient mining algorithms for detecting significant anomalies in system behavior, along with potential explanations for such anomalies, without the need for an explicit supervision signal. We perform extensive experimental evaluation using both synthetic and real-life data sets, and present detailed case studies showing the impact of this technology on operations of the Windows Azure Service.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113365"}, {"primary_key": "4429702", "vector": [], "sparse_vector": [], "title": "Automatic tuning of bag-of-tasks applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents APlug, a framework for automatic tuning of large scale applications of many independent tasks. APlug suggests the best decomposition of the original computation into smaller tasks and the best number of CPUs to use, in order to meet user-specific constraints. We show that the problem is not trivial because there is large variability in the execution time of tasks, and it is possible for a task to occupy a CPU by performing useless computations. APlug collects a sample of task execution times and builds a model, which is then used by a discrete event simulator to calculate the optimal parameters. We provide a C++ API and a stand-alone implementation of APlug, and we integrate it with three typical applications from computational chemistry, bioinformatics, and data mining. A scenario for optimizing resources utilization is used to demonstrate our framework. We run experiments on 16,384 CPUs on a supercomputer, 480 cores on a Linux cluster and 80 cores on Amazon EC2, and show that APlug is very accurate with minimal overhead.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113338"}, {"primary_key": "4429704", "vector": [], "sparse_vector": [], "title": "FCCE: Highly scalable distributed Feature Collection and Correlation Engine for low latency big data analytics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present the design, architecture, and implementation of a novel analysis engine, called Feature Collection and Correlation Engine (FCCE), that finds correlations across a diverse set of data types spanning over large time windows with very small latency and with minimal access to raw data. FCCE scales well to collecting, extracting, and querying features from geographically distributed large data sets. FCCE has been deployed in a large production network with over 450,000 workstations for 3 years, ingesting more than 2 billion events per day and providing low latency query responses for various analytics. We explore two security analytics use cases to demonstrate how we utilize the deployment of FCCE on large diverse data sets in the cyber security domain: 1) detecting fluxing domain names of potential botnet activity and identifying all the devices in the production network querying these names, and 2) detecting advanced persistent threat infection. Both evaluation results and our experience with real-world applications show that FCCE yields superior performance over existing approaches, and excels in the challenging cyber security domain by correlating multiple features and deriving security intelligence.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113379"}, {"primary_key": "4429705", "vector": [], "sparse_vector": [], "title": "Efficient sample generation for scalable meta learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Meta learning techniques such as cross-validation and ensemble learning are crucial for applying machine learning to real-world use cases. These techniques first generate samples from input data, and then train and evaluate machine learning models on these samples. For meta learning on large datasets, the efficient generation of samples becomes problematic, especially when the data is stored distributed in a block-partitioned representation, and processed on a shared-nothing cluster. We present a novel, parallel algorithm for efficient sample generation from large, block-partitioned datasets in a shared-nothing architecture. This algorithm executes in a single pass over the data, and minimizes inter-machine communication. The algorithm supports a wide variety of sample generation techniques through an embedded user-defined sampling function. We illustrate how to implement distributed sample generation for popular meta learning techniques such as hold-out tests, k-fold cross-validation, and bagging, using our algorithm and present an experimental evaluation on datasets with billions of datapoints.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113367"}, {"primary_key": "4429706", "vector": [], "sparse_vector": [], "title": "ControVol: A framework for controlled schema evolution in NoSQL application development.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Building scalable web applications on top of NoSQL data stores is becoming common practice. Many of these data stores can easily be accessed programmatically, and do not enforce a schema. Software engineers can design the data model on the go, a flexibility that is crucial in agile software development. The typical tasks of database schema management are now handled within the application code, usually involving object mapper libraries. However, today's Integrated Development Environments (IDEs) lack the proper tool support when it comes to managing the combined evolution of the application code and of the schema. Yet simple refactorings such as renaming an attribute at the source code level can cause irretrievable data loss or runtime errors once the application is serving in production. In this demo, we present ControVol, a framework for controlled schema evolution in application development against NoSQL data stores. ControVol is integrated into the IDE and statically type checks object mapper class declarations against the schema evolution history, as recorded by the code repository. ControVol is capable of warning of common yet risky cases of mismatched data and schema. ControVol is further able to suggest quick fixes by which developers can have these issues automatically resolved.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113402"}, {"primary_key": "4429708", "vector": [], "sparse_vector": [], "title": "Convex Risk Minimization to Infer Networks from probabilistic diffusion data at multiple scales.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "SEIR (Susceptible-Exposed-Infected-Recovered) is a general and widely-used diffusion model that can model the diffusion in different contexts such as idea spreading and disease propagation. Here, we tackle the problem of inferring graph edges if we can only observe a SEIR diffusion process spreading over the nodes of a graph. This problem is of importance in the common case where node states can be estimated with less cost than the edges can be found. Some applications include inferring a contact network from disease spread data, inferring a reference network from idea spreading, or estimating influenza diffusion rates between U.S. states. We improve upon the existing approaches for this problem in three ways: (1) we assume we are provided only with the probabilistic information about the state of each node which may also be undersampled or incomplete; (2) we present a more general framework that better uses trace data to model edge non-existence under SEIR model; (3) we can infer the network at both micro and macro scales. Experiments on both real and synthetic data show that our method is accurate under these challenging cases at multiple scales, and it performs consistently better than the existing methods. For instance, we can infer a high school human contact network at microscale by tracking influenza diffusion almost 10% better than the existing methods as well as the estimated networks closely mimick the full range of properties of the true network. We also estimated the strength of the influenza diffusion between and inside the U.S. states from Google Flu Trends data at macroscale. Estimated rates are correlated with the human transportation rates between the states to a certain degree, and we gain interesting insight into the influenza diffusion in U.S. such as the importance of the less populous states in epidemics as well as the asymmetric influenza diffusion between U.S. states.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113323"}, {"primary_key": "4429709", "vector": [], "sparse_vector": [], "title": "Conflict-aware event-participant arrangement.", "authors": ["<PERSON><PERSON><PERSON> She", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the rapid development of Web 2.0 and Online To Offline (O2O) marketing model, various online event-based social networks (EBSNs), such as Meetup and Whova, are getting popular. An important task of EBSNs is to facilitate the most satisfactory event-participant arrangement for both sides, i.e. events enroll more participants and participants are arranged with personally interesting events. Existing approaches usually focus on the arrangement of each single event to a set of potential users, and ignore the conflicts between different events, which leads to infeasible or redundant arrangements. In this paper, to address the shortcomings of existing approaches, we first identify a more general and useful event-participant arrangement problem, called Global Event-participant Arrangement with Conflict and Capacity (GEACC) problem, focusing on the conflicts of different events and making event-participant arrangements in a global view. Though it is useful, unfortunately, we find that the GEACC problem is NP-hard due to the conflict constraints among events. Thus, we design two approximation algorithms with provable approximation ratios and an exact algorithm with pruning technique to address this problem. Finally, we verify the effectiveness and efficiency of the proposed methods through extensive experiments on real and synthetic datasets.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113329"}, {"primary_key": "4429710", "vector": [], "sparse_vector": [], "title": "Linear path skylines in multicriteria networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In many graph applications, computing cost-optimal paths between two locations is an important task for routing and distance computation. Depending on the network multiple cost criteria might be of interest. Examples are travel time, energy consumption and toll fees in road networks. Path skyline queries compute the set of pareto optimal paths between two given locations. However, the number of skyline paths increases exponentially with the distance between the locations and the number of cost criteria. Thus, the result set might be too big to be of any use. In this paper, we introduce multicriteria linear path skyline queries. A linear path skyline is the subset of the conventional path skyline where the paths are optimal under a linear combination of their cost values. We argue that cost vectors being optimal with respect to a weighted sum are intuitive to understand and therefore, more interesting in many cases. We show that linear path skylines are convex hulls of an augmented solution space and propose an algorithm which utilizes this observation to efficiently compute the complete linear path skyline. To further control the size of the result set, we introduce an approximate version of our algorithm guaranteeing a certain level of optimality for each possible weighting. In our experimental evaluation, we show that our approach computes linear path skylines significantly faster than previous approaches, including those computing the complete path skyline.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113306"}, {"primary_key": "4429711", "vector": [], "sparse_vector": [], "title": "A graph-based RDF triple store.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this demonstration, we present the gStore RDF triple store. gStore is based on graph encoding and subgraph match, distinct from many other systems. More importantly, it can handle, in a uniform manner, different data types (strings and numerical data) and SPARQL queries with wildcards, aggregate, range and top-k operators over dynamic RDF datasets. We will demonstrate the main features of our system, show how to search Wikipedia documents using gStore and how to build users' own application using gStore through C++/Java API.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113413"}, {"primary_key": "4429712", "vector": [], "sparse_vector": [], "title": "Optimizing recursive queries with monotonic aggregates in DeALS.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The exploding demand for analytics has refocused the attention of data scientists on applications requiring aggregation in recursion. After resisting the efforts of researchers for more than twenty years, this problem is being addressed by innovative systems that are raising logic-oriented data languages to the levels of generality and performance that are needed to support efficiently a broad range of applications. Foremost among these new systems, the Deductive Application Language System (DeALS) achieves superior generality and performance via new constructs and optimization techniques for monotonic aggregates which are described in the paper. The use of a special class of monotonic aggregates in recursion was made possible by recent theoretical results that proved that they preserve the rigorous least-fixpoint semantics of core Datalog programs. This paper thus describes how DeALS extends their definitions and modifies their syntax to enable a concise expression of applications that, without them, could not be expressed in performance-conducive ways, or could not be expressed at all. Then the paper turns to the performance issue, and introduces novel implementation and optimization techniques that outperform traditional approaches, including Semi-naive evaluation. An extensive experimental evaluation was executed comparing DeALS with other systems on large datasets. The results suggest that, unlike other systems, DeALS indeed combines superior generality with superior performance.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113340"}, {"primary_key": "4429713", "vector": [], "sparse_vector": [], "title": "Multicore triangle computations without tuning.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Triangle counting and enumeration has emerged as a basic tool in large-scale network analysis, fueling the development of algorithms that scale to massive graphs. Most of the existing algorithms, however, are designed for the distributed-memory setting or the external-memory setting, and cannot take full advantage of a multicore machine, whose capacity has grown to accommodate even the largest of real-world graphs. This paper describes the design and implementation of simple and fast multicore parallel algorithms for exact, as well as approximate, triangle counting and other triangle computations that scale to billions of nodes and edges. Our algorithms are provably cache-friendly, easy to implement in a language that supports dynamic parallelism, such as Cilk Plus or OpenMP, and do not require parameter tuning. On a 40-core machine with two-way hyper-threading, our parallel exact global and local triangle counting algorithms obtain speedups of 17-50x on a set of real-world and synthetic graphs, and are faster than previous parallel exact triangle counting algorithms. We can compute the exact triangle count of the Yahoo Web graph (over 6 billion edges) in under 1.5 minutes. In addition, for approximate triangle counting, we are able to approximate the count for the Yahoo graph to within 99.6% accuracy in under 10 seconds, and for a given accuracy we are much faster than existing parallel approximate triangle counting implementations.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113280"}, {"primary_key": "4429714", "vector": [], "sparse_vector": [], "title": "Hierarchical in-network attribute compression via importance sampling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many real-world complex systems can be modeled as dynamic networks with real-valued vertex/edge attributes. Examples include users' opinions in social networks and average speeds in a road system. When managing these large dynamic networks, compressing attribute values becomes a key requirement, since it enables the answering of attribute-based queries regarding a node/edge or network region based on a compact representation of the data. To address this problem, we introduce a lossy network compression scheme called Slice Tree (ST), which partitions a network into smooth regions with respect to node/edge values and compresses each value as the average of its region. ST applies a compact representation for network partitions, called slices, that are defined as a center node and radius distance. We propose an importance sampling algorithm to efficiently prune the search space of candidate slices in the ST construction by biasing the sampling process towards the node values that most affect the compression error. The effectiveness of ST in terms of compression error, compression rate, and running time is demonstrated using synthetic and real datasets. ST scales to million-node instances and removes up to 87% of the error in attribute values with a 10 3 compression ratio. We also illustrate how ST captures relevant phenomena in real networks, such as research collaboration patterns and traffic congestions.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113347"}, {"primary_key": "4429715", "vector": [], "sparse_vector": [], "title": "Querying databases by snapping blocks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "A key area of focus in recent Computer Science education research has been block-based programming. In this approach, program instructions are represented as visual blocks with shapes that control the way multiple instructions can be combined. Since programs are created by dragging and connecting blocks, the focus is on the program's logic rather than its syntax. In this demonstration we present DBSnap, a system that enables building database queries, specifically relational algebra queries, by connecting blocks. A differentiating property of DBSnap is that it uses a visual tree-based structure to represent queries. This structure is, in fact, very similar to the intuitive query trees commonly used by database practitioners and educators. DBSnap is also highly dynamic, it shows the query result and the corresponding relational algebra expression as the query is built and enables the inspection of intermediate query results. This paper describes DBSnap's main design elements, its architecture and implementation guidelines, and the interactive demonstration scenarios. DBSnap is a publicly available system and aims to have a transformational effect on database learning.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113404"}, {"primary_key": "4429716", "vector": [], "sparse_vector": [], "title": "Window-chained longest common subsequence: Common event matching in sequences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Sequence data is prevalent, and event processing over sequences is increasingly important in this Big Data era, drawing much attention from both research and industry. In this paper, we address a novel problem, which is to find common event subsequences from two long sequences. This problem is well motivated, with applications in diverse domains. We propose the window-chained longest common subsequence (WCLCS) semantics, and argue that the traditional longest common subsequence (LCS) cannot serve this need. We then devise efficient algorithms to solve this problem by reducing it to a graph problem. We also propose two more methods to improve the performance: one is based on informed search and exploration, and the other is an approximation algorithm with accuracy guarantees. We finally carry out a systematic experimental evaluation using two real-world datasets and some synthetic datasets.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113331"}, {"primary_key": "4429718", "vector": [], "sparse_vector": [], "title": "Making sense of trajectory data: A partition-and-summarization approach.", "authors": ["<PERSON> Su", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to the prevalence of GPS-enabled devices and wireless communication technology, spatial trajectories that describe the movement history of moving objects are being generated and accumulated at an unprecedented pace. However, a raw trajectory in the form of sequence of timestamped locations does not make much sense for humans without semantic representation. In this work we aim to facilitate human's understanding of a raw trajectory by automatically generating a short text to describe it. By formulating this task as the problem of adaptive trajectory segmentation and feature selection, we propose a partition-and-summarization framework. In the partition phase, we first define a set of features for each trajectory segment and then derive an optimal partition with the aim to make the segments within each partition as homogeneous as possible in terms of their features. In the summarization phase, for each partition we select the most interesting features by comparing against the common behaviours of historical trajectories on the same route and generate short text description for these features. For empirical study, we apply our solution to a real trajectory dataset and have found that the generated text can effectively reflect the important parts in a trajectory.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113348"}, {"primary_key": "4429719", "vector": [], "sparse_vector": [], "title": "Result selection and summarization for Web Table search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The amount of information available on the Web has been growing dramatically, raising the importance of techniques for searching the Web. Recently, Web Tables emerged as a model, which enables users to search for information in a structured way. However, effective presentation of results for Web Table search requires (1) selecting a ranking of tables that acknowledges the diversity within the search result; and (2) summarizing the information content of the selected tables concisely but meaningful. In this paper, we formalize these requirements as the diversified table selection problem and the structured table summarization problem. We show that both problems are computationally intractable and, thus, present heuristic algorithms to solve them. For these algorithms, we prove salient performance guarantees, such as near-optimality, stability, and fairness. Our experiments with real-world collections of thousands of Web Tables highlight the scalability of our techniques. We achieve improvements up to 50% in diversity and 10% in relevance over baselines for Web Table selection, and reduce the information loss induced by table summarization by up to 50%. In a user study, we observed that our techniques are preferred over alternative solutions.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113287"}, {"primary_key": "4429721", "vector": [], "sparse_vector": [], "title": "PrivGeoCrowd: A toolbox for studying private spatial Crowdsourcing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Spatial Crowdsourcing (SC) is a novel and transformative platform that engages individuals, groups and communities in the act of collecting, analyzing, and disseminating environmental, social and other spatio-temporal information. SC outsources a set of spatio-temporal tasks to a set of workers, i.e., individuals with mobile devices that perform the tasks by physically traveling to specified locations of interest. Protecting location privacy is an important concern in SC, as an adversary with access to individual whereabouts can infer sensitive details about a person (e.g., health status, political views). Due to the challenging nature of protecting worker privacy in SC, solutions for this problem are quite complex, and require tuning of several parameters to obtain satisfactory results. In this paper, we propose PrivGeoCrowd, a toolbox for interactive visualization and tuning of SC private task assignment methods. This toolbox is useful for several real-world entities that are involved in SC, such as: mobile phone operators that want to sanitize datasets with worker locations, spatial task requesters, and SC-service providers that match workers to tasks.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113387"}, {"primary_key": "4429725", "vector": [], "sparse_vector": [], "title": "Entity Resolution with crowd errors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given a set of records, an Entity Resolution (ER) algorithm finds records that refer to the same real-world entity. Humans can often determine if two records refer to the same entity, and hence we study the problem of selecting questions to ask error-prone humans. We give a Maximum Likelihood formulation for the problem of finding the “most beneficial” questions to ask next. Our theoretical results lead to a lightweight and practical algorithm, bDENSE, for selecting questions to ask humans. Our experimental results show that bDENSE can more quickly reach an accurate outcome, compared to two approaches proposed recently. Moreover, through our experimental evaluation, we identify the strengths and weaknesses of all three approaches.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113286"}, {"primary_key": "4429726", "vector": [], "sparse_vector": [], "title": "Identifying users in social networks with limited information.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of Entity Resolution (ER) with limited information. ER is the problem of identifying and merging records that represent the same real-world entity. In this paper, we focus on the resolution of a single node g from one social graph (Google+ in our case) against a second social graph (Twitter in our case). We want to find the best match for g in Twitter, by dynamically probing the Twitter graph (using a public API), limited by the number of API calls that social systems allow. We propose two strategies that are designed for limited information and can be adapted to different limits. We evaluate our strategies against a naive one on a real dataset and show that our strategies can provide improved accuracy with significantly fewer API calls.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113320"}, {"primary_key": "4429727", "vector": [], "sparse_vector": [], "title": "Enjoy FRDM - play with a schema-flexible RDBMS.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Relational database management systems build on the closed world assumption requiring upfront modeling of a usually stable schema. However, a growing number of today's database applications are characterized by self-descriptive data. The schema of self-descriptive data is very dynamic and prone to frequent changes; a situation which is always troublesome to handle in relational systems. This demo presents the relational database management system FRDM. With flexible relational tables FRDM greatly simplifies the management of self-descriptive data in a relational database system. Self-descriptive data can reside directly next to traditionally modeled data and both can be queried together using SQL. This demo presents the various features of FRDM and provides first-hand experience of the newly gained freedom in relational database systems.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113401"}, {"primary_key": "4429728", "vector": [], "sparse_vector": [], "title": "AllegatorTrack: Combining and reporting results of truth discovery from multi-source data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the Web, a massive amount of user-generated contents is available through various channels, e.g., texts, tweets, Web tables, databases, multimedia-sharing platforms, etc. Conflicting information, rumors, erroneous and fake contents can be easily spread across multiple sources, making it hard to distinguish between what is true and what is not. How do you figure out that a lie has been told often enough that it is now considered to be true? How many lying sources are required to introduce confusion in what you knew before to be the truth? To answer these questions, we present AllegatorTrack, a system that discovers true claims among conflicting data from multiple sources.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113396"}, {"primary_key": "4429730", "vector": [], "sparse_vector": [], "title": "Two birds with one stone: An efficient hierarchical framework for top-k and threshold-based string similarity search.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Ji<PERSON><PERSON>"], "summary": "String similarity search is a fundamental operation in data cleaning and integration. It has two variants, threshold-based string similarity search and top-k string similarity search. Existing algorithms are efficient either for the former or the latter; most of them can't support both two variants. To address this limitation, we propose a unified framework. We first recursively partition strings into disjoint segments and build a hierarchical segment tree index (HS-Tree) on top of the segments. Then we utilize the HS-Tree to support similarity search. For threshold-based search, we identify appropriate tree nodes based on the threshold to answer the query and devise an efficient algorithm (HS-Search). For top-k search, we identify promising strings with large possibility to be similar to the query, utilize these strings to estimate an upper bound which is used to prune dissimilar strings, and propose an algorithm (HS-Topk). We also develop effective pruning techniques to further improve the performance. Experimental results on real-world datasets show our method achieves high performance on the two problems and significantly outperforms state-of-the-art algorithms.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113311"}, {"primary_key": "4429731", "vector": [], "sparse_vector": [], "title": "Preserving privacy in social networks against connection fingerprint attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zheng"], "summary": "Existing works on identity privacy protection on social networks make the assumption that all the user identities in a social network are private and ignore the fact that in many real-world social networks, there exists a considerable amount of users such as celebrities, media users, and organization users whose identities are public. In this paper, we demonstrate that the presence of public users can cause serious damage to the identity privacy of other ordinary users. Motivated attackers can utilize the connection information of a user to some known public users to perform re-identification attacks, namely connection fingerprint (CFP) attacks. We propose two k-anonymization algorithms to protect a social network against the CFP attacks. One algorithm is based on adding dummy vertices. It can resist powerful attackers with the connection information of a user with the public users within n hops (n ≥ 1) and protect the centrality utility of public users. The other algorithm is based on edge modification. It is only able to resist attackers with the connection information of a user with the public users within 1 hop but preserves a rich spectrum of network utility. We perform comprehensive experiments on real-world networks and demonstrate that our algorithms are very efficient in terms of the running time and are able to generate k-anonymized networks with good utility.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113272"}, {"primary_key": "4429732", "vector": [], "sparse_vector": [], "title": "CROWN: A Context-aware RecOmmender for Web News.", "authors": ["<PERSON><PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON>ui<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "It is popular for most people to read news online since the web sites can provide access to news articles from millions of sources around the world. For these news web sites, the key challenge is to help users find related news articles to read. In this paper, we present a system called CROWN (Context-aware RecOmmender for Web News) to do Chinese news recommendation. By recommendation, the system can retrieve personalized fresh and relevant news articles to mobile users according to their particular context. Differing from existing mobile news applications which employ rather simple strategies for news recommendation, CROWN integrates the contextual information in prediction by modeling the data as a tensor. Such context information usually includes the time, the location, etc. This demo paper presents the implementation of the whole procedure of news recommendation in the system of CROWN. Experimental results on a large corpus of newly-published Chinese web news show its performance is satisfactory.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113391"}, {"primary_key": "4429733", "vector": [], "sparse_vector": [], "title": "AP-Tree: Efficiently support continuous spatial-keyword queries over stream.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the problem of processing a large amount of continuous spatial-keyword queries over streaming data, which is essential in many applications such as location-based recommendation and advertising, thanks to the proliferation of geo-equipped devices and the ensuing location-based social media applications. For example, a location-based e-coupon system may allow potentially millions of users to register their continuous spatial-keyword queries (e.g., interests in nearby sales) by specifying a set of keywords and a spatial region; the system then delivers each incoming spatial-textual object (e.g., a geo-tagged e-coupon) to all the matched queries (i.e., users) whose spatial and textual requirements are satisfied. While there are several prior approaches aiming at providing efficient query processing techniques for the problem, their approaches belong to spatial-first indexing method which cannot well exploit the keyword distribution. In addition, their textual filtering techniques are built upon simple variants of traditional inverted indexes, which do not perform well for the textual constraint imposed by the problem. In this paper, we address the above limitations and provide a highly efficient solution based on a novel adaptive index, named AP-Tree. The AP-Tree adaptively groups registered queries using keyword and spatial partitions, guided by a cost model. The AP-Tree also naturally indexes ordered keyword combinations. We present index construction algorithm that seamlessly and effectively integrates keyword and spatial partitions. Consequently, our method adapts well to the underlying spatial and keyword distributions of the data. Our extensive experiments demonstrate that AP-Tree achieves up to an order of magnitude improvement on efficiency compared with prior state-of-the-art methods.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113360"}, {"primary_key": "4429734", "vector": [], "sparse_vector": [], "title": "INSURE: An integrated load reduction framework for XML stream processing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Because of high volumes and unpredictable arrival rates, stream processing systems cannot always keep up with input data streams, resulting in buffer overflow and uncontrolled loss of data. Load shedding and spilling, the two prevalent technologies designed to solve this overflow problem by dropping or flushing data to disk, suffer from serious shortcomings. Dropping data suffers in that partial output is lost forever, while flushing may waste precious resources due to making the strong assumption that flushed data can and will eventually still be processed. In this paper, we propose our solution, INSURE, integrating structure-based drop and flush techniques within one unified framework for XML stream systems. Our INSURE framework provides an optimized fine-grained load reduction solution that achieves high quality result production. First, the fusion candidate lattice models the space of load reduction solutions incorporating both drop and flush decisions, called fusion candidates. Second, our systematic analysis of fusion candidates and their interrelationships in the fusion candidate lattice reveals important relationships, including the monotonicity of their feasibility and profitability properties. Third, based upon this fusion candidate lattice model, a family of optimization strategies for the selection of fusion candidates is designed to successfully maximize the overall result quality. Experimental results demonstrate that INSURE consistently achieves higher quality results compared to the state-of-the-art techniques, yet with negligible overhead.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113333"}, {"primary_key": "4429735", "vector": [], "sparse_vector": [], "title": "PABIRS: A data access middleware for distributed file systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Various big data management systems have emerged to handle different types of applications, which cast very different demands on storage, indexing and retrieval of large amount of data on distributed file system. Such diversity on demands has raised huge challenges to the design of new generation of data access service for big data. In this paper, we present PABIRS, a unified data access middleware to support mixed workloads. PABIRS encapsulates the underlying distributed file system (DFS) and provides a unified access interface to systems such as MapReduce and key-value stores. PABIRS achieves dramatic improvement on efficiency by employing a novel hybrid indexing scheme. Based on the data distribution, the indexing scheme adaptively builds bitmap index and Log Structured Merge Tree (LSM) index. Moreover, PABIRS distributes the computation to multiple index nodes and utilizes a Pregel-based algorithm to facilitate parallel data search and retrieval. We empirically evaluate PABIRS against other existing distributed data processing systems and verify the huge advantages of PABIRS on shorter response time, higher throughput and better scalability, over big data with real-life phone logs and TPC-H benchmark.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113277"}, {"primary_key": "4429736", "vector": [], "sparse_vector": [], "title": "Finding dense and connected subgraphs in dual networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON><PERSON>"], "summary": "Finding dense subgraphs is an important problem that has recently attracted a lot of interests. Most of the existing work focuses on a single graph (or network 1 ). In many real-life applications, however, there exist dual networks, in which one network represents the physical world and another network represents the conceptual world. In this paper, we investigate the problem of finding the densest connected subgraph (DCS) which has the largest density in the conceptual network and is also connected in the physical network. Such pattern cannot be identified using the existing algorithms for a single network. We show that even though finding the densest subgraph in a single network is polynomial time solvable, the DCS problem is NP-hard. We develop a two-step approach to solve the DCS problem. In the first step, we effectively prune the dual networks while guarantee that the optimal solution is contained in the remaining networks. For the second step, we develop two efficient greedy methods based on different search strategies to find the DCS. Different variations of the DCS problem are also studied. We perform extensive experiments on a variety of real and synthetic dual networks to evaluate the effectiveness and efficiency of the developed methods.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113344"}, {"primary_key": "4429737", "vector": [], "sparse_vector": [], "title": "ChronoStream: Elastic stateful stream computation in the cloud.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce ChronoStream, a distributed system specifically designed for elastic stateful stream computation in the cloud. ChronoStream treats internal state as a first-class citizen and aims at providing flexible elastic support in both vertical and horizontal dimensions to cope with workload fluctuation and dynamic resource reclamation. With a clear separation between application-level computation parallelism and OS-level execution concurrency, ChronoStream enables transparent dynamic scaling and failure recovery by eliminating any network I/O and state-synchronization overhead. Our evaluation on dozens of computing nodes shows that ChronoStream can scale linearly and achieve transparent elasticity and high availability without sacrificing system performance or affecting collocated tenants.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113328"}, {"primary_key": "4429738", "vector": [], "sparse_vector": [], "title": "False rumors detection on Sina Weibo by propagation structures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper studies the problem of automatic detection of false rumors on Sina Weibo, the popular Chinese microblogging social network. Traditional feature-based approaches extract features from the false rumor message, its author, as well as the statistics of its responses to form a flat feature vector. This ignores the propagation structure of the messages and has not achieved very good results. We propose a graph-kernel based hybrid SVM classifier which captures the high-order propagation patterns in addition to semantic features such as topics and sentiments. The new model achieves a classification accuracy of 91.3% on randomly selected Weibo dataset, significantly higher than state-of-the-art approaches. Moreover, our approach can be applied at the early stage of rumor propagation and is 88% confident in detecting an average false rumor just 24 hours after the initial broadcast.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113322"}, {"primary_key": "4429739", "vector": [], "sparse_vector": [], "title": "Scalable SPARQL querying using path partitioning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pingpeng Yuan", "<PERSON>", "<PERSON>"], "summary": "The emerging need for conducting complex analysis over big RDF datasets calls for scale-out solutions that can harness a computing cluster to process big RDF datasets. Queries over RDF data often involve complex self-joins, which would be very expensive to run if the data are not carefully partitioned across the cluster and hence distributed joins over massive amount of data are necessary. Existing RDF data partitioning methods can nicely localize simple queries but still need to resort to expensive distributed joins for more complex queries. In this paper, we propose a new data partitioning approach that takes use of the rich structural information in RDF datasets and minimizes the amount of data that have to be joined across different computing nodes. We conduct an extensive experimental study using two popular RDF benchmark data and one real RDF dataset that contain up to billions of RDF triples. The results indicate that our approach can produce a balanced and low redundant data partitioning scheme that can avoid or largely reduce the cost of distributed joins even for very complicated queries. In terms of query execution time, our approach can outperform the state-of-the-art methods by orders of magnitude.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113334"}, {"primary_key": "4429741", "vector": [], "sparse_vector": [], "title": "PIGEON: Progress indicator for subgraph queries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Peipei Yi", "So<PERSON>v S<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Subgraph queries have been a fundamental query for retrieving patterns from graph data. Due to the well known NP hardness of subgraph queries, those queries may sometimes take a long time to complete. Our recent investigation on real- world datasets revealed that the performance of queries on graphs generally varies greatly. In other words, query clients may occasionally encounter “unexpectedly” long execution from a subgraph query processor. This paper aims to demonstrate a tool that alleviates the problem by monitoring subgraph query progress. Specifically, we present a novel subgraph query progress indicator called PIGEON that exploits query-time information to report to users accurate estimated query progress. In the demonstration, users may interact with PIGEON to gain insights on the query evaluation, which include the following: Users are enabled to (i) monitor query progress; (ii) analyze the causes of long query times; and (iii) abort queries that run abnormally long, which may sometimes contain human errors.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113409"}, {"primary_key": "4429742", "vector": [], "sparse_vector": [], "title": "Dynamic interaction graphs with probabilistic edge decay.", "authors": ["<PERSON><PERSON><PERSON>", "Yuanyuan Tian", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A large scale network of social interactions, such as mentions in Twitter, can often be modeled as a \"dynamic interaction graph\" in which new interactions (edges) are continually added over time. Existing systems for extracting timely insights from such graphs are based on either a cumulative \"snapshot\" model or a \"sliding window\" model. The former model does not sufficiently emphasize recent interactions. The latter model abruptly forgets past interactions, leading to discontinuities in which, e.g., the graph analysis completely ignores historically important influencers who have temporarily gone dormant. We introduce TIDE, a distributed system for analyzing dynamic graphs that employs a new \"probabilistic edge decay\" (PED) model. In this model, the graph analysis algorithm of interest is applied at each time step to one or more graphs obtained as samples from the current \"snapshot\" graph that comprises all interactions that have occurred so far. The probability that a given edge of the snapshot graph is included in a sample decays over time according to a user specified decay function. The PED model allows controlled trade-offs between recency and continuity, and allows existing analysis algorithms for static graphs to be applied to dynamic graphs essentially without change. For the important class of exponential decay functions, we provide efficient methods that leverage past samples to incrementally generate new samples as time advances. We also exploit the large degree of overlap between samples to reduce memory consumption from O(N) to O(logN) when maintaining N sample graphs. Finally, we provide bulk-execution methods for applying graph algorithms to multiple sample graphs simultaneously without requiring any changes to existing graph-processing APIs. Experiments on a real Twitter dataset demonstrate the effectiveness and efficiency of our TIDE prototype, which is built on top of the Spark distributed computing framework.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113363"}, {"primary_key": "4429744", "vector": [], "sparse_vector": [], "title": "Differentially private frequent sequence mining via sampling-based candidate pruning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we study the problem of mining frequent sequences under the rigorous differential privacy model. We explore the possibility of designing a differentially private frequent sequence mining (FSM) algorithm which can achieve both high data utility and a high degree of privacy. We found, in differentially private FSM, the amount of required noise is proportionate to the number of candidate sequences. If we could effectively reduce the number of unpromising candidate sequences, the utility and privacy tradeoff can be significantly improved. To this end, by leveraging a sampling-based candidate pruning technique, we propose a novel differentially private FSM algorithm, which is referred to as PFS 2 . The core of our algorithm is to utilize sample databases to further prune the candidate sequences generated based on the downward closure property. In particular, we use the noisy local support of candidate sequences in the sample databases to estimate which sequences are potentially frequent. To improve the accuracy of such private estimations, a sequence shrinking method is proposed to enforce the length constraint on the sample databases. Moreover, to decrease the probability of misestimating frequent sequences as infrequent, a threshold relaxation method is proposed to relax the user-specified threshold for the sample databases. Through formal privacy analysis, we show that our PFS 2 algorithm is ϵ-differentially private. Extensive experiments on real datasets illustrate that our PFS 2 algorithm can privately find frequent sequences with high accuracy.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113354"}, {"primary_key": "4429745", "vector": [], "sparse_vector": [], "title": "Diversified caching for replicated web search engines.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Commercial web search engines adopt parallel and replicated architecture in order to support high query throughput. In this paper, we investigate the effect of caching on the throughput in such a setting. A simple scheme, called uniform caching, would replicate the cache content to all servers. Unfortunately, it does not exploit the variations among queries, thus wasting memory space on caching the same cache content redundantly on multiple servers. To tackle this limitation, we propose a diversified caching problem, which aims to diversify the types of queries served by different servers, and maximize the sharing of terms among queries assigned to the same server. We show that it is NP-hard to find the optimal diversified caching scheme, and identify intuitive properties to seek good solutions. Then we present a framework with a suite of techniques and heuristics for diversified caching. Finally, we evaluate the proposed solution with competitors by using a real dataset and a real query log.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113285"}, {"primary_key": "4429746", "vector": [], "sparse_vector": [], "title": "AEDSMS: Automotive Embedded Data Stream Management System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Kenya Sato", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ya <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data stream management systems (DSMSs) are useful for the management and processing of continuous data at a high input rate with low latency. In the automotive domain, embedded systems use a variety of sensor data and communications from outside the vehicle to promote autonomous and safe driving. Thus, the software developed for these systems must be capable of handling large volumes of data and complex processing. At present, we are developing a platform for the integration and management of data in an automotive embedded system using a DSMS. However, compared with conventional DSMS fields, we have encountered new challenges such as precompiling queries when designing automotive systems (which demands time predictability), distributed stream processing in in-vehicle networks, and real-time scheduling and sensor data fusion by stream processing. Therefore, we developed an automotive embedded DSMS (AEDSMS) to address these challenges. The main contributions of the present study are: (1) a clear understanding of the challenges faced when introducing DSMSs into the automotive field; (2) the development of AEDSMS to tackle these challenges; and (3) an evaluation of AEDSMS during runtime using a driving assistance application.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113377"}, {"primary_key": "4429749", "vector": [], "sparse_vector": [], "title": "Diversified top-k clique search.", "authors": ["Long Yuan", "Lu <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Maximal clique enumeration is a fundamental problem in graph theory and has been extensively studied. However, maximal clique enumeration is time-consuming in large graphs and always returns enormous cliques with large overlaps. Motivated by this, in this paper, we study the diversified top-k clique search problem which is to find top-k maximal cliques that can cover most number of nodes in the graph. Diversified top-k clique search can be widely used in a lot of applications including community search, motif discovery, and anomaly detection in large graphs. A naive solution for diversified top-k clique search is to keep all maximal cliques in memory and then find k of them that cover most nodes in the graph by using the approximate greedy max k-cover algorithm. However, such a solution is impractical when the graph is large. In this paper, instead of keeping all maximal cliques in memory, we devise an algorithm to maintain k candidates in the process of maximal clique enumeration. Our algorithm has limited memory footprint and can achieve a guaranteed approximation ratio. We also introduce a novel light-weight PNP-Index, based on which we design an optimal maximal clique maintenance algorithm. We further explore three optimization strategies to avoid enumerating all maximal cliques and thus largely reduce the computational cost. We conduct extensive performance studies on six real graphs one of which contains 0.3 billion edges, and the results demonstrate the high efficiency and effectiveness of our approach.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113300"}, {"primary_key": "4429750", "vector": [], "sparse_vector": [], "title": "DaVinci: Data-driven visual interface construction for subgraph search in graph databases.", "authors": ["<PERSON><PERSON>", "So<PERSON>v S<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Due to the complexity of graph query languages, the need for visual query interfaces that can reduce the burden of query formulation is fundamental to the spreading of graph data management tools to a wider community. Despite the significant progress towards building such query interfaces to simplify visual subgraph query formulation task, construction of current generation visual interfaces is not data-driven. That is, it does not exploit the underlying data graphs to automatically generate the contents of various panels in the interface. Such data-driven construction has several benefits such as superior support for subgraph query formulation and portability of the interface across different graph databases. In this demonstration, we present a novel data-driven visual subgraph query interface construction engine called DaVinci. Specifically, it automatically generates from the underlying database two key components of the visual interface to aid subgraph query formulation, namely canned patterns and node labels.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113411"}, {"primary_key": "4429751", "vector": [], "sparse_vector": [], "title": "&quot;Anti-Caching&quot;-based elastic memory management for Big Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The increase in the capacity of main memory coupled with the decrease in cost has fueled the development of in-memory database systems that manage data entirely in memory, thereby eliminating the disk I/O bottleneck. However, as we shall explain, in the Big Data era, maintaining all data in memory is impossible, and even unnecessary. Ideally we would like to have the high access speed of memory, with the large capacity and low price of disk. This hinges on the ability to effectively utilize both the main memory and disk. In this paper, we analyze state-of-the-art approaches to achieving this goal for in-memory databases, which is called as \"Anti-Caching\" to distinguish it from traditional caching mechanisms. We conduct extensive experiments to study the effect of each fine-grained component of the entire process of \"Anti-Caching\" on both performance and prediction accuracy. To avoid the interference from other unrelated components of specific systems, we implement these approaches on a uniform platform to ensure a fair comparison. We also study the usability of each approach, and how intrusive it is to the systems that intend to incorporate it. Based on our findings, we propose some guidelines on designing a good \"Anti-Caching\" approach, and sketch a general and efficient approach, which can be utilized in most in-memory database systems without much code modification.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113375"}, {"primary_key": "4429752", "vector": [], "sparse_vector": [], "title": "Cleaning uncertain data with a noisy crowd.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Uncertain data has been emerged as an important problem in database systems due to the imprecise nature of many applications. To handle the uncertainty, probabilistic databases can be used to store uncertain data, and querying facilities are provided to yield answers with confidence. However, the uncertainty may propagate, hence the returned results from a query or mining process may not be useful. In this paper, we leverage the power of crowdsourcing for cleaning uncertain data. Specifically, we will design a set of Human Intelligence Tasks (HIT)s to ask a crowd to improve the quality of uncertain data. Each HIT is associated with a cost, thus, we need to design solutions to maximize the data quality with minimal number of HITs. There are two obstacles for this non-trivial optimization - first, the crowd has a probability to return incorrect answers; second, the HITs decomposed from uncertain data are often correlated. These two obstacles lead to very high computational cost for selecting the optimal set of HITs. Thus, in this paper, we have addressed these challenges by designing an effective approximation algorithm and an efficient heuristic solution. To further improve the efficiency, we derive tight lower and upper bounds, which are used for effective filtering and estimation. We have verified the solutions with extensive experiments on both a simulated crowd and a real crowdsourcing platform.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113268"}, {"primary_key": "4429754", "vector": [], "sparse_vector": [], "title": "Dish comment summarization based on bilateral topic analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> He", "<PERSON><PERSON><PERSON>"], "summary": "With the prosperity of online services enabled by Web 2.0, huge amount of human generated commentary data are now available on the Internet, covering a wide range of domains on different products. Such comments contain valuable information for other customers, but are usually difficult to utilize due to the lack of common description structure, the complexity of opinion expression and fast growing data volume. Comment-based restaurant summarization is even more challenging than other types of products and services, as users' comments on restaurants are usually mixed with opinions on different dishes but attached with only one overall evaluation score on the whole experience with the restaurants. It is thus crucial to distinguish well-made dishes from other lousy dishes by mining the comment archive, in order to generate meaningful and useful summaries for other potential customers. This paper presents a novel approach to tackle the problem of restaurant comment summarization, with a core technique on the new bilateral topic analysis model on the commentary text data. In the bilateral topic model, the attributes discussed in the comments on the dishes and the user's evaluation on the attributes are considered as two independent dimensions in the latent space. Combined with new opinionated word extraction and clustering-based representation selection algorithms, our new analysis technique is effective to generate high-quality summary using representative snippets from the text comments. We evaluate our proposals on two real-world comment archives crawled from the most popular English and Chinese online restaurant review web sites, Yelp and Dianping. The experimental results verify the huge margin of advantage of our proposals on the summarization quality over baseline approaches in the literature.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113308"}, {"primary_key": "4429755", "vector": [], "sparse_vector": [], "title": "SAR: A sentiment-aspect-region model for user preference analysis in geo-tagged reviews.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Many location based services, such as FourSquare, Yelp, TripAdvisor, Google Places, etc., allow users to compose reviews or tips on points of interest (POIs), each having a geographical coordinates. These services have accumulated a large amount of such geo-tagged review data, which allows deep analysis of user preferences in POIs. This paper studies two types of user preferences to POIs: topical-region preference and category aware topical-aspect preference. We propose a unified probabilistic model to capture these two preferences simultaneously. In addition, our model is capable of capturing the interaction of different factors, including topical aspect, sentiment, and spatial information. The model can be used in a number of applications, such as POI recommendation and user recommendation, among others. In addition, the model enables us to investigate whether people like an aspect of a POI or whether people like a topical aspect of some type of POIs (e.g., bars) in a region, which offer explanation for recommendations. Experiments on real world datasets show that the model achieves significant improvement in POI recommendation and user recommendation in comparison to the state-of-the-art methods. We also propose an efficient online recommendation algorithm based on our model, which saves up to 90% computation time.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113324"}, {"primary_key": "4429756", "vector": [], "sparse_vector": [], "title": "A tale of three graphs: Sampling design on hybrid social-affiliation networks.", "authors": ["Junzhou Zhao", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Random walk-based graph sampling methods have become increasingly popular and important for characterizing large-scale complex networks. While powerful, they are known to exhibit problems when the graph is loosely connected, which slows down the convergence of a random walk and can result in poor estimation accuracy. In this work, we observe that many graphs under study, called target graphs, usually do not exist in isolation. In many situations, a target graph is often related to an auxiliary graph and an affiliation graph, and the target graph becomes better connected when viewed from these three graphs as a whole, or what we called a hybrid social-affiliation network. This viewpoint brings extra benefits to the graph sampling framework, e.g., when directly sampling a target graph is difficult or inefficient, we can efficiently sample it with the assistance of auxiliary and affiliation graphs. We propose three sampling methods on such a hybrid social-affiliation network to estimate target graph characteristics, and conduct extensive experiments on both synthetic and real datasets, to demonstrate the effectiveness of these new sampling methods.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113346"}, {"primary_key": "4429757", "vector": [], "sparse_vector": [], "title": "Interactive Top-k Spatial Keyword queries.", "authors": ["<PERSON>", "<PERSON> Su", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Conventional top-k spatial keyword queries require users to explicitly specify their preferences between spatial proximity and keyword relevance. In this work we investigate how to eliminate this requirement by enhancing the conventional queries with interaction, resulting in Interactive Top-k Spatial Keyword (ITkSK) query. Having confirmed the feasibility by theoretical analysis, we propose a three-phase solution focusing on both effectiveness and efficiency. The first phase substantially narrows down the search space for subsequent phases by efficiently retrieving a set of geo-textual k-skyband objects as the initial candidates. In the second phase three practical strategies for selecting a subset of candidates are developed with the aim of maximizing the expected benefit for learning user preferences at each round of interaction. Finally we discuss how to determine the termination condition automatically and estimate the preference based on the user's feedback. Empirical study based on real PoI datasets verifies our theoretical observation that the quality of top-k results in spatial keyword queries can be greatly improved through only a few rounds of interactions.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113303"}, {"primary_key": "4429758", "vector": [], "sparse_vector": [], "title": "Approximate keyword search in semantic trajectory database.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Driven by the advances in location positioning techniques and the popularity of location sharing services, semantic enriched trajectory data have become unprecedentedly available. While finding relevant Point-of-Interest (POIs) based on users' locations and query keywords has been extensively studied in the past years, it is largely untouched to explore the keyword queries in the context of semantic trajectory database. In this paper, we study the problem of approximate keyword search in massive semantic trajectories. Given a set of query keywords, an approximate keyword query of semantic trajectory (AKQST) returns k trajectories that contain the most relevant keywords to the query and yield the least travel effort in the meantime. The main difference between AKQST and conventional spatial keyword queries is that there is no query location in AKQST, which means the search area cannot be localized. To capture the travel effort in the context of query keywords, a novel utility function, called spatio-textual utility function, is first defined. Then we develop a hybrid index structure called GiKi to organize the trajectories hierarchically, which enables pruning the search space by spatial and textual similarity simultaneously. Finally an efficient search algorithm and fast evaluation of the minimum value of spatio-textual utility function are proposed. The results of our empirical studies based on real check-in datasets demonstrate that our proposed index and algorithms can achieve good scalability.", "published": "2015-01-01", "category": "icde", "pdf_url": "", "sub_summary": "", "source": "icde", "doi": "10.1109/ICDE.2015.7113349"}]