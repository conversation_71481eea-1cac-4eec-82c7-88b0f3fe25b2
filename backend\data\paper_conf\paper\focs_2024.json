[{"primary_key": "587134", "vector": [], "sparse_vector": [], "title": "The Tractability Border of Reachability in Simple Vector Addition Systems with States.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Vector Addition Systems with States (VASS), equivalent to Petri nets, are a well-established model of concurrency. The central algorithmic challenge in VASS is the reachability problem: is there a run from a given starting state and counter values to a given target state and counter values? When the input is encoded in binary, reachability is computationally intractable: even in dimension one, it is NP-hard. In this paper, we comprehensively characterise the tractability border of the problem when the input is encoded in unary. For our main result, we prove that reachability is NP-hard in unary encoded 3-VASS, even when structure is heavily restricted to be a simple linear path scheme. This improves upon a recent result of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2022), in both the number of counters and expressiveness of the considered model, as well as answers open questions of <PERSON><PERSON><PERSON>, <PERSON><PERSON>\\'c, and <PERSON><PERSON><PERSON> (2016) and <PERSON><PERSON><PERSON> (2021). The underlying graph structure of a simple linear path scheme (SLPS) is just a path with self-loops at each node. We also study the exceedingly weak model of computation that is SPLS with counter updates in {-1,0,+1}. Here, we show that reachability is NP-hard when the dimension is bounded by O(\\alpha(k)), where \\alpha is the inverse Ackermann function and k bounds the size of the SLPS. We complement our result by presenting a polynomial-time algorithm that decides reachability in 2-SLPS when the initial and target configurations are specified in binary. To achieve this, we show that reachability in such instances is well-structured: all loops, except perhaps for a constant number, are taken either polynomially many times or almost maximally. This extends the main result of Englert, <PERSON>zi\\'c, and Totzke (2016) who showed the problem is in <PERSON> when the initial and target configurations are specified in unary.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00086"}, {"primary_key": "587135", "vector": [], "sparse_vector": [], "title": "Sensitivity Sampling for k-Means: Worst Case and Stability Optimal Coreset Bounds.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Coresets are arguably the most popular compression paradigm for center-based clustering objectives such as $k$-means. Given a point set $P$, a coreset $\\Omega$ is a small, weighted summary that preserves the cost of all candidate solutions $S$ up to a $(1\\pm\\varepsilon)$ factor. For $k$-means in $d$-dimensional Euclidean space the cost for solution $S$ is $\\Sigma_{p\\in P}{\\min}_{s\\in S}\\Vert p-s\\Vert ^2$. A very popular method for coreset construction, both in theory and practice, is Sensitivity Sampling, where points are sampled in proportion to their importance. We show that Sensitivity Sampling yields optimal coresets of size $\\widetilde{O}(k/\\varepsilon^{2}\\min(\\sqrt{k},\\varepsilon^{-2}))$ for worst-case instances. Uniquely among all known coreset algorithms, for well-clusterable data sets with $\\Omega(1)$, cost stability, Sensitivity Sampling gives coresets of size $\\widetilde{O}(k/\\varepsilon^{2})$, improving over the worst-case lower bound. Notably, Sensitivity Sampling does not have to know the cost stability in order to exploit it: it is appropriately sensitive to the clusterability of the data set while being oblivious to it. We also show that any coreset for stable instances consisting of only input points must have size $\\Omega(k/\\varepsilon^{2})$. Our results for Sensitivity Sampling also extend to the k-median problem, and more general metric spaces.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00106"}, {"primary_key": "587136", "vector": [], "sparse_vector": [], "title": "Near-Optimal Deterministic Network Decomposition and Ruling Set, and Improved MIS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper improves and in two cases nearly settles, up to logarithmically lower order factors, the deterministic complexity of some of the most central problems in distributed graph algorithms, which have been studied for over three decades: • Near-Optimal Network Decomposition: We present a deterministic distributed algorithm that computes a network decomposition in $\\tilde{O}(\\log^{2}n)$ rounds with $O(\\log n)$ diameter and $O(\\log n)$ colors. This round complexity is near-optimal in the following sense: even given an ideal network decomposition, using it (in the standard way) requires round complexity equal to the product of diameter and number of colors, and that is known to be $\\tilde \\Omega(\\log^{2}n)$. We find this near-optimality remarkable, considering the rarity of optimal deterministic distributed algorithms and that for network decomposition, even the first polylogarithmic round algorithm was achieved only recently, by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [STOC 2020], after three decades. • Near-Optimal Ruling Set: We present a deterministic distributed algorithm that computes an O(log log n) ruling set—i.e., an independent set such that each node is within its O(log log n) distance—in O(log n) rounds. This is an exponential improvement on the O(log n) ruling set of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> [FOCS'89], while almost matching their O(log n) round complexity. Our result's round complexity nearly matches the (log n) lower bound of <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> [STOC 2022] that holds for any poly(log log n) ruling set. • Improved Maximal Independent Set (MIS): We present a deterministic distributed algorithm for computing an MIS in $\\tilde{O}(\\log^{5/3}n)$ rounds. This improves on the $\\tilde{O}(\\log^{2}n)$ complexity achieved by Ghaffari and Grunau [STOC 2023] and breaks the log-squared barrier necessary for any method based on network decomposition. By known reductions, the $\\tilde{O}(\\log^{5/3}n)$ round complexity also applies to deterministic algorithms for maximal matching, $\\Delta+1$ vertex coloring, and $(2\\Delta-1)$ edge coloring. Also, via the shattering technique, the improvement spreads also to randomized complexities of these problems, e.g., the new state-of-the-art randomized complexity of $\\Delta+1$ vertex coloring is now $\\tilde{O}$ ((log log $N$ )5/3).", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00007"}, {"primary_key": "587137", "vector": [], "sparse_vector": [], "title": "On Approximating Cutwidth and Pathwidth.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study graph ordering problems with a min-max objective. A classical problem of this type is cutwidth, where given a graph we want to order its vertices such that the number of edges crossing any point is minimized. We give a $\\log^{1+o(1)}(n)$ approximation for the problem, substantially improving upon the previous poly-logarithmic guarantees based on the standard recursive balanced partitioning approach of <PERSON><PERSON> and <PERSON> (FOCS'88). Our key idea is a new metric decomposition procedure that is suitable for handling min-max objectives, which could be of independent interest. We also use this to show other results, including an improved $\\log^{1+o(1)}(n)$ approximation for computing the pathwidth of a graph.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00051"}, {"primary_key": "587138", "vector": [], "sparse_vector": [], "title": "Reverse Mathematics of Complexity Lower Bounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Reverse mathematics is a program in mathematical logic that seeks to determine which axioms are necessary to prove a given theorem. In this work, we systematically explore the reverse mathematics of complexity lower bounds. We explore reversals in the setting of bounded arithmetic, with <PERSON>'s theory PV1 as the base theory, and show that several natural lower bound statements about communication complexity, error correcting codes, and Turing machines are equivalent to widely investigated combinatorial principles such as the weak pigeonhole principle for polynomial-time functions and its variants. As a consequence, complexity lower bounds can be formally seen as fundamental mathematical axioms with far-reaching implications. The proof-theoretic equivalence between complexity lower bound statements and combinatorial principles yields several new implications for the (un)provability of lower bounds. Among other results, we derive the following consequences: • Under a plausible cryptographic assumption, the classical single-tape Turing machine (n2)-time lower bound for Palindrome is unprovable in <PERSON><PERSON><PERSON>'s theory APC1. The conditional unprovability of this simple lower bound goes against the intuition shared by some researchers that most complexity lower bounds could be established in APC1. • While APC1 proves one-way communication lower bounds for Set Disjointness, it does not prove one-way communication lower bounds for Equality, under a plausible cryptographic assumption. • An amplification phenomenon connected to the (un)provability of some lower bounds, under which a quantitatively weak lower bound is provable if and only if a stronger (and often tight) nc lower bound is provable. • Feasibly definable randomized algorithms can be feasibly defined deterministically (APC1 is over PV1) if and only if one-way communication complexity lower bound for Set Disjointness are provable in PV1.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00040"}, {"primary_key": "587140", "vector": [], "sparse_vector": [], "title": "Fast Decision Tree Learning Solves Hard Coding-Theoretic Problems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We connect the problem of properly PAC learning decision trees to the parameterized Nearest Codeword Problem (k-NCP). Despite significant effort by the respective communities, algorithmic progress on both problems has been stuck: the fastest known algorithm for the former runs in quasipolynomial time (<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> 1989) and the best known approximation ratio for the latter is $O$($n$/logn) (<PERSON><PERSON> and <PERSON> 2002; <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> 2009). Research on both problems has thus far proceeded independently with no known connections. We show that any improvement of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s algorithm will yield $O$(logn)-approximation algorithms for k-NCP, an exponential improvement of the current state of the art. This can be interpreted either as a new avenue for designing algorithms for k-NCP, or as one for establishing the optimality of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s algorithm. Furthermore, our reduction along with existing inapproximability results for k - NCP already rule out polynomial-time algorithms for properly learning decision trees. A notable aspect of our hardness results is that they hold even in the setting of weak learning whereas prior ones were limited to the setting of strong learning.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00114"}, {"primary_key": "587141", "vector": [], "sparse_vector": [], "title": "Towards Instance-Optimal Euclidean Spanners.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Csaba D. T<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Euclidean spanners are important geometric objects that have been extensively studied since the 1980s. The two most basic “compactness” measures of a Euclidean spanner $E$ 12We shall identify a graph $H = (X,E)$ with its edge set $E$. All edge weights are given by the Euclidean distances. are the size (number of edges) $\\vert E\\vert$ and the weight (sum of edge weights) $\\Vert E\\Vert$. The state-of-the-art constructions of Euclidean $(1+\\epsilon)$-spanners in $\\mathbb{R}^{d}$ have $o_{d}(_{n\\cdot\\epsilon^{-d+1}})$ edges (or sparsity $O_{d}(\\epsilon^{-d+1}))$ and weight $O_{d}(\\epsilon^{-d} \\log \\epsilon^{-1}) \\cdot\\Vert E_{\\text{mst}}\\Vert$ (or lightness $O_{d}(\\epsilon^{-d}\\log\\epsilon^{-1}))$; here $O_{d}$ suppresses a factor of $d^{O(d)}$ and $\\Vert E_{\\text{mst}}\\Vert$ denotes the weight of a minimum spanning tree of the input point set. Importantly, these two upper bounds are (near-)optimal (up to the $d^{O(d)}$ factor and disregarding the factor of $\\log(\\epsilon^{-1})$ in the lightness bound) for some extremal instances [Le and Solomon, 2019], and therefore they are (near-)optimal in an existential sense. Moreover, both these upper bounds are attained by the same construction-the classic greedy spanner, whose sparsity and lightness are not only existentially optimal, but they also significantly outperform those of any other Euclidean spanner construction studied in an experimental study by [Farshi-Gudmundsson, 2009] for various practical point sets in the plane. This raises the natural question of whether the greedy spanner is (near-) optimal for any point set instance? Motivated by this question, we initiate the study of instance optimal Euclidean spanners. Our results are two-fold. •Rather surprisingly (given the aforementioned experimental study), we demonstrate that the greedy spanner is far from being instance optimal, even when allowing its stretch to grow. More concretely, we design two hard instances of point sets in the plane, where the greedy $(1+x\\epsilon)$-spanner (for basically any parameter $x \\geq 1$) has $\\Omega_{x}(\\epsilon^{-1/2})\\cdot\\vert E_{\\text{spa}} \\vert$ edges and weight $\\Omega_{x}(\\epsilon^{-1})\\cdot\\Vert E_{\\text{light}}\\Vert$, where $E_{\\text{spa}}$ and $E_{\\text{light}}$ denote the per-instance sparsest and lightest $(1 +\\epsilon)$-spanners, respectively, and the $\\Omega_{x}$ notation suppresses a polynomial dependence on $1/x$. •As our main contribution, we design a new construction of Euclidean spanners, which is inherently different from known constructions, achieving the following bounds: a stretch of $1+\\epsilon\\cdot 2^{O(\\log^{*}(d/\\epsilon)}$ with $O(1)\\cdot\\vert E_{\\text{spa}}\\vert$ edges and weight $O(1)$. $\\Vert E_{ \\text{light}}\\Vert$. In other words, we show that a slight increase to the stretch suffices for obtaining instance optimality up to an absolute constant for both sparsity and lightness. Remarkably, there is only a log-star dependence on the dimension in the stretch, and there is no dependence on it whatsoever in the number of edges and weight. In general, for any integer $k\\geq 1$, we can construct a Euclidean spanner in $\\mathbb{R}^{d}$ of stretch $1+\\epsilon\\cdot 2^{O(k)}$ with $O(\\log^{(k)}(\\epsilon^{-1})+\\log^{(k-1)}(d))\\cdot\\vert E_{\\text{spa}}\\vert$ edges and weight $O(\\log^{(k)}(\\epsilon^{-1})+\\log^{(k-1)}(d))\\cdot\\Vert E_{\\text{light}}\\Vert$, where $\\log^{(k)}$ denotes the k-iterated logarithm.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00099"}, {"primary_key": "587142", "vector": [], "sparse_vector": [], "title": "Random Gabidulin Codes Achieve List Decoding Capacity in the Rank Metric.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Gabidulin codes, serving as the rank-metric counterpart of Reed-Solomon codes, constitute an important class of maximum rank distance (MRD) codes. However, unlike the fruitful positive results about the list decoding of Reed-Solomon codes, results concerning the list decodability of Gabidulin codes in the rank metric are all negative so far. For example, in contrast to Reed-Solomon codes, which are always list decodable up to the Johnson bound in the Hamming metric, <PERSON><PERSON> and <PERSON> (IEEE TIT, 2016 and 2017) constructed a class of Gabidulin codes that are not even combinatorially list decodable beyond the unique decoding radius in the rank metric. Proving the existence of Gabidulin codes with good combinatorial list decodability in the rank metric has remained a long-standing open problem. In this paper, we resolve the aforementioned open problem by showing that, with high probability, random Gabidulin codes over sufficiently large alphabets attain the optimal generalized Singleton bound for list decoding in the rank metric. In particular, they achieve list decoding capacity in the rank metric. Our work is significantly influenced by the recent break-throughs in the combinatorial list decodability of Reed-Solomon codes, especially the work by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (STOC 2023). Our major conceptual and technical contributions, which may hold independent interest, consist of the following: (1) We initiate the study of “higher order MRD codes” and provide a novel unified theory, which runs parallel to the theory of “higher order MDS codes” developed by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. (2) We prove a natural analog of the GM-MDS theorem, proven by <PERSON>tt (FOCS 2018) and Yildiz and Hassibi (IEEE TIT, 2019), which we call the GM-MRD theorem. In particular, our GMMRD theorem for Gabidulin codes is strictly stronger than the GM-MDS theorem for Gabidulin codes proven by Yildiz and Hassibi.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00111"}, {"primary_key": "587143", "vector": [], "sparse_vector": [], "title": "Spectral Guarantees for Adversarial Streaming PCA.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In streaming PCA, we see a stream of vectors $x_1, \\ldots, x_n \\in \\mathbb{R}^d$ and want to estimate the top eigenvector of their covariance matrix. This is easier if the spectral ratio $\\boldsymbol{R}=\\lambda_{1}/\\lambda_{2}$ is large. We ask: how large does $\\boldsymbol{R}$ need to be to solve streaming PCA in $\\boldsymbol{\\tilde{O}(d)}$ space? Existing algorithms require $\\boldsymbol{R=\\tilde{\\Omega}({d})}$. We show: • For all mergeable summaries, $\\boldsymbol{R=\\tilde{\\Omega}(\\sqrt{d})}$ is necessary. • In the insertion-only model, a variant of <PERSON><PERSON>'s algorithm gets $\\boldsymbol{o(1)}$ error for $\\boldsymbol{R=O(\\log n \\log d)}$ • No algorithm with $\\boldsymbol{o(d^{2})}$ space gets $\\boldsymbol{o(1)}$ error for $\\boldsymbol{R=O(1)}$. Our analysis is the first application of <PERSON><PERSON>'s algorithm to adversarial streams. It is also the first algorithm for adversarial streaming PCA that is designed for a spectral, rather than Frobenius, bound on the tail; and the bound it needs is exponentially better than is possible by adapting a Frobenius guarantee.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00108"}, {"primary_key": "587144", "vector": [], "sparse_vector": [], "title": "On Pigeonhole Principles and Ramsey in TFNP.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that the TFNP problem Ramsey is not black-box reducible to <PERSON><PERSON>, refuting a conjecture of <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> in the black-box setting. We prove this by giving reductions to <PERSON> from a new family of TFNP problems that correspond to generalized versions of the pigeonhole principle, and then proving that these generalized versions cannot be reduced to Pigeon. Formally, we define $t$-PPP as the class of total NP-search problems reducible to finding a $t$-collision in a mapping from $(t-1) N + 1$ pigeons to $N$ holes. These classes are closely related to multi-collision resistant hash functions in cryptography. We show that the generalized pigeonhole classes form a hierarchy as $t$ increases, and also give a natural condition on the parameters $t_{1}, t_{2}$ that captures exactly when $t_{1}$-PPP and $t_2$-PPP collapse in the black-box setting. Finally, we prove other inclusion and separation results between these generalized Pigeon problems and other previously studied TFNP subclasses, such as PLS, PPA, and PLC. Our separation results rely on new lower bounds in propositional proof complexity based on pseudoexpectation operators, which may be of independent interest.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00033"}, {"primary_key": "587145", "vector": [], "sparse_vector": [], "title": "Constant-Depth Arithmetic Circuits for Linear Algebra Problems.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We design polynomial size, constant depth (namely, $\\text{AC}_{\\mathbb{F}}^{0})$ arithmetic formulae for the greatest common divisor (GCD) of two polynomials, as well as the related problems of the discriminant, resultant, Bézout coefficients, squarefree decomposition, and the inversion of structured matrices like Sylvester and Bézout matrices. Our GCD algorithm extends to any number of polynomials. Previously, the best known arithmetic formulae for these problems required super-polynomial size, regardless of depth. These results are based on new algorithmic techniques to compute various symmetric functions in the roots of polynomials, as well as manipulate the multiplicities of these roots, without having access to them. These techniques allow $\\text{AC}_{\\mathbb{F}}^{0}$ computation of a large class of linear and polynomial algebra problems, which include the above as special cases. We extend these techniques to problems whose inputs are multivariate polynomials, which are represented by constant-depth arithmetic circuits. Here too we solve problems such as computing the GCD and squarefree decomposition in $\\text{AC}_{\\mathbb{F}}^{0}$.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00138"}, {"primary_key": "587146", "vector": [], "sparse_vector": [], "title": "Hardness of Packing, Covering and Partitioning Simple Polygons with Unit Squares.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We show that packing axis-aligned unit squares into a simple polygon P is NP-hard, even when P is an orthogonal and orthogonally convex polygon with half-integer coordinates. It has been known since the early 80s that packing unit squares into a polygon with holes is NP-hard [<PERSON>, <PERSON>, <PERSON>, Inf. Process. Lett., 1981], but the version without holes was conjectured to be polynomial-time solvable more than two decades ago [<PERSON><PERSON> and <PERSON>, Algorith<PERSON>a, 2001]. Our reduction relies on a new way of reducing from Planar-3sat. Interestingly, our geometric realization of a planar formula is non-planar. Vertices become rows and edges become columns, with crossings being allowed. The planarity ensures that all endpoints of rows and columns are incident to the outer face of the resulting drawing. We can then construct a polygon following the outer face that realizes all the logic of the formula geometrically, without the need of any holes. This new reduction technique proves to be general enough to also show hardness of two natural covering and partitioning problems, even when the input polygon is simple. We say that a polygon Q is small if Q is contained in a unit square. We prove that it is NP-hard to find a minimum number of small polygons whose union is P (covering) and to find a minimum number of pairwise interior-disjoint small polygons whose union is P (partitioning), when P is an orthogonal simple polygon with half-integer coordinates. This is the first partitioning problem known to be NP-hard for polygons without holes, with the usual objective of minimizing the number of pieces.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00087"}, {"primary_key": "587147", "vector": [], "sparse_vector": [], "title": "Semi-Bandit Learning for Monotone Stochastic Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Stochastic optimization is a widely used approach for optimization under uncertainty, where uncertain input parameters are modeled by random variables. Exact or approximation algorithms have been obtained for several fundamental problems in this area. However, a significant limitation of this approach is that it requires full knowledge of the underlying probability distributions. Can we still get good (approximation) algorithms if these distributions are unknown, and the algorithm needs to learn them through repeated interactions? In this paper, we resolve this question for a large class of “monotone” stochastic problems, by providing a generic online learning algorithm with $\\sqrt{T\\log T}$ regret relative to the best approximation algorithm (under known distributions). Importantly, our online algorithm works in a semi-bandit setting, where in each period, the algorithm only observes samples from the random variables that were actually probed. Our frame-work applies to several fundamental problems in stochastic optimization such as prophet inequality, Pandora's box, stochastic knapsack, stochastic matchings and stochastic submodular optimization.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00083"}, {"primary_key": "587148", "vector": [], "sparse_vector": [], "title": "Near-Tight Bounds for 3-Query Locally Correctable Binary Linear Codes via Rainbow Cycles.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "We prove that a binary linear code of block length $n$ that is locally correctable with 3 queries against a fraction $\\delta > 0$ of adversarial errors must have dimension at most $o_{\\delta} ( >\\log^{2}n$. log log $n$ ). This is almost tight in view of quadratic Reed-Muller codes being a 3-query locally correctable code (LCC) with dimension $\\Theta^{-}(\\log^{2}n)$. Our result improves, for the binary field case, the $O_{\\delta}(\\text{lo}\\overline{\\mathrm{g}}^{8}n)$ bound obtained in the recent breakthrough of [1] (and the more recent improvement to $O_{\\delta}(\\log^{4}n)$ for binary linear codes announced in [2]). Previous bounds for 3-query linear LCCs proceed by constructing a 2-query locally decodable code (LDC) from the 3-query linear LCC/LDC and applying the strong bounds known for the former. Our approach is more direct and proceeds by bounding the covering radius of the dual code, borrowing inspiration from [3]. That is, we show that if $x\\rightarrow(v_{1}\\cdot x,\\ v_{2}\\cdot x,\\ \\ldots,\\ v_{n}\\cdot x)$ is an arbitrary encoding map $\\mathbb{F}_{2}^{k}\\rightarrow \\mathbb{F}_{\\underline{2}}^{n}$ for the 3-query LCC, then all vectors in $\\mathbb{F}_{2}^{k}$ can be written as a $O_{\\delta}(\\log n)$ -sparse linear com-bination of the $v_{i}{\\prime}s$, which immediately implies $\\overline{k}\\leq\\overline{O}_{\\delta}((\\log n)^{2})$. The proof of this fact proceeds by iteratively∼ reducing the size of any arbitrary linear combination of at least $\\Omega_{\\delta}(\\log n)$ of the $v_{i}{\\prime}s$. We achieve this using the recent breakthrough result of [4] on the existence of rainbow cycles in properly edge-colored graphs, applied to graphs capturing the linear dependencies underlying the local correction property.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00112"}, {"primary_key": "587149", "vector": [], "sparse_vector": [], "title": "A Computational Test of Contextuality and, Even Simpler Proofs of Quantumness.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Bell non-locality is a fundamental feature of quantum mechanics whereby measurements performed on \"spatially separated\" quantum systems can exhibit correlations that cannot be understood as revealing predetermined values.This is a special case of the more general phenomenon of \"quantum contextuality\", which says that such correlations can occur even when the measurements are not necessarily on separate quantum systems, but are merely \"compatible\" (i.e.commuting).Crucially, while any non-local game yields an experiment that demonstrates quantum advantage by leveraging the \"spatial separation\" of two or more devices (and in fact several such demonstrations have been conducted successfully in recent years), the same is not true for quantum contextuality: finding the contextuality analogue of such an experiment is arguably one of the central open questions in the foundations of quantum mechanics.In this work, we show that an arbitrary contextuality game can be compiled into an operational \"test of contextuality\" involving a single quantum device, by only making the assumption that the device is computationally bounded.Our work is inspired by the recent work of <PERSON><PERSON> et al. (STOC '23) that converts any non-local game into a classical test of quantum advantage with a single device.The central idea in their work is to use cryptography to enforce spatial separation within subsystems of a single quantum device.Our work can be seen as using cryptography to enforce \"temporal separation\", i.e. to restrict communication between sequential measurements.Beyond contextuality, we employ our ideas to design a \"proof of quantumness\" that, to the best of our knowledge, is arguably even simpler than the ones proposed in the literature so far.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00073"}, {"primary_key": "587150", "vector": [], "sparse_vector": [], "title": "Trading Determinism for Noncommutativity in Edmonds&apos; Problem.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let $X=X_{1} \\sqcup X_{2} \\sqcup \\ldots \\sqcup X_{k}$ be a partitioned set of variables such that the variables in each part $X_{i}$ are noncommuting but for any $i\\neq j$, the variables $x\\in X_{i}$ commute with the variables $x^{\\prime}\\in X_{j}$. Given as input a square matrix $T$ whose entries are linear forms over $\\mathbb{Q}\\langle X\\rangle$ 〉, we consider the problem of checking if $T$ is invertible or not over the universal skew field of fractions of the partially commutative polynomial ring $\\mathbb{Q}\\langle X\\rangle$ [1]. In this paper, we design a deterministic polynomial-time algorithm for this problem for constant $k$. The special case $k=1$ is the noncommutative <PERSON><PERSON>' problem (NSINGULAR) which has a deterministic polynomial-time algorithm by recent results [2]–[4]. En-route, we obtain the first deterministic polynomial-time algorithm for the equivalence testing problem of $k$-tape weighted automata (for constant $k$) resolving a longstanding open problem [5], [6]. Algebraically, the equivalence problem reduces to testing whether a partially commutative rational series over the partitioned set $X$ is zero or not [6]. Decidability of this problem was established by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> [5]. Prior to this work, a randomized polynomial-time algorithm for this problem was given by Worrell [6] and, subsequently, a deterministic quasipolynomial-time algorithm was also developed [7].", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00042"}, {"primary_key": "587151", "vector": [], "sparse_vector": [], "title": "Constant Degree Direct Product Testers with Small Soundness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let $X$ be a d-dimensional simplicial complex. A function $F: X(k)\\rightarrow\\{0,1\\}^{k}$ is said to be a direct product function if there exists a function $f: x(1)\\rightarrow\\{0,1\\}$ such that $F(\\sigma)=(f(\\sigma_{1}),\\ \\ldots,\\ f(\\sigma_{k}))$ for each k-face $\\sigma$, In an effort to simplify components of the PCP theorem, <PERSON><PERSON><PERSON> and <PERSON><PERSON> [1] introduced the problem of direct product testing, which asks whether one can test if $F: X(k)\\rightarrow\\{0,1\\}^{k}$ - is correlated with a direct product function by querying $F$ on only 2 inputs. <PERSON><PERSON> and <PERSON> [2] conjectured that there exist bounded degree complexes with a direct product test in the small soundness regime. We resolve their conjecture by showing that for all $\\delta > 0$, there exists a family of high-dimensional expanders with degree $O_{\\delta}(1)$ and a 2-query direct product tester with soundness $\\delta$ We use the characterization given by [3] and independently by [4], who showed that some form of non-Abelian coboundary expansion (which they called “Unique-Games coboundary expansion”) is a necessary and sufficient condition for a complex to admit such direct product testers. Our main technical contribution is a general technique for showing coboundary expansion of complexes with coefficients in a non-Abelian group. This allows us to prove that the high dimensional expanders constructed by [5] satisfy the conditions of [3], thus admitting a 2-query direct product tester with small soundness.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00059"}, {"primary_key": "587152", "vector": [], "sparse_vector": [], "title": "Efficient Certificates of Anti-Concentration Beyond Gaussians.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A set of high dimensional points X $= \\{x_{1},x_{2},\\ldots,x_{n}\\}\\subseteq \\mathbb{R}^{d}$ in isotropic position is said to be $\\delta$ -anti concentrated if for every direction $v$, the fraction of points in $X$ satisfying $\\left|\\left\\langle x_i, v\\right\\rangle\\right| \\leqslant \\delta$ is at most $O(\\delta)$. Motivated by applications to list-decodable learning and clustering, three recent works [7], [44], [71] considered the problem of constructing efficient certificates of anti-concentration in the average case, when the set of points X corresponds to samples from a Gaussian distribution. Their certificates played a crucial role in several subsequent works in algorithmic robust statistics on list-decodable learning and settling the robust learnability of arbitrary Gaussian mixtures. Unlike related efficient certificates of concentration properties that are known for wide class of distri-butions [52], the aforementioned approach has been limited only to rotationally invariant distributions (and their affine transformations) with the only prominent example being Gaussian distributions. This work presents a new (and arguably the most natural) formulation for anti- concentration. Using this formulation, we give quasi-polynomial time verifiable sum-of-squares certificates of anti-concentration that hold for a wide class of non-Gaussian distributions including anti-concentrated bounded product distributions and uniform distributions over $L_{p}$ balls (and their affine transformations). Consequently, our method upgrades and extends results in algorithmic robust statistics e.g., list-decodable learning and clustering, to such distributions. As in the case of previous works, our certificates are also obtained via relaxations in the sum-of-squares hierarchy. However, the nature of our argument differs significantly from prior works that formulate anti-concentration as the non-negativity of an explicit polynomial. Our argument constructs a canonical integer program for anti-concentration and analysis a SoS relaxation of it, independent of the intended application. The explicit polynomials appearing in prior works can be seen as specific dual certificates to this program. From a technical standpoint, unlike existing works that explicitly construct sum-of-squares certificates, our argument relies on duality and analyzes a pseudo-expectation on large subsets of the input points that take a small value in some direction. Our analysis uses the method of polynomial reweightings to reduce the problem to analyzing only analytically dense or sparse directions.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00065"}, {"primary_key": "587153", "vector": [], "sparse_vector": [], "title": "High-Temperature Gibbs States are Unentangled and Efficiently Preparable.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that thermal states of local Hamiltonians are separable above a constant temperature. Specifically, for a local Hamiltonian $H$ on a graph with degree $\\mathfrak{g}$, its Gibbs state at inverse temperature $\\beta$, denoted by $\\rho=e^{-\\beta H}/\\text{tr}(e^{-\\beta H})$, is a classical distribution over product states for all $\\beta , where $c$ is a constant. This sudden death of thermal entanglement upends conventional wisdom about the presence of short-range quantum correlations in Gibbs states. Moreover, we show that we can efficiently sample from the distribution over product states. In particular, for any $\\beta , we can prepare a state $\\varepsilon$ -close to $\\rho$ in trace distance with a depth-one quantum circuit and $\\text{poly}(n)\\log(1/\\varepsilon)$ classical overhead. 11 In independent and concurrent work, R<PERSON><PERSON><PERSON>, França, and Alhambra [37] obtain an efficient quantum algorithm for preparing high-temperature Gibbs states via a dissipative evolution.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00068"}, {"primary_key": "587154", "vector": [], "sparse_vector": [], "title": "Structure Learning of Hamiltonians from Real-Time Evolution.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of Hamiltonian structure learning from real-time evolution: given the ability to apply $e^{-\\mathrm{i}Ht}$ for an unknown local Hamiltonian $H=\\Sigma_{a=1}^{m}\\lambda_{a}E_{a}$ on $n$ qubits, the goal is to recover $H$. This problem is already well-understood under the assumption that the interaction terms, $E_{a}$, are given, and only the interaction strengths, $\\lambda_{a}$, are unknown. But how efficiently can we learn a local Hamiltonian without prior knowledge of its interaction structure? We present a new, general approach to Hamiltonian learning that not only solves the challenging structure learning variant, but also resolves other open questions in the area, all while achieving the gold standard of Heisenberg-limited scaling. In particular, our algorithm recovers the Hamiltonian to $\\varepsilon$ error with total evolution time $\\mathcal{O}(\\log(n)/\\varepsilon)$, and has the following appealing properties: 1)It does not need to know the Hamiltonian terms; 2)It works beyond the short-range setting, extending to any Hamiltonian $H$ where the sum of terms interacting with a qubit has bounded norm; 3)It evolves according to $H$ in constant time $t$ increments, thus achieving constant time resolution. As an application, we can also learn Hamiltonians exhibiting power-law decay up to accuracy $\\varepsilon$ with total evolution time beating the standard limit of $1/\\varepsilon^{2}$.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00069"}, {"primary_key": "587155", "vector": [], "sparse_vector": [], "title": "Novel Properties of Hierarchical Probabilistic Partitions and Their Algorithmic Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Alon Hovav"], "summary": "We present a refined construction of hierarchical probabilistic partitions with novel properties, substantially stronger than previously known. Our construction provides a family of hierarchical partitions enabling fast dynamic programming algorithms, by guaranteeing that given a sparse set of balls, each cell of the hierarchical partition intersects only a small number of balls. The number of balls intersecting a cell is bounded solely as a function of the padding parameter of the partition (which is bounded in particular by the doubling dimension). This is in contrast to standard guarantees for probabilistic partitions which holds only in expectation. Additionally, each cell of our partition has a significantly smaller description than in previous constructions. These novel partition properties allow faster dynamic programs for a wide spectrum of fundamental problems defined by inherent or implicit sparsity. Among our main applications highlighting the utility of the novel properties are two well-studied clustering problems: min-sum radii (MSR) and min-sum diameters (MSD) clustering. The input to both these problems is a metric space and an integer $k$, and the goal is to partition the space into $k$ clusters so as to minimize the sum of radii or diameters of the clusters, respectively. We apply our construction to give dramatically improved exact and approximation algorithms for these problems in Euclidean and doubling spaces, planar graphs, and more general settings. In particular, we obtain for these problems the first PTAS for doubling spaces, improving and generalizing upon the time bounds known for Euclidean space, even achieving linear time algorithms for fixed parameter $k$. We also obtain the first PTAS for MSR for all metrics of bounded padding parameter, including planar and minor excluded metrics. Moreover, our results extend to constrained variants such as fair MSR and mergeable MSR, dramatically improving upon the best known results on these problems in low dimension. Our methods also extend to other clustering problems, including $\\alpha$-MSR and $\\alpha$-MSD (where the measure is the sum of radii or diameters raised to power of $\\alpha$), as well as aversion clustering, providing in similar settings the first QPTAS and first fixed parameter PTAS for these problems. Moreover, many of our clustering results extend to the corresponding clustering problems with outliers. Our construction applies as well to a wide range of network design problems possessing inherent sparsity properties in doubling spaces. Notably, we can apply our method to dramatically improve upon the best known bounds for the traveling salesman (TSP) and Steiner tree problems in doubling spaces. Similarly, we significantly improve upon the best known runtimes for Steiner forest, TSP with neighborhoods, prize collecting TSP, and 2-ECSS (two edge-connected spanning subgraph), all in doubling spaces. Our new constructions of hierarchical probabilistic partitions present a major simplification of previous methods, and provide a more natural and useful tool for future applications.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00107"}, {"primary_key": "587156", "vector": [], "sparse_vector": [], "title": "Commitments are Equivalent to Statistically-Verifiable One-Way State Generators.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "One-way state generators (OWSG) [1] are natural quantum analogs to classical one-way functions. We consider statistically-verifiable OWSGs (sv-OWSG), which are potentially weaker objects than OWSGs. We show that $O\\left(\\frac{n}{\\log (n)}\\right)$ -copy sv-OWSGs ($n$ represents the input length) are equivalent to $poly (n)$ -copy sv-OWSGs and to quantum commitments. Since known results show that $o\\left(\\frac{n}{\\log (n)}\\right)$ -copy OWSGs cannot imply commitments [2], this shows that $O\\left(\\frac{n}{\\log(n)}\\right)$ -copy sv-OWSGs are the weakest OWSGs from which we can get commitments (and hence much of quantum cryptography). Our construction follows along the lines of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> [3], who obtained classical pseudorandom generators (PRG) from classical one-way functions (OWF), however with crucial modifications. Our construction, when applied to the classical case, provides an alternative to the construction provided by [3] to obtain a classical mildly non-uniform PRG from any classical OWF. Since we do not argue conditioned on the output $f(x)$, our construction and analysis is arguably simpler and may be of independent interest. For converting a mildly non-uniform PRG to a uniform PRG, we can use the same construction as [3].", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00077"}, {"primary_key": "587157", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Matching and Ordered Ruzsa-Szemerédi Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the fully dynamic maximum matching problem. In this problem, the goal is to efficiently maintain an approximate maximum matching of a graph that is subject to edge insertions and deletions. Our focus is particularly on algorithms that maintain the edges of a $(1-\\varepsilon)$ -approximate maximum matching for an arbitrarily small constant $\\varepsilon > 0$. Until recently, the fastest known algorithm for this problem required $\\Theta(n)$ time per update where $n$ is the number of vertices. This bound was slightly improved to $n/(\\log^{\\ast}n)^{\\Omega(1)}$ by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> [STOC'23] and very recently to $n/2_{-}^{\\Omega(\\sqrt{\\log n})}$ by <PERSON> [FOCS'24]. Whether this can be improved to $n^{1-\\Omega(1)}$ remains a major open problem. In this paper, we introduce Ordered Ruzsa-Szemerédi (ORS) graphs (a generalization of Ruzsa-Szemerédi graphs) and show that the complexity of dynamic matching is closely tied to them. For $\\delta > 0$, define ORS $(\\delta n)$ to be the maximum number of matchings $M_{1}, \\ldots, 1M_{t}$, each of size $\\delta n$, that one can pack in an n-vertex graph such that each matching $M_{i}$ is an induced matching in subgraph $M_{1}\\cup\\ldots\\cup M_{i}$. We show that there is a randomized algorithm that maintains a $(1-\\varepsilon)$ -approximate maximum matching of a fully dynamic graph in amortized update-time. While the value of $\\text{ORS}(\\Theta(n))$ remains unknown and is only upper bounded by $n^{1-o(1)}$, the densest construction known from more than two decades ago only achieves $ORS (\\Theta(n))\\geq n^{1/\\Theta(\\log\\log n)}=n^{o(1)}$ [Fischer et al. STOC'02]. If this is close to the right bound, then our algorithm achieves an update-time of $\\sqrt{n^{1+O(\\varepsilon)}}^{-}$, resolving the aforementioned longstanding open problem in dynamic algorithms in a strong sense.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00027"}, {"primary_key": "587158", "vector": [], "sparse_vector": [], "title": "O(1) Insertion for Random Walk d-ary Cuckoo Hashing up to the Load Threshold.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The random walk d-ary cuckoo hashing algorithm was defined by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> to generalize and improve upon the standard cuckoo hashing algorithm of <PERSON><PERSON> and <PERSON><PERSON>. Random walk d-ary cuckoo hashing has low space overhead, guaranteed fast access, and fast in practice insertion time. In this paper, we give a theoretical insertion time bound for this algorithm. More precisely, for every $d\\geq 3$ hashes, let $c_{d}^{*}$ be the sharp threshold for the load factor at which a valid assignment of $cm$ objects to a hash table of size $m$ likely exists. We show that for any $d\\geq 4$ hashes and load factor $c , the expectation of the random walk insertion time is $O(1)$, that is, a constant depending only on $d$ and $c$ but not $m$.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00017"}, {"primary_key": "587159", "vector": [], "sparse_vector": [], "title": "Nearly Optimal List Labeling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The list-labeling problem captures the basic task of storing a dynamically changing set of up to $n$ elements in sorted order in an array of size $m=(1+\\Theta(1))n$ • The goal is to support insertions and deletions while moving around elements within the array as little as possible. Until recently, the best known upper bound stood at $O(\\log^{2}n)$ amortized cost. This bound, which was first established in 1981, was finally improved two years ago, when a randomized $O(\\log^{3/2}n)$ expected-cost algorithm was discovered. The best randomized lower bound for this problem remains $\\Omega(\\log n)$, and closing this gap is considered to be a major open problem in data structures. In this paper, we present the See-Saw Algorithm, a randomized list-labeling solution that achieves a nearly optimal bound of $O(\\log n \\text{polyloglog}\\ n)$ amortized expected cost. This bound is achieved despite at least three lower bounds showing that this type of result is impossible for large classes of solutions.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00132"}, {"primary_key": "587160", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Classical Open Addressing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce a classical open-addressed hash table, called rainbow hashing, that supports a load factor of up to 1 $-\\varepsilon$, while also supporting $O(1)$ expected-time queries, and $O\\left({\\log \\,\\log {\\varepsilon ^{ - 1}}} \\right)$ expected-time insertions and deletions. We further prove that this tradeoff curve is optimal: any classical open-addressed hash table that supports load factor $1-\\varepsilon$ must incur $\\Omega \\left({\\log \\,log{\\varepsilon ^{ - 1}}} \\right)$ expected time per operation. Finally, we extend rainbow hashing to the setting where the hash table is dynamically resized over time. Surprisingly, the addition of dynamic resizing does not come at any time cost-even while maintaining a load factor of $\\geq 1-\\varepsilon$ at all times, we can support $O(1)$ queries and $O\\left({\\log \\,\\log {\\varepsilon ^{ - 1}}} \\right)$ updates. Prior to our work, achieving any time bounds of the form $o(\\varepsilon^{-1})$ for all of insertions, deletions, and queries simultaneously remained an open Question.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00047"}, {"primary_key": "587161", "vector": [], "sparse_vector": [], "title": "Quantum Computational Advantage with Constant-Temperature Gibbs Sampling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A quantum system coupled to a bath at some fixed, finite temperature converges to its Gibbs state. This thermalization process defines a natural, physically-motivated model of quantum computation. However, whether quantum computational advantage can be achieved within this realistic physical setup has remained open, due to the challenge of finding systems that thermalize quickly, but are classically intractable. Here we consider sampling from the measurement outcome distribution of quantum Gibbs states at constant temperatures, and prove that this task demonstrates quantum computational advantage. We design a family of commuting local Hamiltonians (parent Hamiltonians of shallow quantum circuits) and prove that they rapidly converge to their Gibbs states under the standard physical model of thermalization (as a continuous-time quantum Markov chain). On the other hand, we show that no polynomial time classical algorithm can sample from the measurement outcome distribution by reducing to the classical hardness of sampling from noiseless shallow quantum circuits. The key step in the reduction is constructing a fault-tolerance scheme for shallow IQP circuits against input noise.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00071"}, {"primary_key": "587162", "vector": [], "sparse_vector": [], "title": "Maximum Flow by Augmenting Paths in n2+o(1) Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a combinatorial algorithm for computing exact maximum flows in directed graphs with $n$ vertices and edge capacities from $\\{1, \\ldots, U\\}$ in $n^{2+o(1)}\\log U$ time, which is almost optimal in dense graphs. Our algorithm is a novel implementation of the classical augmenting-path framework; we list augmenting paths more efficiently using a new variant of the push-relabel algorithm that uses additional edge weights to guide the algorithm, and we derive the edge weights by constructing a directed expander hierarchy. Even in unit-capacity graphs, this breaks the long-standing $O(m \\cdot\\min\\{\\sqrt{m},n^{2/3}\\})$ time bound of the previous combinatorial algorithms by <PERSON><PERSON><PERSON><PERSON> (1973) and <PERSON> and <PERSON> (1975) when the graph has $m=\\omega(n^{4/3})$ edges. Notably, our approach does not rely on continuous optimization nor heavy dynamic graph data structures, both of which are crucial in the recent developments that led to the almost-linear time algorithm by <PERSON> et al. (FOCS 2022). Our running time also matches the $n^{2+o(1)}$ time bound of the independent combinatorial algorithm by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (STOC 2024) for computing the maximum bipartite matching, a special case of maximum flow.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00123"}, {"primary_key": "587163", "vector": [], "sparse_vector": [], "title": "Faster (Δ+1)-Edge Coloring: Breaking the m√n Time Barrier.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>'s theorem states that any n-vertex m-edge graph of maximum degree $\\Delta$ can be edge colored using at most $\\Delta+1$ different colors [Diskret. Analiz, '64]. <PERSON><PERSON>'s original proof is algorithmic and shows that such an edge coloring can be found in $\\tilde{O}(mn)$ time. This was subsequently improved to $\\tilde{O}(m\\sqrt{n})$, independently by <PERSON><PERSON><PERSON><PERSON><PERSON> [1982] and by <PERSON><PERSON><PERSON> et al. [1985]. In this paper we present an algorithm that computes such an edge coloring in $\\tilde{O}(mn^{1/3})$, time, giving the first polynomial improvement for this fundamental problem in over 40 years.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00128"}, {"primary_key": "587164", "vector": [], "sparse_vector": [], "title": "Fully Dynamic k-Clustering with Fast Update Time and Small Recourse.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the dynamic metric $k-\\mathbf{median}$ problem, we wish to maintain a set of $k$ centers $S\\subseteq V$ in an input metric space $(V, d)$ that gets updated via point insertions/deletions, so as to minimize the objective $\\sum\\nolimits_{x\\in V}\\min\\nolimits_{y\\in S}d(x, y)$. The quality of a dynamic algorithm is measured in terms of its approximation ratio, “recourse” (the number of changes in $S$ per update) and “update time” (the time it takes to handle an update). The ultimate goal in this line of research is to obtain a dynamic $O(1)$ approximation algorithm with $\\tilde{O}(1)$ recourse and $\\tilde{O}(k)$ update time. Dynamic $k-\\mathbf{median}$ is a canonical example of a class of problems known as dynamic $k-\\mathbf{clustering}$, that has received significant attention in recent years [<PERSON><PERSON> et al, SODA'21], [<PERSON><PERSON> et al, SODA'23], [<PERSON><PERSON> et al, SODA'24]. To the best of our knowledge, however, all these previous papers either attempt to minimize the algorithm's recourse while ignoring its update time, or minimize the algorithm's update time while ignoring its recourse. For dynamic $k-\\mathbf{median}$ in particular, the state-of-the-art results get $\\tilde{O}(k^{2})$ update time and $O(k)$ recourse [Cohen-<PERSON>dad et al, ICML'19], [Henzinger and Kale, ESA'20], [Bhattacharya et al, NeurIPS'23]. But, this recourse bound of $O(k)$ can be trivially obtained by recomputing an optimal solution from scratch after every update, provided we ignore the update time. In addition, the update time of $\\tilde{O}(k^{2})$ is polynomially far away from the desired bound of $\\tilde{O}(k)$. We come arbitrarily close to resolving the main open question on this topic, with the following results. (I) We develop a new framework of randomized local search that is suitable for adaptation in a dynamic setting. For every $\\epsilon > 0$, this gives us a dynamic $k-\\mathbf{median}$ algorithm with $O(k^{\\epsilon})$ approximation ratio, $\\tilde{O}(k^{\\epsilon})$ recourse and $\\tilde{O}(k^{1+\\epsilon})$ update time. This framework also generalizes to dynamic $k-\\mathbf{clustering}$ with $\\ell^{p}$ -norm objectives. As a corollary, we obtain similar bounds for the dynamic $k-\\mathbf{means}$ problem, and a new trade-off between approximation ratio, recourse and update time for the dynamic $k-\\mathbf{center}$ problem. (II) If it suffices to maintain only an estimate of the value of the optimal $k-\\mathbf{median}$ objective, then we obtain a $O(1)$ approximation algorithm with $\\tilde{O}(k)$ update time. We achieve this result via adapting the Lagrangian Relaxation framework of [Jain and Vazirani, JACM'01], and a facility location algorithm of [Mettu and Plaxton, FOCS'00] in the dynamic setting.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00023"}, {"primary_key": "587165", "vector": [], "sparse_vector": [], "title": "Improved Distance (Sensitivity) Oracles with Subquadratic Space.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A distance oracle (DO) for a graph $G$ is a data structure that, when queried with vertices $s,t$, returns an estimate $\\widehat{d}(s,t)$ of their distance in $G$. The oracle has stretch $(\\alpha, \\beta)$ if the estimate satisfies $d(s,t)\\leqslant \\widehat{d}(s,t)\\leqslant \\alpha\\cdot d(s,t)+\\beta$. An $f-\\mathbf{edge}$ fault-tolerant distance sensitivity oracle $(f-\\mathbf{DSO})$ additionally receives a set $F$ of up to $f$ edges and estimates the distance in $G-F$. Our first contribution is the design of new distance oracles with subquadratic space for undirected graphs. We show that introducing a small additive stretch $\\beta > 0$ allows one to make the multiplicative stretch $\\alpha$ arbitrarily small. This sidesteps a known lower bound of $\\alpha\\geqslant 3$ (for $\\beta=0$ and subquadratic space) [Thor<PERSON> & <PERSON>wick, JACM 2005]. We present a DO for graphs with edge weights in $[0, W]$ that, for any positive integer $\\ell$ and any $c\\in(0,\\ell/2]$, has stretch $(1+\\frac{1}{\\ell},2W)$, space $\\widetilde{O}(n^{2-\\frac{c}{\\ell}})$, and query time $O(n^{c})$, generalizing results by Agarwal and Godfrey [SODA 2013] to arbitrarily dense graphs. Our second contribution is a framework that turns an $(\\alpha,\\beta)- \\mathbf{stretch}$ DO for unweighted graphs into an $(\\alpha(1+\\varepsilon),\\beta)-\\mathbf{stretch}. f-\\mathbf{DSO}$ with sensitivity $f=o(\\log(n)/\\log\\log n)$ retaining sub-quadratic space. This generalizes a result by Bilò, Chechik, Choudhary, Cohen, Friedrich, Krogmann, and Schirneck [TheoretiCS 2024]. Combining the framework with our new DO gives an $f-\\mathbf{DSO}$ that, for any $\\gamma\\in(0, (\\ell+1)/2]$, has stretch $((1+\\frac{1}{\\ell})(1+\\varepsilon), 2)$, space $n^{2-\\frac{\\gamma}{(t+1)(f+1)}+o(1)}/\\varepsilon^{f+2}$, and query time $\\widetilde{O}(n^{\\gamma}/\\varepsilon^{2})$. This is the first $f-\\mathbf{DSO}$ with subquadratic space, near-additive stretch, and sublinear query time.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00097"}, {"primary_key": "587166", "vector": [], "sparse_vector": [], "title": "Dot-Product Proofs and Their Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A dot-product proof (DPP) is a simple probabilistic proof system in which the input statement $\\boldsymbol{x}$ and the proof $\\boldsymbol{\\pi}$ are vectors over a finite field $\\mathbb{F}$, and the proof is verified by making a single dot-product query $\\langle \\boldsymbol{q}, (\\boldsymbol{x}\\Vert\\boldsymbol{\\pi})\\rangle$ jointly to $\\boldsymbol{x}$ and $\\boldsymbol{\\pi}$. A DPP can be viewed as a 1-query fully linear PCP. We study the feasibility and efficiency of D PPs, obtaining the following results: •Small-field DPP. For any finite field $\\mathbb{F}$ and Boolean circuit $C$ of size $S$, there is a D PP for proving that there exists $\\boldsymbol{w}$ such that $C(\\boldsymbol{x},\\ \\boldsymbol{w})=1$ with a proof $\\boldsymbol{\\pi}$ of length $S\\cdot \\text{poly}(\\vert \\mathbb{F}\\vert)$ and soundness error $\\varepsilon=O(1/\\sqrt{\\vert \\mathbb{F}\\vert })$. We show this error to be asymptotically optimal. In particular, and in contrast to the best known PCPs, there exist strictly linear-length DPPs over constant-size fields. •Large-field DPP. If $\\vert \\mathbb{F}\\vert\\geq$ poly $(S/\\varepsilon)$, there is a similar DPP with soundness error $\\varepsilon$ and proof length $O(S)$ (in field elements). The above results do not rely on the PCP theorem and their proofs are considerably simpler. We apply our DPP constructions toward two kinds of applications. •Hardness of approximation. We obtain a simple proof for the NP-hardness of approximating MAXLIN (with dense instances) over any finite field $\\mathbb{F}$ up to some constant factor $c > 1$, independent of F. Unlike previous PCP-based proofs, our proof yields exponential-time hardness under the exponential time hypothesis (ETH). •Succinct arguments. We improve the concrete efficiency of succinct interactive arguments in the generic group model using input-independent preprocessing. In particular, the communication is comparable to sending two group elements and the verifier's computation is dominated by a single group exponentiation. We also show how to use DPPs together with linear-only encryption to construct succinct commit-and-prove arguments.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00057"}, {"primary_key": "587167", "vector": [], "sparse_vector": [], "title": "The Sample Complexity of Smooth Boosting and the Tightness of the Hardcore Theorem.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smooth boosters generate distributions that do not place too much weight on any given example. Originally introduced for their noise-tolerant properties, such boosters have also found applications in differential privacy, reproducibility, and quantum learning theory. We study and settle the sample complexity of smooth boosting: we exhibit a class that can be weak learned to $\\gamma$. -advantage over smooth distributions with $m$ samples, for which strong learning over the uniform distribution requires $\\tilde{\\Omega}(1/\\gamma^{2}{)}\\cdot m$, samples. This matches the overhead of existing smooth boosters and provides the first separation from the setting of distribution-independent boosting, for which the corresponding overhead is $O(1/\\gamma)$. Our work also sheds new light on <PERSON><PERSON><PERSON><PERSON><PERSON>'s hardcore theorem from complexity theory, all known proofs of which can be cast in the framework of smooth boosting. For a function $f$ that is mildly hard against size-s circuits, the hardcore theorem provides a set of inputs on which $f$ is extremely hard against size- $s^{\\prime}$ circuits. A downside of this important result is the loss in circuit size, i.e. that $s^{\\prime}\\ll s$. Answering a question of Trevisan, we show that this size loss is necessary and in fact, the parameters achieved by known proofs are the best possible.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00092"}, {"primary_key": "587168", "vector": [], "sparse_vector": [], "title": "Gradient Descent is Pareto-Optimal in the Oracle Complexity and Memory Tradeoff for Feasibility Problems.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In this paper we provide oracle complexity lower bounds for finding a point in a given set using a memory-constrained algorithm that has access to a separation oracle. We assume that the set is contained within the unit $d-\\mathbf{dimensional}$ ball and contains a ball of known radius $\\epsilon > 0$. This setup is commonly referred to as the feasibility problem. We show that to solve feasibility problems with accuracy $\\epsilon\\geq e^{-d^{o(1)}}$, any deterministic algorithm either uses $d^{1+\\delta}$ bits of memory or must make at least $1/(d^{0.01\\delta}\\epsilon^{2\\frac{1-\\delta}{1+1.01\\delta}-o(1)})$ oracle queries, for any $\\delta\\in[0,1]$. Additionally, we show that randomized algorithms either use $d^{1+\\delta}$ memory or make at least $1/(d^{2\\delta}\\epsilon^{2(1-4\\delta)-o(1)})$ queries for any $\\delta\\in[0, \\frac{1}{4}]$. Because gradient descent only uses linear memory $\\mathcal{O}(d\\ln 1/\\epsilon)$ but makes $\\mathcal{O}(1/\\epsilon^{2})$ queries, our results imply that it is Pareto-optimal in the oracle complexity/memory tradeoff. Further, our results show that the oracle complexity for deterministic algorithms is always polynomial in $1/\\epsilon$ if the algorithm has less than quadratic memory in $d$. This reveals a sharp phase transition since with quadratic $\\mathcal{O}(d^{2}\\ln 1/\\epsilon)$ memory, cutting plane methods only require $\\mathcal{O}(d\\ln 1/\\epsilon)$ queries.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00141"}, {"primary_key": "587169", "vector": [], "sparse_vector": [], "title": "Semirandom Planted Clique and the Restricted Isometry Property.", "authors": ["Jaroslaw Blasiok", "Rares-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give a simple, greedy $O(n^{\\omega+0.5})=O(n^{2.872})$ - time algorithm to list-decode planted cliques in a semirandom model introduced in [CSV17] (following [FK01) that succeeds whenever the size of the planted clique is $k\\geq O(\\sqrt{n}\\log^{2}n)$. In the model, the edges touching the vertices in the planted k-clique are drawn independently with probability $p=1/2$ while the edges not touching the planted clique are chosen by an adversary in response to the random choices. Our result shows that the computational threshold in the semirandom setting is within a $O(\\log^{2}n)$ factor of the information-theoretic one [Ste17] thus resolving an open question of <PERSON><PERSON>. This threshold also essentially matches the conjectured computational threshold for the well-studied special case of fully random planted clique. All previous algorithms [CSV17], [MMT20], [BKS23] in this model are based on rather sophisticated rounding algorithms for entropy-constrained semidefinite programming relaxations and their sum-of-squares strengthenings and the best known guarantee is a $n^{O(1/\\varepsilon}$) -time algorithm to list-decode planted cliques of size $k\\geq\\tilde{O}(n^{1/2+\\varepsilon})$. In particular, the guarantee trivializes to quasi-polynomial time if the planted clique is of size $O (\\sqrt{n}$ poly log $n$). Our algorithm achieves an almost optimal guarantee with a surprisingly simple greedy algorithm. The prior state-of-the-art algorithmic result above is based on a reduction to certifying bounds on the size of unbalanced bicliques in random graphs - closely related to certifying the restricted isometry property (RIP) of certain random matrices and known to be hard in the low-degree polynomial model. Our key idea is a new approach that relies on the truth of - but not efficient certificates for - RIP of a new class of matrices built from the input graphs.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00064"}, {"primary_key": "587170", "vector": [], "sparse_vector": [], "title": "Almost-Linear Time Algorithms for Decremental Graphs: Min-Cost Flow and More via Duality.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give the first almost-linear total time algorithm for deciding if a flow of cost at most $F$ still exists in a directed graph, with edge costs and capacities, undergoing decremental updates, i.e., edge deletions, capacity decreases, and cost increases. This implies almost-linear time algorithms for approximating the minimum-cost flow value and s-t distance on such decremental graphs. Our framework additionally allows us to maintain decremental strongly connected components in almost-linear time deterministically. These algorithms also improve over the current best known runtimes for statically computing minimum-cost flow, in both the randomized and deterministic settings. We obtain our algorithms by taking the dual perspective, which yields cut-based algorithms. More precisely, our algorithm computes the flow via a sequence of $m^{1+o(1)}$-dynamic min-ratio cut problems, the dual analog of the dynamic min-ratio cycle problem that underlies recent fast algorithms for minimum-cost flow. Our main technical contribution is a new data structure that returns an approximately optimal min-ratio cut in amortized $m^{o(1)}$ time by maintaining a tree-cut sparsifier. This is achieved by devising a new algorithm to maintain the dynamic expander hierarchy of [$\\text{Goranci-Racke-}$ <PERSON>rak<PERSON>an, SODA 2021] that also works in capacitated graphs. All our algorithms are deterministc, though they can be sped up further using randomized techniques while still working against an adaptive adversary.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00120"}, {"primary_key": "587171", "vector": [], "sparse_vector": [], "title": "Tight Analyses of Ordered and Unordered Linear Probing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Linear-probing hash tables have been classically believed to support insertions in time $\\Theta(x^{2})$, where $1-1/x$ is the load factor of the hash table. Recent work by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (FOCS'21), however, has added a new twist to this story: in some versions of linear probing, if the maximum load factor is at most $1-1/x$, then the amortized expected time per insertion will never exceed $x\\ \\text{polylog}\\ x$ (even in workloads that operate continuously at a load factor of $1-1/x$). Determining the exact asymptotic value for the amortized insertion time remains open. In this paper, we settle the amortized complexity with matching upper and lower bounds of $\\Theta(x\\log^{1.5}x)$. Along the way, we also obtain tight bounds for the so-called path surplus problem, a problem in combinatorial geometry that has been shown to be closely related to linear probing. We also show how to extend <PERSON><PERSON> et al.'s bounds to say something not just about ordered linear probing (the version they study) but also about classical linear probing, in the form that is most widely implemented in practice.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00046"}, {"primary_key": "587172", "vector": [], "sparse_vector": [], "title": "Deterministic Algorithm and Faster Algor<PERSON>m for Submodular Maximization Subject to a Matroid Constraint.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of maximizing a monotone submodular function subject to a matroid constraint, and present for it a deterministic non-oblivious local search algorithm that has an approximation guarantee of $1-1/e-\\epsilon$ (for any $\\epsilon > 0$) and query complexity of $\\tilde{O}_{\\epsilon}(nr)$, where $n$ is the size of the ground set and $r$ is the rank of the matroid. Our algorithm vastly improves over the previous state-of-the-art 0.5008-approximation deterministic algorithm, and in fact, shows that there is no separation between the approximation guarantees that can be obtained by deterministic and randomized algorithms for the problem considered. The query complexity of our algorithm can be improved to $\\tilde{O}_{\\epsilon}(n+\\hat{r}\\sqrt{{n}})$ using randomization, which is nearly-linear for $r=O(\\sqrt{n})$, and is always at least as good as the previous state-of-the-art algorithms.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00050"}, {"primary_key": "587173", "vector": [], "sparse_vector": [], "title": "The Bidirected Cut Relaxation for Steiner Tree has Integrality Gap Smaller Than 2.", "authors": ["Jaroslaw Byrka", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Steiner tree problem is one of the most prominent problems in network design. Given an edge-weighted undirected graph and a subset of the vertices, called terminals, the task is to compute a minimum-weight tree containing all terminals (and possibly further vertices). The best-known approximation algorithms for Steiner tree involve enumeration of a (polynomial but) very large number of candidate components and are therefore slow in practice. A promising ingredient for the design of fast and accurate approximation algorithms for Steiner tree is the bidirected cut relaxation (BCR): bidirect all edges, choose an arbitrary terminal as a root, and enforce that each cut containing some terminal but not the root has one unit of fractional edges leaving it. BCR is known to be integral in the spanning tree case [<PERSON><PERSON>'67], i.e., when all the vertices are terminals. For general instances, however, it was not even known whether the integrality gap of BCR is better than the integrality gap of the natural undirected relaxation, which is exactly 2. We resolve this question by proving an upper bound of 1.9988 on the integrality gap of BCR.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00052"}, {"primary_key": "587174", "vector": [], "sparse_vector": [], "title": "On the Existence of Seedless Condensers: Exploring the Terrain.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While the existence of randomness extractors, both seeded and seedless, has been studied for many sources of randomness, currently, not much is known regarding the existence of seedless condensers in many settings. Here, we prove several new results for seedless condensers in the context of three related classes of sources: Non-Oblivious Symbol Fixing (NOSF) sources, online NOSF (oNOSF) sources (originally defined as SHELA sources in [1]), and almost Chor-Goldreich (CG) sources as defined in [2]. We will think of these sources as a sequence of random variables $\\mathbf{X}=\\mathbf{X}_{1}, \\ldots,\\mathbf{X}_{\\ell}$ on $\\ell$ symbols where at least $g$ out of these $\\ell$ symbols are “good” (i.e., have some min-entropy requirement), denoted as a $(g, \\ell)-\\mathbf{source}$, and the remaining “bad” $\\ell-g$ symbols may adversarially depend on these $g$ good blocks. The difference between each of these sources is realized by restrictions on the power of the adversary, with the adversary in NOSF sources having no restrictions. Prior to our work, the only known seedless condenser upper or lower bound in these settings is due to [2], where they explicitly construct a seedless condenser for a restricted subset of $(g,\\ell)- \\mathbf{adversarial}$ CG sources. The following are our main results concerning seedless condensers for each of these sources. 1) oNOSF sources a) When $g\\leq\\ell/2$, we prove that condensing with error 0.99 above rate $\\frac{1}{\\lfloor\\ell/g\\rfloor}$ is impossible. In fact, we show that this is tight. b) Quite surprisingly, for $g > \\ell/2$, we show the existence of excellent condensers for uniform oNOSF sources. In addition, we show the existence of similar condensers for oNOSF sources with only logarithmic min-entropy. Our results are based on a new type of two-source extractors, called output-light two-source extractors, that we introduce and prove the existence of. 2) Adversarial CG sources a) We observe that uniform adversarial CG sources are equivalent to uniform oNOSF sources and consequently inherit the same results. b) We show that one cannot condense beyond the min-entropy gap of each block or condense low min-entropy CG sources above rate 1/2. 3) NOSF sources a) We show that condensing with constant error above rate $\\frac{g}{\\ell}$ is impossible for uniform NOSF sources for any $g$ and $\\ell$, thus ruling out the possibility of any non-trivial condensing. This shows an interesting distinction between NOSF and oNOSF sources.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00093"}, {"primary_key": "587175", "vector": [], "sparse_vector": [], "title": "Stochastic Online Correlated Selection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We initiate the study of Stochastic Online Correlated Selection (SOCS), a family of online rounding algorithms for the general Non-IID model of Stochastic Online Submodular Welfare Maximization and its special cases such as Online Stochastic Matching, Stochastic Ad-Words, and Stochastic Display Ads. At each time step, the algorithm sees the type of an online item and a fractional allocation of the item, then immediately allocates the item to an agent. We propose a metric called the convergence rate that measures the quality of SOCS algorithms in the above special cases. This is cleaner than most metrics in the related Online Correlated Selection (OCS) literature and may be of independent interest. We propose a Type Decomposition framework that reduces the design of SOCS algorithms to the easier special case of two-way SOCS. First, we sample a surrogate type whose fractional allocation is half-integer. The rounding is trivial for a one-way surrogate type fully allocated to one agent. For a two-way surrogate type split equally between two agents, we round it using a two-way SOCS. We design the distribution of surrogate types to get two-way types as often as possible, while respecting the original fractional allocation in expectation. Following this framework, we make progress on nu-merous problems including two open questions related to AdWords.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00133"}, {"primary_key": "587176", "vector": [], "sparse_vector": [], "title": "Efficient Unitary Designs from Random Sums and Permutations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Fernando G. S. L. Brandão", "<PERSON>"], "summary": "A unitary k-design is an ensemble of unitaries that matches the first $k$ moments of the Haar measure. In this work, we provide two efficient constructions of k-designs on n-qubits using new random matrix theory techniques. Our first construction is based on exponentiating sums of random i.i.d. Hermitian matrices and uses O(k2n2)-many gates. In the spirit of central limit theorems, we show that this random sum approximates the Gaussian Unitary Ensemble (GUE). We then show that the product of just two exponentiated GUE matrices is already approximately Haar random. Our second construction is based on products of exponentiated sums of random permutations and uses Õ($k$ poly ($n$)) many gates. The $k$ dependence is optimal (up to polylogarithmic factors) and is inherited from the efficiency of existing k-wise independent permutations. Furthermore, replacing random permutations with quantum-secure pseudorandom permutations (PRPs), we also obtain a pseudorandom unitary (PRU) ensemble that is secure under nonadaptive queries. A central feature of both proofs is a new connection between the polynomial method in quantum query complexity and the large-dimension ($N$) expansion in random matrix theory. In particular, the first construction uses the polynomial method to control high moments of certain random matrix ensembles without requiring delicate Weingarten calculations. In doing so, we define and solve a moment problem on the unit circle, asking whether a finite number of equally weighted points can reproduce a given set of moments. In our second construction, the key step is to exhibit an orthonormal basis for irreducible representations of the partition algebra that has a low-degree large-$N$ expansion. This allows us to show that the distinguishing probability is a low-degree rational polynomial of the dimension $N$.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00037"}, {"primary_key": "587177", "vector": [], "sparse_vector": [], "title": "Optimal Tradeoffs for Estimating Pauli Observables.", "authors": ["<PERSON><PERSON>", "Weiyuan Gong", "<PERSON>"], "summary": "We revisit the problem of Pauli shadow tomography: given copies of an unknown n-qubit quantum state $\\rho$, estimate Tr $(P\\rho)$ for some set of Pauli operators $F$ to within additive error $\\epsilon$. This has been a popular testbed for exploring the advantage of protocols with quantum memory over those without: with enough memory to measure two copies at a time, one can use Bell sampling to estimate $\\vert \\text{Tr}(P\\rho)$ for all $P$ using $O(n/\\epsilon^{4})$ copies, but with $k\\leq n$ qubits of memory, $\\Omega(2^{(n-k)/3})$ copies are needed. These results leave open several natural questions. How does this picture change in the physically relevant setting where one only needs to estimate a certain subset of Paulis? What is the optimal dependence on $\\epsilon ?$ What is the optimal tradeoff between quantum memory and sample complexity? We answer all of these questions: •For any subset $A$ of Paulis and any family of measurement strategies, we completely characterize the optimal sample complexity, up to $\\log\\vert A\\vert$ factors. •We show any protocol that makes poly $(n)$ -copy measure-ments must make $\\Omega(1/\\epsilon^{4})$ measurements. •For any protocol that makes poly $(n)$ -copy measurements and only has $k qubits of memory, we show that $\\tilde{\\Theta}(\\min\\{2^{n}/\\epsilon^{2},2^{n-k}/\\epsilon^{4}\\})$ copies are necessary and sufficient. The protocols we propose can also estimate the actual values $\\text{Tr}(P\\rho)$, rather than just their absolute values as in prior work. Additionally, as a byproduct of our techniques, we establish tight bounds for the task of purity testing and show that it exhibits an intriguing phase transition not present in the memory-sample tradeoff for Pauli shadow tomography.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00072"}, {"primary_key": "587178", "vector": [], "sparse_vector": [], "title": "An Improved Pseudopolynomial Time Algorithm for Subset Sum.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate pseudo-polynomial time algorithms for Subset Sum. Given a multi-set $X$ of $n$ positive integers and a target $t$, Subset Sum asks whether some subset of $X$ sums to $t$. <PERSON><PERSON> proposes an $\\tilde{O}(n+t)$ -time algorithm [<PERSON><PERSON> SODA'17], and an open question has naturally arisen: can Subset Sum be solved in $O(n+w)$ time? Here $w$ is the maximum integer in $X$. We make a progress towards resolving the open question by proposing an $\\tilde{O}(n+\\sqrt{wt})$ -time algorithm.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00129"}, {"primary_key": "587179", "vector": [], "sparse_vector": [], "title": "Computing Approximate Centerpoints in Polynomial Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "The centerpoint is arguably the most natural generalization of the median to higher dimensions. Intuitively, a centerpoint of a point set is such that any hyperplane passing through the point results in an approximately balanced partition of the point set. <PERSON><PERSON>'s theorem guarantees the existence of a point with depth $\\Omega(1/d)$ which is also known to be the best possible. On the other hand, polynomial time algorithms for approximating centerpoints only guarantee a point with depth $\\Omega(1/d^{2})$, established nearly three decades ago. Unfortunately, even the simpler problem of testing whether a candidate point is a centerpoint is hard. In this paper, we present a novel notion of approximation along with a new algorithmic approach that enables efficient computation of a $\\Omega(1/d)-\\mathbf{depth}$ point. Our main result is a randomized algorithm that computes an $\\varepsilon -\\mathbf{approximate}$ centerpoint of depth $\\Omega(1/d)$; that is, the point returned by the algorithm is at most $\\varepsilon$ away from the halfspaces characterizing points of depth at least $\\Omega(1/d)$ along any direction. Furthermore, the runtime of our algorithm is polynomial in $n, d, 1/\\varepsilon, \\log(1/\\delta)$ where $\\delta$ denotes the failure probability of the algorithm. Our approach is based on a reduction to the smoothed setting where each point is is given an independent Gaussian perturbation of scale $\\varepsilon$. In contrast to prior work, our algorithm is based on techniques from continuous optimization and leverages a novel connection between the problem of testing an approximate centerpoint and the Radial Isotropic Transformation, a central tool with diverse applications in mathematics and computer science. We show that the solution to this testing problem yields an approximate separation oracle for the set of large depth points, enabling its use in a gradient-descent style approach to compute centerpoints.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00104"}, {"primary_key": "587180", "vector": [], "sparse_vector": [], "title": "Tight Bounds for the Zig-Zag Product.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Zig-Zag product of two graphs, $Z= G\\bigcirc{\\!\\!\\!\\!\\!\\! \\mathrm{z}}\\ H$, was introduced in the seminal work of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (Ann. of Math. 2002) and has since become a pivotal tool in theoretical computer science. The classical bound, which is used throughout, states that the spectral expansion of the Zig-Zag product can be bounded roughly by the sum of the spectral expansions of the individual graphs, $\\omega z\\leq\\omega_{H}+\\omega_{G}$. In this work we derive, for every (vertex-transitive) c-regular graph $H$ on $d$ vertices, a tight bound for $\\omega z$ by taking into account the entire spectrum of $H$. Our work reveals that the bound, which holds for every graph $G$, is precisely the minimum value of the function \\begin{equation*}\\frac{x}{c^2} \\cdot \\sqrt{1-\\frac{d \\cdot h(x)}{x \\cdot h^{\\prime}(x)}}\\end{equation*} in the domain $(c^{2},\\ \\infty)$, where $h(x)$ is the characteristic polynomial of $H^{2}$. As a consequence, we establish that Zig-Zag products are indeed intrinsically quadratic away from being Ramanujan. We further prove tight bounds for the spectral ex-pansion of the more fundamental replacement product. Our lower bounds are based on results from analytic combinatorics, and we make use of finite free probability to prove their tightness. In a broader context, our work uncovers intriguing links between the two fields and these well-studied graph operators.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00094"}, {"primary_key": "587181", "vector": [], "sparse_vector": [], "title": "Computational Dynamical Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We study the computational complexity theory of smooth, finite-dimensional dynamical systems. Building off of previous work, we give definitions for what it means for a smooth dynamical system to simulate a Turing machine. We then show that ‘chaotic’ dynamical systems (more precisely, Axiom A systems) and ‘integrable’ dynamical systems (more generally, measure-preserving systems) cannot robustly simulate univer-sal Turing machines, although such machines can be robustly simulated by other kinds of dynamical systems. Subsequently, we show that any Turing machine that can be encoded into a structurally stable one-dimensional dynamical system must have a decidable halting problem, and moreover an explicit time complexity bound in instances where it does halt. More broadly, our work elucidates what it means for one ‘machine’ to simulate another, and emphasizes the necessity of defining low-complexity 'encoders' and 'decoders' to translate between the dynamics of the simulation and the system being simulated. We highlight how the notion of a computational dynamical system leads to questions at the intersection of computational complexity theory, dynamical systems theory, and real algebraic geometry.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00021"}, {"primary_key": "587182", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms for Noncommutative CSPs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Noncommutative constraint satisfaction problems (CSPs) are higher-dimensional operator extensions of classical CSPs. Their approximability remains largely unexplored. A notable example of a noncommutative CSP that is not solvable in polynomial time is NC-Max-3-Cut. We present a 0.864-approximation algorithm for this problem. Our approach extends to a broader class of both classical and noncommutative CSPs. We introduce three key concepts: approximate isometry, relative distribution, and generalized anticommutation, which may be of independent interest.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00061"}, {"primary_key": "587183", "vector": [], "sparse_vector": [], "title": "Gaussian Approximation of Convex Sets by Intersections of Halfspaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the approximability of general convex sets in $\\mathbb{R}^{n}$ by intersections of halfspaces, where the approximation quality is measured with respect to the standard Gaussian distribution and the complexity of an approximation is the number of halfspaces used. While a large body of research has considered the approximation of convex sets by intersections of halfspaces under distance metrics such as the <PERSON><PERSON><PERSON> measure and Ha<PERSON><PERSON>f distance, prior to our work there has not been a systematic study of convex approximation under the Gaussian distribution. We establish a range of upper and lower bounds, both for general convex sets and for specific natural convex sets that are of particular interest. Our results demonstrate that the landscape of approximation is intriguingly different under the Gaussian distribution versus previously studied distance measures. Our results are proved using techniques from many different areas. These include classical results on convex polyhedral approximation, Cramér-type bounds on large deviations from probability theory, and-perhaps surprisingly-a range of topics from computational complexity, including computational learning theory, unconditional pseudorandomness, and the study of influences and noise sensitivity in the analysis of Boolean functions.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00115"}, {"primary_key": "587184", "vector": [], "sparse_vector": [], "title": "Boosting Uniformity in Quasirandom Groups: Fast and Simple.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the communication complexity of multiplying $k\\times t$ elements from the group $H$ = SL $(2, q)$ in the number-on-forehead model with $k$ parties. We prove a lower bound of $(t\\log H)/c^{k}$. This is an exponential improvement over previous work, and matches the state-of-the-art in the area. Relatedly, we show that the convolution of $k^{c}$ independent copies of a 3-uniform distribution over $H^{m}$ is close to a $k$- uniform distribution. This is again an exponential improvement over previous work which needed $c^{k}$ copies. The proofs are remarkably simple; the results extend to other quasirandom groups. We also show that for any group $LI$, any distribution over $H^{m}$ whose weight-k Fourier coefficients are small is close to a k-uniform distribution. This generalizes previous work in the abelian setting, and the proof is simpler.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00091"}, {"primary_key": "587185", "vector": [], "sparse_vector": [], "title": "Agnostically Learning Multi-Index Models with Queries.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the power of query access for the fundamental task of agnostic learning under the Gaussian distribution. In the agnostic model, no assumptions are made on the labels of the examples and the goal is to compute a hypothesis that is competitive with the best-fit function in a known class, i.e., it achieves error opt $+\\epsilon$, where opt is the error of the best function in the class. We focus on a general family of Multi-Index Models (MIMs), which are d-variate functions that depend only on few relevant directions, i.e., have the form $g$ (Wx) for an unknown link function $g$ and a $k\\times d$ matrix W. Multi-index models cover a wide range of commonly studied function classes, including real-valued function classes such as constant-depth neural networks with ReLU activations, and Boolean concept classes such as intersections of halfspaces. Our main result shows that query access gives significant runtime improvements over random examples for agnostically learning both real-valued and Boolean-valued MIMs. Under standard regularity assumptions for the link function (namely, bounded variation or surface area), we give an agnostic query learner for MIMs with running time $O(k)^{\\text{poly}(1/\\epsilon}$) poly $(d)$. In contrast, algorithms that rely only on random labeled examples inherently require $d^{\\text{poly}(1/\\epsilon}$ samples and runtime, even for the basic problem of agnostically learning a single ReLU or a halfspace. As special cases of our general approach, we obtain the following results: •For the class of depth-ℓ, width-S ReLU networks on $\\mathbb{R}^{d}$, our agnostic query learner runs in time poly $(d)2^{\\text{poly}(\\ell S/\\epsilon)}$. This bound qualitatively matches the runtime of an algorithm by [1] for the realizable PAC setting with random examples. •For the class of arbitrary intersections of $k$ halfspaces on $\\mathbb{R}^{d}$, our agnostic query learner runs in time poly $(d)2^{\\text{poly}(\\log(k)/\\epsilon)}$. Prior to our work, no improvement over the agnostic PAC model complexity (without queries) was known, even for the case of a single halfspace. In both these settings, we provide evidence that the $2^{\\text{poly}(1/\\epsilon)}$ runtime dependence is required for proper query learners, even for agnosticallylearning a single ReL U or halfspace. Our algorithmic result establishes a strong computational separation between the agnostic PAC and the agnostic PAC+Query models under the Gaussian distribution for a range of natural function classes. Prior to our work, no such separation was known for any natural concept class - even for the case of a single halfspace, for which it was an open problem posed by Feldman [2]. Our results are enabled by a general dimension-reduction technique that leverages query access to estimate gradients of (a smoothed version of) the underlying label function.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00116"}, {"primary_key": "587186", "vector": [], "sparse_vector": [], "title": "Sum-of-Squares Lower Bounds for Non-Gaussian Component Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Non-Gaussian Component Analysis (NGCA) is the statistical task of finding a non-Gaussian direction in a high-dimensional dataset. Specifically, given i.i.d. samples from a distribution $P_{v}^{A}$ on $\\mathbb{R}^{n}$ that behaves like a known distribution $A$ in a hidden direction $v$ and like a standard Gaussian in the orthogonal complement, the goal is to approximate the hidden direction. The standard formulation posits that the first $k$ - moments of $A$ match those of the standard Gaussian and the $k$-th moment differs. Under mild assumptions, this problem has sample complexity $O(n)$. On the other hand, all known efficient algorithms require $\\Omega(n^{k/2})$ samples. Prior work developed sharp Statistical Query and low-degree testing lower bounds suggesting an information-computation tradeoff for this problem. Here we study the complexity of NGCA in the Sum-of-Squares (SoS) framework. Our main contribution is the first super-constant degree SoS lower bound for NGCA. Specifically, we show that if the non-Gaussian distribution $A$ matches the first $(k-1)$ moments of $\\mathrm{N}(\\mathrm{O},\\ 1)$ and satisfies other mild conditions, then with fewer than $n^{(1-\\varepsilon)k/2}$ many samples from the normal distribution, with high probability, degree $(\\log n)^{\\frac{1}{2}-o_{n}(1)}\\mathbf{SoS}$ fails to refute the existence of such a direction $v$. Our result significantly strengthens prior work by establishing a super-polynomial information-computation tradeoff against a broader family of algorithms. As corollaries, we obtain SoS lower bounds for several problems in robust statistics and the learning of mixture models. Our SoS lower bound proof introduces a novel technique’ that we believe may be of broader interest, and a number of refinements over existing methods. As in previous work, we use the framework of [Barak et al. FOCS 2016], where we express the moment matrix $M$ as a sum of graph matrices, find a factorization $M\\approx LQL^{T}$ using minimum vertex separators, and show that with high probability $Q$ is positive semidefinite (PSD) while the errors are small. Our technical innovations involve the following. First, instead of the minimum weight separator used in prior work, we crucially make use of the minimum square separator. Second, proving that $Q$ is PSD poses significant challenges due to an intrinsic reason. In all prior work, the major part of $Q$ was always a constant term, meaning a matrix whose entries are constant functions of the input. Here, however, even after removing a small error term, $Q$ remains a nontrivial linear combination of non-constant, equally dominating terms. We develop an algebraic method to address this difficulty, which may have wider applications. Specifically, we model the multiplications between the “important” graph matrices by an R.-algebra, construct a representation of this algebra, and use it to analyze $Q$. Via this approach, we show that the PSDness of $Q$ boils down to the multiplicative identities of Hermite polynomials.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00063"}, {"primary_key": "587187", "vector": [], "sparse_vector": [], "title": "Low Acceptance Agreement Tests via Bounded-Degree Symplectic HDXs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We solve the derandomized direct product testing question in the low acceptance regime, by constructing new high dimensional expanders that have no small connected covers. We show that our complexes have swap cocycle expansion, which allows us to deduce the agreement theorem by relying on previous work. Derandomized direct product testing, also known as agreement testing, is the following problem. Let $X$ be a family of k-element subsets of $[N]$ and let $\\{f_{s}:s\\rightarrow\\Sigma\\vert s\\in X\\}$ be an ensemble of local functions, each defined over a subset $s\\subset\\lceil N$. Suppose that we run the following so-called agreement test: choose a random pair of sets $s_{1}, s_{2}\\in X$ that intersect on $\\sqrt{k}$ elements, and accept if $f_{s_{1}}, f_{s_{2}}$ agree on the elements in $s_{1}\\cap s_{2}$. We denote the success probability of this test by Agree $\\{f_{s}\\})$ Given that Agree $(\\{f_{s}\\})=\\varepsilon > 0$ is there a global function $G:[N]\\rightarrow\\Sigma$ such that $f_{s}=G\\vert _{s}$ for a non-negligible fraction of $s\\in X\\ ?$ We construct a family $X$ of k-subsets of $[N]$ such that $\\vert X\\vert =O(N)$, and such that it satisfies the low acceptance agreement theorem. Namely, $\\text{Agree}\\left(\\left\\{f_s\\right\\}\\right)>\\varepsilon \\Longrightarrow \\exists G:[N] \\rightarrow \\Sigma, \\quad \\underset{s}{\\mathbb{P}}\\left[\\left.f_s \\stackrel{0.99}{\\approx} G\\right\\vert_s\\right] \\geqslant \\text{poly}(\\varepsilon)$. A key idea is to replace the well-studied LSV complexes by symplectic high dimensional expanders (HDXs). The family $X$ is just the k-faces of the new symplectic HDXs. The latter serve our needs better since their fundamental group satisfies the congruence subgroup property, which implies that they lack small covers. We also give a polynomial-time algorithm to construct this family of sym-plectic HDXs.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00058"}, {"primary_key": "587189", "vector": [], "sparse_vector": [], "title": "The ESPRIT Algorithm Under High Noise: Optimal Error <PERSON> and Noisy Super-Resolution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Subspace-based signal processing techniques, such as the Estimation of Signal Parameters via Rotational Invariant Techniques (ESPRIT) algorithm, are popular methods for spectral estimation. These algorithms can achieve the so-called super-resolution scaling under low noise conditions, surpassing the well-known Nyquist limit. However, the performance of these algorithms under high-noise conditions is not as well understood. Existing state-of-the-art analysis indicates that ESPRIT and related algorithms can be resilient even for signals where each observation is corrupted by statistically independent, mean-zero noise of size $\\mathcal{O}(1)$, but these analyses only show that the error $\\epsilon$ decays at a slow rate $\\epsilon=\\widetilde{\\mathcal{O}}(n^{-1/2})$ with respect to the cutoff frequency $n$ (i.e., the maximum frequency of the measurements). In this work, we prove that under certain assumptions, the ESPRIT algorithm can attain a significantly improved error scaling $\\epsilon=\\widetilde{\\mathcal{O}}(n^{-3/2})$, exhibiting noisy super-resolution scaling beyond the Nyquist limit $\\epsilon=\\mathcal{O}(n^{-1})$ given by the Nyquist-Shannon sampling theorem. We further establish a theoretical lower bound and show that this scaling is optimal. Our analysis introduces novel matrix perturbation results, which could be of independent interest.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00137"}, {"primary_key": "587190", "vector": [], "sparse_vector": [], "title": "Expansion of High-Dimensional Cubical Complexes: with Application to Quantum Locally Testable Codes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a high-dimensional cubical complex, for any dimension $t \\in \\mathbb{N}$, and apply it to the design of quantum locally testable codes. Our complex is a natural generalization of the constructions by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and by <PERSON><PERSON> et. al of a square complex (case $t=2$), which have been applied to the design of classical locally testable codes (LTC) and quantum low-density parity check codes (qLDPC) respectively. We turn the geometric (cubical) complex into a chain complex by relying on constant-sized local codes $h_{1}, \\ldots,h_{t}$ as gadgets. A recent result of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> on existence of tuples of codes that are product expanding enables us to prove lower bounds on the cycle and co-cycle expansion of our chain complex. For $t=4$ our construction gives a new family of “almost-good” quantum LTCs - with constant relative rate, inverse-polylogarithmic relative distance and soundness, and constant-size parity checks. Both the distance of the quantum code and its local testability are proven directly from the cycle and co-cycle expansion of our chain complex.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00031"}, {"primary_key": "587191", "vector": [], "sparse_vector": [], "title": "How to Simulate Random Oracles with Auxiliary Input.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The random oracle model (ROM) allows us to opti-mistically reason about security properties of cryptographic hash functions, and has been hugely influential in designing practical cryptosystems. But it is overly optimistic against non-uniform adversaries, and often suggests security properties and security levels unachievable by any real hash function. To reconcile with this discrepancy, <PERSON><PERSON><PERSON> [CRYPTO '07] proposed the auxiliary-input random oracle model (AI-ROM), where a non-uniform attacker additionally gets a bounded amount of advice about the random oracle. Proving security in the AI-ROM is often much more difficult, but a series of works starting with <PERSON>ruh provided useful technical tools to do so. Although these tools lead to good results in the information-theoretic setting, they are unsatisfactory in the computational setting, where the random oracle is used alongside other computational hardness assumptions. At the most basic level, we did not even know whether it is possible to efficiently simulate random oracle queries given auxiliary input, which has remained as an explicit open problem since the work of <PERSON><PERSON><PERSON>. In this work, we resolve the above open problem and show how to efficiently simulate auxiliary-input random oracles. Moreover, the simulation has low concrete overhead, leading to small losses in exact security. We use it to prove the security of a broad class of computational schemes in the AI-ROM, including the first non-interactive zero-knowledge (NIZK) scheme in the AI-ROM. As a tool of independent interest, we develop a new notion of ultra-secure pseudorandom functions with fast RAM evaluation, which can achieve $2^{\\lambda}$ security while having sublinear $\\mathrm{o}(\\lambda)$ evaluation time.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00080"}, {"primary_key": "587192", "vector": [], "sparse_vector": [], "title": "First-Order Model Checking on Monadically Stable Graph Classes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A graph class $\\mathscr{C}$ is called monadically stable if one cannot interpret, in first-order logic, arbitrary large linear orders in colored graphs from $\\mathscr{C}$. We prove that the model checking problem for first-order logic is fixed-parameter tractable on every monadically stable graph class. This extends the results of [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>; <PERSON><PERSON>17] for nowhere dense classes and of [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>; ST<PERSON> '23] for structurally nowhere dense classes to all monadically stable classes. This result is complemented by a hardness result showing that monadic stability is precisely the dividing line between tractability and intractability of first-order model checking on hereditary classes that are edge-stable: exclude some half-graph as a semi-induced subgraph. Precisely, we prove that for every hereditary graph class $\\mathscr{C}$ that is edge-stable but not monadically stable, first-order model checking is $\\text{AW}[*]$ -hard on $\\mathscr{C}$, and W[1]-hard when restricted to existential sentences. This confirms, in the special case of edge-stable classes, an open conjecture that the notion of monadic dependence delimits the tractability of first-order model checking on hereditary classes of graphs. For our tractability result, we first prove that monadically stable graph classes have almost linear neighborhood complexity, by combining tools from stability theory and from sparsity theory. We then use this result to construct sparse neighborhood covers for monadically stable graph classes, which provides the missing ingredient for the algorithm of [<PERSON>eier, <PERSON><PERSON>hl<PERSON>, <PERSON>ebertz; <PERSON><PERSON> '23]. The key component of this construction is the usage of orders with low crossing number [Welzl; SoCG '88], a tool from the area of range queries. For our hardness result, we first prove a new characterization of monadically stable graph classes in terms of forbidden induced subgraphs. We then use this characterization to show that in hereditary classes that are edge-stable but not monadically stable, one can efficiently interpret the class of all graphs using only existential formulas; this implies W[1]-hardness of model checking already for existential formulas.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00012"}, {"primary_key": "587193", "vector": [], "sparse_vector": [], "title": "Online Combinatorial Allocations and Auctions with Few Samples.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In online combinatorial allocations/auctions, $n$ bidders sequentially arrive, each with a combinatorial valuation (such as submodular/XOS) over subsets of $m$ indivisible items. The aim is to immediately allocate a subset of the remaining items to maximize the total welfare, defined as the sum of bidder valuations. A long line of work has studied this problem when the bidder valuations come from known independent distributions. In particular, for submodular/XOS valuations, we know 2-competitive algorithms/mechanisms that set a fixed price for each item and the arriving bidders take their favorite subset of the remaining items given these prices. However, these algorithms traditionally presume the availability of the underlying distributions as part of the input to the algorithm. Contrary to this assumption, practical scenarios often require the learning of distributions, a task complicated by limited sample availability. This paper investigates the feasibility of achieving $O$(1) -competitive algorithms under the realistic constraint of having access to only a limited number of samples from the underlying bidder distributions. Our first main contribution shows that a mere single sample from each bidder distribution is sufficient to yield an $O$ (1)-competitive algorithm for submodular/XOS valuations. This result leverages a novel extension of the secretary-style analysis, employing the sample to have the algorithm compete against itself. Although online, this first approach does not provide an online truthful mechanism. Our second main contribution shows that a polynomial number of samples suffices to yield a (2 + ∊) -competitive online truthful mechanism for submodular/XOS valuations and any constant ∊ > 0. This result is based on a generalization of the median-based algorithm for the single-item prophet inequality problem to combinatorial settings with multiple items.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00081"}, {"primary_key": "587194", "vector": [], "sparse_vector": [], "title": "Efficient and Near-Optimal Noise Generation for Streaming Differential Privacy.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In the task of differentially private (DP) continual counting, we receive a stream of increments and our goal is to output an approximate running total of these increments, without revealing too much about any specific increment. Despite its simplicity, differentially private continual counting has attracted significant attention both in theory and in practice. Existing algorithms for differentially private continual counting are either inefficient in terms of their space usage or add an excessive amount of noise, inducing suboptimal utility. The most practical DP continual counting algorithms add carefully correlated Gaussian noise to the values. The task of choosing the covariance for this noise can be expressed in terms of factoring the lower-triangular matrix of ones (which computes prefix sums). We present two approaches from this class (for different parameter regimes) that achieve near-optimal utility for DP continual counting and only require logarithmic or polylogarithmic space (and time). Our first approach is based on a space-efficient streaming matrix multiplication algorithm for a class of Toeplitz matrices. We show that to instantiate this algorithm for DP continual counting, it is sufficient to find a low-degree rational function that approximates the square root on a circle in the complex plane. We then apply and extend tools from approximation theory to achieve this. We also derive efficient closed-forms for the objective function for arbitrarily many steps, and show direct numerical optimization yields a highly practical solution to the problem. Our second approach combines our first approach with a recursive construction similar to the binary tree mechanism.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00135"}, {"primary_key": "587195", "vector": [], "sparse_vector": [], "title": "Sensitivity, Proximity and FPT Algorithms for Exact Matroid Problems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of finding a basis of a matroid with weight exactly equal to a given target. Here weights can be discrete values from $\\{-\\Delta,\\ \\ldots,\\ \\Delta\\}$ or more generally m-dimensional vectors of such discrete values. We resolve the parameterized complexity completely, by presenting an FPT algorithm parameterized by $\\Delta$ and $m$ for arbitrary matroids. Prior to our work, no such algorithms were known even when weights are in $\\{0,1\\}$, or arbitrary $\\Delta$ and $m=1$. Our main technical contributions are new proximity and sensitivity bounds for matroid problems, independent of the number of elements. These bounds imply FPT algorithms via matroid intersection.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00100"}, {"primary_key": "587196", "vector": [], "sparse_vector": [], "title": "Verifying Groups in Linear Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Consider the following problem: Given an $n$ × $n$ multiplication table, decide whether it is a <PERSON><PERSON>ley multiplication table of a group. Among deterministic algorithms for this problem, the best known algorithm is implied by <PERSON><PERSON> <PERSON><PERSON>'s associativity test (1949) and has running time of ${O}(n^{2}\\log n)$. Allowing randomization. the best known algorithm has running time of $O(n^{2}\\log(1/\\delta))$, where $\\delta > 0$ is the error probability of the algorithm (<PERSON><PERSON><PERSON><PERSON> and <PERSON>, FOCS 1996, SICOMP 2000). In this work, we improve upon both of the above known algorithms. Specifically, we present a deterministic algorithm for the above problem whose running time is $O(n^{2})$. This performance is optimal up to constants. A central tool we develop is an efficient algorithm for finding a subset $A$ of a group $G$ satisfying $A^{2}=G$ while $\\vert A\\vert=O(\\sqrt{\\vert G\\vert })$.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00126"}, {"primary_key": "587197", "vector": [], "sparse_vector": [], "title": "Optimal Bounds for Open Addressing Without Reordering.", "authors": ["<PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we revisit one of the simplest problems in data structures: the task of inserting elements into an open-addressed hash table so that elements can later be retrieved with as few probes as possible. We show that, even without reordering elements over time, it is possible to construct a hash table that achieves far better expected search complexities (both amortized and worst-case) than were previously thought possible. Along the way, we disprove the central conjecture left by <PERSON> in his seminal paper “Uniform Hashing is Optimal”.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00045"}, {"primary_key": "587198", "vector": [], "sparse_vector": [], "title": "Sparse Graph Counting and Kelley-Meka Bounds for Binary Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In a recent breakthrough, <PERSON> and <PERSON> (FOCS 2023) obtained a strong upper bound on the density of sets of integers without non-trivial three-term arithmetic progressions. In this work, we extend their result, establishing similar bounds for all linear patterns defined by binary systems of linear forms, where “binary” indicates that every linear form depends on exactly two variables. Prior to our work, no strong bounds were known for such systems even in the finite field model setting. A key ingredient in our proof is a graph counting lemma. The classical graph counting lemma, developed by <PERSON><PERSON> (Random Graphs 1985) and <PERSON>, <PERSON>, and <PERSON> (Combinatorica 1989), is a fundamental tool in combinatorics. For a fixed graph $H$, it states that the number of copies of $H$ in a pseudorandom graph $G$ is similar to the number of copies of $H$ in a purely random graph with the same edge density as $G$. However, this lemma is only non-trivial when $G$ is a dense graph. In this work, we prove a graph counting lemma that is also effective when $G$ is sparse. Moreover, our lemma is well-suited for density increment arguments in additive number theory. As an immediate application, we obtain a strong bound for the Turán problem in abelian Cayley sum graphs: let $\\Gamma$ be a finite abelian group with odd order. If a Cayley sum graph on $\\Gamma$ does not contain any r-elique as a sub graph, it must have at most $2^{-\\Omega_r\\left(\\log ^{1 / 16}\\vert \\Gamma\\vert \\right)} \\cdot\\vert \\Gamma\\vert ^2$ edges. These results hinge on the technology developed by <PERSON> and <PERSON><PERSON> and the follow-up work by <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (<PERSON><PERSON> 2024).", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00098"}, {"primary_key": "587199", "vector": [], "sparse_vector": [], "title": "Near-Optimal (1+ε)-Approximate Fully-Dynamic All-Pairs Shortest Paths in Planar Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the fully-dynamic all-pair shortest paths (APSP) problem on planar graphs: given an $n-\\mathbf{vertex}$ planar graph $G=(V, E)$ undergoing edge insertions and deletions, the goal is to efficiently process these updates and support distance and shortest path queries. We give a $(1+\\epsilon)-\\mathbf{approximate}$ dynamic algorithm that supports edge updates and distance queries in $n^{o(1)}$ time, for any $1/\\mathbf{poly}(\\log n) < \\epsilon < 1$. Our result is a significant improvement over the best previously known bound of $\\tilde{O}(\\sqrt{n})$ on update and query time due to [<PERSON>, <PERSON><PERSON><PERSON>, and Gav<PERSON>le, STOC ’12], and bypasses a $\\Omega(\\sqrt{n})$ conditional lower-bound on update and query time for exact fully dynamic planar APSP [<PERSON><PERSON><PERSON> and <PERSON>, FOCS ’16]. The main technical contribution behind our result is to dynamize the planar emulator construction due to [<PERSON>, <PERSON>, <PERSON>, <PERSON>OC ’22].", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00124"}, {"primary_key": "587200", "vector": [], "sparse_vector": [], "title": "Ramsey Theorems for Trees and a General &apos;Private Learning Implies Online Learning&apos; Theorem.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work continues to investigate the link between differentially private (DP) and online learning. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> [4] showed that for binary concept classes, DP learnability of a given class implies that it has a finite Littlestone dimension (equivalently, that it is online learnable). Their proof relies on a model-theoretic result by <PERSON> [36], which demonstrates that any binary concept class with a large Littlestone dimension contains a large subclass of thresholds. In a follow-up work, <PERSON>, <PERSON>, and <PERSON><PERSON> [38] extended this proof to multiclass PAC learning with a bounded number of labels. Unfortunately, <PERSON>'s result does not apply in other natural settings such as multiclass PAC learning with an unbounded label space, and PAC learning of partial concept classes. This naturally raises the question of whether DP learnability continues to imply online learnability in more general scenarios: indeed, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> [5] explicitly leave it as an open question in the context of partial concept classes, and the same question is open in the general multiclass setting. In this work, we give a positive answer to these questions showing that for general classification tasks, DP learnability implies online learnability. Our proof reasons directly about Littlestone trees, without relying on thresholds. We achieve this by establishing several Ramsey-type theorems for trees, which might be of independent interest.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00119"}, {"primary_key": "587201", "vector": [], "sparse_vector": [], "title": "Computing the 3-Edge-Connected Components of Directed Graphs in Linear Time.", "authors": ["<PERSON><PERSON>", "Giuseppe F<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Let $G$ be a directed graph with $m$ edges and $n$ vertices. We present a deterministic linear-time algorithm for computing the 3-edge-connected components of $G$. This is a significant improvement over the previous best bound by <PERSON><PERSON> et al. [SODA 2023], which is $\\tilde{O}(m\\sqrt{m})$ and randomized. Our result is based on a novel characterization of 2-edge cuts in directed graphs and on a new technique that exploits the concept of divergent spanning trees and 2-connectivity-light graphs, and requires a careful modification of the minset-poset technique of Gabow [TALG 2016]. As a side result, our new technique yields also an oracle for providing in constant time a minimum edge-cut for any two vertices that are not 3-edge-connected. The oracle uses space $O(n)$ and can be built in $O(m\\log n)$ time: given two query vertices, it determines in constant time whether they are 3-edge-connected, or provides a k-edge cut, with $k\\leq 2$, that separates them.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00015"}, {"primary_key": "587202", "vector": [], "sparse_vector": [], "title": "Naively Sorting Evolving Data is Optimal and Robust.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study comparison sorting in the evolving data model, introduced by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON> (2011), where the true total order changes while the sorting algorithm is processing the input. More precisely, each comparison operation of the algorithm is followed by a sequence of evolution steps, where an evolution step perturbs the rank of a random item by a “small” random value. The goal is to maintain an ordering that remains close to the true order over time. Previous works have analyzed adaptations of classic sorting algorithms, assuming that an evolution step changes the rank of an item by just one, and that a fixed constant number $b$ of evolution steps take place between two comparisons. In fact, the only previous result achieving optimal linear total deviation, by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (2018a), applies just for $b=1$. We analyze a very simple sorting algorithm suggested by <PERSON><PERSON><PERSON> (2014), which samples a random pair of adjacent items in each step and swaps them if they are out of order. We show that the algorithm achieves and maintains, with high probability, optimal total deviation, $O(n)$, and optimal maximum deviation, $O(\\log n)$, under very general model settings. Namely, the perturbation introduced by each evolution step is sampled from a general distribution of bounded moment generating function, and we just require that the average number of evolution steps between two sorting steps be bounded by an (arbitrary) constant, where the average is over a linear number of steps. The key ingredients of our proof are a novel potential function argument that inserts “gaps” in the list of items, and a general analysis framework which separates the analysis of sorting from that of the evolution steps, and is applicable to a variety of settings for which previous approaches do not apply. Our results settle conjectures and open problems in the three aforementioned works, and provide theoretical support that simple quadratic algorithms are optimal and robust for sorting evolving data, as empirically observed by Be<PERSON> <PERSON>l, <PERSON><PERSON>y, E<PERSON><PERSON>, Goodrich and <PERSON> (2018b).", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00130"}, {"primary_key": "587203", "vector": [], "sparse_vector": [], "title": "Decoding Quasi-Cyclic Quantum LDPC Codes.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "Quantum low-density parity-check (qLDPC) codes are an important component in the quest for quantum fault tolerance. Dramatic recent progress on qLDPC codes has led to constructions which are asymptotically good, and which admit linear-time decoders to correct errors affecting a constant fraction of codeword qubits. These constructions, while theoretically explicit, rely on inner codes with strong properties only shown to exist by probabilistic arguments, resulting in lengths that are too large to be practically relevant. In practice, the surface/toric codes, which are the product of two repetition codes, are still often the qLDPC codes of choice. A previous construction of qLDPC codes based on the lifted product of an expander-based classical LDPC code with a repetition code (<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, 2020) achieved a near-linear distance, and avoids the need for such intractable inner codes. Our main result is an efficient decoding algorithm for these codes that corrects a near-linear number of adversarial errors. En route, we give a similar algorithm for the hypergraph product version these codes, which are simpler but have distance growing only as the square root of the block length. Our decoding algorithms leverage the fact that the codes we consider are quasi-cyclic, meaning that they respect a cyclic group symmetry. Since the repetition code is not based on expanders, previous approaches to decoding expander-based qLDPC codes, which typically worked by greedily flipping code bits to reduce some potential function, do not apply in our setting. Instead, we reduce our decoding problem (in a black-box manner) to that of decoding classical expander-based LDPC codes under noisy parity-check syndromes. For completeness, we also include a treatment of such classical noisy-syndrome decoding that is sufficient for our application to the quantum setting.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00029"}, {"primary_key": "587204", "vector": [], "sparse_vector": [], "title": "Exploration is Harder than Prediction: Cryptographically Separating Reinforcement Learning from Supervised Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Supervised learning is often computationally easy in practice. But to what extent does this mean that other modes of learning, such as reinforcement learning (RL), ought to be computationally easy by extension? In this work we show the first cryptographic separation between RL and supervised learning, by exhibiting a class of block MDPs and associated decoding functions where reward-free exploration is provably computationally harder than the associated regression problem. We also show that there is no computationally efficient algorithm for reward-directed RL in block MDPs, even when given access to an oracle for this regression problem. It is known that being able to perform regression in block MDPs is necessary for finding a good policy; our results suggest that it is not sufficient. Our separation lower bound uses a new robustness property of the Learning Parities with Noise (LPN) hardness assumption, which is crucial in handling the dependent nature of RL data. We argue that separations and oracle lower bounds, such as ours, are a more meaningful way to prove hardness of learning because the constructions better reflect the practical reality that supervised learning by itself is often not the computational bottleneck.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00117"}, {"primary_key": "587205", "vector": [], "sparse_vector": [], "title": "Improved Condensers for Chor-Goldreich Sources.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "One of the earliest models of weak randomness is the Chor-<PERSON><PERSON>ich (CG) source. A $(t, n, k)\\text{-}$ CG source is a sequence of random variables X $=(\\mathrm{x}_{1}, \\ldots, \\mathrm{x}_{t})\\sim(\\{0,1\\}^{n})^{t}$, where each $\\mathrm{X}_{i}$ has min-entropy $k$ conditioned on any fixing of $\\mathrm{x}_{1}, \\ldots, \\mathrm{x}_{i-1}$. <PERSON><PERSON> and <PERSON><PERSON><PERSON> proved that there is no deterministic way to extract randomness from such a source. Nevertheless, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> showed that there is a deterministic way to condense a CG source into a string with small entropy gap. They gave applications of such a condenser to simulating randomized algorithms with small error and to certain cryptographic tasks. They studied the case where the block length $n$ and entropy rate $k/n$ are both constant. We study the much more general setting where the block length can be arbitrarily large, and the entropy rate can be arbitrarily small. We construct the first explicit condenser for CG sources in this setting, and it can be instantiated in a number of different ways. When the entropy rate of the CG source is constant, our condenser requires just a constant number of blocks $t$ to produce an output with entropy rate 0.9, say. In the low entropy regime, using $t= \\text{poly} (n)$ blocks, our condenser can achieve output entropy rate 0.9 even if each block has just 1 bit of min-entropy. Moreover, these condensers have exponentially small error. Finally, we provide strong existential and impossibility results. For our existential result, we show that a random function is a seedless condenser (with surprisingly strong parameters) for any small family of sources. As a corollary, we get new existential results for seeded condensers and condensers for CG sources. For our impossibility result, we show the latter result is nearly tight, by giving a simple proof that the output of any condenser for CG sources must inherit the entropy gap of (one block of) its input.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00096"}, {"primary_key": "587206", "vector": [], "sparse_vector": [], "title": "Fast List Decoding of Univariate Multiplicity and Folded Reed-Solomon Codes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that the known list-decoding algorithms for univariate multiplicity and folded Reed-Solomon (FRS) codes can be made to run in $\\tilde{O}(n)$ time. Univariate multiplicity codes and FRS codes are natural variants of Reed-Solomon codes that were discovered and studied for their applications to list decoding. It is known that for every $\\varepsilon > 0$, and rate $r\\in(0,1)$, there exist explicit families of these codes that have rate $r$ and can be list decoded from a $(1-r-\\varepsilon)$ fraction of errors with constant list size in polynomial time (<PERSON><PERSON><PERSON> (IEEE Trans. Inform. Theory 2013) and <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON> (SIAM J. Comput. 2023)). In this work, we present randomized algorithms that perform the above list-decoding tasks in $\\tilde{O}(n)$, where $n$ is the block-length of the code. Our algorithms have two main components. The first component builds upon the lattice-based approach of <PERSON><PERSON><PERSON><PERSON> (IEEE Trans. Inf. Theory 2005), who designed a $\\tilde{O}(n)$ time list-decoding algorithm for Reed-Solomon codes approaching the Johnson radius. As part of the second component, we design $\\tilde{O}(n)$ time algorithms for two natural algebraic problems: given a $(m+2)$ -variate polynomial $Q(x, y_{0}, \\ldots, y_{m})=\\tilde{Q}(x)+\\sum\\nolimits_{i=0}^{m} Q_{i}(x) \\cdot y_{i}$ the first algorithm solves order-m linear differential equations of the form $Q\\left(x, f(x), \\frac{d f}{d x}, \\ldots, \\frac{d^{m} f}{d x^{m}}\\right) \\equiv 0$ while the second solves functional equations of the form $Q(x, f(x), f(\\gamma x), \\ldots, f(\\gamma^{m}x))\\equiv 0$, where $m$ is an arbitrary constant and $\\gamma$ is a field element of sufficiently high order. These algorithms can be viewed as generalizations of classical $\\tilde{O}(n)$ time algorithms of Sieveking (Computing 1972) and Kung (Numer. Math. 1974) for computing the modular inverse of a power series, and might be of independent interest.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00028"}, {"primary_key": "587207", "vector": [], "sparse_vector": [], "title": "On Robustness to k-Wise Independence of Optimal Bayesian Mechanisms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper reexamines the classic problem of revenue maximization in single-item auctions with $n$ buyers under the lens of the robust optimization framework. The celebrated <PERSON><PERSON>'s mechanism is the format that maximizes the seller's revenue under the prior distribution, which is mutually independent across all $n$ buyers. As argued in a recent line of work (<PERSON><PERSON><PERSON><PERSON> et al. 22), (<PERSON><PERSON><PERSON> et al. 24), mutual independence is a strong assumption that is extremely hard to verify statistically, thus it is important to relax the assumption. While optimal under mutual independent prior, we find that <PERSON><PERSON>'s mechanism may lose almost all of its revenue when the independence assumption is relaxed to pairwise independence, i.e., <PERSON><PERSON>'s mechanism is not pairwise-robust. The mechanism regains robustness when the prior is assumed to be 3-wise independent. In contrast, we show that second-price auctions with anonymous reserve, including optimal auctions under i.i.d. priors, lose at most a constant fraction of their revenues on any regular pairwise independent prior. Our findings draw a comprehensive picture of robustness to $k$-wise independence in single-item auction settings.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00084"}, {"primary_key": "587208", "vector": [], "sparse_vector": [], "title": "A Strong Separation for Adversarially Robust ℓ0 Estimation for Linear Sketches.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Huacheng Yu", "<PERSON>"], "summary": "The majority of streaming problems are defined and analyzed in a static setting, where the data stream is any worst-case sequence of insertions and deletions which is fixed in advance. However, many real-world applications require a more flexible model, where an adaptive adversary may select future stream elements after observing the previous outputs of the algorithm. Over the last few years, there has been increased interest in proving lower bounds for natural problems in the adaptive streaming model. In this work, we give the first known adaptive attack against linear sketches for the well-studied $\\ell_{0}$-estimation problem over turnstile, integer streams. For any linear streaming algorithm $\\mathcal{A}$ which uses sketching matrix $\\mathbf{A}\\varepsilon \\mathbb{Z}^{r\\times n}$, this attack makes $\\tilde{\\mathcal{O}}(r^{8})$ queries and succeeds with high constant probability in breaking the sketch. Additionally, we give an adaptive attack against linear sketches for the $\\ell_{0}$-estimation problem over finite fields $\\mathbb{F}_{p}$, which requires a smaller number of $\\tilde{\\mathcal{O}}(r^{3})$ queries. Finally, we provide an adaptive attack over $\\mathbb{R}^{n}$ against linear sketches A $\\in \\mathbb{R}^{r\\times \\mathfrak{n}}$ for $\\ell_{0}$-estimation, in the setting where A has all nonzero subdeterminants at least $\\frac{1}{\\text{poly}(r)}$. Our results provide an exponential improvement over the previous number of queries known to break an $\\ell_{0}$-estimation sketch.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00136"}, {"primary_key": "587209", "vector": [], "sparse_vector": [], "title": "Optimal Quantile Estimation: Beyond the Comparison Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Estimating quantiles is one of the foundational problems of data sketching. Given $n$ elements $x_{1},x_{2}, \\ldots, x_{n}$ from some universe of size $U$ arriving in a data stream, a quantile sketch estimates the rank of any element with additive error at most $\\varepsilon n$. A low-space algorithm solving this task has applications in database systems, network measurement, load balancing, and many other practical scenarios. Current quantile estimation algorithms described as optimal include the GK sketch (<PERSON> and <PERSON> 2001) using $O(\\varepsilon^{-1}\\log n)$ words (deterministic) and the KLL sketch (<PERSON><PERSON><PERSON>, <PERSON>, and Liberty 2016) using $O (>\\varepsilon$ log log $(1/\\delta)$) words (ran-domized, with failure probability $\\delta$). However, both algorithms are only optimal in the comparison-based model, whereas many typical applications involve streams of integers that the sketch can use aside from making comparisons. If we go beyond the comparison-based model, the deterministic q-digest sketch (Shrivastava, Buragohai<PERSON>, Agrawal, and <PERSON><PERSON> 2004) achieves a space complexity of $O(\\varepsilon^{-1}\\log U)$ words, which is incomparable to the previously-mentioned sketches. It has long been asked whether there is a quantile sketch using $O(\\epsilon^{-1})$ words of space (which is optimal as long as $n\\leq$ poly $(U)$). In this work, we present a deterministic algorithm using $O(\\varepsilon^{-1})$ words, resolving this line of work.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00075"}, {"primary_key": "587210", "vector": [], "sparse_vector": [], "title": "Certifying Euclidean Sections and Finding Planted Sparse Vectors Beyond the √n Dimension Threshold.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the task of certifying that a random d-dimensional subspace X in $\\mathbb{R}^{\\gamma}$ is well-spread - every vec-tor $\\chi\\in X$ satisfies $c\\sqrt{n}\\Vert x\\Vert_{2}\\leq\\Vert x\\Vert_{1}\\leq\\sqrt{n}^{-}\\Vert x\\Vert_{2}$. In a seminal work, <PERSON><PERSON> et. al. [3] showed a polynomial-time certification algorithm when $d\\leqslant O(\\sqrt{n})$. On the other hand, when $d \\gg \\sqrt{n} r$ the certification task is information-theoretically possible but there is evidence that it is computationally hard [10], [39], a phenomenon known as the information-computation gap. In this paper, we give sub exponential-time certification algorithms in the $d \\ll \\sqrt{n}$ regime. Our algorithm runs in time $\\exp(\\tilde{O}(n^{\\varepsilon}))$ when $\\dot{d} \\leqslant \\widetilde{O}\\left(n^{\\frac{1+\\varepsilon}{2}}\\right)$, establishing a smooth trade-off between runtime and the dimension. Our techniques naturally extend to the related planted problem, where the task is to recover a sparse vector planted in a random subspace. Our algorithm achieves the same runtime and dimension trade-off for this task.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00062"}, {"primary_key": "587211", "vector": [], "sparse_vector": [], "title": "Efficient Approximate Unitary Designs from Random Pauli Rotations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct random walks on simple Lie groups that quickly converge to the Haar measure for all moments up to order $t$. Specifically, a step of the walk on the unitary or orthogonal group of dimension $2^{\\mathrm{n}}$ is a random Pauli rotation $e^{\\mathrm{i}\\theta P/2}$. The spectral gap of this random walk is shown to be $\\Omega(1/t)$, which coincides with the best previously known bound for a random walk on the permutation group on $\\{0,1\\}^{\\mathrm{n}}$. This implies that the walk gives an $\\varepsilon$ -approximate unitary t-design in depth $\\mathcal{O}(\\mathrm{n}t^{2}+t\\log\\frac{1}{\\varepsilon})d$ where $d=\\mathrm{O}(\\log \\mathrm{n})$ is the circuit depth to implement $e^{\\mathrm{i}\\theta P/2}$. Our simple proof uses quadratic Casimir operators of Lie algebras.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00036"}, {"primary_key": "587212", "vector": [], "sparse_vector": [], "title": "Universal Optimality of Dijkstra Via Beyond-Worst-Case Heaps.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proves that <PERSON><PERSON><PERSON>'s shortest-path algorithm is universally optimal in both its running time and number of comparisons when combined with a sufficiently efficient heap data structure. Universal optimality is a powerful beyond-worst-case performance guarantee for graph algorithms that informally states that a single algorithm performs as well as possible for every single graph topology. We give the first application of this notion to any sequential algorithm. We design a new heap data structure with a working-set property guaranteeing that the heap takes advantage of locality in heap operations. Our heap matches the optimal (worst-case) bounds of <PERSON><PERSON><PERSON><PERSON> heaps but also provides the beyond-worst-case guarantee that the cost of extracting the minimum element is merely logarithmic in the number of elements inserted after it instead of logarithmic in the number of all elements in the heap. This makes the extraction of recently added elements cheaper. We prove that our working-set property guarantees universal optimality for the problem of ordering vertices by their distance from the source vertex: The sequence of heap operations generated by any run of <PERSON><PERSON><PERSON>'s algorithm on a fixed graph possesses enough locality that one can couple the number of comparisons performed by any heap with our working-set bound to the minimum number of comparisons required to solve the distance ordering problem on this graph for a worst-case choice of arc lengths.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00125"}, {"primary_key": "587213", "vector": [], "sparse_vector": [], "title": "New Structures and Algorithms for Length-Constrained Expander Decompositions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Expander decompositions form the basis of one of the most flexible paradigms for close-to-linear-time graph algorithms. Length-constrained expander de-compositions generalize this paradigm to better work for problems with lengths, distances and costs. Roughly, an $(h,s)$-length $\\phi$-expander decomposition is a small collection of length increases to a graph so that nodes within distance $h$ can route flow over paths of length $hs$ with congestion at most $1/\\phi$. In this work, we give a close-to-linear time algorithm for computing length-constrained expander decompositions in graphs with general lengths and capacities. Notably, and unlike previous works, our algorithm allows for one to trade off off between the size of the decomposition and the length of routing paths: for any $\\epsilon > 0$ not too small, our algorithm computes in close-to-linear time an $(h, s)$-length $\\phi$-expander decomposition of size $m\\cdot\\phi\\cdot n^{\\epsilon}$ where $s$ = exp(poly $(1/\\epsilon)$). The key foundations of our algorithm are: (1) a simple yet powerful structural theorem which states that the union of a sequence of sparse length-constrained cuts is itself sparse and (2) new algorithms for efficiently computing sparse length-constrained flows.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00102"}, {"primary_key": "587214", "vector": [], "sparse_vector": [], "title": "Dynamic Deterministic Constant-Approximate Distance Oracles with nε Worst-Case Update Time.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "We present a new distance oracle in the fully dynamic setting: given a weighted undirected graph G = (V, E) with $n$ vertices undergoing both edge insertions and deletions, and an arbitrary parameter $\\epsilon\\in[1/\\log^{c}n, 1$ where $c$ > 0 is a small constant, we can deterministically maintain a data structure with $O(n^{\\epsilon})$ worst-case update time that, given any pair of vertices (u, v), returns a $2^{\\text{poly}(1/\\epsilon)}$ -approximate distance between $u$ and $v$ in poly(1/E) log log $n$ query time. Our algorithm significantly advances the state-of-the-art in two aspects, both for fully dynamic algorithms and even decremental algorithms. First, no existing algorithm with worst-case update time guarantees a o($n$)-approximation while also achieving an n2-Ω(1)update and $n^{o(1)}$ query time, while our algorithm offers a constant $O_{\\epsilon}(1)$ -approximation with $O(n^{\\epsilon})$ update time and $o_{\\epsilon}$ (log log n) query time. Second, even if amortized update time is allowed, it is the first deterministic constant-approximation algorithm with $n^{1-\\Omega(1)}$ update and query time. The best result in this direction is the recent deterministic distance oracle by <PERSON><PERSON><PERSON><PERSON> and <PERSON> [STOC 2023] which achieves an approxi- mation of (log log $n)^{2^{O (1 / \\epsilon^3)}}$ with amortized update time of $O(n^{\\epsilon)}$ and query time of $2^{\\mathrm{p}\\circ 1\\mathrm{y}(1/\\epsilon)}\\log n$ log log n. We obtain the result by dynamizing tools related to length- constrained expanders [Haeupler-Racke-Ghaffari, STOC 2022; Haeupler-Hershkowitz-Tan, FOCS 2024]. Our technique com- pletely bypasses the 40-year-old Even-Shiloach tree, which has remained the most pervasive tool in the area but is inherently amortized.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00121"}, {"primary_key": "587215", "vector": [], "sparse_vector": [], "title": "Revisiting Agnostic PAC Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "PAC learning, dating back to <PERSON><PERSON>'84 and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>'64,'74, is a classic model for studying supervised learning. In the agnostic setting, we have access to a hypothesis set H} and a training set of labeled samples drawn i,i d}. from an unknown data distribution D. The goal is to produce a classifier that is competitive with the hypothesis in H} having the least probability of mispredicting the label of a new sample from D. Empirical Risk Minimization (ERM) is a natural learning algorithm, where one simply outputs the hypothesis from H} making the fewest mistakes on the training data. This simple algorithm is known to have an optimal error in terms of the VC-dimension of H} and the number of samples. In this work, we revisit agnostic PAC learning and first show that ERM and any other proper learning algorithm is in fact sub-optimal if we treat the performance of the best hypothesis in H}, as a parameter. We then complement this lower bound with the first learning algorithm achieving an optimal error. Our algorithm introduces several new ideas that we hope may find further applications in learning theory.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00118"}, {"primary_key": "587216", "vector": [], "sparse_vector": [], "title": "An Improved Line-Point Low-Degree Test.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Madhu <PERSON>"], "summary": "We prove that the most natural low-degree test for polynomials over finite fields is “robust” in the high-error regime for linear-sized fields. Specifically we consider the “local” agreement of a function $f:\\mathbb{F}_{q}^{m}\\rightarrow \\mathbb{F}_{q}$ from the space of degree-d polynomials, i.e., the expected agreement of the function from univariate degree-d polynomials over a randomly chosen line in $\\mathbb{F}_{q}^{m}$, and prove that if this local agreement is $\\varepsilon\\geq\\Omega((d/q)^{\\tau}))$ for some fixed $\\tau > 0$, then there is a global degree-d polynomial $Q:\\mathbb{F}_{q}^{m}\\rightarrow \\mathbb{F}_{q}$ with agreement nearly $\\varepsilon$ with $f$. This settles a long-standing open question in the area of low-degree testing, yielding an $O(d)$ -query robust test in the “high-error” regime (i.e., when $\\varepsilon 1/2$ (<PERSON><PERSON>, STOC 1994), or $q=\\Omega(d^{4})$ (Arora & Sudan, Combinatorica 2003), orneeded to measure local distance on 2-dimensional “planes” rather than one-dimensional lines leading to $\\Omega(d^{2})$ -query complexity (<PERSON> & <PERSON>, STOC 1997). Our analysis follows the spirit of most previous analyses in first analyzing the low-variable case $(m=O(1))$ and then “boot-strapping” to general multivariate settings. Our main technical novelty is a new analysis in the bivariate setting that exploits a previously known connection between multivariate factorization and finding (or testing) low-degree polynomials, in a non “black-box” manner. This connection was used roughly in a black-box manner in the work of Arora & Sudan — and we show that opening up this black box and making some delicate choices in the analysis leads to our essentially optimal analysis. A second contribution is a bootstrapping analysis which manages to lift analyses for $m=2$ directly to analyses for general $m$, where previous works needed to work with $m=3$ or $m=4$ — arguably this bootstrapping is significantly simpler than those in prior works.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00113"}, {"primary_key": "587217", "vector": [], "sparse_vector": [], "title": "The Online Submodular Assignment Problem.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Online resource allocation is a rich and var-ied field. One of the most well-known problems in this area is online bipartite matching, introduced in 1990 by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Since then, many variants have been studied, including AdWords, the generalized assignment problem (GAP), and online submodular welfare maximization. In this paper, we introduce a generalization of GAP which we call the submodular assignment problem (SAP). This generalization captures many online assignment problems, including all classical online bipartite matching problems as well as broader online combinatorial optimization problems such as online arboricity, flow scheduling, and laminar restricted allocations. We present a fractional algorithm for online SAP that is $(1-1/e)$-competitive. Additionally, we study several integral special cases of the problem. In particular, we provide a $(1\\ -1/e-\\varepsilon){-}$ competitive integral algorithm under a small-bids assumption, and a $(1\\ -1/e)$-competitive integral algorithm for online submodular welfare maximization where the utility functions are given by rank functions of matroids. The key new ingredient for our results is the construction and structural analysis of a “water level” vector for polymatroids, which allows us to generalize the classic water-filling paradigm used in online matching problems. This construction reveals connections to submodular utility allocation markets and principal partition sequences of matroids.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00026"}, {"primary_key": "587218", "vector": [], "sparse_vector": [], "title": "Cycles of Well-Linked Sets and an Elementary Bound for the Directed Grid Theorem.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In 2015, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proved the directed grid theorem - the generalisation of the well-known excluded grid theorem to directed graphs - confirming a conjecture by <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON> from the mid-nineties. The theorem states the existence of a function $f$ such that every digraph of directed tree-width $f(k)$ contains a cylindrical grid of order $k$ as a butterfly minor, but the given function grows non-elementarily with the size of the grid minor. More precisely, it contains a tower whose height depends on the size of the grid. In this paper, we present an alternative proof of the directed grid theorem which is conceptually much simpler, more modular in its composition and also improves the upper bound for the function $f$ to a power tower of height 22. Our proof is inspired by the breakthrough result of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, who proved a polynomial bound for the excluded grid theorem for undirected graphs. We translate a key concept of their proof to directed graphs by introducing cycles of well-linked sets (CWS), and show that any digraph of high directed tree-width contains a large CWS, which in turn contains a large cylindrical grid, improving the result due to <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> from a non-elementary to an elementary function. An immediate application of our result is that we can improve the bound for <PERSON>'s conjecture-the directed Erdős-Pósa property-proved by <PERSON>, <PERSON>, <PERSON> and <PERSON> [2] from a non-elementary to an elementary function. The same improvement applies to other types of Erdős-Pósa style problems on directed graphs. To the best of our knowledge, this is the first significant improvement on the bound for <PERSON>'s conjecture since it was proved in 1996. Since its publication in STOC 2015, the Directed Grid Theorem has found numerous applications (see for example [3]–[7]), all of which directly benefit from our main result. Finally, we believe that the theoretical tools developed in this work may find applications beyond the directed grid theorem, in a similar way as the path-of-sets-system framework due to Chekuri and Chuzhoy [8] did for undirected graphs (see for example [9]–[11]).", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00011"}, {"primary_key": "587219", "vector": [], "sparse_vector": [], "title": "Interactive Proofs for General Distribution Properties.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Suppose <PERSON> has collected a small number of samples from an unknown distribution, and would like to learn about the distribution. <PERSON>, an untrusted data analyst, claims that he ran a sophisticated data analysis on the distribution, and makes assertions about its properties. Can <PERSON> efficiently verify <PERSON>'s claims using fewer resources (say in terms of samples and computation) than would be needed to run the analysis herself? We construct an interactive proof system for any distribution property for which the distance from the property can be computed by (log-space) uniform polynomial size circuits of depth D, where the circuit gets a complete description of the distribution. Taking N to be an upper bound on the size of the distribution's support, the verifier's sample complexity, the running time, and the communication complexity are all sublinear in N: they are bounded by O(N1-a+D) for a constant a > 0. The honest prover runs in poly(N) time and has quasi-linear sample complexity. Moreover, the proof system is tolerant: it can be used to approximate the distribution's distance from the property. We show similar results for any distribution property for which the distance from the property can be approximated by a bounded-space Turing machine (that gets as input a complete description of the distribution). We remark that even for simple properties, deciding the property without a prover requires quasi-linear sample complexity and running time. Prior work [<PERSON> and <PERSON>, FOCS 2023] demonstrated sublinear interactive proof systems, but only for the much more restricted class of label-invariant distribution properties.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00041"}, {"primary_key": "587220", "vector": [], "sparse_vector": [], "title": "Optimal Coding for Randomized Kolmogorov Complexity and Its Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The coding theorem for Kolmogorov complexity states that any string sampled from a computable distribution has a description length close to its information content. A coding theorem for resource-bounded Kolmogorov complexity is the key to obtaining fundamental results in average-case complexity, yet whether any samplable distribution admits a coding theorem for randomized time-bounded Kolmogorov complexity $(\\text{rK}^{\\text{poly}})$ is open and a common bottleneck in the recent literature of meta-complexity. Previous works bypassed this issue by considering probabilistic Kolmogorov complexity $(\\text{pK}^{\\text{poly}})$, in which public random bits are assumed to be available. In this paper, we present an efficient coding theorem for randomized Kolmogorov complexity under the non-existence of one-way functions, thereby removing the common bottleneck. This enables us to prove $\\text{rK}^{\\text{poly}}$ counterparts of virtually all the average-case results that were proved only for $\\text{pK}^{\\text{poly}}$, and enables the resolution of the following concrete open problems. 1)The existence of a one-way function is characterized by the failure of average-case symmetry of information for randomized time-bounded Kolmogorov complexity, as well as a conditional coding theorem for randomized time-bounded Kolmogorov complexity. This resolves the open problem of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> (STOC'23). 2)<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> (CCC'24) showed that randomized time-bounded Kolmogorov complexity admits search-to-decision reductions in the errorless average-case setting over any samplable distribution, and left open whether a similar result holds in the error-prone setting. We resolve this question affirmatively, and as a consequence, characterize the existence of a one-way function by the average-case hardness of computing $\\text{rK}^{\\text{poly}}$ with respect to an arbitrary samplable distribution, which is an $\\text{rK}^{\\text{poly}}$ analogue of the $\\text{pK}^{\\text{poly}}$ characterization of Liu and Pass (CRYPTO'23). The key technical lemma is that any distribution whose next bits are efficiently predictable admits an efficient encoding and decoding scheme, which could be of independent interest to data compression.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00030"}, {"primary_key": "587221", "vector": [], "sparse_vector": [], "title": "Gradient Descent for Unbounded Convex Functions on Hadamard Manifolds and its Applications to Scaling Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we study asymptotic behaviors of continuous-time and discrete-time gradient flows of a “lower-unbounded” convex function on a Hadamard manifold, particularly, their convergence properties to the boundary at infinity. We establish a duality theorem that the infimum of the gradient-norm of a convex function is equal to the supremum of the negative of the recession function over the boundary, provided that the infimum is positive. Furthermore, the infimum and the supremum are obtained by the limits of the gradient flows of the function. Our results feature convex-optimization ingredients of the moment-weight inequality for reductive group actions by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, and are applied to noncommutative optimization by <PERSON><PERSON><PERSON><PERSON><PERSON> et al. FOCS 2019. We show that the gradient descent of the KempfNess function for an unstable orbit converges to a 1-parameter subgroup in the <PERSON><PERSON>-<PERSON> criterion, and the associated moment-map sequence converges to the minimum-norm point of the moment polytope. We show further refinements for operator scaling-the left-right action on a matrix tuple. We characterize the gradient-flow limit of operator scaling via a vector-space generalization of the classical Dulma<PERSON><PERSON><PERSON><PERSON> decomposition of a bipartite graph.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00139"}, {"primary_key": "587222", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Sorting Under Partial Information.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Sorting is one of the fundamental algorithmic problems in theoretical computer science. It has a natural generalization, introduced by <PERSON><PERSON> in 1976, called sorting under partial information. The input consists of: –a ground set X of size n, –a partial oracle OF (where partial oracle queries for any (xi,xj) output whether xi≺Pxj, for some partial order P), –a linear oracle OL (where linear oracle queries for any (xi,xj) output whether xi<Lxj and the order L extends P) The goal is to recover the linear order L on X using the fewest number of linear oracle queries. In this problem, we measure algorithmic complexity through three metrics: the number of linear oracle queries to OL, the number of partial oracle queries to OP, and the time spent (the number of algorithmic instructions required to identify for which pairs (xi,xj) a partial or linear oracle query is performed). Let e(P) denote the number of linear extensions of P. Any algorithm requires worst-case log2e(P) linear oracle queries to recover the linear order on X. In 1984, <PERSON> and <PERSON> presented the first algorithm that uses Θ(loge(P)) linear oracle queries (using O(n2) partial oracle queries and exponential time). Since then, both the general problem and restricted variants have been consistently studied. The state-of-the-art for the general problem is by <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> who at STOC'10 manage to separate the linear and partial oracle queries into a preprocessing and query phase. They can preprocess P using O(n2) partial oracle queries and O(n2.5) time. Then, given OL, they uncover the linear order on X in Θ(loge(P) linear oracle queries and O(n+loge(P)) time - which is worst-case optimal in the number of linear oracle queries but not in the time spent. We present the first algori...", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00131"}, {"primary_key": "587223", "vector": [], "sparse_vector": [], "title": "Replicability in High Dimensional Statistics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The replicability crisis is a major issue across nearly all areas of empirical science, calling for the formal study of replicability in statistics. Motivated in this context, [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and Sorrell STOC 2022] introduced the notion of replicable learning algorithms, and gave basic procedures for 1-dimensional tasks including statistical queries. In this work, we study the computational and statistical cost of replicability for several fundamental high dimensional statistical tasks, including multi-hypothesis testing and mean estimation. Our main contribution establishes a computational and statistical equivalence between optimal replicable algorithms and high dimensional isoperimetric tilings. As a consequence, we obtain matching sample complexity upper and lower bounds for replicable mean estimation of distributions with bounded covariance, resolving an open problem of [<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, STOC 2023] and for the $N$ -Coin Problem, resolving a problem of [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, NeurIPS 2023] up to log factors. While our equivalence is computational, allowing us to shave $\\log$ factors in sample complexity from the best known efficient algorithms, efficient isoperimetric tilings are not known. To circumvent this, we introduce several relaxed paradigms that do allow for sample and computationally efficient algorithms, including allowing pre-processing, adaptivity, and approximate replicability. In these cases we give efficient algorithms matching or beating the best known sample complexity for mean estimation and the coin problem, including a generic procedure that reduces the standard quadratic overhead of replicability to linear in expectation.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00103"}, {"primary_key": "587224", "vector": [], "sparse_vector": [], "title": "Predict to Minimize Swap Regret for All Payoff-Bounded Tasks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Calibration allows predictions to be reliably in-terpreted as probabilities by decision makers. We propose a decision-theoretic calibration error, the Calibration Decision Loss (CDL), defined as the maximum improvement in decision payoff obtained by calibrating the predictions, where the maximum is over all payoff-bounded decision tasks. Vanishing CDL guarantees the payoff loss from miscalibration vanishes simultaneously for all downstream decision tasks. We show separations between CDL and existing calibration error metrics, including the most well-studied metric Expected Calibration Error (ECE). Our main technical contribution is a new efficient algorithm for online calibration that achieves near-optimal $O\\left(\\frac{\\log T T}{\\sqrt{T}}\\right)$ expected CDL, bypassing the $\\Omega(T^{-0472})$ lower bound for ECE by Qiao and Valiant [40]. The full version of the paper is titled Calibration Error for Decision Making. We strongly recommend that our readers read the full arXiv version (https://arxiv.org/abs/2404.13503).", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00024"}, {"primary_key": "587225", "vector": [], "sparse_vector": [], "title": "Capacity Threshold for the Ising Perceptron.", "authors": ["<PERSON><PERSON>"], "summary": "We show that the capacity of the Ising perceptron is with high probability upper bounded by the constant $\\alpha_{\\star}\\approx 0.833$ conjectured by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, under the condition that an explicit two-variable function $\\mathscr{S}_{\\star}\\left(\\lambda_1, \\lambda_2\\right)$ is maximized at (1,0). The earlier work of <PERSON><PERSON> and Sun [1] proves the matching lower bound subject to a similar numerical condition, and together these results give a conditional proof of the conjecture of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>,", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00074"}, {"primary_key": "587226", "vector": [], "sparse_vector": [], "title": "Certifying Almost All Quantum States with Few Single-Qubit Measurements.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A fundamental challenge in quantum information science is certifying that an n-qubit state $\\rho$ prepared in the lab closely matches a target state $\\vert \\psi\\rangle$. Previous approaches to this problem often require deep quantum circuits, exponentially many single-qubit measurements, or are limited to specific state families. In this work, we introduce a new method that leverages a connection between state certification and the mixing time of a random walk, allowing almost all n-qubit target states, including those with exponential circuit complexity, to be certified with only $\\mathrm{O}(n^{2})$ single-qubit measurements. Our protocol is broadly compatible with various experimental platforms and has applications in benchmarking quantum systems, optimizing quantum circuits, and efficiently learning and verifying representations of quantum states—such as neural networks and tensor networks—using only single-qubit measurements. Moreover, these verified representations enable the efficient prediction of highly non-local properties of $\\rho$ that would otherwise require an exponential number of measurements.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00079"}, {"primary_key": "587227", "vector": [], "sparse_vector": [], "title": "Three-Edge-Coloring Projective Planar Cubic Graphs: A Generalization of the Four Color Theorem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that every cyclically 4-edge-connected cubic graph that can be embedded in the projective plane, with the single exception of the Petersen graph, is 3-edge-colorable. In other words, the only (nontrivial) snark that can be embedded in the projective plane is the Petersen graph. This implies that a 2-connected cubic (multi)graph that can be embedded in the projective plane is not 3-edge-colorable if and only if it can be obtained from the Petersen graph by replacing each vertex by a 2-edge-connected planar cubic (multi)graph. Here, a replacement of a vertex $v$ in a cubic graph $G$ is the operation that takes a 2-connected planar (cubic) multigraph $H$ containing some vertex $u$ of degree 3, unifying $G-v$ and $H-u$, and connecting the vertices in $N_{G}[v]$ in $G-v$ with the three neighbors of $u$ in $H-u$ with 3 edges. Any graph obtained in such a way is said to be Petersen-like. This result is a nontrivial generalization of the Four Color Theorem, and its proof requires a combination of extensive computer verification and computer-free extension of existing proofs on colorability. Using this result, we obtain the following algorithmic consequence. Input: A cubic graph $G$. Output: Either a 3-edge-coloring of $G$, an obstruction showing that $G$ is not 3-edge-colorable, or the conclusion that $G$ cannot be embedded in the projective plane (certified by exposing a forbidden minor for the projective plane contained in $G$). Time complexity: $O(n^{2})$, where $n=\\vert V(G)\\vert$. An unexpected consequence of this result is a coloring-flow duality statement for the projective plane: A cubic graph embedded in the projective plane is 3-edge-colorable if and only if its dual multigraph is 5-vertex-colorable. Moreover, we show that a 2-edge connected graph embedded in the projective plane admits a nowhere-zero 4-flow unless it is Petersen-like (in which case it does not admit nowhere-zero 4-flows). This proves a strengthening of the Tutte 4-flow conjecture for graphs on the projective plane. Some of our proofs require extensive computer verification. The necessary source codes, together with the input and output files and the complete set of more than 5000 reducible configurations, are available on Github11https://github.com/edge-coloring. Refer to the “README.md” file in each directory for instructions on how to run each program. which can be considered as an addendum to this paper. Moreover, we provide pseudocodes for all our computer verifications.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00016"}, {"primary_key": "587228", "vector": [], "sparse_vector": [], "title": "Faster Isomorphism Testing of p-Groups of Frattini Class 2.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The finite group isomorphism problem asks to decide whether two finite groups of order $N$ are isomorphic. Improving the classical $N^{O(\\mathrm{I}\\mathrm{o}\\mathrm{g}N)}$ -time algorithm for group isomorphism is a long-standing open problem. It is generally regarded that $p$ groups of class 2 and exponent $p$ form a bottleneck case for group isomorphism in general. The recent breakthrough by <PERSON> (STOC '23) presents an $N^{O\\left((\\log N)^{5 / 6}\\right)}$ -time algorithm for this group class. In this paper, we improve <PERSON>'s algorithm by presenting an $N^{{\\tilde{O}}\\left((\\log {N})^{1^{1 / 2}}\\right)}$ -time algorithm for this group class. We also extend our result to the more general $p$ -groups of Frattini class 2. Our algorithm is obtained by sharpening the key technical ingredients in <PERSON>'s algorithm and building connections with other research topics. One intriguing connection is with the maximal and non-commutative ranks of matrix spaces, which have recently received considerable attention in algebraic complexity and computational invariant theory. Results from the theory of Tensor Isomorphism complexity class (<PERSON><PERSON><PERSON><PERSON><PERSON>, SIAM J. Comput. '23) are utilized to simplify the algorithm and achieve the extension to $p$ -groups of Frattini class 2.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00090"}, {"primary_key": "587229", "vector": [], "sparse_vector": [], "title": "An XOR Lemma for Deterministic Communication Complexity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove a lower bound on the communication complexity of computing the $n$ -fold xor of an arbitrary function $f$, in terms of the communication complexity and rank of $f$. We prove that $D(f^{\\oplus n}) \\geq n\\cdot(\\frac{\\Omega(D(f))}{\\log \\mathrm{r}\\mathrm{k}(t)}-\\log \\text{rk}(f))$, where here $D(f), D(f^{\\oplus n})$ represent the deterministic communication complexity, and $\\text{rk}(f)$ is the rank of $f$. Our methods involve a new way to use information theory to reason about deterministic communication complexity.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00034"}, {"primary_key": "587230", "vector": [], "sparse_vector": [], "title": "Sampling, Counting, and Large Deviations for Triangle-Free Graphs Near the Critical Density.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the following combinatorial counting and sampling problems: can we sample from the Erdős-Rényi random graph $G(n,p)$ conditioned on triangle-freeness? Can we approximate (either algorithmically or with a formula) the probability that $G(n,p)$ is triangle-free? These are prototypical instances of forbidden substructure problems ubiquitous in combinatorics. The algorithmic questions are instances of approximate sampling and counting for a hypergraph hard-core model. Estimating the probability that $G(n,p)$ has no triangles is a fundamental question in probabilistic combinatorics and one that has led to the development of many important tools in the field. Through the work of several authors, the asymnpotics of the logarithm of this probability are known if $p=o(n^{-1/2})$ or if $p=\\omega(n^{-1/2})$. The regime $p=\\Theta(n^{-1/2})$ is more mysterious, as this range witnesses a dramatic change in the the typical structural properties of $G(n,p)$ conditioned on triangle-freeness. As we show, this change in structure has a profound impact on the performance of sampling algorithms. We give two different efficient sampling algorithms for this problem (and complementary approximate counting algorithms), one that is efficient when $p C/\\sqrt{n}$ for constants $c, C > 0$. The latter algorithm involves a new approach for dealing with large defects in the setting of sampling from low-temperature spin models. Our algorithmic results can be used to give an asymptotic formula for the logarithm of the probability $G(n,p)$ is triangle-free when $p < c/\\sqrt{n}$. This algorithmic approach to large deviation problems in random graphs is very different than the known approaches in the suBCRitical regime $p=o(n^{-1/2})$ (based on the Poisson paradigm) and in the supercritical regime $p=\\omega(n^{-1/2})$ (based on regularity lemmas or hypergraph containers); in fact, to the best of our knowledge, no asymptotic formula for the log probability in the regime $p=\\Theta(n^{-1/2})$ was even conjectured previously.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00020"}, {"primary_key": "587231", "vector": [], "sparse_vector": [], "title": "Benchmark-Tight Approximation Ratio of Simple Mechanism for a Unit-Demand Buyer.", "authors": ["Yaonan Jin", "<PERSON><PERSON><PERSON>"], "summary": "We study revenue maximization in the unit-demand single-buyer setting. Our main result is that Uniform-Ironed-Virtual-Value Item Pricing guarantees a tight 3-approximation to the Duality Relaxation Benchmark [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EC’10/GEB’15; <PERSON><PERSON><PERSON><PERSON>, STOC’16/ SICOMP’21], breaking the barrier of 4 since [<PERSON><PERSON><PERSON><PERSON>, STOC’10; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EC’10/GEB’15]. To our knowledge, this is the first benchmark-tight revenue guarantee of any simple multi-item mechanism. Technically, all previous works employ Myerson Auction as an intermediary. The barrier of 4 follows as Uniform-Ironed-Virtual-Value Item Pricing achieves a tight 2-approximation to <PERSON>on Auction, which then achieves a tight 2-approximation to Duality Relaxation Benchmark. Instead, our new approach avoids Myerson Auction, thus enabling the improvement. Central to our work are a benchmark-based 3-competitive prophet inequality and its fully constructive proof. Such variant prophet inequalities shall find future applications, e.g., to Multi-Item Mechanism Design where optimal revenues are relaxed to various more accessible benchmarks. We complement our benchmark-tight ratio with an impossibility result. All previous works and ours follow the single-dimensional representative approach introduced by [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EC'07]. Against Duality Relaxation Benchmark, it turns out that this approach cannot beat our bound of 3 for a large class of Item Pricing's.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00082"}, {"primary_key": "587232", "vector": [], "sparse_vector": [], "title": "A Dense Model Theorem for the Boolean Slice.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The (low soundness) linearity testing problem for the middle slice of the Boolean cube is as follows. Let $\\varepsilon > 0$ and $f$ be a function on the middle slice on the Boolean cube, such that when choosing a uniformly random quadruple $(x,y,\\ z,x\\oplus y\\oplus z)$ of vectors of $2n$ bits with exactly $n$ ones, the probability that $f(x\\oplus y\\oplus z)=f(x)\\oplus f(y)\\oplus f(z)$ is at least $1/2+\\epsilon$. The linearity testing problem, posed by [6], asks whether there must be an actual linear function that agrees with $f$ on $1/2+\\epsilon^{\\prime}$ fraction of the inputs, where $\\varepsilon^{\\prime}=\\in^{\\prime}(\\in) > 0$. We solve this problem, showing that $f$ must indeed be correlated with a linear function. To do so, we prove a dense model theorem for the middle slice of the Boolean hypercube for Gowers uniformity norms. Specifically, we show that for every $k\\in \\mathbb{N}$, the normalized indicator function of the middle slice of the Boolean hypercube $\\{0,1\\}^{2n}$ is close in Gowers norm to the normalized indicator function of the union of all slices with weight $t=n(\\text{mod}\\ 2^{k-1})$. Using our techniques we also give a more general ‘low degree test’ and a biased rank theorem for the slice.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00056"}, {"primary_key": "587233", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (LZ77) Factorization in Sublinear Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Lempel-Ziv (LZ77) factorization is a fundamental problem in string processing: Greedily partition a given string $T$ from left to right into blocks (called phrases) so that each phrase is either the leftmost occurrence of a single letter or the longest prefix of the unprocessed suffix that has another occurrence earlier in the text. This simple routine has numerous applications. Most importantly, the LZ77 factorization is the central component and the computational bottleneck of most existing compression algorithms (utilized in formats like zip, pdf, and png). LZ77 is also a widely used algorithmic tool for the detection of repetitions and periodicities in strings, and the centerpiece of many powerful compressed indexes that enable computation directly over compressed data. LZ77 factorization is one of the most studied problems in string processing. In the 47 years since its inception, numerous efficient algorithms were developed for different models of computation, including parallel, GPU, external-memory, and quantum. Remarkably, however, the complexity of the most basic problem is still not settled: All existing algorithms in the RAM model run in $\\Omega(n)$ time, which is a $\\Theta(\\log n)$ factor away from the lower bound of $\\Omega(n/\\log n)$ (following simply from the necessity to read the entire input, which takes $\\Theta(n/\\log n)$ space for any $T\\in\\{0,1\\}^{n})$. Sublinear-time algorithms are known for nearly all other fundamental problems on strings, but LZ77 seems resistant to all currently known techniques. We present the first $o(n)$ -time algorithm for constructing the LZ77 factorization, breaking the linear-time barrier present for nearly 50 years. More precisely, we show that, in the standard RAM model, it is possible to compute the LZ77 factorization of a given length-$n$ string $T\\in \\{0,1\\}^{n}$ in $\\mathcal{O}(n/\\sqrt{\\log n})=o(n)$ time and using the optimal $O(n/\\log n)$ working space. Our algorithm generalizes to larger alphabets $\\Sigma=[0.. \\sigma),\\text{ where }\\sigma=n^{\\mathcal{O}2(1)}$. The runtime and working space then become $\\mathcal{O}((n\\log\\sigma)/\\sqrt{\\log n})$ and $\\mathcal{O}(n/\\log_{\\sigma}n)$, respectively. To achieve this sublinear-time LZ77 algorithm, we prove a more general result: We show that, for any constant $\\epsilon\\in(0,1)$ and string $T\\in[0..\\sigma)^{n}$, in $\\mathcal{O}((n\\log\\sigma)/\\sqrt{\\log n})$ time and using $\\mathcal{O}(n/\\log_{\\sigma}n)$ working space, we can construct an index of optimal size $\\mathcal{O}(n/\\log_{\\sigma}n)$ that, given any substring $P=T[j.. j+\\ell)$ specified with a pair $(j,\\ell)$, computes the leftmost occurrence of $P$ in $T$ in $O(\\log^{\\epsilon}n)$ time. In other words, we solve the indexing/online variant of the LZ77 problem, where we can efficiently query the phrase length starting at any position. Our solution is based on a new type of queries that we call prefix range minimum queries or prefix RMQ. After developing an efficient solution for these queries, we provide a general reduction showing that any new tradeoff for the prefix RMQ implies a new tradeoff for an index finding leftmost occurrences (and hence a new LZ77 factorization algorithm).", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00122"}, {"primary_key": "587234", "vector": [], "sparse_vector": [], "title": "Jump Operators, Interactive Proofs and Proof Complexity Generators.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "A jump operator $J$ in proof complexity is a function such that for any proof system $P, J(P)$ is a proof system that $P$ cannot simulate. Some candidate jump operators were proposed by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [63] and <PERSON><PERSON><PERSON><PERSON> [57], but it is an open problem whether computable jump operators exist or not. In this regard, we introduce a new candidate jump operator based on the power of interactive proofs which given a proof system $P$, [I P, $P]$ (I P-randomized implicit proof system based on $P)$ is an M A proof system. In the first step, we investigate the relationship between IP - randomized implicit proof systems and Cook-Reckhow proof systems. In particular, we show that if $i$ EF (<PERSON><PERSON><PERSON><PERSON>'s implicit Extended Frege) proves exponential hard on average circuit lower bounds efficiently, then $i$ E F simulates [IP, EF]. Moreover, we show that IP-randomized implicit proof systems can be used to prove new connections between different well-studied concepts in complexity theory. Namely, we prove new results about the hardness magnification in proof complexity, the hardness of proving proof complexity lower bounds, the automatability and the feasible disjunction property for Extended Frege using IP - randomized implicit proof systems. One ingredient of our proofs is a formalization of the sum-check protocol [65] in $\\mathrm{s}_{2}^{1}$ which might be of independent interest. We also look at the general theory of jump operators and consider an old conjecture by <PERSON><PERSON><PERSON> [78] about finite consistency sentences for first-order theories of arithmetic. In this direction, we prove that certain statements are equivalent, in particular, we prove that the widely believed assumption about the existence of computable jump operators in proof complexity is equivalent to a weaker form of Pudlak's conjecture.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00044"}, {"primary_key": "587235", "vector": [], "sparse_vector": [], "title": "Near-Optimal Size Linear Sketches for Hypergraph Cut Sparsifiers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Madhu <PERSON>"], "summary": "A $(1\\pm\\epsilon)$ -sparsifier of a hypergraph $G(V, E)$ is a (weighted) subgraph that preserves the value of every cut to within a $(1\\pm\\epsilon)$ -factor. It is known that every hypergraph with $n$ vertices admits a $(1 \\pm \\epsilon)$ -sparsifier with $\\tilde{O}(n/{\\epsilon}^{2})$ hyperedges. In this work, we explore the task of building such a sparsifier by using only linear measurements (a linear sketch) over the hyperedges of $G$, and provide nearly-matching upper and lower bounds for this task. Specifically, we show that there is a randomized linear sketch of size $\\tilde{O}(nr\\log(m)/\\epsilon^{2})$ bits which with high probability contains sufficient information to recover a $(1\\pm\\epsilon)$ cut-sparsifier with $\\tilde{O}(n/\\epsilon^{2})$ hyperedges for any hypergraph with at most $m$ edges each of which has arity bounded by $r$. This immediately gives a dynamic streaming algorithm for hypergraph cut sparsification with an identical space complexity, improving on the previous best known bound of $\\tilde{O}(nr^{2}\\log^{4}({m})/\\epsilon^{2})$ bits of space (<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON> 2015). We complement our algorithmic result above with a nearly-matching lower bound. We show that for every $\\epsilon\\in(0,1)$, one needs $\\Omega(nr\\log(m/n)/\\log(n))$ bits to construct a $(1\\pm\\epsilon)$ -sparsifier via linear sketching, thus showing that our linear sketch achieves an optimal dependence on both $r$ and $\\log(m)$. The starting point for our improved algorithm is importance sampling of hyperedges based on the new notion of $k$ -cut strength introduced in the recent work of Quanrud (SODA 2024). The natural algorithm based on this concept leads to $\\log m$ levels of sampling where errors can potentially accumulate, and this accounts for the polylog $(m)$ losses in the sketch size of the natural algorithm. We develop a more intricate analysis of the accumulation in error to show most levels do not contribute to the error and actual loss is only polylog $(n)$. Combining with careful preprocessing (and analysis) this enables us to get rid of all extraneous $\\log m$ factors in the sketch size, but the quadratic dependence on $r$ remains. This dependence originates from use of correlated $\\ell_{0}$ -samplers to recover a large number of low-strength edges in a hypergraph simultaneously by looking at neighborhoods of individual vertices. In graphs, this leads to discovery of $\\Omega(n)$ edges in a single shot, whereas in hypergraphs, this may potentially only reveal $O$($n$/$r$) new edges, thus requiring $\\Omega(r)$ rounds of recovery. To remedy this we introduce a new technique of random fingerprinting of hyperedges which effectively eliminates the correlations created by large arity hyperedges, and leads to a scheme for recovering hyperedges of low strength with an optimal dependence on $r$. Putting all these ingredients together yields our linear sketching algorithm. Our lower bound is established by a reduction from the universal relation problem in the one-way communication setting.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00105"}, {"primary_key": "587236", "vector": [], "sparse_vector": [], "title": "Gapped Clique Homology on Weighted Graphs is QMA1-Hard and Contained in QMA.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study the complexity of a classic problem in computational topology, the homology problem: given a description of some space $X$ and an integer $k$, decide if $X$ contains a k-dimensional hole. The setting and statement of the homology problem are completely classical, yet we find that the complexity is characterized by quantum complexity classes. Our result can be seen as an aspect of a connection between homology and supersymmetric quantum mechanics [1]. We consider clique complexes, motivated by the practical application of topological data analysis (TDA). The clique complex of a graph is the simplicial complex formed by declaring every $k+1$ -clique in the graph to be a k-simplex. Our main result is that deciding whether the clique complex of a weighted graph has a hole or not, given a suitable promise on the gap, is QMA1-hard and contained in QMA. Our main innovation is a technique to lower bound the eigenvalues of the combinatorial Laplacian operator. For this, we invoke a tool from algebraic topology known as spectral sequences. In particular, we exploit a connection between spectral sequences and Hodge theory [2]. Spectral sequences will play a role analogous to perturbation theory for combinatorial Lapla-cians. In addition, we develop the simplicial surgery technique used in prior work [3]. Our result provides some suggestion that the quantum TDA algorithm [4] cannot be dequantized. More broadly, we hope that our results will open up new possibilities for quantum advantage in topological data analysis.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00039"}, {"primary_key": "587237", "vector": [], "sparse_vector": [], "title": "Power Series Composition in Near-Linear Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an algebraic algorithm that computes the composition of two power series in softly linear time complexity. The previous best algorithms are $\\mathrm{O}(n^{1+o(1)})$ non-alzebraic algorithm by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (FOCS 2008) and an $\\mathrm{O}(n^{1.43})$ algebraic algorithm by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON> (JACM 2023). Our algorithm builds upon the recent Graeffe iteration approach to manipulate rational power series introduced by <PERSON><PERSON> and <PERSON><PERSON> (SOSA 2021).", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00127"}, {"primary_key": "587238", "vector": [], "sparse_vector": [], "title": "Efficient Approximation of Fractional Hypertree Width.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give two new approximation algorithms to compute the fractional hypertree width of an input hypergraph. The first algorithm takes as input n-vertex m-edge hypergraph $H$ of fractional hypertree width at most $\\omega$, runs in polynomial time and produces a tree decomposition of $H$ of fractional hypertree width $\\mathcal{O}(\\omega\\log n\\log\\omega)$, i.e., it is an $\\mathcal{O}(\\log n\\log\\omega)$-approximation algorithm. As an immediate corollary this yields poly-nomial time $\\mathcal{O}(\\log^{2}n\\log\\omega)$-approximation algorithms for (generalized) hypertree width as well. To the best of our knowledge our algorithm is the first non-trivial polynomial-time approximation algorithm for fractional hypertree width and (generalized) hypertree width, as opposed to algorithms that run in polynomial time only when $\\omega$ is considered a constant. For hypergraphs where every pair of hyperedges have at most $\\eta$ vertices in common, the al-gorithm outputs a hypertree decomposition with fractional hypertree width $\\mathcal{O}(\\eta\\omega^{2}\\log\\omega)$ and generalized hypertree width $\\mathcal{O}(\\eta\\omega^{2}\\log\\omega(\\log\\eta+\\text{log}\\omega))$. This ratio is comparable with the recent algorithm of <PERSON><PERSON><PERSON> and Razgon [STACS 2024], which produces a hypertree decomposition with generalized hypertree width ${\\mathcal{O}}(\\omega^{2}(\\omega+\\eta))$, but uses time (at least) exponential in $\\eta$ and $\\omega$. The second algorithm runs in time $n^{\\omega}m^{\\mathcal{O}(1)}$ and pro-duces a tree decomposition of $H$ of fractional hypertree width $\\mathcal{O}(\\omega{\\mathrm{l}}\\text{og}^{2}\\omega)$. This significantly improves over the $(n+m)^{\\mathcal{O}(\\omega^{3})}$ time algorithm of Marx [ACM TALG 2010], which produces a tree decomposition of fractional hyper-tree width $\\mathcal{O}(\\omega^{3})$, both in terms of running time and the approximation ratio. Our main technical contribution, and the key insight behind both algorithms, is a variant of the classic Menger's Theorem for clique separators in graphs: For every graph $G$, vertex sets $A$ and $B$, family $\\mathcal{F}$ of cliques in $G$, and positive rational $f$, either there exists a sub-family of $\\mathcal{O}(f \\cdot {\\mathrm{l}}\\text{og}^{2}n)$ cliques in $\\mathcal{F}$ whose union separates $A$ from $B$, or there exist $f\\cdot\\log\\vert \\mathcal{F}\\vert$ paths from $A$ to $B$ such that no clique in $\\mathcal{F}$ intersects more than $\\log\\vert \\mathcal{F}\\vert$ paths.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00053"}, {"primary_key": "587239", "vector": [], "sparse_vector": [], "title": "Minor Containment and Disjoint Paths in Almost-Linear Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give an algorithm that, given graphs G and H, tests whether H is a minor of G in time OH(n1+o(1)); here, n is the number of vertices of G and the OH(·)-notation hides factors that depend on H and are computable. By the Graph Minor Theorem, this implies the existence of an n1+o(1) -time membership test for every minor-closed class of graphs. More generally, we give an OH,|X| (m1+o(1))-time algorithm for the rooted version of the problem, in which G comes with a set of roots X⊆V(G) and some of the branch sets of the sought minor model of H are required to contain prescribed subsets of X; here, m is the total number of vertices and edges of G. This captures the Disjoint Pathsproblem, for which we obtain an Ok(m1+o(1)/}-time algorithm, where k is the number of terminal pairs. For all the mentioned problems, the fastest algorithms known before are due to <PERSON>, <PERSON>, and <PERSON> [JCTB 2012], and have a time complexity that is quadratic in the number of vertices of G. Our algorithm has two main ingredients: First, we show that by using the dynamic treewidth data structure of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [FOCS 2023], the irrelevant vertex technique of <PERSON> and <PERSON> can be implemented in almost-linear time on apex-minor-free graphs. Then, we apply the recent advances in almost-linear time flow/cut algorithms to give an almost-linear time implementation of the recursive understanding technique, which effectively reduces the problem to apex-minor-free graphs.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00014"}, {"primary_key": "587240", "vector": [], "sparse_vector": [], "title": "Strong vs. Weak Range Avoidance and the Linear Ordering Principle.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In a pair of recent breakthroughs [1], [2] it was shown that the classes $\\mathrm{S}_{2}^{\\mathrm{E}}, \\mathsf{ZPE}^{\\mathsf{NP}}$ and $\\Sigma_{2}^{\\mathrm{E}}$ require exponential circuit complexity, giving the first unconditional improvements to a classical result of <PERSON><PERSON>n [3]. These results were obtained by designing a surprising new algorithm for the total search problem Range Avoidance: given a circuit $C:\\{0,1\\}^{n}\\rightarrow\\{0,1\\}^{n+1}$, find an $n+1$ -bit strina outside its range. Range Avoidance is a member of the class Tf $\\Sigma_{2}^{\\dot{\\mathrm{F}}}$ of total search problems in the second level of the polynomial hierarchy, analogous to its better-known counterpart TFNP in the first level. TF $\\Sigma_{2}^{\\overline{\\mathrm{F}}}$ was only recently introduced in [4] and its structure is not well understood. We investigate here the extent to which algorithms of the kind in [1], [2] can be applied to other search problems in this class, and prove a variety of results both positive and negative. On the positive side we show that <PERSON>'s Range Avoidance algorithm [2] can be improved to give a reduction from Range Avoidance to a natural total search problem we call the Linear Ordering Principle or “LOP”: given a circuit $\\prec:\\{0,1\\}^{n}\\times\\{0,1\\}^{n}\\rightarrow\\{0,1\\}$ purportedly defining a total order on $\\{0,1\\}^{n}$, find either a witness that $\\prec$ is not a total order or else a minimal element in the ordering. The problem LOP is quite interesting in its own right, as it defines a natural syntactic subclass ” $\\mathrm{L}_{2}^{\\mathrm{P}}$ “ of $\\mathrm{s}_{2}^{\\mathrm{p}}$ which nonetheless maintains most of the interesting properties of $\\mathsf{S}_{2}^{\\mathrm{P}}$ ; in particular we show that $\\mathrm{L}_{2}^{\\mathrm{P}}$ contains MA and that its exponential analogue $\\mathrm{L}_{2}^{\\mathrm{E}}$ requires $2^{n}/n$ size circuits. Both of these are consequences of our reduction from Range Avoidance to LOP. On the negative side we prove that the algorithms developed in [1], [2] cannot be extended to Strong Range Avoidance, a problem considered in the same paper which first introduced Range Avoidance [4]. In this problem we are given a circuit $C$: $\\{0,1\\}^{n}\\backslash \\{0^{n}\\}\\rightarrow\\{0,1\\}^{n}$, and once again seek a point outside its range. We give a separation in the decision tree (oracle) model showing that this problem cannot be solved in FP $\\Sigma_{2}^{\\mathrm{P}}\\Vert$, which in particular rules out all of the new kinds of algorithms considered in [1], [2]. This black box separation is derived from a novel depth 3 AC°circuit lower bound for a total search problem, which we believe is of independent interest from the perspective of circuit complexity: we show that unlike previous depth 3 lower bounds, ours cannot be proven by reduction from a decision problem, and thus requires new techniques specifically tailored to total search problems. Proving lower bounds of this kind was recently proposed by Vyas and Williams in the context of the original (Weak) Avoid problem [5].", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00089"}, {"primary_key": "587241", "vector": [], "sparse_vector": [], "title": "Exponential Lower Bounds for Smooth 3-LCCs and Sharp Bounds for Designs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give improved lower bounds for binary 3-query locally correctable codes (3-LCCs)$C: \\{\\ 0,1 \\}^k \\rightarrow \\{\\ 0,1 \\}^n$. Specifically, we prove: 1) If C is a linear design 3-LCC, then $n \\geq 2^{(1 - o(1))\\sqrt{k} }$. A design 3-LCC has the additional property that the correcting sets for every codeword bit form a perfect matching, and every pair of codeword bits is queried an equal number of times across all matchings. Our bound is tight up to a factor $\\sqrt{8}$ in the exponent of 2, as the best construction of binary 3-LCCs (obtained by taking Reed--Muller codes on F_4 and applying a natural projection map) is a design 3-LCC with $n \\leq 2^{\\sqrt{8 k}}$. Up to a factor of 8, this resolves the <PERSON>ada conjecture on the maximum F_2-codimension of a 4-design. 2) If C is a smooth, non-linear, adaptive 3-LCC with perfect completeness, then, $n \\geq 2^{\\Omega(k^{1/5})}$. 3) If C is a smooth, non-linear, adaptive 3-LCC with completeness 1 - \\eps, then n \\geq \\Omega(k^{\\frac${1}{2\\eps}}). In particular, when $\\eps$ is a small constant, this implies a lower bound for general non-linear LCCs that beats the prior best $n \\geq \\Omega(k^3)$ lower bound of Alrabiah-Guruswami-Kothari-Manohar by a polynomial factor. Our design LCC lower bound is obtained via a fine-grained analysis of the Kikuchi matrix method applied to a variant of the matrix used in the work of Kothari and Manohar (2023). Our lower bounds for non-linear codes are obtained by designing a from-scratch reduction from nonlinear 3-LCCs to a system of “chain XOR equations” — polynomial equations with a similar structure to the long chain derivations that arise in the lower bounds for linear 3-LCCs of Kothari and Manohar.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00110"}, {"primary_key": "587242", "vector": [], "sparse_vector": [], "title": "Tensor Cumulants for Statistical Inference on Invariant Distributions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many problems in high-dimensional statistics appear to have a statistical-computational gap: a range of values of the signal-to-noise ratio where inference is information-theoretically possible, but (conjecturally) computationally in-tractable. A canonical such problem is Tensor PCA, where we observe a tensor $Y$ consisting of a rank-one signal plus Gaussian noise. Multiple lines of work suggest that Tensor PCA becomes computationally hard at a critical value of the signal's magnitude. In particular, below this transition, no low-degree polynomial algorithm can detect the signal with high probability; conversely, various spectral algorithms are known to succeed above this transition. We unify and extend this work by considering tensor networks, orthogonally invariant polynomials where multiple copies of $Y$ are “contracted” to produce scalars, vectors, matrices, or other tensors. We define a new set of objects, tensor cumulants, which provide an explicit, near-orthogonal basis for invariant polynomials of a given degree. This basis lets us unify and strengthen previous results on low-degree hardness, giving a combinatorial explanation of the hardness transition and of a continuum of subexponential-time algorithms that work below it, and proving tight lower bounds against low-degree polynomials for recovering rather than just detecting the signal. It also lets us analyze a new problem of distinguishing between different tensor ensembles, such as <PERSON><PERSON><PERSON> and <PERSON><PERSON> tensors, establishing a sharp computational threshold and giving evidence of a new statistical-computational gap in the Central Limit Theorem for random tensors. Finally, we believe these cumulants are valuable mathematical objects in their own right: they generalize the free cumulants of free probability theory from matrices to tensors, and share many of their properties, including additivity under additive free convolution.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00067"}, {"primary_key": "587243", "vector": [], "sparse_vector": [], "title": "Computational Hardness of Detecting Graph Lifts and Certifying Lift-Monotone Properties of Random Regular Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new conjecture on the computational hardness of detecting random lifts of graphs: we claim that there is no polynomial-time algorithm that can distinguish between a large random $d$ -regular graph and a large random lift of a Ramanujan $d$ -regular base graph (provided the lift is corrupted by a small amount of extra noise), and likewise for bipartite random graphs and lifts of bipartite Ramanujan graphs. We give evidence for this conjecture by proving lower bounds against the local statistics hierarchy of hypothesis testing semidefinite programs. We then explore the consequences of the conjecture for the hardness of certifying bounds on numerous functions of random regular graphs, expanding on a direction initiated by <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON> (2021). Conditional on this conjecture, we show that no polynomial-time algorithm can certify tight bounds on the maximum cut or maximum independent set of random 3- or 4-regular graphs, or on the chromatic number of random 7-regular graphs. Asymptotically for large degree for the maximum independent set and for any degree for the minimum dominating set, we show similar gaps, finding that naive spectral and combinatorial bounds are optimal among efficiently computable ones. Likewise, for small set vertex and edge expansion in the limit of very small sets, we show that the spectral bounds due to <PERSON><PERSON> (1995) are optimal efficient certificates.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00101"}, {"primary_key": "587244", "vector": [], "sparse_vector": [], "title": "Efficient Statistics With Unknown Truncation, Polynomial Time Algorithms, Beyond Gaussians.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Manolis Zampetakis"], "summary": "We study the estimation of distributional parameters when samples are shown only if they fall in some unknown set. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (FOCS'19) gave an algorithm for finding parameters for the special case of Gaussian distributions with diagonal covariance matrix. Recently, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> (COLT'24) showed that an exponential dependence on the inverse of the accuracy parameter is necessary even when the set belongs to some well-behaved classes. These works leave the following open problems which we address in this work: Can we estimate the parameters of any Gaussian or even extend the results beyond Gaussians? Can we design polynomial-time algorithms when for simple sets such as a halfspace? Toward the first question, we provide an estimation algorithm for any exponential family that satisfies some structural assumptions and any unknown set that is approximable by polynomials. This result has two important applications: (a)The first algorithm for estimating arbitrary Gaussian distributions (even with non-diagonal covariance matrix) from samples truncated to unknown set; and (b)The first algorithm for linear regression with unknown truncation and Gaussian features. To address the second question, we provide an algorithm with polynomial sample and time complexity that works for a set of exponential families (that contains multivariate Gaussians) when the unknown survival set is a halfspace or an axis-aligned rectangle.11A preliminary version of this paper incorrectly claimed the result for finite unions of axis-aligned rectangles. The result only holds for a single axis-aligned rectangle. This is the first fully polynomial time algorithm for estimation with an unknown truncation set. Along the way, we develop new tools that may be of independent interest, including: (c)The first polynomial time algorithm for learning halfspaces using only positive examples when the samples have an unknown Gaussian distribution; and (d)A reduction from PAC learning with positive and unlabeled samples to PAC learning with positive and negative samples that is robust to certain covariate shifts.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00066"}, {"primary_key": "587245", "vector": [], "sparse_vector": [], "title": "Distinguishing, Predicting, and Certifying: On the Long Reach of Partial Notions of Pseudorandomness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper revisits the study of two classical technical tools in theoretical computer science: <PERSON>'s trans-formation of distinguishers to next-bit predictors (FOCS 1982), and the “reconstruction paradigm” in pseudorandomness (e.g., as in <PERSON><PERSON> and <PERSON><PERSON><PERSON>, JCSS 1994). Recent works of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (FOCS 2023) and Doron, Pyne, and Tell (STOC 2024) showed that both of these tools can be derandomized in the specific context of read-once branching programs (ROBPs), but left open the question of de randomizing them in more general settings. Our main contributions give appealing evidence that derandomization of the two tools is possible in general settings, show surprisingly strong consequences of such derandomization, and reveal several new settings where such derandomization is unconditionally possible for algorithms stronger than ROBPs (with useful consequences). Specifically: •We show that derandomizing these tools is equivalent to general derandomization. Specifically, we show that derandomizing distinguish - to- predict transformations is equivalent to prBPP=prP, and that derandomized reconstruction procedures (in a more general sense that we introduce) is equivalent to prBPP=prZPP. These statements hold even when scaled down to weak circuit classes and to algorithms that run in super-polynomial time. •Our main technical contributions are unconditional constructions of derandomized versions of <PERSON>'s transformation (or reductions of this task to other problems) for classes and for algorithms beyond ROBPs. Consequently, we deduce new results: A significant relaxation of the hypotheses required to derandomize the isolation lemma for logspace algorithms and deduce that NL=UL; and proofs that de-randomization necessitates targeted PRGs in catalytic logspace (unconditionally) and in logspace (conditionally). In addition, we introduce a natural subclass of prZPP that has been implicitly studied in recent works (Korten FOCS 2021, CCC 2022): The class of problems reducible to a problem called “Lossy Code”. We provide a structural characterization for this class in terms of derandomized reconstruction procedures, and show that this characterization is robust to several natural variations. Lastly, we present alternative proofs for classical results in the theory of pseudorandomness (such as two-sided derandomization reducing to one-sided), relying on the notion of deterministically transforming distinguishers to predictors as the main technical tool.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00095"}, {"primary_key": "587246", "vector": [], "sparse_vector": [], "title": "On Approximate Fully-Dynamic Matching and Online Matrix-Vector Multiplication.", "authors": ["<PERSON>"], "summary": "We study connections between the problem of fully dynamic $(1-\\epsilon)$-approximate maximum bipartite matching, and the dual $(1+\\epsilon)$-approximate vertex cover problem, with the online matrix-vector (OMv) conjecture which has recently been used in several fine-grained hardness reductions. We prove that there is an online algorithm that maintains a $(1+\\epsilon)$-approximate vertex cover in amortized $n^{1-c}\\epsilon^{-C}$ time for constants $c, C > 0$ for fully dynamic updates if and only if the OMv conjecture is false. Similarly, we prove that there is an online algorithm that maintains a $(1-\\epsilon)$-approximate maximum matching in amortized $n^{1-c}\\epsilon^{-C}$ time if and only if there is a nontrivial algorithm for another dynamic problem, which we call dynamic approximate OMv, that has seemingly no matching structure. This provides some evidence against achieving amortized sublinear update times for approximate fully dynamic matching and vertex cover. Leveraging these connections, we obtain faster algorithms for approximate fully dynamic matching in both the online and offline settings. We give a randomized algorithm that with high probability maintains a $(1-\\epsilon)$-approximate bipartite matching and $(1+\\epsilon)$-approximate vertex cover in fully dynamic graphs, in amortized $O(\\epsilon^{-O(1)}\\frac{n}{2^{\\Omega}(\\sqrt{\\log n})})$ up-date time. This improves over the previous fastest runtimes of $O(n/(\\log^{*}n)^{\\Omega(1)})$ due to Assadi-Behnezhad-Khanna-Li [STOC 2023], and $O_{\\epsilon}(n^{1-\\Omega_{\\epsilon}(1)})$ due to Bhattacharya-Kiss-Saranurak [FOCS 2023] for small $\\epsilon$. Our algorithm leverages fast algorithms for OMv due to Larsen and Williams [SODA 2017]. We give a randomized offline algorithm for (1 - $\\epsilon)$-approximate maximum matching with amortized runtime $O(n^{.58}\\epsilon^{-O(1)})$ by using fast matrix multi-plication, significantly improving over the runtimes achieved via online algorithms mentioned above. This mirrors the situation with OMv, where an offline algorithm exactly corresponds to fast matrix mul-tiplication. We also give an offline algorithm that maintains a $(1+\\epsilon)$-approximate vertex cover in amortized $O(n^{.723}\\epsilon^{-O(1)})$ time.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00006"}, {"primary_key": "587247", "vector": [], "sparse_vector": [], "title": "Locally Stationary Distributions: A Framework for Analyzing Slow-Mixing Markov Chains.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Many natural Markov chains fail to mix to their stationary distribution in polynomially many steps. Often, this slow mixing is inevitable since it is computationally intractable to sample from their stationary measure. Nevertheless, Markov chains can be shown to always converge quickly to measures that are locally stationary, i.e., measures that don't change over a small number of steps. These locally stationary measures are analogous to local minima in continuous optimization, while stationary measures correspond to global minima. While locally stationary measures can be statistically far from stationary measures, do they enjoy provable theoretical guarantees that have algorithmic implications? We study this question in this work and demonstrate three algorithmic applications of locally stationary measures: 1)We show that Glauber dynamics on the hardcore model can be used to find large independent sets in triangle-free graphs of bounded degree. 2)We prove that Glauber dynamics on the Ising model defined by a spiked matrix model finds a vector with constant correlation with the planted spike. 3)We show that for sufficiently large constant signal-to-noise ratio, Glauber dynamics on the Ising model finds a vector that has constant correlation with the hidden community vector. In other words, Glauber dynamics subsumes the spectral method for spiked Wigner and community detection, by weakly recovering the planted spike. The full version of this paper can be found on arXiv(arXiv ID: 2405.20849).", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00022"}, {"primary_key": "587248", "vector": [], "sparse_vector": [], "title": "Fast Mixing in Sparse Random Ising Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Motivated by the community detection problem in Bayesian inference, as well as the recent explosion of interest in spin glasses from statistical physics, we study the classical Glauber dynamics for sampling from Ising models with sparse random interactions. It is now well-known that when the in- teraction matrix has spectral diameter less than 1, G<PERSON>ber dynamics mixes in near-linear time. Unfortunately, such criteria fail dramatically for interactions supported on arguably the most well-studied sparse random graph: the <PERSON><PERSON><PERSON><PERSON> random graph. There is a scarcity of positive results in this setting due to the presence of almost linearly many outlier eigenvalues of unbounded magnitude. We prove that for the Viana-Bray spin glass, where the interactions are supported on a random graph and randomly assigned signs, Glauber dynamics mixes in almost-linear time with high probability at sufficiently high temperatures, and we conjecture that our results are tight up to constants. We further extend our results to random graphs drawn according to the 2-community stochastic block model, as well as when the interactions are given by a “centered” version of the adjacency matrix. The latter setting is particularly relevant for the inference problem in community detection. Indeed, we build on this result to demonstrate that Glauber dynamics succeeds at recovering communities in the stochastic block model in a companion paper. The primary technical ingredient in our proof is showing that with high probability, a sparse random graph can be decomposed into two parts - a bulk which behaves like a graph with bounded maximum degree and a well-behaved spectrum, and a near- forest with favorable pseudorandom properties. We then use this decomposition to design a localization procedure that interpolates to simpler Ising models supported only on the near-forest, and then execute a pathwise analysis to establish a modified log- Sobolev inequality. The full version of this paper can be found on arXiv (arXiv ID: 2405.06616).", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00018"}, {"primary_key": "587249", "vector": [], "sparse_vector": [], "title": "Quantum Eigenvalue Processing.", "authors": ["<PERSON><PERSON>", "Yuan Su"], "summary": "Many problems in linear algebra-such as those arising from non-Hermitian physics, transcorrelated quantum chemistry and differential equations-can be solved on a quan-tum computer by processing eigenvalues of the non-normal input matrices. However, the existing Quantum Singular Value Transformation (QSVT) framework is ill-suited to this task, as eigenvalues and singular values are different in general. We present a Quantum EigenValue Transformation (QEVT) framework for applying arbitrary polynomial transformations on eigenvalues of block-encoded non-normal operators, and a related Quantum EigenValue Estimation (QEVE) algorithm for operators with real spectra. QEVT has query complexity to the block encoding nearly recovering that of the QSVT for a Hermitian input, and QEVE achieves the Heisenberg-limited scaling for diagonalizable input matrices. As applications, we develop a linear differential equation solver with strictly linear time query complexity for average-case diagonalizable operators, as well as a ground state preparation algorithm that upgrades previous nearly optimal results for Hermitian Hamiltonians to diagonalizable matrices with real spectra. Underpinning our algorithms is an efficient method to prepare a quantum super-position of Faber polynomials, which generalize the nearly-best uniform approximation properties of <PERSON><PERSON><PERSON><PERSON>v polynomials to the complex plane. Of independent interest, we also develop techniques to generate $n$ Fourier coefficients with 0 (poly log ($n$)) gates compared to prior approaches with linear cost.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00070"}, {"primary_key": "587250", "vector": [], "sparse_vector": [], "title": "On the Complexity of Avoiding Heavy Elements.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Ren", "<PERSON><PERSON>"], "summary": "We introduce and study the following natural total search problem, which we call the heavy element avoidance (Heavy Avoid) problem: for a distribution on $N$ bits specified by a Boolean circuit sampling it, and for some parameter $\\delta(N)\\geq 1/$ poly $(N)$ fixed in advance, output an $N$ -bit string that has probability less than $\\delta(N)$. We show that the complexity of Heavy Avoid is closely tied to frontier open questions in complexity theory about uniform randomized lower bounds and derandomization. Among other results, we show: 1)For a wide range of circuit classes $\\mathcal{C}$, including $\\text{ACC}^{0}, \\text{TC}^{0},\\text{NC}^{1}$ and general Boolean circuits, EX P does not have uniform randomized C-circuits if and only if Heavy Avoid for uniform implicit C -samplers has efficient deterministic algorithms infinitely often. This gives the first algorithmic characterization of lower bounds for EXP against uniform randomized low-depth circuits. We show similar algorithmic characterizations for lower bounds in PSPACE, NP and $\\text{EXP}^{\\text{NP}}$. 2)Unconditionally, there are polynomial-time pseudodeterministic algorithms that work infinitely often for several variants of Heavy Avoid, such as for uniform samplers of small randomness complexity. In contrast, the existence of a similar algorithm that solves Heavy Avoid for arbitrary polynomial-time samplers would solve a long-standing problem about hierarchies for probabilistic time. 3)If there is a time and depth efficient deterministic algorithm for Heavy Avoid, then $BPP=P$. Without the depth-efficiency requirement in the assumption, we still obtain a non-trivial form of infinitely-often deterministic simulation of randomized algorithms. These results are shown using non-black-box reductions, and we argue that the use of non-black-box reductions is essential here. The full version is available on ECCC [1].", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00140"}, {"primary_key": "587251", "vector": [], "sparse_vector": [], "title": "Succinct Arguments for QMA from Standard Assumptions via Compiled Nonlocal Games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We construct a succinct classical argument system for QMA, the quantum analogue of NP, from generic and standard cryptographic assumptions. Previously, building on the prior work of <PERSON><PERSON><PERSON> (FOCS '18), <PERSON><PERSON><PERSON> et al. (CRYPTo ‘22) also constructed a succinct classical argument system for Q M A. However, their construction relied on post-quantumly secure indistinguishability obfuscation, a very strong primitive which is not known from standard cryptographic assumptions. In contrast, the primitives we use (namely, collapsing hash functions and a mild version of quantum homomorphic encryption) are much weaker and are implied by standard assumptions such as LWE. Our protocol is constructed using a general transformation which was designed by <PERSON><PERSON> et al. (STOC '23) as a candidate method to compile any quantum nonlocal game into an argument system. Our main technical contribution is to analyze the soundness of this transformation when it is applied to a succinct self-test for Pauli measurements on maximally entangled states, the latter of which is a key component in the proof of MIP * = R E in Quantum complexity.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00078"}, {"primary_key": "587252", "vector": [], "sparse_vector": [], "title": "Simple Constructions of Linear-Depth t-Designs and Pseudorandom Unitaries.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Uniformly random unitaries, i.e. unitaries drawn from the Haar measure, have many useful properties, but cannot be implemented efficiently. This has motivated a long line of research into random unitaries that “look” sufficiently Haar random while also being efficient to implement. Two different notions of derandomisation have emerged: $t$-designs are random unitaries that information-theoretically reproduce the first $t$ moments of the Haar measure, and pseudorandom unitaries (PRUs) are random unitaries that are computationally indistinguishable from Haar random. In this work, we take a unified approach to constructing $t$-designs and PRUs. For this, we introduce and analyse the “ $PFC$ ensemble”, the product of a random computational basis permutation $P$, a random binary phase operator $F$, and a random Clifford unitary $C$. We show that this ensemble reproduces exponentially high moments of the Haar measure. We can then derandomise the $PFC$ ensemble to show the following: •Linear-depth $t$-designs. We give the first construction of a (diamond-error) approximate $t$-design with circuit depth linear in $t$. This follows from the $PFC$ ensemble by replacing the random phase and permutation operators with their $2t$-wise independent counterparts. •Non-adaptive PRUs. We give the first construction of PRUs with non-adaptive security, i.e. we construct unitaries that are indistinguishable from Haar random to polynomial-time distinguishers that query the unitary in parallel on an arbitary state. This follows from the $PFC$ ensemble by replacing the random phase and permutation operators with their pseudorandom counterparts. •Adaptive pseudorandom isometries. We show that if one considers isometries (rather than unitaries) from $n$ to $n+\\omega(\\log n)$ qubits, a small modification of our PRU construction achieves adaptive security, i.e. even a distinguisher that can query the isometry adaptively in sequence cannot distinguish it from Haar random isometries. This gives the first construction of adaptive pseudorandom isometries. Under an additional conjecture, this proof also extends to adaptive PRUs.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00038"}, {"primary_key": "587253", "vector": [], "sparse_vector": [], "title": "Polynomial Calculus Sizes Over the Boolean and Fourier Bases are Incomparable.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "For every $n > 0$, we show the existence of a CNF tautology over $O(n^{2})$ variables of width $O(\\log {\\it n})$ such that it has a Polynomial Calculus Resolution refutation over $\\{0,1\\}$ variables of size $O(n^{3} \\text{polylog} (n))$ but any Polynomial Calculus refutation over $\\{+1, -1\\}$ variables requires size $2^{\\Omega(n)}$. This shows that Polynomial Calculus sizes over the {0, 1} and $\\{+1,\\ -1\\}$ bases are incomparable (since Tseitin tautologies show a separation in the other direction) and answers an open problem posed by <PERSON><PERSON><PERSON> [1] and <PERSON><PERSON><PERSON><PERSON> [2].", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00055"}, {"primary_key": "587254", "vector": [], "sparse_vector": [], "title": "Instance-Optimality in I/O-Efficient Sampling and Sequential Estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Suppose we have a memory storing 0s and 1s and we want to estimate the frequency of 1s by sampling. We want to do this I/O-efficiently, exploiting that each read gives a block of B bits at unit cost; not just one bit. If the input consists of uniform blocks: either all 1s or all Os, then sampling a whole block at a time does not reduce the number of samples needed for estimation. On the other hand, if bits are randomly permuted, then getting a block of B bits is as good as getting B indendent bit samples. However, we do not want to make any such assumptions on the input. Instead, our goal is to have an algorithm with instance-dependent performance guarantees which stops sampling blocks as soon as we know that we have a probabilistically reliable estimate. We prove our algorithms to be instance-optimal among algorithms oblivious to the order of the blocks, which we argue is the strongest form of instance optimality we can hope for. We also present similar results for I/O-efficiently estimating mean with both additive and multiplicative error, estimating histograms, quantiles, as well as the empirical cumulative distribution function. We obtain our above results on I/O-efficient sampling by reducing to corresponding problems in the so-called sequential estimation. In this setting, one samples from an unknown distribution until one can provide an estimate with some desired error probability. Sequential estimation has been considered extensively in statistics over the past century. However, the focus has been mostly on parametric estimation, making stringent assumptions on the distribution of the input, and thus not useful for our reduction. In this paper, we make no assumptions on the input distribution (apart from its support being a bounded set). Namely, we provide non-parametric instance-optimal results for several fundamental problems: mean and quantile estimation, as well as learning mixture distributions with respect to ℓ_{\\infty} and the so-called Kolmogorov-Smirnov distance. All our algorithms are simple, natural, and practical, and some are even known from other contexts, e.g., from statistics in the parameterized setting. The main technical difficulty is in analyzing them and proving that they are instance optimal.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00048"}, {"primary_key": "587255", "vector": [], "sparse_vector": [], "title": "An Optimal Algorithm for Sorting Pattern-Avoiding Sequences.", "authors": ["<PERSON><PERSON>"], "summary": "We present a deterministic comparison-based algorithm that sorts sequences avoiding a fixed permutation $\\pi$ in linear time, even if $\\pi$ is a priori unkown. Moreover, the dependence of the multiplicative constant on the pattern $\\pi$ matches the information-theoretic lower bound. A crucial ingredient is an algorithm for performing efficient multi-way merge based on the <PERSON> theorem. As a direct corollary, we obtain a linear-time algorithm for sorting permutations of bounded twin-width.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00049"}, {"primary_key": "587256", "vector": [], "sparse_vector": [], "title": "Obstructions to Erdös-Pósa Dualities for Minors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Dimitrios M. Thilikos", "<PERSON>"], "summary": "Let $\\mathcal{G}$ and $\\mathcal{H}$ be minor-closed graph classes. We say that the pair $(\\mathcal{H},\\ \\mathcal{G})$ is an Erdös-Pósa pair (EP-pair) if there exists a function $f$ such that for every $k$ and every graph $G\\in \\mathcal{G}$, either $G$ has $k$ pairwise vertex-disjoint sub graphs which do not belong to $\\mathcal{H}$, or there exists a set $S\\subseteq V(G)$ of size at most $f(k)$ for which $G-S\\in \\mathcal{H}$. The classic result of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> says that if $\\mathcal{F}$ is the class of forests, then $(\\mathcal{F}, \\mathcal{G})$ is an EP-pair for all graph classes $\\mathcal{G}$. A minor-closed graph class $\\mathcal{G}$ is an EP-counterexample for $\\mathcal{H}$ if $\\mathcal{G}$ is minimal with the property that $(\\mathcal{H},\\ \\mathcal{G})$ is not an EP-pair. In this paper, we prove that for every minor-closed graph class $\\mathcal{H}$ the set $\\mathfrak{C}_{\\mathcal{H}}$ of all EP-counterexamples for $\\mathcal{H}$ is finite. In particular, we provide a complete characterization of $\\mathfrak{C}_{\\mathcal{H}}$ for every $\\mathcal{H}$ and give a constructive upper bound on its size. We show that each class $\\mathcal{G}$ in $\\mathfrak{C}_{\\mathcal{H}}$ can be described as the set of all minors of some, suitably defined, sequence of grid-like graphs $\\langle{W}_{k}\\rangle_{k\\in \\mathbb{N}}$. Moreover, each $\\mathrm{W}_{k}$ admits a half-integral packing, i.e., $k$ copies of some $H\\not\\in \\mathcal{H}$ where no vertex is used more than twice. This implies a complete delineation of the half-integrality threshold of the Erdös-Pósa property for minors and as a corollary, we obtain a constructive proof of Thomas' conjecture on the half-integral Erdös-Pósa property for minors which was recently confirmed by Liu. Our results are algorithmic. Let $h=h(\\mathcal{H})$ denote the maximum size of an obstruction to $\\mathcal{H}$. For every minor-closed graph class $\\mathcal{H}$, we construct an algorithm that, given a graph $G$ and an integer $k$, either outputs a half-integral packing of $k$ copies of some $H\\not\\in \\mathcal{H}$ or outputs a set of at most $2^{k^{\\overline{\\mathcal{O}}_{h}(1)}}$ vertices whose deletion creates a graph in $\\mathcal{H}$ in time $2^{2^{k^{\\mathcal{O}_{h}(1)}}}\\cdot\\vert G\\vert ^{4}\\log\\vert G\\vert$. Moreover, as a consequence of our results, for every minor-closed class $\\mathcal{H}$, we obtain min-max-dualities, which may be seen as analogues of the celebrated Grid Theorem of Robertson and Seymour, for the recently introduced parameters $\\mathcal{H}$-treewidth and elimination distance to $\\mathcal{H}$.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00013"}, {"primary_key": "587257", "vector": [], "sparse_vector": [], "title": "Directed Isoperimetry and Monotonicity Testing: A Dynamical Approach.", "authors": ["<PERSON><PERSON>."], "summary": "This paper explores the connection between classical isoperimetric inequalities, their directed analogues, and mono-tonicity testing. We study the setting of real-valued functions $f:[0, 1]^{d}\\rightarrow\\mathbb{R}$ on the solid unit cube, where the goal is to test with respect to the $L^{p}$ distance. Ourgoals are twofold: to further understand the relationship between classical and directed isoperimetry, and to give a monotonicity tester with sublinear query complexity in this setting, Our main results are 1) an $L^{2}$ monotonicity tester for $M$-Lipschitz functions with query complexity $O(\\sqrt{d}M^{2}/\\varepsilon^{2})$ and, behind this result, 2) the directed Poincaré inequality $\\text{dist}_{2}^{\\text{mono}}(f)^{2}\\leq C\\mathbb{E}\\Vert \\nabla^{-}f\\vert^{2}]$, where the “directed gradient” operator $\\nabla{-}$ measures the local violations of monotonicity of $f$. To prove the second result, we introduce a partial differential equation (PDE), the directed heat equation, which takes a one-dimensional function $f$ into a monotone function $f^{*}$ over time and enjoys many desirable analytic properties. We obtain the directed <PERSON><PERSON>caré inequality by combining convergence aspects of this PDE with the theory of optimal transport. Crucially for our conceptual motivation, this proof is in complete analogy with the mathematical physics perspective on the classical Poincaré inequality, namely as characterizing the convergence of the standard heat equation toward equilibrium.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00134"}, {"primary_key": "587258", "vector": [], "sparse_vector": [], "title": "Canonical Forms for Matrix Tuples in Polynomial Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Left-right and conjugation actions on matrix tuples have received considerable attention in theoretical computer science due to their connections with polynomial identity testing, group isomorphism, and tensor isomorphism. In this paper, we present polynomial-time algorithms for computing canonical forms of matrix tuples over a finite field under these actions. Our algorithm builds upon new structural insights for matrix tuples, which can be viewed as a generalization of <PERSON><PERSON>'s lemma for irreducible representations to general representations. Index Terms-canonical form, matrix tuples, tensors, group isomorphism, computer algebra", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00054"}, {"primary_key": "587259", "vector": [], "sparse_vector": [], "title": "Proofs of Space with Maximal Hardness.", "authors": ["<PERSON><PERSON>"], "summary": "In a proof of space, a prover performs a complex computation with a large output. A verifier periodically checks that the prover still holds the output. The security goal for a proof of space construction is to ensure that a prover who erases even a portion of the output has to redo a large portion of the complex computation in order to satisfy the verifier. In existing constructions of proofs of space, the computation that a cheating prover is forced to redo is a small fraction (van-ishing or small constant) of the original complex computation. The only exception is a construction of Pie<PERSON>zak (ITCS 2019) that requires extremely depth-robust graphs, which result in impractically high complexity of the initialization process. We present the first proof of space of reasonable complexity that ensures that the prover has to redo almost the entire computation (fraction arbitrarily close to 1) when trying to save even an arbitrarily small constant fraction of the space. Our construction is a generalization of an existing construction called SDR (Fisch, Eurocrypt 2019) deployed on the Filecoin blockchain. Our improvements, while general, also demonstrate that the already deployed construction has considerably better security than previously shown. Technically, our construction can be viewed as amplifying predecessor-robust graphs. These are directed acyclic graphs in which every sufficiently large set of nodes contains a large subset of nodes whose induced sub graph has just one sink. We take a predecessor-robust graph with constant-fraction parameters for the sizes of the set and subset, and build a bigger predecessor-robust graph with a near-optimal set of parameters and additional guarantees on sink placement, while increasing the degree only by a small additive constant.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00076"}, {"primary_key": "587260", "vector": [], "sparse_vector": [], "title": "Communication Separations for Truthful Auctions: Breaking the Two-Player Barrier.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the communication complexity of truthful combinatorial auctions, and in particular the case where valuations are either subadditive or single-minded, which we denote with SubAddUSingleM. We show that for three bidders with valuations in SubAddUSingleM, any deterministic truthful mechanism that achieves at least a 0.366-approximation requires $\\exp(m)$ communication. In contrast, a natural extension of [Fei09] yields a non-truthful $\\text{poly}(m)-\\mathbf{communication}$ protocol that achieves a $\\frac{1}{2}-\\mathbf{approximation}$, demonstrating a gap between the power of truthful mechanisms and non-truthful protocols for this problem. Our approach follows the taxation complexity framework laid out in [Dob16b], but applies this framework in a setting not encompassed by the techniques used in past work. In particular, the only successful prior application of this framework uses a reduction to simultaneous protocols which only applies for two bidders [AKSW20], whereas our three-player lower bounds are stronger than what can possibly arise from a two-player construction (since a trivial truthful auction guarantees a $\\frac{1}{2}- \\mathbf{approximation}$ for two players).", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00032"}, {"primary_key": "587261", "vector": [], "sparse_vector": [], "title": "The Communication Complexity of Approximating Matrix Rank.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We fully determine the communication complexity of approximating matrix rank, over any finite field F. We study the most general version of this problem, where $0\\leqslant r are given integers and <PERSON> and <PERSON> need to determine whether their respective matrices $A, B\\in \\mathbb{F}^{n\\times n}$ satisfy rk $(A+B)=r$ versus rk $(A+B)=R$. We show that this problem has commu-nication cost $\\Omega(r^{2}\\log\\vert \\mathbb{F}\\vert)$, which is optimal. Our lower bound holds even for quantum protocols and even for error probability $\\frac{1}{2}(1-\\vert \\mathbb{F}\\vert ^{-r/3})$, which too is optimal because this problem has a two-bit classical protocol with error $\\frac{1}{2}(1-\\vert \\mathbb{F}\\vert ^{-\\Theta(r)})$. Prior to our work, lower bounds were known only for constant-error protocols and only for consecutive integers $r$ and $R$, with no implication for the approximation of matrix rank. We also settle an analogous question for subspaces, where <PERSON> has a subspace $S$, <PERSON> has a subspace $T$) and they need to approximate the dimension of the subspace $S+T$ generated by $S$ and $T$ (equivalently, approximate the dimension of $S\\cap T$). As an application, we obtain an $\\Omega(n^{2}\\log\\vert \\mathbb{F}\\vert)/k$ memory lower bound for any streaming algorithm with $k$ passes that approximates the rank of an input matrix $M\\in \\mathbb{F}^{n\\times n}$ within a factor of $\\sqrt{2}-\\delta$, for any $\\delta > 0$. Our result is an exponential improvement in $k$ over previous work.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00035"}, {"primary_key": "587262", "vector": [], "sparse_vector": [], "title": "A Lossless Deamortization for Dynamic Greedy Set Cover.", "authors": ["<PERSON>", "<PERSON><PERSON>i <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The dynamic set cover problem has been subject to growing research attention in recent years. In this problem, we are given as input a dynamic universe of at most $n$ elements and a fixed collection of $m$ sets, where each element appears in a most $f$ sets and the cost of each set is in [1/C, 1], and the goal is to efficiently maintain an approximate minimum set cover under element updates. Two algorithms that dynamize the classic greedy algorithm are known, providing $O(\\log n)$ and $((1+\\epsilon)\\ln n)$-approximation with amortized update times $O(f \\log n)$ and, $O(\\frac{f \\log n}{\\epsilon})$, respectively [GKKP (STOC'17); SU (STOC'23)]. The question of whether one can get approximation $O(\\log n)$ (or even worse) with low worst-case update time has remained open — only the naive $O(f\\cdot n)$ time bound is known, even for unweighted instances. In this work we devise the first amortized greedy algorithm that is amenable to an efficient deamortization, and also develop a lossless deamortization approach suitable for the set cover problem, the combination of which yields a $((1+\\epsilon)\\ln n){-}$ approximation algorithm with a worst-case update time of $O(\\frac{f \\log n}{\\epsilon^{2}})$. Our worst-case time bound — the first to break the naive $O(f\\cdot n)$ bound — matches the previous best amortized bound, and actually improves its $\\epsilon$ -dependence. Further, to demonstrate the applicability of our deamortization approach, we employ it, in conjunction with the primal-dual amortized algorithm of [BHN (FOCS'19)], to obtain a $((1+\\epsilon)f)$-approximation algorithm with a worst-case update time of $O(\\frac{f \\log n}{\\epsilon^{2}})$, improving over the previous best bound of $O(\\frac{f \\cdot \\log ^{2}(C n)}{-3})\\ [$ BHNW (SODA'21)]. Finally, as direct implications of our results for set cover, we (i) achieve the first nontrivial worst-case update time for the dominating set problem, and (ii) improve the state-of-the-art worst-case update time for the vertex cover problem.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00025"}, {"primary_key": "587263", "vector": [], "sparse_vector": [], "title": "A Sampling Lovász Local Lemma for Large Domain Sizes.", "authors": ["<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>"], "summary": "We present polynomial-time algorithms for approximate counting and sampling solutions to constraint satisfaction problems (CSPs) with atomic constraints within the local lemma regime: \\begin{equation*} pD^{2+o_{q}(1)}\\lesssim 1.\\end{equation*} When the domain size $q$ of each variable becomes sufficiently large, this almost matches the known lower bound $pD^{2}\\lesssim 1$ for approximate counting and sampling solutions to atomic CSPs [1], [2], thus establishing an almost tight sampling Lovasz local lemma for large domain sizes.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00019"}, {"primary_key": "587264", "vector": [], "sparse_vector": [], "title": "The Orthogonal Vectors Conjecture and Non-Uniform Circuit Lower Bounds.", "authors": ["<PERSON>"], "summary": "A line of work has shown how nontrivial uniform algorithms for analyzing circuits can be used to derive non-uniform circuit lower bounds. We show how the non-existence of nontrivial circuit-analysis algorithms can also imply non-uniform circuit lower bounds. Our connections yield new win-win circuit lower bounds, and suggest a potential approach to refuting the Orthogonal Vectors Conjecture in the $O(\\log n)$ -dimensional case, which would be sufficient for refuting the Strong Exponential Time Hypothesis (SETH). For example, we show that at least one of the following holds: • There is an $\\varepsilon>0$ such that for infinitely many $n$, read-once 2-DNFs on $n$ variables cannot be simulated by non-uniform $2^{\\varepsilon n}$ -size depth-two exact threshold circuits. It is already a notorious open problem to prove that the class $E^{N P}$ does not have polynomial-size depth-two exact threshold circuits, so such a lower bound would be a significant advance in low-depth circuit complexity. In fact, a stronger lower bound holds in this case: the $2^n \\times 2^n$ Disjointness Matrix (well-studied in communication complexity) cannot be expressed by a linear combination of $2^{o(n)}$ structured matrices that we call “equality matrices”. • For every $c \\geq 1$ and every $\\varepsilon>0$, Orthogonal Vectors on $n$ vectors in $c \\log n$ dimensions can be solved in $n^{1+\\varepsilon}$ uniform deterministic time. This case would provide a strong refutation of the Orthogonal Vectors conjecture, and of SETH: for example, CNF-SAT on $n$ variables and $O(n)$ clauses could be solved in $2^{n / 2+o(n)}$ time. Moreover, this case would imply non-uniform circuit lower bounds for the class $E^{NP}$, against Valiant series-parallel circuits. Inspired by this connection, we give evidence from SAT/SMT solvers that the first item (in particular, the Disjointness lower bound) may be false in its full generality. In particular, we present a systematic approach to solving Orthogonal Vectors via constant-sized decompositions of the Disjointness Matrix, which already yields interesting new algorithms. For example, using a linear combination of 6 equality matrices that express $2^6 \\times 2^6$ Disjointness, we derive an $\\tilde{O}\\left(n \\cdot 6^{d / 6}\\right) \\leq \\tilde{O}\\left(n \\cdot 1. \\dot{35^d}\\right)$ time and $n \\cdot \\operatorname{poly}(\\log n, d)$ space algorithm for Orthogonal Vectors on $n$ vectors in $d$ dimensions. We show similar results for counting pairs of orthogonal vectors.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00088"}, {"primary_key": "587265", "vector": [], "sparse_vector": [], "title": "A Stronger Bound for Linear 3-LCC.", "authors": ["<PERSON><PERSON>"], "summary": "A q-locally correctable code (LCC) $C:\\{0,1\\}^{k}\\rightarrow \\{0,1\\}^{n}$ is a code in which it is possible to correct every bit of a (not too) corrupted codeword by making at most $q$ queries to the word. The cases in which $q$ is constant are of special interest, and so are the cases that $C$ is linear. In a breakthrough result <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC 2024) showed that for linear 3-LCC $n=2^{\\Omega(k^{1/8})}$. In this work we prove that $n=2^{\\Omega(k^{1/4})}$. As Reed-Muller codes yield 3-LCC with $n=2^{O(k^{1/2})}$, this brings us closer to closing the gap. Moreover, in the special case of design-LCC (into which <PERSON><PERSON><PERSON> fall) the bound we get is $n=2^{\\Omega(k^{1/3})}$.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00109"}, {"primary_key": "587266", "vector": [], "sparse_vector": [], "title": "∏2P vs PSpace Dichotomy for the Quantified Constraint Satisfaction Problem.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Quantified Constraint Satisfaction Problem is the problem of evaluating a sentence with both quantifiers, over relations from some constraint language, with conjunction as the only connective. We show that for any constraint language on a finite domain the Quantified Constraint Satisfaction Problem is either in $\\Pi_{2}^{P}$, or PSpace-complete. Additionally, we build a constraint language on a 6-element domain such that the Quantified Constraint Satisfaction Problem over this language is $\\Pi_{2}^{P}$ -complete.", "published": "2024-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS61266.2024.00043"}]