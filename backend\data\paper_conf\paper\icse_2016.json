[{"primary_key": "4128319", "vector": [], "sparse_vector": [], "title": "Extracting conceptual interoperability constraints from API documentation using machine learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Successfully using a software web-service/platform API requires satisfying its conceptual interoperability constraints that are stated within its shared documentation. However, manual and unguided analysis of text in API documents is a tedious and time consuming task. In this work, we present our empirical-based methodology of using machine learning techniques for automatically identifying conceptual interoperability constraints from natural language text. We also show some initial promising results of our research.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892642"}, {"primary_key": "4128320", "vector": [], "sparse_vector": [], "title": "Code drones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We propose and explore a new paradigm called Code Drones in which every software artifact such as a class is an intelligent and socially active entity. In this paradigm, humanized artifacts take the lead and choreograph (socially, in collaboration with other intelligent software artifacts and humans) automated software engineering solutions to a myriad of development and maintenance challenges, including API migration, reuse, documentation, testing, patching, and refactoring. We discuss the implications of having social and intelligent/cognitive software artifacts that guide their own self-improvement.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889211"}, {"primary_key": "4128327", "vector": [], "sparse_vector": [], "title": "Towards promoting design and UML modeling practices in the open source community.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Despite the emergence of UML as the defacto modeling and design tool for software engineering, its adoption remains dismal. Software development, particularly in the open source community, remains code-centric. Adoption of UML in open source projects represents a significant lost opportunity. In this paper, we present an approach to encourage upfront design practices and the adoption of UML modeling in open source projects. In particular, we demonstrate the approach for small contributions and bug fixes. The approach relies on integrating UML-level abstractions into the code. This integration means that open source developers can continue to use their familiar text-based tools to manage the source code and contributions, while at the same time benefit from UML added value of abstractions and comprehension. Other benefits of this approach include broadening the boundaries of bug fix contribution by including modelers and end-users, and incrementally add UML model diagrams into open source project's documentation.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892649"}, {"primary_key": "4128329", "vector": [], "sparse_vector": [], "title": "Understanding asynchronous interactions in full-stack JavaScript.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "JavaScript has become one of the most popular languages in practice. Developers now use JavaScript not only for the client-side but also for server-side programming, leading to \"full-stack\" applications written entirely in JavaScript. Understanding such applications is challenging for developers, due to the temporal and implicit relations of asynchronous and event-driven entities spread over the client and server side. We propose a technique for capturing a behavioural model of full-stack JavaScript applications' execution. The model is temporal and context-sensitive to accommodate asynchronous events, as well as the scheduling and execution of lifelines of callbacks. We present a visualization of the model to facilitate program understanding for developers. We implement our approach in a tool, called Sahand, and evaluate it through a controlled experiment. The results show that Sahand improves developers' performance in completing program comprehension tasks by increasing their accuracy by a factor of three.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884864"}, {"primary_key": "4128332", "vector": [], "sparse_vector": [], "title": "Metrics in Agile project courses.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We believe that software engineering should be taught in a hands-on way such as through a project-based capstone course where students apply the learned concepts in a real setting. However, such a teaching format can be challenging and time-consuming for instructors.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889183"}, {"primary_key": "4128333", "vector": [], "sparse_vector": [], "title": "Risk-driven revision of requirements models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Requirements incompleteness is often the result of unanticipated adverse conditions which prevent the software and its environment from behaving as expected. These conditions represent risks that can cause severe software failures. The identification and resolution of such risks is therefore a crucial step towards requirements completeness. Obstacle analysis is a goal-driven form of risk analysis that aims at detecting missing conditions that can obstruct goals from being satisfied in a given domain, and resolving them.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884838"}, {"primary_key": "4128334", "vector": [], "sparse_vector": [], "title": "Logic-based learning in software engineering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, research efforts have been directed towards the use of Machine Learning (ML) techniques to support and automate activities such as program repair, specification mining and risk assessment. The focus has largely been on techniques for classification, clustering and regression. Although beneficial, these do not produce a declarative, interpretable representation of the learned information. Hence, they cannot readily be used to inform, revise and elaborate software models. On the other hand, recent advances in ML have witnessed the emergence of new logic-based learning approaches that differ from traditional ML in that their output is represented in a declarative, rule-based manner, making them well-suited for many software engineering tasks.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891050"}, {"primary_key": "4128336", "vector": [], "sparse_vector": [], "title": "Engineering the servo web browser engine using Rust.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "All modern web browsers --- Internet Explorer, Firefox, Chrome, Opera, and Safari --- have a core rendering engine written in C++. This language choice was made because it affords the systems programmer complete control of the underlying hardware features and memory in use, and it provides a transparent compilation model. Unfortunately, this language is complex (especially to new contributors!), challenging to write correct parallel code in, and highly susceptible to memory safety issues that potentially lead to security holes.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889229"}, {"primary_key": "4128338", "vector": [], "sparse_vector": [], "title": "Probing for requirements knowledge to stimulate architectural thinking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software requirements specifications (SRSs) often lack the detail needed to make informed architectural decisions. Architects therefore either make assumptions, which can lead to incorrect decisions, or conduct additional stakeholder interviews, resulting in potential project delays. We previously observed that software architects ask Probing Questions (PQs) to gather information crucial to architectural decision-making. Our goal is to equip Business Analysts with appropriate PQs so that they can ask these questions themselves. We report a new study with over 40 experienced architects to identify reusable PQs for five areas of functionality and organize them into structured flows. These PQ-flows can be used by Business Analysts to elicit and specify architecturally relevant information. Additionally, we leverage machine learning techniques to determine when a PQ-flow is appropriate for use in a project, and to annotate individual PQs with relevant information extracted from the existing SRS. We trained and evaluated our approach on over 8,000 individual requirements from 114 requirements specifications and also conducted a pilot study to validate its usefulness.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884801"}, {"primary_key": "4128339", "vector": [], "sparse_vector": [], "title": "An empirical study of blindness and program comprehension.", "authors": ["Ameer Armaly", "<PERSON>"], "summary": "Blind programmers typically use a screen reader when reading code whereas sighted programmers are able to skim the code with their eyes. This difference has the potential to impact the generalizability of software engineering studies and approaches. We present a summary of a paper which will soon be under review at TSE that investigates how code comprehension of blind programmers differs from that of sighted programmers. Put briefly, we found no statistically-significant differences between the areas of code that the blind programmers found to be important and the areas of code that the sighted programmers found to be important.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891041"}, {"primary_key": "4128340", "vector": [], "sparse_vector": [], "title": "StubDroid: automatic inference of precise data-flow summaries for the android framework.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Smartphone users suffer from insufficient information on how commercial as well as malicious apps handle sensitive data stored on their phones. Automated taint analyses address this problem by allowing users to detect and investigate how applications access and handle this data. A current problem with virtually all those analysis approaches is, though, that they rely on explicit models of the Android runtime library. In most cases, the existence of those models is taken for granted, despite the fact that the models are hard to come by: Given the size and evolution speed of a modern smartphone operating system it is prohibitively expensive to derive models manually from code or documentation.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884816"}, {"primary_key": "4128342", "vector": [], "sparse_vector": [], "title": "Realistic bug triaging.", "authors": ["<PERSON>"], "summary": "The task of assigning a bug report to the developer \"best\" able to address it involves identifying a list of developers qualified to understand and address the bug report and ranking them according to their expertise. Most research in this area addresses this task by matching the description of the bug report and the developers' prior development and bug-fixing activities.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889268"}, {"primary_key": "4128345", "vector": [], "sparse_vector": [], "title": "Microsoft touch develop and the BBC micro: bit.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The chance to influence the lives of a million children does not come often. Through a partnership between the BBC and several technology companies, a small instructional computing device called the BBC micro:bit will be given to a million children in the UK in 2016. Moreover, using the micro:bit will be part of the CS curriculum. We describe how Microsoft's Touch Develop programming platform works with the BBC micro:bit. We describe the design and architecture of the micro:bit and the software engineering hurdles that had to be overcome to ensure it was as accessible as possible to children and teachers. The combined hardware/software platform is evaluated and early anecdotal evidence is presented. A video about the micro:bit is available at http://aka.ms/bbcmicrobit.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889179"}, {"primary_key": "4128349", "vector": [], "sparse_vector": [], "title": "The bones of the system: a case study of logging and telemetry at Microsoft.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large software organizations are transitioning to event data platforms as they culturally shift to better support data-driven decision making. This paper offers a case study at Microsoft during such a transition. Through qualitative interviews of 28 participants, and a quantitative survey of 1,823 respondents, we catalog a diverse set of activities that leverage event data sources, identify challenges in conducting these activities, and describe tensions that emerge in data-driven cultures as event data flow through these activities within the organization. We find that the use of event data span every job role in our interviews and survey, that different perspectives on event data create tensions between roles or teams, and that professionals report social and technical challenges across activities.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889231"}, {"primary_key": "4128350", "vector": [], "sparse_vector": [], "title": "A new homogeneous pure birth process based software reliability model.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "A new Software Reliability model based on a pure birth process is proposed. In our novel approach, the birth (failure) rate of the process is considered to be non dependent on time but dependent non linearly on the previous number of births (failures), contrarily to non homogeneous pure birth processes, as it is usually done in the literature. We use the empirical Bayes framework in order to get the birth (failure) rate. Our approach allows either to estimate the mean time to failure MTTF or to simulate the stochastic failure process. We analyze both, the discrete and continuous time case and apply the first to a real case.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892645"}, {"primary_key": "4128352", "vector": [], "sparse_vector": [], "title": "Security expert recommender in software engineering.", "authors": ["<PERSON><PERSON>"], "summary": "Software engineering is a complex filed with diverse specialties. By the growth of Internet based applications, information security plays an important role in software development process. Finding expert software engineers who have expertise in information security requires too much effort. Stack Overflow is the largest social Q&A Website in the field of software engineering. Stack Overflow contains developers' posts and answers in different software engineering areas including information security. Security related posts are asked in conjunction with various technologies, programming languages, tools and frameworks. In this paper, the content and metadata of Stack Overflow is analysed to find experts in diverse software engineering security related concepts using information security ontology.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892648"}, {"primary_key": "4128356", "vector": [], "sparse_vector": [], "title": "Applying scrum to the army: a case study.", "authors": ["<PERSON>", "<PERSON>ael<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Agile methods are now a mainstream production process and their proponents are no longer considered hackers or cowboy coders. Still, in embedded and safety critical domains there is somehow the expectation that the approach be \"more\" plan-based; at least, many of the current normatives seem to lean toward such approach. A definite change in attitude has emerged with the latest DoD standards in the United States; in Italy, this change has been introduced through a new initiative taken at the 4th Logistic Division of the General Staffs of the Italian Army.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892652"}, {"primary_key": "4128360", "vector": [], "sparse_vector": [], "title": "Efficient large-scale trace checking using mapreduce.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The problem of checking a logged event trace against a temporal logic specification arises in many practical cases. Unfortunately, known algorithms for an expressive logic like MTL (Metric Temporal Logic) do not scale with respect to two crucial dimensions: the length of the trace and the size of the time interval of the formula to be checked. The former issue can be addressed by distributed and parallel trace checking algorithms that can take advantage of modern cloud computing and programming frameworks like MapReduce. Still, the latter issue remains open with current state-of-the-art approaches.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884832"}, {"primary_key": "4128361", "vector": [], "sparse_vector": [], "title": "Mining software process lines.", "authors": ["<PERSON>"], "summary": "There is a vast growth of generated event data being collected and stored by organizations. Within the field of Process Mining, this data has been used to discover, analyze and enhance processes from different domains. For this purpose there are hundreds of techniques available in different tools. These techniques are mostly focused on single processes. On the other hand, there are several proposals for dealing with multiple processes. Under different names, such as: configurable process models, process families or process lines, processes are characterized by capturing commonalities and variability between (similar) process models. These approaches have shown to be useful for organizations that have multiple variants of a given process, e.g., reducing redundancy and maintenance time. In this research proposal, we are developing a framework that allows the use of Process Mining techniques in families of processes within the software development domain (i.e., Software Process Lines).", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889267"}, {"primary_key": "4128362", "vector": [], "sparse_vector": [], "title": "Improving and balancing software qualities.", "authors": ["<PERSON>"], "summary": "This Technical Briefing describes the nature of Software Qualities (SQs), ilities, or non-functional requirements (reliability, usability, affordability, etc.), and discusses the importance of understanding their nature and interrelationships, and of bringing them into balance in the practice of software engineering. The relevance and timeliness of this topic reflects the current and future trends toward more software-intensive systems, with greater complexity, autonomy, speed of change, and need for interoperability within systems of systems, given the frequent system shortfalls and overruns that occur when their SQ balance is not achieved. It discusses the weaknesses of current SQ standards and guidance, and summarizes research toward strengthening current SQ definitions and relationships. This includes a set of initial SQ ontology elements and relationships, examples of their application to some key SQs, an identification of further research and development needed to make the ontology fully useful and evolvable, and the nature of an international collaborative effort to help improve current practices via a Qualipedia for accessing the evolving body of knowledge for improving SQ engineering.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891049"}, {"primary_key": "4128365", "vector": [], "sparse_vector": [], "title": "Testing the untestable: model testing of complex software-intensive systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Increasingly, we are faced with systems that are untestable, meaning that traditional testing methods are expensive, time-consuming or infeasible to apply due to factors such as the systems' continuous interactions with the environment and the deep intertwining of software with hardware.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889212"}, {"primary_key": "4128367", "vector": [], "sparse_vector": [], "title": "Behavioral log analysis with statistical guarantees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Scalability is a major challenge for existing behavioral log analysis algorithms, which extract finite-state automaton models or temporal properties from logs generated by running systems. In this paper we present statistical log analysis, which addresses scalability using statistical tools. The key to our approach is to consider behavioral log analysis as a statistical experiment. Rather than analyzing the entire log, we suggest to analyze only a sample of traces from the log and, most importantly, provide means to compute statistical guarantees for the correctness of the analysis result.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884805"}, {"primary_key": "4128369", "vector": [], "sparse_vector": [], "title": "Analysing the program analyser.", "authors": ["<PERSON><PERSON><PERSON>", "Alastair F<PERSON>"], "summary": "The reliability of program analysis tools is clearly important if such tools are to play a serious role in improving the quality and integrity of software systems, and the confidence which users place in such systems. Yet our experience is that, currently, little attention is paid to analysing the correctness of program analysers themselves, beyond regression testing. In this position paper we present our vision that, by 2025, the use of more rigorous analyses to check the reliability of program analysers will be commonplace. Inspired by recent advances in compiler testing, we set out initial steps towards this vision, building upon techniques such as cross-checking, program transformation and program generation.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889206"}, {"primary_key": "4128370", "vector": [], "sparse_vector": [], "title": "Code parallelization through sequential code search.", "authors": ["<PERSON>"], "summary": "In this paper, we propose a new technique to recommend programmers with high quality parallel code that are similar to a given sequential code. This is done by transforming well-grounded parallel code A into its sequential equivalent B, storing them (A->B) into database, given a sequential code C, search the database for syntactic or semantic similar code B and retrieve its parallel version code A, which can be used as the replacement or reference for the original code C. We also outline our solutions towards realizing this technique and present a preliminary study that shows promising results.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891045"}, {"primary_key": "4128371", "vector": [], "sparse_vector": [], "title": "Fixing deadlocks via lock pre-acquisitions.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Manual deadlock fixing is error-prone and time-consuming. Existing generic approach (GA) simply inserts gate locks to fix deadlocks by serializing executions, which could introduce various new deadlocks and incur high runtime overhead. We propose a novel approach DFixer to fix deadlocks without introducing any new deadlocks by design. DFixer only selects one thread of a deadlock to pre-acquire a lock w together with another lock h, where before fixing, the deadlock occurs when the thread holds lock h and waits for lock w. As such, DFixer eliminates a hold-and-wait necessary condition, preventing the deadlock from occurring. The thread performing pre-acquisition is carefully selected such that no other synchronization exists in between the two original acquisitions. Otherwise, DFixer further introduces a context-aware conditional protected by above lock w to guarantee the correctness of DFixer. The evaluation is on 20 deadlocks, including 17 from widely-used real-world C/C++ programs. It shows that DFixer successfully fixed all deadlocks. Whereas GA introduced 9 new deadlocks; a latest work Grail failed to fix 8 deadlocks and introduced 3 new deadlocks on others. On average, DFixer incurred only 2.1% overhead, where GA and Grail incurred 15.8% and 11.5% overhead, respectively.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884819"}, {"primary_key": "4128373", "vector": [], "sparse_vector": [], "title": "Reflections on applying constructive alignment with formative feedback for teaching introductory programming and software architecture.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Constructive alignment is a student-centred approach to teaching and learning that aims to enhance student learning through a combination of constructivist learning theories and aligned curriculum. This paper presents two case studies where units have been developed to apply the principles of constructive alignment in the area of computer science and software engineering. It outlines the role of formative feedback and delayed summative assessment as a means of embedding constructivist learning theories in the application of constructive alignment. The discussion outlines some of the challenges and advantages gained from the greater focus on formative feedback during the teaching period, and presents some recommendations for others considering applying constructive alignment.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889185"}, {"primary_key": "4128378", "vector": [], "sparse_vector": [], "title": "SMACK software verification toolchain.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Tool prototyping is an essential step in developing novel software verification algorithms and techniques. However, implementing a verifier prototype that can handle real-world programs is a huge endeavor, which hinders researchers by forcing them to spend more time engineering tools, and less time innovating. In this paper, we present the SMACK software verification toolchain. The toolchain provides a modular and extensible software verification ecosystem that decouples the front-end source language details from backend verification algorithms. It achieves that by translating from the LLVM compiler intermediate representation into the Boogie intermediate verification language. SMACK benefits the software verification community in several ways: (i) it can be used as an off-the-shelf software verifier in an applied software verification project, (ii) it enables researchers to rapidly develop and release new verification algorithms, (iii) it allows for adding support for new languages in its front-end. We have used SMACK to verify numerous C/C++ programs, including industry examples, showing it is mature and competitive. Likewise, SMACK is already being used in several existing verification research prototypes. Our demonstration of SMACK can be found on YouTube at the following address: https://youtu.be/SPPSC1KdRzs", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889163"}, {"primary_key": "4128379", "vector": [], "sparse_vector": [], "title": "Context-sensitive identification of refactoring opportunities.", "authors": ["<PERSON>"], "summary": "Refactoring is a popular procedure for improving the internal structure of software systems. Refactoring is widely practiced by developers, and considerable development effort has been invested in refactoring tooling support. The identification of refactoring opportunities usually is based on subjective perceptions. At best, identification of refactoring opportunities is based on observation of code smells, which is also subjective and error-prone. As a consequence, developers struggle to identify when large systems should be refactored and, once the identification is complete, they still need to choose the types of refactoring to be performed. To overcome these problems, this PhD research aims at: (i) improving the state-of-the-art by introducing a novel model for identifying refactoring opportunities; and (ii) indicating which types of refactorings should be performed in order to improve the code internal structure.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889266"}, {"primary_key": "4128380", "vector": [], "sparse_vector": [], "title": "Smart decisions: an architectural design game.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Architecture design is notoriously difficult to teach and to learn. Most competent architects in industry have deep knowledge won from long years of experience. But if we want architecture design to be methodical and repeatable, we need better methods for teaching it. Simply waiting for an aspiring architect to accumulate 10 or 20 years of experience is not acceptable if we believe that software engineering is a true engineering discipline. In this paper we describe our experiences with the development of a game that aids in teaching architecture design, specifically design employing the Attribute-Driven Design method. We discuss our approach to creating the game, and the \"design concepts catalog\" that provides the knowledge base for the game. Finally, we report on our experiences with deploying the game, and the (enthusiastic) assessments and feedback that we have received from industrial and academic participants.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889184"}, {"primary_key": "4128382", "vector": [], "sparse_vector": [], "title": "On the reduction of verbose queries in text retrieval based software maintenance.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We argue that verbose queries used for software retrieval contain many terms that follow specific discourse rules, yet hinder retrieval. We report the results of an empirical study on the effect of removing such terms from verbose queries in the context of Text Retrieval-based concept location. In the study, we remove terms from 424 queries, generated from bug reports of nine open source systems. Removing the terms leads to substantial improvement in retrieval: 73% of the queries are improved, leading to 21.8% and 13.4% gain in terms of MRR and MAP, respectively. Such improvement is larger than that of many more sophisticated state-of-the-art approaches. The results show promise and the future challenge lies with automatically identifying the terms to be removed from the verbose queries.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892647"}, {"primary_key": "4128386", "vector": [], "sparse_vector": [], "title": "An empirical comparison of compiler testing techniques.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Hu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Compilers, as one of the most important infrastructure of today's digital world, are expected to be trustworthy. Different testing techniques are developed for testing compilers automatically. However, it is unknown so far how these testing techniques compared to each other in terms of testing effectiveness: how many bugs a testing technique can find within a time limit.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884878"}, {"primary_key": "4128387", "vector": [], "sparse_vector": [], "title": "PAC learning-based verification and model synthesis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tsung<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a novel technique for verification and model synthesis of sequential programs. Our technique is based on learning an approximate regular model of the set of feasible paths in a program, and testing whether this model contains an incorrect behavior. Exact learning algorithms require checking equivalence between the model and the program, which is a difficult problem, in general undecidable. Our learning procedure is therefore based on the framework of probably approximately correct (PAC) learning, which uses sampling instead, and provides correctness guarantees expressed using the terms error probability and confidence. Besides the verification result, our procedure also outputs the model with the said correctness guarantees. Obtained preliminary experiments show encouraging results, in some cases even outperforming mature software verifiers.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884860"}, {"primary_key": "4128390", "vector": [], "sparse_vector": [], "title": "Generating performance distributions via probabilistic symbolic execution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Analyzing performance and understanding the potential best-case, worst-case and distribution of program execution times are very important software engineering tasks. There have been model-based and program analysis-based approaches for performance analysis. Model-based approaches rely on analytical or design models derived from mathematical theories or software architecture abstraction, which are typically coarse-grained and could be imprecise. Program analysis-based approaches collect program profiles to identify performance bottlenecks, which often fail to capture the overall program performance. In this paper, we propose a performance analysis framework PerfPlotter. It takes the program source code and usage profile as inputs and generates a performance distribution that captures the input probability distribution over execution times for the program. It heuristically explores high-probability and low-probability paths through probabilistic symbolic execution. Once a path is explored, it generates and runs a set of test inputs to model the performance of the path. Finally, it constructs the performance distribution for the program. We have implemented PerfPlotter based on the Symbolic PathFinder infrastructure, and experimentally demonstrated that PerfPlotter could accurately capture the best-case, worst-case and distribution of program execution times. We also show that performance distributions can be applied to various important tasks such as performance understanding, bug validation, and algorithm selection.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884794"}, {"primary_key": "4128391", "vector": [], "sparse_vector": [], "title": "Detecting problems in the database access code of large scale systems: an industrial experience report.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Database management systems (DBMSs) are one of the most important components in modern large-scale systems. Thus, it is important for developers to write code that can access DBMS correctly and efficiently. Since the behaviour of database access code can sometimes be a blackbox for developers, writing good test cases to capture problems in database access code can be very difficult. In addition to testing, static bug detection tools are often used to detect problems in the code. However, existing bug detection tools usually fail to detect functional and performance problems in the database access code. In this paper, we document our industrial experience over the past few years on finding bug patterns of database access code, implementing a bug detection tool, and integrating the tool into daily practice. We discuss the challenges that we encountered and the day-to-day lessons that we learned during integrating our tool into the development processes. Since most systems nowadays are leveraging frameworks, we also provide a detailed discussion of five framework-specific database access bug patterns that we found. We hope to encourage further research efforts on framework-specific detectors, instead of the current research focus on general programming language bug patterns and associated detectors.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889228"}, {"primary_key": "4128392", "vector": [], "sparse_vector": [], "title": "Toward arbitrary mapping for debugging visualizations.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Despite the progress that has been made in the field of program visualization, programmers nowadays still rely on inserting extra code (e.g. print statements) to visualize complicated program states during debugging. There are many obstacles that have impeded and continue to impede program visualization for practical use. One such major obstacle is that a wide variety of data types and interpretations from data to visualizations are arbitrary. It is unlikely that visualizations will be available a priori to cover everything that might be of interest. In an attempt to address the problem, a debugging visualization tool called xDIVA is presented here. A practical application of xDIVA in EDA (Electronic Design Automation) industry is described. Demonstrations and tool download can be accessed at \"http://oolab.csie.ncu.edu.tw/xDIVA\".", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889167"}, {"primary_key": "4128393", "vector": [], "sparse_vector": [], "title": "CUSTODES: automatic spreadsheet cell clustering and smell detection using strong and weak features.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Various techniques have been proposed to detect smells in spreadsheets, which are susceptible to errors. These techniques typically detect spreadsheet smells through a mechanism based on a fixed set of patterns or metric thresholds. Unlike conventional programs, tabulation styles vary greatly across spreadsheets. Smell detection based on fixed patterns or metric thresholds, which are insensitive to the varying tabulation styles, can miss many smells in one spreadsheet while reporting many spurious smells in another. In this paper, we propose CUSTODES to effectively cluster spreadsheet cells and detect smells in these clusters. The clustering mechanism can automatically adapt to the tabulation styles of each spreadsheet using strong and weak features. These strong and weak features capture the invariant and variant parts of tabulation styles, respectively. As smelly cells in a spreadsheet normally occur in minority, they can be mechanically detected as clusters' outliers in feature spaces. We implemented and applied CUSTODES to 70 spreadsheets files randomly sampled from the EUSES corpus. These spreadsheets contain 1,610 formula cell clusters. Experimental results confirmed that CUSTODES is effective. It successfully detected harmful smells that can induce computation anomalies in spreadsheets with an F-measure of 0.72, outperforming state-of-the-art techniques.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884796"}, {"primary_key": "4128394", "vector": [], "sparse_vector": [], "title": "Sustainability design in requirements engineering: state of practice.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sustainability is now a major concern in society, but there is little understanding of how it is perceived by software engineering professionals and how sustainability design can become an embedded part of software engineering process. This paper presents the results of a qualitative study exploring requirements engineering practitioners' perceptions and attitudes towards sustainability. It identifies obstacles and mitigation strategies regarding the application of sustainability design principles in daily work life. The results of this study reveal several factors that can prevent sustainability design from becoming a first class citizen in software engineering: software practitioners tend to have a narrow understanding of the concept of sustainability; organizations show limited awareness of its potential opportunities and benefits; and the norms in the discipline are not conducive to sustainable outcomes. These findings suggest the need for focused efforts in sustainability education, but also a need to rethink professional norms and practices.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889217"}, {"primary_key": "4128396", "vector": [], "sparse_vector": [], "title": "Guiding dynamic symbolic execution toward unverified program executions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Most techniques to detect program errors, such as testing, code reviews, and static program analysis, do not fully verify all possible executions of a program. They leave executions unverified when they do not check certain properties, fail to verify properties, or check properties under certain unsound assumptions such as the absence of arithmetic overflow.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884843"}, {"primary_key": "4128398", "vector": [], "sparse_vector": [], "title": "Prodirect manipulation: bidirectional programming for the masses.", "authors": ["<PERSON>"], "summary": "Software interfaces today generally fall at either end of a spectrum. On one end are programmable systems, which allow expert users (i.e. programmers) to write software artifacts that describe complex abstractions, but programs are disconnected from their eventual output. On the other end are domain-specific graphical user interfaces (GUIs), which allow end users (i.e. non-programmers) to easily create varied content but present insurmountable walls when a desired feature is not built-in. Both programmatic and direct manipulation have distinct strengths, but users must typically choose one over the other or use some ad-hoc combination of systems. Our goal, put simply, is to bridge this divide.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889210"}, {"primary_key": "4128400", "vector": [], "sparse_vector": [], "title": "A guided tour of the legal implications of software cloning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Software Cloning is the typical example where an interdisciplinary approach may bring additional elements into the community's discussion. In fact, little research has been done in its analysis from an Intellectual Propriety Rights (IPRs) perspective, even if it is a widely studied aspect of software engineering. An interdisciplinary approach is crucial to better understand the legal implications of software in the IPR context. Interestingly, the academic community of software and systems deals much more with such IPR issues than courts themselves. In this paper, we analyze some recent legal decisions in using software clones from a software engineering perspective. In particular, we survey the behavior of some major courts about cloning issues. As a major outcome of our research, it seems that legal fora do not have major concerns regarding copyright infringements in software cloning. The major contribution of this work is a case by case analysis of more than one hundred judgments by the US courts and the European Court of Justice. We compare the US and European courts case laws and discuss the impact of a recent European ruling. The US and EU contexts are quite different, since in the US software is patentable while in the EU it is not. Hence, European courts look more permissive regarding cloning, since \"principles,\" or \"ideas,\" are not copyrightable by themselves.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889220"}, {"primary_key": "4128401", "vector": [], "sparse_vector": [], "title": "Using docker containers to improve reproducibility in software engineering research.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The ability to replicate and reproduce scientific results has become an increasingly important topic for many academic disciplines. In computer science and, more specifically, software engineering, contributions of scientific work rely on developed algorithms, tools and prototypes, quantitative evaluations, and other computational analyses. Published code and data come with many undocumented assumptions, dependencies, and configurations that are internal knowledge and make reproducibility hard to achieve. This technical briefing presents how Docker containers can overcome these issues and aid the reproducibility of research artifacts in software engineering and discusses their applications in the field.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891057"}, {"primary_key": "4128403", "vector": [], "sparse_vector": [], "title": "Exploring language support for immutability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Programming languages can restrict state change by preventing it entirely (immutability) or by restricting which clients may modify state (read-only restrictions). The benefits of immutability and read-only restrictions in software structures have been long-argued by practicing software engineers, researchers, and programming language designers. However, there are many proposals for language mechanisms for restricting state change, with a remarkable diversity of techniques and goals, and there is little empirical data regarding what practicing software engineers want in their tools and what would benefit them. We systematized the large collection of techniques used by programming languages to help programmers prevent undesired changes in state. We interviewed expert software engineers to discover their expectations and requirements, and found that important requirements, such as expressing immutability constraints, were not reflected in features available in the languages participants used. The interview results informed our design of a new language extension for specifying immutability in Java. Through an iterative, participatory design process, we created a tool that reflects requirements from both our interviews and the research literature.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884798"}, {"primary_key": "4128404", "vector": [], "sparse_vector": [], "title": "Technical debt prioritization using predictive analytics.", "authors": ["Zadia Codabux", "<PERSON>"], "summary": "Recently, Technical Debt (TD) has gained popularity in the Software Engineering community to describe design decisions that allow software development teams to achieve short term benefits such as expedited release of code. Technical debt accrued should be managed to avoid the disastrous consequences of these temporary workarounds. Management of technical debt involve documenting the debt item in the backlog including some type of quantification in terms of person-hours or story points for example. Subsequently, the debt items are prioritized and addressed. Developers or project managers face problems to decide which debt is higher priority or more \"problematic\" and which one needs to be addressed first. This decision-making process is not standardized and is currently context dependent in most organizations. This paper bridge this gap by proposing a framework which makes use of a plethora of techniques ranging from data mining to prediction and decision models that project managers can use in their decision-making process to determine which technical debt is more critical and should be addressed first.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892643"}, {"primary_key": "4128409", "vector": [], "sparse_vector": [], "title": "Using data provenance to improve software process enactment, monitoring and analysis.", "authors": ["<PERSON><PERSON>"], "summary": "A practice to support software processes continuous improvement is to reuse the knowledge acquired in previous executions. One way to capture process execution data is by using data provenance models. Data provenance refers to the origin, lineage or source of data. In computational terms, provenance is a historical record of the derivation of data that can help in its understanding. But, does the provenance data be used to contribute to the software process improvement? Based on this question, the approach proposed in this work aims to apply data provenance to support software process execution, monitoring and analysis phases as well as software process improvement as a whole. To achieve this goal, data provenance, ontology and predefined metrics were used in a pilot case study, considering software processes used in two real software development companies. With this study, two types of implicit information were derived: (1) information about artifacts that can increase new process instances runtime, and (2) information related to new agents that can be added to execute a task and contribute to the reduction of the task runtime.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889255"}, {"primary_key": "4128411", "vector": [], "sparse_vector": [], "title": "RETracer: triaging crashes by reverse execution from partial memory dumps.", "authors": ["Weidong Cui", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many software providers operate crash reporting services to automatically collect crashes from millions of customers and file bug reports. Precisely triaging crashes is necessary and important for software providers because the millions of crashes that may be reported every day are critical in identifying high impact bugs. However, the triaging accuracy of existing systems is limited, as they rely only on the syntactic information of the stack trace at the moment of a crash without analyzing program semantics.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884844"}, {"primary_key": "4128416", "vector": [], "sparse_vector": [], "title": "Program synthesis using natural language.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Interacting with computers is a ubiquitous activity for millions of people. Repetitive or specialized tasks often require creation of small, often one-off, programs. End-users struggle with learning and using the myriad of domain-specific languages (DSLs) to effectively accomplish these tasks.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884786"}, {"primary_key": "4128418", "vector": [], "sparse_vector": [], "title": "Belief &amp; evidence in empirical software engineering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Empirical software engineering has produced a steady stream of evidence-based results concerning the factors that affect important outcomes such as cost, quality, and interval. However, programmers often also have strongly-held a priori opinions about these issues. These opinions are important, since developers are highly-trained professionals whose beliefs would doubtless affect their practice. As in evidence-based medicine, disseminating empirical findings to developers is a key step in ensuring that the findings impact practice. In this paper, we describe a case study, on the prior beliefs of developers at Microsoft, and the relationship of these beliefs to actual empirical data on the projects in which these developers work. Our findings are that a) programmers do indeed have very strong beliefs on certain topics b) their beliefs are primarily formed based on personal experience, rather than on findings in empirical research and c) beliefs can vary with each project, but do not necessarily correspond with actual evidence in that project. Our findings suggest that more effort should be taken to disseminate empirical findings to developers and that more in-depth study the interplay of belief and evidence in software practice is needed.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884812"}, {"primary_key": "4128419", "vector": [], "sparse_vector": [], "title": "Featured model-based mutation analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Model-based mutation analysis is a powerful but expensive testing technique. We tackle its high computation cost by proposing an optimization technique that drastically speeds up the mutant execution process. Central to this approach is the Featured Mutant Model, a modelling framework for mutation analysis inspired by the software product line paradigm. It uses behavioural variability models, viz., Featured Transition Systems, which enable the optimized generation, configuration and execution of mutants. We provide results, based on models with thousands of transitions, suggesting that our technique is fast and scalable. We found that it outperforms previous approaches by several orders of magnitude and that it makes higher-order mutation practically applicable.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884821"}, {"primary_key": "4128420", "vector": [], "sparse_vector": [], "title": "Type-aware concolic testing of JavaScript programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Conventional concolic testing has been used to provide high coverage of paths in statically typed languages. While it has also been applied in the context of JavaScript (JS) programs, we observe that applying concolic testing to dynamically-typed JS programs involves tackling unique problems to ensure scalability. In particular, a naive type-agnostic extension of concolic testing to JS programs causes generation of large number of inputs. Consequently, many executions operate on undefined values and repeatedly explore same paths resulting in redundant tests, thus diminishing the scalability of testing drastically.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884859"}, {"primary_key": "4128422", "vector": [], "sparse_vector": [], "title": "COPE: vision for a change-oriented programming environment.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Software engineering involves a lot of change as code artifacts are not only created once but maintained over time. In the last 25 years, major paradigms of program development have arisen -- agile development with refactorings, software product lines, moving sequential code to multicore or cloud, etc. Each is centered on particular kinds of change; their conceptual foundations rely on transformations that (semi-) automate these changes.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889208"}, {"primary_key": "4128426", "vector": [], "sparse_vector": [], "title": "VEnron: a versioned spreadsheet corpus and related evolution analysis.", "authors": ["Wensheng Dou", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Chushu <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Like most conventional software, spreadsheets are subject to software evolution. However, spreadsheet evolution is rarely assisted by version management tools. As a result, the version information across evolved spreadsheets is often missing or highly fragmented. This makes it difficult for users to notice the evolution issues arising from their spreadsheets.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889238"}, {"primary_key": "4128429", "vector": [], "sparse_vector": [], "title": "Trustworthiness in enterprise crowdsourcing: a taxonomy &amp; evidence from data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we study the trustworthiness of the crowd for crowdsourced software development. Through the study of literature from various domains, we present the risks that impact the trustworthiness in an enterprise context. We survey known techniques to mitigate these risks. We also analyze key metrics from multiple years of empirical data of actual crowdsourced software development tasks from two leading vendors. We present the metrics around untrustworthy behavior and the performance of certain mitigation techniques. Our study and results can serve as guidelines for crowdsourced enterprise software development.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889225"}, {"primary_key": "4128431", "vector": [], "sparse_vector": [], "title": "Are &quot;non-functional&quot; requirements really non-functional?: an investigation of non-functional requirements in practice.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Non-functional requirements (NFRs) are commonly distinguished from functional requirements by differentiating how the system shall do something in contrast to what the system shall do. This distinction is not only prevalent in research, but also influences how requirements are handled in practice. NFRs are usually documented separately from functional requirements, without quantitative measures, and with relatively vague descriptions. As a result, they remain difficult to analyze and test. Several authors argue, however, that many so-called NFRs actually describe behavioral properties and may be treated the same way as functional requirements. In this paper, we empirically investigate this point of view and aim to increase our understanding on the nature of NFRs addressing system properties. We report on the classification of 530 NFRs extracted from 11 industrial requirements specifications and analyze to which extent these NFRs describe system behavior. Our results suggest that most \"non-functional\" requirements are not non-functional as they describe behavior of a system. Consequently, we argue that many so-called NFRs can be handled similarly to functional requirements.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884788"}, {"primary_key": "4128432", "vector": [], "sparse_vector": [], "title": "Locking discipline inference and checking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Concurrency is a requirement for much modern software, but the implementation of multithreaded algorithms comes at the risk of errors such as data races. Programmers can prevent data races by documenting and obeying a locking discipline, which indicates which locks must be held in order to access which data.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884882"}, {"primary_key": "4128433", "vector": [], "sparse_vector": [], "title": "CloudBuild: Microsoft&apos;s distributed and caching build service.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Thousands of Microsoft engineers build and test hundreds of software products several times a day. It is essential that this continuous integration scales, guarantees short feedback cycles, and functions reliably with minimal human intervention. This paper describes CloudBuild, the build service infrastructure developed within Microsoft over the last few years. CloudBuild is responsible for all aspects of a continuous integration workflow, including builds, test and code analysis, as well as drops, package and symbol creation and storage. CloudBuild supports multiple build languages as long as they fulfill a coarse grained, file IO based contract. CloudBuild uses content based caching to run build-related tasks only when needed. Lastly, it builds on many machines in parallel. CloudBuild offers a reliable build service in the presence of unreliable components. It aims to rapidly onboard teams and hence has to support non-deterministic build tools and specification languages that under-declare dependencies. We will outline how we addressed these challenges and characterize the operations of CloudBuild. CloudBuild has on-boarded hundreds of codebases with only man-months of effort each. Some of these codebases are used by thousands of developers. The speed ups of build and test range from 1.3× to 10×, and service availability is 99%.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889222"}, {"primary_key": "4128438", "vector": [], "sparse_vector": [], "title": "Crowdsourcing program preconditions via a classification game.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON> Jr."], "summary": "Invariant discovery is one of the central problems in software verification. This paper reports on an approach that addresses this problem in a novel way; it crowdsources logical expressions for likely invariants by turning invariant discovery into a computer game. The game, called Binary Fission, employs a classification model. In it, players compose preconditions by separating program states that preserve or violate program assertions. The players have no special expertise in formal methods or programming, and are not specifically aware they are solving verification tasks. We show that Binary Fission players discover concise, general, novel, and human readable program preconditions. Our proof of concept suggests that crowdsourcing offers a feasible and promising path towards the practical application of verification technology.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884865"}, {"primary_key": "4128440", "vector": [], "sparse_vector": [], "title": "Student experiences using GitHub in software engineering courses: a case study.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "GitHub has been embraced by the software development community as an important social platform for managing software projects and to support collaborative development. More recently, educators have begun to adopt it for hosting course content and student assignments. From our previous research, we found that educators leverage GitHub's collaboration and transparency features to create, reuse and remix course materials, and to encourage student contributions and monitor student activity on assignments and projects. However, our previous research did not consider the student perspective.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889195"}, {"primary_key": "4128443", "vector": [], "sparse_vector": [], "title": "Values-first SE: research principles in practice.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The realization that software has a far reaching impact on politics, society and the environment is not new. However, only recently software impact has been explicitly described as 'systemic' and framed around complex social problems such as sustainability. We argue that 'wicked' social problems are consequences of the interplay between complex economical, technical and political interactions and their underlying value choices. Such choices are guided by specific sets of human values that have been found in all cultures by extensive evidence-based research. The aim of this paper is to give more visibility to the interrelationship between values and SE choices. To this end, we first introduce the concept of Values-First SE and reflect on its implications for software development. Our contribution to SE is embedding the principles of values research in the SE decision making process and extracting lessons learned from practice.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889219"}, {"primary_key": "4128444", "vector": [], "sparse_vector": [], "title": "Control theory for software engineering: technical briefing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Pervasiveness and complexity of modern software are challenging engineers to design applications able to guarantee the desired quality of service despite unpredictable runtime variations in their execution environment. A variety of techniques have been proposed in the last year for the design of self-adaptive applications; however, most of them is tailored to specific applications or can provide limited guarantees of effectiveness and dependability.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891058"}, {"primary_key": "4128445", "vector": [], "sparse_vector": [], "title": "Software engineering and policy.", "authors": ["<PERSON>"], "summary": "In this short position paper I consider the contributions that software engineering as a discipline can make to the development and implementation of government policy. It is intended to support the growing body of knowledge on scientific advice in government and to encourage software engineers to engage with policy and the policy community.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889215"}, {"primary_key": "4128446", "vector": [], "sparse_vector": [], "title": "Reducing the test effort of variability-rich systems by using feature interaction knowledge and variability-aware source code analysis.", "authors": ["<PERSON>"], "summary": "To keep up with the growing demand for customized software solutions that are tailored to specific customer requirements, techniques like Software Product Line Engineering (SPLE) or the more ad-hoc clone-and-own (where engineers do not build each product from anew, but instead maximize the reuse of the available assets in building product families) have been devised. However testing such highly variable software systems has proven challenging. To improve this, the goal of this doctoral research plan is to propose an automated approach to reduce the effort for testing, while improving its effectiveness. The proposed approach aims to detect code parts that have to be considered when devising a test suite by employing source code analysis and test execution monitoring, which we argue have not been used to their full potential in this context.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889257"}, {"primary_key": "4128448", "vector": [], "sparse_vector": [], "title": "TASSAL: autofolding for source code summarization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a novel tool, TASSAL, that automatically creates a summary of each source file in a project by folding its least salient code regions. The intended use-case for our tool is the first-look problem: to help developers who are unfamiliar with a new codebase and are attempting to understand it. TASSAL is intended to aid developers in this task by folding away less informative regions of code and allowing them to focus their efforts on the most informative ones. While modern code editors do provide code folding to selectively hide blocks of code, it is impractical to use as folding decisions must be made manually or based on simple rules. We find through a case study that TASSAL is strongly preferred by experienced developers over simple folding baselines, demonstrating its usefulness. In short, we strongly believe TASSAL can aid program comprehension by turning code folding into a usable and valuable tool. A video highlighting the main features of TASSAL can be found at https://youtu.be/_yu7JZgiBA4.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889171"}, {"primary_key": "4128450", "vector": [], "sparse_vector": [], "title": "Software release planning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "One of the most critical activities in software product development is the decisional process that assigns features to subsequent releases under technical, resource, risk, and budget constraints. This decision-centric process is referred to as software release planning (SRP). This briefing will expose a state of the art on SRP. A survey of the most relevant approaches will be presented. Emphasis will be made on their applicability (concerning e.g. type of development process - being more predictive versus more adaptive, type of system - commercial, open source product or mobile app), tool support and degree of validation in industry. One of these approaches, EVOLVE, will be analysed in detail.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891051"}, {"primary_key": "4128451", "vector": [], "sparse_vector": [], "title": "Risk assessment in open source systems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Adopting Open Source Software (OSS) components offers many advantages to organizations but also introduces risks related to the intrinsic fluidity of the OSS development projects. Choosing the right components is a critical decision, as it could contribute to the success of any adoption process. Making the right decision requires to evaluate the technical capabilities of the components and also related strategic aspects, including possible impacts on high level objectives. This can be achieved through a portfolio of risk assessment and mitigation methods. In this briefing we introduce the basic concepts related to OSS ecosystems and to risk representation and reasoning. We illustrate how risk management activities in OSS can benefit from the large amount of data available from OSS repositories and how they can be connected to business goals for strategic decision-making. The concepts are illustrated with a software platform developed in the context of the EU FP7 project RISCOSS.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891052"}, {"primary_key": "4128458", "vector": [], "sparse_vector": [], "title": "What makes teaching software architecture difficult?", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The software architecture is usually the first design artifact that addresses quality issues (e.g., performance, security). Also, the architecture is reference point for other development activities, e.g., coding and maintenance. Based on our experience teaching software engineering and architecture at different institutions and levels, we discuss what makes teaching software architecture difficult, and how teaching architecture differs from teaching other software engineering topics. Our discussions can help educators design and improve software architecture curricula, and support education researchers in investigating pedagogical approaches and tools for better software architecture training.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889187"}, {"primary_key": "4128466", "vector": [], "sparse_vector": [], "title": "Impact of CS programs on the quality of test cases generation: an empirical study.", "authors": ["<PERSON>", "Sira <PERSON>", "<PERSON>"], "summary": "Background: Although most Computer Science (CS) programs offered by higher education institutions usually include a software engineering course, some works report a lack of formal training in software testing. Aim: With the aim of studying the possible impact of knowledge acquired from CS programs on software testing, this paper reports an investigation composed of four experiments. The experiments conducted in Spain, Mexico and Ecuador examine the quality of test cases (TC) generated using black-box and white-box methods. The subjects of the experiments were undergraduate and graduate students who were exposed to different levels of CS knowledge. Method: We pool together the data from the four experiments and apply logistic regression to investigate possible relations of the quality of test cases with students' level of exposure to CS knowledge. Results: The quality of test cases generated by students depend significantly on the amount of CS program studied. The odds of generating test cases that reveal failures against those that do not reveal decrease when students are exposed to a low level of CS knowledge. Conclusions: Software testing plays a key role in what is an increasingly complex process of developing and maintaining software products today. The results of our empirical study provide evidence in favor of greater formal training in software testing as part of CS programs.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889190"}, {"primary_key": "4128467", "vector": [], "sparse_vector": [], "title": "On the limits of mutation reduction strategies.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although mutation analysis is considered the best way to evaluate the effectiveness of a test suite, hefty computational cost often limits its use. To address this problem, various mutation reduction strategies have been proposed, all seeking to reduce the number of mutants while maintaining the representativeness of an exhaustive mutation analysis. While research has focused on the reduction achieved, the effectiveness of these strategies in selecting representative mutants, and the limits in doing so have not been investigated, either theoretically or empirically.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884787"}, {"primary_key": "4128468", "vector": [], "sparse_vector": [], "title": "Topsy-Turvy: a smarter and faster parallelization of mutation analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mutation analysis is an effective, if computationally expensive, technique that allows practitioners to accurately evaluate the quality of their test suites. To reduce the time and cost of mutation analysis, researchers have looked at parallelizing mutation runs --- running multiple mutated versions of the program in parallel, and running through the tests in sequence on each mutated program until a bug is found. While an improvement over sequential execution of mutants and tests, this technique carries a significant overhead cost due to its redundant execution of unchanged code paths. In this paper we propose a novel technique (and its implementation) which parallelizes the test runs rather than the mutants, forking mutants from a single program execution at the point of invocation, which reduces redundancy. We show that our technique can lead to significant efficiency improvements and cost reductions.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": ""}, {"primary_key": "4128469", "vector": [], "sparse_vector": [], "title": "Work practices and challenges in pull-based development: the contributor&apos;s perspective.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The pull-based development model is an emerging way of contributing to distributed software projects that is gaining enormous popularity within the open source software (OSS) world. Previous work has examined this model by focusing on projects and their owners---we complement it by examining the work practices of project contributors and the challenges they face.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884826"}, {"primary_key": "4128474", "vector": [], "sparse_vector": [], "title": "Software analytics: challenges and opportunities.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, software development projects produce a large number of software artifacts including source code, execution traces, end-user feedback, as well as informal documentation such as developers' discussions, change logs, Stack-Overflow, and code reviews. Such data embeds rich and significant knowledge about software projects, their quality and services, as well as the dynamics of software development.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891055"}, {"primary_key": "4128475", "vector": [], "sparse_vector": [], "title": "BigDebug: debugging primitives for interactive big data processing in spark.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON> Tetali", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Developers use cloud computing platforms to process a large quantity of data in parallel when developing big data analytics. Debugging the massive parallel computations that run in today's datacenters is time consuming and error-prone. To address this challenge, we design a set of interactive, real-time debugging primitives for big data processing in Apache Spark, the next generation data-intensive scalable cloud computing platform. This requires rethinking the notion of step-through debugging in a traditional debugger such as gdb, because pausing the entire computation across distributed worker nodes causes significant delay and naively inspecting millions of records using a watchpoint is too time consuming for an end user.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884813"}, {"primary_key": "4128476", "vector": [], "sparse_vector": [], "title": "Ontology learning and its application in software-intensive projects.", "authors": ["<PERSON>", "<PERSON>-<PERSON>"], "summary": "Software artifacts, such as requirements, design, source code, documentation, and safety-related artifacts are typically expressed using domain-specific terminology. Automated tools which attempt to analyze software artifacts in order to perform tasks such as trace retrieval and maintenance, domain analysis, program comprehension, or to service natural language queries, need to understand the vocabulary and concepts of the domain in order to achieve acceptable levels of accuracy. Domain concepts can be captured and stored as an ontology. Unfortunately, constructing ontologies is extremely time-consuming and has proven hard to automate. This dissertation proposes a novel approach for semi-automated ontology building that leverages user-defined trace links to identify candidate domain facts. It uses a variety of web-mining, Natural Language Processing, and machine learning techniques to filter and rank the candidate facts, and to assist the user in building a domain-specific ontology. The benefits of the constructed ontology are described and evaluated within the context of automated trace link creation.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889264"}, {"primary_key": "4128480", "vector": [], "sparse_vector": [], "title": "The use of text retrieval and natural language processing in software engineering.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This technical briefing presents the state of the art Text Retrieval and Natural Language Processing techniques used in Software Engineering and discusses their applications in the field.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891053"}, {"primary_key": "4128483", "vector": [], "sparse_vector": [], "title": "Energy profiles of Java collections classes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We created detailed profiles of the energy consumed by common operations done on Java List, Map, and Set abstractions. The results show that the alternative data types for these abstractions differ significantly in terms of energy consumption depending on the operations. For example, an ArrayList consumes less energy than a LinkedList if items are inserted at the middle or at the end, but consumes more energy than a LinkedList if items are inserted at the start of the list. To explain the results, we explored the memory usage and the bytecode executed during an operation. Expensive computation tasks in the analyzed bytecode traces appeared to have an energy impact, but memory usage did not contribute. We evaluated our profiles by using them to selectively replace Collections types used in six applications and libraries. We found that choosing the wrong Collections type, as indicated by our profiles, can cost even 300% more energy than the most efficient choice. Our work shows that the usage context of a data structure and our measured energy profiles can be used to decide between alternative Collections implementations.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884869"}, {"primary_key": "4128488", "vector": [], "sparse_vector": [], "title": "Teaching university students Ka<PERSON>ban with a collaborative board game.", "authors": ["Ville T. Heikkilä", "<PERSON>", "<PERSON>"], "summary": "Kanban is a workflow management method especially suitable for managing continuous software engineering work. We attempted to teach Kanban and lean thinking in a software project management course in Aalto University with a collaborative Kanban board game. Our goal was to measure if the learning goals of the class were reached and to study the student's perceptions of the game. Data was collected from two subsequent classes in 2014 and 2015. Quantitative data was collected with questionnaires and analysed descriptively and statistically. Qualitative data was collected from 57 learning diaries. The students perceived they had learned substantially from the game. They also evaluated the game very positively. However, the qualitative results and the measured learning indicated that the learning goals were only partially reached. The enjoyable game experience did not fully translate into effective learning.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889201"}, {"primary_key": "4128489", "vector": [], "sparse_vector": [], "title": "Comparing white-box and black-box test prioritization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Although white-box regression test prioritization has been well-studied, the more recently introduced black-box prioritization approaches have neither been compared against each other nor against more well-established white-box techniques. We present a comprehensive experimental comparison of several test prioritization techniques, including well-established white-box strategies and more recently introduced black-box approaches. We found that Combinatorial Interaction Testing and diversity-based techniques (Input Model Diversity and Input Test Set Diameter) perform best among the black-box approaches. Perhaps surprisingly, we found little difference between black-box and white-box performance (at most 4% fault detection rate difference). We also found the overlap between black- and white-box faults to be high: the first 10% of the prioritized test suites already agree on at least 60% of the faults found. These are positive findings for practicing regression testers who may not have source code available, thereby making white-box techniques inapplicable. We also found evidence that both black-box and white-box prioritization remain robust over multiple system releases.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884791"}, {"primary_key": "4128491", "vector": [], "sparse_vector": [], "title": "Chaos engineering panel.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "One of the most significant changes in the software industry over the past two decades has been the transition from standalone applications to networked applications, known as the software-as-a-service model. Whether users are interacting with these services through a web browser or a custom app, on a laptop, desktop, mobile phone, tablet, or even a television or other networked device, they are ultimately using a client to connect to a remote server in order to consume a service provided over the Internet.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889230"}, {"primary_key": "4128492", "vector": [], "sparse_vector": [], "title": "Engineering software assemblies for participatory democracy: the participatory budgeting use case.", "authors": ["<PERSON>", "Val<PERSON>", "Cristhian Parra"], "summary": "The worldwide use of the Internet and social networking has transformed the constraints of time and space in human interaction: we can now be heard at a massive scale unprecedented in human history. As a result, information and communication technologies may enable citizens to undertake both government through direct assembly and collective action at a scale and an efficacy previously considered impossible. Our research concerns this opportunity to leverage a new sort of political life. We focus specifically on how software systems may enable participatory democracy, that is, the participation of citizens in democratic assembly, action, and governance. As an initial step, we have developed a service-oriented software platform, called AppCivist-PB, focused on a specific, yet representative use case of participatory democracy, namely, Participatory Budgeting (PB for short). PB is an allocation process used in many cities around the world through which they commit a percentage of their annual budget (often 5%) to implement citizen-proposed projects. In PB, residents of a city (or a higher level territorial organization), brainstorm, develop, and select project proposals that local government institutions are required to fund and implement. The key contribution of AppCivist-PB is to enable the cohesive creation of both citizen and software assemblies that together implement a given participatory budgeting campaign.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889221"}, {"primary_key": "4128495", "vector": [], "sparse_vector": [], "title": "Scalable thread sharing analysis.", "authors": ["<PERSON>"], "summary": "We present two scalable algorithms for identifying program locations that access thread-shared data in concurrent programs. The static algorithm, though simple, and without performing the expensive whole program information flow analysis, is much more efficient, less memory-demanding, and even more precise than the classical escape analysis algorithm. The dynamic algorithm, powered by a locationbased approach, achieves significant runtime speedups over a precise dynamic escape analysis. Our evaluation on a set of large real world complex multithreaded systems such as Apache Derby and Eclipse shows that our algorithms achieve unprecedented scalability. Used by client applications, our algorithms reduce the recording overhead of a record-replay system by 9X on average (as much as 16X) and increase the runtime logging speed of a data race detector by 32% on average (as much as 52%).", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884811"}, {"primary_key": "4128496", "vector": [], "sparse_vector": [], "title": "Maximally stateless model checking for concurrent bugs under relaxed memory models.", "authors": ["<PERSON>"], "summary": "Shared-memory multiprocessor architectures are now ubiq-uitous. To achieve higher performance, the constraints on the memory models become weaker. This makes it more challenging to verify concurrent programs. It is known that sequential consistency (SC) [7] is the most intuitive memory model. However, even for SC, it is challenging enough to verify the correctness of concurrent programs, because the number of the interleavings grows exponentially with the number of threads and the size of the program. For relaxed memory models, the verification problem is more tough because operations from the same thread can be re-ordered and there is even no globally consistent order among the operations of different threads. For example, for the Total Store Order (TSO) [9] and Partial Store Order (PSO) memory models, the order between a write and a following read or a write to different memory locations [2] can be re-ordered non-deterministically in the store buffer.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891042"}, {"primary_key": "4128502", "vector": [], "sparse_vector": [], "title": "SPYSE: a semantic search engine for python packages and modules.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Code reuse is a common practice among software developers, whether novices or experts. Developers often rely on online resources in order to find code to reuse. For Python, the Python Package Index (PyPI) contains all packages developed for the community and is the largest catalog of reusable, open source packages developers can consult. While a valuable resource, the state of the art PyPI search has very limited capabilities, making it hard for developers to find useful, high quality Python code to use for their task at hand.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889174"}, {"primary_key": "4128504", "vector": [], "sparse_vector": [], "title": "Assessing the process of an Eastern European software SME using systemic analysis, GQM, and reliability growth models: a case study.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper reports on the experience of the authors in quantitatively assessing the development process of an Eastern European software SME (Small or Medium Size Enterprise). The company produces a very successful workflow and documentation tool, features about 30 full time developers and has a customer base of about 40 major organizations. It has hired the authors as consultants to address quality and productivity issues raised by the upper management and customers. The adopted approach is based on systemic analysis, and starts with a comprehensive GQM session with the top managers of the company, to fully define the scope of work, and progresses analysing the documentation, interviewing the manager and the lead developers, and quantitatively analysing the issue tracking system in place. Specific attention is placed in identifying \"schismogenesis\", situations that may lead to unresolvable conflicts. The approach has been proven successful in providing a result in short forecasted timeframe, and systemic analysis has been effective in spotting the most critical situations present in the company. The result has been a set of prioritized recommendations, centered first in eliminating the schismogenetic situations and then ranging from adopting a more quantitative process control, to streamline the activities, to organize a line of product.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889250"}, {"primary_key": "4128505", "vector": [], "sparse_vector": [], "title": "Software energy profiling: comparing releases of a software product.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the quest for energy efficiency of Information and Communication Technology, so far research has mostly focused on the role of hardware. However, as hardware technology becomes more sophisticated, the role of software becomes crucial. Recently, the impact of software on energy consumption has been acknowledged as significant by researchers in software engineering. In spite of that, measuring the energy consumption of software has proven to be a challenge, due to the large number of variables that need to be controlled to obtain reliable measurements. Due to cost and time constraints, many software product organizations are unable to effectively measure the energy consumption of software. This prevents them to be in control over the energy efficiency of their products.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889216"}, {"primary_key": "4128506", "vector": [], "sparse_vector": [], "title": "Mining sandboxes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present sandbox mining, a technique to confine an application to resources accessed during automatic testing. Sandbox mining first explores software behavior by means of automatic test generation, and extracts the set of resources accessed during these tests. This set is then used as a sandbox, blocking access to resources not used during testing. The mined sandbox thus protects against behavior changes such as the activation of latent malware, infections, targeted attacks, or malicious updates.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884782"}, {"primary_key": "4128508", "vector": [], "sparse_vector": [], "title": "Synthesizing framework models for symbolic execution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>-<PERSON>"], "summary": "Symbolic execution is a powerful program analysis technique, but it is difficult to apply to programs built using frameworks such as Swing and Android, because the framework code itself is hard to symbolically execute. The standard solution is to manually create a framework model that can be symbolically executed, but developing and maintaining a model is difficult and error-prone. In this paper, we present Pasket, a new system that takes a first step toward automatically generating Java framework models to support symbolic execution. <PERSON><PERSON>'s focus is on creating models by instantiating design patterns. <PERSON><PERSON> takes as input class, method, and type information from the framework API, together with tutorial programs that exercise the framework. From these artifacts and <PERSON><PERSON>'s internal knowledge of design patterns, <PERSON><PERSON> synthesizes a framework model whose behavior on the tutorial programs matches that of the original framework. We evaluated <PERSON><PERSON> by synthesizing models for subsets of Swing and Android. Our results show that the models derived by <PERSON><PERSON> are sufficient to allow us to use off-the-shelf symbolic execution tools to analyze Java programs that rely on frameworks.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884856"}, {"primary_key": "4128510", "vector": [], "sparse_vector": [], "title": "Use runtime verification to improve the quality of medical care practice.", "authors": ["Yu <PERSON>", "<PERSON>", "Hui Kong", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Clinical guidelines and decision support systems (DSS) play an important role in daily practices of medicine. Many text-based guidelines have been encoded for work-flow simulation of DSS to automate health care. During the collaboration with Carle hospital to develop a DSS, we identify that, for some complex and life-critical diseases, it is highly desirable to automatically rigorously verify some complex temporal properties in guidelines, which brings new challenges to current simulation based DSS with limited support of automatical formal verification and real-time data analysis.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889233"}, {"primary_key": "4128513", "vector": [], "sparse_vector": [], "title": "Missing data imputation based on low-rank recovery and semi-supervised regression for software effort estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software effort estimation (SEE) is a crucial step in software development. Effort data missing usually occurs in real-world data collection. Focusing on the missing data problem, existing SEE methods employ the deletion, ignoring, or imputation strategy to address the problem, where the imputation strategy was found to be more helpful for improving the estimation performance. Current imputation methods in SEE use classical imputation techniques for missing data imputation, yet these imputation techniques have their respective disadvantages and might not be appropriate for effort data. In this paper, we aim to provide an effective solution for the effort data missing problem. Incompletion includes the drive factor missing case and effort label missing case. We introduce the low-rank recovery technique for addressing the drive factor missing case. And we employ the semi-supervised regression technique to perform imputation in the case of effort label missing. We then propose a novel effort data imputation approach, named low-rank recovery and semi-supervised regression imputation (LRSRI). Experiments on 7 widely used software effort datasets indicate that: (1) the proposed approach can obtain better effort data imputation effects than other methods; (2) the imputed data using our approach can apply to multiple estimators well.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884827"}, {"primary_key": "4128517", "vector": [], "sparse_vector": [], "title": "Analyzing software engineering experiments: everything you always wanted to know but were afraid to ask.", "authors": ["<PERSON>", "Sira <PERSON>"], "summary": "Experimentation is a key issue in science and engineering. But it is one of software engineering's stumbling blocks. Quite a lot of experiments are run nowadays, but it is a risky business. Software engineering has some special features, leading to some experimentation issues being conceived of differently than in other disciplines. The aim of this technical briefing is to help participants to avoid common pitfalls when analyzing the results of software engineering experiments. The technical briefing is not intended as a data analysis course, because there is already plenty of literature on this subject. It reviews several issues that we have identified in published SE experiments.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891054"}, {"primary_key": "4128527", "vector": [], "sparse_vector": [], "title": "Recognizing relevant code elements during change task navigation.", "authors": ["<PERSON><PERSON>"], "summary": "Developers spend a significant amount of their time exploring source code. Yet, little is known about the way developers break down their code exploration or the fine-grained navigation for change tasks within methods. The objective of our research is to address this gap and learn more about developers' code navigation for change tasks to devise better tool support. For our research, we perform exploratory studies also taking advantage of eye-tracking technology and interaction monitoring to gather detailed data on developers' code navigation down to the line-level. Based on the findings, we devise a model and approach that capture developers' code navigation and allow to automatically determine code elements that are relevant in the near future as well as to determine and summarize the elements that are currently relevant and might, for instance, be helpful for task resumption after interruptions. We plan to evaluate our model and approach in a multiple cases study on navigation recommendation and work resumption.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889270"}, {"primary_key": "4128528", "vector": [], "sparse_vector": [], "title": "AD-ROOM: a tool for automatic detection of refactorings in object-oriented models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Detecting refactorings in Object-Oriented Models (OOM) is essential to automate the repair, maintenance, and migration of OOM-related products. However, detecting refactorings is challenging since multiple sequences of atomic changes may define a single user intention and refactorings may overlap over the atomic change trace. In this paper, we present AD-ROOM, an Eclipse-based tool to automatically detect refactorings during evolution of OOM. In contrast to existing tools, AD-ROOM is designed to reach 100% recall that is confirmed in our eight case studies. We allow user confirmation and we support the user with three heuristics that help to improve the precision of AD-ROOM. See the demonstration video: https://youtu.be/4OJ8zHtfnq8", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889169"}, {"primary_key": "4128529", "vector": [], "sparse_vector": [], "title": "The emerging role of data scientists on software development teams.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Creating and running software produces large amounts of raw data about the development process and the customer usage, which can be turned into actionable insight with the help of skilled data scientists. Unfortunately, data scientists with the analytical and software engineering skills to analyze these large data sets have been hard to come by; only recently have software companies started to develop competencies in software-oriented data analytics. To understand this emerging role, we interviewed data scientists across several product groups at Microsoft. In this paper, we describe their education and training background, their missions in software engineering contexts, and the type of problems on which they work. We identify five distinct working styles of data scientists: (1) Insight Providers, who work with engineers to collect the data needed to inform decisions that managers make; (2) Modeling Specialists, who use their machine learning expertise to build predictive models; (3) Platform Builders, who create data platforms, balancing both engineering and data analysis concerns; (4) Polymaths, who do all data science activities themselves; and (5) Team Leaders, who run teams of data scientists and spread best practices. We further describe a set of strategies that they employ to increase the impact and actionability of their work.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884783"}, {"primary_key": "4128530", "vector": [], "sparse_vector": [], "title": "Improving refactoring speed by 10X.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Refactoring engines are standard tools in today's Integrated Development Environments (IDEs). They allow programmers to perform one refactoring at a time, but programmers need more. Most design patterns in the Gang-of-Four text can be written as a refactoring script -- a programmatic sequence of refactorings. In this paper, we present R3, a new Java refactoring engine that supports refactoring scripts. It builds a main-memory, non-persistent database to encode Java entity declarations (e.g., packages, classes, methods), their containment relationships, and language features such as inheritance and modifiers. Unlike classical refactoring engines that modify Abstract Syntax Trees (ASTs), R3 refactorings modify only the database; refactored code is produced only when pretty-printing ASTs that reference database changes. R3 performs comparable precondition checks to those of the Eclipse Java Development Tools (JDT) but R3's codebase is about half the size of the JDT refactoring engine and runs an order of magnitude faster. Further, a user study shows that R3 improved the success rate of retrofitting design patterns by 25% up to 50%.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884802"}, {"primary_key": "4128536", "vector": [], "sparse_vector": [], "title": "Code review quality: how developers see it.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In a large, long-lived project, an effective code review process is key to ensuring the long-term quality of the code base. In this work, we study code review practices of a large, open source project, and we investigate how the developers themselves perceive code review quality. We present a qualitative study that summarizes the results from a survey of 88 Mozilla core developers. The results provide developer insights into how they define review quality, what factors contribute to how they evaluate submitted code, and what challenges they face when performing review tasks. We found that the review quality is primarily associated with the thoroughness of the feedback, the reviewer's familiarity with the code, and the perceived quality of the code itself. Also, we found that while different factors are perceived to contribute to the review quality, reviewers often find it difficult to keep their technical skills up-to-date, manage personal priorities, and mitigate context switching.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884840"}, {"primary_key": "4128539", "vector": [], "sparse_vector": [], "title": "Rethinking verification: accuracy, efficiency and scalability through human-machine collaboration.", "authors": ["<PERSON>sh <PERSON>", "<PERSON>", "<PERSON>"], "summary": "With growing dependence on software in embedded and cyber-physical systems where vulnerabilities and malware can lead to disasters, efficient and accurate verification has become a crucial need for safety and cybersecurity. Formal verification of large software has remained an elusive target, riddled with problems of low accuracy and high computational complexity [9, 11, 16, 18]. The need for automating verification is undoubted, however human is indispensable to accurate real-world software verification. The automation should actually enable and simplify human crosschecking, which is especially important when the stakes are high. This technical briefing discusses the challenges of creating a powerful fusion of automation and human intelligence to solve software verification problems where complete automation has remained intractable. We will contrast with existing software verification approaches and reflect on their strengths and limitations as a human-machine collaboration framework and outline key software engineering research and practice challenges to be addressed in the future.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891046"}, {"primary_key": "4128540", "vector": [], "sparse_vector": [], "title": "Let&apos;s verify Linux: accelerated learning of analytical reasoning through automation and collaboration.", "authors": ["<PERSON>sh <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We describe our experiences in the classroom using the internet to collaboratively verify a significant safety and security property across the entire Linux kernel. With 66,609 instances to check across three versions of Linux, the naive approach of simply dividing up the code and assigning it to students does not scale, and does little to educate. However, by teaching and applying analytical reasoning, the instances can be categorized effectively, the problems of scale can be managed, and students can collaborate and compete with one another to achieve an unprecedented level of verification.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889192"}, {"primary_key": "4128541", "vector": [], "sparse_vector": [], "title": "How do free/open source developers pick their tools?: a Delphi study of the Debian project.", "authors": ["<PERSON>", "Klaas<PERSON><PERSON>", "<PERSON>"], "summary": "Free and Open Source Software (FOSS) has come to play a critical role in the global software industry. Organizations are widely adopting FOSS and interacting with open source communities, and hence organizations have a considerable interest in seeing these communities flourishing. Little research has focused on the tools used to develop that software. Given the absence of formal mandate that would appear in traditional organizations, an open question is what influences a FOSS contributor's decision to adopt a tool and how workflows get established in FOSS teams. In this paper we report on a Delphi study conducted in the Debian Project, one of the largest FOSS projects. Drawing from data collected in three phases from a panel of 21 carefully selected and well-informed participants, we identified 15 factors that affect decisions to adopt tools and relate those to existing models of innovation and diffusion.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889248"}, {"primary_key": "4128544", "vector": [], "sparse_vector": [], "title": "Teaching code review management using branch based workflows.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Developing software with high code quality in a university environment is a challenge for instructors of software engineering capstone courses. Teaching students how to achieve quality and how to conduct code reviews in projects is often neglected, although it helps to improve maintainability.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889191"}, {"primary_key": "4128545", "vector": [], "sparse_vector": [], "title": "When teams go crazy: an environment to experience group dynamics in software project management courses.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software development consists to a large extend of human-based processes with continuously increasing demands regarding interdisciplinary team work. Understanding the dynamics of software teams can be seen as highly important to successful project execution. Hence, for future project managers, knowledge about non-technical processes in teams is significant. In this paper, we present a course unit that provides an environment in which students can learn and experience the impact of group dynamics on project performance and quality. The course unit uses the Tuckman model as theoretical framework, and borrows from controlled experiments to organize and implement its practical parts in which students then experience the effects of, e.g., time pressure, resource bottlenecks, staff turnover, loss of key personnel, and other stress factors. We provide a detailed design of the course unit to allow for implementation in further software project management courses. Furthermore, we provide experiences obtained from two instances of this unit conducted in Munich and Karlskrona with 36 graduate students. We observed students building awareness of stress factors and developing counter measures to reduce impact of those factors. Moreover, students experienced what problems occur when teams work under stress and how to form a performing team despite exceptional situations.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889194"}, {"primary_key": "4128546", "vector": [], "sparse_vector": [], "title": "Model driven development of business applications: a practitioner&apos;s perspective.", "authors": ["<PERSON><PERSON>"], "summary": "We discuss our experience in use of models and model-driven techniques for developing large business applications. Benefits accrued and limitations observed are highlighted. We describe possible means of overcoming some of the limitations and experience thereof. A case for shift in focus of model driven engineering (MDE) community in the context of large enterprises is argued. Though emerging from a specific context, we think, the takeaways from this experience may have a more general appeal for MDE practitioners, tool vendors and researchers.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889251"}, {"primary_key": "4128549", "vector": [], "sparse_vector": [], "title": "Mentoring trajectories in an evolving agile workplace.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Agile approaches to software development offer flexibility and autonomy to developers, while demanding discipline and attentiveness. At its best, agile constitutes an idealized vision of Wenger's Community of Practice: one where the essence of the software practice is freely negotiated by participants following self-determined trajectories of identity within the community. While agile frameworks prescribe ways of doing, in reality these codified practices must adapt to a real workplace. Nowhere is this more plainly seen than in the onboarding stage of new employees: this is the point where newcomers establish an identity and establish means of participating within the development community.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889236"}, {"primary_key": "4128550", "vector": [], "sparse_vector": [], "title": "Engaging software estimation education using LEGOs: a case study.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Software Estimation is an important part of every Software Engineers' skill set. At Stevens Institute of Technology, we have taught Estimation as part of our Software Engineering Masters Program since 2001. Over the past few years, we have evolved our teaching style to be more experiential and engaging. This case study describes an evolving software engineering pedagogical method using LEGOs, which allows students to get hands-on practice and feedback on their estimation skills and techniques, as well as testing some of the commonly held estimation heuristics. The results of the classroom exercises are positive; the students are very involved and the estimation processes and results are consistent with other software engineering estimation heuristics and models. Student experience Wide-band Delphi, custom models using historical data, the \"wisdom of the crowds,\" estimation model factors (e.g., complexity, motivation, expertise level) and the impact of team size. This teaching technique, since it appears to reinforce software engineering estimation heuristics and models, has the potential to be extended for other software estimation experiential education and experimentation, such as demonstrating the impact of anchoring on estimations. In addition, the students have fun doing these exercises, and become totally engaged in the subject.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889205"}, {"primary_key": "4128552", "vector": [], "sparse_vector": [], "title": "Teaching a global software development course: student experiences using onsite exercise simulation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Over the past decade, major advancements in software development have occurred in the global context. Global software development (GSD) is an effective strategy, and many higher educational institutions have been offering GSD courses. These courses are usually organized together with another institution situated in a different location. However, conducting such a course with more than one institution is not so economical since it involves greater collaboration among various institutions than in the case of a general onsite course. In this paper, we present an onsite simulation that deals with the specifics in the field of GSD training and teaching. We analyzed the students' learning reflections with a phenomenographic approach to validate the relevance of the design science construct of the course model containing an onsite simulation. Based on the analyzed data, it is possible to organize a GSD course on a single location with the aid of role-play simulation. The presented course model can help an institution prepare its students to solve most of the common problems faced in industrial GSD settings.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889198"}, {"primary_key": "4128553", "vector": [], "sparse_vector": [], "title": "Architectural-based speculative analysis to predict bugs in a software system.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over time, a software system's code and its underlying design tend to decay steadily and, in turn, to complicate the system's maintenance. In order to address that phenomenon, many researchers tried to help engineers predict parts of a system that are most likely to create problems while or even before they are modifying the system. Problems that creep into a system may manifest themselves as bugs, in which case engineers have no choice but to fix them or develop workarounds. However, these problems may also be more subtle, such as code clones, circular dependencies among system elements, very large APIs, individual elements that implement multiple diffuse concerns, etc. Even though such architectural and code \"smells\" may not crash a system outright, they impose real costs in terms of engineers' time and effort, as well as system correctness and performance. Along the time, implicit problems may be revealed as explicit problems. However, most current techniques predict explicit problems of a system only based on explicit problems themselves. Our research takes a further step by using implicit problems, e.g., architectural- and code-smells, in combination with explicit problems to provide an accurate, systematic and in depth approach to predict potential system problems, particularly bugs.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889260"}, {"primary_key": "4128559", "vector": [], "sparse_vector": [], "title": "Instantaneous performance bug detection in IDE.", "authors": ["<PERSON><PERSON>"], "summary": "Software performance has a vital impact on user experience. However, many software applications suffer from performance bugs that cause significant perfomance degradation, resource waste and poor user experience. Performance bugs exist widely in released software. For example, Mozilla developers has fixed 5-60 performance bugs reported by users every month over the past ten years [2]. The prevalence of performance bugs is inevitable because modern compilers are ill-equipped to deal with performance bugs that cross many procedure and abstraction boundaries. Worse still, performance bugs are costly to detect due to their non-fail-stop symptoms such as application crashes. Experts may make several months of e.orts to find out a couple of performance bugs that cause a software application a few hundred-millisecond delay [10].", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891044"}, {"primary_key": "4128560", "vector": [], "sparse_vector": [], "title": "Boosting static analysis of Android apps through code instrumentation.", "authors": ["<PERSON>"], "summary": "Static analysis has been applied to dissect Android apps for many years. The main advantage of using static analysis is its efficiency and entire code coverage characteristics. However, the community has not yet produced complete tools to perform in-depth static analysis, putting users at risk to malicious apps. Because of the diverse challenges caused by Android apps, it is hard for a single tool to efficiently address all of them. Thus, in this work, we propose to boost static analysis of Android apps through code instrumentation, in which the knotty code can be reduced or simplified into an equivalent but analyzable code. Consequently, existing static analyzers, without any modification, can be leveraged to perform extensive analysis, although originally they cannot.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889258"}, {"primary_key": "4128561", "vector": [], "sparse_vector": [], "title": "When to release in open source project?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As a rapidly growing number of open source projects adopt distributed version control systems, it becomes more crucial and challenging for release managers to oversee the software development, testing, deployment, and support during the software development life cycle. Manual monitoring and control of the lengthy commit history has been extremely tedious and effort consuming. This paper proposes an automated approach based on chronological commit logs to recommend release candidates for release managers by casting release tagging into a structured learning problem. It predicts whether a commit can be identified as a potential release point leveraging Conditional Random Fields (CRFs) model fueled by automatic feature extraction. Experiments on two open source datasets demonstrate promising results.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892654"}, {"primary_key": "4128562", "vector": [], "sparse_vector": [], "title": "Automated energy optimization of HTTP requests for mobile applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Energy is a critical resource for apps that run on mobile devices. Among all operations, making HTTP requests is one of the most energy consuming. Previous studies have shown that bundling smaller HTTP requests into a single larger HTTP request can be an effective way to improve energy efficiency of network communication, but have not defined an automated way to detect when apps can be bundled nor to transform the apps to do this bundling. In this paper we propose an approach to reduce the energy consumption of HTTP requests in Android apps by automatically detecting and then bundling multiple HTTP requests. Our approach first detects HTTP requests that can be bundled using static analysis, then uses a proxy based technique to bundle HTTP requests at runtime. We evaluated our approach on a set of real world marketplace Android apps. In this evaluation, our approach achieved an average energy reduction of 15% for the subject apps and did not impose a significant runtime overhead on the optimized apps.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884867"}, {"primary_key": "4128563", "vector": [], "sparse_vector": [], "title": "Measuring code behavioral similarity for programming and software engineering education.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In recent years, online programming and software engineering education via information technology has gained a lot of popularity. Typically, popular courses often have hundreds or thousands of students but only a few course staff members. Tool automation is needed to maintain the quality of education. In this paper, we envision that the capability of quantifying behavioral similarity between programs is helpful for teaching and learning programming and software engineering, and propose three metrics that approximate the computation of behavioral similarity. Specifically, we leverage random testing and dynamic symbolic execution (DSE) to generate test inputs, and run programs on these test inputs to compute metric values of the behavioral similarity. We evaluate our metrics on three real-world data sets from the Pex4Fun platform (which so far has accumulated more than 1.7 million game-play interactions). The results show that our metrics provide highly accurate approximation to the behavioral similarity. We also demonstrate a number of practical applications of our metrics including hint generation, progress indication, and automatic grading.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889204"}, {"primary_key": "4128564", "vector": [], "sparse_vector": [], "title": "AntMiner: mining more bugs by reducing noise interference.", "authors": ["<PERSON>", "Pan Bian", "<PERSON>", "Wenchang Shi", "<PERSON>", "<PERSON>"], "summary": "Detecting bugs with code mining has proven to be an effective approach. However, the existing methods suffer from reporting serious false positives and false negatives. In this paper, we developed an approach called AntMiner to improve the precision of code mining by carefully preprocessing the source code. Specifically, we employ the program slicing technique to decompose the original source repository into independent sub-repositories, taking critical operations (automatically extracted from source code) as slicing criteria. In this way, the statements irrelevant to a critical operation are excluded from the corresponding sub-repository. Besides, various semantics-equivalent representations are normalized into a canonical form. Eventually, the mining process can be performed on a refined code database, and false positives and false negatives can be significantly pruned. We have implemented AntMiner and applied it to detect bugs in the Linux kernel. It reported 52 violations that have been either confirmed as real bugs by the kernel development community or fixed in new kernel versions. Among them, 41 cannot be detected by a widely used representative analysis tool Coverity. Besides, the result of a comparative analysis shows that our approach can effectively improve the precision of code mining and detect subtle bugs that have previously been missed.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884870"}, {"primary_key": "4128566", "vector": [], "sparse_vector": [], "title": "iDice: problem identification for emerging issues.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "One challenge for maintaining a large-scale software system, especially an online service system, is to quickly respond to customer issues. The issue reports typically have many categorical attributes that reflect the characteristics of the issues. For a commercial system, most of the time the volume of reported issues is relatively constant. Sometimes, there are emerging issues that lead to significant volume increase. It is important for support engineers to efficiently and effectively identify and resolve such emerging issues, since they have impacted a large number of customers. Currently, problem identification for an emerging issue is a tedious and error-prone process, because it requires support engineers to manually identify a particular attribute combination that characterizes the emerging issue among a large number of attribute combinations. We call such an attribute combination effective combination, which is important for issue isolation and diagnosis. In this paper, we propose iDice, an approach that can identify the effective combination for an emerging issue with high quality and performance. We evaluate the effectiveness and efficiency of iDice through experiments. We have also successfully applied iDice to several Microsoft online service systems in production. The results confirm that iDice can help identify emerging issues and reduce maintenance effort.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884795"}, {"primary_key": "4128567", "vector": [], "sparse_vector": [], "title": "Log clustering based problem identification for online service systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Logs play an important role in the maintenance of large-scale online service systems. When an online service fails, engineers need to examine recorded logs to gain insights into the failure and identify the potential problems. Traditionally, engineers perform simple keyword search (such as \"error\" and \"exception\") of logs that may be associated with the failures. Such an approach is often time consuming and error prone. Through our collaboration with Microsoft service product teams, we propose LogCluster, an approach that clusters the logs to ease log-based problem identification. LogCluster also utilizes a knowledge base to check if the log sequences occurred before. Engineers only need to examine a small number of previously unseen, representative log sequences extracted from the clusters to identify a problem, thus significantly reducing the number of logs that should be examined, meanwhile improving the identification accuracy. Through experiments on two Hadoop-based applications and two large-scale Microsoft online service systems, we show that our approach is effective and outperforms the state-of-the-art work proposed by <PERSON> et al. in ICSE 2013. We have successfully applied LogCluster to the maintenance of many actual Microsoft online service systems. In this paper, we also share our success stories and lessons learned.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889232"}, {"primary_key": "4128569", "vector": [], "sparse_vector": [], "title": "A variability aware configuration management and revision control platform.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern systems need to run in many different contexts like hardware and software platforms or environmental conditions. Additionally different customers might have slightly different requirements towards systems. Therefore software systems need to be highly configurable and provide variable sets of features for different customers. There are various approaches to developing and managing such systems, like ad-hoc clone-and-own approaches or structured software product line approaches for each of which again several different techniques and tools exist to support them. While the different approaches come with advantages they also have several disadvantages and shortcomings. Some work only with specific implementation artifacts (e.g. source code but not models) and others exist only as plugins for specific IDEs which makes them intrusive or even unusable in some development environments. In our work we present a development process and tools for managing and engineering of highly configurable and variable systems that is generic, incremental, flexible and intuitive. We evaluated our approach on several case study systems from various different domains and origins like open source, academia or industry. The results so far showed promising results.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889262"}, {"primary_key": "4128570", "vector": [], "sparse_vector": [], "title": "Towards better program obfuscation: optimization via language models.", "authors": ["<PERSON>"], "summary": "As a common practice in software development, program obfuscation aims at deterring reverse engineering and malicious attacks on released source or binary code. Owning ample obfuscation techniques, we have relatively little knowledge on how to most effectively use them. The biggest challenge lies in identifying the most useful combination of these techniques. We propose a unified framework to automatically generate and optimize obfuscation based on an obscurity language model and a Monte <PERSON> Chain (MCMC) based search algorithm. We further instantiate it for JavaScript programs and developed the Closure* tool. Compared to the well-known Google Closure Compiler, Closure* outperforms its default setting by 26%. For programs which have already been well obfuscated, Closure* can still outperform by 22%.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891040"}, {"primary_key": "4128572", "vector": [], "sparse_vector": [], "title": "DoubleTake: fast and precise error detection via evidence-based dynamic analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Programs written in unsafe languages like C and C++ often suffer from errors like buffer overflows, dangling pointers, and memory leaks. Dynamic analysis tools like Valgrind can detect these errors, but their overhead---primarily due to the cost of instrumenting every memory read and write---makes them too heavyweight for use in deployed applications and makes testing with them painfully slow. The result is that much deployed software remains susceptible to these bugs, which are notoriously difficult to track down.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884784"}, {"primary_key": "4128573", "vector": [], "sparse_vector": [], "title": "Nomen est omen: exploring and exploiting similarities between argument and parameter names.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Programmer-provided identifier names convey information about the semantics of a program. This information can complement traditional program analyses in various software engineering tasks, such as bug finding, code completion, and documentation. Even though identifier names appear to be a rich source of information, little is known about their properties and their potential usefulness. This paper presents an empirical study of the lexical similarity between arguments and parameters of methods, which is one prominent situation where names can provide otherwise missing information. The study involves 60 real-world Java programs. We find that, for most arguments, the similarity is either very high or very low, and that short and generic names often cause low similarities. Furthermore, we show that inferring a set of low-similarity parameter names from one set of programs allows for pruning such names in another set of programs. Finally, the study shows that many arguments are more similar to the corresponding parameter than any alternative argument available in the call site's scope. As applications of our findings, we present an anomaly detection technique that identifies 144 renaming opportunities and incorrect arguments in 14 programs, and a code recommendation system that suggests correct arguments with a precision of 83%.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884841"}, {"primary_key": "4128574", "vector": [], "sparse_vector": [], "title": "An analysis of the search spaces for generate and validate patch generation systems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present the first systematic analysis of key characteristics of patch search spaces for automatic patch generation systems. We analyze sixteen different configurations of the patch search spaces of SPR and Prophet, two current state-of-the-art patch generation systems. The analysis shows that 1) correct patches are sparse in the search spaces (typically at most one correct patch per search space per defect), 2) incorrect patches that nevertheless pass all of the test cases in the validation test suite are typically orders of magnitude more abundant, and 3) leveraging information other than the test suite is therefore critical for enabling the system to successfully isolate correct patches.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884872"}, {"primary_key": "4128575", "vector": [], "sparse_vector": [], "title": "How does regression test prioritization perform in real-world software evolution?", "authors": ["<PERSON><PERSON> Lu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, researchers have intensively investigated various topics in test prioritization, which aims to re-order tests to increase the rate of fault detection during regression testing. While the main research focus in test prioritization is on proposing novel prioritization techniques and evaluating on more and larger subject systems, little effort has been put on investigating the threats to validity in existing work on test prioritization. One main threat to validity is that existing work mainly evaluates prioritization techniques based on simple artificial changes on the source code and tests. For example, the changes in the source code usually include only seeded program faults, whereas the test suite is usually not augmented at all. On the contrary, in real-world software development, software systems usually undergo various changes on the source code and test suite augmentation. Therefore, it is not clear whether the conclusions drawn by existing work in test prioritization from the artificial changes are still valid for real-world software evolution. In this paper, we present the first empirical study to investigate this important threat to validity in test prioritization. We reimplemented 24 variant techniques of both the traditional and time-aware test prioritization, and investigated the impacts of software evolution on those techniques based on the version history of 8 real-world Java programs from GitHub. The results show that for both traditional and time-aware test prioritization, test suite augmentation significantly hampers their effectiveness, whereas source code changes alone do not influence their effectiveness much.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884874"}, {"primary_key": "4128576", "vector": [], "sparse_vector": [], "title": "PRADA: prioritizing android devices for apps by mining large-scale usage data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Selecting and prioritizing major device models are critical for mobile app developers to select testbeds and optimize resources such as marketing and quality-assurance resources. The heavily fragmented distribution of Android devices makes it challenging to select a few major device models out of thousands of models available on the market. Currently app developers usually rely on some reported or estimated general market share of device models. However, these estimates can be quite inaccurate, and more problematically, can be irrelevant to the particular app under consideration. To address this issue, we propose PRADA, the first approach to prioritizing Android device models for individual apps, based on mining large-scale usage data. PRADA adapts the concept of operational profiling (popularly used in software reliability engineering) for mobile apps -- the usage of an app on a specific device model reflects the importance of that device model for the app. PRADA includes a collaborative filtering technique to predict the usage of an app on different device models, even if the app is entirely new (without its actual usage in the market yet), based on the usage data of a large collection of apps. We empirically demonstrate the effectiveness of PRADA over two popular app categories, i.e., Game and Media, covering over 3.86 million users and 14,000 device models collected through a leading Android management app in China.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884828"}, {"primary_key": "4128581", "vector": [], "sparse_vector": [], "title": "FOREPOST: a tool for detecting performance problems with feedback-driven learning software testing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A goal of performance testing is to find situations when applications unexpectedly exhibit worsened characteristics for certain combinations of input values. A fundamental question of performance testing is how to select a manageable subset of the input data faster to find performance problems in applications automatically.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889164"}, {"primary_key": "4128582", "vector": [], "sparse_vector": [], "title": "SolMiner: mining distinct solutions in programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given a programming problem, because of a variety of data structures and algorithms that can be applied and different tradeoffs, such as space-time, to be considered, there may be many distinct solutions. By comparing his/her solution against others' and learning from the distinct solutions, a learner may quickly improve programming skills and gain experience in making trade-offs. Meanwhile, on the Internet many websites provide venues for programming practice and contests. Popular websites receive hundreds of thousands of submissions daily from novices as well as advanced learners. While these websites can automatically judge the correctness of a submission, none extracts distinct solutions from the submissions and provides them to learners. How to automatically identify distinct solutions from a large number of submissions is a challenging and unresolved problem. Due to diverse coding styles and high programming flexibility, submissions implementing the same solution may appear very different from each other; in addition, dealing with submissions at scale imposes extra challenges. We propose SolMiner, a solution miner, that automatically mines distinct solutions from a large number of submissions. SolMiner leverages static program analysis, data mining, and machine learning to automatically measure the similarity between submissions and identify distinct solutions. We have built a prototype of SolMiner and evaluated it. The evaluation shows that the technique is effective and efficient.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889202"}, {"primary_key": "4128583", "vector": [], "sparse_vector": [], "title": "Software engineering for molecular programming.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Molecular programming combines computer science principles with the information-processing power of DNA and other biomolecules to design self-assembling, programmable systems at the nanoscale. Molecular programming is the programming of matter to do our bidding at molecular scales, and it is programming in the literal sense of computer science. Targeted and customized medical therapeutics, cheap and reliable bio-sensors, molecular robots, smart materials, and bio-compatible computer electronics are applications of molecular programming that are poised to have a major impact on society.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891048"}, {"primary_key": "4128585", "vector": [], "sparse_vector": [], "title": "LibRadar: fast and accurate detection of third-party libraries in Android apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present LibRadar, a tool that is able to detect third-party libraries used in an Android app accurately and instantly. As third-party libraries are widely used in Android apps, program analysis on Android apps typically needs to detect or remove third-party libraries first in order to function correctly or provide accurate results. However, most previous studies employ a whitelist of package names of known libraries, which is incomplete and unable to deal with obfuscation. In contrast, LibRadar detects libraries based on stable API features that are obfuscation resilient in most cases. After analyzing one million free Android apps from Google Play, we have identified possible libraries and collected their unique features. Based on these features, LibRadar can detect third-party libraries in a given Android app within seconds, as it only requires simple static analysis and fast comparison. LibRadar is available for public use at http://radar.pkuos.org. The demo video is available at: https://youtu.be/GoMYjYxsZnI", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889178"}, {"primary_key": "4128587", "vector": [], "sparse_vector": [], "title": "Feedback-directed instrumentation for deployed JavaScript applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many bugs in JavaScript applications manifest themselves as objects that have incorrect property values when a failure occurs. For this type of error, stack traces and log files are often insuffcient for diagnosing problems. In such cases, it is helpful for developers to know the control ow path from the creation of an object to a crashing statement. Such crash paths are useful for understanding where the object originated and whether any properties of the object were corrupted since its creation. We present a feedback-directed instrumentation technique for computing crash paths that allows the instrumentation overhead to be distributed over a crowd of users and to re-duce it for users who do not encounter the crash. We imple-mented our technique in a tool, Crowdie, and evaluated it on 10 real-world issues for which error messages and stack traces are insuffcient to isolate the problem. Our results show that feedback-directed instrumentation requires 5% to 25% of the program to be instrumented, that the same crash must be observed 3 to 10 times to discover the crash path, and that feedback-directed instrumentation typically slows down execution by a factor 2x{9x compared to 8x{90x for an approach where applications are fully instrumented.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884846"}, {"primary_key": "4128591", "vector": [], "sparse_vector": [], "title": "An empirical study of practitioners&apos; perspectives on green software engineering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Ciera Jaspan", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The energy consumption of software is an increasing concern as the use of mobile applications, embedded systems, and data center-based services expands. While research in green software engineering is correspondingly increasing, little is known about the current practices and perspectives of software engineers in the field. This paper describes the first empirical study of how practitioners think about energy when they write requirements, design, construct, test, and maintain their software. We report findings from a quantitative, targeted survey of 464 practitioners from ABB, Google, IBM, and Microsoft, which was motivated by and supported with qualitative data from 18 in-depth interviews with Microsoft employees. The major findings and implications from the collected data contextualize existing green software engineering research and suggest directions for researchers aiming to develop strategies and tools to help practitioners improve the energy usage of their applications.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884810"}, {"primary_key": "4128593", "vector": [], "sparse_vector": [], "title": "Causal impact for app store analysis.", "authors": ["<PERSON>"], "summary": "App developers naturally want to know which of their releases are successful and which are unsuccessful. Such information can help with release planning and requirements prioritisation and elicitation. To address this problem, I performed causal analysis on 52 weeks of popular app releases from Google Play and Windows Phone Store. The results reveal properties of successful releases in multiple app stores, and showcase causal analysis as a useful technique for developers seeking to know more about their software releases.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891033"}, {"primary_key": "4128595", "vector": [], "sparse_vector": [], "title": "An empirically developed method to aid decisions on architectural technical debt refactoring: AnaConDebt.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Architectural Technical Debt is regarded as sub-optimal architectural solutions that need to be refactored in order to avoid the payment of a costly interest in the future. However, decisions on if and when to refactor architecture are extremely important and difficult to take, since changing software at the architectural level is quite expensive. Therefore it is important, for software organizations, to have methods and tools that aid architects and managers to understand if Architecture Technical Debt will generate a costly and growing interest to be paid or not. Current knowledge, especially empirically developed and evaluated, is quite scarce. In this paper we developed and evaluated a method, AnaConDebt, by analyzing, together with several practitioners, 12 existing cases of Architecture Debt in 6 companies. The method has been refined several times in order to be useful and effective in practice. We also report the evaluation of the method with a final case, for which we present anonymized results and subsequent refactoring decisions. The method consists of several components that need to be analyzed, combining the theoretical Technical Debt framework and the practical experience of the practitioners, in order to identify the key factors involved in the growth of interest. The output of the method shows summarized indicators that visualizes the factors in a useful way for the stakeholders. This analysis aids the practitioners in deciding on if and when to refactor Architectural Technical Debt items. The method has been evaluated and has been proven useful to support the architects into systematically analyze and decide upon a case.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889224"}, {"primary_key": "4128597", "vector": [], "sparse_vector": [], "title": "Automated test suite generation for time-continuous simulink models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "All engineering disciplines are founded and rely on models, although they may differ on purposes and usages of modeling. Interdisciplinary domains such as Cyber Physical Systems (CPSs) seek approaches that incorporate different modeling needs and usages. Specifically, the Simulink modeling platform greatly appeals to CPS engineers due to its seamless support for simulation and code generation. In this paper, we propose a test generation approach that is applicable to Simulink models built for both purposes of simulation and code generation. We define test inputs and outputs as signals that capture evolution of values over time. Our test generation approach is implemented as a meta-heuristic search algorithm and is guided to produce test outputs with diverse shapes according to our proposed notion of diversity. Our evaluation, performed on industrial and public domain models, demonstrates that: (1) In contrast to the existing tools for testing Simulink models that are only applicable to a subset of code generation models, our approach is applicable to both code generation and simulation Simulink models. (2) Our new notion of diversity for output signals outperforms random baseline testing and an existing notion of signal diversity in revealing faults in Simulink models. (3) The fault revealing ability of our test generation approach outperforms that of the Simulink Design Verifier, the only testing toolbox for Simulink.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884797"}, {"primary_key": "4128598", "vector": [], "sparse_vector": [], "title": "SimCoTest: a test suite generation tool for simulink/stateflow controllers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present SimCoTest, a tool to generate small test suites with high fault revealing ability for Simulink/Stateflow controllers. SimCoTest uses meta-heuristic search to (1) maximize the likelihood of presence of specific failure patterns in output signals (failure-based test generation), and to (2) maximize diversity of output signal shapes (output diversity test generation). SimCoTest has been evaluated on industrial Simulink models and has been systematically compared with Simuilnk Design Verifier (SLDV), an alternative commercial Simulink testing tool. Our results show that the fault revealing ability of SimCoTest outperforms that of SLDV. Further, in contrast to SLDV, SimCoTest is applicable to Simulink/Stateflow models in their entirety. A video describing the main features of SimCoTest is available at: https://youtu.be/YnXgveiGXEA", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889162"}, {"primary_key": "4128599", "vector": [], "sparse_vector": [], "title": "How surveys, tutors, and software help to assess Scrum adoption in a classroom software engineering project.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Agile methods are best taught in a hands-on fashion in realistic projects. The main challenge in doing so is to assess whether students apply the methods correctly without requiring complete supervision throughout the entire project.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889182"}, {"primary_key": "4128602", "vector": [], "sparse_vector": [], "title": "JDeodorant: clone refactoring.", "authors": ["Davood Mazinanian", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Code duplication is widely recognized as a potentially harmful code smell for the maintenance of software systems. In this demonstration, we present a tool, developed as part of the JDeodorant Eclipse plug-in, which offers cutting-edge features for the analysis and refactoring of clones found in Java projects. https://youtu.be/K_xAEqIEJ-4", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889168"}, {"primary_key": "4128605", "vector": [], "sparse_vector": [], "title": "Collaborative software engineering education between college seniors and blind high school students.", "authors": ["<PERSON>", "<PERSON>-<PERSON>"], "summary": "We describe a collaborative software engineering course between sighted college students and high school students with visual impairments. We designed the course as a mentorship experience, in which one college student mentor is connected to one high school student mentee. Each pair of students is responsible for a programming project. The students must learn to communicate programming concepts and software designs, to work with colleagues with very different levels of software engineering knowledge, and to overcome problems related to visual accessibility. We have implemented our course in a pilot program with five mentors and five mentees. This paper covers our course design, initial experiences, and recommendations for future implementations.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889188"}, {"primary_key": "4128608", "vector": [], "sparse_vector": [], "title": "Angelix: scalable multiline program patch synthesis via symbolic analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Since debugging is a time-consuming activity, automated program repair tools such as GenProg have garnered interest. A recent study revealed that the majority of GenProg repairs avoid bugs simply by deleting functionality. We found that SPR, a state-of-the-art repair tool proposed in 2015, still deletes functionality in their many \"plausible\" repairs. Unlike generate-and-validate systems such as GenProg and SPR, semantic analysis based repair techniques synthesize a repair based on semantic information of the program. While such semantics-based repair methods show promise in terms of quality of generated repairs, their scalability has been a concern so far. In this paper, we present Angelix, a novel semantics-based repair method that scales up to programs of similar size as are handled by search-based repair tools such as GenProg and SPR. This shows that Angelix is more scalable than previously proposed semantics based repair methods such as SemFix and DirectFix. Furthermore, our repair method can repair multiple buggy locations that are dependent on each other. Such repairs are hard to achieve using SPR and GenProg. In our experiments, Angelix generated repairs from large-scale real-world software such as wireshark and php, and these generated repairs include multi-location repairs. We also report our experience in automatically repairing the well-known Heartbleed vulnerability.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884807"}, {"primary_key": "4128609", "vector": [], "sparse_vector": [], "title": "Safely evolving preprocessor-based configurable systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Since the 70s, the C preprocessor is still widely used in practice in a numbers of projects, including Apache, Linux, and Libssh, to tailor systems to different platforms. To better understand the C preprocessor challenges, we conducted 40 interviews and a survey among 202 developers. We found that developers deal with three common problems: configuration-related bugs, combinatorial testing, and code comprehension. To safely evolve preprocessor-based configurable systems, we proposed strategies to detect preprocessor-related bugs and bad smells, and a set of 16 refactorings to remove bad smells. To better deal with exponential configuration spaces, we compared 10 sampling algorithms with respect to effort (i.e., number of configurations to test) and bug-detection capabilities (i.e., number of bugs detected in the sampled configurations). Based on the results, we proposed a sampling algorithm with a useful balance between effort and bug-detection capability. By evaluating the proposed solution using 40 popular projects, we found 131 preprocessor-related bugs and more than 5K opportunities to apply the refactorings in practice.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891036"}, {"primary_key": "4128610", "vector": [], "sparse_vector": [], "title": "A comparison of 10 sampling algorithms for configurable systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Almost every software system provides configuration options to tailor the system to the target platform and application scenario. Often, this configurability renders the analysis of every individual system configuration infeasible. To address this problem, researchers have proposed a diverse set of sampling algorithms. We present a comparative study of 10 state-of-the-art sampling algorithms regarding their fault-detection capability and size of sample sets. The former is important to improve software quality and the latter to reduce the time of analysis. In a nutshell, we found that sampling algorithms with larger sample sets are able to detect higher numbers of faults, but simple algorithms with small sample sets, such as most-enabled-disabled, are the most efficient in most contexts. Furthermore, we observed that the limiting assumptions made in previous work influence the number of detected faults, the size of sample sets, and the ranking of algorithms. Finally, we have identified a number of technical challenges when trying to avoid the limiting assumptions, which questions the practicality of certain sampling algorithms.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884793"}, {"primary_key": "4128611", "vector": [], "sparse_vector": [], "title": "FeatureIDE: taming the preprocessor wilderness.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Preprocessors are a common way to implement variability in software. They are used in numerous software systems, such as operating systems and databases. Due to the ability of preprocessors to enable and disable code fragments, not all parts of the program are active at the same time. Thus, programmers and tools need to handle the interactions resulting from annotations in the program. With our Eclipse-based tool FeatureIDE, we provide tool support to tackle multiple challenges with preprocessors, such as code comprehension, feature traceability, separation of concerns, and program analysis. With FeatureIDE, instead of focusing on one particular preprocessor, we provide tool support, which can easily be adopted for further preprocessors. Currently, we support development with CPP, Antenna, and Munge. https://youtu.be/jVe7f32mLCQ", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889175"}, {"primary_key": "4128613", "vector": [], "sparse_vector": [], "title": "How does the degree of variability affect bug finding?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software projects embrace variability to increase adaptability and to lower cost; however, others blame variability for increasing complexity and making reasoning about programs more difficult. We carry out a controlled experiment to quantify the impact of variability on debugging of preprocessor-based programs. We measure speed and precision for bug finding tasks defined at three different degrees of variability on several subject programs derived from real systems. The results show that the speed of bug finding decreases linearly with the number of features, while effectiveness of finding bugs is relatively independent of the degree of variability. Still, identifying the set of configurations in which the bug manifests itself is difficult already for a low number of features. Surprisingly, identifying the exact set of affected configurations appears to be harder than finding the bug in the first place. The difficulty in reasoning about several configurations is a likely reason why the variability bugs are actually introduced in configurable programs. We hope that the detailed findings presented here will inspire the creation of programmer support tools addressing the challenges faced by developers when reasoning about configurations, contributing to more effective debugging and, ultimately, fewer bugs in highly-configurable systems.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884831"}, {"primary_key": "4128614", "vector": [], "sparse_vector": [], "title": "Termination-checking for LLVM peephole optimizations.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Mainstream compilers contain a large number of peephole optimizations, which perform algebraic simplification of the input program with local rewriting of the code. These optimizations are a persistent source of bugs. Our recent research on Alive, a domain-specific language for expressing peephole optimizations in LLVM, addresses a part of the problem by automatically verifying the correctness of these optimizations and generating C++ code for use with LLVM.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884809"}, {"primary_key": "4128615", "vector": [], "sparse_vector": [], "title": "How not to do it: anti-patterns for data science in software engineering.", "authors": ["<PERSON>"], "summary": "Many books and papers describe how to do data science. While those texts are useful, it can also be important to reflect on anti-patterns; i.e. common classes of errors seen when large communities of researchers and commercial software engineers use, and misuse data mining tools. This technical briefing will present those errors and show how to avoid them.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891047"}, {"primary_key": "4128617", "vector": [], "sparse_vector": [], "title": "Mobile malware detection in the real world.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Several works in literature address the mobile malware detection problem by classifying features obtained from real world application and using well-known machine-learning techniques. Several authors have published empirical studies aimed at assessing the quality of set of features. In this paper we propose BehaveYourself!, an Android application able to discriminate a trusted application by a malicious one extracting opcode-based features. Our application is open and flexible: it can be used as a starting point to define, and experiment with, additional features. We release BehaveYourself! to the research community at the following url: http://www.ing.unisannio.it/cimitile/BehaveYourself.apk", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892656"}, {"primary_key": "4128618", "vector": [], "sparse_vector": [], "title": "Exploring process improvement decisions to support a rapidly evolving developer base.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Software engineers learn through experience and training to optimize their software development process through a dynamic cycle of assessment, planning, enactment, and evaluation. When needed, software process improvement (SPI) frameworks (SPIFs) fill in experiential and educational gaps. However, because of the needs that drove their original development, existing SPIFs assume either a specific set of situational criteria or enough software engineering (SE) expertise to select and tailor SE best practices as needed. Outlier projects involving novice software developers or projects with unique concerns are left to find their own path to a quality software product. With such scenarios becoming more prevalent as computing becomes pervasive across society, there is a growing need for flexible, accessible SPIFs that allow decision-makers of all skill levels to efficiently self-drive their SPI efforts according to their individual goals and constraints. This paper outlines how we, as a research community, can and must meet this need by addressing the underlying theoretical, empirical, and practical challenges.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889209"}, {"primary_key": "4128622", "vector": [], "sparse_vector": [], "title": "Assessing iterative practical software engineering courses with play money.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Changing our practical software engineering course from the previous waterfall model to a more agile and iterative approach created more severe assessment challenges. To cope with them we added an assessment concept based on play money. The concept not only includes weekly expenses to simulate real running costs but also investments, which correspond to assessment results of the submissions. This concept simulates a startup-like working environment and its financing in an university course. Our early evaluation shows that the combination of the iterative approach and the play money investments is motivating for many students. At this point we think that the combined approach has advantages from both the supervising and the students point of view. We planned more evaluations to better understand all its effects.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892660"}, {"primary_key": "4128624", "vector": [], "sparse_vector": [], "title": "Reducing combinatorics in GUI testing of android applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The rising popularity of Android and the GUI-driven nature of its apps have motivated the need for applicable automated GUI testing techniques. Although exhaustive testing of all possible combinations is the ideal upper bound in combinatorial testing, it is often infeasible, due to the combinatorial explosion of test cases. This paper presents TrimDroid, a framework for GUI testing of Android apps that uses a novel strategy to generate tests in a combinatorial, yet scalable, fashion. It is backed with automated program analysis and formally rigorous test generation engines. TrimDroid relies on program analysis to extract formal specifications. These specifications express the app's behavior (i.e., control flow between the various app screens) as well as the GUI elements and their dependencies. The dependencies among the GUI elements comprising the app are used to reduce the number of combinations with the help of a solver. Our experiments have corroborated TrimDroid's ability to achieve a comparable coverage as that possible under exhaustive GUI testing using significantly fewer test cases.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884853"}, {"primary_key": "4128626", "vector": [], "sparse_vector": [], "title": "Learning Agile software development in high school: an investigation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Context: Empirical investigations regarding using Agile programming methodologies in high schools are scarce in the literature.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889180"}, {"primary_key": "4128628", "vector": [], "sparse_vector": [], "title": "Decoupling level: a new metric for architectural maintenance complexity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Despite decades of research on software metrics, we still cannot reliably measure if one design is more maintainable than another. Software managers and architects need to understand whether their software architecture is \"good enough\", whether it is decaying over time and, if so, by how much. In this paper, we contribute a new architecture maintainability metric---Decoupling Level (DL)---derived from <PERSON> and <PERSON>'s option theory. Instead of measuring how coupled an architecture is, we measure how well the software can be decoupled into small and independently replaceable modules. We measured the DL for 108 open source projects and 21 industrial projects, each of which has multiple releases. Our main result shows that the larger the DL, the better the architecture. By \"better\" we mean: the more likely bugs and changes can be localized and separated, and the more likely that developers can make changes independently. The DL metric also opens the possibility of quantifying canonical principles of single responsibility and separation of concerns, aiding cross-project comparison and architecture decay monitoring.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884825"}, {"primary_key": "4128630", "vector": [], "sparse_vector": [], "title": "Implications of requirements engineering on software design: a cognitive insight.", "authors": ["<PERSON><PERSON>"], "summary": "There is a broad consensus that understanding software requirements is critical in designing a good software system. Software engineering research literature is rife with state of the art practices and techniques that are proposed to improve requirements engineering techniques. Also, creative approaches are used to determine system requirements in a better way. However, little empirical research has investigated the critical underpinnings of human psychology on design performance from elicited requirements, and its implications on high-level design creativity. The foremost objective of this research will be to explore the harmful effects of human cognitive biases on the designers' ability to generate conceptual designs from a set of requirements, and then to mitigate the harmful effects of biases by developing specific de-biasing technique(s) to enhance creativity of such design concepts. The research will adopt a quantitative approach, i.e., data will be collected through controlled experiments; also the existing literature will be reviewed. Exploring the manifestation of cognitive biases and its subsequent mitigation techniques shall help augmenting the focus of software engineering design research approach from technological to psychological.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889254"}, {"primary_key": "4128632", "vector": [], "sparse_vector": [], "title": "Scaling testing of refactoring engines.", "authors": ["<PERSON><PERSON>"], "summary": "Researchers have proposed a number of automated techniques for testing refactoring engines. However, they may have limitations related to the program generator, time consumption, kinds of bugs, and debugging. We propose a technique to scale testing of refactoring engines. We improve expressiveness of a program generator, use a technique to skip some test inputs to improve performance, and propose new oracles to detect behavioral changes using change impact analysis, overly strong conditions using mutation testing, and transformation issues related to the refactoring definitions. We evaluate our technique in 24 refactoring implementations of Java (Eclipse and JRRT) and C (Eclipse) and found 119 bugs. The technique reduces the time in 96% using skips while misses only 7% of the bugs. Using the new oracle to identify overly strong conditions, it detects 37% of new bugs while misses 16% of the bugs comparing with a previous technique. Furthermore, the proposed oracle facilitates debugging by indicating the overly strong conditions.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891038"}, {"primary_key": "4128634", "vector": [], "sparse_vector": [], "title": "Fixing bug reporting for mobile and GUI-based applications.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Smartphones and tablets have established themselves as mainstays in the modern computing landscape. It is conceivable that in the near future such devices may supplant laptops and desktops, becoming many users primary means of carrying out typical computer assisted tasks. In turn, this means that mobile applications will continue on a trajectory to becoming more complex, and the primary focus of millions of developers worldwide. In order to properly create and maintain these \"apps\" developers will need support, especially with regard to the prompt confirmation and resolution of bug reports. Unfortunately, current issue tracking systems typically only implement collection of coarse grained natural language descriptions, and lack features to facilitate reporters including important information in their reports. This illustrates the lexical information gap that exists in current bug reporting systems for mobile and GUI-based apps. This paper outlines promising preliminary work towards addressing this problem and proposes a comprehensive research program which aims to implement new bug reporting mechanisms and examine the impact that they might have on related software maintenance tasks.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889269"}, {"primary_key": "4128635", "vector": [], "sparse_vector": [], "title": "FUSION: a tool for facilitating and augmenting android bug reporting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "As the popularity of mobile smart devices continues to climb the complexity of \"apps\" continues to increase, making the development and maintenance process challenging. Current bug tracking systems lack key features to effectively support construction of reports with actionable information that directly lead to a bug's resolution. In this demo we present the implementation of a novel bug reporting system, called Fusion, that facilitates users including reproduction steps in bug reports for mobile apps. Fusion links user-provided information to program artifacts extracted through static and dynamic analysis performed before testing or release. Results of preliminary studies demonstrate that Fusion both effectively facilitates reporting and allows for more reliable reproduction of bugs from reports compared to traditional issue tracking systems by presenting more detailed contextual app information. Tool website: www.fusion-android.com Video url: https://youtu.be/AND9h0ElxRg", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889177"}, {"primary_key": "4128636", "vector": [], "sparse_vector": [], "title": "Enriching traditional software engineering curricula with software project management knowledge.", "authors": ["<PERSON>", "<PERSON>", "Fuensanta Medina-Domínguez", "<PERSON>", "<PERSON>"], "summary": "Training existing and future software project managers presents a challenge to the academic community. Software project management is usually taught as part of software engineering bachelor or master programs which are generally based on SWEBOK. However, evidence shows that even when SWEBOK provides some software project management knowledge it is not enough to satisfy the software industry requirements. On the other hand, the Software Extension to the PMBOK Guide was recently published. The extension complements the original PMBOK with software specific contents, so it constitutes valuable referential information for software project managers. This paper describes a smooth transition to enrich our traditional software engineering curricula based on SWEBOK with specific software project management knowledge. To that end, we identify which software project management knowledge suggested by the Software Extension to the PMBOK provides special added value to the SWEBOK contents. The results can be useful for academia but also for software engineering practitioners that can identify training opportunities to complement their SWEBOK background.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889193"}, {"primary_key": "4128640", "vector": [], "sparse_vector": [], "title": "Using (bio)metrics to predict code quality online.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Finding and fixing code quality concerns, such as defects or poor understandability of code, decreases software development and evolution costs. A common industrial practice to identify code quality concerns early on are code reviews. While code reviews help to identify problems early on, they also impose costs on development and only take place after a code change is already completed. The goal of our research is to automatically identify code quality concerns while a developer is making a change to the code. By using biometrics, such as heart rate variability, we aim to determine the difficulty a developer experiences working on a part of the code as well as identify and help to fix code quality concerns before they are even committed to the repository.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884803"}, {"primary_key": "4128642", "vector": [], "sparse_vector": [], "title": "Is continuous adoption in software engineering achievable and desirable?", "authors": ["<PERSON>"], "summary": "Continuity in software development is all about shortening cycle times. For example, continuous integration shortens the time to integrating changes from multiple developers and continuous delivery shortens the time to get those integrated changes into the hands of users. Although it is now possible to get multiple new versions of complex software systems released per day, it still often takes years, if ever, to get software engineering research results into use by software development teams. What would software engineering research and software engineering development look like if we could shorten the cycle time from taking a research result into practice? What can we learn from how continuity in development is performed to make it possible to achieve continuous adoption of research results? Do we even want to achieve continuous adoption? In this talk, I will explore these questions, drawing from experiences I have gained in helping to take a research idea to market and from insights learned from interviewing industry leaders.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2893462"}, {"primary_key": "4128646", "vector": [], "sparse_vector": [], "title": "Jumping through hoops: why do Java developers struggle with cryptography APIs?", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "To protect sensitive data processed by current applications, developers, whether security experts or not, have to rely on cryptography. While cryptography algorithms have become increasingly advanced, many data breaches occur because developers do not correctly use the corresponding APIs. To guide future research into practical solutions to this problem, we perform an empirical investigation into the obstacles developers face while using the Java cryptography APIs, the tasks they use the APIs for, and the kind of (tool) support they desire. We triangulate data from four separate studies that include the analysis of 100 StackOverflow posts, 100 GitHub repositories, and survey input from 48 developers. We find that while developers find it difficult to use certain cryptographic algorithms correctly, they feel surprisingly confident in selecting the right cryptography concepts (e.g., encryption vs. signatures). We also find that the APIs are generally perceived to be too low-level and that developers prefer more task-based solutions.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884790"}, {"primary_key": "4128651", "vector": [], "sparse_vector": [], "title": "Finding security bugs in web applications using a catalog of access control patterns.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We propose a specification-free technique for finding missing security checks in web applications using a catalog of access control patterns in which each pattern models a common access control use case. Our implementation, Space, checks that every data exposure allowed by an application's code matches an allowed exposure from a security pattern in our catalog. The only user-provided input is a mapping from application types to the types of the catalog; the rest of the process is entirely automatic. In an evaluation on the 50 most watched Ruby on Rails applications on Github, Space reported 33 possible bugs---23 previously unknown security bugs, and 10 false positives.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884836"}, {"primary_key": "4128653", "vector": [], "sparse_vector": [], "title": "srcSlice: a tool for efficient static forward slicing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "An efficient lightweight forward static slicing tool is presented. The tool is implemented on top of srcML, an XML representation of source code. The approach does not compute the full program dependence graph but instead dependency information is computed as needed while computing the slice on a variable. The result is a list of line numbers, dependent variables, aliases, and function calls that are part of the slice for a given variable. The tool produces the slice for all variables in a system. The approach is highly scalable and can generate the slices for all variables of the Linux kernel in less than 15 minutes. A demonstration video is at: https://youtu.be/McvFUVSGg-g", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889173"}, {"primary_key": "4128654", "vector": [], "sparse_vector": [], "title": "Characterizing API elements in software documentation with vector representation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In software engineering (SE), documentation for developers (e.g., developers' guide, API documentation), users' documentation, informal documentation [7, 13] (e.g., developers' forums, mailing lists, development communities' discussions, etc.), and issue reports are of much interest for software engineers. This so-called software documentation is a crucial resource for them in understanding various aspects of a software development process.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892658"}, {"primary_key": "4128655", "vector": [], "sparse_vector": [], "title": "Mapping API elements for code migration with vector representations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Problem. Code migration between languages is challenging partly because different languages require developers to use different software libraries and frameworks. For example, in Java, Java Development Kit library (JDK) is a popular toolkit while .NET is the main framework used in C# software development. Code migration requires not only the mappings between the language constructs (e.g., statements, expressions) but also the mappings among the APIs of the libraries/frameworks used in two languages. For example, in Java, to write to a file, one can use FileWriter.write of FileWriter, and in C#, one can achieve the same function with StreamWriter.Write of StreamWriter. Such mapping is called API mapping.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892661"}, {"primary_key": "4128656", "vector": [], "sparse_vector": [], "title": "Learning API usages from bytecode: a statistical approach.", "authors": ["<PERSON> Ng<PERSON>en", "Hung V<PERSON> Pham", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile app developers rely heavily on standard API frameworks and libraries. However, learning API usages is often challenging due to the fast-changing nature of API frameworks for mobile systems and the insufficiency of API documentation and source code examples. In this paper, we propose a novel approach to learn API usages from bytecode of Android mobile apps. Our core contributions include HAPI, a statistical model of API usages and three algorithms to extract method call sequences from apps' bytecode, to train HAPI based on those sequences, and to recommend method calls in code completion using the trained HAPIs. Our empirical evaluation shows that our prototype tool can effectively learn API usages from 200 thousand apps containing 350 million method sequences. It recommends next method calls with top-3 accuracy of 90% and outperforms baseline approaches on average 10--20%.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884873"}, {"primary_key": "4128662", "vector": [], "sparse_vector": [], "title": "Visually reasoning about system and resource behavior.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Understanding how software utilizes resources is an important software engineering task. Existing software comprehension approaches rarely consider how resource utilization affects system behavior. We present Perfume, a general-purpose tool to help developers understand how resource utilization impacts their systems' control flow. Perfume is broadly applicable, as it is configurable to parse a wide variety of execution log formats and applies to all resource types that can be represented numerically. Perfume mines temporal properties that hold over the logged executions and represents system behavior in a resource finite state automaton that satisfies the mined properties. Perfume's interactive interface allows the developers to understand system behavior and to formulate and test hypotheses about system executions. A controlled experiment with 40 students shows that Perfume effectively supports understanding and debugging tasks. Students using Perfume answered 8.3% more questions correctly than those using execution logs alone and did so 15.5% more quickly. Perfume is open source and deployed at http://perfume.cs.umass.edu/.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889166"}, {"primary_key": "4128663", "vector": [], "sparse_vector": [], "title": "Code anomalies flock together: exploring code anomaly agglomerations for locating design problems.", "authors": ["<PERSON><PERSON>", "Alessandro <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Design problems affect every software system. Diverse software systems have been discontinued or reengineered due to design problems. As design documentation is often informal or nonexistent, design problems need to be located in the source code. The main difficulty to identify a design problem in the implementation stems from the fact that such problem is often scattered through several program elements. Previous work assumed that code anomalies -- popularly known as code smells -- may provide sufficient hints about the location of a design problem. However, each code anomaly alone may represent only a partial embodiment of a design problem. In this paper, we hypothesize that code anomalies tend to \"flock together\" to realize a design problem. We analyze to what extent groups of inter-related code anomalies, named agglomerations, suffice to locate design problems. We analyze more than 2200 agglomerations found in seven software systems of different sizes and from different domains. Our analysis indicates that certain forms of agglomerations are consistent indicators of both congenital and evolutionary design problems, with accuracy often higher than 80%.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884868"}, {"primary_key": "4128664", "vector": [], "sparse_vector": [], "title": "Sustainability debt: a portfolio-based approach for evaluating sustainability requirements in architectures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Architectural Sustainability refers to the ability of an architecture to achieve its goals while sustaining its value on dimensions related to environmental, social, economic, individual and/or technical during its operation and evolution. While the process of architectural design implies a fit between the requirements, system conditions and constraints; incomplete information and uncertainty may increase the cost of the architecture, introduce risks, alter its value and influence the extent to which it can evolve and sustain. We propose an economics-driven architectural evaluation method which extends the Cost Benefits Analysis Method (CBAM) and integrates principles of modern portfolio theory to control the risks when linking sustainability concern to architectural design decisions. The method aims at identifying portfolio(s) of architecture design decisions which are more promising for adding/delivering value while reducing risk on the sustainability dimensions. The method quantifies the sustainability debt of these decisions. The ultimate goal is to develop an objective decision-support framework for reasoning about sustainability requirements in relation architecture decisions in the presence of uncertainty. We evaluate the approach with an Emergency Deployment System (EDS). The results show that the method can make the value, cost and risks of architectural design decisions and sustainability requirements explicit.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889218"}, {"primary_key": "4128665", "vector": [], "sparse_vector": [], "title": "When more heads are better than one?: understanding and improving collaborative identification of code smells.", "authors": ["<PERSON>"], "summary": "Code smells are program structures that often indicate software design problems. Their efficient identification is required in order to ensure software longevity. However, the identification of code smells often cannot be performed in isolation by a single developer. This task might require the knowledge of various program parts, which are better understood by different developers. However, there is little guidance to support software teams on efficient identification of code smells. In this research, we investigate how to improve efficiency on the collaborative identification of code smells. Our investigation is based on a set of controlled experiments conducted with more than 58 novice and professional developers. Our preliminary results suggest the use of collaborative practices significantly increases the efficiency of code smell identification. We also compiled a set of guidelines and heuristics to support an effective collaborative strategy for code smell identification.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889272"}, {"primary_key": "4128667", "vector": [], "sparse_vector": [], "title": "Cross-supervised synthesis of web-crawlers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A web-crawler is a program that automatically and systematically tracks the links of a website and extracts information from its pages. Due to the different formats of websites, the crawling scheme for different sites can differ dramatically. Manually customizing a crawler for each specific site is time consuming and error-prone. Furthermore, because sites periodically change their format and presentation, crawling schemes have to be manually updated and adjusted. In this paper, we present a technique for automatic synthesis of web-crawlers from examples. The main idea is to use hand-crafted (possibly partial) crawlers for some websites as the basis for crawling other sites that contain the same kind of information. Technically, we use the data on one site to identify data on another site. We then use the identified data to learn the website structure and synthesize an appropriate extraction scheme. We iterate this process, as synthesized extraction schemes result in additional data to be used for re-learning the website structure. We implemented our approach and automatically synthesized 30 crawlers for websites from nine different categories: books, TVs, conferences, universities, cameras, phones, movies, songs, and hotels.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884842"}, {"primary_key": "4128672", "vector": [], "sparse_vector": [], "title": "Shadow of a doubt: testing for divergences between software versions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While developers are aware of the importance of comprehensively testing patches, the large effort involved in coming up with relevant test cases means that such testing rarely happens in practice. Furthermore, even when test cases are written to cover the patch, they often exercise the same behaviour in the old and the new version of the code.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884845"}, {"primary_key": "4128675", "vector": [], "sparse_vector": [], "title": "The impact of test case summaries on bug fixing performance: an empirical investigation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Automated test generation tools have been widely investigated with the goal of reducing the cost of testing activities. However, generated tests have been shown not to help developers in detecting and finding more bugs even though they reach higher structural coverage compared to manual testing. The main reason is that generated tests are difficult to understand and maintain. Our paper proposes an approach, coined TestDescriber, which automatically generates test case summaries of the portion of code exercised by each individual test, thereby improving understandability. We argue that this approach can complement the current techniques around automated unit test generation or search-based techniques designed to generate a possibly minimal set of test cases. In evaluating our approach we found that (1) developers find twice as many bugs, and (2) test case summaries significantly improve the comprehensibility of test cases, which is considered particularly useful by developers.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884847"}, {"primary_key": "4128676", "vector": [], "sparse_vector": [], "title": "STAGE: a software tool for automatic grading of testing exercises: case study paper.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We report on an approach and associated tool-support for automatically evaluating and grading exercises in Software Engineering courses, by connecting various third-party tools to the online learning platform Moodle. In the case study presented here, the tool was used in several instances of a lecture course to automatically measure the test coverage criteria wrt. the test cases defined by the students for given Java code. We report on empirical evidence gathered using this case-study (involving more than 250 students), including the results of a survey conducted after the exercises (which yielded positive feedback from the students), as well as a performance evaluation of our tool implementation.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889203"}, {"primary_key": "4128677", "vector": [], "sparse_vector": [], "title": "Battles with false positives in static analysis of JavaScript web applications in the wild.", "authors": ["Joonyoung Park", "Inho Lim", "Sukyoung Ryu"], "summary": "Now that HTML5 technologies are everywhere from web services to various platforms, assuring quality of web applications becomes very important. While web application developers use syntactic checkers and type-related bug detectors, extremely dynamic features and diverse execution environments of web applications make it particularly difficult to statically analyze them leading to too many false positives. Recently, researchers have developed static analyzers for JavaScript web applications addressing quirky JavaScript language semantics and browser environments, but they lack empirical studies on the practicality of such analyzers.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889227"}, {"primary_key": "4128681", "vector": [], "sparse_vector": [], "title": "Making a difference: an overview of humanitarian free open source systems.", "authors": ["Esteban Parra", "<PERSON>", "<PERSON>"], "summary": "Humanitarian Free Open Source Software (HFOSS) serves philanthropic goals that usually benefit non-profit organizations meant to improve the human condition. The altruistic goals these systems serve can offer developers additional motivation for contributing to OSS and have been seen as a way to attract more women to computing majors and to improve students' learning.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892651"}, {"primary_key": "4128690", "vector": [], "sparse_vector": [], "title": "Too long; didn&apos;t watch!: extracting relevant fragments from software development video tutorials.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "When knowledgeable colleagues are not available, developers resort to offline and online resources, e.g., tutorials, mailing lists, and Q&A websites. These, however, need to be found, read, and understood, which takes its toll in terms of time and mental energy. A more immediate and accessible resource are video tutorials found on the web, which in recent years have seen a steep increase in popularity. Nonetheless, videos are an intrinsically noisy data source, and finding the right piece of information might be even more cumbersome than using the previously mentioned resources.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884824"}, {"primary_key": "4128691", "vector": [], "sparse_vector": [], "title": "CodeTube: extracting relevant fragments from software development video tutorials.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Nowadays developers heavily rely on sources of informal documentation, including Q&A forums, slides, or video tutorials, the latter being particularly useful to provide introductory notions for a piece of technology. The current practice is that developers have to browse sources individually, which in the case of video tutorials is cumbersome, as they are lengthy and cannot be searched based on their contents.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889172"}, {"primary_key": "4128692", "vector": [], "sparse_vector": [], "title": "Facing the challenges of teaching requirements engineering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> Prado Le<PERSON>"], "summary": "This paper reports on our experience of teaching Requirements Engineering for undergraduate students. It is well known, the obstacles educators have in teaching requirements engineering. These obstacles are related to the very nature of requirements engineering: a multidisciplinary field that deals with both computer science and social sciences concepts. Teaching requirements engineering just with problems descriptions, as a basis for the construction of requirements specifications or requirements models, misses the point. Educators should also provide students with ways of gathering client information. However, to be effective in this regard, there is the need that students interact with clients. Our pedagogical strategy is designed to tackle these challenges. Notwithstanding, we need to have feedback about the strategy, which lead to the design of an assessment to gauge the efficacy of our pedagogical strategy. We, describe the strategy, stress its novelty in facing the challenges, and provide assessment results over 3 semesters.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889200"}, {"primary_key": "4128695", "vector": [], "sparse_vector": [], "title": "Quality experience: a grounded theory of successful agile projects without dedicated testers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Context: While successful conventional software development regularly employs separate testing staff, there are successful agile teams with as well as without separate testers. Question: How does successful agile development work without separate testers? What are advantages and disadvantages? Method: A case study, based on Grounded Theory evaluation of interviews and direct observation of three agile teams; one having separate testers, two without. All teams perform long-term development of parts of e-business web portals. Results: Teams without testers use a quality experience work mode centered around a tight field-use feedback loop, driven by a feeling of responsibility, supported by test automation, resulting in frequent deployments. Conclusion: In the given domain, hand-overs to separate testers appear to hamper the feedback loop more than they contribute to quality, so working without testers is preferred. However, Quality Experience is achievable only with modular architectures and in suitable domains.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884789"}, {"primary_key": "4128696", "vector": [], "sparse_vector": [], "title": "JooMDD: a model-driven development environment for web content management system extensions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Developing software extensions for Web Content Management Systems (WCMSs) like Joomla, WordPress, or Drupal can be a difficult and time consuming process. In this demo we present JooMDD, an environment for model-driven development of software extensions for the WCMS Joomla. JooMDD allows the rapid development of standardised software extensions requiring reduced technological knowledge of Joomla. This implies that even inexperienced developers are able to create their own functional WCMS extensions. This demonstrates that a model-driven approach is suitable for the domain of WCMSs.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889176"}, {"primary_key": "4128697", "vector": [], "sparse_vector": [], "title": "MobiPlay: a remote execution based record-and-replay tool for mobile applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The record-and-replay approach for software testing is important and valuable for developers in designing mobile applications. However, the existing solutions for recording and replaying Android applications are far from perfect. When considering the richness of mobile phones' input capabilities including touch screen, sensors, GPS, etc., existing approaches either fall short of covering all these different input types, or require elevated privileges that are not easily attained and can be dangerous. In this paper, we present a novel system, called MobiPlay, which aims to improve record-and-replay testing. By collaborating between a mobile phone and a server, we are the first to capture all possible inputs by doing so at the application layer, instead of at the Android framework layer or the Linux kernel layer, which would be infeasible without a server. MobiPlay runs the to-be-tested application on the server under exactly the same environment as the mobile phone, and displays the GUI of the application in real time on a thin client application installed on the mobile phone. From the perspective of the mobile phone user, the application appears to be local. We have implemented our system and evaluated it with tens of popular mobile applications showing that MobiPlay is efficient, flexible, and comprehensive. It can record all input data, including all sensor data, all touchscreen gestures, and GPS. It is able to record and replay on both the mobile phone and the server. Furthermore, it is suitable for both white-box and black-box testing.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884854"}, {"primary_key": "4128698", "vector": [], "sparse_vector": [], "title": "Integrating automatic backward error recovery in asynchronous rich clients.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Rich Web Clients allow developers to manage data locally and update it from the server by means of asynchronous requests, thus providing more interactive interfaces and an improved user experience. On the other hand, they face concerning challenges regarding error management. When there is a need to update the local data through multiple asynchronous requests and it is required that all them succeed, an error on a single call can lead to having incorrect information shown to the user. Consequently, developers need to explicitly implement proper recovery mechanisms, a task that most of times is complex and highly error prone, leading to tangled code and harder maintenance, especially in an asynchronous environment. These problems could be lessened through automatic error recovery techniques, but the existing state of the art for Rich Web Client development does not support recovery from asynchronous scenarios. To cope with this problem we extended the existing error recovery technique of Reconstructors, adding to it the capability of recovering the state in the presence of several asynchronous requests. We applied this technology in a widely used open source project for rendering interactive charts, ChartJs, thus allowing the developer to effortlessly guarantee that the data shown to the user, even when it results from multiple asynchronous requests, is never inconsistent. We compare our proposal to other solutions using state of the art approaches and verify that by using Reconstructors the overall implementation requires 39.16% less lines of code, and 5.66 times less lines of code are dedicated specifically to error management, while avoiding code tangling completely. Execution time showed by reconstructors is between 5.2% and 9.3% slower than other solutions, a cost we believe is worth its benefits, and feasible for using these techniques in real world client applications.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889241"}, {"primary_key": "4128699", "vector": [], "sparse_vector": [], "title": "Assessing the usefulness of a requirements monitoring tool: a study involving industrial software engineers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Requirements monitoring approaches support defining and checking the run-time behavior and performance characteristics of complex software systems. However, although numerous monitoring tools have been described in the literature, hardly any empirical studies exist on their usefulness for software engineering practitioners. Empirical data on usefulness, however, is important for practitioners to select and adapt the capabilities of monitoring tools for their application context. This paper first describes common capabilities of requirements monitoring tools and then empirically assesses the usefulness of these capabilities as implemented in the monitoring tool ReMinds. We report findings from an initial assessment of the tool we performed using the Cognitive Dimensions of Notations Framework. We then present results of a usefulness study involving software engineers of a large company from the domain of automation software. Finally, we discuss implications for developers of requirements monitoring tools.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889234"}, {"primary_key": "4128700", "vector": [], "sparse_vector": [], "title": "SWIM: synthesizing what i mean: code search and idiomatic snippet synthesis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern programming frameworks come with large libraries, with diverse applications such as for matching regular expressions, parsing XML files and sending email. Programmers often use search engines such as Google and Bing to learn about existing APIs. In this paper, we describe SWIM, a tool which suggests code snippets given API-related natural language queries such as \"generate md5 hash code\". We translate user queries into the APIs of interest using clickthrough data from the Bing search engine. Then, based on patterns learned from open-source code repositories, we synthesize idiomatic code describing the use of these APIs. We introduce \\emph{structured call sequences} to capture API-usage patterns. Structured call sequences are a generalized form of method call sequences, with if-branches and while-loops to represent conditional and repeated API usage patterns, and are simple to extract and amenable to synthesis. We evaluated SWIM with 30 common C# API-related queries received by <PERSON>. For 70% of the queries, the first suggested snippet was a relevant solution, and a relevant solution was present in the top 10 results for all benchmarked queries. The online portion of the workflow is also very responsive, at an average of 1.5 seconds per snippet.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884808"}, {"primary_key": "4128701", "vector": [], "sparse_vector": [], "title": "Trace link evolution across multiple software versions in safety-critical systems.", "authors": ["<PERSON>"], "summary": "Traceability is defined by the Center of Excellence for Software Traceability as \"the ability to interrelate any uniquely identifiable software engineering artifact to any other, maintain required links over time, and use the resulting network to answer questions of both the software product and its development process\". It is an essential element in the software development process, especially for Safety-Critical systems where trace links are used to demonstrate that all hazards and faults are mitigated in the delivered system. Maintaining trace links as a software system evolves over time is difficult, error-prone and costly. As a result, trace links tend to become outdated and safety-cases, which rely on trace links and associated data to compose arguments and evidence for system safety are invalidated. In practice, software developers must therefore painstakingly update trace links in order to re-validate safety-cases whenever they seek re-certification of their product. While a significant body of work exists in the area of trace link creation, very limited research has focused on the problem of link evolution. The goal of this dissertation is therefore to deliver an accurate, scalable, and practical approach for supporting trace link evolution across a diverse set of software artifacts including requirements, source code and safety cases.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889261"}, {"primary_key": "4128702", "vector": [], "sparse_vector": [], "title": "CoRReCT: code reviewer recommendation in GitHub based on cross-project and technology experience.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Peer code review locates common coding rule violations and simple logical errors in the early phases of software development, and thus reduces overall cost. However, in GitHub, identifying an appropriate code reviewer for a pull request is a non-trivial task given that reliable information for reviewer identification is often not readily available. In this paper, we propose a code reviewer recommendation technique that considers not only the relevant cross-project work history (e.g., external library experience) but also the experience of a developer in certain specialized technologies associated with a pull request for determining her expertise as a potential code reviewer. We first motivate our technique using an exploratory study with 10 commercial projects and 10 associated libraries external to those projects. Experiments using 17,115 pull requests from 10 commercial projects and six open source projects show that our technique provides 85%-- 92% recommendation accuracy, about 86% precision and 79%--81% recall in code reviewer recommendation, which are highly promising. Comparison with the state-of-the-art technique also validates the empirical findings and the superiority of our recommendation technique.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889244"}, {"primary_key": "4128704", "vector": [], "sparse_vector": [], "title": "RDIT: race detection from incomplete traces.", "authors": ["<PERSON><PERSON>"], "summary": "We present RDIT, a novel dynamic algorithm to precisely detect data races in multi-threaded programs with incomplete trace information - the presence of missing events. RDIT enhances the Happens-Before algorithm by relaxing the need to collect the full execution trace, while still being precise and maximal i.e, it detects a maximal set of true data races while generating no false positives. Our approach is based on a sound BarrierPair model that abstracts away missing events by capturing the invocation data of their enclosing methods. By making the least conservative abstraction and by formulating maximal thread causality as logical constraints, we can detect a maximal set of true races from the information available.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891039"}, {"primary_key": "4128706", "vector": [], "sparse_vector": [], "title": "Discovering &quot;unknown known&quot; security requirements.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Security is one of the biggest challenges facing organisations in the modern hyper-connected world. A number of theoretical security models are available that provide best practice security guidelines and are widely utilised as a basis to identify and operationalise security requirements. Such models often capture high-level security concepts (e.g., whitelisting, secure configurations, wireless access control, data recovery, etc.), strategies for operationalising such concepts through specific security controls, and relationships between the various concepts and controls. The threat landscape, however, evolves leading to new tacit knowledge that is embedded in or across a variety of security incidents. These unknown knowns alter, or at least demand reconsideration of the theoretical security models underpinning security requirements. In this paper, we present an approach to discover such unknown knowns through multi-incident analysis. The approach is based on a novel combination of grounded theory and incident fault trees. We demonstrate the effectiveness of the approach through its application to identify revisions to a theoretical security model widely used in industry.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884785"}, {"primary_key": "4128707", "vector": [], "sparse_vector": [], "title": "Do biases related to geographical location influence work-related decisions in GitHub?", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Visible demographic characteristics are seen as elements of bias in offline work environments. In this study, we investigate the influence of the geographical location on the evaluation of pull requests in GitHub -- the most popular online collaborative code development environment. We use a mixed-methods approach and present analyses of 70,000+ pull requests and 2,500+ survey responses. Quantitative analysis of GitHub projects' data suggests that the geographical location significantly explains the pull request acceptance decisions. These observations are in agreement with the perceptions of submitters based on their experiences with bias. Integrators feel that it is easy to work with contributors from the same geographical location and that they encourage contributors from the same geographical location. However, integrators do not feel that contributors from some countries are better at writing pull requests compared to others.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891035"}, {"primary_key": "4128709", "vector": [], "sparse_vector": [], "title": "On the &quot;naturalness&quot; of buggy code.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Ray", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Real software, the kind working programmers produce by the kLOC to solve real-world problems, tends to be \"natural\", like speech or natural language; it tends to be highly repetitive and predictable. Researchers have captured this naturalness of software through statistical models and used them to good effect in suggestion engines, porting tools, coding standards checkers, and idiom miners. This suggests that code that appears improbable, or surprising, to a good statistical language model is \"unnatural\" in some sense, and thus possibly suspicious. In this paper, we investigate this hypothesis. We consider a large corpus of bug fix commits (ca. 7,139), from 10 different Java projects, and focus on its language statistics, evaluating the naturalness of buggy code and the corresponding fixes. We find that code with bugs tends to be more entropic (i.e. unnatural), becoming less so as bugs are fixed. Ordering files for inspection by their average entropy yields cost-effectiveness scores comparable to popular defect prediction methods. At a finer granularity, focusing on highly entropic lines is similar in cost-effectiveness to some well-known static bug finders (PMD, FindBugs) and ordering warnings from these bug finders using an entropy measure improves the cost-effectiveness of inspecting code implicated in warnings. This suggests that entropy may be a valid, simple way to complement the effectiveness of PMD or FindBugs, and that search-based bug-fixing methods may benefit from using entropy both for fault-localization and searching for fixes.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884848"}, {"primary_key": "4128711", "vector": [], "sparse_vector": [], "title": "Continuous assessment of software traceability.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Many guidelines for safety-critical industries, such as aeronautics, medical devices, and railway communications, specify that traceability must be used to demonstrate that a rigorous development process has been followed and to provide evidence that the developed system is safe for use. However, creating accurate and complete traceability is costly and remains a practical challenge. The significant cost and effort of the certification process makes it difficult to introduce changes once the product is certified. We propose an automated traceability assessment approach TRUST that can be used to assess software traceability in a continuous manner to ease the change process of certified products.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892657"}, {"primary_key": "4128712", "vector": [], "sparse_vector": [], "title": "Lessons learned in aligning data and model evolution in collaborative information systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Today's enterprises have to align their information systems continuously with their dynamic business and IT environment. Collaborative information systems address this challenge by involving diverse users in managing the application's data as well as its conceptual model. In this sense, both the data and the model co-evolve. There are different approaches for aligning data and model evolution, wherein either the data is aligned to the model, or vice versa.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889235"}, {"primary_key": "4128715", "vector": [], "sparse_vector": [], "title": "Quantifying and mitigating turnover-induced knowledge loss: case studies of chrome and a project at avaya.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The utility of source code, as of other knowledge artifacts, is predicated on the existence of individuals skilled enough to derive value by using or improving it. Developers leaving a software project deprive the project of the knowledge of the decisions they have made. Previous research shows that the survivors and newcomers maintaining abandoned code have reduced productivity and are more likely to make mistakes. We focus on quantifying the extent of abandoned source files and adapt methods from financial risk analysis to assess the susceptibility of the project to developer turnover. In particular, we measure the historical loss distribution and find (1) that projects are susceptible to losses that are more than three times larger than the expected loss. Using historical simulations we find (2) that projects are susceptible to large losses that are over five times larger than the expected loss. We use Monte Carlo simulations of disaster loss scenarios and find (3) that simplistic estimates of the 'truck factor' exaggerate the potential for loss. To mitigate loss from developer turnover, we modify <PERSON><PERSON> et al.'s coordination requirements matrices. We find (4) that we can recommend the correct successor 34% to 48% of the time. We also find that having successors reduces the expected loss by as much as 15%. Our approach helps large projects assess the risk of turnover thereby making risk more transparent and manageable.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884851"}, {"primary_key": "4128718", "vector": [], "sparse_vector": [], "title": "On the techniques we create, the tools we build, and their misalignments: a study of KLEE.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Our community constantly pushes the state-of-the-art by introducing \"new\" techniques. These techniques often build on top of, and are compared against, existing systems that realize previously published techniques. The underlying assumption is that existing systems correctly represent the techniques they implement. This paper examines that assumption through a study of KLEE, a popular and well-cited tool in our community. We briefly describe six improvements we made to KLEE, none of which can be considered \"new\" techniques, that provide order-of-magnitude performance gains. Given these improvements, we then investigate how the results and conclusions of a sample of papers that cite KLEE are affected. Our findings indicate that the strong emphasis on introducing \"new\" techniques may lead to wasted effort, missed opportunities for progress, an accretion of artifact complexity, and questionable research conclusions (in our study, 27% of the papers that depend on KLEE can be questioned). We conclude by revisiting initiatives that may help to realign the incentives to better support the foundations on which we build.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884835"}, {"primary_key": "4128719", "vector": [], "sparse_vector": [], "title": "Disseminating architectural knowledge on open-source projects: a case study of the book &quot;architecture of open-source applications&quot;.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper reports on an interview-based study of 18 authors of different chapters of the two-volume book \"Architecture of Open-Source Applications\". The main contributions are a synthesis of the process of authoring essay-style documents (ESDs) on software architecture, a series of observations on important factors that influence the content and presentation of architectural knowledge in this documentation form, and a set of recommendations for readers and writers of ESDs on software architecture. We analyzed the influence of three factors in particular: the evolution of a system, the community involvement in the project, and the personal characteristics of the author. This study provides the first systematic investigation of the creation of ESDs on software architecture. The observations we collected have implications for both readers and writers of ESDs, and for architecture documentation in general.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884792"}, {"primary_key": "4128720", "vector": [], "sparse_vector": [], "title": "Discovering important source code terms.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Terms in source code have become extremely important in Software Engineering research. These \"important\" terms are typically used as input to research tools. Therefore, the quality of the output of these tools will depend on the quality of the term extraction technique. Currently, there is no definitive best technique for predicting the importance of terms during program comprehension. In my work, I perform a literature review of several techniques. I then propose a unified importance prediction model based on a machine learning algorithm. I evaluate my model in a field study involving professional programmers, as well as a standard 10-fold synthetic study. I found that my model predicts the top quartile of most-important source code terms with approximately 50% precision and recall, outperforming tf/idf and other popular techniques. Furthermore, I found that, during actual program comprehension tasks, the predictions from my model help programmers equivalent to a real set of most-important terms.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891037"}, {"primary_key": "4128721", "vector": [], "sparse_vector": [], "title": "VisAr3D: an innovative 3D visualization of UML models.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "New challenges and demands on software engineering education are presented by rapid changes and increased complexity of software systems. This paper presents the VisAr3D environment that has been developed as an innovative proposal to be introduced in the classroom to provide a 3D visualization of UML models. The user is invited to intuitively understand the model elements in this 3D environment. It includes exploration and interaction to establish a practical and pleasant learning activity focusing on large scale systems. It provides a new way to visualize and understand UML models by combining the technologies of Virtual Reality and Augmented Reality. A 3D diagram is automatically generated from an existing 2D diagram and is able to provide richer semantics than its corresponding 2D diagram. The paper also presents some results of an experimental study conducted to investigate the feasibility of using the approach. The evaluation has provided positive evidence of its ability to improve the understanding of UML models in systems with many modeling elements. And also shows the contribution of the insertion of the third dimension on this understanding, in the interests of the students and in support to the practice of teaching.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889199"}, {"primary_key": "4128722", "vector": [], "sparse_vector": [], "title": "Can software engineering students program defect-free?: an educational approach.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Qi Shan", "<PERSON>"], "summary": "Quality of software intensive systems is the priority concern and focus in industry and the research community. In practice, the increasing demand for experienced software developers in industry requires developers mature themselves in a timely manner to be able to produce high quality programs. It has become a realistic challenge to both software engineering educators and researchers. To address this challenge, we devised the PSP+ process, in particular for students majored in software engineering, that enhances the original PSP (Personal Software Process) with an ultimate goal at Defect-Free Programming (DFP). Based on the original PSP, the PSP+ incorporates a set of explicitly defined practices to facilitate experience gaining and sharing among students with the special concern on DFP. This paper elaborates the proposed PSP+ process and also reports a controlled experiment that was designed and executed to investigate the effectiveness of the PSP+ within an educational setting. The experiment results indicate that students using the PSP+ are more likely to perform high quality programming without extra effort. They also gain higher confidence with DFP compared to those using the original PSP.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889189"}, {"primary_key": "4128723", "vector": [], "sparse_vector": [], "title": "The challenges of staying together while moving fast: an exploratory study.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We report on the results of an empirical study conducted with 35 experienced software developers from 22 high-tech companies, including Google, Facebook, Microsoft, Intel, and others. The goal of the study was to elicit challenges that these developers face, potential solutions that they envision to these challenges, and research initiatives that they think would deliver useful results.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884871"}, {"primary_key": "4128724", "vector": [], "sparse_vector": [], "title": "Automated partitioning of android applications for trusted execution environments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The co-existence of critical and non-critical applications on computing devices, such as mobile phones, is becoming commonplace. The sensitive segments of a critical application should be executed in isolation on Trusted Execution Environments (TEE) so that the associated code and data can be protected from malicious applications. TEE is supported by different technologies and platforms, such as ARM Trustzone, that allow logical separation of \"secure\" and \"normal\" worlds.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884817"}, {"primary_key": "4128725", "vector": [], "sparse_vector": [], "title": "Floating-point precision tuning using blame analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While tremendously useful, automated techniques for tuning the precision of floating-point programs face important scalability challenges. We present Blame Analysis, a novel dynamic approach that speeds up precision tuning. Blame Analysis performs floating-point instructions using different levels of accuracy for their operands. The analysis determines the precision of all operands such that a given precision is achieved in the final result of the program. Our evaluation on ten scientific programs shows that Blame Analysis is successful in lowering operand precision. As it executes the program only once, the analysis is particularly useful when targeting reductions in execution time. In such case, the analysis needs to be combined with search-based tools such as Precimonious. Our experiments show that combining Blame Analysis with Precimonious leads to obtaining better results with significant reduction in analysis time: the optimized programs execute faster (in three cases, we observe as high as 39.9% program speedup) and the combined analysis time is 9x faster on average, and up to 38x faster than Precimonious alone.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884850"}, {"primary_key": "4128728", "vector": [], "sparse_vector": [], "title": "SourcererCC and SourcererCC-I: tools to detect clones in batch mode and during software development.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given the availability of large source-code repositories, there has been a large number of applications for large-scale clone detection. Unfortunately, despite a decade of active research, there is a marked lack in clone detectors that scale to big software systems or large repositories, specifically for detecting near-miss (Type 3) clones where significant editing activities may take place in the cloned code. This paper demonstrates: (i) SourcererCC, a token-based clone detector that targets the first three clone types, and exploits an index to achieve scalability to large inter-project repositories using a standard workstation. It uses an optimized inverted-index to quickly query the potential clones of a given code block. Filtering heuristics based on token ordering are used to significantly reduce the size of the index, the number of code-block comparisons needed to detect the clones, as well as the number of required token-comparisons needed to judge a potential clone, and (ii) SourcererCC-I, an Eclipse plug-in, that uses SourcererCC's core engine to identify and navigate clones (both inter and intra project) in real-time during software development. In our experiments, comparing SourcererCC with the state-of-the-art tools, we found that it is the only clone detection tool to successfully scale to 250 MLOC on a standard workstation with 12 GB RAM and efficiently detect the first three types of clones (precision 86% and recall 86-100%). Link to the demo: https://youtu.be/l7F_9Qp-ks4", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889165"}, {"primary_key": "4128729", "vector": [], "sparse_vector": [], "title": "Visualizing the effects of requirements evolution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Changes to software requirements occur throughout the software life cycle. Requirements engineers who maintain software systems in regulated environments must identify the affected artifacts when requirements change. This identification is critical to: (a) ensure continued compliance with regulations, and (b) accurately estimate budget requests. Previously, we introduced Requirements Evolution Charts (REC) to provide a visual representation of requirements evolution history. An REC is generated from the issue tickets in which requirements engineers record changes to requirements artifacts. Herein, we examine whether a REC helps software engineers conduct an impact analysis. Thirty experienced NTT requirements engineers with no prior domain knowledge identified the impact of seven requirements changes in a large-scale system governed by Japanese laws and regulations. They were divided into two groups of fifteen engineers. The REC group employed the REC to aid their efforts; the Non-REC group conducted their impact analysis without the REC. Participants in both groups identified which of the 139 artifacts were affected based on a history of 108 issue tickets. Our study reveals that engineers in the REC group identified the affected artifacts more accurately and quickly than the Non-REC group, suggesting that the REC is a valuable tool for examining the impact of requirements evolution.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889237"}, {"primary_key": "4128730", "vector": [], "sparse_vector": [], "title": "SourcererCC: scaling code clone detection to big-code.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Despite a decade of active research, there is a marked lack in clone detectors that scale to very large repositories of source code, in particular for detecting near-miss clones where significant editing activities may take place in the cloned code. We present SourcererCC, a token-based clone detector that targets three clone types, and exploits an index to achieve scalability to large inter-project repositories using a standard workstation. SourcererCC uses an optimized inverted-index to quickly query the potential clones of a given code block. Filtering heuristics based on token ordering are used to significantly reduce the size of the index, the number of code-block comparisons needed to detect the clones, as well as the number of required token-comparisons needed to judge a potential clone. We evaluate the scalability, execution time, recall and precision of SourcererCC, and compare it to four publicly available and state-of-the-art tools. To measure recall, we use two recent benchmarks, (1) a large benchmark of real clones, BigCloneBench, and (2) a Mutation/Injection-based framework of thousands of fine-grained artificial clones. We find SourcererCC has both high recall and precision, and is able to scale to a large inter-project repository (250MLOC) using a standard workstation.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884877"}, {"primary_key": "4128732", "vector": [], "sparse_vector": [], "title": "Practical programming, validation and verification with finite-state machines: a library and its industrial application.", "authors": ["Paulo Salem"], "summary": "Finite-state machines (FSMs) are among the oldest models employed in the formalization and analysis of both software and hardware. Owing to their simplicity, there exist various implementations to support their practical application in mainstream programming languages. Through such software libraries, programmers can explicitly define states, events and transitions in order to delegate the machine's execution to an underlying engine. However, as far as we know, no such library provides formal verification capabilities alongside its execution engine. That is to say, once an FSM is defined, the resulting program cannot be used as a formal specification to be subject to formal verification, thereby not making the analytical tractability of FSMs available. Formal verification, if any, is conducted in an independent model separate from the program, thus duplicating the information and creating the possibility of discrepancies between both. In this paper we show how to overcome this limitation. To this end, we present the Verum library, which allows the specification, execution and verification of FSMs in the Ruby language, largely bypassing the need to explicitly employ an additional modeling language. Formal verification is achieved by automatically translating the source program of the FSM into a Timed Automaton (TA) specification for the UPPAAL model checker. To illustrate the value of the approach, we present the industrial problem which inspired the creation of this tool and is currently using it, namely, a payment system. Besides the technical aspects of the tool, a number of practical lessons learned with this application are explored. Although we describe very concrete artifacts and applications, the overall approach is quite general.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889226"}, {"primary_key": "4128733", "vector": [], "sparse_vector": [], "title": "Cognitive biases in software quality and testing.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Humans are an integral entity for performing software quality and testing activities. The quality is compromised when human-thought process deviates from the laws of rational thinking, referred to as cognitive biases. The work carried out so far from this perspective in software quality and testing is very scarce and is limited to one cognitive bias only. This work aims to explore the phenomenon of cognitive biases in software quality and testing in more detail. Furthermore, investigating the factors that exist in an organisational context and that trigger the biases, which in turn deteriorate the quality of software, is also the focus of this work. Acquiring the knowledge of cognitive biases and the triggering factors will help in circumventing them, thus improving software quality.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889265"}, {"primary_key": "4128734", "vector": [], "sparse_vector": [], "title": "System testing of repository-style software: an experience report.", "authors": ["<PERSON>"], "summary": "System testing based on a black box approach is a common industrial practice in information systems. Despite its widespread use, however, little guidance is available for testing engineers facing the problem of selecting the best test strategy. In previous work, we proposed to adopt functional models and related testing patterns according to the architectural style of the application under test. In this paper, we present an industrial study that applies this technique to system testing of repository based applications.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889239"}, {"primary_key": "4128736", "vector": [], "sparse_vector": [], "title": "Debugging for reactive programming.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Reactive programming is a recent programming technique that provides dedicated language abstractions for reactive software. Reactive programming relieves developers from manually updating outputs when the inputs of a computation change, it overcomes a number of well-know issues of the Observer design pattern, and it makes programs more comprehensible. Unfortunately, complementing the new paradigm with proper tools is a vastly unexplored area. Hence, as of now, developers can embrace reactive programming only at the cost of a more challenging development process.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884815"}, {"primary_key": "4128737", "vector": [], "sparse_vector": [], "title": "Debugging reactive programming with reactive inspector.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Reactive programming provides dedicated language abstractions for reactive software, relieving developers from manually updating outputs when the inputs of a computation change. Unfortunately, complementing the new paradigm with proper tools that support coding activities is a vastly unexplored area.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2893174"}, {"primary_key": "4128738", "vector": [], "sparse_vector": [], "title": "Building a theory of job rotation in software engineering from an instrumental case study.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Cleyton V. C. de Ma<PERSON>ães", "Cleviton V. F. Monteiro"], "summary": "Job Rotation is an organizational practice in which individuals are frequently moved from a job (or project) to another in the same organization. Studies in other areas have found that this practice has both negative and positive effects on individuals' work. However, there are only few studies addressing this issue in software engineering so far. The goal of our study is to investigate the effects of job rotation on work related factors in software engineering by performing a qualitative case study on a large software organization that uses job rotation as an organizational practice. We interviewed senior managers, project managers, and software engineers that had experienced this practice. <PERSON><PERSON><PERSON>, 48 participants were involved in all phases of this research. Collected data was analyzed using qualitative coding techniques and the results were checked and validated with participants through member checking. Our findings suggest that it is necessary to find balance between the positive effects on work variety and learning opportunities, and negative effects on cognitive workload and performance. Further, the lack of feedback resulting from constant movement among projects and teams may have a negative impact on performance feedback. We conclude that job rotation is an important organizational practice with important positive results. However, managers must be aware of potential negative effects and deploy tactics to balance them. We discuss such tactics in this article.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884837"}, {"primary_key": "4128739", "vector": [], "sparse_vector": [], "title": "Multi-objective software effort estimation.", "authors": ["Federica Sarro", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a bi-objective effort estimation algorithm that combines Confidence Interval Analysis and assessment of Mean Absolute Error. We evaluate our proposed algorithm on three different alternative formulations, baseline comparators and current state-of-the-art effort estimators applied to five real-world datasets from the PROMISE repository, involving 724 different software projects in total. The results reveal that our algorithm outperforms the baseline, state-of-the-art and all three alternative formulations, statistically significantly (p < 0.001) and with large effect size (Â12 ≥ 0.9) over all five datasets. We also provide evidence that our algorithm creates a new state-of-the-art, which lies within currently claimed industrial human-expert-based thresholds, thereby demonstrating that our findings have actionable conclusions for practicing software engineers.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884830"}, {"primary_key": "4128740", "vector": [], "sparse_vector": [], "title": "Continuous deployment at Facebook and OANDA.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Kent L. Beck", "<PERSON>"], "summary": "Continuous deployment is the software engineering practice of deploying many small incremental software updates into production, leading to a continuous stream of 10s, 100s, or even 1,000s of deployments per day. High-profile Internet firms such as Amazon, Etsy, Facebook, Flickr, Google, and Netflix have embraced continuous deployment. However, the practice has not been covered in textbooks and no scientific publication has presented an analysis of continuous deployment.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889223"}, {"primary_key": "4128743", "vector": [], "sparse_vector": [], "title": "Feature-model interfaces: the highway to compositional analyses of highly-configurable systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Today's software systems are often customizable by means of load-time or compile-time configuration options. These options are typically not independent and their dependencies can be specified by means of feature models. As many industrial systems contain thousands of options, the maintenance and utilization of feature models is a challenge for all stakeholders. In the last two decades, numerous approaches have been presented to support stakeholders in analyzing feature models. Such analyses are commonly reduced to satisfiability problems, which suffer from the growing number of options. While first attempts have been made to decompose feature models into smaller parts, they still require to compose all parts for analysis. We propose the concept of a feature-model interface that only consists of a subset of features, typically selected by experts, and hides all other features and dependencies. Based on a formalization of feature-model interfaces, we prove compositionality properties. We evaluate feature-model interfaces using a three-month history of an industrial feature model from the automotive domain with 18,616 features. Our results indicate performance benefits especially under evolution as often only parts of the feature model need to be analyzed again.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884823"}, {"primary_key": "4128745", "vector": [], "sparse_vector": [], "title": "Automatized derivation of comprehensive specifications for black-box services.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Today, cloud vendors host third party black-box services, whose developers usually provide only textual descriptions or purely syntactical interface specifications. Cloud vendors that give substantial support to other third party developers to integrate hosted services into new software solutions would have a unique selling feature over their competitors. However, to reliably determine if a service is reusable, comprehensive service specifications are needed. Characteristic for comprehensive in contrast to syntactical specifications are the formalization of ontological and behavioral semantics, homogeneity according to a global ontology, and a service grounding that links the abstract service description and its technical realization. Homogeneous, semantical specifications enable to reliably identify reusable services, whereas the service grounding is needed for the technical service integration.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889271"}, {"primary_key": "4128747", "vector": [], "sparse_vector": [], "title": "Performance issues and optimizations in JavaScript: an empirical study.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "As JavaScript is becoming increasingly popular, the performance of JavaScript programs is crucial to ensure the responsiveness and energy-efficiency of thousands of programs. Yet, little is known about performance issues that developers face in practice and they address these issues. This paper presents an empirical study of 98 fixed performance issues from 16 popular client-side and server-side JavaScript projects. We identify eight root causes of issues and show that inefficient usage of APIs is the most prevalent root cause. Furthermore, we find that most issues are addressed by optimizations that modify only a few lines of code, without significantly affecting the complexity of the source code. By studying the performance impact of optimizations on several versions of the SpiderMonkey and V8 engines, we find that only 42.68% of all optimizations improve performance consistently across all versions of both engines. Finally, we observe that many optimizations are instances of patterns applicable across projects, as evidenced by 139 previously unknown optimization opportunities that we find based on the patterns identified during the study. The results of the study help application developers to avoid common mistakes, researchers to develop performance-related techniques that address relevant problems, and engine developers to address prevalent bottleneck patterns.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884829"}, {"primary_key": "4128749", "vector": [], "sparse_vector": [], "title": "What communication tools students use in software projects and how do different tools suit different parts of project work?", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In software engineering education, the goal is often to provide students with authentic assignments using actual tools of the trade. Students are often allowed to select their preferred tools without specifying what to use for e.g. communication within the team, scheduling, bug tracking, etc. However, there is an abundance of tools to choose from with more appearing rapidly, which can make it difficult for students or staff to select appropriate tools for each task. In this paper, we study the suitability of several commonly used communication and collaboration tools on different tasks that students encounter in team assignments. We surveyed students' experiences with different tools in a university-level web software development course.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889196"}, {"primary_key": "4128754", "vector": [], "sparse_vector": [], "title": "A study of the quality-impacting practices of modern code review at Sony mobile.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, a flexible, lightweight variant of the code review process (i.e., the practice of having other team members critique software changes) is adopted by open source and proprietary software projects. While this flexibility is a blessing (e.g., enabling code reviews to span the globe), it does not mandate minimum review quality criteria like the formal code inspections of the past. Recent work shows that lax reviewing can impact the quality of open source systems. In this paper, we investigate the impact that code reviewing practices have on the quality of a proprietary system that is developed by Sony Mobile. We begin by replicating open source analyses of the relationship between software quality (as approximated by post-release defect-proneness) and: (1) code review coverage, i.e., the proportion of code changes that have been reviewed and (2) code review participation, i.e., the degree of reviewer involvement in the code review process. We also perform a qualitative analysis, with a survey of 93 stakeholders, semi-structured interviews with 15 stakeholders, and a follow-up survey of 25 senior engineers. Our results indicate that while past measures of review coverage and participation do not share a relationship with defect-proneness at Sony Mobile, reviewing measures that are aware of the Sony Mobile development context are associated with defect-proneness. Our results have lead to improvements of the Sony Mobile code review process.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889243"}, {"primary_key": "4128758", "vector": [], "sparse_vector": [], "title": "Toward a framework for detecting privacy policy violations in android application code.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile applications frequently access sensitive personal information to meet user or business requirements. Because such information is sensitive in general, regulators increasingly require mobile-app developers to publish privacy policies that describe what information is collected. Furthermore, regulators have fined companies when these policies are inconsistent with the actual data practices of mobile apps. To help mobile-app developers check their privacy policies against their apps' code for consistency, we propose a semi-automated framework that consists of a policy terminology-API method map that links policy phrases to API methods that produce sensitive information, and information flow analysis to detect misalignments. We present an implementation of our framework based on a privacy-policy-phrase ontology and a collection of mappings from API methods to policy phrases. Our empirical evaluation on 477 top Android apps discovered 341 potential privacy policy violations.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884855"}, {"primary_key": "4128761", "vector": [], "sparse_vector": [], "title": "Identifying successful strategies for resolving static analysis notifications.", "authors": ["<PERSON>"], "summary": "Although static analysis tools detect potential code defects early in the development process, they do not fully support developers in resolving those defects. To accurately and efficiently resolve defects, developers must orchestrate several complex tasks, such as determining whether the defect is a false positive and updating the source code without introducing new defects. Without good defect resolution strategies developers may resolve defects erroneously or inefficiently. In this work, I perform a preliminary analysis of the successful and unsuccessful strategies developers use to resolve defects. Based on the successful strategies identified, I then outline a tool to support developers throughout the defect resolution process.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891034"}, {"primary_key": "4128764", "vector": [], "sparse_vector": [], "title": "Wide-field ethnography: studying software engineering in 2025 and beyond.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a vision of how the Internet of Things will impact the study of software engineering by 2025 and beyond. The following questions guide this inquiry. What will it mean to be able to deploy hundreds of sensors and data collectors running concurrently over months to gather very large and rich datasets of the physical, digital, and social aspects of software engineering organizations and the products and services those organizations create? How might such datasets change the types of research questions that can be addressed? What sort of tools will be needed to allow interdisciplinary communities of researchers to collaboratively analyse such datasets? How might such datasets help us understand the principles governing the interplay of physical, cyber, and social aspects of software engineering and its products, and automate aspects of such systems?", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889214"}, {"primary_key": "4128766", "vector": [], "sparse_vector": [], "title": "DECA: development emails content analyzer.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Written development discussions occurring over different communication means (e.g. issue trackers, development mailing lists, or IRC chats) represent a precious source of information for developers, as well as for researchers interested to build recommender systems. Such discussions contain text having different purposes, e.g. discussing feature requests, bugs to fix etc. In this context, the manual classification or filtering of such discussions in according to their purpose would be a daunting and time-consuming task.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889170"}, {"primary_key": "4128767", "vector": [], "sparse_vector": [], "title": "Spotting design problems with smell agglomerations.", "authors": ["<PERSON>"], "summary": "Design problems are structures that indicate violations of key design principles or rules. The main difficulty to identify them in the source code is due to the fact they are scattered through several code elements. Thus, code smells - microstructures in the program - have been used to reveal surface indications of a design problem. However, individually, each code smell represents only a partial embodiment of a design problem. Since design problem is scattered through several program elements, we are investigating a strategy to select a group of code smells that is likely to help developers to find design problems. We call agglomeration this group of code smells. Our main goal is to summarize smell agglomerations that better indicate the occurrence of design problems. Our strategy to derive agglomerations is based on capturing semantic relations among closely-related code smells. We will assess to what extent code smell agglomerations help developers to locate and prioritize design problems.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889273"}, {"primary_key": "4128769", "vector": [], "sparse_vector": [], "title": "The evolution of C programming practices: a study of the Unix operating system 1973-2015.", "authors": ["<PERSON><PERSON><PERSON>", "Pan<PERSON>", "<PERSON>"], "summary": "Tracking long-term progress in engineering and applied science allows us to take stock of things we have achieved, appreciate the factors that led to them, and set realistic goals for where we want to go. We formulate seven hypotheses associated with the long term evolution of C programming in the Unix operating system, and examine them by extracting, aggregating, and synthesising metrics from 66 snapshots obtained from a synthetic software configuration management repository covering a period of four decades. We found that over the years developers of the Unix operating system appear to have evolved their coding style in tandem with advancements in hardware technology, promoted modularity to tame rising complexity, adopted valuable new language features, allowed compilers to allocate registers on their behalf, and reached broad agreement regarding code formatting. The progress we have observed appears to be slowing or even reversing prompting the need for new sources of innovation to be discovered and followed.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884799"}, {"primary_key": "4128771", "vector": [], "sparse_vector": [], "title": "Industry application of continuous integration modeling: a multiple-case study.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The popular agile practice of continuous integration has become an essential part of the software development process in many companies, sometimes to the extent that delivery to customer is impossible without it. Due to this pivotal role it is an important field of research to better understand the practice: continuous integration system behavior, improvement identification and analysis of change impacts. This paper investigates the effects of modeling of such systems, by applying two continuous integration modeling techniques to four separate industry cases in three companies. These techniques are found to complement each other, and their ability to help professionals gain a better understanding of their continuous integration systems, to communicate around them and to support technical work is demonstrated. In addition, guidelines for conducting similar continuous integration modeling work are presented and feedback on the studied models provided. This work presents software professionals with demonstrably effective methods for design and analysis of continuous integration systems and thereby improving the efficacy of a vital part of their software development efforts, while supporting researchers with recommendations for and feedback on available modeling techniques.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889252"}, {"primary_key": "4128772", "vector": [], "sparse_vector": [], "title": "Continuous validation for data analytics systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "From a future history of 2025: Continuous development is common for build/test (continuous integration) and operations (devOps). This trend continues through the lifecycle, into what we call 'devUsage': continuous usage validation. In addition to ensuring systems meet user needs, organisations continuously validate their legal and ethical use. The rise of end-user programming and multi-sided platforms exacerbate validation challenges. A separate trend is the specialisation of software engineering for technical domains, including data analytics. This domain has specific validation challenges. We must validate the accuracy of statistical models, but also whether they have illegal or unethical biases. Usage needs addressed by machine learning are sometimes not specifiable in the traditional sense, and statistical models are often 'black boxes'. We describe future research to investigate solutions to these devUsage challenges for data analytics systems. We will adapt risk management and governance frameworks previously used for software product qualities, use social network communities for input from aligned stakeholder groups, and perform cross-validation using autonomic experimentation, cyber-physical data streams, and online discursive feedback.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889207"}, {"primary_key": "4128773", "vector": [], "sparse_vector": [], "title": "Teaching Agile: addressing the conflict between project delivery and application of Agile methods.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper analyses the changes we have made in teaching agile methodologies, practices, and principles in four courses in order to address a specific dilemma: students need to apply agile methods in order to learn them, but when complementing our courses with applied content, we face the problem that students perceive the learning and application of agile methods as less important than delivering a finished product at the end of the course. This causes students to not apply theoretical process knowledge and therefore to not develop necessary skills associated with working with defined processes in the industry. Concretely, we report on our experience with teaching Scrum with Lego, removing formal grading requirements on the delivered product, emphasising process application in post-mortem reports, and organisational changes to support the process during supervision. These changes are analysed in the context of student satisfaction, teacher observations, and achievements of learning outcomes. We also provide an overview of the lessons learnt to help guide the design of courses on agile methodologies.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889181"}, {"primary_key": "4128775", "vector": [], "sparse_vector": [], "title": "Overcoming open source project entry barriers with a portal for newcomers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Community-based Open Source Software (OSS) projects are usually self-organized and dynamic, receiving contributions from distributed volunteers. Newcomer are important to the survival, long-term success, and continuity of these communities. However, newcomers face many barriers when making their first contribution to an OSS project, leading in many cases to dropouts. Therefore, a major challenge for OSS projects is to provide ways to support newcomers during their first contribution. In this paper, we propose and evaluate FLOSScoach, a portal created to support newcomers to OSS projects. FLOSScoach was designed based on a conceptual model of barriers created in our previous work. To evaluate the portal, we conducted a study with 65 students, relying on qualitative data from diaries, self-efficacy questionnaires, and the Technology Acceptance Model. The results indicate that FLOSScoach played an important role in guiding newcomers and in lowering barriers related to the orientation and contribution process, whereas it was not effective in lowering technical barriers. We also found that FLOSScoach is useful, easy to use, and increased newcomers' confidence to contribute. Our results can help project maintainers on deciding the points that need more attention in order to help OSS project newcomers overcome entry barriers.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884806"}, {"primary_key": "4128776", "vector": [], "sparse_vector": [], "title": "Grounded theory in software engineering research: a critical review and guidelines.", "authors": ["Klaas<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Grounded Theory (GT) has proved an extremely useful research approach in several fields including medical sociology, nursing, education and management theory. However, GT is a complex method based on an inductive paradigm that is fundamentally different from the traditional hypothetico-deductive research model. As there are at least three variants of GT, some ostensibly GT research suffers from method slurring, where researchers adopt an arbitrary subset of GT practices that are not recognizable as GT. In this paper, we describe the variants of GT and identify the core set of GT practices. We then analyze the use of grounded theory in software engineering. We carefully and systematically selected 98 articles that mention GT, of which 52 explicitly claim to use GT, with the other 46 using GT techniques only. Only 16 articles provide detailed accounts of their research procedures. We offer guidelines to improve the quality of both conducting and reporting GT studies. The latter is an important extension since current GT guidelines in software engineering do not cover the reporting process, despite good reporting being necessary for evaluating a study and informing subsequent research.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884833"}, {"primary_key": "4128777", "vector": [], "sparse_vector": [], "title": "FSMdroid: guided GUI testing of android apps.", "authors": ["<PERSON><PERSON>"], "summary": "GUI testing has been an effective means of validating Android apps. Meanwhile, it still faces a strong challenge about how to explore trails, i.e., unfrequented test sequences, as defects tend to reside on these unfrequented trails. This paper introduces FSMdroid, a novel, guided approach to GUI testing of Android apps. The essential idea of FSMdroid is to (1) construct an initial stochastic model for the app under test, (2) iteratively mutate the stochastic model and derive tests. The model mutations are guided by an MCMC sampling method such that the resulting test sequences can be diverse and also achieve high code coverage during testing. We have evaluated FSMdroid on 40 real-world Android apps. Compared with the traditional model-based testing approaches, FSMdroid enhances the diversity of test sequences by 85%, but reduces the number of them by 54%. Furthermore, we uncover 7 app bugs.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891043"}, {"primary_key": "4128778", "vector": [], "sparse_vector": [], "title": "Reliability of Run-Time Quality-of-Service evaluation using parametric model checking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Run-time Quality-of-Service (QoS) assurance is crucial for business-critical systems. Complex behavioral performance metrics (PMs) are useful but often difficult to monitor or measure. Probabilistic model checking, especially parametric model checking, can support the computation of aggregate functions for a broad range of those PMs. In practice, those PMs may be defined with parameters determined by run-time data. In this paper, we address the reliability of QoS evaluation using parametric model checking. Due to the imprecision with the instantiation of parameters, an evaluation outcome may mislead the judgment about requirement violations. Based on a general assumption of run-time data distribution, we present a novel framework that contains light-weight statistical inference methods to analyze the reliability of a parametric model checking output with respect to an intuitive criterion. We also present case studies in which we test the stability and accuracy of our inference methods and describe an application of our framework to a cloud server management problem.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884814"}, {"primary_key": "4128781", "vector": [], "sparse_vector": [], "title": "Understanding and fixing multiple language interoperability issues: the C/Fortran case.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We performed an empirical study to understand interoperability issues in C and Fortran programs. C/Fortran interoperability is very common and is representative of general language interoperability issues, such as how interfaces between languages are defined and how data types are shared. Fortran presents an additional challenge, since several ad hoc approaches to C/Fortran interoperability were in use long before a standard mechanism was defined. We explored 20 applications, automatically analyzing over 12 million lines of code. We found that only 3% of interoperability instances follow the ISO standard to describe interfaces; the rest follow a combination of compiler-dependent ad hoc approaches. Several parameters in cross-language functions did not have standards-compliant interoperable types, and about one-fourth of the parameters that were passed by reference could be passed by value. We propose that automated refactoring tools may provide a viable way to migrate programs to use the new interoperability features. We present two refactorings to transform code for this purpose and one refactoring to evolve code thereafter; all of these are instances of multiple language refactorings.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884858"}, {"primary_key": "4128782", "vector": [], "sparse_vector": [], "title": "Finding and analyzing compiler warning defects.", "authors": ["Chengnian Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Good compiler diagnostic warnings facilitate software development as they indicate likely programming mistakes or code smells. However, due to compiler bugs, the warnings may be erroneous, superfluous or missing, even for mature production compilers like GCC and Clang. In this paper, we (1) propose the first randomized differential testing technique to detect compiler warning defects and (2) describe our extensive evaluation in finding warning defects in widely-used C compilers.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884879"}, {"primary_key": "4128785", "vector": [], "sparse_vector": [], "title": "IntEQ: recognizing benign integer overflows via equivalence checking across multiple precisions.", "authors": ["Hao Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Qingkai Zeng"], "summary": "Integer overflow (IO) vulnerabilities can be exploited by attackers to compromise computer systems. In the mean time, IOs can be used intentionally by programmers for benign purposes such as hashing and random number generation. Hence, differentiating exploitable and harmful IOs from intentional and benign ones is an important challenge. It allows reducing the number of false positives produced by IO vulnerability detection techniques, helping developers or security analysts to focus on fixing critical IOs without inspecting the numerous false alarms. The difficulty of recognizing benign IOs mainly lies in inferring the intent of programmers from source code.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884820"}, {"primary_key": "4128787", "vector": [], "sparse_vector": [], "title": "Optimizing selection of competing services with probabilistic hierarchical refinement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recently, many large enterprises (e.g., Netflix, Amazon) have decomposed their monolithic application into services, and composed them to fulfill their business functionalities. Many hosting services on the cloud, with different Quality of Service (QoS) (e.g., availability, cost), can be used to host the services. This is an example of competing services. QoS is crucial for the satisfaction of users. It is important to choose a set of services that maximize the overall QoS, and satisfy all QoS requirements for the service composition. This problem, known as optimal service selection, is NP-hard. Therefore, an effective method for reducing the search space and guiding the search process is highly desirable. To this end, we introduce a novel technique, called Probabilistic Hierarchical Refinement (ProHR). ProHR effectively reduces the search space by removing competing services that cannot be part of the selection. ProHR provides two methods, probabilistic ranking and hierarchical refinement, that enable smart exploration of the reduced search space. Unlike existing approaches that perform poorly when QoS requirements become stricter, ProHR maintains high performance and accuracy, independent of the strictness of the QoS requirements. ProHR has been evaluated on a publicly available dataset, and has shown significant improvement over existing approaches.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884861"}, {"primary_key": "4128789", "vector": [], "sparse_vector": [], "title": "Automated parameter optimization of classification techniques for defect prediction models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Defect prediction models are classifiers that are trained to identify defect-prone software modules. Such classifiers have configurable parameters that control their characteristics (e.g., the number of trees in a random forest classifier). Recent studies show that these classifiers may underperform due to the use of suboptimal default parameter settings. However, it is impractical to assess all of the possible settings in the parameter spaces. In this paper, we investigate the performance of defect prediction models where Caret --- an automated parameter optimization technique --- has been applied. Through a case study of 18 datasets from systems that span both proprietary and open source domains, we find that (1) <PERSON><PERSON> improves the AUC performance of defect prediction models by as much as 40 percentage points; (2) Caret-optimized classifiers are at least as stable as (with 35% of them being more stable than) classifiers that are trained using the default settings; and (3) <PERSON><PERSON> increases the likelihood of producing a top-performing classifier by as much as 83%. Hence, we conclude that parameter settings can indeed have a large impact on the performance of defect prediction models, suggesting that researchers should experiment with the parameters of the classification techniques. Since automated parameter optimization techniques like Caret yield substantially benefits in terms of performance improvement and stability, while incurring a manageable additional computational cost, they should be included in future defect prediction studies.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884857"}, {"primary_key": "4128790", "vector": [], "sparse_vector": [], "title": "Towards a better understanding of the impact of experimental components on defect prediction modelling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Defect prediction models are used to pinpoint risky software modules and understand past pitfalls that lead to defective modules. The predictions and insights that are derived from defect prediction models may not be accurate and reliable if researchers do not consider the impact of experimental components (e.g., datasets, metrics, and classifiers) of defect prediction modelling. Therefore, a lack of awareness and practical guidelines from previous research can lead to invalid predictions and unreliable insights. In this thesis, we investigate the impact that experimental components have on the predictions and insights of defect prediction models. Through case studies of systems that span both proprietary and open-source domains, we find that (1) noise in defect datasets; (2) parameter settings of classification techniques; and (3) model validation techniques have a large impact on the predictions and insights of defect prediction models, suggesting that researchers should carefully select experimental components in order to produce more accurate and reliable defect prediction models.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889256"}, {"primary_key": "4128793", "vector": [], "sparse_vector": [], "title": "Coverage-driven test code generation for concurrent classes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous techniques on concurrency testing have mainly focused on exploring the interleaving space of manually written test code to expose faulty interleavings of shared memory accesses. These techniques assume the availability of failure-inducing tests. In this paper, we present AutoConTest, a coverage-driven approach to generate effective concurrent test code that achieve high interleaving coverage. AutoConTest consists of three components. First, it computes the coverage requirements dynamically and iteratively during sequential test code generation, using a coverage metric that captures the execution context of shared memory accesses. Second, it smartly selects these sequential codes based on the computed result and assembles them for concurrent tests, achieving increased context-sensitive interleaving coverage. Third, it explores the newly covered interleavings. We have implemented AutoConTest as an automated tool and evaluated it using 6 real-world concurrent Java subjects. The results show that AutoConTest is able to generate effective concurrent tests that achieve high interleaving coverage and expose concurrency faults quickly. AutoConTest took less than 65 seconds (including program analysis, test generation and execution) to expose the faults in the program subjects.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884876"}, {"primary_key": "4128795", "vector": [], "sparse_vector": [], "title": "Reusing stack traces: automated attack surface approximation.", "authors": ["<PERSON>"], "summary": "Security requirements around software systems have become more stringent as society becomes more interconnected via the Internet. New ways of prioritizing security efforts are needed so security professionals can use their time effectively to find security vulnerabilities or prevent them from occurring in the first place. The goal of this work is to help software development teams prioritize security efforts by approximating the attack surface of a software system via stack trace analysis. Automated attack surface approximation is a technique that uses crash dump stack traces to predict what code may contain exploitable vulnerabilities. If a code entity (a binary, file or function) appears on stack traces, then Attack Surface Approximation (ASA) considers that code entity is on the attack surface of the software system. We also explore whether number of appearances of code on stack traces correlates with where security vulnerabilities are found. To date, feasibility studies of ASA have been performed on Windows 8 and 8.1, and Mozilla Firefox. The results from these studies indicate that ASA may be useful for practitioners trying to secure their software systems. We are now working towards establishing the ground truth of what the attack surface of software systems is, along with looking at how ASA could change over time, among other metrics.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889263"}, {"primary_key": "4128796", "vector": [], "sparse_vector": [], "title": "Software security education at scale.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-Hill"], "summary": "Massively Open Online Courses (MOOCs) provide a unique opportunity to reach out to students who would not normally be reached by alleviating the need to be physically present in the classroom. However, teaching software security coursework outside of a classroom setting can be challenging. What are the challenges when converting security material from an on-campus course to the MOOC format? The goal of this research is to assist educators in constructing software security coursework by providing a comparison of classroom courses and MOOCs. In this work, we compare demographic information, student motivations, and student results from an on-campus software security course and a MOOC version of the same course. We found that the two populations of students differed, with the MOOC reaching a more diverse set of students than the on-campus course. We found that students in the on-campus course had higher quiz scores, on average, than students in the MOOC. Finally, we document our experience running the courses and what we would do differently to assist future educators constructing similar MOOC's.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889186"}, {"primary_key": "4128797", "vector": [], "sparse_vector": [], "title": "Revisiting code ownership and its relationship with software quality in the scope of modern code review.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Code ownership establishes a chain of responsibility for modules in large software systems. Although prior work uncovers a link between code ownership heuristics and software quality, these heuristics rely solely on the authorship of code changes. In addition to authoring code changes, developers also make important contributions to a module by reviewing code changes. Indeed, recent work shows that reviewers are highly active in modern code review processes, often suggesting alternative solutions or providing updates to the code changes. In this paper, we complement traditional code ownership heuristics using code review activity. Through a case study of six releases of the large Qt and OpenStack systems, we find that: (1) 67%--86% of developers did not author any code changes for a module, but still actively contributed by reviewing 21%--39% of the code changes, (2) code ownership heuristics that are aware of reviewing activity share a relationship with software quality, and (3) the proportion of reviewers without expertise shares a strong, increasing relationship with the likelihood of having post-release defects. Our results suggest that reviewing activity captures an important aspect of code ownership, and should be included in approximations of it in future studies.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884852"}, {"primary_key": "4128798", "vector": [], "sparse_vector": [], "title": "A new thread-aware birthmark for plagiarism detection of multithreaded programs.", "authors": ["Zhenzhou Tian", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Dynamic birthmarking used to be an effective approach to detecting software plagiarism. Yet the new trend towards multithreaded programming renders existing algorithms almost useless, due to the fact that thread scheduling nondeterminism severely perturbs birthmark generation and comparison. In this paper, we design a birthmark based on thread-related system calls. Such a birthmark is less susceptible to thread scheduling. The empirical study conducted on an open benchmark shows that the new birthmark is superior to existing birthmarks and is resilient against most state-of-the-art obfuscation techniques.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892653"}, {"primary_key": "4128799", "vector": [], "sparse_vector": [], "title": "Candoia: a platform and ecosystem for mining software repositories tools.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce Candoia, a platform and ecosystem for building Mining Software Repositories (MSR) tools. The platform is designed to support building of MSR tools by providing necessary tools and abstractions that hide the complex details of version control, bug databases, source code programming languages and forges. The ecosystem allows easy sharing and accessing of MSR apps for researchers and practitioners. We have some initial evidence about Candoia's applicability in building robust MSR tools (over two dozen prebuilt apps in the first public release of Candoia), adoptability and interoperability (apps run on widely used projects such as Apache Tomcat, Apache Hadoop etc) and easy customizability (an user study). Candoia is available for download from http://candoia.org.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892662"}, {"primary_key": "4128800", "vector": [], "sparse_vector": [], "title": "Augmenting API documentation with insights from stack overflow.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Software developers need access to different kinds of information which is often dispersed among different documentation sources, such as API documentation or Stack Overflow. We present an approach to automatically augment API documentation with \"insight sentences\" from Stack Overflow---sentences that are related to a particular API type and that provide insight not contained in the API documentation of that type. Based on a development set of 1,574 sentences, we compare the performance of two state-of-the-art summarization techniques as well as a pattern-based approach for insight sentence extraction. We then present SISE, a novel machine learning based approach that uses as features the sentences themselves, their formatting, their question, their answer, and their authors as well as part-of-speech tags and the similarity of a sentence to the corresponding API documentation. With SISE, we were able to achieve a precision of 0.64 and a coverage of 0.7 on the development set. In a comparative study with eight software developers, we found that SISE resulted in the highest number of sentences that were considered to add useful information not found in the API documentation. These results indicate that taking into account the meta data available on Stack Overflow as well as part-of-speech tags can significantly improve unsupervised extraction approaches when applied to Stack Overflow data.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884800"}, {"primary_key": "4128807", "vector": [], "sparse_vector": [], "title": "An empirical study on the impact of C++ lambdas and programmer experience.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Lambdas have seen increasing use in mainstream programming languages, notably in Java 8 and C++ 11. While the technical aspects of lambdas are known, we conducted the first randomized controlled trial on the human factors impact of C++ 11 lambdas compared to iterators. Because there has been recent debate on having students or professionals in experiments, we recruited undergraduates across the academic pipeline and professional programmers to evaluate these findings in a broader context.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884849"}, {"primary_key": "4128810", "vector": [], "sparse_vector": [], "title": "The sky is not the limit: multitasking across GitHub projects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Software development has always inherently required multitasking: developers switch between coding, reviewing, testing, designing, and meeting with colleagues. The advent of software ecosystems like GitHub has enabled something new: the ability to easily switch between projects. Developers also have social incentives to contribute to many projects; prolific contributors gain social recognition and (eventually) economic rewards. Multitasking, however, comes at a cognitive cost: frequent context-switches can lead to distraction, sub-standard work, and even greater stress. In this paper, we gather ecosystem-level data on a group of programmers working on a large collection of projects. We develop models and methods for measuring the rate and breadth of a developers' context-switching behavior, and we study how context-switching affects their productivity. We also survey developers to understand the reasons for and perceptions of multitasking. We find that the most common reason for multitasking is interrelationships and dependencies between projects. Notably, we find that the rate of switching and breadth (number of projects) of a developer's work matter. Developers who work on many projects have higher productivity if they focus on few projects per day. Developers that switch projects too much during the course of a day have lower productivity as they work on more projects overall. Despite these findings, developers perceptions of the benefits of multitasking are varied.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884875"}, {"primary_key": "4128812", "vector": [], "sparse_vector": [], "title": "Assisting developers with license compliance.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Software licensing determines how open source systems are reused, distributed, and modified from a legal perspective. While it facilitates rapid development, it can present difficulty for developers in understanding due to the legal language of these licenses. Because of misunderstandings, systems can incorporate licensed code in a way that violates the terms of the license. Our research first aimed at understanding the rationale of developers in choosing and changing licenses. We also investigated the problem of traceability of license changes. These two studies are fundamental components for understanding problems that developers face so that we can better support developers with licensing.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889259"}, {"primary_key": "4128814", "vector": [], "sparse_vector": [], "title": "Opaque service virtualisation: a practical tool for emulating endpoint systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Large enterprise software systems make many complex interactions with other services in their environment. Developing and testing for production-like conditions is therefore a very challenging task. Current approaches include emulation of dependent services using either explicit modelling or record-and-replay approaches. Models require deep knowledge of the target services while record-and-replay is limited in accuracy. Both face developmental and scaling issues. We present a new technique that improves the accuracy of record-and-replay approaches, without requiring prior knowledge of the service protocols. The approach uses Multiple Sequence Alignment to derive message prototypes from recorded system interactions and a scheme to match incoming request messages against prototypes to generate response messages. We use a modified Needleman-Wunsch algorithm for distance calculation during message matching. Our approach has shown greater than 99% accuracy for four evaluated enterprise system messaging protocols. The approach has been successfully integrated into the CA Service Virtualization commercial product to complement its existing techniques.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889242"}, {"primary_key": "4128815", "vector": [], "sparse_vector": [], "title": "Release planning of mobile apps based on user reviews.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Developers have to to constantly improve their apps by fixing critical bugs and implementing the most desired features in order to gain shares in the continuously increasing and competitive market of mobile apps. A precious source of information to plan such activities is represented by reviews left by users on the app store. However, in order to exploit such information developers need to manually analyze such reviews. This is something not doable if, as frequently happens, the app receives hundreds of reviews per day. In this paper we introduce CLAP (Crowd Listener for releAse Planning), a thorough solution to (i) categorize user reviews based on the information they carry out (e.g., bug reporting), (ii) cluster together related reviews (e.g., all reviews reporting the same bug), and (iii) automatically prioritize the clusters of reviews to be implemented when planning the subsequent app release. We evaluated all the steps behind CLAP, showing its high accuracy in categorizing and clustering reviews and the meaningfulness of the recommended prioritizations. Also, given the availability of CLAP as a working tool, we assessed its practical applicability in industrial environments.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884818"}, {"primary_key": "4128820", "vector": [], "sparse_vector": [], "title": "Enhancing test case prioritization in an industrial setting with resource awareness and multi-objective search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Test case prioritization is an essential part of test execution systems for large organizations developing software systems in the context that their software versions are released very frequently. They must be tested on a variety of compatible hardware with different configurations to ensure correct functioning of a software version on a compatible hardware. In practice, test case execution must not only execute cost-effective test cases in an optimal order, but also optimally allocate required test resources, in order to deliver high quality software releases.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889240"}, {"primary_key": "4128821", "vector": [], "sparse_vector": [], "title": "A practical guide to select quality indicators for assessing pareto-based search algorithms in search-based software engineering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many software engineering problems are multi-objective in nature, which has been largely recognized by the Search-based Software Engineering (SBSE) community. In this regard, Pareto-based search algorithms, e.g., Non-dominated Sorting Genetic Algorithm II, have already shown good performance for solving multi-objective optimization problems. These algorithms produce Pareto fronts, where each Pareto front consists of a set of non-dominated solutions. Eventually, a user selects one or more of the solutions from a Pareto front for their specific problems. A key challenge of applying Pareto-based search algorithms is to select appropriate quality indicators, e.g., hypervolume, to assess the quality of Pareto fronts. Based on the results of an extended literature review, we found that the current literature and practice in SBSE lacks a practical guide for selecting quality indicators despite a large number of published SBSE works. In this direction, the paper presents a practical guide for the SBSE community to select quality indicators for assessing Pareto-based search algorithms in different software engineering contexts. The practical guide is derived from the following complementary theoretical and empirical methods: 1) key theoretical foundations of quality indicators; 2) evidence from an extended literature review; and 3) evidence collected from an extensive experiment that was conducted to evaluate eight quality indicators from four different categories with six Pareto-based search algorithms using three real industrial problems from two diverse domains.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884880"}, {"primary_key": "4128822", "vector": [], "sparse_vector": [], "title": "Automatically learning semantic features for defect prediction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Software defect prediction, which predicts defective code regions, can help developers find bugs and prioritize their testing efforts. To build accurate prediction models, previous studies focus on manually designing features that encode the characteristics of programs and exploring different machine learning algorithms. Existing traditional features often fail to capture the semantic differences of programs, and such a capability is needed for building accurate prediction models.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884804"}, {"primary_key": "4128825", "vector": [], "sparse_vector": [], "title": "What went right and what went wrong: an analysis of 155 postmortems from game development.", "authors": ["<PERSON>.", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In game development, software teams often conduct postmortems to reflect on what went well and what went wrong in a project. The postmortems are shared publicly on gaming sites or at developer conferences. In this paper, we present an analysis of 155 postmortems published on the gaming site Gamasutra.com. We identify characteristics of game development, link the characteristics to positive and negative experiences in the postmortems and distill a set of best practices and pitfalls for game development.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889253"}, {"primary_key": "4128834", "vector": [], "sparse_vector": [], "title": "Identifying and quantifying architectural debt.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Our prior work showed that the majority of error-prone source files in a software system are architecturally connected. Flawed architectural relations propagate defects among these files and accumulate high maintenance costs over time, just like debts accumulate interest. We model groups of architecturally connected files that accumulate high maintenance costs as architectural debts. To quantify such debts, we formally define architectural debt, and show how to automatically identify debts, quantify their maintenance costs, and model these costs over time. We describe a novel history coupling probability matrix for this purpose, and identify architecture debts using 4 patterns of architectural flaws shown to correlate with reduced software quality. We evaluate our approach on 7 large-scale open source projects, and show that a significant portion of total project maintenance effort is consumed by paying interest on architectural debts. The top 5 architectural debts, covering a small portion (8% to 25%) of each project's error-prone files, capture a significant portion (20% to 61%) of each project's maintenance effort. Finally, we show that our approach reveals how architectural issues evolve into debts over time.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884822"}, {"primary_key": "4128835", "vector": [], "sparse_vector": [], "title": "Revisit of automatic debugging via human focus-tracking analysis.", "authors": ["<PERSON><PERSON>", "Zicong Liu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In many fields of software engineering, studies on human behavior have attracted a lot of attention; however, few such studies exist in automated debugging. <PERSON><PERSON><PERSON> and <PERSON><PERSON> conducted a pioneering study comparing the performance of programmers in debugging with and without a ranking-based fault localization technique, namely Spectrum-Based Fault Localization (SBFL). In this paper, we revisit the actual helpfulness of SBFL, by addressing some major problems that were not resolved in <PERSON><PERSON><PERSON> and <PERSON><PERSON>'s study. Our investigation involved 207 participants and 17 debugging tasks. A user-friendly SBFL tool was adopted. It was found that SBFL tended not to be helpful in improving the efficiency of debugging. By tracking and analyzing programmers' focus of attention, we characterized their source code navigation patterns and provided in-depth explanations to the observations. Results indicated that (1) a short \"first scan\" on the source code tended to result in inefficient debugging; and (2) inspections on the pinpointed statements during the \"follow-up browsing\" were normally just quick skimming. Moreover, we found that the SBFL assistance may even slightly weaken programmers' abilities in fault detection. Our observations imply interference between the mechanism of automated fault localization and the actual assistance needed by programmers in debugging. To resolve this interference, we provide several insights and suggestions.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884834"}, {"primary_key": "4128837", "vector": [], "sparse_vector": [], "title": "Advances in unit testing: theory and practice.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Parameterized unit testing, recent advances in unit testing, is a new methodology extending the previous industry practice based on traditional unit tests without parameters. A parameterized unit test (PUT) is simply a test method that takes parameters, calls the code under test, and states assertions. Parameterized unit testing allows the separation of two testing concerns or tasks: the specification of external, black-box behavior (i.e., assertions or specifications) by developers and the generation and selection of internal, white-box test inputs (i.e., high-code-covering test inputs) by tools. PUTs have been supported by various testing frameworks. Various open source and industrial testing tools also exist to generate test inputs for PUTs. This technical briefing presents latest research on principles and techniques, as well as practical considerations to apply parameterized unit testing on real-world programs, highlighting success stories, research and education achievements, and future research directions in developer testing.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2891056"}, {"primary_key": "4128839", "vector": [], "sparse_vector": [], "title": "Recommending developers with supplementary information for issue request resolution.", "authors": ["<PERSON>", "<PERSON>bing Sun", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Software changes, new features and bugs are generally reported as issue requests which need to be quickly and efficiently resolved. A large amount of approaches have been proposed to recommend suitable developers to resolve software issues [1, 2, 6, 3]. These techniques tend to recommend senior developers who have luxuriant developing experience, which prejudice someone who just joined the team. But when the senior developers are not available, these approaches cannot effectively help select an alternative suitable developer (maybe a junior developer) to implement the issue. On the other hand, the junior developers may be not skilled to the issue request and target system. They may also need to refer to other software repositories in understanding the changing task, which is costly and time-consuming.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892644"}, {"primary_key": "4128840", "vector": [], "sparse_vector": [], "title": "From word embeddings to document similarities for improved information retrieval in software engineering.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The application of information retrieval techniques to search tasks in software engineering is made difficult by the lexical gap between search queries, usually expressed in natural language (e.g. English), and retrieved documents, usually expressed in code (e.g. programming languages). This is often the case in bug and feature location, community question answering, or more generally the communication between technical personnel and non-technical stake holders in a software project. In this paper, we propose bridging the lexical gap by projecting natural language statements and code snippets as meaning vectors in a shared representation space. In the proposed architecture, word embeddings are first trained on API documents, tutorials, and reference documents, and then aggregated in order to estimate semantic similarities between documents. Empirical evaluations show that the learned vector space embeddings lead to improvements in a previously explored bug localization task and a newly defined task of linking API documents to computer programming questions.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884862"}, {"primary_key": "4128842", "vector": [], "sparse_vector": [], "title": "Reference hijacking: patching, protecting and analyzing on unmodified and non-rooted android devices.", "authors": ["<PERSON>", "<PERSON>", "Wenchang Shi", "<PERSON><PERSON> Zhu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many efforts have been paid to enhance the security of Android. However, less attention has been given to how to practically adopt the enhancements on off-the-shelf devices. In particular, securing Android devices often requires modifying their write-protected underlying system component files (especially the system libraries) by flashing or rooting devices, which is unacceptable in many realistic cases. In this paper, a novel technique, called reference hijacking, is presented to address the problem. By introducing a specially designed reset procedure, a new execution environment is constructed for the target application, in which the reference to the underlying system libraries will be redirected to the security-enhanced alternatives. The technique can be applicable to both the Dalvik and Android Runtime (ART) environments and to almost all mainstream Android versions (2.x to 5.x). To demonstrate the capability of reference hijacking, we develop three prototype systems, PatchMan, ControlMan, and TaintMan, to enforce specific security enhancements, involving patching vulnerabilities, protecting inter-component communications, and performing dynamic taint analysis for the target application. These three prototypes have been successfully deployed on a number of popular Android devices from different manufacturers, without modifying the underlying system. The evaluation results show that they are effective and do not introduce noticeable overhead. They strongly support that reference hijacking can substantially improve the practicability of many security enhancement efforts for Android.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884863"}, {"primary_key": "4128843", "vector": [], "sparse_vector": [], "title": "VDTest: an automated framework to support testing for virtual devices.", "authors": ["Tingting Yu", "<PERSON>", "<PERSON>"], "summary": "The use of virtual devices in place of physical hardware is increasing in activities such as design, testing and debugging. Yet virtual devices are simply software applications, and like all software they are prone to faults. A full system simulator (FSS), is a class of virtual machine that includes a large set of virtual devices -- enough to run the full target software stack. Defects in an FSS virtual device may have cascading effects as the incorrect behavior can be propagated forward to many different platforms as well as to guest programs. In this work we present VDTest, a novel framework for testing virtual devices within an FSS. VDTest begins by generating a test specification obtained through static analysis. It then employs a two-phase testing approach to test virtual components both individually and in combination. It leverages a differential oracle strategy, taking advantage of the existence of a physical or golden device to eliminate the need for manually generating test oracles. In an empirical study using both open source and commercial FSSs, we found 64 faults, 83% more than random testing.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884866"}, {"primary_key": "4128845", "vector": [], "sparse_vector": [], "title": "Theories of everything.", "authors": ["<PERSON>"], "summary": "In 2025 semantic tools for software engineering will be mature, and their frequency of use in software development will still be disappointing. This proposal explains how research directed at building theories of everything (or, at least, important software domains) can consolidate progress and bring semantic tools into the mainstream of software practice.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889213"}, {"primary_key": "4128846", "vector": [], "sparse_vector": [], "title": "Automatic model generation from documentation for Java API functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Shiqing Ma", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Feng <PERSON>"], "summary": "Modern software systems are becoming increasingly complex, relying on a lot of third-party library support. Library behaviors are hence an integral part of software behaviors. Analyzing them is as important as analyzing the software itself. However, analyzing libraries is highly challenging due to the lack of source code, implementation in different languages, and complex optimizations. We observe that many Java library functions provide excellent documentation, which concisely describes the functionalities of the functions. We develop a novel technique that can construct models for Java API functions by analyzing the documentation. These models are simpler implementations in Java compared to the original ones and hence easier to analyze. More importantly, they provide the same functionalities as the original functions. Our technique successfully models 326 functions from 14 widely used Java classes. We also use these models in static taint analysis on Android apps and dynamic slicing for Java programs, demonstrating the effectiveness and efficiency of our models.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884881"}, {"primary_key": "4128848", "vector": [], "sparse_vector": [], "title": "Guiding the crowds for Android testing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Chunrong Fang", "Zicong Liu"], "summary": "Crowdsourced testing is an emerging trend in software testing, especially for mobile testing due to Android fragment issues. Gaining the productive outputs of high quality always comes with the assumption that the workers are experienced. However, the crowd of testers are not usually professionals. Thus, it becomes an interesting topic to guide the crowd to obtain domain knowledge and fulfill tasks. Motivated by an article in Science[1], we propose an approach in this paper to enhance 'the power of the crowd'. (1) Along with professionals taking the task, exception information is recorded into database to provide hints for the crowd.(2) With the feedback from new exceptions caught in processing of crowd, the database is enriched. Such an iterative process will guide the crowd to finish their tasks effectively.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892659"}, {"primary_key": "4128851", "vector": [], "sparse_vector": [], "title": "HoliCoW: automatically breaking team-based software projects to motivate student testing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Intensive testing is often applied by professional software engineers to assure the quality of enterprise information technology (IT) systems. For example, Netflix's Simian Army consists of services that generate various types of failures, detect abnormal conditions, and test the ability of cloud-based enterprise IT software to survive them. Although software engineering students should be taught these types of rigorous testing techniques, it is often hard to motivate students to produce high-quality test suites for their assignments since classroom environments lack the harsh outcomes of unexpected system failures.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889197"}, {"primary_key": "4128853", "vector": [], "sparse_vector": [], "title": "Cross-project defect prediction using a connectivity-based unsupervised classifier.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Defect prediction on projects with limited historical data has attracted great interest from both researchers and practitioners. Cross-project defect prediction has been the main area of progress by reusing classifiers from other projects. However, existing approaches require some degree of homogeneity (e.g., a similar distribution of metric values) between the training projects and the target project. Satisfying the homogeneity requirement often requires significant effort (currently a very active area of research).", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2884781.2884839"}, {"primary_key": "4128855", "vector": [], "sparse_vector": [], "title": "On the effectiveness of labeled latent dirichlet allocation in automatic bug-report categorization.", "authors": ["Minhaz F. Zibran"], "summary": "Bug-reports are valuable sources of information. However, study of the bug-reports' content written in natural language demands tedious human efforts for manual interpretation. This difficulty limits the scale of empirical studies, which rely on interpretation and categorization of bug-reports. In this work, we investigate the effectiveness of Labeled Latent Dirichlet Allocation (LLDA) in automatic classification of bug-reports into a predefined set of categories.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2892646"}, {"primary_key": "4128856", "vector": [], "sparse_vector": [], "title": "Observations on knowledge transfer of professional software developers during pair programming.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Context: Software development is knowledge-intense work, and so is pair programming. However, the importance of knowledge transfer in pair programming is usually only stressed for expert-novice constellations. Goal: Understand how knowledge transfer during pair programming works and eventually provide guidance for practitioners. Method: Detailed qualitative data analysis of full-length recordings of industrial pair programming sessions. Results: Expert software developers need to transfer knowledge, too, in order to conduct productive pair programming sessions. There is a diversity of beneficial and potentially problematic patterns, which even good pairs do not steadily apply or avoid, respectively. Conclusions: Pair programming is a versatile practice that even experts can profit from. Knowledge transfer skills do not automatically emerge from good software development skills, but can probably be learned.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": "10.1145/2889160.2889249"}, {"primary_key": "4210145", "vector": [], "sparse_vector": [], "title": "Proceedings of the 38th International Conference on Software Engineering, ICSE 2016, Austin, TX, USA, May 14-22, 2016 - Companion Volume", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ICSE 2016 week starts Saturday, May 14 with 4 days of ICSE workshops and symposia, including a day of technical briefings on May 17. The week culminates in the 3-day main conference program May 18-20. The prestigious research track is the heart of the main conference program. However, complementing this track, half-a-dozen smaller tracks offer stimulating sessions for a variety of audiences.", "published": "2016-01-01", "category": "icse", "pdf_url": "", "sub_summary": "", "source": "icse", "doi": ""}]