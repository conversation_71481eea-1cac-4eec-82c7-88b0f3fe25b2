[{"primary_key": "4501732", "vector": [], "sparse_vector": [], "title": "Chaos: scale-out graph processing from secondary storage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Chaos scales graph processing from secondary storage to multiple machines in a cluster. Earlier systems that process graphs from secondary storage are restricted to a single machine, and therefore limited by the bandwidth and capacity of the storage system on a single machine. Chaos is limited only by the aggregate bandwidth and capacity of all storage devices in the entire cluster.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815408"}, {"primary_key": "4501733", "vector": [], "sparse_vector": [], "title": "Yesquel: scalable sql storage for web applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Web applications have been shifting their storage systems from sql to nosql systems. nosql systems scale well but drop many convenient sql features, such as joins, secondary indexes, and/or transactions. We design, develop, and evaluate Yesquel, a system that provides performance and scalability comparable to nosql with all the features of a sql relational system. Yesquel has a new architecture and a new distributed data structure, called YDBT, which <PERSON><PERSON> uses for storage, and which performs well under contention by many concurrent clients. We evaluate Yes<PERSON> and find that <PERSON><PERSON> performs almost as well as Redis---a popular nosql system---and much better than mysql Cluster, while handling sql queries at scale.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815413"}, {"primary_key": "4501734", "vector": [], "sparse_vector": [], "title": "Virtual CPU validation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>n <PERSON>"], "summary": "Testing the hypervisor is important for ensuring the correct operation and security of systems, but it is a hard and challenging task. We observe, however, that the challenge is similar in many respects to that of testing real CPUs. We thus propose to apply the testing environment of CPU vendors to hypervisors. We demonstrate the advantages of our proposal by adapting Intel's testing facility to the Linux KVM hypervisor. We uncover and fix 117 bugs, six of which are security vulnerabilities. We further find four flaws in Intel virtualization technology, causing a disparity between the observable behavior of code running on virtual and bare-metal servers.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815420"}, {"primary_key": "4501735", "vector": [], "sparse_vector": [], "title": "Opportunistic storage maintenance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Storage systems rely on maintenance tasks, such as backup and layout optimization, to ensure data availability and good performance. These tasks access large amounts of data and can significantly impact foreground applications. We argue that storage maintenance can be performed more efficiently by prioritizing processing of data that is currently cached in memory. Data can be cached either due to other maintenance tasks requesting it previously, or due to overlapping foreground I/O activity.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815424"}, {"primary_key": "4501736", "vector": [], "sparse_vector": [], "title": "Software defined batteries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Pan Hu", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Different battery chemistries perform better on different axes, such as energy density, cost, peak power, recharge time, longevity, and efficiency. Mobile system designers are constrained by existing technology, and are forced to select a single chemistry that best meets their diverse needs, thereby compromising other desirable features. In this paper, we present a new hardware-software system, called Software Defined Battery (SDB), which allows system designers to integrate batteries of different chemistries. SDB exposes APIs to the operating system which control the amount of charge flowing in and out of each battery, enabling it to dynamically trade one battery property for another depending on Application And/Or User Needs. Using microbenchmarks from our prototype SDB implementation, and through detailed simulations, we demonstrate that it is possible to combine batteries which individually excel along different axes to deliver an enhanced collective performance when compared to traditional battery packs.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815429"}, {"primary_key": "4501740", "vector": [], "sparse_vector": [], "title": "Using Crash Hoare logic for certifying the FSCQ file system.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "FSCQ is the first file system with a machine-checkable proof (using the Coq proof assistant) that its implementation meets its specification and whose specification includes crashes. FSCQ provably avoids bugs that have plagued previous file systems, such as performing disk writes without sufficient barriers or forgetting to zero out directory blocks. If a crash happens at an inopportune time, these bugs can lead to data loss. FSCQ's theorems prove that, under any sequence of crashes followed by reboots, FSCQ will recover the file system correctly without losing data.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815402"}, {"primary_key": "4501742", "vector": [], "sparse_vector": [], "title": "Paxos made transparent.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "State machine replication (SMR) leverages distributed consensus protocols such as Paxos to keep multiple replicas of a program consistent in face of replica failures or network partitions. This fault tolerance is enticing on implementing a principled SMR system that replicates general programs, especially server programs that demand high availability. Unfortunately, SMR assumes deterministic execution, but most server programs are multithreaded and thus nondeterministic. Moreover, existing SMR systems provide narrow state machine interfaces to suit specific programs, and it can be quite strenuous and error-prone to orchestrate a general program into these interfaces", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815427"}, {"primary_key": "4501743", "vector": [], "sparse_vector": [], "title": "Coz: finding code that counts with causal profiling.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Improving performance is a central concern for software developers. To locate optimization opportunities, developers rely on software profilers. However, these profilers only report where programs spent their time: optimizing that code may have no impact on performance. Past profilers thus both waste developer time and make it difficult for them to uncover significant optimization opportunities.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815409"}, {"primary_key": "4501748", "vector": [], "sparse_vector": [], "title": "No compromises: distributed transactions with consistency, availability, and performance.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Transactions with strong consistency and high availability simplify building and reasoning about distributed systems. However, previous implementations performed poorly. This forced system designers to avoid transactions completely, to weaken consistency guarantees, or to provide single-machine transactions that require programmers to partition their data. In this paper, we show that there is no need to compromise in modern data centers. We show that a main memory distributed computing platform called FaRM can provide distributed transactions with strict serializability, high performance, durability, and high availability. FaRM achieves a peak throughput of 140 million TATP transactions per second on 90 machines with a 4.9 TB database, and it recovers from a failure in less than 50 ms. Key to achieving these results was the design of new transaction, replication, and recovery protocols from first principles to leverage commodity networks with RDMA and a new, inexpensive approach to providing non-volatile DRAM.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815425"}, {"primary_key": "4501749", "vector": [], "sparse_vector": [], "title": "Interruptible tasks: treating memory pressure as interrupts for highly scalable data-parallel programs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Guo<PERSON> Xu", "<PERSON>", "<PERSON>"], "summary": "Real-world data-parallel programs commonly suffer from great memory pressure, especially when they are executed to process large datasets. Memory problems lead to excessive GC effort and out-of-memory errors, significantly hurting system performance and scalability. This paper proposes a systematic approach that can help data-parallel tasks survive memory pressure, improving their performance and scalability without needing any manual effort to tune system parameters. Our approach advocates interruptible task (ITask), a new type of data-parallel tasks that can be interrupted upon memory pressure---with part or all of their used memory reclaimed---and resumed when the pressure goes away.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815407"}, {"primary_key": "4501750", "vector": [], "sparse_vector": [], "title": "IronFleet: proving practical distributed systems correct.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Distributed systems are notorious for harboring subtle bugs. Verification can, in principle, eliminate these bugs a priori, but verification has historically been difficult to apply at full-program scale, much less distributed-system scale.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815428"}, {"primary_key": "4501752", "vector": [], "sparse_vector": [], "title": "JouleGuard: energy guarantees for approximate applications.", "authors": ["<PERSON>"], "summary": "Energy consumption limits battery life in mobile devices and increases costs for servers and data centers. Approximate computing addresses energy concerns by allowing applications to trade accuracy for decreased energy consumption. Approximation frameworks can guarantee accuracy or performance and generally reduce energy usage; however, they provide no energy guarantees. Such guarantees would be beneficial for users who have a fixed energy budget and want to maximize application accuracy within that budget. We address this need by presenting JouleGuard: a runtime control system that coordinates approximate applications with system resource usage to provide control theoretic formal guarantees of energy consumption, while maximizing accuracy. We implement JouleGuard and test it on three different platforms (a mobile, tablet, and server) with eight different approximate applications created from two different frameworks. We find that JouleGuard respects energy budgets, provides near optimal accuracy, adapts to phases in application workload, and provides better outcomes than application approximation or system resource adaptation alone. JouleGuard is general with respect to the applications and systems it controls, making it a suitable runtime for a number of approximate computing frameworks.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815403"}, {"primary_key": "4501753", "vector": [], "sparse_vector": [], "title": "Vuvuzela: scalable private messaging resistant to traffic analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Private messaging over the Internet has proven challenging to implement, because even if message data is encrypted, it is difficult to hide metadata about who is communicating in the face of traffic analysis. Systems that offer strong privacy guarantees, such as Dissent [36], scale to only several thousand clients, because they use techniques with superlinear cost in the number of clients (e.g., each client broadcasts their message to all other clients). On the other hand, scalable systems, such as Tor, do not protect against traffic analysis, making them ineffective in an era of pervasive network monitoring.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815417"}, {"primary_key": "4501757", "vector": [], "sparse_vector": [], "title": "Failure sketching: a technique for automated root cause diagnosis of in-production failures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Developers spend a lot of time searching for the root causes of software failures. For this, they traditionally try to reproduce those failures, but unfortunately many failures are so hard to reproduce in a test environment that developers spend days or weeks as ad-hoc detectives. The shortcomings of many solutions proposed for this problem prevent their use in practice.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815412"}, {"primary_key": "4501760", "vector": [], "sparse_vector": [], "title": "Implementing linearizability at large scale and low latency.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Linearizability is the strongest form of consistency for concurrent systems, but most large-scale storage systems settle for weaker forms of consistency. RIFL provides a general-purpose mechanism for converting at-least-once RPC semantics to exactly-once semantics, thereby making it easy to turn non-linearizable operations into linearizable ones. RIFL is designed for large-scale systems and is lightweight enough to be used in low-latency environments. RIFL handles data migration by associating linearizability metadata with objects in the underlying store and migrating metadata with the corresponding objects. It uses a lease mechanism to implement garbage collection for metadata. We have implemented RIFL in the RAMCloud storage system and used it to make basic operations such as writes and atomic increments linearizable; RIFL adds only 530 ns to the 13.5 μs base latency for durable writes. We also used RIFL to construct a new multi-object transaction mechanism in RAMCloud; RIFL's facilities significantly simplified the transaction implementation. The transaction mechanism can commit simple distributed transactions in about 20 μs and it outperforms the H-Store main-memory database system for the TPC-C benchmark.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815416"}, {"primary_key": "4501761", "vector": [], "sparse_vector": [], "title": "Drowsy power management.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Portable computing devices have fast multi-core processors, large memories, and many on-board sensors and radio interfaces, but are often limited by their energy consumption. Traditional power management subsystems have been extended for smartphones and other portable devices, with the intention of maximizing the time that the devices are in a low-power \"sleep\" state. The approaches taken by these subsystems prove inefficient for many short-lived tasks common to portable devices, e.g., querying a sensor or polling a cloud service.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815414"}, {"primary_key": "4501765", "vector": [], "sparse_vector": [], "title": "Existential consistency: measuring and understanding consistency at Facebook.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Replicated storage for large Web services faces a trade-off between stronger forms of consistency and higher performance properties. Stronger consistency prevents anomalies, i.e., unexpected behavior visible to users, and reduces programming complexity. There is much recent work on improving the performance properties of systems with stronger consistency, yet the flip-side of this trade-off remains elusively hard to quantify. To the best of our knowledge, no prior work does so for a large, production Web service.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815426"}, {"primary_key": "4501766", "vector": [], "sparse_vector": [], "title": "Pivot tracing: dynamic causal monitoring for distributed systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Monitoring and troubleshooting distributed systems is notoriously difficult; potential problems are complex, varied, and unpredictable. The monitoring and diagnosis tools commonly used today -- logs, counters, and metrics -- have two important limitations: what gets recorded is defined a priori, and the information is recorded in a component- or machine-centric way, making it extremely hard to correlate events that cross these boundaries. This paper presents Pivot Tracing, a monitoring framework for distributed systems that addresses both limitations by combining dynamic instrumentation with a novel relational operator: the happened-before join. Pivot Tracing gives users, at runtime, the ability to define arbitrary metrics at one point of the system, while being able to select, filter, and group by events meaningful at other parts of the system, even when crossing component or machine boundaries. We have implemented a prototype of Pivot Tracing for Java-based systems and evaluate it on a heterogeneous Hadoop cluster comprising HDFS, HBase, MapReduce, and YARN. We show that Pivot Tracing can effectively identify a diverse range of root causes such as software bugs, misconfiguration, and limping hardware. We show that Pivot Tracing is dynamic, extensible, and enables cross-tier analysis between inter-operating applications, with low execution overhead.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815415"}, {"primary_key": "4501769", "vector": [], "sparse_vector": [], "title": "Read-log-update: a lightweight synchronization mechanism for concurrent programming.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper introduces read-log-update (RLU), a novel extension of the popular read-copy-update (RCU) synchronization mechanism that supports scalability of concurrent code by allowing unsynchronized sequences of reads to execute concurrently with updates. RLU overcomes the major limitations of RCU by allowing, for the first time, concurrency of reads with multiple writers, and providing automation that eliminates most of the programming difficulty associated with RCU programming. At the core of the RLU design is a logging and coordination mechanism inspired by software transactional memory algorithms. In a collection of micro-benchmarks in both the kernel and user space, we show that RLU both simplifies the code and matches or improves on the performance of RCU. As an example of its power, we show how it readily scales the performance of a real-world application, Kyoto Cabinet, a truly difficult concurrent programming feat to attempt in general, and in particular with classic RCU.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815406"}, {"primary_key": "4501770", "vector": [], "sparse_vector": [], "title": "Cross-checking semantic correctness: the case of finding file system bugs.", "authors": ["Chang<PERSON><PERSON> Min", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Today, systems software is too complex to be bug-free. To find bugs in systems software, developers often rely on code checkers, like Linux's Sparse. However, the capability of existing tools used in commodity, large-scale systems is limited to finding only shallow bugs that tend to be introduced by simple programmer mistakes, and so do not require a deep understanding of code to find them. Unfortunately, the majority of bugs as well as those that are difficult to find are semantic ones, which violate high-level rules or invariants (e.g., missing a permission check). Thus, it is difficult for code checkers lacking the understanding of a programmer's true intention to reason about semantic correctness.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815422"}, {"primary_key": "4501773", "vector": [], "sparse_vector": [], "title": "E2: a framework for NFV applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Sangjin Han", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "By moving network appliance functionality from proprietary hardware to software, Network Function Virtualization promises to bring the advantages of cloud computing to network packet processing. However, the evolution of cloud computing (particularly for data analytics) has greatly benefited from application-independent methods for scaling and placement that achieve high efficiency while relieving programmers of these burdens. NFV has no such general management solutions. In this paper, we present a scalable and application-agnostic scheduling framework for packet processing, and compare its performance to current approaches.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815423"}, {"primary_key": "4501775", "vector": [], "sparse_vector": [], "title": "Parallelizing user-defined aggregations using symbolic execution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "User-defined aggregations (UDAs) are integral to large-scale data-processing systems, such as MapReduce and Hadoop, because they let programmers express application-specific aggregation logic. System-supported associative aggregations, such as counting or finding the maximum, are data-parallel and thus these systems optimize their execution, leading in many cases to orders-of-magnitude performance improvements. These optimizations, however, are not possible on arbitrary UDAs.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815418"}, {"primary_key": "4501777", "vector": [], "sparse_vector": [], "title": "SibylFS: formal specification and oracle-based testing for POSIX and real-world file systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Systems depend critically on the behaviour of file systems, but that behaviour differs in many details, both between implementations and between each implementation and the POSIX (and other) prose specifications. Building robust and portable software requires understanding these details and differences, but there is currently no good way to systematically describe, investigate, or test file system behaviour across this complex multi-platform interface.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815411"}, {"primary_key": "4501783", "vector": [], "sparse_vector": [], "title": "Holistic configuration management at Facebook.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Facebook's web site and mobile apps are very dynamic. Every day, they undergo thousands of online configuration changes, and execute trillions of configuration checks to personalize the product features experienced by hundreds of million of daily active users. For example, configuration changes help manage the rollouts of new product features, perform A/B testing experiments on mobile devices to identify the best echo-canceling parameters for VoIP, rebalance the load across global regions, and deploy the latest machine learning models to improve News Feed ranking. This paper gives a comprehensive description of the use cases, design, implementation, and usage statistics of a suite of tools that manage Facebook's configuration end-to-end, including the frontend products, backend systems, and mobile apps.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815401"}, {"primary_key": "4501784", "vector": [], "sparse_vector": [], "title": "Arabesque: a system for distributed graph mining.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Distributed data processing platforms such as MapReduce and Pregel have substantially simplified the design and deployment of certain classes of distributed graph analytics algorithms. However, these platforms do not represent a good match for distributed graph mining problems, as for example finding frequent subgraphs in a graph. Given an input graph, these problems require exploring a very large number of subgraphs and finding patterns that match some \"interestingness\" criteria desired by the user. These algorithms are very important for areas such as social networks, semantic web, and bioinformatics.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815410"}, {"primary_key": "4501785", "vector": [], "sparse_vector": [], "title": "How to get more value from your file system directory cache.", "authors": ["Chia-Che Tsai", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>zhen<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Applications frequently request file system operations that traverse the file system directory tree, such as opening a file or reading a file's metadata. As a result, caching file system directory structure and metadata in memory is an important performance optimization for an OS kernel.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815405"}, {"primary_key": "4501787", "vector": [], "sparse_vector": [], "title": "Fast in-memory transaction processing using RDMA and HTM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Chen"], "summary": "We present DrTM, a fast in-memory transaction processing system that exploits advanced hardware features (i.e., RDMA and HTM) to improve latency and throughput by over one order of magnitude compared to state-of-the-art distributed transaction systems. The high performance of DrTM are enabled by mostly offloading concurrency control within a local machine into HTM and leveraging the strong consistency between RDMA and HTM to ensure serializability among concurrent transactions across machines. We further build an efficient hash table for DrTM by leveraging HTM and RDMA to simplify the design and notably improve the performance. We describe how DrTM supports common database features like read-only transactions and logging for durability. Evaluation using typical OLTP workloads including TPC-C and SmallBank show that DrTM scales well on a 6-node cluster and achieves over 5.52 and 138 million transactions per second for TPC-C and SmallBank Respectively. This number outperforms a state-of-the-art distributed transaction system (namely Calvin) by at least 17.9X for TPC-C.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815419"}, {"primary_key": "4501789", "vector": [], "sparse_vector": [], "title": "High-performance ACID via modular concurrency control.", "authors": ["<PERSON>", "Chunzhi Su", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper describes the design, implementation, and evaluation of Callas, a distributed database system that offers to unmodified, transactional ACID applications the opportunity to achieve a level of performance that can currently only be reached by rewriting all or part of the application in a BASE/NoSQL Style. The key to combining performance and ease of programming is to decouple the ACID abstraction---which Callas offers identically for all transactions---from the mechanism used to support it. MCC, the new Modular approach to Concurrency Control at the core of Callas, makes it possible to partition transactions in groups with the guarantee that, as long as the concurrency control mechanism within each group upholds a given isolation property, that property will also hold among transactions in different groups. Because of their limited and specialized scope, these group-specific mechanisms can be customized for concurrency with unprecedented aggressiveness. In our MySQL Cluster-based prototype, Callas yields an 8.2x throughput gain for TPC-C with no programming effort.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815430"}, {"primary_key": "4501791", "vector": [], "sparse_vector": [], "title": "Split-level I/O scheduling.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>au"], "summary": "We introduce split-level I/O scheduling, a new framework that splits I/O scheduling logic across handlers at three layers of the storage stack: block, system call, and page cache. We demonstrate that traditional block-level I/O schedulers are unable to meet throughput, latency, and isolation goals. By utilizing the split-level framework, we build a variety of novel schedulers to readily achieve these goals: our Actually Fair Queuing scheduler reduces priority-misallocation by 28x; our Split-Deadline scheduler reduces tail latencies by 4x; our Split-Token scheduler reduces sensitivity to interference by 6x. We show that the framework is general and operates correctly with disparate file systems (ext4 and XFS). Finally, we demonstrate that split-level scheduling serves as a useful foundation for databases (SQLite and PostgreSQL), hypervisors (QEMU), and distributed file systems (HDFS), delivering improved isolation and performance in these important application scenarios.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815421"}, {"primary_key": "4501795", "vector": [], "sparse_vector": [], "title": "Building consistent transactions with inconsistent replication.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dan R. K. <PERSON>s"], "summary": "Application programmers increasingly prefer distributed storage systems with strong consistency and distributed transactions (e.g., Google's Spanner) for their strong guarantees and ease of use. Unfortunately, existing transactional storage systems are expensive to use -- in part because they require costly replication protocols, like Paxos, for fault tolerance. In this paper, we present a new approach that makes transactional storage systems more affordable: we eliminate consistency from the replication protocol while still providing distributed transactions with strong consistency to applications.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": "10.1145/2815400.2815404"}, {"primary_key": "4521123", "vector": [], "sparse_vector": [], "title": "Proceedings of the 25th Symposium on Operating Systems Principles, SOSP 2015, Monterey, CA, USA, October 4-7, 2015", "authors": ["<PERSON>", "<PERSON>"], "summary": "Welcome to the Proceedings of the 25th ACM Symposium on Operating Systems Principles (SOSP 2015), held at the Portola Hotel & Spa, Monterey, California, USA. This year's program includes 30 wonderful papers which cover topics ranging from high performance transaction processing systems to formal specification for practical implementations, from techniques for energy-aware systems to experiences with globally distributed software in the wild, from bug finding to big data. The program committee worked hard to select some of the most creative and interesting ideas in computer systems research today, and each accepted paper was lovingly shepherded by a program committee member to make sure the papers are as readable and complete as possible. We hope you will enjoy the program as much as we did in selecting it.", "published": "2015-01-01", "category": "sosp", "pdf_url": "", "sub_summary": "", "source": "sosp", "doi": ""}]