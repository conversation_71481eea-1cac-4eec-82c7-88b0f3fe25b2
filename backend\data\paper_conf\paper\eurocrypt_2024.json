[{"primary_key": "582683", "vector": [], "sparse_vector": [], "title": "Perfect Asynchronous MPC with Linear Communication Overhead.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study secure multiparty computation in the asynchronous setting with perfect security and optimal resilience (less than one-fourth of the participants are malicious). It has been shown that every function can be computed in this model [Ben-<PERSON>, <PERSON>etti, and <PERSON>, STOC’1993]. Despite 30 years of research, all protocols in the asynchronous setting require\\(\\varOmega (n^2C)\\)communication complexity for computing a circuit withCmultiplication gates. In contrast, for nearly 15 years, in the synchronous setting, it has been known how to achieve\\({\\mathcal {O}}(nC)\\)communication complexity (<PERSON><PERSON><PERSON> and <PERSON>rt; TCC 2008). The techniques for achieving this result in the synchronous setting are not known to be sufficient for obtaining an analogous result in the asynchronous setting. We close this gap between synchronous and asynchronous secure computation and show the first asynchronous protocol with\\({\\mathcal {O}}(nC)\\)communication complexity for a circuit withCmultiplication gates. Linear overhead forms a natural barrier for general secret-sharing-based MPC protocols. Our main technical contribution is an asynchronous weak binding secret sharing that achieves rate-1 communication (i.e.,\\({\\mathcal {O}}(1)\\)-overhead per secret). To achieve this goal, we develop new techniques for the asynchronous setting, including the use oftrivariate polynomials(as opposed to bivariate polynomials).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_10"}, {"primary_key": "582684", "vector": [], "sparse_vector": [], "title": "Constant-Round Simulation-Secure Coin Tossing Extension with Guaranteed Output.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Common randomness is an essential resource in many applications. However, <PERSON><PERSON><PERSON> (STOC 86) rules out the possibility of tossing a fair coin from scratch in the presence of a dishonest majority. A second-best alternative is aCoin Tossing Extension(CTE) protocol, which uses an “online” oracle that produces a few common random bits to generate many common random-looking bits. We initiate the systematic study offully-secureCTE, which guarantees output even in the presence of malicious behavior. A fully-secure two-party statistical CTE protocol with black-box simulation was implicit in <PERSON><PERSON><PERSON> et al. (Eurocrypt 06), but its round complexity is nearly linear in its output length. The problem of constant-round CTE with superlogarithmic stretch remained open. We prove thatstatisticalCTE with full black-box security and superlogarithmic stretch must have superconstant rounds. In thecomputationalsetting we prove that with\\(N\\ge 2\\)parties and polynomial stretch: One roundsuffices for CTE under subexponential LWE, even with Universally Composable security against adaptive corruptions. One-round CTE is implied by DDH or the hidden subgroup assumption in class groups, with a short, reusable Uniform Random String, and by DCR and QR, with a reusableStructuredReference String. One-way functions imply CTE withO(N) rounds, and thus constant-round CTE for any constant number of parties. Such results were not previously known even in the two-party setting with standalone, static security. We also extend one-round CTE to sample fromanyefficient distribution, via strong assumptions including IO. Our one-round CTE protocols can be interpreted asexplainablevariants of classical randomness extractors, wherein a (short) seed and a source instance can be efficiently reverse-sampled given a random output. Such explainable extractors may be of independent interest.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_5"}, {"primary_key": "582685", "vector": [], "sparse_vector": [], "title": "Succinct Homomorphic Secret Sharing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This work introduces homomorphic secret sharing (HSS) with succinct share size. In HSS, private inputs are shared between parties, who can then homomorphically evaluate a function on their shares, obtaining a share of the function output. In succinct HSS, a portion of the inputs can be distributed using shares whose size is sublinear in the number of such inputs. The parties can then locally evaluate a functionfon the shares, with the restriction thatfmust be linear in the succinctly shared inputs. We construct succinct, two-party HSS for branching programs, based on either the decisional composite residuosity assumption, a DDH-like assumption in class groups, or learning with errors with a superpolynomial modulus-to-noise ratio. We then give several applications of succinct HSS, which were only previously known using fully homomorphic encryption, or stronger tools: Succinct vector oblivious linear evaluation (VOLE):Two parties can obtain secret shares of a long, arbitrary vector\\(\\boldsymbol{x}\\), multiplied by a scalar\\(\\varDelta \\), with communication sublinear in the size of the vector. Batch, multi-party distributed point functions: A protocol for distributing a batch of secret, random point functions amongNparties, for any polynomialN, with communication sublinear in the number of DPFs. Sublinear MPC for any number of parties:Two new constructions of MPC with sublinear communication complexity, withNparties for any polynomialN: (1) For general layered Boolean circuits of sizes, with communication\\(O(N s/\\log \\log s)\\), and (2) For layered, sufficiently wide Boolean circuits, with communication\\(O(N s/\\log s)\\).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_11"}, {"primary_key": "582686", "vector": [], "sparse_vector": [], "title": "Improved Differential Meet-in-the-Middle Cryptanalysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dounia M&apos;foukh", "<PERSON><PERSON><PERSON>", "<PERSON>Plase<PERSON>"], "summary": "In this paper, we extend the applicability of differential meet-in-the-middle attacks, proposed at Crypto 2023, to truncated differentials, and in addition, we introduce three new ideas to improve this type of attack: we show how to add longer structures than the original paper, we show how to improve the key recovery steps by introducing some probability in them, and we combine this type of attacks with the state-test technique, that was introduced in the context of impossible differential attacks. Furthermore, we have developed a MILP-based tool to automate the search for a truncated differential-MITM attack with optimized overall complexity, incorporating some of the proposed improvements. Thanks to this, we can build the best known attacks on the cipherCRAFT, reaching 23 rounds against 21 previously; we provide a new attack on 23-roundSKINNY-64-192, and we improve the best attacks onSKINNY-128-384.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_10"}, {"primary_key": "582687", "vector": [], "sparse_vector": [], "title": "Post-quantum Security of Tweakable Even-Mansour, and Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The tweakable Even-Mansour construction yields a tweakable block cipher from a public random permutation. We prove post-quantum security of tweakable Even-Mansour when attackers havequantumaccess to the random permutation but onlyclassicalaccess to the secretly-keyed construction, the relevant setting for most real-world applications. We then use our results to prove post-quantum security—in the same model—of the symmetric-key schemes<PERSON><PERSON><PERSON>(an ISO-standardized MAC),<PERSON>(an AEAD finalist of NIST’s lightweight cryptography standardization effort), and a variant of<PERSON>inal<PERSON>(an AEAD second-round candidate of the CAESAR competition).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_11"}, {"primary_key": "582688", "vector": [], "sparse_vector": [], "title": "Crypto Dark Matter on the Torus - Oblivious PRFs from Shallow PRFs and TFHE.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Partially Oblivious Pseudorandom Functions (POPRFs) are 2-party protocols that allow a client to learn pseudorandom function (PRF) evaluations on inputs of its choice from a server. The client submits two inputs, one public and one private. The security properties ensure that the server cannot learn the private input, and the client cannot learn more than one evaluation per POPRF query. POPRFs have many applications including password-based key exchange and privacy-preserving authentication mechanisms. However, most constructions are based on classical assumptions, and those with post-quantum security suffer from large efficiency drawbacks. In this work, we construct a novel POPRF from lattice assumptions and the “Crypto Dark Matter” PRF candidate (TCC’18) in the random oracle model. At a conceptual level, our scheme exploits the alignment of this family of PRF candidates, relying on mixed modulus computations, and programmable bootstrapping in the torus fully homomorphic encryption scheme (TFHE). We show that our construction achieves malicious client security based on circuit-private FHE, and client privacy from the semantic security of the FHE scheme. We further explore a heuristic approach to extend our scheme to support verifiability, based on the difficulty of computing cheating circuits in low depth. This would yield a verifiable (P)OPRF. We provide a proof-of-concept implementation and preliminary benchmarks of our construction. For the core online OPRF functionality, we require amortised 10.0 KB communication per evaluation and a one-time per-client setup communication of 2.5 MB.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_16"}, {"primary_key": "582689", "vector": [], "sparse_vector": [], "title": "SLAP: Succinct Lattice-Based Polynomial Commitments from Standard Assumptions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent works on lattice-based extractable polynomial commitments can be grouped into two classes: (i) non-interactive constructions that stem from the functional commitment by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (CRYPTO 2022), and (ii) lattice adaptations of the Bulletproofs protocol (S &P 2018). The former class enjoys security in the standard model, albeit a knowledge assumption is desired. In contrast, Bulletproof-like protocols can be made secure under falsifiable assumptions, but due to technical limitations regarding subtractive sets, they only offer inverse-polynomial soundness error. This issue becomes particularly problematic when transforming these protocols to the non-interactive setting using the Fiat-Shamir paradigm. In this work, we propose the first lattice-based non-interactive extractable polynomial commitment scheme which achieves polylogarithmic proof size and verifier runtime (in the length of the committed message) under standard assumptions in the random oracle model. At the core of our work lies a new tree-based commitment scheme, along with an efficient proof of polynomial evaluation inspired by FRI (ICALP 2018). Natively, the interactive version of the construction is secure under a “multi-instance version” of the Power-Ring BASIS assumption (Eprint 2023/846). We then base security on the Module-SIS assumption by introducing several re-randomisation techniques which can be of independent interest.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_4"}, {"primary_key": "582690", "vector": [], "sparse_vector": [], "title": "Can <PERSON> and <PERSON> Output to <PERSON>?", "authors": ["Bar Alon", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In the setting of solitary output computations, only a single designated party learns the output of some function applied to the private inputs of all participating parties with the guarantee that nothing beyond the output is revealed. The setting of solitary output functionalities is a special case of secure multiparty computation, which allows a set of mutually distrusting parties to compute some function of their private inputs. The computation should guarantee some security properties, such as correctness, privacy, fairness, and output delivery. Full security captures all these properties together. Solitary output computation is a common setting that has become increasingly important, as it is relevant to many real-world scenarios, such as federated learning and set disjointness. In the set-disjointness problem, a set of parties with private datasets wish to convey to another party whether they have a common input. In this work, we investigate the limits of achieving set-disjointness which already has numerous applications and whose feasibility (under non-trivial conditions) was left open in the work of <PERSON><PERSON> et al. (TCC 2019). Towards resolving this, we completely characterize the set of Boolean functions that can be computed in the three-party setting in the face of a malicious adversary that corrupts up to two of the parties. As a corollary, we characterize the family of set-disjointness functions that can be computed in this setting, providing somewhat surprising results regarding this family and resolving the open question posed by <PERSON><PERSON> et al.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_2"}, {"primary_key": "582691", "vector": [], "sparse_vector": [], "title": "Updatable Public-Key Encryption, Revisited.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We revisit Updatable Public-Key Encryption (UPKE), which was introduced as a practical mechanism for building forward-secure cryptographic protocols. We begin by observing that all UPKE notions to date are neither syntactically flexible nor secure enough for the most important multi-party protocols motivating UPKE. We provide an intuitive taxonomy of UPKE properties – some partially or completely overlooked in the past – along with an overview of known (explicit and implicit) UPKE constructions. We then introduce a formal UPKE definition capturing all intuitive properties needed for multi-party protocols. Next, we provide a practical pairing-based construction for which we provide concrete bounds under a standard assumption in the random oracle and the algebraic group model. The efficiency profile of the scheme compares very favorably with existing UPKE constructions (despite the added flexibility and stronger security). For example, when used to improve the forward security of the Messaging Layer Security protocol [RFC9420], our new UPKE construction requires less than\\(1.5\\%\\)of the bandwidth of the next-most efficient UPKE construction satisfying the strongest UPKE notion considered so far.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_13"}, {"primary_key": "582692", "vector": [], "sparse_vector": [], "title": "Pseudorandom Isometries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce a new notion called\\({\\mathcal {Q}}\\)-secure pseudorandom isometries (PRI). A pseudorandom isometry is an efficient quantum circuit that maps ann-qubit state to an\\((n+m)\\)-qubit state in an isometric manner. In terms of security, we require that the output of aq-fold PRI on\\(\\rho \\), for\\( \\rho \\in {\\mathcal {Q}}\\), for any polynomialq, should be computationally indistinguishable from the output of aq-fold Haar isometry on\\(\\rho \\). By fine-tuning\\({\\mathcal {Q}}\\), we recover many existing notions of pseudorandomness. We present a construction of PRIs and assuming post-quantum one-way functions, we prove the security of\\({\\mathcal {Q}}\\)-secure pseudorandom isometries (PRI) for different interesting settings of\\({\\mathcal {Q}}\\). We also demonstrate many cryptographic applications of PRIs, including, length extension theorems for quantum pseudorandomness notions, message authentication schemes for quantum states, multi-copy secure public and private encryption schemes, and succinct quantum commitments.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_9"}, {"primary_key": "582693", "vector": [], "sparse_vector": [], "title": "Jolt: SNARKs for Virtual Machines via Lookups.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Succinct Non-interactive Arguments of Knowledge (SNARKs) allow an untrusted prover to establish that it correctly ran some “witness-checking procedure” on a witness. A zkVM (short for zero-knowledge virtual machine) is a SNARK that allows the witness-checking procedure to be specified as a computer program written in the assembly language of a specific instruction set architecture (ISA). Afront-endconverts computer programs into a lower-level representation such as an arithmetic circuit or generalization thereof. A SNARK for circuit-satisfiability can then be applied to the resulting circuit. We describe a new front-end technique calledJoltthat applies to a variety of ISAs.Jo<PERSON>rguably realizes a vision called thelookup singularity, which seeks to produce circuits that only perform lookups into pre-determined lookup tables. The circuits output byJoltprimarily perform lookups into a gigantic lookup table, of size more than\\(2^{128}\\), that depends only on the ISA. The validity of the lookups are proved via a newlookup argumentdescribed in a companion work called Lasso [STW23]. Although size-\\(2^{128}\\)tables are vastly too large to materialize in full, the tables arising inJoltare structured, avoiding costs that grow linearly with the table size. We describe performance and auditability benefits ofJoltcompared to prior zkVMs, focusing on the popular RISC-V ISA as a concrete example. The dominant cost for theJoltprover applied to this ISA (on 64-bit data types) is equivalent to cryptographically committing to under eleven 256-bit field elements per step of the RISC-V CPU. This compares favorably to prior zkVM provers, even those focused on far simpler VMs.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_1"}, {"primary_key": "582694", "vector": [], "sparse_vector": [], "title": "Perfect (Parallel) Broadcast in Constant Expected Rounds via Statistical VSS.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study broadcast protocols in the information-theoretic model under optimal conditions, where the number of corruptionstis at most one-third of the parties,n. While worst-case\\(\\varOmega (n)\\)round broadcast protocols are known to be impossible to achieve, protocols with an expected constant number of rounds have been demonstrated since the seminal work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [STOC’88]. Communication complexity for such protocols has gradually improved over the years, reachingO(nL) plus expected\\(O(n^4\\log n)\\)for broadcasting a message of sizeLbits. This paper presents a perfectly secure broadcast protocol with expected constant rounds and communication complexity ofO(nL) plus expected\\(O(n^3 \\log ^2n)\\)bits. In addition, we consider the problem of parallel broadcast, wherensenders, each wish to broadcast a message of sizeL. We show a parallel broadcast protocol with expected constant rounds and communication complexity of\\(O(n^2L)\\)plus expected\\(O(n^3 \\log ^2n)\\)bits. Our protocol is optimal (up to expectation) for messages of length\\(L \\in \\varOmega (n \\log ^2 n)\\). Our main contribution is a framework for obtainingperfectlysecure broadcast with an expected constant number of rounds from astatisticallysecure verifiable secret sharing. Moreover, we provide a new statistically secure verifiable secret sharing where the broadcast cost per participant is reduced from\\(O(n \\log n)\\)bits to only\\(O(\\textsf{poly}\\log n)\\)bits. All our protocols are adaptively secure.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_11"}, {"primary_key": "582695", "vector": [], "sparse_vector": [], "title": "Trapdoor Memory-Hard Functions.", "authors": ["Benedik<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Memory-hard functions (MHF) are functions whose evaluation provably requires a lot of memory. While MHFs are an unkeyed primitive, it is natural to consider the notion oftrapdoorMHFs (TMHFs). A TMHF is like an MHF, but when sampling the public parameters one also samples a trapdoor which allows evaluating the function muchcheaper. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (Asiacrypt’17) were the first to consider TMHFs and put forth a candidate TMHF construction called\\(\\textsc {Diodon}\\)that is based on the\\(\\textsc {Scrypt}\\)MHF (<PERSON><PERSON><PERSON>, BSDCan’09). To allow for a trapdoor,\\(\\textsc {Scrypt}\\)’s initial hash chain is replaced by a sequence of squares in a group of unknown order where the order of the group is the trapdoor. For a lengthnsequence of squares and a group of orderN,\\(\\textsc {Diodon}\\)’s cumulative memory complexity (CMC) is\\(O(n^2\\log N)\\)without the trapdoor and\\(O(n \\log (n) \\log (N)^2)\\)with knowledge of it. While\\(\\textsc {Scrypt}\\)is proven to be optimally memory-hard in the random oracle model (<PERSON><PERSON> et al., Eurocrypt’17),\\(\\textsc {Diodon}\\)’s memory-hardness has not been proven so far. In this work, we fill this gap by rigorously analyzing a specific instantiation of\\(\\textsc {Diodon}\\). We show that its CMC is lower bounded by\\(\\varOmega (\\frac{n^2}{\\log n} \\log N)\\)which almost matches the upper bound. Our proof is based Alwen et al.’s lower bound on\\(\\textsc {Scrypt}\\)’s CMC but requires non-trivial modifications due to the algebraic structure of\\(\\textsc {Diodon}\\). Most importantly, our analysis involves a more elaborate compression argument and a solvability criterion for certain systems of Diophantine equations.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_11"}, {"primary_key": "582696", "vector": [], "sparse_vector": [], "title": "Fuzzy Private Set Intersection with Large Hyperballs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Pu"], "summary": "Traditional private set intersection (PSI) involves a receiver and a sender holding setsXandY, respectively, with the receiver learning only the intersection\\(X\\cap Y\\). We turn our attention to its fuzzy variant, where the receiver holds\\(|X|\\)hyperballs of radius\\(\\delta \\)in a metric space and the sender has |Y| points. Representing the hyperballs by their center, the receiver learns the points\\(x\\in X\\)for which there exists\\(y\\in Y\\)such that\\(\\textsf{dist}(x,y)\\le \\delta \\)with respect to some distance metric. Previous approaches either require general-purpose multi-party computation (MPC) techniques like garbled circuits or fully homomorphic encryption (FHE), leak details about the sender’s precise inputs, support limited distance metrics, or scale poorly with the hyperballs’ volume. This work presents the first black-box construction for fuzzy PSI (including other variants such as PSI cardinality, labeled PSI, and circuit PSI), which can handle polynomially large radius and dimension (i.e., a potentially exponentially large volume) in two interaction messages, supporting general\\(L_{\\textsf{p}\\in [1,\\infty ]}\\)distance, without relying on garbled circuits or FHE. The protocol excels in both asymptotic and concrete efficiency compared to existing works. For security, we solely rely on the assumption that the Decisional Di<PERSON>ie-Hellman (DDH) holds in the random oracle model.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_12"}, {"primary_key": "582697", "vector": [], "sparse_vector": [], "title": "Twinkle: Threshold Signatures from DDH with Full Adaptive Security.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> is the first threshold signature scheme in the pairing-free discrete logarithm setting (<PERSON><PERSON>, <PERSON>, <PERSON>, Crypto 2023) to be proven secure under adaptive corruptions. However, without using the algebraic group model, <PERSON><PERSON><PERSON>’s proof imposes an undesirable restriction on the adversary. Namely, for a signing threshold\\(t<n\\), the adversary is restricted to corrupt at mostt/2 parties. In addition, <PERSON><PERSON><PERSON>’s proof relies on a strong one-more assumption. In this work, we propose <PERSON>kle, a new threshold signature scheme in the pairing-free setting which overcomes these limitations. <PERSON><PERSON> is the first pairing-free scheme to have a security proof under up totadaptive corruptions without relying on the algebraic group model. It is also the first such scheme with a security proof under adaptive corruptions from a well-studied non-interactive assumption, namely, the Decisional Diffie-Hellman (DDH) assumption. We achieve our result in two steps. First, we design a generic scheme based on a linear function that satisfies several abstract properties and prove its adaptive security under a suitable one-more assumption related to this function. In the context of this proof, we also identify a gap in the security proof of <PERSON><PERSON><PERSON> and develop new techniques to overcome this issue. Second, we give a suitable instantiation of the function for which the corresponding one-more assumption follows from DDH.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_15"}, {"primary_key": "582698", "vector": [], "sparse_vector": [], "title": "Bootstrapping Bits with CKKS.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Cheon<PERSON><PERSON> (CKKS) fully homomorphic encryption scheme is designed to efficiently perform computations on real numbers in an encrypted state. Recently, <PERSON><PERSON><PERSON><PERSON> al[J. Cryptol.] proposed an efficient strategy to use CKKS in a black-box manner to perform computations on binary data. In this work, we introduce several CKKS bootstrapping algorithms designed specifically for ciphertexts encoding binary data. Crucially, the new CKKS bootstrapping algorithms enable to bootstrap ciphertexts containing the binary data in the most significant bits. First, this allows to decrease the moduli used in bootstrapping, saving a larger share of the modulus budget for non-bootstrapping operations. In particular, we obtain full-slot bootstrapping in ring degree\\(2^{14}\\)for the first time. Second, the ciphertext format is compatible with the one used in the DM/CGGI fully homomorphic encryption schemes. Interestingly, we may combine our CKKS bootstrapping algorithms for bits with the fast ring packing technique from <PERSON><PERSON><PERSON> al[CRYPTO’23]. This leads to a new bootstrapping algorithm for DM/CGGI that outperforms the state-of-the-art approaches when the number of bootstraps to be performed simultaneously is in the low hundreds.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_4"}, {"primary_key": "582699", "vector": [], "sparse_vector": [], "title": "Non-malleable Codes with Optimal Rate for Poly-Size Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give an explicit construction of non-malleable codes with rate\\(1-o(1)\\)for the tampering class of poly-size circuits. This rate is optimal, and improves upon the previous explicit construction of <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and Loss [9] which achieves a rate smaller than\\(\\frac{1}{n}\\). Our codes are based on the same hardness assumption used by <PERSON>, <PERSON><PERSON><PERSON>-<PERSON>ed and Loss, namely, that there exists a problem in\\(\\text {E}=\\text {DTIME}(2^{O(n)})\\)that requires nondeterministic circuits of size\\(2^{\\varOmega (n)}\\). This is a standard complexity theoretic assumption that was used in many papers in complexity theory and cryptography, and can be viewed as a scaled, nonuniform version of the widely believed assumption that\\(\\text {EXP} \\not \\subseteq \\text {NP}\\). Our result is incomparable to that of <PERSON>, <PERSON><PERSON><PERSON>-<PERSON><PERSON> and Loss, as we only achieve computational (rather than statistical) security. Non-malleable codes with Computational security (with lower error than what we get) were obtained by [12,26] under strong cryptographic assumptions. We show that our approach can potentially yield statistical security if certain explicit constructions of pseudorandom objects can be improved. By composing our new non-malleable codes with standard (information theoretic) error-correcting codes (that recover from apfraction of errors) we achieve thebest of both worlds. Namely, we achieve explicit codes that recover from ap-fraction of errors and have the same rate as the best known explicit information theoretic codes, whilealsobeing non-malleable for poly-size circuits. Moreover, if we restrict our attention to errors that are introduced by poly-size circuits, we can achieve best of both worlds codes with rate\\(1-H(p)\\). This is superior to the rate achieved by standard (information theoretic) error-correcting codes, and this result is obtained by composing our new non-malleable codes with the recent codes of Shaltiel and Silbak [55]. Our technique combines ideas from non-malleable codes and pseudorandomness. We show how to take a low rate “small set non-malleable code (this is a variant of non-malleable codes with a different notion of security that was introduced by Shaltiel and Silbak [54]) and compile it into a (standard) high-rate non-malleable code. Using small set non-malleable codes (as well as seed-extending PRGs) bypasses difficulties that arise when analysing standard non-malleable codes, and allows us to use a simple construction.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_2"}, {"primary_key": "582700", "vector": [], "sparse_vector": [], "title": "Anamorphic Encryption, Revisited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "An anamorphic encryption scheme allows two parties who share a so-calleddouble keyto embed covert messages in ciphertexts of an established PKE scheme. This protects against a dictator that can force the receiver to reveal the secret keys for the PKE scheme, but who is oblivious about the existence of the double key. We identify two limitations of the original model by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (EUROCRYPT 2022). First, in their definition a double key can only be generated once,togetherwith a key-pair. This has the drawback that a receiver who wants to use the anamorphic modeaftera dictator comes to power, needs to deploy a new key-pair, a potentially suspicious act. Second, a receiver cannot distinguish whether or not a ciphertext contains a covert message. In this work we propose a new model that overcomes these limitations. First, we allow to associatemultipledouble keys to a key-pair,afterits deployment. This also enablesdeniabilityin case the double key only depends on the public key. Second, we propose a natural robustness notion, which guarantees that anamorphically decrypting a regularly encrypted message results in a special symbol indicating that no covert message is contained, which also eliminates certain attacks. Finally, to instantiate our new, stronger definition of anamorphic encryption, we provide generic and concrete constructions. Concretely, we show that <PERSON>G<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> satisfy a new condition,selective randomness recoverability, which enables robust anamorphic extensions, and we also provide a robust anamorphic extension for RSA-OAEP.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_1"}, {"primary_key": "582701", "vector": [], "sparse_vector": [], "title": "Software with Certified Deletion.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Is it possible to prove the deletion of a computer program after having executed it? While this task is clearly impossible using classical information alone, the laws of quantum mechanics may admit a solution to this problem. In this work, we propose a new approach to answer this question, using quantum information. In theinteractivesettings, we present the first fully-secure solution for blind delegation with certified deletion, assuming post-quantum hardness of the learning with errors (LWE) problem. In thenon-interactivesettings, we propose a construction of obfuscation with certified deletion, assuming post-quantum iO and one-way functions. Our main technical contribution is a new deletion theorem for subspace coset states [<PERSON><PERSON><PERSON> and <PERSON>, EUROCRYPT’21, <PERSON><PERSON><PERSON><PERSON> et al., CRYPTO’21], which enables a generic compiler that adds the certified deletion guarantee to a variety of cryptographic primitives. In addition to our main result, this allows us to obtain a host of new primitives, such as functional encryption with certified deletion and secure software leasing for an interesting class of programs. In fact, we are able for the first time to achieve astronger notion ofsecure software leasing, where even adishoneste<PERSON>uator cannot evaluate the program after returning it.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_4"}, {"primary_key": "582702", "vector": [], "sparse_vector": [], "title": "Probabilistically Checkable Arguments for All NP.", "authors": ["<PERSON><PERSON>"], "summary": "A probabilistically checkable argument (\\(\\textsf{PCA}\\)) is a computational relaxation of\\(\\textsf{PCP}\\)s, where soundness is guaranteed to hold only for false proofs generated by a computationally bounded adversary. The advantage of\\(\\textsf{PCA}\\)s is that they are able to overcome the limitations of\\(\\textsf{PCP}\\)s. Asuccinct\\(\\textsf{PCA}\\)has a proof length that is polynomial in the witness length (and is independent of the non-deterministic verification time), which is impossible for\\(\\textsf{PCP}\\)s, under standard complexity assumptions. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (ITCS 2022) constructed succinct\\(\\textsf{PCA}\\)s for\\(\\textsf{NC}\\)that are publicly-verifiable and have constant query complexity under the sub-exponential hardness of\\(\\textsf{LWE}\\). We construct a publicly-verifiable succinct\\(\\textsf{PCA}\\)with constant query complexity for all\\(\\textsf{NP}\\)in the adaptive security setting. Our\\(\\textsf{PCA}\\)scheme offers several improvements compared to the <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> construction: (1) it applies to all problems in\\(\\textsf{NP}\\), (2) it achieves adaptive security, and (3) it can be realized under any of the following assumptions: thepolynomialhardness of\\(\\textsf{LWE}\\);O(1)-\\(\\textsf{LIN}\\); or sub-exponential\\(\\textsf{DDH}\\). Moreover, our\\(\\textsf{PCA}\\)scheme has asuccinct prover, which means that for any\\(\\textsf{NP}\\)relation that can be verified in timeTand spaceS, the proof can be generated in time\\(O_{\\lambda ,m}(T\\cdot \\textrm{polylog}(T))\\)and space\\(O_{\\lambda ,m}(S\\cdot \\textrm{polylog}(T))\\). Here,\\({O}_{\\lambda ,m}\\)accounts for polynomial factors in the security parameter and in the size of the witness. En route, we construct a newcomplexity-preserving\\(\\mathsf {RAM~Delegation}\\)scheme that is used in our\\(\\textsf{PCA}\\)construction and may be of independent interest.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_12"}, {"primary_key": "582703", "vector": [], "sparse_vector": [], "title": "SPRINT: High-Throughput Robust Distributed Schnorr Signatures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We describe robust high-throughput threshold protocols for generating Schnorr signatures in an asynchronous setting with potentially hundreds of parties. The protocols run a single message-independent interactive ephemeral randomness generation procedure (i.e., DKG) followed bynon-interactivesignature generation for multiple messages, at a communication cost similar to one execution of a synchronous non-robust protocol in prior work (e.g., <PERSON><PERSON><PERSON> et al.) and with a large number of parties (ranging from few tens to hundreds and more). Our protocols extend seamlessly to the dynamic/proactive setting where each run of the protocol uses a new committee with refreshed shares of the secret key; in particular, they support large committees periodically sampled from among the overall population of parties and the required secret state is transferred to the selected parties. The protocols work over a broadcast channel and are robust (provide guaranteed output delivery) even over asynchronous networks. The combination of these features makes our protocols a good match for implementing a signature service over a public blockchain with many validators, where guaranteed output delivery is an absolute must. In that setting, there is a system-wide public key, where the corresponding secret signature key is distributed among the validators. Clients can submit messages (under suitable controls, e.g., smart contracts), and authorized messages are signed relative to the global public key. Asymptotically, when running with committees ofnparties, our protocols can generate\\(\\varOmega (n^2)\\)signatures per run, while providing resilience against\\(\\varOmega (n)\\)corrupted nodes and broadcasting only\\(O(n^2)\\)group elements and scalars (henceO(1) elements per signature). We prove the security of our protocols via a reduction to the hardness of the discrete logarithm problem in the random oracle model.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_3"}, {"primary_key": "582704", "vector": [], "sparse_vector": [], "title": "Pauli Manipulation Detection Codes and Applications to Quantum Communication over Adversarial Channels.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We introduce and explicitly construct a quantum error-detection code we coin a “Pauli Manipulation Detection” code (or PMD), which detects every Pauli error with high probability. We apply them to construct the first near-optimal codes for two tasks in quantum communication over adversarial channels. Our main application is an approximate quantum code over qubits which can efficiently correct from a number of (worst-case) erasure errors approaching the quantum Singleton bound. Our construction is based on the composition of a PMD code with a stabilizer code which is list-decodable from erasures, a variant of the stabilizer list-decodable codes studied by <PERSON><PERSON> and <PERSON> [49], and <PERSON><PERSON><PERSON><PERSON> et al. [17]. Our second application is a quantum authentication code for “qubit-wise” channels, which does not require a secret key. Remarkably, this gives an example of a task in quantum communication which is provably impossible classically. Our construction is based on a combination of PMD codes, stabilizer codes, and classical non-malleable codes (<PERSON><PERSON><PERSON><PERSON> et al. [33]), and achieves “minimal redundancy” (rate\\(1-o(1)\\)).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_14"}, {"primary_key": "582705", "vector": [], "sparse_vector": [], "title": "A Generic Algorithm for Efficient Key Recovery in Differential Attacks - and its Associated Tool.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>Plase<PERSON>"], "summary": "Differential cryptanalysis is an old and powerful attack against block ciphers. While different techniques have been introduced throughout the years to improve the complexity of this attack, the key recovery phase remains a tedious and error-prone procedure. In this work, we propose a new algorithm and its associated tool that permits, given a distinguisher, to output an efficient key guessing strategy. Our tool can be applied to SPN ciphers whose linear layer consists of a bit-permutation and whose key schedule is linear or almost linear. It can be used not only to help cryptanalysts find the best differential attack on a given cipher but also to assist designers in their security analysis. We applied our tool to four targets:RECTANGLE,PRESENT-80,SPEEDY-7-192andGIFT-64. We extend the previous best attack onRECTANGLE-128by one round and the previous best differential attack againstPRESENT-80by 2 rounds. We improve a previous key recovery step in an attack againstSPEEDYand present more efficient key recovery strategies forRECTANGLE-80andGIFT. Our tool outputs the results in only a second for most targets.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_8"}, {"primary_key": "582706", "vector": [], "sparse_vector": [], "title": "Two-Round Maliciously-Secure Oblivious Transfer with Optimal Rate.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We give a construction of a two-round batch oblivious transfer (OT) protocol in the CRS model that is UC-secure against malicious adversaries and has (near) optimal communication cost. Specifically, to perform a batch ofkoblivious transfers where the sender’s inputs are bits, the sender and the receiver need to communicate a total of\\(3k + o(k) \\cdot \\textsf{poly}(\\lambda )\\)bits. We argue that 3kbits are required by any protocol with a black-box and straight-line simulator. The security of our construction is proven assuming the hardness of Quadratic Residuosity (QR) and the Learning Parity with Noise (LPN).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_10"}, {"primary_key": "582707", "vector": [], "sparse_vector": [], "title": "From Random Probing to Noisy Leakages Without Field-Size Dependence.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Side channel attacks are devastating attacks targeting cryptographic implementations. To protect against these attacks, various countermeasures have been proposed – in particular, the so-called masking scheme. Masking schemes work by hiding sensitive information via secret sharing all intermediate values that occur during the evaluation of a cryptographic implementation. Over the last decade, there has been broad interest in designing and formally analyzing such schemes. The random probing model considers leakage where the value on each wire leaks with some probability\\(\\epsilon \\). This model is important as it implies security in the noisy leakage model via a reduction by <PERSON><PERSON> et al. (Eurocrypt 2014). Noisy leakages are considered the “gold-standard” for analyzing masking schemes as they accurately model many real-world physical leakages. Unfortunately, the reduction of <PERSON><PERSON> et al. is non-tight, and in particular requires that the amount of noise increases by a factor of\\(|\\mathbb {F} |\\)for circuits that operate over\\(\\mathbb {F} \\)(where\\(\\mathbb {F} \\)is a finite field). In this work, we give a generic transformation from\\(\\varepsilon \\)-random probing to\\(\\delta \\)-average probing, with\\(\\delta \\approx \\varepsilon ^2\\), which avoids this loss of\\(|\\mathbb {F} |\\). Since the average probing is identical to the noisy leakage model (Eurocrypt 2014), this yields for the first time a security analysis of masked circuits where the noise parameter in the noisy leakage model is independent of\\(|\\mathbb {F} |\\). The latter is particularly important for cryptographic schemes operating over large fields, e.g., the AES or the recently standardized post-quantum schemes.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_13"}, {"primary_key": "582708", "vector": [], "sparse_vector": [], "title": "Practical Attack on All Parameters of the DME Signature Scheme.", "authors": ["<PERSON>", "Maxime Bros", "<PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "DME is a multivariate scheme submitted to the call for additional signatures recently launched by NIST. Its performance is one of the best among all the candidates. The public key is constructed from the alternation of very structured linear and non-linear components that constitute the private key, the latter being defined over an extension field. We exploit these structures by proposing an algebraic attack which is practical on all DME parameters.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_1"}, {"primary_key": "582709", "vector": [], "sparse_vector": [], "title": "Monotone-Policy Aggregate Signatures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The notion of aggregate signatures allows for combining signatures from different parties into a short certificate that attests thatallparties signed a message. In this work, we lift this notion to capture different, more expressive signing policies. For example, we can certify that a message was signed by a (weighted) threshold of signers. We present the first constructions of aggregate signatures for monotone policies based on standard polynomial-time cryptographic assumptions. The aggregate signatures in our schemes are succinct, i.e., their size isindependentof the number of signers. Moreover, verification is also succinct if all parties sign the same message (or if the messages have a succinct representation). All prior work requires either interaction between the parties or non-standard assumptions (that imply SNARKs for NP). Our signature schemes are based on non-interactive batch arguments (BARGs) for monotone policies [Brak<PERSON><PERSON>-<PERSON>-<PERSON>-<PERSON>-<PERSON>, Crypto’23]. In contrast to previous constructions, our BARGs satisfy a new notion ofadaptivesecurity which is instrumental to our application. Our new BARGs for monotone policies can be constructed from standard BARGs and other standard assumptions.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_7"}, {"primary_key": "582710", "vector": [], "sparse_vector": [], "title": "Fast Public-Key Silent OT and More from Constrained Naor-Reingold.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Pseudorandom Correlation Functions (PCFs) allow two parties, given correlated evaluation keys, to locally generate arbitrarily many pseudorandom correlated strings, e.g. Oblivious Transfer (OT) correlations, which can then be used by the two parties to jointly run secure computation protocols. In this work, we provide a novel and simple approach for constructing PCFs for OT correlation, by relying on constrained pseudorandom functions for a class of constraints containing a weak pseudorandom function (wPRF). We then show that tweaking the Naor-Reingold pseudorandom function and relying on low-complexity pseudorandom functions allow us to instantiate our paradigm. We further extend our ideas to obtain efficientpublic-keyPCFs, which allow the distribution of correlated keys between parties to be non-interactive: each party can generate a pair of public/secret keys, and any pair of parties can locally derive their correlated evaluation key by combining their secret key with the other party’s public key. In addition to these theoretical contributions, we detail various optimizations and provide concrete instantiations of our paradigm relying on the Boneh-Ishai-Passelègue-Sahai-Wu wPRF and the Goldreich-Applebaum-Raykov wPRF. Putting everything together, we obtain public-key PCFs with a throughput of 15k–40k OT/s, which is of a similar order of magnitude to the state-of-the-artinteractivePCFs and about 4 orders of magnitude faster than state-of-the artpublic-keyPCFs. As a side result, we also show that public-key PCFs can serve as a building block to construct reusable designated-verifier non-interactive zero-knowledge proofs (DV-NIZK) for NP. Combined with our instantiations, this yields simple and efficient reusable DV-NIZKs for NP in pairing-free groups.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_4"}, {"primary_key": "582711", "vector": [], "sparse_vector": [], "title": "Polynomial Time Cryptanalytic Extraction of Neural Network Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Billions of dollars and countless GPU hours are currently spent on training Deep Neural Networks (DNNs) for a variety of tasks. Thus, it is essential to determine the difficulty of extracting all the parameters of such neural networks when given access to their black-box implementations. Many versions of this problem have been studied over the last 30 years, and the best current attack on ReLU-based deep neural networks was presented at Crypto’20 by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. It resembles a differential chosen plaintext attack on a cryptosystem, which has a secret key embedded in its black-box implementation and requires a polynomial number of queries but an exponential amount of time (as a function of the number of neurons). In this paper, we improve this attack by developing several new techniques that enable us to extract with arbitrarily high precision all the real-valued parameters of a ReLU-based DNN using a polynomial number of queriesanda polynomial amount of time. We demonstrate its practical efficiency by applying it to a full-sized neural network for classifying the CIFAR10 dataset, which has 3072 inputs, 8 hidden layers with 256 neurons each, and about 1.2 million neuronal parameters. An attack following the approach by <PERSON><PERSON> et al. requires an exhaustive search over\\(2^{256}\\)possibilities. Our attack replaces this with our new techniques, which require only 30 min on a 256-core computer.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_1"}, {"primary_key": "582712", "vector": [], "sparse_vector": [], "title": "Reduction from Sparse LPN to LPN, Dual Attack 3.0.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The security of code-based cryptography relies primarily on the hardness of decoding generic linear codes. Until very recently, all the best algorithms for solving the decoding problem were information set decoders (\\(\\textsf{ISD}\\)). However, recently a new algorithm called\\(\\textsf{RLPN}\\)-decoding which relies on a completely different approach was introduced and it has been shown that\\(\\textsf{RLPN}\\)outperforms significantly\\(\\textsf{ISD}\\)decoders for a rather large range of rates. This\\(\\textsf{RLPN}\\)decoder relies on two ingredients, first reducing decoding to some underlying\\(\\textsf{LPN}\\)problem, and then computing efficiently many parity-checks of small weight when restricted to some positions. We revisit\\(\\textsf{RLPN}\\)-decoding by noticing that, in this algorithm, decoding is in fact reduced to a sparse-\\(\\textsf{LPN}\\)problem, namely with a secret whose Hamming weight is small. Our new approach consists this time in making an additional reduction from sparse-\\(\\textsf{LPN}\\)to plain-\\(\\textsf{LPN}\\)with a coding approach inspired by\\(\\textsf{coded}\\)-\\(\\textsf{BKW}\\). It outperforms significantly the\\(\\textsf{ISD}\\)’s and\\(\\textsf{RLPN}\\)for code rates smaller than\\(0.42\\). This algorithm can be viewed as the code-based cryptography cousin of recent dual attacks in lattice-based cryptography. We depart completely from the traditional analysis of this kind of algorithm which uses a certain number of independence assumptions that have been strongly questioned recently in the latter domain. We give instead a formula for the\\(\\textsf{LPN}\\)noise relying on duality which allows to analyze the behavior of the algorithm by relying only on the analysis of a certain weight distribution. By using only a minimal assumption whose validity has been verified experimentally we are able to justify the correctness of our algorithm. This key tool, namely the duality formula, can be readily adapted to the lattice setting and is shown to give a simple explanation for some phenomena observed on dual attacks in lattices in [19].", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_11"}, {"primary_key": "582713", "vector": [], "sparse_vector": [], "title": "Publicly Verifiable Secret Sharing Over Class Groups and Applications to DKG and YOSO.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Publicly Verifiable Secret Sharing (PVSS) allows a dealer to publish encrypted shares of a secret so that parties holding the corresponding decryption keys may later reconstruct it. Both dealing and reconstruction are non-interactive and any verifier can check their validity. PVSS finds applications in randomness beacons, distributed key generation (DKG) and in YOSO MPC (Gentryet al.CRYPTO’21), when endowed with suitable publicly verifiable re-sharing as in YOLO YOSO (Cascudoet al.ASIACRYPT’22). We introduce a PVSS scheme over class groups that achieves similar efficiency to state-of-the art schemes that only allow for reconstructinga functionof the secret, while our scheme allows the reconstruction of the original secret. Our construction generalizes the DDH-based scheme of YOLO YOSO to operate over class groups, which poses technical challenges in adapting the necessary NIZKs in face of the unknown group order and the fact that efficient NIZKs of knowledge are not as simple to construct in this setting. Building on our PVSS scheme’s ability to recover the original secret, we propose two DKG protocols for discrete logarithm key pairs: a biasable 1-round protocol, which improves on the concrete communication/computational complexities of previous works; and a 2-round unbiasable protocol, which improves on the round complexity of previous works. We also add publicly verifiable resharing towards anonymous committees to our PVSS, so that it can be used to efficiently transfer state among committees in the YOSO setting. Together with a recent construction of MPC in the YOSO model based on class groups (Braunet al.CRYPTO’23), this results in the most efficient full realization (i.e.without assuming receiver anonymous channels) of YOSO MPC based on the CDN framework with transparent setup.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_8"}, {"primary_key": "582714", "vector": [], "sparse_vector": [], "title": "Anamorphic Encryption: New Constructions and Homomorphic Realizations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The elegant paradigm of Anamorphic Encryption (<PERSON><PERSON><PERSON> al., Eurocrypt 2022) considers the question of establishing a private communication in a world controlled by a dictator. The challenge is to allow two users, sharing some secret anamorphic key, to exchange covert messages without the dictator noticing, even when the latter has full access to the regular secret keys. Over the last year several works considered this question and proposed constructions, novel extensions and strengthened definitions. In this work we make progress on the study of this primitive in three main directions. First, we show that two general and well established encryption paradigms, namely hybrid encryption and the IBE-to-CCA transform, admit very simple and natural anamorphic extensions. Next, we show that anamorphism, far from being a phenomenon isolated to “basic” encryption schemes, extends also to homomorphic encryption. We show that some existing homomorphic schemes, (and most notably the fully homomorphic one by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>) can be made anamorphic, while retaining their homomorphic properties both with respect to the regular and the covert message. Finally we refine the notion of anamorphic encryption by envisioning the possibility of splitting the anamorphic key into an encryption component (that only allows to encrypt covert messages) and a decryption component. This makes possible for a receiver to set up several, independent, covert channels associated with a single covert key.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_2"}, {"primary_key": "582715", "vector": [], "sparse_vector": [], "title": "Approximate Lower Bound Arguments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Suppose a prover, in possession of a large body of valuable evidence, wants to quickly convince a verifier by presenting only a small portion of the evidence. We define an Approximate Lower Bound Argument, or ALBA, which allows the prover to do just that: to succinctly prove knowledge of a large number of elements satisfying a predicate (or, more generally, elements of a sufficient total weight when a predicate is generalized to a weight function). The argument is approximate because there is a small gap between what the prover actually knows and what the verifier is convinced the prover knows. This gap enables very efficient schemes. We present noninteractive constructions of ALBA in the random oracle and Uniform Random String models and show that our proof sizes are nearly optimal. We also show how our constructions can be made particularly communication-efficient when the evidence is distributed among multiple provers working together, which is of practical importance when ALBA is applied to a decentralized setting. We demonstrate two very different applications of ALBAs: for large-scale decentralized signatures and for achieving universal composability in general-purpose succinct proof systems (SNARKs).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_3"}, {"primary_key": "582716", "vector": [], "sparse_vector": [], "title": "Integrating Causality in Messaging Channels.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Causal reasoning plays an important role in the comprehension of communication, but it has been elusive so far how causality should be properly preserved by instant messaging services. To the best of our knowledge, causality preservation is not even treated as a desired security property by most (if not all) existing secure messaging protocols like Signal. This is probably due to the intuition that causality seems already preserved when all received messages are intact and displayed according to their sending order. Our starting point is to notice that this intuition is wrong. Until now, for messaging channels (where conversations take place), both the proper causality model and the provably secure constructions have been left open. Our work fills this gap, with the goal to facilitate the formal understanding of causality preservation in messaging. First, we focus on the common two-user secure messaging channels and model the desired causality preservation property. We take the popular Signal protocol as an example and analyze the causality security of its cryptographic core (the double-ratchet mechanism). We show its inadequacy with a simple causality attack, then fix it such that the resulting Signal channel is causality-preserving, even in a strong sense that guarantees post-compromise security. Our fix is actuallygeneric: it can be applied to any bidirectional channel to gain strong causality security. Then, we model causality security for the so-called message franking channels. Such a channel additionally enables end users to report individual abusive messages to a server (e.g., the service provider), where this server relays the end-to-end-encrypted communication between users. Causality security in this setting further allows the server to retrieve the necessary causal dependencies of each reported message, essentially extending isolated reported messages to message flows. This has great security merit for dispute resolution, because a benign message may be deemed abusive when isolated from the context. As an example, we apply our model to analyze Facebook’s message franking scheme. We show that a malicious user can easily trick Facebook (i.e., the server) to accuse an innocent user. Then we fix this issue by amending the underlying message franking channel to preserve the desired causality.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_9"}, {"primary_key": "582717", "vector": [], "sparse_vector": [], "title": "Diving Deep into the Preimage Security of AES-Like Hashing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Since the seminal works by <PERSON><PERSON> and <PERSON><PERSON>, Meet-in-the-Middle (MITM) attacks are recognized as an effective technique for preimage and collision attacks on hash functions. At Eurocrypt 2021, <PERSON><PERSON><PERSON> al. automated MITM attacks onAES-like hashing and improved upon the best manual result. The attack framework has been furnished by subsequent works, yet far from complete. This paper introduces three key contributions dedicated to further generalizing the idea of MITM and refining the automatic model onAES-like hashing. (1) We introduceS-box linearizationto MITM pseudo-preimage attacks onAES-like hashing. The technique works well with superposition states to preserve information after S-boxes at affordable cost. (2) We proposedistributed initial structures, an extension on the original concept of initial states, that selects initial degrees of freedom in a more versatile manner to enlarge the search space. (3) We exploit thestructural similaritiesbetween encryption and key schedule in constructions (e.g.,WhirlpoolandStreebog) to model propagations more accurately and avoid repeated costs. Weaponed with these innovative techniques, we further empower the MITM framework and improve the attack results onAES-like designs for preimage and collision. We obtain the first preimage attacks on 10-roundAES-192, 10-roundRijndael-192/256, and 7.75-roundWhirlpool, reduced time and/or memory complexities for preimage attacks on 5-, 6-roundWhirlpooland 7.5-, 8.5-roundStreebog, as well as improved collision attacks on 6- and 6.5-roundWhirlpool.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_14"}, {"primary_key": "582718", "vector": [], "sparse_vector": [], "title": "Best-of-Both-Worlds Multiparty Quantum Computation with Publicly Verifiable Identifiable Abort.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Mir<PERSON>) <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> et al. (CRYPTO 2021) introduced a multiparty quantum computation protocol that is secure with identifiable abort (MPQC-SWIA). However, their protocol allows only inside MPQC parties to know the identity of malicious players. This becomes problematic when two groups of people disagree and need a third party, like a jury, to verify who the malicious party is. This issue takes on heightened significance in the quantum setting, given that quantum states may exist in only a single copy. Thus, we emphasize the necessity of a protocol withpublicly verifiable identifiable abort(PVIA), enabling outside observers with only classical computational power to agree on the identity of the malicious party in case of an abort. However, achieving MPQC with PVIA poses significant challenges due to the no-cloning theorem, and previous works proposed by <PERSON><PERSON><PERSON> (STOC 2018) and <PERSON> et al. (Eurocrypt 2022) for classical verification of quantum computation fall short. In this paper, we obtain the first MPQC-PVIA protocol assuming post-quantum oblivious transfer and a classical broadcast channel. The core component of our construction is a new authentication primitive calledauditable quantum authentication(AQA) that identifies the malicious sender with overwhelming probability. Additionally, we provide the first MPQC protocol with best-of-both-worlds (BoBW) security, which guarantees output delivery with an honest majority and remains secure with abort even if the majority is dishonest. Our best-of-both-worlds MPQC protocol also satisfies PVIA upon abort.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_5"}, {"primary_key": "582719", "vector": [], "sparse_vector": [], "title": "A Holistic Security Analysis of Monero Transactions.", "authors": ["Cas Cremers", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Monero is a popular cryptocurrency with strong privacy guarantees for users’ transactions. At the heart of <PERSON><PERSON>’s privacy claims lies a complex transaction system called RingCT, which combines several building blocks such as linkable ring signatures, homomorphic commitments, and range proofs, in a unique fashion. In this work, we provide the first rigorous security analysis for RingCT (as given in Zero to Monero, v2.0.0, 2020) in its entirety. This is in contrast to prior works that only provided security arguments for parts of RingCT. To analyze <PERSON><PERSON>’s transaction system, we introduce the first holistic security model for RingCT. We then prove the security of RingCT in our model. Our framework is modular: it allows to view RingCT as a combination of various different sub-protocols. Our modular approach has the benefit that these components can be easily updated in future versions of RingCT, with only minor modifications to our analysis. At a technical level, we split our analysis in two parts. First, we identify which security notions for building blocks are needed to imply security for the whole system. Interestingly, we observe that existing and well-established notions (e.g., for the linkable ring signature) are insufficient. Second, we analyze all building blocks as implemented in Monero and prove that they satisfy our new notions. Here, we leverage the algebraic group model to overcome subtle problems in the analysis of the linkable ring signature component. As another technical highlight, we show that our security goals can be mapped to a suitable graph problem, which allows us to take advantage of the theory of network flows in our analysis. This new approach is also useful for proving security of other cryptocurrencies.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_5"}, {"primary_key": "582720", "vector": [], "sparse_vector": [], "title": "SQIsignHD: New Dimensions in Cryptography.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce SQIsignHD, a new post-quantum digital signature scheme inspired by SQIsign. SQIsignHD exploits the recent algorithmic breakthrough underlying the attack on SIDH, which allows to efficiently represent isogenies of arbitrary degrees as components of a higher dimensional isogeny. SQIsignHD overcomes the main drawbacks of SQIsign. First, it scales well to high security levels, since the public parameters for SQIsignHD are easy to generate: the characteristic of the underlying field needs only be of the form\\(2^{f}3^{f'}-1\\). Second, the signing procedure is simpler and more efficient. Our signing procedure implemented in C runs in 28 ms, which is a significant improvement compared to SQISign. Third, the scheme is easier to analyse, allowing for a much more compelling security reduction. Finally, the signature sizes are even more compact than (the already record-breaking) SQIsign, with compressed signatures as small as 109 bytes for the post-quantum NIST-1 level of security. These advantages may come at the expense of the verification, which now requires the computation of an isogeny in dimension 4, a task whose optimised cost is still uncertain, as it has been the focus of very little attention. Our experimentalsagemathimplementation of the verification runs in around 600 ms, indicating the potential cryptographic interest of dimension 4 isogenies after optimisations and low level implementation.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_1"}, {"primary_key": "582721", "vector": [], "sparse_vector": [], "title": "Key Recovery Attack on the Partial Vandermonde Knapsack Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Partial Vandermonde\\((\\textsf{PV})\\)Knapsack problem is an algebraic variant of the low-density inhomogeneous\\(\\textsf{SIS}\\)problem. The problem has been used as a building block for various lattice-based constructions, including signatures (ACNS’14, ACISP’18), encryptions (DCC’15,DCC’20), and signature aggregation (Eprint’20). At Crypto’22, Boudgoust, Gachon, and Pellet<PERSON> proposed a key distinguishing attack on the\\(\\textsf{PV}\\)Knapsack exploiting algebraic properties of the problem. Unfortunately, their attack doesn’t offer key recovery, except for worst-case keys. In this paper, we propose an alternative attack on the\\(\\textsf{PV}\\)Knapsack problem which provides key recovery for a much larger set of keys. Like the Crypto’22 attack, it is based on lattice reduction and uses a dimension reduction technique to speed-up the underlying lattice reduction algorithm and enhance its performance. As a side bonus, our attack transforms the\\(\\textsf{PV}\\)Knapsack problem into\\(\\textsf{uSVP}\\)instances instead of\\(\\textsf{SVP}\\)instances in the Crypto’22 attack. This also helps the lattice reduction algorithm, both from a theoretical and practical point of view. We use our attack to re-assess the hardness of the concrete parameters used in the literature. It appears that many contain a non-negligible fraction of weak keys, which are easily identified and extremely susceptible to our attack. For example, a fraction of\\(2^{-19}\\)of the public keys of a parameter set from ACISP’18 can be solved in about 30 hours on a moderate server using off-the-shelf lattice reduction. This parameter set was initially claimed to have a 129-bit security against key recovery attack. Its security was reduced to 87-bit security using the distinguishing attack from Crypto’22. Similarly, the ACNS’14 proposal also includes a parameter set containing a fraction of\\(2^{-19}\\)of weak keys; those can be solved in about 17 hours.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_8"}, {"primary_key": "582722", "vector": [], "sparse_vector": [], "title": "Closing the Efficiency Gap Between Synchronous and Network-Agnostic Consensus.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In the consensus problem,nparties want to agree on a common value, even if some of them are corrupt and arbitrarily misbehave. If the parties have a common inputm, then they must agree onm. Protocols solving consensus assume either asynchronouscommunication network, where messages are delivered within a known time, or anasynchronousnetwork with arbitrary delays. Asynchronous protocols only tolerate\\(t_a < n/3\\)corrupt parties. Synchronous ones can tolerate\\(t_s < n/2\\)corruptions with setup, but their security completely breaks down if the synchrony assumptions are violated. Network-agnostic consensus protocols, as introduced by <PERSON><PERSON>, <PERSON>, and <PERSON> [TCC’19], are secure regardless of network conditions, tolerating up to\\(t_s\\)corruptions with synchrony and\\(t_a\\)without, under provably optimal assumptions\\(t_a \\le t_s\\)and\\(2t_s + t_a < n\\). Despite efforts to improve their efficiency, all known network-agnostic protocols fall short of the asymptotic complexity of state-of-the-art purely synchronous protocols. In this work, we introduce a novel technique to compileanysynchronous andanyasynchronous consensus protocols into a network-agnostic one. This process only incurs a small constant number of overhead rounds, so that the compiled protocol matches the optimal round complexity for synchronous protocols. Our compiler also preserves under a variety of assumptions the asymptotic communication complexity of state-of-the-art synchronous and asynchronous protocols. Hence, it closes the current efficiency gap between synchronous and network-agnostic consensus. As a plus, our protocols support\\(\\ell \\)-bit inputs, and can be extended to achieve communication complexity\\(\\mathcal {O}(n^2\\kappa + \\ell n)\\)under the assumptions for which this is known to be possible for purely synchronous protocols.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_15"}, {"primary_key": "582723", "vector": [], "sparse_vector": [], "title": "Tight Indistinguishability Bounds for the XOR of Independent Random Permutations by Fourier Analysis.", "authors": ["<PERSON><PERSON>"], "summary": "The XOR of two independent permutations (XoP) is a well-known construction for achieving security beyond the birthday bound when implementing a pseudorandom function using a block cipher (i.e., a pseudorandom permutation). The idealized construction (where the permutations are uniformly chosen and independent) and its variants have been extensively analyzed over nearly 25 years. The best-known asymptotic information-theoretic indistinguishability bound for the XoP construction is\\(O(q/2^{1.5n})\\), derived by <PERSON><PERSON><PERSON> in 2017. A generalization of the XoP construction outputs the XOR of\\(r \\ge 2\\)independent permutations, and has also received significant attention in both the single-user and multi-user settings. In particular, for\\(r = 3\\), the best-known bound (obtained by <PERSON> et al. [ASIACRYPT’22]) is about\\(q^2/2^{2.5 n}\\)in the single-user setting and\\(\\sqrt{u} q_{\\max }^2/2^{2.5 n}\\)in the multi-user setting (whereuis the number of users and\\(q_{\\max }\\)is the number of queries per user). In this paper, we prove an indistinguishability bound of\\(q/2^{(r - 0.5)n}\\)for the (generalized) XoP construction in the single-user setting, and a bound of\\(\\sqrt{u} q_{\\max }/2^{(r - 0.5)n}\\)in the multi-user setting. In particular, for\\(r=2\\), we obtain the bounds\\(q/2^{1.5n}\\)and\\(\\sqrt{u} q_{\\max }/2^{1.5n}\\)in single-user and multi-user settings, respectively. For\\(r=3\\)the corresponding bounds are\\(q/2^{2.5n}\\)and\\(\\sqrt{u} q_{\\max }/2^{2.5n}\\). All of these bounds hold assuming\\(q < 2^{n}/2\\)(or\\(q_{\\max } < 2^{n}/2\\)). Compared to previous works, we improve all the best-known bounds for the (generalized) XoP construction in the multi-user setting, and the best-known bounds for the generalized XoP construction for\\(r \\ge 3\\)in the single-user setting (assuming\\(q \\ge 2^{n/2}\\)). For the basic two-permutation XoP construction in the single-user setting, our concrete bound of\\(q/2^{1.5n}\\)stands in contrast to the asymptotic bound of\\(O(q/2^{1.5n})\\)by Eberhard. Since all of our bounds are matched (up to constant factors) for\\(q > 2^{n/2}\\)by attacks published by Patarin in 2008 (and their generalizations to the multi-user setting), they are all tight. We obtain our results by Fourier analysis of Boolean functions. Most of our technical work involves bounding (sums of) Fourier coefficients of the density function associated with sampling without replacement. While the proof of Eberhard relies on similar bounds, our proof is elementary and significantly simpler.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_2"}, {"primary_key": "582724", "vector": [], "sparse_vector": [], "title": "M&amp;M&apos;S: Mix and Match Attacks on Schnorr-Type Blind Signatures with Repetition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Blind signatures allow the issuing of signatures on messages chosen by the user so that they ensureblindnessof the message against the signer. Moreover, a malicious user cannot output\\(\\ell +1\\)signatures while only finishing\\(\\ell \\)signing sessions. This notion, calledone-moreunforgeability, comes in two flavors supporting eithersequentialorconcurrentsessions. In this paper, we investigate the security of a class of blind signatures constructed from Sigma-protocols with small challenge space\\(\\mathcal {C}_{\\varSigma }\\)(i.e., polynomial in the security parameter), usingkrepetitions of the protocol to decrease the chances of a cheating prover. This class of schemes includes, among others, the Schnorr blind signature scheme with bit challenges and the recently proposed isogeny-based scheme CSI-Otter (Crypto’23). For this class of blind signatures, we show apolynomial-timeattack that breaks one-more unforgeability for any\\(\\ell \\ge k\\)concurrent sessions in time\\(O(k \\cdot |\\mathcal {C}_{\\varSigma }|)\\). Contrary to the ROS attack, ours is generic and does not require any particular algebraic structure. We also propose a computational trade-off, where, for any\\(t \\le k\\), our attack works for\\(\\ell = \\frac{k}{t}\\)in time\\(O(\\frac{k}{t} \\cdot |\\mathcal {C}_{\\varSigma }|^t)\\). The consequences of our attack are as follows. Schemes in the investigated class of blind signatures should not be used concurrently without applying specific transformations to boost the security to support more signing sessions. Moreover, for the parameters proposed for CSI-Otter (\\(k=128\\)and\\(|\\mathcal {C}_{\\varSigma }|=2\\)), the scheme becomes forgeable after 128 concurrent signing sessions for the basic attack and with only eight sessions in our optimized attack. We also show that for those parameters, it is even possible to compute two signatures in around 10 min with just one signing session using the computation power of the Bitcoin network. Thus, we show that, for sequential security, the parameterkmust be at least doubled in the security parameter for any of the investigated schemes.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_13"}, {"primary_key": "582725", "vector": [], "sparse_vector": [], "title": "Laconic Function Evaluation, Functional Encryption and Obfuscation for RAMs with Sublinear Computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Laconic function evaluation (LFE) is a “flipped” version of fully homomorphic encryption, where the server performing the computation gets the output. The server commits itself to a functionfby outputting a small digest. Clients can later efficiently encrypt inputsxwith respect to the digest in much less time than computingf, and ensure that the server only decryptsf(x), but does not learn anything else aboutx. Prior works constructed LFE forcircuitsunder LWE, and forTuring Machines (TMs)from indistinguishability obfuscation (iO). In this work we introduce LFE forRandom-Access Machines(RAM-LFE). The server commits itself to a potentially huge databaseyvia a short digest. Clients can later efficiently encrypt inputsxwith respect to the digest and the server decryptsf(x,y) for some specified RAM programf(e.g., a universal RAM), without learning anything else aboutx. The main advantage of RAM-LFE is that the server’s decryption run-time only scales with the RAM run-timeTof the computationf(x,y), which can be sublinear in both |x| and |y|. We consider aweakly efficientvariant, where the client’s run-time is also allowed to scale linearly withT, but not |y|, and afully efficientvariant, where the client’s run-time must be sublinear in bothTand |y|. We construct the former from doubly efficient private information retrieval (DEPIR) and laconic OT (LOT), both of which are known from RingLWE, and the latter from an additional use of iO. We then show how to leverage fully efficient RAM-LFE to also get (many-key)functional encryption for RAMs (RAM-FE)where secret keys are associate with big databasesyand the decryption time is sublinear in |y|, as well asiO for RAMswhere the obfuscated program contains a big databaseyand the evaluation time is sublinear in |y|.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_7"}, {"primary_key": "582726", "vector": [], "sparse_vector": [], "title": "Asymptotics and Improvements of Sieving for Codes.", "authors": ["Léo Du<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A recent work of <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (Eprint’23) proposes a promising adaptation of sieving techniques from lattices to codes, in particular claiming concrete cryptanalytic improvements on various schemes. The core of their algorithm reduces to a Near Neighbor Search (NNS) problem, for which they devise an ad-hoc approach. In this work, we aim for a better theoretical understanding of this approach. First we provide an asymptotic analysis which is not present in the original paper. Second, we propose a more systematic use of known NNS machinery, namely Locality Sensitive Hashing and Filtering (LSH/F), an approach that has been applied very successfully in the case of sieving over lattices. We establish the first baseline for the sieving approach with a decoding complexity of\\(2^{0.117n}\\)for the conventional worst parameters (full distance decoding, complexity maximized over all code rates). Our cumulative improvements, eventually enable us to lower the hardest parameter decoding complexity for SievingISD algorithms to\\(2^{0.101n}\\). While this outperforms the BJMM algorithm (Eurocrypt’12) it falls yet behind the most advanced conventional ISD approach by Both and May (PQCrypto’18). As for lattices, we found the Random-Spherical-Code-Product (RPC) gives the best asymptotic complexity. Moreover, we also consider an alternative that seems specific to the Hamming Sphere, which we believe could be of practical interest, as they plausibly hide less sub-exponential overheads than RPC.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_6"}, {"primary_key": "582727", "vector": [], "sparse_vector": [], "title": "Time-Lock Puzzles with Efficient <PERSON>ch Solving.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Time-Lock Puzzles (TLPs) are a powerful tool for concealing messages until a predetermined point in time. When solving multiple puzzles, in many cases, it becomes crucial to have the ability tobatch-solvepuzzles, i.e., simultaneously open multiple puzzles while working to solve asingle one. Unfortunately, all previously known TLP constructions that support batch solving rely on super-polynomially secure indistinguishability obfuscation, making them impractical. In light of this challenge, we present novel TLP constructions that offer batch-solving capabilities without using heavy cryptographic hammers. Our proposed schemes are simple and concretely efficient, and they can be constructed based on well-established cryptographic assumptions based on pairings or learning with errors (LWE). Along the way, we introduce new constructions of puncturable key-homomorphic PRFs both in the lattice and in the pairing setting, which may be of independent interest. Our analysis leverages an interesting connection to <PERSON>’s marriage theorem and incorporates an optimized combinatorial approach, enhancing the practicality and feasibility of our TLP schemes. Furthermore, we introduce the concept of “rogue-puzzle attacks”, where maliciously crafted puzzle instances may disrupt the batch-solving process of honest puzzles. We then propose constructions of concrete and efficient TLPs designed to prevent such attacks.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_11"}, {"primary_key": "582728", "vector": [], "sparse_vector": [], "title": "Lower-Bounds on Public-Key Operations in PIR.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Private information retrieval (PIR) is a fundamental cryptographic primitive that allows a user to fetch a database entry without revealing to the server which database entry it learns. PIR becomes non-trivial if the server communication is less than the database size. We show that building (even) very weak forms of PIR protocols requires that the amount of public-key operations scale linearly in the database size. We then use this bound to examine the related problem of communication efficient oblivious transfer (OT) extension. Oblivious transfer is a crucial building block in secure multi-party computation (MPC). In most MPC protocols, OT invocations are the main bottleneck in terms of computation and communication. OT extension techniques allow one to minimize the number of public-key operations in MPC protocols. One drawback of all existing OT extension protocols is their communication overhead. In particular, the sender’s communication is roughly double what is information-theoretically optimal. We show that OT extension with close to optimal sender communication is impossible, illustrating that the communication overhead is inherent. Our techniques go much further; we can show many lower bounds on communication-efficient MPC. E.g. we prove that to build high-rate string OT with generic groups, the sender needs to do linearly many group operations.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_3"}, {"primary_key": "582729", "vector": [], "sparse_vector": [], "title": "Partial Sums Meet FFT: Improved Attack on 6-Round AES.", "authors": ["<PERSON><PERSON>", "Shi<PERSON><PERSON> Ghos<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Thepartial sumscryptanalytic technique was introduced in 2000 by <PERSON> et al., who used it to break 6-round AES with time complexity of\\(2^{52}\\)S-box computations – a record that has not been beaten ever since. In 2014, <PERSON><PERSON> and <PERSON><PERSON> showed that for 6-round AES, partial sums can be replaced by a technique based on the Fast Fourier Transform (FFT), leading to an attack with a comparable complexity. In this paper we show that the partial sums technique can be combined with an FFT-based technique, to get the best of the two worlds. Using our combined technique, we obtain an attack on 6-round AES with complexity of about\\(2^{46.4}\\)additions. We fully implemented the attack experimentally, along with the partial sums attack and the Todo-Aoki attack, and confirmed that our attack improves the best known attack on 6-round AES by a factor of more than 32. We expect that our technique can be used to significantly enhance numerous attacks that exploit the partial sums technique. To demonstrate this, we use our technique to improve the best known attack on 7-round Kuznyechik by a factor of more than 80.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_5"}, {"primary_key": "582730", "vector": [], "sparse_vector": [], "title": "Bulletproofs++: Next Generation Confidential Transactions via Reciprocal Set Membership Arguments.", "authors": ["<PERSON>", "Sanket Kanjalkar", "<PERSON>", "<PERSON>"], "summary": "Zero-knowledge proofs are a cryptographic cornerstone of privacy-preserving technologies such as “Confidential Transactions” (CT), which aims at hiding monetary amounts in cryptocurrency transactions. Due to its asymptotically logarithmic proof size and transparent setup, most state-of-the-art CT protocols use the Bulletproofs (BP) [8] zero-knowledge proof system for set membership proofs such as range proofs. However, even taking into account recent efficiency improvements, BP comes with a serious overhead in terms of concrete proof size as well as verifier running time and thus puts a large burden on practical deployments of CT and its extensions. In this work, we introduce Bulletproofs++ (BP++), a drop-in replacement for BP that improves its concrete efficiency and compactness significantly. As for BP, the security of BP++ relies only on the hardness of the discrete logarithm problem in the random oracle model, and BP++ retains all features of Bulletproofs including transparent setup and support for proof aggregation, multi-party proving and batch verification. Asymptotically, BP++ range proofs require only\\(O(n / \\log n)\\)group scalar multiplications compared toO(n) for BP and BP+. At the heart of our construction are novel techniques for permutation and set membership, enabling highly efficient proofs of statements encoded as arithmetic circuits. Concretely, a single BP++ range proof to establish that a committed value is in a 64-bit range (as commonly required by CT) is just 416 bytes over a 256-bit elliptic curve, 38% smaller than an equivalent BP and 27% smaller than BP+. When instantiated on the secp256k1 curve as used in Bitcoin, our benchmarks show that proving is about 5 times faster than BP and verification is about 3 times faster than BP+. When aggregating 32 range proofs, proving and verification are about 9.5 times and 5.5 times faster, respectively.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_9"}, {"primary_key": "582731", "vector": [], "sparse_vector": [], "title": "Efficient and Generic Methods to Achieve Active Security in Private Information Retrieval and More Advanced Database Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by secure database search, we present secure computation protocols for a functionfin the client-servers setting, where a client can obtainf(x) on a private inputxby communicating with multiple servers each holdingf. Specifically, we propose generic compilers from passively secure protocols, which only keep security against servers following the protocols, to actively secure protocols, which guarantee privacy and correctness even against malicious servers. Our compilers are applied to protocols computing any class of functions, and are efficient in that the overheads in communication and computational complexity are only polynomial in the number of servers, independent of the complexity of functions. We then apply our compilers to obtain concrete actively secure protocols for various functions including private information retrieval (PIR), bounded-degree multivariate polynomials and constant-depth circuits. For example, our actively secure PIR protocols achieve exponentially better computational complexity in the number of servers than the currently best-known protocols. Furthermore, our protocols for polynomials and constant-depth circuits reduce the required number of servers compared to the previous actively secure protocols. In particular, our protocol instantiated from the sparse Learning Parity with Noise (LPN) assumption is the first actively secure protocol for multivariate polynomials which has the minimum number of servers, without assuming fully homomorphic encryption.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_4"}, {"primary_key": "582732", "vector": [], "sparse_vector": [], "title": "Plover: Masking-Friendly Hash-and-Sign La<PERSON>ce Signatures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a toolkit for transforming lattice-based hash-and-sign signature schemes into masking-friendly signatures secure in thet-probing model. Until now, efficiently masking lattice-based hash-and-sign schemes has been an open problem, with unsuccessful attempts such as Mitaka. A first breakthrough was made in 2023 with the NIST PQC submission Raccoon, although it was not formally proven. Our main conceptual contribution is to realize that the same principles underlying Ra<PERSON>on are very generic, and to find a systematic way to apply them within the hash-and-sign paradigm. Our main technical contribution is to formalize, prove, instantiate and implement a hash-and-sign scheme based on these techniques. Our toolkit includes noise flooding to mitigate statistical leaks, and an extended Strong Non-Interfering probing security (\\(\\textsf{SNIu} \\)) property to handle masked gadgets with unshared inputs. We showcase the efficiency of our techniques in a signature scheme,\\(\\mathsf {\\mathsf {\\textsf{Plover}} \\text {-}\\mathsf {\\textsf{RLWE}} }\\), based on (hint) Ring-LWE. It is thefirstlattice-based masked hash-and-sign scheme with quasi-linear complexity\\(O(d \\log d)\\)in the number of sharesd. Our performances are competitive with the state-of-the-art masking-friendly signature, the Fiat-Shamir scheme\\(\\textsf{<PERSON><PERSON><PERSON>}\\).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_12"}, {"primary_key": "582733", "vector": [], "sparse_vector": [], "title": "Connecting Leakage-Resilient Secret Sharing to Practice: Scaling Trends and Physical Dependencies of Prime Field Masking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Symmetric ciphers operating in (small or mid-size) prime fields have been shown to be promising candidates to maintain security against low-noise (or even noise-free) side-channel leakage. In order to design prime ciphers that best trade physical security and implementation efficiency, it is essential to understand how side-channel security evolves with the field size (i.e., scaling trends). Unfortunately, it has also been shown that such scaling trends depend on the leakage functions and cannot be explained by the standard metrics used to analyze Boolean masking with noise. In this work, we therefore initiate a formal study of prime field masking for two canonical leakage functions: bit leakages and Hamming weight leakages. By leveraging theoretical results from the leakage-resilient secret sharing literature, we explain formally why (1) bit leakages correspond to a worst-case and do not encourage operating in larger fields, and (2) an opposite conclusion holds for Hamming weight leakages, where increasing the prime field moduluspcan contribute to a security amplification that is exponential in the number of shares, with\\(\\log (p)\\)seen as security parameter like the noise variance in Boolean masking. We combine these theoretical results with simulated experiments and show that the interest masking in larger prime fields can degrade gracefully when leakage functions slightly deviate from the Hamming weight abstraction, motivating further research towards characterizing (ideally wide) classes of leakage functions offering such guarantees.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_12"}, {"primary_key": "582734", "vector": [], "sparse_vector": [], "title": "Isogeny Problems with Level Structure.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given two elliptic curves and the degree of an isogeny between them, finding the isogeny is believed to be a difficult problem—upon which rests the security of nearly any isogeny-based scheme. If, however, to the data above we add information about the behavior of the isogeny on a large enough subgroup, the problem can become easy, as recent cryptanalyses on SIDH have shown. Between the restriction of the isogeny to a fullN-torsion subgroup and no “torsion information” at all lies a spectrum of interesting intermediate problems, raising the question of how easy or hard each of them is. Here we exploremodular isogeny problemswhere the torsion information is masked by the action of a group of\\(2\\times 2\\)matrices. We give reductions between these problems, classify them by their difficulty, and link them to security assumptions found in the literature.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_7"}, {"primary_key": "582735", "vector": [], "sparse_vector": [], "title": "Improving Linear Key Recovery Attacks Using Walsh Spectrum Puncturing.", "authors": ["<PERSON>Guti<PERSON>", "<PERSON><PERSON>"], "summary": "In some linear key recovery attacks, the function which determines the value of the linear approximation from the plaintext, ciphertext and key is replaced by a similar map in order to improve the time or memory complexity at the cost of a data complexity increase. We propose a general framework for key recovery map substitution, and introduceWalsh spectrum puncturing, which consists of removing carefully-chosen coefficients from the Walsh spectrum of this map. The capabilities of this technique are illustrated by describing improved attacks on reduced-round Serpent (including the first 12-round attack on the 192-bit key variant), GIFT-128 andNoekeon, as well as the full DES.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_7"}, {"primary_key": "582736", "vector": [], "sparse_vector": [], "title": "Public-Coin, Complexity-Preserving, Succinct Arguments of Knowledge for NP from Collision-Resistance.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Succinct arguments allow a powerful (yet polynomial-time) prover to convince a weak verifier of the validity of some NP statement using very little communication. A major barrier to the deployment of such proofs is the unwieldy overhead of the prover relative to the complexity of the statement to be proved. In this work, we focus oncomplexity-preservingarguments where proving a non-deterministic timetand spacesRAM computation takes time\\(\\tilde{O}(t)\\)and space\\(\\tilde{O}(s)\\). Currently, all known complexity-preserving arguments either are private-coin, rely on non-standard assumptions, or provide only weak succinctness. In this work, we construct complexity-preserving succinct argument based solely on collision-resistant hash functions, thereby matching the classic succinct argument of <PERSON><PERSON> (STOC ’92).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_5"}, {"primary_key": "582737", "vector": [], "sparse_vector": [], "title": "Concurrently Secure Blind Schnorr Signatures.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Many applications of blind signatures, e.g. in blockchains, require compatibility of the resulting signatures with the existing system. This makes blind issuing of Schnorr signatures (now being standardized and supported by major cryptocurrencies) desirable. Concurrent security of the signing protocol is required to thwart denial-of-service attacks. We present a concurrently secure blind-signing protocol for Schnorr signatures, using the standard primitives NIZK and PKE and assuming that Schnorr signatures themselves are unforgeable. Our protocol is the first to be compatible with standard Schnorr implementations over 256-bit elliptic curves. We cast our scheme as a generalization of blind and partially blind signatures: we introduce the notion ofpredicate blind signatures, in which the signer can define a predicate that the blindly signed message must satisfy. We provide implementations and benchmarks for various choices of primitives and scenarios, such as blindly signing Bitcoin transactions only when they meet certain conditions specified by the signer.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_5"}, {"primary_key": "582738", "vector": [], "sparse_vector": [], "title": "A Novel Framework for Explainable Leakage Assessment.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Schemes such as Common Criteria or FIPS 140-3 require the assessment of cryptographic implementations with respect to side channels at high security levels. Instead of a “penetration testing” style approach where specific tests are carried out, FIPS 140-3 relies on non-specific “leakage assessment” to identify potential side channel leaks in implementations of symmetric schemes. Leakage assessment, as it is understood today, is based on a simple leakage detection testing regime. Leakage assessment to date, provides no evidence whether or not the potential leakage is exploitable in a concrete attack: if a device fails the test, (and therefore certification under the FIPS 140-3 scheme) it remains unclear why it fails. We propose a novel assessment regime that is based on a different statistical rational than the existing leakage detection tests. Our statistical approach enables non-specific detection (i.e. we do not require to specify intermediate values) whilst simultaneously generating evidence for designing an attack vector that exploits identified leakage. We do this via an iterative approach, based on building and comparing nested regression models. We also provide, for the first time, concrete definitions for concepts such as key leakage, exploitable leakage and explainable leakage. Finally, we illustrate our novel leakage assessment framework in the context of two open source masked software implementations on a processor that is known to exhibit micro-architectural leakage.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_8"}, {"primary_key": "582739", "vector": [], "sparse_vector": [], "title": "Proof-of-Work-Based Consensus in Expected-Constant Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the traditional consensus problem (aka Byzantine agreement), parties are required to agree on a common value despite the malicious behavior of some of them, subject to the condition that if all the honest parties start the execution with the same value, then that should be the outcome. This problem has been extensively studied by both the distributed computing and cryptographic protocols communities. With the advent of blockchains, whose main application—a distributed ledger—essentially requires that miners agree on their views, new techniques have been proposed to solve the problem, and in particular in so-called “permissionless” environments, where parties are not authenticated or have access to point-to-point channels and, further, may come and go as they please. So far, the fastest way to achieve consensus in the proof-of-work (PoW)-based setting of Bitcoin, takes\\(O(\\textsf{polylog} \\kappa )\\)number of rounds, where\\(\\kappa \\)is the security parameter. We present the first protocol in this setting that requiresexpected-constantnumber of rounds. Furthermore, we show how to apply securely sequential composition in order to yield a fast distributed ledger protocol that settlesalltransactions in expected-constant time. Our result is based on a novel instantiation of “m-for-1 PoWs” on parallel chains that facilitates our basic building block, Chain-King Consensus. The techniques we use, via parallel chains, to port classical protocol design elements (such as Phase-King Consensus, super-phase sequential composition and others) into the permissionless setting may be of independent interest.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_4"}, {"primary_key": "582740", "vector": [], "sparse_vector": [], "title": "Foundations of Adaptor Signatures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>"], "summary": "Adaptor signatures extend the functionality of regular signatures through the computation ofpre-signatureson messages for statements of NP relations. Pre-signatures are publicly verifiable; they simultaneously hide and commit to a signature of an underlying signature scheme on that message. Anybody possessing a corresponding witness for the statement can adapt the pre-signature to obtain the “regular” signature. Adaptor signatures have found numerous applications for conditional payments in blockchain systems, like payment channels (CCS’20, CCS’21), private coin mixing (CCS’22, SP’23), and oracle-based payments (NDSS’23). In our work, we revisit the state of the security of adaptor signatures and their constructions. In particular, our two main contributions are: Security Gaps and Definitions:We review the widely-used security model of adaptor signatures due to Aumayret al.(AC’21) and identify gaps in their definitions that render known protocols for private coin-mixing and oracle-based paymentsinsecure. We give simple counterexamples of adaptor signatures that are secure w.r.t. their definitions but result in insecure instantiations of these protocols. To fill these gaps, we identify a minimal set of modular definitions that align with these practical applications. Secure Constructions:Despite their popularity,allknown constructions are (1) derived from identification schemes via the Fiat-Shamir transform in the random oracle model or (2) require modifications to the underlying signature verification algorithm, thus making the construction useless in the setting of cryptocurrencies. More concerningly,allknown constructions were proven secure w.r.t. the insufficient definitions of <PERSON><PERSON><PERSON> et al., leaving us with no provably secure adaptor signature scheme to use in applications. Firstly, in this work, we salvageallcurrent applications by proving the security of the widely-used Schnorr adaptor signatures under our proposed definitions. We then provide several new constructions, including presenting thefirstadaptor signature schemes for Camenisch-Lysyanskaya (CL), Boneh-Boyen-Shacham (BBS+), and Waters signatures, all of which are proven securein the standard model. Our new constructions rely on a new abstraction of digital signatures, calleddichotomic signatures, which covers the essential properties we need to build adaptor signatures. Proving the security of all constructions (including identification-based schemes) relies on anovel non-black-boxproof technique. Both our digital signature abstraction and the proof technique could be of independent interest to the community.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_6"}, {"primary_key": "582741", "vector": [], "sparse_vector": [], "title": "Efficient Pre-processing PIR Without Public-Key Cryptography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Classically, Private Information Retrieval (PIR) was studied in a setting without any pre-processing. In this setting, it is well-known that 1) public-key cryptography is necessary to achieve non-trivial (i.e., sublinear) communication efficiency in the single-server setting, and 2) the total server computation per query must be linear in the size of the database, no matter in the single-server or multi-server setting. Recent works have shown that both of these barriers can be overcome if we are willing to introduce a pre-processing phase. In particular, a recent work calledPianoshowed that using only one-way functions, one can construct a single-server preprocessing PIR with\\(\\widetilde{O}(\\sqrt{n})\\)bandwidth and computation per query, assuming\\(\\widetilde{O}(\\sqrt{n})\\)client storage. For the two-server setting, the state-of-the-art is defined by two incomparable results. First,Pianoimmediately implies a scheme in the two-server setting with the same performance bounds as stated above. Moreover, <PERSON><PERSON><PERSON> et al. showed a two-server scheme with\\(O(n^{1/3})\\)bandwidth and\\(O(n/\\log ^2 n)\\)computation per query, and one with\\(O(n^{1/2 + \\epsilon })\\)cost both in bandwidth and computation—both schemes provide information theoretic security. In this paper, we show that assuming the existence of one-way functions, we can construct a two-server preprocessing PIR scheme with\\(\\widetilde{O}(n^{1/4})\\)bandwidth and\\(\\widetilde{O}(n^{1/2})\\)computation per query, while requiring only\\(\\widetilde{O}(n^{1/2})\\)client storage. We also construct a new single-server preprocessing PIR scheme with\\(\\widetilde{O}(n^{1/4})\\)onlinebandwidth and\\(\\widetilde{O}(n^{1/2})\\)offlinebandwidth andcomputationper query, also requiring\\(\\widetilde{O}(n^{1/2})\\)client storage. Specifically, the online bandwidth is the bandwidth required for the client to obtain an answer, and the offline bandwidth can be viewed as background maintenance work amortized to each query. Our new constructions not only advance the theoretical understanding of preprocessing PIR, but are also concretely efficient because the only cryptography needed is pseudorandom functions.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_8"}, {"primary_key": "582742", "vector": [], "sparse_vector": [], "title": "Unbiasable Verifiable Random Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Verifiable Random Functions (VRFs) play a pivotal role in Proof of Stake (PoS) blockchain due to their applications in secret leader election protocols. However, the original definition by <PERSON><PERSON><PERSON>, <PERSON>was<PERSON> and <PERSON><PERSON>i is by itself insufficient for such applications. The primary concern is that adversaries may craft VRF key pairs with skewed output distribution, allowing them to unfairly increase their winning chances. To address this issue <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (2017/573) proposed a stronger definition in the universal composability framework, while <PERSON><PERSON><PERSON> et al. (FC ’21) put forward a weaker game-based one. Their proposed notions come with some limitations though. The former appears to be too strong, being seemingly impossible to instantiate without a programmable random oracle. The latter instead is not sufficient to prove security for VRF-based secret leader election schemes. In this work we close the above gap by proposing a new security property for VRF we callunbiasability. On the one hand, our notion suffices to imply fairness in VRF-based leader elections protocols. On the other hand, we provide an efficient compiler in the plain model (with no CRS) transforming any VRF into an unbiasable one under standard assumptions. Moreover, we show folklore VRF constructions in the ROM to achieve our notion without the need to program the random oracle. As a minor contribution, we also provide a generic and efficient construction of certified 1 to 1 VRFs from any VRF.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_6"}, {"primary_key": "582743", "vector": [], "sparse_vector": [], "title": "Generalized Feistel Ciphers for Efficient Prime Field Masking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A recent work from Eurocrypt 2023 suggests that prime-field masking has excellent potential to improve the efficiency vs. security tradeoff of masked implementations against side-channel attacks, especially in contexts where physical leakages show low noise. We pick up on the main open challenge that this seed result leads to, namely the design of an optimized prime cipher able to take advantage of this potential. Given the interest of tweakable block ciphers with cheap inverses in many leakage-resistant designs, we start by describing theFPM(Feistel for Prime Masking) family of tweakable block ciphers based on a generalized Feistel structure. We then propose a first instantiation ofFPM, which we denote assmall-pSquare. It builds on the recent observation that the square operation (which is non-linear in\\(\\mathbb {F}_p\\)) can lead to masked gadgets that are more efficient than those for multiplication, and is tailored for efficient masked implementations in hardware. We analyze the mathematical security of theFPMfamily of ciphers and thesmall-pSquareinstance, trying to isolate the parts of our study that can be re-used for other instances. We additionally evaluate the implementation features ofsmall-pSquareby comparing the efficiency vs. security tradeoff of masked FPGA circuits against those of a state-of-the art binary cipher, namelySKINNY, confirming significant gains in relevant contexts.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_7"}, {"primary_key": "582744", "vector": [], "sparse_vector": [], "title": "Fast Batched Asynchronous Distributed Key Generation.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present new protocols for threshold Schnorr signatures that work in anasynchronouscommunication setting, providingrobustnessandoptimal resilience. These protocols provide unprecedented performance in terms of communication and computational complexity. In terms of communication complexity, for each signature, a single party must transmit a few dozen group elements and scalars across the network (independent of the size of the signing committee). In terms of computational complexity, the amortized cost for one party to generate a signature is actually less than that of just running the standard Schnorr signing or verification algorithm (at least for moderately sized signing committees, say, up to 100). For example, we estimate that with a signing committee of 49 parties, at most 16 of which are corrupt, we can generate50,000 Schnorr signatures per second(assuming each party can dedicate one standard CPU core and 500 Mbs of network bandwidth to signing). Importantly, this estimate includes both the cost of an offline precomputation phase (which just churns out message independent “presignatures”) and an online signature generation phase. Also, the online signing phase can generate a signature with very little network latency (just one to three rounds, depending on how throughput and latency are balanced). To achieve this result, we provide two new innovations. One is a new secret sharing protocol (again, asynchronous, robust, optimally resilient) that allows the dealer to securely distribute shares of a large batch of ephemeral secret keys, and to publish the corresponding ephemeral public keys. To achieve better performance, our protocol minimizes public-key operations, and in particular, is based on a novel technique that doesnotuse the traditional technique based on “polynomial commitments”. The second innovation is a new algorithm to efficiently combine ephemeral public keys contributed by different parties (some possibly corrupt) into a smaller number of secure ephemeral public keys. This new algorithm is based on a novel construction of a so-called “super-invertible matrix” along with a corresponding highly-efficient algorithm for multiplying this matrix by a vector of group elements. As protocols for verifiably sharing a secret key with an associated public key and the technology of super-invertible matrices both play a major role in threshold cryptography and multi-party computation, our two new innovations should have applicability well beyond that of threshold Schnorr signatures.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_13"}, {"primary_key": "582745", "vector": [], "sparse_vector": [], "title": "The NISQ Complexity of Collision Finding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Collision-resistant hashing, a fundamental primitive in modern cryptography, ensures that there is no efficient way to find distinct inputs that produce the same hash value. This property underpins the security of various cryptographic applications, making it crucial to understand its complexity. The complexity of this problem is well-understood in the classical setting and\\(\\varTheta (N^{1/2})\\)queries are needed to find a collision. However, the advent of quantum computing has introduced new challenges since quantum adversaries—equipped with the power of quantum queries—can find collisions much more efficiently. <PERSON><PERSON>, <PERSON> and <PERSON> [15] and <PERSON><PERSON> and <PERSON> [3] established that full-scale quantum adversaries require\\(\\varTheta (N^{1/3})\\)queries to find a collision, prompting a need for longer hash outputs, which impacts efficiency in terms of the key lengths needed for security. This paper explores the implications of quantum attacks in the Noisy-Intermediate Scale Quantum (NISQ) era. In this work, we investigate three different models for NISQ algorithms and achievetight bounds for all of them: A hybrid algorithm making adaptive quantum or classical queries but with a limited quantum query budget, or A quantum algorithm with access to a noisy oracle, subject to a dephasing or depolarizing channel, or A hybrid algorithm with an upper bound on its maximum quantum depth;i.e.a classical algorithm aided by low-depth quantum circuits. In fact, our results handle all regimes between NISQ and full-scale quantum computers. Previously, only results for the preimage search problem were known for these models (by <PERSON> and <PERSON> [50], <PERSON><PERSON><PERSON><PERSON> [45,46], <PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON> [17]) while nothing was known about the collision finding problem. Along with our main results, we develop an information-theoretic framework for recording query transcripts of quantum-classical algorithms. The main feature of this framework is that it allows us to record queries in two incompatible bases—classical queries in the standard basis and quantum queries in the Fourier basis—consistently. We call the framework thehybrid compressed oracleas it naturally interpolates between the classical way of recording queries and the compressed oracle framework of Zhandry for recording quantum queries. We demonstrate its applicability by giving simpler proofs of the optimal lower bounds for NISQ preimage search and by showing optimal lower bounds for NISQ collision finding.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_1"}, {"primary_key": "582746", "vector": [], "sparse_vector": [], "title": "Toward Malicious Constant-Rate 2PC via Arithmetic Garbling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A recent work by <PERSON>, <PERSON>, <PERSON>, and <PERSON> [Eurocrypt’23] presented a new instantiation of the arithmetic garbling paradigm introduced by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [FOCS’11]. In particular, <PERSON> et al.’s garbling scheme is the firstconstant-rategarbled circuit over large enoughbounded integer computations, inferring the first constant-round constant-rate secure two-party computation (2PC) over bounded integer computations in the presence ofsemi-honestadversaries. The main source of difficulty in lifting the security of garbling schemes-based protocols to the malicious setting lies in proving the correctness of the underlying garbling scheme. In this work, we analyze the security of <PERSON> et al.’s scheme in the presence of malicious attacks. We demonstrate anoverflow attack, which is inevitable in this computational model, even if the garbled circuit isfullycorrect. Our attack follows by defining an adversary, corruptingeitherthe garbler or the evaluator, that chooses a bad input and causes the computation to overflow, thus leaking information about the honest party’s input. By utilizing overflow attacks, we show that 1-bit leakage is necessary for achieving security against a malicious garbler, discarding the possibility of achieving full malicious security in this model. We further demonstrate a wider range of overflow attacks against a malicious evaluator with more than 1 bit of leakage. We boost the security level of <PERSON> et al.’s scheme by utilizing two variants of Vector Oblivious Linear Evaluation, denoted by VOLEc and aVOLE. We present thefirst constant-round constant-rate2PC protocol over bounded integer computations, in the presence of a malicious garbler with 1-bit leakage and a semi-honest evaluator, in the {VOLEc,aVOLE}-hybrid model and being black-box in the underlying group and ring. Compared to the semi-honest variant, our protocol incurs only a constant factor overhead, both in computation and communication. The constant-round and constant-rate properties hold even in the plain model.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_14"}, {"primary_key": "582747", "vector": [], "sparse_vector": [], "title": "Massive Superpoly Recovery with a Meet-in-the-Middle Framework - Improved Cube Attacks on Trivium and Kreyvium.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The cube attack extracts the information of secret key bits by recovering the coefficient called superpoly in the output bit with respect to a subset of plaintexts/IV, which is called a cube. While the division property provides an efficient way to detect the structure of the superpoly, superpoly recovery could still be prohibitively costly if the number of rounds is sufficiently high. In particular, Core Monomial Prediction (CMP) was proposed at ASIACRYPT 2022 as a scaled-down version of Monomial Prediction (MP), which sacrifices accuracy for efficiency but ultimately gets stuck at 848 rounds ofTrivium. In this paper, we provide new insights into CMP by elucidating the algebraic meaning to the core monomial trails. We prove that it is sufficient to recover the superpoly by extracting all the core monomial trails, an approach based solely on CMP, thus demonstrating that CMP can achieve perfect accuracy as MP does. We further reveal that CMP is still MP in essence, but with variable substitutions on the target function. Inspired by the divide-and-conquer strategy that has been widely used in previous literature, we design a meet-in-the-middle (MITM) framework, in which the CMP-based approach can be embedded to achieve a speedup. To illustrate the power of these new techniques, we apply the MITM framework toTrivium, Grain-128AEAD and Kreyvium. As a result, not only can the previous computational cost of superpoly recovery be reduced (e.g., 5x faster for superpoly recovery on 192-round Grain-128AEAD), but we also succeed in recovering superpolies for up to 851 rounds ofTriviumand up to 899 rounds of Kreyvium. This surpasses the previous best results by respectively 3 and 4 rounds. Using the memory-efficient Möbius transform proposed at EUROCRYPT 2021, we can perform key recovery attacks on target ciphers, even though the superpoly may contain over\\(2^{40}\\)monomials. This leads to the best cube attacks on the target ciphers.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_13"}, {"primary_key": "582748", "vector": [], "sparse_vector": [], "title": "Efficient <PERSON><PERSON><PERSON> in Garbled Circuits.", "authors": ["<PERSON>"], "summary": "Garbled Circuit (GC) techniques usually work with Boolean circuits. Despite intense interest, efficient arithmetic generalizations of GC were only known from strong assumptions, such as LWE. We construct symmetric-key-based arithmetic garbled circuits from circular correlation robust hashes, the assumption underlying the celebrated Free XOR garbling technique. Let\\(\\lambda \\)denote a security parameter, and consider the integers\\(\\mathbb {Z}_m\\)for any\\(m \\ge 2\\). Let\\(\\ell = \\lceil \\log _2 m \\rceil \\)be the bit length of\\(\\mathbb {Z}_m\\)values. We garble arithmetic circuits over\\(\\mathbb {Z}_m\\)where the garbling of each gate has size\\(O(\\ell \\cdot \\lambda )\\)bits. Contrast this with Boolean-circuit-based arithmetic, requiring\\(O(\\ell ^2\\cdot \\lambda )\\)bits via the schoolbook multiplication algorithm, or\\(O(\\ell ^{1.585}\\cdot \\lambda )\\)bits via <PERSON><PERSON><PERSON>’s algorithm. Our arithmetic gates are compatible with Boolean operations and with Garbled RAM, allowing to garble complex programs of arithmetic values.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_1"}, {"primary_key": "582749", "vector": [], "sparse_vector": [], "title": "Garbled Circuit Lookup Tables with Logarithmic Number of Ciphertexts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Garbled Circuit (GC) is a basic technique for practical secure computation. GC handles Boolean circuits; it consumes significant network bandwidth to transmit encoded gate truth tables, each of which scales with the computational security parameter\\(\\kappa \\). GC optimizations that reduce bandwidth consumption are valuable. It is natural to consider a generalization of Boolean two-input one-output gates (represented by 4-row one-column lookup tables, LUTs) to arbitraryN-rowm-column LUTs. Known techniques for this do not scale, with naïve size-\\(O(Nm\\kappa )\\)garbled LUT being the most practical approach in many scenarios. Our novel garbling scheme –\\(\\textsf{logrow}\\)– implements GC LUTs while sending only alogarithmicinNnumber of ciphertexts! Specifically, let\\(n = \\lceil \\log _2 N \\rceil \\). We allow the GC parties to evaluate a LUT for\\((n-1)\\kappa + nm\\kappa + Nm\\)bits of communication.\\(\\textsf{logrow}\\)is compatible with modern GC advances, e.g. half gates and free XOR. Our work improves state-of-the-art GC handling of several interesting applications, such as privacy-preserving machine learning, floating-point arithmetic, and DFA evaluation.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_7"}, {"primary_key": "582750", "vector": [], "sparse_vector": [], "title": "Certified Everlasting Secure Collusion-Resistant Functional Encryption, and More.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tapas Pal", "<PERSON><PERSON><PERSON>"], "summary": "We study certified everlasting secure functional encryption (FE) and many other cryptographic primitives in this work. Certified everlasting security roughly means the following. A receiver possessing a quantum cryptographic object (such as ciphertext) can issue a certificate showing that the receiver has deleted the cryptographic object and information included in the object (such as plaintext) was lost. If the certificate is valid, the security is guaranteed even if the receiver becomes computationally unbounded after the deletion. Many cryptographic primitives are known to be impossible (or unlikely) to have information-theoretical security even in the quantum world. Hence, certified everlasting security is a nice compromise (intrinsic to quantum). In this work, we define certified everlasting secure versions of FE, compute-and-compare obfuscation, predicate encryption (PE), secret-key encryption (SKE), public-key encryption (PKE), receiver non-committing encryption (RNCE), and garbled circuits. We also present the following constructions: Adaptively certified everlasting secure collusion-resistant public-key FE for all polynomial-size circuits from indistinguishability obfuscation and one-way functions. Adaptively certified everlasting secure bounded collusion-resistant public-key FE for\\(\\textsf{NC}^1\\)circuits from standard PKE. Certified everlasting secure compute-and-compare obfuscation from standard fully homomorphic encryption and standard compute-and-compare obfuscation. Adaptively (resp., selectively) certified everlasting secure PE from standard adaptively (resp., selectively) secure attribute-based encryption and certified everlasting secure compute-and-compare obfuscation. Certified everlasting secure SKE and PKE from standard SKE and PKE, respectively. Cetified everlasting secure RNCE from standard PKE. Cetified everlasting secure garbled circuits from standard SKE.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_15"}, {"primary_key": "582751", "vector": [], "sparse_vector": [], "title": "A General Framework for Lattice-Based ABE Using Evasive Inner-Product Functional Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a general framework for constructing attribute-based encryption (ABE) schemes for arbitrary function class based on lattices from two ingredients,i)a noisy linear secret sharing scheme for the class andii)a new type of inner-product functional encryption (IPFE) scheme, termedevasiveIPFE, which we introduce in this work. We propose lattice-based evasive IPFE schemes and establish their security under simple conditions based on variants of evasive learning with errors (LWE) assumption recently proposed by Wee [EUROCRYPT ’22] and Tsabary [CRYPTO ’22]. Our general framework is modular and conceptually simple, reducing the task of constructing ABE to that of constructing noisy linear secret sharing schemes, a more lightweight primitive. The versatility of our framework is demonstrated by three new ABE schemes based on variants of the evasive LWE assumption. We obtain two ciphertext-policy ABE schemes for all polynomial-size circuits with a predetermined depth bound. One of these schemes hassuccinctciphertexts and secret keys, of size polynomial in the depth bound, rather than the circuit size. This eliminates the need for tensor LWE, another new assumption, from the previous state-of-the-art construction by We<PERSON> [EUROCRYPT ’22]. We develop ciphertext-policy and key-policy ABE schemes for deterministic finite automata (DFA) and logspace Turing machines (\\(\\textsf{L}\\)). They are the first lattice-based public-key ABE schemes supporting uniform models of computation. Previous lattice-based schemes for uniform computation were limited to the secret-key setting or offered only weaker security against bounded collusion. Lastly, the new primitive of evasive IPFE serves as the lattice-based counterpart of pairing-based IPFE, enabling the application of techniques developed in pairing-based ABE constructions to lattice-based constructions. We believe it is of independent interest and may find other applications.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_15"}, {"primary_key": "582752", "vector": [], "sparse_vector": [], "title": "Leakage-Tolerant Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Aleakage-resilient circuitfor\\(f:\\{0,1\\}^n\\rightarrow \\{0,1\\}^m\\)is a randomized Boolean circuitCmapping a randomized encoding of an inputxto an encoding of\\(y=f(x)\\), such that applying any leakage function\\(L\\in \\mathcal L\\)to the wires ofCreveals essentially nothing aboutx. Aleakage-tolerant circuitachieves the stronger guarantee that even whenxandyare not protected by any encoding, the output ofLcan be simulated by applying some\\(L'\\in \\mathcal L\\)toxandyalone. Thus,Cis as secure as an ideal hardware implementation offwith respect to leakage from\\(\\mathcal L\\). Leakage-resilient circuits were constructed for low-complexity classes\\(\\mathcal L\\), including (length-toutput)\\(\\mathcal{A}\\mathcal{C}0\\)functions, parities, and functions with bounded communication complexity. In contrast, leakage-tolerantcircuits were only known for the simple case ofprobingleakage, whereLoutputs the values oftwires inC. We initiate a systematic study of leakage-tolerant circuits for natural classes\\(\\mathcal L\\)ofgloballeakage functions, obtaining the following main results. Leakage-tolerant circuits for depth-1 leakage.Every circuit\\(C_f\\)forfcan be efficiently compiled into an\\(\\mathcal L\\)-tolerant circuitCforf, where\\(\\mathcal L\\)includes all leakage functionsLthat output eithertparitiesortdisjunctions(alternatively, conjunctions) of any number of wires or their negations. In the case of parities, our simulator runs in\\(2^{O(t)}\\)time. We provide partial evidence that this may be inherent. Application to stateful leakage-resilient circuits.Using a general transformation from leakage-tolerant circuits, we obtain the first construction ofstatefult-leakage-resilient circuits that tolerate acontinuousparity leakage, and the first such construction for disjunction/conjunction leakage in which the circuit size grows sub-quadratically witht. Interestingly, here we can obtain\\(\\text {poly}(t)\\)-time simulation even in the case of parities.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_8"}, {"primary_key": "582753", "vector": [], "sparse_vector": [], "title": "Evaluating the Security of CRYSTALS-Dilithium in the Quantum Random Oracle Model.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the wake of recent progress on quantum computing hardware, the National Institute of Standards and Technology (NIST) is standardizing cryptographic protocols that are resistant to attacks by quantum adversaries. The primary digital signature scheme that NIST has chosen is\\(\\textsf{CRYSTALS}\\hbox {-}\\!\\textsf{Dilithium}\\). The hardness of this scheme is based on the hardness of three computational problems: Module Learning with Errors (\\(\\textsf{MLWE}\\)), Module Short Integer Solution (\\(\\textsf{MSIS}\\)), and\\(\\textsf{SelfTargetMSIS}\\).\\(\\textsf{MLWE}\\)and\\(\\textsf{MSIS}\\)have been well-studied and are widely believed to be secure. However,\\(\\textsf{SelfTargetMSIS}\\)is novel and, though classically as hard as\\(\\textsf{MSIS}\\), its quantum hardness is unclear. In this paper, we provide the first proof of the hardness of\\(\\textsf{SelfTargetMSIS}\\)via a reduction from\\(\\textsf{MLWE}\\)in the Quantum Random Oracle Model (QROM). Our proof uses recently developed techniques in quantum reprogramming and rewinding. A central part of our approach is a proof that a certain hash function, derived from the\\(\\textsf{MSIS}\\)problem, is collapsing. From this approach, we deduce a new security proof for\\(\\textsf{Dilithium}\\)under appropriate parameter settings. Compared to the previous work by <PERSON>ltz, <PERSON>yu<PERSON><PERSON><PERSON>, and Schaffner (EUROCRYPT 2018) that gave the only other rigorous security proof for a variant of\\(\\textsf{Dilithium}\\), our proof has the advantage of being applicable under the condition\\(q = 1 \\ \\textrm{mod} \\ 2n\\), whereqdenotes the modulus andnthe dimension of the underlying algebraic ring. This condition is part of the original\\(\\textsf{Dilithium}\\)proposal and is crucial for the efficient implementation of the scheme. We provide new secure parameter sets for\\(\\textsf{Dilithium}\\)under the condition\\(q = 1 \\ \\textrm{mod} \\ 2n\\), finding that our public key size and signature size are about\\(2.9\\times \\)and\\(1.3\\times \\)larger, respectively, than those proposed by Kiltz et al. at the same security level. [Full version:arXiv:2312.16619]", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_15"}, {"primary_key": "582754", "vector": [], "sparse_vector": [], "title": "Symmetric Signcryption and E2EE Group Messaging in Keybase.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new cryptographic primitive called symmetric signcryption, which differs from traditional signcryption because the sender and recipient share a secret key. We prove that a natural composition of symmetric encryption and signatures achieves strong notions of security against attackers that can learn and control many keys. We then identify that the core encryption algorithm of the Keybase encrypted messaging protocol can be modeled as a symmetric signcryption scheme. We prove the security of this algorithm, though our proof requires assuming non-standard, brittle security properties of the underlying primitives.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_10"}, {"primary_key": "582755", "vector": [], "sparse_vector": [], "title": "Tight Security of TNT and Beyond - Attacks, Proofs and Possibilities for the Cascaded LRW Paradigm.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> laid the theoretical foundations for tweakable block ciphers (TBC). In a seminal paper, they proposed two (up to) birthday-bound secure design strategies —LRW1andLRW2— to convert any block cipher into a TBC. Several of the follow-up works consider cascading ofLRW-type TBCs to construct beyond-the-birthday bound (BBB) secure TBCs. <PERSON><PERSON><PERSON> et al. demonstrated that just two-round cascading ofLRW2can already give a BBB security. <PERSON><PERSON> et al. undertook a similar exercise in context ofLRW1withTNT— a three-round cascading ofLRW1— that has been shown to achieve BBB security as well. In this paper, we present a CCA distinguisher onTNTthat achieves a non-negligible advantage with\\( O(2^{n/2}) \\)queries, directly contradicting the security claims made by the designers. We provide a rigorous and complete advantage calculation coupled with experimental verification that further support our claim. Next, we provide new and simple proofs of birthday-bound CCA security for bothTNTand its single-key variant, which confirm the tightness of our attack. Furthering on to a more positive note, we show that adding just one more block cipher call, referred as\\({4}\\hbox {-}\\textsf {LRW1} \\), does not just re-establish the BBB security, but also amplifies it up to\\( 2^{3n/4} \\)queries. As a side-effect of this endeavour, we propose a new abstraction of the cascadedLRW-design philosophy, referred to as theLRW+paradigm, comprising two block cipher calls sandwiched between a pair of tweakable universal hashes. This helps us to provide a modular proof covering all cascadedLRWconstructions with at least 2 rounds, including\\( {4}\\hbox {-}\\textsf {LRW1} \\), and its more established relative, the well-knownCLRW2, or more aptly,\\( {2}\\hbox {-}\\textsf {LRW2} \\).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_9"}, {"primary_key": "582756", "vector": [], "sparse_vector": [], "title": "Ordering Transactions with Bounded Unfairness: Definitions, Complexity and Constructions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "An important consideration in the context of distributed ledger protocols is fairness in terms of transaction ordering. Recent work [Crypto 2020] revealed a connection of (receiver) order fairness to social choice theory and related impossibility results arising from the <PERSON><PERSON><PERSON><PERSON> paradox. As a result of the impossibility, various relaxations of order fairness were proposed in prior works. Given that distributed ledger protocols, especially those processing smart contracts, must serialize the input transactions, a natural objective is to minimize the distance (in terms of number of transactions) between any pair of unfairly ordered transactions in the output ledger — a concept we callbounded unfairness. In state machine replication (SMR) parlance this asks for minimizing the number of unfair state updates occurring before the processing of any request. This unfairness minimization objective gives rise to a natural class of parametric order fairness definitions that has not been studied before. As we observe, previous realizable relaxations of order fairness do not yield good unfairness bounds. Achieving optimal order fairness in the sense of bounded unfairness turns out to be connected to the graph theoretic properties of the underlying transaction dependency graph and specifically thebandwidthmetric of strongly connected components in this graph. This gives rise to a specific instance of the definition that we call “directed bandwidth order-fairness” which we show that it captures the best possible that any ledger protocol can achieve in terms of bounding unfairness. We prove ordering transactions in this fashion is NP-hard and non-approximable for any constant ratio. Towards realizing the property, we put forth a new distributed ledger protocol calledTaxisthat achieves directed bandwidth order-fairness. We present two variations, one that matches the property perfectly but (necessarily) lacks in performance and liveness, and another that achieves liveness and better complexity while offering a slightly relaxed version of the property. Finally, we comment on applications of our work to social choice, a direction which we believe to be of independent interest.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_2"}, {"primary_key": "582757", "vector": [], "sparse_vector": [], "title": "How to Garble Mixed Circuits that Combine Boolean and Arithmetic Computations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The study of garbling arithmetic circuits is initiated by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [FOCS’11], which can be naturally extended to mixed circuits. The basis of mixed circuits includes Boolean operations, arithmetic operations over a large ring and bit-decomposition that converts an arithmetic value to its bit representation. We construct efficient garbling schemes for mixed circuits. In the random oracle model, we construct two garbling schemes: The first scheme targets mixed circuits modulo some\\(N\\approx 2^b\\). Addition gates are free. Each multiplication gate costs\\(O(\\lambda \\cdot b^{1.5})\\)communication. Each bit-decomposition costs\\(O(\\lambda \\cdot b^{2} / \\log {b})\\). The second scheme targets mixed circuit modulo some\\(N\\approx 2^b\\). Each addition gate and multiplication gate costs\\(O(\\lambda \\cdot b \\cdot \\log b / \\log \\log b)\\). Every bit-decomposition costs\\(O(\\lambda \\cdot b^2 / \\log b)\\). Our schemes improve on the work of <PERSON>, <PERSON>, and Rosulek [CCS’16] in the same model. Additionally relying on the DCR assumption, we construct in the programmable random oracle model a more efficient garbling scheme targeting mixed circuits over\\(\\mathbb Z_{2^b}\\), where addition gates are free, and each multiplication or bit-decomposition gate costs\\(O(\\lambda _{\\text {DCR}} \\cdot b)\\)communication. We improve on the recent work of Ball, <PERSON>, <PERSON>, and Liu [Eurocrypt’23] which also relies on the DCR assumption.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_12"}, {"primary_key": "582758", "vector": [], "sparse_vector": [], "title": "New Records in Collision Attacks on SHA-2.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "TheSHA-2family includingSHA-224,SHA-256,SHA-384,SHA-512,SHA-512/224andSHA512/256is a U.S. federal standard published by NIST. Especially, there is no doubt thatSHA-256is one of the most important hash functions used in real-world applications. Due to its complex design compared withSHA-1, there is almost no progress in collision attacks onSHA-2after ASIACRYPT 2015. In this work, we retake this challenge and aim to significantly improve collision attacks on theSHA-2family. First, we observe from many existing attacks onSHA-2that the current advanced tool to search forSHA-2characteristics has reached the bottleneck. Specifically, longer differential characteristics could not be found, and this causes that the collision attack could not reach more steps. To address this issue, we adopt <PERSON> et al.’s MILP-based method and implement it with SAT/SMT forSHA-2, where we also add more techniques to detect contradictions inSHA-2characteristics. This answers an open problem left in <PERSON> et al.’s paper to apply the technique toSHA-2. With this SAT/SMT-based tool, we search forSHA-2characteristics by controlling its sparsity in a dedicated way. As a result, we successfully find the first practical semi-free-start (SFS) colliding message pair for 39-stepSHA-256, improving the best 38-step SFS collision attack published at EUROCRYPT 2013. In addition, we also report the first practical free-start (FS) collision attack on 40-stepSHA-224, while the previously best theoretic 40-step attack has time complexity\\(2^{110}\\). Moreover, for the first time, we can mount practical and theoretic collision attacks on 28-step and 31-stepSHA-512, respectively, which improve the best collision attack only reaching 27 steps ofSHA-512at ASIACRYPT 2015. In a word, with new techniques to findSHA-2characteristics, we have made some notable progress in the analysis ofSHA-2after the major achievements made at EUROCRYPT 2013 and ASIACRYPT 2015.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_6"}, {"primary_key": "582759", "vector": [], "sparse_vector": [], "title": "Constant-Size zk-SNARKs in ROM from Falsifiable Assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove that the seminal KZG polynomial commitment scheme (PCS) is black-box extractable under a simple falsifiable assumption\\(\\textsf{ARSDH}\\). To create an interactive argument, we construct a compiler that combines a black-box extractable non-interactive PCS and a polynomial IOP (PIOP). The compiler incurs a minor cost per every committed polynomial. Applying the Fiat-Shamir transformation, we obtain slightly less efficient variants of well-known PIOP-based zk-SNARKs, such as Plonk, that are knowledge-sound in the ROM under the\\(\\textsf{ARSDH}\\)assumption. Importantly, there is no need for idealized group models or knowledge assumptions. This results in the first known zk-SNARKs in the ROM from falsifiable assumptions with both an efficient prover and constant-size argument.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_2"}, {"primary_key": "582760", "vector": [], "sparse_vector": [], "title": "A Direct PRF Construction from Kolmogorov Complexity.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "While classic results in the 1980s establish that one-way functions (OWF) imply the existence of pseudorandom generators (PRG) which in turn imply pseudorandom functions (PRF), the constructions (most notably the one from OWFs to PRGs) is complicated and inefficient. Consequently, researchers have developed alternativedirectconstructions of PRFs from various different concrete hardness assumptions. In this work, we continue this thread of work and demonstrate the first direct construction of PRFs from average-case hardness of the time-bounded Kolmogorov complexity problem\\(\\textsf{MK}^t\\textsf{P}[s]\\), where given a threshold,\\(s(\\cdot )\\), and a polynomial time-bound,\\(t(\\cdot )\\),\\(\\textsf{MK}^t\\textsf{P}[s]\\)denotes the language consisting of stringsxwitht-bounded Kolmogorov complexity,\\(K^t(x)\\), bounded bys(|x|). In more detail, we demonstrate a direct PRF construction with quasi-polynomial security from mild avg-case of hardness of\\(\\textsf{MK}^t\\textsf{P}[2^{O(\\sqrt{\\log n})}]\\)w.r.t the uniform distribution. We note that by earlier results, this assumption is known to be equivalent to the existence of quasi-polynomially secure OWFs; as such, our results yield the first direct (quasi-polynomially secure) PRF construction from a natural hardness assumptions that also is known to be implied by (quasi-polynomially secure) PRFs. Perhaps surprisingly, we show how to make use of the Nisan-Wigderson PRG construction to get a cryptographic, as opposed to a complexity-theoretic, PRG.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_14"}, {"primary_key": "582761", "vector": [], "sparse_vector": [], "title": "The Hardness of LPN over Any Integer Ring and Field for PCG Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Learning parity with noise (LPN) has been widely studied and used in cryptography. It was recently brought to new prosperity since <PERSON> et al. (CCS’18), putting LPN to a central role in designing secure multi-party computation, zero-knowledge proofs, private set intersection, and many other protocols. In this paper, we thoroughly studied the security of LPN problems in this particular context. We found that some important aspects have long been ignored and many conclusions from classical LPN cryptanalysis do not apply to this new setting, due to the low noise rates, extremely high dimensions, various types (in addition to\\(\\mathbb F _2\\)) and noise distributions. For LPN over a field, we give a parameterized reduction from exact-noise LPN to regular-noise LPN. Compared to the recent result by Feneuil, <PERSON><PERSON> and Rivain (Crypto’22), we significantly reduce the security loss by paying only a small additive price in dimension and number of samples. We analyze the security of LPN over a ring\\(\\mathbb Z _{2^\\lambda }\\). Existing protocols based on LPN over integer rings use parameters as if they are over fields, but we found an attack that effectively reduces the weight of a noise by half compared to LPN over fields. Consequently, prior works that use LPN over\\(\\mathbb Z _{2^\\lambda }\\)overestimate up to 40 bits of security. We provide a complete picture of the hardness of LPN over integer rings by showing: 1) the equivalence between its search and decisional versions; 2) an efficient reduction from LPN over\\(\\mathbb F _{2}\\)to LPN over\\(\\mathbb Z _{2^\\lambda }\\); and 3) generalization of our results to any integer ring. Finally, we provide an all-in-one estimator tool for the bit security of LPN parameters in the context of PCG, incorporating the recent advanced attacks.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_6"}, {"primary_key": "582762", "vector": [], "sparse_vector": [], "title": "Asymptotically Optimal Message Dissemination with Applications to Blockchains.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Messages in large-scale networks such as blockchain systems are typically disseminated using flooding protocols, in which parties send the message to a random set of peers until it reaches all parties. Optimizing the communication complexity of such protocols and, in particular, the per-party communication complexity is of primary interest since nodes in a network are often subject to bandwidth constraints. Previous flooding protocols incur a per-party communication complexity of\\(\\varOmega (l\\cdot \\gamma ^{-1} \\cdot (\\log (n) + \\kappa ))\\)bits to disseminate anl-bit message amongnparties with security parameter\\(\\kappa \\)when it is guaranteed that a\\(\\gamma \\)fraction of the parties remain honest. In this work, we present the first flooding protocols with a per-party communication complexity of\\(O(l\\cdot \\gamma ^{-1})\\)bits. We further show that this is asymptotically optimal and that our protocols can be instantiated provably securely in the usual setting for proof-of-stake blockchains. To demonstrate that one of our new protocols is not only asymptotically optimal but also practical, we perform several probabilistic simulations to estimate the concrete complexity for given parameters. Our simulations show that our protocol significantly improves the per-party communication complexity over the state-of-the-art for practical parameters. Hence, for given bandwidth constraints, our results allow to, e.g., increase the block size, improving the overall throughput of a blockchain.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_3"}, {"primary_key": "582763", "vector": [], "sparse_vector": [], "title": "Early Stopping for Any Number of Corruptions.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Minimizing the round complexity of byzantine broadcast is a fundamental question in distributed computing and cryptography. In this work, we present the firstearly stoppingbyzantine broadcast protocol that tolerates up to\\(t=n-1\\)malicious corruptions and terminates in\\(\\mathcal {O}(\\min \\{f^2,t+1\\})\\)rounds for any execution with\\(f\\le t\\)actual corruptions. Our protocol is deterministic, adaptively secure, and works assuming a plain public key infrastructure. Prior early-stopping protocols all either require honest majority or tolerate only up to\\(t=(1-\\epsilon )n\\)malicious corruptions while requiring either trusted setup or strong number theoretic hardness assumptions. As our key contribution, we show a novel tool called apolariserthat allows us to transfer certificate-based strategies from the honest majority setting to settings with a dishonest majority.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_16"}, {"primary_key": "582764", "vector": [], "sparse_vector": [], "title": "Witness Se<PERSON>tic Security.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "To date, the strongest notions of security achievable for two-roundpublicly-verifiablecryptographic proofs for\\(\\textsf{NP}\\)are witness indistinguishability (Dwork-Naor 2000, <PERSON><PERSON><PERSON> 2006), witness hiding (Bitansky-Khurana-Paneth 2019, Kuykendall-Zhandry 2020), and super-polynomial simulation (Pass 2003, Khurana<PERSON> 2017). On the other hand, zero-knowledge and even weak zero-knowledge (<PERSON><PERSON><PERSON><PERSON><PERSON> 1999) are impossible in the two-round publicly-verifiable setting (Goldreich<PERSON> 1994). This leaves an enormous gap in our theoretical understanding of known achievable security and the impossibility results for two-round publicly-verifiable cryptographic proofs for\\(\\textsf{NP}\\). Towards filling this gap, we propose a new and natural notion of security, calledwitness semantic security, that captures the natural and strong notion that an adversary should not be able to learn any partial information about the prover’s witness beyond what it could learn given only the statementx. Not only does our notion of witness semantic security subsume both witness indistinguishability and witness hiding, but it also has an easily appreciable interpretation. Moreover, we show that assuming the subexponential hardness of LWE, there exists a two-round public-coin publicly-verifiable witness semantic secure argument. To our knowledge, this is the strongest form of security known for this setting. As a key application of our work, we show that non-interactive zero-knowledge (NIZK) arguments in the common reference string (CRS) model can additionally maintain witness semantic security even when the CRS is maliciously generated. Our work gives the first construction from (subexponential) standard assumptions that achieves a notion stronger than witness-indistinguishability against a malicious CRS authority. In order to achieve our results, we give the first construction of a ZAP from subexponential LWE that is adaptively sound. Additionally, we propose a notion of simulation using non-uniform advice about a malicious CRS, which we also believe will be of independent interest.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58740-5_6"}, {"primary_key": "582765", "vector": [], "sparse_vector": [], "title": "Universal Composable Password Authenticated Key Exchange for the Post-Quantum World.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we construct thefirstpassword authenticated key exchange (PAKE) scheme from isogenies with Universal Composable (UC) security in the random oracle model (ROM). We also construct thefirsttwo PAKE schemes with UC security in the quantum random oracle model (QROM), one is based on the learning with error (LWE) assumption, and the other is based on the group-action decisional <PERSON><PERSON><PERSON><PERSON> (GA-DDH) assumption in the isogeny setting. To obtain our UC-secure PAKE scheme in ROM, we propose a generic construction of PAKE from basic lossy public key encryption (LPKE) and CCA-secure PKE. We also introduce a new variant of LPKE, named extractable LPKE (eLPKE). By replacing the basic LPKE with eLPKE, our generic construction of PAKE achieves UC security in QROM. The LPKE and eLPKE have instantiations not only from LWE but also from GA-DDH, which admit four specific PAKE schemes with UC security in ROM or QROM, based on LWE or GA-DDH.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_5"}, {"primary_key": "582766", "vector": [], "sparse_vector": [], "title": "Accelerating BGV Bootstrapping for Large p Using Null Polynomials over $\\mathbb {Z}_{pe}$.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The BGV scheme is one of the most popular FHE schemes for computing homomorphic integer arithmetic. The bootstrapping technique of BGV is necessary to evaluate arbitrarily deep circuits homomorphically. However, the BGV bootstrapping performs poorly for large plaintext primepdue to its digit removal procedure exhibiting a computational complexity of at least\\(O(\\sqrt{p})\\). In this paper, we propose optimizations for the digit removal procedure with largepby leveraging the properties of null polynomials over the ring\\(\\mathbb {Z}_{p^e}\\). Specifically, we demonstrate that it is possible to construct low-degree null polynomials based on two observations of the input to the digit removal procedure: 1) the support size of the input can be upper-bounded by\\((2B+1)^2\\); 2) the size of the lower digits to be removed can be upper-bounded byB. HereBcan be controlled within a narrow interval [22, 23] in our parameter selection, making the degree of these null polynomials much smaller thanpfor large values ofp. These low-degree null polynomials can significantly reduce the polynomial degrees during homomorphic digit removal, thereby decreasing both running time and capacity consumption. Theoretically, our optimizations reduce the computational cost of extracting a single digit from\\(O(\\sqrt{pe})\\)(by <PERSON> and <PERSON>) or\\(O(\\sqrt{p}\\root 4 \\of {e})\\)(by <PERSON><PERSON> et al.) to\\(\\min (2B+1,\\sqrt{\\lceil e/t\\rceil (2B+1)})\\)for some\\(t\\ge 1\\). We implement and benchmark our method on HElib with\\(p=17,127,257,8191\\)and 65537 (The code is available athttps://github.com/msh086/BGV-Boot-for-Large-p). With our optimized digit removal, we achieve a bootstrapping throughput\\(1.38\\sim 151\\)times that in HElib, with the speedup increasing with the value ofp. For\\(p=65537\\), we accelerate the digit removal step by 80 times and reduce the bootstrapping time from more than 12 h to less than 14 min.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_14"}, {"primary_key": "582767", "vector": [], "sparse_vector": [], "title": "Constructing Leakage-Resilient <PERSON><PERSON><PERSON>&<PERSON>po<PERSON>;s Secret Sharing: Over Composite Order Fields.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Probing physical bits in hardware has compromised cryptographic systems. This work investigates how to instantiate <PERSON><PERSON><PERSON>’s secret sharing so that the physical probes into its shares reveal statistically insignificant information about the secret. Over prime fields, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (EUROCRYPT 2021) proved that choosing random evaluation places achieves this objective with high probability. Our work extends their randomized construction to composite order fields – particularly for fields with characteristic 2. Next, this work presents an algorithm to classify evaluation places as secure or vulnerable against physical-bit probes for some specific cases. Our security analysis of the randomized construction is Fourier-analytic, and the classification techniques are combinatorial. Our analysis relies on (1) contemporary Bézout-theorem-type algebraic complexity results that bound the number of simultaneous zeroes of a system of polynomial equations over composite order fields and (2) characterization of the zeroes of an appropriate generalized Vandermonde determinant.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_11"}, {"primary_key": "582768", "vector": [], "sparse_vector": [], "title": "Fully Homomorphic Encryption Beyond IND-CCA1 Security: Integrity Through Verifiability.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We focus on the problem of constructing fully homomorphic encryption (FHE) schemes that achieve some meaningful notion of adaptive chosen-ciphertext security beyond\\(\\textrm{CCA1}\\). Towards this, we propose a new notion, called security againstverified chosen-ciphertext attack(\\(\\textrm{vCCA}\\)). The idea behind it is to ascertain integrity of the ciphertext by imposing a strong control on the evaluation algorithm. Essentially, we require that a ciphertext obtained by the use of homomorphic evaluation must be “linked” to the original input ciphertexts. We formalize the\\(\\textrm{vCCA}\\)notion in two equivalent formulations; the first is in the indistinguishability paradigm, the second follows the non-malleability simulation-based approach, and is a generalization of the targeted malleability introduced by <PERSON> et al. in 2012. We strengthen the credibility of our definitions by exploring relations to existing security notions for homomorphic encryption schemes, namely\\(\\textrm{CCA1}\\),\\(\\textrm{RCCA}\\),\\(\\textrm{FuncCPA}\\),\\(\\textrm{CCVA}\\), and\\(\\textrm{HCCA}\\). We prove that\\(\\textrm{vCCA}\\)security is the strongest notion known so far, that can be achieved by an FHE scheme; in particular,\\(\\textrm{vCCA}\\)is strictly stronger than\\(\\textrm{CCA1}\\). Finally, we provide a general transformation, that takesany\\(\\textrm{CPA}\\)-secure FHE scheme and makes it\\(\\textrm{vCCA}\\)-secure. Our transformation first turns an FHE scheme into a\\(\\textrm{CCA2}\\)-secure scheme where a part of the ciphertext retains the homomorphic properties and then extends it with a succinct non-interactive argument of knowledge (SNARK) to verifiably control the evaluation algorithm. In fact, we obtainfourgeneral variations of this transformation. We handle both the asymmetric and the symmetric key FHE schemes, and for each we give two variations differing in whether the ciphertext integrity can be verified publicly or requires the secret key. We use well-known techniques to achieve\\(\\textrm{CCA2}\\)security in the first step of our transformation. In the asymmetric case, we use the double encryption paradigm, and in the symmetric case, we use Encrypt-then-MAC techniques. Furthermore, our transformation also gives thefirst\\(\\textrm{CCA1}\\)-secure FHE scheme based onbootstrappingtechniques.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_3"}, {"primary_key": "582769", "vector": [], "sparse_vector": [], "title": "Strong Batching for Non-interactive Statistical Zero-Knowledge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A zero-knowledge proof enables a prover to convince a verifier that\\(x \\in S\\), without revealing anything beyond this fact. By running a zero-knowledge proofktimes, it is possible to prove (still in zero-knowledge) thatkseparate instances\\(x_1,\\dots ,x_k\\)are all inS. However, this increases the communication by a factor ofk. Can one do better? In other words, is (non-trivial) zero-knowledgebatch verificationforSpossible? Recent works by <PERSON><PERSON><PERSON> et al. (TCC 2020, Eurocrypt 2021) show that any problem possessing anon-interactive statisticalzero-knowledge proof (\\(\\textbf{NISZK}\\)) has a non-trivial statistical zero-knowledge batch verification protocol. Their results had two major limitations: (1) to batch verifykinputs of sizeneach, the communication in their batch protocol is roughly\\(\\textrm{poly}(n,\\log {k})+O(k)\\), which is better than the naive cost of\\(k \\cdot \\textrm{poly}(n)\\)but still scales linearly withk, and, (2) the batch protocol requires\\(\\varOmega (k)\\)rounds of interaction. In this work we remove both of these limitations by showing that any problem in\\(\\textbf{NISZK}\\)has anon-interactivestatistical zero-knowledge batch verification protocol with communication\\(\\textrm{poly}(n,\\log {k})\\).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_9"}, {"primary_key": "582770", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of Rank-2 Module-LIP in Totally Real Number Fields.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We formally define the La<PERSON>ce Isomorphism Problem for module lattices (module-LIP) in a number fieldK. This is a generalization of the problem defined by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (Asiacrypt 2022), taking into account the arithmetic and algebraic specificity of module lattices from their representation using pseudo-bases. We also provide the corresponding set of algorithmic and theoretical tools for the future study of this problem in a module setting. Our main contribution is an algorithm solving module-LIP for modules of rank 2 in\\(K^2\\), whenKis a totally real number field. Our algorithm exploits the connection between this problem, relative norm equations and the decomposition of algebraic integers as sums of two squares. For a large class of modules (including\\(\\mathcal {O}_K^2\\)), and a large class of totally real number fields (including the maximal real subfield of cyclotomic fields) it runs in classical polynomial time in the degree of the field and the residue at 1 of the Dedekind zeta function of the field (under reasonable number theoretic assumptions). We provide a proof-of-concept code running over the maximal real subfield of cyclotomic fields.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_9"}, {"primary_key": "582771", "vector": [], "sparse_vector": [], "title": "The Exact Multi-user Security of (Tweakable) Key Alternating Ciphers with a Single Permutation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove the tight multi-user (mu) security of the (tweakable) key alternating cipher (KAC) for any roundrwith a single permutation andr-wise independent subkeys, providing a more realistic provable-security foundation for block ciphers. After <PERSON> and <PERSON> proved the single-user (su) tight security bound ofr-round KAC in 2014, its extension under more realistic conditions has become a new research challenge. The state-of-the-art includes (i) single permutation by <PERSON> et al., (ii) the mu security by <PERSON><PERSON> and <PERSON><PERSON>, and (iii) correlated subkeys by <PERSON><PERSON> and <PERSON>. However, the previous works considered these conditions independently, and the tight security bound ofr-round KACs with all of these conditions is an open research problem. We address it by giving the new mu-bound with ann-bit message space, approximately\\(q \\cdot \\left( \\frac{p + r q}{2^n} \\right) ^r\\), whereinpandqare the number of primitive and construction queries, respectively. The bound ensures the security up to the\\(O(2^\\frac{rn}{r+1})\\)query complexity and is tight, matching the conventional attack bound. Moreover, our result easily extends to ther-round tweakable KAC when its subkeys generated by a tweak function isr-wise independent. The proof is based on the re-sampling method originally proposed for the mu-security analysis of the triple encryption. Its extension to any rounds is the core technique enabling the new bound.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_4"}, {"primary_key": "582772", "vector": [], "sparse_vector": [], "title": "Algorithms for Matrix Code and Alternating Trilinear Form Equivalences via New Isomorphism Invariants.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We devise algorithms for finding equivalences of trilinear forms over finite fields modulo linear group actions. Our focus is on two problems under this umbrella,Matrix Code Equivalence(MCE) andAlternating Trilinear Form Equivalence(ATFE), since their hardness is the foundation of the NIST round-1 signature candidatesMEDSandALTEQrespectively. We present new algorithms forMCEandATFE, which are further developments of the algorithms for polynomial isomorphism and alternating trilinear form equivalence, in particular by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (Eurocrypt2013), and <PERSON><PERSON><PERSON> (Crypto2023). Key ingredients in these algorithms are new easy-to-compute distinguishing invariants under the respective group actions. ForMCE, we associate new isomorphism invariants to corank-1 points of matrix codes, which lead to a birthday-type algorithm. We present empirical justifications that these isomorphism invariants are easy-to-compute and distinguishing, and provide an implementation of this algorithm. This algorithm has some implications to the security ofMEDS. The invariant function forATFEis similar, except it is associated with lower rank points. Mo<PERSON>lo certain assumptions on turning the invariant function into canonical forms, our algorithm forATFEimproves on the runtime of the previously best known algorithm of <PERSON><PERSON><PERSON> (Crypto2023). Finally, we present quantum variants of our classical algorithms with cubic runtime improvements.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_6"}, {"primary_key": "582773", "vector": [], "sparse_vector": [], "title": "The Supersingular Endomorphism Ring and One Endomorphism Problems are Equivalent.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The supersingular Endomorphism Ring problem is the following: given a supersingular elliptic curve, compute all of its endomorphisms. The presumed hardness of this problem is foundational for isogeny-based cryptography. The One Endomorphism problem only asks to find a single non-scalar endomorphism. We prove that these two problems are equivalent, under probabilistic polynomial time reductions. We prove a number of consequences. First, assuming the hardness of the endomorphism ring problem, the <PERSON> hash function is collision resistant, and the SQIsign identification protocol is sound for uniformly random keys. Second, the endomorphism ring problem is equivalent to the problem of computing arbitrary isogenies between supersingular elliptic curves, a result previously known only for isogenies of smooth degree. Third, there exists an unconditional probabilistic algorithm to solve the endomorphism ring problem in time\\(\\tilde{O}(p^{1/2})\\), a result that previously required to assume the generalized Riemann hypothesis. To prove our main result, we introduce a flexible framework for the study of isogeny graphs with additional information. We prove a general and easy-to-use rapid mixing theorem.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_14"}, {"primary_key": "582774", "vector": [], "sparse_vector": [], "title": "Key Exchange with Tight (Full) Forward Secrecy via Key Confirmation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zeng"], "summary": "Weak forward secrecy (wFS) of authenticated key exchange (AKE) protocols is a passive variant of (full) forward secrecy (FS). A natural mechanism to upgrade from wFS to FS is the use of key confirmation messages which compute a message authentication code (MAC) over the transcript. Unfortunately, <PERSON><PERSON><PERSON>, <PERSON>j<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (GGJJ, CRYPTO 2023) show that this mechanism inherently incurs a loss proportional to the number of users, leading to an overall non-tight reduction, even if wFS was established using a tight reduction. Inspired by GGJJ, we propose a new notion, called one-way verifiable weak forward secrecy (OW-VwFS), and prove that OW-VwFS can be transformedtightlyto FS using key confirmation in the random oracle model (ROM). To implement our generic transformation, we show that several tightly wFS AKE protocols additionally satisfy our OW-VwFS notion tightly. We highlight that using the recent lattice-based protocol from Pan, Wagner, and Zeng (CRYPTO 2023) can give us the first lattice-based tightly FS AKE via key confirmation in the classical random oracle model. Besides this, we also obtain a Decisional-Diffie-Hellman-based protocol that is considerably more efficient than the previous ones. Finally, we lift our study on FS via key confirmation to the quantum random oracle model (QROM). While our security reduction is overall non-tight, it matches the best existing bound for wFS in the QROM (<PERSON>, <PERSON>, and <PERSON>, ASIACRYPT 2023), namely, it is square-root- and session-tight. Our analysis is in the multi-challenge setting, and it is more realistic than the single-challenge setting as in Pan et al.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_3"}, {"primary_key": "582775", "vector": [], "sparse_vector": [], "title": "Toothpicks: More Efficient Fork-Free Two-Round Multi-signatures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Tightly secure cryptographic schemes can be implemented with standardized parameters, while still having a sufficiently high security level backed up by their analysis. In a recent work, <PERSON> and <PERSON> (Eurocrypt 2023) presented the first tightly secure two-round multi-signature scheme without pairings, called Chopsticks. While this is an interesting first theoretical step, Chopsticks is much less efficient than its non-tight counterparts. In this work, we close this gap by proposing a new tightly secure two-round multi-signature scheme that is as efficient as non-tight schemes. Our scheme is based on the\\(\\textsf{DDH}\\)assumption without pairings. Compared to Chopsticks, we reduce the signature size by more than a factor of 3 and the communication complexity by more than a factor of 2. Technically, we achieve this as follows: (1) We develop a new pseudorandom path technique, as opposed to the pseudorandom matching technique in Chopsticks. (2) We construct a more efficient commitment scheme with suitable properties, which is an important primitive in both our scheme and Cho<PERSON>ticks. Surprisingly, we observe that the commitment scheme does not have to be binding, enabling our efficient construction.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_16"}, {"primary_key": "582776", "vector": [], "sparse_vector": [], "title": "Threshold Raccoon: Practical Threshold Signatures from Standard Lattice Assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Threshold signatures improve both availability and security of digital signatures by splitting the signing key intoNshares handed out to different parties. Later on, any subset of at leastTparties can cooperate to produce a signature on a given message. While threshold signatures have been extensively studied in the pre-quantum setting, they remain sparse from quantum-resilient assumptions. We present the first efficient lattice-based threshold signatures with signature size 13 KiB and communication cost 40 KiB per user, supporting a threshold size as large as 1024 signers. We provide an accompanying high performance implementation. The security of the scheme is based on the same assumptions as Dilithium, a signature recently selected by NIST for standardisation which, as far as we know, cannot easily be made threshold efficiently. All operations used during signing are due to symmetric primitives and simple lattice operations; in particular our scheme does not need heavy tools such as threshold fully homomorphic encryption or homomorphic trapdoor commitments as in prior constructions. The key technical idea is to useone-time additive masksto mitigate the leakage of the partial signing keys through partial signatures.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_8"}, {"primary_key": "582777", "vector": [], "sparse_vector": [], "title": "Provable Dual Attacks on Learning with Errors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Learning with Errors (LWE) is an important problem for post-quantum cryptography (PQC) that underlines the security of several NIST PQC selected algorithms. Several recent papers [7,25,16,32] have claimed improvements on the complexity of so-called dual attacks on LWE. These improvements make dual attacks comparable to or even better than primal attacks in certain parameter regimes. Unfortunately, those improvements rely on a number of untested and hard-to-test statistical assumptions. Furthermore, a recent paper [20] claims that the whole premise of those improvements might be incorrect. The goal of this paper is to improve the situation by proving the correctness of a dual attack without relying on any statistical assumption. Although our attack is greatly simplified compared to the recent ones, it shares many important technical elements with those attacks and can serve as a basis for the analysis of more advanced attacks. We provide some rough estimates on the complexity of our simplified attack on Kyber using a Monte Carlo Markov Chain discrete Gaussian sampler. Our main contribution is to clearly identify a set of parameters under which our attack (and presumably other recent dual attacks) can work. Furthermore, our analysis completely departs from the existing statistics-based analysis and is instead rooted in geometry. We also compare the regime in which our algorithm works to the “contradictory regime” of [20]. We observe that those two regimes are essentially complementary. Finally, we give a quantum version of our algorithm to speed up the computation. The algorithm is inspired by [10] but is completely formal and does not rely on any heuristics.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_10"}, {"primary_key": "582778", "vector": [], "sparse_vector": [], "title": "AprèsSQI: Extra Fast Verification for SQIsign Using Extension-Field Signing.", "authors": ["Maria <PERSON>-Real Santos", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We optimise the verification of the SQIsign signature scheme. By using field extensions in the signing procedure, we are able to significantly increase the amount of available rational 2-power torsion in verification, which achieves a significant speed-up. This, moreover, allows several other speed-ups on the level of curve arithmetic. We show that the synergy between these high-level and low-level improvements gives significant improvements, making verification 2.07 times faster, or up to 3.41 times when using size-speed trade-offs, compared to the state of the art, without majorly degrading the performance of signing.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_3"}, {"primary_key": "582779", "vector": [], "sparse_vector": [], "title": "New Limits of Provable Security and Applications to ElGamal Encryption.", "authors": ["<PERSON>"], "summary": "We provide new results showing that ElGamal encryption cannot be proven CCA1-secure – a long-standing open problem in cryptography. Our result follows from a very broad, meta-reduction-based impossibility result on random self-reducible relations with efficiently re-randomizable witnesses. The techniques that we develop allow, for the first time, to provide impossibility results for very weak security notions where the challenger outputs fresh challenge statements at the end of the security game. This can be used to finally tackle encryption-type definitions that have remained elusive in the past. We show that our results have broad applicability by casting several known cryptographic setups as instances of random self-reducible and re-randomizable relations. These setups include general semi-homomorphic PKE and the large class of certified homomorphic one-way bijections. As a result, we also obtain new impossibility results for the IND-CCA1 security of the PKEs of Paillier and Damgård–Jurik, and many one-more inversion assumptions like the one-more DLOG or the one-more RSA assumption.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58737-5_10"}, {"primary_key": "582780", "vector": [], "sparse_vector": [], "title": "Unlocking the Lookup Singularity with <PERSON><PERSON>.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Riad S. Wahby"], "summary": "This paper introducesLasso, a new family of lookup arguments, which allow an untrusted prover to commit to a vector\\(a \\in \\mathbb {F}^m\\)and prove that all entries ofareside in some predetermined table\\(t \\in \\mathbb {F}^n\\).<PERSON><PERSON>’s performance characteristics unlock the so-called “lookup singularity”.<PERSON><PERSON>works with any multilinear polynomial commitment scheme, and provides the following efficiency properties. Formlookups into a table of sizen,<PERSON><PERSON>’s prover commits to just\\(m+n\\)field elements. Moreover, the committed field elements aresmall, meaning that, no matter how big the field\\(\\mathbb {F}\\)is, they are all in the set\\(\\{0, \\dots , m\\}\\). When using a multiexponentiation-based commitment scheme, this results in the prover’s costs dominated by only\\(O(m+n)\\)groupoperations(e.g., elliptic curve point additions), plus the cost to prove an evaluation of a multilinear polynomial whose evaluations over the Boolean hypercube are the table entries. This represents a significant improvement in prover costs over prior lookup arguments (e.g., plookup, Halo2’s lookups, logUp). Unlike all prior lookup arguments, if the tabletis structured (in a precise sense that we define), then no party needs to commit tot, enabling the use of much larger tables than prior works (e.g., of size\\(2^{128}\\)or larger). Moreover,<PERSON><PERSON>’s prover only “pays” in runtime for table entries that are accessed by the lookup operations. This applies to tables commonly used to implement range checks, bitwise operations, big-number arithmetic, and even transitions of a full-fledged CPU such as RISC-V. Specifically, for any integer parameter\\(c>1\\),Lasso’s prover’s dominant cost is committing to\\(3 \\cdot c \\cdot m + c \\cdot n^{1/c}\\)field elements. Furthermore, all these field elements are “small”, meaning they are in the set\\(\\{0, \\dots , \\max \\{m, n^{1/c}, q\\}-1\\}\\), whereqis the maximum value in any of the sub-tables that collectively capturet(in a precise manner that we define).", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58751-1_7"}, {"primary_key": "582781", "vector": [], "sparse_vector": [], "title": "Probabilistic Extensions: A One-Step Framework for Finding Rectangle Attacks and Beyond.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In differential-like attacks, the process typically involves extending a distinguisher forward and backward with probability 1 for some rounds and recovering the key involved in the extended part. Particularly in rectangle attacks, a holistic key recovery strategy can be employed to yield the most efficient attacks tailored to a given distinguisher. In this paper, we treat the distinguisher and the extended part as an integrated entity and give a one-step framework for finding rectangle attacks with the purpose of reducing the overall complexity or attacking more rounds. In this framework, we propose to allow probabilistic differential propagations in the extended part and incorporate the holistic recovery strategy. Additionally, we introduce the “split-and-bunch technique” to further reduce the time complexity. Beyond rectangle attacks, we extend these foundational concepts to encompass differential attacks as well. To demonstrate the efficiency of our framework, we apply it toDeoxys-BC-384,SKINNY,ForkSkinny, andCRAFT, achieving a series of refined and improved rectangle attacks and differential attacks. Notably, we obtain the first 15-round attack onDeoxys-BC-384, narrowing its security margin to only one round. Furthermore, our differential attack onCRAFTextends to 23 rounds, covering two more rounds than the previous best attacks.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58716-0_12"}, {"primary_key": "582782", "vector": [], "sparse_vector": [], "title": "The Complexity of Algebraic Algorithms for LWE.", "authors": ["<PERSON>"], "summary": "Arora & Ge introduced a noise-free polynomial system to compute the secret of a Learning With Errors (LWE) instance via linearization. <PERSON><PERSON> et al. later utilized the Arora-Ge polynomial model to study the complexity of Gröbner basis computations on LWE polynomial systems under the assumption of semi-regularity. In this paper we revisit the Arora-Ge polynomial and prove that it satisfies a genericity condition recently introduced by Caminata & Gorla, called being in generic coordinates. For polynomial systems in generic coordinates one can always estimate the complexity of DRL Gröbner basis computations in terms of the Castelnuovo-Mumford regularity and henceforth also via the Macaulay bound. Moreover, we generalize the Grö<PERSON> basis algorithm of <PERSON><PERSON><PERSON> & <PERSON>ti to arbitrary polynomial systems with a finite degree of regularity. In particular, existence of this algorithm yields another approach to estimate the complexity of DRL Gröbner basis computations in terms of the degree of regularity. In practice, the degree of regularity of LWE polynomial systems is not known, though one can always estimate the lowest achievable degree of regularity. Consequently, from a designer’s worst case perspective this approach yields sub-exponential complexity estimates for general, binary secret and binary error LWE. In recent works by <PERSON><PERSON><PERSON><PERSON> et al. the hardness of LWE in the presence of side information was analyzed. Utilizing their framework we discuss how hints can be incorporated into LWE polynomial systems and how they affect the complexity of <PERSON><PERSON><PERSON><PERSON> basis computations.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58734-4_13"}, {"primary_key": "582783", "vector": [], "sparse_vector": [], "title": "Lower Bounds for Lattice-Based Compact Functional Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Functional encryption (FE) is a primitive where the holder of a master secret key can control which functions a user can evaluate on encrypted data. It is a powerful primitive that even implies indistinguishability obfuscation (iO), given sufficiently compact ciphertexts (<PERSON><PERSON><PERSON><PERSON><PERSON>, CRYPTO’15 and <PERSON><PERSON><PERSON><PERSON><PERSON>, FOCS’15). However, despite being extensively studied, there are FE schemes, such as function-hiding inner-product FE (<PERSON><PERSON>, AC’15, Abdalla-Catalano-Fiore-Gay-Ursu, CRYPTO’18) and compact quadratic FE (Baltico-Catalan<PERSON><PERSON><PERSON><PERSON>-<PERSON>, <PERSON>, CRYPTO’17), that can be only realized using pairings. This raises the question if there are some mathematical barriers that hinder us from realizing these FE schemes from other assumptions. In this paper, we study the difficulty of constructing lattice-based compact FE. We generalize the impossibility results of Ünal (EC’20) for lattice-based function-hiding FE, and extend it to the case of compact FE. Concretely, we prove lower bounds for lattice-based compact FE schemes which meet some (natural) algebraic restrictions at encryption and decryption, and have ciphertexts of linear size and secret keys of minimal degree. We see our results as important indications of why it is hard to construct lattice-based FE schemes for new functionalities, and which mathematical barriers have to be overcome.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_9"}, {"primary_key": "582784", "vector": [], "sparse_vector": [], "title": "Circuit Bootstrapping: Faster and Smaller.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a novel circuit bootstrapping algorithm that outperforms the state-of-the-art TFHE method with 9.9\\(\\times \\)speedup and 15.6\\(\\times \\)key size reduction. These improvements can be attributed to two technical contributions. Firstly, we redesigned the circuit bootstrapping workflow to operate exclusively under the ring ciphertext type, which eliminates the need for conversion between LWE and RLWE ciphertexts. Secondly, we improve the LMKC+ blind rotation algorithm by reducing the number of automorphisms, then propose the first automorphism type multi-value functional bootstrapping. These automorphism-based techniques lead to further key size optimization, and are of independent interest besides circuit bootstrapping. Based on our new circuit bootstrapping we can evaluate AES-128 in 26.2 s (single thread), achieving 10.3\\(\\times \\)speedup compared with the state-of-the-art TFHE-based approach.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_12"}, {"primary_key": "582785", "vector": [], "sparse_vector": [], "title": "Succinct Functional Commitments for Circuits from k-sfLin.", "authors": ["<PERSON><PERSON><PERSON>e", "<PERSON>"], "summary": "A functional commitment allows a user to commit to an input\\(\\textbf{x}\\)and later, open the commitment to an arbitrary function\\(\\textbf{y}= f(\\textbf{x})\\). The size of the commitment and the opening should be sublinear in\\(\\left| \\textbf{x} \\right| \\)and\\(\\left| f \\right| \\). In this work, we give the first pairing-based functional commitment for arbitrary circuits where the size of the commitmentandthe size of the opening consist of aconstantnumber of group elements. Security relies on the standard bilateralk-\\(\\textsf{Lin}\\)assumption. This is the first scheme with this level of succinctness from falsifiable bilinear map assumptions (previous approaches required SNARKs for\\(\\textsf{NP} \\)). This is also the first functional commitment scheme for general circuits with\\(\\textsf{poly}(\\lambda )\\)-size commitments and openings fromanyassumption that makes fully black-box use of cryptographic primitives and algorithms. Our construction relies on a new notion of projective chainable commitments which may be of independent interest.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_10"}, {"primary_key": "582786", "vector": [], "sparse_vector": [], "title": "Signatures with Memory-Tight Security in the Quantum Random Oracle Model.", "authors": ["<PERSON><PERSON>"], "summary": "Memory tightness of reductions in cryptography, in addition to the standard tightness related to advantage and running time, is important when the underlying problem can be solved efficiently with large memory, as discussed in <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> (CRYPTO 2017). <PERSON><PERSON><PERSON>, Gellert, Jager, and Lyu (ASIACRYPT 2021) and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> (EUROCRYPT 2022) gave memory-tight proofs for the multi-challenge security of digital signatures in the random oracle model. This paper studies the memory-tight reductions forpost-quantumsignature schemes in thequantumrandom oracle model. Concretely, we show that signature schemes from lossy identification are multi-challenge secure in the quantum random oracle model via memory-tight reductions. Moreover, we show that the signature schemes from lossy identification achieve more enhanced securities consideringquantumsigning oracles proposed by Boneh and Zhandry (CRYPTO 2013) and Alagic, Majenz, Russel, and Song (EUROCRYPT 2020). We additionally show that signature schemes from preimage-sampleable functions achieve those securities via memory-tight reductions.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58754-2_2"}, {"primary_key": "582787", "vector": [], "sparse_vector": [], "title": "Registered Functional Encryptions from Pairings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Junqing Gong", "Hai<PERSON> Qian"], "summary": "This work initiates the study ofconcreteregistered functional encryption (Reg-FE) beyond “all-or-nothing” functionalities: We build the first Reg-FE for linear function or inner-product evaluation (Reg-IPFE) from pairings. The scheme achieves adaptive IND-security underk-Lin assumption in the prime-order bilinear group. A minor modification yields the first Registered Inner-Product Encryption (Reg-IPE) scheme fromk-Lin assumption. Prior work achieves the same security in the generic group model. We build the first Reg-FE for quadratic function (Reg-QFE) from pairing. The scheme achievesvery selectivesimulation-based security (SIM-security) under bilateralk-Lin assumption in the prime-order bilinear group. Here, “very selective” means that the adversary claims challenge messages, all quadratic functions to be registered and all corrupted users at the beginning. Besides focusing on the compactness of the master public key and helper keys, we also aim for compact ciphertexts in Reg-FE. LetLbe the number of slots andnbe the input size. Our first Reg-IPFE hasweakly compactciphertexts of size\\(O(n\\cdot \\log L)\\)while our second Reg-QFE hascompactciphertexts of size\\(O(n+\\log L)\\). Technically, for our first Reg-IPFE, we employnesteddual-system method within the context of Reg-IPFE; for our second Reg-QFE, we follow <PERSON><PERSON>’s “IPFE-to-QFE” transformation [TCC’ 20] but devise a set of new techniques that make ourpairing-basedReg-IPFE compatible. Along the way, we introduce a new notion namedPre-Constrained Registered IPFEwhich generalizes slotted Reg-IPFE by constraining the form of functions that can be registered.", "published": "2024-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-031-58723-8_13"}]