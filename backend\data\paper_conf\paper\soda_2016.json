[{"primary_key": "4189772", "vector": [], "sparse_vector": [], "title": "Designing Networks with Good Equilibria under Uncertainty.", "authors": ["<PERSON>", "Alk<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Designing Networks with Good Equilibria under Uncertainty<PERSON><PERSON><PERSON> and <PERSON>k<PERSON>gouritsaG<PERSON><PERSON> and Alk<PERSON> Sgouritsapp.72 - 89Chapter DOI:https://doi.org/10.1137/1.*************.ch6PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the problem of designing network cost-sharing protocols with good equilibria under uncertainty. The underlying game is a multicast game in a rooted undirected graph with nonnegative edge costs. A set of k terminal vertices or players need to establish connectivity with the root. The social optimum is the Minimum Steiner Tree. We are interested in situations where the designer has incomplete information about the input. We propose two different models, the adversarial and the stochastic. In both models, the designer has prior knowledge of the underlying metric but the requested subset of the players is not known and is activated either in an adversarial manner (adversarial model) or is drawn from a known probability distribution (stochastic model). In the adversarial model, the goal of the designer is to choose a single, universal cost-sharing protocol that has low Price of Anarchy (PoA) for all possible requested subsets of players. The main question we address is: to what extent can prior knowledge of the underlying metric help in the design? We first demonstrate that there exist classes of graphs where knowledge of the underlying metric can dramatically improve the performance of good network cost-sharing design. For outerplanar graph metrics, we provide a universal cost-sharing protocol with constant PoA, in contrast to protocols that, by ignoring the graph metric, cannot achieve PoA better than Ω(log k). Then, in our main technical result, we show that there exist graph metrics, for which knowing the underlying metric does not help and any universal protocol has PoA of Ω(log k), which is tight. We attack this problem by developing new techniques that employ powerful tools from extremal combinatorics, and more specifically Ramsey Theory in high dimensional hypercubes. Then we switch to the stochastic model, where each player is independently activated according to some probability distribution that is known to the designer. We show that there exists a randomized ordered protocol that achieves constant PoA. By using standard derandomization techniques, we produce a deterministic ordered protocol that achieves constant PoA. We remark, that the first result holds also for the black-box model, where the probabilities are not known to the designer, but is allowed to draw independent (polynomially many) samples. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH6"}, {"primary_key": "4189773", "vector": [], "sparse_vector": [], "title": "Error Amplification for Pairwise Spanner Lower Bounds.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A pairwise spanner of a graph G = (V, E) and a \"pair set\" P ⊆ V × V is a subgraph H that preserves all pairwise distances in P, up to some additive error term +β. When β = 0 the object is called a pairwise distance preserver.A large and growing body of work has considered upper bounds for these objects, but lower bounds have been elusive. The only known lower bound results are (1) <PERSON><PERSON> and <PERSON><PERSON> (SODA'05) against preservers, and (2) considerably weaker bounds by <PERSON><PERSON> (FOCS'06) against spanners.Our main result is an amplification theorem: we prove that lower bounds against pairwise distance preservers imply lower bounds against pairwise spanners. In other words, to prove lower bounds against any constant error spanners, it is enough to consider only subgraphs that are not allowed any error at all!We apply this theorem to obtain drastically improved lower bounds. Some of these include:•Linear size pairwise spanners with up to +(2k – 1) error cannot span |P| = ω(n(1+k)/(3+k)) pairs. This is a large improvement over <PERSON><PERSON>'s |P| = ω(n2–2/k) (|P| is now linear, rather than quadratic, as k gets large).•|E(H)| = Ω(n1+1/k) edges are required for a +(2k – 1) spanner of |P| = Ω(n1+1/k) pairs – this is another large improvement over <PERSON><PERSON>'s |P| = Ω(n2).•The first tight bounds for pairwise spanners: for +2 error and P = ⊝(n3/2) we show that ⊝(n3/2) edges are necessary and sufficient (this also reflects a new upper bound: we construct +2 pairwise spanners on O(n|P|1/3) edges, removing a log factor from a prior algorithm).We also show analogous improved lower bounds against subset spanners (where P = S × S for some node subset S), and the first lower bounds against D threshold spanners (where P is the set of node pairs at distance at least D).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH60"}, {"primary_key": "4189774", "vector": [], "sparse_vector": [], "title": "Subtree Isomorphism Revisited.", "authors": ["<PERSON>", "Arturs Backurs", "<PERSON>", "Virginia Vassilevska Williams", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Subtree Isomorphism Revisi<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>.1256 - 1271Chapter DOI:https://doi.org/10.1137/1.*************.ch88PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The Subtree Isomorphism problem asks whether a given tree is contained in another given tree. The problem is of fundamental importance and has been studied since the 1960s. For some variants, e.g., ordered trees, near-linear time algorithms are known, but for the general case truly subquadratic algorithms remain elusive. Our first result is a reduction from the Orthogonal Vectors problem to Subtree Isomorphism, showing that a truly subquadratic algorithm for the latter refutes the Strong Exponential Time Hypothesis (SETH). In light of this conditional lower bound, we focus on natural special cases for which no truly subquadratic algorithms are known. We classify these cases against the quadratic barrier, showing in particular that: Even for binary, rooted trees, a truly subquadratic algorithm refutes SETH. Even for rooted trees of depth O (log log n), where n is the total number of vertices, a truly subquadratic algorithm refutes SETH. For every constant d, there is a constant ∊d > 0 and a randomized, truly subquadratic algorithm for degree-d rooted trees of depth at most (1 + ∊d)logdn. In particular, there is an O(min{2.85h, n2}) algorithm for binary trees of depth h. Our reductions utilize new “tree gadgets” that are likely useful for future SETH-based lower bounds for problems on trees. Our upper bounds apply a folklore result from randomized decision tree complexity. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH88"}, {"primary_key": "4189775", "vector": [], "sparse_vector": [], "title": "Approximation and Fixed Parameter Subquadratic Algorithms for Radius and Diameter in Sparse Graphs.", "authors": ["<PERSON>", "Virginia Vassilevska Williams", "<PERSON>"], "summary": "The radius and diameter are fundamental graph parameters, with several natural definitions for directed graphs. Each definition is well-motivated in a variety of applications. All versions of diameter and radius can be solved via solving all-pairs shortest paths (APSP), followed by a fast postprocessing step. However, solving APSP on n-node graphs requires Ω(n2) time even in sparse graphs.We study the question: when can diameter and radius in sparse graphs be solved in truly subquadratic time, and when is such an algorithm unlikely? Motivated by our conditional lower bounds on computing these measures exactly in truly subquadratic time, we search for approximation and fixed parameter subquadratic algorithms, and alternatively, for reasons why they do not exist.We find that:•Most versions of Diame<PERSON> and Radius can be solved in truly subquadratic time with optimal approximation guarantees, under plausible assumptions. For example, there is a 2-approximation algorithm for directed Radius with one-way distances that runs in time, while a (2 – δ)-approximation algorithm in O(n2–∊) time is considered unlikely.•On graphs with treewidth k, we can solve all versions in 2O(klogk)n1+O(1) time. We show that these algorithms are near optimal since even a (3/2 – δ)-approximation algorithm that runs in time 2o(k)n2–∊ would refute plausible assumptions.Two conceptual contributions of this work that we hope will incite future work are: the introduction of a Fixed Parameter Tractability in P framework, and the statement of a differently-quantified variant of the Orthogonal Vectors Conjecture, which we call the Hitting Set Conjecture.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH28"}, {"primary_key": "4189776", "vector": [], "sparse_vector": [], "title": "On Dynamic Approximate Shortest Paths for Planar Graphs with Worst-Case Costs.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Renato F. Werneck"], "summary": "Given a base weighted planar graph Ginput on n nodes and parameters M, ∊ we present a dynamic distance oracle with 1 + ∊ stretch and worst case update and query costs of ∊–3M4 · poly-log(n). We allow arbitrary edge weight updates as long as the shortest path metric induced by the updated graph has stretch of at most M relative to the shortest path metric of the base graph Ginput.For example, on a planar road network, we can support fast queries and dynamic traffic updates as long as the shortest path from any source to any target (including using arbitrary detours) is between, say, 80 and 3 miles-per-hour.As a warm-up we also prove that graphs of bounded treewidth have exact distance oracles in the dynamic edge model.To the best of our knowledge, this is the first dynamic distance oracle for a non-trivial family of dynamic changes to planar graphs with worst case costs of o(n1/2) both for query and for update operations.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH53"}, {"primary_key": "4189777", "vector": [], "sparse_vector": [], "title": "Bounds for Random Constraint Satisfaction Problems via Spatial Coupling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> L. Urbanke"], "summary": "We report on a novel technique called spatial coupling and its application in the analysis of random constraint satisfaction problems (CSP). Spatial coupling was invented as an engineering construction in the area of error correcting codes where it has resulted in efficient capacity-achieving codes for a wide range of channels. However, this technique is not limited to problems in communications, and can be applied in the much broader context of graphical models. We describe here a general methodology for applying spatial coupling to random constraint satisfaction problems and obtain lower bounds for their (rough) satisfiability threshold. The main idea is to construct a distribution of geometrically structured random K-SAT instances – namely the spatially coupled ensemble – which has the same (rough) satisfiability threshold, and is at the same time algorithmically easier to solve. Then by running well-known algorithms on the spatially coupled ensemble we obtain a lower bound on the (rough) satisfiability threshold of the original ensemble. The method is versatile because one can choose the CSP, there is a certain amount of freedom in the construction of the spatially coupled ensemble, and also in the choice of the algorithm. In this work we focus on random K-SAT but we have also checked that the method is successful for Coloring, NAE-SAT and XOR-SAT. We choose Unit Clause propagation for the algorithm which is analyzed over the spatially coupled instances. For K = 3, for instance, our lower bound is equal to 3.67 which is better than the current bounds in the literature. Similarly, for graph 3-colorability we get a bound of 2.22 which is also better than the current bounds in the literature.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH35"}, {"primary_key": "4189778", "vector": [], "sparse_vector": [], "title": "Focused Stochastic Local Search and the Lovász Local Lemma.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We develop tools for analyzing focused stochastic local search algorithms. These are algorithms that search a state space probabilistically by repeatedly selecting a constraint that is violated in the current state and moving to a random nearby state which, hopefully, addresses the violation without introducing many new ones. A large class of such algorithms arise from algorithmizations of the Lovász Local Lemma, a non-constructive tool for proving the existence of satisfying states. Here we give tools that provide a unified analysis of such algorithms and of many more, expressing them as instances of a general framework.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH141"}, {"primary_key": "4189779", "vector": [], "sparse_vector": [], "title": "An Efficient Algorithm for Computing High-Quality Paths amid Polygonal Obstacles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study a path-planning problem amid a set ℴ of obstacles in ℝ2, in which we wish to compute a short path between two points while also maintaining a high clearance from ℴ; the clearance of a point is its distance from a nearest obstacle in ℴ. Specifically, the problem asks for a path minimizing the reciprocal of the clearance integrated over the length of the path. We present the first polynomial-time approximation scheme for this problem. Let n be the total number of obstacle vertices and let ∊ ∊ (0, 1]. Our algorithm computes in time a path of total cost at most (1 + ∊) times the cost of the optimal path.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH82"}, {"primary_key": "4189780", "vector": [], "sparse_vector": [], "title": "Scheduling Parallel DAG Jobs Online to Minimize Average Flow Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we study the problem of scheduling parallelizable jobs online with an objective of minimizing average flow time. Each parallel job is modeled as a DAG where each node is a sequential task and each edge represents dependence between tasks. Previous work has focused on a model of parallelizability known as the arbitrary speed-up curves setting where a scalable algorithm is known. However, the DAG model is more widely used by practitioners, since many jobs generated from parallel programming languages and libraries can be represented in this model. However, little is known for this model in the online setting with multiple jobs. The DAG model and the speed-up curve models are incomparable and algorithmic results from one do not immediately imply results for the other. Previous work has left open the question of whether an online algorithm can be O(1)-competitive with O(1)-speed for average flow time in the DAG setting. In this work, we answer this question positively by giving a scalable algorithm which is (1 + ∊)-speed -competitive for any ∊ > 0. We further introduce the first greedy algorithm for scheduling parallelizable jobs — our algorithm is a generalization of the shortest jobs first algorithm. Greedy algorithms are among the most useful in practice due to their simplicity. We show that this algorithm is (2 + ∊)-speed -competitive for any ∊ > 0.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH14"}, {"primary_key": "4189781", "vector": [], "sparse_vector": [], "title": "Using Optimization to Obtain a Width-Independent, Parallel, Simpler, and Faster Positive SDP Solver.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the design of polylogarithmic depth algorithms for approximately solving packing and covering semidefinite programs (or positive SDPs for short). This is a natural SDP generalization of the well-studied positive LP problem.Although positive LPs can be solved in polylogarithmic depth while using only log2 n/∊3 parallelizable iterations [4], the best known positive SDP solvers due to <PERSON> and <PERSON> [18] require log14 n/∊13 parallelizable iterations. Several alternative solvers have been proposed to reduce the exponents in the number of iterations [19, 30]. However, the correctness of the convergence analyses in these works has been called into question [30], as they both rely on algebraic monotonicity properties that do not generalize to matrix algebra.In this paper, we propose a very simple algorithm based on the optimization framework proposed in [4] for LP solvers. Our algorithm only needs log2 n/∊3 iterations, matching that of the best LP solver. To surmount the obstacles encountered by previous approaches, our analysis requires a new matrix inequality that extends <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s inequality, and a sign-consistent, randomized variant of the gradient truncation technique proposed in [3, 4].", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH127"}, {"primary_key": "4189782", "vector": [], "sparse_vector": [], "title": "On the maximum quartet distance between phylogenetic trees.", "authors": ["Noga Alon", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)On the maximum quartet distance between phylogenetic trees<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.2095 - 2106Chapter DOI:https://doi.org/10.1137/1.*************.ch146PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A conjecture of Bandelt and <PERSON><PERSON> states that the maximum quartet distance between any two phylogenetic trees on n leaves is at most . Using the machinery of flag algebras we improve the currently known bounds regarding this conjecture, in particular we show that the maximum is at most . We also give further evidence that the conjecture is true by proving that the maximum distance between caterpillar trees is at most . Previous chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH146"}, {"primary_key": "4189783", "vector": [], "sparse_vector": [], "title": "Simpler, faster and shorter labels for distances in graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider how to assign labels to any undirected graph with n nodes such that, given the labels of two nodes and no other information regarding the graph, it is possible to determine the distance between the two nodes. The challenge in such a distance labeling scheme is primarily to minimize the maximum label length and secondarily to minimize the time needed to answer distance queries (decoding). Previous schemes have offered different tradeoffs between label lengths and query time. This paper presents a simple algorithm with shorter labels and shorter query time than any previous solution, thereby improving the state-of-the-art with respect to both label length and query time in one single algorithm. Our solution addresses several open problems concerning label length and decoding time and is the first improvement of label length for more than three decades.More specifically, we present a distance labeling scheme with labels of length bits1 and constant decoding time. This outperforms all existing results with respect to both size and decoding time, including <PERSON>'s (Combinatorica 1983) decade-old result, which uses labels of size (log 3)n and O(n/log n) decoding time, and <PERSON><PERSON><PERSON><PERSON> et al. (SODA'01), which uses labels of size 11n + o(n) and O(log log n) decoding time. In addition, our algorithm is simpler than the previous ones. In the case of integral edge weights of size at most W, we present almost matching upper and lower bounds for the label size . Furthermore, for r-additive approximation labeling schemes, where distances can be off by up to an additive constant r, we present both upper and lower bounds. In particular, we present an upper bound for 1-additive approximation schemes which, in the unweighted case, has the same size (ignoring second order terms) as an adjacency labeling scheme, namely n/2. We also give results for bipartite graphs as well as for exact and 1-additive distance oracles.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH25"}, {"primary_key": "4189784", "vector": [], "sparse_vector": [], "title": "Efficient Quantum Algorithms for (Gapped) Group Testing and Junta Testing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the k-junta testing problem, a tester has to efficiently decide whether a given function f: {0, 1}n → {0, 1} is a k-junta (i.e., depends on at most k of its input bits) or is ∊-far from any k-junta. Our main result is a quantum algorithm for this problem with query complexity and time complexity . This quadratically improves over the query complexity of the previous best quantum junta tester, due to <PERSON>ı<PERSON>ı and Servedio. Our tester is based on a new quantum algorithm for a gapped version of the combinatorial group testing problem, with an up to quartic improvement over the query complexity of the best classical algorithm. For our upper bound on the time complexity we give a near-linear time implementation of a shallow variant of the quantum Fourier transform over the symmetric group, similar to the <PERSON><PERSON><PERSON><PERSON><PERSON> transform. We also prove a lower bound of Ω(k1/3) queries for junta-testing (for constant ∊).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH65"}, {"primary_key": "4189785", "vector": [], "sparse_vector": [], "title": "Windrose Planarity: Embedding Graphs with Direction-Constrained Edges.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Valentino Di Donato", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a planar graph G(V, E) and a partition of the neighbors of each vertex v ∊ V in four sets , and , the problem Windrose Planarity asks to decide whether G admits a windrose-planar drawing, that is, a planar drawing in which (i) each neighbor u ∊ is above and to the right of v, (ii) each neighbor u ∊ is above and to the left of v, (iii) each neighbor u ∊ is below and to the left of v, (iv) each neighbor u ∊ is below and to the right of v, and (v) edges are represented by curves that are monotone with respect to each axis. By exploiting both the horizontal and the vertical relationship among vertices, windrose-planar drawings allow to simultaneously visualize two partial orders defined by means of the edges of the graph.Although the problem is -hard in the general case, we give a polynomial-time algorithm for testing whether there exists a windrose-planar drawing that respects a combinatorial embedding that is given as part of the input. This algorithm is based on a characterization of the plane triangulations admitting a windrose-planar drawing. Furthermore, for any embedded graph admitting a windrose-planar drawing we show how to construct one with at most one bend per edge on an O(n) × O(n) grid. The latter result contrasts with the fact that straight-line windrose-planar drawings may require exponential area.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH70"}, {"primary_key": "4189786", "vector": [], "sparse_vector": [], "title": "Finding Perfect Matchings in Bipartite Hypergraphs.", "authors": ["Chidam<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>'s condition [16] is a natural hypergraph analog of <PERSON>'s condition, which is a well-known necessary and sufficient condition for a bipartite graph to admit a perfect matching. That is, when <PERSON><PERSON><PERSON>'s condition holds it forces the existence of a perfect matching in the bipartite hypergraph. Unlike in graphs, however, there is no known polynomial time algorithm to find the hypergraph perfect matching that is guaranteed to exist when <PERSON><PERSON><PERSON>'s condition is satisfied.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH126"}, {"primary_key": "4189787", "vector": [], "sparse_vector": [], "title": "A Fast and Simple Algorithm for Computing Approximate Euclidean Minimum Spanning Trees.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The Euclidean minimum spanning tree (EMST) is a fundamental and widely studied structure. In the approximate version we are given an n-element point set P in ℝd and an error parameter ∊ > 0, and the objective is to compute a spanning tree over P whose weight is at most (1 + ∊) times that of the true minimum spanning tree. Assuming that d is a fixed constant, existing algorithms have running times that (up to logarithmic factors) grow as O(n/∊Ω(d)). We present an algorithm whose running time is . Thus, this is the first algorithm for approximate EMSTs that eliminates the exponential ∊ dependence on dimension. (Note that the O-notation conceals a constant factor of the form O(1)d.) The algorithm is deterministic and very simple.MSC codesEuclidean minimum spanning treeswell-separated pair decompositionsapproximation algorithms", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH85"}, {"primary_key": "4189788", "vector": [], "sparse_vector": [], "title": "Maximum Matchings in Dynamic Graph Streams and the Simultaneous Communication Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of finding an approximate maximum matching in two closely related computational models, namely, the dynamic graph streaming model and the simultaneous multi-party communication model. In the dynamic graph streaming model, the input graph is revealed as a stream of edge insertions and deletions, and the goal is to design a small space algorithm to approximate the maximum matching. In the simultaneous model, the input graph is partitioned across k players, and the goal is to design a protocol where the k players simultaneously send a small-size message to a coordinator, and the coordinator computes an approximate matching.Dynamic graph streams. We resolve the space complexity of single-pass turnstile streaming algorithms for approximating matchings by showing that for any ∊ > 0, ⊝(n2–3e) space is both sufficient and necessary (up to polylogarithmic factors) to compute an n∊-approximate matching; here n denotes the number of vertices in the input graph.The simultaneous communication model. Our results for dynamic graph streams also resolve the (per-player) simultaneous communication complexity for approximating matchings in the edge partition model. For the vertex partition model, we design new randomized and deterministic protocols for k players to achieve an α-approximation. Specifically, for , we provide a randomized protocol with total communication of O(nk/α2) and a deterministic protocol with total communication of O(nk/α). Both these bounds are tight. Our work generalizes the results established by <PERSON><PERSON><PERSON><PERSON> et al. (STOC 2014) for the special case of k = n. Finally, for the case of , we establish a new lower bound on the simultaneous communication complexity which is super-linear in n.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH93"}, {"primary_key": "4189789", "vector": [], "sparse_vector": [], "title": "Balanced Allocation: Patience is not a Virtue.", "authors": ["<PERSON>", "<PERSON> Jr.", "<PERSON>", "<PERSON>"], "summary": "Load balancing is a well-studied problem, with balls-in-bins being the primary framework. The greedy algorithm $\\mathsf{Greedy}[d]$ of <PERSON><PERSON> et al. places each ball by probing $d > 1$ random bins and placing the ball in the least loaded of them. With high probability, the maximum load under $\\mathsf{Greedy}[d]$ is exponentially lower than the result when balls are placed uniformly randomly. V\\\"ocking showed that a slightly asymmetric variant, $\\mathsf{Left}[d]$, provides a further significant improvement. However, this improvement comes at an additional computational cost of imposing structure on the bins. Here, we present a fully decentralized and easy-to-implement algorithm called $\\mathsf{FirstDiff}[d]$ that combines the simplicity of $\\mathsf{Greedy}[d]$ and the improved balance of $\\mathsf{Left}[d]$. The key idea in $\\mathsf{FirstDiff}[d]$ is to probe until a different bin size from the first observation is located, then place the ball. Although the number of probes could be quite large for some of the balls, we show that $\\mathsf{FirstDiff}[d]$ requires only at most $d$ probes on average per ball (in both the standard and the heavily-loaded settings). Thus the number of probes is no greater than either that of $\\mathsf{Greedy}[d]$ or $\\mathsf{Left}[d]$. More importantly, we show that $\\mathsf{FirstDiff}[d]$ closely matches the improved maximum load ensured by $\\mathsf{Left}[d]$ in both the standard and heavily-loaded settings. We further provide a tight lower bound on the maximum load up to $O(\\log \\log \\log n)$ terms. We additionally give experimental data that $\\mathsf{FirstDiff}[d]$ is indeed as good as $\\mathsf{Left}[d]$, if not better, in practice.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH48"}, {"primary_key": "4189790", "vector": [], "sparse_vector": [], "title": "Packing Small Vectors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Online d-dimensional vector packing models many settings such as minimizing resources in data centers where jobs have multiple resource requirements (CPU, Memory, etc.). However, no online d-dimensional vector packing algorithm can achieve a competitive ratio better than d. Fortunately, in many natural applications, vectors are relatively small, and thus the lower bound does not hold. For sufficiently small vectors, an O(log d)-competitive algorithm was known. We improve this to a constant competitive ratio, arbitrarily close to e ≈ 2.718, given that vectors are sufficiently small.We give improved results for the two dimensional case. For arbitrarily small vectors, the First Fit algorithm for two dimensional vector packing is no better than 2-competitive. We present a natural family of First Fit variants, and for optimized parameters get a competitive ratio ≈ 1.48 for sufficiently small vectors.We improve upon the 1.48 competitive ratio – not via a First Fit variant – and give a competitive ratio arbitrarily close to 4/3 for packing small, two dimensional vectors. We show that no algorithm can achieve better than a 4/3 competitive ratio for two dimensional vectors, even if one allows the algorithm to split vectors among arbitrarily many bins.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH103"}, {"primary_key": "4189791", "vector": [], "sparse_vector": [], "title": "Make-to-Order Integrated Scheduling and Distribution.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Lukasz Jez", "<PERSON><PERSON>"], "summary": "Production and distribution are fundamental operational functions in supply chains. The main challenge is to design algorithms that optimize operational performance by jointly scheduling production and delivery of customer orders. In this paper we study a model of scheduling customer orders on multiple identical machines and their distribution to customers afterwards. The goal is to minimize the total time from release to distribution plus total distribution cost to the customers. We design the first poly-logarithmic competitive algorithm for the problem, improving upon previous algorithms with linear competitive ratios. Our model generalizes two fundamental problems: scheduling of jobs on multiple identical machines (where the goal function is to minimize the total flow time) as well as the TCP Acknowledgment problem.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH11"}, {"primary_key": "4189792", "vector": [], "sparse_vector": [], "title": "Nearly-optimal bounds for sparse recovery in generic norms, with applications to k-median sketching.", "authors": ["Arturs Backurs", "<PERSON><PERSON><PERSON>", "Ilya <PERSON>", "<PERSON>"], "summary": "We initiate the study of trade-offs between sparsity and the number of measurements in sparse recovery schemes for generic norms. Specifically for a norm ‖·‖, sparsity parameter k, approximation factor K > 0, and probability of failure P > 0, we ask: what is the minimal value of m so that there is a distribution over m × n matrices A with the property that for any x, given Ax, we can recover a k-sparse approximation to x in the given norm with probability at least 1 – P? We give a partial answer to this problem, by showing that for norms that admit efficient linear sketches, the optimal number of measurements m is closely related to the doubling dimension of the metric induced by the norm ‖·‖ on the set of all k-sparse vectors. By applying our result to specific norms, we cast known measurement bounds in our general framework (for the ℓp norms, p ∊ [1, 2]) as well as provide new, measurement-efficient schemes (for the Earth-Mover Distance norm). The latter result directly implies more succinct linear sketches for the well-studied planar k-median clustering problem. Finally, our lower bound for the doubling dimension of the EMD norm enables us to resolve the open question of [<PERSON><PERSON><PERSON>-<PERSON><PERSON>, STOC'05] about the space complexity of clustering problems in the dynamic streaming model.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH24"}, {"primary_key": "4189793", "vector": [], "sparse_vector": [], "title": "Locally Adaptive Optimization: Adaptive Seeding for Monotone Submodular Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>via<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Locally Adaptive Optimization: Adaptive Seeding for Monotone Submodular Fun<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.414 - 429Chapter DOI:https://doi.org/10.1137/1.*************.ch31PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The Adaptive Seeding problem is an algorithmic challenge motivated by influence maximization in social networks: One seeks to select among certain accessible nodes in a network, and then select, adaptively, among neighbors of those nodes as they become accessible in order to maximize a global objective function. More generally, adaptive seeding is a stochastic optimization framework where the choices in the first stage affect the realizations in the second stage, over which we aim to optimize. Our main result is a (1 – 1/e)2-approximation for the adaptive seeding problem for any monotone submodular function. While adaptive policies are often approximated via non-adaptive policies, our algorithm is based on a novel method we call locally-adaptive policies. These policies combine a non-adaptive global structure, with local adaptive optimizations. This method enables the (1–1/e)2-approximation for general monotone submodular functions and circumvents some of the impossibilities associated with non-adaptive policies. We also introduce a fundamental problem in submodular optimization that may be of independent interest: given a ground set of elements where every element appears with some small probability, find a set of expected size at most k that has the highest expected value over the realization of the elements. We show a surprising result: there are classes of monotone submodular functions (including coverage) that can be approximated almost optimally as the probability vanishes. For general monotone submodular functions we show via a reduction from Planted-Clique that approximations for this problem are not likely to be obtainable. This optimization problem is an important tool for adaptive seeding via non-adaptive policies, and its hardness motivates the introduction of locally-adaptive policies we use in the main result. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH31"}, {"primary_key": "4189794", "vector": [], "sparse_vector": [], "title": "Improved Approximation for Vector Bin Packing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Improved Approximation for Vector Bin Packing<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1561 - 1579Chapter DOI:https://doi.org/10.1137/1.*************.ch106PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the d-dimensional vector bin packing problem, a well-studied generalization of bin packing arising in resource allocation and scheduling problems. Here we are given a set of d-dimensional vectors v1, …, vn in [0, 1]d, and the goal is to pack them into the least number of bins so that for each bin B, the sum of the vectors in it is at most 1 in every dimension, i.e., . For the 2-dimensional case we give an asymptotic approximation guarantee of 1 + ln(1.5) + ∊ ≈ (1.405 + ∊), improving upon the previous bound of 1 + ln 2 + ∊ ≈ (1.693 + ∊). We also give an almost tight (1.5+ ∊) absolute approximation guarantee, improving upon the previous bound of 2 [23]. For the d-dimensional case, we get a guarantee, improving upon the previous (1 + ln d + ∊) guarantee [2]. Here (1 + ln d) was a natural barrier as rounding-based algorithms can not achieve better than d approximation. We get around this by exploiting various structural properties of (near)-optimal packings, and using multi-objective multi-budget matching based techniques and expanding the Round & Approx framework to go beyond rounding-based algorithms. Along the way we also prove several results that could be of independent interest. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH106"}, {"primary_key": "4189795", "vector": [], "sparse_vector": [], "title": "On Notions of Distortion and an Almost Minimum Spanning Tree with Constant Average Distortion.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)On Notions of Distortion and an Almost Minimum Spanning Tree with Constant Average Distortion<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>pp.873 - 882Chapter DOI:https://doi.org/10.1137/1.*************.ch62PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Minimum Spanning Trees of weighted graphs are fundamental objects in numerous applications. In particular in distributed networks, the minimum spanning tree of the network is often used to route messages between network nodes. Unfortunately, while being most efficient in the total cost of connecting all nodes, minimum spanning trees fail miserably in the desired property of approximately preserving distances between pairs. While known lower bounds exclude the possibility of the worst case distortion of a tree being small, it was shown in [4] that there exists a spanning tree with constant average distortion. Yet, the weight of such a tree may be significantly larger than that of the MST. In this paper, we show that any weighted undirected graph admits a spanning tree whose weight is at most (1 + ρ) times that of the MST, providing constant average distortion O(1/ρ2).1 The constant average distortion bound is implied by a stronger property of scaling distortion, i.e., improved distortion for smaller fractions of the pairs. The result is achieved by first showing the existence of a low weight spanner with small prioritized distortion, a property allowing to prioritize the nodes whose associated distortions will be improved. We show that prioritized distortion is essentially equivalent to coarse scaling distortion via a general transformation, which has further implications and may be of independent interest. In particular, we obtain an embedding for arbitrary metrics into Euclidean space with optimal prioritized distortion. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH62"}, {"primary_key": "4189796", "vector": [], "sparse_vector": [], "title": "Dynamic DFS in Undirected Graphs: breaking the O(m) barrier.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shahbaz Khan"], "summary": "Depth first search (DFS) tree is a fundamental data structure for solving various problems in graphs. It is well known that it takes $O(m+n)$ time to build a DFS tree for a given undirected graph $G=(V,E)$ on $n$ vertices and $m$ edges. We address the problem of maintaining a DFS tree when the graph is undergoing {\\em updates} (insertion and deletion of vertices or edges). We present the following results for this problem. (a) Fault tolerant DFS tree: There exists a data structure of size ${O}(m ~polylog~ n)$ such that given any set ${\\cal F}$ of failed vertices or edges, a DFS tree of the graph $G\\setminus {\\cal F}$ can be reported in ${O}(n|{\\cal F}| ~polylog~ n)$ time. (b) Fully dynamic DFS tree: There exists a fully dynamic algorithm for maintaining a DFS tree that takes worst case ${O}(\\sqrt{mn} ~polylog~ n)$ time per update for any arbitrary online sequence of updates. (c) Incremental DFS tree: Given any arbitrary online sequence of edge insertions, we can maintain a DFS tree in ${O}(n ~polylog~ n)$ worst case time per edge insertion. These are the first $o(m)$ worst case time results for maintaining a DFS tree in a dynamic environment. Moreover, our fully dynamic algorithm provides, in a seamless manner, the first deterministic algorithm with $O(1)$ query time and $o(m)$ worst case update time for the dynamic subgraph connectivity, biconnectivity, and 2-edge connectivity.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH52"}, {"primary_key": "4189797", "vector": [], "sparse_vector": [], "title": "Stabilizing Consensus with Many Opinions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Stabilizing Consensus with Many OpinionsL<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.620 - 635Chapter DOI:https://doi.org/10.1137/1.*************.ch46PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the following distributed consensus problem: Each node in a complete communication network of size n initially holds an opinion, which is chosen arbitrarily from a finite set Σ. The system must converge toward a consensus state in which all, or almost all nodes, hold the same opinion. Moreover, this opinion should be valid, i.e., it should be one among those initially present in the system. This condition should be met even in the presence of a malicious adversary who can modify the opinions of a bounded subset of nodes, adaptively chosen in every round. We consider the 3-majority dynamics: At every round, every node pulls the opinion from three random neighbors and sets his new opinion to the majority one (ties are broken arbitrarily). Let k be the number of valid opinions. We show that, if k ≤ nα, where α is a suitable positive constant, the 3-majority dynamics converges in time polynomial in k and log n with high probability even in the presence of an adversary who can affect up to nodes at each round. Previously, the convergence of the 3-majority protocol was known for |Σ| = 2 only, with an argument that is robust to adversarial errors. On the other hand, no anonymous, uniform-gossip protocol that is robust to adversarial errors was known for |Σ| > 2. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106Key words:Distributed Consensus, Byzantine Agreement, Gossip Model, Majority Rules, Markov Chains", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH46"}, {"primary_key": "4189798", "vector": [], "sparse_vector": [], "title": "New directions in nearest neighbor searching with applications to lattice sieving.", "authors": ["<PERSON><PERSON>", "Léo Du<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)New directions in nearest neighbor searching with applications to lattice sieving<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>.10 - 24Chapter DOI:https://doi.org/10.1137/1.*************.ch2PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract To solve the approximate nearest neighbor search problem (NNS) on the sphere, we propose a method using locality-sensitive filters (LSF), with the property that nearby vectors have a higher probability of surviving the same filter than vectors which are far apart. We instantiate the filters using spherical caps of height 1 – α, where a vector survives a filter if it is contained in the corresponding spherical cap, and where ideally each filter has an independent, uniformly random direction. For small α, these filters are very similar to the spherical locality-sensitive hash (LSH) family previously studied by <PERSON><PERSON> et al. For larger α bounded away from 0, these filters potentially achieve a superior performance, provided we have access to an efficient oracle for finding relevant filters. Whereas existing LSH schemes are limited by a performance parameter of ρ ≥ 1/(2c2 – 1) to solve approximate NNS with approximation factor c, with spherical LSF we potentially achieve smaller asymptotic values of ρ, depending on the density of the data set. For sparse data sets where the dimension is super-logarithmic in the size of the data set, we asymptotically obtain ρ = 1/(2c2 – 1), while for a logarithmic dimensionality with density constant κ we obtain asymptotics of ρ ∼ 1/(4κc2). To instantiate the filters and prove the existence of an efficient decoding oracle, we replace the independent filters by filters taken from certain structured random product codes. We show that the additional structure in these concatenation codes allows us to decode efficiently using techniques similar to lattice enumeration, and we can find the relevant filters with low overhead, while at the same time not significantly changing the collision probabilities of the filters. We finally apply spherical LSF to sieving algorithms for solving the shortest vector problem (SVP) on lattices, and show that this leads to a heuristic time complexity for solving SVP in dimension n of (3/2)n/2+o(n) ≈ 20.292n+o(n). This asymptotically improves upon the previous best algorithms for solving SVP which use spherical LSH and cross-polytope LSH and run in time 20.298n+o(n). Experiments with the GaussSieve validate the claimed speedup and show that this method may be practical as well, as the polynomial overhead is small. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH2"}, {"primary_key": "4189799", "vector": [], "sparse_vector": [], "title": "Range Predecessor and Lempel-Ziv Parsing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Lempel-Ziv parsing of a string (LZ77 for short) is one of the most important and widely-used algorithmic tools in data compression and string processing. We show that the Lempel-Ziv parsing of a string of length n on an alphabet of size σ can be computed in O(n log log σ) time (O(n) time if we allow randomization) using O(n log σ) bits of working space; that is, using space proportional to that of the input string in bits. The previous fastest algorithm using O(n log σ) space takes O(n(log σ + log log n)) time. We also consider the important rightmost variant of the problem, where the goal is to associate with each phrase of the parsing its most recent occurrence in the input string. We solve this problem in time, using the same working space as above. The previous best solution for rightmost parsing uses O(n(1 + log σ/log log n)) time and O(n log n) space. As a bonus, in our solution for rightmost parsing we provide a faster construction method for efficient 2D orthogonal range reporting, which is of independent interest.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH143"}, {"primary_key": "4189800", "vector": [], "sparse_vector": [], "title": "How to Scale Exponential Backoff: Constant Throughput, Polylog Access Attempts, and Robustness.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)How to Scale Exponential Backoff: Constant Throughput, Polylog Access Attempts, and RobustnessM<PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON>, and <PERSON>.636 - 654Chapter DOI:https://doi.org/10.1137/1.*************.ch47PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Randomized exponential backoff is a widely deployed technique for coordinating access to a shared resource. A good backoff protocol should, arguably, satisfy three natural properties: (i) it should provide constant throughput, wasting as little time as possible; (ii) it should require few failed access attempts, minimizing the amount of wasted effort; and (iii) it should be robust, continuing to work efficiently even if some of the access attempts fail for spurious reasons. Unfortunately, exponential backoff has some well-known limitations in two of these areas: it provides poor (sub-constant) throughput (in the worst case), and is not robust (to adversarial disruption). The goal of this paper is to \"fix\" exponential backoff by making it scalable, particularly focusing on the case where processes arrive in an on-line, worst-case fashion. We present a relatively simple backoff protocol, Re-Backoff, that has, at its heart, a version of exponential backoff. It guarantees expected constant throughput with dynamic process arrivals and requires only an expected polylogarithmic number of access attempts per process. Re-Backoff is also robust to periods where the shared resource is unavailable for a period of time. If it is unavailable for D time slots, Re-Backoff provides the following guarantees. When the number of packets is a finite n, the average expected number of access attempts for successfully sending a packet is O(log2(n + D)). In the infinite case, the average expected number of access attempts for successfully sending a packet is O(log2(η + D)) where η is the maximum number of processes that are ever in the system concurrently. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH47"}, {"primary_key": "4189801", "vector": [], "sparse_vector": [], "title": "Robust positioning patterns.", "authors": ["<PERSON>", "Swas<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Robust positioning patternsRoss Berkowitz and Swastik KoppartyRoss Berkowitz and Swastik <PERSON>.1937 - 1951Chapter DOI:https://doi.org/10.1137/1.*************.ch136PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper, we construct large sequences and matrices with the property that the contents of any small window determine the location of the window, robustly. Such objects have found many applications in practical settings, from positioning of wireless devices to smart pens, and have recently gained some theoretical interest. In this context, we give the first explicit constructions of sequences and matrices with high rate and constant relative distance. Accompanying these efficient constructions, we also give efficient decoding algorithms, which can determine the position of the window given its contents, even if a constant fraction of the contents have been corrupted. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH136"}, {"primary_key": "4189802", "vector": [], "sparse_vector": [], "title": "Blocking Optimal k-Arborescences.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given a digraph D = (V, A) and a positive integer k, an arc set F ⊆ A is called a k-arborescence if it is the disjoint union of k spanning arborescences. The problem of finding a minimum cost k-arborescence is known to be polynomial-time solvable using matroid intersection. In this paper we study the following problem: find a minimum cardinality subset of arcs that contains at least one arc from every minimum cost k-arborescence. For k = 1. the problem was solved in [<PERSON><PERSON>, <PERSON>, Blocking optimal arborescences, IPCO 2013]. In this paper we give an algorithm for general k that has polynomial running time if k is fixed.MSC codesarborescencescoveringmatroidspolynomial-time algorithms", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH115"}, {"primary_key": "4189803", "vector": [], "sparse_vector": [], "title": "Faster Fully Dynamic Matchings with Small Approximation Ratios.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Maximum cardinality matching is a fundamental algorithmic problem with many algorithms and applications. The fully dynamic version, in which edges are inserted and deleted over time has also been the subject of much attention. Existing algorithms for dynamic matching (in general n-vertex m-edge graphs) fall into two groups: there are fast (mostly randomized) algorithms that achieve a 2-approximation or worse, and there are slow algorithms with update time that achieve a better-than-2 approximation. Thus the obvious question is whether we can design an algorithm that achieves a tradeoff between these two: a update time and a better-than-2 approximation simultaneously. We answer this question in the affirmative. Previously, such bounds were only known for the special case of bipartite graphs.Our main result is a fully dynamic deterministic algorithm that maintains a (3/2 + ∊)-approximation in amortized update time O(m1/4∊–2.5). In addition to achieving the trade-off described above, our algorithm manages to be polynomially faster than all existing deterministic algorithms (excluding an existing log n-approximation of <PERSON><PERSON> and <PERSON>), while still maintaining a better-than-2 approximation.We also give stronger results for graphs whose arboricity is at most α. We show how to maintain a (1 + ∊)-approximate fractional matching or a (3/2 + ∊)-approximate integral matching in worst-case time O(α(α + log n)) for constant ∊. When the arboricity is constant, this bound is O(log n) and when the arboricity is polylogarithmic the update time is also polylogarithmic. Previous results for small arboricity non-bipartite graphs could only maintain a maximal matching (2-approximation).We maintain the approximate matching without explicitly using augmenting paths. We define an intermediate graph, called an EDCS and show that the EDCS H contains a large matching, and show how to maintain an EDCS in G. The EDCS was used in previous works on bipartite graphs, however the details and proofs are completely different in general graphs. The algorithm for bipartite graphs relies on ideas from flows and cuts to non-constructively prove the existence of a good matching in H, but these ideas do not seem to extend to non-bipartite graphs. In this paper we instead explicitly construct a large fractional matching in H. In some cases we can guarantee that this fractional matching is γ-restricted, which means that it only uses values either in the range [0, γ] or 1. We then combine this matching with a new structural property of maximum matchings in non-bipartite graphs, which is analogous to the cut induced by maximum matchings in bipartite graphs.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH50"}, {"primary_key": "4189804", "vector": [], "sparse_vector": [], "title": "Efficient quantum algorithms for computing class groups and solving the principal ideal problem in arbitrary degree number fields.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper gives polynomial time quantum algorithms for computing the ideal class group (CGP) under the Generalized Riemann Hypothesis and solving the principal ideal problem (PIP) in number fields of arbitrary degree. These are are fundamental problems in number theory and they are connected to many unproven conjectures in both analytic and algebraic number theory. Previously the best known algorithms by <PERSON><PERSON> [20] only allowed to solve these problems in quantum polynomial time for number fields of constant degree. In a recent breakthrough, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. [11] showed how to compute the unit group in arbitrary fields, thus opening the way to the resolution of CGP and PIP in the general case. For example, <PERSON><PERSON><PERSON> and <PERSON> [3] pointed out how to directly apply this result to solve PIP in classes of cyclotomic fields of arbitrary degree.The methods we introduce in this paper run in quantum polynomial time in arbitrary classes of number fields. They can be applied to solve other problems in computational number theory as well including computing the ray class group and solving relative norm equations. They are also useful for ongoing cryptanalysis of cryptographic schemes based on ideal lattices [5, 10].Our algorithms generalize the quantum algorithm for computing the (ordinary) unit group [11]. We first show that CGP and PIP reduce naturally to the computation of S-unit groups, which is another fundamental problem in number theory. Then we show an efficient quantum reduction from computing S-units to the continuous hidden subgroup problem introduced in [11]. This step is our main technical contribution, which involves careful analysis of the metrical properties of lattices to prove the correctness of the reduction. In addition, we show how to convert the output into an exact compact representation, which is convenient for further algebraic manipulations.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH64"}, {"primary_key": "4189805", "vector": [], "sparse_vector": [], "title": "Random-Cluster Dynamics in ℤ2.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The random-cluster model has been widely studied as a unifying framework for random graphs, spin systems and electrical networks, but its dynamics have so far largely resisted analysis. In this paper we analyze the Glauber dynamics of the random-cluster model in the canonical case where the underlying graph is an n × n box in the Cartesian lattice ℤ2. Our main result is a O(n2 log n) upper bound for the mixing time at all values of the model parameter p except the critical point p = pc(q), and for all values of the second model parameter q ≥ 1. We also provide a matching lower bound proving that our result is tight. Our analysis takes as its starting point the recent breakthrough by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> on the location of the random-cluster phase transition in ℤ2. It is reminiscent of similar results for spin systems such as the Ising and Potts models, but requires the reworking of several standard tools in the context of the random-cluster model, which is not a spin system in the usual sense.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH37"}, {"primary_key": "4189806", "vector": [], "sparse_vector": [], "title": "Lower bounds for the parameterized complexity of Minimum Fill-In and other completion problems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we focus on several completion problems for subclasses of chordal graphs: Minimum Fill-In, Interval Completion, Proper Interval Completion, Threshold Completion, and Trivially Perfect Completion. In these problems, the task is to add at most k edges to a given graph in order to obtain a chordal, interval, proper interval, threshold, or trivially perfect graph, respectively. We prove the following lower bounds for all these problems, as well as for the related Chain Completion problem:•Assuming the Exponential Time Hypothesis, none of these problems can be solved in time 2O(n1/2/logcn) or 2O(k1/4/logck). nO(1) for some integer c.•Assuming the non-existence of a subexponential-time approximation scheme for Min Bisection on d-regular graphs, for some constant d, none of these problems can be solved in time 2o(n) or .", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH79"}, {"primary_key": "4189807", "vector": [], "sparse_vector": [], "title": "Subexponential parameterized algorithm for Interval Completion.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the Interval Completion problem we are given an n-vertex graph G and an integer k, and the task is to transform G by making use of at most k edge additions into an interval graph. This is a fundamental graph modification problem with applications in sparse matrix multiplication and molecular biology. The question about fixed-parameter tractability of Interval Completion was asked by <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [FOCS 1994; SIAM J. Comput. 1999] and was answered affirmatively more than a decade later by Villanger at el. [STOC 2007; SIAM J. Comput. 2009], who presented an algorithm with running time O(k2kn3m). We give the first subexponential parameterized algorithm solving Interval Completion in time . This adds Interval Completion to a very small list of parameterized graph modification problems solvable in subexponential time.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH78"}, {"primary_key": "4189808", "vector": [], "sparse_vector": [], "title": "Sparse Approximation via Generating Point Sets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "For a set P of n points in the unit ball b ⊆ ℝd, consider the problem of finding a small subset T ⊆ P such that its convex-hull ∊-approximates the convex-hull of the original set. Specifically, the Hausdorff distance between the convex hull of T and the convex hull of P should be at most ∊. We present an efficient algorithm to compute such an ∊′-approximation of size kalg, where ∊′ is a function of ∊, and kalg is a function of the minimum size kopt of such an ∊-approximation. Surprisingly, there is no dependence on the dimension d in either of the bounds. Furthermore, every point of P can be ∊-approximated by a convex-combination of points of T that is O(1/∊2)-sparse.Our result can be viewed as a method for sparse, convex autoencoding: approximately representing the data in a compact way using sparse combinations of a small subset T of the original data. The new algorithm can be kernelized, and it preserves sparsity in the original input.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH40"}, {"primary_key": "4189809", "vector": [], "sparse_vector": [], "title": "Better Distance Preservers and Additive Spanners.", "authors": ["<PERSON>", "Virginia Vassilevska Williams"], "summary": "We make improvements to the upper bounds on several popular types of distance preserving graph sketches. The first part of our paper concerns pairwise distance preservers, which are sparse subgraphs that exactly preserve the pairwise distances for a set of given pairs of vertices. Our main result here is that all unweighted, undirected n-node graphs G and all pair sets P have distance preservers on |H| = O(n2/3|P|2/3 + n|P|1/3) edges. This improves the known bounds whenever |P| = ω(n3/4).We then develop a new graph clustering technique, based on distance preservers, and we apply this technique to show new upper bounds for additive (standard) spanners, in which all pairwise distances must be preserved up to an additive error function, and for subset spanners, in which only distances within a given node subset must be preserved up to an error function. For both of these objects, we obtain the new best tradeoff between spanner sparsity and error allowance in the regime where the error is polynomial in the graph size.We leave open a conjecture that O(n2/3|P|2/3 + n) pairwise distance preservers are possible for undirected unweighted graphs. Resolving this conjecture in the affirmative would improve and simplify our upper bounds for all the graph sketches mentioned above.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH61"}, {"primary_key": "4189810", "vector": [], "sparse_vector": [], "title": "On the Economic Efficiency of the Combinatorial Clock Auction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Since the 1990s spectrum auctions have been implemented world-wide. This has provided for a practical examination of an assortment of auction mechanisms and, amongst these, two simultaneous ascending price auctions have proved to be extremely successful. These are the simultaneous multiround ascending auction (SMRA) and the combinatorial clock auction (CCA). It has long been known that, for certain classes of valuation functions, the SMRA provides good theoretical guarantees on social welfare. However, no such guarantees were known for the CCA.In this paper, we show that CCA does provide strong guarantees on social welfare provided the price increment and stopping rule are well-chosen. This is very surprising in that the choice of price increment has been used primarily to adjust auction duration and the stopping rule has attracted little attention. The main result is a polylogarithmic approximation guarantee for social welfare when the maximum number of items demanded by a bidder is fixed. Specifically, we show that either the revenue of the CCA is at least an -fraction of the optimal welfare or the welfare of the CCA is at least an -fraction of the optimal welfare, where n is the number of bidders and m is the number of items. As a corollary, the welfare ratio – the worst case ratio between the social welfare of the optimum allocation and the social welfare of the CCA allocation – is at most O(2 · log n·· log2 m). We emphasize that this latter result requires no assumption on bidders valuation functions. Finally, we prove that such a dependence on is necessary. In particular, we show that the welfare ratio of the CCA is at least .", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH97"}, {"primary_key": "4189811", "vector": [], "sparse_vector": [], "title": "Algorithmic Complexity of Power Law Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It was experimentally observed that the majority of real-world networks are scale-free and follow power law degree distribution. The aim of this paper is to study the algorithmic complexity of such \"typical\" networks. The contribution of this work is twofold.First, we define a deterministic condition for checking whether a graph has a power law degree distribution and experimentally validate it on real-world networks. This definition allows us to derive interesting properties of power law networks. We observe that for exponents of the degree distribution in the range [1, 2] such networks exhibit double power law phenomenon that was observed for several real-world networks. Our observation indicates that this phenomenon could be explained by just pure graph theoretical properties.The second aim of our work is to give a novel theoretical explanation why many algorithms run faster on real-world data than what is predicted by algorithmic worst-case analysis. We show how to exploit the power law degree distribution to design faster algorithms for a number of classic P-time problems including transitive closure, maximum matching, determinant, PageRank and matrix inverse. Moreover, we deal with the problems of counting triangles and finding maximum clique.In contrast to previously done average-case analyses, we believe that this is the first \"waterproof\" argument that explains why many real-world networks are easier. Moreover, an interesting aspect of this study is the existence of structure oblivious algorithms, i.e., algorithms that run faster on power law networks without explicit knowledge of this fact or explicit knowledge of the parameters of the degree distribution, e.g., algorithms for maximum clique or triangle counting.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH91"}, {"primary_key": "4189812", "vector": [], "sparse_vector": [], "title": "Efficient Low-Redundancy Codes for Correcting Multiple Deletions.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "We consider the problem of constructing binary codes to recover from k–bit deletions with efficient encoding/decoding, for a fixed k. The single deletion case is well understood, with the Varshamov-Tenengolts-Levenshtein code from 1965 giving an asymptotically optimal construction with ≈ 2n/n codewords of length n, i.e., at most log n bits of redundancy. However, even for the case of two deletions, there was no known explicit construction with redundancy less than nΩ(1).For any fixed k, we construct a binary code with ck log n redundancy that can be decoded from k deletions in Ok(n log4 n) time. The coefficient ck can be taken to be O(k2 log k), which is only quadratically worse than the optimal, non-constructive bound of O(k). We also indicate how to modify this code to allow for a combination of up to k insertions and deletions.We also note that among linear codes capable of correcting k deletions, the (k + 1)-fold repetition code is essentially the best possible.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH132"}, {"primary_key": "4189813", "vector": [], "sparse_vector": [], "title": "The matching problem has no small symmetric SDP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)The matching problem has no small symmetric SDP<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.1067 - 1078Chapter DOI:https://doi.org/10.1137/1.*************.ch75PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Yannakakis [27, 26] showed that the matching problem does not have a small symmetric linear program. <PERSON><PERSON><PERSON> [23] recently proved that any, not necessarily symmetric, linear program also has exponential size. It is natural to ask whether the matching problem can be expressed compactly in a framework such as semidefinite programming (SDP) that is more powerful than linear programming but still allows efficient optimization. We answer this question negatively for symmetric SDPs: any symmetric SDP for the matching problem has exponential size. We also show that an O(k)-round Lasserre SDP relaxation for the asymmetric metric traveling salesperson problem yields at least as good an approximation as any symmetric SDP relaxation of size nk. The key technical ingredient underlying both these results is an upper bound on the degree needed to derive polynomial identities that hold over the space of matchings or traveling salesperson tours. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH75"}, {"primary_key": "4189814", "vector": [], "sparse_vector": [], "title": "Clustering Problems on Sliding Windows.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We explore clustering problems in the streaming sliding window model in both general metric spaces and Euclidean space. We present the first polylogarithmic space O(1)-approximation to the metric k-median and metric k-means problems in the sliding window model, answering the main open problem posed by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [5], which has remained unanswered for over a decade. Our algorithm uses O(k3 log6 W) space and poly(k, log W) update time, where W is the window size. This is an exponential improvement on the space required by the technique due to <PERSON><PERSON><PERSON>, et al. We introduce a data structure that extends smooth histograms as introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [11] to operate on a broader class of functions. In particular, we show that using only polylogarithmic space we can maintain a summary of the current window from which we can construct an O(1)-approximate clustering solution.Merge-and-reduce is a generic method in computational geometry for adapting offline algorithms to the insertion-only streaming model. Several well-known coreset constructions are maintainable in the insertion-only streaming model using this method, including well-known coreset techniques for the k-median and k-means problems in both low-and high-dimensional Euclidean spaces [31, 15]. Previous work [27] has adapted coreset techniques to the insertion-deletion model, but translating them to the sliding window model has remained a challenge. We give the first algorithm that, given an insertion-only streaming coreset of space s (maintained using merge-and-reduce method), maintains this coreset in the sliding window model using O(s2∊–2 log W) space.For clustering problems, our results constitute the first significant step towards resolving problem number 20 from the List of Open Problems in Sublinear Algorithms [39].", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH95"}, {"primary_key": "4189815", "vector": [], "sparse_vector": [], "title": "Interpolating Between Truthful and non-Truthful Mechanisms for Combinatorial Auctions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the communication complexity of combinatorial auctions via interpolation mechanisms that interpolate between non-truthful and truthful protocols. Specifically, an interpolation mechanism has two phases. In the first phase, the bidders participate in some non-truthful protocol whose output is itself a truthful protocol. In the second phase, the bidders participate in the truthful protocol selected during phase one. Note that virtually all existing auctions have either a non-existent first phase (and are therefore truthful mechanisms), or a non-existent second phase (and are therefore just traditional protocols, analyzed via the Price of Anarchy/Stability).The goal of this paper is to understand the benefits of interpolation mechanisms versus truthful mechanisms or traditional protocols, and develop the necessary tools to formally study them. Interestingly, we exhibit settings where interpolation mechanisms greatly outperform the optimal traditional and truthful protocols. Yet, we also exhibit settings where interpolation mechanisms are provably no better than truthful ones. Finally, we apply our new machinery to prove that the recent single-bid mechanism of Dev<PERSON>r et. al. [DMSW15] (the only pre-existing interpolation mechanism in the literature) achieves the optimal price of anarchy among a wide class of protocols, a claim that simply can't be addressed by appealing just to machinery from communication complexity or the study of truthful mechanisms.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH99"}, {"primary_key": "4189816", "vector": [], "sparse_vector": [], "title": "Recovery and Rigidity in a Regular Stochastic Block Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Linh V. Tran"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Recovery and Rigidity in a Regular Stochastic Block Model<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>ranpp.1589 - 1601Chapter DOI:https://doi.org/10.1137/1.*************.ch108PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The stochastic block model is a natural model for studying community detection in random networks. Its clustering properties have been extensively studied in the statistics, physics and computer science literature. Recently this area has experienced major mathematical breakthroughs, particularly for the binary (two-community) version, see [24, 25, 20]. In this paper, we introduce a variant of the binary model which we call the regular stochastic block model (RSBM). We prove rigidity of this model by showing that with high probability an exact recovery of the community structure is possible. Spectral methods exhibit a regime where this can be done efficiently. Moreover we also prove that, in this setting, any suitably good partial recovery can be bootstrapped to obtain a full recovery of the communities. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH108"}, {"primary_key": "4189817", "vector": [], "sparse_vector": [], "title": "Deterministic Algorithms for Submodular Maximization Problems.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Randomization is a fundamental tool used in many theoretical and practical areas of computer science. We study here the role of randomization in the area of submodular function maximization. In this area most algorithms are randomized, and in almost all cases the approximation ratios obtained by current randomized algorithms are superior to the best results obtained by known deterministic algorithms. Derandomization of algorithms for general submodular function maximization seems hard since the access to the function is done via a value oracle. This makes it hard, for example, to apply standard derandomization techniques such as conditional expectations. Therefore, an interesting fundamental problem in this area is whether randomization is inherently necessary for obtaining good approximation ratios.In this work we give evidence that randomization is not necessary for obtaining good algorithms by presenting a new technique for derandomization of algorithms for submodular function maximization. Our high level idea is to maintain explicitly a (small) distribution over the states of the algorithm, and carefully update it using marginal values obtained from an extreme point solution of a suitable linear formulation. We demonstrate our technique on two recent algorithms for unconstrained submodular maximization and for maximizing submodular function subject to a cardinality constraint. In particular, for unconstrained submodular maximization we obtain an optimal deterministic 1/2-approximation showing that randomization is unnecessary for obtaining optimal results for this setting.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH29"}, {"primary_key": "4189818", "vector": [], "sparse_vector": [], "title": "An improved bound on the fraction of correctable deletions.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "We consider codes over fixed alphabets against worst-case symbol deletions. For any fixed k ≥ 2, we construct a family of codes over alphabet of size k with positive rate, which allow efficient recovery from a worst-case deletion fraction approaching . In particular, for binary codes, we are able to recover a fraction of deletions approaching 1/3. Previously, even non-constructively the largest deletion fraction known to be correctable with positive rate was , and around 0.17 for the binary case.Our result pins down the largest fraction of correctable deletions for k-ary codes as 1 – ⊝(1/k), since 1 – 1/k is an upper bound even for the simpler model of erasures where the locations of the missing symbols are known.Closing the gap between 1/3 and 1/2 for the limit of worst-case deletions correctable by binary codes remains a tantalizing open question.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH133"}, {"primary_key": "4189819", "vector": [], "sparse_vector": [], "title": "New Bounds for Approximating Extremal Distances in Undirected Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We provide new bounds for the approximation of extremal distances (the diameter, the radius, and the eccentricities of all nodes) of an undirected graph with n nodes and m edges. First, we show under the Strong Exponential Time Hypothesis (SETH) of I<PERSON>ag<PERSON><PERSON>, <PERSON><PERSON> and <PERSON> [JCSS01] that it is impossible to get a (3/2 – ∊)-approximation of the diameter or a (5/3 – ∊)-approximation of all the eccentricities in O(m2–δ) time for any ∊, δ > 0, even allowing for a constant additive term in the approximation. Second, we present an algorithmic scheme that gives a (2 – 1/2k)-approximation of the diameter and the radius and a (3 – 4/(2k + 1))-approximation of all eccentricities in expected time for any k ≥ 0. For k ≥ 2, this gives a family of previously unknown bounds, and approaches near-linear running time as k grows. Third, we observe a connection between the approximation of the diameter and the h-dominating sets, which are subsets of nodes at distance ≤ h from every other node. We give bounds for the size of these sets, related with the diameter.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH27"}, {"primary_key": "4189820", "vector": [], "sparse_vector": [], "title": "Sampling on Lattices with Free Boundary Conditions Using Randomized Extensions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Many statistical physics models are defined on an infinite lattice by taking appropriate limits of finite lattice regions, where a key consideration is how the boundaries are defined. For several models on planar lattices, such as 3-colorings and lozenge tilings, efficient sampling algorithms are known for regions with fixed boundary conditions, where the colors or tiles around the boundary are pre-specified [14], but much less is known about how to sample when these regions have free boundaries, where we want to include all configurations one could see within a finite window. We introduce a method using randomized extensions of a lattice region to relate sampling problems on regions with free boundaries to a constant number of sampling problems on larger regions with fixed boundaries. We demonstrate this principled approach to sample 3-colorings of regions of ℤ2 and lozenge tilings of regions of the triangular lattice, building on arguments for the fixed boundary cases due to <PERSON><PERSON> et al. [14]. Our approach also yields an efficient algorithm for sampling 3-colorings with free boundary conditions on regions with one reflex corner, the first such result for a nonconvex region. This approach can also be generalized to a broad class of mixed boundary conditions. Sampling for these families of regions is significant because it allows us to establish self-reducibility, giving the first algorithm to approximately count the total number of 3-colorings of rectangular lattice regions.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH137"}, {"primary_key": "4189821", "vector": [], "sparse_vector": [], "title": "Linear Recognition of Almost Interval Graphs.", "authors": ["<PERSON><PERSON>"], "summary": "Let interval + kv, interval+ ke, and interval – ke denote the classes of graphs that can be obtained from some interval graph by adding k vertices, adding k edges, and deleting k edges, respectively. When k is small, these graph classes are called almost interval graphs. They are well motivated from computational biology, where the data ought to be represented by an interval graph while we can only expect an almost interval graph for the best. For any fixed k, we give linear-time algorithms for recognizing all these classes, and in the case of membership, our algorithms provide also a specific interval graph as evidence. When k is part of the input, these problems are also known as graph modification problems, all NP-complete. Our results imply that they are fixed-parameter tractable parameterized by k, thereby resolving the long-standing open problem on the parameterized complexity of recognizing interval + ke, first asked by <PERSON><PERSON><PERSON><PERSON> et al. [Bioinformatics, 11:49–57, 1995]. Moreover, our algorithms for recognizing interval + kv and interval–ke run in times O(6k · (n+m)) and O(8k · (n+m)), (where n and m stand for the numbers of vertices and edges respectively in the input graph,) significantly improving the O(k2k· n3m)-time algorithm of <PERSON><PERSON><PERSON> et al. [STOC 2007; SICOMP 2009] and the O(10k· n9)-time algorithm of <PERSON> and <PERSON> [SODA 2014; TALG 2015] respectively.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH77"}, {"primary_key": "4189822", "vector": [], "sparse_vector": [], "title": "Incidence Geometries and the Pass Complexity of Semi-Streaming Set Cover.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Incidence Geometries and the Pass Complexity of Semi-Streaming Set CoverA<PERSON> and <PERSON> and <PERSON>.1365 - 1373<PERSON>hapter DOI:https://doi.org/10.1137/1.*************.ch94PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Set cover, over a universe of size n, may be modelled as a data-streaming problem, where the m sets that comprise the instance are to be read one by one. A semi-streaming algorithm is allowed only O(npoly{logn, logm}) space to process this stream. For each p ≥ 1, we give a very simple deterministic algorithm that makes p passes over the input stream and returns an appropriately certified (p + 1)n1/(p+1)-approximation to the optimum set cover. More importantly, we proceed to show that this approximation factor is essentially tight, by showing that a factor better than 0.99n1/(p+1)/(p + 1)2 is unachievable for a p-pass semi-streaming algorithm, even allowing randomisation. In particular, this implies that achieving a Θ(log n)-approximation requires Ω (log n/log log n) passes, which is tight up to the log log n factor. These results extend to a relaxation of the set cover problem where we are allowed to leave an ∊ fraction of the universe uncovered: the tight bounds on the best approximation factor achievable in p passes turn out to be Θp(min{n1/(p+1), ∊–1/p}). Our lower bounds are based on a construction of a family of high-rank incidence geometries, which may be thought of as vast generalisations of affine planes. This construction, based on algebraic techniques, appears flexible enough to find other applications and is therefore interesting in its own right. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH94"}, {"primary_key": "4189823", "vector": [], "sparse_vector": [], "title": "Improved Deterministic Algorithms for Linear Programming in Low Dimensions.", "authors": ["<PERSON>"], "summary": "At SODA'93, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> presented a derandomization of <PERSON>'s sampling-based algorithm [FOCS'88] for solving linear programs with n constraints and d variables in d(7+o(1))dn deterministic time. The time bound can be improved to d(5+o(1))dn with subsequent work by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [FOCS'93]. We first point out a much simpler derandomization of <PERSON>'s algorithm that avoids ∊-approximations and runs in d(3+o(1))dn time. We then describe a few additional ideas that eventually improve the deterministic time bound to d(1/2+o(1))dn.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH84"}, {"primary_key": "4189824", "vector": [], "sparse_vector": [], "title": "Reducing Curse of Dimensionality: Improved PTAS for TSP (with Neighborhoods) in Doubling Metrics.", "authors": ["T.<PERSON><PERSON><PERSON>", "Shaofeng H.-<PERSON><PERSON> Jiang"], "summary": "We consider the Traveling Salesman Problem with Neighborhoods (TSPN) in doubling metrics. The goal is to find a shortest tour that visits each of a given collection of subsets (regions or neighborhoods) in the underlying metric space.We give a randomized polynomial time approximation scheme (PTAS) when the regions are fat weakly disjoint. This notion of regions was first defined when a QPTAS was given for the problem in [SODA 2010: <PERSON> and <PERSON><PERSON>]. We combine the techniques in the previous work, together with the recent PTAS for TSP [STOC 2012: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and Kra<PERSON>gamer] to achieve a PTAS for TSPN.Moreover, more refined procedures are used to improve the dependence of the running time on the doubling dimension k from the previous exp[O(1)k2] (even for just TSP) to exp[O(1)O(k log k)].", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH54"}, {"primary_key": "4189825", "vector": [], "sparse_vector": [], "title": "Deterministic APSP, Orthogonal Vectors, and More: Quickly Derandomizing Razborov<PERSON><PERSON><PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We show how to solve all-pairs shortest paths on n nodes in deterministic time, and how to count the pairs of orthogonal vectors among n 0–1 vectors in d = clogn dimensions in deterministic n2–1/O(logc) time. These running times essentially match the best known randomized algorithms of (<PERSON>, STOC'14) and (<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>OD<PERSON> 2015) respectively, and the ability to count was open even for randomized algorithms. By reductions, these two results yield faster deterministic algorithms for many other problems. Our techniques can also be used to deterministically count k-SAT assignments on n variable formulas in 2n–n/O(k) time, roughly matching the best known running times for detecting satisfiability and resolving an open problem of <PERSON><PERSON><PERSON> (2013).A key to our constructions is an efficient way to deterministically simulate certain probabilistic polynomials critical to the algorithms of prior work, carefully applying small-biased sets and modulus-amplifying polynomials.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH87"}, {"primary_key": "4189826", "vector": [], "sparse_vector": [], "title": "Connectivity in bridge-addable graph classes: the McDiarmid-<PERSON>r-<PERSON> conjecture.", "authors": ["<PERSON>", "Guillem Perarnau"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Connectivity in bridge-addable graph classes: the McDiarmid-Steger-Welsh conjecture<PERSON><PERSON><PERSON><PERSON> and Guillem Perarnau<PERSON><PERSON><PERSON><PERSON> and Guillem Perarnaupp.1580 - 1588Chapter DOI:https://doi.org/10.1137/1.*************.ch107PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The study of typical properties of random graphs is of particular importance for the theoretical analysis of complex networks. In this field, many models of randomness (such as Erdős-Rényi or random planar graphs, preferential attachment models) have been successfully analysed thanks to the fact that their underlying structure enables one to perform explicit computations of some observables. Another approach, pioneered by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (2005) is to consider graphs taken uniformly from an abstract graph class, assuming only some global property of the class but without fully specifying it. Despite the fact that exact computations are no longer possible, results obtained in this setup are arguably very robust, since they apply universally for many different models of random graphs. The foundational and most studied problem in this topic is a conjecture of these authors on bridge-addable classes that we prove in this paper. A class of graphs is bridge-addable if any graph obtained by adding an edge between two connected components of a graph in the class, is also in the class. Examples of bridge-addable classes include forests, planar graphs, graphs with bounded tree-width, or graphs excluding any 2-connected minor. We prove that a random graph from a bridge-addable class is connected with probability at least e–1/2 + o(1), when its number of vertices tends to infinity. This lower bound is tight since it is reached for forests. The best previously known constants where e–1, e–0.7983 and e–2/3 proved respectively by McDiarmid, Steger and Welsh, by Balister, Bollobás and Gerke, and by Norin. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH107"}, {"primary_key": "4189827", "vector": [], "sparse_vector": [], "title": "Simple Pricing Schemes For Consumers With Evolving Values.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Balasubramanian <PERSON>"], "summary": "We consider a pricing problem where a buyer is interested in purchasing/using a good, such as an app or music or software, repeatedly over time. The consumer discovers his value for the good only as he uses it, and the value evolves with each use. Optimizing for the seller's revenue in such dynamic settings is a complex problem and requires assumptions about how the buyer behaves before learning his future value(s), and in particular, how he reacts to risk. We explore the performance of a class of pricing mechanisms that are extremely simple for both the buyer and the seller to use: the buyer reacts to prices myopically without worrying about how his value evolves in the future; the seller needs to optimize for revenue over a space of only two parameters, and can do so without knowing the buyer's risk profile or fine details of the value evolution process. We present simple-versus-optimal type results, namely that under certain assumptions, simple pricing mechanisms of the above form are approximately optimal regardless of the buyer's risk profile.Our results assume that the buyer's value per usage evolves as a martingale. For our main result, we consider pricing mechanisms in which the seller offers the product for free for a certain number of uses, and then charges an appropriate fixed price per usage. We assume that the buyer responds by buying the product for as long as his value exceeds the fixed price. Importantly, the buyer does not need to know anything about how his future value will evolve, only how much he wants to use the product right now. Regardless of the buyers' initial value, our pricing captures as revenue a constant fraction of the total value that the buyers accumulate in expectation over time.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH101"}, {"primary_key": "4189828", "vector": [], "sparse_vector": [], "title": "Near-Optimal Light Spanners.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A spanner if of a weighted undirected graph G is a \"sparse\" subgraph that approximately preserves distances between every pair of vertices in G. We refer to if as a δ-spanner of G for some parameter δ ≥ 1 if the distance in H between every vertex pair is at most a factor δ bigger than in G. In this case, we say that H has stretch δ. Two main measures of the sparseness of a spanner are the size (number of edges) and the total weight (the sum of weights of the edges in the spanner). It is well-known that for any positive integer k, one can efficiently construct a (2k-l)-spanner of G with 0(n1+1/k) edges where n is the number of vertices [2]. This size-stretch tradeoff is conjectured to be optimal based on a girth conjecture of Erdö<PERSON> [17]. However, the current state of the art for the second measure is not yet optimal. Recently <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> [ICALP 14] presented an improved analysis of the greedy algorithm, proving that the greedy algorithm admits (2k-1) · (1 + ∈) stretch and total edge weight of Oc((k/logk) · ω(MST(G)) · n1/k), where ω(MST(G)) is the weight of a minimum spanning tree of G. The previous analysis by <PERSON> et al. [SOCG 92] admitted (2k-1) · (1 + ∈) stretch and total edge weight of Ot(kω(MST(G))nl/k). Hence, <PERSON><PERSON> et al. improved the weight of the spanner by a log k factor. In this work, we complectly remove the k factor from the weight, presenting a spanner with (2k-1) · (1 + ∈) stretch, O∈(ω(MST(G))n1/k) total weight, and O(n1+1/k) edges. Up to a (1 + ∈) factor in the stretch this matches the girth conjecture of Erdös [17].", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH63"}, {"primary_key": "4189829", "vector": [], "sparse_vector": [], "title": "Simple and Fast Rounding Algorithms for Directed and Node-weighted Multiway Cut.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Simple and Fast Rounding Algorithms for Directed and Node-weighted Multiway CutChandra Chekuri and <PERSON><PERSON><PERSON> Chekuri and <PERSON><PERSON><PERSON>pp.797 - 807Chapter DOI:https://doi.org/10.1137/1.*************.ch57PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the multiway cut problem in directed graphs and one of its special cases, the node-weighted multiway cut problem in undirected graphs. In Directed Multiway Cut (Dir-MC) the input is an edge-weighted directed graph G = (V, E) and a set of k terminal nodes {s1, s2, …, sk} ⊆ V; the goal is to find a min-weight subset of edges whose removal ensures that there is no path from si to sj for any i ≠ j. In Node-weighted Multiway Cut (Node-wt-MC) the input is a node-weighted undirected graph G and a set of k terminal nodes {s1, s2, …, sk} ⊆ V; the goal is to find a min-weight subset of nodes whose removal ensures that there is no path from si to sj for any i ≠ j. <PERSON>r-<PERSON> admits a 2-approximation [28] and Node-wt-MC admits a -approximation [21], both via rounding of LP relaxations. Previous rounding algorithms for these problems, from nearly twenty years ago, are based on careful rounding of an optimum solution to an LP relaxation. This is particularly true for Dir-MC for which the rounding relies on a custom LP formulation instead of the natural distance based LP relaxation [28]. In this paper we describe extremely simple and near linear-time rounding algorithms for Dir-MC and Node-wt-MC via a natural distance based LP relaxation. The dual of this relaxation is a special case of the maximum multicommodity flow problem. Our algorithms achieve the same bounds as before but have the significant advantage in that they can work with any feasible solution to the relaxation. Consequently, in addition to obtaining “book” proofs of LP rounding for these two basic problems, we also obtain significantly faster approximation algorithms by taking advantage of known algorithms for computing near-optimal solutions for maximum multicommodity flow problems. We also investigate lower bounds for Dir-MC when k = 2 and prove that the integrality gap of the LP relaxation is 2 even in planar directed graphs. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH57"}, {"primary_key": "4189830", "vector": [], "sparse_vector": [], "title": "Constant Factor Approximation for Subset Feedback Set Problems via a new LP relaxation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider subset feedback edge and vertex set problems in undirected graphs. The input to these problems is an undirected graph G = (V, E) and a set S = {s1, s2, …, sk} ⊂ V of k terminals. A cycle in G is interesting if it contains a terminal. In the Subset Feedback Edge Set problem (Subset-FES) the input graph is edge-weighted and the goal is to remove a minimum weight set of edges such that no interesting cycle remains. In the Subset Feedback Vertex Set problem (Subset-FVS) the input graph is node-weighted and the goal is to remove a minimum weight set of nodes such that no interesting cycle remains.A 2-approximation is known for Subset-FES [12] and a 8-approximation is known for Subset-FVS [13]. The algorithm and analysis for Subset-FVS is complicated. One reason for the difficulty in addressing feedback set problems in undirected graphs has been the lack of LP relaxations with constant factor integrality gaps; the natural LP has an integrality gap of ⊝(log n).In this paper, we introduce new LP relaxations for Subset-FES and Subset-FVS and show that their integrality gap is at most 13. Our LP formulation and rounding are simple although the analysis is non-obvious.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH58"}, {"primary_key": "4189831", "vector": [], "sparse_vector": [], "title": "A Fast Approximation for Maximum Weight Matroid Intersection.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)A Fast Approximation for Maximum Weight Matroid IntersectionC<PERSON><PERSON> Chekuri and <PERSON> and <PERSON>pp.445 - 457Chapter DOI:https://doi.org/10.1137/1.*************.ch33PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present an approximation algorithm for the maximum weight matroid intersection problem in the independence oracle model. Given two matroids defined over a common ground set N of n elements, let k be the rank of the matroid intersection and let Q denote the cost of an independence query for either matroid. An exact algorithm for finding a maximum cardinality independent set (the unweighted case), due to <PERSON>, runs in O(nk1.5Q) time. For the weighted case, algorithms due to <PERSON> and <PERSON> et al. run in O(nk2Q) time. There are also scaling based algorithms that run in time, where W is the maximum weight (assuming all weights are integers), and ellipsoid-style algorithms that run in O((n2 log(n)Q + n3 polylog(n))log(nW)) time. Recently, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> described an algorithm that gives a (1 – ∊)-approximation for the weighted matroid intersection problem in O(nk1.5 log(k)Q/∊) time. We observe that a (1 – ∊)-approximation for the maximum cardinality case can be obtained in O(nkQ/∊) time by terminating Cunningham's algorithm early. Our main contribution is a (1 – ∊) approximation algorithm for the weighted matroid intersection problem with running time O(nk log2 (1/∊)Q/∊2). Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH33"}, {"primary_key": "4189832", "vector": [], "sparse_vector": [], "title": "Integrality Gaps and Approximation Algorithms for Dispersers and Bipartite Expanders.", "authors": ["<PERSON><PERSON>"], "summary": "We study the problem of approximating the quality of a disperser. A bipartite graph G on ([N], [M]) is a (ρN, (1 – δ)M)-disperser if for any subset S ⊆ [N] of size ρN, the neighbor set Γ(S) contains at least (1 – δ)M distinct vertices. Our main results are strong integrality gaps in the Lasserre hierarchy and an approximation algorithm for dispersers.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH105"}, {"primary_key": "4189833", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Hit<PERSON> and the Complexity of Blind Rendezvous.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We define and construct a novel pseudorandom tool, the Markovian hitter. Given an input sequence of n independent random bits, a Markovian hitter produces a sequence of pseudorandom samples in {0, 1}k, in an online fashion, that hits any subset W ⊂ {0, 1}k of size ∊2k with probability ≈ 1 – 2–(n–k)∊. This is comparable to the behavior of truly random samples or classical pseudorandom hitting sets. A Markovian hitter has an additional “Markovian” property of interest: each pseudorandom sample is a function of only the O(k) most recent bits of the input sequence (of random bits).Such Markovian properties are useful in distributed online settings. In particular, we apply Markovian hitters to obtain a new algorithm for the well-studied blind rendezvous problem for cognitive radios. This is the problem faced by two parties equipped with radios that can access channels in potentially different subsets, S1 and S2, of a universe of n channels. Their challenge is to discover each other (by tuning their radios to the same channel at the same time) as quickly as possible. In prior work [3] it was shown that deterministic schedules have a lower bound for rendezvous time of Ω(|S1| · |S2|). We beat this quadratic barrier by utilizing a public source of randomness in conjunction with a Markovian hitter to achieve rendezvous in expected timeWe counterbalance this result by establishing two lower bounds on expected rendezvous time: anbound for the setting with public randomness, and an Ω(|S1| · |S2|) bound in the setting with private randomness but no public randomness, which is a strengthening of the result for deterministic schedules.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH45"}, {"primary_key": "4189834", "vector": [], "sparse_vector": [], "title": "Partial Resampling to Approximate Covering Integer Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider positive covering integer programs, which generalize set cover and which have attracted a long line of research developing (randomized) approximation algorithms. <PERSON><PERSON><PERSON><PERSON> (2006) gave a rounding algorithm based on the FKG inequality for systems which are \"column-sparse.\" This algorithm may return an integer solution in which the variables get assigned large (integral) values; <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> (2005) modified this algorithm to limit the solution size, at the cost of a worse approximation ratio. We develop a new rounding scheme based on the Partial Resampling variant of the Lovász Local Lemma developed by <PERSON> (2013). This achieves an approximation ratio of , where amin is the minimum covering constraint and Δ1 is the maximum ℓ1-norm of any column of the covering matrix (whose entries are scaled to lie in [0, 1]); we also show nearly-matching inapproximability and integrality-gap lower bounds.Our approach improves asymptotically, in several different ways, over known results. First, it replaces Δ0, the maximum number of nonzeroes in any column (from the result of <PERSON><PERSON><PERSON><PERSON>) by Δ1 which is always – and can be much – smaller than Δ0; this is the first such result in this context. Second, our algorithm automatically handles multi-criteria programs; we achieve improved approximation ratios compared to the algorithm of <PERSON><PERSON><PERSON><PERSON>, and give, for the first time when the number of objective functions is large, polynomial-time algorithms with good multi-criteria approximations. We also significantly improve upon the upper-bounds of <PERSON><PERSON><PERSON><PERSON><PERSON> & <PERSON> when the integer variables are required to be within (1 + ∊) of some given upper-bounds, and show nearly-matching inapproximability.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH139"}, {"primary_key": "4189835", "vector": [], "sparse_vector": [], "title": "An O(log m)-Competitive Algorithm for Online Machine Minimization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)An ℴ(log m)-Competitive Algorithm for Online Machine Minimization<PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.155 - 163Chapter DOI:https://doi.org/10.1137/1.*************.ch12PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the online machine minimization problem in which jobs with hard deadlines arrive online over time at their release dates. The task is to determine a feasible preemptive schedule on a minimum number of machines. Our main result is a general ℴ(log m)-competitive algorithm for the online problem, where m is the optimal number of machines used in an offline solution. This is the first improvement on an intriguing problem in nearly two decades. To date, the best known result is a ℴ(log(pmax/pmin))-competitive algorithm by <PERSON> et al. (STOC 1997) that depends on the ratio of maximum and minimum job sizes, pmax and pmin. Even for m = 2 no better algorithm was known. Our algorithm is in this case constant-competitive. When applied to laminar or agreeable instances, our algorithm achieves a competitive ratio of ℴ(1) even independently of m. The following two key components lead to our new result. Firstly, we derive a new lower bound on the optimum value that relates the laxity and the number of jobs with intersecting time windows. Then, we design a new algorithm that is tailored to this lower bound and balances the delay of jobs by taking the number of currently running jobs into account. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH12"}, {"primary_key": "4189836", "vector": [], "sparse_vector": [], "title": "Tight Bounds for the Distribution-Free Testing of Monotone Conjunctions.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We improve both upper and lower bounds for the distribution-free testing of monotone conjunctions. Given oracle access to an unknown Boolean function f: {0, 1}n → {0, 1} and sampling oracle access to an unknown distribution over {0, 1}n, we present an Õ(n1/3/∊5)-query algorithm that tests whether f is a monotone conjunction versus ∊-far from any monotone conjunction with respect to . This improves the previous best upper bound of Õ(n1/2/∊) by <PERSON><PERSON> and <PERSON> [DR11], when 1/∊ is small compared to n. For some constant ∊0 > 0, we also prove a lower bound of for the query complexity, improving the previous best lower bound of by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [GS09]. Our upper and lower bounds are tight, up to a polylogarithmic factor, when the distance parameter ∊ is a constant. Furthermore, the same upper and lower bounds can be extended to the distribution-free testing of general conjunctions, and the lower bound can be extended to that of decision lists and linear threshold functions.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH5"}, {"primary_key": "4189837", "vector": [], "sparse_vector": [], "title": "Nearly Optimal Deterministic Algorithm for Sparse Walsh-Hadamard Transform.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For every fixed constant α > 0, we design an algorithm for computing the k-sparse Walsh-<PERSON> transform (i.e., Discrete Fourier Transform over the Boolean cube) of an N-dimensional vector x ∊ ℝN in time k1+α(log N)O(1) Specifically, the algorithm is given query access to x and computes a k-sparse ∊ ℝN satisfying , for an absolute constant c > 0, where is the transform of x and is its best k-sparse approximation. Our algorithm is fully deterministic and only uses non-adaptive queries to x (i.e., all queries are determined and performed in parallel when the algorithm starts).An important technical tool that we use is a construction of nearly optimal and linear lossless condensers which is a careful instantiation of the GUV condenser (<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, JACM 2009). Moreover, we design a deterministic and non-adaptive ℓ1/ℓ1 compressed sensing scheme based on general lossless condensers that is equipped with a fast reconstruction algorithm running in time k1+α(log N)O(1) (for the GUV-based condenser) and is of independent interest. Our scheme significantly simplifies and improves an earlier expander-based construction due to <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> (<PERSON> 2008).Our methods use linear lossless condensers in a black box fashion; therefore, any future improvement on explicit constructions of such condensers would immediately translate to improved parameters in our framework (potentially leading to k(log N)O(1) reconstruction time with a reduced exponent in the poly-logarithmic factor, and eliminating the extra parameter α).By allowing the algorithm to use randomness, while still using non-adaptive queries, the running time of the algorithm can be improved to Õ(k log3 N).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH23"}, {"primary_key": "4189838", "vector": [], "sparse_vector": [], "title": "Kernelization via Sampling with Applications to Finding Matchings and Related Problems in Dynamic Graph Streams.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we present a simple but powerful subgraph sampling primitive that is applicable in a variety of computational models including dynamic graph streams (where the input graph is defined by a sequence of edge/hyperedge insertions and deletions) and distributed systems such as MapReduce. In the case of dynamic graph streams, we use this primitive to prove the following results:•Matching: Our main result for matchings is that there exists an Õ(k2) space algorithm that returns the edges of a maximum matching on the assumption the cardinality is at most k. The best previous algorithm used Õ(kn) space where n is the number of vertices in the graph and we prove our result is optimal up to logarithmic factors. Our algorithm has Õ(1) update time. We also show that there exists an Õ(n2/α3) space algorithm that returns an α-approximation for matchings of arbitrary size. In independent work, <PERSON><PERSON><PERSON> et al. (SODA 2016) proved this approximation algorithm is optimal and provided an alternative algorithm. We generalize our exact and approximate algorithms to weighted matching. For graphs with low arboricity such as planar graphs, the space required for constant approximation can be further reduced. While there has been a substantial amount of work on approximate matching in insert-only graph streams, these are the first nontrivial results in the dynamic setting.•Vertex Cover and Hitting Set: There exists an Õ(kd) space algorithm that solves the minimum hitting set problem where d is the cardinality of the input sets and k is an upper bound on the size of the minimum hitting set. We prove this is optimal up to logarithmic factors. Our algorithm has Õ(1) update time. The case d = 2 corresponds to minimum vertex cover.Finally, we consider a larger family of parameterized problems (including b-matching, disjoint paths, vertex coloring among others) for which our subgraph sampling primitive yields fast, small-space dynamic graph stream algorithms. We then show lower bounds for natural problems outside this family.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH92"}, {"primary_key": "4189839", "vector": [], "sparse_vector": [], "title": "Obstructions for three-coloring graphs with one forbidden induced subgraph.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The complexity of coloring graphs without long induced paths is a notorious problem in algorithmic graph theory, an especially intruiging case being that of 3-colorability. So far, not much was known about certification in this context.We prove that there are only finitely many 4-critical P6-free graphs, and give the complete list that consists of 24 graphs. In particular, we obtain a certifying algorithm for 3-coloring P6-free graphs, which solves an open problem posed by <PERSON><PERSON><PERSON> et al. Here, P6 denotes the induced path on six vertices.Our result leads to the following dichotomy theorem: if H is a connected graph, then there are finitely many 4-critical H-free graphs if and only if H is a subgraph of P6. This answers a question of <PERSON>. The proof of our main result involves two distinct automatic proofs, and an extensive structural analysis by hand.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH123"}, {"primary_key": "4189840", "vector": [], "sparse_vector": [], "title": "Weak duality for packing edge-disjoint odd (u, v)-trails.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite <PERSON><PERSON>'s famous duality between packings and coverings of (u, v)-paths in a graph, there is no duality when we require the paths be odd: a graph with no two edge-disjoint odd (u, v)-paths may need an arbitrarily large number of edges to cover all such paths. In this paper, we study the relaxed problem of packing odd trails. Our main result is an approximate duality for odd trails: if ν(u, v) denotes the maximum number of edge-disjoint (u, v)-trails of odd length in a graph G and τ(u, v) denotes the minimum number of edges that intersect every such trail, thenThe proof leads to a polynomial-time algorithm to find, for any given k, either k edge-disjoint odd (u, v)-trails or a set of fewer than 8k edges intersecting all odd (u, v)-trails. This yields a constant factor approximation algorithm for the packing number ν(u, v).This result generalizes to the setting of signed graphs and to the setting of group-labelled graphs, in which case “odd length” is replaced by “non-unit product of labels”. The motivation for this result comes from the study of totally odd graph immersions, and our results explain, in particular, why there is an essential difference between the totally odd weak and strong immersions.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH145"}, {"primary_key": "4189841", "vector": [], "sparse_vector": [], "title": "The k-mismatch problem revisited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We revisit the complexity of one of the most basic problems in pattern matching. In the k-mismatch problem we must compute the Hamming distance between a pattern of length m and every m-length substring of a text of length n, as long as that Hamming distance is at most k. Where the Hamming distance is greater than k at some alignment of the pattern and text, we simply output \"No\".We study this problem in both the standard offline setting and also as a streaming problem. In the streaming k-mismatch problem the text arrives one symbol at a time and we must give an output before processing any future symbols. Our main results are as follows:•Our first result is a deterministic O(nk2 log k/m + n polylog m) time offline algorithm for k-mismatch on a text of length n. This is a factor of k improvement over the fastest previous result of this form from SODA 2000 [9, 10].•We then give a randomised and online algorithm which runs in the same time complexity but requires only O(k2 polylog m) space in total.•Next we give a randomised (1 + ∊)-approximation algorithm for the streaming k-mismatch problem which uses O(k2 polylog m/∊2) space and runs in O(polylog m/∊2) worst-case time per arriving symbol.•Finally we combine our new results to derive a randomised O(k2 polylog m) space algorithm for the streaming k-mismatch problem which runs in worst-case time per arriving symbol. This improves the best previous space complexity for streaming k-mismatch from FOCS 2009 [26] by a factor of k. We also improve the time complexity of this previous result by an even greater factor to match the fastest known offline algorithm (up to logarithmic factors).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH142"}, {"primary_key": "4189842", "vector": [], "sparse_vector": [], "title": "Nearly Tight Oblivious Subspace Embeddings by Trace Inequalities.", "authors": ["<PERSON>"], "summary": "We present a new analysis of sparse oblivious subspace embeddings, based on the \"matrix Ch<PERSON>off\" technique. These are probability distributions over (relatively) sparse matrices such that for any d-dimensional subspace of Rn, the norms of all vectors in the subspace are simultaneously approximately preserved by the embedding with high probability–typically with parameters depending on d but not on n. The families of embedding matrices considered here are essentially the same as those in [NN13], but with better parameters (sparsity and embedding dimension). Because of this, this analysis essentially serves as a \"drop-in replacement\" for <PERSON><PERSON><PERSON><PERSON>'s, improving bounds on its many applications to problems such as as least squares regression and low-rank approximation.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH21"}, {"primary_key": "4189843", "vector": [], "sparse_vector": [], "title": "Approximately Efficient Double Auctions with Strong Budget Balance.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mechanism design for one-sided markets is an area of extensive research in economics and, since more than a decade, in computer science as well. Two-sided markets, on the other hand, have not received the same attention despite the numerous applications to web advertisement, stock exchange, and frequency spectrum allocation. This work studies double auctions, in which unit-demand buyers and unit-supply sellers act strategically.An ideal goal in double auction design is to maximize the social welfare of buyers and sellers with individually rational (IR), incentive compatible (IC) and strongly budget-balanced (SBB) mechanisms. The first two properties are standard. SBB requires that the payments charged to the buyers are entirely handed to the sellers. This property is crucial in all the contexts that do not allow the auctioneer retaining a share of buyers' payments or subsidizing the market.Unfortunately, this goal is known to be unachievable even for the special case of bilateral trade, where there is only one buyer and one seller. Therefore, in subsequent papers, meaningful trade-offs between these requirements have been investigated.Our main contribution is the first IR, IC and SBB mechanism that provides an O(1)-approximation to the optimal social welfare. This result holds for any number of buyers and sellers with arbitrary, independent distributions. Moreover, our result continues to hold when there is an additional matroid constraint on the sets of buyers who may get allocated an item. To prove our main result, we devise an extension of sequential posted price mechanisms to two-sided markets. In addition to this, we improve the best-known approximation bounds for the bilateral trade problem.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH98"}, {"primary_key": "4189844", "vector": [], "sparse_vector": [], "title": "Tight conditional lower bounds for counting perfect matchings on graphs of bounded treewidth, cliquewidth, and genus.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "By now, we have a good understanding of how NP-hard problems become easier on graphs of bounded treewidth and bounded cliquewidth: for various problems, matching upper bounds and conditional lower bounds describe exactly how the running time has to depend on treewidth or cliquewidth. In particular, <PERSON><PERSON><PERSON> et al. (2009, 2010) have shown a significant difference between these two parameters: assuming the Exponential-Time Hypothesis (ETH), the optimal algorithms for problems such as Max Cut and Edge Dominating Set have running time 2O(t) nO(1) when parameterized by treewidth, but nO(t) when parameterized by cliquewidth.In this paper, we show that a similar phenomenon occurs also for counting problems. Specifically, we prove that, assuming the counting version of the Strong Exponential-Time Hypothesis (#SETH), the problem of counting perfect matchings•has no (2 – ∊)knO(1) time algorithm for any ∊ > 0 on graphs of treewidth k (but it is known to be solvable in time 2knO(1) if a tree decomposition of width k is given), and•has no O(n(1–∊)k) time algorithm for any ∊ > 0 on graphs of cliquewidth k (but it can be solved in time O(nk+1) if a k-expression is given).A celebrated result of <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> from the 1960s shows that counting perfect matchings in planar graphs is polynomial-time solvable. This was later extended by <PERSON><PERSON><PERSON><PERSON> and <PERSON> (1999), <PERSON><PERSON> (2000) and <PERSON><PERSON> and <PERSON>echina (2000) who gave 4k · nO(1) time algorithms for graphs of genus k. We show that the dependence on the genus k has to be exponential: assuming #ETH, the counting version of ETH, there is no 2o(k) · nO(1) time algorithm for the problem on graphs of genus k.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH113"}, {"primary_key": "4189845", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Graph Homomorphism and Subgraph Isomorphism.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Arkadius<PERSON>"], "summary": "We prove that unless Exponential Time Hypothesis (ETH) fails, deciding if there is a homomorphism from graph G to graph H cannot be done in time |V (H)|o(|V (G)|). We also show an exponential-time reduction from Graph Homomorphism to Subgraph Isomorphism. This rules out (subject to ETH) a possibility of |V (H)|o(|V (H)|)-time algorithm deciding if graph G is a subgraph of H. For both problems our lower bounds asymptotically match the running time of brute-force algorithms trying all possible mappings of one graph into another. Thus, our work closes the gap in the known complexity of these fundamental problems.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH112"}, {"primary_key": "4189846", "vector": [], "sparse_vector": [], "title": "Online Pricing with Impatient Bidders.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we consider the following online pricing problem. An auctioneer is selling identical items in unlimited supply, whereas each bidder from a given set is interested in purchasing a single copy of the item. Each bidder is characterized by a budget and a time interval, in which he is considering to buy the item. Bidders are willing to buy the item at the earliest time provided it is within their time intervals and the price at that time is within their budgets. We call such bidders impatient bidders. The problem is considered in the online setting, i.e., each bidder arrives at the start of his time interval, and only then an algorithm learns of his existence and his budget. The goal of the seller is to set the price of the item over time so that the total revenue is maximized.We study two versions of the impatient bidders problem: the one introduced by <PERSON><PERSON> et al. [TALG'10], and a more restricted setting in which the deadline of each bidder remains unknown until it is hit. We give tight bounds for both settings. Rather surprisingly, in both cases the optimum competitive ratios are the same. In particular we prove that the competitive ratio of an optimum deterministic algorithm is ⊝(log h/log log h), whereas for randomized algorithms it is ⊝(log log h).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH15"}, {"primary_key": "4189847", "vector": [], "sparse_vector": [], "title": "The Power of Two Choices with Simple Tabulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The power of two choices is a classic paradigm for load balancing when assigning m balls to n bins. When placing a ball, we pick two bins according to two hash functions ho and h1, and place the ball in the least loaded bin. Assuming fully random hash functions, when m = O(n), <PERSON><PERSON> et al. [STOC'94] proved that the maximum load is lglgn + 0(1) with high probability. No such bound was known with a hash function implementable in constant time. In this paper, we investigate the power of two choices when the hash functions ho and h1 are implemented with simple tabulation, which is a very efficient hash function evaluated in constant time. Following their analysis of Cuckoo hashing [J.ACM'12], <PERSON> and <PERSON><PERSON> claimed that the expected maximum load with simple tabulation is O(lglgn). This did not include any high probability guarantee, so the load balancing was not yet to be trusted. Here, we show that with simple tabulation, the maximum load is O(lglgn) with high probability, giving the first constant time hash function with this guarantee. We also give a concrete example where, unlike with fully random hashing, the maximum load is not bounded by lglgn + 0(l), or even (1 + o(l)) lglgn with high probability. Finally, we show that the expected maximum load is lglgn + 0(1), just like with fully random hashing.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH111"}, {"primary_key": "4189848", "vector": [], "sparse_vector": [], "title": "Species Trees from Gene Trees Despite a High Rate of Lateral Genetic Transfer: A Tight Bound (Extended Abstract).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reconstructing the tree of life from molecular sequences is a fundamental problem in computational biology. Modern data sets often contain a large number of genes which can complicate the reconstruction problem due to the fact that different genes may undergo different evolutionary histories. This is the case in particular in the presence of lateral genetic transfer (LGT), whereby a gene is inherited from a distant species rather than an immediate ancestor. Such an event produces a gene tree which is distinct from (but related to) the species phylogeny. In previous work, a stochastic model of LGT was introduced and it was shown that the species phylogeny can be reconstructed from gene trees despite surprisingly high rates of LGT. Both lower and upper bounds on this rate were obtained, but a large gap remained. Here we close this gap, up to a constant. Specifically, we show that the species phylogeny can be reconstructed perfectly even when each edge of the tree has a constant probability of being the location of an LGT event. Our new reconstruction algorithm builds the tree recursively from the leaves. We also provide a matching bound in the negative direction (up to a constant).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH110"}, {"primary_key": "4189849", "vector": [], "sparse_vector": [], "title": "Online Degree-Bounded Steiner Network Design.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We initiate the study of degree-bounded network design problems in the online setting. The degree-bounded Steiner tree problem – which asks for a subgraph with minimum degree that connects a given set of vertices – is perhaps one of the most representative problems in this class. This paper deals with its well-studied generalization called the degree-bounded Steiner forest problem where the connectivity demands are represented by vertex pairs that need to be individually connected. In the classical online model, the input graph is given offline but the demand pairs arrive sequentially in online steps. The selected subgraph starts off as the empty subgraph, but has to be augmented to satisfy the new connectivity constraint in each online step. The goal is to be competitive against an adversary that knows the input in advance.The standard techniques for solving degree-bounded problems often fall in the category of iterative and dependent rounding techniques. Unfortunately, these rounding methods are inherently difficult to adapt to an online settings since the underlying fractional solution may change dramatically in between the rounding steps. Indeed, this might be the very reason that despite many advances in the online network design paradigm in the past two decades, the natural family of degree-bounded problems has remained widely open.In this paper, we design an intuitive greedy-like algorithm that achieves a competitive ratio of O(log n) where n is the number of vertices. We show that no (randomized) algorithm can achieve a (multiplicative) competitive ratio o(log n); thus our result is asymptotically tight. We further show strong hardness results for the group Steiner tree and the edge-weighted variants of degree-bounded connectivity problems.<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> resolved the offline variant of degree-bounded Steiner forest in their paper in SODA'92. Since then, the family of degree-bounded network design problems has been extensively studied in the literature resulting in the development of many interesting tools and numerous papers on the topic. We hope that our approach and its dual analysis, paves the way for solving the online variants of the classical problems in this family of problems.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH13"}, {"primary_key": "4189850", "vector": [], "sparse_vector": [], "title": "Multiscale Mapper: Topological Summarization via Codomain Covers.", "authors": ["<PERSON><PERSON>", "Facundo <PERSON>", "<PERSON><PERSON>"], "summary": "Summarizing topological information from datasets and maps defined on them is a central theme in topological data analysis. Mapper, a tool for such summarization, takes as input both a possibly high dimensional dataset and a map defined on the data, and produces a summary of the data by using a cover of the codomain of the map. This cover, via a pullback operation to the domain, produces a simplicial complex connecting the data points.The resulting view of the data through a cover of the codomain offers flexibility in analyzing the data. However, it offers only a view at a fixed scale at which the cover is constructed. Inspired by the concept, we explore a notion of a tower of covers which induces a tower of simplicial complexes connected by simplicial maps, which we call multiscale mapper. We study the resulting structure, and design practical algorithms to compute its persistence diagrams efficiently. Specifically, when the domain is a simplicial complex and the map is a real-valued piecewise-linear function, the algorithm can compute the exact persistence diagram only from the 1-skeleton of the input complex. For general maps, we present a combinatorial version of the algorithm that acts only on vertex sets connected by the 1-skeleton graph, and this algorithm approximates the exact persistence diagram thanks to a stability result that we show to hold.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH71"}, {"primary_key": "4189851", "vector": [], "sparse_vector": [], "title": "Approximating Low-Stretch Spanners.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximating Low-Stretch Spanners<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.821 - 840Chapter DOI:https://doi.org/10.1137/1.*************.ch59PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Despite significant recent progress on approximating graph spanners (subgraphs which approximately preserve distances), there are still several large gaps in our understanding. We give new results for two of them: approximating basic k-spanner (particularly for small k), and the dependence on f when approximating f-fault tolerant spanners. We first design an Õ(n1/3)-approximation for 4-spanner (both basic and directed). This was the last value of k for which only an -approximation was known for basic k-spanner, and thus implies that for any k the approximation ratio is at most Õ(n1/3). For basic k-spanner, we also show an integrality gap for the natural flow-based LP (the main tool in almost all nontrivial spanner approximations) which nearly matches the trivial approximation of . For f-fault tolerant spanners, we show that in the small-stretch setting (k ∊ {3, 4}) it is possible to entirely remove the dependence on f from the approximation ratio, at the cost of moving to bicriteria guarantees. The previous best dependence on f was either almost-linear (in the undirected setting) or exponential (in the directed setting for stretch 4). Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH59"}, {"primary_key": "4189852", "vector": [], "sparse_vector": [], "title": "Undirected Graph Exploration with ⊝(log log n) Pebbles.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the fundamental problem of exploring an undirected and initially unknown graph by an agent with little memory. The vertices of the graph are unlabeled, and the edges incident to a vertex have locally distinct labels. In this setting, it is known that ⊝(log n) bits of memory are necessary and sufficient to explore any graph with at most n vertices. We show that this memory requirement can be decreased significantly by making a part of the memory distributable in the form of pebbles. A pebble is a device that can be dropped to mark a vertex and can be collected when the agent returns to the vertex. We show that for an agent ℴ(log log n) distinguishable pebbles and bits of memory are sufficient to explore any bounded-degree graph with at most n vertices. We match this result with a lower bound exhibiting that for any agent with sub-logarithmic memory, Ω(log log n) distinguishable pebbles are necessary for exploration.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH3"}, {"primary_key": "4189853", "vector": [], "sparse_vector": [], "title": "Clustering time series under the <PERSON><PERSON><PERSON> distance.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Clustering time series under the Fréchet distance<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.766 - 785Chapter DOI:https://doi.org/10.1137/1.*************.ch55PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The Fréchet distance is a popular distance measure for curves. We study the problem of clustering time series under the Fréchet distance. In particular, we give (1 + ∊)-approximation algorithms for variations of the following problem with parameters k and ℓ. Given n univariate time series P, each of complexity at most m, we find k time series, not necessarily from P, which we call cluster centers and which each have complexity at most ℓ, such that (a) the maximum distance of an element of P to its nearest cluster center or (b) the sum of these distances is minimized. Our algorithms have running time near-linear in the input size for constant ∊, k and ℓ. To the best of our knowledge, our algorithms are the first clustering algorithms for the Fréchet distance which achieve an approximation factor of (1 + ∊) or better. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106Key words:time series, longitudinal data, functional data, clustering, Fréchet distance, dynamic time warping, approximation algorithms", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH55"}, {"primary_key": "4189854", "vector": [], "sparse_vector": [], "title": "An Improved Combinatorial Polynomial Algorithm for the Linear Arrow-Debreu Market.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present an improved combinatorial algorithm for the computation of equilibrium prices in the linear Arrow-<PERSON><PERSON><PERSON> model. For a market with n agents and integral utilities bounded by U, the algorithm runs in O(n7 log3(nU)) time. This improves upon the previously best algorithm of Ye by a factor of . The algorithm refines the algorithm described by <PERSON><PERSON> and <PERSON><PERSON> and improves it by a factor of . The improvement comes from a better understanding of the iterative price adjustment process, the improved balanced flow computation for nondegenerate instances, and a novel perturbation technique for achieving nondegeneracy.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH7"}, {"primary_key": "4189855", "vector": [], "sparse_vector": [], "title": "On the switch <PERSON><PERSON> chain for perfect matchings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study a simple Markov chain, the switch chain, on the set of all perfect matchings in a bipartite graph. This Markov chain was proposed by <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> as a possible approach to a sampling problem arising in Statistics. They considered several classes of graphs, and conjectured that the switch chain would mix rapidly for graphs in these classes. Here we settle their conjecture almost completely. We ask: for which graph classes is the Markov chain ergodic and for which is it rapidly mixing? We provide a precise answer to the ergodicity question and close bounds on the mixing question. We show for the first time that the mixing time of the switch chain is polynomial in the class of monotone graphs. This class was identified by <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> as being of particular interest in the statistical setting.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH138"}, {"primary_key": "4189856", "vector": [], "sparse_vector": [], "title": "Treetopes and their Graphs.", "authors": ["<PERSON>"], "summary": "We define treetopes, a generalization of the three-dimensional roofless polyhedra (<PERSON><PERSON> graphs) to arbitrary dimensions. Like roofless polyhedra, treetopes have a designated base facet such that every face of dimension greater than one intersects the base in more than one point. We prove an equivalent characterization of the 4-treetopes using the concept of clustered planarity from graph drawing, and we use this characterization to recognize the graphs of 4-treetopes in polynomial time. This result provides one of the first classes of 4-polytopes, other than pyramids and stacked polytopes, that can be recognized efficiently from their graphs.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH69"}, {"primary_key": "4189857", "vector": [], "sparse_vector": [], "title": "The Complexity of All-switches Strategy Improvement.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Strategy improvement is a widely-used and well-studied class of algorithms for solving graph-based infinite games. These algorithms are parametrized by a switching rule, and one of the most natural rules is \"all switches\" which switches as many edges as possible in each iteration. Continuing a recent line of work, we study all-switches strategy improvement from the perspective of computational complexity. We consider two natural decision problems, both of which have as input a game G, a starting strategy s, and an edge e. The problems are: 1. The edge switch problem, namely, is the edge e ever switched by all-switches strategy improvement when it is started from s on game G? 2. The optimal strategy problem, namely, is the edge e used in the final strategy that is found by strategy improvement when it is started from s on game G? We show PSPACE-completeness of the edge switch problem and optimal strategy problem for the following settings: Parity games with the discrete strategy improvement algorithm of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>; mean-payoff games with the gain-bias algorithm [11, 33]; and discounted-payoff games and simple stochastic games with their standard strategy improvement algorithms. We also show PSPACE-completeness of an analogous problem to edge switch for the bottom-antipodal algorithm for Acyclic Unique Sink Orientations on Cubes.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH10"}, {"primary_key": "4189858", "vector": [], "sparse_vector": [], "title": "Online Contention Resolution Schemes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a new rounding technique designed for online optimization problems, which is related to contention resolution schemes, a technique initially introduced in the context of submodular function maximization. Our rounding technique, which we call online contention resolution schemes (OCRSs), is applicable to many online selection problems, including Bayesian online selection, oblivious posted pricing mechanisms, and stochastic probing models. It allows for handling a wide set of constraints, and shares many strong properties of offline contention resolution schemes. In particular, OCRSs for different constraint families can be combined to obtain an OCRS for their intersection. Moreover, we can approximately maximize submodular functions in the online settings we consider.We, thus, get a broadly applicable framework for several online selection problems, which improves on previous approaches in terms of the types of constraints that can be handled, the objective functions that can be dealt with, and the assumptions on the strength of the adversary. Furthermore, we resolve two open problems from the literature; namely, we present the first constant-factor constrained oblivious posted price mechanism for matroid constraints, and the first constant-factor algorithm for weighted stochastic probing with deadlines.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH72"}, {"primary_key": "4189859", "vector": [], "sparse_vector": [], "title": "The complexity of approximately counting in 2-spin systems on k-uniform bounded-degree hypergraphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "One of the most important recent developments in the complexity of approximate counting is the classification of the complexity of approximating the partition functions of antiferromagnetic 2-spin systems on bounded-degree graphs. This classification is based on a beautiful connection to the so-called uniqueness phase transition from statistical physics on the infinite Δ-regular tree. Our objective is to study the impact of this classification on unweighted 2-spin models on k-uniform hypergraphs. As has already been indicated by <PERSON> and <PERSON>, the connection between the uniqueness phase transition and the complexity of approximate counting breaks down in the hypergraph setting. Nevertheless, we show that for every non-trivial symmetric k-ary Boolean function f there exists a degree bound Δ0 so that for all Δ ≥ Δ0 the following problem is NP-hard: given a k-uniform hypergraph with maximum degree at most Δ, approximate the partition function of the hypergraph 2-spin model associated with f. It is NP-hard to approximate this partition function even within an exponential factor. By contrast, if f is a trivial symmetric Boolean function (e.g., any function f that is excluded from our result), then the partition function of the corresponding hypergraph 2-spin model can be computed exactly in polynomial time.The full version of this paper is available at arxiv.org/abs/1505.06146. For the convenience of the reader, we have also included in the theorem statements the number of the respective theorem in the full version.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH34"}, {"primary_key": "4189860", "vector": [], "sparse_vector": [], "title": "Discovering Archipelagos of Tractability for Constraint Satisfaction and Counting.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "The Constraint Satisfaction Problem (CSP) is a central and generic computational problem which provides a common framework for many theoretical and practical applications. A central line of research is concerned with the identification of classes of instances for which CSP can be solved in polynomial time; such classes are often called “islands of tractability.” A prominent way of defining islands of tractability for CSP is to restrict the relations that may occur in the constraints to a fixed set, called a constraint language, whereas a constraint language is conservative if it contains all unary relations. <PERSON><PERSON><PERSON><PERSON>'s famous Dichotomy Theorem (STOC 1978) identifies all islands of tractability in terms of tractable constraint languages over a Boolean domain of values. Since then many extensions and generalizations of this result have been obtained. Recently, <PERSON><PERSON><PERSON> (TOCL 2011, JACM 2013) gave a full characterization of all islands of tractability for CSP and the counting version #CSP that are defined in terms of conservative constraint languages.This paper addresses the general limit of the mentioned tractability results for CSP and #CSP, that they only apply to instances where all constraints belong to a single tractable language (in general, the union of two tractable languages isn't tractable). We show that we can overcome this limitation as long as we keep some control of how constraints over the various considered tractable languages interact with each other. For this purpose we utilize the notion of a strong backdoor of a CSP instance, as introduced by <PERSON> et al. (<PERSON>JCAI 2003), which is a set of variables that when instantiated moves the instance to an island of tractability, i.e., to a tractable class of instances. We consider strong backdoors into scattered classes, consisting of CSP instances where each connected component belongs entirely to some class from a list of tractable classes. Figuratively speaking, a scattered class constitutes an archipelago of tractability. The main difficulty lies in finding a strong backdoor of given size k; once it is found, we can try all possible instantiations of the backdoor variables and apply the polynomial time algorithms associated with the islands of tractability on the list component wise. Our main result is an algorithm that, given a CSP instance with n variables, finds in time f(k)nℴ(1) a strong backdoor into a scattered class (associated with a list of finite conservative constraint languages) of size k or correctly decides that there isn't such a backdoor. This also gives the running time for solving (#)CSP, provided that (#)CSP is polynomial-time tractable for the considered constraint languages. Our result makes significant progress towards the main goal of the backdoor-based approach to CSPs – the identification of maximal base classes for which small backdoors can be detected efficiently.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH114"}, {"primary_key": "4189861", "vector": [], "sparse_vector": [], "title": "Raising The Bar For Vertex Cover: Fixed-parameter Tractability Above A Higher Guarantee.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The standard parameterization of the Vertex Cover problem (Given an undirected graph G and k ∊ ℕ as input, does G have a vertex cover of size at most k?) has the solution size k as the parameter. The following more challenging parameterization of Vertex Cover stems from the observation that the size MM of a maximum matching of G lower-bounds the size of any vertex cover of G: Does G have a vertex cover of size at most MM + kμ? The parameter is the excess kμ of the solution size over the lower bound MM.<PERSON><PERSON> and <PERSON> (ICALP 2008) showed that this above-guarantee parameterization of Vertex Cover is fixed-parameter tractable and can be solved in time *(15kμ), where the * notation hides polynomial factors. This was first improved to *(9kμ) (<PERSON><PERSON> et al., ESA 2011), then to *(4kμ) (<PERSON> et al., IPEC 2011, TOCT 2013), then to *(2.618kμ) (<PERSON> et al., STACS 2012) and finally to the current best bound *(2.3146kμ) (<PERSON><PERSON><PERSON><PERSON> et al., TALG 2014). The last two bounds were in fact proven for a different parameter: namely, the excess kλ of the solution size over LP, the value of the linear programming relaxation of the standard LP formulation of Vertex Cover. Since LP ≥ MM for any graph, we have that kλ ≤ kμ for Yes instances. This is thus a stricter parameterization—the new parameter is, in general, smaller—and the running times carry over directly to the parameter kμ.We investigate an even stricter parameterization of Vertex Cover, namely the excess of the solution size over the quantity (2LP – MM). We ask: Given a graph G and ∊ ℕ as input, does G have a vertex cover of size at most (2LP – MM) + ? The parameter is . It can be shown that (2LP – MM) is a lower bound on vertex cover size, and since LP ≥ MM we have that (2LP – MM) ≥ LP, and hence that ≤ kλ holds for Yes instances. Further, (kλ – ) could be as large as (LP – MM) and—to the best of our knowledge—this difference cannot be expressed as a function of kλ alone. These facts motivate and justify our choice of parameter: this is indeed a stricter parameterization whose tractability does not follow directly from known results.We show that Vertex Cover is fixed-parameter tractable for this stricter parameter : We derive an algorithm which solves Vertex Cover in time *(3), thus pushing the envelope further on the parameterized tractability of Vertex Cover.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH80"}, {"primary_key": "4189862", "vector": [], "sparse_vector": [], "title": "Permutation patterns are hard to count.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Let ℱ ⊂ Sk be a finite set of permutations and let Cn(ℱ) denote the number of permutations σ ∊ Sn avoiding the set of patterns ℱ. We prove that {Cn (ℱ)} cannot be computed in time polynomial in n, unless EXP = ⊕EXP. Our tools also allow us to disprove the <PERSON><PERSON><PERSON> conjecture which states that the sequence {Cn(ℱ)} is P-recursive.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH66"}, {"primary_key": "4189863", "vector": [], "sparse_vector": [], "title": "Towards Optimal Deterministic Coding for Interactive Communication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Gillat Kol", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study efficient, deterministic interactive coding schemes that simulate any interactive protocol both under random and adversarial errors, and can achieve a constant communication rate independent of the protocol length.For channels that flip bits independently with probability ∊ < 1/2, our coding scheme achieves a communication rate of and a failure probability of exp(−n/log n) in length n protocols. Prior to our work, all nontrivial deterministic schemes (either efficient or not) had a rate bounded away from 1. Furthermore, the best failure probability achievable by an efficient deterministic coding scheme with constant rate was only quasi-polynomial, i.e., of the form exp(− logO(1) n) (<PERSON>, ITCS 2012).For channels in which an adversary controls the noise pattern our coding scheme can tolerate Ω(1/log n) fraction of errors with rate approaching 1. Once more, all previously known nontrivial deterministic schemes (either efficient or not) in the adversarial setting had a rate bounded away from 1, and no nontrivial efficient deterministic coding schemes were known with any constant rate.Essential to both results is an explicit, efficiently encodable and decodable systematic tree code of length n that has relative distance Ω(1/log n) and rate approaching 1, defined over an O(log n)-bit alphabet. No nontrivial tree code (either efficient or not) was known to approach rate 1, and no nontrivial distance bound was known for any efficient constant rate tree code. The fact that our tree code is systematic, turns out to play an important role in obtaining rate in the random error model, and approaching rate 1 in the adversarial error model.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH135"}, {"primary_key": "4189864", "vector": [], "sparse_vector": [], "title": "An Improved Distributed Algorithm for Maximal Independent Set.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The Maximal Independent Set (MIS) problem is one of the basics in the study of locality in distributed graph algorithms. This paper presents a very simple randomized algorithm for this problem providing a near-optimal local complexity, which incidentally, when combined with some known techniques, also leads to a near-optimal global complexity.Classical MIS algorithms of <PERSON>by [STOC'85] and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> [JALG'86] provide the global complexity guarantee that, with high probability1, all nodes terminate after O(log n) rounds. In contrast, our initial focus is on the local complexity, and our main contribution is to provide a very simple algorithm guaranteeing that each particular node v terminates after O(log deg(v) + log 1/∊) rounds, with probability at least 1 – ∊. The degree-dependency in this bound is optimal, due to a lower bound of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> [PODC'04].Interestingly, this local complexity smoothly transitions to a global complexity: by adding techniques of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> [FOCS'12; arXiv: 1202.1983v3], we2 get an MIS algorithm with a high probability global complexity of , where Δ denotes the maximum degree. This improves over the result of <PERSON><PERSON><PERSON><PERSON> et al., and gets close to the lower bound of <PERSON> et al.Corollaries include improved algorithms for MIS in graphs of upper-bounded arboricity, or lower-bounded girth, for Ruling Sets, for MIS in the Local Computation Algorithms (LCA) model, and a faster distributed algorithm for the Lovász Local Lemma.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH20"}, {"primary_key": "4189865", "vector": [], "sparse_vector": [], "title": "Distributed Algorithms for Planar Networks II: Low-Congestion Shortcuts, MST, and Min-Cut.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces the concept of low-congestion shortcuts for (near-)planar networks, and demonstrates their power by using them to obtain near-optimal distributed algorithms for problems such as Minimum Spanning Tree (MST) or Minimum Cut, in planar networks.Consider a graph G = (V, E) and a partitioning of V into subsets of nodes S1, …, SN, each inducing a connected subgraph G[Si]. We define an α-congestion shortcut with dilation β to be a set of subgraphs H1, …, HN ⊆ G, one for each subset Si, such that1.For each i ∊ [1, N], the diameter of the subgraph G[Si] + Hi is at most β.2.For each edge e ∊ E, the number of subgraphs G[Si] + Hi containing e is at most α.We prove that any partition of a D-diameter planar graph into individually-connected parts admits an O(D log D)-congestion shortcut with dilation O(D log D), and we also present a distributed construction of it in Õ(D) rounds. We moreover prove these parameters to be near-optimal; i.e., there are instances in which, unavoidably, .Finally, we use low-congestion shortcuts, and their efficient distributed construction, to derive Õ(D)-round distributed algorithms for MST and Min-Cut, in planar networks. This complexity nearly matches the trivial lower bound of Ω(D). We remark that this is the first result bypassing the well-known existential lower bound of general graphs (see <PERSON> and <PERSON> [FOCS'99]; <PERSON><PERSON> [STOC'04]; and <PERSON> et al. [STOC'11]) in a family of graphs of interest.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH16"}, {"primary_key": "4189866", "vector": [], "sparse_vector": [], "title": "Communication with Contextual Uncertainty.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Madhu <PERSON>"], "summary": "We introduce a simple model illustrating the role of context in communication and the challenge posed by uncertainty of knowledge of context. We consider a variant of distributional communication complexity where <PERSON> gets some information x and <PERSON> gets y, where (x, y) is drawn from a known distribution, and <PERSON> wishes to compute some function g(x, y) (with high probability over (x, y)). In our variant, <PERSON> does not know g, but only knows some function f which is an approximation of g. Thus, the function being computed forms the context for the communication, and knowing it imperfectly models (mild) uncertainty in this context.A naive solution would be for <PERSON> and <PERSON> to first agree on some common function h that is close to both f and g and then use a protocol for h to compute h(x, y). We show that any such agreement leads to a large overhead in communication ruling out such a universal solution.In contrast, we show that if g has a one-way communication protocol with complexity k in the standard setting, then it has a communication protocol with complexity O(k · (1 + I)) in the uncertain setting, where I denotes the mutual information between x and y. In the particular case where the input distribution is a product distribution, the protocol in the uncertain setting only incurs a constant factor blow-up in communication and error.Furthermore, we show that the dependence on the mutual information I is required. Namely, we construct a class of functions along with a non-product distribution over (x, y) for which the communication complexity is a single bit in the standard setting but at least bits in the uncertain setting.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH144"}, {"primary_key": "4189867", "vector": [], "sparse_vector": [], "title": "Communication Complexity of Permutation-Invariant Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Madhu <PERSON>"], "summary": "Motivated by the quest for a broader understanding of upper bounds in communication complexity, at least for simple functions, we introduce the class of “permutation-invariant” functions. A partial function f: {0, 1}n × {0, 1}n → {0, 1, ?} is permutation-invariant if for every bijection π: {1, …, n} → {1, …, n} and every x, y ∊ {0, 1}n, it is the case that f(x, y) = f(xπ, yπ). Most of the commonly studied functions in communication complexity are permutation-invariant. For such functions, we present a simple complexity measure (computable in time polynomial in n given an implicit description of f) that describes their communication complexity up to polynomial factors and up to an additive error that is logarithmic in the input size. This gives a coarse taxonomy of the communication complexity of simple functions. Our work highlights the role of the well-known lower bounds of functions such as Set-Disjointness and Indexing, while complementing them with the relatively lesser-known upper bounds for Gap-Inner-Product (from the sketching literature) and Sparse-Gap-Inner-Product (from the recent work of <PERSON><PERSON> et al. [ITCS 2015]). We also present consequences to the study of communication complexity with imperfectly shared randomness where we show that for total permutation-invariant functions, imperfectly shared randomness results in only a polynomial blow-up in communication complexity after an additive O(log log n) overhead.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH134"}, {"primary_key": "4189868", "vector": [], "sparse_vector": [], "title": "Computing in continuous space with self-assembling polygonal tiles (extended abstract).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Trent <PERSON>"], "summary": "In this paper we investigate the computational power of the polygonal tile assembly model (polygonal TAM) at temperature 1, i.e. in non-cooperative systems. The polygonal TAM is an extension of <PERSON><PERSON>'s abstract tile assembly model (aTAM) which not only allows for square tiles (as in the aTAM) but also allows for tile shapes which are arbitrary polygons. Although a number of self-assembly results have shown computational universality at temperature 1, these are the first results to do so by fundamentally relying on tile placements in continuous, rather than discrete, space. With the square tiles of the aTAM, it is conjectured that the class of temperature 1 systems is not computationally universal. Here we show that for each n > 6, the class of systems whose tiles are the shape of the regular polygon P with n sides is computationally universal. On the other hand, we show that the class of systems whose tiles consist of a regular polygon P with n ≤ 6 sides cannot compute using any known techniques. In addition, we show a number of classes of systems whose tiles consist of a non-regular polygon with n ≥ 3 sides are computationally universal.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH67"}, {"primary_key": "4189869", "vector": [], "sparse_vector": [], "title": "Time vs. Information Tradeoffs for Leader Election in Anonymous Trees.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Leader election is one of the fundamental problems in distributed computing. It calls for all nodes of a network to agree on a single node, called the leader. If the nodes of the network have distinct labels, then agreeing on a single node means that all nodes have to output the label of the elected leader. If the nodes of the network are anonymous, the task of leader election is formulated as follows: every node v of the network must output a simple path, which is coded as a sequence of port numbers, such that all these paths end at a common node, the leader. In this paper, we study deterministic leader election in anonymous trees.Our aim is to establish tradeoffs between the allocated time τ and the amount of information that has to be given a priori to the nodes to enable leader election in time τ in all trees for which leader election in this time is at all possible. Following the framework of algorithms with advice, this information (a single binary string) is provided to all nodes at the start by an oracle knowing the entire tree. The length of this string is called the size of advice. For a given time τ allocated to leader election, we give upper and lower bounds on the minimum size of advice sufficient to perform leader election in time τ.For most values of τ, our upper and lower bounds are either tight up to multiplicative constants, or they differ only by a logarithmic factor. Let T be an n-node tree of diameter diam ≤ D. While leader election in time diam can be performed without any advice, for time diam – 1 we give tight upper and lower bounds of ⊝(log D). For time diam – 2 we give tight upper and lower bounds of ⊝(log D) for even values of diam, and tight upper and lower bounds of ⊝(log n) for odd values of diam. Moving to shorter time, in the interval [β · diam, diam – 3] for constant β > 1/2, we prove an upper bound of and a lower bound of , the latter being valid whenever diam is odd or when the time is at most diam – 4. Hence, with the exception of the special case when diam is even and time is exactly diam–3, our bounds leave only a logarithmic gap in this time interval. Finally, for time α · diam for any constant α < 1/2 (except for the case of very small diameters), we again give tight upper and lower bounds, this time ⊝(n).MSC codesleader electiontreeadvicedeterministic distributed algorithmtime", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH44"}, {"primary_key": "4189870", "vector": [], "sparse_vector": [], "title": "Towards Optimal Algorithms for Prediction with Expert Advice.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Balasubramanian <PERSON>"], "summary": "We study the classical problem of prediction with expert advice in the adversarial setting with a geometric stopping time. In 1965, <PERSON> gave the optimal algorithm for the case of 2 experts. In this paper, we design the optimal algorithm, adversary and regret for the case of 3 experts. Further, we show that the optimal algorithm for 2 and 3 experts is a probability matching algorithm (analogous to Thompson sampling) against a particular randomized adversary. Remarkably, our proof shows that the probability matching algorithm is not only optimal against this particular randomized adversary, but also minimax optimal.Our analysis develops upper and lower bounds simultaneously, analogous to the primal-dual method. Our analysis of the optimal adversary goes through delicate asymptotics of the random walk of a particle between multiple walls. We use the connection we develop to random walks to derive an improved algorithm and regret bound for the case of 4 experts, and, provide a general framework for designing the optimal algorithm and adversary for an arbitrary number of experts.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH39"}, {"primary_key": "4189871", "vector": [], "sparse_vector": [], "title": "Algorithms and Adaptivity Gaps for Stochastic Probing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A stochastic probing problem consists of a set of elements whose values are independent random variables. The algorithm knows the distributions of these variables, but not the actual outcomes. The only way to learn the actual outcomes is to probe these elements. However, there are constraints on which set of elements may be probed. (E.g., we may have to travel in some metric to probe elements but have limited time.) These constraints are called outer constraints. We want to develop an algorithm that picks some set of elements to maximize the (expected) value, subject to the picked subset of elements satisfying some other set of constraints, called the inner constraints. In the past, probing problems were studied for the case when both inner and outer constraints were intersections of matroids; these modeled kidney matching and Bayesian auctions applications. One limitation of past work was their reliance on linear-programming-like techniques, which made going beyond matroid-like structures difficult.In this work, we give a very general adaptivity gap result that holds for all prefix-closed outer constraints, as long as the inner constraints are intersections of matroids. The adaptivity gap is O(log n) for any constant number of inner matroid constraints. The prefix-closedness captures most “reasonable” outer constraints, like orienteering, connectivity, and precedence. Based on this we obtain the first approximation algorithms for a number of stochastic probing problems, which have applications, e.g., to path-planning and precedence-constrained scheduling.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH120"}, {"primary_key": "4189872", "vector": [], "sparse_vector": [], "title": "Nearly Optimal NP-Hardness of Unique Coverage.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "The Unique Coverage problem, given a universe V of elements and a collection E of subsets of V, asks to find S ⊆ V to maximize the number of e ∊ E that intersects S in exactly one element. When each e ∊ E has cardinality at most k, it is also known as 1-in-k Hitting Set, and admits a simple -approximation algorithm.For constant k, we prove that 1-in-k Hitting Set is NP-hard to approximate within a factor . This improves the result of <PERSON><PERSON><PERSON> and <PERSON> [SODA'11, ToC'12], who proved the same result assuming the Unique Games Conjecture. For Unique Coverage, we prove that it is hard to approximate within a factor for any ∊ > 0, unless NP admits quasipolynomial time algorithms. This improves the results of <PERSON><PERSON><PERSON> et al. [SODA'06, SICOMP'08], including their ≈ 1/log1/3 n inapproximability factor which was proven under the Random 3SAT Hypothesis. Our simple proof combines ideas from two classical inapproximability results for Set Cover and Constraint Satisfaction Problem, made efficient by various derandomization methods based on bounded independence.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH119"}, {"primary_key": "4189873", "vector": [], "sparse_vector": [], "title": "Approximating the k-Level in Three-Dimensional Plane Arrangements.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let H be a set of n non-vertical planes in three dimensions, and let r < n be a parameter. We give a simple alternative proof of the existence of a O(1/r)-cutting of the first n/r levels of (H), which consists of O(r) semi-unbounded vertical triangular prisms. The same construction yields an approximation of the (n/r)-level by a terrain consisting of O(r/∊3) triangular faces, which lies entirely between the levels (1 ± ∊)n/r. The proof does not use sampling, and exploits techniques based on planar separators and various structural properties of levels in three-dimensional arrangements and of planar maps. The proof is constructive, and leads to a simple randomized algorithm, that computes the terrain in O(n + r2∊–6 log3 r) expected time. An application of this technique allows us to mimic <PERSON><PERSON><PERSON><PERSON>'s construction of cuttings in the plane [36], to obtain a similar construction of “layered” (1/r)-cutting of the entire arrangement (H), of optimal size O(r3). Another application is a simplified optimal approximate range counting algorithm in three dimensions, competing with that of <PERSON><PERSON><PERSON> and <PERSON> [1].", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH83"}, {"primary_key": "4189874", "vector": [], "sparse_vector": [], "title": "Algorithmic and Enumerative Aspects of the Moser-Tardos Distribution.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mo<PERSON> & Tardos have developed a powerful algorithmic approach (henceforth “MT”) to the Lovász Local Lemma (LLL); the basic operation done in MT and its variants is a search for “bad” events in a current configuration. In the initial stage of MT, the variables are set independently. We examine the distributions on these variables which arise during intermediate stages of MT. We show that these configurations have a more or less “random” form, building further on the “MT-distribution” concept of <PERSON><PERSON> et al. in understanding the (intermediate and) output distribution of MT. This has a variety of algorithmic applications; the most important is that bad events can be found relatively quickly, improving upon MT across the complexity spectrum: it makes some polynomial-time algorithms sub-linear (e.g., for Latin transversals, which are of basic combinatorial interest), gives lower-degree polynomial run-times in some settings, transforms certain super-polynomial-time algorithms into polynomial-time ones, and leads to Las Vegas algorithms for some coloring problems for which only Monte Carlo algorithms were known.We show that in certain conditions when the LLL condition is violated, a variant of the MT algorithm can still produce a distribution which avoids most of the bad events. We show in some cases this MT variant can run faster than the original MT algorithm itself, and develop the first-known criterion for the case of the asymmetric LLL. This can be used to find partial Latin transversals – improving upon earlier bounds of <PERSON> (1975) – among other applications. We furthermore give applications in enumeration, showing that most applications (where we aim for all or most of the bad events to be avoided) have many more solutions than known before by proving that the MT-distribution has “large” Rényi entropy and hence that its support-size is large.KeywordsLovász Local LemmaMoser-Tardos algorithmLLL-distributionMT-distributiongraph coloringsatisfiabilityLatin transversalscombinatorial enumeration", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH140"}, {"primary_key": "4189875", "vector": [], "sparse_vector": [], "title": "The Restricted Isometry Property of Subsampled Fourier Matrices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A matrix A ∊ ℂq×N satisfies the restricted isometry property of order k with constant ∊ if it preserves the ℓ2 norm of all k-sparse vectors up to a factor of 1 ± ∊. We prove that a matrix A obtained by randomly sampling q = O(k · log2 k · log N) rows from an N × N Fourier matrix satisfies the restricted isometry property of order k with a fixed ∊ with high probability. This improves on <PERSON><PERSON><PERSON> and <PERSON><PERSON> (Comm. Pure Appl. Math., 2008), its subsequent improvements, and <PERSON><PERSON><PERSON> (GAFA Seminar Notes, 2014).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH22"}, {"primary_key": "4189876", "vector": [], "sparse_vector": [], "title": "An FPTAS for Minimizing Indefinite Quadratic Forms over Integers in Polyhedra.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)An FPTAS for Minimizing Indefinite Quadratic Forms over Integers in PolyhedraR<PERSON>rt <PERSON>brand, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.1715 - 1723Chapter DOI:https://doi.org/10.1137/1.*************.ch118PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present a generic approach that allows us to develop a fully polynomial-time approximation scheme (FTPAS) for minimizing nonlinear functions over the integer points in a rational polyhedron in fixed dimension. The approach combines the subdivision strategy of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [22] with ideas similar to those commonly used to derive real algebraic certificates of positivity for polynomials. Our general approach is widely applicable. We apply it, for instance, to the <PERSON><PERSON>kin polynomial and to indefinite quadratic forms xT Qx in a fixed number of variables, where Q has at most one positive, or at most one negative eigenvalue. In dimension three, this leads to an FPTAS for general Q. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH118"}, {"primary_key": "4189877", "vector": [], "sparse_vector": [], "title": "On the Integrality Gap of Degree-4 Sum of Squares for Planted Clique.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The problem of finding large cliques in random graphs and its “planted” variant, where one wants to recover a clique of size ω ≫ log (n) added to an Erdős-R<PERSON> graph , have been intensely studied. Nevertheless, existing polynomial time algorithms can only recover planted cliques of size . By contrast, information theoretically, one can recover planted cliques so long as ω ≫ log (n).In this work, we continue the investigation of algorithms from the sum of squares hierarchy for solving the planted clique problem begun by <PERSON><PERSON>, Potechin, and <PERSON><PERSON><PERSON><PERSON> [MPW15] and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [DM15b]. Our main results improve upon both these previous works by showing:1.Degree four SoS does not recover the planted clique unless , improving upon the bound ω ≫ n1/3 due to [DM15b].2.For , degree 2d SoS does not recover the planted clique unless ω ≫ n1/(d+1)/(2d polylog n), improving upon the bound due to [MPW15].Our proof for the second result is based on a fine spectral analysis of the certificate used in the prior works [MPW15, DM15b, FK03] by decomposing it along an appropriately chosen basis. Along the way, we develop combinatorial tools to analyze the spectrum of random matrices with dependent entries and to understand the symmetries in the eigenspaces of the set symmetric matrices inspired by work of <PERSON><PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON><PERSON>]An argument of <PERSON><PERSON><PERSON> shows that the first result cannot be proved using the same certificate. Rather, our proof involves constructing and analyzing a new certificate that yields the nearly tight lower bound by “correcting” the certificate of [MPW15, DM15b, FK03]", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH76"}, {"primary_key": "4189878", "vector": [], "sparse_vector": [], "title": "The Adversarial Noise Threshold for Distributed Protocols.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the problem of implementing distributed protocols, despite adversarial channel errors, on synchronous-messaging networks with arbitrary topology.In our first result we show that any n-party T-round protocol on an undirected communication network G can be compiled into a robust simulation protocol on a sparse (ℴ(n) edges) subnetwork so that the simulation tolerates an adversarial error rate of ; the simulation has a round complexity of , where m is the number of edges in G. (So the simulation is work-preserving up to a log factor.) The adversary's error rate is within a constant factor of optimal. Given the error rate, the round complexity blowup is within a factor of ℴ(k log n) of optimal, where k is the edge connectivity of G. We also determine that the maximum tolerable error rate on directed communication networks is ⊝(1/s) where s is the number of edges in a minimum equivalent digraph.Next we investigate adversarial per-edge error rates, where the adversary is given an error budget on each edge of the network. We determine the limit for tolerable per-edge error rates on an arbitrary directed graph to within a factor of 2. However, the construction that approaches this limit has exponential round complexity, so we give another compiler, which transforms T-round protocols into ℴ(mT)-round simulations, and prove that for polynomial-query black box compilers, the per-edge error rate tolerated by this last compiler is within a constant factor of optimal.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH18"}, {"primary_key": "4189879", "vector": [], "sparse_vector": [], "title": "Jointly Private Convex Programming.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a general method for approximately solving convex programs defined by private information from agents, when the solution can be naturally partitioned among the agents. This class of problems includes multi-commodity flow problems, general allocation problems, and multi-dimensional knapsack problems, among other examples. The accuracy of our algorithm depends on the number of coupling constraints, which bind multiple agents. On the other hand, our accuracy is nearly independent of the number of variables, and in many cases, actually improves as the number of agents increases. A special case of our result (solving general allocation problems beyond \"Gross Substitute\" preferences) resolves the main open problem from [H<PERSON> et al. STOC 2014].We also consider strategic agents who have preferences over their part of the solution. For any convex program in our class that maximizes social welfare, we show how to create an approximately dominant strategy truthful mechanism, approximately maximizing welfare. The central idea is to charge agents prices based on the approximately optimal dual variables, which are themselves computed under differential privacy. Our results substantially broaden the class of problems that are known to be solvable under privacy and/or incentive constraints.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH43"}, {"primary_key": "4189881", "vector": [], "sparse_vector": [], "title": "Canonical Paths for MCMC: from Art to Science.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Markov Chain Monte Carlo (MCMC) method is a widely used algorithm design scheme with many applications. To make efficient use of this method, the key step is to prove that the Markov chain is rapid mixing. Canonical paths is one of the two main tools to prove rapid mixing. However, there are much fewer success examples comparing to coupling, the other main tool. The main reason is that there is no systematic approach or general recipe to design canonical paths. Building up on a previous exploration by <PERSON><PERSON><PERSON><PERSON><PERSON> [18], we develop a general theory to design canonical paths for MCMC: We reduce the task of designing canonical paths to solving a set of linear equations, which can be automatically done even by a machine.Making use of this general approach, we obtain fully polynomial-time randomized approximation schemes (FPRAS) for counting the number of b-matching with b ≤ 7 and b-edge-cover with b ≤ 2. They are natural generalizations of matchings and edge covers for graphs. No polynomial time approximation was previously known for these problems.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH38"}, {"primary_key": "4189882", "vector": [], "sparse_vector": [], "title": "Weighted dynamic finger in binary search trees.", "authors": ["<PERSON>", "<PERSON>"], "summary": "It is shown that the online binary search tree data structure GreedyASS performs asymptotically as well on a sufficiently long sequence of searches as any static binary search tree where each search begins from the previous search (rather than the root). This bound is known to be equivalent to assigning each item i in the search tree a positive weight wi and bounding the search cost of an item in the search sequence s1, …, smThis result is the strongest finger-type bound to be proven for binary search trees. By setting the weights to be equal, one observes that our bound implies the dynamic finger bound. Compared to the previous proof of the dynamic finger bound for Splay trees, our result is significantly shorter, stronger, simpler, and has reasonable constants.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH49"}, {"primary_key": "4189883", "vector": [], "sparse_vector": [], "title": "Improved Approximation Algorithms for k-Submodular Function Maximization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a polynomial-time 1/2-approximation algorithm for maximizing nonnegative k-submodular functions. This improves upon the previous max{1/3, 1/(1 + a)}-approximation by <PERSON> and <PERSON> [18], where a = . We also show that for monotone k-submodular functions there is a polynomial-time k/(2k – 1)-approximation algorithm while for any ∊ > 0 a ((k + 1)/2k + ∊)-approximation algorithm for maximizing monotone k-submodular functions would require exponentially many queries. In particular, our hardness result implies that our algorithms are asymptotically tight.We also extend the approach to provide constant factor approximation algorithms for maximizing skewbisubmodular functions, which were recently introduced as generalizations of bisubmodular functions.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH30"}, {"primary_key": "4189884", "vector": [], "sparse_vector": [], "title": "Finding a Stable Allocation in Polymatroid Intersection.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The stable matching model of <PERSON> and <PERSON> (1962) has been generalized in various directions such as matroid kernels due to <PERSON><PERSON><PERSON> (2001) and stable allocations in bipartite networks due to <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2002). Unifying these generalizations, we introduce the concept of stable allocations in polymatroid intersection.Our framework includes both integer- and real-variable versions. The integer-variable version corresponds to a special case of the discrete-concave function model due to <PERSON>, <PERSON>, and <PERSON><PERSON> (2003), who established the existence of a stable allocation by showing that a simple extension of the deferred acceptance algorithm of <PERSON> and <PERSON><PERSON><PERSON> finds a stable allocation in pseudo-polynomial time. It has been open to develop a polynomial-time algorithm even for our special case.In this paper, we present the first strongly polynomial algorithm for finding a stable allocation in polymatroid intersection. To achieve this, we utilize the augmenting path technique for polymatroid intersection. In each iteration, the algorithm searches for an augmenting path by simulating a chain of proposes and rejects in the deferred acceptance algorithm. The running time of our algorithm is O(n3γ), where n and γ respectively denote the cardinality of the ground set and the time for computing the saturation and exchange capacities. This is as fast as the best known algorithm for the polymatroid intersection problem.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH73"}, {"primary_key": "4189885", "vector": [], "sparse_vector": [], "title": "Approximation schemes for machine scheduling with resource (in-)dependent processing times.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider two related scheduling problems: resource constrained scheduling on identical parallel machines and a generalization with resource dependent processing times. In both problems, jobs require a certain amount of an additional resource and have to be scheduled on machines minimizing the makespan, while at every point in time a given resource capacity is not exceeded. In the first variant of the problem the processing times and resource amounts are fixed, while in the second the former depends on the latter. We present asymptotic fully polynomial approximation schemes (AFPTAS) for the problems: For any ∊ > 0 a schedule of length at most (1 + ∊) times the optimum plus an additive term of ℴ(1/∊2) is provided, and the running time is polynomially bounded in 1/∊ and the input length. Up to now only approximation algorithms with constant approximation ratios were known.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH104"}, {"primary_key": "4189886", "vector": [], "sparse_vector": [], "title": "Constructive algorithm for path-width of matroids.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given n subspaces of a finite-dimensional vector space over a fixed finite field F, we wish to find a linear layout V1, V2, …, Vn of the subspaces such that dim((V1 + V2 + ⃛ + Vi)∩(Vi+1 + ⃛ + Vn)) ≤ k for all i; such a linear layout is said to have width at most k. When restricted to 1-dimensional subspaces, this problem is equivalent to computing the path-width of an F-represented matroid in matroid theory and computing the trellis-width (or minimum trellis state-complexity) of a linear code in coding theory.We present a fixed-parameter tractable algorithm to construct a linear layout of width at most k, if it exists, for input subspaces of a finite-dimensional vector space over F. As corollaries, we obtain a fixed-parameter tractable algorithm to produce a path-decomposition of width at most k for an input F-represented matroid of path-width at most k, and a fixed-parameter tractable algorithm to find a linear rank-decomposition of width at most k for an input graph of linear rank-width at most k. In both corollaries, no such algorithms were known previously. Our approach is based on dynamic programming combined with the idea developed by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (1996) for their work on path-width and tree-width of graphs.It was previously known that a fixed-parameter tractable algorithm exists for the decision version of the problem for matroid path-width; a theorem by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (2002) implies that for each fixed finite field F, there are finitely many forbidden F-representable minors for the class of matroids of path-width at most k. An algorithm by <PERSON>lin<PERSON>n<PERSON> (2006) can detect a minor in an input F-represented matroid of bounded branch-width. However, this indirect approach would not produce an actual path-decomposition even if the complete list of forbidden minors were known. Our algorithm is the first one to construct such a path-decomposition and does not depend on the finiteness of forbidden minors.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH116"}, {"primary_key": "4189887", "vector": [], "sparse_vector": [], "title": "Sparsity and dimension.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Veit Wiechert"], "summary": "We prove that posets of bounded height whose cover graphs belong to a fixed class with bounded expansion have bounded dimension. Bounded expansion, introduced by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> as a model for sparsity in graphs, is a property that is naturally satisfied by a wide range of graph classes, from graph structure theory (graphs excluding a minor or a topological minor) to graph drawing (e.g. graphs with constant book thickness). Therefore, our theorem generalizes a number of results including the most recent one for posets of bounded height with cover graphs excluding a fixed graph as a topological minor (<PERSON><PERSON>ak, SODA 2015). We also show that the result is in a sense best possible, as it does not extend to nowhere dense classes; in fact, it already fails for cover graphs with locally bounded treewidth.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH125"}, {"primary_key": "4189888", "vector": [], "sparse_vector": [], "title": "An Improved Approximation Guarantee for the Maximum Budgeted Allocation Problem.", "authors": ["<PERSON><PERSON>"], "summary": "We study the Maximum Budgeted Allocation problem, which is the problem of assigning indivisible items to players with budget constraints. In its most general form, an instance of the MBA problem might include many different prices for the same item among different players, and different budget constraints for every player. So far, the best approximation algorithms we know for the MBA problem achieve a 3/4-approximation ratio, and employ a natural LP relaxation, called the Assignment-LP. In this paper, we give an algorithm for MBA, and prove that it achieves a 3/4 + c-approximation ratio, for some constant c > 0. This algorithm works by rounding solutions to an LP called the Configuration-LP, therefore also showing that the Configuration-LP is strictly stronger than the Assignment-LP (for which we know that the integrality gap is 3/4) for the MBA problem.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH74"}, {"primary_key": "4189889", "vector": [], "sparse_vector": [], "title": "Distance in the Forest Fire Model How far are you from Eve?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON> (2005) observed that many social networks exhibit properties such as shrinking (i.e. bounded) diameter, densification, and (power-law) heavy tail degree distributions. To explain these phenomena, they introduced a generative model, called the Forest Fire model, and using simulations showed that this model indeed exhibited these properties; however, proving this rigorously was left as an open problem.In this paper, we analyse one of these properties, shrinking diameter. We define a restricted version of their model that incorporates the main features that seem to contribute towards this property, and prove that the graphs generated by this model exhibit shrinking distance to the seed graph. We prove that an even simpler model, the random walk model, already exhibits this phenomenon.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH109"}, {"primary_key": "4189890", "vector": [], "sparse_vector": [], "title": "A Faster Subquadratic Algorithm for Finding Outlier Correlations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of detecting outlier pairs of strongly correlated variables among a collection of n variables with otherwise weak pairwise correlations. After normalization, this task amounts to the geometric task where we are given as input a set of n vectors with unit Euclidean norm and dimension d, and we are asked to find all the outlier pairs of vectors whose inner product is at least ρ in absolute value, subject to the promise that all but at most q pairs of vectors have inner product at most τ in absolute value for some constants 0 < τ < ρ < 1.Improving on an algorithm of <PERSON><PERSON> [FOCS 2012; J. <PERSON> 2015], we present a randomized algorithm that for Boolean inputs ({–1, 1}-valued data normalized to unit Euclidean length) runs in timewhere 0 < γ < 1 is a constant tradeoff parameter and M(μ, v) is the exponent to multiply an ⌊nμ⌋ × ⌊nv⌋ matrix with an ⌊nv⌋ × ⌊nμ⌋ matrix and Δ = 1/(1 – logτ ρ). As corollaries we obtain randomized algorithms that run in timeand in timewhere 2 ≤ ω < 2.38 is the exponent for square matrix multiplication and 0.3 < α ≤ 1 is the exponent for rectangular matrix multiplication. We present further corollaries for the light bulb problem and for learning sparse Boolean functions. (The notation Õ(·) hides polylogarithmic factors in n and d whose degree may depend on ρ and τ.)", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH90"}, {"primary_key": "4189891", "vector": [], "sparse_vector": [], "title": "Persistent Homology and Nested Dissection.", "authors": ["<PERSON>", "<PERSON>", "Primoz <PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Persistent Homology and Nested Dissection<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>oz <PERSON>pp.1234 - 1245Chapter DOI:https://doi.org/10.1137/1.*************.ch86PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Nested dissection exploits the underlying topology to do matrix reductions while persistent homology exploits matrix reductions to the reveal underlying topology. It seems natural that one should be able to combine these techniques to beat the currently best bound of matrix multiplication time for computing persistent homology. However, nested dissection works by fixing a reduction order, whereas persistent homology generally constrains the ordering according to an input filtration. Despite this obstruction, we show that it is possible to combine these two theories. This shows that one can improve the computation of persistent homology if the underlying space has some additional structure. We give reasonable geometric conditions under which one can beat the matrix multiplication bound for persistent homology. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH86"}, {"primary_key": "4189892", "vector": [], "sparse_vector": [], "title": "Approximation of non-boolean 2CSP.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We develop a polynomial time Ω ( log R) approximate algorithm for Max 2CSP-R, the problem where we are given a collection of constraints, each involving two variables, where each variable ranges over a set of size R, and we want to find an assignment to the variables that maximizes the number of satisfied constraints. Assuming the Unique Games Conjecture, this is the best possible approximation up to constant factors.Previously, a 1/R-approximate algorithm was known, based on linear programming. Our algorithm is based on semidefinite programming. The Semidefinite Program that we use has an almost-matching integrality gap.For the more general Max kCSP-R, in which each constraint involves k variables, each ranging over a set of size R, it was known that the best possible approximation is of the order of k/Rk – 1, provided that k is sufficiently large compared to R; our algorithm shows that the bound k/Rk – 1 is not tight for k = 2.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH117"}, {"primary_key": "4189893", "vector": [], "sparse_vector": [], "title": "Higher Lower Bounds from the 3SUM Conjecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The 3SUM conjecture has proven to be a valuable tool for proving conditional lower bounds on dynamic data structures and graph problems. This line of work was initiated by <PERSON><PERSON><PERSON><PERSON><PERSON> (STOC 2010) who reduced 3SUM to an offline SetDisjointness problem. However, the reduction introduced by <PERSON><PERSON><PERSON><PERSON><PERSON> suffers from several inefficiencies, making it difficult to obtain tight conditional lower bounds from the 3SUM conjecture.In this paper we address many of the deficiencies of <PERSON><PERSON><PERSON><PERSON><PERSON>'s framework. We give new and efficient reductions from 3SUM to offline SetDisjointness and offline SetIntersection (the reporting version of SetDisjointness) which leads to polynomially higher lower bounds on several problems. Using our reductions, we are able to show the essential optimality of several algorithms, assuming the 3SUM conjecture.•<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>'s O(mα)-time algorithm (SICOMP 1985) for enumerating all triangles in a graph with arboricity/degeneracy α is essentially optimal, for any α.•<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>'s algorithm (ICALP 2014) for listing t triangles is essentially optimal (assuming the matrix multiplication exponent is ω = 2).•Any static data structure for SetDisjointness that answers queries in constant time must spend Ω(N2–o(1)) time in preprocessing, where N is the size of the set system.These statements were unattainable via <PERSON><PERSON><PERSON><PERSON><PERSON>'s reductions.We also introduce several new reductions from 3SUM to pattern matching problems and dynamic graph problems. Of particular interest are new conditional lower bounds for dynamic versions of Maximum Cardinality Matching, which introduce a new technique for obtaining amortized lower bounds.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH89"}, {"primary_key": "4189894", "vector": [], "sparse_vector": [], "title": "Characterisation of Strongly Stable Matchings.", "authors": ["<PERSON>", "Kat<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An instance of a strongly stable matching problem (SSMP) is an undirected bipartite graph G = (A ∪ B, E), with an adjacency list of each vertex being a linearly ordered list of ties, which are subsets of vertices equally good for a given vertex. Ties are disjoint and may contain one vertex. A matching M is a set of vertex-disjoint edges. An edge (x, y) ∊ E\\M is a blocking edge for M if x is either unmatched or strictly prefers y to its current partner in M, and y is either unmatched or strictly prefers x to its current partner in M or is indifferent between them. A matching is strongly stable if there is no blocking edge with respect to it. We present a characterisation of the set of all strongly stable matchings, thus solving an open problem already stated in the book by <PERSON><PERSON> and <PERSON> [7]. It has previously been shown that strongly stable matchings form a distributive lattice [8] and although the number of strongly stable matchings can be exponential in the number of vertices, we show that there exists a partial order with O(m) elements representing all strongly stable matchings, where m denotes the number of edges in the graph. We give two algorithms that construct two such representations: one in O(nm2) time and the other in O(nm) time, where n denotes the number of vertices in the graph. Note that the construction of the second representation has the same time complexity as that of computing a single strongly stable matching.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH8"}, {"primary_key": "4189895", "vector": [], "sparse_vector": [], "title": "Improved Cheeger&apos;s Inequality and Analysis of Local Graph Partitioning using Vertex Expansion and Expansion Profile.", "authors": ["Tsz Chiu Kwok", "<PERSON><PERSON>", "<PERSON>"], "summary": "We prove two generalizations of the <PERSON><PERSON><PERSON>'s inequality. The first generalization relates the second eigenvalue to the edge expansion and the vertex expansion of the graph G,where φV(G) denotes the robust vertex expansion of G and φ(G) denotes the edge expansion of G. The second generalization relates the second eigenvalue to the edge expansion and the expansion profile of G, for all k ≥ 2,where φk(G) denotes the k-way expansion of G. These show that the spectral partitioning algorithm has better performance guarantees when φV(G) is large (e.g. planted random instances) or φk(G) is large (instances with few disjoint non-expanding sets). Both bounds are tight up to a constant factor.Our approach is based on a method to analyze solutions of Laplacian systems, and this allows us to extend the results to local graph partitioning algorithms. In particular, we show that our approach can be used to analyze personal pagerank vectors, and to give a local graph partitioning algorithm for the small-set expansion problem with performance guarantees similar to the generalizations of <PERSON><PERSON><PERSON>'s inequality. We also present a spectral approach to prove similar results for the truncated random walk algorithm. These show that local graph partitioning algorithms almost match the performance of the spectral partitioning algorithm, with the additional advantages that they apply to the small-set expansion problem and their running time could be sublinear. Our techniques provide common approaches to analyze the spectral partitioning algorithm and local graph partitioning algorithms.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH129"}, {"primary_key": "4189896", "vector": [], "sparse_vector": [], "title": "Approximating capacitated k-median with (1 + ∊)k open facilities.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximating capacitated k-median with (1 + ∊)k open facilitiesShi LiShi Lipp.786 - 796Chapter DOI:https://doi.org/10.1137/1.*************.ch56PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the capacitated k-median (CKM) problem, we are given a set F of facilities, each facility i ∊ F with a capacity ui, a set C of clients, a metric d over F ∪ C and an integer k. The goal is to open k facilities in F and connect the clients C to the open facilities such that each facility i is connected by at most ui clients, so as to minimize the total connection cost. In this paper, we give the first constant approximation for CKM, that only violates the cardinality constraint by a factor of 1 + ∊. This generalizes the result of [Li15], which only works for the uniform capacitated case. Moreover, the approximation ratio we obtain is , which is an exponential improvement over the ratio of in [Li15]. The natural LP relaxation for the problem, which almost all previous algorithms for CKM are based on, has unbounded integrality gap even if (2 – ∊)k facilities can be opened. We introduce a novel configuration LP for the problem, that overcomes this integrality gap. On the downside, each facility may be opened twice by our algorithm. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH56"}, {"primary_key": "4189897", "vector": [], "sparse_vector": [], "title": "Independence and Efficient Domination on P6-free Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the Maximum Weight Independent Set problem, the input is a graph G, every vertex has a nonnegative integer weight, and the task is to find a set S of pairwise non-adjacent vertices, maximizing the total weight of the vertices in S. We give an time algorithm for this problem on graphs excluding the path P6 on 6 vertices as an induced subgraph. Currently, there is no constant k known for which Maximum Weight Independent Set on Pk-free graphs becomes NP-complete, and our result implies that if such a k exists, then k > 6 unless all problems in NP can be decided in (quasi)polynomial time.Using the combinatorial tools that we develop for the above algorithm, we also give a polynomial-time algorithm for Maximum Weight Efficient Dominating Set on P6-free graphs. In this problem, the input is a graph G, every vertex has an integer weight, and the objective is to find a set S of maximum weight such that every vertex in G has exactly one vertex in S in its closed neighborhood, or to determine that no such set exists. Prior to our work, the class of P6-free graphs was the only class of graphs defined by a single forbidden induced subgraph on which the computational complexity of Maximum Weight Efficient Dominating Set was unknown.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH124"}, {"primary_key": "4189898", "vector": [], "sparse_vector": [], "title": "Learning and Efficiency in Games with Dynamic Population.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the quality of outcomes in repeated games when the population of players is dynamically changing, and where participants use learning algorithms to adapt to the dynamic environment. Price of anarchy has originally been introduced to study the Nash equilibria of one-shot games. Many games studied in computer science, such as packet routing or ad-auctions, are played repeatedly. Given the computational hardness of Nash equilibria, an attractive alternative in repeated game settings is that players use no-regret learning algorithms. The price of total anarchy considers the quality of such learning outcomes, assuming a steady environment and player population, which is rarely the case in online settings.In this paper we analyze efficiency of repeated games in dynamically changing environments. An important trait of learning behavior is its versatility to changing environments, assuming that the learning method used is adaptive, i.e., doesn't rely too heavily on experience from the distant past. We show that, in large classes of games, if players choose their strategies in a way that guarantees low adaptive regret, high social welfare is ensured, even under very frequent changes.A main technical tool for our analysis is the existence of a solution to the welfare maximization problem that is both close to optimal and relatively stable over time. Such a solution serves as a benchmark in the efficiency analysis of learning outcomes. We show that such a stable and close to optimal solution exists for many problems, even in cases when the exact optimal solution can be very unstable. We further show that a sufficient condition on the existence of stable outcomes is the existence of a differentially private algorithm for the welfare maximization problem. Hence, we draw a strong connection between differential privacy and high efficiency of learning outcomes in frequently changing repeated games.We demonstrate our techniques by focusing on two classes of games as examples: independent item auctions and congestion games. In both applications we show that adaptive learning guarantees high social welfare even with surprisingly high churn in the player population.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH9"}, {"primary_key": "4189899", "vector": [], "sparse_vector": [], "title": "On approximating strip packing with a better ratio than 3/2.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the strip packing problem we are given a set of rectangular items that we want to place in a strip of given width such that we minimize the height of the obtained packing. It is a very classical two-dimensional packing problem that has received a lot of attention and it has applications in many settings such as stock-cutting and scheduling. A straight-forward reduction from Partition shows that the problem cannot be approximated with a better absolute factor than 3/2. However, this reduction requires the numeric values to be exponentially large. In this paper, we present a (1.4 + ∊)-approximation algorithm with pseudo-polynomial running time. This implies that for polynomially bounded input data the problem can be approximated with a strictly better ratio than for exponential input which is a very rare phenomenon in combinatorial optimization.Our algorithm is based on a structural lemma proving that there is a packing of height (1.4 + ∊)OPT that allows a partition of the packing area into few rectangular boxes. These boxes have the property that they decouple the packing decisions for the items that are thin and high, wide and narrow, or large in both dimensions. The interaction of these item types is typically a major problem when designing algorithms and our partition completely solves this. Particularly difficult are items that are thin and high since a single one of them can increase the optimal packing height significantly if placed wrongly and there can be up to Ω(n) of them. For those items the box partition is even fine grained enough so that all items in a box have essentially the same height. This reduces the usually difficult packing decisions for these items to a problem that can be solved easily via a pseudo-polynomial time dynamic program.The mentioned reduction from Partition also breaks down if we allow to drop a constant number of input items (while the compared optimal solution cannot do this). We show that then we can approximate the problem much better and present a polynomial time algorithm computing a packing of height at most (1 + ∊)OPT that needs to drop at most O∊(1) items.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH102"}, {"primary_key": "4189900", "vector": [], "sparse_vector": [], "title": "An Algorithmic Hypergraph Regularity Lemma.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON>'s Regularity Lemma [22, 23] is a powerful tool in graph theory. It asserts that all large graphs G admit a bounded partition of E(G), most classes of which are bipartite subgraphs with uniformly distributed edges. The original proof of this result was non-constructive. A constructive proof was given by <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> [1], which allows one to efficiently construct a regular partition for any large graph.<PERSON><PERSON><PERSON><PERSON><PERSON>'s Regularity Lemma was extended to hypergraphs by various authors. <PERSON><PERSON> and <PERSON><PERSON><PERSON> [3] gave one such extension to 3-uniform hypergraphs, and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [19] extended this result to k-uniform hypergraphs. <PERSON><PERSON><PERSON><PERSON> [4, 5] gave another such extension. Similarly to the graph case, all of these proofs are non-constructive. We present an efficient algorithmic version of the Hypergraph Regularity Lemma for k-uniform hypergraphs.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH122"}, {"primary_key": "4189901", "vector": [], "sparse_vector": [], "title": "Beyond the Richter-Thomassen Conjecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "PREVIOUS ARTICLEComputing in continuous space with self-assembling polygonal tiles (extended abstract)", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH68"}, {"primary_key": "4189902", "vector": [], "sparse_vector": [], "title": "Locality-sensitive Hashing without False Negatives.", "authors": ["<PERSON><PERSON>"], "summary": "We consider a new construction of locality-sensitive hash functions for Hamming space that is covering in the sense that is it guaranteed to produce a collision for every pair of vectors within a given radius r. The construction is efficient in the sense that the expected number of hash collisions between vectors at distance cr, for a given c > 1, comes close to that of the best possible data independent LSH without the covering guarantee, namely, the seminal LSH construction of Indy<PERSON> and Motwani (FOCS ′98). The efficiency of the new construction essentially matches their bound if cr = log(n)/k, where n is the number of points in the data set and k ∊ N, and differs from it by at most a factor ln(4) in the exponent for general values of cr. As a consequence, LSH-based similarity search in Hamming space can avoid the problem of false negatives at little or no cost in efficiency. Read More: http://epubs.siam.org/doi/10.1137/1.*************.ch1", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH1"}, {"primary_key": "4189903", "vector": [], "sparse_vector": [], "title": "Evolutionary Dynamics in Finite Populations Mix Rapidly.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nisheeth K. <PERSON>"], "summary": "In this paper we prove that the mixing time of a broad class of evolutionary dynamics in finite, unstructured populations is roughly logarithmic in the size of the state space. An important special case of such a stochastic process is the Wright-Fisher model from evolutionary biology (with selection and mutation) on a population of size N over m genotypes. Our main result implies that the mixing time of this process is O(log N) for all mutation rates and fitness landscapes, and solves the main open problem from [4]. In particular, it significantly extends the main result in [18] who proved this for m = 2. Biologically, such models have been used to study the evolution of viral populations with applications to drug design strategies countering them. Here the time it takes for the population to reach a steady state is important both for the estimation of the steady-state structure of the population as well in the modeling of the treatment strength and duration. Our result, that such populations exhibit rapid mixing, makes both of these approaches sound.Technically, we make a novel connection between Markov chains arising in evolutionary dynamics and dynamical systems on the probability simplex. This allows us to use the local and global stability properties of the fixed points of such dynamical systems to construct a contractive coupling in a fairly general setting. We expect that our mixing time result would be useful beyond the evolutionary biology setting, and the techniques used here would find applications in bounding the mixing times of Markov chains which have a natural underlying dynamical system.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH36"}, {"primary_key": "4189904", "vector": [], "sparse_vector": [], "title": "On the Complexity of Dynamic Mechanism Design.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>via<PERSON>"], "summary": "We introduce a dynamic mechanism design problem in which the designer wants to offer for sale an item to an agent, and another item to the same agent at some point in the future. The agent's joint distribution of valuations for the two items is known, and the agent knows the valuation for the current item (but not for the one in the future). The designer seeks to maximize expected revenue, and the auction must be deterministic, truthful, and ex post individually rational. The optimum mechanism involves a protocol whereby the seller elicits the buyer's current valuation, and based on the bid makes two take-it-or-leave-it offers, one for now and one for the future. We show that finding the optimum deterministic mechanism in this situation — arguably the simplest meaningful dynamic mechanism design problem imaginable — is NP-hard. We also prove several positive results, among them a polynomial linear programming-based algorithm for the optimum randomized auction (even for many bidders and periods), and we show strong separations in revenue between non-adaptive, adaptive, and randomized auctions, even when the valuations in the two periods are uncorrelated. Finally, for the same problem in an environment in which contracts cannot be enforced, and thus perfection of equilibrium is necessary, we show that the optimum randomized mechanism requires multiple rounds of cheap talk-like interactions.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH100"}, {"primary_key": "4189905", "vector": [], "sparse_vector": [], "title": "Local-on-Average Distributed Tasks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "A distributed task is local if its time complexity is (nearly) constant, otherwise it is global. Unfortunately, local tasks are relatively scarce, and most distributed tasks require time at least logarithmic in the network size (and often higher than that). In a dynamic setting, i.e., when the network undergoes repeated and frequent topological changes, such as vertex and edge insertions and deletions, it is desirable to be able to perform a local update procedure around the modified part of the network, rather than running a static global algorithm from scratch following each change.This paper makes a step towards establishing the hypothesis that many (statically) non-local distributed tasks are local-on-average in the dynamic setting, namely, their amortized time complexity is O(log* n).Towards establishing the plausibility of this hypothesis, we propose a strategy for transforming static O(polylog(n)) time algorithms into dynamic O(log* n) amortized time update procedures. We then demonstrate the usefulness of our strategy by applying it to several fundamental problems whose static time complexity is logarithmic, including forest-decomposition, edge-orientation and coloring sparse graphs, and show that their amortized time complexity in the dynamic setting is indeed O(log* n).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH17"}, {"primary_key": "4189906", "vector": [], "sparse_vector": [], "title": "Dynamic (1 + ∊)-Approximate Matchings: A Density-Sensitive Approach.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Approximate matchings in fully dynamic graphs have been intensively studied in recent years. <PERSON> and <PERSON><PERSON> [FOCS'13] presented a deterministic algorithm for maintaining fully dynamic (1 + ∊)-approximate maximum cardinality matching (MCM) in general graphs with worst-case update time , for any ∊ > 0, where m denotes the current number of edges in the graph. Despite significant research efforts, this update time barrier remains the state-of-the-art even if amortized time bounds and randomization are allowed or the approximation factor is allowed to increase from 1 + ∊ to 2 – ∊, and even in basic graph families such as planar graphs.This paper presents a simple deterministic algorithm whose performance depends on the density of the graph. Specifically, we maintain fully dynamic (1 + ∊)-approximate MCM with worst-case update time O(α · ∊–2) for graphs with arboricity1 bounded by α. The update time bound holds even if the arboricity bound α changes dynamically. Since the arboricity ranges between 1 and , our density-sensitive bound O(α ·∊–2) naturally generalizes the bound of <PERSON> and <PERSON><PERSON>.For the family of bounded arboricity graphs (which includes forests, planar graphs, and graphs excluding a fixed minor), in the regime ∊ = O(1) our update time reduces to a constant. This should be contrasted with the previous best 2-approximation results for bounded arboricity graphs, which achieve either an O(log n) worst-case bound (<PERSON><PERSON><PERSON><PERSON> et al, ICALP'14) or an amortized bound (<PERSON> et al., ISAAC'14), where n stands for the number of vertices in the graph.En route to this result, we provide local algorithms of independent interest for maintaining fully dynamic approximate matching and vertex cover.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH51"}, {"primary_key": "4189907", "vector": [], "sparse_vector": [], "title": "Approximate Undirected Maximum Flows in O(mpolylog(n)) Time.", "authors": ["<PERSON>"], "summary": "We give the first O(mpolylog(n)) time algorithms for approximating maximum flows in undirected graphs and constructing polylog(n)-quality cut-approximating hierarchical tree decompositions. Our algorithm invokes existing algorithms for these two problems recursively while gradually incorporating size reductions. These size reductions are in turn obtained via ultra-sparsifiers, which are key tools in solvers for symmetric diagonally dominant (SDD) linear systems.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH130"}, {"primary_key": "4189908", "vector": [], "sparse_vector": [], "title": "Directed multicut is W[1]-hard, even for four terminal pairs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Directed multicut is W[1]-hard, even for four terminal pairs<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON>.1167 - 1178Chapter DOI:https://doi.org/10.1137/1.*************.ch81PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We prove that Multicut in directed graphs, parameterized by the size of the cutset, is W[1]-hard and hence unlikely to be fixed-parameter tractable even if restricted to instances with only four terminal pairs. This negative result almost completely resolves one of the central open problems in the area of parameterized complexity of graph separation problems, posted originally by Marx and Razgon [SIAM J. Comput. 43(2):355–388 (2014)], leaving only the case of three terminal pairs open. The case of two terminal pairs was shown to be FPT by <PERSON><PERSON><PERSON> et al. [SIAM J. Comput. 42(4):1674–1696 (2013)]. Our gadget methodology also allows us to prove W[1]-hardness of the Steiner Orientation problem parameterized by the number of terminal pairs, resolving an open problem of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [SIAM J. Discrete Math. 27(3):1503–1513 (2013)]. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH81"}, {"primary_key": "4189909", "vector": [], "sparse_vector": [], "title": "Phase Transitions in Group Testing.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The group testing problem consists of determining a sparse subset of a set of items that are \"defective\" based on a set of possibly noisy tests, and arises in areas such as medical testing, fault detection, communication protocols, pattern matching, and database systems. We study the fundamental limits of any group testing procedure regardless of its computational complexity. In the noiseless case with the number of defective items k scaling with the total number of items p as O(pθ) (θ ∊ (0, 1)), we show that the probability of reconstruction error tends to one when , but vanishes when , for some explicit constant c(θ). For θ ≤ ⅓, we show that c(θ) = 1, thus providing an exact threshold on the required number measurements, i.e. a phase transition, which was previously known only in the limit as θ → 0. Analogous necessary and sufficient conditions are derived for the noisy setting, and also for a relaxed partial recovery criterion.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH4"}, {"primary_key": "4189910", "vector": [], "sparse_vector": [], "title": "How to Round Subspaces: A New Spectral Clustering Algorithm.", "authors": ["<PERSON>"], "summary": "A basic problem in spectral clustering is the following. If a solution obtained from the spectral relaxation is close to an integral solution, is it possible to find this integral solution even though they might be in completely different basis? In this paper, we propose a new spectral clustering algorithm. It can recover a k-partition such that the subspace corresponding to the span of its indicator vectors is close to the original subspace in spectral norm with OPT being the minimum possible (OPT ≤ 1 always). Moreover our algorithm does not impose any restriction on the cluster sizes. Previously, no algorithm was known which could find a k-partition closer than o(k · OPT).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH128"}, {"primary_key": "4189911", "vector": [], "sparse_vector": [], "title": "Non-convex Compressed Sensing with the Sum-of-Squares Method.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider stable signal recovery in ℓq quasi-norm for 0 < q ≤ 1. In this problem, given a measurement vector y = Ax for some unknown signal vector x ∊ ℝn and a known matrix A ∊ ℝm×n, we want to recover z ∊ ℝn with ‖x – z‖q = O(‖x – x*‖q) from a measurement vector, where x* is the s-sparse vector closest to x in ℓq quasi-norm.Although a small value of q is favorable for measuring the distance to sparse vectors, previous methods for q < 1 involve ℓq quasi-norm minimization which is computationally intractable. In this paper, we overcome this issue by using the sum-of-squares method, and give the first polynomial-time stable recovery scheme for a large class of matrices A in ℓq quasi-norm for any fixed constant 0 < q ≤ 1.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH42"}, {"primary_key": "4189912", "vector": [], "sparse_vector": [], "title": "Discrete <PERSON><PERSON><PERSON> Reduces to CVP and SVP.", "authors": ["<PERSON>-<PERSON><PERSON>"], "summary": "The discrete Gaussian Dℒ–t,s is the distribution that assigns to each vector x in a shifted lattice ℒ — t probability proportional to . It has long been an important tool in the study of lattices. More recently, algorithms for discrete Gaussian sampling (DGS) have found many applications in computer science. In particular, polynomial-time algorithms for DGS with very high parameters s have found many uses in cryptography and in reductions between lattice problems. And, in the past year, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> showed 2n+o(n)-time algorithms for DGS with a much wider range of parameters and used them to obtain the current fastest known algorithms for the two most important lattice problems, the Shortest Vector Problem (SVP) and the Closest Vector Problem (CVP).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH121"}, {"primary_key": "4189913", "vector": [], "sparse_vector": [], "title": "Natural Algorithms for Flow Problems.", "authors": ["<PERSON>", "Nisheeth K. <PERSON>"], "summary": "In the last few years, there has been a significant interest in the computational abilities of Physarum polycephalum (a slime mold). This arose from a remarkable experiment which showed that this organism can compute shortest paths in a maze [10]. Subsequently, the workings of Physarum were mathematically modeled as a dynamical system and algorithms inspired by this model were proposed to solve several graph problems: shortest paths, flows, and linear programs to name a few. Indeed, computer scientists have initiated a rigorous study of these dynamics and a first step towards this was taken by [1,2] who proved that the Physarum dynamics for the shortest path problem are efficient (when edge-lengths are polynomially bounded). In this paper, we take this further: we prove that the discrete time Physarum-dynamics can also efficiently solve the uncapacitated mincost flow problems on undirected and directed graphs; problems that are non-trivial generalizations of shortest path. This raises the tantalizing possibility that nature, via evolution, developed algorithms that efficiently solve some of the most complex computational problems, about a billion years before we did.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH131"}, {"primary_key": "4189914", "vector": [], "sparse_vector": [], "title": "Approximate Distance Oracles for Planar Graphs with Improved Query Time-Space Tradeoff.", "authors": ["<PERSON><PERSON>"], "summary": "We consider approximate distance oracles for edge-weighted n-vertex undirected planar graphs. Given fixed ϵ > 0, we present a (1 + ϵ)-approximate distance oracle with O(n(log log n)2) space and O((loglogr?,)3) query time. This improves the previous best product of query time and space of the oracles of <PERSON><PERSON> (FOCS 2001, <PERSON><PERSON> 2004) and <PERSON> (SODA 2002) from O(nlogn) to O(n(loglogn)5).", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH26"}, {"primary_key": "4189915", "vector": [], "sparse_vector": [], "title": "Front Matter.", "authors": [], "summary": "There's never been a more exciting time to be involved with health informatics. In the last few decades, health informaticians have established the knowledge base and practical expertise to facilitate the development of ever-more capable technical systems, increasing connectivity, expanding access and greater mobility of e-health and information management systems. We have seen the evolution from simple computer based records to systems that allow intra-organisational, national, even international communication and information exchange. We have also seen progress in e-health and most recently m-health, facilitating access to information and advice almost anytime, anywhere. The health informatics community is now building on this strong foundation, taking a central role in the digital transformation of the healthcare sector. The Australian National Health Informatics Conference (HIC), Australia's premier health informatics event, is a key avenue for facilitating this transformation. This Conference, organised by the Health Informatics Society of Australia (HISA), with the support of the Australasian College of Health Informatics (ACHI), provides the ideal professional and social environment for clinicians, researchers, health IT professionals, industry and consumers to integrate, educate and share their knowledge to drive innovative thinking, to enhance services and allow greater consumer involvement. This is emphasised in the primary theme of the 2016 Conference: Digital Health Innovation for Consumers, Clinicians, Connectivity, Community. The papers in this volume reflect this theme, highlighting the cutting edge research evidence, technology updates and innovations that are seeing the digital transformation of the healthcare sector. The papers are indicative of the wide spectrum of work encompassing major theoretical concepts, examples of key applications of new technologies and important new developments in the field of health informatics. They emphasise the central role that health informatics and e-health play in connecting information systems, being smart with data, and enhancing both practitioner and consumer experience in healthcare interactions. Welcome to the innovation boom. This year's program maintains the high standard of papers for which the conference is well-known. All papers were blind-peer reviewed by three experts in the field of health informatics. These reviewers are widely considered to be prominent academics and industry specialists. The contribution of the Australasian College of Health Informatics, particularly the voluntary participation of Fellows, in supporting this review process is gratefully acknowledged. Similar contributions made by many senior and experienced members of the Health Informatics Society of Australia is also acknowledged. Forty papers underwent the initial review and feedback process. Resubmitted papers were then validated by the Scientific Program Committee to ensure that reviewers' recommendations were appropriately addressed or rebutted. In total 20 papers were selected for inclusion in this volume. Congratulations to all the authors. Andrew Georgiou Louise K. Schaper Sue Whetton.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.FM"}, {"primary_key": "4189916", "vector": [], "sparse_vector": [], "title": "Weighted SGD for ℓp Regression with Randomized Preconditioning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Chow", "<PERSON>", "<PERSON>"], "summary": "In recent years, stochastic gradient descent (SGD) methods and randomized linear algebra (RLA) algorithms have been applied to many large-scale problems in machine learning and data analysis. SGD methods are easy to implement and applicable to a wide range of convex optimization problems. In contrast, RLA algorithms provide much stronger worst-case performance guarantees but are applicable to a narrower class of problems. We aim to bridge the gap between these two classes of methods in solving constrained overdetermined linear regression problems—e.g., ℓ2 and ℓ1 regression problems.•We propose a hybrid algorithm named pwSGD that uses RLA techniques for preconditioning and constructing an importance sampling distribution, and then performs an SGD-like iterative process with weighted sampling on the preconditioned system.•By rewriting the ℓp regression problem into a stochastic optimization problem, we connect pwSGD to several existing ℓp solvers including RLA methods with algorithmic leveraging (RLA for short).•We prove that pwSGD inherits faster convergence rates that only depend on the lower dimension of the linear system, while maintaining low computation complexity. Such SGD convergence rate is superior to other related SGD algorithms such as the weighted randomized Kaczmarz algorithm.•Particularly, when solving ℓ1 regression with size n by d, pwSGD returns an approximate solution with ∊ relative error on the objective value in ℴ(log n · nnz(A) + poly(d)/∊2) time. This complexity is uniformly better than that of RLA methods in terms of both ∊ and d when the problem is unconstrained. In the presence of constraints, pwSGD only has to solve a sequence of much simpler and smaller optimization problem over the same constraints. In general this is more efficient than solving the constrained subproblem required in RLA.•For ℓ2 regression, pwSGD returns an approximate solution with ∊ relative error on the objective value and solution vector in prediction norm in ℴ(log n · nnz(A) + poly(d) log(1/∊)/∊) time. We show that when solving unconstrained ℓ2 regression, this complexity is comparable to that of RLA and is asymptotically better over several state-of-the-art solvers in the regime where the desired accuracy ∊, high dimension n and low dimension d satisfy d ≥ 1/∊ and n ≥ d2/∊.Finally, the effectiveness of such algorithms is illustrated numerically on both synthetic and real datasets, and the results are consistent with our theoretical findings and demonstrate that pwSGD converges to a medium-precision solution, e.g., ∊ = 10–3, more quickly than other methods.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH41"}, {"primary_key": "4189917", "vector": [], "sparse_vector": [], "title": "Gowers Norm, Function Limits, and Parameter Estimation.", "authors": ["<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2016 Annual ACM-SIAM Symposium on Discrete Algorithms (SODA)Gowers Norm, Function Limits, and Parameter Estimation<PERSON><PERSON><PERSON>daYuichi Yoshidapp.1391 - 1406Chapter DOI:https://doi.org/10.1137/1.*************.ch96PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Let be a sequence of functions, where p is a fixed prime and Fp is the finite field of order p. The limit of the sequence can be syntactically defined using the notion of ultralimit. Inspired by the Gowers norm, we introduce a metric over limits of function sequences, and study properties of it. One application of this metric is that it provides a simpler characterization of affine-invariant parameters of functions that are constant-query estimable than the previous one obtained by <PERSON><PERSON><PERSON> (STOC'14). Using this characterization, we show that the property of being a function of a constant number of low-degree polynomials and a constant number of factored polynomials (of arbitrary degrees) is constant-query testable if it is closed under blowing-up. Examples of this property include the property of having a constant spectral norm and degree-structural properties with rank conditions. Previous chapter Next chapter RelatedDetails Published:2016eISBN:978-1-61197-433-1 https://doi.org/10.1137/1.*************Book Series Name:ProceedingsBook Code:PRDA16Book Pages:viii + 2106", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH96"}, {"primary_key": "4189918", "vector": [], "sparse_vector": [], "title": "Expanders via Local Edge Flips.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "Designing distributed and scalable algorithms to improve network connectivity is a central topic in peer-to-peer networks. In this paper we focus on the following well-known problem: given an n-node d-regular network for d = Ω(log n), we want to design a decentralized, local algorithm that transforms the graph into one that has good connectivity properties (low diameter, expansion, etc.) without affecting the sparsity of the graph. To this end, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> introduced the random \"flip\" transformation, where in each time step, a random pair of vertices that have an edge decide to 'swap a neighbor'. They conjectured that performing O(nd) such flips at random would convert any connected d-regular graph into a d-regular expander graph, with high probability. However, the best known upper bound for the number of steps is roughly O(n17d23), obtained via a delicate Markov chain comparison argument.Our main result is to prove that a natural instantiation of the random flip produces an expander in at most steps, with high probability. Our argument uses a potential-function analysis based on the matrix exponential, together with the recent beautiful results on the higher-order <PERSON><PERSON><PERSON> inequality of graphs. We also show that our technique can be used to analyze another well-studied random process known as the 'random switch', and show that it produces an expander in O(nd) steps with high probability.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.CH19"}, {"primary_key": "4211275", "vector": [], "sparse_vector": [], "title": "Proceedings of the Twenty-Seventh Annual ACM-SIAM Symposium on Discrete Algorithms, SODA 2016, Arlington, VA, USA, January 10-12, 2016", "authors": ["<PERSON>"], "summary": "This symposium focuses on research topics related to efficient algorithms and data structures for discrete problems. In addition to the design of such methods and structures, the scope also includes their use, performance analysis, and the mathematical problems related to their development or limitations. Performance analyses may be analytical or experimental and may address worst-case or expected-case performance. Studies can be theoretical or based on data sets that have arisen in practice and may address methodological issues involved in performance analysis.", "published": "2016-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************"}]