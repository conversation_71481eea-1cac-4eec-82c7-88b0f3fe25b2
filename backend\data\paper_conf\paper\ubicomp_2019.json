[{"primary_key": "2860411", "vector": [], "sparse_vector": [], "title": "CORMORANT: Ubiquitous Risk-Aware Multi-Modal Biometric Authentication across Mobile Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "People own and carry an increasing number of ubiquitous mobile devices, such as smartphones, tablets, and notebooks. Being small and mobile, those devices have a high propensity to become lost or stolen. Since mobile devices provide access to their owners' digital lives, strong authentication is vital to protect sensitive information and services against unauthorized access. However, at least one in three devices is unprotected, with inconvenience of traditional authentication being the paramount reason. We present the concept of CORMORANT, an approach to significantly reduce the manual burden of mobile user verification through risk-aware, multi-modal biometric, cross-device authentication. Transparent behavioral and physiological biometrics like gait, voice, face, and keystroke dynamics are used to continuously evaluate the user's identity without explicit interaction. The required level of confidence in the user's identity is dynamically adjusted based on the risk of unauthorized access derived from signals like location, time of day and nearby devices. Authentication results are shared securely with trusted devices to facilitate cross-device authentication for co-located devices. Conducting a large-scale agent-based simulation of 4 000 users based on more than 720 000 days of real-world device usage traces and 6.7 million simulated robberies and thefts sourced from police reports, we found the proposed approach is able to reduce the frequency of password entries required on smartphones by 97.82% whilst simultaneously reducing the risk of unauthorized access in the event of a crime by 97.72%, compared to conventional knowledge-based authentication.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351243"}, {"primary_key": "2860437", "vector": [], "sparse_vector": [], "title": "Quadmetric Optimized Thumb-to-Finger Interaction for Force Assisted One-Handed Text Entry on Mobile Headsets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tong Li", "<PERSON>", "<PERSON><PERSON> Su", "Pan Hui"], "summary": "Augmented reality head-worn computers often feature small-sized touch interfaces that complicate interaction with content, provide insufficient space for comfortable text input, and can be awkward to use in social situations. This paper presents a novel one-handed thumb-to-finger text entry solution for augmented reality head-worn computers. We design a glove composed of 12 force-sensitive nodes featuring an ambiguous keyboard layout. We first explore the viability of force disambiguation to evaluate the force division within the force spectrum. We select a 3-level force division as it allows to considerably reduce the number of keys while featuring a high (83.9%) accuracy. Following this pilot study, we map the 26 English characters onto the 9 nodes located on the index, middle and ring fingers in a 3-3-3 configuration, and attribute the space, enter and backspace keys to the remaining three nodes. We consider text entry performance as a quadmetric optimization problem considering the following criteria: goodness of character pairs, layout similarity to the QWERTY keyboard, easiness of force interaction, and comfort level of thumb reach. The resulting layout strikes a balance between performance and usability. We finally evaluate the quadmetric optimized layout over 6 sessions with 12 participants. The participants achieve an average text entry rate of 6.47 WPM with 6.85% error rate in the final session, which is significantly faster than existing thumb-to-finger solutions. In addition, our one-handed text entry system enhances the user mobility compared to other state-of-the-art solutions by freeing one hand, while allowing the user to direct his visual attention to other activities.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351252"}, {"primary_key": "2860466", "vector": [], "sparse_vector": [], "title": "Measuring the Effects of Stress on Mobile Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Research shows that environmental factors such as ambient noise and cold ambience can render users situationally impaired, adversely affecting interaction with mobile devices. However, an internal factor which is known to negatively impact cognitive abilities -- stress -- has not been systematically investigated in terms of its impact on mobile interaction. In this paper, we report a study where we use the Trier Social Stress Test to induce stress on participants, and investigate its effect on three aspects of mobile interaction: target acquisition, visual search, and text entry. We find that stress reduces completion time and accuracy during target acquisition tasks, as well as completion time during visual search tasks. Finally, we are able to directly contrast the magnitude of these effects to previously published effects of environmentally-caused impairments. Our work contributes to the growing body of literature on situational impairments.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314411"}, {"primary_key": "2860491", "vector": [], "sparse_vector": [], "title": "News From the Background to the Foreground: How People Use Technology To Manage Media Transitions: A Study of Technology-mediated News Behaviors in a Hyper-connected World.", "authors": ["<PERSON>-Brock", "<PERSON>"], "summary": "People are the designers and curators of their own news and information ecosystems, due to the disruption of the news industry and developments in media technology. To understand how people use technology to manage their news consumption, we conducted a two-week diary study with 14 participants, focusing on how people transition between news content and behaviors via different media, sources, platforms and devices. We used an inductive, qualitative analysis of the diary study data to analyze the news behaviors and their underlying motivations and found that people frequently shift their focus between ambient background news streams and active foreground news behaviors. Although people often passively consume news content as a background activity, they also actively manage background news habits to increase the chances of relevant foreground experiences. People manage news consumption by developing routines that are often supported by technology use and social interactions. We encourage product designers to treat backgrounding as an essential part of news consumption behavior and suggest new design directions that employ ubiquitous computing technologies---such as context sensing and routine modeling---to more effectively attend to background-to-foreground behaviors and transitions.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351268"}, {"primary_key": "2860519", "vector": [], "sparse_vector": [], "title": "LimbMotion: Decimeter-level Limb Tracking for Wearable-based Human-Computer Interaction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Wearable-based human-computer interaction is a promising technology to enable various applications. This paper aims to track the 3D posture of the entire limb, both wrist/ankle and elbow/knee, of a user wearing a smart device. This limb tracking technology can trace the geometric motion of the limb, without introducing any training stage usually required in gesture recognition approaches. Nonetheless, the tracked limb motion can also be used as a generic input for gesture-based applications. The 3D posture of a limb is defined by the relative positions among main joints, e.g., shoulder, elbow, and wrist for an arm. When a smartwatch is worn on the wrist of a user, its position is affected by both elbow and shoulder motions. It is challenging to infer the entire 3D posture when only given a single point of sensor data from the smartwatch. In this paper, we propose LimbMotion, an accurate and real-time limb tracking system. The performance gain of LimbMotion comes from multiple key technologies, including an accurate attitude estimator based on a novel two-step filter, fast acoustic ranging, and point clouds-based positioning. We implemented LimbMotion and evaluated its performance using extensive experiments, including different gestures, moving speeds, users, and limbs. Results show that LimbMotion achieves real-time tracking with a median error of 7.5cm to 8.9cm, which outperforms the state-of-the-art approach by about 32%.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369836"}, {"primary_key": "2860360", "vector": [], "sparse_vector": [], "title": "FlexTouch: Enabling Large-Scale Interaction Sensing Beyond Touchscreens Using Flexible and Conductive Materials.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Shwetak N. Patel", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present FlexTouch, a technique that enables large-scale interaction sensing beyond the spatial constraints of capacitive touchscreens using passive low-cost conductive materials. This is achieved by customizing 2D circuit-like patterns with an array of conductive strips that can be easily attached to the sensing nodes on the edge of the touchscreen. FlexTouch requires no hardware modification, and is compatible with various conductive materials (copper foil tape, silver nanoparticle ink, ITO frames, and carbon paint), as well as fabrication methods (cutting, coating, and ink-jet printing). Through a series of studies and illustrative examples, we demonstrate that FlexTouch can support long-range touch sensing for up to 4 meters and everyday object presence detection for up to 2 meters. Finally, we show the versatility and feasibility of FlexTouch through applications such as body posture recognition, human-object interaction as well as enhanced fitness training experiences.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351267"}, {"primary_key": "2860363", "vector": [], "sparse_vector": [], "title": "I3: Sensing Scrolling Human-Computer Interactions for Intelligent Interest Inference on Smartphones.", "authors": ["Li Lu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Xiangyu Xu"], "summary": "The scrolling interaction is a pervasive human-computer interaction on smartphones, which can reflect intrinsic characteristics during dynamic browsings. Different from extrinsic statistical measures like frequency of visits and dwell time, intrinsic features underlying scrolling interactions reveal fine-grained implicit feedbacks about user interests. Toward this end, we explore user interest inference by extracting efficient browsing features from scrolling human-computer interactions on smartphones. In this paper, we first analyze browsing traces of 40 volunteers, and find two intrinsic browsing features underlying scrolling interactions, i.e., browsing velocity stability and browsing velocity sequence, which are tightly related to user interests. Inspired by the observation, we propose an Intelligent Interest Inference system, I3, which infers user interests through sensing scrolling interactions during browsings. Specifically, I3 first extracts the two intrinsic browsing features from users' scrolling interactions. Then, I3 applies a Naive Bayesian-based approach to construct an interest discriminator for coarse-grained user interest (i.e., like-preferred or dislike-preferred) inference. Furthermore, we develop a deep learning-based approach for I3 to train rating classifiers for fine-grained rating inference in like-preferred and dislike-preferred browsings respectively. Finally, I3 utilizes the interest discriminator and rating classifiers to infer exact user ratings about browsing contents on smartphones. Experimental results under browsing traces of 46 volunteers present that I3 achieves 92.4% overall accuracy in interest inference.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351255"}, {"primary_key": "2860366", "vector": [], "sparse_vector": [], "title": "Scaling Crowdsourcing with Mobile Workforce: A Case Study with Belgian Postal Service.", "authors": ["Utku Günay Acer", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional urban-scale crowdsourcing approaches suffer from three caveats - lack of complete spatiotemporal coverage, lack of accurate information and lack of sustained engagement of crowd workers. In this paper, we argue that these caveats can be addressed by embedding crowdsourcing tasks into the daily routine of mobile workforces that roam around an urban area. As a use case, we take the bpost who deliver the letters and parcels to the citizens across entire Belgium. We present a study that explores the behavioural attributes of these mobile postal workers both quantitatively (6.3K) and qualitatively (6) to assess the opportunity of leveraging them for crowdsourcing tasks. We report their mobility pattern, workflow, and behavioural traits which collectively inform the design of a purpose-built crowdsourcing solution. In particular, our solution operates on two key techniques - route augmentation, and on-wearable interruptibility management. Together, these mechanisms enhance the spatial coverage, response accuracy and increase workers' engagement with crowdsourcing tasks. We describe these principal components in a wearable smartwatch application supported by a data management infrastructure. Finally, we report a first-of-its-kind real-world trial with ten postal workers for two weeks to assess the quality of road signs at the city centre of Antwerp. Our findings suggest that our solution was effective in achieving 89% spatial coverage and increasing response rate (83.6%) and accuracy (100%) of the crowdsourcing tasks. Although limited in scale, these and the rest of our findings highlight the way of building an efficient and purposeful crowdsourcing solution of the future.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328906"}, {"primary_key": "2860370", "vector": [], "sparse_vector": [], "title": "To Mask or Not to Mask?: Balancing Privacy with Visual Confirmation Utility in Activity-Oriented Wearable Cameras.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Lucia C<PERSON>", "<PERSON>", "Nabil <PERSON>"], "summary": "Activity-oriented cameras are increasingly being used to provide visual confirmation of specific hand-related activities in real-world settings. However, recent studies have shown that bystander privacy concerns limit participant willingness to wear a camera. Researchers have investigated different image obfuscation methods as an approach to enhance bystander privacy; however, these methods may have varying effects on the visual confirmation utility of the image, which we define as the ability of a human viewer to interpret the activity of the wearer in the image. Visual confirmation utility is needed to annotate and validate hand-related activities for several behavioral-based applications, particularly in cases where a human in the loop method is needed to label (e.g., annotating gestures that cannot be automatically detected yet). We propose a new type of obfuscation, activity-oriented partial obfuscation, as a methodological contribution to researchers interested in obtaining visual confirmation of hand-related activities in the wild. We tested the effects of this approach by collecting ten diverse and realistic video scenarios that involved the wearer performing hand-related activities while bystanders performed activities that could be of concern if recorded. Then we conducted an online experiment with 367 participants to evaluate the effect of varying degrees of obfuscation on bystander privacy and visual confirmation utility. Our results show that activity-oriented partial obfuscation (1) maintains visual confirmation of the wearer's hand-related activity, especially when an object is present in the hand, and even when extreme filters are applied, while (2) significantly reducing bystander concerns and enhancing bystander privacy. Informed by our analysis, we further discuss the impact of the filter method used in activity-oriented partial obfuscation on bystander privacy and concerns.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351230"}, {"primary_key": "2860372", "vector": [], "sparse_vector": [], "title": "Mobile Gait Analysis Using Foot-Mounted UWB Sensors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We demonstrate a new foot-mounted sensor system for mobile gait analysis which is based on Ultra Wideband (UWB) technology. Our system is wireless, inexpensive, portable, and able to estimate clinical measurements that are not currently available in traditional Inertial Measurement Unit (IMU) based wearables such as step width and foot positioning. We collect a dataset of over 2000 steps across 21 people to test our system in comparison with the clinical gold-standard GAITRite, and other IMU-based algorithms. We propose methods to calculate gait metrics from the UWB data that our system collects. Our system is then validated against the GAITRite mat, measuring step width, step length, and step time with mean absolute errors of 0.033m, 0.032m, and 0.012s respectively. This system has the potential for use in many fields including sports medicine, neurological diagnostics, fall risk assessment, and monitoring of the elderly.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351231"}, {"primary_key": "2860375", "vector": [], "sparse_vector": [], "title": "RAMT: Real-time Attitude and Motion Tracking for Mobile Devices in Moving Vehicle.", "authors": ["Chongguang Bi", "<PERSON><PERSON><PERSON>"], "summary": "Recently a class of new in-vehicle technologies based on off-the-shelf mobile devices have been developed to improve driving safety and experience. For instance, wearables like the smartwatches are utilized to monitor the action of the driver and detect possible secondary tasks. Moreover, wearables can allow a driver to use gesture for in-vehicle controls, reducing distractions to driving. The accuracy of these systems can be significantly improved by tracking the real-time attitude of mobile devices. This paper proposes a novel system called Real-time Attitude and Motion Tracking (RAMT) that can enable a mobile device to accurately learn the coordinate system of a moving vehicle, and hence track its attitude and motion in real time. RAMT consists of a series of lightweight algorithms to sense the vehicle's movement and calculate the device's attitude. It provides a solution for trajectory-based gesture recognition. We have implemented RAMT on a smartphone and a smartwatch and evaluated the performance in 10 real driving trips. Our results show that the overall error of the coordinate system alignment is around 5° for the smartphone and 10° for the smartwatch, and over 84% of customized hand gestures can be accurately recognized with the result of RAMT. A video demo of RAMT is available at https://youtu.be/9rZp7HxyRts.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328909"}, {"primary_key": "2860381", "vector": [], "sparse_vector": [], "title": "Understanding Parents&apos; Perspectives on Mealtime Technology.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "For young children, family meals are an enjoyable and developmentally useful part of daily life. Although prior work has shown that ubiquitous computing solutions can enhance children's eating habits and mealtime experiences in valuable ways, other work demonstrates that many families are hesitant to use technology in this context. This paper examines adoption barriers for technology for family meals to understand with more nuance what parents value and resist in this space. Using mixed methods, we first observed family dinnertime experiences and then surveyed 122 parents with children from two to six years old. We found that parents prefer screen-based technology over voice interfaces and smart objects, because parents perceive the latter two systems to intrude on their relationship with children. The pervasiveness of smart objects embedded at meals led parents to worry about distraction and technology dependence, while the anthropomorphization of voice interfaces led parents to worry that this technology could displace parenting relationships or disrupt interpersonal interactions among family members. Parents mindlessly applied social scripts to voice interfaces, suggesting families may be more likely to apply concerns from interpersonal interactions to voice interfaces than to other technologies. We discuss the ways different form factors appeal to and worry parents, providing designers with insights about the likelihood of adoption and acceptance.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314392"}, {"primary_key": "2860382", "vector": [], "sparse_vector": [], "title": "TurnsMap: Enhancing Driving Safety at Intersections with Mobile Crowdsensing and Deep Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Left turns are known to be one of the most dangerous driving maneuvers.1 An effective way to mitigate this safety risk is to install a left-turn enforcement --- e.g., a protected left-turn signal or all-way stop signs --- at every turn that preserves a traffic phase exclusively for left turns. Although this protection scheme can significantly increase the driving safety, information on whether or not a road segment (e.g., intersection) has such a setting is not yet available to the public and navigation systems. This paper presents a system, called TurnsMap, that exploits mobile crowdsensing and deep learning to classify the protection settings of left turns. One of our key findings is that crowdsensed IMU sensor (i.e., gyroscope and accelerometer) data from onboard mobile devices can be used to recognize different types of left-turn protection. TurnsMap first collects IMU sensor data from mobile devices or smartphones carried by the driver/passenger(s) in a moving car. It then feeds the data to an analytics engine powered by (1) a data mining engine for extracting and clustering left turns by processing raw IMU data, and (2) a deep-learning pipeline for learning the model from the IMU data to identify the protection type of each left turn. We have built and used a large-scale real-world driving dataset to evaluate TurnsMap, demonstrating its capability of identifying different left-turn enforcements with 90.3% accuracy. A wide range of automotive apps can benefit (e.g., enhancing traffic safety) from the left-turns information unearthed by TurnsMap.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351236"}, {"primary_key": "2860384", "vector": [], "sparse_vector": [], "title": "Your Table Can Be an Input Panel: Acoustic-based Device-Free Interaction Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chaocan <PERSON>", "<PERSON>"], "summary": "This paper explores the possibility of extending the input and interactions beyond the small screen of the mobile device onto ad hoc adjacent surfaces, e.g., a wooden tabletop with acoustic signals. While the existing finger tracking approaches employ the active acoustic signal with a fixed frequency, our proposed system Ipanel employs the acoustic signals generated by sliding of fingers on the table for tracking. Different from active signal tracking, the frequency of the finger-table generated acoustic signals keeps changing, making accurate tracking much more challenging than the traditional approaches with fix frequency signal from the speaker. Unique features are extracted by exploiting the spatio-temporal and frequency domain properties of the generated acoustic signals. The features are transformed into images and then we employ the convolutional neural network (CNN) to recognize the finger movement on the table. Ipanel is able to support not only commonly used gesture (click, flip, scroll, zoom, etc.) recognition, but also handwriting (10 numbers and 26 alphabets) recognition at high accuracies. We implement Ipanel on smartphones, and conduct extensive real environment experiments to evaluate its performance. The results validate the robustness of Ipanel, and show that it maintains high accuracies across different users with varying input behaviours (e.g., input strength, speed and region). Further, Ipanel's performance is robust against different levels of ambient noise and varying surface materials.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314390"}, {"primary_key": "2860386", "vector": [], "sparse_vector": [], "title": "Multi-Stage Receptivity Model for Mobile Just-In-Time Health Intervention.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sangkeun Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A critical aspect of mobile just-in-time (JIT) health intervention is proper delivery timing, which correlates with successfully promoting target behaviors. Despite extensive prior studies on interruptibility, however, our understanding of the receptivity of mobile JIT health intervention is limited. This work extends prior interruptibility models to capture the JIT intervention process by including multiple stages of conscious and subconscious decisions. We built BeActive, a mobile intervention system for preventing prolonged sedentary behaviors, and we collected users' responses to a given JIT support and relevant contextual factors and cognitive/physical states for three weeks. Using a multi-stage model, we systematically analyzed the responses to deepen our understanding of receptivity using a mixed methodology. Herein, we identify the key factors relevant to each stage outcome and show that the receptivity of JIT intervention is nuanced and context-dependent. We propose several practical design implications for mobile JIT health intervention and context-aware computing.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328910"}, {"primary_key": "2860392", "vector": [], "sparse_vector": [], "title": "PocketCare: Tracking the Flu with Mobile Phones Using Partial Observations of Proximity and Symptoms.", "authors": ["<PERSON>", "Tong <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile phones provide a powerful sensing platform that researchers may adopt to understand proximity interactions among people and the diffusion, through these interactions, of diseases, behaviors, and opinions. However, it remains a challenge to track the proximity-based interactions of a whole community and then model the social diffusion of diseases and behaviors starting from the observations of a small fraction of the volunteer population. In this paper, we propose a novel approach that tries to connect together these sparse observations using a model of how individuals interact with each other and how social interactions happen in terms of a sequence of proximity interactions. We apply our approach to track the spreading of flu in the spatial-proximity network of a 3000-people university campus by mobilizing 300 volunteers from this population to monitor nearby mobile phones through Bluetooth scanning and to daily report flu symptoms about and around them. Our aim is to predict the likelihood for an individual to get flu based on how often her/his daily routine intersects with those of the volunteers. Thus, we use the daily routines of the volunteers to build a model of the volunteers as well as of the non-volunteers. Our results show that we can predict flu infection two weeks ahead of time with an average precision from 0.24 to 0.35 depending on the amount of information. This precision is six to nine times higher than with a random guess model. At the population level, we can predict infectious population in a two-week window with an r-squared value of 0.95 (a random-guess model obtains an r-squared value of 0.2). These results point to an innovative approach for tracking individuals who have interacted with people showing symptoms, allowing us to warn those in danger of infection and to inform health researchers about the progression of contact-induced diseases.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328912"}, {"primary_key": "2860394", "vector": [], "sparse_vector": [], "title": "Evaluating the Impact of Technology Assisted Hotspot Policing on Situational Awareness and Task-Load.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Everyday field work of a police officer requires the perception, filtering and understanding of large amounts of information in highly dynamic situations. This presents opportunities for ICT to alleviate strain on officers by providing adequate information provisioning. We evaluate the usage of a mobile location-based hotspot policing system, comprised of a smartphone, smartwatch and a web-application, during real field work with officers in high and low hotspot density locations. We use a repeated measures design to compare possible effects with our baseline measure, i.e. field work without using the system. Usability, task-load and situational awareness (SA), as well as possible mediators, are evaluated to gain insight into the differences between modes of transportation and the overall viability of the system itself. No significant difference was found between the two locations. Officers using the system scored high on usability measures and interview feedback was largely positive. Measures on SA remained stable throughout baseline and experimental shifts. Task-load was significantly higher with the use of the system. The contradiction in these findings can be explained by showing the differences in the nature of field work with and without the system.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314396"}, {"primary_key": "2860395", "vector": [], "sparse_vector": [], "title": "Decentralized Attention-based Personalized Human Mobility Prediction.", "authors": ["Zipei Fan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human mobility prediction is essential to a variety of human-centered computing applications achieved through upgrading of location-based services (LBS) to future-location-based services (FLBS). Previous studies on human mobility prediction have mainly focused on centralized human mobility prediction, where user mobility data are collected, trained and predicted at the cloud server side. However, such a centralized approach leads to a high risk of privacy issues, and a real-time centralized system for processing such a large volume of distributed data is extremely difficult to apply. Moreover, a large and dynamic set of users makes the predictive model extremely challenging to personalize. In this paper, we propose a novel decentralized attention-based human mobility predictor in which 1) no additional training procedure is required for personalized prediction, 2) no additional training procedure is required for incremental learning, and 3) the predictor can be trained and predicted in a decentralized way. We tested our method on big data of real-world mobile phone user GPS and on Android devices, and achieved a low-power consumption and a good prediction accuracy without collecting user data in the server or applying additional training on the user side.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369830"}, {"primary_key": "2860398", "vector": [], "sparse_vector": [], "title": "Viewport Prediction for Live 360-Degree Mobile Video Streaming Using User-Content Hybrid Motion Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "360-degree video streaming has been gaining popularities recently with the rapid growth of adopting mobile head mounted display (HMD) devices in the consumer video market, especially for live broadcasts. The 360-degree video streaming introduces brand new bandwidth and latency challenges in live streaming due to the significantly increased video data. However, most of the existing bandwidth saving approaches based on viewport prediction have only focused on the video-on-demand (VOD) use cases leveraging historical user behavior data, which is not available in live broadcasts. We develop a new viewport prediction scheme for live 360-degree video streaming using video content-based motion tracking and dynamic user interest modeling. To obtain real-time performance, we implement the Gaussian mixture model (GMM) and optical flow algorithms for motion detection and feature tracking. Then, the user's future viewport of interest is generated by leveraging a dynamic user interest model that weighs all the features and motion information abstracted from the live video frames. Furthermore, we develop two enhancement techniques that take into consideration of user feedback for fast error recovery and view updates. Consequently, our predicted viewports are irregular and dynamically adjusted to cover the maximum portions of the actual user viewports and thus ensure a high prediction accuracy. We evaluate our viewport prediction approach using a public user head movement dataset, which contains the data of 48 users watching 6 360-degree videos. The experimental results show that the proposed approach supports sophisticated user head movement patterns and outperforms the existing velocity-based approach in terms of prediction accuracy. In addition, the motion tracking scheme introduces minimum latency overhead to ensure the quality of live streaming experience.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328914"}, {"primary_key": "2860399", "vector": [], "sparse_vector": [], "title": "Perils of Zero-Interaction Security in the Internet of Things.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Internet of Things (IoT) demands authentication systems which can provide both security and usability. Recent research utilizes the rich sensing capabilities of smart devices to build security schemes operating without human interaction, such as zero-interaction pairing (ZIP) and zero-interaction authentication (ZIA). Prior work proposed a number of ZIP and ZIA schemes and reported promising results. However, those schemes were often evaluated under conditions which do not reflect realistic IoT scenarios. In addition, drawing any comparison among the existing schemes is impossible due to the lack of a common public dataset and unavailability of scheme implementations. In this paper, we address these challenges by conducting the first large-scale comparative study of ZIP and ZIA schemes, carried out under realistic conditions. We collect and release the most comprehensive dataset in the domain to date, containing over 4250 hours of audio recordings and 1 billion sensor readings from three different scenarios, and evaluate five state-of-the-art schemes based on these data. Our study reveals that the effectiveness of the existing proposals is highly dependent on the scenario they are used in. In particular, we show that these schemes are subject to error rates between 0.6% and 52.8%.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314397"}, {"primary_key": "2860400", "vector": [], "sparse_vector": [], "title": "EarEcho: Using Ear Canal Echo for Wearable Authentication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Smart wearable devices have recently become one of the major technological trends and been widely adopted by the general public. Wireless earphones, in particular, have seen a skyrocketing growth due to its great usability and convenience. With the goal of seeking a more unobtrusive wearable authentication method that the users can easily use and conveniently access, in this study we present EarEcho as a novel, affordable, user-friendly biometric authentication solution. EarEcho takes advantages of the unique physical and geometrical characteristics of human ear canal and assesses the content-free acoustic features of in-ear sound waves for user authentication in a wearable and mobile manner. We implemented the proposed EarEcho on a proof-of-concept prototype and tested it among 20 subjects under diverse application scenarios. We can achieve a recall of 94.19% and precision of 95.16% for one-time authentication, while a recall of 97.55% and precision of 97.57% for continuous authentication. EarEcho has demonstrated its stability over time and robustness to cope with the uncertainties on the varying background noises, body motions, and sound pressure levels.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351239"}, {"primary_key": "2860402", "vector": [], "sparse_vector": [], "title": "Towards Reliable, Automated General Movement Assessment for Perinatal Stroke Screening in Infants Using Wearable Accelerometers.", "authors": ["<PERSON>", "<PERSON>", "Yu <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Perinatal stroke (PS) is a serious condition that, if undetected and thus untreated, often leads to life-long disability, in particular Ce<PERSON><PERSON><PERSON> Palsy (CP). In clinical settings, <PERSON><PERSON><PERSON>'s General Movement Assessment (GMA) can be used to classify infant movements using a Gestalt approach, identifying infants at high risk of developing PS. Training and maintenance of assessment skills are essential and expensive for the correct use of GMA, yet many practitioners lack these skills, preventing larger-scale screening and leading to significant risks of missing opportunities for early detection and intervention for affected infants. We present an automated approach to GMA, based on body-worn accelerometers and a novel sensor data analysis method--Discriminative Pattern Discovery (DPD)--that is designed to cope with scenarios where only coarse annotations of data are available for model training. We demonstrate the effectiveness of our approach in a study with 34 newborns (21 typically developing infants and 13 PS infants with abnormal movements). Our method is able to correctly recognise the trials with abnormal movements with at least the accuracy that is required by newly trained human annotators (75%), which is encouraging towards our ultimate goal of an automated PS screening system that can be used population-wide.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314399"}, {"primary_key": "2860404", "vector": [], "sparse_vector": [], "title": "Using Unobtrusive Wearable Sensors to Measure the Physiological Synchrony Between Presenters and Audience Members.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The widespread adoption of mobile and wearable devices enables new approaches for the unobtrusive and continuous monitoring of humans' behavior, physiological state, interactions and more. Within this line of research, we focus on the physiological synchrony between a presenter and her audience and investigate whether it can be used to characterize the experience of presenters and audience members during presentations. To this end, we collect data from 17 presenters and six audience members during a two-days conference. For 40, unique presenter-audience pairs we gather electrodermal activity (EDA) signals and self-reports on different aspects of the experience: engagement, immersion and enjoyment/satisfaction. For 28 of these pairs, we also collect inter-beat interval (IBI) traces. We then apply seven approaches for measuring the synchrony of physiological signals and we contextualize these measures using metrics derived from the self-reports. We find that physiological synchrony -- measured using the Dynamic Time Warping algorithm -- can be used as a proxy to quantify participants' agreement on self-reported engagement. Our findings can be used to provide automated presenter-audience feedback in a conference setting and may be applicable in other scenarios, including education (teacher-student), arts (performer-audience), or meetings (presenter-audience).", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314400"}, {"primary_key": "2860405", "vector": [], "sparse_vector": [], "title": "Keyboard Snooping from Mobile Phone Arrays with Mixed Convolutional and Recurrent Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ubiquity of modern smartphones, because they are equipped with a wide range of sensors, poses a potential security risk---malicious actors could utilize these sensors to detect private information such as the keystrokes a user enters on a nearby keyboard. Existing studies have examined the ability of phones to predict typing on a nearby keyboard but are limited by the realism of collected typing data, the expressiveness of employed prediction models, and are typically conducted in a relatively noise-free environment. We investigate the capability of mobile phone sensor arrays (using audio and motion sensor data) for classifying keystrokes that occur on a keyboard in proximity to phones around a table, as would be common in a meeting. We develop a system of mixed convolutional and recurrent neural networks and deploy the system in a human subjects experiment with 20 users typing naturally while talking. Using leave-one-user-out cross validation, we find that mobile phone arrays have the ability to detect 41.8% of keystrokes and 27% of typed words correctly in such a noisy environment---even without user specific training. To investigate the potential threat of this attack, we further developed the machine learning models into a realtime system capable of discerning keystrokes from an array of mobile phones and evaluated the system's ability with a single user typing in varying conditions. We conclude that, in order to launch a successful attack, the attacker would need advanced knowledge of the table from which a user types, and the style of keyboard on which a user types. These constraints greatly limit the feasibility of such an attack to highly capable attackers and we therefore conclude threat level of this attack to be low, but non-zero.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328916"}, {"primary_key": "2860409", "vector": [], "sparse_vector": [], "title": "The Wearables Development Toolkit: An Integrated Development Environment for Activity Recognition Applications.", "authors": ["<PERSON>"], "summary": "Although the last two decades have seen an increasing number of activity recognition applications with wearable devices, there is still a lack of tools specifically designed to support their development. The development of activity recognition algorithms for wearable devices is particularly challenging because of the several requirements that have to be met simultaneously (e.g., low energy consumption, small and lightweight, accurate recognition). Activity recognition applications are usually developed in a series of iterations to annotate sensor data and to analyze, develop and assess the performance of a recognition algorithm. This paper presents the Wearables Development Toolkit, an Integrated Development Environment designed to lower the entrance barrier to the development of activity recognition applications with wearables. It specifically focuses on activity recognition using on-body inertial sensors. The toolkit offers a repository of high-level reusable components and a set of tools with functionality to annotate data, to analyze and develop activity recognition algorithms and to assess their recognition and computational performance. We demonstrate the versatility of the toolkit with three applications and describe how we developed it incrementally based on two user studies.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369813"}, {"primary_key": "2860416", "vector": [], "sparse_vector": [], "title": "A Deep Reinforcement Learning-Enabled Dynamic Redeployment System for Mobile Ambulances.", "authors": ["Shenggong Ji", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Protecting citizens' lives from emergent accidents (e.g. traffic accidents) and diseases (e.g. heart attack) is of vital importance in urban computing. Every day many people are caught in emergent accidents or diseases and thus need ambulances to transport them to hospitals. In this paper, we propose a dynamic ambulance redeployment system to reduce the time needed for ambulances to pick up patients and to increase the probability of patients being saved in time. For patients in danger, every second counts. Specifically, whenever there is an ambulance becoming available (e.g. finishing transporting a patient to a hospital), our dynamic ambulance redeployment system will redeploy it to a proper ambulance station such that it can better pick up future patients. However, the dynamic ambulance redeployment is challenging, as when we redeploy an available ambulance we need to simultaneously consider each station's multiple dynamic factors. To trade off these multiple factors using handcrafted rules are almost impossible. To deal with this issue, we propose using a deep neural network, called deep score network, to balance each station's dynamic factors into one score, leveraging the excellent representation ability of deep neural networks. And then we propose a deep reinforcement learning framework to learn the deep score network. Finally, based on the learned deep score network, we provide an effective dynamic ambulance redeployment algorithm. Experiment results using data collected in real world show clear advantages of our method over baselines, e.g. comparing with baselines, our method can save ~100 seconds (~20%) of average pickup time of patients and improve the ratio of patients being picked up within 10 minutes from 0.786 to 0.838. With our method, people in danger can be better saved.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314402"}, {"primary_key": "2860420", "vector": [], "sparse_vector": [], "title": "Detachable Smartwatch: More Than A Wearable.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Kent Lyons"], "summary": "Glanceability and low access time are arguably the key assets of a smartwatch. However, smartwatches are currently limited to micro-interactions. They do not enable complex interactions and, in general, they do not afford continuous use for long. We believe that smartwatches can retain micro-interactions and glanceability, but also get better at long and complex interactions. We propose a smartwatch that a user can detach, and use as more than a wearable depending on their context, requirements, and preference. Detaching the watch enables it to morph into different forms, and thereby become a better interaction device, better display, and a better sensor suite. First, we interview participants to elicit usage themes for a detachable watch. Then, we build applications that showcase the range of use-cases where a detachable smartwatch offers additional functionality compared to an always-worn one, and highlights the affordances and benefits enabled due to detachability.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328921"}, {"primary_key": "2860421", "vector": [], "sparse_vector": [], "title": "Modeling Personality vs. Modeling Personalidad: In-the-wild Mobile Data Analysis in Five Countries Suggests Cultural Impact on Personality Models.", "authors": ["<PERSON>", "Sumer S. Vaid", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Sensor data collected from smartphones provides the possibility to passively infer a user's personality traits. Such models can be used to enable technology personalization, while contributing to our substantive understanding of how human behavior manifests in daily life. A significant challenge in personality modeling involves improving the accuracy of personality inferences, however, research has yet to assess and consider the cultural impact of users' country of residence on model replicability. We collected mobile sensing data and self-reported Big Five traits from 166 participants (54 women and 112 men) recruited in five different countries (UK, Spain, Colombia, Peru, and Chile) for 3 weeks. We developed machine learning based personality models using culturally diverse datasets -- representing different countries -- and we show that such models can achieve state-of-the-art accuracy when tested in new countries, ranging from 63% (Agreeableness) to 71% (Extraversion) of classification accuracy. Our results indicate that using country-specific datasets can improve the classification accuracy between 3% and 7% for Extraversion, Agreeableness, and Conscientiousness. We show that these findings hold regardless of gender and age balance in the dataset. Interestingly, using gender- or age- balanced datasets as well as gender-separated datasets improve trait prediction by up to 17%. We unpack differences in personality models across the five countries, highlight the most predictive data categories (location, noise, unlocks, accelerometer), and provide takeaways to technologists and social scientists interested in passive personality assessment.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351246"}, {"primary_key": "2860426", "vector": [], "sparse_vector": [], "title": "GoalKeeper: Exploring Interaction Lockout Mechanisms for Regulating Smartphone Use.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Minsam Ko", "<PERSON><PERSON><PERSON>"], "summary": "Many people often experience difficulties in achieving behavioral goals related to smartphone use. Most of prior studies approached this problem with various behavior change strategies such as self-reflection and social support. However, little is known about the effectiveness and user experiences of restrictive and coercive interventions such as blocking. In this work, we developed \"GoalKeeper,\" a smartphone intervention app that locks the user into the self-defined daily use time limit with restrictive intervention mechanisms. We conducted a four-week field experiment with 36 participants to investigate the effects and user experiences of varying intensities of restrictive interventions. The results showed that restrictive mechanisms are more effective than non-restrictive mechanisms such as warning. However, we found that restrictive mechanisms caused more frustration and pressure to the users, mainly due to diversity of usage contexts and needs. Based on our study results, we extracted practical implications for designing restrictive mechanisms that balance the intervention effectiveness for behavioral changes and the flexibility for user acceptability.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314403"}, {"primary_key": "2860438", "vector": [], "sparse_vector": [], "title": "FMT: A Wearable Camera-Based Object Tracking Memory Aid for Older Adults.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Fan", "Khai N. <PERSON>"], "summary": "Older adults sometimes forget about whether or not they have completed routine actions and the states of objects that they have interacted with (e.g., the kitchen stove is on or off). In this work, we explore whether video clips captured from a body-worn camera every time objects of interest are found within its field of view can help older adults determine if they have completed certain actions with these objects and what their states are. We designed FMT (\"Fiducial Marker Tracker\")---a real-time capture and access application that opportunistically captures video clips of objects the user interacts with. To do this, the user places fiducial markers close to objects which would be captured when the marker enters the user's body-worn camera's field of view. We examine and discuss what objects this system would be best suited to track, and the usefulness and usability of this approach. FMT successfully captured direct interactions with an object at an average rate of 75.6% across all participants (SD = 9.9%). Our results also reveal how, what, and why users would use such a system for help.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351253"}, {"primary_key": "2860443", "vector": [], "sparse_vector": [], "title": "MAAT: Mobile Apps As Things in the IoT.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As the Internet of Things (IoT) proliferates, the potential for its opportunistic interaction with traditional mobile apps becomes apparent. We argue that to fully take advantage of this potential, mobile apps must become things themselves, and interact in a smart space like their hardware counterparts. We present an extension to our Atlas thing architecture on smartphones, allowing mobile apps to behave as things and provide powerful services and functionalities. To this end, we also consider the role of the mobile app developer, and introduce actionable keywords (AKWs)---a dynamically programmable description---to enable potential thing to thing interactions. The AKWs empower the mobile app to dynamically react to services provided by other things, without being known a priori by the original app developer. In this paper, we present the mobile-apps-as-things (MAAT) concept along with its AKW concept and programming construct. For MAAT to be adopted by developers, changes to the existing development environments (IDE) should remain minimal to stay acceptable and practically usable, thus we also propose an IDE plugin to simplify the addition of this dynamic behavior. We present details of MAAT, along with the implementation of the IDE plugin, and give a detailed benchmarking evaluation to assess the responsiveness of our implementation to impromptu interactions and dynamic app behavioral changes. We also investigate another study, targeting Android developers, which evaluates the acceptability and usability of the MAAT IDE plugin.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369823"}, {"primary_key": "2860444", "vector": [], "sparse_vector": [], "title": "Privacy Adversarial Network: Representation Learning for Mobile Data Privacy.", "authors": ["<PERSON><PERSON><PERSON>", "Junz<PERSON> Du", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The remarkable success of machine learning has fostered a growing number of cloud-based intelligent services for mobile users. Such a service requires a user to send data, e.g. image, voice and video, to the provider, which presents a serious challenge to user privacy. To address this, prior works either obfuscate the data, e.g. add noise and remove identity information, or send representations extracted from the data, e.g. anonymized features. They struggle to balance between the service utility and data privacy because obfuscated data reduces utility and extracted representation may still reveal sensitive information. This work departs from prior works in methodology: we leverage adversarial learning to better balance between privacy and utility. We design a representation encoder that generates the feature representations to optimize against the privacy disclosure risk of sensitive information (a measure of privacy) by the privacy adversaries, and concurrently optimize with the task inference accuracy (a measure of utility) by the utility discriminator. The result is the privacy adversarial network (PAN), a novel deep model with the new training algorithm, that can automatically learn representations from the raw data. And the trained encoder can be deployed on the user side to generate representations that satisfy the task-defined utility requirements and the user-specified/agnostic privacy budgets. Intuitively, PAN adversarially forces the extracted representations to only convey information required by the target task. Surprisingly, this constitutes an implicit regularization that actually improves task accuracy. As a result, PAN achieves better utility and better privacy at the same time! We report extensive experiments on six popular datasets, and demonstrate the superiority of PAN compared with alternative methods reported in prior work.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369816"}, {"primary_key": "2860450", "vector": [], "sparse_vector": [], "title": "My Mom was Getting this Popup: Understanding Motivations and Processes in Helping Older Relatives with Mobile Security and Privacy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Security and privacy pose a serious barrier to the use of mobile technology by older adults. While support from family and friends is known to be an effective enabler in older adults' technology adoption, we know very little about the family members' motivations for providing help, the context, and the process in which they provide it. To bridge this gap, we have conducted a mixed method study, qualitatively analyzing the helpers' assistance stories and quantitatively estimating the factors that affect helpers' willingness to offer assistance to older relatives regarding mobile security and privacy problems. Our findings point to the potential for helping older relatives, i.e., people are more willing to help and guide them than other social groups. Furthermore, we show that familiarity with an older relative's preferences is essential in providing meaningful support. We discuss our findings in the context of developing a theory of collective efficacy for security and privacy and new collaborative technologies that can reduce the barriers to social help.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369821"}, {"primary_key": "2860451", "vector": [], "sparse_vector": [], "title": "Differentiating Higher and Lower Job Performers in the Workplace Using Mobile Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Pino G. Audia", "<PERSON>", "<PERSON><PERSON><PERSON>", "Vedant <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sidney K. D&apos;Mello", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Kaifeng Jiang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Subigya Nepal", "<PERSON><PERSON>", "Man<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Assessing performance in the workplace typically relies on subjective evaluations, such as, peer ratings, supervisor ratings and self assessments, which are manual, burdensome and potentially biased. We use objective mobile sensing data from phones, wearables and beacons to study workplace performance and offer new insights into behavioral patterns that distinguish higher and lower performers when considering roles in companies (i.e., supervisors and non-supervisors) and different types of companies (i.e., high tech and consultancy). We present initial results from an ongoing year-long study of N=554 information workers collected over a period ranging from 2-8.5 months. We train a gradient boosting classifier that can classify workers as higher or lower performers with AUROC of 0.83. Our work opens the way to new forms of passive objective assessment and feedback to workers to potentially provide week by week or quarter by quarter guidance in the workplace.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328908"}, {"primary_key": "2860461", "vector": [], "sparse_vector": [], "title": "Unsupervised Factory Activity Recognition with Wearable Sensors Using Process Instruction Information.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents an unsupervised method for recognizing assembly work done by factory workers by using wearable sensor data. Such assembly work is a common part of line production systems and typically involves the factory workers performing a repetitive work process made up of a sequence of manual operations, such as setting a board on a workbench and screwing parts onto the board. This study aims to recognize the starting and ending times for individual operations in such work processes through analysis of sensor data collected from the workers along with analysis of the process instructions that detail and describe the flow of operations for each work process. We propose a particle-filter-based factory activity recognition method that leverages (i) trend changes in the sensor data detected by a nonparametric Bayesian hidden Markov model, (ii) semantic similarities between operations discovered in the process instructions, (iii) sensor-data similarities between consecutive repetitions of individual operations, and (iv) frequent sensor-data patterns (motifs) discovered in the overall assembly work processes. We evaluated the proposed method using sensor data from six workers collected in actual factories, achieving a recognition accuracy of 80% (macro-averaged F-measure).", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328931"}, {"primary_key": "2860469", "vector": [], "sparse_vector": [], "title": "Using Deep Learning and Mobile Offloading to Control a 3D-printed Prosthetic Hand.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Pan Hui"], "summary": "Although many children are born with congenital limb malformation, contemporary functional artificial hands are costly and are not meant to be adapted to growing hand. In this work, we develop a low cost, adaptable and personalizable system of an artificial prosthetic hand accompanied with hardware and software modules. Our solution consists of (i) a consumer grade electromyography (EMG) recording hardware, (ii) a mobile companion device empowered by deep learning classification algorithms, (iii) an cloud component for offloading computations, and (iv) mechanical 3D printed arm operated by the embedded hardware. We focus on the flexibility of the designed system making it more affordable than the alternatives. We use 3D printed materials and open-source software thus enabling the community to contribute and improve the system. In this paper, we describe the proposed system and its components and present the experiments we conducted in order to show the feasibility and applicability of our approach. Extended experimentation shows that our proposal is energy efficient and has high accuracy.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351260"}, {"primary_key": "2860478", "vector": [], "sparse_vector": [], "title": "Enhancing Augmented VR Interaction via Egocentric Scene Analysis.", "authors": ["<PERSON>", "Chi-Wing Fu", "Sheng<PERSON> Zhao", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Augmented virtual reality (AVR) takes portions of the physical world into the VR world to enable VR users to access physical objects. State-of-the-art solutions mainly focus on extracting and showing physical objects in the VR world. In this work, we go beyond previous solutions and propose a novel approach to realize AVR. We first analyze the physical environment in the user's egocentric view through depth sensing and deep learning, then acquire the layout and geometry of the surrounding objects, and further explore their affordances. Based on the above information, we create visual guidance (hollowed guiding path) and hybrid user interfaces (augmented physical notepad, LR finger slider, and LRRL finger slider) to augment the AVR interaction. Empirical evaluations showed that the participants responded positively to our AVR techniques.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351263"}, {"primary_key": "2860484", "vector": [], "sparse_vector": [], "title": "AppMoD: Helping Older Adults Manage Mobile Security with Online Social Help.", "authors": ["Zhiyuan Wan", "Ling<PERSON> Bao", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The rapid adoption of Smartphone devices has caused increasing security and privacy risks and breaches. Catching up with ever-evolving contemporary smartphone technology challenges leads older adults (aged 50+) to reduce or to abandon their use of mobile technology. To tackle this problem, we present AppMoD, a community-based approach that allows delegation of security and privacy decisions a trusted social connection, such as a family member or a close friend. The trusted social connection can assist in the appropriate decision or make it on behalf of the user. We implement the approach as an Android app and describe the results of three user studies (n=50 altogether), in which pairs of older adults and family members used the app in a controlled experiment. Using app anomalies as an ongoing case study, we show how delegation improves the accuracy of decisions made by older adults. Also, we show how combining decision-delegation with crowdsourcing can enhance the advice given and improve the decision-making process. Our results suggest that a community-based approach can improve the state of mobile security and privacy.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369819"}, {"primary_key": "2860485", "vector": [], "sparse_vector": [], "title": "LeakDoctor: Toward Automatically Diagnosing Privacy Leaks in Mobile Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the enormous popularity of smartphones, millions of mobile apps are developed to provide rich functionalities for users by accessing certain personal data, leading to great privacy concerns. To address this problem, many approaches have been proposed to detect privacy disclosures in mobile apps, but they largely fail to automatically determine whether the privacy disclosures are necessary for the functionality of apps. As a result, security analysts may easily face with a large number of false positives when directly adopting such approaches for app analysis. In this paper, we propose LeakDoctor, an analysis system seeking to automatically diagnose privacy leaks by judging if a privacy disclosure from an app is necessary for some functionality of the app. Functionality-irrelevant privacy disclosures are not justifiable, so considered as potential privacy leak cases. To achieve this goal, LeakDoctor integrates dynamic response differential analysis with static response taint analysis. In addition, it employs a novel technique to locate the program statements of each privacy disclosure. We implement a prototype of LeakDoctor and evaluate it against 1060 apps, which contain 2,095 known disclosure cases. Our experimental results show that LeakDoctor can automatically determine that 71.9% of the privacy disclosure cases indeed serve apps' functionalities and are justifiable. Hence, with the diagnosis results of <PERSON>kDoc<PERSON>, analysts may avoid analyzing many justifiable privacy disclosures and only focus on the those unjustifiable cases.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314415"}, {"primary_key": "2860503", "vector": [], "sparse_vector": [], "title": "Automated Detection of Infant Holding Using Wearable Sensing: Implications for Developmental Science and Intervention.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Physical contact is critical for children's physical and emotional growth and well-being. Previous studies of physical contact are limited to relatively short periods of direct observation and self-report methods. These methods limit researchers' understanding of the natural variation in physical contact across families, and its specific impacts on child development. In this study we develop a mobile sensing platform that can provide objective, unobtrusive, and continuous measurements of physical contact in naturalistic home interactions. Using commercially available motion detectors, our model reaches an accuracy of 0.870 (std: 0.059) for a second-by-second binary classification of holding. In addition, we detail five assessment scenarios applicable to the development of activity recognition models for social science research, where required accuracy may vary as a function of the intended use. Finally, we propose a grand vision for leveraging mobile sensors to access high-density markers of multiple determinants of early parent-child interactions, with implications for basic science and intervention.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328935"}, {"primary_key": "2860505", "vector": [], "sparse_vector": [], "title": "SoberComm: Using Mobile Phones to Facilitate Inter-family Communication with Alcohol-dependent Patients.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jui<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper reports on the development of a mobile support system (called SoberComm) aimed at facilitating the sharing of alcohol-use data between patients and their family members, while providing treatment teams with the data necessary to make constructive suggestions concerning inter-family communications. We began by conducting a pilot study to identify salient themes, which could be used to guide the overall design of the system. We then conducted an iterative development process involving the prototyping and evaluation of user interfaces using medium-fidelity wireframes. The final high-fidelity design was used in the practical implementation of SoberComm in a two-week field study involving nine dyads of alcohol dependent patients and their family members. Results demonstrate that SoberComm makes it easier for patients to refuse alcohol and deal with many of the issues underlying the difficulties they face. Responses collected via qualitative interviews also provided qualitative evidence that the proposed system can enhance problem-solving skills and facilitate communication between alcohol-dependent patients and their family members.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351277"}, {"primary_key": "2860512", "vector": [], "sparse_vector": [], "title": "GEVR: An Event Venue Recommendation System for Groups of Mobile Users.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Qin Lv", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we present GEVR, the first Group Event Venue Recommendation system that incorporates mobility via individual location traces and context information into a \"social-based\" group decision model to provide venue recommendations for groups of mobile users. Our study leverages a real-world dataset collected using the OutWithFriendz mobile app for group event planning, which contains 625 users and over 500 group events. We first develop a novel \"social-based\" group location prediction model, which adaptively applies different group decision strategies to groups with different social relationship strength to aggregate each group member's location preference, to predict where groups will meet. Evaluation results show that our prediction model not only outperforms commonly used and state-of-the-art group decision strategies with over 80% accuracy for predicting groups' final meeting location clusters, but also provides promising qualities in cold-start scenarios. We then integrate our prediction model with the Foursquare Venue Recommendation API to construct an event venue recommendation framework for groups of mobile users. Evaluation results show that GEVR outperforms the comparative models by a significant margin.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314421"}, {"primary_key": "2860361", "vector": [], "sparse_vector": [], "title": "Beyond Respiration: Contactless Sleep Sound-Activity Recognition Using RF Signals.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Xiao<PERSON> Chen", "<PERSON><PERSON><PERSON>"], "summary": "Sleep sound-activities including snore, cough and somniloquy are closely related to sleep quality, sleep disorder and even illnesses. To obtain the information of these activities, current solutions either require the user to wear various sensors/devices, or use the camera/microphone to record the image/sound data. However, many people are reluctant to wear sensors/devices during sleep. The video-based and audio-based approaches raise privacy concerns. In this work, we propose a novel system TagSleep to address the issues mentioned above. For the first time, we propose the concept of two-layer sensing. We employ the respiration sensing information as the basic first-layer information, which is applied to further obtain rich second-layer sensing information including snore, cough and somniloquy. Specifically, without attaching any device to the human body, by just deploying low-cost and flexible RFID tags near to the user, we can accurately obtain the respiration information. What's more interesting, the user's cough, snore and somniloquy all affect his/her respiration, so the fine-grained respiration changes can be used to infer these sleep sound-activities without recording the sound data. We design and implement our system with just three RFID tags and one RFID reader. We evaluate the performance of TagSleep with 30 users (13 males and 17 females) for a period of 2 months. TagSleep is able to achieve higher than 96.58% sensing accuracy in recognizing snore, cough and somniloquy under various sleep postures. TagSleep also boosts the sleep posture recognition accuracy to 98.94%.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351254"}, {"primary_key": "2860362", "vector": [], "sparse_vector": [], "title": "Secure Your Voice: An Oral Airflow-Based Continuous Liveness Detection for Voice Assistants.", "authors": ["<PERSON>", "Wandong Cai", "<PERSON> Gu", "<PERSON>", "Yannan Li", "<PERSON>"], "summary": "Voice control has attracted extensive attention recently as it is a prospective User Interface (UI) to substitute for conventional touch control on smart devices. Voice assistants have become increasingly popular in our daily lives, especially for those people who are visually impaired. However, the inherently insecure nature of voice biometrics means that voice assistants are vulnerable to spoofing attacks as evidenced by security experts. To secure the commands for voice assistants, in this paper, we present a liveness detection system that provides continuous speaker verification on smart devices. The basic aim is to match the voice received by the smart device's microphone with the oral airflow of the user when speaking a command. The airflow is captured by an auxiliary commercial off-the-shelf airflow sensor. Specifically, we establish a theoretical model to depict the relationship between the oral airflow pressure and the phonemes in users' speech. The system estimates a series of pressures from the speech according to the theoretical model, and then calculates the consistency between the estimated pressure signal and the actual pressure signal measured by the airflow sensor to determine whether a command is a genuine \"live\" voice or an artificially generated one. We evaluate the system with 26 participants and 30 different voice commands. The evaluation showed that our system achieves an overall accuracy of 97.25% with an Equal Error Rate (EER) of 2.08%.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369811"}, {"primary_key": "2860364", "vector": [], "sparse_vector": [], "title": "WiDetect: Robust Motion Detection with a Statistical Electromagnetic Model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Yi Han", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Motion detection acts as a key component for a range of applications such as home security, occupancy and activity monitoring, retail analytics, etc. Most existing solutions, however, require special installation and calibration and suffer from frequent false alarms with very limited coverage. In this paper, we propose WiDetect, a highly accurate, robust, and calibration-free wireless motion detector that achieves almost zero false alarm rate and large through-the-wall coverage. Different from previous approaches that either extract data-driven features or assume a few reflection multipaths, we model the problem from a perspective of statistical electromagnetic (EM) by accounting for all multipaths indoors. By exploiting the statistical theory of EM waves, we establish a connection between the autocorrelation function of the physical layer channel state information (CSI) and target motion in the environment. On this basis, we devise a novel motion statistic that is independent of environment, location, orientation, and subjects, and then perform a hypothesis testing for motion detection. By harnessing abundant multipaths indoors, WiDetect can detect arbitrary motion, be it in Line-Of-Sight vicinity or behind multiple walls, providing sufficient whole-home coverage for typical apartments and houses using a single link on commodity WiFi. We conduct extensive experiments in a typical office, an apartment, and a single house with different users for an overall period of more than 5 weeks. The results show that WiDetect achieves a remarkable detection accuracy of 99.68% with a zero false rate, significantly outperforming the state-of-the-art solutions and setting up the stage for ubiquitous motion sensing in practice.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351280"}, {"primary_key": "2860365", "vector": [], "sparse_vector": [], "title": "Classifying Attention Types with Thermal Imaging and Eye Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Despite the importance of attention in user performance, current methods for attention classification do not allow to discriminate between different attention types. We propose a novel method that combines thermal imaging and eye tracking to unobtrusively classify four types of attention: sustained, alternating, selective, and divided. We collected a data set in which we stimulate these four attention types in a user study (N = 22) using combinations of audio and visual stimuli while measuring users' facial temperature and eye movement. Using a Logistic Regression on features extracted from both sensing technologies, we can classify the four attention types with high AUC scores up to 75.7% for the user independent-condition independent, 87% for the user-independent-condition dependent, and 77.4% for the user-dependent prediction. Our findings not only demonstrate the potential of thermal imaging and eye tracking for unobtrusive classification of different attention types but also pave the way for novel applications for attentive user interfaces and attention-aware computing.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351227"}, {"primary_key": "2860367", "vector": [], "sparse_vector": [], "title": "Leveraging Active Learning and Conditional Mutual Information to Minimize Data Annotation in Human Activity Recognition.", "authors": ["<PERSON>", "<PERSON>az"], "summary": "A difficulty in human activity recognition (HAR) with wearable sensors is the acquisition of large amounts of annotated data for training models using supervised learning approaches. While collecting raw sensor data has been made easier with advances in mobile sensing and computing, the process of data annotation remains a time-consuming and onerous process. This paper explores active learning as a way to minimize the labor-intensive task of labeling data. We train models with active learning in both offline and online settings with data from 4 publicly available activity recognition datasets and show that it performs comparably to or better than supervised methods while using around 10% of the training data. Moreover, we introduce a method based on conditional mutual information for determining when to stop the active learning process while maximizing recognition performance. This is an important issue that arises in practice when applying active learning to unlabeled datasets.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351228"}, {"primary_key": "2860368", "vector": [], "sparse_vector": [], "title": "EduSense: Practical Classroom Sensing at Scale.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Providing university teachers with high-quality opportunities for professional development cannot happen without data about the classroom environment. Currently, the most effective mechanism is for an expert to observe one or more lectures and provide personalized formative feedback to the instructor. Of course, this is expensive and unscalable, and perhaps most critically, precludes a continuous learning feedback loop for the instructor. In this paper, we present the culmination of two years of research and development on EduSense, a comprehensive sensing system that produces a plethora of theoretically-motivated visual and audio features correlated with effective instruction, which could feed professional development tools in much the same way as a Fitbit sensor reports step count to an end user app. Although previous systems have demonstrated some of our features in isolation, EduSense is the first to unify them into a cohesive, real-time, in-the-wild evaluated, and practically-deployable system. Our two studies quantify where contemporary machine learning techniques are robust, and where they fall short, illuminating where future work remains to bring the vision of automated classroom analytics to reality.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351229"}, {"primary_key": "2860369", "vector": [], "sparse_vector": [], "title": "mORAL: An mHealth Model for Inferring Oral Hygiene Behaviors in-the-wild Using Wrist-worn Inertial Sensors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We address the open problem of reliably detecting oral health behaviors passively from wrist-worn inertial sensors. We present our model named mORAL (pronounced em oral) for detecting brushing and flossing behaviors, without the use of instrumented toothbrushes so that the model is applicable to brushing with still prevalent manual toothbrushes. We show that for detecting rare daily events such as toothbrushing, adopting a model that is based on identifying candidate windows based on events, rather than fixed-length timeblocks, leads to significantly higher performance. Trained and tested on 2,797 hours of sensor data collected over 192 days on 25 participants (using video annotations for ground truth labels), our brushing model achieves 100% median recall with a false positive rate of one event in every nine days of sensor wearing. The average error in estimating the start/end times of the detected event is 4.1% of the interval of the actual toothbrushing event.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314388"}, {"primary_key": "2860371", "vector": [], "sparse_vector": [], "title": "An Optimized Recurrent Unit for Ultra-Low-Power Keyword Spotting.", "authors": ["Justice <PERSON>", "<PERSON><PERSON>"], "summary": "There is growing interest in being able to run neural networks on sensors, wearables and internet-of-things (IoT) devices. However, the computational demands of neural networks make them difficult to deploy on resource-constrained edge devices. To meet this need, our work introduces a new recurrent unit architecture that is specifically adapted for on-device low power acoustic event detection (AED). The proposed architecture is based on the gated recurrent unit ('GRU' -- introduced by <PERSON> et al. [9]) but features optimizations that make it implementable on ultra-low power micro-controllers such as the Arm Cortex M0+. Our new architecture, the Embedded Gated Recurrent Unit (eGRU) is demonstrated to be highly efficient and suitable for short-duration AED and keyword spotting tasks. A single eGRU cell is 60× faster and 10× smaller than a GRU cell. Despite its optimizations, eGRU compares well with GRU across tasks of varying complexities. The practicality of eGRU is investigated in a wearable acoustic event detection application. An eGRU model is implemented and tested on the Arm Cortex M0-based Atmel ATSAMD21E18 processor. The Arm M0+ implementation of the eGRU model compares favorably with a full precision GRU that is running on a workstation. The embedded eGRU model achieves a classification accuracy 95.3%, which is only 2% less than the full precision GRU.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328907"}, {"primary_key": "2860373", "vector": [], "sparse_vector": [], "title": "Design and Evaluation of DIO Construction Toolkit for Co-making Shared Constructions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present the design and implementation of DIO, a novel digital-physical construction toolkit to enable constructionist learning for children from age group 8-12 years. The toolkit comprises of dome-shaped (D) tangible modules with various attachments that allow suspension on the body of multiple children and/or in the environment to support a variety of sensing/input (I), actuation/output (O) functionalities. The modules are enabled for wireless communication and can be linked together using an Augmented Reality based programming interface running on a smartphone. The smartphone recognizes our hemispherical modules omnidirectionally through novel computer vision based 3D patterns; custom made to provide logical as well as semantic encoding. In this paper, we show how, owing to its unique form-factor, the toolkit enables multi-user constructions for the children and offers a shared learning experience. We further reflect on our learning from a one-year long iterative design process and contribute a social scaffolding based procedure to engage children with such constructionist toolkits effectively.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369833"}, {"primary_key": "2860374", "vector": [], "sparse_vector": [], "title": "Back to Real Pictures: A Cross-generational Understanding of Users&apos; Mental Models of Photo Cloud Storage.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Personal pictures storage is currently split between a myriad of physical and digital tools. Cloud photo storage and social networks are seeing increasing adoption, and are being recommended to families (especially older generations) as digital pictures solutions. The ubiquity of these platforms raises the question of whether the design of their photo-based operations consider the mental models of their cross-generational users. Understanding mental models is a key factor for the usability (and adoption) of these technologies. Previous works have observed that perceptions of digital storage limit adoption, especially for older users. However, we do not yet understand users' mental models of these applications. This impedes efforts to design applications better matching diverse user needs. We present here a cross-generational investigation of users' mental models of ubiquitous picture technologies, including cloud storage and social sharing. We find that mental models are split (both between generations and domains), contributing to lower adoption by older adults. Our analysis reveals that digital tools need to understand their roots in physical pictures and bridge this divide by including physical concepts as an aspect of use, if we are to support cross-generational interactions with personal and family pictures.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351232"}, {"primary_key": "2860376", "vector": [], "sparse_vector": [], "title": "The Positive Impact of Push vs Pull Progress Feedback: A 6-week Activity Tracking Study in the Wild.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Octavia Zahrt", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Lack of physical activity has been shown to increase disease and reduce life expectancy. In response, mobile devices are increasingly being used to support people's health and fitness by tracking physical activity. Prior work shows that the type of feedback, either ambient or via notification, affects users' behavior towards their physical activity. Yet, these phone- and watch-based interactions and notifications have primarily been visual in nature. Inspired by prior research, we explored the impact of feedback modality (visual, tactile, and hybrid: visual/tactile) on 44 participants' behavior and exercise mindset in a 6-week field study. We present the differences between modalities and the notion of push vs. pull for interface feedback and notifications. Across 1,662 days of study data, we found statistically significant impacts of feedback modality and, in particular, the positive effects of push feedback on participants' mindset about the process of exercise. Our results also highlight design guidelines for wearables and multimodal notification systems.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351234"}, {"primary_key": "2860377", "vector": [], "sparse_vector": [], "title": "ProspecFit: In Situ Evaluation of Digital Prospective Memory Training for Older Adults.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sur<PERSON>"], "summary": "Prospective Memory (PM), which involves remembering to perform intended actions, is the primary source of everyday memory lapses. While existing solutions mostly focus on supportive memory aids and reminders, it is also crucial to maintain PM functions and independent living for older adults. We present ProspecFit, which digitises implementation intentions, a lab-based memory intervention, making it available on smartphones through iterative design that draws insights from a focus group and preliminary studies. We evaluated its usability and effectiveness in enhancing PM through user studies that included a 12-day in situ study, and pre- and post-testing with 10 adults (61 to 80 years old). Participants in the digital PM training group were more prompt in performing the in situ PM tasks, compared to the control group without digital training, and reported improvement in their PM compared to before the training. We also show findings from diary entries, reports on forgetful moments and user reactions. Our work provides implications for creating digital memory training tools in HCI.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351235"}, {"primary_key": "2860378", "vector": [], "sparse_vector": [], "title": "Tourgether: Exploring Tourists&apos; Real-time Sharing of Experiences as a Means of Encouraging Point-of-Interest Exploration.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We developed Tourgether, an app that enables tourists' mutual sharing of their experiences via check-ins in real time, to enhance their awareness and exploration of various points of interest (POIs) in a tourism region. We conducted formative studies and a between-subjects field experiment to assess how tourists used Tourgether in their travels, and the influence of real-time experience-sharing on unplanned POI visits, respectively. The results of the formative studies indicated that seeing shared real-time experiences encouraged tourists to explore and make unplanned visits to less well-known POIs, and that their decisions to make unplanned POI visits were dependent on familiarity, worthiness, and convenience. The app also created a feeling of co-presence among tourists, boosting their desire to interact with others. Two strong motivators for tourists to check in on the app were identified: contributing to other tourists, and recording journeys. Our experimental results further showed that seeing shared real-time experiences prompted the participants to make more unplanned visits than would have been the case if they had not seen them. This influence was more prominent among tourists who planned more POI visits. Other differences in the usage and influence of Tourgether across these two groups will also be discussed.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369832"}, {"primary_key": "2860379", "vector": [], "sparse_vector": [], "title": "LungTrack: Towards Contactless and Zero Dead-Zone Respiration Monitoring with Commodity RFIDs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Xiao<PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang", "Tao Yan", "<PERSON><PERSON><PERSON>"], "summary": "Respiration rate sensing plays a critical role in elderly care and patient monitoring. The latest research has explored the possibility of employing Wi-Fi signals for respiration sensing without attaching a device to the target. A critical issue with these solutions includes that good monitoring performance could only be achieved at certain locations within the sensing range, while the performance could be quite poor at other \"dead zones.\" In addition, due to the contactless nature, it is challenging to monitor multiple targets simultaneously as the reflected signals are often mixed together. In this work, we present our system, named LungTrack, hosted on commodity RFID devices for respiration monitoring. Our system retrieves subtle signal fluctuations at the receiver caused by chest displacement during respiration without need for attaching any devices to the target. It addresses the dead-zone issue and enables simultaneous monitoring of two human targets by employing one RFID reader and carefully positioned multiple RFID tags, using an optimization technique. Comprehensive experiments demonstrate that LungTrack can achieve a respiration monitoring accuracy of greater than 98% for a single target at all sensing locations (within 1st -- 5th Fresnel zones) using just one RFID reader and five tags, when the target's orientation is known a priori. For the challenging scenario involve two human targets, LungTrack is able to achieve greater than 93% accuracy when the targets are separated by at least 10 cm.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351237"}, {"primary_key": "2860380", "vector": [], "sparse_vector": [], "title": "Deep Multi-Task Learning Based Urban Air Quality Index Modelling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Obtaining comprehensive air quality information can help protect human health from air pollution. Existing spatially fine-grained estimation methods and forecasting methods have the following problems: 1) Only a part of data related to air quality is considered. 2) Features are defined and extracted artificially. 3) Due to the lack of training samples, they usually cannot achieve good generalization performance. Therefore, we propose a deep multi-task learning (MTL) based urban air quality index (AQI) modelling method (PANDA). On one hand, a variety of air quality-related urban big data (meteorology, traffic, factory air pollutant emission, point of interest (POI) distribution, road network distribution, etc.) are considered. Deep neural networks are used to learn the representations of these relevant spatial and sequential data, as well as to build the correlation between AQI and these representations. On the other hand, PANDA solves spatially fine-grained AQI level estimation task and AQI forecasting task jointly, which can leverage the commonalities and differences between these two tasks to improve generalization performance. We evaluate PANDA on the dataset of Hangzhou city. The experimental results show that our method can yield a better performance compared to the state-of-the-art methods.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314389"}, {"primary_key": "2860383", "vector": [], "sparse_vector": [], "title": "CAP: Context-aware App Usage Prediction with Heterogeneous Graph Embedding.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Context-aware mobile application (App) usage prediction benefits a variety of applications such as precise bandwidth allocation, App launch acceleration, etc. Prior works have explored this topic through individual data profiles and contextual information. However, it is still a challenging problem because of the following three aspects: i. App usage behavior is usually influenced by multiple factors, especially temporal and spatial factors. ii. It is difficult to describe individuals' preferences, which are usually time-variant. iii. A single user's data is sparse on the spatial domain and only covers a limited number of locations. Prediction becomes more difficult when the user appears at a new location. This paper presents CAP, a context-aware App usage prediction algorithm that takes both contextual information (location &amp; time) and attribution (App with type information) into consideration. We find that the relationships between App-location, App-time, and App-App type are essential to prediction and propose a heterogeneous graph embedding algorithm to map them into the common comparable latent space. In addition, we create a user profile for each user with App usage and trajectory history to describe the individual dynamic preference for personalized prediction. We evaluate the performance of our proposed CAP with two large-scale real-world datasets. Extensive evaluations demonstrate that CAP achieves 30% higher accuracy than a state-of-the-art method Personalized Ranking Metric Embedding (PRME) in terms of Accuracy@5. In terms of mean reciprocal rank (MRR), CAP achieves 1.5× higher than the straightforward baseline Sta and 2× higher than PRME. Our investigation enables a range of applications to benefit from such timely predictions, including network operators, service providers, and etc.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314391"}, {"primary_key": "2860385", "vector": [], "sparse_vector": [], "title": "ICT: In-field Calibration Transfer for Air Quality Sensor Deployments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent years have witnessed a growing interest in urban air pollution monitoring, where hundreds of low-cost air quality sensors are deployed city-wide. To guarantee data accuracy and consistency, these sensors need periodic calibration after deployment. Since access to ground truth references is often limited in large-scale deployments, it is difficult to conduct city-wide post-deployment sensor calibration. In this work we propose In-field Calibration Transfer (ICT), a calibration scheme that transfers the calibration parameters of source sensors (with access to references) to target sensors (without access to references). On observing that (i) the distributions of ground truth in both source and target locations are similar and (ii) the transformation is approximately linear, ICT derives the transformation based on the similarity of distributions with a novel optimization formulation. The performance of ICT is further improved by exploiting spatial prediction of air quality levels and multi-source fusion. Experiments show that ICT is able to calibrate the target sensors as if they had direct access to the references.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314393"}, {"primary_key": "2860387", "vector": [], "sparse_vector": [], "title": "Identifying and Planning for Individualized Change: Patient-Provider Collaboration Using Lightweight Food Diaries in Healthy Eating and Irritable Bowel Syndrome.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Identifying and planning strategies that support a healthy lifestyle or manage a chronic disease often require patient-provider collaboration. For example, people with healthy eating goals often share everyday food, exercise, or sleep data with health coaches or nutritionists to find opportunities for change, and patients with irritable bowel syndrome (IBS) often gather food and symptom data as part of working with providers to diagnose and manage symptoms. However, a lack of effective support often prevents health experts from reviewing large amounts of data in time-constrained visits, prevents focusing on individual goals, and prevents generating correct, individualized, and actionable recommendations. To examine how to design photo-based diaries to help people and health experts exchange knowledge and focus on collaboration goals when reviewing the data together, we designed and developed Foodprint, a photo-based food diary. Foodprint includes three components: (1) A mobile app supporting lightweight data collection, (2) a web app with photo-based visualization and quantitative visualizations supporting collaborative reflection, and (3) a pre-visit note communicating an individual's expectations and questions to experts. We deployed Foodprint in two studies: (1) with 17 people with healthy eating goals and 7 health experts, and (2) with 16 IBS patients and 8 health experts. Building upon the lens of boundary negotiating artifacts and findings from two field studies, our research contributes design principles to (1) prepare individuals to collect data relevant to their health goals and for collaboration, (2) help health experts focus on an individual's eating context, experiences, and goals in collaborative review, and (3) support individuals and experts to develop individualized, actionable plans and strategies.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314394"}, {"primary_key": "2860388", "vector": [], "sparse_vector": [], "title": "Scheduling Content in Pervasive Display Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Digital displays are a ubiquitous feature of public spaces; London recently deployed a whole network of new displays in its Underground stations, and the screens on One Time Square (New York) allow for presentation of over 16,000 square feet of digital media. However, despite decades of research into pervasive displays, the problem of scheduling content is under-served and there is little forward momentum in addressing the challenges brought with large-scale and open display networks. This paper presents the first comprehensive architectural model for scheduling in current and anticipated pervasive display systems. In contrast to prior work, our three-stage model separates out the process of high level goal setting from content filtering and selection. Our architecture is motivated by an extensive review of the literature and a detailed consideration of requirements. The architecture is realised with an implementation designed to serve the world's largest and longest-running research testbed of pervasive displays. A mixed-methods evaluation confirms the viability of the architecture from three angles: demonstrating capability to meet the articulated requirements, performance that comfortably fits within the demands of typical display deployments, and evidence of its ability to serve as the day-to-day scheduling platform for the previously described research testbed. Based on our evaluation and a reflection on paper as a whole, we identify ten implications that will shape future research and development in pervasive display scheduling.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369826"}, {"primary_key": "2860389", "vector": [], "sparse_vector": [], "title": "BoostMeUp: Improving Cognitive Performance in the Moment by Unobtrusively Regulating Emotions with a Smartwatch.", "authors": ["<PERSON>", "<PERSON>", "Malte F. <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A person's emotional state can strongly influence their ability to achieve optimal task performance. Aiming to help individuals manage their feelings, different emotion regulation technologies have been proposed. However, despite the well-known influence that emotions have on task performance, no study to date has shown if an emotion regulation technology can also enhance user's cognitive performance in the moment. In this paper, we present BoostMeUp, a smartwatch intervention designed to improve user's cognitive performance by regulating their emotions unobtrusively. Based on studies that show that people tend to associate external signals that resemble heart rates as their own, the intervention provides personalized haptic feedback simulating a different heart rate. Users can focus on their tasks and the intervention acts upon them in parallel, without requiring any additional action. The intervention was evaluated in an experiment with 72 participants, in which they had to do math tests under high pressure. Participants who were exposed to slow haptic feedback during the tests decreased their anxiety, increased their heart rate variability and performed better in the math tests, while fast haptic feedback led to the opposite effects. These results indicate that the BoostMeUp intervention can lead to positive cognitive, physiological and behavioral changes.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328911"}, {"primary_key": "2860390", "vector": [], "sparse_vector": [], "title": "ASSV: Handwritten Signature Verification Using Acoustic Signals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As one kind of biological characteristics of people, handwritten signature has been widely used in the banking industry, government and education. Verifying handwritten signatures manually causes too much human cost, and its high probability of errors can threaten the property safety and even society stability. Therefore, the need for an automatic verification system is emphasized. This paper proposes a device-free on-line handwritten signature verification system ASSV, providing paper-based handwritten signature verification service. As far as we know, ASSV is the first system which uses the changes of acoustic signals to realize signature verification. ASSV differs from previous on-line signature verification work in two aspects: 1. It requires neither a special sensor-instrumented pen nor a tablet; 2. People do not need to wear a device such as a smartwatch on the dominant hand for hand tracking. Differing from previous acoustic-based sensing systems, ASSV uses a novel chord-based method to estimate phase-related changes caused by tiny actions. Then based on the estimation, frequency-domain features are extracted by a discrete cosine transform (DCT). Moreover, a deep convolutional neural network (CNN) model fed with distance matrices is designed to verify signatures. Extensive experiments show that ASSV is a robust, efficient and secure system achieving an AUC of 98.7% and an EER of 5.5% with a low latency.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351238"}, {"primary_key": "2860391", "vector": [], "sparse_vector": [], "title": "Learning from Hometown and Current City: Cross-city POI Recommendation via Interest Drift and Transfer Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With more and more frequent population movement between different cities, like users' travel or business trip, recommending personalized cross-city Point-of-Interests (POIs) for these users has become an important scenario of POI recommendation tasks. However, traditional models degrade significantly due to sparsity problem because travelers only have limited visiting behaviors. Through a detailed analysis of real-world check-data, we observe 1) the phenomenon of travelers' interest drift and transfer co-exist between hometown and current city; 2) differences between popular POIs among locals and travelers. Motivated by this, we propose a POI Recommendation framework with User Interest Drift and Transfer (PR-UIDT), which jointly considers above two factors when designing user and POI latent vector. In this framework, user vector is divided into a city-independent part and another city-dependent part, and POI is represented as two independent vectors for locals and travelers, respectively. To evaluate the proposed framework, we implement it with a square error based matrix factorization model and a ranking error based matrix factorization model, respectively, and conduct extensive experiments on three real-world datasets. The experiment results demonstrate the superiority of PR-UIDT framework, with a relative improvement of 0.4% ~ 20.5% over several state-of-the-art baselines, as well as the practicality of applying this framework to real-world applications and multi-city scenarios. Further qualitative analysis confirms both the plausibility and validity of combining user interest transfer and drift into cross-city POI recommendation.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369822"}, {"primary_key": "2860393", "vector": [], "sparse_vector": [], "title": "Modeling Biobehavioral Rhythms with Passive Sensing in the Wild: A Case Study to Predict Readmission Risk after Pancreatic Surgery.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Carissa A. Low"], "summary": "Biobehavioral rhythms are associated with numerous health and life outcomes. We study the feasibility of detecting rhythms in data that is passively collected from Fitbit devices and using the obtained model parameters to predict readmission risk after pancreatic surgery. We analyze data from 49 patients who were tracked before surgery, in hospital, and after discharge. Our analysis produces a model of individual patients' rhythms for each stage of treatment that is predictive of readmission. All of the rhythm-based models outperform the traditional approaches to readmission risk stratification that uses administrative data.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314395"}, {"primary_key": "2860396", "vector": [], "sparse_vector": [], "title": "Personalized Context-aware Collaborative Online Activity Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Li Su", "<PERSON><PERSON><PERSON>"], "summary": "With the rapid development of Internet services and mobile devices, nowadays, users can connect to online services anytime and anywhere. Naturally, user's online activity behavior is coupled with time and location contexts and highly influenced by them. Therefore, personalized context-aware online activity modelling and prediction is very meaningful and necessary but also very challenging, due to the complicated relationship between users, activities, spatial and temporal contexts and data sparsity issues. To tackle the challenges, we introduce offline check-in data as auxiliary data and build a user-location-time-activity 4D-tensor and a location-time-POI 3D-tensor, aiming to model the relationship between different entities and transfer semantic features of time and location contexts among them. Accordingly, in this paper we propose a transfer learning based collaborative tensor factorization method to achieve personalized context-aware online activity prediction. Based on real-world datasets, we compare the performance of our method with several state-of-the-arts and demonstrate that our method can provide more effective prediction results in the high sparsity scenario. With only 30% of observed time and location contexts, our solution can achieve 40% improvement in predicting user's Top5 activity behavior in new time and location scenarios. Our study is the first step forward for transferring knowledge learned from offline check-in behavior to online activity prediction to provide better personalized context-aware recommendation services for mobile users.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369829"}, {"primary_key": "2860397", "vector": [], "sparse_vector": [], "title": "MAC: Measuring the Impacts of Anomalies on Travel Time of Multiple Transportation Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Boyang Fu", "Zixing Song", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Urban anomalies have a large impact on passengers' travel behavior and city infrastructures, which can cause uncertainty on travel time estimation. Understanding the impact of urban anomalies on travel time is of great value for various applications such as urban planning, human mobility studies and navigation systems. Most existing studies on travel time have been focused on the total riding time between two locations on an individual transportation modality. However, passengers often take different modes of transportation, e.g., taxis, subways, buses or private vehicles, and a significant portion of the travel time is spent in the uncertain waiting. In this paper, we study the fine-grained travel time patterns in multiple transportation systems under the impact of urban anomalies. Specifically, (i) we investigate implicit components, including waiting and riding time, in multiple transportation systems; (ii) we measure the impact of real-world anomalies on travel time components; (iii) we design a learning-based model for travel time component prediction with anomalies. Different from existing studies, we implement and evaluate our measurement framework on multiple data sources including four city-scale transportation systems, which are (i) a 14-thousand taxicab network, (ii) a 13-thousand bus network, (iii) a 10-thousand private vehicle network, and (iv) an automatic fare collection system for a public transit network (i.e., subway and bus) with 5 million smart cards.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328913"}, {"primary_key": "2860401", "vector": [], "sparse_vector": [], "title": "Privacy-preserving Cross-domain Location Recommendation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cross-domain recommendation is a typical solution for data sparsity and cold start issue in the field of location recommendation. Specifically, data of an auxiliary domain is leveraged to improve the recommendation of the target domain. There is a typical scenario that two interaction domains (location based check-in service, for example) combine data to perform the cross-domain location recommendation task. Existing approaches are based on the assumption that the interaction data from the auxiliary domain can be directly shared across domains. However, such an assumption is not reasonable, since in the real world those domains may be operated by different companies. Therefore, directly sharing raw data may violate business privacy policy and increase the risk of privacy leakage since the user-location interaction records are very sensitive. In this paper, we propose a framework named privacy-preserving cross-domain location recommendation which works in two stages. First, for the interaction data from the auxiliary domain, we adopt a differential privacy based protection mechanism to hide the real locations of each user to meet the criterion of differential privacy. Then we share the protected user-location interaction to the target domain. Second, we develop a new method of Confidence-aware Collective Matrix Factorization (CCMF) to effectively exploit the transferred interaction data. To verify its efficacy, we collect two real-world datasets suitable for the task. Extensive experiments demonstrate that our proposed framework achieves the best performance compared with the state-of-the-art baseline methods. We further demonstrate that our method can alleviate the data sparsity issue significantly while protecting users' location privacy.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314398"}, {"primary_key": "2860403", "vector": [], "sparse_vector": [], "title": "Understanding Motivators, Constraints, and Practices of Sharing Internet of Things.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Smart devices such as mobile phones, tablets, and smart watches are designed under the assumption that they will be used by a single user. In contrast, many other devices, such as smart thermostats and smart speakers, are inherently sharable. This paper presents results from a diary study that we conducted with 20 participants to gain a nuanced understanding of the purposes, motivators, and constraints involved in the sharing of smart devices, which are cumulatively referred to as the Internet of Things. We also report on users' practices of coordinating their shared use with sharees/co-users, the impact of not understanding a smart device's behavior and the context of shared use, the differences between sharing personal and inherently sharable devices in terms of content that is available and accessible, trust between sharees, and measures taken to ensure accountable use. Finally, we discuss the implications of our findings and provide guidelines for the design of future smart devices.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328915"}, {"primary_key": "2860406", "vector": [], "sparse_vector": [], "title": "Knocker: Vibroacoustic-based Object Recognition with Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While smartphones have enriched our lives with diverse applications and functionalities, the user experience still often involves manual cumbersome inputs. To purchase a bottle of water for instance, a user must locate an e-commerce app, type the keyword for a search, select the right item from the list, and finally place an order. This process could be greatly simplified if the smartphone identifies the object of interest and automatically executes the user preferred actions for the object. We present <PERSON><PERSON><PERSON> that identifies the object when a user simply knocks on an object with a smartphone. The basic principle of <PERSON><PERSON><PERSON> is leveraging a unique set of responses generated from the knock. <PERSON><PERSON><PERSON> takes a multimodal sensing approach that utilizes microphones, accelerometers, and gyroscopes to capture the knock responses, and exploits machine learning to accurately identify objects. We also present 15 applications enabled by <PERSON><PERSON><PERSON> that showcase the novel interaction method between users and objects. <PERSON><PERSON><PERSON> uses only the built-in smartphone sensors and thus is fully deployable without specialized hardware or tags on either the objects or the smartphone. Our experiments with 23 objects show that <PERSON><PERSON><PERSON> achieves an accuracy of 98% in a controlled lab and 83% in the wild.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351240"}, {"primary_key": "2860407", "vector": [], "sparse_vector": [], "title": "On-body Sensing of Cocaine Craving, Euphoria and Drug-Seeking Behavior Using Cardiac and Respiratory Signals.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Drug addiction is a chronic brain-based disorder that affects a person's behavior and leads to an inability to control drug usage. Ubiquitous physiological sensing technologies to detect illicit drug use have been well studied and understood for different types of drugs. However, we currently lack the ability to continuously and passively measure the user state in ways that might shed light on the complex relationships between cocaine-induced subjective states (e.g., craving and euphoria) and compulsive drug-seeking behavior. More specifically, the applicability of wearable sensors to detect drug-related states is underexplored. In the current work, we take an initial step in the modeling of cocaine craving, euphoria and drug-seeking behavior using electrocardiographic (ECG) and respiratory signals unobtrusively collected from a wearable chest band. Ten experienced cocaine users were studied using a human laboratory paradigm of self-regulated (i.e., \"binge\") cocaine administration, during which self-reported visual analog scale (VAS) ratings of cocaine-induced subjective effects (i.e., craving and euphoria) and behavioral measures of drug-seeking behavior (i.e., button clicks for drug infusions) are collected. Our results are encouraging and show that self-reported VAS Craving scores are predicted with a normalized root-mean-squared error (NRMSE) of 17.6% and a Pearson correlation coefficient of 0.49. Similarly, for VAS Euphoria prediction, an NRMSE of 16.7% and a Pearson correlation coefficient of 0.73 were achieved. We further analyze the relative importance of different morphology-related ECG and respiratory features for craving and euphoria prediction. Demographic factor analysis reveals how one single factor (i.e., average dollar ($) per cocaine use) can help to further boost the performance of our craving and euphoria models. Lastly, we model drug-seeking behavior using cardiac and respiratory signals. Specifically, we demonstrate that the latter signals can predict participant button clicks with an F1 score of 0.80 and estimate different levels of click density with a correlation coefficient of 0.85 and an NRMSE of 17.9%.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328917"}, {"primary_key": "2860408", "vector": [], "sparse_vector": [], "title": "Blocks: Collaborative and Persistent Augmented Reality Experiences.", "authors": ["<PERSON><PERSON>", "<PERSON>ter Canberk", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce Blocks, a mobile application that enables people to co-create AR structures that persist in the physical environment. Using Blocks, end users can collaborate synchronously or asynchronously, whether they are colocated or remote. Additionally, the AR structures can be tied to a physical location or can be accessed from anywhere. We evaluated how people used Blocks through a series of lab and field deployment studies with over 160 participants, and explored the interplay between two collaborative dimensions: space and time. We found that participants preferred creating structures synchronously with colocated collaborators. Additionally, they were most active when they created structures that were not restricted by time or place. Unlike most of today's AR experiences, which focus on content consumption, this work outlines new design opportunities for persistent and collaborative AR experiences that empower anyone to collaborate and create AR content.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351241"}, {"primary_key": "2860410", "vector": [], "sparse_vector": [], "title": "VocaBura: A Method for Supporting Second Language Vocabulary Learning While Walking.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "S<PERSON>o Fu<PERSON>"], "summary": "Learning a new language is difficult and time-consuming. Apart from dedicated classroom study, second language (L2) learners often lack opportunities to switch their attention to vocabulary learning over other daily routines. In this work, we propose a method that enables L2 learners to study new vocabulary items during their dead time, such as when commuting to school or work. We developed a smartphone application, VocaBura, which combines audio learning with location-relevant L1-L2 word pairs to allow users to discover new vocabulary items while walking past buildings, shops and other locations. Our evaluation results indicated that Japanese beginner level English learners were able to retain more vocabulary items with the proposed method compared to traditional audio-based study despite being less aware of L2 vocabulary acquisition having occurred. In our second study, we report on the level of English vocabulary coverage for L2 learning achievable with our proposed method. We discuss several design implications for educational technologies supporting second language learning.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369824"}, {"primary_key": "2860412", "vector": [], "sparse_vector": [], "title": "Au-Id: Automatic User Identification and Authentication through the Motions Captured from Sequential Human Activities Using RFID.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The advancements of ambient intelligence and ubiquitous computing are driving the unprecedented development of smart spaces where enhanced services are provided based on activity recognition. Meanwhile, user identification, which can enable the personalization of the enhanced services for specific users and the access control of confidential information, becomes increasingly important. Traditional approaches to user identification require either attached wearable sensors or active user participation. This paper presents Au-Id, a non-intrusive automatic user identification and authentication system through human motions captured from their daily activities based on RFID. The key insight is that the RFID tag array can capture human's physical and behavioral characteristics for user identification. Particularly, phase and RSSI data streams of the RFID tag array are fused to incorporate the information from time, space and modality dimensions. Based on this, a novel sequence labeling based segmentation method is proposed for target motion extraction. Then Au-Id leverages a multi-modal Convolutional Neural Network (CNN) for user identification and significantly reduces the training efforts by transfer learning. In addition, Au-Id facilitates user authentication by integrating the feature representations extracted by CNN with one-class SVM classifiers. The evaluation shows that Au-Id can achieve accurate and robust user identification and authentication.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328919"}, {"primary_key": "2860413", "vector": [], "sparse_vector": [], "title": "Integrating Activity Recognition and Nursing Care Records: The System, Deployment, and a Verification Study.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we introduce a system of integrating activity recognition and collecting nursing care records at nursing care facilities as well as activity labels and sensors through smartphones, and describe experiments at a nursing care facility for 4 months. A system designed to be used even by staff not familiar with smartphones could collected enough number of data without losing but improving their workload for recording. For collected data, we revealed the nature of the collected data as for activities, care details, and timestamps, and considering them, we show a reference accuracy of recognition of nursing activity which is durable to time skewness, overlaps, and class imbalances. Moreover, we demonstrate the near future prediction to predict the next day's activities from the previous day's records which could be useful for proactive care management. The dataset collected is to be opened to the research community, and can be the utilized for activity recognition and data mining in care facilities.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351244"}, {"primary_key": "2860414", "vector": [], "sparse_vector": [], "title": "Exploring the Touch and Motion Features in Game-Based Cognitive Assessments.", "authors": ["Jittrapol Intarasirisawat", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Early detection of cognitive decline is important for timely intervention and treatment strategies to prevent further deterioration or development of more severe cognitive impairment, as well as identify at risk individuals for research. In this paper, we explore the feasibility of using data collected from built-in sensors of mobile phone and gameplay performance in mobile-game-based cognitive assessments. Twenty-two healthy participants took part in the two-session experiment where they were asked to take a series of standard cognitive assessments followed by playing three popular mobile games in which user-game interaction data were passively collected. The results from bivariate analysis reveal correlations between our proposed features and scores obtained from paper-based cognitive assessments. Our results show that touch gestural interaction and device motion patterns can be used as supplementary features on mobile game-based cognitive measurement. This study provides initial evidence that game related metrics on existing off-the-shelf games have potential to be used as proxies for conventional cognitive measures, specifically for visuospatial function, visual search capability, mental flexibility, memory and attention.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351245"}, {"primary_key": "2860415", "vector": [], "sparse_vector": [], "title": "Beyond Control: Enabling Smart Thermostats for Leakage Detection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart thermostats, with multiple sensory abilities, are becoming pervasive and ubiquitous, in both residential and commercial buildings. By analyzing occupants' behavior, adjusting set temperature automatically, and adapting to temporal and spatial changes in the atmosphere, smart thermostats can maximize both - energy savings and user comfort. In this paper, we study smart thermostats for refrigerant leakage detection. Retail outlets, such as milk-booths and quick service restaurants set up cold-rooms to store perishable items. In each room, a refrigeration unit (akin to air-conditioners) is used to maintain a suitable temperature for the stored products. Often, refrigerant leaks through the coils (or valves) of the refrigeration unit which slowly diminishes the cooling capacity of the refrigeration unit while allowing it to be functional. Such leaks waste significant energy, risk occupants' health, and impact the quality of stored perishable products. While store managers usually fail to sense the early symptoms of such leaks, current techniques to report refrigerant leakage are often not scalable. We propose Greina - to continuously monitor the readily available ambient information from the thermostat and timely report such leaks. We evaluate our approach on 74 outlets of a retail enterprise and results indicate that Greina can report the leakage a week in advance when compared to manual reporting.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314401"}, {"primary_key": "2860417", "vector": [], "sparse_vector": [], "title": "Probing Sucrose Contents in Everyday Drinks Using Miniaturized Near-Infrared Spectroscopy Scanners.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Near-Infrared Spectroscopy (NIRS) is a non-invasive sensing technique which can be used to acquire information on an object's chemical composition. Although NIRS is conventionally used in dedicated laboratories, the recent introduction of miniaturized NIRS scanners has greatly expanded the use cases of this technology. Previous work from the UbiComp community shows that miniaturized NIRS can be successfully adapted to identify medical pills and alcohol concentration. In this paper, we further extend this technology to identify sugar (sucrose) contents in everyday drinks. We developed a standalone mobile device which includes inter alia a NIRS scanner and a 3D printed clamp. The clamp can be attached to a straw-like tube to sense a liquid's sucrose content. Through a series of studies, we show that our technique can accurately measure sucrose levels in both lab-made samples and commercially available drinks, as well as classify commercial drinks. Furthermore, we show that our method is robust to variations in the ambient temperature and lighting conditions. Overall, our system can estimate the concentration of sugar with ±0.29 g/100ml error in lab-made samples and &lt; 2.0 g/100ml error in 18 commercial drinks, and can identify everyday drinks with &gt; 99% accuracy. Furthermore, in our analysis, we are able to discern three characteristic wavelengths in the near-infrared region (1055 nm, 1235 nm and 1545 nm) with acute responses to sugar (sucrose). Our proposed protocol contributes to the development of everyday \"food scanners\" consumers.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369834"}, {"primary_key": "2860418", "vector": [], "sparse_vector": [], "title": "Glaze: Overlaying Occupied Spectrum with Downlink IoT Transmissions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Existing Internet of Things (IoT) solutions require expensive infrastructure for sending and receiving data. Emerging technologies such as ambient backscatter help fill this gap by enabling uplink communication for IoT devices. However, there is still no efficient solution to enable low-cost and low-power downlink communication for ambient backscatter systems. In this paper we present Glaze, a system that overlays data on existing wireless signals to create a new channel of downlink communication for IoT backscatter devices. In particular, Glaze uses a new technique that introduces small perturbations to existing signals to convey data. We evaluate the performance of Glaze and show how it can be used across wireless standards such as FM, TV, or Wi-Fi to communicate with devices with minimal impact on existing data transmissions.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369825"}, {"primary_key": "2860419", "vector": [], "sparse_vector": [], "title": "NOSE: A Novel Odor Sensing Engine for Ambient Monitoring of the Frying Cooking Method in Kitchen Environments.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "How we cook and prepare our food has an enormous impact on our health and well-being. Specific cooking methods, like deep-frying, are linked to obesity and the degradation of food nutrients, which contribute to various diseases and health issues. We present NOSE, a Novel Odor Sensing Engine, that passively and continuously monitors gas emissions in the kitchen area using an array of six metal oxide semiconductor (MOS) gas sensors and detects the occurrence of deep-frying. To evaluate NOSE, we collected sensor data from five foods (chicken, fish, beef, potato, and onion) cooked with three methods (deep-frying, grilling, and boiling) and three common frying oils (canola, corn, and soybean) in three different kitchens in a controlled manner. We demonstrate that NOSE can classify cooking by deep-frying with an average F1-score of 0.89. Based on the in-laboratory findings, we deployed NOSE in two different real-world households throughout a three-week period and successfully detected the occurrence of frying cooking with an average F1-score of 0.72, which is a promising result considering the relatively small number of data samples collected. Our results show the potential of using NOSE as an assistive dietary monitoring tool that periodically reports to users about their cooking habits.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328920"}, {"primary_key": "2860422", "vector": [], "sparse_vector": [], "title": "Phyjama: Physiological Sensing via Fiber-enhanced Pyjamas.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Unobtrusive and continuous monitoring of cardiac and respiratory rhythm, especially during sleeping, can have significant clinical utility. An exciting new possibility for such monitoring is the design of textiles that use all-textile sensors that can be woven or stitched directly into a textile or garment. Our work explores how we can make such monitoring possible by leveraging something that is already familiar, such as pyjama made of cotton/silk fabric, and imperceptibly adapt it to enable sensing of physiological signals to yield natural fitting, comfortable, and less obtrusive smart clothing. We face several challenges in enabling this vision including requiring new sensor design to measure physiological signals via everyday textiles and new methods to deal with the inherent looseness of normal garments, particularly sleepwear like pyjamas. We design two types of textile-based sensors that obtain a ballistic signal due to cardiac and respiratory rhythm ---the first a novel resistive sensor that leverages pressure between the body and various surfaces and the second is a triboelectric sensor that leverages changes in separation between layers to measure ballistics induced by the heart. We then integrate several instances of such sensors on a pyjama and design a signal processing pipeline that fuses information from the different sensors such that we can robustly measure physiological signals across a range of sleep and stationary postures. We show that the sensor and signal processing pipeline has high accuracy by benchmarking performance both under restricted settings with twenty one users as well as more naturalistic settings with seven users.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351247"}, {"primary_key": "2860423", "vector": [], "sparse_vector": [], "title": "Face Recognition Assistant for People with Visual Impairments.", "authors": ["<PERSON>", "<PERSON>", "Khai N. <PERSON>"], "summary": "Although there are many face recognition systems to help individuals with visual impairments (VIPs) recognize other people, almost all require a database with the pictures and names of the people who should be tracked. These solutions would not be able to help VIPs recognize people they might not know well. In this work, we investigate the requirements and challenges that must be addressed in the design of a face recognition system for helping VIPs recognize people with whom they have weak-ties. We first conducted a formative study with eight visually impaired people. Using insights learned from the formative study, we developed a research prototype that runs on a mobile phone worn around the user's neck. The developed prototype is a wearable face recognition system that opportunistically captures and stores undistorted face images and contextual information about the user's interaction with each person to a database, without the user intervention, as she interacts with new people. We then used this prototype application as a technology probe---asking VIP participants to use the device in a realistic scenario in which they meet and re-encounter several new people. We analyze and report feedback collected from VIPs about the design and use of such a service.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351248"}, {"primary_key": "2860424", "vector": [], "sparse_vector": [], "title": "VPS Tactile Display: Tactile Information Transfer of Vibration, Pressure, and Shear.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "One of the challenges in the field of haptics is to provide meaningful and realistic sensations to users. While most real world tactile sensations are composed of multiple dimensions, most commercial product only include vibration as it is the most cost effective solution. To improve on this, we introduce VPS (Vibration, Pressure, Shear) display, a multi-dimensional tactile array that increases information transfer by combining Vibration, Pressure, and Shear similar to how RGB LED combines red, blue, and green to create new colors. We characterize the device performance and dynamics for each tactile dimension in terms of its force and displacement profiles, and evaluate information transfer of the VPS display through a stimulus identification task. Our results indicate that the information transfer through a single taxel increases from 0.56 bits to 2.15 bits when pressure and shear are added to vibrations with a slight decrease in identification accuracy. We also explored the pleasantness and continuity of VPS and the study results reveal that tactile strokes in shear mode alone are rated highest on perceived pleasantness and continuity.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328922"}, {"primary_key": "2860425", "vector": [], "sparse_vector": [], "title": "HeyTeddy: Conversational Test-Driven Development for Physical Computing.", "authors": ["<PERSON><PERSON><PERSON>", "Youngkyung Choi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Physical computing is a complex activity that consists of different but tightly coupled tasks: programming and assembling hardware for circuits. Prior work clearly shows that this coupling is the main source of mistakes that unfruitfully take a large portion of novices' debugging time. While past work presented systems that simplify prototyping or introduce novel debugging functionalities, these tools either limit what users can accomplish or are too complex for beginners. In this paper, we propose a general-purpose prototyping tool based on conversation. HeyTeddy guides users during hardware assembly by providing additional information on requests or by interactively presenting the assembly steps to build a circuit. Furthermore, the user can program and execute code in real-time on their Arduino platform without having to write any code, but instead by using commands triggered by voice or text via chat. Finally, the system also presents a set of test capabilities for enhancing debugging with custom and proactive unit tests. We codesigned the system with 10 users over 6 months and tested it with realistic physical computing tasks. With the result of two user studies, we show that conversational programming is feasible and that voice is a suitable alternative for programming simple logic and encouraging exploration. We also demonstrate that conversational programming with unit tests is effective in reducing development time and overall debugging problems while increasing users' confidence. Finally, we highlight limitations and future avenues of research.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369838"}, {"primary_key": "2860427", "vector": [], "sparse_vector": [], "title": "Towards the Design of a Ring Sensor-based mHealth System to Achieve Optimal Motor Function in Stroke Survivors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Joonwoo Park", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Maximizing the motor practice in stroke survivors' living environments may significantly improve the functional recovery of their stroke-affected upper-limb. A wearable system that can continuously monitor upper-limb performance has been considered as an effective clinical solution for its potential to provide patient-centered, data-driven feedback to improve the motor dosage. Towards that end, we investigate a system leveraging a pair of finger-worn, ring-type accelerometers capable of monitoring both gross-arm and fine-hand movements that are clinically relevant to the performance of daily activities. In this work, we conduct a mixed-methods study to (1) quantitatively evaluate the efficacy of finger-worn accelerometers in measuring clinically relevant information regarding stroke survivors' upper-limb performance, and (2) qualitatively investigate design requirements for the self-monitoring system, based on data collected from 25 stroke survivors and seven occupational therapists. Our quantitative findings demonstrate strong face and convergent validity of the finger-worn accelerometers, and its responsiveness to changes in motor behavior. Our qualitative findings provide a detailed account of the current rehabilitation process while highlighting several challenges that therapists and stroke survivors face. This study offers promising directions for the design of a self-monitoring system that can encourage the affected limb use during stroke survivors' daily living.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369817"}, {"primary_key": "2860428", "vector": [], "sparse_vector": [], "title": "micro-Stress EMA: A Passive Sensing Framework for Detecting in-the-wild Stress in Pregnant Mothers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Nabil <PERSON>"], "summary": "High levels of stress during pregnancy increase the chances of having a premature or low-birthweight baby. Perceived self-reported stress does not often capture or align with the physiological and behavioral response. But what if there was a self-report measure that could better capture the physiological response? Current perceived stress self-report assessments require users to answer multi-item scales at different time points of the day. Reducing it to one question, using microinteraction-based ecological momentary assessment (micro-EMA, collecting a single in situ self-report to assess behaviors) allows us to identify smaller or more subtle changes in physiology. It also allows for more frequent responses to capture perceived stress while at the same time reducing burden on the participant. We propose a framework for selecting the optimal micro-EMA that combines unbiased feature selection and unsupervised Agglomerative clustering. We test our framework in 18 women performing 16 activities in-lab wearing a Biostamp, a NeuLog, and a Polar chest strap. We validated our results in 17 pregnant women in real-world settings. Our framework shows that the question \"How worried were you?\" results in the highest accuracy when using a physiological model. Our results provide further in-depth exposure to the challenges of evaluating stress models in real-world situations.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351249"}, {"primary_key": "2860429", "vector": [], "sparse_vector": [], "title": "Exploring the State-of-Receptivity for mHealth Interventions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent advancements in sensing techniques for mHealth applications have led to successful development and deployments of several mHealth intervention designs, including Just-In-Time Adaptive Interventions (JITAI). JITAIs show great potential because they aim to provide the right type and amount of support, at the right time. Timing the delivery of a JITAI such as the user is receptive and available to engage with the intervention is crucial for a JITAI to succeed. Although previous research has extensively explored the role of context in users' responsiveness towards generic phone notifications, it has not been thoroughly explored for actual mHealth interventions. In this work, we explore the factors affecting users' receptivity towards JITAIs. To this end, we conducted a study with 189 participants, over a period of 6 weeks, where participants received interventions to improve their physical activity levels. The interventions were delivered by a chatbot-based digital coach - Ally - which was available on Android and iOS platforms. We define several metrics to gauge receptivity towards the interventions, and found that (1) several participant-specific characteristics (age, personality, and device type) show significant associations with the overall participant receptivity over the course of the study, and that (2) several contextual factors (day/time, phone battery, phone interaction, physical activity, and location), show significant associations with the participant receptivity, in-the-moment. Further, we explore the relationship between the effectiveness of the intervention and receptivity towards those interventions; based on our analyses, we speculate that being receptive to interventions helped participants achieve physical activity goals, which in turn motivated participants to be more receptive to future interventions. Finally, we build machine-learning models to detect receptivity, with up to a 77% increase in F1 score over a biased random classifier.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369805"}, {"primary_key": "2860430", "vector": [], "sparse_vector": [], "title": "How Much is Too Much?: Understanding the Information Needs of Parents of Young Children.", "authors": ["<PERSON>", "<PERSON>"], "summary": "While technologies exist that are designed for parents to monitor their toddlers and school-age children, the actual structure and underlying mechanisms of parents information needs have received only limited attention. A systematic understanding of these core components is crucial for designing appropriate solutions and reducing barriers to using this type of technology. Based on accumulated findings from a three-phase study, this paper (1) describes the structure of information that parents seek about their children, (2) explores parents' underlying motivations for seeking this information, (3) presents a classification of major uses of this information, and, (4) through a set of design recommendations, discusses the role that ubiquitous technology can play in addressing information needs and current obstacles to technologically mediated solutions.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328923"}, {"primary_key": "2860431", "vector": [], "sparse_vector": [], "title": "GeoLifecycle: User Engagement of Geographical Exploration and Churn Prediction in LBSNs.", "authors": ["<PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Pan Hui"], "summary": "As Location-Based Social Networks (LBSNs) have become widely used by users, understanding user engagement and predicting user churn are essential to the maintainability of the services. In this work, we conduct a quantitative analysis to understand user engagement patterns exhibited both offline and online in LBSNs. We employ two large-scale datasets which consist of 1.3 million and 62 million users with 5.3 million reviews and 19 million tips in Yelp and Foursquare, respectively. We discover that users keep traveling to diverse locations where they have not reviewed before, which is in contrast to \"human life\" analogy in real life, an initial exploration followed by exploitation of existing preferences. Interestingly, we find users who eventually leave the community show distinct engagement patterns even with their first ten reviews in various facets, e.g., geographical, venue-specific, linguistic, and social aspects. Based on these observations, we construct predictive models to detect potential churners. We then demonstrate the effectiveness of our proposed features in the churn prediction. Our findings of geographical exploration and online interactions of users enhance our understanding of human mobility based on reviews, and provide important implications for venue recommendations and churn prediction.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351250"}, {"primary_key": "2860432", "vector": [], "sparse_vector": [], "title": "Toccata: Supporting Classroom Orchestration with Activity Based Computing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present Toccata, a system that facilitates the management of rich multi-device pedagogical activities. Through interviews with high school teachers, we identified a set of barriers to conducting digital activities in schools: set-up time, network problems, difficulties in following and changing plans as activities unfold. We designed and developed Toccata to support the planning of pedagogical activities (scripting), seamless sharing of content and collaboration across people and devices, live management of activities in the classroom, roaming for situations outside classrooms, resumption across sessions, and resilience to unstable network conditions. We deployed Toccata in three classes, over seven teaching sessions, involving a total of 69 students. Together, these deployments show that Toccata is a generic solution for managing multi-device activities in schools. We reflect on how Activity Based Computing principles support Orchestration in Toccata, and discuss the design opportunities it creates such as better awareness of learners' activity, micro-orchestration techniques for enabling teachers to better control devices in classrooms, or supporting reflective practices of teachers.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328924"}, {"primary_key": "2860433", "vector": [], "sparse_vector": [], "title": "The PARK Framework for Automated Analysis of Parkinson&apos;s Disease Characteristics.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> (<PERSON><PERSON><PERSON>) <PERSON><PERSON>"], "summary": "There are about 900,000 people with Parkinson's disease (PD) in the United States. Even though there are benefits of early treatment, unfortunately, over 40% of individuals with PD over 65 years old do not see a neurologist. It is often very difficult for these individuals to get to a physician's office for diagnosis and subsequent monitoring. To address this problem, we present PARK, Parkinson's Analysis with Remote Kinetic-tasks. PARK instructs and guides users through six motor tasks and one audio task selected from the standardized MDS-UPDRS rating scale and records their performance via webcam. An initial experiment was conducted with 127 participants with PD and 127 age-matched controls, in which a total of 1,778 video recordings were collected. 90.6% of the PD participants agreed that PARK was easy to use, and 93.7% mentioned that they would use the system in the future. We explored objective differences between those with and without PD. A novel motion feature based on the Fast Fourier Transform (FFT) of optical flow in a region of interest was designed to quantify these differences in the collected video recordings. Additionally, we found that facial action unit AU4 (brow lowerer) was expressed significantly more often, while AU12 (lip corner puller) was expressed less often in various tasks for participants with PD.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328925"}, {"primary_key": "2860434", "vector": [], "sparse_vector": [], "title": "Exploring the Efficacy of Sparse, General-Purpose Sensor Constellations for Wide-Area Activity Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Future smart homes, offices, stores and many other environments will increasingly be monitored by distributed sensors, supporting rich, context-sensitive applications. There are two opposing instrumentation approaches. On one end is full sensor saturation, where every object of interest is tagged with a sensor. On the other end, we can imagine a hypothetical, omniscient sensor capable of detecting events throughout an entire building from one location. Neither approach is currently practical, and thus we explore the middle ground between these two extremes: a sparse constellation of sensors working together to provide the benefits of full saturation, but without the social, aesthetic, maintenance and financial drawbacks. More specifically, we target a density of one sensor per room (and less), which means the average home could achieve full coverage with perhaps ten sensors. We quantify and characterize the performance of sparse sensor constellations through deployments across three environments and 67 unique activities. Our results illuminate accuracy implications across key spatial configurations important for enabling more practical, wide-area activity sensing.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328926"}, {"primary_key": "2860435", "vector": [], "sparse_vector": [], "title": "Intermittent Learning: On-Device Machine Learning on Intermittently Powered System.", "authors": ["<PERSON><PERSON><PERSON>", "Bashima Islam", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces intermittent learning --- the goal of which is to enable energy harvested computing platforms capable of executing certain classes of machine learning tasks effectively and efficiently. We identify unique challenges to intermittent learning relating to the data and application semantics of machine learning tasks, and to address these challenges, we devise 1) an algorithm that determines a sequence of actions to achieve the desired learning objective under tight energy constraints, and 2) propose three heuristics that help an intermittent learner decide whether to learn or discard training examples at run-time which increases the energy efficiency of the system. We implement and evaluate three intermittent learning applications that learn the 1) air quality, 2) human presence, and 3) vibration using solar, RF, and kinetic energy harvesters, respectively. We demonstrate that the proposed framework improves the energy efficiency of a learner by up to 100% and cuts down the number of learning examples by up to 50% when compared to state-of-the-art intermittent computing systems that do not implement the proposed intermittent learning framework.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369837"}, {"primary_key": "2860436", "vector": [], "sparse_vector": [], "title": "VoltKey: Continuous Secret Key Generation Based on Power Line Noise for Zero-Involvement Pairing and Authentication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The explosive proliferation of Internet-of-Things (IoT) ecosystem fuels the needs for a mechanism for the user to easily and securely interconnect multiple heterogeneous devices with minimal involvement. However, the current paradigm of context-unaware pairing and authentication methods (e.g., using a preset or user-defined password) poses severe challenges in the usability and security aspects due to the limited and siloed user interface that requires substantial effort on establishing or maintaining a secure network. In this paper, we present VoltKey, a method that transparently and continuously generates secret keys for colocated devices, leveraging spatiotemporally unique noise contexts observed in commercial power line infrastructure. We introduce a novel scheme to extract randomness from power line noise and securely convert it into the same key by a pair of devices. The unique noise pattern observed only by trusted devices connected to a local power line prevents malicious devices without physical access from obtaining unauthorized access to the network. VoltKey can be implemented on top of standard USB power supplies as a platform-agnostic bolt-on addition to any IoT devices or wireless access points that are constantly connected to the power outlet. Through extensive experiments under various realistic deployment environments, we demonstrate that VoltKey can successfully establish a secret key among colocated devices with over 90% success rate, while effectively rejecting malicious devices that do not have access to the local power line (but may have access to a spatially nearby line).", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351251"}, {"primary_key": "2860439", "vector": [], "sparse_vector": [], "title": "Data-Driven Battery-Lifetime-Aware Scheduling for Electric Bus Fleets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Electric vehicles (EVs) have experienced a sensational growth in the past few years, due to the potential of mitigating global warming and energy scarcity problems. However, the high manufacturing cost of battery packs and limited battery lifetime hinder EVs from further development. Especially, electric bus, as one of the most important means of public transportation, suffers from long daily operation time and peak-hour passenger overload, which aggravate its battery degradation. To address this issue, we propose a novel data-driven battery-lifetime-aware electric bus scheduling system. Leveraging practical bus GPS and transaction datasets, we conduct a detailed analysis of passenger behaviors and design a reliable prediction model for passenger arrival rate at each station. By taking passenger waiting queue at each bus station analogous to data buffer in network systems, we apply Lyapunov optimization and obtain an electric bus scheduling strategy with reliable performance guarantee on both battery degradation rate and passengers' service quality. To verify the effectiveness of the system, we evaluate our design on a 12-month electric bus operation datasets from the city of Shenzhen. The experimental results show that, compared with two baseline methods, our system reduces the battery degradation rate by 14.3% and 21.7% under the same passenger arrival rate, while preserving good passenger service quality.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369810"}, {"primary_key": "2860440", "vector": [], "sparse_vector": [], "title": "Audio-Based Activities of Daily Living (ADL) Recognition with Large-Scale Acoustic Embeddings from Online Videos.", "authors": ["<PERSON><PERSON>", "<PERSON>az"], "summary": "Over the years, activity sensing and recognition has been shown to play a key enabling role in a wide range of applications, from sustainability and human-computer interaction to health care. While many recognition tasks have traditionally employed inertial sensors, acoustic-based methods offer the benefit of capturing rich contextual information, which can be useful when discriminating complex activities. Given the emergence of deep learning techniques and leveraging new, large-scaled multi-media datasets, this paper revisits the opportunity of training audio-based classifiers without the onerous and time-consuming task of annotating audio data. We propose a framework for audio-based activity recognition that makes use of millions of embedding features from public online video sound clips. Based on the combination of oversampling and deep learning approaches, our framework does not require further feature processing or outliers filtering as in prior work. We evaluated our approach in the context of Activities of Daily Living (ADL) by recognizing 15 everyday activities with 14 participants in their own homes, achieving 64.2% and 83.6% averaged within-subject accuracy in terms of top-1 and top-3 classification respectively. Individual class performance was also examined in the paper to further study the co-occurrence characteristics of the activities and the robustness of the framework.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314404"}, {"primary_key": "2860441", "vector": [], "sparse_vector": [], "title": "WearBreathing: Real World Respiratory Rate Monitoring Using Smartwatches.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Respiratory rate is a vital physiological signal that may be useful for a multitude of clinical applications, especially if measured in the wild rather than controlled settings. In-the-wild respiratory rate monitoring is currently done using dedicated chest band sensors, but these devices are specialized, expensive and cumbersome to wear day after day. While recent works have proposed using smartwatch based accelerometer and gyroscope data for respiratory rate monitoring, current methods are unreliable and inaccurate in the presence of motion and have therefore only been applied in controlled or low-motion settings. Thus, measuring respiratory rate in the wild remains a challenge. We observe that for many applications, having fewer accurate readings is better than having more, less accurate readings. Based on this, we develop WearBreathing, a novel system for respiratory rate monitoring. WearBreathing consists of a machine learning based filter that detects and rejects sensor data that are not suitable for respiratory rate extraction and a convolutional neural network model for extracting respiratory rate from accelerometer and gyroscope data. Using a diverse, out-of-the-lab dataset that we collected, we show that WearBreathing has a 2.5 to 5.8 times lower mean absolute error (MAE) than existing approaches. We show that WearBreathing is tunable and by changing a single threshold value, it can, for example, deliver a reading every 50 seconds with a MAE of 2.05 breaths/min or a reading every 5 minutes with an MAE of 1.09 breaths/min. Finally, we evaluate power consumption and find that with some power saving measures, WearBreathing can run on a smartwatch while providing a full day's worth of battery life.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328927"}, {"primary_key": "2860442", "vector": [], "sparse_vector": [], "title": "How Does a Nation Walk?: Interpreting Large-Scale Step Count Activity with Weekly Streak Patterns.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Activity trackers are being deployed in large-scale physical activity intervention programs, but analyzing their data is difficult due to the large data size and complexity. As such large datasets of steps become more available, it is paramount to develop analysis methods to deeply interpret them to understand the variety and changing nature of human steps behavior. In this work, we explored ways to analyze the heterogeneous steps activity data and propose a framework of dimensions and time aggregations to interpret how providing a city-wide population with activity trackers, and monetary incentives influences their wearing and steps behavior. We analyzed the daily step counts of 140,000 individuals, walking a combined 74 billion steps in 305 days of a city-wide public health campaign. We performed data mining clustering to identify 16 user segments, each with distinctive weekly streaks in patterns of device wear and recorded steps. We demonstrate that these clusters enable us to interpret how some users increased their steps level. Our key contributions are: a new analytic method to scalably interpret large steps data; the insights of our analysis about key user segments in our large intervention; demonstrating the power to predictive user outcomes from their first few days of tracking.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328928"}, {"primary_key": "2860445", "vector": [], "sparse_vector": [], "title": "Animo: Sharing Biosignals on a Smartwatch for Lightweight Social Connection.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Maria <PERSON>ska<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Animo, a smartwatch app that enables people to share and view each other's biosignals. We designed and engineered Animo to explore new ground for smartwatch-based biosignals social computing systems: identifying opportunities where these systems can support lightweight and mood-centric interactions. In our work we develop, explore, and evaluate several innovative features designed for dyadic communication of heart rate. We discuss the results of a two-week study (N=34), including new communication patterns participants engaged in, and outline the design landscape for communicating with biosignals on smartwatches.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314405"}, {"primary_key": "2860446", "vector": [], "sparse_vector": [], "title": "Machine Learning for Phone-Based Relationship Estimation: The Need to Consider Population Heterogeneity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Estimating the category and quality of interpersonal relationships from ubiquitous phone sensor data matters for studying mental well-being and social support. Prior work focused on using communication volume to estimate broad relationship categories, often with small samples. Here we contextualize communications by combining phone logs with demographic and location data to predict interpersonal relationship roles on a varied sample population using automated machine learning methods, producing better performance (F1 = 0.68) than using communication features alone (F1 = 0.62). We also explore the effect of age variation in the underlying training sample on interpersonal relationship prediction and find that models trained on younger subgroups, which is popular in the field via student participation and recruitment, generalize poorly to the wider population. Our results not only illustrate the value of using data across demographics, communication patterns and semantic locations for relationship prediction, but also underscore the importance of considering population heterogeneity in phone-based personal sensing studies.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369820"}, {"primary_key": "2860447", "vector": [], "sparse_vector": [], "title": "Reconstructing Human Joint Motion with Computational Fabrics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Accurate and continuous monitoring of joint rotational motion is crucial for a wide range of applications such as physical rehabilitation [6, 85] and motion training [22, 54, 68]. Existing motion capture systems, however, either need instrumentation of the environment, or fail to track arbitrary joint motion, or impose wearing discomfort by requiring rigid electrical sensors right around the joint area. This work studies the use of everyday fabrics as a flexible and soft sensing medium to monitor joint angular motion accurately and reliably. Specifically we focus on the primary use of conductive stretchable fabrics to sense the skin deformation during joint motion and infer the joint rotational angle. We tackle challenges of fabric sensing originated by the inherent properties of elastic materials by leveraging two types of sensing fabric and characterizing their properties based on models in material science. We apply models from bio-mechanics to infer joint angles and propose the use of dual strain sensing to enhance sensing robustness against user diversity and fabric position offsets. We fabricate prototypes using off-the-shelf fabrics and micro-controller. Experiments with ten participants show 9.69° median angular error in tracking joint angle and its sensing robustness across various users and activities.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314406"}, {"primary_key": "2860448", "vector": [], "sparse_vector": [], "title": "DrawingPresence: A Method for Assessing Temporal Fluctuations of Presence Status in a VR Experience.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "One of the main measures to evaluate a head-mounted display (HMD) based experience is the state of feeling present in virtual reality (VR). The detection of disturbances of such an experience that occur over time, namely breaks in presence (BIP), enables the evaluation and improvement of these. Existing methods do not detect BIPs, e.g., questionnaires, or are complex in their application and evaluation, e.g., physiological and behavioral measures. We propose a handy post-experience method in which users reflect on their experienced state of presence by drawing a line in a paper-based drawing template. The amplitude of the drawn line represents the state of presence of the temporal progress of the experience. We propose a descriptive model that describes temporal variations in the drawings by the definition of relevant points over time, e.g., putting on the HMD, phases of the experience, transition into VR, and parameters, e.g., the transition time. The descriptive model enables us to objectively evaluate user drawings and represent the course of the drawings by a defined set of parameters. Our exploratory user study (N = 30) showed that the drawings are very consistent between participants and the method is able to securely detect a variety of BIPs. Moreover, the results indicate that the method might be used in the future to evaluate the strength of BIPs and to reflect the temporal course of a presence experience in detail. Additional application examples and a detailed discussion pave the way for others to use our method. Further, they serve as a motivation to continue working on the method and the general understanding of temporal fluctuations of the presence experience.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369827"}, {"primary_key": "2860449", "vector": [], "sparse_vector": [], "title": "Light Ears: Information Leakage via Smart Lights.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern Internet-enabled smart lights promise energy efficiency and many additional capabilities over traditional lamps. However, these connected lights also create a new attack surface, which can be maliciously used to violate users' privacy and security. In this paper, we design and evaluate novel attacks that take advantage of light emitted by modern smart bulbs, in order to infer users' private data and preferences. The first two attacks are designed to infer users' audio and video playback by a systematic observation and analysis of the multimedia-visualization functionality of smart light bulbs. The third attack utilizes the infrared capabilities of such smart light bulbs to create a covert-channel, which can be used as a gateway to exfiltrate user's private data out of their secured home or office network. A comprehensive evaluation of these attacks in various real-life settings confirms their feasibility and affirms the need for new privacy protection mechanisms.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351256"}, {"primary_key": "2860452", "vector": [], "sparse_vector": [], "title": "Prediction of Mood Instability with Passive Sensing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Sidney K. D&apos;Mello", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mental health issues, which can be difficult to diagnose, are a growing concern worldwide. For effective care and support, early detection of mood-related health concerns is of paramount importance. Typically, survey based instruments including Ecologically Momentary Assessments (EMA) and Day Reconstruction Method (DRM) are the method of choice for assessing mood related health. While effective, these methods require some effort and thus both compliance rates as well as quality of responses can be limited. As an alternative, We present a study that used passively sensed data from smartphones and wearables and machine learning techniques to predict mood instabilities, an important aspect of mental health. We explored the effectiveness of the proposed method on two large-scale datasets, finding that as little as three weeks of continuous, passive recordings were sufficient to reliably predict mood instabilities.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351233"}, {"primary_key": "2860453", "vector": [], "sparse_vector": [], "title": "MenstruLoss: Sensor For Menstrual Blood Loss Monitoring.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Self-monitoring of menstrual blood loss volume could lead to early detection of multiple gynecological diseases. In this paper, we describe the development of a textile-based blood volume sensor which can be integrated into the sanitary napkin to quantify the menstrual blood loss during menstruation. It is based on sensing the resistance change detected as the output voltage change, with the added volume of fluid. Benchtop characterization tests with 5 mL of fluid determined the effect of spacing, orientation and weight, and location of fluid drop on the sensor. The sensor has been evaluated by intravenous blood samples collected from 18 participants and menstrual blood samples collected from 10 participants for four months. The collected intravenous blood samples and menstrual blood samples were used to create two regression model that can predict the blood volume and menstrual blood volume from the voltage input with Mean Absolute Percentage Error (MAPE) of 11-15% and 15-30% respectively.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328929"}, {"primary_key": "2860454", "vector": [], "sparse_vector": [], "title": "Understanding Cycling Trip Purpose and Route Choice Using GPS Traces and Open Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Many mobile applications such as Strava or Mapmyride allow cyclists to collect detailed GPS traces of their trips for health or route sharing purposes. However, cycling GPS traces also have a lot of potential from an urban planning perspective. In this paper, we focus on two important issues to characterize urban cyclist behavior: trip purpose and route choice. Cycling trip purpose has been typically analyzed using survey data. Here, we present a method to automatically infer the purpose of a cycling trip using cyclists' personal data, GPS traces and a variety of built-in and social environment features extracted from open datasets characterizing the streets cycled. We evaluate the proposed method using GPS traces from over 7, 000 cycling routes in the city of Philadelphia and report F1 scores of up to 86% when four trip purposes are considered. On the other hand, we also present a novel statistical method to identify the role that certain variables characterizing the built-in and social environment play in the selection of a specific cycling route. Our results show that cyclists in Philadelphia tend to favor routes with green areas, safety and centrality.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314407"}, {"primary_key": "2860455", "vector": [], "sparse_vector": [], "title": "DeepNavi: A Deep Signal-Fusion Framework for Accurate and Applicable Indoor Navigation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Suining He", "<PERSON>", "S.<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Indoor navigation plays a crucial role in indoor location-based services. Single signal-based navigation systems, however, are prone to sensor noises, signal ambiguities and are specific to trial sites. To address these, existing work fuses different signals with user trajectories. Despite their accuracy, many of them are specific to input signals and navigation modes (e.g., spot-based or sequence-based) and are computationally expensive in large sites. Additionally, they do not give predictive uncertainty estimations, leading to a lack of trust in navigation instructions. In this paper, we propose a unified framework for accurate indoor navigation in various modes with different inputs, termed DeepNavi. We exploit either convolutional or recurrent neural networks for initial feature extraction. Afterwards, we insert fully connected layers to generalize extracted signal-dependent features to a shared domain before fusion. Then, we leverage state-of-the-art ensemble learning to learn multiple predictive models. By combining them together, we further reduce the impact of signal noises and achieve high accuracy. Finally, we insert mixture density networks to model more generalized data distributions and provide uncertainty estimations. We have implemented DeepNavi and conducted extensive experiments in two different trial sites with different signal combinations. Experimental results show that DeepNavi reduces location errors by more than 20% with comparable orientation accuracy.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351257"}, {"primary_key": "2860456", "vector": [], "sparse_vector": [], "title": "AuraRing: Precise Electromagnetic Finger Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Shwetak N. Patel"], "summary": "Wearable computing platforms, such as smartwatches and head-mounted mixed reality displays, demand new input devices for high-fidelity interaction. We present AuraRing, a wearable magnetic tracking system designed for tracking fine-grained finger movement. The hardware consists of a ring with an embedded electromagnetic transmitter coil and a wristband with multiple sensor coils. By measuring the magnetic fields at different points around the wrist, AuraRing estimates the five degree-of-freedom pose of the ring. We develop two different approaches to pose reconstruction---a first-principles iterative approach and a closed-form neural network approach. Notably, AuraRing requires no runtime supervised training, ensuring user and session independence. AuraRing has a resolution of 0.1 mm and a dynamic accuracy of 4.4 mm, as measured through a user evaluation with optical ground truth. The ring is completely self-contained and consumes just 2.3 mW of power.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369831"}, {"primary_key": "2860457", "vector": [], "sparse_vector": [], "title": "Drinks &amp; Crowds: Characterizing Alcohol Consumption through Crowdsensing and Social Media.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "The design of computational methods to recognize alcohol intake is a relevant problem in ubiquitous computing. While mobile crowdsensing and social media analytics are two current approaches to characterize alcohol consumption in everyday life, the question of how they can be integrated, to examine their relative value as informative of the drinking phenomenon and to exploit their complementarity towards the classification of drinking-related attributes, remains as an open issue. In this paper, we present a comparative study based on five years of Instagram data about alcohol consumption and a 200+ person crowdsensing campaign collected in the same country (Switzerland). Our contributions are two-fold. First, we conduct data analyses that uncover temporal, spatial, and social contextual patterns of alcohol consumption on weekend nights as represented by both crowdsensing and social media. This comparative analysis provides a contextual snapshot of the alcohol drinking practices of urban youth dwellers. Second, we use a machine learning framework to classify individual drinking events according to alcohol and non-alcohol categories, using images features and contextual cues from individual and joint data sources. Our best performing models give an accuracy of 82.3% on alcohol category classification (against a baseline of 48.5%) and 90% on alcohol/non-alcohol classification (against a baseline of 65.9%) using a fusion of image features and contextual cues in this task. Our work uncovers important patterns in drinking behaviour across these two datasets and the results of study are promising towards developing systems that use machine learning for self-monitoring of alcohol consumption.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328930"}, {"primary_key": "2860458", "vector": [], "sparse_vector": [], "title": "Using Built-In Sensors to Predict and Utilize User Satisfaction for CPU Settings on Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Understanding user experience/satisfaction with mobile systems in order to manage computational resources has become a popular approach in recent years. One of the key issues in this area is to gauge user satisfaction. In this paper, we propose and evaluate a system to save energy by altering CPU core count and frequency while keeping users satisfied. Specifically, the system uses the sensor data collected from two popular personal devices: a smartphone and a smartwatch. In the proposed architecture, we first develop prediction models by collecting sensor data along with user performance satisfaction inputs. Then, our system predicts users' current satisfaction and sets CPU core/frequency based on these predictions in real-time. We observe that sensor data gathered from these two devices are highly correlated with users' instantaneous satisfaction of the phone. We evaluate the proposed system by developing and comparing two different models. First, we develop a user-independent (user-oblivious) model by using data gathered from 10 users. Second, we develop user-dependent (personal) models for 20 different users. We demonstrate that both models can predict satisfaction with over 97% accuracy on average when a binary satisfaction model is used (i.e., users indicating satisfied versus unsatisfied). The prediction accuracy is over 91% on average if a 3-level satisfaction model is used. Our results also show that when compared to default scheme, the user-independent and user-dependent models save 8.96% and 10.12% of the total system energy on average, respectively, without impacting user satisfaction.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314408"}, {"primary_key": "2860459", "vector": [], "sparse_vector": [], "title": "Combating Replay Attacks Against Voice Assistants.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, there has been a surge in the popularity of voice-first devices, such as Amazon Echo, Google Home, etc. While these devices make our life more convenient, they are vulnerable to new attacks, such as voice replay. We develop an end-to-end system to detect replay attacks without requiring a user to wear any wearable device. Our system, called REVOLT, has several distinct features: (i) it intelligently exploits the inherent differences between the spectral characteristics of the original and replayed voice signals, (ii) it exploits both acoustic and WiFi channels in tandem, (iii) it utilizes unique breathing rate extracted from WiFi signal while speaking to test the liveness of human voice. After extensive evaluation, our voice component yields Equal Error Rate (EER) of 0.88% and 10.32% in our dataset and ASV2017 dataset, respectively; and WiFi based breathing detection achieves Breaths Per Minute (BPM) error of 1.8 up to 3m distance. We further combine WiFi and voice based detection and show the overall system offers low false positive and false negative when evaluated against a range of attacks.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351258"}, {"primary_key": "2860460", "vector": [], "sparse_vector": [], "title": "Cross-Dataset Activity Recognition via Adaptive Spatial-Temporal Transfer Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Human activity recognition (HAR) aims at recognizing activities by training models on the large quantity of sensor data. Since it is time-consuming and expensive to acquire abundant labeled data, transfer learning becomes necessary for HAR by transferring knowledge from existing domains. However, there are two challenges existing in cross-dataset activity recognition. The first challenge is source domain selection. Given a target task and several available source domains, it is difficult to determine how to select the most similar source domain to the target domain such that negative transfer can be avoided. The second one is accurately activity transfer. After source domain selection, how to achieve accurate knowledge transfer between the selected source and the target domain remains another challenge. In this paper, we propose an Adaptive Spatial-Temporal Transfer Learning (ASTTL) approach to tackle both of the above two challenges in cross-dataset HAR. ASTTL learns the spatial features in transfer learning by adaptively evaluating the relative importance between the marginal and conditional probability distributions. Besides, it captures the temporal features via incremental manifold learning. Therefore, ASTTL can learn the adaptive spatial-temporal features for cross-dataset HAR and can be used for both source domain selection and accurate activity transfer. We evaluate the performance of ASTTL through extensive experiments on 4 public HAR datasets, which demonstrates its effectiveness. Furthermore, based on ASTTL, we design and implement an adaptive cross-dataset HAR system called Client-Cloud Collaborative Adaptive Activity Recognition System (3C2ARS) to perform HAR in the real environment. By collecting activities in the smartphone and transferring knowledge in the cloud server, ASTTL can significantly improve the performance of source domain selection and accurate activity transfer.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369818"}, {"primary_key": "2860462", "vector": [], "sparse_vector": [], "title": "ReVibe: A Context-assisted Evening Recall Approach to Improve Self-report Adherence.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Predrag <PERSON>", "<PERSON>"], "summary": "Besides passive sensing, ecological momentary assessments (EMAs) are one of the primary methods to collect in-the-moment data in ubiquitous computing and mobile health. While EMAs have the advantage of low recall bias, a disadvantage is that they frequently interrupt the user and thus long-term adherence is generally poor. In this paper, we propose a less-disruptive self-reporting method, \"assisted recall,\" in which in the evening individuals are asked to answer questions concerning a moment from earlier in the day assisted by contextual information such as location, physical activity, and ambient sounds collected around the moment to be recalled. Such contextual information is automatically collected from phone sensor data, so that self-reporting does not require devices other than a smartphone. We hypothesized that providing assistance based on such automatically collected contextual information would increase recall accuracy (i.e., if recall responses for a moment match the EMA responses at the same moment) as compared to no assistance, and we hypothesized that the overall completion rate of evening recalls (assisted or not) would be higher than for in-the-moment EMAs. We conducted a two-week study (N=54) where participants completed recalls and EMAs each day. We found that providing assistance via contextual information increased recall accuracy by 5.6% (p = 0.032) and the overall recall completion rate was on average 27.8% (p &lt; 0.001) higher than that of EMAs.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369806"}, {"primary_key": "2860463", "vector": [], "sparse_vector": [], "title": "Vision2Sensor: Knowledge Transfer Across Sensing Modalities for Human Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Mobile and wearable sensing devices are pervasive, coming packed with a growing number of sensors. These are supposed to provide direct observations about user activity and context to intelligent systems, and are envisioned to be at the core of smart buildings, towards habitat automation to suit user needs. However, much of this enormous sensing capability is currently wasted, instead of being tapped into, because developing context recognition systems requires substantial amount of labeled sensor data to train models on. Sensor data is hard to interpret and annotate after collection, making it difficult and costly to generate large training sets, which is now stalling the adoption of mobile sensing at scale. We address this fundamental problem in the ubicomp community (not having enough training data) by proposing a knowledge transfer framework, Vision2Sensor, which opportunistically transfers information from an easy to interpret and more advanced sensing modality, vision, to other sensors on mobile devices. Activities recognised by computer vision in the camera field of view are synchronized with inertial sensor data to produce labels, which are then used to dynamically update a mobile sensor based recognition model. We show that transfer learning is also beneficial to identifying the best Convolutional Neural Network for vision based human activity recognition for our task. The performance of a proposed network is first evaluated on a larger dataset, followed by transferring the pre-trained model to be fine-tuned on our five class activity recognition task. Our sensor based Deep Neural Network is robust to withstand substantial degradation of label quality, dropping just 3% in accuracy on induced degradation of 15% to vision generated labels. This indicates that knowledge transfer between sensing modalities is achievable even with significant noise introduced by the labeling modality. Our system operates in real-time on embedded computing devices, ensuring user data privacy by performing all the computations in the local network.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351242"}, {"primary_key": "2860464", "vector": [], "sparse_vector": [], "title": "Clinical Data in Context: Towards Sensemaking Tools for Interpreting Personal Health Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Clinical data augmented with contextual data can help patients with chronic conditions make sense of their disease. However, existing tools do not support interpretation of multiple data streams. To better understand how individuals make sense of clinical and contextual data, we interviewed patients with Type 1 diabetes and their caregivers using context-enhanced visualizations of patients' data as probes to facilitate interpretation activities. We observed that our participants performed four analytical activities when interpreting their data -- finding context-based trends and explaining them, triangulating multiple factors, suggesting context-specific actions, and hypothesizing about alternate contextual factors affecting outcomes. We also observed two challenges encountered during analysis -- the inability to identify clear trends challenged action planning and counterintuitive insights compromised trust in data. Situating our findings within the existing sensemaking frameworks, we demonstrate that sensemaking can not only inform action but can guide the discovery of information needs for exploration. We further argue that sensemaking is a valuable approach for exploring contextual data. Informed by our findings and our reflection on existing sensemaking frameworks, we provide design guidelines for sensemaking tools to improve awareness of contextual factors affecting patients and to support patients' agency in making sense of health data.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314409"}, {"primary_key": "2860465", "vector": [], "sparse_vector": [], "title": "Multi-task Self-Supervised Learning for Human Activity Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep learning methods are successfully used in applications pertaining to ubiquitous computing, health, and well-being. Specifically, the area of human activity recognition (HAR) is primarily transformed by the convolutional and recurrent neural networks, thanks to their ability to learn semantic representations from raw input. However, to extract generalizable features, massive amounts of well-curated data are required, which is a notoriously challenging task; hindered by privacy issues, and annotation costs. Therefore, unsupervised representation learning is of prime importance to leverage the vast amount of unlabeled data produced by smart devices. In this work, we propose a novel self-supervised technique for feature learning from sensory data that does not require access to any form of semantic labels. We learn a multi-task temporal convolutional network to recognize transformations applied on an input signal. By exploiting these transformations, we demonstrate that simple auxiliary tasks of the binary classification result in a strong supervisory signal for extracting useful features for the downstream task. We extensively evaluate the proposed approach on several publicly available datasets for smartphone-based HAR in unsupervised, semi-supervised, and transfer learning settings. Our method achieves performance levels superior to or comparable with fully-supervised networks, and it performs significantly better than autoencoders. Notably, for the semi-supervised case, the self-supervised features substantially boost the detection rate by attaining a kappa score between 0.7-0.8 with only 10 labeled examples per class. We get similar impressive performance even if the features are transferred from a different data source. While this paper focuses on HAR as the application domain, the proposed technique is general and could be applied to a wide variety of problems in other areas.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328932"}, {"primary_key": "2860467", "vector": [], "sparse_vector": [], "title": "Examining Opportunities for Goal-Directed Self-Tracking to Support Chronic Condition Management.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Although self-tracking offers potential for a more complete, accurate, and longer-term understanding of personal health, many people struggle with or fail to achieve their goals for health-related self-tracking. This paper investigates how to address challenges that result from current self-tracking tools leaving a person's goals for their data unstated and lacking explicit support. We examine supporting people and health providers in expressing and pursuing their tracking-related goals via goal-directed self-tracking, a novel method to represent relationships between tracking goals and underlying data. Informed by a reanalysis of data from a prior study of migraine tracking goals, we created a paper prototype to explore whether and how goal-directed self-tracking could address current disconnects between the goals people have for data in their chronic condition management and the tools they use to support such goals. We examined this prototype in interviews with 14 people with migraine and 5 health providers. Our findings indicate the potential for scaffolding goal-directed self-tracking to: 1) elicit different types and hierarchies of management and tracking goals; 2) help people prepare for all stages of self-tracking towards a specific goal; and 3) contribute additional expertise in patient-provider collaboration. Based on our findings, we present implications for the design of tools that explicitly represent and support an individual's specific self-tracking goals.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369809"}, {"primary_key": "2860468", "vector": [], "sparse_vector": [], "title": "Two Tell-tale Perspectives of PTSD: Neurobiological Abnormalities and Bayesian Regulatory Network of the Underlying Disorder in a Refugee Context.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Md Mu<PERSON>", "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>"], "summary": "Global refugee crisis around the world has displaced millions of people from their homes. Although some of them adjust well, many suffer from significant psychological distress, such as post-traumatic stress disorder (PTSD), owing to exposure to traumatic events and hardships. Here, diagnosis and access to psychological health care present particular challenges for various human-centered design issues. Therefore, analyzing the case of Rohingya refugees in Bangladesh, we propose a two-way diagnosis of PTSD using (i) short inexpensive questionnaire to determine its prevalence, and (ii) low-cost portable EEG headset to identify potential neurobiological markers of PTSD. To the best of our knowledge, this study is the first to use consumer-grade EEG devices in the scarce-resource settings of refugees. Moreover, we explored the underlying structure of PTSD and its symptoms via developing various hybrid models based on Bayesian inference by combining aspects from both reflective and formative models of PTSD, which is also the first of its kind. Our findings revealed several key components of PTSD and its neurobiological abnormality. Moreover, challenges faced during our study would inform design processes of screening tools and treatments of PTSD to incorporate refugee experience in a more meaningful way during contemporary and future humanitarian crisis.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351259"}, {"primary_key": "2860470", "vector": [], "sparse_vector": [], "title": "VLA: A Practical Visible Light-based Attack on Face Recognition Systems in Physical World.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Xiaojiang Du"], "summary": "Adversarial example attacks have become a growing menace to neural network-based face recognition systems. Generated by composing facial images with pixel-level perturbations, adversarial examples change key features of inputs and thereby lead to misclassification of neural networks. However, the perturbation loss caused by complex physical environments sometimes prevents existing attack methods from taking effect. In this paper, we focus on designing new attacks that are effective and inconspicuous in the physical world. Motivated by the differences in image-forming principles between cameras and human eyes, we propose VLA, a novel attack against black-box face recognition systems using visible light. In VLA, visible light-based adversarial perturbations are crafted and projected on human faces, which allows an adversary to conduct targeted or un-targeted attacks. VLA decomposes adversarial perturbations into a perturbation frame and a concealing frame, where the former adds modifications on human facial images while the latter makes these modifications inconspicuous to human eyes. We conduct extensive experiments to demonstrate the effectiveness, inconspicuousness, and robustness of the adversarial examples crafted by VLA in physical scenarios.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351261"}, {"primary_key": "2860471", "vector": [], "sparse_vector": [], "title": "Breeze: Smartphone-based Acoustic Real-time Detection of Breathing Phases for a Gamified Biofeedback Breathing Training.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yanick X. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Slow-paced biofeedback-guided breathing training has been shown to improve cardiac functioning and psychological well-being. Current training options, however, attract only a fraction of individuals and are limited in their scalability as they require dedicated biofeedback hardware. In this work, we present Breeze, a mobile application that uses a smartphone's microphone to continuously detect breathing phases, which then trigger a gamified biofeedback-guided breathing training. Circa 2.76 million breathing sounds from 43 subjects and control sounds were collected and labeled to train and test our breathing detection algorithm. We model breathing as inhalation-pause-exhalation-pause sequences and implement a phase-detection system with an attention-based LSTM model in conjunction with a CNN-based breath extraction module. A biofeedback-guided breathing training with <PERSON><PERSON> takes place in real-time and achieves 75.5% accuracy in breathing phases detection. <PERSON><PERSON> was also evaluated in a pilot study with 16 new subjects, which demonstrated that the majority of subjects prefer Breeze over a validated active control condition in its usefulness, enjoyment, control, and usage intentions. Breeze is also effective for strengthening users' cardiac functioning by increasing high-frequency heart rate variability. The results of our study suggest that <PERSON>ze could potentially be utilized in clinical and self-care activities.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369835"}, {"primary_key": "2860472", "vector": [], "sparse_vector": [], "title": "W!NCE: Unobtrusive Sensing of Upper Facial Action Units with EOG-based Eyewear.", "authors": ["Soha Rostaminia", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The ability to unobtrusively and continuously monitor one's facial expressions has implications for a variety of application domains ranging from affective computing to health-care and the entertainment industry. The standard Facial Action Coding System (FACS) along with camera based methods have been shown to provide objective indicators of facial expressions; however, these approaches can also be fairly limited for mobile applications due to privacy concerns and awkward positioning of the camera. To bridge this gap, W!NCE re-purposes a commercially available Electrooculography-based eyeglass (J!NS MEME) for continuously and unobtrusively sensing of upper facial action units with high fidelity. W!NCE detects facial gestures using a two-stage processing pipeline involving motion artifact removal and facial action detection. We validate our system's applicability through extensive evaluation on data from 17 users under stationary and ambulatory settings, a pilot study for continuous pain monitoring and several performance benchmarks. Our results are very encouraging, showing that we can detect five distinct facial action units with a mean F1 score of 0.88 in stationary and 0.82 in ambulatory settings, and that we can accurately detect facial gestures that due to pain.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314410"}, {"primary_key": "2860473", "vector": [], "sparse_vector": [], "title": "Recruit Until It Fails: Exploring Performance Limits for Identification Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Distinguishing identities is useful for several applications such as automated grocery or personalized recommendations. Unfortunately, several recent proposals for identification systems are evaluated using poor recruitment practices. We discovered that 23 out of 30 surveyed systems used datasets with 20 participants or less. Those studies achieved an average classification accuracy of 93%. We show that the classifier performance is misleading when the participant count is small. This is because the finite precision of measurements creates upper limits on the number of users that can be distinguished. To demonstrate why classifier performance is misleading, we used publicly available datasets. The data was collected from human subjects. We created five systems with at least 20 participants each. In three cases we achieved accuracies greater than 90% by merely applying readily available machine learning software packages, often with default parameters. For datasets where we had sufficient participants, we evaluated how the performance degrades as the number of participants increases. One of the systems built suffered a drop in accuracy that was over 35% as the participant count increased from 20 to 250. We argue that data from small participant count datasets do not adequately explore variations. Systems trained on such limited data are likely to incorrectly identify users when the user base increases beyond what was tested. We conclude by explaining generalizable reasons for this issue and provide insights on how to conduct more robust system analysis and design.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351262"}, {"primary_key": "2860474", "vector": [], "sparse_vector": [], "title": "Alvus: A Reconfigurable 2-D Wireless Charging System.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Wireless charging pads such as Qi are rapidly gaining ground, but their limited power supply range still requires precise placement on a specific point. 2-D wireless power transfer (WPT) sheets consisting of coil arrays are one well-known counterpart to extend this range. However, these approaches require custom-made designs by expert engineers; what we need is a WPT system that can be reconfigured by simply placing ready-made modules on the intended surface (e.g., table, floor, shelf board, etc). In this paper, we present \"Alvus\", a reconfigurable 2-D WPT system which enables such simple construction of WPT surfaces. Our system is based on multihop WPT that composes \"virtual power cords\" and consists of three types of ready-made resonator modules: (i) transmitter, which outputs energy, (ii) relays, which pass energy down to the next module, and (iii) receivers, which receive energy and charge the loads. We show that power can be transferred efficiently (over 25%) within a range of 19.6 m2 using a single transmitter. We implemented an end-to-end WPT system and demonstrated that Alvus is capable of intuitive construction/reconfiguration of WPT surfaces, as well as automatically deciding the power routes based on the sensed information (e.g., receiver location, module placement, obstructive objects).", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3332533"}, {"primary_key": "2860475", "vector": [], "sparse_vector": [], "title": "A Multisensor Person-Centered Approach to Understand the Role of Daily Activities in Job Performance with Organizational Personas.", "authors": ["Vedant <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Subigya Nepal", "<PERSON><PERSON>", "Man<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Sidney D&apos;Mello", "<PERSON><PERSON>", "Kaifeng Jiang", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Several psychologists posit that performance is not only a function of personality but also of situational contexts, such as day-level activities. Yet in practice, since only personality assessments are used to infer job performance, they provide a limited perspective by ignoring activity. However, multi-modal sensing has the potential to characterize these daily activities. This paper illustrates how empirically measured activity data complements traditional effects of personality to explain a worker's performance. We leverage sensors in commodity devices to quantify the activity context of 603 information workers. By applying classical clustering methods on this multisensor data, we take a person-centered approach to describe workers in terms of both personality and activity. We encapsulate both these facets into an analytical framework that we call organizational personas. On interpreting these organizational personas we find empirical evidence to support that, independent of a worker's personality, their activity is associated with job performance. While the effects of personality are consistent with the literature, we find that the activity is equally effective in explaining organizational citizenship behavior and is less but significantly effective for task proficiency and deviant behaviors. Specifically, personas that exhibit a daily-activity pattern with fewer location visits, batched phone-use, shorter desk-sessions and longer sleep duration, tend to perform better on all three performance metrics. Organizational personas are a descriptive framework to identify the testable hypotheses that can disentangle the role of malleable aspects like activity in determining the performance of a worker population.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369828"}, {"primary_key": "2860476", "vector": [], "sparse_vector": [], "title": "Input, Output and Construction Methods for Custom Fabrication of Room-Scale Deployable Pneumatic Structures.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we examine the future of designing room-scale deployable pneumatic structures that can be fabricated with interactive capabilities and thus be responsive to human input and environments. While there have been recent advances in fabrication methods for creating large-scale structures, they have mainly focused around creating passive structures. Hence in this work, we collectively tackle three main challenges that need to be solved for designing room-scale interactive deployable structures namely -- the input, output (actuation) and construction methods. First, we explore three types of sensing methods --- acoustic, capacitive and pressure --- in order to embed input into these structures. These sensing methods enable users to perform gestures such as knock, squeeze and swipe with specific parts of our fabricated structure such as doors, windows, etc. and make them interactive. Second, we explore three types of actuation mechanisms -- inflatable tendon drive, twisted tendon drive and roll bending actuator -- that are implemented at structural scale and can be embedded into our structures to enable a variety of responsive actuation. Finally, we provide a construction method to custom fabricate and assemble inter-connected pneumatic trusses with embedded sensing and actuation capability to prototype interactions with room-scale deployable structures. To further illustrate the collective (input, output and construction) usage of the system, we fabricated three exemplar interactive deployable structures -- a responsive canopy, an interactive geodesic dome and a portable table (Figures 1 and 2). These can be deployed from a compact deflated state to a much larger inflated state which takes on a desired form while offering interactivity.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328933"}, {"primary_key": "2860477", "vector": [], "sparse_vector": [], "title": "Investigating Users&apos; Preferences and Expectations for Always-Listening Voice Assistants.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many consumers now rely on different forms of voice assistants, both stand-alone devices and those built into smartphones. Currently, these systems react to specific wake-words, such as \"Alexa,\" \"Siri,\" or \"Ok Google.\" However, with advancements in natural language processing, the next generation of voice assistants could instead always listen to the acoustic environment and proactively provide services and recommendations based on conversations without being explicitly invoked. We refer to such devices as \"always listening voice assistants\" and explore expectations around their potential use. In this paper, we report on a 178-participant survey investigating the potential services people anticipate from such a device and how they feel about sharing their data for these purposes. Our findings reveal that participants can anticipate a wide range of services pertaining to a conversation; however, most of the services are very similar to those that existing voice assistants currently provide with explicit commands. Participants are more likely to consent to share a conversation when they do not find it sensitive, they are comfortable with the service and find it beneficial, and when they already own a stand-alone voice assistant. Based on our findings we discuss the privacy challenges in designing an always-listening voice assistant.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369807"}, {"primary_key": "2860479", "vector": [], "sparse_vector": [], "title": "Tracking Fatigue and Health State in Multiple Sclerosis Patients Using Connnected Wellness Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Multiple Sclerosis requires long-term disease management, but tracking patients through the use of clinical surveys is hindered by high costs and patient burden. In this work, we investigate the feasibility of using data from ubiquitous sensing to predict MS patients' fatigue and health status, as measured by the Fatigue Severity Scale (FSS) and EQ-5D index. We collected data from 198 MS patients who are given connected wellness devices for over 6 months. We examine how accurately can the collected data predict reported FSS and EQ-5D scores per patient using an ensemble of regressors. In predicting for both FSS and EQ-5D, we are able to achieve errors aligning with the instrument' standard measurement error (SEM), as well as strong and significant correlations between predicted and ground truth values. We also show a simple adaptation method that greatly reduces prediction errors through the use of just 1 user-supplied ground truth datapoint. For FSS (SEM 0.7), the universal model predicts weekly scores with MAE 1.00, while an adapted model predicts with MAE 0.58. For EQ-5D (SEM 0.093), the universal model predicts weekly scores with MAE 0.097, while an adapted model predicts with MAE 0.065. Our study represents the first sets of results showing that fatigue and health state of MS patients can be measured using data from connected wellness devices and a small number of background features, with promising prediction performance with errors within the accepted range of error in the widely used clinically-validated questionnaires. Future extensions and potential applications of our results can positively impact MS patient disease management and support clinical research.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351264"}, {"primary_key": "2860480", "vector": [], "sparse_vector": [], "title": "Batch Localization Based on OFDMA Backscatter.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "OFDMA Wi-Fi backscatter can significantly improve the communication efficiency and meanwhile maintain ultra-low power consumption; however, the ground-up reworking on the core mechanism of traditional Wi-Fi system revolutionizes the basis of many existing Wi-Fi based mechanisms. In this paper, we explore how localization can be realized based on OFDMA backscatter, where a batch localization mechanism utilizing concurrent communication in the OFDMA backscatter system is proposed. We present a series of mechanisms to deal with the fundamental change of assumptions brought by the new paradigm. First, we process signals at the receiver in a finer granularity for signal classification. Then we remove phase offsets in real time without interrupting the communication. Finally, we propose an extended MUSIC algorithm to improve accuracy with limited localization information in OFDMA backscatter mechanism. We implement a prototype under the 802.11g framework in WARP, based on which we conduct comprehensive experiments to evaluate our propose mechanism. Results show that our system can localize 48 tags simultaneously, while achieving average localization errors within 0.49m. The tag's power consumption is about 55-81.3μW.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314412"}, {"primary_key": "2860481", "vector": [], "sparse_vector": [], "title": "From Fingerprint to Footprint: Cold-start Location Recommendation by Learning User Interest from App Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Li Su", "<PERSON><PERSON><PERSON>"], "summary": "With increasing diversity of user interest and preference, personalized location recommendation is essential and beneficial to our daily life. To achieve this, the most critical challenge is the cold-start recommendation problem, for we cannot learn preference from cold-start users without any historical records. In this paper, we demonstrate that it is feasible to make personalized location recommendation by learning user interest and location features from app usage data. By proposing a novel generative model to transfer user interests from app usage behavior to location preference, we achieve personalized location recommendation via learning the interest's correlation between locations and apps. Based on two real-world datasets, we evaluate our method's performance with a variety of scenarios and parameters. The results demonstrate that our method outperforms the state-of-the-art solutions in solving cold-start problem, i.e., when there are 60% cold-start users, we can still achieve a 77.0% hitrate in recommending the top five locations, which is at least 9.6% higher than the baselines. Our study is the first step forward for transferring user interests learning from online fingerprints to offline footprints, which paves the way for better personalized location recommendation services.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314413"}, {"primary_key": "2860482", "vector": [], "sparse_vector": [], "title": "Enhancing Indoor Inertial Odometry with WiFi.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Accurately measuring the distance traversed by a subject, commonly referred to as odometry, in indoor environments is of fundamental importance in many applications such as augmented and virtual reality tracking, indoor navigation, and robot route guidance. While theoretically, odometry can be performed using a simple accelerometer, practically, it is well-known that the distances measured using accelerometers suffer from large drift errors. In this paper, we propose WIO, a WiFi-assisted Inertial Odometry technique that uses WiFi signals as an auxiliary source of information to correct these drift errors. The key intuition behind WIO is that among multiple reflections of a transmitted WiFi signal arriving at the WiFi receiver, WIO first isolates one reflection and then measures the change in the length of the path of that reflection as the subject moves. By identifying the extent through which the length of the path of that reflection changes, along with the direction of motion of the subject relative to that path, WIO can estimate the distance traversed by the subject using WiFi signals. WIO then uses this distance estimate to correct the drift errors. While researchers have previously proposed to use WiFi signals to correct drift errors, prior schemes suffer from one or more of the following six limitations: they 1) do not work indoors, 2) require manual exhaustive fingerprinting, 3) are not resilient against changes in environment including human movements, 4) do not work on commodity WiFi devices, 5) require multiple access points, and/or 6) can measure distance traversed by humans but not by non-human subjects. WIO addresses all of these limitations. We implemented WIO using commodity devices, and extensively evaluated it in a wide variety of complex indoor scenarios on both human and robotic subjects. Our results demonstrate that W<PERSON> achieved an average error of just 6.28% in estimating the distances traversed by the subjects.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328918"}, {"primary_key": "2860483", "vector": [], "sparse_vector": [], "title": "Pedestrians and Visual Signs of Intent: Towards Expressive Autonomous Passenger Shuttles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Autonomous Passenger Shuttles (APS) are rapidly becoming an urban public transit alternative. Traversing populous commercial and residential centers, these shuttles are already operating in several cities. In the absence of a human driver and embedded means of communicating the autonomous shuttle's intent, the task of seamlessly navigating crosswalks and pedestrian-friendly zones becomes a challenging pursuit for pedestrians. We contribute to the emerging notion of AV-Pedestrian Interaction by examining the context of autonomous passenger shuttles (APS) in real-world settings, and by comparing four different classes of visual signals -- namely instructional, symbolic, metaphorical, and anthropomorphic -- designed to communicate the shuttle's intentions. Following a participatory methodology involving local residents and public transport service provider, and working within the framework of inflexible road traffic regulations concerning the operation and testing of autonomous vehicles, we conducted a participatory design workshop, a qualitative, and a survey study. The findings revealed differences across these four classes of signals in terms of pedestrians' subjective perceptions. Anthropomorphic signals were identified as the preferred and effective modality in terms of pedestrians' interpretation of the communicated intent and their perceived sense of attention, confidence, and calmness. Additionally, pedestrians' experiences while judging the intention of transitionary vehicular states (starting/slowing) were reported as perplexing and evoked stress. These findings were translated into design and policy implications in collaboration with other stakeholders, and exemplify a viable way for assimilating human factors research in urban mobility.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351265"}, {"primary_key": "2860486", "vector": [], "sparse_vector": [], "title": "sharedCharging: Data-Driven Shared Charging for Large-Scale Heterogeneous Electric Vehicle Fleets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Our society is witnessing a rapid vehicle electrification process. Even though being environmental-friendly, electric vehicles have not reached their full potentials due to prolonged charging time. Moreover, unbalanced spatiotemporal charging demand/supply along with the uneven number of charging stations between heterogeneous fleets make electric vehicle management more challenging, e.g., surplus charging stations across a city for electric buses but limited charging stations in some regions for electric taxis, which severely limit the charging performance of the whole electric vehicle network in a city. In this paper, we first analyze a large-scale real-world dataset from two heterogeneous electric vehicle fleets in the Chinese city Shenzhen. We investigate their mobility and charging patterns and then verify the practicability and necessity of shared charging. Based on the insights we found, we design a generic real-time shared charging scheduling system called sharedCharging to improve overall charging efficiency for heterogeneous electric vehicle fleets. Our sharedCharging also considers sophisticated real-world constraints, e.g., station spaces, availability of charging points, real-time timetable guarantee, etc. More importantly, we take the electric bus and electric taxi fleets as a concrete example of heterogeneous electric vehicle fleets given their different operating patterns. We implement and evaluate sharedCharging with streaming data from over 13,000 electric taxis and 16,000 electric buses, coupled with the charging station data in the Chinese city Shenzhen, which is the largest public electric vehicle network in the world. The evaluation results demonstrate that the proposed sharedCharging reduces the waiting time by 63.5% and reduces the total charging time by 15% on average for e-taxis.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351266"}, {"primary_key": "2860487", "vector": [], "sparse_vector": [], "title": "Modeling Spatio-Temporal App Usage for a Large User Population.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Pan Hui", "<PERSON><PERSON><PERSON>"], "summary": "With the wide adoption of mobile devices, it becomes increasingly important to understand how users use mobile apps. Knowing when and where certain apps are used is instrumental for app developers to improve app usability and for Internet service providers (ISPs) to optimize their network services. However, modeling spatio-temporal patterns of app usage has been a challenging problem due to the complicated usage behavior and the very limited personal data. In this paper, we propose a Bayesian mixture model to capture when, where and what apps are used and predict future app usage. To solve the challenge of data sparsity, we apply a hierarchical Dirichlet process to leverage the shared spatio-temporal patterns to accurately model users with insufficient data. We then evaluate our model using a large dataset of app usage traces involving 1.7 million users over 3503 apps. Our analysis shows a clear correlation between the user's location and the apps being used. Extensive evaluations show that our model can accurately predict users' future locations and app usage, outperforming the state-of-the-art algorithms by 11.7% and 11.1%, respectively. In addition, our model can be used to synthesize app usage traces that do not leak user privacy while preserving the key data statistical properties.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314414"}, {"primary_key": "2860488", "vector": [], "sparse_vector": [], "title": "RFID Tattoo: A Wireless Platform for Speech Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "Chengfeng Pan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents an RF-based assistive technology for voice impairments (i.e., dysphonia), which occurs in an estimated 1% of the global population. We specifically focus on acquired voice disorders where users continue to be able to make facial and lip gestures associated with speech. Despite the rich literature on assistive technologies in this space, there remains a gap for a solution that neither requires external infrastructure in the environment, battery-powered sensors on skin or body-worn manual input devices. We present RFTattoo, which to our knowledge is the first wireless speech recognition system for voice impairments using batteryless and flexible RFID tattoos. We design specialized wafer-thin tattoos attached around the user's face and easily hidden by makeup. We build models that process signal variations from these tattoos to a portable RFID reader to recognize various facial gestures corresponding to distinct classes of sounds. We then develop natural language processing models that infer meaningful words and sentences based on the observed series of gestures. A detailed user study with 10 users reveals 86% accuracy in reconstructing the top-100 words in the English language, even without the users making any sounds.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369812"}, {"primary_key": "2860489", "vector": [], "sparse_vector": [], "title": "RF-Focus: Computer Vision-assisted Region-of-interest RFID Tag Recognition and Localization in Multipath-prevalent Environments.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Capturing RFID tags in the region of interest (ROI) is challenging. Many issues, such as multipath interference, frequency-dependent hardware characteristics and phase periodicity, make RF phase difficult to accurately indicate the tag-to-antenna distance for RFID tag localization. In this paper, we propose a comprehensive solution, called RF-Focus, which fuses RFID and computer vision (CV) techniques to recognize and locate moving RFID-tagged objects within ROI. Firstly, we build a multipath propagation model and propose a dual-antenna solution to minimize the impact of multipath interference on RF phase. Secondly, by extending the multipath model, we estimate phase shifts due to hardware characteristics at different operating frequencies. Thirdly, to minimize the tag position uncertainty due to RF phase periodicity, we leverage CV to extract image regions of being likely to contain ROI RFID-tagged objects, and then associate them with the processed RF phase after the removal of the phase shifts due to multipath interference and hardware characteristics for recognition and localization. Our experiments demonstrate the effectiveness of multipath modelling and hardware-related phase shift estimation. When five RFID-tagged objects are moving in the ROI, RF-Focus achieves the average recognition accuracy of 91.67% and localization accuracy of 94.26% given a false positive rate of 10%.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314416"}, {"primary_key": "2860490", "vector": [], "sparse_vector": [], "title": "CityGuard: Citywide Fire Risk Forecasting Using A Machine Learning Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Junkai Sun", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Forecasting the fire risk is of great importance to fire prevention deployments in a city, which can reduce loss even deaths caused by fires. However, it is very challenging because fires are influenced by many complex factors, including spatial correlations, temporal dependencies, even the mixture of these two and external factors. Firstly, the fire risk of a region is influenced by temporal effect of internal factors (e.g., the historical fire risk records) and temporal effect of external factors (e.g., weather). Secondly, a region's fire risk is not only influenced by its inherent geospatial attributes (e.g., POIs) but also dependent on other regions in spatial. To address these challenges, we propose a machine learning approach to forecast the fire risk, entitled NeuroFire. NeuroFire can represent internal and external temporal effect then combine the temporal representation and spatial dependencies by a spatial-temporal loss function. Experimental evaluations on real-world datasets show that our NeuroFire outperforms 9 baselines, demonstrating the performance of our approach by several visualizations. Moreover, we implement a citywide fire forecasting system named CityGuard to display the analysis and forecasting results, which can assist the fire rescue department in deploying fire prevention.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369814"}, {"primary_key": "2860492", "vector": [], "sparse_vector": [], "title": "Designing Drones: Factors and Characteristics Influencing the Perception of Flying Robots.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The last few years have seen a revolution in aerial robotics where personal drones are becoming pervasive to our environments and can be bought by anyone anywhere, including at local supermarkets. As they become ubiquitous to our lives, it is crucial to understand how they are perceived and understood by people. The robotics community has extensively theorized and quantified how robotic agents are perceived as social creatures and how this affects users and passersby. However, drones present different form factors that are yet to be systematically explored. This work aims to fill this gap by understanding people's perceptions of drones and how drones physical features correlate to a series of dimensions. We explored the quadcopters available on the 2018 market and built a dataset of 63 images that were evaluated in a user study (N=307). Using the study results, we present a model of how people understand drones based on their design and which physical features are better suited for people wanting to interact with drones. Our findings highlight that safety features have a negative effect on several dimensions including trust. Our work contributes a set of design guidelines for future personal drones and concludes on the implications for ubiquitous computing.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351269"}, {"primary_key": "2860493", "vector": [], "sparse_vector": [], "title": "ScratchThat: Supporting Command-Agnostic Speech Repair in Voice-Driven Assistants.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Speech interfaces have become an increasingly popular input method for smartphone-based virtual assistants, smart speakers, and Internet of Things (IoT) devices. While they facilitate rapid and natural interaction in the form of voice commands, current speech interfaces lack natural methods for command correction. We present ScratchThat, a method for supporting command-agnostic speech repair in voice-driven assistants, suitable for enabling corrective functionality within third-party commands. Unlike existing speech repair methods, ScratchThat is able to automatically infer query parameters and intelligently select entities in a correction clause for editing. We conducted three evaluations to (1) elicit natural forms of speech repair in voice commands, (2) compare the interaction speed and NASA TLX score of the system to existing voice-based correction methods, and (3) assess the accuracy of the ScratchThat algorithm. Our results show that (1) speech repair for voice commands differ from previous models for conversational speech repair, (2) methods for command correction based on speech repair are significantly faster than other voice-based methods, and (3) the ScratchThat algorithm facilitates accurate command repair as rated by humans (77% accuracy) and machines (0.94 BLEU score). Finally, we present several ScratchThat use cases, which collectively demonstrate its utility across many applications.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328934"}, {"primary_key": "2860494", "vector": [], "sparse_vector": [], "title": "Revealing Urban Dynamics by Learning Online and Offline Behaviours Together.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Urban problems and diseases accompanied by the pace of urbanization have drawn attention to the importance of understanding urban dynamics, while a deep and comprehensive understanding is challenging due to our diversified lifestyles in the modern city. In this paper, we propose an urban dynamics modeling system to characterize the regularity of urban activity dynamics as well as urban functions by learning residents' online and offline behaviours together. Built on a state-sharing hidden Markov model, our system utilizes online activities of App usage and offline activities of mobility in different urban regions and different time slots for learning. The learnt state sequence of each region reveals urban dynamics with the corresponding urban functions. We evaluate our system via a large-scale mobile network accessing dataset, which discovers ten hidden states characterizing different life modes and eight representative dynamic patterns corresponding to different urban functions. These discovered dynamic patterns and inferred functions are validated by social media check-ins and the land-use published by the government with 81% accuracy. Based on our model, we propose two applications, crowd flow prediction and popular App prediction, which outperforms the state-of-the-art approaches by 36.1% and 15.7%, respectively. This study paves the way for extensive city-related applications including urban demand analysis, land-use planning, and activity prediction.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314417"}, {"primary_key": "2860495", "vector": [], "sparse_vector": [], "title": "MilliBack: Real-Time Plug-n-Play Millimeter Level Tracking Using Wireless Backscattering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yubo Yan", "<PERSON><PERSON>"], "summary": "Real-time handwriting tracking is important for many emerging applications such as Artificial Intelligence assisted education and healthcare. Existing movement tracking systems, including those based on vision, ultrasound or wireless technologies, fail to offer high tracking accuracy, no learning/training/calibration process, low tracking latency, low cost and easy to deploy at the same time. In this work, we design and evaluate a wireless backscattering based handwriting tracking system, called MilliBack, that satisfies all these requirements. At the heart of MilliBack are two Phase Differential Iterative (PDI) schemes that can infer the position of the backscatter tag (which is attached to a writing tool) from the change in the signal phase. By adopting carefully-designed differential techniques in an iterative manner, we can take the diversity of devices out of the equation. The resulting position calculation has a linear complexity with the number of samples, ensuring fast and accurate tracking. We have put together a MilliBack prototype and conducted comprehensive experiments. We show that our system can track various handwriting traces accurately, in some testings it achieve a median error of 4.9 mm. We can accurately track and reconstruct arbitrary writing/drawing trajectories such as equations, Chinese characters or just random shapes. We also show that MilliBack can support relatively high writing speed and smoothly adapt to the changes of working environment.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351270"}, {"primary_key": "2860496", "vector": [], "sparse_vector": [], "title": "AcousticID: Gait-based Human Identification Using Acoustic Signal.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Human identification plays an important role in our daily lives. Previous studies have successfully used characteristics such as fingerprints, irises, and facial features for identity recognition. However, these methods require the user being close to the sensing device, which may cause inconvenience to users. In this paper, we present AcousticID, a system that uses fine-grained gait information derived from acoustic signals generated by Commercial Off-The-Shelf devices to identify human beings. We demonstrate the feasibility of gait recognition by analyzing the Doppler effect of various body parts on acoustic signals while walking, and then extract fine-grained gait features that can distinguish different people from both macro and micro dimensions. Similar to an access control system in the home or office, AcousticID is a convenient, low cost, and universal solution. We evaluate AcousticID using experiments with 50 volunteers in an area of 60 m2, and the results show that it can identify different persons with an average accuracy of 96.6%.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351273"}, {"primary_key": "2860497", "vector": [], "sparse_vector": [], "title": "AdaRF: Adaptive RFID-based Indoor Localization Using Deep Learning Enhanced Holography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Nowadays, RFID-based localization systems have been widely deployed in many factories and warehouses for sorting or locating products. These systems are mainly generalized schemes which might suffer severe accuracy degradation in multipath-rich scenarios. In order to suppress environmental interferences, we present a fine-grained RFID-based indoor localization system AdaRF, which leverages deep learning enhanced holography to create adaptive localization models for individual environments. The key idea is to optimize the localization model using signals from a small number of known location tags to achieve high positioning accuracy in its deployed environment. Based on this point, we propose Adjacent Differential Hologram (ADH) which yields a robust location-independent probability map for each tag. AdaRF subsequently leverages the neural network to create an effective hologram-based position estimation method, which estimates the target tag position by analyzing the whole hologram. And we introduce transfer learning technique to significantly lower training cost for the position estimation model while ensuring high accuracy simultaneously. Comparative experiments demonstrate AdaRF achieves cm-level positioning accuracy both in the lateral and radial direction with only one moving antenna even in complex scenarios.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351271"}, {"primary_key": "2860498", "vector": [], "sparse_vector": [], "title": "iVR: Integrated Vision and Radio Localization with Zero Human Effort.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smartphone localization is essential to a wide range of applications in shopping malls, museums, office buildings, and other public places. Existing solutions relying on radio fingerprints and/or inertial sensors suffer from large location errors and considerable deployment efforts. We observe an opportunity in the recent trend of increasing numbers of security surveillance cameras installed in indoor spaces to overcome these limitations and revisit the problem of smartphone localization with a fresh perspective. However, fusing vision-based and radio-based systems is non-trivial due to the absence of absolute location, incorrespondence of identification and looseness of sensor fusion. This study proposes iVR, an integrated vision and radio localization system that achieves sub-meter accuracy with indoor semantic maps automatically generated from only two surveillance cameras, superior to precedent systems that require manual map construction or plentiful captured images. iVR employs a particle filter to fuse raw estimates from multiple systems, including vision, radio, and inertial sensor systems. By doing so, iVR outputs enhanced accuracy with zero start-up costs, while overcoming the respective drawbacks of each individual sub-system. We implement iVR on commodity smartphones and validate its performance in five different scenarios. The results show that iVR achieves a remarkable localization accuracy of 0.7m, outperforming the state-of-the-art systems by &gt;70%.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351272"}, {"primary_key": "2860499", "vector": [], "sparse_vector": [], "title": "Leveraging Routine Behavior and Contextually-Filtered Features for Depression Detection among College Students.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Daniella K. <PERSON>ba", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The rate of depression in college students is rising, which is known to increase suicide risk, lower academic performance and double the likelihood of dropping out of school. Existing work on finding relationships between passively sensed behavior and depression, as well as detecting depression, mainly derives relevant unimodal features from a single sensor. However, co-occurrence of values in multiple sensors may provide better features, because such features can describe behavior in context. We present a new method to extract contextually filtered features from passively collected, time-series mobile data via association rule mining. After calculating traditional unimodal features from the data, we extract rules that relate unimodal features to each other using association rule mining. We extract rules from each class separately (e.g., depression vs. non-depression). We introduce a new metric to select a subset of rules that distinguish between the two classes. From these rules, which capture the relationship between multiple unimodal features, we automatically extract contextually filtered features. These features are then fed into a traditional machine learning pipeline to detect the class of interest (in our case, depression), defined by whether a student has a high BDI-II score at the end of the semester. The behavior rules generated by our methods are highly interpretable representations of differences between classes. Our best model uses contextually-filtered features to significantly outperform a standard model that uses only unimodal features, by an average of 9.7% across a variety of metrics. We further verified the generalizability of our approach on a second dataset, and achieved very similar results.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351274"}, {"primary_key": "2860500", "vector": [], "sparse_vector": [], "title": "ER-Rhythm: Coupling Exercise and Respiration Rhythm Using Lightweight COTS RFID.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The locomotor-respiratory coupling (LRC) ratio of a person doing exercise is an important parameter to reflect the exercise safety and effectiveness. Existing approaches that can measure LRC either rely on specialized and costly devices or use heavy sensors, bringing much inconvenience to people during exercise. To overcome these limitations, we propose ER-Rhythm using low-cost and lightweight RFID tags attached on the human body to simultaneously extract and couple the exercise and respiration rhythm for LRC estimation. ER-Rhythm captures exercise locomotion rhythm from the signals of the tags on limbs. However, extracting respiration rhythm from the signals of the tags on the chest during exercise is a challenging task because the minute respiration movement can be overwhelmed by the large torso movement. To address this challenge, we first leverage the unique characteristic of human respiratory mechanism to measure the chest movement while breathing, and then perform dedicated signal fusion of multiple tags interrogated by a pair of antennas to remove the torso movement effect. In addition, we take advantage of the multi-path effect of RF signals to reduce the number of needed antennas for respiration pattern extraction to save the system cost. To couple the exercise and respiration rhythm, we adopt a correlation-based approach to facilitate LRC estimation. The experimental results show that LRC can be estimated accurately up to 92% -- 95% of the time.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369808"}, {"primary_key": "2860501", "vector": [], "sparse_vector": [], "title": "Investigating Gesture Typing for Indirect Touch.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the development of ubiquitous computing, entering text on HMDs and smart TVs using handheld touchscreen devices (e.g., smartphone and controller) is becoming more and more attractive. In these indirect touch scenarios, the touch input surface is decoupled from the visual display. Compared with direct touch input, entering text using a keyboard in indirect touch is more challenging because before the finger touch, no visual feedback is available for locating the touch finger. Aiming at this problem, in this paper, we investigate the feasibility of gesture typing for indirect touch since keeping the finger in touch with the screen during typing makes it possible to provide continuous visual feedback, which is beneficial for increasing the input performance. We first examine users' gesture typing ability in terms of the appropriate keyboard size and location in motor space and then compare the typing performance in direct and indirect touch mode. We then propose an improved design to address the uncertainty and inaccuracy of the first touch. Our evaluation result shows that users can quickly acquire indirect gesture typing, and type 22.3 words per minute after 30 phases, which significantly outperforms previous numbers in literature. Our work provides the empirical support for leveraging gesture typing for indirect touch.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351275"}, {"primary_key": "2860502", "vector": [], "sparse_vector": [], "title": "ProxiTalk: Activate Speech Input by Bringing Smartphone to the Mouth.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Speech input, such as voice assistant and voice message, is an attractive interaction option for mobile users today. However, despite its popularity, there is a use limitation for smartphone speech input: users need to press a button or say a wake word to activate it before use, which is not very convenient. To address it, we match the motion that brings the phone to mouth with the user's intention to use voice input. In this paper, we present ProxiTalk, an interaction technique that allows users to enable smartphone speech input by simply moving it close to their mouths. We study how users use ProxiTalk and systematically investigate the recognition abilities of various data sources (e.g., using a front camera to detect facial features, using two microphones to estimate the distance between phone and mouth). Results show that it is feasible to utilize the smartphone's built-in sensors and instruments to detect ProxiTalk use and classify gestures. An evaluation study shows that users can quickly acquire ProxiTalk and are willing to use it. In conclusion, our work provides the empirical support that ProxiTalk is a practical and promising option to enable smartphone speech input, which coexists with current trigger mechanisms.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351276"}, {"primary_key": "2860504", "vector": [], "sparse_vector": [], "title": "Unsupervised Localization by Learning Transition Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Nowadays, it becomes very convenient to collect synchronized WiFi received signal strength and inertial measurement (RSS+IMU) sequences by mobile devices, which enables the promising solution to conduct unsupervised indoor localization without the pain of radio-map calibration. To relax the needs of floor-map information or trajectory knowledge, this paper proposes to learn a transitional model (TM), which segments the massive unlabeled sequences to train a model that captures the expected relationship between {zt--1, zt } and ut--1, where zt--1, zt are two consecutive signal states at t and t -- 1, and ut--1 is the one step motion calculated from inertial data. We present both a transitional model in signal space (TMS) and a transitional model to predict motion from signal change (TMM) to represent the relationship in different ways. In particular, from the massive sequences, both the signal states and the one step motion are smoothed from the nearest neighbours, so that the transition model learns the expected relative signal state change triggered by the smoothed one step motion. Its distinctive features are that (1) no external floor-map or trajectory knowledge is needed; (2) it can be continuously on-line refined as unlabeled sequences are incrementally collected. KALMAN filter based on-line mobile user location tracking methods are given for both models. Experiments show that the transition model based localization method provides comparable accuracy with the manually fingerprint calibration methods.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328936"}, {"primary_key": "2860506", "vector": [], "sparse_vector": [], "title": "ShoesLoc: In-Shoe Force Sensor-Based Indoor Walking Path Tracking.", "authors": ["<PERSON><PERSON>", "<PERSON>ming Jin", "<PERSON><PERSON><PERSON>"], "summary": "Currently, in-shoe force sensors have been widely used for step counting and gait analysis. However, it has not been realized that in-shoe force sensors are also capable of tracking walking paths. In this paper, we present ShoesLoc, an indoor walking path tracking method based on in-shoe force sensors. We show that, based on the force signals from a user's shoes, it is possible to estimate the walking direction change and the stride length of each step with machine learning techniques. We further apply a particle filter to combine this information with the constraint of barriers in floor maps, and thus can determine the walking path and the current position of the user. To solve the problem of the low accuracy caused by cumulative walking direction errors, we improve the particle filter by designing the direction correction algorithm. Moreover, we propose the weight normalization method to handle the impact of handbags and backpacks. Our experimental results show that, after a convergence phase, ShoesLoc achieves the average location error of 0.9-1.3 m. Compared with traditional indoor tracking technologies, ShoesLoc does not require the installation of wireless anchors, and has good robustness to environment changes such as the magnetic interference.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314418"}, {"primary_key": "2860507", "vector": [], "sparse_vector": [], "title": "ShoesHacker: Indoor Corridor Map and User Location Leakage through Force Sensors in Smart Shoes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The past few years have witnessed the rise of smart shoes, the wearable devices that measure foot force or track foot motion. However, people are not aware of the possible privacy leakage from in-shoe force sensors. In this paper, we explore the possibility of locating an indoor victim based on the force signals leaked from smart shoes. We present ShoesHacker, an attack scheme that reconstructs the corridor map of the building that the victim walks in based on force data only. The corridor map enables the attacker to recognize the building, and thus locate the victim on a global map. To handle the lack of training data, we design the stair landing detection algorithm, based on which we extract training data when victims are walking in stairwells. We estimate the trajectory of each walk, and propose the path merging algorithm to merge the trajectories. Moreover, we design a metric to quantify the similarity between corridor maps, which makes building recognition possible. Our experimental results show that, the building recognition accuracy reaches 77.5% in a 40-building dataset, and the victim can be located with an average error lower than 6 m, which reveals the danger of privacy leakage through smart shoes. CCS Concepts: • Information systems~Mobile information processing systems; Location based services; • Human-centered computing~Mobile devices; Ubiquitous and mobile computing systems and tools; • Security and privacy~Domain-specific security and privacy architectures.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351278"}, {"primary_key": "2860508", "vector": [], "sparse_vector": [], "title": "FarSense: Pushing the Range Limit of WiFi-based Respiration Sensing with CSI <PERSON>io of Two Antennas.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "The past few years have witnessed the great potential of exploiting channel state information retrieved from commodity WiFi devices for respiration monitoring. However, existing approaches only work when the target is close to the WiFi transceivers and the performance degrades significantly when the target is far away. On the other hand, most home environments only have one WiFi access point and it may not be located in the same room as the target. This sensing range constraint greatly limits the application of the proposed approaches in real life. This paper presents FarSense--the first real-time system that can reliably monitor human respiration when the target is far away from the WiFi transceiver pair. FarSense works well even when one of the transceivers is located in another room, moving a big step towards real-life deployment. We propose two novel schemes to achieve this goal: (1) Instead of applying the raw CSI readings of individual antenna for sensing, we employ the ratio of CSI readings from two antennas, whose noise is mostly canceled out by the division operation to significantly increase the sensing range; (2) The division operation further enables us to utilize the phase information which is not usable with one single antenna for sensing. The orthogonal amplitude and phase are elaborately combined to address the \"blind spots\" issue and further increase the sensing range. Extensive experiments show that FarSense is able to accurately monitor human respiration even when the target is 8 meters away from the transceiver pair, increasing the sensing range by more than 100%.1 We believe this is the first system to enable through-wall respiration sensing with commodity WiFi devices and the proposed method could also benefit other sensing applications.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351279"}, {"primary_key": "2860509", "vector": [], "sparse_vector": [], "title": "Fixing Mislabeling by Human Annotators Leveraging Conflict Resolution and Prior Knowledge.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "According to the \"human in the loop\" paradigm, machine learning algorithms can improve when leveraging on human intelligence, usually in the form of labels or annotation from domain experts. However, in the case of research areas such as ubiquitous computing or lifelong learning, where the annotator is not an expert and is continuously asked for feedback, humans can provide significant fractions of incorrect labels. We propose to address this issue in a series of experiments where students are asked to provide information about their behavior via a dedicated mobile application. Their trustworthiness is tested by employing an architecture where the machine uses all its available knowledge to check the correctness of its own and the user labeling to build a uniform confidence measure for both of them to be used when a contradiction arises. The overarching system runs through a series of modes with progressively higher confidence and features a conflict resolution component to settle the inconsistencies. The results are very promising and show the pervasiveness of annotation mistakes, the extreme diversity of the users' behaviors which provides evidence of the impracticality of a uniform fits-it-all solution, and the substantially improved performance of a skeptical supervised learning strategy.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314419"}, {"primary_key": "2860510", "vector": [], "sparse_vector": [], "title": "MegaLight: Learning-based Color Adaptation for Barcode Stream Recognition over Screen-Camera Links.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Screen-camera communication using dynamic barcode streaming has emerged as a convenient and secure method for short-range, impromptu device-to-device communication. Conventional dynamic barcode systems adopt a rule-based approach to recognize the color barcode stream in the receiver, which is empirical, inflexible, and lacks self-adaptiveness. In this paper, we propose a novel solution framework for color barcode stream recognition basing on machine learning techniques. By including a number of training frames into the barcode stream to build a classification model, the proposed framework can achieve high accuracy in color barcode recognition and is adaptive to different ambient lighting conditions (without sudden changes during transmission). A semi-supervised learning approach basing on the Mixture of Experts (MoE) model is further proposed to reduce the start-up time. We implement MegaLight on both black-white and color barcode systems. Extensive experiments demonstrate that MegaLight can significantly reduce the frame demodulation error and reach up to 3x improvement in system goodput comparing to conventional barcode stream recognition approaches.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328937"}, {"primary_key": "2860511", "vector": [], "sparse_vector": [], "title": "Facilitating Temporal Synchronous Target Selection through User Behavior Modeling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Temporal synchronous target selection is an association-free selection technique: users select a target by generating signals (e.g., finger taps and hand claps) in sync with its unique temporal pattern. However, classical pattern set design and input recognition algorithm of such techniques did not leverage users' behavioral information, which limits their robustness to imprecise inputs. In this paper, we improve these two key components by modeling users' interaction behavior. In the first user study, we asked users to tap a finger in sync with blinking patterns with various period and delay, and modeled their finger tapping ability using Gaussian distribution. Based on the results, we generated pattern sets for up to 22 targets that minimized the possibility of confusion due to imprecise inputs. In the second user study, we validated that the optimized pattern sets could reduce error rate from 23% to 7% for the classical Correlation recognizer. We also tested a novel Bayesian, which achieved higher selection accuracy than the Correlation recognizer when the input sequence is short. The informal evaluation results show that the selection technique can be effectively scaled to different modalities and sensing techniques.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369839"}, {"primary_key": "2860513", "vector": [], "sparse_vector": [], "title": "Route Prediction for Instant Delivery.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Instant delivery has drawn much attention recently, as it greatly facilitates people's daily lives. Unlike postal services, instant delivery imposes a strict deadline on couriers after a customer places an order online. Therefore it is critical to dispatch the order to an appropriate courier to guarantee the timely delivery. Ideally couriers should choose the optimal routes with the lowest overdue rate (i.e., the rate of the deliveries that are not finished in time) and the minimal distance. In practice, however, decision-making of the couriers is quite complex because individuals have different psychological perception of the environments (e.g., distance) and delivery requirements (e.g., deadline). To well predict their behaviors, we design multiple features to model the decision-making psychology of individual couriers and predict couriers' route with a machine learning algorithm. In particular, we reveal that perceived distance is the main factor influencing couriers' decision, which should be modeled based on the subjective understanding of the actual distances. Our design is implemented, deployed and evaluated on Ele.me, which is one of the largest instant delivery platforms in the world. Experimental results show that the overdue rate can be reduced by 48.02%, which is a significant improvement.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351282"}, {"primary_key": "2860514", "vector": [], "sparse_vector": [], "title": "Towards a Diffraction-based Sensing Approach on Human Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "In recent years, wireless sensing has been exploited as a promising research direction for contactless human activity recognition. However, one major issue hindering the real deployment of these systems is that the signal variation patterns induced by the human activities with different devices and environmental settings are neither stable nor consistent, resulting in unstable system performance. The existing machine learning based methods usually take the \"black box\" approach and fails to achieve consistent performance. In this paper, we argue that a deep understanding of radio signal propagation in wireless sensing is needed, and it may be possible to develop a deterministic sensing model to make the signal variation patterns predictable. With this intuition, in this paper we investigate: 1) how wireless signals are affected by human activities taking transceiver location and environment settings into consideration; 2) a new deterministic sensing approach to model the received signal variation patterns for different human activities; 3) a proof-of-concept prototype to demonstrate our approach and a case study to detect diverse activities. In particular, we propose a diffraction-based sensing model to quantitatively determine the signal change with respect to a target's motions, which eventually links signal variation patterns with motions, and hence can be used to recognize human activities. Through our case study, we demonstrate that the diffraction-based sensing model is effective and robust in recognizing exercises and daily activities. In addition, we demonstrate that the proposed model improves the recognition accuracy of existing machine learning systems by above 10%.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3314420"}, {"primary_key": "2860515", "vector": [], "sparse_vector": [], "title": "PDMove: Towards Passive Medication Adherence Monitoring of Parkinson&apos;s Disease Using Smartphone-based Gait Assessment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Huining Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The medicine adherence in Parkinson's disease (PD) treatment has attracted tremendous attention due to the critical consequences it can lead to otherwise. As a result, clinics need to ensure that the medicine intake is performed on time. Existing approaches, such as self-report, family reminder, and pill counts, heavily rely on the patients themselves to log the medicine intake (hereafter, patient involvement). Unfortunately, PD patients usually suffer from impaired cognition or memory loss, which leads to the so-called medication non-adherence, including missed doses, extra doses, and mistimed doses. These instances can nullify the treatment or even harm the patients. In this paper, we present PDMove, a smartphone-based passive sensing system to facilitate medication adherence monitoring without the need for patient involvement. Specifically, PDMove builds on the fact that PD patients will present gait abnormality if they do not follow medication treatment. To begin with, PDMove passively collects gait data while putting the smartphone in the pocket. Afterward, the gait preprocessor helps extract gait cycle containing the Parkinsonism-related biomarkers. Finally, the medicine intake detector consisting of a multi-view convolutional neural network predicts the medicine intake. In this way, PDMove enables the medication adherence monitoring. To evaluate PDMove, we enroll 247 participants with PD and collect more than 100,000 gait cycle samples. Our results show that smartphone-based gait assessment is a feasible approach to the AI-care strategy to monitor the medication adherence of PD patients.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351281"}, {"primary_key": "2860516", "vector": [], "sparse_vector": [], "title": "Learning to Recognize Unmodified Lights with Invisible Features.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shao<PERSON> Xu", "<PERSON><PERSON><PERSON>", "Huadong Ma"], "summary": "To enable accurate indoor localization at low cost, recent research in visible light positioning (VLP) proposed to employ existing ceiling lights as location landmarks, and use smartphone cameras or light sensors to identify the different lights using statistical visual/optical features. Despite the potential, we find such solutions are unreliable: the features are easily corrupted with a slight rotation of the smartphone, and are not discriminative enough for many practical light models with different size/shape/intensity. In this work, we propose Auto-Litell to resolve these critical challenges and make VLP truly robust. Auto-Litell builds a customized deep-learning neural network model to automatically distill the \"invisible\" visual features from the lights, which are resilient to smartphone orientation and light models. Moreover, Auto-Litell introduces a Light-CycleGAN to generate \"fake\" light images to augment the training data, so as to relieve human labors in data collection and labeling. We have implemented Auto-Litell as a real-time localization and navigation system on Android. Our experiments demonstrate Auto-Litell's high accuracy in discriminating the lights in the same building, and high reliability across a variety of practical usage scenarios.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3328938"}, {"primary_key": "2860517", "vector": [], "sparse_vector": [], "title": "CellTrans: Private Car or Public Transportation? Infer Users&apos; Main Transportation Modes at Urban Scale with Cellular Data.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Understanding citizens' main transportation modes at urban scale is beneficial to a range of applications, such as urban planning, user profiling, transportation management, and precision marketing. Previous methods on mode inference are mostly focused on utilizing GPS data with high spatiotemporal granularity. However, due to high costs of GPS data collection, the previous work typically is in small scales. In contrast, the cellular data logging interactions between cellphone users and cell towers cover much higher population given the ubiquity of cellphones. Nevertheless, utilizing cellular data introduces new challenges given their low spatiotemporal granularity compared to GPS data. In this paper, we design CellTrans, a novel framework to survey users' main transportation modes (public transportation or private car) at urban scale with cellular data. CellTrans extracts various mobility features that are pertinent to users' main transportation modes and presents solutions for different application scenarios including when there are no labeled users in the studied cities. We evaluate CellTrans on two real-world large-scale cellular datasets covering 3 million users, among which 2,589 users are with labels. We assess our method not only quantitatively with labeled users, but also qualitatively with the whole population. The experiments show that CellTrans infers users' main transportation modes with accuracy over 80% (with a performance gain of 20% compared to state-of-the-art), and CellTrans remains effective when applied at urban scale to the whole population.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351283"}, {"primary_key": "2860518", "vector": [], "sparse_vector": [], "title": "A Reliability-Aware Vehicular Crowdsensing System for Pothole Profiling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lu <PERSON>"], "summary": "Accurately profiling potholes on road surfaces not only helps eliminate safety related concerns and improve commuting efficiency for drivers, but also reduces unnecessary maintenance cost for transportation agencies. In this paper, we propose a smartphone-based system that is capable of precisely estimating the length and depth of potholes, and introduce a holistic design on pothole data collection, profile aggregation and pothole warning and reporting. The proposed system relies on the built-in inertial sensors of vehicle-carried smartphones to estimate pothole profiles, and warn the driver about incoming potholes. Because of the difference in driving behaviors and vehicle suspension systems, a major challenge in building such system is how to aggregate conflicting sensory reports from multiple participating vehicles. To tackle this challenge, we propose a novel reliability-aware data aggregation algorithm called Reliability Adaptive Truth Discovery (RATD). It infers the reliability for each data source and aggregates pothole profiles in an unsupervised fashion. Our field test shows that the proposed system can effectively estimate pothole profiles, and the RATD algorithm significantly improves the profiling accuracy compared with popular data aggregation methods.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3369815"}, {"primary_key": "2860520", "vector": [], "sparse_vector": [], "title": "Invisible QR Code Hijacking Using Smart LED.", "authors": ["<PERSON><PERSON>", "Guangyuan Su", "<PERSON><PERSON>", "Huadong Ma"], "summary": "Quick response (QR) codes have found versatile usage in numerous applications, but have also posed severe security threats such as privacy leakage, phishing and even payment inception if the codes are hijacked. The hijacking is often assumed to be preventable by physically isolating the codes from possible attackers, e.g., putting the QR code inside a glass cabinet distant to outsiders. In this paper, we explore a new QR code hijacking attack, named Li-Man, that can subvert such protection using smart LED. The key idea is to illuminate a target victim QR code from afar using specialized flickering light waveforms, which can transform the code to be any other predefined malicious ones when being captured by smart-phone cameras, while keeping the attack invisible to human visual perception. Li-Man builds on a modeling framework that harnesses the disparity between camera and human imaging mechanisms. We develop a Li-Man simulator and also implement a prototype to verify the feasibility and threat level of Li-Man. Experiments demonstrate that Li-Man can successfully realize the invisible hijacking of QR codes from multiple hidden positions in constrained space. On the other hand, we propose and verify a primary countermeasure that is promising to defeat the Li-Man attack.", "published": "2019-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3351284"}]