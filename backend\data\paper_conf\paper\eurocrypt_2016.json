[{"primary_key": "4093055", "vector": [], "sparse_vector": [], "title": "Lucky Microseconds: A Timing Attack on Amazon&apos;s s2n Implementation of TLS.", "authors": ["<PERSON>", "<PERSON>"], "summary": "s2n is an implementation of the TLS protocol that was released in late June 2015 by Amazon. It is implemented in around 6,000 lines of C99 code. By comparison, OpenSSL needs around 70,000 lines of code to implement the protocol. At the time of its release, Amazon announced that s2n had undergone three external security evaluations and penetration tests. We show that, despite this, s2n — as initially released — was vulnerable to a timing attack in the case of CBC-mode ciphersuites, which could be extended to complete plaintext recovery in some settings. Our attack has two components. The first part is a novel variant of the Lucky 13 attack that works even though protections against Lucky 13 were implemented in s2n. The second part deals with the randomised delays that were put in place in s2n as an additional countermeasure to Lucky 13. Our work highlights the challenges of protecting implementations against sophisticated timing attacks. It also illustrates that standard code audits are insufficient to uncover all cryptographic attack vectors.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_24"}, {"primary_key": "4093056", "vector": [], "sparse_vector": [], "title": "On the Complexity of Scrypt and Proofs of Space in the Parallel Random Oracle Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the time- and memory-complexities of the problem of computing labels of (multiple) randomly selected challenge-nodes in a directed acyclic graph. Thew-bit label of a node is the hash of the labels of its parents, and the hash function is modeled as a random oracle. Specific instances of this problem underlie both proofs of space [<PERSON><PERSON><PERSON><PERSON> et al. CRYPTO’15] as well as popular memory-hard functions likescrypt. As our main tool, we introduce the new notion of aprobabilistic parallel entangled pebbling game, a new type of combinatorial pebbling game on a graph, which is closely related to the labeling game on the same graph. As a first application of our framework, we prove that for\\(\\texttt {scrypt} \\), when the underlying hash function is invokedntimes, the cumulative memory complexity (CMC) (a notion recently introduced by <PERSON><PERSON> and <PERSON> (STOC’15) to capture amortized memory-hardness for parallel adversaries) is at least\\(\\varOmega (w \\cdot (n/\\log (n))^2)\\). This bound holds for adversaries that can store many natural functions of the labels (e.g., linear combinations), but still not arbitrary functions thereof. We then introduce and study a combinatorial quantity, and show how a sufficiently small upper bound on it (which we conjecture) extends our CMC bound for\\(\\texttt {scrypt} \\)to hold againstarbitraryadversaries. We also show that such an upper bound solves the main open problem for proofs-of-space protocols: namely, establishing that thetime complexityof computing the label of a random node in a graph onnnodes (given an initialkw-bit state) reduces tightly to the time complexity for black pebbling on the same graph (given an initialk-node pebbling).", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_13"}, {"primary_key": "4093057", "vector": [], "sparse_vector": [], "title": "Automated Unbounded Analysis of Cryptographic Constructions in the Generic Group Model.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We develop a new method to automatically prove security statements in the Generic Group Model as they occur in actual papers. We start by defining (i) a general language to describe security definitions, (ii) a class of logical formulas that characterize how an adversary can win, and (iii) a translation from security definitions to such formulas. We prove a Master Theorem that relates the security of the construction to the existence of a solution for the associated logical formulas. Moreover, we define a constraint solving algorithm that proves the security of a construction by proving the absence of solutions. We implement our approach in a fully automated tool, the\\(\\mathsf {gga}^{\\infty }\\)tool, and use it to verify different examples from the literature. The results improve on the tool by <PERSON><PERSON> et al. (CRYPTO’14, PKC’15): for many constructions,\\(\\mathsf {gga}^{\\infty }\\)succeeds in proving standard (unbounded) security, whereas <PERSON><PERSON>’s tool is only able to prove security for a small number of oracle queries.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_29"}, {"primary_key": "4093058", "vector": [], "sparse_vector": [], "title": "Circuit Compilers with O(1/\\log (n)) Leakage Rate.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The goal of leakage-resilient cryptography is to construct cryptographic algorithms that are secure even if the devices on which they are implemented leak information to the adversary. One of the main parameters for designing leakage resilient constructions is the leakagerate, i.e., a proportion between the amount of leaked information and the complexity of the computation carried out by the construction. We focus on the so-called circuit compilers, which is an important tool for transforming any cryptographic algorithm (represented as a circuit) into one that is secure against the leakage attack. Our model is the “probing attack” where the adversary learns the values on some (chosen by him) wires of the circuit. Our results can be summarized as follows. First, we construct circuit compilers with perfect security and leakage rate\\(O(1/\\log (n))\\), wherendenotes the security parameter (previously known constructions achieved rateO(1 /n)). Moreover, for the circuits that have only affine gates we obtain a construction with a constant leakage rate. In particular, our techniques can be used to obtain constant-rate leakage-resilient schemes for refreshing an encoded secret (previously known schemes could tolerate leakage ratesO(1 /n)). We also show that our main construction is secure against constant-rate leakage in the random probing leakage model, where the leaking wires are chosen randomly.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_21"}, {"primary_key": "4093059", "vector": [], "sparse_vector": [], "title": "Improved Progressive BKZ Algorithms and Their Precise Cost Estimation by Sharp Simulator.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we investigate a variant of the BKZ algorithm, called progressive BKZ, which performs BKZ reductions by starting with a small blocksize and gradually switching to larger blocks as the process continues. We discuss techniques to accelerate the speed of the progressive BKZ algorithm by optimizing the following parameters: blocksize, searching radius and probability for pruning of the local enumeration algorithm, and the constant in the geometric series assumption (GSA). We then propose a simulator for predicting the length of the Gram-Schmidt basis obtained from the BKZ reduction. We also present a model for estimating the computational cost of the proposed progressive BKZ by considering the efficient implementation of the local enumeration algorithm and the LLL algorithm. Finally, we compare the cost of the proposed progressive BKZ with that of other algorithms using instances from the Darmstadt SVP Challenge. The proposed algorithm is approximately 50 times faster than BKZ 2.0 (proposed by <PERSON><PERSON>) for solving the SVP Challenge up to 160 dimensions.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_30"}, {"primary_key": "4093060", "vector": [], "sparse_vector": [], "title": "On the Impossibility of Tight Cryptographic Reductions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The existence oftightreductions in cryptographic security proofs is an important question, motivated by the theoretical search for cryptosystems whose security guarantees are truly independent of adversarial behavior and the practical necessity of concrete security bounds for the theoretically-sound selection of cryptographic parameters. At Eurocrypt 2002, Coron described ameta-reductiontechnique that allows to prove theimpossibilityof tight reductions for certain digital signature schemes. This seminal result has found many further interesting applications. However, due to a technical subtlety in the argument, the applicability of this technique beyond digital signatures in thesingle-usersetting has turned out to be rather limited. We describe a new meta-reduction technique for proving such impossibility results, which improves on known ones in several ways. It enables interesting novel applications, including a formal proof that for certain cryptographic primitives (including public-key encryption/key encapsulation mechanisms and digital signatures), the security loss incurred when the primitive is transferred from an idealized single-user setting to the more realistic multi-user setting isimpossibleto avoid, and a lower tightness bound for non-interactive key exchange protocols. Moreover, the technique allows to rule out tight reductions from a very general class of non-interactive complexity assumptions. Furthermore, the proofs and bounds are simpler than in <PERSON><PERSON>’s technique and its extensions.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_10"}, {"primary_key": "4093061", "vector": [], "sparse_vector": [], "title": "Post-zeroizing Obfuscation: New Mathematical Tools, and the Case of Evasive Circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent devastating attacks by <PERSON><PERSON> et al. [Eurocrypt’15] and others have highlighted significant gaps in our intuition about security in candidate multilinear map schemes, and in candidate obfuscators that use them. The new attacks, and some that were previously known, are typically called “zeroizing” attacks because they all crucially rely on the ability of the adversary to create encodings of 0. In this work, we initiate the study ofpost-zeroizing obfuscation, and we obtain a key new mathematical tool to analyze security in a post-zeroizing world. Our new mathematical tool allows for analyzing polynomials constructed by the adversary when given encodings of randomized matrices arising from a general matrix branching program. This technique shows that the types of encodings an adversary can create are much more restricted than was previously known, and is a crucial step toward achieving post-zeroizing security. We also believe the technique is of independent interest, as it yields efficiency improvements for existing schemes – efficiency improvements that have already found application in other settings. Finally, we show how to apply our new mathematical tool to the special case of evasive functions. We show that our obfuscator survivesall known attackson the underlying multilinear maps, by proving that no top-level encodings of 0 can be created by a generic-model adversary. Previous obfuscators (for both evasive and general functions) were either analyzed in a less-conservative “pre-zeroizing” model thatdoes notcapture recent attacks, or were proved secure relative to assumptions that no longer have any plausible instantiation due to zeroizing attacks.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_27"}, {"primary_key": "4093062", "vector": [], "sparse_vector": [], "title": "Non-malleable Codes for Bounded Depth, Bounded Fan-In Circuits.", "authors": ["<PERSON>", "<PERSON>-<PERSON>ed", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show how to construct efficient, unconditionally secure non-malleable codes for bounded output locality. In particular, our scheme is resilient against functions such that any output bit is dependent on at most\\(n^{\\delta }\\)bits, wherenis the total number of bits in a codeword and\\(0\\le \\delta < 1\\)a constant. Notably, this tampering class includes\\(\\mathsf {NC}^0\\).", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_31"}, {"primary_key": "4093063", "vector": [], "sparse_vector": [], "title": "Randomness Complexity of Private Circuits for Multiplication.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many cryptographic algorithms are vulnerable to side channel analysis and several leakage models have been introduced to better understand these flaws. In 2003, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> introduced thed-probing security model, in which an attacker can observe at mostdintermediate values during a processing. They also proposed an algorithm that securely performs the multiplication of 2 bits in this model, using only\\(d(d+1)/2\\)random bits to protect the computation. We study the randomness complexity of multiplication algorithms secure in thed-probing model. We propose several contributions: we provide new theoretical characterizations and constructions, new practical constructions and a new efficient algorithmic tool to analyze the security of such schemes. We start with a theoretical treatment of the subject: we propose an algebraic model for multiplication algorithms and exhibit an algebraic characterization of the security in thed-probing model. Using this characterization, we prove a linear (ind) lower bound and a quasi-linear (non-constructive) upper bound for this randomness cost. Then, we construct a new generic algorithm to perform secure multiplication in thed-probing model that only uses\\(d + d^2/4\\)random bits. From a practical point of view, we consider the important cases\\(d \\le 4\\)that are actually used in current real-life implementations and we build algorithms with a randomness complexity matching our theoretical lower bound for these small-order cases. Finally, still using our algebraic characterization, we provide a new dedicated verification tool, based on information set decoding, which aims at finding attacks on algorithms for fixed orderdat a very low computational cost.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_22"}, {"primary_key": "4093064", "vector": [], "sparse_vector": [], "title": "Hash-Function Based PRFs: AMAC and Its Multi-User Security.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "\\(\\mathsf {AMAC}\\)is a simple and fast candidate construction of a PRF from an MD-style hash function which applies the keyed hash function and then a cheap, un-keyed output transform such as truncation. Spurred by its use in the widely-deployed Ed25519 signature scheme, this paper investigates the provable PRF security of\\(\\mathsf {AMAC}\\)to deliver the following three-fold message: (1) First, we prove PRF security of\\(\\mathsf {AMAC}\\). (2) Second, we show that\\(\\mathsf {AMAC}\\)has a quite unique and attractive feature, namely that its multi-user security is essentially as good as its single-user security and in particular superior in some settings to that of competitors. (3) Third, it is technically interesting, its security and analysis intrinsically linked to security of the compression function in the presence of leakage.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_22"}, {"primary_key": "4093065", "vector": [], "sparse_vector": [], "title": "New Negative Results on Differing-Inputs Obfuscation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We provide the following negative results for differing-inputs obfuscation (diO): (1) If sub-exponentially secure one-way functions exist then sub-exponentially secure diO for TMs does not exist (2) If in addition sub-exponentially secure iO exists then polynomially secure diO for TMs does not exist.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_28"}, {"primary_key": "4093066", "vector": [], "sparse_vector": [], "title": "Nonce-Based Cryptography: Retaining Security When Randomness Fails.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We take nonce-based cryptography beyond symmetric encryption, developing it as a broad and practical way to mitigate damage caused by failures in randomness, whether inadvertent (bugs) or malicious (subversion). We focus on definitions and constructions for nonce-based public-key encryption and briefly treat nonce-based signatures. We introduce and construct hedged extractors as a general tool in this domain. Our nonce-based PKE scheme guarantees that if the adversary wants to violate IND-CCA security then it must dobothof the following: (1) fully compromise the RNG (2) penetrate the sender system to exfiltrate a seed used by the sender.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_28"}, {"primary_key": "4093067", "vector": [], "sparse_vector": [], "title": "Reverse-Engineering the S-Box of Streebog, Kuznyechik and STRIBOBr1.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ek<PERSON>"], "summary": "The Russian Federation’s standardization agency has recently published a hash function called Stree<PERSON>g and a 128-bit block cipher called Kuznyechik. Both of these algorithms use the same 8-bit S-Box but its design rationale was never made public. In this paper, we reverse-engineer this S-Box and reveal its hidden structure. It is based on a sort of 2-round Feistel Network where exclusive-or is replaced by a finite field multiplication. This structure is hidden by two different linear layers applied before and after. In total, five different 4-bit S-Boxes, a multiplexer, two 8-bit linear permutations and two finite field multiplications in a field of size\\(2^{4}\\)are needed to compute the S-Box. The knowledge of this decomposition allows a much more efficient hardware implementation by dividing the area and the delay by 2.5 and 8 respectively. However, the small 4-bit S-Boxes do not have very good cryptographic properties. In fact, one of them has a probability 1 differential. We then generalize the method we used to partially recover the linear layers used to whiten the core of this S-Box and illustrate it with a generic decomposition attack against 4-round Feistel Networks whitened with unknown linear layers. Our attack exploits a particular pattern arising in the Linear Approximations Table of such functions.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_15"}, {"primary_key": "4093068", "vector": [], "sparse_vector": [], "title": "Essentially Optimal Robust Secret Sharing with Maximal Corruptions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In at-out-of-nrobust secret sharing scheme, a secret message is shared amongnparties who can reconstruct the message by combining their shares. An adversary can adaptively corrupt up totof the parties, get their shares, and modify them arbitrarily. The scheme should satisfyprivacy, meaning that the adversary cannot learn anything about the shared message, androbustness, meaning that the adversary cannot cause the reconstruction procedure to output an incorrect message. Such schemes are only possible in the case of an honest majority, and here we focus on unconditional security in the maximal corruption setting where\\(n = 2t+1\\). In this scenario, to share anm-bit message with a reconstruction failure probability of at most\\(2^{-k}\\), a known lower-bound shows that the share size must be at least\\(m + k\\)bits. On the other hand, all prior constructions have share size that scales linearly with the number of partiesn, and the prior state-of-the-art scheme due to <PERSON><PERSON> et al. (EUROCRYPT ’12) achieves\\(m + \\widetilde{O}(k + n)\\). In this work, we construct the first robust secret sharing scheme in the maximal corruption setting with\\(n=2t+1\\), that avoids the linear dependence between share size and the number of partiesn. In particular, we get a share size of only\\(m + \\widetilde{O}(k)\\)bits. Our scheme is computationally efficient and relies on approximation algorithms for the minimum graph bisection problem.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_3"}, {"primary_key": "4093069", "vector": [], "sparse_vector": [], "title": "Efficient Zero-Knowledge Arguments for Arithmetic Circuits in the Discrete Log Setting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We provide a zero-knowledge argument for arithmetic circuit satisfiability with a communication complexity that grows logarithmically in the size of the circuit. The round complexity is also logarithmic and for an arithmetic circuit with fan-in 2 gates the computation of the prover and verifier is linear in the size of the circuit. The soundness of our argument relies solely on the well-established discrete logarithm assumption in prime order groups. At the heart of our new argument system is an efficient zero-knowledge argument of knowledge of openings of two Pedersen multicommitments satisfying an inner product relation, which is of independent interest. The inner product argument requires logarithmic communication, logarithmic interaction and linear computation for both the prover and the verifier. We also develop a scheme to commit to a polynomial and later reveal the evaluation at an arbitrary point, in a verifiable manner. This is used to build an optimized version of the constant round square root complexity argument of Groth (CRYPTO 2009), which reduces both communication and round complexity.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_12"}, {"primary_key": "4093070", "vector": [], "sparse_vector": [], "title": "Multi-input Functional Encryption in the Private-Key Setting: Stronger Security from Weaker Assumptions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We construct a general-purposemulti-inputfunctional encryption scheme in the private-key setting. Namely, we construct a scheme where a functional key corresponding to a functionfenables a user holding encryptions of\\(x_1, \\ldots , x_t\\)to compute\\(f(x_1, \\ldots , x_t)\\)but nothing else. This is achieved starting from any general-purpose private-keysingle-inputscheme (without any additional assumptions), and is proven to beadaptively securefor any constant number of inputst. Moreover, it can be extended to a super-constant number of inputs assuming that the underlying single-input scheme is sub-exponentially secure. Instantiating our construction with existing single-input schemes, we obtain multi-input schemes that are based on a variety of assumptions (such as indistinguishability obfuscation, multilinear maps, learning with errors, and even one-way functions), offering various trade-offs between security and efficiency. Previous and concurrent constructions of multi-input functional encryption schemes either rely on stronger assumptions and provided weaker security guarantees (<PERSON><PERSON> et al. [EUROCRYPT ’14], and <PERSON><PERSON><PERSON> and <PERSON> [CRYPTO ’15]), or relied on multilinear maps and could be proven secure only in an idealized generic model (<PERSON><PERSON> et al. [EUROCRYPT ’15]). In comparison, we present a general transformation that simultaneously relies on weaker assumptions and guarantees stronger security.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_30"}, {"primary_key": "4093071", "vector": [], "sparse_vector": [], "title": "Safely Exporting Keys from Secure Channels - On the Security of EAP-TLS and TLS Key Exporters.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Douglas <PERSON>"], "summary": "We investigate how to safely export additional cryptographic keys from secure channel protocols, modelled with theauthenticated and confidential channel establishment (ACCE)security notion. For example, the EAP-TLS protocol uses the Transport Layer Security (TLS) handshake to output an additional shared secret which can be used for purposes outside of TLS, and the RFC 5705 standard specifies a general mechanism for exporting keying material from TLS. We show that, for a class of ACCE protocols we call “TLS-like” protocols, the EAP-TLS transformation can be used to export an additional key, and that the result is a secure AKE protocol in the Bellare–Rogaway model. Interestingly, we are able to carry out the proof without looking at the specifics of the TLS protocol itself (beyond the notion that it is “TLS-like”), but rather are able to use the ACCE property in a semi black-box way. To facilitate our modular proof, we develop a novel technique, notably an encryption-based key checking mechanism that is used by the security reduction. Our results imply that EAP-TLS using secure TLS 1.2 ciphersuites is a secure authenticated key exchange protocol.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_26"}, {"primary_key": "4093072", "vector": [], "sparse_vector": [], "title": "Reusable Fuzzy Extractors for Low-Entropy Distributions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fuzzy extractors (<PERSON><PERSON> et al., <PERSON>crypt 2004) convert repeated noisy readings of a secret into the same uniformly distributed key. To eliminate noise, they require an initial enrollment phase that takes the first noisy reading of the secret and produces a nonsecret helper string to be used in subsequent readings.Reusablefuzzy extractors (<PERSON><PERSON>, CCS 2004) remain secure even when this initial enrollment phase is repeated multiple times with noisy versions of the same secret, producing multiple helper strings (for example, when a single person’s biometric is enrolled with multiple unrelated organizations). We construct the first reusable fuzzy extractor that makes no assumptions about how multiple readings of the source are correlated (the only prior construction assumed a very specific, unrealistic class of correlations). The extractor works for binary strings with Hamming noise; it achieves computational security under assumptions on the security of hash functions or in the random oracle model. It is simple and efficient and tolerates near-linear error rates. Our reusable extractor is secure for source distributions of linear min-entropy rate. The construction is also secure for sources with much lower entropy rates—lower than those supported by prior (nonreusable) constructions—assuming that the distribution has some additional structure, namely, that random subsequences of the source have sufficient minentropy. We show that such structural assumptions are necessary to support low entropy rates. We then explore further how different structural properties of a noisy source can be used to construct fuzzy extractors when the error rates are high, building a computationally secure and an information-theoretically secure construction for large-alphabet sources.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_5"}, {"primary_key": "4093073", "vector": [], "sparse_vector": [], "title": "Provably Weak Instances of Ring-LWE Revisited.", "authors": ["W<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In CRYPTO 2015, <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> described an attack on the non-dualdecisionversion of the ring learning with errors problem (RLWE) for two special families of defining polynomials, whose constructiondepends on the modulusqthat is being used. For particularly chosen error parameters, they managed to solve non-dual decision RLWE given 20 samples, with a success rate ranging from 10 % to 80 %. In this paper we show how to solve thesearchversion for the same families and error parameters, using only 7 samples with a success rate of 100 %. Moreover our attack works forevery modulus\\(q'\\)instead of theqthat was used to construct the defining polynomial. The attack is based on the observation that the RLWE error distribution for these families of polynomials is very skewed in the directions of the polynomial basis. For the parameters chosen by <PERSON> et al. the smallest errors are negligible and simple linear algebra suffices to recover the secret. But enlarging the error paremeters makes the largest errors wrap around, thereby turning the RLWE problem unsuitable for cryptographic applications. These observations also apply to dual RLWE, but do not contradict the seminal work by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_6"}, {"primary_key": "4093074", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of the New CLT Multilinear Map over the Integers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multilinear maps serve as a basis for a wide range of cryptographic applications. The first candidate construction of multilinear maps was proposed by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> in 2013, and soon afterwards, another construction was suggested by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (CLT13), which works over the integers. However, both of these were found to be insecure in the face of so-called zeroizing attacks, by <PERSON> and <PERSON><PERSON>, and by <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>. To improve on CLT13, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> proposed another candidate construction of multilinear maps over the integers atCrypto 2015(CLT15). This article presents two polynomial attacks on the CLT15 multilinear map, which share ideas similar to the cryptanalysis of CLT13. Our attacks allow recovery of all secret parameters in time polynomial in the security parameter, and lead to a full break of the CLT15 multilinear map for virtually all applications.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_20"}, {"primary_key": "4093075", "vector": [], "sparse_vector": [], "title": "Online/Offline OR Composition of Sigma Protocols.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Proofs of partial knowledge allow a prover to prove knowledge of witnesses forkout ofninstances ofNPlanguages. <PERSON><PERSON><PERSON>, Schoenmakers and Damgård [10] provided an efficient construction of a 3-round public-coin witness-indistinguishable (k,n)-proof of partial knowledge for anyNPlanguage, by cleverly combiningnexecutions of\\(\\varSigma \\)-protocols for that language. This transform assumes that allninstances are fully specified before the proof starts, and thus directly rules out the possibility of choosing some of the instances after the first round. Very recently, <PERSON><PERSON><PERSON> et al. [6] provided an improved transform where one of the instances can be specified in the last round. They focus on (1, 2)-proofs of partial knowledge with the additional feature that one instance is defined in the last round, and could beadaptivelychosen by the verifier. They left as an open question the existence of an efficient (1, 2)-proof of partial knowledge where no instance is known in the first round. More in general, they left open the question of constructing an efficient (k,n)-proof of partial knowledge where knowledge ofallninstances can be postponed. Indeed, this property is achieved only by inefficient constructions requiringNPreductions [19]. In this paper we focus on the question of achievingadaptive-inputproofs of partial knowledge. We provide through a transform the first efficient construction of a 3-round public-coin witness-indistinguishable (k,n)-proof of partial knowledge whereallinstances can be decided in the third round. Our construction enjoysadaptive-inputwitness indistinguishability. Additionally, the proof of knowledge property remains also if the adversarial prover selects instances adaptively at last round as long as our transform is applied to a proof of knowledge belonging to the widely used class of proofs of knowledge described in [9,21]. Since knowledge of instances and witnesses is not needed before the last round, we have that the first round can be precomputed and in the online/offline setting our performance is similar to the one of [10]. Our new transform relies on the DDH assumption (in contrast to the transforms of [6,10] that are unconditional).", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_3"}, {"primary_key": "4093076", "vector": [], "sparse_vector": [], "title": "Recovering Short Generators of Principal Ideals in Cyclotomic Rings.", "authors": ["<PERSON>", "Léo Du<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A handful of recent cryptographic proposals rely on the conjectured hardness of the following problem in the ring of integers of a cyclotomic number field: given a basis of a principal ideal that is guaranteed to have a “rather short” generator, find such a generator. Recently, <PERSON> and <PERSON><PERSON><PERSON> sketched potential attacks against this problem; most notably, the latter authors claimed apolynomial-time quantumalgorithm. (Alternatively, replacing the quantum component with an algorithm of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> would yield aclassical subexponential-timealgorithm.) A key claim of <PERSON><PERSON> al.is that one step of their algorithm—namely, decoding thelog-unitlattice of the ring to recover a short generator from an arbitrary one—is classically efficient (whereas the standard approach on general lattices takes exponential time). However, very few convincing details were provided to substantiate this claim. In this work, we clarify the situation by giving a rigorous proof that the log-unit lattice is indeed efficiently decodable, for any cyclotomic of prime-power index. Combining this with the quantum algorithm from a recent work of <PERSON><PERSON><PERSON> and <PERSON> confirms the main claim of <PERSON><PERSON> al.Our proof consists of two main technical contributions: the first is a geometrical analysis, using tools from analytic number theory, of the standard generators of the group of cyclotomic units. The second showsthat for a wide class of typical distributions of the short generator, a standard lattice-decoding algorithm can recover it, given any generator. By extending our geometrical analysis, as a second main contribution we obtain an efficient algorithm that, given any generator of a principal ideal (in a prime-power cyclotomic), finds a\\(2^{\\tilde{O}(\\sqrt{n})}\\)-approximate shortest vector in the ideal. Combining this with the result of Biasse and Song yields a quantum polynomial-time algorithm for the\\(2^{\\tilde{O}(\\sqrt{n})}\\)-approximate Shortest Vector Problem on principal ideal lattices.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_20"}, {"primary_key": "4093077", "vector": [], "sparse_vector": [], "title": "10-Round Fe<PERSON><PERSON> is Indifferentiable from an Ideal Cipher.", "authors": ["<PERSON>-<PERSON>ed", "<PERSON>", "Aishwarya Thiruvengadam"], "summary": "We revisit the question of constructing an ideal cipher from a random oracle. <PERSON><PERSON> et al. (Journal of Cryptology, 2014) proved that a 14-round Feistel network using random, independent, keyed round functions is indifferentiable from an ideal cipher, thus demonstrating the feasibility of such a transformation. Left unresolved is the number of rounds of a Feistel network that are needed in order for indifferentiability to hold. We improve upon the result of <PERSON><PERSON> et al. and show that 10 rounds suffice.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_23"}, {"primary_key": "4093078", "vector": [], "sparse_vector": [], "title": "Unconditionally Secure Computation with Reduced Interaction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the question of how much interaction is needed for unconditionally secure multiparty computation. We first consider the number of messages that need to be sent to compute a Boolean function with semi-honest security, where allnparties learn the result. We consider two classes of functions calledt-difficult andt-very difficult functions, wheretrefers to the number of corrupted players. For instance, the AND of an input bit from each player ist-very difficult while the XOR ist-difficult but nott-very difficult. We show lower bounds on the message complexity of both types of functions, considering two notions of message complexity called conservative and liberal, where conservative is the more standard one. In all cases the bounds are\\(\\varOmega (nt)\\). We also show (almost) matching upper bounds for\\(t=1\\)and functions in a rich class\\(PSM_\\mathsf{eff}\\)including non-deterministic log-space, as well as a stronger upper bound for the XOR function. In particular, we find that the conservative message complexity of 1-very difficult functions in\\(PSM_\\mathsf{eff}\\)is 2n, while the conservative message complexity for XOR (and\\(t=1\\)) is\\(2n-1\\). Next, we consider round complexity. It is a long-standing open problem to determine whether all efficiently computable functions can also be efficiently computed in constant-round withunconditionalsecurity. Motivated by this, we consider the question of whether we can compute any function securely, while minimizing the interaction ofsome ofthe players? And if so, how many players can this apply to? Note that we still want the standard security guarantees (correctness, privacy, termination) and we consider the standard communication model with secure point-to-point channels. We answer the questions as follows: for passive security, with\\(n=2t+1\\)players andtcorruptions, up totplayers can have minimal interaction, i.e., they send 1 message in the first round to each of the\\(t+1\\)remaining players and receive one message from each of them in the last round. Using our result on message complexity, we show that this is (unconditionally) optimal. For malicious security with\\(n=3t+1\\)players andtcorruptions, up totplayers can have minimal interaction, and we show that this is also optimal.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_15"}, {"primary_key": "4093079", "vector": [], "sparse_vector": [], "title": "Constrained Pseudorandom Functions for Unconstrained Inputs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Venkata Koppula", "<PERSON>"], "summary": "A constrained pseudo random function (PRF) behaves like a standard PRF, but with the added feature that the (master) secret key holder, having secret keyK, can produce a constrained key,\\(K\\{f\\}\\), that allows for the evaluation of the PRF on all inputs satisfied by the constraintf. Most existing constrained PRF constructions can handle only bounded length inputs. In a recent work, <PERSON><PERSON><PERSON> et al. [1] constructed a constrained PRF scheme where constraints can be represented as Turing machines with unbounded inputs. Their proof of security, however, requires risky “knowledge type” assumptions such as differing inputs obfuscation for circuits and SNARKs. In this work, we construct a constrained PRF scheme for Turing machines with unbounded inputs under weaker assumptions, namely, the existence of indistinguishability obfuscation for circuits (and injective pseudorandom generators).", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_5"}, {"primary_key": "4093080", "vector": [], "sparse_vector": [], "title": "New Attacks on the Concatenation and XOR Hash Combiners.", "authors": ["<PERSON><PERSON>"], "summary": "We study the security of the concatenation combiner\\(H_1(M) \\Vert H_2(M)\\)for two independent iterated hash functions withn-bit outputs that are built using the Merkle-Damgård construction. In 2004 <PERSON><PERSON> showed that the concatenation combiner of hash functions with ann-bit internal state does not offer better collision and preimage resistance compared to a single strongn-bit hash function. On the other hand, the problem of devising second preimage attacks faster than\\(2^n\\)against this combiner has remained open since 2005 when <PERSON> and <PERSON> showed that a single Merkle-Damgård hash function does not offer optimal second preimage resistance for long messages. In this paper, we develop new algorithms for cryptanalysis of hash combiners and use them to devise the first second preimage attack on the concatenation combiner. The attack finds second preimages faster than\\(2^n\\)for messages longer than\\(2^{2n/7}\\)and has optimal complexity of\\(2^{3n/4}\\). This shows that the concatenation of two Merkle-Damgård hash functions is not as strong a single ideal hash function. Our methods are also applicable to other well-studied combiners, and we use them to devise a new preimage attack with complexity of\\(2^{2n/3}\\)on the XOR combiner\\(H_1(M) \\oplus H_2(M)\\)of two Merkle-Damgård hash functions. This improves upon the attack by <PERSON><PERSON>t and <PERSON> (presented at Eurocrypt 2015) whose complexity is\\(2^{5n/6}\\)(but unlike our attack is also applicable to HAIFA hash functions). Our algorithms exploit properties of random mappings generated by fixing the message block input to the compression functions of\\(H_1\\)and\\(H_2\\). Such random mappings have been widely used in cryptanalysis, but we exploit them in new ways to attack hash function combiners.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_19"}, {"primary_key": "4093081", "vector": [], "sparse_vector": [], "title": "Indifferentiability of Confusion-Diffusion Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show the first positive results for the indifferentiability security of the confusion-diffusion networks (which are extensively used in the design of block ciphers and hash functions). In particular, our result shows that a constant number of confusion-diffusion rounds is sufficient to extend the domain of a public random permutation.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_24"}, {"primary_key": "4093082", "vector": [], "sparse_vector": [], "title": "Sanitization of FHE Ciphertexts.", "authors": ["Léo Du<PERSON>", "<PERSON>"], "summary": "By definition, fully homomorphic encryption (FHE) schemes support homomorphic decryption, and all known FHE constructions are bootstrapped from a Somewhat Homomorphic Encryption (SHE) scheme via this technique. Additionally, when a public key is provided, ciphertexts are also re-randomizable, e.g., by adding to them fresh encryptions of 0. From those two operations we devise an algorithm to sanitize a ciphertext, by making its distribution canonical. In particular, the distribution of the ciphertext does not depend on the circuit that led to it via homomorphic evaluation, thus providing circuit privacy in the honest-but-curious model. Unlike the previous approach based on noise flooding, our approach does not degrade much the security/efficiency trade-off of the underlying FHE. The technique can be applied to all lattice-based FHE proposed so far, without substantially affecting their concrete parameters.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_12"}, {"primary_key": "4093083", "vector": [], "sparse_vector": [], "title": "From Improved Leakage Detection to the Detection of Points of Interests in Leakage Traces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Leakage detection usually refers to the task of identifying data-dependent information in side-channel measurements, independent of whether this information can be exploited. Detecting Points-Of-Interest (POIs) in leakage traces is a complementary task that is a necessary first step in most side-channel attacks, where the adversary wants to turn this information into (e.g.) a key recovery. In this paper, we discuss the differences between these tasks, by investigating a popular solution to leakage detection based on a t-test, and an alternative method exploiting Pearson’s correlation coefficient. We first show that the simpler t-test has better sampling complexity, and that its gain over the correlation-based test can be predicted by looking at the Signal-to-Noise Ratio (SNR) of the leakage partitions used in these tests. This implies that the sampling complexity of both tests relates more to their implicit leakage assumptions than to the actual statistics exploited. We also put forward that this gain comes at the cost of some intuition loss regarding the localization of the exploitable leakage samples in the traces, and their informativeness. Next, and more importantly, we highlight that our reasoning based on the SNR allows defining an improved t-test with significantly faster detection speed (with approximately 5 times less measurements in our experiments), which is therefore highly relevant for evaluation laboratories. We finally conclude that whereas t-tests are the method of choice for leakage detection only, correlation-based tests exploiting larger partitions are preferable for detecting POIs. We confirm this intuition by improving automated tools for the detection of POIs in the leakage measurements of a masked implementation, in a black box manner and without key knowledge, thanks to a correlation-based leakage detection test.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_10"}, {"primary_key": "4093084", "vector": [], "sparse_vector": [], "title": "On the Composition of Two-Prover Commitments, and Applications to Multi-round Relativistic Commitments.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the related notions oftwo-proverand ofrelativisticcommitment schemes. In recent work, <PERSON><PERSON><PERSON><PERSON> <PERSON>. proposed a new relativistic commitment scheme with amulti-round sustain phasethat keeps the binding property alive as long as the sustain phase is running. They prove security of their scheme against classical attacks; however, the proven bound on the error parameter is very weak: it blows updouble exponentiallyin the number of rounds. In this work, we give a new analysis of the multi-round scheme of <PERSON><PERSON><PERSON><PERSON> al., and we show alineargrowth of the error parameter instead (also considering classical attacks only). Our analysis is based on a newcomposition theoremfor two-prover commitment schemes. The proof of our composition theorem is based on a better understanding of the binding property of two-prover commitments that we provide in the form of new definitions and relations among them. As an additional consequence of these new insights, our analysis is actually with respect to a strictlystrongernotion of security than considered by <PERSON><PERSON><PERSON><PERSON> al.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_17"}, {"primary_key": "4093085", "vector": [], "sparse_vector": [], "title": "Structural Lattice Reduction: Generalized Worst-Case to Average-Case Reductions and Homomorphic Cryptosystems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In lattice cryptography, worst-case to average-case reductions rely on two problems: <PERSON><PERSON><PERSON><PERSON>s S<PERSON> and <PERSON><PERSON>’s LWE, which both refer to a very small class of random lattices related to the group\\(G=\\mathbb {Z}_q^n\\). We generalize worst-case to average-case reductions to all integer lattices of sufficiently large determinant, by allowing<PERSON>to be any (sufficiently large) finite abelian group. Our main tool is a novel generalization of lattice reduction, which we call structural lattice reduction: given a finite abelian groupGand a latticeL, it finds a short basis of some lattice\\(\\bar{L}\\)such that\\(L \\subseteq \\bar{L}\\)and\\(\\bar{L}/L \\simeq G\\). Our group generalizations of SIS and LWE allow us to abstract lattice cryptography, yet preserve worst-case assumptions: as an illustration, we provide a somewhat conceptually simpler generalization of the <PERSON><PERSON><PERSON> variant of the Gentry-Sahai-Waters homomorphic scheme. We introduce homomorphic mux gates, which allows us to homomorphically evaluate any boolean function with a noise overhead proportional to the square root of its number of variables, and bootstrap the full scheme using only a linear noise overhead.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_19"}, {"primary_key": "4093086", "vector": [], "sparse_vector": [], "title": "The Exact Round Complexity of Secure Computation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>"], "summary": "We revisit theexactround complexity of secure computation in the multi-party and two-party settings. For the special case of two-partieswithouta simultaneous message exchange channel, this question has been extensively studied and resolved. In particular, <PERSON> and <PERSON><PERSON> (CRYPTO ’04) proved that 5 rounds are necessary and sufficient for securely realizing every two-party functionality where both parties receive the output. However, the exact round complexity of general multi-party computation, as well as two-party computationwitha simultaneous message exchange channel, is not very well understood. These questions are intimately connected to the round complexity of non-malleable commitments. Indeed, theexactrelationship between the round complexities of non-malleable commitments and secure multi-party computation has also not been explored. In this work, we revisit these questions and obtain several new results. First, we establish the following main results. Suppose that there exists ak-round non-malleable commitment scheme, and let\\(k'=\\max (4,k+1)\\); then, (Two-party setting with simultaneous message transmission):there exists a\\(k'\\)-round protocol for securely realizingeverytwo-party functionality; (Multi-party setting):there exists a\\(k'\\)-round protocol for securely realizing themulti-party coin-flippingfunctionality. As a corollary of the above results, by instantiating them with existing non-malleable commitment protocols (from the literature), we establish thatfourrounds are both necessary and sufficient for both the results above. Furthermore, we establish that, forevery multi-party functionalityfiverounds are sufficient. We actually obtain a variety of results offering trade-offs between rounds and the cryptographic assumptions used, depending upon the particular instantiations of underlying protocols.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_16"}, {"primary_key": "4093087", "vector": [], "sparse_vector": [], "title": "Tightly CCA-Secure Encryption Without Pairings.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We present the first CCA-secure public-key encryption scheme based on DDH where the security loss is independent of the number of challenge ciphertexts and the number of decryption queries. Our construction extends also to the standardk-\\(\\mathsf {Lin} \\)assumption in pairing-free groups, whereas all prior constructions starting with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (Crypto ’12) rely on the use of pairings. Moreover, our construction improves upon the concrete efficiency of existing schemes, reducing the ciphertext overhead by about half (to only\\(3\\)group elements under DDH), in addition to eliminating the use of pairings. We also show how to use our techniques in the NIZK setting. Specifically, we construct the first tightly simulation-sound designated-verifier NIZK for linear languages without pairings. Using pairings, we can turn our construction into a highly optimized publicly verifiable NIZK with tight simulation-soundness.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_1"}, {"primary_key": "4093088", "vector": [], "sparse_vector": [], "title": "Provably Robust Sponge-Based PRNGs and KDFs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study the problem of devising provably secure PRNGs with input based on the sponge paradigm. Such constructions are very appealing, as efficient software/hardware implementations of SHA-3 can easily be translated into a PRNG in a nearly black-box way. The only existing sponge-based construction, proposed by <PERSON><PERSON><PERSON> <PERSON>.(CHES 2010), fails to achieve the security notion of robustness recently considered by Dodiset al.(CCS 2013), for two reasons: (1) The construction is deterministic, and thus there are high-entropy input distributions on which the construction fails to extract random bits, and (2) The construction is not forward secure, and presented solutions aiming at restoring forward security have not been rigorously analyzed. We propose aseededvariant of Bert<PERSON>et al.’s PRNG with input which we prove secure in the sense of robustness, delivering in particular concrete security bounds. On the way, we make what we believe to be an important conceptual contribution, developing a variant of the security framework of Dodiset al.tailored at the ideal permutation model that captures PRNG security in settings where the weakly random inputs are provided from a large class of possible adversarial samplerswhich are also allowed to query the random permutation. As a further application of our techniques, we also present an efficient sponge-based key-derivation function (which can be instantiated from SHA-3 in a black-box fashion), which we also prove secure when fed with samples from permutation-dependent distributions.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_4"}, {"primary_key": "4093089", "vector": [], "sparse_vector": [], "title": "Improved Masking for Tweakable Blockciphers with Applications to Authenticated Encryption.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A popular approach to tweakable blockcipher design is via masking, where a certain primitive (a blockcipher or a permutation) is preceded and followed by an easy-to-compute tweak-dependent mask. In this work, we revisit the principle of masking. We do so alongside the introduction of the tweakable Even-Mansour construction\\(\\mathsf {MEM}\\). Its masking function combines the advantages of word-oriented LFSR- and powering-up-based methods. We show in particular how recent advancements in computing discrete logarithms over finite fields of characteristic 2 can be exploited in a constructive way to realize highly efficient, constant-time masking functions. If the masking satisfies a set of simple conditions, then\\(\\mathsf {MEM}\\)is a secure tweakable blockcipher up to the birthday bound. The strengths of\\(\\mathsf {MEM}\\)are exhibited by the design of fully parallelizable authenticated encryption schemes\\(\\mathsf {OPP}\\)(nonce-respecting) and\\(\\mathsf {MRO}\\)(misuse-resistant). If instantiated with a reduced-round BLAKE2b permutation,\\(\\mathsf {OPP}\\)and\\(\\mathsf {MRO}\\)achieve speeds up to 0.55 and 1.06 cycles per byte on the Intel Haswell microarchitecture, and are able to significantly outperform their closest competitors.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_11"}, {"primary_key": "4093090", "vector": [], "sparse_vector": [], "title": "On the Size of Pairing-Based Non-interactive Arguments.", "authors": ["<PERSON><PERSON>"], "summary": "Non-interactive arguments enable a prover to convince a verifier that a statement is true. Recently there has been a lot of progress both in theory and practice on constructing highly efficient non-interactive arguments with small size and low verification complexity, so-called succinct non-interactive arguments (SNARGs) and succinct non-interactive arguments of knowledge (SNARKs). Many constructions of SNARGs rely on pairing-based cryptography. In these constructions a proof consists of a number of group elements and the verification consists of checking a number of pairing product equations. The question we address in this article is how efficient pairing-based SNARGs can be. Our first contribution is a pairing-based (preprocessing) SNARK for arithmetic circuit satisfiability, which is an NP-complete language. In our SNARK we work with asymmetric pairings for higher efficiency, a proof is only 3 group elements, and verification consists of checking a single pairing product equations using 3 pairings in total. Our SNARK is zero-knowledge and does not reveal anything about the witness the prover uses to make the proof. As our second contribution we answer an open question of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (TCC 2013) by showing that linear interactive proofs cannot have a linear decision procedure. It follows from this that SNARGs where the prover and verifier use generic asymmetric bilinear group operations cannot consist of a single group element. This gives the first lower bound for pairing-based SNARGs. It remains an intriguing open problem whether this lower bound can be extended to rule out 2 group element SNARGs, which would prove optimality of our 3 element construction.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_11"}, {"primary_key": "4093091", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of GGH Map.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multilinear map is a novel primitive which has many cryptographic applications, and GGH map is a major candidate ofK-linear maps for\\(K>2\\). GGH map has two classes of applications, which are applications with public tools for encoding and with hidden tools for encoding. In this paper, we show that applications of GGH map with public tools for encoding are not secure, and that one application of GGH map with hidden tools for encoding is not secure. On the basis of weak-DL attack presented by the authors themselves, we present several efficient attacks on GGH map, aiming at multipartite key exchange (MKE) and the instance of witness encryption (WE) based on the hardness of exact-3-cover (X3C) problem. First, we use special modular operations, which we call modified Encoding/zero-testing to drastically reduce the noise. Such reduction is enough to break MKE. Moreover, such reduction negatesK-GMDDH assumption, which is a basic security assumption. The procedure involves mostly simple algebraic manipulations, and rarely needs to use any lattice-reduction tools. The key point is our special tools for modular operations. Second, under the condition of public tools for encoding, we break the instance of WE based on the hardness of X3C problem. To do so, we not only use modified Encoding/zero-testing, but also introduce and solve “combined X3C problem”, which is a problem that is not difficult to solve. In contrast with the assumption that multilinear map cannot be divided back, this attack includes a division operation, that is, solving an equivalent secret from a linear equation modular some principal ideal. The quotient (the equivalent secret) is not small, so that modified Encoding/zero-testing is needed to reduce size. This attack is under an assumption that some two vectors are co-prime, which seems to be plausible. Third, for hidden tools for encoding, we break the instance of WE based on the hardness of X3C problem. To do so, we construct level-2 encodings of 0, which are used as alternative tools for encoding. Then, we break the scheme by applying modified Encoding/zero-testing and combined X3C, where the modified Encoding/zero-testing is an extended version. This attack is under two assumptions, which seem to be plausible. Finally, we present cryptanalysis of two simple revisions of GGH map, aiming at MKE. We show that MKE on these two revisions can be broken under the assumption that\\(2^{K}\\)is polynomially large. To do so, we further extend our modified Encoding/zero-testing.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_21"}, {"primary_key": "4093092", "vector": [], "sparse_vector": [], "title": "Honey Encryption Beyond Message Recovery Security.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>par<PERSON> introduced honey encryption (HE) and showed how to achieve message recovery security even in the face of attacks that can exhaustively try all likely keys. This is important in contexts like password-based encryption where keys are very low entropy, and HE schemes based on the JR construction were subsequently proposed for use in password management systems and even long-term protection of genetic data. But message recovery security is in this setting, like previous ones, a relatively weak property, and in particular does not prohibit an attacker from learning partial information about plaintexts or from usefully mauling ciphertexts. We show that one can build HE schemes that can hide partial information about plaintexts and that prevent mauling even in the face of exhaustive brute force attacks. To do so, we introduce target-distribution semantic-security and target-distribution non-malleability security notions. We prove that a slight variant of the JR HE construction can meet them. The proofs require new balls-and-bins type analyses significantly different from those used in prior work. Finally, we provide a formal proof of the folklore result that an unbounded adversary which obtains a limited number of encryptions of known plaintexts can always succeed at message recovery.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_29"}, {"primary_key": "4093093", "vector": [], "sparse_vector": [], "title": "All Complete Functionalities are Reversible.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, in 1991, posed the question of reversibility of functionalities, that is, which functionalities when used in one direction, could securely implement the identical functionality in the reverse direction. <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, in 2006, showed that oblivious transfer is reversible. We study the problem of reversibility among 2-party SFE functionalities, which also enable general multi-party computation, in the information-theoretic setting. We show that any functionality that enables general multi-party computation, when used in both directions, is reversible. In fact, we show that any such functionality can securely realize oblivious transfer when used in an a priori fixed direction. This result enables secure computation using physical setups that parties can only use in a particular direction due to inherent asymmetries in them.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_8"}, {"primary_key": "4093094", "vector": [], "sparse_vector": [], "title": "Secure Computation from Elastic Noisy Channels.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Noisy channels enable unconditionally secure multi-party computation even against parties with unbounded computational power. But inaccurate noise estimation and adversarially determined channel characteristics render known protocols insecure. Such channels are known as unreliable noisy channels. A large body of work in the last three decades has attempted to construct secure multi-party computation from unreliable noisy channels, but this previous work has not been able to deal with most parameter settings. In this work, we study a form of unreliable noisy channels where the unreliability is one-sided, that we nameelasticnoisy channels: thus, in one form of elastic noisy channel, an adversarial receiver can increase the reception reliability unbeknown to the sender, but the sender cannot change the channel characteristic. Our work shows feasibility results for a large set of parameters for the elastic binary symmetric channel, significantly improving upon the best results obtainable using prior techniques. In a key departure from existing approaches, we use a more elemental correlated private randomness as an intermediate cryptographic primitive that exhibits only a rudimentary essence of oblivious transfer. Toward this direction, we introduce new information-theoretic techniques that are potentially applicable to other cryptographic settings involving unreliable noisy channels.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_7"}, {"primary_key": "4093095", "vector": [], "sparse_vector": [], "title": "Fair and Robust Multi-party Computation Using a Global Transaction Ledger.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Classical results on secure multi-party computation (MPC) imply that fully secure computation, including fairness (either all parties get output or none) and robustness (output delivery is guaranteed), is impossible unless a majority of the parties is honest. Recently, cryptocurrencies like Bitcoin where utilized to leverage the fairness loss in MPC against a dishonest majority. The idea is that when the protocol aborts in an unfair manner (i.e., after the adversary receives output) then honest parties get compensated by the adversarially controlled parties. Our contribution is three-fold. First, we put forth a new formal model of secure MPC with compensation and show how the introduction of suitable ledger and synchronization functionalities makes it possible to describe such protocols using standard interactive Turing machines (ITM) circumventing the need for the use of extra features that are outside the standard model as in previous works. Second, our model, is expressed in the universal composition setting with global setup and is equipped with a composition theorem that enables the design of protocols that compose safely with each other and within larger environments where other protocols with compensation take place; a composition theorem for MPC protocols with compensation was not known before. Third, we introduce the first robust MPC protocol with compensation, i.e., an MPC protocol where not only fairness is guaranteed (via compensation) but additionally the protocol is guaranteed to deliver output to the parties that get engaged and therefore the adversary, after an initial round of deposits, is not even able to mount a denial of service attack without having to suffer a monetary penalty. Importantly, our robust MPC protocol requires only aconstantnumber of (coin-transfer and communication) rounds.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_25"}, {"primary_key": "4093096", "vector": [], "sparse_vector": [], "title": "Valiant&apos;s Universal Circuit is Practical.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Universal circuits (UCs) can be programmed to evaluate any circuit of a given sizek. They provide elegant solutions in various application scenarios, e.g. for private function evaluation (PFE) and for improving the flexibility of attribute-based encryption (ABE) schemes. The optimal size of a universal circuit is proven to be\\(\\varOmega (k\\log k)\\). Valiant (STOC’76) proposed a size-optimized UC construction, which has not been put in practice ever since. The only implementation of universal circuits was provided by <PERSON><PERSON><PERSON> and Schneider (FC’08), with size\\(\\mathcal {O}(k\\log ^2 k)\\). In this paper, we refine the size of Valiant’s UC and further improve the construction by (at least) 2k. We show that due to recent optimizations and our improvements, it is the best solution to apply in the case for circuits with a constant number of inputs and outputs. When the number of inputs or outputs is linear in the number of gates, we propose a more efficient hybrid solution based on the two existing constructions. We validate the practicality of Valiant’s UC, by giving an example implementation for PFE using these size-optimized UCs.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_27"}, {"primary_key": "4093097", "vector": [], "sparse_vector": [], "title": "Constant-Round Leakage-Resilient Zero-Knowledge from Collision Resistance.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We construct a constant-round leakage-resilient zero-knowledge argument system under the existence of collision-resistant hash function family. That is, using collision-resistant hash functions, we construct a constant-round zero-knowledge argument system such that for any cheating verifier that can obtain arbitrary amount of leakage of the prover’s state, there exists a simulator that can simulate the adversary’s view by obtaining at most the same amount of leakage of the witness. Previously, leakage-resilient zero-knowledge protocols were constructed only under a relaxed security definition (<PERSON><PERSON><PERSON><PERSON><PERSON>, CRYPTO’11) or under the DDH assumption (<PERSON><PERSON><PERSON>, TCC’14). Our leakage-resilient zero-knowledge argument system satisfies an additional property that it is simultaneously leakage-resilient zero-knowledge, meaning that both zero-knowledgeness and soundness hold in the presence of leakage.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_4"}, {"primary_key": "4093098", "vector": [], "sparse_vector": [], "title": "Improved Differential-Linear Cryptanalysis of 7-Round Chaskey with Partitioning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work we study the security of <PERSON><PERSON><PERSON>, a recent lightweight MAC designed by <PERSON><PERSON><PERSON><PERSON>., currently being considered for standardization by ISO/IEC and ITU-T. <PERSON><PERSON><PERSON> uses an ARX structure very similar to SipHash. We present the first cryptanalysis of <PERSON><PERSON><PERSON> in the single user setting, with a differential-linear attack against 6 and 7 rounds, hinting that the full version of <PERSON><PERSON><PERSON> with 8 rounds has a rather small security margin. In response to these attacks, a 12-round version has been proposed by the designers. To improve the complexity of the differential-linear cryptanalysis, we refine a partitioning technique recently proposed by <PERSON><PERSON><PERSON> and <PERSON><PERSON> to improve the linear cryptanalysis of addition operations. We also propose an analogue improvement of differential cryptanalysis of addition operations. Roughly speaking, these techniques reduce the data complexity of linear and differential attacks, at the cost of more processing time per data. It can be seen as the analogue for ARX ciphers of partial key guess and partial decryption for SBox-based ciphers. When applied to the differential-linear attack against <PERSON><PERSON><PERSON>, this partitioning technique greatly reduces the data complexity, and this also results in a reduced time complexity. While a basic differential-linear attack on 7 round takes\\(2^{78}\\)data and time (respectively\\(2^{35}\\)for 6 rounds), the improved attack requires only\\(2^{48}\\)data and\\(2^{67}\\)time (respectively\\(2^{25}\\)data and\\(2^{29}\\)time for 6 rounds). We also show an application of the partitioning technique to FEAL-8X, and we hope that this technique will lead to a better understanding of the security of ARX designs.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_14"}, {"primary_key": "4093099", "vector": [], "sparse_vector": [], "title": "Zero-Knowledge Arguments for Lattice-Based Accumulators: Logarithmic-Size Ring Signatures and Group Signatures Without Trapdoors.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "San Ling", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "An accumulator is a function that hashes a set of inputs into a short, constant-size string while preserving the ability to efficiently prove the inclusion of a specific input element in the hashed set. It has proved useful in the design of numerous privacy-enhancing protocols, in order to handle revocation or simply prove set membership. In the lattice setting, currently known instantiations of the primitive are based on Merkle trees, which do not interact well with zero-knowledge proofs. In order to efficiently prove the membership of some element in a zero-knowledge manner, the prover has to demonstrate knowledge of a hash chain without revealing it, which is not known to be efficiently possible under well-studied hardness assumptions. In this paper, we provide an efficient method of proving such statements using involved extensions of <PERSON>’s protocol. Under the Small Integer Solution assumption, we provide zero-knowledge arguments showing possession of a hash chain. As an application, we describe new lattice-based group and ring signatures in the random oracle model. In particular, we obtain: (i) The first lattice-based ring signatures with logarithmic size in the cardinality of the ring; (ii) The first lattice-based group signature that does not require any GPV trapdoor and thus allows for a much more efficient choice of parameters.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_1"}, {"primary_key": "4093100", "vector": [], "sparse_vector": [], "title": "Indistinguishability Obfuscation from Constant-Degree Graded Encoding Schemes.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We construct an indistinguishability obfuscation (IO) scheme for all polynomial-size circuits fromconstant-degreegraded encoding schemes, assuming the existence of a subexponentially secure pseudo-random generator computable by constant-degree arithmetic circuits, and the subexponential hardness of the Learning With Errors (LWE) problems. Previously, all candidate general purpose IO schemes rely on polynomial-degree graded encoding schemes.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_2"}, {"primary_key": "4093101", "vector": [], "sparse_vector": [], "title": "On the Influence of Message Length in PMAC&apos;s Security Bounds.", "authors": ["Atul Luykx", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many MAC (Message Authentication Code) algorithms have security bounds which degrade linearly with the message length. Often there are attacks that confirm the linear dependence on the message length, yet PMAC has remained without attacks. Our results show that PMAC’s message length dependence in security bounds is non-trivial. We start by studying a generalization of PMAC in order to focus on PMAC’s basic structure. By abstracting away details, we are able to show that there are two possibilities: either there are infinitely many instantiations of generic PMAC with security bounds independent of the message length, or finding an attack against generic PMAC which establishes message length dependence is computationally hard. The latter statement relies on a conjecture on the difficulty of finding subsets of a finite field summing to zero or satisfying a binary quadratic form. Using the insights gained from studying PMAC’s basic structure, we then shift our attention to the original instantiation of PMAC, namely, with Gray codes. Despite the initial results on generic PMAC, we show that PMAC with Gray codes is one of the more insecure instantiations of PMAC, by illustrating an attack which roughly establishes a linear dependence on the message length.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_23"}, {"primary_key": "4093102", "vector": [], "sparse_vector": [], "title": "On the Power of Hierarchical Identity-Based Encryption.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We prove that there is no fully black-box construction of collision-resistant hash functions (CRH) from hierarchical identity-based encryption (HIBE) with arbitrary polynomial number of identity levels. To the best of our knowledge this is the first limitation proved for HIBE. As a corollary, we obtain a series of separations that are not directly about HIBE or CRH but are interesting on their own right. Namely, we show that primitives such as IBE and CCA-secure public-key encryption cannot be used in a black-box way to construct fully homomorphic encryption or any primitive that implies CRH in a black-box way. Our proof relies on the reconstruction paradigm of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (FOCS 2000) and <PERSON><PERSON><PERSON> et al. (FOCS 2007) and extends their techniques for one-way and trapdoor permutations to the setting of HIBE. A main technical challenge in the proof of our separation stems from theadaptivityof the HIBE adversary who is allowed to obtain keys for different identitiesbefo<PERSON><PERSON> selects the attacked identity. Our main technical contribution is to develop compression/reconstruction techniques that can be achieved relative to such adaptive attackers.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_9"}, {"primary_key": "4093103", "vector": [], "sparse_vector": [], "title": "Towards Stream Ciphers for Efficient FHE with Low-Noise Ciphertexts.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Symmetric ciphers purposed for Fully Homomorphic Encryption (FHE) have recently been proposed for two main reasons. First, minimizing the implementation (time and memory) overheads that are inherent to current FHE schemes. Second, improving the homomorphic capacity,i.e.the amount of operations that one can perform on homomorphic ciphertexts before bootstrapping, which amounts to limit their level of noise. Existing solutions for this purpose suggest a gap between block ciphers and stream ciphers. The first ones typically allow a constant but small homomorphic capacity, due to the iteration of rounds eventually leading to complex Boolean functions (hence large noise). The second ones typically allow a larger homomorphic capacity for the first ciphertext blocks, that decreases with the number of ciphertext blocks (due to the increasing Boolean complexity of the stream ciphers’ output). In this paper, we aim to combine the best of these two worlds, and propose a new stream cipher construction that allows constant and small(er) noise. Its main idea is to apply a Boolean (filter) function to a public bit permutation of a constant key register, so that the Boolean complexity of the stream cipher outputs is constant. We also propose an instantiation of the filter function designed to exploit recent (3rd-generation) FHE schemes, where the error growth is quasi-additive when adequately multiplying ciphertexts with the same amount of noise. In order to stimulate further investigation, we then specify a few instances of this stream cipher, for which we provide a preliminary security analysis. We finally highlight the good properties of our stream cipher regarding the other goal of minimizing the time and memory complexity of calculus delegation (for 2nd-generation FHE schemes). We conclude the paper with open problems related to the large design space opened by these new constructions.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_13"}, {"primary_key": "4093104", "vector": [], "sparse_vector": [], "title": "Practical, Predictable La<PERSON><PERSON> Reduction.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Lattice reduction algorithms are notoriously hard to predict, both in terms of running time and output quality, which poses a major problem for cryptanalysis. While easy to analyze algorithms with good worst-case behavior exist, previous experimental evidence suggests that they are outperformed in practice by algorithms whose behavior is still not well understood, despite more than 30 years of intensive research. This has lead to a situation where a rather complex simulation procedure seems to be the most common way to predict the result of their application to an instance. In this work we present new algorithmic ideas towards bridging this gap between theory and practice. We report on an extensive experimental study of several lattice reduction algorithms, both novel and from the literature, that shows that theoretical algorithms are in fact surprisingly practical and competitive. In light of our results we come to the conclusion that in order to predict lattice reduction, simulation is superfluous and can be replaced by a closed formula using weaker assumptions. One key technique to achieving this goal is a novel algorithm to solve the Shortest Vector Problem (SVP) in the dual without computing the dual basis. Our algorithm enjoys the same practical efficiency as the corresponding primal algorithm and can be easily added to an existing implementation of it.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_31"}, {"primary_key": "4093105", "vector": [], "sparse_vector": [], "title": "Two Round Multiparty Computation via Multi-key FHE.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We construct a general multiparty computation (MPC) protocol with only two rounds of interaction in the common random string model, which is known to be optimal. In the honest-but-curious setting we only rely on the learning with errors (LWE) assumption, and in the fully malicious setting we additionally assume the existence of non-interactive zero knowledge arguments (NIZKs). Previously, <PERSON><PERSON><PERSON> et al. (EUROCRYPT ’12) showed how to achieve three rounds based on LWE and NIZKs, while <PERSON><PERSON><PERSON> et al. (TCC ’14) showed how to achieve the optimal two rounds based on indistinguishability obfuscation, but it was unknown if two rounds were possible under standard assumptions without obfuscation. Our approach relies onmulti-key fully homomorphic encryption (MFHE), introduced by <PERSON><PERSON><PERSON> et al. (STOC ’12), which enables homomorphic computation over data encrypted under different keys. We present a construction of MFHE based on LWE that significantly simplifies a recent scheme of Clear and McGoldrick (CRYPTO ’15). We then extend this construction to allow for a one-round distributed decryption of a multi-key ciphertext. Our entire MPC protocol consists of the following two rounds: Each party individually encrypts its input under its own key and broadcasts the ciphertext. All parties can then homomorphically compute a multi-key encryption of the output. Each party broadcasts a partial decryption of the output using its secret key. The partial decryptions can be combined to recover the output in plaintext.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_26"}, {"primary_key": "4093106", "vector": [], "sparse_vector": [], "title": "Anonymous Traitor Tracing: How to Embed Arbitrary Information in a Key.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In a traitor tracing scheme, each user is given a different decryption key. A content distributor can encrypt digital content using a public encryption key and each user in the system can decrypt it using her decryption key. Even if a coalition of users combines their decryption keys and constructs some “pirate decoder” that is capable of decrypting the content, there is a public tracing algorithm that is guaranteed to recover the identity of at least one of the users in the coalition given black-box access to such decoder. In prior solutions, the users are indexed by numbers\\(1,\\ldots ,N\\)and the tracing algorithm recovers the indexiof a user in a coalition. Such solutions implicitly require the content distributor to keep a record that associates each indexiwith the actual identifying information for the corresponding user (e.g., name, address, etc.) in order to ensure accountability. In this work, we construct traitor tracing schemes where all of the identifying information about the user can be embedded directly into the user’s key and recovered by the tracing algorithm. In particular, the content distributor does not need to separately store any records about the users of the system, and honest users can even remain anonymous to the content distributor. The main technical difficulty comes in designing tracing algorithms that can handle an exponentially large universe of possible identities, rather than just a polynomial set of indices\\(i \\in [N]\\). We solve this by abstracting out an interesting algorithmic problem that has surprising connections with seemingly unrelated areas in cryptography. We also extend our solution to a full “broadcast-trace-and-revoke” scheme in which the traced users can subsequently be revoked from the system. Depending on parameters, some of our schemes can be based only on the existence of public-key encryption while others rely on indistinguishability obfuscation.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_14"}, {"primary_key": "4093107", "vector": [], "sparse_vector": [], "title": "Complete Addition Formulas for Prime Order Elliptic Curves.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An elliptic curve addition law is said to becompleteif it correctly computes the sum ofanytwo points in the elliptic curve group. One of the main reasons for the increased popularity of Edwards curves in the ECC community is that they can allow a complete group law that is also relatively efficient (e.g., when compared to all known addition laws on Edwards curves). Such complete addition formulas can simplify the task of an ECC implementer and, at the same time, can greatly reduce the potential vulnerabilities of a cryptosystem. Unfortunately, until now, complete addition laws that are relatively efficient have only been proposed on curves of composite order and have thus been incompatible with all of the currently standardized prime order curves. In this paper we present optimized addition formulas that are complete oneveryprime order short Weierstrass curve defined over a fieldkwith\\(\\mathrm{char}(k) \\ne 2,3\\). Compared to their incomplete counterparts, these formulas require a larger number of field additions, but interestingly require fewer field multiplications. We discuss how these formulas can be used to achieve secure, exception-free implementations onallof the prime order curves in the NIST (and many other) standards.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_16"}, {"primary_key": "4093108", "vector": [], "sparse_vector": [], "title": "New Complexity Trade-Offs for the (Multiple) Number Field Sieve Algorithm in Non-Prime Fields.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The selection of polynomials to represent number fields crucially determines the efficiency of the Number Field Sieve (NFS) algorithm for solving the discrete logarithm in a finite field. An important recent work due to <PERSON><PERSON> et al. builds upon existing works to propose two new methods for polynomial selection when the target field is a non-prime field. These methods are called the generalised Jo<PERSON>-Lercier (GJL) and the Conjugation methods. In this work, we propose a new method (which we denote as\\(\\mathcal {A}\\)) for polynomial selection for the NFS algorithm in fields\\(\\mathbb {F}_{Q}\\), with\\(Q=p^n\\)and\\(n>1\\). The new method both subsumes and generalises the GJL and the Conjugation methods and provides new trade-offs for bothncomposite andnprime. Let us denote the variant of the (multiple) NFS algorithm using the polynomial selection method “X” by (M)NFS-X. Asymptotic analysis is performed for both the NFS-\\(\\mathcal {A}\\)and the MNFS-\\(\\mathcal {A}\\)algorithms. In particular, when\\(p=L_Q(2/3,c_p)\\), for\\(c_p\\in [3.39,20.91]\\), the complexity of NFS-\\(\\mathcal {A}\\)is better than the complexities of all previous algorithms whether classical or MNFS. The MNFS-\\(\\mathcal {A}\\)algorithm provides lower complexity compared to NFS-\\(\\mathcal {A}\\)algorithm; for\\(c_p\\in (0, 1.12] \\cup [1.45,3.15]\\), the complexity of MNFS-\\(\\mathcal {A}\\)is the same as that of the MNFS-Conjugation and for\\(c_p\\notin (0, 1.12] \\cup [1.45,3.15]\\), the complexity of MNFS-\\(\\mathcal {A}\\)is lower than that of all previous methods.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_17"}, {"primary_key": "4093109", "vector": [], "sparse_vector": [], "title": "Freestart Collision for Full SHA-1.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This article presents an explicit freestart colliding pair forSHA-1,i.e.a collision for its internal compression function. This is the first practical break of the fullSHA-1, reaching all 80 out of 80 steps. Only 10 days of computation on a 64-GPU cluster were necessary to perform this attack, for a runtime cost equivalent to approximately\\(2^{57.5}\\)calls to the compression function ofSHA-1on GPU. This work builds on a continuous series of cryptanalytic advancements onSHA-1since the theoretical collision attack breakthrough of 2005. In particular, we reuse the recent work on 76-stepSHA-1of Karpmanet al.from CRYPTO 2015 that introduced an efficient framework to implement (freestart) collisions on GPUs; we extend it by incorporating more sophisticated accelerating techniques such as boomerangs. We also rely on the results of <PERSON> from EUROCRYPT 2013 to obtain optimal attack conditions; using these techniques required further refinements for this work. Freestart collisions do not directly imply a collision for the full hash function. However, this work is an important milestone towards an actualSHA-1collision and it further shows how GPUs can be used very efficiently for this kind of attack. Based on the state-of-the-art collision attack onSHA-1by Stevens from EUROCRYPT 2013, we are able to present new projections on the computational and financial cost required for aSHA-1collision computation. These projections are significantly lower than what was previously anticipated by the industry, due to the use of the more cost efficient GPUs compared to regular CPUs. We therefore recommend the industry, in particular Internet browser vendors and Certification Authorities, to retractSHA-1quickly. We hope the industry has learned from the events surrounding the cryptanalytic breaks ofMD5and will retractSHA-1before concrete attacks such as signature forgeries appear in the near future.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_18"}, {"primary_key": "4093110", "vector": [], "sparse_vector": [], "title": "An Analysis of OpenSSL&apos;s Random Number Generator.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In this work we demonstrate various weaknesses of the random number generator (RNG) in the OpenSSL cryptographic library. We show how OpenSSL’s RNG, knowingly in a low entropy state, potentially leaks low entropy secrets in its output, which were never intentionally fed to the RNG by client code, thus posing vulnerabilities even when in the given usage scenario the low entropy state is respected by the client application. Turning to the core cryptographic functionality of the RNG, we show how OpenSSL’s functionality for adding entropy to the RNG state fails to be effectively a mixing function. If an initial low entropy state of the RNG was falsely presumed to have 256 bits of entropy based on wrong entropy estimations, this causes attempts to recover from this state to succeed only in long term but to fail in short term. As a result, the entropy level of generated cryptographic keys can be limited to 80 bits, even though thousands of bits of entropy might have been fed to the RNG state previously. In the same scenario, we demonstrate an attack recovering the RNG state from later output with an off-line effort between\\(2^{82}\\)and\\(2^{84}\\)hash evaluations, for seeds with an entropy levelnabove 160 bits. We also show that seed data with an entropy of 160 bits, fed into the RNG, under certain circumstances, might be recovered from its output with an effort of\\(2^{82}\\)hash evaluations. These results are highly relevant for embedded systems that fail to provide sufficient entropy through their operating system RNG at boot time and rely on subsequent reseeding of the OpenSSL RNG. Furthermore, we identify a design flaw that limits the entropy of the RNG’s output to 240 bits in the general case even for an initially correctly seeded RNG, despite the fact that a security level of 256 bits is intended.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_25"}, {"primary_key": "4093111", "vector": [], "sparse_vector": [], "title": "Provable Security Evaluation of Structures Against Impossible Differential and Zero Correlation Linear Cryptanalysis.", "authors": ["<PERSON>", "Mei<PERSON> Liu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Impossible differential and zero correlation linear cryptanalysis are two of the most important cryptanalytic vectors. To characterize the impossible differentials and zero correlation linear hulls which are independent of the choices of the non-linear components, <PERSON><PERSON> al.proposed the structure deduced by a block cipher at CRYPTO 2015. Based on that, we concentrate in this paper on the security of the SPN structure and Feistel structure with SP-type round functions. Firstly, we prove that for an SPN structure, if\\(\\alpha _1\\rightarrow \\beta _1\\)and\\(\\alpha _2\\rightarrow \\beta _2\\)are possible differentials,\\(\\alpha _1|\\alpha _2\\rightarrow \\beta _1|\\beta _2\\)is also a possible differential, i.e., theOR“|” operation preserves differentials. Secondly, we show that for an SPN structure, there exists anr-round impossible differential if and only if there exists anr-round impossible differential\\(\\alpha \\not \\rightarrow \\beta \\)where the Hamming weights of both\\(\\alpha \\)and\\(\\beta \\)are 1. Thus for an SPN structure operating onmbytes, the computation complexity for deciding whether there exists an impossible differential can be reduced from\\(\\mathcal O(2^{2m})\\)to\\(\\mathcal O(m^2)\\). Thirdly, we associate a primitive index with the linear layers of SPN structures. Based on the matrices theory over integer rings, we prove that the length of impossible differentials of an SPN structure is upper bounded by the primitive index of the linear layers. As a result we show that, unless the details of the S-boxes are considered, there do not exist 5-round impossible differentials for the AES and ARIA. Lastly, based on the links between impossible differential and zero correlation linear hull, we projected these results on impossible differentials to zero correlation linear hulls. It is interesting to note some of our results also apply to the Feistel structures with SP-type round functions.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_8"}, {"primary_key": "4093112", "vector": [], "sparse_vector": [], "title": "Polytopic Cryptanalysis.", "authors": ["<PERSON><PERSON>"], "summary": "Standard differential cryptanalysis uses statistical dependencies between the difference of two plaintexts and the difference of the respective two ciphertexts to attack a cipher. Here we introduce polytopic cryptanalysis which considers interdependencies between larger sets of texts as they traverse through the cipher. We prove that the methodology of standard differential cryptanalysis can unambiguously be extended and transferred to the polytopic case including impossible differentials. We show that impossible polytopic transitions have generic advantages over impossible differentials. To demonstrate the practical relevance of the generalization, we present new low-data attacks on round-reduced DES and AES using impossible polytopic transitions that are able to compete with existing attacks, partially outperforming these.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_9"}, {"primary_key": "4093113", "vector": [], "sparse_vector": [], "title": "Computationally Binding Quantum Commitments.", "authors": ["<PERSON>"], "summary": "We present a new definition of computationally binding commitment schemes in the quantum setting, which we call “collapse-binding”. The definition applies to string commitments, composes in parallel, and works well with rewinding-based proofs. We give simple constructions of collapse-binding commitments in the random oracle model, giving evidence that they can be realized from hash functions like SHA-3. We evidence the usefulness of our definition by constructing three-round statistical zero-knowledge quantum arguments of knowledge for all NP languages.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_18"}, {"primary_key": "4093114", "vector": [], "sparse_vector": [], "title": "Adaptively Secure Identity-Based Encryption from Lattices with Asymptotically Shorter Public Parameters.", "authors": ["<PERSON><PERSON>"], "summary": "In this paper, we present two new adaptively secure identity-based encryption (IBE) schemes from lattices. The size of the public parameters, ciphertexts, and private keys are\\(\\tilde{O}(n^2 \\kappa ^{1/d})\\),\\(\\tilde{O}(n)\\), and\\(\\tilde{O}(n)\\)respectively. Here,nis the security parameter,\\(\\kappa \\)is the length of the identity, and\\(d\\in \\mathbb {N}\\)is a flexible constant that can be set arbitrary (but will affect the reduction cost). Ignoring the poly-logarithmic factors hidden in the asymptotic notation, our schemes achieve the best efficiency among existing adaptively secure IBE schemes from lattices. In more detail, our first scheme is anonymous, but proven secure under the LWE assumption with approximation factor\\(n^{\\omega (1)}\\). Our second scheme is not anonymous, but proven adaptively secure assuming the LWE assumption for all polynomial approximation factors. As a side result, based on a similar idea, we construct an attribute-based encryption scheme for branching programs that simultaneously satisfies the following properties for the first time: Our scheme achieves compact secret keys, the security is proven under the LWE assumption with polynomial approximation factors, and the scheme can deal with unbounded length branching programs.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_2"}, {"primary_key": "4093115", "vector": [], "sparse_vector": [], "title": "Pseudorandom Functions in Almost Constant Depth from Low-Noise LPN.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Pseudorandom functions (PRFs) play a central role in symmetric cryptography. While in principle they can be built from any one-way functions by going through the generic HILL (SICOMP 1999) and GGM (JACM 1986) transforms, some of these steps are inherently sequential and far from practical. <PERSON><PERSON>, <PERSON><PERSON><PERSON> (FOCS 1997) and <PERSON> (SICOMP 2002) gave parallelizable constructions of PRFs in NC\\(^2\\)and TC\\(^0\\)based on concrete number-theoretic assumptions such as DDH, RSA, and factoring. <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> (Eurocrypt 2012) constructed relatively more efficient PRFs in NC\\(^1\\)and TC\\(^0\\)based on “learning with errors” (LWE) for certain range of parameters. It remains an open problem whether parallelizable PRFs can be based on the “learning parity with noise” (LPN) problem for both theoretical interests and efficiency reasons (as the many modular multiplications and additions in LWE would then be simplified to AND and XOR operations under LPN). In this paper, we give more efficient and parallelizable constructions of randomized PRFs from LPN under noise rate\\(n^{-c}\\)(for any constant\\(0<c<1)\\)and they can be implemented with a family of polynomial-size circuits with unbounded fan-in AND, OR and XOR gates of depth\\(\\omega (1)\\), where\\(\\omega (1)\\)can be any small super-constant (e.g.,\\(\\log \\log \\log {n}\\)or even less). Our work complements the lower bound results by Razborov and Rudich (STOC 1994) that PRFs of beyond quasi-polynomial security are not contained in AC\\(^0\\)(MOD\\(_2\\)), i.e., the class of polynomial-size, constant-depth circuit families with unbounded fan-in AND, OR, and XOR gates. Furthermore, our constructions are security-lifting by exploiting the redundancy of low-noise LPN. We show that in addition to parallelizability (in almost constant depth) the PRF enjoys either of (or any tradeoff between) the following: A PRF on a weak key of sublinear entropy (or equivalently, a uniform key that leaks any\\((1 - o(1))\\)-fraction) has comparable security to the underlying LPN on a linear size secret. A PRF with key length\\(\\lambda \\)can have security up to\\(2^{O(\\lambda /\\log \\lambda )}\\), which goes much beyond the security level of the underlying low-noise LPN. where adversary makes up to certain super-polynomial amount of queries.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49896-5_6"}, {"primary_key": "4093116", "vector": [], "sparse_vector": [], "title": "Faster Algorithms for Solving LPN.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The LPN problem, lying at the core of many cryptographic constructions for lightweight and post-quantum cryptography, receives quite a lot attention recently. The best published algorithm for solving it at Asiacrypt 2014 improved the classical BKW algorithm by using covering codes, which claimed to marginally compromise the 80-bit security of HB variants, LPN-C and Lapin. In this paper, we develop faster algorithms for solving LPN based on an optimal precise embedding of cascaded concrete perfect codes, in a similar framework but with many optimizations. Our algorithm outperforms the previous methods for the proposed parameter choices and distinctly break the 80-bit security bound of the instances suggested in cryptographic schemes like HB\\(^+\\), HB\\(^\\#\\), LPN-C and Lapin.", "published": "2016-01-01", "category": "eurocrypt", "pdf_url": "", "sub_summary": "", "source": "eurocrypt", "doi": "10.1007/978-3-662-49890-3_7"}]