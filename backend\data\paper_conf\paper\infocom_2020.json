[{"primary_key": "2634228", "vector": [], "sparse_vector": [], "title": "Continuous User Verification via Respiratory Biometrics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The ever-growing security issues in various mobile applications and smart devices create an urgent demand for a reliable and convenient user verification method. Traditional verification methods request users to provide their secrets (e.g., entering passwords and collecting fingerprints). We envision that the essential trend of user verification is to free users from active participation in the verification process. Toward this end, we propose a continuous user verification system, which re-uses the widely deployed WiFi infrastructure to capture the unique physiological characteristics rooted in user's respiratory motions. Different from the existing continuous verification approaches, posing dependency on restricted scenarios/user behaviors (e.g., keystrokes and gaits), our system can be easily integrated into any WiFi infrastructure to provide non-intrusive continuous verification. Specifically, we extract the respiration-related signals from the channel state information (CSI) of WiFi. We then derive the user-specific respiratory features based on the waveform morphology analysis and fuzzy wavelet transformation of the respiration signals. Additionally, a deep learning based user verification scheme is developed to identify legitimate users accurately and detect the existence of spoofing attacks. Extensive experiments involving 20 participants demonstrate that the proposed system can robustly verify/identify users and detect spoofers under various types of attacks.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155258"}, {"primary_key": "2634229", "vector": [], "sparse_vector": [], "title": "One More Config is Enough: Saving (DC)TCP for High-speed Extremely Shallow-buffered Datacenters.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The link speed in production datacenters is growing fast, from 1Gbps to 40Gbps or even 100Gbps. However, the buffer size of commodity switches increases slowly, e.g., from 4MB at 1Gbps to 16MB at 100Gbps, thus significantly outpaced by the link speed. In such extremely shallow-buffered networks, today's TCP/ECN solutions, such as DCTCP, suffer from either excessive packet loss or substantial throughput degradation.To this end, we present BCC 1 , a simple yet effective solution that requires just one more ECN config (i.e., shared buffer ECN/RED) over prior solutions. BCC operates based on real-time global shared buffer utilization. When available buffer space suffices, BCC delivers both high throughput and low packet loss rate as prior work; Once it gets insufficient, BCC automatically triggers the shared buffer ECN to prevent packet loss at the cost of sacrificing little throughput. BCC is readily deployable with existing commodity switches. We validate BCC's hardware feasibility in a small 100G testbed and evaluate its performance using large-scale simulations. Our results show that BCC maintains low packet loss rate while slightly degrading throughput when the available buffer becomes insufficient. For example, compared to current practice, BCC achieves up to 94.4% lower 99th percentile flow completion time (FCT) for small flows while degrading average FCT for large flows by up to 3%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155280"}, {"primary_key": "2634232", "vector": [], "sparse_vector": [], "title": "Neural Tensor Completion for Accurate Network Monitoring.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Monitoring the performance of a large network is very costly. Instead, a subset of paths or time intervals of the network can be measured while inferring the remaining network data by leveraging their spatiotemporal correlations. The quality of missing data recovery highly relies on the inference algorithms. Tensor completion has attracted some recent attentions with its capability of exploiting the multi-dimensional data structure for more accurate missing data inference. However, current tensor completion algorithms only model the three-order interaction of data features through the inner product, which is insufficient to capture the high-order, nonlinear correlations across different feature dimensions. In this paper, we propose a novel Neural Tensor Completion (NTC) scheme to effectively model three-order interaction among data features with the outer product and build a 3D interaction map. Based on which, we apply 3D convolution to learn features of high-order interaction from the local range to the global range. We demonstrate this will lead to good learning ability. We conduct extensive experiments on two real-world network monitoring datasets, Abilene and WS-DREAM, to demonstrate that NTC can significantly reduce the error in missing data recovery. When the sampling ratio is low at 1%, the recovery error ratios on the testing data are around 0.05 (Abilene) and 0.13 (WS-DREAM) when using NTC, but are 0.99 (Abilene) and 0.99 (WS-DREAM) using the best current tensor completion algorithms, which are 21 times and 8 times larger.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155366"}, {"primary_key": "2634233", "vector": [], "sparse_vector": [], "title": "SoSA: Socializing Static APs for Edge Resource Pooling in Large-Scale WiFi System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large-scale WiFi system is gaining an increasing momentum rapidly in most corporate places. Enabling edge functions on the system is imperative to support unprecedented edge applications. However, building edge functionalities at each AP may incur frequent service migrations, low resource utilization, and inflexible resource provisioning. It is thus prospective to federate suitable APs to create a resource-pooled edge system such that users in association with federated APs can share the pooled resource. In this paper, we propose a novel architecture, named SoSA, to Socialize Static APs via user association transition activities for edge resource pooling. A reference implementation of SoSA is developed under an operating large-scale WiFi system in a campus area of 3.0925 km 2 . The novelty and contribution of SoSA lie in its three-layer design. In transition data feeding layer, we collect and process 25,074,733 association records of 55,809 users from 7,404 APs in a real WiFi system. In sociality construction and characterization layer, we construct an AP contact graph based on user transition statistics, under which we empirically study the sociality of APs and explore their evolving patterns. In edge resource pooling layer, by harnessing the AP sociality, we are able to customize resource pooling strategy to improve service provisioning performance. With adopting SoSA, we systematically investigate the performance of AP federation strategy in reducing service migration when users frequently transit among APs. Extensive data-driven experiments corroborate the efficacy of SoSA.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155413"}, {"primary_key": "2634234", "vector": [], "sparse_vector": [], "title": "Skin-MIMO: Vibration-based MIMO Communication over Human Skin.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We explore the feasibility of Multiple-Input-Multiple-Output (MIMO) communication through vibrations over human skin. Using off-the-shelf motors and piezo transducers as vibration transmitters and receivers, respectively, we build a 2x2 MIMO testbed to collect and analyze vibration signals from real subjects. Our analysis reveals that there exist multiple independent vibration channels between a pair of transmitter and receiver, confirming the feasibility of MIMO. Unfortunately, the slow ramping of mechanical motors and rapidly changing skin channels make it impractical for conventional channel sounding based channel state information (CSI) acquisition, which is critical for achieving MIMO capacity gains. To solve this problem, we propose Skin-MIMO, a deep learning based CSI acquisition technique to accurately predict CSI entirely based on inertial sensor (accelerometer and gyroscope) measurements at the transmitter, thus obviating the need for channel sounding. Based on experimental vibration data, we show that Skin-MIMO can improve MIMO capacity by a factor of 2.3 compared to Single-Input-Single-Output (SISO) or open-loop MIMO, which do not have access to CSI. A surprising finding is that gyroscope, which measures the angular velocity, is found to be superior in predicting skin vibrations than accelerometer, which measures linear acceleration and used widely in previous research for vibration communications over solid objects.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155298"}, {"primary_key": "2634235", "vector": [], "sparse_vector": [], "title": "An Effective Multi-node Charging Scheme for Wireless Rechargeable Sensor Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xu"], "summary": "With the maturation of wireless charging technology, Wireless Rechargeable Sensor Networks (WRSNs) has become a promising solution for prolong network lifetimes. Recently studies propose to employ a mobile charger (MC) to simultaneously charge multiple sensors within the same charging range, such that the charging performance can be improved. In this paper, we aim to jointly optimize the number of dead sensors and the energy usage effectiveness in such multi-node charging scenarios. We achieve this by introducing the partial charging mechanism, meaning that instead of following the conventional way that each sensor gets fully charged in one time step, our work allows MC to fully charge a sensor by multiple times. We show that the partial charging mechanism causes minimizing the number of dead sensors and maximizing the energy usage effectiveness to conflict with each other. We formulate this problem and develop a multi-node temporal spatial partial-charging algorithm (MTSPC) to solve it. The optimality of MTSPC is proved, and extensive simulations are carried out to demonstrate the effectiveness of MTSPC.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155262"}, {"primary_key": "2634236", "vector": [], "sparse_vector": [], "title": "Fast Network Alignment via Graph Meta-Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>ce Trajcevski", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Network alignment (NA) - i.e., linking entities from different networks (also known as identity linkage) - is a fundamental problem in many application domains. Recent advances in deep graph learning have inspired various auspicious approaches for tackling the NA problem. However, most of the existing works suffer from efficiency and generalization, due to complexities and redundant computations.We approach the NA from a different perspective, tackling it via meta-learning in a semi-supervised manner, and propose an effective and efficient approach called Meta-NA - a novel, conceptually simple, flexible, and general framework. Specifically, we reformulate NA as a one-shot classification problem and address it with a graph meta-learning framework. Meta-NA exploits the meta-metric learning from known anchor nodes to obtain latent priors for linking unknown anchor nodes. It contains multiple sub-networks corresponding to multiple graphs, learning a unified metric space, where one can easily link entities across different graphs. In addition to the performance lift, Meta-NA greatly improves the anchor linking generalization, significantly reduces the computational overheads, and is easily extendable to multi-network alignment scenarios. Extensive experiments conducted on three real-world datasets demonstrate the superiority of Meta-NA over several state-of-the-art baselines in terms of both alignment accuracy and learning efficiency.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155456"}, {"primary_key": "2634237", "vector": [], "sparse_vector": [], "title": "Look Ahead at the First-mile in Livecast with Crowdsourced Highlight Prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lifeng Sun"], "summary": "Recently, data-driven prediction strategies have shown the potential of shepherding the optimization strategies for end viewer's Quality-of-Experience in practical streaming applications. The current prediction-based designs have largely focused on optimizing the last-mile, i.e., viewer-side, which 1) need the real-time feedback from viewers to improve the prediction accuracy; and 2) need quick responses to guarantee the effectiveness of optimization strategies in the future. Thanks to the emerged crowdsourced livecast services, e.g., Twitch.tv, we for the first time exploit the opportunity to realize the long-term prediction and optimization with the assistance derived from the first-mile, i.e., source broadcasters.In this paper, we propose a novel framework CastFlag, which analyzes the broadcasters' operations and interactions, predicts the key events (i.e., highlights), and optimizes the transcoding stage in the corresponding live streams, even before the encoding stage. Taking the most popular eSports gamecast as an example, we illustrate the effectiveness of this framework in the game highlight prediction and transcoding workload allocation. The trace-driven evaluation shows the superiority of CastFlag as it: (1) improves the prediction accuracy over other learning-based approaches by up to 30%; (2) achieves an average of 10% saving of the transcoding latency at less cost.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155395"}, {"primary_key": "2634238", "vector": [], "sparse_vector": [], "title": "Variational Information Diffusion for Probabilistic Cascades Prediction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ce Trajcevski", "<PERSON><PERSON>"], "summary": "Understanding in-network information diffusion is a fundamental problem in many application domains and one of the primary challenges is to predict the size of the information cascade. Most of the existing models rely either on hypothesized point process (e.g., <PERSON><PERSON><PERSON> and <PERSON><PERSON> process), or simply predict the information propagation via deep neural networks. However, they fail to simultaneously capture the underlying structure of a cascade graph and the propagation of uncertainty in the diffusion, which may result in unsatisfactory prediction performance. To address these, in this work we propose a novel probabilistic cascade prediction framework: Variational Cascade (VaCas) graph learning networks. VaCas allows a non-linear information diffusion inference and models the information diffusion process by learning the latent representation of both the structural and temporal information. It is a pattern-agnostic model leveraging variational inference to learn the node-level and cascade-level latent factors in an unsupervised manner. In addition, VaCas is capable of capturing both the cascade representation uncertainty and node infection uncertainty, while enabling hierarchical pattern learning of information diffusion. Extensive experiments conducted on real-world datasets demonstrate that VaCas significantly improves the prediction accuracy, compared to state-of-the-art approaches, while also enabling interpretability.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155349"}, {"primary_key": "2634239", "vector": [], "sparse_vector": [], "title": "A Universal Method to Combat Multipaths for RFID Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Xiaofeng Shi", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Jinsong Han"], "summary": "There have been increasing interests in exploring the sensing capabilities of RFID to enable numerous IoT applications, including object localization, trajectory tracking, and human behavior sensing. However, most existing methods rely on the signal measurement either in a low multipath environment, which is unlikely to exist in many practical situations, or with special devices, which increase the operating cost. This paper investigates the possibility of measuring `multi-path-free' signal information in multipath-prevalent environments simply using a commodity RFID reader. The proposed solution, Clean Physical Information Extraction (CPIX), is universal, accurate, and compatible to standard protocols and devices. CPIX improves RFID sensing quality with near zero cost - it requires no extra device. We implement CPIX and study two major RFID sensing applications: tag localization and human behavior sensing. CPIX reduces the localization error by 30% to 50% and achieves the MOST accurate localization by commodity readers compared to existing work. It also significantly improves the quality of human behaviour sensing.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155240"}, {"primary_key": "2634243", "vector": [], "sparse_vector": [], "title": "Hop-by-Hop Multipath Routing: Choosing the Right Nexthop Set.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Internet can be made more efficient and robust with hop-by-hop multipath routing: Each router on the path can split packets between multiple nexthops in order to 1) avoid failed links and 2) reduce traffic on congested links. Before deciding how to split traffic, one first needs to decide which nexthops to allow at each step. In this paper, we investigate the requirements and trade-offs for making this choice.Most related work chooses the viable nexthops by applying the \"Downward Criterion\", i.e., only adding nexthops that lead closer to the destination; or more generally by creating a Directed Acyclic Graph (DAG) for each destination. We show that a DAG's nexthop options are necessarily limited, and that, by using certain links in both directions (per destination), we can add further nexthops while still avoiding loops. Our solution LFID (LoopFree Inport-Dependent) routing, though having a slightly higher time complexity, leads to both a higher number of and shorter potential paths than related work. LFID thus protects against a higher percentage of single and multiple failures (or congestions) and comes close to the performance of arbitrary source routing.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155482"}, {"primary_key": "2634245", "vector": [], "sparse_vector": [], "title": "Threats of Adversarial Attacks in DNN-Based Modulation Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the emergence of the information age, mobile data has become more random, heterogeneous and massive. Thanks to its many advantages, deep learning is increasingly applied in communication fields such as modulation recognition. However, recent studies show that the deep neural networks (DNN) is vulnerable to adversarial examples, where subtle perturbations deliberately designed by an attacker can fool a classifier model into making mistakes. From the perspective of an attacker, this study adds elaborate adversarial examples to the modulation signal, and explores the threats and impacts of adversarial attacks on the DNN-based modulation recognition in different environments. The results show that, regardless of a white-box or a black-box model, the adversarial attack can reduce the accuracy of the target model. Among them, the performance of the iterative attack is superior to the one-step attack in most scenarios. In order to ensure the invisibility of the attack (the waveform being consistent before and after the perturbations), an appropriate perturbation level is found without losing the attack effect. Finally, it is attested that the signal confidence level is inversely proportional to the attack success rate, and several groups of signals with high robustness are obtained.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155389"}, {"primary_key": "2634250", "vector": [], "sparse_vector": [], "title": "VFC-Based Cooperative UAV Computation Task Offloading for Post-disaster Rescue.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ruid<PERSON> Li"], "summary": "Natural disasters often cause huge and unpredictable losses to human lives and properties. In such an emergency post-disaster rescue situation, unmanned aerial vehicles (UAVs) are effective tools to enter the damaged areas to perform immediate disaster recovery missions, due to their flexible mobilities and fast deployment. However, the UAVs typically have very limited batteries and computational capacities, which make them unable to perform heavy computation tasks during the complicated disaster recovery process. This paper addresses the issue with a fog computing based UAV system. In specific, we first introduce the vehicular fog computing (VFC) system in which the unmanned ground vehicles (UGVs) perform the computation tasks offloaded from UAVs. To resolve the transmission competitions yet enable cooperations among UAVs and UGVs, a stable matching algorithm is developed to transform the computation task offloading problem into a two-sided matching problem. An iterative algorithm is then developed which matches each UAV with the most suitable UGV for offloading. Finally, extensive simulations are carried out to demonstrate that the proposed scheme can effectively improve utilities of UAVs and reduce average delay through comparison with conventional schemes.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155397"}, {"primary_key": "2634251", "vector": [], "sparse_vector": [], "title": "Camel: Smart, Adaptive Energy Optimization for Mobile Web Interactions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Web technology underpins many interactive mobile applications. However, energy-efficient mobile web interactions is an outstanding challenge. Given the increasing diversity and complexity of mobile hardware, any practical optimization scheme must work for a wide range of users, mobile platforms and web workloads. This paper presents CAMEL, a novel energy optimization system for mobile web interactions. CAMEL leverages machine learning techniques to develop a smart, adaptive scheme to judiciously trade performance for reduced power consumption. Unlike prior work, CAMEL directly models how a given web content affects the user expectation and uses this to guide energy optimization. It goes further by employing transfer learning and conformal predictions to tune a previously learned model in the end-user environment and improve it over time. We apply CAMEL to Chromium and evaluate it on four distinct mobile systems involving 1,000 testing webpages and 30 users. Compared to four state-of-the-art web-event optimizers, CAMEL delivers 22% more energy savings, but with 49% fewer violations on the quality of user experience, and exhibits orders of magnitudes less overhead when targeting a new computing environment.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155489"}, {"primary_key": "2634253", "vector": [], "sparse_vector": [], "title": "BLESS: A BLE Application Security Scanning Framework.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bluetooth Low Energy (BLE) is a widely adopted wireless communication technology in the Internet of Things (IoT). BLE offers secure communication through a set of pairing strategies. However, these pairing strategies are obsolete in the context of IoT. The security of BLE based devices relies on physical security, but a BLE enabled IoT device may be deployed in a public environment without physical security. Attackers who can physically access a BLE-based device will be able to pair with it and may control it thereafter. Therefore, manufacturers may implement extra authentication mechanisms at the application layer to address this issue. In this paper, we design and implement a BLE Security Scan (BLESS) framework to identify those BLE apps that do not implement encryption or authentication at the application layer. Taint analysis is used to track if BLE apps use nonces and cryptographic keys, which are critical to cryptographic protocols. We scan 1073 BLE apps and find that 93% of them are not secure. To mitigate this problem, we propose and implement an application-level defense with a low-cost $0.55 crypto co-processor using public key cryptography.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155473"}, {"primary_key": "2634256", "vector": [], "sparse_vector": [], "title": "DFD: Adversarial Learning-based Approach to Defend Against Website Fingerprinting.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Onion Router (Tor) is designed to support an anonymous communication through end-to-end encryption. To prevent vulnerability of side channel attacks (e.g. website fingerprinting), dummy packet injection modules have been embedded in Tor to conceal trace patterns that are associated with the individual websites. However, recent study shows that current Website Fingerprinting (WF) defenses still generate patterns that may be captured and recognized by the deep learning technology. In this paper, we conduct in-depth analyses of two state-of-the-art WF defense approaches. Then, based on our new observations and insights, we propose a novel defense mechanism using a per-burst injection technique, called Deep Fingerprinting Defender (DFD), against deep learning-based WF attacks. The DFD has two operation modes, one-way and two-way injection. DFD is designed to break the inherent patterns preserved in Tor user's traces by carefully injecting dummy packets within every burst. We conducted extensive experiments to evaluate the performance of DFD over both closed-world and open-world settings. Our results demonstrate that these two configurations can successfully break the Tor network traffic pattern and achieve a high evasion rate of 86.02% over one-way client-side injection rate of 100%, a promising improvement in comparison with state-of-the-art adversarial trace's evasion rate of 60%. Moreover, DFD outperforms the state-of-the-art alternatives by requiring lower bandwidth overhead; 14.26% using client-side injection.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155465"}, {"primary_key": "2634257", "vector": [], "sparse_vector": [], "title": "A New Fully-Distributed Arbitration-Based Membership Protocol.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recently, a new class of \"arbitrator-based\" membership protocols have been proposed. These claim to provide time bounds on how long membership lists can stay inconsistent-this property is critical in many distributed applications which need to take timely recovery actions. In this paper, we: 1) present the first fully decentralized and stabilizing version of membership protocols in this class; 2) formally prove properties and claims about both our decentralized version and the original protocol; and 3) present experimental results from both a simulation and a real cluster implementation.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155481"}, {"primary_key": "2634258", "vector": [], "sparse_vector": [], "title": "COSE: Configuring Serverless Functions using Statistical Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Serverless computing has emerged as a new compelling paradigm for the deployment of applications and services. It represents an evolution of cloud computing with a simplified programming model, that aims to abstract away most operational concerns. Running serverless functions requires users to configure multiple parameters, such as memory, CPU, cloud provider, etc. While relatively simpler, configuring such parameters correctly while minimizing cost and meeting delay constraints is not trivial. In this paper, we present COSE, a framework that uses Bayesian Optimization to find the optimal configuration for serverless functions. COSE uses statistical learning techniques to intelligently collect samples and predict the cost and execution time of a serverless function across unseen configuration values. Our framework uses the predicted cost and execution time, to select the \"best\" configuration parameters for running a single or a chain of functions, while satisfying customer objectives. In addition, COSE has the ability to adapt to changes in the execution time of a serverless function. We evaluate COSE not only on a commercial cloud provider, where we successfully found optimal/near-optimal configurations in as few as five samples, but also over a wide range of simulated distributed cloud environments that confirm the efficacy of our approach.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155363"}, {"primary_key": "2634260", "vector": [], "sparse_vector": [], "title": "Exposing the Fingerprint: Dissecting the Impact of the Wireless Channel on Radio Fingerprinting.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Salvatore D&apos;Oro", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Radio fingerprinting uniquely identifies wireless devices by leveraging tiny hardware-level imperfections inevitably present in off-the-shelf radio circuitry. This way, devices can be directly identified at the physical layer by analyzing the unprocessed received waveform - thus avoiding energy-expensive upper-layer cryptography that resource-challenged embedded devices may not be able to afford. Recent advances have proven that convolutional neural networks (CNNs) - thanks to their multidimensional mappings - can achieve fingerprinting accuracy levels impossible to achieve by traditional low-dimensional algorithms. The same research, however, has also suggested that the wireless channel may negatively impact the accuracy of CNN-based radio fingerprinting algorithms by making device-unique hardware imperfections much harder to recognize.In spite of the growing interest in radio fingerprinting research by academia and DARPA, the wireless research community still lacks (i) a large-scale open dataset for radio fingerprinting collected in diverse environments and rich, diverse, channel conditions; and (ii) a full-fledged, systematic, quantitative investigation of the impact of the wireless channel on the accuracy of CNN-based radio fingerprinting algorithms. The key contribution of this paper is to bridge this gap by (i) collecting and sharing with the community more than 7TB of wireless data obtained from 20 wireless devices with identical RF circuitry (and thus, worst-case scenario for fingerprinting) over the course of several days in (a) an anechoic chamber, (b) in-the-wild testbed, and (c) with cable connections; and (ii) providing a first-of-its-kind evaluation of the impact of the wireless channel on CNN-based fingerprinting algorithms through (a) the 7TB experimental dataset and (b) a 400GB dataset provided by DARPA containing hundreds of thousands of transmissions from thousands of WiFi and ADS-B devices with different SNR conditions. Experimental results conclude that (i) the wireless channel impacts the classification accuracy significantly, i.e., from 85% to 9% and from 30% to 17% in the experimental and DARPA dataset, respectively; and that (ii) equalizing I/Q data can increase the accuracy to a significant extent (i.e., by up to 23%) when the number of devices increases significantly.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155259"}, {"primary_key": "2634261", "vector": [], "sparse_vector": [], "title": "Elastic Network Virtualization.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Network virtualization allows multiple tenant networks to coexist on a shared infrastructure. Core to its realization is the embedding of virtual networks onto the underlying substrate. Existing approaches are not suitable for cloud environments as they lack a fundamental requirement: elasticity. To address this issue we explore the capacity of flexibly changing the topology of a virtual network by proposing an embedding solution that adds elasticity to the tenant's virtual infrastructures. For this purpose, we introduce four primitives to tenants' virtual networks - including scale in and scale out - and propose new algorithms to materialize them. The main challenge is to enable these new services while maximizing resource efficiency and without impacting service quality. Instead of further improving existing online embedding algorithms - always limited by the inability to predict future demand - we follow a different approach. Specifically, we leverage network migration for our embedding procedures and to introduce a new reconfiguration primitive for the infrastructure provider. As migration introduces network churn, our solution uses this technique judiciously, to limit the impact to running services. Our solution improves on network efficiency over the state-of-the-art, while reducing the migration footprint by at least one order of magnitude.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155287"}, {"primary_key": "2634262", "vector": [], "sparse_vector": [], "title": "Classification of Load Balancing in the Internet.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent advances in programmable data planes, software-defined networking, and the adoption of IPv6 support novel, more complex load balancing strategies. We introduce the Multipath Classification Algorithm (MCA), a probing algorithm that extends traceroute to identify and classify load balancing in Internet routes. MCA extends existing formalism and techniques to consider that load balancers may use arbitrary combinations of bits in the packet header for load balancing. We propose optimizations to reduce probing cost that are applicable to MCA and existing load balancing measurement techniques. Through large-scale measurement campaigns, we characterize and study the evolution of load balancing on the IPv4 and IPv6 Internet with multiple transport protocols. Our results show that load balancing is more prevalent and that load balancing strategies are more mature than previous characterizations have found.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155387"}, {"primary_key": "2634265", "vector": [], "sparse_vector": [], "title": "Activating Wireless Voice for E-Toll Collection Systems with Zero Start-up Cost.", "authors": ["Z<PERSON><PERSON> An", "<PERSON><PERSON><PERSON><PERSON> Lin", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work enhances the machine-to-human communication between electronic toll collection (ETC) systems and drivers by providing an AM broadcast service to deployed ETC systems. This study is the first to show that ultra-high radio frequency identification signals can be received by an AM radio receiver due to the presence of the nonlinearity effect in the AM receiver. Such a phenomenon allows the development of a previously infeasible cross-technology and cross-frequency communication, called Tagcaster, which converts an ETC reader to an AM station for broadcasting short messages (e.g., charged- fees and traffic forecast) to drivers at tollbooths. The key innovation in this work is the engineering of Tagcaster over off-the-shelf ETC systems using shadow carrier and baseband whitening without the need for hardware nor firmware changes. This feature allows zero-cost rapid deployment in existing ETC infrastructure. Two prototypes of Tagcaster are designed, implemented and evaluated over four general and five vehicle-mounted AM receivers (e.g., Toyota, Audi, and Jetta). Experiments reveal that Tagcaster can provide good-quality (PESQ> 2) and stable AM broadcasting service with a 30 m coverage range. Tagcaster remarkably improves user experience at ETC stations and two- thirds volunteer drivers rate it with a score of 4+ out of 5.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155223"}, {"primary_key": "2634266", "vector": [], "sparse_vector": [], "title": "Tracking the State of Large Dynamic Networks via Reinforcement Learning.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A Network Inventory Manager (NIM) is a software solution that scans, processes and records data about all devices in a network. We consider the problem faced by a NIM that can send out a limited number of probes to track changes in a large, dynamic network. The underlying change rate for the Network Elements (NEs) is unknown and may be highly non-uniform. The NIM should concentrate its probe budget on the NEs that change most frequently with the ultimate goal of minimizing the weighted Fraction of Stale Time (wFOST) of the inventory. However, the NIM cannot discover the change rate of a NE unless the NE is repeatedly probed.We develop and analyze two algorithms based on Reinforcement Learning to solve this exploration-vs-exploitation problem. The first is motivated by the Thompson Sampling method and the second is derived from the Robbins-Monro stochastic learning paradigm. We show that for a fixed probe budget, both of these algorithms produce a potentially unbounded improvement in terms of wFOST compared to the baseline algorithm that divides the probe budget equally between all NEs. Our simulations of practical scenarios show optimal performance in minimizing wFOST while discovering the change rate of the NEs.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155484"}, {"primary_key": "2634268", "vector": [], "sparse_vector": [], "title": "MagB: Repurposing the Magnetometer for Fine-Grained Localization of IoT Devices.", "authors": ["Paramasiven Appavoo", "<PERSON><PERSON>", "<PERSON>"], "summary": "Interest in fine-grained indoor localization remains high and various approaches including those based on Radio Frequency (RF), ultrasound, acoustic, magnetic field and light have been proposed. However, while the achieved accuracy may be high, many of these approaches do not work well in environments with lots of obstructions. In this paper, we present MagB, a decimeter-level localization scheme that uses the magnetometer available on many IoT devices. MagB estimates the bearing of magnetic beacons by detecting changes in the magnetic field strength. Localization is then performed based on Angle-of-Arrival (AoA) information. We have built a prototype of MagB using low cost, off-the-shelf components. Our evaluation shows that MagB is able to achieve a median accuracy of about 13cm and can localize devices even when they are placed in steel filing cabinet or inside the casing of a running PC.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155353"}, {"primary_key": "2634270", "vector": [], "sparse_vector": [], "title": "(How Much) Does a Private WAN Improve Cloud Performance?", "authors": ["<PERSON>", "<PERSON><PERSON>", "Georgia Essig", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "The construction of private WANs by cloud providers enables them to extend their networks to more locations and establish direct connectivity with end user ISPs. Tenants of the cloud providers benefit from this proximity to users, which is supposed to provide improved performance by bypassing the public Internet. However, the performance impact of cloud providers' private WANs is not widely understood.To isolate the impact of a private WAN, we measure from globally distributed vantage points to two large cloud providers, comparing performance when using their worldwide WAN and when instead using the public Internet. The benefits are not universal. While 48% of our vantage points saw improved performance when using the WAN, 43% had statistically indistinguishable median performance, and 9% had better performance over the public Internet. We find that the benefits of the private WAN tend to improve with client-to-server distance, but the benefits (or drawbacks) for a particular vantage point depend on specifics of its geographic and network connectivity.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155428"}, {"primary_key": "2634274", "vector": [], "sparse_vector": [], "title": "Working Set Theorems for Routing in Self-Adjusting Skip List Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper explores the design of dynamic network topologies which adjust to the workload they serve, in a demand-aware and online manner. Such self-adjusting networks (SANs) are enabled by emerging optical technologies, and can be found, e.g., in datacenters. SANs can be used to reduce routing costs by moving frequently communicating nodes topologically closer. However, such reconfigurations also come at a cost, introducing a need for online algorithms which strike an optimal balance between the benefits and costs of reconfigurations.This paper presents SANs which provide, for the first time, provable working set guarantees: the routing cost between node pairs is proportional to how recently these nodes communicated last time. Our SANs rely on a distributed implementation of skip lists (which serves as the topology) and provide additional interesting properties such as local routing. Our first contribution is SASL 2 , which is a randomized and sequential SAN algorithm that achieves the working set property. Then we show how SASL 2 can be converted to a distributed algorithm that handles concurrent communication requests and maintains SASL 2 's properties. Finally, we present deterministic SAN algorithms.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155495"}, {"primary_key": "2634275", "vector": [], "sparse_vector": [], "title": "MAMBA: A Multi-armed Bandit Framework for Beam Tracking in Millimeter-wave Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Berk Akgun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Millimeter-wave (mmW) spectrum is a major candidate to support the high data rates of 5G systems. However, due to directionality of mmW communication systems, misalignments between the transmit and receive beams occur frequently, making link maintenance particularly challenging and motivating the need for fast and efficient beam tracking. In this paper, we propose a multi-armed bandit framework, called MAMBA, for beam tracking in mmW systems. We develop a reinforcement learning algorithm, called adaptive Thompson sampling (ATS), that MAMBA embodies for the selection of appropriate beams and transmission rates along these beams. ATS uses prior beam-quality information collected through the initial access and updates it whenever an ACK/NACK feedback is obtained from the user. The beam and the rate to be used during next downlink transmission are then selected based on the updated posterior distributions. Due to its model-free nature, ATS can accurately estimate the best beam/rate pair, without making assumptions regarding the temporal channel and/or user mobility. We conduct extensive experiments over the 28 GHz band using a 4x8 phased- array antenna to validate the efficiency of ATS, and show that it improves the link throughput by up to 182%, compared to the beam management scheme proposed for 5G.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155408"}, {"primary_key": "2634276", "vector": [], "sparse_vector": [], "title": "Bandwidth Allocation and Service Differentiation in D2D Wireless Networks.", "authors": ["<PERSON>", "Sanket S<PERSON>"], "summary": "Inspired by a new feature in 5G NR called bandwidth part (BWP), this paper presents a bandwidth allocation (BA) model that allows one to adapt the bandwidth allocated to users depending on their data rate needs. Specifically, in adaptive BA, a wide bandwidth is divided into chunks of smaller bandwidths and the number of bandwidth chunks allocated to a user depends on its needs or type. Although BWP in 5G NR mandates allocation of a set of contiguous bandwidth chunks, our BA model also allows other assumptions on chunk allocation such as the allocation of any set of bandwidth chunks, as in, e.g., LTE resource allocation, where chunks are selected uniformly at random. The BA model studied here is probabilistic in that the user locations are assumed to form a realization of a Poisson point process and each user decides independently to be of a certain type with some probability. This model allows one to quantify spectrum sharing and service differentiation in this context, namely to predict what performance a user gets depending on its type as well as the overall performance. This is based on exact representations of key performance metrics for each user type, namely its success probability, the meta distribution of its signal-to-interference ratio, and its Shannon throughput. We show that, surprisingly, the higher traffic variability stemming from adaptive BA is beneficial: when comparing two networks using adaptive BA and having the same mean signal and the same mean interference powers, the network with higher traffic variability performs better for all these performance metrics. With respect to Shannon throughput, we observe that our BA model is roughly egalitarian per Hertz and leads to a linear service differentiation in aggregated throughput value.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155469"}, {"primary_key": "2634277", "vector": [], "sparse_vector": [], "title": "Sequential addition of coded sub-tasks for straggler mitigation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Straggler mitigation can be achieved by redundant computation. In MDS redundancy method, a task is divided into k sub-tasks which are encoded to n coded sub-tasks, such that a task is completed if any k coded sub-tasks are completed. Two important metrics of interest are task completion time, and server utilization cost which is the aggregate completed work by all servers in this duration. We consider a proactive straggler mitigation strategy where n 0 out of n coded sub-tasks are started at time 0 while the remaining n - n 0 coded sub-tasks are launched when ℓ 0 ≤ min(n 0 , k) of the initial ones finish. The coded sub-tasks are halted when k of them finish. For this flexible forking strategy with multiple parameters, we analyze the mean of two performance metrics for the proposed forking strategy when the random service completion time at each server is independent and distributed identically to a shifted exponential. Our analysis demonstrates that the regime of n 0 0 = n), and is thus not a regime of interest. For n 0 ≥ k, we find that there is a tradeoff between the two performance metrics and leads to decrease in mean server utilization cost at the expense of mean service completion time and an efficient choice of the parameters is helpful.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155358"}, {"primary_key": "2634279", "vector": [], "sparse_vector": [], "title": "MAGIC: Magnetic Resonant Coupling for Intra-body Communication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes MAGIC: magnetic resonant (MR) coupling for intra-body communication between implants and wearables. MAGIC includes not only the hardware-software design of the coupled coils and methods of manipulating the magnetic field for relaying information, but also the ability to raise immediate emergency-related alerts with guaranteed delivery time. MR coupling makes the design of the transmission link robust to channel-related parameters, as the magnetic permeability of skin and muscle is close to that of air. Thus, changes in tissue moisture content and thickness does not impact the design, which is a persistent problem in other approaches for implant communications like RF, ultrasound and galvanic coupling (GC). The paper makes three main contributions: It develops the theory leading to the design of the information relaying coils in MAGIC. It proposes a systems-level design of a communication link that extends up to 50cm with a low expected BER of 10 -4 . Finally, the paper includes an experimental setup demonstrating how MAGIC operates in air and muscle tissue, as well as a comparison with alternative implant communication technologies, such as classical radio frequency and GC. Results reveal that MAGIC offers instantaneous alerts with up to 5 times lower power consumption compared to other forms of communication.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155421"}, {"primary_key": "2634281", "vector": [], "sparse_vector": [], "title": "Preemptive All-reduce Scheduling for Expediting Distributed DNN Training.", "authors": ["<PERSON><PERSON>", "Yanghua Peng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data-parallel training is widely used for scaling DNN training over large datasets, using the parameter server or all-reduce architecture. Communication scheduling has been promising to accelerate distributed DNN training, which aims to overlap communication with computation by scheduling the order of communication operations. We identify two limitations of previous communication scheduling work. First, layer-wise computation graph has been a common assumption, while modern machine learning frameworks (e.g., TensorFlow) use a sophisticated directed acyclic graph (DAG) representation as the execution model. Second, the default sizes of tensors are often less than optimal for transmission scheduling and bandwidth utilization. We propose PACE, a communication scheduler that preemptively schedules (potentially fused) all-reduce tensors based on the DAG of DNN training, guaranteeing maximal overlapping of communication with computation and high bandwidth utilization. The scheduler contains two integrated modules: given a DAG, we identify the best tensor-preemptive communication schedule that minimizes the training time; exploiting the optimal communication scheduling as an oracle, a dynamic programming approach is developed for generating a good DAG, which merges small communication tensors for efficient bandwidth utilization. Experiments in a GPU testbed show that PACE accelerates training with representative system configurations, achieving up to 36% speed-up compared with state-of-the-art solutions.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155446"}, {"primary_key": "2634285", "vector": [], "sparse_vector": [], "title": "Faster and More Accurate Measurement through Additive-Error Counters.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Shay <PERSON>argaftik"], "summary": "Counters are a fundamental building block for networking applications such as load balancing, traffic engineering, and intrusion detection, which require estimating flow sizes and identifying heavy hitter flows. Existing works suggest replacing counters with shorter multiplicative error estimators that improve the accuracy by fitting more of them within a given space. However, such estimators impose a computational overhead that degrades the measurement throughput. Instead, we propose additive error estimators, which are simpler, faster, and more accurate when used for network measurement. Our solution is rigorously analyzed and empirically evaluated against several other measurement algorithms on real Internet traces. For a given error target, we improve the speed of the uncompressed solutions by 5×-30×, and the space by up to 4×. Compared with existing state-of-the-art estimators, our solution is 9×-35× faster while being considerably more accurate.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155340"}, {"primary_key": "2634288", "vector": [], "sparse_vector": [], "title": "Decentralized placement of data and analytics in wireless networks for energy-efficient execution.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON> N. E.", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We address energy-efficient placement of data and analytics components of composite analytics services on a wireless network to minimize execution-time energy consumption (computation and communication) subject to compute, storage and network resource constraints. We introduce an expressive analytics service hypergraph model for representing k-ary composability relationships (k ≥ 2) between various analytics and data components and leverage binary quadratic programming (BQP) to minimize the total energy consumption of a given placement of the analytics hypergraph nodes on the network subject to resource availability constraints. Then, after defining a potential energy functional Φ(·) to model the affinities of analytics components and network resources using analogs of attractive and repulsive forces in physics, we propose a decentralized Metropolis Monte Carlo (MMC) sampling method which seeks to minimize Φ by moving analytics and data on the network. Although Φ is non-convex, using a potential game formulation, we identify conditions under which the algorithm provably converges to a local minimum energy equilibrium placement configuration. Trace-based simulations of the placement of a deep-neural-network analytics service on a realistic wireless network show that for smaller problem instances our MMC algorithm yields placements with total energy within a small factor of BQP and more balanced workload distributions; for larger problems, it yields low-energy configurations while the BQP approach fails.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155222"}, {"primary_key": "2634290", "vector": [], "sparse_vector": [], "title": "AZTEC: Anticipatory Capacity Allocation for Zero-Touch Network Slicing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The combination of network softwarization with network slicing enables the provisioning of very diverse services over the same network infrastructure. However, it also creates a complex environment where the orchestration of network resources cannot be guided by traditional, human-in-the-loop network management approaches. New solutions that perform these tasks automatically and in advance are needed, paving the way to zero-touch network slicing. In this paper, we propose AZTEC, a data-driven framework that effectively allocates capacity to individual slices by adopting an original multi-timescale forecasting model. Hinging on a combination of Deep Learning architectures and a traditional optimization algorithm, AZTEC anticipates resource assignments that minimize the comprehensive management costs induced by resource overprovisioning, instantiation and reconfiguration, as well as by denied traffic demands. Experiments with real-world mobile data traffic show that AZTEC dynamically adapts to traffic fluctuations, and largely outperforms state-of-the-art solutions for network resource orchestration.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155299"}, {"primary_key": "2634292", "vector": [], "sparse_vector": [], "title": "Enabling Live Migration of Containerized Applications Across Clouds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Live migration, the process of transferring a running application to a different physical location with minimal downtime, can provide many benefits desired by modern cloudbased systems. Furthermore, live migration between different cloud providers enables a new level of freedom for cloud users to move their workloads around for performance or business objectives without having to be tied down to any single provider. While this vision is not new, to-date, there are few solutions and proof-of-concepts that provide this capability. As containerized applications are gaining popularity, we focus on the design and implementation of live migration of containers across cloud providers. CloudHopper, our proof-of-concept live migration service for containers to hop around between Amazon Web Services, Google Cloud Platform, and Microsoft Azure is evaluated using a common web-based workload. CloudHopper is automated and supports pre-copy optimization, connection holding, traffic redirection, and multiple interdependent container migration. It is applicable to a broad range of application use cases.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155403"}, {"primary_key": "2634293", "vector": [], "sparse_vector": [], "title": "SwarmControl: An Automated Distributed Control Framework for Self-Optimizing Drone Networks.", "authors": ["<PERSON>", "Salvatore D&apos;Oro", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Zhangyu Guan", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Networks of Unmanned Aerial Vehicles (UAVs), composed of hundreds, possibly thousands of highly mobile and wirelessly connected flying drones will play a vital role in future Internet of Things (IoT) and 5G networks. However, how to control UAV networks in an automated and scalable fashion in distributed, interference-prone, and potentially adversarial environments is still an open research problem. This article introduces SwarmControl, a new software-defined control framework for UAV wireless networks based on distributed optimization principles. In essence, SwarmControl provides the Network Operator (NO) with a unified centralized abstraction of the networking and flight control functionalities. High-level control directives are then automatically decomposed and converted into distributed network control actions that are executed through programmable software-radio protocol stacks. SwarmControl (i) constructs a network control problem representation of the directives of the NO; (ii) decomposes it into a set of distributed sub-problems; and (iii) automatically generates numerical solution algorithms to be executed at individual UAVs.We present a prototype of an SDR-based, fully reconfigurable UAV network platform that implements the proposed control framework, based on which we assess the effectiveness and flexibility of SwarmControl with extensive flight experiments. Results indicate that the SwarmControl framework enables swift reconfiguration of the network control functionalities, and it can achieve an average throughput gain of 159% compared to the state-of-the-art solutions.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155231"}, {"primary_key": "2634294", "vector": [], "sparse_vector": [], "title": "CoBeam: Beamforming-based Spectrum Sharing With Zero Cross-Technology Signaling for 5G Wireless Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Zhangyu Guan", "<PERSON><PERSON><PERSON>"], "summary": "This article studies an essential yet challenging problem in 5G wireless networks: Is it possible to enable spectrally-efficient spectrum sharing for heterogeneous wireless networks with different, possibly incompatible, spectrum access technologies on the same spectrum bands; without modifying the protocol stacks of existing wireless networks? To answer this question, this article explores the system challenges that need to be addressed to enable a new spectrum sharing paradigm based on beamforming, which we refer to as CoBeam. In CoBeam, a secondary wireless network is allowed to access a spectrum band based on cognitive beam-forming without mutual temporal exclusion, i.e., without interrupting the ongoing transmissions of coexisting wireless networks on the same bands; and without cross-technology communication. We first describe the main components of CoBeam, including programmable physical layer driver, cognitive sensing engine, and beamforming engine, and then we showcase the potential of the CoBeam framework by designing a practical coexistence scheme between Wi-Fi and LTE on unlicensed bands. We present a prototype of the resulting coexisting Wi-Fi/U-LTE network built on off-the-shelf software radios based on which we evaluate the performance of CoBeam through an extensive experimental campaign. Performance evaluation results indicate that CoBeam can achieve on average 169% throughput gain while requiring no signaling exchange between the coexisting wireless networks.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155498"}, {"primary_key": "2634297", "vector": [], "sparse_vector": [], "title": "Selection of Sensors for Efficient Transmitter Localization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We address the problem of localizing an (illegal) transmitter using a distributed set of sensors. Our focus is on developing techniques that perform the transmitter localization in an efficient manner, wherein the efficiency is defined in terms of the number of sensors used to localize. Localization of illegal transmitters is an important problem which arises in many important applications, e.g., in patrolling of shared spectrum systems for any unauthorized users. Localization of transmitters is generally done based on observations from a deployed set of sensors with limited resources, thus it is imperative to design techniques that minimize the sensors' energy resources. In this paper, we design greedy approximation algorithms for the optimization problem of selecting a given number of sensors in order to maximize an appropriately defined objective function of localization accuracy. The obvious greedy algorithm delivers a constant-factor approximation only for the special case of two hypotheses (potential locations). For the general case of multiple hypotheses, we design a greedy algorithm based on an appropriate auxiliary objective function - and show that it delivers a provably approximate solution for the general case. We develop techniques to significantly reduce the time complexity of the designed algorithms, by incorporating certain observations and reasonable assumptions. We evaluate our techniques over multiple simulation platforms, including an indoor as well as an outdoor testbed, and demonstrate the effectiveness of our designed techniques - our techniques easily outperform prior and other approaches by up to 50-60% in large-scale simulations.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155230"}, {"primary_key": "2634298", "vector": [], "sparse_vector": [], "title": "Letting off STEAM: Distributed Runtime Traffic Scheduling for Service Function Chaining.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Network function virtualization has introduced a high degree of flexibility for orchestrating service functions. The provisioning of chains of service functions requires making decisions on both (1) placement of service functions and (2) scheduling of traffic through them. The placement problem (1) can be tackled during the planning phase, by exploiting coarse-grained traffic information, and has been studied extensively. However, runtime traffic scheduling (2) for optimizing system utilization and service quality, as required for future edge cloud and mobile carrier scenarios, has not been addressed so far.We fill this gap by presenting a queuing-based system model to characterize the runtime traffic scheduling problem for service function chaining. We propose a throughput-optimal scheduling policy, called integer allocation maximum pressure policy (IA-MPP). To ensure practicality in large distributed settings, we propose multi-site cooperative IA-MPP (STEAM), fulfilling runtime requirements while achieving near-optimal performance. We examine our policies in various settings representing real-world scenarios. STEAM closely matches IA-MPP in terms of throughput, and significantly outperforms (possible adaptations of) existing static or coarse-grained dynamic solutions, requiring 30%-60% less server capacity for similar service quality. Our STEAM prototype shows feasibility running on a standard server.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155404"}, {"primary_key": "2634300", "vector": [], "sparse_vector": [], "title": "Semi-distributed Contention-based Resource Allocation for Ultra Reliable Low Latency Communications.", "authors": ["<PERSON>", "Salah<PERSON><PERSON><PERSON>"], "summary": "Many Industrial Internet of Things (IIoT) use cases are characterized by a large number of users transmitting sporadically packets to a central controller. The transmitted packets have to be conveyed within a very short time so that it is not possible to make per-packet resource reservation, and contention-based access is needed, which is conflicting with the very stringent reliability constraints. In order to achieve both reliability and latency constraints, we consider in this paper the combination of contention-based access with blind replication of packets. Knowing the limited, but large, number of potential users in the system, we propose a semi-centralized channel access scheme where each user is pre-allocated positions for its replicas in case he has a packet to convey. We show, using coding theory, how to design sequences for users so that the number of collisions is minimized. We further exploit our pre-allocation scheme to develop a successive interference cancellation method where the base station tries to decode a packet based on the knowledge of the already decoded colliding packet. We show that the proposed schemes succeed to attain very low loss rates with low resource reservation.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155422"}, {"primary_key": "2634303", "vector": [], "sparse_vector": [], "title": "Towards Correlated Queries on Trading of Private Web Browsing History.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the commoditization of private data, data trading in consideration of user privacy protection has become a fascinating research topic. The trading for private web browsing histories brings huge economic value to data consumers when leveraged by targeted advertising. In this paper, we study the trading of multiple correlated queries on private web browsing history data. We propose TERBE, which is a novel trading framework for correlaTed quEries based on pRivate web Browsing historiEs. TERBE first devises a modified matrix mechanism to perturb query answers. It then quantifies privacy loss under the relaxation of classical differential privacy and a newly devised mechanism with relaxed matrix sensitivity, and further compensates data owners for their diverse privacy losses in a satisfying manner. Through real-data based experiments, our analysis and evaluation results demonstrate that TERBE balances total error and privacy preferences well within acceptable running time, and also achieves all desired economic properties of budget balance, individual rationality, and truthfulness.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155286"}, {"primary_key": "2634304", "vector": [], "sparse_vector": [], "title": "EncELC: Hardening and Enriching Ethereum Light Clients with Trusted Enclaves.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rapid growth of Ethereum blockchain has brought extremely heavy overhead for coin owners or developers to bootstrap and access transactions on Ethereum. To address this, light client is enabled, which only stores a small fraction of blockchain data and relies on bootstrapped full nodes for transaction retrievals. However, because the retrieval requests are outsourced, it raises several severe concerns about the integrity of returned results and the leakage of sensitive blockchain access histories, largely hindering the wider adoption of this important lightweight design. In addition to security issues, the continuously increasing blockchain storage also urges for more effective query functionalities for the Ethereum blockchain, so as to enable more flexible and precise transaction retrievals.In this paper, we propose EncELC, a new Ethereum light client design that enforces full-fledged protections for clients and enables rich queries over the Ethereum blockchain. EncELC leverages trusted hardware (e.g., Intel SGX) as a starting point for building efficient yet secure processing, and further crafts several crucial performance and security refinement designs to boost query efficiency and conceal leakages inside and outside SGX enclave. We implement a prototype of EncELC and test its performance in several real settings, and the results have confirmed the practicality of EncELC.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155385"}, {"primary_key": "2634305", "vector": [], "sparse_vector": [], "title": "Towards Context Address for Camera-to-Human Communication.", "authors": ["<PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Although existing surveillance cameras can identify people, their utility is limited by the unavailability of any direct camera-to-human communication. This paper proposes a real-time end-to-end system to solve the problem of digitally associating people in a camera view with their smartphones, without knowing the phones' IP/MAC addresses. The key idea is using a person's unique \"context features\", extracted from videos, as its sole address. The context address consists of: motion features, e.g. walking velocity; and ambience features, e.g. magnetic trend and Wi-Fi signal strengths. Once receiving a broadcast packet from the camera, a user's phone accepts it only if its context address matches the phone's sensor data. We highlight three novel components in our system: (1) definition of discriminative and noise-robust ambience features; (2) effortless ambient sensing map generation; (3) a context feature selection algorithm to dynamically choose lightweight yet effective features which are encoded into a fixed-length header. Real-world and simulated experiments are conducted for different applications. Our system achieves a sending ratio of 98 . 5%, an acceptance precision of 93 . 4%, and a recall of 98 . 3% with ten people. We believe this is a step towards direct camera-to-human communication and will become a generic underlay to various practical applications.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155342"}, {"primary_key": "2634308", "vector": [], "sparse_vector": [], "title": "PPGPass: Nonintrusive and Secure Mobile Two-Factor Authentication via Wearables.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile devices are promising to apply two-factor authentication in order to improve system security and enhance user privacy-preserving. Existing solutions usually have certain limits of requiring some form of user effort, which might seriously affect user experience and delay authentication time. In this paper, we propose PPGPass, a novel mobile two-factor authentication system, which leverages Photoplethysmography (PPG) sensors in wrist-worn wearables to extract individual characteristics of PPG signals. In order to realize both nonintrusive and secure, we design a two-stage algorithm to separate clean heartbeat signals from PPG signals contaminated by motion artifacts, which allows verifying users without intentionally staying still during the process of authentication. In addition, to deal with non-cancelable issues when biometrics are compromised, we design a repeatable and non-invertible method to generate cancelable feature templates as alternative credentials, which enables to defense against man-in-the-middle attacks and replay attacks. To the best of our knowledge, PPGPass is the first nonintrusive and secure mobile two-factor authentication based on PPG sensors in wearables. We build a prototype of PPGPass and conduct the system with comprehensive experiments involving multiple participants. PPGPass can achieve an average F1 score of 95.3%, which confirms its high effectiveness, security, and usability.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155380"}, {"primary_key": "2634309", "vector": [], "sparse_vector": [], "title": "On the Minimum Achievable Age of Information for General Service-Time Distributions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "There is a growing interest in analysing the freshness of data in networked systems. Age of Information (AoI) has emerged as a popular metric to quantify this freshness at a given destination. There has been a significant research effort in optimizing this metric in communication and networking systems under different settings. In contrast to previous works, we are interested in a fundamental question, what is the minimum achievable AoI in any single-server-single-source queuing system for a given service-time distribution? To address this question, we study a problem of optimizing AoI under service preemptions. Our main result is on the characterization of the minimum achievable average peak AoI (PAoI). We obtain this result by showing that a fixed-threshold policy is optimal in the set of all randomized-threshold causal policies. We use the characterization to provide necessary and sufficient conditions for the service-time distributions under which preemptions are beneficial.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155261"}, {"primary_key": "2634311", "vector": [], "sparse_vector": [], "title": "Systematic Topology Design for Large-Scale Networks: A Unified Framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For modern large-scale networked systems, ranging from cloud to edge computing systems, the topology design has a significant impact on the system performance in terms of scalability, cost, latency, throughput, and fault-tolerance. These performance metrics may conflict with each other and design criteria often vary across different networks. To date, there has been little theoretic foundation on topology designs from a prescriptive perspective, indicating that the current status quo of the design process is more of an art than a science. In this paper, we advocate a novel unified framework to describe, generate, and analyze topology design in a systematic fashion. By reverse-engineering existing topology designs and developing a fine-grained decomposition method for topology design, we propose a general procedure that serves as a common language to describe topology design. By proposing general criteria for the procedure, we devise a top-down approach to generate topology models, based on which we can systematically construct and analyze new topologies. To validate our approach, we leverage concrete tools based on combinatorial design theory and propose a novel layered topology model. With quantitative performance analysis, we reveal the trade-offs among performance metrics and generate new topologies with various advantages for different large-scale networks.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155462"}, {"primary_key": "2634315", "vector": [], "sparse_vector": [], "title": "Mneme: A Mobile Distributed Ledger.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pan Hui"], "summary": "Advances in mobile computing have paved the way for new types of distributed applications that can be executed solely by mobile devices on device-to-device (D2D) ecosystems (e.g., crowdsensing). More sophisticated applications, like cryptocurrencies, need distributed ledgers to function. Distributed ledgers, such as blockchains and directed acyclic graphs (DAGs), employ consensus protocols to add data in the form of blocks. However such protocols are designed for resourceful devices that are interconnected via the Internet. Moreover, existing distributed ledgers are not deployable to D2D ecosystems since their storage needs are continuously increasing. In this work, we introduce Mneme, a DAG-based distributed ledger that can be maintained solely by mobile devices and operates via two consensus protocols: Proof-of-Context (PoC) and Proof-of-Equivalence (PoE). PoC employs users' context to add data on Mneme. PoE is executed periodically to summarize data and produce equivalent blocks that require less storage. We analyze the security of Mneme and justify the ability of PoC and PoE to guarantee the characteristics of distributed ledgers: persistence and liveness. Furthermore, we analyze potential attacks from malicious users and prove that the probability of a successful attack is inversely proportional to the square of the number of mobile users who maintain Mneme.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155497"}, {"primary_key": "2634316", "vector": [], "sparse_vector": [], "title": "Turbo-HB: A Novel Design and Implementation to Achieve Ultra-Fast Hybrid Beamforming.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Chengzhang Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hybrid beamforming (HB) architecture has been widely recognized as the most promising solution to mmWave MIMO systems. A major practical challenge for HB is to obtain a solution in ~1 ms, which is an extremely stringent but necessary time requirement for its deployment in the field. In this paper, we present the design and implementation of Turbo-HB, codename for a novel beamforming design under the HB architecture that can obtain the beamforming matrices in about 1 ms. The key ideas in Turbo-HB include (i) reducing the complexity of SVD techniques by exploiting the limited number of channel paths at mmWave frequencies, and (ii) designing and implementing a parallelizable algorithm for a large number of matrix transformations. We validate Turbo-HB by implementing it on an off-the-shelf Nvidia GPU. Through extensive experiments, we show that Turbo-HB can meet ~1 ms timing requirement while delivering competitive throughput performance compared to state-of-the-art algorithms.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155337"}, {"primary_key": "2634322", "vector": [], "sparse_vector": [], "title": "Learning Optimal Sniffer Channel Assignment for Small Cell Cognitive Radio Networks.", "authors": ["<PERSON><PERSON> Chen", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "To cope with the exploding mobile traffic in the fifth generation cellular network, the dense deployment of small cells and cognitive radios are two key technologies that significantly increase the network capacity and improve the spectrum utilization efficiency. Despite the desirable features, small cell cognitive radio networks (SCRNs) also face a higher risk of unauthorized spectrum access, which should not be overlooked. In this paper, we consider a passive monitoring system for SCRNs, which deploys sniffers for wireless traffic capture and network forensics, and study the optimal sniffer channel assignment (SCA) problem to maximize the monitoring performance. Unlike most existing SCA approaches that concentrate on user activity, we highlight the inherent error in wireless data capture (i.e. imperfect monitoring) due to the unreliable nature of wireless propagation, and propose an online-learning based algorithm called OSA (Online Sniffer-channel Assignment). OSA is a type of contextual combinatorial multi-armed bandit learning algorithm, which addresses key challenges in SCRN monitoring including the time- varying spectrum resource, imperfect monitoring, and uncertain network conditions. We theoretically prove that OSA has a sublinear learning regret bound and illustrate via simulations that OSA significantly outperforms benchmark solutions.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155431"}, {"primary_key": "2634325", "vector": [], "sparse_vector": [], "title": "TagRay: Contactless Sensing and Tracking of Mobile Objects using COTS RFID Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "RFID technology has recently been exploited for not only identification but also for sensing including trajectory tracking and gesture recognition. While contact-based (an RFID tag is attached to the target of interest) sensing has achieved promising results, contactless sensing still faces severe challenges such as low accuracy and the situation gets even worse when the target is non-static, restricting its applicability in real world deployment. In this work, we present TagRay, a contactless RFID-based sensing system, which significantly improves the tracking accuracy, enabling mobile object tracking and even material identification. We design and implement our prototype on commodity RFID device. Comprehensive experiments show that TagRay achieves a high accuracy of 1.3 cm which is a 200% improvement over the-state-of-arts for trajectory tracking. For commonly seen four material types, the material identification accuracy is higher than 95% even with interference from people moving around.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155531"}, {"primary_key": "2634326", "vector": [], "sparse_vector": [], "title": "Global Cooperation for Heterogeneous Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Industrial Scientific Medical (ISM) band has become more and more crowded due to the ever-growing size of many mainstream technologies, e.g., Wi-Fi, ZigBee and Bluetooth. Though they compete for limited spectrum resources leading to severe Cross Technology Interference (CTI), it also provides great opportunities to better utilize the scarce bandwidth resources. A fundamental question is how to ensure harmonious and effective operations for these networks? To exploit this issue, a novel global cooperation framework is proposed. In particular, our work enables direct and simultaneous Cross Technology Communication (CTC) from a single Wi-Fi to ZigBee, Bluetooth and Wi-Fi commodity devices sharing the same band. Compared to existing CTC approaches, our scheme improves the communication efficiency significantly, and hence is the foundation for effective global cooperation. Based on the proposed CTC scheme, a unified Media Access Control (MAC) approach is introduced to cooperate CTC message transmission and reception for heterogeneous devices with different MACs. Two proof-of-concepts applications, e.g. global synchronization and global CTI coordination are discussed to fully leverage the benefits of global cooperation. Extensive evaluations show that compared with existing schemes, the proposed framework achieves 13 times lower synchronization error and 9 times lower average packet delay in CTI intensive environments.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155453"}, {"primary_key": "2634327", "vector": [], "sparse_vector": [], "title": "A Lightweight Auction Framework for Spectrum Allocation with Strong Security Guarantees.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Auction is an effective mechanism to distribute spectrum resources. Although many privacy-preserving auction schemes for spectrum allocation have been proposed, none of them is able to perform practical spectrum auctions while ensuring enough security for bidders' private information, such as geo-locations, bid values, and data access patterns. To address this problem, we propose SLISA, a lightweight auction framework which enables an efficient spectrum allocation without revealing anything but the auction outcome, i.e., the winning bidders and their clearing prices. We present contributions on two fronts. First, as a foundation of our design, we adopt a Shuffle-then-Compute strategy to build a series of secure sub-protocols based on lightweight cryptographic primitives (e.g., additive secret sharing and basic garbled circuits). Second, we improve an advanced spectrum auction mechanism to make it data-oblivious, such that data access patterns can be hidden. Meanwhile, the modified protocols adapt to our elaborate building blocks without affecting its validity and security. We formally prove the security of all protocols under a semi-honest adversary model, and demonstrate performance improvements compared with state-of-the-art works through extensive experiments.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155279"}, {"primary_key": "2634328", "vector": [], "sparse_vector": [], "title": "AcouRadar: Towards Single Source based Acoustic Localization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Acoustic based tracking has been shown promising in many applications like Virtual Reality, smart home, video gaming, etc. Its real life deployments, however, face fundamental limitations. Existing approaches generally need three sound sources, while most COTS devices (e.g., TVs) and speakers have only two sound sources. We present AcouRadar, an acoustic-based localization system with single sound source. In the heart of AcouRadar we adopt a general new model which quantifies signal properties of different frequencies, distances and angles to the source. We verify the model and show that signal from a single source can provide features for localization. To address practical challenges, (1) we design an online model adaption method to address model deviation from real signal, (2) we design pulse modulated signals to alleviate the impact of environment such as multipath effect, and (3) to address signal dynamics over time, we derive relatively stable amplitude ratio between different frequencies, and thus provide a spectrum based localization method. We implement AcouRadar on Android and evaluate its performance for different COTS speakers in different environments. The results show that the model for localization can be generalized to different speakers. AcouRadar can achieve single source localization with average error less than 5 cm and average angle error of 1.76°.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155430"}, {"primary_key": "2634330", "vector": [], "sparse_vector": [], "title": "Stealthy DGoS Attack: DeGrading of Service under the Watch of Network Tomography.", "authors": ["<PERSON><PERSON><PERSON>", "Ting <PERSON>"], "summary": "Network tomography is a powerful tool to monitor the internal state of a closed network that cannot be measured directly, with broad applications in the Internet, overlay networks, and all-optical networks. However, existing network tomography solutions all assume that the measurements are trust-worthy, leaving open how effective they are in an adversarial environment with possibly manipulated measurements. To understand the fundamental limit of network tomography in such a setting, we formulate and analyze a novel type of attack that aims at maximally degrading the performance of targeted paths without being localized by network tomography. By analyzing properties of the optimal attack, we formulate novel combinatorial optimizations to design the optimal attack strategy, which are then linked to well-known problems and approximation algorithms. Our evaluations on real topologies demonstrate the large damage of such attacks, signaling the need of new defenses.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155344"}, {"primary_key": "2634332", "vector": [], "sparse_vector": [], "title": "Simple and Fast Distributed Computation of Betweenness Centrality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Betweenness centrality is a graph parameter that has been successfully applied to network analysis. In the context of computer networks, it was considered for various objectives, ranging from routing to service placement. However, as observed by <PERSON><PERSON><PERSON> et al. [INFOCOM 2018], research on betweenness centrality for improving protocols was hampered by the lack of a usable, fully distributed algorithm for computing this parameter. We resolve this issue by designing an efficient algorithm for computing betweenness centrality, which can be implemented by minimal modifications to any distance-vector routing protocol based on Bellman-Ford. The convergence time of our implementation is shown to be proportional to the diameter of the network.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155354"}, {"primary_key": "2634333", "vector": [], "sparse_vector": [], "title": "Placing Wireless Chargers with Limited Mobility.", "authors": ["<PERSON>peng Dai", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies the problem of Placing directional wIreless chargers with Limited mObiliTy (PILOT), that is, given a budget of mobile directional wireless chargers and a set of static rechargeable devices on a 2D plane, determine deployment positions, stop positions and orientations, and portions of time for all chargers such that overall charging utility of all devices can be maximized. To the best of our knowledge, we are the first to study placement of mobile chargers. To address PILOT, we propose a (1/2 - ε)-approximation algorithm. First, we present a method to approximate nonlinear charging power of chargers, and further propose an approach to construct Maximal Covered Set uniform subareas to reduce the infinite continuous search space for stop positions and orientations to a finite discrete one. Second, we present geometrical techniques to further reduce the infinite solution space for candidate deployment positions to a finite one without performance loss, and transform PILOT to a mixed integer nonlinear programming problem. Finally, we propose a linear programming based greedy algorithm to address it. Simulation and experimental results show that our algorithm outperforms five comparison algorithms by 31.33% ~ 281.10%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155356"}, {"primary_key": "2634335", "vector": [], "sparse_vector": [], "title": "SIMBA: Single RF Chain Multi-User Beamforming in 60 GHz WLANs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Multi-user transmission in 60 GHz Wi-Fi can achieve data rates up to 100 Gbps by multiplexing multiple user data streams. However, a fundamental limit in the approach is that each RF chain is limited to supporting one stream or one user. To overcome this limit, we propose SIngle RF chain Multiuser BeAmforming (SIMBA), a novel framework for multi-stream multi-user downlink transmission via a single RF chain. We build on single beamformed transmission via overlayed constellations to multiplex multiple users' modulated symbols such that grouped users at different locations can share the same transmit beam from the AP. For this, we introduce user grouping and beam selection policies that span tradeoffs in data rate, training and computation overhead. We implement a programmable WLAN testbed using software-defined radios and commercial 60-GHz transceivers and collect over-the-air measurements using phased array antennas and horn antennas with varying beamwidth. We find that in comparison to single user transmissions, SIMBA achieves 2× improvement in aggregate rate and two-fold delay reduction for simultaneous transmission to four users.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155441"}, {"primary_key": "2634336", "vector": [], "sparse_vector": [], "title": "Streaming 360-Degree Videos Using Super-Resolution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Santiago Vargas", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "360° videos provide an immersive experience to users, but require considerably more bandwidth to stream compared to regular videos. State-of-the-art 360° video streaming systems use viewport prediction to reduce bandwidth requirement, that involves predicting which part of the video the user will view and only fetching that content. However, viewport prediction is error prone resulting in poor user Quality of Experience (QoE). We design PARSEC, a 360° video streaming system that reduces bandwidth requirement while improving video quality. PARSEC trades off bandwidth for additional client-side computation to achieve its goals. PARSEC uses an approach based on super-resolution, where the video is significantly compressed at the server and the client runs a deep learning model to enhance the video to a much higher quality. PARSEC addresses a set of challenges associated with using super-resolution for 360° video streaming: large deep learning models, slow inference rate, and variance in the quality of the enhanced videos. To this end, PAR-SEC trains small micro-models over shorter video segments, and then combines traditional video encoding with super-resolution techniques to overcome the challenges. We evaluate PARSEC on a real WiFi network, over a broadband network trace released by FCC, and over a 4G/LTE network trace. PARSEC significantly outperforms the state-of-art 360° video streaming systems while reducing the bandwidth requirement.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155477"}, {"primary_key": "2634337", "vector": [], "sparse_vector": [], "title": "PASID: Exploiting Indoor mmWave Deployments for Passive Intrusion Detection.", "authors": ["<PERSON>", "<PERSON>", "Ilario Fi<PERSON>i", "<PERSON>"], "summary": "As 5G deployments start to roll-out, indoor solutions are increasingly pressed towards delivering a similar user experience. Wi-Fi is the predominant technology of choice indoors and major vendors started addressing this need by incorporating the mmWave band to their products. In the near future, mmWave devices are expected to become pervasive, opening up new business opportunities to exploit their unique properties.In this paper, we present a novel PASsive Intrusion Detection system, namely PASID, leveraging on already deployed indoor mmWave communication systems. PASID is a software module that runs in off-the-shelf mmWave devices. It automatically models indoor environments in a passive manner by exploiting regular beamforming alignment procedures and detects intruders with a high accuracy. We model this problem analytically and show that for dynamic environments machine learning techniques are a cost-efficient solution to avoid false positives. PASID has been implemented in commercial off-the-shelf devices and deployed in an office environment for validation purposes. Our results show its intruder detection effectiveness (~99% accuracy) and localization potential (~ 2 meters range) together with its negligible energy increase cost (~ 2%).", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155401"}, {"primary_key": "2634340", "vector": [], "sparse_vector": [], "title": "Scheduling for Weighted Flow and Completion Times in Reconfigurable Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "New optical technologies offer the ability to reconfigure network topologies dynamically, rather than setting them once and for all. This is true in both optical wide area networks (optical WANs) and in datacenters, despite the many differences between these two settings. Because of these new technologies, there has been a surge of both practical and theoretical research on algorithms to take advantage of them. In particular, <PERSON><PERSON> et al. [INFOCOM '17] designed online scheduling algorithms for dynamically reconfigurable topologies for both the makespan and sum of completion times objectives. In this paper, we work in the same setting but study an objective that is more meaningful in an online setting: the sum of flow times. The flow time of a job is the total amount of time that it spends in the system, which may be considerably smaller than its completion time if it is released late. We provide competitive algorithms for the online setting with speed augmentation, and also give a lower bound proving that speed augmentation is in fact necessary. As a side effect of our techniques, we also improve and generalize the results of <PERSON><PERSON> et al. on completion times by giving an O(1)-competitive algorithm for arbitrary sizes and release times even when nodes have different degree bounds, and moreover allow for the weighted sum of completion times (or flow times).", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155537"}, {"primary_key": "2634341", "vector": [], "sparse_vector": [], "title": "A Longitudinal View of Netflix: Content Delivery over IPv6 and Content Cache Deployments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present an active measurement test (netflix) that downloads content from the Netflix content delivery network. The test measures latency and achievable throughput as key performance indicators when downloading the content from Netflix. We deployed the test on ~100 SamKnows probes connected to dual-stacked networks representing 74 different origin ASes. Using a ~2.75 year-long (Jul 2016-Apr 2019) dataset, we observe Netflix Open Connect Appliance (OCA) infrastructure to be highly available, although some vantage points experience low success rates connecting over IPv6. We witness that clients prefer connecting to Netflix OCAs over IPv6, although the preference over IPv6 tends to drop over certain peak hours during the day. The TCP connect times toward the OCAs have reduced by ~40% and the achievable throughput has increased by 20% over the measurement duration. We also provision scamper right after the netflix test to capture the forwarding path toward the Netflix OCAs. We observe that the Netflix OCA caches deployed inside the ISP are reachable within six IP hops and can reduce IP path lengths by 40% over IPv4 and by half over IPv6. Consequently, TCP connect times are reduced by 64% over both address families. The achieved throughput can~ also increase by a factor of three when such ISP caches are used to stream content. This is the first study to measure Netflix content delivery from residential networks, since the inception of the Netflix CDN infrastructure in 2011. To encourage reproducibility of our work, an anonymized version of the entire longitudinal dataset is publicly released.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155367"}, {"primary_key": "2634343", "vector": [], "sparse_vector": [], "title": "Towards Primary User Sybil-proofness for Online Spectrum Auction in Dynamic Spectrum Access.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Qing<PERSON><PERSON> Yao", "Di <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic spectrum access (DSA) is a promising platform to solve the spectrum shortage problem, in which auction based mechanisms have been extensively studied due to good spectrum allocation efficiency and fairness. Recently, Sybil attacks were introduced in DSA, and Sybil-proof spectrum auction mechanisms have been proposed, which guarantee that each single secondary user (SU) cannot obtain a higher utility under more than one fictitious identities. However, existing Sybil-poof spectrum auction mechanisms achieve only Sybil-proofness for SUs, but not for primary users (PUs), and simulations show that a cheating PU in those mechanisms can obtain a higher utility by Sybil attacks. In this paper, we propose TSUNAMI, the first Truthful and primary user Sybil-proof aUctioN mechAnisM for onlIne spectrum allocation. Specifically, we compute the opportunity cost of each SU and screen out cost-efficient SUs to participate in spectrum allocation. In addition, we present a bid-independent sorting method and a sequential matching approach to achieve primary user Sybil-proofness and 2-D truthfulness, which means that each SU or PU can gain her maximal utility by bidding with her true valuation of spectrum. We evaluate the performance and validate the desired properties of our proposed mechanism through extensive simulations.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155419"}, {"primary_key": "2634344", "vector": [], "sparse_vector": [], "title": "TINA: A Fair Inter-datacenter Transmission Mechanism with Deadline Guarantee.", "authors": ["Xiaodong Dong", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Geographically distributed cloud is a promising technique to achieve high performance for service providers. For inter-datacenter transfers, deadline guarantee and fairness are the two most important requirements. On the one hand, to ensure more transfers finish before their deadlines, preemptive scheduling policies are widely used, leading to the transfer starvation problem and is hence unfair. On the other hand, to ensure fairness, inter-datacenter bandwidth is fairly shared among transfers with per-flow bandwidth allocation, which leads to deadline missing problem. A mechanism that achieves these two seemingly conflicting objectives simultaneously is still missing. In this paper, we propose TINA to schedule network transfers fairly while providing deadline guarantees. TINA allows each transfer to compete freely with each other for bandwidth. More specifically, each transfer is assigned a probability to indicate whether to transmit or not. We formulate the competition among the transfers as an El Farol game while keeping the traffic load under a threshold to avoid congestion. We then prove that the Nash Equilibrium is the optimal strategy and propose a light-weight algorithm to derive it. Finally, both simulations and testbed experiments results show that TINA achieves superior performance than state-of-art methods in terms of fairness and deadline guarantee rate.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155410"}, {"primary_key": "2634346", "vector": [], "sparse_vector": [], "title": "Enabling RFID-Based Tracking for Multi-Objects with Visual Aids: A Calibration-Free Solution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Identification and tracking of multiple objects are essential in many applications. As a key enabler of automatic ID technology, RFID has got widespread adoption with item-level tagging in everyday life. However, restricted to the computation capability of passive RFID systems, locating or tracking tags has always been a challenging task. Meanwhile, as a fundamental problem in the field of computer vision, object tracking in images has progressed to a remarkable state especially with the rapid development of deep learning in the past few years. To enable lightweight tracking of a specific target, researchers try to complement computer vision to existing RFID architecture and achieves fine granularity. However, such solution requires calibration of the cameras extrinsic parameters at each new setup, which is not convenient for usage. In this work, we propose Tagview, a pervasive identifying and tracking system that can work in various settings without repetitive calibration efforts. It addresses the challenge by skillfully deploying the RFID antenna and video camera at the identical position and devising a multi-target recognition schema with only the image-level trajectory information. We have implemented Tagview with commercial RFID and camera devices and evaluated it extensively. Experimental results show that our method can archive high accuracy and robustness.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155355"}, {"primary_key": "2634347", "vector": [], "sparse_vector": [], "title": "Is Deadline Oblivious Scheduling Efficient for Controlling Real-Time Traffic in Cellular Downlink Systems?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "The emergence of bandwidth-intensive latency-critical traffic in 5G Networks, such as Virtual Reality and Cloud Gaming, has motivated interest in wireless resource allocation problems for flows with hard-deadlines. Attempting to solve this problem brings about the following two key challenges: (i) The flow arrival and the wireless channel state information are not known to the Base Station (BS) apriori, thus, the allocation decisions need to be made in an online manner. (ii) Resource allocation algorithms that attempt to maximize a reward in the wireless setting will likely be unfair, causing unacceptable service for some users. In the first part of this paper, we model the problem of allocating resources to deadline-sensitive traffic as an online convex optimization problem, where the BS acquires a per-request reward that depends on the amount of traffic transmitted within the required deadline. We address the question of whether we can efficiently solve that problem with low complexity. In particular, whether we can design a constant-competitive scheduling algorithm that is oblivious to requests' deadlines. To this end, we propose a primal-dual Deadline-Oblivious (DO) algorithm, and show it is approximately 3.6-competitive. Furthermore, we show via simulations that our algorithm tracks the prescient offline solution very closely, significantly outperforming several algorithms that were previously proposed. Our results demonstrate that even though a scheduler may not know the deadlines of each flow, it can still achieve good theoretical and empirical performance. In the second part, we impose a stochastic constraint on the allocation, requiring a guarantee that each user achieves a certain timely throughput (amount of traffic delivered within the deadline over a period of time). We propose a modified version of our algorithm, called the Long-term Fair Deadline Oblivious (LFDO) algorithm for that setup. We combine the Lyapunov framework for stochastic optimization with the Primal-Dual analysis of online algorithms, to show that LFDO retains the high-performance of DO, while satisfying the long-term stochastic constraints.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155523"}, {"primary_key": "2634348", "vector": [], "sparse_vector": [], "title": "Eagle: Refining Congestion Control by Learning from the Experts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traditional congestion control algorithms were designed with a hardwired heuristic mapping between packet- level events and predefined control actions in response to these events, and may fail to satisfy all the desirable performance goals as a result. In this paper, we seek to reconsider these fundamental goals in congestion control, and propose Eagle, a new congestion control algorithm to refine existing heuristics. <PERSON> takes advantage of expert knowledge from an existing algorithm, and uses deep reinforcement learning (DRL) to train a generalized model with the hope of learning from an expert. Learning by trial-and-error may not be as efficient as imitating a teacher; by the same token, DRL alone is not enough to guarantee good performance. In Eagle, we seek help from an expert congestion control algorithm, BBR, to help us train a long-short term memory (LSTM) neural network in the DRL agent, with the hope of making decisions that can be as good as or even better than the expert. With an extensive array of experiments, we discovered that Eagle is able to match and even outperform the performance of its teacher, and outperformed a large number of recent congestion control algorithms by a considerable margin.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155250"}, {"primary_key": "2634350", "vector": [], "sparse_vector": [], "title": "A Structured Bidirectional LSTM Deep Learning Method For 3D Terahertz Indoor Localization.", "authors": ["Shukai Fan", "<PERSON><PERSON>", "Chong Han", "<PERSON><PERSON>"], "summary": "High-accuracy localization technology has gained increasing attention in gesture and motion control and many diverse applications. Due to the shadowing, multi-path fading, blockage effects in indoor propagation, 0.1m-level precise localization is still challenging. Promising for 6G wireless communications, the Terahertz (THz) spectrum provides ultra-broad bandwidth for indoor applications. Applying to indoor localization, the channel state information (CSI) of THz wireless signals, including angle of arrival (AoA), received power, and delay, has unprecedented resolution that can be explored for positioning. In this paper, a Structured Bidirectional Long Short-term Memory (SBi-LSTM) recurrent neural network (RNN) architecture is proposed to solve the CSI-based three-dimensional (3D) THz indoor localization problem with significantly improved accuracy. As a two-level structure, the features of individual multi-path ray are first analyzed in the Bi-LSTM network at the base level. Furthermore, the upper level residual network (ResNet) of the constructed SBi-LSTM network extracts for the geometric coordinates. Simulation results validate the convergence of our SBi-LSTM method and the robustness against indoor non-line-of-sight (NLoS) blockage. Specifically, the localization accuracy in the metric of mean distance error is within 0.27m under the NLoS environment, which demonstrates 60% enhancement over the state-of-the-art techniques.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155458"}, {"primary_key": "2634352", "vector": [], "sparse_vector": [], "title": "When Reputation Meets Subsidy: How to Build High Quality On Demand Service Platforms.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A widely adopted approach to guarantee high-quality services on on-demand service platforms is to introduce a reputation system, where good reputation workers will receive a bonus for providing high-quality services. In this paper, we propose a general reputation framework motivated by various practical examples. Our model captures the evolution of a reputation system, jointly considering worker's strategic behaviors and imperfect customer reviews that are usually studied separately before. We characterize the stationary equilibrium of the market, in particular, the existence and uniqueness of a non-trivial equilibrium that ensures high-quality services. Furthermore, we propose an efficient subsidization mechanism that helps induce high-quality services on the platform, and show the market convergence to the high service quality equilibrium under such a mechanism.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155351"}, {"primary_key": "2634357", "vector": [], "sparse_vector": [], "title": "SDN-based Order-aware Live Migration of Virtual Machines.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Live migration is a key technique to transfer virtual machines (VMs) from one machine to another. Often multiple VMs need to be migrated in response to events such as server maintenance, load balancing, and impending failures. However, VM migration is a resource intensive operation that pressures the CPU, memory, and network resources of the source and destination hosts as well as intermediate network links. The live migration mechanism ends up contending for finite resources with the VMs that it needs to migrate, which prolongs the total migration time and worsens the performance of applications running inside the VMs. In this paper, we propose SOLive, a new approach to reduce resource contention between the migration process and the VMs being migrated. First, by considering the nature of VM workloads, SOLive manages the order in which multiple VMs are migrated to significantly reduce the total mi-gration time. Secondly, to reduce the network contention between the migration process and the VMs, SOLive uses a combination of software-defined networking-based resource reservation and scatter gather-based VM migration to quickly deprovision the source host. A prototype implementation of our approach in KVM/QEMU platform shows that SOLive quickly evicts VMs from the source host with low impact on VMs' performance.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155415"}, {"primary_key": "2634358", "vector": [], "sparse_vector": [], "title": "Tiny Tasks - A Remedy for Synchronization Constraints in Multi-Server Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Models of parallel processing systems typically assume that one has l servers and jobs are split into an equal number of k = l tasks. This seemingly simple approximation has surprisingly large consequences for the resulting stability and performance bounds. In reality, best practices for modern mapreduce systems indicate that a job's partitioning factor should be much larger than the number of servers available, with some researchers going to far as to advocate for a \"tiny tasks\" regime, where jobs are split into over 10,000 tasks. In this paper we use recent advances in stochastic network calculus to fundamentally understand the effects of task granularity on parallel systems' scaling, stability, and performance. For the split-merge model, we show that when one allows for tiny tasks, the stability region is actually much better than had previously been concluded. For the single-queue fork-join model, we show that sojourn times quickly approach the optimal case when l \"big tasks\" are subdivided into k≫ l \"tiny tasks\". Our results are validated using extensive simulations, and the applicability of the models used is validated by experPiments on an Apache Spark cluster.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155368"}, {"primary_key": "2634359", "vector": [], "sparse_vector": [], "title": "PAM &amp; PAL: Policy-Aware Virtual Machine Migration and Placement in Dynamic Cloud Data Centers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We focus on policy-aware data centers (PADCs), wherein virtual machine (VM) traffic traverses a sequence of middleboxes (MBs) for security and performance purposes, and propose two new VM placement and migration problems. We first study PAL: policy-aware virtual machine placement. Given a PADC with a data center policy that communicating VM pairs must satisfy, the goal of PAL is to place the VMs into the PADC to minimize their total communication cost. Due to dynamic traffic loads in PADCs, however, above VM placement may no longer be optimal after some time. We thus study PAM: policy-aware virtual machine migration. Given an existing VM placement in the PADC and dynamic traffic rates among communicating VMs, PAM migrates VMs in order to minimize the total cost of migration and communication of the VM pairs. We design optimal, approximation, and heuristic policyaware VM placement and migration algorithms. Our experiments show that i) VM migration is an effective technique, reducing total communication cost of VM pairs by 25%, ii) our PAL algorithms outperform state-of-the-art VM placement algorithm that is oblivious to data center policies by 40-50%, and iii) our PAM algorithms outperform the only existing policy-aware VM migration scheme by 30%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155472"}, {"primary_key": "2634363", "vector": [], "sparse_vector": [], "title": "Clustering-preserving Network Flow Sketching.", "authors": ["<PERSON><PERSON><PERSON>", "Dongsheng Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Network monitoring is vital in modern clouds and data center networks that need diverse traffic statistics ranging from flow size distributions to heavy hitters. To cope with increasing network rates and massive traffic volumes, sketch based approximate measurement has been extensively studied to trade the accuracy for memory and computation cost, which unfortunately, is sensitive to hash collisions.This paper presents a clustering-preserving sketch method to be resilient to hash collisions. We provide an equivalence analysis of the sketch in terms of the K-means clustering. Based on the analysis result, we cluster similar network flows to the same bucket array to reduce the estimation variance and use the average to obtain unbiased estimation. Testbed shows that the framework adapts to line rates and provides accurate query results. Real-world trace-driven simulations show that LSS remains stable performance under wide ranges of parameters and dramatically outperforms state-of-the-art sketching structures, with over 10 3 to 10 5 times reduction in relative errors for per-flow queries as the ratio of the number of buckets to the number of network flows reduces from 10% to 0.1%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155388"}, {"primary_key": "2634364", "vector": [], "sparse_vector": [], "title": "Combinatorial Multi-Armed Bandit Based Unknown Worker Recruitment in Heterogeneous Crowdsensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile crowdsensing, through which a requester can coordinate a crowd of workers to complete some sensing tasks, has attracted significant attention recently. In this paper, we focus on the unknown worker recruitment problem in mobile crowdsensing, where workers' sensing qualities are unknown a priori. We consider the scenario of recruiting workers to complete some continuous sensing tasks. The whole process is divided into multiple rounds. In each round, every task may be covered by more than one recruited workers, but its completion quality only depends on these workers' maximum sensing quality. Each recruited worker will incur a cost, and each task is attached a weight to indicate its importance. Our objective is to determine a recruiting strategy to maximize the total weighted completion quality under a limited budget. We model such an unknown worker recruitment process as a novel combinatorial multi-armed bandit problem, and propose an extended UCB based worker recruitment algorithm. Moreover, we extend the problem to the case where the workers' costs are also unknown and design the corresponding algorithm. We analyze the regrets of the two proposed algorithms and demonstrate their performance through extensive simulations on real-world traces.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155518"}, {"primary_key": "2634368", "vector": [], "sparse_vector": [], "title": "POLAR: Passive object localization with IEEE 802.11ad using phased antenna arrays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Millimeter-wave systems not only provide high data rates and low latency, but the very large bandwidth also allows for highly accurate environment sensing. Such properties are extremely useful for smart factory scenarios. At the same time, reusing existing communication links for passive object localization is significantly more challenging than radar-based approaches due to the sparsity of the millimeter-wave multi-path environment and the weakness of the reflected paths compared to the line-of-sight path. In this paper, we explore the passive object localization accuracy that can be achieved with IEEE 802.11ad devices. We use commercial Access Points (APs) whereas the station design is based on a full-bandwidth 802.11ad compatible FPGA-based platform with a phased antenna array. The stations exploit the preamble of the beam training packets of the APs to obtain Channel Impulse Response (CIR) measurements for all antenna patterns. With this, we determine distance and angle information for the different multi-path components in the environment to passively localize a mobile object. We evaluate our system with multiple APs and a moving robot with a metallic surface. Our system operates in real-time and achieves 6.5cm mean error accuracy and sub-meter accuracy in 100% of the cases.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155383"}, {"primary_key": "2634369", "vector": [], "sparse_vector": [], "title": "Similarity Caching: Theory and Algorithms.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper focuses on similarity caching systems, in which a user request for an object o that is not in the cache can be (partially) satisfied by a similar stored object o', at the cost of a loss of user utility. Similarity caching systems can be effectively employed in several application areas, like multimedia retrieval, recommender systems, genome study, and machine learning training/serving. However, despite their relevance, the behavior of such systems is far from being well understood. In this paper, we provide a first comprehensive analysis of similarity caching in the offline, adversarial, and stochastic settings. We show that similarity caching raises significant new challenges, for which we propose the first dynamic policies with some optimality guarantees. We evaluate the performance of our schemes under both synthetic and real request traces.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155221"}, {"primary_key": "2634372", "vector": [], "sparse_vector": [], "title": "Exploring the interplay between CDN caching and video streaming performance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Content Delivery Networks (CDNs) are critical for optimizing Internet video delivery. In this paper, we characterize how CDNs serve video content, and the implications for video performance, especially emerging 4K video streaming. Our work is based on measurements of multiple well known video publishers served by top CDNs. Our results show that (i) video chunks in a session often see heterogeneous behavior in terms of whether they hit in the CDN, and which layer they are served from; and (ii) application throughput can vary significantly even across chunks in a session based on where they are served from. The differences while sensitive to client location and CDN can sometimes be significant enough to impact the viability of 4K streaming. We consider the implications for Adaptive bit rate (ABR) algorithms given they rely on throughput prediction, and are agnostic to whether objects hit in the CDN and where. We evaluate the potential benefits of exposing where a video chunk is served from to the client ABR algorithm in the context of the widely studied model predictive control (MPC) algorithm. Emulation experiments show the approach has the potential to reduce prediction inaccuracies, and enhance video streaming performance.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155338"}, {"primary_key": "2634373", "vector": [], "sparse_vector": [], "title": "Differentially Private Range Counting in Planar Graphs for Spatial Sensing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper considers the problem of privately reporting counts of events recorded by devices in different regions of the plane. Unlike previous range query methods, our approach is not limited to rectangular ranges. We devise novel hierarchical data structures to answer queries over arbitrary planar graphs. This construction relies on balanced planar separators to represent shortest paths using O(logn) number of canonical paths, where n is the number of nodes in the graph. Pre-computed sums along these canonical paths allow efficient computations of 1D counting range queries along any shortest path. We make use of differential forms together with the 1D mechanism to answer 2D queries in which a range is a union of faces in the planar graph. The methods are designed such that the range queries could be answered with differential privacy guarantee on any single event, with only a poly-logarithmic error. They also allow private range queries to be performed in a distributed setup. Theoretical and experimental results confirm that the methods are efficient and accurate on real data and incur less error than competing existing methods.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155480"}, {"primary_key": "2634376", "vector": [], "sparse_vector": [], "title": "Delay-Optimal Distributed Edge Computing in Wireless Edge Networks.", "authors": ["<PERSON><PERSON>"], "summary": "By integrating edge computing with parallel computing, distributed edge computing (DEC) makes use of distributed devices in edge networks to perform computing in parallel, which can substantially reduce service delays. In this paper, we explore DEC that exploits distributed edge devices connected by a wireless network to perform a computation task offloaded from an end device. In particular, we study the fundamental problem of minimizing the delay of executing a distributed algorithm of the computation task. We first establish some structural properties of the optimal communication scheduling policy. Then, given these properties, we characterize the optimal computation allocation policy, which can be found by an efficient algorithm. Next, based on the optimal computation allocation, we characterize the optimal scheduling order of communications for some special cases, and develop an efficient algorithm with a finite approximation ratio to find it for the general case. Last, based on the optimal computation allocation and communication scheduling, we further show that the optimal selection of devices can be found efficiently for some special cases. Our results provide some useful insights for the optimal computation-communication codesign. We evaluate the performance of the theoretical findings using simulations.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155272"}, {"primary_key": "2634378", "vector": [], "sparse_vector": [], "title": "IoTGaze: IoT Security Enforcement via Wireless Context Analysis.", "authors": ["Tianbo Gu", "<PERSON>", "Allaukik Abhishek", "Hao Fu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Internet of Things (IoT) has become the most promising technology for service automation, monitoring, and interconnection, etc. However, the security and privacy issues caused by IoT arouse concerns. Recent research focuses on addressing security issues by looking inside platform and apps. In this work, we creatively change the angle to consider security problems from a wireless context perspective. We propose a novel framework called IoTGaze, which can discover potential anomalies and vulnerabilities in the IoT system via wireless traffic analysis. By sniffing the encrypted wireless traffic, IoTGaze can automatically identify the sequential interaction of events between apps and devices. We discover the temporal event dependencies and generate the Wireless Context for the IoT system. Meanwhile, we extract the IoT Context, which reflects user's expectation, from IoT apps' descriptions and user interfaces. If the wireless context does not match the expected IoT context, IoTGaze reports an anomaly. Furthermore, IoTGaze can discover the vulnerabilities caused by the inter-app interaction via hidden channels, such as temperature and illuminance. We provide a proof-of-concept implementation and evaluation of our framework on the Samsung SmartThings platform. The evaluation shows that IoTGaze can effectively discover anomalies and vulnerabilities, thereby greatly enhancing the security of IoT systems.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155459"}, {"primary_key": "2634379", "vector": [], "sparse_vector": [], "title": "PERM: Neural Adaptive Video Streaming with Multi-path Transmission.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ling<PERSON> Song"], "summary": "The multi-path transmission techniques enable multiple paths to maximize resource usage and increase throughput in transmission, which have been installed over mobile devices in recent years. For video streaming applications, compared to the single-path transmission, the multi-path techniques can establish multiple subflows simultaneously to extend the available bandwidth for streaming high-quality videos in mobile devices. Existing adaptive video streaming systems have difficulty in harnessing multi-path scheduling and balancing the tradeoff between the quality of experience (QoE) and quality of service (QoS) concerns. In this paper, we propose an actor-critic network based on Periodical Experience Replay for Multi-path video streaming (PERM). Specifically, PERM employs two actor modules and a critic module: the two actor modules respectively assign the path usage of each subflow and select bitrates for the next chunk of the video, while the critic module predicts the overall objectives. We conduct trace-driven emulation and real-world testbed experiment to examine the performance of PERM, and results show that PERM outperforms state-of-the-art multi-path and single path streaming systems, with an improvement of 10%- 15% on the QoE and QoS metrics.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155492"}, {"primary_key": "2634381", "vector": [], "sparse_vector": [], "title": "Scheduling Placement-Sensitive BSP Jobs with Inaccurate Execution Time Estimation.", "authors": ["Zhenhua Han", "Haisheng Tan", "Shaofeng H.-<PERSON><PERSON> Jiang", "<PERSON><PERSON> Fu", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Bulk Synchronous Parallel (BSP) paradigm is gaining tremendous importance recently because of the pop-ularity of computations such as distributed machine learning and graph computation. In a typical BSP job, multiple workers concurrently conduct iterative computations, where frequent synchronization is required. Therefore, the workers should be scheduled simultaneously and their placement on different computing devices could significantly affect the performance. Simply retrofitting a traditional scheduling discipline will likely not yield the desired performance due to the unique characteristics of BSP jobs. In this work, we derive SPIN, a novel scheduling designed for BSP jobs with placement-sensitive execution to minimize the makespan of all jobs. We first prove the problem approximation hardness and then present how SPIN solves it with a rounding-based randomized approximation approach. Our analysis indicates SPIN achieves a good performance guarantee efficiently. Moreover, SPIN is robust against misestimation of job execution time by theoretically bounding its negative impact. We implement SPIN on a production-trace driven testbed with 40 GPUs. Our extensive experiments show that SPIN can reduce the job makespan and the average job completion time by up to 3× and 4.68×, respectively. Our approach also demonstrates better robustness to execution time misestimation compared with heuristic baselines.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155445"}, {"primary_key": "2634384", "vector": [], "sparse_vector": [], "title": "Coeus: Consistent and Continuous Network Update in Software-Defined Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network update enables Software-Defined Networks (SDNs) to optimize the data plane performance via southbound APIs. The single update between the initial and the final network states fail to handle high-frequency changes or the burst event during the update procedure in time, leading to prolonged update time and inefficiency. On the contrary, the continuous update can respond to the network condition changes at all times. However, existing work, especially \"Update Algebra\" can only guarantee blackhole- and loop-free. The congestion-free property cannot be respected during the update procedure. In this paper, we propose Coeus, a continuous network update system while maintaining blackhole-, loop- and congestion-free simultaneously. Firstly, we establish an operation-based continuous update model. Based on this model, we dynamically reconstruct an operation dependency graph to capture unexecuted update operations and the link utilization variations. Subsequently, we develop an operation composition algorithm to eliminate redundant update commands and an operation node partition algorithm to speed up the update procedure. We prove that the partition algorithm is optimal and can guarantee the consistency. Finally, extensive evaluations show that <PERSON><PERSON> can improve the makespan by at least 179% compared with state-of-the-art approaches when the arrival rate of update events equals to three times per second.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155392"}, {"primary_key": "2634385", "vector": [], "sparse_vector": [], "title": "Fog Integration with Optical Access Networks from an Energy Efficiency Perspective.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Access networks are continuously going through many reformations to make them better suited for various demanding applications and meet new challenging requirements. On one hand, incorporating fog and edge computing has become a necessity for alleviating network congestions and supporting numerous applications that can no longer rely on the resources of a remote cloud. On the other hand, energy-efficiency has grown to be essential for these networks to reduce both their operational costs and carbon footprint but often leads to degradation in their network performance. In this paper, we study the challenges posed by these two imperatives by examining the integration of fog computing with passive optical networks (PONs) under power-conserving frameworks. As most power-conserving frameworks in the literature are centralized-based, we also propose a decentralized-based framework and compare its performance with its centralized counterpart. We study the possible cloudlet placements and the offloading performance in each allocation paradigm to determine which paradigm is able to meet the requirements of next-generation access networks by having better network performance with less energy consumption.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155289"}, {"primary_key": "2634386", "vector": [], "sparse_vector": [], "title": "WBF-PS: WiGig Beam Fingerprinting for UAV Positioning System in GPS-denied Environments.", "authors": ["Pei-Yuan Hong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unmanned aerial vehicles (UAVs) are being investigated to substitute for labor in many indoor applications, e.g., asset tracking and surveillance, where the global positioning system (GPS) is not available. Also, emerging autonomous UAVs are expected to land in indoor parking aprons automatically. Such GPS-denied environments require alternative non-GPS positioning methods. Although there have been some vision-based solutions for UAVs, they perform poorly in the scenes with bad illumination conditions or estimate only relative locations but not global positions. Other common indoor localization methods do not cover UAV factors, such as low power and flying behaviors. To this end, we propose a practical non-GPS positioning system for UAVs, named WBF-PS (WiGig Beam Fingerprinting based Positioning System), using low-power, off-the-shelf WiGig devices. We formulate a 3-dimensional beam fingerprint for the positioning by leveraging the diversity of available transmitter/receiver beams and the link quality. To augment the positioning accuracy, we not only use a weighted k-nearest neighbors algorithm to overcome partial fingerprint inaccuracy but also apply the particle filtering technique into considering the UAV motion. We prototype and evaluate WBF-PS on a UAV platform. The result shows that the positioning errors at the 90th percentile are below 1 m in various cases.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155468"}, {"primary_key": "2634388", "vector": [], "sparse_vector": [], "title": "Energy Harvesting Long-Range Marine Communication.", "authors": ["<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes a self-sustaining broadband long-range maritime communication as an alternative to the expensive and slow satellite communications in offshore areas. The proposed system, named Marinet, consists of many buoys. Each of the buoys has two units: an energy harvesting unit and a wireless communication unit. The energy harvesting unit generates electrical energy from ocean waves to support the operation of the wireless communication unit. The wireless communication unit on each buoy operates in a TV white space frequency band and connects to each other and wired high-speed gateways on land or islands to form a mesh network. The resulting mesh network provides wireless access services to marine users in their range. A prototype of the energy harvesting unit and the wireless communication unit are built and tested in the field. In addition, to ensure Marinet will maintain stable communications in rough sea states, an ocean-link-state prediction algorithm is designed. The algorithm predicts ocean link-states based on ocean wave movements. A realistic ocean simulator is designed and used to evaluate how such a link-state prediction algorithm can improve routing algorithm performance.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155236"}, {"primary_key": "2634389", "vector": [], "sparse_vector": [], "title": "Predictive Scheduling for Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Atilla E<PERSON>il<PERSON>z"], "summary": "A significant challenge for future virtual reality (VR) applications is to deliver high quality-of-experience, both in terms of video quality and responsiveness, over wireless networks with limited bandwidth. This paper proposes to address this challenge by leveraging the predictability of user movements in the virtual world. We consider a wireless system where an access point (AP) serves multiple VR users. We show that the VR application process consists of two distinctive phases, whereby during the first (proactive scheduling) phase the controller has uncertain predictions of the demand that will arrive at the second (deadline scheduling) phase. We then develop a predictive scheduling policy for the AP that jointly optimizes the scheduling decisions in both phases. In addition to our theoretical study, we demonstrate the usefulness of our policy by building a prototype system. We show that our policy can be implemented under Furion, a Unity-based VR gaming software, with minor modifications. Experimental results clearly show visible difference between our policy and the default one. We also conduct extensive simulation studies, which show that our policy not only outperforms others, but also maintains excellent performance even when the prediction of future user movements is not accurate.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155249"}, {"primary_key": "2634390", "vector": [], "sparse_vector": [], "title": "ProTO: Proactive Topology Obfuscation Against Adversarial Network Topology Inference.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The topology of a network is fundamental for building network infrastructure functionalities. In many scenarios, enterprise networks may have no desire to disclose their topology information. In this paper, we aim at preventing attacks that use adversarial, active end-to-end topology inference to obtain the topology information of a target network. To this end, we propose a Proactive Topology Obfuscation (ProTO) system that adopts a detect-then-obfuscate framework: (i) a lightweight probing behavior identification mechanism based on machine learning is designed to detect any probing behavior, and then (ii) a topology obfuscation design is developed to proactively delay all identified probe packets in a way such that the attacker will obtain a structurally accurate yet fake network topology based on the measurements of these delayed probe packets, therefore deceiving the attacker and decreasing its appetency for future inference. We show that ProTO is very effective against active topology inference with minimum performance disruption. Experimental results under different evaluation scenarios show that ProTO is able to (i) achieve a detection rate of 99.9% with a false alarm of 3%, (ii) effectively disrupt adversarial topology inference and lead to the topology inferred by the attacker close to a fake topology, and (iii) result in an overall network delay performance degradation of 1.3% - 2.0%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155255"}, {"primary_key": "2634395", "vector": [], "sparse_vector": [], "title": "ReLoca: Optimize Resource Allocation for Data-parallel Jobs using Deep Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Dongsheng Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Since under-allocating computation resource (e.g., CPU cores) causes suboptimal JCTs of data-parallel jobs, users are inclined to request excessive computation resource to decrease JCTs. However, over-allocating computation resource for data-parallel jobs incurs considerable system overheads (e.g., network communication and disk I/O overhead), which prolong the job completion time. In this paper, we propose ReLoca towards the optimal allocation of computation resource with the objective of minimizing the job completion time. ReLoca employs a deep neural network to guide the allocation of computation resource, by learning the impact of the operations in data-parallel jobs on the system overhead and computation time. Since training samples are time-consuming to collect, we develop an adaptive sampling method to preferably collect high-quality samples and thus overcome the issue of data scarcity. We apply ReLoca to improve Spark and conduct real experiments with five typical applications in big data analytics. Results show that ReLoca significantly reduces the average job completion time. Compared with the state-of-the-art method, ReLoca has higher prediction accuracy, needs fewer training samples and decreases the sampling overhead. With the prediction by ReLoca, the JCT decreases by 29.85%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155521"}, {"primary_key": "2634396", "vector": [], "sparse_vector": [], "title": "KV-Fresh: Freshness Authentication for Outsourced Multi-Version Key-Value Stores.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data outsourcing is a promising technical paradigm to facilitate cost-effective real-time data storage, processing, and dissemination. In such a system, a data owner proactively pushes a stream of data records to a third-party cloud server for storage, which in turn processes various types of queries from end users on the data owner's behalf. This paper considers outsourced multi-version key-value stores that have gained increasing popularity in recent years, where a critical security challenge is to ensure that the cloud server returns both authentic and fresh data in response to end users' queries. Despite several recent attempts on authenticating data freshness in outsourced key-value stores, they either incur excessively high communication cost or can only offer very limited real-time guarantee. To fill this gap, this paper introduces KV-Fresh, a novel freshness authentication scheme for outsourced key-value stores that offers strong real-time guarantee. KV-Fresh is designed based on a novel data structure, Linked Key Span Merkle Hash Tree, which enables highly efficient freshness proof by embedding chaining relationship among records generated at different time. Detailed simulation studies using a synthetic dataset generated from real data confirm the efficacy and efficiency of KV-Fresh.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155270"}, {"primary_key": "2634397", "vector": [], "sparse_vector": [], "title": "Towards Anti-interference WiFi-based Activity Recognition System Using Interference-Independent Phase Component.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Human activity recognition (HAR) has become increasingly essential due to its potential to support a broad array of applications, e.g., elder care, and VR games. Recently, some pioneer WiFi-based HAR systems have been proposed due to its privacy-friendly and device-free characteristics. However, their crucial limitation lies in ignoring the inevitable impact of co-channel interference (CCI), which degrades the performance of these HAR systems significantly. To address this challenge, we propose PhaseAnti, a novel HAR system to exploit the CCI- independent phase component, NLPEV (Nonlinear Phase Error Variation), of Channel State Information (CSI) to cope with the impact of CCI. We provide a rigorous analysis of NLPEV data with respect to its stability and otherness. Validated by our experiments, this phase component across subcarriers is invariant to various CCI scenarios, while different for distinct motions. Based on the analysis, we use NLPEV data to perform HAR in CCI scenarios. Extensive experiments demonstrate that PhaseAnti can reliably recognize activity in various CCI scenarios. Specifically, PhaseAnti achieves a 95% recognition accuracy rate (RAR) on average, which improves up to 16% RAR in the presence of CCI. Moreover, the recognition speed is 9× faster than the state-of-the-art solution.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155536"}, {"primary_key": "2634402", "vector": [], "sparse_vector": [], "title": "DeepAdapter: A Collaborative Deep Learning Framework for the Mobile Web Using Context-Aware Network Pruning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Calton Pu", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning shows great promise in providing more intelligence to the mobile web, but insufficient infrastructure, heavy models, and intensive computation limit the use of deep learning in mobile web applications. In this paper, we present DeepAdapter, a collaborative framework that ties the mobile web with an edge server and a remote cloud server to allow executing deep learning on the mobile web with lower processing latency, lower mobile energy, and higher system throughput. DeepAdapter provides a context-aware pruning algorithm that incorporates the latency, the network condition and the computing capability of the mobile device to fit the resource constraints of the mobile web better. It also provides a model cache update mechanism improving the model request hit rate for mobile web users. At runtime, it matches an appropriate model with the mobile web user and provides a collaborative mechanism to ensure accuracy. Our results show that DeepAdapter decreases average latency by 1.33x, reduces average mobile energy consumption by 1.4x, and improves system throughput by 2.1x with a considerable accuracy. Its contextaware pruning algorithm also improves inference accuracy by up to 0.3% with a smaller and faster model.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155379"}, {"primary_key": "2634403", "vector": [], "sparse_vector": [], "title": "Lightweight Sybil-Resilient Multi-Robot Networks by Multipath Manipulation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Wireless networking opens up many opportunities to facilitate miniaturized robots in collaborative tasks, while the openness of wireless medium exposes robots to the threats of Sybil attackers, who can break the fundamental trust assumption in robotic collaboration by forging a large number of fictitious robots. Recent advances advocate the adoption of bulky multi-antenna systems to passively obtain fine-grained physical layer signatures, rendering them unaffordable to miniaturized robots. To overcome this conundrum, this paper presents ScatterID, a lightweight system that attaches featherlight and batteryless backscatter tags to single-antenna robots to defend against Sybil attacks. Instead of passively \"observing\" signatures, ScatterID actively \"manipulates\" multipath propagation by using backscatter tags to intentionally create rich multipath features obtainable to a single-antenna robot. These features are used to construct a distinct profile to detect the real signal source, even when the attacker is mobile and power-scaling. We implement ScatterID on the iRobot Create platform and evaluate it in typical indoor and outdoor environments. The experimental results show that our system achieves a high AUROC of 0.988 and an overall accuracy of 96.4% for identity verification.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155244"}, {"primary_key": "2634404", "vector": [], "sparse_vector": [], "title": "Physical-Layer Arithmetic for Federated Learning in Uplink MU-MIMO Enabled Wireless Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Federated learning is a very promising machine learning paradigm where a large number of clients cooperatively train a global model using their respective local data. In this paper, we consider the application of federated learning in wireless networks featuring uplink multiuser multiple-input and multiple-output (MU-MIMO), and aim at optimizing the communication efficiency during the aggregation of client-side updates by exploiting the inherent superposition of radio frequency (RF) signals. We propose a novel approach named Physical-Layer Arithmetic (PhyArith), where the clients encode their local updates into aligned digital sequences which are converted into RF signals for sending to the server simultaneously, and the server directly recovers the exact summation of these updates as required from the superimposed RF signal by employing a customized sum-product algorithm. PhyArith is compatible with commodity devices due to the use of full digital operation in both the client-side encoding and the server-side decoding processes, and can also be integrated with other updates compression based acceleration techniques. Simulation results show that PhyArith further improves the communication efficiency by 1.5 to 3 times for training LeNet-5, compared with solutions only applying updates compression.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155479"}, {"primary_key": "2634405", "vector": [], "sparse_vector": [], "title": "Fair and Protected Profit Sharing for Data Trading in Pervasive Edge Computing Environments.", "authors": ["<PERSON><PERSON> Huang", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Innovative edge devices (e.g., smartphones, IoT devices) are becoming much more pervasive in our daily lives. With powerful sensing and computing capabilities, users can generate massive amounts of data. A new business model has emerged where data producers can sell their data to consumers directly to make money. However, how to protect the profit of the data producer from rogue consumers that may resell without authorization remains challenging. In this paper, we propose a smart-contract based protocol to protect the profit of the data producer while allowing consumers to resell the data legitimately. The protocol ensures the revenue is shared with the data producer over authorized reselling, and detects any unauthorized reselling. We formulate a fair revenue sharing problem to maximize the profit of both the data producer and resellers. We formulate the problem into a two-stage Stackelberg game and determine a ratio to share the reselling revenue between the data producer and resellers. Extensive simulations show that with resellers, our mechanism can achieve higher profit for the data producer and resellers.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155399"}, {"primary_key": "2634406", "vector": [], "sparse_vector": [], "title": "Stick: A Harmonious Fusion of Buffer-based and Learning-based Approach for Adaptive Streaming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lifeng Sun"], "summary": "Off-the-shelf buffer-based approaches leverage a simple yet effective buffer-bound to control the adaptive bitrate (ABR) streaming system. Nevertheless, such approaches in standard parameters fail to always provide high quality of experience (QoE) video streaming services under all considered network conditions. Meanwhile, state-of-the-art learning-based ABR approach Pensieve outperforms existing schemes but is impractical to deploy. Therefore, how to harmoniously fuse the buffer-based and learning-based approach has become a key challenge for further enhancing ABR methods. In this paper, we propose Stick, an ABR algorithm that fuses the deep learning method and traditional buffer-based method. <PERSON> utilizes the deep reinforcement learning (DRL) method to train the neural network, which outputs the buffer-bound to control the buffer-based approach for maximizing the QoE metric with different parameters. Trace-driven emulation illustrates that Stick betters Pensieve by 3.5% - 9.41% with an overhead reduction of 88%. Moreover, aiming to further reduce the computational costs while preserving the performances, we propose Trigger, a light-weighted neural network that determines whether the buffer-bound should be adjusted. Experimental results show that Stick+Trigger rivals or outperforms existing schemes in average QoE by 1.7%-28%, and significantly reduces the Stick's computational overhead by 24%-61%. Meanwhile, we show that <PERSON><PERSON> also helps other ABR schemes mitigate the overhead. Extensive results on real-world evaluation demonstrate the superiority of <PERSON> over existing state-of-the-art approaches.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155411"}, {"primary_key": "2634413", "vector": [], "sparse_vector": [], "title": "SketchFlow: Per-Flow Systematic Sampling Using Sketch Saturation Event.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Sampling is a powerful tool to reduce the processing overhead in various systems. NetFlow uses a local table for counting records per flow, and sFlow sends out the collected packet headers periodically to a collecting server over the network. Any measurement system falls into either one of these two models. To reduce the overhead, as in sFlow, simple random sampling (SRS) has been widely used in practice because of its simplicity. However, SRS provides non-uniform sampling rates for different fine-grained flows (defined by 5-tuple), because it samples packets over an aggregated data flow (defined by switch port or VLAN). Consequently, some flows are sampled more than the designated sampling rate (resulting in over-estimation), and others are sampled fewer (resulting in under-estimation). Starting with a simple idea that \"independent per-flow packet sampling provides the most accurate estimation of each flow\", we introduce a new concept of per-flow systematic sampling, aiming to provide the same sampling rate across all flows. In addition, we provide a concrete sampling method called SketchFlow, which approximates the idea of the per-flow systematic sampling using a sketch saturation event. We demonstrate SketchFlow's performance in terms of accuracy, sampling rate, and overhead using real-world datasets, including a backbone network trace, I/O trace, and Twitter dataset. Experimental results show that SketchFlow outperforms SRS (i.e., sFlow) and the non-linear sampling method while requiring a small CPU overhead to measure high-speed traffic in real-time.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155252"}, {"primary_key": "2634414", "vector": [], "sparse_vector": [], "title": "OST: On-Demand TSCH Scheduling with Traffic-Awareness.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Saewoong Bahk"], "summary": "As the emerging Internet of Things (IoT) devices and applications flourish, demand for reliable and energy-efficient low-power wireless network protocols is surging. For this purpose, IEEE 802.15.4 standardized time-slotted channel hopping (TSCH), a promising and viable link-layer solution that has shown outstanding performance achieving over 99% reliability with low duty-cycles. However, it lacks one thing, flexibility. It is not adaptable to a wide variety of applications with varying traffic load and unpredictable routing topology due to its static timeslot scheduling. To this end, we propose OST, an On-demand Scheduling scheme for TSCH with traffic-awareness. In OST, each node dynamically self-adjusts the frequency of timeslots at run time according to time-varying traffic intensity. Moreover, it features on-demand resource allocation to handle bursty/queued packets in a timely manner. By doing so, OST aims to minimize its energy consumption while guaranteeing reliable packet delivery. We evaluate OST on a large-scale 72-node testbed, demonstrating that it achieves improvement of 60% in reliability and 52% in energy-efficiency compared to the state of the art.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155496"}, {"primary_key": "2634415", "vector": [], "sparse_vector": [], "title": "Online Learning for Joint Beam Tracking and Pattern Optimization in Massive MIMO Systems.", "authors": ["Jong<PERSON>ong", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we consider a joint beam tracking and pattern optimization problem for massive multiple input multiple output (MIMO) systems in which the base station (BS) selects a beamforming codebook and performs adaptive beam tracking taking into account the user mobility. A joint adaptation scheme is developed in a two-phase reinforcement learning framework which utilizes practical signaling and feedback information. In particular, an inner agent adjusts the transmission beam index for a given beamforming codebook based on short-term instantaneous signal-to-noise ratio (SNR) rewards. In addition, an outer agent selects the beamforming codebook based on long-term SNR rewards. Simulation results demonstrate that the proposed online learning outperforms conventional codebook-based beamforming schemes using the same number of feedback information. It is further shown that joint beam tracking and beam pattern adaptation provides a significant SNR gain compared to the beam tracking only schemes, especially as the user mobility increases.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155475"}, {"primary_key": "2634418", "vector": [], "sparse_vector": [], "title": "Revealing Much While Saying Less: Predictive Wireless for Status Update.", "authors": ["<PERSON><PERSON><PERSON> Jiang", "<PERSON><PERSON><PERSON>", "Siyu Fu", "<PERSON><PERSON>", "<PERSON>", "Shun<PERSON> Zhang", "<PERSON><PERSON> Xu"], "summary": "Wireless communications for status update are becoming increasingly important, especially for machine-type control applications. Existing work has been mainly focused on Age of Information (AoI) optimizations. In this paper, a status-aware predictive wireless interface design, networking and implementation are presented which aim to minimize the status recovery error of a wireless networked system by leveraging online status model predictions. Two critical issues of predictive status update are addressed: practicality and usefulness. Link-level experiments on a Software-Defined-Radio (SDR) testbed are conducted and test results show that the proposed design can significantly reduce the number of wireless transmissions while maintaining a low status recovery error. A Status-aware Multi-Agent Reinforcement learning neTworking solution (SMART) is proposed to dynamically and autonomously control the transmit decisions of devices in an ad hoc network based on their individual statuses. System-level simulations of a multi dense platooning scenario are carried out on a road traffic simulator. Results show that the proposed schemes can greatly improve the platooning control performance in terms of the minimum safe distance between successive vehicles, in comparison with the AoI-optimized status-unaware and communication latency-optimized schemes-this demonstrates the usefulness of our proposed status update schemes in a real-world application.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155225"}, {"primary_key": "2634421", "vector": [], "sparse_vector": [], "title": "SCYLLA: QoE-aware Continuous Mobile Vision with FPGA-based Dynamic Deep Neural Network Reconfiguration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Continuous mobile vision is becoming increasingly important as it finds compelling applications which substantially improve our everyday life. However, meeting the requirements of quality of experience (QoE) diversity, energy efficiency and multi-tenancy simultaneously represents a significant challenge. In this paper, we present SCYLLA, an FPGA-based framework that enables QoE-aware continuous mobile vision with dynamic reconfiguration to effectively address this challenge. SCYLLA pre-generates a pool of FPGA design and DNN models, and dynamically applies the optimal software-hardware configuration to achieve the maximum overall performance on QoE for concurrent tasks. We implement SCYLLA on state-of-the-art FPGA platform and evaluate SCYLLA using drone-based traffic surveillance application on three datasets. Our evaluation shows that SCYLLA provides much better design flexibility and achieves superior QoE trade-offs than status-quo CPU-based solution that existing continuous mobile vision applications are built upon.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155435"}, {"primary_key": "2634423", "vector": [], "sparse_vector": [], "title": "Latency-aware VNF Chain Deployment with Efficient Resource Reuse at Network Edge.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Liu", "<PERSON>"], "summary": "With the increasing demand of low-latency network services, mobile edge computing (MEC) emerges as a new paradigm, which provides server resources and processing capacities in close proximity to end users. Based on network function virtualization (NFV), network services can be flexibly provisioned as virtual network function (VNF) chains deployed at edge servers. However, due to the resource shortage at the network edge, how to efficiently deploy VNF chains with latency guarantees and resource efficiency remains as a challenging problem. In this work, we focus on jointly optimizing the resource utilization of both edge servers and physical links under the latency limitations. Specifically, we formulate the VNF chain deployment problem as a mixed integer linear programming (MILP) to minimize the total resource consumption. We design a novel two-stage latency-aware VNF deployment scheme: highlighted by a constrained depth-first search algorithm (CDFSA) for selecting paths, and a path-based greedy algorithm (PGA) for assigning VNFs by reusing as many VNFs as possible. We demonstrate that our proposed algorithm can efficiently achieve a near-optimal solution with a theoretically-proved worstcase performance bound. Extensive simulation results show that the proposed algorithm outperforms three previous heuristic algorithms.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155345"}, {"primary_key": "2634424", "vector": [], "sparse_vector": [], "title": "When FTM Discovered MUSIC: Accurate WiFi-based Ranging in the Presence of Multipath.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The recent standardization by IEEE of Fine Timing Measurement (FTM), a time-of-flight based approach for ranging has the potential to be a turning point in bridging the gap between the rich literature on indoor localization and the so-far tepid market adoption. However, experiments with the first WiFi cards supporting FTM show that while it offers meter-level ranging in clear line-of-sight settings (LOS), its accuracy can collapse in non-line-of-sight (NLOS) scenarios. We present FUSIC, the first approach that extends FTM's LOS accuracy to NLOS settings, without requiring any changes to the standard. To accomplish this, FUSIC leverages the results from FTM and MUSIC - both erroneous in NLOS - into solving the double challenge of 1) detecting when FTM returns an inaccurate value and 2) correcting the errors as necessary. Experiments in 4 different physical locations reveal that a) FUSIC extends FTM's LOS ranging accuracy to NLOS settings - hence, achieving its stated goal; b) it significantly improves FTM's capability to offer room-level indoor positioning.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155464"}, {"primary_key": "2634426", "vector": [], "sparse_vector": [], "title": "SAFCast: Smart Inter-Datacenter Multicast Transfer with Deadline Guarantee by Store-And-Forwarding.", "authors": ["<PERSON><PERSON>eh<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the increasing demand of network services, many researches employ a Software Defined Networking architecture to manage large-scale inter-datacenter networks. Some of existing services such as backup, data migration and update need to replicate data to multiple datacenters by multicast before deadline. Recent works set up minimum-weight Steiner tree as routing paths for multicasting transfers to reduce bandwidth waste; meanwhile, using deadline-aware scheduling guarantees deadlines of requests. However, there is an issue of bandwidth competition among those works. SAFCast as a new algorithm is proposed for multicasting transfers and deadline-aware scheduling. In SAFCast, we develop a tree pruning process and make datacenters employ the store-and-forwarding mechanism to improve the issue of bandwidth competition. Meanwhile, more requests can be accepted by SAFCast. Our experimental result shows that SAFCast outperforms DDCCast in the acceptance rate by 16.5%. In addition, given a volume-to-price function for revenue, SAFCast can achieve 10% more profit than DDCCast. As a result, SAFCast is a better choice for cloud providers with the effective deadline guarantee and more efficient multicasting transfers.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155218"}, {"primary_key": "2634428", "vector": [], "sparse_vector": [], "title": "MABSTA: Collaborative Computing over Heterogeneous Devices in Dynamic Environments.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Collaborative computing, leveraging resource on multiple wireless-connected devices, enables complex applications that a single device cannot support individually. However, the problem of assigning tasks over devices becomes challenging in the dynamic environments encountered in real-world settings, considering that the resource availability and channel conditions change over time in unpredictable ways due to mobility and other factors. In this paper, we formulate the task assignment problem as an online learning problem using an adversarial multi-armed bandit framework. We propose MABSTA, a novel algorithm that learns the performance of unknown devices and channel qualities continually through exploratory probing and makes task assignment decisions by exploiting the gained knowledge. The implementation of MABSTA, based on Gibbs Sampling approach, is computational-light and offers competitive performance in different scenarios on the trace-data obtained from a wireless IoT testbed. Furthermore, we prove that MABSTA is 1-competitive compared to the best offline assignment for any dynamic environment without stationarity assumptions, and demonstrate the polynomial-time algorithm for the exact implementation of the sampling process. To the best of our knowledge, MABSTA is the first online learning algorithm tailored to this class of problems.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155232"}, {"primary_key": "2634429", "vector": [], "sparse_vector": [], "title": "SmartBond: A Deep Probabilistic Machinery for Smart Channel Bonding in IEEE 802.11ac.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic bandwidth operation in IEEE 802.11ac helps wireless access points to tune channel widths based on carrier sensing and bandwidth requirements of associated wireless stations. However, wide channels result in a reduction in the carrier sensing range, which leads to the problem of channel sensing asymmetry. As a consequence, access points face hidden channel interference that may lead to as high as 60% reduction in the throughput under certain scenarios of dense deployments of access points. Existing approaches handle this problem by detecting the hidden channels once they occur and affect the channel access performance. In a different direction, in this paper, we develop a method for avoiding hidden channels by meticulously predicting the channel width that can reduce interference as well as can improve the average communication capacity. The core of our approach is a deep probabilistic machinery based on point process modeling over the evolution of channel width selection process. The proposed approach, SmartBond, has been implemented and deployed over a testbed with 8 commercial wireless access points. The experiments show that the proposed model can significantly improve the channel access performance although it is lightweight and does not incur much overhead during the decision making process.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155436"}, {"primary_key": "2634430", "vector": [], "sparse_vector": [], "title": "Dense Distributed Massive MIMO: Precoding and Power Control.", "authors": ["Aliye Özge Kaya", "<PERSON><PERSON>"], "summary": "We present a non-iterative downlink precoding approach for distributed massive multiple-input multiple output systems (DmMIMO) where users are served by overlapping clusters of transmission/reception points (TRP) and channel estimates for links outside the clusters are available. In contrast to traditional cellular systems, each user is served by its own cluster of transmission points and a DmMIMO TRP could be part of multiple clusters. We also propose a power control algorithm that ensures per site power constraints are satisfied when the proposed precoding approach is used. The algorithm extends straightforwardly to the multiple receive antenna case. Extensive simulation results are presented for various cluster sizes and inter-site distances for a dense urban environment with channels generated using ray tracing. Results show that spectral efficiency comparable to massive MIMO can be achieved for a dense deployment of small cells each with only a small number of antennas with our DmMIMO scheme without the need for extensive coordination between many access points.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155377"}, {"primary_key": "2634434", "vector": [], "sparse_vector": [], "title": "Stable and Efficient Piece-Selection in Multiple Swarm BitTorrent-like Peer-to-Peer Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent studies have suggested that the BitTorrent's rarest-first protocol, owing to its work-conserving nature, can become unstable in the presence of non-persistent users. Consequently, in any stable protocol, many peers are at some point endogenously forced to hold off their file-download activity. In this work, we propose a tunable piece-selection policy that minimizes this (undesirable) requisite by combining the (work-conserving) rarest-first protocol with only an appropriate share of the (non-work conserving) mode-suppression protocol. We refer to this policy as \"Rarest-First with Probabilistic Mode-Suppression\" or simply RFwPMS. We study RFwPMS under a stochastic model of the BitTorrent network that is general enough to capture multiple swarms of non-persistent users - each swarm having its own altruistic preferences that may or may not overlap with those of other swarms. Using a Lyapunov drift analysis, we show that RFwPMS is provably stable for all kinds of inter-swarm behaviors, and that the use of rarest-first instead of random-selection is indeed more justified. Our numerical results suggest that RFwPMS is scalable in the general multi-swarm setting and offers better performance than the existing stabilizing schemes like mode-suppression.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155253"}, {"primary_key": "2634437", "vector": [], "sparse_vector": [], "title": "Coded Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Running intensive compute tasks across the fifth generation mobile network of edge devices introduces distributed computing challenges: edge devices are heterogeneous in the compute, storage, and communication capabilities; and can exhibit unpredictable straggler effects and failures. In this work, we propose an error-correcting-code inspired strategy to execute computing tasks in edge computing environments, which is designed to mitigate variability in response times and errors caused by edge devices' heterogeneity and lack of reliability. Unlike prior coding approaches, we incorporate partially unfinished coded tasks into our computation recovery, which allows us to achieve smooth performance degradation with low-complexity decoding when the coded tasks are run on edge devices with a fixed deadline. By further carrying out coding on edge devices as well as a master node, the proposed computing scheme also alleviates communication bottlenecks during data shuffling and is amenable to distributed implementation in a highly variable and limited network. Such distributed encoding forces us to solve new decoding challenges. Using a representative implementation based on federated multi-task learning frameworks, extensive performance simulations are carried out, which demonstrate that the proposed strategy offers significant gains in latency and accuracy over conventional coded computing schemes.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155226"}, {"primary_key": "2634438", "vector": [], "sparse_vector": [], "title": "Joint Optimization of Signal Design and Resource Allocation in Wireless D2D Edge Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we study the distributed computational capabilities of device-to-device (D2D) networks. A key characteristic of D2D networks is that their topologies are reconfigurable to cope with network demands. For distributed computing, resource management is challenging due to limited network and communication resources, leading to inter-channel interference. To overcome this, recent research has addressed the problems of wireless scheduling, subchannel allocation, power allocation, and multiple-input multiple-output (MIMO) signal design, but has not considered them jointly. In this paper, unlike previous mobile edge computing (MEC) approaches, we propose a joint optimization of wireless MIMO signal design and network resource allocation to maximize energy efficiency. Given that the resulting problem is a non-convex mixed integer program (MIP) which is prohibitive to solve at scale, we decompose its solution into two parts: (i) a resource allocation subproblem, which optimizes the link selection and subchannel allocations, and (ii) MIMO signal design subproblem, which optimizes the transmit beamformer, transmit power, and receive combiner. Simulation results using wireless edge topologies show that our method yields substantial improvements in energy efficiency compared with cases of no offloading and partially optimized methods and that the efficiency scales well with the size of the network.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155510"}, {"primary_key": "2634439", "vector": [], "sparse_vector": [], "title": "AnyScatter: Eliminating Technology Dependency in Ambient Backscatter Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we introduce technology-independent ambient backscatter systems where a backscatter tag utilizes all single-stream ambient signals transmitted by nearby devices. Specifically, we design a phase demodulation algorithm that detects a backscatter signal from the phase difference between the two antennas, no matter which signal the tag reflects. We then develop a parallelized backscatter receiver that mitigates the dead spot problem by leveraging antenna diversity. To show the feasibility of our design, we implement a backscatter receiver on the software-defined radio platform and analyze 50 MHz RF bandwidth in real-time. Our evaluation shows that the receiver can decode backscatter bits carried by any single stream ambient signal such as a continuous wave, a QAM signal, and even a noise signal. We also demonstrate backscatter transmissions with commodity Wi-Fi and Bluetooth devices to prove that our design can be combined with existing wireless networks.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155276"}, {"primary_key": "2634440", "vector": [], "sparse_vector": [], "title": "Distributed Collaborative 3D-Deployment of UAV Base Stations for On-Demand Coverage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deployment of unmanned aerial vehicles (UAVs) performing as flying aerial base stations (BSs) has a great potential of adaptively serving ground users during temporary events, such as major disasters and massive events. However, planning an efficient, dynamic, and 3D deployment of UAVs in adaptation to dynamically and spatially varying ground users is a highly complicated problem due to the complexity in air-to-ground channels and interference among UAVs. In this paper, we propose a novel distributed 3D deployment method for UAVBSs in a downlink network for on-demand coverage. Our method consists mainly of the following two parts: sensing-aided crowd density estimation and distributed push-sum algorithm. The first part estimates the ground user density from its observation through on-ground sensors, thereby allowing us to avoid the computationally intensive process of obtaining the positions of all the ground users. On the basis of the estimated user density, in the second part, each UAV dynamically updates its 3D position in collaboration with its neighboring UAVs for maximizing the total coverage. We prove the convergence of our distributed algorithm by employing a distributed push-sum algorithm framework. Simulation results demonstrate that our method can improve the overall coverage with a limited number of ground sensors. We also demonstrate that our method can be applied to a dynamic network in which the density of ground users varies temporally.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155283"}, {"primary_key": "2634441", "vector": [], "sparse_vector": [], "title": "Message Type Identification of Binary Network Protocols using Continuous Segment Similarity.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Protocol reverse engineering based on traffic traces infers the behavior of unknown network protocols by analyzing observable network messages. To perform correct deduction of message semantics or behavior analysis, accurate message type identification is an essential first step. However, identifying message types is particularly difficult for binary protocols, whose structural features are hidden in their densely packed data representation. We leverage the intrinsic structural features of binary protocols and propose an accurate method for discriminating message types.Our approach uses a similarity measure with continuous value range by comparing feature vectors where vector elements correspond to the fields in a message, rather than discrete byte values. This enables a better recognition of structural patterns, which remain hidden when only exact value matches are considered. We combine Hirschberg alignment with DBSCAN as cluster algorithm to yield a novel inference mechanism. By applying novel autoconfiguration schemes, we do not require manually configured parameters for the analysis of an unknown protocol, as required by earlier approaches.Results of our evaluations show that our approach has considerable advantages in message type identification result quality and also execution performance over previous approaches.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155275"}, {"primary_key": "2634448", "vector": [], "sparse_vector": [], "title": "AudiSDN: Automated Detection of Network Policy Inconsistencies in Software-Defined Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ung<PERSON> Woo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Seungwon Shin"], "summary": "At the foundation of every network security architecture lies the premise that formulated network flow policies are reliably deployed and enforced by the network infrastructure. However, software-defined networks (SDNs) add a particular challenge to satisfying this premise, as for SDNs the flow pol-icy implementation spans multiple applications and abstraction layers across the SDN stack. In this paper, we focus on the question of how to automatically identify cases in which the SDN stack fails to prevent policy inconsistencies from arising among these components. This question is rather essential, as when such inconsistencies arise the implications to the security and reliability of the network are devastating. We present AudiSDN, an automated fuzz-testing framework designed to formulate test cases in which policy inconsistencies can arise in OpenFlow networks, the most prevalent SDN protocol used today. We also present results from applying AudiSDN to two widely used SDN controllers, Floodlight and ONOS. In fact, our test results have led to the filing of 3 separate CVE reports. We believe that the approach presented in this paper is applicable to the breadth of OpenFlow platforms used today, and that its broader usage will help to address a serious but yet understudied pragmatic concern.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155378"}, {"primary_key": "2634450", "vector": [], "sparse_vector": [], "title": "Your Privilege Gives Your Privacy Away: An Analysis of a Home Security Camera Service.", "authors": ["<PERSON><PERSON> Li", "Zhenyu Li", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Once considered a luxury, Home Security Cameras (HSCs) are now commonplace and constitute a growing part of the wider online video ecosystem. This paper argues that their expanding coverage and close integration with daily life may result in not only unique behavioral patterns, but also key privacy concerns. This motivates us to perform a detailed measurement study of a major HSC provider, covering 15.4M streams and 211K users. Our study takes two perspectives: (i) we explore the per-user behaviour, identifying core clusters of users; and (ii) we build on this analysis to extract and predict privacy-compromising insight. Key observations include a highly asymmetrical traffic distribution, distinct usage patterns, wasted resources and fixed viewing locations. Furthermore, we identify three privacy risks and explore them in detail. We find that paid users are more likely to be exposed to attacks due to their heavier usage patterns. We conclude by proposing simple mitigations that can alleviate these risks.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155516"}, {"primary_key": "2634451", "vector": [], "sparse_vector": [], "title": "URSAL: Ultra-Efficient, Reliable, Scalable, and Available Block Storage at Low Cost.", "authors": ["Huiba Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Large-scale cloud block storage provides virtual disks for various applications and services like online booking, gaming, and offline data analytics. The state-of-the-art URSA [1] block store adopted a hybrid storage structure which placed primary data on solid-state drives (SSDs) and stored backup data on hard-disk drives (HDDs). URSA used small SSD journals to bridge the performance gap between SSDs and HDDs. Although URSA's SSD-HDD-hybrid storage structure achieves SSD-like I/O performance while using only one third of the SSDs required by the SSD-only storage pattern (storing both primary data and backup data on SSDs), we argue that the traditional HDDonly storage structure is still preferable for a large variety of relatively low-end customers and underloaded applications that are sensitive to the per-bit storage cost.To lower the storage cost, in this paper we design URSAL, an HDD-only block store which provides ultra efficiency, reliability, scalability and availability at low cost. Compared to existing block stores such as URSA, Ceph, and Sheepdog, URSAL has the following distinctions. First, URSAL designs the proxy-based storage architecture, where a proxy server process runs together with each virtual machine (VM) client mounting virtual disks and controls the procedure of all block-level I/O. Second, URSAL selectively performs direct block writes on raw HDDs or indirect log appends to HDD journals (which are then asynchronously replayed to raw HDDs), depending on the characteristics of the workloads. Third, URSAL runs one storage server process for each physical HDD, which conservatively has at most one active thread reading/writing the HDD to avoid I/O contention. We have implemented URSAL. Evaluation results show that URSAL significantly outperforms state-of-the-art HDD-only block stores (Ceph and Sheepdog) when providing virtual disks for underloaded applications.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155513"}, {"primary_key": "2634452", "vector": [], "sparse_vector": [], "title": "DeepTrack: Grouping RFID Tags Based on Spatio-temporal Proximity in Retail Spaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "RFID applications for taking inventory and processing transactions in point-of-sale (POS) systems improve operational efficiency but are not designed to provide insights about customers' interactions with products. We bridge this gap by solving the proximity grouping problem to identify groups of RFID tags that stay in close proximity to each other over time. We design DeepTrack, a framework that uses deep learning to automatically track the group of items carried by a customer during her shopping journey. This unearths hidden purchase behaviors helping retailers make better business decisions and paves the way for innovative shopping experiences such as seamless checkout (`a la Amazon Go). DeepTrack employs a recurrent neural network (RNN) with the attention mechanism, to solve the proximity grouping problem in noisy settings without explicitly localizing tags. We tailor DeepTrack's design to track not only mobile groups (products carried by customers) but also flexibly identify stationary tag groups (products on shelves). The key attribute of DeepTrack is that it only uses readily available tag data from commercial off-the-shelf RFID equipment. Our experiments demonstrate that, with only two hours training data, DeepTrack achieves a grouping accuracy of 98.18% (99.79%) when tracking eight mobile (stationary) groups.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155357"}, {"primary_key": "2634455", "vector": [], "sparse_vector": [], "title": "Bound-based Network Tomography for Inferring Interesting Link Metrics.", "authors": ["Huikang Li", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Network tomography is an attractive methodology for inferring internal network states from accumulated path measurements between pairs of monitors. Motivated by previous results that identifying all link metrics can require a large number of monitors, we focus on calculating the performance bounds of a set of interesting links, i.e., bound-based network tomography. We develop an efficient solution to obtain the tightest upper bounds and lower bounds of all interesting links in an arbitrary network with a given set of end-to-end path measurements. Based on this solution, we further propose an algorithm to place new monitors over existing ones such that the bounds of interesting links can be maximally tightened. We theoretically prove the effectiveness of the proposed algorithms. We implement the algorithms and conduct extensive experiments based on real network topologies. Compared with state-of-the- art approaches, our algorithms can achieve 2.2~3.1 times more reduction on the bound interval lengths of all interesting links and reduce the number of placed monitors significantly in various network settings.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155406"}, {"primary_key": "2634457", "vector": [], "sparse_vector": [], "title": "Automating Cloud Deployment for Deep Learning Inference of Real-time Online Services.", "authors": ["<PERSON>", "Zhenhua Han", "<PERSON><PERSON><PERSON>", "Zhenhua Li", "Haisheng Tan"], "summary": "Real-time online services using pre-trained deep neural network (DNN) models, e.g., Siri and Instagram, require low-latency and cost-efficiency for quality-of-service and commercial competitiveness. When deployed in a cloud environment, such services call for an appropriate selection of cloud configurations (i.e., specific types of VM instances), as well as a considerate device placement plan that places the operations of a DNN model to multiple computation devices like GPUs and CPUs. Currently, the deployment mainly relies on service providers' manual efforts, which is not only onerous but also far from satisfactory oftentimes (for a same service, a poor deployment can incur significantly more costs by tens of times). In this paper, we attempt to automate the cloud deployment for real-time online DNN inference with minimum costs under the constraint of acceptably low latency. This attempt is enabled by jointly leveraging the Bayesian Optimization and Deep Reinforcement Learning to adaptively unearth the (nearly) optimal cloud configuration and device placement with limited search time. We implement a prototype system of our solution based on TensorFlow and conduct extensive experiments on top of Microsoft Azure. The results show that our solution essentially outperforms the nontrivial baselines in terms of inference speed and cost-efficiency.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155267"}, {"primary_key": "2634458", "vector": [], "sparse_vector": [], "title": "Universally Stable Cache Networks.", "authors": ["Yuanyuan Li", "<PERSON><PERSON><PERSON>"], "summary": "We consider a cache network in which intermediate nodes equipped with caches can serve content requests. We model this network as a universally stable queuing system, in which packets carrying identical responses are consolidated before being forwarded downstream. We refer to resulting queues as M/M/1c or counting queues, as consolidated packets carry a counter indicating the packet's multiplicity. Cache networks comprising such queues are hard to analyze; we propose two approximations: one via M/M/∞ queues, and one based on M/M/1c queues under the assumption of Poisson arrivals. We show that, in both cases, the problem of jointly determining (a) content placements and (b) service rates admits a poly-time, 1 1/e approximation algorithm. Numerical evaluations indicate-that both approximations yield good solutions in practice, significantly outperforming competitors.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155416"}, {"primary_key": "2634459", "vector": [], "sparse_vector": [], "title": "Synergetic Denial-of-Service Attacks and Defense in Underwater Named Data Networking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to the harsh environment and energy limitation, maintaining efficient communication is crucial to the lifetime of Underwater Sensor Networks (UWSN). Named Data Networking (NDN), one of future network architectures, begins to be applied to UWSN. Although Underwater Named Data Networking (UNDN) performs well in data transmission, it still faces some security threats, such as the Denial-of-Service (DoS) attacks caused by Interest Flooding Attacks (IFAs). In this paper, we present a new type of DoS attacks, named as Synergetic Denial-of-Service (SDoS). Attackers synergize with each other, taking turns to reply to malicious interests as late as possible. SDoS attacks will damage the Pending Interest Table, Content Store, and Forwarding Information Base in routers with high concealment. Simulation results demonstrate that the SDoS attacks quadruple the increased network traffic compared with normal IFAs and the existing IFA detection algorithm in UNDN is completely invalid to SDoS attacks. In addition, we analyze the infection problem in UNDN and propose a defense method Trident based on carefully designed adaptive threshold, burst traffic detection, and attacker identification. Experiment results illustrate that <PERSON><PERSON> can effectively detect and resist both SDoS attacks and normal IFAs. Meanwhile, Trident can robustly undertake burst traffic and congestion.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155371"}, {"primary_key": "2634460", "vector": [], "sparse_vector": [], "title": "HotDedup: Managing Hot Data Storage at Network Edge through Optimal Distributed Deduplication.", "authors": ["Shi<PERSON>g Li", "<PERSON><PERSON>"], "summary": "The rapid growth of computing capabilities at network edge calls for efficient management frameworks that not only considers placing hot data on edge storage for best accessibility and performance, but also makes optimal utilization of edge storage space. In this paper, we solve a joint optimization problem by exploiting both data popularity (for optimal data access performance) and data similarity (for optimal storage space efficiency). We show that the proposed optimization is NP- hard and develop a 2⌈2Γ⌉ - 1 + ϵ-approximation algorithm by (i) making novel use of δ-similarity graph to capture pairwise data similarity and (ii) leveraging the k-MST algorithm to solve a Prize Collecting Steiner Tree problem on the graph. The proposed algorithm is prototyped using an open-source distributed storage system, Cassandra. We evaluate its performance extensively on a real-world testbed and with respect to real-world IoT datasets. The algorithm is shown to achieve over 55% higher edge service rate and reduces request response time by about 30%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155233"}, {"primary_key": "2634461", "vector": [], "sparse_vector": [], "title": "WiHF: Enable User Identified Gesture Recognition with WiFi.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "User identified gesture recognition is a fundamental step towards ubiquitous device-free sensing. We propose WiHF, which first simultaneously enables cross-domain gesture recognition and user identification using WiFi in a real-time manner. The basic idea of WiHF is to derive a cross-domain motion change pattern of arm gestures from WiFi signals, rendering both unique gesture characteristics and the personalized user performing styles. To extract the motion change pattern in realtime, we develop an efficient method based on the seam carving algorithm. Moreover, taking as input the motion change pattern, a Deep Neural Network (DNN) is adopted for both gesture recognition and user identification tasks. In DNN, we apply splitting and splicing schemes to optimize collaborative learning for dual tasks. We implement WiHF and extensively evaluate its performance on a public dataset including 6 users and 6 gestures performed across 5 locations and 5 orientations in 3 environments. Experimental results show that WiHF achieves 97.65% and 96.74% for in-domain gesture recognition and user identification accuracy, respectively. The cross-domain gesture recognition accuracy is comparable with the state-of-the-art methods, but the processing time is reduced by 30×.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155539"}, {"primary_key": "2634462", "vector": [], "sparse_vector": [], "title": "AoI Scheduling with Maximum Thresholds.", "authors": ["Chengzhang Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Age of Information (AoI) is an application layer performance metric that quantifies the freshness of information. This paper investigates scheduling problems at network edge when each source node has an AoI requirement (which we call Maximum AoI Threshold (MAT)). Specifically, we want to determine whether or not a vector of MATs for the source nodes is schedulable, and if so, find a feasible scheduler for it. For a small network, we present an optimal procedure called Cyclic Scheduler Detection (CSD) that can determine the schedulability with absolute certainty. For a large network where CSD is not applicable, we present a novel low-complexity procedure, called Fictitious Polynomial Mapping (FPM), and prove that FPM can find a feasible scheduler for any MAT vector when the load is under ln 2. We use extensive numerical results to validate our theoretical results and show that the performance of FPM is significantly better than a state-of-the-art scheduling algorithm.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155514"}, {"primary_key": "2634464", "vector": [], "sparse_vector": [], "title": "Secure Balance Planning of Off-blockchain Payment Channel Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Off-blockchain payment channels can significantly improve blockchain scalability by enabling a large number of micro-payments between two blockchain nodes, without committing every single payment to the blockchain. Multiple payment channels form a payment network, so that two nodes without direct channel connection can still make payments. A critical challenge in payment network construction is to decide how many funds should be deposited into payment channels as initial balances, which seriously influences the performance of payment networks, but has been seldom studied by existing work. In this paper, we address this challenge by designing PnP, a balance planning service for payment networks. Given estimated payment demands among nodes, PnP can decide channel balances to satisfy these demands with a high probability. It does not rely on any trusted third-parties, and can provide strong protection from malicious attacks with low overhead. It obtains these benefits with two novel designs, the cryptographic sortition and the chance-constrained balance planning algorithm. Experimental results on a testbed of 30 nodes show that PnP can enable 30% more payments than other designs.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155375"}, {"primary_key": "2634465", "vector": [], "sparse_vector": [], "title": "PROMAR: Practical Reference Object-based Multi-user Augmented Reality.", "authors": ["<PERSON>gpeng Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Augmented reality (AR) is an emerging technology that weaves virtual objects into physical environments, and enables users to interact with them through viewing devices. This paper targets on multi-user AR applications, where virtual objects (VOs) placed by a user can be viewed by other users. We develop a practical framework that supports the basic multi-user AR functions of placing and viewing VOs, and our system can be deployed on off-the-shelf smartphones without special hardware. The main technical challenge we address is that when facing the exact same scene, the user who places the VO and the user who views the VO may have different view angles and distances to the scene. This setting is realistic and the traditional solutions yield a poor performance in terms of the accuracy. In this work, we have developed a suite of algorithms that help the viewers accurately identify the same scene and restore the VO under a moderate range of view angle difference. We have prototyped our system, and the experimental results have shown significant performance improvements. Our source codes and demos can be accessed at https://github.com/PROMAR2019.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155506"}, {"primary_key": "2634466", "vector": [], "sparse_vector": [], "title": "RF-Rhythm: Secure and Usable Two-Factor RFID Authentication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Passive RFID technology is widely used in user authentication and access control. We propose RF-Rhythm, a secure and usable two-factor RFID authentication system with strong resilience to lost/stolen/cloned RFID cards. In RF-Rhythm, each legitimate user performs a sequence of taps on his/her RFID card according to a self-chosen secret melody. Such rhythmic taps can induce phase changes in the backscattered signals, which the RFID reader can detect to recover the user’s tapping rhythm. In addition to verifying the RFID card’s identification information as usual, the backend server compares the extracted tapping rhythm with what it acquires in the user enrollment phase. The user passes authentication checks if and only if both verifications succeed. We also propose a novel phase-hopping protocol in which the RFID reader emits Continuous Wave (CW) with random phases for extracting the user’s secret tapping rhythm. Our protocol can prevent a capable adversary from extracting and then replaying a legitimate tapping rhythm from sniffed RFID signals. Comprehensive user experiments confirm the high security and usability of RF-Rhythm with false-positive and false-negative rates close to zero.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155427"}, {"primary_key": "2634468", "vector": [], "sparse_vector": [], "title": "Efficient Coflow Transmission for Distributed Stream Processing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Distributed streaming applications require the underlying network flows to transmit packets continuously to keep their output results fresh. These results will become stale if no updates come, and their staleness is determined by the slowest flow. At this point, coflows can be semantically comprised. Hence, efficient coflow transmission is critical for streaming applications. However, prior coflow-based solutions have significant limitations. They use a one-shot performance metric-CCT (coflow completion time), which cannot continuously reflect the staleness of the output results for a streaming application.To this end, we propose a new performance metric-coflow age (CA), for coflows generated by distributed streaming applications. The CA tracks the longest time-since-last-service among all flows in a coflow. In such a context, we consider a data center network with multiple coflows that continuously transmit packets between their source-destination pairs and address the problem of minimizing the average long-term CA while simultaneously satisfying the throughput constraints from the coflows. To solve this problem efficiently, we design a randomized algorithm and a drift-plus-age algorithm, and show that they can make the average long-term CA to achieve nearly two times and arbitrarily close to the optimal value, respectively. Through extensive simulations, we further demonstrate that both of the proposed algorithms can significantly reduce the CA of coflows, without violating the throughput requirement of any coflow, when compared to the state-of-the-art solution.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155511"}, {"primary_key": "2634469", "vector": [], "sparse_vector": [], "title": "DyLoRa: Towards Energy Efficient Dynamic LoRa Transmission Control.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LoRa has been shown as a promising platform for connecting large scale of Internet of Things (IoT) devices, by providing low-power long-range communication with a low data rate. LoRa has different transmission parameters (e.g., transmission power and spreading factor) to tradeoff noise resilience, transmission range and energy consumption for different environments. Thus, adjusting those parameters is essential for LoRa performance. Existing approaches are mainly threshold based and fail to achieve optimal energy efficiency. We propose DyLoRa, a dynamic LoRa transmission control system to improve energy efficiency. The high level idea of DyLoRa is to adjust parameters to different environments. The main challenge is that LoRa has very limited data rate and sparse data, making it very time- and energy-consuming to obtain physical link properties. We show that symbol error rate is highly related to the Signal- Noise Ratio (SNR) and derive the model to characterize this. We further derive energy efficiency model based on the symbol error model. DyLoRa can adjust parameters for optimal energy efficiency from sparse LoRa packets. We implement DyLoRa based on LoRaWAN 1.0.2 with SX1276 LoRa node and SX1301 LoRa gateway and evaluate its performance in real networks. The evaluation results show that DyLoRa improves the energy efficiency by 41.2% on average compared with the state-of-the- art LoRaWAN ADR.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155407"}, {"primary_key": "2634476", "vector": [], "sparse_vector": [], "title": "Privacy Policy in Online Social Network with Targeted Advertising Business.", "authors": ["Guocheng Liao", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In an online social network, users exhibit personal information to enjoy social interaction. The social network provider (SNP) exploits users' information for revenue generation through targeted advertising. The SNP can present ads to proper users efficiently. Therefore, an advertiser is more willing to pay for targeted advertising. However, the over-exploitation of users' information would invade users' privacy, which would negatively impact users' social activeness. Motivated by this, we study the optimal privacy policy of the SNP with targeted advertising business. We characterize the privacy policy in terms of the fraction of users' information that the provider should exploit, and formulate the interactions among users, advertiser, and SNP as a three-stage Stackelberg game. By carefully leveraging supermodularity property, we reveal from the equilibrium analysis that higher information exploitation will discourage users from exhibiting information, lowering the overall amount of exploited information and harming advertising revenue. We further characterize the optimal privacy policy based on the connection between users' information levels and privacy policy. Numerical results reveal some useful insights that the optimal policy can well balance the users' trade-off between social benefit and privacy loss.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155500"}, {"primary_key": "2634480", "vector": [], "sparse_vector": [], "title": "Maximizing Charging Utility with Obstacles through Fresnel Diffraction Model.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Benefitting from the recent breakthrough of wireless power transfer technology, Wireless Rechargeable Sensor Networks (WRSNs) have become an important research topic. Most prior arts focus on system performance enhancement in an ideal environment that ignores impacts of obstacles. This contradicts with practical applications in which obstacles can be found almost anywhere and have dramatic impacts on energy transmission. In this paper, we concentrate on the problem of charging a practical WRSN in the presence of obstacles to maximize the charging utility under specific energy constraints. First, we propose a new theoretical charging model with obstacles based on Fresnel diffraction model, and conduct experiments to verify its effectiveness. Then, we propose a spatial discretization scheme to obtain a finite feasible charging position set for MC, which largely reduces computation overhead. Afterwards, we reformalize charging utility maximization with energy constraints as a submodular function maximization problem and propose a cost-efficient algorithm with approximation ratio (e-1)/2e (1 - ε) to solve it. Lastly, we demonstrate that our scheme outperforms other algorithms by at least 14.8% in terms of charging utility through test-bed experiments and extensive simulations.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155274"}, {"primary_key": "2634481", "vector": [], "sparse_vector": [], "title": "Guardian: Evaluating Trust in Online Social Networks with Graph Convolutional Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In modern online social networks, each user is typically able to provide a value to indicate how trustworthy their direct friends are. Inferring such a value of social trust between any pair of nodes in online social networks is useful in a wide variety of applications, such as online marketing and recommendation systems. However, it is challenging to accurately and efficiently evaluate social trust between a pair of users in online social networks. Existing works either designed handcrafted rules that rely on specialized domain knowledge, or required a significant amount of computation resources, which affected their scalability.In recent years, graph convolutional neural networks (GCNs) have been shown to be powerful in learning on graph data. Their advantages provide great potential to trust evaluation as social trust can be represented as graph data. In this paper, we propose Guardian, a new end-to-end framework that learns latent factors in social trust with GCNs. Guardian is designed to incorporate social network structures and trust relationships to estimate social trust between any two users. Extensive experimental results demonstrated that Guardian can speedup trust evaluation by up to 2, 827 × with comparable accuracy, as compared to the stateof-the-art in the literature.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155370"}, {"primary_key": "2634483", "vector": [], "sparse_vector": [], "title": "SpiderMon: Towards Using Cell Towers as Illuminating Sources for Keystroke Monitoring.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Qing Gu"], "summary": "Cellular network operators deploy base stations with a high density to ensure radio signal coverage for 4G/5G networks. While users enjoy the high-speed connection provided by cellular networks, an adversary could exploit the dense cellular deployment to detect nearby human movements and even recognize keystroke movements of a victim by passively listening to the CRS broadcast from base stations. To demonstrate this, we develop SpiderMon, the first attempt to perform passive continuous keystroke monitoring using the signal transmitted by commercial cellular base stations. Our experimental results show that SpiderMon can detect keystroke movements at a distance of 15 meters and can recover a 6-digits PIN input with a success rate of more than 51% within ten trials when the victim is behind the wall.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155447"}, {"primary_key": "2634484", "vector": [], "sparse_vector": [], "title": "Optimizing Mixture Importance Sampling Via Online Learning: Algorithms and Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Importance sampling (IS) is widely used in rare event simulation, but it is costly to deal with many rare events simultaneously. For example, a rare event can be the failure to provide the quality-of-service guarantee for a critical network flow. Since network providers often need to deal with many critical flows (i.e., rare events) simultaneously, if using IS, providers have to simulate each rare event with its customized importance distribution individually. To reduce such cost, we propose an efficient mixture importance distribution for multiple rare events, and formulate the mixture importance sampling optimization problem (MISOP) to select the optimal mixture. We first show that the \"search direction\" of mixture is computationally expensive to evaluate, making it challenging to locate the optimal mixture. We then formulate a \" zero learning cost \" online learning framework to estimate the \"search direction\", and learn the optimal mixture from simulation samples of events. We develop two multi-armed bandit online learning algorithms to: (1) Minimize the sum of estimation variances with a regret of (ln T) 2 /T; (2) Minimize the simulation cost with a regret of √ln T/T , where T denotes the number of simulation samples. We demonstrate our method on a realistic network and show that it can reduce the cost measures (i.e., sum of estimation variances and simulation cost) by as high as 61.6% compared with the uniform mixture IS.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155466"}, {"primary_key": "2634485", "vector": [], "sparse_vector": [], "title": "Online Control of Preamble Groups with Priority in Cellular IoT Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Internet of Things (IoT) is the ongoing revolution that offers a truly connected society by integrating several heterogeneous services and applications. A major transformation is urgently needed when the volume of connected devices far exceeds people oriented connections. Due to diversity in applications and requirements, one important trend is that the connected devices could manifest different priorities. With the variety of requirements accordingly, in terms of latency, payload size, number of connections, update frequency, reliability, the Random access process (RAP) as the first step to establish connection between most devices and the base station would also require modification in cellular IoT. In order to prioritize the IoT devices in RAP, we propose a novel online control algorithm with dynamic preamble distribution over multiple priorities. The proposed algorithm recursively estimates the number of active devices in each priority based on Bayesian rule and controls the number of preambles for each priority accordingly. Subsequently, we extend our proposal to incorporate access class baring (ACB) to optimize the algorithm. Extensive simulations show the effectiveness of proposed algorithm over multiple priorities.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155277"}, {"primary_key": "2634488", "vector": [], "sparse_vector": [], "title": "Multi-Task-Oriented Vehicular Crowdsensing: A Deep Learning Approach.", "authors": ["<PERSON>", "<PERSON>ipeng <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the popularity of drones and driverless cars, vehicular crowdsensing (VCS) becomes increasingly widely-used by taking advantage of their high-precision sensors and durability in harsh environments. Since abrupt sensing tasks usually cannot be prepared beforehand, we need a generic control logic fit-for-use all tasks which are similar in nature, but different in their own settings like Point-of-Interest (PoI) distributions. The objectives include to simultaneously maximize the data collection amount, geographic fairness, and minimize the energy consumption of all vehicles for all tasks, which usually cannot be explicitly expressed in a closed-form equation, thus not tractable as an optimization problem. In this paper, we propose a deep reinforcement learning (DRL)-based centralized control, distributed execution framework for multi-task-oriented VCS, called \"DRL-MTVCS\". It includes an asynchronous architecture with spatiotemporal state information modeling, multi-task-oriented value estimates by adaptive normalization, and auxiliary vehicle action exploration by pixel control. We compare with three baselines, and results show that DRL-MTVCS outperforms all others in terms of energy efficiency when varying different numbers of tasks, vehicles, charging stations and sensing range.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155393"}, {"primary_key": "2634490", "vector": [], "sparse_vector": [], "title": "Deeper Exercise Monitoring for Smart Gym using Fused RFID and CV Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Individual activity recognition is crucial for Human-Computer Interaction (HCI) applications, especially in multi-person scenarios. Current approaches, based on wearable sensors or wireless signals (e.g., WiFi and RFID), however, are often focused on single person scenario only, due to the limitation of existing wireless sensing technologies. In order to address the issue, we design a DEeper Exercise Monitoring system, called DEEM, in which we introduce computer vision techniques to facilitate RFID devices to provide exercise estimation support, as well as identifying the users and the objects users hold. We implement this design with COTS Kinect camera and RFID devices in a smart gym application. To the best of our knowledge, it is the first system for estimating multiple people behavior in a complicated gym environment. We conduct extensive experiments to evaluate the performance of the DEEM system. The experimental results show that the matching accuracy can reach 95%, and the exercise estimation accuracy can reach 94% on average.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155360"}, {"primary_key": "2634491", "vector": [], "sparse_vector": [], "title": "LiveScreen: Video Chat Liveness Detection Leveraging Skin Reflection.", "authors": ["Hongbo Liu", "<PERSON><PERSON><PERSON>", "Yucheng Xie", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The rapid advancement of social media and communication technology enables video chat to become an important and convenient way of daily communication. However, such convenience also makes personal video clips easily obtained and exploited by malicious users who launch scam attacks. Existing studies only deal with the attacks that use fabricated facial masks, while the liveness detection that targets the playback attacks using a virtual camera is still elusive. In this work, we develop a novel video chat liveness detection system, LiveScreen, which can track the weak light changes reflected off the skin of a human face leveraging chromatic eigenspace differences. We design an inconspicuous challenge frame with minimal intervention to the video chat and develop a robust anomaly frame detector to verify the liveness of the remote user in the video chat using the response to the challenge frame. Furthermore, we propose resilient defense strategies to defeat both naive and intelligent playback attacks leveraging spatial and temporal verification. We implemented a prototype over both laptop and smartphone platforms and conducted extensive experiments in various realistic scenarios. We show that our system can achieve robust liveness detection with accuracy and false detection rates 97.7% (94.8%) and 1% (1.6%) on smartphones (laptops), respectively.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155400"}, {"primary_key": "2634494", "vector": [], "sparse_vector": [], "title": "Energy-Efficient UAV Crowdsensing with Multiple Charging Stations by Deep Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Different from using human-centric mobile devices like smartphones, unmanned aerial vehicles (UAVs) can be utilized to form a new UAV crowdsensing paradigm, where UAVs are equipped with build-in high-precision sensors, to provide data collection services especially for emergency situations like earthquakes or flooding. In this paper, we aim to propose a new deep learning based framework to tackle the problem that a group of UAVs energy-efficiently and cooperatively collect data from low-level sensors, while charging the battery from multiple randomly deployed charging stations. Specifically, we propose a new deep model called \"j-PPO+ConvNTM\" which contains a novel spatiotemporal module \"Convolution Neural Turing Machine\" (ConvNTM) to better model long-sequence spatiotemporal data, and a deep reinforcement learning (DRL) model called \"j-PPO\", where it has the capability to make continuous (i.e., route planing) and discrete (i.e., either to collect data or go for charging) action decisions simultaneously for all UAVs. Finally, we perform extensive simulation to show its illustrative movement trajectories, hyperparameter tuning, ablation study, and compare with four other baselines.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155535"}, {"primary_key": "2634499", "vector": [], "sparse_vector": [], "title": "Incremental Server Deployment for Scalable NFV-enabled Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network Function Virtualization (NFV) is a new paradigm to enable service innovation through virtualizing traditional network functions. To construct a new NFV-enabled network, there are two critical requirements: minimizing server deployment cost and satisfying switch resource constraints. However, prior work mostly focuses on the server deployment cost, while ignoring the switch resource constraints (e.g., switch's flow-table size). It thus results in a large number of rules on switches and leads to massive control overhead. To address this challenge, we propose an incremental server deployment (INSD) problem for construction of scalable NFV-enabled networks. We prove that the INSD problem is NP-Hard, and there is no polynomial-time algorithm with approximation ratio of (1- ε) ·ln m, where ε is an arbitrarily small value and m is the number of requests in the network. We then present an efficient algorithm with an approximation ratio of 2 · H(q · p) 1 , where q is the number of VNF's categories and p is the maximum number of requests through a switch. We evaluate the performance of our algorithm with experiments on physical platform (Pica8), Open vSwitches, and large-scale simulations. Both experiment and simulation results show high scalability of the proposed algorithm. For example, our solution can reduce the control and rule overhead by about 88% with about 5% additional server deployment, compared with the existing solutions.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155364"}, {"primary_key": "2634500", "vector": [], "sparse_vector": [], "title": "Dynamic User Recruitment with Truthful Pricing for Mobile CrowdSensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile CrowdSensing (MCS) is a promising paradigm that recruits users to cooperatively perform various sensing tasks. In most realistic scenarios, users dynamically participate in MCS, and hence, we should recruit them in an online manner. In general, we prefer to recruit a user who can make the maximum contribution at the least cost, especially when the recruitment budget is limited. The existing strategies usually formulate the user recruitment as the budgeted optimal stopping problem, while we argue that not only the budget but also the time constraints can greatly influence the recruitment performance. For example, if we have less remaining budget but plenty of time, we should recruit users with more patience. In this paper, we propose a dynamic user recruitment strategy with truthful pricing to address the online recruitment problem under the budget and time constraints. To deal with the two constraints, we first estimate the number of users to be recruited and then recruit them in segments. Furthermore, to correct estimation errors and utilize newly obtained information, we dynamically re-adjust the recruiting strategy and also prove that the proposed strategy achieves a competitive ratio of (1 - 1/e) 2 /7. Finally, a reverse auction-based online pricing mechanism is lightly built into the proposed user recruitment strategy, which achieves truthfulness and individual rationality. Extensive experiments on three real-world data sets validate the proposed online user recruitment strategy, which can effectively improve the number of completed tasks under the budget and time constraints.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155242"}, {"primary_key": "2634501", "vector": [], "sparse_vector": [], "title": "Cost Minimization in Multi-Path Communication under Throughput and Maximum Delay Constraints.", "authors": ["Qing<PERSON>", "Haibo Zeng", "Ming<PERSON> Chen", "<PERSON><PERSON><PERSON>"], "summary": "We consider the scenario where a sender streams a flow at a fixed rate to a receiver across a multi-hop network, possibly using multiple paths. Data transmission over a link incurs a cost and a delay, both of which are traffic-dependent. We study the problem of minimizing network transmission cost subject to a maximum delay constraint and a throughput requirement. The problem is important for leveraging edge-cloud computing platforms to support computationally intensive IoT applications, which are sensitive to three critical performance metrics, i.e., cost, maximum delay, and throughput. Our problem jointly considers the three metrics, while existing ones only account for one or two of them. We first show that our problem is uniquely challenging, as (i) it is NP-complete even to find a feasible solution satisfying all constraints, and (ii) directly extending existing solutions to our problem results in problem-dependent maximum delay violations that can be unbounded. We then design both an approximation algorithm and an efficient heuristic. For any feasible instance, our approximation algorithm will achieve a cost no worse than the optimal, while violating the maximum delay constraint and the throughput requirement only by constant ratios. Meanwhile, our heuristic will construct feasible solutions for a large portion (over 60% empirically) of feasible instances, strictly satisfying the maximum delay constraint and the throughput requirement. We further characterize a condition under which the cost of our heuristic must be within a problem-dependent-ratio gap to the optimal. We simulate representative edge computing platforms, and observe that (i) when sacrificing 3% throughput, our approximation algorithm reduces 32% cost as compared to a greedy baseline, and satisfies the maximum delay constraint for 56% simulated instances; (ii) our heuristic solves 62% of feasible instances, and reduces 24% cost as compared to the baseline while strictly satisfying all constraints.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155519"}, {"primary_key": "2634504", "vector": [], "sparse_vector": [], "title": "AoI and Throughput Tradeoffs in Routing-aware Multi-hop Wireless Networks.", "authors": ["Jiadong Lou", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Age-of-Information (AoI) is a newly introduced metric for capturing information updating timeliness, as opposed to the network throughput, which is a conventional performance metric to measure the network transmission speed and robustness as a whole. While considerable work has addressed either optimal AoI or throughput individually, the inherent relationships between the two performance metrics are yet to be explored, especially in multi-hop networks. In this paper, we explore their relationships in multi-hop networks for the very first time, particularly focusing on the impacts of flexible routes on the two metrics. By developing a rigorous mathematical model with interference, channel allocation, link scheduling, and routing path selection taken into consideration, we build the interrelation between AoI and throughput in multi-hop networks. A multi-criteria optimization problem is formulated with the goal of simultaneously minimizing AoI and maximizing network throughput. To solve this problem, we resort to a novel approach by transforming the multi-criteria problem into a single objective one so as to find the weakly Pareto-optimal points iteratively, thereby allowing us to screen all Pareto-optimal points for the solution. A new algorithm based on the piece-wise linearization technique is then developed to closely linearize the non-linear terms in the single objective problem via their linear approximation segments to make it solvable. We formally prove that our algorithms can find all Pareto-optimal points in a finite number of iterations. From simulation results, we identify the tradeoff points of the optimal AoI and throughput, demonstrating that one performance metric improves at the expense of degrading the other, with the routing path found as one of the key factors in determining such a tradeoff.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155444"}, {"primary_key": "2634506", "vector": [], "sparse_vector": [], "title": "On the impact of accurate radio link modeling on the performance of WirelessHART control networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Alessandro D&apos;Innocenzo", "<PERSON><PERSON><PERSON>"], "summary": "The challenges in analysis and co-design of wireless networked control systems are well highlighted by considering wireless industrial control protocols. In this perspective, this paper addresses the modeling and design challenge by focusing on WirelessHART, which is a networking protocol stack widely adopted for wireless industrial automation. Specifically, we first develop and validate a Markov channel model that abstracts the WirelessHART radio link subject to channel impairments and interference. The link quality metrics introduced in the theoretical framework are validated in order to enable the accurate representation of the average and extreme behavior of the radio link. By adopting these metrics, it is straightforward to handle a consistent finite-state abstraction. On the basis of such a model, we then derive a stationary Markov jump linear system model that captures the dynamics of a control loop closed over the radio link. Subsequently, we show that our modeling framework is able to discover and manage the challenging subtleties arising from bursty behavior. A relevant theoretical outcome consists in designing a controller that guarantees stability and improves control performance of the closed-loop system, where other approaches based on a simplified channel model fail.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155285"}, {"primary_key": "2634507", "vector": [], "sparse_vector": [], "title": "SplitCast: Optimizing Multicast Flows in Reconfigurable Datacenter Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Yu"], "summary": "Many modern cloud applications frequently generate multicast traffic, which is becoming one of the primary communication patterns in datacenters. Emerging reconfigurable datacenter technologies enable interesting new opportunities to support such multicast traffic in the physical layer: novel circuit switches offer high-performance inter-rack multicast capabilities. However, not much is known today about the algorithmic challenges introduced by this new technology.This paper presents SplitCast, a preemptive multicast scheduling approach that fully exploits emerging physical-layer multicast capabilities to reduce flow times. SplitCast dynamically reconfigures the circuit switches to adapt to the multicast traffic, accounting for reconfiguration delays. In particular, SplitCast relies on simple single-hop routing and leverages flexibilities by supporting splittable multicast so that a transfer can already be delivered to just a subset of receivers when the circuit capacity is insufficient. Our evaluation results show that SplitCast can reduce flow times significantly compared to state-of-the-art solutions.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155246"}, {"primary_key": "2634510", "vector": [], "sparse_vector": [], "title": "Analysis, Modeling, and Implementation of Publisher-side Ad Request Filtering.", "authors": ["<PERSON> Lv", "<PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Online advertising has been a great driving force for the Internet industry. To maintain a steady growth of advertising revenue, advertisement (ad) publishers have made great efforts to increase the impressions as well as the conversion rate. However, we notice that the results of these efforts are not as good as expected. In detail, to show more ads to the consumers, publishers have to waste a significant amount of server resources to process the ad requests that do not result in consumers' clicks. On the other hand, the increasing ads are also impacting the browsing experience of the consumers. In this paper, we explore the opportunity to improve publishers' overall utility by handling a selective number of requests on ad servers. Particularly, we propose a publisher-side proactive ad request filtration solution Win2. Upon receiving an ad request, Win2 estimates the probability that the consumer will click if serving it. The ad request will be served if the clicking probability is above a dynamic threshold. Otherwise, it will be filtered to reduce the publisher's resource cost and improve consumer experience. We implement Win2 in a large-scale ad serving system and the evaluation results confirm its effectiveness.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155457"}, {"primary_key": "2634511", "vector": [], "sparse_vector": [], "title": "DRAMD: Detect Advanced DRAM-based Stealthy Communication Channels with Neural Networks.", "authors": ["Zhiyuan Lv", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Shared resources facilitate stealthy communication channels, including side channels and covert channels, which greatly endanger the information security, even in cloud environments. As a commonly shared resource, DRAM memory also serves as a source of stealthy channels. Existing solutions rely on two common features of DRAM-based channels, i.e., high cache miss and high bank locality, to detect the existence of such channels. However, such solutions could be defeated. In this paper, we point out the weakness of existing detection solutions by demonstrating a new advanced DRAM-based channel, which utilizes the hardware Intel SGX to conceal cache miss and bank locality. Further, we propose a novel neural network based solution DRAMD to detect such advanced stealthy channels. DRAMD uses hardware performance counters to track not only cache miss events that are used by existing solutions, but also counts of branches and instructions executed, as well as branch misses. Then DRAMD utilizes neural networks to model the access patterns of different applications and therefore detects potential stealthy communication channels. Our evaluation shows that DRAMD achieves up to 99% precision with 100% recall. Furthermore, DRAMD introduces less than 5% performance overheads and negligible impacts on legacy applications.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155515"}, {"primary_key": "2634513", "vector": [], "sparse_vector": [], "title": "Pinpointing Hidden IoT Devices via Spatial-temporal Traffic Fingerprinting.", "authors": ["<PERSON><PERSON> Ma", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Zhenhua Li", "<PERSON><PERSON>"], "summary": "With the popularization of Internet of Things (IoT) devices in smart home and industry fields, a huge number of IoT devices are connected to the Internet. However, what devices are connected to a network may not be known by the Internet Service Provider (ISP), since many IoT devices are placed within small networks (e.g., home networks) and are hidden behind network address translation (NAT). Without pinpointing IoT devices in a network, it is unlikely for the ISP to appropriately configure security policies and effectively manage the network. In this paper, we design an efficient and scalable system via spatial-temporal traffic fingerprinting. Our system can accurately identify typical IoT devices in a network, with the additional capability of identifying what devices are hidden behind NAT and how many they are. Through extensive evaluation, we demonstrate that the system can generally identify IoT devices with an F-Score above 0.999, and estimate the number of the same type of IoT device behind NAT with an average error below 5%. We also perform small-scale (labor-intensive) experiments to show that our system is promising in detecting user-IoT interactions.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155346"}, {"primary_key": "2634514", "vector": [], "sparse_vector": [], "title": "Harmony: Saving Concurrent Transmissions from Harsh RF Interference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The increasing congestion of the RF spectrum is a key challenge for low-power wireless networks using concurrent transmissions. The presence of radio interference can indeed undermine their dependability, as they rely on a tight synchronization and incur a significant overhead to overcome packet loss. In this paper, we present Harmony, a new data collection protocol that exploits the benefits of concurrent transmissions and embeds techniques to ensure a reliable and timely packet delivery despite highly congested channels. Such techniques include, among others, a data freezing mechanism that allows to successfully deliver data in a partitioned network as well as the use of network coding to shorten the length of packets and increase the robustness to unreliable links. Harmony also introduces a distributed interference detection scheme that allows each node to activate various interference mitigation techniques only when strictly necessary, avoiding unnecessary energy expenditures while finding a good balance between reliability and timeliness. An experimental evaluation on real-world testbeds shows that Harmony outperforms state-of-the-art protocols in the presence of harsh Wi-Fi interference, with up to 50% higher delivery rates and significantly shorter end-to-end latencies, even when transmitting large packets.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155423"}, {"primary_key": "2634515", "vector": [], "sparse_vector": [], "title": "Cooperative Service Caching and Workload Scheduling in Mobile Edge Computing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile edge computing is beneficial for reducing service response time and core network traffic by pushing cloud functionalities to network edge. Equipped with storage and computation capacities, edge nodes can cache services of resource-intensive and delay-sensitive mobile applications and process the corresponding computation tasks without outsourcing to central clouds. However, the heterogeneity of edge resource capacities and mismatch of edge storage and computation capacities make it difficult to fully utilize both the storage and computation capacities in the absence of edge cooperation. To address this issue, we consider cooperation among edge nodes and investigate cooperative service caching and workload scheduling in mobile edge computing. This problem can be formulated as a mixed integer nonlinear programming problem, which has non-polynomial computation complexity. Addressing this problem faces challenges of sub-problem coupling, computation-communication tradeoff, and edge node heterogeneity. We develop an iterative algorithm named ICE to solve this problem. It is designed based on Gibbs sampling, which has provably near-optimal performance, and the idea of water filling, which has polynomial computation complexity. Simulation results demonstrate that our algorithm can jointly reduce the service response time and the outsourcing traffic, compared with the benchmark algorithms.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155455"}, {"primary_key": "2634517", "vector": [], "sparse_vector": [], "title": "How to Distribute Computation in Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In network function computation is as a means to reduce the required communication flow in terms of number of bits transmitted per source symbol. However, the rate region for the function computation problem in general topologies is an open problem, and has only been considered under certain restrictive assumptions (e.g. tree networks, linear functions, etc.). In this paper, we propose a new perspective for distributing computation, and formulate a flow-based delay cost minimization problem that jointly captures the costs of communications and computation. We introduce the notion of entropic surjectivity as a measure to determine how sparse the function is and to understand the limits of computation. Exploiting Little's law for stationary systems, we provide a connection between this new notion and the computation processing factor that reflects the proportion of flow that requires communications. This connection gives us an understanding of how much a node (in isolation) should compute to communicate the desired function within the network without putting any assumptions on the topology. Our analysis characterizes the functions only via their entropic surjectivity, and provides insight into how to distribute computation. We numerically test our technique for search, MapReduce, and classification tasks, and infer for each task how sensitive the processing factor to the entropic surjectivity is.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155442"}, {"primary_key": "2634518", "vector": [], "sparse_vector": [], "title": "OKpi: All-KPI Network Slicing Through Efficient Resource Allocation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Networks can now process data as well as transporting it; it follows that they can support multiple services, each requiring different key performance indicators (KPIs). Because of the former, it is critical to efficiently allocate network and computing resources to provide the required services, and, because of the latter, such decisions must jointly consider all KPIs targeted by a service. Accounting for newly introduced KPIs (e.g., availability and reliability) requires tailored models and solution strategies, and has been conspicuously neglected by existing works, which are instead built around traditional metrics like throughput and latency. We fill this gap by presenting a novel methodology and resource allocation scheme, named OKpi, which enables high-quality selection of radio points of access as well as VNF (Virtual Network Function) placement and data routing, with polynomial computational complexity. OKpi accounts for all relevant KPIs required by each service, and for any available resource from the fog to the cloud. We prove several important properties of OKpi and evaluate its performance in two real-world scenarios, finding it to closely match the optimum.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155263"}, {"primary_key": "2634524", "vector": [], "sparse_vector": [], "title": "Distributed Inference Acceleration with Adaptive DNN Partitioning and Offloading.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Deep neural networks (DNN) are the de-facto solution behind many intelligent applications of today, ranging from machine translation to autonomous driving. DNNs are accurate but resource-intensive, especially for embedded devices such as mobile phones and smart objects in the Internet of Things. To overcome the related resource constraints, DNN inference is generally offloaded to the edge or to the cloud. This is accomplished by partitioning the DNN and distributing computations at the two different ends. However, most of existing solutions simply split the DNN into two parts, one running locally or at the edge, and the other one in the cloud. In contrast, this article proposes a technique to divide a DNN in multiple partitions that can be processed locally by end devices or offloaded to one or multiple powerful nodes, such as in fog networks. The proposed scheme includes both an adaptive DNN partitioning scheme and a distributed algorithm to offload computations based on a matching game approach. Results obtained by using a self-driving car dataset and several DNN benchmarks show that the proposed solution significantly reduces the total latency for DNN inference compared to other distributed approaches and is 2.6 to 4.2 times faster than the state of the art.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155237"}, {"primary_key": "2634531", "vector": [], "sparse_vector": [], "title": "Online Placement of Virtual Machines with Prior Data.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The cloud computing market has a wide variety of customers that deploy various applications from deep learning to classical web services. Each application may have different computing, memory and networking requirements, and each customer may be willing to pay a different price for the service. When a request for a VM arrives, the cloud provider decides online whether to serve it or not and which resources to allocate for this purpose. The goal is to maximize the revenue while obeying the constraints imposed by the limited physical infrastructure and its layout.Although requests arrive online, cloud providers are not entirely in the dark; historical data is readily available and may contain strong indications regarding future requests. Thus, standard theoretical models that assume the online player has no prior knowledge are inadequate. In this paper, we adopt a recent theoretical model for the design and analysis of online algorithms that allows taking such historical data into account. We develop new competitive online algorithms for multidimensional resource allocation and analyze their guaranteed performance. Moreover, using extensive simulation over real data from Google and AWS, we show that our new approach yields much higher revenue to cloud providers than currently used heuristics.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155339"}, {"primary_key": "2634532", "vector": [], "sparse_vector": [], "title": "A Dynamic Mechanism for Security Management in Multi-Agent Networked Systems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of designing a dynamic mechanism for security management in an interconnected multi-agent system with N strategic agents and one coordinator. The system is modeled as a network of N vertices. Each agent resides in one of the vertices of the network and has a privately known security state that describes its safety level at each time. The evolution of an agent's security state depends on its own state, the states of its neighbors in the network and on actions taken by a network coordinator. Each agent's utility at time instant t depends on its own state, the states of its neighbors in the network and on actions taken by a network coordinator. The objective of the network coordinator is to take security actions in order to maximize the long-term expected social surplus. Since agents are strategic and their security states are private information, the coordinator needs to incentivize agents to reveal their information. This results in a dynamic mechanism design problem for the coordinator. We leverage the inter-temporal correlations between the agents' security states to identify sufficient conditions under which an incentive compatible expected social surplus maximizing mechanism can be constructed. We then identify two special cases of our formulation and describe how the desired mechanism is constructed in these cases.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155295"}, {"primary_key": "2634534", "vector": [], "sparse_vector": [], "title": "A Converse Result on Convergence Time for Opportunistic Wireless Scheduling.", "authors": ["<PERSON>"], "summary": "This paper proves an impossibility result for stochastic network utility maximization for multi-user wireless systems, including multi-access and broadcast systems. Every time slot an access point observes the current channel states and opportunistically selects a vector of transmission rates. Channel state vectors are assumed to be independent and identically distributed with an unknown probability distribution. The goal is to learn to make decisions over time that maximize a concave utility function of the running time average transmission rate of each user. Recently it was shown that a stochastic Frank-Wolfe algorithm converges to utility-optimality with an error of O(log(T)/T), where T is the time the algorithm has been running. An existing Ω(1/T) converse is known. The current paper improves the converse to Ω(log(T)/T), which matches the known achievability result. The proof uses a reduction from the opportunistic scheduling problem to a <PERSON><PERSON><PERSON> estimation problem. Along the way, it refines a result on <PERSON><PERSON><PERSON> estimation.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155488"}, {"primary_key": "2634536", "vector": [], "sparse_vector": [], "title": "Enabling Communication via Automotive Radars: An Adaptive Joint Waveform Design Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large scale deployment of connected vehicles with cooperative sensing technologies increases the demand on the vehicular communication spectrum in 5.9 GHz allocated for the exchange of safety messages. To support high data rates needed by such applications, the millimeter-wave (mmWave) automotive radar spectrum at 76-81 GHz can be utilized for communication. For this purpose, joint automotive radar-communication (JARC) system designs have been proposed in the literature to perform both functions using the same waveform. However, employing a large band in the mmWave spectrum deteriorates the performance of both radar and communication functions due to frequency-selectivity. In this paper, we address the optimal joint waveform design problem for wideband JARC systems via Orthogonal Frequency-Division Multiplexing (OFDM). We show that the problem is a non-convex quadratically constrained quadratic fractional programming (QCQFP) problem, which is known to be NP-hard. Existing approaches to solve QCQFP include Semidefinite Relaxation (SDR) and randomization approaches, which have high time complexity. Instead, we propose an approximation method to solve QCQFP more efficiently by leveraging structured matrices in the quadratic fractional objective function. Finally, we evaluate the efficacy of the proposed approach through numerical results.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155527"}, {"primary_key": "2634538", "vector": [], "sparse_vector": [], "title": "ROBin: Known-Plaintext Attack Resistant Orthogonal Blinding via Channel Randomization.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Orthogonal blinding based schemes for wireless physical layer security aim to achieve secure communication by injecting noise into channels orthogonal to the main channel and corrupting the eavesdropper's signal reception. These methods, albeit practical, have been proven vulnerable against multiantenna eavesdroppers who can filter the message from the noise. The vulnerability is rooted in the fact that the main channel state remains static in spite of the noise injection, which allows an eavesdropper to estimate it promptly via known symbols and filter out the noise. Our proposed scheme leverages a reconfigurable antenna for <PERSON> to rapidly change the channel state during transmission and a compressive sensing based algorithm for her to predict and cancel the changing effects for <PERSON>. As a result, the communication between <PERSON> and <PERSON> remains clear, whereas randomized channel state prevents <PERSON> from launching the knownplaintext attack. We formally analyze the security of the scheme against both single and multi-antenna eavesdroppers and identify its unique anti-eavesdropping properties due to the artificially created fast-changing channel. We conduct extensive simulations and real-world experiments to evaluate its performance. Empirical results show that our scheme can suppress <PERSON>'s attack success rate to the level of random guessing, even if she knows all the symbols transmitted through other antenna modes.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155439"}, {"primary_key": "2634539", "vector": [], "sparse_vector": [], "title": "Reconfigure and Reuse: Interoperable Wearables for Healthcare IoT.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we propose Over-The-Air (OTA)-based reconfigurable IoT health-monitoring wearables, which tether wirelessly to a low-power and portable central processing and communication hub (CPH). This hub is responsible for the proper packetization and transmission of the physiological data received from the individual sensors connected to each wearable to a remote server. Each wearable consists of a sensor, a communication adapter, and its power module. We introduce low-power adapters with each sensor, which facilitates the sensor-CPH linkups and on-demand network parameter reconfigurations. The newly introduced adapter supports the interoperability of heterogeneous sensors by eradicating the need for sensor-specific modules through OTA-based reconfiguration. The reconfiguration feature allows for new sensors to connect to an existing adapter, without changing the hardware units or any external interface. The proposed system is scalable and enables multiple sensors to connect in a network and work in synchronization with the CPH to achieve semantic and device interoperability among the sensors. We test the implementation in real-time using three different health-monitoring sensor types - temperature, pulse oximeter, and ECG. The results of our real-time system evaluation depict that the proposed system is reliable and responsive in terms of the achieved packet delivery ratio, received signal strength, and energy consumption.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155398"}, {"primary_key": "2634542", "vector": [], "sparse_vector": [], "title": "A Fast Carrier Scheduling Algorithm for Battery-free Sensor Tags in Commodity Wireless Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "New battery-free sensor tags that interoperate with unmodified standard IoT devices and protocols can extend a sensor network's capabilities in a scalable and cost-effective manner. The tags achieve battery-free operation through backscatterrelated techniques, while the standard IoT devices avoid additional dedicated infrastructure by providing the unmodulated carrier that tags need to communicate. However, this approach requires coordination between devices transmitting, receiving and generating carrier, adds extra latency and energy consumption to already constrained devices, and increases interference and contention in the shared spectrum. We present a scheduling mechanism that optimizes the use of carrier generators, minimizing any disruptions to the regular nodes. We employ time slots to coordinate the unmodulated carrier while minimizing latency, energy consumption and overhead radio emissions. We propose an efficient scheduling algorithm that parallelizes communications with battery-free tags when possible and shares carriers among multiple tags concurrently. In our evaluation we demonstrate the feasibility and reliability of our approach in testbed experiments. We find that we can significantly reduce the excess latency and energy consumption caused by the addition of sensor tags when compared to sequential interrogation. We show that the gains tend to improve with the network size and that our solution is close to optimal on average.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155241"}, {"primary_key": "2634546", "vector": [], "sparse_vector": [], "title": "Optimizing Resolution-Adaptive Massive MIMO Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the uplink of a cellular network wherein each base-station (BS) simultaneously communicates with multiple users. Each BS is equipped with a large number of antenna elements and a limited number of RF chains. Each RF chain (on each BS) houses an analog-to-digital converter (ADC) whose bit resolution can be configured. We seek to jointly optimize user transmit powers and ADC bit resolutions in order to maximize the network spectral efficiency, subject to power budget constraints at each user and BS. This joint optimization becomes intractable if we insist on exactly modeling the nonlinear quantization operation performed at each ADC. On the other hand, simplistic approximations made for tractability need not be meaningful. In this work, we propose a methodology based on a constrained worst-case quantization noise formulation, along with another one that assumes quantization noise covariance to be diagonal. In each case, using a series of effective mathematical re-formulations we are able to express our problem in a form that is well-suited for alternating optimization, in which each sub-problem can be efficiently and optimally solved. Through a detailed performance analysis, we demonstrate that the optimized transmit powers and bit resolutions can yield very significant improvements in achievable spectral efficiency, at a reduced sum power consumption and an affordable complexity.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155508"}, {"primary_key": "2634547", "vector": [], "sparse_vector": [], "title": "Minimizing Age of Information in Multi-channel Time-sensitive Information Update Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "Age of information, as a metric measuring the data freshness, has drawn increasing attention due to its importance in many data update applications. Most existing studies have assumed that there is one single channel in the system. In this work, we are motivated by the plethora of multi-channel systems that are being developed, and investigate the following question: how can one exploit multi-channel resources to improve the age performance? We first derive a policy-independent lower bound of the expected long-term average age in a multi-channel system. The lower bound is jointly characterized by the external arrival process and the channel statistics. Since direct analysis of age in multi-channel systems is very difficult, we focus on the asymptotic regime, when the number of users and number of channels both go to infinity. In the many-channel asymptotic regime, we propose a class of Maximum Weighted Matching policies that converge to the lower bound near exponentially fast. In the many-user asymptotic regime, we design a class of Randomized Maximum Weighted Matching policies that achieve a constant competitive ratio compared to the lower bound. Finally, we use simulations to validate the aforementioned results.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155420"}, {"primary_key": "2634548", "vector": [], "sparse_vector": [], "title": "Network Slicing in Heterogeneous Software-defined RANs.", "authors": ["Qiaofeng Qin", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Marina Thottan", "<PERSON><PERSON><PERSON>"], "summary": "5G technologies promise to revolutionize mobile networks and push them to the limits of resource utilization. Besides better capacity, we also need better resource management via virtualization. End-to-end network slicing not only involves the core but also the Radio Access Network (RAN) which makes this a challenging problem. This is because multiple alternative radio access technologies exist (e. g. ,LTE, WLAN, and WiMAX), and there is no unifying abstraction to compare and compose from diverse technologies. In addition, existing work assumes that all RAN infrastructure exists under a single administrative domain. Software-Defined Radio Access Network (SD-RAN) offers programmability that facilitates a unified abstraction for resource sharing and composition across multiple providers harnessing different technology stacks. In this paper we propose a new architecture for heterogeneous RAN slicing across multiple providers. A central component in our architecture is a service orchestrator that interacts with multiple network providers and service providers to negotiate resource allocations that are jointly optimal. We propose a double auction mechanism that captures the interaction among selfish parties and guarantees convergence to optimal social welfare in finite time. We then demonstrate the feasibility of our proposed system by using open source SD-RAN systems such as EmPOWER (WiFi) and FlexRAN (LTE).", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155532"}, {"primary_key": "2634549", "vector": [], "sparse_vector": [], "title": "Exploring Best Arm with Top Reward-Cost Ratio in Stochastic Bandits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ming Jin", "<PERSON><PERSON><PERSON>"], "summary": "The best arm identification problem in multi-armed bandit model has been widely applied into many practical applications, such as spectrum sensing, online advertising, and cloud computing. Although lots of works have been devoted into this area, most of them do not consider the cost of pulling actions, i.e., a player has to pay some cost when she pulls an arm. Motivated by this, we study a ratio-based best arm identification problem, where each arm is associated with a random reward as well as a random cost. For any δ ∈ (0, 1), with probability at least 1-δ, the player aims to find the optimal arm with the largest ratio of expected reward to expected cost using as few samplings as possible. To solve this problem, we propose three algorithms: 1) a genie-aided algorithm GA; 2) the successive elimination algorithm with unknown gaps SEUG; 3) the successive elimination algorithm with unknown gaps and variance information SEUG-V, where gaps denote the differences between the optimal arm and the suboptimal arms. We show that for all three algorithms, the sample complexities, i.e., the pulling times for all arms, grow logarithmically as 1/δ increases. Moreover, compared to existing works, the running of our elimination-type algorithms is independent of the arm-related parameters, which is more practical. In addition, we also provide a fundamental lower bound for sample complexities of any algorithms under <PERSON><PERSON><PERSON> distributions, and show that the sample complexities of the proposed three algorithms match that of the lower bound in the sense of log 1/δ. Finally, we validate our theoretical results through numerical experiments.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155362"}, {"primary_key": "2634550", "vector": [], "sparse_vector": [], "title": "Joint Access Point Placement and Power-Channel-Resource-Unit Assignment for 802.11ax-Based Dense WiFi with QoS Requirements.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "IEEE 802.11ax is a promising standard for the next-generation WiFi network, which uses orthogonal frequency division multiple access (OFDMA) to segregate the wireless spectrum into time-frequency resource units (RUs). In this paper, we aim at designing an 802.11ax-based dense WiFi network to provide WiFi services to a large number of users within a given area with the following objectives: (1) to minimize the number of access points (APs); (2) to fulfil the users' throughput requirement; and (3) to be resistant to AP failures. We formulate the above into a joint AP placement and power-channel-RU assignment optimization problem, which is NP-hard. To tackle this problem, we first derive an analytical model to estimate each user's throughput under the mechanism of OFDMA and a widely used interference model. We then design a heuristic algorithm to find high-quality solutions with polynomial time complexity. Simulation results show that our algorithm can achieve the optimal performance for a small area of 50×50 m 2 . For a larger area of 100×80 m 2 where we cannot find the optimal solution through an exhaustive search, our algorithm can reduce the number of APs by 32 ~ 55% as compared to the random and Greedy solutions.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155490"}, {"primary_key": "2634551", "vector": [], "sparse_vector": [], "title": "An Adaptive Robustness Evolution Algorithm with Self-Competition for Scale-Free Internet of Things.", "authors": ["<PERSON><PERSON>", "Zilong Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Internet of Things (IoT) includes numerous sensing nodes that constitute a large scale-free network. Optimizing the network topology for increased resistance against malicious attacks is an NP-hard problem. Heuristic algorithms, particularly genetic algorithms, can effectively cope with such problems. However, conventional genetic algorithms are prone to falling into premature convergence owing to the lack of global search ability caused by the loss of population diversity during evolution. Although this can be alleviated by increasing population size, additional computational overhead will be incurred. Moreover, after crossover and mutation operations, individual changes in the population are mixed, and loss of optimal individuals may occur, which will slow down the evolution of the population. Therefore, we combine the population state with the evolutionary process and propose an Adaptive Robustness Evolution Algorithm (AREA) with self-competition for scale-free IoT topologies. In AREA, the crossover and mutation operations are dynamically adjusted according to population diversity to ensure global search ability. Moreover, a self-competitive mechanism is used to ensure convergence. The simulation results demonstrate that AREA is more effective in improving the robustness of scale-free IoT networks than several existing methods.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155426"}, {"primary_key": "2634553", "vector": [], "sparse_vector": [], "title": "Joint Inference on Truth/Rumor and Their Sources in Social Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the contemporary era of information explosion, we are often faced with the mixture of massive truth (true information) and rumor (false information) flooded over social networks. Under such circumstances, it is very essential to infer whether each claim (e.g., news, messages) is a truth or a rumor, and identify their sources, i.e., the users who initially spread those claims. While most prior arts have been dedicated to the two tasks respectively, this paper aims to offer the joint inference on truth/rumor and their sources. Our insight is that a joint inference can enhance the mutual performance on both sides.To this end, we propose a framework named SourceCR, which alternates between two modules, i.e., credibility-reliability training for truth/rumor inference and division-querying for source detection, in an iterative manner. To elaborate, the former module performs a simultaneous estimation of claim credibility and user reliability by virtue of an Expectation Maximization algorithm, which takes the source reliability outputted from the latter module as the initial input. Meanwhile, the latter module divides the network into two different subnetworks labeled via the claim credibility, and in each subnetwork launches source detection by applying querying of theoretical budget guarantee to the users selected via the estimated reliability from the former module. The proposed SourceCR is provably convergent, and algorithmic implementable with reasonable computational complexity. We empirically validate the effectiveness of the proposed framework in both synthetic and real datasets, where the joint inference leads to an up to 35% accuracy of credibility gain and 29% source detection rate gain compared with the separate counterparts.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155478"}, {"primary_key": "2634554", "vector": [], "sparse_vector": [], "title": "Online Bayesian Learning for Rate Selection in Millimeter Wave Cognitive Radio Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of dynamic rate selection in a cognitive radio network (CRN) over the millimeter wave (mmWave) spectrum. Specifically, we focus on the scenario when the transmit power is time varying as motivated by the following applications: i) an energy harvesting CRN, in which the system solely relies on the harvested energy source, and ii) an underlay CRN, in which a secondary user (SU) restricts its transmission power based on a dynamically changing interference temperature limit (ITL) such that the primary user (PU) remains unharmed. Since the channel quality fluctuates very rapidly in mmWave networks and costly channel state information (CSI) is not that useful, we consider rate adaptation over an mmWave channel as an online stochastic optimization problem, and propose a Thompson Sampling (TS) based Bayesian method. Our method utilizes the unimodality and monotonicity of the throughput with respect to rates and transmit powers and achieves logarithmic in time regret with a leading term that is independent of the number of available rates. Our regret bound holds for any sequence of transmits powers and captures the dependence of the regret on the arrival pattern. We also show via simulations that the performance of the proposed algorithm is superior than the stateof-the-art algorithms, especially when the arrivals are favorable.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155530"}, {"primary_key": "2634563", "vector": [], "sparse_vector": [], "title": "SocialDrone: An Integrated Social Media and Drone Sensing System for Reliable Disaster Response.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Social media sensing has emerged as a new disaster response application paradigm to collect real-time observations from online social media users about the disaster status. Due to the noisy nature of social media data, the task of identifying trustworthy information (referred to as \"truth discovery\") has been a crucial task in social media sensing. However, existing truth discovery solutions often fall short of providing accurate results in disaster response applications due to the spread of misinformation and difficulty of an efficient verification in such scenarios. In this paper, we present SocialDrone, a novel closed-loop social-physical active sensing framework that integrates social media and unmanned aerial vehicles (UAVs) for reliable disaster response applications. In SocialDrone, signals emitted from the social media are distilled to drive the drones to target areas to verify the emergency events. The verification results are then taken back to improve the sensing and distillation process on social media. The SocialDrone framework introduces several unique challenges: i) how to drive the drones using the unreliable social media signals? ii) How to ensure the system is adaptive to the high dynamics from both the physical world and social media? iii) How to incorporate real-world constraints (e.g., the deadlines of events, limited number of drones) into the framework? The SocialDrone addresses these challenges by building a novel integrated social-physical sensing system that leverages techniques from game theory, constrained optimization, and reinforcement learning. The evaluation results on a real-world disaster response application show that SocialDrone significantly outperforms state-of-the-art truth discovery schemes and drone-only solutions by providing more effective disaster response.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155522"}, {"primary_key": "2634566", "vector": [], "sparse_vector": [], "title": "DeepWiERL: Bringing Deep Reinforcement Learning to the Internet of Self-Adaptive Things.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent work has demonstrated that cutting-edge advances in deep reinforcement learning (DRL) may be leveraged to empower wireless devices with the much-needed ability to \"sense\" current spectrum and network conditions and \"react\" in real time by either exploiting known optimal actions or exploring new actions. Yet, understanding whether real-time DRL can be at all applied in the resource-challenged embedded IoT domain, as well as designing IoT-tailored DRL systems and architectures, still remains mostly uncharted territory. This paper bridges the existing gap between the extensive theoretical research on wireless DRL and its system-level applications by presenting Deep Wireless Embedded Reinforcement Learning (DeepWiERL), a general-purpose, hybrid software/hardware DRL framework specifically tailored for embedded IoT wireless devices. DeepWiERL provides abstractions, circuits, software structures and drivers to support the training and real-time execution of state-of-the-art DRL algorithms on the device's hardware. Moreover, DeepWiERL includes a novel supervised DRL model selection and bootstrap (S-DMSB) technique that leverages transfer learning and high-level synthesis (HLS) circuit design to orchestrate a neural network architecture that satisfies hardware and application throughput constraints and speeds up the DRL algorithm convergence. Experimental evaluation on a fully-custom software-defined radio testbed (i) proves for the first time the feasibility of real-time DRL-based algorithms on a real-world wireless platform with multiple channel conditions; (ii) shows that DeepWiERL supports 16x data rate and consumes 14x less energy than a software-based implementation, and (iii) indicates that S-DMSB may improve the DRL convergence time by 6x and increase the obtained reward by 45% if prior channel knowledge is available.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155461"}, {"primary_key": "2634568", "vector": [], "sparse_vector": [], "title": "On the Economic Value of Mobile Caching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent growth in user demand for mobile data has strained mobile network infrastructure. One possible solution is to use mobile (i.e., moving) devices to supplement existing infrastructure according to users' needs at different times and locations. For instance, vehicles can be used as communication relays or computation points. However, it is unclear how much value these devices add relative to their deployment costs: they may, for instance, interfere with existing network infrastructure, limiting the potential benefits. We take the first step towards quantifying the value of this supplemental infrastructure by examining the use case of mobile caches. We consider a network operator using both mobile (e.g., vehicular) and stationary (small cell) caches, and find the optimal amount of both types of caches under time- and location-varying user demands, as a function of the cache prices. In doing so, we account for interference between users' connections to the different caches, which requires solving a non-convex optimization problem. We show that there exists a threshold price above which no vehicular caches are purchased. Moreover, as the network operator's budget increases, vehicular caching yields little additional value beyond that provided by small cell caches. These results may help network operators and cache providers find conditions under which vehicles add value to existing networks.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155336"}, {"primary_key": "2634576", "vector": [], "sparse_vector": [], "title": "Looking before Crossing: An Optimal Algorithm to Minimize UAV Energy by Speed Scheduling with a Practical Flight Energy Model.", "authors": ["Feng Shan", "Junzhou Luo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unmanned aerial vehicles (UAVs) are being widely used in wireless communication, e.g., collecting data from ground nodes (GNs), where energy is critical. Existing works combine speed scheduling, i.e., the controlling of speed, with trajectory design for UAVs, making it complicated to solve while loses focus on the fundamental nature of speed scheduling. We focus on speed scheduling by considering straight line flights, with applications in monitoring power transmission lines, roads, water/oil/gas pipes and rivers/coasts. By real-world flight tests, we disclose a speed-related flight energy consumption model, distinct from typical distance-related or duration-related models. Based on such a practical energy model, we develop the looking before crossing (virtual rooms) algorithm, where virtual rooms on the time-distance diagram represent the spatio-temporal constraint of GNs in wireless transmission. This algorithm is proved to be optimal in solving the offline problem, where all information is known before scheduling. For the online problem, i.e., GN information is not unavailable unless flies close, we propose an offline-inspired online heuristic. Simulation shows its performance is near the offline optimal. Our study on the practical flight energy model and speed scheduling sheds light on a new research direction on UAV-aided wireless communication.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155376"}, {"primary_key": "2634577", "vector": [], "sparse_vector": [], "title": "Reducing the Service Function Chain Backup Cost over the Edge and Cloud by a Self-adapting Scheme.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Huang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The fast development of virtual network functions (VNFs) brings new opportunities to network service deployment on edge networks. For complicated services, VNFs can chain up to form service function chains (SFCs). Despite the promises, it is still not clear how to backup VNFs to minimize the cost while meeting the SFC availability requirements in an online manner. In this paper, we propose a novel self-adapting scheme named SAB to efficiently backup VNFs over both the edge and the cloud. Specifically, SAB uses both static backups and dynamic ones created on the fly to accommodate the resource limitation of edge networks. For each VNF backup, SAB determines whether to place it on the edge or the cloud, and if on the edge, which edge server to use for load balancing. SAB does not assume failure rates of VNFs but instead strives to find the sweet point between the desired availability of SFCs and the backup cost. Both theoretical performance bounds and extensive simulation results highlight that SAB provides significantly higher availability with lower backup cost compared with existing baselines.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155394"}, {"primary_key": "2634578", "vector": [], "sparse_vector": [], "title": "Verifying Policy-based Routing at Internet Scale.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Routing policy configuration plays a crucial role in determining the path that network traffic takes to reach a destination. Network administrators/operators typically decide the routing policy for their networks/routers independently. The paths/routes resulted from these independently configured routing policies might not necessarily meet the intent of the network administrators/operators. Even the very basic networkwide properties of the routing policies such as reachability between a pair of nodes need to be verified.In this paper, we propose a scheme that characterizes routingpolicy verification problems into a Satisfiability Modulo Theories (SMT) problems. The key idea is to formulate the SMT model in a policy-aware manner so as to reduce/eliminate the mutual dependencies between variables as much as possible. Further, we reduce the size of the generated SMT model through pruning. We implement and evaluate the policy-aware model through an outof-box SMT solver. The experimental results show that the policyaware model can reduce the time it takes to perform verification by as much as 100x even under a modest topology size. It takes only a few minutes to answer a query for a topology containing tens of thousands of nodes.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155235"}, {"primary_key": "2634579", "vector": [], "sparse_vector": [], "title": "Unsupervised and Network-Aware Diagnostics for Latent Issues in Network Information Databases.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network management database (NID) is essential in modern large-scale networks. Operators rely on NID to provide accurate and up-to-date data, however, NID-like any other databases-can suffers from latent issues such as inconsistent, incorrect, and missing data. In this work, we first reveal latent data issues in NIDs using real traces from a large cloud provider, Tencent. Then we design and implement a diagnostic system, NAuditor, for unsupervised identification of latent issues in NIDs. In the process, we design a compact and graph-based data structure to efficiently encode the complete NID as a Knowledge Graph, and model the diagnostic problems as unsupervised Knowledge Graph Refinement problems. We show that the new encoding achieves superior performance than alternatives, and can facilitate adoption of state-of-the-art KGR algorithms. We also have used NAuditor in a production NID, and found 71 real latent issues, which all have been confirmed by operators.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155512"}, {"primary_key": "2634582", "vector": [], "sparse_vector": [], "title": "Distributed and Optimal RDMA Resource Scheduling in Shared Data Center Networks.", "authors": ["<PERSON><PERSON>", "Junzhou Luo", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Remote Direct Memory Access (RDMA) suffers from unfairness issues and performance degradation when multiple applications share RDMA network resources. Hence, an efficient resource scheduling mechanism is urged to optimally allocates RDMA resources among applications. However, traditional Network Utility Maximization (NUM) based solutions are inadequate for RDMA due to three challenges: 1) The standard NUM-oriented algorithm cannot deal with coupling variables introduced by multiple dependent RDMA operations; 2) The stringent constraint of RDMA on-board resources complicates the standard NUM by bringing extra optimization dimensions; 3) Naively applying traditional algorithms for NUM suffers from scalability and convergence issues in solving a large-scale RDMA resource scheduling problem.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155533"}, {"primary_key": "2634583", "vector": [], "sparse_vector": [], "title": "A Zeroth-Order ADMM Algorithm for Stochastic Optimization over Distributed Processing Networks.", "authors": ["<PERSON><PERSON>", "Atilla E<PERSON>il<PERSON>z"], "summary": "In this paper, we address the problem of stochastic optimization over distributed processing networks, which is motivated by machine learning applications performed in data centers. In this problem, each of a total n nodes in a network receives stochastic realizations of a private function f i (x) and aims to reach a common value that minimizes Σ i=1 n f i (x) via local updates and communication with its neighbors. We focus on zeroth-order methods where only function values of stochastic realizations can be used. Such kind of methods, which are also called derivative-free, are especially important in solving realworld problems where either the (sub)gradients of loss functions are inaccessible or inefficient to be evaluated. To this end, we propose a method called Distributed Stochastic Alternating Direction Method of Multipliers (DS-ADMM) which can choose to use two kinds of gradient estimators for different assumptions. The convergence rates of DS-ADMM are O(n√k log (2k)/T) for general convex loss functions and O(n k log (2kT)/T) for strongly convex functions in terms of optimality gap, where k is the dimension of domain and T is the time horizon of the algorithm. The rates can be improved to O(n/√T )and O(n log T/T) if objective functions have Lipschitz gradients. All these results are better than previous distributed zerothorder methods. Lastly, we demonstrate the performance of DSADMM via experiments of two examples called distributed online least square and distributed support vector machine arising in estimation and classification tasks.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155520"}, {"primary_key": "2634584", "vector": [], "sparse_vector": [], "title": "Communication-Efficient Distributed Deep Learning with Merged Gradient Sparsification on GPUs.", "authors": ["Shaohuai Shi", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Distributed synchronous stochastic gradient descent (SGD) algorithms are widely used in large-scale deep learning applications, while it is known that the communication bottleneck limits the scalability of the distributed system. Gradient sparsification is a promising technique to significantly reduce the communication traffic, while pipelining can further overlap the communications with computations. However, gradient sparsification introduces extra computation time, and pipelining requires many layer-wise communications which introduce significant communication startup overheads. Merging gradients from neighbor layers could reduce the startup overheads, but on the other hand it would increase the computation time of sparsification and the waiting time for the gradient computation. In this paper, we formulate the trade-off between communications and computations (including backward computation and gradient sparsification) as an optimization problem, and derive an optimal solution to the problem. We further develop the optimal merged gradient sparsification algorithm with SGD (OMGS-SGD) for distributed training of deep learning. We conduct extensive experiments to verify the convergence properties and scaling performance of OMGS-SGD. Experimental results show that OMGS-SGD achieves up to 31% end-to-end time efficiency improvement over the state-of-the-art sparsified SGD while preserving nearly consistent convergence performance with original SGD without sparsification on a 16-GPU cluster connected with 1Gbps Ethernet.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155269"}, {"primary_key": "2634586", "vector": [], "sparse_vector": [], "title": "Inferring Firewall Rules by Cache Side-channel Analysis in Network Function Virtualization.", "authors": ["<PERSON><PERSON><PERSON>", "Dong<PERSON>ung Koo", "Junbeom Hur"], "summary": "Network function virtualization takes advantage of virtualization technology to achieve flexibility in network service provisioning. However, it comes at the cost of security risks caused by cache side-channel attacks on virtual machines. In this study, we investigate the security impact of these attacks on virtualized network functions. In particular, we propose a novel cache-based reconnaissance technique against virtualized Linux-based firewalls. The proposed technique has significant advantages in the perspective of attackers. First, it enhances evasiveness against intrusion detection owing to the ability of source spoofing. Second, it allows inference on a wide variety of filtering rules. During experiment in VyOS, the proposed method could infer the firewall rules with an accuracy of more than 90% by using only a few dozen packets. We also present countermeasures to mitigate cache-based attacks on virtualized network functions.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155449"}, {"primary_key": "2634593", "vector": [], "sparse_vector": [], "title": "Reliable Wide-Area Backscatter via Channel Polarization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A long-standing vision of backscatter communications is to provide long-range connectivity and high-speed transmissions for batteryless Internet-of-Things (IoT). Recent years have seen major innovations in designing backscatters toward this goal. Yet, they either operate at a very short range, or experience extremely low throughput. This paper takes one step further toward breaking this stalemate, by presenting PolarScatter that exploits channel polarization in long-range backscatter links. We transform backscatter channels into nearly noiseless virtual channels through channel polarization, and convey bits with extremely low error probability. Specifically, we propose a new polar code scheme that automatically adapts itself to different channel quality, and design a low-cost encoder to accommodate polar codes on resource-constrained backscatter tags. We build a prototype PCB tag and test it in various outdoor and indoor environments. Our experiments show that our prototype achieves up to 10× throughput gain, or extends the range limit by 1.8× compared with the state-of-the-art long-range backscatter solution. We also simulate an IC design in TSMC 65 nm LP CMOS process. Compared with traditional encoders, our encoder reduces storage overhead by three orders of magnitude, and lowers the power consumption to tens of microwatts.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155391"}, {"primary_key": "2634598", "vector": [], "sparse_vector": [], "title": "Online Spread Estimation with Non-duplicate Sampling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Per-flow spread measurement in high-speed networks has many practical applications. It is a more difficult problem than the traditional per-flow size measurement. Most prior work is based on sketches, focusing on reducing their space requirements in order to fit in on-chip cache memory. This design allows measurement to be performed at the line rate, but it has to accept tradeoff with expensive computation for spread queries (unsuitable for online operations) and large errors in spread estimation for small flows. This paper complements the prior art with a new spread estimator design based on an on-chip/off-chip model which is common in practice. The new estimator supports online queries in real time and produces spread estimation with much better accuracy. By storing traffic data in off-chip memory, our new design faces a key technical challenge of efficient non-duplicate sampling. We propose a two-stage solution with on-chip/off-chip data structures and algorithms, which are not only efficient but also highly configurable for a variety of probabilistic performance guarantees. The experiment results based on real Internet traffic traces show that our estimator reduces the mean relative and absolute error by around one order of magnitude, and achieves both space-efficiency and accuracy-efficiency in flow classification for small flows compared to the prior art.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155525"}, {"primary_key": "2634601", "vector": [], "sparse_vector": [], "title": "Towards Personalized Privacy-Preserving Incentive for Truth Discovery in Crowdsourced Binary-Choice Question Answering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Liantao Wu", "<PERSON><PERSON>", "<PERSON><PERSON> Qi", "<PERSON><PERSON>"], "summary": "Truth discovery is an effective tool to unearth truthful answers in crowdsourced question answering systems. Incentive mechanisms are necessary in such systems to stimulate worker participation. However, most of existing incentive mechanisms only consider compensating workers' resource cost, while the cost incurred by potential privacy leakage has been rarely incorporated. More importantly, to the best of our knowledge, how to provide personalized payments for workers with different privacy demands remains uninvestigated thus far. In this paper, we propose a contract-based personalized privacy-preserving incentive mechanism for truth discovery in crowdsourced question answering systems, named PINTION, which provides personalized payments for workers with different privacy demands as a compensation for privacy cost, while ensuring accurate truth discovery. The basic idea is that each worker chooses to sign a contract with the platform, which specifies a privacy-preserving level (PPL) and a payment, and then submits perturbed answers with that PPL in return for that payment. Specifically, we respectively design a set of optimal contracts under both complete and incomplete information models, which could maximize the truth discovery accuracy, while satisfying the budget feasibility, individual rationality and incentive compatibility properties. Experiments on both synthetic and real-world datasets validate the feasibility and effectiveness of PINTION.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155429"}, {"primary_key": "2634606", "vector": [], "sparse_vector": [], "title": "FastVA: Deep Learning Video Analytics Through Edge Processing and NPU in Mobile.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many mobile applications have been developed to apply deep learning for video analytics. Although these advanced deep learning models can provide us with better results, they also suffer from the high computational overhead which means longer delay and more energy consumption when running on mobile devices. To address this issue, we propose a framework called FastVA, which supports deep learning video analytics through edge processing and Neural Processing Unit (NPU) in mobile. The major challenge is to determine when to offload the computation and when to use NPU. Based on the processing time and accuracy requirement of the mobile application, we study two problems: Max-Accuracy where the goal is to maximize the accuracy under some time constraints, and Max-Utility where the goal is to maximize the utility which is a weighted function of processing time and accuracy. We formulate them as integer programming problems and propose heuristics based solutions. We have implemented FastVA on smartphones and demonstrated its effectiveness through extensive evaluations.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155476"}, {"primary_key": "2634608", "vector": [], "sparse_vector": [], "title": "Dynamically Adaptive Cooperation Transmission among Satellite-Ground Integrated Networks.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "It is a desirable goal to fuse satellite and ground integrated networks (SGINs) to improve the resource utilization efficiency. However, existing work did not consider how to integrate them as a whole network because they lack of function-configurable network management and efficient cooperation among satellite and ground networks. In this paper, we firstly propose a SDN-based network architecture, where resources in SGINs are managed and scheduled in the layered and on-demand way. Then, we formulate the dynamical cooperation transmission in SGINs as an optimization problem and prove its NP hardness. Finally, to realize deeper-level resource fusion, we propose an efficient transmission approach DEEPER (aDaptive satEllitE-ground cooPerativE tRasmission) based on dynamical cooperation among satellite and ground networks, which is network-aware and workload-driven. Comprehensive experiment results demonstrate that our approach outperforms related schemes in terms of network throughput, end-to-end delay, transmission quality and load balancing.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155485"}, {"primary_key": "2634611", "vector": [], "sparse_vector": [], "title": "SpreadSketch: Toward Invertible and Network-Wide Detection of Superspreaders.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Superspreaders (i.e., hosts with numerous distinct connections) remain severe threats to production networks. How to accurately detect superspreaders in real-time at scale remains a non-trivial yet challenging issue. We present SpreadSketch, an invertible sketch data structure for network-wide superspreader detection with the theoretical guarantees on memory space, performance, and accuracy. SpreadSketch tracks candidate super-spreaders and embeds estimated fan-outs in binary hash strings inside small and static memory space, such that multiple SpreadSketch instances can be merged to provide a network-wide measurement view for recovering superspreaders and their estimated fan-outs. We present formal theoretical analysis on SpreadSketch in terms of space and time complexities as well as error bounds. Trace-driven evaluation shows that SpreadSketch achieves higher accuracy and performance over state-of-the-art sketches. Furthermore, we prototype SpreadSketch in P4 and show its feasible deployment in commodity hardware switches.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155541"}, {"primary_key": "2634613", "vector": [], "sparse_vector": [], "title": "ZeroWall: Detecting Zero-Day Web Attacks through Encoder-Decoder Recurrent Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The following topics are dealt with: learning (artificial intelligence); optimisation; telecommunication traffic; Internet; cloud computing; computational complexity; mobile computing; resource allocation; security of data; and telecommunication network routing.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155278"}, {"primary_key": "2634616", "vector": [], "sparse_vector": [], "title": "CoLoRa: Enabling Multi-Packet Reception in LoRa.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LoRa, more generically Low-Power Wide Area Network (LPWAN), is a promising platform to connect Internet of Things. It enables low-cost low-power communication at a few kbps over upto tens of kilometers with a 10-year battery lifetime. However, practical LPWAN deployments suffer from collisions, given the dense deployment of devices and wide coverage area. We propose CoLoRa, a protocol to decompose large numbers of concurrent transmissions from one collision in LoRa networks. At the heart of CoLoRa, we utilize packet time offset to disentangle collided packets. CoLoRa incorporates several novel techniques to address practical challenges. (1) We translate time offset, which is difficult to measure, to frequency features that can be reliably measured. (2) We propose a method to cancel inter-packet interference and extract accurate feature from low SNR LoRa signal. (3) We address frequency shift incurred by CFO and time offset for LoRa decoding. We implement CoLoRa on USRP N210 and evaluate its performance in both indoor and outdoor networks. CoLoRa is implemented in software at the base station and it can work for COTS LoRa nodes. The evaluation results show that CoLoRa improves the network throughput by 3.4× compared with Choir and by 14× compared with LoRaWAN.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155509"}, {"primary_key": "2634617", "vector": [], "sparse_vector": [], "title": "Homa: An Efficient Topology and Route Management Approach in SD-WAN Overlays.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents an efficient topology and route management approach in Software-Defined Wide Area Networks (SD-WAN). Traditional WANs suffer from low utilization and lack of global view of the network. Therefore, during failures, topology/service/traffic changes, or new policy requirements, the system does not always converge to the global optimal state. Using Software Defined Networking architectures in WANs provides the opportunity to design WANs with higher fault tolerance, scalability, and manageability. We exploit the correlation matrix derived from monitoring system between the virtual links to infer the underlying route topology and propose a route update approach that minimizes the total route update cost on all flows. We formulate the problem as an integer linear programming optimization problem and provide a centralized control approach that minimizes the total cost while satisfying the quality of service (QoS) on all flows. Experimental results on real network topologies demonstrate the effectiveness of the proposed approach in terms of disruption cost and average disrupted flows.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155503"}, {"primary_key": "2634619", "vector": [], "sparse_vector": [], "title": "Unifying AoI Minimization and Remote Estimation - Optimal Sensor/Controller Coordination with Random Two-way Delay.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The ubiquitous usage of communication networks in modern sensing and control applications has kindled new interests on the timing-based coordination between sensors and controllers, i.e., how to use the \"waiting time\" to improve the system performance. Contrary to the common belief that a zero-wait policy is always optimal, <PERSON> et al. showed that a controller can strictly improve the data freshness, the so-called Age-of-Information (AoI), by postponing transmission in order to lengthen the duration of staying in a good state. The optimal waiting policy for the sensor side was later characterized in the context of remote estimation. Instead of focusing on the sensor and controller sides separately, this work develops the optimal joint sensor/controller waiting policy in a Wiener-process system. The results can be viewed as strict generalization of the above two important results in the sense that not only do we consider joint sensor/controller designs (as opposed to sensor-only or controller-only schemes), but we also assume random delay in both the forward and feedback directions (as opposed to random delay in only one direction). In addition to provable optimality, extensive simulation is used to verify the performance of the proposed scheme in various settings.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155238"}, {"primary_key": "2634620", "vector": [], "sparse_vector": [], "title": "On the Power of Randomization for Scheduling Real-Time Traffic in Wireless Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we consider the problem of scheduling real-time traffic in wireless networks under a conflict-graph interference model and single-hop traffic. The objective is to guarantee that at least a certain fraction of packets of each link are delivered within their deadlines, which is referred to as delivery ratio. This problem has been studied before under restrictive frame-based traffic models, or greedy maximal scheduling schemes like LDF (Largest-Deficit First) that can lead to poor delivery ratio for general traffic patterns. In this paper, we pursue a different approach through randomization over the choice of maximal links that can transmit at each time. We design randomized policies in collocated networks, multipartite networks, and general networks, that can achieve delivery ratios much higher than what is achievable by LDF. Further, our results apply to traffic (arrival and deadline) processes that evolve as positive recurrent Markov chains. Hence, this work is an improvement with respect to both efficiency and traffic assumptions compared to the past work. We further present extensive simulation results over various traffic patterns and interference graphs to illustrate the gains of our randomized policies over LDF variants.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155504"}, {"primary_key": "2634621", "vector": [], "sparse_vector": [], "title": "Network-Aware Optimization of Distributed Learning for Fog Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Fog computing promises to enable machine learning tasks to scale to large amounts of data by distributing processing across connected devices. Two key challenges to achieving this are (i) heterogeneity in devices' compute resources and (ii) topology constraints on which devices can communicate. We are the first to address these challenges by developing a network-aware distributed learning optimization methodology where devices process data for a task locally and send their learnt parameters to a server for aggregation at certain time intervals. Unlike traditional federated learning frameworks, our method enables devices to offload their data processing tasks, with these decisions determined through a convex data transfer optimization problem that trades off costs associated with devices processing, offloading, and discarding data points. We analytically characterize the optimal data transfer solution for different fog network topologies, showing for example that the value of a device offloading is approximately linear in the range of computing costs in the network. Our subsequent experiments on both synthetic and real-world datasets we collect confirm that our algorithms are able to improve network resource utilization substantially without sacrificing the accuracy of the learned model.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155372"}, {"primary_key": "2634622", "vector": [], "sparse_vector": [], "title": "Online Network Flow Optimization for Multi-Grade Service Chains.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of in-network execution of data analytic services using multi-grade VNF chains. The nodes host VNFs offering different and possibly time-varying gains for each stage of the chain, and our goal is to maximize the analytics performance while minimizing the data transfer and processing costs. The VNFs' performance is revealed only after their execution, since it is data-dependent or controlled by third-parties, while the service requests and network costs might also vary with time. We devise an operation algorithm that learns, on the fly, the optimal routing policy and the composition and length of each chain. Our algorithm combines a lightweight sampling technique and a Lagrange-based primal-dual iteration, allowing it to be scalable and attain provable optimality guarantees. We demonstrate the performance of the proposed algorithm using a video analytics service, and explore how it is affected by different system parameters. Our model and optimization framework is readily extensible to different types of networks and services.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155341"}, {"primary_key": "2634623", "vector": [], "sparse_vector": [], "title": "Setting the Yardstick: A Quantitative Metric for Effectively Measuring Tactile Internet.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The next frontier in communications is teleoperation – manipulation and control of remote environments. Compared to conventional networked applications, teleoperation poses widely different requirements, ultra-low latency (ULL) being the primary one. Teleoperation, along with a host of other applications requiring ULL communication, is termed as Tactile Internet (TI). A significant redesign of conventional networking techniques is necessary to realize TI applications. Further, these advancements can be evaluated only when meaningful performance metrics are available. However, existing TI performance metrics fall severely short of comprehensively characterizing TI performance. In this paper, we take the first step towards bridging this gap. To this end, we propose a method that captures the fine-grained performance of TI in terms of delay and precision. We take Dynamic Time Warping (DTW) as the basis of our work and identify whether it is sufficient in characterizing TI systems. We refine DTW by developing a framework called Effective Time- and Value-Offset (ETVO) that extracts fine-grained time and value offsets between input and output signals of TI. Using ETVO, we present two quantitative metrics for TI – Effective Delay-Derivative (EDD) and Effective Root Mean Square Error. Through rigorous experiments conducted on a realistic TI setup, we demonstrate the potential of the proposed metrics to precisely characterize TI interactions.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155540"}, {"primary_key": "2634624", "vector": [], "sparse_vector": [], "title": "Stochastic Network Utility Maximization with Unknown Utilities: Multi-Armed Bandits Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we study a novel Stochastic Network Utility Maximization (NUM) problem where the utilities of agents are unknown. The utility of each agent depends on the amount of resource it receives from a network operator/controller. The operator desires to do a resource allocation that maximizes the expected total utility of the network. We consider threshold type utility functions where each agent gets non-zero utility if the amount of resource it receives is higher than a certain threshold. Otherwise, its utility is zero (hard real-time). We pose this NUM setup with unknown utilities as a regret minimization problem. Our goal is to identify a policy that performs as `good' as an oracle policy that knows the utilities of agents. We model this problem setting as a bandit setting where feedback obtained in each round depends on the resource allocated to the agents. We propose algorithms for this novel setting using ideas from Multiple-Play Multi-Armed Bandits and Combinatorial Semi-Bandits. We show that the proposed algorithm is optimal when all agents have the same utility. We validate the performance guarantees of our proposed algorithms through numerical experiments.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155234"}, {"primary_key": "2634625", "vector": [], "sparse_vector": [], "title": "A Randomly Accessible Lossless Compression Scheme for Time-Series Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We detail a practical compression scheme for lossless compression of time-series data, based on the emerging concept of generalized deduplication. As data is no longer stored for just archival purposes, but needs to be continuously accessed in many applications, the scheme is designed for low-cost random access to its compressed data, avoiding decompression. With this method, an arbitrary bit of the original data can be read by accessing only a few hundred bits in the worst case, several orders of magnitude fewer than state-of-the-art compression schemes. Subsequent retrieval of bits requires visiting at most a few tens of bits. A comprehensive evaluation of the compressor on eight real-life data sets from various domains is provided. The cost of this random access capability is a loss in compression ratio compared with the state-of-the-art compression schemes BZIP2 and 7z, which can be as low as 5% depending on the data set. Compared to GZIP, the proposed scheme has a better compression ratio for most of the data sets. Our method has massive potential for applications requiring frequent random accesses, as the only existing approach with comparable random access cost is to store the data without compression.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155450"}, {"primary_key": "2634627", "vector": [], "sparse_vector": [], "title": "T-cache: Dependency-free Ternary Rule Cache for Policy-based Forwarding.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Ternary Content Addressable Memory (TCAM) is widely used by modern routers and switches to support policy-based forwarding. However, the limited TCAM capacity does not scale with the ever-increasing rule table size. Using TCAM just as a rule cache is a plausible solution, but one must resolve several tricky issues including the rule dependency and the associated TCAM updates. In this paper, we propose a new approach which can generate dependency-free rules to cache. By removing the rule dependency, the TCAM update problem also disappears. We provide the complete T-cache system design including slow path processing and cache replacement. Evaluations based on real-world and synthesized rule tables and traces show that T-cache is efficient and robust for network traffic in various scenarios.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155470"}, {"primary_key": "2634628", "vector": [], "sparse_vector": [], "title": "IoTArgos: A Multi-Layer Security Monitoring System for Internet-of-Things in Smart Homes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The wide deployment of IoT systems in smart homes has changed the landscape of networked systems, Internet traffic, and data communications in residential broadband networks as well as the Internet at large. However, recent spates of cyber attacks and threats towards IoT systems in smart homes have revealed prevalent vulnerabilities and risks of IoT systems ranging from data link layer protocols to application services. To address the security challenges of IoT systems in smart homes, this paper introduces IoTArgos, a multi-layer security monitoring system, which collects, analyzes, and characterizes data communications of heterogeneous IoT devices via programmable home routers. More importantly, this system extracts a variety of multi-layer data communication features and develops supervised learning methods for classifying intrusion activities at system, network, and application layers. In light of the potential zero-day or unknown attacks, IoTArgos also incorporates unsupervised learning algorithms to discover unusual or suspicious behaviors towards smart home IoT systems. Our extensive experimental evaluations have demonstrated that IoTArgos is able to detect anomalous activities targeting IoT devices in smart homes with a precision of 0.9876 and a recall of 0.9763.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155424"}, {"primary_key": "2634629", "vector": [], "sparse_vector": [], "title": "Joint Configuration Adaptation and Bandwidth Allocation for Edge-based Real-time Video Analytics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Real-time analytics on video data demands intensive computation resources and high energy consumption. Traditional cloud-based video analytics relies on large centralized clusters to ingest video streams. With edge computing, we can offload compute-intensive analysis tasks to the nearby server, thus mitigating long latency incurred by data transmission via wide area networks. When offloading frames from the front-end device to the edge server, the application configuration (frame sampling rate and frame resolution) will impact several metrics, such as energy consumption, analytics accuracy and user-perceived latency. In this paper, we study the configuration adaption and bandwidth allocation for multiple video streams, which are connected to the same edge node sharing an upload link. We propose an efficient online algorithm, called JCAB, which jointly optimizes configuration adaption and bandwidth allocation to address a number of key challenges in edge-based video analytics systems, including edge capacity limitation, unknown network variation, intrusive dynamics of video contents. Our algorithm is developed based on Lyapunov optimization and Markov approximation, works online without requiring future information, and achieves a provable performance bound. Simulation results show that JCAB can effectively balance the analytics accuracy and energy consumption while keeping low system latency.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155524"}, {"primary_key": "2634630", "vector": [], "sparse_vector": [], "title": "Travel with Your Mobile Data Plan: A Location-Flexible Data Service.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile Network Operators (MNOs) provide wireless data services based on a tariff data plan with a month data cap. Traditionally, the data cap is only valid for domestic data consumption and users have to pay extra roaming fees for overseas data consumption. A recent location-flexible service allows the user to access the domestic data cap in overseas locations (by configuring location-flexibility with a daily fee). This paper studies the economic effect of the location-flexibility on the overseas market. The overseas market comprises users who travel overseas within the month, thus is monthly variant. Each user decides his joint flexibility configuration and data consumption (J-FCDC) every day. The user's J-FCDC problem is an on-line payoff maximization. We analyze its off-line problem (which is NP-hard) and design an on-line strategy with provable performance. Moreover, we propose a pricing policy for the location-flexible service without relying on the market statistic information. We find that the location-flexibility induces users to consume more data in low-valuation days, and the MNO benefits from stimulating users' data consumption through an appropriate pricing. Numerical results based on empirical data show that the location-flexibility improves the MNO's revenue by 18% and the users' payoffs by 12% on average.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155405"}, {"primary_key": "2634631", "vector": [], "sparse_vector": [], "title": "MultiLive: Adaptive Bitrate Control for Low-delay Multi-party Interactive Live Streaming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In multi-party interactive live streaming, each user can act as both the sender and the receiver of a live video stream. Designing adaptive bitrate (ABR) algorithm for such applications poses three challenges: (i) due to the interaction requirement among the users, the playback buffer has to be kept small to reduce the end-to-end delay; (ii) the algorithm needs to decide what is the bitrate to receive and what is the set of bitrates to send; (iii) the delay and quality requirements between each pair of users may differ, for instance, depending on whether the pair is interacting directly with each other. To address these challenges, we first develop a quality of experience (QoE) model for multi-party live streaming applications. Based on this model, we design MultiLive, an adaptive bitrate control algorithm for the multi-party scenario. MultiLive models the many-to-many ABR selection problem as a non-linear programming problem. Solving the non-linear programming equation yields the target bitrate for each pair of sender-receiver. To alleviate system errors during the modeling and measurement process, we update the target bitrate through the buffer feedback adjustment. To address the throughput limitation of the uplink, we cluster the ideal streams into a few groups, and aggregate these streams through scalable video coding for transmissions. We conduct extensive trace-driven simulations to evaluate the algorithm. The experimental results show that MultiLive outperforms the fixed bitrate algorithm, with 2-5× improvement in average QoE. Furthermore, the end-to-end delay is reduced to around 100 ms, much lower than the 400 ms threshold recommended for video conferencing.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155257"}, {"primary_key": "2634632", "vector": [], "sparse_vector": [], "title": "Intelligent Video Caching at Network Edge: A Multi-Agent Deep Reinforcement Learning Approach.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Lifeng Sun"], "summary": "Today's explosively growing Internet video traffics and viewers' ever-increasing quality of experience (QoE) demands for video streaming bring tremendous pressures to the backbone network. As a new network paradigm, mobile edge caching provides a promising alternative by pushing video content closer at the network edge rather than the remote CDN servers so as to reduce both content access latency and redundant network traffic. However, our large-scale trace analysis shows that different from CDN based caching, edge caching environment is much more complicated with massively dynamic and diverse request patterns, which renders that existing rule-based and model-based caching solutions may not well fit such complicated edge environments. Moreover, although cooperative caching has been proposed to better afford limited storage on each individual edge server, our trace analysis also shows that the request similarity among neighboring edges can be highly dynamic and diverse, which is drastically different from CDN based caching environment, and can easily compromise the benefits from traditional cooperative caching mostly designed based on CDN environment. In this paper, we propose MacoCache, an intelligent edge caching framework that is carefully designed to afford the massively diversified and distributed caching environment to minimize both content access latency and traffic cost. Specifically, MacoCache leverages a multi-agent deep reinforcement learning (MADRL) based solution, where each edge is able to adaptively learn its own best policy in conjunction with other edges for intelligent caching. The real trace-driven evaluation further demonstrates that MacoCache is able to reduce an average of 21% latency and 26% cost compared with the state-of-the-art caching solution.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155373"}, {"primary_key": "2634636", "vector": [], "sparse_vector": [], "title": "Multicast Traffic Engineering with Segment Trees in Software-Defined Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous research on Segment Routing (SR) mostly focused on unicast, whereas online SDN multicast with segment trees supporting IETF dynamic group membership has not been explored. Compared with unicast SR, online SDN multicast with segment trees is more challenging since finding an appropriate size, shape, and location for each segment tree is crucial to deploy it in more multicast trees. In this paper, we explore Multi-tree Multicast Segment Routing (MMSR) to jointly minimize the bandwidth consumption and forwarding rule updates over time by leveraging segment trees. We prove MMSR is NP-hard and design an online competitive algorithm, named Segment Tree Routing and Update Scheduling (STRUS) to achieve the tightest bound. STRUS includes Segment Tree Merging and Segment Tree Pruning to merge smaller overlapping subtrees into segment trees, and then tailor them to serve more multicast trees. We design Stability Indicator and Reusage Indicator to carefully construct segment trees at the backbone of multicast trees and reroute multicast trees to span more segment trees. Simulation and implementation on real SDNs with YouTube traffic manifest that STRUS outperforms state-of-the-art algorithms regarding the total cost and TCAM usage. Moreover, STRUS is practical for SDN since its running time is about 1 second, even for massive networks with thousands of nodes.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155264"}, {"primary_key": "2634637", "vector": [], "sparse_vector": [], "title": "Online Precoding Design for Downlink MIMO Wireless Network Virtualization with Imperfect CSI.", "authors": ["Jun<PERSON> Wang", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider online downlink precoding design for multiple-input multiple-output (MIMO) wireless network virtualization (WNV) in a fading environment with imperfect channel state information (CSI). In our WNV framework, a base station owned by an infrastructure provider (InP) is shared by several service providers (SPs) that are oblivious to each other. The SPs design their virtual MIMO transmission demands to serve their own users, while the InP designs the actual downlink precoding to meet the service demands from the SPs. Therefore, the impact of imperfect CSI is two-fold, on both the InP and the SPs. We aim to minimize the long-term time-averaged expected precoding deviation, considering both long-term and short-term transmit power limits. We propose a new online MIMO WNV algorithm to provide a semi-closed-form precoding solution based only on the current imperfect CSI. We derive a performance bound for our proposed algorithm and show that it is within an O(δ) gap from the optimum over any given time horizon, where δ is a normalized measure of CSI inaccuracy. Simulation results with two popular precoding techniques validate the performance of our proposed algorithm under typical urban micro-cell Long-Term Evolution network settings.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155347"}, {"primary_key": "2634639", "vector": [], "sparse_vector": [], "title": "Machine Learning-based Spoofing Attack Detection in MmWave 60GHz IEEE 802.11ad Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON>"], "summary": "Spoofing attacks pose a serious threat to wireless communications. Exploiting physical-layer features to counter spoofing attacks is a promising solution. Although various physical-layer spoofing attack detection (PL-SAD) techniques have been proposed for conventional 802.11 networks in the sub-6GHz band, the study of PL-SAD for 802.11ad networks in 5G millimeter wave (mmWave) 60GHz band is largely open. In this paper, we propose a unique physical layer feature in IEEE 802.11ad networks, i.e., the signal-to-noise-ratio (SNR) trace obtained at the receiver in the sector level sweep (SLS) process, to achieve efficient PL-SAD. The SNR trace is readily extractable from the off-the-shelf device, and it is dependent on both transmitter location and intrinsic hardware impairment. Therefore, it can be used to achieve an efficient detection no matter the attacker is co-located with the legitimate transmitter or not. The detection problem is formulated as a machine learning classification problem. To tackle the small sample learning and fast model construction challenges, we propose a novel neural network framework consisting of a backpropation network, a forward propagation network, and generative adversarial networks (GANs). It can tackle small sample learning and allow for quick model construction. We conduct experiments using off-the-shelf 802.11ad devices, Talon AD7200s and MG360, to evaluate the performance of the proposed PL-SAD scheme. Experimental results confirm the effectiveness of the proposed PL-SAD scheme, and the detection accuracy can reach 98% using small sample sizes under different scenarios.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155382"}, {"primary_key": "2634640", "vector": [], "sparse_vector": [], "title": "Optimizing Federated Learning on Non-IID Data with Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The widespread deployment of machine learning applications in ubiquitous environments has sparked interests in exploiting the vast amount of data stored on mobile devices. To preserve data privacy, Federated Learning has been proposed to learn a shared model by performing distributed training locally on participating devices and aggregating the local models into a global one. However, due to the limited network connectivity of mobile devices, it is not practical for federated learning to perform model updates and aggregation on all participating devices in parallel. Besides, data samples across all devices are usually not independent and identically distributed (IID), posing additional challenges to the convergence and speed of federated learning. In this paper, we propose Favor, an experience-driven control framework that intelligently chooses the client devices to participate in each round of federated learning to counterbalance the bias introduced by non-IID data and to speed up convergence. Through both empirical and mathematical analysis, we observe an implicit connection between the distribution of training data on a device and the model weights trained based on those data, which enables us to profile the data distribution on that device based on its uploaded model weights. We then propose a mechanism based on deep Q-learning that learns to select a subset of devices in each communication round to maximize a reward that encourages the increase of validation accuracy and penalizes the use of more communication rounds. With extensive experiments performed in PyTorch, we show that the number of communication rounds required in federated learning can be reduced by up to 49% on the MNIST dataset, 23% on FashionMNIST, and 42% on CIFAR-10, as compared to the Federated Averaging algorithm.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155494"}, {"primary_key": "2634641", "vector": [], "sparse_vector": [], "title": "Online Concurrent Transmissions at LoRa Gateway.", "authors": ["<PERSON><PERSON>", "Linghe Kong", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Long Range (LoRa) communication, thanks to its wide network coverage and low energy operation, has attracted extensive attentions from both academia and industry. However, existing LoRa-based Wide Area Network (LoRaWAN) suffers from severe inter-network interference, due to the following two reasons. First, the densely-deployed LoRa ends usually share the same network configurations, such as spreading factor (SF), bandwidth (BW) and carrier frequency (CF), causing interference when operating in the vicinity. Second, LoRa is tailored for low-power devices, which excludes LoRaWAN from using the listen-before-talk (LBT) mechanisms commonly used in wireless communication technologies, such as WiFi and ZigBee -LoRaWAN has to use the duty-cycled medium access policy and thus being incapable of channel sensing or collision avoidance. To mitigate the inter-network interference, we propose a novel solution achieving the online concurrent transmissions at LoRa gateway, called OCT, which recovers collided packets at the gateway and thus improves LoRaWAN's throughput. Moreover, OCT achieves the online concurrent transmission using only LoRa's (de)modulation information, thus can be easily deployed at LoRa gateway. We have implemented and evaluated OCT on USRP platform and commodity LoRa ends, showing OCT achieves: (i) >90% packet reception rate (PRR), (ii) 3 × 10 -3 bit error rate (BER), (iii) 2x and 3x throughput in the scenarios of two- and three- packet collisions respectively, and (iv) reducing 67% latency compared with state-of-the-art.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155433"}, {"primary_key": "2634642", "vector": [], "sparse_vector": [], "title": "Geryon: Accelerating Distributed CNN Training by Network-Level Flow Scheduling.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Increasingly rich data sets and complicated models make distributed machine learning more and more important. However, the cost of extensive and frequent parameter synchronizations can easily diminish the benefits of distributed training across multiple machines. In this paper, we present Geryon, a network-level flow scheduling scheme to accelerate distributed Convolutional Neural Network (CNN) training. Geryon leverages multiple flows with different priorities to transfer parameters of different urgency levels, which can naturally coordinate multiple parameter servers and prioritize the urgent parameter transfers in the entire network fabric. Geryon requires no modification in CNN models and does not affect the training accuracy. Based on the experimental results of four representative CNN models on a testbed of 8 GPU servers, Geryon achieves up to 95.7% scaling efficiency even with 10GbE bandwidth. In contrast, for most models, the scaling efficiency of vanilla TensorFlow is no more than 37% and that of TensorFlow with parameter partition and slicing is around 80%. In terms of training throughput, Geryon enhanced with parameter partition and slicing achieves up to 4.37x speedup, where the flow scheduling algorithm itself achieves up to 1.2x speedup over parameter partition and slicing.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155282"}, {"primary_key": "2634643", "vector": [], "sparse_vector": [], "title": "Towards Pattern-aware Privacy-preserving Real-time Data Collection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Although time-series data collected from users can be utilized to provide services for various applications, they could reveal sensitive information about users. Recently, local differential privacy (LDP) has emerged as the state-of-art approach to protect data privacy by perturbing data locally before outsourcing. However, existing works based on LDP perturb each data point separately without considering the correlations between consecutive data points in time-series. Thus, the important patterns of each time-series might be distorted by existing LDP-based approaches, leading to severe degradation of data utility. In this paper, we focus on real-time data collection under a honest-but-curious server, and propose a novel pattern-aware privacy-preserving approach, called PatternLDP, to protect data privacy while the pattern of time-series can still be preserved. To this end, instead of providing the same level of privacy protection at each data point, each user only samples remarkable points in time-series and adaptively perturbs them according to their impacts on local patterns. In particular, we propose a pattern-aware sampling method based on Piecewise Linear Approximation (PLA) to determine whether to sample and perturb current data point. To reduce the utility loss caused by pattern change after perturbation, we propose an importance-aware randomization mechanism to adaptively perturb sampled data locally while achieving better trade-off between privacy and utility. A novel metric-based w-event privacy is introduced to measure the privacy protection degree for pattern-rich time-series. We prove that PatternLDP can provide the above privacy guarantee, and extensive experiments on real-world datasets demonstrate that PatternLDP outperforms existing mechanisms and can effectively preserve the important patterns.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155290"}, {"primary_key": "2634644", "vector": [], "sparse_vector": [], "title": "Search Me in the Dark: Privacy-preserving Boolean Range Query over Encrypted Spatial Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the increasing popularity of geo-positioning technologies and mobile Internet, spatial keyword data services have attracted growing interest from both the industrial and academic communities in recent years. Meanwhile, a massive amount of data is increasingly being outsourced to cloud in the encrypted form for enjoying the advantages of cloud computing while without compromising data privacy. Most existing works primarily focus on the privacy-preserving schemes for either spatial or keyword queries, and they cannot be directly applied to solve the spatial keyword query problem over encrypted data. In this paper, we study the challenging problem of Privacy-preserving Boolean Range Query (PBRQ) over encrypted spatial databases. In particular, we propose two novel PBRQ schemes. Firstly, we present a scheme with linear search complexity based on the space-filling curve code and Symmetric-key Hidden Vector Encryption (SHVE). Then, we use tree structures to achieve faster-than-linear search complexity. Thorough security analysis shows that data security and query privacy can be guaranteed during the query process. Experimental results using real-world datasets show that the proposed schemes are efficient and feasible for practical applications, which is at least ×70 faster than existing techniques in the literature.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155505"}, {"primary_key": "2634647", "vector": [], "sparse_vector": [], "title": "Dynamic Speed Warping: Similarity-Based One-shot Learning for Device-free Gesture Signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Qing Gu"], "summary": "In this paper, we propose a Dynamic Speed Warping (DSW) algorithm to enable one-shot learning for device-free gesture signals performed by different users. The design of DSW is based on the observation that the gesture type is determined by the trajectory of hand components rather than the movement speed. By dynamically scaling the speed distribution and tracking the movement distance along the trajectory, DSW can effectively match gesture signals from different domains that have a ten-fold difference in speeds. Our experimental results show that DSW can achieve a recognition accuracy of 97% for gestures performed by unknown users, while only use one training sample of each gesture type from four training users.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155491"}, {"primary_key": "2634648", "vector": [], "sparse_vector": [], "title": "Push the Limit of Acoustic Gesture Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "Yuan<PERSON> Zheng"], "summary": "With the flourish of the smart devices and their applications, controlling devices using gestures has attracted increasing attention for ubiquitous sensing and interaction. Recent works use acoustic signals to track hand movement and recognize gestures. However, they suffer from low robustness due to frequency selective fading, interference and insufficient training data. In this work, we propose RobuCIR, a robust contact-free gesture recognition system that can work under different usage scenarios with high accuracy and robustness. RobuCIR adopts frequency-hopping mechanism to mitigate frequency selective fading and avoid signal interference. To further increase system robustness, we investigate a series of data augmentation techniques based on a small volume of collected data to emulate different usage scenarios. The augmented data is used to effectively train neural network models and cope with various influential factors (e.g., gesture speed, distance to transceiver, etc.). Our experiment results show that RobuCIR can recognize 15 gestures and outperform state-of-the-art works in terms of accuracy and robustness.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155402"}, {"primary_key": "2634649", "vector": [], "sparse_vector": [], "title": "Rldish: Edge-Assisted QoE Optimization of HTTP Live Streaming with Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent years have seen a rapidly increasing traffic demand for HTTP-based high-quality live video streaming. The surging traffic demand, as well as the real-time property of live videos, make it challenging for content delivery networks (CDNs) to guarantee the Quality-of-Experiences (QoE) of viewers. The initial video segment (IVS) of live streaming plays an important role in the QoE of live viewers, particularly when users require fast join time and smooth view experience. State-of-the-art research on this regard estimates network throughput for each viewer and thus may incur a large overhead that offsets the benefit. To tackle the problem, we propose Rldish, a scheme deployed at the edge CDN server, to dynamically select a suitable IVS for new live viewers based on Reinforcement Learning (RL). Rldish is transparent to both the client and the streaming server. It collects the real-time QoE observations from the edge without any client-side assistance, then uses these QoE observations as real-time rewards in RL. We deploy Rldish as a virtualized network function (VNF) in a real HTTP cache server, and evaluate its performance using streaming servers distributed over the world. Our experiments show that <PERSON>ldish improves the state- of-the-art IVS selection scheme w.r.t. the average QoE of live viewers by up to 22%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155467"}, {"primary_key": "2634653", "vector": [], "sparse_vector": [], "title": "User Preference Based Energy-Aware Mobile AR System with Edge Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON> (<PERSON>) <PERSON><PERSON>"], "summary": "The advancement in deep learning and edge computing has enabled intelligent mobile augmented reality (MAR) on resource limited mobile devices. However, today very few deep learning based MAR applications are applied in mobile devices because they are significantly energy-guzzling. In this paper, we design a user preference based energy-aware edge-based MAR system that enables MAR clients to dynamically change their configuration parameters, such as CPU frequency and computation model size, based on their user preferences, camera sampling rates, and available radio resources at the edge server. Our proposed dynamic MAR configuration adaptations can minimize the per frame energy consumption of multiple MAR clients without degrading their preferred MAR performance metrics, such as service latency and detection accuracy. To thoroughly analyze the interactions among MAR configuration parameters, user preferences, camera sampling rate, and per frame energy consumption, we propose, to the best of our knowledge, the first comprehensive analytical energy model for MAR clients. Based on the proposed analytical model, we develop a LEAF optimization algorithm to guide the MAR configuration adaptation and server radio resource allocation. Extensive evaluations are conducted to validate the performance of the proposed analytical model and LEAF algorithm.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155517"}, {"primary_key": "2634654", "vector": [], "sparse_vector": [], "title": "SurveilEdge: Real-time Video Query based on Collaborative Cloud-Edge Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The real-time query of massive surveillance video data plays a fundamental role in various smart urban applications such as public safety and intelligent transportation. Traditional cloud-based approaches are not applicable because of high transmission latency and prohibitive bandwidth cost, while edge devices are often incapable of executing complex vision algorithms with low latency and high accuracy due to restricted resources. Given the infeasibility of both cloud-only and edge-only solutions, we present SurveilEdge, a collaborative cloud-edge system for real-time queries of large-scale surveillance video streams. Specifically, we design a convolutional neural network (CNN) training scheme to reduce the training time with high accuracy, and an intelligent task allocator to balance the load among different computing nodes and to achieve the latency-accuracy tradeoff for real-time queries. We implement SurveilEdge on a prototype 1 with multiple edge devices and a public Cloud, and conduct extensive experiments using real-world surveillance video datasets. Evaluation results demonstrate that SurveilEdge manages to achieve up to 7× less bandwidth cost and 5.4× faster query response time than the cloud-only solution; and can improve query accuracy by up to 43.9% and achieve 15.8× speedup respectively, in comparison with edge-only approaches.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155284"}, {"primary_key": "2634655", "vector": [], "sparse_vector": [], "title": "SeVI: Boosting Secure Voice Interactions with Smart Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Voice interaction, as an emerging human-computer interaction method, has gained great popularity, especially on smart devices. However, due to the open nature of voice signals, voice interaction may cause privacy leakage. In this paper, we propose a novel scheme, called SeVI, to protect voice interaction from being deliberately or unintentionally eavesdropped. SeVI actively generates jamming noise of superior characteristics, while a user is performing voice interaction with his/her device, so that attackers cannot obtain the voice contents of the user. Mean-while, the device leverages the prior knowledge of the generated noise to adaptively cancel received noise, even when the device usage environment is changing due to movement, so that the user voice interactions are unaffected. SeVI relies on only normal microphone and speakers and can be implemented as light-weight software. We have implemented SeVI on a commercial off-the- shelf (COTS) smartphone and conducted extensive real-world experiments. The results demonstrate that SeVI can defend both online eavesdropping attacks and offline digital signal processing (DSP) analysis attacks.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155220"}, {"primary_key": "2634658", "vector": [], "sparse_vector": [], "title": "Informative Path Planning for Mobile Sensing with Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large-scale spatial data such as air quality, thermal conditions and location signatures play a vital role in a variety of applications. Collecting such data manually can be tedious and labour intensive. With the advancement of robotic technologies, it is feasible to automate such tasks using mobile robots with sensing and navigation capabilities. However, due to limited battery lifetime and scarcity of charging stations, it is important to plan paths for the robots that maximize the utility of data collection, also known as the informative path planning (IPP) problem. In this paper, we propose a novel IPP algorithm using reinforcement learning (RL). A constrained exploration and exploitation strategy is designed to address the unique challenges of IPP, and is shown to have fast convergence and better optimality than a classical reinforcement learning approach. Extensive experiments using real-world measurement data demonstrate that the proposed algorithm outperforms state-of-the-art algorithms in most test cases. Interestingly, unlike existing solutions that have to be re-executed when any input parameter changes, our RL-based solution allows a degree of transferability across different problem instances.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155528"}, {"primary_key": "2634663", "vector": [], "sparse_vector": [], "title": "mmTrack: Passive Multi-Person Localization Using Commodity Millimeter Wave Radio.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Passive human localization and tracking using RF signals have been studied for over a decade. Most of the existing solutions, however, can only track a single moving subject due to the coarse multipath resolvability limited by bandwidth and antenna number. In this paper, we break down the limitations by leveraging the emerging 60GHz millimeter-wave radios. We present mmTrack, the first system that passively localizes and tracks multiple users simultaneously using a single commodity 60GHz radio. The design of mmTrack consists of three key components. First, we significantly improve the spatial resolution, limited by the small aperture of the compact 60GHz array, by performing digital beamforming over all receive antennas. Second, we propose a novel multi-target detection approach that tackles the near-far-effect and measurement noise. Finally, we devise a robust clustering technique to accurately recognize multiple targets and estimate the respective locations, from which their individual trajectories are further derived by a continuous tracking algorithm. We implement mmTrack on a commodity 802.11ad device and evaluate it in indoor environments. Our experiments demonstrate that mmTrack detects and counts multiple users precisely with an error ≤ 1 person for 97.8% of the time and achieves a respective median location error of 9.9 cm and 19.7 cm for dynamic and static targets.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155293"}, {"primary_key": "2634665", "vector": [], "sparse_vector": [], "title": "On the Optimal Repair-Scaling Trade-off in Locally Repairable Codes.", "authors": ["<PERSON>", "<PERSON><PERSON>ng Shen", "<PERSON>"], "summary": "How to improve the repair performance of erasure-coded storage is a critical issue for maintaining high reliability of modern large-scale storage systems. Locally repairable codes (LRC) are one popular family of repair-efficient erasure codes that mitigate the repair bandwidth and are deployed in practice. To adapt to the changing demands of access efficiency and fault tolerance, modern storage systems also conduct frequent scaling operations on erasure-coded data. In this paper, we analyze the optimal trade-off between the repair and scaling performance of LRC in clustered storage systems. Specifically, we design placement strategies that operate along the optimal repair-scaling trade-off curve subject to the fault tolerance constraints. We prototype and evaluate our placement strategies on a LAN testbed, and show that they outperform the conventional placement scheme in repair and scaling operations.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155417"}, {"primary_key": "2634671", "vector": [], "sparse_vector": [], "title": "LiteNap: Downclocking LoRa Reception.", "authors": ["Xianjin Xia", "Yuan<PERSON> Zheng", "<PERSON> Gu"], "summary": "This paper presents LiteNap which improves the energy efficiency of LoRa by enabling LoRa nodes to operate in a downclocked `light sleep' mode for packet reception. A fundamental limit that prevents radio downclocking is the Nyquist sampling theorem which demands the clock-rate being at least twice the bandwidth of LoRa chirps. Our study reveals under-sampled LoRa chirps suffer frequency aliasing and cause ambiguity in symbol demodulation. LiteNap addresses the problem by leveraging an empirical observation that the hardware of LoRa radio can cause phase jitters on modulated chirps, which result in frequency leakage in the time domain. The timing information of phase jitters and frequency leakages can serve as physical fingerprints to uniquely identify modulated chirps. We propose a scheme to reliably extract the fingerprints from under-sampled chirps and resolve ambiguities in symbol demodulation. We implement LiteNap on a software defined radio platform and conduct trace-driven evaluation. Experiment results show that LiteNap can downclock LoRa nodes to sub-Nyquist rates for energy savings (e.g., 1/8 of Nyquist rate), without substantially affecting packet reception performance (e.g., >95% packet reception rate).", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155224"}, {"primary_key": "2634672", "vector": [], "sparse_vector": [], "title": "Toward Optimal Software-Defined Interdomain Routing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "End-to-end route control spanning a set of networks can provide opportunities to both end users to optimize interdomain control and network service providers to increase business offering. BGP, the de facto interdomain routing protocol, provides no programmable control. Recent proposals for interdomain control, such as MIRO, ARROW and SDX, provide more mechanisms and interfaces, but they are only either point or incremental solutions. In this paper, we provide the first, systematic formulation of the software-defined internetworking (SDI) model, in which a network exposes a programmable interface to allow clients to define the interdomain routes of the network, just as a traditional SDN switch exposes Openflow or another programmable interface to allow clients to define its next hops, extending SDN from intra-domain control to generic interdomain control. Different from intradomain SDN, which allows complete client control, SDI should also maximize network autonomy, such as by allowing a network to maintain the control of its interdomain export policies, to avoid fundamental violations such as valley routing. We define the optimal end-to-end SDI routing problem and conduct rigorous analysis to show that the problem is NP-hard. We develop a blackbox optimization algorithm, which leverages Bayesian optimization theory and important properties of interdomain routing algebra, to sample end-to-end routes sequentially and find a near-optimal policy-compliant end-to-end route with a small number of sample routes. We implement a prototype of our optimization algorithm and validate its effectiveness via extensive experiments using real interdomain network topology. Results show that in an interdomain network with over 60000 ASes and over 320000 AS-level links, in 80% experiment cases, the blackbox optimization algorithm can find a near-optimal policy-compliant end-to-end route by sampling less than 33 routes.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155486"}, {"primary_key": "2634673", "vector": [], "sparse_vector": [], "title": "Modeling the Impact of Network Connectivity on Consensus Security of Proof-of-Work Blockchain.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Blockchain, the technology behind the popular Bitcoin, is considered a \"security by design\" system as it is meant to create security among a group of distrustful parties yet without a central trusted authority. The security of blockchain relies on the premise of honest-majority, namely, the blockchain system is assumed to be secure as long as the majority of consensus voting power is honest. And in the case of proof-of-work (PoW) blockchain, adversaries cannot control more than 50% of the network's gross computing power. However, this 50% threshold is based on the analysis of computing power only, with implicit and idealistic assumptions on the network and node behavior. Recent researches have alluded that factors such as network connectivity, presence of blockchain forks, and mining strategy could undermine the consensus security assured by the honest-majority, but neither concrete analysis nor quantitative evaluation is provided. In this paper we fill the gap by proposing an analytical model to assess the impact of network connectivity on the consensus security of PoW blockchain under different adversary models. We apply our analytical model to two adversarial scenarios: 1) honest-but-potentially-colluding, 2) selfish mining. For each scenario, we quantify the communication capability of nodes involved in a fork race and estimate the adversary's mining revenue and its impact on security properties of the consensus protocol. Simulation results validated our analysis. Our modeling and analysis provide a paradigm for assessing the security impact of various factors in a distributed consensus system.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155451"}, {"primary_key": "2634674", "vector": [], "sparse_vector": [], "title": "Universal Online Sketch for Tracking Heavy Hitters and Estimating Moments of Data Streams.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Traffic measurement is key to many network management tasks such as performance monitoring and cyber-security. Its aim is to inspect the packet stream passing through a network device, classify them into flows according to the header fields, and obtain statistics about the flows. For processing big streaming data in size-limited SRAM of line cards, many space-sublinear algorithms have been proposed, such as CountMin and CountSketch. However, most of them are designed for specific measurement tasks. Implementing multiple independent sketches places burden for online operations of a network device. It is highly desired to design a universal sketch that not only tracks individual large flows (called heavy hitters) but also reports overall traffic distribution statistics (called moments). The prior work UnivMon successfully tackled this ambitious quest. However, it incurs large and variable per-packet processing overhead, which may result in a significant throughput bottleneck in high-rate packet streaming, given that each packet requires 65 hashes and 64 memory accesses on average and many times of that in the worst case. To address this performance issue, we need to fundamentally redesign the solution architecture from hierarchical sampling to new progressive sampling and from CountSketch to new ActiveCM+, which ensure that per-packet overhead is a small constant (4 hash and 4 memory accesses) in the worst case, making it much more suitable for online operations, especially for pipeline implementation. The new design also makes effort to reduce memory footprint or equivalently improve measurement accuracy under the same memory. Our experiments show that our solution incurs just one sixteenth per-packet overhead of UnivMon, while improving measurement accuracy by three times under the same memory.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155454"}, {"primary_key": "2634676", "vector": [], "sparse_vector": [], "title": "A Fast Hybrid Data Sharing Framework for Hierarchical Mobile Edge Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiaofeng Shi", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Edge computing satisfies the stringent latency requirements of data access and processing for applications running on edge devices. The data location service is a key function to provide data storage and retrieval to enable these applications. However, it still lacks research of a scalable and low-latency data location service in mobile edge computing. Meanwhile, the existing solutions, such as DNS and DHT, fail to meet the low latency requirement of mobile edge computing. This paper presents a low-latency hybrid data-sharing framework, HDS, in which the data location service is divided into two parts: intra-region and inter-region. In the intra-region part, we design a data sharing protocol called Cuckoo Summary to achieve fast data localization. In the inter-region part, we develop a geographic routing based scheme to achieve efficient data localization with only one overlay hop. The advantages of HDS include short response latency, low implementation overhead, and few false positives. We implement our HDS framework based on a P4 prototype. The experimental results show that, compared to the state-of-the-art solutions, our design achieves 50.21% shorter lookup paths and 92.75% fewer false positives.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155502"}, {"primary_key": "2634679", "vector": [], "sparse_vector": [], "title": "Exploiting Self-Similarity for Under-Determined MIMO Modulation Recognition.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modulation recognition (modrec) is an essential functional component of future wireless networks with critical applications in dynamic spectrum access. While predominantly studied in single-input single-output (SISO) systems, practical modrec for multiple-input multiple-output (MIMO) communications requires more research attention. Existing MIMO modrec impose stringent requirements of fully- or over-determined sensing front-end, i.e. the number of sensor antennas should exceed that at the transmitter. This poses a prohibitive sensor cost even for simple 2x2 MIMO systems and will severely hamper progress in flexible spectrum access with advanced higher-order MIMO.We design a MIMO modrec framework that enables efficient and cost-effective modulation classification for under-determined settings characterized by fewer sensor antennas than those used for transmission. Our key idea is to exploit the inherent multi-scale self-similarity of MIMO modulation IQ constellations, which persists in under-determined settings. Our framework called SYMMeTRy (Self-similarit Y for MIMO ModulaTion Recognition) designs domain-aware classification features with high discriminative potential by summarizing regularities of symbol co-location in the MIMO constellation. To this end, we summarize the fractal geometry of observed samples to extract discriminative features for supervised MIMO modrec. We evaluate SYMMeTRy in a realistic simulation and in a small-scale MIMO testbed. We demonstrate that it maintains high and consistent performance across various noise regimes, channel fading conditions and with increasing MIMO transmitter complexity. Our efforts highlight SYMMeTRy's high potential to enable efficient and practical MIMO modrec.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155247"}, {"primary_key": "2634680", "vector": [], "sparse_vector": [], "title": "Edge Assisted Mobile Semantic Visual SLAM.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Danyang Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Localization and navigation play a key role in many location-based services and have attracted numerous research efforts from both academic and industrial community. In recent years, visual SLAM has been prevailing for robots and autonomous driving cars. However, the ever-growing computation resource demanded by SLAM impedes its application to resource-constrained mobile devices. In this paper we present the design, implementation, and evaluation of edgeSLAM, an edge assisted real-time semantic visual SLAM service running on mobile devices. edgeSLAM leverages the state-of-the-art semantic segmentation algorithm to enhance localization and mapping accuracy, and speeds up the computation-intensive SLAM and semantic segmentation algorithms by computation offloading. The key innovations of edgeSLAM include an efficient computation offloading strategy, an opportunistic data sharing mechanism, and an adaptive task scheduling algorithm. We fully implement edgeSLAM on an edge server and different types of mobile devices (2 types of smartphones and a development board). Extensive experiments are conducted under 3 data sets, and the results show that edgeSLAM is able to run on mobile devices at 35fps frame rate and achieves a 5cm localization accuracy, outperforming existing solutions by more than 15%. We also demonstrate the usability of edgeSLAM through 2 case studies of pedestrian localization and robot navigation. To the best of our knowledge, edgeSLAM is the first real-time semantic visual SLAM for mobile devices.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155438"}, {"primary_key": "2634681", "vector": [], "sparse_vector": [], "title": "PDL: A Data Layout towards Fast Failure Recovery for Erasure-coded Distributed Storage Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Min Lv", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Erasure coding becomes increasingly popular in distributed storage systems (DSSes) for providing high reliability with low storage overhead. However, traditional random data placement causes massive cross-rack traffic and severely unbalanced load during failure recovery, degrading the recovery performance significantly. In addition, various erasure coding policies coexisting in a DSS exacerbates the above problem. In this paper, we propose PDL, a PBD-based Data Layout, to optimize failure recovery performance in DSSes. PDL is constructed based on Pairwise Balanced Design, a combinatorial design scheme with uniform mathematical properties, and thus presents a uniform data layout. Then we propose rPDL, a failure recovery scheme based on PDL. rPDL reduces cross-rack traffic effectively and provides nearly balanced cross-rack traffic distribution by uniformly choosing replacement nodes and retrieving determined available blocks to recover the lost blocks. We implemented PDL and rPDL in Hadoop 3.1.1. Compared with existing data layout of HDFS, experimental results show that rPDL reduces degraded read latency by an average of 62.83%, delivers 6.27× data recovery throughput, and provides evidently better support for front-end applications.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155350"}, {"primary_key": "2634682", "vector": [], "sparse_vector": [], "title": "S-MAC: Achieving High Scalability via Adaptive Scheduling in LPWAN.", "authors": ["Zhu<PERSON> Xu", "Junzhou Luo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Low Power Wide Area Networks (LPWAN) are an emerging well-adopted platform to connect the Internet-of-Things. With the growing demands for LPWAN in IoT, the number of supported end-devices cannot meet the IoT deployment requirements. The core problem is the transmission collisions when large-scale end-devices transmit concurrently. The previous research mainly includes transmission scheduling strategies, collision detection and avoidance mechanism. The use of these existing approaches to address the above limitations in LPWAN may introduce excessive communication overhead, end-devices cost, power consumption, or hardware complexity. In this paper, we present S-MAC, an adaptive MAC-layer scheduler for LPWAN. The key innovation of S-MAC is to take advantage of the periodic transmission characteristics of LPWAN applications and also the collision behaviour features of LoRa PHY-layer to enhance the scalability. Technically, S-MAC is capable of adaptively perceiving clock drift of end-devices, adaptively identifying the join and exit of end-devices, and adaptively performing the scheduling strategy dynamically. Meanwhile, it is compatible with native LoRaWAN, and adaptable to existing Class A, B and C devices. Extensive implementations and evaluations on commodity devices show that S-MAC increases the number of connected end-devices by 4.06× and improves network throughput by 4.01× with PRR requirement of > 95%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155474"}, {"primary_key": "2634683", "vector": [], "sparse_vector": [], "title": "An Adaptive and Fast Convergent Approach to Differentially Private Deep Learning.", "authors": ["<PERSON><PERSON><PERSON>", "Shu<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With the advent of the era of big data, deep learning has become a prevalent building block in a variety of machine learning or data mining tasks, such as signal processing, network modeling and traffic analysis, to name a few. The massive user data crowdsourced plays a crucial role in the success of deep learning models. However, it has been shown that user data may be inferred from trained neural models and thereby exposed to potential adversaries, which raises information security and privacy concerns. To address this issue, recent studies leverage the technique of differential privacy to design private-preserving deep learning algorithms. Albeit successful at privacy protection, differential privacy degrades the performance of neural models. In this paper, we develop ADADP, an adaptive and fast convergent learning algorithm with a provable privacy guarantee. ADADP significantly reduces the privacy cost by improving the convergence speed with an adaptive learning rate and mitigates the negative effect of differential privacy upon the model accuracy by introducing adaptive noise. The performance of ADADP is evaluated on real-world datasets. Experiment results show that it outperforms state-of-the-art differentially private approaches in terms of both privacy cost and model accuracy.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155359"}, {"primary_key": "2634685", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms for the Team Orienteering Problem.", "authors": ["<PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Liang", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we study a team orienteering problem, which is to find service paths for multiple vehicles in a network such that the profit sum of serving the nodes in the paths is maximized, subject to the cost budget of each vehicle. This problem has many potential applications in IoT and smart cities, such as dispatching energy-constrained mobile chargers to charge as many energy-critical sensors as possible to prolong the network lifetime. In this paper, we first formulate the team orienteering problem, where different vehicles are different types, each node can be served by multiple vehicles, and the profit of serving the node is a submodular function of the number of vehicles serving it. We then propose a novel (1 - (1/e) 1/2+ε )approximation algorithm for the problem, where c is a given constant with 0 ≤ ε ≤ 1 and ε is the base of the natural logarithm. In particular, the approximation ratio is no less than 0.32 when ε = 0.5. In addition, for a special team orienteering problem with the same type of vehicles and the profits of serving a node once and multiple times being the same, we devise an improved approximation algorithm. Finally, we evaluate the proposed algorithms with simulation experiments, and the results of which are very promising. Precisely, the profit sums delivered by the proposed algorithms are approximately 12.5% to 17.5% higher than those by existing algorithms.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155343"}, {"primary_key": "2634687", "vector": [], "sparse_vector": [], "title": "Collaborate or Separate? Distributed Service Caching in Mobile Edge Clouds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Liang", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the development of 5G technology, mobile edge computing is emerging as an enabling technique to promote Quality of Service (QoS) of network services. In particular, the response latency of network services can be significantly reduced by deploying cloudlets at 5G base stations in mobile edge clouds. Network service providers that usually deploy their services in remote clouds now shift their services from the remote clouds to the network edge in the proximity of users. However, the permanent placement of their services into edge clouds may not be economic, since computing and bandwidth resources in edge clouds are limited and relatively expensive. A smart way is to cache the services that are frequently requested by mobile users in edge clouds. In this paper, we study the problem of service caching in mobile edge network under a mobile service market with multiple network service providers completing for both computation and bandwidth resources of the edge cloud. We propose an Integer Linear Program (ILP) and a randomized rounding algorithm, for the problem without resource sharing among the network service providers. We also devise a distributed and stable game-theoretical mechanism for the problem with resource sharing among the network service providers, with the objective to minimize the social cost of all network service providers, by introducing a novel cost sharing model and a coalition formation game. We analyze the performance of the mechanism by showing a good guaranteed gap between the solution obtained and the optimal one, i.e., Strong Price of Anarchy (SPoA). We finally evaluate the performance of our algorithms by extensive simulations, and the obtained results show that the social cost of all players can be reduced significantly via allowing cooperation among network service providers in service caching.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155365"}, {"primary_key": "2634689", "vector": [], "sparse_vector": [], "title": "Injection Time Planning: Making CQF Practical in Time-Sensitive Networking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Zhigang Sun"], "summary": "Time-Aware Shaper (TAS) is a core mechanism to guarantee the deterministic transmission for periodic time-sensitive flows in Time-Sensitive Networking (TSN). The generic TAS requires complex configurations for the Gate Control List (GCL) attached to each queue in a switch. To simplify the design of a TSN switch, a Ping-Pong queue-based model named Cyclic Queuing and Forwarding (CQF) was proposed in IEEE 802.1 Qch by assigning fixed configurations to TAS. However, IEEE 802.1 Qch only defines the queue model and workflow of CQF. A global planning mechanism which maps the time-sensitive flows onto the underlying resources both temporally and spatially is urgently needed to make CQF practical.In this paper, we propose an Injection Time Planning (ITP) mechanism to optimize the network throughput of time-sensitive flows based on the observation that the start time when the packets are injected into the network has an important influence on the utilization of CQF queue resources. ITP provides a global temporal and spatial resource abstraction to make the implementation details transparent to algorithm designers. Based on our ITP mechanism, a novel heuristic algorithm named Tabu-ITP with domain-specific optimizing strategies is designed and evaluated under three typical network topologies in industrial control scenarios. Compared with the Naive algorithm without using ITP mechanism, experimental results demonstrate that Tabu-ITP improves the mapped flow number by 10x and the resource utilization by 65%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155434"}, {"primary_key": "2634690", "vector": [], "sparse_vector": [], "title": "MagPrint: Deep Learning Based User Fingerprinting Using Electromagnetic Signals.", "authors": ["<PERSON><PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Pan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Linghe Kong", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Understanding the nature of user-device interactions (e.g., who is using the device and what he/she is doing with it) is critical to many applications including time management, user profiles, and privacy protection. However, in scenarios where mobile devices are shared among family members or multiple employees in a company, conventional account-based statistics are not meaningful. This poses an even bigger problem when dealing with sensitive data. Moreover, fingerprint readers and front-facing cameras were not designed to continuously identify users. In this study, we developed MagPrint, a novel approach to fingerprint users based on unique patterns in the electromagnetic (EM) signals associated with the specific use patterns of users. Initial experiments showed that time-varying EM patterns are unique to individual users. They are also temporally and spatially consistent, which makes them suitable for fingerprinting. MagPrint has a number of advantages over existing schemes: i) Non-intrusive fingerprinting, ii) implementation using a small and easy-to-deploy device, and iii) high accuracy thanks to the proposed classification algorithm. In experiments involving 30 users, MagPrint achieves 94.3% accuracy in classifying users from these traces, which represents an 10.9% improvement over the state-of-the-art classification method.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155534"}, {"primary_key": "2634691", "vector": [], "sparse_vector": [], "title": "RF-Ear: Contactless Multi-device Vibration Sensing and Identification Using COTS RFID.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Mechanical vibration sensing/monitoring plays a critical role in today's industrial Internet of Things (IoT) applications. Existing solutions usually involve directly attaching sensors to the target objects, which is invasive and may affect the operations of delicate devices. Non-invasive approaches such as video and laser methods have drawbacks in that, the former incurs poor performance in low light conditions, while the latter has difficulties to monitor multiple objects simultaneously.In this work, we design RF-Ear, a contactless vibration sensing system using Commercial off-the-shelf (COTS) RFID hardware. RF-Ear could accurately monitor the mechanical vibrations of multiple (up to 8) devices using a single tag: it can clearly tell which object is vibrating at what frequency without attaching tags on any device. RF-Ear can measure the vibration frequency up to 400Hz with a mean error rate of 0.2%. Our evaluation results show that RF-Ear can effectively detect 0.2cm screw loose with 90% accuracy. We further employ each device's unique vibration fingerprint to identify and differentiate devices of exactly the same model. We also show that RF-ear can monitor not just the vibrations but also a large range of mechanical motions. Comprehensive experiments conducted in a real power plant demonstrate the effectiveness of our system with outstanding performance.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155251"}, {"primary_key": "2634692", "vector": [], "sparse_vector": [], "title": "Network Monitoring for SDN Virtual Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Yang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposes V-Sight, a network monitoring framework for software-defined networking (SDN)-based virtual networks. Network virtualization with SDN (SDN-NV) makes it possible to realize programmable virtual networks; so, the technology can be beneficial to cloud services for tenants. However, to the best of our knowledge, although network monitoring is a vital prerequisite for managing and optimizing virtual networks, it has not been investigated in the context of SDN-NV. Thus, virtual networks suffer from non-isolated statistics between virtual networks, high monitoring delays, and excessive control channel consumption for gathering statistics, which critically hinders the benefits of SDN-NV. To solve these problems, V-Sight presents three key mechanisms: 1) statistics virtualization for isolated statistics, 2) transmission disaggregation for reduced transmission delay, and 3) pCollector aggregation for efficient control channel consumption. V-Sight is implemented on top of OpenVirteX, and the evaluation results demonstrate that V-Sight successfully reduces monitoring delay and control channel consumption up to 454 times.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155260"}, {"primary_key": "2634694", "vector": [], "sparse_vector": [], "title": "MU-ID: Multi-user Identification Through Gaits Using Millimeter Wave Radios.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yucheng Xie"], "summary": "Multi-user identification could facilitate various large-scale identity-based services such as access control, automatic surveillance system, and personalized services, etc. Although existing solutions can identify multiple users using cameras, such vision-based approaches usually raise serious privacy concerns and require the presence of line-of-sight. Differently, in this paper, we propose MU-ID, a gait-based multi-user identification system leveraging a single commercial off-the-shelf (COTS) millimeter-wave (mmWave) radar. Particularly, MU-ID takes as input frequency-modulated continuous-wave (FMCW) signals from the radar sensor. Through analyzing the mmWave signals in the range-Doppler domain, MU-ID examines the users' lower limb movements and captures their distinct gait patterns varying in terms of step length, duration, instantaneous lower limb velocity, and inter-lower limb distance, etc. Additionally, an effective spatial-temporal silhouette analysis is proposed to segment each user's walking steps. Then, the system identifies steps using a Convolutional Neural Network (CNN) classifier and further identifies the users in the area of interest. We implement MU-ID with the TI AWR1642BOOST mmWave sensor and conduct extensive experiments involving 10 people. The results show that MU-ID achieves up to 97% single-person identification accuracy, and over 92% identification accuracy for up to four people, while maintaining a low false positive rate.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155471"}, {"primary_key": "2634700", "vector": [], "sparse_vector": [], "title": "RUEGAN: Embracing a Self-Adversarial Agent for Building a Defensible Edge Security Architecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the era of edge computing and Artificial Intelligence (AI), securing billions of edge devices within a network against intelligent attacks is crucial. We propose PUFGAN, an innovative machine learning attack-proof security architecture, by embedding a self-adversarial agent within a device fingerprint- based security primitive, public PUF (PPUF) known for its strong fingerprint-driven cryptography. The self-adversarial agent is implemented using Generative Adversarial Networks (GANs). The agent attempts to self-attack the system based on two GAN variants, vanilla GAN and conditional GAN. By turning the attacking quality through generating realistic secret keys used in the PPUF primitive into system vulnerability, the security architecture is able to monitor its internal vulnerability. If the vulnerability level reaches at a specific value, PUFGAN allows the system to restructure its underlying security primitive via feedback to the PPUF hardware, maintaining security entropy at as high a level as possible. We evaluated PUFGAN on three different machine environments: Google Colab, a desktop PC, and a Raspberry Pi 2, using a real-world PPUF dataset. Extensive experiments demonstrated that even a strong device fingerprint security primitive can become vulnerable, necessitating active restructuring of the current primitive, making the system resilient against extreme attacking environments.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155501"}, {"primary_key": "2634701", "vector": [], "sparse_vector": [], "title": "Scheduling DDoS Cloud Scrubbing in ISP Networks via Randomized Online Auctions.", "authors": ["Wencong You", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While both Internet Service Providers (ISPs) and third-party Security Service Providers (SSPs) offer Distributed Denial-of-Service (DDoS) mitigation services through cloud-based scrubbing centers, it is often beneficial for ISPs to outsource part of the traffic scrubbing to SSPs to achieve less economic cost and better network performance. To explore this potential, we design an online auction mechanism, featured by the challenge of the switching cost of using different winning bids over time. Formulating the social cost minimization as a nonconvex integer program, we firstly relax it and design an online algorithm that breaks it into a series of modified single-shot problems and solves each of them in polynomial time, without requiring knowledge of future inputs; then, we design a randomized rounding algorithm to convert the fractional decisions into integers without violating any constraints; and finally, we design the payment for each bid based on its winning probability. We rigorously prove that our mechanism achieves a parameterized-constant competitive ratio for the long-term social cost, with truthfulness and individual rationality in expectation. We also exhibit its superior practical performance via evaluations driven by real-world data traces.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155493"}, {"primary_key": "2634702", "vector": [], "sparse_vector": [], "title": "Flow Table Security in SDN: Adversarial Reconnaissance and Intelligent Attacks.", "authors": ["<PERSON><PERSON>", "Ting <PERSON>", "<PERSON>", "<PERSON>"], "summary": "The performance-driven design of SDN architectures leaves many security vulnerabilities, a notable one being the communication bottleneck between the controller and the switches. Functioning as a cache between the controller and the switches, the flow table mitigates this bottleneck by caching flow rules received from the controller at each switch, but is very limited in size due to the high cost and power consumption of the underlying storage medium. It thus presents an easy target for attacks. Observing that many existing defenses are based on simplistic attack models, we develop a model of intelligent attacks that exploit specific cache-like behaviors of the flow table to infer its internal configuration and state, and then design attack parameters accordingly. Our evaluations show that such attacks can accurately expose the internal parameters of the target flow table and cause measurable damage with the minimum effort.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155538"}, {"primary_key": "2634704", "vector": [], "sparse_vector": [], "title": "RePiDeM: A Refined POI Demand Modeling based on Multi-Source Data*.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Point-of-Interest (POI) demand modeling in urban regions is critical for building smart cities with various applications, e.g., business location selection and urban planning. However, existing work does not fully utilize human mobility data and ignores the interactive-aware information. In this work, we design a refined POI demand modeling framework, named RePiDeM, to identify region POI demands based on multi-source data, including cellular data, POI data, satellite image, geographic data, etc. Specifically, we introduce a Cellular Data (CD) based visit inference algorithm to estimate the POI visit probability based on human mobility and POI data. Further, to address the data sparsity issue, we design a multi-source attention neural collaborative filtering (MANCF) model to output region POI demands considering various aspect attention. We conduct extensive experiments on real-world data collected in the Chinese city Shenyang, which show that RePiDeM is effective for modeling region POI demands.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155294"}, {"primary_key": "2634705", "vector": [], "sparse_vector": [], "title": "RepBun: Load-Balanced, Shuffle-Free Cluster Caching for Structured Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Cluster caching systems increasingly store structured data objects in the columnar format. However, these systems routinely face the imbalanced load that significantly impairs the I/O performance. Existing load-balancing solutions, while effective for reading unstructured data objects, fall short in handling columnar data. Unlike unstructured data that can only be read through a full-object scan, columnar data supports direct query of specific columns with two distinct access patterns: (1) columns have the heavily skewed popularity, and (2) hot columns are likely accessed together in a query job. Based on these two access patterns, we propose an effective load-balancing solution for structured data. Our solution, which we call RepBun, groups hot columns into a bundle. It then copies multiple replicas of the column bundle and stores them uniformly across servers. We show that RepBun achieves improved load balancing with reduced memory overhead, while avoiding data shuffling between cache servers. We implemented RepBun atop Alluxio, a popular in-memory distributed storage, and evaluate its performance through EC2 deployment against the TPC-H benchmark work-load. Experimental results show that RepBun outperforms the existing load-balancing solutions with significantly shorter read latency and faster query completion.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155409"}, {"primary_key": "2634706", "vector": [], "sparse_vector": [], "title": "ADA: Adaptive Deep Log Anomaly Detector.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ming<PERSON> Lin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Fu"], "summary": "Large private and government networks are often subjected to attacks like data extrusion and service disruption. Existing anomaly detection systems use offline supervised learning and employ experts for labeling. Hence they cannot detect anomalies in real-time. Even though unsupervised algorithms are increasingly used nowadays, they cannot readily adapt to newer threats. Moreover, many such systems also suffer from high cost of storage and require extensive computational resources. In this paper, we propose ADA: Adaptive Deep Log Anomaly Detector, an unsupervised online deep neural network framework that leverages LSTM networks and regularly adapts to newer log patterns to ensure accurate anomaly detection. In ADA, an adaptive model selection strategy is designed to choose pareto-optimal configurations and thereby utilize resources efficiently. Further, a dynamic threshold algorithm is proposed to dictate the optimal threshold based on recently detected events to improve the detection accuracy. We also use the predictions to guide storage of abnormal data and effectively reduce the overall storage cost. We compare ADA with state-of-the-art approaches through leveraging the Los Alamos National Laboratory cyber security dataset and show that ADA accurately detects anomalies with high F1-score ~95% and it is 97 times faster than existing approaches and incurs very low storage cost.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155487"}, {"primary_key": "2634709", "vector": [], "sparse_vector": [], "title": "A Deep Analysis on General Approximate Counters.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Approximate counters play an important role in many computer domains like network measurement, parallel computing and machine learning because they can reduce the required memory cost. With the emergence of new application needs in these domains like flow counting and parallel measuring, simple Morris counters fail to solve them. Therefore, a more general Morris counter is required. However, there has been a lack of complete theoretical research on the statistical properties of this new approximate counter so far.This paper conducts a deep analysis on general Morris counters and derives the minimum upper bound of the variance. To our best knowledge, this is the first work to thoroughly analyze the statistical properties of general Morris counters in theory. Besides, application scenarios are analyzed, showing that conclusions obtained by our research are effective in testing the performance of approximate counters and guiding system architecture design according to accuracy needs.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155412"}, {"primary_key": "2634710", "vector": [], "sparse_vector": [], "title": "An Incentive Mechanism Design for Efficient Edge Learning by Deep Reinforcement Learning Approach.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Emerging technologies and applications have generated large amounts of data at the network edge. Due to bandwidth, storage, and privacy concerns, it is often impractical to move the collected data to the cloud. With the rapid development of edge computing and distributed machine learning (ML), edge-based ML called federated learning has emerged to overcome the shortcomings of cloud-based ML. Existing works mainly focus on designing efficient learning algorithms, few works focus on designing the incentive mechanisms with heterogeneous edge nodes (EN) and uncertainty of network bandwidth. The incentive mechanisms affect various tradeoffs: (i) between computation and communication latency, and thus (ii) between the edge learning time and payment consumption. We fill this gap by designing an incentive mechanism that captures the tradeoff between latency and payment. Due to the network dynamics and privacy protection, we propose a deep reinforcement learning-based (DRL-based) solution that can automatically learn the best pricing strategy. To the best of our knowledge, this is the first work that applies the advances of DRL to design the incentive mechanism for edge learning. We evaluate the performance of the incentive mechanism using trace-driven experiments. The results demonstrate the superiority of our proposed approach as compared with the baselines.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155268"}, {"primary_key": "2634712", "vector": [], "sparse_vector": [], "title": "RF Backscatter-based State Estimation for Micro Aerial Vehicles.", "authors": ["<PERSON><PERSON><PERSON> Zhang", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "he advances in compact and agile micro aerial vehicles (MAVs) have shown great potential in replacing human for labor-intensive or dangerous indoor investigation, such as warehouse management and fire rescue. However, the design of a state estimation system that enables autonomous flight in such dim or smoky environments presents a conundrum: conventional GPS or computer vision based solutions only work in outdoors or well-lighted texture-rich environments. This paper takes the first step to overcome this hurdle by proposing Marvel, a lightweight RF backscatter-based state estimation system for MAVs in indoors. Marvel is nonintrusive to commercial MAVs by attaching backscatter tags to their landing gears without internal hardware modifications, and works in a plug-and-play fashion that does not require any infrastructure deployment, pre-trained signatures, or even without knowing the controller's location. The enabling techniques are a new backscatter-based pose sensing module and a novel backscatter-inertial super-accuracy state estimation algorithm. We demonstrate our design by programming a commercial-off-the-shelf MAV to autonomously fly in different trajectories. The results show that Marvel supports navigation within a range of 50 m or through three brick walls, with an accuracy of 34 cm for localization and 4.99° for orientation estimation, outperforming commercial GPS-based approaches in outdoors.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155463"}, {"primary_key": "2634715", "vector": [], "sparse_vector": [], "title": "De-anonymization of Social Networks: the Power of Collectiveness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The interaction among users in different social networks raises deep concern on user privacy, as it may facilitate the assailants to identify user identities by matching the anonymized networks with a correlated sanitized one. Prior arts regarding such de-anonymization problem can be primarily divided into a seeded case or a seedless one, depending on whether or not there are a subset of pre-identified nodes. The seedless case is much more complicated since the adjacency matrix representation of one-hop user relations delivers limited structural information. To address this issue, we, for the first time, integrate the multi-hop neighborhood relationships, which exhibit more structural commonness between the anonymized and the sanitized networks, into seedless de-anonymization process. Our aim is to sufficiently leverage these multi-hop neighbors of all nodes and minimize the total disagreements of these multi-hop adjacency matrices, which we call collective adjacency disagreements (CADs), between two networks of different sizes. Theoretically, we demonstrate that CAD enlarges the difference between wrongly matched node pairs and correctly matched pairs, whereby two networks can be correctly matched with high probability even when the network density is below logn. Algorithmically, we adopt the conditional gradient descending method on a collective-form objective, which can efficiently find the minimal CADs for networks with broad degree distributions. Experiments on both synthetic and realworld networks return desirable de-anonymization accuracies thanks to the rich structural information manifested by such collectiveness, since most nodes can be correctly matched with their correspondences, especially in sparse networks where merely utilizing adjacency relations might fail to work.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155499"}, {"primary_key": "2634716", "vector": [], "sparse_vector": [], "title": "Link Quality Estimation of Cross-Technology Communication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Research on Cross-technology communication (CTC) has made rapid progress in recent years, but how to estimate the quality of a CTC link remains an open and challenging problem. Through our observation and study, we find that none of the existing approaches can be applied to estimate the link quality of CTC. Built upon the physical-level emulation, transmission over a CTC link is jointly affected by two factors: the emulation error and the channel distortion. We in this paper propose a new link metric called C-LQI and a joint link model that simultaneously takes into account the emulation error and the channel distortion in the process of CTC. We further design a light-weight link estimation approach to estimate C-LQI and in turn the PRR over the CTC link. We implement C-LQI and compare it with two representative link estimation approaches. The results demonstrate that C-LQI reduces the relative error of link estimation respectively by 46% and 53% and saves the communication cost by 90%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155437"}, {"primary_key": "2634718", "vector": [], "sparse_vector": [], "title": "MagView: A Distributed Magnetic Covert Channel via Video Encoding and Decoding.", "authors": ["<PERSON><PERSON><PERSON>", "Xiaoyu Ji", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Air-gapped networks achieve security by using the physical isolation to keep the computers and network from the Internet. However, magnetic covert channels based on CPU utilization have been proposed to help secret data to escape the Faraday-cage and the air-gap. Despite the success of such cover channels, they suffer from the high risk of being detected by the transmitter computer and the challenge of installing malware into such a computer. In this paper, we propose MagView, a distributed magnetic cover channel, where sensitive information is embedded in other data such as video and can be transmitted over the air-gapped internal network. When any computer uses the data such as playing the video, the sensitive information will leak through the magnetic covert channel. The \"separation\" of information embedding and leaking, combined with the fact that the covert channel can be created on any computer, overcomes these limitations. We demonstrate that CPU utilization for video decoding can be effectively controlled by changing the video frame type and reducing the quantization parameter without video quality degradation. We prototype MagView and achieve up to 8.9 bps throughput with BER as low as 0.0057. Experiments under different environment are conducted to show the robustness of MagView. Limitations and possible countermeasures are also discussed.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155386"}, {"primary_key": "2634721", "vector": [], "sparse_vector": [], "title": "Data-driven Distributionally Robust Optimization for Edge Intelligence.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The past few years have witnessed the explosive growth of Internet of Things (IoT) devices. The necessity of real-time edge intelligence for IoT applications demands that decision making must take place right here right now at the network edge, thus dictating that a high percentage of the IoT created data should be stored and analyzed locally. However, the computing resources are constrained and the amount of local data is often very limited at edge nodes. To tackle these challenges, we propose a distributionally robust optimization (DRO)-based edge intelligence framework, which is based on an innovative synergy of cloud knowledge transfer and local learning. More specifically, the knowledge transfer from the cloud learning is in the form of a reference distribution and its associated uncertainty set. Further, based on its local data, the edge device constructs an uncertainty set centered around its empirical distribution. The edge learning problem is then cast as a DRO problem subject to the above two distribution uncertainty sets. Building on this framework, we investigate two problem formulations for DRO-based edge intelligence, where the uncertainty sets are constructed using the Kullback-Le<PERSON> divergence and the <PERSON><PERSON><PERSON> distance, respectively. Numerical results demonstrate the effectiveness of the proposed DRO-based framework.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155440"}, {"primary_key": "2634723", "vector": [], "sparse_vector": [], "title": "Autonomous Unknown-Application Filtering and Labeling for DL-based Traffic Classifier Update.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Feng Ye", "<PERSON><PERSON>"], "summary": "Network traffic classification has been widely studied to fundamentally advance network measurement and management. Machine Learning is one of the effective approaches for network traffic classification. Specifically, Deep Learning (DL) has attracted much attention from the researchers due to its effectiveness even in encrypted network traffic without compromising neither user privacy nor network security. However, most of the existing models are created from closed-world datasets, thus they can only classify those existing classes previously sampled and labeled. In this case, unknown classes cannot be correctly classified. To tackle this issue, an autonomous learning framework is proposed to effectively update DL-based traffic classification models during active operations. The core of the proposed framework consists of a DL-based classifier, a self-learned discriminator, and an autonomous self-labeling model. The discriminator and self-labeling process can generate new dataset during active operations to support classifier update. Evaluation of the proposed framework is performed on an open dataset, i.e., ISCX VPN-nonVPN, and independently collected data packets. The results demonstrate that the proposed autonomous learning framework can filter packets from unknown classes and provide accurate labels. Thus, corresponding DL-based classification models can be updated successfully with the autonomously generated dataset.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155292"}, {"primary_key": "2634724", "vector": [], "sparse_vector": [], "title": "Communication-Efficient Network-Distributed Optimization with Differential-Coded Compressors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Network-distributed optimization has attracted sig-nificant attention in recent years due to its ever-increasing applications. However, the classic decentralized gradient descent (DGD) algorithm is communication-inefficient for large-scale and high-dimensional network-distributed optimization problems. To address this challenge, many compressed DGD-based algorithms have been proposed. However, most of the existing works have high complexity and assume compressors with bounded noise power. To overcome these limitations, in this paper, we propose a new differential-coded compressed DGD (DC-DGD) algorithm. The key features of DC-DGD include: i) DC-DGD works with general SNR-constrained compressors, relaxing the bounded noise power assumption; ii) The differential-coded design entails the same convergence rate as the original DGD algorithm; and iii) DC-DGD has the same low-complexity structure as the original DGD due to a self-noise-reduction effect. Moreover, the above features inspire us to develop a hybrid compression scheme that offers a systematic mechanism to minimize the communication cost. Finally, we conduct extensive experiments to verify the efficacy of the proposed DC-DGD and hybrid compressor.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155432"}, {"primary_key": "2634725", "vector": [], "sparse_vector": [], "title": "Enabling Execution Assurance of Federated Learning at Untrusted Participants.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL), as a privacy-preserving machine learning framework, draws growing attention in both industry and academia. It obtains a jointly accurate model by distributing training tasks into data owners and aggregating their model updates. However, FL faces new security problems, as it losses direct control to training processes. One fundamental demand is to ensure whether participants execute training tasks as intended.In this paper, we propose TrustFL, a practical scheme that leverages Trusted Execution Environments (TEEs) to build assurance of participants' training executions with high confidence. Specifically, we use TEE to randomly check a small fraction of all training processes for tunable levels of assurance, while all computations are executed on the co-located faster yet insecure processor (e.g., GPU) for efficiency. To prevent various cheating behaviors like only processing TEE-requested computations or uploading old results, we devise a commitment-based method with specific data selection. We prototype TrustFL using GPU and SGX and evaluate its performance. The results show that TrustFL achieves one/two orders of magnitude speedups compared with naive training with SGX, when assuring correct training with a confidence level of 99%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155414"}, {"primary_key": "2634726", "vector": [], "sparse_vector": [], "title": "Voiceprint Mimicry Attack Towards Speaker Verification System in Smart Home.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The advancement of voice controllable systems (VC-Ses) has dramatically affected our daily lifestyle and catalyzed the smart home's deployment. Currently, most VCSes exploit automatic speaker verification (ASV) to prevent various voice attacks (e.g., replay attack). In this study, we present VMask, a novel and practical voiceprint mimicry attack that could fool ASV in smart home and inject the malicious voice command disguised as a legitimate user. The key observation behind VMask is that the deep learning models utilized by ASV are vulnerable to the subtle perturbations in the voice input space. To generate these subtle perturbations, VMask leverages the idea of adversarial examples. Then by adding the subtle perturbations to the recordings from an arbitrary speaker, VMask can mislead the ASV into classifying the crafted speech samples, which mirror the former speaker for human, as the targeted victim. Moreover, psychoacoustic masking is employed to manipulate the adversarial perturbations under human perception threshold, thus making victim unaware of ongoing attacks. We validate the effectiveness of VMask by performing comprehensive experiments on both grey box (VGGVox) and black box (Microsoft Azure Speaker Verification) ASVs. Additionally, a real-world case study on Apple HomeKit proves the VMask's practicability on smart home platforms.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155483"}, {"primary_key": "2634727", "vector": [], "sparse_vector": [], "title": "Expanding the Role of Preambles to Support User-defined Functionality in MIMO-based WLANs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As the Wi-Fi technology goes through its sixth generation (Wi-Fi 6), there is a growing consensus on the need to support security and coordination functions at the Physical (PHY) layer, beyond traditional functions such as frame detection and rate adaptation. In contrast to the costly approach of extending the PHY-layer header to support new functions (e.g., Target Wake Time field in 802.11ax), we propose to turn a specific part of the frame preamble into a data field while maintaining its primary functions. Specifically, in this paper, we develop a scheme called extensible preamble modulation (ePMod) for the MIMO-based 802.11ac protocol. For each frame, eP-Mod can embed up to 20 bits into the 802.11ac preamble under 1 × 2 or 2 × 1 MIMO transmission modes to support the operations of a given PHY-layer function. The proposed scheme enables several promising PHY-layer services, such as PHY-layer encryption and channel/device authentication, PHYlayer signaling, etc. At the same time, it allows legacy (eP-Modunaware) devices to continue to process the received preamble as normal by guaranteeing that our proposed preamble waveforms satisfy the structural properties of a standardized preamble. Through numerical analysis, extensive simulations, and hardware experiments, we validate the practicality and reliability of ePMod.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155507"}, {"primary_key": "2634732", "vector": [], "sparse_vector": [], "title": "Machine Learning on Volatile Instances.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to the massive size of the neural network models and training datasets used in machine learning today, it is imperative to distribute stochastic gradient descent (SGD) by splitting up tasks such as gradient evaluation across multiple worker nodes. However, running distributed SGD can be prohibitively expensive because it may require specialized computing resources such as GPUs for extended periods of time. We propose cost-effective strategies to exploit volatile cloud instances that are cheaper than standard instances, but may be interrupted by higher priority workloads. To the best of our knowledge, this work is the first to quantify how variations in the number of active worker nodes (as a result of preemption) affects SGD convergence and the time to train the model. By understanding these trade-offs between preemption probability of the instances, accuracy, and training time, we are able to derive practical strategies for configuring distributed SGD jobs on volatile instances such as Amazon EC2 spot instances and other preemptible cloud instances. Experimental results show that our strategies achieve good training performance at substantially lower cost.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155448"}, {"primary_key": "2634734", "vector": [], "sparse_vector": [], "title": "Reliable Backscatter with Commodity BLE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recently backscatter communication with commodity radios has received significant attention since specialized hardware is no longer needed. The state-of-the-art BLE backscatter system, FreeRider, realizes ultra-low-power BLE backscatter communication entirely using commodity devices. It, however, suffers from several key reliability issues, including unreliable two-step modulation, productive-data dependency, and lack of interference countermeasures. To address these problems, we propose RBLE, a reliable BLE backscatter system that works with a single commodity receiver. It first introduces direct frequency shift modulation with the single tone generated by an excitation BLE device, making robust single-bit modulation possible. Then it designs dynamic channel configuration that enables channel hopping to avoid interfered channels. Moreover, it presents BLE packet regeneration that uses adaptive encoding to further enhance reliability for various channel conditions. The prototype is implemented using TI BLE radios and customized tags with FPGAs. Empirical results demonstrate that RBLE achieves more than 17x uplink goodput gains over FreeRider under indoor LoS, NLoS, and outdoor environments. We also show that RBLE can realize uplink ranges of up to 25 m for indoors and 56 m for outdoors.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155452"}, {"primary_key": "2634735", "vector": [], "sparse_vector": [], "title": "Improving Quality of Experience by Adaptive Video Streaming with Super-Resolution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ling<PERSON> Song", "<PERSON>"], "summary": "Given high-speed mobile Internet access today, audiences are expecting much higher video quality than before. Video service providers have deployed dynamic video bitrate adaptation services to fulfill such user demands. However, legacy video bitrate adaptation techniques are highly dependent on the estimation of dynamic bandwidth, and fail to integrate the video quality enhancement techniques, or consider the heterogeneous computing capabilities of client devices, leading to low quality of experience (QoE) for users. In this paper, we present a super-resolution based adaptive video streaming (SRAVS) framework, which applies a Reinforcement Learning (RL) model for integrating the video super-resolution (VSR) technique with the video streaming strategy. The VSR technique allows clients to download low bitrate video segments, reconstruct and enhance them to high-quality video segments while making the system less dependent on estimating dynamic bandwidth. The RL model investigates both the playback statistics and the distinguishing features related to the client-side computing capabilities. Trace-driven emulations over real-world videos and bandwidth traces verify that SRAVS can significantly improve the QoE for users compared to the state-of-the-art video streaming strategies with or without involving VSR techniques.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155384"}, {"primary_key": "2634736", "vector": [], "sparse_vector": [], "title": "TrueHeart: Continuous Authentication on Wrist-worn Wearables Using PPG-based Biometrics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Traditional one-time user authentication processes might cause friction and unfavorable user experience in many widely-used applications. This is a severe problem in particular for security-sensitive facilities if an adversary could obtain unauthorized privileges after a user's initial login. Recently, continuous user authentication (CA) has shown its great potential by enabling seamless user authentication with few active participation. We devise a low-cost system exploiting a user's pulsatile signals from the photoplethysmography (PPG) sensor in commercial wrist-worn wearables for CA. Compared to existing approaches, our system requires zero user effort and is applicable to practical scenarios with non-clinical PPG measurements having motion artifacts (MA). We explore the uniqueness of the human cardiac system and design an MA filtering method to mitigate the impacts of daily activities. Furthermore, we identify general fiducial features and develop an adaptive classifier using the gradient boosting tree (GBT) method. As a result, our system can authenticate users continuously based on their cardiac characteristics so little training effort is required. Experiments with our wrist-worn PPG sensing platform on 20 participants under practical scenarios demonstrate that our system can achieve a high CA accuracy of over 90% and a low false detection rate of 4% in detecting random attacks.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155526"}, {"primary_key": "2634737", "vector": [], "sparse_vector": [], "title": "An Energy Efficiency Multi-Level Transmission Strategy based on underwater multimodal communication in UWSNs.", "authors": ["<PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper discusses the data transmission strategy based on underwater multimodal communication for marine applications in underwater wireless sensor networks (UWSNs). Underwater data required by various applications have different values of information (VoIs) depending on the type and timeliness of events. These data should be transmitted in different time latency according to their VoIs to accommodate the both application requirements and network performance. Our objective is to design a multi-level transmission strategy by using underwater multimodal communication system so that multiple paths with different transmission latency and energy consumption are provided for underwater data in UWSNs. For this purpose, we first define a minimum cost flow (MCF) model for the design of transmission strategy that considers transmission latency, energy efficiency, and transfer load. Then a distributed multilevel transmission strategy EMTS is proposed based on time backoff method for large-scale UWSNs. Finally, we compared the transmission latency, energy efficiency and network lifetime obtained by our EMTS strategy to those of the optimum solution of the MCF model, and a transmission algorithm based on greedy strategy. Although the latency of EMTS is slightly higher than that of other algorithms, our average network lifetime can reach 88.7% of that of the optimum solution of the MCF model.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155381"}, {"primary_key": "2634740", "vector": [], "sparse_vector": [], "title": "Automatically and Adaptively Identifying Severe Alerts for Online Service Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In large-scale online service system, to enhance the quality of services, engineers need to collect various monitoring data and write many rules to trigger alerts. However, the number of alerts is way more than what on-call engineers can properly investigate. Thus, in practice, alerts are classified into several priority levels using manual rules, and on-call engineers primarily focus on handling the alerts with the highest priority level (i.e., severe alerts). Unfortunately, due to the complex and dynamic nature of the online services, this rule-based approach results in missed severe alerts or wasted troubleshooting time on non-severe alerts. In this paper, we propose AlertRank, an automatic and adaptive framework for identifying severe alerts. Specifically, AlertRank extracts a set of powerful and interpretable features (textual and temporal alert features, univariate and multivariate anomaly features for monitoring metrics), adopts XGBoost ranking algorithm to identify the severe alerts out of all incoming alerts, and uses novel methods to obtain labels for both training and testing. Experiments on the datasets from a top global commercial bank demonstrate that AlertRank is effective and achieves the F1-score of 0.89 on average, outperforming all baselines. The feedback from practice shows AlertRank can significantly save the manual efforts for on-call engineers.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155219"}, {"primary_key": "2634741", "vector": [], "sparse_vector": [], "title": "Efficient and Consistent TCAM Updates.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The dynamic nature of software-defined networking requires frequent updates to the flow table in the data plane of switches. Therefore, the ternary content-addressable memory (TCAM) used in switches to match packet header fields against forwarding rules needs to support high rates of updates. Existing off-the-shelf switches update rules in batches for efficiency, but may suffer from forwarding inconsistencies during the batch update. In this paper, we design and evaluate a TCAM update optimization framework that can guarantee consistent forwarding during the entire update process while making use of a layered TCAM structure. Our approach is based on a modified-entry-first write-back strategy that significantly reduces the overhead from movements of TCAM entries. In addition, our approach detects reordering cases, which are handled using efficient solutions. Based on our evaluation results, we can reduce the cost of TCAM updates by 30%-88% compared to state-of-the-art techniques.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155281"}, {"primary_key": "2634743", "vector": [], "sparse_vector": [], "title": "HiFi: Hybrid Rule Placement for Fine-Grained Flow Management in SDNs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Jing<PERSON> Fan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fine-grained flow management is useful in many practical applications, e.g., resource allocation, anomaly detection and traffic engineering. However, it is difficult to provide fine-grained management for a large number of flows in SDNs due to switches' limited flow table capacity. While using wildcard rules can reduce the number of flow entries needed, it cannot fully ensure fine-grained management for all the flows without degrading application performance. In this paper, we design and implement HiFi, a system that achieves fine-grained management with a minimal number of flow entries. To this end, HiFi takes a two-step approach: wildcard entry installment and application-specific exact-match entry installment. How to optimally install wildcard and exact-match flow entries, however, is intractable. Therefore, we design approximation algorithms with bounded factors to solve these problems. We consider how to achieve network-wide load balancing via fine-grained flow management as a case study. Both experimental and simulation results show that HiFi can reduce the number of required flow entries by about 45%-69% and reduce the control overhead by 28%-50% compared with the state-of-the-art approaches for achieving fine-grained flow management.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155348"}, {"primary_key": "2634744", "vector": [], "sparse_vector": [], "title": "Offloading Dependent Tasks in Mobile Edge Computing with Service Caching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In Mobile Edge Computing (MEC), many tasks require specific service support for execution and in addition, have a dependent order of execution among the tasks. However, previous works often ignore the impact of having limited services cached at the edge nodes on (dependent) task offloading, thus may lead to an infeasible offloading decision or a longer completion time. To bridge the gap, this paper studies how to efficiently offload dependent tasks to edge nodes with limited (and predetermined) service caching. We formally define the problem of offloading dependent tasks with service caching (ODT-SC), and prove that there exists no algorithm with constant approximation for this hard problem. Then, we design an efficient convex programming based algorithm (CP) to solve this problem. Moreover, we study a special case with a homogeneous MEC and propose a favorite successor based algorithm (FS) to solve this special case with a competitive ratio of O(1). Extensive simulation results using Google data traces show that our proposed algorithms can significantly reduce applications' completion time by about 27-51% compared with other alternatives.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155396"}, {"primary_key": "2634746", "vector": [], "sparse_vector": [], "title": "Towards Latency Optimization in Hybrid Service Function Chain Composition and Embedding.", "authors": ["<PERSON><PERSON> Zheng", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In Network Function Virtualization (NFV), to satisfy the Service Functions (SFs) requested by a customer, service providers will composite a Service Function Chain (SFC) and embed it onto the shared Substrate Network (SN). For many latency-sensitive and computing-intensive applications, the customer forwards data to the cloud/server and the cloud/server sends the results/models back, which may require different SFs to handle the forward and backward traffic. The SFC that requires different SFs in the forward and backward directions is referred to as hybrid SFC (h-SFC). In this paper, we, for the first time, comprehensively study how to optimize the latency in Hybrid SFC composition and Embedding (HSFCE). When each substrate node provides only one unique SF, we prove the NP-hardness of HSFCE and propose the first 2-approximation algorithm to jointly optimize the processes of h-SFC construction and embedding, which is called Eulerian Circuit based Hybrid SFP optimization (EC-HSFP). When a substrate node provides various SFs, we extend EC-HSFP and propose the efficient Betweenness Centrality based Hybrid SFP optimization (BC-HSFP) algorithm. Our extensive simulations and analysis show that EC-HSFP can hold the 2-approximation, while BC-HSFP outperforms the algorithms directly extended from the state-of-art techniques by an average of 20%.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155529"}, {"primary_key": "2634748", "vector": [], "sparse_vector": [], "title": "Joint Power Routing and Current Scheduling in Multi-Relay Magnetic MIMO WPT System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Magnetic resonant coupling wireless power transfer (MRC-WPT) enables convenient device-charging. When MIMO MRC-WPT system incorporated with multiple relay components, both relay On-Off state (i.e., power routing) and TX current (i.e., current scheduling) could be adjusted for improving charging efficiency and distance. Previous approaches need the collaboration and feedback from the energy receiver (RX), achieved using side-channels, e.g., Bluetooth, which is time/energy-consuming. In this work we propose, design, and implement a multi-relay MIMO MRC-WPT system, and design an almost optimum joint optimization of power routing and current scheduling method, without relying on any feedback from RX. We carefully decompose the joint optimization problem into two subproblems without affecting the overall optimality of the combined solution. For current scheduling subproblem, we propose an almost-optimum RX-feedback independent solution. For power routing subproblem, we first design a greedy algorithm with ½ approximation ratio, and then design a DQN based method to further improve its effectiveness. We prototype our system and evaluate it with extensive experiments. Our results demonstrate the effectiveness of the proposed algorithms. The achieved power transfer efficiency (PTE) on average is 3.2X, 1.43X, 1.34X, and 7.3X over the other four strategies: without relay, with non-adjustable relays, greed based, and shortest-path based ones.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155369"}, {"primary_key": "2634749", "vector": [], "sparse_vector": [], "title": "Design and Optimization of Electric Autonomous Vehicles with Renewable Energy Source for Smart Cities.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Electric autonomous vehicles provide a promising solution to the traffic congestion and air pollution problems in future smart cities. Considering intensive energy consumption, charging becomes of paramount importance to sustain the operation of these systems. Motivated by the innovations in renewable energy harvesting, we leverage solar energy to power autonomous vehicles via charging stations and solar-harvesting rooftops, and design a framework that optimizes the operation of these systems from end to end. With a fixed budget, our framework first optimizes the locations of charging stations based on historical spatial-temporal solar energy distribution and usage patterns, achieving (2 + ε) factor to the optimal. Then a stochastic algorithm is proposed to update the locations online to adapt to any shift in the distribution. Based on the deployment, a strategy is developed to assign energy requests in order to minimize their traveling distance to stations while not depleting their energy storage. Equipped with extra harvesting capability, we also optimize route planning to achieve a reasonable balance between energy consumed and harvested en-route. Our extensive simulations demonstrate the algorithm can approach the optimal solution within 10-15% approximation error, and improve the operating range of vehicles by up to 2-3 times compared to other competitive strategies.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155390"}, {"primary_key": "2634750", "vector": [], "sparse_vector": [], "title": "Computation Scheduling for Wireless Powered Mobile Edge Computing Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hong Gao"], "summary": "Mobile Edge Computing (MEC) and Wireless Power Transfer (WPT) are envisioned as two promising techniques to satisfy the increasing energy and computation requirements of latency-sensitive and computation-intensive applications installed on mobile devices. The integration of MEC and WPT introduces a novel paradigm named Wireless Powered Mobile Edge Computing (WP-MEC). In WP-MEC networks, edge devices located at the edge of radio access networks, such as access points and base stations, transmit radio frequency signals to power mobile devices and mobile devices can offload their intensive computation workloads to edge devices. In this paper, we study the Computation Completion Ratio Maximization Scheduling problem for WP-MEC networks with multiple edge devices, which is proved to be NP-hard. We jointly optimize the WPT time allocation and computation scheduling for mobile devices in a WP-MEC network to maximize the computation completion ratio of the WP-MEC network and propose approximation algorithms. The approximation ratio and computation complexity of the proposed algorithms are theoretically analyzed. Extensive simulations are conducted to verify the performance of the proposed algorithms.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155418"}, {"primary_key": "2634753", "vector": [], "sparse_vector": [], "title": "Low-Overhead Joint Beam-Selection and Random-Access Schemes for Massive Internet-of-Things with Non-Uniform Channel and Load.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study low-overhead uplink multi-access algorithms for massive Internet-of-Things (IoT) that can exploit the MIMO performance gain. Although MIMO improves system capacity, it usually requires high overhead due to Channel State Information (CSI) feedback, which is unsuitable for IoT. Recently, a Pseudo-Random Beam-Forming (PRBF) scheme was proposed to exploit the MIMO performance gain for uplink IoT access with uniform channel and load, without collecting CSI at the BS. For non-uniform channel and load, new adaptive beamselection and random-access algorithms are needed to efficiently utilize the system capacity with low overhead. Most existing algorithms for a related multi-channel scheduling problem require each node to at least know some information of the queue length of all contending nodes. In contrast, we propose a new Low-overhead Multi-Channel Joint Channel-Assignment and Random-Access (L-MC-JCARA) algorithm that reduces the overhead to be independent of the number of interfering nodes. A key novelty is to let the BS estimate the total backlog in each contention group by only observing the random-access events, so that no queue-length feedback is needed from IoT devices. We prove that L-MC-JCARA can achieve at least `0.24`` of the capacity region of the optimal centralized scheduler for the corresponding multi-channel system.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155460"}, {"primary_key": "2634754", "vector": [], "sparse_vector": [], "title": "U-CIMAN: Uncover Spectrum and User Information in LTE Mobile Access Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the proliferation of Dynamic Spectrum Access (DSA), Internet of Things (IoT), and Mobile Edge Computing (MEC) technologies, various methods have been proposed to deduce key network and user information in cellular systems, such as available cell bandwidths, as well as user locations and mobility. Not only is such information dominated by cellular networks of vital significance on other systems co-located spectrum-wise and/or geographically, but applications within cellular systems can also benefit remarkably from inferring such information, as exemplified by the endeavours made by video streaming to predict cell bandwidth. Hence, we are motivated to develop a new tool to uncover as much information used to be closed to outsiders or user devices as possible with off-the-shelf products. Given the wide-spread deployment of LTE and its continuous evolution to 5G, we design and implement U-CIMAN, a client-side system to accurately UnCover as much Information in Mobile Access Networks as allowed by LTE encryption. Among the many potential applications of U-CIMAN, we highlight one use case of accurately measuring the spectrum tenancy of a commercial LTE cell. Besides measuring spectrum tenancy in unit of resource blocks, U-CIMAN discovers user mobility and traffic types associated with spectrum usage through decoded control messages and user data bytes. We conduct 4-month detailed accurate spectrum measurement on a commercial LTE cell, and the observations include the predictive power of Modulation and Coding Scheme on spectrum tenancy, and channel off-time bounded under 10 seconds, to name a few.", "published": "2020-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM41043.2020.9155245"}]