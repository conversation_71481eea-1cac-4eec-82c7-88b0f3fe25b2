"use client";

import { LatexEditor } from "@/components/latex/LatexEditor";
import { LatexPreview } from "@/components/latex/LatexPreview";
import { LatexToolbar } from "@/components/latex/LatexToolbar";
import { useState, useRef } from "react";

export default function FormalPage() {
  const [latex, setLatex] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理图片上传
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = async () => {
      const base64 = (reader.result as string).split(",")[1];
      // 调用后端接口
      const res = await fetch("/api/sc-api-chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageBase64: base64 })
      });
      const data = await res.json();
      if (data.latex) {
        setLatex(prev => prev + data.latex); // 拼接到编辑区
      } else {
        alert("识别失败");
      }
    };
    reader.readAsDataURL(file);
  };

  return (
    <div className="flex flex-col h-screen p-4 gap-4 max-w-4xl mx-auto w-full px-4 sm:px-8">
      <h1 className="text-2xl font-bold">LaTeX 公式编辑器</h1>
      <input
        type="file"
        accept="image/*"
        ref={fileInputRef}
        style={{ display: "none" }}
        onChange={handleImageUpload}
      />

      <div className="flex-1 flex flex-col gap-4">
        <LatexToolbar onInsert={(formula) => setLatex(prev => prev + formula)} />
        
        <div className="flex-1 flex flex-col gap-4">
          <LatexEditor value={latex} onChange={setLatex} />
          <LatexPreview formula={latex} />
        </div>
      </div>
    </div>
  );
}
