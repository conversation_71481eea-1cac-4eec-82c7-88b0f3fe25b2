import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface Conference {
  name: string;
  description: string;
  ccf: string;
  impact: string;
  label: string;
}

interface ConferenceInfoProps {
  isOpen: boolean;
  onClose: () => void;
}

export const conferences: Record<string, Conference> = {
  'aaai': {
    name: 'AAAI Conference on Artificial Intelligence',
    description: '人工智能领域顶级国际会议，涵盖AI各方向',
    ccf: 'A',
    impact: '最高，全球顶级AI会议',
    label: 'AAAI'
  },
  'cvpr': {
    name: 'IEEE Conference on Computer Vision and Pattern Recognition',
    description: '计算机视觉领域最顶级会议，CV领域影响力最大',
    ccf: 'A',
    impact: '最高，CV领域首选会议',
    label: 'CVPR'
  },
  'eccv': {
    name: 'European Conference on Computer Vision',
    description: '欧洲计算机视觉顶级会议，CV领域三大顶会之一',
    ccf: 'A',
    impact: '最高，CV领域三大顶会之一',
    label: 'ECCV'
  },
  'iccv': {
    name: 'IEEE International Conference on Computer Vision',
    description: '计算机视觉领域顶级会议，CV领域三大顶会之一',
    ccf: 'A',
    impact: '最高，CV领域三大顶会之一',
    label: 'ICCV'
  },
  'icdm': {
    name: 'IEEE International Conference on Data Mining',
    description: '数据挖掘领域顶级国际会议',
    ccf: 'B',
    impact: '高，数据挖掘领域重要会议',
    label: 'ICDM'
  },
  'icip': {
    name: 'IEEE International Conference on Image Processing',
    description: '图像处理领域重要国际会议',
    ccf: 'C',
    impact: '中，图像处理领域有影响力',
    label: 'ICIP'
  },
  'iclr': {
    name: 'International Conference on Learning Representations',
    description: '表征学习领域新兴顶级会议，深度学习重要会议',
    ccf: '无',
    impact: '最高，深度学习领域增长最快的顶会',
    label: 'ICLR'
  },
  
  'nips': {
    name: 'Conference on Neural Information Processing Systems',
    description: '神经信息处理系统顶级会议，机器学习领域最具影响力会议之一',
    ccf: 'A',
    impact: '最高，机器学习领域最具影响力会议',
    label: 'NeurIPS'
  },
  'icml': {
    name: 'International Conference on Machine Learning',
    description: '机器学习领域顶级国际会议，与 NeurIPS 并称为机器学习领域两大顶会',
    ccf: 'A',
    impact: '最高，机器学习领域最具影响力会议之一',
    label: 'ICML'
  },
  'ijcai': {
    name: 'International Joint Conference on Artificial Intelligence',
    description: '人工智能领域顶级国际会议，涵盖AI各方向，历史悠久',
    ccf: 'A',
    impact: '最高，全球顶级AI会议',
    label: 'IJCAI'
  },
  'acl': {
    name: 'Annual Meeting of the Association for Computational Linguistics',
    description: 'NLP领域最顶级国际会议之一，涵盖所有NLP方向',
    ccf: 'A',
    impact: '最高，全球顶级NLP会议',
    label: 'ACL'
  },
  'aacl': {
    name: 'Asia-Pacific Chapter of the ACL Conference',
    description: 'ACL亚太分会，亚洲/太平洋区域顶级NLP会议',
    ccf: 'B',
    impact: '高，影响力逐年上升',
    label: 'AACL'
  },
  'naacl': {
    name: 'North American Chapter of the ACL Conference',
    description: 'ACL北美分会，北美区域顶级NLP会议',
    ccf: 'A',
    impact: '影响力顶级，和EMNLP、EACL齐名',
    label: 'NAACL'
  },
  'eacl': {
    name: 'European Chapter of the ACL Conference',
    description: 'ACL欧洲分会，欧洲区域顶级NLP会议',
    ccf: 'A',
    impact: '影响力顶级，和NAACL、EMNLP齐名',
    label: 'EACL'
  },
  'emnlp': {
    name: 'Empirical Methods in Natural Language Processing',
    description: 'NLP顶级会议，强调实证、实验方法',
    ccf: 'A',
    impact: '影响力最高，全球顶级NLP会议',
    label: 'EMNLP'
  },
  'coling': {
    name: 'International Conference on Computational Linguistics',
    description: '历史悠久的NLP国际大会，涵盖计算语言学各方向',
    ccf: 'B',
    impact: '影响力高，老牌NLP会议',
    label: 'COLING'
  },
  'ccl': {
    name: 'China National Conference on Computational Linguistics',
    description: '中国计算语言学大会，中文NLP最重要会议',
    ccf: '无',
    impact: '国内最高，中文NLP首选',
    label: 'CCL'
  },
  'lrec': {
    name: 'Language Resources and Evaluation Conference',
    description: '侧重语言资源、评测与数据集建设，在相关细分方向影响力大',
    ccf: '无',
    impact: '资源型NLP领域影响力大',
    label: 'LREC'
  },
  'findings': {
    name: 'Findings of Association for Computational Linguistics',
    description: 'ACL体系论文集，收录主会未录用但质量较高的论文，以及部分workshop、direct submission',
    ccf: '无',
    impact: '影响力高，略低于主会，认可度高',
    label: 'Findings'
  },
  'semeval': {
    name: 'Semantic Evaluation Workshop and Shared Task',
    description: 'NLP领域著名的国际语义评测竞赛与workshop，与NAACL/ACL/EMNLP等主会合办，论文多为系统描述或任务报告，含金量略低于主会但在任务竞赛领域极有影响力',
    ccf: '无',
    impact: '领域内极有影响力，尤其评测任务',
    label: 'SemEval'
  },
  'wmt': {
    name: 'Workshop on Machine Translation',
    description: '机器翻译领域最有影响力的国际评测竞赛与研讨会',
    ccf: '无',
    impact: 'MT领域影响力极高',
    label: 'WMT'
  },
  'ranlp': {
    name: 'Recent Advances in Natural Language Processing',
    description: '欧洲区域知名NLP会议，涵盖应用与理论',
    ccf: '无',
    impact: '欧洲有影响力，略低于EACL/EMNLP等',
    label: 'RANLP'
  }
};

export const journals: Record<string, Conference> = {
  'tdsc': {
    name: 'IEEE Transactions on Dependable and Secure Computing',
    description: 'IEEE顶级期刊，专注于可靠性和安全性',
    ccf: 'A',
    impact: '最高，IEEE顶级期刊',
    label: 'TDSC'
  },
  'tifs': {
    name: 'IEEE Transactions on Information Forensics and Security',
    description: 'IEEE顶级期刊，专注于信息取证和安全',
    ccf: 'A',
    impact: '最高，IEEE顶级期刊',
    label: 'TIFS'
  },
  'tvcg': {
    name: 'IEEE Transactions on Visualization and Computer Graphics',
    description: 'IEEE顶级期刊，专注于可视化和计算机图形学',
    ccf: 'A',
    impact: '最高，可视化领域顶级期刊',
    label: 'TVCG'
  },
  'tip': {
    name: 'IEEE Transactions on Image Processing',
    description: 'IEEE顶级期刊，专注于图像处理',
    ccf: 'A',
    impact: '最高，图像处理领域顶级期刊',
    label: 'TIP'
  },
  'ijcv': {
    name: 'International Journal of Computer Vision',
    description: '计算机视觉领域顶级期刊',
    ccf: 'A',
    impact: '最高，计算机视觉领域顶级期刊',
    label: 'IJCV'
  },
  'tpami': {
    name: 'IEEE Transactions on Pattern Analysis and Machine Intelligence',
    description: 'IEEE顶级期刊，专注于模式识别和机器学习',
    ccf: 'A',
    impact: '最高，模式识别领域顶级期刊',
    label: 'TPAMI'
  },
  'jmlr': {
    name: 'Journal of Machine Learning Research',
    description: '机器学习领域顶级期刊',
    ccf: 'A',
    impact: '最高，机器学习领域顶级期刊',
    label: 'JMLR'
  },
  'ai': {
    name: 'Artificial Intelligence',
    description: '人工智能领域顶级期刊',
    ccf: 'A',
    impact: '最高，人工智能领域顶级期刊',
    label: 'AI'
  },
  'tkde': {
    name: 'IEEE Transactions on Knowledge and Data Engineering',
    description: 'IEEE顶级期刊，专注于知识工程和数据挖掘',
    ccf: 'A',
    impact: '最高，知识工程领域顶级期刊',
    label: 'TKDE'
  },
};

// 导出会议选项数组，供选择器使用
export const conferenceOptions = Object.entries(conferences).map(([value, conf]) => ({
  value,
  label: conf.label
}));

// 导出期刊选项数组，供选择器使用
export const journalOptions = Object.entries(journals).map(([value, journal]) => ({
  value,
  label: journal.label
}));

export default function ConferenceInfo({ isOpen, onClose }: ConferenceInfoProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="text-lg font-bold">会议与期刊信息</DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[60vh] pr-4">
          <div className="space-y-4">
            <div>
              <h3 className="text-md font-semibold mb-2">会议</h3>
              <div className="space-y-2">
                {Object.entries(conferences).map(([key, conf]) => (
                  <div key={key} className="border-b pb-2 last:border-b-0">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold min-w-[60px]">{conf.label}</span>
                      <span className="text-sm text-gray-600 dark:text-gray-400 flex-1 truncate">
                        [{conf.ccf}] {conf.description}
                      </span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="text-sm text-gray-600 dark:text-gray-400 truncate max-w-[300px]">
                              {conf.name}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-[400px]">{conf.name}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="text-md font-semibold mb-2">期刊</h3>
              <div className="space-y-2">
                {Object.entries(journals).map(([key, journal]) => (
                  <div key={key} className="border-b pb-2 last:border-b-0">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold min-w-[60px]">{journal.label}</span>
                      <span className="text-sm text-gray-600 dark:text-gray-400 flex-1 truncate">
                        [{journal.ccf}] {journal.description}
                      </span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="text-sm text-gray-600 dark:text-gray-400 truncate max-w-[300px]">
                              {journal.name}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-[400px]">{journal.name}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}