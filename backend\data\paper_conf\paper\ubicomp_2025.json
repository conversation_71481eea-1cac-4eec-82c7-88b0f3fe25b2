[{"primary_key": "60375", "vector": [], "sparse_vector": [], "title": "MobHAR: Source-free Knowledge Transfer for Human Activity Recognition on Mobile Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Human Activity Recognition (HAR) faces significant challenges when deployed in real-world scenarios due to non-independent and identically distributed (non-IID) data distributions. While existing domain adaptation (DA) approaches attempt to address this issue, they either require access to source data or struggle with large domain shifts. This paper presents a novel source-free domain adaptation framework for HAR that effectively handles substantial domain discrepancies across different datasets. Our approach introduces two key innovations: (1) a Discriminative Information Gramian (DIG) method that quantifies the relationship between target-domain samples and the source domain without requiring access to source data, and (2) an unsupervised domain generalization technique that ensures consistent feature extraction across augmented data samples, enhancing the model's effectiveness in the target domain. We evaluate our framework on five diverse HAR datasets comprising 87 users with varying demographics, devices, and environmental conditions. In single-source scenarios, our method achieves 76.77% accuracy and 67.03% F1-score, surpassing state-of-the-art solutions by 9.77% and 17.43%, respectively. For multi-source scenarios, we attain 85.33% accuracy and 79.55% F1-score, exceeding existing methods by at least 8.8% and 14.8%, respectively. This work represents the first successful attempt at dataset-level HAR domain adaptation without access to source data, marking a significant advancement in practical HAR applications.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712620"}, {"primary_key": "60326", "vector": [], "sparse_vector": [], "title": "CALM: A Ubiquitous Crowdsourced Analytic Learning Mechanism for Continual Service Construction with Data Privacy Preservation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Huang", "<PERSON><PERSON>", "Feijiang Han", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mianxiong Dong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Leveraging collective capabilities, crowdsourcing has become a popular approach for ubiquitous service construction. The most classical paradigm constructs AI services on the platform using the workers' collected data centrally, leading to widespread data privacy concerns. Federated Learning represents another classical attempt to preserve data privacy through the workers' local training in a distributed and iterative manner. However, due to their inherited reliance on the gradients for back-propagation, they both suffer from the famous problem of \"catastrophic forgetting\". As incremental demands arise, continual training will significantly compromise previously learned knowledge within the AI model, leading to the degradation of service quality. To overcome these limitations, we propose a ubiquitous Crowdsourced Analytic Learning Mechanism, named CALM. By introducing analytic learning into crowdsourcing with meticulously designed computation and communication protocols, our CALM can effectively achieve data privacy preservation and absolute knowledge memorization. In CALM, the workers are required to locally perform lightweight computations without back-propagation using their collected data. Subsequently, they only need to upload the privacy-preserved results to the platform rather than the raw data. Finally, the platform can recursively aggregate the workers' uploaded results to continually update the AI model for providing ongoing services. Our CALM guarantees that the recursively derived model is fully equivalent to that obtained from centralized training using the complete dataset, fundamentally avoiding catastrophic forgetting. Theoretical analyses are comprehensively presented regarding the validity, security, efficiency, and interpretability of CALM. Extensive experiments using multiple real-world datasets are conducted to validate the state-of-the-art performance of CALM for continual service construction.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729473"}, {"primary_key": "60327", "vector": [], "sparse_vector": [], "title": "KeyPrint: Practical Black-box Keystroke Inference Attacks to Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "Dai<PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent years have shown substantial interest in revealing vulnerability issues of keystroke privacy on smartphones and tablets. While significant prior works have leveraged different techniques to compromise the keystroke security of these mobile devices, existing methods are typically executed on the basic assumption of white-box scenarios where the adversary possesses prior knowledge of the victim's device and usage behavior, or they are conducted within a pre-set trap attack environment. These limitations undermine the practicality and real-world applicability of the proposed methods. In this paper, we present KeyPrint, a practical black-box keystroke inference attack system for mobile devices, without requiring any prior knowledge of the victims and attack scenarios. The primary innovation of KeyPrint is the ability to leverage both on-device acoustic source and attenuation path disparities in device medium to create the fingerprint of keystroke position. To enable their differentiation, we design a theoretical model to represent the keystroke position with the keystroke-induced sonic effect (KiSe) captured by built-in microphones. We also propose a novel approach to mitigate the impact of ambient noise and detect keystroke events, which improves KeyPrint's wide-adaptability. Finally, we propose using machine learning to cluster KiSe samples and infer keystroke content from unlabelled clustering results. We implemented KeyPrint on commercial smartphones/tablets and evaluate the prototypes in typical indoor and outdoor scenarios using different mobile devices. Experiments results demonstrate that KeyPrint can achieve an average accuracy of 55% and 70% for inference on keystroke content when the number of inputted words only reaches 30 and 40, respectively. Leveraging the spatial correlation between numeric and letter keys within the virtual keyboard, KeyPrint effectively reduces the search space for PINs from 10 digits to 10 candidates, with a probability of 63.9%.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729493"}, {"primary_key": "60330", "vector": [], "sparse_vector": [], "title": "DermaGlow: Objective Quantification of Melanin, Erythema and Skin-tone Using Wearable Optical Spectroscopy.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Accurate characterization of the skin is essential for optimizing diagnostic and therapeutic dermatological tools, as well as technologies like pulse oximetry that rely on skin perfusion. Traditionally, optical spectroscopy has been used for skin assessments through devices like commercial colorimeters, which are high-cost instruments that, while precise, only provide single measurements rather than continuous data. Additionally, medical wearable devices that use this technology often show variable accuracy based on skin tone. The limitations of existing devices demonstrate the need for a solution that can provide low-cost, accurate, and continuous skin monitoring across varying skin tones in a wearable form-factor. This paper introduces DermaGlow, a novel wearable optical spectroscopy framework designed for low-cost, non-invasive monitoring of melanin, erythema, and skin tone. DermaGlow utilizes an off-the-shelf multi-spectrum wearable device available in various configurations to enable real-time, personalized assessments across diverse skin conditions and skin tones. We assess the performance of the DermaGlow algorithm against a state-of-the-art colorimeter in a comprehensive user study involving a diverse group of 77 subjects, demonstrating a normalized mean absolute error (NMAE) of 5.33% (melanin) and 4.18% (erythema), and ΔE values less than 2.5 for CIE LAB measurements. Furthermore, we present an algorithm that utilizes DermaGlow outputs to correct for pulse oximeter inaccuracies typically found in those with darker skin pigmentation, resulting in an up to 75% decrease in mean absolute error (MAE) in hypoxic readings across skin tones relative to arterial blood measurements. Our findings highlight DermaGlow's potential for short and long-term skin monitoring and as a significant enhancement to existing wearable devices, particularly in improving the accuracy of pulse oximeter readings across different skin tones.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729474"}, {"primary_key": "60331", "vector": [], "sparse_vector": [], "title": "DCSNN: An Efficient and High-speed sEMG-based Transient-state Micro-gesture Recognition Method on Wearable Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Xiangjin Chen", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Micro-gesture recognition using wearable devices is an important research topic in human-computer interaction. Surface electromyography (sEMG) is widely researched for gesture recognition due to its ability to capture muscle signals that precede actual gestures. Most existing methods are based on artificial neural networks (ANN), which may lead to high latency, high power consumption, and high memory usage when deployed on wearable devices. We propose a deep compressed spiking neural network (DCSNN) to address the challenges. The DCSNN can significantly reduce the inference power consumption and memory usage while improving recognition accuracy. In addition, we designed a linear method of action detection called leaky integrate-and-fire for transient-state action detection (TAD-LIF), which can improve the robustness of recognition systems effectively. To evaluate our method, we developed two lightweight sEMG wristbands respectively for two interaction modes, and collected two datasets from about 40 subjects. The experiment results show that DCSNN had a higher recognition accuracy than existing methods with values of 88.55% and 95.76% on the two datasets. In addition, its inference latency, power consumption, and memory usage are only about 0.4%, 0.05%, and 2% of those of popular convolutional neural network (CNN) methods. Our method enables precise, high-speed, and low-power micro-gesture recognition on a plethora of resource-constrained consumer-level intelligent wearable devices.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729494"}, {"primary_key": "60333", "vector": [], "sparse_vector": [], "title": "Past, Present, and Future of Sensor-based Human Activity Recognition Using Wearables: A Surveying Tutorial on a Still Challenging Task.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the many years since the inception of wearable sensor-based Human Activity Recognition (HAR), a wide variety of methods have been introduced and evaluated for their ability to recognize activities. Substantial gains have been made since the days of hand-crafting heuristics as features, yet, progress has seemingly stalled on many popular benchmarks, with performance falling short of what may be considered 'sufficient'-despite the increase in computational power and scale of sensor data, as well as rising complexity in techniques being employed. The HAR community approaches a new paradigm shift, this time incorporating world knowledge from foundational models. In this paper, we take stock of sensor-based HAR - surveying it from its beginnings to the current state of the field, and charting its future. This is accompanied by a hands-on tutorial, through which we guide practitioners in developing HAR systems for real-world application scenarios. We provide a compendium for novices and experts alike, of methods that aim at finally solving the activity recognition problem.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729467"}, {"primary_key": "60336", "vector": [], "sparse_vector": [], "title": "MagKey: Empowering Wearables with Ballistocardiography-based Key Generation through Magnetic Field Vibration Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yuanchao Shu", "<PERSON><PERSON>"], "summary": "Symmetric key generation based on biometrics has emerged as a promising solution for wearables pairing. Among various biometrics, heartbeats offer significant potential owing to their inherent randomness and spontaneity. Ballistocardiography (BCG), in particular, stands out for its accessibility and inclusivity, as it measures the body's recoil forces in response to cardiac blood ejection into the vasculature. However, traditional approaches to BCG suffer from challenges in sensing on wearables and limited key generation rates. To this end, this paper presents MagKey, a system that enables wearables with BCG-based key generation. MagKey overcomes the difficulties in effective BCG sensing by translating skin vibration caused by recoil forces into magnetic field vibration (MFV). Moreover, <PERSON>gKey demonstrates that the peak-to-peak trend (PPT) of MFV signals can reliably extract keys, and thus improve the key generation rate. To mitigate the impact of noise and motion artifacts on key generation, MagKey employs analog filters and a peak screening method for signal processing. We implement MagKey on a one-layer flexible printed circuit (FPC) and a two-layer printed circuit board (PCB). Extensive experiments show the usability and effectiveness of MagKey. Furthermore, our security analyses illustrate the scheme's resilience against potential attacks.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712272"}, {"primary_key": "60342", "vector": [], "sparse_vector": [], "title": "Advancing Wearable BCI: Headphone EEG for Cognitive Load Detection in Lab and Field.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "By tracking changes in brain activity, researchers are constantly working to revolutionise human-technology interaction. Unfortunately, such brain-computer interfaces still face limitations in terms of wearability, making large-scale data collection difficult. While gel-based wearable solutions exist, validated dry electrode systems are needed for practical everyday use. In this article, two experiments with 50 participants and 146 recordings were conducted in laboratory and field settings to compare the wearability and performance of two dry-electrode EEG systems: the Open ExG headphones and the OpenBCI Ultracortex full-head EEG. Our results show that the headphone EEG is perceived as more wearable, has equal signal quality and recording reliability when set up by a trained experimenter in the lab, and shows reliable performance in a relevant application scenario: classification of cognitive load levels across four tasks. Field evaluations further validate these results through reliable load monitoring across recording sessions, after self-setup by study participants at home. While some limitations remain for wider field use of dry-electrode headphone EEG, we highlight necessary and achievable improvements for future system and study designs for real-world use.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712283"}, {"primary_key": "60343", "vector": [], "sparse_vector": [], "title": "HandSAW: Wearable Hand-based Event Recognition via On-Body Surface Acoustic Waves.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hyunmin Park", "<PERSON><PERSON>"], "summary": "Enabling computing systems to detect the objects that people hold and interact with provides valuable contextual information that has the potential to support a wide variety of mobile applications. However, existing approaches either directly instrument users' hands, which can reduce tactile sensation, or are limited in the types of objects and interactions they can detect. This work introduces HandSAW, a wireless wrist-worn device incorporating a Surface Acoustic Wave (SAW) sensor with enhanced bandwidth and signal-to-noise ratio while rejecting through-air sounds. The device features a sealed mass-spring diaphragm positioned on top of the sound port of a MEMS microphone, enabling it to capture SAWs generated by objects and through touch interaction events. This custom-designed wearable platform, paired with a real-time ML pipeline, can distinguish 20 passive object events with &gt;99% per-user accuracy and a 91.6% unseen-user accuracy, as validated through a 16-participant user study. For devices that do not emit SAWs, our active tags enable HandSAW to detect those objects and transmit encoded data using ultrasonic signals. Ultimately, HandSAW provides an easy-to-implement, robust, and cost-effective means for enabling user-object interaction and activity detection.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712276"}, {"primary_key": "60348", "vector": [], "sparse_vector": [], "title": "Ethical Disengagement in Mobile Games: The Effects of Loading Delay and Grayscale on User Engagement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "S<PERSON>o Fu<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This study addresses a knowledge gap in understanding intervention strategies for ethical disengagement in mobile games, focusing on approaches that prioritize player health by moderating game interactivity and aesthetics to mitigate usage. In a randomized experiment involving 84,325 participants playing Flying Gorilla over one month, we evaluated user engagement across seven conditions: a baseline, loading time delays of 1, 5, and 10 seconds, grayscale display, and combinations of grayscale with 5- and 10-second delays. The results indicated that loading time delays and grayscale display effectively reduced retention rates and average daily playtime, with the most significant impact observed when combining grayscale with longer loading time delays. Specifically, a grayscale display paired with a 10-second loading delay reduced daily playtime by up to 30.8% and retention rates by 40.4% compared to the baseline. Additionally, regional and user-type analyses suggested that the effects of these interventions may vary based on regional characteristics and user engagement patterns. These findings highlight the potential of such interventions that might help nudge players to curb excessive gaming and offer insights into designing mobile gaming experiences that consider player well-being.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712281"}, {"primary_key": "60350", "vector": [], "sparse_vector": [], "title": "SoundTrack: A Contactless Mobile Solution for Real-time Running Metric Estimation for Treadmill Running in the Wild.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Runxi <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Running metrics like cadence and ground contact time (GCT) are crucial for both novice and experienced runners to optimize performance and prevent injuries. We present SoundTrack, a contactless mobile solution that estimates these metrics by analyzing treadmill running sounds using on-device machine learning. Our main contributions are: (i) SoundTrackDB - a comprehensive 40-hour dataset of treadmill running sounds collected from 61 subjects across 363 sessions in 13 public gyms, created in collaboration with a licensed running coach; and (ii) SoundTrack - an on-device mobile system capturing treadmill running sounds, mitigating noise, estimating cadence and GCT with a custom multi-layer perceptron (MLP) model, and providing real-time feedback. Microbenchmarks and evaluations show that SoundTrack effectively mitigates real-world noise challenges in public gyms and adapts to individual variations among runners and treadmill models. It achieves mean absolute percentage errors (MAPEs) of 1.62% for cadence and 6.05% for GCT on the test set of unseen running sessions, yielding results that are superior or comparable to commercial sports wearables. SoundTrack offers an accessible solution for treadmill metrics on mobile platforms, reducing reliance on specialized wearables and broadening accessibility. SoundTrackDB, SoundTrack, and the demonstration video are available at: https://github.com/Columbia-ICSL/SoundTrackDB.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729486"}, {"primary_key": "60352", "vector": [], "sparse_vector": [], "title": "UltraBoard: Always-available Wearable Ultrasonic Mid-air Haptic Interface for Responsive and Robust VR Inputs.", "authors": ["Changhyeon Park", "<PERSON><PERSON>", "<PERSON>"], "summary": "Free-hand interaction in VR is intuitive and easy to learn, making it applicable to fundamental interactions such as VR typing. However, the absence of haptic feedback reduces spatial awareness and immersion while performing the input, impacting accuracy and increasing fatigue. While previous works embedded haptic feedback, they either require constant contact with the skin or a desktop installation with a fixed distance. We propose Ultraboard, a novel wearable haptic interface providing ultrasonic mid-air haptic feedback for all hand regions, including fingertips. We adaptively control the phased array's position in accordance with the hand movement and location to support consistent haptic feedback for real-time VR typing input. With simulation and experiment, we designed and validated the customized ultrasound phased array to support hand input. We propose guidelines for the development of a finger-level wearable ultrasonic mid-air haptics interface and system through this process. A follow-up user study showed significant improvements in typing confidence and usability with Ultraboard. Notably, the mid-air tactile typo alert enabled with our interface enhances user experience during VR typing. Our results showed a promising approach to utilizing a mid-air haptic interface to promote confidence in VR typing.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3731413"}, {"primary_key": "60356", "vector": [], "sparse_vector": [], "title": "AudioCast: Enabling Ubiquitous Connectivity for Embedded Systems through Audio-broadcasting Low-power Tags.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nobel Ang", "<PERSON><PERSON><PERSON>"], "summary": "Wireless connectivity challenges hinder the deployment of embedded systems. We introduce AudioCast to address two critical issues: spectrum scarcity-induced contention and high power consumption in transmitters. The widespread availability of broadcast radio receivers (for example, FM radios using the 88-108 MHz spectrum) and access to underutilized lower-frequency spectrum motivate the design of AudioCast. The lower-frequency spectrum offers superior radio-wave propagation characteristics, exhibiting at least 10 × lower path loss than the 2.4 GHz and 5 GHz Industrial, Scientific, and Medical (ISM) bands while avoiding congestion and interference. These properties enable reliable and long-distance communication, even for weakly radiated signals. AudioCast builds on these properties and the unique negative resistance of a tunnel diode. AudioCast rethinks the architecture of radio transmitters using a tunnel diode oscillator to generate carrier signals and self-modulate them with baseband signals. This results in frequency-modulated transmissions at an overall power consumption below 200 μW. Unlike related systems based on the backscatter mechanism, AudioCast does not require an externally generated carrier or rely on ambient signals. We argue that AudioCast represents an example of a new class of transmitters which we conceptualize as Beyond-Backscatter transmitters. Through experiments, we demonstrate that AudioCast achieves a transmission range of up to 130 m in line-of-sight and tens of meters in non-line-of-sight conditions respectively. These transmissions are decodable by ubiquitous commodity FM receivers in cars, homes, and phones. We evaluate AudioCast through theoretical analysis, benchtop experiments, and urban/indoor field deployments. Additionally, we prototype and demonstrate novel applications, including low-power voice transmissions and hand gesture communication, enabled by AudioCast's range and power efficiency.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729471"}, {"primary_key": "60374", "vector": [], "sparse_vector": [], "title": "Incorporating Uncertainty in Predictive Models Using Mobile Sensing and Clinical Data: A Case Study on Persistent Post-surgical Pain.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Liu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Chenyang Lu"], "summary": "Persistent postoperative pain (PPSP) is a significant concern in perioperative care, affecting patient quality of life and clinical outcomes. While traditional clinical data may capture patient-reported and objective physiological data in clinical settings, Ecological Momentary Assessment (EMA) collected using smartphones offers dynamic insights into patients' mental and emotional states in their natural environments. However, the self-reported nature of EMA introduces significant variability in both data quality and patient compliance. This variability is often overlooked in studies, where EMA data is treated as contributing equally for all patients. In this paper, we propose an end-to-end approach that addresses this challenge by first quantifying the prediction uncertainty for each data source, including both EMA and clinical data. Next, we integrate the uncertainties into the final decision-making process by assigning more weight to more reliable data sources at the individual level, improving overall predictive performance. Compared to traditional models, our model provides uncertainty estimates for each decision, offering clinicians critical insight into the trustworthiness of predictions. Finally, we conduct both qualitative and quantitative analysis to demonstrate the impact of our uncertainty estimates. Validated through a prospective clinical study involving 782 patients undergoing surgery, our approach achieves superior predictive performance and calibration in PPSP prediction. This work is expected to contribute to personalized perioperative care by effectively integrating traditional clinical and mobile sensing data through an end-to-end uncertainty-aware framework.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729488"}, {"primary_key": "60376", "vector": [], "sparse_vector": [], "title": "SocialMind: LLM-based Proactive AR Social Assistive System with Human-like Perception for In-situ Live Interactions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zhenyu Yan", "Hongkai Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Social interactions are fundamental to human life. The recent emergence of large language models (LLMs)-based virtual assistants has demonstrated their potential to revolutionize human interactions and lifestyles. However, existing assistive systems mainly provide reactive services to individual users, rather than offering in-situ assistance during live social interactions with conversational partners. In this study, we introduce SocialMind, the first LLM-based proactive AR social assistive system that provides users with in-situ social assistance. SocialMind employs human-like perception leveraging multi-modal sensors to extract both verbal and nonverbal cues, social factors, and implicit personas, incorporating these social cues into LLM reasoning for social suggestion generation. Additionally, SocialMind employs a multi-tier collaborative generation strategy and proactive update mechanism to display social suggestions on Augmented Reality (AR) glasses, ensuring that suggestions are timely provided to users without disrupting the natural flow of conversation. Evaluations on three public datasets and a user study with 20 participants show that SocialMind achieves 38.3% higher engagement compared to baselines, and 95% of participants are willing to use SocialMind in their live social interactions.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712286"}, {"primary_key": "60383", "vector": [], "sparse_vector": [], "title": "TAPOR: 3D Hand Pose Reconstruction with Fully Passive Thermal Sensing for Around-Device Interactions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents the design and implementation of TAPOR, a privacy-preserving, non-contact, and fully passive sensing system for accurate and robust 3D hand pose reconstruction for around-device interaction using a single low-cost thermal array sensor. Thermal sensing using inexpensive and miniature thermal arrays emerges with an excellent utility-privacy balance, offering an imaging resolution significantly lower than cameras but far superior to RF signals like radar or WiFi. The design of TAPOR, however, is challenging, mainly because the captured temperature maps are low-resolution and textureless. To overcome the challenges, we investigate thermo-depth and thermo-pose properties, proposing a novel physics-inspired neural network that learns effective 3D spatial representations of potential hand poses. We then formulate the 3D pose reconstruction problem as a distinct retrieval task, enabling accurate hand pose determination from the input temperature map. To deploy TAPOR on IoT devices, we introduce an effective heterogeneous knowledge distillation method, reducing computation by 377×. TAPOR is fully implemented and tested in real-world scenarios, showing remarkable performance, supported by four gesture control and finger tracking case studies. We envision TAPOR to be a ubiquitous interface for around-device control and have open-sourced it at https://github.com/aiot-lab/TAPOR.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729499"}, {"primary_key": "60322", "vector": [], "sparse_vector": [], "title": "DiversityOne: A Multi-Country Smartphone Sensor Dataset for Everyday Life Behavior Modeling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Chaitanya Nutakki", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-Lara", "<PERSON>", "<PERSON>", "<PERSON>", "Amarsanaa Ganbold", "Altangerel Chagnaa", "<PERSON>", "Al<PERSON><PERSON> Hume", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Understanding everyday life behavior of young adults through personal devices, e.g., smartphones and smartwatches, is key for various applications, from enhancing the user experience in mobile apps to enabling appropriate interventions in digital health apps. Towards this goal, previous studies have relied on datasets combining passive sensor data with human-provided annotations or self-reports. However, many existing datasets are limited in scope, often focusing on specific countries primarily in the Global North, involving a small number of participants, or using a limited range of pre-processed sensors. These limitations restrict the ability to capture cross-country variations of human behavior, including the possibility of studying model generalization, and robustness. To address this gap, we introduce DiversityOne, a dataset which spans eight countries (China, Denmark, India, Italy, Mexico, Mongolia, Paraguay, and the United Kingdom) and includes data from 782 college students over four weeks. DiversityOne contains data from 26 smartphone sensor modalities and 350K+ self-reports. As of today, it is one of the largest and most diverse publicly available datasets, while featuring extensive demographic and psychosocial survey data. DiversityOne opens the possibility of studying important research problems in ubiquitous computing, particularly in domain adaptation and generalization across countries, all research areas so far largely underexplored because of the lack of adequate datasets.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712289"}, {"primary_key": "60323", "vector": [], "sparse_vector": [], "title": "Moss: Proxy Model-based Full-Weight Aggregation in Federated Learning with Heterogeneous Models.", "authors": ["Yifeng Cai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern Federated Learning (FL) has become increasingly essential for handling highly heterogeneous mobile devices. Current approaches adopt a partial model aggregation paradigm that leads to sub-optimal model accuracy and higher training overhead. In this paper, we challenge the prevailing notion of partial-model aggregation and propose a novel \"full-weight aggregation\" method named Moss, which aggregates all weights within heterogeneous models to preserve comprehensive knowledge. Evaluation across various applications demonstrates that <PERSON> significantly accelerates training, reduces on-device training time and energy consumption, enhances accuracy, and minimizes network bandwidth utilization when compared to state-of-the-art baselines.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712274"}, {"primary_key": "60324", "vector": [], "sparse_vector": [], "title": "Graph-based Fingerprint Update Using Unlabelled WiFi Signals.", "authors": ["Ka <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "S.<PERSON><PERSON><PERSON>"], "summary": "WiFi received signal strength (RSS) environment evolves over time due to the movement of access points (APs), AP power adjustment, installation and removal of APs, etc. We study how to effectively update an existing database of fingerprints, defined as the RSS values of APs at designated locations, using a batch of newly collected unlabelled (possibly crowdsourced) WiFi signals. Prior art either estimates the locations of the new signals without updating the existing fingerprints or filters out the new APs without sufficiently embracing their features. To address that, we propose GUFU, a novel effective graph-based approach to update WiFi fingerprints using unlabelled signals with possibly new APs. Based on the observation that similar signal vectors likely imply physical proximity, GUFU employs a graph neural network (GNN) and a link prediction algorithm to retrain an incremental network given the new signals and APs. After the retraining, it then updates the signal vectors at the designated locations. Through extensive experiments in four large representative sites, GUFU is shown to achieve remarkably higher fingerprint adaptivity as compared with other state-of-the-art approaches, with error reduction of 21.4% and 29.8% in RSS values and location prediction, respectively.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712277"}, {"primary_key": "60325", "vector": [], "sparse_vector": [], "title": "RadioGami: Batteryless, Long-range Wireless Paper Sensors Using Tunnel Diodes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Paper-based interactive RF devices have opened new possibilities for wireless sensing, yet they are typically constrained by short operational ranges. This paper introduces RadioGami, a method for creating long-range, batteryless RF sensing surfaces on paper using low-cost, DIY materials like copper tape, paper, and off-the-shelf electronics paired with an affordable radio receiver (approx. $20). We explore the design space enabled by RadioGami, including sensing paper deformations like bending, tearing, and origami patterns (<PERSON><PERSON>, <PERSON>) at ranges up to 45.73 meters. RadioGami employs a novel ultra-low power (35μW) switching circuit with a tunnel diode for wireless functionality. These surfaces can sustainably operate by harvesting energy using tiny photodiodes. We demonstrate applications that monitor object status, track user interactions (rotation, sliding), and detect environmental changes. We characterize performance, sensitivity, range, and power consumption with deployment studies. RadioGami advances sustainable, tangible, and batteryless interfaces for embodied interaction.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729487"}, {"primary_key": "60328", "vector": [], "sparse_vector": [], "title": "SputumLocator: Enhancing Airway Clearance with Auscultation-based Sputum Localization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Airway clearance is essential for managing Muco-Obstructive Lung Diseases (MOLDs). Percussion, a widely used airway clearance technique (ACT) in community and home care settings, is favored for its ease of implementation compared to other complex techniques. However, percussion is time-consuming and physically demanding for both caregivers and patients, as caregivers typically perform percussion on the entire back to avoid missing accumulated sputum when its exact location is unknown. Therefore, accurate sputum localization can significantly enhance the percussion experience. Current clinical methods for sputum localization typically rely on imaging techniques, which are costly, expose patients to radiation, and are usually performed only once during diagnosis, thereby limiting their application to inpatient settings. Alternatively, some medical professionals combine auscultation with other clinical assessments, but this approach requires substantial clinical experience and is impractical for community or home care settings where medical experts are unavailable. To address these limitations, we introduce SputumLocator, an innovative sputum localization system based on digital stethoscopes. SputumLocator leverages standard auscultation procedures to detect accumulated sputum in the four quadrants of the back, which is straightforward and highly practical. SputumLocator comprises two components: SputumEmbedder, which extracts key abnormal sounds and their spatial features using a Transformer-based feature extractor, and SputumClassifier, which maps these features to determine sputum presence in each region via a Convolutional Block Attention Module (CBAM). Given the limited availability of annotated sputum data, we developed a pretraining method based on Embedding on Masked Data (EOM) and enhanced model robustness through a Teacher-Student Architecture (TSA) that integrates noisy data. In collaboration with a medical institution, we evaluate SputumLocator on 43 patients with diverse physiological characteristics and under varying recording conditions. Experimental results demonstrate that SputumLocator achieves high accuracy with an overall sensitivity of 0.97, specificity of 0.82, and F1-Score of 0.83, maintaining robustness across different thoracic regions, genders, and disease types.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729472"}, {"primary_key": "60329", "vector": [], "sparse_vector": [], "title": "EchoTouch: Low-power Face-touching Behavior Recognition Using Active Acoustic Sensing on Glasses.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Accurately recognizing face-touching behavior at any time and place can help prevent potential health risks and improve personal habits. However, there remains a lack of effective methods that can be applied in real-world scenarios. In this paper, we propose EchoTouch, a low-power, unobtrusive active acoustic sensing system for monitoring face-touching behavior. EchoTouch captures features from both sides of the face by emitting and receiving orthogonal ultrasound signals through two pairs of microphones and speakers mounted along the under frame of glasses. Then, a lightweight and multi-task deep learning framework identifies the touch area and determines whether the behavior is intrusive to prevent such actions. Finally, a two-stage irrelevant action filtering mechanism effectively handles various interferences. We evaluate EchoTouch on 20 individuals using 11 different types of face-touching areas. EchoTouch achieves an average accuracy of 92.9%, with an 87.2% accuracy in determining whether the behavior is intrusive. Additionally, in-the-wild evaluations further validate the robustness of EchoTouch. We believe that EchoTouch can serve as an unobtrusive and reliable way to monitor and prevent intrusive face-touching behavior.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729481"}, {"primary_key": "60332", "vector": [], "sparse_vector": [], "title": "MC-LoRa: Multi-node Concurrent Localization for LoRaWAN Indoors and Outdoors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xianjin Xia"], "summary": "Multi-node localization is crucial for large-scale and densely deployed Internet of Things (IoT) devices connected via LoRaWAN. Due to limitations in bandwidth and the number of RX chains (antennas), existing LoRaWAN-based localization methods often rely on frequency hopping or additional infrastructure to improve location accuracy. Although promising, these methods struggle to localize multiple nodes during packet collisions. In this paper, we propose MC-LoRa, which features a multi-node localization pipeline that includes reliable preamble detection under the near-far effect, tackling inter-symbol interference among multiple packets, and a virtual antenna array method to obtain extra channel state measurements within a single channel. This approach not only enhances angle resolution in our AoA-based system but also eliminates the need for time-consuming frequency hopping, requiring only software processing in existing gateways. Our extensive evaluation results show that MC-LoRa achieves median errors of 7.1m (single-node), 9.2m (multi-node) in an outdoor area of 140m × 100m, and 2.0m (single-node), 3.9m (multi-node) in an indoor area of 20m × 16m, which represent improvements of 1.1×, 2× and 1.5×, 1.7× compared to the baseline. Additionally, MC-LoRa can provide localization service for hundreds of LoRaWAN nodes with accuracy comparable to that of a state-of-the-art single-node system. Its wide localization range and high accuracy enable MC-LoRa to benefit a variety of applications, including asset tracking, navigation in vast indoor spaces (e.g., airports, warehouses and halls), and smart cities.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712279"}, {"primary_key": "60334", "vector": [], "sparse_vector": [], "title": "The House That Saves Me? Assessing the Role of Smart Home Automation in Warning Scenarios.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Hiba K. Al-Najmi", "<PERSON>", "<PERSON>"], "summary": "As smart home technology becomes integral to modern living, researchers must consider safety aspects. While single-purpose devices alert users to specific dangers, integrating them within comprehensive smart home warning systems (SHWSs) offers new safety potentials by allowing actuators to respond to threats based on predefined protocols. Key questions include whether user preferences for automation levels in smart homes are affected by different warning scenarios, and how unwanted automation or false positives influence acceptance. To explore this, we conduct two studies: (1) A lab study in a smart home with various actuators, where participants (N = 48) encounter warnings across three automation levels. (2) A follow-up interview study (N = 16) further evaluating our prototype and unwanted automation situations. Results show that participants preferred higher automation during warnings and were more receptive to smart technology in dangerous situations, though customization remains essential to ensure acceptance. While higher automation levels reduced perceived interruption, some still preferred less intense warnings. Others preferred not receiving warnings of mild dangers, fully relying on automation. Finally, we find that specific safety protocols and handling of false positive alarms must be chosen carefully to avoid mistrust, users feeling a loss of control, and damage through unwanted executions.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712269"}, {"primary_key": "60335", "vector": [], "sparse_vector": [], "title": "OpenMAE: Efficient Masked Autoencoder for Vibration Sensing with Open-domain Data Enrichment.", "authors": ["<PERSON><PERSON> Hu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper introduces OpenMAE, a novel data enrichment framework utilizing open-world sensor data streams to facilitate efficient masked autoencoder (MAE) pretraining on vibration signals. Due to highly sparse event occurrences and inevitable distributional shifts from downstream tasks, directly concatenating large-scale open-domain data with limited in-domain data during pretraining leads to degraded downstream task performance. The problem is further complicated by missing knowledge of open-world sensor environments and associated physical event semantics. Against these challenges, OpenMAE makes the following contributions to vibration MAE pretraining with open-domain data: First, it automatically filters out uninformative samples based on the event activeness and information consistency without relying on human annotations; Second, to mind the gap between open-domain and in-domain distributions, OpenMAE develops a novel data mixing method, FreqCutMix, that combines two data types in the frequency domain as augmented pretraining samples, preserving both events-of-interest semantics from in-domain data and real-world diversity from open-domain data. The open-domain data scale in data mixing is dynamically increased as pretraining progresses to stabilize the model convergence. We download over 5 million open-world vibration samples from the Raspberry Shake datacenter1 and conduct extensive experiments with two applications (i.e., indoor activity and outdoor transportation analysis). The evaluation results show OpenMAE improves downstream task accuracies by up to 23% and achieves enhanced generalizability into diverse downstream tasks, domain variations, and sensor-to-target distances.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729485"}, {"primary_key": "60337", "vector": [], "sparse_vector": [], "title": "H2OPulse: Smartphone-assisted Vein Evaluation for Early Recognition of Dehydration.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON>"], "summary": "Maintaining human health relies on adequate water intake for digestion, waste elimination, and temperature regulation. Dehydration occurs when the body loses more fluids than it takes in, leading to electrolyte imbalances and serious complications, especially in vulnerable populations. Traditional dehydration detection methods rely on laboratory tests and physical exams, which can be invasive, expensive, time-consuming, and often inaccessible in rural areas. Recent research has proposed several solutions, but these are often either unreliable or not widely accessible. We introduce H2OPulse, the first smartphone-based solution to use vein visibility patterns for dehydration detection, analyzing changes in dorsal and wrist veins to distinguish between hydrated and dehydrated states. Our detection pipeline uses optimized image segmentation and enhancement with transfer learning and Siamese networks to accurately learn and differentiate vein visibility features. Our dataset comprises 3,440 hand vein images from 86 healthy volunteers, captured in both hydrated and dehydrated conditions. Experimental results demonstrate that H2OPulse can accurately detect mild dehydration, making it a practical and accessible solution, with an accuracy of 83.1% and an F1-score of 80.8%. This system empowers early dehydration detection anywhere, enabling timely interventions to prevent further complications.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729490"}, {"primary_key": "60338", "vector": [], "sparse_vector": [], "title": "CRoP: Context-wise Robust Static Human-Sensing Personalization.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The advancement in deep learning and internet-of-things have led to diverse human sensing applications. However, distinct patterns in human sensing, influenced by various factors or contexts, challenge the generic neural network model's performance due to natural distribution shifts. To address this, personalization tailors models to individual users. Yet most personalization studies overlook intra-user heterogeneity across contexts in sensory data, limiting intra-user generalizability. This limitation is especially critical in clinical applications, where limited data availability hampers both generalizability and personalization. Notably, intra-user sensing attributes are expected to change due to external factors such as treatment progression, further complicating the challenges. To address the intra-user generalization challenge, this work introduces CRoP, a novel static personalization approach. CRoP leverages off-the-shelf pre-trained models as generic starting points and captures user-specific traits through adaptive pruning on a minimal sub-network while allowing generic knowledge to be incorporated in remaining parameters. CRoP demonstrates superior personalization effectiveness and intra-user robustness across four human-sensing datasets, including two from real-world health domains, underscoring its practical and social impact. Additionally, to support CRoP's generalization ability and design choices, we provide empirical justification through gradient inner product analysis, ablation studies, and comparisons against state-of-the-art baselines.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729483"}, {"primary_key": "60339", "vector": [], "sparse_vector": [], "title": "Respiration Rate Estimation via Smartwatch-based Photoplethysmography and Accelerometer Data: A Transfer Learning Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Respiration Rate (RR) is a biomarker for several illnesses that can be extracted from biosignals, such as photoplethysmogram (PPG) and accelerometers. Smartwatch-based PPG signals are more prone to noise interference, particularly within their lower frequency spectrum where respiratory data is embedded. Therefore, existing methods are insufficient for extracting RR from PPG data collected from wrists reliably. Additionally, accelerometer sensors embedded in smartwatches capture respiration-induced motion and can be integrated with PPG signals to improve RR extraction. This paper proposes a deep learning-based model to extract RR from raw PPG and accelerometer signals captured via a smartwatch. The proposed network combines dilated residual inception module and Multi-Scale convolutions. We propose a pre-trained foundation model for smartwatch-based RR extraction and apply a transfer learning technique to enhance the generalizability of our method across different datasets. We test the proposed method using two public datasets (i.e., WESAD and PPG-DaLiA). The proposed method shows the Mean Absolute Error (MAE) of 2.29 and 3.09 and Root Mean Squared Errors (RMSE) of 3.11 and 3.79 across PPG-DaLiA and WESAD datasets, respectively. In contrast, the best results obtained by the existing methods are an MAE of 2.68, an RMSE of 3.5 for PPG-DaLiA, an MAE of 3.46, and an RMSE of 4.02 for WESAD datasets.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712280"}, {"primary_key": "60340", "vector": [], "sparse_vector": [], "title": "Watch Out! E-scooter Coming Through!: Multimodal Sensing of Mixed Traffic Use and Conflicts Through Riders&apos; Ego-centric Views.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "E-scooters are becoming a popular means of urban transportation. However, this increased popularity brings challenges, such as road accidents and conflicts when sharing space with traditional transport modes. An in-depth understanding of e-scooter rider behaviour is crucial for ensuring rider safety, guiding infrastructure planning, and enforcing traffic rules. In this paper, we investigated the riding behaviours of e-scooter users through a naturalistic study. We recruited 23 participants, equipped with a bike computer, eye-tracking glasses and cameras, who traversed a pre-determined route, enabling the collection of multi-modal data. We analysed and compared gaze movements, continuous speed, and video feeds across three different transport infrastructure types: a pedestrian-shared path, a cycle lane and a roadway. Our findings reveal that e-scooter riders face unique challenges, including difficulty keeping up with faster-moving cyclists and motor vehicles due to the capped speed limit on shared e-scooters, issues in safely signalling turns due to the risks of losing control when using hand signals, and limited acceptance from other road users in mixed-use spaces. Additionally, we observed that the cycle lane has the highest average speed, the least frequency of speed change points, and the least head movements, supporting the suitability of dedicated cycle lanes - separated from motor vehicles and pedestrians - for e-scooters. These findings are facilitated through multimodal sensing and analysing the e-scooter riders' ego-centric view, which show the efficacy of our method in discovering the behavioural dynamics of the riders in the wild. Our study highlights the critical need to align infrastructure with user behaviour to improve safety and emphasises the importance of targeted safety measures and regulations, especially when e-scooter riders share spaces with pedestrians or motor vehicles. The dataset and analysis code are available at https://github.com/HiruniNuwanthika/Electric-Scooter-Riders-Multi-Modal-Data-Analysis.git.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712284"}, {"primary_key": "60341", "vector": [], "sparse_vector": [], "title": "From Verbal Reports to Personalized Activity Trackers: Understanding the Challenges of Ground Truth Data Collection with Older Adults in the Wild.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tracking activities holds great potential to improve the well-being of older adults, yet the accuracy of activity trackers for this demographic remains in question. Evaluating this accuracy requires ground-truth data directly from older adults, which has largely been gathered in controlled laboratory settings or labeled by researchers. Moreover, considering the diversity in older adults' activity engagement and tracking preferences, personalized activity tracking appears necessary. We demonstrate that older adults can benefit from personalized activity trackers by showing that cadence thresholds for stepping intensities vary within this group. However, collecting ground-truth data from older adults in real-world settings poses unique challenges. This paper examines two sources of ground-truth labels for the smartwatch Inertial Measurement Unit (IMU) data collected with older adults. Using verbal self-reports and a thigh-worn activity tracker, we assess their viability as ground-truth sources in natural settings. Additionally, we evaluate the costs and benefits of triangulating these sources as a ground-truth proxy. Our findings reveal two main costs: data shrinkage and notable effort from both contributors and data stewards. Simultaneously, we observe improved data quality and a greater ability to identify error sources when evaluating a trained model.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3731749"}, {"primary_key": "60344", "vector": [], "sparse_vector": [], "title": "LEGO: Synthesizing IoT Device Components Based on Static Analysis and Large Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "IoT device components---digital representations of IoT devices within a platform and typically developed using Software Development Kits (SDKs)---are essential for ensuring seamless connectivity between IoT platforms and physical devices. However, developing these components demands extensive domain knowledge, as developers must understand the necessary elements of an IoT device and effectively utilize SDKs. Unfortunately, limited research has focused on automating this process, resulting in labor-intensive, time-consuming development. To tackle these challenges, we introduce LEGO, a method for synthesizing IoT device components based on the observation that APIs provided by device SDKs would eventually call network protocol methods to access physical devices. LEGO analyzes the SDK source code to identify candidate APIs that communicate with physical devices. Using static analysis, it generates a dataflow-enhanced call graph, extracts call paths containing network protocol methods, and heuristically identifies APIs that invoke these methods. To efficiently classify each API type and infer relevant device properties, LEGO employs a large language model-based program comprehension technique with an information-augmented prompt. LEGO then synthesizes device components using a platform-specific template, built from a common IoT device component model. It assembles IoT device components by populating the template with inferred properties and identified APIs, enabling developers to efficiently develop device components with minimal SDK knowledge. Comprehensive experiments on a set of open-source device SDKs and ten real-world IoT devices demonstrate the efficiency and effectiveness of LEGO in creating IoT device components.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729482"}, {"primary_key": "60345", "vector": [], "sparse_vector": [], "title": "LGL-BCI: A Motor-Imagery-Based Brain-Computer Interface with Geometric Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Yuzhe <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Brain-computer interfaces are groundbreaking technology whereby brain signals are used to control external devices. Despite some advances in recent years, electroencephalogram (EEG)-based motor-imagery tasks face challenges, such as amplitude and phase variability and complex spatial correlations, with a need for smaller models and faster inference. In this study, we develop a prototype, called the Lightweight Geometric Learning Brain-Computer Interface (LGL-BCI), which uses our customized geometric deep learning architecture for swift model inference without sacrificing accuracy. LGL-BCI contains an EEG channel selection module via a feature decomposition algorithm to reduce the dimensionality of a symmetric positive definite matrix, providing adaptiveness among the continuously changing EEG signal. Meanwhile, a built-in lossless transformation helps boost the inference speed. The performance of our solution was evaluated using two real-world EEG devices and two public EEG datasets. LGL-BCI demonstrated significant improvements, achieving an accuracy of 82.54% compared to 62.22% for the state-of-the-art approach. Furthermore, LGL-BCI uses fewer parameters (64.9Kvs. 183.7K), highlighting its computational efficiency. These findings underscore both the superior accuracy and computational efficiency of LGL-BCI, demonstrating the feasibility and robustness of geometric deep learning in motor-imagery brain-computer interface applications.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3699732"}, {"primary_key": "60346", "vector": [], "sparse_vector": [], "title": "UVX-ray: Urban Village Safety Risk Diagnosis Leveraging Multi-Source Urban Data.", "authors": ["<PERSON><PERSON>", "Junxiang Ji", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Urban villages are a unique phenomenon in the downtown segments of major cities in developing countries. Most of them are heavily populated, intensely constructed, and lack infrastructure, bringing potential safety risks to their residents. Therefore, diagnosing which risk factors in urban villages contribute to the increased risk incidence is crucial for urban authorities to better renovate and manage these areas. However, traditional approaches, such as fire and traffic investigations, are labor-intensive and time-consuming, making it challenging to diagnose risks timely. To address this problem, we propose a data-driven framework that leverages heterogeneous urban data to diagnose urban village safety risks through risk-level prediction and risk factor analysis. First, we propose a crowdsensing-based approach to discover urban village potential risk hotspots and then collect contextual data from multiple sources to represent them comprehensively. Second, we propose a multi-modal representation paradigm of urban village potential risk hotspots in a multi-view manner that utilizes pre-trained models for feature extraction to effectively retain information about risk events. Finally, we design an explainable risk diagnosing model that not only predicts the risk level but also automatically highlights salient features (e.g., overcrowded restaurants for high fire risk level). Experiments using real-world data collected from 125 urban villages in Xiamen show that our approach predicts the fire risk level and the traffic risk level with 89.9% and 89.4% accuracy, respectively. Moreover, relevant risk factors in urban villages can be automatically identified for in-depth analysis by our approach.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729477"}, {"primary_key": "60347", "vector": [], "sparse_vector": [], "title": "Design and Evaluation of a Power Wheelchair-based Self-tracking System to Prevent Pressure Ulcers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ross <PERSON>urgia", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Self-tracking technologies empower people to build self-knowledge and insights across many domains and individual user contexts. However, individuals with severe motor disabilities are largely excluded from personal informatics systems. To bridge this gap, we designed and developed a first-of-a-kind power wheelchair (PWC) based multi-modal self-tracking system to support individuals with a recent spinal cord injury to track their pressure reliefs---a very frequent self-care activity to prevent pressure ulcers. We deployed this system with nine inpatient participants of a rehabilitation hospital and qualitatively evaluated the feasibility through their interactions with audio, visual, and haptic reminder modalities through observations and interviews. Our deployment and evaluation demonstrate the feasibility of creating chairable self-tracking systems to help facilitate independence and self-awareness of their self-care activity and the potential for personal informatics systems to be effectively designed so that they are useful for this population.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712273"}, {"primary_key": "60349", "vector": [], "sparse_vector": [], "title": "Transient Authentication from First-Person-View Video.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose PassFrame, a system which utilizes first-person-view videos to generate personalized authentication challenges based on human episodic memory of event sequences. From the recorded videos, relevant (memorable) scenes are selected to form image-based authentication challenges. These authentication challenges are compatible with a variety of screen sizes and input modalities. As the popularity of using wearable cameras in daily life is increasing, PassFrame may serve as a convenient personalized authentication mechanism to screen-based appliances and services of a camera wearer. We evaluated the system in various settings including a spatially constrained scenario with 12 participants and a deployment on smartphones with 16 participants and more than 9 hours continuous video per participant. The authentication challenge completion time ranged from 2.1 to 9.7 seconds (average: 6 sec), which could facilitate a secure yet usable configuration of three consecutive challenges for each login. We investigated different versions of the challenges to obfuscate potential privacy leakage or ethical concerns with 27 participants. We also assessed the authentication schemes in the presence of informed adversaries, such as friends, colleagues or spouses and were able to detect attacks from diverging login behaviour.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712266"}, {"primary_key": "60351", "vector": [], "sparse_vector": [], "title": "A-UVI: GNSS-Assisted EO-based UV Index Estimation Method for Individual-level Precise UV Exposure Assessment.", "authors": ["<PERSON><PERSON>", "Subaru Atsumi", "Kota Tsubouchi", "<PERSON><PERSON><PERSON>"], "summary": "Excessive or insufficient exposure to ultraviolet (UV) light can have adverse effects on health, including the development of skin cancer, cataracts, and osteoporosis. An Earth observation (EO)-based UV index can estimate area-level UV indexes without effort in open-sky environments but can not provide sufficient accuracy for shaded environments. In contrast, conventional methods for monitoring individual-level, i.e., personal, UV exposure, such as mobile and wearable UV sensors, face limitations in terms of measurement and usability, presenting challenges for practical long-term usage. To address these issues, we introduce A-UVI, a method that enhances the accuracy of the EO-based UV index by leveraging raw signals from global navigation satellite systems (GNSS). By integrating this EO-based UV index and an attenuation ratio estimated from raw GNSS signals, our method especially improves estimation accuracy in shady environments affected by obstructions. We evaluated our method on data collected by different GNSS receivers in different mobility scenarios encompassing a diverse range of contexts and observation areas over the course of three days. Our evaluation showed that A-UVI estimates the UV index with a precision exceeding existing methods by at least 44.25%, achieving 5.53 times higher estimation accuracy in forest environments. We also confirmed that A-UVI is compatible with GNSS receivers in consumer-grade smartphones and has an average accuracy that is 23% better than the baseline EO-based method. Our findings demonstrate that utilizing raw GNSS signals enables accurate estimation of the UV index in various conditions, including in shaded areas, without the need for particular measurement actions or devices. This marks a significant advancement in enabling passive individual-level UV exposure monitoring and adaptive UV exposure management beyond simple exposure tracking.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729463"}, {"primary_key": "60353", "vector": [], "sparse_vector": [], "title": "Exploring Cultural and Intergenerational Dynamics in Voice Assistant Design for Chinese Older Adults.", "authors": ["<PERSON><PERSON><PERSON>", "Jiaojiao Fu", "<PERSON><PERSON>"], "summary": "In this study, we examined voice assistant (VA) use among Chinese older adults through interviews with 12 older adults and 6 of their adult children, as well as observations of VA use in solo and family settings. Our findings reveal that older adults are motivated to use VA for social interaction but encounter barriers due to limited cultural and linguistic customization, such as difficulty understanding regional dialects. Additionally, adult children play a dual role, providing necessary support while sometimes limiting independent use through overprotective tendencies. These results highlight the importance of designing VA with culturally responsive features and adaptable language models that consider the unique linguistic and social needs of older Chinese adults. This study contributes to the development of VA that balances autonomy and family support, enriching the technology's effectiveness for older adults in China and potentially in other similar cultural contexts.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712275"}, {"primary_key": "60354", "vector": [], "sparse_vector": [], "title": "CataractBot: An LLM-powered Expert-in-the-Loop Chatbot for Cataract Patients.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Satvik Golechha", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The healthcare landscape is evolving, with patients seeking reliable information about their health conditions and available treatment options. Despite the abundance of information sources, the digital age overwhelms individuals with excess, often inaccurate information. Patients primarily trust medical professionals, highlighting the need for expert-endorsed health information. However, increased patient loads on experts has led to reduced communication time, impacting information sharing. To address this gap, we developed CataractBot1. CataractBot answers cataract surgery related questions instantly using an LLM to query a curated knowledge base, and provides expert-verified responses asynchronously. It has multimodal and multilingual capabilities. In an in-the-wild deployment study with 49 patients and attendants, 4 doctors, and 2 patient coordinators, CataractBot demonstrated potential, providing anytime accessibility, saving time, accommodating diverse literacy levels, alleviating power differences, and adding a privacy layer between patients and doctors. Users reported that their trust in the system was established through expert verification. Broadly, our results could inform future work on expert-mediated LLM bots.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729479"}, {"primary_key": "60355", "vector": [], "sparse_vector": [], "title": "TxP: Reciprocal Generation of Ground Pressure Dynamics and Activity Descriptions for Improving Human Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Sensor-based human activity recognition (HAR) has predominantly focused on Inertial Measurement Units and vision data, often overlooking the capabilities unique to pressure sensors, which capture subtle body dynamics and shifts in the center of mass. Despite their potential for postural and balance-based activities, pressure sensors remain underutilized in the HAR domain due to limited datasets. To bridge this gap, we propose to exploit generative foundation models with pressure-specific HAR techniques. Specifically, we present a bidirectional TextxPressure model that uses generative foundation models to interpret pressure data as natural language. TxP accomplishes two tasks: (1) Text2Pressure, converting activity text descriptions into pressure sequences, and (2) Pressure2Text, generating activity descriptions and classifications from dynamic pressure maps. Leveraging pre-trained models like CLIP and LLaMA 2 13B Chat, TxP is trained on our synthetic PressLang dataset, containing over 81,100 text-pressure pairs. Validated on real-world data for activities such as yoga and daily tasks, TxP provides novel approaches to data augmentation and classification grounded in atomic actions. This consequently improved HAR performance by up to 12.4% in macro F1 score compared to the state-of-the-art, advancing pressure-based HAR with broader applications and deeper insights into human movement. The data and code will be available on TxP.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3732001"}, {"primary_key": "60357", "vector": [], "sparse_vector": [], "title": "OpenEarable 2.0: Open-Source Earphone Platform for Physiological Ear Sensing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Earphones have evolved from pure audio devices to \"earables\" that are capable of advanced sensing. Bespoke research devices have shown the unique sensing capabilities of the earable platform; however, they are hard to replicate and require expertise to develop in the first place. In this paper, we present OpenEarable 2.0 - an open source, unified platform that integrates a larger number of sensors for conducting comprehensive earable research. OpenEarable 2.0 works as regular binaural Bluetooth earphones and features two ultrasound capable microphones (inward/outward), a 3-axis ear canal accelerometer/bone microphone, a 9-axis head inertial measurement unit, pulse oximeter, optical temperature sensor, ear canal pressure sensor, and microSD card. These capabilities allow for the detection and measurement of 30+ phenomena on the ear that can be used across a wide range of applications in health monitoring, activity tracking, human-computer-interaction and authentication. We describe the design and development of OpenEarable 2.0 which follows best open hardware practices and achieves commercial-level wearability. We provide justification for the selection and placement of integrated sensors and include in-depth descriptions of the extensible, open source firmware and hardware that are implemented using free to use tools and frameworks. For real-time sensor control and data recording we also contribute a web-based dashboard and mobile smartphone app. The wearability and ability to sense different phenomena are validated in four studies which showcases how OpenEarable 2.0 provides accurate measurements in comparison to established gold-standard measurements. We further demonstrate that OpenEarable 2.0 can be assembled by inexperienced users, and that undergraduate students can build applications using the OpenEarable platform.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712069"}, {"primary_key": "60358", "vector": [], "sparse_vector": [], "title": "Slim-Sense: A Resource Efficient WiFi Sensing Framework towards Integrated Sensing and Communication.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the growing use cases of CSI-based WiFi sensing, future WiFi networks are moving towards integrating sensing and communication (ISAC) by sharing the same frequency resources between data communication and WiFi sensing. However, it is known that WiFi sensing is detrimental to WiFi communication due to its expensive use of frequency resources for collecting CSI samples, limiting its effectiveness in ISAC. To address this challenge, we propose Slim-Sense, a novel approach to resource saving while maximizing the sensing accuracy. We first demonstrate that it is possible to perform accurate WiFi sensing without using the entire bandwidth. In fact, we can obtain close to maximum accuracy while utilizing only 24.42% of the bandwidth and 25% of the antennas. Obtaining such accuracy at low bandwidth requires the selection of the antennas and bandwidth that are most relevant for sensing activities. One of Slim-Sense's highlights is using a novel approach consisting of a Sparse Group Regularizer (SGR) and Hierarchical Reinforcement learning (HRL) technique to select the minimum optimal bandwidth resources for sensing while maximizing sensing accuracy. Considering the stochastic nature of the sensing environment and the difference in requirements of different sensing applications, Slim-Sense provides an environment and application-specific bandwidth resources for sensing. We evaluate Slim-Sense with four different WiFi CSI datasets, each varying in sensing environment and application, including one we collected in 46 different environmental settings. The experimental evaluation shows that Slim-Sense saves up to 92.9% resources while incurring &lt; 5% reduction in sensing accuracy compared to using entire spectrum resources. We show that Slim-Sense is generalized to different environments and sensing models. Compared to the state-of-art solution, Slim-Sense outperforms and achieves a maximum improvement of 28.75% in resource-saving and 42.18% in sensing accuracy.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712271"}, {"primary_key": "60359", "vector": [], "sparse_vector": [], "title": "Multi-granularity Supervised Contrastive Learning with Online Adaptation for Contactless In-bed Posture Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In-bed postures offer valuable information about an individual's sleep quality and overall health conditions, particularly for patients with sleep apnea. However, current in-bed posture classification systems lack privacy-friendly and easy-to-install options. Furthermore, existing solutions do not consider variations between patients and are typically trained only once, neglecting the utilization of time consistency and unlabeled data from new patients. To address these limitations, this paper builds on a seismic sensor to introduce a novel sleep posture framework, which comprises two main components, namely, the Multi-Granularity Supervised Contrastive Learning (MGSCL) module and the ensemble Online Adaptation (oa) module. Unlike most existing contrastive learning frameworks that operate at the sample level, MGSCL leverages multi-granular information, operating not only at the sample level but also at the group level. The oa module enables the model to adapt to new patient data while ensuring time consistency in sleep posture predictions. Additionally, it quantifies model uncertainty to generate weighted predictions, further enhancing performance. Evaluated on a dataset of 100 patients collected at a clinical research center, MGSCLoa achieved an average accuracy of 91.67% and an average F1 score of 91.53% with only 40 seconds of labeled data per posture. In a Phase 2 evaluation with 11 participants over 13 nights in home settings, the framework reached an average accuracy of 85.37% and a weighted F1 score of 83.59% using just 3 minutes of labeled data per common posture for each participant. These results underscore the potential of seismic sensor-based in-bed posture classification for assessing sleep quality and related health conditions.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729464"}, {"primary_key": "60360", "vector": [], "sparse_vector": [], "title": "T-Oil: A Contactless Edible Oil Adulteration Perception Using Terahertz Signals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Huadong Ma", "<PERSON><PERSON>"], "summary": "Adulterated edible oil is a widespread issue due to limited production of high-cost oils, undermining fair competition in the food trade and posing health risks to consumers. Existing techniques for adulterated edible oil detection usually require trained professionals and specialized equipment, limiting their applicability in everyday situations. In this paper, we show that Terahertz (THz), a potential frequency band for emerging wireless communication technologies, offers molecular-level sensing capabilities. Leveraging this sensing capability, we propose T-Oil, a novel contactless system for detecting adulteration in edible oils. Specifically, T-Oil transmits a Terahertz signal to the sample being tested and analyzes changes in the absorption characteristics of the reflected signal to assess compositional differences, thereby identifying adulterated edible oils. In T-Oil, we design a two-stage deep learning model that employs contrast learning to identify the oil types present in an adulterated sample, and then apply a multi-source domain adaptation approach to quantify the proportions of the adulterants. With 25,950 signal samples collected, T-Oil can identify the oil types in the adulterated edible oil at an accuracy of 98.3% and quantify the detailed adulterated proportion at an accuracy of 96.4%.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729468"}, {"primary_key": "60361", "vector": [], "sparse_vector": [], "title": "Manipulation of Acoustic Focusing for Multi-target Sensing with Distributed Microphones in Smart Car Cabin.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Smart car cabins have drawn great attention in the past few years to enable a wide range of applications, including in-car entertainment, road noise cancellation, and voice control. These applications have been underpinned by the addition of a very large number of acoustic modules in the cabin, which has further opened up new opportunities for acoustic contactless sensing in smart cabins. Different from the traditional smartphone or smart speaker for acoustic sensing, in this paper, we envision to manipulate the inherently embedded speakers and microphones in car cabin to produce 3D spatial signal focusing for sensing. We propose the new concept of in-car occupancy grids, which enables target identification in 3D cabin space. In order to accomplish signal focusing in a small spot region, we establish the distributed acoustic 3D spatial beamforming model. To further address the phase ambiguity induced by large microphone spacing, we design dual frequency transmitted signals and \"virtually\" create a low-frequency signal to sense targets, which characterizes the highly sensitive and noise-free properties. We build a distributed acoustic sensing system in car and conduct both benchmark experiments and real-world application studies. The results demonstrate that the proposed system can be applied to various sensing tasks, which has superior performance in terms of accurate occupancy detection, multi-target sensing, very subtle motion detection, and interference resistance.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729470"}, {"primary_key": "60362", "vector": [], "sparse_vector": [], "title": "WULoc: Achieving Extremely Long-range High-precision Localization via Wi-Fi-UWB Connection.", "authors": ["Kai Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Wireless localization, especially using ubiquitous Wi-Fi devices, has been studied for decades. However, due to complex signal propagation, resolving multiple reflected paths and further achieving accurate localization face great challenges in the localization precision and localization range, which are caused by the limited bandwidth and sensitivity of Wi-Fi signals respectively. Straightforwardly solving these challenges by employing wide bandwidth Wi-Fi is only helpful for improving precision, while the localization range is still short due to Wi-Fi's intrinsic vulnerability to SNR drop. This paper presents Wi-Fi-UWB Localization (WULoc) to achieve both precise and long-range localization for Wi-Fi devices. Specifically, WULoc leverages the picosecond level timestamp information that is extracted from the links of the Wi-Fi device to multiple UWB anchors, innovatively created by the proposed Wi-Fi-UWB connection establishment, which overcomes the incompatibility between Wi-Fi and UWB physical layers. WULoc further calculates the TDoA of Wi-Fi-UWB links, from which the location of the Wi-Fi device is estimated. Extensive experiments show that WULoc locates a Wi-Fi device at a range of 240 meters with a precision of 5.3 meters.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712282"}, {"primary_key": "60363", "vector": [], "sparse_vector": [], "title": "Layout-Agnostic Human Activity Recognition in Smart Homes through Textual Descriptions Of Sensor Triggers (TDOST).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Human activity recognition (HAR) using ambient sensors in smart homes has numerous applications for human healthcare and wellness. However, building general-purpose HAR models that can be deployed to new smart home environments requires a significant amount of annotated sensor data and training overhead. Most smart homes vary significantly in their layouts, i.e., floor plans and the specifics of sensors embedded, resulting in low generalizability of HAR models trained for specific homes. We address this limitation by introducing a novel, layout-agnostic modeling approach for HAR systems in smart homes that utilizes the transferrable representational capacity of natural language descriptions of raw sensor data. To this end, we generate Textual Descriptions Of Sensor Triggers (TDOST) that encapsulate the surrounding trigger conditions and provide cues for underlying activities to the activity recognition models. Leveraging textual embeddings, rather than raw sensor data, we create activity recognition systems that predict standard activities across homes without (re-)training or adaptation to target homes. Through an extensive evaluation, we demonstrate the effectiveness of TDOST-based models in unseen smart homes through experiments on benchmark Orange4Home and CASAS datasets. Furthermore, we conduct a detailed analysis of how the individual components of our approach affect downstream activity recognition performance.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712278"}, {"primary_key": "60364", "vector": [], "sparse_vector": [], "title": "Everytime Everywhere All at Once: Enhancing Temporal-Spatial Traceability of Optical Codes through Voltmarks.", "authors": ["Jingyu Tong", "<PERSON><PERSON> Dai", "<PERSON>", "<PERSON><PERSON>"], "summary": "Due to the absence of spatial-temporal traceability, static optical codes present considerable security risks and are thus confined to limited applications (for instance, transactions using static QR codes for mobile payments are capped at 500 RMB or about 76 USD daily in China). While dynamic codes that update periodically can mitigate these issues, they necessitate additional hardware, such as screens and computing devices. To overcome these limitations, we introduce Voltmark, a novel approach that enhances the spatial-temporal traceability of static optical codes to the level of dynamic ones through the use of voltmarks---natural watermarks generated by AC flickering during image capture indoors. Voltmarks carry unique temporal and spatial characteristics of the AC and bulbs, allowing for the verification of the time and location where code photographs were taken. This functionality effectively thwarts various security threats and fraudulent activities. Extensive evaluation demonstrates that Voltmark maintains an average accuracy of 93.3% in spatial tracing and delivers a time resolution of 42ms (equivalent to scanning at 24 fps). These outcomes solidly establish Voltmark as a reliable technique for boosting the security of optical codes.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729500"}, {"primary_key": "60365", "vector": [], "sparse_vector": [], "title": "mmHSE: Enhanced Eavesdropping Attack on Headsets Leveraging COTS mmWave Radar.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent advancements in headset technology, coupled with the rising demand for secure, high-quality audio, have made headsets essential in both professional and personal contexts. However, emerging research highlights the vulnerability of these devices to sophisticated eavesdropping attacks using mmWave signals, where subtle diaphragm vibrations can unintentionally reveal playback speech content. Current eavesdropping methods face challenges, including low accuracy in modeling vibration signals, insufficient resolution in capturing diaphragm vibrations, and audio artifacts from nonlinear headset interactions, with FFT-based techniques further limited by resolution. To overcome these limitations, we propose the prototype system, namly mmHSE, which features two key innovations. First, we propose the Spatio-Temporal Segmented Fitting (STSF) method, which enhances signal clarity by isolating primary diaphragm vibrations through spatiotemporal signal segmentation. Second, we devise a two-stage integrated approach combining Minimum Variance Distortionless Response (MVDR) beamforming with the Chirp-Z Transform (CZT), achieving a resolution of 0.04 cm and significantly enhancing vibration capture precision. Additionally, mmHSE incorporates a conditional Generative Adversarial Network (cGAN) to refine Mel Spectrograms, enabling effective speech recovery. Experimental results demonstrate that mmHSE outperforms existing methods, with a 15.4% increase in Peak Signal-to-Noise Ratio (PSNR), an 11.4% reduction in Mel Cepstral Distortion (MCD), a 14.5% improvement in Word Error Rate (WER), and approximately a 15.1% increase in Likert Score, confirming its effectiveness in enhancing eavesdropping accuracy on headsets.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729475"}, {"primary_key": "60366", "vector": [], "sparse_vector": [], "title": "ARive: Assisting Drivers with In-Car Augmented Reality for Risk Zone Detection.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Human factors such as fatigue and distraction often impair drivers' ability to gauge traffic dynamics, leading to collisions, especially at unsignalized intersections. Augmented reality (AR) technology, particularly through advanced 3D projections and wearable head-mounted displays (HMDs), offers a promising enhancement by integrating comprehensive environmental awareness directly into the driver's field of view. This paper presents \"ARive,\" an innovative AR driver-assistance system designed to improve road safety by projecting dynamic risk zones beneath other traffic participants, thus providing real-time kinematic information to promote safer driving distances and informed decision-making. The research involved developing two distinct AR designs and testing them using a fixed-base driving simulator with integrated real-time data communication. A user study with 17 participants revealed that while AR projections significantly improve distance maintenance, particularly in abrupt braking scenarios, they do not markedly affect brake response times or enhance safety during critical events. These findings suggest the need for further optimization of AR design elements to maximize effectiveness, highlighting the potential of AR in enhancing driver awareness and safety.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712270"}, {"primary_key": "60367", "vector": [], "sparse_vector": [], "title": "S2Pair: Secure and Simple Device Pairing Using Human Bone as a Key Generator.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Gu"], "summary": "Secure pairing is an essential measure to ensure user privacy and data security on smart devices. Traditional pairing approaches predominantly rely on manual interactions, which may be cumbersome, susceptible to human error, and often problematic to deploy on devices with poor user interfaces. Despite prior attempts to mitigate these shortcomings, their efficacy has been impeded by the complexities in key generation or reliance on wireless channels for key transmission, leaving them vulnerable to potential man-in-the-middle (MITM) attacks. Therefore, the development of a pairing approach that focuses on security with user-friendly characteristics is important. In this paper, we introduce a novel paradigm that harnesses human bones to generate and transmit pairing keys derived from ambient sound. It spontaneously generates keys using the interference of vibrations passing through the bone, eliminating the complex process of key generation. We explore the unique characteristics of bones as a key generator and develop a quantitative model of this process for key confirmation. Our approach also enhances security by avoiding public wireless links for key transmission. We design a pairing protocol and build a prototype system using off-the-shelf sensors. Extensive experiments involving 44 participants demonstrate that our system has a true acceptance rate of 96.64% and an equal error rate of 1.96%, while also being resilient to various attacks.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729466"}, {"primary_key": "60368", "vector": [], "sparse_vector": [], "title": "RF-AE: Single-site Arterial Elasticity Estimation Using UWB Signals.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Arterial elasticity is the capability of the arteries to dilate and constrict in response to fluctuations in blood pressure as the heart circulates blood throughout the body, serving as an important indicator for assessing arterial health. However, conventional medical detection methods are often complex, costly, and require direct contact between the user's skin and the device, making them impractical for regular monitoring. In this paper, we propose a contactless single-site arterial elasticity assessment approach using ultra-wideband (UWB) signals, where we estimate the brachial-ankle pulse wave velocity, a key indicator of arterial health, by analyzing the UWB signals reflected off the back of a subject sitting still. Specifically, we first propose preprocessing methods to reduce noise in the received UWB signals, and then design a deep generative adversarial network to generate pulse wave signals from these UWB signals. The generated signals are of high quality and fidelity, exhibiting characteristics of vasoconstriction and vasodilation. Further, we analyze the pulse wave and extract the features related to arterial elasticity from the generated pulse waves. Finally, we employ random forest regression to predict pulse transit time and a body-height based method to estimate the length of the pulse transit path, so as to achieve single-site arterial elasticity assessment. We build the corresponding system named RF-AE and conduct extensive experiments to evaluate its performance. The experimental results show that RF-AE can accurately predict the arterial elasticity of users.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729460"}, {"primary_key": "60369", "vector": [], "sparse_vector": [], "title": "MIMIC: AI and AR-enhanced Multi-Modal, Immersive, Relative Instruction Comprehension.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a multimodal instruction comprehension framework, called MImIC, that utilizes visual sensing (including LIDAR and 2D RGB sensing) &amp; AI spatial reasoning capabilities to support more seamless and immersive interaction between humans and AI-driven situated assistive agents. MImIC's key new capability is to support disambiguation of a wider set of relative spatial references that users naturally employ while issuing spatially-situated instructions. To support enhanced visual grounding via a combination of both fully-qualified and relative attribute references, MImIC uses (a) a fine-tuned transformer-based language translation DNN to accurately convert natural verbal commands into a structured set of machine understandable constraints (BLEU score=92.5), (b) a set of modules that use RGB+LIDAR sensing data to convert any relative attribute preferences to fully-qualified attribute constraints (median height/width estimation errors &lt;=2cm), and (c) an enhanced image segmentation DNN, augmented with gesture+verbal cues, to extract target objects of interest (top-1 accuracy= ~85%). To demonstrate the viability and superiority of MImIC, we implement an exemplar AR-augmented, immersive furniture shopping application, called AIRFurn. AIRFurn allows users to browse for, select and overlay furniture items of interest using natural multi-modal and relative cues. experimental studies, using 34 &amp; 11 users over 8 different layouts in a lab setting and 11 users in 6 different real-world home setups, show that AIRFurn offers superior performance, with significantly (~3x) lower task completion times, much higher task (17%+) accuracy and greater user satisfaction (SUS score= 78.8) compared to baselines where users perform selection using only fully-qualified verbal commands or manipulation of AR interfaces.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712268"}, {"primary_key": "60370", "vector": [], "sparse_vector": [], "title": "&quot;I would still use it but I wouldn&apos;t trust it&quot;: Evaluating Mechanisms for Transparency and Control for Smart-Home Sensors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Smart-home devices, such as smart speakers and cameras, provide convenient home automation and media control, but the sensors that continuously collect data in users' homes create privacy concerns. To attempt to increase trust, device manufacturers and researchers have developed privacy features, such as indicator lights, hardware controls, and microphone jammers. To inform the design of more trustworthy products, we conducted a 489-participant online survey to understand how device type, brand, and privacy features impact trust. Our survey also examined whether providing more information about privacy features' limitations changed participants' perceptions. Contrary to our expectations, device brand did not significantly impact trust. Hardware mute controls were most effective at increasing trust. Participants expressed high intent to use familiar software-backed features, while expressing reservations about novel features proposed by researchers (e.g., jamming devices). Participants' reactions after seeing information about privacy features' limitations varied by feature, suggesting that the features' strengths and weaknesses are not equally well-understood. Based on our findings, we make several recommendations, including that device manufacturers and researchers explore making software-backed features more secure, as our results suggest that users may use those features even if they do not consider them reliable or trustworthy.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729480"}, {"primary_key": "60371", "vector": [], "sparse_vector": [], "title": "HandID: Towards Unobtrusive Gesture-independent User Authentication on Smartphones Using Vibration-based Hand Biometrics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Biometric authentication is pivotal in protecting user privacy and ensuring smartphone security. Recent research indicates that the vibration response of hands can serve as a biometric for user authentication on mobile devices. However, the various grip gestures of users can introduce significant noise, disrupting hand biometrics-related signals and compromising authentication performance. This paper introduces HandID, an unobtrusive and gesture-independent user authentication method for smartphones that does not require large amounts of data from different gestures. Unlike existing methods, HandID does not require users to maintain specific gestures or interact with the touchscreen. It utilizes the built-in vibration motor to generate active vibrations and sense the user's hand through responses captured by the onboard accelerometer. HandID employs an adversarial learning model to handle gesture variations and proposes a novel hand biometric generation model to reduce the enrollment data required from users. Comprehensive experiments with 50 subjects show that HandID achieves an authentication accuracy of 92.5%, with a false acceptance rate (FAR) of 5.2% and a false rejection rate (FRR) of 5.6%. Security analyses demonstrate that HandID is resistant to zero-effort, s tatistical, a nd h ill-climbing a ttacks, a nd a u sability study indicates high user acceptance of HandID.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729489"}, {"primary_key": "60372", "vector": [], "sparse_vector": [], "title": "Deconfounding Causal Inference through Two-branch Framework with Early-forking for Sensor-based Cross-domain Activity Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dongzhou Cheng", "<PERSON><PERSON>"], "summary": "Recently, domain generalization (DG) has emerged as a promising solution to mitigate distribution-shift issue in sensor-based human activity recognition (HAR) scenario. However, most existing DG-based works have merely focused on modeling statistical dependence between sensor data and activity labels, neglecting the importance of intrinsic casual mechanism. Intuitively, every sensor input can be viewed as a mixture of causal (category-aware) and non-causal factors (domain-specific), where only the former affects activity classification judgment. In this paper, by casting such DG-based HAR as a casual inference problem, we propose a causality-inspired representation learning algorithm for cross-domain activity recognition. To this end, an early-forking two-branch framework is designed, where two separate branches are respectively responsible for learning casual and non-causal features, while an independence-based Hilbert-Schmidt Information Criterion is employed to implicitly disentangling them. Additionally, an inhomogeneous domain sampling strategy is designed to enhance disentanglement, while a category-aware domain perturbation layer is performed to prevent representation collapse. Extensive experiments on several public HAR benchmarks demonstrate that our causality-inspired approach significantly outperforms eleven related state-of-the-art baselines under cross-person, cross-dataset, and cross-position settings. Detailed ablation and visualizations analyses reveal underlying casual mechanism, indicating its effectiveness, efficiency, and universality in cross-domain activity recognition scenario.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729495"}, {"primary_key": "60373", "vector": [], "sparse_vector": [], "title": "Combating Chirp Interference for Multi-target LoRa Localization.", "authors": ["<PERSON><PERSON> Xu", "<PERSON><PERSON>", "Xianjin Xia", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The long-range and low-power properties of LoRa facilitate its rapid deployment in many location-based services. However, existing LoRa-based localization techniques assume that the received signal is solely from a single node, without any concurrent transmissions from other LoRa nodes. This is because concurrent transmissions lead to mutual interference that inevitably distorts the estimated channel state information (CSI), resulting in significant localization errors. Although interference caused by concurrent transmissions has been studied and addressed in LoRa communication, none of these methods are effective for LoRa localization. This is because localization relies on distinct features and encounters different challenges compared to LoRa communication. To address this fundamental limitation, we propose CLoc, the first LoRa-based multi-target localization method, which is capable of localizing multiple LoRa nodes simultaneously under concurrent transmissions. Through comprehensive analysis, CLoc classifies the interference into two categories based on the chirp slope, i.e., inter-slope interference (different slopes) and co-slope interference (same slope), and identifies their fundamental impacts on CSI errors. CLoc designs dedicated methods that smartly leverage LoRa chirp characteristics to address CSI distortion caused by inter-slope interference, and tackle CSI ambiguity and errors caused by co-slope interference, thereby enabling accurate CSI estimation. We implement the prototype of CLoc with USRP B210 and commodity LoRa nodes. Evaluations under different settings demonstrate that CLoc achieves median localization errors of 3.3 m in a 293,250 m2 outdoor area and 3.5 m in a 6,750 m2 indoor area, reducing the localization errors by up to 90.6% compared with the state-of-the-art single LoRa node localization method.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729491"}, {"primary_key": "60377", "vector": [], "sparse_vector": [], "title": "From Spatial Domain to Temporal Domain: Unleashing the Capability of CFAR for mmWave Point Cloud Generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Point clouds are a crucial data type for mmWave radar and are widely used in sensing applications such as human tracking and activity recognition. However, for indoor human sensing, the point clouds obtained by mmWave radar are often sparse. Previous studies attribute the sparsity to the limited sensing capabilities of mmWave radar, underestimating the impact of CFAR---a key algorithmic component of mmWave systems---on point cloud quality. Through empirical studies, we find that the spatial-based CFAR widely used in existing works suffers from a severe energy masking issue. This is because these algorithms work well when the target is far away enough to be approximated as a point. In short-range indoor sensing, the human body can not be considered as a point but an extended target, causing the spatial-based CFAR to calculate the noise power wrongly and accordingly a miss-generation of the point cloud. To fundamentally solve the problem, this paper proposes a temporal-based CFAR named ETCM-CFAR. We address multiple issues such as lacking initial noise power and the absence of a closed-form threshold solution to make the proposed algorithm work. Based on ETCM-CFAR, this paper proposes a point cloud generation system named mmPC. mmPC is implemented on three different types of commercial-off-the-shelf mmWave radars and extensive experiments demonstrate that mmPC significantly improves point cloud quality, increasing the number of cloud points by 148.6% compared to the state-of-the-art systems. Two representative sensing applications, i.e., fitness activity recognition and human-pet classification are further employed to demonstrate the effectiveness of mmPC on sensing.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729465"}, {"primary_key": "60378", "vector": [], "sparse_vector": [], "title": "USpeech: Ultrasound-Enhanced Speech with Minimal Human Effort via Cross-Modal Synthesis.", "authors": ["<PERSON>-<PERSON>", "Running <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Speech enhancement is crucial for ubiquitous human-computer interaction. Recently, ultrasound-based acoustic sensing has emerged as an attractive choice for speech enhancement because of its superior ubiquity and performance. However, due to inevitable interference from unexpected and unintended sources during audio-ultrasound data acquisition, existing solutions rely heavily on human effort for data collection and processing. This leads to significant data scarcity that limits the full potential of ultrasound-based speech enhancement. To address this, we propose USPEECH, a cross-modal ultrasound synthesis framework for speech enhancement with minimal human effort. At its core is a two-stage framework that establishes the correspondence between visual and ultrasonic modalities by leveraging audio as a bridge. This approach overcomes challenges from the lack of paired video-ultrasound datasets and the inherent heterogeneity between video and ultrasound data. Our framework incorporates contrastive video-audio pre-training to project modalities into a shared semantic space and employs an audio-ultrasound encoder-decoder for ultrasound synthesis. We then present a speech enhancement network that enhances speech in the time-frequency domain and recovers the clean speech waveform via a neural vocoder. Comprehensive experiments show USpeech achieves remarkable performance using synthetic ultrasound data comparable to physical data, outperforming state-of-the-art ultrasound-based speech enhancement baselines. USPEECH is open-sourced at https://github.com/aiot-lab/USpeech/.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729462"}, {"primary_key": "60379", "vector": [], "sparse_vector": [], "title": "SeamFit: Towards Practical Smart Clothing for Automatic Exercise Logging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Smart clothing has exhibited impressive body pose/movement tracking capabilities while preserving the soft, comfortable, and familiar nature of clothing. For practical everyday use, smart clothing should (1) be available in a range of sizes to accommodate different fit preferences, and (2) be washable to allow repeated use. In SeamFit, we demonstrate washable T-shirts, embedded with capacitive seam electrodes, available in three different sizes, for exercise logging. Our T-shirt design, customized signal processing &amp; machine learning pipeline allow the SeamFit system to generalize across users, fits, and wash cycles. Prior wearable exercise logging solutions, which often attach a miniaturized sensor to a body location, struggle to track exercises that mainly involve other body parts. SeamFit T-shirt naturally covers a large area of the body and still tracks exercises that mainly involve uncovered joints (e.g., elbows and the lower body). In a user study with 15 participants performing 14 exercises, SeamFit detects exercises with an accuracy of 89%, classifies exercises with an accuracy of 93.4%, and counts exercises with an error of 0.9 counts, on average. SeamFit is a step towards practical smart clothing for everyday uses.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712287"}, {"primary_key": "60380", "vector": [], "sparse_vector": [], "title": "WindDancer: Understanding Acoustic Sensing under Ambient Airflow.", "authors": ["<PERSON>ang <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Acoustic sensing has recently garnered significant interest for a wide range of applications ranging from motion tracking to health monitoring. However, prior works overlooked an important real-world factor affecting acoustic sensing systems---the instability of the propagation medium due to ambient airflow. Airflow introduces rapid and random fluctuations in the speed of sound, leading to performance degradation in acoustic sensing tasks. This paper presents WindDancer, the first comprehensive framework to understand how ambient airflow influences existing acoustic sensing systems, as well as provides solutions to enhance systems performance in the presence of airflow. Specifically, our work includes a mechanistic understanding of airflow interference, modeling of sound speed variations, and analysis of how several key real-world factors interact with airflow. Furthermore, we provide practical recommendations and signal processing solutions to improve the resilience of acoustic sensing systems for real-world deployment. We envision that WindDancer establishes a theoretical foundation for understanding the impact of airflow on acoustic sensing, and advances the reliability of acoustic sensing technologies for broader adoption.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729469"}, {"primary_key": "60381", "vector": [], "sparse_vector": [], "title": "TIEboard: A Digital Educational Tool for Kids Geometric Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Tangible User Interfaces have shown potential in supporting the acquisition of key concepts in computing and mathematics while fostering engagement in young learners, but these approaches are less commonly utilised in the context of geometry. In this paper we introduce TIEboard, an interactive device to promote early learning of basic geometry concepts. TIEboard draws inspiration from traditional geoboards and lacing toys to leverage children's familiarity with these traditional tools. It employs instructional lights to guide children in creating shapes using colourful threads of optical fiber. The use of conductive materials allows the system to detect lacing activity and provide feedback in real-time. TIEboard incorporates six interaction modes of varying difficulty based on an incremental learning framework. The study evaluated TIEboard's effectiveness in supporting early geometric learning, facilitating creativity and promoting collaboration among 16 children aged 5-9.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729478"}, {"primary_key": "60382", "vector": [], "sparse_vector": [], "title": "WearSE: Enabling Streaming Speech Enhancement on Eyewear Using Acoustic Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Smart eyewear has rapidly evolved in recent years, yet its mobile and in-the-wild characteristics often make voice interactions on such devices susceptible to external interferences. In this paper, we introduce WearSE, a system that utilizes acoustic signals emitted and received by speakers and microphones mounted on eyewear to perceive facial movements during speech, achieving multimodal speech enhancement. WearSE incorporates three key designs to meet the high demands for real-time operation and robustness on smart eyewear. First, considering the frequent use in mobile scenarios, we design a sensing-enhanced network to amplify the capability of acoustic sensing, eliminating dynamic multipath interferences. Second, we develop a lightweight speech enhancement network that enhances both the amplitude and phase of the speech spectrum. Through a casual network design, computational demands are significantly reduced, ensuring real-time operation on mobile devices. Third, addressing the scarcity of paired data, we design a memory-based back-translation mechanism to generate pseudo-acoustic sensing data using a large amount of publicly available speech data for network training. We construct a prototype system and extensively evaluate WearSE through experiments. In multi-speaker scenarios, our approach exhibits much better performance than pure audio speech enhancement methods. Comparisons with commercial smart eyewear also demonstrate that WearSE significantly surpasses existing noise reduction algorithms in these devices. The audio demo of WearSE is available on https://github.com/WearSE/wearse.github.io.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712288"}, {"primary_key": "60384", "vector": [], "sparse_vector": [], "title": "Modeling Mouse-based Pointing and Steering Tasks for People with Parkinson&apos;s Disease.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mouse-based pointing and steering tasks are two primary interaction actions on computer devices. In this research, we created models for mouse-based pointing and steering tasks for people with Parkinson's Disease (PD); second, we detected PD via pointing and steering actions. The data collected from 24 participants (12 PD patients and 12 age-matched non-PD people) revealed that those with PD showed significant differences in their interaction patterns, characterized by slower movement times (MT), higher error rates, and lower information throughput compared to non-PD people. Leveraging this insight, we proposed a CNN-Transformer-based neural network model adept at PD detection, which demonstrated high accuracy in a leave-one-user-out validation. Combining pointing and steering datasets, it reached 0.96 for both AUC and F1-score. When only 10 pointing and steering actions of a user were observed, it reached an AUC of 0.99 and an F1-score of 0.96. Overall, our research contributes mouse-based pointing and steering models for PD users and provides CNN-Transformer models and computer interfaces for convenient PD detection.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3712267"}, {"primary_key": "60385", "vector": [], "sparse_vector": [], "title": "mmMulti: Multi-person Action Recognition Based on Multi-task Learning Using Millimeter Waves.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Action recognition using millimeter waves (mmWave) has shown great potential in various fields. However, the state-of-the-art still falls short in terms of multi-person action recognition inclusive interactive action recognition. In this paper, we propose mmMulti, a multi-person action recognition method based on multi-task learning using millimeter waves. To this end, we first segregate the mmWave data and assign them to each of multiple persons, and propose two new input data---compressed Doppler map (CDM) and point trajectory segments (PTS)---to represent the patterns and sequential characteristics of actions. Next, we leverage ConvNeXt to extract pattern features from CDM and leverage Transformer to extract sequential features from PTS, and fuse them by a cross-attention mechanism. Finally, we custom-design a multi-task learning model to recognize independent and interactive actions from multiple concurrent persons, enabling mmMulti to recognize single-person actions, multi-person independent actions and multi-person interactive actions. We implement mmMulti on a commercial mmWave radar and conduct extensive experiments. mmMulti achieves single-person action recognition accuracy of 99.64%, independent action recognition accuracy of 91.03% for two persons, 72.38% for three persons, 64.75% for four persons, and interactive action recognition accuracy of 100% for two persons. To the best of our knowledge, mmMulti is the first work in the field of mmWave sensing to differentiate both independent and interactive actions in multi-person scenarios, based on a multi-task learning model to accomplish multiple tasks simultaneously.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729461"}, {"primary_key": "60386", "vector": [], "sparse_vector": [], "title": "Large-Scale Indoor Localization via Outdoor Crowdsourcing Trajectories on Ride-Hailing Platform.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Zhaobing Han", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Indoor positioning is critical for a variety of services, including ride-hailing. However, existing large-scale fingerprint-based indoor positioning systems face significant challenges due to high deployment costs, temporal instability and limited accessibility, making them impractical for widespread use. In this paper, we propose a novel approach to indoor positioning that leverages fingerprints only sampled outdoors, which can be collected through crowdsourcing within a ride-hailing platform. This approach significantly reduces deployment costs, enables timely updates to the fingerprint set, and provides unprecedented accessibility. We address three key challenges in this system, including using outdoor fingerprints to estimate indoor position, abnormal Access Points (APs), and existence of \"blackholes\" where overheard APs have no fingerprint. Our implementation, built on the DiDi ride-hailing platform, is evaluated through extensive experiments with 122 million orders across 13 million devices in multiple cities. The results demonstrate that our system achieves a significant reduction of 4.35m in pickup position error compared to existing efforts, showcasing its potential for large-scale adoption.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729498"}, {"primary_key": "60387", "vector": [], "sparse_vector": [], "title": "RingBP: Towards Continuous, Comfortable, and Generalized Blood Pressure Monitoring Using a Smart Ring.", "authors": ["Guanzhou Zhu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Huadong Ma", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The utilization of wearable devices with integrated photoplethysmography (PPG) sensors for continuous and comfortable Arterial Blood Pressure (ABP) monitoring has gained popularity. However, our analysis on a large-scale self-collected dataset reveals a significant challenge: approximately 56.7% of individuals experience the absence of the dicrotic notch (a vital morphology feature) in wrist-PPG data, while 23.8% face the same issue in finger-PPG data, which poses a big obstacle to generalized ABP monitoring. To address this challenge, we propose a morphology-independent ABP estimation algorithm based on the Frank-Starling law. We also devise techniques to address calibration and PPG data drift issues commonly faced in continuous ABP monitoring. Further, a system called RingBP is developed towards continuous, comfortable, and generalized ABP monitoring using a smart ring. We design and implement a smart ring prototype and conduct extensive experiments involving 85 participants, demonstrating its superiority over other solutions.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3731600"}, {"primary_key": "60388", "vector": [], "sparse_vector": [], "title": "Gesture Builder: Flexible Gesture Customization and Efficient Recognition on VR Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the realm of VR/MR interactions, gestures serve as a critical bridge between users and interfaces, with custom gestures enhancing creativity and providing a personalized immersive experience. We introduce a novel gesture definition and recognition framework that allows users to customize a wide array of gestures by demonstrating them just three times. A major challenge lies in effectively representing gestures computationally. To address this, we have pre-trained a hand posture representation model using a Vector Quantized Variational Autoencoder (VQ-VAE) with a codebook of adaptive size, allowing hand postures defined by 23 joint positions of the hand to be projected into a latent space. In this space, different postures are formed into clusters, and a testing posture can be assigned to a cluster by a specific distance metric. The dynamic gestures are then represented as sequences of discrete hand postures and wrist positions. Employing a straightforward sequence matching algorithm, our framework achieves highly efficient recognition with minimal computational demands. We evaluated this system through a user study that includes 16 pre-defined gestures and 106 user-defined gestures. The results confirm that our system can provide robust real-time gesture recognition and effectively supports the customization of gestures according to user preferences. Our approach surpasses previous methods by enhancing gesture diversity and reducing constraints on gesture customization. Project page: https://iscas3dv.github.io/GestureBuilder/.", "published": "2025-01-01", "category": "ubicomp", "pdf_url": "", "sub_summary": "", "source": "ubicomp", "doi": "10.1145/3729484"}]