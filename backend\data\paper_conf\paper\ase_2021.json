[{"primary_key": "2203314", "vector": [], "sparse_vector": [], "title": "Empowering Web Applications with WebAssembly: Are We There Yet?", "authors": ["<PERSON><PERSON>"], "summary": "WebAssembly is the newest web standard. It defines a compact bytecode format that allows it to be loaded and executed fast. While WebAssembly is generally believed to be faster than JavaScript, there have been inconsistent results when it comes to showing which code is faster. Unfortunately, insufficient study has been conducted to understand the performance benefits of WebAssembly. In this paper, we investigate how browser engines optimize WebAssembly execution in comparison to JavaScript. In particular, we measure their execution time and memory usage with diverse programs. Our results show that (1) JIT optimization in Chrome significantly impacts JavaScript speed but has no discernible effect on WebAssembly speed; (2) WebAssembly uses much more memory than JavaScript. We hope that our findings can help WebAssembly virtual machine developers uncover optimization opportunities.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678831"}, {"primary_key": "2203316", "vector": [], "sparse_vector": [], "title": "SMARTIAN: Enhancing Smart Contract Fuzzing with Static and Dynamic Data-Flow Analyses.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Unlike traditional software, smart contracts have the unique organization in which a sequence of transactions shares persistent states. Unfortunately, such a characteristic makes it difficult for existing fuzzers to find out critical transaction sequences. To tackle this challenge, we employ both static and dynamic analyses for fuzzing smart contracts. First, we statically analyze smart contract bytecodes to predict which transaction sequences will lead to effective testing, and figure out if there is a certain constraint that each transaction should satisfy. Such information is then passed to the fuzzing phase and used to construct an initial seed corpus. During a fuzzing campaign, we perform a lightweight dynamic data-flow analysis to collect data-flow-based feedback to effectively guide fuzzing. We implement our ideas on a practical open-source fuzzer, named SMARTIAN. SMARTIAN can discover bugs in real-world smart contracts without the need for the source code. Our experimental results show that SMARTIAN is more effective than existing state-of-the-art tools in finding known CVEs from real-world contracts. SMARTIAN also outperforms other tools in terms of code coverage.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678888"}, {"primary_key": "2203317", "vector": [], "sparse_vector": [], "title": "Shake Those System Parameters! On the Need for Parameter Coverage for Decision Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Decision systems such as Multiple-Criteria Decision Analysis systems formulate a decision process in terms of a mathematical function that takes into consideration different aspects of a problem. Testing such systems is crucial, as they are usually employed in safety-critical systems. A good test suite for these systems should be able to exercise all the possible types of decisions that can be taken by the system. Classic structural coverage criteria do not provide good test suites in this sense, as they can be fulfilled by simple tests that only cover one possible type of decision. Thus, in this paper we discuss the need for tailored coverage criteria for this class of systems, and we propose a criterion based on the perturbation of the decision systems' parameters. We demonstrate the effectiveness of the criterion, compared to classic structural coverage criteria, on a path planner system for autonomous driving. We also discuss other benefits, such as the criterion helping explain why a decision was made during a test.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678593"}, {"primary_key": "2203318", "vector": [], "sparse_vector": [], "title": "Scalable Fuzzing of Program Binaries with E9AFL.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Greybox fuzzing is an effective method for software testing. Greybox fuzzers, such as AFL, use instrumentation that collects path coverage information in order to guide the fuzzing process. The instrumentation is usually inserted by a modified compiler toolchain, meaning that the program must be recompiled in order to be compatible with greybox fuzzing. When source code is unavailable, or for projects with complex build systems, recompilation is not always feasible. In this paper, we present E9AFL, a fast and scalable tool that automatically inserts AFL instrumentation to program binaries. E9AFL is built on top of the E9Patch static binary rewriting tool. To combat the overhead caused by binary instrumentation, E9AFL develops a set of optimization strategies. Our evaluation results show that E9AFL outperforms existing binary instrumentation tools and achieves comparable performance with the compile time instrumentation.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678913"}, {"primary_key": "2203319", "vector": [], "sparse_vector": [], "title": "Intelligent Change Operators for Multi-Objective Refactoring.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Thiago do Nascimento Ferreira", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose intelligent change operators and integrate them into an evolutionary multi-objective search algorithm to recommend valid refactorings that address conflicting quality objectives such as understandability and effectiveness. The proposed intelligent crossover and mutation operators incorporate refactoring dependencies to avoid creating invalid refactorings or invalidating existing refactorings. Further, the intelligent crossover operator is augmented to create offspring that improve solution quality by exchanging blocks of valid refactorings that improve a solution's weakest objectives. We used our intelligent change operators to generate refactoring recommendations for four widely used open-source projects. The results show that our intelligent change operators improve the diversity of solutions. Diversity is important in genetic algorithms because crossing over a homogeneous population does not yield new solutions. Given the inherent nature of design trade-offs in software, giving developers choices that reflect these trade-offs is important. Higher diversity makes better use of developers time than lots of incredibly similar solutions. Our intelligent change operators also accelerate solution convergence to a feasible solution that optimizes the trade-off between the conflicting quality objectives. Finally, they reduce the number of invalid refactorings by up to 71.52% compared to existing search-based refactoring approaches, and increase the quality of the solutions. Our approach outperformed the state-of-the-art search-based refactoring approaches and an existing deterministic refactoring tool based on manual validation by developers with an average manual correctness, precision and recall of 0.89, 0.82, and 0.87.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678519"}, {"primary_key": "2203320", "vector": [], "sparse_vector": [], "title": "Reducing Time-To-Fix For Fuzzer Bugs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At Google, fuzzing C/C++ libraries has discovered tens of thousands of security and robustness bugs. However, these bugs are often reported much after they were introduced. Developers are provided only with fault-inducing test inputs and replication instructions that highlight a crash, but additional debugging information may be needed to localize the cause of the bug. Hence, developers need to spend substantial time debugging the code and identifying commits that introduced the bug. In this paper, we discuss our experience with automating a fuzzing-enabled bisection that pinpoints the commit in which the crash first manifests itself. This ultimately reduces the time critical bugs stay open in our code base. We report on our experience over the past year, which shows that developers fix bugs on average 2.23 times faster when aided by this automated analysis.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678606"}, {"primary_key": "2203321", "vector": [], "sparse_vector": [], "title": "ASE: A Value Set Decision Procedure for Symbolic Execution.", "authors": ["Alireza <PERSON>", "<PERSON>"], "summary": "A symbolic execution engine regularly queries a Satisfiability Modulo Theory (SMT) solver to determine reachability of code during execution. Unfortunately, the SMT solver is often the bottleneck of symbolic execution. Inspired by abstract interpretation, we propose an abstract symbolic execution (ASE) engine which aims at querying the SMT solver less often by trying to compute reachability faster through an increasingly weaker abstraction. For this purpose, we have designed and implemented a value set decision procedure based on strided value interval (SVI) sets for efficiently determining precise, or under-approximating value sets for variables. Our ASE engine begins reasoning with respect to the SVI abstraction, and then only if needed uses the theory of bit-vectors implemented in SMT solvers. Our ASE engine efficiently detects when the former abstraction becomes incomplete to move on and try the next abstraction.We have designed and implemented a prototype of our engine for a subset of 64-bit RISC-V. Our experimental evaluation shows that our prototype often improves symbolic execution time by significantly reducing the number of SMT queries while, whenever the abstraction does not work, the overhead for trying still remains low.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678584"}, {"primary_key": "2203322", "vector": [], "sparse_vector": [], "title": "Nekara: Generalized Concurrency Testing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Testing concurrent systems remains an uncomfortable problem for developers. The common industrial practice is to stress-test a system against large workloads, with the hope of triggering enough corner-case interleavings that reveal bugs. However, stress testing is often inefficient and its ability to get coverage of interleavings is unclear. In reaction, the research community has proposed the idea of systematic testing, where a tool takes over the scheduling of concurrent actions so that it can perform an algorithmic search over the space of interleavings.We present an experience paper on the application of systematic testing to several case studies. We separate the algorithmic advancements in prior work (on searching the large space of interleavings) from the engineering of their tools. The latter was unsatisfactory; often the tools were limited to a small domain, hard to maintain, and hard to extend to other domains. We designed Nekara, an open-source cross-platform library for easily building custom systematic testing solutions.We show that (1) Nekara can effectively encapsulate state-of-the-art exploration algorithms by evaluating on prior bench-marks, and (2) Nekara can be applied to a wide variety of scenarios, including existing open-source systems as well as production distributed services of Microsoft Azure. Nekara was easy to use, improved testing, and found multiple new bugs.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678838"}, {"primary_key": "2203325", "vector": [], "sparse_vector": [], "title": "Automated Repair of Size-Based Inaccessibility Issues in Mobile Applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "An increasing number of people are dependent on mobile devices to access data and complete essential tasks. For people with disabilities, mobile apps that violate accessibility guidelines can prevent them from carrying out these activities. Size-Based Inaccessibility is one of the top accessibility issues in mobile applications. These issues make apps difficult to use, especially for older people and people with motor disabilities. Existing accessibility related techniques are limited in terms of helping developers to resolve these issues. In this paper, we present our novel automated approach for repairing Size-Based Inaccessibility issues in mobile applications. Our empirical evaluation showed that our approach was able to successfully resolve 99% of the reported Size-Based Inaccessibility issues and received a high approval rating in a user study of the appearance of the repaired user interfaces.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678625"}, {"primary_key": "2203326", "vector": [], "sparse_vector": [], "title": "Adaptation2: Adapting Specification Learners in Assured Adaptive Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Specification learning and controller synthesis are two methods that promise to provide control systems with assured adaptive capabilities at run-time. Specification learning can automatically update specifications in light of violation traces observed within the operational environment. Controller synthesis can then automatically generate implementations that are guaranteed to satisfy these specifications in every environment.Specification learning is implemented using general-purpose AI systems. These systems are highly configurable, and the configuration choice heavily affects the effectiveness. Setting configuration parameters is far from obvious as they bear no clear semantic relation with the adaptation task. State of the art requires configurations to be set by domain experts at design time for each application domain.In this paper, we argue that to create assured control systems that can effectively and efficiently adapt at run-time, the learning systems upon which they are built must also have adaptive learning strategies for determining configurations at runtime. We demonstrate this idea with a proof-of-concept that computes domain-dependent policies using reinforcement learning.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678919"}, {"primary_key": "2203329", "vector": [], "sparse_vector": [], "title": "Assessing Robustness of ML-Based Program Analysis Tools using Metamorphic Program Transformations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Metamorphic testing is a well-established testing technique that has been successfully applied in various domains, including testing deep learning models to assess their robustness against data noise or malicious input. Currently, metamorphic testing approaches for machine learning (ML) models focused on image processing and object recognition tasks. Hence, these approaches cannot be applied to ML targeting program analysis tasks. In this paper, we extend metamorphic testing approaches for ML models targeting software programs. We present LAMPION, a novel testing framework that applies (semantics preserving) metamorphic transformations on the test datasets. LAMPION produces new code snippets equivalent to the original test set but different in their identifiers or syntactic structure. We evaluate LAMPION against CodeBERT, a state-of-the-art ML model for Code-To-Text tasks that creates Javadoc summaries for given Java methods. Our results show that simple transformations significantly impact the target model behavior, providing additional information on the models reasoning apart from the classic performance metric.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678706"}, {"primary_key": "2203332", "vector": [], "sparse_vector": [], "title": "VizSmith: Automated Visualization Synthesis by Mining Data-Science Notebooks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visualizations are widely used to communicate findings and make data-driven decisions. Unfortunately creating bespoke and reproducible visualizations requires the use of procedural tools such as matplotlib. These tools present a steep learning curve as their documentation often lacks sufficient usage examples to help beginners get started or accomplish a specific task. Forums such as StackOverflow have long helped developers search for code online and adapt it for their use. However, developers still have to sift through search results and understand the code before adapting it for their use.We built a tool called VizSmith which enables code reuse for visualizations by mining visualization code from Kaggle notebooks and creating a database of 7176 reusable Python functions. Given a dataset, columns to visualize and a text query from the user, VizSmith searches this database for appropriate functions, runs them and displays the generated visualizations to the user. At the core of VizSmith is a novel metamorphic testing based approach to automatically assess the reusability of functions, which improves end-to-end synthesis performance by 10% and cuts the number of execution failures by 50%.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678696"}, {"primary_key": "2203333", "vector": [], "sparse_vector": [], "title": "Privacy as first-class requirements in software development: A socio-technical approach.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Privacy requirements have become increasingly important as information about us is continuously accumulated and digitally stored. However, despite the many proposed methodologies and tools to address these requirements, privacy engineering is often underperformed in most domains of the software industry. Two of the major reasons underlying this under-performance are (1) the low expertise and understanding of privacy by the two main actors in requirements engineering: users and analysts, and (2) the fact that software developers often do not perceive privacy requirements as a priority for their companies, thus neglecting to meet these requirements even when they do have the required knowledge, skills, and supporting tools to do so. To address these two problems, we propose to integrate knowledge from software engineering and organizational psychology in an iterative, customizable, socio-technical environment. Such environment has the potential to support the design of systems by providing technical tools for eliciting, modeling, and designing privacy aspects, thus addressing the knowledge gap of both data subjects and analysts, and social mechanisms for achieving a supportive and sustainable organizational privacy climate within a company, thus reorienting the organizational attention and engagement toward addressing privacy requirements.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678872"}, {"primary_key": "2203334", "vector": [], "sparse_vector": [], "title": "Learning Patterns in Configuration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Large services depend on correct configuration to run efficiently and seamlessly. Checking such configuration for correctness is important because services use a large and continuously increasing number of configuration files and parameters. Yet, very few such tools exist because the permissible values for a configuration parameter are seldom specified or documented, existing at best as tribal knowledge among a few domain experts.In this paper, we address the problem of configuration pattern mining: learning configuration rules from examples. Using program synthesis and a novel string profiling algorithm, we show that we can use file contents and histories of commits to learn patterns in configuration. We have built a tool called ConfMiner that implements configuration pattern mining and have evaluated it on four large repositories containing configuration for a large-scale enterprise service. Our evaluation shows that ConfMiner learns a large variety of configuration rules with high precision and is very useful in flagging anomalous configuration.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678525"}, {"primary_key": "2203336", "vector": [], "sparse_vector": [], "title": "BeAFix: An Automated Repair Tool for Faulty Alloy Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Marcelo F. Frias"], "summary": "This paper describes BeAFix, a tool for automated repair of faulty Alloy models. The tool builds upon the Alloy Analyzer, the analysis tool for Alloy. It generates repair candidates by mutating a faulty Alloy model, and employs a bounded-exhaustive approach to traverse the space of repair candidates. Since BeAFix's mutation operators make the space of repair candidates to quickly grow, the tool supports some sound pruning techniques, that allow it to fix Alloy models with more than one faulty line or expression. Additionally, BeAFix does not require tests as a patch acceptance criterion. Although BeAFix supports tests as oracles, our tool is also able to leverage property-based oracles, which are more commonly found in Alloy models in the form of predicate satisfiability and assertion validity checks.A video demonstration of BeAFix can be found at https://youtu.be/5RG40SmlFXQ. The tool's binaries and further details about its usage, can all be found at https://sites.google.com/view/beafixevaluation/beafix. The tool is also available in a public archive at https://doi.org/10.5281/zenodo.5296466.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678524"}, {"primary_key": "2203337", "vector": [], "sparse_vector": [], "title": "A Compositional Deadlock Detector for Android Java.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We develop a static deadlock analysis for commercial Android Java applications, of sizes in the tens of millions of LoC, under active development at Facebook. The analysis runs primarily at code-review time, on only the modified code and its dependents; we aim at reporting to developers in under 15 minutes.To detect deadlocks in this setting, we first model the real language as an abstract language with balanced re-entrant locks, nondeterministic iteration and branching, and non-recursive procedure calls. We show that the existence of a deadlock in this abstract language is equivalent to a certain condition over the sets of critical pairs of each program thread; these record, for all possible executions of the thread, which locks are currently held at the point when a fresh lock is acquired. Since the critical pairs of any program thread is finite and computable, the deadlock detection problem for our language is decidable, and in NP.We then leverage these results to develop an open-source implementation of our analysis adapted to deal with real Java code. The core of the implementation is an algorithm which computes critical pairs in a compositional, abstract interpretation style, running in quasi-exponential time. Our analyser is built in the Infer verification framework and has been in industrial deployment for over two years; it has seen over two hundred fixed deadlock reports with a report fix rate of ~54%.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678572"}, {"primary_key": "2203339", "vector": [], "sparse_vector": [], "title": "Using Static Analysis to Address Microservice Architecture Reconstruction.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Microservice design offers many advantages for enterprise applications, including increased scalability and faster deployment times. Microservices' independence from one another in development and deployment provides these advantages. This separation, however, results in the absence of a centralized view of the application's functionality, and each microservice's data model is isolated and replicated. As a result, it has the potential to deviate from the architectural design's original intent. To address this, we offer a method for analyzing a microservice mesh and generating a communication diagram, context map, and microservice-specific limited contexts using static code analysis.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678749"}, {"primary_key": "2203341", "vector": [], "sparse_vector": [], "title": "Automatic HMI Structure Exploration Via Curiosity-Based Reinforcement Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Discovering the underlying structure of HMI software efficiently and sufficiently for the purpose of testing without any prior knowledge on the software logic remains a difficult problem. The key challenge lies in the complexity of the HMI software and the high variance in the coverage of current methods. In this paper, we introduce the PathFinder, an effective and automatic HMI software exploration framework. PathFinder adopts a curiosity-based reinforcement learning framework to choose actions that lead to the discovery of more unknown states. Additionally, PathFinder progressively builds a navigation model during the exploration to further improve state coverage. We have conducted experiments on both simulations and real-world HMI software testing environment, which comprise a full tool chain of automobile dashboard instrument cluster. The exploration coverage outperforms manual and fuzzing methods which are the current industrial standards.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678703"}, {"primary_key": "2203343", "vector": [], "sparse_vector": [], "title": "On Multi-Modal Learning of Editing Source Code.", "authors": ["Saikat Chakraborty", "<PERSON><PERSON><PERSON><PERSON> Ray"], "summary": "In recent years, Neural Machine Translator (NMT) has shown promise in automatically editing source code. Typical NMT based code editor only considers the code that needs to be changed as input and suggests developers with a ranked list of patched code to choose from - where the correct one may not always be at the top of the list. While NMT based code editing systems generate a broad spectrum of plausible patches, the correct one depends on the developers' requirement and often on the context where the patch is applied. Thus, if developers provide some hints, using natural language, or providing patch context, NMT models can benefit from them.As a proof of concept, in this research, we leverage three modalities of information: edit location, edit code context, commit messages (as a proxy of developers' hint in natural language) to automatically generate edits with NMT models. To that end, we build Modit, a multi-modal NMT based code editing engine. With in-depth investigation and analysis, we show that developers' hint as an input modality can narrow the search space for patches and outperform state-of-the-art models to generate correctly patched code in top-1 position.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678559"}, {"primary_key": "2203344", "vector": [], "sparse_vector": [], "title": "Race Detection for Event-Driven Node.js Applications.", "authors": ["<PERSON><PERSON>", "Wensheng Dou", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Node.js has become a widely-used event-driven architecture for server-side and desktop applications. Node.js provides an effective asynchronous event-driven programming model, and supports asynchronous tasks and multi-priority event queues. Unexpected races among events and asynchronous tasks can cause severe consequences. Existing race detection approaches in Node.js applications mainly adopt random fuzzing technique, and can miss races due to large schedule space.In this paper, we propose a dynamic race detection approach NRace for Node.js applications. In NRace, we build precise happens-before relations among events and asynchronous tasks in Node.js applications, which also take multi-priority event queues into consideration. We further develop a predictive race detection technique based on these relations. We evaluate NRace on 10 realworld Node.js applications. The experimental result shows that NRace can precisely detect 6 races, and 5 of them have been confirmed by developers.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678814"}, {"primary_key": "2203345", "vector": [], "sparse_vector": [], "title": "Interactive Cross-language Code Retrieval with Auto-Encoders.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Cross-language code retrieval is necessary in many real-world scenarios. A major application is program translation, e.g., porting codebases from an obsolete or deprecated language to a modern one or re-implementing existing projects in one's preferred programming language. Existing approaches based on the translation model require large amounts of training data and extra information or neglects significant characteristics of programs. Leveraging cross-language code retrieval to assist automatic program translation can make use of Big Code. However, existing code retrieval systems have the barrier to finding the translation with only the features of the input program as the query. In this paper, we present BigPT for interactive cross-language retrieval from Big Code only based on raw code and reusing the retrieved code to assist program translation. We build on existing work on cross-language code representation and propose a novel predictive transformation model based on auto-encoders. The model is trained on Big Code to generate a target-language representation, which will be used as the query to retrieve the most relevant translations for a given program. Our query representation enables the user to easily update and correct the returned results to improve the retrieval process. Our experiments show that BigPT outperforms state-of-the-art baselines in terms of program accuracy. Using our novel querying and retrieving mechanism, BigPT can be scaled to the large dataset and efficiently retrieve the translation.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678929"}, {"primary_key": "2203346", "vector": [], "sparse_vector": [], "title": "Testing Your Question Answering Software via Asking Recursively.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Question Answering (QA) is an attractive and challenging area in NLP community. There are diverse algorithms being proposed and various benchmark datasets with different topics and task formats being constructed. QA software has also been widely used in daily human life now. However, current QA software is mainly tested in a reference-based paradigm, in which the expected outputs (labels) of test cases need to be annotated with much human effort before testing. As a result, neither the just-in-time test during usage nor the extensible test on massive unlabeled real-life data is feasible, which keeps the current testing of QA software from being flexible and sufficient. In this paper, we propose a method, qaAskeR, with three novel Metamorphic Relations for testing QA software. qaAskeR does not require the annotated labels but tests QA software by checking its behaviors on multiple recursively asked questions that are related to the same knowledge. Experimental results show that qaAskeR can reveal violations at over 80% of valid cases without using any preannotated labels. Diverse answering issues, especially the limited generalization on question types across datasets, are revealed on a state-of-the-art QA algorithm.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678670"}, {"primary_key": "2203347", "vector": [], "sparse_vector": [], "title": "Graph-based Incident Aggregation for Large-Scale Online Service Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "As online service systems continue to grow in terms of complexity and volume, how service incidents are managed will significantly impact company revenue and user trust. Due to the cascading effect, cloud failures often come with an overwhelming number of incidents from dependent services and devices. To pursue efficient incident management, related incidents should be quickly aggregated to narrow down the problem scope. To this end, in this paper, we propose GRLIA, an incident aggregation framework based on graph representation learning over the cascading graph of cloud failures. A representation vector is learned for each unique type of incident in an unsupervised and unified manner, which is able to simultaneously encode the topological and temporal correlations among incidents. Thus, it can be easily employed for online incident aggregation. In particular, to learn the correlations more accurately, we try to recover the complete scope of failures' cascading impact by leveraging fine-grained system monitoring data, i.e., Key Performance Indicators (KPIs). The proposed framework is evaluated with real-world incident data collected from a large-scale online service system of Huawei Cloud. The experimental results demonstrate that GRLIA is effective and outperforms existing methods. Furthermore, our framework has been successfully deployed in industrial practice.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678746"}, {"primary_key": "2203348", "vector": [], "sparse_vector": [], "title": "Detecting Adversarial Samples with Graph-Guided Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jingyang Xiang", "<PERSON><PERSON>", "<PERSON><PERSON>", "Shouling Ji", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep Neural Networks (DNN) are known to be vulnerable to adversarial samples, the detection of which is crucial for the wide application of these DNN models. Recently, a number of deep testing methods in software engineering were proposed to find the vulnerability of DNN systems, and one of them, i.e., Model Mutation Testing (MMT), was used to successfully detect various adversarial samples generated by different kinds of adversarial attacks. However, the mutated models in MMT are always huge in number (e.g., over 100 models) and lack diversity (e.g., can be easily circumvented by high-confidence adversarial samples), which makes it less efficient in real applications and less effective in detecting high-confidence adversarial samples. In this study, we propose Graph-Guided Testing (GGT) for adversarial sample detection to overcome these aforementioned challenges. GGT generates pruned models with the guide of graph characteristics, each of them has only about 5% parameters of the mutated model in MMT, and graph guided models have higher diversity. The initial experiments on CIFAR10 validate that GGT performs much better than MMT with respect to both effectiveness and efficiency.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678732"}, {"primary_key": "2203349", "vector": [], "sparse_vector": [], "title": "Shaker: a Tool for Detecting More Flaky Tests Faster.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> d&a<PERSON>;<PERSON><PERSON>"], "summary": "A test case that intermittently passes or fails when performed under the same version of source code and test code is said to be flaky. The presence of flaky tests wastes testing time and effort. The most popular approach in industry to detect flakiness is ReRun. The idea behind ReRun is very simple: failing test cases are re-executed many times looking for inconsistencies in the output. Despite its simplicity, the ReRun strategy is very expensive both in terms of time and in terms of computational resources. This is particularly true for contexts where thousands of test cases are performed on a daily basis. Reducing the rerunning overhead is, thus, of utmost importance. This paper presents SHAKER, an open-source tool for detecting flakiness in time-constrained tests by adding noise in the execution environment. The main idea behind SHAKER is to add stressing tasks that compete with the test execution for the use of resources (CPU or memory). SHAKER is available as a GitHub Actions workflow that can be seamlessly integrated with any GitHub project. Alternatively, SHAKER can also be used via its provided Command Line Interface. In our evaluation, SHAKER was able to discover more flaky tests than ReRun and in a faster way (less re-executions); besides, our approach revealed tens of new flaky tests that went undetected by ReRun even after 50 re-executions. Thanks to its flexibility and ease of use, we believe that SHAKER can be useful for both practitioners and researchers.Demo video: https://youtu.be/7-aiQwOb4rAShaker website: https://star-rg.github.io/shaker", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678918"}, {"primary_key": "2203351", "vector": [], "sparse_vector": [], "title": "Deep GUI: Black-box GUI Input Generation with Deep Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Despite the proliferation of Android testing tools, Google Monkey has remained the de facto standard for practitioners. The popularity of Google Monkey is largely due to the fact that it is a black-box testing tool, making it widely applicable to all types of Android apps, regardless of their underlying implementation details. An important drawback of Google Monkey, however, is the fact that it uses the most naive form of test input generation technique, i.e., random testing. In this work, we present Deep GUI, an approach that aims to complement the benefits of black-box testing with a more intelligent form of GUI input generation. Given only screenshots of apps, Deep GUI first employs deep learning to construct a model of valid GUI interactions. It then uses this model to generate effective inputs for an app under test without the need to probe its implementation details. Moreover, since the data collection, training, and inference processes are performed independent of the platform, the model inferred by Deep GUI has application for testing apps in other platforms as well. We implemented a prototype of Deep GUI in a tool called Monkey++ by extending Google Monkey and evaluated it for its ability to crawl Android apps. We found that Monkey++ achieves significant improvements over Google Monkey in cases where an app's UI is complex, requiring sophisticated inputs. Furthermore, our experimental results demonstrate the model inferred using Deep GUI can be reused for effective GUI input generation across platforms without the need for retraining.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678778"}, {"primary_key": "2203352", "vector": [], "sparse_vector": [], "title": "Automated Verification of Go Programs via Bounded Model Checking.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The Go programming language offers a wide range of primitives to coordinate lightweight threads, e.g., channels, waitgroups, and mutexes — all of which may cause concurrency bugs. Static checkers that guarantee the absence of bugs are essential to help programmers avoid these costly errors before their code is executed. However existing tools either miss too many bugs or cannot handle large programs. To address these limitations, we propose a static checker for Go programs which relies on performing bounded model checking of their concurrent behaviours. In contrast to previous works, our approach deals with large codebases, supports programs that have statically unknown parameters, and is extensible to additional concurrency primitives. Our work includes a detailed presentation of the extraction algorithm from Go programs to models, an algorithm to automatically check programs with statically unknown parameters, and a large scale evaluation of our approach. The latter shows that our approach outperforms the state-of-the-art.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678571"}, {"primary_key": "2203355", "vector": [], "sparse_vector": [], "title": "Automatically Deciding on the Integration of Commits Based on Their Descriptions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pedro <PERSON>", "<PERSON><PERSON><PERSON>", "Flávia de S. Santos", "<PERSON>", "<PERSON><PERSON>"], "summary": "Continuous Integration is a critical problem for software maintenance in global projects, compromising companies' performance, which tends to accumulate a high-resolution time due to the approval process, conflict resolution, tests, and validations. The process of the validation involves the commit description interpretation and can be automated by NLP-mechanisms. This paper presents an intelligent NLP-based approach to evaluate whether the commits can be integrated into a certain software release based only on their descriptions. Our experiments showed an accuracy of 92.9%.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678906"}, {"primary_key": "2203356", "vector": [], "sparse_vector": [], "title": "Access Control Tree for Testing and Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present our work on testing access control of large national e-health Internet portal which has millions of monthly visits. Our aim is twofold: (1) to improve testing by applying systematic and rigorous (semi-formal) approach and (2) to obtain holistic view of portal's complex access control structure. Applying more rigorous approach facilitates reducing ambiguity while holistic picture aids on easier and often also faster comprehension of complex access control structure. We use set-theoretic approach for specifying access control. Then, from access control's abstract set notations we derive a visual version in form of the access control tree. Nodes of the tree represent attributes that influence access while edges are values of those attributes. The leaf of the tree represents a scope which is a grouping of individual services. Access control tree presented in this paper has 15 scopes (leaves) which results in 105 pairs of abstract test scenarios. Complete version of the tree has 66 scopes that result in over 2000 pairs of abstract test scenarios. Abstract test scenarios are implemented into over 600 concrete and automated test cases. Manual execution test of one concrete test takes about five minutes while automated execution of all tests takes about one hour (thus achieving over 40 times speedup). These automated test cases run as a part of our CI/CD pipeline. Access control tree can also be used as a collaboration or learning tool, to get quicker familiarity with the solution.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678797"}, {"primary_key": "2203357", "vector": [], "sparse_vector": [], "title": "Automatically Annotating Sentences for Task-specific Bug Report Summarization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There is a need to summarize bug reports as they can become long due to many comments from conversations between developers and various DevOps tools. Although automated approaches to bug report summarization have been developed, we believe they aim at the wrong target - getting as close as possible to a gold-standard summary. Instead, researchers should create automated bug report annotation approaches that allow project members to create summaries based on their task-specific information needs. We present such an approach.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678554"}, {"primary_key": "2203358", "vector": [], "sparse_vector": [], "title": "Is Historical Data an Appropriate Benchmark for Reviewer Recommendation Systems? : A Case Study of the Gerrit Community.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Reviewer recommendation systems are used to suggest community members to review change requests. Like several other recommendation systems, it is customary to evaluate recommendations using held out historical data. While history-based evaluation makes pragmatic use of available data, historical records may be: (1) overly optimistic, since past assignees may have been suboptimal choices for the task at hand; or (2) overly pessimistic, since \"incorrect\" recommendations may have been equal (or even better) choices.In this paper, we empirically evaluate the extent to which historical data is an appropriate benchmark for reviewer recommendation systems. We replicate the CHREV and WLRREC approaches and apply them to 9,679 reviews from the GERRIT open source community. We then assess the recommendations with members of the GERRIT reviewing community using quantitative methods (personalized questionnaires about their comfort level with tasks) and qualitative methods (semi-structured interviews).We find that history-based evaluation is far more pessimistic than optimistic in the context of GERRIT review recommendations. Indeed, while 86% of those who had been assigned to a review in the past felt comfortable handling the review, 74% of those labelled as incorrect recommendations also felt that they would have been comfortable reviewing the changes. This indicates that, on the one hand, when reviewer recommendation systems recommend the past assignee, they should indeed be considered correct. Yet, on the other hand, recommendations labelled as incorrect because they do not match the past assignee may have been correct as well.Our results suggest that current reviewer recommendation evaluations do not always model the reality of software development. Future studies may benefit from looking beyond repository data to gain a clearer understanding of the practical value of proposed recommendations.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678640"}, {"primary_key": "2203359", "vector": [], "sparse_vector": [], "title": "Training Automated Test Oracles to Identify Semantic Bugs.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Can a machine find and fix a Semantic Bug? A Semantic Bug is a deviation from the expected program behaviour that causes to produce incorrect outputs for certain inputs. To identify this category of bugs, the knowledge on the expected program behaviour is essential. The reason is that a program with a semantic bug does not fail (i.e., crash or hang) in the middle of the execution in most scenarios. Thus, only a human (a user or a developer) knowing the correct program behaviour can detect this kind of bug by observing the output. However, identifying bugs solely through human effort is not practical for all software. A Test Oracle is any procedure used to differentiate the correct and incorrect behaviours of a program. This dissertation mainly focuses on developing learning techniques to produce Automated Test Oracles for programs with semantic bugs. Also, discovering methods to incorporate human knowledge effectively for the learning techniques is another concern. The automated test oracles could make semantic bug detection more efficient. Also, such test oracles could guide Automated Program Repair tools to generate more accurate fixes for semantic bugs.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678886"}, {"primary_key": "2203360", "vector": [], "sparse_vector": [], "title": "Evolutionary-Guided Synthesis of Verified Pareto-Optimal MDP Policies.", "authors": ["Simos G<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>wi<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a new approach for synthesising Paretooptimal Markov decision process (MDP) policies that satisfy complex combinations of quality-of-service (QoS) software requirements. These policies correspond to optimal designs or configurations of software systems, and are obtained by translating MDP models of these systems into parametric Markov chains, and using multi-objective genetic algorithms to synthesise Pareto-optimal parameter values that define the required MDP policies. We use case studies from the service-based systems and robotic control software domains to show that our MDP policy synthesis approach can handle a wide range of QoS requirement combinations unsupported by current probabilistic model checkers. Moreover, for requirement combinations supported by these model checkers, our approach generates better Pareto-optimal policy sets according to established quality metrics.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678727"}, {"primary_key": "2203361", "vector": [], "sparse_vector": [], "title": "Leveraging Code Clones and Natural Language Processing for Log Statement Prediction.", "authors": ["Sina Gholamian"], "summary": "Software developers embed logging statements inside the source code as an imperative duty in modern software development as log files are necessary for tracking down runtime system issues and troubleshooting system management tasks. Prior research has emphasized the importance of logging statements in the operation and debugging of software systems. However, the current logging process is mostly manual and ad hoc, and thus, proper placement and content of logging statements remain as challenges. To overcome these challenges, methods that aim to automate log placement and log content, i.e., 'where, what, and how to log', are of high interest. Thus, we propose to accomplish the goal of this research, that is \"to predict the log statements by utilizing source code clones and natural language processing (NLP)\", as these approaches provide additional context and advantage for log prediction. We pursue the following four research objectives: (RO1) investigate whether source code clones can be leveraged for log statement location prediction, (RO2) propose a clone-based approach for log statement prediction, (RO3) predict log statement's description with code-clone and NLP models, and (RO4) examine approaches to automatically predict additional details of the log statement, such as its verbosity level and variables. For this purpose, we perform an experimental analysis on seven open-source java projects, extract their method-level code clones, investigate their attributes, and utilize them for log location and description prediction. Our work demonstrates the effectiveness of log-aware clone detection for automated log location and description prediction and outperforms the prior work.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678596"}, {"primary_key": "2203362", "vector": [], "sparse_vector": [], "title": "Genetic Optimisation of C++ Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Constantin <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software developers sometimes use inefficient data structures or library interfaces without considering the potential impact they may have during the runtime of a program. This is due to the significant effort required to research and evaluate possibly more efficient alternatives. Consequently, there is a need for tooling to automate the design space exploration. Our proposed code optimisation solution, called Artemis++, tries to address this issue with automatic exploration and transformation of data structures to optimise software performance. In preliminary testing on three mainstream C++ libraries, we have observed improvements up to 16.09%, 27.90%, and 2.74% for CPU usage, runtime and memory, respectively.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678650"}, {"primary_key": "2203364", "vector": [], "sparse_vector": [], "title": "Evaluating Semantic Autocompletion of Business Processes with Domain Experts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "Process modeling can benefit from automation using knowledge mined from collections of existing processes. One promising technique for such automation is the recommendation of the next elements to be added to the processes under construction. In this paper, we review an autocompletion engine that is based on the semantic similarity of business processes. To assess its efficiency in practical settings, we conduct a user study where domain experts are asked to rate the suggestions made by the engine for a commercial product. Their ratings are then compared to the engine's accuracy measured by metrics from the natural language processing field. Our study shows a strong correlation between the expert ratings and some of these metrics. We confirm the usefulness of such an autocompletion engine, and enumerate potential improvements to any process autocompletion technique.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678821"}, {"primary_key": "2203365", "vector": [], "sparse_vector": [], "title": "Tackling Flaky Tests: Understanding the Problem and Providing Practical Solutions.", "authors": ["<PERSON>"], "summary": "Non-deterministically behaving tests impede software development as they hamper regression testing, destroy trust, and waste resources. This phenomenon, also called test flakiness, has received increasing attention over the past years. The multitude of both peer-reviewed literature and online blog articles touching the issue illustrates that flaky tests are deemed both a relevant research topic and a serious problem in everyday business. A major shortcoming of existing work aiming to mitigate test flakiness is its limited applicability, since many of the proposed tools are highly relying on specific ecosystems. This issue also reflects on various attempts to investigate flaky tests: Using mostly similar sets of open-source Java projects, many studies are unable to generalize their findings to projects laying beyond this scope. On top of that, a holistic understanding of test flakiness also suffers from a lack of analyses focusing on the developers' perspective, since most existing studies take a code-centric approach.With my work, I want to close these gaps: I plan to create an overarching and widely applicable framework that empowers developers to tackle flaky tests through existing and novel techniques and enables researchers to swiftly deploy and evaluate new approaches. As a starting point, I am studying test flakiness from previously unconsidered angles: I widen the scope of observation investigating flakiness beyond the realm of the Java ecosystem, while also capturing the practitioners' opinion. By adding to the understanding of the phenomenon I not only hope to close existing research gaps, but to retrieve a clear vision of how research on test flakiness can create value for developers working in the field.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678701"}, {"primary_key": "2203369", "vector": [], "sparse_vector": [], "title": "Performance Testing for Cloud Computing with Dependent Data Bootstrapping.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "In Kee Kim", "<PERSON>"], "summary": "To effectively utilize cloud computing, cloud practice and research require accurate knowledge of the performance of cloud applications. However, due to the random performance fluctuations, obtaining accurate performance results in the cloud is extremely difficult. To handle this random fluctuation, prior research on cloud performance testing relied on a non-parametric statistic tool called bootstrapping to design their stop criteria. However, in this paper, we show that the basic bootstrapping employed by prior work overlooks the internal dependency within cloud performance test data, which leads to inaccurate performance results.We then present Metior, a novel automated cloud performance testing methodology, which is designed based on statistical tools of block bootstrapping, the law of large numbers, and autocorrelation. These statistical tools allow Metior to properly consider the internal dependency within cloud performance test data. They also provide better coverage of cloud performance fluctuation and reduce the testing cost. Experimental evaluation on two public clouds showed that 98% of Metior's tests could provide performance results with less than 3% error. Metior also significantly outperformed existing cloud performance testing methodologies in terms of accuracy and cost – with up to 14% increase in the accurate test count and up to 3.1 times reduction in testing cost.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678687"}, {"primary_key": "2203370", "vector": [], "sparse_vector": [], "title": "Context Debloating for Object-Sensitive Pointer Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "Jingbo Lu", "Jingling Xue"], "summary": "We Introduce a new approach, <PERSON><PERSON>, for debloating contexts for all the object-sensitive pointer analysis algorithms developed for object-oriented languages, where the calling contexts of a method are distinguished by its receiver objects. Our key insight is to approximate a recently proposed set of two necessary conditions for an object to be context-sensitive, i.e., context-dependent (whose precise verification is undecidable) with a set of three linearly verifiable conditions (in terms of the number of statements in the program) that are almost always necessary for real-world object-oriented applications, based on three key observations regarding context-dependability for their objects used. To create a practical implementation, we introduce a new IFDS-based algorithm for reasoning about object reachability in a program. By debloating contexts for two representative object-sensitive pointer analyses applied to a set of 12 representative Java programs, <PERSON><PERSON> can speed up the two baselines together substantially (3.1x on average with a maximum of 15.9x) and analyze 7 more programs scalably, but at only a negligible loss of precision (less than 0.1%).", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678880"}, {"primary_key": "2203372", "vector": [], "sparse_vector": [], "title": "Did You Do Your Homework? Raising Awareness on Software Fairness and Discrimination.", "authors": ["<PERSON>", "Federica Sarro"], "summary": "Machine Learning is a vital part of various modern day decision making software. At the same time, it has shown to exhibit bias, which can cause an unjust treatment of individuals and population groups. One method to achieve fairness in machine learning software is to provide individuals with the same degree of benefit, regardless of sensitive attributes (e.g., students receive the same grade, independent of their sex or race). However, there can be other attributes that one might want to discriminate against (e.g., students with homework should receive higher grades). We will call such attributes anti-protected attributes. When reducing the bias of machine learning software, one risks the loss of discriminatory behaviour of anti-protected attributes. To combat this, we use grid search to show that machine learning software can be debiased (e.g., reduce gender bias) while also improving the ability to discriminate against anti-protected attributes.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678568"}, {"primary_key": "2203373", "vector": [], "sparse_vector": [], "title": "Towards Exploring the Limitations of Active Learning: An Empirical Study.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep neural networks (DNNs) are increasingly deployed as integral parts of software systems. However, due to the complex interconnections among hidden layers and massive hyperparameters, DNNs must be trained using a large number of labeled inputs, which calls for extensive human effort for collecting and labeling data. Spontaneously, to alleviate this growing demand, multiple state-of-the-art studies have developed different metrics to select a small yet informative dataset for the model training. These research works have demonstrated that DNN models can achieve competitive performance using a carefully selected small set of data. However, the literature lacks proper investigation of the limitations of data selection metrics, which is crucial to apply them in practice. In this paper, we fill this gap and conduct an extensive empirical study to explore the limits of data selection metrics. Our study involves 15 data selection metrics evaluated over 5 datasets (2 image classification tasks and 3 text classification tasks), 10 DNN architectures, and 20 labeling budgets (ratio of training data being labeled). Our findings reveal that, while data selection metrics are usually effective in producing accurate models, they may induce a loss of model robustness (against adversarial examples) and resilience to compression. Overall, we demonstrate the existence of a trade-off between labeling effort and different model qualities. This paves the way for future research in devising data selection metrics considering multiple quality criteria.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678672"}, {"primary_key": "2203374", "vector": [], "sparse_vector": [], "title": "Automating User Notice Generation for Smart Contract Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Smart contracts have obtained much attention and are crucial for automatic financial and business transactions. For end-users who have never seen the source code, they can read the user notice shown in end-user client to understand what a transaction does of a smart contract function. However, due to time constraints or lack of motivation, user notice is often missing during the development of smart contracts. For end-users who lack the information of the user notices, there is no easy way for them to check the code semantics of the smart contracts. Thus, in this paper, we propose a new approach SMARTDOC to generate user notice for smart contract functions automatically. Our tool can help end-users better understand the smart contract and aware of the financial risks, improving the users' confidence on the reliability of the smart contracts. SMARTDOC exploits the Transformer to learn the representation of source code and generates natural language descriptions from the learned representation. We also integrate the Pointer mechanism to copy words from the input source code instead of generating words during the prediction process. We extract 7,878 〈function, notice〉 pairs from 54,739 smart contracts written in Solidity. Due to the limited amount of collected smart contract functions (i.e., 7,878 functions), we exploit a transfer learning technique to utilize the learned knowledge to improve the performance of SMARTDOC. The learned knowledge obtained by the pre-training on a corpus of Java code, that has similar characteristics as Solidity code. The experimental results show that our approach can effectively generate user notice given the source code and significantly outperform the state-of-the-art approaches. To investigate human perspectives on our generated user notice, we also conduct a human evaluation and ask participants to score user notice generated by different approaches. Results show that SMARTDOC outperforms baselines from three aspects, naturalness, informativeness, and similarity.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678552"}, {"primary_key": "2203375", "vector": [], "sparse_vector": [], "title": "REPFINDER: Finding Replacements for Missing APIs in Library Update.", "authors": ["<PERSON><PERSON> Huang", "<PERSON><PERSON><PERSON>", "Ling<PERSON> Pan", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Libraries are widely adopted in developing software projects. Library APIs are often missing during library evolution as library developers may deprecate, remove or refactor APIs. As a result, client developers have to manually find replacement APIs for missing APIs when updating library versions in their projects, which is a difficult and expensive software maintenance task. One of the key limitations of the existing automated approaches is that they usually consider the library itself as the single source to find replacement APIs, which heavily limits their accuracy.In this paper, we first present an empirical study to understand characteristics about missing APIs and their replacements. Specifically, we quantify the prevalence of missing APIs, and summarize the knowledge sources where the replacements are found, and the code change and mapping cardinality between missing APIs and their replacements. Then, inspired by the insights from our study, we propose a heuristic-based approach, REPFINDER, to automatically find replacements for missing APIs in library update. We design and combine a set of heuristics to hierarchically search three sources (deprecation message, own library, and external library) for finding replacements. Our evaluation has demonstrated that REPFINDER can find replacement APIs effectively and efficiently, and significantly outperform the state-of-the-art approaches.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678905"}, {"primary_key": "2203376", "vector": [], "sparse_vector": [], "title": "Characterizing and Detecting Configuration Compatibility Issues in Android Apps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "XML configuration files are widely used in Android to define an app's user interface and essential runtime information such as system permissions. As Android evolves, it might introduce functional changes in the configuration environment, thus causing compatibility issues that manifest as inconsistent app behaviors at different API levels. Such issues can often induce software crashes and inconsistent look-and-feel when running at specific Android versions. Existing works incur plenty of false positive and false negative issue-detection rules by conducting trivial data-flow analysis while failing to model the XML tree hierarchies of the Android configuration files. Besides, little is known about how the changes in an Android framework can induce such compatibility issues. To bridge such gaps, we conducted a systematic study by analyzing 196 real-world issues collected from 43 popular apps. We identified common patterns of Android framework code changes that induce such configuration compatibility issues. Based on the findings, we propose CONFDROID that can automatically extract rules for detecting configuration compatibility issues. The intuition is to perform symbolic execution based on a model learned from the common code change patterns. Experiment results show that CONFDROID can successfully extract 282 valid issue-detection rules with a precision of 91.9%. Among them, 65 extracted rules can manifest issues that cannot be detected by the rules of state-of-the-art baselines. More importantly, 11 out of them have led to the detection of 107 reproducible configuration compatibility issues that the baselines cannot detect in 30 out of 316 real-world Android apps.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678556"}, {"primary_key": "2203377", "vector": [], "sparse_vector": [], "title": "Counterexample Guided Inductive Repair of Reactive Contracts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Mats <PERSON>"], "summary": "Using third-party executable components to build control systems poses challenges for verification. This is because the informal behavior descriptions that typically accompany the components often fall short of the needed rigor. Consequently, there is a need to formalize a component contract that is strong enough to help establish system properties and also weak enough to account for all potential component behaviors in the system's context. In this paper, we present a novel approach that allows an analyst to hypothesize a component contract, explore if the component meets the contract, and, if not, have automated support to help repair the contract. Preliminary results show that, in more than 32% of the cases, the repaired contract is logically equivalent to a developer-written one; in a further 63% of cases, it is a distinct, valid, and non-trivial property of the component.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678548"}, {"primary_key": "2203378", "vector": [], "sparse_vector": [], "title": "Business Process Extraction Using Static Analysis.", "authors": ["<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Business process mining of a large-scale project has many benefits such as finding vulnerabilities, improving processes, collecting data for data science, generating more clear and simple representation, etc. The general way of process mining is to turn event data such as application logs into insights and actions. Observing logs broad enough to depict the whole business logic scenario of a large project can become very costly due to difficult environment setup, unavailability of users, presence of not reachable or hardly reachable log statements, etc. Using static source code analysis to extract logs and arranging them perfect runtime execution order is a potential way to solve the problem and reduce the business process mining operation cost.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678588"}, {"primary_key": "2203379", "vector": [], "sparse_vector": [], "title": "Improving Mutation-Based Fault Localization with Plausible-code Generating Mutation Operators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposes a new mutation operator using neural network to generate plausible code elements to improve performance of mutation-based fault localization on omission faults. Unlike the existing mutation operators, the proposed mutation operator synthesizes new code elements at a given mutation site with a neural language model. We extended MUSE to use the proposed mutation operator, and conducted a case study with 3 omission faults found in JFreeChart of Defects4J. As a result, the accuracy of MUSE with the new mutation operator increased significantly in all three faults.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678577"}, {"primary_key": "2203380", "vector": [], "sparse_vector": [], "title": "Automated Testing for Machine Translation via Constituency Invariance.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the development of deep neural networks, machine translation has achieved significant progress and integrated with people's daily lives to assist in various tasks. However, machine translators, which are essentially one kind of software, also suffer from software defects. Translation errors might cause misunderstanding or even lead to marketing blunders, and political crisis. Thus, almost all translation service providers have feedback channels of incorrect translations to collect training data and improve product performance. Inspired by the syntax structure analysis, we introduce the constituency invariance, which reflects the structural similarity between a simple sentence and sentences derived from it, to test machine translators. We implement it into an automated tool CIT to detect translation errors by checking the constituency invariance relation between the translation results. CIT adopts constituency parse trees to represent the syntactic structures of sentences and employs an efficient data augmentation method to derive multiple new sentences based on one sentence. To validate CIT, we experiment with three widely-used machine translators, i.e., Bing Microsoft Translator, Google Translate, and Youdao Translator. With 600 seed sentences as input, CIT detects 2212, 1910, and 1590 translation errors with around 77% precision. We have submitted detected errors to the development teams. Until we submit this paper, Google, Bing, and Youdao have fixed 15.4%, 32.0%, 14.3% of reported errors, respectively.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678715"}, {"primary_key": "2203381", "vector": [], "sparse_vector": [], "title": "RULF: Rust Library Fuzzing via API Dependency Graph Traversal.", "authors": ["<PERSON><PERSON><PERSON> Jiang", "<PERSON>", "<PERSON><PERSON>"], "summary": "Robustness is a key concern for Rust library development because Rust promises no risks of undefined behaviors if developers use safe APIs only. Fuzzing is a practical approach for examining the robustness of programs. However, existing fuzzing tools are not directly applicable to library APIs due to the absence of fuzz targets. It mainly relies on human efforts to design fuzz targets case by case which is labor-intensive. To address this problem, this paper proposes a novel automated fuzz target generation approach for fuzzing Rust libraries via API dependency graph traversal. We identify several essential requirements for library fuzzing, including validity and effectiveness of fuzz targets, high API coverage, and efficiency. To meet these requirements, we first employ breadth-first search with pruning to find API sequences under a length threshold, then we backward search longer sequences for uncovered APIs, and finally we optimize the sequence set as a set covering problem. We implement our fuzz target generator and conduct fuzzing experiments with AFL++ on several real-world popular Rust projects. Our tool finally generates 7 to 118 fuzz targets for each library with API coverage up to 0.92. We exercise each target with a threshold of 24 hours and find 30 previously-unknown bugs from seven libraries.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678813"}, {"primary_key": "2203382", "vector": [], "sparse_vector": [], "title": "Property-based Test for Part-of-Speech Tagging Tool.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Part-of-Speech (POS) tagging for sentences is a basic and widely-used Natural Language Processing (NLP) technique. People rely heavily on it to predict POS tags that serve as the base for many advanced NLP tasks, such as sentiment analysis, word sense disambiguation, and information retrieval. However, POS tagging tools could make wrong predictions, which bring consequent error propagation to the advanced tasks and even cause serious threats in critical application domains. In this paper, we propose to test POS tagging tools with Metamorphic Testing against some properties that they should follow. The preliminary exploration with two groups of Metamorphic Relations shows that our method can effectively reveal defects of three common POS tagging tools (i.e., spaCy, NLTK, and Flair) on handling fairly simple intra- and inter-sentence transformation regarding adverbial clause and sentence appending. This demonstrates the great potential of our method to deliver a systematic test and reveal the unaware issues, which may benefit the validation, repair, and improvement, for POS tagging tools.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678807"}, {"primary_key": "2203383", "vector": [], "sparse_vector": [], "title": "Where to Start: Studying Type Annotation Practices in Python.", "authors": ["Wuxia Jin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic programming languages have been embracing gradual typing, which supports optional type annotations in source code. Type-annotating a complex and long-lasting codebase is indeed a gradual and expensive process, where two issues have troubled developers. First, there is few guidance about how to implement type annotations due to the existence of non-trivial type practices; second, there is few guidance about which portion of a codebase should be type-annotated first. To address these issues, this paper investigates the patterns of non-trivial type-annotation practices and features of type-annotated code files. Our study detected six patterns of type-annotation practices, which involve recovering and expressing design concerns. Moreover, we revealed three complementary features of type-annotated files. Besides, we implemented a tool for studying optional typing practice. We suggest that: 1) design concerns should be considered to improve type annotation implementation by following at least six patterns; 2) files critical to software architecture could be type-annotated in priority. We believe these guidelines would promote a better type annotation practice for dynamic languages.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678947"}, {"primary_key": "2203385", "vector": [], "sparse_vector": [], "title": "Defeating Program Analysis Techniques via Ambiguous Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This research explores the possibility of a new anti-analysis technique, carefully designed to attack weaknesses of the existing program analysis approaches. It encodes a program code snippet to hide, and its decoding process is implemented by a sophisticated state machine that produces multiple outputs depending on inputs. The key idea of the proposed technique is to ambiguously decode the program code, resulting in multiple decoded code snippets that are challenging to distinguish from each other. Our approach is stealthier than previous similar approaches as its execution does not exhibit different behaviors between when it decodes correctly or incorrectly. This paper also presents analyses of weaknesses of existing techniques and discusses potential improvements. We implement and evaluate the proof of concept approach, and our preliminary results show that the proposed technique imposes various new unique challenges to the program analysis technique.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678912"}, {"primary_key": "2203386", "vector": [], "sparse_vector": [], "title": "What do pre-trained code models know about code?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pre-trained models of code built on the transformer architecture have performed well on software engineering (SE) tasks such as predictive code generation, code summarization, among others. However, whether the vector representations from these pre-trained models comprehensively encode characteristics of source code well enough to be applicable to a broad spectrum of downstream tasks remains an open question.One way to investigate this is with diagnostic tasks called probes. In this paper, we construct four probing tasks (probing for surface-level, syntactic, structural, and semantic information) for pre-trained code models. We show how probes can be used to identify whether models are deficient in (understanding) certain code properties, characterize different model layers, and get insight into the model sample-efficiency.We probe four models that vary in their expected knowledge of code properties: BERT (pre-trained on English), CodeBERT and CodeBERTa (pre-trained on source code, and natural language documentation), and GraphCodeBERT (pre-trained on source code with dataflow). While GraphCodeBERT performs more consistently overall, we find that BERT performs surprisingly well on some code tasks, which calls for further investigation.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678927"}, {"primary_key": "2203387", "vector": [], "sparse_vector": [], "title": "Q&amp;A MAESTRO: Q&amp;A Post Recommendation for Fixing Java Runtime Exceptions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Programmers often use Q&A sites (e.g., Stack Overflow) to understand a root cause of program bugs. Runtime exceptions is one of such important class of bugs that is actively discussed on Stack Overflow. However, it may be difficult for beginner programmers to come up with appropriate keywords for search. Moreover, they need to switch their attentions between IDE and browser, and it is time-consuming. To overcome these difficulties, we proposed a method, \"Q&A MAESTRO\", to find suitable Q&A posts automatically for Java runtime exception by utilizing structure information of codes described in programming Q&A website. In this paper, we describe a usage scenario of IDE-plugin, the architecture and user interface of the implementation, and results of user studies. A video is available at https://youtu.be/4X24jJrMUVw. A demo software is available at https://github.com/FujitsuLaboratories/Q-A-MAESTRO.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678893"}, {"primary_key": "2203388", "vector": [], "sparse_vector": [], "title": "SATune: A Study-Driven Auto-Tuning Approach for Configurable Software Verification Tools.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many program verification tools can be customized via run-time configuration options that trade off performance, precision, and soundness. However, in practice, users often run tools under their default configurations, because understanding these tradeoffs requires significant expertise. In this paper, we ask how well a single, default configuration can work in general, and we propose SATune, a novel tool for automatically configuring program verification tools for given target programs. To answer our question, we gathered a dataset that runs four well-known program verification tools against a range of C and Java benchmarks, with results labeled as correct, incorrect, or inconclusive (e.g., timeout). Examining the dataset, we find there is generally no one-size-fits-all best configuration. Moreover, a statistical analysis shows that many individual configuration options do not have simple tradeoffs: they can be better or worse depending on the program.Motivated by these results, we developed SATune, which constructs configurations using a meta-heuristic search. The search is guided by a surrogate fitness function trained on our dataset. We compare the performance of SATune to three baselines: a single configuration with the most correct results in our dataset; the most precise configuration followed by the most correct configuration (if needed); and the most precise configuration followed by random search (also if needed). We find that SATune outperforms these approaches by completing more correct tasks with high precision. In summary, our work shows that good configurations for verification tools are not simple to find, and SATune takes an important step towards automating the process of finding them.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678761"}, {"primary_key": "2203389", "vector": [], "sparse_vector": [], "title": "Refactorings and Technical Debt in Docker Projects: An Empirical Study.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Thiago do Nascimento Ferreira", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Software containers, such as Docker, are recently considered as the mainstream technology of providing reusable software artifacts. Developers can easily build and deploy their applications based on the large number of reusable Docker images that are publicly available. Thus, a current popular trend in industry is to move towards the containerization of their applications. However, container-based projects compromise different components including the Docker and Docker-compose files, and several other dependencies to the source code combining different containers and facilitating the interactions with them. Similar to any other complex systems, container-based projects are prone to various quality and technical debt issues related to different artifacts: Docker and Docker-compose files, and regular source code ones. Unfortunately, there is a gap of knowledge in how container-based projects actually evolve and are maintained.In this paper, we address the above gap by studying refactorings, i.e., structural changes while preserving the behavior, applied in open-source Docker projects, and the technical debt issues they alleviate. We analyzed 68 projects, consisting of 19,5 MLOC, along with 193 manually examined commits. The results indicate that developers refactor these Docker projects for a variety of reasons that are specific to the configuration, combination and execution of containers, leading to several new technical debt categories and refactoring types compared to existing refactoring domains. For instance, refactorings for reducing the image size of Dockerfiles, improving the extensibility of Docker-compose files, and regular source code refactorings are mainly associated with the evolution of Docker and Docker-compose files. We also introduced 24 new Docker-specific refactorings and technical debt categories, respectively, and defined different best practices. The implications of this study will assist practitioners, tool builders, and educators in improving the quality of Docker projects.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678585"}, {"primary_key": "2203390", "vector": [], "sparse_vector": [], "title": "Modeling Team Dynamics for the Characterization and Prediction of Delays in User Stories.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In agile software development, proper team structures and effort estimates are crucial to ensure the on-time delivery of software projects. Delivery performance can vary due to the influence of changes in teams, resulting in team dynamics that remain largely unexplored. In this paper, we explore the effects of various aspects of teamwork on delays in software deliveries. We conducted a case study at ING and analyzed historical log data from 765,200 user stories and 571 teams to identify team factors characterizing delayed user stories. Based on these factors, we built models to predict the likelihood and duration of delays in user stories. The evaluation results show that the use of team-related features leads to a significant improvement in the predictions of delay, achieving on average 74%-82% precision, 78%-86% recall and 76%-84% F-measure. Moreover, our results show that team-related features can help improve the prediction of delay likelihood, while delay duration can be explained exclusively using them. Finally, training on recent user stories using a sliding window setting improves the predictive performance; our predictive models perform significantly better for teams that have been stable. Overall, our results indicate that planning in agile development settings can be significantly improved by incorporating team-related information and incremental learning methods into analysis/predictive models.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678939"}, {"primary_key": "2203391", "vector": [], "sparse_vector": [], "title": "Learning Highly Recursive Input Grammars.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents Arvada, an algorithm for learning context-free grammars from a set of positive examples and a Boolean-valued oracle. Arvada learns a context-free grammar by building parse trees from the positive examples. Starting from initially flat trees, Arvada builds structure to these trees with a key operation: it bubbles sequences of sibling nodes in the trees into a new node, adding a layer of indirection to the tree. Bubbling operations enable recursive generalization in the learned grammar. We evaluate Arvada against GLADE and find it achieves on average increases of 4.98× in recall and 3.13× in F1 score, while incurring only a 1.27× slowdown and requiring only 0.87× as many calls to the oracle. Arvada has a particularly marked improvement over GLADE on grammars with highly recursive structure, like those of programming languages.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678879"}, {"primary_key": "2203394", "vector": [], "sparse_vector": [], "title": "RefactorInsight: Enhancing IDE Representation of Changes in Git with Refactorings Information.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Inspection of code changes is a time-consuming task that constitutes a big part of everyday work of software engineers. Existing IDEs provide little information about the semantics of code changes within the file editor view. Therefore developers have to track changes across multiple files, which is a hard task with large codebases.In this paper, we present REFACTORINSIGHT, a plugin for IntelliJ IDEA that introduces a smart diff for code changes in Java and Kotlin where refactorings are auto-folded and provided with their description, thus allowing users to focus on changes that modify the code behavior like bug fixes and new features. REFACTORINSIGHT supports three usage scenarios: viewing smart diffs with auto-folded refactorings and hints, inspecting refactorings in pull requests and in any specific commit in the project change history, and exploring the refactoring history of methods and classes. The evaluation shows that commit processing time is acceptable: on median it is less than 0.2 seconds, which delay does not disrupt developers' IDE workflows.Refactorinsight is available at https://github.com/JetBrains-Research/RefactorInsight. The demonstration video is available at https://youtu.be/-6L2AKQ66nA.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678646"}, {"primary_key": "2203396", "vector": [], "sparse_vector": [], "title": "Towards the generation of machine learning defect reports.", "authors": ["<PERSON><PERSON>"], "summary": "Effective locating and fixing defects requires detailed defect reports. Unlike traditional software systems, machine learning applications are subject defects caused from changes in the input data streams (concept drift) and assumptions encoded into models. Without appropriate training, developers face difficulties understanding and interpreting faults in machine learning (ML). However, little research is done on how to prepare developers to detect and investigate machine learning system defects. Software engineers often do not have sufficient knowledge to fix the issues themselves without the help of data scientists or domain experts. To investigate this issue, we analyse issue templates and check how developers report machine learning related issues in open-source applied AI projects. The overall goal is to develop a tool for automatically repairing ML defects or generating defect reports if a fix cannot be made. Previous research has identified classes of faults specific to machine learning systems, such as performance degradation arising from concept drift where the machine learning model is no longer aligned with the real-world environment. However, the current issue templates that developers use do not seem to capture the information needed. This research seeks to systematically develop a two-way human-machine information exchange protocol to support domain experts, software engineers, and data scientists to collaboratively detect, report, and respond to these new classes of faults.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678592"}, {"primary_key": "2203397", "vector": [], "sparse_vector": [], "title": "DeepCVA: Automated Commit-level Vulnerability Assessment with Deep Multi-task Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is increasingly suggested to identify Software Vulnerabilities (SVs) in code commits to give early warnings about potential security risks. However, there is a lack of effort to assess vulnerability-contributing commits right after they are detected to provide timely information about the exploitability, impact and severity of SVs. Such information is important to plan and prioritize the mitigation for the identified SVs. We propose a novel Deep multi-task learning model, DeepCVA, to automate seven Commit-level Vulnerability Assessment tasks simultaneously based on Common Vulnerability Scoring System (CVSS) metrics. We conduct large-scale experiments on 1,229 vulnerability-contributing commits containing 542 different SVs in 246 real-world software projects to evaluate the effectiveness and efficiency of our model. We show that DeepCVA is the best-performing model with 38% to 59.8% higher Matthews Correlation Coefficient than many supervised and unsupervised baseline models. DeepCVA also requires 6.3 times less training and validation time than seven cumulative assessment models, leading to significantly less model maintenance cost as well. Over-all, DeepCVA presents the first effective and efficient solution to automatically assess SVs early in software systems.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678622"}, {"primary_key": "2203398", "vector": [], "sparse_vector": [], "title": "Log-based Anomaly Detection Without Log Parsing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software systems often record important runtime information in system logs for troubleshooting purposes. There have been many studies that use log data to construct machine learning models for detecting system anomalies. Through our empirical study, we find that existing log-based anomaly detection approaches are significantly affected by log parsing errors that are introduced by 1) OOV (out-of-vocabulary) words, and 2) semantic misunderstandings. The log parsing errors could cause the loss of important information for anomaly detection. To address the limitations of existing methods, we propose NeuralLog, a novel log-based anomaly detection approach that does not require log parsing. NeuralLog extracts the semantic meaning of raw log messages and represents them as semantic vectors. These representation vectors are then used to detect anomalies through a Transformer-based classification model, which can capture the contextual information from log sequences. Our experimental results show that the proposed approach can effectively understand the semantic meaning of log messages and achieve accurate anomaly detection results. Overall, NeuralLog achieves F1-scores greater than 0.95 on four public datasets, outperforming the existing approaches.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678773"}, {"primary_key": "2203399", "vector": [], "sparse_vector": [], "title": "Efficient SMT-Based Model Checking for Signal Temporal Logic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Signal temporal logic (STL) is widely used to specify and analyze properties of cyber-physical systems with continuous behaviors. However, STL model checking is still quite limited, as existing STL model checking methods are either incomplete or very inefficient. This paper presents a new SMT-based model checking algorithm for verifying STL properties of cyber-physical systems. We propose a novel translation technique to reduce the STL bounded model checking problem to the satisfiability of a first-order logic formula over reals, which can be solved using state-of-the-art SMT solvers. Our algorithm is based on a new theoretical result, presented in this paper, to build a small but complete discretization of continuous signals, which preserves the bounded satisfiability of STL. Our translation method allows an efficient STL model checking algorithm that is refutationally complete for bounded signals, and that is much more scalable than the previous refutationally complete algorithm.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678719"}, {"primary_key": "2203400", "vector": [], "sparse_vector": [], "title": "Cross-Lingual Transfer Learning Framework for Program Analysis.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Deep learning-based techniques have been widely applied to program analysis tasks, in fields such as type inference, fault localization, and code summarization. Hitherto deep learning-based software engineering systems rely thoroughly on supervised learning approaches, which require laborious manual effort to collect and label a prohibitively large amount of data. However, most Turing-complete imperative languages share similar control- and data-flow structures, which make it possible to transfer knowledge learned from one language to another. In this paper, we propose a general cross-lingual transfer learning framework PLATO for program analysis by using a series of techniques that are general to different downstream tasks. PLATO allows Bert-based models to leverage prior knowledge learned from the labeled dataset of one language and transfer it to the others. We evaluate our approaches on several downstream tasks such as type inference and code summarization to demonstrate its feasibility.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678848"}, {"primary_key": "2203401", "vector": [], "sparse_vector": [], "title": "Gas Estimation and Optimization for Smart Contracts on Ethereum.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "When users deploy or invoke smart contracts on Ethereum, a fee is charged for avoiding resource abuse. Metered in gas, the fee is the product of the amount of gas used and the gas price. The more gas used indicates a higher transaction fee. In my doctoral research, we aim to investigate two widely studied issues regarding gas, i.e., gas estimation and gas optimization. The former is to predict gas costs for executing a transactions to avoid out-of-gas exceptions, and the latter is to modify existing contracts to save transaction fee. We target some problems that previous work did not solve: gas estimation for loop functions, and gas optimization for storage usage and arrays. We expect that my research can help Ethereum users avoid economical loss for out-of-gas exceptions and pay less transaction fee.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678932"}, {"primary_key": "2203402", "vector": [], "sparse_vector": [], "title": "EditSum: A Retrieve-and-Edit Framework for Source Code Summarization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ge Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing studies show that code summaries help developers understand and maintain source code. Unfortunately, these summaries are often missing or outdated in software projects. Code summarization aims to generate natural language descriptions automatically for source code. According to <PERSON><PERSON> et al., code summaries are highly structured and have repetitive patterns (e.g. \"return true if...\"). Besides the patternized words, a code summary also contains important keywords, which are the key to reflecting the functionality of the code. However, the state-of-the-art approaches perform poorly on predicting the keywords, which leads to the generated summaries suffer a loss in informativeness. To alleviate this problem, this paper proposes a novel retrieve-and-edit approach named EditSum for code summarization. Specifically, EditSum first retrieves a similar code snippet from a pre-defined corpus and treats its summary as a prototype summary to learn the pattern. Then, EditSum edits the prototype automatically to combine the pattern in the prototype with the semantic information of input code. Our motivation is that the retrieved prototype provides a good start-point for post-generation because the summaries of similar code snippets often have the same pattern. The post-editing process further reuses the patternized words in prototype and generates keywords based on the semantic information of input code. We conduct experiments on a large-scale Java corpus (2M) and experimental results demonstrate that EditSum outperforms the state-of-the-art approaches by a substantial margin. The human evaluation also proves the summaries generated by EditSum are more informative and useful. We also verify that EditSum performs well on predicting the patternized words and keywords.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678724"}, {"primary_key": "2203403", "vector": [], "sparse_vector": [], "title": "Understanding and Detecting Performance Bugs in Markdown Compilers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Markdown compilers are widely used for translating plain Markdown text into formatted text, yet they suffer from performance bugs that cause performance degradation and resource exhaustion. Currently, there is little knowledge and understanding about these performance bugs in the wild. In this work, we first conduct a comprehensive study of known performance bugs in Markdown compilers. We identify that the ways Markdown compilers handle the language's context-sensitive features are the dominant root cause of performance bugs. To detect unknown performance bugs, we develop MdPerfFuzz, a fuzzing framework with a syntax-tree based mutation strategy to efficiently generate test cases to manifest such bugs. It equips an execution trace similarity algorithm to de-duplicate the bug reports. With MdPerfFuzz, we successfully identified 216 new performance bugs in real-world Markdown compilers and applications. Our work demonstrates that the performance bugs are a common, severe, yet previously overlooked security problem.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678611"}, {"primary_key": "2203404", "vector": [], "sparse_vector": [], "title": "Metamorphic Testing on Multi-module UAV Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent years have seen a rapid development of machine learning based multi-module unmanned aerial vehicle (UAV) systems. To address the oracle problem in autonomous systems, numerous studies have been conducted to use metamorphic testing to automatically generate test scenes for various modules, e.g., those in self-driving cars. However, as most of the studies are based on unit testing including end-to-end model-based testing, a similar testing approach may not be equally effective for UAV systems where multiple modules are working closely together. Therefore, in this paper, instead of unit testing, we propose a novel metamorphic system testing framework for UAV, named MSTU, to detect the defects in multi-module UAV systems. A preliminary evaluation plan to apply MSTU on an emerging autonomous multi-module UAV system is also presented to demonstrate the feasibility of the proposed testing framework.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678841"}, {"primary_key": "2203405", "vector": [], "sparse_vector": [], "title": "A First Look at the Effect of Deep Learning in Coverage-guided Fuzzing.", "authors": ["<PERSON><PERSON> Li", "<PERSON>", "<PERSON><PERSON><PERSON>", "Yuekang Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Fuzzing has been a widely-used technique for discovering software vulnerabilities. Many existing fuzzers leverage coverage-feedback to evolve seeds to maximize (optimize) program branch coverage. Recently, some techniques propose to train deep learning models to predict the branch coverage of an arbitrary input. Those techniques have proved their success in improving coverage and discovering bugs under different experimental settings. However, deep learning models, usually as a black magic box, are notoriously lack of explanation. Moreover, their performance can be sensitive to the collected runtime coverage information for training, indicating potentially unstable performance. To this end, in this work we conduct a systematic and extensive empirical study on 4 types of deep learning models across 6 projects to reproduce the actual performance of deep learning fuzzers, analyze the advantages and disadvantages of deep learning in the process of fuzzing applications, and explore the future direction of the combination of the two. Our empirical results reveal that the deep learning models can only be effective in very limited scenarios, which is largely restrained by training data imbalance, dependant labels, model over-generalization, and the insufficient expressiveness of the state-of-the-art models. Consequently, the estimated gradients by the models to cover a branch can be less helpful in many scenarios.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678794"}, {"primary_key": "2203407", "vector": [], "sparse_vector": [], "title": "Revisiting Textual Feature of Bug-Triage Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the increase of software users, programmers use issue tracking systems to manage bug reports and researchers propose bug-triage approaches that assign bug reports to programmers. Programmers assign bug reports often according to their descriptions. Based on by this observation, prior approaches typically use classic natural language processing (NLP) to analyze bug reports. Although the technical choice is straightforward, the true effectiveness of this technical choice is largely unknown. Taking a state-of-the-art approach as an example, we explore the impact of textual features in bug triage. By enabling and disabling the textual features of this approach, we analyze their impacts on assigning thousands bug reports from six widely used open source projects. Our result shows that instead of improving it, textual features in fact reduce the effectiveness. In particular, after we turn off its textual features, the f-scores of the baseline approach are improved by 8%. After manual inspection, we find two reasons to explain our result: (1) classic NLP techniques are insufficient to analyze bug reports, because they are not pure natural language texts and contain other elements (e.g., code samples); and (2) some bug reports are poorly written. Our findings reveal a strong need and sufficient opportunities to explore more advanced techniques to handle these complicated elements in bug reports.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678863"}, {"primary_key": "2203408", "vector": [], "sparse_vector": [], "title": "Understanding Code Fragments with Issue Reports.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Code comments are vital for software development and maintenance. To supplement the code comments, researchers proposed various approach that generate code comments. The prior approaches take three sources: (1) programming experience, (2) code-comment pairs in source files, and (3) comments of similar code snippets. Most of their generated comments explain code functionalities, but programmers also need comments that explain why a code fragment was developed as it is. To meet the timely needs, in this paper, we introduce a new source, issue reports (e.g. maintenance types, symptoms, and purposes of modifications), to generate code comments. Issue reports contain rich information on how code was maintained. The valuable details of issue reports are useful to understand source code, especially when programmers learn why code was developed in a specific way. Towards this research direction, we propose the first approach, called ISSUECOMM, that builds the links between code fragments and issue reports. Our results show that it links more than 70% issue numbers that are written by programmers in code comments. Furthermore, the links built by our tool covers 4× bugs, and 10× other issues than the links written in manual comments. We present samples of our built links, and explain why our links are useful to describe the functionalities and the purpose of code.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678864"}, {"primary_key": "2203409", "vector": [], "sparse_vector": [], "title": "An Empirical Study on Obsolete Issue Reports.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Issue reports record valuable maintenance details. Developers write issue numbers into code comprehension and researchers mine knowledge from issue reports to assist various programming tasks. Although issue reports are useful, some of them can be obsolete, in that their corresponding commits are overwritten or rolled back, with the evolution of software. The obsolete issue reports can invalidate their references and descriptions, and can have far-reaching impacts on the approaches built on them. To explore their impacts, we conduct the first empirical study to analyze obsolete issue reports.To measure how an issue report becomes obsolete, we define an obsolete ratio of an issue report as its deleted lines over all its modified lines. To support our analysis, we build a tool, ICLINKER, that builds the links between an issue report and its commits, and calculates the obsolete ratio for each issue report. In our study, we analyze 70,180 commits and 46,257 issue reports that are collected from 5 Apache projects. We explore two research questions, which concern the distributions of obsolete issue reports and the obsolete references in code comments. Our findings to these research questions enrich the knowledge on obsolete issue reports, and some are even counterintuitive. For example, we find that obsolete issue reports are mixed with other issue reports. Even when recent issue reports are obsolete, some old issue reports keep up-to-date.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678543"}, {"primary_key": "2203410", "vector": [], "sparse_vector": [], "title": "Binary Code Similarity Detection.", "authors": ["<PERSON><PERSON>"], "summary": "Binary code similarity detection is to detect the similarity of code at binary (assembly) level without source code. Existing work still has their limitation when dealing with mutated binary code with different compiling options. We proposed a novel approach to address this problem. By inspecting the binary code, we found that generally, within a function, some instructions aim to calculate (prepare) value for some other instructions. The latter instructions are defined by us the key instructions. Currently, we define four categories of key instructions: calling subfunctions, comparing instruction, returning instruction, and memory address writing instruction. Thus if we symbolically execute similar binary codes, the symbolic value at these key instructions should be similar. We implemented our idea into a tool. This prototype tool can: 1. symbolically execute binary code, 2. extract symbolic values at key instructions into a graph, and 3. compare the symbolic graph similarity. In our implementation, we also address some problems, including path explosion and loop handling.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678518"}, {"primary_key": "2203411", "vector": [], "sparse_vector": [], "title": "IFIZZ: Deep-State and Efficient Fault-Scenario Generation to Test IoT Firmware.", "authors": ["<PERSON><PERSON><PERSON>", "Shouling Ji", "<PERSON><PERSON>", "Qinming Dai", "<PERSON><PERSON><PERSON>", "Lirong Fu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "IoT devices are abnormally prone to diverse errors due to harsh environments and limited computational capabilities. As a result, correct error handling is critical in IoT. Implementing correct error handling is non-trivial, thus requiring extensive testing such as fuzzing. However, existing fuzzing cannot effectively test IoT error-handling code. First, errors typically represent corner cases, thus are hard to trigger. Second, testing error-handling code would frequently crash the execution, which prevents fuzzing from testing following deep error paths.In this paper, we propose IFIZZ, a new bug detection system specifically designed for testing error-handling code in Linux-based IoT firmware. IFIZZ first employs an automated binary-based approach to identify realistic runtime errors by analyzing errors and error conditions in closed-source IoT firmware. Then, IFIZZ employs state-aware and bounded error generation to reach deep error paths effectively. We implement and evaluate IFIZZ on 10 popular IoT firmware. The results show that IFIZZ can find many bugs hidden in deep error paths. Specifically, IFIZZ finds 109 critical bugs, 63 of which are even in widely used IoT libraries. IFIZZ also features high code coverage and efficiency, and covers 67.3% more error paths than normal execution. Meanwhile, the depth of error handling covered by IFIZZ is 7.3 times deeper than that covered by the state-of-the-art method. Furthermore, IFIZZ has been practically adopted and deployed in a worldwide leading IoT company. We will open-source IFIZZ to facilitate further research in this area.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678785"}, {"primary_key": "2203413", "vector": [], "sparse_vector": [], "title": "End-to-End Automation of Feedback on Student Assembly Programs.", "authors": ["<PERSON><PERSON><PERSON>", "Tin<PERSON><PERSON> Liu", "<PERSON>", "Wenqing Luo", "<PERSON>"], "summary": "We developed a set of tools designed to provide rapid feedback to students as they learn to write programs in assembly language (LC-3, a RISC-like educational instruction set architecture). At the heart of the system is an extended version of KLEE, KLC3, that enables us to both identify issues and perform equivalence checking between student code and a gold (correct) version of each assignment. Feedback begins when students edit their code using a VSCode extension that leverages static analysis to perform a variety of correctness and style checks, encouraging students to improve their code quality. Each time a student commits code to their Git repository, our system triggers. Using KLC3 (KLEE), the student code is executed along with the gold version, and issues and behavioral differences are delivered back to the student through their Git repository as a human-readable report, test cases, and scripts. A queueing system allows students to monitor progress, but responses are generally available within minutes. We also extended the LC-3 simulation tools to support reverse debugging, making the process of finding complex bugs much more tractable for students, and used Emscripten to develop a browser-based interface for use in testing and debugging. Finally, our system maintains an individual regression test suite for each student and requires a submission to pass all previous tests before re-evaluation in KLC3, thus avoiding encouraging programming-by-guesswork. We deployed the system to provide feedback for the assembly programming assignments in a class of over 100 students in Fall 2020. Students wrote a median of around 700 lines of assembly for these assignments, making heavy use of our tools to understand and eliminate their bugs. Anonymous student feedback on the tools was uniformly positive. Since that semester, we have continued to refine and expand our tools' analysis capabilities and performance, and plan to deploy the system again in the near future (the class is offered every Fall).", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678837"}, {"primary_key": "2203414", "vector": [], "sparse_vector": [], "title": "Can Neural Clone Detection Generalize to Unseen Functionalitiesƒ.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many recently proposed code clone detectors exploit neural networks to capture latent semantics of source code, thus achieving impressive results for detecting semantic clones. These neural clone detectors rely on the availability of large amounts of labeled training data. We identify a key oversight in the current evaluation methodology for neural clone detection: cross-functionality generalization (i.e., detecting semantic clones of which the functionalities are unseen in training). Specifically, we focus on this question: do neural clone detectors truly learn the ability to detect semantic clones, or they just learn how to model specific functionalities in training data while cannot generalize to realistic unseen functionalitiesƒ This paper investigates how the generalizability can be evaluated and improved.Our contributions are 3-folds: (1) We propose an evaluation methodology that can systematically measure the cross-functionality generalizability of neural clone detection. Based on this evaluation methodology, an empirical study is conducted and the results indicate that current neural clone detectors cannot generalize well as expected. (2) We conduct empirical analysis to understand key factors that can impact the generalizability. We investigate 3 factors: training data diversity, vocabulary, and locality. Results show that the performance loss on unseen functionalities can be reduced through addressing the out-of-vocabulary problem and increasing training data diversity. (3) We propose a human-in-the-loop mechanism that help adapt neural clone detectors to new code repositories containing lots of unseen functionalities. It improves annotation efficiency with the combination of transfer learning and active learning. Experimental results show that it reduces the amount of annotations by about 88%. Our code and data are publicly available 1 .", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678907"}, {"primary_key": "2203415", "vector": [], "sparse_vector": [], "title": "Detecting TensorFlow Program Bugs in Real-World Industrial Environment.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON>g <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Jingling Xue"], "summary": "Deep learning has been widely adopted in industry and has achieved great success in a wide range of application areas. Bugs in deep learning programs can cause catastrophic failures, in addition to a serious waste of resources and time.This paper aims at detecting industrial TensorFlow program bugs. We report an extensive empirical study on 12,289 failed TensorFlow jobs, showing that existing static tools can effectively detect 72.55% of the top three types of Python bugs in industrial TensorFlow programs. In addition, we propose (for the first time) a constraint-based approach for detecting TensorFlow shape-related errors (one of the most common TensorFlow-specific bugs), together with an associated tool, ShapeTracer. Our evaluation on a set of 60 industrial TensorFlow programs shows that ShapeTracer is efficient and effective: it analyzes each program in at most 3 seconds and detects effectively 40 out of 60 industrial TensorFlow program bugs, with no false positives. ShapeTracer has been deployed in the platform-X platform and will be released soon.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678891"}, {"primary_key": "2203416", "vector": [], "sparse_vector": [], "title": "InstruGuard: Find and Fix Instrumentation Errors for Coverage-based Greybox Fuzzing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Purui Su", "<PERSON><PERSON>", "Xiangkun <PERSON>"], "summary": "As one of the most successful methods at vulnerability discovery, coverage-based greybox fuzzing relies on the lightweight compile-time instrumentation to achieve the fine-grained coverage feedback of the target program. Researchers improve it by optimizing the coverage metrics without questioning the correctness of the instrumentation. However, instrumentation errors, including missed instrumentation locations and redundant instrumentation locations, harm the ability of fuzzers. According to our experiments, it is a common and severe problem in various coverage-based greybox fuzzers and at different compiler optimization levels.In this paper, we design and implement InstruGuard, an open-source and pragmatic platform to find and fix instrumentation errors. It detects instrumentation errors by static analysis on target binaries, and fixes them with a general solution based on binary rewriting. To study the impact of instrumentation errors and test our solutions, we built a dataset of 15 real-world programs and selected 6 representative fuzzers as targets. We used InstruGuard to check and repair the instrumented binaries with different fuzzers and different compiler optimization options. To evaluate the effectiveness of the repair, we ran the fuzzers with original instrumented programs and the repaired ones, and compared the fuzzing results from aspects of execution paths, line coverage, and real bug findings. The results showed that InstruGuard had corrected the instrumentation errors of different fuzzers and helped to find more bugs in the dataset. Moreover, we discovered one new zero-day vulnerability missed by other fuzzers with fixed instrumentation but without any changes to the fuzzers.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678671"}, {"primary_key": "2203417", "vector": [], "sparse_vector": [], "title": "Characterizing Transaction-Reverting Statements in Ethereum Smart Contracts.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Smart contracts are programs stored on blockchains to execute transactions. When input constraints or security properties are violated at runtime, the transaction being executed by a smart contract needs to be reverted to avoid undesirable consequences. On Ethereum, the most popular blockchain that supports smart contracts, developers can choose among three transaction-reverting statements (i.e., require, if…revert, and if…throw) to handle anomalous transactions. While these transaction-reverting statements are vital for preventing smart contracts from exhibiting abnormal behaviors or suffering malicious attacks, there is limited understanding of how they are used in practice. In this work, we perform the first empirical study to characterize transaction-reverting statements in Ethereum smart contracts. We measured the prevalence of these statements in 3,866 verified smart contracts from popular dapps and built a taxonomy of their purposes via manually analyzing 557 transaction-reverting statements. We also compared template contracts and their corresponding custom contracts to understand how developers customize the use of transaction-reverting statements. Finally, we analyzed the security impact of transaction-reverting statements by removing them from smart contracts and comparing the mutated contracts against the original ones. Our study led to important findings. For example, we found that transaction-reverting statements are commonly used to perform seven types of authority verifications or validity checks, and missing such statements may compromise the security of smart contracts. We also found that current smart contract security analyzers cannot effectively handle transaction-reverting statements when detecting security vulnerabilities. Our findings can shed light on further research in the broad area of smart contract quality assurance and provide practical guidance to smart contract developers on the appropriate use of transaction-reverting statements.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678597"}, {"primary_key": "2203418", "vector": [], "sparse_vector": [], "title": "FirmGuide: Boosting the Capability of Rehosting Embedded Linux Kernels through Model-Guided Kernel Execution.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Shen", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Linux kernel is widely used in embedded systems. To understand practical threats to the Linux kernel, we need to perform dynamic analysis with a full-system emulator, e.g., QEMU. However, due to hardware fragmentation, e.g., various types of peripherals, most embedded systems are not currently supported by QEMU. Though some progress has been made on rehosting firmware, it mainly focuses on user space programs or simple real-time operating systems.The goal of this work is to boost the capability of rehosting the embedded Linux kernels in QEMU. By doing so, dynamic analysis systems can be firstly applied on embedded Linux kernels by leveraging off-the-shelf tools upon QEMU. Accordingly, we proposed a new technique called model-guided kernel execution. It combines the peripheral abstractions in the Linux kernel and kernel-peripheral interactions to semi-automatically generate peripheral models that are then used to synthesize new QEMU virtual machines to start the dynamic analysis.We have implemented a prototype called FirmGuide. It generates 9 peripheral models with full functionality and 64 with minimum functionality covering 26 SoCs. Our evaluation with 6,188 firmware images shows that it can successfully rehost more than 95% of Linux kernels in 2 architectures and 22 versions. None of them can be rehosted in the vanilla QEMU. The result of the LTP benchmark shows the reliability and robustness of the rehosted Linux kernels. We further conduct two security applications, i.e., vulnerability analysis and fuzzing, on the rehosted Linux kernels to demonstrate the usage scenarios.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678653"}, {"primary_key": "2203419", "vector": [], "sparse_vector": [], "title": "Targeting Requirements Violations of Autonomous Driving Systems by Dynamic Evolutionary Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Autonomous Driving Systems (ADSs) are complex systems that must satisfy multiple requirements such as safety, compliance to traffic rules, and comfortableness. However, satisfying all these requirements may not always be possible due to emerging environmental conditions. Therefore, the ADSs may have to make trade-offs among multiple requirements during the ongoing operation, resulting in one or more requirements violations. For ADS engineers, it is highly important to know which combinations of requirements violations may occur, as different combinations can expose different types of failures. However, there is currently no testing approach that can generate scenarios to expose different combinations of requirements violations. To address this issue, in this paper, we introduce the notion of requirements violation pattern to characterize a specific combination of requirements violations. Based on this notion, we propose a testing approach named EMOOD that can effectively generate test scenarios to expose as many requirements violation patterns as possible. EMOOD uses a prioritization technique to sort all possible patterns to search for, from the most to the least critical ones. Then, EMOOD iteratively includes an evolutionary many-objective optimization algorithm to find different combinations of requirements violations. In each iteration, the targeted pattern is determined by a dynamic prioritization technique to give preferences to those patterns with higher criticality and higher likelihood to occur. We apply EMOOD to an industrial ADS under two common traffic situations. Evaluation results show that EMOOD outperforms three baseline approaches in generating test scenarios by discovering more requirements violation patterns.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678883"}, {"primary_key": "2203420", "vector": [], "sparse_vector": [], "title": "Detecting Memory-Related Bugs by Tracking Heap Memory Management of C++ Smart Pointers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The smart pointer mechanism, which is improved in the continuous versions of the C++ standards over the last decade, is designed to prevent memory-leak bugs by automatically deallocating the managed memory blocks. However, not all kinds of memory errors can be immunized by adopting this mechanism. For example, dereferencing a null smart pointer will lead to a software failure. Due to the lack of specialized support for smart pointers, the off-the-shelf C++ static analyzers cannot effectively reveal these bugs.In this paper, we propose a static approach to detecting memory-related bugs by tracking the heap memory management of smart pointers. The behaviors of smart pointers are modeled during their lifetime to trace the state transitions of managed memory blocks. And the specially designed checkers are used to check the state changes according to five collected bug patterns. To evaluate the effectiveness of our approach, we implement it on the top of the Clang Static Analyzer. A set of handmade code snippets, as well as nine popular open-source C++ projects, are used to compare our tool against four other analyzers. The results show that our approach can successfully discover nearly all the built-in bugs. And 442 out of 648 reports generated from the open-source projects are true positives after manual reviewing, where the bugs of dereferencing null smart pointers are most frequently reported. To further confirm our reports, we design patches for Aria2, Restbed, MySQL and LLVM, in which seven pull requests covering 76 bug reports have been merged by the developers up to now. The results indicate that pointers should always be carefully used even after migrated to smart pointers and static analysis upon specialized models can effectively detect such bugs.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678836"}, {"primary_key": "2203422", "vector": [], "sparse_vector": [], "title": "Log Anomaly to Resolution: AI Based Proactive Incident Remediation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Based on 2020 SRE report, 80% of SREs work on postmortem analysis of incidents due to lack of provided information and 16% of toil come from investigating false positives/negatives. As a cloud service provider, the desire is to proactively identify signals that can help reduce outages and/or reduce the mean time to resolution. By leveraging AI for Operations (AIOps), this work proposes a novel methodology for proactive identification of log anomalies and its resolutions by sifting through the log lines. Typically, relevant information to retrieve resolutions corresponding to logs is spread across multiple heterogeneous corpora that exist in silos, namely historical ticket data, historical log data, and symptom resolution available in product documentation, for example. In this paper, we focus on augmented dataset preparation from multiple heterogeneous corpora, metadata selection and prediction, and finally, using these elements during run-time to retrieve contextual resolutions for signals triggered via logs. For early evaluation, we used logs from a production middleware application server, predicted log anomalies and their resolutions, and conducted qualitative evaluation with subject matter experts; the accuracy of metadata prediction and resolution retrieval are 78.57% and 65.7%, respectively.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678815"}, {"primary_key": "2203423", "vector": [], "sparse_vector": [], "title": "API Compatibility Issue Detection, Testing and Analysis for Android Apps.", "authors": ["<PERSON><PERSON>"], "summary": "Android apps are developed using a Software Development Kit (SDK), where the Android application programming interface (API) enables app developers to harness the functionalities of Android devices by interacting with services and hardware. However, API frequently evolves together with its associated SDK. The mismatch between the API level supported by the device where apps are installed and the API level targeted by app developers can induce compatibility issues. These issues can manifest themselves as unexpected behaviors, including runtime crashes, creating a poor user experience. Recent studies investigated API evolution to ensure the reliability of the Android apps, however, they require improvements. This work aims to establish novel methodologies that will improve the state-of-the-art compatibility issue detection and testing approaches.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678812"}, {"primary_key": "2203424", "vector": [], "sparse_vector": [], "title": "Using Version Control and Issue Tickets to detect Code Debt and Economical Cost.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite the fact that there are numerous classifications of technical debt based on various criteria, Code Debt or code smells is a category that appears in the majority of current research. One of the primary causes of code debt is the urgency to deliver software quickly, as well as bad coding practices. Among many approaches, static code analysis has received the most attention in studies to detect code-smell/code debt. However, most of them examine the same programming language, although today's software company utilizes many development stacks with various languages and tools. This problem can be resolved by detecting code debt with Issue/Ticket cards. This paper presents a method for detecting code debt leveraging natural language processing on issue tickets. It also proposes a method for calculating the average amount of time that a code debt was present in the software. This method is implemented utilizing git mining.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678532"}, {"primary_key": "2203425", "vector": [], "sparse_vector": [], "title": "Learning GraphQL Query Cost.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "GraphQL is a query language for APIs and a runtime for executing those queries, fetching the requested data from existing microservices, REST APIs, databases, or other sources. Its expressiveness and its flexibility have made it an attractive candidate for API providers in many industries, especially through the web. A major drawback to blindly servicing a client's query in GraphQL is that the cost of a query can be unexpectedly large, creating computation and resource overload for the provider, and API rate-limit overages and infrastructure overload for the client. To mitigate these drawbacks, it is necessary to efficiently estimate the cost of a query before executing it. Estimating query cost is challenging, because GraphQL queries have a nested structure, GraphQL APIs follow different design conventions, and the underlying data sources are hidden. Estimates based on worst-case static query analysis have had limited success because they tend to grossly overestimate cost. We propose a machine-learning approach to efficiently and accurately estimate the query cost. We also demonstrate the power of this approach by testing it on query-response data from publicly available commercial APIs. Our framework is efficient and predicts query costs with high accuracy, consistently outperforming the static analysis by a large margin.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678513"}, {"primary_key": "2203426", "vector": [], "sparse_vector": [], "title": "Muskit: A Mutation Analysis Tool for Quantum Software Testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Given that quantum software testing is a new area of research, there is a lack of benchmark programs and bugs repositories to assess the effectiveness of testing techniques. To this end, quantum mutation analysis focuses on systematically generating faulty versions of Quantum Programs (QPs), called mutants, using mutation operators. Such mutants can be used as benchmarks to assess the quality of test cases in a test suite. Thus, we present Muskit - a quantum mutation analysis tool for QPs coded in IBM's Qiskit language. Muskit defines mutation operators on gates of QPs and selection criteria to reduce the number of mutants to generate. Moreover, it allows for the execution of test cases on mutants and generation of results for test analyses. Muskit is provided as command line interface, GUI, and web application. We validated <PERSON><PERSON><PERSON> by using it to generate and execute mutants for four QPs. Muskit code: https://github.com/Simula-COMPLEX/muskitWeb app: https://qiskitmutantcreatorsrl.pythonanywhere.com/YouTube Video: EbPHJOK_AEA Artifact Available: https://doi.org/10.5281/zenodo.5288917", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678563"}, {"primary_key": "2203427", "vector": [], "sparse_vector": [], "title": "Binary Diffing as a Network Alignment Problem via Belief Propagation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we address the problem of finding a correspondence, or matching, between the functions of two programs in binary form, which is one of the most common task in binary diffing. We introduce a new formulation of this problem as a particular instance of a graph edit problem over the call graphs of the programs. In this formulation, the quality of a mapping is evaluated simultaneously with respect to both function content and call graph similarities. We show that this formulation is equivalent to a network alignment problem. We propose a solving strategy for this problem based on max-product belief propagation. Finally, we implement a prototype of our method, called QBinDiff, and propose an extensive evaluation which shows that our approach outperforms state of the art diffing tools.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678782"}, {"primary_key": "2203429", "vector": [], "sparse_vector": [], "title": "HyperGI: Automated Detection and Repair of Information Flow Leakage.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Maintaining confidential information control in soft-ware is a persistent security problem where failure means secrets can be revealed via program behaviors. Information flow control techniques traditionally have been based on static or symbolic analyses — limited in scalability and specialized to particular languages. When programs do leak secrets there are no approaches to automatically repair them unless the leak causes a functional test to fail. We present our vision for HyperGI, a genetic improvement framework that detects, localizes and repairs information leakage. Key elements of HyperGI include (1) the use of two orthogonal test suites, (2) a dynamic leak detection approach which estimates and localizes potential leaks, and (3) a repair component that produces a candidate patch using genetic improvement. We demonstrate the successful use of HyperGI on several programs with no failing functional test cases. We manually examine the resulting patches and identify trade-offs and future directions for fully realizing our vision.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678758"}, {"primary_key": "2203430", "vector": [], "sparse_vector": [], "title": "Learning-based Assistant for Data Migration of Enterprise Information Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data migration from source to target information system is a critical step for modernizing information systems. Central to data migration is data transform that transforms the source system data into target system. In this paper we present a tool that assists the experts in creating the data transformation specification by (a) suggesting candidate field matches between the source and target data models using machine learning and knowledge representation, and (b) rules for the data transformation using program synthesis. It takes the expert's feedback for the identified matches and synthesized rules and proposes new matches and transformation rules. We have executed our tool on real-life industrial data. Our schema matching recall at 5 is 0.76, while for the rule generator recall at 2 is 0.81.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678533"}, {"primary_key": "2203431", "vector": [], "sparse_vector": [], "title": "Data-Driven Design and Evaluation of SMT Meta-Solving Strategies: Balancing Performance, Accuracy, and Cost.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many modern software engineering tools integrate SMT decision procedures and rely on the accuracy and performance of SMT solvers. We describe four basic patterns for integrating constraint solvers (earliest verdict, majority vote, feature-based solver selection, and verdict-based second attempt) that can be used for combining individual solvers into meta-decision procedures that balance accuracy, performance, and cost – or optimize for one of these metrics. In order to evaluate the effectiveness of meta-solving, we analyze and minimize 16 existing benchmark suites and benchmark seven state-of-the-art SMT solvers on 17k unique instances. From the obtained performance data, we can estimate the performance of different meta-solving strategies. We validate our results by implementing and analyzing two strategies. As additional results, we obtain (a) the first benchmark suite of unique SMT string problems with validated expected verdicts, (b) an extensive dataset containing data on benchmark instances as well as on the performance of individual decision procedures and several meta-solving strategies on these instances, and (c) a framework for generating data that can easily be used for similar analyses on different benchmark instances or for different decision procedures.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678881"}, {"primary_key": "2203434", "vector": [], "sparse_vector": [], "title": "Human-in-the-Loop XAI-enabled Vulnerability Detection, Investigation, and Mitigation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The need for cyber resilience is increasingly important in our technology-dependent society, where computing systems, devices and data will continue to be the target of cyber attackers. Hence, we propose a conceptual framework called 'Human-in-the-Loop Explainable-AI-Enabled Vulnerability Detection, Investigation, and Mitigation' (HXAI-VDIM). Specifically, instead of resolving complex scenario of security vulnerabilities as an output of an AI/ML model, we integrate the security analyst or forensic investigator into the man-machine loop and leverage explainable AI (XAI) to combine both AI and Intelligence Assistant (IA) to amplify human intelligence in both proactive and reactive processes. Our goal is that HXAI-VDIM integrates human and machine in an interactive and iterative loop with security visualization that utilizes human intelligence to guide the XAI-enabled system and generate refined solutions.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678840"}, {"primary_key": "2203435", "vector": [], "sparse_vector": [], "title": "GenTree: Inferring Configuration Interactions using Decision Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we demonstrate the implementation details and usage of GenTree, a dynamic analysis tool for learning a program's interactions. Configurable software systems, while providing more flexibility to the users, are harder to develop, test, and analyze. GenTree can efficiently analyze the interactions among configuration options in configurable software. These interactions compactly represent large sets of configurations and thus allow us to efficiently analyze and discover interesting properties (e.g., bugs) in configurable software. Our experiments on 17 configurable systems spanning 4 languages show that GenTree efficiently finds precise interactions using a tiny fraction of the configuration space. GenTree and its dataset are open source and available at https://github.com/unsat/gentree and a video demo is at https://youtu.be/x3eqUflvlN8.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678676"}, {"primary_key": "2203436", "vector": [], "sparse_vector": [], "title": "Adversarial Attacks to API Recommender Systems: Time to Wake Up and Smell the Coffeeƒ.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recommender systems in software engineering provide developers with a wide range of valuable items to help them complete their tasks. Among others, API recommender systems have gained momentum in recent years as they became more successful at suggesting API calls or code snippets. While these systems have proven to be effective in terms of prediction accuracy, there has been less attention for what concerns such recommenders' resilience against adversarial attempts. In fact, by crafting the recommenders' learning material, e.g., data from large open-source software (OSS) repositories, hostile users may succeed in injecting malicious data, putting at risk the software clients adopting API recommender systems. In this paper, we present an empirical investigation of adversarial machine learning techniques and their possible influence on recommender systems. The evaluation performed on three state-of-the-art API recommender systems reveals a worrying outcome: all of them are not immune to malicious data. The obtained result triggers the need for effective countermeasures to protect recommender systems against hostile attacks disguised in training data.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678946"}, {"primary_key": "2203441", "vector": [], "sparse_vector": [], "title": "Thinking Like a Developer? Comparing the Attention of Humans with Neural Models of Code.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Neural models of code are successfully tackling various prediction tasks, complementing and sometimes even outperforming traditional program analyses. While most work focuses on end-to-end evaluations of such models, it often remains unclear what the models actually learn, and to what extent their reasoning about code matches that of skilled humans. A poor understanding of the model reasoning risks deploying models that are right for the wrong reason, and taking decisions based on spurious correlations in the training dataset. This paper investigates to what extent the attention weights of effective neural models match the reasoning of skilled humans. To this end, we present a methodology for recording human attention and use it to gather 1,508 human attention maps from 91 participants, which is the largest such dataset we are aware of. Computing human-model correlations shows that the copy attention of neural models often matches the way humans reason about code (Spearman rank coefficients of 0.49 and 0.47), which gives an empirical justification for the intuition behind copy attention. In contrast, the regular attention of models is mostly uncorrelated with human attention. We find that models and humans sometimes focus on different kinds of tokens, e.g., strings are important to humans but mostly ignored by models. The results also show that human-model agreement positively correlates with accurate predictions by a model, which calls for neural models that even more closely mimic human reasoning. Beyond the insights from our study, we envision the release of our dataset of human attention maps to help understand future neural models of code and to foster work on human-inspired models.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678712"}, {"primary_key": "2203442", "vector": [], "sparse_vector": [], "title": "Automating Developer Chat Mining.", "authors": ["<PERSON><PERSON><PERSON> Pan", "Ling<PERSON> Bao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Online chatrooms are gaining popularity as a communication channel between widely distributed developers of Open Source Software (OSS) projects. Most discussion threads in chatrooms follow a Q&A format, with some developers (askers) raising an initial question and others (respondents) joining in to provide answers. These discussion threads are embedded with rich information that can satisfy the diverse needs of various OSS stakeholders. However, retrieving information from threads is challenging as it requires a thread-level analysis to understand the context. Moreover, the chat data is transient and unstructured, consisting of entangled informal conversations. In this paper, we address this challenge by identifying the information types available in developer chats and further introducing an automated mining technique. Through manual examination of chat data from three chatrooms on Gitter, using card sorting, we build a thread-level taxonomy with nine information categories and create a labeled dataset with 2,959 threads. We propose a classification approach (named F2CHAT) to structure the vast amount of threads based on the information type automatically, helping stakeholders quickly acquire their desired information. F2CHAT effectively combines handcrafted non-textual features with deep textual features extracted by neural models. Specifically, it has two stages with the first one leveraging the siamese architecture to pretrain the textual feature encoder, and the second one facilitating an in-depth fusion of two types of features. Evaluation results suggest that our approach achieves an average F1-score of 0.628, which improves the baseline by 57%. Experiments also verify the effectiveness of our identified non-textual features under both intra-project and cross-project validations.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678923"}, {"primary_key": "2203443", "vector": [], "sparse_vector": [], "title": "JSTAR: JavaScript Specification Type Analyzer using Refinement.", "authors": ["Jihyeok Park", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sukyoung Ryu"], "summary": "JavaScript is one of the mainstream programming languages for client-side programming, server-side programming, and even embedded systems. Various JavaScript engines developed and maintained in diverse fields must conform to the syntax and semantics described in ECMAScript, the standard specification of JavaScript. Since an incorrect description in ECMAScript can lead to wrong JavaScript engine implementations, checking the correctness of ECMAScript is critical and essential. However, all the specification updates are currently manually reviewed by the Ecma Technical Committee 39 (TC39) without any automated tools. Moreover, in late 2014, the committee announced the yearly release cadence and open development process of ECMAScript to quickly adapt to evolving development environments. Because of such frequent updates, checking the correctness of ECMAScript becomes more labor-intensive and error-prone.To alleviate the problem, we propose JSTAR, a JavaScript Specification Type Analyzer using Refinement. It is the first tool that performs type analysis on JavaScript specifications and detects specification bugs using a bug detector. For a given specification, JSTAR first compiles each abstract algorithm written in a structured natural language to a corresponding function in IRES, an untyped intermediate representation for ECMAScript. Then, it performs type analysis for compiled functions with specification types defined in ECMAScript. Based on the result of type analysis, JSTAR detects specification bugs using a bug detector consisting of four checkers. To increase the precision of the type analysis, we present condition-based refinement for type analysis, which prunes out infeasible abstract states using conditions of assertions and branches. We evaluated JSTAR with all 864 versions in the official ECMAScript repository for the recent three years from 2018 to 2021. JSTAR took 137.3 seconds on average to perform type analysis for each version, and detected 157 type-related specification bugs with 59.2% precision; 93 out of 157 bugs are true bugs. Among them, 14 bugs are newly detected by JSTAR, and the committee confirmed them all.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678781"}, {"primary_key": "2203444", "vector": [], "sparse_vector": [], "title": "Decoupling Server and Client Code Through Cloud-Native Domain-Specific Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>nni J. Adenuga"], "summary": "Simple domain-specific graphical languages and libraries can empower a variety of users to create application behavior and logic. However, it remains challenging to produce and maintain a heterogeneous set of client applications based on these descriptions, as each client typically requires the developers to both understand and embed the domain-specific logic. This is because application logic must be encoded to some extent in both the server and client sides.In this paper, we propose an alternative approach, which allows the specification of application logic to reside solely on the cloud. In our system, reusable application components are assembled on the cloud in different logical chains and the client is solely concerned with how data is displayed and gathered from users. In this way, the chaining of requests and responses is done by the cloud and the client side has no knowledge of the application logic. This means that the experts in the domain build modular cloud components, arrange them in logical chains, generate a simple user interface, and later leave it to client-side developers to customize the presentation.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678747"}, {"primary_key": "2203446", "vector": [], "sparse_vector": [], "title": "FixMe: A GitHub Bot for Detecting and Monitoring On-Hold Self-Admitted Technical Debt.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Morakot Choetkiertikul", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hidea<PERSON>", "<PERSON><PERSON>"], "summary": "Self-Admitted Technical Debt (SATD) is a special form of technical debt in which developers intentionally record their hacks in the code by adding comments for attention. Here, we focus on issue-related \"On-hold SATD\", where developers suspend proper implementation due to issues reported inside or outside the project. When the referenced issues are resolved, the On-hold SATD also need to be addressed, but since monitoring these issue reports takes a lot of time and effort, developers may not be aware of the resolved issues and leave the On-hold SATD in the code. In this paper, we propose FixMe, a GitHub bot that helps developers detecting and monitoring On-hold SATD in their repositories and notify them whenever the On-hold SATDs are ready to be fixed (i.e. the referenced issues are resolved). The bot can automatically detect On-hold SATD comments from source code using machine learning techniques and discover referenced issues. When the referenced issues are resolved, developers will be notified by FixMe bot. The evaluation conducted with 11 participants shows that our FixMe bot can support them in dealing with On-hold SATD. FixMe is available at https://www.fixmebot.app/ and FixMe's VDO is at https://youtu.be/YSz9kFxN_YQ.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678680"}, {"primary_key": "2203447", "vector": [], "sparse_vector": [], "title": "DEVIATE: A Deep Learning Variance Testing Framework.", "authors": ["Hung V<PERSON> Pham", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning (DL) training is nondeterministic and such nondeterminism was shown to cause significant variance of model accuracy (up to 10.8%). Such variance may affect the validity of the comparison of newly proposed DL techniques with baselines. To ensure such validity, DL researchers and practitioners must replicate their experiments multiple times with identical settings to quantify the variance of the proposed approaches and baselines. Replicating and measuring DL variances reliably and efficiently is challenging and understudied.We propose a ready-to-deploy framework DEVIATE that (1) measures DL training variance of a DL model with minimal manual efforts, and (2) provides statistical tests of both accuracy and variance. Specifically, DEVIATE automatically analyzes the DL training code and extracts monitored important metrics (such as accuracy and loss). In addition, DEVIATE performs popular statistical tests and provides users with a report of statistical p-values and effect sizes along with various confidence levels when comparing to selected baselines.We demonstrate the effectiveness of DEVIATE by performing case studies with adversarial training. Specifically, for an adversarial training process that uses the Fast Gradient Signed Method to generate adversarial examples as the training data, DEVIATE measures a max difference of accuracy among 8 identical training runs with fixed random seeds to be up to 5.1%.Tool and demo links: https://github.com/lin-tan/DEVIATE", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678540"}, {"primary_key": "2203448", "vector": [], "sparse_vector": [], "title": "Towards Systematic and Dynamic Task Allocation for Collaborative Parallel Fuzzing.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Quang-Trung Ta", "<PERSON>", "<PERSON>"], "summary": "Parallel coverage-guided greybox fuzzing is the most common setup for vulnerability discovery at scale. However, so far it has received little attention from the research community compared to single-mode fuzzing, leaving open several problems particularly in its task allocation strategies. Current approaches focus on managing micro tasks, at the seed input level, and their task division algorithms are either ad-hoc or static. In this paper, we leverage research on graph partitioning and search algorithms to propose a systematic and dynamic task allocation solution that works at the macro-task level. First, we design an attributed graph to capture both the program structures (e.g., program call graph) and fuzzing information (e.g., branch hit counts, bug discovery probability). Second, our graph partitioning algorithm divides the global program search space into sub-search-spaces. Finally our search algorithm prioritizes these sub-search-spaces (i.e., tasks) and explores them to maximize code coverage and number of bugs found. The results are collected to update the graph and guide further iterations of partitioning and exploration. We implemented a prototype tool called AFLTeam. In our preliminary experiments on well-tested benchmarks, AFLTeam achieved higher code coverage (up to 16.4% branch coverage improvement) compared to the default parallel mode of AFL and discovered 2 zero-day bugs in FFmpeg and JasPer toolkits.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678810"}, {"primary_key": "2203450", "vector": [], "sparse_vector": [], "title": "PyExplainer: Explaining the Predictions of Just-In-Time Defect Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Just-In-Time (JIT) defect prediction (i.e., an AI/ML model to predict defect-introducing commits) is proposed to help developers prioritize their limited Software Quality Assurance (SQA) resources on the most risky commits. However, the explainability of JIT defect models remains largely unexplored (i.e., practitioners still do not know why a commit is predicted as defect-introducing). Recently, LIME has been used to generate explanations for any AI/ML models. However, the random perturbation approach used by LIME to generate synthetic neighbors is still suboptimal, i.e., generating synthetic neighbors that may not be similar to an instance to be explained, producing low accuracy of the local models, leading to inaccurate explanations for just-in-time defect models. In this paper, we propose PyExplainer—i.e., a local rule-based model-agnostic technique for generating explanations (i.e., why a commit is predicted as defective) of JIT defect models. Through a case study of two open-source software projects, we find that our PyExplainer produces (1) synthetic neighbors that are 41%-45% more similar to an instance to be explained; (2) 18%-38% more accurate local models; and (3) explanations that are 69%-98% more unique and 17%-54% more consistent with the actual characteristics of defect-introducing commits in the future than LIME (a state-ofthe-art model-agnostic technique). This could help practitioners focus on the most important aspects of the commits to mitigate the risk of being defect-introducing. Thus, the contributions of this paper build an important step towards Explainable AI for Software Engineering, making software analytics more explainable and actionable. Finally, we publish our PyExplainer as a Python package to support practitioners and researchers (https://github.com/awsm-research/PyExplainer).", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678763"}, {"primary_key": "2203451", "vector": [], "sparse_vector": [], "title": "Quality analysis of mobile applications with special focus on security aspects.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Smart phones and mobile apps have become an essential part of our daily lives. It is necessary to ensure the quality of these apps. Two important aspects of code quality are maintainability and security. The goals of my PhD project are (1) to study code smells, security issues and their evolution in iOS apps and frameworks, (2) to enhance training and teaching using visualisation support, and (3) to support developers in automatically detecting dependencies to vulnerable library elements in their apps. For each of the three tools, dedicated tool support will be provided, i.e., GraphifyEvolution, VisualiseEvolution, and DependencyEvolution respectively. The tool GraphifyEvolution exists and has been applied to analyse code smells in iOS apps written in Swift. The tool has a modular architecture and can be extended to add support for additional languages and external analysis tools. In the remaining two years of my PhD studies, I will complete the other two tools and apply them in case studies with developers in industry as well as in university teaching.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678681"}, {"primary_key": "2203453", "vector": [], "sparse_vector": [], "title": "How Can Subgroup Discovery Help AIOps?", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The genuine supervision of modern IT systems brings new challenges as it requires higher standards of scalability, reliability and efficiency when analysing and monitoring big data streams. Rule-based inference engines are a key component of maintenance systems in detecting anomalies and automating their resolution. However, they remain confined to simple and general rules and cannot handle the huge amount of data, nor the large number of alerts raised by IT systems, a lesson learned from expert systems era. Artificial Intelligence for Operation Systems (AIOps) proposes to take advantage of advanced analytics and machine learning on big data to improve and automate every step of supervision systems and aid incident management in detecting outages, identifying root causes and applying appropriate healing actions. Nevertheless, the best AIOps techniques rely on opaque models, strongly limiting their adoption. As a part of this PhD thesis, we study how Subgroup Discovery can help AIOps. This promising data mining technique offers possibilities to extract interesting hypothesis from data and understand the underlying process behind predictive models. To ensure relevancy of our propositions, this project involves both data mining researchers and practitioners from Infologic, a French software editor.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678697"}, {"primary_key": "2203454", "vector": [], "sparse_vector": [], "title": "&quot;What makes my queries slow?&quot;: Subgroup Discovery for SQL Workload Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Among daily tasks of database administrators (DBAs), the analysis of query workloads to identify schema issues and improving performances is crucial. Although DBAs can easily pinpoint queries repeatedly causing performance issues, it remains challenging to automatically identify subsets of queries that share some properties only (a pattern) and simultaneously foster some target measures, such as execution time. Patterns are defined on combinations of query clauses, environment variables, database alerts and metrics and help answer questions like what makes SQL queries slow? What makes I/O communications high? Automatically discovering these patterns in a huge search space and providing them as hypotheses for helping to localize issues and root-causes is important in the context of explainable AI. To tackle it, we introduce an original approach rooted on Subgroup Discovery. We show how to instantiate and develop this generic data-mining framework to identify potential causes of SQL workloads issues. We believe that such data-mining technique is not trivial to apply for DBAs. As such, we also provide a visualization tool for interactive knowledge discovery. We analyse a one week workload from hundreds of databases from our company, make both the dataset and source code available, and experimentally show that insightful hypotheses can be discovered.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678915"}, {"primary_key": "2203455", "vector": [], "sparse_vector": [], "title": "DeepMetis: Augmenting a Deep Learning Test Set to Increase its Mutation Score.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Deep Learning (DL) components are routinely integrated into software systems that need to perform complex tasks such as image or natural language processing. The adequacy of the test data used to test such systems can be assessed by their ability to expose artificially injected faults (mutations) that simulate real DL faults.In this paper, we describe an approach to automatically generate new test inputs that can be used to augment the existing test set so that its capability to detect DL mutations increases. Our tool DeepMetis implements a search based input generation strategy. To account for the non-determinism of the training and the mutation processes, our fitness function involves multiple instances of the DL model under test. Experimental results show that DeepMetis is effective at augmenting the given test set, increasing its capability to detect mutants by 63% on average. A leave-one-out experiment shows that the augmented test set is capable of exposing unseen mutants, which simulate the occurrence of yet undetected faults.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678764"}, {"primary_key": "2203456", "vector": [], "sparse_vector": [], "title": "An Empirical Study of Bugs in WebAssembly Compilers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "WebAssembly is the newest programming language for the Web. It defines a portable bytecode format for use as a compilation target for programs developed in high-level languages such as C, C++, and Rust. As a result, WebAssembly binaries are generally created by WebAssembly compilers rather than being written manually. To port native code to the Web, WebAssembly compilers need to address the differences between the source and target languages and dissimilarities in their execution environments. A deep understanding of the bugs in WebAssembly compilers can help compiler developers determine where to focus development and testing efforts. In this paper, we conduct two empirical studies to understand the characteristics of the bugs found in WebAssembly compilers. First, we perform a qualitative analysis of bugs in Emscripten, the most widely-used WebAssembly compiler. We investigate 146 bug reports in Emscripten related to the unique challenges WebAssembly compilers encounter compared with traditional compilers. Second, we provide a quantitative analysis of 1,054 bugs in three open-source WebAssembly compilers, AssemblyScript, Emscripten, and Rustc/Wasm-Bindgen. We analyze these bugs along three dimensions: lifecycle, impact, and sizes of bug-inducing inputs and bug fixes. These studies deepen our understanding of WebAssembly compiler bugs. We hope that the findings of our study will shed light on opportunities to design practical tools for testing and debugging WebAssembly compilers.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678776"}, {"primary_key": "2203457", "vector": [], "sparse_vector": [], "title": "CiFi: Versatile Analysis of Class and Field Immutability.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reasoning about immutability is important for pre-venting bugs, e.g., in multi-threaded software. So far, static analysis to infer immutability properties has mostly focused on individual objects and references. Reasoning about fields and entire classes, while significantly simpler, has gained less attention. Even a consistently used terminology is missing, which makes it difficult to implement analyses that rely on immutability information. We propose a model for class and field immutability that unifies terminology for immutability flavors considered by previous work and covers new levels of immutability to handle lazy initialization and immutability dependent on generic type parameters. Using the OPAL static analysis framework, we implement CiFi, a set of modular, collaborating analyses for different flavors of immutability, inferring the properties defined in our model. Additionally, we propose a benchmark of representative test cases for class and field immutability. We use the benchmark to showcase CiFi's precision and recall in comparison to state of the art and use CiFi to study the prevalence of immutability in real-world libraries, showcasing the practical quality and relevance of our model.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678903"}, {"primary_key": "2203459", "vector": [], "sparse_vector": [], "title": "Automated Approach for System-level Testing of Unmanned Aerial Systems.", "authors": ["<PERSON>"], "summary": "Unmanned aerial systems (UAS) have a large number of applications in civil and military domains. UAS rely on various avionics systems that are safety-critical and mission-critical. A major requirement of international safety standards is to perform rigorous system-level testing of avionics systems, including software systems. The current industrial practice is to manually create test scenarios, manually or automatically execute these scenarios using simulators, and manual evaluation of the outcomes. A fundamental part of system-level testing of such systems is the simulation of environmental context. The test scenarios typically consist of setting certain environment conditions and testing the system under test in these settings. The state-of-the-art approaches available for this purpose also require manual test scenario development and manual test evaluation. In this research work, we propose an approach to automate the system-level testing of the UAS. The proposed approach (AITester) utilizes model-based testing and artificial intelligence (AI) techniques to automatically generate, execute, and evaluate various test scenarios. The test scenarios are generated on the fly, i.e., during test execution based on the environmental context at runtime. We develop a toolset to support automation. We perform a pilot experiment using a widely-used open-source autopilot, ArduPilot. The preliminary results show that the AITester is effective and efficient in violating autopilot expected behavior.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678902"}, {"primary_key": "2203461", "vector": [], "sparse_vector": [], "title": "SoManyConflicts: Resolve Many Merge Conflicts Interactively and Systematically.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Code merging plays an important role in collaborative software development. However, it is often tedious and error-prone for developers to manually resolve merge conflicts, especially when there are many conflicts after merging long-lived branches or parallel versions. In this paper, we present SoManyConflicts, a language-agnostic approach to help developers resolve merge conflicts systematically, by utilizing their interrelations (e.g., dependency, similarity, etc.). SoManyConflicts employs a graph representation to model these interrelations and provides 3 major features: 1) cluster and order related conflict based on the graph connectivity; 2) suggest related conflicts of one focused conflict based on the topological sorting, 3) suggest resolution strategies for unresolved conflicts based already resolved ones. We have implemented SoManyConflicts as a Visual Studio Code extension that supports multiple languages (Java, JavaScript, and TypeScript, etc.), which is briefly introduced in the video: https://youtu.be/asWhj1KTU. The source code is publicly available at: https://github.com/Symbolk/somanyconflicts.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678937"}, {"primary_key": "2203462", "vector": [], "sparse_vector": [], "title": "ISPY: Automatic Issue-Solution Pair Extraction from Community Live Chats.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hanzhi Jiang", "<PERSON>"], "summary": "Collaborative live chats are gaining popularity as a development communication tool. In community live chatting, developers are likely to post issues they encountered (e.g., setup issues and compile issues), and other developers respond with possible solutions. Therefore, community live chats contain rich sets of information for reported issues and their corresponding solutions, which can be quite useful for knowledge sharing and future reuse if extracted and restored in time. However, it remains challenging to accurately mine such knowledge due to the noisy nature of interleaved dialogs in live chat data. In this paper, we first formulate the problem of issue-solution pair extraction from developer live chat data, and propose an automated approach, named ISPY, based on natural language processing and deep learning techniques with customized enhancements, to address the problem. Specifically, ISPY automates three tasks: 1) Disentangle live chat logs, employing a feedforward neural network to disentangle a conversation history into separate dialogs automatically; 2) Detect dialogs discussing issues, using a novel convolutional neural network (CNN), which consists of a BERT-based utterance embedding layer, a context-aware dialog embedding layer, and an output layer; 3) Extract appropriate utterances and combine them as corresponding solutions, based on the same CNN structure but with different feeding inputs. To evaluate ISPY, we compare it with six baselines, utilizing a dataset with 750 dialogs including 171 issue-solution pairs and evaluate ISPY from eight open source communities. The results show that, for issue-detection, our approach achieves the F1 of 76%, and outperforms all baselines by 30%. Our approach achieves the F1 of 63% for solution-extraction and outperforms the baselines by 20%. Furthermore, we apply ISPY on three new communities to extensively evaluate ISPY's practical usage. Moreover, we publish over 30K issue-solution pairs extracted from 11 communities. We believe that ISPY can facilitate community-based software development by promoting knowledge sharing and shortening the issue-resolving process.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678894"}, {"primary_key": "2203463", "vector": [], "sparse_vector": [], "title": "A Program Synthesis Approach for Adding Architectural Tactics to An Existing Code Base.", "authors": ["<PERSON>"], "summary": "Automatically constructing a program based on given specifications has been studied for decades. Despite the advances in the field of Program Synthesis, the current approaches still synthesize a block of code snippet and leave the task of reusing it in an existing code base to program developers. Due to its program-wide effects, synthesizing an architectural tactic and reusing it in a program is even more challenging. Architectural tactics need to be synthesized based on the context of different locations of the program, broken down to smaller pieces, and added to corresponding locations in the code. Moreover, each piece needs to establish correct data- and control-dependencies to its surrounding environment as well as to the other synthesized pieces. This is an error-prone and challenging task, especially for novice program developers. In this paper, we introduce a novel program synthesis approach that synthesizes architectural tactics and adds them to an existing code base.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678705"}, {"primary_key": "2203464", "vector": [], "sparse_vector": [], "title": "CorbFuzz: Checking Browser Security Policies with Fuzzing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Browsers use security policies to block malicious behaviors. Cross-Origin Read Blocking (CORB) is a browser security policy for preventing side-channel attacks such as Spectre. We propose a web browser security policy fuzzer called CorbFuzz for checking CORB and similar policies. In implementing a security policy, the browser only has access to HTTP requests and responses, and takes policy actions based solely on those interactions. In checking the browser security policies, CorbFuzz uses a policy oracle that tracks the web application behavior and infers the desired policy action based on the web application state. By comparing the policy oracle with the browser behavior, CorbFuzz detects weaknesses in browser security policies. CorbFuzz checks the web browser policy by fuzzing a set of web applications where the state-related queries are symbolically evaluated for increased coverage and automation. CorbFuzz collects type information from database queries and branch conditions in order to prevent the generation of inconsistent data values during fuzzing. We evaluated CorbFuzz on CORB implementations of Chromium and Webkit, and Opaque Response Blocking (ORB) policy implementation of Firefox using web applications collected from GitHub. We found three classes of weaknesses in Chromium's implementation of CORB.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678636"}, {"primary_key": "2203465", "vector": [], "sparse_vector": [], "title": "DSInfoSearch: Supporting Experimentation Process of Data Scientists.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Experimentation plays an important role in the work of data scientists to explore unfamiliar problem domains, to answer questions from data, and to develop diverse machine learning applications. Good experimentation requires creativity, is based on prior results and informed from the literature. However, finding relevant information from online sources to guide experimentation causes inefficiencies for data scientists. The objective of this research is to help data scientists through the presentation of context aware ranked data science experiments, considering problem domain, development task and learning task. Data science experiments for this study were extracted from publicly available interactive notebooks and were manually annotated based on a taxonomy of data science techniques and a meta model of a data science experiment. Further, the ranking algorithm was developed for data science experiments for given problem domain and development task. As a result, a tool was developed to demonstrate context aware ranked data science experiments for given problem domains such as natural language processing, computer vision and time series and for development stages such as feature engineering and model selection. This study shows that tools and techniques can be designed to be aware of the data science context, in fact, much more so than for software engineering tools. This study supports these efforts by providing knowledge that can improve experimentation process of data scientists.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678873"}, {"primary_key": "2203466", "vector": [], "sparse_vector": [], "title": "Revizor: A Data-Driven Approach to Automate Frequent Code Changes Based on Graph Matching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many code changes that developers make in their projects are repeated and constitute recurrent change patterns. It is of interest to collect such patterns from the version history of open-source repositories and suggest the most useful of them as quick fixes. In this paper, we present Revizor—a tool aimed to build custom plugins for PyCharm, a popular Python IDE. A Revizor-based plugin can take change patterns and highlight potential places for their application in the developer's code editor. If the developer accepts the quick fix, the plugin automatically performs the edit. Our approach uses a graph-based representation of code changes, which allows it to support complex distributed code patterns. Experienced developers have also rated the usability and the performance of such Revizor-based plugin positively.The source code of the tool and test plugin prototype are available on GitHub: https://github.com/JetBrains-Research/revizor. A demonstration video with a short tool description can be found on YouTube: https://youtu.be/5eLs14nco7E.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678635"}, {"primary_key": "2203468", "vector": [], "sparse_vector": [], "title": "Semi-automated Cross-Component Issue Management and Impact Analysis.", "authors": ["<PERSON><PERSON>"], "summary": "Despite microservices and other component-based architecture styles being state of the art in research for many years by now, issue management across the boundaries of a single component is still challenging. Components that were developed independently and can be used independently are joined together in the overall architecture, which results in dependencies between those components. Due to these dependencies, bugs can result that propagate along the call chains through the architecture. Other types of issues, such as the violation of non-functional quality properties, can also impact other components. However, traditional issue management systems end at the boundaries of a component, making tracking of issues across different components time-consuming and error-prone. Therefore, a need for automation arises for cross-component issue management, which automatically puts issues of the independent components in the correct mutual context, creating new cross-component issues and syncing cross-component issues between different components. This automation could enable developers to manage issues across components as efficiently as possible and increases the system's quality. To solve this problem, we propose an initial approach for semi-automated cross-component issue management in conjunction with service-level objectives based on our Gropius system. For example, relationships between issues of the same or different components can be predicted using classification to identify dependencies of issues across component boundaries. In addition, we are developing a system to model, monitor and alert service-level objectives. Based on this, the impact of such quality violations on the overall system and the business process will be analysed and explained through cross-component issues.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678830"}, {"primary_key": "2203469", "vector": [], "sparse_vector": [], "title": "Improving Test Case Generation for REST APIs Through Hierarchical Clustering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the ever-increasing use of web APIs in modern- day applications, it is becoming more important to test the system as a whole. In the last decade, tools and approaches have been proposed to automate the creation of system-level test cases for these APIs using evolutionary algorithms (EAs). One of the limiting factors of EAs is that the genetic operators (crossover and mutation) are fully randomized, potentially breaking promising patterns in the sequences of API requests discovered during the search. Breaking these patterns has a negative impact on the effectiveness of the test case generation process. To address this limitation, this paper proposes a new approach that uses Agglomerative Hierarchical Clustering (AHC) to infer a linkage tree model, which captures, replicates, and preserves these patterns in new test cases. We evaluate our approach, called LT-MOSA, by performing an empirical study on 7 real-world benchmark applications w.r.t. branch coverage and real-fault detection capability. We also compare LT-MOSA with the two existing state-of-the-art white-box techniques (MIO, MOSA) for REST API testing. Our results show that LT-MOSA achieves a statistically significant increase in test target coverage (i.e., lines and branches) compared to MIO and MOSA in 4 and 5 out of 7 applications, respectively. Furthermore, LT-MOSA discovers 27 and 18 unique real-faults that are left undetected by MIO and MOSA, respectively.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678586"}, {"primary_key": "2203470", "vector": [], "sparse_vector": [], "title": "Reducing Bug Triaging Confusion by Learning from Mistakes with a Bug Tossing Knowledge Graph.", "authors": ["Yanqi Su", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Assigning bugs to the right components is the prerequisite to get the bugs analyzed and fixed. Classification-based techniques have been used in practice for assisting bug component assignments, for example, the BugBug tool developed by Mozilla. However, our study on 124,477 bugs in Mozilla products reveals that erroneous bug component assignments occur frequently and widely. Most errors are repeated errors and some errors are even misled by the BugBug tool. Our study reveals that complex component designs and misleading component names and bug report keywords confuse bug component assignment not only for bug reporters but also developers and even bug triaging tools. In this work, we propose a learning to rank framework that learns to assign components to bugs from correct, erroneous and irrelevant bug-component assignments in the history. To inform the learning, we construct a bug tossing knowledge graph which incorporates not only goal-oriented component tossing relationships but also rich information about component tossing community, component descriptions, and historical closed and tossed bugs, from which three categories and seven types of features for bug, component and bug-component relation can be derived. We evaluate our approach on a dataset of 98,587 closed bugs (including 29,100 tossed bugs) of 186 components in six Mozilla products. Our results show that our approach significantly improves bug component assignments for both tossed and non-tossed bugs over the BugBug tool and the BugBug tool enhanced with component tossing relationships, with >20% Top-k accuracies and >30% NDCG@k (k=1,3,5,10).", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678574"}, {"primary_key": "2203472", "vector": [], "sparse_vector": [], "title": "UI Test Migration Across Mobile Platforms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Writing UI tests manually requires significant effort. Several approaches have tried to address this problem in mobile apps: by exploiting the similarities of different apps within the same domain on a single platform, they have shown that it is possible to transfer tests that exercise similar functionality between the apps. A related recent technique enables transfer of UI tests uni-directionally, from an open-source iOS app to the same app implemented for Android. This paper presents MAPIT, a technique that expands existing work in three important ways: (1) it enables bi-directional UI test transfer between pairs of \"sibling\" Android and iOS apps; (2) it does not assume that the apps' source code is available; (3) it is capable of transferring tests containing oracles in addition to UI events. MAPIT runs existing tests on a \"source\" app and builds a partial model of the app corresponding to each test. The model comprises the app's screenshots, obtainable properties of each screenshot's constituent elements, and labeled transitions between the screenshots. MAPIT uses this model to determine the corresponding information on the \"target\" app and generates an equivalent test, via a novel approach that leverages computer vision and NLP. Our evaluation on a diverse set of widely used, closed-source sibling Android and iOS apps shows that MAPIT is feasible, accurate, and useful in transferring UI tests across platforms.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678643"}, {"primary_key": "2203473", "vector": [], "sparse_vector": [], "title": "Transcode: Detecting Status Code Mapping Errors in Large-Scale Systems.", "authors": ["Wensheng Tang", "<PERSON><PERSON><PERSON>", "Gang Fan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Guangyuan Bai", "Peng<PERSON> Wang", "<PERSON>"], "summary": "Status code mappings reveal state shifts of a program, mapping one status code to another. Due to careless programming or the lack of the system-wide knowledge of a whole program, developers can make incorrect mappings. Such errors are widely spread across modern software, some of which have even become critical vulnerabilities. Unfortunately, existing solutions merely focus on single status code values, while never considering the relationships, that is, mappings, among them. Therefore, it is imperative to propose an effective method to detect status code mapping errors.In this paper, we propose Transcode to detect potential status code mapping errors. It firstly conducts value flow analysis to efficiently and precisely collect candidate status code values, that is, the integer values, which are checked by following conditional comparisons. Then, it aggregates the correlated status codes according to whether they are propagated with the same variable. Finally, Transcode extracts mappings based on control dependencies and reports the mapping error if one status code is mapped to two others of the same kind. We have implemented Transcode as a prototype system, and evaluated it with 5 real-world software projects, each of which possesses in the order of a million lines of code. The experimental results show that Transcode is capable of handling large-scale systems in both a precise and efficient manner. Furthermore, it has discovered 59 new errors in the tested projects, among which 13 have been fixed by the community. We also deploy Transcode in WeChat, a widely-used instant messaging service, and have succeeded in finding real mapping errors in the industrial settings.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678823"}, {"primary_key": "2203474", "vector": [], "sparse_vector": [], "title": "AST-Transformer: Encoding Abstract Syntax Trees Efficiently for Code Summarization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Ge", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Code summarization aims to generate brief natural language descriptions for source code. As source code is highly structured and follows strict programming language grammars, its Abstract Syntax Tree (AST) is often leveraged to inform the encoder about the structural information. However, ASTs are usually much longer than the source code. Current approaches ignore the size limit and simply feed the whole linearized AST into the encoder. To address this problem, we propose AST-Transformer to efficiently encode tree-structured ASTs. Experiments show that AST-Transformer outperforms the state-of-arts by a substantial margin while being able to reduce 90 ~ 95% of the computational complexity in the encoding process.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678882"}, {"primary_key": "2203475", "vector": [], "sparse_vector": [], "title": "Systematic Testing of Autonomous Driving Systems Using Map Topology-Based Scenario Classification.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Fenghua Wu", "<PERSON>", "<PERSON>"], "summary": "Autonomous Driving Systems (ADSs), which replace humans to drive vehicles, are complex software systems deployed in autonomous vehicles (AVs). Since the execution of ADSs highly relies on maps, it is essential to perform global map-based testing for ADSs to guarantee their correctness and AVs' safety in different situations. Existing methods focus more on specific scenarios rather than global testing throughout the map. Testing on a global map is challenging since the complex lane connections in a map can generate enormous scenarios. In this work, we propose ATLAS, an approach to ADSs' collision avoidance testing using map topology-based scenario classification. The core insight of ATLAS is to generate diverse testing scenarios by classifying junction lanes according to their topology-based interaction patterns. First, ATLAS divides the junction lanes into different classes such that an ADS can execute similar collision avoidance maneuvers on the lanes in the same class. Second, for each class, ATLAS selects one junction lane to construct the testing scenario and generate test cases using a genetic algorithm. Finally, we implement and evaluate ATLAS on Baidu Apollo with the LGSVL simulator on the San Francisco map. Results show that ATLAS exposes nine types of real issues in Apollo 6.0 and reduces the number of junction lanes for testing by 98%.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678735"}, {"primary_key": "2203476", "vector": [], "sparse_vector": [], "title": "JTDog: a Gradle Plugin for Dynamic Test Smell Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The concept of the test smell represents potential problems with the readability and maintainability of the test code. Common test smells focus on static aspects of the source code, such as code length and complexity. These are easy to detect and do not cause problems in terms of test execution. On the other hand, dynamic smells, which are based on test runtime behavior, lead to misunderstanding of the test results. For example, rotten green tests give developers the false impression that the test was passed without any problems, even though the test was poorly executed. Therefore, we should detect dynamic smells and take countermeasures as early as possible through the development. In this paper, we introduce JTDog, a Gradle plugin for dynamic smell detection. JTDog has high portability due to its integration into the build tool. We applied JTDog to 150 projects on GitHub and confirmed that the JTDog plugin has high portability. In addition, JTDog detected 958 dynamic smells in 55 projects. JT-Dog is available at https://github.com/kusumotolab/JTDog, and the demo video is available at https://youtu.be/t374HYMCavI.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678529"}, {"primary_key": "2203477", "vector": [], "sparse_vector": [], "title": "Infrastructure in Code: Towards Developer-Friendly Cloud Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The popularity of cloud technologies has led to the development of a new type of applications that specifically target cloud environments. Such applications require a lot of cloud infrastructure to run, which brought about the Infrastructure as Code approach, where the infrastructure is also coded using a separate language in parallel to the main application. In this paper, we propose a new concept of Infrastructure in Code, where the infrastructure is deduced from the application code itself, without the need for separate specifications. We describe this concept, discuss existing solutions that can be classified as Infrastructure in Code and their limitations, and then present our own framework called Kotless — an extendable cloud-agnostic serverless framework for Kotlin that supports two cloud providers, three DSLs, and two runtimes. Finally, we showcase the usefulness of Kotless by demonstrating its efficiency in migrating an existing application to a serverless environment.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678943"}, {"primary_key": "2203478", "vector": [], "sparse_vector": [], "title": "Explainable AI for Software Engineering.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The success of software engineering projects largely depends on complex decision-making. For example, which tasks should a developer do first, who should perform this task, is the software of high quality, is a software system reliable and resilient enough to deploy, etc. However, erroneous decision-making for these complex questions is costly in terms of money and reputation. Thus, Artificial Intelligence/Machine Learning (AI/ML) techniques have been widely used in software engineering for developing software analytics tools and techniques to improve decision-making, developer productivity, and software quality. However, the predictions of such AI/ML models for software engineering are still not practical (i.e., coarse-grained), not explainable, and not actionable. These concerns often hinder the adoption of AI/ML models in software engineering practices. In addition, many recent studies still focus on improving the accuracy, while a few of them focus on improving explainability. Are we moving in the right direction? How can we better improve the SE community (both research and education)?In this tutorial, we first provide a concise yet essential introduction to the most important aspects of Explainable AI and a hands-on tutorial of Explainable AI tools and techniques. Then, we introduce the fundamental knowledge of defect prediction (an example application of AI for Software Engineering). Finally, we demonstrate three successful case studies on how Explainable AI techniques can be used to address the aforementioned challenges by making the predictions of software defect prediction models more practical, explainable, and actionable. The materials are available at https://xai4se.github.io.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678580"}, {"primary_key": "2203481", "vector": [], "sparse_vector": [], "title": "APIzation: Generating Reusable APIs from StackOverflow Code Snippets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Developer forums like StackOverflow have become essential resources to modern software development practices. However, many code snippets lack a well-defined method declaration, and thus they are often incomplete for immediate reuse. Developers must adapt the retrieved code snippets by parameterizing the variables involved and identifying the return value. This activity, which we call APIzation of a code snippet, can be tedious and time-consuming. In this paper, we present APIZATOR to perform APIzations of JAVA code snippets automatically. APIZATOR is grounded by four common patterns that we extracted by studying real APIzations in GitHub. APIZATOR presents a static analysis algorithm that automatically extracts the method parameters and return statements. We evaluated APIZATOR with a ground-truth of 200 APIzations collected from 20 developers. For 113 (56.50 %) and 115 (57.50 %) APIzations, APIZATOR and the developers extracted identical parameters and return statements, respectively. For 163 (81.50 %) APIzations, either the parameters or the return statements were identical.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678576"}, {"primary_key": "2203482", "vector": [], "sparse_vector": [], "title": "Learning Domain-Specific Edit Operations from Model Repositories with Frequent Subgraph Mining.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Model transformations play a fundamental role in model-driven software development. They can be used to solve or support central tasks, such as creating models, handling model co-evolution, and model merging. In the past, various (semi-)automatic approaches have been proposed to derive model transformations from meta-models or from examples. These approaches require time-consuming handcrafting or the recording of concrete examples, or they are unable to derive complex transformations. We propose a novel unsupervised approach, called <PERSON><PERSON>ham, which is able to learn edit operations from model histories in model repositories. <PERSON><PERSON><PERSON> is based on the idea that meaningful domain-specific edit operations are the ones that compress the model differences. It employs frequent subgraph mining to discover frequent structures in model difference graphs. We evaluate our approach in two controlled experiments and one real-world case study of a large-scale industrial model-driven architecture project in the railway domain. We found that our approach is able to discover frequent edit operations that have actually been applied before. Furthermore, <PERSON><PERSON><PERSON> is able to extract edit operations that are meaningful to practitioners in an industrial setting.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678698"}, {"primary_key": "2203483", "vector": [], "sparse_vector": [], "title": "Distribution Models for Falsification and Verification of DNNs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "DNN validation and verification approaches that are input distribution agnostic waste effort on irrelevant inputs and report false property violations. Drawing on the large body of work on model-based validation and verification of traditional systems, we introduce the first approach that leverages environmental models to focus DNN falsification and verification on the relevant input space. Our approach, DFV, automatically builds an input distribution model using unsupervised learning, prefixes that model to the DNN to force all inputs to come from the learned distribution, and reformulates the property to the input space of the distribution model. This transformed verification problem allows existing DNN falsification and verification tools to target the input distribution – avoiding consideration of infeasible inputs. Our study of DFV with 7 falsification and verification tools, two DNNs defined over different data sets, and 93 distinct distribution models, provides clear evidence that the counterexamples found by the tools are much more representative of the data distribution, and it shows how the performance of DFV varies across domains, models, and tools.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678590"}, {"primary_key": "2203484", "vector": [], "sparse_vector": [], "title": "On the Real-World Effectiveness of Static Bug Detectors at Finding Null Pointer Exceptions.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Static bug detectors aim at helping developers to automatically find and prevent bugs. In this experience paper, we study the effectiveness of static bug detectors at identifying Null Pointer Dereferences or Null Pointer Exceptions (NPEs). NPEs pervade all programming domains from systems to web development. Specifically, our study measures the effectiveness of five Java static bug detectors: CheckerFramework, ERADICATE, INFER, NULLAWAY, and SPOTBUGS. We conduct our study on 102 real-world and reproducible NPEs from 42 open-source projects found in the BUGSWARM and DEFECTS4J datasets. We apply two known methods to determine whether a bug is found by a given tool, and introduce two new methods that leverage stack trace and code coverage information. Additionally, we provide a categorization of the tool's capabilities and the bug characteristics to better understand the strengths and weaknesses of the tools. Overall, the tools under study only find 30 out of 102 bugs (29.4%), with the majority found by ERADICATE. Based on our observations, we identify and discuss opportunities to make the tools more effective and useful.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678535"}, {"primary_key": "2203485", "vector": [], "sparse_vector": [], "title": "BeeSwarm: Enabling Parallel Scaling Performance Measurement in Continuous Integration for HPC Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Testing is one of the most important steps in software development–it ensures the quality of software. Continuous Integration (CI) is a widely used testing standard that can report software quality to the developer in a timely manner during development progress. Performance, especially scalability, is another key factor for High Performance Computing (HPC) applications. There are many existing profiling and performance tools for HPC applications, but none of these are integrated into CI tools. In this work, we propose BeeSwarm, an HPC container based parallel scaling performance system that can be easily applied to the current CI test environments. BeeSwarm is mainly designed for HPC application developers who need to monitor how their applications can scale on different compute resources. We demonstrate BeeSwarm using a multi-physics HPC application with Travis CI, GitLab CI and GitHub Actions while using ChameleonCloud and Google Compute Engine as the compute backends. Our results show that BeeSwarm can be used for scalability and performance testing of HPC applications.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678805"}, {"primary_key": "2203486", "vector": [], "sparse_vector": [], "title": "FRUGAL: Unlocking Semi-Supervised Learning for Software Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Standard software analytics often involves having a large amount of data with labels in order to commission models with acceptable performance. However, prior work has shown that such requirements can be expensive, taking several weeks to label thousands of commits, and not always available when traversing new research problems and domains. Unsupervised Learning is a promising direction to learn hidden patterns within unlabelled data, which has only been extensively studied in defect prediction. Nevertheless, unsupervised learning can be ineffective by itself and has not been explored in other domains (e.g., static analysis and issue close time).Motivated by this literature gap and technical limitations, we present FRUGAL, a tuned semi-supervised method that builds on a simple optimization scheme that does not require sophisticated (e.g., deep learners) and expensive (e.g., 100% manually labelled data) methods. FRUGAL optimizes the unsupervised learner's configurations (via a simple grid search) while validating our design decision of labelling just 2.5% of the data before prediction.As shown by the experiments of this paper FRUGAL outperforms the state-of-the-art adoptable static code warning recognizer and issue closed time predictor, while reducing the cost of labelling by a factor of 40 (from 100% to 2.5%). Hence we assert that FRUGAL can save considerable effort in data labelling especially in validating prior work or researching new problems.Based on this work, we suggest that proponents of complex and expensive methods should always baseline such methods against simpler and cheaper alternatives. For instance, a semi-supervised learner like FRUGAL can serve as a baseline to the state-of-theart software analytics.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678617"}, {"primary_key": "2203487", "vector": [], "sparse_vector": [], "title": "Efficient state synchronisation in model-based testing through reinforcement learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Model-based testing is a structured method to test complex systems. Scaling up model-based testing to large systems requires improving the efficiency of various steps involved in testcase generation and more importantly, in test-execution. One of the most costly steps of model-based testing is to bring the system to a known state, best achieved through synchronising sequences. A synchronising sequence is an input sequence that brings a given system to a predetermined state regardless of system's initial state. Depending on the structure, the system might be complete, i.e., all inputs are applicable at every state of the system. However, some systems are partial and in this case not all inputs are usable at every state. Derivation of synchronising sequences from complete or partial systems is a challenging task. In this paper, we introduce a novel Q-learning algorithm that can derive synchronising sequences from systems with complete or partial structures. The proposed algorithm is faster and can process larger systems than the fastest sequential algorithm that derives synchronising sequences from complete systems. Moreover, the proposed method is also faster and can process larger systems than the most recent massively parallel algorithm that derives synchronising sequences from partial systems. Furthermore, the proposed algorithm generates shorter synchronising sequences.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678566"}, {"primary_key": "2203488", "vector": [], "sparse_vector": [], "title": "Mining Cross-Domain Apps for Software Evolution: A Feature-based Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "C<PERSON><PERSON>"], "summary": "The skyrocketing growth of mobile apps and mobile devices has significantly fueled the competition among app developers. They have leveraged the app store capabilities to analyse app data and identify app improvement opportunities. Existing research has shown that app developers mostly rely on in-domain (i.e., same domain or same app) data to improve their apps. However, relying on in-domain data results in low diversity and lacks novelty in recommended features. In this work, we present an approach that automatically identifies, classifies and ranks relevant popular features from cross-domain apps for recommendation to any given target app. It includes the following three steps: 1) identify cross-domain apps that are relevant to the target app in terms of their features; 2) filter and group semantically the features of the relevant cross-domain apps that are complementary to the target app; 3) rank and prioritize the complementary cross-domain features (in terms of their domain, app, feature and popularity characteristics) for adoption by the target app's developers. We have run extensive experiments on 100 target apps from 10 categories over 15,200 cross-domain apps from 31 categories. The experimental results have shown that our approach to identifying, grouping and ranking complementary cross-domain features for recommendation has achieved an accuracy level of over 89%. Our semantic feature grouping technique has also significantly outperformed two existing baseline techniques. The empirical evaluation validates the efficacy of our approach in providing personalised feature recommendation and enhancing app's user serendipity.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678514"}, {"primary_key": "2203489", "vector": [], "sparse_vector": [], "title": "A Mocktail of Source Code Representations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Efficient representation of source code is essential for various software engineering tasks such as code classification and code clone detection. Most recent approaches for representing source code still use AST and do not leverage semantic graphs such as CFG and PDG. One effective technique for representing source code involves extracting paths from the AST and using a learning model to capture program properties. Code2vec is one such path-based approach that uses an attention-based neural network to learn code embeddings which can then be used for various downstream tasks. However, this approach uses only AST and does not leverage CFG and PDG. Even though an integrated graph approach (Code Property Graph) exists for representing source code, it has only been explored in the domain of software security. Moreover, it does not leverage the paths from the individual graphs. Our idea is to extend the path-based approach code2vec to include the semantic graphs CFG and PDG with AST, which is largely unexplored in software engineering. We evaluate our approach on the task of METHODNAMING using a C dataset of 730K methods collected from GitHub. In comparison to code2vec, our approach improves the F1 score by 11% on the full dataset and up to 100% with individual projects. We show that semantic features from the CFG and PDG paths drastically improve the performance of the software engineering tasks. We envision that looking at a mocktail of source code representations for various software engineering tasks can lay the foundation for a new line of research and a re-haul of existing research.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678551"}, {"primary_key": "2203490", "vector": [], "sparse_vector": [], "title": "py2src: Towards the Automatic (and Reliable) Identification of Sources for PyPI Package.", "authors": ["Duc-Ly Vu"], "summary": "Selecting which libraries ('dependencies' or 'packages' in the industry's jargon) to adopt in a project is an essential task in software development. The quality of the corresponding source code is a key factor behind this selection (from security to timeliness). Yet, how easy is it to find the 'actual' source? How reliable is this information? To address this problem, we developed an approach called py2src to automatically identify GitHub source code repositories corresponding to packages in PyPI and automatically provide an indicator of the reliability of such information. We also report a preliminary empirical evaluation of the approach on the top PyPI packages.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678526"}, {"primary_key": "2203492", "vector": [], "sparse_vector": [], "title": "Program Synthesis with Algorithm Pseudocode Guidance.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The limitation of the current program synthesis method is that the synthesized program is small in scale and simple in logic. In this work, we introduce an effective program synthesis approach based on algorithm pseudocode. By parsing the pseudocode, critical information like control structure framework and variable names can be obtained which are used to guide the process of synthesis. Experiments show that information extracted from pseudocode helps to reduce the space of programs and enhance the ability of the synthesizer. It can synthesize some complex programs with control structures.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678657"}, {"primary_key": "2203493", "vector": [], "sparse_vector": [], "title": "Quito: a Coverage-Guided Test Generator for Quantum Programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Automation in quantum software testing is essential to support systematic and cost-effective testing. Towards this direction, we present a quantum software testing tool called Quito that can automatically generate test suites covering three coverage criteria defined on inputs and outputs of a quantum program coded in Qiskit, i.e., input coverage, output coverage, and input-output coverage. Quito also implements two types of test oracles based on program specifications, i.e., checking whether a quantum program produced a wrong output or checking a probabilistic test oracle with statistical test. We describe the architecture and methodology of the tool. We also validated the tool with one quantum program and one faulty version of it. Results indicate that Quito can generate test suites and perform test assessments that detect faults, and produce test results with a good time performance.Quito's code: https://github.com/Simula-COMPLEX/quitoQuito's video: https://youtu.be/kuI9QaCo8A8Artifact Available: https://doi.org/10.5281/zenodo.5288665", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678798"}, {"primary_key": "2203495", "vector": [], "sparse_vector": [], "title": "PyNose: A Test Smell Detector For Python.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Similarly to production code, code smells also occur in test code, where they are called test smells. Test smells have a detrimental effect not only on test code but also on the production code that is being tested. To date, the majority of the research on test smells has been focusing on programming languages such as Java and Scala. However, there are no available automated tools to support the identification of test smells for Python, despite its rapid growth in popularity in recent years. In this paper, we strive to extend the research to Python, build a tool for detecting test smells in this language, and conduct an empirical analysis of test smells in Python projects.We started by gathering a list of test smells from existing research and selecting test smells that can be considered language-agnostic or have similar functionality in Python's standard Unittest framework. In total, we identified 17 diverse test smells. Additionally, we searched for Python-specific test smells by mining frequent code change patterns that can be considered as either fixing or introducing test smells. Based on these changes, we proposed our own test smell called Suboptimal Assert. To detect all these test smells, we developed a tool called PYNOSE in the form of a plugin to PyCharm, a popular Python IDE. Finally, we conducted a large-scale empirical investigation aimed at analyzing the prevalence of test smells in Python code. Our results show that 98% of the projects and 84% of the test suites in the studied dataset contain at least one test smell. Our proposed Suboptimal Assert smell was detected in as much as 70.6% of the projects, making it a valuable addition to the list.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678615"}, {"primary_key": "2203496", "vector": [], "sparse_vector": [], "title": "Faster Mutation Analysis with Fewer Processes and Smaller Overheads.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mutation analysis is a powerful dynamic approach that has many applications, such as measuring the quality of test suites or automatically locating faults. However, the inherent low scalability hampers its practical use. To accelerate mutation analysis, researchers propose approaches to reduce redundant executions. A family of fork-based approaches tries to share identical executions among mutants. Fork-based approaches carry all mutants in one process and decide whether to fork new child processes when reaching a mutated statement. The mutants carried by the parent process are split into groups and distributed to different processes to finish the remaining executions. However, existing fork-based approaches have two limitations: (1) the limited analysis scope on a single statement to compare and cluster mutants prevents their systems from detecting more equivalent mutants, and (2) the interpretation of the mutants and the runtime equivalence analysis introduce significant overhead.In this paper, we present a novel fork-based mutation analysis approach WinMut, which (1) groups mutants in a scope of mutated statements and, (2) removes redundant computations inside interpreters. WinMut not only reduces the number of invoked processes but also has a lower cost for executing a single process. Our experiments show that our approach can further accelerate mutation analysis with an average speedup of 5.57x on top of the state-of-the-art fork-based approach, AccMut.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678827"}, {"primary_key": "2203497", "vector": [], "sparse_vector": [], "title": "Groot: An Event-graph-based Approach for Root Cause Analysis in Industrial Settings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Selçuk Köprü", "<PERSON>"], "summary": "For large-scale distributed systems, it is crucial to efficiently diagnose the root causes of incidents to maintain high system availability. The recent development of microservice architecture brings three major challenges (i.e., complexities of operation, system scale, and monitoring) to root cause analysis (RCA) in industrial settings. To tackle these challenges, in this paper, we present Groot, an event-graph-based approach for RCA. Groot constructs a real-time causality graph based on events that summarize various types of metrics, logs, and activities in the system under analysis. Moreover, to incorporate domain knowledge from site reliability engineering (SRE) engineers, Groot can be customized with user-defined events and domain-specific rules. Currently, Groot supports RCA among 5,000 real production services and is actively used by the SRE teams in eBay, a global e-commerce system serving more than 159 million active buyers per year. Over 15 months, we collect a data set containing labeled root causes of 952 real production incidents for evaluation. The evaluation results show that Groot is able to achieve 95% top-3 accuracy and 78% top-1 accuracy. To share our experience in deploying and adopting RCA in industrial settings, we conduct a survey to show that users of Groot find it helpful and easy to use. We also share the lessons learned from deploying and adopting Groot to solve RCA problems in production environments.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678708"}, {"primary_key": "2203498", "vector": [], "sparse_vector": [], "title": "FinFuzzer: One Step Further in Fuzzing Fintech Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Lihua Xu", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Comprehensive testing is of high importance to ensure the reliability of software systems, especially for systems with high stakes such as FinTech systems. In this paper, we share our observations of the Ant Group's status quo in testing their financial services, specifically on the importance of properly transforming relevant external environment settings and prioritizing input object fields for mutation during automated fuzzing. Based on these observations, we propose FinFuzzer, an automated fuzz testing framework that detects and transforms relevant environmental settings into system inputs, prioritizes input object fields, and mutates system inputs on both environment settings and high-priority object fields. Our evaluation of FinFuzzer against four FinTech systems developed by the Ant Group shows that FinFuzzer can outperform a state-of-the-art approach in terms of line coverage in much shorter time.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678675"}, {"primary_key": "2203499", "vector": [], "sparse_vector": [], "title": "QDiff: Differential Testing of Quantum Software Stacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Guoqing <PERSON>", "<PERSON><PERSON>"], "summary": "Over the past few years, several quantum software stacks (QSS) have been developed in response to rapid hardware advances in quantum computing. A QSS includes a quantum programming language, an optimizing compiler that translates a quantum algorithm written in a high-level language into quantum gate instructions, a quantum simulator that emulates these instructions on a classical device, and a software controller that sends analog signals to a very expensive quantum hardware based on quantum circuits. In comparison to traditional compilers and architecture simulators, QSSes are difficult to tests due to the probabilistic nature of results, the lack of clear hardware specifications, and quantum programming complexity.This work devises a novel differential testing approach for QSSes, named QDiff with three major innovations: (1) We generate input programs to be tested via semantics-preserving, source to source transformation to explore program variants. (2) We speed up differential testing by filtering out quantum circuits that are not worthwhile to execute on quantum hardware by analyzing static characteristics such as a circuit depth, 2-gate operations, gate error rates, and T1 relaxation time. (3) We design an extensible equivalence checking mechanism via distribution comparison functions such as Kolmogorov–S<PERSON>nov test and cross entropy.We evaluate QDiff with three widely-used open source QSSes: <PERSON><PERSON>t from IBM, Cirq from Google, and Py<PERSON>l from Rigetti. By running QDiff on both real hardware and quantum simulators, we found several critical bugs revealing potential instabilities in these platforms. QDiff's source transformation is effective in producing semantically equivalent yet not-identical circuits (i.e., 34% of trials), and its filtering mechanism can speed up differential testing by 66%.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678792"}, {"primary_key": "2203500", "vector": [], "sparse_vector": [], "title": "Effectively Analyzing Evolving Software with Differential Facts.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Software systems evolve continuously during their lifecycle. Developers incrementally introduce new features and fix bugs during the process, leading to lots of changes and artifacts accumulated. Driven by those rich data recorded in version control systems or issue trackers, lots of work has been done to analyze the software histories. In this PhD work, we propose a universal representation to effectively store and query over knowledge extracted from the histories, with the hope of supporting software evolution research. We have created a toolset, named DIFFBASE, to extract both relations between program entities at the same version, as well as atomic changes between versions. Then users can compose queries using algebraic operators, Datalog or an SQL-like language to accomplish several different evolution management tasks. Based on the existing research outcome, possible future work includes utilizing the facts approach in a scalable solution to discovering compatibility issues involving changes of multiple components and improvement on the storage and query performance of DIFFBASE.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678659"}, {"primary_key": "2203501", "vector": [], "sparse_vector": [], "title": "EvoMe: A Software Evolution Management Engine Based on Differential Factbase.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON>"], "summary": "Managing large and fast-evolving software systems can be a challenging task. Numerous solutions have been developed to assist in this process, enhancing software quality and reducing development costs. These techniques—e.g., regression test selection and change impact analysis—are often built as standalone tools, unable to share or reuse information among them. In this paper, we introduce a software evolution management engine, EvoMe, to streamline and simplify the development of such tools, allowing them to be easily prototyped using an intuitive query language and quickly deployed for different types of projects. EvoMe is based on differential factbase, a uniform exchangeable representation of evolving software artifacts, and can be accessed directly through a Web interface. We demonstrate the usage and key features of EvoMe on real open-source software projects. The demonstration video can be found at: http://youtu.be/6mMgu6rfnjY.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678795"}, {"primary_key": "2203502", "vector": [], "sparse_vector": [], "title": "Automated Code Refactoring upon Database-Schema Changes in Web Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern web applications manipulate a large amount of user data and undergo frequent data-schema changes. These changes bring up a unique refactoring task: updating application code to be consistent with data schema. Previous study and our own investigation show that this type of refactoring is error-prone and time-consuming for developers. This paper presents EvolutionSaver, a static code analysis and transformation tool that automates schema-related code refactoring and consistency checking. EvolutionSaver is implemented as an IDE plugin that works for both Rails and Django applications. The source code of EvolutionSaver is available on Github [1] and the plugin can be downloaded from Visual Studio Marketplace [2], with its tutorial available at https://www.youtube.com/watch?v=qBiMkLFIjbE and DOI 10.5281/zenodo.5276127.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678934"}, {"primary_key": "2203504", "vector": [], "sparse_vector": [], "title": "IncBL: Incremental Bug Localization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Numerous efforts have been invested in improving the effectiveness of bug localization techniques, whereas little attention is paid to making these tools run more efficiently in continuously evolving software repositories. This paper first analyzes the information retrieval model behind a classic bug localization tool, BugLocator, and builds a mathematical foundation illustrating that the model can be updated incrementally when codebase or bug reports evolve. Then, we present IncBL, a tool for Incremental Bug Localization in evolving software repositories. IncBL is evaluated on the Bugzbook dataset, and the results show that IncBL can significantly reduce the running time by 77.79% on average compared with the re-computing the model, while maintaining the same level of accuracy. We also implement IncBL as a Github App that can be easily integrated into open-source projects on GitHub. Users can deploy and use IncBL locally as well. The demo video for IncBL can be viewed at https://youtu.be/G4gMuvlJSb0, and the source code can be found at https://github.com/soarsmu/IncBL.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678546"}, {"primary_key": "2203505", "vector": [], "sparse_vector": [], "title": "AID: Efficient Prediction of Aggregated Intensity of Dependency in Large-scale Cloud Systems.", "authors": ["<PERSON><PERSON><PERSON>", "Jiacheng Shen", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Service reliability is one of the key challenges that cloud providers have to deal with. In cloud systems, unplanned service failures may cause severe cascading impacts on their dependent services, deteriorating customer satisfaction. Predicting the cascading impacts accurately and efficiently is critical to the operation and maintenance of cloud systems. Existing approaches identify whether one service depends on another via distributed tracing but no prior work focused on discriminating to what extent the dependency between cloud services is. In this paper, we survey the outages and the procedure for failure diagnosis in two cloud providers to motivate the definition of the intensity of dependency. We define the intensity of dependency between two services as how much the status of the callee service influences the caller service. Then we propose AID, the first approach to predict the intensity of dependencies between cloud services. AID first generates a set of candidate dependency pairs from the spans. AID then represents the status of each cloud service with a multivariate time series aggregated from the spans. With the representation of services, AID calculates the similarities between the statuses of the caller and the callee of each candidate pair. Finally, AID aggregates the similarities to produce a unified value as the intensity of the dependency. We evaluate AID on the data collected from an open-source microservice benchmark and a cloud system in production. The experimental results show that AID can efficiently and accurately predict the intensity of dependencies. We further demonstrate the usefulness of our method in a large-scale commercial cloud system.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678534"}, {"primary_key": "2203506", "vector": [], "sparse_vector": [], "title": "Subtle Bugs Everywhere: Generating Documentation for Data Wrangling Code.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jin L. C. Guo", "<PERSON>"], "summary": "Data scientists reportedly spend a significant amount of their time in their daily routines on data wrangling, i.e. cleaning data and extracting features. However, data wrangling code is often repetitive and error-prone to write. Moreover, it is easy to introduce subtle bugs when reusing and adopting existing code, which results in reduced model quality. To support data scientists with data wrangling, we present a technique to generate documentation for data wrangling code. We use (1) program synthesis techniques to automatically summarize data transformations and (2) test case selection techniques to purposefully select representative examples from the data based on execution information collected with tailored dynamic program analysis. We demonstrate that a JupyterLab extension with our technique can provide on-demand documentation for many cells in popular notebooks and find in a user study that users with our plugin are faster and more effective at finding realistic bugs in data wrangling code.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678520"}, {"primary_key": "2203507", "vector": [], "sparse_vector": [], "title": "Towards Fluid Software Architectures: Bidirectional Human-AI Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The research on engineering software applications that employ artificial intelligence (AI) and machine learning (ML) is at an all-time peak. However, most of the research in this area is focused on the interactions between humans and AI which, in turn, is predominantly concerned with either building immersive interfaces and user experiences that allow for increased telemetry or on handling AI and ML applications in production (MLOps). Nonetheless, the research on fundamental architectural differences between AI-powered applications and traditional ones did not receive its fair share of attention. To that end, we believe that a new take on the fundamental architecture of building software applications is needed. With the ever increasing prominence of content-driven AI-powered applications, it is our conviction that 1) content could be served by servers without clients requesting, 2) servers could (should) request data from clients without waiting for their requests, and 3) interfaces should dynamically adapt to updates that happen to the intelligence driving the application. Hence, in this paper, we propose the fluid architecture that facilitates the bidirectional interaction between clients and servers as well as accommodates the co-dependent evolution of interfaces and back-end intelligence in AI-powered systems.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678647"}, {"primary_key": "2203508", "vector": [], "sparse_vector": [], "title": "Lessons learned from hyper-parameter tuning for microservice candidate identification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "When optimizing software for the cloud, monolithic applications need to be partitioned into many smaller microservices. While many tools have been proposed for this task, we warn that the evaluation of those approaches has been incomplete; e.g. minimal prior exploration of hyperparameter optimization. Using a set of open source Java EE applications, we show here that (a) such optimization can significantly improve microservice partitioning; and that (b) an open issue for future work is how to find which optimizer works best for different problems. To facilitate that future work, see https://github.com/yrahul3910/ase-tuned-mono2micro for a reproduction package for this research.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678704"}, {"primary_key": "2203509", "vector": [], "sparse_vector": [], "title": "Unsupervised Labeling and Extraction of Phrase-based Concepts in Vulnerability Descriptions.", "authors": ["Sofonia<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "People usually describe the key characteristics of software vulnerabilities in natural language mixed with domain-specific names and concepts. This textual nature poses a significant challenge for the automatic analysis of vulnerabilities. Automatic extraction of key vulnerability aspects is highly desirable but demands significant effort to manually label data for model training. In this paper, we propose an unsupervised approach to label and extract important vulnerability concepts in textural vulnerability descriptions (TVDs). We focus on three types of phrase-based vulnerability concepts (root cause, attack vector, and impact) as they are much more difficult to label and extract than name- or number-based entities (i.e., vendor, product, and version). Our approach is based on a key observation that the same-type of phrases, no matter how they differ in sentence structures and phrase expressions, usually share syntactically similar paths in the sentence parsing trees. Therefore, we propose two path representations (absolute paths and relative paths) and use an auto-encoder to encode such syntactic similarities. To address the discrete nature of our paths, we enhance traditional Variational Auto-encoder (VAE) with Gumble-Max trick for categorical data distribution, and thus creates a Categorical VAE (CaVAE). In the latent space of absolute and relative paths, we further use FIt-TSNE and clustering techniques to generate clusters of the same-type of concepts. Our evaluation confirms the effectiveness of our CaVAE for encoding path representations and the accuracy of vulnerability concepts in the resulting clusters. In a concept classification task, our unsupervisedly labeled vulnerability concepts outperform the two manually labeled datasets from previous work.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678638"}, {"primary_key": "2203510", "vector": [], "sparse_vector": [], "title": "Improving Configurability of Unit-level Continuous Fuzzing: An Industrial Case Study with SAP HANA.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents industrial experiences on enhancing the configurability of a fuzzing framework for effective continuous fuzzing of the SAP HANA components. We propose five new mutation scheduling strategies for effective uses of grammar-aware mutators in the unit-level fuzzing framework, and three new seed corpus selection strategies to configure a fuzzing campaign to check on changed code in priority. The empirical results show that the proposed extension gives users chances to improve fuzzing effectiveness and efficiency by configuring the framework specifically for each target component.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678685"}, {"primary_key": "2203512", "vector": [], "sparse_vector": [], "title": "A Prediction Model for Software Requirements Change Impact.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Software requirements Change Impact Analysis (CIA) is a pivotal process in requirements engineering (RE) since changes to requirements are inevitable. When a requirement change is requested, its impact on all software artefacts has to be investigated to accept or reject the request. Manually performed CIA in large-scale software development is time-consuming and error-prone so, automating this analysis can improve the process of requirements change management. The main goal of this research is to apply a combination of Machine Learning (ML) and Natural Language Processing (NLP) based approaches to develop a prediction model for forecasting the requirement change impact on other requirements in the specification document. The proposed prediction model will be evaluated using appropriate datasets for accuracy and performance. The resulting tool will support project managers to perform automated change impact analysis and make informed decisions on the acceptance or rejection of requirement change requests.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678582"}, {"primary_key": "2203513", "vector": [], "sparse_vector": [], "title": "Fuzzing Methods Recommendation Based on Feature Vectors.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Fuzzing is a technique that aims to detect vulnerabilities or exceptions through unexpected input and has found tremendous recent interest in both academia and industry. Although these fuzzing methods have great advantages in the field of vulnerability detection, they also have their own disadvantages in the face of different target programs. It is obviously impractical for a fuzzing test method to adapt to all the target programs. Therefore, we study how to select the appropriate fuzzing methods for different target programs. Specifically, we first analyze the program, and then extract the feature vectors of the target program to get the information of the program, such as syntax, context and so on. Next, we build a matching model to match the similarity of target program and the fuzzing algorithm to select the fuzzing algorithm with higher matching degree. Through our matching model, we get a more suitable fuzzing algorithm to improve the detection efficiency, precision, recall, F-measure, and other statistical measures.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678630"}, {"primary_key": "2203514", "vector": [], "sparse_vector": [], "title": "Towards a Serverless Java Runtime.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Java virtual machine (JVM) has the well-known slow startup and warmup issues. This is because the JVM needs to dynamically create many runtime data before reaching peak performance, including class metadata, method profile data, and just-in-time (JIT) compiled native code, for each run of even the same application. Many techniques are then proposed to reuse and share these runtime data across different runs. For example, Class Data Sharing (CDS) and Ahead-of-time (AOT) compilation aim to save and share class metadata and compiled native code, respectively. Unfortunately, these techniques are developed independently and cannot leverage the ability of each other well. This paper presents an approach that systematically reuses JVM runtime data to accelerate application startup and warmup. We first propose and implement JWarmup, a technique that can record and reuse JIT compilation data (e.g., compiled methods and their profile data). Then, we feed JIT compilation data to the AOT compiler to perform profile-guided optimization (PGO). We also integrate existing CDS and AOT techniques to further optimize application startup. Evaluation on real-world applications shows that our approach can bring a 41.35% improvement to the application startup. Moreover, our approach can trigger JIT compilation in advance and reduce CPU load at peak time.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678709"}, {"primary_key": "2203515", "vector": [], "sparse_vector": [], "title": "BIFF: Practical Binary Fuzzing Framework for Programs of IoT and Mobile Devices.", "authors": ["<PERSON><PERSON>", "Yuekang Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Internet-of-things (IoT) or mobile devices are omnipresent in our daily life; the security issues inside them are especially crucial. Greybox fuzzing has been shown effective in detecting vulnerabilities. However, applications in IoT or mobile devices are usually proprietary to specific vendors, fuzzers are required to support binary-only targets. Moreover, since these devices are of heterogeneous architecture, assigned with limited resources, and many testing targets are server-like programs, applying existing fuzzing techniques faces great challenges.This paper proposes BIFF, a general-purpose fuzzer that aims to stress these issues. It supports binary-only targets, is general (supports multiple CPU architectures including Intel, ARM, MIPS, and PowerPC), fast (has the lowest runtime overhead compared to existing fuzzers), and flexible (uses a new fuzzing workflow that can fuzz any piece of code inside the target binary). Experiments demonstrate that BIFF has the best performance compared with state-of-the-art binary fuzzers and can fuzz the server-like programs which cannot be fuzzed by the existing fuzzers. Using BIFF, we've found 24 unknown vulnerabilities (including memory corruptions, infinite loops, and infinite recursions) in industrial products.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678910"}, {"primary_key": "2203516", "vector": [], "sparse_vector": [], "title": "FIGCPS: Effective Failure-inducing Input Generation for Cyber-Physical Systems with Deep Reinforcement Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cyber-Physical Systems (CPSs) are composed of computational control logic and physical processes, which intertwine with each other. CPSs are widely used in various domains of daily life, including those safety-critical systems and infrastructures, such as medical monitoring, autonomous vehicles, and water treatment systems. It is thus critical to effectively test them. However, it is not easy to obtain test cases which can fail the CPS. In this work, we propose a failure-inducing input generation approach FIGCPS, which requires no knowledge of the CPS under test or any history logs of the CPS which are usually hard to obtain. Our approach adopts deep reinforcement learning techniques to interact with the CPS under test and effectively searches for failure-inducing input guided by rewards. Our approach adaptively collects information from the CPS, which reduces the training time and is also able to explore different states. Moreover, our approach is the first attempt to generate failure-inducing input for CPSs with both continuous action space and high-dimensional discrete action space, which are common for some classes of CPSs. The evaluation results show that FIGCPS not only achieves a higher success rate than the state-of-the-art approaches but also finds two new attacks in a well-tested CPS.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678832"}, {"primary_key": "2203517", "vector": [], "sparse_vector": [], "title": "Bugs4Q: A Benchmark of Real Bugs for Quantum Programs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Realistic benchmarks of reproducible bugs and fixes are vital to good experimental evaluation of debugging and testing approaches. However, there is no suitable benchmark suite that can systematically evaluate the debugging and testing methods of quantum programs until now. This paper proposes Bugs4Q, a benchmark of thirty-six real, manually validated Qiskit bugs from four popular Qiskit elements (Terra, Aer, Ignis, and Aqua), supplemented with the test cases for reproducing buggy behaviors. Bugs4Q also provides interfaces for accessing the buggy and fixed versions of the Qiskit programs and executing the corresponding test cases, facilitating the reproducible empirical studies and comparisons of Qiskit program debugging and testing tools. Bugs4Q is publicly available at https://github.com/Z-928/Bugs4Q", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678908"}, {"primary_key": "2203518", "vector": [], "sparse_vector": [], "title": "FLACK: Localizing Faults in Alloy Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Marcelo F. Frias", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fault localization can help developers identify buggy statements or expressions in programs. Existing fault localization techniques are often designed for imperative programs (e.g., C and Java) and rely on tests to compare correct and incorrect execution traces to identify suspicious statements. In this demo paper, we present FLACK, a tool to automatically locate faults for models written in Alloy, a declarative language where the models are not executed but instead converted into a logical formula and solved using a SAT solver. FLACK takes as input an Alloy model that violates some assertions and returns a ranked list of suspicious expressions contributing to the violation. The key idea is to analyze the differences between counterexamples, i.e., instances of the model that do not satisfy the assertion and instances that do satisfy the assertion to find suspicious expressions in the input model. An experiment with 157 Alloy models with various bugs shows the efficiency and accuracy of FLACK in localizing the causes of these bugs. FLACK and its evaluation benchmark and results can be downloaded from https://github.com/guolong-zheng/flack. The video demonstration is available at https://youtu.be/FKa2ohqIUms.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678740"}, {"primary_key": "2203519", "vector": [], "sparse_vector": [], "title": "Why Do Developers Remove Lambda Expressions in Java?", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Hengcheng Zhu", "<PERSON><PERSON>", "<PERSON>"], "summary": "Java 8 has introduced lambda expressions, a core feature of functional programming. Since its introduction, there is an increasing trend of lambda adoptions in Java projects. Developers often adopt lambda expressions to simplify code, avoid code duplication or simulate other functional features. However, we observe that lambda expressions can also incur different types of side effects (i.e., performance issues and memory leakages) or even severe bugs, and developers also frequently remove lambda expressions in their implementations. Consequently, the advantages of utilizing lambda expressions can be significantly compromised by the collateral side effects. In this study, we present the first large-scale, quantitative and qualitative empirical study to characterize and understand inappropriate usages of lambda expressions. Particularly, we summarized seven main reasons for the removal of lambdas as well as seven common migration patterns. For instance, we observe that lambdas using customized functional interfaces are more likely to be removed by developers. Moreover, from a complementary perspective, we performed a user study over 30 developers to seek the underlying reasons why they remove lambda expressions in practice. Finally, based on our empirical results, we made suggestions on scenarios to avoid lambda usages for Java developers and also pointed out future directions for researchers.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678600"}, {"primary_key": "2203520", "vector": [], "sparse_vector": [], "title": "An Automated Pipeline for Privacy Leak Analysis of Android Applications.", "authors": ["<PERSON><PERSON>"], "summary": "We propose an automated pipeline for analyzing privacy leaks in Android applications. By using a combination of dynamic and static analysis, we validate the results from each other to improve accuracy. Compare to the state-of-the-art approaches, we not only capture the network traffic for analysis, but also look into the data flows inside the application. We particularly focus on the privacy leakage caused by third-party services and high-risk permissions. The proposed automated approach will combine taint analysis, permission analysis, network traffic analysis, and dynamic function tracing during run-time to identify private information leaks. We further implement an automatic validation and complementation process to reduce false positives. A small-scale experiment has been conducted on 30 Android applications and a large-scale experiment on more than 10,000 Android applications is in progress.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678875"}, {"primary_key": "2203521", "vector": [], "sparse_vector": [], "title": "Finding A Needle in a Haystack: Automated Mining of Silent Vulnerability Fixes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Zhiyuan Wan", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Following the coordinated vulnerability disclosure model, a vulnerability in open source software (OSS) is sug-gested to be fixed \"silently\", without disclosing the fix until the vulnerability is disclosed. Yet, it is crucial for OSS users to be aware of vulnerability fixes as early as possible, as once a vulnerability fix is pushed to the source code repository, a malicious party could probe for the corresponding vulnerability to exploit it. In practice, OSS users often rely on the vulnerability disclosure information from security advisories (e.g., National Vulnerability Database) to sense vulnerability fixes. However, the time between the availability of a vulnerability fix and its disclosure can vary from days to months, and in some cases, even years. Due to manpower constraints and the lack of expert knowledge, it is infeasible for OSS users to manually analyze all code changes for vulnerability fix detection. Therefore, it is essential to identify vulnerability fixes automatically and promptly. In a first-of-its-kind study, we propose VulFixMiner, a Transformer-based approach, capable of automatically extracting semantic meaning from commit-level code changes to identify silent vulnerability fixes. We construct our model using sampled commits from 204 projects, and evaluate using the full set of commits from 52 additional projects. The evaluation results show that VulFixMiner outperforms various state-of-the-art baselines in terms of AUC (i.e., 0.81 and 0.73 on Java and Python dataset, respectively) and two effort-aware performance metrics (i.e., EffortCost, P opt ). Especially, with an effort of inspecting 5% of total LOC, VulFixMiner can identify 49% of total vulnerability fixes. Additionally, with manual verification of sampled commits that were identified as vulnerability fixes, but not marked as such in our dataset, we observe that 35% (29 out of 82) of the commits are for fixing vulnerabilities, indicating VulFixMiner is also capable of identifying unreported vulnerability fixes.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678720"}, {"primary_key": "2203522", "vector": [], "sparse_vector": [], "title": "Finding the Missing Piece: Permission Specification Analysis for Android NDK.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Android research community has long focused on building the permission specification for Android framework APIs, which can be referenced by app developers to request the necessary permissions for their apps. However, existing studies just analyze the permission specification for Java framework APIs in Android SDK, whereas the permission specification for native framework APIs in Android NDK remains intact. Since more and more apps implement their functionalities using native framework APIs, and the permission specification for these APIs is poorly documented, the permission specification analysis for Android NDK is in urgent need. To fill in the gap, in this paper, we conduct the first permission specification analysis for Android NDK. In particular, to automatically generate the permission specification for Android NDK, we design and develop PSGen, a new tool that statically analyzes the implementation of Android framework and Android kernel to correlate native framework APIs with their required permissions. Applying PSGen to 3 Android systems, including Android 9.0, 10.0, and 11.0, we find that PSGen can precisely build the permission specification. With the help of PSGen, we discover more than 200 native framework APIs that are correlated with at least one permission.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678843"}, {"primary_key": "2203523", "vector": [], "sparse_vector": [], "title": "DeepMemory: Model-based Memorization Analysis of Deep Neural Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The neural network model is having a significant impact on many real-world applications. Unfortunately, the increasing popularity and complexity of these models also amplifies their security and privacy challenges, with privacy leakage from training data being one of the most prominent issues. In this context, prior studies proposed to analyze the abstraction behavior of neural network models, e.g., RNN, to understand their robustness. However, the existing research rarely addresses privacy breaches caused by memorization in neural language models. To fill this gap, we propose a novel approach, DeepMemory, that analyzes memorization behavior for a neural language model. We first construct a memorization-analysis-oriented model, taking both training data and a neural language model as input. We then build a semantic first-order Markov model to bind the constructed memorization-analysis-oriented model to the training data to analyze memorization distribution. Finally, we apply our approach to address data leakage issues associated with memorization and to assist in dememorization. We evaluate our approach on one of the most popular neural language models, the LSTM-based language model, with three public datasets, namely, WikiText-103, WMT2017, and IWSLT2016. We find that sentences in the studied datasets with low perplexity are more likely to be memorized. Our approach achieves an average AUC of 0.73 in automatically identifying data leakage issues during assessment. We also show that with the assistance of <PERSON><PERSON><PERSON>ory, data breaches due to memorization of neural language models can be successfully mitigated by mutating training data without reducing the performance of neural language models.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678871"}, {"primary_key": "2203524", "vector": [], "sparse_vector": [], "title": "Dynamic Generation of Python Bindings for HPC Kernels.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Traditionally, high performance kernels (HPKs) have been written in statically typed languages, such as C/C++ and Fortran. A recent trend among scientists—prototyping applications in dynamic languages such as Python—created a gap between the applications and existing HPKs. Thus, scientists have to either reimplement necessary kernels or manually create a connection layer to leverage existing kernels. Either option requires substantial development effort and slows down progress in science. We present a technique, dubbed WayOut, which automatically generates the entire connection layer for HPKs invoked from Python and written in C/C++. WayOut performs a hybrid analysis: it statically analyzes header files to generate Python wrapper classes and functions, and dynamically generates bindings for those kernels. By leveraging the type information available at run-time, it generates only the necessary bindings. We evaluate WayOut by rewriting dozens of existing examples from C/C++ to Python and leveraging HPKs enabled by WayOut. Our experiments show the feasibility of our technique, as well as negligible performance overhead on HPKs performance.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678726"}, {"primary_key": "2203525", "vector": [], "sparse_vector": [], "title": "Restoring the Executability of Jupyter Notebooks by Automatic Upgrade of Deprecated APIs.", "authors": ["<PERSON><PERSON>", "Ripon K. Saha", "<PERSON><PERSON><PERSON>", "Sarfraz Khurshid"], "summary": "Data scientists typically practice exploratory programming using computational notebooks, to comprehend new data and extract insights. To do this they iteratively refine their code, actively trying to re-use and re-purpose solutions created by other data scientists, in real time. However, recent studies have shown that a vast majority of publicly available notebooks cannot be executed out of the box. One of the prominent reasons is the deprecation of data science APIs used in such notebooks, due to the rapid evolution of data science libraries. In this work we propose RELANCER, an automatic technique that restores the executability of broken Jupyter Notebooks, in near real time, by upgrading deprecated APIs. RELANCER employs an iterative runtime-error-driven approach to identify and fix one API issue at a time. This is supported by a machine-learned model which uses the runtime error message to predict the kind of API repair needed - an update in the API or package name, a parameter, or a parameter value. Then RELANCER creates a search space of candidate repairs by combining knowledge from API migration examples on GitHub as well as the API documentation and employs a second machine-learned model to rank this space of candidate mappings. An evaluation of RELANCER on a curated dataset of 255 un-executable Jupyter Notebooks from Kaggle shows that RELANCER can successfully restore the executability of 56% of the subjects, while baselines relying on just GitHub examples and just API documentation can only fix 38% and 36% of the subjects respectively. Further, pursuant to its real-time use case, RELANCER can restore execution to 49% of subjects, within a 5 minute time limit, while a baseline lacking its machine learning models can only fix 24%.", "published": "2021-01-01", "category": "ase", "pdf_url": "", "sub_summary": "", "source": "ase", "doi": "10.1109/ASE51524.2021.9678889"}]