import { useState, useEffect } from 'react';
import { conferenceOptions, journalOptions } from './conference-info';

export const useFilters = () => {
  // 年份选择状态
  const [selectedYears, setSelectedYears] = useState<string[]>([]);
  const [isYearsOpen, setIsYearsOpen] = useState(false);
  
  // 论文来源选择状态
  const [sourceType, setSourceType] = useState<'arxiv' | 'conference' | 'journal'>('arxiv');
  const [selectedConferences, setSelectedConferences] = useState<string[]>([]);
  const [selectedJournals, setSelectedJournals] = useState<string[]>([]);
  const [isConferencesOpen, setIsConferencesOpen] = useState(false);
  const [isJournalsOpen, setIsJournalsOpen] = useState(false);
  
  // 更多选项框的展开/折叠状态
  const [isMoreOptionsOpen, setIsMoreOptionsOpen] = useState(false);

  // 生成年份选项（从大到小排序）
  const yearOptions = Array.from({ length: 16 }, (_, i) => (2025 - i).toString());

  // 添加年份选择变化的处理函数
  const handleYearChange = (year: string) => {
    setSelectedYears(prev => {
      if (prev.includes(year)) {
        return prev.filter(y => y !== year);
      }
      return [...prev, year];
    });
  };

  // 添加会议选择变化的处理函数
  const handleConferenceChange = (conference: string) => {
    setSelectedConferences(prev => {
      if (prev.includes(conference)) {
        return prev.filter(c => c !== conference);
      }
      return [...prev, conference];
    });
  };

  // 添加期刊选择变化的处理函数
  const handleJournalChange = (journal: string) => {
    setSelectedJournals(prev => {
      if (prev.includes(journal)) {
        return prev.filter(j => j !== journal);
      }
      return [...prev, journal];
    });
  };

  // 初始化论文来源类型
  useEffect(() => {
    const storedSourceType = localStorage.getItem('sourceType');
    if (storedSourceType) {
      setSourceType(storedSourceType as 'arxiv' | 'conference' | 'journal');
    }
  }, []);

  // 保存论文来源类型
  useEffect(() => {
    localStorage.setItem('sourceType', sourceType);
  }, [sourceType]);

  // 保存会议选择
  useEffect(() => {
    localStorage.setItem('selectedConferences', JSON.stringify(selectedConferences));
  }, [selectedConferences]);

  // 保存期刊选择
  useEffect(() => {
    localStorage.setItem('selectedJournals', JSON.stringify(selectedJournals));
  }, [selectedJournals]);

  // 获取源参数
  const getSourceParam = () => {
    if (sourceType === 'arxiv') {
      return 'arxiv';
    } else if (sourceType === 'conference') {
      return selectedConferences.length > 0 
        ? selectedConferences.join(',') 
        : conferenceOptions.map(option => option.value).join(',');
    } else if (sourceType === 'journal') {
      return selectedJournals.length > 0 
        ? selectedJournals.join(',') 
        : journalOptions.map(option => option.value).join(',');
    }
    return '';
  };

  return {
    selectedYears,
    setSelectedYears,
    isYearsOpen,
    setIsYearsOpen,
    sourceType,
    setSourceType,
    selectedConferences,
    setSelectedConferences,
    selectedJournals,
    setSelectedJournals,
    isConferencesOpen,
    setIsConferencesOpen,
    isJournalsOpen,
    setIsJournalsOpen,
    isMoreOptionsOpen,
    setIsMoreOptionsOpen,
    yearOptions,
    handleYearChange,
    handleConferenceChange,
    handleJournalChange,
    getSourceParam
  };
}; 