[{"primary_key": "4064865", "vector": [], "sparse_vector": [], "title": "Verification-Aided Debugging: An Interactive Web-Service for Exploring Error Witnesses.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Traditionally, a verification task is considered solved as soon as a property violation or a correctness proof is found. In practice, this is where the actual work starts: Is it just a false alarm? Is the error reproducible? Can the error report later be re-used for bug fixing or regression testing? The advent ofexchangeable witnessesis a paradigm shift in verification, from simple answerstrueandfalsetowards qualitatively more valuable information about the reason for the property violation. This paper explains a convenient web-based toolchain that can be used to answer the above questions. We consider as example application the verification of C programs. Our first component collects witnesses and stores them for later re-use; for example, if the bug is fixed, the witness can be tried once again and should now be rejected, or, if the bug was not scheduled for fixing, the database can later provide the witnesses in case an engineer wants to start fixing the bug. Our second component is a web service that takes as input a witness for the property violation and (re-)validates it, i.e., it re-plays the witness on the system in order to re-explore the state-space in question. The third component is a web service that continues from the second step by offering an interactive visualization that interconnects the error path, the system’s sources, the values on the path (test vectors), and the reachability graph. We evaluated the feasibility of our approach on a large benchmark of verification tasks.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_28"}, {"primary_key": "4064866", "vector": [], "sparse_vector": [], "title": "Automatic Verification of Iterated Separating Conjunctions Using Symbolic Execution.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In permission logics such as separation logic, the iterated separating conjunction is a quantifier denoting access permission to an unbounded set of heap locations. In contrast to recursive predicates, iterated separating conjunctions do not prescribe a structure on the locations they range over, and so do not restrict how to traverse and modify these locations. This flexibility is important for the verification of random-access data structures such as arrays and data structures that can be traversed in multiple ways such as graphs. Despite its usefulness, no automatic program verifier natively supports iterated separating conjunctions; they are especially difficult to incorporate into symbolic execution engines, the prevalent technique for building verifiers for these logics. In this paper, we present the first symbolic execution technique to support general iterated separating conjunctions. We propose a novel representation of symbolic heaps and flexible support for logical specifications that quantify over heap locations. Our technique exhibits predictable and fast performance despite employing quantifiers at the SMT level, by carefully controlling quantifier instantiations. It is compatible with other features of permission logics such as fractional permissions, recursive predicates, and abstraction functions. Our technique is implemented as an extension of the Viper verification infrastructure.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_22"}, {"primary_key": "4064868", "vector": [], "sparse_vector": [], "title": "Stateless Model Checking for POWER.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present the first framework for efficient application of stateless model checking (SMC) to programs running under the relaxed memory model of POWER. The framework combines several contributions. The first contribution is that we develop a scheme for systematically deriving operational execution models from existing axiomatic ones. The scheme is such that the derived execution models are well suited for efficient SMC. We apply our scheme to the axiomatic model of POWER from [8]. Our main contribution is a technique for efficient SMC, calledRelaxed Stateless Model Checking(RSMC), which systematically explores the possible inequivalent executions of a program. RSMC is suitable for execution models obtained using our scheme. We prove that RSMC is sound and optimal for the POWER memory model, in the sense that each complete program behavior is explored exactly once. We show the feasibility of our technique by providing an implementation for programs written in C/pthreads.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_8"}, {"primary_key": "4064870", "vector": [], "sparse_vector": [], "title": "Compositional Synthesis of Reactive Controllers for Multi-agent Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ufuk <PERSON>"], "summary": "In this paper we consider the controller synthesis problem for multi-agent systems that consist of a set of controlled and uncontrolled agents. Controlled agents may need to cooperate with each other and react to the actions of uncontrolled agents in order to fulfill their objectives. Besides, the controlled agents may be imperfect, i.e., only partially observe their environment, for example due to the limitations in their sensors. We propose a framework for controller synthesis based on compositional reactive synthesis. We implement the algorithms symbolically and apply them to a robot motion planning case study where multiple robots are placed on a grid-world with static obstacles and other dynamic, uncontrolled and potentially adversarial robots. We consider different objectives such as collision avoidance, keeping a formation and bounded reachability. We show that by taking advantage of the structure of the system, compositional synthesis algorithm can significantly outperform centralized synthesis approach, both from time and memory perspective, and can solve problems where the centralized algorithm is infeasible. Our findings show the potential of symbolic and compositional reactive synthesis methods as planning algorithms in the presence of dynamically changing and possibly adversarial environment.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_14"}, {"primary_key": "4064871", "vector": [], "sparse_vector": [], "title": "Markov Chains and Unambiguous Büchi Automata.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Unambiguous automata, i.e., nondeterministic automata with the restriction of having at most one accepting run over a word, have the potential to be used instead of deterministic automata in settings where nondeterministic automata can not be applied in general. In this paper, we provide a polynomially time-bounded algorithm for probabilistic model checking of discrete-time Markov chains against unambiguous Büchi automata specifications and report on our implementation and experiments.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_2"}, {"primary_key": "4064872", "vector": [], "sparse_vector": [], "title": "Synthesizing Probabilistic Invariants via Doob&apos;s Decomposition.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "When analyzing probabilistic computations, a powerful approach is to first find amartingale—an expression on the program variables whose expectation remains invariant—and then apply the optional stopping theorem in order to infer properties at termination time. One of the main challenges, then, is to systematically find martingales. We propose a novel procedure to synthesize martingale expressions from an arbitrary initial expression. Contrary to state-of-the-art approaches, we do not rely on constraint solving. Instead, we use a symbolic construction based on<PERSON><PERSON><PERSON>’s decomposition. This procedure can produce very complex martingales, expressed in terms of conditional expectations. We show how toautomaticallygenerate and simplify these martingales, as well as how to apply theoptional stopping theoremto infer properties at termination time. This last step typically involves some simplification steps, and is usually done manually in current approaches. We implement our techniques in a prototype tool and demonstrate our process on several classical examples. Some of them go beyond the capability of current semi-automatic approaches.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_3"}, {"primary_key": "4064874", "vector": [], "sparse_vector": [], "title": "Solving Parity Games via Priority Promotion.", "authors": ["<PERSON><PERSON>", "Daniele Dell&apos;Erba", "<PERSON><PERSON><PERSON>"], "summary": "We considerparity games, a special form of two-player infinite-duration games on numerically labelled graphs, whose winning condition requires that the maximal value of a label occurring infinitely often during a play be of some specific parity. The problem has a rather intriguing status from a complexity theoretic viewpoint, since it belongs to the class, and still open is the question whether it can be solved in polynomial time. Parity games also have great practical interest, as they arise in many fields of theoretical computer science, most notably logic, automata theory, and formal verification. In this paper, we propose a new algorithm for the solution of the problem, based on the idea of promoting vertices to higher priorities during the search for winning regions. The proposed approach has nice computational properties, exhibiting the best space complexity among the currently known solutions. Experimental results on both random games and benchmark families show that the technique is also very effective in practice.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_15"}, {"primary_key": "4064875", "vector": [], "sparse_vector": [], "title": "Synthesis of Self-Stabilising and Byzantine-Resilient Distributed Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fault-tolerant distributed algorithms play an increasingly important role in many applications, and their correct and efficient implementation is notoriously difficult. We present an automatic approach to synthesise provably correct fault-tolerant distributed algorithms from formal specifications in linear-time temporal logic. The supported system model covers synchronous reactive systems with finite local state, while the failure model includes strong self-stabilisation as well as Byzantine failures. The synthesis approach for a fixed-size network of processes iscompletefor realisable specifications, and can optimise the solution for small implementations and short stabilisation time. To solve the bounded synthesis problem with Byzantine failures more efficiently, we design an incremental, CEGIS-like loop. Finally, we define two classes of problems for which our synthesis algorithm obtains solutions that are not only correct in fixed-size networks, but in networks of arbitrary size.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_9"}, {"primary_key": "4064877", "vector": [], "sparse_vector": [], "title": "Symbolic Optimal Reachability in Weighted Timed Automata.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Weighted timed automata have been defined in the early 2000 s for modelling resource-consumption or -allocation problems in real-time systems. Optimal reachability is decidable in weighted timed automata, and a symbolic forward algorithm has been developed to solve that problem. This algorithm uses so-calledpriced zones, an extension of standard zones withcost functions. In order to ensure termination, the algorithm requires clocks to be bounded. For unpriced timed automata, much work has been done to develop sound abstractions adapted to the forward exploration of timed automata, ensuring termination of the model-checking algorithm without bounding the clocks. In this paper, we take advantage of recent developments on abstractions for timed automata, and propose an algorithm allowing for symbolic analysis of all weighted timed automata, without requiring bounded clocks.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_28"}, {"primary_key": "4064878", "vector": [], "sparse_vector": [], "title": "The Kind 2 Model Checker.", "authors": ["<PERSON>rien Champion", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Kind2 is an open-source, multi-engine, SMT-based model checker for safety properties of finite- and infinite-state synchronous reactive systems. It takes as input models written in an extension of the Lustre language that allows the specification of assume-guarantee-style contracts for system components.Kind2 was implemented from scratch based on techniques used by its predecessor, thePKindmodel checker. This paper discusses a number of improvements overPKindin terms of invariant generation. It also introduces two main features: contract-based compositional reasoning and certificate generation.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_29"}, {"primary_key": "4064880", "vector": [], "sparse_vector": [], "title": "Termination Analysis of Probabilistic Programs Through Positivstellensatz&apos;s.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Hongfei Fu", "<PERSON>"], "summary": "We consider nondeterministic probabilistic programs with the most basic liveness property of termination. We present efficient methods for termination analysis of nondeterministic probabilistic programs with polynomial guards and assignments. Our approach is through synthesis of polynomial ranking supermartingales, that on one hand significantly generalizes linear ranking supermartingales and on the other hand is a counterpart of polynomial ranking-functions for proving termination of nonprobabilistic programs. The approach synthesizes polynomial ranking-supermartingales through Positivstellensatz’s, yielding an efficient method which is not only sound, but also semi-complete over a large subclass of programs. We show experimental results to demonstrate that our approach can handle several classical programs with complex polynomial guards and assignments, and can synthesize efficient quadratic ranking-supermartingales when a linear one does not exist even for simple affine programs.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_1"}, {"primary_key": "4064881", "vector": [], "sparse_vector": [], "title": "The Commutativity Problem of the MapReduce Framework: A Transducer-Based Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "MapReduce is a popular programming model for data parallel computation. In MapReduce, thereducerproduces an output from a list of inputs. Due to the scheduling policy of the platform, the inputs may arrive at the reducers in different order. Thecommutativity problemof reducers asks if the output of a reducer is independent of the order of its inputs. Although the problem is undecidable in general, the MapReduce programs in practice are usually used for data analytics and thus require very simple control flow. By exploiting the simplicity, we propose a programming language for reducers where the commutativity problem is decidable. The main idea of the reducer language is to separate the control and data flow of programs and disallow arithmetic operations in the control flow. The decision procedure for the commutativity problem is obtained through a reduction to the equivalence problem ofstreaming numerical transducers(SNTs), a novel automata model over infinite alphabets introduced in this paper. The design of SNTs is inspired by streaming transducers (<PERSON><PERSON> and <PERSON>, POPL 2011). Nevertheless, the two models are intrinsically different since the outputs of SNTs are integers while those of streaming transducers are data words. The decidability of the equivalence of SNTs is achieved with an involved combinatorial analysis of the evolvement of the values of the integer variables during the runs of SNTs.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_6"}, {"primary_key": "4064882", "vector": [], "sparse_vector": [], "title": "Structural Synthesis for GXW Specifications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We define the\\(\\textsf {\\small {GXW}} \\)fragment of linear temporal logic (LTL) as the basis for synthesizing embedded control software for safety-critical applications. Since\\(\\textsf {\\small {GXW}} \\)includes the use of aweak-untiloperator we are able to specify a number of diverse programmable logic control (PLC) problems, which we have compiled from industrial training sets. For\\(\\textsf {\\small {GXW}} \\)controller specifications, we develop a novel approach for synthesizing a set of synchronously communicating actor-based controllers. This synthesis algorithm proceeds by means of recursing over the structure of\\(\\textsf {\\small {GXW}} \\)specifications, and generates a set of dedicated and synchronously communicating sub-controllers according to the formula structure. In a subsequent step,2QBFconstraint solving identifies and tries to resolve potential conflicts between individual\\(\\textsf {\\small {GXW}} \\)specifications. This structural approach to\\(\\textsf {\\small {GXW}} \\)synthesis supports traceability between requirements and the generated control code as mandated by certification regimes for safety-critical software. Our experimental results suggest thatGXWsynthesis scales well to industrial-sized control synthesis problems with 20 input and output ports and beyond.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_6"}, {"primary_key": "4064883", "vector": [], "sparse_vector": [], "title": "Hitting Families of Schedules for Asynchronous Programs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the following basic task in the testing of concurrent systems. The input to the task is a partial order of events, which models actions performed on or by the system and specifies ordering constraints between them. The task is to determine if some scheduling of these events can result in a bug. The number of schedules to be explored can, in general, be exponential. Empirically, many bugs in concurrent programs have been observed to have small bug depth; that is, these bugs are exposed by every schedule that ordersdspecific events in a particular way, irrespective of how the other events are ordered, anddis small compared to the total number of events. To find all bugs of depthd, one needs to only test ad-hitting familyof schedules: we call a set of schedules ad-hitting family if for each set ofdevents, and for each allowed ordering of these events, there is some schedule in the family that executes these events in this ordering. The size of ad-hitting family may be much smaller than the number of all possible schedules, and a natural question is whether one can findd-hitting families of schedules that have small size. In general, finding the size of optimald-hitting families is hard, even ford= 2. We show, however, that when the partial order is a tree, one can explicitly constructd-hitting families of schedules of small size. When the tree is balanced, our constructions are polylogarithmic in the number of events.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_9"}, {"primary_key": "4064884", "vector": [], "sparse_vector": [], "title": "A Decision Procedure for Sets, Binary Relations and Partial Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we present a decision procedure for sets, binary relations and partial functions. The language accepted by the decision procedure includes untyped, hereditarily finite sets, where some of their elements can be variables, and basically all the classic set and relational operators used in formal languages such as B and Z. Partial functions are encoded as binary relations which in turn are just sets of ordered pairs. Sets are first-class entities in the language, thus they are not encoded in lower level theories. The decision procedure exploits set unification and set constraint solving as primitive features. The procedure is proved to be sound, complete and terminating. A Prolog implementation is presented.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_10"}, {"primary_key": "4064885", "vector": [], "sparse_vector": [], "title": "Qlose: Program Repair with Quantitative Objectives.", "authors": ["Loris D&apos;Antoni", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The goal of automatic program repair is to identify a set of syntactic changes that can turn a program that is incorrect with respect to a given specification into a correct one. Existing program repair techniques typically aim to findanyprogram that meets the given specification. Such “best-effort” strategies can end up generating a program that is quite different from the original one. Novel techniques have been proposed to compute syntactically minimal program fixes, but the smallest syntactic fix to a program can still significantly alter the original program’s behaviour. We propose a new approach to program repair based onprogram distances, which can quantify changes not only to the program syntax but also to the program semantics. We call this thequantitative program repair problemwhere the “optimal” repair is derived using multiple distances. We implement a solution to the quantitative repair problem in a prototype tool calledQlose(Quantitatively close), using the program synthesizerSketch. We evaluate the effectiveness of different distances in obtaining desirable repairs by evaluatingQloseon programs taken from educational tools such as CodeHunt and edX.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_21"}, {"primary_key": "4064886", "vector": [], "sparse_vector": [], "title": "Array Folds Logic.", "authors": ["Przemyslaw Daca", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present an extension to the quantifier-free theory of integer arrays which allows us to express counting. The properties expressible in Array Folds Logic (AFL) include statements such as “the first array cell contains the array length,” and “the array contains equally many minimal and maximal elements.” These properties cannot be expressed in quantified fragments of the theory of arrays, nor in the theory of concatenation. Using reduction to counter machines, we show that the satisfiability problem of AFL is PSPACE-complete, and with a natural restriction the complexity decreases to NP. We also show that adding either universal quantifiers or concatenation leads to undecidability. AFL contains terms that fold a function over an array. We demonstrate that folding, a well-known concept from functional languages, allows us to concisely summarize loops that count over arrays, which occurs frequently in real-life programs. We provide a tool that can discharge proof obligations in AFL, and we demonstrate on practical examples that our decision procedure can solve a broad range of problems in symbolic testing and program verification.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_13"}, {"primary_key": "4064888", "vector": [], "sparse_vector": [], "title": "Infinite-State Liveness-to-Safety via Implicit Abstraction and Well-Founded Relations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a fully-symbolic LTL model checking approach for infinite-state transition systems. We extendliveness-to-safety, a prominent approach in the finite-state case, by means ofimplicit abstraction, to effectively prove the absence of abstract fair loops without explicitly constructing the abstract state space. We increase the effectiveness of the approach by integrating termination techniques based onwell-founded relationsderived from ranking functions. The idea is to prove that any existing abstract fair loop is covered by a given set of well-founded relations. Within this framework,\\(k\\)-liveness is integrated as a generic ranking function. The algorithm iterates by attempting to remove spurious abstract fair loops: either it finds new predicates, to avoid spurious abstract prefixes, or it introduces new well-founded relations, based on the analysis of the abstract lasso. The implementation fully leverages the efficiency and incrementality of the underlying safety checkerIC3ia. The proposed approach outperforms other temporal checkers on a wide class of benchmarks.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_15"}, {"primary_key": "4064889", "vector": [], "sparse_vector": [], "title": "Proving Parameterized Systems Safe by Generalizing Clausal Proofs of Small Instances.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We describe an approach to proving safety properties of parameterized reactive systems. Clausal inductive proofs for small instances are generalized to quantified formulae, which are then checked against the whole family of systems. Clausal proofs are generated at the bit-level by the IC3 algorithm. The clauses are partitioned into blocks, each of which is represented by a quantified implication formula, whose antecedent is a conjunction of modular linear arithmetic constraints. Each quantified formula approximates the set of clauses it represents; good approximations are computed through a process of proof saturation, and through the computation of convex hulls. Candidate proofs are conjunctions of quantified lemmas. For systems with a small-model bound, the proof can often be shown valid for all values of the parameter. When the candidate proof cannot be shown valid, it can still be used to bootstrap finite proofs to permit verification at larger values of the parameter. While the method is incomplete, it produces non-trivial invariants for a suite of benchmarks including hardware circuits and protocols.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_16"}, {"primary_key": "4064890", "vector": [], "sparse_vector": [], "title": "Effectively Propositional Interpolants.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel interpolation algorithm foreffectively propositional logic(epr), a decidable fragment of first-order logic that enjoys a small-model property.epris a powerful fragment of quantified formulas that has been used to model and verify a range of programs, including heap-manipulating programs and distributed protocols. Our interpolation techniquesamples finite modelsfrom two sides of the interpolation problem andgeneralizes<PERSON> to learn a quantified interpolant. Our results demonstrate our technique’s ability to compute universally-quantified, existentially-quantified, as well as alternation-free interpolants and inductive invariants, thus improving the state of the art.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_12"}, {"primary_key": "4064891", "vector": [], "sparse_vector": [], "title": "Parsimonious, Simulation Based Verification of Linear Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a technique to verify safety properties of linear systems (possibly time varying) using very few simulations. For a linear system of dimensionn, our technique needs\\(n+1\\)simulation runs. This is in contrast to current simulation based approaches, where the number of simulations either depends upon the number of vertices in the convex polyhedral initial set, or on the proximity of the unsafe set to the set of reachable states. At its core, our algorithm exploits the superposition principle of linear systems. Our algorithm computes both an over and an under approximation of the set of reachable states.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_26"}, {"primary_key": "4064892", "vector": [], "sparse_vector": [], "title": "Slugs: Extensible GR(1) Synthesis.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Applying reactive synthesis in practice often requires modifications of the synthesis algorithm in order to obtain useful implementations. We presentslugs, a generalized reactivity(1) synthesis tool that has a powerful plugin architecture for modifying any aspect of the synthesis process to fit the application.Slugscomes pre-equipped with a variety of plugins that improve the quality of the synthesized solutions along criteria such as quick response, cost-optimality, and error-resilience. We demonstrate the utility and scalability of the tool on an example from robotics.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_18"}, {"primary_key": "4064893", "vector": [], "sparse_vector": [], "title": "Synthesis of Fault-Attack Countermeasures for Cryptographic Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fault sensitivity analysis (FSA) is a side-channel attack method that injects faults to cryptographic circuits through clock glitching and applies statistical analysis to deduce sensitive data such as the cryptographic key. It exploits the correlation between the circuit’s signal path delays and sensitive data. A countermeasure, in this case, is an alternative implementation of the circuit where signal path delays are made independent of the sensitive data. However, manually developing such countermeasure is tedious and error prone. In this paper, we propose a method for synthesizing the countermeasure automatically to defend against FSA attacks. Our method uses a syntax-guided inductive synthesis procedure combined with a light-weight static analysis. Given a circuit and a set of sensitive signals as input, it returns a functionally-equivalent and FSA-resistant circuit as output, where all path delays are made independent of the sensitive signals. We have implemented our method and evaluated it on a set of cryptographic circuits. Our experiments show that the method is both scalable and effective in eliminating FSA vulnerabilities.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_19"}, {"primary_key": "4064894", "vector": [], "sparse_vector": [], "title": "Automated Circular Assume-Guarantee Reasoning with N-way Decomposition and Alphabet Refinement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Corina S<PERSON>", "<PERSON>"], "summary": "In this work we develop anautomatedcircular reasoning framework that is applicable to systems decomposed intomultiplecomponents. Our framework uses a family of circular assume-guarantee rules for which we give conditions for soundness and completeness. The assumptions used in the rules are initially approximate and their alphabets are automatically refined based on the counterexamples obtained from model checking the rule premises. A key feature of the framework is that the compositional rules that are used changedynamicallywith each iteration of the alphabet refinement, to only use assumptions that are relevant for the current alphabet, resulting in a smaller number of assumptions and smaller state spaces to analyze for each premise. Our preliminary evaluation of the proposed approach shows promising results compared to 2-way and monolithic verification.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_18"}, {"primary_key": "4064895", "vector": [], "sparse_vector": [], "title": "Automatic Reachability Analysis for Nonlinear Hybrid Models with C2E2.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "C2E2 is a bounded reachability analysis tool for nonlinear dynamical systems and hybrid automaton models. Previously it required users to annotate each system of differential equations of the hybrid automaton withdiscrepancy functions, and since these annotations are difficult to get for general nonlinear differential equations, the tool had limited usability. This version of C2E2 is improved in several ways, the most prominent among which is the elimination of the need for user-provided discrepancy functions. It automatically computes piece-wise (or local) discrepancy functions around the reachable parts of the state space using symbolically computed Jacobian matrix and eigenvalue perturbation bounds. The special cases of linear and constant rate differential equations are handled with more efficient algorithm. In this paper, we discuss these and other new features that make the new C2E2 a usable tool for bounded reachability analysis of hybrid systems.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_29"}, {"primary_key": "4064896", "vector": [], "sparse_vector": [], "title": "Property Directed Equivalence via Abstract Simulation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a novel approach for automated incremental verification that employs both reusable and relational specifications of software to incrementally verify pairs of programs with possibly nested loops. It analyzes two programs,P- the one already verified, andQ- the one needed to be verified, and proceeds by detecting an abstraction\\(\\alpha {P}\\)ofPand a simulation\\(\\rho \\), such that\\(\\alpha {P}\\)simulatesQvia\\(\\rho \\). The key idea behind our simulation synthesis is to drive construction of both\\(\\alpha {P}\\)and\\(\\rho \\)by the safe inductive invariants ofP, thus guaranteeing the property preservations by the results. Finally, our approach allows effective lifting of the safe inductive invariants ofPtoQusing only\\(\\alpha {P}\\)and\\(\\rho \\). Based on our evaluation, in many cases when the absolute equivalence between programs cannot be proven, our approach is able to establish theproperty directed equivalence, confirming that the programQis safe.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_24"}, {"primary_key": "4064897", "vector": [], "sparse_vector": [], "title": "Bounded Cycle Synthesis.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a new approach for the synthesis of Mealy machines from specifications in linear-time temporal logic (LTL), where the number of cycles in the state graph of the implementation is limited by a given bound. Bounding the number of cycles leads to implementations that are structurally simpler and easier to understand. We solve the synthesis problem via an extension of SAT-based bounded synthesis, where we additionally construct a witness structure that limits the number of cycles. We also establish a triple-exponential upper and lower bound for the potential blow-up between the length of the LTL formula and the number of cycles in the state graph.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_7"}, {"primary_key": "4064898", "vector": [], "sparse_vector": [], "title": "Combining Model Learning and Model Checking to Analyze TCP Implementations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Frits <PERSON><PERSON>"], "summary": "We combine model learning and model checking in a challenging case study involving Linux, Windows and FreeBSD implementations of TCP. We use model learning to infer models of different software components and then apply model checking to fully explore what may happen when these components (e.g. a Linux client and a Windows server) interact. Our analysis reveals several instances in which TCP implementations do not conform to their RFC specifications.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_25"}, {"primary_key": "4064899", "vector": [], "sparse_vector": [], "title": "BDD-Based Boolean Functional Synthesis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Boolean functional synthesisis the process of automatically obtaining a constructive formalization from a declarative relation that is given as a Boolean formula. Recently, a framework was proposed for Boolean functional synthesis that is based on Craig Interpolation and in which Boolean functions are represented as And-Inverter Graphs (AIGs). In this work we adapt this framework to the setting of Binary Decision Diagrams (BDDs), a standard data structure for representation of Boolean functions. Our motivation in studying BDDs is their common usage intemporal synthesis, a fundamental technique for constructing control software/hardware from temporal specifications, in which Boolean synthesis is a basic step. Rather than using Craig Interpolation, our method relies on a technique calledSelf-Substitution, which can be easily implemented by using existing BDD operations. We also show that this yields a novel way to perform quantifier elimination for BDDs. In addition, we look at certain BDD structures calledinput-first, and propose a technique calledTrimSubstitute, tailored specifically for such structures. Experiments on scalable benchmarks show that both Self-Substitution and TrimSubstitute scale well for benchmarks with good variable orders and significantly outperform current Boolean-synthesis techniques.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_22"}, {"primary_key": "4064900", "vector": [], "sparse_vector": [], "title": "XSat: A Fast Floating-Point Satisfiability Solver.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Satisfiability Modulo Theory (SMT) problem over floating-point arithmetic is a major hurdle in applying SMT techniques to real-world floating-point code. Solving floating-point constraints is challenging in part because floating-point semantics is difficult to specify or abstract. State-of-the-art SMT solvers still often run into difficulties when solving complex, non-linear floating-point constraints. This paper proposes a new approach to SMT solving that does not need to directly reason about the floating-point semantics. Our insight is to establish the equivalence between floating-point satisfiability and a class of mathematical optimization (MO) problems known as unconstrained MO. Our approach (1) systematically reduces floating-point satisfiability to MO, and (2) solves the latter via the Monte Carlo Markov Chain (MCMC) method. We have compared our implementation, XSat, with MathSat, Z3 and Coral, state-of-the-art solvers that support floating-point arithmetic. Evaluated on 34 representative benchmarks from the SMT-Competition 2015, XSat significantly outperforms these solvers. In particular, it provides both 100 % consistent satisfiability results as MathSat and Z3, and an average speedup of more than 700X over MathSat and Z3, while Coral provides inconsistent results on 16 of the benchmarks.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_11"}, {"primary_key": "4064901", "vector": [], "sparse_vector": [], "title": "Model Checking at Scale: Automated Air Traffic Control Design Space Exploration.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Many possible solutions, differing in the assumptions and implementations of the components in use, are usually in competition during early design stages. Deciding which solution to adopt requires considering several trade-offs. Model checking represents a possible way of comparing such designs, however, when the number of designs is large, building and validating so many models may be intractable. During our collaboration with NASA, we faced the challenge of considering a design space with more than 20,000 designs for the NextGen air traffic control system. To deal with this problem, we introduce a compositional, modular, parameterized approach combining model checking with contract-based design to automatically generate large numbers of models from a possible set of components and their implementations. Our approach is fully automated, enabling the generation and validation of all target designs. The 1,620 designs that were most relevant to NASA were analyzed exhaustively. To deal with the massive amount of data generated, we apply novel data-analysis techniques that enable a rich comparison of the designs, including safety aspects. Our results were validated by NASA system designers, and helped to identify novel as well as known problematic configurations.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_1"}, {"primary_key": "4064902", "vector": [], "sparse_vector": [], "title": "PSI: Exact Symbolic Inference for Probabilistic Programs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Probabilistic inference is a key mechanism for reasoning about probabilistic programs. Since exact inference is theoretically expensive, most probabilistic inference systems today have adopted approximate inference techniques, which trade precision for better performance (but often without guarantees). As a result, while desirable for its ultimate precision, the practical effectiveness of exact inference for probabilistic programs is mostly unknown. This paper presentsPSI(http://www.psisolver.org), a novel symbolic analysis system for exact inference in probabilistic programs with both continuous and discrete random variables.PSIcomputes succinct symbolic representations of the joint posterior distribution represented by a given probabilistic program.PSIcan compute answers to various posterior distribution, expectation and assertion queries using its own back-end for symbolic reasoning. Our evaluation shows thatPSIis more effective than existing exact inference approaches: (i) it successfully computed a precise result for more programs, and (ii) simplified expressions that existing computer algebra systems (e.g., Mathematica, Maple) fail to andle.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_4"}, {"primary_key": "4064903", "vector": [], "sparse_vector": [], "title": "RV-Match: Practical Semantics-Based Program Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present RV-Match, a tool for checking C programs for undefined behavior and other common programmer mistakes. Our tool is extracted from the most complete formal semantics of the C11 language. Previous versions of this tool were used primarily for testing the correctness of the semantics, but we have improved it into a tool for doing practical analysis of real C programs. It beats many similar tools in its ability to catch a broad range of undesirable behaviors. We demonstrate this with comparisons based on a third-party benchmark.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_24"}, {"primary_key": "4064904", "vector": [], "sparse_vector": [], "title": "A Simple Algorithm for Solving Qualitative Probabilistic Parity Games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we develop an approach to find strategies that guarantee a property in systems that contain controllable, uncontrollable, and random vertices, resulting in probabilistic games. Such games are a reasonable abstraction of systems that comprise partial control over the system (reflected by controllable transitions), hostile nondeterminism (abstraction of the unknown, such as the behaviour of an attacker or a potentially hostile environment), and probabilistic transitions for the abstraction of unknown behaviour neutral to our goals. We exploit a simple and only mildly adjusted algorithm from the analysis of non-probabilistic systems, and use it to show that the qualitative analysis of probabilistic games inherits the much celebrated sub-exponential complexity from 2-player games. The simple structure of the exploited algorithm allows us to offer tool support for finding the desired strategy, if it exists, for the given systems and properties. Our experimental evaluation shows that our technique is powerful enough to construct simple strategies that guarantee the specified probabilistic temporal properties.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_16"}, {"primary_key": "4064905", "vector": [], "sparse_vector": [], "title": "Learning-Based Assume-Guarantee Regression Verification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Due to enormous resource consumption, model checking each revision of evolving systems repeatedly is impractical. To reduce cost in checking every revision, contextual assumptions are reused from assume-guarantee reasoning. However, contextual assumptions are not always reusable. We propose a fine-grained learning technique to maximize the reuse of contextual assumptions. Based on fine-grained learning, we develop a regressional assume-guarantee verification approach for evolving systems. We have implemented a prototype of our approach and conducted extensive experiments (with 1018 verification tasks). The results suggest promising outlooks for our incremental technique.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_17"}, {"primary_key": "4064907", "vector": [], "sparse_vector": [], "title": "ParCoSS: Efficient Parallelized Compiled Symbolic Simulation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present the tool ParCoSS for verification of cooperative multithreading programs. Our tool is based on the recently proposed Compiled Symbolic Simulation (CSS) technique. Additionally, we employ parallelization to further speed-up the verification. The potential of our tool is shown by evaluation.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_10"}, {"primary_key": "4064908", "vector": [], "sparse_vector": [], "title": "Soufflé: On Synthesis of Program Analyzers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Souffléis an open source programming framework that performs static program analysis expressed in Datalog on very large code bases, including points-to analysis on OpenJDK7 (1.4M program variables, 350K objects, 160K methods) in under a minute.Souffléis being successfully used for Java security analyses at Oracle Labs due to (1) its high-performance, (2) support for rapid program analysis development, and (3) customizability.Souffléincorporates the highly flexible Datalog-based program analysis paradigm while exhibiting performance results that are on-par with manually developed state-of-the-art tools. In this tool paper, we introduce theSouffléarchitecture, usage and demonstrate its applicability for large-scale code analysis on the OpenJDK7 library as a use case.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_23"}, {"primary_key": "4064909", "vector": [], "sparse_vector": [], "title": "Rahft: A Tool for Verifying Horn Clauses Using Abstract Interpretation and Finite Tree Automata.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We presentRahft(Refinement of Abstraction in Horn clauses using Finite Tree automata), anabstraction refinementtool for verifying safety properties of programs expressed as Horn clauses. The paper describes the architecture, strength and weakness, implementation and usage aspects of the tool.Rahftloosely combines three powerful techniques for program verification: (i) program specialisation, (ii) abstract interpretation, and (iii) trace abstraction refinement in a non-trivial way, with the aim of exploiting their strengths and mitigating their weaknesses through the complementary techniques. It is interfaced with an abstract domain, a tool for manipulating finite tree automata and various solvers for reasoning about constraints. Its modular design and customizable components allows for experimenting with new verification techniques and tools developed for Horn clauses.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_14"}, {"primary_key": "4064910", "vector": [], "sparse_vector": [], "title": "JayHorn: A Framework for Verifying Java programs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Building a competitive program verifiers is becoming cheaper. On the front-end side, openly available compiler infrastructure and optimization frameworks take care of hairy problems such as alias analysis, and break down the subtleties of modern languages into a handful of simple instructions that need to be handled. On the back-end side, theorem provers start providing full-fledged model checking algorithms, such as PDR, that take care looping control-flow. In this spirit, we developedJayHorn, a verification framework for Java with the goal of having as few moving parts as possible. Most steps of the translation from Java into logic are implemented as bytecode transformations, with the implication that their soundness can be tested easily. From the transformed bytecode, we generate a set of constrained Horn clauses that are verified using state-of-the-art Horn solvers. We report on our implementation experience and evaluateJayHornon benchmarks.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_19"}, {"primary_key": "4064911", "vector": [], "sparse_vector": [], "title": "Fast, Flexible, and Minimal CTL Synthesis via SMT.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "CTL synthesis [8] is a long-standing problem with applications to synthesising synchronization protocols and concurrent programs. We show how to formulate CTL model checking in terms of “monotonic theories”, enabling us to use theSAT Modulo Monotonic Theories(SMMT) [5] framework to build an efficient SAT-modulo-CTL solver. This yields a powerful procedure for CTL synthesis, which is not only faster than previous techniques from the literature, but also scales to larger and more difficult formulas. Additionally, because it is a constraint-based approach, it can be easily extended with further constraints to guide the synthesis. Moreover, our approach is efficient at producingminimalKripke structures on common CTL synthesis benchmarks.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_8"}, {"primary_key": "4064912", "vector": [], "sparse_vector": [], "title": "Satisfiability Modulo Heap-Based Programs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this work, we present a semi-decision procedure for a fragment of separation logic with user-defined predicates and Presburger arithmetic. To check the satisfiability of a formula, our procedure iteratively unfolds the formula and examines the derived disjuncts. In each iteration, it searches for a proof of either satisfiability or unsatisfiability. Our procedure is further enhanced with automatically inferred invariants as well as detection of cyclic proof. We also identify a syntactically restricted fragment of the logic for which our procedure is terminating and thus complete. This decidable fragment is relatively expressive as it can capture a range of sophisticated data structures with non-trivial pure properties, such as size, sortedness and near-balanced. We have implemented the proposed solver and a new system for verifying heap-based programs. We have evaluated our system on benchmark programs from a software verification competition.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_21"}, {"primary_key": "4064913", "vector": [], "sparse_vector": [], "title": "A SAT-Based Counterexample Guided Method for Unbounded Synthesis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Reactive synthesis techniques based on constructing the winning region of the system have been shown to work well in many cases but suffer from state explosion in others. A different approach, proposed recently, applies SAT solvers in a counterexample guided framework to solve the synthesis problem. However, this method is limited to synthesising systems that execute for a bounded number of steps and is incomplete for synthesis with unbounded safety and reachability objectives. We present an extension of this technique to unbounded synthesis. Our method applies Craig interpolation to abstract game trees produced by counterexample guided search in order to construct a monotonic sequence of may-losing regions. Experimental results based on SYNTCOMP 2015 competition benchmarks show this to be a promising alternative that solves some previously intractable instances.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_20"}, {"primary_key": "4064914", "vector": [], "sparse_vector": [], "title": "Trigger Selection Strategies to Stabilize Program Verifiers.", "authors": ["<PERSON><PERSON>", "Clément Pit-Claudel"], "summary": "SMT-based program verifiers often suffer from the so-called butterfly effect, in which minor modifications to the program source cause significant instabilities in verification times, which in turn may lead to spurious verification failures and a degraded user experience. This paper identifies matching loops (ill-behaved quantifiers causing an SMT solver to repeatedly instantiate a small set of quantified formulas) as a significant contributor to these instabilities, and describes some techniques to detect and prevent them. At their core, the contributed techniques move the trigger selection logic away from the SMT solver and into the high-level verifier: this move allows authors of verifiers to annotate, rewrite, and analyze user-written quantifiers to improve the solver’s performance, using information that is easily available at the source level but would be hard to extract from the heavily encoded terms that the solver works with. The paper demonstrates three core techniques (quantifier splitting, trigger sharing, and matching loop detection) by extending the Dafny verifier with its own trigger selection routine, and demonstrates significant predictability and performance gains on both Dafny’s test suite and large verification efforts using Dafny.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_20"}, {"primary_key": "4064915", "vector": [], "sparse_vector": [], "title": "Liveness of Randomised Parameterised Systems under Arbitrary Schedulers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the problem of verifying liveness for systems with a finite, but unbounded, number of processes, commonly known asparameterised systems. Typical examples of such systems include distributed protocols (e.g. for the dining philosopher problem). Unlike the case of verifying safety, proving liveness is still considered extremely challenging, especially in the presence of randomness in the system. In this paper we consider liveness under arbitrary (including unfair) schedulers, which is often considered a desirable property in the literature of self-stabilising systems. We introduce an automatic method of proving liveness for randomised parameterised systems under arbitrary schedulers. Viewing liveness as a two-player reachability game (between Scheduler and Process), our method is a CEGAR approach that synthesises a progress relation for Process that can be symbolically represented as a finite-state automaton. The method is incremental and exploits both Angluin-style L*-learning and SAT-solvers. Our experiments show that our algorithm is able to prove liveness automatically for well-known randomised distributed protocols, including Lehmann-Rabin Randomised Dining Philosopher Protocol and randomised self-stabilising protocols (such as the Israeli-Jalfon Protocol). To the best of our knowledge, this is the first fully-automatic method that can prove liveness for randomised protocols.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_7"}, {"primary_key": "4064916", "vector": [], "sparse_vector": [], "title": "From Shape Analysis to Termination Analysis in Linear Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a novel algorithm to conservatively check whether a (recursive) heap-manipulating program terminates. Our algorithm can be used as a post-processing phase of any shape analysis satisfying some natural properties. The running time of the post-processing phase is linear in the size of the output of the chosen shape analysis. The main idea is to partition the (unbounded but finite) set of allocated objects in every state into a bounded set of regions, and track the flow of objects between heap regions in every step of the program. The algorithm proves the existence of the well-founded relation over states by showing that in every loop iteration at least one object (which was allocated before entering the loop) moves to a strictly lower-ranked heap region. The partitioning of objects into regions, the flow of objects between regions, and the ranks of regions are computed automatically from the output of the underlying shape analysis. Our algorithm extends the state of the art in terms of complexity, the class of supported data structures, and its generality. We successfully applied a prototype of our analysis to prove termination of a suite of benchmarks from existing literature, including (looping, recursive, and concurrent) list manipulating programs, looping list-sorting programs, and looping programs that manipulate trees and graphs. The overhead of the termination phase in our experiments is at most 14 % of the overall analysis time.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_23"}, {"primary_key": "4064917", "vector": [], "sparse_vector": [], "title": "PSCV: A Runtime Verification Tool for Probabilistic SystemC Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper describes PSCV, a runtime verification tool for a class of SystemC models which have inherent probabilistic characteristics. The properties of interest are expressed using bounded linear temporal logic. The various features of the tool including automatic monitor generation for producing execution traces of the model-under-verification, mechanism for automatically instrumenting the model, and the interaction with statistical model checker are presented.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_5"}, {"primary_key": "4064918", "vector": [], "sparse_vector": [], "title": "Precise and Complete Propagation Based Local Search for Satisfiability Modulo Theories.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Satisfiability Modulo Theories (SMT) is essential for many applications in computer-aided verification. A recent SMT solving approach based on stochastic local search for the theory of quantifier-free fixed-size bit-vectors proved to be quite effective on hard satisfiable instances, particularly in the context of symbolic execution. However, it still relies on brute-force randomization and restarts to achieve completeness. In this paper we simplify, extend, and formalize the propagation-based variant of this approach. We introduce a notion of essential inputs to lift the well-known concept of controlling inputs from the bit-level to the word-level, which allows to prune search. Guided by a formal completeness proof for our propagation-based variant we obtain a clean, simple and more precise algorithm, which yields a substantial gain in performance, as shown in our experimental evaluation.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_11"}, {"primary_key": "4064919", "vector": [], "sparse_vector": [], "title": "Investigating Safety of a Radiotherapy Machine Using System Models with Pluggable Checkers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Formal techniques for guaranteeing software correctness have made tremendous progress in recent decades. However, applying these techniques to real-world safety-critical systems remains challenging in practice. Inspired by goals set out in prior work, we report on a large-scale case study that applies modern verification techniques to check safety properties of a radiotherapy system in current clinical use. Because of the diversity and complexity of the system’s components (software, hardware, and physical), no single tool was suitable for both checking critical component properties and ensuring that their composition implies critical system properties. This paper describes how we used state-of-the-art approaches to develop specialized tools for verifying safety properties of individual components, as well as an extensible tool for composing those properties to check the safety of the system as a whole. We describe the key design decisions that diverged from previous approaches and that enabled us to practically apply our approach to provide machine-checked guarantees. Our case study uncovered subtle safety-critical flaws in a pre-release of the latest version of the radiotherapy system’s control software.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_2"}, {"primary_key": "4064920", "vector": [], "sparse_vector": [], "title": "Counterexample Guided Abstraction Refinement for Stability Analysis.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present a counterexample guided abstraction refinement (Cegar) algorithm for stability analysis of polyhedral hybrid systems. Our results build upon a quantitative predicate abstraction and model-checking algorithm for stability analysis, which returns a counterexample indicating a potential reason for instability. The main contributions of this paper include the validation of the counterexample and refinement of the abstraction based on the analysis of the counterexample. The counterexample returned by the quantitative predicate abstraction analysis is a cycle such that the product of the weights on its edges is greater than 1. Validation involves checking if there exists an infinite diverging execution which follows the cycle infinitely many times. Unlike in the case ofCegarfor safety, the validation problem is not a bounded model-checking problem. Using novel insights, we present a simple characterization for the existence of an infinite diverging execution in terms of the satisfaction of a first order logic formula which can be efficiently solved. Similarly, the refinement is more involved, since, there is a priori no bound on the number of predecessor computation steps that need to be performed to invalidate the abstract counterexample. We present strategies for refinement based on the insights from the validation step. We have implemented the validation and refinement algorithms and use the stability verification toolAveristin the back end for performing the abstraction and model-checking. We compare theCegaralgorithm withAveristand report experimental results demonstrating the benefits of counterexample guided refinement.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_27"}, {"primary_key": "4064921", "vector": [], "sparse_vector": [], "title": "End-to-End Verification of Processors with ISA-Formal.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Despite 20+ years of research on processor verification, it remains hard to use formal verification techniques in commercial processor development. There are two significant factors: scaling issues and return on investment. Thescaling issuesinclude the size of modern processor specifications, the size/complexity of processor designs, the size of design/verification teams and the (non)availability of enough formal verification experts. Thereturn on investmentissues include the need to start catching bugs early in development, the need to continue catching bugs throughout development, and the need to be able to reuse verification IP, tools and techniques across a wide range of design styles. This paper describes how ARM has overcome these issues in our Instruction Set Architecture Formal Verification framework “ISA-Formal.” This is an end-to-end framework to detect bugs in the datapath, pipeline control and forwarding/stall logic of processors. A key part of making the approach scale is use of a mechanical translation of ARM’s Architecture Reference Manuals to Verilog allowing the use of commercial model-checkers. ISA-Formal has proven especially effective at finding micro-architecture specific bugs involving complex sequences of instructions. An essential feature of our work is that it is able to scale all the way from simple 3-stage microcontrollers, through superscalar in-order processors up to out-of-order processors. We have applied this method to 8 different ARM processors spanning all stages of development up to release. In all processors, this has found bugs that would have been hard for conventional simulation-based verification to find and ISA-Formal is now a key part of ARM’s formal verification strategy. To the best of our knowledge, this is the most broadly applicable formal verification technique for verifying processor pipeline control in mainstream commercial use.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_3"}, {"primary_key": "4064923", "vector": [], "sparse_vector": [], "title": "Probabilistic Automated Language Learning for Configuration Files.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Ruzica Piskac"], "summary": "Software failures resulting from configuration errors have become commonplace as modern software systems grow increasingly large and more complex. The lack of language constructs in configuration files, such as types and grammars, has directed the focus of a configuration file verification towards building post-failure error diagnosis tools. In addition, the existing tools are generally language specific, requiring the user to define at least a grammar for the language models and explicit rules to check. In this paper, we propose a framework which analyzes datasets of correct configuration files and derives rules for building a language model from the given dataset. The resulting language model can be used to verify new configuration files and detect errors in them. Our proposed framework is highly modular, does not rely on the system source code, and can be applied to any new configuration file type with minimal user input. Our tool, named  ConfigC, relies on an abstract representation of language rules to allow for this modularity. ConfigC supports learning of various rules, such as orderings, value relations, type errors, or user defined rules by using a probabilistic type inference strategy and defining a small interface for the rule type.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_5"}, {"primary_key": "4064925", "vector": [], "sparse_vector": [], "title": "BigraphER: Rewriting and Analysis Engine for Bigraphs.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "BigraphERis a suite of open-source tools providing an efficient implementation of rewriting, simulation, and visualisation for bigraphs, a universal formalism for modelling interacting systems that evolve in time and space and first introduced by <PERSON><PERSON>.BigraphERconsists of an OCaml library that provides programming interfaces for the manipulation of bigraphs, their constituents and reaction rules, and a command-line tool capable of simulating Bigraphical Reactive Systems (BRSs) and computing their transition systems. Other features are native support for both bigraphs and bigraphs with sharing, stochastic reaction rules, rule priorities, instantiation maps, parameterised controls, predicate checking, graphical output and integration with the probabilistic model checker PRISM.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_27"}, {"primary_key": "4064926", "vector": [], "sparse_vector": [], "title": "Limit-Deterministic Büchi Automata for Linear Temporal Logic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Limit-deterministic Büchi automata can replace deterministic Rabin automata in probabilistic model checking algorithms, and can be significantly smaller. We present a direct construction from an LTL formula\\(\\varphi \\)to a limit-deterministic Büchi automaton. The automaton is the combination of a non-deterministic component, guessing the set of eventually true\\({\\mathbf {G}}\\)-subformulas of\\(\\varphi \\), and a deterministic component verifying this guess and using this information to decide on acceptance. Contrary to the indirect approach of constructing a non-deterministic automaton for\\(\\varphi \\)and then applying a semi-determinisation algorithm, our translation is compositional and has a clear logical structure. Moreover, due to its special structure, the resulting automaton can be used not only for qualitative, but also for quantitative verification of MDPs, usingthe samemodel checking algorithm as for deterministic automata. This allows one to reuse existing efficient implementations of this algorithm without any modification. Our construction yields much smaller automata for formulas with deep nesting of modal operators and performs at least as well as the existing approaches on general formulas.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_17"}, {"primary_key": "4064927", "vector": [], "sparse_vector": [], "title": "Progressive Reasoning over Recursively-Defined Strings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of reasoning over an expressive constraint language for unbounded strings. The difficulty comes from “recursively defined” functions such asreplace, making state-of-the-art algorithmsnon-terminating. Our first contribution is a progressive search algorithm to not onlymitigatethe problem of non-terminating reasoning but alsoguidethe search towards a “minimal solution” when the input formula is in fact satisfiable. We have implemented our method using the state-of-the-art Z3 framework. Importantly, we have enabled conflict clause learning for string theory so that our solver can be used effectively in the setting of program verification. Finally, our experimental evaluation shows leadership in a large benchmark suite, and a first deployment for another benchmark suite which requires reasoning about string formulas of a class that has not been solved before.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_12"}, {"primary_key": "4064928", "vector": [], "sparse_vector": [], "title": "String Analysis via Automata Manipulation with Logic Circuit Representation.", "authors": ["<PERSON><PERSON><PERSON>", "Tzung<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many severe security vulnerabilities in web applications can be attributed to string manipulation mistakes, which can often be avoided through formal string analysis. String analysis tools are indispensable and under active development. Prior string analysis methods are primarily automata-based or satisfiability-based. The two approaches exhibit distinct strengths and weaknesses. Specifically, existing automata-based methods have difficulty in generating counterexamples at system inputs to witness vulnerability, whereas satisfiability-based methods are inadequate to produce filters amenable for firmware or hardware implementation for real-time screening of malicious inputs to a system under protection. In this paper, we propose a new string analysis method based on a scalable logic circuit representation for (nondeterministic) finite automata to support various string and automata manipulation operations. It enables both counterexample generation and filter synthesis in string constraint solving. By using the new data structure, automata with large state spaces and/or alphabet sizes can be efficiently represented. Empirical studies on a large set of open source web applications and well-known attack patterns demonstrate the unique benefits of our method compared to prior string analysis tools.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_13"}, {"primary_key": "4064929", "vector": [], "sparse_vector": [], "title": "BFS-Based Model Checking of Linear-Time Properties with an Application on GPUs.", "authors": ["<PERSON>"], "summary": "Efficient algorithms have been developed to model check liveness properties, such as the well-known Nested Depth-First Search, which uses a depth-first search (DFS) strategy. However, in some settings, DFS is not a suitable option. For instance, when considering distributed model checking on a cluster, or many-core model checking using a Graphics Processing Unit (GPU), Breadth-First Search (BFS) is a more natural choice, at least for basic reachability analysis. Liveness property checking, however, requires the detection of (accepting) cycles, and BFS is not very suitable to detect these on-the-fly. In this paper, we consider how a model checker that completely runs on a GPU can be extended to efficiently verify whether finite-state concurrent systems satisfy liveness properties. We exploit the fact that the state space is the product of the behaviour of several parallel automata. The result of this work is the very first GPU-based model checker that can check liveness properties.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_26"}, {"primary_key": "4064930", "vector": [], "sparse_vector": [], "title": "A Practical Verification Framework for Preemptive OS Kernels.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We propose a practical verification framework for preemptive OS kernels. The framework models the correctness of API implementations in OS kernels as contextual refinement of their abstract specifications. It provides a specification language for defining the high-level abstract model of OS kernels, a program logic for refinement verification of concurrent kernel code with multi-level hardware interrupts, and automated tactics for developing mechanized proofs. The whole framework is developed for a practical subset of the C language. We have successfully applied it to verify key modules of a commercial preemptive OS\\(\\mu \\text {C/OS-II}\\)[2], including the scheduler, interrupt handlers, message queues, and mutexesetc.We also verify thepriority-inversion-freedom (PIF) in\\(\\mu \\text {C/OS-II}\\). All the proofs are mechanized in Coq. To our knowledge, our work is the first to verify the functional correctness of a practicalpreemptiveOS kernel with machine-checkable proofs.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41540-6_4"}, {"primary_key": "4064931", "vector": [], "sparse_vector": [], "title": "Under-Approximating Backward Reachable Sets by Polytopes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Under-approximations are useful for falsification of safety properties for nonlinear (hybrid) systems by finding counter-examples. Polytopic under-approximations enable analysis of these properties using reasoning in the theory of linear arithmetic. Given a nonlinear system, a target region of the simply connected compact type and a time duration, we in this paper propose a method using boundary analysis to compute an under-approximation of the backward reachable set. The under-approximation is represented as a polytope. The polytope can be computed by solving linear program problems. We test our method on several examples and compare them with existing methods. The results show that our method is highly promising in under-approximating reachable sets. Furthermore, we explore some directions to improve the scalability of our method.", "published": "2016-01-01", "category": "cav", "pdf_url": "", "sub_summary": "", "source": "cav", "doi": "10.1007/978-3-319-41528-4_25"}]