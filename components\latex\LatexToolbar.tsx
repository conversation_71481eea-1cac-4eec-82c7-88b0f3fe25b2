import { Button } from "@/components/ui/button";
import { BetterTooltip } from "@/components/ui/tooltip";
import katex from "katex";

interface LatexToolbarProps {
  onInsert: (formula: string) => void;
}

const FORMULA_GROUPS = [
  {
    group: "常用符号",
    items: [
      { label: "分数", formula: "\\frac{x}{y}", desc: "分数：x/y" },
      { label: "平方根", formula: "\\sqrt{x}", desc: "平方根" },
      { label: "n次方根", formula: "\\sqrt[n]{x}", desc: "n次方根" },
      { label: "指数", formula: "e^{x}", desc: "指数/幂" },
      { label: "下标", formula: "x_{i}", desc: "下标" },
      { label: "极限", formula: "\\lim_{n \\to \\infty}", desc: "极限" },
      { label: "积分", formula: "\\int_{a}^{b}", desc: "定积分" },
      { label: "双重积分", formula: "\\iint_{D}", desc: "双重积分" },
      { label: "三重积分", formula: "\\iiint_{E}", desc: "三重积分" },
      { label: "曲线积分", formula: "\\oint_{C}", desc: "曲线积分" },
      { label: "求和", formula: "\\sum_{i=0}^{n}", desc: "求和符号" },
      { label: "求积", formula: "\\prod_{i=1}^{n}", desc: "求积符号" },
      { label: "矩阵", formula: "\\begin{bmatrix} a & b \\\\ c & d \\end{bmatrix}", desc: "2x2矩阵" },
      { label: "三角函数", formula: "\\sin \\theta", desc: "正弦函数" },
      { label: "括号", formula: "\\left( x \\right)", desc: "自动伸缩括号" },
    ]
  },
  {
    group: "希腊字母",
    items: [
      { label: "α", formula: "\\alpha", desc: "alpha" },
      { label: "β", formula: "\\beta", desc: "beta" },
      { label: "γ", formula: "\\gamma", desc: "gamma" },
      { label: "Γ", formula: "\\Gamma", desc: "大写 Gamma" },
      { label: "δ", formula: "\\delta", desc: "delta" },
      { label: "Δ", formula: "\\Delta", desc: "大写 Delta" },
      { label: "ε", formula: "\\epsilon", desc: "epsilon" },
      { label: "ζ", formula: "\\zeta", desc: "zeta" },
      { label: "η", formula: "\\eta", desc: "eta" },
      { label: "θ", formula: "\\theta", desc: "theta" },
      { label: "Θ", formula: "\\Theta", desc: "大写 Theta" },
      { label: "ι", formula: "\\iota", desc: "iota" },
      { label: "κ", formula: "\\kappa", desc: "kappa" },
      { label: "λ", formula: "\\lambda", desc: "lambda" },
      { label: "Λ", formula: "\\Lambda", desc: "大写 Lambda" },
      { label: "μ", formula: "\\mu", desc: "mu" },
      { label: "ν", formula: "\\nu", desc: "nu" },
      { label: "ξ", formula: "\\xi", desc: "xi" },
      { label: "Ξ", formula: "\\Xi", desc: "大写 Xi" },
      { label: "π", formula: "\\pi", desc: "pi" },
      { label: "Π", formula: "\\Pi", desc: "大写 Pi" },
      { label: "ρ", formula: "\\rho", desc: "rho" },
      { label: "σ", formula: "\\sigma", desc: "sigma" },
      { label: "Σ", formula: "\\Sigma", desc: "大写 Sigma" },
      { label: "τ", formula: "\\tau", desc: "tau" },
      { label: "υ", formula: "\\upsilon", desc: "upsilon" },
      { label: "φ", formula: "\\phi", desc: "phi" },
      { label: "Φ", formula: "\\Phi", desc: "大写 Phi" },
      { label: "χ", formula: "\\chi", desc: "chi" },
      { label: "ψ", formula: "\\psi", desc: "psi" },
      { label: "Ψ", formula: "\\Psi", desc: "大写 Psi" },
      { label: "ω", formula: "\\omega", desc: "omega" },
      { label: "Ω", formula: "\\Omega", desc: "大写 Omega" },
    ]
  },
  {
    group: "二元运算符",
    items: [
      { label: "+", formula: "+", desc: "加号" },
      { label: "-", formula: "-", desc: "减号" },
      { label: "×", formula: "\\times", desc: "乘号" },
      { label: "÷", formula: "\\div", desc: "除号" },
      { label: "·", formula: "\\cdot", desc: "点乘" },
      { label: "*", formula: "*", desc: "星号" },
      { label: "∪", formula: "\\cup", desc: "并集" },
      { label: "∩", formula: "\\cap", desc: "交集" },
      { label: "⊕", formula: "\\oplus", desc: "直和/异或" },
      { label: "⊗", formula: "\\otimes", desc: "张量积/圆乘" },
      { label: "⊙", formula: "\\odot", desc: "点积/圆" },
      { label: "⊞", formula: "\\boxplus", desc: "boxplus" },
      { label: "⊠", formula: "\\boxtimes", desc: "boxtimes" },
      { label: "±", formula: "\\pm", desc: "加减号" },
      { label: "∓", formula: "\\mp", desc: "减加号" },
      { label: "∧", formula: "\\wedge", desc: "逻辑与" },
      { label: "∨", formula: "\\vee", desc: "逻辑或" },
      { label: "⊎", formula: "\\uplus", desc: "多重并集" },
      { label: "⊓", formula: "\\sqcap", desc: "方交集" },
      { label: "⊔", formula: "\\sqcup", desc: "方并集" },
      { label: "⋆", formula: "\\star", desc: "星形运算" },
      { label: "∘", formula: "\\circ", desc: "复合函数" },
      { label: "•", formula: "\\bullet", desc: "实心点" },
      { label: "∖", formula: "\\setminus", desc: "集合差" },
      { label: "≀", formula: "\\wr", desc: "扭积" },
    ]
  },
  {
    group: "关系符号",
    items: [
      { label: "=", formula: "=", desc: "等于" },
      { label: "≠", formula: "\\neq", desc: "不等于" },
      { label: "<", formula: "<", desc: "小于" },
      { label: ">", formula: ">", desc: "大于" },
      { label: "≤", formula: "\\leq", desc: "小于等于" },
      { label: "≥", formula: "\\geq", desc: "大于等于" },
      { label: "≈", formula: "\\approx", desc: "约等于" },
      { label: "≡", formula: "\\equiv", desc: "全等于" },
      { label: "∝", formula: "\\propto", desc: "正比" },
      { label: "∈", formula: "\\in", desc: "属于" },
      { label: "∉", formula: "\\notin", desc: "不属于" },
      { label: "⊂", formula: "\\subset", desc: "真子集" },
      { label: "⊃", formula: "\\supset", desc: "真超集" },
      { label: "⊆", formula: "\\subseteq", desc: "子集" },
      { label: "⊇", formula: "\\supseteq", desc: "超集" },
      { label: "≪", formula: "\\ll", desc: "远小于" },
      { label: "≫", formula: "\\gg", desc: "远大于" },
      { label: "≅", formula: "\\cong", desc: "全等" },
      { label: "≃", formula: "\\simeq", desc: "相似等于" },
      { label: "≍", formula: "\\asymp", desc: "渐近等于" },
      { label: "≺", formula: "\\prec", desc: "先于" },
      { label: "≻", formula: "\\succ", desc: "后于" },
      { label: "⊢", formula: "\\vdash", desc: "推导" },
      { label: "⊣", formula: "\\dashv", desc: "逆推导" },
      { label: "⊥", formula: "\\perp", desc: "垂直" },
      { label: "∥", formula: "\\parallel", desc: "平行" },
      { label: "∦", formula: "\\nparallel", desc: "不平行" },
      { label: "⋈", formula: "\\bowtie", desc: "自然连接" },
      { label: "≐", formula: "\\doteq", desc: "点等于" },
    ]
  },
  {
    group: "箭头符号",
    items: [
      { label: "→", formula: "\\to", desc: "右箭头" },
      { label: "←", formula: "\\leftarrow", desc: "左箭头" },
      { label: "↑", formula: "\\uparrow", desc: "上箭头" },
      { label: "↓", formula: "\\downarrow", desc: "下箭头" },
      { label: "↔", formula: "\\leftrightarrow", desc: "左右箭头" },
      { label: "⇌", formula: "\\rightleftharpoons", desc: "可逆反应" },
      { label: "⇒", formula: "\\Rightarrow", desc: "推出" },
      { label: "⇔", formula: "\\Leftrightarrow", desc: "等价" },
      { label: "⇑", formula: "\\Uparrow", desc: "双上箭头" },
      { label: "⇓", formula: "\\Downarrow", desc: "双下箭头" },
      { label: "↦", formula: "\\mapsto", desc: "映射到" },
      { label: "⟼", formula: "\\longmapsto", desc: "长映射到" },
      { label: "⟶", formula: "\\longrightarrow", desc: "长右箭头" },
      { label: "⟵", formula: "\\longleftarrow", desc: "长左箭头" },
      { label: "⟷", formula: "\\longleftrightarrow", desc: "长双向箭头" },
      { label: "⟹", formula: "\\Longrightarrow", desc: "长双线右箭头" },
      { label: "⟸", formula: "\\Longleftarrow", desc: "长双线左箭头" },
      { label: "⟺", formula: "\\Longleftrightarrow", desc: "长双线双向箭头" },
      { label: "↪", formula: "\\hookrightarrow", desc: "钩形右箭头" },
      { label: "↩", formula: "\\hookleftarrow", desc: "钩形左箭头" },
      { label: "⇀", formula: "\\rightharpoonup", desc: "右半箭头上" },
      { label: "⇁", formula: "\\rightharpoondown", desc: "右半箭头下" },
      { label: "↼", formula: "\\leftharpoonup", desc: "左半箭头上" },
      { label: "↽", formula: "\\leftharpoondown", desc: "左半箭头下" },
    ]
  },
  {
    group: "微积分与极限",
    items: [
      { label: "∫", formula: "\\int", desc: "积分" },
      { label: "∬", formula: "\\iint", desc: "二重积分" },
      { label: "∭", formula: "\\iiint", desc: "三重积分" },
      { label: "∮", formula: "\\oint", desc: "闭合曲线积分" },
      { label: "∯", formula: "\\oiint", desc: "闭合曲面积分" },
      { label: "∰", formula: "\\oiiint", desc: "闭合体积积分" },
      { label: "∇", formula: "\\nabla", desc: "梯度算子" },
      { label: "∂", formula: "\\partial", desc: "偏导数" },
      { label: "′", formula: "^\\prime", desc: "导数符号" },
      { label: "∆", formula: "\\Delta", desc: "拉普拉斯算子" },
      { label: "∑", formula: "\\sum", desc: "求和" },
      { label: "∏", formula: "\\prod", desc: "求积" },
      { label: "∐", formula: "\\coprod", desc: "余积" },
      { label: "lim", formula: "\\lim", desc: "极限" },
      { label: "sup", formula: "\\sup", desc: "上确界" },
      { label: "inf", formula: "\\inf", desc: "下确界" },
      { label: "max", formula: "\\max", desc: "最大值" },
      { label: "min", formula: "\\min", desc: "最小值" },
    ]
  },
  {
    group: "集合符号",
    items: [
      { label: "∅", formula: "\\emptyset", desc: "空集" },
      { label: "∋", formula: "\\ni", desc: "包含成员" },
      { label: "⊈", formula: "\\nsubseteq", desc: "非子集" },
      { label: "⊉", formula: "\\nsupseteq", desc: "非超集" },
      { label: "∁", formula: "\\complement", desc: "补集" },
      { label: "P", formula: "\\mathcal{P}", desc: "幂集" },
      { label: "∀", formula: "\\forall", desc: "任意/全称量词" },
      { label: "∃", formula: "\\exists", desc: "存在/存在量词" },
      { label: "∄", formula: "\\nexists", desc: "不存在" },
    ]
  },
  {
    group: "逻辑符号",
    items: [
      { label: "¬", formula: "\\neg", desc: "否定" },
      { label: "⊤", formula: "\\top", desc: "恒真" },
      { label: "⊥", formula: "\\bot", desc: "恒假" },
      { label: "⊨", formula: "\\models", desc: "语义蕴含" },
      { label: "□", formula: "\\Box", desc: "必然性" },
      { label: "◊", formula: "\\Diamond", desc: "可能性" },
      { label: "∴", formula: "\\therefore", desc: "所以" },
      { label: "∵", formula: "\\because", desc: "因为" },
    ]
  },
  {
    group: "几何符号",
    items: [
      { label: "∠", formula: "\\angle", desc: "角" },
      { label: "∟", formula: "\\measuredangle", desc: "直角" },
      { label: "°", formula: "^\\circ", desc: "度" },
      { label: "△", formula: "\\triangle", desc: "三角形" },
      { label: "□", formula: "\\square", desc: "正方形" },
    ]
  },
  {
    group: "特殊符号",
    items: [
      { label: "∞", formula: "\\infty", desc: "无穷" },
      { label: "ℵ", formula: "\\aleph", desc: "阿列夫数" },
      { label: "ℏ", formula: "\\hbar", desc: "普朗克常数" },
      { label: "ℓ", formula: "\\ell", desc: "小写字母l" },
      { label: "℘", formula: "\\wp", desc: "威尔斯特拉斯p" },
      { label: "ℜ", formula: "\\Re", desc: "实部" },
      { label: "ℑ", formula: "\\Im", desc: "虚部" },
      { label: "℧", formula: "\\mho", desc: "姆欧" },
      { label: "ð", formula: "\\eth", desc: "eth" },
      { label: "…", formula: "\\ldots", desc: "底部省略号" },
      { label: "⋯", formula: "\\cdots", desc: "中部省略号" },
      { label: "⋮", formula: "\\vdots", desc: "垂直省略号" },
      { label: "⋱", formula: "\\ddots", desc: "对角省略号" },
      { label: "♮", formula: "\\natural", desc: "自然符号" },
      { label: "♯", formula: "\\sharp", desc: "升号" },
      { label: "♭", formula: "\\flat", desc: "降号" },
      { label: "†", formula: "\\dagger", desc: "剑号" },
      { label: "‡", formula: "\\ddagger", desc: "双剑号" },
      { label: "§", formula: "\\S", desc: "章节符号" },
      { label: "¶", formula: "\\P", desc: "段落符号" },
      { label: "©", formula: "\\copyright", desc: "版权符号" },
      { label: "®", formula: "\\circledR", desc: "注册商标" },
    ]
  },
  {
    group: "括号与矩阵",
    items: [
      { label: "矩阵", formula: "\\begin{matrix} a & b \\\\ c & d \\end{matrix}", desc: "无边框矩阵" },
      { label: "小括号矩阵", formula: "\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}", desc: "小括号矩阵" },
      { label: "中括号矩阵", formula: "\\begin{bmatrix} a & b \\\\ c & d \\end{bmatrix}", desc: "中括号矩阵" },
      { label: "大括号矩阵", formula: "\\begin{Bmatrix} a & b \\\\ c & d \\end{Bmatrix}", desc: "大括号矩阵" },
      { label: "行列式", formula: "\\begin{vmatrix} a & b \\\\ c & d \\end{vmatrix}", desc: "行列式" },
      { label: "矩阵范数", formula: "\\begin{Vmatrix} a & b \\\\ c & d \\end{Vmatrix}", desc: "范数" },
      { label: "左大括号矩阵", formula: "\\left\\{\\begin{matrix} x & x>0 \\\\ -x & x\\leq 0\\end{matrix}\\right.", desc: "分段函数（左大括号）" },
      { label: "右大括号矩阵", formula: "\\left.\\begin{matrix} x & x>0 \\\\ -x & x\\leq 0\\end{matrix}\\right\\}", desc: "分段函数（右大括号）" },
      { label: "小括号", formula: "\\left( x \\right)", desc: "小括号" },
      { label: "中括号", formula: "\\left[ x \\right]", desc: "中括号" },
      { label: "大括号", formula: "\\left\\{ x \\right\\}", desc: "大括号" },
      { label: "尖括号", formula: "\\left\\langle x \\right\\rangle", desc: "尖括号" },
      { label: "向上取整", formula: "\\left\\lceil x \\right\\rceil", desc: "向上取整" },
      { label: "向下取整", formula: "\\left\\lfloor x \\right\\rfloor", desc: "向下取整" },
      { label: "绝对值", formula: "\\left| x \\right|", desc: "绝对值" },
      { label: "向量范数", formula: "\\left\\| x \\right\\|", desc: "范数" },
    ]
  },
  {
    group: "常用函数",
    items: [
      { label: "sin", formula: "\\sin", desc: "正弦" },
      { label: "cos", formula: "\\cos", desc: "余弦" },
      { label: "tan", formula: "\\tan", desc: "正切" },
      { label: "cot", formula: "\\cot", desc: "余切" },
      { label: "sec", formula: "\\sec", desc: "正割" },
      { label: "csc", formula: "\\csc", desc: "余割" },
      { label: "arcsin", formula: "\\arcsin", desc: "反正弦" },
      { label: "arccos", formula: "\\arccos", desc: "反余弦" },
      { label: "arctan", formula: "\\arctan", desc: "反正切" },
      { label: "sinh", formula: "\\sinh", desc: "双曲正弦" },
      { label: "cosh", formula: "\\cosh", desc: "双曲余弦" },
      { label: "tanh", formula: "\\tanh", desc: "双曲正切" },
      { label: "log", formula: "\\log", desc: "对数" },
      { label: "ln", formula: "\\ln", desc: "自然对数" },
      { label: "lg", formula: "\\lg", desc: "常用对数" },
      { label: "exp", formula: "\\exp", desc: "指数函数" },
      { label: "det", formula: "\\det", desc: "行列式" },
      { label: "ker", formula: "\\ker", desc: "核" },
      { label: "dim", formula: "\\dim", desc: "维数" },
      { label: "deg", formula: "\\deg", desc: "度数" },
      { label: "arg", formula: "\\arg", desc: "辐角" },
      { label: "gcd", formula: "\\gcd", desc: "最大公约数" },
    ]
  },
];

export function LatexToolbar({ onInsert }: LatexToolbarProps) {
  // 计算每组项目的最佳行数和每行项目数
  const getOptimalLayout = (itemCount: number) => {
    if (itemCount <= 10) return { rows: 1, itemsPerRow: itemCount };
    
    // 寻找最佳的行数，使每行项目数在6-10之间
    let bestRows = 2;
    let bestItemsPerRow = Math.ceil(itemCount / bestRows);
    
    // 尝试2到Math.floor(itemCount/6)行
    for (let rows = 2; rows <= Math.floor(itemCount / 6); rows++) {
      const itemsPerRow = Math.ceil(itemCount / rows);
      
      // 如果每行项目数在6-10之间，并且比当前最佳方案更接近平均分布
      if (itemsPerRow <= 10 && itemsPerRow >= 6) {
        // 计算当前方案的最后一行项目数
        const lastRowItems = itemCount - (rows - 1) * itemsPerRow;
        // 如果最后一行项目数大于等于6或者是最后一个项目，则更新最佳方案
        if (lastRowItems >= 6 || lastRowItems === itemCount % itemsPerRow) {
          bestRows = rows;
          bestItemsPerRow = itemsPerRow;
        }
      }
    }
    
    return { rows: bestRows, itemsPerRow: bestItemsPerRow };
  };

  return (
    <div className="flex flex-row gap-2 p-2 border rounded-lg bg-white">
      {FORMULA_GROUPS.map(group => (
        <BetterTooltip
          key={group.group}
          content={
            <div className="flex flex-col gap-1 min-w-[320px]">
              <div className="font-semibold text-xs text-gray-500 mb-1">{group.group}</div>
              <div className="flex flex-col gap-2">
                {(() => {
                  const { rows, itemsPerRow } = getOptimalLayout(group.items.length);
                  
                  // 创建行数组
                  const rowsArray = [];
                  for (let i = 0; i < rows; i++) {
                    // 计算当前行的起始和结束索引
                    const startIdx = i * itemsPerRow;
                    const endIdx = Math.min(startIdx + itemsPerRow, group.items.length);
                    
                    // 将当前行的项目添加到行数组
                    rowsArray.push(
                      <div key={i} className="flex flex-wrap gap-2">
                        {group.items.slice(startIdx, endIdx).map(item => (
                          <button
                            key={item.label + Math.random()}
                            type="button"
                            className="border rounded-md bg-gray-50 hover:bg-blue-50 px-2 py-1 min-w-[36px] min-h-[36px] flex flex-col items-center justify-center shadow-sm"
                            onClick={() => onInsert(item.formula)}
                            tabIndex={0}
                          >
                            <span
                              dangerouslySetInnerHTML={{ __html: katex.renderToString(item.formula, { displayMode: false, throwOnError: false }) }}
                            />
                            {/* <span className="text-[10px] text-gray-400 mt-1">{item.desc}</span> */}
                          </button>
                        ))}
                      </div>
                    );
                  }
                  
                  return rowsArray;
                })()}
              </div>
            </div>
          }
          side="bottom"
          align="center"
        >
          <button
            type="button"
            className="border rounded-md bg-gray-50 hover:bg-blue-50 px-3 py-2 min-w-[48px] min-h-[48px] flex flex-col items-center justify-center shadow-sm"
            tabIndex={0}
          >
            <span
              dangerouslySetInnerHTML={{ __html: katex.renderToString(group.items[0].formula, { displayMode: false, throwOnError: false }) }}
            />
            <span className="text-xs text-gray-500 mt-1">{group.group}</span>
          </button>
        </BetterTooltip>
      ))}
    </div>
  );
}