[{"primary_key": "2561471", "vector": [], "sparse_vector": [], "title": "A Key-Recovery Timing Attack on Post-quantum Primitives Using the Fujisaki-Okamoto Transformation and Its Application on FrodoKEM.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the implementation of post-quantum primitives, it is well known that all computations that handle secret information need to be implemented to run in constant time. Using the Fujisaki-Okamoto transformation or any of its different variants, a CPA-secure primitive can be converted into an IND-CCA secure KEM. In this paper we show that although the transformation does not handle secret information apart from calls to the CPA-secure primitive, it has to be implemented in constant time. Namely, if the ciphertext comparison step in the transformation is leaking side-channel information, we can launch a key-recovery attack. Several proposed schemes in round 2 of the NIST post-quantum standardization project are susceptible to the proposed attack and we develop and show the details of the attack on one of them, being FrodoKEM. It is implemented on the reference implementation of FrodoKEM, which is claimed to be secure against all timing attacks. Experiments show that the attack code is able to extract the secret key for all security levels using about\\(2^{30}\\)decapsulation calls.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_13"}, {"primary_key": "2561472", "vector": [], "sparse_vector": [], "title": "Improved Primitives for MPC over Mixed Arithmetic-Binary Circuits.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This work introduces novel techniques to improve the translation between arithmetic and binary data types in secure multi-party computation. We introduce a new approach to performing these conversions using what we callextended doubly-authenticated bits(edaBits), which correspond to shared integers in the arithmetic domain whose bit decomposition is shared in the binary domain. These can be used to considerably increase the efficiency of non-linear operations such as truncation, secure comparison and bit-decomposition. Our edaBits are similar to thedaBitstechnique introduced by <PERSON><PERSON><PERSON> et al. (Indocrypt 2019). However, we show that edaBits can be directly produced much more efficiently than daBits, with active security, while enabling the same benefits in higher-level applications. Our method for generating edaBits involves a novel cut-and-choose technique that may be of independent interest, and improves efficiency by exploiting natural, tamper-resilient properties of binary circuits that occur in our construction. We also show how edaBits can be applied to efficiently implement various non-linear protocols of interest, and we thoroughly analyze their correctness for both signed and unsigned integers. The results of this work can be applied to any corruption threshold, although they seem best suited to dishonest majority protocols such as SPDZ. We implement and benchmark our constructions, and experimentally verify that our technique yields a substantial increase in efficiency. EdaBits save in communication by a factor that lies between 2 and 60 for secure comparisons with respect to a purely arithmetic approach, and between 2 and 25 with respect to using daBits. Improvements in throughput per second slightly lower but still as high as a factor of 47. We also apply our novel machinery to the tasks of biometric matching and convolutional neural networks, obtaining a noticeable improvement as well.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_29"}, {"primary_key": "2561473", "vector": [], "sparse_vector": [], "title": "Overcoming Impossibility Results in Composable Security Using Interval-Wise Guarantees.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Composable security definitions, at times called simulation-based definitions, provide strong security guarantees that hold in any context. However, they are also met with some skepticism due to many impossibility results; goals such as commitments and zero-knowledge that are achievable in a stand-alone sense were shown to be unachievable composably (without a setup) since provably no efficient simulator exists. In particular, in the context of adaptive security, the so-called “simulator commitment problem” arises: once a party gets corrupted, an efficient simulator is unable to be consistent with its pre-corruption outputs. A natural question is whether such impossibility results are unavoidable or only artifacts of frameworks being too restrictive. In this work, we propose a novel type of composable security statement that evades the commitment problem. Our new type is able to express the composable guarantees of schemes that previously did not have a clear composable understanding. To this end, we leverage the concept of system specifications in the Constructive Cryptography framework, capturing the conjunction of several interval-wise guarantees, each specifying the guarantees between two events. We develop the required theory and present the corresponding new composition theorem. We present three applications of our theory. First, we show in the context of symmetric encryption with adaptive corruption how our notion naturally captures the expected confidentiality guarantee—the messages remain confidential until either party gets corrupted—and that it can be achieved by any standard semantically secure scheme (negating the need for non-committing encryption). Second, we present a composable formalization of (so far only known to be standalone secure) commitment protocols, which is instantiable without a trusted setup like a CRS. We show it to be sufficient for being used in coin tossing over the telephone, one of the early intuitive applications of commitments. Third, we reexamine a result by Hofheinz, Matt, and Maurer [Asiacrypt’15] implying that IND-ID-CPA security is not the right notion for identity-based encryption, unmasking this claim as an unnecessary framework artifact.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_2"}, {"primary_key": "2561474", "vector": [], "sparse_vector": [], "title": "Interactive Proofs for Social Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "E<PERSON>"], "summary": "We consider interactive proofs for social graphs, where the verifier has only oracle access to the graph and can query for the\\(i^{th}\\)neighbor of a vertexv, giveniandv. In this model, we construct a doubly-efficient public-coin two-message interactive protocol for estimating the size of the graph to within a multiplicative factor\\(\\varepsilon >0\\). The verifier performs\\(\\widetilde{O}(1/\\varepsilon ^2 \\cdot \\tau _{mix}\\cdot \\varDelta )\\)queries to the graph, where\\(\\tau _{mix}\\)is the mixing time of the graph and\\(\\varDelta \\)is the average degree of the graph. The prover runs in quasi-linear time in the number of nodes in the graph. Furthermore, we develop a framework for computing the quantiles of essentially any (reasonable) functionfof vertices/edges of the graph. Using this framework, we can estimate many health measures of social graphs such as the clustering coefficients and the average degree, where the verifier performs only a small number of queries to the graph. Using the Fiat-Shamir paradigm, we are able to transform the above protocols to a non-interactive argument in the random oracle model. The result is that social media companies (e.g., Facebook, Twitter, etc.) can publish, once and for all, a short proof for the size or health of their social network. This proof can be publicly verified by any single user using a small number of queries to the graph.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_20"}, {"primary_key": "2561475", "vector": [], "sparse_vector": [], "title": "Universally Composable Relaxed Password Authenticated Key Exchange.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Protocols forpassword authenticated key exchange(PAKE) allow two parties who share only a weak password to agree on a cryptographic key. We revisit the notion of PAKE in the universal composability (UC) framework, and propose a relaxation of the PAKE functionality of <PERSON><PERSON> et al. that we calllazy-extraction PAKE(lePAKE). Our relaxation allows the ideal-world adversary topostponeits password guess until after a session is complete. We argue that this relaxed notion still provides meaningful security in the password-only setting. As our main result, we show that several PAKE protocols that were previously only proven secure with respect to a “game-based” definition of security can be shown to UC-realize the lePAKE functionality in the random-oracle model. These include SPEKE, SPAKE2, and TBPEKE, the most efficient PAKE schemes currently known.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_10"}, {"primary_key": "2561476", "vector": [], "sparse_vector": [], "title": "Functional Encryption for Attribute-Weighted Sums from k-Lin.", "authors": ["<PERSON>", "Junqing Gong", "<PERSON><PERSON><PERSON>e"], "summary": "We present functional encryption schemes for attribute-weighted sums, where encryption takes as inputNattribute-value pairs\\((x_i,z_i)\\)where\\(x_i\\)is public and\\(z_i\\)is private; secret keys are associated with arithmetic branching programsf, and decryption returns the weighted sum\\(\\sum _{i=1}^N f(x_i) z_i\\)while leaking no additional information about the\\(z_i\\)’s. Our main construction achieves (1)compact public parameters and key sizes that are independent ofNand the secret key can decrypt a ciphertext for any a-priori unboundedN; (2)short ciphertexts that grow withNand the size of\\(z_i\\)but not\\(x_i\\); (3)simulation-based security against unbounded collusions; (4)relies on the standardk-linear assumption in prime-order bilinear groups.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_23"}, {"primary_key": "2561477", "vector": [], "sparse_vector": [], "title": "Slide Reduction, Revisited - Filling the Gaps in SVP Approximation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "We show how to generalize <PERSON><PERSON> and <PERSON><PERSON><PERSON>’s slide reduction algorithm [STOC ’08] for solving the approximate Shortest Vector Problem over lattices (SVP) to allow for arbitrary block sizes, rather than just block sizes that divide the ranknof the lattice. This leads to significantly better running times for most approximation factors. We accomplish this by combining slide reduction with the DBKZ algorithm of <PERSON><PERSON><PERSON><PERSON> and <PERSON> [Eurocrypt ’16]. We also show a different algorithm that works when the block size is quite large—at least half the total rank. This yields the first non-trivial algorithm for sublinear approximation factors. Together with some additional optimizations, these results yield significantly faster provably correct algorithms for\\(\\delta \\)-approximate SVP for all approximation factors\\(n^{1/2+\\varepsilon } \\le \\delta \\le n^{O(1)}\\), which is the regime most relevant for cryptography. For the specific values of\\(\\delta = n^{1-\\varepsilon }\\)and\\(\\delta = n^{2-\\varepsilon }\\), we improve the exponent in the running time by a factor of 2 and a factor of 1.5 respectively.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_10"}, {"primary_key": "2561478", "vector": [], "sparse_vector": [], "title": "Time-Space Tradeoffs and Short Collisions in Merkle-Damgård Hash Functions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We study collision-finding against <PERSON><PERSON><PERSON>-<PERSON><PERSON> hashing in the random-oracle model by adversaries with an arbitraryS-bit auxiliary advice input about the random oracle andTqueries. Recent work showed that such adversaries can find collisions (with respect to a random IV) with advantage\\(\\varOmega (ST^2/2^n)\\), wherenis the output length, beating the birthday bound by a factor ofS. These attacks were shown to be optimal. We observe that the collisions produced are very long, on the order ofTblocks, which would limit their practical relevance. We prove several results related to improving these attacks to find shorter collisions. We first exhibit a simple attack for findingB-block-long collisions achieving advantage\\(\\tilde{\\varOmega }(STB/2^n)\\). We then study if this attack is optimal. We show that the prior technique based on the bit-fixing model (used for the\\(ST^2/2^n\\)bound) provably cannot reach this bound, and towards a general result we prove there are qualitative jumps in the optimal attacks for finding length 1, length 2, and unbounded-length collisions. Namely, the optimal attacks achieve (up to logarithmic factors) on the order of\\((S+T)/2^n\\),\\(ST/2^n\\)and\\(ST^2/2^n\\)advantage. We also give an upper bound on the advantage of a restricted class of short-collision finding attacks via a new analysis on the growth of trees in random functional graphs that may be of independent interest.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_6"}, {"primary_key": "2561479", "vector": [], "sparse_vector": [], "title": "Faster Enumeration-Based Lattice Reduction: Root Hermite Factor k1/(2k) Time kk/8+o(k).", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a lattice reduction algorithm that achieves root Hermite factor\\(k^{1/(2k)}\\)in time\\(k^{k/8+o(k)}\\)and polynomial memory. This improves on the previously best known enumeration-based algorithms which achieve the same quality, but in time\\(k^{k/(2e) + o(k)}\\). A cost of\\(k^{k/8 + o(k)}\\)was previously mentioned as potentially achievable (<PERSON><PERSON><PERSON>ehlé’10) or as a heuristic lower bound (<PERSON><PERSON><PERSON>’10) for enumeration algorithms. We prove the complexity and quality of our algorithm under a heuristic assumption and provide empirical evidence from simulation and implementation experiments attesting to its performance for practical and cryptographic parameter sizes. Our work also suggests potential avenues for achieving costs below\\(k^{k/8 + o(k)}\\)for the same root Hermite factor, based on the geometry of SDBKZ-reduced bases.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_7"}, {"primary_key": "2561480", "vector": [], "sparse_vector": [], "title": "MPC with Friends and Foes.", "authors": ["Bar Alon", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Classical definitions for secure multiparty computation assume the existence of a single adversarial entity controlling the set of corrupted parties. Intuitively, the definition requires that the view of the adversary, corruptingtparties, in a real-world execution can be simulated by an adversary in an ideal model, where parties interact only via a trusted-party. No restrictions, however, are imposed on the view of honest parties in the protocol, thus, if honest parties obtain information about the private inputs of other honest parties – it is not counted as a violation of privacy. This is arguably undesirable in many situations that fall into the MPC framework. Nevertheless, there are secure protocols (e.g., the 2-round multiparty protocol of <PERSON>hai et al. [CRYPTO 2010] tolerating a single corrupted party) that instruct the honest parties to reveal their private inputs to all other honest parties (once the malicious party is somehow identified). In this paper, we put forth a new security notion, which we callFaF-security, extending the classical notion. In essence,\\((t,h^*)\\)-FaF-security requires the view of a subset of up to\\(h^*\\)honest parties to also be simulatable in the ideal model (in addition to the view of the malicious adversary, corrupting up totparties). This property should still hold, even if the adversary leaks information to honest parties by sending them non-prescribed messages. We provide a thorough exploration of the new notion, investigating it in relation to a variety of existing security notions. We further investigate the feasibility of achieving FaF-security and show that every functionality can be computed with (computational)\\((t,h^*)\\)-FaF full-security, if and only if\\(2t+ h^*<m\\). Interestingly, the lower-bound result actually shows that even fair FaF-security is impossible in general when\\(2t\\,+\\,h^*\\ge m\\)(surprisingly, the view of the malicious attacker is not used as the trigger for the attack). We also investigate the optimal round complexity for\\((t,h^*)\\)-FaF-secure protocols and give evidence that the leakage of private inputs of honest parties in the protocol of Ishai et al. [CRYPTO 2010] is inherent.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_24"}, {"primary_key": "2561481", "vector": [], "sparse_vector": [], "title": "Security Analysis and Improvements for the IETF MLS Standard for Group Messaging.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure messaging (SM) protocols allow users to communicate securely over untrusted infrastructure. In contrast to most other secure communication protocols (such as TLS, SSH, or Wireguard), SM sessions may be long-lived (e.g., years) and highly asynchronous. In order to deal with likely state compromises of users during the lifetime of a session, SM protocols do not only protect authenticity and privacy, but they also guaranteeforward secrecy (FS)andpost-compromise security (PCS). The former ensures that messages sent and received before a state compromise remain secure, while the latter ensures that users can recover from state compromise as a consequence of normal protocol usage. SM has received considerable attention in the two-party case, where prior work has studied the well-known double-ratchet paradigm, in particular, and SM as a cryptographic primitive, in general. Unfortunately, this paradigm does not scale well to the problem of securegroupmessaging (SGM). In order to address the lack of satisfactory SGM protocols, the IETF has launched the message-layer security (MLS) working group, which aims to standardize an eponymous SGM protocol. In this work we analyze theTreeKEMprotocol, which is at the core of the SGM protocol proposed by the MLS working group. On a positive note, we show that TreeKEM achieves PCS in isolation (and slightly more). However, we observe that the current version of TreeKEM does not provide an adequate form of FS. More precisely, our work proceeds by formally capturing the exact security of TreeKEM as a so-calledcontinuous group key agreement (CGKA)protocol, which we believe to be a primitive of independent interest. To address the insecurity of TreeKEM, we propose a simple modification to TreeKEM inspired by recent work of Jostet al.(EUROCRYPT ’19) and an idea due to Kohbrok (MLS Mailing List). We then show that the modified version of TreeKEM comes with almost no efficiency degradation but achievesoptimal(according to MLS specification) CGKA security, including FS and PCS. Our work also lays out how a CGKA protocol can be used to design a full SGM protocol.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_9"}, {"primary_key": "2561482", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of LEDAcrypt.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We report on the concrete cryptanalysis of LEDAcrypt, a 2nd Round candidate in NIST’s Post-Quantum Cryptography standardization process and one of 17 encryption schemes that remain as candidates for near-term standardization. LEDAcrypt consists of a public-key encryption scheme built from the McEliece paradigm and a key-encapsulation mechanism (KEM) built from the Niederreiter paradigm, both using a quasi-cyclic low-density parity-check (QC-LDPC) code. In this work, we identify a large class of extremely weak keys and provide an algorithm to recover them. For example, we demonstrate how to recover 1 in\\(2^{47.72}\\)of LEDAcrypt’s keys using only\\(2^{18.72}\\)guesses at the 256-bit security level. This is a major, practical break of LEDAcrypt. Further, we demonstrate a continuum of progressively less weak keys (from extremely weak keys up to all keys) that can be recovered in substantially less work than previously known. This demonstrates that the imperfection of LEDAcrypt is fundamental to the system’s design.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_14"}, {"primary_key": "2561483", "vector": [], "sparse_vector": [], "title": "Compressed $\\varSigma $-Protocol Theory and Practical Application to Plug &amp; Play Secure Algorithmics.", "authors": ["<PERSON>", "<PERSON>"], "summary": "\\(\\varSigma \\)-Protocols provide a well-understood basis for secure algorithmics. Recently, Bulletproofs (<PERSON><PERSON><PERSON> et al., EUROCRYPT 2016, and <PERSON><PERSON><PERSON> et al., S&P 2018) have been proposed as adrop-inreplacement in case of zero-knowledge (ZK) for arithmetic circuits, achieving logarithmic communication instead of linear. Itspivotis an ingenious, logarithmic-size proof of knowledge BP for certainquadraticrelations. However, reducing ZK forgeneralrelations to it forces a somewhat cumbersome “reinvention” of cryptographic protocol theory. We take a rather different viewpoint andreconcileBulletproofs with\\(\\varSigma \\)-Protocol Theory such that (a) simpler circuit ZK is developedwithinestablished theory, while (b) achieving exactly the same logarithmic communication. The natural key here islinearization. First, we repurpose BPs as a blackboxcompressionmechanism for standard\\(\\varSigma \\)-Protocols handling ZK proofs of generallinear relations(on compactly committed secret vectors);our pivot. Second, we reduce the case of generalnonlinearrelations toblackboxapplications of our pivot via a novel variation onarithmetic secret sharing based techniques for\\(\\varSigma \\)-Protocols(<PERSON><PERSON><PERSON> et al., ICITS 2012). Orthogonally, we enhance versatility by enabling scenarios not previously addressed, e.g., when a secret input is dispersed across several commitments. Standard implementation platforms leading to logarithmic communication follow from a Discrete-Log assumption or a generalized Strong-RSA assumption. Also, under a Knowledge-of-Exponent Assumption (KEA) communication drops toconstant, as in ZK-SNARKS. All in all, our theory should more generally be useful for modular (“plug & play”) design of practical cryptographic protocols; this is further evidenced by our separate work (2020) on proofs of partial knowledge.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_18"}, {"primary_key": "2561484", "vector": [], "sparse_vector": [], "title": "Practical Product Proofs for Lattice Commitments.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We construct a practical lattice-based zero-knowledge argument for proving multiplicative relations between committed values. The underlying commitment scheme that we use is the currently most efficient one of <PERSON><PERSON> et al. (SCN 2018), and the size of our multiplicative proof (9 KB) is only slightly larger than the 7 KB required for just proving knowledge of the committed values. We additionally expand on the work of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (Eurocrypt 2018) by showing that the above-mentioned result can also apply when working over rings\\(\\mathbb {Z}_q[X]/(X^d+1)\\)where\\(X^d+1\\)splits into low-degree factors, which is a desirable property for many applications (e.g. range proofs, multiplications over\\(\\mathbb {Z}_q\\)) that take advantage of packing multiple integers into the NTT coefficients of the committed polynomial.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_17"}, {"primary_key": "2561485", "vector": [], "sparse_vector": [], "title": "Non-malleability Against Polynomial Tampering.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the first explicit construction of anon-malleable codethat can handle tampering functions that arebounded-degree polynomials. Prior to our work, this was only known for degree-1 polynomials (affine tampering functions), due to <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> (STOC 2017). As a direct corollary, we obtain an explicit non-malleable code that is secure against tampering by bounded-size arithmetic circuits. We show applications of our non-malleable code in constructingnon-malleable secret sharing schemesthat are robust against bounded-degree polynomial tampering. In fact our result is stronger: we can handle adversaries that can adaptively choose the polynomial tampering function based on initial leakage of a bounded number of shares. Our results are derived from explicit constructions ofseedless non-malleable extractorsthat can handle bounded-degree polynomial tampering functions. Prior to our work, no such result was known even for degree-2 (quadratic) polynomials.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_4"}, {"primary_key": "2561486", "vector": [], "sparse_vector": [], "title": "New Techniques for Zero-Knowledge: Leveraging Inefficient Provers to Reduce Assumptions, Interaction, and Trust.", "authors": ["<PERSON>", "<PERSON>-<PERSON>ed", "<PERSON><PERSON><PERSON>"], "summary": "We present a transformation from NIZK withinefficient proversin the uniform random string (URS) model to ZAPs (two message witness indistinguishable proofs) withinefficient provers. While such a transformation was known for the case where the prover is efficient, the security proof breaks down if the prover is inefficient. Our transformation is obtained via new applications of Nisan-Wigdersondesigns, a combinatorial object originally introduced in the derandomization literature. We observe that our transformation is applicable both in the setting of super-polynomial provers/poly-time adversaries, as well as a new fine-grained setting, where the prover is polynomial time and the verifier/simulator/zero knowledge distinguisher are in a lower complexity class, such as\\(\\mathsf {NC}^1\\). We also present\\(\\mathsf {NC}^1\\)-fine-grained NIZK in the URS model for all of\\(\\mathsf {NP}\\)from the worst-case assumption\\(\\oplus L/\\mathrm {poly}\\not \\subseteq \\mathsf {NC}^1\\). Our techniques yield the following applications: ZAPs for\\(\\mathsf {AM}\\)from Minicrypt assumptions (with super-polynomial time provers), \\(\\mathsf {NC}^1\\)-fine-grained ZAPs for\\(\\mathsf {NP}\\)from worst-case assumptions, Protocols achieving an “offline” notion of NIZK (oNIZK) in the standard (no-CRS) model with uniform soundness in both the super-polynomial setting (from Minicrypt assumptions) and the\\(\\mathsf {NC}^1\\)-fine-grained setting (from worst-case assumptions). The oNIZK notion is sufficient for use in indistinguishability-based proofs.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_24"}, {"primary_key": "2561487", "vector": [], "sparse_vector": [], "title": "On Succinct Arguments and Witness Encryption from Groups.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Succinct non-interactive arguments (SNARGs) enable proofs of\\(\\mathsf {NP} \\)statements with very low communication. Recently, there has been significant work in both theory and practice on constructing SNARGs with very short proofs. Currently, the state-of-the-art in succinctness is due to <PERSON><PERSON> (Eurocrypt 2016) who constructed a SNARG frombilinearmaps where the proof consists of just 3 group elements. In this work, we first construct a concretely-efficient designated-verifier (preprocessing) SNARG with inverse polynomial soundness, where the proof consists of just 2 group elements in astandard(generic) group. This leads to a 50% reduction in concrete proof size compared to <PERSON><PERSON>’s construction. We follow the approach of <PERSON><PERSON><PERSON> et al. (TCC 2013) who describe a compiler from linear PCPs to SNARGs in the preprocessing model. Our improvement is based on a newlinear PCP packingtechnique that allows us to construct 1-query linear PCPs which can then be compiled into a SNARG (using ElGamal encryption over a generic group). An appealing feature of our new SNARG is that the verifier can precompute astatement-independentlookup table in an offline phase; verifying proofs then only requires 2 exponentiations and a single table lookup. This makes our new designated-verifier SNARG appealing in settings that demand fast verification and minimal communication. We then turn to the question of constructing arguments where the proof consists of asinglegroup element. Here, we first show that any (possibly interactive) argument for a language\\(\\mathcal {L}\\)where the verification algorithm is “generic” (i.e., only performs generic group operations) and the proof consists of a single group element, implies awitness encryptionscheme for\\(\\mathcal {L}\\). We then show that under a yet-unproven, but highly plausible, hypothesis on the hardness of approximating the minimal distance of linear codes, we can construct a 2-message laconic argument for\\(\\mathsf {NP} \\)where the proof consists of a single group element. Under the same hypothesis, we obtain a witness encryption scheme for\\(\\mathsf {NP} \\)in the generic group model. Along the way, we show that under a conceptually-similar butprovenhardness of approximation result, there is a 2-message laconic argument for\\(\\mathsf {NP} \\)with negligible soundness error where the prover’s message consists of just 2 group elements. In both settings, we obtain laconic arguments (and linear PCPs) withlineardecision procedures. Our constructions circumvent a previous lower bound by Groth on such argument systems with linear decision procedures by relying onimperfect completeness. Namely, our constructions have vanishing but not negligible completeness error, while the lower bound of Groth implicitly assumes negligible completeness error of the underlying argument. Our techniques thus highlight new avenues for designing linear PCPs, succinct arguments, and witness encryption schemes.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_26"}, {"primary_key": "2561488", "vector": [], "sparse_vector": [], "title": "A Classification of Computational Assumptions in the Algebraic Group Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give a taxonomy of computational assumptions in the algebraic group model (AGM). We first analyze <PERSON><PERSON>’s Uber assumption family for bilinear groups and then extend it in several ways to cover assumptions as diverse as Gap <PERSON> and LRSW. We show that in the AGM every member of these families is implied by theq-discrete logarithm (DL) assumption, for someqthat depends on the degrees of the polynomials defining the Uber assumption. Using the meta-reduction technique, we then separate\\((q+1)\\)-DL fromq-DL, which yields a classification of all members of the extended Uber-assumption families. We finally show that there are strong assumptions, such as one-more DL, that provably fall outside our classification, by proving that they cannot be reduced fromq-DL even in the AGM.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_5"}, {"primary_key": "2561489", "vector": [], "sparse_vector": [], "title": "Efficient Constant-Round MPC with Identifiable Abort and Public Verifiability.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent years have seen a tremendous growth in the interest in secure multiparty computation (MPC) and its applications. While much progress has been made concerning its efficiency, many current, state-of-the-art protocols are vulnerable toDenial of Service attacks, where a cheating party may prevent the honest parties from learning the output of the computation, whilst remaining anonymous. The security model ofidentifiable abortaims to prevent these attacks, by allowing honest parties to agree upon the identity of a cheating party, who can then be excluded in the future. Several existing MPC protocols offer security with identifiable abort against a dishonest majority of corrupted parties. However, all of these protocols have a round complexity that scales linearly with the depth of the circuit (and are therefore unsuitable for use in high latency networks) or use cryptographic primitives or techniques that have a high computational overhead. In this work, we present the first efficient MPC protocols with identifiable abort in the dishonest majority setting, which run in a constant number of rounds and make only black-box use of cryptographic primitives. Our main construction is built from highly efficient primitives in a careful way to achieve identifiability at a low cost. In particular, we avoid the use of public-key operations outside of a setup phase, incurring a relatively low overhead on top of the fastest currently known constant-round MPC protocols based on garbled circuits. Our construction also avoids the use of adaptively secure primitives and heavy zero-knowledge machinery, which was inherent in previous works. In addition, we show how to upgrade our protocol to achievepublic verifiabilityusing a public bulletin board, allowing any external party to verify correctness of the computation or identify a cheating party.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_20"}, {"primary_key": "2561490", "vector": [], "sparse_vector": [], "title": "Alzette: A 64-Bit ARX-box - (Feat. CRAX and TRAX).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ek<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "S-boxes are the only source of non-linearity in many symmetric primitives. While they are often defined as being functions operating on a small space, some recent designs propose the use of much larger ones (e.g., 32 bits). In this context, an S-box is then defined as a subfunction whose cryptographic properties can be estimated precisely. We present a 64-bit ARX-based S-box calledAlzette, which can be evaluated in constant time using only 12 instructions on modern CPUs. Its parallel application can also leverage vector (SIMD) instructions. One iteration ofAlzettehas differential and linear properties comparable to those of the AES S-box, and two are at least as secure as the AES super S-box. As the state size is much larger than the typical 4 or 8 bits, the study of the relevant cryptographic properties ofAlzetteis not trivial. We further discuss how such wide S-boxes could be used to construct round functions of 64-, 128- and 256-bit (tweakable) block ciphers with good cryptographic properties that are guaranteed even in the related-tweak setting. We use these structures to design a very lightweight 64-bit block cipher (Crax) which outperforms SPECK-64/128 for short messages on micro-controllers, and a 256-bit tweakable block cipher (Trax) which can be used to obtain strong security guarantees against powerful adversaries (nonce misuse, quantum attacks).", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_15"}, {"primary_key": "2561491", "vector": [], "sparse_vector": [], "title": "Improved Differential-Linear Attacks with Applications to ARX Ciphers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present several improvements to the framework of differential-linear attacks with a special focus on ARX ciphers. As a demonstration of their impact, we apply them to <PERSON><PERSON><PERSON> and ChaC<PERSON> and we are able to significantly improve upon the best attacks published so far.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_12"}, {"primary_key": "2561492", "vector": [], "sparse_vector": [], "title": "Random Probing Security: Verification, Composition, Expansion and New Constructions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The masking countermeasure is among the most powerful countermeasures to counteract side-channel attacks. Leakage models have been exhibited to theoretically reason on the security of such masked implementations. So far, the most widely used leakage model is theprobing modeldefined by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> at (CRYPTO 2003). While it is advantageously convenient for security proofs, it does not capture an adversary exploiting full leakage traces as,e.g., in horizontal attacks. Those attacks target the multiple manipulations of the same share to reduce noise and recover the corresponding value. To capture a wider class of attacks another model was introduced and is referred to as therandom probing model. From a leakage parameterp, each wire of the circuit leaks its value with probabilityp. While this model much better reflects the physical reality of side channels, it requires more complex security proofs and does not yet come with practical constructions. In this paper, we define the first framework dedicated to the random probing model. We provide an automatic tool, calledVRAPS, to quantify the random probing security of a circuit from its leakage probability. We also formalize a composition property for secure random probing gadgets and exhibit its relation to thestrong non-interference(SNI) notion used in the context of probing security. We then revisit the expansion idea proposed by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (CRYPTO 2018) and introduce a compiler that builds a random probing secure circuit from small base gadgets achieving arandom probing expandabilityproperty. We instantiate this compiler with small gadgets for which we verify the expected properties directly from our automatic tool. Our construction can tolerate a leakage probability up to\\(2^{-8}\\), against\\(2^{-25}\\)for the previous construction, with a better asymptotic complexity.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_12"}, {"primary_key": "2561493", "vector": [], "sparse_vector": [], "title": "Mode-Level vs. Implementation-Level Physical Security in Symmetric Cryptography - A Practical Guide Through the Leakage-Resistance Jungle.", "authors": ["<PERSON><PERSON>", "Olivier <PERSON>", "G<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Triggered by the increasing deployment of embedded cryptographic devices (e.g., for the IoT), the design of authentication, encryption and authenticated encryption schemes enabling improved security against side-channel attacks has become an important research direction. Over the last decade, a number of modes of operation have been proposed and analyzed under different abstractions. In this paper, we investigate the practical consequences of these findings. For this purpose, we first translate the physical assumptions of leakage-resistance proofs into minimum security requirements for implementers. Thanks to this (heuristic) translation, we observe that (i) security against physical attacks can be viewed as a tradeoff between mode-level and implementation-level protection mechanisms, and (ii) security requirements to guarantee confidentiality and integrity in front of leakage can be concretely different for the different parts of an implementation. We illustrate the first point by analyzing several modes of operation with gradually increased leakage-resistance. We illustrate the second point by exhibiting leveled implementations, where different parts of the investigated schemes have different security requirements against leakage, leading to performance improvements when high physical security is needed. We finally initiate a comparative discussion of the different solutions to instantiate the components of a leakage-resistant authenticated encryption scheme.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_13"}, {"primary_key": "2561494", "vector": [], "sparse_vector": [], "title": "A Tight Parallel Repetition Theorem for Partially Simulatable Interactive Arguments via Smooth KL-Divergence.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ad <PERSON>"], "summary": "Hardness amplification is a central problem in the study of interactive protocols. While “natural” parallel repetition transformation is known to reduce the soundness error of some special cases of interactive arguments: three-message protocols (Bellare, Impagliazzo, and Naor [FOCS ’97]) and public-coin protocols (H<PERSON>stad, Pass, Wikström, and <PERSON><PERSON><PERSON> [TCC ’10], <PERSON> and <PERSON> [TCC ’10] and <PERSON> and <PERSON> [TCC ’15]), it fails to do so in the general case (the above <PERSON><PERSON> et al.; also <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [TCC ’07]). The only known round-preserving approach that applies to all interactive arguments is <PERSON><PERSON><PERSON>’srandom-terminatingtransformation [SICOMP ’13], who showed that the parallel repetition of the transformed protocol reduces the soundness error at aweakexponential rate: if the originalm-round protocol has soundness error\\(1-\\varepsilon \\), then then-parallel repetition of its random-terminating variant has soundness error\\((1-\\varepsilon )^{\\varepsilon n{/}m^4}\\)(omitting constant factors). <PERSON><PERSON><PERSON> et al. have generalized this result topartially simulatable interactive arguments, showing that then-fold repetition of anm-round\\(\\delta \\)-simulatable argument of soundness error\\(1-\\varepsilon \\)has soundness error\\((1-\\varepsilon )^{\\varepsilon \\delta ^2 n{/}m^2}\\). When applied to random-terminating arguments, the H<PERSON>stad et al. bound matches that of <PERSON>tner. In this work we prove that parallel repetition of random-terminating arguments reduces the soundness error at a much stronger exponential rate: the soundness error of thenparallel repetition is\\((1-\\varepsilon )^{n{/}m}\\), only anmfactor from the optimal rate of\\((1-\\varepsilon )^n\\)achievable in public-coin and three-message arguments. The result generalizes to\\(\\delta \\)-simulatable arguments, for which we prove a bound of\\((1-\\varepsilon )^{\\delta n{/}m}\\). This is achieved by presenting a tight bound on a relaxed variant of the KL-divergence between the distribution induced by our reduction and its ideal variant, a result whose scope extends beyond parallel repetition proofs. We prove the tightness of the above bound for random-terminating arguments, by presenting a matching protocol.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_19"}, {"primary_key": "2561495", "vector": [], "sparse_vector": [], "title": "Out of Oddity - New Cryptanalytic Techniques Against Symmetric Primitives Optimized for Integrity Proof Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>Plase<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The security and performance of many integrity proof systems like SNARKs, STARKs and Bulletproofs highly depend on the underlying hash function. For this reason several new proposals have recently been developed. These primitives obviously require an in-depth security evaluation, especially since their implementation constraints have led to less standard design approaches. This work compares the security levels offered by two recent families of such primitives, namelyGMiMCandHadesMiMC. We exhibit low-complexity distinguishers against theGMiMCandHadesMiMCpermutations for most parameters proposed in recently launched public challenges for STARK-friendly hash functions. In the more concrete setting of the sponge construction corresponding to the practical use in the ZK-STARK protocol, we present a practical collision attack on a round-reduced version ofGMiMCand a preimage attack on some instances ofHadesMiMC. To achieve those results, we adapt and generalize several cryptographic techniques to fields of odd characteristic.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_11"}, {"primary_key": "2561496", "vector": [], "sparse_vector": [], "title": "Always Have a Backup Plan: <PERSON>y Secure Synchronous MPC with Asynchronous Fallback.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Protocols for secure Multi-Party Computation (MPC) can be classified according to the underlying communication model. Two prominent communication models considered in the literature are the synchronous and asynchronous models, which considerably differ in terms of the achievable security guarantees. Synchronous MPC protocols can achieve the optimal corruption thresholdn/2 and allow every party to give input, but become completely insecure when synchrony assumptions are violated. On the other hand, asynchronous MPC protocols remain secure under arbitrary network conditions, but can tolerate onlyn/3 corruptions and parties with slow connections unavoidably cannot give input. A natural question is whether there exists a protocol for MPC that can tolerate up to\\(t_s < n/2\\)corruptions under a synchronous network and\\(t_a < n/3\\)corruptions even when the network is asynchronous. We answer this question by showing tight feasibility and impossibility results. More specifically, we show that such a protocol exists if and only if\\(t_a + 2t_s < n\\)and the number of inputs taken into account under an asynchronous network is at most\\(n-t_s\\).", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_25"}, {"primary_key": "2561497", "vector": [], "sparse_vector": [], "title": "Random Self-reducibility of Ideal-SVP via Arakelov Random Walks.", "authors": ["<PERSON><PERSON>", "Léo Du<PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "Fixing a number field, the space of all ideal lattices, up to isometry, is naturally an abelian group, called the<PERSON><PERSON><PERSON><PERSON> class group. This fact, well known to number theorists, has so far not been explicitly used in the literature on lattice-based cryptography. Remarkably, the <PERSON>kelov class group is a combination of two groups that have already led to significant cryptanalytic advances: the class group and the unit torus. In the present article, we show that the Arakelov class group has more to offer. We start with the development of a new versatile tool: we prove that, subject to the Riemann Hypothesis for HeckeL-functions, certain random walks on the <PERSON><PERSON>ov class group have a rapid mixing property. We then exploit this result to relate the average-case and the worst-case of the Shortest Vector Problem in ideal lattices. Our reduction appears particularly sharp: for Hermite-SVP in ideal lattices of certain cyclotomic number fields, it loses no more than a\\(\\tilde{O}(\\sqrt{n})\\)factor on the Hermite approximation factor. Furthermore, we suggest that this rapid-mixing theorem should find other applications in cryptography and in algorithmic number theory.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_9"}, {"primary_key": "2561498", "vector": [], "sparse_vector": [], "title": "A Non-PCP Approach to Succinct Quantum-Safe Zero-Knowledge.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Today’s most compact zero-knowledge arguments are based on the hardness of the discrete logarithm problem and related classical assumptions. If one is interested in quantum-safe solutions, then all of the known techniques stem from the PCP-based framework of Kilian (STOC 92) which can be instantiated based on the hardness of any collision-resistant hash function. Both approaches produce asymptotically logarithmic sized arguments but, by exploiting extra algebraic structure, the discrete logarithm arguments are a few orders of magnitude more compact in practice than the generic constructions. In this work, we present the first (poly)-logarithmic, potentiallypost-quantumzero-knowledge arguments that deviate from the PCP approach. At the core of succinct zero-knowledge proofs are succinct commitment schemes (in which the commitment and the opening proof are sub-linear in the message size), and we propose two such constructions based on the hardness of the (Ring)-Short Integer Solution (Ring-SIS) problem, each having certain trade-offs. For commitments toNsecret values, the communication complexity of our first scheme is\\(\\tilde{O}(N^{1/c})\\)for any positive integerc, and\\(O(\\log ^2 N)\\)for the second. Both of these are a significant theoretical improvement over the previously best lattice construction by <PERSON><PERSON><PERSON> et al. (CRYPTO 2018) which gave\\(O(\\sqrt{N})\\)-sized proofs.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_16"}, {"primary_key": "2561499", "vector": [], "sparse_vector": [], "title": "Comparing the Difficulty of Factorization and Discrete Logarithm: A 240-Digit Experiment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We report on two new records: the factorization of RSA-240, a 795-bit number, and a discrete logarithm computation over a 795-bit prime field. Previous records were the factorization of RSA-768 in 2009 and a 768-bit discrete logarithm computation in 2016. Our two computations at the 795-bit level were done using the same hardware and software, and show that computing a discrete logarithm is not much harder than a factorization of the same size. Moreover, thanks to algorithmic variants and well-chosen parameters, our computations were significantly less expensive than anticipated based on previous records. The last page of this paper also reports on the factorization of RSA-250.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_3"}, {"primary_key": "2561500", "vector": [], "sparse_vector": [], "title": "Fast and Secure Updatable Encryption.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Updatable encryption allows a client to outsource ciphertexts to some untrusted server and periodically rotate the encryption key. The server can update ciphertexts from an old key to a new key with the help of an update token, received from the client, which should not reveal anything about plaintexts to an adversary. We provide a new and highly efficient suite of updatable encryption schemes that we collectively call\\(\\mathsf {SHINE}\\). In the variant designed for short messages, ciphertext generation consists of applying one permutation and one exponentiation (per message block), while updating ciphertexts requires just one exponentiation. Variants for longer messages provide much stronger security guarantees than prior work that has comparable efficiency. We present a new confidentiality notion for updatable encryption schemes that implies prior notions. We prove that\\(\\mathsf {SHINE}\\)is secure under our new confidentiality definition while also providing ciphertext integrity.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_16"}, {"primary_key": "2561501", "vector": [], "sparse_vector": [], "title": "Efficient Pseudorandom Correlation Generators from Ring-LPN.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>v <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Secure multiparty computation can often utilize a trusted source of correlated randomness to achieve better efficiency. A recent line of work, initiated by <PERSON> et al. (CCS 2018, Crypto 2019), showed how useful forms of correlated randomness can be generated using a cheap, one-time interaction, followed by only “silent” local computation. This is achieved via apseudorandom correlation generator(PCG), a deterministic function that stretches short correlated seeds into long instances of a target correlation. Previous works constructed concretely efficient PCGs for simple but useful correlations, including random oblivious transfer and vector-OLE, together with efficient protocols to distribute the PCG seed generation. Most of these constructions were based on variants of the Learning Parity with Noise (LPN) assumption. PCGs for other useful correlations had poor asymptotic and concrete efficiency. In this work, we design a new class of efficient PCGs based on different flavors of thering-LPNassumption. Our new PCGs can generate OLE correlations, authenticated multiplication triples, matrix product correlations, and other types of useful correlations over large fields. These PCGs are more efficient by orders of magnitude than the previous constructions and can be used to improve the preprocessing phase of many existing MPC protocols.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_14"}, {"primary_key": "2561502", "vector": [], "sparse_vector": [], "title": "NIZK from LPN and Trapdoor Hash via Correlation Intractability for Approximable Relations.", "authors": ["<PERSON><PERSON><PERSON>", "Venkata Koppula", "<PERSON><PERSON>"], "summary": "We present new non-interactive zero-knowledge argument systems (NIZK), based on standard assumptions that were previously not known to imply it. In particular, we rely on the hardness of both the learning parity with noise (LPN) assumption, and the existence oftrapdoor hash functions(TDH, defined by <PERSON><PERSON> et al., Crypto 2019). Such TDH can be based on a number of standard assumptions, including DDH, QR, DCR, and LWE. We revisit the correlation intractability (CI) framework for converting\\(\\varSigma \\)-protocols into NIZK, and present a different strategy for instantiating it by putting together two new components. First, while prior works considered the search-complexity of the relations for which CI is sought, we consider theirprobabilistic representation. Namely, a distribution over lower-complexity functions that bitwise-computes the target function with all but small (constant) probability. The second component is a new perspective for quantifying the class of relations for which CI is achieved. We show that it is instructive to considerCI for approximable relations(CI-Apx) which is quantified by a class of relations, but requires CI to hold againstany approximationof any relation in this class. We show that CI-Apx for just constant-degree polynomials suffices for NIZK, if the underlying\\(\\varSigma \\)-protocol is implemented using a suitable commitment scheme. We show that such a commitment scheme can be constructed based on low noise LPN. We then show how to construct CI-Apx for constant-degree polynomials from any suitable TDH (with an enhanced correctness property that is satisfied by all existing TDH constructions).", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_26"}, {"primary_key": "2561503", "vector": [], "sparse_vector": [], "title": "Scalable Pseudorandom Quantum States.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Efficiently sampling a quantum state that is hard to distinguish from a truly random quantum state is an elementary task in quantum information theory that has both computational and physical uses. This is often referred to as pseudorandom (quantum) state generator, or PRS generator for short. In existing constructions of PRS generators, security scales with the number of qubits in the states, i.e. the (statistical) security parameter for ann-qubit PRS is roughlyn. Perhaps counter-intuitively,n-qubit PRS are not known to implyk-qubit PRS even for\\(k<n\\). Therefore the question ofscalabilityfor PRS was thus far open: is it possible to constructn-qubit PRS generators with security parameter\\(\\lambda \\)for all\\(n, \\lambda \\). Indeed, we believe that PRS with tiny (even constant)nand large\\(\\lambda \\)can be quite useful. We resolve the problem in this work, showing that any quantum-secure one-way function implies scalable PRS. We follow the paradigm of first showing astatisticallysecure construction when given oracle access to a random function, and then replacing the random function with a quantum-secure (classical) pseudorandom function to achieve computational security. However, our methods deviate significantly from prior works since scalable pseudorandom states require randomizing the amplitudes of the quantum state, and not just the phase as in all prior works. We show how to achieve this using Gaussian sampling.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_15"}, {"primary_key": "2561504", "vector": [], "sparse_vector": [], "title": "Non-malleable Secret Sharing Against Bounded Joint-Tampering Attacks in the Plain Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Secret sharing enables a dealer to split a secret into a set of shares, in such a way that certain authorized subsets of share holders can reconstruct the secret, whereas all unauthorized subsets cannot. Non-malleable secret sharing (<PERSON><PERSON> and <PERSON>, STOC 2018) additionally requires that, even if the shares have been tampered with, the reconstructed secret is either the original or a completely unrelated one. In this work, we construct non-malleable secret sharing tolerating\\(p\\)-timejoint-tamperingattacks in the plain model (in the computational setting), where the latter means that, for any\\(p>0\\)fixeda priori, the attacker can tamper with the same target secret sharing up to\\(p\\)times. In particular, assuming one-to-one one-way functions, we obtain: A secret sharing scheme for threshold access structures which tolerates joint\\(p\\)-time tampering with subsets of the shares of maximal size (i.e., matching the privacy threshold of the scheme). This holds in a model where the attacker commits to a partition of the shares into non-overlapping subsets, and keeps tampering jointly with the shares within such a partition (so-calledselective partitioning). A secret sharing scheme for general access structures which tolerates joint\\(p\\)-time tampering with subsets of the shares of size\\(O(\\sqrt{\\log n})\\), where\\(n\\)is the number of parties. This holds in a stronger model where the attacker is allowed to adaptively change the partition within each tampering query, under the restriction that once a subset of the shares has been tampered with jointly, that subset is always either tampered jointly or not modified by other tampering queries (so-calledsemi-adaptive partitioning). At the heart of our result for selective partitioning lies a new technique showing that every one-timestatisticallynon-malleable secret sharing against joint tampering is in factleakage-resilientnon-malleable (i.e., the attacker can leak jointly from the shares prior to tampering). We believe this may be of independent interest, and in fact we show it implies lower bounds on the share size and randomness complexity of statistically non-malleable secret sharing againstindependenttampering.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_5"}, {"primary_key": "2561505", "vector": [], "sparse_vector": [], "title": "Fully Deniable Interactive Encryption.", "authors": ["<PERSON><PERSON>", "Sunoo Park", "<PERSON><PERSON><PERSON>"], "summary": "Deniable encryption (<PERSON><PERSON><PERSON> al., Crypto 1996) enhances secret communication over public channels, providing the additional guarantee that the secrecy of communication is protected even if the parties are later coerced (or willingly bribed) to expose their entire internal states: plaintexts, keys and randomness. To date, constructions of deniable encryption—and more generally, interactive deniable communication—only address restricted cases where onlyoneparty is compromised (<PERSON><PERSON> and <PERSON>, STOC 2014). The main question—whether deniable communication is at all possible ifbothparties are coerced at once—has remained open. We resolve this question in the affirmative, presenting a communication protocol that isfully deniableunder coercion of both parties. Our scheme has three rounds, assumes subexponentially secure indistinguishability obfuscation and one-way functions, and uses a short global reference string that is generated once at system set-up and suffices for an unbounded number of encryptions and decryptions. Of independent interest, we introduce a new notion calledoff-the-record deniability, which protects parties even when their claimed internal states are inconsistent (a case not covered by prior definitions). Our scheme satisfies both standard deniability and off-the-record deniability.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_27"}, {"primary_key": "2561506", "vector": [], "sparse_vector": [], "title": "Cryptanalytic Extraction of Neural Network Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We argue that the machine learning problem ofmodel extractionis actually a cryptanalytic problem in disguise, and should be studied as such. Given oracle access to a neural network, we introduce a differential attack that can efficiently steal the parameters of the remote model up to floating point precision. Our attack relies on the fact that ReLU neural networks are piecewise linear functions, and thus queries at the critical points reveal information about the model parameters. We evaluate our attack on multiple neural network models and extract models that are\\(2^{20}\\)times more precise and require\\(100{\\times }\\)fewer queries than prior work. For example, we extract a 100, 000 parameter neural network trained on the MNIST digit recognition task with\\(2^{21.5}\\)queries in under an hour, such that the extracted model agrees with the oracle on all inputs up to a worst-case error of\\(2^{-25}\\), or a model with 4, 000 parameters in\\(2^{18.5}\\)queries with worst-case error of\\(2^{-40.4}\\). Code is available athttps://github.com/google-research/cryptanalytic-model-extraction.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_7"}, {"primary_key": "2561507", "vector": [], "sparse_vector": [], "title": "Breaking the Decisional Di<PERSON>ie-<PERSON> Problem for Class Group Actions Using Genus Theory.", "authors": ["W<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we use genus theory to analyze the hardness of the decisional <PERSON><PERSON><PERSON> problem (DDH) for ideal class groups of imaginary quadratic orders, acting on sets of elliptic curves through isogenies; such actions are used in the Couveignes–<PERSON><PERSON><PERSON><PERSON><PERSON>–<PERSON>ol<PERSON><PERSON> protocol and in CSIDH. Concretely, genus theory equips every imaginary quadratic order\\(\\mathcal {O}\\)with a set of assigned characters\\(\\chi : {\\text {cl}}(\\mathcal {O}) \\rightarrow \\{ \\pm 1\\}\\), and for each such character and every secret ideal class\\([\\mathfrak {a}]\\)connecting two public elliptic curvesEand\\(E' = [\\mathfrak {a}] \\star E\\), we show how to compute\\(\\chi ([\\mathfrak {a}])\\)given onlyEand\\(E'\\), i.e. without knowledge of\\([\\mathfrak {a}]\\). In practice, this breaks DDH as soon as the class number is even, which is true for a density 1 subset of all imaginary quadratic orders. For instance, our attack works very efficiently for all supersingular elliptic curves over\\(\\mathbb {F}_p\\)with\\(p \\equiv 1 \\bmod 4\\). Our method relies on computing Tate pairings and walking down isogeny volcanoes.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_4"}, {"primary_key": "2561508", "vector": [], "sparse_vector": [], "title": "Reverse Firewalls for Actively Secure MPCs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Reverse firewalls were introduced at Eurocrypt 2015 by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, as a method for protecting cryptographic protocols against attacks on the devices of the honest parties. In a nutshell: a reverse firewall is placed outside of a device and its goal is to “sanitize” the messages sent by it, in such a way that a malicious device cannot leak its secrets to the outside world. It is typically assumed that the cryptographic devices are attacked in a “functionality-preserving way” (i.e. informally speaking, the functionality of the protocol remains unchanged under this attacks). In their paper, <PERSON><PERSON><PERSON> and <PERSON><PERSON> construct a protocol for passively-secure two-party computations with firewalls, leaving extension of this result to stronger models as an open question. In this paper, we address this problem by constructing a protocol for secure computation with firewalls that has two main advantages over the original protocol from Eurocrypt 2015. Firstly, it is amultiparty computation protocol (i.e. it works for an arbitrary numbernof the parties, and not just for 2). Secondly, it is secure in much stronger corruption settings, namely in theactive corruption model. More precisely: we consider an adversary that can fully corrupt up to\\(n-1\\)parties, while the remaining parties are corrupt in a functionality-preserving way. Our core techniques are: malleable commitments and malleable non-interactive zero-knowledge, which in particular allow us to create a novel protocol for multiparty augmented coin-tossing into the well with reverse firewalls (that is based on a protocol of Lindell from Crypto 2001).", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_26"}, {"primary_key": "2561509", "vector": [], "sparse_vector": [], "title": "Private Set Intersection in the Internet Setting from Lightweight Oblivious PRF.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a new protocol for two-party private set intersection (PSI) with semi-honest security in the plain model and one-sided malicious security in the random oracle model. Our protocol achieves a better balance between computation and communication than existing PSI protocols. Specifically, our protocol is the fastest in networks with moderate bandwidth (e.g., 30–100 Mbps). Considering the monetary cost (proposed by <PERSON><PERSON> et al. in CRYPTO 2019) to run the protocol on a cloud computing service, our protocol also compares favorably. Underlying our PSI protocol is a new lightweight multi-point oblivious pesudorandom function (OPRF) protocol based on oblivious transfer (OT) extension. We believe this new protocol may be of independent interest.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_2"}, {"primary_key": "2561510", "vector": [], "sparse_vector": [], "title": "Multiparty Generation of an RSA Modulus.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a new multiparty protocol for the distributed generation of biprime RSA moduli, with security against any subset of maliciously colluding parties assuming oblivious transfer and the hardness of factoring. Our protocol is highly modular, and its uppermost layer can be viewed as a template that generalizes the structure of prior works and leads to a simpler security proof. We introduce a combined sampling-and-sieving technique that eliminates both the inherent leakage in the approach of <PERSON><PERSON><PERSON><PERSON> et al. (Crypto’18), and the dependence upon additively homomorphic encryption in the approach of <PERSON><PERSON><PERSON> et al. (JCrypt’19). We combine this technique with an efficient, privacy-free check to detect malicious behavior retroactively when a sampled candidate is not a biprime, and thereby overcome covert rejection-sampling attacks and achieve both asymptotic and concrete efficiency improvements over the previous state of the art.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_3"}, {"primary_key": "2561511", "vector": [], "sparse_vector": [], "title": "Dynamic Decentralized Functional Encryption.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce Dynamic Decentralized Functional Encryption (\\(\\textsf {DDFE}\\)), a generalization of Functional Encryption which allows multiple users to join the system dynamically, without relying on a trusted third party or on expensive and interactive Multi-Party Computation protocols. This notion subsumes existing multi-user extensions of Functional Encryption, such as Multi-Input, Multi-Client, and Ad Hoc Multi-Input Functional Encryption. We define and construct schemes for various functionalities which serve as building-blocks for latter primitives and may be useful in their own right, such as a scheme for dynamically computing sums in any Abelian group. These constructions build upon simple primitives in a modular way, and have instantiations from well-studied assumptions, such as DDH or LWE. Our constructions culminate in an Inner-Product scheme for computing weighted sums on aggregated encrypted data, from standard assumptions in prime-order groups in the Random Oracle Model.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_25"}, {"primary_key": "2561512", "vector": [], "sparse_vector": [], "title": "Non-interactive Zero-Knowledge Arguments for QMA, with Preprocessing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A non-interactive zero-knowledge (NIZK) proof system for a language\\(L\\in \\text {NP} \\)allows a prover (who is provided with an instance\\(x \\in L\\), and a witnesswforx) to compute aclassical certificate\\(\\pi \\)for the claim that\\(x\\in L\\)such that\\(\\pi \\)has the following properties: 1)\\(\\pi \\)can be verified efficiently, and 2)\\(\\pi \\)does not reveal any information aboutw, besides the fact that it exists (i.e. that\\(x \\in L\\)). NIZK proof systems have recently been shown to exist for all languages in\\(\\text {NP} \\)in the common reference string (CRS) model and under the learning with errors (LWE) assumption. We initiate the study of NIZKargumentsfor languages in\\(\\text {QMA} \\). An argument system differs from a proof system in that the honest prover must be efficient, and that it is only sound against (quantum) polynomial-time provers. Our first main result is the following: if LWE is hard for quantum computers, then any language in\\(\\text {QMA} \\)has anNIZK argumentwith preprocessing. The preprocessing in our argument system consists of (i) the generation of a CRS and (ii) asingle (instance-independent) quantum messagefrom verifier to prover. The instance-dependent phase of our argument system, meanwhile, involves only a singleclassicalmessage from prover to verifier. Importantly, verification in our protocol is entirely classical, and the verifier needs not have quantum memory; its only quantum actions are in the preprocessing phase. NIZK proofs of (classical) knowledge are widely used in the construction of more advanced cryptographic protocols, and we expect the quantum analogue to likewise find a broad range of applications. In this respect, the fact that our protocol has an entirely classical verification phase is particularly appealing. Our second contribution is to extend the notion of a classicalproof of knowledgeto the quantum setting. We introduce the notions ofargumentsandproofs of quantum knowledge(AoQK/PoQK), and we show that our non-interactive argument system satisfies the definition of an AoQK, which extends its domain of usefulness with respect to cryptographic applications. In particular, we explicitly construct an extractor which can recover a quantum witness from any prover who is successful in our protocol. We also show that any language in QMA has an (interactive)proof of quantum knowledge, again by exhibiting a particular proof system for all languages in QMA and constructing an extractor for it.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_28"}, {"primary_key": "2561513", "vector": [], "sparse_vector": [], "title": "A Polynomial-Time Algorithm for Solving the Hidden Subset Sum Problem.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "At Crypto ’99, <PERSON><PERSON><PERSON> and <PERSON> described a lattice based algorithm for solving the hidden subset sum problem, a variant of the classical subset sum problem where thenweights are also hidden. While the Nguyen-Stern algorithm works quite well in practice for moderate values ofn, we argue that its complexity is actually exponential inn; namely in the final step one must recover a very short basis of an-dimensional lattice, which takes exponential-time inn, as one must apply BKZ reduction with increasingly large block-sizes. In this paper, we describe a variant of the Nguy<PERSON>-Stern algorithm that works in polynomial-time. The first step is the same orthogonal lattice attack with LLL as in the original algorithm. In the second step, instead of applying BKZ, we use a multivariate technique that recovers the short lattice vectors and finally the hidden secrets in polynomial time. Our algorithm works quite well in practice, as we can reach\\(n \\simeq 250\\)in a few hours on a single PC.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_1"}, {"primary_key": "2561514", "vector": [], "sparse_vector": [], "title": "Shorter Non-interactive Zero-Knowledge Arguments and ZAPs for Algebraic Languages.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We put forth a new framework for building pairing-based non-interactive zero-knowledge (\\(\\mathsf {NIZK}\\)) arguments for a wide class of algebraic languages, which are an extension of linear languages, containing disjunctions of linear languages and more. Our approach differs from the Groth-<PERSON>hai methodology, in that we rely on pairings to compile a\\(\\varSigma \\)-protocol into a\\(\\mathsf {NIZK}\\). Our framework enjoys a number of interesting features: conceptual simplicity, parameters derive from the\\(\\varSigma \\)-protocol; proofs as short as resulting from the Fiat-Shamir heuristic applied to the underlying\\(\\varSigma \\)-protocol; fully adaptive soundness and perfect zero-knowledge in the common random string model with a single random group element as CRS; yields simple and efficient two-round, public coin, publicly-verifiable perfect witness-indistinguishable (WI) arguments(ZAPs) in the plain model. To our knowledge, this is thefirstconstruction of two-roundsstatisticalwitness-indistinguishable arguments from pairing assumptions. Our proof system relies on a new (static, falsifiable) assumption over pairing groups which generalizes the standard kernel Diffie-Hellman assumption in a natural way and holds in the generic group model (GGM) and in the algebraic group model (AGM). Replacing <PERSON>roth-<PERSON><PERSON>\\(\\textsf {NIZKs}\\)with our new proof system allows to improve several important cryptographic primitives. In particular, we obtain the shortest tightly-secure structure-preserving signature scheme (which are a core component in anonymous credentials), the shortest tightly-secure quasi-adaptive\\(\\mathsf {NIZK}\\)with unbounded simulation soundness (which in turns implies the shortest tightly-mCCA-secure cryptosystem), and shorter ring signatures.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_27"}, {"primary_key": "2561515", "vector": [], "sparse_vector": [], "title": "LWE with Side Information: Attacks and Concrete Security Estimation.", "authors": ["<PERSON>-<PERSON>ed", "Léo Du<PERSON>", "Huijing Gong", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a framework for cryptanalysis of lattice-based schemes, when side information—in the form of “hints”—about the secret and/or error is available. Our framework generalizes the so-called primal lattice reduction attack, and allows the progressive integration of hints before running a final lattice reduction step. Our techniques for integrating hints include sparsifying the lattice, projecting onto and intersecting with hyperplanes, and/or altering the distribution of the secret vector. Our main contribution is to propose a toolbox and a methodology to integrate such hints into lattice reduction attacks and to predict the performance of those lattice attacks with side information. While initially designed for side-channel information, our framework can also be used in other cases: exploiting decryption failures, or simply exploiting constraints imposed by certain schemes (LAC, Round5, NTRU). We implement a Sage 9.0 toolkit to actually mount such attacks with hints when computationally feasible, and to predict their performances on larger instances. We provide several end-to-end application examples, such as an improvement of a single trace attack on <PERSON><PERSON><PERSON> by <PERSON> et al. (SAC 2018). In particular, our work can estimates security loss even given very little side information, leading to a smooth measurement/computation trade-off for side-channel attacks.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_12"}, {"primary_key": "2561516", "vector": [], "sparse_vector": [], "title": "Black-Box Transformations from Passive to Covert Security with Public Verifiability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the context of secure computation, protocols with security against covert adversaries ensure that any misbehavior by malicious parties will be detected by the honest parties with some constant probability. As such, these protocols provide better security guarantees than passively secure protocols and, moreover, are easier to construct than protocols with full security against active adversaries. Protocols that, upon detecting a cheating attempt, allow the honest parties to compute a certificate that enables third parties to verify whether an accused party misbehaved or not are called publicly verifiable. In this work, we present the first generic compilers for constructing two-party protocols with covert security and public verifiability from protocols with passive security. We present two separate compilers, which are both fully blackbox in the underlying protocols they use. Both of them only incur a constant multiplicative factor in terms of bandwidth overhead and a constant additive factor in terms of round complexity on top of the passively secure protocols they use. The first compiler applies to all two-party protocols that have no private inputs. This class of protocols covers the important class of preprocessing protocols that are used to setup correlated randomness among parties. We use our compiler to obtain the first secret-sharing based two-party protocol with covert security and public verifiability. Notably, the produced protocol achieves public verifiability essentially for free when compared with the best known previous solutions based on secret-sharing that did not provide public verifiability. Our second compiler constructs protocols with covert security and public verifiability for arbitrary functionalities from passively secure protocols. It uses our first compiler to perform a setup phase, which is independent of the parties’ inputs as well as the protocol they would like to execute. Finally, we show how to extend our techniques to obtain multiparty computation protocols with covert security and public verifiability against arbitrary constant fractions of corruptions.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_23"}, {"primary_key": "2561517", "vector": [], "sparse_vector": [], "title": "Adaptively Secure Constrained Pseudorandom Functions in the Standard Model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Constrained pseudorandom functions (CPRFs) allow learning “constrained” PRF keys that can evaluate the PRF on a subset of the input space, or based on some predicate. First introduced by <PERSON><PERSON> and <PERSON> [AC’13], <PERSON><PERSON><PERSON> et al. [CCS’13] and <PERSON> et al. [PKC’14], they have shown to be a useful cryptographic primitive with many applications. These applications often require CPRFs to be adaptively secure, which allows the adversary to learn PRF values and constrained keys in an arbitrary order. However, there is no known construction of adaptively secure CPRFs based on a standard assumption in the standard model for any non-trivial class of predicates. Moreover, even if we rely on strong tools such as indistinguishability obfuscation (IO), the state-of-the-art construction of adaptively secure CPRFs in the standard model only supports the limited class of\\(\\mathbf{NC} ^1\\)predicates. In this work, we develop new adaptively secure CPRFs for various predicates from different types of assumptions in the standard model. Our results are summarized below. We construct adaptively secure andO(1)-collusion-resistant CPRFs fort-conjunctive normal form (t-CNF) predicates from one-way functions (OWFs) wheretis a constant. Here,O(1)-collusion-resistance means that we can allow the adversary to obtain a constant number of constrained keys. Note thatt-CNF includes bit-fixing predicates as a special case. We construct adaptively secure and single-key CPRFs for inner-product predicates from the learning with errors (LWE) assumption. Here, single-key security means that we only allow the adversary to learn one constrained key. Note that inner-product predicates includet-CNF predicates for a constanttas a special case. Thus, this construction supports more expressive class of predicates than that supported by the first construction though it loses the collusion-resistance and relies on a stronger assumption. We construct adaptively secure andO(1)-collusion-resistant CPRFs for all circuits from the LWE assumption and indistinguishability obfuscation (IO). The first and second constructions are the first CPRFs for any non-trivial predicates to achieve adaptive security outside of the random oracle model or relying on strong cryptographic assumptions. Moreover, the first construction is also the first to achieve any notion of collusion-resistance in this setting. Besides, we prove that the first and second constructions satisfy weak 1-key privacy, which roughly means that a constrained key does not reveal the corresponding constraint. The third construction is an improvement over previous adaptively secure CPRFs for less expressive predicates based on IO in the standard model.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_19"}, {"primary_key": "2561518", "vector": [], "sparse_vector": [], "title": "Cryptanalysis Results on Spook - Bringing Full-Round Shadow-512 to the Light.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>Plase<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Spook[BBB+19] is one of the 32 candidates that has made it to the second round of the NIST Lightweight Cryptography Standardization process, and is particularly interesting since it proposes differential side channel resistance. In this paper, we present practical distinguishers of the full 6-step version of the underlying permutations ofSpook, namelyShadow-512andShadow-384, solving challenges proposed by the designers on the permutation. We also propose practical forgeries with 4-stepShadowfor the S1P mode of operation in the nonce misuse scenario, which is allowed by the CIML2 security game considered by the authors. All the results presented in this paper have been implemented.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_13"}, {"primary_key": "2561519", "vector": [], "sparse_vector": [], "title": "Cryptanalysis of the Lifted Unbalanced Oil Vinegar Signature Scheme.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In 2017, <PERSON>.submitted Lifted Unbalanced Oil and Vinegar (LUOV) \n[4], a signature scheme based on the famous multivariate public key cryptosystem (MPKC) called Unbalanced Oil and Vinegar (UOV), to NIST for the competition for post-quantum public key scheme standardization. The defining feature of LUOV is that, though the public key\\(\\mathscr {P}\\)works in the extension field of degreerof\\(\\mathbb {F}_2\\), the coefficients of\\(\\mathscr {P}\\)come from\\(\\mathbb {F}_2\\). This is done to significantly reduce the size of\\(\\mathscr {P}\\). The LUOV scheme is now in the second round of the NIST PQC standardization process. In this paper we introduce a new attack on LUOV. It exploits the “lifted” structure of LUOV to reduce direct attacks on it to those over a subfield. We show that this reduces the complexity below the targeted security for the NIST post-quantum standardization competition.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_10"}, {"primary_key": "2561520", "vector": [], "sparse_vector": [], "title": "The Measure-and-Reprogram Technique 2.0: Multi-round Fiat-Shamir and More.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We revisit recent works by <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON> and by <PERSON> and <PERSON> on the security of the Fiat-Shamir (FS) transformation of\\(\\varSigma \\)-protocols in the quantum random oracle model (QROM). Two natural questions that arise in this context are: (1) whether the results extend to the FS transformation ofmulti-roundinteractive proofs, and (2) whether <PERSON> et al.’s\\(O(q^2)\\)loss in security is optimal. Firstly, we answer question (1) in the affirmative. As a byproduct of solving a technical difficulty in proving this result, we slightly improve the result of <PERSON> et al., equipping it with a cleaner bound and an even simpler proof. We apply our result to digital signature schemes showing that it can be used to prove strong security for schemes like MQDSS in the QROM. As another application we prove QROM-security of a non-interactive OR proof by <PERSON>, <PERSON> and <PERSON>. As for question (2), we show via a Grover-search based attack that <PERSON> et al.’s quadratic security loss for the FS transformation of\\(\\varSigma \\)-protocols is optimal up to a small constant factor. This extends to our new multi-round result, proving it tight up to a factor depending on the number of rounds only, i.e. is constant for constant-round interactive proofs.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_21"}, {"primary_key": "2561521", "vector": [], "sparse_vector": [], "title": "The Memory-Tightness of Authenticated Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper initiates the study of the provable security of authenticated encryption (AE) in the memory-bounded setting. Recent works – <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (TCC ’18), <PERSON><PERSON><PERSON> and <PERSON><PERSON> (EUROCRYPT ’19), and <PERSON><PERSON> (EUROCRYPT ’20) – focus on confidentiality, and look at schemes for which trade-offs between the attacker’s memory and its data complexity are inherent. Here, we ask whether these results and techniques can be lifted to the full AE setting, which additionally asks for integrity. We show both positive and negative results. On the positive side, we provide tight memory-sensitive bounds for the security of GCM and its generalization, CAU (<PERSON><PERSON> and <PERSON>, CRYPTO ’16). Our bounds apply to a restricted case of AE security which abstracts the deployment within protocols like TLS, and rely on a new memory-tight reduction to corresponding restricted notions of confidentiality and integrity. In particular, our reduction uses an amount of memory which linearly depends on that of the given adversary, as opposed to only imposing a constant memory overhead as in earlier works (<PERSON><PERSON><PERSON> et al., CRYPTO ’17). On the negative side, we show that a large class of black-box reductions cannot generically lift confidentiality and integrity security to a joint definition of AE security in a memory-tight way.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_5"}, {"primary_key": "2561522", "vector": [], "sparse_vector": [], "title": "Guaranteed Output Delivery Comes Free in Honest Majority MPC.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the communication complexity of unconditionally secure MPC with guaranteed output delivery over point-to-point channels for corruption threshold\\(t < n/2\\), assuming the existence of a public broadcast channel. We ask the question: “is it possible to construct MPC in this setting s.t. the communication complexity per multiplication gate is linear in the number of parties?” While a number of works have focused on reducing the communication complexity in this setting, the answer to the above question has remained elusive until now. We also focus on the concrete communication complexity of evaluating each multiplication gate. We resolve the above question in the affirmative by providing an MPC with communication complexity\\(O(Cn\\phi )\\)bits (ignoring fixed terms which are independent of the circuit) where\\(\\phi \\)is the length of an element in the field,Cis the size of the (arithmetic) circuit,nis the number of parties. This is the first construction where the asymptotic communication complexity matches the best-known semi-honest protocol. This represents a strict improvement over the previously best-known communication complexity of\\(O(C(n\\phi \\,+\\,\\kappa )\\,+\\,D_Mn^2\\kappa )\\)bits, where\\(\\kappa \\)is the security parameter and\\(D_M\\)is the multiplicative depth of the circuit. Furthermore, the concrete communication complexity per multiplication gate is 5.5 field elements per party in the best case and 7.5 field elements in the worst case when one or more corrupted parties have been identified. This also roughly matches the best-known semi-honest protocol, which requires 5.5 field elements per gate. The above also yields the first secure-with-abort MPC protocol with the same cost per multiplication gate as the best-known semi-honest protocol. Our main result is obtained by compiling the secure-with-abort MPC protocol into a fully secure one.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_22"}, {"primary_key": "2561523", "vector": [], "sparse_vector": [], "title": "Verifiable Registration-Based Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In recent work, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>\n[18] introduced a new encryption framework, which they referred to as Registration-Based Encryption (RBE). The central motivation behind RBE was to provide a novel methodology for solving the well-knownkey-escrowproblem in Identity-Based Encryption (IBE) systems \n[33]. Informally, in an RBE system, there is no private-key generator unlike IBE systems, but instead, it is replaced with a publickey accumulator. Every user in an RBE system samples its own public-secret key pair and sends the public key to the accumulator for registration. The key accumulator has no secret state and is only responsible for compressing all the registered user identity-key pairs into a short public commitment. Here the encryptor only requires the compressed parameters along with the target identity, whereas a decryptor requires supplementary key material along with the secret key associated with the registered public key. The initial construction in \n[18] based on standard assumptions only provided weak efficiency properties. In a follow-up work by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> \n[19], they gave an efficient RBE construction from standard assumptions. However, both these works considered the key accumulator to be honest which might be too strong an assumption in real-world scenarios. In this work, we initiate a formal study of RBE systems withmaliciouskey accumulators. To that end, we introduce a strengthening of the RBE framework which we callVerifiable RBE(VRBE). A VRBE system additionally gives the users an extra capability to obtainshortproofs from the key accumulator proving correct (and unique) registration for every registered user as well as proving non-registration for any yet unregistered identity. We construct VRBE systems that provide succinct proofs of registration and non-registration from standard assumptions (such as CDH, Factoring, LWE). Our proof systems also naturally allow a much more efficient audit process which can be performed by any non-participating third party as well. A by-product of our approach is that we provide a more efficient RBE construction than that provided in the prior work of Garg et al.\n[19]. And lastly, we initiate a study on the extension of VRBE to a wider range of access and trust structures.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_21"}, {"primary_key": "2561524", "vector": [], "sparse_vector": [], "title": "New Constructions of Hinting PRGs, OWFs with Encryption, and More.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Over the last few years, there has been a surge of new cryptographic results, including laconic oblivious transfer \n[13,16], (anonymous/ hierarchical) identity-based encryption \n[9], trapdoor functions \n[19,20], chosen-ciphertext security transformations \n[32,33], designated-verifier zero-knowledge proofs \n[30,34,37], due to a beautiful framework recently introduced in the works of <PERSON> et al.\n[13], and <PERSON><PERSON><PERSON><PERSON> and Garg \n[14]. The primitive of one-way function with encryption (OWFE) \n[19,20] and its relatives (chameleon encryption, one-time signatures with encryption, hinting PRGs, trapdoor hash encryption, batch encryption) \n[9,14,16,17,33] have been a centerpiece in all these results. While there exist multiple realizations of OWFE (and its relatives) from a variety of assumptions such as CDH, Factoring, and LWE, all such constructions fall under the same general “missing block” framework \n[13,14]. Although this framework has been instrumental in opening up a new pathway towards various cryptographic functionalities via the abstraction of OWFE (and its relatives), it has been accompanied by undesirable inefficiencies that might inhibit a much wider adoption in many practical scenarios. Motivated by the surging importance of the OWFE abstraction (and its relatives), a natural question to ask is whether the existing approaches can be diversified to not only obtain more constructions from different assumptions, but also in developing newer frameworks. We believe answering this question will eventually lead to important and previously unexplored performance trade-offs in the overarching applications of this novel cryptographic paradigm. In this work, we propose a newaccumulation-styleframework for building a new class of OWFE as well as hinting PRG constructions with a special focus on achieving shorter ciphertext size and shorter public parameter size (respectively). Such performance improvements parlay into shorter parameters in their corresponding applications. Briefly, we explore the following performance trade-offs—(1) for OWFE, our constructions outperform in terms of ciphertext size as well as encryption time, but this comes at the cost of larger evaluation and setup times, (2) for hinting PRGs, our constructions provide a rather dramatic trade-off between evaluation time versus parameter size, with our construction leading to significantly shorter public parameter size. The trade-off enabled by our hinting PRG construction also leads to interesting implications in the CPA-to-CCA transformation provided in \n[33]. We also provide concrete performance measurements for our constructions and compare them with existing approaches. We believe highlighting such trade-offs will lead to a wider adoption of these abstractions in a practical sense.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_18"}, {"primary_key": "2561525", "vector": [], "sparse_vector": [], "title": "The Summation-Truncation Hybrid: Reusing Discarded Bits for Free.", "authors": ["Aldo <PERSON>ing", "<PERSON>"], "summary": "A well-established PRP-to-PRF conversion design is truncation: one evaluates ann-bit pseudorandom permutation on a certain input, and truncates the result toabits. The construction is known to achieve tight\\(2^{n-a/2}\\)security. Truncation has gained popularity due to its appearance in the GCM-SIV key derivation function (ACM CCS 2015). This key derivation function makes four evaluations of AES, truncates the outputs ton/2 bits, and concatenates these to get a 2n-bit subkey. In this work, we demonstrate that truncation is wasteful. In more detail, we present the Summation-Truncation Hybrid (STH). At a high level, the construction consists of two parallel evaluations of truncation, where the truncated\\((n-a)\\)-bit chunks are not discarded but rather summed together and appended to the output. We prove that STH achieves a similar security level as truncation, and thus that the\\(n-a\\)bits of extra output is rendered for free. In the application of GCM-SIV, the current key derivation can be used to output 3nbits of random material, or it can be reduced to three primitive evaluations. Both changes come with no security loss.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_7"}, {"primary_key": "2561526", "vector": [], "sparse_vector": [], "title": "Better Concrete Security for Half-Gates Garbling (in the Multi-instance Setting).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Chen<PERSON>", "<PERSON>"], "summary": "We study theconcrete securityof high-performance implementations of half-gates garbling, which all rely on (hardware-accelerated) AES. We find that current instantiations usingk-bit wire labels can becompletely broken—in the sense that the circuit evaluator learns all the inputs of the circuit garbler—in time\\(O(2^k/C)\\), whereCis the total number of (non-free) gates that are garbled, possibly across multiple independent executions. The attack can be applied to existing circuit-garbling libraries using\\(k=80\\)when\\(C \\approx 10^9\\), and would require\\(267\\)machine-months and cost about $\\(3500\\)to implement on the Google Cloud Platform. Since the attack can be fully parallelized, it could be carried out in about a month using\\({\\approx }250\\)machines. With this as our motivation, we seek a way to instantiate the hash function in the half-gates scheme so as to achieve better concrete security. We present a construction based on AES that achieves optimal security in the single-instance setting (when only a single circuit is garbled). We also show how to modify the half-gates scheme so that its concrete security does not degrade in the multi-instance setting. Our modified scheme is as efficient as prior work in networks with up to 2 Gbps bandwidth.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_28"}, {"primary_key": "2561527", "vector": [], "sparse_vector": [], "title": "Lattice-Based Blind Signatures, Revisited.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We observe that all previously known lattice-based blind signature schemes contain subtle flaws in their security proofs (e.g., <PERSON><PERSON><PERSON>, ASIACRYPT ’08) or can be attacked (e.g., BLAZE by <PERSON><PERSON><PERSON><PERSON> et al., FC ’20). Motivated by this, we revisit the problem of constructing blind signatures from standard lattice assumptions. We propose a new three-round lattice-based blind signature scheme whose security can be proved, in the random oracle model, from the standard SIS assumption. Our starting point is a modified version of the (insecure) BLAZE scheme, which itself is based <PERSON><PERSON><PERSON><PERSON><PERSON>’s three-round identification scheme combined with a new aborting technique to reduce the correctness error. Our proof builds upon and extends the recent modular framework for blind signatures of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (EUROCRYPT ’19). It also introduces several new techniques to overcome the additional challenges posed by the correctness error which is inherent to all lattice-based constructions. While our construction is mostly of theoretical interest, we believe it to be an important stepping stone for future works in this area.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_18"}, {"primary_key": "2561528", "vector": [], "sparse_vector": [], "title": "Stacked Gar<PERSON> - Garbled Circuit Proportional to Longest Execution Path.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Secure two party computation (2PC) of arbitrary programs can be efficiently achieved using garbled circuits (GC). The bottleneck of GC efficiency is communication. It is widely believed that it is necessary to transmit the entire GC during 2PC, even for conditional branches that are not taken. This folklore belief isfalse. We propose a novel GC technique,stacked garbling, that eliminates the communication cost of inactive conditional branches. We extend the ideas of conditional GC evaluation explored in (Ko<PERSON>nikov, Asiacrypt 18) and (<PERSON> and Kolesnikov, Eurocrypt 20). Unlike these works, ours is for general 2PC where no player knows which conditional branch is taken. Our garbling scheme,\\(\\textsf {Stack}\\), requires communication proportional to the longest execution path rather than to the entire circuit.\\(\\textsf {Stack}\\)is compatible with state-of-the-art techniques, such as free XOR and half-gates. \\(\\textsf {Stack}\\)is agarbling scheme. As such, it can be plugged into a variety of existing protocols, and the resulting round complexity is the same as that of standard GC. The approach does incur computation cost quadratic in the conditional branching factor vs linear in standard schemes, but the tradeoff is beneficial for most programs: GC computation even on weak hardware is faster than GC transmission on fast channels. We implemented\\(\\textsf {Stack}\\)in C++.\\(\\textsf {Stack}\\)reduces communication cost by approximately the branching factor: for 16 branches, communication is reduced by\\(10.5{\\times }\\). In terms of wall-clock time for circuits with branching factor 16 over a 50 Mbps WAN on a laptop,\\(\\textsf {Stack}\\)outperforms state-of-the-art half-gates-based 2PC by more than\\(4{\\times }\\).", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_27"}, {"primary_key": "2561529", "vector": [], "sparse_vector": [], "title": "Security Analysis of NIST CTR-DRBG.", "authors": ["Viet Tung Hoang", "<PERSON><PERSON>"], "summary": "We study the security of\\(\\mathsf {CTR\\text {-}DRBG}\\), one of NIST’s recommended Pseudorandom Number Generator (PRNG) designs. Recently, <PERSON><PERSON> and <PERSON><PERSON><PERSON> (Eurocrypt’ 19), and then <PERSON><PERSON><PERSON> et al. (S&P’ 20) point out some potential vulnerabilities in both NIST specification and common implementations of\\(\\mathsf {CTR\\text {-}DRBG}\\). While these researchers do suggest counter-measures, the security of the patched\\(\\mathsf {CTR\\text {-}DRBG}\\)is still questionable. Our work fills this gap, proving that\\(\\mathsf {CTR\\text {-}DRBG}\\)satisfies the robustness notion of <PERSON><PERSON> et al. (CCS’13), the standard security goal for PRNGs.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_8"}, {"primary_key": "2561530", "vector": [], "sparse_vector": [], "title": "Chosen Ciphertext Security from Injective Trapdoor Functions.", "authors": ["<PERSON>", "Venkata Koppula", "<PERSON>"], "summary": "We provide a construction of chosen ciphertext secure public-key encryption from (injective) trapdoor functions. Our construction is black box and assumes no special properties (e.g. “lossy”, “correlated product secure”) of the trapdoor function.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_28"}, {"primary_key": "2561531", "vector": [], "sparse_vector": [], "title": "Handling Adaptive Compromise for Practical Encryption Schemes.", "authors": ["<PERSON>", "Nirvan Tyagi"], "summary": "We provide a new definitional framework capturing the multi-user security of encryption schemes and pseudorandom functions in the face of adversaries that can adaptively compromise users’ keys. We provide a sequence of results establishing the security of practical symmetric encryption schemes under adaptive compromise in the random oracle or ideal cipher model. The bulk of analysis complexity for adaptive compromise security is relegated to the analysis of lower-level primitives such as pseudorandom functions. We apply our framework to give proofs of security for the BurnBox system for privacy in the face of border searches and the in-use searchable symmetric encryption scheme due to <PERSON> et al. In both cases, prior analyses had bugs that our framework helps avoid.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_1"}, {"primary_key": "2561532", "vector": [], "sparse_vector": [], "title": "Amplifying the Security of Functional Encryption, Unconditionally.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Security amplification is a fundamental problem in cryptography. In this work, we study security amplification for functional encryption (FE). We show two main results: For any constant\\(\\epsilon \\in (0,1)\\), we can amplify any FE scheme for\\(\\mathsf {P/poly}\\)which is\\(\\epsilon \\)-secure against all polynomial sized adversaries to a fully secure FE scheme for\\(\\mathsf {P/poly}\\), unconditionally. For any constant\\(\\epsilon \\in (0,1)\\), we can amplify any FE scheme for\\(\\mathsf {P/poly}\\)which is\\(\\epsilon \\)-secure against subexponential sized adversaries to a fully subexponentially secure FE scheme for\\(\\mathsf {P/poly}\\), unconditionally. Furthermore, both of our amplification results preserve compactness of the underlying FE scheme. Previously, amplification results for FE were only known assuming subexponentially secure LWE. Along the way, we introduce a new form of homomorphic secret sharing called set homomorphic secret sharing that may be of independent interest. Additionally, we introduce a new technique, which allows one to argue security amplification of nested primitives, and prove a general theorem that can be used to analyze the security amplification of parallel repetitions.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_24"}, {"primary_key": "2561533", "vector": [], "sparse_vector": [], "title": "Delegation with Updatable Unambiguous Proofs and PPAD-Hardness.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we construct anupdatableandunambiguousdelegation scheme based on the decisional assumption on bilinear groups introduced by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> [STOC 2019]. Using this delegation scheme, we showPPAD-hardness (and hence the hardness of computing Nash equilibria) based on the quasi-polynomial hardness of this bilinear group assumption and any hard language that is decidable in quasi-polynomial time and polynomial space. The delegation scheme is for super-polynomial time deterministic computations and is publicly verifiable and non-interactive in the common reference string (CRS) model. It isupdatablemeaning that given a proof for the statement that a Turing machine reaches some configurationCinTsteps, it isefficientto update it into a proof for the statement that the machine reaches the next configuration\\(C'\\)in\\(T+1\\)steps. It isunambiguousmeaning that it is hard to find two different proofs for the same statement.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_23"}, {"primary_key": "2561534", "vector": [], "sparse_vector": [], "title": "Order-Fairness for Byzantine Consensus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Decades of research in both cryptography and distributed systems has extensively studied the problem of state machine replication, also known as Byzantine consensus. A consensus protocol must satisfy two properties:consistencyandliveness. These properties ensure that honest participating nodes agree on the same log and dictate when fresh transactions get added. They fail, however, to ensure against adversarial manipulation of the actualorderingof transactions in the log. Indeed, in leader-based protocols (almost all protocols used today), malicious leaders can directly choose the final transaction ordering. To rectify this problem, we propose a third consensus property:transaction order-fairness. We initiate the first formal investigation of order-fairness and explain its fundamental importance. We provide several natural definitions for order-fairness and analyze the assumptions necessary to realize them. We also propose a new class of consensus protocols called Aequitas. Aequitas protocols are the first to achieve order-fairness in addition to consistency and liveness. They can be realized in a black-box way using existing broadcast and agreement primitives (or indeed using any consensus protocol), and work in both synchronous and asynchronous network models.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_16"}, {"primary_key": "2561535", "vector": [], "sparse_vector": [], "title": "Fast Reduction of Algebraic Lattices over Cyclotomic Fields.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We describe two very efficient polynomial-time algorithms for reducing module lattices defined over arbitrary cyclotomic fields that solve the\\(\\gamma \\)-Hermite Module-SVP problem. They both exploit the structure of tower fields and the second one also uses the symplectic geometry existing in these fields. We conjecture that a rank-2 module over a cyclotomic field of degreenwithB-bit coefficients can be heuristically reduced within approximation factor\\(2^{\\widetilde{\\text {O}}\\left( n\\right) }\\)in time\\(\\widetilde{\\text {O}}\\left( n^2B\\right) \\). In the symplectic algorithm, if the condition numberCof the input matrix is large enough, this complexity shrinks to\\(\\widetilde{\\text {O}}\\left( n^{\\log _2 3}C\\right) \\). In cryptography, matrices are well-conditioned and we can take\\(C=B\\), but in the worst case,Ccan be as large asnB. This last result is particularly striking as for some matrices, we can go below the\\(n^2B\\)swaps lower bound given by the analysis of LLL based on the potential. These algorithms are parallel and we provide a full implementation. We apply them on multilinear cryptographic concrete parameters by reducing matrices of dimension 4096 with 6675-bit integers in 4 days. Finally, we give a quasicubic time for the Gentry-Szy<PERSON> algorithm and run it in dimension 1024. It requires efficient ideal multiplications which need fast lattice reductions.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_6"}, {"primary_key": "2561536", "vector": [], "sparse_vector": [], "title": "Round-Optimal Black-Box Commit-and-Prove with Succinct Communication.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We give a four-round black-box construction of a commit-and-prove protocol with succinct communication. Our construction is WI and has constant soundness error, and it can be upgraded into a one that is ZK and has negligible soundness error by relying on a round-preserving transformation of <PERSON><PERSON><PERSON> et al. (TCC 2018). Our construction is obtained by combining the MPC-in-the-head technique of <PERSON><PERSON> et al. (SICOMP 2009) with the two-round succinct argument of <PERSON><PERSON> et al. (STOC 2014), and the main technical novelty lies in the analysis of the soundness—we show that, although the succinct argument of <PERSON><PERSON> et al. does not necessarily provide soundness for\\(\\mathcal {NP}\\)statements, it can be used in the MPC-in-the-head technique for proving the consistency of committed MPC views. Our construction is based on sub-exponentially hard collision-resistant hash functions, two-round PIRs, and two-round OTs.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_19"}, {"primary_key": "2561537", "vector": [], "sparse_vector": [], "title": "Anonymous Tokens with Private Metadata Bit.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a cryptographic construction for anonymous tokens with private metadata bit, called PMBTokens. This primitive enables an issuer to provide a user with a lightweight, single-use anonymous trust token that can embed a single private bit, which is accessible only to the party who holds the secret authority key and is private with respect to anyone else. Our construction generalizes and extends the functionality of Privacy Pass (PETS’18) with this private metadata bit capability. It provides unforgeability, unlinkability, and privacy for the metadata bit properties based on the DDH and CTDH assumptions in the random oracle model. Both Privacy Pass and PMBTokens rely on non-interactive zero-knowledge proofs (NIZKs). We present new techniques to remove the need for NIZKs, while still achieving unlinkability. We implement our constructions and we report their efficiency costs.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_11"}, {"primary_key": "2561538", "vector": [], "sparse_vector": [], "title": "Leakage-Resilient Key Exchange and Two-Seed Extractors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Can <PERSON> and <PERSON> agree on a uniformly random secret key without having any truly secret randomness to begin with? Here we consider a setting where <PERSON> can get partial leakage on the internal state of both <PERSON> and <PERSON> individually before the protocol starts. They then run a protocol using their states without any additional randomness and need to agree on a shared key that looks uniform to <PERSON>, even after observing the leakage and the protocol transcript. We focus on non-interactive (one round) key exchange (NIKE), where <PERSON> and <PERSON> send one message each without waiting for one another. We first consider this problem in the symmetric-key setting, where the states of <PERSON> and <PERSON> include a shared secret as well as individual uniform randomness. However, since <PERSON> gets leakage on these states, <PERSON> and <PERSON> need to performprivacy amplificationto derive a fresh secret key from them. Prior solutions require <PERSON> and <PERSON> to sample fresh uniform randomness during the protocol, while in our setting all of their randomness was already part of their individual states a priori and was therefore subject to leakage. We show an information-theoretic solution to this problem using a novel primitive that we call atwo-seed extractor, which we in turn construct by drawing a connection to communication-complexity lower-bounds in the number-on-forehead (NOF) model. We then turn to studying this problem in the public-key setting, where the states of <PERSON> and <PERSON> consist of independent uniform randomness. Unfortunately, we give a black-box separation showing that leakage-resilient NIKE in this setting cannot be proven secure via a black-box reduction under any game-based assumption when the leakage is super-logarithmic. This includes virtually all assumptions used in cryptography, and even very strong assumptions such as indistinguishability obfuscation (\\(\\textit{i}\\mathcal {O}\\)). Nevertheless, we also provide positive results that get around the above separation: We show that every key exchange protocol (e.g., Diffie-Hellman) is secure when the leakage amount is logarithmic, or potentially even greater if we assume sub-exponential security without leakage. We notice that the black-box separation does not extend to schemes in thecommon reference string(CRS) model, or to schemes withpreprocessing, where Alice and Bob can individually pre-process their random coins to derive their secret state prior to leakage. We give a solution in the CRS model with preprocessing using bilinear maps. We also give solutions in just the CRS model alone (without preprocessing)orjust with preprocessing (without a CRS), using\\(\\textit{i}\\mathcal {O}\\)and lossy functions.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_14"}, {"primary_key": "2561539", "vector": [], "sparse_vector": [], "title": "Automatic Verification of Differential Characteristics: Application to Reduced Gimli.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Since <PERSON><PERSON><PERSON> was selected as the SHA-3 standard, more and more permutation-based primitives have been proposed. Different from block ciphers, there is no round key in the underlying permutation for permutation-based primitives. Therefore, there is a higher risk for a differential characteristic of the underlying permutation to become incompatible when considering the dependency of difference transitions over different rounds. However, in most of the MILP or SAT based models to search for differential characteristics, only the difference transitions are involved and are treated as independent in different rounds, which may cause that an invalid one is found for the underlying permutation. To overcome this obstacle, we are motivated to design a model which automatically avoids the inconsistency in the search for differential characteristics. Our technique is to involve both the difference transitions and value transitions in the constructed model. Such an idea is inspired by the algorithm to find SHA-2 characteristics as proposed by <PERSON> et al. in ASIACRYPT 2011, where the differential characteristic and the conforming message pair are simultaneously searched. As a first attempt, our new technique will be applied to the Gimli permutation, which was proposed in CHES 2017. As a result, we reveal that some existing differential characteristics of reduced Gimli are indeed incompatible, one of which is found in the Gimli document. In addition, since only the permutation is analyzed in the Gimli document, we are lead to carry out a comprehensive study, covering the proposed hash scheme and the authenticated encryption (AE) scheme specified for Gimli, which has become a second round candidate of the NIST lightweight cryptography standardization process. For the hash scheme, a semi-free-start (SFS) collision attack can reach up to 8 rounds starting from an intermediate round. For the AE scheme, a state recovery attack is demonstrated to achieve up to 9 rounds. It should be emphasized that our analysis does not threaten the security of Gimli.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_8"}, {"primary_key": "2561540", "vector": [], "sparse_vector": [], "title": "Rounding in the Rings.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we conduct a comprehensive study on establishing hardness reductions for (Module) Learning with Rounding over rings (RLWR). Towards this, we present an algebraic framework of LWR, inspired by a recent work of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (TCC ’19). Then we show a search-to-decision reduction for Ring-LWR, generalizing a result in the plain LWR setting by <PERSON><PERSON><PERSON><PERSON> et al. (TCC ’15). Finally, we show a reduction from Ring-LWE to Module Ring-LWR (even for leaky secrets), generalizing the plain LWE to LWR reduction by <PERSON><PERSON> et al. (Crypto ’13). One of our central techniques is a new ring leftover hash lemma, which might be of independent interests.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_11"}, {"primary_key": "2561541", "vector": [], "sparse_vector": [], "title": "Fiat-Shamir for Repeated Squaring with Applications to PPAD-Hardness and VDFs.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The Fiat-Shamir transform is a methodology for compiling a (public-coin) interactive proof system for a languageLinto anon-interactiveargument system forL. Proving security of the Fiat-Shamir transform in the standard model, especially in the context ofsuccinctarguments, is largely an unsolved problem. The work of <PERSON><PERSON> et al. (STOC 2019) proved the security of the Fiat-Shamir transform applied to the Goldwasser-<PERSON>-<PERSON> (STOC 2008) succinct interactive proof system under a very strong “optimal learning with errors” assumption. Achieving a similar result under standard assumptions remains an important open question. In this work, we consider the problem of compiling a different succinct interactive proof system: <PERSON><PERSON><PERSON>’s proof system (ITCS 2019) for the iterated squaring problem. We construct a hash function family (with evaluation time roughly\\(2^{\\lambda ^{\\epsilon }}\\)) that guarantees the soundness of Fiat-Shamir for this protocol assuming the sub-exponential (\\(2^{-n^{1-\\epsilon }}\\))-hardness of then-dimensional learning with errors problem. (The latter follows from the worst-case\\(2^{n^{1-\\epsilon }}\\)hardness of lattice problems.) More generally, we extend the “bad-challenge function” methodology of <PERSON><PERSON> et al. for proving the soundness of Fiat-Shamir to a class of protocols whose bad-challenge functions arenotefficiently computable. As a corollary (following <PERSON><PERSON><PERSON><PERSON> et al., ePrint 2019 and Ephraim et al., EUROCRYPT 2020), we construct hard-on-average problems in the complexity class\\(\\mathbf {CLS}\\subset \\mathbf {PPAD}\\)under the\\(2^{\\lambda ^\\epsilon }\\)-hardness of the repeated squaring problem and the\\(2^{-n^{1-\\epsilon }}\\)-hardness of the learning with errors problem. Under the additional assumption that the repeated squaring problem is “inherently sequential”, we also obtain a Verifiable Delay Function (Boneh et al., EUROCRYPT 2018) in the standard model. Finally, we give additional PPAD-hardness and VDF instantiations demonstrating a broader tradeoff between the strength of the repeated squaring assumption and the strength of the lattice assumption.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_22"}, {"primary_key": "2561542", "vector": [], "sparse_vector": [], "title": "Black-Box Use of One-Way Functions is Useless for Optimal Fair Coin-Tossing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A two-party fair coin-tossing protocol guarantees output delivery to the honest party even when the other party aborts during the protocol execution. <PERSON><PERSON><PERSON> (STOC–1986) demonstrated that a computationally bounded fail-stop adversary could alter the output distribution of the honest party by (roughly) 1/r(in the statistical distance) in anr-message coin-tossing protocol. An optimal fair coin-tossing protocol ensures that no adversary can alter the output distribution beyond 1/r. In a seminal result, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (TCC–2009) constructed the first optimal fair coin-tossing protocol using (unfair) oblivious transfer protocols. Whether the existence of oblivious transfer protocols is a necessary hardness of computation assumption for optimal fair coin-tossing remains among the most fundamental open problems in theoretical cryptography. The results of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (FOCS–1989) and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (1993) prove that optimal fair coin-tossing implies the necessity of one-way functions’ existence; a significantly weaker hardness of computation assumption compared to the existence of secure oblivious transfer protocols. However, the sufficiency of the existence of one-way functions is not known. Towards this research endeavor, our work proves a black-box separation of optimal fair coin-tossing from the existence of one-way functions. That is, the black-box use of one-way functions cannot enable optimal fair coin-tossing. Following the standard <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC–1989) approach of proving black-box separations, our work considers anyr-message fair coin-tossing protocol in the random oracle model where the parties have unbounded computational power. We demonstrate a fail-stop attack strategy for one of the parties to alter the honest party’s output distribution by\\(1/\\sqrt{r}\\)by making polynomially-many additional queries to the random oracle. As a consequence, our result proves that ther-message coin-tossing protocol of Blum (COMPCON–1982) and Cleve (STOC–1986), which uses one-way functions in a black-box manner, is the best possible protocol because an adversary cannot change the honest party’s output distribution by more than\\(1/\\sqrt{r}\\). Several previous works, for example, Dachman–Soled, Lindell, Mahmoody, and Malkin (TCC–2011), Haitner, Omri, and Zarosim (TCC–2013), and Dachman–Soled, Mahmoody, and Malkin (TCC–2014), made partial progress on proving this black-box separation assuming some restrictions on the coin-tossing protocol. Our work diverges significantly from these previous approaches to prove this black-box separation in its full generality. The starting point is the recently introduced potential-based inductive proof techniques for demonstrating large gaps in martingales in the information-theoretic plain model. Our technical contribution lies in identifying a global invariant of communication protocols in the random oracle model that enables the extension of this technique to the random oracle model.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_21"}, {"primary_key": "2561543", "vector": [], "sparse_vector": [], "title": "Nearly Optimal Robust Secret Sharing Against Rushing Adversaries.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Robust secret sharing is a strengthening of standard secret sharing that allows the shared secret to be recovered even if some of the shares being used in the reconstruction have been adversarially modified. In this work, we study the setting where out of all thenshares, the adversary is allowed to adaptively corrupt and modify up totshares, where\\(n = 2t+1\\)(Note that if the adversary is allowed to modify any more shares, then correct reconstruction would be impossible.). Further, we deal withrushingadversaries, meaning that the adversary is allowed to see the honest parties’ shares before modifying its own shares. It is known that when\\(n = 2t+1\\), to share a secret of lengthmbits and recover it with error less than\\(2^{-\\lambda }\\), shares of size at least\\(m+\\lambda \\)bits are needed. Recently, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (EUROCRYPT 2016) constructed a robust secret sharing scheme with shares of size\\(m + O(\\lambda \\cdot \\mathrm{polylog}(n,m,\\lambda ))\\)bits that is secure in this setting against non-rushing adversaries. Later, <PERSON><PERSON> and <PERSON> (EUROCRYPT 2019) constructed a scheme that is secure against rushing adversaries, but has shares of size\\(m\\,+\\,O(\\lambda \\cdot n^{{\\varepsilon }}\\cdot \\mathrm{polylog}(n,m,\\lambda ))\\)bits for an arbitrary constant\\({\\varepsilon }> 0\\). They also showed a variant of their construction with share size\\(m + O(\\lambda \\cdot \\mathrm{polylog}(n,m,\\lambda ))\\)bits, but with super-polynomial reconstruction time. We present a robust secret sharing scheme that is simultaneously close-to-optimal in all of these respects – it is secure against rushing adversaries, has shares of size\\(m\\,+\\,O(\\lambda \\log {n} (\\log {n}+\\log {m}))\\)bits, and has polynomial-time sharing and reconstruction. Central to our construction is a polynomial-time algorithm for a problem on semi-random graphs that arises naturally in the paradigm of local authentication of shares used by us and in the aforementioned work.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_6"}, {"primary_key": "2561544", "vector": [], "sparse_vector": [], "title": "Two-Sided Malicious Security for Private Intersection-Sum with Cardinality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Private intersection-sum with cardinality allows two parties, where each party holds a private set and one of the parties additionally holds a private integer value associated with each element in her set, to jointly compute the cardinality of the intersection of the two sets as well as the sum of the associated integer values for all the elements in the intersection, and nothing beyond that. We present a new construction for private intersection sum with cardinality that provides malicious security with abort and guarantees that both parties receive the output upon successful completion of the protocol. A central building block for our constructions is a primitive calledshuffled distributed oblivious PRF (DOPRF), which is a PRF that offers oblivious evaluation using a secret key shared between two parties, and in addition to this allows obliviously permuting the PRF outputs of several parallel oblivious evaluations. We present the first construction for shuffled DOPRF with malicious security. We further present several new sigma proof protocols for relations across Pedersen commitments, ElGamal encryptions, and Camenisch-Shoup encryptions that we use in our main construction, for which we develop new batching techniques to reduce communication. We implement and evaluate the efficiency of our protocol and show that we can achieve communication cost that is only\\(4{-}5{\\times }\\)greater than the most efficient semi-honest protocol. When measuring monetary cost of executing the protocol in the cloud, our protocol is\\(25{\\times }\\)more expensive than the semi-honest protocol. Our construction also allows for different parameter regimes that enable trade-offs between communication and computation.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_1"}, {"primary_key": "2561545", "vector": [], "sparse_vector": [], "title": "Asymptotic Complexities of Discrete Logarithm Algorithms in Pairing-Relevant Finite Fields.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the discrete logarithm problem at the boundary case between small and medium characteristic finite fields, which is precisely the area where finite fields used in pairing-based cryptosystems live. In order to evaluate the security of pairing-based protocols, we thoroughly analyze the complexity of all the algorithms that coexist at this boundary case: the Quasi-Polynomial algorithms, the Number Field Sieve and its many variants, and the Function Field Sieve. We adapt the latter to the particular case where the extension degree is composite, and show how to lower the complexity by working in a shifted function field. All this study finally allows us to give precise values for the characteristic asymptotically achieving the highest security level for pairings. Surprisingly enough, there exist special characteristics that are as secure as general ones.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_2"}, {"primary_key": "2561546", "vector": [], "sparse_vector": [], "title": "Incompressible Encodings.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Anincompressible encodingcan probabilistically encode some dataminto a codewordc, which is not much larger. Anyone can decode the codewordcto recover the original datam. However, the codewordccannot be efficiently compressed, even if the original datamis given to the decompression procedure on the side. In other words,cis an efficiently decodable representation ofm, yet is computationally incompressible even givenm. An incompressible encoding iscomposableif many encodings cannot be simultaneously compressed. The recent work of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> (CRYPTO ’19) defined a variant of incompressible encodings as a building block for “proofs of replicated storage”. They constructed incompressible encodings in an ideal permutation model, but it was left open if they can be constructed under standard assumptions, or even in the more basic random-oracle model. In this work, we undertake the comprehensive study of incompressible encodings as a primitive of independent interest and give new constructions, negative results and applications: We construct incompressible encodings in thecommon random string (CRS)model under either Decisional Composite Residuosity (DCR) or Learning with Errors (LWE). However, the construction has several drawbacks: (1) it is not composable, (2) it only achieves selective security, and (3) the CRS is as long as the datam. We leverage the above construction to also get a scheme in the random-oracle model, under the same assumptions, that avoids all of the above drawbacks. Furthermore, it is significantly more efficient than the prior ideal-model construction. We give black-box separations, showing that incompressible encodings in the plain model cannot be proven secure under any standard hardness assumption, and incompressible encodings in the CRS model must inherently suffer from all of the drawbacks above. We give a new application to “big-key cryptography in the bounded-retrieval model”, where secret keys are made intentionally huge to make them hard to exfiltrate. Using incompressible encodings, we can get all the security benefits of a big key without wasting storage space, by having the key to encode useful data.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_17"}, {"primary_key": "2561547", "vector": [], "sparse_vector": [], "title": "Lattice Reduction for Modules, or How to Reduce ModuleSVP to ModuleSVP.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "This is the extended abstract of \n[MS20]. See the full version ateprint:2019/1142. We show how to generalize lattice reduction algorithms to module lattices. Specifically, we reduce\\(\\gamma \\)-approximate ModuleSVP over module lattices with rank\\(k \\ge 2\\)to\\(\\gamma '\\)-approximate ModuleSVP over module lattices with rank\\(2 \\le \\beta \\le k\\). To do so, we modify the celebrated slide-reduction algorithm of <PERSON><PERSON> and <PERSON><PERSON><PERSON> to work with module filtrations, a high-dimensional generalization of the (\\(\\mathbb {Z}\\)-)basis of a lattice. The particular value of\\(\\gamma \\)that we achieve depends on the underlying number fieldK, the order\\(R \\subseteq \\mathcal {O}_K\\), and the embedding (as well as, of course,kand\\(\\beta \\)). However, for reasonable choices of these parameters, the resulting value of\\(\\gamma \\)is surprisingly close to the one achieved by “plain” lattice reduction algorithms, which require an arbitrary SVP oracle in the same dimension. In other words, we show that ModuleSVP oracles are nearly as useful as SVP oracles for solving higher-rank instances of approximate ModuleSVP. Our result generalizes the recent independent result of <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, which works in the important special case when\\(\\beta = 2\\)and\\(R = \\mathcal {O}_K\\)is the ring of integers ofKunder the canonical embedding, while our reduction works. Indeed, at a high level our reduction can be thought of as a generalization of theirs in roughly the same way that block reduction generalizes LLL reduction. In this extended abstract, we present a special case of the more general result to appear in the full version \n[MS20].", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56880-1_8"}, {"primary_key": "2561548", "vector": [], "sparse_vector": [], "title": "Lower Bounds for Encrypted Multi-Maps and Searchable Encryption in the Leakage Cell Probe Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Encrypted multi-maps (EMMs) enable clients to outsource the storage of a multi-map to a potentially untrusted server while maintaining the ability to perform operations in a privacy-preserving manner. EMMs are an important primitive as they are an integral building block for many practical applications such as searchable encryption and encrypted databases. In this work, we formally examine the tradeoffs between privacy and efficiency for EMMs. Currently, all known dynamic EMMs with constant overhead reveal if two operations are performed on the same key or not that we denote as the\\( global\\ key\\text {-}equality\\ pattern \\). In our main result, we present strong evidence that the leakage of the global key-equality pattern is inherent for any dynamic EMM construction withO(1) efficiency. In particular, we consider the slightly smaller leakage of\\( decoupled\\ key\\text {-}equality\\ pattern \\)where leakage of key-equality between update and query operations is decoupled and the adversary only learns whether two operations of the\\( same\\ type \\)are performed on the same key or not. We show that any EMM with at most decoupled key-equality pattern leakage incurs\\(\\varOmega (\\lg n)\\)overhead in the\\( leakage\\ cell\\ probe\\ model \\). This is tight as there exist ORAM-based constructions of EMMs with logarithmic slowdown that leak no more than the decoupled key-equality pattern (and actually, much less). Furthermore, we present stronger lower bounds that encrypted multi-maps leaking at most the decoupled key-equality pattern but are able to perform one of either the update or query operations in the plaintext still require\\(\\varOmega (\\lg n)\\)overhead. Finally, we extend our lower bounds to show that dynamic,\\( response\\text {-}hiding \\)searchable encryption schemes must also incur\\(\\varOmega (\\lg n)\\)overhead even when one of either the document updates or searches may be performed in the plaintext.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_15"}, {"primary_key": "2561549", "vector": [], "sparse_vector": [], "title": "Quantifying the Security Cost of Migrating Protocols to Practice.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give a framework for relating the concrete security of a “reference” protocol (say, one appearing in an academic paper) to that of some derived, “real” protocol (say, appearing in a cryptographic standard). It is based on the indifferentiability framework of Ma<PERSON>, <PERSON>ner, and Holenstein (MRH), whose application has been exclusively focused upon non-interactive cryptographic primitives, e.g., hash functions and Feistel networks. Our extension of MRH is supported by a clearly defined execution model and two composition lemmata, all formalized in a modern pseudocode language. Together, these allow for precise statements about game-based security properties of cryptographic objects (interactive or not) at various levels of abstraction. As a real-world application, we design and prove tight security bounds for a potential TLS 1.3 extension that integrates the SPAKE2 password-authenticated key-exchange into the handshake.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_4"}, {"primary_key": "2561550", "vector": [], "sparse_vector": [], "title": "The MALICIOUS Framework: Embedding Backdoors into Tweakable Block Ciphers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Inserting backdoors in encryption algorithms has long seemed like a very interesting, yet difficult problem. Most attempts have been unsuccessful for symmetric-key primitives so far and it remains an open problem how to build such ciphers. In this work, we propose the MALICIOUS framework, a new method to build tweakable block ciphers that have backdoors hidden which allows to retrieve the secret key. Our backdoor is differential in nature: a specific related-tweak differential path with high probability is hidden during the design phase of the cipher. We explain how any entity knowing the backdoor can practically recover the secret key of a user and we also argue why even knowing the presence of the backdoor and the workings of the cipher will not permit to retrieve the backdoor for an external user. We analyze the security of our construction in the classical black-box model and we show that retrieving the backdoor (the hidden high-probability differential path) is very difficult. We instantiate our framework by proposing theLowMC-Mconstruction, a new family of tweakable block ciphers based on instances of theLowMCcipher, which allow such backdoor embedding. GeneratingLowMC-Minstances is trivial and theLowMC-Mfamily has basically the same efficiency as theLowMCinstances it is based on.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_9"}, {"primary_key": "2561551", "vector": [], "sparse_vector": [], "title": "Generically Speeding-Up Repeated Squaring Is Equivalent to Factoring: <PERSON> Thresholds for All Generic-Ring Delay Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Despite the fundamental importance of delay functions, repeated squaring in RSA groups (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>) is the only candidate offering both a useful structure and a realistic level of practicality. Somewhat unsatisfyingly, its sequentiality is provided directly by assumption (i.e., the function is assumed to be a delay function). We prove sharp thresholds on the sequentiality of all generic-ring delay functions relative to an RSA modulus based on the hardness of factoring in the standard model. In particular, we show that generically speeding-up repeated squaring (even with a preprocessing stage and any polynomial number parallel processors) is equivalent to factoring. More generally, based on the (essential) hardness of factoring, we prove that any generic-ring function is in fact a delay function, admitting a sharp sequentiality threshold that is determined by our notion ofsequentiality depth. Moreover, we show that generic-ring functions admit not only sharp sequentiality thresholds, but also sharp pseudorandomness thresholds.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_17"}, {"primary_key": "2561552", "vector": [], "sparse_vector": [], "title": "Spartan: Efficient and General-Purpose zkSNARKs Without Trusted Setup.", "authors": ["<PERSON><PERSON>"], "summary": "This paper introduces Spartan, a new family of zero-knowledge succinct non-interactive arguments of knowledge (zkSNARKs) for the rank-1 constraint satisfiability (R1CS), an NP-complete language that generalizes arithmetic circuit satisfiability. A distinctive feature of <PERSON> is that it offers the first zkSNARKs without trusted setup (i.e., transparent zkSNARKs) for NP where verifying a proof incurs sub-linear costs—without requiring uniformity in the NP statement’s structure. Furthermore, <PERSON> offers zkSNARKs with a time-optimal prover, a property that has remained elusive for nearly all zkSNARKs in the literature. To achieve these results, we introduce new techniques that we compose with the sum-check protocol, a seminal interactive proof protocol: (1)computation commitments, a primitive to create a succinct commitment to a description of a computation; this technique is crucial for a verifier to achieve sub-linear costs after investing a one-time, public computation to preprocess a given NP statement; (2)spark, a cryptographic compiler to transform any existing extractable polynomial commitment scheme for multilinear polynomials to one that efficiently handlessparsemultilinear polynomials; this technique is critical for achieving a time-optimal prover; and (3) a compact encoding of an R1CS instance as a low-degree polynomial. The end result is a public-coin succinct interactive argument of knowledge for NP (which can be viewed asa succinct variant of the sum-check protocol); we transform it into a zkSNARK using prior techniques. By applyingsparkto different commitment schemes, we obtain several zkSNARKs where the verifier’s costs and the proof size range from\\(O(\\log ^2{n})\\)to\\(O(\\sqrt{n})\\)depending on the underlying commitment scheme (ndenotes the size of the NP statement). These schemes do not require a trusted setup except for one that requires a universal trusted setup. We implement Spartan as a library in about 8,000 lines of Rust. We use the library to build a transparent zkSNARK in the random oracle model where security holds under the discrete logarithm assumption. We experimentally evaluate it and compare with recent zkSNARKs for R1CS instance sizes up to\\(2^{20}\\)constraints. Among schemes without trusted setup, Spartan offers the fastest prover with speedups of 36–\\(152{\\times }\\)depending on the baseline, produces proofs that are shorter by 1.2–416\\({\\times }\\), and incurs the lowest verification times with speedups of 3.6–1326\\({\\times }\\). When compared to the state-of-the-art zkSNARK with trusted setup, Spartan ’s prover is\\(2{\\times }\\)faster for arbitrary R1CS instances and\\(16{\\times }\\)faster for data-parallel workloads.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56877-1_25"}, {"primary_key": "2561553", "vector": [], "sparse_vector": [], "title": "Collusion Resistant Watermarkable PRFs from Standard Assumptions.", "authors": ["Rupeng <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A software watermarking scheme can embed a message into a program without significantly changing its functionality. Moreover, any attempt to remove the embedded message in a marked program will substantially change the functionality of the program. Prior constructions of watermarking schemes focus on watermarking cryptographic functions, such as pseudorandom function (PRF), public key encryption, etc. A natural security requirement for watermarking schemes is collusion resistance, where the adversary’s goal is to remove the embedded messages given multiple marked versions of the same program. Currently, this strong security guarantee has been achieved by watermarking schemes for public key cryptographic primitives from standard assumptions (<PERSON><PERSON> et al., CRYPTO 2019) and by watermarking schemes for PRFs from indistinguishability obfuscation (<PERSON> et al., ASIACRYPT 2019). However, no collusion resistant watermarking scheme for PRF from standard assumption is known. In this work, we solve this problem by presenting a generic construction that upgrades a watermarkable PRF without collusion resistance to a collusion resistant one. One appealing feature of our construction is that it can preserve the security properties of the original scheme. For example, if the original scheme has security with extraction queries, the new scheme is also secure with extraction queries. Besides, the new scheme can achieve unforgeability even if the original scheme does not provide this security property. Instantiating our construction with existing watermarking schemes for PRF, we obtain collusion resistant watermarkable PRFs from standard assumptions, offering various security properties.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_20"}, {"primary_key": "2561554", "vector": [], "sparse_vector": [], "title": "Indifferentiability for Public Key Cryptosystems.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We initiate the study of indifferentiability for public key encryption and other public key primitives. Our main results are definitions and constructions of public key cryptosystems that are indifferentiable from ideal cryptosystems, in the random oracle model. Cryptosystems include: Public key encryption; Digital signatures; Non-interactive key agreement. Our schemes are based on relatively standard public key assumptions. By being indifferentiable from an ideal object, our schemes automatically satisfy a wide range of security properties, including any property representable as a single-stage game, and can be composed to operate in higher-level protocols.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_3"}, {"primary_key": "2561555", "vector": [], "sparse_vector": [], "title": "New Techniques for Traitor Tracing: Size N1/3 and More from Pairings.", "authors": ["<PERSON>"], "summary": "The best existing pairing-based traitor tracing schemes have\\(O(\\sqrt{N})\\)-sized parameters, which has stood since 2006. This intuitively seems to be consistent with the fact that pairings allow for degree-2 computations, yielding a quadratic compression. In this work, we show that this intuition is false by building a traitor tracing scheme from pairings with\\(O(\\root 3 \\of {N})\\)-sized parameters. We additionally give schemes with a variety of parameter size trade-offs, including a scheme with constant-size ciphertexts and public keys (but linear-sized secret keys). We obtain our schemes by developing a number of new traitor tracing techniques, giving the first significant parameter improvements in pairings-based traitor tracing in over a decade.", "published": "2020-01-01", "category": "crypto", "pdf_url": "", "sub_summary": "", "source": "crypto", "doi": "10.1007/978-3-030-56784-2_22"}]