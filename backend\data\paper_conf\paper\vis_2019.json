[{"primary_key": "3127758", "vector": [], "sparse_vector": [], "title": "VisWall: Visual Data Exploration Using Direct Combination on Large Touch Displays.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "An increasing number of data visualization tools are being designed for touch-based devices ranging from smartwatches to large wall-sized displays. While most of these tools have focused on exploring novel techniques to manually specify visualizations, recent touch-based visualization systems have begun to explore interface and interaction techniques for attribute-based visualization recommendations as a way to aid users (particularly novices) during data exploration. Advancing this line of work, we present a visualization system, VisWall, that enables visual data exploration in both single user and co-located collaborative settings on large touch displays. Coupling the concepts of direct combination and derivable visualizations, VisWall enables rapid construction of multivariate visualizations using attributes of previously created visualizations. By blending visualization recommendations and naturalistic interactions, VisWall seeks to help users visually explore their data by allowing them to focus more on aspects of the data (particularly, data attributes) rather than specifying and reconfiguring visualizations. We discuss the design, interaction techniques, and operations employed by VisWall along with a scenario of how these can be used to facilitate various tasks during visual data exploration.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933673"}, {"primary_key": "3127782", "vector": [], "sparse_vector": [], "title": "Learning Vis Tools: Teaching Data Visualization Tutorials.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Teaching and advocating data visualization are among the most important activities in the visualization community. With growing interest in data analysis from business and science professionals, data visualization courses attract students across different disciplines. However, comprehensive visualization training requires students to have a certain level of proficiency in programming, a requirement that imposes challenges on both teachers and students. With recent developments in visualization tools, we have managed to overcome these obstacles by teaching a wide range of visualization and supporting tools. Starting with GUI-based visualization tools and data analysis with Python, students put visualization knowledge into practice with increasing amounts of programming. At the end of the course, students can design and implement visualizations with D3 and other programming-based visualization tools. Throughout the course, we continuously collect student feedback and refine the teaching materials. This paper documents our teaching methods and considerations when designing the teaching materials.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933751"}, {"primary_key": "3127755", "vector": [], "sparse_vector": [], "title": "MissBiN: Visual Analysis of Missing Links in Bipartite Networks.", "authors": ["<PERSON><PERSON>", "Maoyuan Sun", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The analysis of bipartite networks is critical in a variety of application domains, such as exploring entity co-occurrences in intelligence analysis and investigating gene expression in bio-informatics. One important task is missing link prediction, which infers the existence of unseen links based on currently observed ones. In this paper, we propose MissBiN that involves analysts in the loop for making sense of link prediction results. MissBiN combines a novel method for link prediction and an interactive visualization for examining and understanding the algorithm outputs. Further, we conducted quantitative experiments to assess the performance of the proposed link prediction algorithm and a case study to evaluate the overall effectiveness of MissBiN.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933639"}, {"primary_key": "3127756", "vector": [], "sparse_vector": [], "title": "Nonuniform Timeslicing of Dynamic Graphs Based on Visual Complexity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Uniform timeslicing of dynamic graphs has been used due to its convenience and uniformity across the time dimension. However, uniform timeslicing does not take the data set into account, which can generate cluttered timeslices with edge bursts and empty times-lices with few interactions. The graph mining filed has explored nonuniform timeslicing methods specifically designed to preserve graph features for mining tasks. In this paper, we propose a nonuniform timeslicing approach for dynamic graph visualization. Our goal is to create timeslices of equal visual complexity. To this end, we adapt histogram equalization to create timeslices with a similar number of events, balancing the visual complexity across timeslices and conveying more important details of timeslices with bursting edges. A case study has been conducted, in comparison with uniform timeslicing, to demonstrate the effectiveness of our approach.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933748"}, {"primary_key": "3127759", "vector": [], "sparse_vector": [], "title": "Towards Quantifying Multiple View Layouts in Visualisation as Seen from Research Publications.", "authors": ["<PERSON>der Al-Maneea", "<PERSON>"], "summary": "We present initial results of a quantitative analysis of how developers layout the visualisations in their multiple view systems. Many developers create multiple view systems and the technique is commonly used by the visualisation community. Each visualisation shows data in a different way, and often user interaction is coordinated between the views. But it is not always clear to know how many views a developer should use, or what would be the best layout. We extract images of visualisation tools, across TVCG journal, conference, posters and workshop papers 2012-2018 to analyse the quantity and layout of the views in these visualisation systems. Focusing on view juxtaposition, we code the layout of 491 images and analyse view topology in juxtaposed views. Our analysis acts as a starting point to help designers create better visualisations, acts as a taxonomy of visualisation layouts, and provides a quantitative analysis of how many views developers have used in their visualisation systems.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933655"}, {"primary_key": "3127761", "vector": [], "sparse_vector": [], "title": "Sabrina: Modeling and Visualization of Financial Data over Time with Incremental Domain Knowledge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Investment planning requires knowledge of the financial landscape on a large scale, both in terms of geo-spatial and industry sector distribution. There is plenty of data available, but it is scattered across heterogeneous sources (newspapers, open data, etc.), which makes it difficult for financial analysts to understand the big picture. In this paper, we present <PERSON>, a financial data analysis and visualization approach that incorporates a pipeline for the generation of firm-to-firm financial transaction networks. The pipeline is capable of fusing the ground truth on individual firms in a region with (incremental) domain knowledge on general macroscopic aspects of the economy. <PERSON> unites these heterogeneous data sources within a uniform visual interface that enables the visual analysis process. In a user study with three domain experts, we illustrate the usefulness of <PERSON>, which eases their analysis process.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933598"}, {"primary_key": "3127762", "vector": [], "sparse_vector": [], "title": "Toward Perception-Based Evaluation of Clustering Techniques for Visual Analytics.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Abdelkader Baggag", "<PERSON><PERSON>"], "summary": "Automatic clustering techniques play a central role in Visual Analytics by helping analysts to discover interesting patterns in high-dimensional data. Evaluating these clustering techniques, however, is difficult due to the lack of universal ground truth. Instead, clustering approaches are usually evaluated based on a subjective visual judgment of low-dimensional scatterplots of different datasets. As clustering is an inherent human-in-the-loop task, we propose a more systematic way of evaluating clustering algorithms based on quantification of human perception of clusters in 2D scatterplots. The core question we are asking is in how far existing clustering techniques align with clusters perceived by humans. To do so, we build on a dataset from a previous study [1], in which 34 human subjects la-beled 1000 synthetic scatterplots in terms of whether they could see one or more than one cluster. Here, we use this dataset to benchmark state-of-the-art clustering techniques in terms of how far they agree with these human judgments. More specifically, we assess 1437 variants of K-means, Gaussian Mixture Models, CLIQUE, DBSCAN, and Agglomerative Clustering techniques on these benchmarks data. We get unexpected results. For instance, CLIQUE and DBSCAN are at best in slight agreement on this basic cluster counting task, while model-agnostic Agglomerative clustering can be up to a substantial agreement with human subjects depending on the variants. We discuss how to extend this perception-based clustering benchmark approach, and how it could lead to the design of perception-based clustering techniques that would better support more trustworthy and explainable models of cluster patterns.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933620"}, {"primary_key": "3127765", "vector": [], "sparse_vector": [], "title": "Point Movement in a DSL for Higher-Order FEM Visualization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Scientific visualization tools tend to be flexible in some ways (e.g., for exploring isovalues) while restricted in other ways, such as working only on regular grids, or only on unstructured meshes (as used in the finite element method, FEM). Our work seeks to expose the common structure of visualization methods, apart from the specifics of how the fields being visualized are formed. Recognizing that previous approaches to FEM visualization depend on efficiently updating computed positions within a mesh, we took an existing visualization domain-specific language, and added a mesh position type and associated arithmetic operators. These are orthogonal to the visualization method itself, so existing programs for visualizing regular grid data work, with minimal changes, on higher-order FEM data. We reproduce the efficiency gains of an earlier guided search method of mesh position update for computing streamlines, and we demonstrate a novel ability to uniformly sample ridge surfaces of higher-order FEM solutions defined on curved meshes.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933623"}, {"primary_key": "3127767", "vector": [], "sparse_vector": [], "title": "Visualization Assessment: A Machine Learning Approach.", "authors": ["<PERSON>n <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Haidong Zhang"], "summary": "Researchers assess visualizations from multiple aspects, such as aesthetics, memorability, engagement, and efficiency. However, these assessments are mostly carried out through user studies. There is a lack of automatic visualization assessment approaches, which hinders further applications like visualization recommendation, indexing, and generation. In this paper, we propose automating the visualization assessment process with modern machine learning approaches. We utilize a semi-supervised learning method, which first employs Variational Autoencoder (VAE) to learn effective features from visualizations, subsequently training machine learning models for different assessment tasks. Then, we can automatically assess new visualization images by predicting their scores or rankings with the trained model. To evaluate our method, we run two different assessment tasks, namely, aesthetics and memorability, on different visualization datasets. Experiments show that our method can learn effective visual features and achieves good performance on these assessment tasks.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933570"}, {"primary_key": "3127769", "vector": [], "sparse_vector": [], "title": "Graph-Assisted Visualization of Microvascular Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Microvessels are frequent targets for research into tissue development and disease progression. These complex and subtle differences between networks are currently difficult to visualize, making sample comparisons subjective and difficult to quantify. These challenges are due to the structure of microvascular networks, which are sparse but space-filling. This results in a complex and interconnected mesh that is difficult to represent and impractical to interpret using conventional visualization techniques. We develop a bi-modal visualization framework, leveraging graph-based and geometry-based techniques to achieve interactive visualization of microvascular networks. This framework allows researchers to objectively interpret the complex and subtle variations that arise when comparing microvascular networks.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933682"}, {"primary_key": "3127770", "vector": [], "sparse_vector": [], "title": "scenery: Flexible Virtual Reality Visualization on the Java VM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The following topics are dealt with: data visualisation; data analysis; interactive systems; learning (artificial intelligence); pattern clustering; rendering (computer graphics); medical image processing; graph theory; neurophysiology; trees (mathematics).", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933605"}, {"primary_key": "3127771", "vector": [], "sparse_vector": [], "title": "Visual Analysis of the Time Management of Learning Multiple Courses in Online Learning Environment.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Self-paced online learning not only provides the opportunities of learning anytime but also chanllenges students' time management, especially in the context of learning multiple courses at same time. The inappropriate scheduling of multiple courses may affect student engagement and learning performance, thus how to arrange the study time of multiple courses is a concern of both instructors and students. Existing studies related to student engagement and time management in online learning mainly focus on providing self-regulated learning strategies and evaluating learning performance. However, these methods have limited abilities to gain intuitive understanding of the time management of multi-course learning. To address this issue, we present LearnerVis to help users analyze how students schedule their multi-course learning. LearnerVis visualize the temporal features of learning process, and it enables users to customize student groups to compare the differences in student engagement and time management. A case study is conducted to demonstrate the usefulness of the system with real-word dataset.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933778"}, {"primary_key": "3127772", "vector": [], "sparse_vector": [], "title": "Would You Like A Chart With That? Incorporating Visualizations into Conversational Interfaces.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Conversational interfaces, such as chatbots, are increasing in prevalence, and have been shown to be preferred by and help users to complete tasks more efficiently than standard web interfaces in some cases. However, little is understood about if and how information should be visualized during the course of an interactive conversation. This paper describes studies in which participants report their preferences for viewing visualizations in chat-style interfaces when answering questions about comparisons and trends. We find a significant split in preferences among participants; approximately 40% prefer not to see charts and graphs in the context of a conversational interface. For those who do prefer to see charts, most preferred to see additional supporting context beyond the direct answer to the question. These results have important ramifications for the design of conversational interfaces to data.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933766"}, {"primary_key": "3127773", "vector": [], "sparse_vector": [], "title": "Toward Interface Defaults for Vague Modifiers in Natural Language Interfaces for Visual Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Natural language interfaces for data visualizations tools are growing in importance, but little research has been done on how a system should respond to questions that contain vague modifiers like \"high\" and \"expensive.\" This paper makes a first step toward design guidelines for this problem, based on existing research from cognitive linguistics and the results of a new empirical study with 274 crowdsourcing participants. A comparison of four bar chart-based views finds that highlighting the top items according to distribution-sensitive values is preferred in most cases and is a good starting point as a design guideline.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933569"}, {"primary_key": "3127774", "vector": [], "sparse_vector": [], "title": "Visualization of Symmetries in Fourth-Order Stiffness Tensors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many materials like wood, biological tissue, composites or rock have anisotropic mechanical properties. They become increasingly important in modern material, earth, and life sciences. The stress-strain response of such materials can be characterized (to first-order) by the three-dimensional fourth-order stiffness tensor. There are different anisotropy classes, i.e. material symmetries, that differ in the number and orientation of symmetry planes characteristic of the material. A three-dimensional fourth-order stiffness tensor of a hyperelastic material has up to 21 independent coefficients representing both moduli and orientation information which challenges any visualization method. Therefore, we use a fourth-order tensor decomposition to compute the anisotropy classes and the position of the corresponding symmetry planes. To facilitate judgment of the significance of the amount of anisotropy, we construct an isotropic material. Based on these computations, we design a glyph that represents the stiffness tensor. We demonstrate our method in a finite deformation setting of an initially isotropic hyperelastic material of Ogden class which is often modeling biological tissue. Upon deformation, the stiffness tensor can evolve along with its symmetry creating an inhomogeneous, unsteady fourth-order tensor field in three dimensions.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933592"}, {"primary_key": "3127775", "vector": [], "sparse_vector": [], "title": "TeleGam: Combining Visualization and Verbalization for Interpretable Machine Learning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While machine learning (ML) continues to find success in solving previously-thought hard problems, interpreting and exploring ML models remains challenging. Recent work has shown that visualizations are a powerful tool to aid debugging, analyzing, and interpreting ML models. However, depending on the complexity of the model (e.g., number of features), interpreting these visualizations can be difficult and may require additional expertise. Alternatively, textual descriptions, or verbalizations, can be a simple, yet effective way to communicate or summarize key aspects about a model, such as the overall trend in a model's predictions or comparisons between pairs of data instances. With the potential benefits of visualizations and verbalizations in mind, we explore how the two can be combined to aid ML interpretability. Specifically, we present a prototype system, TeleGam, that demonstrates how visualizations and verbalizations can collectively support interactive exploration of ML models, for example, generalized additive models (GAMs). We describe TELEGAM's interface and underlying heuristics to generate the verbalizations. We conclude by discussing how TeleGam can serve as a platform to conduct future studies for understanding user expectations and designing novel interfaces for interpretable ML.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933695"}, {"primary_key": "3127776", "vector": [], "sparse_vector": [], "title": "Visual Inspection of DBS Efficacy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "At present, approximately ten million people worldwide are afflicted by Parkinson's Disease (PD). One of the most promising therapies for PD is Deep Brain Stimulation (DBS). DBS works via stimulation of targeted central brain regions (nuclei), whose dysfunction is implicated in PD. A key problem with DBS is determining optimal parameters for clinical outcome. While multiple parameters may influence outcomes in DBS, we explore spatial correlation of volume of tissue activated (VTA) to Unified Parkinson's Disease Rating Scale (UPDRS) scores. Using the Neurostimulation Uncertainty Viewer (nuView), we investigate a number of cooperative visualizations for DBS inspection. Surface-to-surface Euclidean distance between VTA and selected brain nuclei are used in a linked 3D and parallel coordinates view of patient outcome. We then present a semivariogram-based approach to measure spatial correlation of patient outcomes with VTA. As a third component, nuView provides a unique visualization of an ensemble of electrode placements to reduce clutter and emphasize electrodes with spatially similar VTA. These methods corroborate a spatial aspect to DBS efficacy.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933720"}, {"primary_key": "3127778", "vector": [], "sparse_vector": [], "title": "High Fidelity Visualization of Large Scale Digitally Reconstructed Brain Circuitry with Signed Distance Functions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We explore a first proof-of-concept application for visualizing large scale digitally reconstructed brain circuitry using signed distance functions. The significance of our method is demonstrated in comparison with using implicit geometry that is limited to provide the natural look of neurons or explicit geometry that requires huge amounts of memory and has limited scalability with larger circuits.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933693"}, {"primary_key": "3127780", "vector": [], "sparse_vector": [], "title": "Evidence for Area as the Primary Visual Cue in Pie Charts.", "authors": ["<PERSON>"], "summary": "The long-standing assumption of angle as the primary visual cue used to read pie charts has recently been called into question. We conducted a controlled, preregistered study using parallel-projected 3D pie charts. Angle, area, and arc length differ dramatically when projected and change over a large range of values. Modeling these changes and comparing them to study participants' estimates allows us to rank the different visual cues by model fit. Area emerges as the most likely cue used to read pie charts.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933547"}, {"primary_key": "3127781", "vector": [], "sparse_vector": [], "title": "ElectroLens: Understanding Atomistic Simulations through Spatially-Resolved Visualization of High-Dimensional Features.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> (Polo) Chau", "<PERSON>"], "summary": "In recent years, machine learning (ML) has gained significant popularity in the field of chemical informatics and electronic structure theory. These techniques often require researchers to engineer abstract \"features\" that encode chemical concepts into a mathematical form compatible with the input to machine-learning models. However, there is no existing tool to connect these abstract features back to the actual chemical system, making it difficult to diagnose failures and to build intuition about the meaning of the features. We present ElectroLens, a new visualization tool for high-dimensional spatially-resolved features to tackle this problem. The tool visualizes high-dimensional data sets for atomistic and electron environment features by a series of linked 3D views and 2D plots. The tool is able to connect different derived features and their corresponding regions in 3D via interactive selection. It is built to be scalable, and integrate with existing infrastructure.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933647"}, {"primary_key": "3127783", "vector": [], "sparse_vector": [], "title": "Sociotechnical Considerations for Accessible Visualization Design.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Accessibility-the process of designing for people with disabilities (PWD)-is an important but under-explored challenge in the visualization research community. Without careful attention, and if PWD are not included as equal participants throughout the process, there is a danger of perpetuating a vision-first approach to accessible design that marginalizes the lived experience of disability (e.g., by creating overly simplistic \"sensory translations\" that map visual to non-visual modalities in a one-to-one fashion). In this paper, we present a set of sociotechnical considerations for research in accessible visualization design, drawing on literature in disability studies, tactile information systems, and participatory methods. We identify that using state-of-the-art technologies may introduce more barriers to access than they remove, and that expectations of research novelty may not produce outcomes well-aligned with the needs of disability communities. Instead, to promote a more inclusive design process, we emphasize the importance of clearly communicating goals, following existing accessibility guidelines, and treating PWD as equal participants who are compensated for their specialized skills. To illustrate how these considerations can be applied in practice, we discuss a case study of an inclusive design workshop held in collaboration with the Perkins School for the Blind.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933762"}, {"primary_key": "3127789", "vector": [], "sparse_vector": [], "title": "Overlap-Free Drawing of Generalized Pythagoras Trees for Hierarchy Visualization.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Generalized Pythagoras trees were developed for visualizing hierarchical data, producing organic, fractal-like representations. However, the drawback of the original layout algorithm is visual overlap of tree branches. To avoid such overlap, we introduce an adapted drawing algorithm using ellipses instead of circles to recursively place tree nodes representing the subhierarchies. Our technique is demonstrated by resolving overlap in diverse real-world and generated datasets, while comparing the results to the original approach.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933606"}, {"primary_key": "3127790", "vector": [], "sparse_vector": [], "title": "Unsteady Flow Visualization via Physics Based Pathline Exploration.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work proposes to analyze the time-dependent characteristics of the physical attributes measured along pathlines derived from unsteady flows, which can be represented as a series of time activity curves (TAC). A new TAC-based unsteady flow visualization and analysis framework is proposed. The center of this framework is a new event-based distance metric (EDM) that compares the similarity of two TACs, from which a new spatio-temporal, hierarchical clustering of pathlines based on their physical attributes and an attribute-based pathline exploration are proposed. These techniques are integrated into a visual analytics system, which has been applied to a number of unsteady flow in 2D and 3D to demonstrate its utility.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933578"}, {"primary_key": "3127791", "vector": [], "sparse_vector": [], "title": "SANVis: Visual Analytics for Understanding Self-Attention Networks.", "authors": ["Cheonbok Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Na", "<PERSON><PERSON><PERSON>", "Sungbok Shin", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Attention networks, a deep neural network architecture inspired by humans' attention mechanism, have seen significant success in image captioning, machine translation, and many other applications. Recently, they have been further evolved into an advanced approach called multi-head self-attention networks, which can encode a set of input vectors, e.g., word vectors in a sentence, into another set of vectors. Such encoding aims at simultaneously capturing diverse syntactic and semantic features within a set, each of which corresponds to a particular attention head, forming altogether multi-head attention. Meanwhile, the increased model complexity prevents users from easily understanding and manipulating the inner workings of models. To tackle the challenges, we present a visual analytics system called SANVis, which helps users understand the behaviors and the characteristics of multi-head self-attention networks. Using a state-of-the-art self-attention model called Transformer, we demonstrate usage scenarios of SANVis in machine translation tasks. Our system is available at http://short.sanvis.org.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933677"}, {"primary_key": "3127796", "vector": [], "sparse_vector": [], "title": "Visual Cues in Estimation of Part-To-Whole Comparisons.", "authors": ["<PERSON>"], "summary": "Pie charts were first published in 1801 by <PERSON> and have caused some controversy since. Despite the suggestions of many experts against their use, several empirical studies have shown that pie charts are at least as good as alternatives. From Brinton to Few on one side and <PERSON><PERSON><PERSON> to Kosara on the other, there appears to have been a hundred-year war waged on the humble pie. In this paper a set of experiments are reported that compare the performance of pie charts and horizontal bar charts with various visual cues. Amazon's Mechanical Turk service was employed to perform the tasks of estimating segments in various part-to-whole charts. The results lead to recommendations for data visualization professionals in developing dashboards.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933718"}, {"primary_key": "3127797", "vector": [], "sparse_vector": [], "title": "Hi-D Maps: An Interactive Visualization Technique for Multi-Dimensional Categorical Data.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present Hi-D maps, a novel method for the visualization of multi-dimensional categorical data. Our work addresses the scarcity of techniques for visualizing a large number of data-dimensions in an effective and space-efficient manner. We have mapped the full data-space onto a 2D regular polygonal region. The polygon is cut hierarchically with lines parallel to a user-controlled, ordered sequence of sides, each representing a dimension. We have used multiple visual cues such as orientation, thickness, color, countable glyphs, and text to depict cross-dimensional information. We have added interactivity and hierarchical browsing to facilitate flexible exploration of the display: small areas can be scrutinized for details. Thus, our method is also easily extendable to visualize hierarchical information. Our glyph animations add an engaging aesthetic during interaction. Like many visualizations, Hi-D maps become less effective when a large number of dimensions stresses perceptual limits, but Hi-D maps may add clarity before those limits are reached.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933709"}, {"primary_key": "3127799", "vector": [], "sparse_vector": [], "title": "Visualizing RNN States with Predictive Semantic Encodings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recurrent Neural Networks are an effective and prevalent tool used to model sequential data such as natural language text. However, their deep nature and massive number of parameters pose a challenge for those intending to study precisely how they work. We present a visual technique that gives a high level intuition behind the semantics of the hidden states within Recurrent Neural Networks. This semantic encoding allows for hidden states to be compared throughout the model independent of their internal details. The proposed technique is displayed in a proof of concept visualization tool which is demonstrated to visualize the natural language processing task of language modelling.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933744"}, {"primary_key": "3127800", "vector": [], "sparse_vector": [], "title": "EasyPZ.js: Interaction Binding for Pan and Zoom Visualizations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The creation of data visualizations has become easier as the skillbarrier to our tools has decreased. However, adding interactivity, such as gestures for pan and zoom, still requires significant coding expertise. We introduce an open-source library-EasyPZ.js-for the creation of multi-scale (pan and zoom) visualizations across desktop and mobile devices. EasyPZ is fully customizable and extendable with flexible options for interaction design. For example, it is easy to choose gestures which are compatible with selection interactions such as clicking. EasyPZ can be enabled on any SVG-based visualization on the web with one line of code, or by simply clicking a bookmark without requiring commitment to code changes. With this library, we contribute ways for the visualization community to more easily author interactive multi-scale visualizations.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933747"}, {"primary_key": "3127801", "vector": [], "sparse_vector": [], "title": "H-Matrix: Hierarchical Matrix for Visual Analysis of Cross-Linguistic Features in Large Learner Corpora.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a visualization technique for cross-linguistic error analysis in large learner corpora. H-Matrix combines a matrix, which is commonly used by linguists to investigate cross-linguistic patterns, with a tree diagram to aggregate and interactively re-weight the importance of matrix rows to create custom investigative views. Our technique can help experts to perform data operations, such as, feature aggregation, filtering, ordering and language comparison interactively without having to reprocess the data. H-Matrix dynamically links the high-level multi-language overview to the extracted textual examples, and a reading view where linguists can see the detected features in context, confirm and generate hypotheses.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933537"}, {"primary_key": "3127802", "vector": [], "sparse_vector": [], "title": "Designing Visual Guides for Casual Listeners of Live Orchestral Music.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The experience of attending live orchestra performances is rich in cultural heritage and can be emotionally moving; however, for those unfamiliar with classical music, it can be intimidating. In this work, we explore the use of visual listening guides to supplement live performances with information that supports the casual listener's increased engagement. We employ human-centred design practices to evaluate a currently implemented guide with users, from which we extracted design requirements. We then identify dimensions of a music piece that may be visualized and created sample guide designs. Finally, we presented these designs to experts of visualization and music theory. Feedback from the two evaluations informs design implications to consider when creating visual guides of classical music for casual listeners.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933734"}, {"primary_key": "3127805", "vector": [], "sparse_vector": [], "title": "Toward a Design Space for Mitigating Cognitive Bias in Vis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The use of cognitive heuristics often leads to fast and effective decisions. However, they can also systematically and predictably lead to errors known as cognitive biases. Strategies for minimizing or mitigating these biases, however, remain largely non-technological (e.g., training courses). The growing use of visual analytic (VA) tools for analysis and decision making enables a new class of bias mitigation strategies. In this work, we explore the ways in which the design of visualizations (vis) may be used to mitigate cognitive biases. We derive a design space comprised of 8 dimensions that can be manipulated to impact a user's cognitive and analytic processes and describe them through an example hiring scenario. This design space can be used to guide and inform future vis systems that may integrate cognitive processes more closely.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933611"}, {"primary_key": "3127808", "vector": [], "sparse_vector": [], "title": "Interactive Visualisation of Hierarchical Quantitative Data: An Evaluation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We have compared three common visualisations for hierarchical quantitative data, treemaps, icicle plots and sunburst charts as well as a semicircular variant of sunburst charts we call the sundown chart. In a pilot study, we found that the sunburst chart was least preferred. In a controlled study with 12 participants, we compared treemaps, icicle plots and sundown charts. Treemap was the least preferred and had a slower performance on a basic navigation task and slower performance and accuracy in hierarchy understanding tasks. The icicle plot and sundown chart had similar performance with slight user preference for the icicle plot.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933545"}, {"primary_key": "3127809", "vector": [], "sparse_vector": [], "title": "TempoCave: Visualizing Dynamic Connectome Datasets to Support Cognitive Behavioral Therapy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON>"], "summary": "We introduce TempoCave, a novel visualization application for analyzing dynamic brain networks, or connectomes. TempoCave provides a range of functionality to explore metrics related to the activity patterns and modular affiliations of different regions in the brain. These patterns are calculated by processing raw data retrieved functional magnetic resonance imaging (fMRI) scans, which creates a network of weighted edges between each brain region, where the weight indicates how likely these regions are to activate synchronously. TempoCave supports the analysis needs of clinical psychologists, who examine these modular affiliations and weighted edges and their temporal dynamics, utilizing them to understand relationships between neurological disorders and brain activity, which could have significant impact on how patients are diagnosed and treated. In addition to summarizing the main functionality of TempoCave, we present a real world use case that analyzes pre- and post-treatment connectome datasets from 27 subjects in a clinical study investigating the use of cognitive behavior therapy to treat major depression disorder, indicating that TempoCave can provide new insight into the dynamic behavior of the human brain.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933544"}, {"primary_key": "3127811", "vector": [], "sparse_vector": [], "title": "Data-Driven Colormap Optimization for 2D Scalar Field Visualization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Colormapping is an effective and popular visual representation to analyze data patterns for 2D scalar fields. Scientists usually adopt a default colormap and adjust it to fit data in a trial-and-error process. Even though a few colormap design rules and measures are proposed, there is no automatic algorithm to directly optimize a default colormap for better revealing spatial patterns hidden in unevenly distributed data, especially the boundary characteristics. To fill this gap, we conduct a pilot study with six domain experts and summarize three requirements for automated colormap adjustment. We formulate the colormap adjustment as a nonlinear constrained optimization problem, and develop an efficient GPU-based implementation accompanying with a few interactions. We demonstrate the usefulness of our method with two case studies.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933764"}, {"primary_key": "3127812", "vector": [], "sparse_vector": [], "title": "Evaluating Alignment Approaches in Superimposed Time-Series and Temporal Event-Sequence Visualizations.", "authors": ["<PERSON><PERSON><PERSON>", "Sara Di Bartolomeo", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Composite temporal event sequence visualizations have included sentinel event alignment techniques to cope with data volume and variety. Prior work has demonstrated the utility of using single-event alignment for understanding the precursor, co-occurring, and aftereffect events surrounding a sentinel event. However, the usefulness of single-event alignment has not been sufficiently evaluated in composite visualizations. Furthermore, recently proposed dual-event alignment techniques have not been empirically evaluated. In this work, we designed tasks around temporal event sequence and timing analysis and conducted a controlled experiment on Amazon Mechanical Turk to examine four sentinel event alignment approaches: no sentinel event alignment (NoAlign), single-event alignment (SingleAlign), dual-event alignment with left justification (DualLeft), and dual-event alignment with stretch justification (DualStretch). Differences between approaches were most pronounced with more rows of data. For understanding intermediate events between two sentinel events, dual-event alignment was the clear winner for correctness-71% vs. 18% for NoAlign and SingleAlign. For understanding the duration between two sentinel events, NoAlign was the clear winner: correctness-88% vs. 36% for DualStretch- completion time-55 seconds vs. 101 seconds for DualLeft-and error-1.5% vs. 8.4% for DualStretch. For understanding precursor and aftereffect events, there was no significant difference among approaches. A free copy of this paper, the evaluation stimuli and data, and source code are available at osf.io/78fs5", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933584"}, {"primary_key": "3127757", "vector": [], "sparse_vector": [], "title": "RuleVis: Constructing <PERSON><PERSON><PERSON> and Rules for Rule-Based Models.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Cassia <PERSON>gara", "<PERSON>", "<PERSON>", "<PERSON> <PERSON>"], "summary": "We introduce RuleVis, a web-based application for defining and editing \"correct-by-construction\" executable rules that model biochemical functionality, which can be used to simulate the behavior of protein-protein interaction networks and other complex systems. Rule-based models involve emergent effects based on the interactions between rules, which can vary considerably with regard to the scale of a model, requiring the user to inspect and edit individual rules. RuleVis bridges the graph rewriting and systems biology research communities by providing an external visual representation of salient patterns that experts can use to determine the appropriate level of detail for a particular modeling context. We describe the visualization and interaction features available in RuleVis and provide a detailed example demonstrating how RuleVis can be used to reason about intracellular interactions.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933596"}, {"primary_key": "3127760", "vector": [], "sparse_vector": [], "title": "Towards Enhancing RadViz Analysis and Interpretation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "RadViz plots are commonly used to represent multidimensional data because they use the familiar notion of 2D points for encoding data elements, displaying the original data dimensions that act as springs for setting the x andy coordinates. However, this intuitive approach implies several drawbacks and produces misleading visualizations that can confuse the user, even while analyzing a single data point. The paper attacks this problem following the well known idea of changing the order of the dimensions and introducing ancillary visualizations to mitigate some of RadViz drawbacks. In particular, the paper defines the notion of point optimal disposition of the dimensions for a single data point, generalizes this concept to a set of data points, and proposes effective heuristics for dealing with the intractable problem of exploring all the (n-1)!/2 dispositions of the dimensions along the RadViz circumference. Additional views, visual quality metrics, and a circular grid superimposed on the RadViz complement the attribute reordering strategy and provide abetter understanding of the actual plot of the data elements.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933775"}, {"primary_key": "3127763", "vector": [], "sparse_vector": [], "title": "Interactive Dendritic Spine Analysis Based on 3D Morphological Features.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Won-<PERSON>"], "summary": "Dendritic spines are submicron scale protrusions on neuronal dendrites that form the postsynaptic sites of excitatory neuronal inputs. The morphological changes of dendritic spines reflect alterations in physiological conditions and are further indicators of various neuropsychiatric conditions. However, due to the highly dynamic and heterogeneous nature of spines, accurate measurement and object analysis of spine morphology is a major challenge in neuroscience research. Here, we propose an interactive 3D dendritic spine analysis system that displays 3D rendering of spines and plots the high-dimensional features extracted from the 3D mesh of spines in three graph types (parallel coordinate plot, radar plot, and 2D scatter plot with t-Distributed Stochastic Neighbor Embedding). With this system, analysts can effectively explore and analyze the dendritic spine in a 3D manner with high-dimensional features. For the system, we have constructed a set of morphological high-dimensional features from the 3D mesh of dendritic spines.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933795"}, {"primary_key": "3127764", "vector": [], "sparse_vector": [], "title": "Interpreting Distortions in Dimensionality Reduction by Superimposing Neighbourhood Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To perform visual data exploration, many dimensionality reduction methods have been developed. These tools allow data analysts to represent multidimensional data in a 2D or 3D space, while preserving as much relevant information as possible. Yet, they cannot preserve all structures simultaneously and they induce some unavoidable distortions. Hence, many criteria have been introduced to evaluate a map's overall quality, mostly based on the preservation of neighbourhoods. Such global indicators are currently used to compare several maps, which helps to choose the most appropriate mapping method and its hyperparameters. However, those aggregated indicators tend to hide the local repartition of distortions. Thereby, they need to be supplemented by local evaluation to ensure correct interpretation of maps. In this paper, we describe a new method, called MING, for `Map Interpretation using Neighbourhood Graphs'. It offers a graphical interpretation of pairs of map quality indicators, as well as local evaluation of the distortions. This is done by displaying on the map the nearest neighbours graphs computed in the data space and in the embedding. Shared and unshared edges exhibit reliable and unreliable neighbourhood information conveyed by the mapping. By this mean, analysts may determine whether proximity (or remoteness) of points on the map faithfully represents similarity (or dissimilarity) of original data, within the meaning of a chosen map quality criteria. We apply this approach to two pairs of widespread indicators: precision/recall and trustworthiness/continuity, chosen for their wide use in the community, which will allow an easy handling by users.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933568"}, {"primary_key": "3127766", "vector": [], "sparse_vector": [], "title": "Uncovering Data Landscapes through Data Reconnaissance and Task Wrangling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Domain experts are inundated with new and heterogeneous types of data and require better and more specific types of data visualization systems to help them. In this paper, we consider the data landscape that domain experts seek to understand, namely the set of datasets that are either currently available or could be obtained. Experts need to understand this landscape to triage which data analysis projects might be viable, out of the many possible research questions that they could pursue. We identify data reconnaissance and task wrangling as processes that experts undertake to discover and identify sources of data that could be valuable for some specific analysis goal. These processes have thus far not been formally named or defined by the research community. We provide formal definitions of data reconnaissance and task wrangling and describe how they relate to the data landscape that domain experts must uncover. We propose a conceptual framework with a four-phase cycle of acquire, view, assess, and pursue that occurs within three distinct chronological stages, which we call fog and friction, informed data ideation, and demarcation of final data. Collectively, these four phases embedded within three temporal stages delineate an expert's progressively evolving understanding of the data landscape. We describe and provide concrete examples of these processes within the visualization community through an initial systematic analysis of previous design studies, identifying situations where there is evidence that they were at play. We also comment on the response of domain experts to this framework, and suggest design implications stemming from these processes to motivate future research directions. As technological changes will only keep adding unknown terrain to the data landscape, data reconnaissance and task wrangling are important processes that need to be more widely understood and supported by the data visualization tools. By articulating a concrete understanding of this challenge and its implications, our work impacts the design and evaluation of data visualization systems.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933542"}, {"primary_key": "3127768", "vector": [], "sparse_vector": [], "title": "OCTVis: Ontology-Based Comparison of Topic Models.", "authors": ["<PERSON><PERSON> Ge", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Evaluating topic modeling results requires communication between domain and NLP experts. OCTVis is a visual interface to compare the quality of two topic models when mapped against a domain ontology. Its design is based on detailed data and task models, and was tested in a case study in the healthcare domain.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933646"}, {"primary_key": "3127777", "vector": [], "sparse_vector": [], "title": "Disentangled Representation of Data Distributions in Scatterplots.", "authors": ["<PERSON><PERSON><PERSON>", "Jinwook Seo"], "summary": "We present a data-driven approach to obtain a disentangled and interpretable representation that can characterize bivariate data distributions of scatterplots. We first collect tabular datasets from the Web and build a training corpus consisting of over one million scatterplot images. Then, we train a state-of-the-art disentangling model, β-variational autoencoder, to derive a disentangled representation of the scatterplot images. The main output of this work is a list of 32 representative features that can capture the underlying structures of bivariate data distributions. Through latent traversals, we seek for high-level semantics of the features and compare them to previous human-derived concepts such as scagnostics measures. Finally, using the 32 features as an input, we build a simple neural network to predict the perceptual distances between scatterplots that were previously scored by human annotators. We found <PERSON>'s correlation coefficient between the predicted and perceptual distances was above 0.75, which indicates the effectiveness of our representation in the quantitative characterization of scatterplots.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933670"}, {"primary_key": "3127779", "vector": [], "sparse_vector": [], "title": "Thumbnails for Data Stories: A Survey of Current Practices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "When people browse online news, small thumbnail images accompanying links to articles attract their attention and help them to decide which articles to read. As an increasing proportion of online news can be construed as data journalism, we have witnessed a corresponding increase in the incorporation of visualization in article thumbnails. However, there is little research to support alternative design choices for visualization thumbnails, which include resizing, cropping, simplifying, and embellishing charts appearing within the body of the associated article. We therefore sought to better understand these design choices and determine what makes a visualization thumbnail inviting and interpretable. This paper presents our findings from a survey of visualization thumbnails collected online and from conversations with data journalists and news graphics designers. Our study reveals that there exists an uncharted design space, one that is in need of further empirical study. Our work can thus be seen as a first step toward providing structured guidance on how to design thumbnails for data stories.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933773"}, {"primary_key": "3127784", "vector": [], "sparse_vector": [], "title": "Analyzing Time Attributes in Temporal Event Sequences.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Maria-Cruz Villa-Uriol"], "summary": "Event data is present in a variety of domains such as electronic health records, daily living activities and web clickstream records. Current visualization methods to explore event data focus on discovering sequential patterns but present limitations when studying time attributes in event sequences. Time attributes are especially important when studying waiting times or lengths of visit in patient flow analysis. We propose a visual analytics methodology that allows the identification of trends and outliers in respect of duration and time of occurrence in event sequences. The proposed method presents event data using a single Sequential and Time Patterns overview. User-driven alignment by multiple events, sorting by sequence similarity and a novel visual encoding of events allows the comparison of time trends across and within sequences. The proposed visualization allows the derivation of findings that otherwise could not be obtained using traditional visualizations. The proposed methodology has been applied to a real-world dataset provided by Sheffield Teaching Hospitals NHS Foundation Trust, for which four classes of conclusions were derived.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933770"}, {"primary_key": "3127785", "vector": [], "sparse_vector": [], "title": "GalStamps: Analyzing Real and Simulated Galaxy Observations.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "One way astronomers and astrophysicists study galaxy formation and evolution is by analyzing and comparing real galaxy observations, captured by telescopes, and simulated galaxy observations, generated from theoretical models. They approach this through a combination of statistical and visual analysis, conducted either independently or sequentially. During the first year of an ongoing design study with astronomers and astrophysicists, we explored approaches to integrating statistical and visual analysis to enhance understanding of these data. Contributions from this stage of the study include a data and task abstraction for statistically and visually analyzing real and simulated galaxy observations, as well as an initial design, implemented in a prototype called GalStamps, and evaluated through two case studies with domain experts.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933671"}, {"primary_key": "3127786", "vector": [], "sparse_vector": [], "title": "Evaluating Ordering Strategies of Star Glyph Axes.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Star glyphs are a well-researched visualization technique to represent multi-dimensional data. They are often used in small multiple settings for a visual comparison of many data points. However, their overall visual appearance is strongly influenced by the ordering of dimensions. To this end, two orthogonal categories of layout strategies are proposed in the literature: order dimensions by similarity to get homogeneously shaped glyphs vs. order by dissimilarity to emphasize spikes and salient shapes. While there is evidence that salient shapes support clustering tasks, evaluation, and direct comparison of data-driven ordering strategies has not received much research attention. We contribute an empirical user study to evaluate the efficiency, effectiveness, and user confidence in visual clustering tasks using star glyphs. In comparison to similarity-based ordering, our results indicate that dissimilarity-based star glyph layouts support users better in clustering tasks, especially when clutter is present.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933656"}, {"primary_key": "3127787", "vector": [], "sparse_vector": [], "title": "Efficient Space Skipping and Adaptive Sampling of Unstructured Volumes Using Hardware Accelerated Ray Tracing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Sample based ray marching is an effective method for direct volume rendering of unstructured meshes. However, sampling such meshes remains expensive, and strategies to reduce the number of samples taken have received relatively little attention. In this paper, we introduce a method for rendering unstructured meshes using a combination of a coarse spatial acceleration structure and hardware-accelerated ray tracing. Our approach enables efficient empty space skipping and adaptive sampling of unstructured meshes, and outperforms a reference ray marcher by up to 7×.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933539"}, {"primary_key": "3127788", "vector": [], "sparse_vector": [], "title": "Periphery Plots for Contextualizing Heterogeneous Time-Based Charts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Patterns in temporal data can often be found across different scales, such as days, weeks, and months, making effective visualization of time-based data challenging. Here we propose a new approach for providing focus and context in time-based charts to enable interpretation of patterns across time scales. Our approach employs a focus zone with a time and a second axis, that can either represent quantities or categories, as well as a set of adjacent periphery plots that can aggregate data along the time, value, or both dimensions. We present a framework for periphery plots and describe two use cases that demonstrate the utility of our approach.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933582"}, {"primary_key": "3127792", "vector": [], "sparse_vector": [], "title": "Slope-Dependent Rendering of Parallel Coordinates to Reduce Density Distortion and Ghost Clusters.", "authors": ["<PERSON>", "Frederik L. <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Parallel coordinates are a popular technique to visualize multi-dimensional data. However, they face a significant problem influencing the perception and interpretation of patterns. The distance between two parallel lines differs based on their slope. Vertical lines are rendered longer and closer to each other than horizontal lines. This problem is inherent in the technique and has two main consequences: (1) clusters which have a steep slope between two axes are visually more prominent than horizontal clusters. (2) Noise and clutter can be perceived as clusters, as a few parallel vertical lines visually emerge as a ghost cluster. Our paper makes two contributions: First, we formalize the problem and show its impact. Second, we present a novel technique to reduce the effects by rendering the polylines of the parallel coordinates based on their slope: horizontal lines are rendered with the default width, lines with a steep slope with a thinner line. Our technique avoids density distortions of clusters, can be computed in linear time, and can be added on top of most parallel coordinate variations. To demonstrate the usefulness, we show examples and compare them to the classical rendering.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933706"}, {"primary_key": "3127793", "vector": [], "sparse_vector": [], "title": "A Deep Learning Approach to Selecting Representative Time Steps for Time-Varying Multivariate Data.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a deep learning approach that selects representative time steps from a given time-varying multivariate data set. Our solution leverages an autoencoder that implicitly learns feature descriptors of each individual volume in a latent space. These feature descriptors are used to reconstruct respective volumes for error estimation during network training. We then perform dimensionality reduction of these feature descriptors and select representative time steps in the projected space. Unlike previous approaches, our solution can handle time-varying multivariate data sets where the multivariate features can be learned using a multichannel input to the autoencoder. We demonstrate the effectiveness of our approach using several time-varying multivariate data sets and compare our selection results with those generated using an information-theoretic approach.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933759"}, {"primary_key": "3127794", "vector": [], "sparse_vector": [], "title": "Time Varying Predominance Tag Maps.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Visually conveying time-dependent changes in tag maps is insufficiently addressed by current approaches. Typically, for each time range a tag map is determined, and the change between tag maps of subsequent time ranges is progressively visualized. Our method compares tag maps locally in order to enable a continuous display of geographical topic changes among subsequent time ranges. We further provide an alternate tag map variant focusing on frequency changes instead of relative frequency values to visualize the geospatial-temporal rise and fall of topics.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933654"}, {"primary_key": "3127795", "vector": [], "sparse_vector": [], "title": "Evaluating Gradient Perception in Color-Coded Scalar Fields.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Color mapping is a commonly used technique for visualizing scalar fields. While there exists advice for choosing effective colormaps, it is unclear if current guidelines apply equally across task types. We study the perception of gradients and evaluate the effectiveness of three colormaps at depicting gradient magnitudes. In a crowd-sourced experiment, we determine the just-noticeable differences (JNDs) at which participants can reliably compare and judge variations in gradient between two scalar fields. We find that participants exhibited lower JNDs with a diverging (cool-warm) or a spectral (rainbow) scheme, as compared with a monotonic-luminance colormap (viridis). The results support a hypothesis that apparent discontinuities in the color ramp may help viewers discern subtle structural differences in gradient. We discuss these findings and highlight future research directions for colormap evaluation.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933760"}, {"primary_key": "3127798", "vector": [], "sparse_vector": [], "title": "SAX Navigator: Time Series Exploration through Hierarchical Clustering.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Comparing many long time series is challenging to do by hand. Clustering time series enables data analysts to discover relevance between and anomalies among multiple time series. However, even after reasonable clustering, analysts have to scrutinize correlations between clusters or similarities within a cluster. We developed SAX Navigator, an interactive visualization tool, that allows users to hierarchically explore global patterns as well as individual observations across large collections of time series data. Our visualization provides a unique way to navigate time series that involves a \"vocabulary of patterns\" developed by using a dimensionality reduction technique, Symbolic Aggregate approXimation (SAX). With SAX, the time series data clusters efficiently and is quicker to query at scale. We demonstrate the ability of SAX Navigator to analyze patterns in large time series data based on three case studies for an astronomy data set. We verify the usability of our system through a think-aloud study with an astronomy domain scientist.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933618"}, {"primary_key": "3127803", "vector": [], "sparse_vector": [], "title": "Interactive Bicluster Aggregation in Bipartite Graphs.", "authors": ["Maoyuan Sun", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Exploring coordinated relationships is important for sense making of data in various fields, such as intelligence analysis. To support such investigations, visual analysis tools use biclustering to mine relationships in bipartite graphs and visualize the resulting biclusters with standard graph visualization techniques. Due to overlaps among biclusters, such visualizations can be cluttered (e.g., with many edge crossings), when there are a large number of biclusters. Prior work attempted to resolve this problem by automatically ordering nodes in a bipartite graph. However, visual clutter is still a serious problem, since the number of displayed biclusters remains unchanged. We propose bicluster aggregation as an alternative approach, and have developed two methods of interactively merging biclusters. These interactive bicluster aggregations help organize similar biclusters and reduce the number of displayed biclusters. Initial expert feedback indicates potential usefulness of these techniques in practice.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933546"}, {"primary_key": "3127804", "vector": [], "sparse_vector": [], "title": "A Markov Model of Users&apos; Interactive Behavior in Scatterplots.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recently, <PERSON> et al. proposed a set of computational metrics for quantifying cognitive bias based on user interaction sequences. The metrics rely on a Markov model to predict a user's next interaction based on the current interaction. The metrics characterize how a user's actual interactive behavior deviates from a theoretical baseline, where \"unbiased behavior\" was previously defined to be equal probabilities of all interactions. In this paper, we analyze the assumptions made of these metrics. We conduct a study in which participants, subject to anchoring bias, interact with a scatterplot to complete a categorization task. Our results indicate that, rather than equal probabilities of all interactions, unbiased behavior across both bias conditions can be better approximated by proximity of data points.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933779"}, {"primary_key": "3127806", "vector": [], "sparse_vector": [], "title": "Conditional Parallel Coordinates.", "authors": ["<PERSON>"], "summary": "Parallel Coordinates [11],[12] are a popular data visualization technique for multivariate data. Dating back to as early as 1880 [8] PC are nearly as old as <PERSON>'s famous cholera outbreak map [18] of 1855, which is frequently regarded as a historic landmark for modern data visualization. Numerous extensions have been proposed to address integrity, scalability and readability. We make a new case to employ PC on conditional data, where additional dimensions are only unfolded if certain criteria are met in an observation. Compared to standard PC which operate on a flat set of dimensions the ontology of our input to Conditional Parallel Coordinates is of hierarchical nature. We therefore briefly review related work around hierarchical PC using aggregation or nesting techniques. Our contribution is a visualization to seamlessly adapt PC for conditional data under preservation of intuitive interaction patterns to select or highlight polylines. We conclude with intuitions on how to operate CPC on two data sets: an AutoML hyperparameter search log, and session results from a conversational agent.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933632"}, {"primary_key": "3127807", "vector": [], "sparse_vector": [], "title": "FacIt: Factorizing Tensors into Interpretable and Scrutinizable Patterns.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Tensor Factorization has been widely used in many fields to discover latent patterns from multidimensional data. Interpreting or scrutinizing the tensor factorization results are, however, by no means easy. We introduce FacIt, a generic visual analytic system that directly factorizes tensor-formatted data into a visual representation of patterns to facilitate result interpretation, scrutinization, information query, as well as model selection. Our design consists of (i) a suite of model scrutinizing and inspection tools that allows efficient tensor model selection (commonly known as rank selection problem) and (ii) an interactive visualization design that empowers users with both characteristics- and content-driven pattern discovery. We demonstrate the effectiveness of our system through usage scenarios with policy adoption analysis.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933750"}, {"primary_key": "3127810", "vector": [], "sparse_vector": [], "title": "Hybrid Grids for Sparse Volume Rendering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Shallow k-d trees are an efficient empty space skipping data structure for sparse volume rendering and can be constructed in real-time for moderately sized data sets. Larger volume data sets however require deeper k-d trees that sufficiently cull empty space but take longer to construct. In contrast to k-d trees, uniform grids have inferior culling properties but can be constructed in real-time. We propose a hybrid data structure that employs hierarchical subdivision at the root level and a uniform grid at the leaf level to balance construction and rendering times for sparse volume rendering. We provide a thorough evaluation of this spatial index and compare it to state of the art space skipping data structures.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933631"}, {"primary_key": "3127813", "vector": [], "sparse_vector": [], "title": "FeatureExplorer: Interactive Feature Selection and Exploration of Regression Models for Hyperspectral Images.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Melba M<PERSON>", "<PERSON>"], "summary": "Feature selection is used in machine learning to improve predictions, decrease computation time, reduce noise, and tune models based on limited sample data. In this article, we present FeatureExplorer, a visual analytics system that supports the dynamic evaluation of regression models and importance of feature subsets through the interactive selection of features in high-dimensional feature spaces typical of hyperspectral images. The interactive system allows users to iteratively refine and diagnose the model by selecting features based on their domain knowledge, interchangeable (correlated) features, feature importance, and the resulting model performance.", "published": "2019-01-01", "category": "vis", "pdf_url": "", "sub_summary": "", "source": "vis", "doi": "10.1109/VISUAL.2019.8933619"}]