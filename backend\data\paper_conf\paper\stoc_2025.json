[{"primary_key": "197836", "vector": [], "sparse_vector": [], "title": "Tight Results for Online Convex Paging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Online convex paging (<PERSON><PERSON> and <PERSON>, 2015; <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, 2023) models a broad class of cost functions for the classical paging problem. In particular, it naturally captures fairness constraints: e.g., that no specific page (or groups of pages) suffers an “unfairly” high number of evictions by considering ℓp norms of eviction vectors for p>1. The case of the ℓ∞ norm has also been of special interest, and is called min-max paging. We give tight upper and lower bounds for the convex paging problem for a broad class of convex functions. Prior to our work, only fractional algorithms were known for this general setting. Moreover, our general result also improves on prior works for special cases of the problem. For example, it implies that the randomized competitive ratio of the min-max paging problem is Θ(logklogn); this improves both the upper bound and the lower bound given in prior work. It also shows that the randomized and deterministic competitive ratios for ℓp-norm paging are Θ(plogk) and Θ(pk) respectively; the randomized results are completely new, as is the deterministic lower bound. All previous algorithms we know for paging with non-linear costs used fractional relaxations. We show a fundamental limitation of this approach — we give integrality gap instances for the natural relaxation used in these works. This shows that a generic relax-and-round framework—solving the relaxation and then rounding it—is insufficient for obtaining tight bounds for this problem. To bypass this bottleneck, we work with the integer versions of the problems directly. Somewhat surprisingly, we show how to take an arbitrary online algorithm for the weighted paging problem (with linear costs), and convert it in a black-box way to an online algorithm for convex paging, losing just an optimal factor in this reduction. This reduction proves especially challenging in the randomized case, where the underlying weighted paging algorithm is randomized, and the analysis needs to proceed via a delicate martingale argument. We believe this approach of lifting arbitrary (weighted linear) online algorithms to convex objectives may be of broader interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718217"}, {"primary_key": "197837", "vector": [], "sparse_vector": [], "title": "Testing vs Estimation for Index-Invariant Properties in the Huge Object Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Huge Object model of property testing [<PERSON><PERSON><PERSON> and <PERSON>, TheoretiCS 23] concerns properties of distributions supported on {0,1}n, where n is so large that even reading a single sampled string is unrealistic. Instead, query access is provided to the samples, and the efficiency of the algorithm is measured by the total number of queries that were made to them. Index-invariant properties under this model were defined in [<PERSON><PERSON><PERSON><PERSON> et al., COLT 23], as a compromise between enduring the full intricacies of string testing when considering unconstrained properties, and giving up completely on the string structure when considering label-invariant properties. Index-invariant properties are those that are invariant through a consistent reordering of the bits of the involved strings. Here we provide an adaptation of <PERSON><PERSON><PERSON><PERSON><PERSON>’s regularity method for this setting, and in particular show that if an index-invariant property admits an є-test with a number of queries depending only on the proximity parameter є, then it also admits a distance estimation algorithm whose number of queries depends only on the approximation parameter.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718166"}, {"primary_key": "197838", "vector": [], "sparse_vector": [], "title": "Characterizing and Testing Principal Minor Equivalence of Matrices.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Two matrices are said to be principal minor equivalent if they have equal corresponding principal minors of all orders. We give a characterization of principal minor equivalence and a deterministic polynomial time algorithm to check if two given matrices are principal minor equivalent. Earlier such results were known for certain special cases like symmetric matrices, skew-symmetric matrices with 0, 1, -1-entries, and matrices with no cuts (i.e., for any non-trivial partition of the indices, the top right block or the bottom left block must have rank more than 1). As an immediate application, we get an algorithm to check if the determinantal point processes corresponding to two given kernel matrices (not necessarily symmetric) are the same. As another application, we give a deterministic polynomial-time test to check the equality of two multivariate polynomials, each computed by a symbolic determinant with a rank 1 constraint on coefficient matrices.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718146"}, {"primary_key": "197839", "vector": [], "sparse_vector": [], "title": "Maximum Circuit Lower Bounds for Exponential-Time Arthur Merlin.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that the complexity class of exponential-time <PERSON> with sub-exponential advice (AMEXP/2nε) requires circuit complexity at least 2n/n. Previously, the best known such near-maximum lower bounds were for symmetric exponential time by <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (STOC’24) and <PERSON> (STOC’24), or randomized exponential time with MCSP oracle and sub-exponential advice by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (CCC’23). Our result is proved by combining the recent iterative win-win paradigm of <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (FOCS’23) together with the uniform hardness-vs-randomness connection for Arthur-Merlin protocols by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (STOC’07) and <PERSON> (CCC’23). We also provide a conceptually different proof using a novel ”critical win-win” argument that extends a technique of <PERSON>, <PERSON>, and <PERSON><PERSON> (STOC’21). Indeed, our circuit lower bound is a corollary of a new explicit construction for properties in coAM. We show that for every dense property P ∈ coAM, there is a quasi-polynomial-time Arthur-Merlin protocol with short advice such that the following holds for infinitely many n: There exists a canonical string wn ∈ P ∩ {0,1}n so that (1) there is a strategy of <PERSON> such that <PERSON> outputs wn with probability 1 and (2) for any strategy of <PERSON>, with probability 2/3, <PERSON> outputs either wn or a failure symbol ⊥. As a direct consequence of this new explicit construction, our circuit lower bound also generalizes to circuits with an AM ∩ coAM oracle. To our knowledge, this is the first unconditional lower bound against a strong non-uniform class using a hard language that is only ”quantitatively harder”.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718224"}, {"primary_key": "197840", "vector": [], "sparse_vector": [], "title": "Fiat-Shamir in the Plain Model from Derandomization (Or: Do Efficient Algorithms Believe that NP = PSPACE?).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A classical challenge in complexity theory and cryptography is to simulate interactive proof systems by non-interactive proof systems. In this work we leverage approaches from recent works in derandomization to address this challenge, focusing on non-interactive simulations that are sound against uniform adversarial algorithms. Our results concern fundamental questions in complexity theory, such as the NP vs PSPACE question, and also in cryptography, such as the question of constructing non-interactive zero-knowledge arguments for NP from unstructured assumptions. Relying on strong complexity-theoretic hardness assumptions (that will be described below): 1. *Complexity theory.* We prove that PSPACE is contained in the “computationally sound” version of NP. Specifically, for every L∈PSPACE, membership in L can be verified by an NP-type (deterministic, polynomial-time) verifier V with the following guarantee: The verifier accepts every x∈ L when given a proof π from an honest prover that runs in fixed exponential time TP; and every uniform adversary running in probabilistic time poly(TP) cannot find x∉ L and π such that V(x,π)=1, except with negligible probability in TP. As a corollary in the area of bounded arithmetic, under the same assumptions, we deduce that NP≠PSPACE is not provable in the theory APC1. This is a strong theory, which captures many of the major results in complexity. 2. *Cryptography.* We construct new cryptographic protocols, including succinct non-interactive arguments (SNARGs) for NC in the plain model, as well as non-interactive zero-knowledge and witness indistinguishable (NIZK and NIWI) proof systems for NP, all with computational soundness against uniform adversaries. The SNARG relies solely on the aforementioned complexity-theoretic assumption, whereas the NIZK and NIWI require also a sub-exponentially secure one-way function (which should be injective in the case of the NIWI). These are the first constructions of the above protocols that do not rely on highly structured cryptographic primitives. Roughly speaking, following Chen and Tell (FOCS 2021, STOC 2023), the complexity-theoretic hardness assumptions throughout our paper assert the existence of functions f ∶ {0,1}n → {0,1}k that are computable in polynomial time and hard for bounded-space machines (say, linear space) in a strong average-case sense: No efficient algorithm can find an input x on which the bounded-space machine computes f, except with negligible probability.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718177"}, {"primary_key": "197841", "vector": [], "sparse_vector": [], "title": "Online Stochastic Matching with Unknown Arrival Order: Beating 0.5 against the Online Optimum.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the online stochastic matching problem. Against the offline benchmark, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (SODA 2015) designed an optimal 0.5-competitive algorithm. A recent line of work, initiated by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (MOR 2024), focuses on designing approximation algorithms against the online optimum. The online benchmark allows positive results surpassing the 0.5 ratio. In this work, adapting the order-competitive analysis by <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON> (SODA 2023), we design a 0.5+Ω(1) order-competitive algorithm against the online benchmark with unknown arrival order. Our algorithm is significantly different from existing ones, as the known arrival order is crucial to the previous approximation algorithms.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718196"}, {"primary_key": "197842", "vector": [], "sparse_vector": [], "title": "Phase Transitions via Complex Extensions of Markov Chains.", "authors": ["Jingcheng Liu", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study algebraic properties of partition functions, particularly the location of zeros, through the lens of rapidly mixing Markov chains. The classical Lee-Yang program initiated the study of phase transitions via locating complex zeros of partition functions. Markov chains, besides serving as algorithms, have also been used to model physical processes tending to equilibrium. In many scenarios, rapid mixing of Markov chains coincides with the absence of phase transitions (complex zeros). Our motivating example is the independence polynomial on k-uniform hypergraphs, where the best-known zero-free regime has been significantly lagging behind the regime where we have rapidly mixing Markov chains for the underlying hypergraph independent sets. Specifically, the Glauber dynamics is known to mix rapidly on independent sets in a k-uniform hypergraph of maximum degree Δ provided that Δ ≲ 2k/2. On the other hand, the best-known zero-freeness around the point 1 of the independence polynomial on k-uniform hypergraphs requires Δ ≤ 5, the same bound as on a graph. By introducing a complex extension of Markov chains, we lift an existing percolation argument to the complex plane, and show that if Δ ≲ 2k/2, the Markov chain converges in a complex neighborhood, and the independence polynomial itself does not vanish in the same neighborhood. In the same regime, our result also implies central limit theorems for the size of a uniformly random independent set, and deterministic approximation algorithms for the number of hypergraph independent sets of size k ≤ α n for some constant α.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718230"}, {"primary_key": "197843", "vector": [], "sparse_vector": [], "title": "Polynomial-Time PIT from (Almost) Necessary Assumptions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The celebrated result of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (Computational Complexity, 2004) showed that PIT algorithms imply circuit lower bounds, and vice versa. Since then it has been a major challenge to understand the precise connections between PIT and lower bounds. In particular, a main goal has been to understand which lower bounds suffice to obtain efficient PIT algorithms, and how close are they to lower bounds that are necessary for the conclusion. We construct polynomial-time PIT algorithms from lower bounds that are, up to relatively minor remaining gaps, necessary for the existence of such algorithms. That is, we prove that these lower bounds are, up to the mentioned minor gaps, both sufficient and necessary for polynomial-time PIT, over fields of characteristic zero. Over sufficiently large finite fields, we show a similar result wherein the PIT algorithm runs in time nlog(c)(n), i.e. a power of c-iterated log for an arbitrarily large constant c>1. The key to these improvements is studying PIT versus lower bounds in the uniform setting, in which we focus on proving lower bounds for uniform arithmetic circuits and their variants (and on deducing algorithms from such lower bounds). Indeed, by working in this setting we obtain results that are significantly tighter than previously known results concerning polynomial-time PIT vs lower bounds, and are in fact also tighter than known hardness-vs-randomness connections in the Boolean setting. Our results are obtained by combining recent techniques from Boolean hardness vs randomness, and in particular the generator of <PERSON> and <PERSON> (FOCS 2021), with the algebraic hitting-set generator of <PERSON>, <PERSON>, <PERSON>, and <PERSON> (SIAM J. Computing 2022) along with the bootstrapping ideas of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (ST<PERSON> 2018) and of <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (SODA 2019).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718198"}, {"primary_key": "197844", "vector": [], "sparse_vector": [], "title": "Accelerated Approximate Optimization of Multi-commodity Flows on Directed Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We provide m1+o(1)kє−1-time algorithms for computing multiplicative (1 − є)-approximate solutions to multi-commodity flow problems with k-commodities on m-edge directed graphs, including concurrent multi-commodity flow and maximum multi-commodity flow. To obtain our results, we provide new optimization tools of potential independent interest. First, we provide an improved optimization method for solving ℓq, p-regression problems to high accuracy. This method makes Õq, p(k) queries to a high accuracy convex minimization oracle, where Õq, p(·) hides factors depending only on q, p, or poly(logm), improving upon the Õq, p(k2) bound of [Chen-Ye, ICALP 2024]. As a result, we obtain the first almost-linear time algorithm that solves ℓq, p flows on directed graphs to high accuracy. Second, we present optimization tools to reduce approximately solving composite ℓ1, ∞-regression problems to solving mo(1)є−1 instances of the composite ℓq, p-regression problem. The method builds upon recent advances in solving box-simplex games [Jambulapati-Tian, NeurIPS 2023] and the area convex regularizer introduced in [Sherman, STOC 2017] to obtain faster rates for constrained versions of the problem. Carefully combining these techniques yields our directed multi-commodity flow algorithm.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718267"}, {"primary_key": "197845", "vector": [], "sparse_vector": [], "title": "All-Pairs Shortest Paths with Few Weights per Node.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "Virginia Vassilevska Williams", "<PERSON>"], "summary": "We study the central All-Pairs Shortest Paths (APSP) problem under the restriction that there are at most d distinct weights on the outgoing edges from every node. For d=n this is the classical (unrestricted) APSP problem that is hypothesized to require cubic time n3−o(1), and at the other extreme, for d=1, it is equivalent to the Node-Weighted APSP problem. We present new algorithms that achieve the following results: * Node-Weighted APSP can be solved in time Õ(n(3+ω)/2) = Õ(n2.686), improving on the 15-year-old subcubic bounds Õ(n(9+ω)/4) = Õ(n2.843) [<PERSON>; STOC ’07] and Õ(n2.830) [<PERSON>ster; SODA ’09]. This positively resolves the question of whether Node-Weighted APSP is an ”intermediate” problem in the sense of having complexity n2.5+o(1) if ω=2, in which case it also matches an n2.5−o(1) conditional lower bound. * For up to d ≤ n3−ω−є distinct weights per node (where є > 0), the problem can be solved in subcubic time O(n3−f(є)) (where f(є) > 0). In particular, assuming that ω = 2, we can tolerate any sublinear number of distinct weights per node d ≤ n1−є, whereas previous work [<PERSON><PERSON>; SODA ’09] could only handle d ≤ n1/2−є in subcubic time. This promotes our understanding of the APSP hypothesis showing that the hardest instances must exhaust a linear number of weights per node. With the current bounds on ω, we achieve a subcubic algorithm for d ≤ n0.628 whereas previously a subcubic running time could only be achieved for d ≤ n0.384. Our result also applies to the All-Pairs Exact Triangle problem, thus generalizing a result of Chan and Lewenstein on “Clustered 3SUM” from arrays to matrices. Notably, our technique constitutes a rare application of additive combinatorics in graph algorithms. We complement our algorithmic results with simple hardness reductions extending the n2.5−o(1) conditional lower bound for Node-Weighted APSP to undirected graphs. Interestingly, under fine-grained assumptions, the complexity in the undirected case jumps from O(nω) for d=1 to n2.5−o(1) for d ≥ 2.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718240"}, {"primary_key": "197846", "vector": [], "sparse_vector": [], "title": "Succinct Oblivious Tensor Evaluation and Applications: Adaptively-Secure Laconic Function Evaluation and Trapdoor Hashing for All Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose the notion of succinct oblivious tensor evaluation (OTE), where two parties compute an additive secret sharing of a tensor product of two vectors x ⊗ y, exchanging two simultaneous messages. Crucially, the size of both messages and of the CRS is independent of the dimension of x. We present a construction of OTE with optimal complexity from the standard learning with errors (LWE) problem. Then we show how this new technical tool enables a host of cryptographic primitives, all with security reducible to LWE, such as: (a) Adaptively secure laconic function evaluation for depth-D functions f:{0, 1}m→{0, 1}ℓ with communication m+ℓ+D· poly(λ); (b) A trapdoor hash function for all functions; (c) An (optimally) succinct homomorphic secret sharing for all functions; (d) A rate-1/2 laconic oblivious transfer for batch messages, which is best possible. In particular, we obtain the first laconic function evaluation scheme that is adaptively secure from the standard LWE assumption, improving upon <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (FOCS 2018). As a key technical ingredient, we introduce a new notion of adaptive lattice encodings, which may be of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718164"}, {"primary_key": "197847", "vector": [], "sparse_vector": [], "title": "Pauli Measurements Are Not Optimal for Single-Copy Tomography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Quantum state tomography is a fundamental problem in quantum computing. Given n copies of an unknown N-qubit state ρ∈ℂd× d,d=2N, the goal is to learn the state up to an accuracy ε in trace distance, say with at least constant probability 0.99. We are interested in the copy complexity, the minimum number of copies of ρ needed to fulfill the task. As current quantum devices are physically limited, Pauli measurements have attracted significant attention due to their ease of implementation. However, a large gap exists in the literature for tomography with Pauli measurements. The best-known upper bound is O(N· 12N/ε2), and no non-trivial lower bound is known besides the general single-copy lower bound of Ω(8N/ε2), achieved by hard-to-implement structured POVMs such as MUB, SIC-POVM, and uniform POVM. We have made significant progress on this long-standing problem. We first prove a stronger upper bound of O(10N/ε2). To complement it, we also obtain a lower bound of Ω(9.118N/ε2), which holds even with adaptivity. To our knowledge, this demonstrates the first known separation between Pauli measurements and structured POVMs. The new lower bound is a consequence of a novel framework for adaptive quantum state tomography with measurement constraints. The main advantage is that we can use measurement-dependent hard instances to prove tight lower bounds for Pauli measurements, while prior lower-bound techniques for tomography only work with measurement-independent constructions. Moreover, we connect the copy complexity lower bound of tomography to the eigenvalues of the measurement information channel, which governs the measurement’s capacity to distinguish between states. To demonstrate the generality of the new framework, we obtain tight bounds for adaptive quantum state tomography with k-outcome measurements, where we recover existing results and establish new ones.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718248"}, {"primary_key": "197848", "vector": [], "sparse_vector": [], "title": "Online Locality Meets Distributed Quantum Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "Francesco D&apos;Amore", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We connect three distinct lines of research that have recently explored extensions of the classical LOCAL model of distributed computing: A. distributed quantum computing and non-signaling distributions [e.g. STOC 2024], B. finitely-dependent processes [e.g. Forum Math. Pi 2016], and C. locality in online graph algorithms and dynamic graph algorithms [e.g. ICALP 2023]. We prove new results on the capabilities and limitations of all of these models of computing, for locally checkable labeling problems (LCLs). We show that all these settings can be sandwiched between the classical LOCAL model and what we call the randomized online-LOCAL model. Our work implies limitations on the quantum advantage in the distributed setting, and we also exhibit a new barrier for proving tighter bounds. Our main technical results are these: 1. All LCL problems solvable with locality $O(\\log^\\star n)$ in the classical deterministic LOCAL model admit a finitely-dependent distribution with locality $O(1)$. This answers an open question by <PERSON><PERSON><PERSON><PERSON> [2024], and also presents a new barrier for proving bounds on distributed quantum advantage using causality-based arguments. 2. In rooted trees, if we can solve an LCL problem with locality $o(\\log \\log \\log n)$ in the randomized online-LOCAL model (or any of the weaker models, such as quantum-LOCAL), we can solve it with locality $O(\\log^\\star n)$ in the classical deterministic LOCAL model. One of many implications is that in rooted trees, $O(\\log^\\star n)$ locality in quantum-LOCAL is not stronger than $O(\\log^\\star n)$ locality in classical LOCAL.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718211"}, {"primary_key": "197849", "vector": [], "sparse_vector": [], "title": "Lifting to Bounded-Depth and Regular Resolutions over Parities via Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Proving superpolynomial lower bounds on proof size in the proof system resolution over parities (Res(⊕)) remains a significant open challenge. A recent breakthrough by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (STOC 2024) established an exponential lower bound for regular Res(⊕). In this work, we introduce a lifting technique for regular Res(⊕), applicable to a wide range of formulas. Specifically, we develop a method that transforms any formula with large resolution depth into a formula requiring exponential-size regular Res(⊕) refutations. This transformation is achieved through a combination of mixing and constant-size lifting. Using this approach, we provide an alternative and improved separation between resolution and regular Res(⊕), originally proved by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (CCC 2024). We construct an n-variable formula with a polynomial-size resolution refutation of depth O(√n), yet requires regular Res(⊕) refutations of size 2Ω(√n). Furthermore, we apply our technique to establish an exponential lower bound on the size of depth-cnloglogn Res(⊕) refutations, where n is the number of variables in the refuted formula, and c is a constant. The hard instances in this setting are Tseitin formulas lifted with the Maj5 gadget. Since even depth-n Res(⊕) captures all possible definitions of regular Res(⊕), our result yields an exponential lower bound for top-regular Res(⊕), resolving an open question posed by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (CCC 2022).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718150"}, {"primary_key": "197850", "vector": [], "sparse_vector": [], "title": "From Signaling to Interviews in Random Matching Markets.", "authors": ["<PERSON>", "Itai Ashlagi", "<PERSON><PERSON>", "<PERSON>"], "summary": "In many two-sided labor markets, interviews are conducted before matches are formed. An increase in the number of interviews in the market for medical residencies raised the demand for signaling mechanisms, in which applicants can send a limited number of signals to communicate interest. We study the role of signaling mechanisms in reducing the number of interviews in centralized random matching markets with post-interview shocks. For the market to clear we focus on interim stability, which extends the notion of stability to ensure that agents do not regret not interviewing with each other. A matching is almost interim stable if it is interim stable after removing a vanishingly small fraction of agents. We first study signaling mechanisms in random matching markets with n agents when agents on the short side, long side, or both sides signal their top d preferred partners. Interviews graphs are formed by including all pairs where at least one party has signaled the other. We show that when d = ω(1), short-side signaling leads to almost interim stable matchings. Long-side signaling is only effective when the market is almost balanced. Conversely, when the interview shocks are negligible and d = o(logn), both-side signaling fails to achieve almost interim stability. For larger d ≥ Ω(log2 n), short-side signaling achieves perfect interim stability, while long-side signaling fails in imbalanced markets. We build on our findings to propose a signaling mechanism for multi-tiered random markets. Our analysis identifies conditions under which signaling mechanisms are incentive compatible. A technical contribution is the analysis of a message-passing algorithm that efficiently determines interim stability and matching outcomes by leveraging local neighborhood structures.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718158"}, {"primary_key": "197851", "vector": [], "sparse_vector": [], "title": "Low Rank Matrix Rigidity: Tight Lower Bounds and Hardness Amplification.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For an N × N matrix A, its rank-r rigidity, denoted RA(r), is the minimum number of entries of A that one must change to make its rank become at most r. Determining the rigidity of interesting explicit families of matrices remains a major open problem, and is central to understanding the complexities of these matrices in many different models of computation and communication. We focus in this paper on the <PERSON><PERSON> transform and on the ‘distance matrix’, whose rows and columns correspond to binary vectors, and whose entries calculate whether the row and column are close in Hamming distance. Our results also generalize to other Kronecker powers and ‘Majority powers’ of fixed matrices. We prove two new results about such matrices. First, we prove new rigidity lower bounds in the low-rank regime where r 0 such that the N × N Walsh-Hadamard matrix Hn satisfies RHn(c1 logN) ≥ N2 ( 1/2 − N−c2 ), and a similar lower bound for the other aforementioned matrices. This is tight, and is the new best rigidity lower bound for an explicit matrix family at this rank; the previous best was R(c1 logN) ≥ c3 N2 for a small constant c3>0. Second, we give new hardness amplification results, showing that rigidity lower bounds for these matrices for slightly higher rank would imply breakthrough rigidity lower bounds for much higher rank. For instance, if one could prove RHn(log1 + ε N) ≥ N2 ( 1/2 − N−1/2(loglogN)o(1) ) over any finite field for some ε>0, this would imply that Hn is Razborov rigid, giving a breakthrough lower bound in communication complexity.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718287"}, {"primary_key": "197852", "vector": [], "sparse_vector": [], "title": "DNF Learning via Locally Mixing Random Walks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give two results on PAC learning DNF formulas using membership queries in the challenging “distribution-free” learning framework, where learning algorithms must succeed for an arbitrary and unknown distribution over {0,1}n. (1) We first give a quasi-polynomial time “list-decoding” algorithm for learning a single term of an unknown DNF formula. More precisely, for any target s-term DNF formula f = T1 ∨ ⋯ ∨ Ts over {0,1}n and any unknown distribution D over {0,1}n, our algorithm, which uses membership queries and random examples from D, runs in quasipoly(n,s) time and outputs a list L of candidate terms such that with high probability some term Ti of f belongs to L. (2) We then use result (1) to give a quasipoly(n,s)-time algorithm, in the distribution-free PAC learning model with membership queries, for learning the class of size-s DNFs in which all terms have the same size. Our algorithm learns using a DNF hypothesis. The key tool used to establish result (1) is a new result on “locally mixing random walks,” which, roughly speaking, shows that a random walk on a graph that is covered by a small number of expanders has a non-negligible probability of mixing quickly in a subset of these expanders.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718262"}, {"primary_key": "197853", "vector": [], "sparse_vector": [], "title": "Ideal Pseudorandom Codes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Pseudorandom codes are error-correcting codes with the property that no efficient adversary can distinguish encodings from uniformly random strings. They were recently introduced by <PERSON> and <PERSON> [CRYPTO 2024] for the purpose of watermarking the outputs of randomized algorithms, such as generative AI models. Several constructions of pseudorandom codes have since been proposed, but none of them are robust to error channels that depend on previously seen codewords. This stronger kind of robustness is referred to as adaptive robustness, and it is important for meaningful applications to watermarking. In this work, we show the following. Adaptive robustness: We show that the pseudorandom codes of <PERSON> and <PERSON> are adaptively robust, resolving a conjecture posed by <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> [S&P 2025]. Our proof involves several new ingredients, combining ideas from both cryptography and coding theory and taking hints from the analysis of Boolean functions. Ideal security: We define an ideal pseudorandom code as one which is indistinguishable from the ideal functionality, capturing both the pseudorandomness and robustness properties in one simple definition. We show that any adaptively robust pseudorandom code for single-bit messages can be bootstrapped to build an ideal pseudorandom code with linear information rate, under no additional assumptions. CCA security: In the setting where the encoding key is made public, we define a CCA-secure pseudorandom code in analogy with CCA-secure encryption. We show that any adaptively robust public-key pseudorandom code for single-bit messages can be used to build a CCA-secure pseudorandom code with linear information rate, in the random oracle model. Together with the result of <PERSON> and <PERSON>, it follows that there exist ideal pseudorandom codes assuming the 2O(√n)-hardness of LPN. This extends to CCA security in the random oracle model. These results immediately imply stronger robustness guarantees for generative AI watermarking schemes, such as the practical quality-preserving image watermarks of Gunn, Zhao, and Song [ICLR 2025].", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718309"}, {"primary_key": "197854", "vector": [], "sparse_vector": [], "title": "Adaptive Approximation Schemes for Matching Queues.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study a continuous-time, infinite-horizon dynamic bipartite matching problem. Suppliers arrive according to a Poisson process; while waiting, they may abandon the queue at a uniform rate. Customers on the other hand must be matched upon arrival. The objective is to minimize the expected long-term average cost subject to a throughput constraint on the total match rate. Previous literature on dynamic matching focuses on ”static” policies, where the matching decisions do not depend explicitly on the state of the supplier queues, achieving constant-factor approximations. By contrast, we design ”adaptive” policies, which leverage queue length information, and obtain near-optimal polynomial-time algorithms for several classes of instances. First, we develop a bi-criteria fully polynomial-time approximation scheme for dynamic matching on networks with a constant number of queues—that computes a (1−є)-approximation of the optimal policy in time polynomial in both the input size and 1/є. A key new technique is a hybrid LP relaxation, which combines static and state-dependent LP approximations of the queue dynamics, after a decomposition of the network. Networks with a constant number of queues are motivated by deceased organ donation schemes, where the supply types can be divided according to blood and tissue types. The above algorithm, combined with a careful cell decomposition gives a polynomial-time approximation scheme for dynamic matching on Euclidean networks of fixed dimension. The Euclidean case is of interest in ride-hailing and spatial service platforms, where the goal is to fulfill as many trips as possible while minimizing driving distances.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718317"}, {"primary_key": "197855", "vector": [], "sparse_vector": [], "title": "Approximation Algorithms for the Geometric Multimatching Problem.", "authors": ["Shinwoo An", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let S and T be two point sets in a metric space (M,d) with |S|+|T|=n, and b:S∪ T→ ℕ be a function satisfying b(s)≤ |T| for s∈ S and b(t)≤ |S| for t∈ T. We call a function µ:S× T→ {0,1} a multimatching between S and T with respect to b if for each s∈ S (and t∈ T), the number of points t∈ T (and s∈ S) with µ(s,t)=1 is at least b(s) (and b(t)). The cost of a multimatching µ is defined as cost(µ)=∑(s,t)µ(s,t)· d(s,t) where d(s,t) is the distance between s and t in M. The geometric multimatching problem aims to find a multimatching that minimizes its cost. The special case that b(v)=1 for all v∈ S∪ T is known as the geometric many-to-many matching problem. We present two results for the geometric multimatching problem and the geometric many-to-many matching problem when a metric space M has a doubling dimension ddim. Notably, we present the first near-linear-time approximation scheme for the geometric multimatching problem with respect to the output size. Furthermore, our second result improves the best-known (1+ε)-approximation algorithm for the geometric many-to-many matching problem presented by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [SoCG24], winning the best paper award at SoCG’24. More specifically, we present the following two algorithms: 1. A (1/ε)O(ddim) Blog2 n-time deterministic algorithm that returns a multimatching of cost at most (1+ε) times the cost of an optimal multimatching for any constant ε>0, where B denotes the summation of b(v) for all v∈ S∪ T. 2. A (1/ε)O(ddim) nlogn-time deterministic algorithm that returns a many-to-many matching of cost at most (1+ε) times the cost of an optimal many-to-many matching for any constant ε>0.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718147"}, {"primary_key": "197856", "vector": [], "sparse_vector": [], "title": "Computational Lower Bounds for No-Regret Learning in Normal-Form Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A celebrated connection in the interface of online learning and game theory establishes that the repeated interaction of no-regret players leads to a coarse correlated equilibrium (CCE)—a seminal game-theoretic solution concept. Despite the rich history of this foundational problem and the tremendous interest it has received in recent years, a basic question still remains open: how many iterations are needed for no-regret players to approximate an equilibrium under the usual normal-form representation? In this paper, we first establish tight computational lower bounds for that problem in two-player (general-sum) games under the constraint that the CCE reached approximates the optimal social welfare (or some other natural objective). From a technical standpoint, our approach revolves around proving lower bounds for computing a near-optimal T-sparse CCE—a mixture of T product distributions, circumscribing the iteration complexity of no-regret learning even in the centralized model of computation. Our proof proceeds by extending a classical reduction of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (GEB ’89) for optimal Nash to sparse (approximate) CCE through the use of PCP-type gap amplification, thereby ruling out attaining any non-trivial sparsity in polynomial time. Moreover, we strengthen our hardness results to apply in the low-precision regime as well via the planted clique conjecture. Building on those lower bounds, we next address the more challenging problem that lifts the welfare constraint. In particular, we work in the algorithmic framework put forward by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (STOC ’18) in the context of computing Nash equilibria, which consists of the sum-of-squares (SoS) relaxation in conjunction with oracle access to a verification oracle; the goal in that framework is to lower bound either the degree of the SoS relaxation or the number of queries to the verification oracle. Here, we obtain two such hardness results, precluding computing i) uniform logn-sparse correlated equilibria (CE) when є =poly(1/logn) and ii) uniform n1 − o(1)-sparse CE when є = poly(1/n).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718250"}, {"primary_key": "197857", "vector": [], "sparse_vector": [], "title": "Smoothed Analysis for Graph Isomorphism.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "There is no known polynomial-time algorithm for graph isomorphism testing, but elementary combinatorial “refinement” algorithms seem to be very efficient in practice. Some philosophical justification for this phenomenon is provided by a classical theorem of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>: an extremely simple polynomial-time combinatorial algorithm (variously known as “naïve refinement”, “naïve vertex classification”, “colour refinement” or the “1-dimensional Wei<PERSON><PERSON>iler–<PERSON> algorithm”) yields a so-called canonical labelling scheme for “almost all graphs”. More precisely, for a typical outcome of a random graph G(n,1/2), this simple combinatorial algorithm assigns labels to vertices in a way that easily permits isomorphism-testing against any other graph.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718173"}, {"primary_key": "197858", "vector": [], "sparse_vector": [], "title": "Sample-Optimal Private Regression in Polynomial Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the task of privately obtaining prediction error guarantees in ordinary least-squares regression problems with Gaussian covariates (with unknown covariance structure). We provide the first sample-optimal polynomial time algorithm for this task under both pure and approximate differential privacy. We show that any improvement to the sample complexity of our algorithm would violate either statistical-query or information-theoretic lower bounds. Additionally, our algorithm is robust to a small fraction of arbitrary outliers and achieves optimal error rates as a function of the fraction of outliers. In contrast, all prior efficient algorithms either incurred sample complexities with sub-optimal dimension dependence, scaling with the condition number of the covariates, or obtained a polynomially worse dependence on the privacy parameters. Our technical contributions are two-fold: first, we leverage resilience guarantees of Gaussians within the sum-of-squares framework. As a consequence, we obtain efficient sum-of-squares algorithms for regression with optimal robustness rates and sample complexity. Second, we generalize the recent robustness-to-privacy framework of <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> to account for the geometry induced by the covariance of the input samples. This framework crucially relies on the robust estimators to be sum-of-squares algorithms, and combining the two steps yields a sample-optimal private regression algorithm. We believe our techniques are of independent interest, and we demonstrate this by obtaining an efficient algorithm for covariance-aware mean estimation, with an optimal dependence on the privacy parameters.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718218"}, {"primary_key": "197859", "vector": [], "sparse_vector": [], "title": "A Framework for Building Data Structures from Communication Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a general framework for designing efficient data structures for high-dimensional pattern-matching problems (∃ i ∈ [n], f(xi,y) = 1) through communication models in which f(x,y) admits sublinear communication protocols with exponentially-small error. Specifically, we reduce the data structure problem to the Unambiguous Arthur-Merlin (UAM) communication complexity of f(x,y) under product distributions. We apply our framework to the Partial Match problem (a.k.a, matching with wildcards), whose underlying communication problem is sparse set-disjointness. When the database consists of n points in dimension d, and the number of ⋆’s in the query is at most w = c logn (≪ d), the fastest known linear-space data structure (<PERSON>, <PERSON> and <PERSON>, STOC’04) had query time t ≈ 2w = nc, which is nontrivial only when c<1. By contrast, our framework produces a data structure with query time n1−1/(c log2 c) and space close to linear. To achieve this, we develop a one-sided є-error communication protocol for set-disjointness under product distributions with Θ(√d log(1/є)) complexity, improving on the classical result of <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (FOCS’86). Building on this protocol, we show that the Unambiguous AM communication complexity of w-Sparse set-disjointness with є-error under product distributions is Õ(√w log(1/є)), independent of the ambient dimension d, which is crucial for the partial match result. Our framework sheds further light on the power of data-dependent data structures, which is instrumental for reducing to the (much easier) case of product distributions.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718300"}, {"primary_key": "197860", "vector": [], "sparse_vector": [], "title": "On the Computational Power of QAC0 with Barely Superlinear Ancillae.", "authors": ["<PERSON><PERSON><PERSON>", "Yangjing Dong", "Fengning Ou", "<PERSON><PERSON><PERSON>"], "summary": "QAC0 is the family of constant-depth polynomial-size quantum circuits consisting of arbitrary single qubit unitaries and multi-qubit Toffoli gates. It was introduced by <PERSON> as a quantum counterpart of AC0, along with the conjecture that QAC0 circuits can not compute PARITY. In this work we make progress on this longstanding conjecture: we show that any depth-d QAC0 circuit requires n1+3−d ancillae to compute a function with approximate degree Θ(n), which includes PARITY, MAJORITY and MODk. We further establish superlinear lower bounds on quantum state synthesis and quantum channel synthesis. This is the first superlinear lower bound on the super-linear sized QAC0. Regarding PARITY, we show that any further improvement on the size of ancillae to n1+exp(−o(d)) would imply that PARITY ∉ QAC0. These lower bounds are derived by giving low-degree approximations to QAC0 circuits. We show that a depth-d QAC0 circuit with a ancillae, when applied to low-degree operators, has a degree (n+a)1−3−d polynomial approximation in the spectral norm. This implies that the class QLC0, corresponding to linear size QAC0 circuits, has approximate degree o(n). This is a quantum generalization of the result that LC0 circuits have approximate degree o(n) by <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Our result also implies that QLC0≠NC1.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718189"}, {"primary_key": "197861", "vector": [], "sparse_vector": [], "title": "The Meta-complexity of Secret Sharing.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A secret-sharing scheme allows the distribution of a secret s among n parties, such that only certain predefined “authorized” sets of parties can reconstruct the secret, while all other “unauthorized” sets learn nothing about s. The collection of authorized/unauthorized sets is defined by a monotone function f: {0,1}n → {0,1}. It is known that any monotone function can be realized by a secret-sharing scheme; thus, the smallest achievable total share size, S(f), serves as a natural complexity measure. In this paper, we initiate the study of the following meta-complexity question: Given a monotone function f, is it possible to efficiently distinguish between cases where the secret-sharing complexity of f is small versus large? We examine this question across several computational models, yielding the following main results. (Hardness for formulas and circuits): Given a monotone formula f of size L, it is coNP-hard to distinguish between “cheap” functions, where the maximum share size is 1 bit and the total share size is O(L0.01), and “expensive” functions, where the maximum share size is Ω(√L) and the total share size is Ω(L/logL). This latter bound nearly matches known secret-sharing constructions yielding a total share size of L bits. For monotone circuits, we strengthen the bound on the expensive case to a maximum share size of Ω(L/logL) and a total share size of Ω(L2/logL). These results rule out the existence of instance-optimal compilers that map a formula f to a secret-sharing scheme with complexity polynomially related to S(f). (Hardness for truth tables): Under cryptographic assumptions, either (1) every n-bit slice function can be realized by a poly(n)-size secret-sharing scheme, or (2) given a truth-table representation of f of size N = 2n, it is computationally infeasible to distinguish in time poly(N) between cases where S(f) = poly(n) and S(f) = nω(1). Option (1) would be considered a breakthrough result, as the best-known construction for slices has a sub-exponential complexity of 2Õ(√n) (Liu, Vaikuntanathan, and Wee; Eurocrypt 2018). Our proof introduces a new worst-case-to-average-case reduction for slices, which may be of independent interest. (Hardness for graphs): We examine the simple case where f is given as a 2-DNF, represented by a graph G whose edges correspond to 2-terms, and ask whether it is possible to distinguish between cases where the share size is constant and those where the share size is large, say Ω(logn). We establish several connections between this question and questions in communication complexity. For instance, we show that graphs admitting constant-cost secret sharing form a subclass of graphs with constant randomized communication complexity and constant-size adjacency sketches (Harms, Wild, and Zamaraev; STOC 2022). We leverage these connections to establish new lower bounds for specific graph families, derive a combinatorial characterization of graphs with constant-size linear secret-sharing schemes, and show that a natural class of myopic algorithms fails to distinguish cheap graphs from expensive ones.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718161"}, {"primary_key": "197862", "vector": [], "sparse_vector": [], "title": "Polynomial-Time Tolerant Testing Stabilizer States.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the following task: suppose an algorithm is given copies of an unknown n-qubit quantum state |ψ⟩ promised (i) |ψ⟩ is ε1-close to a stabilizer state in fidelity or (ii) |ψ⟩ is ε2-far from all stabilizer states, decide which is the case. We show that for every ε1>0 and ε2≤ ε1C, there is a poly(1/ε1)-sample and n· poly(1/ε1)-time algorithm that decides which is the case (where C>1 is a universal constant). Our proof includes a new definition of Gowers norm for quantum states, an inverse theorem for the Gowers-3 norm of quantum states and new bounds on stabilizer covering for structured subsets of Paulis using results in additive combinatorics.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718277"}, {"primary_key": "197863", "vector": [], "sparse_vector": [], "title": "Testing and Learning Structured Quantum Hamiltonians.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Francisco E<PERSON> Gutiérrez"], "summary": "We consider the problems of testing and learning an unknown n-qubit quantum Hamiltonian H=∑x λx σx expressed in its Pauli basis, from queries to its evolution operator e−iHt under the normalized Frobenius norm. To this end, we prove the following results (with and without quantum memory) for Hamiltonians whose Pauli spectrum involves only k-local terms or has sparsity at most s: (1) Local Hamiltonians: We give a tolerant testing protocol to decide if a Hamiltonian is ε1-close to k-local or ε2-far from k-local, with O(1/(ε2−ε1)4) queries, thereby solving two open questions posed in a recent work by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [BCO’24]. For learning a k-local Hamiltonian up to error ε, we give a protocol with query complexity and total time evolution exp(O(k2+klog(1/ε))). Our algorithm leverages the non-commutative Bohnenb<PERSON><PERSON>-<PERSON> inequality in order to get a complexity independent of n. (2) Sparse Hamiltonians: We give a protocol for testing whether a Hamiltonian is ε1-close to being s-sparse or ε2-far from being s-sparse, with O(s6/(ε22−ε12)6) queries. For learning up to error ε, we show that O(s4/ε8) queries suffices. (3) Learning without quantum memory: The learning results stated above have no dependence on the system size n, but require n-qubit quantum memory. We give subroutines that allow us to reproduce all the above learning results without quantum memory; increasing the query complexity by a (logn)-factor in the local case and an n-factor in the sparse case. (4) Testing without quantum memory: We give a new subroutine called Pauli hashing, which allows one to tolerantly test s-sparse Hamiltonians using Õ(s14/(ε22−ε12)18) query complexity. A key ingredient is showing that s-sparse Pauli channels can be tested in a tolerant fashion as being ε1-close to being s-sparse or ε2-far under the diamond norm, using Õ(s2/(ε2−ε1)6) queries via Pauli hashing. In order to prove these results, we prove new structural theorems for local Hamiltonians, sparse Pauli channels and sparse Hamiltonians. We complement our learning algorithms with lower bounds that are polynomially weaker. Furthermore, our algorithms use short time evolutions and do not assume prior knowledge of the terms on which the Pauli spectrum is supported on, i.e., we do not require prior knowledge about the support of the Hamiltonian terms.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718289"}, {"primary_key": "197864", "vector": [], "sparse_vector": [], "title": "Vizing&apos;s <PERSON><PERSON> in Near-Linear Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>’s theorem states that any n-vertex m-edge graph of maximum degree Δ can be edge colored using at most Δ + 1 different colors [<PERSON><PERSON>, 1964]. <PERSON><PERSON>’s original proof is algorithmic and shows that such an edge coloring can be found in O(mn) time. This was subsequently improved to Õ(m√n) time, independently by [<PERSON><PERSON><PERSON><PERSON><PERSON>, 1982] and by [<PERSON><PERSON><PERSON> et al., 1985]. Very recently, independently and concurrently, using randomization, this runtime bound was further improved to Õ(n2) by [<PERSON><PERSON><PERSON>, 2024] and Õ(mn1/3) by [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>, 2024] (and subsequently to Õ(mn1/4) by [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>, 2024]). In this paper, we present a randomized algorithm that computes a (Δ+1)-edge coloring in near-linear time—in fact, only O(mlogΔ) time—with high probability, giving a near-optimal algorithm for this fundamental problem.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718265"}, {"primary_key": "197865", "vector": [], "sparse_vector": [], "title": "Covering Approximate Shortest Paths with DAGs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We define and study analogs of probabilistic tree embedding and tree cover for directed graphs. We define the notion of a DAG cover of a general directed graph G: a small collection D1,…, Dg of DAGs so that for all pairs of vertices s,t, some DAG Di provides low distortion for (s,t); i.e. Di(s, t) ≤ α · G(s, t), where α is the distortion. As a trivial upper bound, there is a DAG cover with n DAGs and α=1 by taking the shortest-paths tree from each vertex. When each DAG is restricted to be a subgraph of G, there is a simple matching lower bound (via a directed cycle) that n DAGs are necessary, even to preserve reachability. Thus, we allow the DAGs to include a limited number of additional edges not from the original graph. When n2 additional edges are allowed, there is a simple upper bound of two DAGs and α=1. Our first result is an almost-matching lower bound that even for n2−o(1) additional edges, at least n1−o(1) DAGs are needed, even to preserve reachability. However, the story is different when the number of additional edges is Õ(m), a natural setting where the sparsity of the DAG collection asymptotically matches that of the original graph. Our main upper bound is that there is a near-linear time algorithm to construct a DAG cover with Õ(m) additional edges, polylogarithmic distortion, and only O(logn) DAGs. This is similar to known results for undirected graphs: the well-known FRT probabilistic tree embedding implies a tree cover where both the number of trees and the distortion are logarithmic. Our algorithm also extends to a certain probabilistic embedding guarantee. Lastly, we complement our upper bound with a lower bound showing that achieving a DAG cover with no distortion and Õ(m) additional edges requires a polynomial number of DAGs.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718298"}, {"primary_key": "197866", "vector": [], "sparse_vector": [], "title": "Correlation Clustering and (De)Sparsification: Graph Sketches Can Match Classical Algorithms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Correlation clustering is a widely-used approach for clustering large data sets based only on pairwise similarity information. In recent years, there has been a steady stream of better and better classical algorithms for approximating this problem. Meanwhile, another line of research has focused on porting the classical advances to various sublinear algorithm models, including semi-streaming, Massively Parallel Computation (MPC), and distributed computing. Yet, these latter works typically rely on ad-hoc approaches that do not necessarily keep up with advances in approximation ratios achieved by classical algorithms. Hence, the motivating question for our work is this: can the gains made by classical algorithms for correlation clustering be ported over to sublinear algorithms in a black-box manner? We answer this question in the affirmative by introducing the paradigm of graph de-sparsification. A versatile approach for designing sublinear algorithms across various models is the graph (linear) sketching. It is known that one can find a cut sparsifier of a given graph—which approximately preserves cut structures—via graph sketching, and that this is sufficient information-theoretically for recovering a near-optimal correlation clustering solution. However, no efficient algorithms are known for this task as the resulting cut sparsifier is necessarily a weighted graph, and correlation clustering is known to be a distinctly harder problem on weighted graphs. Our main result is a randomized linear sketch of O(n) size for n-vertex graphs, from which one can recover with high probability an (α+o(1))-approximate correlation clustering in polynomial time, where α is the best approximation ratio of any polynomial time classical algorithm for (unweighted) correlation clustering. This is proved via our new de-sparsification result: we recover in polynomial-time from some O(n) size linear sketch of a graph G, an unweighted, simple graph that approximately preserves the cut structure of G. In fact we show that under some mild conditions, any spectral sparsifier of a graph G can be de-sparsified into an unweighted simple graph with nearly the same spectrum. We believe the de-sparsification paradigm is interesting in its own right as a way of reducing graph complexity when weighted version of a problem is harder than its unweighted version. Finally, we use our techniques to get efficient algorithms for correlation clustering that match the performance of best classical algorithms, in a variety of different models, including dynamic streaming, MPC, and distributed communication models.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718194"}, {"primary_key": "197867", "vector": [], "sparse_vector": [], "title": "Feasibly Constructive Proof of Schwartz-Zippel Lemma and the Complexity of Finding Hitting Sets.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Schwartz-Zippel Lemma states that if a low-degree multivariate polynomial with coefficients in a field is not zero everywhere in the field, then it has few roots on every finite subcube of the field. This fundamental fact about multivariate polynomials has found many applications in algorithms, complexity theory, coding theory, and combinatorics. We give a new proof of the lemma that offers some advantages over the standard proof. First, the new proof is more constructive than previously known proofs. For every given side-length of the cube, the proof constructs a polynomial-time computable and polynomial-time invertible surjection onto the set of roots in the cube. The domain of the surjection is tight, thus showing that the set of roots on the cube can be compressed. Second, the new proof can be formalised in Buss’ bounded arithmetic theory S21 for polynomial-time reasoning. One consequence of this is that the theory S21 + dWPHP(PV) for approximate counting can prove that the problem of verifying polynomial identities (PIT) can be solved by polynomial-size circuits. The same theory can also prove the existence of small hitting sets for any explicitly described class of polynomials of polynomial degree. To complete the picture we show that the existence of such hitting sets is equivalent to the surjective weak pigeonhole principle dWPHP(PV), over the theory S21. This is a contribution to a line of research studying the reverse mathematics of computational complexity. One consequence of this is that the problem of constructing small hitting sets for such classes is complete for the class APEPP of explicit construction problems whose totality follows from the probabilistic method. This class is also known and studied as the class of Range Avoidance Problems.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718276"}, {"primary_key": "197868", "vector": [], "sparse_vector": [], "title": "History-Independent Concurrent Hash Tables.", "authors": ["Hagit Attiya", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A history-independent data structure does not reveal the history of operations applied to it, only its current logical state, even if its internal state is examined. This paper studies history-independent concurrent dictionaries, in particular, hash tables, and establishes inherent bounds on their space requirements. This paper shows that there is a lock-free history-independent concurrent hash table, in which each memory cell stores two elements and two bits, based on <PERSON> hashing. Our implementation is linearizable, and uses the shared memory primitive LL/SC. The expected amortized step complexity of the hash table is O(c), where c is an upper bound on the number of concurrent operations that access the same element, assuming the hash table is not overpopulated. We complement this positive result by showing that even if we have only two concurrent processes, no history-independent concurrent dictionary that supports sets of any size, with wait-free membership queries and obstruction-free insertions and deletions, can store only two elements of the set and a constant number of bits in each memory cell. This holds even if the step complexity of operations on the dictionary is unbounded.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718283"}, {"primary_key": "197869", "vector": [], "sparse_vector": [], "title": "Hardness of 4-Colouring k-Colourable Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the complexity of a class of promise graph homomorphism problems. For a fixed graph H, the H-colouring problem is to decide whether a given graph has a homomorphism to H. By a result of <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, this problem is NP-hard for any non-bipartite loop-less graph H. <PERSON><PERSON> and <PERSON><PERSON><PERSON> [SODA 2018] conjectured the hardness extends to promise graph homomorphism problems as follows: fix a pair of non-bipartite loop-less graphs G, H such that there is a homomorphism from G to H, it is NP-hard to distinguish between graphs that are G-colourable and those that are not H-colourable. We confirm this conjecture in the cases when both G and H are 4-colourable. This is a common generalisation of previous results of <PERSON>na, Lin<PERSON>, and Safra [Comb. 20(3): 393-415 (2000)] and of <PERSON><PERSON><PERSON> and <PERSON><PERSON> [FOCS 2019]. The result is obtained by combining the algebraic approach to promise constraint satisfaction with methods of topological combinatorics and equivariant obstruction theory.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718154"}, {"primary_key": "197870", "vector": [], "sparse_vector": [], "title": "Stochastic Matching via In-n-Out Local Computation Algorithms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Consider the following stochastic matching problem. We are given a known graph G=(V, E). An unknown subgraph Gp = (V, Ep) is realized where Ep includes every edge of E independently with some probability p ∈ (0, 1]. The goal is to query a sparse subgraph H of G, such that the realized edges in H include an approximate maximum matching of Gp. This problem has been studied extensively over the last decade due to its applications in kidney exchange, online dating, and online labor markets. For any fixed є > 0, [BDH STOC’20] showed that any graph G has a subgraph H with (1/p) = (1/p)(log(1/p)) maximum degree, achieving a (1−є)-approximation. A major open question is the best approximation achievable with (1/p)-degree subgraphs. A long line of work has progressively improved the approximation in the (1/p)-degree regime from .5 [BDH+ EC’15] to .501 [AKL EC’17], .656 [BHFR SODA’19], .666 [AB SOSA’19], .731 [BBD SODA’22] (bipartite graphs), and most recently to .68 [DS ’24]. In this work, we show that a (1/p)-degree subgraph can obtain a (1−є)-approximation for any desirably small fixed є > 0, achieving the best of both worlds. Beyond its quantitative improvement, a key conceptual contribution of our work is to connect local computation algorithms (LCAs) to the stochastic matching problem for the first time. While prior work on LCAs mainly focuses on their out-queries (the number of vertices probed to produce the output of a given vertex), our analysis also bounds the in-queries (the number of vertices that probe a given vertex). We prove that the outputs of LCAs with bounded in- and out-queries (in-n-out LCAs for short) have limited correlation, a property that our analysis crucially relies on and might find applications beyond stochastic matchings.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718279"}, {"primary_key": "197871", "vector": [], "sparse_vector": [], "title": "Share-Based Fairness for Arbitrary Entitlements.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the problem of fair allocation of indivisible items to agents that have arbitrary entitlements to the items. Every agent i has a valuation function vi and an entitlement bi, where the entitlements sum up to 1. Which allocation should one choose in situations in which agents fail to agree on one acceptable fairness notion? We study this problem in the case in which each agent focuses on the value she gets, and fairness notions are restricted to be share based. A share s is a function that maps every (vi,bi) to a value s(vi,bi), representing the minimal value i should get, and s is feasible if it is always possible to give every agent i value of at least s(vi,bi). Our main result is that for additive valuations over goods, there is an allocation that gives every agent at least half her share value, regardless of which feasible share-based fairness notion the agent wishes to use. Moreover, the ratio of half is best possible. More generally, we provide tight characterizations of what can be achieved, both ex-post (as single allocations) and ex-ante (as expected values of distributions of allocations), both for goods and for chores. We also show that for chores one can achieve the ex-ante and ex-post guarantees simultaneously (a “best of both world” result), whereas for goods one cannot.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718119"}, {"primary_key": "197872", "vector": [], "sparse_vector": [], "title": "Rounding Large Independent Sets on Expanders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We develop a new approach for approximating large independent sets when the input graph is a one-sided spectral expander - that is, the uniform random walk matrix of the graph has its second eigenvalue bounded away from 1. Consequently, we obtain a polynomial time algorithm to find linear-sized independent sets in one-sided expanders that are almost 3-colorable or are promised to contain an independent set of size (1/2−є)n. Our second result above can be refined to require only a weaker vertex expansion property with an efficient certificate. In a surprising contrast to our algorithmic result, we observe that the analogous task of finding a linear-sized independent set in almost 4-colorable one-sided expanders (even when the second eigenvalue is on(1)) is NP-hard, assuming the Unique Games Conjecture. All prior algorithms that beat the worst-case guarantees for this problem rely on bottom eigenspace enumeration techniques (following the classical spectral methods of <PERSON><PERSON> and Kahale) and require two-sided expansion, meaning a bounded number of negative eigenvalues of magnitude Ω(1). Such techniques naturally extend to almost k-colorable graphs for any constant k, in contrast to analogous guarantees on one-sided expanders, which are Unique Games-hard to achieve for k ≥ 4. Our rounding scheme builds on the method of simulating multiple samples from a pseudo-distribution introduced in <PERSON>f<PERSON> et. al. for rounding Unique Games instances. The key to our analysis is a new clustering property of large independent sets in expanding graphs - every large independent set has a larger-than-expected intersection with some member of a small list - and its formalization in the low-degree sum-of-squares proof system.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718137"}, {"primary_key": "197873", "vector": [], "sparse_vector": [], "title": "Quasi-Linear Size PCPs with Small Soundness from HDX.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct 2-query, quasi-linear size probabilistically checkable proofs (PCPs) with arbitrarily small constant soundness, improving upon <PERSON><PERSON>’s 2-query quasi-linear size PCPs with soundness 1−Ω(1). As an immediate corollary, we get that under the exponential time hypothesis, for all >0 no approximation algorithm for 3-SAT can obtain an approximation ratio of 7/8+ in time 2n/logC n, where C is a constant depending on . Our result builds on a recent line of independent works by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, that showed the existence of linear size direct product testers with small soundness. The main new ingredient in our proof is a technique that embeds a given 2-CSP into a 2-CSP on a prescribed graph, provided that the latter is a graph underlying a sufficiently good high-dimensional expander (HDX). We achieve this by establishing a novel connection between PCPs and fault-tolerant distributed computing, more precisely, to the almost-everywhere reliable transmission problem introduced by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and Upfal (1986). We instantiate this connection by showing that graphs underlying HDXs admit routing protocols that are tolerant to adversarial edge corruptions, also improving upon the state of the art constructions of sparse edge-fault-tolerant networks in the process. Our PCP construction requires variants of the aforementioned direct product testers with poly-logarithmic degree. The existence and constructability of these variants is shown in the full version.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718197"}, {"primary_key": "197874", "vector": [], "sparse_vector": [], "title": "Constant Degree Networks for Almost-Everywhere Reliable Transmission.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the almost-everywhere reliable message transmission problem, introduced by [<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>’86], the goal is to design a sparse communication network G that supports efficient, fault-tolerant protocols for interactions between all node pairs. By fault-tolerant, we mean that that even if an adversary corrupts a small fraction of vertices in G, then all but a small fraction of vertices can still communicate perfectly via the constructed protocols. Being successful to do so allows one to simulate, on a sparse graph, any fault-tolerant distributed computing task and secure multi-party computation protocols built for a complete network, with only minimal overhead in efficiency. Previous works on this problem achieved either constant-degree networks tolerating o(1) faults, constant-degree networks tolerating a constant fraction of faults via inefficient protocols (exponential work complexity), or poly-logarithmic degree networks tolerating a constant fraction of faults. We show a construction of constant-degree networks with efficient protocols (i.e., with polylogarithmic work complexity) that can tolerate a constant fraction of adversarial faults, thus solving the main open problem of <PERSON><PERSON> et al.. Our main contribution is a composition technique for communication networks, based on graph products. Our technique combines two networks tolerant to adversarial edge-faults to construct a network with a smaller degree while maintaining efficiency and fault-tolerance. We apply this composition result multiple times, using the polylogarithmic-degree edge-fault tolerant networks constructed in a recent work of [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>’24] (that are based on high-dimensional expanders) with itself, and then with the constant-degree networks (albeit with inefficient protocols) of [Upfal’92].", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718170"}, {"primary_key": "197875", "vector": [], "sparse_vector": [], "title": "Near Optimal Constant Inapproximability under ETH for Fundamental Problems in Parameterized Complexity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> C. S.", "<PERSON><PERSON>"], "summary": "We prove that under the Exponential Time Hypothesis (ETH), for every ε > 0, there exists a constant C > 0 such that no algorithm running in time nk / logC k can determine whether a given 2-CSP instance with k variables, O(k) constraints, and alphabet size n, is perfectly satisfiable or if every assignment satisfies at most an ε fraction of the constraints. By known reductions in the literature, the above result implies near-optimal conditional lower bounds for approximating a host of parameterized problems, such as the k-Clique problem, k-Max-Coverage problem, k-Unique Set Cover problem, k-Median and k-Means problems, parameterized variants of the Nearest Codeword problem, Minimum Distance of a Code problem, Closest Vector problem, and Shortest Vector problem. We also establish a densification theorem for the parameterized 2-CSP problem, showing that the aforementioned conditional lower bound for sparse 2-CSPs also holds when the constraint graph is a complete graph. From this densification, we conclude that assuming ETH, there is no algorithm running in time n√k / logC k that approximates the k-Directed Steiner Network problem and the k-Strongly Connected Steiner Subgraph problem to some constant factors.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718257"}, {"primary_key": "197876", "vector": [], "sparse_vector": [], "title": "Learning the Closest Product State.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Ryan <PERSON>&<PERSON>;Donnell", "<PERSON><PERSON>"], "summary": "We study the problem of finding a product state with optimal fidelity to an unknown n-qubit quantum state ρ, given copies of ρ. This is a basic instance of a fundamental question in quantum learning: is it possible to efficiently learn a simple approximation to an arbitrary state? We give an algorithm which finds a product state with fidelity ε-close to optimal, using N = npoly(1/ε) copies of ρ and poly(N) classical overhead. We further show that estimating the optimal fidelity is NP-hard for error ε = 1/poly(n), showing that the error dependence cannot be significantly improved. For our algorithm, we build a carefully-defined cover over candidate product states, qubit by qubit, and then demonstrate that extending the cover can be reduced to approximate constrained polynomial optimization. For our proof of hardness, we give a formal reduction from polynomial optimization to finding the closest product state. Together, these results demonstrate a fundamental connection between these two seemingly unrelated questions. Building on our general approach, we also develop more efficient algorithms in three simpler settings: when the optimal fidelity exceeds 5/6; when we restrict ourselves to a discrete class of product states; and when we are allowed to output a matrix product state.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718207"}, {"primary_key": "197877", "vector": [], "sparse_vector": [], "title": "Extractors for Samplable Distributions with Low Min-Entropy.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (FOCS 2000) introduced the notion of (seedless) extractors for samplable distributions. They showed that under a very strong complexity theoretic hardness assumption, there are extractors for samplable distributions with large min-entropy of k=(1−γ) · n, for some small constant γ>0. Recent work by <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> (FOCS 2023) weakened the hardness assumption. However, since the original paper by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, there has been no improvement in the min-entropy threshold k. In this paper we give a construction of extractors for samplable distributions with low min-entropy of k=n1−γ for some constant γ>0, and in particular we achieve k0, and a problem in =(2O(n)) that cannot be computed by size 2β n circuits that have an oracle to Σ5¶. Our approach builds on the technique of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, while introducing new objects and ideas. We introduce and construct two objects: an errorless (seedless) condenser for samplable distributions, and functions that are hard to compute on every samplable distributions with sufficient min-entropy. We use techniques by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (STOC 2024), as well as additional tools and ideas, to construct the two new objects, under the hardness assumption. We then show how to modify the construction of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, using these new objects, so that the barrier of k=n/2 can be bypassed, and we can achieve an extractor for samplable distributions with low min-entropy.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718143"}, {"primary_key": "197878", "vector": [], "sparse_vector": [], "title": "Distributed Quantum Advantage for Local Problems.", "authors": ["Alki<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "Francesco D&apos;Amore", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the first local problem that shows a super-constant separation between the classical randomized LOCAL model of distributed computing and its quantum counterpart. By prior work, such a separation was known only for an artificial graph problem with an inherently global definition [<PERSON> et al. 2019]. We present a problem that we call iterated GHZ, which is defined using only local constraints. Formally, it is a family of locally checkable labeling problems [<PERSON><PERSON> and <PERSON> 1995]; in particular, solutions can be verified with a constant-round distributed algorithm. We show that in graphs of maximum degree $\\Delta$, any classical (deterministic or randomized) LOCAL model algorithm will require $\\Omega(\\Delta)$ rounds to solve the iterated GHZ problem, while the problem can be solved in $1$ round in quantum-LOCAL. We use the round elimination technique to prove that the iterated GHZ problem requires $\\Omega(\\Delta)$ rounds for classical algorithms. This is the first work that shows that round elimination is indeed able to separate the two models, and this also demonstrates that round elimination cannot be used to prove lower bounds for quantum-LOCAL. To apply round elimination, we introduce a new technique that allows us to discover appropriate problem relaxations in a mechanical way; it turns out that this new technique extends beyond the scope of the iterated GHZ problem and can be used to e.g. reproduce prior results on maximal matchings [FOCS 2019, PODC 2020] in a systematic manner.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718233"}, {"primary_key": "197879", "vector": [], "sparse_vector": [], "title": "Tensor Concentration Inequalities: A Geometric Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Matrix concentration inequalities, commonly used in the forms of non-commutative Khintchine inequalities or matrix Chernoff bounds, are central to a wide range of applications in computer science and mathematics. However, they fall short in many applications where tensor versions of these inequalities are needed. In this work, we study the ℓp injective norms of sums of independent tensors. We obtain the first non-trivial concentration inequalities in this setting, and our inequalities are nearly tight in certain regimes of p and the order of the tensors. Previously, tensor concentration inequalities were known only in the special cases of rank-1 tensors or p=2. Our results are obtained via a geometric argument based on estimating the covering numbers for the natural stochastic processes corresponding to tensor injective norms. Our approach is quite general and might be applicable to other settings of matrix and tensor concentration. We discuss applications and connections of our inequalities to various other problems, including tensor principle component analysis, various models of random tensors and matrices, type-2 constants of certain Banach spaces, and locally decodable codes.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718188"}, {"primary_key": "197880", "vector": [], "sparse_vector": [], "title": "Matrix Chaos Inequalities and Chaos of Combinatorial Type.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Matrix concentration inequalities and their recently discovered sharp counterparts provide powerful tools to bound the spectrum of random matrices whose entries are linear functions of independent random variables. However, in many applications in theoretical computer science and in other areas one encounters more general random matrix models, called matrix chaoses, whose entries are polynomials of independent random variables. Such models have often been studied on a case-by-case basis using ad-hoc methods that can yield suboptimal dimensional factors. In this paper we provide general matrix concentration inequalities for matrix chaoses, which enable the treatment of such models in a systematic manner. These inequalities are expressed in terms of flattenings of the coefficients of the matrix chaos. We further identify a special family of matrix chaoses of combinatorial type for which the flattening parameters can be computed mechanically by a simple rule. This allows us to provide a unified treatment of and improved bounds for matrix chaoses that arise in a variety of applications, including graph matrices, Khatri-Rao matrices, and matrices that arise in average case analysis of the sum-of-squares hierarchy.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718238"}, {"primary_key": "197881", "vector": [], "sparse_vector": [], "title": "Sandwiching Random Geometric Graphs and Erdos-Renyi with Applications: Sharp Thresholds, Robust Testing, and Enumeration.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The distribution RGG(n,Sd−1,p) is formed by sampling independent vectors {Vi}i = 1n uniformly on Sd−1 and placing an edge between pairs of vertices i and j for which ⟨ Vi,Vj⟩ ≥ τdp, where τdp is such that the expected density is p. Our main result is a poly-time implementable coupling between Erdős-Rényi and RGG such that G(n,p(1 − O(√np/d)))⊆ RGG(n,Sd−1,p)⊆ G(n,p(1 + O(√np/d))) edgewise with high probability when d≫ np. We apply the result to: 1) Sharp Thresholds: We show that for any monotone property having a sharp threshold with respect to the Erdős-<PERSON><PERSON> distribution and critical probability pnc, random geometric graphs also exhibit a sharp threshold when d≫ npnc, thus partially answering a question of Perkins. 2) Robust Testing: The coupling shows that testing between G(n,p) and RGG(n,Sd−1,p) with є n2p adversarially corrupted edges for any constant є>0 is information-theoretically impossible when d≫ np. We match this lower bound with an efficient (constant degree SoS) spectral refutation algorithm when d≪ np. 3) Enumeration: We show that the number of geometric graphs in dimension d is at least exp(dnlog−7n), recovering (up to the log factors) the sharp result of <PERSON><PERSON><PERSON>.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718125"}, {"primary_key": "197882", "vector": [], "sparse_vector": [], "title": "Near-Optimal Time-Sparsity Trade-Offs for Solving Noisy Linear Equations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a polynomial-time reduction from solving noisy linear equations over in dimension Θ(klogn/(logk,logq,loglogn)) with a uniformly random coefficient matrix to noisy linear equations over in dimension n where each row of the coefficient matrix has uniformly random support of size k. This allows us to deduce the hardness of sparse problems from their dense counterparts. In particular, we derive hardness results in the following canonical settings: • Assuming the ℓ-dimensional (dense) learning with errors () problem over a polynomial-size field takes time 2Ω(ℓ), k-sparse in dimension n takes time nΩ(k/(logk · (logk + loglogn))) . • Assuming the ℓ-dimensional (dense) learning parity with noise () problem over ℤ/2ℤ takes time 2Ω(ℓ/logℓ), k-sparse in dimension n takes time nΩ(k/(logk · (logk + loglogn)2)) . These running time lower bounds are nearly tight as both sparse problems can be solved in time nO(k), given sufficiently many samples. Our reduction allows us to derive several consequences in cryptography and the computational complexity of statistical problems. In addition, as a new application, we give a reduction from k-sparse LWE to noisy tensor completion. Concretely, composing the two reductions implies that order-k rank-2k−1 noisy tensor completion in ℝn⊗ k takes time nΩ(k/ logk · (logk + loglogn)), assuming the exponential hardness of standard worst-case lattice problems.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718284"}, {"primary_key": "197883", "vector": [], "sparse_vector": [], "title": "Tolerant Testing of Stabilizer States with a Polynomial Gap via a Generalized Uncertainty Relation.", "authors": ["Zongbo <PERSON>o", "<PERSON>", "<PERSON>"], "summary": "We prove a conjecture of <PERSON><PERSON><PERSON><PERSON> & Dutt on the existence of a tolerant stabilizer testing algorithm, and achieve an exponential improvement in the parameters of the tester. Key to our argument is a generalized uncertainty relation for sets of Pauli operators, based on the Lov<PERSON><PERSON> theta function.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718201"}, {"primary_key": "197884", "vector": [], "sparse_vector": [], "title": "Monotone Contractions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study functions f : [0, 1]d → [0, 1]d that are both monotone and contracting, and we consider the problem of finding an ε-approximate fixed point of f. We show that the problem lies in the complexity class UEOPL. We give an algorithm that finds an ε-approximate fixed point of a three-dimensional monotone contraction using O(log(1/ε)) queries to f. We also give a decomposition theorem that allows us to use this result to obtain an algorithm that finds an ε-approximate fixed point of a d-dimensional monotone contraction using O((c · log(1/ε))⌈ d / 3 ⌉) queries to f for some constant c. Moreover, each step of both of our algorithms takes time that is polynomial in the representation of f. These results are strictly better than the best-known results for functions that are only monotone, or only contracting. All of our results also apply to Shapley stochastic games, which are known to be reducible to the monotone contraction problem. Thus we put Shapley games in UEOPL, and we give a faster algorithm for approximating the value of a Shapley game.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718175"}, {"primary_key": "197885", "vector": [], "sparse_vector": [], "title": "Optimal Non-oblivious Open Addressing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A hash table is said to be open-addressed (or non-obliviously open-addressed) if it stores elements (and free slots) in an array with no additional metadata. Intuitively, open-addressed hash tables must incur a space-time tradeoff: The higher the load factor at which the hash table operates, the longer insertions/deletions/queries should take. In this paper, we show that no such tradeoff exists: It is possible to construct an open-addressed hash table that supports constant-time operations even when the hash table is entirely full. In fact, it is even possible to construct a version of this data structure that: (1) is dynamically resized so that the number of slots in memory that it uses, at any given moment, is the same as the number of elements it contains; (2) supports O(1)-time operations, not just in expectation, but with high probability; and (3) requires external access to just O(1) hash functions that are each just O(1)-wise independent. Our results complement a recent lower bound by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> showing that oblivious open-addressed hash tables must incur Ω(loglogε−1)-time operations. The hash tables in this paper are non-oblivious, which is why they are able to bypass the previous lower bound.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718215"}, {"primary_key": "197886", "vector": [], "sparse_vector": [], "title": "Matroid Products via Submodular Coupling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Bogl<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Balázs Ma<PERSON>", "<PERSON><PERSON>"], "summary": "The study of matroid products traces back to the 1970s, when <PERSON><PERSON><PERSON><PERSON> and <PERSON> studied the existence of various types of matroid products with different strengths. Among these, the tensor product is arguably the most important, which can be considered as an extension of the tensor product from linear algebra. However, <PERSON> showed that the tensor product of two matroids does not always exist. Over the following four decades, matroid products remained surprisingly underexplored, regaining attention only in recent years due to applications in tropical geometry, information theory, and the limit theory of matroids. In this paper, inspired by the concept of coupling in probability theory, we introduce the notion of coupling for matroids – or, more generally, for submodular set functions. This operation can be viewed as a relaxation of the tensor product. Unlike the tensor product, however, we prove that a coupling always exists for any two submodular functions and can be chosen to be increasing if the original functions are increasing. As a corollary, we show that two matroids always admit a matroid coupling, leading to a novel operation on matroids. Our construction is algorithmic, providing an oracle for the coupling matroid through a polynomial number of oracle calls to the original matroids. We apply this construction to derive new necessary conditions for matroid representability and establish connection between tensor products and <PERSON><PERSON><PERSON>’s inequality. In addition, we verify the existence of set functions that are universal with respect to a given property, meaning any set function over a finite domain with that property can be obtained as a quotient.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718152"}, {"primary_key": "197887", "vector": [], "sparse_vector": [], "title": "Computing Moment Polytopes of Tensors, with Applications in Algebraic Complexity and Quantum Information.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tensors play a central role in various areas of computer science and mathematics, such as algebraic complexity theory (matrix multiplication), quantum information theory (entanglement), and additive combinatorics (slice rank). Fundamental problems about tensors are strongly tied to well-known questions in computational complexity — such as the problem of determining the matrix multiplication exponent via asymptotic rank, and the stronger Strassen asymptotic rank conjecture, which has recently been intimately linked to a whole range of computational problems. Unlike matrices, which are often well understood through their rank, tensors have such intricate structure that understanding them (and aforementioned problems) requires information of a more subtle nature. The moment polytope, going back decades to work in symplectic geometry, invariant theory, and representation theory, is a mathematical object associated to any tensor that collects such ”rank-like” information. Their relevance has become apparent in several areas: (1) through applications in geometric complexity theory (GCT), (2) in the construction of functions in <PERSON><PERSON><PERSON>’s asymptotic spectrum of tensors, (3) as entanglement polytopes in quantum information theory, and (4) in optimization via scaling algorithms. Despite their fundamental role and interest from many angles, little is known about these polytopes, and in particular for tensors beyond ℂ2⊗ℂ2⊗ℂ2 and ℂ2⊗ℂ2⊗ℂ2⊗ℂ2 only sporadically have they been computed. Even less is known about the polytopes’ inclusions and separations (which are particularly relevant for applications). We give a new algorithm for computing moment polytopes of tensors (and in fact moment polytopes for a natural general class of reductive algebraic groups) based on a mathematical characterization of moment polytopes by <PERSON>. This algorithm enables us to compute moment polytopes of tensors of dimension an order of magnitude larger than previous methods, allowing us to compute with certainty, for the first time, all moment polytopes of tensors in ℂ3⊗ℂ3⊗ℂ3, and with high probability those in ℂ4⊗ℂ4⊗ℂ4. Towards an open problem in geometric complexity theory, we prove (guided by moment polytopes computed with our algorithm) separations between the moment polytopes of matrix multiplication tensors and unit tensors, showing in particular that the matrix multiplication moment polytopes are not maximal (i.e., not equal to the corresponding Kronecker polytopes). As a consequence of the above, we obtain a no-go result for a certain operational characterization of moment polytope inclusion, by proving that Strassen’s asymptotic restriction on tensors does not imply moment polytope inclusion. Finally, based on our algorithmic observations, we construct explicit (concise) non-free tensors in every format ℂn ⊗ ℂn ⊗ ℂn, thus solving a ”hay in a haystack” problem for this generic property that plays an important role in Strassen’s theory of asymptotic spectra.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718221"}, {"primary_key": "197888", "vector": [], "sparse_vector": [], "title": "Deterministic Dynamic Maximal Matching in Sublinear Update Time.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Thatchaphol <PERSON>"], "summary": "We give a fully dynamic deterministic algorithm for maintaining a maximal matching of an n-vertex graph in Õ(n8/9) amortized update time. This breaks the long-standing Ω(n)-update-time barrier on dense graphs, achievable by trivially scanning all incident vertices of the updated edge, and affirmatively answers a major open question repeatedly asked in the literature <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> [FOCS 2011], <PERSON><PERSON><PERSON>,<PERSON><PERSON>, <PERSON><PERSON> and <PERSON> [SODA 2018], <PERSON> [<PERSON>]. We also present a faster randomized algorithm against an adaptive adversary with Õ(n3/4) amortized update time. Our approach employs the edge degree constrained subgraph (EDCS), a central object for optimizing approximation ratio, in a completely novel way; we instead use it for maintaining a matching that matches all high degree vertices in sublinear update time so that it remains to handle low degree vertices rather straightforwardly. To optimize this approach, we employ tools never used in the dynamic matching literature prior to our work, including sublinear-time algorithms for matching high degree vertices, random walks on directed expanders, and the monotone Even-Shiloach tree for dynamic shortest paths.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718153"}, {"primary_key": "197889", "vector": [], "sparse_vector": [], "title": "Parallel Repetition for 3-Player XOR Games.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In a 3-XOR game G, the verifier samples a challenge (x,y,z)∼ µ where µ is a probability distribution over Σ×Γ×Φ, and a map t∶ Σ×Γ×Φ→A for a finite Abelian group A defining a constraint. The verifier sends the questions x, y and z to the players <PERSON>, <PERSON> and <PERSON> respectively, receives answers f(x), g(y) and h(z) that are elements in A and accepts if f(x)+g(y)+h(z) = t(x,y,z). The value, val(G), of the game is defined to be the maximum probability the verifier accepts over all players’ strategies. We show that if G is a 3-XOR game with value strictly less than 1, whose underlying distribution over questions µ does not admit Abelian embeddings into (ℤ,+), then the value of the n-fold repetition of G is exponentially decaying. That is, there exists c=c(G)>0 such that val(G⊗ n)≤ 2−cn. This extends a previous result of [<PERSON><PERSON>, FOCS 2023] showing exponential decay for the GHZ game. Our proof combines tools from additive combinatorics and tools from discrete Fourier analysis.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718190"}, {"primary_key": "197890", "vector": [], "sparse_vector": [], "title": "On Approximability of Satisfiable k-CSPs: V.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a framework of algorithm vs. hardness for all Max-CSPs and demonstrate it for a large class of predicates. This framework extends the work of <PERSON><PERSON><PERSON><PERSON> [STOC, 2008], who showed a similar result for almost satisfiable Max-CSPs. Our framework is based on a new hybrid approximation algorithm, which uses a combination of the Gaussian elimination technique (i.e., solving a system of linear equations over an Abelian group) and the semidefinite programming relaxation. We complement our algorithm with a matching dictator vs. quasirandom test that has perfect completeness. The analysis of our dictator vs. quasirandom test is based on a novel invariance principle, which we call the mixed invariance principle. Our mixed invariance principle is an extension of the invariance principle of <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> [Annals of Mathematics, 2010] which plays a crucial role in <PERSON><PERSON><PERSON><PERSON>’s work. The mixed invariance principle allows one to relate 3-wise correlations over discrete probability spaces with expectations over spaces that are a mixture of Guassian spaces and Abelian groups, and may be of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718127"}, {"primary_key": "197891", "vector": [], "sparse_vector": [], "title": "Fully Dynamic k-Median with Near-Optimal Update Time and Recourse.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Farokhnejad"], "summary": "In metric k-clustering, we are given as input a set of n points in a general metric space, and we have to pick k centers and cluster the input points around these chosen centers, so as to minimize an appropriate objective function. In recent years, significant effort has been devoted to the study of metric k-clustering problems in a dynamic setting, where the input keeps changing via updates (point insertions/deletions), and we have to maintain a good clustering throughout these updates [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, SODA’21; <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, SODA’23; <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, SODA’24; <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, FOCS’24; <PERSON> and <PERSON>, SODA’25]. The performance of such a dynamic algorithm is measured in terms of three parameters: (i) Approximation ratio, which signifies the quality of the maintained solution, (ii) Recourse, which signifies how stable the maintained solution is, and (iii) Update time, which signifies the efficiency of the algorithm. We consider a textbook metric k-clustering problem, metric k-median, where the objective is the sum of the distances of the points to their nearest centers. We design the first dynamic algorithm for this problem with near-optimal guarantees across all three performance measures (up to a constant factor in approximation ratio, and polylogarithmic factors in recourse and update time). Specifically, we obtain a O(1)-approximation algorithm for dynamic metric k-median with Õ(1) recourse and Õ(k) update time. Prior to our work, the state-of-the-art here was the recent result of [Bhattacharya, Costa, Garg, Lattanzi and Parotsidis, FOCS’24], who obtained O(є−1)-approximation ratio with Õ(kє) recourse and Õ(k1+є) update time. We achieve our results by carefully synthesizing the concept of robust centers introduced in [Fichtenberger, Lattanzi, Norouzi-Fard and Svensson, SODA’21] along with the randomized local search subroutine from [Bhattacharya, Costa, Garg, Lattanzi and Parotsidis, FOCS’24], in addition to several key technical insights of our own.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718176"}, {"primary_key": "197892", "vector": [], "sparse_vector": [], "title": "Reachability in One-Dimensional Pushdown Vector Addition Systems Is Decidable.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the model of one-dimensional Pushdown Vector Addition Systems (1-PVAS), a fundamental computational model simulating both recursive and concurrent behaviours. Our main result is decidability of the reachability problem for 1-PVAS, an important open problem investigated for at least a decade. In the algorithm we actually consider an equivalent model of Grammar Vector Addition Systems (GVAS). We prove the main result by showing that for every one-dimensional GVAS (1-GVAS) one can compute another 1-GVAS, which has the same reachability relation as the original one and additionally has the so-called thin property. Due to the work of <PERSON><PERSON> and <PERSON><PERSON><PERSON> from 2011, thin 1-GVAS have decidable reachability problem, therefore our construction implies decidability of the problem for all 1-GVAS. Moreover, we also show that if reachability in thin 1-GVAS can be decided in elementary time then also reachability in all 1-GVAS can be decided in elementary time.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718149"}, {"primary_key": "197893", "vector": [], "sparse_vector": [], "title": "Adaptive and Oblivious Statistical Adversaries Are Equivalent.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We resolve a fundamental question about the ability to perform a statistical task, such as learning, when an adversary corrupts the sample. Such adversaries are specified by the types of corruption they can make and their level of knowledge about the sample. The latter distinguishes between sample-adaptive adversaries which know the contents of the sample when choosing the corruption, and sample-oblivious adversaries, which do not. We prove that for all types of corruptions, sample-adaptive and sample-oblivious adversaries are equivalent up to polynomial factors in the sample size. This resolves the main open question introduced by <PERSON> et al. (COLT, 2022) and further explored in <PERSON><PERSON> et al. (FOCS, 2023). Specifically, consider any algorithm A that solves a statistical task even when a sample-oblivious adversary corrupts its input. We show that there is an algorithm A′ that solves the same task when the corresponding sample-adaptive adversary corrupts its input. The construction of A′ is simple and maintains the computational efficiency of A: It requests a polynomially larger sample than A uses and then runs A on a uniformly random subsample. One of our main technical tools is a new structural result relating two distributions defined on sunflowers which may be of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718193"}, {"primary_key": "197894", "vector": [], "sparse_vector": [], "title": "Agnostic Smoothed Online Learning.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Classical results in statistical learning typically consider two extreme data-generating models: i.i.d. instances from an unknown distribution, or fully adversarial instances, often much more challenging statistically. To bridge the gap between these models, recent work introduced the smoothed framework, in which at each iteration an adversary generates an instance from a distribution constrained to have density bounded by σ−1 compared to some fixed base measure µ. This framework interpolates between the i.i.d. and adversarial cases, depending on the value of σ. For the classical online prediction problem, most prior results in smoothed online learning rely on the arguably strong assumption that the base measure µ is known to the learner, contrasting with standard settings in the PAC learning or consistency literature. We consider the general agnostic problem in which the base measure is unknown and values are arbitrary. In this direction, <PERSON> et al. (2024) showed that empirical risk minimization has sublinear regret under the well-specified assumption. We propose an algorithm R-Cover based on recursive coverings which is the first to guarantee sublinear regret for agnostic smoothed online learning without prior knowledge of µ and without the well-specified assumption. For classification, we prove that R-Cover has adaptive regret", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718111"}, {"primary_key": "197895", "vector": [], "sparse_vector": [], "title": "Global vs. s-t Vertex Connectivity Beyond Sequential: Almost-Perfect Reductions and Near-Optimal Separations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sorrachai <PERSON>i"], "summary": "A recent breakthrough by [LNPSY STOC’21] showed that solving s-t vertex connectivity is sufficient (up to polylogarithmic factors) to solve (global) vertex connectivity in the sequential model. This raises a natural question: What is the relationship between s-t and global vertex connectivity in other computational models? In this paper, we demonstrate that the connection between global and s-t variants behaves very differently across computational models. In parallel and distributed models, we obtain almost tight reductions from global to s-t vertex connectivity. In PRAM, this leads to a nω+o(1)-work and no(1)-depth algorithm for vertex connectivity, improving over the 35-year-old Õ(nω+1)-work O(log2n)-depth algorithm by [LLW FOCS’86], where ω is the matrix multiplication exponent and n is the number of vertices. In CONGEST, the reduction implies the first sublinear-round vertex connectivity algorithm when the diameter is moderately small. This answers an open question in [JM STOC’23]. In contrast, we show that global vertex connectivity is strictly harder than s-t vertex connectivity in the two-party communication setting, requiring n1.5 bits of communication. The s-t variant was known to be solvable in Õ(n) communication [BvdBEMN FOCS’22]. Our results resolve open problems raised by [MN STOC’20, BvdBEMN FOCS’22, AS SOSA’23]. At the heart of our results is a new graph decomposition framework we call common-neighborhood clustering, which can be applied in multiple models. Finally, we observe that global vertex connectivity cannot be solved without using s-t vertex connectivity by proving an s-t to global reduction in dense graphs in the PRAM and communication models.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718316"}, {"primary_key": "197896", "vector": [], "sparse_vector": [], "title": "Õptimal Fault-Tolerant Labeling for Reachability and Approximate Distances in Directed Planar Graphs.", "authors": ["Itai Boneh", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a labeling scheme that assigns labels of size Õ(1) to the vertices of a directed weighted planar graph G, such that for any fixed ε>0 from the labels of any three vertices s, t and f one can determine in Õ(1) time a (1+ε)-approximation of the s-to-t distance in the graph G∖{f}. For approximate distance queries, prior to our work, no efficient solution existed, not even in the centralized oracle setting. Even for the easier case of reachability, Õ(1) queries were known only with a centralized oracle of size Õ(n) [SODA 21].", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718249"}, {"primary_key": "197897", "vector": [], "sparse_vector": [], "title": "Treewidth Inapproximability and Tight ETH Lower Bound.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present a simple, self-contained, linear reduction from 3-SAT to Treewidth. Specifically, it shows that 1.00005-approximating Treewidth is NP-hard, and solving Treewidth exactly requires $2^{\\Omega(n)}$ time, unless the Exponential-Time Hypothesis fails. We further derive, under the latter assumption, that there is some constant $\\delta > 1$ such that $\\delta$-approximating Treewidth requires time $2^{n^{1-o(1)}}$.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718117"}, {"primary_key": "197898", "vector": [], "sparse_vector": [], "title": "A 5/4-Approximation for Two-Edge Connectivity.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The 2-Edge-Connected Spanning Subgraph problem (2ECSS) is among the most basic survivable network design problems: given an undirected and unweighted graph, the task is to find a spanning subgraph with the minimum number of edges that is 2-edge-connected (i.e., it remains connected after the removal of any single edge). 2ECSS is an NP-hard problem that has been extensively studied in the context of approximation algorithms. The best-known approximation ratio for 2ECSS prior to this work was 1.3+ε, for any constant ε>0 [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>23; <PERSON>, <PERSON>’23]. In this paper, we present a 5/4-approximation algorithm. Our algorithm is also faster for small values of ε: its running time is nO(1) instead of nO(1/ε).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718275"}, {"primary_key": "197899", "vector": [], "sparse_vector": [], "title": "A General Quantum Duality for Representations of Groups with Applications to Quantum Money, Lightning, and Fire.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (2020) established that efficiently mapping between quantum states |ψ⟩ and |φ⟩ is computationally equivalent to distinguishing their superpositions 1/√2(|ψ⟩ + |φ⟩) and 1/√2(|ψ⟩ − |φ⟩). We generalize this insight into a broader duality principle in quantum computation, wherein manipulating quantum states in one basis is equivalent to extracting their value in a complementary basis. In its most general form, this duality principle states that for a given group, the ability to implement a unitary representation of the group is computationally equivalent to the ability to perform a Fourier extraction from the invariant subspaces corresponding to its irreducible representations. Building on our duality principle, we present the following applications: (1) Quantum money, which captures quantum states that are verifiable but unclonable, and its stronger variant, quantum lightning, have long resisted constructions based on concrete cryptographic assumptions. While (public-key) quantum money has been constructed from indistinguishability obfuscation (iO)—an assumption widely considered too strong—quantum lightning has not been constructed from any such assumptions, with previous attempts based on assumptions that were later broken. We present the first construction of quantum lightning with a rigorous security proof, grounded in a plausible and well-founded cryptographic assumption. We extend the construction of <PERSON><PERSON><PERSON> (2024) from Abelian group actions to non-Abelian group actions, and eliminate <PERSON><PERSON><PERSON>’s reliance on a black-box model for justifying security. Instead, we prove a direct reduction to a computational assumption – the pre-action security of cryptographic group actions. We show how these group actions can be realized with various instantiations, including with the group actions of the symmetric group implicit in the McEliece cryptosystem. (2) We provide an alternative quantum money and lightning construction from one-way homomorphisms, showing that security holds under specific conditions on the homomorphism. Notably, our scheme exhibits the remarkable property that four distinct security notions—quantum lightning security, security against both worst-case cloning and average-case cloning, and security against preparing a specific canonical state – are all equivalent. (3) Quantum fire captures the notion of a samplable distribution on quantum states that are efficiently clonable, but not efficiently telegraphable, meaning they cannot be efficiently encoded as classical information. These states can be spread like fire, provided they are kept alive quantumly and do not decohere. The only previously known construction relied on a unitary quantum oracle, whereas we present the first candidate construction of quantum fire using a classical oracle.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718195"}, {"primary_key": "197900", "vector": [], "sparse_vector": [], "title": "The State Hidden Subgroup Problem and an Efficient Algorithm for Locating Unentanglement.", "authors": ["<PERSON>", "<PERSON>-Tiron", "<PERSON>"], "summary": "We study a generalization of entanglement testing which we call the “hidden cut problem.” Taking as input copies of an n-qubit pure state which is product across an unknown bipartition, the goal is to learn precisely where the state is unentangled, i.e. to determine which of the exponentially many possible cuts separates the state. We give a polynomial-time quantum algorithm which can find the cut using O(n/є2) many copies of the state, which is optimal up to logarithmic factors. Our algorithm also generalizes to learn the entanglement structure of arbitrary product states. In the special case of Haar-random states, we further show that our algorithm requires circuits of only constant depth. To develop our algorithm, we introduce a state generalization of the hidden subgroup problem (StateHSP) which might be of independent interest, in which one is given a quantum state invariant under an unknown subgroup action, with the goal of learning the hidden symmetry subgroup. We show how the hidden cut problem can be formulated as a StateHSP with a carefully chosen Abelian group action. We then prove that Fourier sampling on the hidden cut state produces similar outcomes as a variant of the well-known <PERSON>’s problem, allowing us to find the hidden cut efficiently. Therefore, our algorithm can be interpreted as an extension of <PERSON>’s algorithm to entanglement testing. We discuss possible applications of StateHSP and hidden cut problems to cryptography and pseudorandomness.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718118"}, {"primary_key": "197901", "vector": [], "sparse_vector": [], "title": "Faster Distributed Δ-Coloring via Ruling Subgraphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>’ theorem states that all connected graphs but odd cycles and cliques can be colored with Δ colors, where Δ is the maximum degree of the graph. Such colorings have been shown to admit non-trivial distributed algorithms [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, Combinatorica 1995] and have been studied intensively in the distributed literature. In particular, it is known that any deterministic algorithm computing a Δ-coloring requires Ω(logn) rounds in the LOCAL model [<PERSON>, <PERSON>, and <PERSON>, FOCS 2016], and that this lower bound holds already on constant-degree graphs. In contrast, the best upper bound in this setting is given by an O(log2 n)-round deterministic algorithm that can be inferred already from the works of [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, FOCS 1989] and [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, Combinatorica 1995] roughly three decades ago, raising the fundamental question about the true complexity of Δ-coloring in the constant-degree setting. We answer this long-standing question almost completely by providing an almost-optimal deterministic O(logn log* n)-round algorithm for Δ-coloring, matching the lower bound up to a log* n-factor. Similarly, in the randomized LOCAL model, we provide an O(loglogn log* n)-round algorithm, improving over the state-of-the-art upper bound of O(log2 logn) [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, Di<PERSON>ributed Computing 2021] and almost matching the Ω(loglogn)-round lower bound by [<PERSON><PERSON>HKLR<PERSON>, STOC 2016]. Our results make progress on several important open problems and conjectures. One key ingredient for obtaining our results is the introduction of ruling subgraph families as a novel tool for breaking symmetry between substructures of a graph, which we expect to be of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718320"}, {"primary_key": "197902", "vector": [], "sparse_vector": [], "title": "Redundancy Is All You Need.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "The seminal work of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> demonstrated cut sparsifiers of near-linear size, with several applications throughout theoretical computer science. Subsequent extensions have yielded sparsifiers for hypergraph cuts and more recently linear codes over Abelian groups. A decade ago, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> asked about the sparsifiability of arbitrary constraint satisfaction problems (CSPs). For this question, a trivial lower bound is the size of a non-redundant CSP instance, which admits, for each constraint, an assignment satisfying only that constraint (so that no constraint can be dropped by the sparsifier). For instance, for graph cuts, spanning trees are non-redundant instances. Our main result is that redundant clauses are sufficient for sparsification: for any CSP predicate R, every unweighted instance of (R) has a sparsifier of size at most its non-redundancy (up to polylog factors). For weighted instances, we similarly pin down the sparsifiability to the so-called chain length of the predicate. These results precisely determine the extent to which any CSP can be sparsified. A key technical ingredient in our work is a novel application of the entropy method from <PERSON><PERSON>’s recent breakthrough on the union-closed sets conjecture. As an immediate consequence of our main theorem, a number of results in the non-redundancy literature immediately extend to CSP sparsification. We also contribute new techniques for understanding the non-redundancy of CSP predicates. In particular, we give an explicit family of predicates whose non-redundancy roughly corresponds to the structure of matching vector families in coding theory. By adapting methods from the matching vector codes literature, we are able to construct an explicit predicate whose non-redundancy lies between Ω(n1.5) and Oє(n1.6), the first example with a provably non-integral exponent.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718212"}, {"primary_key": "197903", "vector": [], "sparse_vector": [], "title": "Optimality of Frequency Moment Estimation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Estimating the second frequency moment of a stream up to (1±ε) multiplicative error requires at most O(logn / ε2) bits of space, due to a seminal result of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. It is also known that at least Ω(logn + 1/ε2) space is needed. We prove a tight lower bound of Ω(log(n ε2 ) / ε2) for all ε = Ω(1/√n). Note that when ε>n−1/2 + c, where c>0, our lower bound matches the classic upper bound of AMS. For smaller values of ε we also introduce a revised algorithm that improves the classic AMS bound and matches our lower bound. Our lower bound holds also for the more general problem of p-th frequency moment estimation for the range of p∈ (1,2], giving a tight bound in the only remaining range to settle the optimal space complexity of estimating frequency moments.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718171"}, {"primary_key": "197904", "vector": [], "sparse_vector": [], "title": "A Fine-Grained Classification of Subquadratic Patterns for Subgraph Listing and Friends.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In an m-edge host graph G, all triangles can be listed in time O(m1.5) [<PERSON><PERSON>, <PERSON><PERSON>78], and all k-cycles can be listed in time O(m2−1/⌈ k/2 ⌉ + t) where t is the output size [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON> ’97]. These classic results also hold for the colored problem variant, where the nodes of the host graph G are colored by nodes in the pattern graph H, and we are only interested in subgraphs of G that are isomorphic to the pattern H and respect the colors. We study the problem of listing all H-subgraphs in the colored setting, for fixed pattern graphs H. As our main result, we determine all pattern graphs H such that all H-subgraphs can be listed in subquadratic time O(m2−ε + t), where t is the output size. Moreover, for each such subquadratic pattern H we determine the smallest exponent c(H) such that all H-subgraphs can be listed in time O(mc(H) + t). This is a vast generalization of the classic results on triangles and cycles. In particular, it answers an open problem from the database community [Joglekar, Ré ’18]. We also show the same results for two related problems: finding an H-subgraph of minimum total edge-weight in time O(mc(H)), and enumerating all H-subgraphs in O(mc(H)) preprocessing time and constant delay. Again we determine all pattern graphs H that have complexity c(H) < 2, and for each such subquadratic pattern we determine the optimal complexity c(H). To prove these results, we design new algorithms and prove conditional lower bounds based on standard hypotheses from fine-grained complexity theory. For our algorithms we introduce a new ingredient that we call hyper-degree splitting, where we split tuples of nodes into high degree and low degree depending on their number of common neighbors. This technique generalizes the classic high-degree-low-degree approach (which only splits along degrees of single nodes) and is crucial to obtain optimal algorithms. Furthermore, we contribute the simple but fundamental insight that to determine the optimal complexity of some natural families of algorithms and lower bounds it suffices to study graphs without clique separators (i.e., cliques whose removal disconnects the pattern graph). This insight immediately implies some existing results in the area and is crucial to obtain a complete classification.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718141"}, {"primary_key": "197905", "vector": [], "sparse_vector": [], "title": "Extending the Extension: Deterministic Algorithm for Non-monotone Submodular Maximization.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Maximization of submodular functions under various constraints is a fundamental problem that has been extensively studied. A powerful technique that has emerged and has been shown to be extremely effective for such problems is the following. First, a continuous relaxation of the problem is obtained by relaxing the (discrete) set of feasible solutions to a convex body, and extending the discrete submodular function f to a continuous function F known as the multilinear extension. Then, two algorithmic steps are implemented. The first step approximately solves the relaxation by finding a fractional solution within the convex body that approximately maximizes F; and the second step rounds this fractional solution to a feasible integral solution. While this “fractionally solve and then round” approach has been a key technique for resolving many questions in the field, the main drawback of algorithms based on it is that evaluating the multilinear extension may require a number of value oracle queries to f that is exponential in the size of f’s ground set. The only known way to tackle this issue is to approximate F via sampling, which makes all algorithms based on this approach inherently randomized and quite slow. In this work, we introduce a new tool, that we refer to as the extended multilinear extension, designed to derandomize submodular maximization algorithms that are based on the successful “solve fractionally and then round” approach. We demonstrate the effectiveness of this new tool on the fundamental problem of maximizing a submodular function subject to a matroid constraint, and show that it allows for a deterministic implementation of both the fractionally solving step and the rounding step of the above approach. As a bonus, we also get a randomized algorithm for the problem with an improved query complexity.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718120"}, {"primary_key": "197906", "vector": [], "sparse_vector": [], "title": "Solving the Correlation Cluster LP in Sublinear Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shuyi Yan", "<PERSON><PERSON>"], "summary": "Correlation Clustering is a fundamental and widely-studied problem in unsupervised learning and data mining. The input is a graph and the goal is to construct a clustering minimizing the number of inter-cluster edges plus the number of missing intra-cluster edges. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON> [STOC 2024] introduced the cluster LP for Correlation Clustering, which they argued captures the problem much more succinctly than previous linear programming formulations. However, the cluster LP has exponential size, with a variable for every possible set of vertices in the input graph. Nevertheless, they showed how to find a feasible solution for the cluster LP in time O(npoly(1/ε)) with objective value at most (1+ε) times the value of an optimal solution for the respective Correlation Clustering instance. Furthermore, they showed how to round a solution to the cluster LP, yielding a (1.437+ε)-approximation algorithm for the Correlation Clustering problem. The main technical result of this paper is a new approach to find a feasible solution for the cluster LP with objective value at most (1+ε) of the optimum in time O(2poly(1/ε) n), where n is the number of vertices in the graph. We also show how to implement the rounding within the same time bounds, thus achieving a fast (1.437+ε)-approximation algorithm for the Correlation Clustering problem. This bridges the gap between state-of-the-art methods for approximating Correlation Clustering and the recent focus on fast algorithms.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718181"}, {"primary_key": "197907", "vector": [], "sparse_vector": [], "title": "Network Unreliability in Almost-Linear Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The network unreliability problem asks for the probability that a given undirected graph gets disconnected when every edge independently fails with a given probability p. <PERSON> (1979) showed that this problem is #P-hard; therefore, the best we can hope for are approximation algorithms. In a classic result, <PERSON><PERSON> (1995) obtained the first FPRAS for this problem by leveraging the fact that when a graph disconnects, it almost always does so at a near-minimum cut, and there are only a small (polynomial) number of near-minimum cuts. Since then, a series of results have obtained progressively faster algorithms to the current bound of m1+o(1) + Õ(n3/2) (<PERSON>, <PERSON>, <PERSON>, and <PERSON>, 2024). In this paper, we obtain an m1+o(1)-time algorithm for the network unreliability problem. This is essentially optimal, since we need O(m) time to read the input graph. Our main new ingredient is relating network unreliability to an ideal tree packing of spanning trees (<PERSON><PERSON>, 2001).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718140"}, {"primary_key": "197908", "vector": [], "sparse_vector": [], "title": "Output-Sensitive Approximate Counting via a Measure-Bounded Hyperedge Oracle, or: How Asymmetry Helps Estimate k-Clique Counts Faster.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Virginia Vassilevska Williams"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [DLM SICOMP 2022] presented a general reduction from approximate counting to decision for a class of fine-grained problems that can be viewed as hyperedge counting or detection problems in an implicit hypergraph, thus obtaining tight equivalences between approximate counting and decision for many key problems such as k-clique, k-sum and more. Their result is a reduction from approximately counting the number of hyperedges in an implicit k-partite hypergraph to a polylogarithmic number of calls to a hyperedge oracle that returns whether a given subhypergraph contains an edge. The main result of this paper is a generalization of the DLM result for output-sensitive approximate counting, where the running time of the desired counting algorithm is inversely proportional to the number of witnesses. Our theorem is a reduction from approximately counting the (unknown) number of hyperedges in an implicit k-partite hypergraph to a polylogarithmic number of calls to a hyperedge oracle called only on subhypergraphs with a small “measure”. If a subhypergraph has ui nodes in the ith node partition of the k-partite hypergraph, then its measure is ∏i ui. Using the new general reduction and by efficiently implementing measure-bounded colorful independence oracles, we obtain new improved output-sensitive approximate counting algorithms for k-clique, k-dominating set and k-sum. In graphs with nt k-cliques, for instance, our algorithm (1± є)-approximates the k-clique count in time Õє(nω(k−t−1/3,k−t/3,k−t+2/3) +n2), where ω(a,b,c) is the exponent of na× nb by nb× nc matrix multiplication. For large k and t>2, this is a substantial improvement over prior work, even if ω=2.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718314"}, {"primary_key": "197909", "vector": [], "sparse_vector": [], "title": "Quantum Advantage from Soft Decoders.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the last years, <PERSON><PERSON>'s reduction has been used as a quantum algorithmic tool for providing a quantum advantage for variants of the decoding problem. Following this line of work, the authors of [JSW+24] have recently come up with a quantum algorithm called Decoded Quantum Interferometry that is able to solve in polynomial time several optimization problems. They study in particular the Optimal Polynomial Interpolation (OPI) problem, which can be seen as a decoding problem on Reed-Solomon codes. In this work, we provide strong improvements for some instantiations of the OPI problem. The most notable improvements are for the $ISIS_{\\infty}$ problem (originating from lattice-based cryptography) on Reed-Solomon codes but we also study different constraints for OPI. Our results provide natural and convincing decoding problems for which we believe to have a quantum advantage. Our proof techniques involve the use of a soft decoder for Reed-Solomon codes, namely the decoding algorithm from Koetter and Vardy [KV03]. In order to be able to use this decoder in the setting of <PERSON><PERSON>'s reduction, we provide a novel generic reduction from a syndrome decoding problem to a coset sampling problem, providing a powerful and simple to use theorem, which generalizes previous work and is of independent interest. We also provide an extensive study of OPI using the <PERSON><PERSON><PERSON> and <PERSON>ardy algorithm.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718319"}, {"primary_key": "197910", "vector": [], "sparse_vector": [], "title": "Monotonicity Testing of High-Dimensional Distributions with Subcube Conditioning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study monotonicity testing of high-dimensional distributions on {−1,1}n in the model of subcube conditioning, suggested and studied by <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. Previous work shows that the sample complexity of monotonicity testing must be exponential in n (<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Yo<PERSON><PERSON>). We show that the subcube query complexity is Θ(n/є2), by proving nearly matching upper and lower bounds. Our work is the first to use directed isoperimetric inequalities (developed for function monotonicity testing) for analyzing a distribution testing algorithm. Along the way, we generalize an inequality of <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> to real-valued functions on {−1,1}n. We also study uniformity testing of distributions that are promised to be monotone, a problem introduced by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, using subcube conditioning. We show that the query complexity is Θ(√n/є2). Our work proves the lower bound, which matches (up to poly-logarithmic factors) the uniformity testing upper bound for general distributions (<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>). Hence, we show that monotonicity does not help, beyond logarithmic factors, in testing uniformity of distributions with subcube conditional queries.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718297"}, {"primary_key": "197911", "vector": [], "sparse_vector": [], "title": "How Random CSPs Fool Hierarchies: II.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Relaxations for the constraint satisfaction problem (CSP) include bounded width (BW), linear program (LP), semidefinite program (SDP), affine integer program (AIP), and their combinations. Tightening relaxations systematically leads to hierarchies and stronger algorithms. For LP+AIP and SDP+AIP hierarchies, various lower bounds were shown by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (STOC 2023, STOC 2024) and by <PERSON>, <PERSON>, and <PERSON> (STOC 2024). This paper continues on this line of work to show lower bounds for related hierarchies. We prove new lower bounds to BW and AIP hierarchies. We also show the first lower bounds to the cohomological consistency hierarchy of <PERSON> (MFCS 2022) and the C(BLP+AIP) hierarchy of <PERSON>iard<PERSON> and <PERSON><PERSON><PERSON><PERSON> (SODA 2022). Our lower bounds are for linear level and optimal. They make partial progress towards an open question of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (arXiv 2407.09097v1) concerning the power of these hierarchies. We prove our results using new closure and boundary. We generalize closure and boundary to streamline proofs across hierarchies.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718301"}, {"primary_key": "197912", "vector": [], "sparse_vector": [], "title": "Learning the <PERSON><PERSON><PERSON>-<PERSON><PERSON> Model Even at Low Temperature.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the fundamental problem of learning the parameters of an undirected graphical model or Markov Random Field (MRF) in the setting where the edge weights are chosen at random. For Ising models, we show that a multiplicative-weight update algorithm due to <PERSON><PERSON><PERSON> and <PERSON><PERSON> learns the parameters in polynomial time for any inverse temperature β ≤ √logn. This immediately yields an algorithm for learning the <PERSON><PERSON><PERSON>-<PERSON> (SK) model beyond the high-temperature regime of β < 1. Prior work breaks down at β = 1 and requires heavy machinery from statistical physics or functional inequalities. In contrast, our analysis is relatively simple and uses only subgaussian concentration. Our results extend to MRFs of higher order (such as pure p-spin models), where even results in the high-temperature regime were not known.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718232"}, {"primary_key": "197913", "vector": [], "sparse_vector": [], "title": "Light Tree Covers, Routing, and Path-Reporting Oracles via Spanning Tree Covers in Doubling Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A (1+ε)-stretch tree cover of an edge-weighted n-vertex graph G is a collection of trees, where every pair of vertices has a (1+ε)-stretch path in one of the trees. The celebrated Dumbbell Theorem by <PERSON><PERSON> et. al. [STOC’95] states that any set of n points in d-dimensional Euclidean space admits a (1+ε)-stretch tree cover with a constant number of trees, where the constant depends on ε and the dimension d. This result was generalized for arbitrary doubling metrics by <PERSON><PERSON> et. al. [ICALP’19]. While the total number of edges in the tree covers of <PERSON><PERSON> et. al. and <PERSON><PERSON> et. al. is O(n), all known tree cover constructions incur a total lightness of Ω(logn); whether one can get a tree cover of constant lightness has remained a longstanding open question, even for 2-dimensional point sets. In this work we resolve this fundamental question in the affirmative, as a direct corollary of a new construction of (1+ε)-stretch spanning tree cover for doubling graphs; in a spanning tree cover, every tree may only use edges of the input graph rather than the corresponding metric. To the best of our knowledge, this is the first constant-stretch spanning tree cover construction (let alone for (1+ε)-stretch) with a constant number of trees, for any nontrivial family of graphs. Concrete applications of our spanning tree cover include: - A (1+ε)-stretch tree cover construction, where both the number of trees and lightness are bounded by O(1), for doubling graphs. In doubling metrics, we can also bound the maximum degree of each vertex by O(1) (which is impossible in doubling graphs). - A compact (1+ε)-stretch routing scheme in the labeled model for doubling graphs, which uses the asymptotically optimal (up to the dependencies on ε and d) bound of O(logn) bits on all the involved measures (label, header, and routing tables sizes). This is a significant improvement over the works of Chan et. al. [SODA’05], Abraham et. al. [ICDCS’06], Konjevod et. al. [SODA’07], where the local memory usage either depends on the aspect ratio of the graph or is Ω(log3 n). - The first path-reporting distance oracle for doubling graphs achieving optimal bounds for all important parameters: O(n) space, (1+ε)-stretch, and O(1) query time for constant d and ε.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718312"}, {"primary_key": "197914", "vector": [], "sparse_vector": [], "title": "Optimal Rounding for Sparsest Cut.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We prove that the integrality gap of the <PERSON><PERSON>s–Linial semidefinite program for the Sparsest Cut problem (with general capacities and demands) on inputs of size n≥ 2 is Θ(√logn). We achieve this by establishing the following geometric/structural result. If (M,d) is an n-point metric space of negative type, then for every τ>0 there is a random subset Z of M such that for any pair of points x,y∈ M with d(x,y)≥ τ, the probability that both x∈ Z and d(y,Z)≥ βτ/√1+log(|B(y,κ β τ)|/|B(y,β τ)|) is Ω(1), where 0<β<1<κ are universal constants. The proof relies on a refinement of the <PERSON><PERSON><PERSON><PERSON> rounding technique.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718285"}, {"primary_key": "197915", "vector": [], "sparse_vector": [], "title": "Six Candidates Suffice to Win a Voter Majority.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A cornerstone of social choice theory is <PERSON><PERSON><PERSON><PERSON>’s paradox which says that in an election where n voters rank m candidates it is possible that, no matter which candidate is declared the winner, a majority of voters would have preferred an alternative candidate. Instead, can we always choose a small committee of winning candidates that is preferred to any alternative candidate by a majority of voters? <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> raised this question and called such a committee a <PERSON><PERSON><PERSON>t winning set. They showed that winning sets of size 2 may not exist, but sets of size logarithmic in the number of candidates always do. In this work, we show that Condorcet winning sets of size 6 always exist, regardless of the number of candidates or the number of voters. More generally, we show that if α/1 − lnα ≥ 2/k + 1, then there always exists a committee of size k such that less than an α fraction of the voters prefer an alternate candidate. These are the first nontrivial positive results that apply for all k ≥ 2. Our proof uses the probabilistic method and the minimax theorem, inspired by recent work on approximately stable committee selection. We construct a distribution over committees that performs sufficiently well (when compared against any candidate on any small subset of the voters) so that this distribution must contain a committee with the desired property in its support.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718235"}, {"primary_key": "197916", "vector": [], "sparse_vector": [], "title": "Uncloneable Quantum States Are Necessary as Proofs and Advice.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Uncloneability is one of the most remarkable properties of quantum states that separates them from classical information. Over the years, this property has found numerous uses in cryptography — many tasks that are classically impossible become possible in quantum cryptography due to uncloneability. In this work, we examine the utility of uncloneable quantum states in complexity theory, specifically in the form of proofs and advice. We define and study languages that necessarily need uncloneable quantum proofs and advice. Specifically, we define strictly uncloneable versions of the classes QMA, BQP/qpoly and FEQP/qpoly (which is the class of relational problems solvable exactly with polynomial-sized quantum advice). Strictly uncloneable QMA is defined to be the class of languages in QMA that only have uncloneable proofs, i.e., given any family of candidate proof states, a polynomial-time cloning algorithm cannot act on it to produce states that are jointly usable by k separate polynomial-time verifiers, for arbitrary polynomial k. This is a stronger notion of uncloneable proofs and advice than those considered in previous works, which only required the existence of a single family of proof or advice states that are uncloneable. We show that in the quantum oracle model, there exist languages in strictly uncloneable QMA and strictly uncloneable BQP/qpoly. The language in strictly uncloneable QMA also gives a quantum oracle separation between QMA and the class cloneableQMA introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2024). We also show without using any oracles that the language, used by <PERSON><PERSON>, <PERSON> and <PERSON> (2024) to separate FEQP/qpoly and FBQP/poly, is in strictly uncloneable FEQP/qpoly.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718226"}, {"primary_key": "197917", "vector": [], "sparse_vector": [], "title": "Leakage-Resilient Extractors against Number-on-Forehead Protocols.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Given a sequence of N independent sources X1,X2,…,XN∼{0,1}n, how many of them must be good (i.e., contain some min-entropy) in order to extract a uniformly random string? This question was first raised by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON> (STOC ’20), motivated by applications in cryptography, distributed computing, and the unreliable nature of real-world sources of randomness. In their paper, they showed how to construct explicit low-error extractors for just K ≥ N1/2 good sources of polylogarithmic min-entropy. In a follow-up, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> improved the number of good sources required to just K ≥ N0.01 (FOCS ’21). In this paper, we finally achieve K=3. Our key ingredient is a near-optimal explicit construction of a new pseudorandom primitive, called a leakage-resilient extractor (LRE) against number-on-forehead (NOF) protocols. Our LRE can be viewed as a significantly more robust version of <PERSON>’s low-error three-source extractor (FOCS ’15), and resolves an open question put forth by <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (FOCS ’19) and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (FOCS ’20). Our LRE construction is based on a simple new connection we discover between multiparty communication complexity and non-malleable extractors, which shows that such extractors exhibit strong average-case lower bounds against NOF protocols.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718272"}, {"primary_key": "197918", "vector": [], "sparse_vector": [], "title": "Explicit Folded Reed-Solomon and Multiplicity Codes Achieve Relaxed Generalized Singleton Bounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we prove that explicit folded Reed–Solomon (RS) codes and univariate multiplicity codes achieve relaxed generalized Singleton bounds for list size L≥1. Specifically, we show the following: (1) Any folded RS code of block length n and rate R over the alphabet Fqs with distinct evaluation points is (L/L+1(1−sR/s−L+1),L) (average-radius) list-decodable for list size L∈[s]. (2) Any univariate multiplicity code of block length n and rate R over the alphabet Fps (where p is a prime) with distinct evaluation points is (L/L+1(1−sR/s−L+1),L) (average-radius) list-decodable for list size L∈[s]. Choosing s=Θ(1/є2) and L=O(1/є), our results imply that both explicit folded RS codes and explicit univariate multiplicity codes achieve list-decoding capacity 1−R−є with optimal list size O(1/є). This exponentially improves the previous state of the art (1/є)O(1/є) established by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (FOCS 2018 or SICOMP, 2023) and <PERSON><PERSON> (IEEE TIT, 2024). In particular, our results on folded Reed–Solomon codes fully resolve a long-standing open problem originally proposed by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC 2006 or IEEE TIT, 2008). Furthermore, our results imply the first explicit constructions of (1−R−є,O(1/є)) (average-radius) list-decodable codes of rate R with polynomial-sized alphabets in the literature. Our methodology can also analyze the list-recoverability of folded RS codes. We provide a tighter radius upper bound that states folded RS codes cannot be (L+1−ℓ/L+1(1−mR/m−1)+o(1), ℓ, L) list-recoverable where m=⌈logℓ(L+1)⌉>1. We conjecture this bound is almost tight when L+1=ℓa for any a∈ℕ≥ 2. To give some evidences, we show folded RS codes over the alphabet Fqs are (1/2−sR/s−2,2,3) list-recoverable, which proves the tightness of this bound in the smallest non-trivial special case. As a corollary, our bound states (folded) RS codes cannot be (1−R−є, ℓ, ℓoR(1/є)) list-recoverable, which refutes the possibility that these codes could achieve list-recovery capacity (1−R−є, ℓ, O(ℓ/є)). This implies an intrinsic separation between list-decodability and list-recoverablility of (folded) RS codes.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718114"}, {"primary_key": "197919", "vector": [], "sparse_vector": [], "title": "Long Arithmetic Progressions in Sumsets and Subset Sums: Constructive Proofs and Efficient Witnesses.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Existence of long arithmetic progression in sumsets and subset sums has been studied extensively in additive combinatorics. These results play a central role in the recent progress of fundamental problems in theoretical computer science including Knapsack and Subset Sum. The non-constructiveness of relevant additive combinatorics results affects their application in algorithms. In particular, additive combinatorics-based algorithms for Subset Sum, including an Õ(n)-time algorithm for dense subset sum [<PERSON><PERSON> and <PERSON> ’21] and an Õ(n + √amaxt)-time algorithm [<PERSON>, <PERSON>, <PERSON>, and <PERSON>24], work only for the decision version of the problem, but not for the search version. To find a solution, one has to spend a lot more time. In this paper, we provide constructive proofs for finite addition theorems [Sárközy’89 ’94], which are fundamental results in additive combinatorics concerning the existence of long arithmetic progression in sumsets and subset sums. Our constructive proofs yield a near-linear time algorithm that returns an arithmetic progression explicitly, and moreover, for each term in the arithmetic progression, it also returns its representation as the sum of elements in the base set. As an application, we can obtain an Õ(n)-time algorithm for the search version of dense subset sum now. Another application of our result is Unbounded Subset Sum, where each input integer can be used an infinite number of times. A classic result on the Frobenius problem [<PERSON><PERSON><PERSON><PERSON> and <PERSON>72] implies that for all t ≥ 2amax2/n, the decision version can be solved trivially in linear time. It remains unknown whether the search version can be solved in the same time. Our result implies that for all t ≥ camax2/n for some constant c, a solution for Unbounded Subset Sum can be obtained in O(n logamax) time. The major technical challenge is that the original proofs for the above-mentioned additive combinatorics results heavily rely on two fundamental theorems, Mann’s theorem and Kneser’s theorem. These two theorems constitute the main non-constructive part. To bypass these two obstacles, we introduce two techniques. (i). A new set operation called greedy sumset. Greedy sumset computes a moderately large subset of the traditional sumset, but enjoys the advantage that searching for a representation for elements in the greedy sumset can be done efficiently. (ii). A framework that can be used to iteratively augment an arithmetic progression. It plays the role of Kneser’s theorem in the proof but enjoys the advantage that the representation of elements in the arithmetic progression can be efficiently traced.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718281"}, {"primary_key": "197920", "vector": [], "sparse_vector": [], "title": "Rapid Mixing at the Uniqueness Threshold.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Over the past decades, a fascinating computational phase transition has been identified in sampling from Gibbs distributions. Specifically, for the hardcore model on graphs with n vertices and maximum degree Δ, the computational complexity of sampling from the Gibbs distribution, defined over the independent sets of the graph with vertex-weight λ>0, undergoes a sharp transition at the critical threshold λc(Δ) := (Δ−1)Δ−1/(Δ−2)Δ, known as the tree-uniqueness threshold: In the uniqueness regime where λλc(Δ), the Glauber dynamics exhibits exponential mixing time; furthermore, the sampling problem becomes intractable unless RP=NP. The computational complexity at the critical point λ = λc(Δ) remains poorly understood, as previous algorithmic and hardness results all required a constant slack from this threshold. In this paper, we resolve this open question at the critical phase transition threshold, thus completing the picture of the computational phase transition. We show that for the hardcore model on graphs with maximum degree Δ≥ 3 at the uniqueness threshold λ = λc(Δ), the mixing time of Glauber dynamics is upper bounded by a polynomial in n, but is not nearly linear in the worst case: specifically, it falls between Õ(n(2+4e)+O(1/Δ)) and Ω(n4/3). For the Ising model (either antiferromagnetic or ferromagnetic), we establish similar results. For the Ising model on graphs with maximum degree Δ≥ 3 at the critical temperature β where |β| = βc(Δ), with the tree-uniqueness threshold βc(Δ) defined by (Δ−1)tanhβc(Δ)=1, we show that the mixing time of Glauber dynamics is upper bounded by Õ(n2 + O(1/Δ)) and lower bounded by Ω(n3/2) in the worst case. For the Ising model specified by a critical interaction matrix J with ∥ J ∥2=1, we obtain an upper bound Õ(n3/2) for the mixing time, matching the lower bound Ω(n3/2) on the complete graph up to a logarithmic factor. Our mixing time upper bounds hold regardless of whether the maximum degree Δ is constant. These bounds are derived from a new interpretation and analysis of the localization scheme method introduced by Chen and Eldan, applied to the field dynamics for the hardcore model and the proximal sampler for the Ising model. As key steps in both our upper and lower bounds, we establish sub-linear upper and lower bounds for spectral independence at the critical point for worst-case instances.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718260"}, {"primary_key": "197921", "vector": [], "sparse_vector": [], "title": "Unambiguous SNARGs for P from LWE with Applications to PPAD Hardness.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Zhengzhong Jin", "<PERSON>"], "summary": "We construct the first unambiguous succinct non-interactive arguments (SNARGs) for P and incrementally verifiable computation (IVC) for P from the polynomial hardness of learning with errors (LWE). Unambiguity guarantees that it is computationally hard to find two distinct accepting proofs for the same statement. As an application, we establish the first PPAD hardness result based on the polynomial hardness of LWE combined with a widely believed complexity assumption. Central to our approach is a new notion of rate-1 witness-unambiguous batch arguments for NP, which we give the first construction from the polynomial hardness of LWE. This notion may be of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718159"}, {"primary_key": "197922", "vector": [], "sparse_vector": [], "title": "Stabilizer Bootstrapping: A Recipe for Efficient Agnostic Tomography and Magic Estimation.", "authors": ["<PERSON><PERSON>", "Weiyuan Gong", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the task of agnostic tomography: given copies of an unknown n-qubit state ρ which has fidelity τ with some state in a given class C, find a state which has fidelity ≥ τ − є with ρ. We give a new framework, stabilizer bootstrapping, for designing computationally efficient protocols for this task, and use this to get new agnostic tomography protocols for the following classes: Stabilizer states: We give a protocol that runs in time poly(n,1/є)· (1/τ)O(log(1/τ)), answering an open question posed by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON>. Previous protocols ran in time exp(Θ(n)) or required τ>cos2(π/8). States with stabilizer dimension n − t: We give a protocol that runs in time n3·(2t/τ)O(log(1/є)), extending recent work on learning quantum states prepared by circuits with few non-Clifford gates, which only applied in the realizable setting where τ = 1. Discrete product states: If C = K⊗ n for some µ-separated discrete set K of single-qubit states, we give a protocol that runs in time (n/µ)O((1 + log(1/τ))/µ)/є2. This strictly generalizes a prior guarantee which applied to stabilizer product states. For stabilizer product states, we give a further improved protocol that runs in time (n2/є2)· (1/τ)O(log(1/τ)). As a corollary, we give the first protocol for estimating stabilizer fidelity, a standard measure of magic for quantum states, to error є in n3 quasipoly(1/є) time.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718191"}, {"primary_key": "197923", "vector": [], "sparse_vector": [], "title": "Succinct Non-interactive Arguments of Proximity.", "authors": ["<PERSON><PERSON>", "Zhengzhong Jin", "<PERSON>"], "summary": "We study succinct non-interactive arguments of proximity (SNAP), which allow a prover to convince a verifier that a statement is true through a short message. Moreover, the verifier reads only a sublinear number of bits of the statement, and soundness is required to hold against polynomial-time adversaries when the statement is є-far from any true statements. SNAPs can be seen as the natural analog of property testing in the context of succinct non-interactive arguments (SNARGs). We obtain both positive and negative results for SNAPs. For any є ∈ (0, 1), we construct the first adaptively sound SNAPs for P with є-proximity based on standard assumptions: LWE or subexponential DDH or DLIN over bilinear maps. Our proof size, verifier’s query complexity, and verification time are n1/2 + o(1)· poly(λ), where n is the length of the statement and λ is the security parameter. By additionally assuming sub-exponentially secure indistinguishability obfuscation, we upgrade this result to SNAPs for NP with essentially the same parameters. Previously, we only had non-adaptively sound SNAPs for P in the designated verifier setting with O(n1−δ) proof size, query complexity, and verification time for some constant δ > 0. We show that our parameters in the adaptive soundness setting are nearly optimal, up to an no(1) · poly(λ) factor: in any adaptive SNAP for P, the product of proof size and verifier query complexity must be Ω(n). Our lower bound is unconditional. For any constant є ∈ (0, 1), we construct the first non-adaptively sound SNAPs for NP with є-proximity, based on learning with errors and indistinguishability obfuscation. The proof size, verifier’s query complexity, and verification time in our constructions are fixed polynomials in the security parameter. We also show that, restricting such SNAPs to just P would already imply non-adaptively sound SNARGs for NP. Central to our SNAP constructions is a new notion of commitment of proximity, which enables sublinear-time verification of the commitment. To derive our unconditional lower bound, we adopt and generalize theorems from oracle-presampling techniques in the random oracle literature. Both techniques may be of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718172"}, {"primary_key": "197924", "vector": [], "sparse_vector": [], "title": "Provably Learning a Multi-head Attention Layer.", "authors": ["<PERSON><PERSON>", "<PERSON>zhi Li"], "summary": "The multi-head attention layer is one of the key components of the transformer architecture that sets it apart from traditional feed-forward models. Given a sequence length k, attention matrices Σ1,…,Σm∈ℝd× d, and projection matrices W1,…,Wm∈ℝd× d, the corresponding multi-head attention layer F: ℝk× d→ ℝk× d transforms length-k sequences of d-dimensional tokens X∈ℝk× d via F(X) ≜ ∑i=1m softmax(XΣiX⊤)XWi. In this work, we initiate the study of provably learning a multi-head attention layer from random examples and give the first nontrivial upper and lower bounds for this problem. Provided {Wi, Σi} satisfy certain non-degeneracy conditions, we give a (dk)O(m3)-time algorithm that learns F to small error given random labeled examples drawn uniformly from {± 1}k× d. We also prove computational lower bounds showing that in the worst case, exponential dependence on the number of heads m is unavoidable. We chose to focus on Boolean X to mimic the discrete nature of tokens in large language models, though our techniques naturally extend to standard continuous settings, e.g. Gaussian. Our algorithm, which is centered around using examples to sculpt a convex body containing the unknown parameters, is a significant departure from existing provable algorithms for learning feed-forward networks, which predominantly exploit fine-grained algebraic and rotation invariance properties of the Gaussian distribution. In contrast, our analysis is more flexible as it primarily relies on various upper and lower tail bounds for the input distribution and “slices” thereof.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718174"}, {"primary_key": "197925", "vector": [], "sparse_vector": [], "title": "Counting Random k-SAT near the Satisfiability Threshold.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present efficient counting and sampling algorithms for random k-SAT when the clause density satisfies α ≤ 2k/poly(k).In particular, the exponential term 2k matches the satisfiability threshold Θ(2k) for the existence of a solution and the (conjectured) algorithmic threshold 2k (lnk) / k for efficiently finding a solution. Previously, the best-known counting and sampling algorithms required far more restricted densities α≲ 2k/3 [<PERSON>, <PERSON>, <PERSON>, <PERSON>23]. Notably, our result goes beyond the lower bound d≳ 2k/2 for worst-case k-SAT with bounded-degree d [<PERSON><PERSON><PERSON> et al, SICOMP ’19], showing that for counting and sampling, the average-case random k-SAT model is computationally much easier than the worst-case model. At the heart of our approach is a new refined analysis of the recent novel coupling procedure by [<PERSON>, <PERSON>, <PERSON><PERSON>24], utilizing the structural properties of random constraint satisfaction problems (CSPs). Crucially, our analysis avoids reliance on the 2-tree structure used in prior works, which cannot extend beyond the worst-case threshold 2k/2. Instead, we employ a witness tree similar to that used in the analysis of the Moser-Tardos algorithm [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> ’10] for the Lovász Local lemma, which may be of independent interest. Our new analysis provides a universal framework for efficient counting and sampling for random atomic CSPs, including, for example, random hypergraph colorings. At the same time, it immediately implies as corollaries several structural and probabilistic properties of random CSPs that have been widely studied but rarely justified, including replica symmetry and non-reconstruction.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718163"}, {"primary_key": "197926", "vector": [], "sparse_vector": [], "title": "Constant Approximation of <PERSON><PERSON><PERSON> Distance in Strongly Subquadratic Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Let τ and σ be two polygonal curves in ℝd for any fixed d. Suppose that τ and σ have n and m vertices, respectively, and m≤ n. While conditional lower bounds prevent approximating the Fréchet distance between τ and σ within a factor of 3 in strongly subquadratic time, the current best approximation algorithm attains a ratio of nc in strongly subquadratic time, for some constant c∈(0,1). We present a randomized algorithm with running time O(nm0.99log(n/ε)) that approximates the Fréchet distance within a factor of 7+ε, with a success probability at least 1−1/n6. We also adapt our techniques to develop a randomized algorithm that approximates the discrete Fréchet distance within a factor of 7+ε in strongly subquadratic time. They are the first algorithms to approximate the Fréchet distance and the discrete Fréchet distance within constant factors in strongly subquadratic time.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718157"}, {"primary_key": "197927", "vector": [], "sparse_vector": [], "title": "On Reductions and Representations of Learning Problems in Euclidean Spaces.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many practical prediction algorithms represent inputs in Euclidean space and replace the discrete 0/1 classification loss with a real-valued surrogate loss, effectively reducing classification tasks to stochastic optimization. In this paper, we investigate the expressivity of such reductions in terms of key resources, including dimension and the role of randomness. We establish bounds on the minimum Euclidean dimension D needed to reduce a concept class with VC dimension d to a Stochastic Convex Optimization (SCO) problem in ℝD, formally addressing the intuitive interpretation of the VC dimension as the number of parameters needed to learn the class. To achieve this, we develop a generalization of the Borsuk-Ulam Theorem that combines the classical topological approach with convexity considerations. Perhaps surprisingly, we show that, in some cases, the number of parameters D must be exponentially larger than the VC dimension d, even if the reduction is only slightly non-trivial. We also present natural classification tasks that can be represented in much smaller dimensions by leveraging randomness, as seen in techniques like random initialization. This result resolves an open question posed by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (COLT 2020). Our findings introduce new variants of dimension complexity (also known as sign-rank), a well-studied parameter in learning and complexity theory. Specifically, we define an approximate version of sign-rank and another variant that captures the minimum dimension required for a reduction to SCO. We also propose several open questions and directions for future research.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718213"}, {"primary_key": "197928", "vector": [], "sparse_vector": [], "title": "Asymptotic Tensor Rank Is Characterized by Polynomials.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Asymptotic tensor rank, originally developed to characterize the complexity of matrix multiplication, is a parameter that plays a fundamental role in problems in mathematics, computer science and quantum information. This parameter is notoriously difficult to determine; indeed, determining its value for the 2× 2 matrix multiplication tensor would determine the matrix multiplication exponent, a long-standing open problem. <PERSON><PERSON><PERSON>’s asymptotic rank conjecture, on the other hand, makes the bold statement that asymptotic tensor rank equals the largest dimension of the tensor and is thus as easy to compute as matrix rank. Recent works have proved strong consequences of <PERSON><PERSON><PERSON>’s asymptotic rank conjecture in computational complexity theory. Despite tremendous interest, much is still unknown about the structural and computational properties of asymptotic rank; for instance whether it is computable. We prove that asymptotic tensor rank is “computable from above”, that is, for any real number r there is an (efficient) algorithm that determines, given a tensor T, if the asymptotic tensor rank of T is at most r. The algorithm has a simple structure; it consists of evaluating a finite list of polynomials on the tensor. Indeed, we prove that the sublevel sets of asymptotic rank are Zariski-closed (just like matrix rank). While we do not exhibit these polynomials explicitly, their mere existence has strong implications on the structure of asymptotic rank. As one such implication, we find that the values that asymptotic tensor rank takes, on all tensors, is a well-ordered set. In other words, any non-increasing sequence of asymptotic ranks stabilizes (“discreteness from above”). In particular, for the matrix multiplication exponent (which is the base-2 logarithm of an asymptotic rank) there is no sequence of exponents of bilinear maps that approximates it arbitrarily closely from above without being eventually constant. In other words, any such upper bound on the matrix multiplication exponent that is close enough, will “snap” to it. Previously such discreteness results were only known for finite fields or for other tensor parameters (e.g., asymptotic slice rank). We obtain them for infinite fields like the complex numbers. We prove our result more generally for a large class of functions on tensors, and in particular obtain similar properties for all functions in Strassen’s asymptotic spectrum of tensors. We prove a variety of related structural results on the way. For instance, we prove that for any converging sequence of asymptotic ranks, the limit is also an asymptotic rank for some tensor. We leave open whether asymptotic rank is also discrete from below (which would be implied by Strassen’s asymptotic rank conjecture).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718122"}, {"primary_key": "197929", "vector": [], "sparse_vector": [], "title": "Breaking the O(m2n)-Time Barrier for Vertex-Weighted Global Minimum Cut.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the Global Minimum Vertex-Cut problem: given an undirected vertex-weighted graph G, compute a minimum-weight subset of its vertices whose removal disconnects G. The problem is closely related to Global Minimum Edge-Cut, where the weights are on the graph edges instead of vertices, and the goal is to compute a minimum-weight subset of edges whose removal disconnects the graph. Global Minimum Cut is one of the most basic and extensively studied problems in combinatorial optimization and graph theory. While an almost-linear time algorithm was known for the edge version of the problem for awhile (<PERSON><PERSON>, STOC 1996 and J. ACM 2000), the fastest previous algorithm for the vertex version (<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, FOCS 1996 and J. <PERSON> 2000) achieves a running time of (mn), where m and n denote the number of edges and vertices in the input graph, respectively. For the special case of unit vertex weights, this bound was broken only recently (<PERSON> et al., STOC 2021); their result, combined with the recent breakthrough almost-linear time algorithm for Maximum s-t Flow (<PERSON> et al., FOCS 2022, <PERSON> et al., FOCS 2023), yields an almost-linear time algorithm for Global Minimum Vertex-Cut with unit vertex weights. In this paper we break the 28 years old bound of <PERSON><PERSON><PERSON> et al. for the general weighted Global Minimum Vertex-Cut, by providing a randomized algorithm for the problem with running time O(min{mn0.99+o(1),m1.5+o(1)}).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718185"}, {"primary_key": "197930", "vector": [], "sparse_vector": [], "title": "A (2+ε)-Approximation Algorithm for Metric k-Median.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the classical NP-hard (metric) k-median problem, we are given a set of n clients and centers with metric distances between them, along with an integer parameter k ≥ 1. The objective is to select a subset of k open centers that minimizes the total distance from each client to its closest open center. In their seminal work, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> presented the Greedy algorithm for facility location, which implies a 2-approximation algorithm for k-median that opens k centers in expectation. Since then, substantial research has aimed at narrowing the gap between their algorithm and the best achievable approximation by an algorithm guaranteed to open exactly k centers, as required in the k-median problem. During the last decade, all improvements have been achieved by leveraging their algorithm (or a small improvement thereof), followed by a second step called bi-point rounding, which inherently adds an additional factor to the approximation guarantee. Our main result closes this gap: for any > 0, we present a (2+)-approximation algorithm for the k-median problem, improving the previous best-known approximation factor of 2.613. Our approach builds on a combination of two key algorithms. First, we present a non-trivial modification of the Greedy algorithm that operates with only O(logn/2) adaptive phases. Through a novel walk-between-solutions approach, this enables us to construct a (2+)-approximation algorithm for k-median that consistently opens at most k + O(logn/2) centers: via known results, this already implies a (2+)-approximation algorithm that runs in quasi-polynomial time. Second, we develop a novel (2+)-approximation algorithm tailored for stable instances, where removing any center from an optimal solution increases the cost by at least an Ω(3/logn) fraction. Achieving this involves several ideas, including a sampling approach inspired by the k-means++ algorithm and a reduction to submodular optimization subject to a partition matroid. This allows us to convert the previous result into a polynomial time algorithm that opens exactly k centers while maintaining the (2+)-approximation guarantee.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718299"}, {"primary_key": "197931", "vector": [], "sparse_vector": [], "title": "Almost Optimal PAC Learning for k-Means.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given a set of points, the k-means clustering problem consists of finding a partition of a set of points into k clusters such that the sum of squared Euclidean distances between the points and their assigned centers is minimized. In this paper, we consider learning bounds for this problem. That is, given a set of n samples P drawn independently from some unknown but fixed distribution D, how quickly does a solution computed on P converge to the optimal clustering of D? The currently fastest provable rate of convergence of the order √k/nmin(k,logklog2(n/k)) is due to [<PERSON><PERSON><PERSON>, <PERSON>, 2021] with the best known lower bound being of the order √k/n due to [<PERSON>, <PERSON>, and <PERSON>, 1998]. We give learning bounds with both optimal dependency on the sample size n and nearly optimal dependency on k by proving a convergence rate of the order of √klogk/n.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718180"}, {"primary_key": "197932", "vector": [], "sparse_vector": [], "title": "Tractable Agreement Protocols.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give an efficient reduction through which any machine learning algorithm can be converted into an interactive protocol that can interact with another party (such as a human) to reach agreement on predictions and improve accuracy. The requirements on each party are calibration conditions which are computationally and statistically tractable relaxations of Bayesian rationality — that are sensible even in prior free settings — and hence are a substantial generalization of <PERSON><PERSON>’s classic “agreement theorem” [<PERSON><PERSON> 1976]. In the interactive protocol, the machine learning model first produces a prediction. Then, the human responds to the model’s prediction by either conveying agreement, or else providing feedback of some sort. The model then updates its state and provides a new prediction, and the human in turn may update their beliefs. The process continues until the model and the human reach agreement. The first setting we study generalizes past work on <PERSON><PERSON>’s Agreement Theorem, in which the parties aim to agree on a one-dimensional expectation. At each round, each party simply communicates an estimate of their current prediction for the expectation. In this setting we recover the quantitative convergence theorem of <PERSON><PERSON> (2005) (but under our much weaker assumptions). We then move on to the case in which the parties maintain beliefs about a distribution over d outcomes and consider two feedback mechanisms. The first simply corresponds to a vector-valued estimate of the agents’ current prediction. The second takes a decision theoretic perspective: if the human needs to take some downstream action from a finite set, and has an arbitrary utility function of their action and the outcome, then we show that the parties can communicate and reach agreement about the correct downstream action to take by simply communicating at each round the action that they believe to be utility maximizing. The number of rounds until agreement remains independent of d in this case. We can also generalize our protocols to more than 2 parties, with computational complexity that degrades only linearly with the number of parties. Our protocols are based on simple, efficiently maintainable conditions and result in predictions that are more accurate than any single party’s alone.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718222"}, {"primary_key": "197933", "vector": [], "sparse_vector": [], "title": "How to Protect Yourself from Threatening Skeletons: Optimal Padded Decompositions for Minor-Free Graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Roughly, a metric space has padding parameter β if for every Δ>0, there is a stochastic decomposition of the metric points into clusters of diameter at most Δ such that every ball of radius γΔ is contained in a single cluster with probability at least e−γβ. The padding parameter is an important characteristic of a metric space with vast algorithmic implications. In this paper we prove that the shortest path metric of every Kr-minor-free graph has padding parameter O(logr), which is also tight. This resolves a long standing open question, and exponentially improves the previous bound. En route to our main result, we construct sparse covers for Kr-minor-free graphs with improved parameters, and we prove a general reduction from sparse covers to padded decompositions.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718252"}, {"primary_key": "197934", "vector": [], "sparse_vector": [], "title": "The Structure of Catalytic Space: Capturing Randomness and Time via Compression.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the catalytic logspace (CL) model of (<PERSON><PERSON><PERSON> et. al. STOC 2013), we are given a small work tape, and a larger catalytic tape that has an arbitrary initial configuration. We may edit this tape, but it must be exactly restored to its initial configuration at the completion of the computation. This model is of interest from a complexity-theoretic perspective as it gains surprising power over traditional space. However, many fundamental structural questions remain open. We substantially advance the understanding of the structure of CL, addressing several questions raised in prior work. Our main results are as follows. 1: We unconditionally derandomize catalytic logspace: CBPL = CL. 2: We show time and catalytic space bounds can be achieved separately if and only if they can be achieved simultaneously: any problem in both CL and P can be solved in polynomial time-bounded CL. 3: We characterize deterministic catalytic space by the intersection of randomness and time: CL is equivalent to polytime-bounded, zero-error randomized CL. Our results center around the compress--or--random framework. For the second result, we introduce a simple yet novel compress--or--compute algorithm which, for any catalytic tape, either compresses the tape or quickly and successfully computes the function at hand. For our first result, we further introduce a compress--or--compress--or--random algorithm that combines runtime compression with a second compress--or--random algorithm, building on recent work on distinguish-to-predict transformations and pseudorandom generators with small-space deterministic reconstruction.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718112"}, {"primary_key": "197935", "vector": [], "sparse_vector": [], "title": "Time and Space Efficient Deterministic Decoders.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Time efficient decoding algorithms for error correcting codes often require linear space. However, locally decodable codes yield more efficient randomized decoders that run in time n1+o(1) and space no(1). In this work we focus on deterministic decoding. <PERSON><PERSON><PERSON><PERSON> showed that any non-adaptive deterministic decoder for a good code running in time n1+δ must use space n1−δ. In sharp contrast, we show that all typical locally correctable codes have (non-uniform) time and space efficient adaptive deterministic decoders. To obtain the decoders, we devise a new time-space efficient derandomization technique that works by iterative correction. Further, we give a new construction of curve samplers that allow us to uniformly decode Reed-Muller codes time and space efficiently. In particular, for any constant γ > 0, we give asymptotically good Reed-Muller codes that are decodable in time n1 + γ and space nγ by a uniform, deterministic decoder. A related construction allows us to uniformly decode asymptotically good codes based on lifted Reed-Solomon codes in time n1+o(1) and space no(1).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718132"}, {"primary_key": "197936", "vector": [], "sparse_vector": [], "title": "Breaking the T(2/3) Barrier for Sequential Calibration.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A set of probabilistic forecasts is calibrated if each prediction of the forecaster closely approximates the empirical distribution of outcomes on the subset of timesteps where that prediction was made. We study the fundamental problem of online calibrated forecasting of binary sequences, which was initially studied by <PERSON> and <PERSON>. They derived an algorithm with O(T2/3) calibration error after T time steps, and showed a lower bound of Ω(T1/2). These bounds remained stagnant for two decades, until <PERSON><PERSON> and <PERSON><PERSON> improved the lower bound to Ω(T0.528) by introducing a combinatorial game called sign preservation and showing that lower bounds for this game imply lower bounds for calibration. In this paper, we give the first improvement to the O(T2/3) upper bound on calibration error of <PERSON> and <PERSON><PERSON><PERSON>. We do this by introducing a variant of <PERSON><PERSON> and <PERSON><PERSON>’s game that we call sign preservation with reuse (SPR). We prove that the relationship between SPR and calibrated forecasting is bidirectional: not only do lower bounds for SPR translate into lower bounds for calibration, but algorithms for SPR also translate into new algorithms for calibrated forecasting. We then give an improved upper bound for the SPR game, which implies, via our equivalence, a forecasting algorithm with calibration error O(T2/3 − ) for some > 0, improving <PERSON> and <PERSON><PERSON>’s upper bound for the first time. Using similar ideas, we then prove a slightly stronger lower bound than that of <PERSON><PERSON> and <PERSON><PERSON>, namely Ω(T0.54389). Our lower bound is obtained by an oblivious adversary, marking the first ω(T1/2) calibration lower bound for oblivious adversaries.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718178"}, {"primary_key": "197937", "vector": [], "sparse_vector": [], "title": "Locality vs Quantum Codes.", "authors": ["<PERSON>", "<PERSON>"], "summary": "This paper proves optimal tradeoffs between the locality and parameters of quantum error-correcting codes. Quantum codes give a promising avenue towards quantum fault tolerance, but the practical constraint of locality limits their quality. The seminal Bravyi-Po<PERSON>-Te<PERSON>hal (BPT) bound says that a [[n,k,d]] quantum stabilizer code with 2D-locality must satisfy kd2≤ O(n). We answer the natural question: for better code parameters, how much “non-locality” is needed? In particular, (i) how long must the long-range interactions be, and (ii) how many long-range interactions must there be? We give a complete answer to both questions for all n,k,d: above the BPT bound, any 2D-embedding must have at least Ω(M*) interactions of length Ω(ℓ*), where M*= max(k,d) and ℓ*=max(d/√n, ( kd2/n )1/4 ). Conversely, we exhibit quantum codes that show, in strong ways, that our interaction length ℓ* and interaction count M* are asymptotically optimal for all n,k,d. Our results generalize or improve all prior works on this question, including the BPT bound and the results of <PERSON><PERSON><PERSON> and <PERSON>. One takeaway of our work is that, for any desired distance d and dimension k, the number of long-range interactions is asymptotically minimized by a good qLDPC code of length Θ(max(k,d)). Following <PERSON><PERSON><PERSON> and <PERSON>, we also apply our results to the codes implemented in the stacked architecture and obtain better bounds. In particular, we rule out any implementation of hypergraph product codes in the stacked architecture.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718113"}, {"primary_key": "197938", "vector": [], "sparse_vector": [], "title": "Efficient Learning and Computation of Linear Correlated Equilibrium in General Convex Games.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose efficient no-regret learning dynamics and ellipsoid-based methods for computing linear correlated equilibria—a relaxation of correlated equilibria and a strengthening of coarse correlated equilibria—in general convex games. These are games where the number of pure strategies is potentially exponential in the natural representation of the game, such as extensive-form games. Our work identifies linear correlated equilibria as the tightest known notion of equilibrium that is computable in polynomial time and is efficiently learnable for general convex games. Our results are enabled by a generalization of the seminal framework of <PERSON> et al. for Φ-regret minimization, providing extensions to this framework that can be used even when the set of deviations Φ is intractable to separate/optimize over. Our polynomial-time algorithms are similarly enabled by extending the Ellipsoid-Against-Hope approach of Papadimitriou and Roughgarden and its generalization to games of non-polynomial type proposed by <PERSON><PERSON> and <PERSON><PERSON>. We provide an extension to these approaches when we do not have access to the separation oracles required by these works for the dual player.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718307"}, {"primary_key": "197939", "vector": [], "sparse_vector": [], "title": "On the Locality of the Lovász Local Lemma.", "authors": ["<PERSON>-<PERSON>"], "summary": "The Lov\\'asz Local Lemma is a versatile result in probability theory, characterizing circumstances in which a collection of $n$ `bad events', each occurring with probability at most $p$ and dependent on a set of underlying random variables, can be avoided. It is a central tool of the probabilistic method, since it can be used to show that combinatorial objects satisfying some desirable properties must exist. While the original proof was existential, subsequent work has shown algorithms for the Lov\\'asz Local Lemma: that is, in circumstances in which the lemma proves the existence of some object, these algorithms can constructively find such an object. One main strand of these algorithms, which began with <PERSON><PERSON> and <PERSON><PERSON><PERSON>'s well-known result (JACM 2010), involves iteratively resampling the dependent variables of satisfied bad events until none remain satisfied. In this paper, we present a novel analysis that can be applied to resampling-style Lov\\'asz Local Lemma algorithms. This analysis shows that an output assignment for the dependent variables of most events can be determined only from $O(\\log \\log_{1/p} n)$-radius local neighborhoods, and that the events whose variables may still require resampling can be identified from these neighborhoods. This allows us to improve randomized complexities for the constructive Lov\\'asz Local Lemma (with polynomial criterion) in several parallel and distributed models. In particular, we obtain: 1) A LOCAL algorithm with $O(\\log\\log_{1/p} n)$ node-averaged complexity (while matching the $O(\\log_{1/p} n)$ worst-case complexity of <PERSON>, <PERSON><PERSON>, and <PERSON>). 2) An algorithm for the LCA and VOLUME models requiring $d^{O(\\log\\log_{1/p} n)}$ probes per query. 3) An $O(\\log\\log\\log_{1/p} n)$-round algorithm for CONGESTED CLIQUE, linear space MPC, and Heterogenous MPC.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718103"}, {"primary_key": "197940", "vector": [], "sparse_vector": [], "title": "SoS Certifiability of Subgaussian Distributions and Its Algorithmic Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ankit Pensia", "<PERSON>"], "summary": "We prove that there is a universal constant C>0 so that for every d ∈ ℕ, every centered subgaussian distribution D on ℝd, and every even p ∈ ℕ, the d-variate polynomial (Cp)p/2 · ||v||2p − EX ∼ D ⟨ v,X⟩p is a sum of square polynomials. This establishes that every subgaussian distribution is SoS-certifiably subgaussian—a condition that yields efficient learning algorithms for a wide variety of high-dimensional statistical tasks. As a direct corollary, we obtain computationally efficient algorithms with near-optimal guarantees for the following tasks, when given samples from an arbitrary subgaussian distribution: robust mean estimation, list-decodable mean estimation, clustering mean-separated mixture models, robust covariance-aware mean estimation, robust covariance estimation, and robust linear regression. Our proof makes essential use of <PERSON><PERSON><PERSON>’s generic chaining/majorizing measures theorem.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718183"}, {"primary_key": "197941", "vector": [], "sparse_vector": [], "title": "SoS Certificates for Sparse Singular Values and Their Applications: Robust Statistics, Subspace Distortion, and More.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ankit Pensia", "<PERSON>"], "summary": "We study sparse singular value certificates for random rectangular matrices. If M is a d × n matrix with independent Gaussian entries, we give a new family of polynomial-time algorithms which can certify upper bounds on the maximum of ||M u||, where u is a unit vector with at most η n nonzero entries for a given η ∈ (0,1). This basic algorithmic primitive lies at the heart of a wide range of problems across algorithmic statistics and theoretical computer science, including robust mean and covariance estimation, certification of distortion of random subspaces of n, certification of the 2 → p norm of a random matrix, and sparse principal component analysis. Our algorithms certify a bound which is asymptotically smaller than the naive one, given by the maximum singular value of M, for nearly the widest-possible range of n,d, and η. Efficiently certifying such a bound for a range of n,d and η which is larger by any polynomial factor than what is achieved by our algorithm would violate lower bounds in the statistical query and low-degree polynomials models. Our certification algorithm makes essential use of the Sum-of-Squares hierarchy. To prove the correctness of our algorithm, we develop a new combinatorial connection between the graph matrix approach to analyze random matrices with dependent entries, and the Efron-Stein decomposition of functions of independent random variables. As applications of our certification algorithm, we obtain new efficient algorithms for a wide range of well-studied algorithmic tasks. In algorithmic robust statistics, we obtain new algorithms for robust mean and covariance estimation with tradeoffs between breakdown point and sample complexity, which are nearly matched by statistical query and low-degree polynomial lower bounds (that we establish). We also obtain new polynomial-time guarantees for certification of ℓ1/ℓ2 distortion of random subspaces of n (also with nearly matching lower bounds), sparse principal component analysis, and certification of the 2→ p norm of a random matrix.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718293"}, {"primary_key": "197942", "vector": [], "sparse_vector": [], "title": "Entangled Mean Estimation in High Dimensions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Thanasis <PERSON>"], "summary": "We study the task of high-dimensional entangled mean estimation in the subset-of-signals model. Specifically, given N independent random points x1,…,xN in D and a parameter α ∈ (0, 1) such that each xi is drawn from a Gaussian with mean µ and unknown covariance, and an unknown α-fraction of the points have identity-bounded covariances, the goal is to estimate the common mean µ. The one-dimensional version of this task has received significant attention in theoretical computer science and statistics over the past decades. Recent work has given near-optimal upper and lower bounds for the one-dimensional setting. On the other hand, our understanding of even the information-theoretic aspects of the multivariate setting has remained limited. In this work, we design a computationally efficient algorithm achieving an information-theoretically near-optimal error. Specifically, we show that the optimal error (up to polylogarithmic factors) is f(α,N) + √D/(α N), where the term f(α,N) is the error of the one-dimensional problem and the second term is the sub-Gaussian error rate. Our algorithmic approach employs an iterative refinement strategy, whereby we progressively learn more accurate approximations µ to µ. This is achieved via a novel rejection sampling procedure that removes points significantly deviating from µ, as an attempt to filter out unusually noisy samples. A complication that arises is that rejection sampling introduces bias in the distribution of the remaining points. To address this issue, we perform a careful analysis of the bias, develop an iterative dimension-reduction strategy, and employ a novel subroutine inspired by list-decodable learning that leverages the one-dimensional result.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718162"}, {"primary_key": "197943", "vector": [], "sparse_vector": [], "title": "A New Approach for LPN-Based Pseudorandom Functions: Low-Depth and Key-Homomorphic.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give new constructions of pseudorandom functions (PRFs) computable in NC1 from (variants of the) Learning Parity with Noise (LPN) assumption. Prior to our work, the only NC1-computable PRF from LPN-style assumptions was due to <PERSON> et al. (FOCS 2020) who constructed a weak PRF from a new heuristic variant of LPN called variable-density LPN. We give the following results: (1) A weak PRF computable in NC1 from standard LPN, (2) A (strong) encoded-input PRF (EI-PRF) computable in NC1 from sparse LPN (An EI-PRF is a PRF whose input domain is restricted to an efficiently sampleable and recognizable set. The input encoding can be computed in NC1+є for any constant є > 0, implying a strong PRF computable in NC1+є), and (3) A (strong) PRF computable in NC1 from a (new, heuristic) seeded LPN assumption. In our assumption, each column of the public LPN matrix is generated by an n-wise independent distribution. Supporting evidence for the security of the assumption is given by showing resilience to linear tests. As a bonus, all of our PRF constructions are key-homomorphic, an algebraic property that is useful in many symmetric-cryptography applications. No previously-known LPN-based PRFs have this property, even if we completely ignore depth-efficiency. In fact, our constructions support key homomorphism for linear functions (and not only additive), a property that no previously-known PRF satisfies, including ones from LWE. Additionally, all of our PRF constructions nicely fit into the substitution-permutation network (SPN) design framework used in modern block ciphers (e.g. AES). No prior PRF construction that has a reduction to a standard cryptographic assumptions (let alone LPN) has an SPN-like structure. Technically, all of our constructions of PFRs leverage a new recursive derandomization technique for LPN instances, which allows us to generate LPN error terms deterministically. This technique is inspired by a related idea from the LWE literature (Kim, EUROCRYPT 2020) for which devising an LPN analogue has been an outstanding open problem.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718210"}, {"primary_key": "197944", "vector": [], "sparse_vector": [], "title": "When Connectivity Is Hard, Random Walks Are Easy with Non-determinism.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Two fundamental problems on directed graphs are to decide s-t connectivity, and to estimate the behavior of random walks. Currently, there is no known algorithm for s-t connectivity running in polynomial time and no(1) space, and no known algorithm for estimating the n-step random walk matrix running in non-deterministic logspace. We show that for every directed graph, at least one of these problems is solvable in time and space that significantly improve on the respective state-of-the-art. In particular, there is a pair of algorithms A1 and A2 such that for every graph G, either: A1(G) outputs the transitive closure of G in polynomial time and polylogarithmic space. A2(G) outputs an approximation of the n-step random walk matrix of G in non-deterministic logspace. As one application, we show surprisingly tight win-win results for space-bounded complexity. For example, for certain parameter regimes, either <PERSON><PERSON><PERSON>’s theorem can be non-trivially sped up, or randomized space can be almost completely derandomized. We also apply our techniques to significantly weaken the assumptions required to derandomize space-bounded computation, and to make non-deterministic space-bounded computation unambiguous. Specifically, we deduce such conclusions from lower bounds against uniform circuits of polynomial size, which is an exponential improvement on the required hardness in previous works (<PERSON><PERSON><PERSON>–Tell STOC 2024, <PERSON><PERSON>–Tell FOCS 2024). We further show similar results for minimal-memory derandomization (<PERSON><PERSON>–Tell CCC 2024). To prove these results, we substantially improve the array of technical tools introduced in recent years for studying hardness-vs.-randomness for bounded-space computation. In particular, we develop derandomized distinguish-to-predict transformations for new types of distinguishers (corresponding to compositions of PRGs with weak distinguishers), we construct a derandomized logspace reconstruction procedure for the Shaltiel–Umans generator (JACM 2005) that can compress hard truth-tables to polylogarithmic size, and we design a version of the Chen–Tell generator (FOCS 2021) that is particularly suitable for the space-bounded setting.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718303"}, {"primary_key": "197945", "vector": [], "sparse_vector": [], "title": "Disjoint Connected Dominating Sets in Pseudorandom Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A connected dominating set (CDS) in a graph is a dominating set of vertices that induces a connected subgraph. Having many disjoint CDSs in a graph can be considered as a measure of its connectivity, and has various graph-theoretic and algorithmic implications. We show that d-regular (weakly) pseudoreandom graphs contain (1+o(1))d/lnd disjoint CDSs, which is asymptotically best possible. In particular, this implies that random d-regular graphs typically contain (1+o(1))d/lnd disjoint CDSs.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718184"}, {"primary_key": "197946", "vector": [], "sparse_vector": [], "title": "Merge-Width and First-Order Model Checking.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce merge-width, a family of graph parameters that unifies several structural graph measures, including treewidth, degeneracy, twin-width, clique-width, and generalized coloring numbers. Our parameters are based on new decompositions called construction sequences. These are sequences of ever coarser partitions of the vertex set, where each pair of parts has a specified default connection, and all vertex pairs of the graph that differ from the default are marked as resolved. The radius-r merge-width is the maximum number of parts reached from a vertex by following a path of at most r resolved edges. Graph classes of bounded merge-width – for which the radius-r merge-width parameter can be bounded by a constant, for each fixed r=1,2,3,… – include all classes of bounded expansion or of bounded twin-width, thus unifying two central notions from the Sparsity and Twin-width frameworks. Furthermore, they are preserved under first-order transductions, which attests to their robustness. We conjecture that classes of bounded merge-width are equivalent to the previously introduced classes of bounded flip-width. As our main result, we show that the model checking problem for first-order logic is fixed-parameter tractable on graph classes of bounded merge-width, assuming the input includes a witnessing construction sequence. This unites and extends two previous model checking results: the result of <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> for classes of bounded expansion, and the result of <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> for classes of bounded twin-width. Finally, we suggest future research directions that could impact the study of structural and algorithmic graph theory, in particular of monadically dependent graph classes, which we conjecture to coincide with classes of almost bounded merge-width.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718259"}, {"primary_key": "197947", "vector": [], "sparse_vector": [], "title": "Breaking the Sorting Barrier for Directed Single-Source Shortest Paths.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Xinkai Shu", "<PERSON><PERSON>"], "summary": "We give a deterministic O(mlog2/3n)-time algorithm for single-source shortest paths (SSSP) on directed graphs with real non-negative edge weights in the comparison-addition model. This is the first result to break the O(m+nlogn) time bound of <PERSON><PERSON><PERSON>’s algorithm on sparse graphs, showing that <PERSON><PERSON><PERSON>’s algorithm is not optimal for SSSP.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718179"}, {"primary_key": "197948", "vector": [], "sparse_vector": [], "title": "The Cost of Consistency: Submodular Maximization with Constant Recourse.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>Fard", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we study online submodular maximization and how the requirement of maintaining a stable solution impacts the approximation. In particular, we seek bounds on the best-possible approximation ratio that is attainable when the algorithm is allowed to make, at most, a constant number of updates per step. We show a tight information-theoretic bound of 2/3 for general monotone submodular functions and an improved (also tight) bound of 3/4 for coverage functions. Since both these bounds are attained by non poly-time algorithms, we also give a poly-time randomized algorithm that achieves a 0.51-approximation. Combined with an information-theoretic hardness of 1/2 for deterministic algorithms from prior work, our work thus shows a separation between deterministic and randomized algorithms, both information theoretically and for poly-time algorithms.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718131"}, {"primary_key": "197949", "vector": [], "sparse_vector": [], "title": "On Approximability of the Permanent of PSD Matrices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the complexity of approximating the permanent of a positive semidefinite matrix A∈ ℂn× n. Our first result is a new approximation algorithm for per(A) with approximation ratio e−(0.9999 + γ)n, exponentially improving upon the current best bound of e−(1+γ−o(1))n (<PERSON><PERSON><PERSON> 2017, Yuan-<PERSON> 2022). Here, γ ≈ 0.577 is <PERSON><PERSON><PERSON>’s constant. Our second result is a hardness result. We prove that it is NP-hard to approximate per(A) within a factor e−(γ−)n for any >0. This is the first exponential hardness of approximation for this problem. Along the way, we prove optimal hardness of approximation results for the ||·||2→ q “norm” problem of a matrix for all −1 < q < 2.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718107"}, {"primary_key": "197950", "vector": [], "sparse_vector": [], "title": "Approximately Counting and Sampling Hamilton<PERSON>fs in Sublinear Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Counting small subgraphs, referred to as motifs, in large graphs is a fundamental task in graph analysis, extensively studied across various contexts and computational models. In the sublinear-time regime, the relaxed problem of approximate counting has been explored within two prominent query frameworks: the standard model, which permits degree, neighbor, and pair queries, and the strictly more powerful augmented model, which additionally allows for uniform edge sampling. Currently, in the standard model, (optimal) results have been established only for approximately counting edges, stars, and cliques, all of which have a radius of one. This contrasts sharply with the state of affairs in the augmented model, where algorithmic results (some of which are optimal) are known for any input motif, leading to a disparity which we term the “scope gap” between the two models. In this work, we make significant progress in bridging this gap. Our approach draws inspiration from recent advancements in the augmented model and utilizes a framework centered on counting by uniform sampling, thus allowing us to establish new results in the standard model and simplify on previous results. In particular, our first, and main, contribution is a new algorithm in the standard model for approximately counting any Hamiltonian motif in sublinear time, where the complexity of the algorithm is the sum of two terms. One term equals the complexity of the known algorithms by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> (ITCS 2019) and <PERSON><PERSON><PERSON><PERSON> and <PERSON> (ICALP 2020) in the (strictly stronger) augmented model and the other is an additional, necessary, additive overhead. Our second contribution is a variant of our algorithm that enables nearly uniform sampling of these motifs, a capability previously limited in the standard model to edges and cliques. Our third contribution is to introduce even simpler algorithms for stars and cliques by exploiting their radius-one property. As a result, we simplify all previously known algorithms in the standard model for stars (Gonen, Ron, Shavitt (SODA 2010)), triangles (Eden, Levi, Ron Seshadhri (FOCS 2015)) and cliques (Eden, Ron, Seshadri (STOC 2018)).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718160"}, {"primary_key": "197951", "vector": [], "sparse_vector": [], "title": "Optimal Proof Systems for Complex Sets Are Hard to Find.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We provide the first evidence for the inherent difficulty of finding complex sets with optimal proof systems. For this, we construct oracles O1 and O2 with the following properties, where RE denotes the class of recursively enumerable sets and NQP the class of sets accepted in non-deterministic quasi-polynomial time. O1: no set in PSPACE \\ NP has optimal proof systems and PH is infinite O2: no set in RE \\ NQP has optimal proof systems and NP ≠ coNP Oracle O2 is the first relative to which complex sets with optimal proof systems do not exist. By oracle O1, no relativizable proof can show that there exist sets in PSPACE \\ NP with optimal proof systems, even when assuming an infinite PH. By oracle O2, no relativizable proof can show that there exist sets outside NQP with optimal proof systems, even when assuming NP ≠ coNP. This explains the difficulty of the following longstanding open questions raised by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>ák in 1989, <PERSON><PERSON> in 1997, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in 1998, and <PERSON><PERSON><PERSON> in 2000. Q1: Are there sets outside NP with optimal proof systems? Q2: Are there arbitrarily complex sets outside NP with optimal proof systems? Moreover, relative to O2, there exist arbitrarily complex sets L ∉ NQP with almost optimal algorithms, but none of them has optimal proof systems. This explains the difficulty of <PERSON><PERSON><PERSON>’s approach to translate almost optimal algorithms into optimal proof systems.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718182"}, {"primary_key": "197952", "vector": [], "sparse_vector": [], "title": "Multi-parameter Mechanisms for Consumer Surplus Maximization.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the problem of designing auctions that maximize consumer surplus (i.e., the social welfare minus the payments charged to the buyers). In the consumer surplus maximization problem, a seller with a set of goods faces a set of strategic buyers with private values, each of whom aims to maximize their own individual utility. The seller, in contrast, aims to allocate the goods in a way that maximizes the total buyer utility. The seller must then elicit the values of the buyers in order to decide what goods to award each buyer. The canonical approach in mechanism design to ensure truthful reporting of the private information is to find appropriate prices to charge each buyer in order to align their objective with the objective of the seller. Indeed, there are many celebrated results to this end when the seller’s objective is welfare maximization or revenue maximization. However, in the case of consumer surplus maximization the picture is less clear – using high payments to ensure the highest value bidders are served necessarily decreases their surplus utility, but using low payments may lead the seller into serving lower value bidders. Our main result in this paper is a framework for designing mechanisms that maximize consumer surplus. We instantiate our framework in a variety of canonical multi-parameter auction settings (i.e., unit-demand bidders with heterogeneous items, multi-unit auctions, and auctions with divisible goods) and use it to design auctions achieving consumer surplus with tight approximation guarantees against the total social welfare. Along the way, we resolve an open question posed by <PERSON><PERSON> and <PERSON><PERSON> [STOC 2008] for the two bidders single item setting.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718126"}, {"primary_key": "197953", "vector": [], "sparse_vector": [], "title": "Constant-Cost Communication Is Not Reducible to k-Hamming Distance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Every known communication problem whose randomized communication cost is constant (independent of the input size) can be reduced to k-Hamming Distance, that is, solved with a constant number of deterministic queries to some k-Hamming Distance oracle. We exhibit the first examples of constant-cost problems which cannot be reduced to k-Hamming Distance. To prove this separation, we relate it to a natural coding-theoretic question. For f∶ {2,4,6}→ℕ, we say that an encoding function E∶{0,1}n→{0,1}m is an f-code if it transforms Hamming distances according to dist(E(x),E(y))=f(dist(x,y)) whenever f is defined. We prove that, if there exist f-codes for infinitely many n, then f must be affine: f(4)=(f(2)+f(6))/2.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718129"}, {"primary_key": "197954", "vector": [], "sparse_vector": [], "title": "Constant Approximation for Weighted Nash Social Welfare with Submodular Valuations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of assigning items to agents so as to maximize the weighted Nash Social Welfare (NSW) under submodular valuations. The best-known result for the problem is an O(nwmax)-approximation due to <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (STOC’23), where wmax is the maximum weight over all agents. Obtaining a constant approximation algorithm is an open problem in the field that has recently attracted considerable attention. We give the first such algorithm for the problem, thus solving the open problem in the affirmative. Our algorithm is based on the natural Configuration LP for the problem, which was introduced recently by <PERSON> and <PERSON> (ICALP’24) for the additive valuation case. Our rounding algorithm is similar to that of <PERSON> (SODA’25) developed for the unrelated machine scheduling problem to minimize weighted completion time. Roughly speaking, we designate the largest item in each configuration as a large item and the remaining items as small items. So, every agent gets precisely 1 fractional large item in the configuration LP solution. With the rounding algorithm in Li (SODA’25), we can ensure that in the obtained solution, every agent gets precisely 1 large item, and the assignments of small items are negatively correlated.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718203"}, {"primary_key": "197955", "vector": [], "sparse_vector": [], "title": "Minimum Degree Edge-Disjoint Hamilton Cycles in Random Directed Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we consider the problem of finding “as many edge-disjoint Hamilton cycles as possible” in the binomial random digraph Dn,p. We show that a typical Dn,p contains precisely the minimum between the minimum out- and in-degrees many edge-disjoint Hamilton cycles, given that p≥ log15 n/n, which is optimal up to a factor of polylogn. Our proof provides a randomized algorithm to generate the cycles and uses a novel idea of generating Dn,p in a sophisticated way that enables us to control some key properties, and on an “online sprinkling” idea as was introduced by <PERSON><PERSON> and <PERSON><PERSON>.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718261"}, {"primary_key": "197956", "vector": [], "sparse_vector": [], "title": "Bypassing the Noisy Parity Barrier: Learning Higher-Order <PERSON><PERSON> Random Fields from Dynamics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of learning graphical models, also known as Markov random fields (MRFs) from temporally correlated samples. As in many traditional statistical settings, fundamental results in the area all assume independent samples from the distribution. However, these samples generally will not directly correspond to more realistic observations from nature, which instead evolve according to some stochastic process. From the computational lens, even generating a single sample from the true MRF distribution is intractable unless NP=RP, and moreover, any algorithm to learn from i.i.d. samples requires prohibitive runtime due to hardness reductions to the parity with noise problem. These computational barriers for sampling and learning from the i.i.d. setting severely lessen the utility of these breakthrough results for this important task; however, dropping this assumption typically only introduces further algorithmic and statistical complexities. In this work, we surprisingly demonstrate that the direct trajectory data from a natural evolution of the MRF overcomes the fundamental computational lower bounds to efficient learning. In particular, we show that given a trajectory with Ok(n) site updates of an order k MRF from the Glau<PERSON> dynamics, a well-studied, natural stochastic process on graphical models, there is an algorithm that recovers the graph and the parameters in Ok(n2) time. By contrast, all prior algorithms for learning order k MRFs inherently suffer from nΘ(k) runtime even in sparse instances due to the reductions to sparse parity with noise. Our results thus surprisingly show that this more realistic, but intuitively less tractable, model for MRFs actually leads to efficiency far beyond what is known and believed to be true in the traditional i.i.d. case.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718231"}, {"primary_key": "197957", "vector": [], "sparse_vector": [], "title": "Primes via Zeros: Interactive Proofs for Testing Primality of Natural Classes of Ideals.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A central question in mathematics and computer science is the question of determining whether a given ideal $I$ is prime, which geometrically corresponds to the zero set of $I$, denoted $Z(I)$, being irreducible. The case of principal ideals (i.e., $m=1$) corresponds to the more familiar absolute irreducibility testing of polynomials, where the seminal work of (<PERSON><PERSON><PERSON><PERSON> 1995) yields a randomized, polynomial time algorithm for this problem. However, when $m > 1$, the complexity of the primality testing problem seems much harder. The current best algorithms for this problem are only known to be in EXPSPACE. In this work, we significantly reduce the complexity-theoretic gap for the ideal primality testing problem for the important families of ideals $I$ (namely, radical ideals and equidimensional Cohen-Macaulay ideals). For these classes of ideals, assuming the Generalized Riemann Hypothesis, we show that primality testing lies in $\\Sigma_3^p \\cap \\Pi_3^p$. This significantly improves the upper bound for these classes, approaching their lower bound, as the primality testing problem is coNP-hard for these classes of ideals. Another consequence of our results is that for equidimensional Cohen-Macaulay ideals, we get the first PSPACE algorithm for primality testing, exponentially improving the space and time complexity of prior known algorithms.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718167"}, {"primary_key": "197958", "vector": [], "sparse_vector": [], "title": "Constant-Factor EFX Exists for Chores.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of fair allocation of chores among agents with additive preferences. In the discrete setting, envy-freeness up to any chore (EFX) has emerged as a compelling fairness criterion. However, establishing its (non-)existence or achieving a meaningful approximation remains a major open question in fair division. The current best guarantee is the existence of O(n2)-EFX allocations, where n denotes the number of agents, obtained through a sophisticated algorithm. In this paper, we show the existence of 4-EFX allocations, providing the first constant-factor approximation of EFX. We further investigate the existence of allocations that are both fair and efficient, using Pareto optimality (PO) as our efficiency criterion. For the special case of bivalued instances, we establish the existence of allocations that are both 3-EFX and PO, thereby improving upon the current best factor of O(n)-EFX without any efficiency guarantees. For general additive instances, the existence of allocations that are α-EFk and PO has remained open for any constant values of α and k, where EFk denotes envy-freeness up to k chores. We provide the first positive result in this direction by showing the existence of allocations that are 2-EF2 and PO. Our results are obtained via a novel economic framework called earning restricted (ER) competitive equilibrium for fractional allocations, which imposes limits on the earnings of agents from each chore. We show the existence of ER equilibria by carefully formulating a linear complementarity problem (LCP) that captures all ER equilibria, and then prove that the classic complementary pivot algorithm applied to this LCP terminates at an ER equilibrium. By carefully setting earning limits and leveraging the properties of ER equilibria, we design algorithms that involve rounding the fractional solutions and then performing swaps and merges of bundles to meet the desired fairness and efficiency criteria. We expect that the concept of ER equilibrium will play a crucial role in deriving further results on related problems.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718305"}, {"primary_key": "197959", "vector": [], "sparse_vector": [], "title": "Improved PIR Schemes using Matching Vectors and Derivatives.", "authors": ["<PERSON><PERSON><PERSON>", "Swas<PERSON><PERSON>", "Madhu <PERSON>"], "summary": "In this paper, we construct new t-server Private Information Retrieval (PIR) schemes with communication complexity subpolynomial in the previously best known, for all but finitely many t. Our results are based on combining derivatives (in the spirit of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) with the Matching Vector based PIRs of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. Previously such a combination was achieved in an ingenious way by <PERSON><PERSON> and <PERSON><PERSON>, using polynomials and derivatives over certain exotic rings, en route to their fundamental result giving the first 2-server PIR with subpolynomial communication. Our improved PIRs are based on two ingredients: We develop a new and direct approach to combine derivatives with Matching Vector based PIRs. This approach is much simpler than that of Dvir-Gopi: it works over the same field as the original PIRs, and only uses elementary properties of polynomials and derivatives. A key subproblem that arises in the above approach is a higher-order polynomial interpolation problem. We show how “sparse S-decoding polynomials”, a powerful tool from the original constructions of Matching Vector PIRs, can be used to solve this higher-order polynomial interpolation problem using surprisingly few higer-order evaluations. Using the known sparse S-decoding polynomials in combination with our ideas leads to our improved PIRs. Notably, we get a 3-server PIR scheme with communication 2( (logn)1/3) , improving upon the previously best known communication of 2( √logn) due to <PERSON><PERSON><PERSON><PERSON>.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718313"}, {"primary_key": "197960", "vector": [], "sparse_vector": [], "title": "Using the Planted Clique Conjecture for Cryptography: Public-Key Encryption from Planted Clique and Noisy k-LIN over Expanders.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give a public key encryption scheme that is provably secure against poly-size adversaries, assuming nlogαn hardness of the standard planted clique conjecture, for any α ∈ (0,1), and a relatively mild hardness conjecture about noisy k-LIN over expanders that is not known to imply public-key encryption on its own. Both of our conjectures correspond to natural average-case variants of NP-complete problems and have been studied for multiple decades, with unconditional lower bounds supporting them in a variety of restricted models of computation. Our encryption scheme answers an open question in a seminal work by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> [STOC’10].", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718306"}, {"primary_key": "197961", "vector": [], "sparse_vector": [], "title": "Single-Sample and Robust Online Resource Allocation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Online Resource Allocation problem is a central problem in many areas of Computer Science, Operations Research, and Economics. In this problem, we sequentially receive n stochastic requests for m kinds of shared resources, where each request can be satisfied in multiple ways, consuming different amounts of resources and generating different values. The goal is to achieve a (1−є)-approximation to the hindsight optimum, where є>0 is a small constant, assuming each resource has a large budget (at least Ω((1/є))). In this paper, we investigate the learnability and robustness of online resource allocation. Our primary contribution is a novel Exponential Pricing algorithm with the following properties: Firstly, it requires only a single sample from each of the n request distributions to achieve a (1−є)-approximation for online resource allocation with large budgets. Such an algorithm was previously unknown, even with access to polynomially many samples, as prior work either assumed full distributional knowledge or was limited to i.i.d. or random-order arrivals. Secondly, it is robust to corruptions in the outliers model and the value augmentation model . Specifically, it maintains its (1 − є)-approximation guarantee under both these robustness models, resolving the open question posed by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> (SODA 2022). Lastly, it operates as a simple item-pricing algorithm that ensures incentive compatibility. The intuition behind our Exponential Pricing algorithm is that the price of a resource should adjust exponentially as it is overused or underused. It differs from conventional approaches that use an online learning algorithm for item pricing. This departure guarantees that the algorithm will never run out of any resource, but loses the usual no-regret properties of online learning algorithms, necessitating a new analytical approach.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718246"}, {"primary_key": "197962", "vector": [], "sparse_vector": [], "title": "Metric Distortion of Small-Group Deliberation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider models for social choice where voters rank a set of choices (or alternatives) by deliberating in small groups of size at most k, and these outcomes are aggregated by a social choice rule to find the winning alternative. We ground these models in the metric distortion framework, where the voters and alternatives are embedded in a latent metric space, with closer alternative being more desirable for a voter. We posit that the outcome of a small-group interaction optimally uses the voters’ collective knowledge of the metric, either deterministically or probabilistically. We characterize the distortion of our deliberation models for small k, showing that groups of size k=3 suffice to drive the distortion bound below the deterministic metric distortion lower bound of 3, and groups of size 4 suffice to break the randomized lower bound of 2.11. We also show nearly tight asymptotic distortion bounds in the group size, showing that for any constant є > 0, achieving a distortion of 1+є needs group size that only depends on 1/є, and not the number of alternatives. We obtain these results via formulating a basic optimization problem in small deviations of the sum of i.i.d. random variables, which we solve to global optimality via non-convex optimization. The resulting bounds may be of independent interest in probability theory.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718121"}, {"primary_key": "197963", "vector": [], "sparse_vector": [], "title": "Oblivious Defense in ML Models: Backdoor Removal without Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As society grows more reliant on machine learning, ensuring the security of machine learning systems against sophisticated attacks becomes a pressing concern. A recent result of <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (FOCS ’22) shows that an adversary can plant undetectable backdoors in machine learning models, allowing the adversary to covertly control the model’s behavior. Backdoors can be planted in such a way that the backdoored machine learning model is computationally indistinguishable from an honest model without backdoors. In this paper, we present strategies for defending against backdoors in ML models, even if they are undetectable. The key observation is that it is sometimes possible to provably mitigate or even remove backdoors without needing to detect them, using techniques inspired by the notion of random self-reducibility. This depends on properties of the ground-truth labels (chosen by nature), and not of the proposed ML model (which may be chosen by an attacker). We give formal definitions for secure backdoor mitigation, and proceed to show two types of results. First, we show a “global mitigation” technique, which removes all backdoors from a machine learning model under the assumption that the ground-truth labels are close to a Fourier-heavy function. Second, we consider distributions where the ground-truth labels are close to a linear or polynomial function in ℝn. Here, we show “local mitigation” techniques, which remove backdoors with high probability for every input of interest, and are computationally cheaper than global mitigation. All of our constructions are black-box, so our techniques work without needing access to the model’s representation (i.e., its code or parameters). Along the way we prove a simple result for robust mean estimation.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718245"}, {"primary_key": "197964", "vector": [], "sparse_vector": [], "title": "Asymptotically Good Quantum Codes with Transversal Non-Clifford Gates.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "We construct quantum codes that support transversal CCZ gates over qudits of arbitrary prime power dimension q (including q=2) such that the code dimension and distance grow linearly in the block length. The only previously known construction with such linear dimension and distance required a growing alphabet size q (<PERSON><PERSON><PERSON><PERSON>, 2019). Our codes imply protocols for magic state distillation with vanishing yield parameter (i.e. with sub-polylogarithmic overhead) as the block length grows. It was previously an open question to obtain such a protocol with a constant alphabet size q. We construct our codes by combining two modular components, namely, (i) a black-box transformation from classical codes satisfying certain properties to quantum codes supporting transversal CCZ gates, and (ii) a concatenation scheme for reducing the alphabet size of codes supporting transversal CCZ gates. For this scheme we introduce a quantum analogue of multiplication-friendly codes, which provide a way to express multiplication over a field in terms of a subfield. We obtain our asymptotically good construction by instantiating (i) with algebraic-geometric codes, and applying a constant number of iterations of (ii). We also give an alternative construction with nearly asymptotically good parameters (k,d=n/2O(log*n)) by instantiating (i) with Reed-Solomon codes and then performing a superconstant number of iterations of (ii).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718234"}, {"primary_key": "197965", "vector": [], "sparse_vector": [], "title": "Quantum LDPC Codes with Transversal Non-Clifford Gates via Products of Algebraic Codes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "For every integer r≥ 2 and every є>0, we construct an explicit infinite family of quantum LDPC codes supporting a transversal Cr−1Z gate with length N, dimension K≥ N1−є, distance D≥ N1/r/poly(logN), and stabilizer weight w≤ poly(logN). The previous state of the art construction (in most parameter regimes) was the r-dimensional color code, which has only constant dimension K=O(1), and otherwise has the same parameters up to polylogarithmic factors. Our construction provides the first known codes with low-weight stabilizers that are capable of magic state distillation with arbitrarily small yield parameter γ=log(N/K)/log(D)>0. A classical analogue of transversal Cr−1Z gates is given by the multiplication property, which requires component-wise products of classical codewords to belong to another similar code. As a byproduct of our techniques, we also obtain a new construction of classical locally testable codes with such a multiplication property. We construct our codes as products of chain complexes associated to classical LDPC codes, which in turn we obtain by imposing local Reed-Solomon codes on a specific spectral expander that we construct. We prove that our codes support the desired transversal Cr−1Z gates by using the multiplication property to combine local circuits based on the topological structure.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718139"}, {"primary_key": "197966", "vector": [], "sparse_vector": [], "title": "Quantum Communication Advantage in TFNP.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We exhibit a total search problem with classically verifiable solutions whose communication complexity in the quantum SMP model is exponentially smaller than in the classical two-way randomized model. Our problem is a bipartite version of a query complexity problem recently introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (JACM 2024). We prove the classical lower bound using the structure-vs-randomness paradigm for analyzing communication protocols.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718155"}, {"primary_key": "197967", "vector": [], "sparse_vector": [], "title": "Supercritical Tradeoffs for Monotone Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We exhibit a monotone function computable by a monotone circuit of quasipolynomial size such that any monotone circuit of polynomial depth requires exponential size. This is the first size–depth tradeoff result for monotone circuits in the so-called supercritical regime. Our proof is based on an analogous result in proof complexity: We introduce a new family of unsatisfiable 3-CNF formulas (called bracket formulas) that admit resolution refutations of quasipolynomial size while any refutation of polynomial depth requires exponential size.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718229"}, {"primary_key": "197968", "vector": [], "sparse_vector": [], "title": "Bounded Edit Distance: Optimal Static and Dynamic Algorithms for Small Integer Weights.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The edit distance (also known as the Levenshtein distance) of two strings is the minimum number of character insertions, deletions, and substitutions needed to transform one string into the other. The textbook algorithm determines the edit distance of two length-n strings in O(n2) time, and one of the foundational results of fine-grained complexity is that any polynomial-factor improvement upon this quadratic runtime would violate the Orthogonal Vectors Hypothesis. In the bounded version of the problem, where the complexity is parameterized by the value k of the edit distance, the classic algorithm of <PERSON>au and <PERSON>kin [JCSS’88] achieves O(n+k2) time, which is optimal (up to sub-polynomial factors and conditioned on OVH) as a function of n and k. While the Levenshtein distance is a fundamental theoretical notion, most practical applications use weighted edit distance, where the weight (cost) of each edit can be an arbitrary real number in [1,∞) that may depend on the edit type and the characters involved. Unfortunately, the <PERSON><PERSON>–<PERSON> algorithm does not generalize to the weighted setting and, for many decades, a simple O(nk)-time dynamic programming procedure remained the state of the art for bounded weighted edit distance. Only recently, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> [STOC’23] provided an O(n+k5)-time algorithm; shortly afterward, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> [FOCS’23] presented an Õ(n+√nk3)-time solution (where Õ(·) hides polylogn factors) and proved this runtime optimal for √n ≤ k ≤ n (up to sub-polynomial factors and conditioned on the All-Pairs Shortest Paths Hypothesis). Notably, the hard instances constructed to establish the underlying conditional lower bound use fractional weights with large denominators, reaching (n), which stands in contrast to weight functions used in practice (e.g., in bioinformatics) that, after normalization, typically attain small integer values. Our first main contribution is a surprising discovery that the Õ(n+k2) running time of the Landau–Vishkin algorithm can be recovered if the weights are small integers (e.g., if some specific weight function is fixed in a given application). In general, our solution takes Õ(n+min{W,√k}· k2) time for integer weights not exceeding a threshold W. Despite matching time complexities, we do not use the Landau–Vishkin framework; instead, we build upon the recent techniques for arbitrary weights. For this, we exploit extra structure following from integer weights and avoid further bottlenecks using several novel ideas to give faster and more robust implementations of multiple steps of the previous approach. Next, we shift focus to the dynamic version of the unweighted edit distance problem, which asks to maintain the edit distance of two strings that change dynamically, with each update modeled as a single edit (character insertion, deletion, or substitution). For many years, the best approach for dynamic edit distance combined the Landau–Vishkin algorithm with a dynamic strings implementation supporting efficient substring equality queries, such as one by Mehlhorn, Sundar, and Uhrig [SODA’94]; the resulting solution supports updates in Õ(k2) time. Recently, Charalampopoulos, Kociumaka, and Mozes [CPM’20] observed that a framework of Tiskin [SODA’10] yields a dynamic algorithm with an update time of Õ(n). This is optimal in terms of n: significantly faster updates would improve upon the static O(n2)-time algorithm and violate OVH. With the state-of-the-art update time at Õ(min{n,k2}), an exciting open question is whether Õ(k) update time is possible. Our second main contribution is an affirmative answer to this question: we present a deterministic dynamic algorithm that maintains the edit distance in Õ(k) worst-case time per update. Surprisingly, this result builds upon our new static solution and hence natively supports small integer weights: if they do not exceed a threshold W, the update time becomes Õ(W2 k).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718168"}, {"primary_key": "197969", "vector": [], "sparse_vector": [], "title": "Approximation Guarantees of Median Mechanism in ℝᵈ.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The coordinate-wise median is a classic and most well-studied strategy-proof mechanism in social choice and facility location scenarios. Surprisingly, there is no systematic study of its approximation ratio in d-dimensional spaces. The best known approximation guarantee in d-dimensional Euclidean space L2(ℝd) is √d via a simple argument of embedding L1(ℝd) into L2(ℝd) metric space, that only appeared in appendix of [<PERSON><PERSON> 2019]. This upper bound is known to be tight in dimension d=2 from [<PERSON><PERSON> and <PERSON><PERSON> 2023], but there are no known super constant lower bounds. A few recent papers on mechanism design with predictions (e.g., [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tan 2022], [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Vlachos 2024], and [<PERSON><PERSON>, <PERSON>, <PERSON> 2024]) directly rely on the √d-approximation result. In this paper, we systematically study approximate efficiency of the coordinate-median in Lq(ℝd) spaces for any Lq norm with q∈[1,∞] and any dimension d. We derive a series of constant upper bounds UB(q) independent of the dimension d. This series UB(q) is growing with parameter q, but never exceeds the constant UB(∞)= 3. Our bound UB(2)=√6√3−81.41 in dimension d=2. Furthermore, we show that our upper bounds are essentially tight by giving almost matching lower bounds LB(q,d)=UB(q)·(1−O(1/d)) for any dimension d with LB(q,d)=UB(q) when d→∞. We also extend our analysis to the generalized median mechanism used in [Agrawal, Balkanski, Gkatzelis, Ou, Tan 2022] for L2(ℝ2) space to arbitrary dimensions d with similar results for both robustness and consistency approximation guarantees.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718156"}, {"primary_key": "197970", "vector": [], "sparse_vector": [], "title": "Lifting Linear Sketches: Optimal Bounds and Adversarial Robustness.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Huacheng Yu", "<PERSON>"], "summary": "We introduce a novel technique for “lifting” dimension lower bounds for linear sketches in the real-valued setting to dimension lower bounds for linear sketches with polynomially-bounded integer entries when the input is a polynomially-bounded integer vector. Using this technique, we obtain the first optimal sketching lower bounds for discrete inputs in a data stream, for classical problems such as approximating the frequency moments, estimating the operator norm, and compressed sensing. Additionally, we lift the adaptive attack of <PERSON><PERSON> and <PERSON> (STOC, 2013) for breaking any real-valued linear sketch via a sequence of real-valued queries, and show how to obtain an attack on any integer-valued linear sketch using integer-valued queries. This shows that there is no linear sketch in a data stream with insertions and deletions that is adversarially robust for approximating any Lp norm of the input, resolving a central open question for adversarially robust streaming algorithms. To do so, we introduce a new pre-processing technique of independent interest which, given an integer-valued linear sketch, increases the dimension of the sketch by only a constant factor in order to make the orthogonal lattice to its row span smooth. This pre-processing then enables us to leverage results in lattice theory on discrete Gaussian distributions and reason that efficient discrete sketches imply efficient continuous sketches. Our work resolves open questions from the Banff ’14 and ’17 workshops on Communication Complexity and Applications, as well as the STOC ’21 and FOCS ’23 workshops on adaptivity and robustness.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718227"}, {"primary_key": "197971", "vector": [], "sparse_vector": [], "title": "On the Complexity of Isomorphism Problems for Tensors, Groups, and Polynomials IV: Linear-Length Reductions and Their Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "By giving new reductions, we show the following algorithmic results and relations between various isomorphism problems: If Graph Isomorphism is in P, then testing equivalence of cubic forms in n variables over a finite field Fq, and testing isomorphism of n-dimensional algebras over Fq, can both be solved in time qO(n), improving from the brute-force upper bound qO(n2) for both of these. Polynomial-time search- and counting-to-decision reduction for testing isomorphism of p-groups of class 2 and exponent p in the <PERSON><PERSON>ley table model. This answers questions of <PERSON><PERSON><PERSON> and <PERSON> (Bull. EATCS, 2005) for this group class, thought to be one of the hardest cases of Group Isomorphism. Combined with the |G|O((log|G|)1/2)-time isomorphism test for p-groups of Frattini class 2 (<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, FOC<PERSON> ’24), our reductions extend this runtime to p-groups of exponent p and class c < p. Our reductions show that several other Tensor Isomorphism-complete problems over a finite prime field Fq can be solved in time qÕ(n3/2), where n is the side length. This improves the previous state of the art bound, which was the brute force qO(n2), for the isomorphism problems for cubic forms, algebras, tensors, and more. The key to our reductions is to give new gadgets that improve the parameters of previous reductions around Tensor Isomorphism (<PERSON><PERSON><PERSON><PERSON> and <PERSON>, ITCS ’21; S<PERSON>M J. Comp., ’23). In particular, several of these previous reductions incurred a quadratic increase in the length of the tensors involved. When the tensors represent p-groups, this corresponds to an increase in the order of the group of the form |G|Θ(log|G|), negating any asymptotic gains in the Cayley table model. We remedy this by presenting a new kind of tensor gadget that allows us to replace those quadratic-length reductions with linear-length ones, yielding the above consequences.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718282"}, {"primary_key": "197972", "vector": [], "sparse_vector": [], "title": "On the Complexity of Isomorphism Problems for Tensors, Groups, and Polynomials V: Over Commutative Rings.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tensors over commutative rings naturally appear in number theory, geometry, and group theory. For example, 2× 2× 2 tensors over ℤ form the starting point of <PERSON><PERSON><PERSON><PERSON>’s celebrated works generalising <PERSON><PERSON><PERSON>’s composition law (<PERSON><PERSON><PERSON><PERSON>, Ann. Math., 2004). Symmetric tensors over ℤ are central to the classification of Calabi–Yau threefolds (<PERSON><PERSON>, <PERSON><PERSON><PERSON>. Pure Appl. Math., 1978; Wall, Invent. Math., 1966), geometric objects of significance in string theory. Additionally, tensors over finite commutative rings closely correspond to finite nilpotent groups of class 2 (<PERSON><PERSON>, Trans. Am. Math. Soc., 1938). In these settings, testing isomorphism of tensors is of great interest. For example, in mathematical physics, several recent works apply machine learning techniques to distinguish symmetric tensors from Calabi–Yau threefolds. For group isomorphism, a recent breakthrough of Sun (STOC, 2023) gives the first No(logN)-time algorithm for testing isomorphism of p-groups of class 2 and exponent p of order N, using tensor-based techniques. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> studied the computability of tensor isomorphism problems over ℤ, showing that they are computable in finite time (<PERSON><PERSON>, 1980). In this work, we study isomorphism testing of tensors over commutative rings from a complexity-theoretic viewpoint, and its applications. Some of our main results are as follows. Let R be a commutative ring. We introduce two complexity classes: 3TIR consisting of problems that are polynomial-time reducible to isomorphism problems of tensor products of three modules over R, and 3FTIR consisting of problems that are polynomial-time reducible to isomorphism problems of tensor products of three free modules over R. We show that some classical problems considered by Grunewald and Segal (ibid.), and the problem of classifying Calabi–Yau threefolds, are 3FTIℤ-complete. We also show that many natural problems are complete for 3TIℤ/peℤ. We show that testing isomorphism of tensors in ℤ2⊗ℤ2⊗ℤ2 is polynomial-time equivalent to the principal ideal problem in algorithmic number theory. The key to this reduction is Bhargava’s work (Ann. Math., 2004). Using our equivalence, a result of Hallgren (J. ACM, 2007) then implies that 2× 2× 2 tensor isomorphism over ℤ is in quantum polynomial time. We present an NO((logN)8/9)-time algorithm for testing isomorphism of finite nilpotent groups of class 2 and odd order N. This is achieved by considering tensor isomorphism over ℤ/peℤ. Following the strategy of (Sun, STOC, 2023), the algorithm is a reduction to testing the congruence of matrix tuples over ℤ/peℤ, for which we present a polynomial-time solution following and generalizing (Ivanyos–Qiao, SIAM J. Comput., 2019), who solved the analogous problem over finite fields of odd order.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718286"}, {"primary_key": "197973", "vector": [], "sparse_vector": [], "title": "Student-Teacher Constructive Separations and (Un)Provability in Bounded Arithmetic: Witnessing the Gap.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Let C be a complexity class and A be a language. The statement “A ∉ C” is a separation of A from C. A separation is constructive if there is an efficient algorithm called a refuter that prints counterexamples to the statement “<PERSON> decides A” for every C-algorithm M. Concretely, refuters witness errors of M on A by printing, on input 1n, an n-bit string x such that M(x) ≠ A(x). Many recent breakthroughs in lower bounds and derandomization that use the algorithmic method rely on constructive separations as a core component (<PERSON>, <PERSON>, <PERSON> 2020, for example). <PERSON>, <PERSON>, <PERSON>, and <PERSON> (2022) studied the consequences of constructivizing classical non-constructive lower bounds in complexity theory. They showed that (1) constructivizing many known separations would imply breakthrough lower bounds, and (2) some separations are impossible to constructivize. We study a more general notion of “efficient refutation” by C-Student-Teacher Games, where the C-refuter (Student) is allowed to adaptively propose candidate counterexamples xi to an omniscient Teacher. If xi fails to witness an error, Teacher reveals a counterexample yi to the statement “xi is a counterexample to the statement ‘M decides A’ ” — the nature of yi depending on how the separated language A and complexity class C are defined. We show: If there is a P-Student-Teacher constructive separation of Palindromes from one-tape nondeterministic O(n1 + ε) time (<PERSON><PERSON> 1984), then NP ⊄SIZE[nk] for every k. If there is a uniform AC0[qpoly]-Student-Teacher protocol generating truth tables of super fixed polynomial circuit complexity, then P ≠ NP. There is no P-Student-Teacher protocol which for infinitely many c>0, generates high-Knc strings. Our results imply a conditional separation of Jeřábek’s theory VAPC from V1. This improves and simplifies the work of Ilango, Li, and Williams (2023), who separate VAPC from the weaker theory VPV under the existence of indistinguishability obfuscation. We do not use cryptographic assumptions in our separation. Instead we introduce a natural and plausible conjecture on the uniformity of proofs in bounded arithmetic, inspired by Kreisel’s Conjecture in logic. We believe this conjecture to be of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718216"}, {"primary_key": "197974", "vector": [], "sparse_vector": [], "title": "Classical Commitments to Quantum States.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We define the notion of a classical commitment scheme to quantum states, which allows a quantum prover to compute a classical commitment to a quantum state, and later open each qubit of the state in either the standard or the <PERSON><PERSON><PERSON> basis. Our notion is a strengthening of the measurement protocol from <PERSON><PERSON><PERSON> (STOC 2018). We construct such a commitment scheme from the post-quantum Learning With Errors (LWE) assumption, and more generally from any noisy trapdoor claw-free function family that has the distributional strong adaptive hardcore bit property (a property that we define in this work). Our scheme is succinct in the sense that the running time of the verifier in the commitment phase depends only on the security parameter (independent of the size of the committed state), and its running time in the opening phase grows only with the number of qubits that are being opened (and the security parameter). As a corollary we obtain a classical succinct argument system for QMA under the post-quantum LWE assumption. Previously, this was only known assuming post-quantum secure indistinguishability obfuscation. As an additional corollary we obtain a generic way of converting any X/Z quantum PCP into a succinct argument system under the quantum hardness of LWE.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718264"}, {"primary_key": "197975", "vector": [], "sparse_vector": [], "title": "Quantum One-Time Programs, Revisited.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "One-time programs (<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, CRYPTO 2008) are programs that can be run on any single input of a user’s choice, but not on a second input. Classically, they are unachievable without trusted hardware, but the destructive nature of quantum measurements seems to provide an alternate path to constructing them. Unfortunately, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (CRYPTO 2013) showed that even with quantum techniques, a strong notion of one-time programs, similar to ideal obfuscation, cannot be achieved for any non-trivial quantum function. On the positive side, <PERSON><PERSON><PERSON> and <PERSON> (Quantum, 2023) showed how to construct a quantum one-time program for a certain (probabilistic) digital signature scheme, under a weaker notion of one-time program security. There is a vast gap between achievable and provably impossible notions of one-time program security, and it is unclear what functionalities are one-time programmable and which are not, under the achievable notions of security. In this work, we present new, meaningful, yet achievable definitions of one-time program security for probabilistic classical functions. We show how to construct one time programs satisfying these definitions for all functions in the classical oracle model and for constrained pseudorandom functions in the plain model. Finally, we examine the limits of these notions: we show a class of functions which cannot be one-time programmed in the plain model, as well as a class of functions which appears to be highly random given a single query, but whose quantum one-time program leaks the entire function even in the oracle model.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718204"}, {"primary_key": "197976", "vector": [], "sparse_vector": [], "title": "A Zero-Knowledge PCP Theorem.", "authors": ["<PERSON>", "Jack <PERSON>&<PERSON>;<PERSON>", "<PERSON>"], "summary": "We show that for every polynomial 𝑞∗ there exist polynomial-size, constant-query, non-adaptive PCPs for NP which are perfect zero knowledge against (adaptive) adversaries making at most 𝑞∗ queries to the proof. In addition, we construct exponential-size constant- query PCPs for NEXP with perfect zero knowledge against any polynomial-time adversary. This improves upon both a recent con- struction of perfect zero-knowledge PCPs for #P (STOC 2024) and the seminal work of <PERSON><PERSON>, <PERSON> and <PERSON> (STOC 1997).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718128"}, {"primary_key": "197977", "vector": [], "sparse_vector": [], "title": "Almost Optimal Time Lower Bound for Approximating Parameterized Clique, CSP, and More, under ETH.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "Bing<PERSON> Lin", "<PERSON><PERSON><PERSON>", "Yican Sun", "<PERSON><PERSON>"], "summary": "The Parameterized Inapproximability Hypothesis (PIH), which is an analog of the PCP theorem in parameterized complexity, asserts the following: there is a constant ε> 0 such that for any computable function f:ℕ→ℕ, no f(k)· nO(1)-time algorithm can, on input a k-variable CSP instance with domain size n, find an assignment satisfying 1−ε fraction of the constraints. A recent work by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON> (STOC’24) established PIH under the Exponential Time Hypothesis (ETH). In this work, we improve the quantitative aspects of PIH and prove (under ETH) that approximating sparse parameterized CSPs within a constant factor requires nk1−o(1) time. This immediately implies, for example, that finding a (k/2)-clique in an n-vertex graph with a k-clique requires nk1−o(1) time (assuming ETH). We also prove almost optimal time lower bounds for approximating k-ExactCover and Max k-Coverage. Our proof follows the blueprint of the previous work to identify a ”vector-structured” ETH-hard CSP whose satisfiability can be checked via an appropriate form of ”parallel” PCP. Using further ideas in the reduction, we guarantee additional structures for constraints in the CSP. We then leverage this to design a parallel PCP of almost linear size based on Reed-Muller codes and derandomized low degree testing.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718130"}, {"primary_key": "197978", "vector": [], "sparse_vector": [], "title": "Single-Copy Stabilizer Testing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider the problem of testing whether an unknown n-qubit quantum state is a stabilizer state, with only single-copy access. We give an algorithm solving this problem using O(n) copies, and conversely prove that Ω(√n) copies are required for any algorithm. The main observation behind our algorithm is that when repeatedly measuring in a randomly chosen stabilizer basis, stabilizer states are the most likely among the set of all pure states to exhibit linear dependencies in measurement outcomes. Our algorithm is designed to probe deviations from this extremal behavior. For the lower bound, we first reduce stabilizer testing to the task of distinguishing random stabilizer states from the maximally mixed state. We then argue that, without loss of generality, it is sufficient to consider measurement strategies that a) lie in the commutant of the tensor action of the Clifford group and b) satisfy a Positive Partial Transpose (PPT) condition. By leveraging these constraints, together with novel results on the partial transposes of the generators of the Clifford commutant, we derive the lower bound on the sample complexity.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718169"}, {"primary_key": "197979", "vector": [], "sparse_vector": [], "title": "Error-Correction of Matrix Multiplication Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given an efficient algorithm that correctly computes a tiny fraction of the entries of the matrix multiplication of a small fraction of two matrices, can one design an efficient algorithm that computes matrix multiplication exactly for all the matrices? In this paper, we present such “worst-case exact to average-case approximate” reductions that transform any algorithm that correctly computes a tiny fraction of the entries of the multiplication of two uniformly random matrices over a finite field into a randomized worst-case algorithm that computes matrix multiplication for all the matrices. Under non-uniform reductions, we present an optimal reduction that error-corrects an algorithm whose output has expected Hamming distance 1 − 1/p − ε to the multiplication of two random matrices over a finite field of size p for any positive constant ε > 0. Under uniform reductions, we present efficient reductions that correct a (1 − ε)-fraction of errors over a field of size p for all ε > 0 and for all sufficiently large p. We also present an optimal uniform reduction for the Online Matrix-Vector Multiplication problem. The non-uniform reduction is based on a new and simple proof of <PERSON>’s XOR lemma for multi-output functions, whose complexity overhead is independent of the length of the output.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718244"}, {"primary_key": "197980", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Biconnectivity in Õ(log² n) Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a deterministic fully-dynamic data structure for maintaining information about the cut-vertices in a graph; i.e. the vertices whose removal would disconnect the graph. Our data structure supports insertion and deletion of edges, as well as queries to whether a pair of connected vertices are either biconnected, or can be separated by a cutvertex, and in the latter case we support access to separating cutvertices. All update operations are supported in amortized O(log² n log² log n) time, and queries take worst-case O(log n log² log n) time. Note that these time bounds match the current best for deterministic dynamic connectivity up to log log n factors. The previous best algorithm for biconnectivity had an update time of O(log⁴ n log log n) by <PERSON><PERSON> [STOC'00], based on the O(log⁵ n) data structure by <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> [STOC'98]. We obtain our improved running time by a series of reductions from the original problem into well-defined data structure problems. While we do indeed apply the well-known techniques for improving running time of two-edge connectivity [STOC'00, SODA'18], surprisingly, these techniques alone do not lead to an update time of Õ(log³ n), let alone the Õ(log² n) we give as a final result. Our contributions include a formally defined transient expose operation, which can be thought of as a cheaper read-only expose operation on a top tree. For each vertex in the graph, we maintain a data structure over its neighbors, and in this data structure we apply biasing (twice) to save an Õ(log n) factor (twice, so two Õ(log n) factors). One of these biasing techniques is a new, simple biased disjoint sets data structure, which may be of independent interest. Moreover, in this neighborhood data structure, we facilitate that the vertex can select two VIP neighbors that get special treatment, corresponding to its potentially two neighbors on an exposed path, improving an otherwise log n-time operation down to constant time. It is this combination of VIP neighbors with the transient expose operation that saves an Õ(log n)-factor from another bottleneck. Combining these technical contributions with the well-known techniques for two-edge connectivity [STOC'00, SODA'18], we obtain the desired update times of O(log² n log² log n). The near-linear query time follows directly from the usage of transient expose.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718270"}, {"primary_key": "197981", "vector": [], "sparse_vector": [], "title": "Hypercontractivity on HDX II: Symmetrization and q-Norms.", "authors": ["<PERSON>"], "summary": "<PERSON><PERSON><PERSON>’s symmetrization theorem is a powerful technique reducing boolean analysis on product spaces to the cube. It states that for any product Ωi⊗ d, function f: Ωi⊗ d → , and q > 1: ||T_1/2f(x)||_q ≤||f(r,x)||_q ≤||T_c_qf(x)||_q where Tρf = ∑ρSf=S is the noise operator and f(r,x) = ∑rSf=S(x) ‘symmetrizes’ f by convolving its Fourier components {f=S}S ⊆ [d] with a random boolean string r ∈ {± 1}d. In this work, we extend the symmetrization theorem to high dimensional expanders (HDX). Building on (<PERSON><PERSON> and <PERSON> 2021), we show this implies nearly-sharp (2→q)-hypercontractivity for partite HDX. This resolves the main open question of (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and Liu STOC 2022) and gives the first fully hypercontractive subsets X ⊂ [n]d of support n·exp((d)), an exponential improvement over <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>’s n·exp(exp(d)) bound (BHKL STOC 2022). Adapting (<PERSON><PERSON><PERSON> 1999), we also give the first booster theorem for HDX, resolving a main open question of BHKL. Our proof is based on two elementary new ideas in the theory of high dimensional expansion. First we introduce ‘q-norm HDX’, generalizing standard spectral notions to higher moments, and observe every spectral HDX is a q-norm HDX. Second, we introduce a simple method of coordinate-wise analysis on HDX which breaks high dimensional random walks into coordinate-wise components and allows each component to be analyzed as a 1-dimensional operator locally within X. This allows for application of standard tricks such as the replacement method, greatly simplifying prior analytic techniques. This is an extended abstract. The full paper may be found at https://arxiv.org/abs/2408.16687", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718187"}, {"primary_key": "197982", "vector": [], "sparse_vector": [], "title": "Explicit Two-Sided Vertex Expanders beyond the Spectral Barrier.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ryan <PERSON>&<PERSON>;Donnell", "<PERSON>"], "summary": "We construct the first explicit two-sided vertex expanders that bypass the spectral barrier. Previously, the strongest known explicit vertex expanders were given by d-regular Ramanujan graphs, whose spectral properties imply that every small subset of vertices S has at least 0.5d|S| distinct neighbors. However, it is possible to construct Ramanujan graphs containing a small set S with no more than 0.5d|S| neighbors. In fact, no explicit construction was known to break the 0.5 d-barrier. In this work, we give an explicit construction of an infinite family of d-regular graphs (for large enough d) where every small set expands by a factor of ≈ 0.6d. More generally, for large enough d1,d2, we give an infinite family of (d1,d2)-biregular graphs where small sets on the left expand by a factor of ≈ 0.6d1, and small sets on the right expand by a factor of ≈ 0.6d2. In fact, our construction satisfies an even stronger property: small sets on the left and right have unique-neighbor expansion 0.6d1 and 0.6d2 respectively. Our construction follows the tripartite line product framework of <PERSON><PERSON><PERSON> et. al., and instantiates it using the face-vertex incidence of the 4-dimensional Ramanujan clique complex as its base component. As a key part of our analysis, we derive new bounds on the triangle density of small sets in the Ramanujan clique complex.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718241"}, {"primary_key": "197983", "vector": [], "sparse_vector": [], "title": "Optimal Static Dictionary with Worst-Case Constant Query Time.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Huacheng Yu", "Jun<PERSON> Zhang", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we design a new succinct static dictionary with worst-case constant query time. A dictionary data structure stores a set of key-value pairs with distinct keys in [U] and values in [σ], such that given a query x ∈ [U], it quickly returns if x is one of the input keys, and if so, also returns its associated value. The textbook solution to dictionaries is hash tables. On the other hand, the (information-theoretical) optimal space to encode such a set of key-value pairs is only OPT := log( Un ) + n logσ. We construct a dictionary that uses OPT + nε bits of space, and answers queries in constant time in worst case. Previously, constant-time dictionaries are only known with OPT + n / poly logn space, or with OPT + nε space but expected constant query time. We emphasize that most of the extra nε bits are used to store a lookup table that does not depend on the input, and random bits for hash functions. The “main” data structure only occupies OPT + poly logn bits.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718278"}, {"primary_key": "197984", "vector": [], "sparse_vector": [], "title": "Omnipredicting Single-Index Models with Multi-index Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent work on supervised learning defined the notion of omnipredictors, i.e., predictor functions p over features that are simultaneously competitive for minimizing a family of loss functions L against a comparator class C. Omniprediction requires approximating the Bayes-optimal predictor beyond the loss minimization paradigm, and has generated significant interest in the learning theory community. However, even for basic settings such as agnostically learning single-index models (SIMs), existing omnipredictor constructions require impractically-large sample complexities and runtimes, and output complex, highly-improper hypotheses. Our main contribution is a new, simple construction of omnipredictors for SIMs. We give a learner outputting an omnipredictor that is ε-competitive on any matching loss induced by a monotone, Lipschitz link function, when the comparator class is bounded linear predictors. Our algorithm requires ≈ ε−4 samples and runs in nearly-linear time, and its sample complexity improves to ≈ ε−2 if link functions are bi-Lipschitz. This significantly improves upon the only prior known construction, which used ≳ ε−10 samples. We achieve our construction via a new, sharp analysis of the classical Isotron algorithm in the challenging agnostic learning setting, of potential independent interest. Previously, Isotron was known to properly learn SIMs in the realizable setting, as well as constant-factor competitive hypotheses under the squared loss. As they are based on Isotron, our omnipredictors are multi-index models with ≈ ε−2 prediction heads, bringing us closer to the tantalizing goal of proper omniprediction for general loss families and comparators.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718223"}, {"primary_key": "197985", "vector": [], "sparse_vector": [], "title": "Near-Optimal Dimension Reduction for Facility Location.", "authors": ["<PERSON><PERSON><PERSON>", "Shaofeng H.-<PERSON><PERSON> Jiang", "<PERSON>", "<PERSON>"], "summary": "Oblivious dimension reduction, à la the <PERSON> (JL) Lemma, is a fundamental approach for processing high-dimensional data. We study this approach for Uniform Facility Location (UFL) on a Euclidean input X ⊂ℝd, where facilities can lie in the ambient space (not restricted to X). Our main result is that target dimension m=Õ(є−2 ddim) suffices to (1+є)-approximate the optimal value of UFL on inputs whose doubling dimension is bounded by ddim. It significantly improves over previous results, that could only achieve O(1)-approximation [<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, ICML 2021] or dimension m=O(є−2logn) for n=|X|, which follows from [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, STOC 2019]. Our oblivious dimension reduction has immediate implications to streaming and offline algorithms, by employing known algorithms for low dimension. In dynamic geometric streams, it implies a (1+є)-approximation algorithm that uses O(є−1logn)Õ(ddim/є2) bits of space, which is the first streaming algorithm for UFL to utilize the doubling dimension. In the offline setting, it implies a (1+є)-approximation algorithm, which we further refine to run in time ((1/є)Õ(ddim) d + 2(1/є)Õ(ddim)) · Õ(n). Prior work has a similar running time but requires some restriction on the facilities [<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, JACM 2021]. Our main technical contribution is a fast procedure to decompose an input X into several k-median instances for small k. This decomposition is inspired by, but has several significant differences from [Czumaj, Lammersen, Monemizadeh and Sohler, SODA 2013], and is key to both our dimension reduction and our PTAS.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718214"}, {"primary_key": "197986", "vector": [], "sparse_vector": [], "title": "Weak Poincaré Inequalities, Simulated Annealing, and Sampling from Spherical Spin Glasses.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "There has been a recent surge of powerful tools to show rapid mixing of Markov chains, via functional inequalities such as Poincaré inequalities. In many situations, Markov chains fail to mix rapidly from a worst-case initialization, yet are expected to approximately sample from a random initialization. For example, this occurs if the target distribution has metastable states, small clusters accounting for a vanishing fraction of the mass that are essentially disconnected from the bulk of the measure. Under such conditions, a Poincaré inequality cannot hold, necessitating new tools to prove sampling guarantees. We develop a framework to analyze simulated annealing, based on establishing so-called weak Poincaré inequalities. These inequalities imply mixing from a suitably warm start, and simulated annealing provides a way to chain such warm starts together into a sampling algorithm. We further identify a local-to-global principle to prove weak Poincaré inequalities, mirroring the spectral independence and localization schemes frameworks for analyzing mixing times of Markov chains. As our main application, we prove that simulated annealing samples from the Gibbs measure of a spherical spin glass for inverse temperatures up to a natural threshold, matching recent algorithms based on algorithmic stochastic localization. This provides the first Markov chain sampling guarantee that holds beyond the uniqueness threshold for spherical spin glasses, where mixing from a worst-case initialization is provably slow due to the presence of metastable states. As an ingredient in our proof, we prove bounds on the operator norm of the covariance matrix of spherical spin glasses in the full replica-symmetric regime. Additionally, we resolve a question related to sampling using data-based initializations. The full version of this paper can be found on arXiv (arXiv ID: 2411.09075).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718236"}, {"primary_key": "197987", "vector": [], "sparse_vector": [], "title": "Simple and Optimal Algorithms for Heavy Hitters and Frequency Moments in Distributed Models.", "authors": ["<PERSON><PERSON><PERSON> Huang", "Zhongzheng Xiong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problems of distributed heavy hitters and frequency moments in both the coordinator model and the distributed tracking model (also known as the distributed functional monitoring model). We present simple and optimal (up to logarithmic factors) algorithms for ℓp heavy hitters and Fp estimation (p ≥ 2) in these distributed models. For ℓp heavy hitters in the coordinator model, our algorithm requires only one round and uses Õ(kp−1/p) bits of communication. For p > 2, this is the first near-optimal result. By combining our algorithm with the standard recursive sketching technique, we obtain a near-optimal two-round algorithm for Fp in the coordinator model, matching a significant result from recent work by <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (STOC 2024). Our algorithm and analysis are much simpler and have better costs with respect to logarithmic factors. Furthermore, our technique provides a one-round algorithm for Fp, which is a significant improvement over a result of <PERSON> and <PERSON> (STOC 2012). Thanks to the simplicity of our heavy hitter algorithms, we manage to adapt them to the distributed tracking model with only a (n) increase in communication. For ℓp heavy hitters, our algorithm has a communication cost of Õ(kp−1/p), representing the first near-optimal algorithm for all p ≥ 2. By applying the recursive sketching technique, we also provide the first near-optimal algorithm for Fp in the distributed tracking model, with a communication cost of Õ(kp−1/2) for all p ≥ 2. Even for F2, our result improves upon the bounds established by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON> (SODA 2008) and <PERSON> and <PERSON> (STOC 2012), nearly matching the existing lower bound for the first time.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718199"}, {"primary_key": "197988", "vector": [], "sparse_vector": [], "title": "Protecting Computations against Continuous Bounded-Communication Leakage.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the question of protecting a general computation device, modeled by a stateful Boolean circuit, against leakage of partial information about its internal wires. <PERSON><PERSON> et al. (FOCS 2016) obtained a solution for the case of bounded-communication leakage, where the wires are partitioned into two parts and the leakage can be any function computed using t bits of communication between the parts. However, this solution suffers from two major limitations: (1) it only applies to a one-shot (stateless) computation, mapping an encoded input to an encoded output, and (2) the leakage-resilient circuit consumes fresh random bits, whose number scales linearly with the circuit complexity of the computed function. In this work, we eliminate the first limitation and make progress on the second. Concretely: - We present the first construction of stateful circuits that offer information-theoretic protection against continuous bounded-communication leakage. As an application, we extend a two-party “malware-resilient” protocol of <PERSON><PERSON> et al. to the continuous-leakage case. - For simple types of bounded-communication leakage, which leak t parities or t disjunctions of circuit wires or their negations, we obtain a deterministic variant that does not require any fresh randomness beyond the randomness in the initial state. Here we get computational security based on a subexponentially secure one-way function. This is the first deterministic leakage-resilient circuit construction for any nontrivial class of global leakage.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718200"}, {"primary_key": "197989", "vector": [], "sparse_vector": [], "title": "Fast, Robust Approximate Message Passing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a fast, spectral procedure for implementing approximate-message passing (AMP) algorithms robustly. For any quadratic optimization problem over symmetric matrices X with independent subgaussian entries, and any separable AMP algorithm A, our algorithm performs a spectral pre-processing step and then mildly modifies the iterates of A. If given the perturbed input X + E ∈ ℝn × n for any E supported on a ε n × ε n principal minor, our algorithm outputs a solution v which is guaranteed to be close to the output of A on the uncorrupted X, with ||A(X) − v||2 ≤ f(ε) ||A(X)||2 where f(ε) → 0 as ε → 0 depending only on ε.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718165"}, {"primary_key": "197990", "vector": [], "sparse_vector": [], "title": "Linear Hashing Is Optimal.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We prove that hashing n balls into n bins via random 2-linear maps yields expected maximum load O(logn / loglogn), resolving an open question of <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (STOC ’97, JACM ’99). More generally, we show that the maximum load exceeds r· logn/loglogn with probability at most O(1/r2). Our proof uses potential functions to detect heavy bins.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718208"}, {"primary_key": "197991", "vector": [], "sparse_vector": [], "title": "Explicit Codes Approaching Generalized Singleton Bound using Expanders.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We construct a new family of explicit codes that are list decodable to capacity and achieve an optimal list size of O(1/є). In contrast to existing explicit constructions of codes achieving list decoding capacity, our arguments do not rely on algebraic structure but utilize simple combinatorial properties of expander graphs. Our construction is based on a celebrated distance amplification procedure due to <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> [FOCS’95], which transforms any high-rate code into one with near-optimal rate-distance tradeoff. We generalize it to show that the same procedure can be used to transform any high-rate code into one that achieves list decoding capacity. Our proof can be interpreted as a ”local-to-global” phenomenon for (a slight strengthening of) the generalized Singleton bound. Using this construction, for every R, є ∈ (0,1) and k ∈ ℕ+, we obtain an explicit family of rate R codes C ⊆ Σn that achieve the є-relaxed generalized Singleton bound. The alphabet size of these codes is a constant depending only on є and k, and they can be list decoded up to radius k−1/k · (1−R−є), in time nOk,є(1) with a list of size k−1. As a corollary of our result, we also obtain the first explicit construction of LDPC codes achieving list decoding capacity, and in fact arbitrarily close to the generalized Singleton bound.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718302"}, {"primary_key": "197992", "vector": [], "sparse_vector": [], "title": "Positive Bias Makes Tensor-Network Contraction Tractable.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tensor network contraction is a powerful computational tool in quantum many-body physics, quantum information and quantum chemistry. The complexity of contracting a tensor network is thought to mainly depend on its entanglement properties, as reflected by the Schmidt rank across bipartite cuts. Here, we study how the complexity of tensor-network contraction depends on a different notion of quantumness, namely, the sign structure of its entries. We tackle this question rigorously by investigating the complexity of contracting tensor networks whose entries have a positive bias. We show that for intermediate bond dimension d≳ n, a small positive mean value ≳ 1/d of the tensor entries already dramatically decreases the computational complexity of approximately contracting random tensor networks, enabling a quasi-polynomial time algorithm for arbitrary 1/poly(n) multiplicative approximation. At the same time exactly contracting such tensor networks remains #P-hard, like for the zero-mean case. The mean value 1/d matches the phase transition point observed in previous work. Our proof makes use of <PERSON><PERSON><PERSON>’s method for approximate counting and the technique of mapping random instances to statistical mechanical models. We further consider the worst-case complexity of approximate contraction of positive tensor networks, where all entries are non-negative. We first give a simple proof showing that a multiplicative approximation with error exponentially close to one is at least StoqMA-hard. We then show that when considering additive error in the matrix 1-norm, the contraction of positive tensor network is BPP-complete. This result compares to <PERSON><PERSON> and <PERSON><PERSON>’s result, which shows that for general tensor networks, approximate contraction up to matrix 2-norm additive error is BQP-complete. Our work thus identifies new parameter regimes in terms of the positivity of the tensor entries in which tensor networks can be (nearly) efficiently contracted.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718105"}, {"primary_key": "197993", "vector": [], "sparse_vector": [], "title": "Improved Complexity for Smooth Nonconvex Optimization: A Two-Level Online Learning Approach with Quasi-Newton Methods.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of finding an є-first-order stationary point (FOSP) of a smooth function, given access only to gradient information. The best-known gradient query complexity for this task, assuming both the gradient and Hessian of the objective function are Lipschitz continuous, is O(є−7/4). In this work, we propose a method with a gradient complexity of O(d1/4є−13/8), where d is the problem dimension, leading to an improved complexity when d = O(є−1/2). To achieve this result, we design an optimization algorithm that, underneath, involves solving two online learning problems. Specifically, we first reformulate the task of finding a stationary point for a nonconvex problem as minimizing the regret in an online convex optimization problem, where the loss is determined by the gradient of the objective function. Then, we introduce a novel optimistic quasi-Newton method to solve this online learning problem, with the Hessian approximation update itself framed as an online learning problem in the space of matrices. Beyond improving the complexity bound for achieving an є-FOSP using a gradient oracle, our result provides the first guarantee suggesting that quasi-Newton methods can potentially outperform gradient descent-type methods in nonconvex settings.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718308"}, {"primary_key": "197994", "vector": [], "sparse_vector": [], "title": "Deterministic Vertex Connectivity via Common-Neighborhood Clustering and Pseudorandomness.", "authors": ["<PERSON><PERSON><PERSON>", "Chaitanya Nalam", "Thatchaphol <PERSON>", "Sorrachai <PERSON>i"], "summary": "We give a deterministic algorithm for computing a global minimum vertex cut in a vertex-weighted graph with n vertices and m edges in Ô(mn) time. We use Õ(·) and Ô· to hide log(n) and no(1) factors, respectively. This breaks the long-standing Ω(n4)-time barrier in dense graphs, achievable by trivially computing all-pairs maximum flows. Up to subpolynomial factors, we match the fastest randomized Õ(mn)-time algorithm by [<PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> FOCS’96], and affirmatively answer the question by [Gabow FOCS’00] whether deterministic O(mn)-time algorithms exist even for unweighted graphs. Our algorithm works in directed graphs, too. In unweighted undirected graphs, we present a faster deterministic Ô(mκ)-time algorithm where κ≤ n is the size of the global minimum vertex cut. For a moderate value of κ, this strictly improves upon all previous deterministic algorithms in unweighted graphs with running time Ô(m(n+κ2)) [Even’75], Ô(m(n+κ√n)) [Gabow FOCS’00], and Ô(m2O(κ2)) [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>onthawornchai FOCS’22]. Recently, a linear-time algorithm has been shown by [<PERSON><PERSON><PERSON><PERSON><PERSON>24] for small κ. Our approach applies the common-neighborhood clustering, recently introduced by [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and Ying<PERSON>reonthawornchai STOC’25], in novel ways, e.g., on top of weighted graphs and on top of vertex-expander decomposition. We also exploit pseudorandom objects often used in computational complexity communities, including crossing families based on dispersers from [TaShma, Umans and Zuckerman STOC’01, Wigderson and Zuckerman Combinatorica’99] and selectors based on linear lossless condensers [Cheraghchi’11, Guruswami, Umans and Vadhan JACM’09]. To our knowledge, this is the first application of selectors in graph algorithms.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718304"}, {"primary_key": "197995", "vector": [], "sparse_vector": [], "title": "Universal SNARGs for NP from Proofs of Correctness.", "authors": ["Zhengzhong Jin", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We give new constructions of succinct non-interactive arguments (SNARGs) for NP in the settings of both non-adaptive and adaptive soundness. Our construction of non-adaptive SNARG is universal assuming the security of a (leveled or unleveled) fully homomorphic encryption (FHE) scheme as well as a batch argument (BARG) scheme. Specifically, for any choice of parameters ℓ and L, we construct a candidate SNARG scheme for any NP language L with the following properties: (i) the proof length is ℓ· poly(λ), (ii) the common reference string crs has length L· poly(λ), and (iii) the setup is transparent (no private randomness). We prove that this SNARG has non-adaptive soundness assuming the existence of any SNARG where the proof size is ℓ, the crs size is L, and there is a size L Extended Frege (EF) proof of completeness for the SNARG. Moreover, we can relax the underlying SNARG to be any 2-message privately verifiable argument where the first message is of length L and the second message is of length ℓ. This yields new SNARG constructions based on any “EF-friendly” designated-verifier SNARG or witness encryption scheme. We emphasize that our SNARG is universal in the sense that it does not depend on the argument system. We show several new implications of this construction that do not reference proof complexity: (1) a non-adaptive SNARG for NP with transparent crs from LWE under the evasive LWE heuristic. This gives a candidate lattice-based SNARG for NP. (2) a non-adaptive SNARG for NP with transparent crs assuming the (non-explicit) existence of any iO and LWE. (3) a non-adaptive SNARG for NP with a short and transparent (i.e., uniform) crs assuming LWE, FHE and the (non-explicit) existence of any hash function that makes Micali’s SNARG construction sound. (4) a non-adaptive SNARG for languages such as QR and DCR assuming only LWE. In the setting of adaptive soundness, we show how to convert any designated verifier SNARG into publicly verifiable SNARG, assuming the underlying designated verifier SNARG has an EF proof of completeness. As a corollary, we construct an adaptive SNARG for UP with a transparent crs assuming subexponential LWE under the evasive LWE heuristic. We prove our results by extending the encrypt-hash-and-BARG paradigm of [Jin-Kalai-Lombardi-Vaikuntanathan, STOC ’24].", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718104"}, {"primary_key": "197996", "vector": [], "sparse_vector": [], "title": "The Hypergraph Removal Process.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Let k≥ 2 and fix a k-uniform hypergraph F. Consider the random greedy algorithm for generating a hypergraph without copies of F that, starting from a complete k-uniform hypergraph on n vertices, repeatedly deletes the edges of a copy of F chosen uniformly at random and terminates when no copies of F remain. This algorithm is a special case of the random greedy hypergraph matching algorithm and with an interest in the performance of this special case, we use Rn(F) to denote the number of edges that are left after termination. We show that Rn(F)=nk−1/ρ± o(1), where ρ:=(|E(F)|−1)/(|V(F)|−k), holds with high probability provided that F is strictly k-balanced. Since we may in particular choose F to be a complete hypergraph, this confirms the major folklore conjecture in the area in a very strong form and establishes new precise bounds characterizing the performance of this special case of the random greedy hypergraph matching algorithm.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718110"}, {"primary_key": "197997", "vector": [], "sparse_vector": [], "title": "The Jacobi Factoring Circuit: Quantum Factoring with Near-Linear Gates and Sublinear Space and Depth.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a compact quantum circuit for factoring a large class of integers, including some whose classical hardness is expected to be equivalent to RSA (but not including RSA integers themselves). Most notably, we factor n-bit integers of the form P2 Q with logQ = Θ(na) for a ∈ (2/3, 1) in space and depth sublinear in n (specifically, O(logQ)) using O(n) quantum gates; for these integers, no known classical algorithms exploit the relatively small size of Q to run asymptotically faster than general-purpose factoring algorithms. To our knowledge, this is the first polynomial-time circuit to achieve sublinear qubit count for a classically-hard factoring problem. We thus believe that factoring such numbers has potential to be the most concretely efficient classically-verifiable proof of quantumness currently known. Our circuit builds on the quantum algorithm for squarefree decomposition discovered by <PERSON>, <PERSON>, <PERSON>, and <PERSON> (Nature Scientific Reports 2012), which relies on computing the Jacobi symbol in quantum superposition. The technical core of our contribution is a new space-efficient quantum algorithm to compute the Jacobi symbol of A mod B, in the regime where B is classical and much larger than A. Our circuit for computing the Jacobi symbol generalizes to related problems such as computing the greatest common divisor and modular inverses, and thus could be of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718273"}, {"primary_key": "197998", "vector": [], "sparse_vector": [], "title": "On the Limits of Language Generation: Trade-Offs between Hallucination and Mode-Collapse.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Specifying all desirable properties of a language model is challenging, but certain requirements seem essential for any good model. Given samples drawn from an unknown language, the trained model should (1) produce valid strings that have not been seen in the training data, and (2) be expressive enough to capture the full richness of the language. Otherwise, if the language model outputs invalid strings, it \"hallucinates,\" and if it fails to capture the full range of the language, it suffers from \"mode collapse.\" In this paper, we ask whether it is possible for a language model to meet both of these requirements. We investigate this question within a statistical setting of language generation, building on the seminal works of Gold (Inf. Control, 1967), <PERSON><PERSON><PERSON> (STOC, 1979), and <PERSON><PERSON><PERSON> (Tech. Report, 1988). In this setting, the language model is presented with randomly sampled strings from a distribution supported on an unknown language K, which is only known to belong to a possibly infinite collection of candidate languages. The goal of the model is to generate unseen strings from this target language. We say that the language model generates from K with consistency and breadth if, as the size of the training set increases, the set of strings it can output converges to the set of all unseen strings in K<PERSON> and <PERSON> (NeurIPS, 2024) posed an open question of whether consistency and breadth in language generation are both possible. We answer this question negatively: for a large class of language models -- including next-token-prediction-based models -- this is impossible for most collections of candidate languages. This contrasts with the recent positive result of <PERSON> and <PERSON><PERSON><PERSON><PERSON>, which demonstrated that consistent generation, without requiring breadth, is possible for any countable collection of candidate languages. Our finding highlights that generation with breadth is fundamentally different from generation without breadth. As a byproduct of our result, we also examine how many samples are required for generation with or without breadth, establishing near-tight bounds on the \"learning curves\" for generation in the statistical framework of Bousquet, Hanneke, Moran, van Handel, and Yehudayoff (STOC, 2021). Finally, our results also give some hope for consistent generation with breadth: it is achievable for any countable collection of languages when negative examples -- in the form of strings outside of K -- are available in addition to strings inside of K. This suggests that feedback in post-training, which encodes negative examples, can be crucial in reducing hallucinations while also limiting mode collapse.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718108"}, {"primary_key": "197999", "vector": [], "sparse_vector": [], "title": "Locally Sampleable Uniform Symmetric Distributions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We characterize the power of constant-depth Boolean circuits in generating uniform symmetric distributions. Let f∶{0,1}m→{0,1}n be a Boolean function where each output bit of f depends only on O(1) input bits. Assume the output distribution of f on uniform input bits is close to a uniform distribution D with a symmetric support. We show that D is essentially one of the following six possibilities: (1) point distribution on 0n, (2) point distribution on 1n, (3) uniform over {0n,1n}, (4) uniform over strings with even Hamming weights, (5) uniform over strings with odd Hamming weights, and (6) uniform over all strings. This confirms a conjecture of <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (RANDOM 2023). This is an extended abstract. The full paper can be found at https://arxiv.org/abs/2411.08183v1. An updated version with a stronger result can be found at https://arxiv.org/abs/2411.08183.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718243"}, {"primary_key": "198000", "vector": [], "sparse_vector": [], "title": "On Differentially Private Linear Algebra.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce efficient differentially private (DP) algorithms for several linear algebraic tasks, including solving linear equalities over arbitrary fields, linear inequalities over the reals, and computing affine spans and convex hulls. As an application, we obtain efficient DP algorithms for learning halfspaces and affine subspaces. Our algorithms addressing equalities are strongly polynomial, whereas those addressing inequalities are weakly polynomial. Furthermore, this distinction is inevitable: no DP algorithm for linear programming can be strongly polynomial-time efficient.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718274"}, {"primary_key": "198001", "vector": [], "sparse_vector": [], "title": "Coboundary Expansion of Coset Complexes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Coboundary expansion is a high dimensional generalization of the <PERSON><PERSON><PERSON> constant to simplicial complexes. Originally, this notion was motivated by the fact that it implies topological expansion, but nowadays a significant part of the motivation stems from its deep connection to problems in theoretical computer science such as list agreement expansion and agreement expansion in the low soundness regime. In this paper, we prove coboundary expansion with non-Abelian coefficients for the coset complex construction of <PERSON> and <PERSON>. Our proof uses a novel global argument, as opposed to the local-to-global arguments that are used to prove cosystolic expansion.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718136"}, {"primary_key": "198002", "vector": [], "sparse_vector": [], "title": "On the Hardness Hierarchy for the O(n√log n) Complexity in the Word RAM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we study the relative hardness of fundamental problems with state-of-the-art word RAM algorithms taking O(n√logn) time for instances described in Θ(n) machine words (i.e., Θ(nlogn) bits). The word RAM model nowadays serves as the default model of computation for sequential algorithms, and understanding its limitations lies at the core of theoretical computer science. The class of problems solvable in O(n√logn) time is one of the six levels of hardness listed in the seminal paper of <PERSON> and <PERSON> [SODA 2010]. According to the current state of knowledge, this class characterizes problems from several domains, including counting inversions, string processing problems (BWT Construction, LZ77 Factorization, Longest Common Substring, Batched Longest Previous Factor Queries, Batched Inverse Suffix Array Queries), and computational geometry problems (Orthogonal Range Counting, Orthogonal Segment Intersection). Our contribution is twofold: We present several new connections between the aforementioned string problems and an old Dictionary Matching problem, which asks whether a given text contains (an exact occurrence of) at least one among the given patterns. This is a classical problem with a solution based on the Aho–Corasick automaton dating back to 1975. In this work, we restrict Dictionary Matching to instances with O(n) binary patterns of length m=O(logn), short enough to be stored using O(1) machine words each, and we prove that, unless this problem can be solved faster than the current bound of O(n√logn), most fundamental string problems cannot be solved faster either. With further reductions, we extend this hierarchy beyond string problems, proving that computational tasks like counting inversions—a fundamental component in geometric algorithms—inherit this hardness. This, in turn, establishes the hardness of Orthogonal Range Counting and Orthogonal Segment Intersection. The key to extending our results to other domains is a surprising equivalent characterization of Dictionary Matching in terms of a new problem we call String Nesting, which, through a chain of three more reductions, can be solved by counting inversions. Put together, our results unveil a single hard problem, with two different but equivalent formulations, that underlies the hardness of nearly all known major problems, coming from different domains, currently occupying the O(n√logn) level of hardness. These results drastically funnel further efforts to improve the complexity of near-linear problems. Many of our reductions hold even for simpler versions of basic problems, such as determining the parity of the number of phrases in the LZ77 factorization or the number of runs in the BWT. This yields stronger results that can be used to design future reductions more easily. As an auxiliary outcome of our framework, we also prove that several central string problems in the RAM model do not get easier when limited to strings over the binary alphabet. Our reductions to the binary case simplify the currently fastest algorithms for many classical problems, including LZ77 Factorization and Longest Common Substring.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718291"}, {"primary_key": "198003", "vector": [], "sparse_vector": [], "title": "Near-Optimal Linear Sketches and Fully-Dynamic Algorithms for Hypergraph Spectral Sparsification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A hypergraph spectral sparsifier of a hypergraph G is a weighted subgraph H that approximates the Laplacian of G to a specified precision. Recent work has shown that similar to ordinary graphs, there exist O(n)-size hypergraph spectral sparsifiers. However, the task of computing such sparsifiers turns out to be much more involved, and all known algorithms rely on the notion of balanced weight assignments, whose computation inherently relies on repeated, complete access to the underlying hypergraph. We introduce a significantly simpler framework for hypergraph spectral sparsification which bypasses the need to compute such weight assignments, essentially reducing hypergraph sparsification to repeated effective resistance sampling in ordinary graphs, which are obtained by oblivious vertex-sampling of the original hypergraph. Our framework immediately yields a simple, new nearly-linear time algorithm for nearly-linear size spectral hypergraph sparsification. Furthermore, as a direct consequence of our framework, we obtain the first nearly-optimal algorithms in several other models of computation: 1. The first nearly-optimal size linear sketches for spectral hypergraph sparsification. For hypergraphs on n vertices, with hyperedges of arity ≤ r and with ≤ m hyperedges, these sketches require only O(n r (m) / 2) bits and recover a (1 ± ) spectral-hypergraph sparsifier with high probability. It is known that linear sketches require Ω(nr log(m)) bits even for the easier task of cut sparsification (Khanna-Putterman-Sudan FOCS 2024). 2. The first nearly-optimal fully dynamic (1 ± ) spectral (and cut) hypergraph sparsification algorithm. Our algorithm has an amortized, expected update time of O(r (m) / 2), and produces sparsifiers with O(n (m) / 2) hyperedges. This is nearly-optimal as even to read a single hyperedge takes time Ω(r). 3. The first nearly-optimal algorithm for online hypergraph spectral sparsification. On a sequence of m (unweighted) hyperedges, our algorithm creates a (1 ± ) hypergraph spectral sparsifier with O(n (m) / 2) hyperedges in an online manner. When m≤ (n), this improves upon the work of Soma, Tung, and Yoshida (IPCO 2024) by a factor of r, who created online sparsifiers with O(n (r + log(m)) / 2) hyperedges. We complement this result with an Ω(n log(m)) lower-bound for any online sparsifier, thus provably separating the classical and online settings. Our main conceptual and technical contributions are introduction of (a) the vertex sampling framework to reduce spectral sparsification in hypergraphs to ordinary graphs, and (b) a notion of collective energy in hypergraphs that may be seen as a continuous generalization of k-cuts.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718239"}, {"primary_key": "198004", "vector": [], "sparse_vector": [], "title": "Efficient Algorithms and New Characterizations for CSP Sparsification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Madhu <PERSON>"], "summary": "CSP sparsification, introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (ITCS 2015), considers the following question: how much can an instance of a constraint satisfaction problem be sparsified (by retaining a reweighted subset of the constraints) while still roughly capturing the weight of constraints satisfied by every assignment. CSP sparsification captures as a special case several well-studied problems including graph cut-sparsification, hypergraph cut-sparsification, hypergraph XOR-sparsification, and corresponds to a general class of hypergraph sparsification problems where an arbitrary 0/1-valued splitting function is used to define the notion of cutting a hyperedge (see, for instance, Veldt-Benson-<PERSON>berg SIAM Review 2022). The main question here is to understand, for a given constraint predicate P:Σr → {0,1} (where variables are assigned values in Σ), the smallest constant c such that O(nc) sized sparsifiers exist for every instance of a constraint satisfaction problem over P. A recent work of <PERSON><PERSON>, <PERSON><PERSON> and Sudan (SODA 2024) [KPS24] showed existence of near-linear size sparsifiers for new classes of CSPs. In this work, (1) we significantly extend the class of CSPs for which nearly linear-size sparsifications can be shown to exist while also extending the scope to settings with non-linear-sized sparsifications; (2) we give a polynomial-time algorithm to extract such sparsifications for all the problems we study including the first efficient sparsification algorithms for the problems studied in [KPS24]. Our results captured in item (1) lead to two new classifications: First we get a complete classification of all symmetric Boolean predicates P (i.e., on the Boolean domain Σ = {0,1}) that allow nearly-linear-size sparsifications. This classification reveals an inherent, and previously unsuspected, number-theoretic phenomenon that determines near-linear size sparsifiability. Second, we also completely classify the set of Boolean predicates P that allow non-trivial (o(nr)-size) sparsifications, thus answering an open question from the work of Kogan and Krauthgamer. The constructive aspect of our result is an arguably unexpected strengthening of [KPS24]. Their work roughly seemed to suggest that sparsifications can be found by solving problems related to finding the minimum distance of linear codes. These problems remain unsolved (in fact, are NP-hard) to this date and our work finds a different path to achieve poly-time sparsification, resolving an open problem from their work. As a consquence we also get the first efficient algorithms to spectrally sparsify Cayley graphs over F2n in time polynomial in the number of generators. Our techniques build on [KPS24] which proves the existence of nearly-linear size sparsifiers for CSPs where the unsatisfying assignments of the underlying predicate P are given by a linear equation over a finite field. Our main contributions are to extend this framework to higher-degree equations over general Abelian groups (both elements are crucial for our classification results) as well as designing polynomial-time sparsification algorithms for all problems in our framework.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718205"}, {"primary_key": "198005", "vector": [], "sparse_vector": [], "title": "Founding Quantum Cryptography on Quantum Advantage, or, Towards Cryptography from #P Hardness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent oracle separations [<PERSON><PERSON><PERSON><PERSON>, TQC<PERSON>21, <PERSON><PERSON><PERSON><PERSON> et. al., STOC’23] have raised the tantalizing possibility of basing quantum cryptography on sources of hardness that persist even if the polynomial hierarchy collapses. We realize this possibility by building quantum bit commitments and secure computation from unrelativized, well-studied mathematical problems that are conjectured to be #P-hard – such as approximating the permanents of complex Gaussian matrices, or approximating the output probabilities of random quantum circuits. Indeed, we show that as long as any one of the conjectures underlying sampling-based quantum advantage (e.g., BosonSampling [<PERSON><PERSON>, STOC’11], Random Circuit Sampling [Bo<PERSON><PERSON> et. al., Nature Physics 2018], IQP [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, Proc. Royal Society of London 2010]) is true, quantum cryptography can be based on the extremely mild (worst-case) complexity assumption that efficient quantum machines cannot decide P^#P. Our techniques uncover strong connections between the hardness of approximating the probabilities of outcomes of quantum processes, the existence of “one-way” state synthesis problems, and the existence of useful cryptographic primitives such as one-way puzzles and quantum bit commitments. Specifically, we prove that the following hardness assumptions are equivalent under quantum polynomial time reductions. Specifically, we prove that the following hardness assumptions are equivalent under quantum polynomial time reductions. - The hardness of approximating the probabilities of outcomes of quantumly efficiently sampleable distributions, upto inverse polynomial relative error. - The existence of one-way puzzles, where a quantum sampler outputs a pair of classical strings – a puzzle and its key – and where the hardness lies in finding any key corresponding to a random puzzle. These are known to imply quantum bit commitments [Khurana and Tomer, STOC’24]. - The existence of state puzzles, or one-way state synthesis problems, where it is hard to synthesize a secret quantum state given a public classical identifier. These capture the hardness of search problems with quantum secrets and classical challenges. These are the first constructions of quantum cryptographic primitives (one-way puzzles, quantum bit commitments, state puzzles) from mathematical assumptions that do not imply the existence of one-way functions/classical cryptography. Along the way, we also show that distributions that admit efficient quantum samplers but cannot be pseudo-deterministically efficiently sampled imply quantum commitments.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718145"}, {"primary_key": "198006", "vector": [], "sparse_vector": [], "title": "Faster <PERSON><PERSON><PERSON> Computation via a Natural Generalization of the Euclidean Algorithm.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Euclidean algorithm is one of the oldest algorithms known to mankind. Given two integral numbers a1 and a2, it computes the greatest common divisor (gcd) of a1 and a2 in a very elegant way. From a lattice perspective, it computes a basis of the lattice generated by a1 and a2 as gcd(a1,a2) ℤ = a1 ℤ + a2 ℤ. In this paper, we show that the classical Euclidean algorithm can be adapted in a very natural way to compute a basis of a lattice that is generated by vectors A1, … , An ∈ ℤd with n> rank(A1, … ,An). Similar to the Euclidean algorithm, our algorithm is easy to describe and implement and can be written within 12 lines of pseudocode. As our main result, we obtain an algorithm to compute a lattice basis for given vectors A1, … , An ∈ ℤd in time (counting bit operations) LS + Õ((n−d)d2 · log(||A||), where LS is the time required to obtain the exact fractional solution of a certain system of linear equalities. The analysis of the running time of our algorithms relies on fundamental statements on the fractionality of solutions of linear systems of equations. So far, the fastest algorithm for lattice basis computation was due to <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> (ISSAC 1996) having a running time of Õ(ndωlog||A||), where ω denotes the matrix multiplication exponent. We can improve upon their running time as our algorithm requires at most Õ(max{n−d, d2}dω(2)−1 log||A||) bit operations, where ω(2) denotes the exponent for multiplying a n× n matrix with an n× n2 matrix. For current values of ω and ω(2), our algorithm improves the running time therefore by a factor of at least d0.12 (since n>d) providing the first general runtime improvement for lattice basis computation in nearly 30 years. In the cases of either few additional vectors, e.g. n−d ∈ do(1), or a very large number of additional vectors, e.g. n−d ∈ Ω (dk) and k>1, the run time improves even further in comparison. At last, we present a postprocessing procedure which yields an improved size bound of √d ||A|| for vectors of the resulting basis matrix. The procedure only requires Õ(d3 log||A||) bit operations. By this we improve upon the running time of previous results by a factor of at least d0.74.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718138"}, {"primary_key": "198007", "vector": [], "sparse_vector": [], "title": "Approximating the Held-Karp Bound for Metric TSP in Nearly Linear Work and Polylogarithmic Depth.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sorrachai <PERSON>i"], "summary": "We present a nearly linear work parallel algorithm for approximating the Held–<PERSON> bound for Metric TSP. Given an edge-weighted undirected graph G=(V,E) on m edges and ε>0, it returns a (1+ε)-approximation to the Held–<PERSON> bound with high probability, in Õ(m/ε4) work and Õ(1/ε4) depth. While a nearly linear time sequential algorithm was known for almost a decade (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> <PERSON>17), it was not known how to simultaneously achieve nearly linear work alongside polylogarithmic depth. Using a reduction by <PERSON><PERSON><PERSON><PERSON> et al. ’22, we also give a parallel algorithm for computing a (1+ε)-approximate fractional solution to the k-edge-connected spanning subgraph (k-ECSS) problem, with similar complexity. To obtain these results, we introduce a notion of core-sequences for the parallel Multiplicative Weights Update (MWU) framework (Luby<PERSON> ’93, Young ’01). For Metric TSP and k-ECSS, core-sequences enable us to exploit the structure of approximate minimum cuts to reduce the cost per iteration and/or the number of iterations. The acceleration technique via core-sequences is generic and of independent interest. In particular, it improves the best-known iteration complexity of MWU algorithms for packing/covering LPs from poly(lognnz(A)) to polylogarithmic in the product of cardinalities of the core-sequence sets, where A is the constraint matrix of the LP. For certain implicitly defined LPs such as the k-ECSS LP, this yields an exponential improvement in depth.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718288"}, {"primary_key": "198008", "vector": [], "sparse_vector": [], "title": "Sampling and Integration of Logconcave Functions by Algorithmic Diffusion.", "authors": ["<PERSON><PERSON><PERSON>", "Santosh S<PERSON>"], "summary": "We study the complexity of sampling, rounding, and integrating arbitrary logconcave functions given an evaluation oracle. Our new approach provides the first complexity improvements in nearly two decades for general logconcave functions for all three problems, and matches the best-known complexities for the special case of uniform distributions on convex bodies. For the sampling problem, our output guarantees are significantly stronger than previously known, and lead to a streamlined analysis of statistical estimation based on dependent random samples.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718202"}, {"primary_key": "198009", "vector": [], "sparse_vector": [], "title": "High Rate Multivariate Polynomial Evaluation Codes.", "authors": ["Swas<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The classical Reed-Muller codes over a finite field Fq are based on evaluations of m-variate polynomials of degree at most d over a product set Um, for some d 0. In fact, we give two quite different constructions, and for both we develop efficient decoding algorithms for these codes that can decode from half the minimum distance. The first of these codes is based on evaluating multivariate polynomials on simplex-like sets. The distance of this code is proved via a generalized Schwartz-<PERSON> lemma on the probability of non-zeroness when evaluating polynomials on sparser subsets of Um – the final bound only depends on the “shape” of the set, and recovers the <PERSON><PERSON><PERSON><PERSON><PERSON> bound for the case of the full Um, while still being Ω(1) for much sparser simplex-like subsets of Um. The second of these codes is more algebraic and, surprisingly (to us), has some strong locality properties. It is based on evaluating multivariate polynomials at the intersection points of hyperplanes in general position. It turns out that these evaluation points have many large subsets of collinear points. These subsets form the basis of a simple local characterization, and using some deeper algebraic tools generalizing ideas from <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and Ben-<PERSON>-<PERSON>, we show that this gives a local test for these codes. Interestingly, the set of evaluation points for these locally testable multivariate polynomial evaluation codes can be as small as O(dm), and need not occupy a constant or even noticeable fraction of the full space Fqm.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718106"}, {"primary_key": "198010", "vector": [], "sparse_vector": [], "title": "Linear-Time Algorithms for k-Edge-Connected Components, k-Lean Tree Decompositions, and More.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present kO(k2) m time algorithms for various problems about decomposing a given undirected graph by edge cuts or vertex separators of size <k into parts that are “well-connected” with respect to cuts or separators of size <k; here, m is the total number of vertices and edges of the graph. As an application of our results, we obtain for every fixed k a linear-time algorithm for computing the k-edge-connected components of a given graph, solving a long-standing open problem. More generally, we obtain a kO(k2) m time algorithm for computing a k-Gomory-Hu tree of a given graph, which is a structure representing pairwise minimum cuts of size <k. Our main technical result, from which the other results follow, is a kO(k2) m time algorithm for computing a k-lean tree decomposition of a given graph. This is a tree decomposition with adhesion size <k that captures the existence of separators of size <k between subsets of its bags. A k-lean tree decomposition is also an unbreakable tree decomposition with optimal unbreakability parameters for the adhesion size bound k. As further applications, we obtain kO(k2) m time algorithms for k-vertex connectivity and for element connectivity k-Gomory-Hu tree. All of our algorithms are deterministic. Our techniques are inspired by the tenth paper of the Graph Minors series of <PERSON> and <PERSON> and by <PERSON><PERSON><PERSON><PERSON>’s parameterized linear-time algorithm for treewidth.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718123"}, {"primary_key": "198011", "vector": [], "sparse_vector": [], "title": "Quantum-Computable One-Way Functions without One-Way Functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We construct a classical oracle relative to which P = NP but quantum-computable quantum-secure trapdoor one-way functions exist. This is a substantial strengthening of the result of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (STOC 2023), which only achieved single-copy pseudorandom quantum states relative to an oracle that collapses NP to P. For example, our result implies multi-copy pseudorandom states and pseudorandom unitaries, but also classical-communication public-key encryption, signatures, and oblivious transfer schemes relative to an oracle on which P=NP. Hence, in our new relativized world, classical computers live in ”Algorithmica” whereas quantum computers live in ”Cryptomania,” using the language of Impagliazzo’s worlds. Our proof relies on a new distributional block-insensitivity lemma for AC0 circuits, wherein a single block is resampled from an arbitrary distribution.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718144"}, {"primary_key": "198012", "vector": [], "sparse_vector": [], "title": "A Bound on the Quantum Value of All Compiled Nonlocal Games.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A cryptographic compiler introduced by <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON> (STOC’23) converts any nonlocal game into an interactive protocol with a single computationally bounded prover. Although the compiler is known to be sound in the case of classical provers and complete in the quantum case, quantum soundness has so far only been established for special classes of games. In this work, we establish a quantum soundness result for all compiled two-player nonlocal games. In particular, we prove that the quantum commuting operator value of the underlying nonlocal game is an upper bound on the quantum value of the compiled game, and we also provide a corresponding self-testing result. Our results employ techniques from operator algebras in a computational and cryptographic setting to establish information-theoretic objects in the asymptotic limit of the security parameter. They further rely on a sequential characterization of quantum commuting operator correlations, which may be of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718237"}, {"primary_key": "198013", "vector": [], "sparse_vector": [], "title": "Statistical Inference of a Ranked Community in a Directed Graph.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of detecting or recovering a planted ranked subgraph from a directed graph, an analog for directed graphs of the well-studied planted dense subgraph model. We suppose that, among a set of n items, there is a subset S of k items having a latent ranking in the form of a permutation π of S, and that we observe a fraction p of pairwise orderings between elements of S which agree with π with probability 1/2 + q and otherwise are uniformly random. Unlike in the planted dense subgraph and planted clique problems where the community S is distinguished by its unusual density of edges, here the community is only distinguished by the unusual consistency of its pairwise orderings. We establish computational and statistical thresholds for both detecting and recovering such a ranked community. In the log-density setting where k, p, and q all scale as powers of n, we establish the exact thresholds in the associated exponents at which detection and recovery become statistically and computationally feasible. These regimes include a rich variety of behaviors, exhibiting both statistical-computational and detection-recovery gaps. We also give finer-grained results for two extreme cases: (1) p = 1, k = n, and q small, where a full tournament is observed that is weakly correlated with a global ranking, and (2) p = 1, q = 1/2, and k small, where a small “ordered clique” (totally ordered directed subgraph) is planted in a random tournament.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718280"}, {"primary_key": "198014", "vector": [], "sparse_vector": [], "title": "Dynamic Locality Sensitive Orderings in Doubling Metrics.", "authors": ["An La", "<PERSON>"], "summary": "In their pioneering work, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON> (SICOMP 2020) introduced locality-sensitive ordering (LSO), and constructed an LSO with a constant number of orderings for point sets in the d-dimensional Euclidean space. Furthermore, their LSO could be made dynamic effortlessly under point insertions and deletions, taking O(log(n)) time per update by exploiting Euclidean geometry. Their LSO provides a powerful primitive to solve a host of geometric problems in Euclidean spaces in both dynamic and static settings. <PERSON><PERSON><PERSON> and <PERSON> (STOC 2022) constructed the first LSO with a constant number of orderings in the more general setting of doubling metrics. However, their algorithm is inherently static since it relies on several sophisticated constructions in intermediate steps, none of which is known to have a dynamic version. Making their LSO dynamic would recover the full generality of LSO and provide a general tool to dynamize a vast number of static constructions in doubling metrics. In this work, we give a dynamic algorithm that has O(logn) time per update to construct an LSO in doubling metrics under point insertions and deletions. We introduce a toolkit of several new data structures: a pairwise tree cover, a net tree cover, and a leaf tracker. A key technical is stabilizing the dynamic net tree of <PERSON> and <PERSON><PERSON><PERSON><PERSON> (STOC 2006), a central dynamic data structure in doubling metrics. Specifically, we show that every update to the dynamic net tree can be decomposed into a few simple updates to trees in the net tree cover. As stability is the key to any dynamic algorithm, our technique could be useful for other problems in doubling metrics. We obtain several algorithmic applications from our dynamic LSO. The most notably is the first dynamic algorithm for maintaining an k-fault tolerant spanner in doubling metrics with optimal sparsity in optimal O(logn) time per update.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718209"}, {"primary_key": "198015", "vector": [], "sparse_vector": [], "title": "Learning Quantum States Prepared by Shallow Circuits in Polynomial Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a polynomial time algorithm that, given copies of an unknown quantum state ψ=U0n that is prepared by an unknown constant depth circuit U on a finite-dimensional lattice, learns a constant depth quantum circuit that prepares ψ. The algorithm extends to the case when the depth of U is polylog(n), with a quasi-polynomial run-time. The key new idea is a simple and general procedure that efficiently reconstructs the global state ψ from its local reduced density matrices. As an application, we give an efficient algorithm to test whether an unknown quantum state on a lattice has low or high quantum circuit complexity.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718311"}, {"primary_key": "198016", "vector": [], "sparse_vector": [], "title": "Asymptotically Optimal Hardness for k-Set Packing and k-Matroid Intersection.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON>", "Theophile <PERSON>"], "summary": "For any ε > 0, we prove that k-Dimensional Matching is hard to approximate within a factor of k/(12 + ε) for large k unless NP ⊆ BPP. Listed in <PERSON><PERSON>’s 21 NP-complete problems, k-Dimensional Matching is a benchmark computational complexity problem which we find as a special case of many constrained optimization problems over independence systems including: k-Set Packing, k-Matroid Intersection, and Matroid k-Parity. For all the aforementioned problems, the best-known lower bound was a Ω(k /log(k))-hardness by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. In contrast, state-of-the-art algorithms achieve an approximation of O(k). Our result narrows down this gap to a constant and thus provides a rationale for the observed algorithmic difficulties. The crux of our result hinges on a novel approximation preserving gadget from R-degree bounded k-CSPs over alphabet size R to kR-Dimensional Matching. Along the way, we prove that R-degree bounded k-CSPs over alphabet size R are hard to approximate within a factor Ωk(R) using known randomised sparsification methods for CSPs.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718142"}, {"primary_key": "198017", "vector": [], "sparse_vector": [], "title": "The 2-Token Theorem: Recognising History-Deterministic Parity Automata Efficiently.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "History-determinism is a restricted notion of nondeterminism in automata, where the nondeterminism can be successfully resolved based solely on the prefix read so far. History-deterministic automata still allow for exponential succinctness in automata over infinite words compared to deterministic automata (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 2015), allow for canonical forms unlike deterministic automata (<PERSON> and <PERSON><PERSON>, 2019 and 2020; <PERSON><PERSON><PERSON> and <PERSON>, 2022), and retain some of the algorithmic properties of deterministic automata, for example for reactive synthesis (<PERSON><PERSON><PERSON> and <PERSON>, 2006; <PERSON><PERSON><PERSON> and <PERSON>, 2024). Despite the topic of history-determinism having received a lot of attention over the last decade, the complexity of deciding whether a parity automaton is history-deterministic has, up till now, remained open. We show that history-determinism for a parity automaton with a fixed parity index can be checked in PTIME, thus improving upon the naive EXPTIME upper bound of <PERSON><PERSON><PERSON> and <PERSON><PERSON> that has stood since 2006. More precisely, we show that the so-called 2-token game, which can be solved in PTIME for parity automata with a fixed parity index, characterises history-determinism for parity automata. This game was introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in 2018, who showed that to decide if a Büchi automaton is history-determinism, it suffices to find the winner of the 2-token game on it. They conjectured that this 2-token game based characterisation extends to parity automata. <PERSON><PERSON>, <PERSON>perberg, Lehtinen, and Skrzypcak showed in 2020 that this conjecture holds for coBüchi automata as well. We prove Bagnol and Kuperberg’s conjecture that the winner of the 2-token game characterises history-determinism on parity automata. As consequences of our result, we also get the 2-token game based characterisation for history-determinism in alternating parity automata and ω-regular automata, thus yielding efficient algorithms for deciding their history-determinism. Additionally, we also obtain an efficient algorithm for deciding the good-enough-synthesis (Almagor and Kupferman, 2020) for specifications given by a deterministic parity automata.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718310"}, {"primary_key": "198018", "vector": [], "sparse_vector": [], "title": "Discrepancy Algorithms for the Binary Perceptron.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The binary perceptron problem asks us to find a sign vector in the intersection of independently chosen random halfspaces with intercept −κ. We analyze the performance of the canonical discrepancy minimization algorithms of <PERSON><PERSON><PERSON> and <PERSON>vos<PERSON>/El<PERSON> for the asymmetric binary perceptron problem. We obtain new algorithmic results in the κ = 0 case and in the large-|κ| case. In the κ → −∞ case, we additionally characterize the storage capacity and complement our algorithmic results with an almost-matching overlap-gap lower bound.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718124"}, {"primary_key": "198019", "vector": [], "sparse_vector": [], "title": "Privately Evaluating Untrusted Black-Box Functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We provide tools for sharing sensitive data in situations when the data curator does not know in advance what questions an (untrusted) analyst might want to ask about the data. The analyst can specify a program that they want the curator to run on the dataset. We model the program as a black-box function f. We study differentially private algorithms, called privacy wrappers, that, given black-box access to a real-valued function f and a sensitive dataset x, output an accurate approximation to f(x). The dataset x is modeled as a finite subset of a possibly infinite set U, in which each entry x represents data of one individual. A privacy wrapper calls f on the dataset x and on some subsets of x and returns either an approximation to f(x) or a nonresponse symbol ⊥. The wrapper may also use additional information (that is, parameters) provided by the analyst, but differential privacy is required for all values of these parameters. Correct setting of these parameters will ensure better accuracy of the privacy wrapper. The bottleneck in the running time of our privacy wrappers is the number of calls to f, which we refer to as queries. Our goal is to design privacy wrappers with high accuracy and small query complexity. We introduce a novel setting, called the automated sensitivity detection setting, where the analyst supplies only the black-box function f and the intended (finite) range of f. In contrast, in the previously considered setting, which we refer to as the claimed sensitivity bound setting, the analyst also supplies additional parameters that describe the sensitivity of f. We design privacy wrappers for both settings and show that our wrappers are nearly optimal in terms of accuracy, locality (i.e., the depth of the local neighborhood of the dataset x they explore), and query complexity. In the claimed sensitivity bound setting, we provide the first accuracy guarantees that have no dependence on the size of the universe U. We also re-interpret and analyze previous constructions in our framework, and use them as comparison points. In addition to addressing the black-box privacy problem, our private mechanisms provide feasibility results for differentially private release of general classes of functions.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718247"}, {"primary_key": "198020", "vector": [], "sparse_vector": [], "title": "Model Stealing for Any Low-Rank Language Model.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Model stealing, where a learner tries to recover an unknown model via carefully chosen queries, is a critical problem in machine learning, as it threatens the security of proprietary models and the privacy of data they are trained on. In recent years, there has been particular interest in stealing large language models (LLMs). In this paper, we aim to build a theoretical understanding of stealing language models by studying a simple and mathematically tractable setting. We study model stealing for Hidden Markov Models (HMMs), and more generally low-rank language models. We assume that the learner works in the conditional query model, introduced by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>. Our main result is an efficient algorithm in the conditional query model, for learning any low-rank distribution. In other words, our algorithm succeeds at stealing any language model whose output distribution is low-rank. This improves upon the previous result which also requires the unknown distribution to have high “fidelity” – a property that holds only in restricted cases. There are two key insights behind our algorithm: First, we represent the conditional distributions at each timestep by constructing barycentric spanners among a collection of vectors of exponentially large dimension. Second, for sampling from our representation, we iteratively solve a sequence of convex optimization problems that involve projection in relative entropy to prevent compounding of errors over the length of the sequence. This is an interesting example where, at least theoretically, allowing a machine learning model to solve more complex problems at inference time can lead to drastic improvements in its performance.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718220"}, {"primary_key": "198021", "vector": [], "sparse_vector": [], "title": "QMA vs QCMA and Pseudorandomness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study a longstanding question of <PERSON><PERSON> and <PERSON><PERSON><PERSON> on whether there exists a classical oracle separating QMA from QCMA. Settling this question in either direction would yield insight into the power of quantum proofs over classical proofs. We show that such an oracle exists if a certain quantum pseudorandomness conjecture holds. Roughly speaking, the conjecture posits that quantum algorithms cannot, by making few queries, distinguish between the uniform distribution over permutations versus permutations drawn from so-called “dense” distributions. Our result can be viewed as establishing a “win-win” scenario: either there is a classical oracle separation of QMA from QCMA, or there is quantum advantage in distinguishing pseudorandom distributions on permutations.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718296"}, {"primary_key": "198022", "vector": [], "sparse_vector": [], "title": "Disjoint Paths Problem with Group-Expressable Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study an extension of the k-Disjoint Paths Problem where, in addition to finding k disjoint paths joining k given pairs of vertices in a graph, we ask that those paths satisfy certain constraints expressable by abelian groups. We give an O(n8) time algorithm to solve this problem under the assumption that the constraint can be expressed as avoiding a bounded number of group elements; moreover, our O(n8) algorithm allows any bounded number of such constraints to be combined. Examples of group-expressable constraints include: (1) paths of length ℓ modulo m for any fixed integers ℓ and m with m ≥ 2, (2) paths passing through a bounded number of prescribed sets of edges and/or vertices, and (3) paths that are long detours (si-ti-paths with length at least (si,ti)+ℓ for any fixed integer ℓ). The k=1 case of the problem with modularity constraints solves a problem in [<PERSON><PERSON>, <PERSON>, and <PERSON>, J. <PERSON>, (1991)] that has remained open for over 30 years. Our work also implies a polynomial time algorithm for testing the existence of a subgraph isomorphic to a subdivision of a fixed graph, where each path of the subdivision between branch vertices satisfies any combination of a bounded number of group-expressable constraints. This in particular gives a unified polynomial time algorithm for testing the existence of k disjoint cycles with such constraints. For example, we can test in polynomial time the existence of k disjoint cycles in surface-embedded graphs such that each cycle is non-homologous to 0 and is at least ℓ longer than the minimum length of such a cycle. In addition, our work implies similar results addressing edge-disjointness.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718109"}, {"primary_key": "198023", "vector": [], "sparse_vector": [], "title": "Efficiently Finding and Counting Patterns with Distance Constraints in Sparse Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph classes of bounded expansion were introduced by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> as a general model of structurally sparse graphs, which have received considerable attention from both combinatorial and algorithmic perspectives. A celebrated result of <PERSON><PERSON> et al. [JACM’13] showed that any first-order model checking problem on bounded-expansion graph classes is fixed-parameter tractable. A main drawback of the FPT algorithms resulted from this result is the high dependency of their time complexity on the parameter k: the algorithms run in time at least doubly exponential in k, even when the graph class is of polynomial expansion. It is natural to ask whether there exist FPT algorithms for these problem that run in singly exponential time, i.e., 2kO(1) nO(1) time. In this paper, we give a new algorithmic framework for a broad family of first-order model checking problems on sparse graphs, which results in algorithms with running time 2kO(1) · n when the graph class is of exponential expansion (i.e., the expansion is bounded by a singly exponential function). This covers most well-studied instances of bounded-expansion graph classes, in particular, all polynomial-expansion graph classes. Our framework applies to all problems that can be formulated as finding k vertices in a host graph G with certain distance constraints. Furthermore, the framework can be generalized to give (1 ± ε)-approximation algorithms for the counting versions of these problems with running time 2kO(1) · n (logn/ε)O(1) on exponential-expansion graph classes. In terms of techniques, our framework differs entirely from the one of <PERSON><PERSON> et al. based on centered coloring. We develop various technical components based on the theory of sparse graphs and other tools such as representative sets/functions, tree decomposition, inclusion-exclusion, etc., which are of independent interest. Remarkably, some of our techniques can be applied to even more general graph classes, such as degenerate graph classes. Therefore, as a byproduct, we obtain a (1 ± ε)-approximation algorithm for approximately counting bounded-treewidth induced subgraphs in degenerate graphs with running time kO(k) · (n/ε)O(1). This resolves (in a much stronger form) an open problem of Bressan and Roth [FOCS’22], which asked whether such an algorithm exists for counting induced k-matching in degenerate graphs.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718251"}, {"primary_key": "198024", "vector": [], "sparse_vector": [], "title": "Subexponential Parameterized Algorithms for Hitting Subgraphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For a finite set F of graphs, the F-Hitting problem aims to compute, for a given graph G (taken from some graph class G) of n vertices (and m edges) and a parameter k ∈ ℕ, a set S of vertices in G such that |S| ≤ k and G−S does not contain any subgraph isomorphic to a graph in F. As a generic problem, F-Hitting subsumes many fundamental vertex-deletion problems that are well-studied in the literature. The F-Hitting problem admits a simple branching algorithm with running time 2O(k) · nO(1), while it cannot be solved in 2o(k) · nO(1) time on general graphs assuming the ETH, follows from the seminal work of <PERSON> and <PERSON>. In this paper, we establish a general framework to design subexponential parameterized algorithms for the F-Hitting problem on a broad family of graph classes. Specifically, our framework yields algorithms that solve F-Hitting with running time 2O(kc) · n + O(m) for a constant c < 1 on any graph class G that admits balanced separators whose size is (strongly) sublinear in the number of vertices and polynomial in the size of a maximum clique. Examples include all graph classes of polynomial expansion (e.g., planar graphs, bounded-genus graphs, minor-free graphs, etc.) and many important classes of geometric intersection graphs (e.g., map graphs, intersection graphs of any fat geometric objects, pseudo-disks, etc.). Our algorithms also apply to the weighted version of F-Hitting, where each vertex of G has a weight and the goal is to compute the set S with a minimum weight that satisfies the desired conditions. The core of our framework, which is our main technical contribution, is an intricate subexponential branching algorithm that reduces an instance of F-Hitting (on the aforementioned graph classes) to 2O(kc) general hitting-set instances, where the Gaifman graph of each instance has treewidth O(kc), for some constant c < 1 depending on F and the graph class.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718192"}, {"primary_key": "198025", "vector": [], "sparse_vector": [], "title": "Fingerprinting Codes Meet Geometry: Improved Lower Bounds for Private Query Release and Adaptive Data Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fingerprinting codes are a crucial tool for proving lower bounds in differential privacy. They have been used to prove tight lower bounds for several fundamental questions, especially in the “low accuracy” regime. Unlike reconstruction/discrepancy approaches however, they are more suited for query sets that arise naturally from the fingerprinting codes construction. In this work, we propose a general framework for proving fingerprinting type lower bounds, that allows us to tailor the technique to the geometry of the query set. Our approach allows us to prove several new results, including the following. We show that any (sample- and population-)accurate algorithm for answering Q arbitrary adaptive counting queries over a universe X to accuracy α needs Ω(√log|X|· logQ/α3) samples, matching known upper bounds. This shows that the approaches based on differential privacy are optimal for this question, and improves significantly on the previously known lower bounds of logQ/α2 and min(√Q, √log|X|)/α2. We show that any (,δ)-DP algorithm for answering Q counting queries to accuracy α needs Ω(√ log|X| log(1/δ) logQ/ α2) samples, matching known upper bounds up to constants. Our framework allows for proving this bound via a direct correlation analysis and improves the prior bound of [] by √log(1/δ). For privately releasing a set of random 0-1 queries, we show tight sample complexity lower bounds in the high accuracy regime. In the low accuracy regime, the picture is more complex. For random queries, we show that there is a discontinuity in the sample complexity. For Q random queries over a universe , the sample complexity grows as Θ, δ(1/α2), with no dependence on Q or |X|. This new sample complexity bound, based on sparse histograms, is asymptotically better than known lower bounds for CDP. However, at α ≈ √log|X|/√Q, the sample complexity jumps to Θ,δ(√Q/α).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718295"}, {"primary_key": "198026", "vector": [], "sparse_vector": [], "title": "How to Construct Random Unitaries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The existence of pseudorandom unitaries (PRUs)—efficient quantum circuits that are computationally indistinguishable from Haar-random unitaries—has been a central open question, with significant implications for cryptography, complexity theory, and fundamental physics. In this work, we close this question by proving that PRUs exist, assuming that any quantum-secure one-way function exists. We establish this result for both (1) the standard notion of PRUs, which are secure against any efficient adversary that makes queries to the unitary U, and (2) a stronger notion of PRUs, which are secure even against adversaries that can query both the unitary U and its inverse U†. In the process, we prove that any algorithm making queries to a Haar-random unitary can be efficiently simulated on a quantum computer, up to inverse-exponential trace distance.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718254"}, {"primary_key": "198027", "vector": [], "sparse_vector": [], "title": "Refuting the Direct Sum Conjecture for Total Functions in Deterministic Communication Complexity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In communication complexity the input of a function f:X×Y→Z is distributed between two players <PERSON> and <PERSON>. If <PERSON> knows only x∈X and <PERSON> only y∈Y, how much information must <PERSON> and <PERSON> share to be able to elicit the value of f(x,y)? Do we need ℓ more resources to solve ℓ instances of a problem? This question is the direct sum question and has been studied in many computational models. In this paper we focus on the case of 2-party deterministic communication complexity and give a counterexample to the direct sum conjecture in its strongest form. To do so we exhibit a family of functions for which the complexity of solving ℓ instances is less than (1−ϵ)ℓ times the complexity of solving one instance for some small enough ϵ>0. We use a customised method in the analysis of our family of total functions, showing that one can force the alternation of rounds between players. This idea allows us to exploit the integrality of the complexity measure to create an increasing gap between the complexity of solving the instances independently with that of solving them together.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718148"}, {"primary_key": "198028", "vector": [], "sparse_vector": [], "title": "Permutation Superposition Oracles for Quantum Query Lower Bounds.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a generalization of <PERSON><PERSON><PERSON>’s compressed oracle method to random permutations, where an algorithm can query both the permutation and its inverse. We show how to use the resulting oracle simulation to bound the success probability of an algorithm for any predicate on input-output pairs, a key feature of <PERSON><PERSON><PERSON>’s technique that had hitherto resisted attempts at generalization to random permutations. One key technical ingredient is to use the strictly monotone factorization of a permutation, which also underlies the well-known Fisher-Yates shuffle, to represent it in the oracle’s database. As an application of our framework, we show that the one-round sponge construction is unconditionally preimage resistant in the random permutation model, for all parameter choices. This proves a conjecture by <PERSON><PERSON><PERSON>.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718266"}, {"primary_key": "198029", "vector": [], "sparse_vector": [], "title": "Improved Bounds for Testing Low Stabilizer Complexity States.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Stabilizer states are fundamental families of quantum states with crucial applications such as error correction, quantum computation, and simulation of quantum circuits. In this paper, we study the problem of testing how close or far a quantum state is to a stabilizer state. We make two contributions: First, we improve the state-of-the-art parameters for the tolerant testing of stabilizer states. In particular, we show that there is an efficient quantum primitive to distinguish if the maximum fidelity of a quantum state with a stabilizer state is ≥ є1 or ≤ є2, given one of them is the case, provided that є2 ≤ є1O(1). This result improves the parameters in the previous work which assumed є2 ≤ e− 1/є1O(1). Our proof technique extends the toolsets developed in before by applying a random Clifford map which balances the characteristic function of a quantum state, enabling the use of standard proof techniques from higher-order Fourier analysis for Boolean functions, where improved testing bounds are available. Second, we study the problem of testing low stabilizer rank states. We show that if for an infinite family of quantum states stabilizer rank is lower than a constant independent of system size, then stabilizer fidelity is lower bounded by an absolute constant. Using a result on estimating stabilizer fidelity of quantum states, one of the implications of this result is that low approximate stabilizer rank states are not pseudo-random.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718228"}, {"primary_key": "198030", "vector": [], "sparse_vector": [], "title": "The FPᴺᴾ versus #P Dichotomy for #EO.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The complexity classification of the Holant problem has remained unresolved for the past fifteen years. Counting complex-weighted Eulerian orientations problems, denoted as #EO, is regarded as one of the most significant challenges to the comprehensive complexity classification of the Holant problem. This article presents an FPNP vs. #P dichotomy for #EO, demonstrating that #EO defined by a signature set is either #P-hard or polynomial-time computable with a specific NP oracle. This result provides a complete complexity classification for #EO, and potentially leads to a dichotomy for the Holant problem. Furthermore, we derive three additional dichotomies related to the Holant problem from that for #EO.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718135"}, {"primary_key": "198031", "vector": [], "sparse_vector": [], "title": "Cryptographic Characterization of Quantum Advantage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Quantum computational advantage refers to an existence of computational tasks that are easy for quantum computing but hard classically. Unconditionally showing quantum advantage is beyond our current understanding of complexity theory, and therefore some computational assumptions are needed. Which complexity assumption is necessary and sufficient for quantum advantage? In this paper, we show that inefficient-verifier proofs of quantumness (IV-PoQ) exist if and only if classically-secure one-way puzzles (OWPuzzs) exist. As far as we know, this is the first time that a complete cryptographic characterization of quantum advantage is obtained. IV-PoQ are a generalization of proofs of quantumness (PoQ) where the verifier is efficient during the interaction but may use unbounded time afterward. IV-PoQ capture various types of quantum advantage previously studied, such as sampling and search based quantum advantage. Previous work [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, Crypto 2024] showed that IV-PoQ can be constructed from OWFs, but a construction of IV-PoQ from weaker assumptions was left open. Our result solves the open problem, because OWPuzzs are believed to be weaker than OWFs. OWPuzzs are one of the most fundamental quantum cryptographic primitives implied by many quantum cryptographic primitives weaker than one-way functions (OWFs), such as pseudorandom unitaries (PRUs), pseudorandom state generators (PRSGs), and one-way state generators (OWSGs). The equivalence between IV-PoQ and classically-secure OWPuzzs therefore highlights that if there is no quantum advantage, then these fundamental cryptographic primitives do not exist. The equivalence also means that quantum advantage is an example of the applications of OWPuzzs. Except for commitments, no application of OWPuzzs was known before. Our result shows that quantum advantage is another application of OWPuzzs, which solves the open question of [Chung, Goldin, and Gray, Crypto 2024]. Moreover, it is the first quantum-computation-classical-communication (QCCC) application of OWPuzzs. To show the main result, we introduce several new concepts and show some results that will be of independent interest. In particular, we introduce an interactive (and average-case) version of sampling problems where the task is to sample the transcript obtained by a classical interaction between two quantum polynomial-time algorithms. We show that quantum advantage in interactive sampling problems is equivalent to the existence of IV-PoQ, which is considered as an interactive (and average-case) version of Aaronson’s result [Aaronson, TCS 2014], SampBQP≠SampBPP⇔ FBQP≠FBPP. Finally, we also introduce zero-knowledge IV-PoQ and study sufficient and necessary conditions for their existence.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718133"}, {"primary_key": "198032", "vector": [], "sparse_vector": [], "title": "Weak Recovery, Hypothesis Testing, and Mutual Information in Stochastic Block Models and Planted Factor Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Young<PERSON><PERSON>"], "summary": "The stochastic block model is a canonical model of communities in random graphs. It was introduced in the social sciences and statistics as a model of communities, and in theoretical computer science as an average case model for graph partitioning problems under the name of the “planted partition model.” Given a sparse stochastic block model, the two standard inference tasks are: (i) Weak recovery: can we estimate the communities with non-trivial overlap with the true communities? (ii) Detection/Hypothesis testing: can we distinguish if the sample was drawn from the block model or from a random graph with no community structure with probability tending to 1 as the graph size tends to infinity? In this work, we show that for sparse stochastic block models, the two inference tasks are equivalent except at a critical point. That is, weak recovery is information theoretically possible if and only if detection is possible. We thus find a strong connection between these two notions of inference for the model. We further prove that when detection is impossible, an explicit hypothesis test based on low-degree polynomials in the adjacency matrix of the observed graph achieves the optimal statistical power. This low-degree test is efficient as opposed to the likelihood ratio test, which is not known to be efficient. Moreover, we prove that the asymptotic mutual information between the observed network and the community structure exhibits a phase transition at the weak recovery threshold. Our results are proven in much broader settings including the hypergraph stochastic block models and general planted factor graphs. In these settings, we prove that the impossibility of weak recovery implies contiguity and provide a condition that guarantees the equivalence of weak recovery and detection.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718292"}, {"primary_key": "198033", "vector": [], "sparse_vector": [], "title": "Good Binary Quantum Codes with Transversal CCZ Gate.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We give an asymptotically good family of quantum CSS codes on qubits with a transversal CCZ gate, meaning that the parallel logical CCZ on all logical qubits is performed by parallel physical CCZs on (a subset of) physical qubits. The construction is based on the observation that any classical code satisfying a multiplication property can be used to construct a quantum CSS code with transversal (qudit) CCZ. To obtain a constant-rate and linear-distance family, we then instantiate this construction with a classical good family of algebraic-geometry codes on a non-binary, but constant-sized, alphabet. Finally, we use a technique from the arithmetic secret sharing literature to reduce the alphabet to binary. As a corollary, the constructed code family provides a magic state distillation scheme with constant space overhead.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718186"}, {"primary_key": "198034", "vector": [], "sparse_vector": [], "title": "Quantum Fault Tolerance with Constant-Space and Logarithmic-Time Overheads.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In a model of fault-tolerant quantum computation with quick and noiseless polyloglog-time auxiliary classical computation, we construct a quantum fault tolerance protocol with constant-space and O(logN)-time overheads, where the notation O(·) hides sub-polylogarithmic factors. This significantly improves over the previous state-of-the-art protocol of Yamasaki and Koashi that achieved constant-space and quasi-polylogarithmic-time overhead. Our construction is obtained by using constant-rate quantum locally testable codes (qLTC) of appropriately chosen block size and developing new fault-tolerant gadgets on qLTCs and qLDPC codes. In particular, we obtain the following new technical results: (1) We develop a magic state distillation protocol with (log1/ε)γ spacetime overhead, where γ → 0 as ε → 0. This result differs from recent works in that we use a simple and self-contained construction using Reed-Solomon codes to obtain low spacetime overhead (rather than just space overhead as in recent works). We show a similar result for stabilizer state distillation. (2) We prove that quantum codes based on the cubical complex construction admit sequential and parallel single-shot decoders against adversarial errors of weight scaling with the code distance. In particular, our proof applies to a recent family of almost-good qLTCs of Dinur-Lin-Vidick and the good qLDPC codes of <PERSON>ur-Hsieh-Lin-Vidick. (3) Using the introduced distillation schemes, we develop fault-tolerant logical state preparation procedures with O(1)-spacetime overhead on qLTCs. Here, the qLTC property is used to quickly test if a state is too far from the codespace before proceeding. (4) We introduce the use of multiple resource states (from a small-sized set) to obtain addressable qLDPC logic that can be easily prepared using our state preparation schemes and transversal qLDPC gates. We obtain the main result by combining the above results with carefully chosen parameters. In doing so, we introduce a new weight enumerator formalism to prove fault tolerance in a composable way, which is of independent interest. To our knowledge, this gives the lowest spacetime overhead to date in the considered model of quantum fault tolerance, which, for the first time, matches that of classical fault tolerance up to sub-polylogarithmic factors.We conjecture this is optimal up to sub-polylogarithmic factors.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718318"}, {"primary_key": "198035", "vector": [], "sparse_vector": [], "title": "Faster Weighted and Unweighted Tree Edit Distance and APSP Equivalence.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON> Xu", "<PERSON>"], "summary": "The tree edit distance (TED) between two rooted ordered trees with n nodes labeled from an alphabet Σ is the minimum cost of transforming one tree into the other by a sequence of valid operations consisting of insertions, deletions and relabeling of nodes. The tree edit distance is a well-known generalization of string edit distance and has been studied since the 1970s. Its running time has seen steady improvements starting with an O(n6) algorithm [<PERSON>, <PERSON> 1979], improved to O(n4) [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON> 1989] and to O(n3logn) [<PERSON>, <PERSON> 1998], and culminating in an O(n3) algorithm [<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, ACM TALG 2010]. The latter is known to be optimal for any dynamic programming based algorithm that falls under a certain decomposition framework that captures all known sub-n4 time algorithms. Fine-grained complexity casts further light onto this hardness showing that a truly subcubic time algorithm for TED implies a truly subcubic time algorithm for All-Pairs Shortest Paths (APSP) [<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, ACM TALG 2020]. Therefore, under the popular APSP hypothesis, a truly subcubic time algorithm for TED cannot exist. However, unlike many problems in fine-grained complexity for which conditional hardness based on APSP also comes with equivalence to APSP, whether TED can be reduced to APSP has remained unknown. In this paper, we resolve this. Not only we show that TED is fine-grained equivalent to APSP, our reduction is tight enough, so that combined with the fastest APSP algorithm to-date [<PERSON>, <PERSON>ICOMP 2018] it gives the first ever subcubic time algorithm for TED running in n3/2Ω(√logn) time. We also consider the unweighted tree edit distance problem in which the cost of each edit (insertion, deletion, and relabeling) is one. For unweighted TED, a truly subcubic algorithm is known due to Mao [Mao, FOCS 2022], and later improved slightly by Dürr [Dürr, IPL 2023] to run in O(n2.9148) time. Since their algorithm uses bounded monotone min-plus product as a crucial subroutine, and the best running time for this product is Õ(n3+ω/2)≤ O(n2.6857) (where ω is the exponent of fast matrix multiplication), the much higher running time of unweighted TED remained unsatisfactory. In this work, we close this gap and give an algorithm for unweighted TED that runs in Õ(n3+ω/2) time.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718116"}, {"primary_key": "198036", "vector": [], "sparse_vector": [], "title": "Vanishing of <PERSON> Coefficients.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Schubert coefficients are nonnegative integers that arise in Algebraic Geometry and play a central role in Algebraic Combinatorics. Computing them is both difficult and mysterious. It is known that they are in GapP, but little else is known except in special cases. Notably, it is a major open problem to show that they are in # P in full generality. We study the hardness of vanishing of Schubert coefficients, i.e. whether they are equal to zero. Until this work it was open whether the vanishing is in PH. In fact, it was believed to be not in PH. We prove that the vanishing problem is in coAM assuming the GRH (the Generalized Riemann Hypothesis). Our approach is based on a reduction to HNP (Parametric Hilbert’s Nullstellensatz) recently introduced by <PERSON><PERSON> et al. We then use a completely different approach to show that the non-vanishing of Schubert coefficients is in NP ℂ ∩ P ℝ in the <PERSON>–<PERSON>–<PERSON>mal<PERSON> (BSS) model of computation. This result is incomparable to the inclusion in AM and underscores the algebraic nature of Schubert coefficients. We apply our approach to show that computing Schubert coefficients is in # P ℂ. This is the first nontrivial upper bound for the problem. We present our results in the generality of all series of classical reductive groups: general linear, special orthogonal, and symplectic groups of complex matrices, corresponding to root systems A,B,C, and D, respectively. With one notable exception, the above results extend to all series.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718269"}, {"primary_key": "198037", "vector": [], "sparse_vector": [], "title": "List-Decoding Capacity Implies Capacity on the q-ary Symmetric Channel.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is known that the Shannon capacity of the q-ary symmetric channel (qSC) is the same as the list-decoding capacity of an adversarial channel, raising the question of whether there is a formal (and black-box) connection between the two. We show that there is: Any linear code C⊆ Fqn that has superconstant minimum distance and achieves list-decoding capacity also achieves capacity on the qSC.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718206"}, {"primary_key": "198038", "vector": [], "sparse_vector": [], "title": "A Sharp Version of Talagrand&apos;s Selector Process Conjecture and an Application to Rounding Fractional Covers.", "authors": ["<PERSON><PERSON>"], "summary": "Expectation thresholds arise from a class of integer linear programs (LPs) that are fundamental to the study of thresholds in large random systems. An avenue towards estimating expectation thresholds comes from the fractional relaxation of these integer LPs, which yield the fractional expectation thresholds. Regarding the gap between the integer LPs and their fractional relaxations, <PERSON><PERSON><PERSON> made a bold conjecture, that the integral and fractional expectation thresholds are within a constant factor of each other. In other words, any small fractional solution can be \"rounded\". In this paper, we prove a strong upper bound on the expectation threshold starting from a fractional solution supported on sets with small size. In particular, this resolves <PERSON><PERSON><PERSON>'s conjecture for fractional solutions supported on sets with bounded size. Our key input for rounding the fractional solutions is a sharp version of <PERSON><PERSON><PERSON>'s selector process conjecture that is of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718256"}, {"primary_key": "198039", "vector": [], "sparse_vector": [], "title": "Testing Support Size More Efficiently Than Learning Histograms.", "authors": ["<PERSON><PERSON>.", "<PERSON>"], "summary": "Consider two problems about an unknown probability distribution p: (1) How many samples from p are required to test if p is supported on n elements or not? Specifically, given samples from p, determine whether it is supported on at most n elements, or it is ”є-far” (in total variation distance) from being supported on n elements. (2) Given m samples from p, what is the largest lower bound on its support size that we can produce? The best known upper bound for problem (1) uses a general algorithm for learning the histogram of the distribution p, which requires Θ(n/є2 logn) samples. We show that testing can be done more efficiently than learning the histogram, using only O(n/є logn log(1/є)) samples, nearly matching the best known lower bound of Ω(n/є logn). This algorithm also provides a better solution to problem (2), producing larger lower bounds on support size than what follows from previous work. The proof relies on an analysis of Chebyshev polynomial approximations outside the range where they are designed to be good approximations.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718134"}, {"primary_key": "198040", "vector": [], "sparse_vector": [], "title": "Sum-of-Squares Lower Bounds for Coloring Random Graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we prove Sum-of-Squares lower bounds for the coloring problem on random graphs. In particular, we show that for all є > 0, if G ∼ G(n,1/2) then with high probability, SoS requires degree Ω(logn) in order to prove that the chromatic number of G is at least n1/2 + є. Our result extends analogously for sparse random graphs from G(n,d/n) for logn ≤ d ≪ √n. Despite being a major goal in the study of integrality gaps for the Sum-of-Squares relaxation, before this work, such a result was known only for the basic -Theta SDP relaxation (equivalently, degree 2 Sum-of-Squares). While the related problem of Sum-of-Squares bounds for the independence numbers of random graphs has been progressively resolved over the past few years, similar progress on coloring requires tackling the challenge that the natural planted distribution is easily distinguishable from G(n,1/2) as well as new challenges in handling multiple exact equality constraints. In this work, we introduce new technical tools to tackle these challenges. We design a new principled “fix” to the pseudo-expectation values given by pseudo-calibration in order to address the failure of low-degree indistinguishability while still respecting the exact equality constraints. Our analysis of the new construction relies on an approximate matrix factorization technique via a new type of vertex separator which we call rainbow separators.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718151"}, {"primary_key": "198041", "vector": [], "sparse_vector": [], "title": "Truly Supercritical Trade-Offs for Resolution, Cutting Planes, Monotone Circuits, and Weisfeiler-Leman.", "authors": ["Susanna F. de Rezende", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We exhibit supercritical trade-off for monotone circuits, showing that there are functions computable by small circuits for which any small circuit must have depth superlinear or even super-polynomial in the number of variables, far exceeding the linear worst-case upper bound. We obtain similar trade-offs in proof complexity, where we establish the first size-depth trade-offs for cutting planes and resolution that are truly supercritical, i.e., in terms of formula size rather than number of variables, and also show supercritical trade-offs between width and size for treelike resolution. Our results build on a new supercritical width-depth trade-off for resolution, obtained by refining and strengthening the compression scheme for the cop-robber game in [Grohe, Lichter, Neuen & Schweitzer 2023]. This yields robust supercritical trade-offs for dimension versus iteration number in the <PERSON><PERSON><PERSON><PERSON><PERSON> algorithm, which also translate into trade-offs between number of variables and quantifier depth in first-order logic. Our other results follow from improved lifting theorems that might be of independent interest.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718271"}, {"primary_key": "198042", "vector": [], "sparse_vector": [], "title": "A Generalized Trace Reconstruction Problem: Recovering a String of Probabilities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce the following natural generalization of trace reconstruction, parameterized by a deletion probability δ ∈ (0,1) and length n: There is a length n string of probabilities, S=p1,…,pn, and each trace is obtained by 1) sampling a length n binary string whose ith coordinate is independently set to 1 with probability pi and 0 otherwise, and then 2) deleting each of the binary values independently with probability δ, and returning the corresponding binary string of length ≤ n. The goal is to recover an estimate of S from a set of independently drawn traces. In the case that all pi ∈ {0,1} this is the standard trace reconstruction problem. We show two complementary results. First, for worst-case strings S and any deletion probability at least order 1/√n, no algorithm can approximate S to constant ℓ∞ distance or ℓ1 distance o(√n) using fewer than 2Ω(√n) traces. Second–as in the case for standard trace reconstruction–reconstruction is easy for random S: for any sufficiently small constant deletion probability, and any є>0, drawing each pi independently from the uniform distribution over [0,1], with high probability S can be recovered to ℓ1 error є using (n,1/є) traces and computation time. We show indistinguishability in our lower bound by regarding a complicated alternating sum (comparing two distributions) as the Fourier transformation of some function evaluated at ± π, and then showing that the Fourier transform decays rapidly away from zero by analyzing its moment generating function.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718315"}, {"primary_key": "198043", "vector": [], "sparse_vector": [], "title": "Efficient Thermalization and Universal Quantum Computing with Quantum Gibbs Samplers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The preparation of quantum Gibbs states is a crucial task in quantum computing. In this work, we prove that a recently introduced, efficiently implementable dissipative evolution thermalizes to the Gibbs state in time scaling polynomially or even logarithmically with system size at high enough temperatures for any Hamiltonian that satisfies a Lie<PERSON><PERSON><PERSON> bound, such as local Hamiltonians on a lattice. Furthermore, we show the efficient adiabatic preparation of the associated purifications or \"thermofield double\" states. To the best of our knowledge, these are the first results rigorously establishing the efficient preparation of high-temperature Gibbs states and their purifications. In the low-temperature regime, we show that implementing this family of dissipative evolutions for inverse temperatures polynomial in the system's size is computationally equivalent to standard quantum computations. On a technical level, for high temperatures, our proof makes use of the mapping of the generator of the evolution into a Hamiltonian, and then connecting its convergence to that of the infinite temperature limit. We further present an alternative proof that is based on showing the exponential decay of the so-called oscillator norm, yielding convergence in logarithmic times. For low temperature, we instead perform a perturbation at zero temperature and resort to circuit-to-Hamiltonian mappings akin to the proof of universality of quantum adiabatic computing. Taken together, our results show that a family of quasi-local dissipative evolutions efficiently prepares a large class of quantum many-body states of interest, and has the potential to mirror the success of classical Monte Carlo methods for quantum many-body systems.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718268"}, {"primary_key": "198044", "vector": [], "sparse_vector": [], "title": "Strong XOR Lemma for Information Complexity.", "authors": ["<PERSON><PERSON>", "Huacheng Yu"], "summary": "For any {0,1}-valued function f, its n-folded XOR is the function f⊕ n where f⊕ n(X1, …, Xn) = f(X1) ⊕ ⋯ ⊕ f(Xn). Given a procedure for computing the function f, one can apply a “naive” approach to compute f⊕ n by computing each f(Xi) independently, followed by XORing the outputs. This approach uses n times the resources required for computing f. In this paper, we prove a strong XOR lemma for information complexity in the two-player randomized communication model: if computing f with an error probability of O(n−1) requires revealing I bits of information about the players’ inputs, then computing f⊕ n with a constant error requires revealing Ω(n) · (I − 1 − on(1)) bits of information about the players’ inputs. Our result demonstrates that the naive protocol for computing f⊕ n is both information-theoretically optimal and asymptotically tight in error trade-offs.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718258"}, {"primary_key": "198045", "vector": [], "sparse_vector": [], "title": "A Tolerant Independent Set Tester.", "authors": ["<PERSON>"], "summary": "We give nearly optimal bounds on the sample complexity of (Ω(є),є)-tolerant testing the ρ-independent set property in the dense graph setting. In particular, we give an algorithm that inspects a random subgraph on O(ρ3/є2) vertices and, for some constant c, distinguishes between graphs that have an induced subgraph of size ρ n with fewer than є/c log4(1/є) n2 edges from graphs for which every induced subgraph of size ρ n has at least є n2 edges. Our sample complexity bound matches, up to logarithmic factors, the recent upper bound by <PERSON><PERSON><PERSON> and <PERSON> (2023) for the non-tolerant testing problem, which is known to be optimal for the non-tolerant testing problem based on a lower bound by <PERSON><PERSON>, <PERSON> and <PERSON> (2004). Our main technique is a new graph container lemma for sparse subgraphs instead of independent sets. We also show that our new lemma can be used to generalize one of the classic applications of the container method, that of counting independent sets in regular graphs, to counting sparse subgraphs in regular graphs.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718290"}, {"primary_key": "198046", "vector": [], "sparse_vector": [], "title": "Better Approximation for Weighted k-Matroid Intersection.", "authors": ["<PERSON><PERSON>", "Theophile <PERSON>"], "summary": "We consider the problem of finding an independent set of maximum weight simultaneously contained in k matroids over a common ground set. This k-matroid intersection problem appears naturally in many contexts, for example in generalizing graph and hypergraph matching problems. In this paper, we provide a (k+1)/(2 ln2)-approximation algorithm for the weighted k-matroid intersection problem. This is the first improvement over the longstanding (k−1)-guarantee of <PERSON>, <PERSON><PERSON><PERSON> and <PERSON> (2009). Along the way, we also give the first improvement over greedy for the more general weighted matroid k-parity problem. Our key innovation lies in a randomized reduction in which we solve almost unweighted instances iteratively. This perspective allows us to use insights from the unweighted problem for which <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> have designed a k/2-approximation algorithm. We analyze this procedure by constructing refined matroid exchanges and leveraging randomness to avoid bad local minima.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718219"}, {"primary_key": "198047", "vector": [], "sparse_vector": [], "title": "Dimension Independent and Computationally Efficient Shadow Tomography.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We describe a new shadow tomography algorithm that uses n=Θ(√mlogm/є2) samples, for m measurements and additive error є, which is independent of the dimension of the quantum state being learned. This stands in contrast to all previously known algorithms that improve upon the naive approach. The sample complexity also has optimal dependence on є. Due to its dimension independent nature, this algorithm has the best known sample complexity in the regime m=o(log2 d) (when є is chosen independently from d). Additionally, this algorithm is efficient in various aspects, including quantum memory usage (possibly even O(1)), gate complexity, classical computation, all of which are also do not have any dimension dependence. This algorithm is also robust against qubit measurement noise, by which we mean that the qubit measurement errors can only affect the accuracy of the estimates, and small noise leads to small additive error in the estimates. Apart from this, the algorithm can be modified to also be implementable as a read-once quantum circuit with low quantum memory usage, i.e., it will hold only one copy of ρ in memory, and discard it before asking for a new one, with the additional quantum memory needed being O(mlogn) qubits. Our approach builds on the idea of using noisy measurements, but instead of focusing on gentleness in trace distance, we focus on the gentleness in shadows, i.e., we show that the noisy measurements do not significantly perturb the expected values. To work with this, we think of noisy measurements as measuring a noisy encoding of the quantity we are trying to noisily measure. In our case, these are the observables corresponding to sample means of the m POVMs. We see that the product state structure of the samples is effectively maintained (with possibly some additional randomness) even after the creation and tracing out of the specific noisy encodings we work with.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718253"}, {"primary_key": "198048", "vector": [], "sparse_vector": [], "title": "Sharp Phase Transitions in Estimation with Low-Degree Polynomials.", "authors": ["Young<PERSON><PERSON>", "<PERSON>"], "summary": "High-dimensional planted problems, such as finding a hidden dense subgraph within a random graph, often exhibit a gap between statistical and computational feasibility. While recovering the hidden structure may be statistically possible, it is conjectured to be computationally intractable in certain parameter regimes. A powerful approach to understanding this hardness involves proving lower bounds on the efficacy of low-degree polynomial algorithms. We introduce new techniques for establishing such lower bounds, leading to novel results across diverse settings: planted submatrix, planted dense subgraph, the spiked <PERSON><PERSON><PERSON> model, and the stochastic block model. Notably, our results address the estimation task — whereas most prior work is limited to hypothesis testing — and capture sharp phase transitions such as the “BBP” transition in the spiked <PERSON><PERSON><PERSON> model (named for <PERSON><PERSON>, <PERSON>, and <PERSON>) and the Kesten–Stigum threshold in the stochastic block model. Existing work on estimation either falls short of achieving these sharp thresholds or is limited to polynomials of very low (constant or logarithmic) degree. In contrast, our results rule out estimation with polynomials of degree nδ where n is the dimension and δ > 0 is a constant, and in some cases we pin down the optimal constant δ. Our work resolves open problems posed by <PERSON> & <PERSON> (2017) and Schramm & Wein (2022), and provides rigorous support within the low-degree framework for conjectures by <PERSON><PERSON> <PERSON> (2018) and Lelarge & Miolane (2019).", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718294"}, {"primary_key": "198049", "vector": [], "sparse_vector": [], "title": "Faster Rates for No-Regret Learning in General Games via Cautious Optimism.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We establish the first uncoupled learning algorithm that attains O(n log2 d logT) per-player regret in multi-player general-sum games, where n is the number of players, d is the number of actions available to each player, and T is the number of repetitions of the game. Our results exponentially improve the dependence on d compared to the O(n d logT) regret attainable by Log-Regularized Lifted Optimistic FTRL introduced by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and Sand<PERSON> [2022], and also reduce the dependence on the number of iterations T from log4 T to logT compared to Optimistic Hedge, the previously well-studied algorithm with O(n logd log4 T) regret shown by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [2021]. Our algorithm is obtained by combining the classic Optimistic Multiplicative Weights Update (OMWU) with an adaptive, non-monotonic learning rate that paces the learning process of the players, making them more cautious when their regret becomes too negative.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718242"}, {"primary_key": "198050", "vector": [], "sparse_vector": [], "title": "Symmetric Perceptrons, Number Partitioning and Lattices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The symmetric binary perceptron (SBPκ) problem with parameter κ : ℝ≥1 → [0,1] is an average-case search problem defined as follows: given a random Gaussian matrix A ∼ N(0,1)n × m as input where m ≥ n, output a vector x ∈ {−1,1}m such that || A x ||∞ ≤ κ(m/n) · √m . The number partitioning problem (NPPκ) corresponds to the special case of setting n=1. There is considerable evidence that both problems exhibit large computational-statistical gaps. In this work, we show (nearly) tight average-case hardness for these problems, assuming the worst-case hardness of standard approximate shortest vector problems on lattices. • For SBPκ, statistically, solutions exist with κ(x) = 2−Θ(x) (<PERSON><PERSON>, <PERSON> and <PERSON>de<PERSON>v<PERSON>, Journal of Physics 2019). For large n, the best that efficient algorithms have been able to achieve is a far cry from the statistical bound, namely κ(x) = Θ(1/√x) (<PERSON><PERSON> and <PERSON>, Random Structures and Algorithms 2020). The problem has been extensively studied in the TCS and statistics communities, and Gamarnik, K<PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (FOCS 2022) conjecture that Ban<PERSON><PERSON><PERSON> is tight: namely, κ(x) = Θ(1/√x) is the optimal value achieved by computationally efficient algorithms. We prove their conjecture assuming the worst-case hardness of approximating the shortest vector problem on lattices. • For NPPκ, statistically, solutions exist with κ(m) = Θ(2−m) (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>eker and <PERSON>dlyzko, Journal of Applied Probability 1986). Karmarkar and Karp’s classical differencing algorithm achieves κ(m) = 2−O(log2 m) . We prove that Karmarkar-Karp is nearly tight: namely, no polynomial-time algorithm can achieve κ(m) = 2−Ω(log3 m), once again assuming the worst-case subexponential hardness of approximating the shortest vector problem on lattices to within a subexponential factor. Our hardness results are versatile, and hold with respect to different distributions of the matrix A (e.g., i.i.d. uniform entries from [0,1]) and weaker requirements on the solution vector x.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718263"}, {"primary_key": "198051", "vector": [], "sparse_vector": [], "title": "Breaking the Barrier of Self-Concordant Barriers: Faster Interior Point Methods for M-Matrices.", "authors": ["<PERSON>"], "summary": "We study two fundamental optimization problems: (1) scaling a symmetric positive definite matrix by a positive diagonal matrix so that the resulting matrix has row and column sums equal to 1; and (2) minimizing a quadratic function subject to hard non-negativity constraints. Both problems lend themselves to efficient algorithms based on interior point methods (IPMs). For general instances, standard self-concordance theory places a limit on the iteration complexity of these methods at Õ(n^1/2), where n denotes the matrix dimension. We show via an amortized analysis that, when the input matrix is an M-matrix, an IPM with adaptive step sizes solves both problems in only Õ(n^1/3) iterations. As a corollary, using fast Laplacian solvers, we obtain an ℓ_2 flow diffusion algorithm with depth Õ(n^1/3) and work Õ(n^1/3·nnz). This result marks a significant instance in which a standard log-barrier IPM permits provably fewer than Θ(n^1/2) iterations.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718255"}, {"primary_key": "198052", "vector": [], "sparse_vector": [], "title": "Harmonic Decomposition in Data Sketches.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In the turnstile streaming model, a dynamic vector x=(x1,…,xn)∈ ℤn is updated by a stream of entry-wise increments/decrements. Let f∶ℤ→ ℝ+ be a symmetric function with f(0)=0. The f-moment of x is defined to be f(x) := ∑v∈[n]f(xv). We revisit the problem of constructing a universal sketch that can estimate many different f-moments. Previous constructions of universal sketches rely on the technique of sampling with respect to the L0-mass (uniform samples) or L2-mass (L2-heavy-hitters), whose universality comes from being able to evaluate the function f over the samples. To get samples, hash collisions are deliberately detected and avoided (with high probability), e.g. singleton-detectors are used in L0-sampling and the CountSketch is used in L2-sampling. Such auxiliary data structures introduce significant overhead in space. Apart from this issue, sampling-based methods are shown to perform poorly for estimating certain “nearly periodic functions” where Ω(poly(n)) samples are needed. In this paper, we propose a new universal sketching scheme that is almost “dual” to the sampling-based methods. Instead of evaluating f on samples, we decompose f into a linear combination of homomorphisms f1,f2,… from (ℤ,+) to (ℂ,×), where the fk-moments can be estimated regardless of hash collisions—because each fk is a homomorphism! Then we synthesize the estimates of the fk-moments to obtain an estimate of the f-moment. Universality now comes from the fact that we can weight the fk-moments arbitrarily, where the correct weighting depends on the harmonic structure of the function f. In contrast to the sampling-based methods, the new SymmetricPoissonTower sketch takes the harmonic approach. It embraces hash collisions instead of avoiding them, which saves multiple logn factors in space, e.g., when estimating all Lp-moments (f(z) = |z|p,p∈[0,2]). For many nearly periodic functions, the new sketch is exponentially more efficient than sampling-based methods. We conjecture that the SymmetricPoissonTower is the universal sketch that can estimate every tractable function f.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718321"}, {"primary_key": "198053", "vector": [], "sparse_vector": [], "title": "Simulating Time with Square-Root Space.", "authors": ["<PERSON><PERSON>"], "summary": "We show that for all functions t(n) ≥ n, every multitape Turing machine running in time t can be simulated in space only O(√t logt). This is a substantial improvement over <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>’s simulation of time t in O(t/logt) space from 50 years ago [F<PERSON><PERSON> 1975, JACM 1977]. Among other results, our simulation implies that bounded fan-in circuits of size s can be evaluated on any input in only √s · poly(logs) space, and that there are explicit problems solvable in O(n) space which require at least n2−ε time on every multitape Turing machine for all ε > 0, thereby making a little progress on the P versus PSPACE problem. Our simulation reduces the problem of simulating time-bounded multitape Turing machines to a series of implicitly-defined Tree Evaluation instances with nice parameters, leveraging the remarkable space-efficient algorithm for Tree Evaluation recently found by <PERSON> and <PERSON> [STOC 2024].", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718225"}, {"primary_key": "198054", "vector": [], "sparse_vector": [], "title": "Learning the Structure of Any Hamiltonian from Minimal Assumptions.", "authors": ["<PERSON>"], "summary": "We study the problem of learning an unknown quantum many-body Hamiltonian H from black-box queries to its time evolution e−i H t. Prior proposals for solving this task either impose some assumptions on H, such as its interaction structure or locality, or otherwise use an exponential amount of computational postprocessing. In this paper, we present algorithms to learn any n-qubit Hamiltonian, which do not need to know the Hamiltonian terms in advance, nor are they restricted to local interactions. Our algorithms are efficient as long as the number of terms m is polynomially bounded in the system size n. We consider two models of control over the time evolution: the first has access to time reversal (t < 0), enabling an algorithm that outputs an є-accurate classical description of H after querying its dynamics for a total of O(m/є) evolution time. The second access model is more conventional, allowing only forward-time evolutions; our algorithm requires O(||H||3/є4) evolution time in this setting. Central to our results is the recently introduced concept of a pseudo-Choi state of H. We extend the utility of this learning resource by showing how to use it to learn the Fourier spectrum of H, how to achieve nearly Heisenberg-limited scaling with it, and how to prepare it even under our more restricted access models.", "published": "2025-01-01", "category": "stoc", "pdf_url": "", "sub_summary": "", "source": "stoc", "doi": "10.1145/3717823.3718115"}]