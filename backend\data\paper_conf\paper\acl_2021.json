[{"primary_key": "2079043", "vector": [], "sparse_vector": [], "title": "A Case Study of Analysis of Construals in Language on Social Media Surrounding a Crisis Event.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.31"}, {"primary_key": "2079060", "vector": [], "sparse_vector": [], "title": "Ecco: An Open Source Library for the Explainability of Transformer Language Models.", "authors": ["<PERSON><PERSON>"], "summary": "<PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.30"}, {"primary_key": "2079066", "vector": [], "sparse_vector": [], "title": "REM: Efficient Semi-Automated Real-Time Moderation of Online Forums.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.17"}, {"primary_key": "2079071", "vector": [], "sparse_vector": [], "title": "COVID-19 and Misinformation: A Large-Scale Lexical Analysis on Twitter.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.13"}, {"primary_key": "2079084", "vector": [], "sparse_vector": [], "title": "Semantics of the Unwritten: The Effect of End of Paragraph and Sequence Tokens on Text Generation with GPT2.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.16"}, {"primary_key": "2079087", "vector": [], "sparse_vector": [], "title": "Long Document Summarization in a Low Resource Setting using Pretrained Language Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Krishna", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bradford Windsor", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.7"}, {"primary_key": "2079090", "vector": [], "sparse_vector": [], "title": "How Low is Too Low? A Computational Perspective on Extremely Low-Resource Languages.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.5"}, {"primary_key": "2079097", "vector": [], "sparse_vector": [], "title": "Trafilatura: A Web Scraping Library and Command-Line Tool for Text Discovery and Extraction.", "authors": ["<PERSON><PERSON>"], "summary": "<PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.15"}, {"primary_key": "2079140", "vector": [], "sparse_vector": [], "title": "Tools Impact on the Quality of Annotations for Chat Untangling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.22"}, {"primary_key": "2079171", "vector": [], "sparse_vector": [], "title": "ReTraCk: A Flexible and Efficient Framework for Knowledge Base Question Answering.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Feng <PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.39"}, {"primary_key": "2079196", "vector": [], "sparse_vector": [], "title": "Stylistic approaches to predicting Reddit popularity in diglossia.", "authors": ["Huikai Chua"], "summary": "Hui<PERSON> Chua. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.10"}, {"primary_key": "2079219", "vector": [], "sparse_vector": [], "title": "Synchronous Syntactic Attention for Transformer Neural Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.36"}, {"primary_key": "2079221", "vector": [], "sparse_vector": [], "title": "Cross-lingual Evidence Improves Monolingual Fake News Detection.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.32"}, {"primary_key": "2079266", "vector": [], "sparse_vector": [], "title": "Does My Representation Capture X? Probe-Ably.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.23"}, {"primary_key": "2079267", "vector": [], "sparse_vector": [], "title": "Changing the Basis of Contextual Representations with Explicit Semantics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.25"}, {"primary_key": "2079274", "vector": [], "sparse_vector": [], "title": "Erase and Rewind: Manual Correction of NLP Output through a Web Interface.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.13"}, {"primary_key": "2079304", "vector": [], "sparse_vector": [], "title": "fastHan: A BERT-based Multi-Task Toolkit for Chinese NLP.", "authors": ["<PERSON><PERSON><PERSON>", "Hang Yan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.12"}, {"primary_key": "2079316", "vector": [], "sparse_vector": [], "title": "IFlyEA: A Chinese Essay Assessment System with Automated Rating, Review Generation, and Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.29"}, {"primary_key": "2079320", "vector": [], "sparse_vector": [], "title": "Many-to-English Machine Translation Tools, Data, and Pretrained Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.37"}, {"primary_key": "2079324", "vector": [], "sparse_vector": [], "title": "Video-guided Machine Translation with Spatial Hierarchical Attention Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.9"}, {"primary_key": "2079338", "vector": [], "sparse_vector": [], "title": "SumPubMed: Summarization Dataset of PubMed Scientific Articles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>egah <PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.30"}, {"primary_key": "2079355", "vector": [], "sparse_vector": [], "title": "Neural Machine Translation with Synchronous Latent Phrase Structure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.33"}, {"primary_key": "2079372", "vector": [], "sparse_vector": [], "title": "ESRA: Explainable Scientific Research Assistant.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhang<PERSON>ng Lai", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.14"}, {"primary_key": "2079373", "vector": [], "sparse_vector": [], "title": "Exploring Word Usage Change with Continuously Evolving Embeddings.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.35"}, {"primary_key": "2079380", "vector": [], "sparse_vector": [], "title": "Stretch-VST: Getting Flexible With Visual Stories.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON>) <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.42"}, {"primary_key": "2079415", "vector": [], "sparse_vector": [], "title": "ParCourE: A Parallel Corpus Explorer for a Massively Multilingual Corpus.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.8"}, {"primary_key": "2079416", "vector": [], "sparse_vector": [], "title": "Modeling Text using the Continuous Space Topic Model with Pre-Trained Word Embeddings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.15"}, {"primary_key": "2079417", "vector": [], "sparse_vector": [], "title": "Zero Pronouns Identification based on Span prediction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.34"}, {"primary_key": "2079435", "vector": [], "sparse_vector": [], "title": "CogIE: An Information Extraction Toolkit for Bridging Texts and CogNet.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.11"}, {"primary_key": "2079438", "vector": [], "sparse_vector": [], "title": "The Classical Language Toolkit: An NLP Framework for Pre-Modern Languages.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.3"}, {"primary_key": "2079442", "vector": [], "sparse_vector": [], "title": "Edit Distance Based Curriculum Learning for Paraphrase Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.24"}, {"primary_key": "2079467", "vector": [], "sparse_vector": [], "title": "LOA: Logical Optimal Actions for Text-based Interaction Games.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.27"}, {"primary_key": "2079481", "vector": [], "sparse_vector": [], "title": "Joint Detection and Coreference Resolution of Entities and Events with Document-level Context Aggregation.", "authors": ["<PERSON>", "<PERSON>ng <PERSON>"], "summary": "<PERSON>, <PERSON><PERSON> <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.18"}, {"primary_key": "2079485", "vector": [], "sparse_vector": [], "title": "How do different factors Impact the Inter-language Similarity? A Case Study on Indian languages.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.12"}, {"primary_key": "2079507", "vector": [], "sparse_vector": [], "title": "IntelliCAT: Intelligent Machine Translation Post-Editing with Quality Estimation and Translation Suggestion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Heesoo Park", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.2"}, {"primary_key": "2079518", "vector": [], "sparse_vector": [], "title": "Supporting Complaints Investigation for Nursing and Midwifery Regulatory Agencies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.10"}, {"primary_key": "2079521", "vector": [], "sparse_vector": [], "title": "LEGOEval: An Open-Source Toolkit for Dialogue System Evaluation via Crowdsourcing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.38"}, {"primary_key": "2079544", "vector": [], "sparse_vector": [], "title": "TextBox: A Unified, Modularized, and Extensible Framework for Text Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jinhao Jiang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.4"}, {"primary_key": "2079580", "vector": [], "sparse_vector": [], "title": "skweak: Weak Supervision Made Easy for NLP.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.40"}, {"primary_key": "2079588", "vector": [], "sparse_vector": [], "title": "ExplainaBoard: An Explainable Leaderboard for NLP.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Weizhe Yuan", "<PERSON><PERSON><PERSON>", "Junqi Dai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.34"}, {"primary_key": "2079618", "vector": [], "sparse_vector": [], "title": "TexSmart: A System for Enhanced Natural Language Understanding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Haiyun Jiang", "<PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Song", "<PERSON><PERSON><PERSON> Zheng", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Shuming Shi"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.1"}, {"primary_key": "2079674", "vector": [], "sparse_vector": [], "title": "Personal Bias in Prediction of Emotions Elicited by Textual Opinions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.26"}, {"primary_key": "2079677", "vector": [], "sparse_vector": [], "title": "A Graphical Interface for Curating Schemas.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.19"}, {"primary_key": "2079699", "vector": [], "sparse_vector": [], "title": "PAWLS: PDF Annotation With Labels and Structure.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Sam <PERSON>kjonsberg"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.31"}, {"primary_key": "2079702", "vector": [], "sparse_vector": [], "title": "Inside ASCENT: Exploring a Deep Commonsense Knowledge Base and its Usage in Question Answering.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "ASCENT is a fully automated methodology for extracting and consolidating commonsense assertions from web contents (<PERSON><PERSON><PERSON> et al., WWW 2021). It advances traditional triple-based commonsense knowledge representation by capturing semantic facets like locations and purposes, and composite concepts, i.e., subgroups and related aspects of subjects. In this demo, we present a web portal that allows users to understand its construction process, explore its content, and observe its impact in the use case of question answering. The demo website and an introductory video are both available online.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.5"}, {"primary_key": "2079708", "vector": [], "sparse_vector": [], "title": "Data Augmentation with Unsupervised Machine Translation Improves the Structural Similarity of Cross-lingual Word Embeddings.", "authors": ["<PERSON><PERSON>", "Ryokan <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.17"}, {"primary_key": "2079729", "vector": [], "sparse_vector": [], "title": "CLTR: An End-to-End, Transformer-Based System for Cell-Level Table Retrieval and Table Question Answering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.24"}, {"primary_key": "2079757", "vector": [], "sparse_vector": [], "title": "CMTA: COVID-19 Misinformation Multilingual Analysis on Twitter.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Genoveva Vargas-Solar"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>-<PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.28"}, {"primary_key": "2079763", "vector": [], "sparse_vector": [], "title": "ProphetNet-X: Large-Scale Pre-training Models for English, Chinese, Multi-lingual, Dialog, and Code Generation.", "authors": ["Weizhen Qi", "<PERSON>yun Gong", "Yu <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.28"}, {"primary_key": "2079786", "vector": [], "sparse_vector": [], "title": "Neural Extractive Search.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.25"}, {"primary_key": "2079789", "vector": [], "sparse_vector": [], "title": "MT-Telescope: An interactive platform for contrastive evaluation of MT systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.9"}, {"primary_key": "2079816", "vector": [], "sparse_vector": [], "title": "Attending Self-Attention: A Case Study of Visually Grounded Supervision in Vision-and-Language Transformers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.8"}, {"primary_key": "2079834", "vector": [], "sparse_vector": [], "title": "TweeNLP: A Twitter Exploration Portal for Natural Language Processing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.32"}, {"primary_key": "2079846", "vector": [], "sparse_vector": [], "title": "SciConceptMiner: A system for large-scale scientific concept discovery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.6"}, {"primary_key": "2079860", "vector": [], "sparse_vector": [], "title": "Improving the Robustness of QA Models to Challenge Sets with Variational Question-Answer Pair Generation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.21"}, {"primary_key": "2079873", "vector": [], "sparse_vector": [], "title": "How Many Layers and Why? An Analysis of the Model Depth in Transformers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.23"}, {"primary_key": "2079874", "vector": [], "sparse_vector": [], "title": "&quot;Hold on honey, men at work&quot;: A semi-supervised approach to detecting sexism in sitcoms.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.19"}, {"primary_key": "2079878", "vector": [], "sparse_vector": [], "title": "Situation-Based Multiparticipant Chat Summarization: a Concept, an Exploration-Annotation Tool and an Example Collection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.14"}, {"primary_key": "2079888", "vector": [], "sparse_vector": [], "title": "Observing the Learning Curve of NMT Systems With Regard to Linguistic Phenomena.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.20"}, {"primary_key": "2079928", "vector": [], "sparse_vector": [], "title": "&quot;<PERSON>&apos;ve Seen Things You People Wouldn&apos;t Believe&quot;: Hallucinating Entities in GuessWhat?!", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.11"}, {"primary_key": "2079938", "vector": [], "sparse_vector": [], "title": "Investigation on Data Adaptation Techniques for Neural Named Entity Recognition.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Data processing is an important step in various natural language processing tasks. As the commonly used datasets in named entity recognition contain only a limited number of samples, it is important to obtain additional labeled data in an efficient and reliable manner. A common practice is to utilize large monolingual unlabeled corpora. Another popular technique is to create synthetic data from the original labeled data (data augmentation). In this work, we investigate the impact of these two methods on the performance of three different named entity recognition tasks.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.1"}, {"primary_key": "2079956", "vector": [], "sparse_vector": [], "title": "On the Relationship between Zipf&apos;s Law of Abbreviation and Interfering Noise in Emergent Languages.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.6"}, {"primary_key": "2079963", "vector": [], "sparse_vector": [], "title": "On the differences between BERT and MT encoder spaces and how to address them in translation tasks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.35"}, {"primary_key": "2079971", "vector": [], "sparse_vector": [], "title": "SummVis: Interactive Visual Analysis of Models, Data, and Evaluation for Text Summarization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.18"}, {"primary_key": "2079996", "vector": [], "sparse_vector": [], "title": "TextFlint: Unified Multilingual Robustness Evaluation Toolkit for Natural Language Processing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Yicheng Zou", "<PERSON><PERSON>", "Jiacheng Ye", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuan Hu", "<PERSON><PERSON> Bian", "<PERSON><PERSON><PERSON>", "Shan Qin", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhongyu Wei", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.41"}, {"primary_key": "2080008", "vector": [], "sparse_vector": [], "title": "Dodrio: Exploring Transformer Models with Interactive Visualization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.16"}, {"primary_key": "2080018", "vector": [], "sparse_vector": [], "title": "Transformer-Based Direct Hidden Markov Model for Machine Translation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.3"}, {"primary_key": "2080019", "vector": [], "sparse_vector": [], "title": "Stage-wise Fine-tuning for Graph-to-Text Generation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>ng <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.2"}, {"primary_key": "2080068", "vector": [], "sparse_vector": [], "title": "KuiLeiXi: a Chinese Open-Ended Text Adventure Game.", "authors": ["Yadong Xi", "<PERSON><PERSON>", "Le Li", "<PERSON><PERSON>", "Yanjiang Chen", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Gongzheng Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.21"}, {"primary_key": "2080106", "vector": [], "sparse_vector": [], "title": "TURING: an Accurate and Interpretable Multi-Hypothesis Cross-Domain Natural Language Database Interface.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.36"}, {"primary_key": "2080117", "vector": [], "sparse_vector": [], "title": "FastSeq: Make Sequence Generation Faster.", "authors": ["Yu <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>yun Gong", "<PERSON>", "<PERSON><PERSON><PERSON>", "Bingyu Chi", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.26"}, {"primary_key": "2080130", "vector": [], "sparse_vector": [], "title": "Predicting pragmatic discourse features in the language of adults with autism spectrum disorder.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Emily <PERSON>&apo<PERSON>;hommeaux"], "summary": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.29"}, {"primary_key": "2080173", "vector": [], "sparse_vector": [], "title": "OpenAttack: An Open-source Textual Adversarial Attack Toolkit.", "authors": ["Guoyang Zeng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Maosong Sun"], "summary": "Textual adversarial attacking has received wide and increasing attention in recent years. Various attack models have been proposed, which are enormously distinct and implemented with different programming frameworks and settings. These facts hinder quick utilization and fair comparison of attack models. In this paper, we present an open-source textual adversarial attack toolkit named OpenAttack to solve these issues. Compared with existing other textual adversarial attack toolkits, OpenAttack has its unique strengths in support for all attack types, multilinguality, and parallel processing. Currently, OpenAttack includes 15 typical attack models that cover all attack types. Its highly inclusive modular design not only supports quick utilization of existing attack models, but also enables great flexibility and extensibility. OpenAttack has broad uses including comparing and evaluating attack models, measuring robustness of a model, assisting in developing new attack models, and adversarial training. Source code and documentation can be obtained at https://github.com/thunlp/OpenAttack.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.43"}, {"primary_key": "2080188", "vector": [], "sparse_vector": [], "title": "ChrEnTranslate: Cherokee-English Machine Translation Demo with Quality Estimation and Corrective Feedback.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.33"}, {"primary_key": "2080200", "vector": [], "sparse_vector": [], "title": "TEXTOIR: An Integrated and Visualized Platform for Text Open Intent Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "Xiaoteng Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "TEXTOIR is the first integrated and visualized platform for text open intent recognition. It is composed of two main modules: open intent detection and open intent discovery. Each module integrates most of the state-of-the-art algorithms and benchmark intent datasets. It also contains an overall framework connecting the two modules in a pipeline scheme. In addition, this platform has visualized tools for data and model management, training, evaluation and analysis of the performance from different aspects. TEXTOIR provides useful toolkits and convenient visualized interfaces for each sub-module (Toolkit code: https://github.com/thuiar/TEXTOIR), and designs a framework to implement a complete process to both identify known intents and discover open intents (Demo code: https://github.com/thuiar/TEXTOIR-DEMO).", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.20"}, {"primary_key": "2080228", "vector": [], "sparse_vector": [], "title": "NeurST: Neural Speech Translation Toolkit.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.7"}, {"primary_key": "2080253", "vector": [], "sparse_vector": [], "title": "CRSLab: An Open-Source Toolkit for Building Conversational Recommender System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: System Demonstrations. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-DEMO.22"}, {"primary_key": "2080257", "vector": [], "sparse_vector": [], "title": "AutoRC: Improving BERT Based Relation Classification Models via Architecture Search.", "authors": ["<PERSON>"], "summary": "<PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.4"}, {"primary_key": "2080258", "vector": [], "sparse_vector": [], "title": "MVP-BERT: Multi-Vocab Pre-training for Chinese BERT.", "authors": ["<PERSON>"], "summary": "<PERSON>. Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing: Student Research Workshop. 2021.", "published": "2021-01-01", "category": "acl", "pdf_url": "", "sub_summary": "", "source": "acl", "doi": "10.18653/V1/2021.ACL-SRW.27"}]