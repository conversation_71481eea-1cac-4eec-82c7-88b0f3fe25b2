[{"primary_key": "2549765", "vector": [], "sparse_vector": [], "title": "Estimating g-Leakage via Machine Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper considers the problem of estimating the information leakage of a system in the black-box scenario, i.e. when the system's internals are unknown to the learner, or too complicated to analyze, and the only available information are pairs of input-output data samples, obtained by submitting queries to the system or provided by a third party. The frequentist approach relies on counting the frequencies to estimate the input-output conditional probabilities, however this method is not accurate when the domain of possible outputs is large. To overcome this difficulty, the estimation of the Bay<PERSON> error of the ideal classifier was recently investigated using Machine Learning (ML) models, and it has been shown to be more accurate thanks to the ability of those models to learn the input-output correspondence. However, the Bayes vulnerability is only suitable to describe one-try attacks. A more general and flexible measure of leakage is the g-vulnerability, which encompasses several different types of adversaries, with different goals and capabilities. We propose a novel approach to perform black-box estimation of the g-vulnerability using ML which does not require to estimate the conditional probabilities and is suitable for a large class of ML algorithms. First, we formally show the learnability for all data distributions. Then, we evaluate the performance via various experiments using k-Nearest Neighbors and Neural Networks. Our approach outperform the frequentist one when the observables domain is large.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423363"}, {"primary_key": "2549766", "vector": [], "sparse_vector": [], "title": "FREEDOM: Engineering a State-of-the-Art DOM Fuzzer.", "authors": ["<PERSON>", "Soyeon Park", "<PERSON><PERSON><PERSON>"], "summary": "The DOM engine of a web browser is a popular attack surface and has been thoroughly fuzzed during its development. A common approach adopted by the latest DOM fuzzers is to generate new inputs based on context-free grammars. However, such a generative approach fails to capture the data dependencies in the inputs of a DOM engine, namely, HTML documents. Meanwhile, it is unclear whether or not coverage-guided mutation, which is well-known to be effective in fuzzing numerous software, still remains to be effective against DOM engines. Worse yet, existing DOM fuzzers cannot adopt a coverage-guided approach because they are unable to fully support HTML mutation and suffer from low browser throughput. To scientifically understand the effectiveness and limitations of the two approaches, we propose FreeDom, a full-fledged cluster-friendly DOM fuzzer that works with both generative and coverage-guided modes. FreeDom relies on a context-aware intermediate representation to describe HTML documents with proper data dependencies. FreeDom also exhibits up to 3.74x higher throughput through browser self-termination. FreeDom has found 24 previously unknown bugs in commodity browsers including Safari, Firefox, and Chrome, and 10 CVEs has been assigned so far. With the context-aware generation, FreeDom finds 3x more unique crashes in WebKit than the state-of-the-art DOM fuzzer, Domato. FreeDom guided by coverage is more effective in revealing new code blocks (2.62%) and finds three complex bugs that its generative approach fails to find. However, coverage-guided mutation that bootstraps with an empty corpus triggers 3.8x fewer unique crashes than the generative approach. The newly revealed coverage, more often than not, negatively affects the effectiveness of DOM fuzzers in bug finding. Therefore, we consider context-aware generation the best practice to find more DOM engine bugs and expect further improvement on coverage-guided DOM fuzzing facilitated by FreeDom.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423340"}, {"primary_key": "2549767", "vector": [], "sparse_vector": [], "title": "DeepDyve: Dynamic Verification for Deep Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deep neural networks (DNNs) have become one of the enabling technologies in many safety-critical applications, e.g., autonomous driving and medical image analysis. DNN systems, however, suffer from various kinds of threats, such as adversarial example attacks and fault injection attacks. While there are many defense methods proposed against maliciously crafted inputs, solutions against faults presented in the DNN system itself (e.g., parameters and calculations) are far less explored. In this paper, we develop a novel lightweight fault-tolerant solution for DNN-based systems, namely DeepDyve, which employs pre-trained neural networks that are far simpler and smaller than the original DNN for dynamic verification. The key to enabling such lightweight checking is that the smaller neural network only needs to produce approximate results for the initial task without sacrificing fault coverage much. We develop efficient and effective architecture and task exploration techniques to achieve optimized risk/overhead trade-off in DeepDyve. Experimental results show that DeepDyve can reduce 90% of the risks at around 10% overhead.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423338"}, {"primary_key": "2549768", "vector": [], "sparse_vector": [], "title": "Lies in the Air: Characterizing Fake-base-station Spam Ecosystem in China.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Lu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Fake base station (FBS) has been exploited by criminals to attack mobile users by spamming fraudulent messages for over a decade. Despite that prior work has proposed several techniques to mitigate this issue, FBS spam is still a long-standing challenging issue in some countries, such as China, and causes billions of dollars of financial loss every year. Therefore, understanding and exploring the thematic strategies in the FBS spam ecosystem at a large scale would improve the defense mechanisms.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417257"}, {"primary_key": "2549769", "vector": [], "sparse_vector": [], "title": "Is the Classical GMW Paradigm Practical? The Case of Non-Interactive Actively Secure 2PC.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "One of the most challenging aspects in secure computation is offering protection against active adversaries, who may arbitrarily alter the behavior of corrupted parties. A powerful paradigm due to <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (GMW), is to follow a two-step approach: (1) design a passively secure protocol π for the task at hand; (2) apply a general compiler to convert π into an actively secure protocol π' for the same task.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423366"}, {"primary_key": "2549770", "vector": [], "sparse_vector": [], "title": "VisualPhishNet: Zero-Day Phishing Website Detection by Visual Similarity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Phishing websites are still a major threat in today's Internet ecosystem. Despite numerous previous efforts, similarity-based detection methods do not offer sufficient protection for the trusted websites, in particular against unseen phishing pages. This paper contributes VisualPhishNet, a new similarity-based phishing detection framework, based on a triplet Convolutional Neural Network (CNN). VisualPhishNet learns profiles for websites in order to detect phishing websites by a similarity metric that can generalize to pages with new visual appearances. We furthermore present VisualPhish, the largest dataset to date that facilitates visual phishing detection in an ecologically valid manner. We show that our method outperforms previous visual similarity phishing detection approaches by a large margin while being robust against a range of evasion attacks.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417233"}, {"primary_key": "2549771", "vector": [], "sparse_vector": [], "title": "Lift-and-Shift: Obtaining Simulation Extractable Subversion and Updatable SNARKs Generically.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Zero-knowledge proofs and in particular succinct non-interactive zero-knowledge proofs (so called zk-SNARKs) are getting increasingly used in real-world applications, with cryptocurrencies being the prime example. Simulation extractability (SE) is a strong security notion for zk-SNARKs which informally ensures non-malleability of proofs. The high importance of this property is acknowledged by leading companies in this field such as Zcash and underpinned by various attacks against the malleability of cryptographic primitives in the past. Another problematic issue for the practical use of zk-SNARKs is the requirement of a fully trusted setup, as especially for large-scale decentralized applications finding a trusted party that runs the setup is practically impossible. Quite recently, the study of approaches to relax or even remove the trust in the setup procedure, and in particular subversion as well as updatable zk-SNARKs (with latter being the most promising approach), has been initiated and received considerable attention since then. Unfortunately, so far SE-SNARKs with the aforementioned properties are only constructed in an ad-hoc manner and no generic techniques are available.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417228"}, {"primary_key": "2549772", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>, Robust Anonymous Committed Broadcast.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Anonymous Committed Broadcast is a functionality that extends DC-nets and allows a set of clients to privately commit messages to set of servers, which can then simultaneously open all committed messages in a random ordering. Anonymity holds since no one can learn the ordering or the content of the client's committed message. We present <PERSON><PERSON>, the first system that provides a scalable and fully robust solution for anonymous committed broadcast. <PERSON><PERSON> maintains both properties of security (anonymity) and robustness (aka. 'guaranteed output delivery' or 'availability') in the face of a global active (malicious) adversary. Moreover, <PERSON><PERSON> is censorship resistant, that is, an honest client cannot be blocked from participating. <PERSON><PERSON> obtains its security and scalability by carefully combining classical and state-of-the-art techniques from the fields of anonymous communication and secure multiparty computation (MPC). Relying on MPC for such a system is beneficial since it naturally allows the parties (servers) to enforce some properties on accepted messages prior their publication. A GPU based implementation of <PERSON><PERSON> with 5 servers, which accepts 1 million clients, incurs a latency of less than 8 minutes; faster by a factor of $>100$ than the 3-servers Riposte protocol (S&P '15), which is not robust and not censorship resistant; we get an even larger factor when comparing to AsynchroMix and PowerMix (CCS '19), which are the only ones that guarantee fairness (or robustness in the online phase).", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417261"}, {"primary_key": "2549775", "vector": [], "sparse_vector": [], "title": "AISec&apos;20: 13th Workshop on Artificial Intelligence and Security.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent years have seen a dramatic increase in applications of Artificial Intelligence (AI), Machine Learning (ML), and data mining to security and privacy problems. The analytic tools and intelligent behavior provided by these techniques make AI and ML increasingly important for autonomous real-time analysis and decision making in domains with a wealth of data or that require quick reactions to constantly changing situations. The use of learning methods in security-sensitive domains, in which adversaries may attempt to mislead or evade intelligent machines, creates new frontiers for security research. The recent widespread adoption of \"deep learning'' techniques, whose security properties are difficult to reason about directly, has only added to the importance of this research. In addition, data mining and machine learning techniques create a wealth of privacy issues, due to the abundance and accessibility of data. The AISec workshop provides a venue for presenting and discussing new developments in the intersection of security and privacy with AI and machine learning.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416247"}, {"primary_key": "2549776", "vector": [], "sparse_vector": [], "title": "Game-Set-MATCH: Using Mobile Devices for Seamless External-Facing Biometric Matching.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We use biometrics like fingerprints and facial images to identify ourselves to our mobile devices and log on to applications everyday. Such authentication is internal-facing: we provide measurement on the same device where the template is stored. If our personal devices could participate in external-facing authentication too, where biometric measurement is captured by a nearby external sensor, then we could also enjoy a frictionless authentication experience in a variety of physical spaces like grocery stores, convention centers, ATMs, etc. The open setting of a physical space brings forth important privacy concerns though. We design a suite of secure protocols for external-facing authentication based on the cosine similarity metric which provide privacy for both user templates stored on their devices and the biometric measurement captured by external sensors in this open setting. The protocols provide different levels of security, ranging from passive security with some leakage to active security with no leakage at all. With the help of new packing techniques and zero-knowledge proofs for Paillier encryption -- and careful protocol design, our protocols achieve very practical performance numbers. For templates of length 256 with elements of size 16 bits each, our fastest protocol takes merely 0.024 seconds to compute a match, but even the slowest one takes no more than 0.12 seconds. The communication overhead of our protocols is very small too. The passive and actively secure protocols (with some leakage) need to exchange just 16.5KB and 27.8KB of data, respectively. The first message is designed to be reusable and, if sent in advance, would cut the overhead down to just 0.5KB and 0.8KB, respectively.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417287"}, {"primary_key": "2549779", "vector": [], "sparse_vector": [], "title": "Methodologies for Quantifying (Re-)randomization Security and Timing under JIT-ROP.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> (Daphne) Yao"], "summary": "Just-in-time return-oriented programming (JIT-ROP) allows one to dynamically discover instruction pages and launch code reuse attacks, effectively bypassing most fine-grained address space layout randomization (ASLR) protection. However, in-depth questions regarding the impact of code (re-)randomization on code reuse attacks have not been studied. For example, how would one compute the re-randomization interval effectively by considering the speed of gadget convergence to defeat JIT-ROP attacks? ; how do starting pointers in JIT-ROP impact gadget availability and gadget convergence time? ; what impact do fine-grained code randomizations have on the Turing-complete expressive power of JIT-ROP payloads? We conduct a comprehensive measurement study on the effectiveness of fine-grained code randomization schemes, with 5 tools, 20 applications including 6 browsers, 1 browser engine, and 25 dynamic libraries. We provide methodologies to measure JIT-ROP gadget availability, quality, and their Turing-complete expressiveness, as well as to empirically determine the upper bound of re-randomization intervals in re-randomization schemes using the Turing-complete (TC), priority, MOV TC, and payload gadget sets. Experiments show that the upper bound ranges from 1.5 to 3.5 seconds in our tested applications. Besides, our results show that locations of leaked pointers used in JIT-ROP attacks have no impacts on gadget availability but have an impact on how fast attackers find gadgets. Our results also show that instruction-level single-round randomization thwarts current gadget finding techniques under the JIT-ROP threat model.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417248"}, {"primary_key": "2549783", "vector": [], "sparse_vector": [], "title": "Deterministic Wallets in a Quantum World.", "authors": ["Nabil Alkeilani Alkadri", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Most blockchain solutions are susceptible to quantum attackers as they rely on cryptography that is known to be insecure in the presence of quantum adversaries. In this work we advance the study of quantum-resistant blockchain solutions by giving a quantum-resistant construction of a deterministic wallet scheme. Deterministic wallets are frequently used in practice in order to secure funds by storing the sensitive secret key on a so-called cold wallet that is not connected to the Internet. Recently, <PERSON> et al. (CCS'19) developed a formal model for the security analysis of deterministic wallets and proposed a generic construction from certain types of signature schemes that exhibit key rerandomization properties. We revisit the proposed classical construction in the presence of quantum adversaries and obtain the following results.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423361"}, {"primary_key": "2549784", "vector": [], "sparse_vector": [], "title": "Mnemosyne: An Effective and Efficient Postmortem Watering Hole Attack Investigation System.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Compromising a website that is routinely visited by employees of a targeted organization has become a popular technique for nation-state level adversaries to penetrate an enterprise's network. This technique, dubbed a \"watering hole\" attack, leverages a compromised website to serve as a stepping stone into the true victims' network. Despite watering hole attacks being one of the main techniques used by attackers to achieve the initial compromise stage of the cyber kill chain, there has been relatively little research related to detecting or investigating complex watering hole attacks. While there is existing work that seeks to detect malicious modifications made to an otherwise benign website, we argue that simply detecting that the website is compromised is only the first stage of the investigation. In this paper, we propose Mnemosyne, a postmortem forensic analysis engine that relies on browser-based attack provenance to accurately reconstruct, investigate, and assess the ramifications of watering hole attacks. Mnemosyne relies on a lightweight browser-modification-free auditing daemon to passively collect causality logs related to the browser's execution. Next, Mnemosyne applies a set of versioning techniques on top of these causality logs to precisely pinpoint when the website was compromised and what modifications were made by the adversary. Following this step, Mnemosyne relies on a novel user-level analysis to assess how the malicious modifications affected the targeted enterprise and seeks to identify exactly which employees fell victim to the attack. Throughout our extensive evaluation, we found that <PERSON>nemosyne's forensic analysis engine was able to identify the true victims in all seven real-world watering hole scenarios, while also reducing the amount of manual analysis required by the forensic analyst by 98.17% on average.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423355"}, {"primary_key": "2549785", "vector": [], "sparse_vector": [], "title": "Zombie Awakening: Stealthy Hijacking of Active Domains through DNS Hosting Referral.", "authors": ["<PERSON><PERSON><PERSON>", "Si<PERSON> Tang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In recent years, the security implication of stale NS records, which point to a nameserver that no longer resolves the domain, has been unveiled. Prior research studied the stale DNS records that point to expired domains. The popularity of DNS hosting services brings in a new category of stale NS records, which reside in the domain's zone (instead of the TLD zone) for an active domain. To the best of our knowledge, the security risk of this kind of stale NS record has never been studied before. In our research, we show that this new type of stale NS record can be practically exploited, causing a stealthier hijack of domains associated with the DNS hosting service. We also performed a large-scale analysis on over 1M high-profile domains, 17 DNS hosting providers and 12 popular public resolver operators to confirm the prevalence of this security risk. Our research further discovers 628 hijackable domains (e.g., 6 government entities and 2 payment services), 14 affected DNS hosting providers (e.g., Amazon Route 53), and 10 vulnerable public resolver operators (e.g., CloudFlare). Furthermore, we conducted an in-depth measurement analysis on them, thus providing a better understanding of this new security risk. Also, we explore the mitigation techniques that can be adopted by different affected parties.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417864"}, {"primary_key": "2549787", "vector": [], "sparse_vector": [], "title": "LadderLeak: Breaking ECDSA with Less than One Bit of Nonce Leakage.", "authors": ["Diego F. <PERSON>a", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Although it is one of the most popular signature schemes today, ECDSA presents a number of implementation pitfalls, in particular due to the very sensitive nature of the random value (known as the nonce) generated as part of the signing algorithm. It is known that any small amount of nonce exposure or nonce bias can in principle lead to a full key recovery: the key recovery is then a particular instance of <PERSON><PERSON> and <PERSON><PERSON><PERSON>'s hidden number problem (HNP). That observation has been practically exploited in many attacks in the literature, taking advantage of implementation defects or side-channel vulnerabilities in various concrete ECDSA implementations. However, most of the attacks so far have relied on at least 2 bits of nonce bias (except for the special case of curves at the 80-bit security level, for which attacks against 1-bit biases are known, albeit with a very high number of required signatures). In this paper, we uncover LadderLeak, a novel class of side-channel vulnerabilities in implementations of the Montgomery ladder used in ECDSA scalar multiplication. The vulnerability is in particular present in several recent versions of OpenSSL. However, it leaks less than 1 bit of information about the nonce, in the sense that it reveals the most significant bit of the nonce, but with probability <1. Exploiting such a mild leakage would be intractable using techniques present in the literature so far. However, we present a number of theoretical improvements of the Fourier analysis approach to solving the HNP (an approach originally due to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>), and this lets us practically break LadderLeak-vulnerable ECDSA implementations instantiated over the sect163r1 and NIST P-192 elliptic curves. In so doing, we achieve several significant computational records in practical attacks against the HNP.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417268"}, {"primary_key": "2549788", "vector": [], "sparse_vector": [], "title": "ProMACs: Progressive and Resynchronizing MACs for Continuous Efficient Authentication of Message Streams.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Efficiently integrity verification of received data requires Message Authentication Code (MAC) tags. However, while security calls for rather long tags, in many scenarios this contradicts other requirements. Examples are strict delay requirements (e.g., robot or drone control) or resource-scarce settings (e.g., LoRaWAN networks with limited battery capacity).", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423349"}, {"primary_key": "2549791", "vector": [], "sparse_vector": [], "title": "iDEA: Static Analysis on the Security of Apple Kernel Drivers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Drivers on Apple OSes (e.g., iOS, tvOS, iPadOS, macOS, etc.) run in the kernel space and driver vulnerabilities can incur serious security consequences. A recent report from Google Project Zero shows that driver vulnerabilities on Apple OSes have been actively exploited in the wild. Also, we observed that driver vulnerabilities have accounted for one-third of kernel bugs in recent iOS versions based on Apple's security updates. Despite the serious security implications, systematic static analysis on Apple drivers for finding security vulnerabilities has never been done before, not to mention any large-scale study of Apple drivers.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423357"}, {"primary_key": "2549793", "vector": [], "sparse_vector": [], "title": "Private Summation in the Multi-Message Shuffle Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The shuffle model of differential privacy (<PERSON><PERSON><PERSON><PERSON> et al. SODA 2019; <PERSON><PERSON> et al. EUROCRYPT 2019) and its close relative encode-shuffle-analyze (<PERSON><PERSON><PERSON> et al. SOSP 2017) provide a fertile middle ground between the well-known local and central models. Similarly to the local model, the shuffle model assumes an untrusted data collector who receives privatized messages from users, but in this case a secure shuffler is used to transmit messages from users to the collector in a way that hides which messages came from which user. An interesting feature of the shuffle model is that increasing the amount of messages sent by each user can lead to protocols with accuracies comparable to the ones achievable in the central model. In particular, for the problem of privately computing the sum of n bounded real values held by n different users, <PERSON><PERSON> et al. showed that O(sqrtn ) messages per user suffice to achieve O(1) error (the optimal rate in the central model), while <PERSON><PERSON> et al. (CRYPTO 2019) recently showed that a single message per user leads to Theta(n^1/3 ) MSE (mean squared error), a rate strictly in-between what is achievable in the local and central models. This paper introduces two new protocols for summation in the shuffle model with improved accuracy and communication trade-offs. Our first contribution is a recursive construction based on the protocol from <PERSON><PERSON> et al. mentioned above, providing poly(log log n) error with O(log log n) messages per user. The second contribution is a protocol with O(1) error and O(1) messages per user based on a novel analysis of the reduction from secure summation to shuffling introduced by Ishai et al. (FOCS 2006) (the original reduction required O(log n) messages per user). We also provide a numerical evaluation showing that our protocols provide good trade-offs between privacy, accuracy and communication for realistic values of n.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417242"}, {"primary_key": "2549794", "vector": [], "sparse_vector": [], "title": "Poking a Hole in the Wall: Efficient Censorship-Resistant Internet Communications by Parasitizing on WebRTC.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many censorship circumvention tools rely on trusted proxies that allow users within censored regions to access blocked Internet content by tunneling it through a covert channel (e.g,. piggybacking on Skype video calls). However, building tools that can simultaneously (i) provide good bandwidth capacity for accommodating the typical activities of Internet users, and (ii) be secure against traffic analysis attacks has remained an open problem and a stumbling block to the practical adoption of such tools for censorship evasion.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417874"}, {"primary_key": "2549795", "vector": [], "sparse_vector": [], "title": "Privaros: A Framework for Privacy-Compliant Delivery Drones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present Privaros, a framework to enforce privacy policies on drones. Privaros is designed for commercial delivery drones, such as the ones that will likely be used by Amazon Prime Air. Such drones visit various host airspaces, each of which may have different privacy requirements. Privaros uses mandatory access control to enforce the policies of these hosts on guest delivery drones. Privaros is tailored for ROS, a middleware popular in many drone platforms. This paper presents the design and implementation of <PERSON>rivaro<PERSON>'s policy-enforcement mechanisms, describes how policies are specified, and shows that policy specification can be integrated with India's Digital Sky portal. Our evaluation shows that a drone running Privaros can robustly enforce various privacy policies specified by hosts, and that its core mechanisms only marginally increase communication latency and power consumption.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417858"}, {"primary_key": "2549796", "vector": [], "sparse_vector": [], "title": "Analyzing Information Leakage of Updates to Natural Language Models.", "authors": ["Santiago Zanella-Béguelin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "To continuously improve quality and reflect changes in data, machine learning applications have to regularly retrain and update their core models. We show that a differential analysis of language model snapshots before and after an update can reveal a surprising amount of detailed information about changes in the training data. We propose two new metrics---differential score and differential rank---for analyzing the leakage due to updates of natural language models. We perform leakage analysis using these metrics across models trained on several different datasets using different methods and configurations. We discuss the privacy implications of our findings, propose mitigation strategies and evaluate their effect.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417880"}, {"primary_key": "2549797", "vector": [], "sparse_vector": [], "title": "Secure Single-Server Aggregation with (Poly)Logarithmic Overhead.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Secure aggregation is a cryptographic primitive that enables a server to learn the sum of the vector inputs of many clients. <PERSON><PERSON><PERSON> et al. (CCS 2017) presented a construction that incurs computation and communication for each client linear in the number of parties. While this functionality enables a broad range of privacy preserving computational tasks, scaling concerns limit its scope of use. We present the first constructions for secure aggregation that achieve polylogarithmic communication and computation per client. Our constructions provide security in the semi-honest and the semi-malicious settings where the adversary controls the server and a δ-fraction of the clients, and correctness with up to δ-fraction dropouts among the clients. Our constructions show how to replace the complete communication graph of <PERSON><PERSON><PERSON> et al., which entails the linear overheads, with a k-regular graph of logarithmic degree while maintaining the security guarantees. Beyond improving the known asymptotics for secure aggregation, our constructions also achieve very efficient concrete parameters. The semi-honest secure aggregation can handle a billion clients at the per-client cost of the protocol of <PERSON><PERSON><PERSON> et al. for a thousand clients. In the semi-malicious setting with 10 4 clients, each client needs to communicate only with 3% of the clients to have a guarantee that its input has been added together with the inputs of at least 5000 other clients, while withstanding up to 5% corrupt clients and 5% dropouts. We also show an application of secure aggregation to the task of secure shuffling which enables the first cryptographically secure instantiation of the shuffle model of differential privacy.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417885"}, {"primary_key": "2549800", "vector": [], "sparse_vector": [], "title": "Ligero++: A New Optimized Sublinear IOP.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Tiancheng Xie", "<PERSON><PERSON><PERSON>"], "summary": "This paper follows the line of works that design concretely efficient transparent sublinear zero-knowledge Interactive Oracle Proofs (IOP). Arguments obtained via this paradigm have the advantages of not relying on public-key cryptography, not requiring a trusted setup, and resistance to known quantum attacks. In the realm of transparent systems, Ligero and Aurora stand out with incomparable advantages where the former has a fast prover algorithm somewhat succinct proofs and the latter has somewhat fast prover and succinct proofs. In this work, we introduce Ligero++ that combines the best features of both approaches to achieve the best of both worlds. We implement our protocol and benchmark the results.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417893"}, {"primary_key": "2549807", "vector": [], "sparse_vector": [], "title": "Security Analysis and Implementation of Relay-Resistant Contactless Payments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Contactless systems, such as the EMV (Europay, Mastercard and Visa) payment protocol, are vulnerable to relay attacks. The typical countermeasure to this relies on distance bounding protocols, in which a reader estimates an upper bound on its physical distance from a card by doing round-trip time (RTT) measurements. However, these protocols are trivially broken in the presence of rogue readers. At Financial Crypto 2019, we proposed two novel EMV-based relay-resistant protocols: they integrate distance-bounding with the use of hardware roots of trust (HWRoT) in such a way that correct RTT-measurements can no longer be bypassed.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417235"}, {"primary_key": "2549811", "vector": [], "sparse_vector": [], "title": "Threshold Password-Hardened Encryption Services.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Password-hardened encryption (PHE) was introduced by <PERSON> et al. at USENIX 2018 and immediately productized by VirgilSecurity. PHE is a password-based key derivation protocol that involves an oblivious external crypto service for key derivation. The security of PHE protects against offline brute-force attacks, even when the attacker is given the entire database. Furthermore, the crypto service neither learns the derived key nor the password. PHE supports key-rotation meaning that both the server and crypto service can update their keys without involving the user. While PHE significantly strengthens data security, it introduces a single point of failure because key-derivation always requires access to the crypto service. In this work, we address this issue and simultaneously increase security by introducing threshold password-hardened encryption. Our formalization of this primitive revealed shortcomings of the original PHE definition that we also address in this work. Following the spirit of prior works, we give a simple and efficient construction using lightweight tools only. We also implement our construction and evaluate its efficiency. Our experiments confirm the practical efficiency of our scheme and show that it is more efficient than common memory-hard functions, such as scrypt. From a practical perspective this means that threshold PHE can be used as an alternative to scrypt for password protection and key-derivation, offering better security in terms of offline brute force attacks.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417266"}, {"primary_key": "2549816", "vector": [], "sparse_vector": [], "title": "TrafficSliver: Fighting Website Fingerprinting Attacks with Traffic Splitting.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Website fingerprinting (WFP) aims to infer information about the content of encrypted and anonymized connections by observing patterns of data flows based on the size and direction of packets. By collecting traffic traces at a malicious Tor entry node --- one of the weakest adversaries in the attacker model of Tor --- a passive eavesdropper can leverage the captured meta-data to reveal the websites visited by a Tor user. As recently shown, WFP is significantly more effective and realistic than assumed. Concurrently, former WFP defenses are either infeasible for deployment in real-world settings or defend against specific WFP attacks only.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423351"}, {"primary_key": "2549818", "vector": [], "sparse_vector": [], "title": "Impersonation-as-a-Service: Characterizing the Emerging Criminal Infrastructure for User Impersonation at Scale.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper we provide evidence of an emerging criminal infrastructure enabling impersonation attacks at scale. Impersonation-as-a-Service (IMPaaS) allows attackers to systematically collect and enforce user profiles (consisting of user credentials, cookies, device and behavioural fingerprints, and other metadata) to circumvent risk-based authentication system and effectively bypass multi-factor authentication mechanisms. We present the IMPaaS model and evaluate its implementation by analysing the operation of a large, invite-only, Russian IMPaaS platform providing user profiles for more than 260,000 Internet users worldwide. Our findings suggest that the IMPaaS model is growing, and provides the mechanisms needed to systematically evade authentication controls across multiple platforms, while providing attackers with a reliable, up-to-date, and semi-automated environment enabling target selection and user impersonation against Internet users as scale.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417892"}, {"primary_key": "2549820", "vector": [], "sparse_vector": [], "title": "UC Non-Interactive, Proactive, Threshold ECDSA with Identifiable Aborts.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Building on the Gennaro & Goldfeder and Lindell & Nof protocols (CCS '18), we present two threshold ECDSA protocols, for any number of signatories and any threshold, that improve as follows over the state of the art: -- For both protocols, only the last round requires knowledge of the message, and the other rounds can take place in a preprocessing stage, lending to a non-interactive threshold ECDSA protocol. -- Both protocols withstand adaptive corruption of signatories. Furthermore, they include a periodic refresh mechanism and offer full proactive security. -- Both protocols realize an ideal threshold signature functionality within the UC framework, in the global random oracle model, assuming Strong RSA, DDH, semantic security of the Paillier encryption, and a somewhat enhanced variant of existential unforgeability of ECDSA. -- Both protocols achieve accountability by identifying corrupted parties in case of failure to generate a valid signature. The two protocols are distinguished by the round-complexity and the identification process for detecting cheating parties. Namely: -- For the first protocol, signature generation takes only 4 rounds (down from the current state of the art of 8 rounds), but the identification process requires computation and communication that is quadratic in the number of parties. -- For the second protocol, the identification process requires computation and communication that is only linear in the number of parties, but signature generation takes 7 rounds. These properties (low latency, compatibility with cold-wallet architectures, proactive security, identifiable abort and composable security) make the two protocols ideal for threshold wallets for ECDSA-based cryptocurrencies.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423367"}, {"primary_key": "2549823", "vector": [], "sparse_vector": [], "title": "ASHES 2020: 4th Workshop on Attacks and Solutions in Hardware Security.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The workshop on \"Attacks and Solutions in HardwarE Security\"(ASHES) welcomes any theoretical and practical works on hardware security, including attacks, solutions, countermeasures, proofs, classification, formalization, and implementations. Besides mainstream research, ASHES puts some focus on new and emerging scenarios: This includes the internet of things (IoT), nuclear weapons inspections, arms control, consumer and infrastructure security, or supply chain security, among others. ASHES also welcomes dedicated works on special purpose hardware, such as lightweight, low-cost, and energy-efficient devices, or non-electronic security systems. The workshop hosts four different paper categories: Apart from regular and short papers, this includes works that systematize and structure a certain (sub-)area (so-called \"Systematization of Knowledge\" (SoK) papers), and so-termed \"Wild and Crazy\" (WaC) papers, which distribute seminal ideas at an early conceptual stage. This summary gives a brief overview of the fourth edition of the workshop, which will take place virtually on November 13, 2020, as a post-conference satellite workshop of ACM CCS.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416249"}, {"primary_key": "2549825", "vector": [], "sparse_vector": [], "title": "The Signal Private Group System and Anonymous Credentials Supporting Efficient Verifiable Encryption.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we present a system for maintaining a membership list of users in a group, designed for use in the Signal Messenger secure messaging app. The goal is to support private groups where membership information is readily available to all group members but hidden from the service provider or anyone outside the group. In the proposed solution, a central server stores the group membership in the form of encrypted entries. Members of the group authenticate to the server in a way that reveals only that they correspond to some encrypted entry, then read and write the encrypted entries.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417887"}, {"primary_key": "2549827", "vector": [], "sparse_vector": [], "title": "A Systematic Study of Elastic Objects in Kernel Exploitation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent research has proposed various methods to perform kernel exploitation and bypass kernel protection. For example, security researchers have demonstrated an exploitation method that utilizes the characteristic of elastic kernel objects to bypass KASLR, disclose stack/heap cookies, and even perform arbitrary read in the kernel. While this exploitation method is considered a commonly adopted approach to disclosing critical kernel information, there is no evidence indicating a strong need for developing a new defense mechanism to limit this exploitation method. It is because the effectiveness of this exploitation method is demonstrated only on anecdotal kernel vulnerabilities. It is unclear whether such a method is useful for a majority of kernel vulnerabilities.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423353"}, {"primary_key": "2549829", "vector": [], "sparse_vector": [], "title": "GAN-Leaks: A Taxonomy of Membership Inference Attacks against Generative Models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Deep learning has achieved overwhelming success, spanning from discriminative models to generative models. In particular, deep generative models have facilitated a new level of performance in a myriad of areas, ranging from media manipulation to sanitized dataset generation. Despite the great success, the potential risks of privacy breach caused by generative models have not been analyzed systematically. In this paper, we focus on membership inference attack against deep generative models that reveals information about the training data used for victim models. Specifically, we present the first taxonomy of membership inference attacks, encompassing not only existing attacks but also our novel ones. In addition, we propose the first generic attack model that can be instantiated in a large range of settings and is applicable to various kinds of deep generative models. Moreover, we provide a theoretically grounded attack calibration technique, which consistently boosts the attack performance in all cases, across different attack settings, data modalities, and training configurations. We complement the systematic analysis of attack performance by a comprehensive experimental study, that investigates the effectiveness of various attacks w.r.t. model type and training configurations, over three diverse application scenarios (i.e., images, medical data, and location data).", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417238"}, {"primary_key": "2549830", "vector": [], "sparse_vector": [], "title": "Dangerous Skills Got Certified: Measuring the Trustworthiness of Skill Certification in Voice Personal Assistant Platforms.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Hong<PERSON> Hu"], "summary": "With the emergence of the voice personal assistant (VPA) ecosystem, third-party developers are allowed to build new voice-apps are called skills in the Amazon Alexa platform and actions in the Google Assistant platform, respectively. For the sake of brevity, we use the term skills to describe voice-apps including Amazon skills and Google actions, unless we need to distinguish them for different VPA platforms. and publish them to the skills store, which greatly extends the functionalities of VPAs. Before a new skill becomes publicly available, that skill must pass a certification process, which verifies that it meets the necessary content and privacy policies. The trustworthiness of skill certification is of significant importance to platform providers, developers, and end users. Yet, little is known about how difficult it is for a policy-violating skill to get certified and published in VPA platforms. In this work, we study the trustworthiness of the skill certification in Amazon Alexa and Google Assistant platforms to answer three key questions: 1) Whether the skill certification process is trustworthy in terms of catching policy violations in third-party skills. 2) Whether there exist policy-violating skills published in their skills stores. 3) What are VPA users' perspectives on the skill certification and their vulnerable usage behavior when interacting with VPA devices? Over a span of 15 months, we crafted and submitted for certification 234 Amazon Alexa skills and 381 Google Assistant actions that intentionally violate content and privacy policies specified by VPA platforms. Surprisingly, we successfully got 234 (100%) policy-violating Alexa skills certified and 148 (39%) policy-violating Google actions certified. Our analysis demonstrates that policy-violating skills exist in the current skills stores, and thus users (children, in particular) are at risk when using VPA services. We conducted a user study with 203 participants to understand users' misplaced trust on VPA platforms. Unfortunately, user expectations are not being met by the skill certification in leading VPA platforms.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423339"}, {"primary_key": "2549831", "vector": [], "sparse_vector": [], "title": "Cyber-Physical Inconsistency Vulnerability Identification for Safety Checks in Robotic Vehicles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new type of vulnerability for Robotic Vehicles (RVs), called Cyber-Physical Inconsistency. These vulnerabilities target safety checks in RVs (e.g., crash detection). They can be exploited by setting up malicious environment conditions such as placing an obstacle with a certain weight and a certain angle in the RV's trajectory. Once exploited, the safety checks may fail to report real physical accidents or report false alarms (while the RV is still operating normally). Both situations could lead to life-threatening consequences. The root cause of such vulnerabilities is that existing safety checks are mostly using simple range checks implemented in general-purpose programming languages, which are incapable of describing the complex and delicate physical world. We develop a novel technique that requires the interplay of program analysis, vehicle modeling, and search-based testing to identify such vulnerabilities. Our experiment on 4 real-world control software and 8 vehicles including quadrotors, rover, and fixed-wing airplane has discovered 10 real vulnerabilities. Our technique does not have false positives as it only reports when an exploit can be generated.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417249"}, {"primary_key": "2549832", "vector": [], "sparse_vector": [], "title": "TEMPEST Comeback: A Realistic Audio Eavesdropping Threat on Mixed-signal SoCs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This study presents a new TEMPEST threat that an attacker can surreptitiously obtain original plain audio information from a distance by exploiting recently emerging unintentional electromagnetic (EM) radiations. As lightweight sensor-based Internet of things (IoT) services become widespread, a mixed-signal system on chip (MSoC) spontaneously integrates all components, such as digital, analog, and even power circuits, into a single chipset to minimize the size of IoT devices. Accordingly, we pay attention to the accelerated integration of a switching regulator (SWREG), which is one of the typical power circuits and may substantially increase the unintentional EM leakages, re-enabling the audio TEMPEST attack.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417241"}, {"primary_key": "2549835", "vector": [], "sparse_vector": [], "title": "Oracle Simulation: A Technique for Protocol Composition with Long Term Shared Secrets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We provide a composition framework together with a variety of composition theorems allowing to split the security proof of an unbounded number of sessions of a compound protocol into simpler goals. While many proof techniques could be used to prove the subgoals, our model is particularly well suited to the Computationally Complete Symbolic Attacker (ccsA) model.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417229"}, {"primary_key": "2549836", "vector": [], "sparse_vector": [], "title": "Usage Patterns of Privacy-Enhancing Technologies.", "authors": ["Kovila P. L. <PERSON>"], "summary": "The steady reports of privacy invasions online paints a picture of the Internet growing into a more dangerous place. This is supported by reports of the potential scale for online harms facilitated by the mass deployment of online technology and by the data-intensive web. While Internet users often express concern about privacy, some report taking actions to protect their privacy online. We investigate the methods and technologies that individuals employ to protect their privacy online. We conduct two studies, of N=180 and N=907, to elicit individuals' use of privacy methods, within the US, the UK and Germany. We find that non-technology methods are among the most used methods in the three countries. We identify distinct groupings of privacy methods usage in a cluster map. The map shows that together with non-technology methods of privacy protection, simple PETs that are integrated in services, form the most used cluster, whereas more advanced PETs form a different, least used cluster. We further investigate user perception and reasoning for mostly using one set of PETs, in a third study with N=183 participants. We do not find a difference in perceived competency in protecting privacy online between advanced and simpler PETs users. We compare use perceptions between advanced and simpler PETs and report on user reasoning for not using advanced PETs, as well as support needed for potential use. This paper contributes to privacy research by eliciting use and perception of use across 43 privacy methods, including 26 PETs across three countries and provides a map of PETs usage. The cluster map provides a systematic and reliable point of reference for future user-centric investigations across PETs. Overall, this research provides a broad understanding of use and perceptions across a collection of PETs, and can lead to future research for scaling use of PETs.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423347"}, {"primary_key": "2549837", "vector": [], "sparse_vector": [], "title": "Clone Detection in Secure Messaging: Improving Post-Compromise Security in Practice.", "authors": ["Cas Cremers", "<PERSON><PERSON>", "<PERSON>", "Aurora Naska"], "summary": "We investigate whether modern messaging apps achieve the strong post-compromise security guarantees offered by their underlying protocols. In particular, we perform a black-box experiment in which a user becomes the victim of a clone attack; in this attack, the user's full state (including identity keys) is compromised by an attacker who clones their device and then later attempts to impersonate them, using the app through its user interface.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423354"}, {"primary_key": "2549838", "vector": [], "sparse_vector": [], "title": "TPDP&apos;20: 6th Workshop on Theory and Practice of Differential Privacy.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Differential privacy is a rigorous mathematical model of privacy protection that has been the subject of deep theoretical research and also been deployed in real-world systems. This workshop aims to bring together a diverse array of researchers and practitioners to provoke stimulating discussion about the current state of differential privacy, in theory and practice. TPDP aims to be an inclusive forum that seeks to grow and diversify the differential privacy community.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416253"}, {"primary_key": "2549839", "vector": [], "sparse_vector": [], "title": "Everything is a <PERSON> and Nakamoto Always Wins.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> invented the longest chain protocol, and claimed its security by analyzing the private double-spend attack, a race between the adversary and the honest nodes to grow a longer chain. But is it the worst attack? We answer the question in the affirmative for three classes of longest chain protocols, designed for different consensus models: 1) <PERSON><PERSON><PERSON>'s original Proof-of-Work protocol; 2) Ouroboros and SnowWhite Proof-of-Stake protocols; 3) Chia Proof-of-Space protocol. As a consequence, exact characterization of the maximum tolerable adversary power is obtained for each protocol as a function of the average block time normalized by the network delay. The security analysis of these protocols is performed in a unified manner by a novel method of reducing all attacks to a race between the adversary and the honest nodes.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417290"}, {"primary_key": "2549842", "vector": [], "sparse_vector": [], "title": "The Cookie Hunter: Automated Black-box Auditing for Web Authentication and Authorization Flaws.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we focus on authentication and authorization flaws in web apps that enable partial or full access to user accounts. Specifically, we develop a novel fully automated black-box auditing framework that analyzes web apps by exploring their susceptibility to various cookie-hijacking attacks while also assessing their deployment of pertinent security mechanisms (e.g., HSTS). Our modular framework is driven by a custom browser automation tool developed to transparently offer fault-tolerance during extended interactions with web apps. We use our framework to conduct the first automated large-scale study of cookie-based account hijacking in the wild. As our framework handles every step of the auditing process in a completely automated manner, including the challenging process of account creation, we are able to fully audit 25K domains. Our framework detects more than 10K domains that expose authentication cookies over unencrypted connections, and over 5K domains that do not protect authentication cookies from JavaScript access while also embedding third party scripts that execute in the first party's origin. Our system also automatically identifies the privacy loss caused by exposed cookies and detects 9,324 domains where sensitive user data can be accessed by attackers (e.g., address, phone number, password). Overall, our study demonstrates that cookie-hijacking is a severe and prevalent threat, as deployment of even basic countermeasures (e.g., cookie security flags) is absent or incomplete, while developers struggle to correctly deploy more demanding mechanisms.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417869"}, {"primary_key": "2549848", "vector": [], "sparse_vector": [], "title": "Devil is Virtual: Reversing Virtual Inheritance in C++ Binaries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The complexities that arise from the implementation of object-oriented concepts in C++ such as virtual dispatch and dynamic type casting have attracted the attention of attackers and defenders alike. Binary-level defenses are dependent on full and precise recovery of class inheritance tree of a given program. While current solutions focus on recovering single and multiple inheritances from the binary, they are oblivious of virtual inheritance. The conventional wisdom among binary-level defenses is that virtual inheritance is uncommon and/or support for single and multiple inheritances provides implicit support for virtual inheritance. In this paper, we show neither to be true. Specifically, (1) we present an efficient technique to detect virtual inheritance in C++ binaries and show through a study that virtual inheritance can be found in non-negligible number (more than 10% on Linux and 12.5% on Windows) of real-world C++ programs including Mysql and Libstdc++. (2) We show that failure to handle virtual inheritance introduces both false positives and false negatives in the hierarchy tree. These falses either introduce attack surface when the hierarchy recovered is used to enforce CFI policies, or make the hierarchy difficult to understand when it is needed for program understanding (e.g., during decompilation). (3) We present a solution to recover virtual inheritance from COTS binaries. We recover a maximum of 95% and 95.5% (GCC -O0) and a minimum of 77.5% and 73.8% (Clang -O2) of virtual and intermediate bases respectively in the virtual inheritance tree.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417251"}, {"primary_key": "2549849", "vector": [], "sparse_vector": [], "title": "Full Database Reconstruction in Two Dimensions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the past few years, we have seen multiple attacks on one-dimensional databases that support range queries. These attacks achieve full database reconstruction by exploiting access pattern leakage along with known query distribution or search pattern leakage. We are the first to go beyond one dimension, exploring this threat in two dimensions. We unveil an intrinsic limitation of reconstruction attacks by showing that there can be an exponential number of distinct databases that produce equivalent leakage. Next, we present a full database reconstruction attack. Our algorithm runs in polynomial time and returns a poly-size encoding of all databases consistent with the given leakage profile. We implement our algorithm and observe real-world databases that admit a large number of equivalent databases, which aligns with our theoretical results.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417275"}, {"primary_key": "2549851", "vector": [], "sparse_vector": [], "title": "Off-Path TCP Exploits of the Mixed IPID Assignment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we uncover a new off-path TCP hijacking attack that can be used to terminate victim TCP connections or inject forged data into victim TCP connections by manipulating the new mixed IPID assignment method, which is widely used in Linux kernel version 4.18 and beyond to help defend against TCP hijacking attacks. The attack has three steps. First, an off-path attacker can downgrade the IPID assignment for TCP packets from the more secure per-socket-based policy to the less secure hash-based policy, building a shared IPID counter that forms a side channel on the victim. Second, the attacker detects the presence of TCP connections by observing the shared IPID counter on the victim. Third, the attacker infers the sequence number and the acknowledgment number of the detected connection by observing the side channel of the shared IPID counter. Consequently, the attacker can completely hijack the connection, i.e., resetting the connection or poisoning the data stream. We evaluate the impacts of this off-path TCP attack in the real world. Our case studies of SSH DoS, manipulating web traffic, and poisoning BGP routing tables show its threat on a wide range of applications. Our experimental results show that our off-path TCP attack can be constructed within 215 seconds and the success rate is over 88%. Finally, we analyze the root cause of the exploit and develop a new IPID assignment method to defeat this attack. We prototype our defense in Linux 4.18 and confirm its effectiveness through extensive evaluation over real applications on the Internet.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417884"}, {"primary_key": "2549853", "vector": [], "sparse_vector": [], "title": "Asynchronous Remote Key Generation: An Analysis of Yubico&apos;s Proposal for W3C WebAuthn.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "WebAuthn, forming part of FIDO2, is a W3C standard for strong authentication, which employs digital signatures to authenticate web users whilst preserving their privacy. Owned by users, WebAuthn authenticators generate attested and unlinkable public-key credentials for each web service to authenticate users. Since the loss of authenticators prevents users from accessing web services, usable recovery solutions preserving the original WebAuthn design choices and security objectives are urgently needed. We examine <PERSON><PERSON><PERSON>'s recent proposal for recovering from the loss of a WebAuthn authenticator by using a secondary backup authenticator. We analyse the cryptographic core of their proposal by modelling a new primitive, called Asynchronous Remote Key Generation (ARKG), which allows some primary authenticator to generate unlinkable public keys for which the backup authenticator may later recover corresponding private keys. Both processes occur asynchronously without the need for authenticators to export or share secrets, adhering to WebAuthn's attestation requirements. We prove that <PERSON><PERSON><PERSON>'s proposal achieves our ARKG security properties under the discrete logarithm and PRF-ODH assumptions in the random oracle model. To prove that recovered private keys can be used securely by other cryptographic schemes, such as digital signatures or encryption schemes, we model compositional security of ARKG using composable games by <PERSON><PERSON><PERSON><PERSON> et al. (ACM CCS 2011), extended to the case of arbitrary public-key protocols. As well as being more general, our results show that private keys generated by ARKG may be used securely to produce unforgeable signatures for challenge-response protocols, as used in WebAuthn. We conclude our analysis by discussing concrete instantiations behind Yubico's ARKG protocol, its integration with the WebAuthn standard, performance, and usability aspects.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417292"}, {"primary_key": "2549854", "vector": [], "sparse_vector": [], "title": "Continuous and Multiregional Monitoring of Malicious Hosts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The number of cybersecurity threats has been increasing, and these threats have become more sophisticated year after year. Malicious hosts play a large role in modern cyberattacks, e.g., as a launcher of remote-control attacks or as a receiver of stolen information. In such circumstances, continuous monitoring of malicious hosts (URL/IP addresses) is indispensable to reveal cyberattack activities, and many studies have been conducted on that. However, many of them have limitations: they help only in the short-term or they help only a few regions and/or a few organizations. Therefore, we cannot effectively monitor attacks that are active for only a short time or that change their behavior depending on where the victims are from (e.g., country/organization). In this paper, we propose Stargazer, a program that monitors malicious hosts from multiple points on a long-term basis. Multiregional monitoring sensors and inter-organizational collaboration are conducted to achieve this surveillance. In this paper, we describe an implementation of the Stargazer prototype and how monitoring was carried out using multiregional sensors starting in Dec. 2018 of 1,050 malicious hosts; 10,929,418 measurements were obtained. Case studies on (1) revived hosts, (2) hosts that only respond to specific regions, and (3) the behavior of attack preparation were created.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420018"}, {"primary_key": "2549858", "vector": [], "sparse_vector": [], "title": "Tight Consistency Bounds for Bitcoin.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We establish the optimal security threshold for the Bitcoin protocol in terms of adversarial hashing power, honest hashing power, and network delays. Specifically, we prove that the protocol is secure if [ra < 1/Δ0 + 1/rh,,] where rh is the expected number of honest proof-of-work successes in unit time, ra is the expected number of adversarial successes, and no message is delayed by more than Δ0 time units. In this regime, the protocol guarantees consistency and liveness with exponentially decaying failure probabilities. Outside this region, the simple private chain attack prevents consensus. Our analysis immediately applies to any Nakamoto-style proof-of-work protocol; in the full version of this paper we also present the adaptations needed to apply it in the proof-of-stake setting, establishing a similar threshold there.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423365"}, {"primary_key": "2549859", "vector": [], "sparse_vector": [], "title": "2nd Workshop on Cyber-Security Arms Race (CYSARM 2020).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The goal of CYSARM workshop is to foster collaboration among researchers and practitioners to discuss the various facets and trade-offs of cyber-security. In particular, how new technologies and algorithms might impact the cyber-security of existing or future models and systems.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416250"}, {"primary_key": "2549862", "vector": [], "sparse_vector": [], "title": "Speculative Probing: Hacking Blind in the Spectre Era.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To defeat ASLR or more advanced fine-grained and leakage-resistant code randomization schemes, modern software exploits rely on information disclosure to locate gadgets inside the victim's code. In the absence of such info-leak vulnerabilities, attackers can still hack blind and derandomize the address space by repeatedly probing the victim's memory while observing crash side effects, but doing so is only feasible for crash-resistant programs. However, high-value targets such as the Linux kernel are not crash-resistant. Moreover, the anomalously large number of crashes is often easily detectable. In this paper, we show that the Spectre era enables an attacker armed with a single memory corruption vulnerability to hack blind without triggering any crashes. Using speculative execution for crash suppression allows the elevation of basic memory write vulnerabilities into powerful speculative probing primitives that leak through microarchitectural side effects. Such primitives can repeatedly probe victim memory and break strong randomization schemes without crashes and bypass all deployed mitigations against Spectre-like attacks. The key idea behind speculative probing is to break Spectre mitigations using memory corruption and resurrect Spectre-style disclosure primitives to mount practical blind software exploits. To showcase speculative probing, we target the Linux kernel, a crash-sensitive victim that has so far been out of reach of blind attacks, mount end-to-end exploits that compromise the system with just-in-time code reuse and data-only attacks from a single memory write vulnerability, and bypass strong Spectre and strong randomization defenses. Our results show that it is crucial to consider synergies between different (Spectre vs. code reuse) threat models to fully comprehend the attack surface of modern systems.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417289"}, {"primary_key": "2549864", "vector": [], "sparse_vector": [], "title": "Pointproofs: Aggregating Proofs for Multiple Vector Commitments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Vector commitments enable a user to commit to a sequence of values and provably reveal one or many values at specific posi- tions at a later time. In this work, we construct Pointproofs? a new vector commitment scheme that supports non-interactive aggregation of proofs across multiple commitments. Our construction enables any third party to aggregate a collection of proofs with respect to different, independently computed commitments into a single proof represented by an elliptic curve point of 48-bytes. In addition, our scheme is hiding: a commitment and proofs for some values reveal no information about the remaining values. We build Pointproofs and demonstrate how to apply them to blockchain smart contracts. In our example application, Pointproofs reduce bandwidth overheads for propagating a block of transactions by at least 60% compared to prior state- of-art vector commitments. Pointproofs are also efficient: on a single-thread, it takes 0.08 seconds to generate a proof for 8 values with respect to one commitment, 0.25 seconds to aggregate 4000 such proofs across multiple commitments into one proof, and 23 seconds (0.7 ms per value proven) to verify the aggregated proof.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417244"}, {"primary_key": "2549865", "vector": [], "sparse_vector": [], "title": "Examining Mirai&apos;s Battle over the Internet of Things.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Using hundreds of thousands of compromised IoT devices, the Mirai botnet emerged in late 2016 as a game changing threat actor, capable of temporarily taking down major Internet service providers and Internet infrastructure. Since then, dozens of variants of IoT-based botnets have sprung up, and in today's Internet distributed denial-of-service attacks from IoT devices have become a major attack vector. This proliferation was significantly driven by the public distribution of the Mirai source code, which other actors used to create their own, customized version of the original Mirai botnet. In this paper we provide a comprehensive view into the ongoing battle over the Internet of Things fought by Mirai and its many siblings. Using 7,500 IoT honeypots, we show that we can use 300,000,000 compromisation attempts from infected IoT devices as well as a design flaw in Mirai's random number generator to obtain insights into Mirai infections worldwide. We find that networks and the particular malware strains that plague them are tightly connected, and malware authors over time take over strategies from their competitors. The most surprising finding is that epidemiologically, IoT botnets are not self-sustaining: were it not for continuous pushes from bootstrapping, Mirai and its variants would die out.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417277"}, {"primary_key": "2549866", "vector": [], "sparse_vector": [], "title": "InSpectre: Breaking and Fixing Microarchitectural Vulnerabilities by Formal Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Mads Dam"], "summary": "The recent Spectre attacks have demonstrated the fundamental insecurity of current computer microarchitecture. The attacks use features like pipelining, out-of-order and speculation to extract arbitrary information about the memory contents of a process. A comprehensive formal microarchitectural model capable of representing the forms of out-of-order and speculative behavior that can meaningfully be implemented in a high performance pipelined architecture has not yet emerged. Such a model would be very useful, as it would allow the existence and non-existence of vulnerabilities, and soundness of countermeasures to be formally established. This paper presents such a model targeting single core processors. The model is intentionally very general and provides an infrastructure to define models of real CPUs. It incorporates microarchitectural features that underpin all known Spectre vulnerabilities. We use the model to elucidate the security of existing and new vulnerabilities, as well as to formally analyze the effectiveness of proposed countermeasures. Specifically, we discover three new (potential) vulnerabilities, including a new variant of Spectre v4, a vulnerability on speculative fetching, and a vulnerability on out-of-order execution, and analyze the effectiveness of existing countermeasures including constant time and serializing instructions.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417246"}, {"primary_key": "2549868", "vector": [], "sparse_vector": [], "title": "Dumbo: Faster Asynchronous BFT Protocols.", "authors": ["<PERSON><PERSON><PERSON>", "Zhenliang Lu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "HoneyBadgerBFT, proposed by <PERSON> et al. [34] as the first practical asynchronous atomic broadcast protocol, demonstrated impressive performance. The core of HoneyBadgerBFT (HB-BFT) is to achieve batching consensus using asynchronous common subset protocol (ACS) of <PERSON><PERSON><PERSON> et al., constituted with n reliable broadcast protocol (RBC) to have each node propose its input, followed by n asynchronous binary agreement protocol (ABA) to make a decision for each proposed value (n is the total number of nodes).", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417262"}, {"primary_key": "2549870", "vector": [], "sparse_vector": [], "title": "FEAST&apos;20: Fifth Workshop on Forming an Ecosystem Around Software Transformation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The Fifth Workshop on Forming an Ecosystem Around Software Transformation (FEAST) provides a forum for presentation and discussion of new tools, methodologies, and techniques facilitating the automated or semi-automated transformation and analysis of software executables for improving their security and efficiency without the benefit of any original source code whence they were developed. Late-stage software customization of this form is of particular benefit to security-conscious software consumers who must use closed-source or source-free binary software components in mission-critical settings, or who must harden software against newly emerging attacks not anticipated during the software's original design and development. However, code analysis and transformation becomes much more difficult without the aid of source-level information to provide a context for its intended operation. This outstanding challenge motivates the FEAST Workshop's goal of forming a robust ecosystem of strategies and tools for accomplishing source-free binary code transformation reliably and on-demand.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416251"}, {"primary_key": "2549873", "vector": [], "sparse_vector": [], "title": "Voice-Indistinguishability - Protecting Voiceprint with Differential Privacy under an Untrusted Server.", "authors": ["Yaowei Han", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rising adoption of advanced voice-based technology together with increasing consumer demand for smart devices, voice-controlled \"virtual assistants\" such as Apple's Siri and Google Assistant have been integrated into people's daily lives. However, privacy and security concerns may hinder the development of such voice-based applications since speech data contain the speaker's biometric identifier, i.e., voiceprint (as analogous to fingerprint). To alleviate privacy concerns in speech data collection, we propose a fast speech data de-identification system that allows a user to share her speech data with formal privacy guarantee to an untrusted server. Our open-sourced system can be easily integrated into other speech processing systems for collecting users' voice data in a privacy-preserving way. Experiments on public datasets verify the effectiveness and efficiency of the proposed system.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420025"}, {"primary_key": "2549876", "vector": [], "sparse_vector": [], "title": "Déjà Vu: Side-Channel Analysis of Mozilla&apos;s NSS.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Jesús<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent work on Side Channel Analysis (SCA) targets old, well-known vulnerabilities, even previously exploited, reported, and patched in high-profile cryptography libraries. Nevertheless, researchers continue to find and exploit the same vulnerabilities in old and new products, highlighting a big issue among vendors: effectively tracking and fixing security vulnerabilities when disclosure is not done directly to them. In this work, we present another instance of this issue by performing the first library-wide SCA security evaluation of Mozilla's NSS security library. We use a combination of two independently-developed SCA security frameworks to identify and test security vulnerabilities. Our evaluation uncovers several new vulnerabilities in NSS affecting DSA, ECDSA, and RSA cryptosystems. We exploit said vulnerabilities and implement key recovery attacks using signals---extracted through different techniques such as timing, microarchitecture, and EM---and improved lattice methods.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3421761"}, {"primary_key": "2549879", "vector": [], "sparse_vector": [], "title": "A 2.1 KHz Zero-Knowledge Processor with BubbleRAM.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Zero-Knowledge (ZK) proofs (ZKP) are foundational in cryptography. Most recent ZK research focuses on non-interactive proofs (NIZK) of small statements, useful in blockchain scenarios. Another line, and our focus, instead targets proofs of large statements that are useful, e.g., in proving properties of programs in ZK. We specify a zero-knowledge processor that executes arbitrary programs written in a simple instruction set, and proves in ZK the correctness of the execution. Such an approach is well-suited for constructing ZK proofs of large statements as it efficiently supports complex programming constructs, such as loops and RAM access. Critically, we propose several novel ZK improvements that make our approach concretely efficient: (1) an efficient arithmetic representation with conversions to/from Boolean, (2) an efficient read-only memory that uses $2łog n$ OTs per access, and (3) an efficient read-write memory, øurram, which uses $\\frac1 2 łog^2 n$ OTs per access. ø<PERSON><PERSON> beats linear scan for RAM of size $>3$ elements! Prior ZK systems used generic ORAM costing orders of magnitude more. We cast our system as a garbling scheme that can be plugged into the ZK protocol of [<PERSON><PERSON><PERSON><PERSON> et al, CCS'13]. Put together, our system is concretely efficient: for a processor instantiated with $512$KB of main memory, each processor cycle costs $24$KB of communication. We implemented our approach in \\textttC++. On a 1Gbps LAN our implementation realizes a $2.1$KHz processor.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417283"}, {"primary_key": "2549881", "vector": [], "sparse_vector": [], "title": "Cybersecurity Research and Training for Power Distribution Grids - A Blueprint.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mitigating cybersecurity threats in power distribution grids requires a testbed for cybersecurity, e.g., to evaluate the (physical) impact of cyberattacks, generate datasets, test and validate security approaches, as well as train technical personnel. In this paper, we present a blueprint for such a testbed that relies on network emulation and power flow computation to couple real network applications with a simulated power grid. We discuss the benefits of our approach alongside preliminary results and various use cases for cybersecurity research and training for power distribution grids.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420016"}, {"primary_key": "2549882", "vector": [], "sparse_vector": [], "title": "The Boon and Bane of Cross-Signing: Shedding Light on a Common Practice in Public Key Infrastructures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Public Key Infrastructures (PKIs) with their trusted Certificate Authorities (CAs) provide the trust backbone for the Internet: CAs sign certificates which prove the identity of servers, applications, or users. To be trusted by operating systems and browsers, a CA has to undergo lengthy and costly validation processes. Alternatively, trusted CAs can cross-sign other CAs to extend their trust to them. In this paper, we systematically analyze the present and past state of cross-signing in the Web PKI. Our dataset (derived from passive TLS monitors and public CT logs) encompasses more than 7 years and 225 million certificates with 9.3 billion trust paths. We show benefits and risks of cross-signing. We discuss the difficulty of revoking trusted CA certificates where, worrisome, cross-signing can result in valid trust paths to remain after revocation; a problem for non-browser software that often blindly trusts all CA certificates and ignores revocations. However, cross-signing also enables fast bootstrapping of new CAs, e.g., Let's Encrypt, and achieves a non-disruptive user experience by providing backward compatibility. In this paper, we propose new rules and guidance for cross-signing to preserve its positive potential while mitigating its risks.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423345"}, {"primary_key": "2549884", "vector": [], "sparse_vector": [], "title": "Security of Streaming Encryption in Google&apos;s Tink Library.", "authors": ["Viet Tung Hoang", "<PERSON><PERSON>"], "summary": "We analyze the multi-user security of the streaming encryption in Google's Tink library via an extended version of the framework of nonce-based online authenticated encryption of <PERSON><PERSON> et al. (CRYPTO'15) to support random-access decryption. We show that Tink's design choice of using random nonces and a nonce-based key-derivation function indeed improves the concrete security bound. We then give two better alternatives that are more robust against randomness failure. In addition, we show how to efficiently instantiate the key-derivation function via AES, instead of relying on HMAC-SHA256 like the current design in Tink. To accomplish this we give a multi-user analysis of the XOR-of-permutation construction of Bellare, Krovetz, and Rogaway (EUROCRYPT'98).", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417273"}, {"primary_key": "2549885", "vector": [], "sparse_vector": [], "title": "PPE Circuits: Formal Definition to Software Automation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Pairing-based cryptography is widely used for its efficiency and functionality. When designing pairing-based schemes, one common task is to devise algorithms for verifying a set of untrusted group elements with respect to a set of trusted group elements. One might be searching for a verification algorithm for a signature scheme or a method for verifying an IBE/ABE private key with respect to the IBE/ABE public parameters. In ACM CCS 2019 Hohenberger Vusirikala, the AutoPPE software tool was introduced for automatically generating a set of pairing product equations (PPEs) that can verify the correctness of a set of pairing group elements with respect to a set of trusted group elements. This task is non-trivial. Some schemes (e.g., those based on dual system encryption) provably do not support any efficient algorithm for verifying the private keys with respect to the public parameters. Other schemes (e.g., the <PERSON>en-Waters anonymous IBE) were left in a gray area by <PERSON><PERSON><PERSON><PERSON> (CCS 19) -- no conjunction of PPEs was known for testing them, but no proof of untestability either.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417230"}, {"primary_key": "2549888", "vector": [], "sparse_vector": [], "title": "LPET - Mining MS-Windows Software Privilege Escalation Vulnerabilities by Monitoring Interactive Behavior.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Local Privilege Escalation (LPE) is a common attack vector used by attackers to gain higher-level permissions. In this poster, we present a system called LPET to mine LPE vulnerabilities of third-party software in MS-Windows. Our insight is that the LPE is often caused by the interactions between high-privilege processes and user-controllable files. The interactions include creating a file, starting a process and others. Based on this observation, LPET first monitors software behaviors and constructs a directed interaction graph to abstract entities, such as files and processes, and their interactions. Then LPET analyzes exploiting paths from the graph by extracting user-controllable entities and checking their privileges. Finally, LPET verifies the exploiting paths using replacement or hijacking attacks. In the preliminary experiments, LPET found vulnerabilities in various software. Moreover, we discovered a common weakness pattern that some components were executed by software with high privilege after being released in the user-controllable temporary directory during installation, update, and uninstallation. By replacing the components, attackers with low privilege can hijack the execution flow of software to execute their codes with high privilege. We found that a wide range of software suffers from this weakness pattern, including Cisco AnyConnect, Dropbox, Notepad++.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420014"}, {"primary_key": "2549893", "vector": [], "sparse_vector": [], "title": "Implementing the Exponential Mechanism with Base-2 Differential Privacy.", "authors": ["<PERSON>"], "summary": "Despite excellent theoretical support, Differential Privacy (DP) can still be a challenge to implement in practice. In part, this challenge is due to the concerns associated with translating arbitrary- or infinite-precision theoretical mechanisms to the reality of floating point or fixed-precision. Beginning with the troubling result of <PERSON><PERSON><PERSON> demonstrating the security issues of using floating point for implementing the Laplace mechanism, there have been many reasonable questions raised concerning the vulnerabilities of real-world implementations of DP. In this work, we examine the practicalities of implementing the exponential mechanism of McSherry and Talwar. We demonstrate that naive or malicious implementations can result in catastrophic privacy failures. To address these problems, we show that the mechanism can be implemented exactly for a rich set of utility functions and values of the privacy parameter epsilon with limited practical overhead in running time and minimal code complexity. How do we achieve this result? We employ a simple trick of switching from base-e to base 2, allowing us to perform precise base-2 arithmetic. A short, precise expression is always available for epsilon, and the only approximation error we incur is the conversion of the base-2 privacy parameter back to base-e for reporting purposes. The core base-2 arithmetic of the mechanism can be simply and efficiently implemented using open-source high precision arithmetic libraries. Furthermore, the exact nature of the implementation lends itself to simple monitoring of correctness and proofs of privacy.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417269"}, {"primary_key": "2549899", "vector": [], "sparse_vector": [], "title": "PDiff: Semantic-based Patch Presence Testing for Downstream Kernels.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Open-source kernels have been adopted by massive downstream vendors on billions of devices. However, these vendors often omit or delay the adoption of patches released in the mainstream version. Even worse, many vendors are not publicizing the patching progress or even disclosing misleading information. However, patching status is critical for groups (e.g., governments and enterprise users) that are keen to security threats. Such a practice motivates the need for reliable patch presence testing for downstream kernels. Currently, the best means of patch presence testing is to examine the existence of a patch in the target kernel by using the code signature match. However, such an approach cannot address the key challenges in practice. Specifically, downstream vendors widely customize the mainstream code and use non-standard building configurations, which often change the code around the patching sites such that the code signatures are ineffective.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417240"}, {"primary_key": "2549901", "vector": [], "sparse_vector": [], "title": "Harnessing the Ambient Radio Frequency Noise for Wearable Device Pairing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wearable devices that capture user's rich information regarding their health conditions and daily activities have unmet pairing needs. Today's solutions, which primarily rely on human involvement, are cumbersome, error-prone, and do not scale well. Despite some prior efforts trying to fill this gap, they either rely on some sophisticated sensors, such as electromyogram (EMG) or electrocardiogram (ECG) pads that may not universally exist, or non-trivial design of communication transceivers that cannot be found easily on current commercial devices. Therefore, a pairing scheme for wearable devices that is secure, practical, and convenient is in dire need. In this paper, we propose a novel approach that leverages ambient radio frequency (RF) noise. Our design is based on a key observation that received RF noise power measured in the logarithmic scale at different parts of a human body surface experience the same variation trend, whereas those from different human bodies or off the body are distinct. Wearables make use of the observed noise as the entropy source for the proposed pairing protocol. Extensive experiments show that our scheme has an equal error rate (EER) as low as 1.4% for pairing. Its key generation rate reaches 138 bits/sec, which beats so-far existing pairing schemes. Besides, our scheme can be efficiently executed within 0.97 s. Its incurred energy consumption is as low as 0.27 J for the entire pairing procedure.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417288"}, {"primary_key": "2549903", "vector": [], "sparse_vector": [], "title": "Deploying Android Security Updates: an Extensive Study Involving Manufacturers, Carriers, and End Users.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Android's fragmented ecosystem makes the delivery of security updates and OS upgrades cumbersome and complex. While Google initiated various projects such as Android One, Project Treble, and Project Mainline to address this problem, and other involved entities (e.g., chipset vendors, manufacturers, carriers) continuously strive to improve their processes, it is still unclear how effective these efforts are on the delivery of updates to supported end-user devices. In this paper, we perform an extensive quantitative study (Aug. 2015 to Dec. 2019) to measure the Android security updates and OS upgrades rollout process. Our study leverages multiple data sources: the Android Open Source Project (AOSP), device manufacturers, and the top four U.S. carriers (AT&T, Verizon, T-Mobile, and Sprint). Furthermore, we analyze an end-user dataset captured in 2019 (152M anonymized HTTP requests associated with 9.1M unique user identifiers) from a U.S.-based social network. Our findings include unique measurements that, due to the fragmented and inconsistent ecosystem, were previously challenging to perform. For example, manufacturers and carriers introduce a median latency of 24 days before rolling out security updates, with an additional median delay of 11 days before end devices update. We show that these values alter per carrier-manufacturer relationship, yet do not alter greatly based on a model's age. Our results also delve into the effectiveness of current Android projects. For instance, security updates for Treble devices are available on average 7 days faster than for non-Treble devices. While this constitutes an improvement, the security update delay for Treble devices still averages 19 days.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423346"}, {"primary_key": "2549905", "vector": [], "sparse_vector": [], "title": "Forensic Analysis in Access Control: Foundations and a Case-Study from Practice.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We pose and study forensic analysis in the context of access control systems in a manner that prior work has not. Forensics seeks to answer questions about past states of a system, and thereby provides important clues and evidence in the event of a security incident. Access control deals with who may perform what action on a resource and is a critical security function. Our focus is access control systems that allow for changes to the authorization state to be delegated to potentially untrusted users. We argue that this context in access control is an important one in which to consider forensic analysis, and observe that it is a natural complement of safety analysis, which has been considered extensively in the literature. We pose the forensic analysis problem for such access control systems abstractly, and instantiate it for three schemes from the literature: a well-known access matrix scheme, a role-based scheme, and a discretionary scheme. We identify the computational complexity of forensic analysis, and compare it to that of safety analysis for each of the schemes. We consider also the notion of logs, i.e., data that can be collected over time to aid forensic analysis.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417860"}, {"primary_key": "2549907", "vector": [], "sparse_vector": [], "title": "MP-SPDZ: A Versatile Framework for Multi-Party Computation.", "authors": ["<PERSON>"], "summary": "Multi-Protocol SPDZ (MP-SPDZ) is a fork of SPDZ-2 (<PERSON> et al., CCS '13), an implementation of the multi-party computation (MPC) protocol called SPDZ (<PERSON><PERSON><PERSON><PERSON> et al., Crypto '12). MP-SPDZ extends SPDZ-2 to 30 MPC protocol variants, all of which can be used with the same high-level programming interface based on Python. This considerably simplifies comparing the cost of different protocols and security models. The protocols cover all commonly used security models (honest/dishonest majority and semi-honest/malicious corruption) as well as computation of binary and arithmetic circuits (the latter modulo primes and powers of two). The underlying primitives employed include secret sharing, oblivious transfer, homomorphic encryption, and garbled circuits. The breadth of implemented protocols coupled with an accessible high-level interface makes it suitable to benchmark the cost of computation in various security models for researchers both with and without a background in secure computation This paper aims to outline the variety of protocols implemented and the design choices made in the development of MP-SPDZ as well as the capabilities of the programming interface.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417872"}, {"primary_key": "2549912", "vector": [], "sparse_vector": [], "title": "Asynchronous Distributed Key Generation for Computationally-Secure Randomness, Consensus, and Threshold Signatures.", "authors": ["<PERSON>ef<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present the first Asynchronous Distributed Key Generation (ADKG) algorithm which is also the first distributed key generation algorithm that can generate cryptographic keys with a dual (f,2f+1)-threshold (where f is the number of faulty parties). As a result, using our ADKG we remove the trusted setup assumption that the most scalable consensus algorithms make. In order to create a DKG with a dual (f,2f+1)- threshold we first answer in the affirmative the open question posed by <PERSON><PERSON><PERSON> et al. [7] on how to create an Asynchronous Verifiable Secret Sharing (AVSS) protocol with a reconstruction threshold of f+1<k łe 2f+1, which is of independent interest. Our High-threshold-AVSS (HAVSS) uses an asymmetric bivariate polynomial to encode the secret. This enables the reconstruction of the secret only if a set of k nodes contribute while allowing an honest node that did not participate in the sharing phase to recover his share with the help of f+1 honest parties. Once we have HAVSS we can use it to bootstrap scalable partially synchronous consensus protocols, but the question on how to get a DKG in asynchrony remains as we need a way to produce common randomness. The solution comes from a novelEventually Perfect Common Coin (EPCC) abstraction that enables the generation of a common coin from n concurrent HAVSS invocations. EPCC's key property is that it is eventually reliable, as it might fail to agree at most f times (even if invoked a polynomial number of times). UsingEPCC we implement anEventually Efficient Asynchronous Binary Agreement (EEABA) which is optimal when the EPCC agrees and protects safety when EPCC fails. Finally, using EEABA we construct the first ADKG which has the same overhead and expected runtime as the best partially-synchronous DKG (O(n4) words, O(f) rounds). As a corollary of our ADKG, we can also create the first Validated Asynchronous Byzantine Agreement (VABA) that does not need a trusted dealer to setup threshold signatures of degree n-f. Our VABA has an overhead of expected O(n2) words and O(1) time per instance, after an initial O(n4) words and O(f) time bootstrap via ADKG.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423364"}, {"primary_key": "2549918", "vector": [], "sparse_vector": [], "title": "Machine Learning and Security: The Good, The Bad, and The Ugly.", "authors": ["<PERSON><PERSON>"], "summary": "I would like to share my thoughts on the interactions between machine learning and security.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3424552"}, {"primary_key": "2549919", "vector": [], "sparse_vector": [], "title": "A Forensically Sound Method of Identifying Downloaders and Uploaders in Freenet.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The creation and distribution of child sexual abuse materials (CSAM) involves a continuing violation of the victims? privacy beyond the original harms they document. A large volume of these materials is distributed via the Freenet anonymity network: in our observations, nearly one third of requests on Freenet were for known CSAM. In this paper, we propose and evaluate a novel approach for investigating these violations of exploited childrens' privacy. Our forensic method distinguishes whether or not a neighboring peer is the actual uploader or downloader of a file or merely a relayer. Our method requires analysis of the traffic sent to a single, passive node only. We evaluate our method extensively. Our in situ measurements of actual CSAM requests show an FPR of 0.002 ± 0.003 for identifying downloaders. And we show an FPR of 0.009 ± 0.018, a precision of 1.00 ± 0.01, and a TPR of 0.44 ± 0.01 for identifying uploaders based on in situ tests. Further, we derive expressions for the FPR and Power of our hypothesis test; perform simulations of single and concurrent downloaders; and characterize the Freenet network to inform parameter selection. We were participants in several United States Federal Court cases in which the use of our method was uniformly upheld.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417876"}, {"primary_key": "2549920", "vector": [], "sparse_vector": [], "title": "T2Pair: Secure and Usable Pairing for Heterogeneous IoT Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tongbo Luo"], "summary": "Secure pairing is key to trustworthy deployment and application of Internet of Things (IoT) devices. However, IoT devices lack conventional user interfaces, such as keyboards and displays, which makes many traditional pairing approaches inapplicable. Proximity-based pairing approaches are very usable, but can be exploited by co-located malicious devices. Approaches based on a user's physical operations on IoT devices are more secure, but typically require inertial sensors, while many devices do not satisfy this requirement. A secure and usable pairing approach that can be applied to heterogeneous IoT devices still does not exist. We develop a technique, Universal Operation Sensing, which allows an IoT device to sense the user's physical operations on it without requiring inertial sensors. With this technique, a user holding a smartphone or wearing a wristband can finish pairing in seconds through some very simple operations, e.g., pressing a button or twisting a knob. Moreover, we reveal an inaccuracy issue in original fuzzy commitment and propose faithful fuzzy commitment to resolve it. We design a pairing protocol using faithful fuzzy commitment, and build a prototype system named Touch-to-Pair (T2Pair, for short). The comprehensive evaluation shows that it is secure and usable.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417286"}, {"primary_key": "2549921", "vector": [], "sparse_vector": [], "title": "AdvPulse: Universal, Synchronization-free, and Targeted Audio Adversarial Attacks via Subsecond Perturbations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Existing efforts in audio adversarial attacks only focus on the scenarios where an adversary has prior knowledge of the entire speech input so as to generate an adversarial example by aligning and mixing the audio input with corresponding adversarial perturbation. In this work we consider a more practical and challenging attack scenario where the intelligent audio system takes streaming audio inputs (e.g., live human speech) and the adversary can deceive the system by playing adversarial perturbations simultaneously. This change in attack behavior brings great challenges, preventing existing adversarial perturbation generation methods from being applied directly. In practice, (1) the adversary cannot anticipate what the victim will say: the adversary cannot rely on their prior knowledge of the speech signal to guide how to generate adversarial perturbations; and (2) the adversary cannot control when the victim will speak: the synchronization between the adversarial perturbation and the speech cannot be guaranteed. To address these challenges, in this paper we propose AdvPulse, a systematic approach to generate subsecond audio adversarial perturbations, that achieves the capability to alter the recognition results of streaming audio inputs in a targeted and synchronization-free manner. To circumvent the constraints on speech content and time, we exploit penalty-based universal adversarial perturbation generation algorithm and incorporate the varying time delay into the optimization process. We further tailor the adversarial perturbation according to environmental sounds to make it inconspicuous to humans. Additionally, by considering the sources of distortions occurred during the physical playback, we are able to generate more robust audio adversarial perturbations that can remain effective even under over-the-air propagation. Extensive experiments on two representative types of intelligent audio systems (i.e., speaker recognition and speech command recognition) are conducted in various realistic environments. The results show that our attack can achieve an average attack success rate of over 89.6% in indoor environments and 76.0% in inside-vehicle scenarios even with loud engine and road noises.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423348"}, {"primary_key": "2549922", "vector": [], "sparse_vector": [], "title": "Finding Cracks in Shields: On the Security of Control Flow Integrity Mechanisms.", "authors": ["Yuan Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Control-flow integrity (CFI) is a promising technique to mitigate control-flow hijacking attacks. In the past decade, dozens of CFI mechanisms have been proposed by researchers. Despite the claims made by themselves, the security promises of these mechanisms have not been carefully evaluated, and thus are questionable.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417867"}, {"primary_key": "2549923", "vector": [], "sparse_vector": [], "title": "RIPT - An Efficient Multi-Core Record-Replay System.", "authors": ["<PERSON><PERSON><PERSON>", "Guancheng Li", "<PERSON>", "Ming Yuan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given the same input, a program may not behave the same in two runs due to some non-deterministic features, e.g., context switch and randomization. Such behaviors would cause non-deterministic program bugs which are hard to discover or diagnose. Record-and-replay is a promising technique to address such issues, however, performance and transparency are the main obstacles of existing works. In this poster, we propose a novel record-and-replay system named RIPT. RIPT utilizes Intel Processor Trace to record control flow information with very low overhead, and transparently captures non-deterministic sources such as system calls and signals with a kernel module. During replay, RIPT recovers the effect of non-deterministic events from the collected information, and makes target programs behave the same as recorded. We evaluate it with real-world program bugs and show that RIPT works well in practice.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420021"}, {"primary_key": "2549926", "vector": [], "sparse_vector": [], "title": "Fill in the Blanks: Empirical Analysis of the Privacy Threats of Browser Form Autofill.", "authors": ["<PERSON>", "Panagiotis <PERSON>", "<PERSON>"], "summary": "Providing functionality that streamlines the more tedious aspects of website interaction is of paramount importance to browsers as it can significantly improve the overall user experience. Browsers' autofill functionality exemplifies this goal, as it alleviates the burden of repetitively typing the same information across websites. At the same time, however, it also presents a significant privacy risk due to the inherent disparity between the browser's interpretation of a given web page and what users can visually perceive. In this paper we present the first, to our knowledge, comprehensive exploration of the privacy threats of autofill functionality. We first develop a series of new techniques for concealing the presence of form elements that allow us to obtain sensitive user information while bypassing existing browser defenses. Alarmingly, our large-scale study in the Alexa top 100K reveals the widespread use of such deceptive techniques for stealthily obtaining user-identifying information, as they are present in at least 5.8% of the forms that are autofilled by Chrome. Subsequently, our in-depth investigation of browsers' autofill functionality reveals a series of flaws and idiosyncrasies, which we exploit through a series of novel attack vectors that target specific aspects of browsers' behavior. By chaining these together we are able to demonstrate a novel invasive side-channel attack that exploits browser's autofill preview functionality for inferring sensitive information even when users choose to not utilize autofill. This attack affects all major Chromium-based browsers and allows attackers to probe users' autofill profiles for over a hundred thousand candidate values (e.g., credit card and phone numbers). Overall, while the preview mode is intended as a protective measure for enabling more informed decisions, ultimately it creates a new avenue of exposure that circumvents a user's choice to not divulge their information. In light of our findings, we have disclosed our techniques to the affected vendors, and have also created a Chrome extension that can prevent our attacks and mitigate this threat until our countermeasures are incorporated into browsers.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417271"}, {"primary_key": "2549928", "vector": [], "sparse_vector": [], "title": "Composite Backdoor Attack for Deep Neural Network by Mixing Existing Benign Features.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the prevalent use of Deep Neural Networks (DNNs) in many applications, security of these networks is of importance. Pre-trained DNNs may contain backdoors that are injected through poisoned training. These trojaned models perform well when regular inputs are provided, but misclassify to a target output label when the input is stamped with a unique pattern called trojan trigger. Recently various backdoor detection and mitigation systems for DNN based AI applications have been proposed. However, many of them are limited to trojan attacks that require a specific patch trigger. In this paper, we introduce composite attack, a more flexible and stealthy trojan attack that eludes backdoor scanners using trojan triggers composed from existing benign features of multiple labels. We show that a neural network with a composed backdoor can achieve accuracy comparable to its original version on benign data and misclassifies when the composite trigger is present in the input. Our experiments on 7 different tasks show that this attack poses a severe threat. We evaluate our attack with two state-of-the-art backdoor scanners. The results show none of the injected backdoors can be detected by either scanner. We also study in details why the scanners are not effective. In the end, we discuss the essence of our attack and propose possible defense.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423362"}, {"primary_key": "2549932", "vector": [], "sparse_vector": [], "title": "HoneyPLC: A Next-Generation Honeypot for Industrial Control Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Industrial Control Systems (ICS) provide management and control capabilities for mission-critical utilities such as the nuclear, power, water, and transportation grids. Within ICS, Programmable Logic Controllers (PLCs) play a key role as they serve as a convenient bridge between the cyber and the physical worlds, e.g., controlling centrifuge machines in nuclear power plants. The critical roles that ICS and PLCs play have made them the target of sophisticated cyberattacks that are designed to disrupt their operation, which creates both social unrest and financial losses. In this context, honeypots have been shown to be highly valuable tools for collecting real data, e.g., malware payload, to better understand the many different methods and strategies that attackers use. However, existing state-of-the-art honeypots for PLCs lack sophisticated service simulations that are required to obtain valuable data. Worse, they cannot adapt while ICS malware keeps evolving, and attack patterns become more sophisticated. To overcome these shortcomings, we present HoneyPLC, a high-interaction, extensible, and malware collecting honeypot supporting a broad spectrum of PLCs models and vendors. Results from our experiments show that HoneyPLC exhibits a high level of camouflaging: it is identified as real devices by multiple widely used reconnaissance tools, including Nmap, <PERSON><PERSON><PERSON>'s Honeyscore, the Siemens Step7 Manager, PLCinject, and PLCScan, with a high level of confidence. We deployed HoneyPLC on Amazon AWS and recorded a large amount of interesting interactions over the Internet, showing not only that attackers are in fact targeting ICS systems, but also that HoneyPLC can effectively engage and deceive them while collecting data samples for future analysis.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423356"}, {"primary_key": "2549934", "vector": [], "sparse_vector": [], "title": "Demystifying Resource Management Risks in Emerging Mobile App-in-App Ecosystems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "App-in-app is a new and trending mobile computing paradigm in which native app-like software modules, called sub-apps, are hosted by popular mobile apps such as Wechat, Baidu, TikTok and Chrome, to enrich the host app's functionalities and to form an \"all-in-one app\" ecosystem. Sub-apps access system resources through the host, and their functionalities come close to regular mobile apps (taking photos, recording voices, banking, shopping, etc.). Less clear, however, is whether the host app, typically a third-party app, is capable of securely managing sub-apps and their access to system resources. In this paper, we report the first systematic study on the resource management in app-in-app systems. Our study reveals high-impact security flaws, which allow the adversary to stealthily escalate privilege (e.g., accessing the camera, photo gallery, microphone, etc.) or acquire sensitive data (e.g., location, passwords of Amazon, Google, etc.). To understand the impacts of those flaws, we developed an analysis tool that automatically assesses 11 popular app-in-app platforms on both Android and iOS. Our results brought to light the prevalence of the security flaws. We further discuss the lessons learned and propose mitigation strategies.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417255"}, {"primary_key": "2549935", "vector": [], "sparse_vector": [], "title": "19th Workshop on Privacy in the Electronic Society (WPES 2020).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The 19th Workshop on Privacy in the Electronic Society (WPES 2020) was held as a virtual conference on 9 November, 2020, in conjunction with the 27th ACM Conference on Computer and Communication Security (CCS 2020). The goal of WPES is to bring together privacy researchers and practitioners to discuss the privacy problems that arise in an interconnected society and solutions to those problems. The program for the workshop contains 12 full papers and 3 short papers selected from a total of 34 submissions. Specific topics covered in the program include but are not limited to: communication privacy, data anonymization, differential privacy, medical privacy, mobile privacy, privacy engineering, privacy policies, user perception of privacy, and Web privacy.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416246"}, {"primary_key": "2549936", "vector": [], "sparse_vector": [], "title": "RTFM! Automatic Assumption Discovery and Verification Derivation from Library Document for API Misuse Detection.", "authors": ["Tao Lv", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Peiwei Hu", "<PERSON><PERSON>"], "summary": "To use library APIs, a developer is supposed to follow guidance and respect some constraints, which we call integration assumptions (IAs). Violations of these assumptions can have serious consequences, introducing security-critical flaws such as use-after-free, NULL-dereference, and authentication errors. Analyzing a program for compliance with IAs involves significant effort and needs to be automated. A promising direction is to automatically recover IAs from a library document using Natural Language Processing (NLP) and then verify their consistency with the ways APIs are used in a program through code analysis. However, a practical solution along this line needs to overcome several key challenges, particularly the discovery of IAs from loosely formatted documents and interpretation of their informal descriptions to identify complicated constraints (e.g., data-/control-flow relations between different APIs).", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423360"}, {"primary_key": "2549937", "vector": [], "sparse_vector": [], "title": "Practical Lattice-Based Zero-Knowledge Proofs for Integer Relations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present a novel lattice-based zero-knowledge proof system for showing that (arbitrary-sized) committed integers satisfy additive and multiplicative relationships. The proof sizes of our schemes are between two to three orders of magnitude smaller than in the lattice proof system of <PERSON><PERSON> et al. (CRYPTO 2018) for the same relations. Because the proof sizes of our protocols grow linearly in the integer length, our proofs will eventually be longer than those produced by quantum-safe succinct proof systems for general circuits (e.g. Ligero, Aurora, etc.). But for relations between reasonably-sized integers (e.g. $512$-bit), our proofs still result in the smallest zero-knowledge proof system based on a quantum-safe assumption. Of equal importance, the run-time of our proof system is at least an order of magnitude faster than any other quantum-safe scheme.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417894"}, {"primary_key": "2549941", "vector": [], "sparse_vector": [], "title": "DNS Cache Poisoning Attack Reloaded: Revolutions with Side Channels.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we report a series of flaws in the software stack that leads to a strong revival of DNS cache poisoning --- a classic attack which is mitigated in practice with simple and effective randomization-based defenses such as randomized source port. To successfully poison a DNS cache on a typical server, an off-path adversary would need to send an impractical number of $2^32 $ spoofed responses simultaneously guessing the correct source port (16-bit) and transaction ID (16-bit). Surprisingly, we discover weaknesses that allow an adversary to \"divide and conquer'' the space by guessing the source port first and then the transaction ID (leading to only $2^16 +2^16 $ spoofed responses). Even worse, we demonstrate a number of ways an adversary can extend the attack window which drastically improves the odds of success. The attack affects all layers of caches in the DNS infrastructure, such as DNS forwarder and resolver caches, and a wide range of DNS software stacks, including the most popular BIND, Unbound, and dnsmasq, running on top of Linux and potentially other operating systems. The major condition for a victim being vulnerable is that an OS and its network is configured to allow ICMP error replies. From our measurement, we find over 34% of the open resolver population on the Internet are vulnerable (and in particular 85% of the popular DNS services including Google's 8.8.8.8). Furthermore, we comprehensively validate the proposed attack with positive results against a variety of server configurations and network conditions that can affect the success of the attack, in both controlled experiments and a production DNS resolver (with authorization).", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417280"}, {"primary_key": "2549942", "vector": [], "sparse_vector": [], "title": "CPSIOTSEC&apos;20: 2020 Joint Workshop on CPS&amp;IoT Security and Privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang"], "summary": "There is a rapidly growing interest in the security of cyber-physical systems (CPS) and internet-of-things (IoT) in industry, government and academia. NIST recently created a Cyber-Physical Systems group and it is leading a public-private initiative to identify a general architecture, design principles, solutions, and challenges ahead. In Europe, the Horizon 2020 research program has targeted security issues relating to cyber-physical infrastructures and Internet of Things, while the fundamental science program CHIST-ERA launched calls for research projects on resilient trustworthy cyber-physical systems (2015) and user-centered security and privacy in the Internet of Things (2016). In the US, grant calls such as the 2019 CPS grant call by NSF (total of 50M USD) show the significance of CPS&IoT and their security.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416243"}, {"primary_key": "2549943", "vector": [], "sparse_vector": [], "title": "A Performant, Misuse-Resistant API for Primality Testing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Primality testing is a basic cryptographic task. But developers today are faced with complex APIs for primality testing, along with documentation that fails to clearly state the reliability of the tests being performed. This leads to the APIs being incorrectly used in practice, with potentially disastrous consequences. In an effort to overcome this, we present a primality test having a simplest-possible API: the test accepts a number to be tested and returns a Boolean indicating whether the input was composite or probably prime. For all inputs, the output is guaranteed to be correct with probability at least 1 - 2-128. The test is performant: on random, odd, 1024-bit inputs, it is faster than the default test used in OpenSSL by 17%. We investigate the impact of our new test on the cost of random prime generation, a key use case for primality testing. The OpenSSL developers have adopted our suggestions in full; our new API and primality test are scheduled for release in OpenSSL 3.0.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417264"}, {"primary_key": "2549945", "vector": [], "sparse_vector": [], "title": "Minimal Symmetric PAKE and 1-out-of-N OT from Programmable-Once Public Functions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Symmetric password-authenticated key exchange (sPAKE) can be seen as an extension of traditional key exchange where two parties agree on a shared key if and only if they share a common secret (possibly low-entropy) password. We present the first sPAKE protocol to simultaneously achieve the following properties: only two exponentiations per party, the same as plain unauthenticated <PERSON><PERSON><PERSON><PERSON> key agreement (and likely optimal); optimal round complexity: a single flow (one message from each party that can be sent in parallel) to achieve implicit authentication, or two flows to achieve explicit mutual authentication; security in the random oracle model, rather than ideal cipher or generic group model; UC security, rather than game-based. Our protocol is a generalization of the seminal EKE protocol of Bellovin & Merritt (S&P 1992).", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417870"}, {"primary_key": "2549946", "vector": [], "sparse_vector": [], "title": "BDoS: Blockchain Denial-of-Service.", "authors": ["<PERSON>", "Yan Ji", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Proof-of-work (PoW) cryptocurrency blockchains like Bitcoin secure vast amounts of money. Their operators, called miners, expend resources to generate blocks and receive monetary rewards for their effort. Blockchains are, in principle, attractive targets for Denial-of-Service (DoS) attacks: There is fierce competition among coins, as well as potential gains from short selling. Classical DoS attacks, however, typically target a few servers and cannot scale to systems with many nodes. There have been no successful DoS attacks to date against prominent cryptocurrencies. We present Blockchain DoS (BDoS), the first incentive-based DoS attack that targets PoW cryptocurrencies. Unlike classical DoS, BDoS targets the system's mechanism design: It exploits the reward mechanism to discourage miner participation. Previous DoS attacks against PoW blockchains require an adversary's mining power to match that of all other miners. In contrast, BDoS can cause a blockchain to grind to a halt with significantly fewer resources, e.g., 21% as of March 2020 in Bitcoin, according to our empirical study. We find that Bitcoin's vulnerability to BDoS increases rapidly as the mining industry matures and profitability drops. BDoS differs from known attacks like Selfish Mining in its aim not to increase an adversary's revenue, but to disrupt the system. Although it bears some algorithmic similarity to those attacks, it introduces a new adversarial model, goals, algorithm, and game-theoretic analysis. Beyond its direct implications for operational blockchains, BDoS introduces the novel idea that an adversary can manipulate miners' incentives by proving the existence of blocks without actually publishing them.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417247"}, {"primary_key": "2549948", "vector": [], "sparse_vector": [], "title": "R2DP: A Universal and Automated Approach to Optimizing the Randomization Mechanisms of Differential Privacy for Utility Metrics with No Known Optimal Distributions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yuan Hong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Differential privacy (DP) has emerged as a de facto standard privacy notion for a wide range of applications. Since the meaning of data utility in different applications may vastly differ, a key challenge is to find the optimal randomization mechanism, i.e., the distribution and its parameters, for a given utility metric. Existing works have identified the optimal distributions in some special cases, while leaving all other utility metrics (e.g., usefulness and graph distance) as open problems. Since existing works mostly rely on manual analysis to examine the search space of all distributions, it would be an expensive process to repeat such efforts for each utility metric. To address such deficiency, we propose a novel approach that can automatically optimize different utility metrics found in diverse applications under a common framework. Our key idea that, by regarding the variance of the injected noise itself as a random variable, a two-fold distribution may approximately cover the search space of all distributions. Therefore, we can automatically find distributions in this search space to optimize different utility metrics in a similar manner, simply by optimizing the parameters of the two-fold distribution. Specifically, we define a universal framework, namely, randomizing the randomization mechanism of differential privacy (R2DP), and we formally analyze its privacy and utility. Our experiments show that R2DP can provide better results than the baseline distribution (Laplace) for several utility metrics with no known optimal distributions, whereas our results asymptotically approach to the optimality for utility metrics having known optimal distributions. As a side benefit, the added degree of freedom introduced by the two-fold distribution allows R2DP to accommodate the preferences of both data owners and recipients.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417259"}, {"primary_key": "2549949", "vector": [], "sparse_vector": [], "title": "Fast Database Joins and PSI for Secret Shared Data.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a scalable protocol for database joins on secret shared data in the honest-majority three-party setting. The key features of our protocol are a rich set of SQL-like join/select queries and the ability to compose join operations together due to the inputs and outputs being generically secret shared between the parties. Provided that all joins operate on unique primary keys, no information is revealed to any party during the protocol. In particular, not even the sizes of intermediate joins are revealed. All of our protocols are constant-round and achieve O(n) communication and computation overhead for joining two tables of n rows.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423358"}, {"primary_key": "2549951", "vector": [], "sparse_vector": [], "title": "QuantumHammer: A Practical Hybrid Attack on the LUOV Signature Scheme.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Berk Sunar"], "summary": "Post-quantum schemes are expected to replace existing public-key schemes within a decade in billions of devices. To facilitate the transition, the US National Institute for Standards and Technology (NIST) is running a standardization process. Multivariate signatures is one of the main categories in NIST's post-quantum cryptography competition. Among the four candidates in this category, the LUOV and Rainbow schemes are based on the Oil and Vinegar scheme, first introduced in 1997 which has withstood over two decades of cryptanalysis. Beyond mathematical security and efficiency, security against side-channel attacks is a major concern in the competition. The current sentiment is that post-quantum schemes may be more resistant to fault-injection attacks due to their large key sizes and the lack of algebraic structure. We show that this is not true. We introduce a novel hybrid attack, QuantumHammer, and demonstrate it on the constant-time implementation of LUOV currently in Round 2 of the NIST post-quantum competition. The QuantumHammer attack is a combination of two attacks, a bit-tracing attack enabled via Rowhammer fault injection and a divide and conquer attack that uses bit-tracing as an oracle. Using bit-tracing, an attacker with access to faulty signatures collected using Rowhammer attack, can recover secret key bits albeit slowly. We employ a divide and conquer attack which exploits the structure in the key generation part of LUOV and solves the system of equations for the secret key more efficiently with few key bits recovered via bit-tracing. We have demonstrated the first successful in-the-wild attack on LUOV recovering all 11K key bits with less than 4 hours of an active Rowhammer attack. The post-processing part is highly parallel and thus can be trivially sped up using modest resources. QuantumHammer does not make any unrealistic assumptions, only requires software co-location (no physical access), and therefore can be used to target shared cloud servers or in other sandboxed environments.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417272"}, {"primary_key": "2549952", "vector": [], "sparse_vector": [], "title": "Impact of Energy Consumption Attacks on LoRaWAN-Enabled Devices in Industrial Context.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Successful deployment of Long-Range Wide Area Network (LoRaWAN) technology in several Industrial Internet of Things (IIoT) scenarios, such as Outage Management System (OMS) in smart metering, rely on low energy consumption of the end device. In this work, we conducted an experiment to demonstrate an on-off Denial-of-Service (DoS) attack to analyze the impact on the energy consumption of the LoRaWAN end device. We implemented the attack that manipulates the end device to remain in packet retransmission mode for several seconds. The conducted experiments show that the configurable parameters of LoRaWAN that are required for applications, like OMS, are susceptible to energy consumption attacks. In summary, our results show that when an on-off DoS attack is performed, the end device utilizing the Spreading Factor (SF) 12 consumes 92 times more energy due to packet retransmissions as compared to the end node using SF 7 under no attack.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420022"}, {"primary_key": "2549954", "vector": [], "sparse_vector": [], "title": "Phantom of the ADAS: Securing Advanced Driver-Assistance Systems from Split-Second Phantom Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we investigate \"split-second phantom attacks,\" a scientific gap that causes two commercial advanced driver-assistance systems (ADASs), Telsa Model X (HW 2.5 and HW 3) and Mobileye 630, to treat a depthless object that appears for a few milliseconds as a real obstacle/object. We discuss the challenge that split-second phantom attacks create for ADASs. We demonstrate how attackers can apply split-second phantom attacks remotely by embedding phantom road signs into an advertisement presented on a digital billboard which causes <PERSON>sla's autopilot to suddenly stop the car in the middle of a road and Mobileye 630 to issue false notifications. We also demonstrate how attackers can use a projector in order to cause <PERSON><PERSON>'s autopilot to apply the brakes in response to a phantom of a pedestrian that was projected on the road and Mobileye 630 to issue false notifications in response to a projected road sign. To counter this threat, we propose a countermeasure which can determine whether a detected object is a phantom or real using just the camera sensor. The countermeasure (GhostBusters) uses a \"committee of experts\" approach and combines the results obtained from four lightweight deep convolutional neural networks that assess the authenticity of an object based on the object's light, context, surface, and depth. We demonstrate our countermeasure's effectiveness (it obtains a TPR of 0.994 with an FPR of zero) and test its robustness to adversarial machine learning attacks.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423359"}, {"primary_key": "2549955", "vector": [], "sparse_vector": [], "title": "WI is Almost Enough: Contingent Payment All Over Again.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The problem of fair exchange consists of interchanging goods between two parties that do not trust each other. Despite known impossibility results, recent works leverage the block-chain and zero-knowledge proofs to implement zero-knowledge contingent payment (zkCP) systems that make fair exchange of digital goods possible. Implementing these systems in a secure and efficient way is a big challenge, as evidenced by several unsuccessful attempts from the literature. <PERSON><PERSON><PERSON> et al. (ACM CCS 2017) discovered a vulnerability on an existing zkCP proposal based on SNARKs (succinct non-interactive arguments of knowledge) and suggested several repairs. <PERSON><PERSON><PERSON><PERSON> (ACM CCS 2019) found a flaw in the mentioned countermeasures. In particular, he showed that witness-indistinguishability (WI) is not sufficient for the zkCP schemes proposed by <PERSON><PERSON><PERSON> et al. to be secure. In this work, we observe that a slightly stronger notion of WI, that we coin trapdoor subversion WI (tS-WI), rules out <PERSON><PERSON><PERSON><PERSON>'s attack. We formally define security properties for CP systems and show that, under tS-WI, <PERSON><PERSON><PERSON> et al.'s proposal indeed satisfies these properties. Additionally, we explore alternative approaches to implement ZK (other than SNARKs) and develop a prototype, using it to demonstrate their potential. Our new ideas result in a protocol to sell ECDSA signatures with contingent payment that can be executed in less than $150$ milliseconds over a LAN network.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417888"}, {"primary_key": "2549957", "vector": [], "sparse_vector": [], "title": "MuSig-DN: Schnorr Multi-Signatures with Verifiably Deterministic Nonces.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "MuSig is a multi-signature scheme for Schnorr signatures, which supports key aggregation and is secure in the plain public key model. Standard derandomization techniques for discrete logarithm-based signatures such as RFC~6979, which make the signing procedure immune to catastrophic failures in the randomness generation, are not applicable to multi-signatures as an attacker could trick an honest user into producing two different partial signatures with the same randomness, which would reveal the user's secret key.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417236"}, {"primary_key": "2549960", "vector": [], "sparse_vector": [], "title": "TRUSTORE: Side-Channel Resistant Storage for SGX using Intel Hybrid CPU-FPGA.", "authors": ["Hyunyoung Oh", "<PERSON><PERSON>", "Seonghyun Park", "<PERSON><PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON><PERSON>"], "summary": "Intel SGX is a security solution promising strong and practical security guarantees for trusted computing. However, recent reports demonstrated that such security guarantees of SGX are broken due to access pattern based side-channel attacks, including page fault, cache, branch prediction, and speculative execution. In order to stop these side-channel attackers, Oblivious RAM (ORAM) has gained strong attention from the security community as it provides cryptographically proven protection against access pattern based side-channels. While several proposed systems have successfully applied ORAM to thwart side-channels, those are severely limited in performance and its scalability due to notorious performance issues of ORAM. This paper presents TrustOre, addressing these issues that arise when using ORAM with Intel SGX. TrustOre leverages an external device, FPGA, to implement a trusted storage service within a completed isolated environment secure from side-channel attacks. TrustOre tackles several challenges in achieving such a goal: extending trust from SGX to FPGA without imposing architectural changes, providing a verifiably-secure connection between SGX applications and FPGA, and seamlessly supporting various access operations from SGX applications to FPGA.We implemented TrustOre on the commodity Intel Hybrid CPU-FPGA architecture. Then we evaluated with three state-of-the-art ORAM-based SGX applications, ZeroTrace, Obliviate, and Obfuscuro, as well as an end-to-end key-value store application. According to our evaluation, TrustOre-based applications outperforms ORAM-based original applications ranging from 10x to 43x, while also showing far better scalability than ORAM-based ones. We emphasize that since TrustOre can be deployed as a simple plug-in to SGX machine's PCIe slot, it is readily used to thwart side-channel attacks in SGX, arguably one of the most cryptic and critical security holes today.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417265"}, {"primary_key": "2549961", "vector": [], "sparse_vector": [], "title": "MTD&apos;20: 7th ACM Workshop on Moving Target Defense.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The seventh ACM Workshop on Moving Target Defense (MTD) Workshop is held virtually on November 9, 2020, in conjunction with the ACM Conference on Computer and Communications Security (CCS). The main objective of the workshop is to discuss novel randomization, diversification, and dynamism techniques for computer systems and network, new metric and analysis frameworks to assess and quantify the effectiveness of MTD, and discuss challenges and opportunities that such defenses provide. New this year the workshop has incorporated a number of invited papers to capture systematization of knowledge (SoK) from experts in this field that investigate the past ten years of MTD and discuss the way forward. We have constructed an exciting and diverse program of three refereed papers, five invited papers, and two invited keynote talks that will provide the participant with a vibrant and thought-provoking set of ideas and insights.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416244"}, {"primary_key": "2549963", "vector": [], "sparse_vector": [], "title": "Logging to the Danger Zone: Race Condition Attacks and Defenses on System Audit Frameworks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "For system logs to aid in security investigations, they must be beyond the reach of the adversary. Unfortunately, attackers that have escalated privilege on a host are typically able to delete and modify log events at will. In response to this threat, a variety of secure logging systems have appeared over the years that attempt to provide tamper-resistance (e.g., write once read many drives, remote storage servers) or tamper-evidence (e.g., cryptographic proofs) for system logs. These solutions expose an interface through which events are committed to a secure log, at which point they enjoy protection from future tampering. However, all proposals to date have relied on the assumption that an event's occurrence is concomitant with its commitment to the secured log.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417862"}, {"primary_key": "2549964", "vector": [], "sparse_vector": [], "title": "Exaggerated Error Handling <PERSON>s! An In-Depth Study and Context-Aware Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Operating system (OS) kernels frequently encounter various errors due to invalid internal states or external inputs. To ensure the security and reliability of OS kernels, developers propose a diverse set of mechanisms to conservatively capture and handle potential errors. Existing research has thus primarily focused on the completeness and adequacy of error handling to not miss the attention. However, we find that handling an error with an over-severe level (e.g., unnecessarily terminating the execution) instead hurts the security and reliability. In this case, the error-handling consequences are even worse than the error it attempts to resolve. We call such a case Exaggerated Error Handling (EEH). The security impacts of EEH bugs vary, including denial-of-service, data losses, broken control-flow integrity, memory leaks, etc. Despite its significance, detecting EEH remains an unexplored topic.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417256"}, {"primary_key": "2549965", "vector": [], "sparse_vector": [], "title": "A Tale of Evil Twins: Adversarial Inputs versus Poisoned Models.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shouling Ji", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Despite their tremendous success in a range of domains, deep learning systems are inherently susceptible to two types of manipulations: adversarial inputs -- maliciously crafted samples that deceive target deep neural network (DNN) models, and poisoned models -- adversely forged DNNs that misbehave on pre-defined inputs. While prior work has intensively studied the two attack vectors in parallel, there is still a lack of understanding about their fundamental connections: what are the dynamic interactions between the two attack vectors? what are the implications of such interactions for optimizing existing attacks? what are the potential countermeasures against the enhanced attacks? Answering these key questions is crucial for assessing and mitigating the holistic vulnerabilities of DNNs deployed in realistic settings.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417253"}, {"primary_key": "2549967", "vector": [], "sparse_vector": [], "title": "You&apos;ve Changed: Detecting Malicious Browser Extensions through their Update Deltas.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we conduct the largest to-date analysis of browser extensions, by investigating 922,684 different extension versions collected in the past six years, and using this data to discover malicious versions of extensions. We propose a two-stage system that first identifies malicious extensions based on anomalous extension ratings and locates the code that was added to a benign extension in order to make it malicious. We encode these code deltas according to the APIs that they abuse and search our historical dataset for other similar deltas of extensions which have not yet been flagged, neither by users nor by Chrome's Web Store. We were able to discover 143 malicious extensions belonging to 21 malicious clusters, exhibiting a wide range of abuse, from history stealing and ad injection, to the hijacking of new tabs and search engines. Our results show that our proposed techniques operate in an abuse-agnostic way and can identify malicious extensions that are evading detection.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423343"}, {"primary_key": "2549968", "vector": [], "sparse_vector": [], "title": "BlackMirror: Preventing Wallhacks in 3D Online FPS Games.", "authors": ["Seonghyun Park", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "Online gaming, with a reported 152 billion US dollar market, is immensely popular today. One of the critical issues in multiplayer online games is cheating, in which a player uses an illegal methodology to create an advantage beyond honest game play. For example, wallhacks, the main focus of this work, animate enemy objects on a cheating player's screen, despite being actually hidden behind walls (or other occluding objects). Since such cheats discourage honest players and cause game companies to lose revenue, gaming companies deploy mitigation solutions alongside game applications on the player's machine. However, their solutions are fundamentally flawed since they are deployed on a machine where the attacker has absolute control.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417890"}, {"primary_key": "2549969", "vector": [], "sparse_vector": [], "title": "A Qualitative Study of Dependency Management and Its Security Implications.", "authors": ["<PERSON>", "Duc-Ly Vu", "<PERSON><PERSON><PERSON>"], "summary": "Several large scale studies on the Maven, NPM, and Android ecosystems point out that many developers do not often update their vulnerable software libraries thus exposing the user of their code to security risks. The purpose of this study is to qualitatively investigate the choices and the interplay of functional and security concerns on the developers' overall decision-making strategies for selecting, managing, and updating software dependencies.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417232"}, {"primary_key": "2549973", "vector": [], "sparse_vector": [], "title": "HACLxN: Verified Generic SIMD Crypto (for all your favourite platforms).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Santiago Zanella-Béguelin"], "summary": "We present a new methodology for building formally verified cryptographic libraries that are optimized for multiple architectures. In particular, we show how to write and verify generic crypto code in the F* programming language that exploits single-instruction multiple data (SIMD) parallelism. We show how this code can be compiled to platforms that support vector instructions, including ARM Neon and Intel AVX, AVX2, and AVX512. We apply our methodology to obtain verified vectorized implementations on all these platforms for the ChaCha20 encryption algorithm, the Poly1305 one-time MAC, and the SHA-2 and Blake2 families of hash algorithms.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423352"}, {"primary_key": "2549976", "vector": [], "sparse_vector": [], "title": "Slimium: Debloating the Chromium Browser with Feature Subsetting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Today, a web browser plays a crucial role in offering a broad spectrum of web experiences. The most popular browser, Chromium, has become an extremely complex application to meet ever-increasing user demands, exposing unavoidably large attack vectors due to its large code base. Code debloating attracts attention as a means of reducing such a potential attack surface by eliminating unused code. However, it is very challenging to perform sophisticated code removal without breaking needed functionalities because Chromium operates on a large number of closely connected and complex components, such as a renderer and JavaScript engine. In this paper, we present Slimium, a debloating framework for a browser (i.e., Chromium) that harnesses a hybrid approach for a fast and reliable binary instrumentation. The main idea behind Slimium is to determine a set of features as a debloating unit on top of a hybrid (i.e., static, dynamic, heuristic) analysis, and then leverage feature subsetting to code debloating. It aids in i) focusing on security-oriented features, ii) discarding unneeded code simply without complications, and iii)~reasonably addressing a non-deterministic path problem raised from code complexity. To this end, we generate a feature-code map with a relation vector technique and prompt webpage profiling results. Our experimental results demonstrate the practicality and feasibility of Slimium for 40 popular websites, as on average it removes 94 CVEs (61.4%) by cutting down 23.85 MB code (53.1%) from defined features (21.7% of the whole) in Chromium.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417866"}, {"primary_key": "2549981", "vector": [], "sparse_vector": [], "title": "Censored Planet: An Internet-wide, Longitudinal Censorship Observatory.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Remote censorship measurement techniques offer capabilities for monitoring Internet reachability around the world. However, operating these techniques continuously is labor-intensive and requires specialized knowledge and synchronization, leading to limited adoption. In this paper, we introduce Censored Planet, an online censorship measurement platform that collects and analyzes measurements from ongoing deployments of four remote measurement techniques (Augur, Satellite/Iris, Quack, and Hyperquack). Censored Planet adopts a modular design that supports synchronized baseline measurements on six Internet protocols as well as customized measurements that target specific countries and websites. Censored Planet has already collected and published more than 21.8 billion data points of longitudinal network observations over 20 months of operation. Censored Planet complements existing censorship measurement platforms such as OONI and ICLab by offering increased scale, coverage, and continuity. We introduce a new representative censorship metric and show how time series analysis can be applied to Censored Planet's longitudinal measurements to detect 15 prominent censorship events, two-thirds of which have not been reported previously. Using trend analysis, we find increasing censorship activity in more than 100 countries, and we identify 11 categories of websites facing increasing censorship, including provocative attire, human rights issues, and news media. We hope that the continued publication of Censored Planet data helps counter the proliferation of growing restrictions to online freedom.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417883"}, {"primary_key": "2549982", "vector": [], "sparse_vector": [], "title": "CrypTFlow2: Practical 2-Party Secure Inference.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present CrypTFlow2, a cryptographic framework for secure inference over realistic Deep Neural Networks (DNNs) using secure 2-party computation. CrypTFlow2 protocols are both correct -- i.e., their outputs are bitwise equivalent to the cleartext execution -- and efficient -- they outperform the state-of-the-art protocols in both latency and scale. At the core of CrypTFlow2, we have new 2PC protocols for secure comparison and division, designed carefully to balance round and communication complexity for secure inference tasks. Using CrypTFlow2, we present the first secure inference over ImageNet-scale DNNs like ResNet50 and DenseNet121. These DNNs are at least an order of magnitude larger than those considered in the prior work of 2-party DNN inference. Even on the benchmarks considered by prior work, CrypTFlow2 requires an order of magnitude less communication and 20x-30x less time than the state-of-the-art.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417274"}, {"primary_key": "2549983", "vector": [], "sparse_vector": [], "title": "Bento: Bringing Network Function Virtualization to Tor.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Tor is a powerful and important tool for providing anonymity and censorship resistance to users around the world. Yet it is surprisingly difficult to deploy new services in Tor---it is largely relegated to proxies and hidden services---or to nimbly react to new forms of attack. Conversely, \"non-anonymous\" Internet services are thriving like never before because of recent advances in programmable networks, such as Network Function Virtualization (NFV) which provides programmable in-network middleboxes.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420020"}, {"primary_key": "2549985", "vector": [], "sparse_vector": [], "title": "CLAPS: Client-Location-Aware Path Selection in Tor.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Much research has investigated improving the security and performance of Tor by having Tor clients choose paths through the network in a way that depends on the client's location. However, this approach has been demonstrated to lead to serious deanonymization attacks. Moreover, we show how in some scenarios it can lead to significant performance degradation. For example, we demonstrate that using the recently-proposed Counter-RAPTOR system when guard bandwidth isn't abundant could increase median download times by 28.7%. We propose the CLAPS system for performing client-location-aware path selection, which fixes the known security and performance issues of existing designs. We experimentally compare the security and performance of CLAPS to Counter-RAPTOR and DeNASA. CLAPS puts a strict bound on the leakage of information about the client's location, where the other systems could completely reveal it after just a few connections. It also guarantees a limit on the advantage that an adversary can obtain by strategic relay placement, which we demonstrate to be overwhelming against the other systems. Finally, due to a powerful formalization of path selection as an optimization problem, CLAPS is approaching or even exceeding the original goals of algorithms to which it is applied, while solving their known deficiencies.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417279"}, {"primary_key": "2549995", "vector": [], "sparse_vector": [], "title": "eThor: Practical and Provably Sound Static Analysis of Ethereum Smart Contracts.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ethereum has emerged as the most popular smart contract platform, with hundreds of thousands of contracts stored on the blockchain and covering diverse application scenarios, such as auctions, trading platforms, or elections. Given the financial nature of smart contracts, security vulnerabilities may lead to catastrophic consequences and, even worse, can hardly be fixed as data stored on the blockchain, including the smart contract code itself, are immutable. An automated security analysis of these contracts is thus of utmost interest, but at the same time technically challenging. This is as e.g., Ethereum's transaction-oriented programming mechanisms feature a subtle semantics, and since the blockchain data at execution time, including the code of callers and callees, are not statically known. In this work, we present eThor, the first sound and automated static analyzer for EVM bytecode, which is based on an abstraction of the EVM bytecode semantics based on Horn clauses. In particular, our static analysis supports reachability properties, which we show to be sufficient for capturing interesting security properties for smart contracts (e.g., single-entrancy) as well as contract-specific functional properties. Our analysis is proven sound against a complete semantics of EVM bytecode, and a large-scale experimental evaluation on real-world contracts demonstrates that eThor is practical and outperforms the state-of-the-art static analyzers: specifically, eThor is the only one to provide soundness guarantees, terminates on 94% of a representative set of real-world contracts, and achieves an F-measure (which combines sensitivity and specificity) of 89%.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417250"}, {"primary_key": "2549996", "vector": [], "sparse_vector": [], "title": "Post-Quantum TLS Without Handshake Signatures.", "authors": ["<PERSON>", "Douglas <PERSON>", "<PERSON>"], "summary": "We present KEMTLS, an alternative to the TLS 1.3 handshake that uses key-encapsulation mechanisms (KEMs) instead of signatures for server authentication. Among existing post-quantum candidates, signature schemes generally have larger public key/signature sizes compared to the public key/ciphertext sizes of KEMs: by using an IND-CCA-secure KEM for server authentication in post-quantum TLS, we obtain multiple benefits. A size-optimized post-quantum instantiation of KEMTLS requires less than half the bandwidth of a size-optimized post-quantum instantiation of TLS 1.3. In a speed-optimized instantiation, KEMTLS reduces the amount of server CPU cycles by almost 90% compared to TLS 1.3, while at the same time reducing communication size, reducing the time until the client can start sending encrypted application data, and eliminating code for signatures from the server's trusted code base.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423350"}, {"primary_key": "2549997", "vector": [], "sparse_vector": [], "title": "A Generic Technique for Automatically Finding Defense-Aware Code Reuse Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Code reuse attacks have been the subject of a substantial amount of research during the past decade. This research largely resulted from early work on Return-Oriented Programming (ROP), which showed that the then newly proposed Non-Executable Memory (NX) defense could be bypassed. More recently, the research community has been simultaneously investigating new defenses that are believed to thwart code reuse attacks, such as Control Flow Integrity (CFI), and defense-aware techniques for attacking these defenses, such as Data-Oriented Programming (DOP). Unfortunately, the feasibility of defense-aware attacks are very dependent on the behaviors of the attacked program, which makes it difficult for defenders to understand how much protection a defense such as CFI may provide. To better understand this, researchers have introduced automated defense-aware code reuse attack systems. Unfortunately, the handful of existing systems implement a single fixed, defense-specific strategy that is complex and cannot be used to consider other defenses.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417234"}, {"primary_key": "2549998", "vector": [], "sparse_vector": [], "title": "Mitigation of Attacks on Email End-to-End Encryption.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "OpenPGP and S/MIME are two major standards for securing email communication introduced in the early 1990s. Three recent classes of attacks exploit weak cipher modes (EFAIL Malleability Gadgets, or EFAIL-MG), the flexibility of the MIME email structure (EFAIL Direct Exfiltration, or EFAIL-DE), and the Reply action of the email client (REPL<PERSON> attacks). Although all three break message confidentiality by using standardized email features, only EFAIL-MG has been mitigated in IETF standards with the introduction of AEAD algorithms. So far, no uniform and reliable countermeasures have been adopted by email clients to prevent EFAIL-DE and REPLY attacks. Instead, email clients implement a variety of different ad-hoc countermeasures which are only partially effective, cause interoperability problems, and fragment the secure email ecosystem.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417878"}, {"primary_key": "2549999", "vector": [], "sparse_vector": [], "title": "Towards Attribution in Mobile Markets: Identifying Developer Account Polymorphism.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Malicious developers may succeed at publishing their apps in mobile markets, including the official ones. If reported, the apps will be taken down and the developer accounts possibly be banned. Unfortunately, such take-downs do not prevent the attackers to use other developer accounts to publish variations of their malicious apps. This work presents a novel approach for identifying developer accounts, and other indicators of compromise (IOCs) in mobile markets, that belong to the same operation, i.e., to the same owners. Given a set of seed IOCs, our approach explores app and version metadata to identify new IOCs that belong to the same operation. It outputs an attribution graph, which details the attribution inferences, so that they can be reviewed. We have implemented our approach into Retriever, a tool that supports multiple mobile markets including the official GooglePlay and AppleStore. We have evaluated Retriever on 17 rogueware and adware operations. In 94% of the operations, Retriever discovers at least one previously unknown developer account. Furthermore, Retriever reveals that operations that look dead still have active developer accounts.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417281"}, {"primary_key": "2550000", "vector": [], "sparse_vector": [], "title": "SNI-in-the-head: Protecting MPC-in-the-head Protocols against Side-channel Analysis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "MPC-in-the-head based protocols have recently gained much popularity and are at the brink of seeing widespread usage. With widespread use come the spectres of implementation issues and implementation attacks such as side-channel attacks. We show that implementations of protocols implementing the MPC-in-the-head paradigm are vulnerable to side-channel attacks. As a case study, we choose the ZKBoo-protocol of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> (USENIX 2016) and show that even a single leaked value is sufficient to break the security of the protocol. To show that this attack is not just a theoretical vulnerability, we apply differential power analysis to show the vulnerabilities via a simulation.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417889"}, {"primary_key": "2550001", "vector": [], "sparse_vector": [], "title": "Gotta Catch&apos;Em All: Using Honeypots to Catch Adversarial Attacks on Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Deep neural networks (DNN) are known to be vulnerable to adversarial attacks. Numerous efforts either try to patch weaknesses in trained models, or try to make it difficult or costly to compute adversarial examples that exploit them. In our work, we explore a new \"honeypot\" approach to protect DNN models. We intentionally inject trapdoors, honeypot weaknesses in the classification manifold that attract attackers searching for adversarial examples. Attackers' optimization algorithms gravitate towards trapdoors, leading them to produce attacks similar to trapdoors in the feature space. Our defense then identifies attacks by comparing neuron activation signatures of inputs to those of trapdoors. In this paper, we introduce trapdoors and describe an implementation of a trapdoor-enabled defense. First, we analytically prove that trapdoors shape the computation of adversarial attacks so that attack inputs will have feature representations very similar to those of trapdoors. Second, we experimentally show that trapdoor-protected models can detect, with high accuracy, adversarial examples generated by state-of-the-art attacks (PGD, optimization-based CW, Elastic Net, BPDA), with negligible impact on normal classification. These results generalize across classification domains, including image, facial, and traffic-sign recognition. We also present significant results measuring trapdoors' robustness against customized adaptive attacks (countermeasures).", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417231"}, {"primary_key": "2550003", "vector": [], "sparse_vector": [], "title": "VAHunt: Warding Off New Repackaged Android Malware in App-Virtualization&apos;s Clothing.", "authors": ["<PERSON><PERSON>", "Jiang <PERSON>", "Jianming Fu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xuanchen Pan"], "summary": "Repackaging popular benign apps with malicious payload used to be the most common way to spread Android malware. Nevertheless, since 2016, we have observed an alarming new trend to Android ecosystem: a growing number of Android malware samples abuse recent app-virtualization innovation as a new distribution channel. App-virtualization enables a user to run multiple copies of the same app on a single device, and tens of millions of users are enjoying this convenience. However, cybercriminals repackage various malicious APK files as plugins into an app-virtualization platform, which is flexible to launch arbitrary plugins without the hassle of installation. This new style of repackaging gains the ability to bypass anti-malware scanners by hiding the grafted malicious payload in plugins, and it also defies the basic premise embodied by existing repackaged app detection solutions.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423341"}, {"primary_key": "2550004", "vector": [], "sparse_vector": [], "title": "Text Captcha Is Dead? A Large Scale Deployment and Empirical Study.", "authors": ["<PERSON><PERSON>", "Shouling Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The development of deep learning techniques has significantly increased the ability of computers to recognize CAPTCHA (Completely Automated Public Turing test to tell Computers and Humans Apart), thus breaking or mitigating the security of existing captcha schemes. To protect against these attacks, recent works have been proposed to leverage adversarial machine learning to perturb captcha pictures. However, they either require the prior knowledge of captcha solving models or lack adaptivity to the evolving behaviors of attackers. Most importantly, none of them has been deployed in practical applications, and their practical applicability and effectiveness are unknown.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417258"}, {"primary_key": "2550007", "vector": [], "sparse_vector": [], "title": "On the Optimality of Optimistic Responsiveness.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Abraham", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Synchronous consensus protocols, by definition, have a worst-case commit latency that depends on the bounded network delay. The notion of optimistic responsiveness was recently introduced to allow synchronous protocols to commit instantaneously when some optimistic conditions are met. In this work, we revisit this notion of optimistic responsiveness and present optimal latency results. We present a lower bound for Byzantine Broadcast that relates the latency of optimistic and synchronous commits when the designated sender is honest and while the optimistic commit can tolerate some faults. We then present two matching upper bounds for tolerating f faults out of $n = 2f+1$ parties. Our first upper bound result achieves optimal optimistic and synchronous commit latency when the designated sender is honest and the optimistic commit can tolerate at least one fault. We experimentally evaluate this protocol and show that it achieves throughput comparable to state-of-the-art synchronous and partially synchronous protocols and under optimistic conditions achieves latency better than the state-of-the-art. Our second upper bound result achieves optimal optimistic and synchronous commit latency when the designated sender is honest but the optimistic commit does not tolerate any faults. The presence of matching lower and upper bound results make both of the results tight for $n = 2f+1$. Our upper bound results are presented in a state machine replication setting with a steady-state leader who is replaced with a view-change protocol when they do not make progress. For this setting, we also present an optimistically responsive protocol where the view-change protocol is optimistically responsive too.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417284"}, {"primary_key": "2550008", "vector": [], "sparse_vector": [], "title": "rProfiler - Assessing Insider Influence on Enterprise Assets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Insider threat is a well-recognized problem in the cyber-security domain. There is good amount of research on detecting and predicting an insider attack. However, none of them addresses the influence of an insider over other individuals, and the spread of impact due to direct and indirect access to enterprise assets by having such influence. In this work, we propose a graph-based influence profiling solution called rProfiler that analyzes the data from multiple sources to determine the influence spread and calculate the probability of loss of data from an affected device using pertinent graph features. We also highlight multiple enterprise scenarios that may benefit from this work.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420026"}, {"primary_key": "2550011", "vector": [], "sparse_vector": [], "title": "CCSW&apos;20: 2020 Cloud Computing Security Workshop.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Clouds and massive-scale computing infrastructures are starting to dominate computing and will likely continue to do so for the foreseeable future. Major cloud operators are now comprising millions of cores hosting substantial fractions of corporate and government IT infrastructure.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416242"}, {"primary_key": "2550017", "vector": [], "sparse_vector": [], "title": "Information Leakage in Embedding Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Embeddings are functions that map raw input data to low-dimensional vector representations, while preserving important semantic information about the inputs. Pre-training embeddings on a large amount of unlabeled data and fine-tuning them for downstream tasks is now a de facto standard in achieving state of the art learning in many domains.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417270"}, {"primary_key": "2550019", "vector": [], "sparse_vector": [], "title": "Realistic Threats and Realistic Users: Lessons from the Election.", "authors": ["<PERSON>"], "summary": "The speaker will utilize his experience from inside one of the world's largest social networks during the 2016 and 2018 elections, and running an election integrity war room in 2020 to discuss the ways that technology fails the people we try so hard to serve. We will discuss the realistic assumptions we can make about threats, and the expectations we should have of users, and try to chart a path forward for how cutting-edge security research might better inform the engineers and product designers who end up putting computing technologies in the hands of billions.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3424553"}, {"primary_key": "2550020", "vector": [], "sparse_vector": [], "title": "PMForce: Systematically Analyzing postMessage Handlers at Scale.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The Web has become a platform in which sites rely on intricate interactions that span across the boundaries of origins. While the Same-Origin Policy prevents direct data exchange with documents from other origins, the postMessage API offers one relaxation that allows developers to exchange data across these boundaries. While prior manual analysis could show the presence of issues within postMessage handlers, unfortunately, a steep increase in postMessage usage makes any manual approach intractable. To deal with this increased work load, we set out to automatically find issues in postMessage handlers that allow an attacker to execute code in the vulnerable sites, alter client-side state, or leak sensitive information. To achieve this goal, we present an automated analysis framework running inside the browser, which uses selective forced execution paired with lightweight dynamic taint tracking to find traces in the analyzed handlers that end in sinks allowing for code-execution or state alterations. We use path constraints extracted from the program traces and augment them with Exploit Templates, i.e., additional constraints, ascertaining that a valid assignment that solves all these constraints produces a code-invoking or state-manipulating behavior. Based on these constraints, we use Z3 to generate postMessages aimed at triggering the insecure functionality to prove exploitability, and validate our findings at scale. We use this framework to conduct the most comprehensive experiment studying the security issues of postMessage handlers found throughout the top 100,000 most influential sites yet, which allows us to find potentially exploitable data flows in 252 unique handlers out of which 111 were automatically exploitable.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417267"}, {"primary_key": "2550021", "vector": [], "sparse_vector": [], "title": "PLAS&apos;20: 15th Workshop on Programming Languages and Analysis for Security.", "authors": ["Alley Stoughton", "<PERSON>"], "summary": "The 15th ACM SIGSAC Workshop on Programming Languages and Analysis for Security (PLAS 2020) is co-located with the 27th ACM Conference on Computer and Communications Security (ACM CCS 2020). Over its now more than ten-year history, PLAS has provided a unique forum for researchers and practitioners to exchange ideas about programming language and program analysis techniques with the goal of improving the security of software systems. Strongly encouraged are proposals of new, speculative ideas, evaluations of new or known techniques in practical settings, and discussions of emerging threats and important problems.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416252"}, {"primary_key": "2550023", "vector": [], "sparse_vector": [], "title": "A Multi-phased Multi-faceted IoT Honeypot Ecosystem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The rapid growth of Internet of Things (IoT) devices makes it vitally important to understand real-world cybersecurity threats to them. Traditionally, honeypots have been used as decoys to mimic real devices on a network and help researchers/organizations understand the dynamic of threats. A crucial condition for a honeypot to yield useful insights is to let attackers believe they are real systems used by humans and organizations. However, IoT devices pose unique challenges in this respect, due to the large variety of device types and the physical-connectedness nature. In this work, we (1) presented an approach to create a multi-phased multi-faceted honeypot ecosystem, where researchers gradually increase the sophistication of a low-interaction IoT honeypot by observing real-world attackers' behaviors, (2) built a low-interaction honeypot for IoT cameras that allowed researchers to gain a concrete understanding of what attackers were going after on IoT camera devices, and (3) designed a proxy instance, called ProxyPot, that sits between IoT devices and the external network and helps researchers study the IoT devices' inbound/outbound communication. We used PorxyPot as a means to understanding attacks against IoT cameras and increasing the honeypot's sophistication. We deployed honeypots for more than two years. Our preliminary results showed that we were able to attract increasingly sophisticated attack data in each new phase. Moreover, we captured activities that appeared to involve direct human interactions rather than purely automated scripts.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420023"}, {"primary_key": "2550026", "vector": [], "sparse_vector": [], "title": "Practical Recommendations for Stronger, More Usable Passwords Combining Minimum-strength, Minimum-length, and Blocklist Requirements.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Multiple mechanisms exist to encourage users to create stronger passwords, including minimum-length and character-class requirements, prohibiting blocklisted passwords, and giving feedback on the strength of candidate passwords. Despite much research, there is little definitive, scientific guidance on how these mechanisms should be combined and configured to best effect. Through two online experiments, we evaluated combinations of minimum-length and character-class requirements, blocklists, and a minimum-strength requirement that requires passwords to exceed a strength threshold according to neural-network-driven password-strength estimates.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417882"}, {"primary_key": "2550029", "vector": [], "sparse_vector": [], "title": "Verifiable Timed Signatures Made Practical.", "authors": ["<PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A verifiable timed signature (VTS) scheme allows one to time-lock a signature on a known message for a given amount of time T such that after performing a sequential computation for time T anyone can extract the signature from the time-lock. Verifiability ensures that anyone can publicly check if a time-lock contains a valid signature on the message without solving it first, and that the signature can be obtained by solving the same for time T.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417263"}, {"primary_key": "2550041", "vector": [], "sparse_vector": [], "title": "Towards Using Source Code Repositories to Identify Software Supply Chain Attacks.", "authors": ["Duc-Ly Vu", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Increasing popularity of third-party package repositories, like NPM, PyPI, or RubyGems, makes them an attractive target for software supply chain attacks. By injecting malicious code into legitimate packages, attackers were known to gain more than 100,000 downloads of compromised packages. Current approaches for identifying malicious payloads are resource demanding. Therefore, they might not be applicable for the on-the-fly detection of suspicious artifacts being uploaded to the package repository. In this respect, we propose to use source code repositories (e.g., those in Github) for detecting injections into the distributed artifacts of a package. Our preliminary evaluation demonstrates that the proposed approach captures known attacks when malicious code was injected into PyPI packages. The analysis of the 2666 software artifacts (from all versions of the top ten most downloaded Python packages in PyPI) suggests that the technique is suitable for lightweight analysis of real-world packages.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420015"}, {"primary_key": "2550042", "vector": [], "sparse_vector": [], "title": "Cache-in-the-Middle (CITM) Attacks: Manipulating Sensitive Data in Isolated Execution Environments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The traditional usage of ARM TrustZone has difficulty on solving the conflicts between the manufacturers that want to minimize the trusted computing base by constraining the installation of third-party applications in the secure world and the third-party application developers who prefer to have the freedom of installing their applications into the secure world. To address this issue, researchers propose to create Isolated Execution Environments (called IEEs) in the normal world to protect the security-sensitive applications. In this paper, we perform a systematic study on the IEE data protection models and the ARM cache attributes, and discover three cache-based attacks called CITM that can be leveraged to manipulate the sensitive data protected in IEEs. Specifically, due to the inefficient and incoherent security measures on the cache that maps to the IEE memory (i.e., memory designated for IEEs), attackers in the normal world may compromise the security of IEE data by manipulating the IEE memory during concurrent execution, bypassing the security measures enforced when a security-sensitive application is suspended or finished, or misusing the incomplete security measures during IEE's context switching processes. We conduct case studies of CITM attacks on three well-known IEE systems including SANCTUARY, Ginseng, and TrustICE to illustrate the feasibility to exploit them on real hardware testbeds. Finally, we analyze the root causes of the CITM attacks and propose a countermeasure to defeat them. The experimental results show that our defense scheme has a small overhead.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417886"}, {"primary_key": "2550044", "vector": [], "sparse_vector": [], "title": "When the Differences in Frequency Domain are Compensated: Understanding and Defeating Modulated Replay Attacks on Automatic Speech Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Automatic speech recognition (ASR) systems have been widely deployed in modern smart devices to provide convenient and diverse voice-controlled services. Since ASR systems are vulnerable to audio replay attacks that can spoof and mislead ASR systems, a number of defense systems have been proposed to identify replayed audio signals based on the speakers' unique acoustic features in the frequency domain. In this paper, we uncover a new type of replay attack called modulated replay attack, which can bypass the existing frequency domain based defense systems. The basic idea is to compensate for the frequency distortion of a given electronic speaker using an inverse filter that is customized to the speaker's transform characteristics. Our experiments on real smart devices confirm the modulated replay attacks can successfully escape the existing detection mechanisms that rely on identifying suspicious features in the frequency domain. To defeat modulated replay attacks, we design and implement a countermeasure named DualGuard. We discover and formally prove that no matter how the replay audio signals could be modulated, the replay attacks will either leave ringing artifacts in the time domain or cause spectrum distortion in the frequency domain. Therefore, by jointly checking suspicious features in both frequency and time domains, DualGuard can successfully detect various replay attacks including the modulated replay attacks. We implement a prototype of DualGuard on a popular voice interactive platform, ReSpeaker Core v2. The experimental results show DualGuard can achieve 98% accuracy on detecting modulated replay attacks.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417254"}, {"primary_key": "2550046", "vector": [], "sparse_vector": [], "title": "CheckDP: An Automated and Integrated Approach for Proving Differential Privacy or Finding Precise Counterexamples.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang"], "summary": "We propose CheckDP, the first automated and integrated approach for proving or disproving claims that a mechanism is differentially private. CheckDP can find counterexamples for mechanisms with subtle bugs for which prior counterexample generators have failed. Furthermore, it was able to \\emph{automatically} generate proofs for correct mechanisms for which no formal verification was reported before. CheckDP is built on static program analysis, allowing it to be more efficient and more precise in catching infrequent events than existing counterexample generators (which run mechanisms hundreds of thousands of times to estimate their output distribution). Moreover, its sound approach also allows automatic verification of correct mechanisms. When evaluated on standard benchmarks and newer privacy mechanisms, CheckDP generates proofs (for correct mechanisms) and counterexamples (for incorrect mechanisms) within 70 seconds without any false positives or false negatives.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417282"}, {"primary_key": "2550049", "vector": [], "sparse_vector": [], "title": "FirmXRay: Detecting Bluetooth Link Layer Vulnerabilities From Bare-Metal Firmware.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Today, Bluetooth 4.0, also known as Bluetooth Low Energy (BLE), has been widely used in many IoT devices (e.g., smart locks, smart sensors, and wearables). However, BLE devices could contain a number of vulnerabilities at the BLE link layer during broadcasting, pairing, and message transmission. To detect these vulnerabilities directly from the bare-metal firmware, we present FirmXRay, the first static binary analysis tool with a set of enabling techniques including a novel base address identification algorithm for robust firmware disassembling, precise data structure recognition, and configuration value resolution. As a proof-of-concept, we focus on the BLE firmware from two leading SoC vendors (i.e., Nordic and Texas Instruments), and implement a prototype of FirmXRay atop Ghidra. We have evaluated FirmXRay with 793 unique firmware (corresponding to 538 unique devices) collected using a mobile app based approach, and our experiment results show that 98.1% of the devices have configured random static MAC addresses, 71.5% Just Works pairing, and 98.5% insecure key exchanges. With these vulnerabilities, we demonstrate identity tracking, spoofing, and eavesdropping attacks on real-world BLE devices.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423344"}, {"primary_key": "2550050", "vector": [], "sparse_vector": [], "title": "LEAF: A Faster Secure Search Algorithm via Localization, Extraction, and Reconstruction.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Secure search looks for and retrieves records from a (possibly cloud-hosted) encrypted database while ensuring the confidentiality of the queries. Researchers are paying increasing attention to secure search in recent years due to the growing concerns about database privacy. However, the low efficiency of (especially multiplicative) homomorphic operations in secure search has hindered its deployment in practice. To address this issue, <PERSON><PERSON><PERSON> et al. [CCS 2018, PETS 2019] proposed new protocols that bring down the number of multiplications in the search algorithm from O(n2) to O(n log2 n), and then to O(n log n), where n is the size of the database.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417237"}, {"primary_key": "2550052", "vector": [], "sparse_vector": [], "title": "Facilitating Protocol-independent Industrial Intrusion Detection Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cyber-physical systems are increasingly threatened by sophisticated attackers, also attacking the physical aspect of systems. Supplementing protective measures, industrial intrusion detection systems promise to detect such attacks. However, due to industrial protocol diversity and lack of standard interfaces, great efforts are required to adapt these technologies to a large number of different protocols. To address this issue, we identify existing universally applicable intrusion detection approaches and propose a transcription for industrial protocols to realize protocol-independent semantic intrusion detection on top of different industrial protocols.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420019"}, {"primary_key": "2550055", "vector": [], "sparse_vector": [], "title": "ACE: Asynchronous and Concurrent Execution of Complex Smart Contracts.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Smart contracts are programmable, decentralized and transparent financial applications. Because smart contract platforms typically support Turing-complete programming languages, such systems are often said to enable arbitrary applications. However, the current permissionless smart contract systems impose heavy restrictions on the types of computations that can be implemented. For example, the globally-replicated and sequential execution model of Ethereum requires low gas limits that make many computations infeasible.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417243"}, {"primary_key": "2550065", "vector": [], "sparse_vector": [], "title": "Ferret: Fast Extension for Correlated OT with Small Communication.", "authors": ["<PERSON>", "Chen<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Correlated oblivious transfer (COT) is a crucial building block for secure multi-party computation (MPC) and can be generated efficiently via OT extension. Recent works based on the pseudorandom correlation generator (PCG) paradigm presented a new way to generate random COT correlations using only communication sublinear to the output length. However, due to their high computational complexity, these protocols are only faster than the classical IKNP-style OT extension under restricted network bandwidth. In this paper, we propose new COT protocols in the PCG paradigm that achieve unprecedented performance. \\em With $50$ Mbps network bandwidth, our maliciously secure protocol can produce one COT correlation in $22$ nanoseconds. More specifically, our results are summarized as follows: \\beginenumerate \\item We propose a semi-honest COT protocol with sublinear communication and linear computation. This protocol assumes primal-LPN and is built upon a recent VOLE protocol with semi-honest security by <PERSON><PERSON><PERSON> et al. (CCS 2019). We are able to apply various optimizations to reduce its communication cost by roughly $15\\times$, not counting a one-time setup cost that diminishes as we generate more COT correlations. \\item We strengthen our COT protocol to malicious security with no loss of efficiency. Among all optimizations, our new protocol features a new checking technique that ensures correctness and consistency essentially for free. In particular, our maliciously secure protocol is only \\em $1-3$ nanoseconds slower for each COT. \\item We implemented our protocols, and the code will be publicly available at EMP toolkit. We observe at least $9\\times$ improvement in running time compared to the state-of-the-art protocol by <PERSON> et al. (CCS 2019) in both semi-honest and malicious settings under any network faster than $50$ Mbps. \\endenumerate With this new record of efficiency for generating COT correlations, we anticipate new protocol designs and optimizations will flourish on top of our protocol.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417276"}, {"primary_key": "2550066", "vector": [], "sparse_vector": [], "title": "More Efficient MPC from Improved Triple Generation and Authenticated Garbling.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent works on distributed garbling have provided highly efficient solutions for constant-round MPC tolerating an arbitrary number of corruptions. In this work, we improve upon state-of-the-art protocols in this paradigm for further performance gain. First, we propose a new protocol for generating authenticated AND triples, which is a key building block in many recent works. \\beginitemize \\item We propose a new authenticated bit protocol in the two-party and multi-party settings from bare IKNP OT extension, allowing us to reduce the communication by about $24%$ and eliminate many computation bottlenecks. We further improve the computational efficiency for multi-party authenticated AND triples with cheaper and fewer consistency checks and fewer hash function calls. \\item We implemented our triple generation protocol and observe around $4\\times$ to $5\\times$ improvement compared to the best prior protocol in most settings. For example, in the two-party setting with 10 Gbps network and 8 threads, our protocol can generate more than $4$ million authenticated triples per second, while the best prior implementation can only generate $0.8$ million triples per second. In the multi-party setting, our protocol can generate more than $37000$ triples per second over 80 parties, while the best prior protocol can only generate the same number of triples per second over 16 parties. \\enditemize We also improve the state-of-the-art multi-party authenticated garbling protocol. \\beginitemize \\item We take the first step towards applying half-gates in the multi-party setting, which enables us to reduce the size of garbled tables by $2κ$ bits per gate per garbler, where κ is the computational security parameter. This optimization is also applicable in the semi-honest multi-party setting. \\item We further reduce the communication of circuit authentication from $4ρ$ bits to $1$ bit per gate, using a new multi-party batched circuit authentication, where ρ is the statistical security parameter. Prior solution with similar efficiency is only applicable in the two-party setting. \\enditemize For example, in the three-party setting, our techniques can lead to roughly a $35%$ reduction in the size of a distributed garbled circuit.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417285"}, {"primary_key": "2550073", "vector": [], "sparse_vector": [], "title": "PhishBench 2.0: A Versatile and Extendable Benchmarking Framework for Phishing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We describe version 2.0 of our benchmarking framework, PhishBench. With the addition of the ability to dynamically load features, metrics, and classifiers, our new and improved framework allows researchers to rapidly evaluate new features and methods for machine-learning based phishing detection. Researchers can compare under identical circumstances their contributions with numerous built-in features, ranking methods, and classifiers used in the literature with the right evaluation metrics. We will demonstrate PhishBench 2.0 and compare it against at least two other automated ML systems.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420017"}, {"primary_key": "2550075", "vector": [], "sparse_vector": [], "title": "Zero Knowledge Proofs for Decision Tree Predictions and Accuracy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Machine learning has become increasingly prominent and is widely used in various applications in practice. Despite its great success, the integrity of machine learning predictions and accuracy is a rising concern. The reproducibility of machine learning models that are claimed to achieve high accuracy remains challenging, and the correctness and consistency of machine learning predictions in real products lack any security guarantees. In this paper, we initiate the study of zero knowledge machine learning and propose protocols for zero knowledge decision tree predictions and accuracy tests. The protocols allow the owner of a decision tree model to convince others that the model computes a prediction on a data sample, or achieves a certain accuracy on a public dataset, without leaking any information about the model itself. We develop approaches to efficiently turn decision tree predictions and accuracy into statements of zero knowledge proofs. We implement our protocols and demonstrate their efficiency in practice. For a decision tree model with 23 levels and 1,029 nodes, it only takes 250 seconds to generate a zero knowledge proof proving that the model achieves high accuracy on a dataset of 5,000 samples and 54 attributes, and the proof size is around 287 kilobytes.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417278"}, {"primary_key": "2550077", "vector": [], "sparse_vector": [], "title": "DECO: Liberating Web Data Using Decentralized Oracles for TLS.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Thanks to the widespread deployment of TLS, users can access private data over channels with end-to-end confidentiality and integrity. What they cannot do, however, is prove to third parties the provenance of such data, i.e., that it genuinely came from a particular website. Existing approaches either introduce undesirable trust assumptions or require server-side modifications. Users' private data is thus locked up at its point of origin. Users cannot export data in an integrity-protected way to other applications without help and permission from the current data holder. We propose DECO (short for decentralized oracle) to address the above problems. DECO allows users to prove that a piece of data accessed via TLS came from a particular website and optionally prove statements about such data in zero-knowledge, keeping the data itself secret. DECO is the first such system that works without trusted hardware or server-side modifications. DECO can liberate private data from centralized web-service silos, making it accessible to a rich spectrum of applications. To demonstrate the power of DECO, we implement three applications that are hard to achieve without it: a private financial instrument using smart contracts, converting legacy credentials to anonymous credentials, and verifiable claims against price discrimination.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417239"}, {"primary_key": "2550078", "vector": [], "sparse_vector": [], "title": "VRLifeTime - An IDE Tool to Avoid Concurrency and Memory Bugs in Rust.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As a young programming language designed for systems software development, Rust aims to provide safety guarantees like high-level languages and performance efficiency like low-level languages. Lifetime is a core concept in Rust, and it is key to both safety checks and automated resource management conducted by the Rust compiler. However, Rust's lifetime rules are very complex. In reality, it is not uncommon that Rust programmers fail to infer the correct lifetime, causing severe concurrency and memory bugs. In this paper, we present VRLifeTime, an IDE tool that can visualize lifetime for Rust programs and help programmers avoid lifetime-related mistakes. Moreover, VRLifeTime can help detect some lifetime-related bugs (i.e., double locks) with detailed debugging information. A demo video is available at https://youtu.be/L5F_XCOrJTQ.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420024"}, {"primary_key": "2550080", "vector": [], "sparse_vector": [], "title": "PPMLP 2020: Workshop on Privacy-Preserving Machine Learning In Practice.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Shouling Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the rapid development of technology, data is becoming ubiquitous. User privacy and data security are drawing much attention over the recent years, especially with the European Union's General Data Protection Regulation (GDPR) and other laws coming into force. On one hand, from the customers' perspective, how to protect user privacy while making use of customers? data is a challenging task. On the other hand, data silos are becoming one of the most prominent issues for the society. From the business? perspective, how to bridge these isolated data islands to build better AI systems while meeting the data privacy and regulatory compliance requirements has imposed great challenges to the traditional machine learning paradigm. PPMLP will provide an opportunity to connect researchers from both CCS community and machine learning community to tackle these challenges.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3416245"}, {"primary_key": "2550081", "vector": [], "sparse_vector": [], "title": "Bypassing Tor Exit Blocking with Exit Bridge Onion Services.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Tor exit blocking, in which websites disallow clients arriving from Tor, is a growing and potentially existential threat to the anonymity network. This paper introduces HebTor, a new and robust architecture for exit bridges---short-lived proxies that serve as alternative egress points for Tor. A key insight of <PERSON><PERSON><PERSON><PERSON> is that exit bridges can operate as Tor onion services, allowing any device that can create outbound TCP connections to serve as an exit bridge, regardless of the presence of NATs and/or firewalls. HebTor employs a micropayment system that compensates exit bridge operators for their services, and a privacy-preserving reputation scheme that prevents freeloading. We show that <PERSON><PERSON><PERSON><PERSON> effectively thwarts server-side blocking of Tor, and we describe the security, privacy, and legal implications of our design.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417245"}, {"primary_key": "2550082", "vector": [], "sparse_vector": [], "title": "Talking with Familiar Strangers: An Empirical Study on HTTPS Context Confusion Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ziqiao Kong", "<PERSON><PERSON> Lu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "HTTPS is principally designed for secure end-to-end communication, which adds confidentiality and integrity to sensitive data transmission. While several man-in-the-middle attacks (e.g., SSL Stripping) are available to break the secured connections, state-of-the-art security policies (e.g., HSTS) have significantly increased the cost of successful attacks. However, the TLS certificates shared by multiple domains make HTTPS hijacking attacks possible again.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417252"}, {"primary_key": "2550083", "vector": [], "sparse_vector": [], "title": "Enhancing State-of-the-art Classifiers with API Semantics to Detect Evolved Android Malware.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning (ML) classifiers have been widely deployed to detect Android malware, but at the same time the application of ML classifiers also faces an emerging problem. The performance of such classifiers degrades---or called ages---significantly over time given the malware evolution. Prior works have proposed to use retraining or active learning to reverse and improve aged models. However, the underlying classifier itself is still blind, unaware of malware evolution. Unsurprisingly, such evolution-insensitive retraining or active learning comes at a price, i.e., the labeling of tens of thousands of malware samples and the cost of significant human efforts. In this paper, we propose the first framework, called APIGraph, to enhance state-of-the-art malware classifiers with the similarity information among evolved Android malware in terms of semantically-equivalent or similar API usages, thus naturally slowing down classifier aging. Our evaluation shows that because of the slow-down of classifier aging, APIGraph saves significant amounts of human efforts required by active learning in labeling new malware samples.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417291"}, {"primary_key": "2550084", "vector": [], "sparse_vector": [], "title": "PatchScope: Memory Object Centric Patch Diffing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zhu", "Jiang <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Software patching is one of the most significant mechanisms to combat vulnerabilities. To demystify underlying patch details, the techniques of patch differential analysis (a.k.a. patch diffing) are proposed to find differences between patched and unpatched programs' binary code. Considering the sophisticated security patches, patch diffing is expected to not only correctly locate patch changes but also provide sufficient explanation for understanding patch details and the fixed vulnerabilities. Unfortunately, none of the existing patch diffing techniques can meet these requirements. In this study, we first perform a large-scale study on code changes of security patches for better understanding their patterns. We then point out several challenges and design principles for patch diffing. To address the above challenges, we design a dynamic patch diffing technique PatchScope. Our technique is motivated by two key observations: 1) the way that a program processes its input reveals a wealth of semantic information, and 2) most memory corruption patches regulate the handling of malformed inputs via updating the manipulations of input-related data structures. The core of PatchScope is a new semantics-aware program representation, memory object access sequence, which characterizes how a program references data structures to manipulate inputs. The representation can not only deliver succinct patch differences but also offer rich patch context information such as input-patch correlations. Such information can interpret patch differences and further help security analysts understand patch details, locate vulnerability root causes, and even detect buggy patches.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3423342"}, {"primary_key": "2550085", "vector": [], "sparse_vector": [], "title": "SQUIRREL: Testing Database Management Systems with Language Validity and Coverage Feedback.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hong Hu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fuzzing is an increasingly popular technique for verifying software functionalities and finding security vulnerabilities. However, current mutation-based fuzzers cannot effectively test database management systems (DBMSs), which strictly check inputs for valid syntax and semantics. Generation-based testing can guarantee the syntax correctness of the inputs, but it does not utilize any feedback, like code coverage, to guide the path exploration.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3417260"}, {"primary_key": "2550086", "vector": [], "sparse_vector": [], "title": "Benchmarking Label Dynamics of VirusTotal Engines.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "VirusTotal is the largest online anti-malware scanning service. It is widely used by security researchers for labeling malware data or serving as a comparison baseline. However, several important challenges of using VirusTotal are left unaddressed (e.g., whether VirusTotal labels are already stable, when VirusTotal labels can be trusted), severely harming the correctness of research projects depending on VirusTotal.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297.3420013"}, {"primary_key": "2550087", "vector": [], "sparse_vector": [], "title": "A Personalised Blood Pressure Prediction System using Gaussian Mixture Regression and Online Recurrent Extreme Learning Machine.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hypertension is an epidemic restricted not only to the developing but also to the developed nations. It is triggered by various lifestyle choices that depend on each individual based on their personal physiology and lifestyle. Early diagnosis is possible, but it requires continuous blood pressure monitoring. Various machine learning methods have been proposed for early diagnosis of hypertension by predicting blood pressure and detecting high spikes in the values. However, these solutions are built upon the generic guidelines which may not be applicable for every patient. Most of these solutions incorporate batch learning and require all data to be present before prediction and do not support any online learning mechanism. This leads to potentially outdated models. Furthermore, there is also a lack of an intelligent approach to handling incomplete time series while training the model. This paper presents a personalized approach to estimate blood pressure that eliminates the need for continuous monitoring based on the Online recurrent extreme learning machine (OR-ELM). The missing values are imputed using Gaussian mixture models. The prediction model learns from the historical data and learns online as more data becomes available. The proposed scheme is developed and deployed on a mobile application for secured prediction results. The method is used to predict blood pressure in Malaysian population and compared with existing batch-learning and online learning methods. The results show that OR-ELM based model outperforms the existing online techniques such as the Online sequential extreme learning machine and batch learning technique such as Extreme learning machine.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231328"}, {"primary_key": "2550088", "vector": [], "sparse_vector": [], "title": "Learning from Social Robots.", "authors": ["<PERSON>"], "summary": "Social robots are robots which manipulate the social world, rather than the physical world of traditional robotics. Their appearance and behaviour is designed to tap into the human desire for social interaction. Because social interaction comes naturally to all of us, there is a misconception that building artificial social interaction is easy. This talk will argue that social robotics is on the one hand one of the hardest challenges in robotics but will at same time show that relatively simple designs and behaviours can go a long way in achieving social human-robot interaction. The illustrate the power of social robots the talk will focus on how social robots can be used to support education, and in specific how social robots can support children when learning a second language.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231310"}, {"primary_key": "2550089", "vector": [], "sparse_vector": [], "title": "EventGo! Exploring Event Dynamics from Social-Media Posts.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "One way to explore a city is to know what people do on their leisure. Looking for local events and promotions is a common need for most people during travel or moving to a new city. However, delivering such messages to the right people is still a challenge for small businesses that do not sell tickets on high-end websites. Instead, most events are usually distributed by posting on social networking sites like Facebook. To fulfill such information need, we consider information technologies to extract events from 230K Facebook fan pages to build an event database and provide a social event search service. The technologies includes web scraping and web data extraction as well as natural language processing techniques for event names and venues recognition. We show how to speed up training data preparation through locality sensitive hashing (LSH) on seed lists based on distant supervision as well as how to improve the training data quality via double-tier automatic labeling. In addition to the demonstration of event search service provided by EventGo, we also disclose statistics in the events extracted from Facebook Fan Pages and Facebook events, showing the change in the ad market.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231457"}, {"primary_key": "2550090", "vector": [], "sparse_vector": [], "title": "Path Planning Algorithm for Robotic Lawnmower using RTK-GPS Localization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recently, automatic lawnmowers have been widely used in families and public areas and the usage is also increasing rapidly. In order to design an efficient automatic lawnmower, this paper proposes an efficient complete coverage path planning algorithm. In this paper, we propose a coverage path planning algorithm for RTK-based autonomous lawnmower with the ability to detect obstacles and to plan a collision-free path simultaneously by using various sensors. The key technology is the complete coverage path planner which plans an efficient path with low duplication rate. The duplication rate of planned paths is about 150%.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231484"}, {"primary_key": "2550091", "vector": [], "sparse_vector": [], "title": "Multimodal Sensing for Understanding Color Effect on Students&apos; Task Performance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a study to investigate the effect of color on students' task performance in use of ICT device. In color psychology, colors in surroundings can affect human emotional states and behavioral responses. In educational field, the effect on students' emotions and performance in class has been discussed. The design of an environment through a variety of means such as lighting and colors can stimulate perceptual and emotional responses in students and affect their behavior. However, they did not discuss the methodology for quantitatively assessing student's internal state. We therefore developed a multimodal sensing for understanding color effect on students' task performance.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231451"}, {"primary_key": "2550092", "vector": [], "sparse_vector": [], "title": "Fertility Treatment Assist by Information Technology.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Currently, there are a lot of active studies in the medical and healthcare field using information technology and artificial intelligence (AI). In this talk, I will focus on the fusion research of AI and reproductive medicine, and discuss the usefulness. I will talk about the following topics. (1) Detection of thick seminiferous tubules in the testicles: In micro-TESE surgery is performed using a microscope, and the seminiferous tubules that produce sperm are removed, and the sperm is collected from the removed seminiferous tubules. This sperm collection surgery for patients with non-obstructive azoospermia requires incision of the testes. This damage needs to be minimized, and this cost must be saved. Therefore, it is required for both surgeons and patients to non-invasively detect the presence of sperm in the testes before this operation. In this study, we detect the seminiferous tubules with a diameter of 250-300 micrometer that can recover sperm using ultrasound technique. However, currently used ultrasonic waves of about 8 MHz cannot find a fine tube of this diameter by the low resolution. Thus, we found a characteristic that the peak frequency of the ultrasonic reflected wave is proportional to the reciprocal of the diameter, and estimated the diameter of the seminiferous tubule present in the testicle based on fuzzy inference. (2) Supporting of ovum collection surgery: At the time of surgery to collect ova, an ultrasonic system is generally used. However, the follicles watched by the ultrasound system do not always contain ovum, and there are follicles that do not contain ovum is called vacuole. It is not possible in terms of resolution to confirm the vacuoles with ultrasound images before their removal, and the presence or absence of ovum is only known after collection. Therefore, we developed detection software of vacuoles using AI. (3) Determining the insertion position of sperm that does not cause rupture on the ovum: In micro-insemination, a sperm is directly injected into an ovum collected from a woman using a pipette. When injecting sperm, the oval cell membrane is usually sufficiently extended with a pipette, and then rupture is performed by piezo pulse. However, rupture may occur during the development of the membrane, in which case the insemination rate is reduced. Therefore, we quantitatively evaluate the effect of puncture position on ruptured membrane from ovum images during micro-insemination, and develop a system that can perform puncture based on the evaluation. (4) Analysis of peristalsis of the uterus: The uterus performs a movement called uterine peristalsis to transport sperm. It is known that the direction and frequency of uterine peristalsis change according to the menstrual cycle. We developed a system to evaluate peristalsis of the uterus by Cine-MRI analysis, and an evaluation system of peristaltic movement frequency by the clinical ultrasonic image analysis. Finally, I would like to consider the future role of AI in the medical and health fields.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231344"}, {"primary_key": "2550093", "vector": [], "sparse_vector": [], "title": "A Design of Anthropomorphic Hand based on Human Finger Anatomy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The main role of the prosthetic hand is to help people with the upper limb amputees carry out daily activities. The appearance of the prosthetic hand should also be as close as possible to the natural hand. The human hand is a very complex structure, and these complex structures have a profound effect on the movement of the hand. In order to develop a prosthetic hand with the same flexibility as a human hand, we designed the prosthetic hand according to hands anatomical features, and incorporating these structural features into design: prosthetic joints, ligaments and extensor hoods. To evaluate the performance of the proposed prosthetic hand in daily life, a grabbing experiment was conducted on common objects in our daily life. The results show that the proposed prosthetic hand has a high performance and natural appearance.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231423"}, {"primary_key": "2550094", "vector": [], "sparse_vector": [], "title": "A Comparative Study on Three-mode Fuzzy Co-clustering Based on Co-occurrence Aggregation Criteria.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Three-mode fuzzy co-clustering is a promising technique for analyzing relational co-occurrence information among three mode elements. This paper proposes a modified version of the three-mode fuzzy clustering for categorical multivariate data (3FCCM) algorithm, which was constructed with the aggregation criterion of three elements based on the fuzzy c-means (FCM) concept and often suffers from careful tuning of three independent fuzzification parameters. In the modified algorithm, a novel clustering criterion is proposed based on a probabilistic concept, where we can easily tune the fuzziness degree of three-mode fuzzy partition by comparing with the probabilistic standard. The characteristics of the two algorithms are discussed through comparative experiments such that the modified version is more useful in tuning the influences of the fuzziness parameters and is more promising in real applications than the conventional one.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231431"}, {"primary_key": "2550095", "vector": [], "sparse_vector": [], "title": "Multi-Label Classification of ICD Coding Using Deep Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This study uses deep learning approach to tackle the multi-label classification problem in ICD coding. The discharge summaries on MIMIC-III dataset are adopted to explore the training methods of text preprocessing, label preprocessing, and model training. Specifically, the methods of label-to-chapter and common label classification are experimented, hoping to find the best recommendations of ICD codes for each diagnosis by the physicians. The result shows CNN has the best performance 76% by micro F1-measure in label-to-chapter. In addition, it also shows CNN outperforms other methods within top-50 and top-100 common labels.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231498"}, {"primary_key": "2550096", "vector": [], "sparse_vector": [], "title": "Short-Term Load Forecasting by Machine Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Xiang<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In the global energy transition, Taiwan government has legislated the law to require large-scale power consumers with the obligation to partially use renewable energy. Many companies choose to follow the regulation by purchasing green energy. To purchase the energy effectively, it is necessary to understand its own electricity consumption. In this paper, electricity load forecasting models are studied and compared. The impact of the holiday adjustment policy of Taiwan on the forecasting is investigated. Experimental results demonstrated that the recent, deep-learning technique LSTM achieved the best performance. On the 9-month test data, MAPE of the LSTM was 1.85%.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231499"}, {"primary_key": "2550097", "vector": [], "sparse_vector": [], "title": "Pixel Histogram based Background Modeling for Moving Target Detection.", "authors": ["<PERSON>", "Liangca<PERSON> Zen<PERSON>", "Gongfa Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Existing moving target detection methods mainly include inter-frame differences, background differences, optical flow and so on. For the recognition of human motions in the process of human-computer collaboration, existing algorithms are usually difficult to meet the requirements of real-time processing and easily interfered by lighting or image noises. In this paper, a method for establishing a static background model based on pixel histogram is proposed. The effect of moving targets and noises on the background model is excluded due to the selectivity of the new algorithm to the gray values, so it can detect the real background more reliably. Compared with other moving target detection methods, this method has the characteristics of fast speed, strong anti-interference ability, and the ability to identify human body movement quickly and accurately.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231352"}, {"primary_key": "2550098", "vector": [], "sparse_vector": [], "title": "Analysis of Relation between Brainwave and Heart Rate Information towards Entrainment Robot Assistance.", "authors": ["Tzong<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The purpose of this research project is for robots to react to biological data from humans, thus the necessity for understanding human interactions. This project observes synchronization of relations between human interaction. Through casual conversation, we monitored changes in heartrate and brainwaves. In the future we hope to program robots to accurately calculate student interest and through synchronization to improve learning impact and motivation.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231392"}, {"primary_key": "2550099", "vector": [], "sparse_vector": [], "title": "Relation of Heart Rate and a Human-interest Listening an Article for Supporting Continuous Conversation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this research, to verify whether human-interesting change can be analyzed with vital signs, human biological data have been collected. Feel was analyzed based on the collected data without using the entrainment used in conventional excitement analysis. This result realized the goal of deriving human-interesting with high accuracy from personal heart rate information.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231493"}, {"primary_key": "2550100", "vector": [], "sparse_vector": [], "title": "Development of Method for Detecting Conversation Breakdown Using Behavioral Information.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Existing conversation systems have difficulty in communicating smoothly. One of the reasons is that the robot cannot recognize the conversation breakdown. To solve this problem, we attempt to detect conversation breakdown using human motion characteristics. According to our preliminary research, features were found in head Yaw movement when conversation breakdown occurred. As a result of FFT analysis of yaw data, the low-frequency power was different between normal conversation and conversation breakdown. Based on this finding, the energy rate, which relatively expresses the magnitude of the frequency power, was defined. The conversation breakdown detector was developed to estimate conversation breakdown when the z-test shows a difference between the energy rate of normal conversation and the energy rate of the conversation breakdown.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231508"}, {"primary_key": "2550101", "vector": [], "sparse_vector": [], "title": "IOD and ENSO-Related Time Series Variability and Forecasting of Dengue and Malaria Incidence in Indonesia.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dengue and malaria are mosquito-borne infectious diseases driven by climate change and have an endemic impact in tropical and subtropical regions of the world, particularly in South and South-East Asia. Most studies examined the effect of local climate variability, temperature, and precipitation on dengue and malaria incidence. Nevertheless, the effects of Indian Ocean Dipole (IOD) and El Niño Southern Oscillation (ENSO) on dengue incidence are still rarely discussed. Besides, the study of the influence of IOD and ENSO on malaria incidence remain unexplored. This paper examined the influence of IOD and ENSO on the interannual variability of dengue and malaria incidence in all Indonesia provinces using Pearson correlation. Historical time series data on dengue and malaria incidence in all Indonesia provinces from 2005 to 2018 were investigated for time series prediction using deep Long Short-Term Memory (LSTM).", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231358"}, {"primary_key": "2550102", "vector": [], "sparse_vector": [], "title": "A Deep Neural Model for Pedestrians Detection with Danger Estimation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Personal mobility vehicles (PMV) with the autonomous driving system is required for supporting movement-limited persons. To realize their safe movement, researchers extract pedestrian features from images without considering pedestrian characteristics. However, before these extractions, it is also important to estimate which person should be paid attention and assigning priority order by the degree of danger for PMV's collision avoidance. In this paper, we propose a deep neural model for pedestrian detection with danger estimation. Finally, we show some experimental results and discuss parameters that are important for pedestrian danger estimation for each experimental scenario.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231483"}, {"primary_key": "2550103", "vector": [], "sparse_vector": [], "title": "A Study on AI-FML Robotic Agent for Student Learning Behavior Ontology Construction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>nch<PERSON> Li", "<PERSON><PERSON><PERSON>", "Tzong<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose an AI-FML robotic agent for student learning behavior ontology construction which can be applied in English speaking and listening domain. The AI-FML robotic agent with the ontology contains the perception intelligence, computational intelligence, and cognition intelligence for analyzing student learning behavior. In addition, there are three intelligent agents, including a perception agent, a computational agent, and a cognition agent in the AI-FML robotic agent. We deploy the perception agent and the cognition agent on the robot Kebbi Air. Moreover, the computational agent with the Deep Neural Network (DNN) model is performed in the cloud and can communicate with the perception agent and cognition agent via the Internet. The proposed AI-FML robotic agent is applied in Taiwan and tested in Japan. The experimental results show that the agents can be utilized in the human and machine co-learning model for the future education.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231339"}, {"primary_key": "2550104", "vector": [], "sparse_vector": [], "title": "Ankle joint motion change induced by vibration stimulation on the Tibialis anterior muscle during continuous motion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motor paralysis of lower limbs has a great influence on the daily actions and social life of stroke patients. In order to carry out rehabilitation training for patients, a variety of power-assist robots has been studied. On the other hand, it has been shown that a motion change induced by the reflection phenomenon and the kinesthetic illusion can be generated by stimulating the specific muscles with vibration stimulation during upper-limb movement. Artificially generating the intended motion or giving the movement sensation by adding vibration stimulation may be an alternative method to realize the rehabilitation training. Because In this study, change of ankle joint movement based on the vibration stimulation of the tibialis anterior muscle of the lower-limb is studied by performing the experiment of adding vibration stimulation to the tibialis anterior muscle during the movement of ankle joint dorsiflexion and plantar flexion, and then the possibility of its application to rehabilitation training is evaluated.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231319"}, {"primary_key": "2550105", "vector": [], "sparse_vector": [], "title": "User Story Driven Adaptive Planning Framework in Personal Daily Context.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper proposes a just-in-time approach to introduce adaptive planning in personal daily context. Adaptive planning is known to be effective in agile development, which is expected to be useful for our daily life as well. The chatbot-based approach has been proposed for modeling personal daily context as user story graph. This paper extends this approach by adopting the design of sprint planning. To evaluate the effectiveness of the proposed method, a short-term real-world simulation using a storytelling interface is conducted. The result shows that the proposed adaptive planning framework could lead to better decision-making during individual's daily life.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231325"}, {"primary_key": "2550106", "vector": [], "sparse_vector": [], "title": "Incremental Learning Algorithm of Data Complexity Based on KNN Classifier.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In practice, new data are constantly generated, and the existing data complexity algorithms are based on the idea of batch learning. In the face of the dynamic increase of data scale, how to measure the characteristic information of data has become an urgent problem to be solved in the field of data mining. This paper focuses on this problem and further studies its incremental learning function on the basis of in-depth discussion of data complexity proposed by <PERSON><PERSON> et al. Among them, N3 and N4 are the complexity indexes based on KNN classifier (K=1). In this paper, the incremental learning algorithm I1NN was proposed on the basis of 1-NN classifier, and its feasibility and validity were verified on both the artificial data set and the UCI public data set.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231514"}, {"primary_key": "2550107", "vector": [], "sparse_vector": [], "title": "Apply Deep Reinforcement Learning to NS-SHAFT Game Control.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Reinforcement Learning (RL) with both exploration and exploit abilities is applied to games. Related literature shows that it can surpass human performance, and some can even defeat human. This paper is mainly based on the combination of reinforcement learning and deep learning, called Deep Q-Network, to learn the action response of game NS-SHAFT autonomously. Based on a personal computer, we built an experimental learning environment that automatically captures the NS-SHAFT's frame which is provided to DQN to decide the action of moving left, moving right, or stay in same location, survey different parameters: such as the learning cycle, different reward settings, and exploration / exploit ratios etc., which affect the learning effectiveness. The experimental results show that moderate parameter settings have a certain degree of impact on the DQN learning effect.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231455"}, {"primary_key": "2550108", "vector": [], "sparse_vector": [], "title": "LSTM Classification of sEMG Signals For Individual Finger Movements Using Low Cost Wearable Sensor.", "authors": ["<PERSON>", "Nazmul H. <PERSON>", "<PERSON><PERSON>"], "summary": "The electrical activity of the muscles that control finger movements can be extracted during the performance of these movements and using machine learning techniques, the myoelectric signals can be decoded and classified according to the movement that generated the specific signal. The focus of this paper is to classify sEMG signal using easily accessible cheap hardware to capture the signal. Furthermore, to employ neural networks to classify the signal using established methodology i.e. feature extraction, with the highest possible accuracy. To classify these sEMG signals, an LSTM network has been developed and was able to classify 12 individual finger movements with accuracies reaching 90%.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231515"}, {"primary_key": "2550109", "vector": [], "sparse_vector": [], "title": "Development of EMG-wrist angle model based on <PERSON><PERSON> process toward user&apos;s voluntary operation of myoelectric hand.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The following topics are dealt with: learning (artificial intelligence); mobile robots; human-robot interaction; diseases; medical signal processing; health care; convolutional neural nets; human computer interaction; biomechanics; and pattern classification.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231318"}, {"primary_key": "2550110", "vector": [], "sparse_vector": [], "title": "Examination of the Impact of Differences in Restraint Conditions and Stimulus Intensity on the Fall Avoidance Strategy During a Slip Fall.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we analyze fall avoidance strategies with experiments during the applied stimulation of the slipping fall. The objective is a verification whether a fall avoidance movement in a single subject will be changed or not changed by the difference of experimental conditions. There are two conditions in these experiments. One is restrained of subject's movement by a string. The other is increased a intensity of the applying stimulus in experiments. In restrained the condition, caused by limited lowly angle of the shoulder joint and the hip joint, a subject cannot swing a upper limb and a lower limb like a usual in the forward and backward direction. In the condition of the stimulus intensity, caused by control an acceleration of one side belt in split-belt treadmill, a stimulus intensity can be increased or decreased. As a result, in restrained the condition caused by limited lowly angle of the shoulder joint and the hip joint, one subject had fallen in the first half of the trial, but could avoid fall in the latter half of the trial. Therefore, it was found that even if the subject had fallen initially in a condition, the subject may have changed the fall avoidance strategy finally in order to avoid a fall in the same condition.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231472"}, {"primary_key": "2550111", "vector": [], "sparse_vector": [], "title": "Finger Joint Detection Method in Hand X-ray Radiograph Images Using Statistical Shape Model and Support Vector Machine.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The number of rheumatoid arthritis (RA) patients is about 700,000 in Japan. The modified total sharp score (mTSS) calculated from hand X-ray image is a standard diagnosis method of RA progression but can be time-consuming for physicians. Computer-aided diagnostic (CAD) system is expected to improve the diagnostic quality of RA patients. We have previously proposed a CAD system, which can detect finger joint positions using support vector machine and estimate mTSS using support vector regression. This study improves the finger joint detection accuracy by incorporating with statistical shape model, which statistically models individual variety of spatial relationship among finger joints. Experimental results on radiographic images of the hands of 90 RA patients showed that the finger joints were detected with an accuracy of 94.5%, which is higher than 90.6% of the accuracy with the previous method.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231372"}, {"primary_key": "2550112", "vector": [], "sparse_vector": [], "title": "Early Detection of the Risk of Stunting in Pregnant Women and Its Recommendations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Stunting is considered an important concern in the public health of children throughout the world. Inadequate nutrition in toddlers is closely related to the risk of growth retardation, where malnutrition is caused by several risk factors in the prenatal and postnatal periods. It is necessary to improve the identification, measurement, and understanding of stunting and increase the scope of preventive stunting to reduce the number of stunting events. In this paper, we develop Android-based for early detection of the risk of stunting in pregnant women and their recommendations. We use forward chaining techniques to detect stunting, and we provide recommendations for pregnant women who are at high risk for birth. Our proposed stunt detection with forward chaining yielded in a high accuracy value of 89%.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231464"}, {"primary_key": "2550113", "vector": [], "sparse_vector": [], "title": "Artificial Intelligence (AI) for Humanity.", "authors": ["<PERSON>"], "summary": "Natural disasters including earthquakes, tsunamis, and anthropogenic disasters, such as wildfires, are dynamic situations requiring constant monitoring as numerous hazards are constantly emerging that hinder humanitarian efforts and create deadly conditions for rescue workers and victims. Now, with humanity facing a pandemic, it becomes even more vital to provide health care workers and first responders with low-cost and robust methods to protect themselves and help stop the spread of a disease that is ravaging our lives and economies. There are high expectations that Artificial Intelligence will be the game changing technology that will make sense of the plethora of disparate sources of data being collected about individuals, communities and the environment and that it will help solve all of these problems. Meanwhile, coupling AI with robotics is anticipated to be the foundation for a future contactless society that will mitigate the spread of diseases and keep us healthier. However, AI is not a box that can magically produce these solutions and building trustworthy AI comes with many challenges. This talk will discuss those challenges and explore some of the pathways forward to best utilize AI and its promise to advance robotics to conquer societal challenges.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231494"}, {"primary_key": "2550114", "vector": [], "sparse_vector": [], "title": "Automatic Aerial Victim Detection on Low-Cost Thermal Camera Using Convolutional Neural Network.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The first thing to do by the search-and-rescue (SAR) team after the disaster occurred is to find the location of the victim quickly. Thus the loss of lives can be reduced. After the disaster, the environment usually very messy, containing debris from building, soil, and gravel, which makes it harder to find the victims. By detecting the temperature using a thermal camera, it can easily be distinguished between the victims and the other background. Previous work, the technology to detect a person using a thermal camera from aerial has been developed, but it is only working with the most nearly uniform background. In this paper, we developed an automatic aerial (drones) victim detection using a thermal camera. A low-cost thermal camera has been used so that anyone can quickly implement in the real situation. By combining CNN as its algorithm that widely uses for its excellent performance on object detection, it can easily detect victims from the low-cost thermal camera and distinguished from complex background. Experiments show very well that the proposed method able to detect victims from aerial thermal view with accuracy AP = 82.49%. We believe it could bring benefits for future work with the related field and able to help search-and-rescue team to find and evacuate the victims quickly.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231433"}, {"primary_key": "2550115", "vector": [], "sparse_vector": [], "title": "Solusi247.", "authors": ["Beno K. Pradekso"], "summary": "Summary form only given, as follows. The complete presentation was not made available for publication as part of the conference proceedings. HGrid is a Hadoop data engineering tool made by solusi247 for Map Reduce, Spark and Storm frameworks which has been built since 2011 to help programmers building server-side data processing applications by automatically generate codes based on visually designed workflow. The biggest challenge to build this tool was to make most of the functions, modules, libraries and even workflow schemes (nearly) compatible between frameworks and to make a multipurpose single Integrated Development Environment (IDE) for Big Data availlable in the desktop and on the cloud. The development of the HGrid library starts with the collection of functions needed, how they can be implemented in each framework and how the codes can be generated. HGrid generated codes were also benchmarked against some commercially availlable tools to ensure good performance. HGrid is also designed to be visual, robust and user friendly although it still need to be improved in user experience part. It is also designed for average programmers and analysts. Data engineering libraries has been continuosly developed to ease application development with target near zero programming at the programmers side. HGrid has been used largely at the telco operators in Indonesia, in the largest banks, government institutions, hospitals and military to build complex applications such as Data Lake, Mediation Device, Media Analytics and many others.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231309"}, {"primary_key": "2550116", "vector": [], "sparse_vector": [], "title": "Kinetic Analysis of Pushing Force in Wheelchair Operation by Caregiver.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Transportation using wheelchair is one of the most frequent transportation methods at home, medical and nursing care facilities. Wheelchairs are designed to be operable by passengers but often require assistance, and wheelchair assistance is a heavy burden. In order to quantitatively evaluate the physical burden of the caregiver when operating the wheelchair, it is necessary to analyze the characteristics of the force applied to the wheelchair when the caregiver operates the wheelchair. On the other hand, the optimal posture index of the caregiver when operating a wheelchair is not known, and the operation posture differs depending on the caregiver. Therefore, in this study, we compare the posture and the mechanical characteristics of multiple caregivers to operate a wheelchair, and consider the posture of the caregiver.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231317"}, {"primary_key": "2550117", "vector": [], "sparse_vector": [], "title": "Introduction of Boolean Operation into Context Search Engine.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper proposes to implement boolean operations on a query of Context search engine (CSE), and shows experimental results to present its advantages on information retrieval tasks. Although web search engines have shown lots of advantages for us to search on the internet, its basic usage is remaining in only entering a few keywords. Its simplicity enables many people to use it easily, however, combining keywords needs well-experienced ability, therefore, it cannot be applied to another usage such as searching trends. CSE has been established to deal with temporal data, especially, trends among contents. However, it does not support combined queries like that of using boolean operations. This paper proposes to introduce boolean operations into CSE, and shows its advantages through experiments conducted with help of participants of students.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231408"}, {"primary_key": "2550118", "vector": [], "sparse_vector": [], "title": "Predicting the Risk of Preeclampsia using Soft Voting-based Ensemble and Its Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Preeclampsia is one of the leading causes of maternal deaths and pregnancy-related complications in many low-income countries. Factors related to preeclampsia include lack of prenatal care and access to medical services, lack of facilities, and inadequate diagnosis and treatment of preeclampsia patients. Early detection and prompt treatment of preeclampsia are essential for the prevention of eclampsia and other life-threatening complications. Several studies have developed the risk prediction models for preeclampsia using mobile applications and machine learning techniques. The use of a soft voting-based ensemble method and recommendation system for women at high risk of preeclampsia remains unexplored. We developed a mobile application with two functionalities of preeclampsia prediction and recommendation system for women at high risk of preeclampsia. We employed the soft voting ensemble learning for the preeclampsia prediction. We obtained a high accuracy value of 98.51% ± 0.0186% compared to the six individual classifiers (k-Nearest Neighbors, Linear SVM, RBF SVM, Gaussian Process, Multi-Layer Perceptron, and Ada Boost). Our proposed recommender system also yielded in a high accuracy value of 96.66% ± 0.0229%. Both our proposed ensemble based on soft-voting and recommendation system had small variance values, which indicated high stability.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231400"}, {"primary_key": "2550119", "vector": [], "sparse_vector": [], "title": "An Experimental Study for Algorithm of Finding for Resident-Tracking Robot System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Among the advanced countries, especially in Japan, we are facing on the decreasing birthrate and aging population ahead of the world. In recent, population of single-living elder, and elderly people have been taking care by same-aged person, have been actualized. Unfortunately, the population of caregivers is decreasing according to year. In this situation, the Elderly Person Watching System has been focused on. Especially, Robotics, Internet of Things, and Artificial Intelligence technology have been having high affinity with this system. In this study, firstly, the person watching robot system will be developed. In detail, in this paper, the flexible-analyze system will be focused on. In the algorithm, a resident-tracking task and an energy-saving task are holding. The whole tasks are working based on Reinforcement Learning. Thus, the behavior algorithm of the person watching robot system will be developed. In detail, in this paper, the flexible-analyze system will be focused on. In the algorithm, a resident-tracking task and an energy-saving task are holding. From the verification experiment, the simulation results showed the proposed method has been acquired actions to switch the desired task.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231492"}, {"primary_key": "2550120", "vector": [], "sparse_vector": [], "title": "Aerial Drone Mapping and Trajectories Generator for Agricultural Ground Robots.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Precision agriculture concept using robotics technology has attracted much interest. The concept requires increasing crop yield and quality while reducing labor costs by better cultivating the farmland using robotics technology. The first step of such technology requires sophisticated maps to get better accuracy. Conventional maps, such as scale maps and topological maps, are susceptible to noise and might not represent the real terrain of farmland, while digital terrain models acquired from satellites and other advanced acquisition devices are quite costly. In this paper, a system of an automatic trajectories generator for the paths of ground robots is developed. Aerial images taken from drones are employed to reconstruct a map that better representing the real terrain. A contour detector is then developed to get the farm boundaries, which are then followed by generating trajectories to be sent to the ground robots. We believe that this work will bring many benefits to agriculture.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231397"}, {"primary_key": "2550121", "vector": [], "sparse_vector": [], "title": "Wearable Sensor Data Visualization based on CNN towards Healthcare Promotion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the technological revolution of wearable devices, advanced medical methods have emerged, such as telemedicine, image diagnosis, disease prediction, healthcare and so on. This paper is focused on the development of wearable sensor, using sensors embedded in smart watches to read people's basic bio-information. Using people's living habits, daily activities and health status by comparing the past one day's resume. Through the CNN visualization results and future expansion research, it will further and more intuitively display the regularity of people's lives and the amount of exercise. To promote active sports habits and living habits. It has a positive effect on solving the high-stress, high-intensity and sub-healthy lifestyles of today's society. At the same time, users can still have a sense of accomplishment.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231517"}, {"primary_key": "2550122", "vector": [], "sparse_vector": [], "title": "ROS-base Multi-Sensor Fusion for Accuracy Positioning and SLAM System.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "To facing lower birth rate and aging society trend, how to design intelligent robots demanded in different fields to reduce the shortage of human resource has become an one of important research topics in recent years. In order to satisfy mobility ability of intelligent robots, accurate indoor positioning is an important function.In this paper, we use the Robot Operation System(ROS) as our platform, combining with different positioning sensors and technologies, such as Light Detection and Ranging(LiDAR), Inertial Measurement Unit (IMU), odometer and Ultra-wideband(UWB), and fuse these data by Extended Kalman Filter(EKF) to provide accuracy positioning. Thus, we can achieve more refined Simultaneous Localization and Mapping(SLAM) which meets the intelligent robot application demand. Experiment results show that the average error distance of the mobile robot in our system can be limited in 10cm.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231442"}, {"primary_key": "2550123", "vector": [], "sparse_vector": [], "title": "A Survey of Event Detection Methods for Eye Movements Classification in Smooth-Pursuit-Based Interactive Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Gaze-based interaction is an important research area in the field of human-computer interaction. Gaze-based input gives benefit for motion-impaired people and provides faster and intuitive interaction with an application content. Developing gaze-based interactive applications has to deal with several challenges: data processing, eye movements classification, and action. Smooth pursuit eye movement has been used as one of primary input modalities for gaze-based interactive applications. In developing smooth-pursuit-based interactive applications, challenge in the event classification is important to be addressed. However, there is no survey paper that studies various event detection methods in smooth-pursuit-based interactive applications. To tackle this research gap, we present a brief literature review of some event detection methods that consider smooth pursuit eye movement. This paper also presents a preliminary comparison of event detection methods based on some properties that are needed in smooth-pursuit-based interactive applications. From the preliminary comparison, there are some considerations to be taken in to account in order to develop smooth-pursuit-based interactive applications.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231384"}, {"primary_key": "2550124", "vector": [], "sparse_vector": [], "title": "A Survey of Learning Style Detection Method using Eye-Tracking and Machine Learning in Multimedia Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>a <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Current utilization of multimedia learning environment focuses on student-centered approach. This approach is based on a theory stating that learning styles affect individuals in information processing. Based on prior works, there are three main approaches to distinguish learning styles: conventional approach-such as interview and self-reporting, artificial-intelligence-based approach, and sensor-based approach. Unfortunately, there is no comparative analysis that addresses strengths and limitations of these approaches. Thus, there is no information on how and when to use these approaches appropriately. To address this limitation, we present a brief literature review of several studies in distinguishing learning styles, including their strengths and limitations. We also present insights on potential methods of detecting learning styles in multimedia learning based on eye movement data and machine learning algorithms. Our paper is useful as a guideline for developing intelligent e-learning systems based on eye tracking and machine learning.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231447"}, {"primary_key": "2550125", "vector": [], "sparse_vector": [], "title": "Design and Usage Support System of Robot Partners based on a User-centric Modular Structure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, various robot systems have been developing. And the social necessity of the robot system is gradually emphasized, especially in the service industry have an intention to use the robot system. However, the usage of robots is rarely expanded in the social place. One of the main reasons is the needs of users who actually use the robots are not determined, and it is not easy to design and use robots to meet the needs of users. Therefore, to consider this problem, we propose the concept that users can conveniently consider the robot hardware design. In this research, we focused on the design selection and fabrication based on modular structured robot partner system. The proposed method is composed of three parts. First, we introduce the development of robot partners in accordance with design thinking. Next, we propose a modular robot partner system in order to meet users' needs. Finally, we show experimental results of the effectiveness of the robot partner system and discuss the applicability of the proposed system.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231347"}, {"primary_key": "2550126", "vector": [], "sparse_vector": [], "title": "Divisive Hierarchical Clustering Based on Adaptive Resonance Theory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Divisive hierarchical clustering is a powerful tool for extracting knowledge from data with a pluralistic and appropriate information granularity. Recent developments of hierarchical clustering algorithms apply Growing Neural Gas (GNG) to data divisive mechanisms. However, GNG-based algorithms tend to generate nodes excessively and sensitive to the input order of data points. Furthermore, the plasticity-stability dilemma is another unavoidable problem. In this paper, we propose a divisive hierarchical clustering algorithm based on Adaptive Resonance Theory-based clustering. Simulation experiments show that the proposed algorithm can generate an appropriate tree structure depending on data while improving the performance of hierarchical clustering.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231474"}, {"primary_key": "2550127", "vector": [], "sparse_vector": [], "title": "An automated fracture detection from pelvic CT images with 3-D convolutional neural networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The demand for an automatic bone fracture detection in the emergency section of the hospitals is high for quick diagnosis while maintaining the quality. Previous studies on fracture detection with computed tomography (CT) images or X-ray images have a performance limitation because those methods are based on 2-D image analysis and cannot consider the 3-D internal structure of pelvic bones. This study proposes an automated bone fracture detection from 3-D CT images. Firstly, it introduces a new 3-D annotation method of fractures (called 3-D surface annotation). By using 3-D shape data of pelvic surfaces, it decreases the annotation load significantly. The proposed method estimates the degree of fracture for each point on the pelvic surface. The degree is estimated by 3-D convolutional neural networks (CNN) using 3-D distribution of CT values inside the pelvic surface. The proposed method was validated by using 103 subjects. The accuracy, precision, recall, and specificity for the test data were 69.5%, 61.1%, 56.4%, and 77.7%, respectively.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231453"}, {"primary_key": "2550128", "vector": [], "sparse_vector": [], "title": "Ecological-Inspired System Design for Safety Manipulation Strategy in Home-care Robot.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Manipulation tasks such as grasping, placing, and hand-over ability is crucial for the robot home application. In order to help the elderly at home, robots should have awareness and react carefully to prevent injury or accidents. The idea of safety strategy in robotics mainly used for industrial environment with less uncertainty. However, the home-care robot deals with many uncertainties condition and it is difficult to predict the human behavior than predict the industrial moving object. Thus, it is important to formulate a systematic approach to improving safety awareness on home-care robots. The following paper describes an approach of safety strategy for robot home manipulation task. This system uses an ecological approach based on the interconnection between perceptual system and action system according to spatial-temporal context of the updated information from environment. Some recommendation and preliminary experiments are conducted to show the possibility of further development for future research.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231354"}, {"primary_key": "2550129", "vector": [], "sparse_vector": [], "title": "Specific Surface Recognition Using Custom Finger Vision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hong<PERSON> Liu"], "summary": "FingerVision is becoming a promising solution to tactile manipulation tasks. This paper introduces a modular custom FingerVision and a new method of specific contact surface recognition implementing KNN (k-Nearest Neighbor) algorithm. This method uses the markers array in the sensor as feature points to describe the deformation in contact, and generates a database recording all simulated conditions to identify surfaces of different shapes. Adjusted parameters of the blob detection algorithm and a frame correction algorithm by linear interpolation in the feature extraction process are proposed as well. Recognition effects of different sample sizes, k values and other factors are discussed with an accuracy test. With optimization, accuracy rate of recognition achieves 100% among 570 test samples with 1140 given samples from 7 surfaces in 19 categories. The conclusions are that the accuracy of this method hardly depends on the k value and the dimension of coordinates analyzed, so the default k value 5 and only abscissas are applied to simplify the calculation. As for sample size, when the given sample number of each category reaches about 40, the accuracy rate is almost 100%, and thus it is unnecessary to continue to increase the sample size.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231465"}, {"primary_key": "2550130", "vector": [], "sparse_vector": [], "title": "Sensor2Vec: an Embedding Learning for Heterogeneous Sensors for Activity Classification.", "authors": ["Junpei &apos;Joni&apos; Zhong", "<PERSON>"], "summary": "Based on the idea of word2vec embedding method in NLP, this paper presents a novel idea called sensor2vec which captures the contextual information of the heterogeneous sensory information in the ambient assisted living setting. The contextual information is essential in order to classify and understand the human activity using multi-modal sensory data. In the activity classification, the sensor2vec embedding method is able to do the pre-processing which produce the embedding layer which represents the semantic value in the high-dimensional space. The preliminary experiment based on LSTM shows that the sensor2vec performs better classification result than the one-hot inputs.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1109/CCS49175.2020.9231478"}, {"primary_key": "2700271", "vector": [], "sparse_vector": [], "title": "CCS &apos;20: 2020 ACM SIGSAC Conference on Computer and Communications Security, Virtual Event, USA, November 9-13, 2020", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "These are interesting times. This year, due to the COVID-19 pandemic, we are holding a virtual conference. Thanks to the heroic efforts of the General Chairs, however, we are using various technologies to foster interaction among attendees, so that our community can continue to hold discussions and exchange ideas even when we cannot be physically co-located. Selecting papers is always a challenging task, but it was made even more challenging this year due to the additional demands and general stress put on authors, program-committee members, area chairs, and the program chairs themselves. Nevertheless, they all rose to the task and we were able to follow a process similar to the one used for this conference last year. As in 2019, there were two submission deadlines this year (in January and May), each with a roughly 2.5-month review cycle. Due to the large number of papers submitted, and also to give authors the opportunity to submit their papers elsewhere, some papers were rejected early in the process, for the most part only following two high-confidence, negative reviews. For the remaining papers, authors were given the opportunity to provide a rebuttal addressing specific concerns of the reviewers. By the end of each cycle, each submitted paper was marked for acceptance, conditional acceptance (shepherding), rejection, or revision. Papers in the last category were allowed to be resubmitted for another round of review, with the intention that they would be accepted if specific changes recommended by the reviewers (in some cases requiring extensive work) were made. All submissions were reviewed by a program committee of 157 security and privacy experts from around the world, along with many expert subreviewers from outside the committee, with the vast majority of papers (not including papers that were rejected early) receiving 4-5 reviews. The program chairs were assisted by 9 area chairs who are recognized experts in their respective subfields. The area chairs were also involved in selecting the award papers. The January cycle received 235 submissions, with 26 accepted (including those accepted with shepherding). An additional 14 papers were chosen for revision, with 13 of those eventually being accepted as well. Of the papers that were rejected, 113 were rejected early. A total of 480 papers were submitted to the May cycle, with 52 papers accepted (some with shepherding), and 35 papers chosen to be revised. From the latter group, 30 papers were eventually accepted. Of the rejected papers, 171 were rejected early. Altogether, 121 out of 715 submissions were accepted, for an acceptance rate of 17%.", "published": "2020-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3372297"}]