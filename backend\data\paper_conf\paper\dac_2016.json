[{"primary_key": "4080560", "vector": [], "sparse_vector": [], "title": "Invited - Approximate computing with partially unreliable dynamic random access memory - approximate DRAM.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the context of approximate computing, Approximate Dynamic Random Access Memory (ADRAM) enables the tradeoff between energy efficiency, performance and reliability. The inherent error resilience of applications allows sacrificing data storage robustness and stability by lowering the refresh rate or disabling refresh in DRAMs completely. Consequently, it is important to know exactly the statistical DRAM behavior with respect to retention time, process variation and temperature to manage this trade-off and thereby deliberately exploiting the error resilience of different target applications.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905002"}, {"primary_key": "4080561", "vector": [], "sparse_vector": [], "title": "Invited - Things, trouble, trust: on building trust in IoT systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>. <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The emerging and much-touted Internet of Things (IoT) presents a variety of security and privacy challenges. Prominent among them is the establishment of trust in remote IoT devices, which is typically attained via remote attestation, a distinct security service that aims to ascertain the current state of a potentially compromised remote device. Remote attestation ranges from relatively heavy-weight secure hardware-based techniques, to light-weight software-based ones, and also includes approaches that blend software (e.g., control-flow integrity) and hardware features (e.g., PUFs). In this paper, we survey the landscape of state-of-the-art attestation techniques from the IoT device perspective and argue that most of them have a role to play in IoT trust establishment.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905020"}, {"primary_key": "4080562", "vector": [], "sparse_vector": [], "title": "Automatic parallelization and accelerator offloading for embedded applications on heterogeneous MPSoCs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "MPSoCs have evolved into heterogeneous architectures, where general purpose processors are combined with accelerators. Directive-based programming models such as the OpenMP 4.0 accelerator model have emerged as an approach to parallelize and offload code regions to accelerators. However, existing compiler technologies have focused mainly on parallelization, leaving the challenging task of offloading code regions to the developers. In this paper, we propose a novel approach that addresses parallelization and offloading jointly. Results show that our approach is able to speedup sequential embedded applications significantly on a commercial heterogeneous MPSoC, which incorporates a quad-core ARM cluster and an octa-core DSP cluster.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897991"}, {"primary_key": "4080563", "vector": [], "sparse_vector": [], "title": "Reliability-aware design to suppress aging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Due to aging, circuit reliability has become extraordinary challenging. Reliability-aware circuit design flows do virtually not exist and even research is in its infancy. In this paper, we propose to bring aging awareness to EDA tool flows based on so-called degradation-aware cell libraries. These libraries include detailed delay information of gates/cells under the impact that aging has on both threshold voltage (Vth) and carrier mobility (μ) of transistors. This is unlike state of the art which considers Vth only. We show how ignoring μ degradation leads to underestimating guard-bands by 19% on average. Our investigation revealed that the impact of aging is strongly dependent on the operating conditions of gates (i.e. input signal slew and output load capacitance), and not solely on the duty cycle of transistors. Neglecting this fact results in employing insufficient guard-bands and thus not sustaining reliability during lifetime.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898082"}, {"primary_key": "4080564", "vector": [], "sparse_vector": [], "title": "BLESS: a simple and efficient scheme for prolonging PCM lifetime.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Limited endurance problem and low cell reliability are main challenges of phase change memory (PCM) as an alternative to DRAM. To further prolong the lifetime of a PCM device, there exist a number of techniques that can be grouped in two categories: 1) reducing the write rate to PCM cells, and 2) handling cell failures when faults occur. Our experiments confirm that during write operations, an extensive non-uniformity in bit ips is exhibited. To reduce this non-uniformity, we present byte-level shifting scheme (BLESS) which reduces write pressure over hot cells of blocks. Additionally, this shifting mechanism can be used for error recovery purpose by using the MLC capability of PCM and manipulating the data block to recover faulty cells. Evaluation results for multi-threaded workloads reveal 14-25% improvement in lifetime over existing state-of-the-art schemes.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897993"}, {"primary_key": "4080565", "vector": [], "sparse_vector": [], "title": "Efficient transistor-level timing yield estimation via line sampling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Yield estimation has been and will continue to be the integral part in design flow, particularly under large process variability in advanced technology nodes. This paper proposes an efficient method that accelerates transistor-level statistical timing simulations which are intensively conducted in various design stages, such as in final timing verification, while considering thousands of random variables. The proposed method utilizes line sampling (LS), in which integration of randomly generated lines, not the random points, are evaluated. Numerical experiments show that the proposed method achieves 14× to 300× speed-up compared to the fastest one that has ever reported.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898016"}, {"primary_key": "4080566", "vector": [], "sparse_vector": [], "title": "Exploiting design-for-debug for flexible SoC security architecture.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Systematic implementation of System-on-Chip (SoC) security policies typically involves smart wrappers extracting local security critical events of interest from Intellectual Property (IP) blocks, together with a control engine that communicates with the wrappers to analyze the events for policy adherence. However, developing customized wrappers at each IP for security requirements may incur significant overhead in area and hardware resources. In this paper, we address this problem by exploiting the extensive design-for-debug (DfD) instrumentation already available on-chip. In addition to reduction in the overall hardware overhead, the approach also adds flexibility to the security architecture itself, e.g., permitting use of on-field DfD instrumentation, survivability and control hooks to patch security policy implementation in response to bugs and attacks found at post-silicon or changing security requirements on-field. We show how to design scalable interface between security and debug architectures that provides the benefits of flexibility to security policy implementation without interfering with existing debug and survivability use cases and at minimal additional cost in energy and design complexity.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898020"}, {"primary_key": "4080567", "vector": [], "sparse_vector": [], "title": "SwiftGPU: fostering energy efficiency in a near-threshold GPU through a tactical performance boost.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we investigate the challenges of preserving energy-efficiency in a Near-Threshold Computing (NTC) GPU. Two key factors can significantly undermine the efficacy of GPUs at NTC: (a) elongated delays at NTC make the GPU applications severely sensitive toMulti-cycle Latency Datapaths (MLDs) within the GPU pipeline; and (b) process variation (PV) at NTC induces a substantial performance variance. To address these emerging challenges, we propose SwiftGPU---an energyefficient GPU design paradigm at NTC. SwiftGPU dynamically adjusts the degree of parallelization, and the speed of the MLDs within each stream core of the GPU. The proposed scheme achieves an average of~15% improvement in energy-efficiency over an ideal PV-free GPU, operating at the Super-Threshold regime. SwiftGPU incurs marginal area, wire-length and power overheads of 0.65%, 2.6% and 3.7%, respectively.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898100"}, {"primary_key": "4080568", "vector": [], "sparse_vector": [], "title": "Achieving lightweight multicast in asynchronous networks-on-chip using local speculation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a lightweight parallel multicast targeting an asynchronous NoC with a variant Mesh-of-Trees topology. A novel strategy, local speculation, is introduced, where a subset of switches are speculative and always broadcast. These switches are surrounded by non-speculative switches, which throttle any redundant packets, restricting these packets to small regions. Speculative switches have simplified designs, thereby improving network performance. A hybrid network architecture is proposed to mix the speculative and non-speculative switches. For multicast benchmarks, significant performance improvements with small power savings are obtained by the new approach over a tree-based non-speculative approach. Interestingly, similar improvements are also shown for unicast. Finally, another benefit is to reduce the address field size in multicast packets.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897978"}, {"primary_key": "4080569", "vector": [], "sparse_vector": [], "title": "Lower power by voltage stacking: a fine-grained system design approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Stacking voltage domains on top of each other is a design approach that is getting the attention of engineering communities due to the implicit high efficiency of the power delivery. Previous works have shown voltage stacking at the core level only. In this paper we present a more involved approach required to deploy voltage stacking not at the core level but at the IP level of a complex microcontroller. Our demonstrator chip features an ARM Cortex M0+ platform with an on-chip switched-capacitor voltage regulator. We chose to place the standard logic in one voltage domain between ground and VDD, and the memory \"on top of it\" between VDD and 2VDD, creating in this way a voltage stacked system. We further present silicon measurements that include a measured peak power efficiency in \"stacked mode\" of 96%.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898041"}, {"primary_key": "4080570", "vector": [], "sparse_vector": [], "title": "Optimal and fast throughput evaluation of CSDF.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Synchronous Dataow Graph (SDFG) and Cyclo-Static Dataow Graph (CSDFG) are two well-known models, used practically by industry for many years, and for which there is a large number of analysis techniques. Yet, basic problems such as the throughput computation or the liveness evaluation are not well solved, and their complexity is still unknown. In this paper, we propose K-Iter, an iterative algorithm based on K-periodic scheduling to compute the throughput of a CSDFG. By using this technique, we are able to compute in less than a minute the throughput of industry applications for which no result was available before.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898056"}, {"primary_key": "4080571", "vector": [], "sparse_vector": [], "title": "Remote attestation for low-end embedded devices: the prover&apos;s perspective.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Security of embedded devices is a timely and important issue, due to the proliferation of these devices into numerous and diverse settings, as well as their growing popularity as attack targets, especially, via remote malware infestations. One important defense mechanism is remote attestation, whereby a trusted, and possibly remote, party (verifier) checks the internal state of an untrusted, and potentially compromised, device (prover).", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898083"}, {"primary_key": "4080572", "vector": [], "sparse_vector": [], "title": "Procedural capacitor placement in differential charge-scaling converters by nonlinearity analysis.", "authors": ["Florin <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a new approach to capacitor placement in differential charge-scaling data converters. Good capacitor matching is crucial for charge-scaling converter resolution. Differential circuits are preferred to single-ended architectures due to improved immunity to noise and variations. The proposed method is based on the analysis of converter static nonlinearity equations. The impact of capacitor variations on converter linearity is minimized by exploiting spatial correlation between devices. Mathematical analysis is completed for two differential analog-to-digital converters. A procedure to obtain a capacitor placement is presented for each circuit. The architectures are compared with respect to linearity and capacitor placement area.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898073"}, {"primary_key": "4080573", "vector": [], "sparse_vector": [], "title": "Invited - Who is the major threat to tomorrow&apos;s security?: you, the hardware designer.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "More and more security attacks today are perpetrated by exploiting the hardware: memory errors can be exploited to take over systems, side-channel attacks leak secrets to the outside worlds, weak random number generators render cryptography ineffective, etc. At the same time, many of the tenets of efficient design are in tension with guaranteeing security. For instance, classic secure hardware does not allow to optimize common execution patterns, share resources, or provide deep introspection.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905022"}, {"primary_key": "4080574", "vector": [], "sparse_vector": [], "title": "Invited - Can IoT be secured: emerging challenges in connecting the unconnected.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Embedded, mobile, and cyberphysical systems are becoming ubiquitous and are used in many applications, from consumer electronics, industrial control systems, modern vehicles, to critical infrastructures. Current trends and initiatives, such as Internet of Things (IoT) and smart cities, promise innovative business models and novel user experiences through strong connectivity and effective use of next generation embedded devices. These systems generate, process, and exchange vast amount of security-critical and privacy-sensitive data, which makes them attractive targets of attacks. Cyberattacks on IoT systems are highly critical since they may cause physical damage and threaten human lives. The complexity of these systems, the lack of security and privacy by design for current IoT devices, and potential impact of cyberattacks will bring about new threats. This paper gives an overview on the related security and privacy challenges, and an outlook on possible solutions towards a holistic security framework for IoT systems.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905004"}, {"primary_key": "4080575", "vector": [], "sparse_vector": [], "title": "Debugging and verifying SoC designs through effective cross-layer hardware-software co-simulation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Verification of modern day electronic circuits has become the bottleneck for the timely delivery of complex SoC designs. We develop a novel cross-layer hardware/software co-simulation framework that can effectively debug and verify an SoC design. We combine high-level C/C++ software with cycle-accurate SystemC hardware, uniquely identify various types of bugs, and help the hardware designer localize them. Experimental results show that we are able to detect and aid in localization of logic bugs from both C/C++ specifications as well as the high-level synthesis engine itself. Our framework is fully automated, representing an important step forward targeting fast and effective SoC design verification.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898002"}, {"primary_key": "4080576", "vector": [], "sparse_vector": [], "title": "A low-power carry cut-back approximate adder with fixed-point implementation and floating-point precision.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper introduces an approximate adder architecture based on a digital quasi-feedback technique called Carry Cut-Back in which high-significance stages can cut the carry propagation chain at lower-significance positions. This lightweight approach prevents activation of the critical path, improving energy efficiency while guaranteeing low worst-case relative error. It offers a degree of freedom which allows to dissociate precision and dynamic range in fixed-point implementation. A design methodology is presented along with results and a comparative study. For a worst-case accuracy of 98%, energy savings up to 44% and power-delay-area reductions up to 62% are demonstrated compared to low-power conventional designs.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897964"}, {"primary_key": "4080577", "vector": [], "sparse_vector": [], "title": "Invited - The case for embedded scalable platforms.", "authors": ["<PERSON>"], "summary": "Heterogeneous system-on-chip (SoC) architectures are emerging as a fundamental computing platform across a variety of domains, from mobile to cloud computing. Heterogeneity, however, increases design complexity in terms of hardware-software interactions, access to shared resources, and diminished regularity of the design. Embedded Scalable Platforms are a novel approach to SoC design and programming that addresses these design-complexity challenges by combining an architecture and a methodology. The flexible socketed architecture simplifies the integration of heterogeneous components by balancing regularity and specialization. The companion methodology raises the level of abstraction to system-level design, thus promoting closer collaboration among software programmers and hardware engineers. The architecture is supported by a scalable communication infrastructure. The methodology leverages compositional design-space exploration with high-level synthesis. The case for Embedded Scalable Platforms is made based on experiments on the development of various full-system prototypes and experience in teaching these concepts in a new graduate course.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905018"}, {"primary_key": "4080578", "vector": [], "sparse_vector": [], "title": "Precise error determination of approximated components in sequential circuits with model checking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Error metrics are used to evaluate the quality of an approximated circuit or to trade-off several approximated candidates in design exploration. Precisely determining the error of an approximated circuit is a hard problem since the errors accumulate over time depending on the composition and nature of individual components. In this paper, we present methods based on model checking to precisely determine error behavior in sequential circuits that contain approximated combinational components. Our experiments show that such an analysis is very significant and crucial to properly deduce the effects of approximations.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898069"}, {"primary_key": "4080579", "vector": [], "sparse_vector": [], "title": "Multiple patterning layout decomposition considering complex coloring rules.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multiple patterning lithography has been recognized as one of the most promising solutions, in addition to extreme ultraviolet lithography, directed self-assembly, nanoimprint lithography, and electron beam lithography, for advancing the resolution limit of conventional optical lithography. Multiple patterning layout decomposition (MPLD) becomes more challenging as advanced technology introduces complex coloring rules. Existing works model MPLD as a graph coloring problem; nevertheless, when complex coloring rules are considered, layout decomposition can no longer be modeled accurately by graph coloring. Therefore, in this paper, for capturing the essence of layout decomposition with complex coloring rules, we model the MPLD problem as an exact cover problem. We then propose a fast and exact MPLD framework based on augmented dancing links. Our method is flexible and general: It can consider the basic and complex coloring rules simultaneously, and it can handle quadruple patterning and beyond. Experimental results show that our approach outperforms state-of-the-art works on reported conflicts and stitches and is promising for handling complex coloring rules as well.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898048"}, {"primary_key": "4080580", "vector": [], "sparse_vector": [], "title": "Match-making for monolithic 3D IC: finding the right technology node.", "authors": ["Kyungwook Chang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Monolithic 3D IC (M3D) has the potential to provide a break-through in the power and performance scaling challenges. We, for the first time, present a comprehensive study of M3D on a commercial design across multiple technology nodes. The performance and power impact of M3D is investigated using a commercial, in-order, 32-bit application processor, implemented on foundry 28nm and 14/16nm process nodes, as well as a predictive 7nm node. We study the factors across the technology nodes that affect the efficiency of M3D, and propose a roadmap for optimum technology and design interaction that will enable the full entitlement of M3D.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898043"}, {"primary_key": "4080581", "vector": [], "sparse_vector": [], "title": "Enabling sub-blocks erase management to boost the performance of 3D NAND flash memory.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "3D NAND has been proposed to provide a large capacity storage with low-cost consideration due to its high density memory architecture. However, 3D NAND needs to consume enormous time for garbage collection because of live-page copying overhead and long block erase time. To alleviate the impact of live-page copying on the performance of 3D NAND, a sub-block erase design has been designed. With sub-block erase design, this paper proposes a performance booster strategy to extremely boost the performance of garbage collection. As experimental results shows, the proposed strategy has a significant improvement on the average response time.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898018"}, {"primary_key": "4080582", "vector": [], "sparse_vector": [], "title": "AOS: adaptive overwrite scheme for energy-efficient MLC STT-RAM cache.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Spin-Transfer Torque Random Access Memory (STT-RAM) has been identified as an advantageous candidate for on-chip memory technology due to its high density and ultra low leakage power. Recent research progress in Magnetic Tunneling Junction (MTJ) devices has developed Multi-Level Cell (MLC) STT-RAM to further enhance cell density. To avoid the write disturbance in MLC strategy, data stored in the soft bit must be restored back immediately after the hard bit switching is completed. However, frequent restores are not only unnecessary, but also introduce a significant energy consumption overhead. In this paper, we propose an Adaptive Overwrite Scheme (AOS) which alleviates restoration overhead by intentionally overwriting selected soft bits based on RRD (Read Reuse Distance). Our experimental results show 54.6% reduction in soft bit restoration, delivering 10.8% decrease in overall energy consumption. Moreover, AOS promotes MLC to be a preferable L2 design alternative in terms of energy, area and latency product.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897987"}, {"primary_key": "4080583", "vector": [], "sparse_vector": [], "title": "MORPh: mobile OLED-friendly recording and playback system for low power video streaming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kent <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Even with the adoption of the latest OLED technology, the display panel remains one of the most power-hungry components in smartphones. Existing attempts for OLED power optimization have mainly focused on modifying the content that is shown on the display during the playback phase, requiring significant overhead in terms of image analysis and modification. While such methods are effective, they overlook opportunities present during the camera recording phase, where utilization of already determined camera parameters could reduce or eliminate the image processing overhead. Hence, we proposed MORPh, a cross-layer optimization system for OLED. We first analyze three fundamental parameters extracted from the smartphone camera and their impact on OLED power consumption and visual quality. We then define corresponding metrics to assess power optimization potentials, and propose a set of algorithms that optimize camera recording to be more OLED friendly. Finally, the proposed schemes are realized as part of a video recording and playback application on an existing Android smartphone. The experiments results indicate power saving of 7.3%~39.7%, and 20.3% on average while maintaining perceived visual quality.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898047"}, {"primary_key": "4080584", "vector": [], "sparse_vector": [], "title": "Clear: cross-layer exploration for architecting resilience combining hardware and software techniques to tolerate soft errors in processor cores.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Lukasz G. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a first of its kind framework which overcomes a major challenge in the design of digital systems that are resilient to reliability failures: achieve desired resilience targets at minimal costs (energy, power, execution time, area) by combining resilience techniques across various layers of the system stack (circuit, logic, architecture, software, algorithm). This is also referred to as cross-layer resilience. In this paper, we focus on radiation-induced soft errors in processor cores. We address both single-event upsets (SEUs) and single-event multiple upsets (SEMUs) in terrestrial environments. Our framework automatically and systematically explores the large space of comprehensive resilience techniques and their combinations across various layers of the system stack (586 cross-layer combinations in this paper), derives cost-effective solutions that achieve resilience targets at minimal costs, and provides guidelines for the design of new resilience techniques. We demonstrate the practicality and effectiveness of our framework using two diverse designs: a simple, in-order processor core and a complex, out-of-order processor core. Our results demonstrate that a carefully optimized combination of circuit-level hardening, logic-level parity checking, and micro-architectural recovery provides a highly cost-effective soft error resilience solution for general-purpose processor cores. For example, a 50x improvement in silent data corruption rate is achieved at only 2.1% energy cost for an out-of-order core (6.1% for an in-order core) with no speed impact. However, selective circuit-level hardening alone, guided by a thorough analysis of the effects of soft errors on application benchmarks, provides a cost-effective soft error resilience solution as well (with ~1% additional energy cost for a 50x improvement in silent data corruption rate).", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897996"}, {"primary_key": "4080585", "vector": [], "sparse_vector": [], "title": "PICO: mitigating heterodyne crosstalk due to process variations and intermodulation effects in photonic NoCs.", "authors": ["<PERSON> Chittamuru", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Photonic networks-on-chip (PNoCs) employ photonic waveguides with dense-wavelength-division-multiplexing (DWDM) for signal traversal and microring resonators (MRs) for signal modulation, to enable high bandwidth on-chip transfers. Unfortunately, DWDM increases susceptibility to intermodulation effects, which reduces signal-to-noise ratio (SNR) for photonic data transfers. Additionally, process variations induce variations in the width and thickness of MRs causing resonance wavelength shifts, which further reduces SNR, and creates communication errors. This paper proposes a novel framework (called PICO) for mitigating heterodyne crosstalk due to process variations and intermodulation effects in PNoC architectures. Experimental results indicate that our approach can improve the worst-case SNR by up to 4.4× and significantly enhance the reliability of DWDM-based PNoC architectures.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898063"}, {"primary_key": "4080586", "vector": [], "sparse_vector": [], "title": "A quantitative analysis on microarchitectures of modern CPU-FPGA platforms.", "authors": ["<PERSON><PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "CPU-FPGA heterogeneous acceleration platforms have shown great potential for continued performance and energy efficiency improvement for modern data centers, and have captured great attention from both academia and industry. However, it is nontrivial for users to choose the right platform among various PCIe and QPI based CPU-FPGA platforms from different vendors. This paper aims to find out what microarchitectural characteristics affect the performance, and how. We conduct our quantitative comparison and in-depth analysis on two representative platforms: QPI-based Intel-Altera HARP with coherent shared memory, and PCIe-based Alpha Data board with private device memory. We provide multiple insights for both application developers and platform designers.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897972"}, {"primary_key": "4080587", "vector": [], "sparse_vector": [], "title": "Legalization algorithm for multiple-row height standard cell design.", "authors": ["<PERSON><PERSON><PERSON>", "Chak-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>. <PERSON>"], "summary": "Typical standard cell placement algorithms assume that all cells are of the same height such that cells can be aligned along the placement rows. However, modern standard cell designs are getting more complicated and multiple-row height cell becomes more common. With multiple-row height cells, placement of cells are not independent among different rows. It turns out that most of the commonly used detailed placement and legalization techniques cannot be extended easily to handle the problem. We propose a novel algorithm in handling legalization of placement involving multiple-row height cells. The algorithm can efficiently legalize a local region of cells with various heights, which is especially useful for local cell movement, cell sizing, and buffer insertion. Experiments on the application of the technique in detailed placement show that our approach can effectively and efficiently legalize global placement results and obtain significant improvement in the objective function.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898038"}, {"primary_key": "4080588", "vector": [], "sparse_vector": [], "title": "Comprehensive optimization of scan chain timing during late-stage IC implementation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Scan chain timing is increasingly critical to test time and product cost. However, hold buffer insertions (e.g., due to large clock skew) limit scan timing improvement. Dynamic voltage drop (DVD) during scan shift further degrades scan shift timing, inducing \"false failures\" in silicon. Hence, new optimizations are needed in late stages of implementation when accurate (skew, DVD) information is available. We propose skew-aware scan ordering to minimize hold buffers, and DVD-aware gating insertion to improve scan shift timing slacks. Our optimizations at the post-CTS and post-routing stages reduce hold buffers by up to 82%, and DVD-induced timing degradation by up to 58%, with negligible area and power overheads.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897998"}, {"primary_key": "4080589", "vector": [], "sparse_vector": [], "title": "Simplifying deep neural networks for neuromorphic architectures.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Taehwan Shin"], "summary": "Deep learning using deep neural networks is taking machine intelligence to the next level in computer vision, speech recognition, natural language processing, etc. Brain-like hardware platforms for the brain-inspired computational models are being studied, but none of such platforms deals with the huge size of practical deep neural networks. This paper presents two techniques, factorization and pruning, that not only compress the models but also maintain the form of the models for the execution on neuromorphic architectures. We also propose a novel method to combine the two techniques. The proposed method shows significant improvements in reducing the number of model parameters over standalone use of each method while maintaining the performance. Our experimental results show that the proposed method can achieve 31× reduction rate without loss of accuracy for the largest layer of AlexNet.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898092"}, {"primary_key": "4080590", "vector": [], "sparse_vector": [], "title": "Invited - Heterogeneous datacenters: options and opportunities.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we present our ongoing study and deployment efforts for enabling FPGAs in datacenters. An important focus is to provide a quantitative evaluation of a wide range of heterogeneous system designs and integration options, from low-power field-programmable SoCs to server-class computer nodes plus high-capacity FPGAs, with real system prototyping and implementation on real-life applications. In the meantime, we develop a cloud-friendly programming interface and a runtime environment for efficient accelerator deployment, scheduling and transparent resource management for integration of FPGAs for large-scale acceleration across different system integration platforms to enable \"write once, execute everywhere\".", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905012"}, {"primary_key": "4080591", "vector": [], "sparse_vector": [], "title": "Statistical fault injection for impact-evaluation of timing errors on application performance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes a novel approach to modeling of gate level timing errors during high-level instruction set simulation. In contrast to conventional, purely random fault injection, our physically motivated approach directly relates to the underlying circuit structure, hence allowing for a significantly more detailed characterization of application performance under scaled frequency / voltage (including supply noise). The model uses gate level timing statistics extracted by dynamic timing analysis from the post place & route netlist of a general-purpose processor to perform instruction-aware fault injections. We employ a 28 nm OpenRISC core as a case study, to demonstrate how statistical fault injection provides a more accurate and realistic analysis of power vs. error performance.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898095"}, {"primary_key": "4080592", "vector": [], "sparse_vector": [], "title": "Invited - A box of dots: using scan-based path delay test for timing verification.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper, we describe the use of manufacturing scan-based vectors to structurally assess the frequency of any given semiconductor design, as opposed to the complex and costly effort of creating a functional set of vectors that can actually exercise all of the functions needed to accurately determine if the chip really operates at its rated or advertised frequency. Structural techniques reduce the problem to one of a finite measureable and deterministic set of tests whereas functional vectors can be somewhat subjective unless analyzed, simulated and assessed. The techniques developed and described here were developed on microprocessor designs and were then expanded to cover the general case of an ASIC, SoC, and even FPGA by using static timing analysis, automatic test pattern generation (ATPG) against a path-delay fault model, path selection from STA and using path filtering to eliminate false-paths that would result in an incorrect frequency assessment.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905001"}, {"primary_key": "4080593", "vector": [], "sparse_vector": [], "title": "A model-driven approach to warp/thread-block level GPU cache bypassing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhou", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The high amount of memory requests from massive threads may easily cause cache contention and cache-miss-related resource congestion on GPUs. This paper proposes a simple yet effective performance model to estimate the impact of cache contention and resource congestion as a function of the number of warps/thread blocks (TBs) to bypass the cache. Then we design a hardware-based dynamic warp/thread-block level GPU cache bypassing scheme, which achieves 1.68x speedup on average on a set of memory-intensive benchmarks over the baseline. Compared to prior works, our scheme achieves 21.6% performance improvement over SWL-best [29] and 11.9% over CBWT-best [4] on average.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897966"}, {"primary_key": "4080594", "vector": [], "sparse_vector": [], "title": "Quest for high-performance bufferless NoCs with single-cycle express paths and self-learning throttling.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Router buffers are the main reason for the Network-on-Chip's (NoC) scalable bandwidth, but consumes significant area and power. The SCEPTER bufferless NoC sets up single-cycle virtual express paths dynamically, allowing packets to traverse non-minimal paths without latency penalty. Using prioritization, bypassing, and throttling mechanisms, we maximize opportunities to use these paths while pushing bandwidth. For 64 and 256 nodes, we achieve 62% lower latency, 1.3x higher throughput, and 35% lower starvation over a baseline bufferless NoC for synthetic traffic. Full-system 36-core simulations show a 19% lower runtime, on-par performance to a buffered network, with 36% lower area, 33% lower power.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898075"}, {"primary_key": "4080595", "vector": [], "sparse_vector": [], "title": "nZDC: a compiler technique for near zero silent data corruption.", "authors": ["Mosle<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Exponentially growing rate of soft errors makes reliability a major concern in modern processor design. Since software-oriented approaches offer flexible protection even in off-the-shelf processes, they are attractive solutions in protecting against soft errors. Among such approaches, in-application instruction duplication based approaches have been widely used and are deemed to be the most effective. Such techniques duplicate the program assembly instructions and periodically check the results to identify possible errors. Even though early reports suggest that these achieve close to 100% protection from soft errors, we find several gaps in the protection. Existing techniques are unable to protect several important microarchitectural components, as well as a significant fraction of instructions, resulting in Silent Data Corruptions (SDCs). This paper presents nZDC or near Zero silent Data Corruption -- an effective instruction duplication based approach to protect programs from soft errors. Extensive fault injection experiments on almost all the unprotected microarchitectural components in simulated ARM Cortex A53, while executing benchmarks fromMiBench suite, demonstrate that nZDC is extremely effective, without incurring any more performance penalty than the state-of-the-art.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898054"}, {"primary_key": "4080596", "vector": [], "sparse_vector": [], "title": "Self-aligned double patterning-aware detailed routing with double via insertion and via manufacturability consideration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In 10nm technology node, self-aligned double patterning (SA DP) and triple patterning lithography (TPL) allow us to achieve minimum wiring pitch of around 45nm. While metal layers can be printed by SADP, via layer manufacturing requires TPL to maintain design rules. SADP-aware detailed routing is proposed to ensure decomposability of metal layer patterns. However, its routing solution does not automatically guarantee TPL decomposable via layers. Vias have an inherently low reliability and via failure causes a great yield loss. Double via insertion (DVI) is an effective means to increase yield by reducing via failures. With the restriction of SADP design rules and consideration of TPL decomposability for via layers, DVI becomes a more challenging problem. In this paper, we consider DVI and via layer TPL manufacturability simultaneously in SADP-aware detailed routing. The experimental results demonstrate our router can obtain 100% routability and TPL decomposable via layers with reduced dead via count.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898088"}, {"primary_key": "4080597", "vector": [], "sparse_vector": [], "title": "A fast simulator for the analysis of sub-threshold thermal noise transients.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The gate length of CMOS transistors is continuing to shrink down to the sub-10nm region and operating voltages are moving toward near-threshold and even sub-threshold values. With this trend, the number of electrons responsible for the total charge of a CMOS node is greatly reduced. As a consequence, thermal fluctuations that shift a gate from its equilibrium point may no longer have a negligible impact on circuit reliability. Time-domain analysis helps understand how transient faults affect a circuit and can guide designers in producing noise-resistant circuitry. However, modeling thermal noise in the time-domain is computationally very costly. Moreover, small fluctuations in electron occupation introduce time-varying biasing point fluctuations, increasing the modeling complexity. To address these challenges, this paper introduces a new approach to modeling thermal noise directly in the time domain by developing a series of stochastic differential equations (SDE) to model various transient effects in the presence of thermal noise. In comparisons to SPICE-based simulations, our approach can provide 3 orders of magnitude speedup in simulation time, with comparable accuracy. This simulation framework is especially valuable for detecting rare events that could translate into fault-inducing noise transients. While it is computationally infeasible to use SPICE to detect such rare events due to thermal noise, we introduce a new iterative approach that allows detecting 6σ events in a matter of a few hours.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897960"}, {"primary_key": "4080598", "vector": [], "sparse_vector": [], "title": "Invited - Airtouch: a novel single layer 3D touch sensing system for human/mobile devices interactions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Touchscreen technology plays an important role in the booming mobile devices market. Traditional touchscreen only provides 2D interactions with limited user experience. To overcome these limitations, we propose a novel 3D touch sensing system called the Airtouch system, which can recognize the movement of the finger in a 3-dimensional space. Half of the manufacturing cost is reduced by applying only single layer electrodes in the touch panel design. Moreover, an oscillator based correlated double sampling circuit is implemented as the self-capacitive sensor with bootstrapping technique to reduce inter-channel-coupling effect. Additionally, new algorithm for finger positioning is created with grouping filter invented to reduce system background noise. The demonstrated setup can successfully detect finger movement within a vertical range of 6cm and achieve a horizontal resolution up to 1cm. This system offers great potential in both gesture recognition for small-sized electronics, and advanced human interactive games for TV and mobile device.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2901902"}, {"primary_key": "4080599", "vector": [], "sparse_vector": [], "title": "Fault injection acceleration by simultaneous injection of non-interacting faults.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Fault injection is the de facto standard for evaluating the sensitivity of digital systems to transient errors. Due to various masking effects only a very small portion of the injected faults lead to system-level failures, and hence, too many faults have to be injected for achieving statistically meaningful results. At the same time, since the majority of injected faults will be masked, lots of simulation cycles will be wasted for tracking each and every injected fault separately. In this paper, we propose an opportunistic acceleration technique which evaluates the impact of multiple non-interacting faults in one workload execution. In case no failure is observed, this technique skips the evaluation of those individual faults which leads to a significant speedup. The experimental results on the Leon3 processor show that our proposed technique shortens the fault injection runtime by two orders of magnitude.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898023"}, {"primary_key": "4080600", "vector": [], "sparse_vector": [], "title": "Invited - Cross-layer approaches for soft error modeling and mitigation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Radiation-induced soft errors are major reliability threat for VLSI systems fabricated using nanoscale technologies. While soft errors are generated due to device-level interactions, they could propagate at multiple levels and impair user-visible outputs. Hence, it is crucial to evaluate the impact of these errors using a combined knowledge of various layers in a cross-layer manner. Development of a fast and accurate soft error analysis requires a detailed information about underlying technology, functionality of the hardware implementation, the system micro-architecture and architecture, and also the running application characteristics. This paper justifies the importance of cross-layer soft error modeling and mitigation by showing how existing soft error modeling techniques at various abstraction levels could be coupled to form a fast and accurate cross-layer soft error modeling platform, and accordingly, how this platform can be exploited towards low-cost design for soft error reliability.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905007"}, {"primary_key": "4080601", "vector": [], "sparse_vector": [], "title": "NVSim-VXs: an improved NVSim for variation aware STT-RAM simulation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Spin-transfer torque random access memory (STT-RAM) recently received significant attentions for its promising characteristics in cache and memory applications. As an early-stage modeling tool, NVSim has been widely adopted for simulations of emerging nonvolatile memory technologies in computer architecture research, including STT-RAM, ReRAM, PCM, etc. In this work, we introduce a new member of NVSim family -- NVSim-VXs, which enables statistical simulation of STT-RAM for write performance, errors, and energy consumption. This enhanced model takes into account the impacts of parametric variabilities of CMOS and MTJ devices and the chip operating temperature. It is also calibrated with Monte-Carlo Simulations based on macro-magnetic and SPICE models, covering five technology nodes between 22nm and 90nm. NVSim-VXs strongly supports the fast-growing needs of STT-RAM research on reliability analysis and enhancement, announcing the next important stage of NVSim development.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898053"}, {"primary_key": "4080602", "vector": [], "sparse_vector": [], "title": "Efficient performance modeling of analog integrated circuits via kernel density based sparse regression.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Huang", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the aggressive scaling of integrated circuit technology, analog performance modeling is facing enormous challenges due to high-dimensional variation space and expensive transistor-level simulation. In this paper, we propose a kernel density based sparse regression algorithm (KDSR) to accurately fit analog performance models where the modeling error is not simply Gaussian due to strong nonlinearity. The key idea of KDSR is to approximate the non-Gaussian likelihood function by using non-parametric kernel density estimation. Furthermore, we adopt Laplace distribution as our prior knowledge to enforce a sparse pattern for model coefficients. The unknown model coefficients are finally determined by using an EM type algorithm for maximum-a-posteriori (MAP) estimation. Our proposed method can be viewed as an iterative and weighted sparse regression algorithm that aims to reduce the estimation bias for model coefficients due to outliers. Our experimental results demonstrate that our proposed KDSR method can achieve superior accuracy over the conventional sparse regression method.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898013"}, {"primary_key": "4080603", "vector": [], "sparse_vector": [], "title": "Spectral graph sparsification in nearly-linear time leveraging efficient spectral perturbation analysis.", "authors": ["<PERSON><PERSON>"], "summary": "Spectral graph sparsification aims to find an ultra-sparse subgraph whose Laplacian matrix can well approximate the original Laplacian matrix in terms of its eigenvalues and eigenvectors. The resultant sparsified subgraph can be efficiently leveraged as a proxy in a variety of numerical computation applications and graph-based algorithms. This paper introduces a practically efficient, nearly-linear time spectral graph sparsification algorithm that can immediately lead to the development of nearly-linear time symmetric diagonally-dominant (SDD) matrix solvers. Our spectral graph sparsification algorithm can efficiently build an ultra-sparse subgraph from a spanning tree subgraph by adding a few \"spectrally-critical\" off-tree edges back to the spanning tree, which is enabled by a novel spectral perturbation approach and allows to approximately preserve key spectral properties of the original graph Laplacian. Extensive experimental results confirm the nearly-linear runtime scalability of an SDD matrix solver for large-scale, real-world problems, such as VLSI, thermal and finite-element analysis problems, etc. For instance, a sparse SDD matrix with 40 million unknowns and 180 million nonzeros can be solved (1E-3 accuracy level) within two minutes using a single CPU core and about 6GB memory.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898094"}, {"primary_key": "4080604", "vector": [], "sparse_vector": [], "title": "StitchUp: automatic control flow protection for high level synthesis circuits.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Soft-error detection in FPGAs typically requires replication, doubling the required area. We propose an approach which distinguishes between tolerable errors in data-flow, such-as arithmetic, and intolerable errors in control-flow, such as branches and their data-dependencies. This approach is demonstrated in a new high-level synthesis compiler pass called StitchUp, which precisely identifies the control critical parts of the design, then automatically replicates only that part. We applied StitchUp to the CHStone benchmark suite and performed exhaustive hardware fault injection in each case, finding that all control-flow errors were detected while only requiring 1% circuit area overhead in the best case.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898097"}, {"primary_key": "4080605", "vector": [], "sparse_vector": [], "title": "Nonvolatile memory design based on ferroelectric FETs.", "authors": ["<PERSON><PERSON><PERSON>", "Kaisheng Ma", "<PERSON><PERSON><PERSON>", "Xueqing Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Ferroelectric FETs (FEFETs) offer intriguing possibilities for the design of low power nonvolatile memories by virtue of their three-terminal structure coupled with the ability of the ferroelectric (FE) material to retain its polarization in the absence of an electric field. Utilizing the distinct features of FEFETs, we propose a 2-transistor (2T) FEFET-based nonvolatile memory with separate read and write paths. With proper co-design at the device, cell and array levels, the proposed design achieves non-destructive read and lower write power at iso-write speed compared to standard FERAM. In addition, the FEFET-based memory exhibits high distinguishability with six orders of magnitude difference in the read currents corresponding to the two states. Comparative analysis based on experimentally calibrated models shows significant improvement of access energy-delay. For example, at a fixed write time of 550ps, the write voltage and energy are 58.5% and 67.7% lower than FERAM, respectively. These benefits are achieved with 2.4 times the area overhead. Further exploration of the proposed FEFET memory in energy harvesting nonvolatile processors shows an average improvement of 27% in forward progress over FERAM.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898050"}, {"primary_key": "4080606", "vector": [], "sparse_vector": [], "title": "Re-target-able software power management framework using SoC data auto-generation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Heterogeneous multiprocessor SoCs (MPSoCs) are becoming very complex with greater demands for performance and low power. Advanced hardware techniques and involved software programming (>1000 registers) are employed to attain low/ultra-low power. Power management (PM) software development time is high. It is made worse with heavy rework when migrating across SoCs. In this paper we propose techniques to create a re-target-able software PM framework, independent of OS or file system, with auto-generated databases containing registers, clock tree and device power attributes from SoC IP-XACT specifications. These techniques helped achieve 90% reduction in migration time on Texas Instrument's automotive and industrial MPSoCs.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898085"}, {"primary_key": "4080607", "vector": [], "sparse_vector": [], "title": "An MPSoC for energy-efficient database query processing.", "authors": ["<PERSON>", "<PERSON>", "Benedikt <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a heterogeneous database hardware accelerator MPSoC manufactured in 28 nm SLP CMOS. The 18 mm2 chip integrates a runtime task scheduling unit for energy-efficient query processing and hierarchical power management supported by an ultra-fast dynamic voltage and frequency scaling. Four processing elements, connected by a star-mesh network-on-chip, are accelerated by an instruction set extension tailored to fundamental data-intensive applications. We evaluate the MPSoC with typical database benchmarks focusing on scans and bitmap operations. When the processing elements operate on data stored in local memories, the chip consumes 250 mW and shows a 96x energy efficiency improvement compared to state-of-the-art platforms.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897986"}, {"primary_key": "4080608", "vector": [], "sparse_vector": [], "title": "A framework for verification of SystemC TLM programs with model slicing: a case study.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we evaluate the effectiveness of model slicing to provide assurance about correctness of SystemC TLM programs. The need for such assurance is important since SystemC has become a de-facto standard for building systems with hardware/software co-design. Existing approaches that enable one to transform the given SystemC TLM program into an UPPAAL model that can be verified suffer from models that result in state space explosion. This problem becomes even more complex when verifying fault-tolerance. Model slicing has the potential to provide a solution to this problem. Therefore, we focus on developing a model slicer that extends existing work on model slicing and combines it with tools to generate UPPAAL models from SystemC TLM programs and tools to add the impact of faults to those UPPAAL models. The experimental results show that with the proposed framework, the designer is capable of verifying even very complex SystemC TLM models, which would have been impossible without the proposed approach.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897961"}, {"primary_key": "4080609", "vector": [], "sparse_vector": [], "title": "A low-power dynamic divider for approximate applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, a low-power, low-error divider design is proposed that can achieve significant power and area savings, while introducing insignificant inaccuracies to the output. The design of our divider is highly scalable, offering a wide range of power and inaccuracy trade-offs based on the application requirements. Furthermore, the proposed divider has a lower delay compared to the accurate design, enabling its use on the critical path. We theoretically analyze the error of our design as a function of its configuration, and we thoroughly evaluate the error and power characteristics of our divider in a standalone case and demonstrate that the proposed design can achieve up to 70% in power savings, while introducing an mean average absolute error of only 3.08%. We also implement three image-processing applications in hardware using our divider and conclude that use of the proposed divider will not perceptibly impact their quality-of-service while achieving power benefits of up to 75%.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897965"}, {"primary_key": "4080610", "vector": [], "sparse_vector": [], "title": "Random modulo: a new processor cache design for real-time critical systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Cache memories have a huge impact on software's worst-case execution time (WCET). While enabling the seamless use of caches is key to provide the increasing levels of (guaranteed) performance required by automotive software, caches complicate timing analysis. In the context of Measurement-Based Probabilistic Timing Analysis (MBPTA) -- a promising technique to ease timing analyis of complex hardware -- we propose Random Modulo (RM), a new cache design that provides the probabilistic behavior required by MBPTA and with the following advantages over existing MBPTA-compliant cache designs: (i) an outstanding reduction in WCET estimates, (ii) lower latency and area overhead, and (iii) competitive average performance w.r.t conventional caches.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898076"}, {"primary_key": "4080611", "vector": [], "sparse_vector": [], "title": "Invited - Integrated millimeter-wave/terahertz sensor systems for near-field IoT.", "authors": ["<PERSON><PERSON>"], "summary": "The emergence of Internet of Things (IoT) has brought forth new opportunities by seamlessly integrating the physical world using computing, sensing, and wireless networks, transforming it into a cyber-physical system. An essential building block enabling an IoT is a sensing system. The use of \"near-field communication (NFC)\" has gained attention in recent years, as it enables low-power short-range sensing and wireless transfer of low content information. The NFC, however, is inapplicable in scenarios where the real-time high-resolution image or video of the object(s) needs to be sensed. The penetration of THz waves through many materials, which are impervious for visible light makes THz imaging akin to X-rays, except that THz radiation is non-ionizing and therefore not harmful to the object being imaged, especially living tissues. This special issue paper presents an overview of recent advances in the development of siliconbased mm-wave/THz imaging sensors for near-field IoT applications.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2907985"}, {"primary_key": "4080612", "vector": [], "sparse_vector": [], "title": "A real-time energy-efficient superpixel hardware accelerator for mobile computer vision applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Superpixel generation is a common preprocessing step in vision processing aimed at dividing an image into non-overlapping regions. Simple Linear Iterative Clustering (SLIC) is a commonly used superpixel algorithm that offers a good balance between performance and accuracy. However, the algorithm's high computational and memory bandwidth requirements result in performance and energy efficiency that do not meet the requirements of real-time embedded applications. In this work, we explore the design of an energy-efficient superpixel accelerator for real-time computer vision applications. We propose a novel algorithm, Subsampled SLIC (S-SLIC), that uses pixel subsampling to reduce the memory bandwidth by 1.8×. We integrate S-SLIC into an energy-efficient superpixel accelerator and perform an in-depth design space exploration to optimize the design. We completed a detailed design in a 16nm FinFET technology using commercially-available EDA tools for high-level synthesis to map the design automatically from a C-based representation to a gate-level implementation. The proposed S-SLIC accelerator achieves real-time performance (30 frames per second) with 250× better energy efficiency than an optimized SLIC software implementation running on a mobile GPU.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897974"}, {"primary_key": "4080613", "vector": [], "sparse_vector": [], "title": "Standard lattices in hardware.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>ire O&apo<PERSON>;Neill", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Lattice-based cryptography has gained credence recently as a replacement for current public-key cryptosystems, due to its quantum-resilience, versatility, and relatively low key sizes. To date, encryption based on the learning with errors (LWE) problem has only been investigated from an ideal lattice standpoint, due to its computation and size efficiencies. However, a thorough investigation of standard lattices in practice has yet to be considered. Standard lattices may be preferred to ideal lattices due to their stronger security assumptions and less restrictive parameter selection process.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898037"}, {"primary_key": "4080614", "vector": [], "sparse_vector": [], "title": "Dot-product engine for neuromorphic computing: programming 1T1M crossbar to accelerate matrix-vector multiplication.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>ng <PERSON>e", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Vector-matrix multiplication dominates the computation time and energy for many workloads, particularly neural network algorithms and linear transforms (e.g, the Discrete Fourier Transform). Utilizing the natural current accumulation feature of memristor crossbar, we developed the Dot-Product Engine (DPE) as a high density, high power efficiency accelerator for approximate matrix-vector multiplication. We firstly invented a conversion algorithm to map arbitrary matrix values appropriately to memristor conductances in a realistic crossbar array, accounting for device physics and circuit issues to reduce computational errors. The accurate device resistance programming in large arrays is enabled by close-loop pulse tuning and access transistors. To validate our approach, we simulated and benchmarked one of the state-of-the-art neural networks for pattern recognition on the DPEs. The result shows no accuracy degradation compared to software approach (99 % pattern recognition accuracy for MNIST data set) with only 4 Bit DAC/ADC requirement, while the DPE can achieve a speed-efficiency product of 1,000× to 10,000× compared to a custom digital ASIC.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898010"}, {"primary_key": "4080615", "vector": [], "sparse_vector": [], "title": "Utilization bounds on allocating rate-monotonic scheduled multi-mode tasks on multiprocessor systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Formal models used for representing recurrent real-time processes have traditionally been characterized by a collection of jobs that are released periodically. However, such a modeling may result in resource under-utilization in systems whose behaviors are not entirely periodic. For instance, tasks in cyber-physical system (CPS) may change their service levels, e.g., periods and/or execution times, to adapt to the changes of environments. In this work, we study a model that is a generalization of the periodic task model, called multi-mode task model: a task has several modes specified with different execution times and periods to switch during runtime, independent of other tasks. Moreover, we study the problem of allocating a set of multi-mode tasks on a homogeneous multiprocessor system. We present a scheduling algorithm using any reasonable allocation decreasing (RAD) algorithm for task allocations for scheduling multi-mode tasks on multiprocessor systems. We prove that this algorithm achieves 38% utilization for implicit-deadline rate-monotonic (RM) scheduled multi-mode tasks on multiprocessor systems.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898108"}, {"primary_key": "4080616", "vector": [], "sparse_vector": [], "title": "Area optimization of resilient designs guided by a mixed integer geometric program.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Timing resilient designs can remove variation margins by adding error detecting logic (EDL) that detects timing errors when execution completes within a resiliency window. Speeding up near-critical-paths during logic synthesis can reduce the amount of EDL needed but at the cost of increasing logic area. This creates a logic optimization strategy called resynthesis. This paper proposes a gate-sizing based mixed integer geometric programming framework to analytically model and optimize paths during resynthesis. We evaluate our approach on a set of ISCAS89 benchmarks and compare the overall area improvement after resynthesis guided by our mathematical model versus a previously published naive brute-force approach. Our experimental results demonstrate that our approach achieves up to 11% larger average area improvement.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897990"}, {"primary_key": "4080617", "vector": [], "sparse_vector": [], "title": "MIRROR: symmetric timing analysis for real-time tasks on multicore platforms with shared resources.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The emergence of multicore and manycore platforms poses a big challenge for the design of real-time embedded systems, especially for timing analysis. We observe in this paper that response-time analysis for multicore platforms with shared resources can be symmetrically approached from two perspectives: a core-centric and a shared-resource-centric perspective. The common \"core-centric\" perspective is that a task executes on a core until it suspends the execution due to shared resource accesses. The potentially less intuitive \"shared-resource-centric\" perspective is that a task performs requests on shared resources until suspending itself back to perform computation on its respective core.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898046"}, {"primary_key": "4080618", "vector": [], "sparse_vector": [], "title": "Efficient performance modeling via Dual-Prior Bayesian Model Fusion for analog and mixed-signal circuits.", "authors": ["<PERSON><PERSON> Huang", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we propose a novel Dual-Prior Bayesian Model Fusion (DP-BMF) algorithm for performance modeling. Different from the previous BMF methods which use only one source of prior knowledge, DP-BMF takes advantage of multiple sources of prior knowledge to fully exploit the available information and, hence, further reduce the modeling cost. Based on a graphical model, an efficient Bayesian inference is developed to fuse two different prior models and combine the prior information with a small number of training samples to achieve high modeling accuracy. Several circuit examples demonstrate that the proposed method can achieve up to 1.83× cost reduction over the traditional one-prior BMF method without surrendering any accuracy.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898014"}, {"primary_key": "4080619", "vector": [], "sparse_vector": [], "title": "Timing-driven cell placement optimization for early slack histogram compression.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "As interconnects dominate circuit performance in modern chip designs, placement becomes an essential stage in optimizing timing. Recent timing-driven placement (TDP) techniques focus mainly on optimizing late slack rather than early slack. This paper presents a TDP algorithm to improve the early slack while preserving an optimized late slack. The preservation is achieved by accurately predicting optimal Steiner tree topologies after each move in our TDP algorithm. An optimality-preserving pruning scheme for each move is proposed to speed up the optimization process, without sacrificing the solution quality. Experimental results show that our algorithm can substantially improve the early slacks and the overall quality scores of the top-2 winning placers of the 2015 ICCAD Incremental Timing-Driven Placement Contest, while preserving their late slacks.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898105"}, {"primary_key": "4080620", "vector": [], "sparse_vector": [], "title": "Efficient probing schemes for fine-pitch pads of InFO wafer-level chip-scale package.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the increasing demand of super high scale of integration and small form factor in advanced semiconductor products, especially those that integrate DRAM and logic dies, 3D IC and Wafer-Level Chip-Scale Packaging (WLCSP) are considered promising approaches. In Integrated Fan-Out (InFO) WLCSP, a large number of fine-pitch pads, where neighboring pads cannot be probed simultaneously due to insufficient pitch, are used as the contact interfaces of inter-die interconnections. If the fine-pitch pads cannot be probed, the interconnections between the pads and boundary scan cells (BSCs) cannot be tested, which can lead to higher defect level. From industrial investigation, untested fine-pitch pads lead to 1-2% test coverage loss. To improve the overall test coverage, in this paper, we propose a pre-bond probing methodology for fine-pitch pads of InFO WLCSP. By the proposed probing schemes, open/short faults on the interconnects between the fine-pitch pads and BSCs can be all tested by the ATE. Moreover, for short faults that only occur between adjacent pads (interconnects), we propose a grouping method to determine the test patterns at each probing stage, which can minimize the test time. We also show that our method can achieve 100% test coverage of open/short faults.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898015"}, {"primary_key": "4080621", "vector": [], "sparse_vector": [], "title": "Physics-based full-chip TDDB assessment for BEOL interconnects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhongdong Qi", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "As technology advances, Time-Dependent Dielectric Breakdown (TDDB) has become one of the major reliability threats for Copper/low-k interconnects. This article presents a novel approach, techniques, and flow for the physics-based chip-scale assessment of backend low-k TDDB. In our work, the breakdown development is considered as the complementary combination of electric current path generation by means of diffusing metal ions and field-based hoping conductivity of the current carriers. It replaces the widely accepted across-layout electrostatic field based TDDB assessment. As a result, the model generated time-to-failure (TTF) is governed by kinetics of the electric current path generation, which is controlled by a time-dependent minimum metal ion concentration in the inter-metal dielectrics (IMD) gap-fill. Finite element analysis (FEA)-based simulations are used for populating the set of lookup tables, which provide a time to breakdown for any interconnect pattern with given geometries and voltages. A pattern-matching technique is used for extracting from the layout all patterns belonging to different classes of pattern shapes with different geometries, locations and electric loads. Experimental results obtained on a test chip show that upon the calibration the proposed flow provides a capability to evaluate chip-scale low-k TDDB reliability based on the calculated TTF and detect most leaking shapes in the layout.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898062"}, {"primary_key": "4080622", "vector": [], "sparse_vector": [], "title": "A distributed timing analysis framework for large designs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Given ever-increasing circuit complexities, recent trends are driving the requirement for distributed timing analysis (DTA) in electronic design automation (EDA) tools. However, DTA has received little research attention so far and remains a critical problem. In this paper, we introduce a DTA framework for large designs. Our framework supports (1) general design partitions in distributed file systems, (2) non-blocking IO with event-driven loop for effective communication and computation overlap, and (3) an efficient messaging interface between application and network layers. The effectiveness and scalability of our framework has been evaluated on large hierarchical industry designs over a cluster with hundreds of machines.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897959"}, {"primary_key": "4080623", "vector": [], "sparse_vector": [], "title": "Privacy preserving localization for smart automotive systems.", "authors": ["Siam <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents the first provably secure localization method for smart automotive systems. Using this method, a lost car can compute its location with assistance from three nearby cars while the locations of all the participating cars including the lost car remain private. This localization application is one of the very first location-based services that does not sacrifice accuracy to maintain privacy. The secure location is computed using a protocol utilizing <PERSON>'s Garbled Circuit (GC) that allows two parties to jointly compute a function on their private inputs. We design and optimize GC netlists of the functions required for computation of location by leveraging conventional logic synthesis tools. Proof-of-concept implementation of the protocol shows that the complete operation can be performed within only 550 ms. The fast computing time enables practical localization of moving cars.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898071"}, {"primary_key": "4080624", "vector": [], "sparse_vector": [], "title": "Exploration of associative power management with instruction governed operation for ultra-low power design.", "authors": ["<PERSON><PERSON><PERSON>", "Yuan<PERSON> Fan", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper explores a novel associative low power operation where instructions govern the operation of on-chip regulators in real time. Based on explicit association between long delay instruction patterns and hardware performance, an instruction based power management scheme is developed with energy models formulated for deriving the energy efficiency of the associative operation. The proposed system scheme is demonstrated using a low power microprocessor design with an integrated switched capacitor regulator in 45nm CMOS technology. Simulations on benchmark programs show a power saving of around 14% from the proposed scheme. A novel compiler optimization strategy is also proposed to further improve the energy efficiency.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898021"}, {"primary_key": "4080625", "vector": [], "sparse_vector": [], "title": "A semantics-aware design for mounting remote sensors on mobile systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Application paradigms will increasingly exceed a mobile device's physical boundaries. This paper presents a system solution for a mobile device to mount remote sensors on other devices. Our design is generic to mobile senor stacks, thus supporting unmodified apps and commodity sensors. Furthermore, it uses an asynchronous access model to facilitate semantics passing and data reporting in between. Such semantic information allows the development of an energy-efficient reporting policy for remote sensing applications. The results of experiments conducted on commercial Android smartphones with popular apps demonstrate that our design is very efficient in terms of energy consumption and completion time.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897975"}, {"primary_key": "4080626", "vector": [], "sparse_vector": [], "title": "Real-time co-scheduling of multiple dataflow graphs on multi-processor systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It is challenging to schedule multiple dataflow applications concurrently on multi-processor embedded systems with processor sharing. As a viable solution, an approach has been proposed recently, in which the dataflow graphs are transformed into a set of independent realtime tasks. However, it may produce poor resource utilization and excessive buffer usage. Alternatively, we propose a novel two-phase scheduling technique. In the first phase, a set of static schedules is produced for each dataflow considering the resource sharing possibility; Then, we use a meta-heuristic to find the combination of per-graph schedules to minimize the resource requirement by processor sharing. We show that the proposed technique exhibits better resource and buffer efficiency.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898077"}, {"primary_key": "4080627", "vector": [], "sparse_vector": [], "title": "PDS: pseudo-differential sensing scheme for STT-MRAM.", "authors": ["<PERSON>", "<PERSON>gt<PERSON> Pang", "<PERSON><PERSON>", "Weifeng Lv", "<PERSON><PERSON><PERSON>", "Guangyu Sun", "<PERSON><PERSON><PERSON> Zhao"], "summary": "STT-MRAM has been considered as one of the most promising nonvolatile memory candidates in the next-generation of computer architecture. However, the read reliability and dynamic write power concerns greatly hinder its practical application. In this paper, we propose a synergistic solution, namely pseudo-differential sensing (PDS), to jointly address these two concerns. Three techniques, including cell cluster, asymmetric sensing amplifier (ASA) and self-error-detection-correction (SEDC), are proposed to implement the PDS concept. Our experimental results show that the PDS scheme with the 3T3MTJ cell cluster can reduce the area (~21.7%) and write power (~25.6%) of the differential sensing (DS) scheme while improve the read reliability (read margin, ~35.6%) of the typical sensing (TS) scheme for a 16 Mbit cache. Furthermore, the PDS scheme with the 1T3MTJ cell cluster can outperform both the TS and DS schemes in terms of area (~40.0%, ~66.1%), read latency (~16.6%, ~32.1%), read power (~16.7%, ~37.1%), write latency (~5.4%, 16.3%) and write power (~18.6%, ~43.4%).", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898058"}, {"primary_key": "4080628", "vector": [], "sparse_vector": [], "title": "Reducing control bit overhead for X-masking/X-canceling hybrid architecture via pattern partitioning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "An X-masking scheme prevents unknown (X) values from shifting into an output response compactor, whereas an X-canceling MISR methodology allows X's to enter the compactor, but then cancels them out through selective XORing. However, both approaches require significantly high volume of the control bits to remove X values to generate X-free output signatures. This paper proposes a method to reduce the control bit overhead by combining X-masking and X-canceling methodologies and exploiting the fact that unknown values tend to have high correlation in the scan cells. In this paper, correlation is considered across whole patterns in order to enhance reuse of control bits. The proposed hybrid method of X-canceling and X-masking reduces test time without losing fault coverage. The experimental results show that the proposed method significantly reduces control bits and test time compared to a conventional X-canceling MISR methodology.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898078"}, {"primary_key": "4080629", "vector": [], "sparse_vector": [], "title": "Similarity-based wakeup management for mobile systems in connected standby.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Resident applications, which autonomously awaken mobile devices, can gradually and imperceptibly drain device batteries. This paper introduces the concept of alarm similarity into wakeup management for mobile systems in connected standby. First, we define hardware similarity to reflect the degree of energy savings and time similarity to reflect the impact on user experience. We then propose a policy that aligns alarms based on their similarity to save standby energy while maintaining the quality of the user experience. Finally, we integrate our design into Android and conduct extensive experiments on a commercial smartphone running popular mobile apps. The results demonstrate that our design can further extend the standby time achieved with Android's native policy by up to one-third.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898091"}, {"primary_key": "4080630", "vector": [], "sparse_vector": [], "title": "A high-resolution side-channel attack on last-level cache.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently demonstrated side-channel attacks on shared Last Level Caches (LLCs) work under a number of constraints on both the system and the victim behavior that limit their applicability. This paper demonstrates on a real system a new high-resolution LLC side channel attack that relaxes some of these assumptions. Specifically, we introduce and exploit new techniques to achieve high-resolution tracking of the victim accesses to enable attacks on ciphers where critical events have a small cache footprint. We compare the quality of the side-channel in our attack to that obtained using Flush+ Reload attacks, which are significantly more precise but work only when the sensitive data is shared between the attacker and the victim. We show that our attack frequently obtains an equal quality channel, which we also confirmed by reconstructing the victim cryptographic key.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897962"}, {"primary_key": "4080631", "vector": [], "sparse_vector": [], "title": "Dynamic energy-accuracy trade-off using stochastic computing in deep neural networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents an efficient DNN design with stochastic computing. Observing that directly adopting stochastic computing to DNN has some challenges including random error fluctuation, range limitation, and overhead in accumulation, we address these problems by removing near-zero weights, applying weight-scaling, and integrating the activation function with the accumulator. The approach allows an easy implementation of early decision termination with a fixed hardware design by exploiting the progressive precision characteristics of stochastic computing, which was not easy with existing approaches. Experimental results show that our approach outperforms the conventional binary logic in terms of gate area, latency, and power consumption.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898011"}, {"primary_key": "4080632", "vector": [], "sparse_vector": [], "title": "Invited - Cross-layer modeling and optimization for electromigration induced reliability.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hengyang Zhao", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "In this paper, we propose a new approach for cross-layer electromigration (EM) induced reliability modeling and optimization at physics, system and datacenter levels. We consider a recently proposed physics-based electromigration (EM) reliability model to predict the EM reliability of full-chip power grid networks for long-term failures. We show how the new physics-based dynamic EM model at the physics level can be abstracted at the system level and even at the datacenter level. Our datacenter system-level power model is based on the BigHouse simulator. To speed up the online optimization for energy in a datacenter, we propose a new combined datacenter power and reliability compact model using a learning based approach in which a feed-forward neural network (FNN) is trained to predict energy and long term reliability for each processor under datacenter scheduling and workloads. To optimize the energy and reliability of a datacenter, we apply the efficient adaptive Q-learning based reinforcement learning method. Experimental results show that the proposed compact models for the datacenter system trained with different workloads under different cluster power modes and scheduling policies are able to build accurate energy and lifetime. Moreover, the proposed optimization method effectively manages and optimizes data-center energy subject to reliability, given power budget and performance.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905010"}, {"primary_key": "4080633", "vector": [], "sparse_vector": [], "title": "Designing approximate circuits using clock overgating.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Approximate computing is an emerging paradigm to improve the efficiency of computing systems by leveraging the intrinsic resilience of applications to their computations being executed in an approximate manner. Prior efforts on approximate hardware design have largely focused on circuit-level techniques. We propose a new approach, clock overgating, for the design of approximate circuits at the Register Transfer Level (RTL). The key idea is to gate the clock signal to selected Flip-Flops (FFs) in the circuit, even during execution cycles in which the circuit functionality is sensitive to their state. This saves power in the clock tree, the FF itself and in its downstream logic, while a quality loss ensues if the erroneous FF state propagates to the circuit output. We develop a systematic methodology to identify an energy-efficient overgating configuration for any given circuit and quality constraint. Towards this end, we develop 3 key strategies --- significance-based overgating, grouping FFs into overgating islands, and utilizing internal signals of the circuit as triggers for overgating --- that efficiently prune the large space of possible overgating configurations. We evaluate clock overgating by designing approximate versions of 6 machine learning accelerators, and demonstrate energy benefits of 1.36× on average (and upto 1.80×) for negligible (<0.5%) loss in application quality (classification accuracy).", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898005"}, {"primary_key": "4080634", "vector": [], "sparse_vector": [], "title": "Information dispersion for trojan defense through high-level synthesis.", "authors": ["S. T. Choden Konigsmark", "<PERSON><PERSON>", "<PERSON>"], "summary": "Emerging technologies such as the Internet of Things (IoT) heavily rely on hardware security for data and privacy protection. However, constantly increasing integration complexity requires automatic synthesis to maintain the pace of innovation. We introduce the first High-Level Synthesis (HLS) flow that produces a security enhanced hardware design to directly prevent Hardware Trojan Horse (HTH) injection by a malicious foundry. Through analysis of entropy loss and criticality decay, the presented algorithms implement highly efficient resource-targeted information dispersion to counter HTH insertion. The flow is evaluated on existing HLS benchmarks and a new IoT-specific benchmark and shows significant resource savings.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898034"}, {"primary_key": "4080635", "vector": [], "sparse_vector": [], "title": "ageOpt-RMT: compiler-driven variation-aware aging optimization for redundant multithreading.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reliability optimization in the nano-era needs to account for multiple reliability concerns. Redundant Multithreading (RMT) has emerged as a promising technique to mitigate soft-errors in multi-cores. Since variation- and aging-unawareness during RMT may increase aging of slow cores due to high utilization or lead to unbalanced aging under varying workload scenarios, we propose to leverage variations in vulnerability and duty cycle by means of multiple compiled versions. We perform variation-aware task mapping to proactively reduce the aging of slower cores and thereby maintaining the minimum processing capabilities. Afterwards, we perform an aging-aware activation/deactivation of RMT considering tasks' variable resilience properties and select appropriate reliable versions for the mapped tasks. Experimental results demonstrate that compared to state-of-the-art aging-unaware RMT techniques, our ageOpt-RMT provides improved and balanced aging profiles by 2x on average.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897980"}, {"primary_key": "4080636", "vector": [], "sparse_vector": [], "title": "Leveraging FDSOI through body bias domain partitioning and bias search.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In FDSOI, sophisticated body biasing schemes can greatly reduce leakage or improve performance as well as efficiency. This paper proposes algorithms to determine body bias domain candidates which then merge those to reach a desired number of domains. Domain candidates are determined using an activation based approach, analyzing mapped verilog netlists to identify which parts of the design are used under specified conditions. Body bias domain partitionings are then determined based on activation and the timing of the partitioned parts. The algorithms include a body bias assignment algorithm to reach given timing goals with multiple domains and cross-domain resource sharing. The approach is compatible with any synthesis optimization and is resource sharing aware. Using an implementation of the proposed algorithms, overall leakage can be significantly reduced in all scenarios while obtaining the same benefits of body biasing. The method is evaluated in STMicro's 28nm FDSOI and Renesas's 65nm SOTB.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898039"}, {"primary_key": "4080637", "vector": [], "sparse_vector": [], "title": "Probabilistic bug-masking analysis for post-silicon tests in microprocessor verification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Ark<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Post-silicon validation has become essential in catching hard-to-detect, rarely-occurring bugs that have slipped through pre-silicon verification. Post-silicon validation flows, however, are challenged by limited signal observability, which impacts their ability of diagnosing and detecting bugs. Indeed, bug manifestations during the execution of constrained-random tests may be masked and be unobservable from the test's outputs. The ability to evaluate the bug-masking rate of a test provides great value in generating and/or selecting effective tests for high coverage regressions.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898072"}, {"primary_key": "4080638", "vector": [], "sparse_vector": [], "title": "Accelerating soft-error-rate (SER) estimation in the presence of single event transients.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Radiation-induced soft errors have posed an ever increasing reliability challenge as device dimensions keep shrinking in advanced CMOS technology. Therefore, it is imperative to devise fast and accurate soft error rate (SER) estimation methods. Previous works mainly focus on improving the accuracy of the SER results, whereas the speed improvement is limited to partitioning and parallel processing. This paper presents an efficient SER estimation framework for combinational logic circuits in the presence of single-event transients (SETs). A novel top-down memoization algorithm is proposed to accelerate the propagation of SETs. Experimental results of a variety of benchmark circuits demonstrate that the proposed approach achieves up to 560.2X times speedup with less than 3% difference in terms of SER results compared with the baseline algorithm.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897976"}, {"primary_key": "4080639", "vector": [], "sparse_vector": [], "title": "Performance-aware task scheduling for energy harvesting nonvolatile processors considering power switching overhead.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Li", "Daming <PERSON>", "Jingtong Hu", "<PERSON><PERSON><PERSON>"], "summary": "Nonvolatile processors have manifested strong vitality in battery-less energy harvesting sensor nodes due to their characteristics of zero standby power, resilience to power failures and fast read/write operations. However, I/O and sensing operations cannot store their system states after power off, hence they are sensitive to power failures and high power switching overhead is induced during power oscillation, which significantly degrades the system performance. In this paper, we propose a novel performance-aware task scheduling technique considering power switching overhead for energy harvesting nonvolatile processors. We first give the analysis of the power switching overhead on energy harvesting sensor nodes. Then, the scheduling problem is formulated by MILP (Mixed Integer Linear Programming). Furthermore, a task splitting strategy is adopted to improve the performance and an heuristic scheduling algorithm is proposed to reduce the problem complexity. Experimental results show that the proposed scheduling approach can improve the performance by 14% on average compared to the state-of-the-art scheduling strategy. With the employment of the task splitting approach, the execution time can be further reduced by 10.6%.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898059"}, {"primary_key": "4080640", "vector": [], "sparse_vector": [], "title": "High-level synthesis for micro-electrode-dot-array digital microfluidic biochips.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Po-<PERSON><PERSON><PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A digital microfluidic biochip (DMFB) is an attractive technology platform for automating laboratory procedures in biochemistry. However, today's DMFBs suffer from several limitations: (i) constraints on droplet size and the inability to vary droplet volume in a fine-grained manner; (ii) the lack of integrated sensors for real-time detection; (iii) the need for special fabrication processes and reliability/yield concerns. To overcome the above problems, DMFBs based on a micro-electrode-dot-array (MEDA) architecture have recently been demonstrated. However, due to the inherent differences between today's DMFBs and MEDA, existing synthesis solutions cannot be utilized for MEDA-based biochips. We present the first biochip synthesis approach that can be used for MEDA. The proposed synthesis method targets operation scheduling, module placement, routing of droplets of various sizes, and diagonal movement of droplets in a two-dimensional array. Simulation results using benchmarks and experimental results using a fabricated MEDA biochip demonstrate the effectiveness of the proposed co-optimization technique.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898028"}, {"primary_key": "4080641", "vector": [], "sparse_vector": [], "title": "HW/SW co-design of nonvolatile IO system in energy harvesting sensor nodes for optimal data acquisition.", "authors": ["<PERSON>ew<PERSON>", "<PERSON><PERSON>", "Daming <PERSON>", "<PERSON>", "<PERSON><PERSON>", "Xi<PERSON>", "Wenyu Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Energy harvesting has been widely investigated as a promising alternative for future wearable sensors or internet-of-things. However, power and performance overhead is induced when IO operations are interrupted by power failures because non-preemptive characteristic of IO operations causes expensive re-executions. Furthermore, the state-of-art IO devices need long and power hungry initializing process, which makes IO operations inefficient in transient powered systems. This paper proposed a HW/SW co-design approach for nonvolatile IO system to maximize data acquisition. A ferroelectric flip-flop based nonvolatile IO architecture is adopted to reduce IO initialization overhead by 3-4 orders of magnitude. Based on the nonvolatile IO interface, we further formulate the optimal data acquisition as an INLP problem and a risk-aware online scheduler is presented to solve the problem efficiently. Experimental results show that the proposed HW/SW co-design architecture improves data acquisition by 2-5 times compared with conventional HW/SW architecture.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898029"}, {"primary_key": "4080642", "vector": [], "sparse_vector": [], "title": "Practical public PUF enabled by solving max-flow problem on chip.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The execution-simulation gap (ESG) is a fundamental property of public physical unclonable function (PPUF), which exploits the time gap between direct IC execution and computer simulation. ESG needs to consider both advanced computing scheme, including parallel and approximate computing scheme, and IC physical realization. In this paper, we propose a novel PPUF design, whose execution is equivalent to solving the hard-to-parallel and hard-to-approximate max-flow problem in a complete graph on chip. Thus, max-flow problem can be used as the simulation model to bound the ESG rigorously. To enable an efficient physical realization, we propose a crossbar structure and adopt source degeneration technique to map the graph topology on chip. The difference on asymptotic scaling between execution delay and simulation time is examined in the experimental results. The measurability of output difference is also verified to prove the physical practicality.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898067"}, {"primary_key": "4080643", "vector": [], "sparse_vector": [], "title": "A Monte Carlo simulation flow for SEU analysis of sequential circuits.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "An efficient methodology for soft error analysis of sequential circuits based on <PERSON> Carlo sampling is proposed. It uses nested sampling for faster statistical convergence: it samples only from the workload space and statically evaluates the conditional probability over the subspace of particle strike and circuit parameters. A novel check on the stationarity of machine state sequence to reduce the number of samples to convergence is introduced. The flow combines logic simulation for latch-level error propagation and stationarity diagnostic and an improved combinational error simulator with a new masking model based on signal controllability.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897967"}, {"primary_key": "4080644", "vector": [], "sparse_vector": [], "title": "Pinatubo: a processing-in-memory architecture for bulk bitwise operations in emerging non-volatile memories.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Processing-in-memory (PIM) provides high bandwidth, massive parallelism, and high energy efficiency by implementing computations in main memory, therefore eliminating the overhead of data movement between CPU and memory. While most of the recent work focused on PIM in DRAM memory with 3D die-stacking technology, we propose to leverage the unique features of emerging non-volatile memory (NVM), such as resistance-based storage and current sensing, to enable efficient PIM design in NVM. We propose Pinatubo1, a Processing In Non-volatile memory ArchiTecture for bUlk Bitwise Operations. Instead of integrating complex logic inside the cost-sensitive memory, Pinatubo redesigns the read circuitry so that it can compute the bitwise logic of two or more memory rows very efficiently, and support one-step multi-row operations. The experimental results on data intensive graph processing and database applications show that Pinatubo achieves a ~500× speedup, ~28000× energy saving on bitwise operations, and 1.12× overall speedup, 1.11× overall energy saving over the conventional processor.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898064"}, {"primary_key": "4080645", "vector": [], "sparse_vector": [], "title": "Efficient design space exploration via statistical sampling and AdaBoost learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Yao", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Design space exploration (DSE) has become a notoriously difficult problem due to the exponentially increasing size of design space of microprocessors and time-consuming simulations. To address this issue, machine learning techniques have been widely employed to build predictive models. However, most previous approaches randomly sample the training set leading to considerable simulation cost and low prediction accuracy. In this paper, we propose an efficient and precise DSE methodology by combining statistical sampling and Adaboost learning technique. The proposed method includes three phases. (1) Firstly, orthogonal design based feature selection is employed to prune design space. (2) Sencondly, an orthogonal array based training data sampling method is introduced to select the representative configurations for simulation. (3) Finally, a new active learning approach ActBoost is proposed to build predictive model. Evaluations demonstrate that the proposed framework is more efficient and precise than state-of-art DSE techniques.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898012"}, {"primary_key": "4080646", "vector": [], "sparse_vector": [], "title": "Relevance vector and feature machine for statistical analog circuit characterization and built-in self-test optimization.", "authors": ["Hong<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Aiding design and test optimization of analog circuits requires accurate models that can reliably capture complex dependencies of circuit performances on essential circuit and device parameters, and test signatures. We present a novel Bayesian learning technique, namely relevance vector and feature machine (RVFM), for characterizing analog circuits with sparse statistical regression models. RVFM not only produces accurate models learned from a moderate amount of simulation or measurement data, but also computes a probabilistically inferred weighting factor quantifying the criticality of each parameter as part of the overall learning framework, hence offering a powerful enabler for variability modeling, failure diagnosis, and test development. Compared to other popular learning-based techniques, the proposed RVFM produces more accurate models, requires less amount of training data, and extracts more reliable parametric ranking. The effectiveness of RVFM is demonstrated in terms of the statistical variability modeling of a low-dropout regulator (LDO) and the built-in self-test (BIST) development of a charge-pump phase-locked loop (PLL).", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898081"}, {"primary_key": "4080647", "vector": [], "sparse_vector": [], "title": "Invited - Cooperation or competition?: coexistence of safety and security in next-generation ethernet-based automotive networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Safety is traditionally the most relevant property for automotive systems, and it is further enhanced by Advanced Driver Assistance Systems (ADAS) in modern automotive systems. To support ADAS and other advanced autonomous functions, automotive electronic systems become more distributed and connected than ever, with in-vehicle architecture or Vehicle-to-X (V2X) communication. These connections create a variety of interfaces which become breeding grounds for security attacks. Accordingly, security becomes a rising issue for automotive systems. In this paper, we address safety and security together, especially their interactions, in Ethernet-based automotive networks which are believed to be the next-generation automotive networks since they are able to provide high bandwidths, certain timing guarantees, and well-developed technologies. We discuss the interactions between safety and security in three problems: secret key management, frame replication and elimination, and Virtual Local Area Network (VLAN) segmentation. We demonstrate that safety and security can work together, but sometimes there is a trade-off between them. This indicates that safety cannot stand alone without considering security, and network security is a necessary component of system security. Towards safer and securer automotive systems, safety and security should be considered together during design stages of automotive systems.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905006"}, {"primary_key": "4080648", "vector": [], "sparse_vector": [], "title": "Invited - A 2.2 GHz SRAM with high temperature variation immunity for deep learning application under 28nm.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Cho<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "With the coming era of Big Data, hardware implementation of machine learning has become attractive for many applications, such as real-time object recognition and face recognition. The implementation of machine learning algorithms needs intensive memory access, and SRAM is critical for the overall performance. This paper proposes a new design of high speed SRAM for machine learning purposes. With fast access time (cycle time: 650 ps, access time: 350 ps), low sensitivity to temperature variation and high configurability (less than 10% performance difference between 125_rcw_tt vs 0_rcw_tt), the proposed SRAM is a better candidate for hardware machine learning system than the conventional SRAM. Compared with Samsung HL 152, our design has smaller size (121×43 um2 vs 127×44 um2) with half the number of pins ports (12 vs 25) and higher speed (2.2GHz vs 0.8GHz).", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2903982"}, {"primary_key": "4080649", "vector": [], "sparse_vector": [], "title": "Extended statistical element selection: a calibration method for high resolution in analog/RF designs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we propose a high resolution digital calibration method for analog/RF circuits that is an extension of the statistical element selection (SES) approach. As compared to SES, the proposed ESES method provides wider calibration range to accommodate multiple variation sources and produces higher calibration yield for the same calibration resolution target. Two types of ESES-based calibration with application in analog/RF designs are demonstrated; current source calibration and phase/delay calibration. As compared to traditional calibration methods, the proposed ESES-based calibration incurs lower circuit overhead while achieving higher calibration resolution. ESES calibration is further applied to a wideband harmonic-rejection receiver design that achieves best-in-class harmonic-rejection performance after calibration.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898022"}, {"primary_key": "4080650", "vector": [], "sparse_vector": [], "title": "Incremental layer assignment for critical path timing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "With VLSI technology nodes scaling into nanometer regime, interconnect delay plays an increasingly critical role in timing. For layer assignment, most works deal with via counts or total net delays, ignoring critical paths of each net and resulting in potential timing issues. In this paper we propose an incremental layer assignment framework targeting at delay optimization for critical path of each net. A set of novel techniques are presented: self-adaptive quadruple partition based on KxK division benefits the run-time; semidefinite programming is utilized for each partition; post mapping algorithm guarantees integer solutions while satisfying edge capacities. The effectiveness of our work is verified by ISPD'08 benchmarks.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898033"}, {"primary_key": "4080651", "vector": [], "sparse_vector": [], "title": "Invited - Wireless sensor nodes for environmental monitoring in internet of things.", "authors": ["<PERSON>g<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Ku<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>-<PERSON><PERSON>"], "summary": "This paper presents a self-sustainable landslide surveillance system that detects hazardous water content level in soils and provides real-time landslide warnings to residents, without requiring wired electricity transmission. A self-powered soil water content sensor was applied as the trigger of alert event. It solves the energy supply problem by an environmental interrupt mechanism, which wakes up the sensor and communication circuits in a sensing node only when the water content in monitored soils exceeds a certain threshold, and thus completely eliminates the need for an ALS node to periodically wake up, sense and communicate. By tightly integrating energy harvesting, environment sensing and circuit wake-up, it may well be the most energy-efficient landslide surveillance system designed to monitor water content in soils in the world.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898602"}, {"primary_key": "4080652", "vector": [], "sparse_vector": [], "title": "Two-step state transition minimization for lifetime and performance improvement on MLC STT-RAM.", "authors": ["<PERSON><PERSON><PERSON>", "Jingtong Hu", "<PERSON>", "<PERSON>", "Qingfeng Zhuge"], "summary": "Spin-transfer torque random access memory (STT-RAM) is considered as a promising candidate to replace SRAM as the next generation cache memory since it has better scalability and lower leakage power. Recently, 2-bit multi-level cell (MLC) STT-RAM has been proposed to further increase data density. However, a key drawback for MLC STT-RAM is that the magnetization directions of its hard and soft domains cannot be flipped to two opposite directions simultaneously, which leads to the two-step problem in state transitions. Two-step state transitions would significantly impact the lifetime of MLC STT-RAM due to the wasted flips in the soft domains. To solve the problem, this paper proposes a novel two-step state transition minimization (TSTM) scheme, to improve the lifetime of MLC STT-RAM when it is employed in cache design. The basic idea is by sacrificing certain cells as auxiliary flags, the two-step state transitions in STT-RAM can be well eliminated. Experimental results show that the proposed scheme can improve the lifetime of MLC STT-RAM to 318.5%.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898106"}, {"primary_key": "4080653", "vector": [], "sparse_vector": [], "title": "Energy efficient computation with asynchronous races.", "authors": ["Ad<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "By encoding information as digital signal propagation delay, rather than conventional logic levels, some basic processing operations become exceedingly energy efficient to implement. The result of such a computation can then be observed by relative timing differences between injected signals. We demonstrate the embodiment of such an approach utilizing current starved inverters as delay elements and characterize application-level artifacts of circuit-level variance. Specifically we chose the well-studied DNA sequence alignment problem for comparison and we show that, for the synthesized design, asynchronous races are 10× more energy efficient and 4× denser at comparable speeds as compared to prior approaches.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898019"}, {"primary_key": "4080654", "vector": [], "sparse_vector": [], "title": "Invited - Specification and modeling for systems-on-chip security verification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper describes a methodology for system-level security verification of modern Systems-on-Chip (SoC) designs. These designs comprise interacting firmware and hardware modules which makes verification particularly challenging. These challenges relate to (i) specifying security verification properties, and (ii) verifying these properties across firmware and hardware. We address the latter through raising the level of abstraction of the hardware modules to be similar to that of instructions in software/firmware. This abstraction, referred to as an instruction-level abstraction (ILA), plays a similar role to the instruction set architecture (ISA) definition for general purpose processors and enables high-level analysis of SoC firmware. In particular, the ILA can be used instead of the cycle-accurate bit-precise hardware implementation for scalable verification of system-level security properties in SoCs.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2911991"}, {"primary_key": "4080655", "vector": [], "sparse_vector": [], "title": "An FPGA-based infrastructure for fine-grained DVFS analysis in high-performance embedded systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Emerging technologies provide SoCs with fine-grained DVFS capabilities both in space (number of domains) and time (transients in the order of tens of nanoseconds). Analyzing these systems requires cycle-accurate accounting of rapidly-changing dynamics and complex interactions among accelerators, interconnect, memory, and OS. We present an FPGA-based infrastructure that facilitates such analyses for high-performance embedded systems. We show how our infrastructure can be used to first generate SoCs with loosely-coupled accelerators, and then perform design-space exploration considering several DVFS policies under full-system workload scenarios, sweeping spatial and temporal domain granularity.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897984"}, {"primary_key": "4080656", "vector": [], "sparse_vector": [], "title": "TEMP: thread batch enabled memory partitioning for GPU.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jingtong Hu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hai <PERSON>"], "summary": "As massive multi-threading in GPU imposes tremendous pressure on memory subsystems, efficient bandwidth utilization becomes a key factor affecting the GPU throughput. In this work, we propose thread batch enabled memory partitioning (TEMP), to improve GPU performance through the improvement of memory bandwidth utilization. In particular, TEMP clusters multiple thread blocks sharing the same set of pages into a thread batch and dispatches the entire thread batch to a stream multiprocessor. TEMP separates the memory access streams of different thread batches by OS memory management, preserving the intrinsic locality of thread batches and increasing the memory access parallelism. Experimental results show that TEMP can obtain up to 10.3% performance improvement and 14.6% DRAM energy reduction compared to a state-of-the-art scheduler without any memory-side optimizations.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898103"}, {"primary_key": "4080657", "vector": [], "sparse_vector": [], "title": "Notifying memories: a case-study on data-flow applications with NoC interfaces implementation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "NoC-based architectures overcome the limitations of traditional buses by exploiting parallelism and offer large bandwidths. NoC adoption also increases communication latency, which is especially penalising for data-flow applications (DF). We introduce the notifying memories (NM) concept to reduce this overhead. Our original approach eliminates useless memory requests. This paper demonstrates NM in the context of video coding applications implemented with dynamic DF. We have conducted cycle accurate systemC simulation of the NoC on an MPEG4 decoder to evaluate NM efficiency. The results show significant reductions in terms of latency (78%), injection rate (60%), and power savings (49%) along with throughput improvement (16%).", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898051"}, {"primary_key": "4080658", "vector": [], "sparse_vector": [], "title": "A probabilistic scheduling framework for mixed-criticality systems.", "authors": ["<PERSON>"], "summary": "We propose a probabilistic scheduling framework for the design and development of mixed-criticality systems, i.e., where tasks with different levels of criticality need to be scheduled on a shared resource. Whereas highly critical tasks normally require hard real-time guarantees, less or non-critical ones may be degraded or even temporarily discarded at runtime. We hence propose giving probabilistic (instead of deterministic) real-time guarantees on low-criticality tasks. This simplifies the analysis and reduces conservativeness on the one hand. On the other hand, probabilistic guarantees can be tuned by the designer to reach a desired level of assurance. We illustrate these and other benefits of our framework based on extensive simulations.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897971"}, {"primary_key": "4080659", "vector": [], "sparse_vector": [], "title": "An area-efficient consolidated configurable error correction for approximate hardware accelerators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Approximate adders are widely being advocated for developing hardware accelerators to perform complex arithmetic operations. Most of the state-of-the-art accuracy configurable approximate adders utilize some integrated Error Detection and Correction (EDC) circuitry. Consequently, the accumulated area overhead due to the EDC (integrated within individual adders) is significant. In this paper, we propose a low-cost Consolidated Error Correction (CEC) unit, that essentially corrects the accumulated error at the accelerator output. The proposed CEC is based on a mathematical model of approximation error. We integrate our CEC unit in approximate hardware accelerators deployed in different applications to demonstrate its area savings and speed enhancement compared to state-of-the-art.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897981"}, {"primary_key": "4080660", "vector": [], "sparse_vector": [], "title": "Invited - Energy harvesting and transient computing: a paradigm shift for embedded systems?", "authors": ["<PERSON>"], "summary": "Embedded systems powered from time-varying energy harvesting sources traditionally operate using the principles of energy-neutral computing: over a certain period of time, the energy that they consume equals the energy that they harvest. This has the significant advantage of making the system 'look like' a battery-powered system, yet typically results in large, complex and expensive power conversion circuitry and introduces numerous challenges including fast and reliable cold-start. In recent years, the concept of transient computing has emerged to challenge this traditional approach, whereby low-power embedded systems are enabled to operate as usual while energy is available but, after loss of supply, can quickly regain state and continue where they left off. This paper provides a summary of these different approaches.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905011"}, {"primary_key": "4080661", "vector": [], "sparse_vector": [], "title": "Perform-ML: performance optimized machine learning by platform and content aware customization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose Perform-ML, the first Machine Learning (ML) framework for analysis of massive and dense data which customizes the algorithm to the underlying platform for the purpose of achieving optimized resource efficiency. Perform-ML creates a performance model quantifying the computational cost of iterative analysis algorithms on a pertinent platform in terms of FLOPs, communication, and memory, which characterize runtime, storage, and energy. The core of Perform-ML is a novel parametric data projection algorithm, called Elastic Dictionary (ExD), that enables versatile and sparse representations of the data which can help in minimizing performance cost. We show that Perform-ML can achieve the optimal performance objective, according to our cost model, by platform aware tuning of the ExD parameters. An accompanying API ensures automated applicability of Perform-ML to various algorithms, datasets, and platforms. Proof-of-concept evaluations of massive and dense data on different platforms demonstrate more than an order of magnitude improvements in performance compared to the state of the art, within guaranteed user-defined error bounds.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898060"}, {"primary_key": "4080662", "vector": [], "sparse_vector": [], "title": "Predicting electromigration mortality under temperature and product lifetime specifications.", "authors": ["<PERSON><PERSON><PERSON>", "Sachin <PERSON>"], "summary": "Today's methodologies for electromigration (EM) identify EM-susceptible wires based on their current density, using the Blech criterion to filter out wires that are EM-immortal. The Blech criterion is agnostic to the product lifetime and temperature conditions: many Blech-mortal wires may never experience EM during the product lifetime. We develop new methods that evaluate the transient evolution of stress, relative to the product lifetime, and present an improved set of simple and practical mortality criteria. On a set of power grid benchmarks, we demonstrate that the actual number of mortal wires may depend strongly on the lifetime and reliability conditions.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898070"}, {"primary_key": "4080663", "vector": [], "sparse_vector": [], "title": "PLL to the rescue: a novel EM fault countermeasure.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Electromagnetic injection (EMI) is a powerful and precise technique for fault injection in modern ICs. This intentional fault can be utilized to steal secret information hidden inside of ICs. Unlike laser fault injection, tedious package decapsulation is not needed for EMI, which reduces an attacker's cost and thus causes a serious information security threat. In this paper, a PLL-based sensor circuit is proposed to detect EMI reactively on chip. A fully automatic design flow is devised to integrate the proposed sensor together with a cryptographic processor. A high fault detection coverage and a small hardware overhead are demonstrated experimentally on an FPGA platform.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898065"}, {"primary_key": "4080664", "vector": [], "sparse_vector": [], "title": "VR-scale: runtime dynamic phase scaling of processor voltage regulators for improving power efficiency.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A voltage regulator (VR) for a processor is one of the most critical platform components. Particularly, a VR is required to support fast, accurate, and fine-grained voltage changes for efficient processor power management. Such requirements, nonetheless, can be relaxed when a processor consumes low power at runtime. Thus, manufacturers begin to offer some knobs so that a processor can adapt VR's operating parameters to cost-effectively satisfy the requirements with high efficiency. In this paper, we first demonstrate that: (1) VR efficiency heavily depends on load current (i.e., current delivered to a processor) and a VR operating parameter (e.g., the number of active phases) at given voltage; (2) a processor running a parallel application mostly consumes small current due to aggressive power management; and (3) when the processor is in active state, all the phases are always activated in the VR. (2) and (3) in turn lead to poor VR efficiency at most runtime. Second, we present VR-Scale that dynamically scales the number of active phases based on the predicted load current for the next interval. Our evaluations based on an Intel processor running emerging parallel applications show that VR-Scale can reduce the total power consumed by a processor and its VR by more than 19% with negligible performance impact.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898109"}, {"primary_key": "4080665", "vector": [], "sparse_vector": [], "title": "Invited - Towards fail-operational ethernet based in-vehicle networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the future, vehicles are expected to act more and more autonomously. The transition towards highly automated and autonomous driving will push the safety requirements for in-vehicle networks. Such networks must support isolation between mixed-critical traffic (e.g. critical control and non-critical infotainment) and must be fail-operational. This paper will present new concepts and mechanisms to achieve these goals in Ethernet-based networks. It will cover advanced topics such as software defined networking (SDN) to implement isolation, fault recovery, and controlled degradation, e.g. to maintain (degraded) operation until the driver takes over or to reach a safe stop.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905021"}, {"primary_key": "4080666", "vector": [], "sparse_vector": [], "title": "A novel time and voltage based SAR ADC design with self-learning technique.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a novel time and voltage based technique for successive approximation register (SAR) analog-to-digital converter (ADC) to improve the conversion speed. By taking advantage of the fact that at low supply voltage there will be a significant difference in comparator decision time for different input voltages, the proposed technique creates multiple auxiliary voltage levels for comparison and hence eliminates the need of additional comparators for acceleration as compared with the existing methods. In addition, a digital self-learning module is also presented, which calculates the uncertainty window required for bound update in the proposed method and thus adjusts to different process corners. To validate these concepts, a 10-bit SAR ADC is designed in 130nm CMOS process with 0.5V power supply voltage. The circuit operates in both conventional and proposed modes. Simulations show that the largest number of conversion cycles is 7, hence resulting in an acceleration of 30% over the conventional scheme, while the average number of cycles is 5.58. Simulation results also demonstrate that the proposed method does not affect accuracy. Both ADC operation modes achieve SNDR (signal-to-noise distortion ratio) of 59dB, corresponding to an ENOB (effective number of bits) of 9.5-bits.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897970"}, {"primary_key": "4080667", "vector": [], "sparse_vector": [], "title": "AVFSM: a framework for identifying and mitigating vulnerabilities in FSMs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Domenic Forte", "<PERSON>"], "summary": "A finite state machine (FSM) is responsible for controlling the overall functionality of most digital systems and, therefore, the security of the whole system can be compromised if there are vulnerabilities in the FSM. These vulnerabilities can be created by improper designs or by the synthesis tool which introduces additional don't-care states and transitions during the optimization and synthesis process. An attacker can utilize these vulnerabilities to perform fault injection attacks or insert malicious hardware modifications (Trojan) to gain unauthorized access to some specific states. To our knowledge, no systematic approaches have been proposed to analyze these vulnerabilities in FSM. In this paper, we develop a framework named Analyzing Vulnerabilities in FSM (AVFSM) which extracts the state transition graph (including the don't-care states and transitions) from a gate-level netlist using a novel Automatic Test Pattern Generation (ATPG) based approach and quantifies the vulnerabilities of the design to fault injection and hardware Trojan insertion. We demonstrate the applicability of the AVFSM framework by analyzing the vulnerabilities in the FSM of AES and RSA encryption module. We also propose a low-cost mitigation technique to make FSM more secure against these attacks.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897992"}, {"primary_key": "4080668", "vector": [], "sparse_vector": [], "title": "Serial T0: approximate bus encoding for energy-efficient transmission of sensor signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Off-chip serial buses are common in embedded systems, and due to the long physical lines, can contribute significantly to their energy consumption. However, these buses are often connected to analog sensors, whose data is inherently affected by noise and A/D errors. Thus, communication can tolerate small approximations, without a significant impact on the system outputs quality.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898089"}, {"primary_key": "4080669", "vector": [], "sparse_vector": [], "title": "Invited - Cross-layer approximations for neuromorphic computing: from devices to circuits and systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Neuromorphic algorithms are being increasingly deployed across the entire computing spectrum from data centers to mobile and wearable devices to solve problems involving recognition, analytics, search and inference. For example, large-scale artificial neural networks (popularly called deep learning) now represent the state-of-the art in a wide and ever-increasing range of video/image/audio/text recognition problems. However, the growth in data sets and network complexities have led to deep learning becoming one of the most challenging workloads across the computing spectrum. We posit that approximate computing can play a key role in the quest for energy-efficient neuromorphic systems. We show how the principles of approximate computing can be applied to the design of neuromorphic systems at various layers of the computing stack. At the algorithm level, we present techniques to significantly scale down the computational requirements of a neural network with minimal impact on its accuracy. At the circuit level, we show how approximate logic and memory can be used to implement neurons and synapses in an energy-efficient manner, while still meeting accuracy requirements. A fundamental limitation to the efficiency of neuromorphic computing in traditional implementations (software and custom hardware alike) is the mismatch between neuromorphic algorithms and the underlying computing models such as von Neumann architecture and Boolean logic. To overcome this limitation, we describe how emerging spintronic devices can offer highly efficient, approximate realization of the building blocks of neuromorphic computing systems.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905009"}, {"primary_key": "4080670", "vector": [], "sparse_vector": [], "title": "Improving performance and lifetime of NAND storage systems using relaxed program sequence.", "authors": ["Jisung Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new system-level solution that improves both the performance and lifetime of NAND storage systems by exploiting the performance asymmetry of NAND devices. At the device level, we propose a new program sequence, called relaxed program sequence (RPS), which allows more flexible page allocations in a block without compromising NAND reliability. By combining RPS with per-block parity pages, we can improve the write bandwidth and eliminate expensive paired page backup operations. Experimental results show that the proposed technique can increase IOPS by up to 56% and reduce the number of block erasures by up to 30% over an existing RPS-oblivious FTL.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898032"}, {"primary_key": "4080671", "vector": [], "sparse_vector": [], "title": "Distributed scheduling for many-cores using cooperative game theory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many-cores are envisaged to include hundreds of processing cores etched on to a single die and will execute tens of multi-threaded tasks in parallel to exploit their massive parallel processing potential. A task can be sped up by assigning it to more than one core. Moreover, processing requirements of tasks are in a constant state of flux and some of the cores assigned to a task entering a low processing requirement phase can be transferred to a task entering high requirement phase, maximizing overall performance of the system.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898009"}, {"primary_key": "4080672", "vector": [], "sparse_vector": [], "title": "Near-threshold computing in FinFET technologies: opportunities for improved voltage scalability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In recent years, operating at near-threshold supply voltages has been proposed to improve energy efficiency in circuits, yet decreased efficacy of dynamic voltage scaling has been observed in recent planar technologies. However, foundries have introduced a shift from planar to FinFET fabrication processes. In this paper, we study 7nm FinFET's ability to voltage scale and compare it to planar technologies across three dynamic voltage scaling scenarios. The switch to FinFET allows for a return to strong voltage scalability. We find up to 8.6× higher energy efficiency at NT compared to nominal supply voltage (vs. 4.8× gain in 20nm planar).", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898049"}, {"primary_key": "4080673", "vector": [], "sparse_vector": [], "title": "Fine-granularity tile-level parallelism in non-volatile memory architecture with two-dimensional bank subdivision.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Emerging memory technologies such as phase-change memory (PCM) and resistive RAMs (RRAM) have been proposed as promising candidates for future DRAM replacements. Due to the nature of how these memories operate, unique properties (such as non-destructive read and current-sensing) can be exploited to further subdivide memory and provide increasing parallelism with negligible overhead. In this work, we leverage these properties to design a finegrained non-volatile memory (FgNVM), featuring two-dimensional bank subdivision for tile-level parallelism (TLP) in a NVM memory bank, with much finer-granularity and increased parallelism than the one-dimensional bank subdivision for subarray-level parallelism (SALP) in a DRAM memory bank. With such new tile-level parallelism, three new memory access modes are proposed for further performance improvement and energy reduction: Partial-Activation, Multi-Activation, and Background Writes. Our experimental results show that the new architecture is highly effective in boosting non-volatile memory performance with significant energy reduction. To the best of our knowledge, this is the first work to study fine-granularity memory access in emerging non-volatile memory architectures.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898024"}, {"primary_key": "4080674", "vector": [], "sparse_vector": [], "title": "Improving mobile gaming performance through cooperative CPU-GPU thermal management.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "State-of-the-art thermal management techniques independently throttle the frequencies of high-performance multi-core CPU and powerful graphics processing units (GPU) on heterogeneous multiprocessor system-on-chips deployed in latest mobile devices. For graphics-intensive gaming applications, this approach is inadequate because both the CPU and the GPU contribute towards the overall application performance (frames per second or FPS) as well as the on-chip temperature. The lack of coordination between CPU and GPU induces recurrent frequency throttling to maintain on-chip temperature below the permissible limit. This leads to significantly degraded application performance and large variation in temperature over time. We propose a control-theory based dynamic thermal management technique that cooperatively scales CPU and GPU frequencies to meet the thermal constraint while achieving high performance for mobile gaming. Experimental results with six popular Android games on a commercial mobile platform show an average 19% performance improvement and over 90% reduction in temperature variance compared to the original Linux approach.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898031"}, {"primary_key": "4080675", "vector": [], "sparse_vector": [], "title": "Physical unclonable functions-based linear encryption against code reuse attacks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Wang", "<PERSON>"], "summary": "Recently, code reuse attacks (CRAs) have emerged as a new class of ingenious security threatens. Attackers can utilize CRAs to hijack the control flow of programs to perform malicious actions without injecting any codes. Existing defenses against CRAs often incur high memory and performance overheads or require extending the existing processors' instruction set architectures (ISAs). To tackle these issues, we propose a hardware-based control flow integrity (CFI) that employs physical unclonable functions (PUF)-based linear encryption architecture (LEA) to protect against CRAs with negligible hardware extending and run time overheads. The proposed method can protect ret and indirect jmp instructions from return oriented programming (ROP) and jump oriented programming (JOP) without any additional software manipulations and extending ISAs. The pre-process will be conducted on codes once the executable binary is loaded into memory, and the real-time control flow verification based on LEA can be done while ret and jmp instructions are executed. Performance evaluations on benchmarks show that the proposed method only introduces 0.61% run-time overhead and 0.63% memory overhead on average.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898061"}, {"primary_key": "4080676", "vector": [], "sparse_vector": [], "title": "Integration of multi-sensor occupancy grids into automotive ECUs.", "authors": ["Tiana A. Ra<PERSON>vao", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Occupancy Grids (OGs) are a popular framework for robotic perception. They were recently adopted for performing multi-sensor fusion and environment mapping for autonomous vehicles. However, high computational requirements strongly hinder their integration into less powerful automotive ECUs. To overcome this problem, we propose an algorithmic improvement for mapping range measurements into OGs. Experiments were conducted on a vehicle equipped with 16 LIDAR scans. Results demonstrate that a single-core ARM cortex A9 can build now in real-time OGs that map urban traffic scenarios of 100m-by-100m.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898035"}, {"primary_key": "4080677", "vector": [], "sparse_vector": [], "title": "Statistical path tracing in timing graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Path tracing is a key requirement in providing various functionalities in a Static Timing Analysis (STA) tool. With growing design sizes and advancements in STA techniques like statistical timing analysis, path tracing techniques need to be more efficient and accurate. Naïve extensions of path tracing techniques for statistical timing analysis are found to be inefficient and inaccurate. In this paper, we present a novel Statistical Path Tracing (SPT) approach and illustrate its application in Common Path Pessimism Removal (CPPR). We demonstrate that SPT is more efficient and accurate when compared to a Deterministic Path Tracing (DPT) approach.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898096"}, {"primary_key": "4080678", "vector": [], "sparse_vector": [], "title": "Shift sprinting: fine-grained temperature-aware NoC-based MCSoC architecture in dark silicon age.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Reliability is a critical feature of chip integration and unreliability can lead to performance, cost, and time-to-market penalties. Moreover, upcoming Many-Core System-on-Chips (MCSoCs), notably future generations of mobile devices, will suffer from high power densities due to the dark silicon problem. Thus, in this paper, a novel NoC-based MCSoC architecture, called Shift Sprinting, is introduced in order to reliably utilize dark silicon under the power budget constraint. By employing the concept of distributional sprinting, our proposed architecture provides Quality of Service (QoS) to efficiently run real-time streaming applications in mobile devices. Simulation results show meaningful gain in performance and reliability of the system compared to state-of-the-art works.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898090"}, {"primary_key": "4080679", "vector": [], "sparse_vector": [], "title": "Plug-n-learn: automatic learning of computational algorithms in human-centered internet-of-things applications.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Wearable technologies play a central role in human-centered Internet-of-Things applications. Wearables leverage computational and machine learning algorithms to detect events of interest such as physical activities and medical complications. A major obstacle in large-scale utilization of current wearables is that their computational algorithms need to be re-built from scratch upon any changes in the configuration of the network. Retraining of these algorithms requires significant amount of labeled training data, a process that is labor-intensive, time-consuming, and infeasible. We propose an approach for automatic retraining of the machine learning algorithms in real-time without need for any labeled training data. We measure the inherent correlation between observations made by an old sensor view for which trained algorithms exist and the new sensor view for which an algorithm needs to be developed. By applying our real-time multi-view autonomous learning approach, we achieve an accuracy of 80.66% in activity recognition, which is an improvement of 15.96% in the accuracy due to the automatic labeling of the data in the new sensor node. This performance is only 7.96% lower than the experimental upper bound where labeled training data are collected with the new sensor.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898066"}, {"primary_key": "4080680", "vector": [], "sparse_vector": [], "title": "Invited - Ultra low power integrated transceivers for near-field IoT.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we propose mm-Waves for Near-Field IoT, ultralow power transceivers. With small footprint and no external components, the transceivers could be integrated with the sensors, with the wireless sensor nodes organized in a Master-Slave, asymmetrical network. With low complexity and high energy efficiency, the slave nodes benefit from a minimalist design approach with integrated antennas and integrated resonators for absolute frequency accuracy. Two designs are presented. The first is a K-band, super-regenerative, logarithmic-mode, OOK receiver achieving a peak energy efficiency of 200pJ/bit at 4Mb/s and a BER of 10−3. With 800μW peak and 8μW average power, the sensitivity of the receiver is −60dBm for the same data and bit-error rates. Realized in a 65nm CMOS process from GF, the active area of the receiver is 740×670μm2. The second design is a 100Kb/s, V-band transceiver with integrated antenna. It achieves 20pJ/bit energy efficiency (Rx mode) and it provides means for 1/f noise mitigation.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2907024"}, {"primary_key": "4080681", "vector": [], "sparse_vector": [], "title": "Designing guardbands for instantaneous aging effects.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Montserrat Nafría", "<PERSON><PERSON><PERSON>"], "summary": "Bias Temperature Instability (BTI) is one of the key causes of reliability degradations of nano-CMOS circuits. While the long-term impact of BTI has been studied since years, the short-term implications of BTI on circuits are unexplored. In fact, in physics short-term BTI effects, i.e. instantaneous (i.e. sub μs) frequency dependent processes, have been recently reported. In order to design circuits with guardbands that are safe for long-term and instantaneous effects, new aging models are required. We are presenting the first approach that in fact considers both long-term as well as instantaneous BTI effects. It can be employed for complex circuits at the micro-architecture level. Designing guardbands based upon our physical BTI model reduces the guardbands by 41% and thus allows for the development of more cost-effective yet reliable designs. We also revisit existing state-of-the-art aging mitigation techniques to investigate how they can be properly adapted to additionally account for instantaneous aging effects. Along with our BTI model this further reduces the guardbands by up to 59%.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898006"}, {"primary_key": "4080682", "vector": [], "sparse_vector": [], "title": "Invited - Context-aware energy-efficient communication for IoT sensor nodes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "The widespread proliferation of sensor nodes in the era of Internet of Things (IoT) coupled with increasing sensor fidelity and data-acquisition modality is expected to generate 30+ Exabytes of data per month by 2020. In this data driven IoT world, wireless communication is a significant consumer of energy, and paying careful attention to the balance between local and remote computation is critical to overall energy usage. The communication fabrics that will handle this enormous amount of IoT workload will need to be energy-efficient under changing contexts such as channel conditions, applications, QoS, data-rate requirements etc. Moreover, the IoT devices will often include multiple parallel communication fabrics; e.g. wired, proximity, mm-wave, 5G etc. We will discuss how self-learning can enable context-aware operation in such communication systems to allow minimum energy/bit and energy/information for any given communication scenario. The need for context-aware operation within and among multiple physical layers (PHYs) in future IoT workloads will be highlighted. Such energy-efficient communication (<PERSON>'s Law) along with low-power computing (<PERSON>'s Law), is expected to harness the true potential of the IoT revolution and produce dramatic societal impact.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905005"}, {"primary_key": "4080683", "vector": [], "sparse_vector": [], "title": "Invited - Optimizing device reliability effects at the intersection of physics, circuits, and architecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sachin <PERSON>"], "summary": "Over the years, there has been tremendous progress in developing new methods for modeling and diagnosing reliability at the level of individual transistors and interconnects. The thrust to propagate these models to higher levels of abstraction to predict the reliability of larger circuits is much more recent. This paper addresses the intersection of physics, circuits, and architecture for reliability modeling and optimization that must come together for cross-layer optimization. For various device reliability phenomena, this paper shows how physical models can be leveraged at the circuit level, or circuit models at the architecture level, to deliver composite solutions that comprehend chip-level design goals.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2905016"}, {"primary_key": "4080684", "vector": [], "sparse_vector": [], "title": "Minimizing the energy-delay product of SRAM arrays using a device-circuit-architecture co-optimization framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The objective of this paper is to minimize the energy-delay product of static random access memory (SRAM) arrays by using a device-circuit architecture co-optimization framework. More specifically, at the device-level, high-Vt FinFETs are adopted for the 6T SRAM cell, which significantly reduces the leakage power and improves static noise margins. However, due to the lower ON current, the bit-line delay of the read access is increased. Accordingly, at the circuit-level, the voltage level of assist circuits, and at the architecture-level (i.e., the array organization), key parameters of the SRAM array are jointly optimized to derive a design that results in the minimum energy-delay product point. By using the proposed optimization framework, for SRAM array capacities ranging from 1KB to 16KB, on average 59% lower energy-delay product with maximum 12% (and on average 9%) performance penalty is achieved.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898044"}, {"primary_key": "4080685", "vector": [], "sparse_vector": [], "title": "Invited - Cross-layer approximate computing: from logic to architectures.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a survey of approximate techniques and discuss concepts for building power-/energy-efficient computing components reaching from approximate accelerators to arithmetic blocks (like adders and multipliers). We provide a systematical understanding of how to generate and explore the design space of approximate components, which enables a wide-range of power/energy, performance, area and output quality tradeoffs, and a high degree of design flexibility to facilitate their design. To enable cross-layer approximate computing, bridging the gap between the logic layer (i.e. arithmetic blocks) and the architecture layer (and even considering the software layers) is crucial. Towards this end, this paper introduces open-source libraries of low-power and high-performance approximate components. The elementary approximate arithmetic blocks (adder and multiplier) are used to develop multi-bit approximate arithmetic blocks and accelerators. An analysis of data-driven resilience and error propagation is discussed. The approximate computing components are a first steps towards a systematic approach to introduce approximate computing paradigms at all levels of abstractions.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2906199"}, {"primary_key": "4080686", "vector": [], "sparse_vector": [], "title": "Redundant via insertion for multiple-patterning directed-self-assembly lithography.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In sub-7nm technology, the size and pitch of vias are much smaller than optical resolution limit, and directed self-assembly lithography with multiple patterning technology (MP-DSAL) has been proposed as a solution. In MP-DSAL, vias that are close are clustered and patterned together via DSAL process, and via clusters that are close are printed using different masks via MP. Redundant vias, which are typically used for better via manufacturability, should be inserted very carefully in MP-DSAL because some redundant vias may cause large and complex via clusters, which are undesirable in DSAL; some other redundant vias may cause mask assignment of via clusters impossible, often called MP coloring conflict.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898080"}, {"primary_key": "4080687", "vector": [], "sparse_vector": [], "title": "Low-power approximate convolution computing unit with domain-wall motion based &quot;spin-memristor&quot; for image processing applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Convolution serves as the basic computational primitive for various associative computing tasks ranging from edge detection to image matching. CMOS implementation of such computations entails significant bottlenecks in area and energy consumption due to the large number of multiplication and addition operations involved. In this paper, we propose an ultra-low power and compact hybrid spintronic-CMOS design for the convolution computing unit. Low-voltage operation of domain-wall motion based magneto-metallic \"Spin-Memristor\"s interfaced with CMOS circuits is able to perform the convolution operation with reasonable accuracy. Simulation results of Gabor filtering for edge detection reveal ~ 2.5× lower energy consumption compared to a baseline 45nm-CMOS implementation.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898042"}, {"primary_key": "4080688", "vector": [], "sparse_vector": [], "title": "Catching the flu: emerging threats from a third party power management unit.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Power management units (PMU) have come into the spotlight with energy efficiency becoming a first order constraint in MPSoC designs. To cater to the exponential rise in power events, and to meet the demands of tight power and energy budgets, PMUs are evolving to more complex and intelligent designs. In an era defined by energy efficient computing, a malicious circuit embedded in a third party PMU can adversely affect the operation of the entire MPSoC.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897994"}, {"primary_key": "4080689", "vector": [], "sparse_vector": [], "title": "Practical statistical static timing analysis with current source models.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper considers the practical nuances of using current source gate models in an industrial statistical timing analysis environment. Specifically, the memory overhead of a naive implementation combining statistical and current source models to obtain and store gate output waveforms is found to be impractical for large microprocessor designs. A study is performed to observe variational gate output waveforms, and a technique is presented to store the waveforms in a memory efficient manner with minimal accuracy impact. The presented technique is validated over a set of 14 nanometer designs, and has enabled the usage of current source models in our industrial statistical timing analysis flow. Results demonstrate slack accuracy improvements of up to 17 picoseconds with a 1.15X run-time overhead and 1.1 gigabytes per million-gates memory overhead in comparison to an existing flow.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898068"}, {"primary_key": "4080690", "vector": [], "sparse_vector": [], "title": "Formal reliability analysis of switched ethernet automotive networks under transient transmission errors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern cars integrate a huge number of functionalities with high bandwidth, real-time, and reliability requirements. Ethernet offers the possibility to satisfy these bandwidth requirements and enables the usage of temporal redundancy mechanisms to increase the reliability of the communication network. In this paper, we present a lightweight formal analysis approach for the determination of the transmission reliability of messages in switched Ethernet networks under the influence of transient errors. In particular, this approach takes the interrelation between the individual message reliability and the timing behavior of the communication network into account. We present both a fast approach delivering a pessimistic safe reliability bound and a more sophisticated approach that results in a tighter yet still safe bound. The proposed approaches are compared by performing a design space exploration of an automotive communication network.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898026"}, {"primary_key": "4080691", "vector": [], "sparse_vector": [], "title": "Optimal design of JPEG hardware under the approximate computing paradigm.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Sachin <PERSON>"], "summary": "JPEG compression based on the discrete cosine transform (DCT) is a key building block in low-power multimedia applications. We use approximate computing to exploit the error tolerance of JPEG and formulate a novel optimization problem that maximizes power savings under an error budget. We analyze the error propagation sensitivity in the DCT network and use this information to model the impact of introduced errors on the output quality. Simulations show up to 15% reduction in area and delay which corresponds to 40% power savings at iso-delay.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898057"}, {"primary_key": "4080692", "vector": [], "sparse_vector": [], "title": "Unlocking efficiency and scalability of reversible logic synthesis using conventional logic synthesis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Latest quantum technologies promise realization of extremely large circuits, whereas, reversible logic synthesis, the key automation step for quantum computing, suffers from scalability bottleneck. Scalability can be achieved with Decision Diagram (DD)-based synthesis at the cost of significant an-cilla/garbage lines overhead. In this paper, we present a novel hierarchical reversible logic synthesis, where DD-based synthesis is invoked within an And-Inverter Graph (AIG)-based synthesis wrapper, balancing scalability and performance.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898107"}, {"primary_key": "4080693", "vector": [], "sparse_vector": [], "title": "An MIG-based compiler for programmable logic-in-memory architectures.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Resistive memories have gained high research attention for enabling design of in-memory computing circuits and systems. We propose for the first time an automatic compilation methodology suited to a recently proposed computer architecture solely based on resistive memory arrays. Our approach uses Majority-Inverter Graphs (MIGs) to manage the computational operations. In order to obtain a performance and resource efficient program, we employ optimization techniques both to the underlying MIG as well as to the compilation procedure itself. In addition, our proposed approach optimizes the program with respect to memory endurance constraints which is of particular importance for in-memory computing architectures.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897985"}, {"primary_key": "4080694", "vector": [], "sparse_vector": [], "title": "Single-tier virtual queuing: an efficacious memory controller architecture for MPSoCs with multiple realtime cores.", "authors": ["<PERSON>", "Kambiz <PERSON>", "<PERSON>"], "summary": "In heterogeneous MPSoCs, memory interference between the CPU and realtime cores is a critical impediment to system performance. Previous memory schedulers adopt the classic two-tier queuing system, but unfortunately the use of two-tier queuing deteriorates the QoS of scheduling policies. In this paper, we propose the Single-Tier Virtual Queuing (STVQ) memory controller for efficacious QoS-aware scheduling. The STVQ memory controller maintains single-tier transaction queues and employs separable allocation for transaction scheduling with high scalability. A multi-source realtime scheduling algorithm is further presented. The STVQ controller achieves up to 13.9% less CPU IPC slowdown than previous schedulers with no frame rate penalty on realtime cores.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898093"}, {"primary_key": "4080695", "vector": [], "sparse_vector": [], "title": "C-brain: a deep learning accelerator that tames the diversity of CNNs through adaptive data-level parallelization.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Liu", "<PERSON><PERSON>"], "summary": "Convolutional neural networks (CNN) accelerators have been proposed as an efficient hardware solution for deep learning based applications, which are known to be both compute-and-memory intensive. Although the most advanced CNN accelerators can deliver high computational throughput, the performance is highly unstable. Once changed to accommodate a new network with different parameters like layers and kernel size, the fixed hardware structure, may no longer well match the data flows. Consequently, the accelerator will fail to deliver high performance due to the underutilization of either logic resource or memory bandwidth. To overcome this problem, we proposed a novel deep learning accelerator, which offers multiple types of data-level parallelism: inter-kernel, intra-kernel and hybrid. Our design can adaptively switch among the three types of parallelism and the corresponding data tiling schemes to dynamically match different networks or even different layers of a single network. No matter how we change the hardware configurations or network types, the proposed network mapping strategy ensures the optimal performance and energy-efficiency. Compared with previous state-of-the-art NN accelerators, it is possible to achieve a speedup of 4.0x-8.3x for some layers of the well-known large scale CNNs. For the whole phase of network forward-propagation, our design achieves 28.04% PE energy saving, 90.3% on-chip memory energy saving on average.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897995"}, {"primary_key": "4080696", "vector": [], "sparse_vector": [], "title": "GarbledCPU: a MIPS processor for secure computation in hardware.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present GarbledCPU, the first framework that realizes a hardware-based general purpose sequential processor for secure computation. Our MIPS-based implementation enables development of applications (functions) in a high-level language while performing secure function evaluation (SFE) using <PERSON>'s garbled circuit protocol in hardware. GarbledCPU provides three degrees of freedom for SFE which allow leveraging the trade-off between privacy and performance: public functions, private functions, and semi-private functions. We synthesize GarbledCPU on a Virtex-7 FPGA as a proof-of-concept implementation and evaluate it on various benchmarks including Hamming distance, private set intersection and AES. Our results indicate that our pipelined hardware framework outperforms the fastest available software implementation.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898027"}, {"primary_key": "4080697", "vector": [], "sparse_vector": [], "title": "Reducing serial I/O power in error-tolerant applications by efficient lossy encoding.", "authors": ["<PERSON>-<PERSON><PERSON>", "<PERSON>"], "summary": "Transferring data between integrated circuits (ICs) accounts for an important fraction of the power dissipation in wearable and mobile systems. Reducing signal transitions reduces the dynamic power dissipated in the data transfer between ICs. Techniques such as Gray coding to reduce transitions between two parallel words cannot be applied when the signal transitions are between bits of a single serialized word.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898079"}, {"primary_key": "4080698", "vector": [], "sparse_vector": [], "title": "A quantum annealing approach for boolean satisfiability problem.", "authors": ["Juexiao Su", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Quantum annealing device has shown a great potential in solving discrete problems that are theoretically and empirically hard. Boolean Satisfiability (SAT) problem, determining if there is an assignment of variables that satisfies a given Boolean function, is the first proven NP-complete problem widely used in various domains. Here, we present a novel mapping of the SAT problem to the quadratic unconstrained binary optimization problem (QUBO), and further develop a tool flow embedding the proposed QUBO to the architecture of the commercialized quantum computer D-Wave. By leveraging electronic design automation techniques including synthesis, placement and routing, this is not only the first work providing the detail flow that embeds the QUBO, but also a technique scalable for real world applications and some hard SAT problems with over 6000 variables in QUBO. Based on our results, we discuss the challenges in solving SAT using the current generation of annealing device, and explore the problem solving capability of future quantum annealing computers.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897973"}, {"primary_key": "4080699", "vector": [], "sparse_vector": [], "title": "Strategy without tactics: policy-agnostic hardware-enhanced control-flow integrity.", "authors": ["<PERSON>", "Orlando Arias", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Control-flow integrity (CFI) is a general defense against code-reuse exploits that currently constitute a severe threat against diverse computing platforms. Existing CFI solutions (both in software and hardware) suffer from shortcomings such as (i) inefficiency, (ii) security weaknesses, or (iii) are not scalable. In this paper, we present a generic hardware-enhanced CFI scheme that tackles these problems and allows to enforce diverse CFI policies. Our approach fully supports multi-tasking, shared libraries, prevents various forms of code-reuse attacks, and allows CFI protected code and legacy code to co-exist. We evaluate our implementation on SPARC LEON3 and demonstrate its high efficiency.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898098"}, {"primary_key": "4080700", "vector": [], "sparse_vector": [], "title": "SECRET: smartly EnCRypted energy efficient non-volatile memories.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data persistence in emerging non-volatile memories (NVMs) poses a multitude of security vulnerabilities, motivating main memory encryption for data security. However, practical encryption algorithms demonstrate strong diffusion characteristics that increase cell flips, resulting in increased write energy/latency and reduced lifetime of NVMs. State-of-the-art security solutions have focused on reducing the encryption penalty (increased write energy/latency and reduced memory lifetime) in single-level cell (SLC) NVMs; however, the realization of low encryption penalty solutions for multi-/triple-level cell (MLC/TLC) secure NVMs remains an open area of research. This work synergistically integrates zero-based partial writes with XOR-based energy masking to realize Smartly EnCRypted Energy efficienT, i.e., SECRET MLC/TLC NVMs, without compromising the security of the underlying encryption technique. Our simulations on an MLC (TLC) resistive RAM (RRAM) architecture across SPEC CPU2006 workloads demonstrate that for 6.25% (7.84%) memory overhead, SECRET reduces write energy by 80% (63%), latency by 37% (49%), and improves memory lifetime by 63% (56%) over conventional advanced encryption standard-based (AES-based) counter mode encryption.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898087"}, {"primary_key": "4080701", "vector": [], "sparse_vector": [], "title": "Minimum-implant-area-aware detailed placement with spacing constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Due to the continuous shrinking of technology nodes, the minimum implant area (MIA) constraint has become a critical issue for modern circuit placement. With a fixed cell height, this constraint can be transferred into a minimum cell width constraint, and thus cells of small widths may have MIA violations. To solve such violations, we may shift neighboring cells to preserve whitespace or abut violating cells with the same threshold voltages (VTs). This paper presents an MIA-aware detailed placement algorithm to effectively solve the placement problem with the MIA constraint by clustering violating cells with the same VTs, and then apply cluster-based detailed placement algorithms to solve this problem. To further minimize the design area, an MIA-aware cell flipping algorithm based on linear-time dynamic programming is presented. Experimental results show that our algorithm can achieve high-quality results for this problem and is very robust for different multi-VT designs and MIA constraints.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898045"}, {"primary_key": "4080702", "vector": [], "sparse_vector": [], "title": "Columba: co-layout synthesis for continuous-flow microfluidic biochips.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "Continuous-flow microfluidics have evolved rapidly in the last decades, due to their advantages in effective and accurate control. However, complex control results in complicated valve actuations. As a result, sophisticated interactions between control and flow layers substantially raise the design difficulty. Previous work on design automation for microfluidics neglects the interactions between the control and flow layers and designs each layer separately, which leads to unrealistic designs. We propose the first planarity-guaranteed architectural model, and the first physical-design module models for important microfluidic components, which have modelled the interactions between both control and flow layers, while reducing the design difficulty. Based on the above, we propose the co-layout synthesis tool called Columba, which considers the pressure sharing among different valves, and routes channels in an any-angled manner. Experimental results show that complicated designs considering layer interactions can be synthesized for the first time.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897997"}, {"primary_key": "4080703", "vector": [], "sparse_vector": [], "title": "Novel CMOS RFIC layout generation with concurrent device placement and fixed-length microstrip routing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hsiang<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With advancing process technologies and booming IoT markets, millimeter-wave CMOS RFICs have been widely developed in re- cent years. Since the performance of CMOS RFICs is very sensi- tive to the precision of the layout, precise placement of devices and precisely matched microstrip lengths to given values have been a labor-intensive and time-consuming task, and thus become a major bottleneck for time to market. This paper introduces a progressive integer-linear-programming-based method to gener- ate high-quality RFIC layouts satisfying very stringent routing requirements of microstrip lines, including spacing/non-crossing rules, precise length, and bend number minimization, within a given layout area. The resulting RFIC layouts excel in both per- formance and area with much fewer bends compared with the simulation-tuning based manual layout, while the layout gener- ation time is significantly reduced from weeks to half an hour.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898052"}, {"primary_key": "4080704", "vector": [], "sparse_vector": [], "title": "Approximate bitcoin mining.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Bitcoin is the most popular cryptocurrency today. A bedrock of the Bitcoin framework is mining, a computation intensive process that is used to verify Bitcoin transactions for profit. We observe that mining is inherently error tolerant due to its embarrassingly parallel and probabilistic nature. We exploit this inherent tolerance to inaccuracy by proposing approximate mining circuits that trade off reliability with area and delay. These circuits can then be operated at Better Than Worst-Case (BTWC) to enable further gains. Our results show that approximation has the potential to increase mining profits by 30%.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897988"}, {"primary_key": "4080705", "vector": [], "sparse_vector": [], "title": "The cat and mouse in split manufacturing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Split manufacturing of integrated circuits eliminates vulnerabilities introduced by an untrusted foundry by manufacturing only a part of design at an untrusted high-end foundry and the remaining part at a trusted low-end foundry. Most researchers have focused on attack and defenses for hierarchical designs and/or use a relatively high-end trusted foundry, leading to high cost. We propose an attack and defense for split manufacturing for industry-standard/relevant flattened designs. Our attack uses network-flow model and outperforms previous attacks. We also develop a defense technique using placement perturbation, while considering overhead. The effectiveness of our techniques is demonstrated on benchmark circuits.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898104"}, {"primary_key": "4080706", "vector": [], "sparse_vector": [], "title": "SecDCP: secure dynamic cache partitioning for efficient timing channel protection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>", "<PERSON><PERSON>"], "summary": "In today's multicore processors, the last-level cache is often shared by multiple concurrently running processes to make efficient use of hardware resources. However, previous studies have shown that a shared cache is vulnerable to timing channel attacks that leak confidential information from one process to another. Static cache partitioning can eliminate the cache timing channels but incurs significant performance overhead. In this paper, we propose Secure Dynamic Cache Partitioning (SecDCP), a partitioning technique that defeats cache timing channel attacks. The SecDCP scheme changes the size of cache partitions at run time for better performance while preventing insecure information leakage between processes. For cache-sensitive multiprogram workloads, our experimental results show that SecDCP improves performance by up to 43% and by an average of 12.5% over static cache partitioning.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898086"}, {"primary_key": "4080707", "vector": [], "sparse_vector": [], "title": "On harmonic fixed-priority scheduling of periodic real-time tasks with constrained deadlines.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "It is well known that a harmonic task set, i.e., task periods are integer multiples of each other, can better utilize a processor to achieve high system utilization. However, the current definition of harmonic task set is limited only to tasks with deadlines equal to their periods. In this paper, we extend the concept of \"harmonic task set\" to tasks with constrained deadlines, i.e., deadlines less than or equal to their periods. We show that a harmonic task set with constrained deadlines has a better schedulability than the non-harmonic one with the same task utilization. We employ this characteristic for task partitioning on multi-core platform, and our extensive experimental results show that, by taking the task harmonic relationship into consideration, our partitioning approach can greatly improve the schedulability of real-time tasks on multi-core platforms.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898055"}, {"primary_key": "4080708", "vector": [], "sparse_vector": [], "title": "DISCO: a low overhead in-network data compressor for energy-efficient chip multi-processors.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Data compression has been proposed to increase the utility of on-chip memory space or Network-on-Chip (NoC) bandwidth in energy-efficient processors. However, such techniques usually add additional compression and decompression latency to the critical path of memory access, which is one of the major factors limiting their application to processors. In contrast to prior work that deals with either cache compression or network compression separately, this study proposes a unified on-chip DIStributed data COmpressor, DISCO, to enable near-zero latency cache/NoC compression for chip multi-processors (CMPs) adopting Non-Uniform Cache Access (NUCA). DISCO integrates data compressors into NoC routers and seeks opportunity to overlap the de/compression latency with the NoC queuing delay through a coordinated NoC scheduling and cache compression mechanism With the support of DISCO that unifies the solutions of on-chip data compression, it is shown in evaluation that DISCO significantly boosts the efficiency of on-chip data caching and data moving.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898007"}, {"primary_key": "4080709", "vector": [], "sparse_vector": [], "title": "Correlated Bayesian Model Fusion: efficient performance modeling of large-scale tunable analog/RF integrated circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Tunable circuit has emerged as a promising methodology to address the grand challenge posed by process variations. Efficient high-dimensional performance modeling of tunable analog/RF circuits is an important yet challenging task. In this paper, we propose a novel performance modeling approach for tunable circuits, referred to as Correlated Bayesian Model Fusion (C-BMF). The key idea is to encode the correlation information for both model template and coefficient magnitude among different knob configurations by using a unified prior distribution. The prior distribution is then combined with a few simulation samples via Bayesian inference to efficiently determine the unknown model coefficients. Two circuit examples designed in a commercial 32nm SOI CMOS process demonstrate that C-BMF achieves more than 2× cost reduction over the traditional state-of-the-art modeling technique without surrendering any accuracy.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897999"}, {"primary_key": "4080710", "vector": [], "sparse_vector": [], "title": "MTJ variation monitor-assisted adaptive MRAM write.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Spin-transfer torque random access memory (STT-RAM) and magnetoelectric random access memory (MeRAM) are promising non-volatile memory technologies. But STT-RAM and Me RAM both suffer from high write error rate due to thermal fluctuation of magnetization. Temperature and wafer-level process variation significantly exacerbate these problems. In this paper, we propose a design that adaptively selects optimized write pulse for STT-RAM and MeRAM to overcome ambient process and temperature variation. To enable the adaptive write, we design specific MTJ-based variation monitor, which precisely senses process and temperature variation. The monitor is over 10X faster, 5X more energy-efficient, and 20X smaller compared with conventional thermal monitors of similar accuracy. With adaptive write, the write latency of STT-RAM and MeRAM cache are reduced by up to 17% and 59% respectively, and application run time is improved by up to 41%.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897979"}, {"primary_key": "4080711", "vector": [], "sparse_vector": [], "title": "Latency sensitivity-based cache partitioning for heterogeneous multi-core architecture.", "authors": ["Po<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Shared last-level cache (LLC) management is a critical design issue for heterogeneous multi-cores. In this paper, we observe two major challenges: the contribution of LLC latency to overall performance varies among applications/cores and also across time; overlooking the off-chip latency factor often leads to adverse effects on overall performance. Hence, we propose a Latency Sensitivity-based Cache Partitioning (LSP) framework, including a lightweight runtime mechanism to quantify the latency-sensitivity and a new cost function to guide the LLC partitioning. Results show that LSP improves the overall throughput by 8% on average (27% at most), compared with the state-of-the-art partitioning mechanism, TAP.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898036"}, {"primary_key": "4080712", "vector": [], "sparse_vector": [], "title": "An expected hypervolume improvement algorithm for architectural exploration of embedded processors.", "authors": ["<PERSON><PERSON>", "Jinglin Shi", "<PERSON><PERSON><PERSON>"], "summary": "Surrogate model based design space exploration (DSE) techniques have been widely used in finding the Pareto-optimal design points of embedded processor architectures. However, existing such methods lack of sound modeling of the uncertainty of the surrogate models, which greatly limits the searching scope. In this paper we propose an expected hypervolume improvement (EHVI) algorithm which models the uncertainty of an adaptive component selection and smoothing operator (ACOSSO) surrogate model by means of constructing a Gaussian random distribution and searches the Pareto points by taking an EHVI criterion. Experimental results prove the effectiveness of the proposed algorithm through comparing with two existing DSE algorithms.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897983"}, {"primary_key": "4080713", "vector": [], "sparse_vector": [], "title": "DeepBurning: automatic generation of FPGA-based learning accelerators for the neural network family.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent advances in Neural Networks (NN) are enabling more and more innovative applications. As an energy-efficient hardware solution, machine learning accelerators for CNNs or traditional ANNs are also gaining popularity in the area of embedded vision, robotics and cyberphysics. However, the design parameters of NN models vary significantly from application to application. Hence, it's hard to provide one general and highly-efficient hardware solution to accommodate all of them, and it is also impractical for the domain-specific developers to customize their flown hardware targeting on a specific NN model. To deal with this dilemma, this study proposes a design automation tool, DeepBurning, allowing the application developers to build from scratch learning accelerators that targets their specific NN models with custom configurations and optimized performance. DeepBurning includes a RTL-level accelerator generator and a coordinated compiler that generates the control flow and data layout under the user-specified constraints. The results can be used to implement FPGA-based NN accelerator or help generate chip design for early design stage. In general, DeepBurning supports a large family of NN models, and greatly simplifies the design flow of NN accelerators for the machine learning or AI application developers. The evaluation shows that the generated learning accelerators burnt to our FPGA board exhibit great power efficiency compared to state-of-the-art FPGA-based solutions.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898003"}, {"primary_key": "4080714", "vector": [], "sparse_vector": [], "title": "A new learning method for inference accuracy, core occupation, and performance co-optimization on TrueNorth chip.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Kent <PERSON><PERSON>", "Qing Wu", "<PERSON>", "Hai <PERSON>", "<PERSON><PERSON>"], "summary": "IBM TrueNorth chip uses digital spikes to perform neuromorphic computing and achieves ultrahigh execution parallelism and power efficiency. However, in TrueNorth chip, low quantization resolution of the synaptic weights and spikes significantly limits the inference (e.g., classification) accuracy of the deployed neural network model. Existing workaround, i.e., averaging the results over multiple copies instantiated in spatial and temporal domains, rapidly exhausts the hardware resources and slows down the computation. In this work, we propose a novel learning method on TrueNorth platform that constrains the random variance of each computation copy and reduces the number of needed copies. Compared to the existing learning method, our method can achieve up to 68.8% reduction of the required neuro-synaptic cores or 6.5× speedup, with even slightly improved inference accuracy.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897968"}, {"primary_key": "4080715", "vector": [], "sparse_vector": [], "title": "Hybrid STT-CMOS designs for reverse-engineering prevention.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a rigorous step towards design-for-assurance by introducing a new class of logically reconfigurable design resilient to design reverse engineering. Based on the non-volatile spin transfer torque (STT) magnetic technology, we introduce a basic set of non-volatile reconfigurable Look-Up-Table (LUT) logic components (NV-STT-based LUTs). STT-based LUT with significantly different set of characteristics compared to CMOS provides new opportunities to enhance design security yet makes it challenging to remain highly competitive with custom CMOS or even SRAM-based LUT in terms of power, performance and area. To address these challenges, we propose several algorithms to select and replace custom CMOS gates with reconfigurable STT-based LUTs during design implementation such that the functionality of STT-based components and therefore the entire design cannot be determined in any manageable time, rendering any design reverse engineering attack ineffective. Our study conducted on a large number of standard circuit benchmarks concludes significant resiliency of hybrid STT-CMOS circuits against various types of attacks. Furthermore, the selection algorithms on average have a small impact of less than 3%, 8%, and 3% on design parametric constraints including performance, power and area, respectively.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898099"}, {"primary_key": "4080716", "vector": [], "sparse_vector": [], "title": "QB-trees: towards an optimal topological representation and its applications to analog layout designs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A modern analog placer often needs to consider various geometrical constraints to generate desired layouts. To handle general constraints simultaneously, current state-of-the-art works adopt simulated annealing based on topological representations, due to their smaller solution spaces and higher efficiency. However, no published work achieves the optimal time complexity for general geometrical constraint handling and module packing. Besides, only limited constraints are considered and handled in each work. To remedy these insufficiencies, we present a new hybrid representation of a quadtree and B*-trees (QB-tree, for short) to handle general geometrical constraints while achieving linear, lower-bound time complexity of module packing and constraint handling. Experimental results based on real industrial designs with various constraints show that our placer outperforms the leading published works in both runtime and solution quality.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898074"}, {"primary_key": "4080717", "vector": [], "sparse_vector": [], "title": "An efficient method for multi-level approximate logic synthesis under error rate constraint.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Approximate computing is an emerging design paradigm targeting at error-tolerant applications. It trades off accuracy for improvement in hardware cost and energy efficiency. In this paper, we propose a novel approach for multi-level approximate logic synthesis under error rate constraint. The basic idea of our approach is to pick nodes in a Boolean network and shrink them by approximating their factored-form expressions. We propose two different algorithms to implement the basic idea. The first algorithm iteratively picks the most effective node at present to shrink. Its drawback lies in that it may need a large number of iterations. To overcome this drawback, the second algorithm formulates a knapsack problem to pick multiple nodes for shrinking simultaneously. It is still iterative, but the number of iterations is greatly reduced. We apply the two algorithms to MCNC benchmarks and arithmetic circuits including adders and multipliers. The experimental results demonstrated that our algorithms perform better in area saving and are 1.7 and 5.9 times faster, respectively, compared with the state-of-the-art approach.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897982"}, {"primary_key": "4080718", "vector": [], "sparse_vector": [], "title": "Design partitioning for large-scale equivalence checking and functional correction.", "authors": ["<PERSON>", "Yi-<PERSON> Sun", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Equivalence checking and functional correction are important steps ensuring design correctness. Direct verification of large industrial designs is challenging and often requires a divide-and-conquer approach. The 2015 CAD Contest at ICCAD poses the challenge of large-scale equivalence checking and functional correction. This paper reports our work in the competition. An algorithm to identify cut-points in both equivalent and inequivalent circuit pairs is proposed for design partitioning. To obtain high quality cuts, we take into consideration their proximity information in cone sizes and circuit depths. Experiments on the contest benchmarks show our method achieves top quality results among all contestants.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898004"}, {"primary_key": "4080719", "vector": [], "sparse_vector": [], "title": "Flip-flop clustering by weighted K-means algorithm.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a novel flip-flop clustering and relocation framework to help reduce the overall chip power consumption. Given an initial legalized placement, our goal is to reduce the wirelength of the clock network by reducing distance between flip-flops and their drivers, while minimize the disturbance of original placement result. The idea is to form flip-flops into clusters, such that all flip-flops within each cluster can be placed near a single clock buffer and connected by a simple routing structure. Therefore, overall clock network wirelength can be greatly reduced and significant power savings can be achieved. In particular, we propose a modified K-means algorithm which effectively assigns flops into clusters at the clustering step. Then, at the relocation step, flops are actually relocated and regularly structured clusters are formed. Our framework is evaluated on real industrial benchmarks. We compare our framework with a flow without flop clustering and an industrial window based flop clustering flow. Experimental results show our framework can achieve significant dynamic power savings while has less disturbance of the original placement.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898025"}, {"primary_key": "4080720", "vector": [], "sparse_vector": [], "title": "Switched by input: power efficient structure for RRAM-based convolutional neural network.", "authors": ["<PERSON><PERSON><PERSON> Xi<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Convolutional Neural Network (CNN) is a powerful technique widely used in computer vision area, which also demands much more computations and memory resources than traditional solutions. The emerging metal-oxide resistive random-access memory (RRAM) and RRAM crossbar have shown great potential on neuromorphic applications with high energy efficiency. However, the interfaces between analog RRAM crossbars and digital peripheral functions, namely Analog-to-Digital Converters (ADCs) and Digital-to-Analog Converters (DACs), consume most of the area and energy of RRAM-based CNN design due to the large amount of intermediate data in CNN. In this paper, we propose an energy efficient structure for RRAM-based CNN. Based on the analysis of data distribution, a quantization method is proposed to transfer the intermediate data into 1 bit and eliminate DACs. An energy efficient structure using input data as selection signals is proposed to reduce the ADC cost for merging results of multiple crossbars. The experimental results show that the proposed method and structure can save 80% area and more than 95% energy while maintaining the same or comparable classification accuracy of CNN on MNIST.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898101"}, {"primary_key": "4080721", "vector": [], "sparse_vector": [], "title": "Data cache prefetching via context directed pattern matching for coarse-grained reconfigurable arrays.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a context directed pattern matching (CDPM) mechanism, which employs the context of the coarse-grained reconfigurable arrays (CGRAs) as a guide to improve cache prefetching accuracy. CDPM generates a prefetch pattern for an initially executed context, and reuses the pattern to issue prefetch requests when the context is again executed on CGRA. To eliminate the outdated prefetch pattern, CDPM also evaluates the prefetching accuracy of the prefetch pattern at run-time. Experiments showed that CDPM averagely improved performance by 31.1% compared to tests without any prefetching and by 7.7% compared to state-of-the-art prefetching techniques.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898001"}, {"primary_key": "4080722", "vector": [], "sparse_vector": [], "title": "Synergistic timing speculation for multi-threaded programs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we address the problem of timing speculation for multi-threaded workloads executing on a multi-core processor. Our approach is based on a new observation --- heterogeneity in path sensitization delays across different threads in multi-threaded programs. Leveraging this heterogeneity, we propose Synergistic Timing Speculation (SynTS) to jointly optimize the energy and execution time of multithreaded applications. In particular, SynTS uses a sampling based online error probability estimation technique, coupled with a polynomial time algorithm, to optimally determine the voltage, frequency and the amount of timing speculation for each thread. Our experimental evaluations, based on detailed cross-layer simulations, demonstrate that SynTS reduces energy delay product by up to 21%, compared to existing timing speculation schemes.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898102"}, {"primary_key": "4080723", "vector": [], "sparse_vector": [], "title": "DAG-aware logic synthesis of datapaths.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Traditional datapath synthesis for standard-cell designs go through extraction of arithmetic operations from the high-level description, high-level synthesis, and netlist generation. In this paper, we take a fresh look at applying high-level synthesis methodologies in logic synthesis. We present a DAG-Aware synthesis technique for datapaths synthesis which is implemented using And-Inv-Graphs. Our approach targets area minimization. The proposed algorithm includes identifying vector multiplexers, searching for common specification logic, and reallocating multiplexers in the Boolean network. We propose an algorithm to identify common specification logic by using subgraph isomorphism. Experimental results show that our technique can provide over 10% area reduction beyond the traditional design flow. The proposed algorithm is tested on industry designs and academic benchmark suits using IBM 14nm technology.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898000"}, {"primary_key": "4080724", "vector": [], "sparse_vector": [], "title": "Distributed on-chip regulation: theoretical stability foundation, over-design reduction and performance optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "While distributed on-chip voltage regulation offers an appealing solution to power delivery, designing power delivery networks (PDNs) with distributed on-chip voltage regulators with guaranteed stability is challenging because of the complex interactions between active regulators and the bulky passive network. The recently developed hybrid stability theory provides an efficient stability checking and design approach, giving rise to highly desirable localized design of PDNs. However, the inherent conservativeness of the hybrid stability criteria can lead to pessimism in stability evaluation and hence large over-design. We address this challenge by proposing an optimal frequency-dependent system partitioning technique to significantly reduce the amount of pessimism in stability analysis. With theoretical rigor, we show how to partition a PDN system by employing optimal frequency-dependent impedance splitting between the passive network and voltage regulators while maintaining the desired theoretical properties of the partitioned system blocks upon which the hybrid stability principle is anchored. We demonstrate a new stability-ensuring PDN design approach with the proposed over-design reduction technique using an automated optimization flow which significantly boosts regulation performance and power efficiency.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898008"}, {"primary_key": "4080725", "vector": [], "sparse_vector": [], "title": "Resource budgeting for reliability in reconfigurable architectures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "SRAM-based reconfigurable architectures are susceptible to soft-errors. Accelerators in the reconfigurable fabric need to be protected by fault tolerance techniques such as modular redundancy and scrubbing. However, blindly applying these techniques to all accelerators leads to suboptimal performance due to overprotection.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898084"}, {"primary_key": "4080726", "vector": [], "sparse_vector": [], "title": "Architecting energy-efficient STT-RAM based register file on GPGPUs via delta compression.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "To facilitate efficient context switches, GPUs usually employ a large-capacity register file to accommodate a massive amount of context information. However, the large register file introduces high power consumption, flowing to high leakage power SRAM cells. Emerging non-volatile STT-RAM memory has recently been studied as a potential replacement to alleviate the leakage challenge when constructing register files on GPUs. Unfortunately, due to the long write latency and high energy consumption associated with write operations in STT-RAM, simply replacing SRAM with STTRAM for register files would incur non-trivial performance overhead and only bring marginal energy benefits.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897989"}, {"primary_key": "4080727", "vector": [], "sparse_vector": [], "title": "Write-back aware shared last-level cache management for hybrid main memory.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hybrid main memory with both DRAM and emerging non-volatile memory (NVM) becomes a promising solution for high performance and energy-efficient embedded systems. Cache plays an important role and highly affects the number of write backs to NVM and DRAM blocks. However, existing cache policies fail to fully address the significant asymmetry between NVM operations (especially writes) and DRAM operations, leading to non-optimal system designs. We propose a write-back aware last-level cache management scheme for the hybrid main memory, which improves the cache hit ratio of NVM memory blocks and minimizes write-backs to NVM. Experimental results show that our proposed framework leads to better performance and energy saving compared with the state-of-the-art cache management scheme for hybrid main memory architecture.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898110"}, {"primary_key": "4080728", "vector": [], "sparse_vector": [], "title": "EffiTest: efficient delay test and statistical prediction for configuring post-silicon tunable buffers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "At nanometer manufacturing technology nodes, process variations significantly affect circuit performance. To combat them, post-silicon clock tuning buffers can be deployed to balance timing budgets of critical paths for each individual chip after manufacturing. The challenge of this method is that path delays should be measured for each chip to configure the tuning buffers properly. Current methods for this delay measurement rely on path-wise frequency stepping. This strategy, however, requires too much time from expensive testers. In this paper, we propose an efficient delay test framework (EffiTest) to solve the post-silicon testing problem by aligning path delays using the already-existing tuning buffers in the circuit. In addition, we only test representative paths and the delays of other paths are estimated by statistical delay prediction. Experimental results demonstrate that the proposed method can reduce the number of frequency stepping iterations by more than 94% with only a slight yield loss.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898017"}, {"primary_key": "4080729", "vector": [], "sparse_vector": [], "title": "Improving high-level synthesis with decoupled data structure optimization.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Existing high-level synthesis (HLS) tools are mostly effective on algorithm-dominated programs that only use primitive data structures such as fixed size arrays and queues. However, many widely used data structures such as priority queues, heaps, and trees feature complex member methods with data-dependent work and irregular memory access patterns. These methods can be inlined to their call sites, but this does not address the aforementioned issues and may further complicate conventional HLS optimizations, resulting in a low-performance hardware implementation. To overcome this deficiency, we propose a novel HLS architectural template in which complex data structures are decoupled from the algorithm using a latency-insensitive interface. This enables overlapped execution of the algorithm and data structure methods, as well as parallel and out-of-order execution of independent methods on multiple decoupled lanes. Experimental results across a variety of real-life benchmarks show our approach is capable of achieving very promising speedups without causing significant area overhead.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898030"}, {"primary_key": "4080730", "vector": [], "sparse_vector": [], "title": "A low-cost conflict-free NoC for GPGPUs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As integrated circuits are limited by hardware resources, reducing cost while maintaining the performance becomes especially important. In this article, we propose a conflict-free NoC (cfNoC) for the GPGPU request network. The cfNoC eliminates (i) conflicts among different columns by deploying an exclusive subnet for each column, and (ii) conflicts inside the same column by using a token-based mechanism. The elimination of conflicts allows cfNoC to exploit different subnet channel widths to maintain the performance while reducing cost. Compared with a baseline mesh with 1 VC, our work reduces request network area by 22.6% and power by 23.7%. With 2 VCs, cfNoC achieves more than 37.6% power reduction compared to the baseline and the checkboard placement/routing (CP) design. All these benefits do not cause any performance loss.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897963"}, {"primary_key": "4080731", "vector": [], "sparse_vector": [], "title": "Accurate phase-level cross-platform power and performance estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Fast and accurate performance and power prediction is a key challenge in co-development of hardware and software. Traditional analytical or simulation-based approaches are often too inaccurate or slow. In this work, we propose LACross, a novel learning-based, analytical cross-platform prediction framework that provides fast and accurate estimation of time-varying software performance and power consumption on a target hardware platform. We employ a fine-grained phase-based approach, where the learning algorithm synthesizes analytical proxy models that predict the performance and power of the workload in each program phase from performance statistics obtained through hardware counter measurements on the host. Our learning approach relies on a one-time training phase using a target reference model or real hardware. We applied our approach to 35 benchmarks from SPEC 2006, SD-VBS and MiBench. Results show on average over 97% prediction accuracy for predicting both fine-grain performance and power traces at speeds of over 500 MIPS.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897977"}, {"primary_key": "4080732", "vector": [], "sparse_vector": [], "title": "Lin-analyzer: a high-level performance analysis tool for FPGA-based accelerators.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The increasing complexity of FPGA-based accelerators, coupled with time-to-market pressure, makes high-level synthesis (HLS) an attractive solution to improve designer productivity by abstracting the programming effort above register-transfer level (RTL). HLS offers various architectural design options with different trade-offs via pragmas (loop unrolling, loop pipelining, array partitioning). However, non-negligible HLS runtime renders manual or automated HLS-based exhaustive architectural exploration practically infeasible. To address this challenge, we present Lin-Analyzer, a high-level accurate performance analysis tool that enables rapid design space exploration with various pragmas for FPGA-based accelerators without requiring RTL implementations.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2898040"}, {"primary_key": "4080733", "vector": [], "sparse_vector": [], "title": "A novel cross-layer framework for early-stage power delivery and architecture co-exploration.", "authors": ["<PERSON>", "Kassa<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the reduced noise margin brought by relentless technology scaling, power integrity assurance has become more challenging than ever. On the other hand, traditional design methodologies typically focus on a single design layer without much cross-layer interaction, potentially introducing unnecessary guard-band and wasting significant design resources. Both issues imperatively call for a cross-layer framework for the co-exploration of power delivery (PD) and system architecture, especially at early design stage with larger design freedom. Unfortunately, such a framework does not exist yet in the literature. As a step forward, this paper provides a run-time simulation framework of both PD and architecture and captures their interactions. Enabled by the proposed recursive run-time PD model, it handles an entire PD system on-the-fly simulation with <1% deviation from SPICE. Moreover, with a seamless interaction among architecture, power and PD simulators, it has the capability to simulate benchmarks with millions of cycles within reasonable time. A support vector regression (SVR) model is employed to further speed up power estimation of functional units to millions cycle/second with good accuracy. The experimental results of running PARSEC suite have illustrated the framework's capability to explore hardware configurations to discover the co-effect of PD and architecture for early stage optimization. Moreover, it also illustrates multiple over-pessimisms in traditional methodologies.", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937.2897969"}, {"primary_key": "4209411", "vector": [], "sparse_vector": [], "title": "Proceedings of the 53rd Annual Design Automation Conference, DAC 2016, Austin, TX, USA, June 5-9, 2016", "authors": [], "summary": "Touchscreen technology plays an important role in the booming mobile devices market. Traditional touchscreen only provides 2D interactions with limited user experience. To overcome these limitations, we propose a novel 3D touch sensing system called the ...", "published": "2016-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/2897937"}]