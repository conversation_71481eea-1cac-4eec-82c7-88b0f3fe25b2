[{"primary_key": "3871399", "vector": [], "sparse_vector": [], "title": "HapticDrone: An Encountered-Type Kinesthetic Haptic Interface with Controllable Force Feedback: Initial Example for 1D Haptic Feedback.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present HapticDrone, a concept to generate controllable and comparable force feedback for direct haptic interaction with a drone. As a proof-of-concept study this paper focuses on creating haptic feedback only in 1D direction. To this end, an encountered-type, safe and un-tethered haptic display is implemented. An overview of the system and details on how to control the force output of drones is provided. Our current prototype generates forces up to 1.53 N upwards and 2.97 N downwards. This concept serves as a first step towards introducing drones as mainstream haptic devices.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131821"}, {"primary_key": "3871400", "vector": [], "sparse_vector": [], "title": "CommandBoard: Creating a General-Purpose Command Gesture Input Space for Soft Keyboard.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "CommandBoard offers a simple, efficient and incrementally learnable technique for issuing gesture commands from a soft keyboard. We transform the area above the keyboard into a command-gesture input space that lets users draw unique command gestures or type command names followed by execute. Novices who pause see an in-context dynamic guide, whereas experts simply draw. Our studies show that CommandBoard's inline gesture shortcuts are significantly faster (almost double) than markdown symbols and significantly preferred by users. We demonstrate additional techniques for more complex commands, and discuss trade-offs with respect to the user's knowledge and motor skills, as well as the size and structure of the command space.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126639"}, {"primary_key": "3871401", "vector": [], "sparse_vector": [], "title": "Trigger-Action-Circuits: Leveraging Generative Design to Enable Novices to Design and Build Circuitry.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The dramatic decrease in price and increase in availability of hobbyist electronics has led to a wide array of embedded and interactive devices. While electronics have become more widespread, developing and prototyping the required circuitry for these devices is still difficult, requiring knowledge of electronics, components, and programming. In this paper, we present Trigger-Action-Circuits (TAC), an interactive system that leverages generative design to produce circuitry, firmware, and assembly instructions, based on high-level, behavioural descriptions. TAC is able to generate multiple candidate circuits from a behavioural description, giving the user a number of alternative circuits that may be best suited to their use case (e.g., based on cost, component availability or ease of assembly). The generated circuitry uses off-the-shelf, commodity electronics, not specialized hardware components, enabling scalability and extensibility. TAC supports a range of common components and behaviors that are frequently required for prototyping electronic circuits. A user study demonstrated that TAC helps users avoid problems encountered during circuit design and assembly, with users completing their circuits significantly faster than with traditional methods.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126637"}, {"primary_key": "3871402", "vector": [], "sparse_vector": [], "title": "CanalSense: Face-Related Movement Recognition System based on Sensing Air Pressure in Ear Canals.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a jaw, face, or head movement (face-related movement) recognition system called CanalSense. It recognizes face-related movements using barometers embedded in earphones. We find that face-related movements change air pressure inside the ear canals, which shows characteristic changes depending on the type and degree of the movement. We also find that such characteristic changes can be used to recognize face-related movements. We conduct an experiment to measure the accuracy of recognition. As a result, random forest shows per-user recognition accuracies of 87.6% for eleven face-related movements and 87.5% for four OpenMouth levels.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126649"}, {"primary_key": "3871403", "vector": [], "sparse_vector": [], "title": "Designing and Evaluating Livefonts.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The emergence of personal computing devices offers both a challenge and opportunity for displaying text: small screens can be hard to read, but also support higher resolution. To fit content on a small screen, text must be small. This small text size can make computing devices unusable, in particular to low-vision users, whose vision is not correctable with glasses. Usability is also decreased for sighted users straining to read the small letters, especially without glasses at hand. We propose animated scripts called livefonts for displaying English with improved legibility for all users. Because paper does not support animation, traditional text is static. However, modern screens support animation, and livefonts capitalize on this capability. We evaluate our livefont variations' legibility through a controlled lab study with low-vision and sighted participants, and find our animated scripts to be legible across vision types at approximately half the size (area) of traditional letters, while previous smartfonts (static alternate scripts) did not show a significant legibility advantage for low-vision users. We evaluate the learnability of our livefont with low-vision and sighted participants, and find it to be comparably learnable to static smartfonts after two thousand practice sentences.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126660"}, {"primary_key": "3871404", "vector": [], "sparse_vector": [], "title": "More than a Feeling: The MiFace Framework for Defining Facial Communication Mappings.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Facial expressions transmit a variety of social, grammatical, and affective signals. For technology to leverage this rich source of communication, tools that better model the breadth of information they convey are required. MiFace is a novel framework for creating expression lexicons that map signal values to parameterized facial muscle movements. In traditional mapping paradigms using posed photographs, naïve judges select from predetermined label sets and movements are inferred by trained experts. The set of generally accepted expressions established in this way is limited to six basic displays of affect. In contrast, our approach generatively simulates muscle movements on a 3D avatar. By applying natural language processing techniques to crowdsourced free-response labels for the resulting images, we efficiently converge on an expression's value across signal categories. Two studies returned 218 discriminable facial expressions with 51 unique labels. The six basic emotions are included, but we additionally define such nuanced expressions as embarrassed, curious, and hopeful.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126640"}, {"primary_key": "3871405", "vector": [], "sparse_vector": [], "title": "Learning Visual Importance for Graphic Designs and Data Visualizations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Peter <PERSON>;<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Knowing where people look and click on visual designs can provide clues about how the designs are perceived, and where the most important or relevant content lies. The most important content of a visual design can be used for effective summarization or to facilitate retrieval from a database. We present automated models that predict the relative importance of different elements in data visualizations and graphic designs. Our models are neural networks trained on human clicks and importance annotations on hundreds of designs. We collected a new dataset of crowdsourced importance, and analyzed the predictions of our models with respect to ground truth importance and human eye movements. We demonstrate how such predictions of importance can be used for automatic design retargeting and thumbnailing. User studies with hundreds of MTurk participants validate that, with limited post-processing, our importance-driven applications are on par with, or outperform, current state-of-the-art methods, including natural image saliency. We also provide a demonstration of how our importance predictions can be built into interactive design tools to offer immediate feedback during the design process.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126653"}, {"primary_key": "3871406", "vector": [], "sparse_vector": [], "title": "INVISO: A Cross-platform User Interface for Creating Virtual Sonic Environments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The predominant interaction paradigm of current audio spatialization tools, which are primarily geared towards expert users, imposes a design process in which users are characterized as stationary, limiting the application domain of these tools. Navigable 3D sonic virtual realities, on the other hand, can support many applications ranging from soundscape prototyping to spatial data representation. Although modern game engines provide a limited set of audio features to create such sonic environments, the interaction methods are inherited from the graphical design features of such systems, and are not specific to the auditory modality. To address such limitations, we introduce INVISO, a novel web-based user interface for designing and experiencing rich and dynamic sonic virtual realities. Our interface enables both novice and expert users to construct complex immersive sonic environments with 3D dynamic sound components. INVISO is platform-independent and facilitates a variety of mixed reality applications, such as those where users can simultaneously experience and manipulate a virtual sonic environment. In this paper, we detail the interface design considerations for our audio-specific VR tool. To evaluate the usability of INVISO, we conduct two user studies: The first demonstrates that our visual interface effectively facilitates the generation of creative audio environments; the second demonstrates that both expert and non-expert users are able to use our software to accurately recreate complex 3D audio scenes.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126644"}, {"primary_key": "3871407", "vector": [], "sparse_vector": [], "title": "Ani-<PERSON><PERSON>: A Mixed-Reality Modular Robotics System.", "authors": ["<PERSON><PERSON> Cao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Ani-Bot, a modular robotics system that allows users to construct Do-It-Yourself (DIY) robots and use mixed-reality approach to interact with them. Ani-Bot enables novel user experience by embedding Mixed-Reality Interaction (MRI) in the three phases of interacting with a modular construction kit, namely, Creation, Tweaking, and Usage. In this paper, we first present the system design that allows users to instantly perform MRI once they finish assembling the robot. Further, we discuss the augmentations offered by MRI in the three phases in specific.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131833"}, {"primary_key": "3871408", "vector": [], "sparse_vector": [], "title": "Characterizing Latency in Touch and Button-Equipped Interactive Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a low cost method to measure and characterize the end-to-end latency when using a touch system (tap latency) or an input device equipped with a physical button. Our method relies on a vibration sensor attached to a finger and a photo-diode to detect the screen response. Both are connected to a micro-controller connected to a host computer using a low-latency USB communication protocol in order to combine software and hardware probes to help determine where the latency comes from. We present the operating principle of our method before investigating the main sources of latency in several systems. We show that most of the latency originates from the display side. Our method can help application designers characterize and troubleshoot latency on a wide range of interactive systems.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126606"}, {"primary_key": "3871409", "vector": [], "sparse_vector": [], "title": "Data Storage and Interaction using Magnetized Fabric.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper enables data storage and interaction with smart fabric, without the need for onboard electronics or batteries. To do this, we present the first smart fabric design that harnesses the ferromagnetic properties of conductive thread. Specifically, we manipulate the polarity of magnetized fabric and encode different forms of data including 2D images and bit strings. These bits can be read by swiping a commodity smartphone across the fabric, using its inbuilt magnetometer. Our results show that magnetized fabric retains its data even after washing, drying and ironing. Using a glove made of magnetized fabric, we can also perform six gestures in front of a smartphone, with a classification accuracy of 90.1%. Finally, using magnetized thread, we create fashion accessories like necklaces, ties, wristbands and belts with data storage capabilities as well as enable authentication applications.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126620"}, {"primary_key": "3871410", "vector": [], "sparse_vector": [], "title": "Panning and Zooming High-Resolution Panoramas in Virtual Reality Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Two recent innovations in immersive media include the ability to capture very high resolution panoramic imagery, and the rise of consumer level heads-up displays for virtual reality. Unfortunately, zooming to examine the high resolution in VR breaks the basic contract with the user, that the FOV of the visual field matches the FOV of the imagery. In this paper, we study methods to overcome this restriction to allow high resolution panoramic imagery to be able to be explored in VR. We introduce and test new interface modalities for exploring high resolution panoramic imagery in VR. In particular, we demonstrate that limiting the visual FOV of the zoomed in imagery to the central portion of the visual field, and modulating the transparency or zoom level of the imagery during rapid panning, reduce simulator sickness and help with targeting tasks.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126617"}, {"primary_key": "3871411", "vector": [], "sparse_vector": [], "title": "A Thermally Enhanced Weather Checking System in VR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this project, by combining thermal feedback with Virtual Reality (VR) and utilizing thermal stimuli to present temperature data of weather, we attempted to provide a multi-sensory experience for enhancing users' perception of environment in virtual space. By integrating thermal modules with the current VR head mounted display to provide thermal feedback directly on the face, and by setting thermal stimulus to provide similar feeling towards real air temperature, we developed an application in which users are able to \"feel\" the weather in VR environment. An user experiment was also conducted to evaluate our design, according to which we verified that thermal feedback can improve users' experience in perceiving environment, and this research also provided a new approach for setting thermal feedback for presenting environmental information in virtual space.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131825"}, {"primary_key": "3871412", "vector": [], "sparse_vector": [], "title": "Mutual Human Actuation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Human actuation is the idea of using people to provide large-scale force feedback to users. The Haptic Turk system, for example, used four human actuators to lift and push a virtual reality user; TurkDeck used ten human actuators to place and animate props for a single user. While the experience of human actuators was decent, it was still inferior to the experience these people could have had, had they participated as a user. In this paper, we address this issue by making everyone a user. We introduce mutual human actuation, a version of human actuation that works without dedicated human actuators. The key idea is to run pairs of users at the same time and have them provide human actuation to each other. Our system, Mutual Turk, achieves this by (1) offering shared props through which users can exchange forces while obscuring the fact that there is a human on the other side, and (2) synchronizing the two users' timelines such that their way of manipulating the shared props is consistent across both virtual worlds. We demonstrate mutual human actuation with an example experience in which users pilot kites though storms, tug fish out of ponds, are pummeled by hail, battle monsters, hop across chasms, push loaded carts, and ride in moving vehicles.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126667"}, {"primary_key": "3871413", "vector": [], "sparse_vector": [], "title": "Grabity: A Wearable Haptic Interface for Simulating Weight and Grasping in Virtual Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Ungrounded haptic devices for virtual reality (VR) applications lack the ability to convincingly render the sensations of a grasped virtual object's rigidity and weight. We present Grabity, a wearable haptic device designed to simulate kinesthetic pad opposition grip forces and weight for grasping virtual objects in VR. The device is mounted on the index finger and thumb and enables precision grasps with a wide range of motion. A unidirectional brake creates rigid grasping force feedback. Two voice coil actuators create virtual force tangential to each finger pad through asymmetric skin deformation. These forces can be perceived as gravitational and inertial forces of virtual objects. The rotational orientation of the voice coil actuators is passively aligned with the real direction of gravity through a revolute joint, causing the virtual forces to always point downward. This paper evaluates the performance of Grabity through two user studies, finding promising ability to simulate different levels of weight with convincing object rigidity. The first user study shows that Grabity can convey various magnitudes of weight and force sensations to users by manipulating the amplitude of the asymmetric vibration. The second user study shows that users can differentiate different weights in a virtual environment using Grabity.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126599"}, {"primary_key": "3871414", "vector": [], "sparse_vector": [], "title": "The Feedback Block Model for an Adaptive E-Book.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Soon-<PERSON><PERSON>"], "summary": "The emergence of social reading services has enabled readers to participate actively in reading activities by means of sharing and feedback. Readers can state their opinion on a book by providing feedback. However, because current e-books are published with fixed, unchangeable content, it is difficult to reflect the reader's feedback on them. In this paper, we propose a system for an adaptive e-book that dynamically updates itself on user participation. To achieve this, we designed a Feedback Block Model and a Feedback Engine. In the Feedback Block Model, at the time of publication, the author defines the type of feedback expected from readers. After publication, the Feedback Engine collects and aggregates the readers? feedback. The Feedback Engine can be configured with drag-and-drop block programming, and hence, even authors inexperienced in programming can create an adaptive e-book.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131834"}, {"primary_key": "3871415", "vector": [], "sparse_vector": [], "title": "MatchPoint: Spontaneous Spatial Coupling of Body Movement for Touchless Pointing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Pointing is a fundamental interaction technique where user movement is translated to spatial input on a display. Conventionally, this is based on a rigid configuration of a display coupled with a pointing device that determines the types of movement that can be sensed, and the specific ways users can affect pointer input. Spontaneous spatial coupling is a novel input technique that instead allows any body movement, or movement of tangible objects, to be appropriated for touchless pointing on an ad hoc basis. Pointer acquisition is facilitated by the display presenting graphical objects in motion, to which users can synchronise to define a temporary spatial coupling with the body part or tangible object they used in the process. The technique can be deployed using minimal hardware, as demonstrated by MatchPoint, a generic computer vision-based implementation of the technique that requires only a webcam. We explore the design space of spontaneous spatial coupling, demonstrate the versatility of the technique with application examples, and evaluate MatchPoint performance using a multi-directional pointing task.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126626"}, {"primary_key": "3871416", "vector": [], "sparse_vector": [], "title": "Hacking Computer Science History: A Cultural Intervention.", "authors": ["<PERSON><PERSON>"], "summary": "One cannot understand computer hacking without delving into the history of computer science but is the converse true? In this talk, I examine zones of collaboration as well as points of tension between the fields of computer science/engineering and computer hacking. By drawing on my anthropological research on hackers and turning to a set of cases around voting machines vulnerabilities, the Unix operating system, cryptography, and the Bit Torrent protocol, this talk suggests that while the object and practices around computers help account for a range of important similarities, the differences end there. Significant cultural differences and institutional constraints account for distinct moral and even technical trajectories evident in these two domains. I end reflecting on a slice of the important differences to asses what computer science may have to offer hacking and what hacking may have to offer the field of computer science.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126669"}, {"primary_key": "3871417", "vector": [], "sparse_vector": [], "title": "ImAxes: Immersive Axes as Embodied Affordances for Interactive Multivariate Data Visualisation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce ImAxes immersive system for exploring multivariate data using fluid, modeless interaction. The basic interface element is an embodied data axis. The user can manipulate these axes like physical objects in the immersive environment and combine them into sophisticated visualisations. The type of visualisation that appears depends on the proximity and relative orientation of the axes with respect to one another, which we describe with a formal grammar. This straight-forward composability leads to a number of emergent visualisations and interactions, which we review, and then demonstrate with a detailed multivariate data analysis use case.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126613"}, {"primary_key": "3871418", "vector": [], "sparse_vector": [], "title": "Jaguar: Indoor Navigation System for Organizations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Mira <PERSON>"], "summary": "Global Positioning System (GPS) technology is widely used for outdoor navigation, but it is still challenging to apply this technology to a mid-scale or indoor environment. Using GPS in this way raises issues, such as reliability, deployment cost, and maintenance. Recently, companies like Google have begun to provide accurate indoor mapping. However, current implementations rely on both Wi-Fi and cellular technologies which have a hard time identifying the user's exact location in an indoor environment. There are two research questions in this paper: (1) How do we design a flexible and cost efficient indoor navigation system for organizations? (2) How to find an optimized path in a mid-scale/local environment. Here we propose Jaguar, which is a novel navigation system that utilizes a customized KML map with NFC technologies to address above questions. Our system includes an Android mobile application, a web-based map authoring tool and an implementation of a Cartesian plane based path finding algorithm. The initial testing of the system shows successful adaptation for a school campus environment.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131826"}, {"primary_key": "3871419", "vector": [], "sparse_vector": [], "title": "Rico: A Mobile App Dataset for Building Data-Driven Design Applications.", "authors": ["Biplab Deka", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Data-driven models help mobile app designers understand best practices and trends, and can be used to make predictions about design performance and support the creation of adaptive UIs. This paper presents Rico, the largest repository of mobile app designs to date, created to support five classes of data-driven applications: design search, UI layout generation, UI code generation, user interaction modeling, and user perception prediction. To create Rico, we built a system that combines crowdsourcing and automation to scalably mine design and interaction data from Android apps at runtime. The Rico dataset contains design data from more than 9.7k Android apps spanning 27 categories. It exposes visual, textual, structural, and interactive design properties of more than 72k unique UI screens. To demonstrate the kinds of applications that Rico enables, we present results from training an autoencoder for UI layout similarity, which supports query- by-example search over UIs.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126651"}, {"primary_key": "3871420", "vector": [], "sparse_vector": [], "title": "ZIPT: Zero-Integration Performance Testing of Mobile App Designs.", "authors": ["Biplab Deka", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To evaluate the performance of mobile app designs, designers and researchers employ techniques such as A/B, usability, and analytics-driven testing. While these are all useful strategies for evaluating known designs, comparing many divergent solutions to identify the most performant remains a costly and difficult problem. This paper introduces a design performance testing approach that leverages existing app implementations and crowd workers to enable comparative testing at scale. This approach is manifest in ZIPT, a zero-integration performance testing platform that allows designers to collect detailed design and interaction data over any Android app -- including apps they do not own and did not build. Designers can deploy scripted tests via ZIPT to collect aggregate user performance metrics (e.g., completion rate, time on task) and qualitative feedback over third-party apps. Through case studies, we demonstrate that designers can use ZIPT's aggregate data and visualizations to understand the relative performance of interaction patterns found in the wild, and identify usability issues in existing Android apps.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126647"}, {"primary_key": "3871421", "vector": [], "sparse_vector": [], "title": "SkinBot: A Wearable Skin Climbing Robot.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We introduce SkinBot; a lightweight robot that moves over the skin surface with a two-legged suction-based locomotion mechanism and that captures a wide range of body parameters with an exchangeable multipurpose sensing module. We believe that robots that live on our skin such as SkinBot will enable a more systematic study of the human body and will offer great opportunities to advance many areas such as telemedicine, human-computer interfaces, body care, and fashion.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131796"}, {"primary_key": "3871422", "vector": [], "sparse_vector": [], "title": "SmoothMoves: Smooth Pursuits Head Movements for Augmented Reality.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "SmoothMoves is an interaction technique for augmented reality (AR) based on smooth pursuits head movements. It works by computing correlations between the movements of on-screen targets and the user's head while tracking those targets. The paper presents three studies. The first suggests that head based input can act as an easier and more affordable surrogate for eye-based input in many smooth pursuits interface designs. A follow-up study grounds the technique in the domain of augmented reality, and captures the error rates and acquisition times on different types of AR devices: head-mounted (2.6%, 1965ms) and hand-held (4.9%, 2089ms). Finally, the paper presents an interactive lighting system prototype that demonstrates the benefits of using smooth pursuits head movements in interaction with AR interfaces. A final qualitative study reports on positive feedback regarding the technique's suitability for this scenario. Together, these results indicate show SmoothMoves is viable, efficient and immediately available for a wide range of wearable devices that feature embedded motion sensing.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126616"}, {"primary_key": "3871423", "vector": [], "sparse_vector": [], "title": "A Digital Pen and Paper Email System for Older Adults.", "authors": ["Taciana Pontual Falcão", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "As communication technologies continue to rapidly evolve, older adults face challenges to access systems and devices, which may increase their social isolation. Our project investigates the design of a digital pen and paper-based communication system that allows users to connect to their family and friends' e-mail inboxes. Given the unique needs of older adults, we opted for a participatory design approach, prototyping the system with 22 older adults through a series of design workshops in two locations. Four individuals used our resulting system over a period of two weeks. Based on their feedback and a review of design workshops, we are currently in the process of refining our interface and preparing for a larger deployment study.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131827"}, {"primary_key": "3871424", "vector": [], "sparse_vector": [], "title": "Contour: An Efficient Voice-enabled Workflow for Producing Text-to-Speech Content.", "authors": ["<PERSON><PERSON><PERSON>", "Soyoung Shin", "Vids Samanta"], "summary": "Voice assistant technology has expanded the design space for voice-activated consumer products and audio-centric user experience. To navigate this emerging design space, Speech Synthesis Markup Language (SSML) provides a standard to characterize synthetic speech based on parametric control of the prosody elements, i.e. pitch, rate, volume, contour, range, and duration. However, the existing voice assistants utilizing Text-to-Speech (TTS) lack expressiveness. The need of a new production workflow for more efficient and emotional audio content using TTS is discussed. A prototype that allows a user to produce TTS-based content in any emotional tone using voice input is presented. To evaluate the new workflow enabled by the prototype, an initial comparative study is conducted against the parametric approach. Preliminary quantitative and qualitative results suggest the new workflow is more efficient based on time to complete tasks and number of design iterations, while maintaining the same level of user preferred production quality.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131835"}, {"primary_key": "3871425", "vector": [], "sparse_vector": [], "title": "FlexStylus: Leveraging Bend Input for Pen Interaction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "FlexStylus, a flexible stylus, detects deformation of the barrel as a vector with both a rotational and an absolute value, providing two degrees of freedom with the goal of improving the expressivity of digital art using a stylus device. We outline the construction of the prototype and the principles behind the sensing method, which uses a cluster of four fibre-optic based deformation sensors. We propose interaction techniques using the FlexStylus to improve menu navigation and tool selection. Finally, we describe a study comparing users' ability to match a changing target value using a commercial pressure stylus and the FlexStylus' absolute deformation. When using the FlexStylus, users had a significantly higher accuracy overall. This suggests that deformation may be a useful input method for future work considering stylus augmentation.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126597"}, {"primary_key": "3871426", "vector": [], "sparse_vector": [], "title": "HeatSpace: Automatic Placement of Displays by Empirical Analysis of User Behavior.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present HeatSpace, a system that records and empirically analyzes user behavior in a space and automatically suggests positions and sizes for new displays. The system uses depth cameras to capture 3D geometry and users' perspectives over time. To derive possible display placements, it calculates volumetric heatmaps describing geometric persistence and planarity of structures inside the space. It evaluates visibility of display poses by calculating a volumetric heatmap describing occlusions, position within users' field of view, and viewing angle. Optimal display size is calculated through a heatmap of average viewing distance. Based on the heatmaps and user constraints we sample the space of valid display placements and jointly optimize their positions. This can be useful when installing displays in multi-display environments such as meeting rooms, offices, and train stations.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126621"}, {"primary_key": "3871427", "vector": [], "sparse_vector": [], "title": "Raising the Heat: Electrical Muscle Stimulation for Simulated Heat Withdrawal Response.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Virtual Reality (VR) has numerous mechanisms for making a virtual scene more compellingly real. Most effort has been focused on visual and auditory techniques for immersive environments, although some commercial systems now include relatively crude haptic effects through handheld controllers or haptic suits. We present results from a pilot experiment demonstrating the use of Electrical Muscle Stimulation (EMS) to trick participants into thinking a surface is dangerously hot even though it is below 50C. This is accomplished by inducing an artificial heat withdrawal reflex by contracting the participant's bicep shortly after contact with the virtual hot surface. Although the effects of multiple experimental confounds need to be quantified in future work, results so far suggest that EMS could potentially be used to modify temperature perception in VR and AR contexts. Such an illusion has applications for VR gaming as well as emergency response and workplace training and simulation, in addition to providing new insights into the human perceptual system.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131828"}, {"primary_key": "3871428", "vector": [], "sparse_vector": [], "title": "CritiqueKit: A Mixed-Initiative, Real-Time Interface For Improving Feedback.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present CritiqueKit, a mixed-initiative machine-learning system that helps students give better feedback to peers by reusing prior feedback, reducing it to be useful in a general context, and retraining the system about what is useful in real time. CritiqueKit exploits the fact that novices often make similar errors, leading reviewers to reuse the same feedback on many different submissions. It takes advantage of all prior feedback, and classifies feedback as the reviewer types it. CritiqueKit continually updates the corpus of feedback with new comments that are added, and it guides reviewers to improve their feedback, and thus the entire corpus, over time.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131791"}, {"primary_key": "3871429", "vector": [], "sparse_vector": [], "title": "Designing Vibrotactile Widgets with Printed Actuators and Sensors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Physical controls are fabricated through complicated assembly of parts requiring expensive machinery and are prone to mechanical wear. One solution is to embed controls directly in interactive surfaces, but the proprioceptive part of gestural interaction that makes physical controls discoverable and usable solely by hand gestures is lost and has to be compensated, by vibrotactile feedback for instance. Vibrotactile actuators face the same aforementioned issues as for physical controls. We propose printed vibrotactile actuators and sensors. They are printed on plastic sheets, with piezoelectric ink for actuation, and with silver ink for conductive elements, such as wires and capacitive sensors. These printed actuators and sensors make it possible to design vibrotactile widgets on curved surfaces, without complicated mechanical assembly.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131800"}, {"primary_key": "3871430", "vector": [], "sparse_vector": [], "title": "Grip Force Estimation by Emitting Vibration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a method for determining grip force based on active bone-conducted sound sensing, which is an active acoustic sensing. In our previous studies, we estimated the joint angle, hand pose, and contact force by emitting a vibration to the body. We aspired to expand to an additional application of an active bone-conducted sound sensing, thus, we tried to estimate the grip force by creating a wrist-type device. The grip force was determined by using the power spectral density as the features, and gradient boosted regression trees (GBRT). Through evaluation experiments, the average error of the estimated grip force was around 15 N. Moreover, we confirmed that the grip strength could be determined with high accuracy.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131829"}, {"primary_key": "3871431", "vector": [], "sparse_vector": [], "title": "Pyro: Thumb-Tip Gesture Recognition Using Pyroelectric Infrared Sensing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present Pyro, a micro thumb-tip gesture recognition technique based on thermal infrared signals radiating from the fingers. Pyro uses a compact, low-power passive sensor, making it suitable for wearable and mobile applications. To demonstrate the feasibility of Pyro, we developed a self-contained prototype consisting of the infrared pyroelectric sensor, a custom sensing circuit, and software for signal processing and machine learning. A ten-participant user study yielded a 93.9% cross-validation accuracy and 84.9% leave-one-session-out accuracy on six thumb-tip gestures. Subsequent lab studies demonstrated Pyro's robustness to varying light conditions, hand temperatures, and background motion. We conclude by discussing the insights we gained from this work and future research questions.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126615"}, {"primary_key": "3871432", "vector": [], "sparse_vector": [], "title": "Towards Intermanual Apparent Motion of Thermal Pulses.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Perceptual illusions enable designers to go beyond hardware limitations to create rich haptic content. Nevertheless, spatio-temporal interactions for thermal displays have not been studied thoroughly. We focus on the apparent motion of hot and cold thermal pulses delivered at the thenar eminence of both hands. Here we show that 1000 ms hot and cold thermal pulses overlapping for about 40% of their actuation time are likely to produce a continuous apparent motion sensation. Furthermore, we show that the quality of the illusion (defined as the motion's temporal continuity) was more sensitive to changes in SOA for cold pulses in relation to hot pulses.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131814"}, {"primary_key": "3871433", "vector": [], "sparse_vector": [], "title": "HapticClench: Investigating Squeeze Sensations using Memory Alloys.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Squeezing sensations are one of the most common and intimate forms of human contact. In this paper, we investigate HapticClench, a device that generates squeezing sensations using shape memory alloys. We define squeezing feedback in terms of it perceptual properties and conduct a psychophysical evaluation of HapticClench. HapticClench is capable of generating up to four levels of distinguishable load and works well in distracted scenarios. HapticClench has a high spatial acuity and can generate spatial patterns on the wrist that the user can accurately recognize. We also demonstrate the use of HapticClench for communicating gradual progress of an activity, and for generating squeezing sensations using rings and loose bracelets.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126598"}, {"primary_key": "3871434", "vector": [], "sparse_vector": [], "title": "Eye Tracking Using Built-in Camera for Smartphone-based HMD.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Virtual reality (VR) using head-mounted displays (HMDs) is becoming popular. Smartphone-based HMDs (SbHMDs) are so low cost that users can easily experience VR. Unfortunately, their input modality is quite limited. We propose a real-time eye tracking technique that uses the built-in front facing camera to capture the user's eye. It realizes stand-alone pointing functionality without any additional device.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131809"}, {"primary_key": "3871435", "vector": [], "sparse_vector": [], "title": "Frictio: Passive Kinesthetic Force Feedback for Smart Ring Output.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Smart rings have a unique form factor suitable for many applications, however, they offer little opportunity to provide the user with natural output. We propose passive kinesthetic force feedback as a novel output method for rotational input on smart rings. With this new output channel, friction force profiles can be designed, programmed, and felt by a user when they rotate the ring. This modality enables new interactions for ring form factors. We demonstrate the potential of this new haptic output method though <PERSON><PERSON><PERSON>, a prototype smart ring. In a controlled experiment, we determined the recognizability of six force profiles, including Hard Stop, Ramp-Up, Ramp-Down, Resistant Force, Bump, and No Force. The results showed that participants could distinguish between the force profiles with 94% accuracy. We conclude by presenting a set of novel interaction techniques that <PERSON><PERSON><PERSON> enables, and discuss insights and directions for future research.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126622"}, {"primary_key": "3871436", "vector": [], "sparse_vector": [], "title": "SoundCraft: Enabling Spatial Interactions on Smartwatches using Hand Generated Acoustics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present SoundCraft, a smartwatch prototype embedded with a microphone array, that localizes angularly, in azimuth and elevation, acoustic signatures: non-vocal acoustics that are produced using our hands. Acoustic signatures are common in our daily lives, such as when snapping or rubbing our fingers, tapping on objects or even when using an auxiliary object to generate the sound. We demonstrate that we can capture and leverage the spatial location of such naturally occurring acoustics using our prototype. We describe our algorithm, which we adopt from the MUltiple SIgnal Classification (MUSIC) technique [31], that enables robust localization and classification of the acoustics when the microphones are required to be placed at close proximity. SoundCraft enables a rich set of spatial interaction techniques, including quick access to smartwatch content, rapid command invocation, in-situ sketching, and also multi-user around device interaction. Via a series of user studies, we validate SoundCraft's localization and classification capabilities in non-noisy and noisy environments.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126612"}, {"primary_key": "3871437", "vector": [], "sparse_vector": [], "title": "GraspForm: Shape and Hardness Rendering on Handheld Device toward Universal Interface for 3D Haptic TV.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The main goal of our research is to develop a haptic display that conveys the shapes, hardness, and textures of objects displayed on near-future 3D haptic TVs. We present a novel handheld device, GraspForm. This device renders the surface shapes and hardness of a virtual object that is represented in an absolute position in real space. GraspForm has a 2×2 matrix of actuated hemispheres for one fingertip, two actuated pads for the palm, and a force feedback actuator for the thumb. Our first experimental results showed that eight participants succeeded in recognizing the side geometries of a cylinder and a square prism regardless of the availability of visual information.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131824"}, {"primary_key": "3871438", "vector": [], "sparse_vector": [], "title": "Designing 3D-Printed Deformation Behaviors Using Spring-Based Structures: An Initial Investigation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent work in 3D printing has focused on tools and techniques to design deformation behaviors using mechanical structures such as joints and metamaterials. In this poster, we explore how to embed and control mechanical springs to create deformable 3D-printed objects. We propose an initial design space of 3D-printable spring-based structures to support a wide range of expressive behaviors, including stretch and compress, bend, twist, and all possible combinations. The poster concludes with a brief feasibility test and enumerates future work.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131836"}, {"primary_key": "3871439", "vector": [], "sparse_vector": [], "title": "PhyShare: Sharing Physical Interaction in Virtual Reality.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON>"], "summary": "We present PhyShare, a new haptic user interface based on actuated robots. Virtual reality has recently been gaining wide adoption, and an effective haptic feedback in these scenarios can strongly support user's sensory in bridging virtual and physical world. Since participants do not directly observe these robotic proxies, we investigate the multiple mappings between physical robots and virtual proxies that can utilize the resources needed to provide a well rounded VR experience. PhyShare bots can act either as directly touchable objects or invisible carriers of physical objects, depending on different scenarios. They also support distributed collaboration, allowing remotely located VR collaborators to share the same physical feedback.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131795"}, {"primary_key": "3871440", "vector": [], "sparse_vector": [], "title": "Creating Haptic Illusion of Compliance for Tangential Force Input using Vibrotactile Actuator.", "authors": ["Seongkook Heo", "<PERSON><PERSON><PERSON>"], "summary": "We demonstrate a haptic feedback method that generates a compliance illusion on a rigid surface with a tangential force sensor and a vibrotactile actuator. The method assumes a conceptual model where a virtual object is placed on a textured surface and stringed to four walls with four springs. A two dimensional tangential force vector measured from the rigid surface is mapped to the virtual position of the virtual object on the textured surface. By playing vibration patterns that simulate the friction-induced vibrations made from the movement of the virtual object, we could make an illusion that the rigid surface feels like moving. We also demonstrate that the perceptual properties of the illusion, such as the stiffness of the virtual spring and the maximum travel distance of the virtual object, can be programmatically controlled.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131804"}, {"primary_key": "3871441", "vector": [], "sparse_vector": [], "title": "Mobile Brain-Computer Interface for Dance and Somatic Practice.", "authors": ["<PERSON><PERSON>"], "summary": "Sensor technologies have been adapted to performing arts, and owing to the recent advancement of low-cost mobile electroencephalography devices, brain-computer interface (BCI) is integrated to dance performances as well. Nevertheless, BCI is less accessible to artists compared to other sensors because signal processing and machine learning are required. This paper proposes a work-in-progress example of BCI applications for performances that has been designed in collaboration with contemporary dancers. Its contribution is that the piece is not an add-on to a performance, but the implementation reflects practices of contemporary dance.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131803"}, {"primary_key": "3871442", "vector": [], "sparse_vector": [], "title": "Hand Development Kit: Soft Robotic Fingers as Prosthetic Augmentation of the Hand.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent developments in wearable robots and human augmentation open up new possibilities of designing computational interfaces integrated to the body. Particularly, supernumerary robot is a recently established field of research that investigates a radical idea of adding robotic limbs to users. Such augmentations, however, pose a limit in how much we can add to the body due to weight or interference with other body parts. To address that, we explore the use of soft robots as supernumerary robotic fingers. We present a pair of soft robotic fingers driven by cables and servomotors, and applications using the robotic fingers in various contexts.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131805"}, {"primary_key": "3871443", "vector": [], "sparse_vector": [], "title": "Evorus: A Crowd-powered Conversational Assistant That Automates Itself Over Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Crowd-powered conversational assistants have found to be more robust than automated systems, but do so at the cost of higher response latency and monetary costs. One promising direction is to combined the two approaches for high quality and low cost solutions. However, traditional offline approaches of building automated systems with the crowd requires first collecting training data from the crowd, and then training a model before an online system can be launched. In this paper, we introduce <PERSON><PERSON><PERSON>, a crowd-powered conversational assistant with online-learning capability that automate itself over time. Evorus expands a previous crowd-powered conversation system by reducing its reliance on the crowd over time while maintaining the robustness and reliability of human intelligence, by (i) allowing new chatbots to be added to help contribute possible answers, (ii) learning to reuse past responses to similar queries over time, and (iii) learning to reduce the amount of crowd oversight necessary to retain quality. Our deployment study with 28 users show that automated responses were chosen 12.84% of the time, and voting cost was reduced by 6%. Evorus introduced a new framework for constructing crowd-powered conversation systems that can gradually automate themselves using machine learning, a concept that we believe can be generalize to other types of crowd-powered systems for future research.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131823"}, {"primary_key": "3871444", "vector": [], "sparse_vector": [], "title": "RetroShape: Leveraging Rear-Surface Shape Displays for 2.5D Interaction on Smartwatches.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The small screen size of a smartwatch limits user experience when watching or interacting with media. We propose a supplementary tactile feedback system to enhance the user experience with a method unique to the smartwatch form factor. Our system has a deformable surface on the back of the watch face, allowing the visual scene on screen to extend into 2.5D physical space. This allows the user to watch and feel virtual objects, such as experiencing a ball bouncing against the wrist. We devised two controlled experiments to analyze the influence of tactile display resolution on the illusion of virtual object presence. Our first study revealed that on average, a taxel can render virtual objects between 70% and 138% of its own size without shattering the illusion. From the second study, we found visual and haptic feedback can be separated by 4.5mm to 16.2mm for the tested taxels. Based on the results, we developed a prototype (called RetroShape) with 4×4 10mm taxels using micro servo motors, and demonstrated its unique capability through a set of tactile-enhanced games and videos. A preliminary user evaluation showed that participants welcome RetroShape as a useful addition to existing smartwatch output.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126610"}, {"primary_key": "3871445", "vector": [], "sparse_vector": [], "title": "Omnicode: A Novice-Oriented Live Programming Environment with Always-On Run-Time Value Visualizations.", "authors": ["Hyeonsu B. Kang", "<PERSON>"], "summary": "Visualizations of run-time program state help novices form proper mental models and debug their code. We push this technique to the extreme by posing the following question: What if a live programming environment for an imperative language always displays the entire history of all run-time values for all program variables all the time? To explore this question, we built a prototype live IDE called Omnicode (\"Omniscient Code\") that continually runs the user's Python code and uses a scatterplot matrix to visualize the entire history of all of its numerical values, along with meaningful numbers derived from other data types. To filter the visualizations and hone in on specific points of interest, the user can brush and link over the scatterplots or select portions of code. They can also zoom in to view detailed stack and heap visualizations at each execution step. An exploratory study on 10 novice programmers discovered that they found Omnicode to be useful for debugging, forming mental models, explaining their code to others, and discovering moments of serendipity that would not have been likely within an ordinary IDE.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126632"}, {"primary_key": "3871446", "vector": [], "sparse_vector": [], "title": "Tactile Element with Double-sided Inkjet Printing to Generate Electrostatic Forces and Electrostimuli.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a tactile element that can generate both an electrostatic force and an electrostimulus, and can be used to provide tactile feedback on a wide area of human skin such as the palm of the hand. Touching the flat surface through the our proposed tactile element allow the user to feel both uneven and rough textures. In addition, the element can be fabricated using double-sided inkjet printing with conductive ink. Use of a flexible substrate, such as a PET film or paper, allows the user to design a free-formed tactile element. In this demonstration, we describe the implementation of the proposed stimuli element and show examples of applications.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131789"}, {"primary_key": "3871447", "vector": [], "sparse_vector": [], "title": "DreamSketch: Early Stage 3D Design Explorations with Sketching and Generative Design.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present DreamSketch, a novel 3D design interface that combines the free-form and expressive qualities of sketching with the computational power of generative design algorithms. In DreamSketch, a user coarsely defines the problem by sketching the design context. Then, a generative design algorithm produces multiple solutions that are augmented as 3D objects in the sketched context. The user can interact with the scene to navigate through the generated solutions. The combination of sketching and generative algorithms enables designers to explore multiple ideas and make better informed design decisions during the early stages of design. Design study sessions with designers and mechanical engineers demonstrate the expressive nature and creative possibilities of DreamSketch.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126662"}, {"primary_key": "3871448", "vector": [], "sparse_vector": [], "title": "EyeScout: Active Eye Tracking for Position and Movement Independent Gaze Interaction with Large Public Displays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While gaze holds a lot of promise for hands-free interaction with public displays, remote eye trackers with their confined tracking box restrict users to a single stationary position in front of the display. We present EyeScout, an active eye tracking system that combines an eye tracker mounted on a rail system with a computational method to automatically detect and align the tracker with the user's lateral movement. EyeScout addresses key limitations of current gaze-enabled large public displays by offering two novel gaze-interaction modes for a single user: In \"Walk then Interact\" the user can walk up to an arbitrary position in front of the display and interact, while in \"Walk and Interact\" the user can interact even while on the move. We report on a user study that shows that EyeScout is well perceived by users, extends a public display's sweet spot into a sweet line, and reduces gaze interaction kick-off time to 3.5 seconds -- a 62% improvement over state of the art solutions. We discuss sample applications that demonstrate how EyeScout can enable position and movement-independent gaze interaction with large public displays.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126630"}, {"primary_key": "3871449", "vector": [], "sparse_vector": [], "title": "Shall We Fabricate?: Collaborative, Bidirectional, Incremental Fabrication.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The recent emergence of digital fabrication allows everyday designers to make, deploy, and enjoy their creation. However, the excitement over digital fabrication presumes that users have sufficient domain knowledge to create complex models by understanding the underlying principles, can be self-creative without computational supports. This paper presents a new fabrication framework that lowers the boundary of solving everyday issues with fabrication. A formalism and accompanying finite state machine (FSM) model that help assign a fabrication machine intelligence to appreciate humans' design actions was proposed, with a view towards a new fabrication framework empowering collaborative, incremental fabrication. Empowered by the novel framework, this paper envisions a future of fabrication that pushes the ceiling, a collaborative fabrication, processing intermittent, unpredictable events as live input and reflect them in the emerging outcomes by co-design process between a designer and a machine.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131844"}, {"primary_key": "3871450", "vector": [], "sparse_vector": [], "title": "Information Identification Support Method for Areas with Densely Located Signboards.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We developed methods and implemented a system prototype to help people find specific signboards in areas with densely located signboards. In addition, we examined whether the proposed methods would reduce the search time of a specific signboard. The result showed that the proposed method was superior in cases where there were multiple signboards to be searched and background saturation was low.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131815"}, {"primary_key": "3871451", "vector": [], "sparse_vector": [], "title": "Printing System Reflecting User&apos;s Intent in Real Time Using a Handheld Printer.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a new type of printing system that incorporates sensors in a handheld printer to reflect in real time user intent in the results of printing on paper. This system achieves two key functions: \"real-time embellishment\" for altering printed content by reading user hand movements by pressure and optical sensors, and \"local transcription\" for selecting content to be output by tracing existing content on paper with a linear camera. We performed experiments to measure the accuracy of both techniques and evaluate their usefulness.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131790"}, {"primary_key": "3871452", "vector": [], "sparse_vector": [], "title": "Towards a Universal Knowledge Accelerator.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The human mind remains an unparalleled engine of innovation, with its unique ability to make sense of complex information and find deep analogical connections driving progress in science and technology over the past millennia. The recent explosion of online information available in virtually every domain should present an opportunity for accelerating this engine; instead, it threatens to slow it as the information processing limits of individual minds are reached. In this talk I discuss our efforts towards building a universal knowledge accelerator: a system in which the sensemaking people engage in online is captured and made useful for others, leading to virtuous cycles of constantly improving information sources that in turn help people more effectively synthesize and innovate. Approximately 70 billion hours per year in the U.S. alone are spent on complex online sensemaking in domains ranging from scientific literature to health; capturing even a fraction of this could provide significant benefits. We discuss three integrated levels of research that are needed to realize this vision: at the individual level in understanding and capturing higher order cognition; at the computational level in developing new interaction systems and AI partners for human cognition; and at the social level in developing complex and creative crowdsourcing and social computing systems.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126668"}, {"primary_key": "3871453", "vector": [], "sparse_vector": [], "title": "Filum: A Sewing Technique to Alter Textile Shapes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a novel shape-changing technique called Filum, which makes it possible to alter the shapes of textiles to better suit the requirements of people and the environment. Using strings and various sewing methods, ordinary textiles can be automatically shortened or shrunk into curved shapes. We demonstrate a series of novel interactions between people and textiles via three applications.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131797"}, {"primary_key": "3871454", "vector": [], "sparse_vector": [], "title": "Demonstrating TrussFab&apos;s Editor: Designing Sturdy Large-Scale Structures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>siang<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We demonstrate TrussFab's editor for creating large-scale structures that are sturdy enough to carry human weight. TrussFab achieves the large scale by using plastic bottles as beams that form structurally sound node-link structures, also known as trusses, allowing it to handle the forces resulting from scale and load. During this hands-on demo at UIST, attendees will use the TrussFab software to design their own structures, validate their design using integrated structural analysis, and export their designs for 3D printing.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131802"}, {"primary_key": "3871455", "vector": [], "sparse_vector": [], "title": "Fading into the Background: Unleashing Ubiquitous and Unobtrusive Context Sensing.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "As computing becomes increasingly embedded into the fabric of everyday life, systems that understand people's context of use are of paramount importance. Regardless of whether the platform is a mobile device, wearable, or smart infrastructure, context offers an implicit dimension that is vital to increasing the richness of human-computer interaction. In my thesis work, I introduce multiple enabling technologies that greatly enhance context awareness in highly dynamic platforms, all without costly or invasive instrumentation. My systems have been deployed across long periods and multiple environments, the results of which show the versatility, accuracy and potential for robust context sensing. By combining novel sensing with machine learning, my work transforms raw signals into intelligent abstractions that can power rich, context-sensitive applications, unleashing the potential of next-generation computing platforms.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131839"}, {"primary_key": "3871456", "vector": [], "sparse_vector": [], "title": "Wind Tactor: An Airflow-based Wearable Tactile Display.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Traditional wearable tactile displays transfer information through a firm contact between the tactile stimulator (tactor) and the skin. The firm contact, however, might limit the location of wearable tactile displays and might be the source of discomfort when the skin is being exposed to prolonged contact. This motivated us to find a non-contact wearable tactile display, which is able to transfer information without a contact. Based on the literature review, we concluded that we should focus on airflow-based tactile displays among various non-contact stimulation methods. In my previous work, I proposed the concept of a non-contact wearable tactile display using airflows and explored its feasibility. Focusing on an airflow-based wearable tactile display, I am investigating the expressivity and the feasibility of wearable airflow displays in real-world environments. I expect my dissertation will provide empirical grounds and guidelines for the design of an airflow-based wearable tactile display.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131838"}, {"primary_key": "3871457", "vector": [], "sparse_vector": [], "title": "Hybrid Use of Asynchronous and Synchronous Interaction for Collaborative Creation.", "authors": ["<PERSON>"], "summary": "My dissertation is aimed at enabling people to collaborate to create complex artifacts: for example, to develop software, sketch GUI prototypes, play music together, or write a novel. Such creative processes are not well defined and can evolve dynamically. We introduce interactive systems that help users collaborate and communicate in the open-ended process. In particular, we investigate the benefits of both integrating asynchronous interactions into real-time collaborations and of having real time components in asynchronous collaborative settings. The systems provide tools that combine the two different types of interaction techniques, and we validate them via user study, participatory performing arts, and the online deployments of systems and crowdsourced tasks. The hybrid methods are designed to help users recover collaborative context, make the process approachable to nonexperts, collaborate online crowds on demand in real-time, and sustain liveness during collaboration. The dissertation will result in cross-domain knowledge in designing collaborative systems and it will help us create a framework for future intelligent systems that will help people solve more complex tasks effectively.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131841"}, {"primary_key": "3871458", "vector": [], "sparse_vector": [], "title": "Projective Windows: Arranging Windows in Space Using Projective Geometry.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In augmented and virtual reality, there may be many 3D planar windows with 2D texts, images, and videos on them. Projective Windows is a technique using projective geometry to bring any near or distant window instantly to the fingertip and then to scale and position it simultaneously with a single, continuous flow of hand motion.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131816"}, {"primary_key": "3871459", "vector": [], "sparse_vector": [], "title": "Automatically Visualizing Audio Travel Podcasts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Audio Podcasts have gained popularity because they are a compelling form of storytelling and are easy to consume. However, they are not as easy to produce since resources are invested in the research, recording, and editing process and the average length of an episode is over an hour. Some audio podcasts could benefit from visuals to increase engagement and learning, but manually curating them can be arduous and time-consuming. We introduce a tool for automatically visualizing audio podcasts, currently focused on the genre of travelogues. Our system works by first time-aligning the transcript of a given podcast, using NLP techniques to extract entities and track how interesting or relevant they are throughout the podcast, and then retrieving visual data appropriately to describe them, either through transitions on a map or professionally taken photographs with captions. By automatically creating a visual narrative to accompany a podcast, we hope our tool can provide listeners with a better sense of the podcast's topic.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131818"}, {"primary_key": "3871460", "vector": [], "sparse_vector": [], "title": "EYE DEAR: Smartphone Text Resizing Interaction for the Eye Health of the Presbyopia Population.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "In Seok Hong", "<PERSON>", "<PERSON>"], "summary": "Presbyopia is a symptom in which the elasticity of the lens is weakened and the image is not formed. However, as the use of smart phones increases, the age at which presbyopia symptoms appear is gradually decreasing. The closer the distance is from the smartphone, the less the focus of the eyes will be which making the letters and pictures on the smartphone screen appear blurred. In this study, we conducted a study on the interactions that helped to improve health of eye for people with presbyopia or those who have a habit that can facilitating presbyopia. As the distance of the smartphone from the eye is increased, the font size is increased to upgrading readability and the prototype is tested by 20 experimenters.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131830"}, {"primary_key": "3871461", "vector": [], "sparse_vector": [], "title": "Reflector: Distance-Independent, Private Pointing on a Reflective Screen.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Reflector is a novel direct pointing method that utilizes hidden design space on reflective screens. By aligning a part of the user's onscreen reflection with objects rendered on the screen, Reflector enables (1) distance-independent and (2) private pointing on commodity screens. Reflector can be implemented easily in both desktop and mobile conditions through a single camera installed at the edge of the screen. Reflector's pointing performance was compared to today's major direct input devices: eye trackers and touchscreens. We demonstrate that Reflector allows the user to point more reliably, regardless of distance from the screen, compared to an eye tracker. Further, due to the private nature of an onscreen reflection, Reflector shows a shoulder surfing success rate 20 times lower than that of touchscreens for the task of entering a 4-digit PIN.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126665"}, {"primary_key": "3871462", "vector": [], "sparse_vector": [], "title": "SketchExpress: Remixing Animations for More Effective Crowd-Powered Prototyping of Interactive Interfaces.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Stephanie D. O&apos;Ke<PERSON>e", "<PERSON>"], "summary": "Low-fidelity prototyping at the early stages of user interface (UI) design can help designers and system builders quickly explore their ideas. However, interactive behaviors in such prototypes are often replaced by textual descriptions because it usually takes even professionals hours or days to create animated interactive elements due to the complexity of creating them. In this paper, we introduce SketchExpress, a crowd-powered prototyping tool that enables crowd workers to create reusable interactive behaviors easily and accurately. With the system, a requester-designers or end-users-describes aloud how an interface should behave and crowd workers make the sketched prototype interactive within minutes using a demonstrate-remix-replay approach. These behaviors are manually demonstrated, refined using remix functions, and then can be replayed later. The recorded behaviors persist for future reuse to help users communicate with the animated prototype. We conducted a study with crowd workers recruited from Mechanical Turk, which demonstrated that workers could create animations using SketchExpress in 2.9 minutes on average with 27% gain in the quality of animations compared to the baseline condition of manual demonstration.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126595"}, {"primary_key": "3871463", "vector": [], "sparse_vector": [], "title": "StrutModeling: A Low-Fidelity Construction Kit to Iteratively Model, Test, and Adapt 3D Objects.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present StrutModeling, a computationally enhanced construction kit that enables users without a 3D modeling background to prototype 3D models by assembling struts and hub primitives in physical space. Physical 3D models are immediately captured in software and result in readily available models for 3D printing. Given the concrete physical format of StrutModels, modeled objects can be tested and fine tuned in the presence of existing objects and specific needs of users. StrutModeling avoids puzzling with pieces by contributing an adjustable strut and universal hub design. Struts can be adjusted in length and snap to magnetic hubs in any configuration. As such, arbitrarily complex models can be modeled, tested, and adjusted during the design phase. In addition, the embedded sensing capabilities allow struts to be used as measuring devices for lengths and angles, and tune physical mesh models according to existing physical objects.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126643"}, {"primary_key": "3871464", "vector": [], "sparse_vector": [], "title": "Interacting with Acoustic Simulation and Fabrication.", "authors": ["Dingzeyu Li"], "summary": "Incorporating accurate physics-based simulation into interactive design tools is challenging. However, adding the physics accurately becomes crucial to several emerging technologies. For example, in virtual/augmented reality (VR/AR) videos, the faithful reproduction of surrounding audios is required to bring the immersion to the next level. Similarly, as personal fabrication is made possible with accessible 3D printers, more intuitive tools that respect the physical constraints can help artists to prototype designs. One main hurdle is the sheer amount of computation complexity to accurately reproduce the real-world phenomena through physics-based simulation. In my thesis research, I develop interactive tools that implement efficient physics-based simulation algorithms for automatic optimization and intuitive user interaction.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131842"}, {"primary_key": "3871465", "vector": [], "sparse_vector": [], "title": "Enabling Sensing and Interaction with Everyday Objects.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The Internet of Things (IoT) promises to revolutionize the way people interact with their environment and the objects within it by creating a ubiquitous network of physical devices. However, current advancement in IoT has been heavily targeted towards creating battery-powered electronics. There remains a huge gap between the collection of smart devices integrated into the IoT and the massive number of everyday physical objects. The goal of my research is to bridge this gap in the current IoT framework to enable novel sensing and interactive applications with daily objects.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131843"}, {"primary_key": "3871466", "vector": [], "sparse_vector": [], "title": "SweepCanvas: Sketch-based 3D Prototyping on an RGB-D Image.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hongbo Fu"], "summary": "The creation of 3D contents still remains one of the most crucial problems for the emerging applications such as 3D printing and Augmented Reality. In Augmented Reality, how to create virtual contents that seamlessly overlay with the real environment is a key problem for human-computer interaction and many subsequent applications. In this paper, we present a sketch-based interactive tool, which we term emph{SweepCanvas}, for rapid exploratory 3D modeling on top of an RGB-D image. Our aim is to offer end-users a simple yet efficient way to quickly create 3D models on an image. We develop a novel sketch-based modeling interface, which takes a pair of user strokes as input and instantly generates a curved 3D surface by sweeping one stroke along the other. A key enabler of our system is an optimization procedure that extracts pairs of spatial planes from the context to position and sweep the strokes. We demonstrate the effectiveness and power of our modeling system on various RGB-D data sets and validate the use cases via a pilot study.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126611"}, {"primary_key": "3871467", "vector": [], "sparse_vector": [], "title": "AirCode: Unobtrusive Physical Tags for Digital Fabrication.", "authors": ["Dingzeyu Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "We present AirCode, a technique that allows the user to tag physically fabricated objects with given information. An AirCode tag consists of a group of carefully designed air pockets placed beneath the object surface. These air pockets are easily produced during the fabrication process of the object, without any additional material or postprocessing. Meanwhile, the air pockets affect only the scattering light transport under the surface, and thus are hard to notice to our naked eyes. But, by using a computational imaging method, the tags become detectable. We present a tool that automates the design of air pockets for the user to encode information. AirCode system also allows the user to retrieve the information from captured images via a robust decoding algorithm. We demonstrate our tagging technique with applications for metadata embedding, robotic grasping, as well as conveying object affordances.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126635"}, {"primary_key": "3871468", "vector": [], "sparse_vector": [], "title": "Dwell+: Multi-Level Mode Selection Using Vibrotactile Cues.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Dwell+, a method that boosts the effectiveness of typical dwell selection by augmenting the passive dwell duration with active haptic ticks which promptly drives rapid switches of modes forward through the user's skin sensations. In this way, Dwell+ enables multi-level dwell selection using rapid haptic ticks. To select a mode from a button, users dwell-touch the button until the mode of selection is haptically prompted. Our haptic stimulation design consists of a short 10ms vibrotacile feedback that indicates a mode arriving and a break that separates consecutive modes. We first tested the effectiveness of 170ms, 150ms, 130ms, and 110ms intervals between modes for a 10-level selection. The results reveal that 3-beats-per-chunk rhythm design, e.g., displaying longer 25ms vibrations initially for all three modes, could potentially achieve higher accuracy. The second study reveals significant improvement wherein a 94.5% accuracy was achieved for a 10-level Dwell+ selection using the 170ms interval with 3-beats-per-chunk design, and a 93.82% rate of accuracy using the more frequent 150ms interval with similar chunks for 5-level selection. The performance of conducting touch and receiving vibration from disparate hands was investigated for our final study to provide a wider range of usage. Our applications demonstrated implementing Dwell+ across interfaces, such as text input on a smartwatch, enhancing touch space for HMDs, boosting modalities of stylus-based tool selection, and extending the input vocabulary of physical interfaces.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126627"}, {"primary_key": "3871469", "vector": [], "sparse_vector": [], "title": "Outside-In: Visualizing Out-of-Sight Regions-of-Interest in a 360° Video Using Spatial Picture-in-Picture Previews.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shan-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "360-degree video contains a full field of environmental content. However, browsing these videos, either on screens or through head-mounted displays (HMDs), users consume only a subset of the full field of view per a natural viewing experience. This causes a search problem when a region-of-interest (ROI) in a video is outside of the current field of view (FOV) on the screen, or users may search for non-existing ROIs. We propose Outside-In, a visualization technique which re-introduces off-screen regions-of-interest (ROIs) into the main screen as spatial picture-in-picture (PIP) previews. The geometry of the preview windows further encodes a ROI's relative location vis-à-vis the main screen view, allowing for effective navigation. In an 18-participant study, we compare Outside-In with traditional arrow-based guidance within three types of 360-degree video. Results show that Outside-In outperforms in regard to understanding spatial relationship, the storyline of the content and overall preference. Two applications are demonstrated for use with Outside-In in 360-degree video navigation with touchscreens, and live telepresence.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126656"}, {"primary_key": "3871470", "vector": [], "sparse_vector": [], "title": "Optically Dynamic Interfaces.", "authors": ["<PERSON>"], "summary": "In the virtual world, changing properties of objects such as their color, size or shape is one of the main means of communication. Objects are hidden or revealed when needed, or undergo changes in color or size to communicate importance. I am interested in how these features can be brought into the real world by modifying the optical properties of physical objects and devices, and how this dynamic appearance influences interaction and behavior. The interplay of creating functional prototypes of interactive artifacts and devices, and studying them in controlled experiments forms the basis of my research. During my research I created a three level model describing how physical artifacts and interfaces can be appropriated to allow for dynamic appearance: (1) dynamic objects, (2) augmented objects, and (3) augmented surroundings. This position paper outlines these three levels and details instantiations of each level that were created in the context of this thesis research.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131840"}, {"primary_key": "3871471", "vector": [], "sparse_vector": [], "title": "Demonstrating Interactive Systems based on Electrical Muscle Stimulation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We provide a hands-on demonstration of the potential of interactive systems based on electrical muscle stimulation (EMS). These wearable devices allow attendees, for example, to physically learn how to manipulate objects they never seen before, feel walls and forces in virtual reality, and so forth. In our demo we plan to not only demonstrate several of these EMS-based prototypes but also to provide instructions and free hardware for people to conduct their first projects using EMS.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131806"}, {"primary_key": "3871472", "vector": [], "sparse_vector": [], "title": "Pepper&apos;s Cone: An Inexpensive Do-It-Yourself 3D Display.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper describes a simple 3D display that can be built from a tablet computer and a plastic sheet folded into a cone. This display allows naturally viewing a three-dimensional object from any direction over a 360-degree path of travel without the use of a head mount or special glasses. Inspired by the classic Pepper's Ghost illusion, our approach uses a curved transparent surface to reflect the image displayed on a 2D display. By properly pre-distorting the displayed image our system can produce a perspective-correct image to the viewer that appears to be suspended inside the reflector. We use the gyroscope integrated into modern tablet computers to adjust the rendered image based on the relative orientation of the viewer. The end result is a natural and intuitive interface for inspecting a 3D object. Our choice of a cone reflector is obtained by analyzing optical performance and stereo-compatibility over rotationally-symmetric conic reflector shapes. We also present the prototypes we built and measure the performance of our display through side-by-side comparisons with reference images.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126602"}, {"primary_key": "3871473", "vector": [], "sparse_vector": [], "title": "Multiplanes: Assisted Freehand VR Drawing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Jingwan Lu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Multiplanes is a virtual reality (VR) drawing system that provides users with the flexibility of freehand drawing and the ability to draw perfect shapes. Through the combination of both beautified and 2D drawing, Multiplanes addresses challenges in creating 3D VR drawings. To achieve this, the system beautifies user's strokes based on the most probable, intended shapes while the user is drawing them. It also automatically generates snapping planes and beautification trigger points based on previous and current strokes and the current controller pose. Based on geometrical relationships to previous strokes, beautification trigger points act as guides inside the virtual environment. Users can hit these points to (explicitly) trigger a stroke beautification. In contrast to other systems, when using Multiplanes, users do not need to manually set or do any kind of special gesture to activate, such guides allowing the user to focus on the creative process.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131794"}, {"primary_key": "3871474", "vector": [], "sparse_vector": [], "title": "A Capacitive Touch Sensing Technique with Series-connected Sensing Electrodes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Touch sensing with multiple electrodes allows expressive touch interactions. The adaptability and flexibility of the sensor are important in efficiently prototyping touch based systems. The proposed technique uses capacitive touch sensing and simplifies the connections as the electrodes are connected in series via capacitors and the interface circuit is connected to the electrode array by just two wires. The touched electrode is recognized by measuring the capacitance changes while switching the polarity of the signal. We show that the technique is capable of detecting different touches through simulations and actual measurements. User tests show that ten electrodes are successfully recognized after user calibration. They also show the proposal's other novel capabilities of multi-touch (2-touch) and `capacitor-free' design. Various forms of electrodes and applications are examined to elucidate the application range.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126625"}, {"primary_key": "3871475", "vector": [], "sparse_vector": [], "title": "WhichFingers: Identifying Fingers on Touch Surfaces and Keyboards using Vibration Sensors.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "HCI researchers lack low latency and robust systems to support the design and development of interaction techniques using finger identification. We developed a low cost prototype using piezo based vibration sensors attached to each finger. By combining the events from an input device with the information from the vibration sensors we demonstrate how to achieve low latency and robust finger identification. Our prototype was evaluated in a controlled experiment, using two keyboards and a touchpad, showing recognition rates of 98.2% for the keyboard and, for the touchpad, 99.7% for single touch and 94.7% for two simultaneous touches. These results were confirmed in an additional laboratory style experiment with ecologically valid tasks. Last we present new interactions techniques made possible using this technology.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126619"}, {"primary_key": "3871476", "vector": [], "sparse_vector": [], "title": "Attaching Objects to Smartphones Back Side for a Modular Interface.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a new approach to enhancing interaction with general applications on smartphones. Physical objects held on back surface of the smartphone, which can be captured by the rear camera with a mirror, work as input devices or controllers. It does not require any additional electronic devices but offers tactile feedback. The occlusion problem does not occur when using smartphone's back side, in terms of both display and camera viewing. We implemented on an Android smartphone and confirmed that it provides richer interaction and low latency (100 ms).", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131810"}, {"primary_key": "3871477", "vector": [], "sparse_vector": [], "title": "HaptI/O: Physical Node for the Internet of Haptics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose the concept of the \"Internet of Haptics\" (IoH) that enables sharing experiences of others with the sense of touch. IoH allows to multicast haptic sensation from one Sensor-Node (Inter-Node) to multiple Actuator-Node (Ceive-Node) and to multicast from multiple Inter-Node to multiple Ceive-Node via the Internet. As a proof of concept, we developed the \"HaptI/O\" device which is a physical network node that can perform as a gateway to input or output the haptic information to/from the human body or tangible objects. We use the WebRTC as the baseline protocol for communication. Users can gain access IoH Web using a smartphone or PC and experience the haptic sensation by selecting the Inter-Node and Ceive-Node from a web browser. Multiple HaptI/O would be connected on the IoH server and transmit the haptic information from one node to multiple nodes as well as one to one mutual connection so that HaptI/O enables us to share our experiences with the sense of touch.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131788"}, {"primary_key": "3871478", "vector": [], "sparse_vector": [], "title": "Bifröst: Visualizing and Checking Behavior of Embedded Systems across Hardware and Software.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The Maker movement has encouraged more people to start working with electronics and embedded processors. A key challenge in developing and debugging custom embedded systems is understanding their behavior, particularly at the boundary between hardware and software. Existing tools such as step debuggers and logic analyzers only focus on software or hardware, respectively. This paper presents a new development environment designed to illuminate the boundary between embedded code and circuits. <PERSON><PERSON><PERSON><PERSON><PERSON> automatically instruments and captures the progress of the user's code, variable values, and the electrical and bus activity occurring at the interface between the processor and the circuit it operates in. This data is displayed in a linked visualization that allows navigation through time and program execution, enabling comparisons between variables in code and signals in circuits. Automatic checks can detect low-level hardware configuration and protocol issues, while user-authored checks can test particular application semantics. In an exploratory study with ten participants, we investigated how <PERSON><PERSON><PERSON><PERSON><PERSON> influences debugging workflows.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126658"}, {"primary_key": "3871479", "vector": [], "sparse_vector": [], "title": "SensIR: Detecting Hand Gestures with a Wearable Bracelet using Infrared Transmission and Reflection.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Gestures have become an important tool for natural interaction with computers and thus several wearables have been developed to detect hand gestures. However, many existing solutions are unsuitable for practical use due to low accuracy, high cost or poor ergonomics. We present SensIR, a bracelet that uses near-infrared sensing to infer hand gestures. The bracelet is composed of pairs of infrared emitters and receivers that are used to measure both the transmission and reflection of light through/off the wrist. SensIR improves the accuracy of existing infrared gesture sensing systems through the key idea of taking measurements with all possible combinations of emitters and receivers. Our study shows that SensIR is capable of detecting 12 discrete gestures with 93.3% accuracy. SensIR has several advantages compared to other systems such as high accuracy, low cost, robustness against bad skin coupling and thin form-factor.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126604"}, {"primary_key": "3871480", "vector": [], "sparse_vector": [], "title": "Qoom: An Interactive Omnidirectional Ball Display.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhengqing Li", "<PERSON><PERSON><PERSON>"], "summary": "We present a sphere-shaped interactive display system, named Qoom, as a new input and output device. Unlike existing sphere-shaped displays, Qoom is a perfectly spherical ball that can be rotated, thrown, or even kicked. First, we discuss how spherical displays can be used in daily life and describe how users interact with spheres. Then, we show how we developed the Qoom prototype that uses touch and rotation detection, real-time object tracking, and spherical projection mapping. We implemented actions including touching, rotating, bouncing and throwing as controls. We also developed applications for Qoom that utilize the unique advantages of ball displays.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126607"}, {"primary_key": "3871481", "vector": [], "sparse_vector": [], "title": "&quot;Smellization&quot; of Warnings against Overuse Power used to Promote Energy Saving Behavior.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "In Japan, the necessity of saving energy is rising due to the nuclear accident caused by the Great East Japan Earthquake that occurred on March 11, 2011. Reduction of energy usage is required due to rapid increases in electricity consumption due to the scorching summer heat in recent years. The common ways to provide information on energy consumption mainly occur through \"visualization\" of information. On the contrary, olfactory stimulation can be performed while working, and it is effective also when the degree of arousal is low. This study considers applications on the basis of the concept of \"smellization\" of information using olfactive stimulation. In this paper, we introduce the configuration and operation examples of a system developed for evoking public energy conservation behavior using smell.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131817"}, {"primary_key": "3871482", "vector": [], "sparse_vector": [], "title": "Reinventing the Wheel: Transforming Steering Wheel Systems for Autonomous Vehicles.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we introduce two different transforming steering wheel systems that can be utilized to augment user experience for future partially autonomous and fully autonomous vehicles. The first one is a robotic steering wheel that can mechanically transform by using its actuators to move the various components into different positions. The second system is a LED steering wheel that can visually transform by using LEDs embedded along the rim of wheel to change colors. Both steering wheel systems contain onboard microcontrollers developed to interface with our driving simulator. The main function of these two systems is to provide emergency warnings to drivers in a variety of safety critical scenarios, although the design space that we propose for these steering wheel systems also includes the use as interactive user interfaces. To evaluate the effectiveness of the emergency alerts, we conducted a driving simulator study examining the performance of participants (N=56) after an abrupt loss of autonomous vehicle control. Drivers who experienced the robotic steering wheel performed significantly better than those who experienced the LED steering wheel. The results of this study suggest that alerts utilizing mechanical movement are more effective than purely visual warnings.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126655"}, {"primary_key": "3871483", "vector": [], "sparse_vector": [], "title": "Sonoliards: Rendering Audible Sound Spots by Reflecting the Ultrasound Beams.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes a dynamic acoustic field generation system for a spot audio towards particular person indoors. Spot audio techniques have been explored by generating the ultrasound beams toward the target person in certain area however everyone in this area can hear the sound. Our system recognizes the positions of each person indoor using motion capture and 3D model data of the room. After that we control direction of parametric speaker in real-time so that sound reach only particular person by calculating the reflection of sound on surfaces such as wall and ceiling. We calculate direction of parametric speaker using a beam tracing method. We present generating methods of dynamic acoustic field in our system and conducted the experiments on human factor to evaluate performance of proposed system.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131807"}, {"primary_key": "3871484", "vector": [], "sparse_vector": [], "title": "Walk-In Music: Walking Experience with Synchronized Music and Its Effect of Pseudo-gravity.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "\"Walk-In Music\" is a system that provides a new walking experience through synchronized music and pseudo-gravity. This system synchronizes each step with the music being listened to and creates a feeling of generating music through walking. It creates a Walk-In state where music and walking are consistent at all times. In this state, when changing the speed of music, the pedestrian may feel pseudo-gravity based on pseudo-haptics. Our results indicate that by changing the speed of music during the Walk-In state, the walking speed became faster and slower. We call this a Walk-Shift. This demonstrated the possibility of controlling personal walking by music. Walk-In Music has created a pleasant experience by music, and proposed a new relationship between people and music that leads to behavior changes.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131822"}, {"primary_key": "3871485", "vector": [], "sparse_vector": [], "title": "NaviFields: Relevance fields for adaptive VR navigation.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Virtual Reality allow users to explore virtual environments naturally, by moving their head and body. However, the size of the environments they can explore is limited by real world constraints, such as the tracking technology or the physical space available. Existing techniques removing these limitations often break the metaphor of natural navigation in VR (e.g. steering techniques), involve control commands (e.g., teleporting) or hinder precise navigation (e.g., scaling user's displacements). This paper proposes NaviFields, which quantify the requirements for precise navigation of each point of the environment, allowing natural navigation within relevant areas, while scaling users' displacements when travelling across non-relevant spaces. This expands the size of the navigable space, retains the natural navigation metaphor and still allows for areas with precise control of the virtual head. We present a formal description of our NaviFields technique, which we compared against two alternative solutions (i.e., homogeneous scaling and natural navigation). Our results demonstrate our ability to cover larger spaces, introduce minimal disruption when travelling across bigger distances and improve very significantly the precise control of the viewpoint inside relevant areas.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126645"}, {"primary_key": "3871486", "vector": [], "sparse_vector": [], "title": "Erg-O: Ergonomic Optimization of Immersive Virtual Environments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Interaction in VR involves large body movements, easily inducing fatigue and discomfort. We propose Erg-O, a manipulation technique that leverages visual dominance to maintain the visual location of the elements in VR, while making them accessible from more comfortable locations. Our solution works in an open-ended fashion (no prior knowledge of the object the user wants to touch), can be used with multiple objects, and still allows interaction with any other point within user's reach. We use optimization approaches to compute the best physical location to interact with each visual element, and space partitioning techniques to distort the visual and physical spaces based on those mappings and allow multi-object retargeting. In this paper we describe the Erg-O technique, propose two retargeting strategies and report the results from a user study on 3D selection under different conditions, elaborating on their potential and application to specific usage scenarios.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126605"}, {"primary_key": "3871487", "vector": [], "sparse_vector": [], "title": "Torta: Generating Mixed-Media GUI and Command-Line App Tutorials Using Operating-System-Wide Activity Tracing.", "authors": ["Alok Mysore", "<PERSON>"], "summary": "Tutorials are vital for helping people perform complex software-based tasks in domains such as programming, data science, system administration, and computational research. However, it is tedious to create detailed step-by-step tutorials for tasks that span multiple interrelated GUI and command-line applications. To address this challenge, we created Torta, an end-to-end system that automatically generates step-by-step GUI and command-line app tutorials by demonstration, provides an editor to trim, organize, and add validation criteria to these tutorials, and provides a web-based viewer that can validate step-level progress and automatically run certain steps. The core technical insight that underpins <PERSON><PERSON> is that combining operating-system-wide activity tracing and screencast recording makes it easier to generate mixed-media (text+video) tutorials that span multiple GUI and command-line apps. An exploratory study on 10 computer science teaching assistants (TAs) found that they all preferred the experience and results of using Torta to record programming and sysadmin tutorials relevant to classes they teach rather than manually writing tutorials. A follow-up study on 6 students found that they all preferred following the Torta tutorials created by those TAs over the manually-written versions.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126628"}, {"primary_key": "3871488", "vector": [], "sparse_vector": [], "title": "FoamSense: Design of Three Dimensional Soft Sensors with Porous Materials.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Here we report the new soft sensor \"FoamSense\" that can measure the deformation state of a volumetric soft object such as compressed, bent, twisted and sheared (Figure 1). This sensor is made by impregnating a porous soft object with conductive ink. The design process of FoamSense is explained. We then summarized the features and basic characteristics of some porous materials for designing these sensors appropriately. We also proposed the potential of using digital fabrication for controlling the carrier structure of FoamSense. Proposed porous structure showed an anisotropic sensor characteristic. We discussed the potential and limitation of this approach. Three possible applications are proposed by using FoamSense. FoamSense supports a richer interaction between the user and soft objects.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126666"}, {"primary_key": "3871489", "vector": [], "sparse_vector": [], "title": "CollaVR: Collaborative In-Headset Review for VR Video.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Collaborative review and feedback is an important part of conventional filmmaking and now Virtual Reality (VR) video production as well. However, conventional collaborative review practices do not easily translate to VR video because VR video is normally viewed in a headset, which makes it difficult to align gaze, share context, and take notes. This paper presents CollaVR, an application that enables multiple users to review a VR video together while wearing headsets. We interviewed VR video professionals to distill key considerations in reviewing VR video. Based on these insights, we developed a set of networked tools that enable filmmakers to collaborate and review video in real-time. We conducted a preliminary expert study to solicit feedback from VR video professionals about our system and assess their usage of the system with and without collaboration features.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126659"}, {"primary_key": "3871490", "vector": [], "sparse_vector": [], "title": "Atypical: A Type System for Live Performances.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Chalktalk is a computer-based visual language based around real-time interaction with virtual objects in a blackboard-style environment. Its aim is to be a presentation and communication tool, using animation and interactivity to allow easy visualization of complex ideas and concepts. This demonstration will show Chalktalk in action, with focus on its ability to link these objects together to send data between them, as well as the flexible type system, named Atypical, that underpins this feature.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131812"}, {"primary_key": "3871491", "vector": [], "sparse_vector": [], "title": "AccelTag: A Passive Smart ID Tag with Acceleration Sensor for Interactive Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "There are many everyday situations in which users need to enter their user identification (user ID), such as logging in to computer systems and entering secure offices. In such situations, contactless passive IC cards are convenient because users can input their user ID simply by passing the card over a reader. However, these cards cannot be used for successive interactions. To address this issue, we propose AccelTag, a contactless IC card equipped with an acceleration sensor and a liquid crystal display (LCD). AccelTag utilizes high-function RFID technology so that the acceleration sensor and the LCD can also be driven by a wireless power supply. With its built-in acceleration sensor, AccelTag can acquire its direction and movement when it is waved over the reader. We demonstrate several applications using AccelTag, such as displaying several types of information in the card depending on the user's requirements.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131808"}, {"primary_key": "3871492", "vector": [], "sparse_vector": [], "title": "Grouping Applications Using Geometrical Information of Applications on Tabletop Systems.", "authors": ["Jonggyu Park", "<PERSON><PERSON><PERSON><PERSON>", "Young Ik Eom"], "summary": "In this paper, we propose a grouping scheme that classifies applications into groups for individual users by utilizing their geometrical information on a tabletop system. The proposed scheme investigates the geometrical information of each application, such as its position on the display and its rotational information, and then groups the applications of each individual user by utilizing a classifier with the geometrical information. We evaluate the proposed scheme with lab experiments, and the results show that, on average, 95.6% of applications are well classified into their users.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131820"}, {"primary_key": "3871493", "vector": [], "sparse_vector": [], "title": "Non-Linear Editor for Text-Based Screencast.", "authors": ["Jungkook Park", "Yeong Hoon Park", "<PERSON>"], "summary": "Screencasts, where computer screen is broadcast to a large audience on the web, are becoming popular as an online educational tool. Among various types of screencast content, popular are the contents that involve text editing, including computer programming. There are emerging platforms that support such text-based screencasts by recording every character insertion/deletion from the creator and reconstructing its playback on the viewer's screen. However, these platforms lack rich support for creating and editing the screencast itself, mainly due to the difficulty of manipulating recorded text changes; the changes are tightly coupled in sequence, thus modifying arbitrary part of the sequence is not trivial. We present a non-linear editing tool for text-based screencasts. With the proposed selective history rewrite process, our editor allows users to substitute an arbitrary part of a text-based screencast while preserving overall consistency of the rest of the text-based screencast.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131831"}, {"primary_key": "3871494", "vector": [], "sparse_vector": [], "title": "SmartSleeve: Real-time Sensing of Surface and Deformation Gestures on Flexible, Interactive Textiles, using a Hybrid Gesture Detection Pipeline.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Over the last decades, there have been numerous efforts in wearable computing research to enable interactive textiles. Most work focus, however, on integrating sensors for planar touch gestures, and thus do not fully take advantage of the flexible, deformable and tangible material properties of textile. In this work, we introduce SmartSleeve, a deformable textile sensor, which can sense both surface and deformation gestures in real-time. It expands the gesture vocabulary with a range of expressive interaction techniques, and we explore new opportunities using advanced deformation gestures, such as, Twirl, Twist, Fold, Push and Stretch. We describe our sensor design, hardware implementation and its novel non-rigid connector architecture. We provide a detailed description of our hybrid gesture detection pipeline that uses learning-based algorithms and heuristics to enable real-time gesture detection and tracking. Its modular architecture allows us to derive new gestures through the combination with continuous properties like pressure, location, and direction. Finally, we report on the promising results from our evaluations which demonstrate real-time classification.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126652"}, {"primary_key": "3871495", "vector": [], "sparse_vector": [], "title": "Shot Orientation Controls for Interactive Cinematography with 360 Video.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Virtual reality filmmakers creating 360-degree video currently rely on cinematography techniques that were developed for traditional narrow field of view film. They typically edit together a sequence of shots so that they appear at a fixed-orientation irrespective of the viewer's field of view. But because viewers set their own camera orientation they may miss important story content while looking in the wrong direction. We present new interactive shot orientation techniques that are designed to help viewers see all of the important content in 360-degree video stories. Our viewpoint-oriented technique reorients the shot at each cut so that the most important content lies in the the viewer's current field of view. Our active reorientation technique, lets the viewer press a button to immediately reorient the shot so that important content lies in their field of view. We present a 360-degree video player which implements these techniques and conduct a user study which finds that users spend 5.2-9.5% more time viewing the important points (manually labelled) of the scene with our techniques compared to the traditional fixed-orientation cuts. In practice, 360-degree video creators may label important content, but we also provide an automatic method for determining important content in existing 360-degree videos.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126636"}, {"primary_key": "3871496", "vector": [], "sparse_vector": [], "title": "MagTics: Flexible and Thin Form Factor Magnetic Actuators for Dynamic and Wearable Haptic Feedback.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present MagTics, a novel flexible and wearable haptic interface based on magnetically actuated bidirectional tactile pixels (taxels). MagTics' thin form factor and flexibility allows for rich haptic feedback in mobile settings. We propose a novel actuation mechanism based on bistable electromagnetic latching that combines high frame rate and holding force with low energy consumption and a soft and flexible form factor. We overcome limitations of traditional soft actuators by placing several hard actuation cells, driven by flexible printed electronics, in a soft 3D printed case. A novel EM-shielding prevents magnet-magnet interactions and allows for high actuator densities. A prototypical implementation comprising of 4 actuated pins on a 1.7 cm pitch, with 2 mm travel, and generating 160 mN to 200 mN of latching force is used to implement a number of compelling application scenarios including adding haptic and tactile display capabilities to wearable devices, to existing input devices and to provide localized haptic feedback in virtual reality. Finally, we report results of a psychophysical study, conducted to inform future developments and to identify possible application domains.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126609"}, {"primary_key": "3871497", "vector": [], "sparse_vector": [], "title": "Exploring of Simulating Passing through Feeling on the Wrist: Using Thermal Feedback.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wearable devices combining with VR/AR technology become a research hotspot these years. In some research, tactile displays are put on the skin and synchronized with VR/AR environment. Researchers try to use these display to simulate varied embodied feeling to enhance the immersion in the VR/AR environment. In the field of game entertainment, based on the scenario, sometimes the feeling of passing through the body need to be presented to the user. However this is physically impossible. Thus we make a exploration attempting to simulate this feeling by thermal feedback. Here we use two thermal modules bonding on the two side of the wrist( inside and outside). When we actuate two modules sequentially, user would perceive the stimuli and interpret this into a feeling of passing though. In the paper, we will introduce the interface and describe the experiment to determine the principle for thermo-tactile illusion of passing through.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131819"}, {"primary_key": "3871498", "vector": [], "sparse_vector": [], "title": "Codestrates: Literate Computing with Webstrates.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Clemens Nylandsted Klokmose"], "summary": "We introduce Codestrates, a literate computing approach to developing interactive software. Codestrates blurs the distinction between the use and development of applications. It builds on the literate computing approach, commonly found in interactive notebooks such as Jupyter notebook. Literate computing weaves together prose and live computation in the same document. However, literate computing in interactive notebooks are limited to computation and it is challenging to extend their user interface, reprogram their functionality, or develop stand-alone applications. Codestrates builds literate computing capabilities on top of Webstrates and demonstrates how it can be used for (i) collaborative interactive notebooks, (ii) extending its functionality from within itself, and (iii) developing reprogrammable applications.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126642"}, {"primary_key": "3871499", "vector": [], "sparse_vector": [], "title": "Feeling Fireworks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Feeling Fireworks, a tactile firework show. Feeling Fireworks is aimed at making fireworks more inclusive for blind and visually impaired users in a novel experience that is open to all. Tactile effects are created using directable water jets that spray onto the rear of a flexible screen, with different nozzles for different firework effects. Our approach is low-cost and scales well, and allows for dynamic tactile effects to be rendered with high spatial resolution. A user study demonstrates that the tactile effects are meaningful analogs to the visual fireworks that they represent. Beyond the specific application, the technology represents a novel and cost-effective approach for making large scalable tactile displays.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131811"}, {"primary_key": "3871500", "vector": [], "sparse_vector": [], "title": "One Reality: Augmenting How the Physical World is Experienced by combining Multiple Mixed Reality Modalities.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Most of our daily activities take place in the physical world, which inherently imposes physical constraints. In contrast, the digital world is very flexible, but usually isolated from its physical counterpart. To combine these two realms, many Mixed Reality (MR) techniques have been explored, at different levels in the continuum. In this work we present an integrated Mixed Reality ecosystem that allows users to incrementally transition from pure physical to pure virtual experiences in a unique reality. This system stands on a conceptual framework composed of 6 levels. This paper presents these levels as well as the related interaction techniques.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126638"}, {"primary_key": "3871501", "vector": [], "sparse_vector": [], "title": "JDLED: Towards Visio-Tactile Displays Based on Electrochemical Locomotion of Liquid-Metal Janus Droplets.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "An actuated shape-changing interface with faster response and smaller pixel size using a liquid material can provide real time tangible interaction with the digital world in physical space. To this end, we demonstrate an interface that displays user-defined patterns dynamically using liquid metal droplets as programmable micro robots on a flat surface. We built a prototype using an array of embedded electrodes and a switching circuit to control the jump of the droplets from electrode to electrode. The actuation and dynamics of the droplets under the finger provides mild tactile feedback to the user. Our demo is the first to show a planar visio-tactile display using liquid metal, and is a first step to make shape-changing physical ephemeral widgets on a tabletop interface.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131793"}, {"primary_key": "3871502", "vector": [], "sparse_vector": [], "title": "You as a Puppet: Evaluation of Telepresence User Interface for Puppetry.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose an immersive telepresence system for puppetry that transmits a human performer's body and facial movements into a puppet with audiovisual feedback to the performer. The cameras carried in place of puppet's eyes stream live video to the HMD worn by the performer, so that performers can see the images from the puppet's eyes with their own eyes and have a visual understanding of the puppet's ambience. In conventional methods to manipulate a puppet (a hand-puppet, a string-puppet, and a rod-puppet), there is a need to practice manipulating puppets, and there is difficulty carrying out interactions with the audience. Moreover, puppeteers must be positioned exactly where the puppet is. The proposed system addresses these issues by enabling a human performer to manipulate the puppet remotely using his or her body and facial movements. We conducted several user studies with both beginners and professional puppeteers. The results show that, unlike the conventional method, the proposed system facilitates the manipulation of puppets especially for beginners. Moreover, this system allows performers to enjoy puppetry and fascinate audiences.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126608"}, {"primary_key": "3871503", "vector": [], "sparse_vector": [], "title": "Interactive Room Capture on 3D-Aware Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We propose a novel interactive system to simplify the process of indoor 3D CAD room modeling. Traditional room modeling methods require users to measure room and furniture dimensions, and manually select models that match the scene from large catalogs. Users then employ a mouse and keyboard interface to construct walls and place the objects in their appropriate locations. In contrast, our system leverages the sensing capabilities of a 3D aware mobile device, recent advances in object recognition, and a novel augmented reality user interface, to capture indoor 3D room models in-situ. With a few taps, a user can mark the surface of an object, take a photo, and the system retrieves and places a matching 3D model into the scene, from a large online database. User studies indicate that this modality is significantly quicker, more accurate, and requires less effort than traditional desktop tools.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126629"}, {"primary_key": "3871504", "vector": [], "sparse_vector": [], "title": "Ultrasonic Cuisine: Proposal of Ultrasonic Non-contact Stirring Methods.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we propose a method of non-contact stirring. Ultrasonic waves have been studied for various applications. However, devices using ultrasound have been devised only to specialize in one role up to now. In recent years, we aim at generalization of aerial ultrasonic equipment used for various applications such as tactile presentation and super directional speaker, and propose applications closely our real life.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131801"}, {"primary_key": "3871505", "vector": [], "sparse_vector": [], "title": "A Modular Smartphone for Lending.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We motivate, design, and prototype a modular smartphone designed to make temporary device lending trustworthy and convenient. The concept is that the phone can be separated into pieces, so a child, friend, or even stranger can begin an access-controlled interaction with one piece, while the own-er retains another piece to continue their tasks and monitor activity. This is grounded in a survey capturing attitudes towards device lending, and an exploratory study probing how people might lend pieces of different kinds of modular smartphones. Design considerations are generated for a hardware form factor and software interface to support different lending scenarios. A functional prototype combining three smartphones into a single modular device is described and used to demonstrate a lending interaction design. A usability test validates the concept using the prototype.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126633"}, {"primary_key": "3871506", "vector": [], "sparse_vector": [], "title": "GestAKey: Get More Done with Just-a-Key on a Keyboard.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>alvez", "<PERSON><PERSON>", "Sur<PERSON>"], "summary": "The computer keyboard is a widely used input device to operate computers, such as text entry and command execution. Typically, keystrokes are detected as binary states (e.g. \"pressed\" vs. \"not pressed\"). Due to this, more complex input commands need multiple key presses that could go up to pressing four keys at the same time, such as pressing \"Cmd + Shift + Opt + 4\" to take a screenshot to the clipboard on macOS. We present GestAKey, a technique to enable multifunctional keystrokes on a single key, providing new interaction possibilities on the familiar keyboards. The system consists of touch sensitive keycaps and a software backend that recognizes micro-gestures performed on individual keys to perform system commands or input special characters. In this demo, attendees will get the chance to interact with several GestAKey-enabled proof-of-concept applications.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131786"}, {"primary_key": "3871507", "vector": [], "sparse_vector": [], "title": "Markit and Talkit: A Low-Barrier Toolkit to Augment 3D Printed Models with Audio Annotations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As three-dimensional printers become more available, 3D printed models can serve as important learning materials, especially for blind people who perceive the models tactilely. Such models can be much more powerful when augmented with audio annotations that describe the model and their elements. We present Markit and Talkit, a low-barrier toolkit for creating and interacting with 3D models with audio annotations. Makers (e.g., hobbyists, teachers, and friends of blind people) can use Markit to mark model elements and associate then with text annotations. A blind user can then print the augmented model, launch the Talkit application, and access the annotations by touching the model and following Talkit's verbal cues. Talkit uses an RGB camera and a microphone to sense users' inputs so it can run on a variety of devices. We evaluated Mark<PERSON> with eight sighted \"makers\" and Talkit with eight blind people. On average, non-experts added two annotations to a model in 275 seconds (SD=70) with Markit. Meanwhile, with Talkit, blind people found a specified annotation on a model in an average of 7 seconds (SD=8).", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126650"}, {"primary_key": "3871508", "vector": [], "sparse_vector": [], "title": "shapeShift: A Mobile Tabletop Shape Display for Tangible and Haptic Interaction.", "authors": ["Alexa F. Siu", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "shapeShift is a compact, high-resolution (7 mm pitch), mobile tabletop shape display. We explore potential interaction techniques in both passive and active mobile scenarios. In the passive case, the user is able to freely move and spin the display as it renders elements. We introduce use cases for rendering lateral I/O elements, exploring volumetric datasets, and grasping and manipulating objects. On an active omnidirectional-robot platform, shapeShift can display moving objects and provide both vertical and lateral kinesthetic feedback. We use the active platform as an encounter-type haptic device combined with a head-mounted display to dynamically simulate the presence of virtual content.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131792"}, {"primary_key": "3871509", "vector": [], "sparse_vector": [], "title": "Scanalog: Interactive Design and Debugging of Analog Circuits with Programmable Hardware.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Analog circuit design is a complex, error-prone task in which the processes of gathering observations, formulating reasonable hypotheses, and manually adjusting the circuit raise significant barriers to an iterative workflow. We present Scanalog, a tool built on programmable analog hardware that enables users to rapidly explore different circuit designs using direct manipulation, and receive immediate feedback on the resulting behaviors without manual assembly, calculation, or probing. Users can interactively tune modular signal transformations on hardware with real inputs, while observing real-time changes at all points in the circuit. They can create custom unit tests and assertions to detect potential issues. We describe three interactive applications demonstrating the expressive potential of Scanalog. In an informal evaluation, users successfully conditioned analog sensors and described Scanalog as both enjoyable and easy to use.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126618"}, {"primary_key": "3871510", "vector": [], "sparse_vector": [], "title": "WearMail: On-the-Go Access to Information in Your Email with a Privacy-Preserving Human Computation Workflow.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON>) <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Email is more than just a communication medium. Email serves as an external memory for people---it contains our reservation numbers, meeting details, phone numbers, and more. Often, people need access to this information while on the go, which is cumbersome from mobile devices with limited I/O bandwidth. In this paper, we introduce WearMail, a conversational interface to retrieve specific information in email. WearMail is mostly automated but is made robust to information extraction tasks via a novel privacy-preserving human computation workflow. In WearMail, crowdworkers never have direct access to emails, but rather (i) generate an email filter to help the system find messages that may contain the desired information, and (ii) generate examples of the requested information that are then used to create custom, low-level information extractors that run automatically within the set of filtered emails. We explore the impact of varying levels of obfuscation on result quality, demonstrating that workers are able to deal with highly-obfuscated information nearly as well as with the original. WearMail introduces general mechanisms that let the crowd search and select private data without having direct access to the data itself.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126603"}, {"primary_key": "3871511", "vector": [], "sparse_vector": [], "title": "Crowd Research: Open and Scalable University Laboratories.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> (Neil) <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Imanol <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Research experiences today are limited to a privileged few at select universities. Providing open access to research experiences would enable global upward mobility and increased diversity in the scientific workforce. How can we coordinate a crowd of diverse volunteers on open-ended research? How could a PI have enough visibility into each person's contributions to recommend them for further study? We present Crowd Research, a crowdsourcing technique that coordinates open-ended research through an iterative cycle of open contribution, synchronous collaboration, and peer assessment. To aid upward mobility and recognize contributions in publications, we introduce a decentralized credit system: participants allocate credits to each other, which a graph centrality algorithm translates into a collectively-created author order. Over 1,500 people from 62 countries have participated, 74% from institutions with low access to research. Over two years and three projects, this crowd has produced articles at top-tier Computer Science venues, and participants have gone on to leading graduate programs.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126648"}, {"primary_key": "3871512", "vector": [], "sparse_vector": [], "title": "Playful Interactions with Body Channel Communication: Conquer it!", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Conquer it! is a lightweight proof-of-concept exertion game that demonstrates Body Channel Communication (BCC) in a smart environment. BCC employs the human body as communication medium to transfer digital data between physical objects by using electric fields that are coupled to the body. During the game participants are provided with BCC wearables, each of which represents a specific RGB color. When the user stands, walks on, or touches with a hand the BCC tiles, communication is automatically established: the corresponding sensor area decodes the message (RGB value) originating from the wearable and lights up according to that color for two seconds. The goal of the game is to try to light up as many tile cells simultaneously as possible. Participants can try to keep alive the colors by continuously moving around on the tiles. In the multiuser version, by stepping on or touching a blinking cell, users can immediately claim the area and overwrite the color of that subtile.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131798"}, {"primary_key": "3871513", "vector": [], "sparse_vector": [], "title": "AutoDub: Automatic Redubbing for Voiceover Editing.", "authors": ["<PERSON><PERSON><PERSON>", "Paris Smaragdis", "<PERSON><PERSON>ham J. Mysore"], "summary": "Redubbing is an extensively used technique to correct errors in voiceover recordings. It involves re-recording a part of a voiceover, identifying the corresponding section of audio in the original recording that needs to be replaced, and using low level audio tools to replace the audio. Although this sequence of steps can be performed using traditional audio editing tools, the process can be tedious when dealing with long voiceover recordings and prohibitively difficult for users not familiar with such tools. To address this issue, we present AutoDub, a novel system for redubbing voiceover recordings. Using our system, a user simply needs to re-record the part of the voiceover that needs to be replaced. Our system automatically locates the corresponding part in the original recording and performs the low level audio processing to replace it. The system can be easily incorporated in any existing sophisticated audio editor or can be employed as a functionality in an audio-guided user interface. User studies involving participation from novice, knowledgeable and expert users indicate that our tool is preferred to a traditional audio editor based redubbing approach by all categories of users due to its faster and easier redubbing capabilities.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126661"}, {"primary_key": "3871514", "vector": [], "sparse_vector": [], "title": "Neuroanatomical Correlates of Perceived Usability.", "authors": ["Chi Thanh Vi", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Usability has a distinct subjective component, yet surprisingly little is known about its neural basis and relation to the neuroanatomy of aesthetics. To begin closing this gap, we conducted two functional magnetic resonance imaging studies in which participants were shown static webpages (in the first study) and videos of interaction with webpages (in the second study). The webpages were controlled so as to exhibit high and low levels of perceived usability and perceived aesthetics. Our results show unique links between perceived usability and brain areas involved in functions such as emotional processing (left fusiform gyrus, superior frontal gyrus), anticipation of physical interaction (precentral gyrus), task intention (anterior cingulate cortex), and linguistic processing (medial and bilateral superior frontal gyri). We use these findings to discuss the brain correlates of perceived usability and the use of fMRI for usability evaluation and for generating new user experiences.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126657"}, {"primary_key": "3871515", "vector": [], "sparse_vector": [], "title": "Carpacio: Repurposing Capacitive Sensors to Distinguish Driver and Passenger Touches on In-Vehicle Screens.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Shwetak N. Patel"], "summary": "Standard vehicle infotainment systems often include touch screens that allow the driver to control their mobile phone, navigation, audio, and vehicle configurations. For the driver's safety, these interfaces are often disabled or simplified while the car is in motion. Although this reduced functionality aids in reducing distraction for the driver, it also disrupts the usability of infotainment systems for passengers. Current infotainment systems are unaware of the seating position of their user and hence, cannot adapt. We present Carpacio, a system that takes advantage of the capacitive coupling created between the touchscreen and the electrode present in the seat when the user touches the capacitive screen. Using this capacitive coupling phenomenon, a car infotainment system can intelligently distinguish who is interacting with the screen seamlessly, and adjust its user interface accordingly. Manufacturers can easily incorporate <PERSON><PERSON><PERSON> into vehicles since the included seat occupancy detection sensor or seat heating coils can be used as the seat electrode. We evaluated <PERSON><PERSON><PERSON> in eight different cars and five mobile devices and found that it correctly detected over 2600 touches with an accuracy of 99.4%.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126623"}, {"primary_key": "3871516", "vector": [], "sparse_vector": [], "title": "Auditory Overview of Web Pages for Screen Reader Users.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Blind users browse the web using screen readers. Screen readers read the content on a web page sequentially via synthesized speech. The linear nature of this process makes it difficult to obtain an overview of the web page, which creates navigation challenges. To alleviate this problem, we have developed ScreenTrack, a browser extension that summarizes a web page's accessibility features into a short, dynamically generated soundtrack. Users can quickly gain an overview of the presence of web elements useful for navigation on a web page. Here we describe ScreenTrack and discuss future research plans.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131837"}, {"primary_key": "3871517", "vector": [], "sparse_vector": [], "title": "Secondary Motion for Performed 2D Animation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "When bringing animated characters to life, artists often augment the primary motion of a figure by adding secondary animation -- subtle movement of parts like hair, foliage or cloth that complements and emphasizes the primary motion. Traditionally, artists add secondary motion to animated illustrations only through arduous manual effort, and often eschew it entirely. Emerging ``live' performance applications allow both novices and experts to perform the primary motion of a character, but only a virtuoso performer could manage the degrees of freedom needed to specify both primary and secondary motion together. This paper introduces physically-inspired rigs that propagate the primary motion of layered, illustrated characters to produce plausible secondary motion. These composable elements are rigged and controlled via a small number of parameters to produce an expressive range of effects. Our approach supports a variety of the most common secondary effects, which we demonstrate with an assortment of characters of varying complexity.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126641"}, {"primary_key": "3871518", "vector": [], "sparse_vector": [], "title": "Triggering Artwork Swaps for Live Animation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Live animation of 2D characters is a new form of storytelling that has started to appear on streaming platforms and broadcast TV. Unlike traditional animation, human performers control characters in real time so that they can respond and improvise to live events. Current live animation systems provide a range of animation controls, such as camera input to drive head movements, audio for lip sync, and keyboard shortcuts to trigger discrete pose changes via artwork swaps. However, managing all of these controls during a live performance is challenging. In this work, we present a new interactive system that specifically addresses the problem of triggering artwork swaps in live settings. Our key contributions are the design of a multi-touch triggering interface that overlays visual triggers around a live preview of the character, and a predictive triggering model that leverages practice performances to suggest pose transitions during live performances. We evaluate our system with quantitative experiments, a user study with novice participants, and interviews with professional animators.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126596"}, {"primary_key": "3871519", "vector": [], "sparse_vector": [], "title": "CurrentViz: Sensing and Visualizing Electric Current Flows of Breadboarded Circuits.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Electric current and voltage are fundamental to learning, understanding, and debugging circuits. Although both can be measured using tools such as multimeters and oscilloscopes, electric current is much more difficult to measure because users have to unplug parts of a circuit and then insert the measuring tools in serial. Furthermore, users need to restore the circuits back to its original state after measurements have been taken. In practice, this cumbersome process poses a formidable barrier to knowing how current flows throughout a circuit. We present CurrentViz, a system that can sense and visualize the electric current flowing through a circuit, which helps users quickly understand otherwise invisible circuit behavior. It supports fully automatic, ubiquitous, and real-time collection of amperage information of breadboarded circuits. It also supports visualization of the amperage data on a circuit schematic to provide an intuitive view into the current state of a circuit.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126646"}, {"primary_key": "3871520", "vector": [], "sparse_vector": [], "title": "DodecaPen: Accurate 6DoF Tracking of a Passive Stylus.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Shangchen Han", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose a system for real-time six degrees of freedom (6DoF) tracking of a passive stylus that achieves sub-millimeter accuracy, which is suitable for writing or drawing in mixed reality applications. Our system is particularly easy to implement, requiring only a monocular camera, a 3D printed dodecahedron, and hand-glued binary square markers. The accuracy and performance we achieve are due to model-based tracking using a calibrated model and a combination of sparse pose estimation and dense alignment. We demonstrate the system performance in terms of speed and accuracy on a number of synthetic and real datasets, showing that it can be competitive with state-of-the-art multi-camera motion capture systems. We also demonstrate several applications of the technology ranging from 2D and 3D drawing in VR to general object manipulation and board games.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126664"}, {"primary_key": "3871521", "vector": [], "sparse_vector": [], "title": "CircuitSense: Automatic Sensing of Physical Circuits and Generation of Virtual Circuits to Support Software Tools.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The rise of Maker communities and open-source electronic prototyping platforms have made electronic circuit projects increasingly popular around the world. Although there are software tools that support the debugging and sharing of circuits, they require users to manually create the virtual circuits in software, which can be time-consuming and error-prone. We present CircuitSense, a system that automatically recognizes the wires and electronic components placed on breadboards. It uses a combination of passive sensing and active probing to detect and generate the corresponding circuit representation in software in real-time. CircuitSense bridges the gap between the physical and virtual representations of circuits. It enables users to interactively construct and experiment with physical circuits while gaining the benefits of using software tools. It also dramatically simplifies the sharing of circuit designs with online communities.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126634"}, {"primary_key": "3871522", "vector": [], "sparse_vector": [], "title": "iSphere: Self-Luminous Spherical Drone Display.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present iSphere, a flying spherical display that can display high resolution and bright images in all directions from anywhere in 3D space. Our goal is to build a new platform which can physically and directly emerge arbitrary bodies in the real world. iSphere flies by itself using a built-in drone and creates a spherical display by rotating arcuate multi light-emitting diode (LED) tapes around the drone. As a result of the persistence of human vision, we see it as a spherical display flying in the sky. The proposed method yields large display surfaces, high resolution, drone mobility, high visibility and 360° field of view. Previous approaches fail to match these characteristics, because of problems with aerodynamics and payload. We construct a prototype and validate the proposed method. The unique characteristics and benefits of flying spherical display surfaces are discussed and we describe application scenarios based on iSphere such as guidance, signage and telepresence.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126631"}, {"primary_key": "3871523", "vector": [], "sparse_vector": [], "title": "BlowFab: Rapid Prototyping for Rigid and Reusable Objects using Inflation of Laser-cut Surfaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This study proposes BlowFab, a prototyping method used to create a 2.5-dimensional prototype in a short time by combining laser cutting and blow molding techniques. The user creates adhesive areas and inflatable areas by engraving and cutting multilayered plastic sheets using a laser cutter. These adhesive areas are fused automatically by overlapping two crafted sheets and softening them with a heater. The user can then create hard prototypes by injecting air into the sheets. Objects can be bent in any direction by cutting incisions or engraving a resistant resin. The user can create uneven textures by engraving a pattern with a heat-resistant film. These techniques can be used for prototyping various strong inflatable objects. The finished prototype is strong and can be collapsed readily for storage when not required. In this study, the design process is described using the proposed method. The study also evaluates possible bending mechanisms and texture expression methods along with various usage scenarios and discusses the resolution, strength, and reusability of the prototype developed.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126624"}, {"primary_key": "3871524", "vector": [], "sparse_vector": [], "title": "iSoft: A Customizable Soft Sensor with Real-time Continuous Contact and Stretching Sensing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present iSoft, a single volume soft sensor capable of sensing real-time continuous contact and unidirectional stretching. We propose a low-cost and an easy way to fabricate such piezoresistive elastomer-based soft sensors for instant interactions. We employ an electrical impedance tomography (EIT) technique to estimate changes of resistance distribution on the sensor caused by fingertip contact. To compensate for the rebound elasticity of the elastomer and achieve real-time continuous contact sensing, we apply a dynamic baseline update for EIT. The baseline updates are triggered by fingertip contact and movement detections. Further, we support unidirectional stretching sensing using a model-based approach which works separately with continuous contact sensing. We also provide a software toolkit for users to design and deploy personalized interfaces with customized sensors. Through a series of experiments and evaluations, we validate the performance of contact and stretching sensing. Through example applications, we show the variety of examples enabled by iSoft.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126654"}, {"primary_key": "3871525", "vector": [], "sparse_vector": [], "title": "SceneCtrl: Mixed Reality Enhancement via Efficient Scene Editing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gang Ren", "<PERSON><PERSON>"], "summary": "Due to the development of 3D sensing and modeling techniques, the state-of-the-art mixed reality devices such as Microsoft Hololens have the ability of digitalizing the physical world. This unique feature bridges the gap between virtuality and reality and largely elevates the user experience. Unfortunately, the current solution only performs well if the virtual contents complement the real scene. It can easily cause visual artifacts when the reality needs to be modified due to the virtuality (e.g., remove real objects to offer more space for virtual objects), a common scenario in mixed reality applications such as room redecoration and environment design. We present a novel system, called emph{SceneCtrl}, that allows the user to interactively edit the real scene sensed by <PERSON><PERSON><PERSON><PERSON>, such that the reality can be adapted to suit virtuality. Our proof-of-concept prototype employs scene reconstruction and understanding to enable efficient editing such as deleting, moving, and copying real objects in the scene. We also demonstrate emph{SceneCtrl} on a number of example scenarios in mixed reality, verifying the enhanced experience by resolving conflicts between virtuality and reality.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126601"}, {"primary_key": "3871526", "vector": [], "sparse_vector": [], "title": "Systems for Improving Online Discussion.", "authors": ["<PERSON>"], "summary": "More and more of the discussion that happens now takes place on the web, whether it be for work, communities of interest, political and civic discourse, or education. However, little has changed in the design of online discussion systems, such as email, forums, and chat, in the decades they have been available, even as discussions grow in size and scope. As a result, online communities continue to struggle with issues stemming from growing participation, a diversity of participants, and new application domains. To solve these problems, my research is on building novel online discussion systems that give members of a community direct control over their experiences and information within these systems. Specifically, I focus on: 1) tools to make sense of large discussions and extract usable knowledge from them, 2) tools to situate conversations in the context what is being discussed, as well as 3) tools to give users more fine-grained control over the delivery of content, so that messages only go to those who wish to receive it.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131845"}, {"primary_key": "3871527", "vector": [], "sparse_vector": [], "title": "Thermal-Comfort Design of Personalized Casts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Chengkai Dai", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper introduces a novel method for designing personalized orthopedic casts which are aware of thermal-comfort while satisfying mechanical requirements. Our pipeline starts from thermal images taken by an infrared camera, by which the distribution of thermal-comfort sensitivity is generated on the surface of a 3D scanned model. We formulate a hollowed Voronoi tessellation pattern to represent the covered region for a web-like cast design. The pattern is further optimized according to the thermal-comfort sensitivity calculated from thermal images. Working together with a thickness variation method, we generate a solid model for a personalized cast maximizing both thermal comfort and mechanical stiffness. To demonstrate the effectiveness of our approach, 3D printed models of personalized casts are tested on body parts of different individuals.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126600"}, {"primary_key": "3871528", "vector": [], "sparse_vector": [], "title": "DS.js: Turn Any Webpage into an Example-Centric Live Programming Environment for Learning Data Science.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Data science courses and tutorials have grown popular in recent years, yet they are still taught using production-grade programming tools (e.g., R, MATLAB, and Python IDEs) within desktop computing environments. Although powerful, these tools present high barriers to entry for novices, forcing them to grapple with the extrinsic complexities of software installation and configuration, data file management, data parsing, and Unix-like command-line interfaces. To lower the barrier for novices to get started with learning data science, we created DS.js, a bookmarklet that embeds a data science programming environment directly into any existing webpage. By transforming any webpage into an example-centric IDE, DS.js eliminates the aforementioned complexities of desktop-based environments and turns the entire web into a rich substrate for learning data science. DS.js automatically parses HTML tables and CSV/TSV data sets on the target webpage, attaches code editors to each data set, provides a data table manipulation and visualization API designed for novices, and gives instructional scaffolding in the form of bidirectional previews of how the user's code and data relate.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126663"}, {"primary_key": "3871529", "vector": [], "sparse_vector": [], "title": "Everyday Eye Contact Detection Using Unsupervised Gaze Target Discovery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Eye contact is an important non-verbal cue in social signal processing and promising as a measure of overt attention in human-object interactions and attentive user interfaces. However, robust detection of eye contact across different users, gaze targets, camera positions, and illumination conditions is notoriously challenging. We present a novel method for eye contact detection that combines a state-of-the-art appearance-based gaze estimator with a novel approach for unsupervised gaze target discovery, i.e. without the need for tedious and time-consuming manual data annotation. We evaluate our method in two real-world scenarios: detecting eye contact at the workplace, including on the main work display, from cameras mounted to target objects, as well as during everyday social interactions with the wearer of a head-mounted egocentric camera. We empirically evaluate the performance of our method in both scenarios and demonstrate its effectiveness for detecting eye contact independent of target object type and size, camera position, and user and recording environment.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3126594.3126614"}, {"primary_key": "3871530", "vector": [], "sparse_vector": [], "title": "An EEG-based Adaptive Training System for ASD Children.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Children with ASD (Autism Spectrum Disorder) have difficulties in expressing their feelings and needs, their teachers have to be very familiar with them to adjust teaching contents in related training lessons. In this paper, we present an adaptive training system with EEG (Electroencephalogram) devices for autistic children. We designed an EEG helmet to monitor children's attention levels, and a video chat system with virtual cartoon faces covered on teacher's face. Cartoon faces are synchronized with the performer's facial movements to help trainers express themselves in an exaggerated way. When the attention reduction is detected by the EEG helmet, cartoon face will adjust automatically, and try to draw their attention back through changing cartoon types, colors, brightness, etc. Each change and feedback from children will be traced by the helmet and analyzed for improvements. By continuous iterative learning, the system will become smarter in avoiding children's physical exhaustion. The system was introduced in the form of a specific training lesson to an ASD school, and preliminary experiment has indicated an encouraging result.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131832"}, {"primary_key": "3871531", "vector": [], "sparse_vector": [], "title": "KinToon: A Kinect Facial Projector for Communication Enhancement for ASD Children.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Children with ASD (Autism Spectrum Disorder) have social communication difficulties partly due to their abnormal avoidance of eye contact on human faces, yet they have a normal visual processing strategy on cartoon face. In this paper, we present KinToon, a face-to-face communication enhancement system to help ASD children in their training lessons. Our system use Kinect to scan human face and extract key points from facial contour, and match them to corresponding key points of a cartoon face. A modified cartoon face is projected to the communicator's face to achieve the effect of dynamic \"makeup\". ASD children will finally talk to the communicator with dynamic cartoon makeup, which would reduce their stress of interacting with people and make them easier to understand emotions. The interactive devices were applied to an ASD training lesson, and our creative approach was examined to be relatively effective in encouraging ASD children to fetch more emotional information and have more eye contact with people by eye tracking.", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785.3131813"}, {"primary_key": "3885254", "vector": [], "sparse_vector": [], "title": "Proceedings of the 30th Annual ACM Symposium on User Interface Software and Technology, UIST 2017, Quebec City, QC, Canada, October 22 - 25, 2017", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Welcome to the 30th Annual ACM Symposium on User Interface Software and Technology (UIST), held from October 22nd to October 25th 2017, in Québec City, Canada. UIST is the premier forum for the presentation of research innovations in the software and technology of human-computer interfaces. Sponsored by ACM's special interest groups on computer-human interaction (SIGCHI) and computer graphics (SIGGRAPH), UIST brings together researchers and practitioners from diverse areas including graphical & web user interfaces, tangible & ubiquitous computing, virtual & augmented reality, multimedia, new input & output devices, fabrication, wearable computing and CSCW. UIST 2017 received 324 technical paper submissions. After a thorough review process, the 42-member program committee accepted 73 papers (22.5%). Each anonymous submission that entered the full review process was first reviewed by three external reviewers, and a meta-review was provided by a program committee member. If, after these four reviews, the submission was deemed to pass a rebuttal threshold, we asked the authors to submit a short rebuttal addressing the reviewers' concerns and a second member of the program committee to review the paper. The program committee met in person at Carnegie Mellon University in Pittsburgh, PA, USA on June 23rd and 24th, 2017, to select the papers to invite for the program. Submissions were accepted only after the authors provided a final revision addressing the committee's comments. Three papers (<1% of submissions) were recognized by the reviewers and the program committee as Best Papers and eight (2.5%) received Honorable Mentions. In addition to papers, our program includes 26 posters, 56 demonstrations, and 8 student presentations in the thirteenth annual Doctoral Symposium. Our program also features the ninth annual Student Innovation Contest -- teams from all over the world will compete in this year's contest, in which we explore how novel input, interaction, actuation, and output techniques can augment experiences that literally \"reach out\" into the world. Student teams will develop novel input and output techniques for an Arduino Braccio, a desktop-sized and fully-customizable, multi-DOF robotic arm. The opening keynote will be given by Prof. Gabriella Coleman from McGill University and the closing keynote will be given by Prof. Niki Kittur from Carnegie Mellon University. Both will highlight impactful and sometimes unexpected ways in which interactive technology can be used to shape our society. Once again: Welcome to Québec City and to UIST 2017!", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": ""}, {"primary_key": "3885255", "vector": [], "sparse_vector": [], "title": "Adjunct Publication of the 30th Annual ACM Symposium on User Interface Software and Technology, UIST 2017 Adjunct Volume, Quebec City, QC, Canada, October 22 - 25, 2017", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Welcome to the 30th Annual ACM Symposium on User Interface Software and Technology (UIST), held from October 22nd to October 25th 2017, in Québec City, Canada. UIST is the premier forum for the presentation of research innovations in the software and technology of human-computer interfaces. Sponsored by ACM's special interest groups on computer-human interaction (SIGCHI) and computer graphics (SIGGRAPH), UIST brings together researchers and practitioners from diverse areas including graphical & web user interfaces, tangible & ubiquitous computing, virtual & augmented reality, multimedia, new input & output devices, fabrication, wearable computing and CSCW. UIST 2017 received 324 technical paper submissions. After a thorough review process, the 42-member program committee accepted 73 papers (22.5%). Each anonymous submission that entered the full review process was first reviewed by three external reviewers, and a meta-review was provided by a program committee member. If, after these four reviews, the submission was deemed to pass a rebuttal threshold, we asked the authors to submit a short rebuttal addressing the reviewers' concerns and a second member of the program committee to review the paper. The program committee met in person at Carnegie Mellon University in Pittsburgh, PA, USA on June 23rd and 24th, 2017, to select the papers to invite for the program. Submissions were accepted only after the authors provided a final revision addressing the committee's comments. Three papers (<1% of submissions) were recognized by the reviewers and the program committee as Best Papers and eight (2.5%) received Honorable Mentions. In addition to papers, our program includes 26 posters, 56 demonstrations, and 8 student presentations in the thirteenth annual Doctoral Symposium. Our program also features the ninth annual Student Innovation Contest - teams from all over the world will compete in this year's contest, in which we explore how novel input, interaction, actuation, and output techniques can augment experiences that literally \"reach out\" into the world. Student teams will develop novel input and output techniques for an Arduino Braccio, a desktop-sized and fully-customizable, multi-DOF robotic arm. The opening keynote will be given by Prof. Gabriella Coleman from McGill University and the closing keynote will be given by Prof. Niki Kittur from Carnegie Mellon University. Both will highlight impactful and sometimes unexpected ways in which interactive technology can be used to shape our society. Once again: Welcome to Québec City and to UIST 2017!", "published": "2017-01-01", "category": "uist", "pdf_url": "", "sub_summary": "", "source": "uist", "doi": "10.1145/3131785"}]