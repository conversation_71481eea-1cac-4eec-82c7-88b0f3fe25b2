"use client";

import 'katex/dist/katex.min.css';
import { useEffect, useState } from 'react';
import katex from 'katex';

interface LatexPreviewProps {
  formula: string;
}

export function LatexPreview({ formula }: LatexPreviewProps) {
  // 预处理公式
  const processFormula = (input: string): string[] => {
    if (!input.trim()) return [];
    
    // 处理带有 LaTeX 分隔符的情况
    const delimiterPattern = /(\\\(|\\\)|\\\[|\\\]|\$\$|\$)/g;
    
    // 1. 先按分隔符将内容分块
    const rawBlocks = input.split(delimiterPattern).filter(Boolean);
    
    // 2. 处理并收集有效的数学公式块
    const mathBlocks: string[] = [];
    let isInMathMode = false;
    let currentBlock = '';
    
    for (const block of rawBlocks) {
      // 检查是否是分隔符
      if (block === '\\(' || block === '$' || block === '\\[' || block === '$$') {
        isInMathMode = true;
        continue;
      } else if (block === '\\)' || block === '\\]' || block === '$' || block === '$$') {
        isInMathMode = false;
        if (currentBlock.trim()) {
          mathBlocks.push(currentBlock.trim());
          currentBlock = '';
        }
        continue;
      }
      
      if (isInMathMode) {
        // 在数学模式中，收集内容
        currentBlock += block;
      } else {
        // 检查是否有普通文本中嵌入的公式块
        const textBlocks = block.split(/\n{2,}/).map(b => b.trim()).filter(Boolean);
        mathBlocks.push(...textBlocks);
      }
    }
    
    // 如果最后还有未处理的块
    if (currentBlock.trim()) {
      mathBlocks.push(currentBlock.trim());
    }
    
    // 3. 处理没有分隔符的情况
    if (mathBlocks.length === 0 && input.trim()) {
      return input.split(/\n{2,}/).map(b => b.trim()).filter(Boolean);
    }
    
    return mathBlocks;
  };

  const blocks = processFormula(formula);

  return (
    <div className="flex flex-col gap-2">
      <div className="text-sm font-medium">预览</div>
      <div className="h-full border rounded-lg p-4 flex flex-col gap-4 bg-white">
        <div className="overflow-x-auto w-full" style={{paddingBottom: 2}}>
          {blocks.length === 0 ? (
            <div className="text-gray-400">暂无内容</div>
          ) : (
            blocks.map((block, idx) => {
              try {
                // 在这里确保去除了所有可能的LaTeX分隔符
                const cleanBlock = block.replace(/(\\\(|\\\)|\\\[|\\\]|\$\$|\$)/g, '').trim();
                const html = katex.renderToString(cleanBlock, {
                  displayMode: true,
                  throwOnError: false,
                  strict: false
                });
                return (
                  <div key={idx} style={{minWidth: 'max-content', width: '100%'}} dangerouslySetInnerHTML={{ __html: html }} />
                );
              } catch (err) {
                return (
                  <div key={idx} className="text-red-500 text-sm">
                    {err instanceof Error ? err.message : '渲染错误'}
                  </div>
                );
              }
            })
          )}
        </div>
      </div>
    </div>
  );
} 