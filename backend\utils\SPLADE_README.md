# SPLADE 稀疏向量生成器

基于 SPLADE (Sparse Lexical and Expansion) 模型的稀疏向量生成类，用于生成适合存储在 Zilliz 等向量数据库中的稀疏向量表示。

## 特性

- 🚀 **高效稀疏向量生成**: 使用预训练的 SPLADE 模型生成高质量稀疏向量
- 📊 **原生稀疏格式**: 输出字典格式 `{token_id: weight}`，直接兼容 Zilliz 稀疏向量字段
- 🔍 **可解释性**: 支持将稀疏向量解码为可读的 token 和权重
- 📈 **统计分析**: 提供稀疏度、维度分布等统计信息
- 🔄 **异步支持**: 提供同步和异步接口

## 安装依赖

```bash
pip install transformers torch scipy
```

## 快速开始

### 基本使用

```python
from utils.llm import BaseSparseEmbedding, ModelSelector

# 初始化 SPLADE 模型
sparse_model = BaseSparseEmbedding(**ModelSelector.splade_default)

# 生成稀疏向量
texts = ["The weather is lovely today.", "量子机器学习算法"]
sparse_vectors = sparse_model.embedding(texts)

# 查看结果
for text, sparse_vec in zip(texts, sparse_vectors):
    print(f"文本: {text}")
    print(f"稀疏向量维度数: {len(sparse_vec)}")
    
    # 解码权重最高的 token
    decoded = sparse_model.decode_sparse_vector(sparse_vec, top_k=5)
    print(f"权重最高的 5 个 token: {decoded}")
```

### 异步使用

```python
import asyncio

async def async_example():
    sparse_model = BaseSparseEmbedding(**ModelSelector.splade_v3)
    texts = ["Machine learning", "Deep learning"]
    
    sparse_vectors = await sparse_model.aembedding(texts)
    return sparse_vectors

# 运行异步函数
sparse_vectors = asyncio.run(async_example())
```

## 可用模型

在 `ModelSelector` 中预定义了几个 SPLADE 模型：

```python
# 默认模型（推荐）
ModelSelector.splade_default = {
    "model_name": "naver/splade-cocondenser-ensembledistil", 
    "device": "cpu"
}

# SPLADE v3 模型
ModelSelector.splade_v3 = {
    "model_name": "naver/splade-v3", 
    "device": "cpu"
}

# DistilBERT 版本
ModelSelector.splade_distilbert = {
    "model_name": "naver/splade-cocondenser-selfdistil", 
    "device": "cpu"
}
```

## Zilliz 集成

生成的稀疏向量可以直接存储到 Zilliz 向量数据库：

```python
# 生成稀疏向量
documents = ["Document 1", "Document 2"]
sparse_vectors = sparse_model.embedding(documents)

# 准备 Zilliz 数据
zilliz_data = []
for i, (doc, sparse_vec) in enumerate(zip(documents, sparse_vectors)):
    zilliz_data.append({
        "id": i,
        "text": doc,
        "sparse_vector": sparse_vec,  # 直接使用字典格式
        "metadata": {"source": "example"}
    })

# 插入到 Zilliz（需要配置连接）
# collection.insert(zilliz_data)
```

## API 参考

### BaseSparseEmbedding

#### 初始化参数

- `model_name` (str): Hugging Face 上的 SPLADE 模型名称
- `device` (str): 运行设备，'cpu' 或 'cuda'

#### 主要方法

##### `embedding(text_list, **kwargs) -> list[dict]`

生成文本列表的稀疏向量表示。

**参数:**
- `text_list` (list[str]): 需要编码的文本列表

**返回:**
- `list[dict]`: 每个文本对应的稀疏向量字典 `{token_id: weight}`

##### `aembedding(text_list, **kwargs) -> list[dict]`

异步版本的 `embedding` 方法。

##### `decode_sparse_vector(sparse_dict, top_k=10) -> list[tuple]`

将稀疏向量解码为可读格式。

**参数:**
- `sparse_dict` (dict): 稀疏向量字典
- `top_k` (int): 返回权重最高的前 k 个 token

**返回:**
- `list[tuple]`: `[(token, weight), ...]` 按权重降序排列

##### `get_sparsity_stats(sparse_embeddings) -> dict`

计算稀疏向量的统计信息。

**返回:**
- `dict`: 包含词汇表大小、平均非零维度、稀疏度等统计信息

## 性能特点

- **稀疏度**: 通常 > 99%，大大减少存储空间
- **维度数**: 词汇表大小（通常 30,000+）
- **非零维度**: 平均 100-300 个活跃维度
- **处理速度**: CPU 上约 1-5 文档/秒（取决于文本长度和硬件）

## 注意事项

1. **内存使用**: 模型加载需要约 500MB-1GB 内存
2. **首次运行**: 首次使用会下载模型文件，需要网络连接
3. **设备选择**: GPU 可以显著提升处理速度，设置 `device="cuda"`
4. **文本长度**: 输入文本会被截断到 512 个 token

## 示例脚本

运行完整示例：

```bash
cd backend/utils
python splade_example.py
```

这将运行基本使用、Zilliz 集成和性能测试示例。

## 技术原理

SPLADE 使用 Masked Language Modeling (MLM) 头来生成稀疏向量：

1. **输入处理**: 文本通过 BERT tokenizer 转换为 token
2. **模型推理**: MLM 模型输出每个位置的词汇表概率分布
3. **聚合策略**: 使用 max pooling 聚合所有位置的预测
4. **激活函数**: 应用 ReLU 和 log(1+x) 变换
5. **稀疏化**: 保留非零权重，形成稀疏向量

公式: `w_j = max_{i ∈ t} log(1 + ReLU(w_{ij}))`

其中 `w_{ij}` 是位置 i 对 token j 的预测权重。
