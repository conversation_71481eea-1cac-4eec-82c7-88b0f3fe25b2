[{"primary_key": "3485004", "vector": [], "sparse_vector": [], "title": "Flare: Practical Viewport-Adaptive 360-Degree Video Streaming for Mobile Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Xiao", "<PERSON>"], "summary": "Flare is a practical system for streaming 360-degree videos on commodity mobile devices. It takes a viewport-adaptive approach, which fetches only portions of a panoramic scene that cover what a viewer is about to perceive. We conduct an IRB-approved user study where we collect head movement traces from 130 diverse users to gain insights on how to design the viewport prediction mechanism for Flare. We then develop novel online algorithms that determine which spatial portions to fetch and their corresponding qualities. We also innovate other components in the streaming pipeline such as decoding and server-side transmission. Through extensive evaluations (~400 hours' playback on WiFi and ~100 hours over LTE), we show that Flare significantly improves the QoE in real-world settings. Compared to non-viewport-adaptive approaches, Flare yields up to 18x quality level improvement on WiFi, and achieves high bandwidth reduction (up to 35%) and video quality enhancement (up to 4.9x) on LTE.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241565"}, {"primary_key": "3485005", "vector": [], "sparse_vector": [], "title": "Demo: Tile-Based Viewport-Adaptive Panoramic Video Streaming on Smartphones.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Xiao", "<PERSON>"], "summary": "Flare is a practical system for streaming 360-degree videos on smartphones. It takes a viewport-adaptive approach, which fetches only portions of a panoramic scene that cover what a viewer is about to perceive. Flare consists of a novel framework for the end-to-end streaming pipeline, introduces innovative streaming algorithms, and brings numerous system-level optimizations. In our demo, we will show that <PERSON><PERSON><PERSON> substantially outperforms traditional viewport-agnostic streaming algorithms in terms of the video quality. We will also invite the audience to use Flare to watch attractive 360-degree videos.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267715"}, {"primary_key": "3485007", "vector": [], "sparse_vector": [], "title": "Poster: Feasibility of Desynchronization Attack in LTE/SAE Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The long term evolution/system architecture evolution (LTE/SAE) networks have been shown vulnerable to desynchronization attack. In this attack, the adversary compromises a legitimate evolved NodeB and gains access to the encryption keys. The essential feasibility conditions have not been considered in the existing work. Hence, in this paper, we propose the conditions essential for the occurrence of DA. Through numerical results, we evaluate the feasibility of DA for various network scenarios.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267773"}, {"primary_key": "3485011", "vector": [], "sparse_vector": [], "title": "Invited Keynote by Mr. <PERSON><PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Reliance has become the first Indian company to record PBDIT of over US$ 10 billion with each of our key businesses - Refining, Petrochemicals, Retail and Digital Services achieving record earnings performance. Substantial synergies, productivity gains and production growth in our energy and materials business has allowed us to perform at very competitive levels despite the uptrend in oil prices through the year. We have established strong foundations in retailing and digital services business with world-class supply chain management and network infrastructure which will serve our customers well. It is very heartening to see the traction our service offerings are gaining, with discerning Indian consumers. The growing Indian market provides exciting opportunities to scale-up these businesses and maximize long-term shareholder value in the coming years.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241585"}, {"primary_key": "3485012", "vector": [], "sparse_vector": [], "title": "Cross-Frequency Communication: Near-Field Identification of UHF RFIDs with WiFi!", "authors": ["Z<PERSON><PERSON> An", "<PERSON><PERSON><PERSON><PERSON> Lin", "<PERSON><PERSON>"], "summary": "Recent advances in Cross-Technology Communication (CTC) have improved efficient cooperation among heterogeneous wireless devices. To date, however, even the most effective CTC systems require these devices to operate in the same ISM band (e.g., 2.4 GHz) because of the conventional wisdom that wireless transceivers with different (fundamental) frequencies cannot communicate with one another. Our work, which is called TiFi, challenges this belief by allowing a 2.4 GHz WiFi receiver (e.g., a smartphone) to identify UHF RFID tags, which operates at the spectrum between 840 - 920 MHz. TiFi does not require changing current smartphones or tags. Instead, it leverages the underlying harmonic backscattering of tags to open a second channel and uses it to communicate with WiFi receivers. We design and implement TiFi with commodity WiFi chipsets (e.g., Broadcom BCM43xx, Murata KM6D280 40, and Qualcomm WCN3990). Our comprehensive evaluation shows that TiFi allows WiFi receivers to identify UHF RFID tags within the range of 2 m and with a median goodput of 95%, which is comparable to today's mobile RFID readers.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241569"}, {"primary_key": "3485013", "vector": [], "sparse_vector": [], "title": "Demo: Near-Field Identification of UHF RFIDs with WiFi!", "authors": ["Z<PERSON><PERSON> An", "<PERSON><PERSON><PERSON><PERSON> Lin", "<PERSON><PERSON>"], "summary": "Recent advances in Cross-Technology Communication (CTC) have improved efficient cooperation among heterogeneous wireless devices. To date, however, even the most effective CTC systems require these devices to operate in the same ISM band (eg. 2.4GHz) because of the conventional wisdom that wireless transceivers with different (fundamental) frequencies cannot communicate with one another. In this demo, we present a practical CTC application, called øursystem, allowing a 2.4GHz WiFi receiver (eg. a smartphone) to identify UHF RFID tags, which operates at the spectrum between 840~920MHz. øursystem leverages the underlying harmonic backscattering of tags to open a second channel and uses it to communicate with WiFi receivers. We design and implement øursystem with commodity WiFi chipsets. Our comprehensive evaluation shows that øursystem allows WiFi receivers to identify UHF RFID tags within the range of $2$ m and with a median goodput of 95%, which is comparable to today's mobile RFID readers.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267718"}, {"primary_key": "3485014", "vector": [], "sparse_vector": [], "title": "Poster: Hybrid Android Malware Detection by Combining Supervised and Unsupervised Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Permissions and the network traffic features are the widely used attributes in static and dynamic Android malware detection respectively. However, static permissions cannot detect stealthy malware with update attacks capability, while dynamic network traffic cannot detect the malware samples without network connectivity. Hence, there is a need to build a hybrid model combining both these attributes. In this work, we propose a hybrid malware detector that examines both the permissions and the traffic features to detect malicious Android samples. The proposed approach is based on the combination of Supervised Learning (KNN Algorithm) and Unsupervised Learning (K-Medoids Algorithm). Experimental results demonstrate that hybrid approach gives the overall detection accuracy of 91.98%, better than static and dynamic detection accuracies of 71.46% and 81.13% respectively.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267768"}, {"primary_key": "3485016", "vector": [], "sparse_vector": [], "title": "Session details: Living on the Edge: Mobile Systems at the Network&apos;s Edge.", "authors": ["<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485017", "vector": [], "sparse_vector": [], "title": "Session details: Keynote Address III.", "authors": ["<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485018", "vector": [], "sparse_vector": [], "title": "Edge Computing with ParaDrop Tutorial.", "authors": ["<PERSON><PERSON>"], "summary": "This half-day tutorial will explore edge computing through hands-on development activities using a physical edge computing platform. With the recent trends in the networking ecosystem-the rise of IoT devices, high bandwidth wireless, and powerful, energy-efficient, and inexpensive computation-edge computing is a promising technology for highly interactive and immersive environments. This tutorial aims to promote awareness of the possibilities of edge computing in general and introduce attendees to the tools that they can use to begin exploring this space. The tutorial's activities will give attendees hands-on experience with the ParaDrop edge computing platform developed at the University of Wisconsin-Madison. We hope to impart working knowledge about edge computing and our vision for its future. Recent developments in home wireless networks such as the proliferation of connected devices (e.g. Internet-of-Things) and gigabit wireless (e.g. 802.11ac) create an environment where edge computing can truly enhance applications. The availability of low latency and high bandwidth at the network edge but not necessarily end-to-end to the cloud, can enable interesting new applications involving video (e.g. augmented reality), low latency sensor-actuator coordination, and other public safety or educational applications. However, before such applications emerge, there needs to be a platform available for them. We argue that the availability of widespread platforms and standards for edge computing will unleash a new wave of innovation much like the creation of app markets for mobile devices. The hardware exists, e.g. in the form of millions of always-on Wi-Fi routers in homes and businesses, but it is not very programmable. With our tutorial, we aim to foster awareness of the possibilities that exist with edge computing and promote the advancement of research toward good standards for its realization. We introduce the ParaDrop platform as an open source demonstration of our vision for edge computing. We have been single-mindedly focused on researcher and developer needs in creating the ParaDrop platform. As such, applications that are already written to be run as cloud services can be easily modified to run on the ParaDrop platform in order to benefit from running at the network edge. Applications are not required to be written in a highly specialized language, and applications are able to leverage the rapidly growing software ecosystem surrounding Docker. Our tutorial will give researchers a hands-on experience with the platform and knowledge that they will be able to use beyond the workshop.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3270097"}, {"primary_key": "3485021", "vector": [], "sparse_vector": [], "title": "Session details: Panel Discussion.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485022", "vector": [], "sparse_vector": [], "title": "MobiCom&apos;18 Panel: Hammer &amp; Nail vis-a-vis AI / ML Applications to Networked Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Artificial Intelligence (AI) and Machine Learning (ML) approaches, well known from IT disciplines, are beginning to excite the networking and networked systems community. Of late, we are seeing a huge excitement about applying AI and ML to networked systems. Is this merely a hype? Are there use cases and genuine applications that could lead to real deployment and practical solutions? What are the key challenges in applying AI and ML to networked systems? Can researchers and practitioners in communication networks and networked systems tap into machine learning and AI techniques to optimize network architecture, control and management, leading to increased automation in network operations? Can researchers and practitioners in the AI community explore synergy with networking researchers to optimize network architecture and design? The above are some of the questions that would be addressed during the panel discussion. The objective of the panel discussion would be to tap the minds of the global experts in order to understand the merits and limitations and the future landscape in the intersection of networking/networked systems and AI/ML.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241590"}, {"primary_key": "3485026", "vector": [], "sparse_vector": [], "title": "Poster: Scalable Network Slicing Architecture for 5G.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The diversified use cases of next-generation mobile networks can be realized by the key concept of Network Slicing that enables mobile network operators to slice a single physical network into multiple virtual network instances optimized to specific services and business goals. Scaling of network slices is required to cope with the resources needed for peak traffic demand. In this paper, we demonstrate scaling of network slices based on the type of network slice such as enhanced Mobile Broadband (eMBB), massive Machine Type Communication (mMTC) in order to ensure Service Level Agreement (SLA) guarantees of the network slices with the help of our proposed Network Slicing Profiler (NSP) and Network Slice Scaling Function (NSSF) in an ETSI MANO based network slicing framework.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267762"}, {"primary_key": "3485027", "vector": [], "sparse_vector": [], "title": "Demo: A Software-Defined Radio for Wireless Brain Implants Network.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Arto V. Nurmikko", "<PERSON><PERSON>", "<PERSON>"], "summary": "A novel Brain-Machine Interface (BMI) system based on a distributed network of implantable wireless sensors was proposed. Small CMOS \"Neurograin\" chips (0.5x0.5 mm2) with on-chip antenna are designed to harvest near-field RF energy at ~1 GHz, and backscatter 10 Mbps BPSK modulated data asynchronously and periodically. A \"Skinpatch\" software-defined radio (SDR) receiver is realized on a commercial USRP running GNU Radio programs. It down-converts the reflected waves from the Neurograins and performs data recovery. In this BMI prototype demonstration, 32 Neurograins will be wirelessly powered, while a Skinpatch USRP will recover their backscattered packets in real-time.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267727"}, {"primary_key": "3485028", "vector": [], "sparse_vector": [], "title": "The Science of Social Cyber-Security.", "authors": ["<PERSON>"], "summary": "Social Cyber-Security is an emerging scientific and engineering area. It is an applied field focused on the science to characterize, understand, and forecast cyber-mediated changes in human behavior and activity, and the related social, cultural, economic and political outcomes. From the engineering side it is concerned with building the cyber-infrastructure needed for society to persist in its essential character in a cyber-mediated information environment under changing conditions, in the presence of actual or imminent social cyber-threats. Research in social cyber-security has demonstrated that social media and personalized data assistants are critical technologies that affect the ways humans navigate this cyber-mediated information space, and the way they interact and engage in discussions. This impact is complex and is related to the human socio-cognitive response, engineered features of the technologies, and the policy/legal environment in which humans and technology interact. The changing nature of interaction in the cyber-mediated information environment are creating new scientific and policy challenges that those in social cyber-security must address. This talk will describe the nature, promise of, and challenges in social cyber-security. The role of network science and AI, as to of the core methodologies, in addressing these challenges is addressed. Illustrative examples will be drawn from areas such as disaster response, state stability, and the distribution of false information.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241587"}, {"primary_key": "3485029", "vector": [], "sparse_vector": [], "title": "Chip-to-chip RF Communications and Power Delivery via On-chip Antennas.", "authors": ["<PERSON>"], "summary": "This tutorial provides students with a basic understanding of the challenges involved in designing RF-powered RF-connected integrated circuits (ICs) with on-chip antennas (OCAs). The tutorial begins by discussing the potential cost and performance advantages of creating ICs with OCAs. The design of ICs in which the antenna and conventional circuitry are all fabricated on the same substrate offers advantages in terms of size, weight and cost over conventional RFID/sensor systems in which an IC is assembled onto an antenna substrate; however, it is challenging because the limited size of the antenna set by the die size and its proximity to a resistive ground plane (the IC substrate) both decrease the RF energy that can be harvested to operate the on-chip circuitry. In this Tutorial, we will apply simplifying approximations in order to develop basic equations relating the size and geometry of the antennas to the ability of the OCA-IC to harvest ambient RF energy; and, we will validate those equations with electromagnetic field simulations. The tutorial will also examine the challenging issue of matching the antenna to the on-chip electronics in a way that maximizes the power that can be extracted from an external RF signal. The Tutorial will consider operation across a range of frequencies and will explore both in-plane spiral antennas and out-of-plane helical antennas. The Tutorial will conclude with an analysis of the case when an OCA-ICs is integrated into medicines in order to provide secure validation of medicinal pills and capsules.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3270100"}, {"primary_key": "3485030", "vector": [], "sparse_vector": [], "title": "Dynamic Network Analytics: Tutorial Outline.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Network analytics are widely used in many fields. Increasingly though, the networks of interest are high dimensional. Rather than just focusing on the traditional who is interaction with whom networks, modern network analysis examines the who, what, where, when and why ecology of relations. For example, studies of organizations might look at the social network within the organization as well as the task assignment and knowledge network. Studies using Twitter frequently look at the mentions network at the same time as the hashtag network. Making network science actionable typically involves answering questions about where the network is concentrated or how the network varies over time. This tutorial will cover the basics of Network analytics and demonstrate the use of ORA to support social media analytics using high dimensional network analytics and visualization for social media networks, semantic networks, geo-spatial networks, and dynamic networks. It will cover the basics of visualizing and analyzing time varying networks and spatially positioned networks. Permission to make digital or hard copies of part or all of this work for personal or classroom use is granted without fee provided that copies are not made or distributed for profit or commercial advantage and that copies bear this notice and the full citation on the first page. Copyrights for third-party components of this work must be honored. For all other uses, contact the Owner/Author. ORA is a powerful network analysis and visualization tool (<PERSON><PERSON>, 2017). ORA supports the assessment of standard social network data, organizational network data, high-dimensional network data, meta-network data, geo-spatial network data, and dynamic network data.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3270099"}, {"primary_key": "3485031", "vector": [], "sparse_vector": [], "title": "Ghostbuster: Detecting the Presence of Hidden Eavesdroppers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jiachen Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper explores the possibility of detecting the hidden presence of wireless eavesdroppers. Such eavesdroppers employ passive receivers that only listen and never transmit any signals making them very hard to detect. In this paper, we show that even passive receivers leak RF signals on the wireless medium. This RF leakage, however, is extremely weak and buried under noise and other transmitted signals that can be 3-5 orders of magnitude larger. Hence, it is missed by today's radios. We design and build Ghostbuster, the first device that can reliably extract this leakage, even when it is buried under ongoing transmissions, in order to detect the hidden presence of eavesdroppers. Ghostbuster does not require any modifications to current transmitters and receivers and can accurately detect the eavesdropper in the presence of ongoing transmissions. Empirical results show that Ghostbuster can detect eavesdroppers with more than 95% accuracy up to 5 meters away.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241580"}, {"primary_key": "3485032", "vector": [], "sparse_vector": [], "title": "Surface MIMO: Using Conductive Surfaces For MIMO Between Small Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As connected devices continue to decrease in size, we explore the idea of leveraging everyday surfaces such as tabletops and walls to augment the wireless capabilities of devices. Specifically, we introduce Surface MIMO, a technique that enables MIMO communication between small devices via surfaces coated with conductive paint or covered with conductive cloth. These surfaces act as an additional spatial path that enables MIMO capabilities without increasing the physical size of the devices themselves. We provide an extensive characterization of these surfaces that reveal their effect on the propagation of EM waves. Our evaluation shows that we can enable additional spatial streams using the conductive surface and achieve average throughput gains of 2.6-3x for small devices. Finally, we also leverage the wideband characteristics of these conductive surfaces to demonstrate the first Gbps surface communication system that can directly transfer bits through the surface at up to 1.3 Gbps.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241562"}, {"primary_key": "3485033", "vector": [], "sparse_vector": [], "title": "Type2Motion: Detecting Mobility Context from Smartphone Typing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Bivas Mitra", "<PERSON><PERSON>"], "summary": "Recent context detection techniques in smartphones leverage on the embedded motion sensors, which in turn increases the potential of side-channel attacks. We in this paper propose an alternative modality for obtaining mobility context using smartphone keyboard interaction patterns using a personalized framework. Experimental results show that the framework can predict the mobility context at an average F1 score (both micro and macro) greater than 0.6 across all subjects.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267746"}, {"primary_key": "3485034", "vector": [], "sparse_vector": [], "title": "Session details: Keynote Address II.", "authors": ["<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485035", "vector": [], "sparse_vector": [], "title": "EasyGO: A Rapid Indoor Navigation and Evacuation System Using Smartphones through Internet of Things Technologies.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper proposes a smartphone-based indoor navigation and evacuation system, called EasyGO, to minimize moving time for mobile users through Internet of Things (IoT) technologies. In normal time, EasyGO can estimate the density of indoor people in each area and determine the moving speeds to pass through different areas. Based on the determined moving speed of each area, an indoor navigation path can be planned to provide the shortest moving time for EasyGO users. In emergent time, EasyGO can accurately estimate the escaping time for each EasyGO user by considering the capacity and length of passageways, the capacity of exits, and the current distribution and parallel moving of indoor people. Based on the estimated escaping time, EasyGO can evenly distribute the evacuation load among exits and alleviate the congestion of all passageways and exits to minimize the total escaping time. An Android-based prototype with iBeacon indoor localization is implemented to verify the feasibility of our EasyGO system.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267721"}, {"primary_key": "3485036", "vector": [], "sparse_vector": [], "title": "Poster: An SDN Based Content Cache at the WiFi Edge.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We describe the current version of Wi-Cache, a SDN framework for caching at the WiFi edge. Wi-Cache is motivated by the belief that edge caching technologies are needed to augment emerging network technologies to meet the increasing (volume, quality, and variety) demand for content, which is itself changing its characteristics significantly. Wi-Cache is being used to test new ideas for edge caching. Specifically, Wi-Cache is a framework for edge caching which allows caching and delivery of content on WiFi APs. Apart from a network induced handoff of clients, it allows communication between the APs for content delivery. We have also developed an API that is exposed for implementation of algorithms for content delivery and placement, and cache replacement.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267752"}, {"primary_key": "3485040", "vector": [], "sparse_vector": [], "title": "Poster: Exploring Visible Light Communication System using RTS/CTS Mechanism for Mobile Environment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work presents a software-centric visible light communication (VLC) system in full duplex mode with CSMA/CA and RTS-CTS in mobile environment. We focus on channel access mechanism problems in the same environment. To solve these we modify Distributed Coordination function (DCF) by adding ambient light measurement slot. The result shows modified DCF can handle the problems.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267765"}, {"primary_key": "3485041", "vector": [], "sparse_vector": [], "title": "Demo: Combating Caller ID Spoofing on 4G Phones Via CEIVE.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present the demonstration of CEIVE (Callee-only inference and verification), an effective and practical defense against caller ID spoofing. CEIVE is a victim callee only solution without requiring additional infrastructure support or changes on telephony systems; It is ready to deploy and easy to use. Given an incoming call, CEIVE leverages a callback session and its associated call signaling observed at the phone to infer the call state of the other party. It further compares with the anticipated call state of the incoming call, thus quickly verifying whether the incoming call comes from the originating number or not. In this demo, we demonstrate CEIVE installed on Android phones combating both basic and advanced caller ID spoofing attacks.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267725"}, {"primary_key": "3485042", "vector": [], "sparse_vector": [], "title": "CEIVE: Combating Caller ID Spoofing on 4G Mobile Phones Via Callee-Only Inference and Verification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON>"], "summary": "Caller ID spoofing forges the authentic caller identity, thus making the call appear to originate from another user. This seemingly simple attack technique has been used in the growing telephony frauds and scam calls, resulting in substantial monetary loss and victim complaints. Unfortunately, caller ID spoofing is easy to launch, yet hard to defend; no effective and practical defense solutions are in place to date. In this paper, we propose CEIVE (Callee-only inference and verification), an effective and practical defense against caller ID spoofing. It is a victim callee only solution without requiring additional infrastructure support or changes on telephony systems. We formulate the design as an inference and verification problem. Given an incoming call, CEIVE leverages a callback session and its associated call signaling observed at the phone to infer the call state of the other party. It further compares with the anticipated call state, thus quickly verifying whether the incoming call comes from the originating number. We exploit the standardized call signaling messages to extract useful features, and devise call-specific verification and learning to handle diversity and extensibility. We implement CEIVE on Android phones and test it with all top four US mobile carriers, one landline and two small carriers. It shows 100% accuracy in almost all tested spoofing scenarios except one special, targeted attack case.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241573"}, {"primary_key": "3485043", "vector": [], "sparse_vector": [], "title": "Magneto: Leveraging Magnetic Field Changes for Inferring Smartphone App Usage.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Side-channel attacks, which exploit deficiencies in the implementations of theoretically secure systems, have been known to take a variety of forms on the mobile platforms. In this work, we present Magneto, a magnetic field based app classification mechanism. Magneto captures the Hall effect due to energy consumption by different components in a smartphone, and fingerprints apps based on data captured with a Hall sensor, and a phone magnetometer. We demonstrate that our mechanism can identify magnetic field changes due to varying levels of energy consumption. We further show that Magento can not only classify between apps in the same scenario, but also can tell apart scenarios when the phone is being charged (with an AC adapter, wireless charger, or powerbank) or not. We perform validation experiments with 5 different apps, and achieve ~85% accuracy with 3 Android apps, subject to 3 different charging scenarios.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267772"}, {"primary_key": "3485044", "vector": [], "sparse_vector": [], "title": "Poster: A Raspberry Pi Based Data-Centric MAC for Robust Multicast in Vehicular Network.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Data-centric networks provide content instead of address (what vs. where) based communication primitives, and have been argued to be the proper candidate for data dissemination in high mobility vehicular networks (e.g., delivering road side accident video clips to affected drivers in both directions). However, current Medium Access Control (MAC) layers filter incoming frames based on destination addresses, not content. The data-centric network community has resorted to MAC broadcast, with high and greatly varying frame loss rates. We propose V-MAC, a data-centric MAC layer that filters frames by content. It supports one to many multicast at MAC level, and ensures a uniform and controllable small frame loss rate across all receivers, despite their varying reception qualities. We have created a V-MAC prototype using Raspberry Pis and WiFi dongles. Experiments under extremely noisy environment show that it reduces frame loss from 50% (broadcast) to less than 10%, and consistently among multiple receivers.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267759"}, {"primary_key": "3485046", "vector": [], "sparse_vector": [], "title": "NestDNN: Resource-Aware Multi-Tenant On-Device Deep Learning for Continuous Mobile Vision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile vision systems such as smartphones, drones, and augmented-reality headsets are revolutionizing our lives. These systems usually run multiple applications concurrently and their available resources at runtime are dynamic due to events such as starting new applications, closing existing applications, and application priority changes. In this paper, we present NestDNN, a framework that takes the dynamics of runtime resources into account to enable resource-aware multi-tenant on-device deep learning for mobile vision systems. NestDNN enables each deep learning model to offer flexible resource-accuracy trade-offs. At runtime, it dynamically selects the optimal resource-accuracy trade-off for each deep learning model to fit the model's resource demand to the system's available runtime resources. In doing so, NestDNN efficiently utilizes the limited resources in mobile vision systems to jointly maximize the performance of all the concurrently running applications. Our experiments show that compared to the resource-agnostic status quo approach, NestDNN achieves as much as 4.2% increase in inference accuracy, 2.0x increase in video frame processing rate and 1.7x reduction on energy consumption.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241559"}, {"primary_key": "3485047", "vector": [], "sparse_vector": [], "title": "Poster: Maintaining UAV Stability using Low-Power WANs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Future urban spaces are expected to see Unmanned Aerial Vehicles (UAVs) deployed for wide-ranging applications including product delivery, imaging and search-and-rescue. Yet today's UAVs rely critically on inertial sensors and GPS to remain stable in-flight, meaning they struggle to penetrate urban canyons where GPS is unavailable. This poster presents LoRaTilt, a system that maintains stability of a UAV using a Low-Power Wide-Area Network (LP-WAN) transmitter. We mount a light-weight and ten-year battery-powered LP-WAN transmitter on a UAV and process its signals from base stations up to hundreds of meter away. We demonstrate how our system estimates and corrects for UAV drift in GPS-denied settings with minimal impact on the cost and flight life of a UAV. Our proof-of-concept experiments show a promising median accuracy of 1.2326 mm and 0.0164 radians in estimating the displacement and orientation of a drone with a LoRa LP-WAN radio.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267747"}, {"primary_key": "3485048", "vector": [], "sparse_vector": [], "title": "Poster: Analyzing Bitrates in Modern Wi-Fi Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The IEEE 802.11 standard has become the dominant protocol for Wireless Local Area Networks (WLANs). In a span of 20 years, the speed of these networks has increased from 1 Mbps to more than 1 Gbps. Today's Wi-Fi networks may consist of a variety of client devices, ranging from slow legacy 802.11a/b/g to modern and fast 802.11n/ac devices. We describe preliminary findings from a large-scale study obtained from 448 Google Wifi and Google OnHub access points with 2,975 clients. We focus on characterizing the maximum achievable bitrate of heterogeneous wireless links. We also determine the average physical-layer bitrate used on the down link (AP to client) and compare it with the maximum supported bitrate. We find that about 75% of 802.11n and 50% of 802.11ac client devices operate at 75% of their maximum or more and that the bitrates of the remaining devices can be very far from their maximum. These low bitrates could significantly reduce the throughput of high-bitrate devices.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267774"}, {"primary_key": "3485049", "vector": [], "sparse_vector": [], "title": "Conductive Inkjet Printed Passive 2D TrackPad for VR Interaction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile virtual reality (VR) headsets, such as Google Cardboard and Samsung GearVR, can reuse a smartphone as near-eye display to create immersive experience. But such devices barely support any user interaction, even for simple tasks such as menu selection and single-character input. In this paper, we design Inkput, a simple passive interface attached to the unexploited backside of the headset to enable touch sensing. Inkput is a piece of paper substrate with carbon ink patterns printed atop. It leverages the column of electrodes near the edge of the smartphone touchscreen to sense multi-touch on the 2D space, and is even able to locate finger hovering. Our experiments demonstrate that Inkput can precisely detect touch positions with mm-level precision. Our case studies in actual VR applications also verify that Inkput can support common VR interactions and can even outperform high-end handheld controllers in terms of efficiency.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241546"}, {"primary_key": "3485051", "vector": [], "sparse_vector": [], "title": "HISC NEMO: High Scalability in Nested Network Mobility Using Hierarchical Attachment Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The continuous decrease in the cost of mobile devices and mobile Internet has triggered an exponential growth of overall IP devices, which has consequently increased the demand for network mobility (NEMO), which is required to ensure seamless connectivity of the mobile devices with the Internet on the go. To support this growing demand, the concept of nested-NEMO has evolved over the past few years. However, due to the inherent architecture of nested-NEMO, it is inefficient in terms of hand-off delay and throughput which hinder it to deploy as a highly scalable network. In this paper, we address the scalability issue of nested-NEMO and propose a hierarchical node attachment process for the mobile devices. The performance of the proposed mechanism has been evaluated where we computed the hand-off delay and throughput and observed that the proposed mechanism supports a considerable degree of scalability as compared to the existing nested network mobility architecture.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267767"}, {"primary_key": "3485054", "vector": [], "sparse_vector": [], "title": "Multi-Stream Beam-Training for mmWave MIMO Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Multi-stream 60 GHz communication has the potential to achieve data rates up to $100$ Gbps via multiplexing multiple data streams. Unfortunately, establishing multi-stream directional links can be a high overhead procedure as the search space increases with the number of spatial streams and the product of AP-client beam resolution. In this paper, we present MUlti-stream beam-Training for mm-wavE networks (MUTE) a novel system that leverages channel sparsity, GHz-scale sampling rate, and the knowledge of mm-Wave RF codebook beam patterns to construct a set of candidate beams for multi-stream beam steering. In 60 GHz WLANs, the AP establishes and maintains a directional link with every client through periodic beam training. MUTE repurposes these beam acquisition sweeps to estimate the Power Delay Profile (PDP) of each beam with zero additional overhead. Coupling PDP estimates with beam pattern knowledge, MUTE selects a set of candidate beams that capture diverse or ideally orthogonal paths to obtain maximum stream separability. Our experiments demonstrate that MUTE achieves 90% of the maximum achievable aggregate rate while incurring only 0.04% of exhaustive search's training overhead.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241556"}, {"primary_key": "3485055", "vector": [], "sparse_vector": [], "title": "Poster: Effectiveness of Deep Neural Network Model in Typing-based Emotion Detection on Smartphones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Bivas Mitra", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Typing characteristics on smartphones can provide clues for emotion detection. Collecting large volumes of typing data is also easy on smartphones. This motivates the use of Deep Neural Network (DNN) to determine emotion states from smartphone typing. In this work, we developed a DNN model based on typing features to predict four emotion states (happy, sad, stressed, relaxed) and investigate its performance on a smartphone. The evaluation of the model in a 3-week study with 15 participants reveals that it can reliably detect emotions with an average accuracy of 80% with peak CPU utilization less than 15%.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267761"}, {"primary_key": "3485056", "vector": [], "sparse_vector": [], "title": "The Future of Wireless and What it will Enable.", "authors": ["<PERSON>"], "summary": "Wireless technology has enormous potential to change the way we live, work, and play over the next several decades. Future wireless networks will support 100 Gbps communication between people, devices, and the \"Internet of Things,\" with high reliability and uniform coverage indoors and out. The shortage of spectrum to support such systems will be alleviated by advances in massive MIMO and mmW technology as well as cognitive radios. Wireless technology will also enable smart and energy-efficient homes and buildings, automated highways and skyways, and in-body networks for monitoring, analysis and treatment of medical conditions. Breakthrough energy-efficiency architectures, algorithms and hardware will allow wireless networks to be powered by tiny batteries, energy-harvesting, or over-the-air power transfer. Finally, new communication systems based on biology and chemistry to encode bits will enable a wide range of new micro and macroscale applications. There are many technical challenges that must be overcome in order to make this vision a reality. This talk will describe what the wireless future might look like along with some of the innovations and breakthroughs required to realize this vision.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241591"}, {"primary_key": "3485058", "vector": [], "sparse_vector": [], "title": "Poster: Low Cost Platform Design for Pollution Measurement in Delhi-NCR using Vehicle-Mounted Sensors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This poster describes a low-cost and robust embedded platform, designed for vehicle mounted sensing of particulate matter (PM2.5 and PM10). The prototype is specifically designed to be mounted on the Delhi Integrated Multi-Modal Transit System (DIMTS) buses. Movement of the buses adds noise to pollution data. Error in GPS measurement causes issues in detecting moving vs. stationary state of the buses, useful to filter out noisy pollution data collected in the moving state. Intermittent cellular network connectivity causes frequent disconnects with the remote server. Our prototype is designed to handle such real world deployment challenges. Pilot deployment with this platform is currently ongoing. Preliminary data analysis from the pilot deployment will be discussed as part of the poster presentation, along with demonstration of the prototype sensor platform. This hardware prototype has the potential of creating locality wise, dense air pollution data providing crucial insights into the causes of air pollution.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267779"}, {"primary_key": "3485060", "vector": [], "sparse_vector": [], "title": "Session details: We are the Engineers: Mobile Systems and Networking.", "authors": ["<PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485061", "vector": [], "sparse_vector": [], "title": "FoggyCache: Cross-Device Approximate Computation Reuse.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile and IoT scenarios increasingly involve interactive and computation intensive contextual recognition. Existing optimizations typically resort to computation offloading or simplified on-device processing. Instead, we observe that the same application is often invoked on multiple devices in close proximity. Moreover, the application instances often processsimilar contextual data that map to thesame outcome. In this paper, we proposecross-device approximate computation reuse, which minimizes redundant computation by harnessing the \"equivalence'' between different input values and reusing previously computed outputs with high confidence. We devise adaptive locality sensitive hashing (A-LSH) and homogenized k nearest neighbors (H-kNN). The former achieves scalable and constant lookup, while the latter provides high-quality reuse and tunable accuracy guarantee. We further incorporate approximate reuse as a service, called \\name, in the computation offloading runtime. Extensive evaluation shows that, when given 95% accuracy target, \\name\\ consistently harnesses over 90% of reuse opportunities, which translates to reduced computation latency and energy consumption by a factor of 3 to 10.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241557"}, {"primary_key": "3485062", "vector": [], "sparse_vector": [], "title": "Poster: A lightweight Mutually Authenticated Key-Agreement scheme for Wireless Body Area Networks in Internet of Things Environment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Wireless body area network plays a vital role in real time healthcare applications for collecting and exchanging sensitive and private information of patient's physical conditions. For the trusted communication between involved entities, an effective authentication technique must be applied to ensure privacy and confidentiality. As the wearable devices are resource-constraint, we propose a lightweight mutually authenticated key-agreement scheme for wireless body area networks using simple XOR and one-way cryptographic hash functions. The proposed scheme enables the wearable sensing device to authenticate the mobile terminal and establish a secret session key. We measure the performance analysis of our proposed scheme with other related schemes regarding computational and communication cost.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267775"}, {"primary_key": "3485064", "vector": [], "sparse_vector": [], "title": "LiSteer: mmWave Beam Acquisition and Steering by Tracking Indicator LEDs on Wireless APs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present LiSteer, a novel system that steers mmWave beams at mobile devices by repurposing indicator LEDs on wireless Access Points (APs) to passively acquire direction estimates using off-the-shelf light sensors. We demonstrate that LiSteer maintains beam alignment at the narrowest beamwidth level even in case of device mobility, without incurring any training overhead at mobile devices. Our extensive evaluation on a custom dual-band hardware platform comprising highly directional horn antennas as well as practical phased antenna arrays with electronic beam steering shows that LiSteer achieves direction estimates within 2.5 degrees of ground truth on average. Moreover, it achieves beam steering accuracy of more than 97% while in tracking mode, without incurring any client beam training or feedback overhead.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241542"}, {"primary_key": "3485065", "vector": [], "sparse_vector": [], "title": "Proximity-Proof: Secure and Usable Mobile Two-Factor Authentication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile two-factor authentication (2FA) has become commonplace along with the popularity of mobile devices. Current mobile 2FA solutions all require some form of user effort which may seriously affect the experience of mobile users, especially senior citizens or those with disability such as visually impaired users. In this paper, we propose Proximity-Proof, a secure and usable mobile 2FA system without involving user interactions. Proximity-Proof automatically transmits a user's 2FA response via inaudible OFDM-modulated acoustic signals to the login browser. We propose a novel technique to extract individual speaker and microphone fingerprints of a mobile device to defend against the powerful man-in-the-middle (MiM) attack. In addition, Proximity-Proof explores two-way acoustic ranging to thwart the co-located attack. To the best of our knowledge, Proximity-Proof is the first mobile 2FA scheme resilient to the MiM and co-located attacks. We empirically analyze that Proximity-Proof is at least as secure as existing mobile 2FA solutions while being highly usable. We also prototype Proximity-Proof and confirm its high security, usability, and efficiency through comprehensive user experiments.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241574"}, {"primary_key": "3485070", "vector": [], "sparse_vector": [], "title": "Session details: Running on Empty: Backscatter and Low-Power Systems.", "authors": ["<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485071", "vector": [], "sparse_vector": [], "title": "Experience: Cross-Technology Radio Respiratory Monitoring Performance Study.", "authors": ["Peter <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper addresses the performance of systems which use commercial wireless devices to make bistatic RF channel measurements for non-contact respiration sensing. Published research has typically presented results from short controlled experiments on one system. In this paper, we deploy an extensive real-world comparative human subject study. We observe twenty patients during their overnight sleep (a total of 160 hours), during which contact sensors record ground-truth breathing data, patient position is recorded, and four different RF breathing monitoring systems simultaneously record measurements. We evaluate published methods and algorithms. We find that WiFi channel state information measurements provide the most robust respiratory rate estimates of the four RF systems tested. However, all four RF systems have periods during which RF-based breathing estimates are not reliable.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241560"}, {"primary_key": "3485072", "vector": [], "sparse_vector": [], "title": "Poster: Using Barometer on Smartphones to Improve GPS Navigation Altitude Accuracy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Today's GPS navigators have difficulty determining altitude such as whether a car is under or on an overpass. In this paper, we show how to use the barometers, which come with off-the-shelf smartphones, to improve vertical positioning accuracy. We will present performance testing results from different real road scenarios to show the feasibility of the proposed method.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267735"}, {"primary_key": "3485073", "vector": [], "sparse_vector": [], "title": "GPF: A GPU-based Design to Achieve ~100 μs Scheduling for 5G NR.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "5G New Radio (NR) is designed to operate under a broad range of frequency bands and support new applications with ultra-low latency. To support its diverse operating conditions, a set of different OFDM numerologies has been defined in the standards body. Under this numerology, it is necessary to perform scheduling with a time resolution of ∼100 μs. This requirement poses a new challenge that does not exist in LTE and cannot be supported by any existing LTE schedulers. In this paper, we present the design of GPF -- a GPU-based proportional fair (PF) scheduler that can meet the ∼100 μs time requirement. The key ideas include decomposing the scheduling problem into a large number of small and independent sub-problems and selecting a subset of sub-problems from the most promising search space to fit into a GPU. By implementing GPF on an off-the-shelf Nvidia Quadro P6000 GPU, we show that GPF is able to achieve near-optimal performance while meeting the ∼100 $\\mathrmμs time requirement. GPF represents the first successful design of a GPU-based PF scheduler that can meet the new time requirement in NR.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241552"}, {"primary_key": "3485074", "vector": [], "sparse_vector": [], "title": "Verification: Accuracy Evaluation of WiFi Fine Time Measurements on an Open Platform.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Academic and industry research has argued for supporting WiFi time-of-flight measurements to improve WiFi localization. The IEEE 802.11-2016 now includes a Fine Time Measurement (FTM) protocol for WiFi ranging, and several WiFi chipsets offer hardware support albeit without fully functional open software. This paper introduces an open platform for experimenting with fine time measurements and a general, repeatable, and accurate measurement framework for evaluating time-based ranging systems. We analyze the key factors and parameters that affect the ranging performance and revisit standard error correction techniques for WiFi time-based ranging system. The results confirm that meter-level ranging accuracy is possible as promised, but the measurements also show that this can only be consistently achieved in low-multipath environments such as open outdoor spaces or with denser access point deployments to enable ranging at or above 80 MHz bandwidth.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241555"}, {"primary_key": "3485077", "vector": [], "sparse_vector": [], "title": "Frame Aggregation in 802.11ac: Need for Modified Block ACK.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Frame aggregation in 802.11n and ac gives higher throughputs because larger aggregates reduce inter-frame timing overheads. When aggregate (A-MPDU) sizes are forced to be smaller the throughput benefit dwindles. During normal operation the A-MPDU sizes fall from the maximum possible due to the loss of individual constituent subframes. In the worst case, the size can be as low as a single subframe. We show that, compared to the worst case, full-sized A-MPDUs can give up to 400% higher throughput for the highest MCS indexes of 802.11ac. In this paper, we explain how fixed block ACK size coupled with subframe retransmission are the main causes of reduction in A-MPDU size. We present the modification in the block ACK mechanism needed to attain larger sizes of aggregates even in the presence of subframe losses.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267736"}, {"primary_key": "3485079", "vector": [], "sparse_vector": [], "title": "Mitigating the Latency-Accuracy Trade-off in Mobile Data Analytics Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Ion <PERSON>"], "summary": "An increasing amount of mobile analytics is performed on data that is procured in a real-time fashion to make real-time decisions. Such tasks include simple reporting on streams to sophisticated model building. However, the practicality of these analyses are impeded in several domains because they are faced with a fundamental trade-off between data collection latency and analysis accuracy. In this paper, we first study this trade-off in the context of a specific domain, Cellular Radio Access Networks (RAN). We find that the trade-off can be resolved using two broad, general techniques: intelligent data grouping and task formulations that leverage domain characteristics. Based on this, we present CellScope, a system that applies a domain specific formulation and application of Multi-task Learning (MTL) to RAN performance analysis. It uses three techniques: feature engineering to transform raw data into effective features, a PCA inspired similarity metric to group data from geographically nearby base stations sharing performance commonalities, and a hybrid online-offline model for efficient model updates. Our evaluation shows that CellScope's accuracy improvements over direct application of ML range from 2.5× to 4.4× while reducing the model update overhead by up to 4.8×. We have also used CellScope to analyze an LTE network of over 2 million subscribers, where it reduced troubleshooting efforts by several magnitudes. We then apply the underlying techniques in CellScope to another domain specific problem, mobile phone energy bug diagnosis, and show that the techniques are general.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241581"}, {"primary_key": "3485080", "vector": [], "sparse_vector": [], "title": "Poster: Sparse Signal Recovery and Energy Harvesting for Potential 5G Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, a new protocol for simultaneous wireless information and power transfer (SWIPT) has been proposed, which randomly switches the signal for information decoding (ID) and energy harvesting (EH). However, the information decoder recovers the complete data by utilizing the sparse behaviour of the real world signal, thus not compromising the system performance, unlike the conventional SWIPT protocols such as time switching and power splitting.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267750"}, {"primary_key": "3485081", "vector": [], "sparse_vector": [], "title": "Elixir: Efficient Data Transfer in WiFi-based IoT Nodes.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Internet-of-Things (IoT) nodes in dense WiFi deployments struggle to maintain their associations with the access points (APs) and efficiently transmit their data. In the process, they waste their energy and airtime, which affects their battery-life and throughput. We present Elixir, a protocol for WiFi-based IoT nodes that enables them to transmit data without the need of a WiFi association. Elixir is implemented in the user space, which makes it easy to port the implementation to any Linux-based client and AP. We demonstrate its end-to-end operation on a Raspberry Pi module in good [signal strengths '40 to '50 dBm and no losses] and bad network conditions [signal strengths below '80 dBm and losses present]. We measure benefits of Elixir in terms of (a) the ability to transmit when the WiFi association cannot be maintained [in bad network condition], (b) lesser number of frames transmitted [50x - good network condition, 100x ? bad network condition], (c) reduced airtime consumption [10x - bad network condition], and (d) reduced battery expenditure [18% for both network conditions].", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267717"}, {"primary_key": "3485082", "vector": [], "sparse_vector": [], "title": "Session details: Keynote Address V.", "authors": ["<PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485084", "vector": [], "sparse_vector": [], "title": "Achieving Receiver-Side Cross-Technology Communication with Cross-Decoding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Cross-technology Communication (CTC) is a key technique to explore the full capacity of heterogeneous wireless. The latest CTC designs explore the PHY-layer to reach the standards' maximum rate, but leaving a critical gap to practicality -- existing PHY-layer CTCs are commonly transmitter-side techniques requiring a high-end transmitter (with a high degree of freedom in signal manipulation) to emulate the receiver signal closely. This inherently limits the reverse direction (low-end to high-end) communication. We present XBee, a unique receiver-side CTC that fills in the gap and makes a critical step towards achieving CTC bidirectionality. XBee is demonstrated as a ZigBee to BLE communication, where the key innovation lies in the unique mechanism of cross-technology decoding, or cross-decoding in short, which interprets a ZigBee frame only by carefully observing the bit patterns obtained at the BLE receiver. Technically, XBee counterintuitively explores the sampling offset to overcome the intrinsic challenge due to BLE's narrower bandwidth (1MHz) than ZigBee (2MHz). Extensive implementation and evaluation on USRP and commodity devices reach 250 kbps under 85% reliability, a 15,000x improvement over state-of-the-art ZigBee to BLE communication, and comparable with the latest PHY-layer CTCs to achieve CTC bidirectionality.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241547"}, {"primary_key": "3485085", "vector": [], "sparse_vector": [], "title": "Towards Environment Independent Device Free Human Activity Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lu <PERSON>"], "summary": "Driven by a wide range of real-world applications, significant efforts have recently been made to explore device-free human activity recognition techniques that utilize the information collected by various wireless infrastructures to infer human activities without the need for the monitored subject to carry a dedicated device. Existing device free human activity recognition approaches and systems, though yielding reasonably good performance in certain cases, are faced with a major challenge. The wireless signals arriving at the receiving devices usually carry substantial information that is specific to the environment where the activities are recorded and the human subject who conducts the activities. Due to this reason, an activity recognition model that is trained on a specific subject in a specific environment typically does not work well when being applied to predict another subject's activities that are recorded in a different environment. To address this challenge, in this paper, we propose EI, a deep-learning based device free activity recognition framework that can remove the environment and subject specific information contained in the activity data and extract environment/subject-independent features shared by the data collected on different subjects under different environments. We conduct extensive experiments on four different device free activity recognition testbeds: WiFi, ultrasound, 60 GHz mmWave, and visible light. The experimental results demonstrate the superior effectiveness and generalizability of the proposed EI framework.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241548"}, {"primary_key": "3485086", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> in the Wild: When <PERSON><PERSON><PERSON>ss and Randomness Play with You.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiao<PERSON> Chen"], "summary": "Parallel backscatter is a promising technique for high throughput, low power communications. The existing approaches of parallel backscatter are based on a common assumption, i.e. the states of the collided signals are distinguishable from each other in either the time domain or the IQ (the In-phase and Quadrature) domain. We in this paper disclose the superclustering phenomenon, which invalidates that assumption and seriously affects the decoding performance. Then we propose an interstellar travelling model to capture the bursty Gaussian process of a collided signal. Based on this model, we design Hubble, a reliable signal processing approach to support parallel backscatter in the wild. <PERSON><PERSON> addresses several technical challenges: (i) a novel scheme based on <PERSON>'s Chi-Square test to extract the collided signals' combined states, (ii) a Markov driven method to capture the law of signal state transitions, and (iii) error correction schemes to guarantee the reliability of parallel decoding. Theoretically, <PERSON><PERSON> is able to decode all the backscattered data, as long as the signals are detectable by the receiver. The experiment results demonstrate that the median throughput of <PERSON><PERSON> is $11.7\\times$ higher than that of the state-of-the-art approach.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241544"}, {"primary_key": "3485087", "vector": [], "sparse_vector": [], "title": "Demo: Design and Implementation of LTE Advanced Underlay Device to Device Communication Framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Device to Device (D2D) communication is one of the key enhancement of Long-Term Evolution (LTE) Advanced standard which is a precursor to fifth generation (5G) cellular standard. D2D has gained considerable attention due to several drawbacks associated with the existing mobile networks such as multi-hop transmission, higher latency, high power consumption and significant path loss. In an underlay D2D communication, both cellular and D2D users coexist by simultaneously sharing the licensed cellular spectrum. However, the performance of an underlay D2D communication is limited by the amount of interference from the cellular user(CU) to the D2D user and vice versa. In this paper, we design and develop a test-bed to evaluate the real-world performance of the underlay D2D communication framework. We demonstrate the impact of interference caused by CU on D2D receiver's throughput at different cellular angles and position with respect to the base station (BS). The demonstration has been performed on National Instruments Universal Software Radio Peripheral (USRP) RIO platform with LTE application framework module of LabVIEW communication system design suite as a supportive Application programming interface (API).", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267728"}, {"primary_key": "3485088", "vector": [], "sparse_vector": [], "title": "iCALM: A Topology Agnostic Socio-inspired Channel Assignment Performance Prediction Metric for Mesh Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "A multitude of Channel Assignment (CA) schemes have created a paradox of plenty, making CA selection for Wireless Mesh Networks (WMNs) an onerous task. CA performance prediction (CAPP) metrics are novel tools that address the problem of appropriate CA selection. However, most CAPP metrics depend upon a variety of factors such as the WMN topology, the type of CA scheme, and connectedness of the underlying graph. In this work, we propose an improved Channel Assignment Link-Weight Metric (iCALM) that is independent of these constraints. To the best of our knowledge, iCALM is the first universal CAPP metric for WMNs. To evaluate iCALM, we design two WMN topologies that conform to the attributes of real-world mesh network deployments, and run rigorous simulations in ns-3. We compare iCALM to four existing CAPP metrics, and demonstrate that it performs exceedingly well, regardless of the CA type, and the WMN layout.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267753"}, {"primary_key": "3485092", "vector": [], "sparse_vector": [], "title": "The Early International Activities in the Arpanet, Its Mutation into the Internet, and Some Further Regional Extensions.", "authors": ["<PERSON>"], "summary": "This talk will consider some of the activities involved in setting up the International Internet and its precursors. It will consider later efforts in promoting the technology in regions that it had not reached earlier. There will be consideration not only the technical development, but also the political climate which either encouraged or prevented its take-up. Moreover, the importance of personal networks at many stages of the story will be highlighted. I will consider why the first Arpanet service node was sited at UCL, how it grew to provide an international heterogeneous interconnection service until the late '80s, and how this differed from other European developments. This will include the governance put in place, the role of the Open Systems Interconnection rise and fall in Europe, and the growth of multi- agency support on both sides of the Atlantic. In parallel with the development of a UK-US service, an International Collaboration Board (ICB) was formed that fostered unclassified collaborations between certain European and American NATO defence departments. While the ICB was not very significant technically, how its success initiated broader US-European Internet extension will be described. Our emphasis on application services to the '90s will be outlined - message, directory, security and multimedia conferencing. Finally we will treat briefly the start of international networking in India around 1990, and the bringing the academics of Central Asia and the Caucasus into the Internet community.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241588"}, {"primary_key": "3485093", "vector": [], "sparse_vector": [], "title": "Demo: Android Resists Liberation from Its Primary Use Case.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network connectivity is often one of the most challenging aspects of deploying sensors. In many countries, cellular networks provide the most reliable, highest bandwidth, and greatest coverage option for internet access. Repurposing smartphones as gateways could extract value from hundreds of millions of devices currently considered to be e-waste. While these factors make smartphones a seemingly ideal platform to serve as a gateway between sensors and the cloud, we find that a device designed for multi-tenant operation and frequent human interaction becomes unreliable when tasked to continuously run a single application with no human interaction, a somewhat counter-intuitive result. Further, we find that economy phones cannot physically withstand continuous operation, resulting in a surprisingly high rate of permanent device failures in the field. If these observations hold more broadly, they would make mobile phones poorly suited to a range of sensing applications for which they have been rumored to hold great promise.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267726"}, {"primary_key": "3485094", "vector": [], "sparse_vector": [], "title": "Experience: Android Resists Liberation from Its Primary Use Case.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network connectivity is often one of the most challenging aspects of deploying sensors. In many countries, cellular networks provide the most reliable, highest bandwidth, and greatest coverage option for Internet access. While this makes smartphones a seemingly ideal platform to serve as a gateway between sensors and the cloud, we find that a device designed for multi-tenant operation and frequent human interaction becomes unreliable when tasked to continuously run a single application with no human interaction, a seemingly counter-intuitive result. Further, we find that economy phones cannot physically withstand continuous operation, resulting in a surprisingly high rate of permanent device failures in the field. If these observations hold more broadly, they would make mobile phones poorly suited to a range of sensing applications for which they have been rumored to hold great promise.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241583"}, {"primary_key": "3485098", "vector": [], "sparse_vector": [], "title": "Session details: What&apos;s the Frequency, Kenneth? Millimeter-Wave Networks.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485105", "vector": [], "sparse_vector": [], "title": "Demo: Mitigating Multiple Narrowband Interferers in SDR IEEE 802.11g Diversity Receiver.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Narrowband transmitters such as IEEE 802.15.4 (ZigBee) operating in 2.4 GHz ISM band cause significant degradation of throughput to the co-channel IEEE 802.11g (WiFi). The phenomenon is common in smart factories and smart homes which generously use ZigBee sensors for process monitoring and automation. This interferes with omnipresent WiFi in the same 2.4 GHz ISM band. In our demonstration, we will present Software Defined Radio (SDR) based single antenna and multi-antenna prototype of standard-compliant IEEE 802.11g receiver. Our receiver prototype is capable of significantly reducing the packet error rate while facing multiple co-channel narrowband ZigBee interferers. We also demonstrate a real-time SDR implementation of Soft Bit Maximal Ratio Combiner capable of decoding frames from standard compliant IEEE 802.11g transmitters. This is a first of its kind implementation to the best of author's knowledge. The demonstrations use Ettus B210 as SDR hardware and a combination of signal processing modules from two different SDR packages: GNU Radio and Openairinterface.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267713"}, {"primary_key": "3485106", "vector": [], "sparse_vector": [], "title": "Session details: Poster Presentations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485107", "vector": [], "sparse_vector": [], "title": "Demo: MegaSense: Megacity-scale Accurate Air Quality Sensing with the Edge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Sasu Tarkoma"], "summary": "This demo presents MegaSense, an air pollution monitoring system for realizing low-cost, near real-time and high resolution spatio-temporal air pollution maps of urban areas. MegaSense involves a novel hierarchy of multi-vendor distributed air quality sensors, in which accurate sensors calibrate lower cost sensors. Current low-cost air quality sensors suffer from measurement drift and they have low accuracy. We address this significant open problem for dense urban areas by developing a calibration scheme that detects and automatically corrects drift. MegaSense integrates with the 5G cellular network and leverages mobile edge computing for sensor management and distributed pollution map creation. We demonstrate MegaSense with two sensor types, a state of the art air quality monitoring station and a low-cost sensor array, with calibration between the two to improve the accuracy of the low-cost device. Participants can interact with the sensors and see air quality changes in real-time, and observe the mechanism to mitigate sensor drift. Our re-calibration method minimizes the error for NO2 and O3 81% of the time (vs single calibration) and reduces the mean relative error by 25%-45%.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267724"}, {"primary_key": "3485108", "vector": [], "sparse_vector": [], "title": "Keeping the Internet Open with an Open-Source Virtual Assistant.", "authors": ["<PERSON>"], "summary": "Virtual assistants, such as <PERSON><PERSON>, Google Home, and Siri, are revolutionizing our digital life. In the future, they will provide us with a uniform, fully personalized, natural-language interface to all our diverse data sources, web services and IoT devices. The virtual assistant will become a powerful platform as it sees all our personal data and has great influence over the services and vendors we use. We propose a collaborative research effort to develop open-source virtual assistant technology, in concert with a commercially viable distributed infrastructure that safeguards users' data privacy, supports interoperability, and promotes open competition. To jumpstart this effort, we have developed Almond, a working open-source prototype of distributed virtual assistants. Almond lets users use natural language to share their data, IoT, and services with fine-grain control, while preserving their privacy by keeping data on their own devices. Almond also lets users issue advanced commands that monitor real-time events and connect multiple services together. This research lays the groundwork for the creation of three key open, non-proprietary, collaborative virtual assistant resources: (1) Thingpedia, a repository of virtual assistant skills, (2) LUInet (Linguistic User Interface Network), a neural network that translates natural language into ThingTalk, a virtual assistant programming language, and (3) a Distributed ThingTalk Protocol, DTP, that supports sharing with privacy via cooperating virtual assistants.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241586"}, {"primary_key": "3485109", "vector": [], "sparse_vector": [], "title": "Demo: COIN by SenseGiz.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "SenseGiz makes Enterprise and Industrial IoT products for sensor-based condition monitoring, security and real-time asset/people tracking applications using a combination of proprietary mesh connected hardware, cloud, analytics, and apps. Currently, we have designed and developed the following products, COIN: Through our product COIN, we have created a proprietary low powered point to point mesh network of tiny low-cost sensor nodes having an effective communication speed of 1 Mbps and self-healing and self-learning capabilities. COIN can be used across various industries and for a wide range of applications which include, Asset tracking through warehouses, hospitals, airports etc., monitoring values for temperature, humidity, motion, and vibration. Threshold values can be set using our Web Control Panel to get real-time notifications if any value is crossed, also data can be streamed at regular intervals of time, People movement tracking & safety monitoring as well as geo-fencing, security for perimeter/fence to avoid multiple intrusions and unauthorized entry. Using our COINS, -Asset movement can be tracked through warehouses, hospitals, airports etc, thus speeding up processes and eliminate errors by getting real-time location status of your assets. -COIN can monitor values for temperature, humidity, motion, and vibration. Threshold values can be set using our Web Control Panel to get real-time notifications if any value is crossed, also data can be streamed at regular intervals of time. -People movement tracking & safety monitoring, as well as geo-fencing, is done in real time in confined environments such as offices, factories and so on. One can secure their entire perimeter/fence and avoid multiple intrusions and unauthorized entry. Also, get real-time alerts if any vibration or motion is detected.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267722"}, {"primary_key": "3485110", "vector": [], "sparse_vector": [], "title": "RAVEN: Improving Interactive Latency for the Connected Car.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Increasingly, vehicles sold today are connected cars: they offer vehicle-to-infrastructure connectivity through built-in WiFi and cellular interfaces, and they act as mobile hotspots for devices in the vehicle. We study the connection quality available to connected cars today, focusing on user-facing, latency-sensitive applications. We find that network latency varies significantly and unpredictably at short time scales and that high tail latency substantially degrades user experience. We also find an increase in coverage options available due to commercial WiFi offerings and that variations in latency across network options are not well-correlated. Based on these findings, we develop RAVEN, an in-kernel MPTCP scheduler that mitigates tail latency and network unpredictability by using redundant transmission when confidence about network latency predictions is low. RAVEN has several novel design features. It operates transparently, without application modification or hints, to improve interactive latency. It seamlessly supports three or more wireless networks. Its in-kernel implementation allows proactive cancellation of transmissions made unnecessary through redundancy. Finally, it explicitly considers how the age of measurements affects confidence in predictions, allowing better handling of interactive applications that transmit infrequently and networks that exhibit periods of temporary poor performance. Results from speech, music, and recommender applications in both emulated and live vehicle experiments show substantial improvement in application response time.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241571"}, {"primary_key": "3485111", "vector": [], "sparse_vector": [], "title": "Poster: Development of an LAA-LTE Transmitter with Lightweight Wi-Fi Frame Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Since License Assisted Access LTE (LAA-LTE) employs a conservative energy detection (ED) threshold for transmission to coexist with Wi-Fi in unlicensed bands, the spatial spectrum reuse of LAA-LTE can be significantly impaired. Such non-flexible thresholding has been introduced mainly due to ED's incapability of differentiating Wi-Fi frames from LTE frames. As a remedy, we design and develop an LAA-LTE transmitter based on a software-defined radio system proposing lightweight but effective Wi-Fi frame detection with which an LAA-LTE device can capture a Wi-Fi preamble by only using LTE's own time-domain samples. Experiments confirm the efficacy of the developed system.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267754"}, {"primary_key": "3485114", "vector": [], "sparse_vector": [], "title": "Session details: Blinded by the Light: AR, VR, and Vision.", "authors": ["<PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485115", "vector": [], "sparse_vector": [], "title": "RainbowLight: Towards Low Cost Ambient Light Positioning with Mobile Phones.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Visible Light Positioning (VLP) has attracted much research effort recently. Most existing VLP approaches require special designed light or receiver, collecting light information or strict user operation (e.g., horizontally holding mobile phone). This incurs a high deployment, maintenance and usage overhead. We present RainbowLight, a low cost ambient light 3D localization approach easy to deploy in today's buildings. Our key finding is that light through a chip of polarizer and birefringence material produces specific interference and light spectrum at different directions to the chip. We derive a model to characterize the relation for direction, light interference and spectrum. Exploiting the model, RainbowLight calculates the direction to a chip after taking a photo containing the chip. With multiple chips, RainbowLight designs a direction intersection based method to derive the location. We implement RainbowLight and extensively evaluate its performance in various environments. The evaluation results show that RainbowLight achieves an average localization error of 3.3 cm in 2D and 9.6 cm in 3D for light on, and an error of 7.4 cm in 2D and 20.5 cm in 3D for light off scenario in daytime.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241545"}, {"primary_key": "3485116", "vector": [], "sparse_vector": [], "title": "Demo: RainbowLight: Design and Implementation of a Low Cost Ambient Light Positioning System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most existing VLP approaches require special designed light or receiver, collecting light information or strict user operation (e.g., horizontally holding mobile phone). This incurs a high deployment, maintenance and usage overhead. In this demo, we present RainbowLight, a low cost ambient light 3D localization approach easy to deploy in today's buildings. Our key finding is that light through a chip of polarizer and birefringence material produces specific interference and light spectrum at different directions to the chip. We derive a model to characterize the relation for direction, light interference and spectrum. Exploiting the model, RainbowLight calculates the direction to a chip after taking a photo containing the chip. With multiple chips, RainbowLight designs a direction intersection based method to derive the location. This demo shows our prototype of implementation, with simple photo capturing and deriving location of camera on mobile phone. The evaluation results show that RainbowLight achieves an average localization error of 3.3 cm in 2D and 9.6 cm in 3D for light on, and an error of 7.4 cm in 2D and 20.5 cm in 3D for light off scenario in daytime.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267711"}, {"primary_key": "3485118", "vector": [], "sparse_vector": [], "title": "Battery-Free Eye Tracker on Glasses.", "authors": ["T<PERSON>xing Li", "<PERSON><PERSON>"], "summary": "This paper presents a battery-free wearable eye tracker that tracks both the 2D position and diameter of a pupil based on its light absorption property. With a few near-infrared (NIR) lights and photodiodes around the eye, NIR lights sequentially illuminate the eye from various directions while photodiodes sense spatial patterns of reflected light, which are used to infer pupil's position and diameter on the fly via a lightweight inference algorithm. The system also exploits characteristics of different eye movement stages and adjusts its sensing and computation accordingly for further energy savings. A prototype is built with off-the-shelf hardware components and integrated into a regular pair of glasses. Experiments with 22 participants show that the system achieves 0.8-mm mean error in tracking pupil position (2.3 mm at the 95th percentile) and 0.3-mm mean error in tracking pupil diameter (0.9 mm at the 95th percentile) at 120-Hz output frame rate, consuming 395 µW mean power supplied by two small, thin solar cells on glasses side arms.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241578"}, {"primary_key": "3485120", "vector": [], "sparse_vector": [], "title": "SensingGO: Toward Mobile/Cellular Data Measurement with Social and Rewarding Activities.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Mobile Crowd Sensing (MCS) is a promising paradigm to collect large-scale network data globally. However, how to motivate people to collect and share data is a challenge. We believe the major reason why many MCS systems are not pervasive is because there are no incentives for people to use them. In this paper, we present SensingGO, a system which encourages people to keep sensing data by integrating incentive mechanisms. The sensed data are then transmitted to our backend server. The data we collected and the source code of SensingGO are open to anyone freely. We also demonstrate the analysis of real mobile data collected from SensingGO.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267733"}, {"primary_key": "3485121", "vector": [], "sparse_vector": [], "title": "SaFePlay+: A Wearable Cycling Measurement and Analysis System of Lower Limbs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Different from the ordinary apparatus and plug-in devices on bikes, this research brings up a novel system called SaFePlay+, Smart Footwear Platform Plus. The system includes a pair of knee motion sensing module and smart pressure and motion sensing insole, analyzed module and interactive App. SaFePlay+ can immediately collect lower limbs information of users. Moreover, through the novel algorithm of wearable power analyzer, it can help users to understand their body condition in order to adjust the posture, raise the efficiency and avoid sports injury. Through the verification of the apparatus, it shows that our method can get good cadence and power performance.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267757"}, {"primary_key": "3485122", "vector": [], "sparse_vector": [], "title": "Poster: Leveraging Breathing for Continuous User Authentication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This work proposes a continuous user verification system based on unique human respiratory-biometric characteristics extracted from the off-the-shelf WiFi signals. Our system innovatively re-uses widely available WiFi signals to capture the unique physiological characteristics rooted in respiratory motions for continuous authentication. Different from existing continuous authentication approaches having limited applicable scenarios due to their dependence on restricted user behaviors (e.g., keystrokes and gaits) or dedicated sensing infrastructures, our approach can be easily integrated into any existing WiFi infrastructure to provide non-invasive continuous authentication independent of user behaviors. Specifically, we extract representative features leveraging waveform morphology analysis and fuzzy wavelet transformation of respiration signals derived from the readily available channel state information (CSI) of WiFi. A respiration-based user authentication scheme is developed to accurately identify users and reject spoofers. Extensive experiments involving 20 subjects demonstrate that the proposed system can achieve a high authentication success rate of over 93% and robustly defend against various types of attacks.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267743"}, {"primary_key": "3485124", "vector": [], "sparse_vector": [], "title": "Simultaneous Localization and Mapping with Power Network Electromagnetic Field.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hongkai Wen", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Various sensing modalities have been exploited for indoor location sensing, each of which has well understood limitations, however. This paper presents a first systematic study on using the electromagnetic field (EMF) induced by a building's electric power network for simultaneous localization and mapping (SLAM). A basis of this work is a measurement study showing that the power network EMF sensed by either a customized sensor or smartphone's microphone as a side-channel sensor is spatially distinct and temporally stable. Based on this, we design a SLAM approach that can reliably detect loop closures based on EMF sensing results. With the EMF feature map constructed by SLAM, we also design an efficient online localization scheme for resource-constrained mobiles. Evaluation in three indoor spaces shows that the power network EMF is a promising modality for location sensing on mobile devices, which is able to run in real time and achieve sub-meter accuracy.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241540"}, {"primary_key": "3485126", "vector": [], "sparse_vector": [], "title": "Wireless Caching in Large-Scale Edge Access Points: A Local Distributed Approach.", "authors": ["Ge Ma", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Today's mobile users achieve unsatisfactory quality of experience mainly due to the large network distance to the centralized infrastructure. To improve users' experiences, caching at the wireless access points (APs) has been proposed for bringing the contents closer to users. However, the wireless content placement is challenging as the placement is affected by many realistic constraints, such as a large number of APs, interaction among neighboring APs, various local content popularities. In this paper, we study the wireless caching problem, i.e., which contents should be stored by which APs. First, we fulfil these constraints to formulate our problem and introduce an objective function that maximizes the total cache hit rate of all APs. Next, we prove the NP-hardness of the problem and propose a local distributed caching algorithm to address it. Furthermore, we provide a game theoretic perspective on the problem and prove that the proposed algorithm can converge to the Nash Equilibrium in polynomial time. Finally, we perform simulations on a real-world dataset to demonstrate the effectiveness of our algorithm.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267741"}, {"primary_key": "3485128", "vector": [], "sparse_vector": [], "title": "Experience: Implications of Roaming in Europe.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Özgü Alay", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "\"Roam like Home\" is the initiative of the European Commission (EC) to end the levy of extra charges when roaming within the European region. As a result, people are able to use data services more freely across Europe. However, the implications roaming solutions have on performance have not been carefully examined. This paper provides an in-depth characterization of the implications of international data roaming within Europe. We build a unique roaming measurement platform using 16 different mobile networks deployed in six countries across Europe. Using this platform, we measure different aspects of international roaming in 3G and 4G networks, including mobile network configuration, performance characteristics, and content discrimination. We find that operators adopt common approaches to implementing roaming, resulting in additional latency penalties of ∼60 ms or more, depending on geographical distance. Considering content accessibility, roaming poses additional constraints that leads to only minimal deviations when accessing content in the original country. However, geographical restrictions in the visited country make the picture more complicated and less intuitive.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241577"}, {"primary_key": "3485131", "vector": [], "sparse_vector": [], "title": "How Should I Slice My Network?: A Multi-Service Empirical Evaluation of Resource Sharing Efficiency.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "By providing especially tailored instances of a virtual network,network slicing allows for a strong specialization of the offered services on the same shared infrastructure. Network slicing has profound implications on resource management, as it entails an inherent trade-off between: (i) the need for fully dedicated resources to support service customization, and (ii) the dynamic resource sharing among services to increase resource efficiency and cost-effectiveness of the system. In this paper, we provide a first investigation of this trade-off via an empirical study of resource management efficiency in network slicing. Building on substantial measurement data collected in an operational mobile network (i) we quantify the efficiency gap introduced by non-reconfigurable allocation strategies of different kinds of resources, from radio access to the core of the network, and (ii) we quantify the advantages of their dynamic orchestration at different timescales. Our results provide insights on the achievable efficiency of network slicing architectures, their dimensioning, and their interplay with resource management algorithms.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241567"}, {"primary_key": "3485132", "vector": [], "sparse_vector": [], "title": "Poster: Bringing mmWave Communications to Raspberry Pi.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently there has been a huge interest in performing research on millimeter wave (mmWave) communications. Prior work utilize this technology in enabling Gbps wireless links. In contrast, we exploit mmWave technology in designing high-density IoT networks, where there are hundreds of nodes, but each requiring only a Mbps wireless link. However, existing mmWave radios are costly and have high power consumption which makes them unsuitable for IoT sensors. We have built mmPi: a low-cost and low-power mmWave radio that operates as a daughterboard for the Raspberry Pi platform. We believe that mmPi helps advance mmWave research in the IoT domain.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267778"}, {"primary_key": "3485136", "vector": [], "sparse_vector": [], "title": "Poster: Redesigning MPTCP for Edge Clouds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Edge clouds are an attractive platform to support latency-sensitive applications by providing computations on servers deployed close to end-users. These servers aim to employ MPTCP to leverage multiple connections including wireless over a public network. In this paper, we show that the default MPTCP design does not adequately support reliability in these environments, which makes it unfit for use in edge clouds. We propose RAMPTCP, an extension to MPTCP which focuses on adding reliability over network paths.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267738"}, {"primary_key": "3485138", "vector": [], "sparse_vector": [], "title": "SkyCore: Moving Core to the Edge for Untethered and Reliable UAV-based LTE Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The advances in unmanned aerial vehicle (UAV) technology have empowered mobile operators to deploy LTE base stations (BSs) on UAVs, and provide on-demand, adaptive connectivity to hotspot venues as well as emergency scenarios. However, today's evolved packet core (EPC) that orchestrates the LTE RAN faces fundamental limitations in catering to such a challenging, wireless and mobile UAV environment, particularly in the presence of multiple BSs (UAVs). In this work, we argue for and propose an alternate, radical edge EPC design, called SkyCore that pushes the EPC functionality to the extreme edge of the core network - collapses the EPC into a single, light-weight, self-contained entity that is co-located with each of the UAV BS. SkyCore incorporates elements that are designed to address the unique challenges facing such a distributed design in the UAV environment, namely the resource-constraints of UAV platforms, and the distributed management of pronounced UAV and UE mobility. We build and deploy a fully functional version of SkyCore on a two-UAV LTE network and showcase its (i) ability to interoperate with commercial LTE BSs as well as smartphones, (ii) support for both hotspot and standalone multi-UAV deployments, and (iii) superior control and data plane performance compared to other EPC variants in this environment.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241549"}, {"primary_key": "3485140", "vector": [], "sparse_vector": [], "title": "Demo: HAMS: Driver and Driving Monitoring using a Smartphone.", "authors": ["S. <PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Venkata N. <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Road safety is a major public health issue the world over. Many studies have found that the primary factors responsible for road accidents center on the driver and her/his driving. Hence, there is the need to monitor driver's state and her/his driving, with a view to providing effective feedback. Our proposed demo is of HAMS, a windshield-mounted, smartphone-based system that uses the front camera to monitor the driver and back camera to monitor her/his driving behaviour. The objective of HAMS is to provide ADAS-like functionality with low-cost devices that can be retrofitted onto the large installed base of vehicles that lack specialized and expensive sensors such as LIDAR and RADAR. Our demo would show HAMS in action on an Android smartphone to monitor the state of the driver, specifically such as drowsiness, distraction and gaze, and vehicle ranging, lane detection running on pre-recorded videos from drives.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267723"}, {"primary_key": "3485141", "vector": [], "sparse_vector": [], "title": "Session details: Take Me Back to School: Learning and Sensing.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485142", "vector": [], "sparse_vector": [], "title": "Poster: Utilizing Social Networks Data for Trust Management in a Social Internet of Things Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Social Internet of Things (SIoT), an amalgamation of Social Networking concepts to the Internet of Things (IoT), is a strong architectural alternative for IoT solutions. A lot of research work in SIoT has proposed the use of social networking data for community and trust management in SIoT networks. While it seems like an interesting choice, it is important to analyze the effectiveness of social networking data for application to SIoT. In this paper, we analyze the accuracy of using tie information from the Facebook Friend Graph to mimic real-world SIoT network ties. We also discuss a method for ranking the strength of ties in a SIoT network by analyzing the structure of the Facebook Friend Graph. A similar analysis can be performed on data available from other Social Networking platforms, like Twitter, LinkedIn etc.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267744"}, {"primary_key": "3485144", "vector": [], "sparse_vector": [], "title": "Body-Guided Communications: A Low-power, Highly-Confined Primitive to Track and Secure Every Touch.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The growing number of devices we interact with require a convenient yet secure solution for user identification, authorization and authentication. Current approaches are cumbersome, susceptible to eavesdropping and relay attacks, or energy inefficient. In this paper, we propose a body-guided communication mechanism to secure every touch when users interact with a variety of devices and objects. The method is implemented in a hardware token worn on user's body, for example in the form of a wristband, which interacts with a receiver embedded inside the touched device through a body-guided channel established when the user touches the device. Experiments show low-power (uJ/bit) operation while achieving superior resilience to attacks, with the received signal at the intended receiver through the body channel being at least 20dB higher than that of an adversary in cm range.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241550"}, {"primary_key": "3485145", "vector": [], "sparse_vector": [], "title": "ECHO: A Reliable Distributed Cellular Core Network for Hyper-scale Public Clouds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Economies of scale associated with hyper-scale public cloud platforms offer flexibility and cost-effectiveness, resulting in various services and businesses moving to the cloud. One area with little progress in this direction is cellular core networks. A cellular core network manages the state of cellular clients; it is essentially a large distributed state machine with very different virtualization challenges compared to typical cloud services. In this paper we present a novel cellular core network architecture, called ECHO, particularly suited to public cloud deployments, where the availability guarantees might be an order of magnitude worse compared to existing (redundant) hardware platforms. We present the design and implementation of our approach and evaluate its functionality on a public cloud platform. Analysis shows ECHO promises higher availability than existing telco solutions.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241564"}, {"primary_key": "3485147", "vector": [], "sparse_vector": [], "title": "Adaptive Codebook Optimization for Beam Training on Off-the-Shelf IEEE 802.11ad Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Beamforming is vital to overcome the high attenuation in wireless millimeter-wave networks. It enables nodes to steer their antennas in the direction of communication. To cope with complexity and overhead, the IEEE 802.11ad standard uses a sector codebook with distinct steering directions. In current off-the-shelf devices, we find codebooks with generic pre-defined beam patterns. While this approach is simple and robust, the antenna modules that are typically deployed in such devices are capable of generating much more precise antenna beams. In this paper, we adaptively adjust the sector codebook of IEEE 802.11ad devices to optimize the transmit beam patterns for the current channel. To achieve this, we propose a mechanism to extract full channel state information (CSI) regarding phase and magnitude from coarse signal strength readings on off-the-shelf IEEE 802.11ad devices. Since such devices do not expose the CSI directly, we generate a codebook with phase-shifted probing beams that enables us to obtain the CSI by combining strategically selected magnitude measurements. Using this CSI, transmitters dynamically compute a transmit beam pattern that maximizes the signal strength at the receiver. Thereby, we automatically exploit reflectors in the environment and improve the received signal quality. Our implementation of this mechanism on off-the-shelf devices demonstrates that adaptive codebook optimization achieves a significantly higher throughput of about a factor of two in typical real-world scenarios.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241576"}, {"primary_key": "3485150", "vector": [], "sparse_vector": [], "title": "META: Memory Exploration Tool for Android Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Handheld devices are becoming increasingly common in today's world. With every passing year, the diversity of applications (apps) being supported by mobile platforms is growing manyfold. In addition, Android, the most popular handheld OS in the market is releasing a new version every year, with a newer and richer set of APIs, enabling the next generation of feature-rich applications. To support these apps, hardware requirements of these devices are increasing rapidly. Mobile SoCs need a larger number of faster cores, better GPUs and above all, higher DRAM capacities to do justice to user experience. To augment capacity requirements, non-volatile memories (NVMs) have been proposed as a potential addition to LPDDR variants, which have been the mainstay of mobile SoCs. However, few tools exist to carry out architectural design space exploration of main memory hierarchies featuring NVMs for newer Android and app versions. In this paper, we present META, a trace based tool for facilitating the exploration of memory hierarchies in mobile devices. META uses an enhanced version of Android emulator for generating raw instruction traces. These traces are then fed into a cache hierarchy and memory simulation modules to carry out design space exploration for a wide variety of apps and memory technologies.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267737"}, {"primary_key": "3485153", "vector": [], "sparse_vector": [], "title": "Poster: Proximity Detection with Single-Antenna IoT Devices.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Close physical proximity among wireless devices that have never shared a secret key is sometimes used as a basis of trust. In these cases, devices in close proximity are deemed trustworthy while more distant devices are viewed as potential adversaries. Because radio waves are invisible, however, a user may believe a wireless device is communicating with a nearby device when in fact the user's device is communicating with a distant adversary. Researchers have previously proposed methods for multi-antenna devices to ascertain physical proximity with other devices, but devices with a single antenna, such as those commonly used in the Internet of Things, cannot take advantage of these techniques. We investigate a method for a single-antenna Wi-Fi device to quickly determine proximity with another Wi-Fi device. Our approach leverages the repeating nature Wi-Fi's preamble and the characteristics of a transmitting antenna's near field to detect proximity with high probability. Our method never falsely declares proximity at ranges longer than 14 cm.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267751"}, {"primary_key": "3485155", "vector": [], "sparse_vector": [], "title": "Poster: Caching Static and Transient Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by applications like Information Centric Networking for the Internet of Things, we study caching policies for the setting where the data being cached is heterogeneous in nature. This heterogeneity is in two aspects, namely, the lifetime of the data and the size of the data. We propose a caching policy which divides the cache into sub-caches, such that each sub-cache is reserved for data of a specific size and lifetime. Via analytical results and simulations, we show that our policy outperforms existing caching policies for heterogeneous data.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267770"}, {"primary_key": "3485159", "vector": [], "sparse_vector": [], "title": "Service Level Virtualization (SLV): A Preliminary Implementation of 3GPP Service Based Architecture (SBA).", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "For future 5G core networks, Service Based Architecture (SBA) has been proposed in 3GPP Release 15. To realize the idea of SBA, we propose Service Level Virtualization (SLV) in which each network entity is decomposed into different service blocks to provide respective services. In addition, each service block can be virtualized independently into different virtual machine. Thus, SLV can increase the flexibility of the core network.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267749"}, {"primary_key": "3485160", "vector": [], "sparse_vector": [], "title": "VeData: Promoting AI Assisted Autonomous Vehicles.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Jing", "<PERSON>", "<PERSON><PERSON><PERSON> (Sherman) Shen"], "summary": "Connected and autonomous vehicles (CAVs) are envisioned as a promising solution integrating the powerful AI and communication technologies to realize fully self-driving. However, there is few vehicular dataset open to study AI assisted self-driving. To make effectively use of AI technologies to optimize self-driving maneuver, we develop an open VeData platform to share the collected datasets. We also develop a Vehicular network Data harvester (VeData), which can collect various vehicular data at an arbitrary frequency. Based on this, we have incrementally collected diversified first-hand data in many different vehicular scenarios, including driving-in-campus, driving-around-campus, driving-in-downtown, and driving-on-highway. More datasets will be collected and shared to promote the research of AI assisted CAVs.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267756"}, {"primary_key": "3485161", "vector": [], "sparse_vector": [], "title": "Session details: Lock it Down! Security, Countermeasures, and Authentication.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485163", "vector": [], "sparse_vector": [], "title": "Poster: Uplink and Downlink Resource Allocation for Energy Efficient Cellular Networks with Dual Connectivity.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In heterogeneous cellular networks (HCNs), dual connectivity (DC) has been introduced to increase throughput of the system. Base station sleeping (BSS) techniques has been proposed to reduce the power consumption of the under-utilized base stations (BSs). In this paper, given a partially shared deployment (PSD) of subchannels between macro BS (MBS) and the small cell base stations (SCBSs), the DC uplink (UL) resource allocation problem is formulated. Given such PSD system, the optimum number of subchannels that can be allocated for SCBSs for a DC user in UL are computed. The computation of MBS and SCBSs thresholds for selection of DC user in downlink is also presented. Further, a framework to select the operating point based on energy, throughput, user density, and SCBSs density is presented.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267777"}, {"primary_key": "3485166", "vector": [], "sparse_vector": [], "title": "Poster: Facilitating Low Latency and Reliable VR over Heterogeneous Wireless Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Current VR headsets are tethered to computers which limits mobility and poses a tripping hazard. Delivering VR content over a wireless link is challenging due to the high data rates and stringent time delivery requirements. Millimeter wave communication at 60 GHz (WiGig) can meet these requirements, but, it is unreliable due to blockages and beam misalignments. In this poster, we present an idea of using both WiFi and WiGig interfaces to transmit the VR content. We divide a video frame into tiles and prioritize the tiles in the user's field of view. Based on the wireless link conditions, the tiles are encoded with varying qualities and transmitted over either WiFi or WiGig interface. We formulate an optimization framework to deliver the VR video with high reliability and low latency.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267781"}, {"primary_key": "3485167", "vector": [], "sparse_vector": [], "title": "Poster: Joint Data Latency and Packet Loss Optimization for Relay-Node Selection in Time-Varying IoT Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The mobility of smart and connected devices in Internet of Things brings the challenge of reliable data forwarding. In this work, a relay-node selection method is proposed for mobility-tolerant data forwarding. The proposed relay-node selection method uses the connectivity information in time-varying networks. The connectivity information of IoT devices is modelled using homogeneous Poisson point processes. The connectivity duration information of all the devices with their neighbours is updated continuously. Based on connectivity information, an optimal relay-node is selected as a data forwarding node. The online relay-node selection from a source node to the base station establishes a data forwarding path. The proposed method selects the relay-nodes based on joint optimization of two network parameters, data latency and packet loss risk. The simulation results show that the proposed method significantly improves the data latency and the packet loss risk for data forwarding over time-varying IoT networks.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267758"}, {"primary_key": "3485168", "vector": [], "sparse_vector": [], "title": "Demo: Cisco DNA-C as a Platform.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cisco DNA Center is the foundational controller and analytics Center at the heart of Cisco's intent-based network. DNA Center supports the expression of intent for multiple use cases, including base automation capabilities, fabric provisioning, and policy-based segmentation in the enterprise network. DNA Center adds context to this journey through the introduction of Analytics and Assurance. DNA Center provides end-to-end visibility into the network with full context through data and insights", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267712"}, {"primary_key": "3485171", "vector": [], "sparse_vector": [], "title": "Session details: Demos and Exhibits.", "authors": ["<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485172", "vector": [], "sparse_vector": [], "title": "AMuSe: An Agile Multipath TCP Scheduler for Dual-Band 802.11ad/ac Wireless LANs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "802.11ad links provide data rates up to 6.7 Gbps but remain highly susceptible to blockage and mobility. On the other hand, legacy 802.11ac/n links yield much lower rates but are robust even under dynamic scenarios. In this work, we explore using Multipath TCP (MPTCP) to engage both 802.11ad and 802.11ac interfaces simultaneously for performance speed-up and improved reliability. We show that vanilla MPTCP achieves these goals under static conditions but often results in performance worse than using the faster interface alone under dynamic scenarios. We then design and implement AMuSe, a new MPTCP scheduler that allows MPTCP to perform near-optimally under all scenarios.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267755"}, {"primary_key": "3485174", "vector": [], "sparse_vector": [], "title": "Poster: A Learning Automata-based DDoS Attack Defense Mechanism in Software Defined Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sampa Sahoo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The primary innovations behind Software Defined Networks (SDN)are the decoupling of the control plane from the data plane and centralizing the network management through a specialized application running on the controller. Despite all its capabilities, the introduction of various architectural entities of SDN poses many security threats and potential target. Especially, Distributed Denial of Services (DDoS) is a rapidly growing attack that poses a tremendous threat to both control plane and forwarding plane of SDN. Asthe control layer is vulnerable to DDoS attack, the goal of this paper is to provide a defense system which is based on Learning Automata (LA) concepts. It is a self-operating mechanism that responds to a sequence of actions in a certain way to achieve a specific goal. The simulation results show that this scheme effectively reduces the TCP connection setup delay due to DDoS attack.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267764"}, {"primary_key": "3485175", "vector": [], "sparse_vector": [], "title": "E-Witness: Preserve and Prove Forensic Soundness of Digital Evidence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this demo we present E-Witness, a system that uses blockchain technology to prove the integrity and spatio-temporal properties of digital evidence captured through a smart-phone. The system consists of a smart-phone application that computes robust hash of pictures or videos taken from the phone camera, a location attestation service and a public blockchain which contains ledger entries to preserve the evidence file's hash and location certificate. The human witness can remain anonymous in this process. An investigator who receives the evidence (by any means) can verify the integrity and spatio-temporal claims of the evidence by querying the blockchain.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267720"}, {"primary_key": "3485178", "vector": [], "sparse_vector": [], "title": "Closing the Gaps in Inertial Motion Tracking.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A rich body of work has focused on motion tracking techniques using inertial sensors, namely accelerometers, gyroscopes, and magnetometers. Applications of these techniques are in indoor localization, gesture recognition, inventory tracking, vehicular motion, and many others. This paper identifies room for improvement over today's motion tracking techniques. The core observation is that conventional systems have trusted gravity more than the magnetic North to infer the 3D orientation of the object. We find that the reverse is more effective, especially when the object is in continuous fast motion. We leverage this opportunity to design MUSE, a magnetometer-centric sensor fusion algorithm for orientation tracking. Moreover, when the object's motion is somewhat restricted (e.g., human-arm motion restricted by elbow and shoulder joints), we find new methods of sensor fusion to fully leverage the restrictions. Real experiments across a wide range of uncontrolled scenarios show consistent improvement in orientation and location accuracy, without requiring any training or machine learning. We believe this is an important progress in the otherwise mature field of IMU-based motion tracking.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241582"}, {"primary_key": "3485179", "vector": [], "sparse_vector": [], "title": "Poster: Networked Acoustics Around Human Ears.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Junfeng Guan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Ear devices, such as noise-canceling headphones and hearing aids, have dramatically changed the way we listen to the outside world. We re-envision this area by combining wireless communication with acoustics. The core idea is to scatter IoT devices in the environment that listen to ambient sound and forward it over their wireless radio. Since wireless signals travel much faster than sound, the ear-device receives the sound much earlier than its actual arrival. This \"glimpse\" into the future allows sufficient time for acoustic digital processing, serving as a valuable opportunity for various signal processing and machine learning applications. We believe this will enable a digital app store around human ears.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267760"}, {"primary_key": "3485180", "vector": [], "sparse_vector": [], "title": "Session details: Keynote Address I.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485181", "vector": [], "sparse_vector": [], "title": "Session details: Keynote Address IV.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485182", "vector": [], "sparse_vector": [], "title": "ACP: Age Control Protocol for Minimizing Age of Information over the Internet.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Real-time monitoring is characterized by a source repeatedly sending updates over the Internet to a monitor, which desires the sensed information at it to be as fresh (of small age) as possible, given network constraints. We propose the Age Control Protocol (ACP), which, in a network-transparent manner, enables a source to keep the age at the monitor small. We evaluate it using simulations and real-world experiments.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267740"}, {"primary_key": "3485183", "vector": [], "sparse_vector": [], "title": "Minion: The World&apos;s Smallest Energy Auditor.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "In addition to reducing the environmental footprint, smart energy monitoring systems today should reduce operating costs, provide insights on energy consumption, and manage load distribution. This involves monitoring the overall energy consumption of a facility and the individual energy consumption of each device. This is usually done by using multiple energy meters. Minion, a hand sized energy auditing device, has the ability to analyze multiple devices' energy consumption without the need of multiple energy meters. This allows valuable actionable insights for non-intrusive energy management solution. Minion captures voltage and energy signatures at microsecond intervals and uses machine learning techniques to identify the individual assets used and the patterns of their consumption. Minion enables energy transparency, energy savings of up to 30%, predictive maintenance and asset health monitoring, This provides accurate and rapid analysis of multiple energy signatures at any given point.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267730"}, {"primary_key": "3485184", "vector": [], "sparse_vector": [], "title": "Poster: Detection of Topology Poisoning by Silent Relay Attacker in SDN.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Topology Poisoning can be easily performed by injecting a fake link between SDN switches using a silent relay attack. This attack is difficult to detect because 1) the attacker is a passive man-in-the-middle to relay control messages between the compromised ports on SDN switches, and 2) such messages are genuine for the SDN switches and controller. This poster proposes Silent Relay Detector (SRD) that tailor- makes the SDN control messages to be processed normally by authentic switches but to make the attacker malfunction. The SRD implementation and experiment reveal how the silent relay attack is detected and fake link injection is prohibited ingeniously.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267763"}, {"primary_key": "3485186", "vector": [], "sparse_vector": [], "title": "Poster: Wi-Fi User&apos;s Video QoE in the Presence of Duty Cycled LTE-U.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent advances in LTE operating in unlicensed spectrum (LTE-U) has grabbed a lot of attention from industry and academia. The duty cycled LTE-U is shown to be fair with Wi-Fi technology by following an ON-OFF cycle for its transmission. However, the effect of LTE-U on the video quality of Wi-Fi users has not been studied in the literature. In this work, we study the video quality performance of a Wi-Fi user in the presence of LTE-U, in a testbed system. Our results show that the parameters that contribute to the video QoE (Quality of Experience) of Wi-Fi users get adversely affected as the fraction of channel utilized by LTE-U increases, but the same is not shown to be true with another Wi-Fi. We found that poor video QoE of Wi-Fi users in the presence of LTE-U is because of a large number of packet collisions and less channel access time due to ON cycle of LTE-U.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267780"}, {"primary_key": "3485189", "vector": [], "sparse_vector": [], "title": "Demo: Optical Wireless Communication &quot;Li-Fi&quot;.", "authors": ["<PERSON><PERSON>"], "summary": "Data is the new currency impacting everybody's lives. As the modern world receives & sends millions of Terabytes of data every day, the present-day wireless data communication technologies comprising of Wi-Fi & 4G-LTE is on the verge of becoming partially inept for information exchange as they suffer from spectrum congestion in both controlled and uncontrolled environments. Li-Fi, also known as light fidelity, is a full duplex communication network enabling transmittal of data. The potency of bidirectional Visible Light Communication allows us to build an ideal medium, independent of congested radio frequencies and interference from electromagnetic waves, thus, resulting in faster data transfer. Inception of LED technology for lighting in 90's paved the way for high growth trajectory for LED Lighting industry which we have witnessed from the last 2 decades. As semiconductors, LEDs were poised to develop much bigger applications like integrated sensors apart from normal dimming and ambient lighting. Li-Fi is a technology which creates a bridge between the world of data communication & LED Lighting. Multiple forward & backward integration are poised to happen in coming years when lighting players will develop enterprise communication enabled lighting products. Even system integrators will look forward to Li-Fi enabled luminaires for establishing wireless networks. Li-Fi is being seen as a big step forward in enabling 5G telecommunication networks. Security benefits and outdoor long-range communication capabilities Li-Fi a potential technology for Defence & Smart Cities applications. Li-Fi uses the visible and invisible frequency band (380nm - 1500nm) which is 10,000 times broader than usable RF frequency band. The property of light spectrum to be unlicensed and free from any health regulations makes it even more desirable for us. Its applications can extend in areas where the RF technology lacks its presence like aircrafts and hospitals (operation theatres), power plants and various other areas, where electromagnetic (Radio) interference is of great concern for safety and security of equipment's and people. Since there is no potential health hazard associated with light, it can be used safely in such locations or areas. Li-Fi / OWC has applications in both indoor (≅) and outdoor ( ) scenarios.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267729"}, {"primary_key": "3485190", "vector": [], "sparse_vector": [], "title": "Poster: Age-of-Information Aware Scheduling for Heterogeneous Sources.", "authors": ["Bejjipuram Sombabu", "<PERSON><PERSON><PERSON>"], "summary": "We consider a system consisting of multiple sensors, a central monitoring station, and multiple orthogonal frequency channels. The sensors measure heterogeneous time-varying signals and report their measurements to the central monitoring station which uses them to make control decisions. Due to limited communication capacity, not all sensors can send updates to the monitoring station at all times. The cost of the system pays at any time is a weighted sum of the ages-of-information of the various sensors at that time. The goal is to design scheduling policies which minimize the time-average of this cost. We propose a policy called SQRT-Weight which schedules updates from sensors at a frequency proportional to the square-root of the corresponding weights and show that this policy is asymptotically 8--optimal. In addition, we compare the performance of the SQRT-Weight policy with other natural scheduling policies via simulations.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267734"}, {"primary_key": "3485193", "vector": [], "sparse_vector": [], "title": "One Billion Apples&apos; Secret Sauce: Recipe for the Apple Wireless Direct Link Ad hoc Protocol.", "authors": ["Milan Stute", "<PERSON>", "<PERSON>"], "summary": "Apple Wireless Direct Link (AWDL) is a proprietary and undocumented IEEE 802.11-based ad hoc protocol. Apple first introduced AWDL around 2014 and has since integrated it into its entire product line, including iPhone and Mac. While we have found that AWDL drives popular applications such as AirPlay and AirDrop on more than one billion end-user devices, neither the protocol itself nor potential security and Wi-Fi coexistence issues have been studied. In this paper, we present the operation of the protocol as the result of binary and runtime analysis. In short, each AWDL node announces a sequence of Availability Windows (AWs) indicating its readiness to communicate with other AWDL nodes. An elected master node synchronizes these sequences. Outside the AWs, nodes can tune their Wi-Fi radio to a different channel to communicate with an access point, or could turn it off to save energy. Based on our analysis, we conduct experiments to study the master election process, synchronization accuracy, channel hopping dynamics, and achievable throughput. We conduct a preliminary security assessment and publish an open source Wireshark dissector for AWDL to nourish future work.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241566"}, {"primary_key": "3485194", "vector": [], "sparse_vector": [], "title": "Linux Goes Apple Picking: Cross-Platform Ad hoc Communication with Apple Wireless Direct Link.", "authors": ["Milan Stute", "<PERSON>", "<PERSON>"], "summary": "Apple Wireless Direct Link (AWDL) is a proprietary and undocumented wireless ad hoc protocol that Apple introduced around 2014 and which is the base for applications such as AirDrop and AirPlay. We have reverse engineered the protocol and explain its frame format and operation in our MobiCom '18 paper \"One Billion Apples' Secret Sauce: Recipe of the Apple Wireless Direct Link Ad hoc Protocol.\" AWDL builds on the IEEE 802.11 standard and implements election, synchronization, and channel hopping mechanisms on top of it. Furthermore, AWDL features an IPv6-based data path which enables direct communication. To validate our own work, we implement a working prototype of AWDL on Linux-based systems. Our implementation is written in C, runs in userspace, and makes use of Linux's Netlink API for interactions with the system's networking stack and the pcap library for frame injection and reception. In our demonstrator, we show how our Linux system synchronizes to an existing AWDL cluster or takes over the master role itself. Furthermore, it can receive data frames from and send them to a MacBook or iPhone via AWDL. We demonstrate the data exchange via ICMPv6 echo request and replies as well as sending and receiving data over a TCP connection.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267716"}, {"primary_key": "3485195", "vector": [], "sparse_vector": [], "title": "Session details: Multi-Modal and Cross-Technology Communications.", "authors": ["Lu <PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485196", "vector": [], "sparse_vector": [], "title": "Poster: GPU based High Definition Parallel Video Codec Optimization in Mobile Device.", "authors": ["Baichuan Su", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the advances in wireless communication and the growing popularity of mobile devices, it has become rather normal to watch videos using mobile devices. However, there are severely challenges to using video codec on mobile devices, because: 1) insufficient computing resources, there is poor performance using mobile device ; 2) the limited battery capacity on mobile device; 3) CPU utilization is too high when using traditional video codec. In this paper, we proposed a GPU based High Definition Parallel Video Codec on mobile devices, which is an efficient video codec with the cooperation of CPU and GPU. The video codec system is fully compliant with the video codec h264 standard. Compared with the scheme using existing X264, the presented experimental results evaluated the GPU based video codec achieves appreciable improvements in FPS(frames per second), the energy consumption and utilization of CPU are reduced properly at the same time.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267776"}, {"primary_key": "3485197", "vector": [], "sparse_vector": [], "title": "VSkin: Sensing Touch Gestures on Surfaces of Mobile Devices Using Acoustic Signals.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Enabling touch gesture sensing on all surfaces of the mobile device, not limited to the touchscreen area, leads to new user interaction experiences. In this paper, we propose VSkin, a system that supports fine-grained gesture-sensing on the back of mobile devices based on acoustic signals. VSkin utilizes both the structure-borne sounds, i.e., sounds propagating through the structure of the device, and the air-borne sounds, i.e., sounds propagating through the air, to sense finger tapping and movements. By measuring both the amplitude and the phase of each path of sound signals, VSkin detects tapping events with an accuracy of 99.65% and captures finger movements with an accuracy of 3.59mm.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241568"}, {"primary_key": "3485200", "vector": [], "sparse_vector": [], "title": "Session details: Slice, Schedule, Repeat: 5G Cellular Networks.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485201", "vector": [], "sparse_vector": [], "title": "5G: An Evolution Towards a Revolution.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The aim of this tutorial is to give the audience an overview of the landscape of the future generation of mobile networks, namely 5G. Contrary to popular view, 5G is not expected to be anchored on a single disruptive technology but rather supported by an amalgamation of multiple technologies. In essence, it is an evolution of several key technical advancements, whose synergy is expected to revolutionize the heterogeneity of use cases that can be \"simultaneously\" enabled by a single network. Such use cases range from throughput-focused mobile broadband (Gigabit peak rates) to latency/reliability-focused mission critical (e.g. augmented/virtual reality, autonomous driving) and density-focused massive connection (IoT) services. While physical layer advancements in the form of New Radio (NR) are an integral part of 5G, realizing the diverse use cases envisioned, will equally require innovation and flexible orchestration of its access, network and computing layers as well. This tutorial will provide an overview of some of these innovative ingredients that will constitute 5G, from the perspective of not just radio access network, but also core network and services/applications.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3270098"}, {"primary_key": "3485202", "vector": [], "sparse_vector": [], "title": "Towards Scalable and Ubiquitous Millimeter-Wave Wireless Networks.", "authors": ["Sanjib Sur", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Millimeter-wave (mmWave) technology is emerging as the most promising solution to meet the multi-fold demand increase for mobile data. Very short wavelength, high directionality, together with sensitivity to rampant blockages and mobility, however, render state-of-the-art mmWave technologies unsuitable for ubiquitous wireless coverage. In this work, we design and implement UbiG - a mmWave wireless access network - that can deliver ubiquitous gigabits per second wireless access consistently to the commercial-off-the-shelf IEEE 802.11ad devices. UbiG has two key design components: (1) a fast probing based beam alignment algorithm that can identify the best beam consistently with guaranteed latency in a mmWave link, and the algorithm scales well even with a very large number of beams; and (2) an infrastructure-side predictive ranking based fast access point switching algorithm to ensure seamless gigabits per second connectivity under mobility and blockage in a dense mmWave deployment. Our IEEE 802.11ad testbed experiments show that UbiG performs close to an \"Oracle\" solution that instantaneously knows the best beam and access point for gigabits per second data transmission to users.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241579"}, {"primary_key": "3485203", "vector": [], "sparse_vector": [], "title": "Poster: Your Phone Tells Us The Truth: Driver Identification Using Smartphone on One Turn.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Due to the extensive use of smart devices using them to study the driving behaviors has attracted a lot of researchers. This work demonstrates the problem of identifying drivers based on their driving style using smart phones. For this purpose the turns done by the drivers are being studied. Different sensors are embedded in the smart phones which are being used in order to extract some features to distinguish different drivers. Experiments are being done with four drivers and the results show that our system can distinguish them with high accuracy of 92% using only one turn.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267769"}, {"primary_key": "3485213", "vector": [], "sparse_vector": [], "title": "Challenge: RFID Hacking for Fun and Profit.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Passive radio frequency identification (RFID) tags are ubiquitous today due to their low cost (a few cents), relatively long communication range ($\\sim$7-11~m), ease of deployment, lack of battery, and small form factor. Hence, they are an attractive foundation for environmental sensing. Although RFID-based sensors have been studied in the research literature and are also available commercially, manufacturing them has been a technically-challenging task that is typically undertaken only by experienced researchers. In this paper, we show how even hobbyists can transform commodity RFID tags into sensors by physically altering (`hacking') them using COTS sensors, a pair of scissors, and clear adhesive tape. Importantly, this requires no change to commercial RFID readers. We also propose a new legacy-compatible tag reading protocol called Differential Minimum Response Threshold (DMRT) that is robust to the changes in an RF environment. To validate our vision, we develop RFID-based sensors for illuminance, temperature, touch, and gestures. We believe that our approach has the potential to open up the field of batteryless backscatter-based RFID sensing to the research community, making it an exciting area for future work.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241561"}, {"primary_key": "3485214", "vector": [], "sparse_vector": [], "title": "Towards Replay-resilient RFID Authentication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jinsong Han", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We provide the first solution to an important question, \"how a physical-layer authentication method can defend against signal replay attacks''. It was believed that if an attacker can replay the exact same reply signal of a legitimate authentication object (such as an RFID tag), any physical-layer authentication method will fail. This paper presents Hu-Fu, the first physical layer RFID authentication protocol that is resilient to the major attacks including tag counterfeiting, signal replay, signal compensation, and brute-force feature reply. Hu-Fu is built on two fundamental ideas, namely inductive coupling of two tags and signal randomization. Hu-Fu does not require any hardware or protocol modification on COTS passive tags and can be implemented with COTS devices. We implement a prototype of Hu-Fu and demonstrate that it is accurate and robust to device diversity and environmental changes, including locations, distance, and temperature. Hu-Fu provides a new direction of battery-free/low-power device authentication that enables numerous IoT applications.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241541"}, {"primary_key": "3485215", "vector": [], "sparse_vector": [], "title": "Poster: A SDN/NFV-Based IoT Network Slicing Creation System.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the emergency of IoT, there are many IoT network slices with different network requirements. Most of the current IoT system are specific and non-programmable and therefore their slices are difficult to reuse. It is difficult to meet different QoS requirements especially in IoT system because there are plenty of IoT sensors in IoT system. In this paper, we propose a novel IoT network slicing creation system which based on two emerging SDN and NFV technologies. It provides an easily-operating service creation environment and a service execution environment based on micro service architecture. We implement an IoT muti-flow transmission scenario. After adding subservices and QoS policies into a business process at the design plane, the IoT scenario can run automatically at the execution plane. Experiment results on the scenario show that the numbers of packets per second of different flows are changing gradually depend on QoS policies.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267745"}, {"primary_key": "3485216", "vector": [], "sparse_vector": [], "title": "Poster: Inferring Mobile Payment Passcodes Leveraging Wearable Devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Mobile payment has drawn considerable attention due to its convenience of paying via personal mobile devices at anytime and anywhere, and passcodes (i.e., PINs) are the first choice of most consumers to authorize the payment. This work demonstrates a serious security breach and aims to raise the awareness of the public that the passcodes for authorizing transactions in mobile payments can be leaked by exploiting the embedded sensors in wearable devices (e.g., smartwatches). We present a passcode inference system, which examines to what extent the user's PIN during mobile payment could be revealed from a single wrist-worn wearable device under different input scenarios involving either two hands or a single hand. Extensive experiments with 15 volunteers demonstrate that an adversary is able to recover a user's PIN with high success rate within 5 tries under various input scenarios.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267742"}, {"primary_key": "3485220", "vector": [], "sparse_vector": [], "title": "SWAN: <PERSON><PERSON><PERSON> Wi-Fi ANtennas.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents our experience in designing, implementing, testing, and applying a general-purpose antenna extension solution with commodity Wi-Fi. The proposed solution, SWAN, builds an array of stitched antennas extended from the radio chains of commodity Wi-Fi. SWAN has low hardware cost and provides easy-to-use interfaces embedded in the Linux kernel. Two application cases for wireless sensing and communication are presented that proves the usefulness of the solution. SWAN is able to provide over 3× performance improvement on Wi-Fi azimuth estimation and localization and over 30% improvement on Wi-Fi throughput over original Wi-Fi AP with three fixed antennas.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241572"}, {"primary_key": "3485221", "vector": [], "sparse_vector": [], "title": "Session details: Where are U Now? Localization and Motion Tracking.", "authors": ["<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}, {"primary_key": "3485222", "vector": [], "sparse_vector": [], "title": "DeepCache: <PERSON>rin<PERSON><PERSON> <PERSON><PERSON> for Mobile Deep Vision.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present DeepCache, a principled cache design for deep learning inference in continuous mobile vision. DeepCache benefits model execution efficiency by exploiting temporal locality in input video streams. It addresses a key challenge raised by mobile vision: the cache must operate under video scene variation, while trading off among cacheability, overhead, and loss in model accuracy. At the input of a model, DeepCache discovers video temporal locality by exploiting the video's internal structure, for which it borrows proven heuristics from video compression; into the model, DeepCache propagates regions of reusable results by exploiting the model's internal structure. Notably, DeepCache eschews applying video heuristics to model internals which are not pixels but high-dimensional, difficult-to-interpret data. Our implementation of DeepCache works with unmodified deep learning models, requires zero developer's manual effort, and is therefore immediately deployable on off-the-shelf mobile devices. Our experiments show that DeepCache saves inference execution time by 18% on average and up to 47%. DeepCache reduces system energy consumption by 20% on average.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241563"}, {"primary_key": "3485223", "vector": [], "sparse_vector": [], "title": "Resolving Policy Conflicts in Multi-Carrier Cellular Access.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Multi-carrier cellular access dynamically selects a preferred wireless carrier by leveraging the availability and diversity of multiple carrier networks at a location. It offers an alternative to the dominant single-carrier paradigm, and shows early signs of success through the operational Project Fi by Google. In this paper, we study the important, yet largely unexplored, problem of inter-carrier switching for multi-carrier access. We show that policy conflicts can arise between inter- and intra-carrier switching, resulting in oscillations among carriers in the worst case akin to BGP looping. We derive the conditions under which such oscillations occur for three categories of popular policy, and validate them with Project Fi whenever possible. We provide practical guidelines to ensure loop-freedom and assess them via trace-driven emulations.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241558"}, {"primary_key": "3485225", "vector": [], "sparse_vector": [], "title": "Poster: A Lightweight Timestamp-based MAC Detection Scheme for XOR Network Coding in Wireless Sensor Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yuan Tao", "<PERSON><PERSON><PERSON> Zhao", "<PERSON>"], "summary": "Network coding has become a promising approach to improve the communication capability for WSN, which is vulnerable to malicious attacks. There are some solutions, including cryptographic and information-theory schemes, just can thwart data pollution attacks but are not able to detect replay attacks. In the paper, we present a lightweight timestamp-based message authentication code method, called as TMAC. Based on TMAC and the time synchronization technique, the proposed detection scheme can not only resist pollution attacks but also defend replay attacks simultaneously. Finally", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267766"}, {"primary_key": "3485226", "vector": [], "sparse_vector": [], "title": "Demo: Low Latency Mobile Augmented Reality with Flexible Tracking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Pan Hui"], "summary": "Jaguar is a mobile Augmented Reality (AR) framework that leverages GPU acceleration on edge cloud to push the limit of end-to-end latency for AR systems and enable accurate and large-scale object recognition based on image retrieval. It integrates the emerging AR development tools (e.g., ARCore and ARKit) into its client design for achieving flexible, robust and context-aware object tracking. Our prototype implementation of Jaguar reduces the end-to-end AR latency to ~33 ms and achieves accurate six degrees of freedom (6DoF) tracking. In this demo, we will show that our Jaguar client recognizes movie posters within the camera view by offloading computation intensive tasks to edge cloud and augments these posters with their movie trailers in 3D upon receiving the recognition results.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267719"}, {"primary_key": "3485227", "vector": [], "sparse_vector": [], "title": "RF-iCare: An RFID-based Approach for Infusion Status Monitoring.", "authors": ["<PERSON><PERSON>", "Bingbing He", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Infusion monitoring is in great demand for the hospital. In this demo, we propose RF-iCare, an RFID-based approach for monitoring the infusion status, including the liquid level and the drop speed. With a tag array attached on the infusion bottle, we design an RSSI-based signal match model to estimate the liquid level. With a tag attached on the <PERSON>'s dropper, we leverage the phase variation of the tag to estimate the drop speed. We implement RF-iCare with a COTS RFID system and evaluate it in the real-world hospitals. Our experiments demonstrate that RF-iCare can accurately monitor the completion of the infusion over 91% tests, and estimate the liquid level with the mean accuracy of 0.8 cm as well as the drop speed with the error rate less than 3%.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267714"}, {"primary_key": "3485228", "vector": [], "sparse_vector": [], "title": "CrossSense: Towards Cross-Site and Large-Scale WiFi Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present CrossSense, a novel system for scaling up WiFi sensing to new environments and larger problems. To reduce the cost of sensing model training data collection, CrossSense employs machine learning to train, off-line, a roaming model that generates from one set of measurements synthetic training samples for each target environment. To scale up to a larger problem size, CrossSense adopts a mixture-of-experts approach where multiple specialized sensing models, or experts, are used to capture the mapping from diverse WiFi inputs to the desired outputs. The experts are trained offline and at runtime the appropriate expert for a given input is automatically chosen. We evaluate CrossSense by applying it to two representative WiFi sensing applications, gait identification and gesture recognition, in controlled single-link environments. We show that CrossSense boosts the accuracy of state-of-the-art WiFi sensing techniques from 20% to over 80% and 90% for gait identification and gesture recognition respectively, delivering consistently good performance - particularly when the problem size is significantly greater than that current approaches can effectively handle.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241570"}, {"primary_key": "3485229", "vector": [], "sparse_vector": [], "title": "ChromaCode: A Fully Imperceptible Screen-Camera Communication System.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Hidden screen-camera communication techniques emerge as a new paradigm that embeds data imperceptibly into regular videos while remaining unobtrusive to human viewers. Three key goals on imperceptible, high rate, and reliable communication are desirable but conflicting, and existing solutions usually made a trade-off among them. In this paper, we present the design and implementation of ChromaCode, a screen-camera communication system that achieves all three goals simultaneously. In our design, we consider for the first time color space for perceptually uniform lightness modifications. On this basis, we design an outcome-based adaptive embedding scheme, which adapts to both pixel lightness and regional texture. Last, we propose a concatenated code scheme for robust coding and devise multiple techniques to overcome various screen-camera channel errors. Our prototype and experiments demonstrate that ChromaCode achieves remarkable raw throughputs of >700 kbps, data goodputs of 120 kbps with BER of 0.05, and with fully imperceptible flicker for viewing proved by user study, which significantly outperforms previous works.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241543"}, {"primary_key": "3485230", "vector": [], "sparse_vector": [], "title": "X-Tandem: Towards Multi-hop Backscatter Communication with Commodity WiFi.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Backscatter communication offers a cost- and energy-efficient means for IoT sensor data exchange. The IoT vision for ubiquitous interconnection, in practice, demands multi-hop connectivity for robust and scalable sensor networks, as well as compatibility with such prevailing wireless technologies as WiFi. Today's backscatter solutions however typically follow a single-hop paradigm, i.e., tags do not relay for each other. This paper presents X-Tandem, a multi-hop backscatter system that works with commodity WiFi devices. For the first time, we demonstrate that sensing tags can not only work as relays for each other but also modulate their sensing data into a single backscatter packet, which remains a legit WiFi packet that can be decoded with any commercial WiFi NICs. We discuss the design details of X-Tandem and have built a prototype with FPGAs and off-the-shelf WiFi devices. The prototype demonstrates a two-hop implementation, achieving a throughput up to 200 bps with tag-to-tag distances up to 0.4 m and communication ranges up to 8 m. Compared to single-hop solutions, X-Tandem can improve backscatter throughput by more than 10x in challenging indoor environments with obstacles.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241553"}, {"primary_key": "3485231", "vector": [], "sparse_vector": [], "title": "Your Heart Won&apos;t Lie: PPG-based Continuous Authentication on Wrist-worn Wearable Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a photoplethysmography (PPG)-based continuous user authentication (CA) system, which especially leverages the PPG sensors in wrist-worn wearable devices to identify users. We explore the uniqueness of the human cardiac system captured by the PPG sensing technology. Existing CA systems require either the dedicated sensing hardware or specific gestures, whereas our system does not require any users' interactions but only the wearable device, which has already been pervasively equipped with PPG sensors. Notably, we design a robust motion artifacts (MA) removal method to mitigate the impact of MA from wrist movements. Additionally, we explore the characteristic fiducial features from PPG measurements to efficiently distinguish the human cardiac system. Furthermore, we develop a cardiac-based classifier for user identification using the Gradient Boosting Tree (GBT). Experiments with the prototype of the wrist-worn PPG sensing platform and 10 participants in different scenarios demonstrate that our system can effectively remove MA and achieve a high average authentication success rate over $90%$.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267748"}, {"primary_key": "3485232", "vector": [], "sparse_vector": [], "title": "Poster: Pose-assisted Active Visual Recognition in Mobile Augmented Reality.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "While existing visual recognition approaches, which rely on 2D images to train their underlying models, work well for object classification, recognizing the changing state of a 3D object requires addressing several additional challenges. This paper proposes an active visual recognition approach to this problem, leveraging camera pose data available on mobile devices. With this approach, the state of a 3D object, which captures its appearance changes, can be recognized in real time. Our novel approach selects informative video frames filtered by 6-DOF camera poses to train a deep learning model to recognize object state. We validate our approach through a prototype for Augmented Reality-assisted hardware maintenance.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3267771"}, {"primary_key": "3485233", "vector": [], "sparse_vector": [], "title": "EchoPrint: Two-factor Authentication using Acoustics and Vision on Smartphones.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "User authentication on smartphones must satisfy both security and convenience, an inherently difficult balancing art. Apple's FaceID is arguably the latest of such efforts, at the cost of additional hardware (e.g., dot projector, flood illuminator and infrared camera). We propose a novel user authentication system EchoPrint, which leverages acoustics and vision for secure and convenient user authentication, without requiring any special hardware. EchoPrint actively emits almost inaudible acoustic signals from the earpiece speaker to \"illuminate\" the user's face and authenticates the user by the unique features extracted from the echoes bouncing off the 3D facial contour. To combat changes in phone-holding poses thus echoes, a Convolutional Neural Network (CNN) is trained to extract reliable acoustic features, which are further combined with visual facial landmark locations to feed a binary Support Vector Machine (SVM) classifier for final authentication. Because the echo features depend on 3D facial geometries, EchoPrint is not easily spoofed by images or videos like 2D visual face recognition systems. It needs only commodity hardware, thus avoiding the extra costs of special sensors in solutions like FaceID. Experiments with 62 volunteers and non-human objects such as images, photos, and sculptures show that EchoPrint achieves 93.75% balanced accuracy and 93.50% F-score, while the average precision is 98.05%, and no image/video based attack is observed to succeed in spoofing.", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": "10.1145/3241539.3241575"}, {"primary_key": "3530754", "vector": [], "sparse_vector": [], "title": "Proceedings of the 24th Annual International Conference on Mobile Computing and Networking, MobiCom 2018, New Delhi, India, October 29 - November 02, 2018", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> (<PERSON>) <PERSON>", "<PERSON>"], "summary": "The 24th Annual International Conference on Mobile Computing and Networking (ACM MobiCom 2018) is the premier international forum for disseminating significant, cutting-edge research in mobile systems and wireless networks. The technical program this year features 42 outstanding papers that cover a wide variety of topics including augmented reality, cellular networks, millimeter-wave networking, security, and localization. The program this year also includes an experience-track paper and a verification-track paper, reflecting contributions that present experiences in the deployment and operations of mobile systems and networks as well as contributions that seek to verify and/or characterize recent results using rigorous experimental methodology. This year's call for papers attracted 187 qualified submissions from across the globe that were carefully reviewed by 63 Program Committee (PC) members along with a selected group of 12 External Review Committee (ERC) experts. The PC was formed with the goal of covering diverse research expertise as well as diverse perspectives and approaches, and included researchers from 12 countries including China, France, Germany, Great Britain, India, Singapore, South Korea, Spain, Switzerland, and the United States. 18% of the PC members were female and there was broad industry participation with PC and ERC members from Alcatel-Lucent, Google, IBM, Microsoft, NEC, and Telefonica. The PC this year was \"all-heavy,\" meaning that all members participated throughout the entire review and shepherding process. The paper review process was double-blind (both authors' and reviewers' identities were hidden from each other), and carried out in five phases: three review rounds, followed by an online discussion phase, and discussion at the PC meeting itself. In Round 1, each paper was reviewed by at least four PC members, with the top 122 papers selected for promotion to Round 2. In Round 2, each paper was reviewed by three more reviewers, with the top 92 papers selected for promotion to Round 3. Round 3 papers received between one and four further reviews, at the chairs' discretion, depending on the strength of their review scores as well as the confidence of those review scores. We are pleased to report evidence that the additional number of reviews helped the reliability of the review process: 15 papers had three Round 1 reviews all scoring either 1 or 2 (out of 4), yet eventually made it into Round 3 on the basis of a fourth first-round review score of 3 or above, and further high second-round scores. After Round 3, reviewers participated in an online discussion phase to come to an agreement on whether to promote each paper to discussion at the in-person PC meeting, based on an often-extensive technical exchange of each paper's merits and demerits. The online discussion phase resulted in 69 papers being promoted to discussion at the PC meeting, which was held on May 21-22 at Rutgers University in New Jersey. These 69 papers were organized by topic area, and discussed at length in the meeting. After the in-person PC meeting, 42 papers were selected to enter a new doublyanonymous shepherding process, which we describe next. ", "published": "2018-01-01", "category": "mobicom", "pdf_url": "", "sub_summary": "", "source": "mobicom", "doi": ""}]