'use client';

// 简单的 IndexedDB 缓存实现
const DB_NAME = 'pdf2md-cache-db';
const STORE_NAME = 'pdf2md-cache';
const MAX_CACHE = 100;

function openDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, 1);
    request.onupgradeneeded = function (event) {
      const db = request.result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        db.createObjectStore(STORE_NAME, { keyPath: 'pdfUrl' });
      }
    };
    request.onsuccess = function () {
      resolve(request.result);
    };
    request.onerror = function () {
      reject(request.error);
    };
  });
}

async function getCache(pdfUrl: string): Promise<string | undefined> {
  const db = await openDB();
  return new Promise((resolve) => {
    const tx = db.transaction(STORE_NAME, 'readonly');
    const store = tx.objectStore(STORE_NAME);
    const req = store.get(pdfUrl);
    req.onsuccess = function () {
      resolve(req.result?.markdown);
    };
    req.onerror = function () {
      resolve(undefined);
    };
  });
}

async function setCache(pdfUrl: string, markdown: string): Promise<void> {
  const db = await openDB();
  return new Promise((resolve) => {
    const tx = db.transaction(STORE_NAME, 'readwrite');
    const store = tx.objectStore(STORE_NAME);
    const time = Date.now();
    store.put({ pdfUrl, markdown, time });
    // 清理多余缓存
    const allReq = store.getAll();
    allReq.onsuccess = function () {
      const all = allReq.result;
      if (all.length > MAX_CACHE) {
        // 按 time 升序，删除最早的
        all.sort((a: any, b: any) => a.time - b.time);
        for (let i = 0; i < all.length - MAX_CACHE; i++) {
          store.delete(all[i].pdfUrl);
        }
      }
      resolve();
    };
    allReq.onerror = function () {
      resolve();
    };
  });
}

// 处理PDF转换为Markdown的函数，带缓存
export const processPDFToMarkdown = async (pdfUrl: string): Promise<string> => {
  // 先查缓存
  const cached = await getCache(pdfUrl);
  if (cached !== undefined){
    console.log('PDF 解析缓存命中，直接使用缓存结果')
    return cached;
  }
  try {
    const res = await fetch('/api/pdf2md', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ pdfUrl }),
    });
    const data = await res.json();
    if (data.text.length > 100) {
      await setCache(pdfUrl, data.text);
      return data.text;
    }
    return '';
  } catch (error) {
    console.error('PDF处理错误:', error);
    return '';
  }
};

// 只在 @ 且仅 @ 一篇论文时才解析 PDF
export const getSinglePaperPDFContent = async (papers: any[], currentAtTargets: any[]): Promise<string> => {
  if (
    Array.isArray(currentAtTargets) &&
    currentAtTargets.length === 1 &&
    (currentAtTargets[0].type === 'main-paper' || currentAtTargets[0].type === 'collected-paper') &&
    currentAtTargets[0].paper?.pdf_url
  ) {
    try {
      const markdownContent = await processPDFToMarkdown(currentAtTargets[0].paper.pdf_url);
      if (markdownContent) {
        console.log(markdownContent)
        return `\n\n[PDF内容解析]\n${markdownContent}`;
      }
    } catch (error) {
      console.error('获取PDF内容错误:', error);
    }
  }
  return '';
};