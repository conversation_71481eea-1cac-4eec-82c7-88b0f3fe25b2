[{"primary_key": "4164409", "vector": [], "sparse_vector": [], "title": "Vision and Language Integration Meets Multimedia Fusion: Proceedings of ACM Multimedia 2016 Workshop.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Multimodal information fusion both at the signal and the semantics levels is a core part in most multimedia applications, including multimedia indexing, retrieval, summarization and others. Early or late fusion of modality-specific processing results has been addressed in multimedia prototypes since their very early days, through various methodologies including rule-based approaches, information-theoretic models and machine learning. Vision and Language are two of the predominant modalities that are being fused and which have attracted special attention in international challenges with a long history of results, such as TRECVid, ImageClef and others. During the last decade, vision-language semantic integration has attracted attention from traditionally non-interdisciplinary research communities, such as Computer Vision and Natural Language Processing. This is due to the fact that one modality can greatly assist the processing of another providing cues for disambiguation, complementary information and noise/error filtering. The latest boom of deep learning methods has opened up new directions in joint modelling of visual and co-occurring verbal information in multimedia discourse. The workshop on Vision and Language Integration Meets Multimedia Fusion has been held during the workshop weekend of the ACM Multimedia 2016 Conference and the European Conference on Computer Vision (ECCV 2016) on October 16, 2016 in Amsterdam, the Netherlands. The proceedings contain seven selected long papers, which have been orally presented at the workshop, and three abstracts of the invited keynote speeches. The papers and abstracts discuss data collection, representation learning, deep learning approaches, matrix and tensor factorization methods and graph based clustering with regard to the fusion of multimedia data. A variety of applications is presented including image captioning, summarization of news, video hyperlinking, sub-shot segmentation of user generated video, cross-modal classification, cross-modal question-answering, and the detection of misleading metadata of user generated video. The workshop is organized and supported by the EU COST action iV&L Net, the European Network on Integrating Vision and Language: Combining Computer Vision and Language Processing for Advanced Search, Retrieval, Annotation and Description of Visual Data (IC 1307--2014-2018).", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2980537"}, {"primary_key": "4164416", "vector": [], "sparse_vector": [], "title": "Overview of the ACM MultiMedia 2016 International Workshop on Multimedia Assisted Dietary Management.", "authors": ["Stavroula G<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This abstract provides a summary and overview of the 2nd international workshop on multimedia assisted dietary management.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2980535"}, {"primary_key": "4164233", "vector": [], "sparse_vector": [], "title": "AKSDA-MSVM: A GPU-accelerated Multiclass Learning Framework for Multimedia.", "authors": ["<PERSON><PERSON><PERSON>-Chartamp<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, a combined nonlinear dimensionality reduction and multiclass classification framework is proposed. Specifically, a novel discriminant analysis (DA) technique, called accelerated kernel subclass discriminant analysis (AKSDA), derives a discriminant subspace, and a linear multiclass support vector machine (MSVM) computes a set of separating hyperplanes in the derived subspace. Moreover, within this framework an approach for accelerating the computation of multiple Gram matrices and an associated late fusion scheme are presented. Experimental evaluation in five multimedia datasets, on tasks such as video event detection and news document classification, shows that the proposed framework achieves excellent results in terms of both training time and generalization performance.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967263"}, {"primary_key": "4164249", "vector": [], "sparse_vector": [], "title": "Multimedia for personal health and health care.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ever since the emergence of digitization, the term multimedia has been used to represent a combination of different kinds of media types, such images, audio, and videos. As new sensing technologies emerge and are now becoming omnipresent in daily lives, the definition, role and significance of multimedia is changing. Multimedia now represents the means for communicating, cooperating, and also for monitoring numerous aspects of daily life, at various levels of granularity and application, ranging from personal to societal. With this shift, we have since moved from comprehending single media and its state toward comprehending media in terms of its use context. Multimedia is thus no longer confined to documentation and preservation, entertainment or personal media collections; rather, it has become an integral part of the tools and systems that are providing solutions to today's societal challenges-including challenges related to health care and personal health , aging, education, societal participation, sustainable energy, and intelligent transportation. Multimedia has thus evolved into a core enabler for future interactive and cooperative applications at the heart of society. In this workshop we explore the relevance and contribution of multimedia to health care and personal media.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2980536"}, {"primary_key": "4164258", "vector": [], "sparse_vector": [], "title": "Multimedia on the Mountaintop: Using Public Snow Images to Improve Water Systems Operation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper merges multimedia and environmental research to verify the utility of public web images for improving water management in periods of water scarcity, an increasingly critical event due to climate change. A multimedia processing pipeline fetches mountain images from multiple sources and extracts virtual snow indexes correlated to the amount of water accumulated in the snow pack. Such indexes are used to predict water availability and design the operating policy of Lake Como, Italy. The performance of this informed policy is contrasted, via simulation, with the current operation, which depends only on lake water level and day of the year, and with a policy that exploits official Snow Water Equivalent (SWE) estimated from ground stations data and satellite imagery. Virtual snow indexes allow improving the system performance by 11.6% w.r.t. the baseline operation, and yield further improvement when coupled with official SWE information, showing that the two data sources are complementary. The proposed approach exemplifies the opportunities and challenges of applying multimedia content analysis methods to complex environmental problems.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2976759"}, {"primary_key": "4164259", "vector": [], "sparse_vector": [], "title": "AltMM 2016: 1st International Workshop on Multimedia Alternate Realities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Multimedia experiences allow us to access other worlds, to live other people's stories, to communicate with or experience alternate realities. Different spaces, times or situations can be entered thanks to multimedia contents and systems, which coexist with our current reality, and are sometimes so vivid and engaging that we feel we are living in them. Advances in multimedia are making it possible to create immersive experiences that may involve the user in a different or augmented world, as an alternate reality. AltMM 2016, the 1st International Workshop on Multimedia Alternate Realities at ACM Multimedia, aims at exploring how the synergy between multimedia technologies and effects can foster the creation of alternate realities and make their access an enriching, valuable and real experience. The workshop program will contain a combination of oral and invited keynote presentations, and poster, demo and discussion sessions, altogether enabling interactive scientific sharing and discussion between practitioners and researchers.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2980531"}, {"primary_key": "4164299", "vector": [], "sparse_vector": [], "title": "bBridge: A Big Data Platform for Social Multimedia Analytics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this technical demonstration, we propose a cloud-based Big Data Platform for Social Multimedia Analytics called bBridge that automatically detects and profiles meaningful user communities in a specified geographical region, followed by rich analytics on communities' multimedia streams. The system executes a community detection approach that considers the ability of social networks to complement each other during the process of latent representation learning, while the community profiling is implemented based on the state-of-the-art multi-modal latent topic modeling and personal user profiling techniques. The stream analytics is performed via cloud-based stream analytics engine, while the multi-source data crawler deployed as a distributed cloud jobs. Overall, the bBridge platform integrates all the above techniques to serve both business and personal objectives.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973836"}, {"primary_key": "4164304", "vector": [], "sparse_vector": [], "title": "Multimedia Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This tutorial brings together a number of recent advances at the nexus of multimedia analysis, online privacy, and social media mining. Our goal is to offer a multidisciplinary view of the emerging field of Multimedia Privacy: the study of privacy issues arising in the context of multimedia sharing in online platforms, and the pursuit of new approaches to mitigating those issues within multimedia computer science.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2986915"}, {"primary_key": "4164338", "vector": [], "sparse_vector": [], "title": "Scalable Multimedia Streaming in Wireless Networks with Device-to-Device Cooperation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a scalable mobile multimedia streaming system with device-to-device cooperation that enables common content distribution in dense wireless networking environments. This is particularly applicable to use cases such as delivering real-time multimedia content to fans watching a soccer game in a stadium or to participants attending a major conference in a large auditorium. The key novel characteristics of our system include seamless neighbor discovery and link quality estimation, intelligent clustering and channel allocation algorithms based on constrained minimum spanning trees, robustness against device mobility, and device centric operation with no changes to existing wireless systems. We demonstrate the functionality of the proposed system on Android devices using heterogeneous networks (cellular/WiFi/WiFi-Direct) and show the formation of multiple clusters to allow for scalable operation. The gained insights will help bridge the gap between theoretical and simulation based research conducted in this area and practical operation taking into account the capabilities and limitations of existing wireless technologies and smartphones/tablets.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973837"}, {"primary_key": "4164356", "vector": [], "sparse_vector": [], "title": "Key Color Generation for Affective Multimedia Production: An Initial Method and Its Application.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>on-<PERSON>ong Suk"], "summary": "In this paper, we introduce a method that generates a key color to construct an aesthetic and affective harmony with visual content. Given an image and an affective term, our method creates a key color by combining a dominant hue of the image and a unique tone associated with the affective word. To match each affective term with a specific tone, we collected color themes from a crowd-sourced database and identified the most popular tone of color themes that are relevant to each affective term. The results of a user test showed that the method generates satisfactory key colors as much as designers do. Finally, as a prospective application, we employed our method to a promotional video editing prototype. Our method automatically generates a key color based on a frame of an input video and apply the color to a shape that delivers a promotional message. A second user study verifies that the video editing prototype with our method can effectively deliver the desired affective state with a satisfactory quality.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964323"}, {"primary_key": "4164391", "vector": [], "sparse_vector": [], "title": "Zero-Example Multimedia Event Detection and Recounting with Unsupervised Evidence Localization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Retrieval of a complex multimedia event has long been regarded as a challenging task. Multimedia event recounting, other than event detection, focuses on providing comprehensible evidence which justifies a detection result. Recounting enables \"video skimming\", which not only enhances video exploration, but also makes human-in-the-loop possible for improving the detection result. Most existing systems treat event recounting as a disjoint post-processing step over the result of event detection. Unlike these systems, this doctoral research aims to provide an in-depth understanding of how recounting, i.e., evidence localization, helps in event detection in the first place. It can potentially benefit the overall design of an efficient event detection system with or without human-in-the-loop. More importantly, we propose a framework for detecting and recounting everyday events without any needs of training examples. The system only takes a text description of an event as input, then performs evidence localization, event detection and recounting in a large, unlabelled video corpus. The goal of the system is to take advantage of event recounting which eventually improves zero-example event detection. We present preliminary results and work in progress.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2971480"}, {"primary_key": "4164445", "vector": [], "sparse_vector": [], "title": "Multimedia and Medicine: Teammates for Better Disease Detection and Survival.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Wallapak <PERSON>pong", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Health care has a long history of adopting technology to save lives and improve the quality of living. Visual information is frequently applied for disease detection and assessment, and the established fields of computer vision and medical imaging provide essential tools. It is, however, a misconception that disease detection and assessment are provided exclusively by these fields and that they provide the solution for all challenges. Integration and analysis of data from several sources, real-time processing, and the assessment of usefulness for end-users are core competences of the multimedia community and are required for the successful improvement of health care systems. We have conducted initial investigations into two use cases surrounding diseases of the gastrointestinal (GI) tract, where the detection of abnormalities provides the largest chance of successful treatment if the initial observation of disease indicators occurs before the patient notices any symptoms. Although such detection is typically provided visually by applying an endoscope, we are facing a multitude of new multimedia challenges that differ between use cases. In real-time assistance for colonoscopy, we combine sensor information about camera position and direction to aid in detecting, investigate means for providing support to doctors in unobtrusive ways, and assist in reporting. In the area of large-scale capsular endoscopy, we investigate questions of scalability, performance and energy efficiency for the recording phase, and combine video summarization and retrieval questions for analysis.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2976760"}, {"primary_key": "4164447", "vector": [], "sparse_vector": [], "title": "vitrivr: A Flexible Retrieval Stack Supporting Multiple Query Modes for Searching in Multimedia Collections.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "vitrivr is an open source full-stack content-based multimedia retrieval system with focus on video. Unlike the majority of the existing multimedia search solutions, vitrivr is not limited to searching in metadata, but also provides content-based search and thus offers a large variety of different query modes which can be seamlessly combined: Query by sketch, which allows the user to draw a sketch of a query image and/or sketch motion paths, Query by example, keyword search, and relevance feedback. The vitrivr architecture is self-contained and addresses all aspects of multimedia search, from offline feature extraction, database management to frontend user interaction. The system is composed of three modules: a web-based frontend which allows the user to input the query (e.g., add a sketch) and browse the retrieved results (vitrivr-ui), a database system designed for interactive search in large-scale multimedia collections (ADAM), and a retrieval engine that handles feature extraction and feature-based retrieval (Cineast). The vitrivr source is available on GitHub under the MIT open source (and similar) licenses and is currently undergoing several upgrades as part of the Google Summer of Code 2016.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973797"}, {"primary_key": "4164455", "vector": [], "sparse_vector": [], "title": "The Lifecycle of Geotagged Multimedia Data.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The world is a big place. At any given instant something is happening somewhere, but even when nothing is going on people still find ways to generate multimedia data, ranging from social media posts, to photos and videos. A substantial number of these media objects is associated with a location, and in an increasingly mobile and connected world (both in terms of people and devices), this number is only bound to get larger. Yet, in the multimedia literature we observe that many researchers often unwittingly treat the geospatial dimension as if it were a regular feature dimension, despite it requiring special attention. In order to avoid pitfalls and to steer clear of erroneous conclusions, this tutorial aims to teach researchers and students how geotagged multimedia data differs from regular data and to educate them on best practices when dealing with such data. We will cover the lifecycle of geotagged data in multimedia research, where the topics range from how this kind of data is represented, processed, analyzed, and visualized. The tutorial requires both passive and active involvement, where we not only present the material, but the attendees also get the opportunity to interact with it using a variety of open source data and tools that we have prepared using a virtual machine.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2986911"}, {"primary_key": "4164458", "vector": [], "sparse_vector": [], "title": "Multimodal-based Multimedia Analysis, Retrieval, and Services in Support of Social Media Applications.", "authors": ["<PERSON><PERSON>"], "summary": "The rapid growth in the amount of user-generated content (UGCs) online necessitates for social media companies to automatically extract knowledge structures (concepts) from user-generated images (UGIs) and user-generated videos (UGVs) to provide diverse multimedia-related services. For instance, recommending preference-aware multimedia content, the understanding of semantics and sentics from UGCs, and automatically computing tag relevance for UGIs are benefited from knowledge structures extracted from multiple modalities. Since contextual information captured by modern devices in conjunction with a media item greatly helps in its understanding, we leverage both multimedia content and contextual information (eg., spatial and temporal metadata) to address above-mentioned social media problems in our doctoral research. We present our approaches, results, and works in progress on these problems.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2971471"}, {"primary_key": "4164479", "vector": [], "sparse_vector": [], "title": "Geospatial Multimedia Data for Situation Recognition.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Many emerging problems increasingly rely on integrating complex heterogeneous sensor streams, ranging from photos, texts, environmental data streams and other participating human sensors. The diversity of data types from disparate sensors poses a major challenge in data aggregation and assimilation. EventShop was originally designed for situation recognition using diverse data sources. We propose and develop new interpolation and prediction models on top of EventShop, allowing for effective ingesting and combining appropriate data streams to improve data quality and predict specific situations. We also incorporate data from participatory sensing into the system. The synergy of data gives powerful insight into better understanding of evolving situations, in which participatory sensing is integrated with the surrounding environment. Furthermore, the enhanced system is used for two real-world problems: asthma risk management and smart city.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2971472"}, {"primary_key": "4164481", "vector": [], "sparse_vector": [], "title": "Research Challenges in Developing Multimedia Systems for Managing Emergency Situations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With an increasing amount of diverse heterogeneous data and information, the methodology of multimedia analysis has become increasingly relevant in solving challenging societal problems such as managing emergency situations during disasters. Using cybernetic principles combined with multimedia technology, researchers can develop effective frameworks for using diverse multimedia (including traditional multimedia as well as diverse multimodal) data for situation recognition, and determining and communicating appropriate actions to people stranded during disasters. We present known issues in disaster management and then focus on emergency situations. We show that an emergency management problem is fundamentally a multimedia information assimilation problem for situation recognition and for connecting people's needs to available resources effectively, efficiently, and promptly. Major research challenges for managing emergency situations are identified and discussed. We also present a intelligently detecting evolving environmental situations, and discuss the role of multimedia micro-reports as spontaneous participatory sensing data streams in emergency responses. Given enormous progress in concept recognition using machine learning in the last few years, situation recognition may be the next major challenge for learning approaches in multimedia contextual big data. The data needed for developing such approaches is now easily available on the Web and many challenging research problems in this area are ripe for exploration in order to positively impact our society during its most difficult times.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2976761"}, {"primary_key": "4164485", "vector": [], "sparse_vector": [], "title": "Multimedia COMMONS Workshop 2016 (MMCommons 2016): Datasets, Evaluation, and Reproducibility.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Leveraged wisely, new datasets can inspire new multimedia methods and algorithms, as well as catalyze innovations in how their efficacy, efficiency, and generalizability can be evaluated. The availability of very large multimedia datasets like the Yahoo-Flickr Creative Commons 100 Million has offered unique opportunities for advancing the state of the art in multimedia processing, analysis, search, and visualization. The Multimedia Commons Initiative has been developing a community around the YFCC100M, including associated annotation and evaluation efforts. In addition to research in several multimedia subfields, including computer vision, image processing, and video content analysis, the YFCC100M and Multimedia Commons resources have been used in various competitions and benchmarks, such as the MediaEval Placing Task and the ACM Multimedia Grand Challenge competition. With additional annotation and curation, the data has the potential to enable major leaps forward in research.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2980533"}, {"primary_key": "4164515", "vector": [], "sparse_vector": [], "title": "Visual Analytics for Multimedia: Challenges and Opportunities.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Understanding huge multimedia collections is a huge challenge. Given a set of hundreds of thousands or millions of images, how to to understand its contents and how to find the images that are relevant for the task at hand? Using a combination of automated methods, visualization, and interaction, known as visual analytics, is probably the only way to go, combining the strengths of man and machine. An overview is given of trends in data visualization and visual analytics is given, and examples of recent work in multimedia analytics are presented. Exploiting meta-data, using interaction with relatively simple visual representations, and alignment with the work flow of users are promising routes, but scalability and evaluation are still challenging.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984750"}, {"primary_key": "4164530", "vector": [], "sparse_vector": [], "title": "History Rhyme: Searching Historic Events by Multimedia Knowledge.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Qin Jin", "<PERSON>"], "summary": "This demo shows a novel system \"History Rhyme\" which searches historic events by multimedia knowledge. Different from existing historic events related works, we focus on the retrieval of historic events by semantic related multimedia knowledge. Our system can not only search historic events based on keyword queries, but also retrieve similar historic events to a given event based on chosen facets. In both cases, the system returns top retrieval results and shows the image profiles of each historic event. To build such functions, we automatically mine knowledge from multimedia data and index each historic event. Our online demo is available at http://222.29.193.164/HistoryRhyme.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973832"}, {"primary_key": "4164549", "vector": [], "sparse_vector": [], "title": "Sentiment and Emotion Analysis for Social Multimedia: Methodologies and Applications.", "authors": ["Quanzeng You"], "summary": "Online social networks have attracted the attention from both the academia and real world. In particular, the rich multimedia information accumulated in recent years provides an easy and convenient way for more active communication between people. This offers an opportunity to research people's behaviors and activities based on those multimedia content. One emerging area is driven by the fact that these massive multimedia data contain people's daily sentiments and opinions. However, existing sentiment analysis typically focuses on textual information regardless of the visual content, which may be as informative in expressing people's sentiments and opinions. In this research, we attempt to analyze the online sentiment changes of social media users using both the textual and visual content. Nowadays, social media networks such as Twitter have become major platforms of information exchange and communication between users, with tweets as the common information carrier. As an old saying has it, an image is worth a thousand words. The image tweet is a great example of multimodal sentiment. In this research, we focus on sentiment analysis based on visual and multimedia information analysis. We will review the state-of-the-art in this topic. Several of our projects related to this research area will also be discussed. Experimental results are included to demonstrate and summarize our contributions.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2971475"}, {"primary_key": "4210676", "vector": [], "sparse_vector": [], "title": "Proceedings of the 2016 ACM Conference on Multimedia Conference, MM 2016, Amsterdam, The Netherlands, October 15-19, 2016", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We warmheartedly welcome you to the 24th ACM Multimedia conference, which is hosted for the first time in the Netherlands, in the wonderful city of Amsterdam. ACM Multimedia 2016 brings an extensive program consisting of technical sessions covering all aspects of the multimedia field in the form of oral and poster presentations, tutorials, panels, exhibits, demonstrations and workshops, bringing into focus the principal subjects of investigation, competitions of research teams on challenging problems, and an interactive art program stimulating artists and computer scientists to meet and discover together the frontiers of artistic communication. The call for contributions attracted submissions from all over the world, which were all thoroughly reviewed for their merit in terms of scientific quality, innovation, and match to the conference. In addition to these, the main program has two exciting keynote presentations, by <PERSON> from ETH Zurich, Switzerland, titled \"A digital world to thrive in -- How the Internet of Things can make the 'invisible hand' work\" and by <PERSON> from Eindhoven University of Technology, the Netherlands, titled \"Visual Analytics for Multimedia: Challenges and Opportunities\". Furthermore, a visionary presentation will be given by the winner of the SIGMM Award for Outstanding Technical Contributions to Multimedia Computing, Communications and Applications 2016, <PERSON>, from the University of Florence, Italy. The main program will conclude with the SIGMM Rising Stars Symposium, highlighting the scientific results and vision of the invited young researchers, who demonstrated great potential in multimedia research and who are considered to become future leaders in the multimedia field. The main program is accompanied by eight workshops to discuss challenging topics and six tutorials to bring you up to speed on important foundations of our multimedia field. The unique co-location of ACM Multimedia 2016 with the European Conference on Computer Vision (ECCV 2016) has brought the opportunity to have additional twelve invited tutorials from world leaders covering both multimedia and computer vision.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284"}, {"primary_key": "4164222", "vector": [], "sparse_vector": [], "title": "Zero-Shot Hashing via Transferring Supervised Knowledge.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Hashing has shown its efficiency and effectiveness in facilitating large-scale multimedia applications. Supervised knowledge (\\emph{e.g.}, semantic labels or pair-wise relationship) associated to data is capable of significantly improving the quality of hash codes and hash functions. However, confronted with the rapid growth of newly-emerging concepts and multimedia data on the Web, existing supervised hashing approaches may easily suffer from the scarcity and validity of supervised information due to the expensive cost of manual labelling. In this paper, we propose a novel hashing scheme, termed \\emph{zero-shot hashing} (ZSH), which compresses images of \"unseen\" categories to binary codes with hash functions learned from limited training data of \"seen\" categories. Specifically, we project independent data labels (i.e., 0/1-form label vectors) into semantic embedding space, where semantic relationships among all the labels can be precisely characterized and thus seen supervised knowledge can be transferred to unseen classes. Moreover, in order to cope with the semantic shift problem, we rotate the embedded space to more suitably align the embedded semantics with the low-level visual feature space, thereby alleviating the influence of semantic gap. In the meantime, to exert positive effects on learning high-quality hash functions, we further propose to preserve local structural property and discrete nature in binary codes. Besides, we develop an efficient alternating algorithm to solve the ZSH model. Extensive experiments conducted on various real-life datasets show the superior zero-shot image retrieval performance of ZSH as compared to several state-of-the-art hashing methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964319"}, {"primary_key": "4164223", "vector": [], "sparse_vector": [], "title": "Audio Event Detection using Weakly Labeled Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Acoustic event detection is essential for content analysis and description of multimedia recordings. The majority of current literature on the topic learns the detectors through fully-supervised techniques employing strongly labeled data. However, the labels available for majority of multimedia data are generally weak and do not provide sufficient detail for such methods to be employed. In this paper we propose a framework for learning acoustic event detectors using only weakly labeled data. We first show that audio event detection using weak labels can be formulated as an Multiple Instance Learning problem. We then suggest two frameworks for solving multiple-instance learning, one based on support vector machines, and the other on neural networks. The proposed methods can help in removing the time consuming and expensive process of manually annotating data to facilitate fully supervised learning. Moreover, it can not only detect events in a recording but can also provide temporal locations of events in the recording. This helps in obtaining a complete description of the recording and is notable since temporal information was never known in the first place in weakly labeled data.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964310"}, {"primary_key": "4164225", "vector": [], "sparse_vector": [], "title": "Emerging Topics in Learning from Noisy and Missing Data.", "authors": ["<PERSON>-<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While vital for handling most multimedia and computer vision problems, collecting large scale fully annotated datasets is a resource-consuming, often unaffordable task. Indeed, on the one hand datasets need to be large and variate enough so that learning strategies can successfully exploit the variability inherently present in real data, but on the other hand they should be small enough so that they can be fully annotated at a reasonable cost. With the overwhelming success of (deep) learning methods, the traditional problem of balancing between dataset dimensions and resources needed for annotations became a full-fledged dilemma. In this context, methodological approaches able to deal with partially described data sets represent a one-of-a-kind opportunity to find the right balance between data variability and resource-consumption in annotation. These include methods able to deal with noisy, weak or partial annotations. In this tutorial we will present several recent methodologies addressing different visual tasks under the assumption of noisy, weakly annotated data sets.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2986910"}, {"primary_key": "4164226", "vector": [], "sparse_vector": [], "title": "Motion Segmentation using Visual and Bio-mechanical Features.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Nowadays, egocentric wearable devices are continuously increasing their widespread among both the academic community and the general public. For this reason, methods capable of automatically segment the video based on the recorder motion patterns are gaining attention. These devices present the unique opportunity of both high quality video recordings and multimodal sensors readings. Significant efforts have been made in either analyzing the video stream recorded by these devices or the bio-mechanical sensor information. So far, the integration between these two realities has not been fully addressed, and the real capabilities of these devices are not yet exploited. In this paper, we present a solution to segment a video sequence into motion activities by introducing a novel data fusion technique based on the covariance of visual and bio-mechanical features. The experimental results are promising and show that the proposed integration strategy outperforms the results achieved focusing solely on a single source.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967266"}, {"primary_key": "4164229", "vector": [], "sparse_vector": [], "title": "Leveraging ICN for Secure Content Distribution in IP Networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent studies shows that by the end of 2016 more than 60% of Internet traffic would be running on HTTPS. In presence of secure tunnels such as HTTPS, transparent caching solutions become in vain, as the application payload is encrypted by lower level security protocols. This paper addresses this issue and provides an alternate approach, for contents caching without compromising their security. There are three parts to our proposal. First, we propose two new IP layer primitives that allow routers to differentiate between IP and ICN flows. Second, we introduce DCAR (Dual-mode Content Aware Router), which is a traditional IP router enabled to understand the proposed IP primitives. Third, design of DISCS (DCAR based Information centric Secure Content Sharing) framework is proposed that leverages DCAR to allow content object caching along with security services that are comparable to HTTPS. Finally we share details on realizing such system.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973838"}, {"primary_key": "4164232", "vector": [], "sparse_vector": [], "title": "Weighted Linear Fusion of Multimodal Data: A Reasonable Baseline?", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "The ever-increasing demand for reliable inference capable of handling unpredictable challenges of practical application in the real world, has made research on information fusion of major importance. There are few fields of application and research where this is more evident than in the sphere of multimedia which by its very nature inherently involves the use of multiple modalities, be it for learning, prediction, or human-computer interaction, say. In the development of the most common type, score-level fusion algorithms, it is virtually without an exception desirable to have as a reference starting point a simple and universally sound baseline benchmark which newly developed approaches can be compared to. One of the most pervasively used methods is that of weighted linear fusion. It has cemented itself as the default off-the-shelf baseline owing to its simplicity of implementation, interpretability, and surprisingly competitive performance across a wide range of application domains and information source types. In this paper I argue that despite this track record, weighted linear fusion is not a good baseline on the grounds that there is an equally simple and interpretable alternative - namely quadratic mean-based fusion - which is theoretically more principled and which is more successful in practice. I argue the former from first principles and demonstrate the latter using a series of experiments on a diverse set of fusion problems: computer vision-based object recognition, arrhythmia detection, and fatality prediction in motor vehicle accidents.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964304"}, {"primary_key": "4164237", "vector": [], "sparse_vector": [], "title": "Experience Individualization on Online TV Platforms through Persona-based Account Decomposition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Online TV has seen rapid growth in recent years, with most of the large media companies broadcasting their linear content online. Access to the online TV accounts is protected by an authentication, and like the traditional cable TV subscription, users in the same household share the online TV credentials. However, as the standard data collection techniques have capability to collect only account level information, online TV measurements fail to capture individual level viewing characteristics in shared accounts. Thus, individual profile identification and experience individualization are challenging and difficult for online TV platforms. In this paper, we propose a novel approach to decompose online TV account into distinct personas sharing the account through analyzing viewing characteristics. A recommendation algorithm is then proposed to individualize the experience for each persona. Finally, we demonstrate the usefulness of the proposed approach through experiments on a large online TV database.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967221"}, {"primary_key": "4164238", "vector": [], "sparse_vector": [], "title": "A Browsing and Retrieval System for Broadcast Videos using Scene Detection and Automatic Annotation.", "authors": ["<PERSON>", "Costa<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a novel video access and retrieval system for edited videos. The key element of the proposal is that videos are automatically decomposed into semantically coherent parts (called scenes) to provide a more manageable unit for browsing, tagging and searching. The system features an automatic annotation pipeline, with which videos are tagged by exploiting both the transcript and the video itself. Scenes can also be retrieved with textual queries; the best thumbnail for a query is selected according to both semantics and aesthetics criteria.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973825"}, {"primary_key": "4164239", "vector": [], "sparse_vector": [], "title": "How Cosmopolitan Are Emojis?: Exploring Emojis Usage and Meaning over Different Languages with Distributional Semantics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Choosing the right emoji to visually complement or condense the meaning of a message has become part of our daily life. Emojis are pictures, which are naturally combined with plain text, thus creating a new form of language. These pictures are the same independently of where we live, but they can be interpreted and used in different ways. In this paper we compare the meaning and the usage of emojis across different languages. Our results suggest that the overall semantics of the subset of the emojis we studied is preserved across all the languages we analysed. However, some emojis are interpreted in a different way from language to language, and this could be related to socio-geographical differences.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967278"}, {"primary_key": "4164240", "vector": [], "sparse_vector": [], "title": "Deep Learning for Image Memorability Prediction: the Emotional Bias.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Image memorability prediction is a recent topic in computer science. First attempts have shown that it is possible to computationally infer from the intrinsic properties of an image the extent to which it is memorable. In this paper, we introduce a fine-tuned deep learning-based computational model for image memorability prediction. The performance of this model significantly outperforms previous work and obtains a 32.78% relative increase compared to the best-performing model from the state of the art on the same dataset. We also investigate how our model generalizes on a new dataset of 150 images, for which memorability and affective scores were collected from 50 participants. The prediction performance is weaker on this new dataset, which highlights the issue of representativity of the datasets. In particular, the model obtains a higher predictive performance for arousing negative pictures than for neutral or arousing positive ones, recalling how important it is for a memorability dataset to consist of images that are appropriately distributed within the emotional space.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967269"}, {"primary_key": "4164241", "vector": [], "sparse_vector": [], "title": "Pyo, the Python DSP toolbox.", "authors": ["<PERSON>"], "summary": "This paper introduces pyo, a python module dedicated to the digital processing of sound. This audio engine distinguishes itself from other alternatives by being natively integrated to a common general programming language. This integration allows incorporating audio processes quickly to other programming tasks, like mathematical computations, network communications or graphical interface programming. We will expose the main features of the library as well as the different contexts of use where pyo can be of a great benefit to composers and audio software developers.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973804"}, {"primary_key": "4164243", "vector": [], "sparse_vector": [], "title": "SDNDASH: Improving QoE of HTTP Adaptive Streaming Using Software Defined Networking.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "HTTP adaptive streaming (HAS) is being adopted with increasing frequency and becoming the de-facto standard for video streaming. However, the client-driven, on-off adaptation behavior of HAS results in uneven bandwidth competition and this is exacerbated when a large number of clients share the same bottleneck network link and compete for the available bandwidth. With HAS each client independently strives to maximize its individual share of the available bandwidth, which leads to bandwidth competition and a decrease in end-user quality of experience (QoE). The competition causes scalability issues, which are quality instability, unfair bandwidth sharing and network resource underutilization. We propose a new software defined networking (SDN) based dynamic resource allocation and management architecture for HAS systems, which aims to alleviate these scalability issues and improve the per-client QoE. Our architecture manages and allocates the network resources dynamically for each client based on its expected QoE. Experimental results show that the proposed architecture significantly enhances scalability by improving per-client QoE by at least 30% and supporting up to 80% more clients with the same QoE compared to the conventional schemes.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964332"}, {"primary_key": "4164244", "vector": [], "sparse_vector": [], "title": "Leveraging Contextual Cues for Generating Basketball Highlights.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The massive growth of sports videos has resulted in a need for automatic generation of sports highlights that are comparable in quality to the hand-edited highlights produced by broadcasters such as ESPN. Unlike previous works that mostly use audio-visual cues derived from the video, we propose an approach that additionally leverages contextual cues derived from the environment that the game is being played in. The contextual cues provide information about the excitement levels in the game, which can be ranked and selected to automatically produce high-quality basketball highlights. We introduce a new dataset of 25 NCAA games along with their play-by-play stats and the ground-truth excitement data for each basket. We explore the informativeness of five different cues derived from the video and from the environment through user studies. Our experiments show that for our study participants, the highlights produced by our system are comparable to the ones produced by ESPN for the same games.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964286"}, {"primary_key": "4164245", "vector": [], "sparse_vector": [], "title": "Query Adaptive Instance Search using Object Sketches.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Junsong Yuan", "<PERSON>xiang Hong", "<PERSON><PERSON>"], "summary": "Sketch-based object search is a challenging problem mainly due to two difficulties: (1) how to match the binary sketch query with the colorful image, and (2) how to locate the small object in a big image with the sketch query. To address the above challenges, we propose to leverage object proposals for object search and localization. However, instead of purely relying on sketch features, e.g., Sketch-a-Net, to locate the candidate object proposals, we propose to fully utilize the appearance information to resolve the ambiguities among object proposals and refine the search results. Our proposed query adaptive search is formulated as a sub-graph selection problem, which can be solved by maximum flow algorithm. By performing query expansion using a smaller set of more salient matches as the query representatives, it can accurately locate the small target objects in cluttered background or densely drawn deformation intensive cartoon (Manga like) images. Our query adaptive sketch based object search on benchmark datasets exhibits superior performance when compared with existing methods, which validates the advantages of utilizing both the shape and appearance features for sketch-based search.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964317"}, {"primary_key": "4164246", "vector": [], "sparse_vector": [], "title": "Bidirectional Long-Short Term Memory for Video Description.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Video captioning has been attracting broad research attention in multimedia community. However, most existing approaches either ignore temporal information among video frames or just employ local contextual temporal knowledge. In this work, we propose a novel video captioning framework, termed as \\emph{Bidirectional Long-Short Term Memory} (BiLSTM), which deeply captures bidirectional global temporal structure in video. Specifically, we first devise a joint visual modelling approach to encode video data by combining a forward LSTM pass, a backward LSTM pass, together with visual features from Convolutional Neural Networks (CNNs). Then, we inject the derived video representation into the subsequent language model for initialization. The benefits are in two folds: 1) comprehensively preserving sequential and visual information; and 2) adaptively learning dense visual features and sparse semantic representations for videos and sentences, respectively. We verify the effectiveness of our proposed video captioning framework on a commonly-used benchmark, i.e., Microsoft Video Description (MSVD) corpus, and the experimental results demonstrate that the superiority of the proposed approach as compared to several state-of-the-art methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967258"}, {"primary_key": "4164247", "vector": [], "sparse_vector": [], "title": "Contextual Enrichment of Remote-Sensed Events with Social Media Streams.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The availability of satellite images for academic or commercial purpose is increasing rapidly due to efforts made by governmental agencies (NASA, ESA) to publish such data openly or commercial startups (PlanetLabs) to provide real-time satellite data. Beyond many commercial application, satellite data is helpful to create situation awareness in disaster recovery and emergency situations such as wildfires, earthquakes, or flooding. To fully utilize such data sources, we present a scalable system for the contextual enrichment of satellite images by crawling and analyzing multimedia content from social media. This information stream can provide vital information from the ground and help to complement remote sensing in situations. We use Twitter as main data source and analyze its textual, visual, temporal, geographical and social dimensions. Visualizations show different aspects of the event allowing high-level comprehension and provide deeper insights into the event as complemented by social media.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984063"}, {"primary_key": "4164248", "vector": [], "sparse_vector": [], "title": "madmom: A New Python Audio and Music Signal Processing Library.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we present madmom, an open-source audio processing and music information retrieval (MIR) library written in Python. madmom features a concise, NumPy-compatible, object oriented design with simple calling conventions and sensible default values for all parameters, which facilitates fast prototyping of MIR applications. Prototypes can be seamlessly converted into callable processing pipelines through madmo<PERSON>'s concept of Processors, callable objects that run transparently on multiple cores. Processors can also be serialised, saved, and re-run to allow results to be easily reproduced anywhere. Apart from low-level audio processing, madmom puts emphasis on musically meaningful high-level features. Many of these incorporate machine learning techniques and madmom provides a module that implements some methods commonly used in MIR such as hidden Markov models and neural networks. Additionally, madmom comes with several state-of-the-art MIR algorithms for onset detection, beat, downbeat and meter tracking, tempo estimation, and chord recognition. These can easily be incorporated into bigger MIR systems or run as stand-alone programs.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973795"}, {"primary_key": "4164250", "vector": [], "sparse_vector": [], "title": "CrowdNet: A Deep Convolutional Network for Dense Crowd Counting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>vas S. S<PERSON>", "<PERSON><PERSON>"], "summary": "Our work proposes a novel deep learning framework for estimating crowd density from static images of highly dense crowds. We use a combination of deep and shallow, fully convolutional networks to predict the density map for a given crowd image. Such a combination is used for effectively capturing both the high-level semantic information (face/body detectors) and the low-level features (blob detectors), that are necessary for crowd counting under large scale variations. As most crowd datasets have limited training samples (<100 images) and deep learning based approaches require large amounts of training data, we perform multi-scale data augmentation. Augmenting the training samples in such a manner helps in guiding the CNN to learn scale invariant representations. Our method is tested on the challenging UCF_CC_50 dataset, and shown to outperform the state of the art methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967300"}, {"primary_key": "4164253", "vector": [], "sparse_vector": [], "title": "Improving Speaker Diarization of TV Series using Talking-Face Detection and Clustering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "While successful on broadcast news, meetings or telephone conversation, state-of-the-art speaker diarization techniques tend to perform poorly on TV series or movies. In this paper, we propose to rely on state-of-the-art face clustering techniques to guide acoustic speaker diarization. Two approaches are tested and evaluated on the first season of Game Of Thrones TV series. The second (better) approach relies on a novel talking-face detection module based on bi-directional long short-term memory recurrent neural network. Both audio-visual approaches outperform the audio-only baseline. A detailed study of the behavior of these approaches is also provided and paves the way to future improvements.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967202"}, {"primary_key": "4164254", "vector": [], "sparse_vector": [], "title": "Do Textual Descriptions Help Action Recognition?", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a novel method to improve action recognition by leveraging a set of captioned videos. By learning linear projections to map videos and text onto a common space, our approach shows that improved results on unseen videos can be obtained. We also propose a novel structure preserving loss that further ameliorates the quality of the projections. We tested our method on the challenging, realistic, Hollywood2 action recognition dataset where a considerable gain in performance is obtained. We show that the gain is proportional to the number of training samples used to learn the projections.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967301"}, {"primary_key": "4164255", "vector": [], "sparse_vector": [], "title": "Quartet-net Learning for Visual Instance Retrieval.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recently, neuron activations extracted from a pre-trained convolutional neural network (CNN) show promising performance in various visual tasks. However, due to the domain and task bias, using the features generated from the model pre-trained for image classification as image representations for instance retrieval is problematic. In this paper, we propose quartet-net learning to improve the discriminative power of CNN features for instance retrieval. The general idea is to map the features into a space where the image similarity can be better evaluated. Our network differs from the traditional Siamese-net in two ways. First, we adopt a double-margin contrastive loss with a dynamic margin tuning strategy to train the network which leads to more robust performance. Second, we introduce in the mimic learning regularization to improve the generalization ability of the network by preventing it from overfitting to the training data. Catering for the network learning, we collect a large-scale dataset, namely GeoPair, which consists of 68k matching image pairs and 63k non-matching pairs. Experiments on several standard instance retrieval datasets demonstrate the effectiveness of our method.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967262"}, {"primary_key": "4164256", "vector": [], "sparse_vector": [], "title": "WorkCache: Salvaging siloed knowledge.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The proliferation of workplace multimedia collaboration applications has meant on one hand more opportunities for group work but on the other more data locked away in proprietary interfaces. We are developing new tools to capture and access multimedia content from any source. In this demo, we focus primarily on new methods that allow users to rapidly reconstitute, enhance, and share document-based information.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973809"}, {"primary_key": "4164257", "vector": [], "sparse_vector": [], "title": "User Redirection and Direct Haptics in Virtual Environments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes a haptic interaction system for Virtual Reality (VR) based on a combination of tracking devices for hands and objects and a real-to-virtual mapping system for user redirection. In our solution the user receives haptic stimuli by manipulating real objects mapped to virtual ob- jects. This solution departs from systems that rely on haptic devices (e.g., haptic gloves) as interfaces for the user to inter- act with objects in the Virtual Environment (VE). As such, the proposed solution makes use of direct haptics (touching) and redirection techniques to guide the user through the vir- tual environment. Using the mapping framework, when the user touches a virtual object in the VE, he will simultane- ously be physically touching the equivalent real object. A relevant feature of the framework is the possibility to de- fine a warped mapping between the real and virtual worlds, such that the relation between the user and the virtual space can be different from the one between the user and the real space. This is particularly useful when the application re- quires the emulation of large virtual spaces but the physical space available is more confined. To achieve this, both the user's hands and the objects are tracked. In the presented prototype we use a head-mounted depth sensor (i.e., Leap Motion) and a depth-sensing camera (i.e., Kinect). To assess the feasibility of this solution, a functional prototype and a room setup with core functionality were implemented. The test sessions with users evaluated the mapping accuracy, the user execution time and the awareness of the user regarding the warped space when performing tasks with redirection. The results gathered indicate that the solution can be used to provide direct haptic feedback in VR applications and for warping space perception within certain limits.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964293"}, {"primary_key": "4164260", "vector": [], "sparse_vector": [], "title": "Performance Measurements of Virtual Reality Systems: Quantifying the Timing and Positioning Accuracy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose the very first non-intrusive measurement methodology for quantifying the performance of commodity Virtual Reality (VR) systems. Our methodology considers the VR system under test as a black-box and works with any VR applications. Multiple performance metrics on timing and positioning accuracy are considered, and detailed testbed setup and measurement steps are presented. We also apply our methodology to several VR systems in the market, and carefully analyze the experiment results. We make several observations: (i) 3D scene complexity affects the timing accuracy the most, (ii) most VR systems implement the dead reckoning algorithm, which incurs a non-trivial correction latency after incorrect predictions, and (iii) there exists an inherent trade-off between two positioning accuracy metrics: precision and sensitivity.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967303"}, {"primary_key": "4164261", "vector": [], "sparse_vector": [], "title": "Location-Independent WiFi Action Recognition via Vision-based Methods.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Due to the characteristics of ubiquity, non-occlusion, privacy preservation of WiFi, many researchers have devoted to human action recognition using WiFi signals. As demonstrated in [1], Channel State Information (CSI), a fine-grained information capturing the properties of WiFi signal propagation, could be transformed into images for achieving a promising accuracy on action recognition via vision-based methods. However, from the experimental results shown in [1], the CSI is usually location dependent, which affects the recognition performance if signals are recorded in different places.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967203"}, {"primary_key": "4164262", "vector": [], "sparse_vector": [], "title": "ReadMe: A Real-Time Recommendation System for Mobile Augmented Reality Ecosystems.", "authors": ["<PERSON><PERSON>", "Pan Hui"], "summary": "We introduce ReadMe, a real-time recommendation system (RS) and an online algorithm for Mobile Augmented Reality (MAR) ecosystems. A MAR ecosystem is the one that contains mobile users and virtual objects. The role of ReadMe is to detect and present the most suitable virtual objects on the mobile user's screen. The selection of the proper virtual objects depends on the mobile users' context. We consider the user's context as a set of variables that can be either drawn directly by user's device or can be inferred by it or can be collected in collaboration with other mobile devices.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967233"}, {"primary_key": "4164263", "vector": [], "sparse_vector": [], "title": "Multi-Modal Learning: Study on A Large-Scale Micro-Video Data Collection.", "authors": ["<PERSON><PERSON> Chen"], "summary": "Micro-video sharing social services, as a new phenomenon in social media, enable users to share micro-videos and thus gain increasing enthusiasm among people. One distinct characteristic of micro-videos is the multi-modality, as these videos always have visual signals, audio tracks, textual descriptions as well as social clues. Such multi-modality data makes it possible to obtain a comprehensive understanding of videos and hence provides new opportunities for researchers. However, limited efforts thus far have been dedicated to this new emerging user-generated contents (UGCs) due to the lack of large-scale benchmark dataset. Towards this end, in this paper, we construct a large-scale micro-video dataset, which can support many research domains, such as popularity prediction and venue estimation. Based upon this dataset, we conduct an initial study in popularity prediction of micro-videos. Finally, we identify our future work.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2971477"}, {"primary_key": "4164264", "vector": [], "sparse_vector": [], "title": "Context-aware Image Tweet Modelling and Recommendation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON><PERSON><PERSON>n"], "summary": "While efforts have been made on bridging the semantic gap in image understanding, the in situ understanding of social media images is arguably more important but has had less progress. In this work, we enrich the representation of images in image tweets by considering their social context. We argue that in the microblog context, traditional image features, e.g., low-level SIFT or high-level detected objects, are far from adequate in interpreting the necessary semantics latent in image tweets. To bridge this gap, we move from the images' pixels to their context and propose a context-aware image bf tweet modelling (CITING) framework to mine and fuse contextual text to model such social media images' semantics. We start with tweet's intrinsic contexts, namely, 1) text within the image itself and 2) its accompanying text; and then we turn to the extrinsic contexts: 3) the external web page linked to by the tweet's embedded URL, and 4) the Web as a whole. These contexts can be leveraged to benefit many fundamental applications. To demonstrate the effectiveness our framework, we focus on the task of personalized image tweet recommendation, developing a feature-aware matrix factorization framework that encodes the contexts as a part of user interest modelling. Extensive experiments on a large Twitter dataset show that our proposed method significantly improves performance. Finally, to spur future studies, we have released both the code of our recommendation model and our image tweet dataset.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964291"}, {"primary_key": "4164265", "vector": [], "sparse_vector": [], "title": "A Perceptual Quality Metric for Videos Distorted by Spatially Correlated Noise.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Assessing the perceptual quality of videos is critical for monitoring and optimizing video processing pipelines. In this paper, we focus on predicting the perceptual quality of videos distorted by noise. Existing video quality metrics are tuned for \"white\", i.e., spatially uncorrelated noise. However, white noise is very rare in real videos. Based on our analysis of the noise correlation patterns in a broad and comprehensive video set, we build a video database that simulates the commonly encountered noise characteristics. Using the database, we develop a perceptual quality assessment algorithm that explicitly incorporates the noise correlations. Experimental results show that, for videos with spatially correlated noises, the proposed algorithm presents high accuracy in predicting perceptual qualities.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964302"}, {"primary_key": "4164266", "vector": [], "sparse_vector": [], "title": "Multi-modal Conditional Attention Fusion for Dimensional Emotion Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "Qin Jin"], "summary": "Continuous dimensional emotion prediction is a challenging task where the fusion of various modalities usually achieves state-of-the-art performance such as early fusion or late fusion. In this paper, we propose a novel multi-modal fusion strategy named conditional attention fusion, which can dynamically pay attention to different modalities at each time step. Long-short term memory recurrent neural networks (LSTM-RNN) is applied as the basic uni-modality model to capture long time dependencies. The weights assigned to different modalities are automatically decided by the current input features and recent history information rather than being fixed at any kinds of situation. Our experimental results on a benchmark dataset AVEC2015 show the effectiveness of our method which outperforms several common fusion strategies for valence prediction.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967286"}, {"primary_key": "4164267", "vector": [], "sparse_vector": [], "title": "Semantic Image Profiling for Historic Events: Linking Images to Phrases.", "authors": ["<PERSON><PERSON>", "Qin Jin", "<PERSON><PERSON>"], "summary": "Automatically generating image profiles for historic events is desired for history knowledge preservation and curation. However, a simple profile with groups of related images lacks explicit semantic information, such as which images correspond to which aspects of the event. In this paper, we propose to add explicit semantic information to image profiling by linking images in the profile with related phrases in the event description. We measure the relevance of an image-phrase pair via a real-valued matching score. We exploit instance-wise ranking loss function to learn the matching score and we deal with two challenges: 1) how to automatically generate labeled positive data: we leverage out-of-domain labeled datasets to generate pseudo positive in-domain labels and propose a new algorithm (WIL4PPL) to robustly learn the model from the noisy pseudo positive labels; 2) how to automatically generate negative data: we propose a negative set generation algorithm to guide the model in learning which phrases and images to distinguish. We compare our model to three baselines and conduct detailed analysis and case studies to verify the quality of learnt semantic information. The extensive experiment results show the effectiveness of our proposed algorithms which significantly outperform the baselines.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964306"}, {"primary_key": "4164268", "vector": [], "sparse_vector": [], "title": "Facial Age Estimation Using Robust Label Distribution.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Facial age estimation, to predict the persons' exact ages given facial images, usually encounters the data sparsity problem due to the difficulties in data annotation. To mitigate the suffering from sparse data, a recent label distribution learning (LDL) algorithm attempts to embed label correlation into a classification based framework. However, the conventional label distribution learning framework only considers correlations across the neighbouring variables (ages), which omits the intrinsic complexity of age classes during different ageing periods (age groups). In the light of this, we introduce a novel concept of robust label distribution for scalar-valued labels, which is designed to encode the age scalars into label distribution matrices, i.e. two-dimensional Gaussian distributions along age classes and age groups respectively. Overcoming the limitations of conventional hard group boundaries in age grouping and capturing intrinsic inter-group dependency, our framework achieves robust and competitive performance over the conventional algorithms on two popular benchmarks for human age estimation.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967186"}, {"primary_key": "4164269", "vector": [], "sparse_vector": [], "title": "Novel Word Embedding and Translation-based Language Modeling for Extractive Speech Summarization.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Berlin Chen", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Word embedding methods revolve around learning continuous distributed vector representations of words with neural networks, which can capture semantic and/or syntactic cues, and in turn be used to induce similarity measures among words, sentences and documents in context. Celebrated methods can be categorized as prediction-based and count-based methods according to the training objectives and model architectures. Their pros and cons have been extensively analyzed and evaluated in recent studies, but there is relatively less work continuing the line of research to develop an enhanced learning method that brings together the advantages of the two model families. In addition, the interpretation of the learned word representations still remains somewhat opaque. Motivated by the observations and considering the pressing need, this paper presents a novel method for learning the word representations, which not only inherits the advantages of classic word embedding methods but also offers a clearer and more rigorous interpretation of the learned word representations. Built upon the proposed word embedding method, we further formulate a translation-based language modeling framework for the extractive speech summarization task. A series of empirical evaluations demonstrate the effectiveness of the proposed word representation learning and language modeling techniques in extractive speech summarization.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967246"}, {"primary_key": "4164270", "vector": [], "sparse_vector": [], "title": "Deep-based Ingredient Recognition for Cooking Recipe Retrieval.", "authors": ["<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Retrieving recipes corresponding to given dish pictures facilitates the estimation of nutrition facts, which is crucial to various health relevant applications. The current approaches mostly focus on recognition of food category based on global dish appearance without explicit analysis of ingredient composition. Such approaches are incapable for retrieval of recipes with unknown food categories, a problem referred to as zero-shot retrieval. On the other hand, content-based retrieval without knowledge of food categories is also difficult to attain satisfactory performance due to large visual variations in food appearance and ingredient composition. As the number of ingredients is far less than food categories, understanding ingredients underlying dishes in principle is more scalable than recognizing every food category and thus is suitable for zero-shot retrieval. Nevertheless, ingredient recognition is a task far harder than food categorization, and this seriously challenges the feasibility of relying on them for retrieval. This paper proposes deep architectures for simultaneous learning of ingredient recognition and food categorization, by exploiting the mutual but also fuzzy relationship between them. The learnt deep features and semantic labels of ingredients are then innovatively applied for zero-shot retrieval of recipes. By experimenting on a large Chinese food dataset with images of highly complex dish appearance, this paper demonstrates the feasibility of ingredient recognition and sheds light on this zero-shot problem peculiar to cooking recipe retrieval.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964315"}, {"primary_key": "4164271", "vector": [], "sparse_vector": [], "title": "DRIVING: Distributed Scheduling for Video Streaming in Vehicular Wi-Fi Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Video streaming has been dominating the mobile bandwidth, and is still expanding drastically. Its tremendous economic benefits have driven the automobile industry to equip vehicles with video streaming capacity. As a result, the new in-cabin Wi-Fi systems have been deployed, enabling each vehicle as a streaming hotspot on the wheels. A built-in Access Point (AP) bridges the communications between Wi-Fi devices inside and cellular networks outside. Distinct advantages offered by this system include a more powerful antenna array to improve multimedia quality, a constant energy source to power the streaming, etc. However, there exist two challenging features that may jeopardize the system performance. (1) The in-cabin Wi-Fi hotspots are mostly deployed on private vehicles, and thus are completely decentralized. (2) Video packets need to be delivered before their deadlines with small delays. Due to these features, existing algorithms may fail to efficiently schedule the in-cabin Wi-Fi video streaming. To fill the gap, we propose the Delay-awaRe dIstributed Video schedulING (DRIVING) framework. Being fully distributed and delay-aware, DRIVING not only increases the streaming goodput, but also reduces the delivery latency and deadline missing ratio. %In order to optimize this new framework, we establish cross-layer analytical models, which help us tune the framework parameters for better performance. In a typical scenario, DRIVING increases the goodput by up to 27.0%, while reducing the queueing delay and the deadline missing ratio by up to 40.0% and 38.4%, respectively.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964290"}, {"primary_key": "4164272", "vector": [], "sparse_vector": [], "title": "Deep CTR Prediction in Display Advertising.", "authors": ["<PERSON><PERSON><PERSON>", "Baigui Sun", "<PERSON><PERSON>", "Hongtao Lu", "Xian<PERSON><PERSON><PERSON>"], "summary": "Click through rate (CTR) prediction of image ads is the core task of online display advertising systems, and logistic regression (LR) has been frequently applied as the prediction model. However, LR model lacks the ability of extracting complex and intrinsic nonlinear features from handcrafted high-dimensional image features, which limits its effectiveness. To solve this issue, in this paper, we introduce a novel deep neural network (DNN) based model that directly predicts the CTR of an image ad based on raw image pixels and other basic features in one step. The DNN model employs convolution layers to automatically extract representative visual features from images, and nonlinear CTR features are then learned from visual features and other contextual features by using fully-connected layers. Empirical evaluations on a real world dataset with over 50 million records demonstrate the effectiveness and efficiency of this method.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964325"}, {"primary_key": "4164273", "vector": [], "sparse_vector": [], "title": "Micro Tells Macro: Predicting the Popularity of Micro-Videos via a Transductive Model.", "authors": ["<PERSON><PERSON> Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Micro-videos, a new form of user generated contents (UGCs), are gaining increasing enthusiasm. Popular micro-videos have enormous commercial potential in many ways, such as online marketing and brand tracking. In fact, the popularity prediction of traditional UGCs including tweets, web images, and long videos, has achieved good theoretical underpinnings and great practical success. However, little research has thus far been conducted to predict the popularity of the bite-sized videos. This task is non-trivial due to three reasons: 1) micro-videos are short in duration and of low quality; 2) they can be described by multiple heterogeneous channels, spanning from social, visual, acoustic to textual modalities; and 3) there are no available benchmark dataset and discriminant features that are suitable for this task. Towards this end, we present a transductive multi-modal learning model. The proposed model is designed to find the optimal latent common space, unifying and preserving information from different modalities, whereby micro-videos can be better represented. This latent space can be used to alleviate the information insufficiency problem caused by the brief nature of micro-videos. In addition, we built a benchmark dataset and extracted a rich set of popularity-oriented features to characterize the popular micro-videos. Extensive experiments have demonstrated the effectiveness of the proposed model. As a side contribution, we have released the dataset, codes and parameters to facilitate other researchers.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964314"}, {"primary_key": "4164274", "vector": [], "sparse_vector": [], "title": "Emotion in Context: Deep Semantic Feature Fusion for Video Emotion Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Yu-Gang <PERSON>"], "summary": "Human emotions demonstrate high correlations with certain events that consist of the interaction of objects, and are usually constrained by particular scenes. In this paper, we exploit these abundant context clues for video emotion recognition. We first compute event, object and scene scores with state-of-the-art detectors based on deep neural networks. The extracted high-level features serve as effective contextual information demonstrating what is occurring in the video, and are further integrated in a context fusion network to generate a unified representation aiming to bridge the affective gap. The contribution of this paper is three-fold: (a) we are the first to incorporate event as context for emotion recognition, and we demonstrate its superiority for emotion understanding, (b) we utilize a context fusion network to exploit a comprehensive set of high-level semantics features as contextual clues and (c) the proposed framework can realize recognition in real-time and achieves state-of-the-art performance on two challenging emotion recognition benchmarks, 50.6 on VideoEmotion and 51.8 on Ekman.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967196"}, {"primary_key": "4164275", "vector": [], "sparse_vector": [], "title": "Video eCommerce: Towards Online Video Advertising.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Xian<PERSON><PERSON><PERSON>"], "summary": "The prevalence of online videos provides an opportunity for e-commerce companies to exhibit their product ads in videos by recommendation. In this paper, we propose an advertising system named Video eCommerce to exhibit appropriate product ads to particular users at proper time stamps of videos, which takes into account video semantics, user shopping preference and viewing behavior feedback by a two-level strategy. At the first level, Co-Relation Regression (CRR) model is novelly proposed to construct the semantic association between keyframes and products. Heterogeneous information network (HIN) is adopted to build the user shopping preference from two different e-commerce platforms, Tmall and MagicBox, which alleviates the problems of data sparsity and cold start. In addition, Video Scene Importance Model (VSIM) utilizes the viewing behavior of users to embed ads at the most attractive position within the video stream. At the second level, taking the results of CRR, HIN and VSIM as the input, Heterogeneous Relation Matrix Factorization (HRMF) is applied for product advertising. Extensive evaluation on a variety of online videos from Tmall MagicBox demonstrates that Video eCommerce achieves promising performance, which significantly outperforms the state-of-the-art advertising methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964326"}, {"primary_key": "4164276", "vector": [], "sparse_vector": [], "title": "Seventh International Workshop on Human Behavior Understanding (HBU 2016).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "With advances in pattern recognition and multimedia computing, it becomes possible to analyze human behavior via multimodal sensors at varying time-scales, levels of analysis, and meaning. This ability opens up far-ranging possibilities for multimedia and multimodal interaction. Research has the, potential to endow computers with the capacity to detect and understand people's actions and activities and infer their attitudes, preferences, personality, and social relationships. This workshop brings together researchers in this rapidly emerging area and especially those concerned with behavior analysis and multimedia in children.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2980538"}, {"primary_key": "4164278", "vector": [], "sparse_vector": [], "title": "Adaptive Bitrate Selection for Video Encoding with Reduced Block Artifacts.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Blocking artifacts, commonly introduced during video encoding, are one of the major causes of reduced perceptual video quality. The trade-off between these artifacts and bitrate can be improved by adaptively selecting frames from a set of video copies encoded at different bitrates, prior to actual video encoding. We propose a new direction of constructing mixed bitrate video based on content-based image analysis on each video frame, which was posed as a problem of pre-analysis for the final video encoding step. The proposed method consists of the following steps: First, we define a simple and fast impact metric in order to identify the blocking artifacts in each frame of multiple videos, encoded at different bitrates. Based on the impact metric, we generate a blocking artifact density functions for the available bitrates, on the whole video. Finally, we define and optimize our objective function from the blocking artifact density functions in order to select a bitrate with minimum perceptual blockiness and file size for each frame. We validated our method throughout multiple types of videos, showing improved visual quality for the same file size based on commonly used quality assessment measures, such as MSU blocking, MSU blurring, SSIM, 3SSIM, and stSSIM. The reduction rates of average file size and average blocking artifact were about 4.9% and 8.3% over maximum bitrate encoding, respectively.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967227"}, {"primary_key": "4164280", "vector": [], "sparse_vector": [], "title": "Efficient Digital Holographic Image Reconstruction on Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Advanced digital holography attracts a lot of attentions for 3D visualization nowadays. The representation of digital holographic images suffers from computational inefficiency on the mobile devices due to the limited hardware for digital holographic processing. In this paper, we point out that the above critical issue deteriorates the digital holographic image representation on the mobile devices. To reduce computational complexity in digital holographic image reconstruction, we propose an efficient and effective algorithm to simplify Fresnel transforms for the mobile devices. Our algorithm outperforms previous approaches in not only smaller running time but also the better quality of the digital holographic image representation for the mobile devices.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967192"}, {"primary_key": "4164281", "vector": [], "sparse_vector": [], "title": "Deep Correlation Features for Image Style Classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a comprehensive study of deep correlation features on image style classification. Inspired by that correlation between feature maps can effectively describe image texture, we design and transform various such correlations into style vectors, and investigate classification performance brought by different variants. In addition to intra-layer correlation, we also propose inter-layer correlation and verify its benefit. Through extensive experiments on image style classification and artist classification, we demonstrate that the proposed style vectors significantly outperforms CNN features coming from fully-connected layers, as well as outperforms the state-of-the-art deep representation.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967251"}, {"primary_key": "4164285", "vector": [], "sparse_vector": [], "title": "Binary Optimized Hashing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wang", "Yu-Gang <PERSON>"], "summary": "This paper studies the problem of learning to hash, which is essentially a mixed integer optimization problem, containing both the binary hash code output and the (continuous) parameters forming the hash functions. Different from existing relaxation methods in hashing, which have no theoretical guarantees for the error bound of the relaxations, we propose binary optimized hashing (BOH), in which we prove that if the loss function is Lipschitz continuous, the binary optimization problem can be relaxed to a bound-constrained continuous optimization problem. Then we introduce a surrogate objective function, which only depends on unbinarized hash functions and does not need the slack variables transforming unbinarized hash functions to discrete functions, to approximate the relaxed objective function. We show that the approximation error is bounded and the bound is small when the problem is optimized. We apply the proposed approach to learn hash codes from either handcraft feature inputs or raw image inputs. Extensive experiments are carried out on three benchmarks, demonstrating that our approach outperforms state-of-the-arts with a significant margin on search accuracies.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964331"}, {"primary_key": "4164288", "vector": [], "sparse_vector": [], "title": "INRS Audiovisual Quality Dataset.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present the INRS audiovisual quality dataset made of 160 unique configurations for audiovisual content including various media compression and network distortion parameters such as video frame rate, quantization and noise reduction parameters, and packet loss rate. The compression and network distortion parameter range values are selected to match real-time communications use cases. The H.264 video codec in 720p resolution and the AMR-WB audio codec are used for encoding video and audio streams. Thirty observers have rated the overall audiovisual quality on the Absolute Category Rating (ACR) 5-level quality scale in a controlled environment. The dataset includes MOS values, packet loss rates measured at bit stream level for both video and audio streams, compression parameters and various packet header information. We have used open source software for producing source audiovisual sequences, end-to-end streaming and a custom video player. These tools and the dataset are free to public access for research and development purposes.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967204"}, {"primary_key": "4164289", "vector": [], "sparse_vector": [], "title": "Server Allocation for Multiplayer Cloud Gaming.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wentong Cai"], "summary": "Advances in cloud computing and GPU virtualization are allowing the game industry to move into a cloud gaming era. While shifting standalone video games to the cloud gaming mode is straightforward, adapting multiplayer online games to the cloud gaming paradigm faces unique challenges. In this paper, we consider multiplayer cloud gaming (MCG), which is the natural integration of multiplayer online gaming and cloud gaming paradigms. We formulate an MCG server allocation problem with the objective of minimizing the total server rental and bandwidth cost charged by the cloud to support an MCG session. We propose several efficient heuristics to address the MCG server allocation problem which is hard to solve optimally. We conduct extensive experiments using real Internet latency and cloud pricing data to evaluate the effectiveness of our proposed algorithms as well as several alternatives. Experimental results show that our best algorithm can achieve near-optimal cost under real-time latency constraints.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964301"}, {"primary_key": "4164290", "vector": [], "sparse_vector": [], "title": "PlaylistCreator: An Assisted Approach for Playlist Creation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this demo paper we describe PlaylistCreator, an assisted approach for supporting the creation of music playlists. Our solution allows creators to express song selection and browsing through a visual representation of their intents in a unified view, which relies on a set-based model for representing sources of songs. Creators can convey their purposes by seamlessly combining criteria for song selection from either manual or automatic sources, such as artists and albums, or similarity measures between artists or songs.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973816"}, {"primary_key": "4164291", "vector": [], "sparse_vector": [], "title": "Early Embedding and Late Reranking for Video Captioning.", "authors": ["Jianfeng Dong", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "This paper describes our solution for the MSR Video to Language Challenge. We start from the popular ConvNet + LSTM model, which we extend with two novel modules. One is early embedding, which enriches the current low-level input to LSTM by tag embeddings. The other is late reranking, for re-scoring generated sentences in terms of their relevance to a specific video. The modules are inspired by recent works on image captioning, repurposed and redesigned for video. As experiments on the MSR-VTT validation set show, the joint use of these two modules add a clear improvement over a non-trivial ConvNet + LSTM baseline under four performance metrics. The viability of the proposed solution is further confirmed by the blind test by the organizers. Our system is ranked at the 4th place in terms of overall performance, while scoring the best CIDEr-D, which measures the human-likeness of generated captions.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984064"}, {"primary_key": "4164292", "vector": [], "sparse_vector": [], "title": "WIMBY: What&apos;s in My Backyard?", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Location-aware social media is increasing being used to inform decisions in a spatiotemporal context. However, collecting, fusing, processing and merging information from different social media platforms is a challenge because of diversity of information between different platforms. Here, we present the WIMBY, which is able to access multiple social media platforms to help users answer the question \"What's in my Backyard?\". In doing so, the WIMBY helps to address the challenge of dealing with diverse social media information. It is believed that the WIMBY can be extended to include more information sources (including other social media platforms) to help inform decision makers in a wider array of applications.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973818"}, {"primary_key": "4164295", "vector": [], "sparse_vector": [], "title": "Placing Broadcast News Videos in their Social Media Context Using Hashtags.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Hongzhi Li", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the growth of social media platforms in recent years, social media is now a major source of information and news for many people around the world. In particular the rise of hashtags have helped to build communities of discussion around particular news, topics, opinions, and ideologies. However, television news programs still provide value and are used by a vast majority of the population to obtain their news, but these videos are not easily linked to broader discussion on social media. We have built a novel pipeline that allows television news to be placed in its relevant social media context, by leveraging hashtags. In this paper, we present a method for automatically collecting television news and social media content (Twitter) and discovering the hashtags that are relevant for a TV news video. Our algorithms incorporate both the visual and text information within social media and television content, and we show that by leveraging both modalities we can improve performance over single modality approaches.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2970929"}, {"primary_key": "4164296", "vector": [], "sparse_vector": [], "title": "n-Dimensional Display Interface.", "authors": ["<PERSON>"], "summary": "The n-Dimensional Display Interface is a display abstraction conceived from first principles with the intention of replacing the framebuffer as a new \"narrow waist\" for modern display pipelines.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2971476"}, {"primary_key": "4164297", "vector": [], "sparse_vector": [], "title": "SuperStreamer: Enabling Progressive Content Streaming in a Game Engine.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Law", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This technical demonstration presents the SuperStreamer project, which enables progressive game assets streaming to players while games are played, reducing the startup time required to download and start playing a cloud-based game. SuperStreamer modifies a popular game engine, Unreal Engine 4, to support developing and playing games with progressive game asset streaming. With SuperStreamer, developers can mark the minimal set of files, containing only the game content essential to start playing the game. When a player plays the game, these minimal set of files will be downloaded to the player's device. SuperStreamer also generates low resolution textures automatically when a developer publishes a game, and these low resolution textures are transmitted first into the game client. As players move through a game level, high quality textures required for the game will be downloaded. In our demo game, we are able to decrease the time taken to startup and load the first game level by around 30%.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973827"}, {"primary_key": "4164298", "vector": [], "sparse_vector": [], "title": "Data Aesthetics: The Ethics and Aesthetics of Big Data Gathering seen from the Artists Eye.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Art can reflect in depth about data (vertical) and at the same time has an aesthetical, ontological, intentional, and conscious realm (horizontal) that can communicate cross-dimensionally to the general public. In that way data can be contextualized in its being, can be made palpable and hence facilitate the communication of data into communication about data. The art exhibition of the 24th ACM MM conference will be presented at the Amsterdam Public Library (http://www.oba.nl/oba/english.html) and aims to provide art works that critically reflect on the use of data in procedural contexts. The presented art works were established in collaborations between artists and researchers that, in cases, have access to large data volumes. The presented art works make use of non-motivated and motivated functions art is valued for, as it is art in its act of resembling, expression and the presentation of the self that facilitates the representation of reality.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2993205"}, {"primary_key": "4164300", "vector": [], "sparse_vector": [], "title": "Deep Representation for Abnormal Event Detection in Crowded Scenes.", "authors": ["Ya<PERSON>ang <PERSON>", "Yuan Yuan", "<PERSON><PERSON><PERSON>"], "summary": "Abnormal event detection is extremely important, especially for video surveillance. Nowadays, many detectors have been proposed based on hand-crafted features. However, it remains challenging to effectively distinguish abnormal events from normal ones. This paper proposes a deep representation based algorithm which extracts features in an unsupervised fashion. Specially, appearance, texture, and short-term motion features are automatically learned and fused with stacked denoising autoencoders. Subsequently, long-term temporal clues are modeled with a long short-term memory (LSTM) recurrent network, in order to discover meaningful regularities of video events. The abnormal events are identified as samples which disobey these regularities. Moreover, this paper proposes a spatial anomaly detection strategy via manifold ranking, aiming at excluding false alarms. Experiments and comparisons on real world datasets show that the proposed algorithm outperforms state of the arts for the abnormal event detection problem in crowded scenes.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967290"}, {"primary_key": "4164301", "vector": [], "sparse_vector": [], "title": "Kurento: The WebRTC Modular Media Server.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we introduce Kurento Media Server: an open source WebRTC Media Server providing a toolbox of capabilities which include group communications, recording, routing, transcoding and mixing. Kurento supports a large number of media protocols such as WebRTC, plain RTP, RTSP or HTTP and bunch of codecs including VP8, VP9, H.264, H.263, OPUS, Speex, PCM or AMR. Kurento Media Server is based on a modular architecture, which makes it possible for developers to extend and customize its native capabilities with advanced media processing features such as computer vision, augmented reality or speech analysis. Kurento is ideal for WWW developers who find natural programming with its Java and JavaScript APIs following the traditional three tiered WWW development model.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973798"}, {"primary_key": "4164307", "vector": [], "sparse_vector": [], "title": "A Fast 3D Retrieval Algorithm via Class-Statistic and Pair-Constraint Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the development of 3D technologies and devices, 3D model retrieval becomes a hot research topic where multi-view matching algorithms have demonstrated satisfying performance. However, exciting works overlook the common factors among objects in a single class, and they are time consuming in retrieval processing. In this paper, a class-statistics and pair-constraint model (CSPC) method is originally proposed for 3D model retrieval, which is composed of supervised class-based statistics model and pair-constraint object retrieval model. In our CSPC model, we firstly convert view-based distance measure into object-based distance measure without falling in performance, which will advance 3D model retrieval speed. Secondly, the generality of the distribution of each feature dimension in each class is computed to judge category information, and then we further adopt this distribution information to build class models. Finally, an object-based pairwise constraint is introduced on the base of the class-statistic measure, which can remove a lot of false alarm samples in retrieval. Experimental results on ETH, NTU-60, MVRED and PSB 3D datasets show that our method is fast, and its performance is also comparable with the-state-of-the-art algorithms.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967194"}, {"primary_key": "4164308", "vector": [], "sparse_vector": [], "title": "Morph: A Fast and Scalable Cloud Transcoding System.", "authors": ["Guanyu Gao", "<PERSON><PERSON><PERSON>"], "summary": "Morph is an open source cloud transcoding system. It can leverage the scalability of the cloud infrastructure to encode and transcode video contents in fast speed, and dynamically provision the resources in cloud to accommodate the workload. The system is composed of a master node that performs the video file segmentation, concentration, and task scheduling operations; and multiple worker nodes that perform the transcoding for video blocks. Morph can transcode the video blocks of a video file on multiple workers in parallel to achieve fast speed, and automatically manage the data transfers and communications between the master node and the worker nodes. The worker nodes can join into or leave the transcoding cluster at any time for dynamic resource provisioning. The system is very modular, and all of the algorithms can be easily modified or replaced. We release the source code of Morph under MIT License, hoping that it can be shared among various research communities.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973792"}, {"primary_key": "4164309", "vector": [], "sparse_vector": [], "title": "Dynamic Resource Provisioning with QoS Guarantee for Video Transcoding in Online Video Sharing Service.", "authors": ["Guanyu Gao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Video transcoding is widely adopted in online video sharing services to encode video content into multiple representations. This solution, however, could consume huge amount of computing resource and incur excessive processing delays. Moreover, content has heterogeneous QoS requirements for transcoding. Some content must be transcoded in real time, while some are deferrable for transcoding. It needs to determine the strategy for intelligently provisioning the right amount of resource under dynamic workload to meet the heterogeneous QoS requirements. To this end, this paper develops a robust dynamic resource provisioning scheme for transcoding with heterogeneous QoS criteria. We adopt the Preemptive Resume Priority discipline for scheduling, so that the transcoding-deferrable content can utilize idle resources for transcoding to maximize resource utilization while remain transparent to delay-sensitive content. We leverage Model Predictive Control to design the online algorithm for dynamic resource provisioning using predictions to accommodate time-varying workload. To seek robustness of system performance against prediction noises, we improve our online algorithm through Robust Design. The experiment results in a real environment demonstrate that our proposed framework can achieve the QoS requirements while reducing 50% of resource consumption on average.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964296"}, {"primary_key": "4164311", "vector": [], "sparse_vector": [], "title": "Tamp: A Library for Compact Deep Neural Networks with Structured Matrices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce Tamp, an open source C++ library for reducing the space and time costs of deep neural network models. In particular, <PERSON><PERSON> implements several recent works which use structured matrices to replace unstructured matrices which are often bottlenecks in neural networks.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973802"}, {"primary_key": "4164312", "vector": [], "sparse_vector": [], "title": "GeoTracks: Adaptive Music for Everyday Journeys.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Listening to music on the move is an everyday activity for many people. This paper proposes geotracks and geolists, music tracks and playlists of existing music that are aligned and adapted to specific journeys. We describe how everyday walking journeys such as commutes to work and existing popular music tracks can each be analysed, decomposed and then brought together, using musical adaptations including skipping and repeating parts of tracks, dynamically remixing tracks and cross-fades. Using a naturalistic experiment we compared walking while listening to geotracks (dynamically adapted using GPS location information) to walking while listening to a fixed playlist. Overall, participants enjoyed the walk more when listening to the adaptive geotracks. However adapting the lengths of tracks appeared to detract from the experience of the music in some situations and for some participants, revealing trade-offs in achieving fine-grained alignment of music and walking journeys.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967181"}, {"primary_key": "4164313", "vector": [], "sparse_vector": [], "title": "Supervised Recurrent Hashing for Large Scale Video Retrieval.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Hashing for large-scale multimedia is a popular research topic, attracting much attention in computer vision and visual information retrieval. Previous works mostly focus on hashing the images and texts while the approaches designed for videos are limited. In this paper, we propose a \\textit{Supervised Recurrent Hashing} (SRH) that explores the discriminative representation obtained by deep neural networks to design hashing approaches. The long-short term memory (LSTM) network is deployed to model the structure of video samples. The max-pooling mechanism is introduced to embedding the frames into fixed-length representations that are fed into supervised hashing loss. Experiments on UCF-101 dataset demonstrate that the proposed method can significantly outperforms several state-of-the-art methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967225"}, {"primary_key": "4164315", "vector": [], "sparse_vector": [], "title": "LIME: A Method for Low-light IMage Enhancement.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "When one captures images in low-light conditions, the images often suffer from low visibility. This poor quality may significantly degrade the performance of many computer vision and multimedia algorithms that are primarily designed for high-quality inputs. In this paper, we propose a very simple and effective method, named as LIME, to enhance low-light images. More concretely, the illumination of each pixel is first estimated individually by finding the maximum value in R, G and B channels. Further, we refine the initial illumination map by imposing a structure prior on it, as the final illumination map. Having the well-constructed illumination map, the enhancement can be achieved accordingly. Experiments on a number of challenging real-world low-light images are present to reveal the efficacy of our LIME and show its superiority over several state-of-the-arts.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967188"}, {"primary_key": "4164316", "vector": [], "sparse_vector": [], "title": "Attention-based LSTM with Semantic Consistency for Videos Captioning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent progress in using Long Short-Term Memory (LSTM) for image description has motivated the exploration of their applications for automatically describing video content with natural language sentences. By taking a video as a sequence of features, LSTM model is trained on video-sentence pairs to learn association of a video to a sentence. However, most existing methods compress an entire video shot or frame into a static representation, without considering attention which allows for salient features. Furthermore, most existing approaches model the translating error, but ignore the correlations between sentence semantics and visual content.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967242"}, {"primary_key": "4164317", "vector": [], "sparse_vector": [], "title": "LTA 2016: The First Workshop on Lifelogging Tools and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The organisation of personal data is receiving increasing research attention due to the challenges we face in gathering, enriching, searching, and visualising such data. Given the increasing ease with which personal data being gathered by individuals, the concept of a lifelog digital library of rich multimedia and sensory content for every individual is fast becoming a reality. The LTA~2016 workshop aims to bring together academics and practitioners to discuss approaches to lifelog data analytics and applications; and to debate the opportunities and challenges for researchers in this new and challenging area.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2980534"}, {"primary_key": "4164318", "vector": [], "sparse_vector": [], "title": "Analyzing and Predicting GIF Interestingness.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Animated GIFs have regained huge popularity. They are used in instant messaging, online journalism, social media, among others. In this paper, we present an in-depth study on the interestingness of GIFs. We create and annotate a dataset with a set of affective labels, which allows us to investigate the sources of interest. We show that GIFs of pets are considered more interesting that GIFs of people. Furthermore, we study the connection of interest to other features and factors such as popularity. Finally, we build a predictive model and show that it can estimate GIF interestingness with high accuracy. Our model outperforms the existing methods on GIF popularity, as well as a model based on still image interestingness, by a large margin. We envision that the insights and method developed can be used for automatic recognition and generation of interesting GIFs.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967195"}, {"primary_key": "4164321", "vector": [], "sparse_vector": [], "title": "Cross-modal Retrieval by Real Label Partial Least Squares.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Ma", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper proposes a novel method named Real Label Partial Least Squares (RL-PLS) for the task of cross-modal retrieval. Pervious works just take the texts and images as two modalities in PLS. But in RL-PLS, considering that the class label is more related to the semantics directly, we take the class label as the assistant modality. Specially, we build two KPLS models and project both images and texts into the label space. Then, the similarity of images and texts can be measured more accurately in the label space. Furthermore, we do not restrict the label indicator values as the binary values as the traditional methods. By contraries, in RL-PLS, the label indicator values are set to the real values. Specially, the label indicator values are comprised by two parts: positive or negative represents the sample class while the absolute value represents the local structure in the class. By this way, the discriminate ability of RL-PLS is improved greatly. To show the effectiveness of RL-PLS, the experiments are conducted on two cross-modal retrieval tasks (Wiki and Pascal Voc2007), on which the competitive results are obtained.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967216"}, {"primary_key": "4164322", "vector": [], "sparse_vector": [], "title": "An Intention-Aware Interactive System for Mobile Video Browsing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Video browsing describes an interactive process where the user wants to find a target shot in a long video. Therefore, it is crucial for a video browsing system to be fast and accurate with minimum user effort. Previously, video browsing systems suffered from either lots of user efforts or heavy computation cost, which makes them less effective. We propose an RNN-based \\textit{intention-aware} system to overcome these limitations. At each interactive round, the user only needs to select one of the displayed shots that is most visually similar to her mental target and then the user's choice will further tailor the search to the target. The search model update merely requires vector inner products which means the system is highly responsive.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973814"}, {"primary_key": "4164323", "vector": [], "sparse_vector": [], "title": "A Digital World to Thrive In: How the Internet of Things Can Make the &quot;Invisible Hand&quot; Work.", "authors": ["<PERSON>"], "summary": "Managing data-rich societies wisely and reaching sustainable development are among the greatest challenges of the 21st century. We are faced with existential threats and huge opportunities. If we don't act now, large parts of our society will not be able to economically benefit from the digital revolution. This could lead to mass unemployment and social unrest. It is time to create the right framework for the digital society to come.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984749"}, {"primary_key": "4164325", "vector": [], "sparse_vector": [], "title": "Mental Visual Indexing: Towards Fast Video Browsing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Video browsing describes an interactive process where users want to find a target shot in a long video. Therefore, it is crucial for a video browsing system to be fast and accurate with minimum user effort. In sharp contrast to traditional Relevance Feedback (RF), we propose a novel paradigm for fast video browsing dubbed Mental Visual Indexing (MVI). At each interactive round, the user only needs to select one of the displayed shots that is most visually similar to her mental target and then the user's choice will further tailor the search to the target. The search model update given a user feedback only requires vector inner products, which makes MVI highly responsive. MVI is underpinned by a sequence model in terms of Recurrent Neural Network (RNN), which is trained by automatically generated shot sequences from a rigorous Bayesian framework, which simulates user feedback process. Experimental results on three 3-hour movies conducted by real users demonstrate the effectiveness of the proposed approach.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967296"}, {"primary_key": "4164326", "vector": [], "sparse_vector": [], "title": "Multimodal Learning via Exploring Deep Semantic Similarity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ng Li"], "summary": "Deep learning is skilled at learning representation from raw data, which are embedded in the semantic space. Traditional multimodal networks take advantage of this, and maximize the joint distribution over the representations of different modalities. However, the similarity among the representations are not emphasized, which is an important property for multimodal data. In this paper, we will introduce a novel learning method for multimodal networks, named as Semantic Similarity Learning (SSL), which aims at training the model via enhancing the similarity between the high-level features of different modalities. Sets of experiments are conducted for evaluating the method on different multimodal networks and multiple tasks. The experimental results demonstrate the effectiveness of SSL in keeping the shared information and improving the discrimination. Particularly, SSL shows its ability in encouraging each modality to learn transferred knowledge from the other one when faced with missing data.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967239"}, {"primary_key": "4164327", "vector": [], "sparse_vector": [], "title": "Smart Beholder: An Extensible Smart Lens Platform.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Smart Lenses refer to detachable, orientable and zoomable lenses that stream live videos over wireless networks to heterogeneous computing devices, including tablets and smartphones. Various novel applications are made possible by smart lenses, including mobile photography, smart surveillance cameras, and Unmanned Aerial Vehicle (UAV) cameras. However, to our best knowledge, existing smart lenses are closed and proprietary, and thus we initiate an open-source project called Smart Beholder for end-to-end solutions of smart lenses. The code and documents of Smart Beholder can be found at our website http://www.smartbeholder.org. Our Smart Beholder platform are useful to researchers for fast prototyping, developers for rapid development, and amateurs for hobbies. We have implemented Smart Beholder server (camera) using a popular embedded Linux platform, called Raspberry Pi. We have also realized Smart Beholder client (controller) on various OS's, including Android. Our experimental results show the practicality and efficiency of our proposed Smart Beholder: we outperform commercial products in the market in terms of both objective and subjective metrics. We believe the release of Smart Beholder will stimulate future studies on novel multimedia applications enabled by smart lenses.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973793"}, {"primary_key": "4164328", "vector": [], "sparse_vector": [], "title": "Detecting Arbitrary Oriented Text in the Wild with a Visual Attention Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Text embedded in images provides important semantic information about a scene and its content. Detecting text in an unconstrained environment is a challenging task because of the many fonts, sizes, backgrounds, and alignments of the characters. We present a novel attention model for detecting arbitrary oriented and curved scene text. Inspired by the attention mechanisms in the human visual system, our model utilizes a spatial glimpse network to processes the attended area and deploys a recurrent neural network that aggregates the information over time to determine the attention movement. Combining this with an off-the-shelf region proposal method, the model achieves the state-of-the-art performance on the highly cited ICDAR2013 dataset, and the MSRA-TD500 dataset which contains arbitrary oriented text.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967282"}, {"primary_key": "4164329", "vector": [], "sparse_vector": [], "title": "StressClick: <PERSON><PERSON> from Gaze-Click Patterns.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Stress sensing is valuable in many applications, including online learning crowdsourcing and other daily human-computer interactions. Traditional affective computing techniques investigate affect inference based on different individual modalities, such as facial expression, vocal tones, and physiological signals or the aggregation of signals of these independent modalities, without explicitly exploiting their inter-connections. In contrast, this paper focuses on exploring the impact of mental stress on the coordination between two human nervous systems, the somatic and autonomic nervous systems. Specifically, we present the analysis of the subtle but indicative pattern of human gaze behaviors surrounding a mouse-click event, i.e. the gaze-click pattern. Our evaluation shows that mental stress affects the gaze-click pattern, and this influence has largely been ignored in previous work. This paper, therefore, further proposes a non-intrusive approach to inferring human stress level based on the gaze-click pattern, using only data collected from the common computer webcam and mouse. We conducted a human study on solving math questions under different stress levels to explore the validity of stress recognition based on this coordination pattern. Experimental results show the effectiveness of our technique and the generalizability of the proposed features for user-independent modeling. Our results suggest that it may be possible to detect stress non-intrusively in the wild, without the need for specialized equipment.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964318"}, {"primary_key": "4164331", "vector": [], "sparse_vector": [], "title": "A Multi-Video Browser for Endoscopic Videos on Tablets.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a browser for endoscopic videos that is designed to easily navigate and compare scenes on a tablet. It utilizes frame stripes of different levels of detail to quickly switch between fast and detailed navigation. Moreover, it uses saliency methods to determine which areas of a given keyframe contain the most information to further improve the visualization of the frame stripes. As scenes with much movement can be non-relevant out-of-patient scenes, the tool supports filtering for scenes of low, medium and high motion. The tool can be especially useful for patient debriefings as well as for educational purposes.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973821"}, {"primary_key": "4164332", "vector": [], "sparse_vector": [], "title": "A Tablet Annotation Tool for Endoscopic Videos.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a tool for mobile browsing and annotation tailored for endoscopic videos. Professional users can utilize this tablet app for patients debriefings or educational purposes. It supports text input, free-hand and shape drawing as well as angle measurements, e.g. for comparing instrument orientation. It is possible to annotate single frames as well as user-defined video sections. Moreover, it provides easy and efficient navigation via a zoom-able navigation bar that is based on frame stripes. Frame stripes are created by extracting a single, one pixel wide vertical stripe from every keyframe. The stripes are then arranged next to each other to form a uniform bar. This gives users great overview of the content of a given video. Furthermore, the app supports creation of custom reports based on the entered annotations that can be directly mailed or printed for further usage.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973822"}, {"primary_key": "4164333", "vector": [], "sparse_vector": [], "title": "Ensemble of Sparse Cross-Modal Metrics for Heterogeneous Face Recognition.", "authors": ["<PERSON>", "<PERSON>", "Yinghuan Shi", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Heterogeneous face recognition aims to identify or verify person identity by matching facial images of different modalities. In practice, it is known that its performance is highly influenced by modality inconsistency, appearance occlusions, illumination variations and expressions. In this paper, a new method named as ensemble of sparse cross-modal metrics is proposed for tackling these challenging issues. In particular, a weak sparse cross-modal metric learning method is firstly developed to measure distances between samples of two modalities. It learns to adjust rank-one cross-modal metrics to satisfy two sets of triplet based cross-modal distance constraints in a compact form. Meanwhile, a group based feature selection is performed to enforce that features in the same position of two modalities are selected simultaneously. By neglecting features that attribute to \"noise\" in the face regions (eye glasses, expressions and so on), the performance of learned weak metrics can be markedly improved. Finally, an ensemble framework is incorporated to combine the results of differently learned sparse metrics into a strong one. Extensive experiments on various face datasets demonstrate the benefit of such feature selection especially when heavy occlusions exist. The proposed ensemble metric learning has been shown superiority over several state-of-the-art methods in heterogeneous face recognition.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964311"}, {"primary_key": "4164334", "vector": [], "sparse_vector": [], "title": "A New Tool for Collaborative Video Search via Content-based Retrieval and Visual Inspection.", "authors": ["<PERSON>", "<PERSON><PERSON>non <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a new approach for collaborative video search and video browsing relying on a combination of traditional, index-based video retrieval complemented with large-scale human-based visual inspection. In particular, a traditional PC interface is used for query-based search using advanced indexing and querying methods (e.g., concept search), whereas a visualization of the database on a tablet is used for pure human-based browsing. Both parts are coupled to compensate for mutual disadvantages; human visual inspection allows for a better, more detailed analysis of the data - also bridging the semantic gap - but comes at the price of an unfiltered database - a disadvantage that is resolved by using the results from the query engine to change visualization order.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973824"}, {"primary_key": "4164335", "vector": [], "sparse_vector": [], "title": "Vibrotactile Experiences for Augmented Reality.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This demo illustrates several use cases for utilizing vibrotactile feedback, i.e., tactile sensations created by small, body-worn vibration motors, in an augmented reality setting with head-mounted displays. In particular, we address how such feedback can be used to create different experiences and also how it can be applied to provide additional information to a user that might be beneficial when performing certain tasks, such as interactions requiring a high accuracy that is hard to achieve with pure visual feedback (e.g., correct placement of virtual objects at a specific location in the real world).", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973830"}, {"primary_key": "4164337", "vector": [], "sparse_vector": [], "title": "Adaptation of Word Vectors using Tree Structure for Visual Semantics.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose a framework of word-vector adaptation, which makes vectors of visually similar concepts close to each other. Here, word vectors are real-valued vector representation of words, e.g., word2vec representation. Our basic idea is to assume that each concept has some hypernyms that are important to determine its visual features. For example, for a concept Swallow with hypernyms <PERSON>, Animal and Entity, we believe Bird is the most important since birds have common visual features with their feathers etc. Adapted word vectors are obtained for each word by taking a weighted sum of a given original word vector and its hypernym word vectors. Our weight optimization makes vectors of visually similar concepts close to each other, by giving a large weight for such important hypernyms. We apply the adapted word vectors to zero-shot learning on the TRECVID 2014 semantic indexing dataset. We achieved 0.083 of Mean Average Precision, which is the best performance without using TRECVID training data to the best of our knowledge.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967226"}, {"primary_key": "4164339", "vector": [], "sparse_vector": [], "title": "Artist-based Classification via Deep Learning with Multi-scale Weighted Pooling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "For analyzing digital images of paintings we propose a new approach to categorize them based on artist. Determining the authorship of a painting is challenging because common subjects are illustrated in paintings, and paintings of an artist may not have a unique style. The proposed approach is built upon convolutional neural networks (CNN)---a class of biologically inspired vision model that recently demonstrates near-human performance on several visual recognition tasks. However, training a CNN model requires large scale training data of a fixed input image size (e.g. 224 * 224). In this paper, we propose to construct a multi-layer pyramid from an image, providing 21X more features than using a single layer (i.e., the original image) alone. We train a CNN model for each layer, and propose a new weighted fusion scheme to adaptively combine the decision results. To evaluate the proposed methods, we collect a new painting image dataset, categorized into 13 artists. As demonstrated in the experimental results, the proposed method achieves a promising result---88.08% recall rate in top-2 retrieval on the challenging classification task.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967299"}, {"primary_key": "4164340", "vector": [], "sparse_vector": [], "title": "Jockey Time: Making Video Playback to Enhance Emotional Effect.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>on-<PERSON>ong Suk"], "summary": "In order to effectively and easily deliver the affective quality of a video, this study investigated the emotional manifests induced by the playback design of the video. In designing the playback, we articulated speed, direction, and continuity of the video and surveyed observers' responses. Based on the results, we propose seven categories of playback design, and each appeals cheerful, happy, relaxed, funny, urgent, angry, and sad emotion. For an easy use, we offer an online video editing service, \"Jockey Time.\" A beta version was operated for a month for monitoring purpose, and finally, Jockey Time v.1.0 is now launched for anybody to easily enhance the emotional effect of one's video.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967183"}, {"primary_key": "4164341", "vector": [], "sparse_vector": [], "title": "A Supervised Approach for Text Illustration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Midhun Gundapuneni", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we propose a novel method to illustrate text articles with pictures from a tagged repository. Certain types of documents, like news articles, are often accompanied by a few pictures only. Prior works leverage topics or key phrases from the text to suggest relevant pictures. We propose a supervised model based on features like readability, picturability, sentiment polarity, and presence of important phrases, to identify and rank key sentences. The proposed method then suggests some relevant pictures based on the top ranked sentences thus identified.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967214"}, {"primary_key": "4164342", "vector": [], "sparse_vector": [], "title": "Deep Bi-directional Cross-triplet Embedding for Cross-Domain Clothing Retrieval.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Yun Fu"], "summary": "In this paper, we address two practical problems when shopping online: 1) What will I look like when wearing this clothing on the street? 2) How to find the exact same or similar clothing that other people are wearing on the street or in a movie? In this paper, we jointly solve these two problems with one bi-directional shop-to-street street-to-shop clothing retrieval framework. There are three main challenges of cross-domain clothing retrieval task. First is to learn the discrepancy (e.g., background, pose, illumination) between street domain and shop domain clothing. Second, both intra-domain and cross-domain similarity need to be considered during feature embedding. Third, there is large bias between the number of matched and non-matched street and shop pairs. To solve these challenges, in this paper, we propose a deep bi-directional cross-triplet embedding algorithm by extending the start-of-the-art triplet embedding into cross-domain retrieval scenario. Extensive experiments demonstrate the effectiveness of the proposed algorithm.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967182"}, {"primary_key": "4164343", "vector": [], "sparse_vector": [], "title": "Describing Videos using Multi-modal Fusion.", "authors": ["Qin Jin", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Describing videos with natural language is one of the ultimate goals of video understanding. Video records multi-modal information including image, motion, aural, speech and so on. MSR Video to Language Challenge provides a good chance to study multi-modality fusion in caption task. In this paper, we propose the multi-modal fusion encoder and integrate it with text sequence decoder into an end-to-end video caption framework. Features from visual, aural, speech and meta modalities are fused together to represent the video contents. Long Short-Term Memory Recurrent Neural Networks (LSTM-RNNs) are then used as the decoder to generate natural language sentences. Experimental results show the effectiveness of multi-modal fusion encoder trained in the end-to-end framework, which achieved top performance in both common metrics evaluation and human evaluation.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984065"}, {"primary_key": "4164344", "vector": [], "sparse_vector": [], "title": "A Discriminative and Compact Audio Representation for Event Detection.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper presents a novel two-phase method for audio representation: Discriminative and Compact Audio Representation (DCAR). In the first phase, each audio track is modeled using a Gaussian mixture model (GMM) that includes several components to capture the variability within that track. The second phase takes into account both global structure and local structure. In this phase, the components are rendered more discriminative and compact by formulating an optimization problem on Grassmannian manifolds, which we found represents the structure of audio effectively. Experimental results on the YLI-MED dataset show that the proposed DCAR representation consistently outperforms state-of-the-art audio representations: i-vector, mv-vector, and GMM.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2970377"}, {"primary_key": "4164345", "vector": [], "sparse_vector": [], "title": "Hypervideo Production Using Crowdsourced Youtube Videos.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Hypervideos, consisting of media enriched and linked video scenes, have proven useful in many scenarios. Software solutions exist that help authors make hypervideos from media files. However, recording and editing video scenes for hypervideos is a tedious and time consuming job. Huge video databases like YouTube exist that can provide rich sources of video material. Yet it is often illegal to download and re-purpose videos from these sites, requiring a solution that links whole videos or parts of videos and plays them in an embedded player. This work presents the SIVA Web Producer, a Chrome extension for the creation of hypervideos consisting of scenes from YouTube videos. After creating a project, the SIVA Web Producer embeds YouTube videos or parts thereof as video clips. These can then be linked in a scene graph and extended with annotations. The plug-in provides a preview space for testing the hypervideo. Finalized videos can be published on the SIVA Web Portal or embedded in a Web page.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973810"}, {"primary_key": "4164346", "vector": [], "sparse_vector": [], "title": "Crowdsourcing Biodiversity Monitoring: How Sharing your Photo Stream can Sustain our Planet.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Large scale biodiversity monitoring is essential for sustainable development (earth stewardship). With the recent advances in computer vision, we see the emergence of more and more effective identification tools allowing to set-up large-scale data collection platforms such as the popular [email protected] initiative that allow to reuse interaction data. Although it covers only a fraction of the world flora, this platform is already being used by more than 300K people who produce tens of thousands of validated plant observations each year. This explicitly shared and validated data is only the tip of the iceberg. The real potential relies on the millions of raw image queries submitted by the users of the mobile application for which there is no human validation. People make such requests to get information on a plant along a hike or something they find in their garden but not know anything about. Allowing the exploitation of such contents in a fully automatic way could scale up the world-wide collection of implicit plant observations by several orders of magnitude, which can complement the explicit monitoring efforts. In this paper, we first survey existing automated plant identification systems through a five-year synthesis of the PlantCLEF benchmark and an impact study of the [email protected] platform. We then focus on the implicit monitoring scenario and discuss related research challenges at the frontier of computer science and biodiversity studies. Finally, we discuss the results of a preliminary study focused on implicit monitoring of invasive species in mobile search logs. We show that the results are promising but that there is room for improvement before being able to automatically share implicit observations within international platforms.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2976762"}, {"primary_key": "4164347", "vector": [], "sparse_vector": [], "title": "Deep Cross Residual Learning for Multitask Visual Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Residual learning has recently surfaced as an effective means of constructing very deep neural networks for object recognition. However, current incarnations of residual networks do not allow for the modeling and integration of complex relations between closely coupled recognition tasks or across domains. Such problems are often encountered in multimedia applications involving large-scale content recognition. We propose a novel extension of residual learning for deep networks that enables intuitive learning across multiple related tasks using cross-connections called cross-residuals. These cross-residuals connections can be viewed as a form of in-network regularization and enables greater network generalization. We show how cross-residual learning (CRL) can be integrated in multitask networks to jointly train and detect visual concepts across several tasks. We present a single multitask cross-residual network with >40% less parameters that is able to achieve competitive, or even better, detection performance on a visual sentiment concept detection problem normally requiring multiple specialized single-task networks. The resulting multitask cross-residual network also achieves better detection performance by about 10.4% over a standard multitask residual network without cross-residuals with even a small amount of cross-task weighting.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964309"}, {"primary_key": "4164348", "vector": [], "sparse_vector": [], "title": "Adaptive Visual Feedback Generation for Facial Expression Improvement with Multi-task Deep Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While many studies in computer vision and pattern recognition have been actively conducted to recognize people's current states, few studies have tackled the problem of generating feedback on how people can improve their states, although there are many real-world applications such as in sports, education, and health care. In particular, it has been challenging to develop such a system that can adaptively generate feedback for real-world situations, namely various input and target states, since it requires formulating various rules of feedback to do so. We propose a learning-based method to solve this problem. If we can obtain a large amount of feedback annotations, it is possible to explicitly learn the rules, but it is difficult to do so due to the subjective nature of the task. To mitigate this problem, our method implicitly learns the rules from training data consisting of input images, key-point annotations, and state annotations that do not require professional knowledge in feedback. Given such training data, we first learn a multi-task deep neural network with state recognition and key-point localization. Then, we apply a novel propagation method for extracting feedback information from the network. We evaluated our method in a facial expression improvement task using real-world data and clarified its characteristics and effectiveness.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967236"}, {"primary_key": "4164349", "vector": [], "sparse_vector": [], "title": "News Program Detection in TV Broadcast Videos.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Television news channels broadcast different kinds of content like debates, interviews, commercials along with news presentations. Real-time detection of these news programs or their retrieval from large volumes of stored broadcast videos is a challenging problem and is a necessary first step for broadcast analytics. News program detection is even harder in Indian context where closed caption text or program markers are not provided by TV news channels (not mandated by law). We propose a two-stage approach to classify news video segments. First, broadcast video shots are classified with multiple labels based on a set of audio-visual features. Second, sequences of these shot features are modeled to detect news programs. Another contribution of this work is the construction of a dataset of 120 hours of shot categories and news programs from Indian English news channels. We have experimented with SVM, HMM and CRF based classifiers and achieved a F1 score of 99% in detecting news programs while experimenting on our dataset.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967281"}, {"primary_key": "4164350", "vector": [], "sparse_vector": [], "title": "Generating Affective Captions using Concept And Syntax Transition Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The area of image captioning i.e. the automatic generation of short textual descriptions of images has experienced much progress recently. However, image captioning approaches often only focus on describing the content of the image without any emotional or sentimental dimension which is common in human captions. This paper presents an approach for image captioning designed specifically to incorporate emotions and feelings into the caption generation process. The presented approach consists of a Deep Convolutional Neural Network (CNN) for detecting Adjective Noun Pairs in the image and a graphical network architecture called \"Concept And Syntax Transition (CAST)\" network for generating sentences from these detected concepts.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984070"}, {"primary_key": "4164351", "vector": [], "sparse_vector": [], "title": "A Platform for Building New Human-Computer Interface Systems that Support Online Automatic Recognition of Audio-Gestural Commands.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new framework to build human-computer interfaces that provide online automatic audio-gestural command recognition. The overall system allows the construction of a multimodal interface that recognizes user input expressed naturally as audio commands and manual gestures, captured by sensors such as Kinect. It includes a component for acquiring multimodal user data which is used as input to a module responsible for training audio-gestural models. These models are employed by the automatic recognition component, which supports online recognition of audio-visual modalities. The overall framework is exemplified by a working system use case. This demonstrates the potential of the overall software platform, which can be employed to build other new human-computer interaction systems. Moreover, users may populate libraries of models and/or data, that can be shared in the network. In this way users may reuse or extend existing systems.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973794"}, {"primary_key": "4164352", "vector": [], "sparse_vector": [], "title": "Fast Supervised LDA for Discovering Micro-Events in Large-Scale Video Datasets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper introduces fsLDA, a fast variational inference method for supervised LDA, which overcomes the computational limitations of the original supervised LDA and enables its application in large-scale video datasets. In addition to its scalability, our method also overcomes the drawbacks of standard, unsupervised LDA for video, including its focus on dominant but often irrelevant video information (e.g. background, camera motion). As a result, experiments in the UCF11 and UCF101 datasets show that our method consistently outperforms unsupervised LDA in every metric. Furthermore, analysis shows that class-relevant topics of fsLDA lead to sparse video representations and encapsulate high-level information corresponding to parts of video events, which we denote \"micro-events\".", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967237"}, {"primary_key": "4164354", "vector": [], "sparse_vector": [], "title": "Exploration of Large Image Corpuses in Virtual Reality.", "authors": ["San<PERSON> Khanwalkar", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the increasing capture of photos and their proliferation on social media, there is a pressing need for a more intuitive and versatile image search and exploration system. Image search systems have long been confined to the binds of the 2D legacy screens and the keyword text-box. With the recent advances in Virtual Reality (VR) technology, a move towards an immersive VR environment will redefine the image navigation experience. To this end, we propose a VR platform that gathers images from various sources, and addresses the 5 Ws of image search - what, where, when, who and why. We achieve this by providing the user with two modes of interactive exploration - (i) A mode that allows for a graph based navigation of an image dataset, using a steering wheel visualization, along multiple dimensions of time, location, visual concept, people, etc. and (ii) Another mode that provides an intuitive exploration of the image dataset using a logical hierarchy of visual concepts. Our contributions include creating a VR image exploration experience that is intuitive and allows image navigation along multiple dimensions.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967291"}, {"primary_key": "4164355", "vector": [], "sparse_vector": [], "title": "Micro-Expression Recognition with Expression-State Constrained Spatio-Temporal Feature Representations.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recognizing spontaneous micro-expression in video sequences is a challenging problem. In this paper, we propose a new method of small scale spatio-temporal feature learning. The proposed learning method consists of two parts. First, the spatial features of micro-expressions at different expression-states (i.e., onset, onset to apex transition, apex, apex to offset transition and offset) are encoded using convolutional neural networks (CNN). The expression-states are taken into account in the objective functions, to improve the expression class separability of the learned feature representation. Next, the learned spatial features with expression-state constraints are transferred to learn temporal features of micro-expression. The temporal feature learning encodes the temporal characteristics of the different states of the micro-expression using long short-term memory (LSTM) recurrent neural networks. Extensive and comprehensive experiments have been conducted on the publically available CASME II micro-expression dataset. The experimental results showed that the proposed method outperformed state-of-the-art micro-expression recognition methods in terms of recognition accuracy.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967247"}, {"primary_key": "4164357", "vector": [], "sparse_vector": [], "title": "Ad Recommendation for Sponsored Search Engine via Composite Long-Short Term Memory.", "authors": ["Dejiang Kong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yu<PERSON>"], "summary": "Search engine logs contain a large amount of users' click-through data that can be leveraged as implicit indicators of relevance. In this paper we address ad recommendation problem that finding and ranking the most relevant ads with respect to users' search queries. Due to the click sparsity, the conventional methods can hardly model the both inter- and intra-relations among users, queries and ads. We utilize the long-short term memory(LSTM) network to effectively encode two kinds of sequences: the (user, query) sequence and the query word sequence to represent users' query intention in a continuous vector space and decode them as distributions over ads respectively. Further more, we combine these two LSTM networks in an appropriate way to build up a more robust model referred as composite LSTM model(cLSTM) for ad recommendation. We evaluate the proposed cLSTM on real world click-through data set comparing with two baseline methods, the results demonstrate that our proposed model outperforms two baselines and mitigate the click sparsity problem to a certain degree.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967254"}, {"primary_key": "4164359", "vector": [], "sparse_vector": [], "title": "SenseCap: Synchronized Data Collection with Microsoft Kinect2 and LeapMotion.", "authors": ["<PERSON>. <PERSON>"], "summary": "We present a new recording tool to capture synchronized video and skeletal data streams from cheap sensors such as the Microsoft Kinect2, and LeapMotion. While other recording tools act as virtual playback devices for testing on-line real-time applications, we target multi-media data collection for off-line processing. Images are encoded in common video formats, and skeletal data as flat text tables. This approach enables long duration recordings (e.g. over 30 minutes), and supports post-hoc mapping of the Kinect2 depth video to the color space if needed. By using common file formats, the data can be played back and analyzed on any other computer, without requiring sensor specific SDKs to be installed. The project is released under a 3-clause BSD license, and consists of an extensible C++11 framework, with support for the official Microsoft Kinect 2 and LeapMotion APIs to record, a command-line interface, and a Matlab GUI to initiate, inspect, and load Kinect2 recordings.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973805"}, {"primary_key": "4164361", "vector": [], "sparse_vector": [], "title": "SuperSelect: An Interactive Superpixel-Based Segmentation Method for Touch Displays.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present the concept of an interactive image segmentation method, which allows a fast and precise extraction of foreground objects from natural images. The method is especially suited for mobile, touch-based devices. The approach combines automatic image segmentation with interactive refinement. In a first step, the user extracts an object using the first stage of the GrabCut algorithm. Any errors in the resulting segmentation can then be corrected via a novel correction step in which the underlying image is over-segmented into superpixels using the SLIC algorithm. The user can select individual superpixels in order to adjust the image segmentation. This image segmentation method is implemented as a full working demo on a Microsoft Surface Pro 3.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973819"}, {"primary_key": "4164362", "vector": [], "sparse_vector": [], "title": "A Fast Cattle Recognition System using Smart devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A recognition system is very useful to recognize human, object, and animals. An animal recognition system plays an important role in livestock biometrics, that helps in recognition and verification of livestock in case of missed or swapped animals, false insurance claims, and reallocation of animals at slaughter houses. In this research, we propose a fast and cost-effective animal biometrics based cattle recognition system to quickly recognize and verify the false insurance claims of cattle using their primary muzzle point image pattern characteristics. To solve this major problem, users (owner, parentage, or other) have captured the images of cattle using their smart devices. The captured images are transferred to the server of the cattle recognition system using a wireless network or internet technology. The system performs pre-processing on the muzzle point image of cattle to remove and filter the noise, increases the quality, and enhance the contrast. The muzzle point features are extracted and supervised machine learning based multi-classifier pattern recognition techniques are applied for recognizing the cattle. The server has a database of cattle images which are provided by the owners. Finally, One-Shot-Similarity (OSS) matching and distance metric learning based techniques with ensemble of classifiers technique are used for matching the query muzzle image with the stored database.A prototype is also developed for evaluating the efficacy of the proposed system in term of recognition accuracy and end-to-end delay.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973829"}, {"primary_key": "4164363", "vector": [], "sparse_vector": [], "title": "Barrista: Caffe Well-Served.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The caffe framework is one of the leading deep learning toolboxes in the machine learning and computer vision community. While it offers efficiency and configurability, it falls short of a full interface to Python. With increasingly involved procedures for training deep networks and reaching depths of hundreds of layers, creating configuration files and keeping them consistent becomes an error prone process. We introduce the barrista framework, offering full, pythonic control over caffe. It separates responsibilities and offers code to solve frequently occurring tasks for pre-processing, training and model inspection. It is compatible to all caffe versions since mid 2015 and can import and export .prototxt files. Examples are included, e.g., a deep residual network implemented in only 172 lines (for arbitrary depths), comparing to 2320 lines in the official implementation for the equivalent model.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973803"}, {"primary_key": "4164364", "vector": [], "sparse_vector": [], "title": "Learning Multimodal Temporal Representation for Dubbing Detection in Broadcast Media.", "authors": ["Nam Do-Hoang Le", "<PERSON><PERSON><PERSON>"], "summary": "Person discovery in the absence of prior identity knowledge requires accurate association of visual and auditory cues. In broadcast data, multimodal analysis faces additional challenges due to narrated voices over muted scenes or dubbing in different languages. To address these challenges, we define and analyze the problem of dubbing detection in broadcast data, which has not been explored before. We propose a method to represent the temporal relationship between the auditory and visual streams. This method consists of canonical correlation analysis to learn a joint multimodal space, and long short term memory (LSTM) networks to model cross-modality temporal dependencies. Our contributions also include the introduction of a newly acquired dataset of face-speech segments from TV data, which we have made publicly available. The proposed method achieves promising performance on this real world dataset as compared to several baselines.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967211"}, {"primary_key": "4164365", "vector": [], "sparse_vector": [], "title": "Locality-preserving K-SVD Based Joint Dictionary and Classifier Learning for Object Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper concerns the development of locality-preserving methods for object recognition. The major purpose is consideration of both descriptor-level locality and image-level locality throughout the recognition process. Two dual-layer locality-preserving methods are developed, in which locality-constrained linear coding (LLC) is used to represent an image. In the learning phase, the discriminative locality-preserving K-SVD (DLP-KSVD) in which the label information is incorporated into the locality-preserving term is proposed. In addition to using class labels to learn a linear classifier, the label-consistent LP-KSVD (LCLP-KSVD) is proposed to enhance the discriminability of the learned dictionary. In LCLP-KSVD, the objective function includes a label-consistent term that penalizes sparse codes from different classes. For testing, additional information about the locality of query samples is obtained by treating the locality-preserving matrix as a feature. The recognition results that were obtained in experiments with the Caltech101 database indicate that the proposed method outperforms existing sparse coding based approaches.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967267"}, {"primary_key": "4164368", "vector": [], "sparse_vector": [], "title": "Modular Parallelization Framework for Multi-Stream Video Processing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A flow-based software framework specialized for 3D and video is presented, which in particular handles automatic parallelization of multi-stream video processing. The workflow is decomposed into a set of filter units which become nodes in a graph. Execution with support for time windows on node inputs is automatically handled by the framework, as well as low-level manipulation of multi-dimensional data.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973799"}, {"primary_key": "4164369", "vector": [], "sparse_vector": [], "title": "Event Specific Multimodal Pattern Mining for Knowledge Base Construction.", "authors": ["Hongzhi Li", "<PERSON>", "<PERSON>ng <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Knowledge bases, which consist of a collection of entities, attributes, and the relations between them are widely used and important for many information retrieval tasks. Knowledge base schemas are often constructed manually using experts with specific domain knowledge for the field of interest. Once the knowledge base is generated then many tasks such as automatic content extraction and knowledge base population can be performed, which have so far been robustly studied by the Natural Language Processing community. However, the current approaches ignore visual information that could be used to build or populate these structured ontologies. Preliminary work on visual knowledge base construction only explores limited basic objects and scene relations. In this paper, we propose a novel multimodal pattern mining approach towards constructing a high-level \"event\" schema semi-automatically, which has the capability to extend text only methods for schema construction. We utilize a large unconstrained corpus of weakly-supervised image-caption pairs related to high-level events such as \"attack\" and \"demonstration\" to both discover visual aspects of an event, and name these visual components automatically. We compare our method with several state-of-the-art visual pattern mining approaches and demonstrate that our proposed method can achieve dramatic improvements in terms of the number of concepts discovered (33% gain), semantic consistence of visual patterns (52% gain), and correctness of pattern naming (150% gain).", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964287"}, {"primary_key": "4164371", "vector": [], "sparse_vector": [], "title": "Detecting Violence in Video using Subclasses.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qin Jin", "<PERSON><PERSON><PERSON>"], "summary": "This paper attacks the challenging problem of violence detection in videos. Different from existing works focusing on combining multi-modal features, we go one step further by adding and exploiting subclasses visually related to violence. We enrich the MediaEval 2015 violence dataset by manually labeling violence videos with respect to the subclasses. Such fine-grained annotations not only help understand what have impeded previous efforts on learning to fuse the multi-modal features, but also enhance the generalization ability of the learned fusion to novel test data. The new subclass based solution, with AP of 0.303 and P100 of 0.55 on the MediaEval 2015 test set, outperforms the state-of-the-art. Notice that our solution does not require fine-grained annotations on the test set, so it can be directly applied on novel and fully unlabeled videos. Interestingly, our study shows that motion related features (MBH, HOG and HOF), though being essential part in previous systems, are seemingly dispensable. Data is available at http://lixirong.net/datasets/mm2016vsd", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967289"}, {"primary_key": "4164372", "vector": [], "sparse_vector": [], "title": "Exploiting Hierarchical Activations of Neural Network for Image Retrieval.", "authors": ["<PERSON>", "Xiangwei Kong", "<PERSON>", "<PERSON>"], "summary": "The Convolutional Neural Networks (CNNs) have achieved breakthroughs on several image retrieval benchmarks. Most previous works re-formulate CNNs as global feature extractors used for linear scan. This paper proposes a Multi-layer Orderless Fusion (MOF) approach to integrate the activations of CNN in the Bag-of-Words (BoW) framework. Specifically, through only one forward pass in the network, we extract multi-layer CNN activations of local patches. Activations from each layer are aggregated in one BoW model, and several BoW models are combined with late fusion. Experimental results on two benchmark datasets demonstrate the effectiveness of the proposed method.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967197"}, {"primary_key": "4164373", "vector": [], "sparse_vector": [], "title": "Multiview Video Super-Resolution via Information Extraction and Merging.", "authors": ["<PERSON><PERSON>", "Xiaofeng Li", "Zhizhong Fu", "<PERSON><PERSON>"], "summary": "Multiview video super-resolution provides a promising solution to the contradiction between the huge data size of multiview video and the degraded video quality due to mixed-resolution compression. This algorithm consists of two different functional layers. An information extraction layer draws relevant high-frequency information from the high-resolution views via depth-image-based rendering and interpolation. A merging layer fuses multiview high-frequency information to refine the low-resolution view. In this paper, we introduce kernel regression and non-local means to improve the two layers, respectively. Kernel regression adapts to the local image structure and thus outperforms basic interpolation methods. Non-local means exploits the similarity between different views of multiview videos to restore the high-frequency component of a low-resolution image. We constrain non-local means by limiting the pixels used to restore a pixel. The experimental results show the effectiveness of the proposed algorithm.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967260"}, {"primary_key": "4164374", "vector": [], "sparse_vector": [], "title": "Image Captioning with both Object and Scene Information.", "authors": ["Xiangyang Li", "<PERSON><PERSON><PERSON> Song", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, automatic generation of image captions has attracted great interest not only because of its extensive applications but also because it connects computer vision and natural language processing. By combining convolutional neural networks (CNNs), which learn visual representations from images, and recurrent neural networks (RNNs), which translate the learned features into text sequences, the content of a image can be transformed into linguistic sequences. Existing approaches typically focus on visual features extracted form an object-oriented CNN (train on ImageNet) and then decode them into natural language. In this paper, we propose a novel model using not only object-related, but also scene-related information extracted from the images. To make full use of both object and scene information, we first combine object information and scene information (extracted from a scene-oriented CNN), and then using as inputs to RNNs. Both types of information provide complementary aspects that help in generating a more complete description of the image. Qualitative and quantitative evaluation results validate the effectiveness of our method.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984069"}, {"primary_key": "4164375", "vector": [], "sparse_vector": [], "title": "Video ChatBot: Triggering Live Social Interactions by Automatic Video Commenting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We demonstrate a video chatbot, which can generate human-level emotional comments referring to the videos shared by users and trigger a conversation with users. Our video chatbot performs a large-scale similar video search to find visually similar videos w.r.t. a given video using approximate nearest-neighbor search. Then, the comments associated with the searched similar videos are ranked by learning a deep multi-view embedding space for modeling video content, visual sentiment and textual comments. The top ranked comments are selected as responses to the given video and trigger the succeeding text-based chat between users and the chatbot. The demonstration is conducted on a newly collected dataset with over 102K videos and 10.6M comments. Moreover, our video chatbot has great potential to increase live social interactions.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973835"}, {"primary_key": "4164376", "vector": [], "sparse_vector": [], "title": "Share-and-Chat: Achieving Human-Level Video Commenting by Search and Multi-View Embedding.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Hong<PERSON> Chao", "<PERSON>"], "summary": "Video has become a predominant social media for the booming live interactions. Automatic generation of emotional comments to a video has great potential to significantly increase user engagement in many socio-video applications (e.g., chat bot). Nevertheless, the problem of video commenting has been overlooked by the research community. The major challenges are that the generated comments are to be not only as natural as those from human beings, but also relevant to the video content. We present in this paper a novel two-stage deep learning-based approach to automatic video commenting. Our approach consists of two components. The first component, similar video search, efficiently finds the visually similar videos w.r.t. a given video using approximate nearest-neighbor search based on the learned deep video representations, while the second dynamic ranking effectively ranks the comments associated with the searched similar videos by learning a deep multi-view embedding space. For modeling the emotional view of videos, we incorporate visual sentiment, video content, and text comments into the learning of the embedding space. On a newly collected dataset with over 102K videos and 10.6M comments, we demonstrate that our approach outperforms several state-of-the-art methods and achieves human-level video commenting.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964320"}, {"primary_key": "4164377", "vector": [], "sparse_vector": [], "title": "Robust Face Recognition with Deep Multi-View Representation Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper describes our proposed method targeting at the MSR Image Recognition Challenge MS-Celeb-1M. The challenge is to recognize one million celebrities from their face images captured in the real world. The challenge provides a large scale dataset crawled from the Web, which contains a large number of celebrities with many images for each subject. Given a new testing image, the challenge requires an identify for the image and the corresponding confidence score. To complete the challenge, we propose a two-stage approach consisting of data cleaning and multi-view deep representation learning. The data cleaning can effectively reduce the noise level of training data and thus improves the performance of deep learning based face recognition models. The multi-view representation learning enables the learned face representations to be more specific and discriminative. Thus the difficulties of recognizing faces out of a huge number of subjects are substantially relieved. Our proposed method achieves a coverage of 46.1% at 95% precision on the random set and a coverage of 33.0% at 95% precision on the hard set of this challenge.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984061"}, {"primary_key": "4164378", "vector": [], "sparse_vector": [], "title": "Online Weighted Clustering for Real-time Abnormal Event Detection in Video Surveillance.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Brendon J<PERSON>", "<PERSON>"], "summary": "Detecting abnormal events in video surveillance is a challenging problem due to the large scale, stream fashion video data as well as the real-time constraint. In this paper, we present an online, adaptive, and real-time framework to address this problem. The spatial locations in a frame is partitioned into grids, in each grid the proposed Adaptive Multi-scale Histogram Optical Flow (AMHOF) features are extracted and modelled by an Online Weighted Clustering (OWC) algorithm. The AMHOFs which cannot be fit to a cluster with large weight are regarded as abnormal events. The OWC algorithm is simple to implement and computational efficient. In addition, we improve the detection performance by a Multiple Target Tracking (MTT) algorithm. Experimental results demonstrate our approach outperforms the state-of-the-art approaches in pixel-level rate of detection at a processing speed of 30 FPS.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967279"}, {"primary_key": "4164380", "vector": [], "sparse_vector": [], "title": "Automatic Music Video Generation Based on Emotion-Oriented Pseudo Song Prediction and Matching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The main difficulty in automatic music video (MV) generation lies in how to match two different media (i.e., video and music). This paper proposes a novel content-based MV generation system based on emotion-oriented pseudo song prediction and matching. We use a multi-task deep neural network (MDNN) to jointly learn the relationship among music, video, and emotion from an emotion-annotated MV corpus. Given a queried video, the MDNN is applied to predict the acoustic (music) features from the visual (video) features, i.e., the pseudo song corresponding to the video. Then, the pseudo acoustic (music) features are matched with the acoustic (music) features of each music track in the music collection according to a pseudo-song-based deep similarity matching (PDSM) metric given by another deep neural network (DNN) trained on the acoustic and pseudo acoustic features of the positive (official), less-positive (artificial), and negative (artificial) MV examples. The results of objective and subjective experiments demonstrate that the proposed pseudo-song-based framework performs well and can generate appealing MVs with better viewing and listening experiences.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967245"}, {"primary_key": "4164381", "vector": [], "sparse_vector": [], "title": "V3I-STAL: Visual Vehicle-to-Vehicle Interaction via Simultaneous Tracking and Localization.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "This paper investigates a visual interaction system for vehicle-to-vehicle (V2V) platform, called V3I. Our system employs common visual cameras that are mounted on connected vehicles to perceive the existence of isolated vehicles in the same roadway, and provides human drivers with imagery situational awareness. This allows effective interactions between vehicles even with a low permeation rate of V2V devices. The underlying research problem for V3I includes two aspects: i) tracking isolated vehicles of interest over time through local cameras; ii) at each time-step fusing the results of local visual perceptions to obtain a global location map that involves both isolated and connected vehicles. In this paper, we introduce a unified probabilistic approach to solve the above two problems, i.e., tracking and localization, in a joint fashion. Our approach will explore both the visual features of individual vehicles in images and the pair-wise spatial relationships between vehicles. We develop a fast Markov Chain Monte Carlo (MCMC) algorithm to search the joint solution space efficiently, which enables real-time application. To evaluate the performance of the proposed approach, we collect and annotate a set of video sequences captured with a group of vehicle-resident cameras. Extensive experiments with comparisons clearly demonstrate that the proposed V3I approach can precisely recover the dynamic location map of the surrounding and thus enable direct visual interactions between vehicles .", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964285"}, {"primary_key": "4164382", "vector": [], "sparse_vector": [], "title": "Magic Mirror: A Virtual Fashion Consultant.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "What should I wear? We present <PERSON> Mirror, a virtual fashion consultant, which can parse, appreciate and recommend the wearing. Magic Mirror is designed with a large display and Kinect to simulate the real mirror and interact with users in augmented reality. Internally, Magic Mirror is a practical appreciation system for automatic aesthetics-oriented clothing analysis. Specifically, we focus on the clothing collocation rather than the single one, the style (aesthetic words) rather than the visual features. We bridge the gap between the visual features and aesthetic words of clothing collocation to enable the computer to learn appreciating the clothing collocation. Finally, both object and subject evaluations verify the effectiveness of the proposed algorithm and Magic Mirror system.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2970928"}, {"primary_key": "4164383", "vector": [], "sparse_vector": [], "title": "What Makes a Good Movie Trailer?: Interpretation from Simultaneous EEG and Eyetracker Recording.", "authors": ["<PERSON><PERSON>", "Jinglei Lv", "<PERSON><PERSON>", "<PERSON><PERSON>", "Qinglin Dong", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "What makes a good movie trailer? It's a big challenge to answer this question because of the complexity of multimedia in both low level sensory features and high level semantic features. However, human perception and reactivity could be straightforward evidence for evaluation. Modern Electro-encephalography (EEG) technology provides measurement of consequential brain neural activity to external stimuli. Meanwhile, visual perception and attention could be captured and interpreted by Eye Tracking technology. Intuitively, simultaneous EEG and Eye Tracker recording of human audience with multimedia stimuli could bridge the gap between human comprehension and multimedia analysis, and provide a new way for movie trailer evaluation. In this paper, we propose a novel platform to simultaneously record EEG and eye movement for participants with video stimuli by integrating 256-channel EEG, Eye Tracker and video display device as a system. Based on the proposed system a novel experiment has been designed, in which independent and joint features of EEG and Eye tracking data were mined to evaluate the movie trailer. Our analysis has shown interesting features that are corresponding with trailer quality and video shoot changes.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967187"}, {"primary_key": "4164384", "vector": [], "sparse_vector": [], "title": "Learning Music Emotion Primitives via Supervised Dynamic Clustering.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper explores a fundamental problem in music emotion analysis, i.e., how to segment the music sequence into a set of basic emotive units, which are named as emotion primitives. Current works on music emotion analysis are mainly based on the fixed-length music segments, which often leads to the difficulty of accurate emotion recognition. Short music segment, such as an individual music frame, may fail to evoke emotion response. Long music segment, such as an entire song, may convey various emotions over time. Moreover, the minimum length of music segment varies depending on the types of the emotions. To address these problems, we propose a novel method dubbed supervised dynamic clustering (SDC) to automatically decompose the music sequence into meaningful segments with various lengths. First, the music sequence is represented by a set of music frames. Then, the music frames are clustered according to the valence-arousal values in the emotion space. The clustering results are used to initialize the music segmentation. After that, a dynamic programming scheme is employed to jointly optimize the subsequent segmentation and grouping in the music feature space. Experimental results on standard dataset show both the effectiveness and the rationality of the proposed method.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967215"}, {"primary_key": "4164385", "vector": [], "sparse_vector": [], "title": "AntiLoiter: A Loitering Discovery System for Longtime Videos across Multiple Surveillance Cameras.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Since loitering is a suspicious behavior that often leads to abnormal situations, such as pickpocketing and terrorist attacks, its analysis attracts increasing research attention. In this paper, we present AntiLoiter, a practical loitering discovery system for surveillance. AntiLoiter can efficiently discover loitering candidates from longtime videos across multiple cameras. Most of existing systems mainly focus on how to detect or identify loiterers by behavior tracking techniques. However, the difficulties of tracking-based methods are known as that their analysis results are heavily influenced by occlusions, overlaps, and shadows. Moreover, tracking-based methods need to track the human appearance continuously. Therefore, they are not readily applied to longtime videos across multiple cameras due to the appearance discontinuity of criminal loitering. For instance, a suspect would probably come to the same place several times on different dates for preliminary inspections before a terrorist attack happens, where the loitering behavior is discontinuous. To solve this problem, we abandon the tracking method, instead, design a novel approach to efficiently discover loiterers based on their frequent appearance patterns for longtime videos across multiple cameras. Our approach simply yet effectively adopts existing face recognition techniques for loitering discovery. We conducted extensive experiments on both synthetic and real surveillance videos to evaluate the efficiency and efficacy of our approach. The experimental results show that our system can find out loitering candidates correctly and outperforms existing method by 100 times in terms of runtime.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2970927"}, {"primary_key": "4164386", "vector": [], "sparse_vector": [], "title": "Boosting Video Description Generation by Explicitly Translating from Frame-Level Captions.", "authors": ["<PERSON>", "Zhongchao Shi"], "summary": "Automatically describing video content with natural language is a fundamental challenge of computer vision. The recent advanced technique that approaches this problem is Recurrent Neural Networks (RNN). The need to train RNN on large-scale complex and diverse videos and their associated language, however, makes the task human-labeling intensive and computationally expensive. Moreover, the results can suffer from robustness problem, especially when there are rich of temporal dynamics in the sequence of video frames. We demonstrate in this paper that the above two limitations can be mitigated by jointly exploring the largely available data from image domain and representing each frame by high-level attributes rather than visual features. The former leverages the learnt models on image captioning benchmark to generate caption for each video frame, while the latter explicitly incorporates the obtained captions which are regarded as the attributes of each frame. Specifically, we propose a novel sequence to sequence architecture to generate descriptions for videos, in a sense that the inputs are the captions of sequential frames and it outputs words sequentially. On a widely used YouTube2Text dataset, our proposal is shown to be powerful with superior performance over several state-of-the-art methods including both architectures that are purely developed on video data and RNN-based models which translate directly from visual features to language.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967298"}, {"primary_key": "4164387", "vector": [], "sparse_vector": [], "title": "Image2Text: A Multimodal Image Captioner.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we showcase the Image2Text system, which is a real-time captioning system that can generate human-level natural language description for any input image. We formulate the problem of image captioning as a multimodal translation task. Analogous to machine translation, we present a sequence-to-sequence recurrent neural networks (RNN) model for image caption generation. Different from most existing work where the whole image is represented by a convolutional neural networks (CNN) feature, we propose to represent the input image as a sequence of detected objects to serve as the source sequence of the RNN model. Based on the captioning framework, we develop a user-friendly system to automatically generated human-level captions for users. The system also enables users to detect salient objects in an image, and retrieve similar images and corresponding descriptions from a database.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973831"}, {"primary_key": "4164388", "vector": [], "sparse_vector": [], "title": "Learning a Multi-class Discriminative Dictionary with Nonredundancy Constraints for Visual Classification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Junsong Yuan", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent studies have demonstrated advantages of sparse representation in providing an appealing paradigm for visual classification tasks. However, how to effectively learn a compact dictionary of superior reconstruction and discrimination power is still a challenging problem. In this paper, we concurrently exploit both the intra-class and the inter-class visual correlations to learn a multi-class discriminative dictionary. The intra-nonredundancy constraint prevents zero entities from appearing in the class-specific bases, thereby making the learned dictionary more stable. The inter-nonredundancy constraint effectively separates the common visual patterns from all the class-specific bases, yielding a more compact dictionary. Combining nonredundancy constraints with the reconstruction error and the classification error to form a unified objective function, our method can learn a superior dictionary and an optimal linear classifier simultaneously. Extensive experimental results demonstrate that the proposed algorithm achieves notable improvement over the state-of-the-art methods in image classification and visual tracking tasks.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967255"}, {"primary_key": "4164389", "vector": [], "sparse_vector": [], "title": "Event Localization in Music Auto-tagging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In music auto-tagging, people develop models to automatically label a music clip with attributes such as instruments, styles or acoustic properties. Many of these tags are actually descriptors of local events in a music clip, rather than a holistic description of the whole clip. Localizing such tags in time can potentially innovate the way people retrieve and interact with music, but little work has been done to date due to the scarcity of labeled data with granularity specific enough to the frame level. Most labeled data for training a learning-based model for music auto-tagging are in the clip level, providing no cues when and how long these attributes appear in a music clip. To bridge this gap, we propose in this paper a convolutional neural network (CNN) architecture that is able to make accurate frame-level predictions of tags in unseen music clips by using only clip-level annotations in the training phase. Our approach is motivated by recent advances in computer vision for localizing visual objects, but we propose new designs of the CNN architecture to account for the temporal information of music and the variable duration of such local tags in time. We report extensive experiments to gain insights into the problem of event localization in music, and validate through experiments the effectiveness of the proposed approach. In addition to quantitative evaluations, we also present qualitative analyses showing the model can indeed learn certain characteristics of music tags.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964292"}, {"primary_key": "4164390", "vector": [], "sparse_vector": [], "title": "Multi-Scale Triplet CNN for Person Re-Identification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Person re-identification aims at identifying a certain person across non-overlapping multi-camera networks. It is a fundamental and challenging task in automated video surveillance. Most existing researches mainly rely on hand-crafted features, resulting in unsatisfactory performance. In this paper, we propose a multi-scale triplet convolutional neural network which captures visual appearance of a person at various scales. We propose to optimize the network parameters by a comparative similarity loss on massive sample triplets, addressing the problem of small training set in person re-identification. In particular, we design a unified multi-scale network architecture consisting of both deep and shallow neural networks, towards learning robust and effective features for person re-identification under complex conditions. Extensive evaluation on the real-world Market-1501 dataset have demonstrated the effectiveness of the proposed approach.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967209"}, {"primary_key": "4164392", "vector": [], "sparse_vector": [], "title": "Patterns of Free-form Curation: Visual Thinking with Web Content.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Web curation involves choosing, organizing, and commenting on content. Popular web curation apps-- e.g., Facebook, Twitter, and Pinterest-- provide linear feeds that show people the latest content, but provide little support for articulating relationships among content elements. The new medium of free-form web curation enables multimedia elements to be spontaneously gathered from the web, written about, sketched amidst, manipulated, and visually assembled in a continuous space. Through free-form web curation, content is collected, interpreted, and arranged, creating context. We conducted a field study of 1581 students in 6 courses, spanning diverse fields. We derive patterns of free-form curation through a visual grounded theory analysis of the resulting dataset of 4426 curations. From the observed range of invocations of the patterns in the performance of ideation tasks, we conclude that free-form is valuable as a new medium of web curation in how it supports creative visual thinking.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964303"}, {"primary_key": "4164393", "vector": [], "sparse_vector": [], "title": "Frustratingly Easy Cross-<PERSON><PERSON>.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiangwei Kong", "<PERSON><PERSON>"], "summary": "Cross-modal hashing has attracted considerable attention due to its low storage cost and fast retrieval speed. Recently, more and more sophisticated researches related to this topic are proposed. However, they seem to be inefficient computationally for several reasons. On one hand, learning coupled hash projections makes the iterative optimization problem challenging. On the other hand, individual collective binary codes for each content are also learned with a high computation complexity. In this paper we describe a simple yet effective cross-modal hashing approach that can be implemented in just three lines of code. This approach first obtains the binary codes for one modality via unimodal hashing methods (e.g., iterative quantization (ITQ)), then applies simple linear regression to project the other modalities into the obtained binary subspace. Obviously, it is non-iterative and parameter-free, which makes it more attractive for many real-world applications. We further compare our approach with other state-of-the-art methods on four benchmark datasets (i.e., the Wiki, VOC, LabelMe and NUS-WIDE datasets). Despite its extraordinary simplicity, our approach performs remarkably and generally well for these datasets under different experimental settings (i.e., large-scale, high-dimensional and multi-label datasets).", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967218"}, {"primary_key": "4164395", "vector": [], "sparse_vector": [], "title": "First-Person Shooter Game for Virtual Reality Headset with Advanced Multi-Agent Intelligent System.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a multiplayer first-person shooter (FPS) game with advanced intelligent non-playable characters (NPC) under computer control. The game is specially adapted for playing in VR headset so the simulator sickness symptoms are significantly reduced. The demo allows users to play with the other human and NPC players in a shooter game made in Unreal Engine 4. User can verify his/her game skills versus evolving human-like NPCs with a level adjusting model. The humanness of NPC was verified with Alan Turing game test beating 52% record from BotPrize'12 competition.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973826"}, {"primary_key": "4164396", "vector": [], "sparse_vector": [], "title": "Scene Image Synthesis from Natural Sentences Using Hierarchical Syntactic Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Synthesizing a new image from verbal information is a challenging task that has a number of applications. Most research on the issue has attempted to address this question by providing external clues, such as sketches. However, no study has been able to successfully handle various sentences for this purpose without any other information. We propose a system to synthesize scene images solely from sentences. Input sentences are expected to be complete sentences with visualizable objects. Our priorities are the analysis of sentences and the correlation of information between input sentences and visible image patches. A hierarchical syntactic parser is developed for sentence analysis, and a combination of lexical knowledge and corpus statistics is designed for word correlation. The entire system was applied to both a clip-art dataset and an actual image dataset. This application highlighted the capability of the proposed system to generate novel images as well as its ability to succinctly convey ideas.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967193"}, {"primary_key": "4164397", "vector": [], "sparse_vector": [], "title": "Technology &amp; Art in Stimulating Creative Placemaking in Public-Use Spaces.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this tutorial we are going to elaborate on how technology and art can contribute in stimulating creative placemaking in public-use spaces. This presentation will highlight the authors approach and several other past and potential approaches that embrace on technology and connectivity to refocus on a creative function in public spaces that aims to move away from traditional planning and textbook concepts. Our approach, which is demonstrated by our design and implementation of a multi-use public space called \"Adressaparken\" is aimed to create a space towards greater flexibility, changeability, and appreciation for the public`s contribution. The presenters will also identify key lessons learned in the design and implementation of a technology re-usable public space. Potential variables needed to address the challenges in the integration and evaluation of digital public art installations will also be elaborated. By surveying past and present exhibited contributions and the presenters' own contributions in a public realm, this tutorial will offer guidance to artists, designers, researchers and practitioners on how they can successfully blend in to this interdisciplinary area of research.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2986912"}, {"primary_key": "4164398", "vector": [], "sparse_vector": [], "title": "Super Resolution of the Partial Pixelated Images With Deep Convolutional Neural Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Yun Fu"], "summary": "The problem of super resolution of partial pixelated images is considered in this paper. Partial pixelated images are more and more common in nowadays due to public safety etc. However, in some special cases, for instance criminal investigation, some images are pixelated intentionally by criminals and partial pixelate make it hard to reconstruct images even a higher resolution images. Hence, a method is proposed to handle this problem based on the deep convolutional neural network, termed depixelate super resolution CNN(DSRCNN). Given the mathematical expression pixelates, we propose a model to reconstruct the image from the pixelation and map to a higher resolution by combining the adversarial autoencoder with two depixelate layers. This model is evaluated on standard public datasets in which images are pixelated randomly and compared to the state of arts methods, shows very exciting performance.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967235"}, {"primary_key": "4164399", "vector": [], "sparse_vector": [], "title": "Deep Multi-task Learning with Label Correlation Constraint for Video Concept Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work we propose a method that integrates multi-task learning (MTL) and deep learning. Our method appends a MTL-like loss to a deep convolutional neural network, in order to learn the relations between tasks together at the same time, and also incorporates the label correlations between pairs of tasks. We apply the proposed method on a transfer learning scenario, where our objective is to fine-tune the parameters of a network that has been originally trained on a large-scale image dataset for concept detection, so that it be applied on a target video dataset and a corresponding new set of target concepts. We evaluate the proposed method for the video concept detection problem on the TRECVID 2013 Semantic Indexing dataset. Our results show that the proposed algorithm leads to better concept-based video annotation than existing state-of-the-art methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967271"}, {"primary_key": "4164400", "vector": [], "sparse_vector": [], "title": "Multimodal Popularity Prediction of Brand-related Social Media Posts.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Brand-related user posts on social networks are growing at a staggering rate, where users express their opinions about brands by sharing multimodal posts. However, while some posts become popular, others are ignored. In this paper, we present an approach for identifying what aspects of posts determine their popularity. We hypothesize that brand-related posts may be popular due to several cues related to factual information, sentiment, vividness and entertainment parameters about the brand. We call the ensemble of cues engagement parameters. In our approach, we propose to use these parameters for predicting brand-related user post popularity. Experiments on a collection of fast food brand-related user posts crawled from Instagram show that: visual and textual features are complementary in predicting the popularity of a post; predicting popularity using our proposed engagement parameters is more accurate than predicting popularity directly from visual and textual features; and our proposed approach makes it possible to understand what drives post popularity in general as well as isolate the brand specific drivers.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967210"}, {"primary_key": "4164402", "vector": [], "sparse_vector": [], "title": "MP3DG-PCC, Open Source Software Framework for Implementation and Evaluation of Point Cloud Compression.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present MP3DG-PCC, an open source framework for design, implementation and evaluation of point cloud compression algorithms. The framework includes objective quality metrics, lossy and lossless anchor codecs, and a test bench for consistent comparative evaluation. The framework and proposed methodology is in use for the development of an international point cloud compression standard in MPEG. In addition, the library is integrated with the popular point cloud library, making a large number of point cloud processing available and aligning the work with the broader open source community.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973806"}, {"primary_key": "4164403", "vector": [], "sparse_vector": [], "title": "Multi-Protocol Video Delivery with Late Trans-Muxing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In practice, video delivery using multiple protocols is needed to support the wide range of clients in the digital media ecosystem. Naive approaches increase backhaul traffic and cache storage requirements proportional to the number of protocols in use. To reduce this overhead, we present an efficient multi-protocol video delivery architecture. It exploits the fact that media segments in different protocols are often based on the same raw encoded media data. Instead of caching and distributing near duplicate media segments for each protocol throughout the content delivery network, we push their generation towards the edge. We call this Late Trans-Muxing (LTM). We implement LTM in a smart edge cache that can request and cache media byte ranges and generate protocol specific media segments on the fly. Experiments in an emulation testbed show that backhaul traffic and cache storage are reduced up to 85% and 50% respectively compared to CDN based approaches. This shows the benefit of LTM in multi-protocol DASH based video delivery.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967189"}, {"primary_key": "4164406", "vector": [], "sparse_vector": [], "title": "Weakly-Supervised Recognition, Localization, and Explanation of Visual Entities.", "authors": ["<PERSON>"], "summary": "To learn from visual collections, manual annotations are required. Humans however can no longer keep up with providing strong and time consuming annotations on the ever increasing wealth of visual data. As a result, approaches are required that can learn from fast and weak forms of annotations in visual data. This doctorial symposium summarizes my ongoing PhD dissertation on how to utilize weakly-supervised annotations to recognize, localize, and explain visual entities in images and videos. In this context, visual entities denote objects, scenes, and actions (in images), and actions and events (in videos). The summary is performed through four publications. For each publication, we discuss the current state-of-the-art, as well as our proposed novelties and performed experiments. The end of the summary discusses several possibilities to extend the dissertation.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2971479"}, {"primary_key": "4164408", "vector": [], "sparse_vector": [], "title": "Generating Diverse Image Datasets with Limited Labeling.", "authors": ["Niluthpol Chowdh<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Image datasets play a pivotal role in advancing multimedia and image analysis research. However, most of these datasets are created by extensive human effort and extremely expensive to scale up. There is high chance that we may have no instances for some required concepts in these data-sets or the available instances do not cover the diversity of real-world scenarios. In this regard, several approaches for learning from web images and refining them have been proposed, but these approaches either include significant redundant instances in the dataset or fail to guarantee a diverse enough set to train a robust classifier. In this work, we propose a semi-supervised sparse coding framework to collect a diverse set of images with minimal human effort, which can be used to both create a dataset from scratch or enrich an existing dataset with diverse examples. To evaluate our method, we constructed an image dataset with our framework, which is named as DivNet. Experiments on this dataset demonstrate that our method not only reduces manual effort, but also the created dataset has excellent accuracy, diversity and cross-dataset generalization ability.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967285"}, {"primary_key": "4164410", "vector": [], "sparse_vector": [], "title": "First Person View Video Summarization Subject to the User Needs.", "authors": ["<PERSON>"], "summary": "Our life is becoming heavily documented and expressed on the digital substrate. This booming flow of consumer video has lead to an increasing demand of multimedia analysis tools to organize and summarize those visual memories. Due to the personal nature of such videos, though, the summarization needs to be adapted to the user needs and preferences. Yet, most summarization systems rely solely on pre-defined criteria, e.g. story-coherence or interestingness pre-trained classifiers. I propose a system which is capable of finding relevant digital memories to a given semantic query, and then summarize them on a customized manner. The proposed framework includes a wide set of tools to match a user's needs, from retrieval using multimodal queries to summarization striving to his/her preferences, both provided passively and actively. Preliminary results show the high potential of such a framework, with over 70% retrieval accuracy. More importantly, as seen from the user study, the summaries generated achieve an unprecedented compromise between usability and quality.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2971474"}, {"primary_key": "4164412", "vector": [], "sparse_vector": [], "title": "A Multimodal Gamified Platform for Real-Time User Feedback in Sports Performance.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Noel <PERSON> O&a<PERSON>;<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we introduce a novel platform that utilises multi-modal low-cost motion capture technology for the delivery of real-time visual feedback for sports performance. This platform supports the expansion to multi-modal interfaces that utilise haptic and audio feedback, which scales effectively with motor task complexity. We demonstrate an implementation of our platform within the field of sports performance. The platform includes low-cost motion capture through a fusion technique, combining a Microsoft Kinect V2 with two wrist inertial sensors, which make use of the accelerometer and gyroscope sensors, alongside a game-based Graphical User Interface (GUI) for instruction, visual feedback and gamified score tracking.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973815"}, {"primary_key": "4164415", "vector": [], "sparse_vector": [], "title": "Alone versus In-a-group: A Comparative Analysis of Facial Affect Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Automatic affect analysis and understanding has become a well established research area in the last two decades. Recent works have started moving from individual to group scenarios. However, little attention has been paid to comparing the affect expressed in individual and group settings. This paper presents a framework to investigate the differences in affect recognition models along arousal and valence dimensions in individual and group settings. We analyse how a model trained on data collected from an individual setting performs on test data collected from a group setting, and vice versa. A third model combining data from both individual and group settings is also investigated. A set of experiments is conducted to predict the affective states along both arousal and valence dimensions on two newly collected databases that contain sixteen participants watching affective movie stimuli in individual and group settings, respectively. The experimental results show that (1) the affect model trained with group data performs better on individual test data than the model trained with individual data tested on group data, indicating that facial behaviours expressed in a group setting capture more variation than in an individual setting; and (2) the combined model does not show better performance than the affect model trained with a specific type of data (i.e., individual or group), but proves a good compromise. These results indicate that in settings where multiple affect models trained with different types of data are not available, using the affect model trained with group data is a viable solution.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967276"}, {"primary_key": "4164417", "vector": [], "sparse_vector": [], "title": "Predicting and Optimizing Image Compression.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Image compression is a core task for mobile devices, social media and cloud storage backend services. Key evaluation criteria for compression are: the quality of the output, the compression ratio achieved and the computational time (and energy) expended. Predicting the effectiveness of standard compression implementations like libjpeg and WebP on a novel image is challenging, and often leads to non-optimal compression. This paper presents a machine learning-based technique to accurately model the outcome of image compression for arbitrary new images in terms of quality and compression ratio, without requiring significant additional computational time and energy. Using this model, we can actively adapt the aggressiveness of compression on a per image basis to accurately fit user requirements, leading to a more optimal compression.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967305"}, {"primary_key": "4164418", "vector": [], "sparse_vector": [], "title": "Synchronization among Groups of Spectators for Highlight Detection in Movies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Detection of emotional and aesthetic highlights is a challenge for the affective understanding of movies. Our assumption is that synchronized spectators' physiological and behavioral reactions occur during these highlights. We propose to employ the periodicity score to capture synchronization among groups of spectators' signals. To uncover the periodicity score's capabilities, we compare it with baseline synchronization measures, such as the nonlinear interdependence and the windowed mutual information. The results show that the periodicity score and the pairwise synchronization measures are able to capture different properties of spectators' synchronization, and they indicate the presence of some types of emotional and aesthetic highlights in a movie based on spectators' electro-dermal and acceleration signals.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967229"}, {"primary_key": "4164419", "vector": [], "sparse_vector": [], "title": "Application-Layer Rate-Adaptive Multicast Video Streaming over 802.11 for Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Multicast video streaming over IEEE 802.11 is unreliable due to the lack of feedback from receivers. High data rates and variable link conditions require feedback from the receivers for link estimation to improve reliability and rate adaptation accordingly. In this paper, we validate on a test platform an application-layer rate-adaptive video multicast streaming framework using an 802.11 ad-hoc network applicable for mobile senders and receivers. Experimental results serve as a proof of concept and show the performance in terms of goodput, delay, packet loss, and received video quality.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967272"}, {"primary_key": "4164420", "vector": [], "sparse_vector": [], "title": "Are Safer Looking Neighborhoods More Lively?: A Multimodal Investigation into Urban Life.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gloria Zen", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Policy makers, urban planners, architects, sociologists, and economists are interested in creating urban areas that are both lively and safe. But are the safety and liveliness of neighborhoods independent characteristics? Or are they just two sides of the same coin? In a world where people avoid unsafe looking places, neighborhoods that look unsafe will be less lively, and will fail to harness the natural surveillance of human activity. But in a world where the preference for safe looking neighborhoods is small, the connection between the perception of safety and liveliness will be either weak or nonexistent. In this paper we explore the connection between the levels of activity and the perception of safety of neighborhoods in two major Italian cities by combining mobile phone data (as a proxy for activity or liveliness) with scores of perceived safety estimated using a Convolutional Neural Network trained on a dataset of Google Street View images scored using a crowdsourced visual perception survey. We find that: (i) safer looking neighborhoods are more active than what is expected from their population density, employee density, and distance to the city centre; and (ii) that the correlation between appearance of safety and activity is positive, strong, and significant, for females and people over 50, but negative for people under 30, suggesting that the behavioral impact of perception depends on the demographic of the population. Finally, we use occlusion techniques to identify the urban features that contribute to the appearance of safety, finding that greenery and street facing windows contribute to a positive appearance of safety (in agreement with <PERSON>'s defensible space theory). These results suggest that urban appearance modulates levels of human activity and, consequently, a neighborhood's rate of natural surveillance.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964312"}, {"primary_key": "4164422", "vector": [], "sparse_vector": [], "title": "MARIM: Mobile Augmented Reality for Interactive Manuals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we present a practical system which uses mobile devices for interactive manuals. In particular, there are two modes provided in the system, namely, expert/trainer and trainee modes. Given the expert/trainer editor, experts design the step-by-step interactive manuals. For each step, the experts capture the images by using phones/tablets and provide visual instructions such as interest regions, text, and action animations. In the trainee mode, the system utilizes the existing object detection and tracking algorithms to identify the step scene and retrieve the respective instruction to be displayed on the mobile device. The trainee then follows the displayed instruction. Once each step is performed, the trainee commands the devices to proceed to the next step.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973807"}, {"primary_key": "4164423", "vector": [], "sparse_vector": [], "title": "Improved Dense Trajectory with Cross Streams.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Improved dense trajectories (iDT) have shown great performance in action recognition, and their combination with the two-stream approach has achieved state-of-the-art performance. It is, however, difficult for iDT to completely remove background trajectories from video with camera shaking. Trajectories in less discriminative regions should be given modest weights in order to create more discriminative local descriptors for action recognition. In addition, the two-stream approach, which learns appearance and motion information separately, cannot focus on motion in important regions when extracting features from spatial convolutional layers of the appearance network, and vice versa. In order to address the above mentioned problems, we propose a new local descriptor that pools a new convolutional layer obtained from crossing two networks along iDT. This new descriptor is calculated by applying discriminative weights learned from one network to a convolutional layer of the other network. Our method has achieved state-of-the-art performance on ordinal action recognition datasets, 92.3% on UCF101, and 66.2% on HMDB51.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967222"}, {"primary_key": "4164427", "vector": [], "sparse_vector": [], "title": "CNNdroid: GPU-Accelerated Execution of Trained Deep Convolutional Neural Networks on Android.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many mobile applications running on smartphones and wearable devices would potentially benefit from the accuracy and scalability of deep CNN-based machine learning algorithms. However, performance and energy consumption limitations make the execution of such computationally intensive algorithms on mobile devices prohibitive. We present a GPU-accelerated library, dubbed CNNdroid, for execution of trained deep CNNs on Android-based mobile devices. Empirical evaluations show that CNNdroid achieves up to 60X speedup and 130X energy saving on current mobile devices. The CNNdroid open source library is available for download at https://github.com/ENCP/CNNdroid", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973801"}, {"primary_key": "4164428", "vector": [], "sparse_vector": [], "title": "Beauty eMakeup: A Deep Makeup Transfer System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this demo, we present a Beauty eMakeup System to automatically recommend the most suitable makeup for a female and synthesis the makeup on her face. Given a before-makeup face, her most suitable makeup is determined automatically. Then, both the before-makeup and the reference faces are fed into the proposed Deep Transfer Network to generate the after-makeup face. Our end-to-end makeup transfer network have several nice properties including: (1) with complete functions: including foundation, lip gloss, and eye shadow transfer; (2) cosmetic specific: different cosmetics are transferred in different manners; (3) localized: different cosmetics are applied on different facial regions; (4) producing naturally looking results without obvious artifacts; (5) controllable makeup lightness: various results from light makeup to heavy makeup can be generated. Extensive experimental evaluations and analysis on testing images well demonstrate the effectiveness of the proposed system.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973812"}, {"primary_key": "4164430", "vector": [], "sparse_vector": [], "title": "Social and Affective Robotics Tutorial.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Social and Affective Robotics is a growing multidisciplinary field encompassing computer science, engineering, psychology, education, and many other disciplines. It explores how social and affective factors influence interactions between humans and robots, and how affect and social signals can be sensed and integrated into the design, implementation, and evaluation of robots. With talks by renowned researchers in this area, Social and Affective Robotics Tutorial will help both new and experienced researchers to identify trends, concepts, methodologies and applications in this field, identified as a technological megatrend driving the fourth industrial revolution.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2986914"}, {"primary_key": "4164431", "vector": [], "sparse_vector": [], "title": "Label Tree Embeddings for Acoustic Scene Classification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present in this paper an efficient approach for acoustic scene classification by exploring the structure of class labels.Given a set of class labels, a category taxonomy is automatically learned by collectively optimizing a clustering of the labels into multiple meta-classes in a tree structure.An acoustic scene instance is then embedded into a low-dimensional feature representation which consists of the likelihoods that it belongs to the meta-classes.We demonstrate state-of-the-art results on two different datasets for the acoustic scene classification task, including the DCASE 2013 and LITIS Rouen datasets.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967268"}, {"primary_key": "4164432", "vector": [], "sparse_vector": [], "title": "Spectral and Cepstral Audio Noise Reduction Techniques in Speech Emotion Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Signal noise reduction can improve the performance of machine learning systems dealing with time signals such as audio. Real-life applicability of these recognition technologies requires the system to uphold its performance level in variable, challenging conditions such as noisy environments. In this contribution, we investigate audio signal denoising methods in cepstral and log-spectral domains and compare them with common implementations of standard techniques. The different approaches are first compared generally using averaged acoustic distance metrics. They are then applied to automatic recognition of spontaneous and natural emotions under simulated smartphone-recorded noisy conditions. Emotion recognition is implemented as support vector regression for continuous-valued prediction of arousal and valence on a realistic multimodal database. In the experiments, the proposed methods are found to generally outperform standard noise reduction algorithms.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967306"}, {"primary_key": "4164433", "vector": [], "sparse_vector": [], "title": "A Deeply-Supervised Deconvolutional Network for Horizon Line Detection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Automatic skyline detection from mountain pictures is an important task in many applications, such as web image retrieval, augmented reality and autonomous robot navigation. Recent works addressing the problem of Horizon Line Detection (HLD) demonstrated that learning-based boundary detection techniques are more accurate than traditional filtering methods. In this paper we introduce a novel approach for skyline detection, which adheres to a learning-based paradigm and exploits the representation power of deep architectures to improve the horizon line detection accuracy. Differently from previous works, we explore a novel deconvolutional architecture, which introduces intermediate levels of supervision to support the learning process. Our experiments, conducted on a publicly available dataset, confirm that the proposed method outperforms previous learning-based HLD techniques by reducing the number of spurious edge pixels.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967198"}, {"primary_key": "4164436", "vector": [], "sparse_vector": [], "title": "Multi-modal Multi-view Topic-opinion Mining for Social Event Analysis.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xu"], "summary": "In this paper, we propose a novel multi-modal multi-view topic-opinion mining (MMTOM) model for social event analysis in multiple collection sources. Compared with existing topic-opinion mining methods, our proposed model has several advantages: (1) The proposed MMTOM can effectively take into account multi-modal and multi-view properties jointly in a unified and principled way for social event modeling. (2) Our model is general and can be applied to many other applications in multimedia, such as opinion mining and sentiment analysis, multi-view association visualization, and topic-opinion mining for movie review. (3) The proposed MMTOM is able to not only discover multi-modal common topics from all collections as well as summarize the similarities and differences of these collections along each specific topic, but also automatically mine multi-view opinions on the learned topics across different collections. (4) Our topic-opinion mining results can be effectively applied to many applications including multi-modal multi-view topic-opinion retrieval and visualization, which achieve much better performance than existing methods. To evaluate the proposed model, we collect a real-world dataset for research on multi-modal multi-view social event analysis, and will release it for academic use. We have conducted extensive experiments, and both qualitative and quantitative evaluation results have demonstrated the effectiveness of the proposed MMTOM.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964294"}, {"primary_key": "4164437", "vector": [], "sparse_vector": [], "title": "Who is where?: Matching People in Video to Wearable Acceleration During Crowded Mingling Events.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We address the challenging problem of associating acceleration data from a wearable sensor with the corresponding spatio-temporal region of a person in video during crowded mingling scenarios. This is an important first step for multi-sensor behavior analysis using these two modalities. Clearly, as the numbers of people in a scene increases, there is also a need to robustly and automatically associate a region of the video with each person's device. We propose a hierarchical association approach which exploits the spatial context of the scene, outperforming the state-of-the-art approaches significantly. Moreover, we present experiments on matching from 3 to more than 130 acceleration and video streams which, to our knowledge, is significantly larger than prior works where only up to 5 device streams are associated.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967224"}, {"primary_key": "4164439", "vector": [], "sparse_vector": [], "title": "Multimodal Video Description.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Dong Huk Park", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Real-world web videos often contain cues to supplement visual information for generating natural language descriptions. In this paper we propose a sequence-to-sequence model which explores such auxiliary information. In particular, audio and the topic of the video are used in addition to the visual information in a multimodal framework to generate coherent descriptions of videos \"in the wild\". In contrast to current encoder-decoder based models which exploit visual information only during the encoding stage, our model fuses multiple sources of information judiciously, showing improvement over using the different modalities separately. We based our multimodal video description network on the state-of-the-art sequence to sequence video to text (S2VT) model and extended it to take advantage of multiple modalities. Extensive experiments on the challenging MSR-VTT dataset are carried out to show the superior performance of the proposed approach on natural videos found in the web.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984066"}, {"primary_key": "4164440", "vector": [], "sparse_vector": [], "title": "ConTagNet: Exploiting User Context for Image Tag Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, deep convolutional neural networks have shown great success in single-label image classification. However, images usually have multiple labels associated with them which may correspond to different objects or actions present in the image. In addition, a user assigns tags to a photo not merely based on the visual content but also the context in which the photo has been captured. Inspired by this, we propose a deep neural network which can predict multiple tags for an image based on the content as well as the context in which the image is captured. The proposed model can be trained end-to-end and solves a multi-label classification problem. We evaluate the model on a dataset of 1,965,232 images which is drawn from the YFCC100M dataset provided by the organizers of Yahoo-Flickr Grand Challenge. We observe a significant improvement in the prediction accuracy after integrating user-context and the proposed model performs very well in the Grand Challenge.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984068"}, {"primary_key": "4164441", "vector": [], "sparse_vector": [], "title": "What Makes Photo Cultures Different?", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Billions of photos shared online today are created by people with different socio-economic characteristics living in different locations. We introduce a number of methods for quantifying the differences between such \"photo cultures\" and apply them to a large collection of Instagram images shared in five mega-cities around the world. First, we extract image content and style features and use them to design a new visualization technique for qualitative analysis of photo cultures. We then use supervised learning to automatically recognize and compare visual activity at different locations and expose surprising connections between geographically distant photo cultures. Finally, we perform a low-level quantitative analysis to understand what makes photo cultures different from each other.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967228"}, {"primary_key": "4164442", "vector": [], "sparse_vector": [], "title": "Joint Image-Text Representation by Gaussian Visual-Semantic Embedding.", "authors": ["<PERSON>", "<PERSON><PERSON> Jin", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "How to jointly represent images and texts is important for tasks involving both modalities. Visual-semantic embedding models have been recently proposed and shown to be effective. The key idea is that by learning a mapping from images into a semantic text space, the algorithm is able to learn a compact and effective joint representation. However, existing approaches simply map each text concept to a single point in the semantic space. Mapping instead to a density distribution provides many interesting advantages, including better capturing uncertainty about each text concept, and enabling better geometric interpretation of concepts such as inclusion, intersection, etc. In this work, we present a novel Gaussian Visual-Semantic Embedding (GVSE) model, which leverages the visual information to model text concepts as Gaussian distributions in semantic space. Experiments in two tasks, image classification and text-based image retrieval on the large scale MIT Places205 dataset, have demonstrated the superiority of our method over existing approaches, with higher accuracy and better robustness.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967212"}, {"primary_key": "4164443", "vector": [], "sparse_vector": [], "title": "News Archive Exploration Combining Face Detection and Tracking with Network Visual Analytics.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shin&apos;<PERSON><PERSON>"], "summary": "Visual analytics helps analytical reasoning and exploration of complex systems, for which it combines the means of interactive visualization, with the power of data analytics. The recent progress in computer vision techniques opens wide applications in real world video archives. Particularly, recent advances in face detection and recognition have been put under the spotlight. The applications of such techniques are often concern intelligence, or peer recognition in photo posted in social networks. We propose to combine those two domains by demonstrating a visual exploration of over a decade of the news program from the Japanese broadcaster NHK News 7. We derive social networks from face detection and tracking of this large dataset. With the help of a little domain knowledge, we monitor the activity of political public figures and explore the archive. This allows understanding and comparison of the politico-media scene presented by NHK under different Prime Minister's governance. The social networks are interactive, and also allow to explore the multimedia database and explore its video content.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973823"}, {"primary_key": "4164446", "vector": [], "sparse_vector": [], "title": "Families in the Wild (FIW): Large-Scale Kinship Image Database and Benchmarks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Yun Fu"], "summary": "We present the largest kinship recognition dataset to date, Families in the Wild (FIW). Motivated by the lack of a single, unified dataset for kinship recognition, we aim to provide a dataset that captivates the interest of the research community. With only a small team, we were able to collect, organize, and label over 10,000 family photos of 1,000 families with our annotation tool designed to mark complex hierarchical relationships and local label information in a quick and efficient manner. We include several benchmarks for two image-based tasks, kinship verification and family recognition. For this, we incorporate several visual features and metric learning methods as baselines. Also, we demonstrate that a pre-trained Convolutional Neural Network (CNN) as an off-the-shelf feature extractor outperforms the other feature types. Then, results were further boosted by fine-tuning two deep CNNs on FIW data: (1) for kinship verification, a triplet loss function was learned on top of the network of pre-trained weights; (2) for family recognition, a family-specific softmax classifier was added to the network.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967219"}, {"primary_key": "4164449", "vector": [], "sparse_vector": [], "title": "InnerView: Learning Place Ambiance from Social Media Images.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "In the recent past, there has been interest in characterizing the physical and social ambiance of urban spaces to understand how people perceive and form impressions of these environments based on physical and psychological constructs. Building on our earlier work on characterizing ambiance of indoor places, we present a methodology to automatically infer impressions of place ambiance, using generic deep learning features extracted from images publicly shared on Foursquare. We base our methodology on a corpus of 45,000 images from 300 popular places in six cities on Foursquare. Our results indicate the feasibility to automatically infer place ambiance with a maximum R2 of 0.53 using features extracted from a pre-trained convolutional neural network. We found that features extracted from deep learning with convolutional nets consistently outperformed individual and combinations of several low-level image features (including Color, GIST, HOG and LBP) to infer all the studied 13 ambiance dimensions. Our work constitutes a first study to automatically infer ambiance impressions of indoor places from deep features learned from images shared on social media.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967261"}, {"primary_key": "4164450", "vector": [], "sparse_vector": [], "title": "Analyzing Structural Characteristics of Object Category Representations From Their Semantic-part Distributions.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Studies from neuroscience show that part-mapping computations are employed by human visual system in the process of object recognition. In this paper, we present an approach for analyzing semantic-part characteristics of object category representations. For our experiments, we use category-epitome, a recently proposed sketch-based spatial representation for objects. To enable part-importance analysis, we first obtain semantic-part annotations of hand-drawn sketches originally used to construct the epitomes. We then examine the extent to which the semantic-parts are present in the epitomes of a category and visualize the relative importance of parts as a word cloud. Finally, we show how such word cloud visualizations provide an intuitive understanding of category-level structural trends that exist in the category-epitome object representations. Our method is general in applicability and can also be used to analyze part-based visual object representations for other depiction methods such as photographic images.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967190"}, {"primary_key": "4164451", "vector": [], "sparse_vector": [], "title": "SwiDeN: Convolutional Neural Networks For Depiction Invariant Object Recognition.", "authors": ["<PERSON>", "Shiv Surya", "<PERSON><PERSON>vas S. S<PERSON>", "<PERSON><PERSON>"], "summary": "Current state of the art object recognition architectures achieve impressive performance but are typically specialized for a single depictive style (e.g. photos only, sketches only). In this paper, we present SwiDeN: our Convolutional Neural Network (CNN) architecture which recognizes objects regardless of how they are visually depicted (line drawing, realistic shaded drawing, photograph etc.). In SwiDeN, we utilize a novel `deep' depictive style-based switching mechanism which appropriately addresses the depiction-specific and depiction-invariant aspects of the problem. We compare SwiDeN with alternative architectures and prior work on a 50-category Photo-Art dataset containing objects depicted in multiple styles. Experimental results show that SwiDeN outperforms other approaches for the depiction-invariant object recognition problem.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967208"}, {"primary_key": "4164452", "vector": [], "sparse_vector": [], "title": "Enabling My Robot To Play Pictionary: Recurrent Neural Networks For Sketch Recognition.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Freehand sketching is an inherently sequential process. Yet, most approaches for hand-drawn sketch recognition either ignore this sequential aspect or exploit it in an ad-hoc manner. In our work, we propose a recurrent neural network architecture for sketch object recognition which exploits the long-term sequential and structural regularities in stroke data in a scalable manner. Specifically, we introduce a Gated Recurrent Unit based framework which leverages deep sketch features and weighted per-timestep loss to achieve state-of-the-art results on a large database of freehand object sketches across a large number of object categories. The inherently online nature of our framework is especially suited for on-the-fly recognition of objects as they are being drawn. Thus, our framework can enable interesting applications such as camera-equipped robots playing the popular party game Pictionary with human players and generating sparsified yet recognizable sketches of objects.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967220"}, {"primary_key": "4164453", "vector": [], "sparse_vector": [], "title": "Multimodal Interest Level Estimation via Variational Bayesian Mixture of Robust CCA.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents a method which estimates interest level while watching videos, based on collaborative use of facial expression and biological signals such as electroencephalogram (EEG) and electrocardiogram (ECG). To the best of our knowledge, no studies have been carried out on the collaborative use of facial expression and biological signals for estimating interest level. Since training data, which is used for estimating interest level, is generally small and imbalanced, Variational Bayesian Mixture of Robust Canonical Correlation Analysis (VBMRCCA) is newly applied to facial expression and biological signals, which are obtained from users while they are watching the videos. Unlike some related works, VBMRCCA is used to obtain the posterior distributions which represent the latent correlation between facial expression and biological signals in our method. Then, the users' interest level can be estimated by comparing the posterior distributions of the positive class data with those of the negative. Consequently, successful interest level estimation, via collaborative use of facial expression and biological signals, becomes feasible.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967248"}, {"primary_key": "4164454", "vector": [], "sparse_vector": [], "title": "Detecting Sarcasm in Multimodal Social Platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Sarcasm is a peculiar form of sentiment expression, where the surface sentiment differs from the implied sentiment. The detection of sarcasm in social media platforms has been applied in the past mainly to textual utterances where lexical indicators (such as interjections and intensifiers), linguistic markers, and contextual information (such as user profiles, or past conversations) were used to detect the sarcastic tone. However, modern social media platforms allow to create multimodal messages where audiovisual content is integrated with the text, making the analysis of a mode in isolation partial. In our work, we first study the relationship between the textual and visual aspects in multimodal posts from three major social media platforms, i.e., Instagram, Tumblr and Twitter, and we run a crowdsourcing task to quantify the extent to which images are perceived as necessary by human annotators. Moreover, we propose two different computational frameworks to detect sarcasm that integrate the textual and visual modalities. The first approach exploits visual semantics trained on an external dataset, and concatenates the semantics features with state-of-the-art textual features. The second method adapts a visual neural network initialized with parameters trained on ImageNet to multimodal sarcastic posts. Results show the positive effect of combining modalities for the detection of sarcasm across platforms and methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964321"}, {"primary_key": "4164456", "vector": [], "sparse_vector": [], "title": "DeepSketch2Image: Deep Convolutional Neural Networks for Partial Sketch Recognition and Image Retrieval.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Freehand sketches are an interesting universal form of visual representation. Sketching has become easily accessible with many of the devices that we use on a daily basis. In this paper, we propose a system for real-time sketch recognition and similarity search. Our system is able to recognize partial sketches from 250 object categories. It is then able to retrieve similar sketches but also images/photographs. In this work, we propose to use deep convolutional neural networks (ConvNets) for partial sketch recognition and feature extraction. Features are extracted from sketches and image contours in order to be used as a basis for similarity search using k-Nearest Neighbors (kNN). Our system demonstrates promising results in identifying similar images, and could be integrated in larger content-based search engines.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973828"}, {"primary_key": "4164457", "vector": [], "sparse_vector": [], "title": "ThePlantGame: Actively Training Human Annotators for Domain-specific Crowdsourcing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In a typical citizen science/crowdsourcing environment, the contributors label items. When there are few labels, it is straightforward to train contributors and judge the quality of their labels by giving a few examples with known answers. Neither is true when there are thousands of domain-specific labels and annotators with heterogeneous skills. This demo paper presents an Active User Training framework implemented as a serious game called ThePlantGame. It is based on a set of data-driven algorithms allowing to (i) actively train annotators, and (ii) evaluate the quality of contributors' answers on new test items to optimize predictions.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973820"}, {"primary_key": "4164460", "vector": [], "sparse_vector": [], "title": "Transform-Invariant Convolutional Neural Networks for Image Classification and Search.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "S<PERSON>yan Sun", "Dacheng Tao"], "summary": "Convolutional neural networks (CNNs) have achieved state-of-the-art results on many visual recognition tasks. However, current CNN models still exhibit a poor ability to be invariant to spatial transformations of images. Intuitively, with sufficient layers and parameters, hierarchical combinations of convolution (matrix multiplication and non-linear activation) and pooling operations should be able to learn a robust mapping from transformed input images to transform-invariant representations. In this paper, we propose randomly transforming (rotation, scale, and translation) feature maps of CNNs during the training stage. This prevents complex dependencies of specific rotation, scale, and translation levels of training images in CNN models. Rather, each convolutional kernel learns to detect a feature that is generally helpful for producing the transform-invariant answer given the combinatorially large variety of transform levels of its input feature maps. In this way, we do not require any extra training supervision or modification to the optimization process and training images. We show that random transformation provides significant improvements of CNNs on many benchmark tasks, including small-scale image recognition, large-scale image recognition, and image retrieval.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964316"}, {"primary_key": "4164461", "vector": [], "sparse_vector": [], "title": "Frame- and Segment-Level Features and Candidate Pool Evaluation for Video Caption Generation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present our submission to the Microsoft Video to Language Challenge of generating short captions describing videos in the challenge dataset. Our model is based on the encoder--decoder pipeline, popular in image and video captioning systems. We propose to utilize two different kinds of video features, one to capture the video content in terms of objects and attributes, and the other to capture the motion and action information. Using these diverse features we train models specializing in two separate input sub-domains. We then train an evaluator model which is used to pick the best caption from the pool of candidates generated by these domain expert models. We argue that this approach is better suited for the current video captioning task, compared to using a single model, due to the diversity in the dataset. Efficacy of our method is proven by the fact that it was rated best in MSR Video to Language Challenge, as per human evaluation. Additionally, we were ranked second in the automatic evaluation metrics based table.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984062"}, {"primary_key": "4164464", "vector": [], "sparse_vector": [], "title": "Frame Untangling for Unobtrusive Display-Camera Visible Light Communication.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Pairing displays and cameras can open up convenient and \"free\" visible light communication channels. But in realistic settings, the synchronization between displays (transmitters) and cameras (receivers) can be far more involved than assumed in the literature. This study aims to analyze and model the temporal behaviors of displays and cameras to make the visible light communication channel between the two more robust, while maintaining perceptual transparency of the transmitted data.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967302"}, {"primary_key": "4164465", "vector": [], "sparse_vector": [], "title": "Situation Recognition from Multimodal Data.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "No abstract available.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2986913"}, {"primary_key": "4164467", "vector": [], "sparse_vector": [], "title": "OpenVQ: A Video Quality Assessment Toolkit.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents an open source video quality assessment tool called OpenVQ and OPVQ, an objective quality metric based on work of the Video Quality Experts Group. It was primarily developed to satisfy the need of the community for a freely accessible and easy-to-use quality assessment tool, which would remove the need for using throughly outdated methods that is still prevalent because only those are easily available. OpenVQ is implemented as a toolbox of full-reference quality assessment methods, which can be extended with improved methods in the future. The primary method provided by OpenVQ at this time is OPVQ, the Open Perceptual Video Quality metric. It is very much inspired by the formal model of ITU-T Rec. J.247 Annex B, however with many modifications to deal with errors and inconsistencies in formulas and pseudo code, and restrictions to avoid violating known patents. OPVQ has been validated with several IRRCyN datasets and was found to yield results similar to those reported in ITU.T J.247. OpenVQ implements also PSNR (Peak signal-to-noise-ratio), because it is in high demand in spite of its bad performance, and SSIM (Structural similarity index), an image quality metric that is known to perform quite well in many cases.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973800"}, {"primary_key": "4164470", "vector": [], "sparse_vector": [], "title": "Joint Graph Learning and Video Segmentation via Multiple Cues and Topology Calibration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Video segmentation has become an important and active research area with a large diversity of proposed approaches. Graph-based methods, enabling top performance on recent benchmarks, usually focus on either obtaining a precise similarity graph or designing efficient graph cutting strategies. However, these two components are often conducted in two separated steps, and thus the obtained similarity graph may not be the optimal one for segmentation and this may lead to suboptimal results. In this paper, we propose a novel framework, joint graph learning and video segmentation (JGLVS)}, which learns the similarity graph and video segmentation simultaneously. JGLVS learns the similarity graph by assigning adaptive neighbors for each vertex based on multiple cues (appearance, motion, boundary and spatial information). Meanwhile, the new rank constraint is imposed to the Laplacian matrix of the similarity graph, such that the connected components in the resulted similarity graph are exactly equal to the number of segmentations. Furthermore, JGLVS can automatically weigh multiple cues and calibrate the pairwise distance of superpixels based on their topology structures. Most noticeably, empirical results on the challenging dataset VSB100 show that JGLVS achieves promising performance on the benchmark dataset which outperforms the state-of-the-art by up to 11% for the BPR metric.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964295"}, {"primary_key": "4164471", "vector": [], "sparse_vector": [], "title": "Semantic Description of Timbral Transformations in Music Production.", "authors": ["Ryan Stables", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "György Fazekas", "<PERSON>"], "summary": "In music production, descriptive terminology is used to define perceived sound transformations. By understanding the underlying statistical features associated with these descriptions, we can aid the retrieval of contextually relevant processing parameters using natural language, and create intelligent systems capable of assisting in audio engineering. In this study, we present an analysis of a dataset containing descriptive terms gathered using a series of processing modules, embedded within a Digital Audio Workstation. By applying hierarchical clustering to the audio feature space, we show that similarity in term representations exists within and between transformation classes. Furthermore, the organisation of terms in low-dimensional timbre space can be explained using perceptual concepts such as size and dissonance. We conclude by performing Latent Semantic Indexing to show that similar groupings exist based on term frequency.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967238"}, {"primary_key": "4164474", "vector": [], "sparse_vector": [], "title": "Automatic Reflection Removal using Gradient Intensity and Motion Cues.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a method to separate the background image and reflection from two photos that are taken in front of a transparent glass under slightly different viewpoints. In our method, the SIFT-flow between two images is first calculated and a motion hierarchy is constructed from the SIFT-flow at multiple levels of spatial smoothness. To distinguish background edges and reflection edges, we calculate a motion score for each edge pixel by its variance along the motion hierarchy. Alternatively, we make use of the so-called superpixels to group edge pixels into edge segments and calculate the motion scores by averaging over each segment. In the meantime, we also calculate an intensity score for each edge pixel by its gradient magnitude. We combine both motion and intensity scores to get a combination score. A binary labelling (for separation) can be obtained by thresholding the combination scores. The background image is finally reconstructed from the separated gradients. Compared to the existing approaches that require a sequence of images or a video clip for the separation, we only need two images, which largely improves its feasibility. Various challenging examples are tested to validate the effectiveness of our method.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967264"}, {"primary_key": "4164475", "vector": [], "sparse_vector": [], "title": "Exploiting Objects with LSTMs for Video Categorization.", "authors": ["Yongqing Sun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yu-Gang <PERSON>"], "summary": "Temporal dynamics play an important role for video classification. In this paper, we propose to leverage high-level semantic features to open the \"black box\" of the state-of-the-art temporal model, Long Short Term Memory (LSTM), with an aim to understand what is learned. More specifically, we first extract object features from a state-of-the-art CNN model that is trained to recognize 20K objects. Then we leverage LSTM with the extracted features as inputs to capture the temporal dynamics in videos. In combination with spatial and motion information, we achieve improvements for supervised video categorization. Furthermore, by masking the inputs, we demonstrate what is learned by LSTM, namely (i) which objects are crucial for recognizing a class-of-interest; (ii) how the LSTM model could assist the temporal localization of these detected objects.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967199"}, {"primary_key": "4164476", "vector": [], "sparse_vector": [], "title": "Processing-Aware Privacy-Preserving Photo Sharing over Online Social Networks.", "authors": ["Weiwei Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the ever-increasing popularity of mobile devices and online social networks (OSNs), sharing photos online has become extremely easy and popular. The privacy issues of shared photos and the associated protection schemes have received significant attention in recent years. In this work, we address the problem of designing privacy-preserving, high-fidelity, storage-efficient photo sharing solution over Facebook. We first conduct an in-depth study on the manipulations that Facebook performs to the uploaded images. With the awareness of such information, we suggest a DCT-domain image encryption scheme that is robust against these lossy operations. As validated by our experimental results, superior performance in terms of security, quality of the reconstructed images, and storage cost can be achieved.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967288"}, {"primary_key": "4164477", "vector": [], "sparse_vector": [], "title": "Spatio-Temporal Analysis of Bandwidth Maps for Geo-Predictive Video Streaming in Mobile Environments.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "High quality video streaming is increasingly popular among mobile users especially with the rise of high speed LTE networks. But despite the high network capacity of LTE, streaming videos may suffer from disruptions since the quality of the video depends on the network bandwidth, which in turn depends on the location of the user with respect to their cell tower, crowd levels, and objects like buildings and trees. Maintaining a good video playback experience becomes even more challenging if the user is moving fast in a vehicle, the location is changing rapidly and the available bandwidth fluctuates. In this paper we introduce GeoStream, a video streaming system that relies on the use of geostatistics to analyze spatio-temporal bandwidth. Data measured from users' streaming videos while they are commuting is collected in order to predict future bandwidth availability in unknown locations. Our approach investigates and leverages the relationship between the separation distance between sample bandwidth points, the time they were captured, and the semivariance, expressed by a variogram plot, to finally predict the future bandwidth at unknown locations. Using the datasets from GTube our experimental results show an improved performance.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964333"}, {"primary_key": "4164478", "vector": [], "sparse_vector": [], "title": "Intelli-Wrench: Smart Navigation Tool for Mechanical Assembly and Maintenance.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present Intelli-Wrench, a smart tool for providing just-in-time information for mechanical assembly and maintenance workers, which releases the workers from miss-operation and annoying manual documents. The Intelli-Wrench captures the image of a bolt head as its \"fingerprint\" by using FIBAR (Fingerprint Imaging by Binary Angular Reflection) imaging method. The fingerprint image is then uploaded to a cloud server and matched with parts databases in order to provide the specific information associated with the bolt. Receiving the information, the Intelli-Wrench informs the workers about the designated location and torque requirements of the bolt. This direct integration of information retrieval into a tangible tool provides immediate access to relevant information otherwise found in manual documents. Furthermore, the Intelli-Wrench automatically logs the interaction and eliminates the annoying pointing-and-calling procedure which is the traditional way of secure servicing. We demonstrate a working prototype and interaction scenario.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973833"}, {"primary_key": "4164480", "vector": [], "sparse_vector": [], "title": "Capped Lp-Norm Graph Embedding for Photo Clustering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Photos are a predominant source of information on a global scale. Cluster analysis of photos can be applied to situation recognition and understanding cultural dynamics. Graph-based learning provides a current approach for modeling data in clustering problems. However, the performance of this framework depends heavily on initial graph construction by input data. Data outliers degrade graph quality, leading to poor clustering results. We designed a new capped lp-norm graph-based model to reduce the impact of outliers. This is accomplished by allowing the data graph to self adjust as part of the graph embedding. Furthermore, we derive an iterative algorithm to solve the objective function optimization problem. Experiments on four real-world benchmark data sets and Yahoo Flickr Creative Commons data set show the effectiveness of this new graph-based capped lp-norm clustering method.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967257"}, {"primary_key": "4164482", "vector": [], "sparse_vector": [], "title": "Deeply-Supervised Recurrent Convolutional Neural Network for Saliency Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposes a novel saliency detection method by developing a deeply-supervised recurrent convolutional neural network (DSRCNN), which performs a full image-to-image saliency prediction. For saliency detection, the local, global, and contextual information of salient objects is important to obtain a high quality salient map. To achieve this goal, the DSRCNN is designed based on VGGNet-16. Firstly, the recurrent connections are incorporated into each convolutional layer, which can make the model more powerful for learning the contextual information. Secondly, side-output layers are added to conduct the deeply-supervised operation, which can make the model learn more discriminative and robust features by effecting the intermediate layers. Finally, all of the side-outputs are fused to integrate the local and global information to get the final saliency detection results. Therefore, the DSRCNN combines the advantages of recurrent convolutional neural networks and deeply-supervised nets. The DSRCNN model is tested on five benchmark datasets, and experimental results demonstrate that the proposed method significantly outperforms the state-of-the-art saliency detection approaches on all test datasets.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967250"}, {"primary_key": "4164484", "vector": [], "sparse_vector": [], "title": "Real-time Wearable Computer Vision System for Improved Museum Experience.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The goal of this work is to implement a real-time computer vision system that can run on wearable devices to perform object classification and artwork recognition, to improve the experience of a museum visit through understanding the interests of users. Object classification helps to understand the context of the visit, e.g. differentiating when a visitor is talking with people, or just wandering through the museum, or if he is looking at an exhibit that interests him. Artwork recognition allows to provide automatically information of the observed item or to create a user profile based on what and how long a user has observed artworks.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973813"}, {"primary_key": "4164486", "vector": [], "sparse_vector": [], "title": "Assessing 3D Scan Quality Through Paired-comparisons Psychophysics.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "Consumer 3D scanners and depth cameras are increasingly being used to generate content and avatars for Virtual Reality (VR) environments and avoid the inconveniences of hand modeling; however, it is sometimes difficult to evaluate quantitatively the mesh quality at which consumer available 3D scans should be exported, and whether the object perception might be affected by its shading. We propose using a paired-comparisons test based on psychophysics of perception to do that evaluation. As psychophysics is not subject to opinion, skill level, mental state, or economic situation it can be considered a quantitative way to measure how people perceive the mesh quality. In particular, we compare four different levels of mesh quality (1K, 5K, 10K and 20K triangles). We present two studies within subjects: in one we investigate the influences of seeing an object in a regular screen vs. in a Head Mounted Display (HMD); while in the second experiment we aim at detecting the effects of shading into quality perception. At each iteration of the pair-test comparisons participants pick the mesh that they think had higher quality; by the end of the experiment we compile a preference matrix. The results show a correlation between real and assessed quality, despite participants' reported uncertainty. We also find an interaction with quality and shading, which gains importance for quality perception when the mesh has high definition. Furthermore, we assess the subjective realism of the most/least preferred scans using an Immersive Augmented Reality (IAR) video-see-through setup to compare the real vs the 3D scanned object in the same HMD environment. Results show higher levels of realism were perceived through the HMD than when using a regular monitor, although the quality was similarly perceived in both systems.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967200"}, {"primary_key": "4164487", "vector": [], "sparse_vector": [], "title": "Action Recognition Using Local Consistent Group Sparse Coding with Spatio-Temporal Structure.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> An", "Yun Fu"], "summary": "This paper presents a novel and efficient framework for human action recognition through integrating the local consistent group sparse representation with spatio-temporal structure of each video sequence. We firstly propose a sparse encoding scheme named local consistent group sparse coding (LCGSC) to generate the sparse representation of each video sequence. The novel encoding scheme takes global structural information of features belonging to one group into consideration as well as the local correlations between similar features. In order to incorporate the spatio-temporal structures, an average location (AL) model is proposed to describe the distribution of each visual word along the spatio-temporal coordinates on the basis of the obtained sparse codes. Eventually, each video sequence is jointly represented by the sparse representation and the spatio-temporal layouts which fully model its motion, appearance and spatio-temporal information. Our framework is computationally efficient and achieves comparable performance on the challenging datasets with state-of-the-art methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967234"}, {"primary_key": "4164491", "vector": [], "sparse_vector": [], "title": "Summary for AVEC 2016: Depression, Mood, and Emotion Recognition Workshop and Challenge.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The sixth Audio-Visual Emotion Challenge and workshop AVEC 2016 was held in conjunction ACM Multimedia'16. This year the AVEC series addresses two distinct sub-challenges, multi-modal emotion recognition and audio-visual depression detection. Both sub-challenges are in a way a return to AVEC's past editions: the emotion sub-challenge is based on the same dataset as the one used in AVEC 2015, and depression analysis was previously addressed in AVEC 2013/2014. In this summary, we mainly describe participation and its conditions.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2980532"}, {"primary_key": "4164493", "vector": [], "sparse_vector": [], "title": "A Robust Distance with Correlated Metric Learning for Multi-Instance Multi-Label Data.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "In multi-instance data, every object is a bag that contains multiple elements or instances. Each bag may be assigned to one or more classes, such that it has at least one instance corresponding to every assigned class. However, since the annotations are at bag-level, there is no direct association between the instances within a bag and the assigned class labels, hence making the problem significantly challenging. While existing methods have mostly focused on Bag-to-Bag or Class-to-Bag distances, in this paper, we address the multiple instance learning problem using a novel Bag-to-Class distance measure. This is based on two observations: (a) existence of outliers is natural in multi-instance data, and (b) there may exist multiple instances within a bag that belong to a particular class. In order to address these, in the proposed distance measure (a) we employ L1-distance that brings robustness against outliers, and (b) rather than considering only the most similar instance-pair during distance computation as done by existing methods, we consider a subset of instances within a bag while determining its relevance to a given class. We parameterize the proposed distance measure using class-specific distance metrics, and propose a novel metric learning framework that explicitly captures inter-class correlations within the learned metrics. Experiments on two popular datasets demonstrate the effectiveness of the proposed distance measure and metric learning.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967259"}, {"primary_key": "4164494", "vector": [], "sparse_vector": [], "title": "Kvazaar: Open-Source HEVC/H.265 Encoder.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Kvazaar is an academic software video encoder for the emerging High Efficiency Video Coding (HEVC/H.265) standard. It provides students, academic professionals, and industry experts a free, cross-platform HEVC encoder for x86, x64, PowerPC, and ARM processors on Windows, Linux, and Mac. Kvazaar is being developed from scratch in C and optimized in Assembly under the LGPLv2.1 license. The development is being coordinated by Ultra Video Group at Tampere University of Technology (TUT) and the implementation work is carried out by an active community on GitHub. Developer friendly source code of Kvazaar makes joining easy for new developers. Currently, Kvazaar includes all essential coding tools of HEVC and its modular source code facilitates parallelization on multi and manycore processors as well as algorithm acceleration on hardware. Kvazaar is able to attain real-time HEVC coding speed up to 4K video on an Intel 14-core Xeon processor. Kvazaar is also supported by FFmpeg and Libav. These de-facto standard multimedia frameworks boost Kvazaar popularity and enable its joint usage with other well-known multimedia processing tools. Nowadays, Kvazaar is an integral part of teaching at TUT and it has got a key role in three Eureka Celtic-Plus projects in the fields of 4K TV broadcasting, virtual advertising, Video on Demand, and video surveillance.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973796"}, {"primary_key": "4164495", "vector": [], "sparse_vector": [], "title": "Transportation Mode Detection on Mobile Devices Using Recurrent Nets.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present an approach to the use of Recurrent Neural Networks (RNN) for transportation mode detection (TMD) on mobile devices. The proposed model, called Control Gate-based Recurrent Neural Network (CGRNN), is an end-to-end model that works directly with raw signals from an embedded accelerometer. As mobile devices have limited computational resources, we evaluate the model in terms of accuracy, computational cost, and memory usage. Experiments on the HTC transportation mode dataset demonstrate that our proposed model not only exhibits remarkable accuracy, but also is efficient with low resource consumption.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967249"}, {"primary_key": "4164497", "vector": [], "sparse_vector": [], "title": "Accelerating Convolutional Neural Networks for Mobile Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Convolutional neural networks (CNNs) have achieved remarkable performance in a wide range of computer vision tasks, typically at the cost of massive computational complexity. The low speed of these networks may hinder real-time applications especially when computational resources are limited. In this paper, an efficient and effective approach is proposed to accelerate the test-phase computation of CNNs based on low-rank and group sparse tensor decomposition. Specifically, for each convolutional layer, the kernel tensor is decomposed into the sum of a small number of low multilinear rank tensors. Then we replace the original kernel tensors in all layers with the approximate tensors and fine-tune the whole net with respect to the final classification task using standard backpropagation. \\\\ Comprehensive experiments on ILSVRC-12 demonstrate significant reduction in computational complexity, at the cost of negligible loss in accuracy. For the widely used VGG-16 model, our approach obtains a 6.6$\\times$ speed-up on PC and 5.91$\\times$ speed-up on mobile device of the whole network with less than 1\\% increase on top-5 error.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967280"}, {"primary_key": "4164498", "vector": [], "sparse_vector": [], "title": "Discriminative Paired Dictionary Learning for Visual Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "A Paired Discriminative K-SVD (PD-KSVD) dictionary learning method is presented in this paper for visual recognition. To achieve high discrimination and low reconstruction errors simultaneously for sparse coding, we propose to learn class-specific sub-dictionaries from pairs of positive and negative classes to jointly reduce the reconstruction errors of positive classes while keeping the reconstruction errors of negative classes high. Then, multiple sub-dictionaries are concatenated with respect to the same negative class so that the non-zero sparse coefficients can be discriminatively distributed to improve classification accuracy. Compared to the current dictionary learning methods, the proposed PD-KSVD method achieves very competitive performance in a variety of visual recognition tasks on several publicly available datasets.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967184"}, {"primary_key": "4164499", "vector": [], "sparse_vector": [], "title": "Local Diffusion Map Signature for Symmetry-aware Non-rigid Shape Correspondence.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Identifying accurate correspondences information among different shapes is of great importance in shape analysis such as shape registration, segmentation and retrieval. This paper aims to develop a paradigm to address the challenging issues posed by shape structural variation and symmetry ambiguity. Specifically, the proposed research developed a novel shape signature based on local diffusion map on 3D surface, which is used to identify the shape correspondence through graph matching process. The developed shape signature, named local diffusion map signature (LDMS), is obtained by projecting heat diffusion distribution on 3D surface into 2D images along the surface normal direction with orientation determined by gradients of heat diffusion field. The local diffusion map signature is able to capture the concise geometric essence that is deformation-insensitive and symmetry-aware. Experimental results on 3D shape correspondence demonstrate the superior performance of our proposed method over other state-of-the-art techniques in identifying correspondences for non-rigid shapes with symmetry ambiguity.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967277"}, {"primary_key": "4164500", "vector": [], "sparse_vector": [], "title": "Global Consistent Shape Correspondence for Efficient and Effective Active Shape Models.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Finding the accurate corresponded landmarks from a collection of shape instances plays critical role in constructing active shape models (ASMs). We have developed a global consistent shape correspondence paradigm for efficient and effective active shape models to address challenging issues in statistical shape modelling. Specifically, in this paper, we developed techniques to perform a fast multiple shape matching to identify global consistent shape correspondence from a set of training shape instances via efficient low-rank recovery optimization. High quality ASMs can then be constructed based on the identified corresponded points. The entire process is unsupervised without manual annotation as well as free of selection of anatomically significant point. Experimental results on mobile hand image data demonstrate the superior performance of our proposed method over other state-of-the-art techniques like MDL in constructing active shape models.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967283"}, {"primary_key": "4164501", "vector": [], "sparse_vector": [], "title": "Towards Ultra-Low-Bitrate Video Conferencing Using Facial Landmarks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Providing high-quality video conferencing experience over the best-effort Internet and wireless networks is challenging, because 2D videos are bulky. In this paper, we exploit the common structure of conferencing videos for an ultra-low-bitrate video conferencing system. In particular, we design, implement, optimize, and evaluate a video conferencing system, which: (i) extracts facial landmarks, (ii) transmits the selected facial landmarks and 2D images, and (iii) warps the untransmitted 2D images at the receiver. Several optimization techniques are adopted for minimizing the running time and maximizing the video quality, e.g., the image and warping frames are optimally determined based on network conditions and video content. The experiment results from real conferencing videos reveal that our proposed system: (i) outperforms the state-of-the-art x265 by up to 11.05 dB in PSNR (Peak Signal-to-Noise Ratio), (ii) adapts to different video content and network conditions, and (iii) runs in real-time at about 12 frame-per-second.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967284"}, {"primary_key": "4164502", "vector": [], "sparse_vector": [], "title": "Personal Multi-view Viewpoint Recommendation based on Trajectory Distribution of the Viewing Target.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Multi-camera videos with abundant information and high flexibility are expected to be useful in a wide range of applications, such as surveillance systems, web lecture broadcasting, concerts and sports viewing, etc. Viewers can enjoy a high-presence viewing experience of their own choosing by means of virtual camera switching and controlling viewing interfaces. However, some viewers may feel annoyed by continual manual viewpoint selection, especially when the number of selectable viewpoints is relatively large. In order to solve this issue, we propose an automatic viewpoint-recommending method designed especially for soccer games. This method focuses on a viewer's personal preference for viewpoint-selection, instead of common and professional editing rules. We assume that the different trajectory distributions cause a difference in the viewpoint selection according to personal preference. We therefore analyze the relationship between the viewer's personal viewpoint selecting tendency and the spatio-temporal game context. We compare methods based on a Gaussian mixture model, a general histogram+SVM and bag-of-words+SVM to seek the best representation for this relationship. The performance of the proposed methods are verified by assessing the degree of similarity between the recommended viewpoints and the viewers' edited records.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967265"}, {"primary_key": "4164503", "vector": [], "sparse_vector": [], "title": "Tracking Natural Events through Social Media and Computer Vision.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Accurate, efficient, global observation of natural events is important for ecologists, meteorologists, governments, and the public. Satellites are effective but limited by their perspective and by atmospheric conditions. Public images on photo-sharing websites could provide crowd-sourced ground data to complement satellites, since photos contain evidence of the state of the natural world. In this work, we test the ability of computer vision to observe natural events in millions of geo-tagged Flickr photos, over nine years and an entire continent. We use satellites as (noisy) ground truth to train two types of classifiers, one that estimates if a Flickr photo has evidence of an event, and one that aggregates these estimates to produce an observation for given times and places. We present a web tool for visualizing the satellite and photo observations, allowing scientists to explore this novel combination of data sources.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984067"}, {"primary_key": "4164504", "vector": [], "sparse_vector": [], "title": "Scalable Compression of Deep Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep neural networks generally involve some layers with millions of parameters, making them difficult to be deployed and updated on devices with limited resources such as mobile phones and other smart embedded systems. In this paper, we propose a scalable representation of the network parameters, so that different applications can select the most suitable bit rate of the network based on their own storage constraints. Moreover, when a device needs to upgrade to a high-rate network, the existing low-rate network can be reused, and only some incremental data are needed to be downloaded. We first hierarchically quantize the weights of a pre-trained deep neural network to enforce weight sharing. Next, we adaptively select the bits assigned to each layer given the total bit budget. After that, we retrain the network to fine-tune the quantized centroids. Experimental results show that our method can achieve scalable compression with graceful degradation in the performance.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967273"}, {"primary_key": "4164505", "vector": [], "sparse_vector": [], "title": "Action Recognition Based on Joint Trajectory Maps Using Convolutional Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Li", "<PERSON><PERSON>", "Wanqing Li"], "summary": "Recently, Convolutional Neural Networks (ConvNets) have shown promising performances in many computer vision tasks, especially image-based recognition. How to effectively use ConvNets for video-based recognition is still an open problem. In this paper, we propose a compact, effective yet simple method to encode spatio-temporal information carried in 3D skeleton sequences into multiple 2D images, referred to as Joint Trajectory Maps (JTM), and ConvNets are adopted to exploit the discriminative features for real-time human action recognition. The proposed method has been evaluated on three public benchmarks, i.e., MSRC-12 Kinect gesture dataset (MSRC-12), G3D dataset and UTD multimodal human action dataset (UTD-MHAD) and achieved the state-of-the-art results.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967191"}, {"primary_key": "4164506", "vector": [], "sparse_vector": [], "title": "Objectness-aware Semantic Segmentation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Hanqing Lu"], "summary": "Recent advances in semantic segmentation are driven by the success of fully convolutional neural network (FCN). However, the coarse label map from the network and the object discrimination ability for semantic segmentation weaken the performance of those FCN-based models. To address these issues, we propose an objectness-aware semantic segmentation framework (OA-Seg) by jointly learning an object proposal network (OPN) and a lightweight deconvolutional neural network (Light-DCNN). First, OPN is learned based on a fully convolutional architecture to simultaneously predict object bounding boxes and their objectness scores. Second, we design a Light-DCNN to provide a finer upsampling way than FCN. The Light-DCNN is constructed with convolutional layers in VGG-net and their mirrored deconvolutional structure, where all fully-connected layers are removed. And hierarchical classification layers are added to multi-scale deconvolutional features to introduce more contextual information for pixel-wise label prediction. Compared with previous works, our approach performs an obvious decrease on model size and convergence time. Thorough evaluations are performed on the PASCAL VOC 2012 benchmark, and our model yields impressive results on its validation data (70.3% mean IoU) and test data (74.1% mean IoU).", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967232"}, {"primary_key": "4164507", "vector": [], "sparse_vector": [], "title": "MatchDR: Image Correspondence by Leveraging Distance Ratio Constraint.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Image correspondence is to establish the connections between coherent images, which can be quite challenging due to the visual and geometric deformations. This paper proposes a robust image correspondence technique from the perspective of spatial regularity. Specifically, the visual deformation is addressed by introducing the spatial information by enforcing the distance ratio constrain. At the same time, the geometric deformation is tolerated by adopting a smoothness term. Subsequently, image correspondence is formulated as permutation problem, for which, we propose a Gradient Guided Simulated Annealing method for robust optimization. Furthermore, our method is much more memory efficient, where the storage complexity is reduced from O(n4) to O(n2). The experiments on several datasets indicate that our proposed formulation and optimization significantly improve the baselines for both visually-similar and semantically-similar images, where both visual and geometric deformations are present.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967293"}, {"primary_key": "4164510", "vector": [], "sparse_vector": [], "title": "Image Captioning with Deep Bidirectional LSTMs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "This work presents an end-to-end trainable deep bidirectional LSTM (Long-Short Term Memory) model for image captioning. Our model builds on a deep convolutional neural network (CNN) and two separate LSTM networks. It is capable of learning long term visual-language interactions by making use of history and future context information at high level semantic space. Two novel deep bidirectional variant models, in which we increase the depth of nonlinearity transition in different way, are proposed to learn hierarchical visual-language embeddings. Data augmentation techniques such as multi-crop, multi-scale and vertical mirror are proposed to prevent overfitting in training deep models. We visualize the evolution of bidirectional LSTM internal states over time and qualitatively analyze how our models \"translate\" image to sentence. Our proposed models are evaluated on caption generation and image-sentence retrieval tasks with three benchmark datasets: Flickr8K, Flickr30K and MSCOCO datasets. We demonstrate that bidirectional LSTM models achieve highly competitive performance to the state-of-the-art results on caption generation even without integrating additional mechanism (e.g. object detection, attention model etc.) and significantly outperform recent methods on retrieval task", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964299"}, {"primary_key": "4164511", "vector": [], "sparse_vector": [], "title": "Human Pose Estimation from Depth Images via Inference Embedded Multi-task Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Human pose estimation (i.e., locating the body parts / joints of a person) is a fundamental problem in human-computer interaction and multimedia applications. Significant progress has been made based on the development of depth sensors, i.e., accessible human pose prediction from still depth images~\\cite{rf12pami}. However, most of the existing approaches to this problem involve several components/models that are independently designed and optimized, leading to suboptimal performances. In this paper, we propose a novel inference-embedded multi-task learning framework for predicting human pose from still depth images, which is implemented with a deep architecture of neural networks. Specifically, we handle two cascaded tasks: i) generating the heat (confidence) maps of body parts via a fully convolutional network (FCN); ii) seeking the optimal configuration of body parts based on the detected body part proposals via an inference built-in MatchNet~\\cite{mn15cvpr}, which measures the appearance and geometric kinematic compatibility of body parts and embodies the dynamic programming inference as an extra network layer. These two tasks are jointly optimized. Our extensive experiments show that the proposed deep model significantly improves the accuracy of human pose estimation over other several state-of-the-art methods or SDKs. We also release a large-scale dataset for comparison, which includes 100K depth images under challenging scenarios.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964322"}, {"primary_key": "4164512", "vector": [], "sparse_vector": [], "title": "Linear Distance Preserving Pseudo-Supervised and Unsupervised Hashing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the advantage in compact representation and efficient comparison, binary hashing has been extensively investigated for approximate nearest neighbor search. In this paper, we propose a novel and general hashing framework, which simultaneously considers a new linear pair-wise distance preserving objective and point-wise constraint. The direct distance preserving objective aims to keep the linear relationships between the Euclidean distance and the Hamming distance of data points. Based on different point-wise constraints, we propose two methods to instantiate this framework. The first one is a pseudo-supervised hashing method, which uses existing unsupervised hashing methods to generate binary codes as pseudo-supervised information. The second one is an unsupervised hashing method, in which quantization loss is considered. We validate our framework on two large-scale datasets. The experiments demonstrate that our pseudo-supervised method achieves consistent improvement for the state-of-the-art unsupervised hashing methods, while our unsupervised method outperforms the state-of-the-art methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964334"}, {"primary_key": "4164516", "vector": [], "sparse_vector": [], "title": "A Pragmatically Designed Adaptive and Web-compliant Object-based Video Streaming Methodology: Implementation and Subjective Evaluation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The bulk of contemporary online video traffic is encoded in a traditional manner, hereby neglecting most, if not all, of the semantics of the underlying visual scene. One essential piece of semantic information in the context of video streaming is awareness of the objects that jointly constitute the scene. A canonical example of a benefit associated with such object awareness is the ability to subdivide a video fragment in respectively a background and one or more foreground objects. This paper reports on a pragmatically designed video streaming approach that exploits object-related knowledge in order to improve the real-time adaptability of video streaming sessions (manifested in the form of increased granularity in terms of streaming quality control). The proposed approach is completely compliant with present-day video codecs and HTTP Adaptive Streaming schemes, most notably H.264 and MPEG-DASH. Findings from subjecting the proposed video streaming technique to a comparative subjective evaluation suggest that scenarios exist where the presented approach holds the capacity to improve on traditional streaming in terms of user-perceived video quality.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964300"}, {"primary_key": "4164517", "vector": [], "sparse_vector": [], "title": "One Sensor is not Enough: Adapting and Fusing Sensors for the Quality Assessment of User Generated Video.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In comparison to professional productions, UGV often suffers from quality degradations such as camera shakes or harmful occlusions. Quick and reliable algorithms are needed to detect and assess the impact of these quality degradations. Recently, researchers leverage data from auxiliary sensors such as gyroscope readings or accelerometer values for quality assessment. In comparison to video-based approaches, auxiliary sensor-based ones achieve results more quickly but often lack precision.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967297"}, {"primary_key": "4164520", "vector": [], "sparse_vector": [], "title": "Time Matters: Multi-scale Temporalization of Social Media Popularity.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON>"], "summary": "The evolution of social media popularity exhibits rich temporality, i.e., popularities change over time at various levels of temporal granularity. This is influenced by temporal variations of public attentions or user activities. For example, popularity patterns of street snap on Flickr are observed to depict distinctive fashion styles at specific time scales, such as season-based periodic fluctuations for Trench Coat or one-off peak in days for Evening Dress. However, this fact is often overlooked by existing research of popularity modeling. We present the first study to incorporate multiple time-scale dynamics into predicting online popularity. We propose a novel computational framework in the paper, named Multi-scale Temporalization, for estimating popularity based on multi-scale decomposition and structural reconstruction in a tensor space of user, post, and time by joint low-rank constraints. By considering the noise caused by context inconsistency, we design a data rearrangement step based on context aggregation as preprocessing to enhance contextual relevance of neighboring data in the tensor space. As a result, our approach can leverage multiple levels of temporal characteristics and reduce the noise of data decomposition to improve modeling effectiveness. We evaluate our approach on two large-scale Flickr image datasets with over 1.8 million photos in total, for the task of popularity prediction. The results show that our approach significantly outperforms state-of-the-art popularity prediction techniques, with a relative improvement of 10.9%-47.5% in terms of prediction accuracy.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964335"}, {"primary_key": "4164521", "vector": [], "sparse_vector": [], "title": "Multi-Stream Multi-Class Fusion of Deep Networks for Video Classification.", "authors": ["<PERSON><PERSON><PERSON>", "Yu-Gang <PERSON>", "<PERSON>", "<PERSON><PERSON>", "Xiangyang Xue"], "summary": "This paper studies deep network architectures to address the problem of video classification. A multi-stream framework is proposed to fully utilize the rich multimodal information in videos. Specifically, we first train three Convolutional Neural Networks to model spatial, short-term motion and audio clues respectively. Long Short Term Memory networks are then adopted to explore long-term temporal dynamics. With the outputs of the individual streams on multiple classes, we propose to mine class relationships hidden in the data from the trained models. The automatically discovered relationships are then leveraged in the multi-stream multi-class fusion process as a prior, indicating which and how much information is needed from the remaining classes, to adaptively determine the optimal fusion weights for generating the final scores of each class. Our contributions are two-fold. First, the multi-stream framework is able to exploit multimodal features that are more comprehensive than those previously attempted. Second, our proposed fusion method not only learns the best weights of the multiple network streams for each class, but also takes class relationship into account, which is known as a helpful clue in multi-class visual classification tasks. Our framework produces significantly better results than the state of the arts on two popular benchmarks, 92.2% on UCF-101 (without using audio) and 84.9% on Columbia Consumer Videos.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964328"}, {"primary_key": "4164522", "vector": [], "sparse_vector": [], "title": "Affective Contextual Mobile Recommender System.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Exponential growth of media consumption in online social networks demands effective recommendation to improve the quality of experience especially for on-the-go mobile users. By means of large-scale trace-driven measurements over mobile Twitter traces from users, we reveal the significance of affective features in shaping users' social media behaviors. Existing recommender systems however, rarely support this psychological effect in real-life. To capture this effect, in this paper we propose Kaleido, a real mobile system to achieve an affect-aware learning-based social media recommendation.Specifically, we design a machine learning mechanism to infer the affective feature within media contents. Furthermore, a cluster-based latent bias model is provided for jointly training the affect, behavior and social contexts. Our comprehensive experiments on Android prototype expose a superior prediction accuracy of 82%, with more than 20% accuracy improvement over existing mobile recommender systems. Moreover, by enabling users to offload their machine learning procedures to the deployed edge-cloud testbed, our system achieves speed-up of a factor of 1,000 against the local data training execution on smartphones.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964327"}, {"primary_key": "4164523", "vector": [], "sparse_vector": [], "title": "Deep Convolutional Neural Network with Independent Softmax for Large Scale Face Recognition.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Yu Kong", "Yun Fu"], "summary": "In this paper, we present our solution to the MS-Celeb-1M Challenge. This challenge aims to recognize 100k celebrities at the same time. The huge number of celebrities is the bottleneck for training a deep convolutional neural network of which the output is equal to the number of celebrities. To solve this problem, an independent softmax model is proposed to split the single classifier into several small classifiers. Meanwhile, the training data are split into several partitions. This decomposes the large scale training procedure into several medium training procedures which can be solved separately. Besides, a large model is also trained and a simple strategy is introduced to merge the two models. Extensive experiments on the MSR-Celeb-1M dataset demonstrate the superiority of the proposed method. Our solution ranks the first and second in two tracks of the final evaluation.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984060"}, {"primary_key": "4164524", "vector": [], "sparse_vector": [], "title": "Learning to Make Better Mistakes: Semantics-aware Visual Food Recognition.", "authors": ["<PERSON>", "<PERSON>", "Rosario Uceda-Sosa", "<PERSON>"], "summary": "We propose a visual food recognition framework that integrates the inherent semantic relationships among fine-grained classes. Our method learns semantics-aware features by formulating a multi-task loss function on top of a convolutional neural network (CNN) architecture. It then refines the CNN predictions using a random walk based smoothing procedure, which further exploits the rich semantic information. We evaluate our algorithm on a large \"food-in-the-wild\" benchmark, as well as a challenging dataset of restaurant food dishes with very few training images. The proposed method achieves higher classification accuracy than a baseline which directly fine-tunes a deep learning network on the target dataset. Furthermore, we analyze the consistency of the learned model with the inherent semantic relationships among food categories. Results show that the proposed approach provides more semantically meaningful results than the baseline method, even in cases of mispredictions.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967205"}, {"primary_key": "4164525", "vector": [], "sparse_vector": [], "title": "Neighborhood-Preserving Hashing for Large-Scale Cross-Modal Search.", "authors": ["<PERSON><PERSON><PERSON>", "Yizhou Wang"], "summary": "In the literature of cross-modal search, most methods employ linear models to pursue hash codes that preserve data similarity, in terms of Euclidean distance, both within-modal and across-modal. However, data dimensionality can be quite different across modalities. It is known that the behavior of Euclidean distance/similarity between datapoints can be drastically different in linear spaces of different dimensionality. In this paper, we identify this \"variation of dimensionality\" problem in cross-modal search that may harm most of distance-based methods. We propose a semi-supervised nonlinear probabilistic cross-modal hashing method, namely Neighborhood-Preserving Hashing (NPH), to alleviate the negative effect due to the variation of dimensionality issue. Inspired by tSNE \\cite{tSNE_van2008visualizing}, rather than preserve pairwise data distances, we propose to learn hash codes that preserve neighborhood relationship of datapoints via matching their conditional distribution derived from distance to that of datapoints of multi-modalities. Experimental results on three real-world datasets demonstrate that the proposed method outperforms the state-of-the-art distance-based semi-supervised cross-modal hashing methods as well as many fully-supervised ones.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967241"}, {"primary_key": "4164526", "vector": [], "sparse_vector": [], "title": "Facial Expression Recognition with Deep two-view Support Vector Machine.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper proposes a novel deep two-view approach to learn features from both visible and thermal images and leverage the commonality among visible and thermal images for facial expression recognition from visible images. The thermal images are used as privileged information, which is required only during training to help visible images learn better features and classifier. Specifically, we first learn a deep model for visible images and thermal images respectively, and use the learned feature representations to train SVM classifiers for expression classification. We then jointly refine the deep models as well as the SVM classifiers for both thermal images and visible images by imposing the constraint that the outputs of the SVM classifiers from two views are similar. Therefore, the resulting representations and classifiers capture the inherent connections among visible facial image, infrared facial image and target expression labels, and hence improve the recognition performance for facial expression recognition from visible images during testing. Experimental results on the benchmark expression database demonstrate the effectiveness of our proposed method.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967295"}, {"primary_key": "4164527", "vector": [], "sparse_vector": [], "title": "A Compact Binary Aggregated Descriptor via Dual Selection for Visual Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Junsong Yuan", "<PERSON><PERSON><PERSON>"], "summary": "To achieve high retrieval accuracy over a large scale image/video dataset, recent research efforts have demonstrated that employing extremely high-dimensional descriptors such as the Fisher Vector (FV) and the Vector of Locally Aggregated Descriptors (VLAD) can yield good performance. To enable fast search, the FV (or VLAD) is usually compressed by product quantization (PQ) or hashing. However, compressing high-dimensional descriptors via PQ or hashing may become intractable and infeasible due to both the storage and computation requirements for the linear/nonlinear projection of PQ or hashing methods. We develop a novel compact aggregated descriptor via dual selection for visual search. We utilize both sample-specific Gaussian component redundancy and bit dependency within a binary aggregated descriptor to produce its compact binary codes. The proposed method can effectively reduce the codesize of the raw aggregated descriptors, without degrading the search accuracy or introducing additional memory footprint. We demonstrate the significant advantages of the proposed binary codes in solving the approximate nearest neighbor (ANN) visual search problem. Experimental results on extensive datasets show that our method outperforms the state-of-the-art methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967256"}, {"primary_key": "4164528", "vector": [], "sparse_vector": [], "title": "A Live Face Swapper.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shuicheng Yan"], "summary": "In this technical demonstration, we propose a face swapping framework, which is able to interactively change the appearance of a face in the wild to a different person/creature's face in real time on a mobile device. To realize this objective, we develop a deep learning-based face detector which is able to accurately detect faces in the wild. Our face feature points tracking system based on progressive initialization ensures accurate and robust localization of facial landmarks under extreme poses and expressions in real time. Relying on the advances of our face detector and face feature points tracker, we construct the Face Swapper which can smoothly replace the face appearance of a user in real time.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973808"}, {"primary_key": "4164529", "vector": [], "sparse_vector": [], "title": "DASH2M: Exploring HTTP/2 for Internet Streaming to Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Song<PERSON> Chen"], "summary": "Today HTTP/1.1 is the most popular vehicle for delivering Internet content, including streaming video. Standardized in 2015 with a few new features, HTTP/2 is gradually replacing HTTP 1.1 to improve user experience. Yet, how HTTP/2 can help improve the video streaming delivery has not been thoroughly investigated. In this work, we set to investigate how to utilize the new features offered by HTTP/2 for video streaming over the Internet, focusing on the streaming delivery to mobile devices as, today, more and more users watch video on their mobile devices. For this purpose, we design DASH2M, Dynamic Adaptive Streaming over HTTP/2 to Mobile Devices. DASH2M deliberately schedules the streaming content delivery by comprehensively considering the user's Quality of Experience (QoE), the dynamics of the network resources, and the power efficiency on the mobile devices. Experiments based on an implemented prototype show that DASH2M can outperform prior strategies for users' QoE while minimizing the battery power consumption on mobile devices.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964313"}, {"primary_key": "4164531", "vector": [], "sparse_vector": [], "title": "Looking Good With Flickr Faves: Gaussian Processes for Finding Difference Makers in Personality Impressions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Flickr allows its users to generate galleries of \"faves\", i.e., pictures that they have tagged as favourite. According to recent studies, the faves are predictive of the personality traits that people attribute to Flickr users. This article investigates the phenomenon and shows that faves allow one to predict whether a Flickr user is perceived to be above median or not with respect to each of the Big-Five Traits (accuracy up to 79\\% depending on the trait). The classifier - based on Gaussian Processes with a new kernel designed for this work - allows one to identify the visual characteristics of faves that better account for the prediction outcome.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967253"}, {"primary_key": "4164532", "vector": [], "sparse_vector": [], "title": "Dictionary Learning Based Hashing for Cross-Modal Retrieval.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Recent years have witnessed the growing popularity of cross-modal hashing for fast multi-modal data retrieval. Most existing cross-modal hashing methods project heterogeneous data directly into a common space with linear projection matrices. However, such scheme will lead to large error as there will probably be some heterogeneous data with semantic similarity hard to be close in latent space when linear projection is used. In this paper, we propose a dictionary learning cross-modal hashing (DLCMH) to perform cross-modal similarity search. Instead of projecting data directly, DLCMH learns dictionaries and generates sparse representation for each instance, which is more suitable to be projected to latent space. Then, it assumes that all modalities of one instance have identical hash codes, and gets final binary codes by minimizing quantization error. Experimental results on two real-world datasets show that DLCMH outperforms or is comparable to several state-of-the-art hashing models.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967206"}, {"primary_key": "4164533", "vector": [], "sparse_vector": [], "title": "Academic Coupled Dictionary Learning for Sketch-based Image Retrieval.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the last few years, the query-by-visual-example paradigm gained popularity, specially for content based retrieval systems. As sketches represent a natural way of expressing a synthetic query, recent research efforts focused on developing algorithmic solutions to address the sketch-based image retrieval (SBIR) problem. Within this context, we propose a novel approach for SBIR that, unlike previous methods, is able to exploit the visual complexity inherently present in sketches and images. We introduce academic learning, a paradigm in which the sample learning order is constructed both from the data, as in self-paced learning, and from partial curricula. We propose an instantiation of this paradigm within the framework of coupled dictionary learning to address the SBIR task. We also present an efficient algorithm to learn the dictionaries and the codes, and to pace the learning combining the reconstruction error, the prior knowledge suggested by the partial curricula and the cross-domain code coherence. In order to evaluate the proposed approach, we report an extensive experimental validation showing that the proposed method outperforms the state-of-the-art in coupled dictionary learning and in SBIR on three different publicly available datasets.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964329"}, {"primary_key": "4164534", "vector": [], "sparse_vector": [], "title": "Cross-modal Retrieval with Label Completion.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cross-modal retrieval has been attracting increasing attention because of the explosion of multi-modal data, e.g., texts and images. Most supervised cross-modal retrieval methods learn discriminant common subspaces minimizing the heterogeneity of different modalities by exploiting the label information. However, these methods neglect the fact that, in practice, the given labels of training data might be incomplete (i.e., some of their labels are missing). The low-quality labels result in less effective subspace and consequent unsatisfactory retrieval performance. To tackle this, we propose a novel model that simultaneously performs label completion and cross-modal retrieval. Specifically, we assume the to-be-learned common subspace can be jointly derived through two aspects: 1) linear projection from modality-specific features and 2) enriching mapping from the incomplete labels. We thus formulate the subspace learning problem as a co-regularized learning framework based on multi-modal features and incomplete labels. Extensive experiments on two large-scale multi-modal datasets demonstrate the superiority of our model for both label completion and cross-modal retrieval over the state-of-the-arts.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967231"}, {"primary_key": "4164535", "vector": [], "sparse_vector": [], "title": "Parsimonious Mixed-Effects HodgeRank for Crowdsourced Preference Aggregation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In crowdsourced preference aggregation, it is often assumed that all the annotators are subject to a common preference or utility function which generates their comparison behaviors in experiments. However, in reality annotators are subject to variations due to multi-criteria, abnormal, or a mixture of such behaviors. In this paper, we propose a parsimonious mixed-effects model based on <PERSON>Rank, which takes into account both the fixed effect that the majority of annotators follows a common linear utility model, and the random effect that a small subset of annotators might deviate from the common significantly and exhibits strongly personalized preferences. HodgeRank has been successfully applied to subjective quality evaluation of multimedia and resolves pairwise crowdsourced ranking data into a global consensus ranking and cyclic conflicts of interests. As an extension, our proposed methodology further explores the conflicts of interests through the random effect in annotator specific variations. The key algorithm in this paper establishes a dynamic path from the common utility to individual variations, with different levels of parsimony or sparsity on personalization, based on newly developed Linearized Bregman Algorithms with Inverse Scale Space method. Finally the validity of the methodology are supported by experiments with both simulated examples and three real-world crowdsourcing datasets, which shows that our proposed method exhibits better performance (i.e. smaller test error) compared with HodgeRank due to its parsimonious property.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964298"}, {"primary_key": "4164536", "vector": [], "sparse_vector": [], "title": "Video Generation Using 3D Convolutional Neural Network.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, content generation using neural network has been widely studied. Motivated by this recent progress, we studied the generation of videos using only a label as input. In our method, we iteratively minimize two objective functions at the same time : an objective function to evaluate how close the video is to the target class and another to evaluate how natural-appearing the video is. Our proposed method uses the cross-entropy error between the target label and the output of 3D convolutional neural network (C3D) as the objective function for evaluating how close the video is to the target class and uses the Euclidean distance between the input video and the video decoded from our temporal convolutional auto-encoder (\"tempCAE\") as the objective function for evaluating how natural-appearing the video is. We conducted an experiment evaluating the generated videos using a crowdsourcing service and confirmed the utility of our method.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967287"}, {"primary_key": "4164537", "vector": [], "sparse_vector": [], "title": "CNN vs. SIFT for Image Retrieval: Alternative or Complementary?", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the past decade, SIFT is widely used in most vision tasks such as image retrieval. While in recent several years, deep convolutional neural networks (CNN) features achieve the state-of-the-art performance in several tasks such as image classification and object detection. Thus a natural question arises: for the image retrieval task, can CNN features substitute for SIFT? In this paper, we experimentally demonstrate that the two kinds of features are highly complementary. Following this fact, we propose an image representation model, complementary CNN and SIFT (CCS), to fuse CNN and SIFT in a multi-level and complementary way. In particular, it can be used to simultaneously describe scene-level, object-level and point-level contents in images. Extensive experiments are conducted on four image retrieval benchmarks, and the experimental results show that our CCS achieves state-of-the-art retrieval results.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967252"}, {"primary_key": "4164538", "vector": [], "sparse_vector": [], "title": "Efficient Mobile Implementation of A CNN-based Object Recognition System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Because of the recent progress on deep learning studies, Convolutional Neural Network (CNN) based method have outperformed conventional object recognition methods with a large margin. However, it requires much more memory and computational costs compared to the conventional methods. Therefore, it is not easy to implement a CNN-based object recognition system on a mobile device where memory and computational power are limited. In this paper, we examine CNN architectures which are suitable for mobile implementation, and propose multi-scale network-in-networks (NIN) in which users can adjust the trade-off between recognition time and accuracy. We implemented multi-threaded mobile applications on both iOS and Android employing either NEON SIMD instructions or the BLAS library for fast computation of convolutional layers, and compared them in terms of recognition time on mobile devices. As results, it has been revealed that BLAS is better for iOS, while NEON is better for Android, and that reducing the size of an input image by resizing is very effective for speedup of CNN-based recognition.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967243"}, {"primary_key": "4164540", "vector": [], "sparse_vector": [], "title": "Synthesizing Emerging Images from Photographs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Emergence is the visual phenomenon by which humans recognize the objects in a seemingly noisy image through aggregating information from meaningless pieces and perceiving a whole that is meaningful. Such an unique mental skill renders emergence an effective scheme to tell humans and machines apart. Images that are detectable by human but difficult for an automatic algorithm to recognize are also referred as emerging images. A recent state-of-the-art work proposes to synthesize images of 3D objects that are detectable by human but difficult for an automatic algorithm to recognize. Their results are further verified to be easy for humans to recognize while posing a hard time for automatic machines. However, using 3D objects as inputs prevents their system from being practical and scalable for generating an infinite number of high quality images. For instance, the image quality may degrade quickly as the viewing and lighting conditions changing in 3D domain, and the available resources of 3D models are usually limited. However, using 3D objects as inputs brings drawbacks. For instance, the quality of results is sensitive to the viewing and lighting conditions in the 3D domain. The available resources of 3D models are usually limited, and thus restricts the scalability. This paper presents a novel synthesis technique to automatically generate emerging images from regular photographs, which are commonly taken with decent setting and widely accessible online. We adapt the previous system to the 2D setting of input photographs and develop a set of image-based operations. Our algorithm is also designed to support the difficulty level control of resultant images through a limited set of parameters. We conducted several experiments to validate the efficacy and efficiency of our system.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967304"}, {"primary_key": "4164541", "vector": [], "sparse_vector": [], "title": "Cross-batch Reference Learning for Deep Classification and Retrieval.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Learning feature representations for image retrieval is essential to multimedia search and mining applications. Recently, deep convolutional networks (CNNs) have gained much attention due to their impressive performance on object detection and image classification, and the feature representations learned from a large-scale generic dataset (e.g., ImageNet) can be transferred to or fine-tuned on the datasets of other domains. However, when the feature representations learned with a deep CNN are applied to image retrieval, the performance is still not as good as they are used for classification, which restricts their applicability to relevant image search. To ensure the retrieval capability of the learned feature space, we introduce a new idea called cross-batch reference (CBR) to enhance the stochastic-gradient-descent (SGD) training of CNNs. In each iteration of our training process, the network adjustment relies not only on the training samples in a single batch, but also on the information passed by the samples in the other batches. This inter-batches communication mechanism is formulated as a cross-batch retrieval process based on the mean average precision (MAP) criterion, where the relevant and irrelevant samples are encouraged to be placed on top and rear of the retrieval list, respectively. The learned feature space is not only discriminative to different classes, but the samples that are relevant to each other or of the same class are also enforced to be centralized. To maximize the cross-batch MAP, we design a loss function that is an approximated lower bound of the MAP on the feature layer of the network, which is differentiable and easier for optimization. By combining the intra-batch classification and inter-batch cross-reference losses, the learned features are effective for both classification and retrieval tasks. Experimental results on various benchmarks demonstrate the effectiveness of our approach.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964324"}, {"primary_key": "4164542", "vector": [], "sparse_vector": [], "title": "Multilayer and Multimodal Fusion of Deep Neural Networks for Video Classification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents a novel framework to combine multiple layers and modalities of deep neural networks for video classification. We first propose a multilayer strategy to simultaneously capture a variety of levels of abstraction and invariance in a network, where the convolutional and fully connected layers are effectively represented by our proposed feature aggregation methods. We further introduce a multimodal scheme that includes four highly complementary modalities to extract diverse static and dynamic cues at multiple temporal scales. In particular, for modeling the long-term temporal information, we propose a new structure, FC-RNN, to effectively transform pre-trained fully connected layers into recurrent layers. A robust boosting model is then introduced to optimize the fusion of multiple layers and modalities in a unified way. In the extensive experiments, we achieve state-of-the-art results on two public benchmark datasets: UCF101 and HMDB51.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964297"}, {"primary_key": "4164543", "vector": [], "sparse_vector": [], "title": "SceneTextReg: A Real-Time Video OCR System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We showcase a system for real-time video text recognition. The system is based on the standard workflow of text spotting system, which includes text detection and word recognition procedures. We apply deep neural networks in both procedures. In text localization stage, textual candidates are roughly captured by using a Maximally Stable Extremal Regions (MSERs) detector with high recall rate, false alarms are then eliminated by using Convolutional Neural Network (CNN ) verifier. For word recognition, we developed a skeleton based method for segmenting text region from its background, then a CNN based word recognizer is utilized for recognizing texts. Our current implementation demonstrates a real time performance for recognizing scene text by using a standard laptop with webcam. The word recognizer achieves competitive result to state-of-the-art methods by only using synthetical training data.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973811"}, {"primary_key": "4164544", "vector": [], "sparse_vector": [], "title": "Abnormal Event Discovery in User Generated Photos.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xu"], "summary": "Vision based event analysis plays a very critical role in automatically organizing user generated photos. As one of the important tasks in event analysis, abnormal event discovery still does not obtain much attentions. It is difficult because only few samples can be used for event pattern learning. In this paper, by considering the photo taken time, we propose a novel one-class structured event modeling (OSEM) where we explore the temporal event patterns in negative photos of the event using the continuous conditional random field (CRF). With the estimated piecewise training of CRF, the proposed OSEM can be efficiently solved using stochastic gradients descent (SGD) in an end-to-end form. The extensive experimental results on a collected abnormal event dataset demonstrate the effectiveness of the proposed OSEM.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2975215"}, {"primary_key": "4164545", "vector": [], "sparse_vector": [], "title": "A Domain Robust Approach For Image Dataset Construction.", "authors": ["Yazhou Yao", "Xian<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There have been increasing research interests in automatically constructing image dataset by collecting images from the Internet. However, existing methods tend to have a weak domain adaptation ability, known as the \"dataset bias problem\". To address this issue, in this work, we propose a novel image dataset construction framework which can generalize well to unseen target domains. In specific, the given queries are first expanded by searching in the Google Books Ngrams Corpora (GBNC) to obtain a richer semantic description, from which the noisy query expansions are then filtered out. By treating each expansion as a \"bag\" and the retrieved images therein as \"instances\", we formulate image filtering as a multi-instance learning (MIL) problem with constrained positive bags. By this approach, images from different data distributions will be kept while with noisy images filtered out. Comprehensive experiments on two challenging tasks demonstrate the effectiveness of our proposed approach.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967213"}, {"primary_key": "4164546", "vector": [], "sparse_vector": [], "title": "Face Recognition via Active Annotation and Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Xiangyang Xue"], "summary": "In this paper, we introduce an active annotation and learning framework for the face recognition task. Starting with an initial label deficient face image training set, we iteratively train a deep neural network and use this model to choose the examples for further manual annotation. We follow the active learning strategy and derive the Value of Information criterion to actively select candidate annotation images. During these iterations, the deep neural network is incrementally updated. Experimental results conducted on LFW benchmark and MS-Celeb-1M challenge demonstrate the effectiveness of our proposed framework.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2984059"}, {"primary_key": "4164547", "vector": [], "sparse_vector": [], "title": "LightNet: A Versatile, Standalone Matlab-based Environment for Deep Learning.", "authors": ["Chengxi Ye", "<PERSON>", "Yezhou Yang", "Cornelia <PERSON>ü<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "LightNet is a lightweight, versatile, purely Matlab-based deep learning framework. The idea underlying its design is to provide an easy-to-understand, easy-to-use and efficient computational platform for deep learning research. The implemented framework supports major deep learning architectures such as Multilayer Perceptron Networks (MLP), Convolutional Neural Networks (CNN) and Recurrent Neural Networks (RNN). The framework also supports both CPU and GPU computation, and the switch between them is straightforward. Different applications in computer vision, natural language processing and robotics are demonstrated as experiments.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973791"}, {"primary_key": "4164548", "vector": [], "sparse_vector": [], "title": "A Novel Shadow-Free Feature Extractor for Real-Time Road Detection.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Ge Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Road detection is one of the most important research areas in driver assistance and automated driving field. However, the performance of existing methods is still unsatisfactory, especially in severe shadow conditions. To overcome those difficulties, first we propose a novel shadow-free feature extractor based on the color distribution of road surface pixels. Then we present a road detection framework based on the extractor, whose performance is more accurate and robust than that of existing extractors. Also, the proposed framework has much low-complexity, which is suitable for usage in practical systems.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967294"}, {"primary_key": "4164550", "vector": [], "sparse_vector": [], "title": "Robust Visual-Textual Sentiment Analysis: When Attention meets Tree-structured Recursive Neural Networks.", "authors": ["Quanzeng You", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Jin", "<PERSON><PERSON><PERSON>"], "summary": "Sentiment analysis is crucial for extracting social signals from social media content. Due to huge variation in social media, the performance of sentiment classifiers using single modality (visual or textual) still lags behind satisfaction. In this paper, we propose a new framework that integrates textual and visual information for robust sentiment analysis. Different from previous work, we believe visual and textual information should be treated jointly in a structural fashion. Our system first builds a semantic tree structure based on sentence parsing, aimed at aligning textual words and image regions for accurate analysis. Next, our system learns a robust joint visual-textual semantic representation by incorporating 1) an attention mechanism with LSTM (long short term memory) and 2) an auxiliary semantic learning task. Extensive experimental results on several known data sets show that our method outperforms existing the state-of-the-art joint models in sentiment analysis. We also investigate different tree-structured LSTM (T-LSTM) variants and analyze the effect of the attention mechanism in order to provide deeper insight on how the attention mechanism helps the learning of the joint visual-textual sentiment classifier.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964288"}, {"primary_key": "4164551", "vector": [], "sparse_vector": [], "title": "UnitBox: An Advanced Object Detection Network.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In present object detection systems, the deep convolutional neural networks (CNNs) are utilized to predict bounding boxes of object candidates, and have gained performance advantages over the traditional region proposal methods. However, existing deep CNN methods assume the object bounds to be four independent variables, which could be regressed by the $\\ell_2$ loss separately. Such an oversimplified assumption is contrary to the well-received observation, that those variables are correlated, resulting to less accurate localization. To address the issue, we firstly introduce a novel Intersection over Union ($IoU$) loss function for bounding box prediction, which regresses the four bounds of a predicted box as a whole unit. By taking the advantages of $IoU$ loss and deep fully convolutional networks, the UnitBox is introduced, which performs accurate and efficient localization, shows robust to objects of varied shapes and scales, and converges fast. We apply UnitBox on face detection task and achieve the best performance among all published methods on the FDDB benchmark.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967274"}, {"primary_key": "4164552", "vector": [], "sparse_vector": [], "title": "HEVC-compliant Tile-based Streaming of Panoramic Video for Virtual Reality Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Delivering wide-angle and high-resolution spherical panoramic video content entails a high streaming bitrate. This imposes challenges when panorama clips are consumed in virtual reality (VR) head-mounted displays (HMD). The reason is that the HMDs typically require high spatial and temporal fidelity contents and strict low-latency in order to guarantee the user's sense of presence while using them. In order to alleviate the problem, we propose to store two versions of the same video content at different resolutions, each divided into multiple tiles using the High Efficiency Video Coding (HEVC) standard. According to the user's present viewport, a set of tiles is transmitted in the highest captured resolution, while the remaining parts are transmitted from the low-resolution version of the same content. In order to enable randomly choosing different combinations, the tile sets are encoded to be independently decodable. We further study the trade-off in the choice of tiling scheme and its impact on compression and streaming bitrate performances. The results indicate streaming bitrate saving from 30% to 40%, depending on the selected tiling scheme, when compared to streaming the entire video content.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967292"}, {"primary_key": "4164553", "vector": [], "sparse_vector": [], "title": "Multi-pose Facial Expression Recognition Using Transformed Dirichlet Process.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Driven by recent advances in human-centered computing, Facial Expression Recognition (FER) has attracted significant attention in many applications. In this paper, we propose a novel graphical model, multi-level Transformed Dirichlet Process (ml-TDP), for multi-pose FER. In our approach, pose is explicitly introduced into ml-TDP so that separate training and parameter tuning for each pose is not required. In addition, ml-TDP can learn an intermediate facial expression representation subject to geometric constraints. By sharing the pool of spatially-coherent features over expressions and poses, we provide a scalable solution for multi-pose FER. Extensive experimental result on benchmark facial expression databases shows the superior performance of ml-TDP.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967240"}, {"primary_key": "4164554", "vector": [], "sparse_vector": [], "title": "PL-ranking: A Novel Ranking Method for Cross-Modal Retrieval.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON> Ma", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper proposes a novel method for cross-modal retrieval named Pairwise-Listwise \\textbf{ranking} (PL-ranking) based on the low-rank optimization framework. Motivated by the fact that optimizing the top of ranking is more applicable in practice, we focus on improving the precision at the top of ranked list for a given sample and learning a low-dimensional common subspace for multi-modal data. Concretely, there are three constraints in PL-ranking. First, we use a pairwise ranking loss constraint to optimize the top of ranking. Then, considering that the pairwise ranking loss constraint ignores class information, we further adopt a listwise constraint to minimize the intra-neighbors variance and maximize the inter-neighbors separability. By this way, class information is preserved while the number of iterations is reduced. Finally, low-rank based regularization is applied to exploit the correlations between features and labels so that the relevance between the different modalities can be enhanced after mapping them into the common subspace. We design an efficient low-rank stochastic subgradient descent method to solve the proposed optimization problem. The experimental results show that the average MAP scores of PL-ranking are improved 5.1%, 9.2%, 4.7% and 4.8% than those of the state-of-the-art methods on the Wiki, Flickr, <PERSON> and NUS-WIDE datasets, respectively.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964336"}, {"primary_key": "4164555", "vector": [], "sparse_vector": [], "title": "Shorter-is-Better: Venue Category Estimation from Micro-Video.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "According to our statistics on over 2 million micro-videos, only 1.22% of them are associated with venue information, which greatly hinders the location-oriented applications and personalized services. To alleviate this problem, we aim to label the bite-sized video clips with venue categories. It is, however, nontrivial due to three reasons: 1) no available benchmark dataset; 2) insufficient information, low quality, and 3) information loss; and 3) complex relatedness among venue categories. Towards this end, we propose a scheme comprising of two components. In particular, we first crawl a representative set of micro-videos from Vine and extract a rich set of features from textual, visual and acoustic modalities. We then, in the second component, build a tree-guided multi-task multi-modal learning model to estimate the venue category for each unseen micro-video. This model is able to jointly learn a common space from multi-modalities and leverage the predefined Foursquare hierarchical structure to regularize the relatedness among venue categories. Extensive experiments have well-validated our model. As a side research contribution, we have released our data, codes and involved parameters.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964307"}, {"primary_key": "4164556", "vector": [], "sparse_vector": [], "title": "From Seed Discovery to Deep Reconstruction: Predicting <PERSON><PERSON><PERSON> in Crowd via Deep Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although saliency prediction in crowd has been recently recognized as an essential task for video analysis, it is not comprehensively explored yet. The challenges lie in that eye fixations in crowded scenes are inherently \"distinct\" and \"multi-modal\", which differs from those in regular scenes. To this end, the existing saliency prediction schemes typically rely on hand designed features with shallow learning paradigm, which neglect the underlying characteristics of crowded scenes. In this paper, we propose a saliency prediction model dedicated for crowd videos with two novelties: 1) Distinct units are discovered using deep representation learned by a Stacked Denoising Auto-Encoder (SDAE), considering perceptual properties of crowd saliency; 2) Contrast-based saliency is measured through deep reconstruction errors in the second SDAE trained on all units excluding distinct units. A unified model is integrated for online processing crowd saliency. Extensive evaluations on two crowd video benchmark datasets demonstrate that our approach can effectively explore crowd saliency mechanism in two-stage SDAEs and achieve significantly better results than state-of-the-art methods, with robustness to parameters.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967185"}, {"primary_key": "4164557", "vector": [], "sparse_vector": [], "title": "Play and Rewind: Optimizing Binary Representations of Videos by Self-Supervised Temporal Hashing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We focus on hashing videos into short binary codes for efficient Content-based Video Retrieval (CBVR), which is a fundamental technique that supports access to the ever-growing abundance of videos on the Web. Existing video hash functions are built on three isolated stages: frame pooling, relaxed learning, and binarization, which have not adequately explored the temporal order of video frames in a joint binary optimization model, resulting in severe information loss. In this paper, we propose a novel unsupervised video hashing framework called Self-Supervised Temporal Hashing (SSTH) that is able to capture the temporal nature of videos in an end-to-end learning-to-hash fashion. Specifically, the hash function of SSTH is an encoder RNN equipped with the proposed Binary LSTM (BLSTM) that generates binary codes for videos. The hash function is learned in a self-supervised fashion, where a decoder <PERSON><PERSON><PERSON> is proposed to reconstruct the original video frames in both forward and reverse orders. For binary code optimization, we develop a backpropagation rule that tackles the non-differentiability of BLSTM. This rule allows efficient deep network training without suffering from the binarization loss. Through extensive CBVR experiments on two real-world consumer video datasets of Youtube and Flickr, we show that SSTH consistently outperforms state-of-the-art video hashing methods, eg., in terms of [email protected], SSTH using only 128 bits can still outperform others using 256 bits by at least 9% to 15% on both datasets.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964308"}, {"primary_key": "4164558", "vector": [], "sparse_vector": [], "title": "On Estimating Air Pollution from Photos Using Convolutional Neural Network.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Changsheng Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Air pollution has raised people's intensive concerns especially in developing countries such as China and India. Different from using expensive or unreliable methods like sensor-based or social network based one, photo based air pollution estimation is a promising direction, while little work has been done up to now. Focusing on this immediate problem, this paper devises an effective convolutional neural network to estimate air's quality based on photos. Our method is comprised of two ingredients: first a negative log-log ordinal classifier is devised in the last layer of the network, which can improve the ordinal discriminative ability of the model. Second, as a variant of the Rectified Linear Units (ReLU), a modified activation function is developed for photo based air pollution estimation. This function has been shown it can alleviate the vanishing gradient issue effectively. We collect a set of outdoor photos and associate the pollution levels from official agency as the ground truth. Empirical experiments are conducted on this real-world dataset which shows the capability of our method.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967230"}, {"primary_key": "4164559", "vector": [], "sparse_vector": [], "title": "Image Emotion Computing.", "authors": ["Sicheng Zhao"], "summary": "Images can convey rich semantics and induce strong emotions in viewers. My research aims to predict image emotions from different aspects with respect to two main challenges: affective gap and subjective evaluation. To bridge the affective gap, we extract emotion features based on principles-of-art to recognize image-centric dominant emotions. As the emotions that are induced in viewers by an image are highly subjective and different, we propose to predict user-centric personalized emotion perceptions for each viewer and image-centric emotion probability distribution for each image. To tackle the subjective evaluation issue, we set up a large scale image emotion dataset from Flickr, named Image-Emotion-Social-Net, on both dimensional and categorical emotion representations with over 1 million images and about 8,000 users. Different types of factors may influence personalized image emotion perceptions, including visual content, social context, temporal evolution and location influence. We make an initial attempt to jointly combine them by the proposed rolling multi-task hypergraph learning. Both discrete and continuous emotion distributions are modelled via shared sparse learning. Further, several potential applications based on image emotions are designed and implemented.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2971473"}, {"primary_key": "4164560", "vector": [], "sparse_vector": [], "title": "Partial Multi-Modal Sparse Coding via Adaptive Similarity Structure Regularization.", "authors": ["<PERSON>", "Hanqing Lu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yu<PERSON>"], "summary": "Multi-modal sparse coding has played an important role in many multimedia applications, where data are usually with multiple modalities. Recently, various multi-modal sparse coding approaches have been proposed to learn sparse codes of multi-modal data, which assume that data appear in all modalities, or at least there is one modality containing all data. However, in real applications, it is often the case that some modalities of the data may suffer from missing information and thus result in partial multi-modality data. In this paper, we propose to solve the partial multi-modal sparse coding problem via multi-modal similarity structure regularization. Specifically, we propose a partial multi-modal sparse coding framework termed Adaptive Partial Multi-Modal Similarity Structure Regularization for Sparse Coding (AdaPM2SC), which preserves the similarity structure within the same modality and between different modalities. Experimental results conducted on two real-world datasets demonstrate that AdaPM2SC significantly outperforms the state-of-the-art methods under partial multi-modality scenario.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967201"}, {"primary_key": "4164561", "vector": [], "sparse_vector": [], "title": "LSOD: Local Sparse Orthogonal Descriptor for Image Matching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hongtao Lu"], "summary": "We propose a novel method for feature description used for image matching in this paper. Our method is inspired by the autoencoder, an artificial neural network designed for learning efficient codings. Sparse and orthogonal constraints are imposed on the autoencoder and make it a highly discriminative descriptor. It is shown that the proposed descriptor is not only invariant to geometric and photometric transformations (such as viewpoint change, intensity change, noise, image blur and JPEG compression), but also highly efficient. We compare it with existing state-of-the-art descriptors on standard benchmark datasets, the experimental results show that our LSOD method yields better performance both in accuracy and efficiency.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967217"}, {"primary_key": "4164562", "vector": [], "sparse_vector": [], "title": "Predicting Personalized Emotion Perceptions of Social Images.", "authors": ["Sicheng Zhao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Rongrong Ji", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Images can convey rich semantics and induce various emotions to viewers. Most existing works on affective image analysis focused on predicting the dominant emotions for the majority of viewers. However, such dominant emotion is often insufficient in real-world applications, as the emotions that are induced by an image are highly subjective and different with respect to different viewers. In this paper, we propose to predict the personalized emotion perceptions of images for each individual viewer. Different types of factors that may affect personalized image emotion perceptions, including visual content, social context, temporal evolution, and location influence, are jointly investigated. Rolling multi-task hypergraph learning is presented to consistently combine these factors and a learning algorithm is designed for automatic optimization. For evaluation, we set up a large scale image emotion dataset from Flickr, named Image-Emotion-Social-Net, on both dimensional and categorical emotion representations with over 1 million images and about 8,000 users. Experiments conducted on this dataset demonstrate that the proposed method can achieve significant performance gains on personalized emotion classification, as compared to several state-of-the-art approaches.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964289"}, {"primary_key": "4164563", "vector": [], "sparse_vector": [], "title": "SocialFX: Studying a Crowdsourced Folksonomy of Audio Effects Terms.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We present the analysis of crowdsourced studies into how a population of Amazon Mechanical Turk Workers describe three commonly used audio effects: equalization, reverberation, and dynamic range compression. We find three categories of words used to describe audio: ones that are generally used across effects, ones that tend towards a single effect, and ones that are exclusive to a single effect. We present select examples from these categories. We visualize and present an analysis of the shared descriptor space between audio effects. Data on the strength of association between words and effects is made available online for a set of 4297 words drawn from 1233 unique users for three effects (equalization, reverberation, compression). This dataset is an important step towards implementing of an end-to-end language-based audio production system, in which a user describes a creative goal, as they would to a professional audio engineer, and the system picks which audio effect to apply, as well as the setting of the audio effect.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967207"}, {"primary_key": "4164564", "vector": [], "sparse_vector": [], "title": "Context-aware Geometric Object Reconstruction for Mobile Education.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The solid geometric objects in the educational geometric books are usually illustrated as 2D line drawings accompanied with description text. In this paper, we present a method to recover the geometric objects from 2D to 3D. Unlike the previous methods, we not only use the geometric information from the line drawing itself, but also the textual information extracted from its context. The essential of our method is a cost function to mix the two types of information, and we optimize the cost function to identify the geometric object and recover its 3D information. Our method can recover various types of solid geometric objects including straight-edge manifolds and curved objects such as cone, cylinder and sphere. We show that our method performs significantly better compared to the previous ones.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967244"}, {"primary_key": "4164565", "vector": [], "sparse_vector": [], "title": "Joint Image and Text Representation for Aesthetics Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Image aesthetics assessment is essential to multimedia applications such as image retrieval, and personalized image search and recommendation. Primarily relying on visual information and manually-supplied ratings, previous studies in this area have not adequately utilized higher-level semantic information. We incorporate additional textual phrases from user comments to jointly represent image aesthetics utilizing multimodal Deep Boltzmann Machine. Given an image, without requiring any associated user comments, the proposed algorithm automatically infers the joint representation and predicts the aesthetics category of the image. We construct the AVA-Comments dataset to systematically evaluate the performance of the proposed algorithm. Experimental results indicate that the proposed joint representation improves the performance of aesthetics assessment on the benchmarking AVA dataset, comparing with only visual features.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967223"}, {"primary_key": "4164566", "vector": [], "sparse_vector": [], "title": "Interactive Image Search for Clothing Recommendation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou", "Li<PERSON> Zhang"], "summary": "This demo delivers a novel retrieval system which meets users' multi-dimensional requirements in clothing image search. In this system, users are able to use both image and keywords as query inputs. We employ the color, texture, shape and attributes as additional descriptors to further refine the requirements. We propose the Hybrid Topic (HT) model, a probabilistic network integrating the multi-channel descriptors into a unified framework, to learn the intricate semantic representation of the descriptors above. The proposed model provides an effective multi-modal representation of clothes. Our experiments show that the HT method significantly outperforms the CNN-based deep search methods.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2973834"}, {"primary_key": "4164567", "vector": [], "sparse_vector": [], "title": "Demand-adaptive Clothing Image Retrieval Using Hybrid Topic Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Zhou", "Li<PERSON> Zhang"], "summary": "This paper proposes a novel approach to meet users' multi-dimensional requirements in clothing image retrieval. It enables users to add search conditions by modifying the color, texture, shape and attribute descriptors of the query images to further refine their requirements. We propose the Hybrid Topic (HT) model to learn the intricate semantic representation of the descriptors above. The model provides an effective multi-dimensional representation of clothes and is able to perform automatic image annotation by probabilistic reasoning from image search. Furthermore, we develop a demand-adaptive retrieval strategy which refines users' specific requirements and removes users' unwanted features. Our experiments show that the HT method significantly outperforms the deep neural network methods. The accuracy could be further improved in cooperation with image annotation and demand-adaptive retrieval strategy.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2967270"}, {"primary_key": "4164568", "vector": [], "sparse_vector": [], "title": "QoE Prediction for Enriched Assessment of Individual Video Viewing Experience.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Most automatic Quality of Experience (QoE) assessment models have so far aimed at predicting the QoE of a video as experienced by an average user, and solely based on perceptual characteristics of the video being viewed. The importance of other characteristics, such as those related to the video content being watched, or those related to an individual user have been largely neglected. This is suboptimal in view of the fact that video viewing experience is individual and multifaceted, considering the perceived quality (related to coding or network-induced artifacts), but also other -- more hedonic - aspects, like enjoyment. In this paper, we propose an expanded model which aims to assess QoE of a given video, not only in terms of perceived quality but also of enjoyment, as experienced by a specific user. To do so, we feed the model not only with information extracted from the video (related to both perceived quality and content), but also with individual user characteristics, such as interest, personality and gender. We assess our expanded QoE model based on two publicly available QoE datasets, namely i_QoE and CP-QAE-I. The results show that combining various types of characteristics enables better QoE prediction performance as compared to only considering perceptual characteristics of the video, both when targeting perceived quality and enjoyment.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964330"}, {"primary_key": "4164569", "vector": [], "sparse_vector": [], "title": "High-speed Depth Stream Generation from a Hybrid Camera.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "High-speed video has been commonly adopted in consumer-grade cameras, augmenting these videos with a corresponding depth stream will enable new multimedia applications, such as 3D slow-motion video. In this paper, we present a hybrid camera system that combines a high-speed color camera with a depth sensor, e.g. Kinect depth sensor, to generate a depth stream that can produce both high-speed and high-resolution RGB+depth stream. Simply interpolating the low-speed depth frames is not satisfactory, where interpolation artifacts and lose in surface details are often visible. We have developed a novel framework that utilizes both shading constraints within each frame and optical flow constraints between neighboring frames. More specifically we present (a) an effective method to find the intrinsics images to allow more accurate normal estimation; and (b) an optimization-based framework to estimate the high-resolution/high-speed depth stream, taking into consideration temporal smoothness and shading/depth consistency. We evaluated our holistic framework with both synthetic and real sequences, it showed superior performance than previous state-of-the-art.", "published": "2016-01-01", "category": "mm", "pdf_url": "", "sub_summary": "", "source": "mm", "doi": "10.1145/2964284.2964305"}]