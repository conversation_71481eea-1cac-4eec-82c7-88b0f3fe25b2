export interface ModelConfig {
  id: string;
  name: string;
  description: string;
}

export const models: ModelConfig[] = [

    // {
    //     id: 'qwen3_30b_a3b',
    //     name: 'Qwen3-30B-A3B',
    //     description: '速度起飞 Qwen3'
    // },
    {
        id: 'qwen3_235b_a22b_think',
        name: 'Qwen3-235B-A22B-Thinking',
        description: 'Qwen3-think 7月更新 最大杯 MOE'
    },
    {
        id: 'qwen3_235b_a22b',
        name: 'Qwen3-235B-A22B',
        description: 'Qwen3 7月更新 最大杯 MOE'
    },
    {
        id: 'glm45',
        name: 'GLM-4.5',
        description: 'GLM-4.5 355B 参数，激活 32B'
    },
    {
        id: 'kimi_k2',
        name: 'Kimi-K2',
        description: 'KIMI - 1000B 参数，激活 32B'
    },
    {
        id: 'deepseek_v3_sc',
        name: 'DeepSeek-V3',
        description: '最新 DeepSeek-V3-0324'
    },
    {
        id: 'deepseek_r1_op',
        name: 'DeepSeek-R1',
        description: '最新 DeepSeek-R1-0528'
    },
    {
        id: 'gpt41',
        name: 'GPT-4.1',
        description: '速度 ↑ 通用能力 ↑  '
    },
    {
        id: 'gemini_25_flash',
        name: 'gemini-2.5-flash',
        description: '速度 ↑↑↑ 通用能力 ↑ '
    },
    {
        id: 'claude4_sonnet',
        name: 'Claude4-Sonnet',
        description: '速度 ↓ 编程能力 ↑↑↑ '
    },
    // {
    //     id: 'o4_mini',
    //     name: 'o4-mini',
    //     description: 'GPT 系列 o4-mini 思考模型，通用能力 ↑  '
    // },
    // {
    //     id: 'qwen3_235b_a22b',
    //     name: 'Qwen3-235B-A22B',
    //     description: 'Qwen3 最大杯 MOE'
    // },
    // {
    //     id: 'glm4_32b',
    //     name: 'GLM4-32B',
    //     description: '智谱 均衡 (非思考)'
    // },
    // {
    //     id: 'glm4_32b_z1',
    //     name: 'GLM4-32B-Thinking',
    //     description: '智谱 均衡'
    // },
    // {
    //     id: 'qwen25_72b',
    //     name: 'Qwen2.5-72B',
    //     description: 'Qwen2.5 最大杯 (非思考，速度稍快)'
    // },
];

export const DEFAULT_MODEL = models[0];