[{"primary_key": "3855573", "vector": [], "sparse_vector": [], "title": "Model Predictive Real-Time Monitoring of Linear Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The predictive monitoring problem asks whether a deployed system is likely to fail over the next T seconds under some environmental conditions. This problem is of the utmost importance for cyber-physical systems, and has inspired real-time architectures capable of adapting to such failures upon forewarning. In this paper, we present a linear model-predictive scheme for the real-time monitoring of linear systems governed by time-triggered controllers and time-varying disturbances. The scheme uses a combination of offline (advance) and online computations to decide if a given plant model has entered a state from which no matter what control is applied, the disturbance has a strategy to drive the system to an unsafe region. Our approach is independent of the control strategy used: this allows us to deal with plants that are controlled using model-predictive control techniques or even opaque machine-learning based control algorithms that are hard to reason with using existing reachable set estimation algorithms. Our online computation reuses the symbolic reachable sets computed offline. The real-time monitor instantiates the reachable set with a concrete state estimate, and repeatedly performs emptiness checks with respect to a safety property. We classify the various alarms raised by our approach in terms of what they imply about the system as a whole. We implement our real-time monitoring approach over numerous linear system benchmarks and show that the computation can be performed rapidly in practice. Furthermore, we also examine the alarms reported by our approach and show how some of the alarms can be used to improve the controller.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00035"}, {"primary_key": "3855574", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Isochronous Execution Models for Mixed-Criticality Systems on Parallel Processors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose redundancy-based execution models to address the reliability and correctness of safety/time-critical applications, and in particular, mixed-criticality systems. In our models, every job has one or more (possibly identical) versions, and all versions of a job are to run isochronously on multiple parallel machines in a lockstep fashion. The redundant machines act as monitoring coprocessors, and the execution of a job is deemed successful as soon as one of its versions completes within its worst-case execution time estimate, at which point we may terminate all the other versions. Doing so e ectively increases the chance that a job completes successfully and thus provides timing guarantees in the form of increased predictability. We present several allocation and scheduling problems with varying levels of generality, with the objective of minimizing the maximum makespan across all processors.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00050"}, {"primary_key": "3855575", "vector": [], "sparse_vector": [], "title": "GPU Scheduling on the NVIDIA TX2: Hidden Details Revealed.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The push towards fielding autonomous-driving capabilities in vehicles is happening at breakneck speed. Semi-autonomous features are becoming increasingly common, and fully autonomous vehicles are optimistically forecast to be widely available in just a few years. Today, graphics processing units (GPUs) are seen as a key technology in this push towards greater autonomy. However, realizing full autonomy in mass-production vehicles will necessitate the use of stringent certification processes. Currently available GPUs pose challenges in this regard, as they tend to be closed-source “black boxes” that have features that are not publicly disclosed. For certification to be tenable, such features must be documented. This paper reports on such a documentation effort. This effort was directed at the NVIDIA TX2, which is one of the most prominent GPU-enabled platforms marketed today for autonomous systems. In this paper, important aspects of the TX2's GPU scheduler are revealed as discerned through experimental testing and validation.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00017"}, {"primary_key": "3855576", "vector": [], "sparse_vector": [], "title": "Beyond Implicit-Deadline Optimality: A Multiprocessor Scheduling Framework for Constrained-Deadline Tasks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the real-time systems community, many studies have addressed how to efficiently utilize a multiprocessor platform so as to accommodate as many periodic/sporadic real-time tasks as possible without violating any timing constraints. The scheduling theory has sufficiently matured for a set of implicit-deadline tasks (the relative deadline equal to the period), yielding a class of optimal scheduling algorithms. However, the same does not hold for a set of constrained-deadline tasks (the relative deadline no larger than the period) in that those task sets have been fully covered by neither existing implicit-deadline optimal scheduling algorithms nor heuristic scheduling algorithms., In this paper, we propose a scheduling framework that not only takes advantage of both existing implicit-deadline optimal and heuristic algorithms, but also surpasses both in finding schedulable constrained-deadline task sets. The proposed framework logically divides a given task set into the higher- and lower-priority classes and schedules the classes using an implicit-deadline optimal algorithm and a heuristic algorithm, respectively. Then, while the proposed framework guarantees schedulability of tasks in the higher-priority class by the target implicit-deadline optimal algorithm, we need to address the following technical issues for enabling tasks in the lower-priority class to efficiently reclaim remaining processor capacity while guaranteeing their schedulability: (i) division of a given task set into the two classes, (ii) selection/development of scheduling algorithms for the two classes, and (iii) development of a schedulability test for the framework with given (i) and (ii). We present a general case showing how to address (i)-(iii), and then a specific case addressing how to further improve schedulability by utilizing characteristics of the specific case. Our simulation results demonstrate that the proposed framework outperforms all existing scheduling algorithms in covering schedulable task sets; in particular, if we focus on task sets with the system density larger than the number of processors, the framework finds up to 446.3% additional schedulable task sets, compared to task sets covered by at least one of existing scheduling algorithms.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00038"}, {"primary_key": "3855577", "vector": [], "sparse_vector": [], "title": "Regular Composite Resource Partition in Open Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In open systems, no global scheduler has knowledge of the complete resource requirements from all the applications. Each application has its own task group and can generate tasks on demand at run time. Regularity-based Resource Partition (RRP) model is an effective strategy to hierarchically allocate resource in such environments. However, when applying the RRP model to multi-resource environments, end-to-end tasks could experience unexpected delay and miss the deadlines. The tasks might arrive at non-resource-slice boundaries because the resource slice sizes of different physical resource may vary in such non-uniform environments. This paper extends the RRP model to non-uniform multi-resource open systems. It introduces a novel composite resource partition abstraction, identifies the feasible conditions for hierarchical regular composite resource partitioning and proposes an acyclic regular composite resource partition scheduling (ARCRPS) algorithm. Simulation results show that compared with the state-of-the-art approach, ARCRPS improves the acceptance ratio by 20% and 25% in uniform and non-uniform multi-resource environments, respectively. A multi-resource scheduling framework jointly considering the CPU and network resources is also designed and implemented to evaluate the feasibility of this theoretical model in practice.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00011"}, {"primary_key": "3855578", "vector": [], "sparse_vector": [], "title": "Memory Bank Partitioning for Fixed-Priority Tasks in a Multi-core System.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In a multi-core platform, resources, such as memory banks and buses, are mostly shared among all cores for power, performance, and cost reasons. The access interference on the shared resources poses a major challenge on the analysis of real-time properties, but can be alleviated if task data partition onto memory banks is applied with care. In this paper, we consider to schedule RAS (resource access sporadic) tasks onto a platform consisting of homogeneous cores and capacity-limited memory banks. According to our observation, we should avoid internal data spreading among the memory banks for a task while advocate external data spreading among memory banks for a given task set. We propose a two-phase algorithm with (4 + ρ + 3(2γ+1)/γ) speedup factor and (γ + 1) memory augmentation factor, where ρ γ 0 and ρ ≥ 1. The derived adjustable resource augmentation factors can be useful in terms of system synthesis and schedulability. Moreover, under the premise that a given task set is feasible, we devise a bi-section approach that can derive a schedulable solution requiring the least amount of memory augmentation. According to our experiment results, the proposed algorithm significantly outperformed the state-of-the-art algorithm [15] in terms of schedulability test even when memory augmentation is prohibited.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00027"}, {"primary_key": "3855579", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Real-Time Containers for Large-Scale Mixed-Criticality Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper presents the notion of real-time containers, or rt-cases, conceived as the convergence of software container technologies, such as Linux Containers and/or Docker, and real-time operating systems. The idea is to allow critical containers, characterized by stringent timeliness and reliability requirements, to cohabit with traditional non real-time containers on the same hardware. The approach allows to keep the advantages of real-time virtualization, largely adopted in the industry, while reducing its inherent scalability limitation when to be applied to large-scale mixed-criticality systems. The paper provides a reference architecture scheme for implementing the real-time container concept on top on a patched real-time Linux kernel, and it overviews the challenges to be faced to implement the rt-case vision.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00046"}, {"primary_key": "3855580", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Adaptive Scheduling with Approximate Computing for Audio Graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Interactive music systems are highly dynamic systems that combine audio processing and control in real-time. They often have to work on soft real-time platforms, where no stringent real-time guarantees can be upheld. We present here an overhead-aware online degradation algorithm that finds a tradeoff between quality and lateness for the processing nodes of a dynamic audio graph.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00045"}, {"primary_key": "3855581", "vector": [], "sparse_vector": [], "title": "Analysis Techniques for Supporting Hard Real-Time Sporadic Gang Task Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "This paper studies the problem of scheduling hard real-time sporadic gang task systems under global earliest-deadline-first, where a gang application's threads need to be concurrently scheduled on distinct processors. A novel approach combining new lag-based reasoning and executing/non-executing gang interval analysis technique is introduced, which is able to characterize the parallelisminduced idleness, as a key challenge of analyzing gang task schedules. To the best of our knowledge, this approach yields the first utilization-based test for hard real-time gang task systems.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00019"}, {"primary_key": "3855582", "vector": [], "sparse_vector": [], "title": "REC: Predictable Charging Scheduling for Electric Taxi Fleets.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Yanhua Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Due to the energy security concern, our society is witnessing a surge of EV fleet applications, e.g., public EV taxi fleet systems. A major issue impeding an even more widespread adoption of EVs is range anxiety, which is due to several factors including limited battery capacity, limited availability of battery charging stations, and long charging time compared to traditional gasoline vehicles. By analyzing our accessible real-world EV taxi system-wide datasets, we observe that current EV taxi drivers often suffer from unpredictable, long waiting times at charging stations, due to temporally and spatially unbalanced utilization among charging stations. This is mainly because current taxi fleet management system simply rely on taxi drivers to make charging decisions. In this paper, In this paper, we develop REC, a Real-time Ev Charging scheduling framework for EV taxi fleets, which informs each EV taxi driver at runtime when and where to charge the battery. REC is able to analytically guarantee predictable and tightly bounded waiting times for all EVs in the fleet and temporally/spatially balanced utilization among charging stations, if each driver follows the charging decision made by REC. Moreover, REC can further efficiently handle real-life issues, e.g., allowing a taxi driver to charge at its preferred charging station while still guaranteeing balanced charging station utilization.We have extensively evaluated REC using our accessible real-world EV taxi system-wide datasets. Experimental results show that REC is able to address the unpredictability and unbalancing issues existing in current EV taxi fleet systems, yielding predictable and tightly bounded waiting times, and equally important, temporally/spatially balanced charging station utilization.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00034"}, {"primary_key": "3855583", "vector": [], "sparse_vector": [], "title": "Fixed-Priority Schedulability of Sporadic Tasks on Uniprocessors is NP-Hard.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We study the computational complexity of the FP-schedulability problem for sporadic or synchronous periodic tasks on a preemptive uniprocessor. We show that this problem is (weakly) NP-hard, even when restricted to either (i) task sets with implicit deadlines and rate-monotonic priority ordering, or (ii) task sets with constrained deadlines, deadline-monotonic priority ordering and utilization bounded by any constant c, such that 0 <; c <; 1.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00020"}, {"primary_key": "3855584", "vector": [], "sparse_vector": [], "title": "Temporal Capabilities: Access Control for Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Embedded systems are increasingly required to handle code of various qualities that must often be isolated, yet predictably share resources. This has motivated the isolation of, for example, mission-critical code from best-effort features using isolation structures such as virtualization. Such systems usually focus on limiting interference between subsystems, which complicates the increasingly common functional dependencies between them. Though isolation must be paramount, the fundamental goal of efficiently sharing hardware motivates a principled mechanism for cooperating between subsystems. This paper introduces Temporal Capabilities (TCaps) which integrate CPU management into a capability-based access-control system and distribute authority for scheduling. In doing so, the controlled temporal coordination between subsystems becomes a first-class concern of the system. By enabling temporal delegations to accompany activations and requests for service, we apply TCaps to a virtualization environment with a shared VM for orchestrating I/O. We show that TCaps, unlike prioritizations and carefully chosen budgets, both meet deadlines for a hard real-time subsystem, and maintain high throughput for a best-effort subsystem.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00013"}, {"primary_key": "3855585", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Toward a Coq-Certified Tool for the Schedulability Analysis of Tasks with Offsets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents the first steps toward a formally proven tool for schedulability analysis of tasks with offsets. We formalize and verify the seminal response time analysis of <PERSON><PERSON> by extending the Prosa proof library, which is based on the Coq proof assistant. Thanks to Coq's extraction capabilities, this will allow us to easily obtain a certified analyzer. Additionally, we want to build a Coq certifier that can verify the correctness of results obtained using related (but uncertified), already existing analyzers. Our objective is to investigate the advantages and drawbacks of both approaches, namely the certified analysis and the certifier. The work described in this paper as well as its continuation is intended to enrich the Prosa library.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00049"}, {"primary_key": "3855586", "vector": [], "sparse_vector": [], "title": "Sustainability in Mixed-Criticality Scheduling.", "authors": ["<PERSON><PERSON><PERSON>", "Sai Sr<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Sustainability is a formalization of the requirement for scheduling algorithms and schedulability tests that a system deemed to be correctly schedulable should remain so if its run-time behavior is better than anticipated. The notion of sustainability is extended to mixed-criticality systems, and sustainability properties are determined for a variety of widely-studied uniprocessor and multi-processor mixed-criticality scheduling algorithms.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00010"}, {"primary_key": "3855587", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Cache-Aware Partitioned EDF Scheduling for Multi-core Real-Time Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zhang"], "summary": "As the number of cores and utilization of the system are increasing quickly, shared resources like caches are interfering tasks' execution behaviors more heavily. In order to achieve resource efficiency in both temporal and spatial domains for multi-core real-time systems, caches should be taken into consideration when performing partitions. In this paper, partitioned Earliest Deadline First (EDF) scheduling on a preemptive multi-core platform is considered. We propose a new system model that covers inter-task cache interference and describe some ongoing work in identifying proper partition schemes under such settings.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00054"}, {"primary_key": "3855588", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Maximizing Model Accuracy in Real-time and Iterative Machine Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As iterative machine learning (ML) (e.g. neural network based supervised learning and k-means clustering) becomes more ubiquitous in our daily life, it is becoming increasingly important to complete model training quickly to support real-time decision making, while still achieving high model accuracy (e.g. low prediction errors) that is critical for profits of ML tasks. Motivated by the observation that the small proportions of accuracy-critical input data can contribute to large parts of model accuracy in many iterative ML applications, this paper introduces a system middleware to maximize model accuracy by spending the limited time budget on the most accuracy-related input data. To achieve this, our approach employs a fast method to divide the input data into multiple parts of similar points and represents each part with an aggregated data point. Using these points, it quickly estimates the correlations between different parts and model accuracy, thus allowing ML tasks to process the most accuracy-related parts first. We incorporate our approach with two popular supervised and unsupervised ML algorithms on Spark and demonstrate its benefits in providing high model accuracy under short deadlines.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00055"}, {"primary_key": "3855589", "vector": [], "sparse_vector": [], "title": "RT-IFTTT: Real-Time IoT Framework with Trigger Condition-Aware Flexible Polling Intervals.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With a simple \"If This Then That\" syntax, IoT frameworks such as IFTTT and Microsoft Flow allow users to easily create custom applets integrating sensors and actuators. Users expect appropriate actions to be taken within a certain latency in response to sensor value changes while the sensors usually have limited battery power. Therefore, reading the sensor values at the right time point is crucial for the IoT frameworks to support real-time responses of the applets while saving battery lives of sensors. However, existing IoT frameworks periodically read the sensor data with fixed intervals without reflecting current sensor values and trigger conditions of applets, so the intervals are either too long to meet the real-time constraints, or too short wasting batteries of sensors. This work extends the existing IFTTT syntax for users to describe real-time constraints, and proposes the first real-time IoT framework with trigger condition-aware flexible polling intervals, called RT-IFTTT. RT-IFTTT analyzes current sensor values, trigger conditions and constraints of all the applets in the framework, and dynamically calculates the efficient polling intervals for each sensor. This work collects real-world sensing data from 10 physical sensors for 10 days, and shows that the RT-IFTTT framework with the proposed scheduling algorithm executes 100 to 400 applets according to user-defined real-time constraints with up to 64.12% less sensor polling counts compared to the framework with the fixed intervals.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00032"}, {"primary_key": "3855590", "vector": [], "sparse_vector": [], "title": "An O(Log Log m)-Competitive Algorithm for Online Machine Minimization.", "authors": ["Sungjin Im", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper considers the online machine minimization problem, a basic real time scheduling problem. The setting for this problem consists of n jobs that arrive over time, where each job has a deadline by which it must be completed. The goal is to design an online scheduler that feasibly schedules the jobs on a nearly minimal number of machines. An algorithm is c-machine optimal if the algorithm will feasibly schedule a collection of jobs on c ·m machines if there exists a feasible schedule on m machines. For over two decades the best known result was a O(log P)-machine optimal algorithm, where P is the ratio of the maximum to minimum job size. In a recent breakthrough, a O(log m)-machine optimal algorithm was given. In this paper, we exponentially improve on this recent result by giving a O(log log m)-machine optimal algorithm.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00039"}, {"primary_key": "3855591", "vector": [], "sparse_vector": [], "title": "Semi-Federated Scheduling of Parallel Real-Time Tasks on Multiprocessors.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Federated scheduling is a promising approach to schedule parallel real-time tasks on multi-cores, where each heavy task exclusively executes on a number of dedicated processors, while light tasks are treated as sequential sporadic tasks and share the remaining processors. However, federated scheduling suffers resource waste since a heavy task with processing capacity requirement x+epsilon (where x is an integer and 0 epsilon 1) needs x+1 dedicated processors. In the extreme case, almost half of the processing capacity is wasted. In this paper we propose the semi-federate scheduling approach, which only grants x dedicated processors to a heavy task with processing capacity requirement x+epsilon, and schedules the remaining epsilon part together with light tasks on shared processors. Experiments with randomly generated task sets show the semi-federated scheduling approach significantly outperforms not only federated scheduling, but also all existing approaches for scheduling parallel real-time tasks on multi-cores.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00015"}, {"primary_key": "3855592", "vector": [], "sparse_vector": [], "title": "Offset Assignment to Signals for Improving Frame Packing in CAN-FD.", "authors": ["<PERSON><PERSON><PERSON>", "S. S. Ravi", "<PERSON><PERSON><PERSON>", "Unmesh D<PERSON>", "<PERSON><PERSON>", "Haibo Zeng"], "summary": "Controller Area Network (CAN) is a widely used protocol that allows communication among Electronic Control Units (ECUs) in automotive electronics. It was extended to CAN-FD (CAN with Flexible Data-rate) to meet the increasing demand for bandwidth utilization caused by the growing number of features in modern automobiles. The signal-to-frame packing problem has been studied in literature for both CAN and CAN-FD. In this work, we propose and formulate, for the first time, the signal offset assignment problem (SOAP) in a frame in order to improve the bus bandwidth utilization. We prove that SOAP is NP-complete. We propose a general approximation framework (GAF) for SOAP which can use any approximation algorithm for the makespan minimization problem (MMP) in multiprocessor systems. We derive the performance guarantee provided by GAF as a function of the performance guarantee of the approximation algorithm for MMP and the number of signal periods in the frame. We demonstrate the efficacy of our approach through experiments using three different algorithms (two approximation algorithms and an integer linear programming formulation) for MMP in GAF. Our results indicate that by using offsets for signals in frame packing schemes, one can achieve about 10.54% improvement in bandwidth utilization (on a single bus) in CAN-FD systems.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00023"}, {"primary_key": "3855593", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: A Flexible Router Architecture for 3D NoCs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Flexibility either in buffering or routing is a very promising solution for NoC congestion problem. In this paper, a new 3D router with flexible architecture is introduced and validated for 3D NoCs. In fact, the Flexible router architecture introduced in literature would be deadlock prone if it is incorporated in a 3D NoC domain. We successfully design the new buffering constraints and extensively evaluate its performance under various traffic scenarios using real benchmark applications. We show the great enhancement of the new candidate router architecture in comparison to the conventional 3D NoC router architecture. Finally, we show that this enhancement in performance comes at a very low impact in power and area; especially for large NoC sizes.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00044"}, {"primary_key": "3855594", "vector": [], "sparse_vector": [], "title": "End-to-End Network Delay Guarantees for Real-Time Systems Using SDN.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Rakesh B. Bobba"], "summary": "Real-time systems (RTS) require end-to-end delay guarantees for the delivery of network packets. In this paper, we propose a framework to reduce the management and integration overheads for such real-time (RT) network flows by leveraging the capabilities of software-defined networking (SDN) - capabilities that include global visibility and management of the network. Given the specifications of flows that must meet hard real-time requirements, our framework synthesizes paths through the network. To guarantee that these flows meet both, their bandwidth and end-to-end timing requirements, our framework solves a multi-constraint optimization problem using a heuristic algorithm. We use exhaustive emulations and experiments on hardware switches to demonstrate our techniques and feasibility of our approach. As a result of this work, SDNs become \"delay-aware\" and thus can be adapted for use in safety-critical and other delay-sensitive applications.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00029"}, {"primary_key": "3855595", "vector": [], "sparse_vector": [], "title": "Abstract PRET Machines.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Prior work has shown that it is possible to design microarchitectures called PRET machines that deliver precise and repeatable timing of software execution without sacrificing performance. That prior work provides specific designs for PRET microarchitectures and compares them against conventional designs. This paper defines a class of microarchitectures called abstract PRET machines (APMs) that capture the essential temporal properties of PRET machines. We show that APMs deliver deterministic timing with no loss of performance for a family of real-time problems consisting of sporadic event streams with deadlines equal to periods. On the other hand, we observe a tradeoff between deterministic timing and the ability to meet deadlines for sporadic event streams with constrained deadlines.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00041"}, {"primary_key": "3855596", "vector": [], "sparse_vector": [], "title": "Network Scheduling for Secure Cyber-Physical Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Existing design techniques for providing security guarantees against network-based attacks in cyber-physical systems (CPS) are based on continuous use of standard cryptographic tools to ensure data integrity. This creates an apparent conflict with common resource limitations in these systems, given that, for instance, lengthy message authentication codes (MAC) introduce significant overheads. We present a framework to ensure both timing guarantees for real-time network messages and Quality-of-Control (QoC) in the presence of network-based attacks. We exploit physical properties of controlled systems to relax constant integrity enforcement requirements, and show how the problem of feasibility testing of intermittently authenticated real-time messages can be cast as a mixed integer linear programming problem. Besides scheduling a set of real-time messages with predefined authentication rates obtained from QoC requirements, we show how to optimally increase the overall system QoC while ensuring that all real-time messages are schedulable. Finally, we introduce an efficient runtime bandwidth allocation method, based on opportunistic scheduling, in order to improve QoC. We evaluate our framework on a standard benchmark designed for CAN bus, and show how an infeasible message set with strong security guarantees can be scheduled if dynamics of controlled systems are taken into account along with real-time requirements.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00012"}, {"primary_key": "3855597", "vector": [], "sparse_vector": [], "title": "Work-in-Progress Paper: An Analysis of the Impact of Dependencies on Probabilistic Timing Analysis and Task Scheduling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recently there has been a renewed interest for probabilistic timing analysis (PTA) and probabilistic task scheduling (PTS). Despite the number of works in both fields, the link between them is weak: works on the latter build upon a series of assumptions on the probabilistic behavior of each task – or instances (jobs) of it – that have not been shown how to be fulfilled by PTA. This paper makes a first step towards covering this gap with emphasis on providing the right meaning of pWCET estimate as understood by both PTA and PTS. We show that the main issue related to ensuring that PTS assumptions on pWCET estimates are captured by PTA relates to the dependencies among tasks, and even jobs of a given task. Both change the scope of applicability of pWCET estimates provided by PTA and hence, their use by PTS.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00042"}, {"primary_key": "3855598", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Dealing with Aperiodic Tasks on Quasi-Partitioning Scheduling.", "authors": ["<PERSON><PERSON><PERSON><PERSON> Santos Nascimento", "<PERSON>", "<PERSON>"], "summary": "Quasi-Partition Scheduling (QPS) is a new scheduling approach with low preemption and migration overhead. QPS was originally proposed taking into consideration hard periodic and sporadic tasks. In this paper we discuss a proposal for extending QPS to deal with soft aperiodic tasks.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00048"}, {"primary_key": "3855599", "vector": [], "sparse_vector": [], "title": "An Exact and Sustainable Analysis of Non-preemptive Scheduling.", "authors": ["<PERSON><PERSON>", "Björn B. Brandenburg"], "summary": "This paper provides an exact and sustainable schedulability test for a set of non-preemptive jobs scheduled with a fixed-job-priority (FJP) policy upon a uniprocessor. Both classic work-conserving and recent non-work-conserving schedulers are supported. Jobs may exhibit both release jitter and execution time variation. Both best- and worst-case response time bounds are derived. No prior response-time analysis (RTA) for this general setting is both exact and sustainable, nor does any prior RTA support non-work-conserving schedulers. The proposed analysis works by building a schedule graph that precisely abstracts all possible execution scenarios. Key to deferring the state-space explosion problem is a novel path-merging technique that collapses similar scenarios without giving up analysis precision. In an empirical evaluation with randomly generated workloads based on an automotive benchmark, the method is shown to scale to 30+ periodic tasks with thousands of jobs (per hyperperiod).", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00009"}, {"primary_key": "3855600", "vector": [], "sparse_vector": [], "title": "Aerial Video Stream over Multi-hop Using Adaptive TDMA Slots.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Unmanned Aerial Vehicles (UAVs) are rapidly becoming an important tool for applications like surveillance, target tracking and facility monitoring. In many of these contexts, one or more UAVs need to reach an area of interest (AOI) while streaming live video to a ground station (GS) where one or more operators inspect the AOI and carry out fine control of UAVs position. In remote areas, intermediate UAVs can act as relays and form a line network to extend range. Interactive control requires a live video stream where both throughput and delay are important. In this paper, we show that routing packets over CSMA/CA (native medium access protocol of WiFi, the most common wireless technology among UAVs) behaves poorly in this context due to link asymmetries. We propose a novel distributed, adaptive and self-synchronized TDMA protocol (DVSP) that both enhances delay and packet delivery while operating on commodity hardware and leveraging a standard UDP/IP protocol stack. We prove that DVSP converges to a global solution that minimizes delay using local information, only, thus in a fully distributed manner. Real world experiments with multiple UAVs show gains in delay up to 75%, and packet delivery up to 50%, without sacrificing goodput.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00022"}, {"primary_key": "3855601", "vector": [], "sparse_vector": [], "title": "Awakening Power of Physical Layer: High Precision Time Synchronization for Industrial Ethernet.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Fengyuan Ren"], "summary": "High-precision time synchronization is critical for nowadays industrial Ethernet systems. Most existing time synchronization mechanisms are implemented based on packet communication. This interaction pattern, however, greatly limits their synchronizing frequency. In order to achieve microsecond-level synchronization precision, expensive high-quality oscillator is necessary for maintaining low clock skew under this long synchronization period (usually several seconds). Furthermore, packet processing introduces many nondeterministic variances (e.g. network stack overhead), which needs to be carefully eliminated. In this paper, we propose the brand-new Industrial Time Protocol (ITP). We deploy the entire ITP in the physical layer, so it eliminates most time uncertainties caused by network stack processing. Furthermore, ITP leverages the InterFrame Gap (IFG), which is the inherent interval between any two Ethernet frames, to carry the synchronization message. With this novel design, ITP can synchronize peer devices at very high frequency without degrading the goodput. The accuracy of ITP is bounded by 16ns for two adjacent devices with only intrinsic cheap oscillator. Furthermore, our theoretical analysis deduces that ITP guarantees 16N-nanosecond accuracy for N-hop network. We implement ITP design with NetFPGA. Experiments show that ITP can provide about 76-nanosecond accuracy for #hops=16 network under severe congestions. In addition, the design of ITP is scalable. It only consumes about 0.67% of logic cells in the low-end FPGA for supporting every ITP-aware port increase.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00021"}, {"primary_key": "3855602", "vector": [], "sparse_vector": [], "title": "Integrated Analysis of Cache Related Preemption Delays and Cache Persistence Reload Overheads.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Schedulability analysis for tasks running on micro- processors with cache memory is incomplete without a treatment of Cache Related Preemption Delays (CRPD) and Cache Persistence Reload Overheads (CPRO). State-of-the-art analyses compute CRPD and CPRO independently, which might result in counting the same overhead more than once. In this paper, we analyze the pessimism associated with the independent calculation of CRPD and CPRO in comparison to an integrated approach. We answer two main questions: (1) Is it benecial to integrate the calculation of CRPD and CPRO? (2) When and to what extent can we gain in terms of schedulability by integrating the calculation of CRPD and CPRO? To achieve this, we (i) identify situations where considering CRPD and CPRO separately might result in overestimating the total memory overhead suffered by tasks, (ii) derive new analyses that integrate the calculation of CRPD and CPRO; and (iii) perform a thorough experimental evaluation using benchmarks to compare the performance of the integrated analysis against the separate calculation of CRPD and CPRO.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00025"}, {"primary_key": "3855603", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Best-Case Response Time Analysis for Ethernet AVB.", "authors": ["<PERSON>Verd<PERSON>co", "Pieter J. <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the automotive industry, Ethernet is currently being introduced as a viable solution for real-time communication with high bandwidth requirements. In this paper, we present and prove a tight bound for the relative best-case response time of a frame in an Ethernet AVB switch compared to its response time in a schedule without interference. Our analysis is based on the observation that frames in a burst, scheduled using a credit shaping policy, may be scheduled earlier in case of interference than in a schedule without interference. We show how an upper bound on the build up of credit may be used to bound this relative speed-up of frames.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00043"}, {"primary_key": "3855604", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Design-Space Exploration of Multi-Core Processors for Safety-Critical Real-Time Systems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper we outline Design Space Exploration methodology aimed at homogeneous multi-core architectures, where the safety-criticality is the crux of a system design. Multi-core architectures provide better computational abilities, but at the same time complicate the computation of timing bounds. Determining suitable architectures that achieve timing requirements is an important aspect for a system designer. The proposed work conceptualizes ways to automate and explore different design facets of a multi-core processor. The intention is to ensure that the particular application meets its deadlines, while optimizing other objectives such as minimizing hardware costs, energy consumption and floor area. The automated exploration builds upon Mulitcore Response Time Analysis for timing verification and multicube for heuristic search methods. The aim is to generate an architecture design in the end that can be used directly to build a custom application specific processor.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00040"}, {"primary_key": "3855605", "vector": [], "sparse_vector": [], "title": "Event-Driven Bandwidth Allocation with Formal Guarantees for Camera Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern computing systems are often formed by multiple components that interact with each other through the use of shared resources (e.g., CPU, network bandwidth, storage). In this paper, we consider a representative scenario of one such system in the context of an Internet of Things application. The system consists of a network of self-adaptive cameras that share a communication channel, transmitting streams of frames to a central node. The cameras can modify a quality parameter to adapt the amount of information encoded and to affect their bandwidth requirements and usage. A critical design choice for such a system is scheduling channel access, i.e., how to determine the amount of channel capacity that should be used by each of the cameras at any point in time. Two main issues have to be considered for the choice of a bandwidth allocation scheme: (i) camera adaptation and network access scheduling may interfere with one another, (ii) bandwidth distribution should be triggered only when necessary, to limit additional overhead. This paper proposes the first formally verified event-triggered adaptation scheme for bandwidth allocation, designed to minimize additional overhead in the network. Desired properties of the system are verified using model checking. The paper also describes experimental results obtained with an implementation of the scheme.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00030"}, {"primary_key": "3855606", "vector": [], "sparse_vector": [], "title": "On Using GEV or Gumbel Models When Applying EVT for Probabilistic WCET Estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The technique known as Measurement-Based Probabilistic Timing Analysis (MBPTA) promises producing Worst-Case Execution Time (WCET) bounds for real-time systems' tasks based on the analysis of execution time measurements through Extreme Value Theory (EVT), a statistical framework designed to estimate the probability of extreme events. For that MBPTA requires the analysed tasks' maximum observed execution times to adhere to an extreme value distribution, such as Gumbel or Generalized Extreme Value (GEV), and allows determining execution time values expected to be exceeded only with arbitrarily small probabilities. Several works on the area assume that the Gumbel model should be employed in such analysis, while others consider GEV, which generalizes Weibull, Gumbel and Fréchet models, would be more adequate. In this work we perform an empirical assessment on the reliability and tightness of the WCET bounds determined through the GEV and Gumbel models. We do so by comparing the yielded estimates and their associated confidence intervals against the maximum values observed on large samples (e.g. of size 100 million), of both real and synthetic nature, as the modelling sample size is increased.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00028"}, {"primary_key": "3855607", "vector": [], "sparse_vector": [], "title": "Global EDF-Based Scheduling of Multiple Independent Synchronous Dataflow Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The global scheduling of systems that can be modeled as collections of multiple independent recurrent real-time tasks, each represented as a synchronous dataflow graph (SDFG), upon an identical multiprocessor platform is considered. An EDF-based scheduling algorithm is proved optimal under the speedup factor metric, and a speedup-optimal sufficient schedulability test is derived.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00036"}, {"primary_key": "3855608", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: Networked Control of Autonomous Underwater Vehicles with Acoustic and Radio Frequency Hybrid Communication.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Özg<PERSON>r Gü<PERSON>", "<PERSON><PERSON>"], "summary": "Underwater control applications, such as control of Autonomous Underwater Vehicles (AUV)s, have recently gained significant interest, and there is a growing demand for high-speed wireless communication between AUVs and base station. Acoustic communication provides low data rates and high propagation delays, both of which are not suitable for employing high control gains for the navigation of AUVs. On the other hand, Radio Frequency (RF) communication provides high data rate, but it is constrained by high attenuation due to high conductivity and permittivity of water resulting in a short working range. In this work, we propose an underwater networked control system with acoustic and RF hybrid communication, where an acoustic link is employed for long range communication and RF link is applied in close range. Our performance results indicate that the acoustic and RF hybrid communication system takes up to 38.5% less time to dock, up to 91% smaller steady state error and spends up to 39% lower communication energy as compared to the acoustic only system at the expense of up to 22.35% higher motive energy.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00051"}, {"primary_key": "3855609", "vector": [], "sparse_vector": [], "title": "Synthesis of Queue and Priority Assignment for Asynchronous Traffic Shaping in Switched Ethernet.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Real-time switched Ethernet communication is of increasing importance in many cyber-physical and embedded systems application areas such as automotive electronics, avionics, and industrial control. The IEEE 802.1 Time-Sensitive Networking (TSN) task group develops standards for real-time Ethernet, for example a time-triggered traffic class (IEEE 802.1Qbv-2015). New application areas, such as active safety and autonomous driving using radar, lidar, and camera sensors, which do not fall into the strictly periodic, time-triggered communication model, require a flexible traffic class that can accommodate various communication models while still providing hard real-time guarantees. In our previous work, we developed such a traffic class, Urgency-Based Scheduler (UBS), and its worst-case latency analysis. UBS is currently under standardization (P802.1Qcr) in the TSN task group. In this paper, we introduce and solve the UBS synthesis problem of assigning hard real-time data flows to queues and priorities to queues, the main parameters that determine communication latencies. The synthesis problem is particularly challenging due to the flexibility offered by UBS to aggregate flows and assign individual priority levels per network hop. We present an SMT approach, a cluster-based heuristic, and an extensive experimental evaluation.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00024"}, {"primary_key": "3855610", "vector": [], "sparse_vector": [], "title": "Real-Time Scheduling and Analysis of OpenMP Task Systems with Tied Tasks.", "authors": ["Jinghao Sun", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "OpenMP is a promising framework for developing parallel real-time software on multi-cores. Although similar to the DAG task model, OpenMP task systems are significantly more difficult to analyze due to constraints posed by the OpenMP specification. An important feature in OpenMP is tied tasks, which must execute on the same thread during the whole life cycle. Although tied tasks enjoy benefits in simplicity and efficiency, it was considered to be not suitable to real-time systems due to its complex behavior. In this paper, we study the realtime scheduling and analysis of OpenMP task systems with tied tasks. First, we show that under the existing scheduling algorithms in OpenMP, tied tasks indeed may lead to extremely bad timing behaviors where the parallel workload is sequentially executed completely. To solve this problem, we proposed a new scheduling algorithm and developed two response time bounds for it, with different trade-off between simplicity and analysis precision. Experiments with both randomly generated OpenMP task systems and realistic OpenMP programs show that the response time bounds obtained by our approach for tied task systems are very close to that of untied tasks.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00016"}, {"primary_key": "3855611", "vector": [], "sparse_vector": [], "title": "Revisiting GPC and AND Connector in Real-Time Calculus.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Real-Time Calculus (RTC) is a powerful framework for modeling and worst-case performance analysis of networked systems. GPC and AND are two fundamental components in RTC, which model priority-based resource arbitration and synchronization operations, respectively. In this paper, we revisit GPC and AND. For GPC, we develop tighter output arrival curves to more precisely characterize the output event streams. For AND, we first identify a problem in the existing analysis method that may lead to negative values in the output curves, and present corrections to the problem. Then we generalize AND to synchronize more than two input event streams. We implement our new theoretical results and conduct experiments to evaluate their performance. Experiment results show significant improvement of our new methods in analysis precision and efficiency.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00031"}, {"primary_key": "3855612", "vector": [], "sparse_vector": [], "title": "Jitter-Compensated VHT and Its Application to WSN Clock Synchronization.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Accurate and energy-efficient clock synchronization is an enabler for many applications of Wireless Sensor Networks. A fine-grained synchronization is beneficial both at the system level, for example to favor deterministic radio protocols, and at the application level, when network-wide event timestamping is required. However, there is a tradeoff between the resolution of a WSN node's timekeeping device and its energy consumption. The Virtual High-resolution Timer (VHT) is an innovative solution, that was proposed to overcome this tradeoff. It combines a high-resolution oscillator to a low-power one, turning off the former when not needed. In this paper we improve VHT by first identifying the jitter of the low-power oscillator as the current limit to the technique, and then proposing an enhanced solution that synchronizes the fast and the slow clock, rejecting the said jitter. The improved VHT is also less demanding than the original technique in terms of hardware resources. Experimental results show the achieved advantages in terms of accuracy.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00033"}, {"primary_key": "3855613", "vector": [], "sparse_vector": [], "title": "Work-in-Progress: TTI: A Timing ISA for LET Model in Safety-Critical Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Safety-critical systems have suffered a complexity growth as the number of services continuously increases in these systems. The Logical Execution Time (LET) model is applied to tackle this issue due to its simple strategies and deterministic timed behaviors. However, existing implementations of LET usually rely on periodic timer interrupts of operating systems, yielding limited time precision and enormous jitter in kernel's executions. In this paper, we propose a time-triggered instruction set - TTI to augment ISA with timing properties. TTI implements as a processor architecture extension using co-processor2 interfaces in standard MIPS32. The extension mainly comprises a task management module, and a timed I/O-behaviors management module. Preliminary results show that our approach can significantly reduce overheads of LET kernel and jitters of the LET-based tasks compared to traditional implementations, which can achieve cycle-level precise timed behaviors as a result.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00047"}, {"primary_key": "3855614", "vector": [], "sparse_vector": [], "title": "Functionally and Temporally Correct Simulation of Cyber-Systems for Automotive Systems.", "authors": ["Kyoung-Soo We", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The current simulation tools used in the automotive industry do not correctly model timing behaviors of cyber-systems such as varying execution times and preemptions. Thus, they cannot correctly predict the real control performance. Motivated by this limitation, this paper proposes functionally and temporally correct simulation for the cyber-side of an automotive system. The key idea is to keep the data and time correctness only at physical interaction points and enjoy freedom of scheduling simulated jobs for all other cases. This way, the proposed approach significantly improves the real-time simulation capacity of the state-of-the-art simulation methods while keeping the functional and temporal correctness.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00014"}, {"primary_key": "3855615", "vector": [], "sparse_vector": [], "title": "Schedulability Analysis of Non-preemptive Real-Time Scheduling for Multicore Processors with Shared Caches.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Shared caches in multicore processors introduce serious difficulties in providing guarantees on the real-time properties of embedded software due to the interaction and the resulting contention in the shared caches. To address this problem, we develop a new schedulability analysis for real-time multicore systems with shared caches. To the best of our knowledge, this is the first work that addresses the schedulability problem with inter-core cache interference. We construct an integer programming formulation, which can be transformed to an integer linear programming formulation, to calculate an upper bound on cache interference exhibited by a task within a given execution window. Using the integer programming formulation, an iterative algorithm is presented to obtain the upper bound on cache interference a task may exhibit during one job execution. The upper bound on cache interference is subsequently integrated into the schedulability analysis to derive a new schedulability condition. A range of experiments is performed to investigate how the schedulability is degraded by shared cache interference.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00026"}, {"primary_key": "3855616", "vector": [], "sparse_vector": [], "title": "On the Soft Real-Time Optimality of Global EDF on Uniform Multiprocessors.", "authors": ["<PERSON><PERSON> Yang", "<PERSON>"], "summary": "It has long been known that the global earliest-deadlinefirst (GEDF) scheduler is soft real-time (SRT) optimal for sporadic task systems executing on identical multiprocessor platforms, regardless of whether task execution is preemptive or non-preemptive. This notion of optimality requires deadline tardiness to be provably bounded for any feasible task system. In recent years, there has been interest in extending these SRT optimality results to apply to uniform heterogeneous platforms, in which processors may have different speeds. However, it was recently shown that nonpreemptive GEDF is not SRT optimal on such platforms. The remaining case, preemptive GEDF, has turned out to be quite difficult to tackle and has remained open for a number of years. In this paper, this case is resolved by showing that preemptive GEDF is indeed SRT optimal on uniform platforms, provided a certain job migration policy is used.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00037"}, {"primary_key": "3855617", "vector": [], "sparse_vector": [], "title": "The Virtual Deadline Based Optimization Algorithm for Priority Assignment in Fixed-Priority Scheduling.", "authors": ["Ye<PERSON> Zhao", "Haibo Zeng"], "summary": "This paper considers the problem of design optimization for real-time systems scheduled with fixed priority, where task priority assignment is part of the decision variables, and the timing constraints and/or objective function linearly depend on the exact value of task response times (such as end-to-end deadline constraints). The complexity of response time analysis techniques makes it difficult to leverage existing optimization frameworks and scale to large designs. Instead, we propose an efficient optimization framework that is three magnitudes (1,000×) faster than Integer Linear Programming (ILP) while providing solutions with the same quality. The framework centers around three novel ideas: (1) An efficient algorithm that finds a schedulable task priority assignment for minimizing the average worst-case response time; (2) The concept of Maximal Unschedulable Deadline Assignment (MUDA) that abstracts the schedulability conditions, i.e., a set of maximal virtual deadline assignments such that the system is unschedulable; and (3) A new optimization procedure that leverages the concept of MUDA and the efficient algorithm to compute it.", "published": "2017-01-01", "category": "rtss", "pdf_url": "", "sub_summary": "", "source": "rtss", "doi": "10.1109/RTSS.2017.00018"}]