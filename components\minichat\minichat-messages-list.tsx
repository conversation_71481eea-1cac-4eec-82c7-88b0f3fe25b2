import React, { useRef, useEffect, useState, Dispatch, SetStateAction } from 'react';
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronRight } from 'lucide-react';
import { ScrollArea } from "@/components/ui/scroll-area";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';
import { toast } from 'react-hot-toast';

// 导出消息类型接口供其他文件使用
export interface Message {
  role: 'user' | 'assistant';
  content: string;
  thinking?: string;
  isThinking?: boolean;
  showThinking?: boolean;
}

interface MiniChatMessagesListProps {
  messages: Message[];
  setMessages: Dispatch<SetStateAction<Message[]>>;
  toggleThinking: (index: number) => void;
}

// 导出插件配置供其他文件使用
export const remarkPlugins = [remarkGfm, remarkMath];
export const rehypePlugins = [rehypeRaw, rehypeSanitize, rehypeKatex];

export const INIT_MESSAGE = '我是论文小助手😗，可以帮您：\n \
1. 📊 总结这些论文的共同点和差异\n \
2. 📈 分析某个具体研究方向的发展趋势\n \
3. 🔍 解释某个具体概念或方法\n \
4. ⚖️ 对比不同论文的方法和结果\n\n \
🚨：请您提问前先进行检索';

// 获取节点文本内容（用来复制文本块中的内容）
const getNodeText = (node: any): string => {
  if (!node) return '';
  if (node.type === 'text') {
    return node.value || '';
  }
  if (Array.isArray(node.children)) {
    return node.children.map(getNodeText).join('');
  }
  return '';
};

// 新增：自定义 markdown 渲染组件，超宽内容横向滚动
const markdownComponents = {
  table: ({node, ...props}: any) => (
    <div className="overflow-x-auto max-w-full">
      <table style={{ tableLayout: 'fixed', width: '100%' }} {...props} />
    </div>
  ),
  td: ({node, ...props}: any) => (
    <td style={{ minWidth: '200px', whiteSpace: 'normal', wordBreak: 'break-word', padding: '8px' }} {...props} />
  ),
  th: ({node, ...props}: any) => (
    <th style={{ minWidth: '200px', whiteSpace: 'normal', wordBreak: 'break-word', padding: '8px' }} {...props} />
  ),
  // 针对 pre 标签做横向滚动
  pre: ({node, ...props}: any) => {
    const codeToCopy = getNodeText(node);
    return (
      <div className="overflow-x-auto max-w-full relative group">
        <div className="relative">
          <pre {...props} className="bg-gray-800 text-gray-100 dark:bg-gray-700 dark:text-gray-200 rounded-lg p-4" style={{ width: '100%', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }} />
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity h-6 px-2 text-xs font-medium bg-gray-700 hover:bg-gray-600 text-gray-200 hover:text-gray-50 dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-gray-300 dark:hover:text-gray-100 rounded"
            onClick={() => {
              if (!codeToCopy.trim()) {
                toast.error('无法获取代码内容');
                return;
              }
              navigator.clipboard.writeText(codeToCopy).then(() => {
                toast.success('代码已复制到剪贴板');
              }).catch(err => {
                console.error('复制失败:', err);
                toast.error('复制失败');
              });
            }}
          >
            复制
          </Button>
        </div>
      </div>
    );
  },
  // 针对 KaTeX block 公式（.katex-display）做横向滚动
  span: ({node, className, ...props}: any) => {
    if (className && className.includes('katex-display')) {
      return (
        <div className="overflow-x-auto max-w-[calc(100%-10px)]">
          <span className={className} {...props} style={{ 
            width: '100%',
            maxWidth: '100%',
            display: 'block',
            overflowX: 'scroll',
            overflowY: 'hidden',
            whiteSpace: 'nowrap',
            padding: '0.5rem 0'
          }}/>
        </div>
      );
    }
    return <span className={className} {...props} />;
  },
};

const MiniChatMessagesList: React.FC<MiniChatMessagesListProps> = ({
  messages,
  setMessages,
  toggleThinking,
}) => {
  // 内部 ref 管理
  const [scrollAreaEl, setScrollAreaEl] = useState<HTMLDivElement | null>(null);
  const thinkingContentRefs = useRef<(HTMLDivElement | null)[]>([]);
  const messageContentRefs = useRef<(HTMLDivElement | null)[]>([]);
  const lastUserMessageRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 新增：记录倒数第二条用户消息是否已被检测
  const hasScrolledForSecondLastUserMsg = useRef(false);

  // 新增：添加一个状态来跟踪要删除的消息
  const [confirmDeleteIndex, setConfirmDeleteIndex] = useState<number | null>(null);
  const [deleteDialogTop, setDeleteDialogTop] = useState<number | null>(null);
  const listContainerRef = useRef<HTMLDivElement>(null);
  const deleteBtnRefs = useRef<(HTMLButtonElement | null)[]>([]);

  // 清理引用数组
  useEffect(() => {
    thinkingContentRefs.current = thinkingContentRefs.current.slice(0, messages.length);
    messageContentRefs.current = messageContentRefs.current.slice(0, messages.length);
  }, [messages.length]);

  // 监听滚动区域 DOM 挂载
  useEffect(() => {
    if (!scrollAreaEl) {
      const el = document.querySelector('.h-full.px-3.py-2');
      if (el) {
        setScrollAreaEl(el as HTMLDivElement);
      }
    }
  }, [scrollAreaEl]);

  // 检测用户消息并滚动到顶部（只在特定条件下）
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      const secondLastMessage = messages.length > 1 ? messages[messages.length - 2] : null;

      if (lastMessage && lastMessage.role === 'user') {
        // 最新用户消息滚动到窗口最上方
        lastUserMessageRef.current?.scrollIntoView({ block: 'start', behavior: 'smooth' });
        hasScrolledForSecondLastUserMsg.current = false;
      } else if (
        secondLastMessage &&
        secondLastMessage.role === 'user' &&
        !hasScrolledForSecondLastUserMsg.current
      ) {
        lastUserMessageRef.current?.scrollIntoView({ block: 'start', behavior: 'smooth' });
        hasScrolledForSecondLastUserMsg.current = true;
      }
    }
  }, [messages]);

  // 添加欢迎消息
  useEffect(() => {
    setMessages([{
      role: 'assistant',
      content: INIT_MESSAGE
    }]);
  }, []);

  // 处理删除消息
  const handleDeleteMessage = (indexToDelete: number) => {
    setMessages(prevMessages => prevMessages.filter((_, index) => index !== indexToDelete));
  };

  // 处理复制消息
  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content).then(() => {
      // 可以添加一个toast提示用户复制成功
      toast.success('消息已复制到剪贴板');
    }).catch(err => {
      toast.error('复制失败'); // 使用toast组件显示错误消息
    });
  };

  // 关闭弹窗
  const closeDeleteDialog = () => {
    setConfirmDeleteIndex(null);
    setDeleteDialogTop(null);
  };

  // 监听窗口变化，自动关闭弹窗（防止位置错乱）
  useEffect(() => {
    if (confirmDeleteIndex !== null) {
      const handleResize = () => closeDeleteDialog();
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [confirmDeleteIndex]);

  return (
    <ScrollArea className="h-full px-3 py-2">
    <div className="space-y-3 pt-12 pb-24 relative" ref={listContainerRef}>
      {messages.map((message, index) => (
        <div key={index} className="group">
          <div
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            ref={message.role === 'user' && index === messages.length - 1 ? lastUserMessageRef : undefined}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 shadow-sm min-w-0 ${
                message.role === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted'
              }`}
            >
              {message.role === 'assistant' && (message.isThinking || message.thinking) && (
                <div className="mb-2">
                  {message.thinking ? (
                    <>
                      <Button 
                        variant={message.showThinking ? "secondary" : "outline"} 
                        size="sm" 
                        onClick={() => toggleThinking(index)}
                        className="h-auto py-1 px-2 flex items-center text-xs font-medium mb-2"
                      >
                        {message.showThinking ? 
                          <ChevronDown className="h-3 w-3 mr-1" /> : 
                          <ChevronRight className="h-3 w-3 mr-1" />
                        }
                        {message.isThinking ? "思考中..." : "查看思考过程"}
                      </Button>
                      {message.showThinking && (
                        <div 
                          className="bg-slate-100 dark:bg-slate-800 p-2 rounded text-sm mb-3 overflow-x-auto max-w-full"
                          ref={el => {
                            if (!thinkingContentRefs.current) {
                              thinkingContentRefs.current = [];
                            }
                            thinkingContentRefs.current[index] = el;
                          }}
                        >
                          <ReactMarkdown 
                            className="prose prose-sm dark:prose-invert max-w-full"
                            remarkPlugins={remarkPlugins}
                            rehypePlugins={rehypePlugins}
                            components={markdownComponents}
                          >
                            {message.thinking}
                          </ReactMarkdown>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="flex items-center">
                      <div className="animate-pulse mr-2 h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-sm text-muted-foreground">思考中...</span>
                    </div>
                  )}
                </div>
              )}
              {message.role === 'assistant' ? (
                <div
                  ref={el => {
                    if (!messageContentRefs.current) {
                      messageContentRefs.current = [];
                    }
                    messageContentRefs.current[index] = el;
                  }}
                >
                  <ReactMarkdown 
                    className="prose prose-sm dark:prose-invert max-w-full"
                    remarkPlugins={remarkPlugins}
                    rehypePlugins={rehypePlugins}
                    components={markdownComponents}
                  >
                    {message.content}
                  </ReactMarkdown>
                </div>
              ) : (
                message.content
              )}
            </div>
          </div>
          {/* 按钮区域紧跟气泡，间距用 mt-0.5 控制 */}
          <div className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} gap-0 mt-0.5`}>
            <Button
              variant="ghost"
              size="sm"
              ref={el => { deleteBtnRefs.current[index] = el; }}
              onClick={e => {
                setConfirmDeleteIndex(index);
                // 计算弹窗 top
                const btnRect = e.currentTarget.getBoundingClientRect();
                const containerRect = listContainerRef.current?.getBoundingClientRect();
                const scrollY = window.scrollY || window.pageYOffset;
                let top = btnRect.top - (containerRect?.top || 0) + btnRect.height/2 + scrollY;
                // 限制最小/最大边距
                const minMargin = 32;
                const dialogHeight = 110; // 预估弹窗高度
                const containerHeight = containerRect?.height || 0;
                if (top < minMargin) top = minMargin;
                if (top > containerHeight - dialogHeight - minMargin) top = containerHeight - dialogHeight - minMargin;
                setDeleteDialogTop(top);
              }}
              className="h-6 px-2 text-xs font-medium text-muted-foreground hover:text-destructive hover:bg-destructive/10 rounded"
            >
              删除
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleCopyMessage(message.content)}
              className="h-6 px-2 text-xs font-medium text-muted-foreground hover:text-primary hover:bg-primary/10 rounded"
            >
              复制
            </Button>
          </div>
        </div>
      ))}
      {/* 居中弹窗，确认是否删除，y 位置与按钮平齐，点击遮罩关闭 */}
      {confirmDeleteIndex !== null && deleteDialogTop !== null && (
        <>
          <div className="absolute inset-0 z-40" onClick={closeDeleteDialog} />
          <div
            className="absolute left-1/2 z-50 -translate-x-1/2"
            style={{ top: deleteDialogTop }}
          >
            <div className="min-w-[180px] bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded shadow-lg p-3 flex flex-col items-center animate-fade-in">
              <div className="text-sm mb-3 text-center text-slate-700 dark:text-slate-200">确定要删除这条消息吗？</div>
              <div className="flex gap-2 w-full justify-center">
                <Button
                  variant="destructive"
                  size="sm"
                  className="px-3 text-xs"
                  onClick={() => {
                    handleDeleteMessage(confirmDeleteIndex);
                    closeDeleteDialog();
                  }}
                >
                  确定
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  className="px-3 text-xs"
                  onClick={closeDeleteDialog}
                >
                  取消
                </Button>
              </div>
            </div>
          </div>
        </>
      )}
      {/* 添加一个占位元素，确保有足够的滚动空间 */}
      <div className="h-[calc(80vh-50px)]" />
      <div ref={messagesEndRef} />
    </div>
    </ScrollArea>
  );
};

export default MiniChatMessagesList;