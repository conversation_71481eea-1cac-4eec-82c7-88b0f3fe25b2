[{"primary_key": "1711147", "vector": [], "sparse_vector": [], "title": "MARISA: A Self-configuring Metasurfaces Absorption and Reflection Solution Towards 6G.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reconfigurable Intelligent Surfaces (RISs) are considered one of the key disruptive technologies towards future 6G networks. RISs revolutionize the traditional wireless communication paradigm by controlling the wave propagation properties of the impinging signals at will. A major roadblock for RIS is though the need for a fast and complex control channel to continuously adapt to the ever-changing wireless channel conditions. In this paper, we ask ourselves the question: Would it be feasible to remove the need for control channels for RISs? We analyze the feasibility of devising Self-Configuring Smart Surfaces that can be easily and seamlessly installed throughout the environment, following the new Internet-of-Surfaces (IoS) paradigm, without requiring modifications of the deployed mobile network. To this aim we design MARISA, a self-configuring metasurfaces absorption and reflection solution. Our results show that MARISA achieves outstanding performance, rivaling with state-of-the-art control channel-driven RISs solutions.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796976"}, {"primary_key": "1711151", "vector": [], "sparse_vector": [], "title": "Joint Superposition Coding and Training for Federated Learning over Multi-Width Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Won Joon Yun", "Yunseok Kwak", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jihong Park", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "This paper aims to integrate two synergetic technologies, federated learning (FL) and width-adjustable slimmable neural network (SNN) architectures. FL preserves data privacy by exchanging the locally trained models of mobile devices. By adopting SNNs as local models, FL can flexibly cope with the time-varying energy capacities of mobile devices. Combining FL and SNNs is however non-trivial, particularly under wireless connections with time-varying channel conditions. Furthermore, existing multi-width SNN training algorithms are sensitive to the data distributions across devices, so are ill-suited to FL. Motivated by this, we propose a communication and energy efficient SNN-based FL (named SlimFL) that jointly utilizes superposition coding (SC) for global model aggregation and superposition training (ST) for updating local models. By applying SC, SlimFL exchanges the superposition of multiple width configurations that are decoded as many as possible for a given communication throughput. Leveraging ST, SlimFL aligns the forward propagation of different width configurations, while avoiding the inter-width interference during back propagation. We formally prove the convergence of SlimFL. The result reveals that SlimFL is not only communication-efficient but also can counteract non-IID data distributions and poor channel conditions, which is also corroborated by simulations.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796733"}, {"primary_key": "1711153", "vector": [], "sparse_vector": [], "title": "ChARM: NextG Spectrum Sharing Through Data-Driven Real-Time O-RAN Dynamic Control.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Today's radio access networks (RANs) are monolithic entities which often operate statically on a given set of parameters for the entirety of their operations. To implement realistic and effective spectrum sharing policies, RANs will need to seamlessly and intelligently change their operational parameters. In stark contrast with existing paradigms, the new O-RAN architectures for 5G-and-beyond networks (NextG) separate the logic that controls the RAN from its hardware substrate, allowing unprecedented real-time fine-grained control of RAN components. In this context, we propose the Channel-Aware Reactive Mechanism (ChARM), a data-driven O-RAN-compliant framework that allows (i) sensing the spectrum to infer the presence of interference and (ii) reacting in real time by switching the distributed unit (DU) and radio unit (RU) operational parameters according to a specified spectrum access policy. ChARM is based on neural networks operating directly on unprocessed I/Q waveforms to determine the current spectrum context. ChARM does not require any modification to the existing 3GPP standards. It is designed to operate within the O-RAN specifications, and can be used in conjunction with other spectrum sharing mechanisms (e.g., LTE-U, LTE-LAA or MulteFire). We demonstrate the performance of ChARM in the context of spectrum sharing among LTE and Wi-Fi in unlicensed bands, where a controller operating over a RAN Intelligent Controller (RIC) senses the spectrum and switches cell frequency to avoid Wi-Fi. We develop a prototype of ChARM using srsRAN, and leverage the Colosseum channel emulator to collect a large-scale waveform dataset to train our neural networks with. To collect standard-compliant Wi-Fi data, we extended the Colosseum testbed using system-on-chip (SoC) boards running a modified version of the OpenWiFi architecture. Experimental results show that ChARM achieves accuracy of up to 96% on Colosseum and 85% on an over-the-air testbed, demonstrating the capacity of ChARMto exploit the considered spectrum channels.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796985"}, {"primary_key": "1711162", "vector": [], "sparse_vector": [], "title": "Midpoint Optimization for Segment Routing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we discuss the concept of Midpoint Optimization (MO) for Segment Routing (SR). It is based on the idea of integrating SR policies into the Interior Gateway Protocol (IGP) to allow various demands to be steered into them. We discuss the benefits of this approach when compared to end-to-end SR and potential challenges that might arise in deployment. We further develop an LP-based optimization algorithm to assess the Traffic Engineering capabilities of MO for SR. Based on traffic and topology data from a Tier-1 Internet Service Provider as well as other, publicly available data, we show that this algorithm is able to achieve virtually optimal results with regards to the maximum link utilization, that are on par with state-of-the-art end-to-end SR approaches. However, our MO approach requires substantially less policies to do so. For some instances the achieved reduction ranges up to more than 99%.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796747"}, {"primary_key": "1711165", "vector": [], "sparse_vector": [], "title": "Dyssect: Dynamic Scaling of Stateful Network Functions.", "authors": ["Fabrício B. Carvalho", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Network Function Virtualization promises better utilization of computational resources by dynamically scaling resources on demand. However, most network functions (NFs) are stateful and require state updates on a per-packet basis. During a scaling operation, cores need to synchronize access to a shared state to avoid race conditions and to guarantee that NFs process packets in arrival order. Unfortunately, the classic approach to control concurrent access to a shared state with locks does not scale to today's throughput and latency requirements. Moreover, network traffic is highly skewed, leading to load imbalances in systems that use only sharding to partition the NF states. To address these challenges, we present Dyssect, a system that enables dynamic scaling of stateful NFs by disaggregating the states of network functions. By carefully coordinating actions between cores and a central controller, Dyssect migrates shards and flows between cores for load balancing or traffic prioritization without resorting to locks or reordering packets. Our experimental evaluation shows that <PERSON><PERSON><PERSON> reduces tail latency up to 32% and increases throughput up to 19.36% when compared to state-of-the-art competing solutions.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796848"}, {"primary_key": "1711169", "vector": [], "sparse_vector": [], "title": "Joint Near-Optimal Age-based Data Transmission and Energy Replenishment Scheduling at Wireless-Powered Network Edge.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Hong Gao"], "summary": "Age of Information (AoI), emerged as a new metric to quantify the data freshness, has attracted increasing interests recently. Most existing works try to optimize the system AoI from the point of data transmission. Unfortunately, at wireless-powered network edge, the charging schedule of the source nodes also needs to be decided besides data transmission. Thus, in this paper, we investigate the joint scheduling problem of data transmission and energy replenishment to optimize the peak AoI at network edge with directional chargers. To the best of our knowledge, this is the first work that considers such two problems simultaneously. Firstly, the theoretical bounds of the peak AoI with respect to the charging latency are derived. Secondly, for the minimum peak AoI scheduling problem with a single charger, an optimal scheduling algorithm is proposed to minimize the charging latency, and then a data transmission scheduling strategy is also given to optimize the peak AoI. The proposed algorithm is proved to have a constant approximation ratio of up to 1.5. When there exist multiple chargers, an approximate algorithm is also proposed to minimize the charging latency and peak AoI. Finally, the simulation results verify the high performance of proposed algorithms in terms of AoI.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796808"}, {"primary_key": "1711170", "vector": [], "sparse_vector": [], "title": "ArmSpy: Video-assisted PIN Inference Leveraging Keystroke-induced Arm Posture Changes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Du", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hongbo Liu", "<PERSON><PERSON>", "Yanzhi Ren", "<PERSON><PERSON>"], "summary": "PIN inference attack leveraging keystroke-induced side-channel information poses a substantial threat to the security of people's privacy and properties. Among various PIN inference attacks, video-assisted method provide more intuitive and robust side-channel information to infer PINs. But it usually requires there is no visual occlusion between the attacker and the victims or their hand gestures, making the attackers either easy to expose themselves or inapplicable to the scenarios such as ATM or POS terminals. In this paper, we present a novel and practical video-assisted PIN inference system, ArmSpy, which infers victim's PIN by observing from behind the victims in a stealthy way. Specifically, ArmSpy explores the subtle keystroke-induced arm posture changes, including elbow bending angle changes and the spatial relationship between different arm joints, to infer the PIN entries. We develop the keystroke inference mechanism to detect the keystroke events and pinpoint the keystroke positions, and then accurately infer the PINs with the proposed inferred PIN coordination mechanism. Extensive experimental results demonstrate that ArmSpy can achieve over 67% average accuracy on inferring the PIN with 3 attempts and even over 80% for some victims, indicating the severity of the threat posed by <PERSON><PERSON><PERSON>.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796738"}, {"primary_key": "1711172", "vector": [], "sparse_vector": [], "title": "Big Brother is Listening: An Evaluation Framework on Ultrasonic Microphone Jammers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "Li Lu", "<PERSON>", "Jinsong Han", "<PERSON><PERSON>"], "summary": "Covert eavesdropping via microphones has always been a major threat to user privacy. Benefiting from the acoustic non-linearity property, the ultrasonic microphone jammer (UMJ) is effective in resisting this long-standing attack. However, prior UMJ researches underestimate adversary's attacking capability in reality and miss critical metrics for a thorough evaluation. The strong assumptions of adversary unable to retrieve information under low word recognition rate, and adversary's weak denoising abilities in the threat model make these works overlook the vulnerability of existing UMJs. As a result, their UMJs' resilience is overestimated. In this paper, we refine the adversary model and completely investigate potential eavesdropping threats. Correspondingly, we define a total of 12 metrics that are necessary for evaluating UMJs' resilience. Using these metrics, we propose a comprehensive framework to quantify UMJs' practical resilience. It fully covers three perspectives that prior works ignored in some degree, i.e., ambient information, semantic comprehension, and collaborative recognition. Guided by this framework, we can thoroughly and quantitatively evaluate the resilience of existing UMJs towards eavesdroppers. Our extensive assessment results reveal that most existing UMJs are vulnerable to sophisticated adverse approaches. We further outline the key factors influencing jammers' performance and present constructive suggestions for UMJs' future designs.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796834"}, {"primary_key": "1711173", "vector": [], "sparse_vector": [], "title": "M3: A Sub-Millisecond Scheduler for Multi-Cell MIMO Networks under C-RAN Architecture.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cloud Radio Access Network (C-RAN) is a novel centralized architecture for cellular networks. C-RAN can significantly improve spectrum efficiency by performing cooperative signal processing for multiple cells at a centralized baseband unit (BBU) pool. However, a new resource scheduler is needed before we can take advantage of C-RAN's multi-cell processing capability. Under C-RAN architecture, the scheduler must jointly determine RB allocation, MCS assignment, and beamforming matrices for all users under all covering cells. In addition, it is necessary to obtain a scheduling solution within each TTI (at most 1 ms) to be useful for the frame structure defined by 5G NR. In this paper, we present M 3 —a sub-ms scheduler for multi-cell MIMO networks under C-RAN architecture. M 3 addresses the stringent timing requirement through a novel multi-pipeline design that exploits parallelism. Under this design, one pipeline performs a sequence of operations for cell-edge users to explore joint transmission, and in parallel, the other pipeline is for cell-center users to explore MU-MIMO transmission. Experimental results show that M 3 is capable of offering a scheduling solution within 1 ms for 7 remote radio heads (RRHs), 100 users, 100 RBs, and 2×12 MIMO. Meanwhile, M 3 provides ~40%. throughput gain on average by employing joint transmission.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796829"}, {"primary_key": "1711175", "vector": [], "sparse_vector": [], "title": "VR Viewport Pose Model for Quantifying and Exploiting Frame Correlations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The importance of the dynamics of the viewport pose, i.e., the location and the orientation of users' points of view, for virtual reality (VR) experiences calls for the development of VR viewport pose models. In this paper, informed by our experimental measurements of viewport trajectories across 3 different types of VR interfaces, we first develop a statistical model of viewport poses in VR environments. Based on the developed model, we examine the correlations between pixels in VR frames that correspond to different viewport poses, and obtain an analytical expression for the visibility similarity (ViS) of the pixels across different VR frames. We then propose a lightweight ViS-based ALG-ViS algorithm that adaptively splits VR frames into the background and the foreground, reusing the background across different frames. Our implementation of ALG-ViS in two Oculus Quest 2 rendering systems demonstrates ALG-ViS running in real time, supporting the full VR frame rate, and outperforming baselines on measures of frame quality and bandwidth consumption.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796904"}, {"primary_key": "1711176", "vector": [], "sparse_vector": [], "title": "Towards Optimal Multi-Modal Federated Learning on Non-IID Data with Hierarchical Gradient Blending.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent advances in federated learning (FL) made it feasible to train a machine learning model across multiple clients, even with non-IID data distributions. In contrast to these uni-modal models that have been studied extensively in the literature, there are few in-depth studies on how multi-modal models can be trained effectively with federated learning. Unfortunately, we empirically observed a counter-intuitive phenomenon that, compared with its uni-modal counterpart, multi-modal FL leads to a significant degradation in performance.Our in-depth analysis of such a phenomenon shows that modality sub-networks and local models can overfit and generalize at different rates. To alleviate these inconsistencies in collaborative learning, we propose hierarchical gradient blending (HGB), which simultaneously computes the optimal blending of modalities and the optimal weighting of local models by adaptively measuring their overfitting and generalization behaviors. When HGB is applied, we present a few important theoretical insights and convergence guarantees for convex and smooth functions, and evaluate its performance in multi-modal FL. Our experimental results on an extensive array of non-IID multi-modal data have demonstrated that HGB is not only able to outperform the best uni-modal baselines but also to achieve superior accuracy and convergence speed as compared to state-of-the-art frameworks.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796724"}, {"primary_key": "1711177", "vector": [], "sparse_vector": [], "title": "PhoneyTalker: An Out-of-the-Box Toolkit for Adversarial Example Attack on Speaker Recognition.", "authors": ["<PERSON><PERSON>", "Li Lu", "Zhongjie Ba", "<PERSON><PERSON>"], "summary": "Voice has become a fundamental method for human-computer interactions and person identification these days. Benefit from the rapid development of deep learning, speaker recognition exploiting voice biometrics has achieved great success in various applications. However, the shadow of adversarial example attacks on deep neural network-based speaker recognition recently raised extensive public concerns and enormous research interests. Although existing studies propose to generate adversarial examples by iterative optimization to deceive speaker recognition, these methods require multiple iterations to construct specific perturbations for a single voice, which is input-specific, time-consuming, and non-transferable, hindering the deployment and application for non-professional adversaries. In this paper, we propose PhoneyTalker, an out-of-the-box toolkit for any adversary to generate universal and transferable adversarial examples with low complexity, releasing the requirement for professional background and specialized equipment. PhoneyTalker decomposes an arbitrary voice into phone combinations and generates phone-level perturbations using a generative model, which are reusable for voices from different persons with various texts. Experiments on mainstream speaker recognition systems with large-scale corpus show that PhoneyTalker outperforms state-of-the-art methods with overall attack success rates of 99.9% and 84.0% under white-box and black-box settings respectively.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796934"}, {"primary_key": "1711178", "vector": [], "sparse_vector": [], "title": "Torp: Full-Coverage and Low-Overhead Profiling of Host-Side Latency.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhou"], "summary": "In data center networks (DCNs), host-side packet processing accounts for a large portion of the end-to-end latency of TCP flows. Thus, the profiling of host-side latency anomalies has been considered as a crucial part in DCN performance diagnosis and troubleshooting. In particular, such profiling requires full coverage (i.e., profiling every TCP packet handled by end-hosts) and low overhead (i.e., profiling should avoid high CPU consumption in end-hosts). However, existing solutions fully rely on end-hosts to implement host-side latency profiling, leading to low coverage or high overhead. In this paper, we propose Torp, a framework that offers full-coverage and low-overhead profiling of host-side latency. Our key idea is to offload profiling operations to top-of-rack (ToR) switches, which inherently offer full coverage and line-rate packet processing performance. Specifically, Torp selectively offloads profiling operations to the ToR switch based on switch limitations. It efficiently coordinates the ToR switch and end-hosts to execute the entire latency profiling task. We have implemented Torp on 32×100Gbps Tofino switches. Testbed experiments indicate that Torp achieves full coverage and orders of magnitude lower host-side overhead compared to other solutions.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796758"}, {"primary_key": "1711179", "vector": [], "sparse_vector": [], "title": "Persistent Items Tracking in Large Data Streams Based on Adaptive Sampling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We address the problem of persistent item tracking in large-scale data streams. A persistent item refers to the one that persists to occur in the stream over a long timespan. Tracking persistent items is an important and pivotal functionality for many networking and computing applications as persistent items, though not necessarily contributing significantly to the data volume, may convey valuable information on the data pattern about the stream. The state-of-the-art solutions of tracking persistent items require to know the monitoring time horizon to set the sampling rate. This limitation is further accentuated when we need to track the persistent items in recent w slots where w can be any value between 0 and T to support different monitoring granularity. Motivated by this limitation, we develop a persistent item tracking algorithm that can function without knowing the monitoring time horizon beforehand, and can thus track persistent items up to the current time t or within a certain time window at any moment. Our central technicality is adaptively reducing the sampling rate such that the total memory overhead can be limited while still meeting the target tracking accuracy. Through both theoretical and empirical analysis, we fully characterize the performance of our proposition.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796709"}, {"primary_key": "1711180", "vector": [], "sparse_vector": [], "title": "Blockchain Based Non-repudiable IoT Data Trading: Simpler, Faster, and Cheaper.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tao <PERSON>", "<PERSON><PERSON>"], "summary": "Next-generation wireless technology and machine-to-machine technology can provide the ability to connect and share data at any time among IoT smart devices. However, the traditional centralized data sharing/trading mechanism lacks trust guarantee and cannot satisfy the real-time requirement. Distributed systems, especially blockchain, provide us with promising solutions. In this paper, we propose a blockchain based non-repudiation scheme for IoT data trading to resolve the credibility and real-time limits. The proposed scheme has two parts, i.e., a trading scheme and an arbitration scheme. The trading scheme employs a divide-and-conquer method and two commitment methods to support efficient IoT data trading, which runs in a two-round manner. The arbitration scheme first leverages a smart contract to solve disputes on-chain in real time. In case of on-chain arbitration dissatisfaction, the arbitration scheme also employs an off-line arbitration to make a final resolution. Short-term and long-term analysis show that the proposed scheme enforces non-repudiation among the data trading parties and runs efficiently for rational data owners and buyers. We implemented the proposed scheme. Experimental results confirm that the proposed scheme has an orders-of-magnitude performance speedup than the state-of-the-art scheme.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796857"}, {"primary_key": "1711181", "vector": [], "sparse_vector": [], "title": "Optimal Admission Control Mechanism Design for Time-Sensitive Services in Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Liu"], "summary": "Edge computing is a promising solution for reducing service latency by provisioning time-sensitive services directly from the network edge. However, upon workload peaks at the resource-limited edge, an edge service has to queue service requests, incurring high waiting time. Such quality of service (QoS) degradation ruins the reputation and reduces the long-term revenue of the service provider.To address this issue, we propose an admission control mechanism for time-sensitive edge services. Specifically, we allow the service provider to offer admission advice to arriving requests regarding whether to join for service or balk to seek alternatives. Our goal is twofold: maximizing revenue of the service provider and ensuring QoS if the provided admission advice is followed. To this end, we propose a threshold structure that estimates the highest length of the request queue. Leveraging such a threshold structure, we propose O2A, a mechanism to balance the trade-off between increasing revenue from accepting more requests and guaranteeing QoS by advising requests to balk. Rigorous analysis shows that O2A achieves the goal and that the provided admission advice is optimal for end-users to follow. We further validate O2A through trace-driven simulations with both synthetic and real-world service request traces.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796847"}, {"primary_key": "1711184", "vector": [], "sparse_vector": [], "title": "Learning for Crowdsourcing: Online Dispatch for Video Analytics with Guarantee.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Crowdsourcing enables a paradigm to conduct the manual annotation and the analytics by those recruited workers, with their rewards relevant to the quality of the results. Existing dispatchers fail to capture the resource-quality trade-off for video analytics, since the configurations supported by various workers are different, and the workers' availability is essentially dynamic. To determine the most suitable configurations as well as workers for video analytics, we formulate a non-linear mixed program in a long-term scope, maximizing the profit for the crowdsourcing platform. Based on previous results under various configurations and workers, we design an algorithm via a series of subproblems to decide the configurations adaptively upon the prediction of the worker rewards. Such prediction is based on volatile multi-armed bandit to capture the workers' availability and stochastic changes on resource uses. Via rigorous proof, the regret is ensured upon the <PERSON><PERSON><PERSON>nov optimization and the bandit, measuring the gap between the online decisions and the offline optimum. Extensive trace-driven experiments show that our algorithm improves the platform profit by 37%, compared with other algorithms.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796960"}, {"primary_key": "1711185", "vector": [], "sparse_vector": [], "title": "Jingwei: An Efficient and Adaptable Data Migration Strategy for Deduplicated Storage Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The traditional migration methods are confronted with formidable challenges when data deduplication technologies are incorporated. Firstly, the deduplication creates data-sharing dependencies in the stored files; breaking such dependencies in migration would attach extra space overhead. Secondly, the redundancy elimination heightens the risk of data unavailability during server crashes. The existing methods fail to tackle them at one shot. To this end, we propose <PERSON><PERSON>, an efficient and adaptable data migration strategy for deduplicated storage systems. To be specific, <PERSON><PERSON> tries to minimize the extra space cost in migration for space efficiency. Meanwhile, <PERSON><PERSON> realizes the service adaptability by encouraging replicas of hot data to spread out their data access requirements. We first model such a problem as an integer linear programming (ILP) and solve it with a commercial solver when only one empty migration target server is allowed. We then extend this problem to a scenario wherein multiple non-empty target servers are available for migration. We solve it by effective heuristic algorithms based on the Bloom Filter-based data sketches. Trace-driven experiments show that <PERSON><PERSON> fortifies the file replicas by 25%, while only 5.7% of the extra storage space is occupied compared with the latest \"Goseed\" method.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796954"}, {"primary_key": "1711189", "vector": [], "sparse_vector": [], "title": "LossLeaP: Learning to Predict for Intent-Based Networking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Intent-Based Networking mandates that high-level human-understandable intents are automatically interpreted and implemented by network management entities. As a key part in this process, it is required that network orchestrators activate the correct automated decision model to meet the intent objective. In anticipatory networking tasks, this requirement maps to identifying and deploying a tailored prediction model that can produce a forecast aligned with the specific –and typically complex– network management goal expressed by the original intent. Current forecasting models for network demands or network management optimize generic, non-flexible, and manually designed objectives, hence do not fulfil the needs of anticipatory Intent-Based Networking. To close this gap, we propose LossLeaP, a novel forecasting model that can autonomously learn the relationship between the prediction and the target management objective, steering the former to minimize the latter. To this end, LossLeaP adopts an original deep learning architecture that advances current efforts in automated machine learning, towards a spontaneous design of loss functions for regression tasks. Extensive experiments in controlled environments and in practical application case studies prove that LossLeaP outperforms a wide range of benchmarks, including state-of-the-art solutions for network capacity forecasting.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796918"}, {"primary_key": "1711190", "vector": [], "sparse_vector": [], "title": "Optimal Rate Adaption in Federated Learning with Compressed Communications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated Learning (FL) incurs high communication overhead, which can be greatly alleviated by compression for model updates. Yet the tradeoff between compression and model accuracy in the networked environment remains unclear and, for simplicity, most implementations adopt a fixed compression rate only. In this paper, we for the first time systematically examine this tradeoff, identifying the influence of the compression error on the final model accuracy with respect to the learning rate. Specifically, we factor the compression error of each global iteration into the convergence rate analysis under both strongly convex and non-convex loss functions. We then present an adaptation framework to maximize the final model accuracy by strategically adjusting the compression rate in each iteration. We have discussed the key implementation issues of our framework in practical networks with representative compression algorithms. Experiments over the popular MNIST and CIFAR-10 datasets confirm that our solution effectively reduces network traffic yet maintains high model accuracy in FL.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796982"}, {"primary_key": "1711191", "vector": [], "sparse_vector": [], "title": "Mag-E4E: Trade Efficiency for Energy in Magnetic MIMO Wireless Power Transfer System.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Magnetic resonant coupling (MRC) wireless power transfer (WPT) is a convenient and potential power supply solution for smart devices. The scheduling problem in the multiple-input multiple-output (MIMO) scenarios is essential to concentrate energy at the receiver (RX) side. Meanwhile, strong TX-RX coupling could ensure better power transfer efficiency (PTE), but may cause lower power delivered to load (PDL) when transmitter voltages are bounded. In this paper, we propose the frequency adjustment based PDL maximization scheme for MIMO MRC-WPT systems. We formulate such joint optimization problem and decouple it into two sub-problems, i.e., high-level frequency adjustment and low-level voltage adaptation. We solve these two sub-problems with gradient descent based and alternating direction method of multipliers (ADMM) based algorithms, respectively. We further design an energy-voltage transform matrix algebra based estimation mechanism to reduce context measurement overhead. We prototype the proposed system, and conduct extensive experiments to evaluate its performance. As compared with the PTE maximization solutions, our system trades smaller efficiency for larger energy, i.e., 361% PDL improvement with respect to 26% PTE losses when TX-RX distance is 10cm.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796771"}, {"primary_key": "1711193", "vector": [], "sparse_vector": [], "title": "OrchestRAN: Network Automation through Orchestrated Intelligence in the Open RAN.", "authors": ["Salvatore D&apos;Oro", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The next generation of cellular networks will be characterized by softwarized, open, and disaggregated architectures exposing analytics and control knobs to enable network intelligence via innovative data-driven algorithms. How to practically realize this vision, however, is largely an open problem. For a given network optimization/automation objective, it is currently unknown how to select which data-driven models should be deployed and where, which parameters to control, and how to feed them appropriate inputs. In this paper, we take a decisive step forward by presenting and prototyping OrchestRAN, a novel orchestration framework for next generation systems that embraces and builds upon the Open Radio Access Network (RAN) paradigm to provide a practical solution to these challenges. OrchestRAN has been designed to execute in the non-Real-time (RT) RAN Intelligent Controller (RIC) and allows Network Operators (NOs) to specify high-level control/inference objectives (i.e., adapt scheduling, and forecast capacity in near-RT, e.g., for a set of base stations in Downtown New York). OrchestRAN automatically computes the optimal set of data-driven algorithms and their execution location (e.g., in the cloud, or at the edge) to achieve intents specified by the NOs while meeting the desired timing requirements and avoiding conflicts between different data-driven algorithms controlling the same parameters set. We show that the intelligence orchestration problem in Open RAN is NP-hard, and design low-complexity solutions to support real-world applications. We prototype OrchestRAN and test it at scale on Colosseum, the world's largest wireless network emulator with hardware in the loop. Our experimental results on a network with 7 base stations and 42 users demonstrate that OrchestRAN is able to instantiate data-driven services on demand with minimal control overhead and latency.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796744"}, {"primary_key": "1711194", "vector": [], "sparse_vector": [], "title": "Switching Gaussian Mixture Variational RNN for Anomaly Detection of Diverse CDN Websites.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "To conduct service quality management of industry devices or Internet infrastructures, various deep learning approaches have been used for extracting the normal patterns of multivariate Key Performance Indicators (KPIs) for unsupervised anomaly detection. However, in the scenario of Content Delivery Networks (CDN), KPIs that belong to diverse websites usually exhibit various structures at different timesteps and show the non-stationary sequential relationship between them, which is extremely difficult for the existing deep learning approaches to characterize and identify anomalies. To address this issue, we propose a switching Gaussian mixture variational recurrent neural network (SGmVRNN) suitable for multivariate CDN KPIs. Specifically, SGmVRNN introduces the variational recurrent structure and assigns its latent variables into a mixture Gaussian distribution to model complex KPI time series and capture the diversely structural and dynamical characteristics within them, while in the next step it incorporates a switching mechanism to characterize these diversities, thus learning richer representations of KPIs. For efficient inference, we develop an upward-downward autoencoding inference method which combines the bottom-up likelihood and up-bottom prior information of the parameters for accurate posterior approximation. Extensive experiments on real-world data show that SGmVRNN significantly outperforms the state-of-the-art approaches according to F1-score on CDN KPIs from diverse websites.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796836"}, {"primary_key": "1711195", "vector": [], "sparse_vector": [], "title": "AoI-minimal UAV Crowdsensing by Model-based Graph Convolutional Reinforcement Learning.", "authors": ["<PERSON>ipeng <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Mobile Crowdsensing (MCS) with smart devices has become an appealing paradigm for urban sensing. With the development of 5G-and-beyond technologies, unmanned aerial vehicles (UAVs) become possible for real-time applications, including wireless coverage, search and even disaster response. In this paper, we consider to use a group of UAVs as aerial base stations (BSs) to move around and collect data from multiple MCS users, forming a UAV crowdsensing campaign (UCS). Our goal is to maximize the collected data, geographical coverage whiling minimizing the age-of-information (AoI) of all mobile users simultaneously, with efficient use of constrained energy reserve. We propose a model-based deep reinforcement learning (DRL) framework called \"GCRL-min(AoI)\", which mainly consists of a novel model-based Monte Carlo tree search (MCTS) structure based on state-of-the-art approach MCTS (AlphaZero). We further improve it by adding a spatial UAV-user correlation extraction mechanism by a relational graph convolutional network (RGCN), and a next state prediction module to reduce the dependance of experience data. Extensive results and trajectory visualization on three real human mobility datasets in Purdue University, KAIST and NCSU show that GCRL-min(AoI) consistently outperforms five baselines, when varying different number of UAVs and maximum coupling loss in terms of four metrics.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796732"}, {"primary_key": "1711200", "vector": [], "sparse_vector": [], "title": "Optimal Pricing Under Vertical and Horizontal Interaction Structures for IoT Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An Internet of Things (IoT) system can include several different types of service providers, who sell IoT service, network service, and computation service to customers, either jointly or separately. The complicated coupling among these providers in terms of pricing and service decisions is an under-explored research area, the understanding of which is critical to the success of IoT networks. This paper studies the impact of the provider interaction structures on the overall IoT system with massive heterogeneous customers. Specifically, we consider three interaction structures: coordinated, vertically-uncoordinated, and horizontally-uncoordinated structures. Despite the challenging non-convex optimization problems involved in modeling and analyzing these structures, we successfully obtain the closed-form optimal pricing strategies of providers in each interaction structure. We prove that the coordinated structure is better than two uncoordinated structures for both providers and customers, as it avoids selfish price markup behaviors in uncoordinated structures. When customers' demand variance is large and utility-cost ratio is medium, vertically-uncoordinated structure is better than horizontal one for both providers and customers, due to the complementary providers' competition in horizontally-uncoordinated structure. Counter-intuitively, we identify that providers' optimal prices do not change with their costs at the critical point of customers' full participation in the vertically-uncoordinated structure.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796826"}, {"primary_key": "1711201", "vector": [], "sparse_vector": [], "title": "The Information Velocity of Packet-Erasure Links.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of in-order packet transmission over a cascade of packet-erasure links with acknowledgment (ACK) signals, interconnected by relays. We treat first the case of transmitting a single packet, in which ACKs are unnecessary, over links with independent identically distributed erasures. For this case, we derive tight upper and lower bounds on the probability of arrive failure within an allowed end-to-end communication delay over a given number of links. When the number of links is commensurate with the allowed delay, we determine the maximal ratio between the two—coined information velocity—for which the arrive-failure probability decays to zero; we further derive bounds on the arrive-failure probability when the ratio is below the information velocity, determine the exponential arrive-failure decay rate, and extend the treatment to links with different erasure probabilities. We then elevate all these results for a stream of packets with independent geometrically distributed interarrival times, and prove that the information velocity and the exponential decay rate remain the same for any stationary ergodic arrival process and for deterministic interarrival times. We demonstrate the significance of the derived fundamental limits—the information velocity and the arrive-failure exponential decay rate—by comparing them to simulation results.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796916"}, {"primary_key": "1711202", "vector": [], "sparse_vector": [], "title": "Real-Time Execution of Trigger-Action Connection for Home Internet-of-Things.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Daoming Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "IFTTT is a programming framework for Applets (i.e., user customized policies with a \"trigger-action\" syntax), and is the most popular Home Internet-of-Things (H-IoT) platform. The execution of an Applet prompted by a device operation suffers from a long delay, since IFTTT has to periodically reads the states of the device to determine whether the trigger is satisfied, with an interval of up to 5min for professionals and 60min for normal users. Although IFTTT sets up a flexible polling interval based on the past several times an Applet has run, the delay is still around 2min even for frequently executed Applets. This paper proposes a novel trigger notification mechanism \"RTX-IFTTT\" to implement real-time execution of Applets. The mechanism does not require any changes to the current IFTTT framework or the H-IoT devices, but only requires an H-IoT edge node (e.g., router) to identify the device events (e.g., turning on/off) and notify IFTTT to perform the action of an Applet when an identified event is the trigger of that Applet. The experimental results show that the averaged Applet execution delay for RTX-IFTTT is only about 2sec.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796872"}, {"primary_key": "1711203", "vector": [], "sparse_vector": [], "title": "Short-Term Memory Sampling for Spread Measurement in High-Speed Networks.", "authors": ["<PERSON>", "<PERSON>", "Yu<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Per-flow spread measurement in high-speed networks can provide indispensable information to many practical applications. However, it is challenging to measure millions of flows at line speed because on-chip memory modules cannot simultaneously provide large capacity and large bandwidth. The prior studies address this mismatch by entirely using on-chip compact data structures or utilizing off-chip space to assist limited on-chip memory. Nevertheless, their on-chip data structures record massive transient elements, each of which only appears in a short time interval in a long-period measurement task, and thus waste significant on-chip space. This paper presents short-term memory sampling, a novel spread estimator that samples new elements while only holding elements for short periods. Our estimator can work with tiny on-chip space and provide accurate estimations for online queries. The key of our design is a short-term memory duplicate filter that reports new elements and filters duplicates effectively while allowing incoming elements to override the stale elements to reduce on-chip memory usage. We implement our approach on a NetFPGA-equipped prototype. Experimental results based on real Internet traces show that, compared to the state-of-the-art, short-term memory sampling reduces up to 99% of on-chip memory usage when providing the same probabilistic assurance on spread-estimation error.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796702"}, {"primary_key": "1711205", "vector": [], "sparse_vector": [], "title": "Mercury: A Simple Transport Layer Scheduler to Accelerate Distributed DNN Training.", "authors": ["Qing<PERSON> Duan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Communication scheduling is crucial to improve the efficiency of training large deep learning models with data parallelism, in which the transmission order of layer-wise deep neural network (DNN) tensors is determined for a better computation-communication overlap. Prior approaches adopt tensor partitioning to enhance the priority scheduling with finer granularity. However, a startup time slot inserted before each tensor partition will neutralize this scheduling gain. Tuning the optimal partition size is difficult and the application-layer solutions cannot eliminate the partitioning overhead. In this paper, we propose Mercury, a simple transport layer scheduler that does not partition the tensors, but moves the priority scheduling to the transport layer at the packet granularity. The packets with the highest priority in the Mercury buffer will be transmitted first. Mercury achieves the near-optimal overlapping between communication and computation. It leverages immediate aggregation at the transport layer to enable the coincident gradient push and parameter pull. We implement <PERSON> in MXNet and conduct comprehensive experiments on five DNN models in an 8-node cluster with 10Gbps Ethernet. Experimental results show that Mercury can achieve about 1.18 ~ 2.18 × speedup over vanilla MXNet, and 1.08 ~ 2.04× speedup over the state-of-the-art tensor partitioning solution.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796820"}, {"primary_key": "1711206", "vector": [], "sparse_vector": [], "title": "Stream Iterative Distributed Coded Computing for Learning Applications in Heterogeneous Systems.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "To improve the utility of learning applications and render machine learning solutions feasible for complex applications, a substantial amount of heavy computations is needed. Thus, it is essential to delegate the computations among several workers, which brings up the major challenge of coping with delays and failures caused by the system's heterogeneity and uncertainties. In particular, minimizing the end-to-end job in-order execution delay, from arrival to delivery, is of great importance for real-world delay-sensitive applications. In this paper, for computation of each job iteration in a stochastic heterogeneous distributed system where the workers vary in their computing and communicating powers, we present a novel joint scheduling-coding framework that optimally split the coded computational load among the workers. This closes the gap between the workers' response time, and is critical to maximize the resource utilization. To further reduce the in-order execution delay, we also incorporate redundant computations in each iteration of a distributed computational job. Our simulation results demonstrate that the delay obtained using the proposed solution is dramatically lower than the uniform split which is oblivious to the system's heterogeneity and, in fact, is very close to an ideal lower bound just by introducing a small percentage of redundant computations.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796977"}, {"primary_key": "1711207", "vector": [], "sparse_vector": [], "title": "Joint Order Dispatch and Charging for Electric Self-Driving Taxi Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>ming Jin", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lu <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Nowadays, the rapid development of self-driving technology and its fusion with the current vehicle electrification process has given rise to electric self-driving taxis (es-taxis). Foreseeably, es-taxis will become a major force that serves the massive urban mobility demands not far into the future. Though promising, it is still a fundamental unsolved problem of effectively deciding when and where a city-scale fleet of es-taxis should be charged, so that enough es-taxis will be available whenever and wherever ride requests are submitted. Furthermore, charging decisions are far from isolated, but tightly coupled with the order dispatch process that matches orders with es-taxis. Therefore, in this paper, we investigate the problem of joint order dispatch and charging in es-taxi systems, with the objective of maximizing the ride-hailing platform's long-term cumulative profit. Technically, such problem is challenging in a myriad of aspects, such as long-term profit maximization, partial statistical information on future orders, etc. We address the various arising challenges by meticulously integrating a series of methods, including distributionally robust optimization, primal-dual transformation, and second order conic programming to yield far-sighted decisions. Finally, we validate the effectiveness of our proposed methods though extensive experiments based on two large-scale real-world online ride-hailing order datasets.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796825"}, {"primary_key": "1711211", "vector": [], "sparse_vector": [], "title": "Opportunistic Routing in Quantum Networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Unlike classical routing algorithms, quantum routing algorithms make use of entangled states—a type of resources that have a limited lifetime and need to be regenerated after consumption. In a nutshell, quantum routing algorithms have to use these resources efficiently, while optimizing some objectives such as the total waiting time. Current routing algorithms tend to keep a routing request waiting until all of the resources on its path are available. In this paper, we introduce a new way of managing entanglement resources in an opportunistic fashion: a request can move forward along its path as soon as possible (even if some resources on its path are not ready). We show that this opportunistic approach is fundamentally better than conventional approaches. In particular, our results indicate that this new approach achieves a 30-50% improvement in the average total waiting time and average link waiting time compared with several state-of-the-art routing algorithms. As a by-product of this work, we develop a new simulator for quantum routing, which can be used to evaluate various design choices under different scenarios.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796816"}, {"primary_key": "1711212", "vector": [], "sparse_vector": [], "title": "Lazy Self-Adjusting Bounded-Degree Networks for the Matching Model.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Self-adjusting networks (SANs) utilize novel optical switching technologies to support dynamic physical network topology reconfiguration. SANs rely on online algorithms to exploit this topological flexibility to reduce the cost of serving network traffic, leveraging locality in the demand. While prior work has shown the potential of SANs, the theoretical guarantees rely on a simplified cost model in which traversing and adjusting a single link has uniform cost.We initiate the study of online algorithms for SANs in a more realistic cost model, the Matching Model (MM), in which the network topology is given by the union of a constant number of bipartite matchings (realized by optical switches), and in which changing an entire matching incurs a fixed cost α. The cost of routing is given by the number of hops packets need to traverse.Our main result is a lazy topology adjustment method for designing efficient online SAN algorithms in the MM. We design and analyze online SAN algorithms for line, tree, and bounded degree networks in the MM, with cost ${\\mathcal{O}}(\\sqrt \\alpha )$ times the cost of reference algorithms in the uniform cost model. We report on empirical results considering publicly available datacenter network traces, that verify the theoretical bounds.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796885"}, {"primary_key": "1711215", "vector": [], "sparse_vector": [], "title": "Accelerating Deep Learning Classification with Error-controlled Approximate-key Caching.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While Deep Learning (DL) technologies are a promising tool to solve networking problems that map to classification tasks, their computational complexity is still too high with respect to real-time traffic measurements requirements. To reduce the DL inference cost, we propose a novel caching paradigm, that we named approximate-key caching, which returns approximate results for lookups of selected input based on cached DL inference results. While approximate cache hits alleviate DL inference workload and increase the system throughput, they however introduce an approximation error. As such, we couple approximate-key caching with an error-correction principled algorithm, that we named auto-refresh. We analytically model our caching system performance for classic LRU and ideal caches, we perform a trace-driven evaluation of the expected performance, and we compare the benefits of our proposed approach with the state-of-the-art similarity caching – this testifies the practical interest of our proposal.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796677"}, {"primary_key": "1711216", "vector": [], "sparse_vector": [], "title": "Optimal Routing for Stream Learning Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Consider a stream learning system with a source and a set of computation nodes that solves a machine learning task modeled as stochastic convex optimization problem over an unknown distribution D. The source generates i.i.d. data points from D and routes the data points to the computation nodes for processing. The data points are processed in a streaming fashion, i.e., each data point can be accessed only once and is discarded after processing. The system employs local stochastic gradient descent (local SGD), where each computation node performs stochastic gradient descent locally using the data it receives from the source and periodically synchronizes with other computation nodes. Since the routing policy of the source determines the availability of data points at each computation node, the performance of the system, i.e., the optimization error obtained by local SGD, depends on the routing policy.In this paper, we study the influence of the routing policy on the performance of stream learning systems. We first derive an upper bound on the optimization error as a function of the routing policy. The upper bound reveals that the routing policy influences the performance through tuning the bias-variance trade-off of the optimization process, and gives rise to a framework for optimizing the routing policy for stream learning systems. By minimizing the upper bound, we propose an optimal static routing policy that achieves the best trade-off for stream learning systems with deterministic data generation process. We then propose a routing policy that can approximate the optimal static routing policy arbitrarily closely for systems where the data points are generated according to a stochastic process with unknown rate. Finally, we conduct simulations using Support Vector Machine as the machine learning task on a real data set, and show that the optimal static routing policy has excellent empirical performance in terms of minimizing the optimization error and the proposed stochastic routing policy closely matches the optimal static routing policy.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796959"}, {"primary_key": "1711217", "vector": [], "sparse_vector": [], "title": "Kalmia: A Heterogeneous QoS-aware Scheduling Framework for DNN Tasks on Edge Servers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Motivated by the popularity of edge intelligence, DNN services have been widely deployed at the edge, posing significant performance pressure on edge servers. How to improve the QoS of edge DNN services becomes a crucial and challenging problem. Previous works, however, did not fully consider the heterogeneous QoS requirements on urgent and non-urgent tasks, causing frequent QoS violations. Meanwhile, our empirical study shows that severe task interference exists in concurrent DNN tasks, further degrading the timeliness of urgent tasks and throughput of non-urgent tasks. To address these issues, we propose Kalmia, a heterogeneous QoS-aware framework for DNN inference task scheduling on edge servers. Specifically, <PERSON><PERSON><PERSON> includes an offline profiling stage and an online scheduling policy. In offline profiling, we build a regression model to predict the execution time of tasks. During online scheduling, we classify the tasks into urgent and non-urgent tasks and distribute them into two CUDA contexts. By a tailored scheduling strategy, non-urgent tasks can fully utilize the computing resources for throughput improvement, while the timeliness of urgent tasks can be guaranteed via preemption. Experimental results demonstrate that <PERSON><PERSON><PERSON> can achieve up to 2.8× improvement in throughput and significantly reduce the deadline violation rate compared with state-of-the-art methods.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796661"}, {"primary_key": "1711220", "vector": [], "sparse_vector": [], "title": "RouteNet-Erlang: A Graph Neural Network for Network Performance Evaluation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Xiang Shi", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON><PERSON><PERSON><PERSON>"], "summary": "Network modeling is a fundamental tool in network research, design, and operation. Arguably the most popular method for modeling is Queuing Theory (QT). Its main limitation is that it imposes strong assumptions on the packet arrival process, which typically do not hold in real networks. In the field of Deep Learning, Graph Neural Networks (GNN) have emerged as a new technique to build data-driven models that can learn complex and non-linear behavior. In this paper, we present RouteNet-Erlang, a pioneering GNN architecture designed to model computer networks. RouteNet-Erlang supports complex traffic models, multi-queue scheduling policies, routing policies and can provide accurate estimates in networks not seen in the training phase. We benchmark RouteNet-Erlang against a state-of-the-art QT model, and our results show that it outperforms QT in all the network scenarios.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796944"}, {"primary_key": "1711222", "vector": [], "sparse_vector": [], "title": "InertiEAR: Automatic and Device-independent IMU-based Eavesdropping on Smartphones.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zhongjie Ba", "<PERSON><PERSON>", "Jinsong Han"], "summary": "IMU-based eavesdropping has brought growing concerns over smartphone users' privacy. In such attacks, adversaries utilize IMUs that require zero permissions for access to acquire speeches. A common countermeasure is to limit sampling rates (within 200 Hz) to reduce overlap of vocal fundamental bands (85-255 Hz) and inertial measurements (0-100 Hz). Nevertheless, we experimentally observe that IMUs sampling below 200 Hz still record adequate speech-related information because of aliasing distortions. Accordingly, we propose a practical side-channel attack, InertiEAR, to break the defense of sampling rate restriction on the zero-permission eavesdropping. It leverages IMUs to eavesdrop on both top and bottom speakers in smartphones. In the InertiEAR design, we exploit coherence between responses of the built-in accelerometer and gyroscope and their hardware diversity using a mathematical model. The coherence allows precise segmentation without manual assistance. We also mitigate the impact of hardware diversity and achieve better device-independent performance than existing approaches that have to massively increase training data from different smartphones for a scalable network model. These two advantages re-enable zero-permission attacks but also extend the attacking surface and endangering degree to off-the-shelf smartphones. InertiEAR achieves a recognition accuracy of 78.8% with a cross-device accuracy of up to 49.8% among 12 smartphones.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796890"}, {"primary_key": "1711224", "vector": [], "sparse_vector": [], "title": "Network Synthesis under Delay Constraints: The Power of Network Calculus Differentiability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the advent of standards for deterministic network behavior, synthesizing network designs under delay constraints becomes the natural next task to tackle. Network Calculus (NC) has become a key method for validating industrial networks, as it computes formally verified end-to-end delay bounds. However, analyses from the NC framework were thus far designed to bound one flow's delay at a time. Attempts to use classical analyses for derivation of a network configuration revealed this approach to be poorly fitted for practical use cases. Take finding a delay-optimal routing configuration: One model for each routing alternative had to be created, then each flow delay had to be bounded, then the bounds were compared to the given constraints. To overcome this three-step procedure, we introduce Differential Network Calculus. We extend NC to allow for differentiation of delay bounds w.r.t. to a wide range of network parameters – such as flow routes. This opens up NC to a class of efficient nonlinear optimization techniques taking advantage of the delay bound computation's gradient. Our numerical evaluation on the routing problem shows that our novel method can synthesize flow path in a matter of seconds, outperforming existing methods by multiple orders of magnitude.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796777"}, {"primary_key": "1711227", "vector": [], "sparse_vector": [], "title": "Layer-aware Collaborative Microservice Deployment toward Maximal Edge Throughput.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Lightweight container-based microservice has been widely advocated to promote the elasticity of edge cloud. The inherent layered structure of containers offers a compelling way to cope with the resource scarcity of edge servers through layer sharing, which can significantly increase storage utilization and improve the edge throughput. Recent studies show that it is possible to share layers not only within the same server but also between servers, which microservice deployment can take full advantage of. In this paper, we investigate the problem of how to collaboratively deploy microservices by incorporating both intra-server and inter-server layer sharing to maximize the edge throughput. We formulate this problem into an integer linear programming form and prove it as NP-hard. We propose a randomized rounding based heuristic algorithm, and conduct formal analysis on the guaranteed approximation ratio. Through extensive experiments, we verify the efficiency of our proposed algorithm, and the results demonstrate that it can deploy 6× and 12× more microservice instances, and improve the edge throughput by 27.74% and 38.46% in comparison with state-of-the-art strategies.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796670"}, {"primary_key": "1711232", "vector": [], "sparse_vector": [], "title": "A Theory of Second-Order Wireless Network Optimization and Its Application on AoI.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper introduces a new theoretical framework for optimizing second-order behaviors of wireless networks. Unlike existing techniques for network utility maximization, which only consider first-order statistics, this framework models every random process by its mean and temporal variance. The inclusion of temporal variance makes this framework well-suited for modeling stateful fading wireless channels and emerging network performance metrics such as age-of-information (AoI). Using this framework, we sharply characterize the second-order capacity region of wireless access networks. We also propose a simple scheduling policy and prove that it can achieve every interior point in the second-order capacity region. To demonstrate the utility of this framework, we apply it for an important open problem: the optimization of AoI over Gilbert-Elliott channels. We show that this framework provides a very accurate characterization of AoI. Moreover, it leads to a tractable scheduling policy that outperforms other existing work.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796686"}, {"primary_key": "1711233", "vector": [], "sparse_vector": [], "title": "Mudra: A Multi-Modal Smartwatch Interactive System with Hand Gesture Recognition and User Identification.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Yusheng Ji", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The great popularity of smartwatches leads to a growing demand for smarter interactive systems. Hand gesture is suitable for interaction due to its unique features. However, the existing single-modal gesture interactive systems have different biases in diverse scenarios, which makes it intractable to be applied in real life. In this paper, we propose a multi-modal smartwatch interactive system named Mudra, which fuses vision and Inertial Measurement Unit (IMU) signals to recognize and identify hand gestures for convenient and robust interaction. We carefully design a parallel attention multi-task model for different modals, and fuse classification results at the decision level with an adaptive weight adjustment algorithm. We implement a prototype of Mudra and collect data from 25 volunteers to evaluate its effectiveness. Extensive experiments demonstrate that Mudra can achieve 95.4% and 92.3% F1-scores on recognition and identification tasks, respectively. Meanwhile, Mudra can maintain stability and robustness under different experimental settings.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796879"}, {"primary_key": "1711234", "vector": [], "sparse_vector": [], "title": "Online Learning-Based Rate Selection for Wireless Interactive Panoramic Scene Delivery.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Interactive panoramic scene delivery not only consumes 4∼6× more bandwidth than traditional video streaming of the same resolution but also requires timely displaying the delivered content to ensure smooth interaction. Since users can only see roughly 20% of the entire scene at a time (called the viewport), it is sufficient to deliver the relevant portion of the panoramic scene if we can accurately predict the user's motion. It is customary to deliver a portion larger than the viewport to tolerate inaccurate predictions. Intuitively, the larger the delivered portion, the higher the prediction accuracy and lower the wireless transmission success probability. The goal is to select an appropriate delivery portion to maximize system throughput. We formulate this problem as a multi-armed bandit problem and use the classical Kullback-Leibler Upper Confidence Bound (KL-UCB) algorithm for the portion selection. We further develop a novel variant of the KL-UCB algorithm that effectively leverages two-level feedback (i.e., both prediction and transmission outcomes) after each decision on the selected portion and show its asymptotical optimality, which may be of independent interest by itself. We demonstrate the superior performance of our proposed algorithms over existing heuristic methods using both synthetic simulations and real experimental evaluations.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796965"}, {"primary_key": "1711238", "vector": [], "sparse_vector": [], "title": "EdgeTuner: Fast Scheduling Algorithm Tuning for Dynamic Edge-Cloud Workloads and Resources.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Edge-cloud jobs are rapidly prevailing in many application domains, posing the challenge of using both resource-strenuous edge devices and elastic cloud resources. Efficient resource allocation on such jobs via scheduling algorithms is essential to guarantee their performance, e.g. latency. Deep reinforcement learning (DRL) is increasingly adopted to make scheduling decisions but faces the conundrum of achieving high rewards at a low training overhead. It is unknown if such a DRL can be applied to timely tune the scheduling algorithms that are adopted in response to fast changing workloads and resources. In this paper, we propose EdgeTuner to effectively leverage DRL to select scheduling algorithms online for edge-cloud jobs. The enabling features of EdgeTuner are sophisticated DRL model that captures complex dynamics of Edge-Cloud jobs/tasks and an effective simulator to emulate the response times of short-running jobs in accordance to dynamically changing scheduling algorithms. EdgeTuner trains DRL agents offline by directly interacting with the simulator. We implement EdgeTuner on Kubernetes scheduler and extensively evaluate it on Kubernetes cluster testbed driven by the production traces. Our results show that EdgeTuner outperforms prevailing scheduling algorithms by achieving significant lower job response time while accelerating DRL training speed by more than 180x.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796792"}, {"primary_key": "1711239", "vector": [], "sparse_vector": [], "title": "Fast and Heavy Disjoint Weighted Matchings for Demand-Aware Datacenter Topologies.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Reconfigurable optical topologies promise to improve the performance in datacenters by dynamically optimizing the physical network in a demand-aware manner. State-of-the-art optical technologies allow to establish and update direct connectivity (in the form of edge-disjoint matchings) between top-of-rack switches within microseconds or less. However, to fully exploit temporal structure in the demand, such fine-grained reconfigurations also require fast algorithms for optimizing the interconnecting matchings.Motivated by the desire to offload a maximum amount of demand to the reconfigurable network, this paper initiates the study of fast algorithms to find k disjoint heavy matchings in graphs. We present and analyze six algorithms, based on iterative matchings, b-matching, edge coloring, and node-rankings. We show that the problem is generally ${\\mathcal{N}}{\\mathcal{P}}{\\text{ - hard}}$ and study the achievable approximation ratios.An extensive empirical evaluation of our algorithms on both real-world and synthetic traces (88 in total), including traces collected in Facebook datacenters and in HPC clusters reveals that all our algorithms provide high-quality matchings, and also very fast ones come within 95 % or more of the best solution. However, the running times differ significantly and what is the best algorithm depends on k and the acceptable runtime-quality tradeoff.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796921"}, {"primary_key": "1711241", "vector": [], "sparse_vector": [], "title": "DBAC: Directory-Based Access Control for Geographically Distributed IoT Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We propose and implement Directory-Based Access Control (DBAC), a flexible and systematic access control approach for geographically distributed multi-administration IoT systems. DBAC designs and relies on a particular module, IoT directory, to store device metadata, manage federated identities, and assist with cross-domain authorization. The directory service decouples IoT access into two phases: discover device information from directories and operate devices through discovered interfaces. DBAC extends attribute-based authorization and retrieves diverse attributes of users, devices, and environments from multi-faceted sources via standard methods, while user privacy is protected. To support resource-constrained devices, DBAC assigns a capability token to each authorized user, and devices only validate tokens to process a request.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796804"}, {"primary_key": "1711246", "vector": [], "sparse_vector": [], "title": "Auter: Automatically Tuning Multi-layer Network Buffers in Long-Distance Shadowsocks Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "To bypass network censorship, Shadowsocks is often deployed on long-distance transnational networks; however, such proxy networks are usually plagued by high latency, high packet loss rate, and unstable bandwidth. Most existing tuning solutions rely on hand-tuned heuristics, which cannot work well in the volatile Shadowsocks networks due to the labor intensive and time-consuming properties. In this paper, we propose <PERSON>ter, which automatically tunes multi-layer buffer parameters with reinforcement learning (RL) to improve the performance of Shadowsocks in long-distance networks. The key insight behind <PERSON><PERSON> is that different network environments require different sizes of buffers to achieve sufficiently good performance. Hence, <PERSON><PERSON> continuously learns a tuning policy from volatile network states and dynamically alter sizes of multi-buffers for high network performance. We prototype Auter and evaluate its effectiveness under various real networks. Our experimental results show that <PERSON><PERSON> can effectively improve network performance, up to 40.5% throughput increase in real networks. Besides, we demonstrate that <PERSON><PERSON> outperforms all the existing tuning schemes.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796882"}, {"primary_key": "1711248", "vector": [], "sparse_vector": [], "title": "Tackling Multipath and Biased Training Data for IMU-Assisted BLE Proximity Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "S.<PERSON><PERSON><PERSON>"], "summary": "Proximity detection is to determine whether an IoT receiver is within a certain distance from a signal transmitter. Due to its low cost and high popularity, Bluetooth low energy (BLE) has been used to detect proximity based on the received signal strength indicator (RSSI). To address the fact that RSSI can be markedly influenced by device carriage states, previous works have incorporated RSSI with inertial measurement unit (IMU) using deep learning. However, they have not sufficiently accounted for the impact of multipath. Furthermore, due to the special setup, the IMU data collected in the training process may be biased, which hampers the system's robustness and generalizability. This issue has not been studied before.We propose PRID, an IMU-assisted BLE proximity detection approach robust against RSSI fluctuation and IMU data bias. PRID histogramizes RSSI to extract multipath features and uses carriage state regularization to mitigate overfitting due to IMU data bias. We further propose PRID-lite based on a binarized neural network to substantially cut memory requirements for resource-constrained devices. We have conducted extensive experiments under different multipath environments, data bias levels, and a crowdsourced dataset. Our results show that PRID significantly reduces false detection cases compared with the existing arts (by over 50%). PRID-lite further reduces over 90% PRID model size and extends 60% battery life, with a minor compromise in accuracy (7%).", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796716"}, {"primary_key": "1711249", "vector": [], "sparse_vector": [], "title": "Age-Based Scheduling for Monitoring and Control Applications in Mobile Edge Computing Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the development of Mobile Edge Computing (MEC) and Internet of Things (IoT) technology, various real-time monitoring and control applications are deployed to benefit people's daily life. The performance of these applications relies heavily on the timeliness of collected environmental information, which can be effectively quantified by the recently introduced metric named age of information (AoI). Although extensive researches have been conducted to optimize AoI under various circumstances, these works commonly require a priori information about the system dynamics that is usually unknown in realistic situations. To design a more practical scheduling algorithm, in this paper, we formulate the AoI minimization problem as a Constrained Markov Decision Process (CMDP) which can be solved by Reinforcement Learning (RL) algorithms without prior knowledge. To improve the running efficiency, we (1) introduce post-decision states (PDSs) to exploit the partial knowledge of the system's dynamics, (2) perform a batch update in every learning step, (3) decompose the system-level value function into multiple device-level value functions, and (4) propose a heuristic algorithm to find the greedy action. Numerical results demonstrate that our algorithm is highly efficient and outperforms the benchmarks under various scenarios.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796654"}, {"primary_key": "1711250", "vector": [], "sparse_vector": [], "title": "MUSTER: Subverting User Selection in MU-MIMO Networks.", "authors": ["<PERSON>", "Shengping Bi", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "WiFi 5/6 relies on a key feature, Multi-User Multiple-In-Multiple-Out (MU-MIMO), to offer high-volume network throughput and spectrum efficiency. MU-MIMO uses a user selection algorithm, based on each user's channel state information (CSI), to schedule transmission opportunities for a group of users to maximize the service quality and efficiency. In this paper, we discover that such algorithm creates a subtle attack surface for attackers to subvert user selection in MU-MIMO, causing severe disruptions in today's wireless networks. We develop a system, named MU-MIMO user selection strategy inference and subversion (MUSTER), to systematically study the attack strategies and further to seek efficient mitigation. MUSTER is designed to include two major modules: (i) strategy inference, which leverages a new neural group-learning strategy named MC-grouping via combining Recurrent Neural Network (RNN) and Monte Carlo Tree Search (MCTS) to reverseengineer a user selection algorithm, and (ii) user selection subversion, which proactively fabricates CSI to manipulate user selection results for disruption. Experimental evaluation shows that MUSTER achieves a high accuracy rate around 98.6% in user selection prediction and effectively launches the attacks to disrupt the network performance. Finally, we create a Reciprocal Consistency Checking technique to defend against the proposed attacks to secure MU-MIMO user selection.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796815"}, {"primary_key": "1711252", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>;t <PERSON> Packets: Boosting LoRa Reception with Antenna Diversities.", "authors": ["<PERSON><PERSON><PERSON>", "Xianjin Xia", "Yuan<PERSON> Zheng"], "summary": "LoRa technology promises to connect billions of battery-powered devices over a long range for years. However, recent studies and industrial deployment find that LoRa suffers severe signal attenuation because of signal blockage in smart cities and long communication ranges in smart agriculture applications. As a result, weak LoRa packets cannot be correctly demodulated or even be detected in practice. To address this problem, this paper presents the design and implementation of MALoRa: a new LoRa reception scheme which aims to improve LoRa reception performance with antenna diversities. At a high level, MALoRa improves signal strength by reliably detecting and coherently combining weak signals received by multiple antennas of a gateway. MALoRa addresses a series of practical challenges, including reliable packet detection, symbol edge extraction, and phase-aligned constructive combining of weak signals. Experiment results show that MALoRa can effectively expand communication range, increase battery life of LoRa devices, and improve packet detection and demodulation performance especially in ultra-low SNR scenarios.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796699"}, {"primary_key": "1711254", "vector": [], "sparse_vector": [], "title": "Distributed Inference with Deep Learning Models across Heterogeneous Edge Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent years witnessed an increasing research attention in deploying deep learning models on edge devices for inference. Due to limited capabilities and power constraints, it may be necessary to distribute the inference workload across multiple devices. Existing mechanisms divided the model across edge devices with the assumption that deep learning models are constructed with a chain of layers. In reality, however, modern deep learning models are more complex, involving a directed acyclic graph (DAG) rather than a chain of layers.In this paper, we present EdgeFlow, a new distributed inference mechanism designed for general DAG structured deep learning models. Specifically, EdgeFlow partitions model layers into independent execution units with a new progressive model partitioning algorithm. By producing near-optimal model partitions, our new algorithm seeks to improve the run-time performance of distributed inference as these partitions are distributed across the edge devices. During inference, EdgeFlow orchestrates the intermediate results flowing through these units to fulfill the complicated layer dependencies. We have implemented Edge-Flow based on PyTorch, and evaluated it with state-of-the-art deep learning models in different structures. The results show that EdgeFlow reducing the inference latency by up to 40.2% compared with other approaches, which demonstrates the effectiveness of our design.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796896"}, {"primary_key": "1711255", "vector": [], "sparse_vector": [], "title": "Otus: A Gaze Model-based Privacy Control Framework for Eye Tracking Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Eye tracking techniques have been widely adopted by a wide range of devices (e.g., AR/VR headsets, smartphones) to enhance user experiences. However, eye gaze data is private in nature, which can reveal users' psychological and physiological features. Privacy protection techniques can be incorporated to preserve privacy of eye tracking information. Yet, most existing solutions based on Differential Privacy (DP) mechanisms cannot well protect privacy for individual users without sacrificing user experience. In this paper, we are among the first to propose a novel gaze model-based privacy control framework called Otus for eye tracking applications, which incorporates local DP (LDP) mechanisms to preserve user privacy and improves user experience in the meanwhile. First, we conduct a measurement study on real traces to illustrate that direct noise injection on raw gaze trajectories can significantly lower the utility of gaze data. To preserve utility and privacy simultaneously, <PERSON><PERSON> injects noises in two steps: (1) Extracting model features from raw data to depict gaze trajectories on individual users; (2) Adding LDP noises into model features so as to protect privacy. On one hand, established models can be used to recover user gaze data in order to improve service quality of eye tracking applications. On the other hand, we only need to add LDP noises to distort a small number of model parameters rather than every point on a trajectory to preserve privacy, which has less impact on the utility of gaze data given the same privacy budget. By applying the tile view graph model in step (1), we illustrate the entire workflow of Otus and prove its privacy protection level. For evaluation, we conduct extensive experiments using real gaze traces and the results show that Otus can effectively protect privacy for individual users without significantly compromising gaze data utility.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796665"}, {"primary_key": "1711256", "vector": [], "sparse_vector": [], "title": "MILLIEAR: Millimeter-wave Acoustic Eavesdropping with Unconstrained Vocabulary.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As acoustic communication systems become more common in homes and offices, eavesdropping brings significant security and privacy risks. Current approaches of acoustic eavesdropping either provide low resolution due to the use of sub-6 GHz frequencies, work only for limited words using classification, or cannot work through-wall due to the use of optical sensors. In this paper, we present MILLIEAR, a mmWave acoustic eavesdropping system that leverages the high-resolution of mmWave FMCW ranging and generative machine learning models to not only extract vibrations but to reconstruct the audio. MILLIEAR combines speaker vibration estimation with conditional generative adversarial networks to eavesdrop with unconstrained vocabulary. We implement and evaluate MIL-LIEAR using off-the-shelf mmWave radar deployed in different scenarios and settings. We find that it can accurately reconstruct the audio even at different distances, angles and through the wall with different insulator materials. Our subjective and objective evaluations show that the reconstructed audio has a strong similarity with the original audio.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796940"}, {"primary_key": "1711257", "vector": [], "sparse_vector": [], "title": "Dino: A Block Transmission Protocol with Low Bandwidth Consumption and Propagation Latency.", "authors": ["<PERSON><PERSON><PERSON> Hu", "<PERSON><PERSON>"], "summary": "Block capacity plays a critical role in maintaining blockchain security and improving transactions per second (TPS). Increasing block capacity can help attain higher TPS, but it also prolongs block propagation latency and degrades system security. In the present paper, we argue that existing work compressing the block size to shorten block propagation latency introduces an undesired side effect, which is that the size of compressed blocks will increase with transaction volume. Instead, we propose Dino, a new block transmission protocol between two peers. Once a node receives a Dino block, it can recover the original block with that Dino block and transactions in its transaction pool. Since <PERSON> transmits block construction rules instead of compressed block content, it has good scalability to transmit blocks with larger transaction volume. We deploy Dino into Bitcoin and Bitcoin Cash to compare it with the state-of-art protocols: Compact, XThin, and Graphene. For a block with 3,000 transactions, its corresponding Dino block is no more than 1 KB in size, which is only 4% of a XThin block, 5% of a Compact block, and 20% of a Graphene block. The size of Dino blocks stays constant when the transaction volume reaches Bitcoin and Bitcoin Cash's protocol limit. Simulation experiments show that <PERSON> scales well with higher transaction generation rates and can reduce block propagation latency.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796837"}, {"primary_key": "1711259", "vector": [], "sparse_vector": [], "title": "BrokerChain: A Cross-Shard Blockchain Protocol for Account/Balance-based State Sharding.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jianzhou Zhan", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "State-of-the-art blockchain sharding solutions, say Monoxide, can induce imbalanced transaction (TX) distributions among all blockchain shards due to their account deployment mechanisms. Imbalanced TX distributions then cause hot shards, in which the cross-shard TXs may experience an unlimited length of confirmation latency. Thus, how to address the hot-shard issue and how to reduce cross-shard TXs become significant challenges of blockchain state sharding. Through reviewing the related studies, we find that a cross-shard TX protocol that can achieve workload balance among all shards and simultaneously reduce the number of cross-shard TXs is still absent from the literature. To this end, we propose BrokerChain, which is a cross-shard blockchain protocol devised for the account/balance-based state sharding. Essentially, BrokerChain exploits fine-grained state partition and account segmentation. We also elaborate on how BrokerChain handles cross-shard TXs through broker accounts. The security issues and other properties of BrokerChain are analyzed substantially. Finally, we conduct comprehensive evaluations using both a cloud-based prototype and a transaction-driven simulator. The evaluation results show that BrokerChain outperforms other solutions in terms of system throughput, transaction confirmation latency, the queue size of transaction pool, and workload balance.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796859"}, {"primary_key": "1711260", "vector": [], "sparse_vector": [], "title": "AoDNN: An Auto-Offloading Approach to Optimize Deep Inference for Fostering Mobile Web.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Employing today's deep neural network (DNN) into the cross-platform web with an offloading way has been a promising means to alleviate the tension between intensive inference and limited computing resources. However, it is still challenging to directly leverage the distributed DNN execution into web apps with the following limitations, including (1) how special computing tasks such as DNN inference can provide fine-grained and efficient offloading in the inefficient JavaScript-based environment? (2) lacking the ability to balance the latency and mobile energy to partition the inference facing various web applications' requirements. (3) and ignoring that DNN inference is vulnerable to the operating environment and mobile devices' computing capability, especially dedicated web apps. This paper designs AoDNN, an automatic offloading framework to orchestrate the DNN inference across the mobile web and the edge server, with three main contributions. First, we design the DNN offloading based on providing a snapshot mechanism and use multi-threads to monitor dynamic contexts, partition decision, trigger offloading, etc. Second, we provide a learning-based latency and mobile energy prediction framework for supporting various web browsers and platforms. Third, we establish a multi-objective optimization to solve the optimal partition by balancing the latency and mobile energy.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796763"}, {"primary_key": "1711261", "vector": [], "sparse_vector": [], "title": "Thwarting Unauthorized Voice Eavesdropping via Touch Sensing in Mobile Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Enormous mobile applications (apps) now support voice functionality for convenient user-device interaction. However, these voice-enabled apps may spitefully invoke microphone to realize voice eavesdropping with arousing security risks and privacy concerns. To explore the issue of voice eavesdropping, in this work, we first design eavesdropping apps through native development and injection development to conduct eavesdropping attacks on a series of smart devices. The results demonstrate that eavesdropping could be carried out freely without any hint. To thwart voice eavesdropping, we propose a valid eavesdropping detection (EarDet) scheme based on the discovery that the activation of voice function in most apps requires authorization from the user by touching a specific voice icon. In the scheme, we construct a request-response time model using the Unix time stamps of touching the voice icon and microphone invoked. Through numerical analysis and hypothesis testing to effectively verify the pattern of the app's normal access under user authorization to the microphone, we could detect eavesdropping attacks by sensing whether there is a touch operation. Finally, we apply the scheme to different smart devices and test several apps. The experimental results show that the proposed EarDet scheme can achieve a high detection accuracy.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796653"}, {"primary_key": "1711264", "vector": [], "sparse_vector": [], "title": "Network Tomography based on Adaptive Measurements in Probabilistic Routing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We discuss Boolean network tomography in a probabilistic routing environment. Although the stochastic behavior of routing can be found in load balancing mechanisms and normal routing protocols, it has not been discussed much in network tomography so far. In probabilistic routing, because monitoring paths are not uniquely determined, a huge number of measurements are generally required to identify the network state. To overcome this difficulty, we propose a network tomography method for efficiently narrowing down the states with a limited number of measurements by iteratively updating the posterior of the states. In this method, we introduce mutual information as a measure of the effectiveness of the probabilistic monitoring path. This enables us to prioritize measurements that are critically effective in identifying the state, thus significantly reducing the number of required measurements. We show that our method has a theoretical guarantee of the approximation ratio (1 – 1/e) on the basis of submodularity analysis. Numerical evaluations show that our method can identify the network states with far fewer measurements than existing methods.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796807"}, {"primary_key": "1711267", "vector": [], "sparse_vector": [], "title": "ComAI: Enabling Lightweight, Collaborative Intelligence by Retrofitting Vision DNNs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While Deep Neural Network (DNN) models have transformed machine vision capabilities, their extremely high computational complexity and model sizes present a formidable deployment roadblock for AIoT applications. We show that the complexity-vs-accuracy-vs-communication tradeoffs for such DNN models can be significantly addressed via a novel, lightweight form of \"collaborative machine intelligence\" that requires only runtime changes to the inference process. In our proposed approach, called ComAI, the DNN pipelines of different vision sensors share intermediate processing state with one another, effectively providing hints about objects located within their mutually-overlapping Field-of-Views (FoVs). CoMAI uses two novel techniques: (a) a secondary shallow ML model that uses features from early layers of a peer DNN to predict object confidence values in the image, and (b) a pipelined sharing of such confidence values, by collaborators, that is then used to bias a reference DNN's outputs. We demonstrate that CoMAI (a) can boost accuracy (recall) of DNN inference by 20-50%, (b) works across heterogeneous DNN models and deployments, and (c) incurs negligible processing, bandwidth and processing overheads compared to non-collaborative baselines.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796769"}, {"primary_key": "1711269", "vector": [], "sparse_vector": [], "title": "Energy Saving in Heterogeneous Wireless Rechargeable Sensor Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Lu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile chargers (MCs) are usually dispatched to deliver energy to sensors in wireless rechargeable sensor networks (WRSNs) due to its flexibility and easy maintenance. This paper concerns the fundamental issue of charging path DEsign with the Minimized energy cOst (DEMO), i.e., given a set of rechargeable sensors, we appropriately design the MC's charging path to minimize the energy cost which is due to the wireless charging and the MC's movement, such that the different charging demand of each sensor is satisfied. Solving DEMO is NP-hard and involves handling the tradeoff between the charging efficiency and the moving cost. To address DEMO, we first develop a computational geometry-based algorithm to deploy multiple charging positions where the MC stays to charge nearby sensors. We prove that the designed algorithm has the approximation ratio of O(lnN), where N is the number of sensors. Then we construct the charging path by calculating the shortest Hamiltonian cycle passing through all the deployed charging positions within the network. Extensive evaluations validate the effectiveness of our path design in terms of the MC's energy cost minimization.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796770"}, {"primary_key": "1711270", "vector": [], "sparse_vector": [], "title": "Copa+: Analysis and Improvement of the Delay-based Congestion Control Algorithm Copa.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Danfeng Shan", "<PERSON><PERSON><PERSON>"], "summary": "Copa is a delay-based congestion control algorithm proposed in NSDI recently. It can achieve consistent high performance under various network environments and has already been deployed in Facebook. In this paper, we theoretically analyze Copa and reveal its large queuing delay and poor fairness issue under certain conditions. The root cause is that Copa fails to clear the bottleneck buffer occupancy periodically as expected. Accordingly, Copa may get a wrong base RTT estimation and enter its competitive mode by mistake, leading to large delay and unfairness. To address these issues, we propose Copa+, which enhances Copa with a parameter adaptation mechanism and an optimized competitive mode entrance criterion. Designed based on our theoretical analysis, Copa+ can adaptively clear the bottleneck buffer occupancy for correct estimation of base RTT. Consequently, Copa+ inherits the advantages of Copa but achieves lower queuing delay and better fairness under different environments, as confirmed by the real-world experiments and simulations. Specifically, Copa+ has the highest throughput similar to Copa but 11.9% lower queuing delay over different Internet links among different cloud nodes, and achieves 39.4% lower queuing delay and 8.9% higher throughput compared to Sprout over emulated cellular links.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796913"}, {"primary_key": "1711278", "vector": [], "sparse_vector": [], "title": "JADE: Data-Driven Automated Jammer Detection Framework for Operational Mobile Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Wireless jammer activity from malicious or malfunctioning devices cause significant disruption to mobile network services and user QoE degradation. In practice, detection of such activity is manually intensive and costly, taking days and weeks after the jammer activation to detect it. We present a novel data-driven jammer detection framework termed JADE that leverages continually collected operator-side cell-level KPIs to automate this process. As part of this framework, we develop two deep learning based semi-supervised anomaly detection methods tailored for the jammer detection use case. JADE features further innovations, including an adaptive thresholding mechanism and transfer learning based training to efficiently scale JADE for operation in real-world mobile networks. Using a real-world 4G RAN dataset from a multinational mobile network operator, we demonstrate the efficacy of proposed jammer detection methods relative to commonly used anomaly detection methods. We also demonstrate the robustness of our proposed methods in accurately detecting jammer activity across multiple frequency bands and diverse types of jammers. We present real-world validation results from applying our methods in the operator's network for online jammer detection. We also present promising results on pinpointing jammer locations when our methods spot jammer activity in the network along with cell site location data.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796674"}, {"primary_key": "1711280", "vector": [], "sparse_vector": [], "title": "MoDEMS: Optimizing Edge Computing Migrations for User Mobility.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Edge computing capabilities in 5G wireless networks promise to benefit mobile users: computing tasks can be offloaded from user devices to nearby edge servers, reducing users' experienced latencies. Few works have addressed how this offloading should handle long-term user mobility: as devices move, they will need to offload to different edge servers, which may require migrating data or state information from one edge server to another. In this paper, we introduce MoDEMS, a system model and architecture that provides a rigorous theoretical framework and studies the challenges of such migrations to minimize the service provider cost and user latency. We show that this cost minimization problem can be expressed as an integer linear programming problem, which is hard to solve due to resource constraints at the servers and unknown user mobility patterns. We show that finding the optimal migration plan is in general NP-hard, and we propose alternative heuristic solution algorithms that perform well in both theory and practice. We finally validate our results with real user mobility traces, ns-3 simulations, and an LTE testbed experiment. Migrations reduce the latency experienced by users of edge applications by 33% compared to previously proposed migration approaches.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796680"}, {"primary_key": "1711282", "vector": [], "sparse_vector": [], "title": "Network Link Weight Setting: A Machine Learning Based Approach.", "authors": ["Murali S<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Internet routing protocols like OSPF and ISIS use shortest path routing to route traffic from ingress nodes to egress nodes in a network. These shortest paths are computed with respect to the weights assigned to links in the underlying network. Since the routed paths depend on the assigned link weights, a fundamental problem in optimizing network routing is the determination of the set of weights that minimizes congestion in the network. This is an NP-hard combinatorial optimization problem. Consequently, several heuristics have been developed to determine the set of link weights to minimize congestion. In this paper, we develop a machine-learning based approach by formulating a smoothed version of the weight setting problem and using gradient descent in the PyTorch framework to derive approximate solutions to this problem. We demonstrate the improvement in performance compared to traditional approaches using several benchmark network topologies.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796922"}, {"primary_key": "1711285", "vector": [], "sparse_vector": [], "title": "Push the Limit of WiFi-based User Authentication towards Undefined Gestures.", "authors": ["Hao Kong", "Li Lu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Linghe Kong", "<PERSON>"], "summary": "With the development of smart indoor environments, user authentication becomes an essential mechanism to support various secure accesses. Although recent studies have shown initial success on authenticating users with human activities or gestures using WiFi, they rely on predefined body gestures and perform poorly when meeting undefined body gestures. This work aims to enable WiFi-based user authentication with undefined body gestures rather than only predefined body gestures, i.e., realizing a gesture-independent user authentication. In this paper, we first explore physiological characteristics underlying body gestures, and find that statistical distributions under WiFi signals induced by body gestures can exhibit invariant individual uniqueness unrelated to specific body gestures. Inspired by this observation, we propose a user authentication system, which utilizes WiFi signals to identify individuals in a gesture-independent manner. Specifically, we design an adversarial learning-based model, which suppresses specific gesture characteristics, and extracts invariant individual uniqueness unrelated to specific body gestures, to authenticate users in a gesture-independent manner. Extensive experiments in indoor environments show that the proposed system is feasible and effective in gesture-independent user authentication.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796740"}, {"primary_key": "1711290", "vector": [], "sparse_vector": [], "title": "SpaceRTC: Unleashing the Low-latency Potential of Mega-constellations for Real-Time Communications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>"], "summary": "User-perceived latency is important for the quality of experience (QoE) of wide-area real-time communications (RTC). This paper explores a futuristic yet important problem facing the RTC community: can we exploit emerging mega-constellations to facilitate low-latency RTC globally? We carry out our quest in three steps. First, through a measurement study associated with a large number of geo-distributed RTC users, we quantitatively expose that the meandering routes in the client-cloud and inter-cloud-site segment of existing cloud-based RTC architecture are critical culprits for the high latency issue suffered by wide-area RTC sessions. Second, we propose SPACERTC, a satellite-cloud cooperative framework that adaptively selects relay servers upon satellites and cloud sites to build an overlay network which enables diverse close-to-optimal paths, and then judiciously allocates RTC flows upon the network to facilitate low-latency interactions. Finally, we implement our SPACERTC prototype on an experimental environment based on public constellation information and RTC trace, and extensive experiments demonstrate that SPACERTC can deliver near-optimal interactive latency, with up to 64.9% latency reduction as compared to other state-of-the-art cloud-based solutions under representative videoconferencing traffic.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796887"}, {"primary_key": "1711293", "vector": [], "sparse_vector": [], "title": "Nadege: When <PERSON><PERSON><PERSON> Kernels meet Network Anomaly Detection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "With the continuous growing level of dynamicity, heterogeneity, and complexity of traffic data, anomaly detection remains one of the most critical tasks to ensure an efficient and flexible management of a network. Recently, driven by their empirical success in many domains, especially bioinformatics and computer vision, graph kernels have attracted increasing attention. Our work aims at investigating their discrimination power for detecting vulnerabilities and distilling traffic in the field of networking.In this paper, we propose Nadege, a new graph-based learning framework which aims at preventing anomalies from disrupting the network while providing assistance for traffic monitoring. Specifically, we design a graph kernel tailored for network profiling by leveraging propagation schemes which regularly adapt to contextual patterns. Moreover, we provide provably efficient algorithms and consider both offline and online detection policies. Finally, we demonstrate the potential of kernel-based models by conducting extensive experiments on a wide variety of network environments. Under different usage scenarios, <PERSON><PERSON><PERSON> significantly outperforms all baseline approaches.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796978"}, {"primary_key": "1711294", "vector": [], "sparse_vector": [], "title": "Cutting Through the Noise to Infer Autonomous System Topology.", "authors": ["<PERSON>rt<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The Border Gateway Protocol (BGP) is a distributed protocol that manages interdomain routing without requiring a centralized record of which autonomous systems (ASes) connect to which others. Many methods have been devised to infer the AS topology from publicly available BGP data, but none provide a general way to handle the fact that the data are notoriously incomplete and subject to error. This paper describes a method for reliably inferring AS-level connectivity in the presence of measurement error using Bayesian statistical inference acting on BGP routing tables from multiple vantage points. We employ a novel approach for counting AS adjacency observations in the AS-PATH attribute data from public route collectors, along with a Bayesian algorithm to generate a statistical estimate of the AS-level network. Our approach also gives us a way to evaluate the accuracy of existing reconstruction methods and to identify advantageous locations for new route collectors or vantage points.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796874"}, {"primary_key": "1711296", "vector": [], "sparse_vector": [], "title": "CurveALOHA: Non-linear Chirps Enabled High Throughput Random Channel Access for LoRa.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Long Range Wide Area Network (LoRaWAN), using the linear chirp for data modulation, is known for its low-power and long-distance communication to connect massive Internet-of-Things devices at a low cost. However, LoRaWAN throughput is far behind the demand for the dense and large-scale IoT deployments, due to the frequent collisions with the by-default random channel access (i.e., ALOHA). Recently, some works enable an effective LoRa carrier-sense for collision avoidance. However, the continuous back-off makes the network throughput easily saturated and degrades the energy efficiency at LoRa end nodes. In this paper, we propose CurveALOHA, a brand-new media access control scheme to enhance the throughput of random channel access by embracing non-linear chirps enabled quasi-orthogonal logical channels. First, we empirically show that non-linear chirps can achieve similar noise tolerance ability as the linear one does. Then, we observe that multiple nonlinear chirps can create new logical channels which are quasi-orthogonal with the linear one and each other. Finally, given a set of non-linear chirps, we design two random chirp selection methods to guarantee an end node can access a channel with less collision probability. We implement CurveALOHA with the software-defined radios and conduct extensive experiments in both indoor and outdoor environments. The results show that CurveALOHA's network throughput is 59.6% higher than the state-of-the-art carrier-sense MAC.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796757"}, {"primary_key": "1711297", "vector": [], "sparse_vector": [], "title": "D2BF - Data-Driven Beamforming in MU-MIMO with Channel Estimation Uncertainty.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Accurate estimation of Channel State Information (CSI) is essential to design MU-MIMO beamforming. However, errors in CSI estimation are inevitable in practice. State-of-the-art works model CSI as random variables and assume certain specific distributions or worst-case boundaries, both of which suffer performance issues when providing performance guarantees to the users. In contrast, this paper proposes a Data-Driven Beamforming (D 2 BF) that directly handles the available CSI data samples (without assuming any particular distributions). Specifically, we employ chance-constrained programming (CCP) to provide probabilistic data rate guarantees to the users and introduce ∞-Wasserstein ambiguity set to bridge the unknown CSI distribution with the available (limited) data samples. Through problem decomposition and a novel bilevel formulation for each subproblem, we show that each subproblem can be solved by binary search and convex approximation. We also validate that D 2 BF offers better performance than the state-of-the-art approach while meeting probabilistic data rate guarantees to the users.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796930"}, {"primary_key": "1711298", "vector": [], "sparse_vector": [], "title": "RCID: Fingerprinting Passive RFID Tags via Wideband Backscatter.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tag cloning and spoofing pose great challenges to RFID applications. This paper presents the design and evaluation of RCID, a novel system to fingerprint RFID tags based on the unique reflection coefficient of each tag circuit. Based on a novel OFDM-based fingerprint collector, our system can quickly acquire and verify each tag's RCID fingerprint which are independent of the RFID reader and measurement environment. Our system applies to COTS RFID tags and readers after a firmware update at the reader. Extensive prototyped experiments on 600 tags confirm that RCID is highly secure with the authentication accuracy up to 97.15% and the median authentication error rate equal to 1.49%. RCID is also highly usable because it only takes about 8 s to enroll a tag and 2 ms to verify an RCID fingerprint with a fully connected multi-class neural network. Finally, empirical studies demonstrate that the entropy of an RCID fingerprint is about 202 bits over a bandwidth of 20 MHz in contrast to the best prior result of 17 bits, thus offering strong theoretical resilience to RFID cloning and spoofing.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796663"}, {"primary_key": "1711299", "vector": [], "sparse_vector": [], "title": "On Uploading Behavior and Optimizations of a Mobile Live Streaming Service.", "authors": ["<PERSON><PERSON> Li", "Zhenyu Li", "Qinghua Wu", "<PERSON>"], "summary": "Mobile Live Streaming (MLS) services are now one of the most popular types of mobile apps. They involve a (often amateur) user broadcasting content to a potentially large online audience via unreliable networks (e.g., LTE). Although prior work has focused on viewer-side behavior, it is equally important to study and improve broadcaster operations. Using detailed logs obtained from a major MLS provider, we first conduct an in-depth measurement study of uploading behavior. Our key findings include large wasteful uploads, strong viewing locality, and traffic dominance of loyal viewers. Specifically, 33.3% of uploads go unwatched, and the viewership of broadcasters tends to be localized to a small set of broadcaster-specific network regions. Inspired by our findings, we propose two system innovations to streamline MLS systems: adaptive uploading and edge server pre-fetching. These optimizations leverage machine learning for reduced waste and improved QoE. Trace-driven experiments show that the adaptive uploading reduces the resources wastage by 63%, and the pre-fetching boosts the startup by 29.5%.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796824"}, {"primary_key": "1711300", "vector": [], "sparse_vector": [], "title": "GASLA: Enhancing the Applicability of Sign Language Translation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Zhenjiang Li"], "summary": "This paper studies an important yet overlooked applicability issue in existing American sign language (ASL) translation systems. With excessive sensing data collected for each ASL word already, current designs treat every to-be-recognized sentence as new and collect their sensing data from scratch, while the amounts of sentences and the data samples per sentence are large usually. It takes a long time to complete the data collection for each single user, e.g., hours to a half day, which brings non-trivial burden to the end users inevitably and prevents the broader adoption of the ASL systems in practice. In this paper, we figure out the reason causing this issue. We present GASLA atop the wearable sensors to instrument our design. With GASLA, the sentence-level sensing data can be generated from the word-level data automatically, which can be then applied to train ASL systems. Moreover, GASLA has a clear interface to be integrated to existing ASL systems for overhead reduction directly. With this ability, sign language translation could become highly lightweight in both initial setup and future new-sentence addition. Compared with around 10 per-sentence data samples in current systems, GASLA requires 2–3 samples to achieve a similar performance.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796819"}, {"primary_key": "1711303", "vector": [], "sparse_vector": [], "title": "Revisiting Frequency Analysis against Encrypted Deduplication via Statistical Distribution.", "authors": ["<PERSON>wei Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yanjing Ren", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Encrypted deduplication addresses both security and storage efficiency in large-scale storage systems: it ensures that each plaintext is encrypted to a ciphertext by a symmetric key derived from the content of the plaintext, so as to allow deduplication on the ciphertexts derived from duplicate plaintexts. However, the deterministic nature of encrypted deduplication leaks the frequencies of plaintexts, thereby allowing adversaries to launch frequency analysis against encrypted deduplication and infer the ciphertext-plaintext pairs in storage. In this paper, we revisit the security vulnerability of encrypted deduplication due to frequency analysis, and show that encrypted deduplication can be even more vulnerable to the sophisticated frequency analysis attack that exploits the underlying storage workload characteristics. We propose the distribution-based attack, which builds on a statistical approach to model the relative frequency distributions of plaintexts and ciphertexts, and improves the inference precision (i.e., have high confidence on the correctness of inferred ciphertext-plaintext pairs) of the previous attack. We evaluate the new attack against real-world storage workloads and provide insights into its actual damage.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796897"}, {"primary_key": "1711305", "vector": [], "sparse_vector": [], "title": "Fast and Secure Key Generation with Channel Obfuscation in Slowly Varying Environments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Jun<PERSON> Zhang", "Hongbo Liu", "<PERSON><PERSON><PERSON>"], "summary": "Physical-layer secret key generation has emerged as a promising solution for establishing cryptographic keys by leveraging reciprocal and time-varying wireless channels. However, existing approaches suffer from low key generation rates and vulnerabilities under various attacks in slowly varying environments. We propose a new physical-layer secret key generation approach with channel obfuscation, which improves the dynamic property of channel parameters based on random filtering and random antenna scheduling. Our approach makes one party obfuscate the channel to allow the legitimate party to obtain similar dynamic channel parameters, yet prevents a third party from inferring the obfuscation information. Our approach allows more random bits to be extracted from the obfuscated channel parameters by a joint design of the K-L transform and adaptive quantization. Results from a testbed implementation show that our approach, compared to the existing ones that we evaluate, performs the best in generating high entropy bits at a fast rate and is able to resist various attacks in slowly varying environments. Specifically, our approach can achieve a significantly faster secret bit generation rate at roughly 67 bit/pkt, and the key sequences can pass the randomness tests of the NIST test suite.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796694"}, {"primary_key": "1711306", "vector": [], "sparse_vector": [], "title": "The Hanging ROA: A Secure and Scalable Encoding Scheme for Route Origin Authorization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "On top of the Resource Public Key Infrastructure (RPKI), the Route Origin Authorization (ROA) creates a cryptographically verifiable binding of an autonomous system to a set of IP prefixes it is authorized to originate. By their design, ROAs can protect the inter-domain routing system against prefix and sub-prefix hijacks. However, inappropriate configurations bring in vulnerabilities to other types of routing security attacks. As such, the state-of-the-art approach implements the minimal-ROA principle, eliminating the risk of using ROAs at the cost of system scalability. This paper proposes the hanging ROA, a novel bitmap-based encoding scheme for ROAs, that not only ensures strong security, but also significantly improves system scalability. According to the performance evaluation with real-world data sets, the hanging ROA outperforms the state-of-the-art approach 2.4 times in terms of the compression ratio, and it can reduce the cost of a router to synchronize all validated ROA payloads by 44.5% ~ 64.7%.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796844"}, {"primary_key": "1711307", "vector": [], "sparse_vector": [], "title": "Online Pricing with Limited Supply and Time-Sensitive Valuations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many efforts have been devoted to online pricing mechanism design for different settings. In this work, we consider a common but challenging setting where the buyers have private time-sensitive valuations and the seller has limited supply. The seller offers a take-it-or-leave-it posted price for each arriving buyer and aims to maximize the expected total revenue. The unknown distribution of time-sensitive valuations and limited supply significantly increase the difficulty of searching the optimal dynamic posted prices. Given B identical items to sell, when the time-dependent valuations can be estimated with a factor of α, we prove Ω(log(1/α)) lower bound with respect to the optimal fixed distribution over prices and design an algorithm achieving tight O(log(1/α)) competitive ratio. When the seller has no information about the future trends of buyers' valuations, we prove Ω(log B) lower bound and show that there is an algorithm with tight O(log B) competitive ratio by modeling the problem as adversarial bandits with knapsacks optimization. Extensive simulation studies show that our algorithm outperforms previous mechanisms in various settings.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796973"}, {"primary_key": "1711308", "vector": [], "sparse_vector": [], "title": "PolarScheduler: Dynamic Transmission Control for Floating LoRa Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Huadong Ma"], "summary": "LoRa is widely deploying in aquatic environments to support various Internet of Things applications. However, floating LoRa networks suffer from serious performance degradation due to the polarization loss caused by the swaying antenna. Existing methods that only control the transmission starting from the aligned attitude have limited improvement due to the ignorance of aligned period length. In this paper, we propose PolarScheduler, a dynamic transmission control method for floating LoRa networks. PolarScheduler actively controls transmission configurations to match polarization aligned periods. We propose a V-zone model to capture diverse aligned periods under different configurations. We also design a low-cost model establishment method and an efficient optimal configuration searching algorithm to make full use of aligned periods. We implement PolarScheduler on commercial LoRa platforms and evaluate its performance in a deployed network. Extensive experiments show that PolarScheduler can improve the packet delivery rate and throughput by up to 20.0% and 15.7%, compared to the state-of-the-art method.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796656"}, {"primary_key": "1711309", "vector": [], "sparse_vector": [], "title": "Real-time Machine Learning for Symbol Detection in MIMO-OFDM Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, there have been renewed interests in applying machine learning (ML) techniques to wireless systems. Nevertheless, ML-based approaches often require a large amount of data in training, and prior ML-based MIMO symbol detectors usually adopt offline learning approaches, which are not applicable to real-time signal processing. This paper adopts echo state network (ESN), a prominent type of reservoir computing (RC), to the real-time symbol detection task in MIMO-OFDM systems. Two novel ESN training methods, namely recursive-least-square and generalized adaptive weighted recursive-least-square, are introduced to enhance the performance of ESN training. Furthermore, a decision feedback mechanism is adopted to improve training efficiency and BER performance. Simulation studies show that the proposed methods perform better than previous conventional and ML-based MIMO symbol detectors. Finally, the effectiveness of our RC-based approach is validated with a software-defined radio (SDR) transceiver and extensive field tests in various real-world scenarios. To the best of our knowledge, this is the first real-time SDR implementation for ML-based MIMO-OFDM symbol detectors. Our work strongly indicates that ML-based signal processing could be a promising and critical approach for future wireless networks.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796854"}, {"primary_key": "1711313", "vector": [], "sparse_vector": [], "title": "MC-Sketch: Enabling Heterogeneous Network Monitoring Resolutions with Multi-Class Sketch.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Nowadays, with the emergence of software-defined networking, sketch-based network measurements have been widely used to balance the tradeoff between efficiency and reliability. The simplicity and generality of a sketch-based system allow it to track divergent performance metrics and deal with heterogeneous traffic characteristics. However, most of the existing proposals mainly consider priority-agnostic measurements, which introduce equal error probability to different classes of traffic. While network measurements are usually task-oriented, e.g., traffic engineering or intrusion detection, a system operator may be interested only in tracking specific types of traffic and expect various levels of tracking resolutions for different traffic classes. To achieve this goal, we propose MC-Sketch (Multi-Class Sketch), a priority-aware system that provides various classes of traffic with differential accuracy subject to the limited resources of a programmable switch. It privileges higher priority traffic in accessing the sketch over background traffic and naturally provides heterogeneous tracking resolutions. The experimental results and large-scale analysis show that MC-Sketch reduces the measurement errors of high priority flows by 56.92% without harming the overall accuracy much.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796955"}, {"primary_key": "1711314", "vector": [], "sparse_vector": [], "title": "MDoC: Compromising WRSNs through Denial of Charge by Mobile Charger.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The discovery of wireless power transfer technology enables power transferred between transceivers in a wireless manner, thus generating the concept of wireless rechargeable sensor networks (WRSNs). Previous arts paid little attention to network security issues, making them prone to novel attacks. In this work, we focus on developing a denial of charge attack for WRSNs, which aims at corrupting network functionalities by manipulating the malicious mobile charger. We formalize the maximization of destructiveness problem (MAD) and propose a denial of charge attacking method, termed MDoC, with a performance guarantee to solve it. MDoC is composed of two attacking rounds, which first triggers sensors to send requests to create a request explosion phenomenon and then figures out the longest charging route to yield nodes starving to death as much as possible. Finally, extensive testbed experiments and simulations are conducted to verify the performance of MDoC. The results reveal that MDoC attack is able to exhaust at least 20% additional nodes without being noticed.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796835"}, {"primary_key": "1711315", "vector": [], "sparse_vector": [], "title": "MalGraph: Hierarchical Graph Neural Networks for Robust Windows Malware Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Zhenqing Qu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Shouling Ji"], "summary": "With the ever-increasing malware threats, malware detection plays an indispensable role in protecting information systems. Although tremendous research efforts have been made, there are still two key challenges hindering them from being applied to accurately and robustly detect malwares. Firstly, most of them represent executables with shallow features, but ignore their semantic and structural information. Secondly, they are primarily based on representations that can be easily modified by attackers and thus cannot provide robustness against adversarial attacks. To tackle the challenges, we present MalGraph, which first represents executables with hierarchical graphs and then uses an end-to-end learning framework based on graph neural networks for malware detection. In particular, a hierarchical graph consists of a function call graph that captures the interaction semantics among different functions at the inter-function level and corresponding control-flow graphs for learning the structural semantics of each function at the intra-function level. We argue the abstraction and hierarchy nature of hierarchical graphs makes them not only easy to capture rich structural information of executables, but also be immune to adversarial attacks. Evaluations show that MalGraph not only outperforms state-of-the-art malware detection, but also exhibits stronger robustness against adversarial attacks by a large margin.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796786"}, {"primary_key": "1711316", "vector": [], "sparse_vector": [], "title": "Towards an Efficient Defense against Deep Learning based Website Fingerprinting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Website fingerprinting (WF) attacks allow an attacker to eavesdrop on the encrypted network traffic between a victim and an anonymous communication system so as to infer the real destination websites visited by a victim. Recently, the deep learning (DL) based WF attacks are proposed to extract high level features by DL algorithms to achieve better performance than that of the traditional WF attacks and defeat the existing defense techniques. To mitigate this issue, we propose a-genetic-programming-based variant cover traffic search technique to generate defense strategies for effectively injecting dummy Tor cells into the raw Tor traffic. We randomly perform mutation operations on labeled original traffic traces by injecting dummy Tor cells into the traces to derive variant cover traffic. A high level feature distance based fitness function is designed to improve the mutation rate to discover successful variant traffic traces that can fool the DL-based WF classifiers. Then the dummy Tor cell injection patterns in the successful variant traces are extracted as defense strategies that can be applied to the Tor traffic. Extensive experiments demonstrate that we can introduce 8.1% of bandwidth overhead to significantly decrease the accuracy rate below 0.4% in the realistic open-world setting.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796685"}, {"primary_key": "1711317", "vector": [], "sparse_vector": [], "title": "Escala: Timely Elastic Scaling of Control Channels in Network Measurement.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Dezhang Kong", "Jinbo Sun", "<PERSON>", "<PERSON><PERSON> Zhou", "<PERSON><PERSON>"], "summary": "In network measurement, data plane switches measure traffic and report events (e.g., heavy hitters) to the control plane via control channels. The control plane makes decisions to process events. However, current network measurement suffers from two problems. First, when traffic bursts occur, massive events are reported in a short time so that the control channels may be overloaded due to limited bandwidth capacity. Second, only a few events are reported in normal cases, making control channels underloaded and wasting network resources. In this paper, we propose Escala to provide the elastic scaling of control channels at runtime. The key idea is to dynamically migrate event streams among control channels to regulate the loads of these channels. Escala offers two components, including an Escala monitor that detects scaling situations based on realtime network statistics, and an optimization framework that makes scaling decisions to eliminate overload and underload situations. We have implemented a prototype of Escala on Tofino-based switches. Extensive experiments show that Escala achieves timely elastic scaling while preserving high application-level accuracy.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796830"}, {"primary_key": "1711318", "vector": [], "sparse_vector": [], "title": "Revisiting RFID Missing Tag Identification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We revisit the problem of missing tag identification in RFID networks by making three contributions. Firstly, we quantitatively compare and gauge the existing propositions spanning over a decade on missing tag identification. We show that the expected execution time of the best solution in the literature is $\\Theta \\left( {N + \\frac{{{{(1 - \\alpha )}^2}{{(1 - \\delta )}^2}}}{{{\\varepsilon ^2}}}} \\right)$, where δ and ϵ are parameters quantifying the required identification accuracy, N denotes the number of tags in the system, among which αN tags are missing. Secondly, we analytically establish the expected execution time lower-bound for any missing tag identification algorithm as $\\Theta \\left( {\\frac{N}{{\\log N}} + \\frac{{{{(1 - \\delta )}^2}{{(1 - \\alpha )}^2}}}{{{\\varepsilon ^2}\\log \\frac{{(1 - \\delta )(1 - \\alpha )}}{\\varepsilon }}}} \\right)$, thus giving the theoretical performance limit. Thirdly, we develop a novel missing tag identification algorithm by leveraging a tree structure with the expected execution time of $\\Theta \\left( {\\frac{{\\log \\log N}}{{\\log N}}N + \\frac{{{{(1 - \\alpha )}^2}{{(1 - \\delta )}^2}}}{{{\\varepsilon ^2}}}} \\right)$, reducing the time overhead by a factor of up to log N over the best algorithm in the literature. The key technicality in our design is a novel data structure termed as collision-partition tree (CPT), built on a subset of bits in tag pseudo-IDs, leading to more balanced tree structure and reducing the time complexity in parsing the entire tree.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796971"}, {"primary_key": "1711320", "vector": [], "sparse_vector": [], "title": "Backdoor Defense with Machine Unlearning.", "authors": ["<PERSON>", "<PERSON><PERSON> Fan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Backdoor injection attack is an emerging threat to the security of neural networks, however, there still exist limited effective defense methods against the attack. In this paper, we propose BAERASER, a novel method that can erase the backdoor injected into the victim model through machine unlearning. Specifically, BAERASER mainly implements backdoor defense in two key steps. First, trigger pattern recovery is conducted to extract the trigger patterns infected by the victim model. Here, the trigger pattern recovery problem is equivalent to the one of extracting an unknown noise distribution from the victim model, which can be easily resolved by the entropy maximization based generative model. Subsequently, BAERASER leverages these recovered trigger patterns to reverse the backdoor injection procedure and induce the victim model to erase the polluted memories through a newly designed gradient ascent based machine unlearning method. Compared with the previous machine unlearning solutions, the proposed approach gets rid of the reliance on the full access to training data for retraining and shows higher effectiveness on backdoor erasing than existing fine-tuning or pruning methods. Moreover, experiments show that BAERASER can averagely lower the attack success rates of three kinds of state-of-the-art backdoor attacks by 99% on four benchmark datasets.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796974"}, {"primary_key": "1711321", "vector": [], "sparse_vector": [], "title": "Physical-World Attack towards WiFi-based Behavior Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jinsong Han", "<PERSON>", "<PERSON><PERSON>"], "summary": "Behavior recognition plays an essential role in numerous behavior-driven applications (e.g., virtual reality and smart home) and even in the security-critical applications (e.g., security surveillance and elder healthcare). Recently, WiFi-based behavior recognition (WBR) technique stands out among many behavior recognition techniques due to its advantages of being non-intrusive, device-free, and ubiquitous. However, existing WBR research mainly focuses on improving the recognition precision, while neglecting the security aspects. In this paper, we reveal that WBR systems are vulnerable to manipulating physical signals. For instance, our observation shows that WiFi signals can be changed by jamming signals. By exploiting the vulnerability, we propose two approaches to generate physically online adversarial samples to perform untargeted attack and targeted attack, respectively. The effectiveness of these attacks are extensively evaluated over four real-world WBR systems. The experiment results show that our attack approaches can achieve 80% and 60% success rates for untargeted attack and targeted attack in physical world, respectively. We also propose three methods to mitigate the hazard of such attacks.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796920"}, {"primary_key": "1711323", "vector": [], "sparse_vector": [], "title": "Ao2I: Minimizing Age of Outdated Information to Improve Freshness in Data Collection.", "authors": ["Qing<PERSON>", "Chengzhang Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, it has been recognized that there is a serious limitation with the original Age of Information (AoI) metric in terms of quantifying true freshness of information content. A new metric, called Age of Incorrect Information (AoII), has been proposed. By further refining this new metric with practical considerations, we introduce Age of Outdated Information (Ao 2 I) metric. In this paper, we investigate a scheduling problem for minimizing Ao 2 I in an IoT data collection network. We derive a theoretical lower bound for the minimum Ao 2 I that any scheduler can achieve. Then we present Heh—a low-complexity online scheduler. The design of Heh is based on the estimation of a novel offline scheduling priority metric in the absence of knowledge of the future. We prove that at each time, transmitting one source with the largest offline scheduling priority metric minimizes Ao 2 I. Through extensive simulations, we show that the lower bound is very tight and that the Ao 2 I obtained by Heh is close-to-optimal.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796932"}, {"primary_key": "1711324", "vector": [], "sparse_vector": [], "title": "Experimental Design Networks: A Paradigm for Serving Heterogeneous Learners under Networking Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "Yuanyuan Li", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Significant advances in edge computing capabilities enable learning to occur at geographically diverse locations. In general, the training data needed in those learning tasks are not only heterogeneous but also not fully generated locally. In this paper, we propose an experimental design network paradigm, wherein learner nodes train possibly different Bayesian linear regression models via consuming data streams generated by data source nodes over a network. We formulate this problem as a social welfare optimization problem in which the global objective is defined as the sum of experimental design objectives of individual learners, and the decision variables are the data transmission strategies subject to network constraints. We first show that, assuming Poisson data streams, the global objective is a continuous DR-submodular function. We then propose a Frank-Wolfe type algorithm that outputs a solution within a 1 – 1/e factor from the optimal. Our algorithm contains a novel gradient estimation component which is carefully designed based on Poisson tail bounds and sampling. Finally, we complement our theoretical findings through extensive experiments. Our numerical evaluation shows that the proposed algorithm outperforms several baseline algorithms both in maximizing the global objective and in the quality of the trained models.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796907"}, {"primary_key": "1711326", "vector": [], "sparse_vector": [], "title": "Distributed Cooperative Caching in Unreliable Edge Environments.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Caching popular contents at the network edge is promising to reduce the retrieval latency, the network congestion, and the number of requests to the remote content provider during peak hours. In general, edge caching resource is costly and highly limited. Nevertheless, it is possible to provide cost-effective caching services using unreliable resources, which are resources reserved for other applications but have not been fully used or resources on vulnerable servers. In this paper, we consider the problem of caching popular contents over unreliable resources as a less expensive solution to limited edge caching capacity. In particular, to address the unreliability of edge resources, erasure coding is leveraged to increase the availability of cached contents. We formulate the problem as a discrete optimization problem and prove it is NP-hard. We start with two special cases of the problem and provide optimal algorithms for them. We then design an algorithm for the general version of the proposed problem and provide a provable performance guarantee. Real-world data-driven simulations demonstrate that the proposed algorithms significantly outperform popular baselines, and the rewards for the general version of the problem are near-optimal.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796799"}, {"primary_key": "1711328", "vector": [], "sparse_vector": [], "title": "RF-Protractor: Non-Contacting Angle Tracking via COTS RFID in Industrial IoT Environment.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "As a key component of most machines, the status of the rotation shaft is a crucial issue in the factories, which affects both the industrial safety and the product quality. Tracking the rotation angle can efficiently monitor the status of the rotation shaft, but traditional solutions either rely on the specialized sensors, suffering from intrusive transformation, or use the computer vision-based solutions, suffering from poor light conditions. In this paper, we present a non-contacting low-cost angle tracking solution, RF-Protractor, to track the rotation shaft based on the surrounding RFID tags. Particularly, instead of directly attaching the tags to the shaft, which may lead to serious miss reading problems due to metal interference, we deploy the tags beside the shaft and leverage the polarization effect of the reflection signal from the shaft for angle tracking. To improve the polarization effect, we exploit the linear polarization feature by using the linear shaft turntable or placing a light aluminum foil on the shaft turntable, which requires no transformation of the shaft. We firstly build a polarization model to quantify the relationship between the rotation angle and the reflection signal. To extract the accurate reflection signal, we then propose to combine the signals of multiple tags to cancel the reflection effect and then estimate the environment-related parameter to calibrate the model. Finally, we propose to leverage both the power trend and the IQ signal to estimate the rotation direction and the rotation angle. We have implemented a real system and the extensive experiments in the real environment confirm the effectiveness of RF-Protractor, which achieves an average error of about 3.1° in angle tracking.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796839"}, {"primary_key": "1711329", "vector": [], "sparse_vector": [], "title": "Worker Selection Towards Data Completion for Online Sparse Crowdsensing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As a cost-effective paradigm, Sparse Crowdsensing aims to recruit workers to perform a part of sensing tasks and infer the rest, which has broad applications, including environmental monitoring, urban sensing, etc. In most cases, workers will participate in real time, and thus their sensing data are coming dynamically. Taking full advantage of the online coming data to complete the full sensing map is an important problem for Sparse Crowdsensing. However, for data completion, the importance of data collected from different spatio-temporal areas is usually different and time-varying. For example, the newly obtained data in the center is often more important than the old ones from edges. Moreover, the area importance may also influence the following worker selection, i.e., selecting suitable workers to actively sense important areas (instead of passively waiting for given data) for improving completion accuracy. To this end, in this paper, we propose a framework for online Sparse Crowdsensing, called OS-MCS, which consists of three parts: matrix completion, importance estimation, and worker selection. We start from the dynamically coming data and propose an online matrix completion algorithm with spatio-temporal constraints. Based on that, we estimate the spatio-temporal area importance by conducting a reinforcement learning-based up-to-date model. Finally, we utilize the prophet secretary problem to select suitable workers to sense important areas for accurate completion in an online manner. Extensive experiments on real-world data sets show the effectiveness of our proposals.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796743"}, {"primary_key": "1711331", "vector": [], "sparse_vector": [], "title": "The Right to be Forgotten in Federated Learning: An Efficient Realization with Rapid Retraining.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>g Yuan", "<PERSON><PERSON>", "<PERSON>"], "summary": "In Machine Learning, the emergence of \\textit{the right to be forgotten} gave birth to a paradigm named \\textit{machine unlearning}, which enables data holders to proactively erase their data from a trained model. Existing machine unlearning techniques focus on centralized training, where access to all holders' training data is a must for the server to conduct the unlearning process. It remains largely underexplored about how to achieve unlearning when full access to all training data becomes unavailable. One noteworthy example is Federated Learning (FL), where each participating data holder trains locally, without sharing their training data to the central server. In this paper, we investigate the problem of machine unlearning in FL systems. We start with a formal definition of the unlearning problem in FL and propose a rapid retraining approach to fully erase data samples from a trained FL model. The resulting design allows data holders to jointly conduct the unlearning process efficiently while keeping their training data locally. Our formal convergence and complexity analysis demonstrate that our design can preserve model utility with high efficiency. Extensive evaluations on four real-world datasets illustrate the effectiveness and performance of our proposed realization.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796721"}, {"primary_key": "1711332", "vector": [], "sparse_vector": [], "title": "When Deep Learning Meets Steganography: Protecting Inference Privacy in the Dark.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Hongbo Jiang", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While cloud-based deep learning benefits for high-accuracy inference, it leads to potential privacy risks when exposing sensitive data to untrusted servers. In this paper, we work on exploring the feasibility of steganography in preserving inference privacy. Specifically, we devise GHOST and GHOST+, two private inference solutions employing steganography to make sensitive images invisible in the inference phase. Motivated by the fact that deep neural networks (DNNs) are inherently vulnerable to adversarial attacks, our main idea is turning this vulnerability into the weapon for data privacy, enabling the DNN to misclassify a stego image into the class of the sensitive image hidden in it. The main difference is that GHOST retrains the DNN into a poisoned network to learn the hidden features of sensitive images, but GHOST+ leverages a generative adversarial network (GAN) to produce adversarial perturbations without altering the DNN. For enhanced privacy and a better computation-communication trade-off, both solutions adopt the edge-cloud collaborative framework. Compared with the previous solutions, this is the first work that successfully integrates steganography and the nature of DNNs to achieve private inference while ensuring high accuracy. Extensive experiments validate that steganography has excellent ability in accuracy-aware privacy protection of deep learning.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796975"}, {"primary_key": "1711335", "vector": [], "sparse_vector": [], "title": "Separating Voices from Multiple Sound Sources using 2D Microphone Array.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> Gu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Voice assistant has been widely used for human-computer interaction and automatic meeting minutes. However, for multiple sound sources, the performance of speech recognition in voice assistant decreases dramatically. Therefore, it is crucial to separate multiple voices efficiently for an effective voice assistant application in multi-user scenarios. In this paper, we present a novel voice separation system using a 2D microphone array in multiple sound source scenarios. Specifically, we propose a spatial filtering-based method to iteratively estimate the Angle of Arrival (AoA) of each sound source and separate the voice signals with adaptive beamforming. We use BeamForming-based cross-Correlation (BF-Correlation) to accurately assess the performance of beamforming and automatically optimize the voice separation in the iterative framework. Different from cross-correlation, BF-Correlation further performs cross-correlation among the after-beamforming voice signals processed with each linear microphone array. In this way, the mutual interference from voice signals out of the specified direction can be effectively suppressed or mitigated via the spatial filtering technique. We implement a prototype system and evaluate its performance in real environments. Experimental results show that the average AoA error is 1.4 degree and the average ratio of automatic speech recognition accuracy is 90.2% in the presence of three sound sources.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796768"}, {"primary_key": "1711336", "vector": [], "sparse_vector": [], "title": "Optimizing Task Placement and Online Scheduling for Distributed GNN Training Acceleration.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Training Graph Neural Networks (GNN) on large graphs is resource-intensive and time-consuming, mainly due to the large graph data that cannot be fit into the memory of a single machine, but have to be fetched from distributed graph storage and processed on the go. Unlike distributed deep neural network (DNN) training, the bottleneck in distributed GNN training lies largely in large graph data transmission for constructing mini-batches of training samples. Existing solutions often advocate data-computation colocation, and do not work well with limited resources where the colocation is infeasible. The potentials of strategical task placement and optimal scheduling of data transmission and task execution have not been well explored. This paper designs an efficient algorithm framework for task placement and execution scheduling of distributed GNN training, to better resource utilization, improve execution pipelining, and expediting training completion. Our framework consists of two modules: (i) an online scheduling algorithm that schedules the execution of training tasks, and the data transmission plan; and (ii) an exploratory task placement scheme that decides the placement of each training task. We conduct thorough theoretical analysis, testbed experiments and simulation studies, and observe up to 67% training speed-up with our algorithm as compared to representative baselines.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796910"}, {"primary_key": "1711338", "vector": [], "sparse_vector": [], "title": "Tackling System and Statistical Heterogeneity for Federated Learning with Adaptive Client Sampling.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated learning (FL) algorithms usually sample a fraction of clients in each round (partial participation) when the number of participants is large and the server's communication bandwidth is limited. Recent works on the convergence analysis of FL have focused on unbiased client sampling, e.g., sampling uniformly at random, which suffers from slow wall-clock time for convergence due to high degrees of system heterogeneity and statistical heterogeneity. This paper aims to design an adaptive client sampling algorithm that tackles both system and statistical heterogeneity to minimize the wall-clock convergence time. We obtain a new tractable convergence bound for FL algorithms with arbitrary client sampling probabilities. Based on the bound, we analytically establish the relationship between the total learning time and sampling probabilities, which results in a non-convex optimization problem for training time minimization. We design an efficient algorithm for learning the unknown parameters in the convergence bound and develop a low-complexity algorithm to approximately solve the non-convex problem. Experimental results from both hardware prototype and simulation demonstrate that our proposed sampling scheme significantly reduces the convergence time compared to several baseline sampling schemes. Notably, our scheme in hardware prototype spends 73% less time than the uniform sampling baseline for reaching the same target loss.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796935"}, {"primary_key": "1711339", "vector": [], "sparse_vector": [], "title": "Efficient Pipeline Planning for Expedited Distributed DNN Training.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Xiaodong Yi", "<PERSON><PERSON>", "Shiqing Fan", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To train modern large DNN models, pipeline parallelism has recently emerged, which distributes the model across GPUs and enables different devices to process different microbatches in pipeline. Earlier pipeline designs allow multiple versions of model parameters to co-exist (similar to asynchronous training), and cannot ensure the same model convergence and accuracy performance as without pipelining. Synchronous pipelining has recently been proposed which ensures model performance by enforcing a synchronization barrier between training iterations. Nonetheless, the synchronization barrier requires waiting for gradient aggregation from all microbatches and thus delays the training progress. Optimized pipeline planning is needed to minimize such wait and hence the training time, which has not been well studied in the literature. This paper designs efficient, near-optimal algorithms for expediting synchronous pipeline-parallel training of modern large DNNs over arbitrary inter-GPU connectivity. Our algorithm framework comprises two components: a pipeline partition and device mapping algorithm, and a pipeline scheduler that decides processing order of microbatches over the partitions, which together minimize the per-iteration training time. We conduct thorough theoretical analysis, extensive testbed experiments and trace-driven simulation, and demonstrate our scheme can accelerate training up to 157% compared with state-of-the-art designs.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796787"}, {"primary_key": "1711340", "vector": [], "sparse_vector": [], "title": "VITA: Virtual Network Topology-aware Southbound Message Delivery in Clouds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Southbound message delivery from the control plane to the data plane is one of the essential issues in multi-tenant clouds. A natural method of southbound message delivery is that the control plane directly communicates with compute nodes in the data plane. However, due to the large number of compute nodes, this method may result in massive control overhead. The Message Queue (MQ) model can solve this challenge by aggregating and distributing messages to queues. Existing MQ-based solutions often perform message aggregation based on the physical network topology, which do not align with the fundamental requirements of southbound message delivery, leading to high message redundancy on compute nodes. To address this issue, we design and implement VITA, the first-of-its-kind work on virtual network topology-aware southbound message delivery. However, it is intractable to optimally deliver southbound messages according to the virtual attributes of messages. Thus, we design two algorithms, submodular-based approximation algorithm and simulated annealing-based algorithm, to solve different scenarios of the problem. Both experiment and simulation results show that VITA can reduce the total traffic amount of redundant messages by 45%-75% and reduce the control overhead by 33%-80% compared with state-of-the-art solutions.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796938"}, {"primary_key": "1711341", "vector": [], "sparse_vector": [], "title": "Lumos: towards Better Video Streaming QoE through Accurate Throughput Prediction.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Qinghua Wu", "<PERSON><PERSON>", "Zhenyu Li", "<PERSON><PERSON><PERSON>"], "summary": "ABR algorithms dynamically select the bitrate of chunks based on the network capacity. To estimate the network capacity, most ABR algorithms use throughput prediction while recent works start to leverage delivery time prediction. We in this paper examine all components of the predictor for ABR algorithms, i.e., input features, mapping function and output target. We build an automated video streaming measurement platform, and collect extensive dataset under various network environments, containing 2500+ video sessions. Through analysis, we find that most of previous works failed to achieve accurate prediction due to ignoring how application behavior influences application throughput, e.g., the strong correlation between chunk size and throughput. Then we identify underlying factors affecting this correlation, and consider them as features for more accurate prediction. Moreover, we show that throughput is a better target for data-driven predictors than delivery time in terms of prediction error, due to the long tail distribution of delivery time. Based on those above, we propose a decision-tree-based throughput predictor, named Lumos, which acts as a plug-in for ABR algorithms. Extensive experiments in real-world Internet demonstrate that Lumos achieves high prediction accuracy and improves the QoE of ABR algorithms when integrated into them.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796948"}, {"primary_key": "1711342", "vector": [], "sparse_vector": [], "title": "AutoByte: Automatic Configuration for Optimal Communication Scheduling in DNN Training.", "authors": ["Yiqing Ma", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "ByteScheduler partitions and rearranges tensor transmissions to improve the communication efficiency of distributed Deep Neural Network (DNN) training. The configuration of hyper-parameters (i.e., the partition size and the credit size) is critical to the effectiveness of partitioning and rearrangement. Currently, ByteScheduler adopts Bayesian Optimization (BO) to find the optimal configuration for the hyper-parameters beforehand. In practice, however, various runtime factors (e.g., worker node status and network conditions) change over time, making the statically-determined one-shot configuration result suboptimal for real-world DNN training.To address this problem, we present a real-time configuration method (called AutoByte) that automatically and timely searches the optimal hyper-parameters as the training systems dynamically change. AutoByte extends the ByteScheduler framework with a meta-network, which takes the system's runtime statistics as its input and outputs predictions for speedups under specific configurations. Evaluation results on various DNN models show that AutoByte can dynamically tune the hyper-parameters with low resource usage, and deliver up to 33.2% higher performance than the best static configuration in ByteScheduler.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796752"}, {"primary_key": "1711347", "vector": [], "sparse_vector": [], "title": "Joint Resource Management and Flow Scheduling for SFC Deployment in Hybrid Edge-and-Cloud Network.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network Function Virtualization (NFV) migrates network functions from proprietary hardware to commercial servers on the edge or cloud, making network services more cost-efficient, manage-convenient, and flexible. To facilitate these advantages, it is critical to find an optimal deployment of the chained virtual network functions, i.e. service function chains (SFCs), in hybrid edge-and-cloud environment, considering both resource and latency. It is an NP-hard problem. In this paper, we first limit the problem at the edge and design a constant approximation algorithm named chained next fit (CNF), where a sub-algorithm called double spanning tree (DST) is designed to deal with virtual network embedding. Then we take both cloud and edge resources into consideration and create a promotional algorithm called decreasing sorted, chained next fit (DCNF), which also has a provable constant approximation ratio. The simulation results demonstrate that the ratio between DCNF and the optimal solution is much smaller than the theoretical bound, approaching an average of 1.25. Moreover, DCNF always has a better performance than the benchmarks, which implies that it is a good candidate for joint resource and latency optimization in hybrid edge-and-cloud networks.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796884"}, {"primary_key": "1711348", "vector": [], "sparse_vector": [], "title": "Provably Efficient Algorithms for Traffic-sensitive SFC Placement and Flow Routing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Network Function Virtualization (NFV) has the potential of cost-efficiency, manage-convenience, and flexibility but meanwhile poses challenges for the service function chain (SFC) deployment problem, which is NP-hard. It is so complicated that existing work conspicuously neglects the flow changes along the chains and only gives heuristic algorithms without a performance guarantee. In this paper, we fill this gap by formulating a traffic-sensitive online joint SFC placement and flow routing (TO-JPR) model and proposing a novel two-stage scheme to solve it. Moreover, we design a dynamic segmental packing (DSP) algorithm for the first stage, which not only maintains the minimal traffic burden for the network but also achieves an approximation ratio of 2 on the resource cost. Such a two-stage scheme and DSP can pave the way for efficiently solving TO-JPR. For example, simply applying the nearest neighbor (NN) algorithm for the second stage can guarantee a global approximation ratio of O(log(M)) on the network latency, where M is the number of servers. More future work can be done based on our scheme to get better performance on the network latency. Finally, we perform extensive simulations to demonstrate the outstanding performance of DSP+NN compared with the optimal solutions and benchmarks.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796690"}, {"primary_key": "1711359", "vector": [], "sparse_vector": [], "title": "A Comparative Measurement Study of Commercial 5G mmWave Deployments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "5G-NR is beginning to be widely deployed in the mmWave frequencies in urban areas in the US and around the world. Due to the directional nature of mmWave signal propagation, improving performance of such deployments heavily relies on beam management and deployment configurations. We perform detailed measurements of mmWave 5G deployments by two major commercial 5G operators in the US in two diverse environments: an open field with a baseball park (BP) and a downtown urban canyon region (DT), using smartphone-based tools that collect detailed measurements across several layers (PHY, MAC and up) such as beam-specific metrics like signal strength, beam switch times, and throughput per beam. Our measurement analysis shows that the parameters of the two deployments differ in a number of aspects: number of beams used, number of channels aggregated, and density of deployments, which reflect on the throughput performance. Our measurement-driven propagation analysis demonstrates that narrower beams experience a lower path-loss exponent than wider beams, which combined with up to eight frequency channels aggregated on up to eight beams can deliver a peak throughput of 1.2 Gbps at distances greater than 100m.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796693"}, {"primary_key": "1711362", "vector": [], "sparse_vector": [], "title": "TrojanFlow: A Neural Backdoor Attack to Deep Learning-based Network Traffic Classifiers.", "authors": ["<PERSON><PERSON>", "Chunsheng Xin", "<PERSON><PERSON>"], "summary": "While deep learning (DL)-based network traffic classification has demonstrated its success in a range of practical applications, such as network management and security control to just name a few, it is vulnerable to adversarial attacks. This paper reports TrojanFlow, a new and practical neural backdoor attack to DL-based network traffic classifiers. In contrast to traditional neural backdoor attacks where a designated and sample-agnostic trigger is used to plant backdoor, TrojanFlow poisons a model using dynamic and sample-specific triggers that are optimized to efficiently hijack the model. It features a unique design to jointly optimize the trigger generator with the target classifier during training. The trigger generator can thus craft optimized triggers based on the input sample to efficiently manipulate the model's prediction. A well-engineered prototype is developed using Pytorch to demonstrate TrojanFlow attacking multiple practical DL-based network traffic classifiers. Thorough analysis is conducted to gain insights into the effectiveness of TrojanFlow, revealing the fundamentals of why it is effective and what it does to efficiently hijack the model. Extensive experiments are carried out on the well-known ISCXVPN2016 dataset with three widely adopted DL network traffic classifier architectures. TrojanFlow is compared with two other backdoor attacks under five state-of-the-art backdoor defenses. The results show that the TrojanFlow attack is stealthy, efficient, and highly robust against existing neural backdoor mitigation schemes.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796878"}, {"primary_key": "1711364", "vector": [], "sparse_vector": [], "title": "Lightweight Trilinear Pooling based Tensor Completion for Network Traffic Monitoring.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Zhang"], "summary": "Network traffic engineering and anomaly detection rely heavily on network traffic measurement. Due to the lack of infrastructure to measure all points of interest, the high measurement cost, and the unavoidable transmission loss, network monitoring systems suffer from the problem that the network traffic data are incomplete with only a subset of paths or time slots measured. Recent studies show that tensor completion can be applied to infer the missing traffic data from partial measurements. Although promising, the interaction model adopted in current tensor completion algorithms can only capture linear and simple correlations in the traffic data, which compromises the recovery performance. To solve the problem, we propose a new tensor completion scheme based on Lightweight Trilinear Pooling, which designs (1) a Trilinear Pooling, a new multi-modal fusion method to model the interaction function to capture the complex correlations, (2) a low-rank decomposition based neural network compression method to reduce the storage and computation complexity, (3) an attention enhanced LSTM to encode and incorporate the temporal patterns in the tensor completion scheme. The extensive experiments on three real-world network traffic datasets demonstrate that our scheme can significantly reduce the error in missing data recovery with fast speed using small storage.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796873"}, {"primary_key": "1711365", "vector": [], "sparse_vector": [], "title": "LSAB: Enhancing Spatio-Temporal Efficiency of AoA Tracking Systems.", "authors": ["Qingrui Pan", "Z<PERSON><PERSON> An", "<PERSON><PERSON><PERSON><PERSON> Lin", "<PERSON><PERSON>"], "summary": "Estimating the angle-of-arrival (AoA) of an RF source by using a large-sized antenna array is a classical topic in wireless systems. However, AoA tracking systems are not yet used for Internet of Things (IoT) in real world due to their unaffordable cost. Many efforts, such as a time-sharing array, emulated array and sparse array, were recently made to cut the cost. This work introduces a log-spiral antenna belt (LSAB), a new novel sparse \"planar array\" that could estimate the AoA of an IoT device in 3D space by using a few antennas connected to a single timeshare channel. Unlike the conventional arrays, LSAB deploys antennas on a log-spiral-shaped belt in a non-linear manner, following the theory of minimum resolution redundancy newly discovered in this work. One physical 8×8 uniform planar array (UPA) and four logical sparse arrays, including LSAB, were prototyped to validate the theory and evaluate the performance of sparse arrays. The extensive benchmark demonstrates that the performance of LSAB was comparable to that of a UPA, with similar degree of resolution; and LSAB could provide over 40% performance improvement than existing sparse arrays.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796679"}, {"primary_key": "1711366", "vector": [], "sparse_vector": [], "title": "Optimizing Sampling for Data Freshness: Unreliable Transmissions with Random Two-way Delay.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "In this paper, we study a sampling problem in which fresh samples of a signal (source) are sent through an unreliable channel to a remote estimator, and acknowledgments are sent back over a feedback channel. Both the forward and feedback channels are subject to random transmission times. Motivated by distributed sensing, the estimator can estimate the real-time value of the source signal by combining the signal samples received through the channel and noisy signal observations collected from a local sensor. We prove that the estimation error is a non-decreasing function of the Age of Information (AoI) for received signal samples and design an optimal sampling strategy that minimizes the long-term average estimation error. The optimal sampler design follows a threshold strategy: If the last transmission was successful, the source waits until the expected estimation error upon delivery exceeds a threshold and then sends out a new sample. If the last transmission fails, the source immediately sends out a new sample without waiting. The threshold is the unique root of a fixed-point equation and can be solved with low complexity (e.g., by bisection search). In addition, the proposed sampling strategy is also optimal for minimizing the long-term average of general non-decreasing functions of the AoI. Its optimality holds for general transmission time distributions of the forward and feedback channels.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796895"}, {"primary_key": "1711367", "vector": [], "sparse_vector": [], "title": "Retention-Aware Container Caching for Serverless Edge Computing.", "authors": ["Li Pan", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Liu"], "summary": "Serverless edge computing adopts an event-based model where Internet-of-Things (IoT) services are executed in lightweight containers only when requested, leading to significantly improved edge resource utilization. Unfortunately, the startup latency of containers degrades the responsiveness of IoT services dramatically. Container caching, while masking this latency, requires retaining resources thus compromising resource efficiency. In this paper, we study the retention-aware container caching problem in serverless edge computing. We leverage the distributed and heterogeneous nature of edge platforms and propose to optimize container caching jointly with request distribution. We reveal step by step that this joint optimization problem can be mapped to the classic ski-rental problem. We first present an online competitive algorithm for a special case where request distribution and container caching are based on a set of carefully designed probability distribution functions. Based on this algorithm, we propose an online algorithm called O-RDC for the general case, which incorporates the resource capacity and network latency by opportunistically distributing requests. We conduct extensive experiments to examine the performance of the proposed algorithms with both synthetic and real-world serverless computing traces. Our results show that ORDC outperforms existing caching strategies of current serverless computing platforms by up to 94.5% in terms of the overall system cost.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796705"}, {"primary_key": "1711368", "vector": [], "sparse_vector": [], "title": "Add/Drop Flexibility and System Complexity Tradeoff in ROADM Designs.", "authors": ["<PERSON><PERSON>", "Tong Ye"], "summary": "As a key component in dynamic wavelength-routing optical networks (WRONs), the contention performance at add/drop (A/D) ports of ROADMs has attracted a lot of attention in recent years. For the first time, this paper derives the close-form solutions of the blocking probability (BP) of A/D requests to characterize the fundamental tradeoff between A/D flexibility and system complexity in the ROADM. We show that the ROADM with fiber cross-connect (FXC) can decide whether a request can be satisfied based on global transceiver-usage information, and thus an exact expression of BP can be obtained. In comparison, the ROADM without FXC needs detail transceiver-usage information to make decision, and thus is hard to be analyzed. To circumvent the difficulty in analysis, we describe the evolution of the number of busy transceivers using a one-dimensional Markov chain, where the state transition rates are estimated from global transceiver-usage information. Based on this model, we obtain an approximate BP the accuracy of which is high enough for currently commercialized ROADMs and increases with the number of drop ports. Our analytical results provide an interesting insight into the tradeoff between A/D flexibility and system complexity, based on which we give some suggestions for system design.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796692"}, {"primary_key": "1711369", "vector": [], "sparse_vector": [], "title": "Towards Online Privacy-preserving Computation Offloading in Mobile Edge Computing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Zhetao Li"], "summary": "Mobile Edge Computing (MEC) is a new paradigm where mobile users can offload computation tasks to the nearby MEC server to reduce their resource consumption. Some works have pointed out that the true amount of offloaded tasks may reveal the sensitive information (e.g., device usage pattern and location information) of users, and proposed several privacy-preserving offloading mechanisms. However, to the best of our knowledge, none of them can provide strict and provable privacy guarantee. In this paper, we focus on the privacy leakage issue in computation offloading in MEC with a honest-but-curious server, and propose a novel online privacy-preserving computation offloading mechanism, called OffloadingGuard, to generate efficient offloading strategies for users in real time, which provide strict user privacy guarantee while minimizing the total cost of task computation. To this end, we design a deep reinforcement learning-based offloading model which allows each user to adaptively determine the satisfactory perturbed offloading ratio according to the time-varying channel state at each time slot to achieve trade-off between user privacy and computation cost. In particular, to strictly protect the true amount of offloaded tasks and prevent the untrusted MEC server from revealing mobile users' privacy, a range-constrained Laplace distribution is designed to obfuscate the original offloading ratio of each user and restrict the perturbed offloading ratio in a rational range. OffloadingGuard is proved to satisfy ϵ-differential privacy, and extensive experiments demonstrate its effectiveness.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796748"}, {"primary_key": "1711372", "vector": [], "sparse_vector": [], "title": "Payment Channel Networks: Single-Hop Scheduling for Throughput Maximization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Payment channel networks (PCNs) have emerged as a scalability solution for blockchains built on the concept of a payment channel: a setting that allows two parties to safely transact between themselves in high frequencies by updating pre-committed balances. Transaction requests in PCNs may be declined because of unavailability of funds due to temporary uneven distribution of the channel balances. In this paper, we investigate how to alleviate unnecessary payment blockage via proper prioritization of the transaction execution order. Specifically, we consider the scheduling problem in a payment channel: as transactions continuously arrive on both sides, nodes need to decide which ones to process and when, in order to maximize channel throughput. We introduce a stochastic model to capture the dynamics of a payment channel under discrete stochastic arrivals, with incoming transactions potentially held in buffers up until some deadline in order to enable more elaborate processing decisions. We describe a scheduling policy that maximizes the channel success rate/throughput, formally prove its optimality for fixed-amount transactions, and also show its superiority in the case of heterogeneous amounts via experiments in our discrete event simulator. Overall, our work is a step in the direction of formal research on improving PCN performance.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796862"}, {"primary_key": "1711373", "vector": [], "sparse_vector": [], "title": "FUME: Fuzzing Message Queuing Telemetry Transport Brokers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Cliff <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Message Queuing Telemetry Transport (MQTT) is a popular communication protocol used to interconnect devices with considerable network restraints, such as those found in Internet of Things (IoT). MQTT directly impacts a large number of devices, but the software security of its server (\"broker\") implementations is not well studied. In this paper, we design, implement, and evaluate a novel fuzz testing model for MQTT. The fuzzer combines aspects of mutation guided fuzzing and generation guided fuzzing to rigorously exhaust the MQTT protocol and identify vulnerabilities in servers. We introduce Markov chains for mutation guided fuzzing and generation guided fuzzing that model the fuzzing engine according to a finite Bernoulli process. We implement \"response feedback\", a novel technique which monitors network and console activity to learn which inputs trigger new responses from the broker. In total, we found 7 major vulnerabilities across 9 different MQTT implementations, including 6 zero-day vulnerabilities and 2 CVEs. We show that when fuzzing these popular MQTT targets, our fuzzer compares favorably with other state-of-the-art fuzzing frameworks, such as BooFuzz and AFLNet.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796755"}, {"primary_key": "1711374", "vector": [], "sparse_vector": [], "title": "Communication-Efficient Device Scheduling for Federated Learning Using Stochastic Optimization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Federated learning (FL) is a useful tool in distributed machine learning that utilizes users' local datasets in a privacy-preserving manner. When deploying FL in a constrained wireless environment; however, training models in a time-efficient manner can be a challenging task due to intermittent connectivity of devices, heterogeneous connection quality, and non-i.i.d. data. In this paper, we provide a novel convergence analysis of non-convex loss functions using FL on both i.i.d. and non-i.i.d. datasets with arbitrary device selection probabilities for each round. Then, using the derived convergence bound, we use stochastic optimization to develop a new client selection and power allocation algorithm that minimizes a function of the convergence bound and the average communication time under a transmit power constraint. We find an analytical solution to the minimization problem. One key feature of the algorithm is that knowledge of the channel statistics is not required and only the instantaneous channel state information needs to be known. Using the FEMNIST and CIFAR-10 datasets, we show through simulations that the communication time can be significantly decreased using our algorithm, compared to uniformly random participation.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796818"}, {"primary_key": "1711377", "vector": [], "sparse_vector": [], "title": "S-Store: A Scalable Data Store towards Permissioned Blockchain Sharding.", "authors": ["Xiaodong Qi"], "summary": "Sharding technique, which divides the whole network into multiple disjoint groups or committees, has been recognized as a revolutionary solution to enhance the scalability of blockchains. For account-based model, state data are partitioned over all committees and organized as Merkle trees to ensure data consistency and immutability. However, existing techniques on Merkle tree-based state storage fail to scale out due to a large amount of network and compute overheads incurred by data migration and Merkle tree reconstruction, respectively. In this paper, we propose $\\mathcal{S}$-Store, a scalable data storage technique towards permissioned blockchain sharding based on Aggregate Merkle B+ tree (AMB-tree). $\\mathcal{S}$-Store utilizes consistent hashing to reduce data migration among committees and uses split and merge on AMB-tree to decrease Merkle tree reconstruction overheads. $\\mathcal{S}$-Store also employs a novel committee addition protocol that guarantees the system service availability during data migration. Extensive experiments show that $\\mathcal{S}$-Sotre outperforms existing techniques by one order of magnitude in terms of transaction execution, data transmission, and committee addition.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796800"}, {"primary_key": "1711380", "vector": [], "sparse_vector": [], "title": "Landing Reinforcement Learning onto Smart Scanning of The Internet of Things.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Ma", "<PERSON><PERSON><PERSON>", "Hongqing Sang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhenhua Li", "<PERSON>", "<PERSON><PERSON>"], "summary": "Cyber search engines, such as Shodan and Censys, have gained popularity due to their strong capability of indexing the Internet of Things (IoT). They actively scan and fingerprint IoT devices for unearthing IP-device mapping. Because of the large address space of the Internet and the mapping's mutative nature, efficiently tracking the evolution of IP-device mapping with a limited budget of scans is essential for building timely cyber search engines. An intuitive solution is to use reinforcement learning to schedule more scans to networks with high churn rates of IP-device mapping. However, such an intuitive solution has never been systematically studied. In this paper, we take the first step toward demystifying this problem based on our experiences in maintaining a global IoT scanning platform. Inspired by the measurement study of large-scale real-world IoT scan records, we land reinforcement learning onto a system capable of smartly scanning IoT devices in a principled way. We disclose key parameters affecting the effectiveness of different scanning strategies, and find that our system would achieve growing advantages with the proliferation of IoT devices.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796737"}, {"primary_key": "1711381", "vector": [], "sparse_vector": [], "title": "Learning-based Multi-Drone Network Edge Orchestration for Video Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Unmanned aerial vehicles (a.k.a. drones) with high-resolution video cameras are useful for applications in e.g., public safety and smart farming. Inefficient configurations in drone video analytics applications due to edge network miscon-figurations can result in degraded video quality and inefficient resource utilization. In this paper, we present a novel scheme for offline/online learning-based network edge orchestration to achieve pertinent selection of both network protocols and video properties in multi-drone based video analytics. Our approach features both supervised and unsupervised machine learning algorithms to enable decision making for selection of both network protocols and video properties in the drones' pre-takeoff stage i.e., offline stage. In addition, our approach facilitates drone trajectory optimization during drone flights through an online reinforcement learning-based multi-agent deep Q-network algorithm. Evaluation results show how our offline orchestration can suitably choose network protocols (i.e., amongst TCP/HTTP, UDP/RTP, QUIC). We also demonstrate how our unsupervised learning approach outperforms existing learning approaches, and achieves efficient offloading while also improving the network performance (i.e., throughput and round-trip time) by least 25% with satisfactory video quality. Lastly, we show via trace-based simulations, how our online orchestration achieves 91% of oracle baseline network throughput performance with comparable video quality.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796706"}, {"primary_key": "1711389", "vector": [], "sparse_vector": [], "title": "An Efficient Two-Layer Task Offloading Scheme for MEC System with Multiple Services Providers.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the explosive growth of mobile and Internet of Things (IoT) applications, increasing Mobile Edge Computing (MEC) systems have been developed by diverse Edge Service Providers (ESPs), opening a new computing market with stiff competition. However, considering the spatiotemporally varying features of computation tasks, taking over all the received tasks alone may greatly degrade the service performance of the MEC system and lead to poor economical benefit. To this end, this paper proposes a two-layer collaboration model for ESPs. Each ESP can balance the computation workload among the internal edge nodes from the ESP and offload part of computation tasks to the ESP external edge servers from other ESPs. For internal load balancing, we propose a task balancing scheme based on the Alternating Direction Method of Multipliers (ADMM) to manage the computation tasks within the edge nodes of the ESP, such that the computation delay can be minimized. For external task offloading, we formulate a game-based pricing and task allocation scheme to derive the best game strategy, aiming at maximizing the total revenue of each ESP. Extensive simulation results demonstrate that the proposed schemes can achieve improved performance in terms of system revenue and stability, as well as computation delay.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796843"}, {"primary_key": "1711390", "vector": [], "sparse_vector": [], "title": "EdgeMatrix: A Resources Redefined Edge-Cloud System for Prioritized Services.", "authors": ["Yuanming Ren", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The edge-cloud system has the potential to com-bine the advantages of heterogeneous devices and truly realize ubiquitous computing. However, for service providers to guar-antee the Service-Level-Agreement (SLA) priorities, the complex networked environment brings inherent challenges such as multi-resource heterogeneity, resource competition, and networked sys-tem dynamics. In this paper, we design a framework for the edge-cloud system, namely EdgeMatrix, to maximize the throughput while guaranteeing various SLA priorities. First, EdgeMatrix introduces Networked Multi-agent Actor-Critic (NMAC) algorithm to redefines physical resources as logically isolated resource combinations, i.e., resource cells. Then, we use a clustering algorithm to group the cells with similar characteristics into various sets, i.e., resource channels, for different channels can offer different SLA guarantees. Besides, we design a multi-task mechanism to solve the problem of joint service orchestration and request dispatch (JSORD) among edge-cloud clusters, significantly reducing the runtime than traditional methods. To ensure stability, EdgeMatrix adopts a two-time-scale framework, i.e., coordinating resources and services at the large time scale and dispatching requests at the small time scale. The real trace-based experimental results verify that EdgeMatrix can improve system throughput in complex networked environments, reduce SLA violations, and significantly reduce the runtime than traditional methods.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796939"}, {"primary_key": "1711391", "vector": [], "sparse_vector": [], "title": "Minimal Total Deviation in TCAM Load Balancing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Traffic splitting is a required functionality in networks, for example for load balancing over multiple paths or among different servers. The capacities of the servers determine the partition by which traffic should be split. A recent approach implements traffic splitting within the ternary content addressable memory (TCAM), which is often available in switches. It is important to reduce the amount of memory allocated for this task since TCAMs are power consuming and are also required for other tasks such as classification and routing. Previous work showed how to compute the smallest prefix-matching TCAM necessary to implement a given partition exactly. In this paper we solve the more practical case, where at most n prefix-matching TCAM rules are available, restricting the ability to implement exactly the desired partition. We consider the L 1 distance between partitions, which is of interest when overloaded requests are simply dropped, and we want to minimize the total loss. We prove that the Niagara algorithm [1] can be used to find the closest partition in L 1 to the desired partition, that can be realized with n TCAM rules. Moreover, we prove it for arbitrary partitions, with (possibly) non-integer parts.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796698"}, {"primary_key": "1711392", "vector": [], "sparse_vector": [], "title": "FlowShark: Sampling for High Flow Visibility in SDNs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As the scale and speed of modern networks continue to increase, traffic sampling has become an indispensable tool in network management. While there exist a plethora of sampling solutions, they either provide limited flow visibility or have poor scalability in large networks. This paper presents the design and evaluation of FlowShark, a high-visibility per-flow sampling system for Software-Defined Networks (SDNs). The key idea in FlowShark is to separate sampling decisions on short and long flows, whereby sampling short flows is managed locally on edge switches, while a central controller optimizes sampling decisions on long flows. To this end, we formulate flow sampling as an optimization problem and design an online algorithm with a bounded competitive ratio to solve the problem efficiently. To show the feasibility of our design, we have implemented FlowShark in a small OpenFlow network using Mininet. We present experimental results of our Mininet implementation as well as performance benchmarks obtained from packet-level simulations in larger networks. Our experiments with a machine learning based Traffic Classifier application show up to 27% and 19% higher classification recall and precision, respectively, with FlowShark compared to existing sampling approaches.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796658"}, {"primary_key": "1711393", "vector": [], "sparse_vector": [], "title": "FLASH: Federated Learning for Automated Selection of High-band mmWave Sectors.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fast sector-steering in the mmWave band for vehicular mobility scenarios remains an open challenge. This is because standard-defined exhaustive search over predefined antenna sectors cannot be assuredly completed within short contact times. This paper proposes machine learning to speed up sector selection using data from multiple non-RF sensors, such as LiDAR, GPS, and camera images. The contributions in this paper are threefold: First, a multimodal deep learning architecture is proposed that fuses the inputs from these data sources and locally predicts the sectors for best alignment at a vehicle. Second, it studies the impact of missing data (e.g., missing LiDAR/images) during inference, which is possible due to unreliable control channels or hardware malfunction. Third, it describes the first-of-its-kind multimodal federated learning framework that combines model weights from multiple vehicles and then disseminates the final fusion architecture back to them, thus incorporating private sharing of information and reducing their individual training times. We validate the proposed architectures on a live dataset collected from an autonomous car equipped with multiple sensors (GPS, LiDAR, and camera) and roof-mounted Talon AD7200 60GHz mmWave radios. We observe 52.75% decrease in sector selection time than 802.11ad standard while maintaining 89.32% throughput with the globally optimal solution.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796865"}, {"primary_key": "1711396", "vector": [], "sparse_vector": [], "title": "Constrained In-network Computing with Low Congestion in Datacenter Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Distributed computing has become a common practice nowadays, where recent focus has been given to the usage of smart networking devices with in-network computing capabilities. State-of-the-art switches with near-line rate computing and aggregation capabilities enable acceleration and improved performance for various modern applications like big data analytics and large-scale distributed and federated machine learning.In this work, we formulate and study the theoretical algorithmic foundations of such approaches, and focus on how to deploy and use constrained in-network computing capabilities within the data center. We focus our attention on reducing the network congestion, i.e., the most congested link in the network, while supporting the given workload(s). We present an efficient optimal algorithm for tree-like network topologies and show that our solution provides as much as an x13 improvement over common alternative approaches. In particular, our results show that having merely a small fraction of network devices that support in-network aggregation can significantly reduce the network congestion, both for single and multiple workloads.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796980"}, {"primary_key": "1711398", "vector": [], "sparse_vector": [], "title": "Enabling QoE Support for Interactive Applications over Mobile Edge with High User Mobility.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Huang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The fast development of mobile edge computing (MEC) and service virtualization brings new opportunities to the deployment of interactive applications, e.g., VR education, stream gaming, autopilot assistance, at the network edge for better performance. Ensuring quality of experience (QoE) for such services often requires the satisfaction of multiple quality of service (QoS) factors, e.g., short delay, high throughput rate, low packet loss. Nevertheless, existing mobile edge networks often fail to meet these requirements due to the mobility of end users and the volatility of network conditions. In this paper, we propose a novel scheme that both reduces delay and adjusts data throughput rate for QoE enhancement. We design an online service placement and throughput rate adjustment (SPTA) algorithm which coordinately migrates virtual services while tuning their data throughput rates based on real-time bandwidth fluctuation. By implementing a small-scale prototype supporting stream gaming at the edge, we show the necessity and feasibility of our work. Based on data from the experiments, we conduct real-world trace driven simulations to further demonstrate the advantages of our scheme over existing baselines.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796811"}, {"primary_key": "1711399", "vector": [], "sparse_vector": [], "title": "Learning for Robust Combinatorial Optimization: Algorithm and Application.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Learning to optimize (L2O) has recently emerged as a promising approach to solving optimization problems by exploiting the strong prediction power of neural networks and offering lower runtime complexity than conventional solvers. While L2O has been applied to various problems, a crucial yet challenging class of problems — robust combinatorial optimization in the form of minimax optimization — have largely remained under-explored. In addition to the exponentially large decision space, a key challenge for robust combinatorial optimization lies in the inner optimization problem, which is typically non-convex and entangled with outer optimization. In this paper, we study robust combinatorial optimization and propose a novel learning-based optimizer, called LRCO (Learning for Robust Combinatorial Optimization), which quickly outputs a robust solution in the presence of uncertain context. LRCO leverages a pair of learning-based optimizers — one for the minimizer and the other for the maximizer — that use their respective objective functions as losses and can be trained without the need of labels for training problem instances. To evaluate the performance of LRCO, we perform simulations for the task offloading problem in vehicular edge computing. Our results highlight that LRCO can greatly reduce the worst-case cost and improve robustness, while having a very low runtime complexity.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796715"}, {"primary_key": "1711401", "vector": [], "sparse_vector": [], "title": "User Experience Oriented Task Computation for UAV-Assisted MEC System.", "authors": ["<PERSON><PERSON>"], "summary": "Unmanned aerial vehicle (UAV)-assisted computation paradigms have been treated as the new common in the integrated space-air-ground networks in B5G and 6G era. However, providing services solely from the perspective of the UAVs can not ensure a high Quality of Experience (QoE) at the user devices (UDs). Therefore, this paper presents a user experience-oriented service provision model for UAV-aided mobile edge computing (MEC) systems. First, a novel metric called shrinkage ratio is defined to reflect the experience at the UDs, and then the promotion of the QoE is formulated as a shrinkage ratio minimization problem. Second, a three-step strategy is adopted to tackle the problem which is non-convex: 1) a posterior method is adopted to eliminate the unknown upper bounds; 2) the non-convex constraints are approximately converted to convex by applying the first order Taylor expansion and are handled by SCA technique; 3) an algorithm for derive an accurate estimation in step 1) is put forward. Third, an initialization scheme for UAV trajectory is proposed by applying TSP technique. Finally, numerical results are given to validate our proposed design.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796968"}, {"primary_key": "1711404", "vector": [], "sparse_vector": [], "title": "DiFi: A Go-as-You-Pay Wi-Fi Access System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As video streaming services become more popular, users desire high perceived video quality, which has placed more stringent requirements on the quality of connection. Existing issues of cellular networks encourage users to seek alternative connections such as public Wi-Fi networks; however, expectations of both users and owners of Wi-Fi networks are not sufficiently satisfied and various concerns are yet to be addressed by a better Wi-Fi access system. Based on a go-as-you-pay scheme, we design and implement DiFi, a per-user-based system with dynamic resource allocation and pricing. DiFi offers data burst that accommodates user requirements on the burstiness of traffic, in addition to bandwidth. It better caters to the various individual requirements of users, and better utilizes the limited network resources for the owners. We leverage the blockchain-based smart contract to address realistic concerns on decentralized control, privacy and trustiness and our implementation is compatible with existing Wi-Fi infrastructures.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796832"}, {"primary_key": "1711410", "vector": [], "sparse_vector": [], "title": "Learning from Delayed Semi-Bandit Feedback under Strong Fairness Guarantees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Multi-armed bandit frameworks, including combinatorial semi-bandits and sleeping bandits, are commonly employed to model problems in communication networks and other engineering domains. In such problems, feedback to the learning agent is often delayed (e.g. communication delays in a wireless network or conversion delays in online advertising). Moreover, arms in a bandit problem often represent entities required to be treated fairly, i.e. the arms should be played at least a required fraction of the time. In contrast to the previously studied asymptotic fairness, many real-time systems require such fairness guarantees to hold even in the short-term (e.g. ensuring the credibility of information flows in an industrial Internet of Things (IoT) system). To that end, we develop the Learning with Delays under Fairness (LDF) algorithm to solve combinatorial semi-bandit problems with sleeping arms and delayed feedback, which we prove guarantees strong (short-term) fairness. While previous theoretical work on bandit problems with delayed feedback typically derive instance-dependent regret bounds, this approach proves to be challenging when simultaneously considering fairness. We instead derive a novel instance-independent regret bound in this setting which agrees with state-of-the-art bounds. We verify our theoretical results with extensive simulations using both synthetic and real-world datasets.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796683"}, {"primary_key": "1711411", "vector": [], "sparse_vector": [], "title": "A Profit-Maximizing Model Marketplace with Differentially Private Federated Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Guocheng Liao", "<PERSON><PERSON><PERSON>"], "summary": "Existing machine learning (ML) model marketplaces generally require data owners to share their raw data, leading to serious privacy concerns. Federated learning (FL) can partially alleviate this issue by enabling model training without raw data exchange. However, data owners are still susceptible to privacy leakage from gradient exposure in FL, which discourages their participation. In this work, we advocate a novel differentially private FL (DPFL)-based ML model marketplace. We focus on the broker-centric design. Specifically, the broker first incentivizes data owners to participate in model training via DPFL by offering privacy protection as per their privacy budgets and explicitly accounting for their privacy costs. Then, it conducts optimal model versioning and pricing to sell the obtained model versions to model buyers. In particular, we focus on the broker's profit maximization, which is challenging due to the significant difficulties in the revenue characterization of model trading and the cost estimation of DPFL model training. We propose a two-layer optimization framework to address it, i.e., revenue maximization and cost minimization under model quality constraints. The latter is still challenging due to its non-convexity and integer constraints. We hence propose efficient algorithms, and their performances are both theoretically guaranteed and empirically validated.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796833"}, {"primary_key": "1711412", "vector": [], "sparse_vector": [], "title": "Subset Selection for Hybrid Task Scheduling with General Cost Constraints.", "authors": ["Yu Sun", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Subset selection problem for task scheduling with general cost constraints exists widely in IoT applications. Its objective is to select several profitable tasks to execute under routing and cost constraints such that the total profit is maximized. Most prior arts only focus on either online tasks or offline tasks, which are usually inapplicable in practical applications where online tasks and offline tasks co-exist. In this paper, we study the subset selection problem for HybrId Task Scheduling with general cost constraints (HITS), in which both online and offline tasks are scheduled to maximize the overall profit. We first divide the HITS problem into online and offline subproblems and propose two algorithms to solve them with bounded approximation ratios. Furthermore, we propose an approximation algorithm for the hybrid scenario where both online and offline tasks are considered. Extensive simulations show that our proposed algorithm outperforms baseline algorithms by 21.5% averagely in profit and also performs well in pure online/offline scenarios. We further demonstrate the feasibility of our algorithm through test-bed experiments in a realistic scene.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796947"}, {"primary_key": "1711413", "vector": [], "sparse_vector": [], "title": "Optimal Oblivious Routing for Structured Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Oblivious routing distributes traffic from sources to destinations following predefined routes with rules independent of traffic demands. While finding optimal oblivious routing is intractable for general topologies, we show that it is tractable for structured topologies often used in datacenter networks. To achieve this, we apply graph automorphism and prove the existence of the optimal automorphism-invariant solution. This result reduces the search space to targeting the optimal automorphism-invariant solution. We design an iterative algorithm to obtain such a solution by alternating between two linear programs. The first program finds an automorphism-invariant solution based on representative variables and constraints, making the problem tractable. The second program generates adversarial demands to ensure the final result satisfies all possible demands. Since, the construction of the representative variables and constraints are combinatorial problems, we design polynomial-time algorithms for the construction. We evaluate proposed iterative algorithm in terms of throughput performance, scalability, and generality over three potential applications. The algorithm i) improves the throughput up to 87.5% over a heuristic algorithm for partially deployed FatTree, ii) scales for FatClique with a thousand switches, iii) is applicable to a general structured topology with non-uniform link capacity and server distribution.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796682"}, {"primary_key": "1711414", "vector": [], "sparse_vector": [], "title": "Policy-Induced Unsupervised Feature Selection: A Networking Case Study.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A promising approach for leveraging the flexibility and mitigating the complexity of future telecom systems is the use of machine learning (ML) models that can analyze the network performance, as well as taking proactive actions. A key enabler for ML models is timely access to reliable data, in terms of features, which require pervasive measurement points throughout the network. However, excessive monitoring is associated with network overhead. Considering domain knowledge may provide clues to find a balance between overhead reduction and meeting requirements on future ML use cases by monitoring just enough features. In this work, we propose a method of unsupervised feature selection that provides a structured approach in incorporation of the domain knowledge in terms of policies. Policies are provided to the method in form of must-have features defined as the features that need to be monitored at all times. We name such family of unsupervised feature selection the policy-induced unsupervised feature selection as the policies inform selection of the latent features. We evaluate the performance of the method on two rich sets of data traces collected from a data center and a 5G-mmWave testbed. Our empirical evaluations point at the effectiveness of the solution.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796928"}, {"primary_key": "1711416", "vector": [], "sparse_vector": [], "title": "Deep Learning on Mobile Devices Through Neural Processing Units and Edge Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep Neural Network (DNN) is becoming adopted for video analytics on mobile devices. To reduce the delay of running DNNs, many mobile devices are equipped with Neural Processing Units (NPU). However, due to the resource limitations of NPU, these DNNs have to be compressed to increase the processing speed at the cost of accuracy. To address the low accuracy problem, we propose a Confidence Based Offloading (CBO) framework for deep learning video analytics. The major challenge is to determine when to return the NPU classification result based on the confidence level of running the DNN, and when to offload the video frames to the server for further processing to increase the accuracy. We first identify the problem of using existing confidence scores to make offloading decisions, and propose confidence score calibration techniques to improve the performance. Then, we formulate the CBO problem where the goal is to maximize accuracy under some time constraint, and propose an adaptive solution that determines which frames to offload at what resolution based on the confidence score and the network condition. Through real implementations and extensive evaluations, we demonstrate that the proposed solution can significantly outperform other approaches.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796929"}, {"primary_key": "1711417", "vector": [], "sparse_vector": [], "title": "CoToRu: Automatic Generation of Network Intrusion Detection Rules from Code.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Programmable Logic Controllers (PLCs) are the brains of Industrial Control Systems (ICSes), and thus, are often targeted by attackers. While many intrusion detection systems (IDSes) have been adapted to monitor ICS, they cannot detect malicious network packets from a compromised PLC that con-form to the network protocol. A domain expert needs to manually construct IDS rules to model a PLC's behavior. That approach is time-consuming and error-prone. Alternatively, machine learning can infer a PLC's behavior model from network traces, but that model may be inaccurate due to a lack of high-quality training data. This paper presents CoToRu - a toolchain that takes in the PLC's code to automatically generate a comprehensive set of IDS rules. CoToRu comprises (1) an analyzer that parses PLC code to build a state transition table for modeling the PLC's behavior, and (2) a generator that instantiates IDS rules for detecting deviations in PLC behavior. The generated rules can be imported into Zeek IDS to detect various attacks. We apply CoToRu to a power grid testbed and show that our generated rules provide superior performance compared to existing IDSes, including those based on statistical analysis, invariant-checking, and machine learning. Our prototype with CoToRu's generated rules provide sub-millisecond detection latency, even for complex PLC logic.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796697"}, {"primary_key": "1711418", "vector": [], "sparse_vector": [], "title": "Multi-Agent Distributed Reinforcement Learning for Making Decentralized Offloading Decisions.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We formulate computation offloading as a decentralized decision-making problem with autonomous agents. We design an interaction mechanism that incentivizes agents to align private and system goals by balancing between competition and cooperation. The mechanism provably has Nash equilibria with optimal resource allocation in the static case. For a dynamic environment, we propose a novel multi-agent online learning algorithm that learns with partial, delayed and noisy state information, and a reward signal that reduces information need to a great extent. Empirical results confirm that through learning, agents significantly improve both system and individual performance, e.g., 40% offloading failure rate reduction, 32% communication overhead reduction, up to 38% computation resource savings in low contention, 18% utilization increase with reduced load variation in high contention, and improvement in fairness. Results also confirm the algorithm's good convergence and generalization property in significantly different environments.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796717"}, {"primary_key": "1711419", "vector": [], "sparse_vector": [], "title": "Sending Timely Status Updates through Channel with Random Delay via Online Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jingzhou Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, we study a status update system with a source node sending timely information to the destination through a channel with random delay. We measure the timeliness of the information stored at the receiver via the Age of Information (AoI), the time elapsed since the freshest sample stored at the receiver is generated. The goal is to design a sampling strategy that minimizes the total cost of the expected time average AoI and sampling cost in the absence of transmission delay statistics. We reformulate the total cost minimization problem as the optimization of a renewal-reward process, and propose an online sampling strategy based on the Robbins-Monro algorithm. Denote K to be the number of samples we have taken. We show that, when the transmission delay is bounded, the expected time average total cost obtained by the proposed online algorithm converges to the minimum cost when K goes to infinity, and the optimality gap decays with rate ${\\mathcal{O}}$(ln K/K). Simulation results validate the performance of our proposed algorithm.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796970"}, {"primary_key": "1711421", "vector": [], "sparse_vector": [], "title": "Connectivity Maintenance in Uncertain Networks under Adversarial Attack.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies the problem of connectivity maintenance in adversarial uncertain networks, where a defender prevents the largest connected component from being decomposed by an attacker. In contrast with its deterministic counterpart, connectivity maintenance in an uncertain network involves additional testing on edges to determine their existence. To this end, by modeling a general uncertain network as a random graph with each edge associated with an existence probability and a testing cost, our goal is to design a general adaptive defensive strategy to maximize the expected size of the largest remaining connected component with minimum expected testing cost and, moreover, the strategy should be independent of the attacking patterns. The computational complexity of the connectivity maintenance problem is unraveled by proving its NP-hardness. To accurately tackle the problem, based on dynamic programming we first propose an optimal defensive strategy for a specific class of uncertain networks with uniform testing costs. Thereafter multi-objective optimization is adopted to generalize the optimal strategy for general uncertain networks through weighted sum of normalized size and cost. Due to the prohibitive price of an optimal strategy, two approximate defensive strategies are further designed to pursue decent performance with quasilinear complexity. We first derive a heuristic approach by quantifying the edge vulnerability through an analogy from the degree centrality in deterministic networks to the probability degree and connectivity weight in uncertain networks. For performance guarantee, we then devise an adaptive greedy policy incorporating the minimax rule from game theory, which minimizes the possible loss suffered by the defender in a worst-case scenario caused by the attacker and has an approximation ratio of (1 − 1/e). Extensive experiments on both synthetic and real-world network datasets under diverse attacking patterns demonstrate the superiority of the proposed strategies over baselines.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796966"}, {"primary_key": "1711422", "vector": [], "sparse_vector": [], "title": "ABS: Adaptive Buffer Sizing via Augmented Programmability with Machine Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Programmable switches have been proposed in today's network to enable flexible reconfiguration of devices and reduce time-to-deployment. Buffer sizing, an important factor for network performance, however, has not received enough attention in programmable network. The state-of-the-art buffer sizing solutions usually employ either fixed buffer size or adjust the buffer size heuristically. Without programmability, they suffer from either massive packet drops or large queueing delay in dynamic environment. In this paper, we propose Adaptive Buffer Sizing (ABS), a low-cost and deploy-friendly framework compatible with programmable network. By decoupling the data plane and control plane, ABS-capable switches only need to react to the actions from controller, optimizing network performance in run-time under dynamic traffic. Meanwhile, actions can be programmed by particular Machine Learning (ML) models in the controller to meet different network requirements. In this paper, we address two specific ML models for different scenarios, a reinforcement learning model for relatively stable network with user specific quality requirements, and a supervised learning model for highly dynamic network condition. We implement the ABS framework by integrating the prevalent network simulator NS-2 with ML module. The experiment shows that ABS outperforms state-of-the-art buffer sizing solutions by up to 38.23x under various network environments.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796967"}, {"primary_key": "1711423", "vector": [], "sparse_vector": [], "title": "Optimal Shielding to Guarantee Region-Based Connectivity under Geographical Failures.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As networks and their inter-connectivity grow and become complex, failures in the networks impact society and industries more than ever. In these networks the notion of connectedness is the key to understanding and reasoning about these failures. Traditional studies in improving edge/node connectivity assume that failures occur at random. However, in many scenarios (such as earthquakes, hurricanes, and human-designed attacks on networks) failures are not random, and most traditional methods do not always work. To address this limitation, we consider region-based connectivity to capture the local nature of failures under the geographical failure model, where failures may happen only on edges in a sub-network (region) and we want to shield some edges in regions to protect the connectivity. There may be several regions and in different regions the failures occur independently. Firstly, we establish the NP-hardness of the problem for regions, answering a question proposed in previous papers. Secondly, we propose a polynomial-time algorithm for the special case of two regions based on the matroid techniques. Furthermore, we design an ILP-based algorithm to solve the problem for regions. Experimental results on random and real networks show that our algorithms are much faster than previously known algorithms.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796753"}, {"primary_key": "1711424", "vector": [], "sparse_vector": [], "title": "Caching-based Multicast Message Authentication in Time-critical Industrial Control Systems.", "authors": ["Utku <PERSON>k", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Attacks against industrial control systems (ICSs) often exploit the insufficiency of authentication mechanisms. Verifying whether the received messages are intact and issued by legitimate sources can prevent malicious data/command injection by illegitimate or compromised devices. However, the key challenge is to introduce message authentication for various ICS communication models, including multicast or broadcast, with a messaging rate that can be as high as thousands of messages per second, within very stringent latency constraints. For example, certain commands for protection in smart grids must be delivered within 2 milliseconds, ruling out public-key cryptography. This paper proposes two lightweight message authentication schemes, named CMA and its multicast variant CMMA, that perform precomputation and caching to authenticate future messages. With minimal precomputation and communication overhead, C(M)MA eliminates all cryptographic operations for the source after the message is given, and all expensive cryptographic operations for the destinations after the message is received. C(M)MA considers the urgency profile (or likelihood) of a set of future messages for even faster verification of the most time-critical (or likely) messages. We demonstrate the feasibility of C(M)MA in an ICS setting based on a substation automation system in smart grids.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796767"}, {"primary_key": "1711426", "vector": [], "sparse_vector": [], "title": "PreGAN: Preemptive Migration Prediction Network for Proactive Fault-Tolerant Edge Computing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Building a fault-tolerant edge system that can quickly react to node overloads or failures is challenging due to the unreliability of edge devices and the strict service deadlines of modern applications. Moreover, unnecessary task migrations can stress the system network, giving rise to the need for a smart and parsimonious failure recovery scheme. Prior approaches often fail to adapt to highly volatile workloads or accurately detect and diagnose faults for optimal remediation. There is thus a need for a robust and proactive fault-tolerance mechanism to meet service level objectives. In this work, we propose PreGAN, a composite AI model using a Generative Adversarial Network (GAN) to predict preemptive migration decisions for proactive fault-tolerance in containerized edge deployments. PreGAN uses co-simulations in tandem with a GAN to learn a few-shot anomaly classifier and proactively predict migration decisions for reliable computing. Extensive experiments on a Raspberry-Pi based edge environment show that PreGAN can outperform state-of-the-art baseline methods in fault-detection, diagnosis and classification, thus achieving high quality of service. PreGAN accomplishes this by 5.1% more accurate fault detection, higher diagnosis scores and 23.8% lower overheads compared to the best method among the considered baselines.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796778"}, {"primary_key": "1711427", "vector": [], "sparse_vector": [], "title": "Vehicle-to-Nothing? Securing C-V2X Against Protocol-Aware DoS Attacks.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Vehicle-to-vehicle (V2V) communication allows vehicles to directly exchange messages, increasing their situational awareness and offering the potential to prevent hundreds of thousands vehicular crashes annually. Cellular Vehicle-to-Everything (C-V2X), with its LTE-V2X and New Radio (NR)-V2X variants in 4G/LTE- and 5G-based C-V2X, is emerging as the main V2V technology. However, despite security protocols and standards for C-V2X, we expose in this paper that its physical (PHY) and MAC layers are not resilient against intelligent, protocol-aware attacks due to the very predictable PHY-layer structure and vulnerable scheduling algorithm used in both LTE-V2X and NR-V2X. We devise two stealthy denial-of-service (DoS) exploits that dramatically degrade C-V2X availability, thereby increasing the chances of fatal vehicle collisions. We experimentally evaluate our attacks on an integrated, hybrid testbed with USRPs and state-of-the-art LTE-V2X devices as well as through extensive simulations, showing that within seconds, our attacks can reduce a target's packet delivery ratio by 90% or degrade C-V2X channel throughput by 50%. We propose, analyze, and evaluate detection approaches as well as mitigation techniques to address the vulnerabilities we expose in the C-V2X PHY/MAC layers, providing direction towards better-secured, resilient 5G C-V2X.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796667"}, {"primary_key": "1711429", "vector": [], "sparse_vector": [], "title": "Learning Optimal Antenna Tilt Control Policies: A Contextual Linear Bandit Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Controlling antenna tilts in cellular networks is imperative to reach an efficient trade-off between network coverage and capacity. In this paper, we devise algorithms learning optimal tilt control policies from existing data (in the so-called passive learning setting) or from data actively generated by the algorithms (the active learning setting). We formalize the design of such algorithms as a Best Policy Identification (BPI) problem in Contextual Linear Multi-Arm Bandits (CL-MAB). An arm represents an antenna tilt update; the context captures current network conditions; the reward corresponds to an improvement of performance, mixing coverage and capacity; and the objective is to identify, with a given level of confidence, an approximately optimal policy (a function mapping the context to an arm with maximal reward). For CL-MAB in both active and passive learning settings, we derive information-theoretical lower bounds on the number of samples required by any algorithm returning an approximately optimal policy with a given level of certainty, and devise algorithms achieving these fundamental limits. We apply our algorithms to the Remote Electrical Tilt (RET) optimization problem in cellular networks, and show that they can produce optimal tilt update policy using much fewer data samples than naive or existing rule-based learning algorithms.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796783"}, {"primary_key": "1711430", "vector": [], "sparse_vector": [], "title": "Polynomial-Time Algorithm for the Regional SRLG-disjoint Paths Problem.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zsombor L. <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The current best practice in survivable routing is to compute link or node disjoint paths in the network topology graph. It can protect single-point failures; however, several failure events may cause the interruption of multiple network elements. The set of network elements subject to potential failure events is called Shared Risk Link Group (SRLG), identified during network planning. Unfortunately, for any given list of SRLGs, finding two paths that can survive a single SRLG failure is NP-Complete. In this paper, we provide a polynomial-time SRLG-disjoint routing algorithm for planar network topologies and a large set of SRLGs. Namely, we focus on regional failures, where the failed network elements must not be far from each other. We use a flexible definition of regional failure, where the only restriction is that the topology is a planar graph, and the SRLGs form a set of connected edges in the dual of the planar graph. The proposed algorithm is based on a max-min theorem. Through extensive simulations, we show that the algorithm scales well with the network size, and one of the paths returned by the algorithm is only 4% longer than the shortest path on average.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796870"}, {"primary_key": "1711433", "vector": [], "sparse_vector": [], "title": "Performance and Scaling of Parallel Systems with Blocking Start and/or Departure Barriers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Parallel systems divide jobs into smaller tasks that can be serviced by many workers at the same time. Some parallel systems have blocking barriers that require all of their tasks to start and/or depart in unison. This is true of many parallelized machine learning workloads, and the popular Apache Spark processing engine has recently added support for Barrier Execution Mode, which allows users to add such barriers to their jobs. The drawback of these barriers is reduced performance and stability compared to equivalent non-blocking systems.We derive analytical expressions for the stability regions for parallel systems with blocking start and/or departure barriers. We extend results from queueing theory to derive waiting and sojourn time bounds for systems with blocking start barriers. Our results show that for a given system utilization and number of servers, there is an optimal degree of parallelism that balances waiting time and job execution time. This observation leads us to propose and implement a class of self-adaptive schedulers, we call \"Take-Half\", that modulate the allowed degree of parallelism based on the instantaneous system load, improving mean performance and eliminating stability issues.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796754"}, {"primary_key": "1711434", "vector": [], "sparse_vector": [], "title": "IoTMosaic: Inferring User Activities from IoT Network Traffic in Smart Homes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent advances in cyber-physical systems, artificial intelligence, and cloud computing have driven the wide deployment of Internet-of-things (IoT) in smart homes. As IoT devices often directly interact with the users and environments, this paper studies if and how we could explore the collective insights from multiple heterogeneous IoT devices to infer user activities for home safety monitoring and assisted living. Specifically, we develop a new system, namely IoTMosaic, to first profile diverse user activities with distinct IoT device event sequences, which are extracted from smart home network traffic based on their TCP/IP data packet signatures. Given the challenges of missing and out-of-order IoT device events due to device malfunctions or varying network and system latencies, IoTMosaic further develops simple yet effective approximate matching algorithms to identify user activities from real-world IoT network traffic. Our experimental results on thousands of user activities in the smart home environment over two months show that our proposed algorithms can infer different user activities from IoT network traffic in smart homes with the overall accuracy, precision, and recall of 0.99, 0.99, and 1.00, respectively.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796908"}, {"primary_key": "1711436", "vector": [], "sparse_vector": [], "title": "FeCo: Boosting Intrusion Detection Capability in IoT Networks via Contrastive Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Over the last decade, Internet of Things (IoT) has permeated our daily life with a broad range of applications. However, a lack of sufficient security features in IoT devices renders IoT ecosystems vulnerable to various network intrusion attacks, potentially causing severe damage. Previous works have explored using machine learning to build anomaly detection models for defending against such attacks. In this paper, we propose FeCo, a federated-contrastive-learning framework that coordinates in-network IoT devices to jointly learn intrusion detection models. FeCo utilizes federated learning to alleviate users' privacy concerns as participating devices only submit their model parameters rather than local data. Compared to previous works, we develop a novel representation learning method based on contrastive learning that is able to learn a more accurate model for the benign class. FeCo significantly improves the intrusion detection accuracy compared to previous works. Besides, we implement a two-step feature selection scheme to avoid overfitting and reduce computation time. Through extensive experiments on the NSL-KDD dataset, we demonstrate that FeCo achieves as high as 8% accuracy improvement compared to the state-of-the-art and is robust to non-IID data. Evaluations on convergence, computation overhead, and scalability further confirm the suitability of FeCo for IoT intrusion detection.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796926"}, {"primary_key": "1711437", "vector": [], "sparse_vector": [], "title": "Online Model Updating with Analog Aggregation in Wireless Edge Learning.", "authors": ["Jun<PERSON> Wang", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider federated learning in a wireless edge network, where multiple power-limited mobile devices collaboratively train a global model, using their local data with the assistance of an edge server. Exploiting over-the-air computation, the edge server updates the global model via analog aggregation of the local models over noisy wireless fading channels. Unlike existing works that separately optimize computation and communication at each step of the learning algorithm, in this work, we jointly optimize the training of the global model and the analog aggregation of local models over time. Our objective is to minimize the accumulated training loss at the edge server, subject to individual long-term transmit power constraints at the mobile devices. We propose an efficient algorithm, termed Online Model Updating with Analog Aggregation (OMUAA), to adaptively update the local and global models based on the time-varying communication environment. The trained model of OMUAA is channel- and power-aware, and it is in closed form with low computational complexity. We study the mutual impact between model training and analog aggregation over time, to derive performance bounds on the computation and communication performance metrics. Simulation results based on real-world image classification datasets and typical Long-Term Evolution network settings demonstrate substantial performance gain of OMUAA over the known best alternatives.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796860"}, {"primary_key": "1711440", "vector": [], "sparse_vector": [], "title": "Socially-Optimal Mechanism Design for Incentivized Online Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multi-arm bandit (MAB) is a classic online learning framework that studies the sequential decision-making in an uncertain environment. The MAB framework, however, overlooks the scenario where the decision-maker cannot take actions (e.g., pulling arms) directly. It is a practically important scenario in many applications such as spectrum sharing, crowdsensing, and edge computing. In these applications, the decision-maker would incentivize other selfish agents to carry out desired actions (i.e., pulling arms on the decision-maker's behalf). This paper establishes the incentivized online learning (IOL) framework for this scenario. The key challenge to design the IOL framework lies in the tight coupling of the unknown environment learning and asymmetric information revelation. To address this, we construct a special La<PERSON>ngian function based on which we propose a socially-optimal mechanism for the IOL framework. Our mechanism satisfies various desirable properties such as agent fairness, incentive compatibility, and voluntary participation. It achieves the same asymptotic performance as the state-of-art benchmark that requires extra information. Our analysis also unveils the power of crowd in the IOL framework: a larger agent crowd enables our mechanism to approach more closely the theoretical upper bound of social performance. Numerical results demonstrate the advantages of our mechanism in large-scale edge computing.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796676"}, {"primary_key": "1711441", "vector": [], "sparse_vector": [], "title": "Protect Privacy from Gradient Leakage Attack in Federated Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Federated Learning (FL) is susceptible to gradient leakage attacks, as recent studies show the feasibility of obtaining private training data on clients from publicly shared gradients. Existing work solves this problem by incorporating a series of privacy protection mechanisms, such as homomorphic encryption and local differential privacy to prevent data leakage. However, these solutions either incur significant communication and computation costs, or significant training accuracy loss. In this paper, we show that the sensitivity of gradient changes w.r.t. training data is an essential measure of information leakage risk. Based on this observation, we present a novel defense, whose intuition is perturbing gradients to match information leakage risk such that the defense overhead is lightweight while privacy protection is adequate. Our another key observation is that global correlations of gradients could compensate for this perturbation. Based on such compensation, training can achieve guaranteed accuracy. We conduct experiments on MNIST, Fashion-MNIST and CIFAR-10 for defending against two gradient leakage attacks. Without sacrificing accuracy, the results demonstrate that our lightweight defense can decrease the PSNR and SSIM between the reconstructed images and raw images by up to more than 60% for both two attacks, compared with baseline defensive methods.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796841"}, {"primary_key": "1711442", "vector": [], "sparse_vector": [], "title": "xNet: Improving Expressiveness and Granularity for Network Modeling with Graph Neural Networks.", "authors": ["<PERSON><PERSON>", "Linbo Hui", "<PERSON>", "<PERSON><PERSON>", "Zhenhua Li"], "summary": "Today's network is notorious for its complexity and uncertainty. Network operators often rely on network models to achieve efficient network planning, operation, and optimization. The network model is responsible for understanding the complex relationships between the network performance metrics (e.g., latency) and the network characteristics (e.g., traffic). However, we still lack a systematic approach to developing accurate and lightweight network models that are aware of the impact of network configurations (i.e., expressiveness) and provide fine-grained flow-level temporal predictions (i.e., granularity).In this paper, we propose xNet, a data-driven network modeling framework based on graph neural networks (GNN). Unlike the previous proposals, xNet is not a dedicated network model designed for specific network scenarios with constraint considerations. On the contrary, xNet provides a general approach to modeling the network characteristics of concern with relation graph representations and configurable GNN blocks. xNet learns the state transition function between time steps and rolls it out to obtain the full fine-grained prediction trajectory. We implement and instantiate xNet with three use cases. The experiment results show that xNet can accurately predict different performance metrics while achieving over two orders of magnitude of speedup compared with the conventional packet-level simulator.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796726"}, {"primary_key": "1711443", "vector": [], "sparse_vector": [], "title": "Learning Buffer Management Policies for Shared Memory Switches.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Huang", "<PERSON>", "<PERSON><PERSON>", "Zhenhua Li"], "summary": "Today's network switches often use on-chip shared memory to improve buffer efficiency and absorb bursty traffic. Current buffer management practices usually rely on simple heuristics and have unrealistic assumptions about the traffic pattern, since developing a buffer management policy suited for every scenario is infeasible. We show that modern machine learning techniques can be of essential help to learn efficient policies automatically.In this paper, we propose Neural Dynamic Threshold (NDT) that uses deep reinforcement learning (RL) to learn buffer management policies without human instructions except for a high-level objective. To tackle the high complexity and scale of the buffer management problem, we develop two domain-specific techniques upon off-the-shelf deep RL solutions. First, we design a scalable RL model by leveraging the permutation symmetry of the switch ports. Second, we use a two-level control mechanism to achieve efficient training and decision-making. The buffer allocation is directly controlled by a low-level heuristic during the decision interval, while the RL agent only decides the high-level control factor according to the traffic density. Testbed and simulation experiments demonstrate that NDT generalizes well and outperforms hand-tuned heuristic policies even on workloads for which it was not explicitly trained.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796784"}, {"primary_key": "1711444", "vector": [], "sparse_vector": [], "title": "Amaging: Acoustic Hand Imaging for Self-adaptive Gesture Recognition.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A practical challenge common to state-of-the-art acoustic gesture recognition techniques is to adaptively respond to intended gestures rather than unintended motions during the real-time tracking on human motion flow. Besides, other disadvantages of under-expanded sensing space and vulnerability against mobile interference jointly impair the pervasiveness of acoustic sensing. Instead of struggling along the bottlenecked routine, we innovatively open up an independent sensing dimension of acoustic 2-D hand-shape imaging. We first deductively demonstrate the feasibility of acoustic imaging through multiple viewpoints dynamically generated by hand movement. Amaging, hand-shape imaging triggered gesture recognition, is then proposed to offer adaptive gesture responses. Digital Dechirp is novelly performed to largely reduce computational cost in demodulation and pulse compression. Mobile interference is filtered by Moving Target Indication. Multi-frame macro-scale imaging with Joint Time-Frequency Analysis is performed to eliminate image blur while maintaining adequate resolution. Amaging features revolutionary multiplicative expansion on sensing capability and dual dimensional parallelism for both hand-shape and gesture-trajectory recognition. Extensive experiments and simulations demonstrate Amaging's distinguishing hand-shape imaging performance, independent from diverse hand movement and immune against mobile interference. 96% hand-shape recognition rate is achieved with ResNet18 and 60× augmentation rate.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796906"}, {"primary_key": "1711446", "vector": [], "sparse_vector": [], "title": "Semi-Online Precoding with Information Parsing for Cooperative MIMO Wireless Networks.", "authors": ["Jun<PERSON> Wang", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider cooperative multiple-input multiple-output (MIMO) precoding design with multiple access points (APs) assisted by a central controller (CC) in a fading environment. Even though each AP may have its own local channel state information (CSI), due to the communication delay in the backhaul, neither the APs nor the CC has timely global CSI. Under this semi-online setting, our goal is to minimize the accumulated precoding deviation between the actual local precoders executed by the APs and an ideal cooperative precoder based on the global CSI, subject to per-AP transmit power limits. We propose an efficient algorithm, termed Semi-Online Precoding with Information Parsing (SOPIP), which accounts for the network heterogeneity in information timeliness and computational capacity. SOPIP does not require the CC to send the full global CSI to each AP. Instead, it takes advantage of the precoder structure to substantially lower the communication overhead, while allowing each AP to effectively combine its own timely local CSI with the delayed global CSI to enable adaptive precoder updates. We analyze the performance of SOPIP in the presence of both multi-slot communication delay and gradient estimation error, showing that it has a bounded performance gap from an offline optimal solution. Simulation results under typical Long-Term Evolution network settings further demonstrate the substantial performance gain of SOPIP over other centralized and distributed schemes.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796813"}, {"primary_key": "1711447", "vector": [], "sparse_vector": [], "title": "mmPhone: Acoustic Eavesdropping on Loudspeakers via mmWave-characterized Piezoelectric Effect.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhongjie Ba", "Li Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "More and more people turn to online voice communication with loudspeaker-equipped devices due to its convenience. To prevent speech leakage, soundproof rooms are often adopted. This paper presents mmPhone, a novel acoustic eavesdropping system that recovers loudspeaker speech protected by soundproof environments. The key idea is that properties of piezoelectric films in mmWave band can change with sound pressure due to the piezoelectric effect. If the property changes are acquired by an adversary (i.e., characterizing the piezoelectric effect with mmWaves), speech leakage can happen. More importantly, the piezoelectric film can work without a power supply. Base on this, we proposed a methodology using mmWaves to sense the film and decoding the speech from mmWaves, which turns the film into a passive \"microphone\". To recover intelligible speech, we further develop an enhancement scheme based on a denoising neural network, multi-channel augmentation, and speech synthesis, to compensate for the propagation and penetration loss of mmWaves. We perform extensive experiments to evaluate mmPhone and conduct digit recognition with over 93% accuracy. The results indicate mmPhone can recover high-quality and intelligible speech from a distance over 5m and is resilient to incident angles of sound waves (within 55 degrees) and different types of loudspeakers.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796806"}, {"primary_key": "1711448", "vector": [], "sparse_vector": [], "title": "Large-scale Evaluation of Malicious Tor Hidden Service Directory Discovery.", "authors": ["Chun<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Tor is the largest anonymous communication system, providing anonymous communication services to approximately 2.8 million users and 170,000 hidden services per day. The Tor hidden service mechanism can protect a server from exposing its real identity during the communication. However, due to a design flaw of the Tor hidden service mechanism, adversaries can deploy malicious Tor hidden service directories (HSDirs) to covertly collect all onion addresses of hidden services and further probe the hidden services. To mitigate this issue, we design customized honeypot hidden services based on one-to-one and many-to-one HSDir monitoring approaches to luring and identifying the malicious HSDirs conducting the rapid and delayed probing attacks, respectively. By analyzing the probing behaviors and payloads, we investigate a novel semantic-based probing pattern clustering approach to classify the adversaries so as to shed light on the purposes of the malicious HSDirs. Moreover, we perform theoretical analysis of the capability and accuracy of our approaches. Large-scale experiments are conducted in the real-world Tor network by deploying hundreds of thousands of honeypots during a monitoring period of more than three months. Finally, we identify 8 groups of 32 malicious HSDirs, discover 25 probing pattern clusters and reveal 3 major probing purposes.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796703"}, {"primary_key": "1711452", "vector": [], "sparse_vector": [], "title": "Privacy-Preserving Online Task Assignment in Spatial Crowdsourcing: A Graph-based Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, the growing popularity of Spatial Crowd-sourcing (SC), allowing untrusted platforms to obtain a great quantity of information about workers and tasks' locations, has raised numerous privacy concerns. In this paper, we investigate the privacy-preserving task assignment in the online scenario, where workers and tasks arrive at the platform in real time and tasks should be assigned to workers immediately. Traditional online task assignments usually make a benchmark to decide the following task assignment. However, when location privacy is considered, the benchmark does not work anymore. Hence, how to assign tasks in real time based on workers and tasks' obfuscated locations is a challenging problem. Especially when many tasks could be assigned to one worker, path planning should be considered, making the assignment more challenging. To this end, we propose a Planar Laplace distribution based Privacy mechanism (PLP) to obfuscate real locations of workers and tasks, where the obfuscation does not change the ranking of these locations' relative distances. Furthermore, we design a Threshold-based Online task Assignment mechanism (TOA), which could deal with the one-worker-many-tasks assignment and achieve a satisfactory competitive ratio. Simulations based on two real-world datasets show that the proposed algorithm consistently outperforms the state-of-the-art approach.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796827"}, {"primary_key": "1711453", "vector": [], "sparse_vector": [], "title": "Decentralized Task Offloading in Edge Computing: A Multi-User Multi-Armed Bandit Approach.", "authors": ["<PERSON><PERSON>", "Jiancheng Ye", "<PERSON>"], "summary": "Mobile edge computing facilitates users to offload computation tasks to edge servers for meeting their stringent delay requirements. Previous works mainly explore task offloading when system-side information is given (e.g., server processing speed, cellular data rate), or centralized offloading under system uncertainty. But both generally fall short of handling task placement involving many coexisting users in a dynamic and uncertain environment. In this paper, we develop a multi-user offloading framework considering unknown yet stochastic system-side information to enable a decentralized user-initiated service placement. Specifically, we formulate the dynamic task placement as an online multi-user multi-armed bandit process, and propose a decentralized epoch based offloading (DEBO) to optimize user rewards which are subject to the network delay. We show that DEBO can deduce the optimal user-server assignment, thereby achieving a close-to-optimal service performance and tight O(log T ) offloading regret. Moreover, we generalize DEBO to various common scenarios such as unknown reward gap, dynamic entering or leaving of clients, and fair reward distribution, while further exploring when users' offloaded tasks require heterogeneous computing resources. Particularly, we accomplish a sub-linear regret for each of these instances. Real measurements based evaluations corroborate the superiority of our offloading schemes over state-of-the-art approaches in optimizing delay-sensitive rewards.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796961"}, {"primary_key": "1711454", "vector": [], "sparse_vector": [], "title": "NeuroMessenger: Towards Error Tolerant Distributed Machine Learning Over Edge Networks.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite the evolution of distributed machine learning (ML) systems in recent years, the communication overhead induced by their data transfers remains a major issue that hampers the efficiency of such systems, especially in edge networks with poor wireless link conditions. In this paper, we propose to explore a new paradigm of error-tolerant distribute ML to mitigate the communication overhead. Unlike generic network traffic, ML data exhibits an intrinsic error-tolerant capability which helps the model yield fair performance even with errors in the data transfers. We first characterize the error tolerance capability of state-of-art distributed ML frameworks. Based on the observations, we propose NeuroMessenger, a lightweight mechanism that can be built into the cellular network stack, which can enhance and utilize the error tolerance in ML data to reduce communication overhead. NeuroMessenger does not require per-model profiling and is transparent to application layer, which simplifies the development and deployment. Our experiments on a 5G simulation framework demonstrate that NeuroMessenger reduces the end-to-end latency by up to 99% while maintaining low accuracy loss under various link conditions.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796695"}, {"primary_key": "1711456", "vector": [], "sparse_vector": [], "title": "FedFPM: A Unified Federated Analytics Framework for Collaborative Frequent Pattern Mining.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Frequent pattern mining is an important class of knowledge discovery problems. It aims at finding out high-frequency items or structures (e.g., itemset, sequence) in a database, and plays an essential role in deriving other interesting patterns, like association rules. The traditional approach of gathering data to a central server and analyze is no longer viable due to the increasing awareness of user privacy and newly established laws on data protection. Previous privacy-preserving frequent pattern mining approaches only target a particular problem with great utility loss when handling complex structures. In this paper, we take the first initiative to propose a unified federated analytics framework (FedFPM) for a variety of frequent pattern mining problems, including item, itemset, and sequence mining. FedFPM achieves high data utility and guarantees local differential privacy without uploading raw data. Specifically, FedFPM adopts an interactive query-response approach between clients and a server. The server meticulously employs the Apriori property and the Hoeffding's inequality to generates informed queries. The clients randomize their responses in the reduced space to realize local differential privacy. Experiments on three different frequent pattern mining tasks demonstrate that FedFPM achieves better performances than the state-of-the-art specialized benchmarks, with a much smaller computation overhead.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796719"}, {"primary_key": "1711457", "vector": [], "sparse_vector": [], "title": "Spatiotemporal Fracture Data Inference in Sparse Urban CrowdSensing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While Mobile CrowdSensing (MCS) has become a popular paradigm that recruits mobile users to carry out various sensing tasks collaboratively, the performance of MCS is frequently degraded due to the limited spatiotemporal coverage in data collection. A possible way here is to incorporate sparse MCS with data inference, where unsensed data could be completed through prediction. However, the spatiotemporal data inference is usually \"fractured\" with poor performance, because of following challenges: 1) the sparsity of the sensed data, 2) the unpredictability of a spatiotemporal fracture and 3) the complex spatiotemporal relations. To resolve such fracture data issues, we elaborate a data generative model for achieving spatiotemporal fracture data inference in sparse MCS. Specifically, an algorithm named Generative High-Fidelity Matrix Completion (GHFMC) is proposed through combining traditional Deep Matrix Factorization (DMF) and Generative Adversarial Networks (GAN) for generating spatiotemporal fracture data. Along this line, GHFMC learns to extract the features of spatiotemporal data and further efficiently complete and predict the unsensed data by using Binary Cross Entropy (BCE) loss. Finally, we conduct experiments on three popular datasets. The experimental results show that our approach performs higher than the state-of-the-art (SOTA) baselines in both data inference accuracy and fidelity.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796868"}, {"primary_key": "1711458", "vector": [], "sparse_vector": [], "title": "TRUST: Real-Time Request Updating with Elastic Resource Provisioning in Clouds.", "authors": ["Jingzhou Wang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In a commercial cloud, service providers (e.g., video streaming service provider) rent resources from cloud vendors (e.g., Google Cloud Platform) and provide services to cloud users, making a profit from the price gap. Cloud users acquire services by forwarding their requests to corresponding servers. In practice, as a common scenario, traffic dynamics will cause server overload or load-unbalancing. Existing works mainly deal with the problem by two methods: elastic resource provisioning and request updating. Elastic resource provisioning is a fast and agile solution but may cost too much since service providers need to buy extra resources from cloud vendors. Though request updating is a free solution, it will cause a significant delay, resulting in a bad users' QoS. In this paper, we present a new scheme, called real-time request updating with elastic resource provisioning (TRUST), to help service providers pay less cost with users' QoS guarantee in clouds. In addition, we propose an efficient algorithm for TRUST with a bounded approximation factor based on randomized rounding. Both small-scale experiment results and large-scale simulation results show the superior performance of our proposed algorithm compared with state-of-the-art benchmarks.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796788"}, {"primary_key": "1711459", "vector": [], "sparse_vector": [], "title": "Addressing Network Bottlenecks with Divide-and-Shuffle Synchronization for Distributed DNN Training.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Bulk synchronous parallel (BSP) is the de-facto paradigm for distributed DNN training in today's production clusters. However, due to the global synchronization nature, its performance can be significantly influenced by network bottlenecks caused by either static topology heterogeneity or dynamic bandwidth contentions. Existing solutions, either system-level optimizations strengthening BSP (e.g., Ring or Hierarchical All-reduce) or algorithmic optimizations replacing BSP (e.g., ASP or SSP, which relax the global barriers), do not completely solve the problem, as they may still suffer from communication inefficiency or risk convergence inaccuracy.In this paper, we present a novel divide-and-shuffle synchronization (DS-Sync) to realize communication efficiency without sacrificing convergence accuracy for distributed DNN training. At its heart, by taking into account the network bottlenecks, DS-Sync improves communication efficiency by dividing workers into non-overlap groups to synchronize independently in a bottleneck-free manner. Meanwhile, it maintains convergence accuracy by iteratively shuffling workers among different groups to ensure a global consensus. We theoretically prove that DS-Sync converges properly in non-convex and smooth conditions like DNN. We further implement DS-Sync and integrate it with PyTorch, and our testbed experiments show that DS-Sync can achieve up to 94% improvements on the end-to-end training time with existing solutions while maintaining the same accuracy.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796688"}, {"primary_key": "1711462", "vector": [], "sparse_vector": [], "title": "StepConf: SLO-Aware Dynamic Resource Configuration for Serverless Function Workflows.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Liu"], "summary": "Function-as-a-Service (FaaS) offers a fine-grained resource provision model, enabling developers to build highly elastic cloud applications. User requests are handled by a series of serverless functions step by step, which forms a function-based workflow. The developers are required to set proper resource configuration for functions, so as to meet service level objectives (SLOs) and save cost. However, developing the resource configuration strategy is challenging. It is mainly because execution of cloud functions often suffers from cold start and performance fluctuation, which requires a dynamic configuration strategy to guarantee the SLOs. In this paper, we present StepConf, a framework that automates the resource configuration for functions as the workflow runs. StepConf optimizes memory size for each function step in the workflow and takes inter and intra-function parallelism into consideration. We evaluate StepConf on AWS Lambda. Compared with baselines, the experimental results show that StepConf can save cost up to 40.9% while ensuring the SLOs.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796962"}, {"primary_key": "1711464", "vector": [], "sparse_vector": [], "title": "OnionCode: Enabling Multi-priority Coding in LED-based Optical Camera Communications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Optical camera communication (OCC) has attracted increasing attention recently thanks to the wide usage of LED and high-resolution cameras. The lens-image sensor structure enables the camera distinguish light from various source, which is ideal for spatial MIMO. Hence, OCC can be applied to several emerging application scenarios, such as vehicle and drone communications. However, distance is a major bottleneck for OCC system, because the increase in distance makes it difficult for the camera to distinguish adjacent LEDs, which we call LED spatial mixing.In this paper, we propose a novel hierarchical coding scheme named as OnionCode to support dynamic range of channel capacity in one-to-many OCC scenario. OnionCode adopts a multi-priority receiving scheme, i.e., the receivers can dynamically discard the low-priority bit streams according to the measured channel capacity. OnionCode achieves this based on a key insight that, the luminance level of a mix-LED is distinguishable. We prototype a LED-based OCC system to evaluate the efficacy of OnionCode and the results show that OnionCode achieves a higher coding efficiency and overall throughput compared with the existing hierarchical coding.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796655"}, {"primary_key": "1711465", "vector": [], "sparse_vector": [], "title": "Optimal Data Placement for Stripe Merging in Locally Repairable Codes.", "authors": ["<PERSON>", "Qingpeng Du", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Erasure coding is a storage-efficient redundancy scheme for modern clustered storage systems by storing stripes of data and parity blocks across the nodes of multiple clusters; in particular, locally repairable codes (LRC) continue to be one popular family of practical erasure codes that achieve high repair efficiency. To efficiently adapt to the dynamic requirements of access efficiency and reliability, storage systems often perform redundancy transitioning by tuning erasure coding parameters. In this paper, we apply a stripe merging approach for redundancy transitioning of LRC in clustered storage systems, by merging multiple LRC stripes to form a large LRC stripe with low storage redundancy. We show that the random placement of multiple LRC stripes that are being merged can lead to high cross-cluster transitioning bandwidth. To this end, we design an optimal data placement scheme that provably minimizes the cross-cluster traffic for stripe merging, by judiciously placing the blocks to be merged in the same cluster while maintaining the repair efficiency of LRC. We prototype and implement our optimal data placement scheme on a local cluster. Our evaluation shows that it significantly reduces the transitioning time by up to 43.2% compared to the baseline.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796704"}, {"primary_key": "1711466", "vector": [], "sparse_vector": [], "title": "Detecting and Resolving PFC Deadlocks with ITSY Entirely in the Data Plane.", "authors": ["Xinyu Crystal Wu", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "The Priority-based Flow Control (PFC) protocol is adopted to guarantee zero packet loss in many high-performance data centers. PFC, however, can induce deadlocks and in severe cases cause the entire network to be blocked. Existing solutions have focused on deadlock avoidance; unfortunately, they are not foolproof. Therefore, deadlock detection is a necessity. We propose ITSY, a novel system that correctly detects and resolves deadlocks entirely in the data plane. It works with any network topologies and routing algorithms. Unique to ITSY is the use of deadlock initial triggers, which contributes to efficient deadlock detection, mitigation, and recurrence prevention. ITSY provides three deadlock resolution mechanisms with different trade-off options. We implement ITSY for programmable switches in the P4 language. Experiments show that ITSY detects and resolves deadlocks rapidly with minimal overheads.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796798"}, {"primary_key": "1711467", "vector": [], "sparse_vector": [], "title": "Boosting Internet Card Cellular Business via User Portraits: A Case of Churn Prediction.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Internet card (IC) as a new business model emerges, which penetrates rapidly and holds the potential to foster a great business market. However, the understanding of IC user portraits is insufficient, which is the building block to boost the IC business. In this paper, we take the lead to bridge the gap by studying one large-scale dataset collected from a provincial network operator of China, which contains about 4 million IC users and 22 million traditional card (TC) users. Particularly, we first conduct a systematical analysis on usage data by investigating the difference of two types of users, examining the impact of user properties, and characterizing the spatio-temporal networking patterns. After that, we shed light on one specific business case of churn prediction by devising an IC user Churn Prediction model, named ICCP, which consists of a feature extraction component and a learning architecture design. In ICCP, both the static portrait features and temporal sequential features are extracted, and one principal component analysis block and the embedding/transformer layers are devised to learn the respective information of two types of features, which are collectively fed into the classification multilayer perceptron layer for prediction. Extensive experiments corroborate the efficacy of ICCP.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796869"}, {"primary_key": "1711468", "vector": [], "sparse_vector": [], "title": "NFlow and MVT Abstractions for NFV Scaling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The ability to dynamically scale in/out network functions (NFs) on multiple cores/servers to meet traffic demands is a key benefit of network function virtualization (NFV). The stateful NF operations make NFV scaling a challenging task: if care is not taken, NFV scaling can lead to incorrect operations and poor performance. We advocate two general abstractions, NFlow and Match-Value Table (MVT), for NFV packet processing pipelines. We present formal definitions of the abstractions and discuss how they can facilitate NFV scaling by minimizing or eliminating shared states. Using NFs implemented with the proposed abstractions, we conduct extensive experiments and demonstrate their efficacy in terms of correctness and performance of NFV scaling.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796764"}, {"primary_key": "1711471", "vector": [], "sparse_vector": [], "title": "WiRa: Enabling Cross-Technology Communication from WiFi to LoRa with IEEE 802.11ax.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Huadong Ma"], "summary": "Cross-Technology Communication (CTC) is an emerging technique that enables direct interconnection among incompatible wireless technologies. Recent work proposes CTC from IEEE 802.11b to LoRa but has a low efficiency due to their extremely asymmetric data rates. In this paper, we propose WiRa that emulates LoRa waveforms with IEEE 802.11ax to achieve an efficient CTC from WiFi to LoRa. By taking advantage of the OFDMA in 802.11ax, WiRa can use only a small Resource Unit (RU) to emulate LoRa chirps and set other RUs free for high-rate WiFi users. WiRa carefully selects the RU to avoid emulation failures and adopts WiFi frame aggregation to emulate the long LoRa frame. We propose a subframe header mapping method to identify and remove invalid symbols caused by irremovable subframe headers in the aggregated frame. We also propose a mode flipping method to solve Cyclic Prefix errors, based on our finding that different CP modes have different and even opposite impacts on the emulation of a specific LoRa symbol. We implement a prototype of WiRa on the USRP platform and commodity LoRa device. The extensive experiments demonstrate WiRa can efficiently transmit complete LoRa frames with the throughput of 40.037kbps and the symbol error rate (SER) lower than 0.1.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796831"}, {"primary_key": "1711472", "vector": [], "sparse_vector": [], "title": "A Comparative Approach to Resurrecting the Market of MOD Vehicular Crowdsensing.", "authors": ["Chaocan <PERSON>", "Yaoyu Li", "<PERSON><PERSON>", "Suining He", "<PERSON><PERSON>", "Zhenhua Li", "<PERSON><PERSON>", "<PERSON>"], "summary": "With the popularity of Mobility-on-Demand (MOD) vehicles, a new market called MOD-Vehicular-Crowdsensing (MOVE-CS) was introduced for drivers to earn more by collecting road data. Unfortunately, MOVE-CS failed after two years of operation. To identify the root cause, we survey 581 drivers and reveal its simple operation model based on blindly competitive rewards. This model brings most drivers few yields, resulting in their withdrawals. In contrast, a similar market termed MOD-Human-Crowdsensing (MOMAN-CS) remains successful thanks to a complex model based on exclusively customized rewards. Hence, we wonder whether MOVE-CS can be resurrected by learning from MOMAN-CS. Despite considerable similarity, we can hardly apply the operation model of MOMAN-CS to MOVE-CS, since drivers are also concerned with passenger missions that dominate their earnings. To this end, we analyze a large-scale dataset of 12,493 MOD vehicles, finding that drivers have explicit preference for short-term, immediate gains as well as implicit rationality in pursuit of long-term, stable profits. Therefore, we design a novel operation model for MOVE-CS, at the heart of which lies a spatial-temporal differentiation-aware task recommendation scheme empowered by submodular optimization. Applied to the dataset, our design would essentially benefit both the drivers and platform, thus possessing the potential to resurrect MOVE-CS.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796749"}, {"primary_key": "1711474", "vector": [], "sparse_vector": [], "title": "A Unified Model for Bi-objective Online Stochastic Bipartite Matching with Two-sided Limited Patience.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>peng Dai"], "summary": "Bi-objective online stochastic bipartite matching can capture a wide range of real-world problems such as online ride-hailing, crowdsourcing markets, and internet adverting, where the vertices in the left side are known in advance and that in the right side arrive from a known identical independent distribution (KIID) in an online manner. Mutual interest and limited attention-span are two common conditions and can be modeled as the edge existence probability and two-sided limited patience. Existing works fail to take them into bi-objective online optimization. This paper establishes a unified model for bi-objective online stochastic bipartite matching that can provide a general tradeoff among the matched edges (OBJ-1) and vertices (OBJ-2). We formulate two linear programs (LP) and accordingly design four LP-based parameterized online algorithms to tradeoff OBJ-1 and OBJ-2, with the best competitive ratio of (0.3528α, 0.3528β), where α, β are two positive input parameters and α + β = 1. Our hardness analysis proves that any non-adaptive algorithm cannot achieve (δ 1 , δ 2 )-competitive such that ${\\delta _1} + {\\delta _2} > 1 - \\frac{1}{e}$. Trace-driven experiments show that our algorithms can always achieve better performance and provide a flexible tradeoff.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796963"}, {"primary_key": "1711475", "vector": [], "sparse_vector": [], "title": "DNN-Driven Compressive Offloading for Edge-Assisted Semantic Video Segmentation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning has shown impressive performance in semantic segmentation, but it is still unaffordable for resource-constrained mobile devices. While offloading computation tasks is promising, the high traffic demands overwhelm the limited bandwidth. Existing compression algorithms are not fit for semantic segmentation, as the lack of obvious and concentrated regions of interest (RoIs) forces the adoption of uniform compression strategies, leading to low compression ratios or accuracy. This paper introduces STAC, a DNN-driven compression scheme tailored for edge-assisted semantic video segmentation. STAC is the first to exploit DNN's gradients as spatial sensitivity metrics for spatial adaptive compression and achieves superior compression ratio and accuracy. Yet, it is challenging to adapt this content-customized compression to videos. Practical issues include varying spatial sensitivity and huge bandwidth consumption for compression strategy feedback and offloading. We tackle these issues through a spatiotemporal adaptive scheme, which (1) takes partial strategy generation operations offline to reduce communication load, and (2) propagates compression strategies and segmentation results across frames through dense optical flow, and adaptively offloads keyframes to accommodate video content. We implement STAC on a commodity mobile device. Experiments show that STAC can save up to 20.95% of bandwidth without losing accuracy, compared to the state-of-the-art algorithm.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796657"}, {"primary_key": "1711476", "vector": [], "sparse_vector": [], "title": "Mousika: Enable General In-Network Intelligence in Programmable Switches by Knowledge Distillation.", "authors": ["<PERSON><PERSON><PERSON>", "Qing Li", "<PERSON><PERSON><PERSON>", "Guang<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Given the power efficiency and Tbps throughput of packet processing, several works are proposed to offload the decision tree (DT) to programmable switches, i.e., in-network intelligence. Though the DT is suitable for the switches' match-action paradigm, it has several limitations. E.g., its range match rules may not be supported well due to the hardware diversity; and its implementation also consumes lots of switch resources (e.g., stages and memory). Moreover, as learning algorithms (particularly deep learning) have shown their superior performance, some more complicated learning models are emerging for networking. However, their high computational complexity and large storage requirement are cause challenges in the deployment on switches. Therefore, we propose Mousika, an in-network intelligence framework that addresses these drawbacks successfully. First, we modify the DT to the Binary Decision Tree (BDT). Compared with the DT, our BDT supports faster training, generates fewer rules, and satisfies switch constraints better. Second, we introduce the teacher-student knowledge distillation in Mousika, which enables the general translation from other learning models to the BDT. Through the translation, we can not only utilize the super learning capabilities of complicated models, but also avoid the computation/memory constraints when deploying them on switches directly for line-speed processing.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796936"}, {"primary_key": "1711477", "vector": [], "sparse_vector": [], "title": "TeethPass: Dental Occlusion-based User Authentication via In-ear Acoustic Sensing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "With the rapid development of mobile devices and the fast increase of sensitive data, secure and convenient mobile authentication technologies are desired. Except for traditional passwords, many mobile devices have biometric-based authentication methods (e.g., fingerprint, voiceprint, and face recognition), but they are vulnerable to spoofing attacks. To solve this problem, we study new biometric features which are based on the dental occlusion and find that the bone-conducted sound of dental occlusion collected in binaural canals contains unique features of individual bones and teeth. Motivated by this, we propose a novel authentication system, TeethPass, which uses earbuds to collect occlusal sounds in binaural canals to achieve authentication. We design an event detection method based on spectrum variance and double thresholds to detect bone-conducted sounds. Then, we analyze the time-frequency domain of the sounds to filter out motion noises and extract unique features of users from three aspects: bone structure, occlusal location, and occlusal sound. Finally, we design an incremental learning-based Siamese network to construct the classifier. Through extensive experiments including 22 participants, the performance of TeethPass in different environments is verified. TeethPass achieves an accuracy of 96.8% and resists nearly 99% of spoofing attacks.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796951"}, {"primary_key": "1711478", "vector": [], "sparse_vector": [], "title": "NMMF-Stream: A Fast and Accurate Stream-Processing Scheme for Network Monitoring Data Recovery.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recovery of missing network monitoring data is of great significance for network operation and maintenance tasks such as anomaly detection and traffic prediction. To exploit historical data for more accurate missing data recovery, some recent studies combine the data together as a tensor to learn more features. However, the need of performing high cost data decomposition compromises their speed and accuracy, which makes them difficult to track dynamic features from streaming monitoring data. To ensure fast and accurate recovery of network monitoring data, this paper proposes NMMF-Stream, a stream-processing scheme with a context extraction module and a generation module. To achieve fast feature extraction and missing data filling with a low sampling rate, we propose several novel techniques, including the context extraction based on both positive and negative monitoring data, context validation via measuring the Pointwise Mutual Information, GRU-based temporal feature learning and memorization, and a new composite loss function to guide the fast and accurate data filling. We have done extensive experiments using two real network traffic monitoring data sets and one network latency data set. The experimental results demonstrate that, compared with three baselines, NMMF-Stream can fill the newly arrived monitoring data very quickly with much higher accuracy.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796720"}, {"primary_key": "1711479", "vector": [], "sparse_vector": [], "title": "Cost Effective MLaaS Federation: A Combinatorial Reinforcement Learning Approach.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the advancement of deep learning techniques, major cloud providers and niche machine learning service providers start to offer their cloud-based machine learning tools, also known as machine learning as a service (MLaaS), to the public. According to our measurement, for the same task, these MLaaSes from different providers have varying performance due to the proprietary datasets, models, etc. Federating different MLaaSes together allows us to improve the analytic performance further. However, naively aggregating results from different MLaaSes not only incurs significant momentary cost but also may lead to sub-optimal performance gain due to the introduction of possible false-positive results. In this paper, we propose Armol, a framework to federate the right selection of MLaaS providers to achieve the best possible analytic performance. We first design a word grouping algorithm to unify the output labels across different providers. We then present a deep combinatorial reinforcement learning based-approach to maximize the accuracy while minimizing the cost. The predictions from the selected providers are then aggregated together using carefully chosen ensemble strategies. The real-world trace-driven evaluation further demonstrates that Armol is able to achieve the same accuracy results with 67% less inference cost.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796701"}, {"primary_key": "1711480", "vector": [], "sparse_vector": [], "title": "Reinforcement Learning for Dynamic Dimensioning of Cloud Caches: A Restless Bandit Approach.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Gang Yan", "<PERSON><PERSON>"], "summary": "We study the dynamic cache dimensioning problem, where the objective is to decide how much storage to place in the cache to minimize the total costs with respect to the storage and content delivery latency. We formulate this problem as a Markov decision process, which turns out to be a restless multi-armed bandit problem and is provably hard to solve. For given dimensioning decisions, it is possible to develop solutions based on the celebrated Whittle index policy. However, Whittle index policy has not been studied for dynamic cache dimensioning, mainly because cache dimensioning needs to be repeatedly solved and jointly optimized with content caching. To overcome this difficulty, we propose a low-complexity fluid Whittle index policy, which jointly determines dimensioning and content caching. We show that this policy is asymptotically optimal. We further develop a lightweight reinforcement learning augmented algorithm dubbed fW-UCB when the content request and delivery rates are unavailable. fW-UCB is shown to achieve a sub-linear regret as it fully exploits the structure of the near-optimal fluid Whittle index policy and hence can be easily implemented. Extensive simulations using real traces support our theoretical results.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796809"}, {"primary_key": "1711481", "vector": [], "sparse_vector": [], "title": "LSync: A Universal Event-synchronizing Solution for Live Streaming.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The widespread of smart devices and the development of mobile networks brings the growing popularity of live streaming services worldwide. In addition to the video and audio transmission, a lot more media content is sent to the audiences as well, including player statistics for a sports stream, subtitles for living news, etc. However, due to the diverse transmission process between live streams and other media content, the synchronization of them has grown to be a great challenge. Unfortunately, the existing commercial solutions are not universal, which require specific server cloud services or CDN and limit the users' free choices of web infrastructures. To address the issue, we propose a lightweight universal event-synchronizing solution for live streaming, called LSync, which inserts a series of audio signals containing metadata into the original audio stream. It brings no modification to the original live broadcast process and thus fits prevalent live broadcast infrastructure. Evaluations on real system show that the proposed solution reduces the signal processing delay by at most 5.62% of an audio buffer length in mobile phones and ensures real-time signal processing. It also achieves a data rate of 156.25 bps in a specific configuration and greatly outperforms recent works.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796933"}, {"primary_key": "1711482", "vector": [], "sparse_vector": [], "title": "Maximizing h-hop Independently Submodular Functions Under Connectivity Constraint.", "authors": ["<PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Liang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This study is motivated by the maximum connected coverage problem (MCCP), which is to deploy a connected UAV network with given K UAVs in the top of a disaster area such that the number of users served by the UAVs is maximized. The deployed UAV network must be connected, since the received data by a UAV from its served users need to be sent to the Internet through relays of other UAVs. Motivated by this application, in this paper we study a more generalized problem – the h-hop independently submodular maximization problem, where the MCCP problem is one of its special cases with h = 4. We propose a $\\frac{{1 - 1/e}}{{2h + 3}}$-approximation algorithm for the h-hop independently submodular maximization problem, where e is the base of the natural logarithm. Then, one direct result is a $\\frac{{1 - 1/e}}{{11}}$-approximate solution to the MCCP problem with h = 4, which significantly improves its currently best $\\frac{{1 - 1/e}}{{32}}$-approximate solution. We finally evaluate the performance of the proposed algorithm for the MCCP problem in the application of deploying UAV networks, and experimental results show that the number of users served by deployed UAVs delivered by the proposed algorithm is up to 12.5% larger than those by existing algorithms.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796957"}, {"primary_key": "1711483", "vector": [], "sparse_vector": [], "title": "Schedule or Wait: Age-Minimization for IoT Big Data Processing in MEC via Online Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Liang", "<PERSON><PERSON><PERSON> Xu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The age of data (AoD) is identified as one of the most novel and important metrics to measure the quality of big data analytics for Internet-of-Things (IoT) applications. Meanwhile, mobile edge computing (MEC) is envisioned as an enabling technology to minimize the AoD of IoT applications by processing the data in edge servers close to IoT devices. In this paper, we study the AoD minimization problem for IoT big data processing in MEC networks. We first propose an exact solution for the problem by formulating it as an Integer Linear Program (ILP). We then propose an efficient heuristic for the offline AoD minimization problem. We also devise an approximation algorithm with a provable approximation ratio for a special case of the problem, by leveraging the parametric rounding technique. We thirdly develop an online learning algorithm with a bounded regret for the online AoD minimization problem under dynamic arrivals of IoT requests and uncertain network delay assumptions, by adopting the Multi-Armed Bandit (MAB) technique. We finally evaluate the performance of the proposed algorithms by extensive simulations and implementations in a real test-bed. Results show that the proposed algorithms outperform existing approaches by reducing the AoD around 10%.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796718"}, {"primary_key": "1711484", "vector": [], "sparse_vector": [], "title": "AoI-centric Task Scheduling for Autonomous Driving Systems.", "authors": ["Cheng<PERSON> Xu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "An Autonomous Driving System (ADS) uses a plethora of sensors and many deep learning based tasks to aid its perception, prediction, motion planning, and vehicle control. To ensure road safety, those tasks should be synchronized and use the latest sensing data, which is challenging since 1) different sensors have different sensing periods, 2) the tasks are interdependent, 3) computing resource is limited. This work is the first that uses Age of Information (AoI) as the performance metric for task scheduling in an ADS. We show that minimizing AoI is equivalent to jointly minimizing the response time and maximizing the throughput. We formally formulate the AoI-centric task scheduling problem. To derive practical scheduling solutions, we extend the formulation and formulate the optimal AoI-centric periodic scheduling problem with a given cycle. A reinforcement learning-based solution is designed accordingly. With experiments simulated according to the Apollo driving system, we compare the scheduling performance of the AoI-centric task scheduling with Apollo's schedulers from the perspective of AoI, throughput, and worst case response time. The experiment results show that the maximum AoI in the proposed scheduling solution with 4 cores is lower than that in Apollo's schedulers with 8 cores.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796958"}, {"primary_key": "1711485", "vector": [], "sparse_vector": [], "title": "mmECG: Monitoring Human Cardiac Cycle in Driving Environments Leveraging Millimeter Wave.", "authors": ["Xiangyu Xu", "<PERSON><PERSON>", "Chengguang Ma", "Yanzhi Ren", "Hongbo Liu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The continuously increasing time spent on car trips in recent years brings growing attention to the physical and mental health of drivers on roads. As one of the key vital signs, the heartbeat is a critical indicator of drivers' health states. Most existing studies on heartbeat monitoring either require sensor attachment or could only provide sketchy heart rates. Moreover, most approaches require the subject to remain stationary or a quiet measuring environment, which is hard to apply to dynamic driving environments. In this paper, we propose a contactless cardiac cycle monitoring system, mmECG, which leverages Commercial-Off-The-Shelf mmWave radar to estimate the fine-grained heart movements of drivers in moving vehicles. By exploring the principle of mmWave signal-based sensing, we first perform studies in static environments and find the fine-grained heart movements, represented as stages of atria and ventricles in repetitive cardiac cycles, can be captured by the FMCW-based mmWave radar as phase changes in signals. Whereas in driving environments, such phase changes are caused and influenced by not only the heartbeat of drivers but also driving operations and vehicle dynamics. To further extract the minute heart movements of drivers and eliminate other influences in phase changes, we construct a movement mixture model to represent the phase changes caused by different movements, and further design a hierarchy variational mode decomposition (VMD) approach to extract and estimate the essential heart movement in mmWave signals. Finally, based on the extracted phase changes, mmECG reconstructs the cardiac cycle by estimating fine-grained movements of atria and ventricles leveraging a template-based optimization method. Experimental results involving 25 drivers in real driving scenarios demonstrate that mmECG can accurately estimate not only heart rates but also cardiac cycles of drivers in real driving environments.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796912"}, {"primary_key": "1711486", "vector": [], "sparse_vector": [], "title": "Online Data Valuation and Pricing for Machine Learning Tasks in Mobile Health.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile health (mHealth) applications, benefiting from mobile computing, have emerged rapidly in recent years, and generated a large volume of mHealth data. However, these valuable data are dispersed across isolated devices or organizations, which hinders discovering insights underlying the aggregated data. Considering the online characteristics of mHealth tasks, there is an urgent need for online data acquisition. In this paper, we present the first online data Valuation And Pricing mechanism, namely VAP, to incentive users to contribute mHealth data for machine learning (ML) tasks in mHealth systems. Under the framework of Bayesian ML, we propose a new metric based on the concept of entropy, to evaluate data valuation during model training in an online manner. In proportion to the data valuation, we then determine payments as compensations for users to contribute their data. We formulate this pricing problem as a contextual multi-armed bandit with the goal of profit maximization and propose a new algorithm based on the characteristics of pricing. We also extend VAP to general ML models. Finally, we have evaluated VAP on two real-world mHealth data sets. Evaluation results show that VAP outperforms the state-of-the-art valuation and pricing mechanisms in terms of computational complexity and extracted profit.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796669"}, {"primary_key": "1711487", "vector": [], "sparse_vector": [], "title": "Distributed Bandits with Heterogeneous Agents.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "This paper tackles a multi-agent bandit setting where M agents cooperate together to solve the same instance of a K-armed stochastic bandit problem. The agents are heterogeneous: each agent has limited access to a local subset of arms and the agents are asynchronous with different gaps between decision-making rounds. The goal for each agent is to find its optimal local arm, and agents can cooperate by sharing their observations with others. While cooperation between agents improves the performance of learning, it comes with an additional complexity of communication between agents. For this heterogeneous multi-agent setting, we propose two learning algorithms, CO-UCB and CO-AAE. We prove that both algorithms achieve order-optimal regret, which is $O\\left({{\\sum _{i:{{\\bar \\Delta }_i} > 0}}\\log T/{{\\tilde \\Delta }_i}}\\right)$, where ${\\tilde \\Delta _i}$ is the minimum suboptimality gap between the reward mean of arm i and any local optimal arm. In addition, a careful selection of the valuable information for cooperation, CO-AAE achieves a low communication complexity of O(log T). Last, numerical experiments verify the efficiency of both algorithms.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796901"}, {"primary_key": "1711488", "vector": [], "sparse_vector": [], "title": "6Forest: An Ensemble Learning-based Approach to Target Generation for Internet-wide IPv6 Scanning.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "IPv6 target generation is the critical step for fast IPv6 scanning for Internet-wide surveys. Existing techniques, however, commonly suffer from low hit rates due to inappropriate space partition caused by the outlier addresses and short-sighted splitting indicators. To address the problem, we propose 6Forest, an ensemble learning-based approach for IPv6 target generation that is from a global perspective and resilient to outlier addresses. Given a set of known addresses, 6Forest first considers it as an initial address region and then iteratively divides the IPv6 address space into smaller regions using a maximum-covering splitting indicator. Before a round of space partition, it builds a forest structure for each region and exploits an enhanced isolation forest algorithm to remove the outlier addresses. Finally, it pre-scans samples from the divided address regions and based on the results generates IPv6 addresses. Experiments on eight large-scale candidate datasets indicate that, compared with the state-of-the-art methods in IPv6 worldwide scanning, 6Forest can achieve up to 116.5% improvement for low-budget scanning and 15× improvement for high-budget scanning.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796925"}, {"primary_key": "1711493", "vector": [], "sparse_vector": [], "title": "FlexPatch: Fast and Accurate Object Detection for On-device High-Resolution Live Video Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present FlexPatch, a novel mobile system to enable accurate and real-time object detection over high-resolution video streams. A widely-used approach for real-time video analysis is detection-based tracking (DBT), i.e., running the heavy-but-accurate detector every few frames and applying a lightweight tracker for in-between frames. However, the approach is limited for real-time processing of high-resolution videos in that i) a lightweight tracker fails to handle occlusion, object appearance changes, and occurrences of new objects, and ii) the detection results do not effectively offset tracking errors due to the high detection latency. We propose tracking-aware patching technique to address such limitations of the DBT frameworks. It effectively identifies a set of subareas where the tracker likely fails and tightly packs them into a small-sized rectangular area where the detection can be efficiently performed at low latency. This prevents the accumulation of tracking errors and offsets the tracking errors with frequent fresh detection results. Our extensive evaluation shows that FlexPatch not only enables real-time and power-efficient analysis of high-resolution frames on mobile devices but also improves the overall accuracy by 146% compared to baseline DBT frameworks.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796984"}, {"primary_key": "1711494", "vector": [], "sparse_vector": [], "title": "DeepEar: Sound Localization with Binaural Microphones.", "authors": ["<PERSON><PERSON>", "Yuan<PERSON> Zheng"], "summary": "Binaural microphones, referring to two microphones with artificial human-shaped ears, are pervasively used in humanoid robots and hearing aids improving sound quality. In many applications, it is crucial for such robots to interact with humans by finding the voice direction. However, sound source localization with binaural microphones remains challenging, especially in multi-source scenarios. Prior works utilize microphone arrays to deal with the multi-source localization problem. Extra arrays yet incur higher deployment costs and take up more space. However, human brains have evolved to locate multiple sound sources with only two ears. Inspired by this fact, we propose DeepEar, a binaural microphone-based localization system that can locate multiple sounds. To this end, we develop a neural network to mimic the acoustic signal processing pipeline of the human auditory system. Different from hand-crafted features used in prior works, DeepEar can automatically extract useful features for localization. More importantly, the trained neural networks can be extended and adapted to new environments with a minimum amount of extra training data. Experiment results show that DeepEar can substantially outperform the state-of-the-art deep learning approach, with a sound detection accuracy of 93.3% and an azimuth estimation error of 7.4 degrees in multisource scenarios.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796850"}, {"primary_key": "1711498", "vector": [], "sparse_vector": [], "title": "Encoding-based Range Detection in Commodity RFID Systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xingyu Chen", "<PERSON>", "<PERSON><PERSON>"], "summary": "RFID technologies have been widely used for item-level object monitoring and tracking in industrial applications. In this paper, we study the problem of range detection in a commodity RFID system, which aims to quickly figure out whether there are any target tags that hold specific data between a lower and upper boundary. This is important to help users pinpoint tagged objects of interest (if any) and give an early warning for reducing the potential risk, e.g., temperature monitoring for fire safety. We propose a time-efficient protocol called encoding range query (EnRQ). The basic idea is to use a sparse vector to separate target tags from the others with a few select commands. The sparse vector is specifically designed by encoding the tag's data based on notational systems. We implement EnRQ in commodity RFID systems with no need for any hardware modifications. Extensive experiments show that EnRQ can improve the time efficiency by more than 40% on average, compared with the state-of-the-art.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796900"}, {"primary_key": "1711499", "vector": [], "sparse_vector": [], "title": "ANTIGONE: Accurate Navigation Path Caching in Dynamic Road Networks leveraging Route APIs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Navigation paths and corresponding travel times play a key role in location-based services (LBS) of which large-scale navigation path caching constitutes a fundamental component. In view of the highly dynamic real-time traffic changes in road networks, the main challenge amounts to updating paths in the cache in a fashion that incurs minimal costs due to querying external map service providers and cache maintenance. In this paper, we propose a hybrid graph approach in which an LBS provider maintains a dynamic graph with edge weights representing travel times, and queries the external map server so as to ascertain high fidelity of the cached paths subject to stringent limitations on query costs. We further deploy our method in one of the biggest on-demand food delivery platforms and evaluate the performance against state-of-the-art methods. Our experimental results demonstrate the efficacy of our approach in terms of both substantial savings in the number of required queries and superior fidelity of the cached paths.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796817"}, {"primary_key": "1711501", "vector": [], "sparse_vector": [], "title": "GADGET: Online Resource Optimization for Scheduling Ring-All-Reduce Learning Jobs.", "authors": ["<PERSON><PERSON><PERSON> Yu", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fueled by advances in distributed deep learning (DDL), recent years have witnessed a rapidly growing demand for resource-intensive distributed/parallel computing to process DDL computing jobs. To resolve network communication bottleneck and load balancing issues in distributed computing, the so-called \"ring-all-reduce\" decentralized architecture has been increasingly adopted to remove the need for dedicated parameter servers. To date, however, there remains a lack of theoretical understanding on how to design resource optimization algorithms for efficiently scheduling ring-all-reduce DDL jobs in computing clusters. This motivates us to fill this gap by proposing a series of new resource scheduling designs for ring-all-reduce DDL jobs. Our contributions in this paper are threefold: i) We propose a new resource scheduling analytical model for ring-all-reduce deep learning, which covers a wide range of objectives in DDL performance optimization (e.g., excessive training avoidance, energy efficiency, fairness); ii) Based on the proposed performance analytical model, we develop an efficient resource scheduling algorithm called GADGET (greedy ring-all-reduce distributed graph embedding technique), which enjoys a provable strong performance guarantee; iii) We conduct extensive trace-driven experiments to demonstrate the effectiveness of the GADGET approach and its superiority over the state of the art.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796785"}, {"primary_key": "1711502", "vector": [], "sparse_vector": [], "title": "Physical-Level Parallel Inclusive Communication for Heterogeneous IoT Devices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The proliferation of Internet of Things (IoT) has transformed the way people interact with the world. Various kinds of wireless protocols have been developed to support diverse types of IoT communications. Unfortunately, the lack of spectrum resources puts a hard limit on managing the large-scale heterogeneous IoT system. Although previous works alleviate this strain by coordinating transmission power, time slots, and sub-channels, they may not be feasible in future IoT applications with dense deployments. In this paper, we explore a physical-level parallel inclusive communication paradigm for the coexistence of Wi-Fi and ZigBee, which leverages novel bits embedding approaches on the OQPSK protocol to enable both Wi-Fi and ZigBee IoT devices to decode the same inclusive signals at the same time but with each one's different data. By carefully crafting the inclusive signals using legacy Wi-Fi protocol, the overlapping spectrum can be simultaneously re-used by both protocols, expecting a maximum data rate (250kbps) for ZigBee devices and up to 3.75Mbps for a Wi-Fi pair over only a 2MHz bandwidth. The achieved spectrum efficiency outperforms a majority of CTC schemes and parallel communication designs. Compared with existing works on parallel communication, our proposed system is the first one that achieves an entire software-level design, which can be readily implemented on Commercial Off-The-Shelf (COTS) devices without any hardware modification. Based on extensive real-world experiments on both USRP and COTS device platforms, we demonstrate the feasibility, generality, and efficiency of the proposed new paradigm.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796876"}, {"primary_key": "1711503", "vector": [], "sparse_vector": [], "title": "LoRadar: An Efficient LoRa Channel Occupancy Acquirer based on Cross-channel Scanning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Huadong Ma"], "summary": "LoRa is widely deployed for various applications. Though the knowledge of the channel occupancy is the prerequisite of all aspects of network management, acquiring the channel occupancy for LoRa is challenging due to the large number of channels to be detected. In this paper, we propose LoRadar, a novel LoRa channel occupancy acquirer based on cross-channel scanning. Our in-depth study finds that Channel Activity Detection (CAD) in a narrow band can indicate the channel activities of wide bands because they have the same slope in the time-frequency domain. Based on our finding, we design the cross-channel scanning mechanism that infers the channel occupancy states of all the overlapping channels by the distribution of CAD results. We elaborately select and adjust the CAD settings to enhance the distribution features. We also design the pattern correction method to cope with distribution distortions. We implement LoRadar on commodity LoRa platforms and evaluate its performance on the indoor testbed and the outdoor deployed network. The experimental results show that LoRadar can achieve a detection accuracy of 0.99 and reduce the acquisition overhead by up to 0.90, compared to existing traversal-based methods.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796845"}, {"primary_key": "1711505", "vector": [], "sparse_vector": [], "title": "AI in 5G: The Case of Online Distributed Transfer Learning over Edge Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Zhu", "<PERSON><PERSON>", "<PERSON>"], "summary": "Transfer learning does not train from scratch but leverages existing models to help train the new model of better accuracy. Unfortunately, realizing transfer learning in distributed cloud-edge networks faces critical challenges such as online training, uncertain network environments, time-coupled control decisions, and the balance between resource consumption and model accuracy. We formulate distributed transfer learning as a non-linear mixed-integer program of long-term cost optimization. We design polynomial-time online algorithms by exploiting the real-time trade-off between preserving previous decisions and applying new decisions, based on primal-dual one-shot solutions for each single time slot. While orchestrating model placement, data dispatching, and inference aggregation, our approach produces new models via combining the existing offline models and the online models being trained using weights adaptively updated based on inference upon data samples that dynamically arrive. Our approach provably incurs the number of inference mistakes no greater than a constant times that of the single best model in hindsight, and achieves a constant competitive ratio for the total cost. Evaluations have confirmed the superior performance of our approach compared to alternatives on real-world traces.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796779"}, {"primary_key": "1711506", "vector": [], "sparse_vector": [], "title": "VSiM: Improving QoE Fairness for Video Streaming in Mobile Environments.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bangbang Ren", "<PERSON>", "<PERSON><PERSON> Fu"], "summary": "The rapid growth of mobile video traffic and user demand poses a more stringent requirement for efficient bandwidth allocation in mobile networks where multiple users may share a bottleneck link. This provides content providers an opportunity to optimize multiple users' experiences jointly, but users often suffer short connection durations and frequent handoffs because of their high mobility. This paper proposes an end-to-end scheme, VSiM, to support mobile video streaming applications in heterogeneous wireless networks. The key idea is allocating bottleneck bandwidth among multiple users based on their mobility profiles and Quality of Experience (QoE)-related knowledge to achieve max-min QoE fairness. Besides, the QoE of buffer-sensitive clients is further improved by the novel server push strategy based on HTTP/3 protocol without affecting the existing bandwidth allocation approach or sacrificing other clients' view quality. We evaluated VSiM experimentally in both simulations and a lab testbed on top of the HTTP/3 protocol. We find that the clients' QoE fairness of VSiM achieves more than 40% improvement compared with state-of-the-art solutions, i.e., the viewing quality of clients in VSiM can be improved from 720p to 1080p in resolution. Meanwhile, VSiM provides about 20% improvement on average of the averaged QoE.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796725"}, {"primary_key": "1711507", "vector": [], "sparse_vector": [], "title": "Impact of Later-Stages COVID-19 Response Measures on Spatiotemporal Mobile Service Usage.", "authors": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The COVID-19 pandemic has affected our lives and how we use network infrastructures in an unprecedented way. While early studies have started shedding light on the link between COVID-19 containment measures and mobile network traffic, we presently lack a clear understanding of the implications of the virus outbreak, and of our reaction to it, on the usage of mobile apps. We contribute to closing this gap, by investigating how the spatiotemporal usage of mobile services has evolved through different response measures enacted in France during a continued seven-month period in 2020 and 2021. Our work complements previous studies in several ways: (i) it delves into individual service dynamics, whereas previous studies have not gone beyond broad service categories; (ii) it encompasses different types of containment strategies, allowing to observe their diverse effects on mobile traffic; (iii) it covers both spatial and temporal behaviors, providing a comprehensive view on the phenomenon. These elements of novelty let us lay new insights on how the demands for hundreds of different mobile services are reacting to the new environment set forth by the pandemics.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796888"}, {"primary_key": "1711508", "vector": [], "sparse_vector": [], "title": "Cutting Tail Latency in Commodity Datacenters with Cloudburst.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Long tail latency of short flows (or messages) greatly affects user-facing applications in datacenters. Prior solutions to the problem introduce significant implementation complexities, such as global state monitoring, complex network control, or non-trivial switch modifications. While promising superior performance, they are hard to implement in practice.This paper presents Cloudburst, a simple, effective yet readily deployable solution achieving similar or even better results without introducing the above complexities. At its core, Cloudburst explores forward error correction (FEC) over multipath — it proactively spreads FEC-coded packets generated from messages over multipath in parallel, and recovers them with the first few arriving ones. As a result, Cloudburst is able to obliviously exploit underutilized paths, thus achieving low tail latency. We have implemented Cloudburst as a user-space library, and deployed it on a testbed with commodity switches. Our testbed and simulation experiments show the superior performance of Cloudburst. For example, Cloudburst achieves 63.69% and 60.06% reduction in 99th percentile message/flow completion time (FCT) compared to DCTCP and PIAS, respectively.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796898"}, {"primary_key": "1711509", "vector": [], "sparse_vector": [], "title": "Multi-Entanglement Routing Design over Quantum Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Quantum networks are considered as a promising future platform for quantum information exchange and quantum applications, which have capabilities far beyond the traditional communication networks. Remote quantum entanglement is an essential component of a quantum network. How to efficiently design a multi-routing entanglement protocol is a fundamental yet challenging problem. In this paper, we study a quantum entanglement routing problem to simultaneously maximize the number of quantum-user pairs and their expected throughput. Our approach is to formulate the problem as two sequential integer programming steps. We propose efficient entanglement routing algorithms for the two integer programming steps and analyze their time complexity and performance bounds. Results of evaluation highlight that our approach outperforms existing solutions in both served quantum-user pairs numbers and the network expected throughput.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796810"}, {"primary_key": "1711510", "vector": [], "sparse_vector": [], "title": "Energy-Efficient Trajectory Optimization for Aerial Video Surveillance under QoS Constraints.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Surveillance drones are unmanned aerial vehicles (UAVs) that are utilized to collect video recordings of targets. In this paper, we propose a novel design framework for aerial video surveillance in urban areas, where a cellular-connected UAV captures and transmits videos to the cellular network that services users. Fundamental challenges arise due to the limited onboard energy and quality of service (QoS) requirements over environment-dependent air-to-ground cellular links, where UAVs are usually served by the sidelobes of base stations (BSs). We aim to minimize the energy consumption of the UAV by jointly optimizing the mission completion time and UAV trajectory as well as transmission scheduling and association, subject to QoS constraints. The problem is formulated as a mixed-integer nonlinear programming (MINLP) problem by taking into account building blockage and BS antenna patterns. We first consider the average performance for uncertain local environments, and obtain an efficient sub-optimal solution by employing graph theory and convex optimization techniques. Next, we investigate the site-specific performance for specific urban local environments. By reformulating the problem as a Markov decision process (MDP), a deep reinforcement learning (DRL) algorithm is proposed by employing a dueling deep Q-network (DQN) neural network model with only local observations of sampled rate measurements. Simulation results show that the proposed solutions achieve significant performance gains over baseline schemes.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796696"}, {"primary_key": "1711511", "vector": [], "sparse_vector": [], "title": "Optimizing Coverage with Intelligent Surfaces for Indoor mmWave Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Reconfigurable intelligent surfaces (RISs) have been proposed to increase coverage in millimeter-wave networks by providing an indirect path from transmitter to receiver when the line-of-sight (LoS) path is blocked. In this paper, the problem of optimizing the locations and orientations of multiple RISs is considered for the first time. An iterative coverage expansion algorithm based on gradient descent is proposed for indoor scenarios where obstacles are present. The goal of this algorithm is to maximize coverage within the shadowed regions where there is no LoS path to the access point. The algorithm is guaranteed to converge to a local coverage maximum and is combined with an intelligent initialization procedure to improve the performance and efficiency of the approach. Numerical results demonstrate that, in dense obstacle environments, the proposed algorithm doubles coverage compared to a solution without RISs and provides about a 10% coverage increase compared to a brute force sequential RIS placement approach.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796762"}, {"primary_key": "1711513", "vector": [], "sparse_vector": [], "title": "SAH: Fine-grained RFID Localization with Antenna Calibration.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Xingyu Chen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Radio frequency identification (RFID) based localization has attracted increasing attentions due to competitive advantages of RFID tags: unique identification, low-cost, and battery-free. Although many advanced phase-based localization methods are proposed, few of them take fully the unknown phase center (PC) and the phase offset (PO) into account, which however are the key factors in fine-grained localization. In this paper, we propose a novel localization algorithm called Segment Aligned Hologram (SAH) that jointly calibrates the PC and the PO. SAH first builds a phase matrix and then designs a phase alignment algorithm based on the phase matrix for reducing the multi-path effect. Afterwards, SAH constructs a hologram for calibration and localization, which greatly reduces the system errors. We implement SAH through commercial RFID devices. Extensive experiments show that SAH can achieve a fine-grained localization accuracy in both the lateral and the radial directions with only a single antenna.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796672"}, {"primary_key": "1711515", "vector": [], "sparse_vector": [], "title": "An RFID and Computer Vision Fusion System for Book Inventory using Mobile Robot.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Gu", "<PERSON><PERSON>", "Dong<PERSON> Liu", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Mobile robot-assisted book inventory such as book identification and book order detection has become increasingly popular in smart library, replacing the manual book inventory which is time-consuming and error-prone. The existing systems are either computer vision (CV)-based or RFID-based, however several limitations are inevitable. CV-based systems may not be able to identify books effectively due to low accuracy of detecting texts on book spine. RFID tags attached to books can be used to identify a book uniquely. However, in high tag density scenarios such as library, tag coupling effects of adjacent tags may seriously affect the accuracy of tag reading. To overcome these limitations, this paper presents a novel RFID and CV fusion system for Book Inventory using mobile robot (RC-BI). RFID and CV are first used individually to obtain book order, then the information will be fused by the sequence based matching algorithm to remove ambiguity and improve overall accuracy. Specifically, we address three technical challenges. We design a deep neural network (DNN) model with multiple inputs and mixed data to filter out interference of RFID tags on other tiers, and propose a video information extracting schema to extract book spine information accurately, and use strong link to align and match RFID- and CV-based timestamp vs. book-name sequences to avoid errors during fusion. Extensive experiments indicate that our system achieves an average accuracy of 98.4% for tier filtering and an average accuracy of 98.9% for book order, significantly outperforming the state-of-the-arts.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796711"}, {"primary_key": "1711516", "vector": [], "sparse_vector": [], "title": "RC6D: An RFID and CV Fusion System for Real-time 6D Object Pose Estimation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies the problem of 6D pose estimation, which is practically important in various application scenarios such as robotic-based object grasping, obstacle avoidance in autonomous driving scene, and object integration in mixed reality. However, existing methods suffer from at least one of the five major limitations: dependence on object identification, complex deployment, difficulty in data collection, low accuracy, and incomplete estimation. To overcome the above limitations, this paper proposes an RC6D system, which is the first to estimate 6D poses by fusing RFID and Computer Vision (CV) data with multi-modal deep learning techniques. In RC6D, we first detect 2D keypoints through a deep learning approach. We then propose a novel RFID-CV fusion neural network to predict the depth of the scene, and use the estimated depth information to expand the 2D keypoints to 3D keypoints. Finally, we model the coordinate correspondences between the detected 2D-3D keypoints, which is applied to estimate the 6D pose of the target object. When implementing RC6D, we mainly address the following three technical challenges. (i) To predict 6D poses without using the CAD model, we propose a network architecture for monocular depth estimation. (ii) To train the neural network for 6D pose estimation without time-consuming 6D labeling, we use an unsupervised learning algorithm based on 2D-3D point pair matching. (iii) To detect the subject of the object without identification, we leverage optical flow to restrict the object and RFID to directly obtain its information. The experimental results show that the localization error of RC6D is less than 10 cm with a probability higher than 90.64% and its orientation estimation error is less than 10° with a probability higher than 79.63%. Hence, the proposed RC6D system performs much better than the state-of-the-art related solutions.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796802"}, {"primary_key": "1711517", "vector": [], "sparse_vector": [], "title": "Dual-track Protocol Reverse Analysis Based on Share Learning.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Private protocols, whose specifications are agnostic, are widely used in the Industrial Internet. While providing customized service, they also raise essential security concerns as well, due to their agnostic nature. The Protocol Reverse Analysis (PRA) techniques are developed to infer the specifications of private protocols. However, the conventional PRA techniques are far from perfection for the following reasons: (i) Error propagation: Canonical solutions strictly follow the \"from keyword extraction to message clustering\" serial structure, which deteriorates the performance for ignoring the interplay between the sub-tasks, and the error will flow and accumulate through the sequential workflow. (ii) Increasing diversity: As the protocols' diversities of characteristics increase, tailoring for specific types of protocols becomes infeasible. To address these issues, we design a novel dual-track framework SPRA, and propose Share Learning, a new concept of protocol reverse analysis. Particularly, based on the share layer for protocol learning, SPRA builds a parallel workflow to co-optimize both the generative model for keyword extraction and the probability-based model for message clustering, which delivers automatic and robust syntax inference across diverse protocols and greatly improves the performance. Experiments on five real-world datasets demonstrate that the proposed SPRA achieves better performance compared with the state-of-art PRA methods.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796964"}, {"primary_key": "1711519", "vector": [], "sparse_vector": [], "title": "Online File Caching in Latency-Sensitive Systems with Delayed Hits and Bypassing.", "authors": ["<PERSON>", "Haisheng Tan", "<PERSON><PERSON><PERSON>", "Zhenhua Han", "Shaofeng H.-<PERSON><PERSON> Jiang", "Xiangyang Li"], "summary": "In latency-sensitive file caching systems such as Content Delivery Networks (CDNs) and Mobile Edge Computing (MEC), the latency of fetching a missing file to the local cache can be significant. Recent studies have revealed that successive requests of the same missing file before the fetching completes could still suffer latency (so-called delayed hits).Motivated by the practical scenarios, we study the online general file caching problem with delayed hits and bypassing, i.e., a request may be bypassed and processed directly at the remote data center. The objective is to minimize the total request latency. We show a general reduction that turns a traditional file caching algorithm to one that can handle delayed hits. We give an O(Z 3/2 logK)-competitive algorithm called CaLa with this reduction, where Z is the maximum fetching latency of any file and K is the cache size, and we show a nearly-tight lower bound Ω(Z logK) for our ratio. Extensive simulations based on the production data trace from Google and the Yahoo benchmark illustrate that CaLa can reduce the latency by up to 9.42% compared with the state-of-the-art scheme dealing with delayed hits without bypassing, and this improvement increases to 32.01% if bypassing is allowed.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796969"}, {"primary_key": "1711520", "vector": [], "sparse_vector": [], "title": "CASVA: Configuration-Adaptive Streaming for Live Video Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The advent of high-accuracy and resource-intensive deep neural networks (DNNs) has fulled the development of live video analytics, where camera videos need to be streamed over the network to edge or cloud servers with sufficient computational resources. Although it is promising to strike a balance between available bandwidth and server-side DNN inference accuracy by adjusting video encoding configurations, the influences of fine-grained network and video content dynamics on configuration performance should be addressed. In this paper, we propose CASVA, a Configuration-Adaptive Streaming framework designed for live Video Analytics. The design of CASVA is motivated by our extensive measurements on how video configuration affects its bandwidth requirement and inference accuracy. To handle the complicated dynamics in live video analytics streaming, CASVA trains a deep reinforcement learning model which does not make any assumptions about the environment but learns to make configuration choices through its experiences. A variety of real-world network traces are used to drive the evaluation of CASVA. The results on a multitude of video types and video analytics tasks show the advantages of CASVA over state-of-the-art solutions.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796875"}, {"primary_key": "1711521", "vector": [], "sparse_vector": [], "title": "Enabling Low-latency-capable Satellite-Ground Topology for Emerging LEO Satellite Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The network topology design is critical for achieving low latency and high capacity in future integrated satellite and terrestrial networks (ISTN). However, existing studies mainly focus on the design of inter-satellite topology of ISTN, and very little is known about the design of satellite-ground topology, as well as its impact on the attainable network performance.In this paper, we conduct a quantitative study on the impact of various satellite-ground designs on the network performance of ISTN. We identify that the high-density and high-dynamicity characteristics of emerging mega-constellations have jointly imposed big challenges, such as significant routing instability, low network reachability, high latency and jitter on the ISTN paths. To alleviate the above challenges, we formulate the Low-latency Satellite-Ground Interconnecting (LSGI) problem, targeting at the integration of space and ground segment in the ISTN, while minimizing the maximum transmission latency and keeping routing stable. We further design algorithms to solve the LSGI problem through wisely coordinating the establishment of ground-to-satellite links among distributed ground stations. Comprehensive experiment results demonstrate that our solution can outperform existing related schemes by about 19% reduction of the latency and 70% reduction of the jitter on average, while sustaining the highest network reachability.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796886"}, {"primary_key": "1711525", "vector": [], "sparse_vector": [], "title": "Can We Obtain Fine-grained Heartbeat Waveform via Contact-free RF-sensing?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Contact-free vital-signs monitoring enabled by radio frequency (RF) sensing is gaining increasing attention, thanks to its non-intrusiveness, noise-resistance, and low cost. Whereas most of these systems only perform respiration monitoring or retrieve heart rate, few can recover fine-grained heartbeat waveform. The major reason is that, though both respiration and heartbeat cause detectable micro-motions on human bodies, the former is so strong that it overwhelms the latter. In this paper, we aim to answer the question in the paper title, by demystifying how heartbeat waveform can be extracted from RF-sensing signal. Applying several mainstream methods to recover heartbeat waveform from raw RF signal, our results reveal that these methods may not achieve what they have claimed, mainly because they assume linear signal mixing whereas the composition between respiration and heartbeat can be highly nonlinear. To overcome the difficulty of decomposing nonlinear signal mixing, we leverage the power of a novel deep generative model termed variational encoder-decoder (VED). Exploiting the universal approximation ability of deep neural networks and the generative potential of variational inference, VED demonstrates a promising capability in recovering fine-grained heartbeat waveform from RF-sensing signal; this is firmly validated by our experiments with 12 subjects and 48-hour data.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796905"}, {"primary_key": "1711526", "vector": [], "sparse_vector": [], "title": "CausalRD: A Causal View of Rumor Detection via Eliminating Popularity and Conformity Biases.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ce Li", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A large amount of disinformation on social media has penetrated into various domains and brought significant adverse effects. Understanding their roots and propagation becomes desired in both academia and industry. Prior literature has developed many algorithms to identify this disinformation, particularly rumor detection. Some leverage the power of deep learning and have achieved promising results. However, they all focused on building predictive models and improving forecast accuracy, while two important factors - popularity and conformity biases - that play critical roles in rumor spreading behaviors are usually neglected.To overcome such an issue and alleviate the bias from these two factors, we propose a rumor detection framework to learn debiased user preference and effective event representation in a causal view. We first build a graph to capture causal relationships among users, events, and their interactions. Then we apply the causal intervention to eliminate popularity and conformity biases and obtain debiased user preference representation. Finally, we leverage the power of graph neural networks to aggregate learned user representation and event features for the final event type classification. Empirical experiments conducted on two real-world datasets demonstrate the effectiveness of our proposed approach compared to several cutting-edge baselines.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796678"}, {"primary_key": "1711527", "vector": [], "sparse_vector": [], "title": "Batch Adaptative Streaming for Video Analytics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Zhang", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Video streaming plays a critical role in the video analytics pipeline and thus its adaptation scheme has been a focus of optimization. As machine learning algorithms have become main consumers of video contents, the streaming adaptation decision should be made to optimize their inference performance. Existing video streaming adaptation schemes for video analytics are usually designed to adapt to bandwidth and content variations separately, which fail to consider the coordination between transmission and computation. Given the nature of batch transmission in video streaming and batch processing in deep learning-based inference, we observe that the choices of the batch sizes directly affects the bandwidth efficiency, the response delay and the accuracy of the deep learning inference in video analytics. In this work, we investigate the effect of the batch size in transmission and processing, formulate the optimal batch size adaptation problem, and further develop the deep reinforcement learning-based solution. Practical issues are further addressed for Implementation. Extensive simulations are conducted for performance evaluation, whose results demonstrate the superiority of our proposed batch adaptive streaming approach over the baseline streaming approaches.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796853"}, {"primary_key": "1711528", "vector": [], "sparse_vector": [], "title": "On Designing Secure Cross-user Redundancy Elimination for WAN Optimization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Redundancy elimination (RE) systems allow network users to remove duplicate parts in their messages by introducing caches at both message senders' and receivers' sides. While RE systems have been successfully deployed for handling unencrypted traffic, making them work over encrypted links is still open. A few solutions have been proposed recently, however they either completely violate end-to-end security or focus on single-user setting. In this paper, we present a highly secure RE solution which supports cross-user redundancy eliminations on encrypted traffics. Our solution not only preserves the end-to-end security against outside adversaries, but also protects users' privacy against semi-honest RE agents. Furthermore, our solution can defend malicious users' poisoning attack, which is crucial for cross-user RE systems but has never been studied before. In cross-user RE systems, since all users inside a LAN write into a shared, global cache and use it to recover their original messages from deduplicated ones, the poisoning attack is prone to happen, and cause systematic damage to all users even when only one user is malicious and injects poisoned data into the cache. We rigorously prove our solution's security properties, and demonstrate its promising performance via testing the proof-of-concept implementation with real-world internet traffic data.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796893"}, {"primary_key": "1711529", "vector": [], "sparse_vector": [], "title": "DroneSense: Leveraging Drones for Sustainable Urban-scale Sensing of Open Parking Spaces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Huadong Ma"], "summary": "Energy and cost are two primary concerns when leveraging drones for urban sensing. With the advances of wireless charging technologies and the inspiration from the sparse crowdsensing paradigm, this paper proposes a novel drone-based collaborative sparse-sensing framework DroneSense, demonstrating its feasibility for sustainable urban-scale sensing. We focus on a typical use case, i.e., leveraging DroneSense to sense open parking spaces. DroneSense selects a minimum number of Points of Interest (POIs) to schedule drones for physical data sensing and then infers the parking occupancy of the remaining POIs to meet the overall quality requirement. However, drone-based sensing is different from human-centric crowdsensing, resulting in a series of new problems, including which POIs are visited first, when and where to charge drones, which drones to charge first, how much to charge, and when to stop the scheduling. To this end, we design a holistic solution, including context-aware matrix factorization for parking occupancy data inference, progressive determination of task quantity, deep reinforcement learning (DRL) based task selection, energy-aware DRL-based task scheduling, and adaptive charger scheduling. Extensive experiments with a real-world on-street parking dataset from Shenzhen, China demonstrate the obvious advantages of DroneSense.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796795"}, {"primary_key": "1711532", "vector": [], "sparse_vector": [], "title": "RF-Wise: Pushing the Limit of RFID-based Sensing.", "authors": ["<PERSON><PERSON>", "Zhenjiang Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "RFID shows great potentials to build useful sensing applications. However, current RFID sensing can obtain mainly a single-dimensional sensing measurement from each reader-to-tag query, such as phase, RSS, etc. This is sufficient to fulfill the designs that are bounded to the tag's own movement, e.g., the localization of tags. However, it imposes inevitable uncertainty to many sensing tasks relying on the features extracted from the RFID signals, which limits the fidelity of RFID sensing fundamentally and prevents its broader usage in more sophisticated sensing scenarios. This paper presents RF-Wise to push the limit of the RFID-based sensing, motivated by an insightful observation to customize RFID signals. RF-Wise can enrich the existing single-dimensional feature measure to a channel state information (CSI)-like measure with up to 150 dimensional samples across different frequencies concurrently. More importantly, RF-Wise is a software solution atop the standard EPC Gen2 protocol without using any extra hardware, requires only one tag for sensing and works within the ISM band. RF-Wise, so far as we know, is the first system of such a kind. Extensive experiments show that RF-Wise does not impact underlying RFID communications, while by using the features extracted by RF-Wise, applications' sensing performance can be improved remarkably. The source codes of RF-Wise are available at https://cui-zhao.github.io/RF-WISE/.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796909"}, {"primary_key": "1711533", "vector": [], "sparse_vector": [], "title": "E2E Fidelity Aware Routing and Purification for Throughput Maximization in Quantum Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper studies reliable teleportation of quantum bits (called qubits) in a quantum data network with multiple sources (S) and destinations (D) as well as repeaters. To teleport qubits for a SD pair reliably, not only an entanglement path for the SD pair, but also appropriate purification of the links along the path is required to ensure that the end-to-end (E2E) fidelity of the established entanglement connections is high enough.This is the first work on quantifying the E2E fidelity, and also using this E2E fidelity to determine critical links to achieve the most resource efficient purification. A novel approach called E2E Fidelity aware Routing and Purification (EFiRAP) is proposed to maximize network throughput, i.e., the number of entanglement connections among multiple SD pairs, with each connection having an E2E fidelity above a given required threshold. EFiRAP accomplishes this goal by first preparing multiple candidate entanglement paths and determining optimal purification schemes, and then selecting the final set of entanglement paths that can maximize network throughput under the given quantum resource constraints. Existing works only ensured the fidelity of individual links, rather than the E2E fidelity is above a given threshold. Extensive simulations show that the proposed EFiRAP can enhance network throughput by about 50% when compared with the state-of-the-art approach.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796814"}, {"primary_key": "1711534", "vector": [], "sparse_vector": [], "title": "Sound of Motion: Real-time Wrist Tracking with A Smart Watch-Phone Pair.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Proliferation of smart environments entails the need for real-time and ubiquitous human-machine interactions through, mostly likely, hand/arm motions. Though a few recent efforts attempt to track hand/arm motions in real-time with COTS devices, they either obtain a rather low accuracy or have to rely on a carefully designed infrastructure and some heavy signal processing. To this end, we propose SoM (Sound of Motion) as a lightweight system for wrist tracking. Requiring only a smart watch-phone pair, SoM entails very light computations that can operate in resource constrained smartwatches. SoM uses embedded IMU sensors to perform basic motion tracking in the smartwatch, and it depends on the fixed smartphone to act as an \"acoustic anchor\": regular beacons sent by the phone are received in an irregular manner due to the watch motion, and such variances provide useful hints to adjust the drifting of IMU tracking. Using extensive experiments on our SoM prototype, we demonstrate that the delicately engineered system achieves a satisfactory wrist tracking accuracy and strikes a good balance between complexity and performance.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796731"}, {"primary_key": "1711536", "vector": [], "sparse_vector": [], "title": "Poisoning Attacks on Deep Learning based Wireless Traffic Prediction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Big client data and deep learning bring a new level of accuracy to wireless traffic prediction in non-adversarial environments. However, in a malicious client environment, the training-stage vulnerability of deep learning (DL) based wireless traffic prediction remains under-explored. In this paper, we conduct the first systematic study on training-stage poisoning attacks against DL-based wireless traffic prediction in both centralized and distributed training scenarios. In contrast to previous poisoning attacks on computer vision, we consider a more practical threat model, specific to wireless traffic prediction, to design these poisoning attacks. In particular, we assume that potential malicious clients do not collude or have any additional knowledge about the other clients’ data. We propose a perturbation masking strategy and a tuning-and-scaling method to fit data and model poisoning attacks into the practical threat model. We also explore potential defenses against these poisoning attacks and propose two defense methods. Through extensive evaluations, we show the mean square error (MSE) can be increased by over 50% to 10 8 times with our proposed poisoning attacks. We also demonstrate the effectiveness of our data sanitization approach and anomaly detection method against our poisoning attacks in centralized and distributed scenarios.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796791"}, {"primary_key": "1711540", "vector": [], "sparse_vector": [], "title": "Muses: Enabling Lightweight Learning-Based Congestion Control for Mobile Devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Zhenyu Li", "<PERSON><PERSON>", "Hongtao Guan", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Various congestion control (CC) algorithms have been designed to target specific scenarios. To automate this process, researchers have begun to use machine learning to automatically control the congestion window. These, however, often rely on heavyweight learning models (e.g., neural networks). This can make them unsuitable for resource-constrained mobile devices. On the other hand, lightweight models (e.g., decision trees) are often incapable of reflecting the complexity of diverse mobile wireless environments. To address this, we present Muses, a learning-based approach for generating lightweight congestion control algorithms. <PERSON><PERSON> relies on imitation learning to train a universal (heavy) LSTM model, which is then used to extract (lightweight) decision tree models that are each targeted at an individual environment. <PERSON><PERSON> then dynamically selects the most appropriate decision tree on a per-flow basis. We show that <PERSON><PERSON> can generate high throughput policies across a diverse set of environments, and it is sufficiently light to operate on mobile devices.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796880"}, {"primary_key": "1711541", "vector": [], "sparse_vector": [], "title": "PACC: Proactive and Accurate Congestion Feedback for RDMA Congestion Control.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zixuan <PERSON>uan", "<PERSON><PERSON><PERSON>"], "summary": "The rapid upgrade of link speed and the prosperity of new applications in data center networks (DCNs) lead to a rigorous demand for ultra-low latency and high throughput. To mitigate the overhead of traditional software-based packet processing at end-hosts, RDMA (Remote Direct Memory Access) has been widely adopted in DCNs. Particularly, congestion control (CC) mechanisms designed for RDMA have attracted much attention to avoid performance deterioration when packets lose. However, through comprehensive analysis, we found that existing RDMA CC schemes have limitations of a sluggish response to congestion and unawareness of tiny microbursts due to the long end-to-end control loop. In this paper, we propose PACC, a switch-driven RDMA CC algorithm with easy deployability. PACC is driven by PI controller-based computation, threshold-based flow discrimination and weight-based allocation at the switch. It leverages real-time queue length to generate accurate congestion feedback proactively and piggybacks it to the corresponding source without modification to end-hosts. We theoretically analyze the stability and key parameter settings of PACC. Then, we conduct both micro-benchmark and large-scale simulations to evaluate the performance of PACC. The results show that PACC achieves fairness, fast reaction, high throughput, and 6~69% lower FCT (Flow Completion Time) than DCQCN, TIMELY and HPCC.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796803"}, {"primary_key": "1711542", "vector": [], "sparse_vector": [], "title": "Two Time-Scale Joint Service Caching and Task Offloading for UAV-assisted Mobile Edge Computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Haisheng Tan", "<PERSON><PERSON>"], "summary": "The emergence of unmanned aerial vehicles (UAVs) extends the mobile edge computing (MEC) services in broader coverage to offer new flexible and low-latency computing services for user equipment (UE) in the era of 5G and beyond. One of the fundamental requirements in UAV-assisted mobile wireless systems is the low latency, which can be jointly optimized with service caching and task offloading. However, this is challenged by the communication overhead involved with service caching and constrained by limited energy capacity. In this work, we present a comprehensive optimization framework with the objective of minimizing the service latency while incorporating the unique features of UAVs. Specifically, to reduce the caching overhead, we make caching placement decision every T slots (specified by service providers), and adjust UAV trajectory, user equipment or UE-UAV association, and task offloading decisions at each time slot under the constraints of UAV's energy and resource capacity. By leveraging Lyapunov optimization approach and dependent rounding technique, we design an alternating optimization-based algorithm, named TJSO, which iteratively optimizes caching and offloading decisions. Theoretical analysis proves that TJSO converges to the near-optimal solution in polynomial time. Extensive simulations further verify that our proposed solution can significantly reduce the service delay for UEs while maintaining low energy consumption when compared to the three state-of-the-art baselines.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796714"}, {"primary_key": "1711543", "vector": [], "sparse_vector": [], "title": "Target-oriented Semi-supervised Domain Adaptation for WiFi-based HAR.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Incorporating domain adaptation is a promising solution to mitigate the domain shift problem of WiFi-based human activity recognition (HAR). The state-of-the-art solutions, however, do not fully exploit all the data, only focusing either on unlabeled samples or labeled samples in the target WiFi environment. Moreover, they largely fail to carefully consider the discrepancy between the source and target WiFi environments, making the adaptation of models to the target environment with few samples become much less effective. To cope with those issues, we propose a Target-Oriented Semi-Supervised (TOSS) domain adaptation method for WiFi-based HAR that can effectively leverage both labeled and unlabeled target samples. We further design a dynamic pseudo label strategy and an uncertainty-based selection method to learn the knowledge from both source and target environments. We implement TOSS with a typical meta learning model and conduct extensive evaluations. The results show that TOSS greatly outperforms state-of-the-art methods under comprehensive 1 on 1 and multi-source one-shot domain adaptation experiments across multiple real-world scenarios.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796782"}, {"primary_key": "1711544", "vector": [], "sparse_vector": [], "title": "Shield: Safety Ensured High-efficient Scheduling for Magnetic MIMO Wireless Power Transfer System.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Haisheng Tan", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Recently, the developed techniques such as magnetic resonant coupling (MRC) and multiple-input multiple-output (MIMO) transmission have significantly improved the charging efficiency and distance for wireless power transfer (WPT) systems. However, the electromagnetic radiation (EMR) safety of wireless charging is critical in practice while mostly ignored. In this work, we take the EMR safety into account in MIMO MRC-WPT systems. We propose a safety ensured high-efficient scheduling algorithm for magnetic MIMO wireless power transfer system (called Shield). Technically, we firstly devise a simple but accurate Z-axis rotational symmetrical EMR model along with a magnetic-field-line-based meshing scheme. Further, we express the EMR safety requirement in the continuous physical space with a limited number of constraints via random sampling and rule-based filtering. Finally, we build up a system prototype for Shield and conduct extensive experiments. With the given power budget and resonant frequency, the results reveal that the EMR safety requirement only influences the charging performance of an MRC-WPT system within a certain range. Furthermore, Shield can dramatically improve the payload power transfer efficiency (PTE) by up to 66.60% compared with state-of-the-art baselines while guaranteeing the EMR safety.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796790"}, {"primary_key": "1711551", "vector": [], "sparse_vector": [], "title": "Deadline-aware Multipath Transmission for Streaming Blocks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Interactive applications have deadline requirements, e.g. video conferencing and online gaming. Compared with a single path, which may be less stable or bandwidth insufficient, using multiple network paths simultaneously (e.g., WiFi and cellular network) can leverage the ability of multiple paths to service for the deadline. However, existing multipath schedulers usually ignore the deadline and the influence from subsequent blocks to the current scheduling decision when multiple blocks exist at the sender. In this paper, we propose DAMS, a Deadline-Aware Multipath Scheduler aiming to deliver more blocks with heterogeneous attributes before their deadlines. DAMS carefully schedules the sending order of blocks and balances its allocation on multiple paths to reduce the waste of bandwidth resources with the consideration of the block's deadline. We implement DAMS with the inspiration of MPQUIC in user space. The extensive experimental results show that DAMS brings 41%-63% performance improvement on average compared with existing multipath solutions.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796942"}, {"primary_key": "1711553", "vector": [], "sparse_vector": [], "title": "Adaptive Bitrate with User-level QoE Preference for Video Streaming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recent years have witnessed tremendous growth of video streaming applications. To describe users' expectations of videos, QoE was proposed, which is critical for content providers. Current video delivery systems optimize QoE with ABR algorithms. However, ABR is usually designed for an abstract \"average user\" without considering that QoE varies with users. In this paper, to investigate the difference in user preferences, we conduct a user study with 90 subjects and find that the average user can not represent all users. This observation inspires us to propose Ruyi, a video streaming system that incorporates preference awareness into the QoE model and the ABR algorithm. <PERSON><PERSON><PERSON> profiles QoE preference of users and introduces preference-aware weights over different quality metrics into the QoE model. Based on this QoE model, <PERSON><PERSON><PERSON>'s ABR is designed to directly predict the influence on metrics after taking different actions. With these predicted metrics, <PERSON><PERSON><PERSON> chooses the bitrate that maximizes user-specific QoE once the preference is given. Consequently, Ruyi is scalable to different user preferences without re-training the learned models for each user. Simulation results show that Ruyi increases QoE for all users with up to 65.22% improvement. Testbed experimental results show that <PERSON><PERSON><PERSON> has the highest ratings from subjects.", "published": "2022-01-01", "category": "infocom", "pdf_url": "", "sub_summary": "", "source": "infocom", "doi": "10.1109/INFOCOM48880.2022.9796953"}]