[{"primary_key": "1222975", "vector": [], "sparse_vector": [], "title": "The Identity Problem in the special affine group of Z2.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We consider semigroup algorithmic problems in the Special Affine group ${\\text{SA}}(2,{\\mathbb{Z}}) = {{\\mathbb{Z}}^2} \\rtimes {\\text{SL}}(2,{\\mathbb{Z}})$, which is the group of affine transformations of the lattice ${{\\mathbb{Z}}^2}$ that preserve orientation. Our paper focuses on two decision problems introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> (2005): the Identity Problem (does a semigroup contain a neutral element?) and the Group Problem (is a semigroup a group?) for finitely generated sub-semigroups of ${\\text{SA}}(2,{\\mathbb{Z}})$. We show that both problems are decidable and NP-complete. Since ${\\text{SL}}(2,{\\mathbb{Z}}) \\leq {\\text{SA}}(2,{\\mathbb{Z}}) \\leq {\\text{SL}}(3,{\\mathbb{Z}})$, our result extends that of <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (SODA 2017) on the NP-completeness of both problems in ${\\text{SL}}(2,{\\mathbb{Z}})$, and contributes a first step towards the open problems in ${\\text{SL}}(3,{\\mathbb{Z}})$.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175768"}, {"primary_key": "1222976", "vector": [], "sparse_vector": [], "title": "A system of inference based on proof search: an extended abstract.", "authors": ["<PERSON>"], "summary": "<PERSON><PERSON><PERSON> designed his natural deduction proof system to \"come as close as possible to actual reasoning.\" Indeed, natural deduction proofs closely resemble the static structure of logical reasoning in mathematical arguments. However, different features of inference are compelling to capture when one wants to support the process of searching for proofs. PSF (Proof Search Framework) attempts to capture these features naturally and directly. The design and metatheory of PSF are presented, and its ability to specify a range of proof systems for classical, intuitionistic, and linear logic is illustrated.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175827"}, {"primary_key": "1222977", "vector": [], "sparse_vector": [], "title": "Orbit-finite linear programming.", "authors": ["Arka Ghosh", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "An infinite set is orbit-finite if, up to permutations of the underlying structure of atoms, it has only finitely many elements. We study a generalisation of linear programming where constraints are expressed by an orbit-finite system of linear inequalities. As our principal contribution we provide a decision procedure for checking if such a system has a real solution, and for computing the minimal/maximal value of a linear objective function over the solution set. We also show undecidability of these problems in case when only integer solutions are considered. Therefore orbit-finite linear programming is decidable, while orbit-finite integer linear programming is not.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175799"}, {"primary_key": "1222978", "vector": [], "sparse_vector": [], "title": "Stochastic Best-<PERSON><PERSON>ort Strategies for Borel Goals.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study reactive systems with Borel goals operating in a possibly non-Markovian stochastic environment. Moreover, the specific environment is not known, only its support is, i.e., at each step one knows which transitions are possible and which are impossible, but the probability distribution amongst the possible transitions is unknown. We consider system strategies that are maximal in the dominance order, i.e., no other strategy achieves the goal with at least the same probability in all environments, and with a higher probability in some environment. We call such strategies \"stochastic best-effort\". We prove the very general result that stochastic best-effort strategies exist for any Borel goal. We do this by providing local characterizations in terms of a three-valued abstraction of the probability of achieving the goal at a history. The correctness of the characterization is shown using a version of the Lebesgue Density Theorem from geometric measure theory. On the more practical side, we consider goals given in linear temporal logic. We establish the computational complexity of synthesizing a stochastic best-effort strategy, and show that it is not harder than synthesizing an optimal strategy in a domain with fixed known probabilities.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175747"}, {"primary_key": "1222979", "vector": [], "sparse_vector": [], "title": "Minimization of Dynamical Systems over Monoids.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON>", "Mirco Tribastone", "<PERSON>", "<PERSON>"], "summary": "Quantitative notions of bisimulation are well-known tools for the minimization of dynamical models such as Markov chains and ordinary differential equations (ODEs). In forward bisimulations, each state in the quotient model represents an equivalence class and the dynamical evolution gives the overall sum of its members in the original model. Here we introduce generalized forward bisimulation (GFB) for dynamical systems over commutative monoids and develop a partition refinement algorithm to compute the coarsest one. When the monoid is (ℝ,+), we recover probabilistic bisimulation for Markov chains and more recent forward bisimulations for nonlinear ODEs. Using (ℝ,•) we get nonlinear reductions for discrete-time dynamical systems and ODEs where each variable in the quotient model represents the product of original variables in the equivalence class. When the domain is a finite set such as the Booleans B, we can apply GFB to Boolean networks (BN), a widely used dynamical model in computational biology. Using a prototype implementation of our minimization algorithm for GFB, we find disjunction- and conjunction-preserving reductions on 60 BN from two well-known repositories, and demonstrate the obtained analysis speed-ups. We also provide the biological interpretation of the reduction obtained for two selected BN, and we show how GFB enables the analysis of a large one that could not be analyzed otherwise. Using a randomized version of our algorithm we find product-preserving (therefore non-linear) reductions on 21 dynamical weighted networks from the literature that could not be handled by the exact algorithm.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175697"}, {"primary_key": "1222980", "vector": [], "sparse_vector": [], "title": "A Higher-Order Indistinguishability Logic for Cryptographic Reasoning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The field of cryptographic protocol verification in the computational model aims at obtaining formal security proofs of protocols. To facilitate writing such proofs, which are complex and hard to automate, <PERSON><PERSON> and <PERSON><PERSON> have proposed the Computationally Complete Symbolic Attacker (CCSA) approach, which is based on a first-order logic with a probabilistic computational semantics. Later, a meta-logic was built on top of the CCSA logic, to extend it with support for unbounded protocols and effective mechanisation. This meta-logic was then implemented in the SQUIRREL prover.In this paper, we propose a careful re-design of the SQUIRREL logic, providing clean and robust foundations for its future development. We show in this way that the original meta-logic was both needlessly complex and too restrictive. Our new, higher-order logic avoids the indirect definition of the meta-logic on top of the CCSA logic, decouples the logic from the notion of protocol, and supports advanced generic reasoning and non-computable functions. We also equip it with generalised cryptographic rules to reason about corruption. This theoretical work justifies our extension of SQUIRREL with higher-order reasoning, which we illustrate on case studies.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175781"}, {"primary_key": "1222981", "vector": [], "sparse_vector": [], "title": "Multiplicity Problems on Algebraic Series and Context-Free Grammars.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we obtain complexity bounds for computational problems on algebraic power series over several commuting variables. The power series are specified by systems of polynomial equations: a formalism closely related to weighted context-free grammars. We focus on three problems—decide whether a given algebraic series is identically zero, determine whether all but finitely many coefficients are zero, and compute the coefficient of a specific monomial. We relate these questions to well-known computational problems on arithmetic circuits and thereby show that all three problems lie in the counting hierarchy. Our main result improves the best known complexity bound on deciding zeroness of an algebraic series. This problem is known to lie in PSPACE by reduction to the decision problem for the existential fragment of the theory of real closed fields. Here we show that the problem lies in the counting hierarchy by reduction to the problem of computing the degree of a polynomial given by an arithmetic circuit. As a corollary we obtain new complexity bounds on multiplicity equivalence of context-free grammars restricted to a bounded language, language inclusion of a non-deterministic finite automaton in an unambiguous context-free grammar, and language inclusion of a non-deterministic context-free grammar in an unambiguous finite automaton.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175707"}, {"primary_key": "1222982", "vector": [], "sparse_vector": [], "title": "Symmetries of Graphs and Structures that Fail to Interpret a Finite Thing.", "authors": ["<PERSON><PERSON>", "Bertalan Bodor", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We investigate structural implications arising from the condition that a given directed graph does not interpret, in the sense of primitive positive interpretation with parameters or orbits, every finite structure. Our results generalize several theorems from the literature and yield further algebraic invariance properties that must be satisfied in every such graph. Algebraic properties of this kind are tightly connected to the tractability of constraint satisfaction problems, and we obtain new such properties even for infinite countably categorical graphs. We balance these positive results by showing the existence of a countably categorical hypergraph that fails to interpret some finite structure, while still lacking some of the most essential algebraic invariance properties known to hold for finite structures.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175732"}, {"primary_key": "1222983", "vector": [], "sparse_vector": [], "title": "Computing the linear hull: Deciding Deterministic? and Unambiguous? for weighted automata over fields.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The (left) linear hull of a weighted automaton over a field is a topological invariant. If the automaton is minimal, the linear hull can be used to determine whether or not the automaton is equivalent to a deterministic one. Furthermore, the linear hull can also be used to determine whether the minimal automaton is equivalent to an unambiguous one. We show how to compute the linear hull, and thus prove that it is decidable whether or not a given automaton over a number field is equivalent to a deterministic one. In this case we are also able to compute an equivalent deterministic automaton. We also show the analogous decidability and computability result for the unambiguous case. Our results resolve a problem posed in a 2006 survey by <PERSON><PERSON> and <PERSON>.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175691"}, {"primary_key": "1222984", "vector": [], "sparse_vector": [], "title": "Embedded Finite Models beyond Restricted Quantifier Collapse.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We revisit evaluation of logical formulas that allow both uninterpreted relations, constrained to be finite, as well as interpreted vocabulary over an infinite domain: denoted in the past as embedded finite model theory. We extend the analysis of \"collapse results\": the ability to eliminate first-order quantifiers over the infinite domain in favor of quantification over the finite structure. We investigate several weakenings of collapse, one allowing higher-order quantification over the finite structure, another allowing expansion of the theory. We also provide results comparing collapse for unary signatures with general signatures, and new analyses of collapse for natural decidable theories.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175804"}, {"primary_key": "1222985", "vector": [], "sparse_vector": [], "title": "Quantifying Over Trees in Monadic Second-Order Logic.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Monadic Second-Order Logic (MSO) extends First-Order Logic (FO) with variables ranging over sets and quantifications over those variables. We introduce and study Monadic Tree Logic (MTL), a fragment of MSO interpreted on infinite-tree models, where the sets over which the variables range are arbitrary subtrees of the original model. We analyse the expressiveness of MTL compared with variants of MSO and MPL, namely MSO with quantifications over paths. We also discuss the connections with temporal logics, by providing non-trivial fragments of the Graded µ-CALCULUS that can be embedded into MTL and by showing that MTL is enough to encode temporal logics for reasoning about strategies with FO-definable goals.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175832"}, {"primary_key": "1222986", "vector": [], "sparse_vector": [], "title": "Simulating Logspace-Recursion with Logarithmic Quantifier Depth.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The fixed-point logic LREC = was developed by <PERSON><PERSON><PERSON> et al. (CSL 2011) in the quest for a logic to capture all problems decidable in logarithmic space. It extends FO+C, first-order logic with counting, by an operator that formalises a limited form of recursion. We show that for every LREC = -definable property on relational structures, there is a constant k such that the k-variable fragment of first-order logic with counting quantifiers expresses the property via formulae of logarithmic quantifier depth. This yields that any pair of graphs separable by the property can be distinguished with the k-dimensional <PERSON><PERSON><PERSON><PERSON><PERSON> algorithm in a logarithmic number of iterations. In particular, it implies that a constant dimension of the algorithm identifies every interval graph and every chordal claw-free graph in logarithmically many iterations, since every such graph admits LREC = -definable canonisation.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175818"}, {"primary_key": "1222987", "vector": [], "sparse_vector": [], "title": "Revisiting Membership Problems in Subclasses of Rational Relations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We revisit the membership problem for subclasses of rational relations over finite and infinite words: Given a relation R in a class C 2 , does R belong to a smaller class C 1 ? The subclasses of rational relations that we consider are formed by the deterministic rational relations, synchronous (also called automatic or regular) relations, and recognizable relations. For almost all versions of the membership problem, determining the precise complexity or even decidability has remained an open problem for almost two decades. In this paper, we provide improved complexity and new decidability results. (i) Testing whether a synchronous relation over infinite words is recognizable is NL-complete (PSPACE-complete) if the relation is given by a deterministic (nondeterministic) ω-automaton. This fully settles the complexity of this recognizability problem, matching the complexity of the same problem over finite words. (ii) Testing whether a deterministic rational binary relation is recognizable is decidable in polynomial time, which improves a previously known double exponential time upper bound. For relations of higher arity, we present a randomized exponential time algorithm. (iii) We provide the first algorithm to decide whether a deterministic rational relation is synchronous. For binary relations the algorithm even runs in polynomial time.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175722"}, {"primary_key": "1222988", "vector": [], "sparse_vector": [], "title": "Verifying linear temporal specifications of constant-rate multi-mode systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "Constant-rate multi-mode systems (MMS) are hybrid systems with finitely many modes and real-valued variables that evolve over continuous time according to mode-specific constant rates. We introduce a variant of linear temporal logic (LTL) for MMS, and we investigate the complexity of the model-checking problem for syntactic fragments of LTL. We obtain a complexity landscape where each fragment is either P-complete, NP-complete or undecidable. These results generalize and unify several results on MMS and continuous counter systems.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175721"}, {"primary_key": "1222989", "vector": [], "sparse_vector": [], "title": "Folding interpretations.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study the polyregular string-to-string functions, which are certain functions of polynomial output size that can be described using automata and logic. We describe a system of combinators that generates exactly these functions. Unlike previous systems, the present system includes an iteration mechanism, namely fold. Although unrestricted fold can define all primitive recursive functions, we identify a type system (inspired by linear logic) that restricts fold so that it defines exactly the polyregular functions. We also present related systems, for quantifier-free functions as well as for linear regular functions on both strings and trees.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175796"}, {"primary_key": "1222990", "vector": [], "sparse_vector": [], "title": "On the Growth Rates of Polyregular Functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider polyregular functions, which are certain string-to-string functions that have polynomial output size. We prove that a polyregular function has output size ${\\mathcal{O}}({n^k})$ if and only if it can be defined by an MSO interpretation of dimension k, i.e. a string-to-string transformation where every output position is interpreted, using monadic second-order logic MSO, in some k-tuple of input positions. We also show that this characterization does not extend to pebble transducers, another model for describing polyregular functions: we show that for every {k ∈ 1, 2, …} there is a polyregular function of quadratic output size which needs at least k pebbles to be computed.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175808"}, {"primary_key": "1222991", "vector": [], "sparse_vector": [], "title": "Operational Algorithmic Game Semantics.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider a simply-typed call-by-push-value calculus with state, and provide a fully abstract trace model via a labelled transition system (LTS) in the spirit of operational game semantics. By examining the shape of configurations and performing a series of natural optimisation steps based on name recycling, we identify a fragment for which the LTS can be recast as a deterministic visibly pushdown automaton. This implies decidability of contextual equivalence for the fragment identified and solvability in exponential time for terms in canonical form. We also identify a fragment for which these automata are finite-state machines.Further, we use the trace model to prove that translations of prototypical call-by-name (IA) and call-by-value (RML) languages into our call-by-push-value language are fully abstract. This allows our decidability results to be seen as subsuming several results from the literature for IA and RML. We regard our operational approach as a simpler and more intuitive way of deriving such results. The techniques we rely on draw upon simple intuitions from operational semantics and the resultant automata retain operational style, capturing the dynamics of the underlying language.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175791"}, {"primary_key": "1222992", "vector": [], "sparse_vector": [], "title": "Complete Graphical Language for Hermiticity-Preserving Superoperators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Universal and complete graphical languages have been successfully designed for pure state quantum mechanics, corresponding to linear maps between Hilbert spaces, and mixed states quantum mechanics, corresponding to completely positive superoperators. In this paper, we go one step further and present a universal and complete graphical language for Hermiticity-preserving superoperators. Such a language opens the possibility of diagrammatic compositional investigations of antilinear transformations featured in various physical situations, such as the Choi-<PERSON><PERSON> isomorphism, spin-flip, or entanglement witnesses. Our construction relies on an extension of the ZW-calculus exhibiting a normal form for Hermitian matrices.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175712"}, {"primary_key": "1222993", "vector": [], "sparse_vector": [], "title": "Central Submonads and Notions of Computation: Soundness, Completeness and Internal Languages.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Monads in category theory are algebraic structures that can be used to model computational effects in programming languages. We show how the notion of \"centre\", and more generally \"centrality\", i.e., the property for an effect to commute with all other effects, may be formulated for strong monads acting on symmetric monoidal categories. We identify three equivalent conditions which characterise the existence of the centre of a strong monad (some of which relate it to the premonoidal centre of Power and Robinson) and we show that every strong monad on many well-known naturally occurring categories does admit a centre, thereby showing that this new notion is ubiquitous. More generally, we study central submonads, which are necessarily commutative, just like the centre of a strong monad. We provide a computational interpretation by formulating equational theories of lambda calculi equipped with central submonads, we describe categorical models for these theories and prove soundness, completeness and internal language results for our semantics.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175687"}, {"primary_key": "1222994", "vector": [], "sparse_vector": [], "title": "Cut-Restriction: From Cuts to Analytic Cuts.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cut-elimination is the bedrock of proof theory with a multitude of applications from computational interpretations to proof analysis. It is also the starting point for important meta-theoretical investigations into decidability, complexity, disjunction property, interpolation, and more. Unfortunately cut-elimination does not hold for the sequent calculi of most non-classical logics. It is well-known that the key to applications is the subformula property (a typical consequence of cut-elimination) rather than cut-elimination itself. With this in mind, we introduce cut-restriction, a procedure to restrict arbitrary cuts to analytic cuts (when elimination is not possible). The algorithm applies to all sequent calculi satisfying language-independent and simple-to-check conditions, and it is obtained by adapting age-old cut-elimination. Our work encompasses existing results in a uniform way, subsumes <PERSON><PERSON><PERSON>'s cut-elimination, and establishes new analytic cut properties.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.********"}, {"primary_key": "1222995", "vector": [], "sparse_vector": [], "title": "The Cartesian Closed Bicategory of Thin Spans of Groupoids.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Recently, there has been growing interest in bicategorical models of programming languages, which are \"proof-relevant\" in the sense that they keep distinct account of execution traces leading to the same observable outcomes, while assigning a formal meaning to reduction paths as isomorphisms.In this paper we introduce a new model, a bicategory called thin spans of groupoids. Conceptually it is close to <PERSON><PERSON> et al.'s generalized species of structures and to <PERSON><PERSON><PERSON>' homotopy template games, but fundamentally differs as to how replication of resources and the resulting symmetries are treated. Where those models are saturated – the interpretation is inflated by the fact that semantic individuals may carry arbitrary symmetries – our model is thin, drawing inspiration from thin concurrent games: the interpretation of terms carries no symmetries, but semantic individuals satisfy a subtle invariant defined via biorthogonality, which guarantees their invariance under symmetry.We first build the bicategory Thin of thin spans of groupoids. Its objects are certain groupoids with additional structure, its morphisms are spans composed via plain pullback with identities the identity spans, and its 2-cells are span morphisms making the induced triangles commute only up to natural isomorphism. We then equip Thin with a pseudocomonad !, and finally show that the Kleisli bicategory Thin ! is cartesian closed.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175754"}, {"primary_key": "1222996", "vector": [], "sparse_vector": [], "title": "From Thin Concurrent Games to Generalized Species of Structures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Two families of denotational models have emerged from the semantic analysis of linear logic: dynamic models, typically presented as game semantics, and static models, typically based on a category of relations. In this paper we introduce a formal bridge between two-dimensional dynamic and static models: we connect the bicategory of thin concurrent games and strategies, based on event structures, to the bicategory of generalized species of structures, based on distributors.In the first part of the paper, we construct an oplax functor from (the linear bicategory of) thin concurrent games to distributors. This explains how to view a strategy as a distributor, and highlights two fundamental differences: the composition mechanism, and the representation of resource symmetries.In the second part of the paper, we adapt established methods from game semantics (visible strategies, payoff structure) to enforce a tighter connection between the two models. We obtain a cartesian closed pseudofunctor, which we exploit to shed new light on recent results in the bicategorical theory of the λ-calculus.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175681"}, {"primary_key": "1222997", "vector": [], "sparse_vector": [], "title": "A Complete Equational Theory for Quantum Circuits.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We introduce the first complete equational theory for quantum circuits.More precisely, we introduce a set of circuit equations that we prove to be sound and complete: two circuits represent the same unitary map if and only if they can be transformed one into the other using the equations.The proof is based on the properties of multi-controlled gates -that are defined using elementary gates -together with an encoding of quantum circuits into linear optical circuits, which have been proved to have a complete axiomatisation.2 Raw terms are for instance similarly used [31] as an intermediate step in the defintion of prop.3 denotes the identity, the swap and the empty circuit. 4We use the standard Dirac notations.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175801"}, {"primary_key": "1222998", "vector": [], "sparse_vector": [], "title": "ℤ-polyregular functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper studies a robust class of functions from finite words to integers that we call ℤ-polyregular functions. We show that it admits natural characterizations in terms of logics, ℤ-rational expressions, ℤ-rational series and transducers.We then study two subclass membership problems. First, we show that the asymptotic growth rate of a function is computable, and corresponds to the minimal number of variables required to represent it using logical formulas. Second, we show that first-order definability of ℤ-polyregular functions is decidable. To show the latter, we introduce an original notion of residual transducer, and provide a semantic characterization based on aperiodicity.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175685"}, {"primary_key": "1222999", "vector": [], "sparse_vector": [], "title": "Computational expressivity of (circular) proofs with fixed points.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the computational expressivity of proof systems with fixed point operators, within the 'proofs-as-programs' paradigm. We start with a calculus μLJ (due to <PERSON><PERSON><PERSON><PERSON>) that extends intuitionistic logic by least and greatest positive fixed points. Based in the sequent calculus, μLJ admits a standard extension to a 'circular' calculus CμLJ.Our main result is that, perhaps surprisingly, both μLJ and CμLJ represent the same first-order functions: those provably total in $\\Pi _2^1 - {\\text{C}}{{\\text{A}}_0}$, a subsystem of second-order arithmetic beyond the 'big five' of reverse mathematics and one of the strongest theories for which we have an ordinal analysis (due to <PERSON><PERSON><PERSON><PERSON>). This solves various questions in the literature on the computational strength of (circular) proof systems with fixed points.For the lower bound we give a realisability interpretation from an extension of Peano Arithmetic by fixed points that has been shown to be arithmetically equivalent to $\\Pi _2^1 - {\\text{C}}{{\\text{A}}_0}$ (due to <PERSON><PERSON><PERSON><PERSON>). For the upper bound we construct a novel computability model in order to give a totality argument for circular proofs with fixed points. In fact we formalise this argument itself within $\\Pi _2^1 - {\\text{C}}{{\\text{A}}_0}$ in order to obtain the tight bounds we are after. Along the way we develop some novel reverse mathematics for the K<PERSON><PERSON>-<PERSON> fixed point theorem.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175772"}, {"primary_key": "1223000", "vector": [], "sparse_vector": [], "title": "Deterministic stream-sampling for probabilistic programming: semantics and verification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Probabilistic programming languages rely fundamentally on some notion of sampling, and this is doubly true for probabilistic programming languages which perform Bayesian inference using Monte <PERSON> techniques. Verifying samplers—proving that they generate samples from the correct distribution—is crucial to the use of probabilistic programming languages for statistical modelling and inference. However, the typical denotational semantics of probabilistic programs is incompatible with deterministic notions of sampling. This is problematic, considering that most statistical inference is performed using pseudorandom number generators.We present a higher-order probabilistic programming language centred on the notion of samplers and sampler operations. We give this language an operational and denotational semantics in terms of continuous maps between topological spaces. Our language also supports discontinuous operations, such as comparisons between reals, by using the type system to track discontinuities. This feature might be of independent interest, for example in the context of differentiable programming.Using this language, we develop tools for the formal verification of sampler correctness. We present an equational calculus to reason about equivalence of samplers, and a sound calculus to prove semantic correctness of samplers, i.e. that a sampler correctly targets a given measure by construction.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175773"}, {"primary_key": "1223001", "vector": [], "sparse_vector": [], "title": "Logic for Explainable AI.", "authors": ["<PERSON><PERSON>"], "summary": "A central quest in explainable AI relates to understanding the decisions made by (learned) classifiers. There are three dimensions of this understanding that have been receiving significant attention in recent years. The first dimension relates to characterizing conditions on instances that are necessary and sufficient for decisions, therefore providing abstractions of instances that can be viewed as the \"reasons behind decisions.\" The next dimension relates to characterizing minimal conditions that are sufficient for a decision, therefore identifying aspects of the instance that are irrelevant to the decision. The last dimension relates to characterizing minimal conditions that are necessary for a decision, therefore identifying minimal perturbations to the instance that yield alternate decisions. We will discuss in this tutorial a comprehensive, semantical and computational theory of explainability along these dimensions which is based on some recent developments in symbolic logic. The tutorial will also discuss how this theory is particularly applicable to non-symbolic classifiers such as those based on Bayesian networks, decision trees, random forests and some types of neural networks.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175757"}, {"primary_key": "1223002", "vector": [], "sparse_vector": [], "title": "The Big-O Problem for Max-Plus Automata is Decidable (PSPACE-Complete).", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We show that the big-O problem for max-plus automata, i.e. weighted automata over the semiring (ℕ ∪ {–∞}, max, +), is decidable and PSPACE-complete. The big-O (or affine domination) problem asks whether, given two max-plus automata computing functions f and g, there exists a constant c such that f ≤ cg + c. This is a relaxation of the containment problem asking whether f ≤ g, which is undecidable. Our decidability result uses <PERSON>'s forest factorisation theorem, and relies on detecting specific elements, that we call witnesses, in a finite semigroup closed under two special operations: stabilisation and flattening.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175798"}, {"primary_key": "1223003", "vector": [], "sparse_vector": [], "title": "Pseudorandom Finite Models.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study pseudorandomness and pseudorandom generators from the perspective of logical definability. Building on results from ordinary derandomization and finite model theory, we show that it is possible to deterministically construct, in polynomial time, graphs and relational structures that are statistically indistinguishable from random structures by any sentence of first order or least fixed point logics. This raises the question of whether such constructions can be implemented via logical transductions from simpler structures with less entropy. In other words, can logical formulas be pseudorandom generators? We provide a complete classification of when this is possible for first order logic, fixed point logic, and fixed point logic with parity, and provide partial results and conjectures for first order logic with parity.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175694"}, {"primary_key": "1223004", "vector": [], "sparse_vector": [], "title": "Commutativity in Automated Verification.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Trace theory (formulated by <PERSON><PERSON><PERSON><PERSON><PERSON> in 1987) is a framework for formalizing equivalence relations for concurrent program runs based on a commutativity relation over the set of atomic steps taken by individual program threads. It has been implicitly or explicitly used in a broad set of program analysis techniques, such as predictive testing for atomicity or data race violations, static and dynamic partial order reduction in model checking (particularly stateless model checking), and reasoning about distributed programs. In this paper, we introduce a different line of work that uses traces for the purpose of proof simplification for a broad set of automated verification goals. The long term thesis of this line of work has been that by taking advantage of commutativity, one can discover a substantially simpler verification task to replace the original one, and succeed at it despite the inevitability of the failure of the original one. The idea is to verify a different program in place of the original one and use commutativity as a way of soundly carrying the verification results over to the original one. We discuss hypersafety verification of sequential and concurrent programs, and safety and liveness verification of concurrent and distributed programs. We show how commutativity can be incorporated into a new verification algorithm which enumerates infinitely many possibilities for alternative programs to be verified instead of the original one. We conclude with an overview of some open research questions in this area.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175734"}, {"primary_key": "1223005", "vector": [], "sparse_vector": [], "title": "Fixed Point Logics on Hemimetric Spaces.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The µ-calculus can be interpreted over metric spaces and is known to enjoy, among other celebrated properties, variants of the McKinsey-Ta<PERSON> completeness theorem and of <PERSON><PERSON> and <PERSON>'s modal characterization theorem. In its topological form, this theorem states that every topological fixed point may be defined in terms of the tangled derivative, a polyadic generalization of <PERSON><PERSON>'s perfect core. However, these results fail when spaces not satisfying basic separation axioms are considered, in which case the base modal logic is not the well-known K4, but the weaker wK4.In this paper we show how these shortcomings may be overcome. First, we consider semantics over the wider class of hemimetric spaces, and obtain metric completeness results for wK4 and related logics. In this setting, the <PERSON>war-<PERSON> theorem still fails, but we argue that this is due to the tangled derivative not being suitably defined for general application in arbitrary topological spaces. We thus introduce the hybrid tangle, which coincides with the tangled derivative over metric spaces but is better behaved in general. We show that only the hybrid tangle suffices to define simulability of finite structures, a key 'test case' for an expressively complete fragment of the µ-calculus.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175784"}, {"primary_key": "1223006", "vector": [], "sparse_vector": [], "title": "Structure-Aware Lower Bounds and Broadening the Horizon of Tractability for QBF.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The QSAT problem, which asks to evaluate a quantified Boolean formula (QBF), is of fundamental interest in approximation, counting, decision, and probabilistic complexity and is also considered the prototypical PSPACE-complete problem. As such, it has previously been studied under various structural restrictions (parameters), most notably parameterizations of the primal graph representation of instances. Indeed, it is known that QSAT remains PSPACE-complete even when restricted to instances with constant treewidth of the primal graph, but the problem admits a double-exponential fixed-parameter algorithm parameterized by the vertex cover number (primal graph).However, prior works have left a gap in our understanding of the complexity of QSAT when viewed from the perspective of other natural representations of instances, most notably via incidence graphs. In this paper, we develop structure-aware reductions which allow us to obtain essentially tight lower bounds for highly restricted instances of QSAT, including instances whose incidence graphs have bounded treedepth or feedback vertex number. We complement these lower bounds with novel algorithms for QSAT which establish a nearly-complete picture of the problem's complexity under standard graph-theoretic parameterizations. We also show implications for other natural graph representations, and obtain novel upper as well as lower bounds for QSAT under more fine-grained parameterizations of the primal graph.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175675"}, {"primary_key": "1223007", "vector": [], "sparse_vector": [], "title": "PDL on Steroids: on Expressive Extensions of PDL with Intersection and Converse.", "authors": ["<PERSON>", "Santiago Figueira", "<PERSON>"], "summary": "We introduce CPDL + , a family of expressive logics rooted in Propositional Dynamic Logic (PDL). In terms of expressive power, CPDL + strictly contains PDL extended with intersection and converse (a.k.a. ICPDL) as well as Conjunctive Queries (CQ), Conjunctive Regular Path Queries (CRPQ), or some known extensions thereof (Regular Queries and CQPDL). We investigate the expressive power, indistinguishability via bisimulations, satisfiability, and model checking for CPDL + .We argue that natural subclasses of CPDL + can be defined in terms of the tree-width of the underlying graphs of the formulas. We show that the class of CPDL + formulas of tree-width 2 is equivalent to ICPDL, and that it also coincides with CPDL + formulas of tree-width 1. However, beyond tree-width 2, incrementing the tree-width strictly increases the expressive power. We characterize the expressive power for every class of fixed tree-width formulas in terms of a bisimulation game with pebbles. Based on this characterization, we show that CPDL + has a tree-like model property. We prove that the satisfiability problem is decidable in 2EXPTIME on fixed tree-width formulas, coinciding with the complexity of ICPDL. We also exhibit classes for which satisfiability is reduced to EXPTIME. Finally, we establish that the model checking problem for fixed tree-width formulas is in PTIME, contrary to the full class CPDL + .", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175813"}, {"primary_key": "1223008", "vector": [], "sparse_vector": [], "title": "Fixpoint operators for 2-categorical structures.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Fixpoint operators are tools to reason on recursive programs and data types obtained by induction (e.g. lists, trees) or coinduction (e.g. streams). They were given a categorical treatment with the notion of categories with fixpoints. A theorem by <PERSON><PERSON><PERSON> and <PERSON> characterizes existence and uniqueness of fixpoint operators for categories satisfying some conditions on bifree algebras and recovers the standard examples of the category Cppo (ω-complete pointed partial orders and continuous functions) in domain theory and the relational model in linear logic.We present a categorification of this result and develop the theory of 2-categorical fixpoint operators where the 2-dimensional framework allows to model the execution steps for languages with (co)inductive principles. We recover the standard categorical constructions of initial algebras and final coalgebras for endofunctors as well as fixpoints of generalized species and polynomial functors.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.********"}, {"primary_key": "1223009", "vector": [], "sparse_vector": [], "title": "Allegories of Symbolic Manipulations.", "authors": ["<PERSON>"], "summary": "Moving from the mathematical theory of (abstract) syntax, we develop a general relational theory of symbolic manipulation parametric with respect to, and accounting for, general notions of syntax. We model syntax relying on categorical notions, such as free algebras and monads, and show that a general theory of symbolic manipulation in the style of rewriting systems can be obtained by extending such notions to an allegorical setting. This way, we obtain an augmented calculus of relations accounting for syntax-based rewriting. We witness the effectiveness of the relational approach by generalising and unifying milestones results in rewriting, such as the parallel moves and the <PERSON><PERSON><PERSON> techniques.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.********"}, {"primary_key": "1223010", "vector": [], "sparse_vector": [], "title": "Reachability in Injective Piecewise Affine Maps.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "One of the most basic, longstanding open problems in the theory of dynamical systems is whether reachability is decidable for one-dimensional piecewise affine maps with two intervals. In this paper we prove that for injective maps, it is decidable.We also study various related problems, in each case either establishing decidability, or showing that they are closely connected to Diophantine properties of certain transcendental numbers, analogous to the positivity problem for linear recurrence sequences. Lastly, we consider topological properties of orbits of one-dimensional piecewise affine maps, not necessarily with two intervals, and negatively answer a question of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, about the set of orbits in expanding maps.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175723"}, {"primary_key": "1223011", "vector": [], "sparse_vector": [], "title": "Intuitionistic S4 is decidable.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Lutz Straßburger"], "summary": "In this paper we demonstrate decidability for the intuitionistic modal logic S4 first formulated by <PERSON>. This solves a problem that has been open for almost thirty years since it had been posed in <PERSON>'s PhD thesis in 1994. We obtain this result by performing proof search in a labelled deductive system that, instead of using only one binary relation on the labels, employs two: one corresponding to the accessibility relation of modal logic and the other corresponding to the order relation of intuitionistic Kripke frames. Our search algorithm outputs either a proof or a finite counter-model, thus, additionally establishing the finite model property for intuitionistic S4, which has been another long-standing open problem in the area.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175684"}, {"primary_key": "1223012", "vector": [], "sparse_vector": [], "title": "Semi-Simplicial Set Models for Distributed Knowledge.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In recent years, a new class of models for multi-agent epistemic logic has emerged, based on simplicial complexes. Since then, many variants of these simplicial models have been investigated, giving rise to different logics and axiomatizations. In this paper, we present a further generalization, which encompasses all previously studied variants of simplicial models. Geometrically, this is achieved by generalizing beyond simplicial complexes, and considering instead semi-simplicial sets. By doing so, we define a new semantics for epistemic logic with distributed knowledge, where a group of agents may distinguish two worlds, even though each individual agent in the group is unable to distinguish them. As it turns out, these models are the geometric counterpart of a generalization of Kripke models, called \"pseudo-models\". We show how to recover the previously defined variants of simplicial models as sub-classes of our models; and give a sound and complete axiomatization for each of them.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175737"}, {"primary_key": "1223013", "vector": [], "sparse_vector": [], "title": "The Descriptive Complexity of Graph Neural Networks.", "authors": ["<PERSON>"], "summary": "We analyse the power of graph neural networks (GNNs) in terms of Boolean circuit complexity and descriptive complexity.We prove that the graph queries that can be computed by a polynomial-size bounded-depth family of GNNs are exactly those definable in the guarded fragment GFO+C of first-order logic with counting and with built-in relations. This puts GNNs in the circuit complexity class TC 0 . Remarkably, the GNN families may use arbitrary real weights and a wide class of activation functions that includes the standard ReLU, logistic \"sigmoid\", and hyperbolic tangent functions. If the GNNs are allowed to use random initialisation and global readout (both standard features of GNNs widely used in practice), they can compute exactly the same queries as bounded depth Boolean circuits with threshold gates, that is, exactly the queries in TC 0 .Moreover, we show that queries computable by a single GNN with piecewise linear activations and rational weights are definable in GFO+C without built-in relations. Therefore, they are contained in uniform TC 0 .", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175735"}, {"primary_key": "1223014", "vector": [], "sparse_vector": [], "title": "The Iteration Number of the Weisfeiler-Leman Algorithm.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We prove new upper and lower bounds on the number of iterations the k-dimensional Weisfe<PERSON>-<PERSON> algorithm (k-WL) requires until stabilization. For k ≥ 3, we show that k-WL stabilizes after at most O(kn k−1 log n) iterations (where n denotes the number of vertices of the input structures), obtaining the first improvement over the trivial upper bound of n k − 1 and extending a previous upper bound of O(n log n) for k = 2 [<PERSON> et al., LICS 2019].We complement our upper bounds by constructing k-ary relational structures on which k-WL requires at least n Ω(k) iterations to stabilize. This improves over a previous lower bound of n Ω(k/logk) [<PERSON><PERSON>, Nordström, LICS 2016].We also investigate tradeoffs between the dimension and the iteration number of WL, and show that d-WL, where $d = \\left\\lceil {\\frac{{3(k + 1)}}{2}} \\right\\rceil $, can simulate the k-WL algorithm using only O(k 2 • n ⌊k/2⌋+1 log n) many iterations, but still requires at least n Ω(k) iterations for any d (that is sufficiently smaller than n).The number of iterations required by k-<PERSON><PERSON> to distinguish two structures corresponds to the quantifier rank of a sentence distinguishing them in the (k + 1)-variable fragment ${{\\mathcal{C}}_k}_{ + 1}$ of first-order logic with counting quantifiers. Hence, our results also imply new upper and lower bounds on the quantifier rank required in the logic ${{\\mathcal{C}}_k}_{ + 1}$, as well as tradeoffs between variable number and quantifier rank.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175741"}, {"primary_key": "1223015", "vector": [], "sparse_vector": [], "title": "Automatic Amortized Resource Analysis with Regular Recursive Types.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The goal of automatic resource bound analysis is to statically infer symbolic bounds on the resource consumption of the evaluation of a program. A longstanding challenge for automatic resource analysis is the inference of bounds that are functions of complex custom data structures. This article builds on type-based automatic amortized resource analysis (AARA) to address this challenge. AARA is based on the potential method of amortized analysis and reduces bound inference to standard type inference with additional linear constraint solving, even when deriving non-linear bounds. Such bounds come from resource functions, which are linear combinations of basic functions of data structure sizes that fulfill certain closure properties.Previous work on AARA defined resource functions for many data structures such as lists of lists, but left open whether such functions exist for arbitrary data structures. This work answers this question positively by uniformly constructing resource polynomials for algebraic data structures defined by regular recursive types. These functions are a generalization of all previously proposed polynomial resource functions and can be seen as a general notion of polynomials for values of a given recursive type. A resource type system for FPC, a core language with recursive types, demonstrates how resource polynomials can be integrated with AARA while preserving all benefits of past techniques. The article also proposes the use of new techniques useful for stating the rules of this type system succinctly and proving it sound against a small-step cost semantics. First, multivariate potential annotations are stated in terms of free semimodules, substantially abstracting details of the presentation of annotations and the proofs of their properties. Second, a logical relation giving semantic meaning to resource types enables a proof of soundness by a single induction on typing derivations.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175720"}, {"primary_key": "1223016", "vector": [], "sparse_vector": [], "title": "Higher-Dimensional Subdiagram Matching.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Higher-dimensional rewriting is founded on a duality of rewrite systems and cell complexes, connecting computational mathematics to higher categories and homotopy theory: the two sides of a rewrite rule are two halves of the boundary of an (n + 1)-cell, which are diagrams of n-cells. We study higher-dimensional diagram rewriting as a mechanism of computation, focussing on the matching problem for rewritable subdiagrams within the combinatorial framework of diagrammatic sets. We provide an algorithm for subdiagram matching in arbitrary dimensions, based on new results on layerings of diagrams, and derive upper bounds on its time complexity. We show that these superpolynomial bounds can be improved to polynomial bounds under certain acyclicity conditions, and that these conditions hold in general for diagrams up to dimension 3. We discuss the challenges that arise in dimension 4.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175726"}, {"primary_key": "1223017", "vector": [], "sparse_vector": [], "title": "Expressive Completeness of Two-Variable First-Order Logic with Counting for First-Order Logic Queries on Rooted Unranked Trees.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the class of finite, rooted, unranked, unordered, node-labeled trees. Such trees are represented as structures with only the parent-child relation, in addition to any number of unary predicates for node labels. We prove that every unary first-order query over the considered class of trees is already expressible in two-variable first-order logic with counting. Somewhat to our surprise, we have not seen this result being conjectured in the extensive literature on logics for trees. Our proof is based on a global variant of local equivalence notions on nodes of trees. This variant applies to entire trees, and involves counting ancestors of locally equivalent nodes.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175828"}, {"primary_key": "1223018", "vector": [], "sparse_vector": [], "title": "ωPAP Spaces: Reasoning Denotationally About Higher-Order, Recursive Probabilistic and Differentiable Programs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We introduce a new setting, the category of ωPAP spaces, for reasoning denotationally about expressive differentiable and probabilistic programming languages. Our semantics is general enough to assign meanings to most practical probabilistic and differentiable programs, including those that use general recursion, higher-order functions, discontinuous primitives, and discrete and continuous sampling. But crucially, it is also specific enough to exclude many pathological denotations, enabling us to establish new results about differentiable and probabilistic programs. In the differentiable setting, we prove general correctness theorems for automatic differentiation and its use within gradient descent. In the probabilistic setting, we establish the almost-everywhere differentiability of probabilistic programs’ trace density functions, and the existence of convenient base measures for density computation in Monte Carlo inference. In some cases these results were previously known, but required detailed proofs of an operational flavor; by contrast, all our proofs work directly with programs’ denotations.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.********"}, {"primary_key": "1223019", "vector": [], "sparse_vector": [], "title": "A categorical account of composition methods in logic.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present a categorical theory of the composition methods in finite model theory – a key technique enabling modular reasoning about complex structures by building them out of simpler components. The crucial results required by the composition methods are <PERSON><PERSON><PERSON><PERSON> (FVM) type theorems, which characterize how logical equivalence be-haves under composition and transformation of models.Our results are developed by extending the recently introduced game comonad semantics for model comparison games. This level of abstraction allow us to give conditions yielding FVM type results in a uniform way. Our theorems are parametric in the classes of models, logics and operations involved. Furthermore, they naturally account for the positive existential fragment, and extensions with counting quantifiers of these logics. We also reveal surprising connections between FVM type theorems, and classical concepts in the theory of monads.We illustrate our methods by recovering many classical theorems of practical interest, including a refinement of a previous result by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> concerning the 3-variable counting logic and cospectrality. To highlight the importance of our techniques being parametric in the logic of interest, we prove a family of FVM theorems for products of structures, uniformly in the logic in question, which cannot be done using specific game arguments.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.********"}, {"primary_key": "1223020", "vector": [], "sparse_vector": [], "title": "Set-Theoretic and Type-Theoretic Ordinals Coincide.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In constructive set theory, an ordinal is a hereditarily transitive set. In homotopy type theory (HoTT), an ordinal is a type with a transitive, wellfounded, and extensional binary relation. We show that the two definitions are equivalent if we use (the <PERSON><PERSON> refinement of) <PERSON><PERSON><PERSON>'s interpretation of constructive set theory into type theory. Following this, we generalize the notion of a type-theoretic ordinal to capture all sets in <PERSON><PERSON><PERSON>'s interpretation rather than only the ordinals. This leads to a natural class of ordered structures which contains the type-theoretic ordinals and realizes the higher inductive interpretation of set theory. All our results are formalized in Agda.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175762"}, {"primary_key": "1223021", "vector": [], "sparse_vector": [], "title": "The Power of Positivity.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The Positivity Problem for linear recurrence sequences over a ring R of real algebraic numbers is to determine, given an LRS ${\\left( {{u_n}} \\right)_{n \\in \\mathbb{N}}}$ over R, whether u n ≥ 0 for all n. It is known to be Turing-equivalent to the following reachability problem: given a linear dynamical system (M, s) R d×d ×R d and a halfspace H ⊆ ℝ d , determine whether the orbit ${\\left( {{M^n}s} \\right)_{n \\in \\mathbb{N}}}$ ever enters H. The more general model-checking problem for LDS is to determine, given (M, s) and an ω-regular property φ over semialgebraic predicates T 1 ,…, T ℓ ⊆ ℝ d , whether the orbit of (M, s) satisfies φ.In this paper, we establish the following1)The Positivity Problem for LRS over real algebraic numbers reduces to the Positivity Problem for LRS over the integers; and2)The model-checking problem for LDS with diagonalisable M is decidable subject to a Positivity oracle for simple LRS over the integers.In other words, the full semialgebraic model-checking problem for diagonalisable linear dynamical systems is no harder than the Positivity Problem for simple integer linear recurrence sequences. This is in sharp contrast with the situation for arbitrary (not necessarily diagonalisable) LDS and arbitrary (not necessarily simple) integer LRS, for which no such correspondence is expected to hold.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175758"}, {"primary_key": "1223022", "vector": [], "sparse_vector": [], "title": "Taylor Expansion as a Monad in Models of DiLL.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Differential Linear Logic (DiLL) adds to Linear Logic (LL) a symmetrization of three out of the four exponential rules, which allows the expression of a natural notion of differentiation. In this paper, we introduce a codigging inference rule for <PERSON>LL and study the categorical semantics of DiLL with codigging using differential categories. The addition of codigging makes the rules of DiLL completely symmetrical. We will explain how codigging is interpreted thanks to the exponential function e x , and in certain cases by the convolutional exponential. In a setting with codigging, every proof is equal to its Taylor series, which implies that every model of DiLL with codigging is quantitative. We provide examples of codigging in relational models, as well as models related to game logic and quantum programming. We also construct a graded model of DiLL with codigging in which the indices witness exponential growth. Codigging makes the exponential of-course connective ! in LL into a monad, where the monad axioms enforce Taylor expansion. As such, codigging opens the door to monadic reformulations of quantitative features in programming languages, as well as further categorical generalizations.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175753"}, {"primary_key": "1223023", "vector": [], "sparse_vector": [], "title": "Fully Abstract Normal Form Bisimulation for Call-by-Value PCF.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present the first fully abstract normal form bisimulation for call-by-value PCF (PCF v ). Our model is based on a labelled transition system (LTS) that combines elements from applicative bisimulation, environmental bisimulation and game semantics. In order to obtain completeness while avoiding the use of semantic quotiening, the LTS constructs traces corresponding to interactions with possible functional contexts. The model gives rise to a sound and complete technique for checking of PCF v program equivalence, which we implement in a bounded bisimulation checking tool. We test our tool on known equivalences from the literature and new examples.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175778"}, {"primary_key": "1223024", "vector": [], "sparse_vector": [], "title": "Stopping Criteria for Value Iteration on Stochastic Games with Quantitative Objectives.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A classic solution technique for Markov decision processes (MDP) and stochastic games (SG) is value iteration (VI). Due to its good practical performance, this approximative approach is typically preferred over exact techniques, even though no practical bounds on the imprecision of the result could be given until recently. As a consequence, even the most used model checkers could return arbitrarily wrong results. Over the past decade, different works derived stopping criteria, indicating when the precision reaches the desired level, for various settings, in particular MDP with reachability, total reward, and mean payoff, and SG with reachability.In this paper, we provide the first stopping criteria for VI on SG with total reward and mean payoff, yielding the first anytime algorithms in these settings. To this end, we provide the solution in two flavours: First through a reduction to the MDP case and second directly on SG. The former is simpler and automatically utilizes any advances on MDP. The latter allows for more local computations, heading towards better practical efficiency.Our solution unifies the previously mentioned approaches for MDP and SG and their underlying ideas. To achieve this, we isolate objective-specific subroutines as well as identify objective-independent concepts. These structural concepts, while surprisingly simple, form the very essence of the unified solution.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175771"}, {"primary_key": "1223025", "vector": [], "sparse_vector": [], "title": "Evidential Decision Theory via Partial Markov Categories.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We introduce partial Markov categories. In the same way that Markov categories encode stochastic processes, partial Markov categories encode stochastic processes with constraints, observations and updates. In particular, we prove a synthetic Bayes theorem; we apply it to define a syntactic partial theory of observations on any Markov category whose normalisations can be computed in the original Markov category. Finally, we formalise Evidential Decision Theory in terms of partial Markov categories, and provide examples.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175776"}, {"primary_key": "1223026", "vector": [], "sparse_vector": [], "title": "Formalizing π4(S3) ≅Z/2Z and Computing a Brunerie Number in Cubical Agda.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>'s 2016 PhD thesis contains the first synthetic proof in Homotopy Type Theory (HoTT) of the classical result that the fourth homotopy group of the 3-sphere is ℤ/2ℤ. The proof is one of the most impressive pieces of synthetic homotopy theory to date and uses a lot of advanced classical algebraic topology rephrased synthetically. Furthermore, the proof is fully constructive and the main result can be reduced to the question of whether a particular \"B<PERSON>erie number\" β can be normalized to ±2. The question of whether <PERSON><PERSON><PERSON>'s proof could be formalized in a proof assistant, either by computing this number or by formalizing the pen-and-paper proof, has since remained open. In this paper, we present a complete formalization in Cubical Agda. We do this by modifying <PERSON><PERSON><PERSON>'s proof so that a key technical result, whose proof <PERSON><PERSON><PERSON> only sketched in his thesis, can be avoided. We also present a formalization of a new and much simpler proof that β is ±2. This formalization provides us with a sequence of simpler <PERSON><PERSON><PERSON> numbers, one of which normalizes very quickly to −2 in Cubical Agda, resulting in a fully formalized computer-assisted proof that ${\\pi _4}({\\mathbb{S}^3}) \\cong \\mathbb{Z}/2\\mathbb{Z}$.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175833"}, {"primary_key": "1223027", "vector": [], "sparse_vector": [], "title": "The Logic of Prefixes and Suffixes is Elementary under Homogeneity*.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we study the finite satisfiability problem for the logic BE under the homogeneity assumption. BE is the cornerstone of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s interval temporal logic, and features modal operators corresponding to the prefix (a.k.a. \"Begins\") and suffix (a.k.a. \"Ends\") relations on intervals. In terms of complexity, BE lies in between the \"Chop\" logic C, whose satisfiability problem is known to be non-elementary, and the PSpace-complete interval logic D of the sub-interval (a.k.a. \"During\") relation. BE was shown to be ExpSpace-hard, and the only known satisfiability procedure is primitive recursive, but not elementary. Our contribution consists of tightening the complexity bounds of the satisfiability problem for BE, by proving it to be ExpSpace-complete. We do so by devising an equi-satisfiable normal form with boundedly many nested modalities. The normalization technique resembles <PERSON>'s quantifier elimination, but it turns out to be much more involved due to the limitations enforced by the homogeneity assumption.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175824"}, {"primary_key": "1223028", "vector": [], "sparse_vector": [], "title": "Boolean symmetric vs. functional PCSP dichotomy.", "authors": ["Tamio<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "As our first result, we establish a dichotomy for promise constraint satisfaction problems of the form PCSP(A, B), where A is Boolean and symmetric and B is functional (on a domain of any size); i.e, all but one element of any tuple in a relation in B determine the last element. This includes PCSPs of the form PCSP(q-in-r, B), where B is functional, thus making progress towards a classification of PCSP(1-in-3, B), which were studied by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> [STACS'21] for B on three-element domains.As our second result, we show that for PCSP(A, B), where A contains a single symmetric relation and B is arbitrary (and thus not necessarily functional), the combined basic linear programming relaxation (BLP) and the affine integer programming relaxation (AIP) of <PERSON><PERSON><PERSON><PERSON> et al. [SICOMP'20] is no more powerful than the (in general strictly weaker) AIP relaxation of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [SICOMP'21].", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175746"}, {"primary_key": "1223029", "vector": [], "sparse_vector": [], "title": "Existential Calculi of Relations with Transitive Closure: Complexity and Edge Saturations.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We study the decidability and complexity of equational theories of the existential calculus of relations with transitive closure (ECoR*) and its fragments, where ECoR* is the positive calculus of relations with transitive closure extended with complements of term variables and constants. We give characterizations of these equational theories by using edge saturations and we show that the equational theory is 1) coNP-complete for ECoR* without transitive closure; 2) in coNEXP for ECoR* without intersection and PSPACE-complete for two smaller fragments; 3) $\\Pi _1^0$-complete for ECoR*. The second result gives PSPACE-upper bounds for some extensions of Kleene algebra, including Kleene algebra with top w.r.t. binary relations.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175811"}, {"primary_key": "1223030", "vector": [], "sparse_vector": [], "title": "A Metalanguage for Cost-Aware Denotational Semantics.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We present metalanguages for developing synthetic cost-aware denotational semantics of programming languages. Extending recent advances by <PERSON><PERSON> et al. in cost and behavioral verification in dependent type theory, we define two successively more expressive metalanguages for studying cost-aware metatheory. We construct synthetic denotational models of the simply-typed lambda calculus and Modernized Algol, a language with first-order store and while loops, and show that they satisfy a cost-aware generalization of the classic Plotkin-type computational adequacy theorem. Moreover, by developing our proofs in a synthetic language of phase-separated constructions of intension and extension, our results easily restrict to the corresponding extensional theorems. Consequently, our work provides a positive answer to the conjecture raised in op. cit. and contributes a framework for cost-aware programming, verification, and metatheory.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175777"}, {"primary_key": "1223031", "vector": [], "sparse_vector": [], "title": "The Probabilistic Rabin Tree Theorem*.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The Rabin tree theorem yields an algorithm to solve the satisfiability problem for monadic second-order logic over infinite trees. Here we solve the probabilistic variant of this problem. Namely, we show how to compute the probability that a randomly chosen tree satisfies a given formula. We additionally show that this probability is an algebraic number. This closes a line of research where similar results were shown for formalisms weaker than the full monadic second-order logic.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175800"}, {"primary_key": "1223032", "vector": [], "sparse_vector": [], "title": "Group Separation Strikes Back.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Group languages are regular languages recognized by finite groups, or equivalently by finite automata in which each letter induces a permutation on the set of states. We investigate the separation problem for this class of languages: given two arbitrary regular languages as input, we show how to decide if there exists a group language containing the first one while being disjoint from the second. We prove that covering, a problem generalizing separation, is decidable. A simple covering algorithm was already known: it can be obtained indirectly as a corollary of an algebraic theorem by <PERSON>. Unfortunately, while deducing the algorithm from this algebraic result is straightforward, all proofs of <PERSON>'s result itself require a strong background on algebraic concepts, and a wealth of technical machinery outside of automata theory. Our proof is independent previous ones. It relies exclusively on standard notions from automata theory: we directly deal with separation and work with input languages represented by nondeterministic finite automata.We also investigate two strict subclasses. First, the alphabet modulo testable languages are those defined by counting the occurrences of each letter modulo some fixed integer (equivalently, they are the languages recognized by a commutative group). Secondly, the modulo languages are those defined by counting the length of words modulo some fixed integer. We prove that covering is decidable for both classes, with algorithms that rely on the construction made for group languages.Our proofs lead to tight complexity bounds for separation for all three classes, as well as for covering for both alphabet modulo testable languages and for modulo testable languages.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175683"}, {"primary_key": "1223033", "vector": [], "sparse_vector": [], "title": "Completeness for arbitrary finite dimensions of ZXW-calculus, a unifying calculus.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ZX-calculus is a universal graphical language for qubit quantum computation, meaning that every linear map between qubits can be expressed in the ZX-calculus. Furthermore, it is a complete graphical rewrite system: any equation involving linear maps that is derivable in the Hilbert space formalism for quantum theory can also be derived in the calculus by rewriting. It has widespread usage within quantum industry and academia for a variety of tasks such as quantum circuit optimisation, error-correction, and education.The ZW-calculus is an alternative universal graphical language that is also complete for qubit quantum computing. In fact, its completeness was used to prove that the ZX-calculus is universally complete. This calculus has advanced how quantum circuits are compiled into photonic hardware architectures in the industry.Recently, by combining these two calculi, a new calculus has emerged for qubit quantum computation, the ZXW-calculus. Using this calculus, graphical-differentiation, -integration, and -exponentiation were made possible, thus enabling the development of novel techniques in the domains of quantum machine learning and quantum chemistry.Here, we generalise the ZXW-calculus to arbitrary finite dimensions, that is, to qudits. Moreover, we prove that this graphical rewrite system is complete for any finite dimension. This is the first completeness result for any universal graphical language beyond qubits.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175672"}, {"primary_key": "1223034", "vector": [], "sparse_vector": [], "title": "Distal Combinatorial Tools for Graphs of Bounded Twin-Width.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON> Przybyszewski"], "summary": "We study set systems formed by neighborhoods in graphs of bounded twin-width. We start by proving that such graphs have linear neighborhood complexity, in analogy to previous results concerning graphs from classes with bounded expansion and of bounded clique-width. Next, we shift our attention to the notions of distality and abstract cell decomposition, which come from model theory. We give a direct combinatorial proof that the edge relation is distal in classes of ordered graphs of bounded twin-width. This allows us to apply Distal cutting lemma and Distal regularity lemma, so we obtain powerful combinatorial tools for graphs of bounded twin-width.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175719"}, {"primary_key": "1223035", "vector": [], "sparse_vector": [], "title": "Extensional and Non-extensional Functions as Processes.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Following <PERSON><PERSON>'s seminal paper, the representation of functions as processes has received considerable attention. For pure λ-calculus, the process representations yield (at best) non-extensional λ-theories (i.e., β rule holds, whereas η does not).In the paper, we study how to obtain extensional representations, and how to move between extensional and non-extensional representations. Using Internal π, Iπ (a subset of the π-calculus in which all outputs are bound), we develop a refinement of <PERSON><PERSON>'s original encoding of functions as processes that is parametric on certain abstract components called wires. These are, intuitively, processes whose task is to connect two end-point channels. We show that when a few algebraic properties of wires hold, the encoding yields a λ-theory. Exploiting the symmetries and dualities of Iπ, we isolate three main classes of wires. The first two have a sequential behaviour and are dual of each other; the third has a parallel behaviour and is the dual of itself. We show the adoption of the parallel wires yields an extensional λ-theory; in fact, it yields an equality that coincides with that of Böhm trees with infinite η. In contrast, the other two classes of wires yield non-extensional λ-theories whose equalities are those of the Lévy-Longo and Böhm trees.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175686"}, {"primary_key": "1223036", "vector": [], "sparse_vector": [], "title": "Applications of Information Inequalities to Database Theory Problems.", "authors": ["<PERSON>"], "summary": "The paper describes several applications of information inequalities to problems in database theory. The problems discussed include: upper bounds of a query's output, worst-case optimal join algorithms, the query domination problem, and the implication problem for approximate integrity constraints. The paper is self-contained: all required concepts and results from information inequalities are introduced here, gradually, and motivated by database problems.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175769"}, {"primary_key": "1223037", "vector": [], "sparse_vector": [], "title": "Cartesian Coherent Differential Categories.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We extend to general cartesian categories the idea of Coherent Differentiation recently introduced by <PERSON><PERSON><PERSON> in the setting of categorical models of Linear Logic. The first ingredient is a summability structure which induces a partial left-additive structure on the category. Additional functoriality and naturality assumptions on this summability structure implement a differential calculus which can also be presented in a formalism close to <PERSON><PERSON>, <PERSON> and <PERSON>'s cartesian differential categories. We show that a simple term language equipped with a natural notion of differentiation can easily be interpreted in such a category.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175717"}, {"primary_key": "1223038", "vector": [], "sparse_vector": [], "title": "Weak Similarity in Higher-Order Mathematical Operational Semantics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Higher-order abstract GSOS is a recent extension of <PERSON><PERSON> and <PERSON><PERSON><PERSON>'s framework of Mathematical Operational Semantics to higher-order languages. The fundamental well-behavedness property of all specifications within the framework is that coalgebraic strong (bi)similarity on their operational model is a congruence. In the present work, we establish a corresponding congruence theorem for weak similarity, which is shown to instantiate to well-known concepts such as <PERSON><PERSON>'s applicative similarity for the λ-calculus. On the way, we develop several techniques of independent interest at the level of abstract categories, including relation liftings of mixed-variance bifunctors and higher-order GSOS laws, as well as <PERSON>'s method.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175706"}, {"primary_key": "1223039", "vector": [], "sparse_vector": [], "title": "On Exact Sampling in the Two-Variable Fragment of First-Order Logic.", "authors": ["<PERSON><PERSON>", "Juhua Pu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we study the sampling problem for first-order logic proposed recently by <PERSON> et al.—how to efficiently sample a model of a given first-order sentence on a finite domain? We extend their result for the universally-quantified subfragment of two-variable logic FO 2 (UFO 2 ) to the entire fragment of FO 2 . Specifically, we prove the domain-liftability under sampling of FO 2 , meaning that there exists a sampling algorithm for FO 2 that runs in time polynomial in the domain size. We then further show that this result continues to hold even in the presence of counting constraints, such as ∀x∃ =k y : φ(x, y) and ∃ =k x∀y : φ(x, y), for some quantifier-free formula φ(x, y). Our proposed method is constructive, and the resulting sampling algorithms have potential applications in various areas, including the uniform generation of combinatorial structures and sampling in statistical-relational models such as Markov logic networks and probabilistic logic programs.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175742"}, {"primary_key": "1223040", "vector": [], "sparse_vector": [], "title": "On Certificates, Expected Runtimes, and Termination in Probabilistic Pushdown Automata.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Probabilistic pushdown automata (pPDA) are a natural operational model for a variety of recursive discrete stochastic processes. In this paper, we study certificates – succinct and easily verifiable proofs – for upper and lower bounds on various quantitative properties of a given pPDA. We reveal an intimate, yet surprisingly simple connection between the existence of such certificates and the expected time to termination of the pPDA at hand. This is established by showing that certain intrinsic properties, like the spectral radius of the Jacobian of the pPDA's underlying polynomial equation system, are directly related to expected runtimes. As a consequence, we obtain that there always exist easy-to-check proofs for positive almost-sure termination: does a pPDA terminate in finite expected time?", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175714"}, {"primary_key": "1223041", "vector": [], "sparse_vector": [], "title": "Making Concurrency Functional.", "authors": ["<PERSON><PERSON>"], "summary": "The article bridges between two major paradigms in computation, the functional, at basis computation from input to output, and the interactive, where computation reacts to its environment while underway. Central to any compositional theory of interaction is the dichotomy between a system and its environment. Concurrent games and strategies address the dichotomy in fine detail, very locally, in a distributed fashion, through distinctions between Player moves (events of the system) and Opponent moves (those of the environment). A functional approach has to handle the dichotomy more ingeniously, via its blunter distinction between input and output. This has led to a variety of functional approaches, specialised to particular interactive demands. Through concurrent games we can see what separates and connects the differing paradigms, and show how:•to lift functions to strategies; how to turn functional dependency to causal dependency and so exploit functional techniques.•several approaches of functional programming and logic arise naturally as full subcategories of concurrent games, including stable domain theory; nondeterministic dataflow; geometry of interaction; the dialectica interpretation; lenses and optics, and their extensions to containers in dependent lenses and optics.•the enrichments of strategies (e.g. to probabilistic, quantum or real-number computation) specialise to the functional cases.", "published": "2023-01-01", "category": "lics", "pdf_url": "", "sub_summary": "", "source": "lics", "doi": "10.1109/LICS56636.2023.10175727"}]