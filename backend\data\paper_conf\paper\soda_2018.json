[{"primary_key": "3510030", "vector": [], "sparse_vector": [], "title": "Revenue Maximization with an Uncertainty-Averse Buyer.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most work in mechanism design assumes that buyers are risk neutral; some considers risk aversion arising due to a non-linear utility for money. Yet behavioral studies have established that real agents exhibit risk attitudes which cannot be captured by any expected utility model. We initiate the study of revenue-optimal mechanisms under behavioral models beyond expected utility theory. We adopt a model from prospect theory which arose to explain these discrepancies and incorporates agents under-weighting uncertain outcomes. In our model, an event occurring with probability x < 1 is worth strictly less to the agent than x times the value of the event when it occurs with certainty.We present three main results. First, we characterize optimal mechanisms as menus of two-outcome lotteries. Second, we show that under a reasonable bounded-risk-aversion assumption, posted pricing obtains a constant approximation to the optimal revenue. Notably, this result is \"risk-robust\" in that it does not depend on the details of the buyer's risk attitude. Third, we consider dynamic settings in which the buyer's uncertainty about his future value may allow the seller to extract more revenue. In contrast to the positive result above, here we show it is not possible to achieve any constant-factor approximation to revenue using deterministic mechanisms in a risk-robust manner.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.134"}, {"primary_key": "3510031", "vector": [], "sparse_vector": [], "title": "Quasi-regular sequences and optimal schedules for security games.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study security games in which a defender commits to a mixed strategy for protecting a finite set of targets of different values. An attacker, knowing the defender's strategy, chooses which target to attack and for how long. If the attacker spends time $t$ at a target $i$ of value $\\alpha_i$, and if he leaves before the defender visits the target, his utility is $t \\cdot \\alpha_i $; if the defender visits before he leaves, his utility is 0. The defender's goal is to minimize the attacker's utility. The defender's strategy consists of a schedule for visiting the targets; it takes her unit time to switch between targets. Such games are a simplified model of a number of real-world scenarios such as protecting computer networks from intruders, crops from thieves, etc. We show that optimal defender play for this continuous time security games reduces to the solution of a combinatorial question regarding the existence of infinite sequences over a finite alphabet, with the following properties for each symbol $i$: (1) $i$ constitutes a prescribed fraction $p_i$ of the sequence. (2) The occurrences of $i$ are spread apart close to evenly, in that the ratio of the longest to shortest interval between consecutive occurrences is bounded by a parameter $K$. We call such sequences $K$-quasi-regular. We show that, surprisingly, $2$-quasi-regular sequences suffice for optimal defender play. What is more, even randomized $2$-quasi-regular sequences suffice for optimality. We show that such sequences always exist, and can be calculated efficiently. The question of the least $K$ for which deterministic $K$-quasi-regular sequences exist is fascinating. Using an ergodic theoretical approach, we show that deterministic $3$-quasi-regular sequences always exist. For $2 \\leq K < 3$ we do not know whether deterministic $K$-quasi-regular sequences always exist.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.106"}, {"primary_key": "3510033", "vector": [], "sparse_vector": [], "title": "Reachability Preservers: New Extremal Bounds and Approximation Algorithms.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In this paper we prove new results about the extremal structure of paths in directed graphs. Say we are given a directed graph G = (V, E) on n nodes, a set of sources S ⊆ V of size |S| = n1/3, and a subset P ⊆ S × V of pairs (s,t) where s ∊ S, of size O(n2/3), such that for all pairs (s,t) ∊ P, there is a path from s to t. Our goal is to remove as many edges from G as possible while maintaining the reachability of all pairs in P. How many edges will we have to keep? Can you always go down to n1+o(1) edges? Or maybe for some nasty graphs G you cannot even go below the simple bound of O(n4/3) edges? Embarrassingly, in a world where graph reachability is ubiquitous in countless scientific fields, the current bounds on the answer to this question are far from tight.In this paper, we make polynomial progress in both the upper and lower bounds for these Reachability Preservers over bounds that were implicit in the literature. We show that in the above scenario, O(n) edges will always be sufficient, and in general one is even guaranteed a subgraph on edges that preserves the reachability of all pairs in P. We complement this with a lower bound graph construction, establishing that the above result fully characterizes the settings in which we are guaranteed a preserver of size O(n). Moreover, we design an efficient algorithm that can always compute a preserver of existentially optimal size.The second contribution of this paper is a new connection between extremal graph sparsification results and classical Steiner Network Design problems. Surprisingly, prior to this work, the osmosis of techniques between these two fields had been superficial. This allows us to improve the state of the art approximation algorithms for the most basic Steiner-type problem in directed graphs from the O(n0.6+ε) of Chlamatac, Dinitz, Kortsarz, and Laekhanukit (SODA'17) to O(n0.577+ε).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.122"}, {"primary_key": "3510034", "vector": [], "sparse_vector": [], "title": "Near-Optimal Compression for the Planar Graph Metric.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The Planar Graph Metric Compression Problem is to compactly encode the distances among k nodes in a planar graph of size n. Two naïve solutions are to store the graph using O(n) bits, or to explicitly store the distance matrix with O(k2 log n) bits. The only lower bounds are from the seminal work of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>g, Prennes, and <PERSON>z [SODA'01], who rule out compressions into a polynomially smaller number of bits, for weighted planar graphs, but leave a large gap for unweighted planar graphs. For example, when , the upper bound is O(n) and their constructions imply an Ω(n3/4) lower bound. This gap is directly related to other major open questions in labeling schemes, dynamic algorithms, and compact routing.Our main result is a new compression of the planar graph metric into bits, which is optimal up to log factors. Our data structure circumvents an Õ(k2) lower bound of <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [SIDMA'14] for compression using minors, and the lower bound of <PERSON><PERSON><PERSON><PERSON> et al. for compression of weighted planar graphs. This is an unexpected and decisive proof that weights can make planar graphs inherently more complex. Moreover, we design a new Subset Distance Oracle for planar graphs with space, and Õ(n3/4) query time.Our work carries strong messages to related fields. In particular, the famous O(n1/2) vs. Ω(n1/3) gap for distance labeling schemes in planar graphs cannot be resolved with the current lower bound techniques. On the positive side, we introduce the powerful tool of unit-monge to planar graph algorithms.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.35"}, {"primary_key": "3510035", "vector": [], "sparse_vector": [], "title": "<PERSON> Spanning Trees and their Applications.", "authors": ["<PERSON><PERSON> Abraham", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The metric Ramsey problem asks for the largest subset S of a metric space that can be embedded into an ultrametric (more generally into a Hilbert space) with a given distortion. Study of this problem was motivated as a non-linear version of <PERSON><PERSON><PERSON><PERSON><PERSON> theorem. <PERSON><PERSON> and <PERSON><PERSON> [MN07] devised the so called Ramsey Partitions to address this problem, and showed the algorithmic applications of their techniques to approximate distance oracles and ranking problems.In this paper we study the natural extension of the metric Ramsey problem to graphs, and introduce the notion of Ramsey Spanning Trees. We ask for the largest subset S ⊆ V of a given graph G = (V, E), such that there exists a spanning tree of G that has small stretch for S. Applied iteratively, this provides a small collection of spanning trees, such that each vertex has a tree providing low stretch paths to all other vertices. The union of these trees serves as a special type of spanner, a tree-padding spanner. We use this spanner to devise the first compact stateless routing scheme with O(1) routing decision time, and labels which are much shorter than in all currently existing schemes.We first revisit the metric Ramsey problem, and provide a new deterministic construction. We prove that for every k, any n-point metric space has a subset S of size at least n1–1/k which embeds into an ultrametric with distortion 8k. We use this result to obtain the state-of-the-art deterministic construction of a distance oracle. Building on this result, we prove that for every k, any n-vertex graph G = (V, E) has a subset S of size at least n1–1/k, and a spanning tree of G, that has stretch O(k log log n) between any point in S and any point in V.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.108"}, {"primary_key": "3510036", "vector": [], "sparse_vector": [], "title": "Voronoi tessellations in the CRT and continuum random maps of finite excess.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Given a large graph G and k agents on this graph, we consider the Voronoi tessellation induced by the graph distance. Each agent gets control of the portion of the graph that is closer to itself than to any other agent. We study the limit law of the vector Vor: = (V1/n, V2/n, …, Vk/n), whose i'th coordinate records the fraction of vertices of G controlled by the i'th agent, as n tends to infinity. We show that if G is a uniform random tree, and the agents are placed uniformly at random, the limit law of Vor is uniform on the (k – 1)-dimensional simplex. In particular, when k = 2, the two agents each get a uniform random fraction of the territory. In fact, we prove the result directly on the Brownian continuum random tree (CRT), and we also prove the same result for a \"higher genus\" analogue of the CRT that we call the continuum random unicellular map, indexed by a genus parameter g ≥ 0. As a key step of independent interest, we study the case when G is a random planar embedded graph with a finite number of faces. The main idea of the proof is to show that <PERSON><PERSON> has the same distribution as another partition of mass Int: = (I1/n, I2/n, …, Ik/n) where Ij is the contour length separating the i-th agent from the next one in clockwise order around the graph.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.60"}, {"primary_key": "3510037", "vector": [], "sparse_vector": [], "title": "On the complexity of range searching among curves.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern tracking technology has made the collection of large numbers of densely sampled trajectories of moving objects widely available. We consider a fundamental problem encountered when analysing such data: Given n polygonal curves S in ℝd, preprocess S into a data structure that answers queries with a query curve q and radius ρ for the curves of S that have Fréchet distance at most ρ to q.We initiate a comprehensive analysis of the space/query-time tradeoff for this data structuring problem. Our lower bounds imply that any data structure in the pointer model model that achieves Q(n) + O(k) query time, where k is the output size, has to use roughly n((n/Q(n))2) space in the worst case, even if queries are mere points (for the discrete Fréchet distance) or line segments (for the continuous Fréchet distance). More importantly, we show that more complex queries and input curves lead to additional logarithmic factors in the lower bound. Roughly speaking, the number of logarithmic factors added is linear in the number of edges added to the query and input curve complexity. This means that the space/query time tradeoff worsens by an exponential factor of input and query complexity. This behaviour addresses an open question (see [1, 9]) in the range searching literature concerning multilevel partition trees which may be of independent interest, namely, whether it is possible to avoid the additional logarithmic factors in the space and query time of a multilevel partition tree. We answer this question negatively.On the positive side, we show we can build data structures for the Fréchet distance by using semialgebraic range searching. The space/query-time tradeoff of our data structure for the discrete Fréchet distance is in line with the lower bound, as the number of levels in the data structure is O(t), where t denotes the maximal number of vertices of a curve. For the continuous Fréchet distance, the number of levels increases to O(t2).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.58"}, {"primary_key": "3510039", "vector": [], "sparse_vector": [], "title": "Race Detection and Reachability in Nearly Series-Parallel DAGs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "I-<PERSON>g <PERSON>", "<PERSON>", "<PERSON><PERSON> Xu"], "summary": "A program is said to have a determinacy race if logically parallel parts of a program access the same memory location and one of the accesses is a write. These races are generally bugs in the program since they lead to non-deterministic program behavior — different schedules of the program can lead to different results. Most prior work on detecting these races focuses on a subclass of programs with series-parallel or nested parallelism.This paper presents a race-detection algorithm for detecting races in a more general class of programs, namely programs that include arbitrary ordering constraints in additional to the series-parallel constructs. The algorithm performs a serial execution of the program, augmented to detect races, in O(T1 + k2) time, where T1 is the sequential running time of the original program and k is the number of non series-parallel constraints.The main technical novelty of this paper is a new data structure, R-Sketch, for answering reachability queries in nearly series-parallel (SP) directed acyclic graphs (DAGs). Given as input a graph comprising an n-node series parallel graph and k additional non-SP edges, the total construction time of the data structure is O(n + k2), and each reachability query can be answered in O(1) time. The data structure is traversally incremental, meaning that it supports the insertion of nodes/edges, but only as they are discovered through a graph traversal.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.11"}, {"primary_key": "3510040", "vector": [], "sparse_vector": [], "title": "Recognizing Weak Embeddings of Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Csaba D. T<PERSON>"], "summary": "We present an efficient algorithm for a problem in the interface between clustering and graph embeddings. An embedding φ : G → M of a graph G into a 2-manifold M maps the vertices in V (G) to distinct points and the edges in E (G) to interior-disjoint Jordan arcs between the corresponding vertices. In applications in clustering, cartography, and visualization, nearby vertices and edges are often bundled to a common node or arc, due to data compression or low resolution. This raises the computational problem of deciding whether a given map φ : G → M comes from an embedding. A map φ : G → M is a weak embedding if it can be perturbed into an embedding ψε : G → M with ║φ – ψε║ 0.A polynomial-time algorithm for recognizing weak embeddings was recently found by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [14], which reduces to solving a system of linear equations over ℤ2. It runs in O(π2ω) ≤ O(n4.75) time, where ω ≈ 2.373 is the matrix multiplication exponent and n is the number of vertices and edges of G. We improve the running time to O(n log n). Our algorithm is also conceptually simpler than [14]: We perform a sequence of local operations that gradually \"untangles\" the image φ(G) into an embedding ψ(G), or reports that φ is not a weak embedding. It generalizes a recent technique developed for the case that G is a cycle and the embedding is a simple polygon [1], and combines local constraints on the orientation of subgraphs directly, thereby eliminating the need for solving large systems of linear equations.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.20"}, {"primary_key": "3510041", "vector": [], "sparse_vector": [], "title": "Space-Optimal Majority in Population Protocols.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Population protocols are a popular model of distributed computing, in which n agents with limited local state interact randomly, and cooperate to collectively compute global predicates. Inspired by recent developments in DNA programming, an extensive series of papers, across different communities, has examined the computability and complexity characteristics of this model. Majority, or consensus, is a central task in this model, in which agents need to collectively reach a decision as to which one of two states A or B had a higher initial count. Two metrics are important: the time that a protocol requires to stabilize to an output decision, and the state space size that each agent requires to do so. It is known that majority requires Ω(log log n) states per agent to allow for fast (poly-logarithmic time) stabilization, and that O(log2 n) states are sufficient. Thus, there is an exponential gap between the space upper and lower bounds for this problem. This paper addresses this question.On the negative side, we provide a new lower bound of Ω(log n) states for any protocol which stabilizes in O(n1–c) expected time, for any constant c > 0. This result is conditional on monotonicity and output assumptions, satisfied by all known protocols. Technically, it represents a departure from previous lower bounds, in that it does not rely on the existence of dense configurations. Instead, we introduce a new generalized surgery technique to prove the existence of incorrect executions for any algorithm which would contradict the lower bound. Subsequently, our lower bound also applies to general initial configurations, including ones with a leader. On the positive side, we give a new algorithm for majority which uses O(log n) states, and stabilizes in O(log2 n) expected time. Central to the algorithm is a new leaderless phase clock technique, which allows agents to synchronize in phases of Θ(n log n) consecutive interactions using O(log n) states per agent, exploiting a new connection between population protocols and power-of-two-choices load balancing mechanisms. We also employ our phase clock to build a leader election algorithm with a state space of size O(log n), which stabilizes in O(log2 n) expected time.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.144"}, {"primary_key": "3510042", "vector": [], "sparse_vector": [], "title": "A Hamiltonian Cycle in the Square of a 2-connected Graph in Linear Time.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON>'s theorem says that the square of every 2-connected graph contains a Hamiltonian cycle. We present a proof resulting in an O(|E|) algorithm for producing a Hamiltonian cycle in the square G2 of a 2-connected graph G = (V;E). The previous best was O(|V |2) by <PERSON> in 1980. More generally, we get an O(|E|) algorithm for producing a Hamiltonian path between any two prescribed vertices, and we get an O(|V |2) algorithm for producing cycles C3;C4; : : : ;C|V| in G2 of lengths 3; 4; : : : ; |V|, respectively. © Copyright 2018 by SIAM.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.107"}, {"primary_key": "3510043", "vector": [], "sparse_vector": [], "title": "Approximating the Largest Root and Applications to Interlacing Families.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of approximating the largest root of a real-rooted polynomial of degree n using its top k coefficients and give nearly matching upper and lower bounds. We present algorithms with running time polynomial in k that use the top k coefficients to approximate the maximum root within a factor of n1/k and when k ≤ log n and k > log n respectively. We also prove corresponding information-theoretic lower bounds of nΩ(1/k) and , and show strong lower bounds for noisy version of the problem in which one is given access to approximate coefficients.This problem has applications in the context of the method of interlacing families of polynomials, which was used for proving the existence of Ramanujan graphs of all degrees, the solution of the Kadison-Singer problem, and bounding the integrality gap of the asymmetric traveling salesman problem. All of these involve computing the maximum root of certain real-rooted polynomials for which the top few coefficients are accessible in subexponential time. Our results yield an algorithm with the running time of for all of them.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.66"}, {"primary_key": "3510044", "vector": [], "sparse_vector": [], "title": "Nash Social Welfare for Indivisible Items under Separable, Piecewise-Linear Concave Utilities.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Recently <PERSON> and <PERSON><PERSON><PERSON><PERSON> [10] gave the first constant factor approximation algorithm for the problem of allocating indivisible items to agents, under additive valuations, so as to maximize the Nash social welfare (NSW). We give constant factor algorithms for a substantial generalization of their problem – to the case of separable, piecewise-linear concave utility functions. We give two such algorithms, the first using market equilibria and the second using the theory of real stable polynomials. Both approaches require new algorithmic ideas.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.147"}, {"primary_key": "3510045", "vector": [], "sparse_vector": [], "title": "A Faster Algorithm for Minimum-Cost Bipartite Perfect Matching in Planar Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a weighted planar bipartite graph G(A ∪ B, E) where each edge has a positive integer edge cost, we give an Õ(n4/3 log nC) time algorithm to compute minimum-cost perfect matching; here C is the maximum edge cost in the graph. The previous best known planarity exploiting algorithm has a running time of O(n3/2 log n) and is achieved by using planar separators (<PERSON><PERSON> and <PERSON><PERSON><PERSON> '80).Our algorithm is based on the bit-scaling paradigm (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> '89). For each scale, our algorithm first executes O(n1/3) iterations of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s algorithm in O(n4/3) time leaving only O(n2/3) vertices unmatched. Next, it constructs a compressed residual graph H with O(n2/3) vertices and O(n) edges. This is achieved by using an r-division of the planar graph G with r = n2/3. For each partition of the r-division, there is an edge between two vertices of H if and only if they are connected by a directed path inside the partition. Using existing efficient shortest-path data structures, the remaining O(n2/3) vertices are matched by iteratively computing a minimum-cost augmenting path each taking Õ(n2/3) time. Augmentation changes the residual graph, so the algorithm updates the compressed representation for each affected partition in O(n2/3) time. We bound the total number of affected partitions over all the augmenting paths by O(n2/3 log n). Therefore, the total time taken by the algorithm is Õ(n4/3).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.31"}, {"primary_key": "3510046", "vector": [], "sparse_vector": [], "title": "Polycubes with Small Perimeter Defect.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A polycube is a face-connected set of cubical cells on ℤ3. To-date, no formulae enumerating polycubes by volume (number of cubes) or perimeter (number of empty cubes neighboring the polycube) are known. We present a few formulae enumerating polycubes with a fixed deviation from the maximum possible perimeter.MSC codesPolycubeslinear recurrencegenerating functions", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.6"}, {"primary_key": "3510047", "vector": [], "sparse_vector": [], "title": "Tight Bounds on the Round Complexity of the Distributed Maximum Coverage Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the maximum k-set coverage problem in the following distributed setting. A collection of input sets S1, …, Sm over a universe [n] is partitioned across p machines and the goal is to find k sets whose union covers the most number of elements. The computation proceeds in rounds where in each round machines communicate information to each other. Specifically, in each round, all machines simultaneously send a message to a central coordinator who then communicates back to all machines a summary to guide the computation for the next round. At the end of the last round, the coordinator outputs the answer. The main measures of efficiency in this setting are the approximation ratio of the returned solution, the communication cost of each machine, and the number of rounds of computation.Our main result is an asymptotically tight bound on the tradeoff between these three measures for the distributed maximum coverage problem. We first show that any r-round protocol for this problem either incurs a communication cost of k · mΩ(1/r) or only achieves an approximation factor of kΩ(1/r). This in particular implies that any protocol that simultaneously achieves good approximation ratio (O(1) approximation) and good communication cost (Õ(n) communication per machine), essentially requires logarithmic (in k) number of rounds. We complement our lower bound result by showing that there exist an r-round protocol that achieves an -approximation (essentially best possible) with a communication cost of k · mO(1/r) as well as an r-round protocol that achieves a kO(1/r)-approximation with only Õ(n) communication per each machine (essentially best possible).We further use our results in this distributed setting to obtain new bounds for maximum coverage in two other main models of computation for massive datasets, namely, the dynamic streaming model and the MapReduce model.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.155"}, {"primary_key": "3510048", "vector": [], "sparse_vector": [], "title": "Randomized Algorithms for Online Vector Load Balancing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study randomized algorithms for the online vector bin packing and vector scheduling problems. For vector bin packing, we achieve a competitive ratio of Õ(d1/B), where d is the number of dimensions and B the size of a bin. This improves the previous bound of Õ(d1/(B-1)) by a polynomial factor, and is tight up to logarithmic factors. For vector scheduling, we show a lower bound of on the competitive ratio of randomized algorithms, which is the first result for randomized algorithms and is asymptotically tight. Finally, we analyze the widely used \"power of two choices' algorithm for vector scheduling, and show that its competitive ratio is , which is optimal up to the additive O(log log n) term that also appears in the scalar version of this algorithm.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.63"}, {"primary_key": "3510049", "vector": [], "sparse_vector": [], "title": "Targeting and Signaling in Ad Auctions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Haifeng Xu"], "summary": "Modern ad auctions allow advertisers to target more specific segments of the user population. Unfortunately, this is not always in the best interest of the ad platform – partially hiding some information could be more beneficial for the platform's revenue. In this paper, we examine the following basic question in the context of second-price ad auctions: how should an ad platform optimally reveal information about the ad opportunity to the advertisers in order to maximize revenue? We consider a model in which bidders' valuations depend on a random state of the ad opportunity. Different from previous work, we focus on a more practical, and challenging, situation where the space of possible realizations of ad opportunities is extremely large. We thus focus on developing algorithms whose running time is polynomial in the number of bidders, but is independent of the number of ad opportunity realizations.We assume that the auctioneer can commit to a signaling scheme to reveal noisy information about the realized state of the ad opportunity, and examine the auctioneer's algorithmic question of designing the optimal signaling scheme. We first consider that the auctioneer is restricted to send a public signal to all bidders. As a warm-up, we start with a basic (though less realistic) setting in which the auctioneer knows the bidders' valuations, and show that an e-optimal scheme can be implemented in time polynomial in the number of bidders and 1/∊. We then move to a well-motivated Bayesian valuation setting in which the auctioneer and bidders both have private information, and present two results. First, we exhibit a characterization result regarding approximately optimal schemes and prove that any constant-approximate public signaling scheme must use exponentially many signals. Second, we present a \"simple\" public signaling scheme that serves as a constant approximation under mild assumptions.Finally, we initiate an exploration on the power of being able to send different signals privately to different bidders. In the basic setting where the auctioneer knows bidders' valuations, we exhibit a polynomial-time private scheme that extracts almost full surplus even in the worst Bayes Nash equilibrium. This illustrates the surprising power of private signaling schemes in extracting revenue.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.163"}, {"primary_key": "3510050", "vector": [], "sparse_vector": [], "title": "Parameterized Algorithms for Survivable Network Design with Uniform Demands.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pranaben<PERSON> Mi<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the Survivable Network Design Problem (SNDP), the input is an edge-weighted (di)graph G and an integer ruυ for every pair of vertices u, υ ∊ V(G). The objective is to construct a subgraph H of minimum weight which contains ruυ edge-disjoint (or node-disjoint) u-υ paths. This is a fundamental problem in combinatorial optimization that captures numerous well-studied problems in graph theory and graph algorithms. Consequently, there is a long line of research into exact-polynomial time algorithms as well as approximation algorithms for various restrictions of this problem. An important restriction of this problem is one where the connectivity demands are the same for every pair of vertices. In this paper, we first consider the edge-connectivity version of this problem which we call λ-Edge Connected Subgraph (λ-ECS). In this problem, the input is a λ-edge connected (di)graph G and an integer k and the objective is to check whether G contains a spanning subgraph H that is also λ-edge connected and H excludes at least k edges of G. In other words, we are asked to compute a maximum subset of edges, of cardinality at least k, which may be safely deleted from G without affecting its connectivity. If we replace λ-edge connectivity with λ-vertex connectivity we get the λ-Vertex Connected Subgraph (λ-VCS) problem. We show that λ-ECS is fixed-parameter tractable (FPT) for both graphs and digraphs even if the (di)graph has nonnegative real weights on the edges and the objective is to exclude from H, some edges of G whose total weight exceeds a prescribed value. In particular, we design an algorithm for the weighted variant of the problem with running time 2O(k log k) |V(G)|O(1). We follow up on this result and obtain a polynomial compression for λ-ECS on unweighted graphs. As a direct consequence of our results, we obtain the first FPT algorithm for the parameterized version of the classical Minimum Equivalent Graph (MEG) problem. We also show that λ-Ves is FPT on digraphs; however the problem on undirected graphs remains open. Finally, we complement our algorithmic findings by showing that SNDP is W[1]-hard for both arc and vertex connectivity versions on digraphs. The core of our algorithms is composed of new combinatorial results on connectivity in digraphs and undirected graphs.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.180"}, {"primary_key": "3510051", "vector": [], "sparse_vector": [], "title": "Competitive Algorithms for Generalized k-Server in Uniform Metrics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The generalized k-server problem is a far-reaching extension of the k-server problem with several applications. Here, each server si lies in its own metric space Mi. A request is a k-tuple r = (r1, r2, …, rk) and to serve it, we need to move some server si to the point ri ∊ Mi, and the goal is to minimize the total distance traveled by the servers. Despite much work, no f(k)-competitive algorithm is known for the problem for k > 2 servers, even for special cases such as uniform metrics and lines.Here, we consider the problem in uniform metrics and give the first f(k)-competitive algorithms for general k. In particular, we obtain deterministic and randomized algorithms with competitive ratio k · 2k and O(k3 log k) respectively. Our deterministic bound is based on a novel application of the polynomial method to online algorithms, and essentially matches the long-known lower bound of 2k – 1. We also give a 22O(k)-competitive deterministic algorithm for weighted uniform metrics, which also essentially matches the recent doubly exponential lower bound for the problem.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.64"}, {"primary_key": "3510052", "vector": [], "sparse_vector": [], "title": "Nested Convex Bodies are Chaseable.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In the Convex Body Chasing problem, we are given an initial point v0 ∊ ℝd and an online sequence of n convex bodies F1, …, Fn. When we receive Fi, we are required to move inside Fi. Our goal is to minimize the total distance traveled. This fundamental online problem was first studied by <PERSON> and <PERSON> (DCG 1993). They proved an lower bound on the competitive ratio, and conjectured that a competitive ratio depending only on d is possible. However, despite much interest in the problem, the conjecture remains wide open.We consider the setting in which the convex bodies are nested: Fi ⊃ … ⊃ Fn. The nested setting is closely related to extending the online LP framework of <PERSON><PERSON><PERSON><PERSON> and <PERSON> (ESA 2005) to arbitrary linear constraints. Moreover, this setting retains much of the difficulty of the general setting and captures an essential obstacle in resolving <PERSON> and <PERSON><PERSON>'s conjecture. In this work, we give a f(d)-competitive algorithm for chasing nested convex bodies in ℝd.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.81"}, {"primary_key": "3510053", "vector": [], "sparse_vector": [], "title": "Approximate Single Source Fault Tolerant Shortest Path.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Let G = (V, E) be an n-vertices m-edges directed graph with edge weights in the range [1, W] and L = log(W). Let s ∊ V be a designated source. In this paper we address several variants of the problem of maintaining the (1 + ∊)-approximate shortest path from s to each υ ∊ V \\ {s} in the presence of a failure of an edge or a vertex. From the graph theory perspective we show that G has a subgraph H with Õ(nL/∊) edges such that for any x,υ ∊ V, the graph H \\ x contains a path whose length is a (1 + ∊)-approximation of the length of the shortest path from s to υ in G \\ x. We show that the size of the subgraph H is optimal (up to logarithmic factors) by proving a lower bound of Ω(nL/∊) edges. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> [12] showed that the size of a fault tolerant exact shortest path subgraph in weighted directed/undirected graphs is Ω(m). <PERSON><PERSON> and <PERSON><PERSON><PERSON> [18] showed that even in the restricted case of unweighted undirected graphs the size of any subgraph for the exact shortest path is at least Ω(n1.5). Therefore, a (1 + ∊)-approximation is the best one can hope for. We consider also the data structure problem and show that there exists an Õ(nL/∊) size oracle that for any υ ∊ V reports a (1 + ∊)-approximate distance of v from s on a failure of any x ∊ V in O(loglog1+∊(nW)) time. We show that the size of the oracle is optimal (up to logarithmic factors) by proving a lower bound of Ω(nL/∊ log n). Finally, we present two distributed algorithms. We present a single source routing scheme that can route on a (1 + ∊)-approximation of the shortest path from a fixed source s to any destination t in the presence of a fault. Each vertex has a label and a routing table of Õ(L/∊) bits. We present also a labeling scheme that assigns each vertex a label of Õ(L/∊) bits. For any two vertices x, υ ∊ V the labeling scheme outputs a (1 + ∊)-approximation of the distance from s to υ in G\\x using only the labels of x and v.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.124"}, {"primary_key": "3510054", "vector": [], "sparse_vector": [], "title": "Incremental DFS algorithms: a theoretical and experimental study.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shahbaz Khan"], "summary": "The depth first search (DFS) tree is a fundamental data structure used for solving various graph problems. For a given graph G = (V, E) on n vertices and m edges, a DFS tree can be built in O(m + n) time. In the last 20 years, a few algorithms have been designed for maintaining a DFS tree efficiently under insertion of edges. For undirected graphs, there are two prominent algorithms, namely, ADFS1 and ADFS2 [ICALP14] that achieve total update time of and O(n2) respectively. For directed acyclic graphs, the only non-trivial algorithm, namely, FDFS [IPL97] requires total O(mn) update time. However, even after 20 years of this result, there does not exist any non-trivial incremental algorithm for maintaining a DFS tree in directed graphs with o(m2) worst case bound.In this paper, we carry out extensive experimental and theoretical evaluation of the existing incremental DFS algorithms in random graphs and real world graphs and derive the following results.1.For insertion of a uniformly random sequence of edges, each of ADFS1, ADFS2 and FDFS perform equally well and are found to take Θ(n2) time experimentally. This is quite surprising because the worst case bounds of ADFS1 and FDFS are greater than Θ(n2) by a factor of and m/n respectively, which are also proven to be tight. We complement this experimental result with a probabilistic analysis of these algorithms establishing Õ(n2) bound on their time complexity. For this purpose, we derive results about the structure of a DFS tree in a random graph. These results are of independent interest in the domain of random graphs.2.The insight that we developed about DFS tree in random graphs leads us to design an extremely simple algorithm for incremental DFS that works for both undirected and directed graphs. Moreover, this algorithm theoretically matches and experimentally outperforms the state-of-the-art algorithm in dense random graphs. Furthermore, it can also be used as a single-pass semi-streaming algorithm for computing incremental DFS and strong connectivity for random graphs using O(n log n) space.3.Even for real world graphs, which are usually sparse, both ADFS1 and FDFS turn out to be much better than their theoretical bounds. Here again, we present two simple algorithms for incremental DFS for directed and undirected graphs respectively, which perform very well on real graphs. In fact our proposed algorithm for directed graphs almost always matches the performance of FDFS.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.4"}, {"primary_key": "3510055", "vector": [], "sparse_vector": [], "title": "From Battlefields to Elections: Winning Strategies of Blotto and Auditing Games.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Mixed strategies are often evaluated based on the expected payoff that they guarantee. This is not always desirable. In this paper, we consider games for which maximizing the expected payoff deviates from the actual goal of the players. To address this issue, we introduce the notion of a (u,p)-maxmin strategy which ensures receiving a minimum utility of u with probability at least p. We then give approximation algorithms for the problem of finding a (u, p)-maxmin strategy for these games.The first game that we consider is Colonel <PERSON>, a well-studied game that was introduced in 1921. In the Colonel <PERSON> game, two colonels divide their troops among a set of battlefields. Each battlefield is won by the colonel that puts more troops in it. The payoff of each colonel is the weighted number of battlefields that she wins. We show that maximizing the expected payoff of a player does not necessarily maximize her winning probability for certain applications of <PERSON>. For example, in presidential elections, the players' goal is to maximize the probability of winning more than half of the votes, rather than maximizing the expected number of votes that they get. We give an exact algorithm for a natural variant of continuous version of this game. More generally, we provide constant and logarithmic approximation algorithms for finding (u, p)-maxmin strategies.We also introduce a security game version of <PERSON> which we call auditing game. It is played between two players, a defender and an attacker. The goal of the defender is to prevent the attacker from changing the outcome of an instance of Colonel <PERSON>. Again, maximizing the expected payoff of the defender is not necessarily optimal. Therefore we give a constant approximation for (u, p)-maxmin strategies.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.148"}, {"primary_key": "3510056", "vector": [], "sparse_vector": [], "title": "Improved Bounds for Testing Forbidden Order Patterns.", "authors": ["<PERSON><PERSON><PERSON>", "Clément L. <PERSON>"], "summary": "A sequence f: {1, …, n} → R contains a permutation π of length k if there exist i1 < · · · < ik such that, for all x, y, f (ix) < f (iy) if and only if π(x) < π(y); otherwise, f is said to be π-free. In this work, we consider the problem of testing for π-freeness with one-sided error, continuing the investigation of [<PERSON> et al., SODA'17].We demonstrate a surprising behavior for non-adaptive tests with one-sided error: While a trivial sampling-based approach yields an ε-test for π-freeness making Θ(ε–1/kn1–1/k) queries, our lower bounds imply that this is almost optimal for most permutations! Specifically, for most permutations π of length k, any non-adaptive one-sided ε-test requires ε–1/(k–Θ(1))n1–1/(k–Θ(1)) queries; furthermore, the permutations that are hardest to test require Θ(ε–1/(k–1)n1–1/(k–1)) queries, which is tight in n and ε.Additionally, we show two hierarchical behaviors here. First, for any k and l ≤ k – 1, there exists some π of length k that requires non-adaptive queries. Second, we show an adaptivity hierarchy for π = (1, 3, 2) by proving upper and lower bounds for (one- and two-sided) testing of π-freeness with r rounds of adaptivity. The results answer open questions of <PERSON> et al. and [<PERSON><PERSON> and <PERSON>, CCC'17].", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.137"}, {"primary_key": "3510057", "vector": [], "sparse_vector": [], "title": "Comparing mixing times on sparse random graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "It is natural to expect that nonbacktracking random walk will mix faster than simple random walks, but so far this has only been proved in regular graphs. To analyze typical irregular graphs, let G be a random graph on n vertices with minimum degree 3 and a degree distribution that has exponential tails. We determine the precise worst-case mixing time for simple random walk on G, and show that, with high probability, it exhibits cutoff at time h–1 log n, where h is the asymptotic entropy for simple random walk on a Galton-Watson tree that approximates G locally. (Previously this was only known for typical starting points.) Furthermore, we show this asymptotic mixing time is strictly larger than the mixing time of nonbacktracking walk, via a delicate comparison of entropies on the Galton-Watson tree.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.113"}, {"primary_key": "3510058", "vector": [], "sparse_vector": [], "title": "Estimating graph parameters via random walks with restarts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we discuss the problem of estimating graph parameters from a random walk with restarts. In this setting, an algorithm observes the trajectory of a random walk over an unknown graph G, starting from a vertex x. The algorithm also sees the degrees along the trajectory. The only other power that the algorithm has is to request that the random walk be reset to its initial state at any given time, based on what it has seen so far. Our main results are as follows. For regular graphs G, one can estimate the number of vertices nG and the ℓ2 mixing time of G from x in steps, where is the uniform mixing time of the random walk on G. The algorithm is based on the number of intersections of random walk paths X, Y, ie. the number of times (t, s) such that Xt = Ys. Our method improves on previous methods by various authors which only consider collisions (ie. times t with Xt = Yt). We also show that the time complexity of our algorithm is optimal (up to log factors) for 3-regular graphs with prescribed mixing times. For general graphs, we adapt the intersections algorithm to compute the number of edges mG and the ℓ2 mixing time from the starting vertex x in steps. Under mild additional assumptions (which hold e.g. for sparse graphs) the number of vertices can also be estimated by this time. Finally, we show that these algorithms, which may take sublinear time, have a fundamental limitation: it is not possible to devise a sublinear stopping time at which one can be reasonably sure that our parameters are well estimated. On the other hand, we show that, given either ma or the mixing time of G, we can compute the \"other parameter\" with a self-stopping algorithm.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.111"}, {"primary_key": "3510059", "vector": [], "sparse_vector": [], "title": "A tight -approximation for Linear 3-Cut.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We investigate the approximability of the linear 3-cut problem in directed graphs, which is the simplest unsolved case of the linear k-cut problem. The input here is a directed graph D = (V, E) with node weights and three specified terminal nodes s,r,t ∊ V, and the goal is to find a minimum weight subset of non-terminal nodes whose removal ensures that s cannot reach r and t, and r cannot reach t. The problem is approximation-equivalent to the problem of blocking rooted in- and out-arborescences, and it also has applications in network coding and security. The approximability of linear 3-cut has been wide open until now: the best known lower bound under the Unique Games Conjecture (UGC) was 4/3, while the best known upper bound was 2 using a trivial algorithm. In this work we completely close this gap: we present a -approximation algorithm and show that this factor is tight assuming UGC. Our contributions are twofold: (1) we analyze a natural two-step deterministic rounding scheme through the lens of a single-step randomized rounding scheme with non-trivial distributions, and (2) we construct integrality gap instances that meet the upper bound of . Our gap instances can be viewed as a weighted graph sequence converging to a \"graph limit structure\".", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.92"}, {"primary_key": "3510060", "vector": [], "sparse_vector": [], "title": "Tight Bounds for Coalescing-Branching Random Walks on Regular Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "A Coalescing-Branching Random Walk (CoBra) is a natural extension to the standard random walk on a graph. The process starts with one pebble at an arbitrary node. In each round of the process every pebble splits into k pebbles, which are sent to k random neighbors. At the end of the round all pebbles at the same node coalesce into a single pebble. The process is also similar to randomized rumor spreading, with each informed node pushing the rumor to k random neighbors each time it receives a copy of the rumor. Besides its mathematical interest, this process is relevant as an information dissemination primitive and a basic model for the spread of epidemics.We study the cover time of CoBra walks, which is the time until each node has seen at least one pebble. Our main result is a bound of O (φ–1 log n) rounds with high probability on the cover time of a CoBra walk with k = 2 on any regular graph with n nodes and conductance φ. This bound improves upon all previous bounds in terms of graph expansion parameters (<PERSON><PERSON> et al. [13], <PERSON><PERSON><PERSON> et al. [27], <PERSON> et al. [8, 9]). Moreover, we show that for any connected regular graph the cover time is O (n log n) with high probability, independently of the expansion. Both bounds are asymptotically tight.Since our bounds coincide with the worst-case time bounds for Push rumor spreading on regular graphs until all nodes are informed, this raises the question whether CoBra walks and Push rumor spreading perform similarly in general. We answer this negatively by separating the cover time of Co<PERSON>ra walks and the rumor spreading time of Push by a super-polylogarithmic factor on a family of tree-like regular graphs.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.112"}, {"primary_key": "3510062", "vector": [], "sparse_vector": [], "title": "Incremental Topological Sort and Cycle Detection in Expected Total Time.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In the incremental cycle detection problem edges are inserted to a directed graph (initially empty) and the algorithm has to report once a directed cycle is formed in the graph. A closely related problem to the incremental cycle detection is that of the incremental topological sort problem, in which edges are inserted to an acyclic graph and the algorithm has to maintain a valid topological sort on the vertices at all times.Both incremental cycle detection and incremental topological sort have a long history. The state of the art is a recent breakthrough of <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON> [TALG 2016], with two different algorithms with respective total update times of Õ(n2) and O(m · min{m1/2, n2/3}). The two algorithms work for both incremental cycle detection and incremental topological sort.In this paper we introduce a novel technique that allows us to improve upon the state of the art for a wide range of graph sparsity. Our algorithms has a total expected update time of for both the incremental cycle detection and the topological sort problems.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.2"}, {"primary_key": "3510063", "vector": [], "sparse_vector": [], "title": "Online Bipartite Matching with Amortized Replacements.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In the online bipartite matching problem with replacements, all the vertices on one side of the bipartition are given, and the vertices on the other side arrive one by one with all their incident edges. The goal is to maintain a maximum matching while minimizing the number of changes (replacements) to the matching. We show that the greedy algorithm that always takes the shortest augmenting path from the newly inserted vertex (denoted the SAP protocol) uses at most amortized O(log2 n) replacements per insertion, where n is the total number of vertices inserted. This is the first analysis to achieve a polylogarithmic number of replacements for any replacement strategy, almost matching the Ω(log n) lower bound. The previous best strategy known achieved amortized O(√n) replacements [<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, FOCS 2014]. For the SAP protocol in particular, nothing better than then trivial O(n) bound was known except in special cases. Our analysis immediately implies the same upper bound of O(log2 n) reassignments for the capacitated assignment problem, where each vertex on the static side of the bipartition is initialized with the capacity to serve a number of vertices. We also analyze the problem of minimizing the maximum server load. We show that if the final graph has maximum server load L, then the SAP protocol makes amortized O(min{L log2 n, √n log n}) reassignments. We also show that this is close to tight because Ω(min{L,√n}) reassignments can be necessary.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.61"}, {"primary_key": "3510064", "vector": [], "sparse_vector": [], "title": "Near-optimal approximation algorithm for simultaneous Max-Cut.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Swas<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the simultaneous Max-Cut problem, we are given k weighted graphs on the same set of n vertices, and the goal is to find a cut of the vertex set so that the minimum, over the k graphs, of the cut value is as large as possible. Previous work [BKS15] gave a polynomial time algorithm which achieved an approximation factor of 1/2 – o(1) for this problem (and an approximation factor of 1/2 + εk in the unweighted case, where εk → 0 as k → ∞).In this work, we give a polynomial time approximation algorithm for simultaneous Max-Cut with an approximation factor of 0.8780 (for all constant k). The natural SDP formulation for simultaneous Max-Cut was shown to have an integrality gap of 1/2 + εk in [BKS15]. In achieving the better approximation guarantee, we use a stronger Sum-of-Squares hierarchy SDP relaxation and a rounding algorithm based on <PERSON><PERSON><PERSON><PERSON><PERSON> [RT12], in addition to techniques from [BKS15].", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.93"}, {"primary_key": "3510065", "vector": [], "sparse_vector": [], "title": "Dynamic Algorithms for Graph Coloring.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Danupon <PERSON>"], "summary": "We design fast dynamic algorithms for proper vertex and edge colorings in a graph undergoing edge insertions and deletions. In the static setting, there are simple linear time algorithms for (Δ + 1)- vertex coloring and (2Δ – 1)-edge coloring in a graph with maximum degree Δ. It is natural to ask if we can efficiently maintain such colorings in the dynamic setting as well. We get the following three results. (1) We present a randomized algorithm which maintains a (Δ + 1)-vertex coloring with O(log Δ) expected amortized update time. (2) We present a deterministic algorithm which maintains a (1 + o(1)Δ-vertex coloring with O(polylog Δ) amortized update time. (3) We present a simple, deterministic algorithm which maintains a (2Δ – 1)-edge coloring with O(log Δ) worst-case update time. This improves the recent O(Δ)-edge coloring algorithm with worst-case update time [4].", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.1"}, {"primary_key": "3510066", "vector": [], "sparse_vector": [], "title": "A o(d) · polylog n Monotonicity Tester for Boolean Functions over the Hypergrid [n]d.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study monotonicity testing of Boolean functions over the hypergrid [n]d and design a non-adaptive tester with 1-sided error whose query complexity is Õ(d5/6). poly(log n, 1/ε). Previous to our work, the best known testers had query complexity linear in d but independent of n. We improve upon these testers as long as n = 2do(1).To obtain our results, we work with what we call the augmented hypergrid, which adds extra edges to the hypergrid. Our main technical contribution is a Margulis-style isoperimetric result for the augmented hypergrid, and our tester, like previous testers for the hypercube domain, performs directed random walks on this structure.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.139"}, {"primary_key": "3510067", "vector": [], "sparse_vector": [], "title": "Tolerant Junta Testing and the Connection to Submodular Optimization and Function Isomorphism.", "authors": ["<PERSON>", "Clément L. <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The function f : {– 1, 1}n → {– 1, 1} is a k-junta if it depends on at most k of its variables. We consider the problem of tolerant testing of k-juntas, where the testing algorithm must accept any function that is ∊-close to some k-junta and reject any function that is e′-far from every k′-junta for some ∊′ = O(∊) and k′ = O(k).Our first result is an algorithm that solves this problem with query complexity polynomial in k and 1/∊. This result is obtained via a new polynomial-time approximation algorithm for submodular function minimization (SFM) under large cardinality constraints, which holds even when only given an approximate oracle access to the function.Our second result considers the case where k′ = k. We show how to obtain a smooth tradeoff between the amount of tolerance and the query complexity in this setting. Specifically, we design an algorithm that given ρ ∊ (0, 1/2) accepts any function that is -close to some k-junta and rejects any function that is ∊-far from every k-junta. The query complexity of the algorithm is .Finally, we show how to apply the second result to the problem of tolerant isomorphism testing between two unknown Boolean functions f and g. We give an algorithm for this problem whose query complexity only depends on the (unknown) smallest k such that either f or g is close to being a k-junta.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.138"}, {"primary_key": "3510068", "vector": [], "sparse_vector": [], "title": "Spatial Mixing and Non-local Markov chains.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider spin systems with nearest-neighbor interactions on an n-vertex d-dimensional cube of the integer lattice graph ℤd. We study the effects that exponential decay with distance of spin correlations, specifically the strong spatial mixing condition (SSM), has on the rate of convergence to equilibrium of non-local Markov chains. We prove that SSM implies O(log n) mixing of a block dynamics whose steps can be implemented efficiently. We then develop a methodology, consisting of several new comparison inequalities concerning various block dynamics, that allow us to extend this result to other non-local dynamics. As a first application of our method we prove that, if SSM holds, then the relaxation time (i.e., the inverse spectral gap) of general block dynamics is O(r), where r is the number of blocks. A second application of our technology concerns the Swendsen-Wang dynamics for the ferromagnetic Ising and Potts models. We show that SSM implies an O(1) bound for the relaxation time. As a by-product of this implication we observe that the relaxation time of the Swendsen-Wang dynamics in square boxes of ℤ2 is O(1) throughout the subcritical regime of the q-state Potts model, for all q ≥ 2. We also prove that for monotone spin systems SSM implies that the mixing time of systematic scan dynamics is O(log n(log log n)2). Systematic scan dynamics are widely employed in practice but have proved hard to analyze. Our proofs use a variety of techniques for the analysis of Markov chains including coupling, functional analysis and linear algebra.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.128"}, {"primary_key": "3510069", "vector": [], "sparse_vector": [], "title": "Optimal streaming and tracking distinct elements with high probability.", "authors": ["Jaroslaw Blasiok"], "summary": "The distinct elements problem is one of the fundamental problems in streaming algorithms — given a stream of integers in the range {1, … n}, we wish to provide a (1+ε) approximation of the number of distinct elements in the input. After a long line of research optimal solution for this problem with constant probability of success, using bits of space, was given by <PERSON>, <PERSON> and <PERSON> in [KNW10].The standard approach used in order to achieve low failure probability δ, is to take a median of 1g δ–1 parallel repetitions of the original algorithm. We show that such a multiplicative space blow-up is unnecessary: we provide an optimal algorithm using bits of space — matching known lower bounds for this problem. That is, the lg δ–1 factor does not multiply the lg n term. This settles completely the space complexity of the distinct elements problem with respect to all standard parameters.Recently some attention in streaming algorithms has turned into continuously reporting the estimate of the statistic of interest, as opposed to reporting it only at the end of the stream. In this scenario, we want an algorithm which provides a (1 + ε) multiplicative approximation of the number of distinct elements at all times with probability 1 – δ, we call this strong tracking or continuous monitoring. The traditional way to achieve this kind of guarantee is to use a low failure probability algorithm, and union bound over carefully chosen subset of positions — this method, using as black-box the optimal streaming algorithm for distinct elements with low failure probability, would require bits of space. We show that this approach can be improved upon: we propose a stronger analysis of the algorithm, with space complexity .Finally, we show the matching lower bound for the strong tracking of the number of distinct elements with accuracy (1 + ε), proving optimality of our algorithm in terms of space usage.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.156"}, {"primary_key": "3510070", "vector": [], "sparse_vector": [], "title": "Optimal Vertex Fault Tolerant Spanners (for fixed stretch).", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Virginia Vassilevska Williams"], "summary": "A k-spanner of a graph G is a sparse subgraph H whose shortest path distances match those of G up to a multiplicative error k. In this paper we study spanners that are resistant to faults. A subgraph H ⊆ G is an f vertex fault tolerant (VFT) k-spanner if H \\ F is a k-spanner of G \\ F for any small set F of f vertices that might \"fail.\" One of the main questions in the area is: what is the minimum size of an f fault tolerant k-spanner that holds for all n node graphs (as a function of f, k and n)? This question was first studied in the context of geometric graphs [<PERSON><PERSON><PERSON><PERSON> et al. STOC '98, <PERSON><PERSON><PERSON><PERSON> and <PERSON> '03] and has more recently been considered in general undirected graphs [Chechik et al. STOC '09, <PERSON><PERSON> and Krauthgamer PODC '11].In this paper, we settle the question of the optimal size of a VFT spanner, in the setting where the stretch factor k is fixed. Specifically, we prove that every (undirected, possibly weighted) n-node graph G has a (2k – 1)-spanner resilient to f vertex faults with Ok (f1–1/kn1+1/k) edges, and this is fully optimal (unless the famous Erdös Girth Conjecture is false). Our lower bound even generalizes to imply that no data structure capable of approximating distG\\F (s, t) similarly can beat the space usage of our spanner in the worst case. To the best of our knowledge, this is the first instance in fault tolerant network design in which introducing fault tolerance to the structure increases the size of the (non-FT) structure by a sublinear factor in f. Another advantage of this result is that our spanners are constructed by a very natural and simple greedy algorithm, which is the obvious extension of the standard greedy algorithm used to build spanners in the non-faulty setting.We also consider the edge fault tolerant (EFT) model, defined analogously with edge failures rather than vertex failures. We show that the same spanner upper bound applies in this setting. Our data structure lower bound extends to the case k = 2 (and hence we close the EFT problem for 3-approximations), but it falls to D(f 1/2-1/(2k) · n1+1/k) for k > 3. We leave it as an open problem to close this gap.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.123"}, {"primary_key": "3510072", "vector": [], "sparse_vector": [], "title": "Approximating Edit Distance in Truly Subquadratic Time: Quantum and MapReduce.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The edit distance between two strings is defined as the smallest number of insertions, deletions, and substitutions that need to be made to transform one of the strings to another one. Approximating edit distance in subquadratic time is \"one of the biggest unsolved problems in the field of combinatorial pattern matching\" [21]. Our main result is a quantum constant approximation algorithm for computing the edit distance in truly subquadratic time. More precisely, we give an O(n1.858) quantum algorithm that approximates the edit distance within a factor of 7. We further extend this result to an O(n1.781) quantum algorithm that approximates the edit distance within a larger constant factor.Our solutions are based on a framework for approximating edit distance in parallel settings. This framework requires as black box an algorithm that computes the distances of several smaller strings all at once. For a quantum algorithm, we reduce the black box to metric estimation and provide efficient algorithms for approximating it. We further show that this framework enables us to approximate edit distance in distributed settings. To this end, we provide a MapReduce algorithm to approximate edit distance within a factor of 3, with sublinearly many machines and sublinear memory. Also, our algorithm runs in a logarithmic number of rounds.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.76"}, {"primary_key": "3510073", "vector": [], "sparse_vector": [], "title": "Promise Constraint Satisfaction: Structure Theory and a Symmetric Boolean Dichotomy.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "A classic result of <PERSON><PERSON><PERSON><PERSON> [<PERSON><PERSON>, 1978] classifies all constraint satisfaction problems (CSPs) over the Boolean domain to be either in P or NP-hard. This paper considers a promise-problem variant of CSPs called PCSPs. Many problems such as approximate graph and hypergraph coloring, the (2 + ∊)-SAT problem due to <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [SIAM Journal on Computing, 2017], and the digraph homomorphism problem can be placed in this framework.This paper is motivated by the pursuit of understanding the computational complexity of Boolean PCSPs, determining which PCSPs are polynomial-time tractable or NP-hard. As our main result, we show that PCSPs exhibits a dichotomy (it is either polynomial-time tractable or NP-hard) when the clauses are symmetric and allow for negations of variables. In particular, we show that every such polynomial-time tractable instance can be solved via either Gaussian elimination over F2 or a linear programming relaxation. We achieve our dichotomy theorem by extending the weak polymorphism framework of AGH which itself is a generalization of the algebraic approach used by polymorphisms to study CSPs. In both the algorithm and hardness portions of our proof, we incorporate new ideas and techniques not utilized in the CSP case.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.117"}, {"primary_key": "3510074", "vector": [], "sparse_vector": [], "title": "On Simultaneous Two-player Combinatorial Auctions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the following communication problem: <PERSON> and <PERSON> each have some valuation functions υ1(·) and υ2(·) over subsets of m items, and their goal is to partition the items into S, in a way that maximizes the welfare, . We study both the allocation problem, which asks for a welfare-maximizing partition and the decision problem, which asks whether or not there exists a partition guaranteeing certain welfare, for binary XOS valuations. For interactive protocols with poly(m) communication, a tight 3/4-approximation is known for both [29, 23].For interactive protocols, the allocation problem is provably harder than the decision problem: any solution to the allocation problem implies a solution to the decision problem with one additional round and log m additional bits of communication via a trivial reduction. Surprisingly, the allocation problem is provably easier for simultaneous protocols. Specifically, we show:•There exists a simultaneous, randomized protocol with polynomial communication that selects a partition whose expected welfare is at least 3/4 of the optimum. This matches the guarantee of the best interactive, randomized protocol with polynomial communication.•For all ε > 0, any simultaneous, randomized protocol that decides whether the welfare of the optimal partition is ≥ 1 or ≤ 3/4 – 1/108 + ε correctly with probability > 1/2 + 1/poly(m) requires exponential communication. This provides a separation between the attainable approximation guarantees via interactive (3/4) versus simultaneous (≤ 3/4 – 1/108) protocols with polynomial communication.In other words, this trivial reduction from decision to allocation problems provably requires the extra round of communication. We further discuss the implications of our results for the design of truthful combinatorial auctions in general, and extensions to general XOS valuations. In particular, our protocol for the allocation problem implies a new style of truthful mechanisms.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.146"}, {"primary_key": "3510075", "vector": [], "sparse_vector": [], "title": "Tree Edit Distance Cannot be Computed in Strongly Subcubic Time (unless APSP can).", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The edit distance between two rooted ordered trees with n nodes labeled from an alphabet Σ is the minimum cost of transforming one tree into the other by a sequence of elementary operations consisting of deleting and relabeling existing nodes, as well as inserting new nodes. Tree edit distance is a well known generalization of string edit distance. The fastest known algorithm for tree edit distance runs in cubic O(n3) time and is based on a similar dynamic programming solution as string edit distance. In this paper we show that a truly subcubic O(n3–ε) time algorithm for tree edit distance is unlikely: For |Σ| = Ω(n), a truly subcubic algorithm for tree edit distance implies a truly subcubic algorithm for the all pairs shortest paths problem. For |Σ| = O(1), a truly subcubic algorithm for tree edit distance implies an O(nk–ε) algorithm for finding a maximum weight k-clique.Thus, while in terms of upper bounds string edit distance and tree edit distance are highly related, in terms of lower bounds string edit distance exhibits the hardness of the strong exponential time hypothesis [Backurs, Indyk STOC'15] whereas tree edit distance exhibits the hardness of all pairs shortest paths. Our result provides a matching conditional lower bound for one of the last remaining classic dynamic programming problems.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.77"}, {"primary_key": "3510076", "vector": [], "sparse_vector": [], "title": "Multivariate Fine-Grained Complexity of Longest Common Subsequence.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We revisit the classic combinatorial pattern matching problem of finding a longest common subsequence (LCS). For strings $x$ and $y$ of length $n$, a textbook algorithm solves LCS in time $O(n^2)$, but although much effort has been spent, no $O(n^{2-\\varepsilon})$-time algorithm is known. Recent work indeed shows that such an algorithm would refute the Strong Exponential Time Hypothesis (SETH) [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> + <PERSON>, <PERSON>\\\"unnemann FOCS'15]. Despite the quadratic-time barrier, for over 40 years an enduring scientific interest continued to produce fast algorithms for LCS and its variations. Particular attention was put into identifying and exploiting input parameters that yield strongly subquadratic time algorithms for special cases of interest, e.g., differential file comparison. This line of research was successfully pursued until 1990, at which time significant improvements came to a halt. In this paper, using the lens of fine-grained complexity, our goal is to (1) justify the lack of further improvements and (2) determine whether some special cases of LCS admit faster algorithms than currently known. To this end, we provide a systematic study of the multivariate complexity of LCS, taking into account all parameters previously discussed in the literature: the input size $n:=\\max\\{|x|,|y|\\}$, the length of the shorter string $m:=\\min\\{|x|,|y|\\}$, the length $L$ of an LCS of $x$ and $y$, the numbers of deletions $\\delta := m-L$ and $\\Delta := n-L$, the alphabet size, as well as the numbers of matching pairs $M$ and dominant pairs $d$. For any class of instances defined by fixing each parameter individually to a polynomial in terms of the input size, we prove a SETH-based lower bound matching one of three known algorithms. Specifically, we determine the optimal running time for LCS under SETH as $(n+\\min\\{d, \\delta \\Delta, \\delta m\\})^{1\\pm o(1)}$. [...]", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.79"}, {"primary_key": "3510077", "vector": [], "sparse_vector": [], "title": "Algorithms to Approximate Column-Sparse Packing Problems.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Column-sparse packing problems arise in several contexts in both deterministic and stochastic discrete optimization. We present two unifying ideas, (non-uniform) attenuation and multiple-chance algorithms, to obtain improved approximation algorithms for some well-known families of such problems. As three main examples, we attain the integrality gap, up to lower-order terms, for known LP relaxations for k-column sparse packing integer programs (<PERSON><PERSON> et al., Theory of Computing, 2012) and stochastic k-set packing (<PERSON><PERSON> et al., Algorithmica, 2012), and go \"half the remaining distance\" to optimal for a major integrality-gap conjecture of <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> on hypergraph matching (Combinatorica, 1993).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.22"}, {"primary_key": "3510078", "vector": [], "sparse_vector": [], "title": "Dichotomy for Real Holantc Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Holant problems capture a class of Sum-of-Product computations such as counting matchings. It is inspired by holographic algorithms and is equivalent to tensor networks, with counting CSP being a special case. A complexity classification for Holant problems is more difficult to prove, not only because it logically implies a classification for counting CSP, but also due to the deeper reason that there exist more intricate polynomial time tractable problems in the broader framework. We discover a new family of constraint functions ℒ which define polynomial time computable counting problems. These do not appear in counting CSP, and no newly discovered tractable constraints can be symmetric. It has a delicate support structure related to error-correcting codes. Local holographic transformations is fundamental in its tractability. We prove a complexity dichotomy theorem for all Holant problems defined by any real valued constraint function set on Boolean variables and contains two 0–1 pinning functions. Previously, dichotomy for the same framework was only known for symmetric constraint functions. The set ℒ supplies the last piece of tractability. We also prove a dichotomy for a variant of counting CSP as a technical component toward this Holant dichotomy.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.118"}, {"primary_key": "3510080", "vector": [], "sparse_vector": [], "title": "Lifting Linear Extension Complexity Bounds to the Mixed-Integer Setting.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mixed-integer mathematical programs are among the most commonly used models for a wide set of problems in Operations Research and related fields. However, there is still very little known about what can be expressed by small mixed-integer programs. In particular, prior to this work, it was open whether some classical problems, like the minimum odd-cut problem, can be expressed by a compact mixed-integer program with few (even constantly many) integer variables. This is in stark contrast to linear formulations, where recent breakthroughs in the field of extended formulations have shown that many polytopes associated to classical combinatorial optimization problems do not even admit approximate extended formulations of sub-exponential size.We provide a general framework for lifting inapproximability results of extended formulations to the setting of mixed-integer extended formulations, and obtain almost tight lower bounds on the number of integer variables needed to describe a variety of classical combinatorial optimization problems. Among the implications we obtain, we show that any mixed-integer extended formulation of sub-exponential size for the matching polytope, cut polytope, travelling salesman polytope or dominant of the odd-cut polytope, needs Ω(n / log n) many integer variables, where n is the number of vertices of the underlying graph. Conversely, the above-mentioned polyhedra admit polynomial-size mixed-integer formulations with only O(n) or O(n log n) (for the traveling salesman polytope) many integer variables.Our results build upon a new decomposition technique that, for any convex set C, allows for approximating any mixed-integer description of C by the intersection of C with the union of a small number of affine subspaces.MSC codesextension complexitymixed-integer programsextended formulations", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.51"}, {"primary_key": "3510082", "vector": [], "sparse_vector": [], "title": "On the complexity of optimal homotopies.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this article, we provide new structural results and algorithms for the Homotopy Height problem. In broad terms, this problem quantifies how much a curve on a surface needs to be stretched to sweep continuously between two positions. More precisely, given two homotopic curves γ1 and γ2 on a combinatorial (say, triangulated) surface, we investigate the problem of computing a homotopy between γ1 and γ2 where the length of the longest intermediate curve is minimized. Such optimal homotopies are relevant for a wide range of purposes, from very theoretical questions in quantitative homotopy theory to more practical applications such as similarity measures on meshes and graph searching problems.We prove that Homotopy Height is in the complexity class NP, and the corresponding exponential algorithm is the best one known for this problem. This result builds on a structural theorem on monotonicity of optimal homotopies, which is proved in a companion paper. Then we show that this problem encompasses the Homotopic Fréchet Distance problem which we therefore also establish to be in NP, answering a question which has previously been considered in several different settings. We also provide an O(log n)-approximation algorithm for Homotopy Height on surfaces by adapting an earlier algorithm of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON> in the planar setting.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.73"}, {"primary_key": "3510083", "vector": [], "sparse_vector": [], "title": "More Logarithmic-Factor Speedups for 3SUM, (median, +)-Convolution, and Some Geometric 3SUM-Hard Problems.", "authors": ["<PERSON>"], "summary": "We present an algorithm that solves the 3SUM problem for n real numbers in O((n2 / log2 n)(log log n)O(1)) time, improving previous solutions by about a logarithmic factor. Our framework for shaving off two logarithmic factors can be applied to other problems, such as (median,+)-convolution/matrix multiplication and algebraic generalizations of 3SUM. We also obtain the first subquadratic results on some 3SUM-hard problems in computational geometry, for example, deciding whether (the interiors of) a constant number of simple polygons have a common intersection.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.57"}, {"primary_key": "3510085", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>-Oblivious and Data-Oblivious Sorting and Applications.", "authors": ["T.<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Although external-memory sorting has been a classical algorithms abstraction and has been heavily studied in the literature, perhaps somewhat surprisingly, when data-obliviousness is a requirement, even very rudimentary questions remain open. Prior to our work, it is not even known how to construct a comparison-based, external-memory oblivious sorting algorithm that is optimal in IO-cost.We make a significant step forward in our understanding of external-memory, oblivious sorting algorithms. Not only do we construct a comparison-based, external-memory oblivious sorting algorithm that is optimal in IO-cost, our algorithm is also cache-agnostic in that the algorithm need not know the storage hierarchy's internal parameters such as the cache and cache-line sizes. Our result immediately implies a cache-agnostic ORAM construction whose asymptotic IO-cost matches the best known cache-aware scheme.Last but not the least, we propose and adopt a new and stronger security notion for external-memory, oblivious algorithms and argue that this new notion is desirable for resisting possible cache-timing attacks. Thus our work also lays a foundation for the study of oblivious algorithms in the cache-agnostic model.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.143"}, {"primary_key": "3510086", "vector": [], "sparse_vector": [], "title": "Hypergraph k-Cut in Randomized Polynomial Time.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the hypergraph k-cut problem, the input is a hypergraph, and the goal is to find a smallest subset of hyperedges whose removal ensures that the remaining hypergraph has at least k connected components. This problem is known to be at least as hard as the densest k-subgraph problem when k is part of the input (<PERSON><PERSON><PERSON><PERSON>, 2015). We present a randomized polynomial time algorithm to solve the hypergraph k-cut problem for constant k. Our algorithm solves the more general hedge k-cut problem when the subgraph induced by every hedge has a constant number of connected components. In the hedge k-cut problem, the input is a hedgegraph specified by a vertex set and a disjoint set of hedges, where each hedge is a subset of edges defined over the vertices. The goal is to find a smallest subset of hedges whose removal ensures that the number of connected components in the remaining underlying (multi-)graph is at least k. Our algorithm is based on random contractions akin to <PERSON><PERSON>'s min cut algorithm. Our main technical contribution is a distribution over the hedges (hyperedges) so that random contraction of hedges (hyperedges) chosen from the distribution succeeds in returning an optimum solution with large probability.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.94"}, {"primary_key": "3510087", "vector": [], "sparse_vector": [], "title": "Tightening Curves on Surfaces via Local Moves.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove new upper and lower bounds on the number of homotopy moves required to tighten a closed curve on a compact orientable surface (with or without boundary) as much as possible. First, we prove that Ω(n2) moves are required in the worst case to tighten a contractible closed curve on a surface with non-positive Euler characteristic, where n is the number of self-intersection points. Results of <PERSON><PERSON> and <PERSON> imply a matching O(n2) upper bound for contractible curves on orientable surfaces. Second, we prove that any closed curve on any orientable surface can be tightened as much as possible using at most O(n4) homotopy moves. Except for a few special cases, only naïve exponential upper bounds were previously known for this problem.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.8"}, {"primary_key": "3510088", "vector": [], "sparse_vector": [], "title": "The Complexity of Distributed Edge Coloring with Small Palettes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> He", "Wenzheng Li", "<PERSON>", "<PERSON><PERSON>"], "summary": "The complexity of distributed edge coloring depends heavily on the palette size as a function of the maximum degree Δ. In this paper we explore the complexity of edge coloring in the LOCAL model in different palette size regimes. Our results are as follows.•We simplify the round elimination technique of <PERSON> et al. [9] and prove that (2Δ – 2)-edge coloring requires Ω(logΔ log n) time w.h.p. and Ω(logΔ n) time deterministically, even on trees. The simplified technique is based on two ideas: the notion of an irregular running time (in which network components terminate the algorithm at prescribed, but irregular times) and some general observations that transform weak lower bounds into stronger ones.•We give a randomized edge coloring algorithm that can use palette sizes as small as , which is a natural barrier for randomized approaches. The running time of the algorithm is at most O(log Δ · TLLL), where TLLL is the complexity of a permissive version of the constructive Lovász local lemma.•We develop a new distributed Lovász local lemma algorithm for tree-structured dependency graphs, which leads to a (1 + ∊)Δ-edge coloring algorithm for trees running in O(log log n) time. This algorithm arises from two new results: a deterministic O(log n)-time LLL algorithm for tree-structured instances, and a randomized O(log log n)-time graph shattering method for breaking the dependency graph into independent O(log n)-size LLL instances.•A natural approach to computing (Δ + 1)-edge colorings (<PERSON><PERSON>'s theorem) is to extend partial colorings by iteratively re-coloring parts of the graph, e.g., via \"augmenting paths.\" We prove that this approach may be viable, but in the worst case requires recoloring subgraphs of diameter Ω(Δ log n). This stands in contrast to distributed algorithms for Brooks' theorem [32], which exploit the existence of O(logΔ n)-length augmenting paths.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.168"}, {"primary_key": "3510089", "vector": [], "sparse_vector": [], "title": "The Gotsman-Linial Conjecture is False.", "authors": ["<PERSON><PERSON>"], "summary": "In 1991, <PERSON> and <PERSON> conjectured that for all $n$ and $d$, the average sensitivity of a degree-$d$ polynomial threshold function on $n$ variables is maximized by the degree-$d$ symmetric polynomial which computes the parity function on the $d$ layers of the hypercube with Hamming weight closest to $n/2$. We refute the conjecture for almost all $d$ and for almost all $n$, and we confirm the conjecture in many of the remaining cases.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.45"}, {"primary_key": "3510090", "vector": [], "sparse_vector": [], "title": "Lower Bounds for Symbolic Computation on Graphs: Strongly Connected Components, Liveness, Safety, and Diameter.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A model of computation that is widely used in the formal analysis of reactive systems is symbolic algorithms. In this model the access to the input graph is restricted to consist of symbolic operations, which are expensive in comparison to the standard RAM operations. We give lower bounds on the number of symbolic operations for basic graph problems such as the computation of the strongly connected components and of the approximate diameter as well as for fundamental problems in model checking such as safety, liveness, and coliveness. Our lower bounds are linear in the number of vertices of the graph, even for constant-diameter graphs. For none of these problems lower bounds on the number of symbolic operations were known before. The lower bounds show an interesting separation of these problems from the reachability problem, which can be solved with O(D) symbolic operations, where D is the diameter of the graph.Additionally we present an approximation algorithm for the graph diameter which requires symbolic steps to achieve a (1 + ∊)-approximation for any constant ∊ > 0. This compares to O(n · D) symbolic steps for the (naive) exact algorithm and O(D) symbolic steps for a 2-approximation. Finally we also give a refined analysis of the strongly connected components algorithms of [15], showing that it uses an optimal number of symbolic steps that is proportional to the sum of the diameters of the strongly connected components.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.151"}, {"primary_key": "3510092", "vector": [], "sparse_vector": [], "title": "Randomized MWU for Positive LPs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We describe and analyze a simple randomized multiplicative weight update (MWU) based algorithm for approximately solving positive linear programming problems, in particular, mixed packing and covering LPs.Given m explicit linear packing and covering constraints over n variables specified by N nonzero entries, <PERSON> [36] gave a deterministic algorithm returning an (1 + ε)-approximate feasible solution (if a feasible solution exists) in Õ(N/ε2) time. We show that a simple randomized implementation matches this bound, and that randomization can be further exploited to improve the running time to Õ(N/ε + m/ε2 + n/ε3) (both with high probability). For instances that are not very sparse (with at least ῶ(1/ε) nonzeroes per column on average), this improves the running time of Õ(N/ε2). The randomized algorithm also gives improved running times for some implicitly defined problems that arise in combinatorial and geometric optimization.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.25"}, {"primary_key": "3510094", "vector": [], "sparse_vector": [], "title": "A Nearly Instance Optimal Algorithm for Top-k Ranking under the Multinomial Logit Model.", "authors": ["<PERSON>", "<PERSON>zhi Li", "<PERSON><PERSON><PERSON>"], "summary": "We study the active learning problem of top-k ranking from multi-wise comparisons under the popular multinomial logit model. Our goal is to identify the top-k items with high probability by adaptively querying sets for comparisons and observing the noisy output of the most preferred item from each comparison. To achieve this goal, we design a new active ranking algorithm without using any information about the underlying items' preference scores. We also establish a matching lower bound on the sample complexity even when the set of preference scores is given to the algorithm. These two results together show that the proposed algorithm is nearly instance optimal (similar to instance optimal [12], but up to polylog factors). Our work extends the existing literature on rank aggregation in three directions. First, instead of studying a static problem with fixed data, we investigate the top-k ranking problem in an active learning setting. Second, we show our algorithm is nearly instance optimal, which is a much stronger theoretical guarantee. Finally, we extend the pairwise comparison to the multi-wise comparison, which has not been fully explored in ranking literature.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.160"}, {"primary_key": "3510095", "vector": [], "sparse_vector": [], "title": "Covering a tree with rooted subtrees - parameterized and approximation algorithms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the multiple traveling salesman problem on a weighted tree. In this problem there are m salesmen located at the root initially. Each of them will visit a subset of vertices and return to the root. The goal is to assign a tour to every salesman such that every vertex is visited and the longest tour among all salesmen is minimized. The problem is equivalent to the subtree cover problem, in which we cover a tree with rooted subtrees such that the weight of the maximum weighted subtree is minimized. The classical machine scheduling problem can be viewed as a special case of our problem when the given tree is a star. We provide approximation and parameterized algorithms for this problem. We first present a PTAS (Polynomial Time Approximation Scheme). We then observe that, the problem remains NP-hard even if tree height and edge weight are constant, and present an FPT algorithm for this problem parameterized by the largest tour length. To achieve the FPT algorithm, we first formulate the problem as an integer linear program having a certain \"tree-fold\" structure. Then we show that an ILP with such a structure is FPT, which is a generalization of an earlier FPT result for n-fold integer programming by <PERSON>, <PERSON> and <PERSON> [5]. This extension of n-fold ILP may be of independent interest.MSC codesApproximation schemesFixed Parameter TractableInteger ProgrammingScheduling", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.178"}, {"primary_key": "3510096", "vector": [], "sparse_vector": [], "title": "On the Complexity of Simple and Optimal Deterministic Mechanisms for an Additive Buyer.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that the Revenue-Optimal Deterministic Mechanism Design problem for a single additive buyer is #P-hard, even when the distributions have support size 2 for each item and, more importantly, even when the optimal solution is guaranteed to be of a very simple kind: the seller picks a price for each individual item and a price for the grand bundle of all the items; the buyer can purchase either the grand bundle at its given price or any subset of items at their total individual prices. The following problems are also #P-hard, as immediate corollaries of the proof:1.determining if individual item pricing is optimal for a given instance,2.determining if grand bundle pricing is optimal, and3.computing the optimal (deterministic) revenue.On the positive side, we show that when the distributions are i.i.d. with support size 2, the optimal revenue obtainable by any mechanism, even a randomized one, can be achieved by a simple solution of the above kind (individual item pricing with a discounted price for the grand bundle) and furthermore, it can be computed in polynomial time. The problem can be solved in polynomial time too when the number of items is constant.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.133"}, {"primary_key": "3510097", "vector": [], "sparse_vector": [], "title": "Steiner Point Removal - Distant Terminals Don&apos;t (Really) Bother.", "authors": ["<PERSON>"], "summary": "Given a weighted graph G = (V, E, w) with a set of k terminals T ⊂ V, the Steiner Point Removal problem seeks for a minor of the graph with vertex set T, such that the distance between every pair of terminals is preserved within a small multiplicative distortion. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [13] used a ball-growing algorithm to show that the distortion is at most for general graphs.In this paper, we improve the distortion bound to . The improvement is achieved based on a known algorithm that constructs terminal-distance exact-preservation minor with (which is independent of |V|) vertices, and also two tail bounds on the sums of independent exponential random variables, which allow us to show that it is unlikely for a non-terminal being contracted to a distant terminal.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.89"}, {"primary_key": "3510098", "vector": [], "sparse_vector": [], "title": "Discrete Choice, Permutations, and Reconstruction.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper we study the well-known family of Random Utility Models, developed over 50 years ago to codify rational user behavior in choosing one item from a finite set of options. In this setting each user draws i.i.d. from some distribution a utility function mapping each item in the universe to a real-valued utility. The user is then offered a subset of the items, and selects the one of maximum utility. A Max-Dist oracle for this choice model takes any subset of items and returns the probability (over the distribution of utility functions) that each will be selected. A discrete choice algorithm, given access to a Max-Dist oracle, must return a function that approximates the oracle.We show three primary results. First, we show that any algorithm exactly reproducing the oracle must make exponentially many queries. Second, we show an equivalent representation of the distribution over utility functions, based on permutations, and show that if this distribution has support size k, then it is possible to approximate the oracle using O(nk) queries. Finally, we consider settings in which the subset of items is always small. We give an algorithm that makes less than n(1–∊/2)K queries, each to sets of size at most (1–∊/2)K, in order to approximate the Max-Dist oracle on every set of size |T| ≤ K with statistical error at most ∊. In contrast, we show that any algorithm that queries for subsets of size must make maximal statistical error on some large sets.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.38"}, {"primary_key": "3510099", "vector": [], "sparse_vector": [], "title": "Persistent Path Homology of Directed Networks.", "authors": ["<PERSON><PERSON>", "Facundo <PERSON>"], "summary": "While standard persistent homology has been successful in extracting information from metric datasets, its applicability to more general data, e.g. directed networks, is hindered by its natural insensitivity to asymmetry. We extend a construction of homology of digraphs due to <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> to the persistent framework. The result, which we call persistent path homology or PPH, encodes a rich level of detail about the asymmetric structure of the input directed network. For example, we prove that PPH identifies a class of directed cyclic networks as directed analogues of the circle. In general, PPH produces signatures that differ from natural extensions of Rips or Čech persistence to the directed setting, but we prove that PPH agrees with Čech persistence on symmetric spaces. Additionally, we prove that PPH agrees with Čech persistence on directed networks satisfying a local condition that we call square-freeness. We prove stability of PPH by utilizing a separate theory of homotopy of digraphs that is compatible with path homology. Finally, we study computational aspects of PPH, and derive an algorithm showing that over field coefficients, computing PPH requires the same worst case running time as standard persistent homology.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.75"}, {"primary_key": "3510100", "vector": [], "sparse_vector": [], "title": "A submodular measure and approximate Gomory-<PERSON> theorem for packing odd trails.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Motivated by a problem about totally odd immersions of graphs, we define the odd edge-connectivity λo(u, υ) as the maximum number of edge-disjoint trails of odd length from u to υ. It was recently discovered that λo(u, υ) can be approximated up to a constant multiplicative factor using the usual edge-connectivity between u and v and the minimum value of another parameter that measures \"how far from a bipartite graph\" the part of the graph around u and v is.In this paper, we formalize this second ingredient and call it the perimeter. We prove that perimeter is a submodular function on the vertex-sets of a graph. Using this fact, we obtain a version of the Gomory–Hu Theorem in which minimum edge-cuts are replaced by sets of minimum perimeter. We construct (in polynomial time) a rooted forest structure, analogous to the Gomory-Hu tree of a graph, which encodes a collection of minimum-perimeter vertex-sets. Although the classical Gomory-Hu Theorem extends to arbitrary symmetric submodular functions, our result is novel and indicates a possibility for further generalizations.These results have significant implications for the study of path and trail systems with parity constraints. We present two such applications: an efficient data structure for storing approximate odd edge-connectivities for all pairs of vertices in a graph, and a rough structure theorem for graphs with no \"totally odd\" immersion of a large complete graph.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.15"}, {"primary_key": "3510101", "vector": [], "sparse_vector": [], "title": "The Classical Complexity of <PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the classical complexity of the exact Boson Sampling problem where the objective is to produce provably correct random samples from a particular quantum mechanical distribution. The computational framework was proposed in STOC '11 by <PERSON><PERSON> and <PERSON><PERSON><PERSON> in 2011 as an attainable demonstration of 'quantum supremacy', that is a practical quantum computing experiment able to produce output at a speed beyond the reach of classical (that is non-quantum) computer hardware. Since its introduction Boson Sampling has been the subject of intense international research in the world of quantum computing. On the face of it, the problem is challenging for classical computation. <PERSON><PERSON> and <PERSON><PERSON><PERSON> show that exact Boson Sampling is not efficiently solvable by a classical computer unless P#P = BPPNP and the polynomial hierarchy collapses to the third level.The fastest known exact classical algorithm for the standard Boson Sampling problem requires time to produce samples for a system with input size n and m output modes, making it infeasible for anything but the smallest values of n and m. We give an algorithm that is much faster, running in time and additional space. The algorithm is simple to implement and has low constant factor overheads. As a consequence our classical algorithm is able to solve the exact Boson Sampling problem for system sizes far beyond current photonic quantum computing experimentation, thereby significantly reducing the likelihood of achieving near-term quantum supremacy in the context of Boson Sampling.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.10"}, {"primary_key": "3510102", "vector": [], "sparse_vector": [], "title": "A Fast Approximation Scheme for Low-Dimensional k-Means.", "authors": ["<PERSON>"], "summary": "We consider the popular k-means problem in ddimensional Euclidean space. Recently <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> [FOCS'16] and <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> [FOCS'16] showed that the standard local search algorithm yields a p1\"q-approximation in time pn kq1-\"Opdq, giving the first polynomial-time approximation scheme for the problem in low-dimensional Euclidean space. While local search achieves optimal approximation guarantees, it is not competitive with the state-of-the-art heuristics such as the famous kmeans++ and D2-sampling algorithms. In this paper, we aim at bridging the gap between theory and practice by giving a p1 \"q-approximation algorithm for low-dimensional k-means running in time nk plog nqpd\" 1qOpdq, and so matching the running time of the k-means++ and D2-sampling heuristics up to polylogarithmic factors. We speed-up the local search approach by making a non-standard use of randomized dissections that allows to find the best local move efficiently using a quite simple dynamic program. We hope that our techniques could help design better local search heuristics for geometric problems.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.29"}, {"primary_key": "3510103", "vector": [], "sparse_vector": [], "title": "Hierarchical Clustering: Objective Functions and Algorithms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Hierarchical clustering is a recursive partitioning of a dataset into clusters at an increasingly finer granularity. Motivated by the fact that most work on hierarchical clustering was based on providing algorithms, rather than optimizing a specific objective, [19] framed similarity-based hierarchical clustering as a combinatorial optimization problem, where a ‘good’ hierarchical clustering is one that minimizes some cost function. He showed that this cost function has certain desirable properties, such as in order to achieve optimal cost, disconnected components must be separated first and that in ‘structureless’ graphs, i.e., cliques, all clusterings achieve the same cost. We take an axiomatic approach to defining ‘good’ objective functions for both similarity and dissimilarity-based hierarchical clustering. We characterize a set of admissible objective functions (that includes the one introduced by <PERSON><PERSON><PERSON>) that have the property that when the input admits a ‘natural’ ground-truth hierarchical clustering, the ground-truth clustering has an optimal value. Equipped with a suitable objective function, we analyze the performance of practical algorithms, as well as develop better and faster algorithms for hierarchical clustering. For similarity-based hierarchical clustering, [19] showed that a simple recursive sparsest-cut based approach achieves an O(log3/2 n)-approximation on worst-case inputs. We give a more refined analysis of the algorithm and show that it in fact achieves an -approximation1. This improves upon the LP-based O(log n)-approximation of [33]. For dissimilarity-based hierarchical clustering, we show that the classic average-linkage algorithm gives a factor 2 approximation, and provide a simple and better algorithm that gives a factor 3/2 approximation. This aims at explaining the success of these heuristics in practice. Finally, we consider a ‘beyond-worst-case’ scenario through a generalisation of the stochastic block model for hierarchical clustering. We show that Dasgupta's cost function also has desirable properties for these inputs and we provide a simple algorithm that for graphs generated according to this model yields a 1 + o(1) factor approximation.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.26"}, {"primary_key": "3510104", "vector": [], "sparse_vector": [], "title": "The Bane of Low-Dimensionality Clustering.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we give a conditional lower bound of nω(k) on running time for the classic k-median and k-means clustering objectives (where n is the size of the input), even in low-dimensional Euclidean space of dimension four, assuming the Exponential Time Hypothesis (ETH). We also consider k-median (and k-means) with penalties where each point need not be assigned to a center, in which case it must pay a penalty, and extend our lower bound to at least three-dimensional Euclidean space. This stands in stark contrast to many other geometric problems such as the traveling salesman problem, or computing an independent set of unit spheres. While these problems benefit from the so-called (limited) blessing of dimensionality, as they can be solved in time nO(k1--1/d) or 2n1--1/d in d dimensions, our work shows that widely-used clustering objectives have a lower bound of nω(k), even in dimension four. We complete the picture by considering the two-dimensional case: we show that there is no algorithm that solves the penalized version in time less than [Equation], and provide a matching upper bound of [Equation]. The main tool we use to establish these lower bounds is the placement of points on the moment curve, which takes its inspiration from constructions of point sets yielding Delaunay complexes of high complexity.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.30"}, {"primary_key": "3510105", "vector": [], "sparse_vector": [], "title": "A Near-Linear Approximation Scheme for Multicuts of Embedded Graphs with a Fixed Number of Terminals.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "For an undirected edge-weighted graph $G$ and a set $R$ of pairs of vertices called pairs of terminals, a multicut is a set of edges such that removing these edges from $G$ disconnects each pair in $R$. We provide an algorithm computing a $(1+\\varepsilon)$-approximation of the minimum multicut of a graph $G$ in time $(g+t)^{(O(g+t)^3)}\\cdot(1/\\varepsilon)^{O(g+t)} \\cdot n \\log n$, where $g$ is the genus of $G$ and $t$ is the number of terminals. This result is tight in several aspects, as the minimum multicut problem is both APX-hard and W[1]-hard (parameterized by the number of terminals), even on planar graphs (equivalently, when $g=0$). In order to achieve this, our article leverages on a novel characterization of a minimum multicut as a family of Steiner trees in the universal cover of a surface on which $G$ is embedded. The algorithm heavily relies on topological techniques, and in particular on the use of homotopical tools and computations in covering spaces, which we blend with classic ideas stemming from approximation schemes for planar graphs and low-dimensional geometric inputs.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.95"}, {"primary_key": "3510107", "vector": [], "sparse_vector": [], "title": "Randomized Online Matching in Regular Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we study the classic online matching problem, introduced in the seminal work of <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (STOC 1990), in regular graphs. For such graphs, an optimal deterministic algorithm as well as efficient algorithms under stochastic input assumptions were known. In this work, we present a novel randomized algorithm with competitive ratio tending to one on this family of graphs, under adversarial arrival order. Our main contribution is a novel algorithm which achieves competitive ratio in expectation on d-regular graphs. In contrast, we show that all previously-studied online algorithms have competitive ratio strictly bounded away from one. Moreover, we show the convergence rate of our algorithm's competitive ratio to one is nearly tight, as no algorithm achieves competitive ratio better than . Finally, we show that our algorithm yields a similar competitive ratio with high probability, as well as guaranteeing each vertex a probability of being matched tending to one.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.62"}, {"primary_key": "3510108", "vector": [], "sparse_vector": [], "title": "Fully polynomial FPT algorithms for some classes of bounded clique-width graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recently, hardness results for problems in P were achieved using reasonable complexity-theoretic assumptions such as the Strong Exponential Time Hypothesis. According to these assumptions, many graph-theoretic problems do not admit truly subquadratic algorithms. A central technique used to tackle the difficulty of the above-mentioned problems is fixed-parameter algorithms with polynomial dependency in the fixed parameter (P-FPT). Applying this technique to clique-width, an important graph parameter, remained to be done. In this article, we study several graph-theoretic problems for which hardness results exist such as cycle problems, distance problems, and maximum matching. We give hardness results and P-FPT algorithms, using clique-width and some of its upper bounds as parameters. We believe that our most important result is an algorithm in O(k4 ⋅ n + m)-time for computing a maximum matching, where k is either the modular-width of the graph or the P4-sparseness. The latter generalizes many algorithms that have been introduced so far for specific subclasses such as cographs. Our algorithms are based on preprocessing methods using modular decomposition and split decomposition. Thus they can also be generalized to some graph classes with unbounded clique-width.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.176"}, {"primary_key": "3510109", "vector": [], "sparse_vector": [], "title": "A Tight Lower Bound for Counting Hamiltonian Cycles via Matrix Rank.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "For even k ∊ ℕ, the matchings connectivity matrix Mk is a binary matrix indexed by perfect matchings on k vertices; the entry at (M, M‘) is 1 iff M ∪ M‘ forms a single cycle. <PERSON><PERSON> et al. (STOC 2013) showed that the rank of Mk over ℤ is and used this to give an time algorithm for counting Hamiltonian cycles modulo 2 on graphs of pathwidth pw, carrying over to the decision problem via witness isolation. The same authors complemented their algorithm by an essentially tight lower bound under the Strong Exponential Time Hypothesis (SETH). This bound crucially relied on a large permutation submatrix within Mk, which enabled a “pattern propagation” commonly used in previous related lower bounds, as initiated by <PERSON><PERSON><PERSON><PERSON> et al. (SODA 2011).We present a new technique for a similar “pattern propagation” when only a black-box lower bound on the asymptotic rank of Mk is given; no stronger structural insights such as the existence of large permutation submatrices in Mk are needed. Given appropriate rank bounds, our technique yields lower bounds for counting Hamiltonian cycles (also modulo fixed primes p) parameterized by pathwidth.To apply this technique, we prove that the rank of Mk over the rationals is 4k/poly(k), using the representation theory of the symmetric group and various insights from algebraic combinatorics. We also show that the rank of Mk over ℤp is Ω(1.57k) for any prime p ≠ 2.Combining our rank bounds with the new pattern propagation technique, we show that Hamiltonian cycles cannot be counted in time O*((6 – ε)pw) for any ε > 0 unless <PERSON>TH fails. This bound is tight due to a O*(6pw) time algorithm by Bodlaender et al. (ICALP 2013). Under SETH, we also obtain that Hamiltonian cycles cannot be counted modulo primes p ≠ 2 in time O*(3.57pw), indicating that the modulus can affect the complexity in intricate ways.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.70"}, {"primary_key": "3510110", "vector": [], "sparse_vector": [], "title": "Fast, Deterministic and Sparse Dimensionality Reduction.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We provide a deterministic construction of the sparse <PERSON> transform of <PERSON> (J<PERSON> 2014) which runs, under a mild restriction, in the time necessary to apply the sparse embedding matrix to the input vectors.Specifically, given a set of n vectors in R d and target error ε, we give a deterministic algorithm to compute a {-1, 0, 1} embedding matrix of rank O((ln n)/ε 2 ) with O((ln n)/ε) entries per column which preserves the norms of the vectors to within 1±ε.If NNZ, the number of non-zero entries in the input set of vectors, is Ω(d 2 ), our algorithm runs in time O(NNZ • ln n/ε).One ingredient in our construction is an extremely simple proof of the Hanson-Wright inequality for subgaussian random variables, which is more amenable to derandomization.As an interesting byproduct, we are able to derive the essentially optimal form of the inequality in terms of its functional dependence on the parameters.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.87"}, {"primary_key": "3510111", "vector": [], "sparse_vector": [], "title": "Geometric Rescaling Algorithms for Submodular Function Minimization.", "authors": ["<PERSON>", "László A. <PERSON>", "<PERSON>"], "summary": "We present a new class of polynomial-time algorithms for submodular function minimization (SFM), as well as a unified framework to obtain strongly polynomial SFM algorithms. Our new algorithms are based on simple iterative methods for the minimum-norm problem, such as the conditional gradient and the Fujishige-Wolfe algorithms. We exhibit two techniques to turn simple iterative methods into polynomial-time algorithms.Firstly, we use the geometric rescaling technique, which has recently gained attention in linear programming. We adapt this technique to SFM and obtain a weakly polynomial bound O((n4 · EO + n5) log(nL)).Secondly, we exhibit a general combinatorial black-box approach to turn any strongly polynomial εL-approximate SFM oracle into an strongly polynomial exact SFM algorithm. This framework can be applied to a wide range of combinatorial and continuous algorithms, including pseudopolynomial ones. In particular, we can obtain strongly polynomial algorithms by a repeated application of the conditional gradient or of the Fujishige-Wolfe algorithm. Combined with the geometric rescaling technique, the black-box approach provides a O((n5 · EO + n6) log2 n) algorithm.Finally, we show that one of the techniques we develop in the paper, \"sliding\", can also be combined with the cutting-plane method of <PERSON>, <PERSON>, and <PERSON> [27], yielding a simplified variant of their O(n3 log2 n · EO + n4 logO(1) n) algorithm.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.54"}, {"primary_key": "3510112", "vector": [], "sparse_vector": [], "title": "Testing Ising Models.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given samples from an unknown multivariate distribution p, is it possible to distinguish whether p is the product of its marginals versus p being far from every product distribution? Similarly, is it possible to distinguish whether p equals a given distribution q versus p and q being far from each other? These problems of testing independence and goodness-of-fit have received enormous attention in statistics, information theory, and theoretical computer science, with sample-optimal algorithms known in several interesting regimes of parameters [BFF+01, Pan08, VV17, ADK15, DK16]. Unfortunately, it has also been understood that these problems become intractable in large dimensions, necessitating exponential sample complexity.Motivated by the exponential lower bounds for general distributions as well as the ubiquity of Markov Random Fields (MRFs) in the modeling of high-dimensional distributions, we initiate the study of distribution testing on structured multivariate distributions, and in particular the prototypical example of MRFs: the Ising Model. We demonstrate that, in this structured setting, we can avoid the curse of dimensionality, obtaining sample and time efficient testers for independence and goodness-of-fit. One of the key technical challenges we face along the way is bounding the variance of functions of the Ising model.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.130"}, {"primary_key": "3510113", "vector": [], "sparse_vector": [], "title": "Which Distribution Distances are Sublinearly Testable?", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given samples from an unknown distribution p and a description of a distribution q, are p and q close or far? This question of \"identity testing\" has received significant attention in the case of testing whether p and q are equal or far in total variation distance. However, in recent work [VV11a, ADK15, DP17], the following questions have been been critical to solving problems at the frontiers of distribution testing:•Alternative Distances: Can we test whether p and q are far in other distances, say <PERSON><PERSON>?•Tolerance: Can we test when p and q are close, rather than equal? And if so, close in which distances?Motivated by these questions, we characterize the complexity of distribution testing under a variety of distances, including total variation, ℓ2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and χ2. For each pair of distances d1 and d2, we study the complexity of testing if p and q are close in d1 versus far in d2, with a focus on identifying which problems allow strongly sublinear testers (i.e., those with complexity O(n1–γ) for some γ > 0 where n is the size of the support of the distributions p and q). We provide matching upper and lower bounds for each case. We also study these questions in the case where we only have samples from q (equivalence testing), showing qualitative differences from identity testing in terms of when tolerance can be achieved. Our algorithms fall into the classical paradigm of χ2-statistics, but require crucial changes to handle the challenges introduced by each distance we consider. Finally, we survey other recent results in an attempt to serve as a reference for the complexity of various distribution testing problems.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.175"}, {"primary_key": "3510114", "vector": [], "sparse_vector": [], "title": "Boolean function analysis meets stochastic optimization: An approximation scheme for stochastic knapsack.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "The stochastic knapsack problem is the stochastic variant of the classical knapsack problem in which the algorithm designer is given a a knapsack with a given capacity and a collection of items where each item is associated with a profit and a probability distribution on its size. The goal is to select a subset of items with maximum profit and violate the capacity constraint with probability at most p (referred to as the overflow probability).While several approximation algorithms [27, 22, 4, 17, 30] have been developed for this problem, most of these algorithms relax the capacity constraint of the knapsack. In this paper, we design efficient approximation schemes for this problem without relaxing the capacity constraint.(i)Our first result is in the case when item sizes are Bernoulli random variables. In this case, we design a (nearly) fully polynomial time approximation scheme (FPTAS) which only relaxes the overflow probability.(ii)Our second result generalizes the first result to the case when all the item sizes are supported on a (common) set of constant size. In this case, we obtain a quasi-FPTAS.(iii)Our third result is in the case when item sizes are so-called \"hypercontractive\" random variables i.e., random variables whose second and fourth moments are within constant factors of each other. In other words, the kurtosis of the random variable is upper bounded by a constant. This class has been widely studied in probability theory and most natural random variables are hypercontractive including well-known families such as Poisson, Gaussian, exponential and Laplace distributions. In this case, we design a polynomial time approximation scheme which relaxes both the overflow probability and maximum profit.Crucially, all of our algorithms meet the capacity constraint exactly, a result which was previously known only when the item sizes were Poisson or Gaussian random variables [22, 24]. Our results rely on new connections between Boolean function analysis and stochastic optimization and are obtained by an adaption and extension of ideas such as (central) limit theorems, moment matching theorems and the influential critical index machinery of Servedio [43] developed in the context of complexity theoretic analysis of halfspaces. We believe that these ideas and techniques may prove to be useful in other stochastic optimization problems as well.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.84"}, {"primary_key": "3510115", "vector": [], "sparse_vector": [], "title": "Non interactive simulation of correlated distributions is decidable.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A basic problem in information theory is the following: Let P = (X, Y) be an arbitrary distribution where the marginals X and Y are (potentially) correlated. Let <PERSON> and <PERSON> be two players where <PERSON> gets samples {xi}i≥1 and <PERSON> gets samples {yi}i≥i and for all i, (xi,yi) ∼ P. What joint distributions Q can be simulated by <PERSON> and <PERSON> without any interaction?Classical works in information theory by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> answer this question when at least one of P or Q is the distribution Eq (Eq is defined as uniform over the points (0, 0) and (1, 1)). However, other than this special case, the answer to this question is understood in very few cases. Recently, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and Sudan showed that this problem is decidable for Q supported on {0, 1} × {0, 1}. We extend their result to Q supported on any finite alphabet. Moreover, we show that If Q can be simulated, our algorithm also provides a (non-interactive) simulation protocol.We rely on recent results in Gaussian geometry (by the authors) as well as a new smoothing argument inspired by the method of boosting from learning theory and potential function arguments from complexity theory and additive combinatorics.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.174"}, {"primary_key": "3510116", "vector": [], "sparse_vector": [], "title": "Envy-free Chore Division for An Arbitrary Number of Agents.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Chore division, introduced by <PERSON> in 1970s [10], is the problem of fairly dividing a chore among n different agents. In particular, in an envy-free chore division, we would like to divide a negatively valued heterogeneous object among a number of agents who have different valuations for different parts of the object, such that no agent envies another agent. It is the dual variant of the celebrated cake cutting problem, in which we would like to divide a desirable object among agents. There has been an extensive amount of study and effort to design bounded and envy-free protocols/algorithms for fair division of chores and goods, such that envy-free cake cutting became one of the most important open problems in 20-th century mathematics according to <PERSON><PERSON><PERSON>nk<PERSON> [11]. However, despite persistent efforts, due to delicate nature of the problem, there was no bounded protocol known for cake cutting even among four agents, until the breakthrough of <PERSON> and <PERSON> [2], which provided the first discrete and bounded envy-free protocol for cake cutting for four agents. Afterward, <PERSON> and <PERSON> [3], generalized their work and provided an envy-free cake cutting protocol for any number of agents to settle a significant and longstanding open problem. However, there is much less known for chore division. Unfortunately, there is no general method known to apply cake cutting techniques to chore division. Thus, it remained an open problem to find a discrete and bounded envy-free chore division protocol even for four agents.In this paper, we provide the first discrete and bounded envy-free protocol for chore division for an arbitrary number of agents. We produce major and powerful tools for designing protocols for the fair division of negatively valued objects. These tools are based on structural results and important observations. In general, we believe these structures and techniques may be useful not only in chore division but also in other fairness problems. Interestingly, we show that applying these techniques simplifies Core Protocol provided in <PERSON> and <PERSON> [3].", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.164"}, {"primary_key": "3510117", "vector": [], "sparse_vector": [], "title": "A New Class of Combinatorial Markets with Covering Constraints: Algorithms and Applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We introduce a new class of combinatorial markets in which agents have covering constraints over resources required and are interested in delay minimization. Our market model is applicable to several settings including scheduling and communicating over a network.This model is quite different from the traditional models, to the extent that neither do the classical equilibrium existence results seem to apply to it nor do any of the efficient algorithmic techniques developed to compute equilibria. In particular, our model does not satisfy the condition of non-satiation, which is used critically to show the existence of equilibria in traditional market models and we observe that our set of equilibrium prices could be a connected, nonconvex set.We give a proof of the existence of equilibria and a polynomial time algorithm for finding one, drawing heavily on techniques from LP duality and submodular minimization. Finally, we show that our model inherits many of the fairness properties of traditional equilibrium models as well as new models, such as CEEI.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.149"}, {"primary_key": "3510118", "vector": [], "sparse_vector": [], "title": "Truthful Multi-Parameter Auctions with Online Supply: an Impossible Combination.", "authors": ["<PERSON><PERSON>", "Balasubramanian <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study a basic auction design problem with online supply. There are two unit-demand bidders and two types of items. The first item type will arrive first for sure, and the second item type may or may not arrive. The auctioneer has to decide the allocation of an item immediately after each item arrives, but is allowed to compute payments after knowing how many items arrived. For this problem we show that there is no deterministic truthful and individually rational mechanism that, even with unbounded computational resources, gets any finite approximation factor to the optimal social welfare.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.49"}, {"primary_key": "3510119", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON> Learning a Gaussian: Getting Optimal Error, Efficiently.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the fundamental problem of learning the parameters of a high-dimensional Gaussian in the presence of noise — where an ε-fraction of our samples were chosen by an adversary. We give robust estimators that achieve estimation error O(ε) in the total variation distance, which is optimal up to a universal constant that is independent of the dimension.In the case where just the mean is unknown, our robustness guarantee is optimal up to a factor of and the running time is polynomial in d and 1/ε. When both the mean and covariance are unknown, the running time is polynomial in d and quasipolynomial in 1/ε. Moreover all of our algorithms require only a polynomial number of samples. Our work shows that the same sorts of error guarantees that were established over fifty years ago in the one-dimensional setting can also be achieved by efficient algorithms in high-dimensional settings.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.171"}, {"primary_key": "3510120", "vector": [], "sparse_vector": [], "title": "Nearly Tight Bounds for Sandpile Transience on the Grid.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We use techniques from the theory of electrical networks to give nearly tight bounds for the transience class of the Abelian sandpile model on the two-dimensional grid up to polylogarithmic factors. The Abelian sandpile model is a discrete process on graphs that is intimately related to the phenomenon of self-organized criticality. In this process, vertices receive grains of sand, and once the number of grains exceeds their degree, they topple by sending grains to their neighbors. The transience class of a model is the maximum number of grains that can be added to the system before it necessarily reaches its steady-state behavior or, equivalently, a recurrent state. Through a more refined and global analysis of electrical potentials and random walks, we give an O(n4 log4 n) upper bound and an Ω(n4) lower bound for the transience class of the n × n grid. Our methods naturally extend to nd-sized d-dimensional grids to give O(n3d–2 logd+2 n) upper bounds and Ω(n3d–2) lower bounds.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.40"}, {"primary_key": "3510121", "vector": [], "sparse_vector": [], "title": "Thin graph classes and polynomial-time approximation schemes.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "<PERSON> [1] devised a powerful technique to obtain approximation schemes for various problems restricted to planar graphs. Her technique can be directly extended to various other graph classes, among the most general ones the graphs avoiding a fixed apex graph as a minor. Further generalizations (e.g., to all proper minor closed graph classes) are known, but they use a combination of techniques and usually focus on somewhat restricted classes of problems. We present a new type of graph decompositions (thin systems of overlays) generalizing <PERSON>'s technique and leading to straightforward polynomial-time approximation schemes. We also show that many graph classes (all proper minor-closed classes, and all subgraph-closed classes with bounded maximum degree and strongly sublinear separators) admit such decompositions.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.110"}, {"primary_key": "3510122", "vector": [], "sparse_vector": [], "title": "Testing bounded arboricity.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we consider the problem of testing whether a graph has bounded arboricity. The family of graphs with bounded arboricity includes, among others, bounded-degree graphs, all minor-closed graph classes (e.g. planar graphs, graphs with bounded treewidth) and randomly generated preferential attachment graphs. Graphs with bounded arboricity have been studied extensively in the past, in particular since for many problems they allow for much more efficient algorithms and/or better approximation ratios.We present a tolerant tester in the sparse-graphs model. The sparse-graphs model allows access to degree queries and neighbor queries, and the distance is defined with respect to the actual number of edges. More specifically, our algorithm distinguishes between graphs that are e-close to having arboricity α and graphs that c · ∊-far from having arboricity 3α, where c is an absolute small constant. The query complexity and running time of the algorithm are1 where n denotes the number of vertices and m denotes the number of edges. In terms of the dependence on n and m this bound is optimal up to poly-logarithmic factors since queries are necessary (and the arboricity of a graph is always . We leave it as an open question whether the dependence on 1/∊ can be improved from quasi-polynomial to polynomial. Our techniques include an efficient local simulation for approximating the outcome of a global (almost) forest-decomposition algorithm as well as a tailored procedure of edge sampling.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.136"}, {"primary_key": "3510124", "vector": [], "sparse_vector": [], "title": "Sampling Random Colorings of Sparse Random Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We study the mixing properties of the single-site Markov chain known as the G<PERSON>ber dynamics for sampling k-colorings of a sparse random graph G(n, d/n) for constant d. The best known rapid mixing results for general graphs are in terms of the maximum degree Δ of the input graph G and hold when k > 11Δ/6 for all G. Improved results hold when k > αΔ for graphs with girth ≥ 5 and Δ sufficiently large where α ≈ 1.7632 … is the root of α = exp(1/α); further improvements on the constant α hold with stronger girth and maximum degree assumptions.For sparse random graphs the maximum degree is a function of n and the goal is to obtain results in terms of the expected degree d. The following rapid mixing results for G(n,d/n) hold with high probability over the choice of the random graph for sufficiently large constant d. <PERSON> and <PERSON> (2009) proved rapid mixing for constant k, and <PERSON><PERSON><PERSON><PERSON><PERSON> (2014) improved this to k linear in d. The condition was improved to k > 3d by <PERSON> and <PERSON> (2016) using non-MCMC methods.Here we prove rapid mixing when k > αd where α ≈ 1.7632 … is the same constant as above. Moreover we obtain O(n3) mixing time of the Glauber dynamics, while in previous rapid mixing results the exponent was an increasing function in d. Our proof analyzes an appropriately defined block dynamics to \"hide\" high-degree vertices. One new aspect in our improved approach is utilizing so-called local uniformity properties for the analysis of block dynamics. To analyze the \"burn-in\" phase we prove a concentration inequality for the number of disagreements propagating in large blocks.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.115"}, {"primary_key": "3510125", "vector": [], "sparse_vector": [], "title": "Prophet Secretary for Combinatorial Auctions and Matroids.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The secretary and the prophet inequality problems are central to the field of Stopping Theory. Recently, there has been a lot of work in generalizing these models to multiple items because of their applications in mechanism design. The most important of these generalizations are to matroids and to combinatorial auctions (extends bipartite matching). <PERSON><PERSON><PERSON> [33] and <PERSON><PERSON><PERSON> et al. [17] show that for adversarial arrival order of random variables the optimal prophet inequalities give a 1/2-approximation. For many settings, however, it's conceivable that the arrival order is chosen uniformly at random, akin to the secretary problem. For such a random arrival model, we improve upon the 1/2-approximation and obtain (1 – 1/e)-approximation prophet inequalities for both matroids and combinatorial auctions. This also gives improvements to the results of <PERSON> [45] and <PERSON><PERSON><PERSON><PERSON><PERSON> et al. [15] who worked in the special cases where we can fully control the arrival order or when there is only a single item.Our techniques are threshold based. We convert our discrete problem into a continuous setting and then give a generic template on how to dynamically adjust these thresholds to lower bound the expected total welfare.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.46"}, {"primary_key": "3510126", "vector": [], "sparse_vector": [], "title": "Proximity results and faster algorithms for Integer Programming using the Steinitz Lemma.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We consider integer programming problems in standard form max{cTx : Ax = b, x ≥ 0, x ∊ ℤn} where A ∊ ℤm×n, b ∊ ℤm and c ∊ ℤn. We show that such an integer program can be solved in time (m·Δ)O(m) · ||b||∞2, where Δ is an upper bound on each absolute value of an entry in A. This improves upon the longstanding best bound of <PERSON><PERSON><PERSON><PERSON><PERSON> (1981) of (m · Δ)O(m2), where in addition, the absolute values of the entries of b also need to be bounded by Δ. Our result relies on a lemma of <PERSON><PERSON> that states that a set of vectors in ℝm that is contained in the unit ball of a norm and that sum up to zero can be ordered such that all partial sums are of norm bounded by m.We also use the <PERSON><PERSON> lemma to show that the ℓ1-distance of an optimal integer and fractional solution, also under the presence of upper bounds on the variables, is bounded by m · (2m · Δ + 1)m. Here Δ is again an upper bound on the absolute values of the entries of A. The novel strength of our bound is that it is independent of n.We provide evidence for the significance of our bound by applying it to general knapsack problems where we obtain structural and algorithmic results that improve upon the recent literature.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.52"}, {"primary_key": "3510127", "vector": [], "sparse_vector": [], "title": "Adaptive Hierarchical Clustering Using Ordinal Queries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In many applications of clustering (for example, ontologies or clusterings of animal or plant species), hierarchical clusterings are more descriptive than a flat clustering. A hierarchical clustering over n elements is represented by a rooted binary tree with n leaves, each corresponding to one element. The subtrees rooted at interior nodes capture the clusters. In this paper, we study active learning of a hierarchical clustering using only ordinal queries. An ordinal query consists of a set of three elements, and the response to a query reveals the two elements (among the three elements in the query) which are \"closer\" to each other than to the third one. We say that elements x and x′ are closer to each other than x″ if there exists a cluster containing x and x′, but not x″.When all the query responses are correct, there is a deterministic algorithm that learns the underlying hierarchical clustering using at most n log2 n adaptive ordinal queries. We generalize this algorithm to be robust in a model in which each query response is correct independently with probability p > ½, and adversarially incorrect with probability 1 – p. We show that in the presence of noise, our algorithm outputs the correct hierarchical clustering with probability at least 1 – δ, using O(n log n + n log(1/δ)) adaptive ordinal queries. For our results, adaptivity is crucial: we prove that even in the absence of noise, every non-adaptive algorithm requires Ω(n3) ordinal queries in the worst case.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.28"}, {"primary_key": "3510128", "vector": [], "sparse_vector": [], "title": "Metric Violation Distance: Hardness and Approximation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Metric data plays an important role in various settings, for example, in metric-based indexing, clustering, classification, and approximation algorithms in general. Due to measurement error, noise, or an inability to completely gather all the data, a collection of distances may not satisfy the basic metric requirements, most notably the triangle inequality. In this paper we initiate the study of the metric violation distance problem: given a set of pairwise distances, modify the minimum number of distances such that the resulting set forms a metric. Three variants of the problem are considered, based on whether distances are allowed to only decrease, only increase, or the general case which allows both decreases and increases. We show that while the decrease only variant is polynomial time solvable, the increase only and general variants are NP-Complete, and moreover cannot in polynomial time be approximated to any ratio better than the minimum vertex cover problem. We then provide approximation algorithms for the increase only and general variants of the problem, by proving interesting necessary and sufficient conditions on the optimal solution, which are used to approximately reduce to a purely combinatorial problem for which we provide matching asymptotic upper and lower bounds.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.14"}, {"primary_key": "3510129", "vector": [], "sparse_vector": [], "title": "A Framework for the Secretary Problem on the Intersection of Matroids.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The secretary problem became one of the most prominent online selection problems due to its numerous applications in online mechanism design. The task is to select a maximum weight subset of elements subject to given constraints, where elements arrive one-by-one in random order, revealing a weight upon arrival. The decision whether to select an element has to be taken immediately after its arrival. The different applications that map to the secretary problem ask for different constraint families to be handled. The most prominent ones are matroid constraints, which both capture many relevant settings and admit strongly competitive secretary algorithms. However, dealing with more involved constraints proved to be much more difficult, and strong algorithms are known only for a few specific settings. In this paper, we present a general framework for dealing with the secretary problem over the intersection of several matroids. This framework allows us to combine and exploit the large set of matroid secretary algorithms known in the literature. As one consequence, we get constant-competitive secretary algorithms over the intersection of any constant number of matroids whose corresponding (single-)matroid secretary problems are currently known to have a constant-competitive algorithm. Moreover, we show that our results extend to submodular objectives.MSC codesmatroid secretary problemmatroid intersectiononline algorithms", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.48"}, {"primary_key": "3510130", "vector": [], "sparse_vector": [], "title": "Computing Simplicial Representatives of Homotopy Group Elements.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A central problem of algebraic topology is to understand the homotopy groups πd(X) of a topological space X. For the computational version of the problem, it is well known that there is no algorithm to decide whether the fundamental group π1(Χ) of a given finite simplicial complex X is trivial. On the other hand, there are several algorithms that, given a finite simplicial complex X that is simply connected (i.e., with π1(Χ) trivial), compute the higher homotopy group πd(X) for any given d ≥ 2.However, these algorithms come with a caveat: They compute the isomorphism type of πd(Χ), d ≥ 2 as an abstract finitely generated abelian group given by generators and relations, but they work with very implicit representations of the elements of πd(Χ). Converting elements of this abstract group into explicit geometric maps from the d-dimensional sphere Sd to Χ has been an important open question in the emerging field of computational homotopy theory.Here we present an algorithm that, given a simply connected simplicial complex Χ, computes πd(Χ) and represents its elements as simplicial maps from a suitable triangulation of the d-sphere Sd to Χ. For fixed d, the algorithm runs in time exponential in size(X), the number of simplices of Χ. Moreover, we prove that this is optimal: For every fixed d ≥ 2, we construct a family of simply connected simplicial complexes Χ such that for any simplicial map representing a generator of πd(Χ), the size of the triangulation of Sd on which the map is defined is exponential in size(X).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.74"}, {"primary_key": "3510131", "vector": [], "sparse_vector": [], "title": "Steiner Point Removal with Distortion O(log k).", "authors": ["<PERSON>"], "summary": "In the Steiner point removal (SPR) problem, we are given a weighted graph G = (V, E) and a set of terminals K ⊂ V of size k. The objective is to find a minor M of G with only the terminals as its vertex set, such that the distance between the terminals will be preserved up to a small multiplicative distortion. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON><PERSON>] used a ball-growing algorithm with exponential distributions to show that the distortion is at most O(log5 k). <PERSON> [Che18] improved the analysis of the same algorithm, bounding the distortion by O(log2 k). We improve the analysis of this ball-growing algorithm even further, bounding the distortion by O(log k).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.90"}, {"primary_key": "3510132", "vector": [], "sparse_vector": [], "title": "Approximating Weighted Tree Augmentation via Chvátal-Gomory Cuts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The weighted tree augmentation problem (WTAP) is a fundamental network design problem. We are given an undirected tree G = (V, E) with n = |V| nodes, an additional set of edges L called links and a cost vector . The goal is to choose a minimum cost subset S ⊆ L such that G = (V, E ∪ S) is 2-edgeconnected. In the unweighted case, that is, when we have cℓ = 1 for all ℓ ∊ L, the problem is called the tree augmentation problem (TAP).Both problems are known to be APX-hard, and the best known approximation factors are 2 for WTAP by (<PERSON><PERSON> and <PERSON>, '81) and for TAP due to (<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, TALG '16). <PERSON><PERSON><PERSON><PERSON> (SODA '17) recently presented an ≈ 1.96418 + ε-approximation algorithm for WTAP for the case where all link costs are bounded by a constant. This is the first approximation with a better guarantee than 2 that does not require restrictions on the structure of the tree or the links.In this paper, we improve <PERSON><PERSON><PERSON><PERSON>'s approximation to a + ε-approximation for WTAP under the bounded cost assumption. We achieve this by introducing a strong LP that combines {0, ½}-Chvátal-Gomory cuts for the standard LP for the problem with bundle constraints from <PERSON><PERSON><PERSON><PERSON>. We show that our LP can be solved efficiently and that it is exact for some instances that arise at the core of <PERSON><PERSON><PERSON><PERSON>'s approach. This results in the improved performance guarantee of + ε, which is asymptotically on par with the result by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Our result also is the best-known LP-relative approximation algorithm for TAP.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.53"}, {"primary_key": "3510133", "vector": [], "sparse_vector": [], "title": "Improved bounds for testing Dyck languages.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we consider the problem of deciding membership in Dyck languages, a fundamental family of context-free languages, comprised of well-balanced strings of parentheses. In this problem we are given a string of length n in the alphabet of parentheses of m types and must decide if it is well-balanced. We consider this problem in the property testing setting, where one would like to make the decision while querying as few characters of the input as possible.Property testing of strings for Dyck language membership for m = 1, with a number of queries independent of the input size n, was provided in [<PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>, SICOMP 2001]. Property testing of strings for Dyck language membership for m ≥ 2 was first investigated in [<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, RSA 2003]. They showed an upper bound and a lower bound for distinguishing strings belonging to the language from strings that are far (in terms of the Hamming distance) from the language, which are respectively (up to polylogarithmic factors) the 2/3 power and the 1/11 power of the input size n.Here we improve the power of n in both bounds. For the upper bound, we introduce a recursion technique, that together with a refinement of the methods in the original work provides a test for any power of n larger than 2/5. For the lower bound, we introduce a new problem called Truestring Equivalence, which is easily reducible to the 2-type Dyck language property testing problem. For this new problem, we show a lower bound of n to the power of 1/5.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.100"}, {"primary_key": "3510134", "vector": [], "sparse_vector": [], "title": "Tight Analysis of Parallel Randomized Greedy MIS.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We provide a tight analysis which settles the round complexity of the well-studied parallel randomized greedy MIS algorithm, thus answering the main open question of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> [SPAA'12].The parallel/distributed randomized greedy Maximal Independent Set (MIS) algorithm works as follows. An order of the vertices is chosen uniformly at random. Then, in each round, all vertices that appear before their neighbors in the order are added to the independent set and removed from the graph along with their neighbors. The main question of interest is the number of rounds it takes until the graph is empty. This algorithm has been studied since 1987, initiated by <PERSON>smith, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [FOCS'87], and the previously best known bounds were O(log n) rounds in expectation for Erdős-Rényi random graphs by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [Random Struc. & Alg. '90] and O(log2 n) rounds with high probability for general graphs by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> [SPAA'12].We prove a high probability upper bound of O(log n) on the round complexity of this algorithm in general graphs, and that this bound is tight. This also shows that parallel randomized greedy MIS is as fast as the celebrated algorithm of Luby [STOC'85, JALG'86].", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.140"}, {"primary_key": "3510135", "vector": [], "sparse_vector": [], "title": "The Complexity of Counting Surjective Homomorphisms and Compactions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A homomorphism from a graph G to a graph H is a function from the vertices of G to the vertices of H that preserves edges. A homomorphism is surjective if it uses all of the vertices of H and it is a compaction if it uses all of the vertices of H and all of the non-loop edges of H. <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gave a complete characterisation of the complexity of deciding whether there is a homomorphism from an input graph G to a fixed graph H. A complete characterisation is not known for surjective homomorphisms or for compactions, though there are many interesting results. <PERSON> and <PERSON> gave a complete characterisation of the complexity of counting homomorphisms from an input graph G to a fixed graph H. In this paper, we give a complete characterisation of the complexity of counting surjective homomorphisms from an input graph G to a fixed graph H and we also give a complete characterisation of the complexity of counting compactions from an input graph G to a fixed graph H.The full version containing detailed proofs is available at http://arxiv.org/abs/1706.08786.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.116"}, {"primary_key": "3510136", "vector": [], "sparse_vector": [], "title": "Approximation Schemes for Clustering with Outliers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Clustering problems are well-studied in a variety of fields such as data science, operations research, and computer science. Such problems include variants of centre location problems, k-median, and k-means to name a few. In some cases, not all data points need to be clustered; some may be discarded for various reasons. For instance, some points may arise from noise in a data set or one might be willing to discard a certain fraction of the points to avoid incurring unnecessary overhead in the cost of a clustering solution.We study clustering problems with outliers. More specifically, we look at UNCAPACITATED FACILITY LOCATION (UFL), k-MEDIAN, and k-MEANS. In these problems, we are given a set χ of data points in a metric space δ(.,.), a set C of possible centres (each maybe with an opening cost), maybe an integer parameter k, plus an additional parameter z as the number of outliers. In UNCAPACITATED FACILITY LOCATION with outliers, we have to open some centres, discard up to z points of χ and assign every other point to the nearest open centre, minimizing the total assignment cost plus centre opening costs. In k-MEDIAN and k-MEANS, we have to open up to k centres but there are no opening costs. In k-MEANS, the cost of assigning j to i is δ2(j, i).We present several results. Our main focus is on cases where δ is a doubling metric (this includes fixed dimensional Euclidean metrics as a special case) or is the shortest path metrics of graphs from a minor-closed family of graphs. For UNIFORM-COST UFL with outliers on such metrics we show that a multiswap simple local search heuristic yields a PTAS. With a bit more work, we extend this to bicriteria approximations for the k-MEDIAN and k-MEANS problems in the same metrics where, for any constant ε > 0, we can find a solution using (1 + ε)k centres whose cost is at most a (1 + ε)-factor of the optimum and uses at most z outliers. Our algorithms are all based on natural multiswap local search heuristics. We also show that natural local search heuristics that do not violate the number of clusters and outliers for k-MEDIAN (or k-MEANS) will have unbounded gap even in Euclidean metrics.Furthermore, we show how our analysis can be extended to general metrics for k-MEANS with outliers to obtain a (25 + ε, 1 + ε)-approximation: an algorithm that uses at most (1 + ε)k clusters and whose cost is at most 25 + ε of optimum and uses no more than z outliers.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.27"}, {"primary_key": "3510137", "vector": [], "sparse_vector": [], "title": "The Value of Information Concealment.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider a revenue optimizing seller selling a single item to a buyer, on whose private value the seller has a noisy signal. We show that, when the signal is kept private, arbitrarily more revenue could potentially be extracted than if the signal is leaked or revealed. We then show that, if the seller is not allowed to make payments to the buyer and if the value distribution conditioning on each signal is regular, the gap between the two is bounded by a multiplicative factor of 3. We give examples showing that both conditions are necessary for a constant bound on the gap to hold.We connect this scenario to multi-bidder single-item auctions where bidders' values are correlated. Similarly to the setting above, we show that the revenue of a Bayesian incentive compatible, ex post individually rational auction can be arbitrarily larger than that of a dominant strategy incentive compatible auction, whereas the two are no more than a factor of 5 apart if the auctioneer never pays the bidders and if the distribution is jointly regular. The upper bounds in both settings degrade gracefully when the distribution is a mixture of a small number of regular distributions.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.162"}, {"primary_key": "3510138", "vector": [], "sparse_vector": [], "title": "Optimal-Time Text Indexing in BWT-runs Bounded Space.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Indexing highly repetitive texts | such as genomic databases, software repositories and versioned text collections | has become an important problem since the turn of the millennium. A relevant compressibility measure for repetitive texts is r, the number of runs in their Burrows-Wheeler Transform (BWT). One of the earliest indexes for repetitive collections, the Run-Length FMindex, used O(r) space and was able to efficiently count the number of occurrences of a pattern of length m in the text (in loglogarithmic time per pattern symbol, with current techniques). However, it was unable to locate the positions of those occurrences efficiently within a space bounded in terms of r. Since then, a number of other indexes with space bounded by other measures of repetitiveness | the number of phrases in the LempelZiv parse, the size of the smallest grammar generating the text, the size of the smallest automaton recognizing the text factors | have been proposed for efficiently locating, but not directly counting, the occurrences of a pattern. In this paper we close this long-standing problem, showing how to extend the Run-Length FMindex so that it can locate the occ occurrences efficiently within O(r) space (in loglogarithmic time each), and reaching optimal time O(m+occ) within O(r log(n=r)) space, on a RAM machine with words of w = (log n) bits. Raising the space to O(rw logÏƒ (n=r)), we support locate in O(mlog(Ïƒ)=w+occ) time, which is optimal in the packed setting and had not been obtained before in compressed space. We also describe a structure using O(r log(n=r)) space that replaces the text and efficiently extracts any text substring, with an O(log(n=r)) additive time penalty over the optimum. Preliminary experiments show that our new structure outperforms the alternatives by orders of magnitude in the space/time tradeoff map.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.96"}, {"primary_key": "3510139", "vector": [], "sparse_vector": [], "title": "Improved Rectangular Matrix Multiplication using Powers of the Coppersmith-Winograd Tensor.", "authors": ["<PERSON><PERSON>", "Florent Urrutia"], "summary": "In the past few years, successive improvements of the asymptotic complexity of square matrix multiplication have been obtained by developing novel methods to analyze the powers of the Coppersmith-Winograd tensor, a basic construction introduced thirty years ago. In this paper we show how to generalize this approach to make progress on the complexity of rectangular matrix multiplication as well, by developing a framework to analyze powers of tensors in an asymmetric way. By applying this methodology to the fourth power of the Coppersmith-Winograd tensor, we succeed in improving the complexity of rectangular matrix multiplication.Let α denote the maximum value such that the product of an n × nα matrix by an nα × n matrix can be computed with O(n2+∊) arithmetic operations for any ∊ > 0. By analyzing the fourth power of the Coppersmith-Winograd tensor using our methods, we obtain the new lower bound α > 0.31389, which improves the previous lower bound α > 0.30298 obtained by <PERSON> (FOCS’12) from the analysis of the second power of the Coppersmith-Winograd tensor. More generally, we give faster algorithms computing the product of an n × nk matrix by an nk × n matrix for any value k ≠ 1. (In the case k = 1, we recover the bounds recently obtained for square matrix multiplication).These improvements immediately lead to improvements in the complexity of a multitude of fundamental problems for which the bottleneck is rectangular matrix multiplication, such as computing the all-pair shortest paths in directed graphs with bounded weights.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.67"}, {"primary_key": "3510140", "vector": [], "sparse_vector": [], "title": "Uniform generation of random graphs with power-law degree sequences.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We give a linear-time algorithm that approximately uniformly generates a random simple graph with a power-law degree sequence whose exponent is at least 2.8811. While sampling graphs with power-law degree sequence of exponent at least 3 is fairly easy, and many samplers work efficiently in this case, the problem becomes dramatically more difficult when the exponent drops below 3; ours is the first provably practicable sampler for this case. We also show that with an appropriate rejection scheme, our algorithm can be tuned into an exact uniform sampler. The running time of the exact sampler is O(n2.107) with high probability, and O(n4.081) in expectation.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.114"}, {"primary_key": "3510141", "vector": [], "sparse_vector": [], "title": "Approximating the Nash Social Welfare with Budget-Additive Valuations.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present the first constant-factor approximation algorithm for maximizing the Nash social welfare when allocating indivisible items to agents with budget-additive valuation functions. Budget-additive valuations represent an important class of submodular functions. They have attracted a lot of research interest in recent years due to many interesting applications. For every ε > 0, our algorithm obtains a (2.404 + ε)-approximation in time polynomial in the input size and 1/ε.Our algorithm relies on rounding an approximate equilibrium in a linear Fisher market where sellers have earning limits (upper bounds on the amount of money they want to earn) and buyers have utility limits (upper bounds on the amount of utility they want to achieve). In contrast to markets with either earning or utility limits, these markets have not been studied before. They turn out to have fundamentally different properties.Although the existence of equilibria is not guaranteed, we show that the market instances arising from the Nash social welfare problem always have an equilibrium. Further, we show that the set of equilibria is not convex, answering a question of [17]. We design an FPTAS to compute an approximate equilibrium, a result that may be of independent interest.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.150"}, {"primary_key": "3510142", "vector": [], "sparse_vector": [], "title": "Fast Space Optimal Leader Election in Population Protocols.", "authors": ["Leszek Gasieniec", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The model of population protocols refers to the growing in popularity theoretical framework suitable for studying pairwise interactions within a large collection of simple indistinguishable entities, frequently called agents. In this paper the emphasis is on the space complexity in fast leader election via population protocols governed by the random scheduler, which uniformly at random selects pairwise interactions within the population of n agents. The main result of this paper is a new fast and space optimal leader election protocol. The new protocol utilises O(log^2 n) parallel time (which is equivalent to O(n log^2 n) sequential pairwise interactions), and each agent operates on O(log log n) states. This double logarithmic space usage matches asymptotically the lower bound 1/2 log log n on the minimal number of states required by agents in any leader election algorithm with the running time o(n/polylog n). Our solution takes an advantage of the concept of phase clocks, a fundamental synchronisation and coordination tool in distributed computing. We propose a new fast and robust population protocol for initialisation of phase clocks to be run simultaneously in multiple modes and intertwined with the leader election process. We also provide the reader with the relevant formal argumentation indicating that our solution is always correct, and fast with high probability.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.169"}, {"primary_key": "3510143", "vector": [], "sparse_vector": [], "title": "Optimal Dynamic Strings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we study the fundamental problem of maintaining a dynamic collection of strings under the following operations:•• make_string – add a string of constant length,•• concat – concatenate two strings,•• split – split a string into two at a given position,•• compare – find the lexicographical order (less, equal, greater) between two strings,•• LCP – calculate the longest common prefix of two strings.We develop a generic framework for dynamizing the recompression method recently introduced by <PERSON><PERSON> [J<PERSON>, 2016]. It allows us to present an efficient data structure for the above problem, where an update requires only O(log n) worst-case time with high probability, with n being the total length of all strings in the collection, and a query takes constant worst-case time. On the lower bound side, we prove that even if the only possible query is checking equality of two strings, either updates or queries must take amortized Ω(log n) time; hence our implementation is optimal.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.99"}, {"primary_key": "3510144", "vector": [], "sparse_vector": [], "title": "Labeling Schemes for Nearest Common Ancestors through Minor-Universal Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Preprocessing a tree for finding the nearest common ancestor of two nodes is a basic tool with multiple applications. Quite a few linear-space constant-time solutions are known and the problem seems to be well-understood. This is however not so clear if we want to design a labeling scheme. In this model, the structure should be distributed: every node receives a distinct binary string, called its label, so that given the labels of two nodes (and no further information about the topology of the tree) we can compute the label of their nearest common ancestor. The goal is to make the labels as short as possible. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> [Theor. Comput. Syst. 37(3):441–456 2004] showed that O(log n)-bit labels are enough, with a somewhat large constant. More recently, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> [SODA 2014] refined this to only 2.772 log n, and provided a lower bound of 1.008 log n.We connect the question of designing a labeling scheme for nearest common ancestors to the existence of a tree, called a minor-universal tree, that contains every tree on n nodes as a topological minor. Even though it is not clear if a labeling scheme must be based on such a notion, we argue that all already existing schemes can be reformulated as such. Further, we show that this notion allows us to easily obtain clean and good bounds on the length of the labels. As the main upper bound, we show that 2.318 log n-bit labels are enough. Surprisingly, the notion of a minor-universal tree for binary trees on n nodes has been already used in a different context by <PERSON><PERSON><PERSON> et al. [CC<PERSON> 2010], and <PERSON>, <PERSON>, and <PERSON> [J. ACM 46(3):416–435, 1999] introduced a very closely related (but not equivalent) notion of a universal tree. On the lower bound side, we show that any minor-universal tree for trees on n nodes must contain at least Ω(n2.174) nodes. This highlights a natural limitation for all approaches based on defining a minor-universal tree. We complement the existential results with a generic transformation that allows us, for any labeling scheme for nearest common ancestors based on a minor-universal tree, to decrease the query time to constant, while increasing the length of the labels only by lower order terms.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.166"}, {"primary_key": "3510145", "vector": [], "sparse_vector": [], "title": "Voronoi Diagrams on Planar Graphs, and Computing the Diameter in Deterministic Õ(n5/3) Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an efficient construction of additively weighted Voronoi diagrams on planar graphs. Let G be a planar graph with n vertices and b sites that lie on a constant number of faces. We show how to preprocess G in Õ(nb2) time1 so that one can compute any additively weighted Voronoi diagram for these sites in Õ(b) time.We use this construction to compute the diameter of a directed planar graph with real arc lengths in Õ(n5/3) time. This improves the recent breakthrough result of <PERSON><PERSON><PERSON> (SODA'17), both by improving the running time (from Õ(n11/6)), and by providing a deterministic algorithm. It is in fact the first truly subquadratic deterministic algorithm for this problem. Our use of Voronoi diagrams to compute the diameter follows that of <PERSON><PERSON><PERSON>, but he used abstract Voronoi diagrams, which makes his diameter algorithm more involved, more expensive, and randomized.As in <PERSON><PERSON><PERSON>'s work, our algorithm can also compute the Wiener index of a planar graph (i.e., the sum of all pairwise distances) within the same bound. Our construction of Voronoi diagrams for planar graphs is of independent interest. It has already been used to obtain fast exact distance oracles for planar graphs [<PERSON> et al., FOCS'17].", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.33"}, {"primary_key": "3510146", "vector": [], "sparse_vector": [], "title": "Better Tradeoffs for Exact Distance Oracles in Planar Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an O(n1:5)-space distance oracle for directed planar graphs that answers distance queries in O(log n) time. Our oracle both significantly simplifies and significantly improves the recent oracle of <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> [FOCS 2017], which uses O(n5=3)-space and answers queries in O(log n) time. We achieve this by designing an elegant and efficient point location data structure for Voronoi diagrams on planar graphs. We further show a smooth tradeoff between space and query-time. For any S 2 [n; n2], we show an oracle of size S that answers queries in ~O (maxf1; n1:5=Sg) time. This new tradeoff is currently the best (up to polylogarithmic factors) for the entire range of S and improves by polynomial factors over all previously known tradeoffs for the range S 2 [n; n5=3].", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.34"}, {"primary_key": "3510147", "vector": [], "sparse_vector": [], "title": "Resource-Efficient Common Randomness and Secret-Key Schemes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "We study common randomness where two parties have access to i.i.d. samples from a known random source, and wish to generate a shared random key using limited (or no) communication with the largest possible probability of agreement. This problem is at the core of secret key generation in cryptography, with connections to communication under uncertainty and locality sensitive hashing. We take the approach of treating correlated sources as a critical resource, and ask whether common randomness can be generated resource-efficiently.We consider two notable sources in this setup arising from correlated bits and correlated Gaussians. We design the first explicit schemes that use only a polynomial number of samples (in the key length) so that the players can generate shared keys that agree with constant probability using optimal communication. The best previously known schemes were both non-constructive and used an exponential number of samples. In the amortized setting, we characterize the largest achievable ratio of key length to communication in terms of the external and internal information costs, two well-studied quantities in theoretical computer science. In the relaxed setting where the two parties merely wish to improve the correlation between the generated keys of length k, we show that there are no interactive protocols using o(k) bits of communication having agreement probability even as small as 2–o(k). For the related communication problem where the players wish to compute a joint function f of their inputs using i.i.d samples from a known source, we give a simultaneous message passing protocol using 2O(c) bits where c is the interactive randomized public-coin communication complexity of f. This matches the lower bound shown previously while the best previously known upper bound was doubly exponential in c.Our schemes reveal a new connection between common randomness and unbiased error-correcting codes, e.g., dual-BCH codes and their analogues in Euclidean space.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.120"}, {"primary_key": "3510148", "vector": [], "sparse_vector": [], "title": "Exponentially slow mixing in the mean-field Swendsen-Wang dynamics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Swendsen-Wang dynamics for the Potts model was proposed in the late 1980's as an alternative to single-site heat-bath dynamics, in which global updates allow this MCMC sampler to switch between metastable states and ideally mix faster. <PERSON> and <PERSON><PERSON><PERSON> (1997) found that this dynamics may in fact exhibit slow mixing: they showed that, for the Potts model with q ≥ 3 colors on the complete graph on n vertices at the critical point βc(q), <PERSON><PERSON><PERSON><PERSON>–<PERSON> dynamics has . <PERSON><PERSON> et al. (2015) showed that tMIX ≥ exp(cn1/3) throughout the critical window (βs, βs) around βc, and <PERSON><PERSON> and <PERSON> (2015) established that in the critical window for corresponding mean-field FK model, which implied the same bound for <PERSON><PERSON><PERSON><PERSON><PERSON> via known comparison estimates. In both cases, an upper bound of tMIX ≤ exp(c′n) was known. Here we show that the mixing time is truly exponential in n: namely, tMIX ≥ exp(cn) for Swendsen–Wang dynamics when q ≥ 3 and β ∊ (βs, βS), and the same bound holds for the related MCMC samplers for the mean-field FK model when q > 2.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.129"}, {"primary_key": "3510149", "vector": [], "sparse_vector": [], "title": "Cliquewidth III: The Odd Case of Graph Coloring Parameterized by <PERSON><PERSON>width.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Max<PERSON><PERSON> (MC), Edge Dominating Set (EDS), Graph Coloring (GC) and Hamiltonian Path (HP) on graphs of bounded cliquewidth have received significant attention as they can be formulated in MSO2 (and therefore have linear-time algorithms on bounded treewidth graphs by the celebrated <PERSON><PERSON><PERSON><PERSON>'s theorem), but cannot be formulated in MSO1 (which would have yielded linear-time algorithms on bounded cliquewidth graphs by a well-known theorem of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and R<PERSON>). Each of these problems can be solved in time g(k)nf(k) on graphs of cliquewidth k. <PERSON> et al. [Intractability of Clique-Width Parameterizations. SIAM J. Comput. 39(5): 1941–1956 (2010)] showed that the running times cannot be improved to g(k)nO(1) assuming W[1]≠FPT. However, this does not rule out nontrivial improvements to the exponent f(k) in the running times. In a follow-up paper, <PERSON><PERSON><PERSON> et al. [Almost Optimal Lower Bounds for Problems Parameterized by Clique-Width. SIAM J. Comput. 43(5): 1541–1563 (2014)] improved the running times for EDS and MC to nO(k), and proved g(k)no(k) lower bounds for EDS, MC and HP assuming the ETH. Recently, <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON> [WADS 2017] gave an nO(k)-time algorithm for HP. Thus, prior to this work, EDS, MC and HP were known to have tight nΘ(k) algorithmic upper and lower bounds. In contrast, GC has an upper bound of nO(2k) and a lower bound of merely (implicit from the W[1]-hardness proof). In this paper, we close the gap for GC by proving a lower bound of n2o(k) This shows that GC behaves qualitatively different from the other three problems. To the best of our knowledge, GC is the first natural problem known to require exponential dependence on the parameter in the exponent of n.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.19"}, {"primary_key": "3510150", "vector": [], "sparse_vector": [], "title": "Planar Graphs as L-intersection or L-contact graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ⌞-intersection graphs are the graphs that have a representation as intersection graphs of axis-parallel ⌞ shapes in the plane. A subfamily of these graphs are {⌞, |, –}-contact graphs which are the contact graphs of axis parallel ⌞, |, and – shapes in the plane. We prove here two results that were conjectured by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> in 2013. We show that planar graphs are ⌞-intersection graphs, and that triangle-free planar graphs are {⌞, |, –}-contact graphs. These results are obtained by a new and simple decomposition technique for 4-connected triangulations. Our results also provide a much simpler proof of the known fact that planar graphs are segment intersection graphs.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.12"}, {"primary_key": "3510151", "vector": [], "sparse_vector": [], "title": "Separation in Correlation-Robust Monopolist Problem with Budget.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider a monopolist seller that has n heterogeneous items to sell to a single buyer. The seller's goal is to maximize her revenue. We study this problem in the correlation-robust framework recently proposed by <PERSON> [Econometrica 2017]. In this framework, the seller only knows marginal distributions for each separate item but has no information about correlation across different items in the joint distribution. Any mechanism is then evaluated according to its expected profit in the worst-case, over all possible joint distributions with given marginal distributions. <PERSON>'s main result states that in multi-item monopoly problem with buyer, whose value for a set of items is additive, the optimal correlation-robust mechanism should sell items separately.We use alternative dual Linear Programming formulation for the optimal correlation-robust mechanism design problem. This LP can be used to compute optimal mechanisms in general settings. We give an alternative proof for the additive monopoly problem without constructing worst-case distribution. As a surprising byproduct of our approach we get that separation result continues to hold even when buyer has a budget constraint on her total payment. Namely, the optimal robust mechanism splits the total budget in a fixed way across different items independent of the bids, and then sells each item separately with a respective per item budget constraint.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.135"}, {"primary_key": "3510152", "vector": [], "sparse_vector": [], "title": "Stochastic Load Balancing on Unrelated Machines.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We consider the problem of makespan minimization: i.e., scheduling jobs on machines to minimize the maximum load. For the deterministic case, good approximations are known even when the machines are unrelated. However, the problem is not well-understood when there is uncertainty in the job sizes. In our setting the job sizes are stochastic, i.e., the size of a job j on machine i is a random variable Xij, whose distribution is known. (Sizes of different jobs are independent of each other.) The goal is to find a fixed assignment of jobs to machines, to minimize the expected makespan—i.e., the expected value of the maximum load over the m machines. For the identical machines special case when the size of a job is the same across all machines, a constant-factor approximation algorithm has long been known. However, the problem has remained open even for the next-harder related machines case. Our main result is a constant-factor approximation for the most general case of unrelated machines. The main technical challenge we overcome is obtaining an efficiently computable lower bound for the optimal solution. We give an exponential-sized LP that we argue gives a strong lower bound. Then we show how to round any fractional solution to satisfy only a small subset of the constraints, which are enough to bound the expected makespan of our solution. We then consider two generalizations. The first is the budgeted makespan minimization problem, where the goal is to minimize the makespan subject to scheduling any subset of jobs whose reward is at least some target reward R. We extend our above result to a constant-factor approximation here using polyhedral properties of the bipartite matching polytope. The second problem is the q-norm minimization problem, where we want to minimize the expected ℓq-norm of the load vectors. Here we give an O(q/ log q)-approximation algorithm using a reduction to the deterministic q-norm problem with side constraints.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.83"}, {"primary_key": "3510153", "vector": [], "sparse_vector": [], "title": "An FPT Algorithm Beating 2-Approximation for k-Cut.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>"], "summary": "In the k-CuT problem, we are given an edge-weighted graph G and an integer k, and have to remove a set of edges with minimum total weight so that G has at least k connected components. Prior work on this problem gives, for all h ∊ [2, k], a (2 – h/k)-approximation algorithm for k-cut that runs in time nO(h). Hence to get a (2 – ε)-approximation algorithm for some absolute constant ε, the best runtime using prior techniques is nO(kε). Moreover, it was recently shown that getting a (2 – ε)-approximation for general k is NP-hard, assuming the Small Set Expansion Hypothesis.If we use the size of the cut as the parameter, an FPT algorithm to find the exact k-Cut is known, but solving the k-CuT problem exactly is W[1]-hard if we parameterize only by the natural parameter of k. An immediate question is: can we approximate k-Cut better in FPT-time, using k as the parameter?We answer this question positively. We show that for some absolute constant ε > 0, there exists a (– ε)-approximation algorithm that runs in time 2O(κ6) · Õ(n4). This is the first FPT algorithm that is parameterized only by k and strictly improves the 2-approximation.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.179"}, {"primary_key": "3510154", "vector": [], "sparse_vector": [], "title": "Coding against deletions in oblivious and online models.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON>"], "summary": "We consider binary error correcting codes when errors are deletions. A basic challenge concerning deletion codes is determining p0(adv), the zero-rate threshold of adversarial deletions, defined to be the supremum of all p for which there exists a code family with rate bounded away from 0 capable of correcting a fraction p of adversarial deletions. A recent construction of deletion-correcting codes [3] shows that , and the trivial upper bound, p0(adv) ≤ ½, is the best known. Perhaps surprisingly, we do not know whether or not p0(adv) = 1/2.In this work, to gain further insight into deletion codes, we explore two related error models: oblivious deletions and online deletions, which are in between random and adversarial deletions in power. In the oblivious model, the channel can inflict an arbitrary pattern of pn deletions, picked without knowledge of the codeword. We prove the existence of binary codes of positive rate that can correct any fraction p < 1 of oblivious deletions, establishing that the associated zero-rate threshold p0(obliv) equals 1.For online deletions, where the channel decides whether to delete bit xi based only on knowledge of bits x1x2 … xi, define the deterministic zero-rate threshold for online deletions p0(on, d) to be the supremum of p for which there exist deterministic codes against an online channel causing pn deletions with low average probability of error. That is, the probability that a randomly chosen codeword is decoded incorrectly is small. We prove p0(adv) = ½ if and only if p0(adv) = ½.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.41"}, {"primary_key": "3510155", "vector": [], "sparse_vector": [], "title": "On Separating Points by Lines.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Given a set P of n points in the plane, its separability is the minimum number of lines needed to separate all its pairs of points from each other. We show that the minimum number of lines needed to separate n points, picked randomly (and uniformly) in the unit square, is , where hides polylogarithmic factors. In addition, we provide a fast approximation algorithm for computing the separability of a given point set in the plane. Finally, we point out the connection between separability and partitions.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.59"}, {"primary_key": "3510156", "vector": [], "sparse_vector": [], "title": "Derandomized concentration bounds for polynomials, and hypergraph maximal independent set.", "authors": ["<PERSON>"], "summary": "A parallel algorithm for maximal independent set (MIS) in hypergraphs has been a long-standing algorithmic challenge, dating back nearly 30 years to a survey of <PERSON><PERSON> & <PERSON> (1990). Despite its apparent simplicity, there have been no general sub-polynomial-time algorithms or hardness reductions. The best randomized parallel algorithm for hypergraphs of fixed rank r was developed by <PERSON><PERSON> & <PERSON> (1990) and <PERSON><PERSON><PERSON> (1992), running in time roughly (log n)r!. The key probabilistic tool underlying this algorithm is a concentration bound for low-degree polynomials applied to independent input variables; this is a natural generalization of concentration bounds for sums of independent random variables, which are ubiquitous in combinatorics and computer science.These concentration bounds for polynomials do not lend themselves to standard derandomization techniques. Thus, the algorithm of <PERSON><PERSON>en cannot be derandomized in any known way. There are no deterministic parallel algorithms for hypergraph MIS for any fixed rank r > 3.We improve the randomized algorithm of <PERSON><PERSON><PERSON> to obtain a running time of (log n)2r. We also give a method for derandomizing concentration bounds for polynomials, thus obtaining a deterministic algorithm running in (log n)2r+3 time and (mn)O(1) processors. Our analysis can also apply when r is slowly growing; using this in conjunction with a strategy of <PERSON><PERSON><PERSON> et al. (2015) gives a deterministic MIS algorithm running in time .", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.141"}, {"primary_key": "3510157", "vector": [], "sparse_vector": [], "title": "Computing the Independence Polynomial: from the Tree Threshold down to the Roots.", "authors": ["<PERSON> J<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study an algorithm for approximating the multivariate independence polynomial Z(z), with negative and complex arguments. While the focus so far has been mostly on computing combinatorial polynomials restricted to the univariate positive setting (with seminal results for the independence polynomial by <PERSON><PERSON> (2006) and <PERSON><PERSON> (2010)), the independence polynomial with negative or complex arguments has strong connections to combinatorics and to statistical physics. The independence polynomial with negative arguments, Z(–p), determines the Shearer region, the maximal region of probabilities to which the Lovász Local Lemma (LLL) can be extended (<PERSON> 1985). In statistical physics, complex zeros of the independence polynomial relate to existence of phase transitions.Our main result is a deterministic algorithm to compute approximately the independence polynomial in any root-free complex polydisc centered at the origin. More precisely, we can (1 + ε)-approximate the independence polynomial Z(z) for an n-vertex graph of degree at most d, for any complex vector z such that Z(z′) ≠ 0 for |z′i| ≤ (1 + α)|zi|, in running time . Our result also extends to graphs of unbounded degree that have a bounded connective constant. Our algorithm is essentially the same as <PERSON><PERSON>'s algorithm for positive parameters up to the tree uniqueness threshold. The core of the analysis is a novel multivariate form of the correlation decay technique, which can handle non-uniform complex parameters. In summary, we provide a unifying algorithm for all known regions where Z(z) is approximately computable. In particular, in the univariate real setting our work implies that <PERSON><PERSON>'s algorithm works in an interval between two critical points (−λ′c(d), λc(d)), and outside of this interval an approximation of Z(λ) is known to be NP-hard.As an application, we provide an algorithm to test membership in Shearer's region within a multiplicative error of 1 + α, in running time . We also give a deterministic algorithm for Shearer's lemma (extending the LLL) with n events on m independent variables under slack α, with running time .On the hardness side, we prove that evaluating Z(z) at an arbitrary point in Shearer's region, and testing membership in Shearer's region, are #P-hard problems. For Weitz's correlation decay technique in the negative regime, we show that the dependence in the exponent is optimal.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.102"}, {"primary_key": "3510158", "vector": [], "sparse_vector": [], "title": "Approximate Local Decoding of Cubic Reed-Muller Codes Beyond the List Decoding Radius.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider the question of decoding Reed-Muller codes over beyond their list-decoding radius. Since, by definition, in this regime one cannot demand an efficient exact listdecoder, we seek an approximate decoder: Given a word F and radii r' > r > 0, the goal is to output a codeword within radius r' of F, if there exists a codeword within distance r. As opposed to the list decoding problem, it suffices here to output any codeword with this property, since the list may be too large if r exceeds the list decoding radius.Prior to our work, such decoders were known for Reed-Muller codes of degree 2, due to works of <PERSON> and the second author [FOCS 2011]. In this work we make the first progress on this problem for the degree 3 where the list decoding radius is 1/8. We show that there is a constant and an efficient approximate decoder, that given query access to a function , such that F is within distance r = δ – ε from a cubic polynomial, runs in time polynomial in message length and outputs with high probability a cubic polynomial which is at distance at most r' = 1/2 – ε' from F, where ε' is a quasi polynomial function of ε.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.43"}, {"primary_key": "3510159", "vector": [], "sparse_vector": [], "title": "Dynamic Bridge-Finding in Õ(log2 n) Amortized Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a deterministic fully-dynamic data structure for maintaining information about the bridges in a graph. We support updates in Õ((log n)2) amortized time, and can find a bridge in the component of any given vertex, or a bridge separating any two given vertices, in O(log n/ log log n) worst case time. Our bounds match the current best for bounds for deterministic fully-dynamic connectivity up to log log n factors. The previous best dynamic bridge finding was an Õ((log n)3) amortized time algorithm by <PERSON><PERSON> [STOC2000], which was a bittrick-based improvement on the O((log n)4) amortized time algorithm by <PERSON><PERSON> et al.[STOC98, JACM2001]. Our approach is based on a different and purely combinatorial improvement of the algorithim of <PERSON><PERSON> et al., which by itself gives a new combinatorial Õ((log n)3) amortized time algorithm. Combining it with <PERSON><PERSON>'s bittrick, we get down to the claimed Õ((log n)2) amortized time. Essentially the same new trick can be applied to the biconnectivity data structure from [STOC98, JACM2001], improving the amortized update time to Õ((log n)3). We also offer improvements in space. We describe a general trick which applies to both of our new algorithms, and to the old ones, to get down to linear space, where the previous best use O(m + n log n log log n). Our result yields an improved running time for deciding whether a unique perfect matching exists in a static graph.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.3"}, {"primary_key": "3510160", "vector": [], "sparse_vector": [], "title": "A fast generalized DFT for finite groups of Lie type.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give an arithmetic algorithm using O(|G|ω/2+o(1)) operations to compute the generalized Discrete Fourier Transform (DFT) over group G for finite groups of Lie type, including the linear, orthogonal, and symplectic families and their variants, as well as all finite simple groups of Lie type. Here ω is the exponent of matrix multiplication, so the exponent ω/2 is optimal if ω = 2.Previously, \"exponent one\" algorithms were known for supersolvable groups and the symmetric and alternating groups. No exponent one algorithms were known (even under the assumption ω = 2) for families of linear groups of fixed dimension, and indeed the previous best-known algorithm for SL2(Fq) had exponent 4/3 despite being the focus of significant effort. We unconditionally achieve exponent at most 1.19 for this group, and exponent one if ω = 2.We also show that ω = 2 implies a exponent for general finite groups G, which beats the longstanding previous best upper bound (assuming ω = 2) of 3/2.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.68"}, {"primary_key": "3510161", "vector": [], "sparse_vector": [], "title": "Near Optimal Jointly Private Packing Algorithms via Dual Multiplicative Weight Update.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present an improved (ε, δ-jointly differentially private algorithm for packing problems. Our algorithm gives a feasible output that is approximately optimal up to an αn additive factor as long as the supply of each resource is at least , where m is the number of resources. This improves the previous result by <PERSON><PERSON> et al. (SODA '16), which requires the total supply to be at least Õ(m2/αε), and only guarantees approximate feasibility in terms of total violation. Further, we complement our algorithm with an almost matching hardness result, showing that supply is necessary for any (ε, δ)-jointly differentially private algorithm to compute an approximately optimal packing solution. Finally, we introduce an alternative approach that runs in linear time, is exactly truthful, can be implemented online, and can be ε-jointly differentially private, but requires a larger supply of each resource.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.24"}, {"primary_key": "3510162", "vector": [], "sparse_vector": [], "title": "Set Cover in Sub-linear Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the classic set cover problem from the perspective of sub-linear algorithms. Given access to a collection of m sets over n elements in the query model, we show that sub-linear algorithms derived from existing techniques have almost tight query complexities.On one hand, first we show an adaptation of the streaming algorithm presented in [17] to the sub-linear query model, that returns an α-approximate cover using Õ(m(n/k)1/(α–1) + nk) queries to the input, where k denotes the value of a minimum set cover. We then complement this upper bound by proving that for lower values of k, the required number of queries is , even for estimating the optimal cover size. Moreover, we prove that even checking whether a given collection of sets covers all the elements would require Ω(nk) queries. These two lower bounds provide strong evidence that the upper bound is almost tight for certain values of the parameter k.On the other hand, we show that this bound is not optimal for larger values of the parameter k, as there exists a (1 + ε)-approximation algorithm with Õ(mn/kε2) queries. We show that this bound is essentially tight for sufficiently small constant ε, by establishing a lower bound of query complexity.Our lower-bound results follow by carefully designing two distributions of instances that are hard to distinguish. In particular, our first lower bound involves a probabilistic construction of a certain set system with a minimum set cover of size αk, with the key property that a small number of \"almost uniformly distributed\" modifications can reduce the minimum set cover size down to k. Thus, these modifications are not detectable unless a large number of queries are asked. We believe that our probabilistic construction technique might find applications to lower bounds for other combinatorial optimization problems.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.158"}, {"primary_key": "3510163", "vector": [], "sparse_vector": [], "title": "Algorithms based on *-algebras, and their applications to isomorphism of polynomials with one secret, group isomorphism, and polynomial identity testing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider two basic algorithmic problems concerning tuples of (skew-)symmetric matrices. The first problem asks to decide, given two tuples of (skew-)symmetric matrices (B1, …, Bm) and (C1, …, Cm), whether there exists an invertible matrix A such that for every i ∊ {1, …, m}, AtBiA = Ci. We show that this problem can be solved in randomized polynomial time over finite fields of odd size, the reals, and the complex numbers. The second problem asks to decide, given a tuple of square matrices (B1, …, Bm), whether there exist invertible matrices A and D, such that for every i ∊ {1, …, m}, ABiD is (skew-)symmetric. We show that this problem can be solved in deterministic polynomial time over fields of characteristic not 2. For both problems we exploit the structure of the underlying *-algebras (algebras with an involutive antiautomorphism), and utilize results and methods from the module isomorphism problem.Applications of our results range from multivariate cryptography, group isomorphism, to polynomial identity testing. Specifically, these results imply efficient algorithms for the following problems. (1) Test isomorphism of quadratic forms with one secret over a finite field of odd size. This problem belongs to a family of problems that serves as the security basis of certain authentication schemes proposed by <PERSON><PERSON><PERSON> (<PERSON><PERSON>ry<PERSON> 1996). (2) Test isomorphism of p-groups of class 2 and exponent p (p odd) with order pℓ in time polynomial in the group order, when the commutator subgroup is of order . (3) Deterministically reveal two families of singularity witnesses caused by the skew-symmetric structure. This represents a natural next step for the polynomial identity testing problem, in the direction set up by the recent resolution of the non-commutative rank problem (Garg-Gurvits-Oliveira-Wigderson, FOCS 2016; Ivanyos-Qiao-Subrahmanyam, ITCS 2017).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.152"}, {"primary_key": "3510164", "vector": [], "sparse_vector": [], "title": "Efficient Õ(n/∊) Spectral Sketches for the Laplacian and its Pseudoinverse.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we consider the problem of efficiently computing ∊-sketches for the Laplacian and its pseudoinverse. Given a Laplacian and an error tolerance ∊, we seek to construct a function f such that for any vector x (chosen obliviously from f), with high probability (1 – ∊)x⊤ Ax ≤ f(x) ≤ (1 + ∊)x⊤ Ax where A is either the Laplacian or its pseudoinverse. Our goal is to construct such a sketch f efficiently and to store it in the least space possible.We provide nearly-linear time algorithms that, when given a Laplacian matrix ℒ ∊ ℝn×n and an error tolerance ∊, produce Õ(n/∊)-size sketches of both ℒ and its pseudoinverse. Our algorithms improve upon the previous best sketch size of Õ(n/∊1.6) for sketching the Laplacian form by [1] and O(n/∊2) for sketching the Laplacian pseudoinverse by [2].Furthermore we show how to compute all-pairs effective resistances from our Õ(n/∊) size sketch in Õ(n2/∊) time. This improves upon the previous best running time of Õ(n2/∊2) by [3].", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.159"}, {"primary_key": "3510166", "vector": [], "sparse_vector": [], "title": "MST in O(1) Rounds of Congested Clique.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a distributed randomized algorithm finding Minimum Spanning Tree (MST) of a given graph in O(1) rounds, with high probability, in the congested clique model.The input graph in the congested clique model is a graph of n nodes, where each node initially knows only its incident edges. The communication graph is a clique with limited edge bandwidth: each two nodes (not necessarily neighbours in the input graph) can exchange O(log n) bits.As in previous works, the key part of the MST algorithm is an efficient Connected Components (CC) algorithm. However, unlike the former approaches, we do not aim at simulating the standard <PERSON><PERSON><PERSON><PERSON>'s algorithm, at least at initial stages of the CC algorithm. Instead, we develop a new technique which combines connected components of sample sparse subgraphs of the input graph in order to accelerate the process of uncovering connected components of the original input graph. More specifically, we develop a sparsification technique which reduces an initial CC problem in O(1) rounds to its two restricted instances. The former instance has a graph with maximal degree O(log log n) as the input – here our sample-combining technique helps. In the latter instance, a partition of the input graph into O(n/ log log n) connected components is known. This gives an opportunity to apply previous algorithms to determine connected components in O(1) rounds.Our result addresses a problem proposed by <PERSON><PERSON> et al. [SPAA 2003; SICOMP 2005] and improves over previous O(log* n) algorithm of <PERSON><PERSON><PERSON><PERSON> et al. [PODC 2016], and O(log log log n) algorithm of <PERSON><PERSON><PERSON> et al. [PODC 2015]. It also determines Θ(1) round complexity in the congested clique for MST, as well as other graph problems, including bipartiteness, cut verification, s-t connectivity, and cycle containment.MSC codescongested cliqueconnected componentsminimum spanning treerandomized algorithmsbroadcastunicast", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.167"}, {"primary_key": "3510167", "vector": [], "sparse_vector": [], "title": "Decrementai Transitive Closure and Shortest Paths for Planar Digraphs and Beyond.", "authors": ["<PERSON>"], "summary": "In this paper we show that the tools used to obtain the best state-of-the-art decremental algorithms for reachability and approximate shortest paths in directed graphs can be successfully combined with the existence of small separators in certain graph classes.In particular, for graph classes admitting balanced separators of size , such as planar, bounded-genus and minor-free graphs, we show that for both transitive closure and (1 + ε)-approximate all pairs shortest paths (where ∊ is constant), there exist decremental algorithms with Õ(n3/2) total update time and worst-case query time.Additionally, for the case of planar graphs, we show that for any t ∊ [1, n], there exists a decremental transitive closure algorithm with Õ(n2/t) total update time and worst-case query time. In particular, for t = n2/3, if all the edges are eventually deleted, we obtain Õ(n1/3) amortized update and query times.Most of the algorithms we obtain are correct with high probability against an oblivious adversary.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.5"}, {"primary_key": "3510168", "vector": [], "sparse_vector": [], "title": "A Polynomial Excluded-Minor Approximation of Treedepth.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Treedepth is a well-studied graph invariant in the family of \"width measures\" that includes treewidth and pathwidth. Understanding these invariants in terms of excluded minors has been an active area of research. The recent Grid Minor Theorem of Chekuri and Chuzhoy [12] establishes that treewidth is polynomially approximated by the largest k × k grid minor. In this paper, we give a similar polynomial excluded-minor approximation for treedepth in terms of three basic obstructions: grids, tree, and paths. Specifically, we show that there is a constant c such that every graph of treedepth ≥ kc contains one of the following minors (each of treedepth ≥ k):•the k × k grid,•the complete binary tree of height k,•the path of order 2k.Let us point out that we cannot drop any of the above graphs for our purpose. Moreover, given a graph G we can, in randomized polynomial time, find either an embedding of one of these minors or conclude that treedepth of G is at most kc.This result has potential applications in a variety of settings where bounded treedepth plays a role. In addition to some graph structural applications, we describe a surprising application in circuit complexity and finite model theory from recent work of the second author [28].", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.17"}, {"primary_key": "3510169", "vector": [], "sparse_vector": [], "title": "Conflict-Free Coloring of Intersection Graphs of Geometric Objects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In FOCS'2002, <PERSON> et al. introduced and studied the notion of conflict-free colorings of geometrically defined hypergraphs. They motivated it by frequency assignment problems in cellular networks. This notion has been extensively studied since then.A conflict-free coloring of a graph is a coloring of its vertices such that the neighborhood (pointed or closed) of each vertex contains a vertex whose color differs from the colors of all other vertices in that neighborhood. In this paper we study conflict-free colorings of intersection graphs of geometric objects. We show that any intersection graph of n pseudo-discs in the plane admits a conflict-free coloring with O(log n) colors, with respect to both closed and pointed neighborhoods. We also show that the latter bound is asymptotically sharp.Using our methods, we obtain the following strengthening of the two main results of <PERSON> et al.: Any family F of n discs in the plane can be colored with O(log n) colors in such a way that for any disc B in the plane, the set of discs in F that intersect B contains a uniquely-colored element. Moreover, such a coloring can be computed deterministically in polynomial time. In view of the original motivation to study such colorings, this strengthening suggests further applications to frequency assignment in wireless networks.Finally, we present bounds on the number of colors needed for conflict-free colorings of other classes of intersection graphs, including intersection graphs of axis-parallel rectangles and of ρ-fat objects in the plane.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.154"}, {"primary_key": "3510170", "vector": [], "sparse_vector": [], "title": "Erdős-Pósa property of chordless cycles and its applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "A chordless cycle in a graph G is an induced subgraph of G which is a cycle of length at least four. We prove that the Erdős-Pósa property holds for chordless cycles, which resolves the major open question concerning the Erdős-Pós<PERSON> property. Our proof for chordless cycles is constructive: in polynomial time, one can find either k + 1 vertex-disjoint chordless cycles, or ck2 log k vertices hitting every chordless cycle for some constant c. It immediately implies an approximation algorithm of factor O(opt log opt) for Chordal Vertex Deletion. We complement our main result by showing that chordless cycles of length at least ℓ for any fixed ℓ ≥ 5 do not have the Erdős-Pósa property.As a corollary, for a non-negative integral function w defined on the vertex set of a graph G, the minimum value Σx∊S w(x) over all vertex sets S hitting all cycles of G is at most O(k2 log k) where k is the maximum number of cycles (not necessarily vertex-disjoint) in G such that each vertex υ is used at most w(υ) times.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.109"}, {"primary_key": "3510171", "vector": [], "sparse_vector": [], "title": "The Entropy of Backwards Analysis.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Backwards analysis, first popularized by <PERSON><PERSON><PERSON>, is often the simplest most elegant way of analyzing a randomized algorithm. It applies to incremental algorithms where elements are added incrementally, following some random permutation, e.g., incremental Delauney triangulation of a pointset, where points are added one by one, and where we always maintain the Delauney triangulation of the points added thus far. For backwards analysis, we think of the permutation as generated backwards, implying that the ith point in the permutation is picked uniformly at random from the i points not picked yet in the backwards direction. Backwards analysis has also been applied elegantly by <PERSON> to the randomized linear time minimum spanning tree algorithm of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.The question considered in this paper is how much randomness we need in order to trust the expected bounds obtained using backwards analysis, exactly and approximately. For the exact case, it turns out that a random permutation works if and only if it is minwise, that is, for any given subset, each element has the same chance of being first. Minwise permutations are known to have Φ(n) entropy, and this is then also what we need for exact backwards analysis.However, when it comes to approximation, the two concepts diverge dramatically. To get backwards analysis to hold within a factor α, the random permutation needs entropy Ω(n/α). This contrasts with minwise permutations, where it is known that a 1 + ε approximation only needs Φ(log(n/ε)) entropy. Our negative result for backwards analysis essentially shows that it is as abstract as any analysis based on full randomness.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.56"}, {"primary_key": "3510173", "vector": [], "sparse_vector": [], "title": "Syndrome decoding of Reed-Muller codes and tensor decomposition over finite fields.", "authors": ["Swas<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reed-Muller codes are some of the oldest and most widely studied error-correcting codes, of interest for both their algebraic structure as well as their many algorithmic properties. A recent beautiful result of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and Volk [SSV17] showed that for binary Reed-Muller codes of length n and distance d = O(1), one can correct polylog(n) random errors in poly(n) time (which is well beyond the worst-case error tolerance of O(1)).In this paper, we consider the problem of syndrome decoding Reed-Muller codes from random errors. More specifically, given the polylog(n)-bit long syndrome vector of a codeword corrupted in polylog(n) random coordinates, we would like to compute the locations of the codeword corruptions. This problem turns out to be equivalent to a basic question about computing tensor decomposition of random low-rank tensors over finite fields.Our main result is that syndrome decoding of Reed-Muller codes (and the equivalent tensor decomposition problem) can be solved efficiently, i.e., in polylog(n) time. We give two algorithms for this problem:1.The first algorithm is a finite field variant of a classical algorithm for tensor decomposition over real numbers due to <PERSON><PERSON><PERSON>. This also gives an alternate proof for the main result of [SSV17].2.The second algorithm is obtained by implementing the steps of [SSV17]'s Berlekamp-Welch-style decoding algorithm in sublinear-time. The main new ingredient is an algorithm for solving certain kinds of systems of polynomial equations.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.44"}, {"primary_key": "3510174", "vector": [], "sparse_vector": [], "title": "Ergodic Effects in Token Circulation.", "authors": ["<PERSON>", "Przemys<PERSON>nanski"], "summary": "We consider a dynamical process in a network which distributes all particles (tokens) located at a node among its neighbors, in a round-robin manner.We show that in the recurrent state of this dynamics (i.e., disregarding a polynomially long initialization phase of the system), the number of particles located on a given edge, averaged over an interval of time, is tightly concentrated around the average particle density in the system. Formally, for a system of k particles in a graph of m edges, during any interval of length T, this time-averaged value is k/m±Õ(1/T), whenever gcd(m, k) = Õ(1) (and so, e.g., whenever m is a prime number). To achieve these bounds, we link the behavior of the studied dynamics to ergodic properties of traversals based on Eulerian circuits on a symmetric directed graph. These results are proved through sum set methods and are likely to be of independent interest.As a corollary, we also obtain bounds on the idleness of the studied dynamics, i.e., on the longest possible time between two consecutive appearances of a token on an edge, taken over all edges. Designing trajectories for k tokens in a way which minimizes idleness is fundamental to the study of the patrolling problem in networks. Our results immediately imply a bound of Õ(m/k) on the idleness of the studied process, showing that it is a distributed Õ(1)-competitive solution to the patrolling task, for all of the covered cases. Our work also provides some further insights that may be interesting in load-balancing applications.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.170"}, {"primary_key": "3510175", "vector": [], "sparse_vector": [], "title": "Lempel-Ziv: a &quot;one-bit catastrophe&quot; but not a tragedy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The so-called \"one-bit catastrophe\" for the compression algorithm LZ'78 asks whether the compression ratio of an infinite word can change when a single bit is added in front of it. We answer positively this open question raised by <PERSON><PERSON> and others: we show that there exists an infinite word w such that ρsup(w) = 0 but ρinf (0w) > 0, where ρsup and ρinf are respectively the lim sup and the lim inf of the compression ratios ρ of the prefixes (Theorem 2.1).To that purpose we explore the behaviour of LZ'78 on finite words and show the following results:•• There is a constant C > 0 such that, for any finite word w and any letter . Thus, sufficiently compressible words (ρ(w) = o(1/ log |w|)) remain compressible with a letter in front (Theorem 2.2);•• The previous result is tight up to a multiplicative constant for any compression ratio ρ(w) = O(1/ log |w|) (Theorem 2.4). In particular, there are infinitely many words w satisfying ρ(w) = O(1/log |w|) but ρ(0w) = Ω(1).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.97"}, {"primary_key": "3510176", "vector": [], "sparse_vector": [], "title": "Online Facility Location against a t-Bounded Adversary.", "authors": ["<PERSON>"], "summary": "In the streaming model, the order of the stream can significantly affect the difficulty of a problem. A t-semirandom stream was introduced as an interpolation between random-order (t = 1) and adversarial-order (t = n) streams where an adversary intercepts a random-order stream and can delay up to t elements at a time. IITK Sublinear Open Problem #15 asks to find algorithms whose performance degrades smoothly as t increases. We show that the celebrated online facility location algorithm achieves an expected competitive ratio of . We present a matching lower bound that any randomized algorithm has an expected competitive ratio of .We use this result to construct an O(1)-approximate streaming algorithm for k-median clustering that stores O(k log t) points and has O(k log t) worst-case update time. Our technique generalizes to any dissimilarity measure that satisfies a weak triangle inequality, including k-means, M-estimators, and ℓp norms. The special case t = 1 yields an optimal O(k) space algorithm for random-order streams as well as an optimal O(nk) time algorithm in the RAM model, closing a long line of research on this problem.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.65"}, {"primary_key": "3510177", "vector": [], "sparse_vector": [], "title": "Subquadratic Kernels for Implicit 3-Hitting Set and 3-Set Packing Problems.", "authors": ["Tien-Nam Le", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider four well-studied NP-complete packing/covering problems on graphs: Feedback Vertex Set in Tournaments (FVST), Cluster Vertex Deletion (CVD), Triangle Packing in Tournaments (TPT) and Induced P3-Packing. For these four problems kernels with O(k2) vertices have been known for a long time. In fact, such kernels can be obtained by interpreting these problems as finding either a packing of k pairwise disjoint sets of size 3 (3-Set Packing) or a hitting set of size at most k for a family of sets of size at most 3 (3-Hitting Set). In this paper, we give the first kernels for FVST, CVD, TPT and Induced P3-Packing with a subquadratic number of vertices. Specifically, we obtain the following results.•• FVST admits a kernel with vertices.•• CVD admits a kernel with vertices.•• TPT admits a kernel with vertices.•• Induced P3-Packing admits a kernel with vertices.Our results resolve an open problem from WorKer 2010 on the existence of kernels with 𝒪(k2–ε) vertices for FVST and CVD. All of our results are based on novel uses of old and new \"expansion lemmas\", and a weak form of crown decomposition where (i) almost all of the head is used by the solution (as opposed to all), (ii) almost none of the crown is used by the solution (as opposed to none), and (iii) if H is removed from G, then there is almost no interaction between the head and the rest (as opposed to no interaction at all).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.23"}, {"primary_key": "3510178", "vector": [], "sparse_vector": [], "title": "Kirchhoff Index as a Measure of Edge Centrality in Weighted Networks: Nearly Linear Time Algorithms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Most previous work of centralities focuses on metrics of vertex importance and methods for identifying powerful vertices, while related work for edges is much lesser, especially for weighted networks, due to the computational challenge. In this paper, we propose to use the well-known Kirchhoff index as the measure of edge centrality in weighted networks, called $\\theta$-Kirchhoff edge centrality. The Kirchhoff index of a network is defined as the sum of effective resistances over all vertex pairs. The centrality of an edge $e$ is reflected in the increase of Kirchhoff index of the network when the edge $e$ is partially deactivated, characterized by a parameter $\\theta$. We define two equivalent measures for $\\theta$-Kirchhoff edge centrality. Both are global metrics and have a better discriminating power than commonly used measures, based on local or partial structural information of networks, e.g. edge betweenness and spanning edge centrality. Despite the strong advantages of Kirchhoff index as a centrality measure and its wide applications, computing the exact value of Kirchhoff edge centrality for each edge in a graph is computationally demanding. To solve this problem, for each of the $\\theta$-Kirchhoff edge centrality metrics, we present an efficient algorithm to compute its $\\epsilon$-approximation for all the $m$ edges in nearly linear time in $m$. The proposed $\\theta$-Kirchhoff edge centrality is the first global metric of edge importance that can be provably approximated in nearly-linear time. Moreover, according to the $\\theta$-Kirchhoff edge centrality, we present a $\\theta$-Kirchhoff vertex centrality measure, as well as a fast algorithm that can compute $\\epsilon$-approximate Kirchhoff vertex centrality for all the $n$ vertices in nearly linear time in $m$.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.153"}, {"primary_key": "3510179", "vector": [], "sparse_vector": [], "title": "Tight Hardness for Shortest Cycles and Paths in Sparse Graphs.", "authors": ["<PERSON>", "Virginia Vassilevska Williams", "<PERSON><PERSON>"], "summary": "Fine-grained reductions have established equivalences between many core problems with Õ(n3)-time algorithms on n-node weighted graphs, such as Shortest Cycle, All-Pairs Shortest Paths (APSP), Radius, Replacement Paths, Second Shortest Paths, and so on. These problems also have Õ(mn)-time algorithms on m-edge n-node weighted graphs, and such algorithms have wider applicability. Are these mn bounds optimal when m 0 for any of the below problems•• Minimum Weight (2ℓ + 1)-Cycle in a directed weighted graph,•• Shortest Cycle in a directed weighted graph,•• APSP in a directed or undirected weighted graph,•• Radius (or Eccentricities) in a directed or undirected weighted graph,•• Wiener index of a directed or undirected weighted graph,•• Replacement Paths in a directed weighted graph,•• Second Shortest Path in a directed weighted graph,•• Betweenness Centrality of a given node in a directed weighted graph.That is, we prove hardness for a variety of sparse graph problems from the hardness of a dense graph problem. Our results also lead to new conditional lower bounds from several related hypothesis for unweighted sparse graph problems including k-cycle, shortest cycle, Radius, Wiener index and APSP.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.80"}, {"primary_key": "3510180", "vector": [], "sparse_vector": [], "title": "On the Competition Complexity of Dynamic Mechanism Design.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "The Competition Complexity of an auction measures how much competition is needed for the revenue of a simple auction to surpass the optimal revenue. A classic result from auction theory by <PERSON><PERSON><PERSON> and <PERSON><PERSON> [11], states that the Competition Complexity of VCG, in the case of n i.i.d. buyers and a single item, is 1. In other words, it is better to invest in recruiting one extra buyer and run a second price auction than to invest in learning exactly the buyers' underlying distribution and run the revenue-maximizing auction tailored to this distribution.In this paper we study the Competition Complexity of dynamic auctions. Consider the following problem: a monopolist is auctioning off m items in m consecutive stages to n interested buyers. A buyer realizes her value for item k in the beginning of stage k. How many additional buyers are necessary and sufficient for a second price auction at each stage to extract revenue at least that of the optimal dynamic auction? We prove that the Competition Complexity of dynamic auctions is at most 3n - and at least linear in n - even when the buyers' values are correlated across stages, under a monotone hazard rate assumption on the stage (marginal) distributions. This assumption can be relaxed if one settles for independent stages. We also prove results on the number of additional buyers necessary for VCG at every stage to be an α-approximation of the optimal revenue; we term this number the α-approximate Competition Complexity. For example, under the same mild assumptions on the stage distributions we prove that one extra buyer suffices for a -approximation. As a corollary we provide the first results on prior-independent dynamic auctions. This is, to the best of our knowledge, the first nontrivial positive guarantees for simple ex-post IR dynamic auctions for correlated stages.A key step towards proving bounds on the Competition Complexity is getting a good benchmark/upper bound to the optimal revenue. To this end, we extend the recent duality framework of [14] to dynamic settings. As an aside to our approach we obtain a revenue non-monotonicity lemma for dynamic auctions, which may be of independent interest.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.131"}, {"primary_key": "3510181", "vector": [], "sparse_vector": [], "title": "The complexity of independent set reconfiguration on bipartite graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We settle the complexity of the Independent Set Reconfiguration problem on bipartite graphs under all three commonly studied reconfiguration models. We show that under the token jumping or token addition/removal model the problem is NP-complete. For the token sliding model, we show that the problem remains PSPACE-complete.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.13"}, {"primary_key": "3510182", "vector": [], "sparse_vector": [], "title": "Beating Brute Force for (Quantified) Satisfiability of Circuits of Bounded Treewidth.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We investigate the algorithmic properties of circuits of bounded treewidth. Here the treewidth of a circuit C is defined as the treewidth of the underlying undirected graph of C, after the vertices corresponding to input gates have been removed. Thus, boolean formulae correspond to circuits of treewidth 1.•Our first main result is an algorithm for counting the number of satisfying assignments of circuits with n input gates, treewidth ω, and at most s · n gates. The running time of our algorithm is , which for formulae instantiates to 2n(1–1/O(s)). This is the first algorithm to achieve exponential speedup over brute force for the satisfiability of linear size circuits with treewidth bounded by a constant greater than 1. For treewidth 1, i.e., boolean formulae, our algorithm significantly outperforms the previously fastest 2n(1-1/O(s2)) time satisfiability algorithm by <PERSON><PERSON><PERSON> [32].•Our second main result is an algorithm for True Quantified Boolean Circuit Satisfiability for circuits of treewidth ω, in which every input gate has fanout at most s. The running time of our algorithm is . Our algorithm is the first to achieve exponential speed-up over brute force for such circuits. Indeed, even for quantified boolean formulae where every variable appears at most s times, the previously best known algorithm by <PERSON><PERSON><PERSON> [32] has running time 2n(1–1/O(f(s)log n)).•Utilizing the structural properties of low treewidth circuits which helped us obtain improved exponential-time algorithms for satisfiability, we also show that the number of wires of any constant treewidth circuit that computes the majority function must be super-linear.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.18"}, {"primary_key": "3510183", "vector": [], "sparse_vector": [], "title": "Covering Small Independent Sets and Separators with Applications to Parameterized Algorithms.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present two new combinatorial tools for the design of parameterized algorithms. The first is a simple linear time randomized algorithm that given as input a d-degenerate graph G and an integer k, outputs an independent set Y, such that for every independent set X in G of size at most k, the probability that X is a subset of Y is at least . The second is a new (deterministic) polynomial time graph sparsification procedure that given a graph G, a set T = {{s1, t1}, {s2, t2}, …, {sℓ, tℓ}} of terminal pairs and an integer k, returns an induced subgraph G* of G that maintains all the inclusion minimal multicuts of G of size at most k, and does not contain any (k + 2)-vertex connected set of size 2O(k). In particular, G* excludes a clique of size 2O(k) as a topological minor. Put together, our new tools yield new randomized fixed parameter tractable (FPT) algorithms for Stable s-t Separator, Stable Odd Cycle Transversal and Stable Multicut on general graphs, and for Stable Directed Feedback Vertex Set on d-degenerate graphs, resolving two problems left open by <PERSON> et al. [ACM Transactions on Algorithms, 2013]. All of our algorithms can be derandomized at the cost of a small overhead in the running time.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.177"}, {"primary_key": "3510184", "vector": [], "sparse_vector": [], "title": "When Recursion is Better than Iteration: A Linear-Time Algorithm for Acyclicity with Few Error Vertices.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Planarity, bipartiteness and (directed) acyclicity are basic graph properties with classic linear time recognition algorithms. However, the problems of testing whether a given (di)graph has k vertices whose deletion makes it planar, bipartite or a directed acyclic graph (DAG) are all fundamental NP-complete problems when k is part of the input. As a result, a significant amount of research has been devoted to understanding whether, for every fixed k, these problems admit a polynomial time algorithm (where the exponent in the polynomial is independent of k) and in particular, whether they admit linear time algorithms.While we now know that for any fixed k, we can test in linear time whether a graph is k vertices away from being planar [FOCS 2009, SODA 2014] or bipartite [SODA 2014, SICOMP 2016], the best known algorithms in the case of directed acyclicity are the algorithm of Garey and Tarjan [IPL 78] which runs in time O(nk–1m) and the algorithm of <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON> [JACM 2008] which runs in time O(k!4kk4nm). In other words, it has remained open whether it is possible to recognize in linear time, a graph which is 2 vertices away from being acyclic!In this paper, we settle this question by giving an algorithm that decides whether a given graph is k vertices away from being acyclic, in time O(k!4kk5(n + m)). That is, for every fixed k, our algorithm runs in time O(m + n), thus mirroring the case for planarity and bipartiteness.Our algorithm is designed via a general methodology that shaves off a factor of n from some algorithms that use the powerful technique of iterative compression. The two main features of our methodology are: (i) This is the first generic technique for designing linear time algorithms for directed cut-problems and (ii) it can be used in combination with future improvements in algorithms for the compression version of other well-studied cut-problems such as Multicut and Directed Subset Feedback Vertex Set.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.125"}, {"primary_key": "3510185", "vector": [], "sparse_vector": [], "title": "Probabilistic Existence of Large Sets of Designs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "A new probabilistic technique for establishing the existence of certain regular combinatorial structures has been recently introduced by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (STOC 2012). Using this technique, it can be shown that under certain conditions, a randomly chosen structure has the required properties of a t-(n, k, λ) combinatorial design with tiny, yet positive, probability.Herein, we strengthen both the method and the result of <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> as follows. We modify the random choice and the analysis to show that, under the same conditions, not only does a t-(n, k, λ) design exist but, in fact, with positive probability there exists a large set of such designs — that is, a partition of the set of k-subsets of [n] into t-(n, k, λ) designs. Specifically, using the probabilistic approach derived herein, we prove that for all sufficiently large n, large sets of t-(n, k, λ) designs exist whenever k > 9t and the necessary divisibility conditions are satisfied. This resolves the existence conjecture for large sets of designs for all k > 9t.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.101"}, {"primary_key": "3510186", "vector": [], "sparse_vector": [], "title": "The Robust Sensitivity of Boolean Functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The sensitivity conjecture is one of the central open problems in Boolean complexity. A recent work of <PERSON><PERSON><PERSON> et al. [CCC 2016] conjectured a robust analog of the sensitivity conjecture, which relates the decay of the Fourier mass of a Boolean function to moments of its sensitivity. We prove the robust sensitivity conjecture in this work with near optimal parameters.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.119"}, {"primary_key": "3510187", "vector": [], "sparse_vector": [], "title": "Cycles in Adversarial Regularized Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Regularized learning is a fundamental technique in online optimization, machine learning, and many other fields of computer science. A natural question that arises in this context is how regularized learning algorithms behave when faced against each other. We study a natural formulation of this problem by coupling regularized learning dynamics in zero-sum games. We show that the system's behavior is Poincaré recurrent, implying that almost every trajectory revisits any (arbitrarily small) neighborhood of its starting point infinitely often. This cycling behavior is robust to the agents’ choice of regularization mechanism (each agent could be using a different regularizer), to positive-affine transformations of the agents’ utilities, and it also persists in the case of networked competition (zero-sum polymatrix games).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.172"}, {"primary_key": "3510188", "vector": [], "sparse_vector": [], "title": "Embeddability in ℝ3 is NP-hard.", "authors": ["<PERSON><PERSON><PERSON>", "Yo&apos;av <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We prove that the problem of deciding whether a 2- or 3-dimensional simplicial complex embeds into ℝ3 is NP-hard. This stands in contrast with the lower dimensional cases which can be solved in linear time, and a variety of computational problems in ℝ3 like unknot or 3-sphere recognition which are in NP ∩ co-NP (assuming the generalized <PERSON>iemann hypothesis). Our reduction encodes a satisfiability instance into the embeddability problem of a 3-manifold with boundary tori, and relies extensively on techniques from low-dimensional topology, most importantly <PERSON><PERSON> fillings on link complements.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.86"}, {"primary_key": "3510189", "vector": [], "sparse_vector": [], "title": "Consistent Hashing with Bounded Loads.", "authors": ["<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In dynamic load balancing, we wish to allocate a set of clients (balls) to a set of servers (bins) with the goal of minimizing the maximum load of any server and also minimizing the number of moves after adding or removing a server or a client. We want a hashing-style solution where we given the ID of a client can efficiently find its server in a distributed dynamic environment. In such a dynamic environment, both servers and clients may be added and/or removed from the system in any order. The most popular solutions for such dynamic settings are Consistent Hashing [KLL+97, SML+03] or Rendezvous Hashing [TR98]. However, the load balancing of these schemes is no better than a random assignment of clients to servers, so with n of each, we expect many servers to be overloaded with Φ(log n / log log n) clients. In this paper, we aim to design hashing schemes that achieve any desirable level of load balancing, while minimizing the number of movements under any addition or removal of servers or clients.In particular, we consider a problem with m balls and n bins, and given a user-specified balancing parameter c = 1 + ε > 1, we aim to find a hashing scheme with no load above [cm/n], referred to as the capacity of the bins. Our algorithmic starting point is the consistent hashing scheme where current balls and bins are hashed to the unit cycle, and a ball is placed in the first bin succeeding it in clockwise order. In order to cope with given capacity constraints, we apply the idea of linear probing by forwarding the ball on the circle to the first non-full bin. We show that in our hashing scheme when a ball or bin is inserted or deleted, the expected number of balls that have to be moved is within a multiplicative factor of of the optimum for ε ≤ 1 (Theorem 1.2) and within a factor of the optimum for ε ≥ 1 (Theorem 1.1). Technically, the latter bound is the most challenging to prove. It implies that for superconstant c, we only pay a negligible cost in extra moves. We also get the same bounds for the simpler problem where, instead of a user specified balancing parameter, we have a fixed bin capacity C for all bins, and define c = 1 + ε = Cn/m.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.39"}, {"primary_key": "3510190", "vector": [], "sparse_vector": [], "title": "Minimum Cut of Directed Planar Graphs in O(n log log n) Time.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We give an O(n log log n) time algorithm for computing the minimum cut (or equivalently, the shortest cycle) of a weighted directed planar graph. This improves the previous fastest O(n log3 n) solution. Interestingly, while in undirected planar graphs both min cut and min st-cut have O(n log log n) solutions, in directed planar graphs our result makes min cut faster than min st-cut, which currently requires O(n log n).", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.32"}, {"primary_key": "3510191", "vector": [], "sparse_vector": [], "title": "Time and Space Efficient Representations of Distributive Lattices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a space-efficient data structure using O(n log n) bits that represents a distributive lattice on n elements and supports finding meets and joins in O(log n) time. Our data structure extends the ideal tree structure of Habib and Nourine which occupies O(n log n) bits of space and requires O(m) time to compute a meet or join, where m depends on the specific lattice and may be as large as n – 1. We also give an encoding of a distributive lattice using bits, which is very close to the information theoretic lower bound. This encoding can be created or decompressed in O(n log n) time.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.36"}, {"primary_key": "3510192", "vector": [], "sparse_vector": [], "title": "Stability of the <PERSON><PERSON><PERSON><PERSON> Method for Matrix Function Approximation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Theoretically elegant and ubiquitous in practice, the Lanczo<PERSON> method can approximate f (A)x for any symmetric matrix A ∊ ℝn × n, vector x ∊ ℝn, and function f. In exact arithmetic, the method's error after k iterations is bounded by the error of the best degree-k polynomial uniformly approximating the scalar function f(x) on the range [λmin(A), λmax(A)]. However, despite decades of work, it has been unclear if this powerful guarantee holds in finite precision.We resolve this problem, proving that when , <PERSON><PERSON><PERSON><PERSON> essentially matches the exact arithmetic guarantee if computations use roughly log(nC║A║) bits of precision. Our proof extends work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [11], leveraging the stability of the classic Ch<PERSON><PERSON>hev recurrence to bound the stability of any polynomial approximating f(x).We also study the special case of f(A) = A–1 for positive definite A, where stronger guarantees hold for <PERSON><PERSON><PERSON><PERSON>. In exact arithmetic the algorithm performs as well as the best polynomial approximating 1/x at each of <PERSON>'s eigenvalues, rather than on the full range [λmin (A), λmax (A)]. In seminal work, <PERSON><PERSON> gives a natural approach to extending this bound to finite precision: she proves that finite precision Lanczos and the related conjugate gradient method match any polynomial approximating 1/x in a tiny range around each eigenvalue [17].For A–1, <PERSON><PERSON>'s bound appears stronger than our result. However, we exhibit matrices with condition number κ where exact arithmetic Lanczos converges in polylog(k) iterations, but <PERSON>baum's bound predicts at best Ω(κ1/5) iterations in finite precision. It thus cannot offer more than a polynomial improvement over the O(κ1/2) bound achievable via our result for general f(A). Our analysis bounds the power of stable approximating polynomials and raises the question of if they fully characterize the behavior of finite precision Lanczos in solving linear systems. If they do, convergence in less than poly(K) iterations cannot be expected, even for matrices with clustered, skewed, or otherwise favorable eigenvalue distributions.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.105"}, {"primary_key": "3510193", "vector": [], "sparse_vector": [], "title": "Submodular Minimization Under Congruency Constraints.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Submodular function minimization (SFM) is a fundamental and efficiently solvable problem class in combinatorial optimization with a multitude of applications in various fields. Surprisingly, there is only very little known about constraint types under which SFM remains efficiently solvable. The arguably most relevant non-trivial constraint class for which polynomial SFM algorithms are known are parity constraints, i.e., optimizing only over sets of odd (or even) cardinality. Parity constraints capture classical combinatorial optimization problems like the odd-cut problem, and they are a key tool in a recent technique to efficiently solve integer programs with a constraint matrix whose subdeterminants are bounded by two in absolute value.We show that efficient SFM is possible even for a significantly larger class than parity constraints, by introducing a new approach that combines techniques from Combinatorial Optimization, Combinatorics, and Number Theory. In particular, we can show that efficient SFM is possible over all sets (of any given lattice) of cardinality r mod m, as long as m is a constant prime power. This covers generalizations of the odd-cut problem with open complexity status, and with relevance in the context of integer programming with higher subdeterminants. To obtain our results, we establish a connection between the correctness of a natural algorithm, and the inexistence of set systems with specific combinatorial properties. We introduce a general technique to disprove the existence of such set systems, which allows for obtaining extensions of our results beyond the above-mentioned setting. These extensions settle two open questions raised by <PERSON><PERSON> and <PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON><PERSON>, 2017] in the context of computing the girth and cogirth of certain types of binary matroids.MSC codessubmodular function minimizationcongruency constraintscombinatorics", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.55"}, {"primary_key": "3510194", "vector": [], "sparse_vector": [], "title": "Impossibility of dimension reduction in the nuclear norm.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Let S1 (the <PERSON><PERSON><PERSON><PERSON><PERSON> trace class) denote the Banach space of all compact linear operators T : ℓ2 → ℓ2 whose nuclear norm ||T||S1 = Σj=1∞ σj(T) is finite, where {σj(T)}j=1∞ are the singular values of T. We prove that for arbitrarily large n ∊ ℕ there exists a subset with that cannot be embedded with bi-Lipschitz distortion O(1) into any no(1)-dimensional linear subspace of S1. is not even a O(1)-Lipschitz quotient of any subset of any no(1)-dimensional linear subspace of S1. Thus, S1 does not admit a dimension reduction result á <PERSON> and <PERSON> (1984), which complements the work of <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> (2011) on the limitations of quantum dimension reduction under the assumption that the embedding into low dimensions is a quantum channel. Such a statement was previously known with S1 replaced by the Banach space ℓ1 of absolutely summable sequences via the work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2003). In fact, the above set can be taken to be the same set as the one that <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> considered, viewed as a collection of diagonal matrices in S1. The challenge is to demonstrate that cannot be faithfully realized in an arbitrary low-dimensional subspace of S1, while <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> obtained such an assertion only for subspaces of S1 that consist of diagonal operators (i.e., subspaces of ℓ1). We establish this by proving that the Markov 2-convexity constant of any finite dimensional linear subspace X of S1 is at most a universal constant multiple of .", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.88"}, {"primary_key": "3510195", "vector": [], "sparse_vector": [], "title": "On the Decidability of the Fréchet Distance between Surfaces.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that the Fréchet distance between two piecewise linear surfaces can be decided in finite time, hence, the problem is decidable. For the special case that one of the surfaces is a triangle, we show that the problem is in PSPACE. In both cases, our computational model is a Turing Machine, and our algorithms rely on <PERSON><PERSON>'s result [STOC 1988] that the existential theory of the real numbers is decidable in PSPACE.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.72"}, {"primary_key": "3510197", "vector": [], "sparse_vector": [], "title": "A Near-Quadratic Lower Bound for the Size of Quantum Circuits of Constant Treewidth.", "authors": ["<PERSON><PERSON>"], "summary": "We show that any quantum circuit of treewidth t, built from r-qubit gates, requires at least gates to compute the element distinctness function. Our result generalizes a near-quadratic lower bound for quantum formula size obtained by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> [SIAM J. on Computing, 2001]. The proof of our lower bound follows by an extension of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s method to the context of quantum circuits of constant treewidth. This extension is made via a combination of techniques from structural graph theory, tensor-network theory, and the connected-component counting method, which is a classic tool in algebraic geometry. In particular, an essential key to proving our lower bound is the development of a new algorithm for tensor network contraction which may be of independent interest.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.9"}, {"primary_key": "3510198", "vector": [], "sparse_vector": [], "title": "Approximating Cycles in Directed Graphs: Fast Algorithms for Girth and Roundtrip Spanners.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Virginia Vassilevska Williams"], "summary": "The girth of a graph, i.e. the length of its shortest cycle, is a fundamental graph parameter. Unfortunately all known algorithms for computing, even approximately, the girth and girth-related structures in directed weighted m-edge and n-node graphs require Ω(min{nω, mn}) time (for 2 ≤ ω < 2.373). In this paper, we drastically improve these runtimes as follows:•• Multiplicative Approximations in Nearly Linear Time: We give an algorithm that in Õ(m) time computes an Õ(1)-multiplicative approximation of the girth as well as an Õ(1)-multiplicative roundtrip spanner with Õ(n) edges with high probability (w.h.p).•• Nearly Tight Additive Approximations: For unweighted graphs and any a ∊ (0, 1) we give an algorithm that in Õ(mn1–a) time computes an O(na)-additive approximation of the girth, w.h.p. We show that the runtime of our algorithm cannot be significantly improved without a breakthrough in combinatorial boolean matrix multiplication. We also show that if the girth is O(na), then the same guarantee can be achieved via a deterministic algorithm.Our main technical contribution to achieve these results is the first nearly linear time algorithm for computing roundtrip covers, a directed graph decomposition concept key to previous roundtrip spanner constructions. Previously it was not known how to compute these significantly faster than Ω(mn) time. Given the traditional difficulty in efficiently processing directed graphs, we hope our techniques may find further applications.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.91"}, {"primary_key": "3510199", "vector": [], "sparse_vector": [], "title": "Estimating Graph Parameters from Random Order Streams.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We develop a new algorithmic technique that allows to transfer some constant time approximation algorithms for general graphs into random order streaming algorithms. We illustrate our technique by proving that in random order streams with probability at least 2/3,•the number of connected components of G can be approximated up to an additive error of εn using space,•the weight of a minimum spanning tree of a connected input graph with integer edges weights from {1, …, W} can be approximated within a multiplicative factor of 1 + ε using space,•the size of a maximum independent set in planar graphs can be approximated within a multiplicative factor of 1+ε using space .", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.157"}, {"primary_key": "3510200", "vector": [], "sparse_vector": [], "title": "Improved Coresets for Kernel Density Estimates.", "authors": ["<PERSON>", "Wai Ming Tai"], "summary": "We study the construction of coresets for kernel density estimates. That is we show how to approximate the kernel density estimate described by a large point set with another kernel density estimate with a much smaller point set. For characteristic kernels (including Gaussian and Laplace kernels), our approximation preserves the L∞ error between kernel density estimates within error ε, with coreset size 4/ε2, but no other aspects of the data, including the dimension, the diameter of the point set, or the bandwidth of the kernel common to other approximations. When the dimension is unrestricted, we show this bound is tight for these kernels as well as a much broader set.This work provides a careful analysis of the iterative Frank-Wolfe algorithm adapted to this context, an algorithm called kernel herding. This analysis unites a broad line of work that spans statistics, machine learning, and geometry.When the dimension d is constant, we demonstrate much tighter bounds on the size of the coreset specifically for Gaussian kernels, showing that it is bounded by the size of the coreset for axis-aligned rectangles. Currently the best known constructive bound is , and non-constructively, this can be improved by . This improves the best constant dimension bounds polynomially for d ≥ 3.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.173"}, {"primary_key": "3510201", "vector": [], "sparse_vector": [], "title": "Almost Envy-Freeness with General Valuations.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The goal of fair division is to distribute resources among competing players in a “fair” way. Envy-freeness is the most extensively studied fairness notion in fair division. Envy-free allocations do not always exist with indivisible goods, motivating the study of relaxed versions of envy-freeness. We study the envy-freeness up to any good (EFX) property, which states that no player prefers the bundle of another player following the removal of any single good, and prove the first general results about this property. We use the leximin solution to show existence of EFX allocations in several contexts, sometimes in conjunction with Pareto optimality. For two players with valuations obeying a mild assumption, one of these results provides stronger guarantees than the currently deployed algorithm on Spliddit, a popular fair division website. Unfortunately, finding the leximin solution can require exponential time. We show that this is necessary by proving an exponential lower bound on the number of value queries needed to identify an EFX allocation, even for two players with identical valuations. We consider both additive and more general valuations, and our work suggests that there is a rich landscape of problems to explore in the fair division of indivisible goods with different classes of player valuations.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.165"}, {"primary_key": "3510202", "vector": [], "sparse_vector": [], "title": "In-Place Sparse Suffix Sorting.", "authors": ["<PERSON>"], "summary": "Suffix arrays encode the lexicographical order of all suffixes of a text and are often combined with the Longest Common Prefix array (LCP) to simulate navigational queries on the suffix tree in reduced space. In space-critical applications such as sparse and compressed text indexing, only information regarding the lexicographical order of a size-b subset of all n text suffixes is often needed. Such information can be stored space-efficiently (in b words) in the sparse suffix array (SSA). The SSA and its relative sparse LCP array (SLCP) can be used as a space-efficient substitute of the sparse suffix tree. Very recently, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [11] showed that the sparse suffix tree (and therefore SSA and SLCP) can be built in asymptotically optimal space with a Monte Carlo algorithm running in time. The main reason for using the SSA and SLCP arrays in place of the sparse suffix tree is, however, their reduced space of b words each. This leads naturally to the quest for in-place algorithms building these arrays. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [8] showed that the full suffix array can be built in-place and in optimal running time. On the other hand, finding sub-quadratic in-place algorithms for building the SSA and SLCP for general subsets of suffixes has been an elusive task for decades. In this paper, we give the first solution to this problem. We provide the first in-place algorithm building the full LCP array in expected time and the first Monte Carlo in-place algorithms building the SSA and SLCP in expected time. We moreover describe the first in-place solution for the suffix selection problem: to compute the i-th smallest text suffix. In order to achieve these results, we show that we can quickly overwrite the text with a reversible and implicit data structure supporting Longest Common Extension queries in polylogarithmic time and text extraction in optimal time: this structure is strictly more powerful than a plain text representation and is of independent interest.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.98"}, {"primary_key": "3510203", "vector": [], "sparse_vector": [], "title": "A Grid-Based Approximation Algorithm for the Minimum Weight Triangulation Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given a set of n points on a plane, in the Minimum Weight Triangulation problem, we wish to find a triangulation that minimizes the sum of Euclidean length of its edges. This incredibly challenging problem has been studied for more than four decades and has been only recently shown to be NP-Hard. In this paper we present a novel polynomial-time algorithm that computes an expected 14-approximation of the minimum weight triangulation—a constant that is significantly smaller than what has been previously known. For every integer q ≥ 1, we also show that our triangulation is simultaneously a 14-approximation of a triangulation that minimizes the q-norm of its edge costs, i.e., the sum of qth powers of all its edges.In our algorithm, we use grids to partition the edges into levels where shorter edges appear at smaller levels and edges with similar lengths appear at the same level. We then triangulate the point set incrementally by introducing edges in increasing order of their levels. We introduce the edges of any level i + 1 in two steps. In the first step, we partition the boundary of any non-triangulated face into reflex chains and add edges between successive chains using a variant of the well-known ring heuristic to generate a partial triangulation Âi. In the second step, we greedily add non-intersecting level i + 1 edges to Âi in increasing order of their length and obtain a partial triangulation Âi+1. The ring heuristic is known to yield only an 𝒪(log n)-approximation even for a convex polygon and the greedy heuristic achieves only a -approximation. Therefore, it is surprising that their combination leads to an improved approximation ratio of 14.For the proof, we identify several useful properties of Âi and combine it with a new Euler characteristic based technique to show that Âi has more edges than τi*; here τi* is the partial triangulation consisting of level ≤ i edges of some minimum weight triangulation. We then use a simple greedy stays ahead proof strategy to bound the approximation ratio.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.7"}, {"primary_key": "3510204", "vector": [], "sparse_vector": [], "title": "Average-radius list-recoverability of random linear codes.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We analyze the list-decodability, and related notions, of random linear codes. This has been studied extensively before: there are many different parameter regimes and many different variants. Previous works have used complementary styles of arguments---which each work in their own parameter regimes but not in others---and moreover have left some gaps in our understanding of the list-decodability of random linear codes. In particular, none of these arguments work well for list-recovery, a generalization of list-decoding that has been useful in a variety of settings. In this work, we present a new approach, which works across parameter regimes and further generalizes to list-recovery. In particular, our argument provides better results for list-decoding and list-recovery over large fields; improved (quasipolynomial) list sizees for high-rate list-recovery of random linear codes; improved algorithmic results for list-decoding; and optimal average-radius list-decoding over constant-sized alphabets.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.42"}, {"primary_key": "3510205", "vector": [], "sparse_vector": [], "title": "A Two-pronged Progress in Structured Dense Matrix Vector Multiplication.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Matrix-vector multiplication is one of the most fundamental computing primitives. Given a matrix A∈FN×N and a vector b∈FN , it is known that in the worst case Θ(N2) operations over F are needed to compute Ab. Many types of structured matrices do admit faster multiplication. However, even given a matrix A that is known to have this property, it is hard in general to recover a representation of A exposing the actual fast multiplication algorithm. Additionally, it is not known in general whether the inverses of such structured matrices can be computed or multiplied quickly. A broad question is thus to identify classes of structured dense matrices that can be represented with O(N) parameters, and for which matrix-vector multiplication (and ideally other operations such as solvers) can be performed in a sub-quadratic number of operations. One such class of structured matrices that admit near-linear matrix-vector multiplication are the orthogonal polynomial transforms whose rows correspond to a family of orthogonal polynomials. Other well known classes include the <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> matrices and their extensions (e.g. confluent Cauchy-like matrices) that are all special cases of a low displacementrank property. In this paper, we make progress on two fronts: Our work unifies, generalizes, and simplifies existing state-of-the-art results in structured matrix-vector multiplication. Finally, we show how applications in areas such as multipoint evaluations of multivariate polynomials can be reduced to problems involving low recurrence width matrices.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.69"}, {"primary_key": "3510206", "vector": [], "sparse_vector": [], "title": "Community Detection on Euclidean Random Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study Community Detection (CD) on a class of sparse spatial random graphs embedded in the Euclidean space. Our random graph is the planted-partition version of the random connection model studied in Stochastic Geometry. Each node has two labels - an i.i.d. uniform {–1, +1} valued community label and a ℝd valued location label which form the support of a Poisson Point Process of intensity λ on ℝd. Conditional on the labels, edges are drawn independently at random depending both on the Euclidean distance between the nodes and the community labels on the nodes. The CD problem then consists in estimating the partition of nodes into communities better than at random, based on an observation of the random graph and the spatial location labels on nodes. We establish a non-trivial phase-transition for this problem in terms of λ. We show that for small λ, there exists no algorithm for CD, For large λ, our algorithm solves CD efficiently. We show that for small λ, there exists no algorithm for CD. For large λ, we propose an algorithm which solves CD efficiently. In certain special cases, we establish the exact threshold on λ which separates the existence of an algorithm which solves CD from the impossibility of any algorithm. We also establish a distinguishability result which says that one can always efficiently infer the existence of a partition given the graph and the spatial locations even when one cannot identify the partition better than at random. This is a new phenomenon not observed thus far in any non-spatial Erdős-Rényi based models.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.142"}, {"primary_key": "3510207", "vector": [], "sparse_vector": [], "title": "A Hamilton Path for the Sigma-Tau Problem.", "authors": ["<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> asked the following question in their Combinatorial Algorithms textbook from 1975: Can the permutations of {1, 2, …, n} be ordered so that each permutation is transformed into the next by applying either the operation σ, a rotation to the left, or τ, a transposition of the first two symbols? <PERSON><PERSON><PERSON> rated the challenge of finding a cyclic solution for odd n (cycles do not exist for even n > 2) at 48/50 in The Art of Computer Programming, which makes it Volume 4's hardest open problem since the 'middle levels' problem was solved by <PERSON><PERSON><PERSON><PERSON>. In this paper we solve the 40 year-old question by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, by providing a simple successor rule to generate each successive permutation. We also present insights into how our solution can be modified to find a Hamilton cycle for odd n.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.37"}, {"primary_key": "3510208", "vector": [], "sparse_vector": [], "title": "The menu complexity of &quot;one-and-a-half-dimensional&quot; mechanism design.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the menu complexity of optimal and approximately-optimal auctions in the context of the \"FedEx\" problem, a so-called \"one-and-a-half-dimensional\" setting where a single bidder has both a value and a deadline for receiving an item [FGKK16]. The menu complexity of an auction is equal to the number of distinct (allocation, price) pairs that a bidder might receive [HN13]. We show the following when the bidder has n possible deadlines:•Exponential menu complexity is necessary to be exactly optimal: There exist instances where the optimal mechanism has menu complexity ≥ 2n – 1. This matches exactly the upper bound provided by Fiat et al.'s algorithm, and resolves one of their open questions [FGKK16].•Fully polynomial menu complexity is necessary and sufficient for approximation: For all instances, there exists a mechanism guaranteeing a multiplicative (1 – ∊)-approximation to the optimal revenue with menu complexity , where υmax denotes the largest value in the support of integral distributions.•There exist instances where any mechanism guaranteeing a multiplicative (1 – O(1/n2))-approximation to the optimal revenue requires menu complexity Ω(n2).Our main technique is the polygon approximation of concave functions [Rot92], and our results here should be of independent interest. We further show how our techniques can be used to resolve an open question of [DW17] on the menu complexity of optimal auctions for a budget-constrained buyer.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.132"}, {"primary_key": "3510209", "vector": [], "sparse_vector": [], "title": "Localization of Electrical Flows.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that in any graph, the average length of a flow path in an electrical flow between the endpoints of a random edge is O(log2 n). This is a consequence of a more general result which shows that the spectral norm of the entrywise absolute value of the transfer impedance matrix of a graph is O(log2 n). This result implies a simple oblivious routing scheme based on electrical flows in the case of transitive graphs.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.103"}, {"primary_key": "3510210", "vector": [], "sparse_vector": [], "title": "Consensus of Interacting Particle Systems on Erdös-Rényi Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Interacting Particle Systems—exemplified by the voter model, iterative majority, and iterative k—majority processes—have found use in many disciplines including distributed systems, statistical physics, social networks, and Markov chain theory. In these processes, nodes update their \"opinion\" according to the frequency of opinions amongst their neighbors.We propose a family of models parameterized by an update function that we call Node Dynamics: every node initially has a binary opinion. At each round a node is uniformly chosen and randomly updates its opinion with the probability distribution specified by the value of the update function applied to the frequencies of its neighbors' opinions.In this work, we prove that the Node Dynamics converge to consensus in time Θ(n log n) in complete graphs and dense Erdös-Rényi random graphs when the update function is from a large family of \"majority-like\" functions. Our technical contribution is a general framework that upper bounds the consensus time. In contrast to previous work that relies on handcrafted potential functions, our framework systematically constructs a potential function based on the state space structure.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.127"}, {"primary_key": "3510211", "vector": [], "sparse_vector": [], "title": "Fréchet-Stable Signatures Using Persistence Homology.", "authors": ["<PERSON>"], "summary": "For a metric space Y, the Fréchet distance is a metric on trajectories f, g : [0, 1] → Y that minimizes maxt∊[0,1] dY(f(t),g(h(t))) over continuous reparameterizations h of time. One can define the generalized Fréchet distance between more complex objects, functions f : X → Y where X is some topological space that minimizes over homeomorphisms from X → X. This more general definition has been studied for surfaces and often leads to computationally hard problems. We show how to compute in polynomial-time signatures for these functions for which the resulting metric on the signatures can also be computed in polynomial-time and provides a meaningful lower bound on the generalized Fréchet distance. Our approach uses persistent homology and exploits the natural invariance of persistence diagrams of functions to homeomorphisms of the domain. Our algorithm for computing the signatures in Euclidean spaces uses a new method for computing persistent homology of convex functions on simplicial complexes which may be of independent interest.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.71"}, {"primary_key": "3510212", "vector": [], "sparse_vector": [], "title": "The Diameter of Dense Random Regular Graphs.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "There is a tight upper bound on the order (the number of vertices) of any d-regular graph of diameter D, known as the Moore bound in graph theory. This bound implies a lower bound D0(n, d) on the diameter of any d-regular graph of order n. Actually, the diameter diam(Gn,d) of a random d-regular graph Gn,d of order n is known to be asymptotically \"optimal\" as n → ∞. <PERSON><PERSON><PERSON> and <PERSON> (1982) proved that diam(Gn,d) = (1 + o(1)) D0(n, d) = (1 + o(1)) logd–1 n holds w.h.p. (with high probability) for fixed d ≥ 3, whereas there exists a gap diam(Gn,d) – D0(n, d) = Ω(log log n).In this paper, we investigate the gap diam(Gn,d) – D0(n, d) for d = (β + o(1)) nα where α ∊ (0, 1) and β > 0 are arbitrary constants. We prove that diam(Gn,d) = ⌊α–1⌋ + 1 holds w.h.p. for such d. Our result implies that the gap is 1 if α–1 is an integer and d ≥ nα, and is 0 otherwise. One can easily obtain that diam(Gn,d) ≤ ⌊α–1⌋ + 1 holds w.h.p. by using the embedding theorem due to <PERSON> et al. (2017). Our critical contribution is to show that diam(Gn,d) ≤ ⌊a–1⌋ + 1 holds w.h.p. by the analysis of the distances of fixed vertex pairs.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.126"}, {"primary_key": "3510213", "vector": [], "sparse_vector": [], "title": "Variance Reduced Value Iteration and Faster Algorithms for Solving Markov Decision Processes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we provide faster algorithms for approximately solving discounted Markov Decision Processes in multiple parameter regimes. Given a discounted Markov Decision Process (DMDP) with |S| states, |A| actions, discount factor γ ∊ (0, 1), and rewards in the range [–M, M], we show how to compute an ∊-optimal policy, with probability 1 – δ in timeThis contribution reflects the first nearly linear time, nearly linearly convergent algorithm for solving DMDP's for intermediate values of γ.We also show how to obtain improved sublinear time algorithms and provide an algorithm which computes an ∊-optimal policy with probability 1 – δ in timeprovided we can sample from the transition function in O(1) time.Interestingly, we obtain our results by a careful modification of approximate value iteration. We show how to combine classic approximate value iteration analysis with new techniques in variance reduction. Our fastest algorithms leverage further insights to ensure that our algorithms make monotonic progress towards the optimal value. This paper is one of few instances in using sampling to obtain a linearly convergent linear programming algorithm and we hope that the analysis may be useful more broadly.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.50"}, {"primary_key": "3510214", "vector": [], "sparse_vector": [], "title": "Approximate Positive Correlated Distributions and Approximation Algorithms for D-optimal Design.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Experimental design is a classical area in statistics [21] and has also found new applications in machine learning[2]. In the combinatorial experimental design problem, the aim is to estimate an unknown m-dimensional vector x from linear measurements where a Gaussian noise is introduced in each measurement. The goal is to pick k out of the given n experiments so as to make the most accurate estimate of the unknown parameter x. Given a set S of chosen experiments, the most likelihood estimate x′ can be obtained by a least squares computation. One of the robust measures of error estimation is the D-optimality criterion [27] which aims to minimize the generalized variance of the estimator. This corresponds to minimizing the volume of the standard confidence ellipsoid for the estimation error x – x′. The problem gives rise to two natural variants depending on whether repetitions of experiments is allowed or not. The latter variant, while being more general, has also found applications in geographical location of sensors [19].We show a close connection between approximation algorithms for the D-optimal design problem and constructions of approximately m-wise positively correlated distributions. This connection allows us to obtain a approximation for the D-optimal design problem with and without repetitions giving the first constant factor approximation for the problem. We then consider the case when the number of experiments chosen is much larger than the dimension m and show one can obtain (1 – ∊)-approximation if when repetitions are allowed and if when no repetitions are allowed improving on previous work.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.145"}, {"primary_key": "3510215", "vector": [], "sparse_vector": [], "title": "The Price of Information in Combinatorial Optimization.", "authors": ["<PERSON><PERSON>"], "summary": "Consider a network design application where we wish to lay down a minimum-cost spanning tree in a given graph; however, we only have stochastic information about the edge costs. To learn the precise cost of any edge, we have to conduct a study that incurs a price. Our goal is to find a spanning tree while minimizing the disutility, which is the sum of the tree cost and the total price that we spend on the studies. In a different application, each edge gives a stochastic reward value. Our goal is to find a spanning tree while maximizing the utility, which is the tree reward minus the prices that we pay.Situations such as the above two often arise in practice where we wish to find a good solution to an optimization problem, but we start with only some partial knowledge about the parameters of the problem. The missing information can be found only after paying a probing price, which we call the price of information. What strategy should we adopt to optimize our expected utility/disutility?A classical example of the above setting is <PERSON><PERSON><PERSON>'s “Pandora's box” problem where we are given probability distributions on values of n independent random variables. The goal is to choose a single variable with a large value, but we can find the actual outcomes only after paying a price. Our work is a generalization of this model to other combinatorial optimization problems such as matching, set cover, facility location, and prize-collecting Steiner tree. We give a technique that reduces such problems to their non-price counterparts, and use it to design exact/approximation algorithms to optimize our utility/disutility. Our techniques extend to situations where there are additional constraints on what parameters can be probed or when we can simultaneously probe a subset of the parameters.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.161"}, {"primary_key": "3510216", "vector": [], "sparse_vector": [], "title": "Lower Bounds for Approximating the Matching Polytope.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We prove that any linear program that approximates the matching polytope on n-vertex graphs up to a factor of (1 + ε) for any must have at least inequalities where 0 < α < 1 is an absolute constant. This is tight as exhibited by the (1 + ε) approximating linear program obtained by dropping the odd set constraints of size larger than (1 + ε)/ε from the description of the matching polytope. Previously, a tight lower bound of 2Ω(n) was only known for [22, 5] whereas for , the best lower bound was 2Ω(1/ε) [22]. The key new ingredient in our proof is a close connection to the non-negative rank of a lopsided version of the unique disjointness matrix.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.104"}, {"primary_key": "3510217", "vector": [], "sparse_vector": [], "title": "Strong Algorithms for the Ordinal Matroid Secretary Problem.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the ordinal Matroid Secretary Problem (MSP), elements from a weighted matroid are presented in random order to an algorithm that must incrementally select a large weight independent set. However, the algorithm can only compare pairs of revealed elements without using its numerical value. An algorithm is α probability-competitive if every element from the optimum appears with probability 1/α in the output. We present a technique to design algorithms with strong probability-competitive ratios, improving the guarantees for almost every matroid class considered in the literature: e.g., we get ratios of 4 for graphic matroids (improving on 2e by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [ICALP 2009]) and of 5.19 for laminar matroids (improving on 9.6 by <PERSON> et al. [THEOR COMPUT SYST 2016]). We also obtain new results for superclasses of k column sparse matroids, for hypergraphic matroids, certain gammoids and graph packing matroids, and a probability-competitive algorithm for uniform matroids of rank ρ based on <PERSON><PERSON>'s utility-competitive algorithm [SODA 2005] for that class. Our second contribution are algorithms for the ordinal MSP on arbitrary matroids of rank ρ. We devise an O(log ρ) probability-competitive algorithm and an O(log log ρ) ordinal-competitive algorithm, a weaker notion of competitiveness but stronger than the utility variant. These are based on the O(log log ρ) utility-competitive algorithm by <PERSON><PERSON><PERSON> et al. [SODA 2015].", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.47"}, {"primary_key": "3510218", "vector": [], "sparse_vector": [], "title": "An Alon-Boppana Type Bound for Weighted Graphs and Lowerbounds for Spectral Sparsification.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "We prove the following Alon-Boppan<PERSON> type theorem for general (not necessarily regular) weighted graphs: if G is an n-node weighted undirected graph of average combinatorial degree d (that is, G has dn/2 edges) and girth g > 2d1/8 + 1, and if λ1 ≤ λ2 ≤ · · · λn are the eigenvalues of the (non-normalized) Laplacian of G, then(The Alon-Boppana theorem implies that if G is unweighted and d-regular, then if the diameter is at least d1.5.)Our result implies a lower bound for spectral sparsifiers. A graph H is a spectral є-sparsifier of a graph G ifwhere L(G) is the Laplacian matrix of G and L(H) is the Laplacian matrix of H. <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proved that for every G there is an є-sparsifier H of average degree d where and the edges of H are a (weighted) subset of the edges of G. <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> also show that the bound on є cannot be reduced below when G is a clique; our Alon-Boppana-type result implies that є cannot be reduced below when G comes from a family of expanders of super-constant degree and superconstant girth.The method of <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proves a more general result, about sparsifying sums of rank-one matrices, and their method applies to an \"online\" setting. We show that for the online matrix setting the bound is tight, up to lower order terms.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.85"}, {"primary_key": "3510219", "vector": [], "sparse_vector": [], "title": "Scheduling When You Don&apos;t Know the Number of Machines.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Often in a scheduling problem, there is uncertainty about the jobs to be processed. The issue of uncertainty regarding the machines has been much less studied. In this paper, we study a scheduling environment in which jobs first need to be grouped into some sets before the number of machines is known, and then the sets need to be scheduled on machines without being separated. In order to evaluate algorithms in such an environment, we introduce the idea of an α-robust algorithm, one which is guaranteed to return a schedule on any number m of machines that is within an α factor of the optimal schedule on m machine, where the optimum is not subject to the restriction that the sets cannot be separated. Under such environment, we give a -robust algorithm for scheduling on parallel machines to minimize makespan, and show a lower bound . For the special case when the jobs are infinitesimal, we give a 1.233-robust algorithm with an asymptotic lower bound of 1.207. We also study a case of fair allocation, where the objective is to minimize the difference between the maximum and minimum machine load.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.82"}, {"primary_key": "3510220", "vector": [], "sparse_vector": [], "title": "Approaching for the s-t-path TSP.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We show that there is a polynomial-time algorithm with approximation guarantee for the s-t-path TSP, for any fixed ε > 0.It is well known that <PERSON><PERSON><PERSON>'s analysis of <PERSON><PERSON><PERSON>' algorithm also works for the s-t-path TSP with its natural LP relaxation except for the narrow cuts (in which the LP solution has value less than two). A fixed optimum tour has either a single edge in a narrow cut (then call the edge and the cut lonely) or at least three (then call the cut busy). Our algorithm \"guesses\" (by dynamic programming) lonely cuts and edges. Then we partition the instance into smaller instances and strengthen the LP, requiring value at least three for busy cuts. By setting up a k-stage recursive dynamic program, we can compute a spanning tree (V, S) and an LP solution y such that is in the T-join polyhedron, where T is the set of vertices whose degree in S has the wrong parity.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.121"}, {"primary_key": "3510221", "vector": [], "sparse_vector": [], "title": "On the Difference Between Closest, Furthest, and Orthogonal Pairs: Nearly-Linear vs Barely-Subquadratic Complexity.", "authors": ["<PERSON>"], "summary": "Point location problems for n points in d-dimensional Euclidean space (and ℓp spaces more generally) have typically had two kinds of running-time solutions:(Nearly-Linear) less than dpo1y(d) · n logO(d) n time, or (Barely-Subquadratic) f(d) · n2–1/Θ(d) time, for various f.For small d and large n, \"nearly-linear\" running times are generally feasible, while the \"barely-subquadratic\" times are generally infeasible, requiring essentially quadratic time. For example, in the Euclidean metric, finding a Closest Pair among n points in ℝd is nearly-linear, solvable in 2O(d). n logO(1) n time, while the known algorithms for finding a Furthest Pair (the diameter of the point set) are only barely-subquadratic, requiring Ω(n2–1/Θ(d)) time. Why do these proximity problems have such different time complexities? Is there a barrier to obtaining nearly-linear algorithms for problems which are currently only barely-subquadratic?We give a novel exact and deterministic self-reduction for the Orthogonal Vectors problem on n vectors in {0, 1}d to n vectors in ℤω(log d) that runs in 2o(d) time. As a consequence, barely-subquadratic problems such as Euclidean diameter, Euclidean bichromatic closest pair, and incidence detection do not have O(n2–∊) time algorithms (in Turing models of computation) for dimensionality d = ω(log log n)2, unless the popular Orthogonal Vectors Conjecture and the Strong Exponential Time Hypothesis are false. That is, while the poly-log-log-dimensional case of Closest Pair is solvable in n1+o(1) time, the poly-log-log-dimensional case of Furthest Pair can encode difficult large-dimensional problems conjectured to require n2–o(1) time.We also show that the All-Nearest Neighbors problem in ω(log n) dimensions requires n2–o(1) time to solve, assuming either of the above conjectures.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.78"}, {"primary_key": "3510225", "vector": [], "sparse_vector": [], "title": "Stochastic Packing Integer Programs with Few Queries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider a stochastic variant of the packing-type integer linear programming problem, which contains random variables in the objective vector. We are allowed to reveal each entry of the objective vector by conducting a query, and the task is to find a good solution by conducting a small number of queries. We propose a general framework of adaptive and non-adaptive algorithms for this problem, and provide a unified methodology for analyzing the performance of those algorithms. We also demonstrate our framework by applying it to a variety of stochastic combinatorial optimization problems such as matching, matroid, and stable set problems.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.21"}, {"primary_key": "3510226", "vector": [], "sparse_vector": [], "title": "Minor-matching hypertree width.", "authors": ["<PERSON>"], "summary": "In this paper we present a new width measure for a tree decomposition, minor-matching hypertree width, μ-tw, for graphs and hypergraphs, such that bounding the width guarantees that set of maximal independent sets has a polynomially-sized restriction to each decomposition bag. The relaxed conditions of the decomposition allow a much wider class of graphs and hypergraphs to have bounded width compared to other tree decompositions. We show that, for fixed k, there are n-vertex graphs of minor-matching hypertree width at most k. A number of problems including Maximum Independence Set, k-Colouring, and Homomorphism of uniform hypergraphs permit polynomial-time solutions for hypergraphs with bounded minor-matching hypertree width and bounded rank. We show that for any given k and any graph G, it is possible to construct a decomposition of minor-matching hypertree width at most O(k3), or to prove that µ-tw(G) > k in time nO(k3). This is done by presenting a general algorithm for approximating the hypertree width of well-behaved measures, and reducing μ-tw to such measure. The result relating the restriction of the maximal independent sets to a set S with the set of induced matchings intersecting S in graphs, and minor matchings intersecting S in hypergraphs, might be of independent interest.", "published": "2018-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.*************.16"}]