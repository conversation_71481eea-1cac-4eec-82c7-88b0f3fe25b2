from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, Body
from contextlib import asynccontextmanager
from apscheduler.schedulers.asyncio import AsyncIOScheduler

from starlette.responses import JSONResponse
from starlette.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.responses import StreamingResponse
import json


import uvicorn
import asyncio
import os
from utils.llm import BaseLLM, ModelSelector
from service.search import deep_semantic_search
from service.search import semantic_search
from service.search import abstract_refinement
from service.translate import fast_translate
from service.pdf2md import pdf_url_to_text
from service.scheduler import filter_sc_balance_to_file

class TimeoutMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, timeout: int = 5):
        super().__init__(app)
        self.timeout = timeout

    async def dispatch(self, request: Request, call_next):
        try:
            return await asyncio.wait_for(call_next(request), timeout=self.timeout)
        except asyncio.TimeoutError:
            return JSONResponse({"detail": "请求超时, 请稍后重试。"}, status_code=408)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时
    scheduler = AsyncIOScheduler()
    # scheduler.add_job(my_job, 'interval', days=1, hours=2, minutes=30, seconds=10) # 每隔1天2小时30分钟执行一次
    scheduler.add_job(filter_sc_balance_to_file, 'interval', minutes=60) # 每隔10秒执行一次
    scheduler.start()
    yield
    # 关闭时（可选）
    scheduler.shutdown()

# 热重载模式
# uvicorn main:app --reload
app = FastAPI(lifespan=lifespan)

# 添加超时约束 15s
app.add_middleware(TimeoutMiddleware, timeout=15)
# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)

llm_map = {
    'qwen3_32b': ModelSelector.qwen3_32b,
    'qwen3_14b': ModelSelector.qwen3_14b,
    'qwen3_30b_a3b': ModelSelector.qwen3_30b_a3b,
    'qwen3_235b_a22b': ModelSelector.qwen3_235b_a22b,
    'qwen3_235b_a22b_think': ModelSelector.qwen3_235b_a22b_think,
    'qwen_long_32b': ModelSelector.qwen_long_32b,

    'qwen25_72b': ModelSelector.qwen25_72b,
    'kimi_k2': ModelSelector.kimi_k2,
    'deepseek_v3': ModelSelector.deepseek_v3,
    'deepseek_r1': ModelSelector.deepseek_r1,
    'deepseek_v3_sc': ModelSelector.deepseek_v3_sc,
    'deepseek_r1_sc': ModelSelector.deepseek_r1_sc,
    'deepseek_v3_op': ModelSelector.deepseek_v3_op,
    'deepseek_r1_op': ModelSelector.deepseek_r1_op,

    'glm4_32b': ModelSelector.glm4_32b,
    'glm4_32b_z1': ModelSelector.glm4_32b_z1,
    'glm4_9b': ModelSelector.glm4_9b,
    'glm4_9b_z1': ModelSelector.glm4_9b_z1,
    'glm45': ModelSelector.glm45,

    'claude4_sonnet': ModelSelector.claude4_sonnet,
    'gpt_4o': ModelSelector.gpt4o,
    'gpt41': ModelSelector.gpt41,
    'o4_mini': ModelSelector.o4_mini,
    'gemini_25_flash': ModelSelector.gemini25_flash,
}

@app.get("/semantic-search")
async def csemantic_search(query: str, page: int = 1, limit: int = 12, years: str = '', 
                          source: str = 'arxiv', deepsearch: str = 'false'):
    """根据搜索内容，返回语义搜索结果
    输出示例：
    [
        {
            "title": "...",
            "summary": "...",
            "authors": "...",
            "published": "...",
            "pdf_url": "...",
            "distance": 0.123  # 相似度分数（越小越相关）
        },
        ...
    ]
    """
    # 1. 处理 source
    source = source.split(',')
    if len(source) == 0:
        source = ['arxiv']

    if deepsearch.lower() == 'false':
        # 2. 进行 semantic search
        results = await semantic_search(query=query, page=page, limit=limit, years=years, source=source)
        return results
    else:
        # 3. 进行 deepsearch （其实应该叫 deeprerank）
        results = await deep_semantic_search(query=query, page=1, limit=166, years=years, match_limit=4, source=source, use_sub_summary=False)

        # 3. 如果数量大于 66，则只返回分数大于 6 的，否则返回分数大于 5.1 的（控制返回数量不要过多）
        threshold = 6 if len([r for r in results if r['score'] > 5.1]) > 66 else 5.1
        results_filtered = [r for r in results if r['score'] > threshold]
        print(f"筛选前数量: {len(results)}，筛选后数量: {len(results_filtered)}")

        # 4. 排序
        results_filtered = sorted(results_filtered, key=lambda x: x['distance'], reverse=False) # 从小到大
        
        # 5. 返回前 66 条
        return results_filtered[:66]

@app.post("/translate")
async def translate(query: str):
    """根据内容，直接返回翻译结果
    """
    response = await fast_translate(query, max_length=200)
    return response

@app.post("/translate-multi")
async def translate_multi(querys: list[str]):
    """根据内容，直接返回翻译结果（批量）
    """
    # 并发调用 fast_translate
    tasks = [fast_translate(query) for query in querys]
    responses = await asyncio.gather(*tasks)
    return responses

@app.post("/abstract-refinement-multi")
async def abstract_refinement_multi(querys: list[str]):
    """根据内容，直接返回摘要精炼结果（批量）
    """
    responses = await abstract_refinement(querys)
    return responses

@app.post("/stream-chat")
async def stream_chat(request: Request):
    """流式对话API
    
    接收用户的提问，返回流式的回答内容
    
    请求体格式:
    {
        "prompt": "用户问题",
        "history_messages": ["历史消息1", "历史消息2", ...],
        "system_prompt": "系统提示词（可选）",
        "temperature": 0.7 (可选，默认0.7)
    }
    
    返回:
        流式文本响应
    """
    # 解析请求体
    data = await request.json()
    prompt = data.get("prompt", "")
    history_messages = data.get("history_messages", []) # 历史消息
    system_prompt = data.get("system_prompt", "你是一个有用的AI助手")
    temperature = float(data.get("temperature", 0.7))
    model = data.get("model", "glm4_32b")
    llm_cur = BaseLLM(**llm_map[model])

    if not prompt:
        raise HTTPException(status_code=400, detail="提问内容不能为空")
    
    # 创建流式响应
    async def generate_stream():
        try:
            async for chunk in llm_cur.ask_async_stream(prompt, system_prompt, history_messages, temperature):
                # print(chunk)
                yield f"data: {json.dumps({'content': chunk})}\n\n"
            # 发送结束标记
            yield f"data: {json.dumps({'done': True})}\n\n"
        except Exception as e:
            error_msg = f"流式生成出错: {str(e)}"
            print(error_msg)
            yield f"data: {json.dumps({'error': error_msg})}\n\n"
            yield f"data: {json.dumps({'done': True})}\n\n"
    
    # 返回流式响应
    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@app.post("/pdf2text")
async def pdf2text(
    pdf_url: str = Body(..., embed=True),
    pages: str = '1-10'
):
    """
    根据 PDF 链接和页码，返回提取的文本内容
    Args:
        pdf_url: PDF 文件的网络地址
        pages: 页码字符串，如 "1,2,3" 或 "1-3"，为空则全部
    Returns:
        {"text": ...}
    """
    try:
        # 解析页码
        page_list = None
        if pages:
            pages = pages.replace(' ', '')
            if '-' in pages:
                # 范围 "2-5"
                start, end = pages.split('-')
                page_list = list(range(int(start), int(end)+1))
            else:
                # 逗号分隔 "1,3,5"
                page_list = [int(p) for p in pages.split(',') if p.isdigit()]
        text = pdf_url_to_text(pdf_url, page_list)
        print(f'解析结果长度：{len(text)}')
        return {"text": text}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"PDF 解析失败: {e}")




if __name__ == "__main__":
    file_path = os.path.basename(__file__)
    module_name = file_path[:-3]  # 移除 .py 后缀
    uvicorn.run(
        f"{module_name}:app",
        host="0.0.0.0",
        port=8848,
        reload=False,
        log_level="debug"  # 启用日志输出
    )