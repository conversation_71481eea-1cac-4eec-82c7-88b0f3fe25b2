[{"primary_key": "1139502", "vector": [], "sparse_vector": [], "title": "Towards derandomising Markov chain Monte Carlo.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a new framework to derandomise certain Markov chain Monte Carlo (MCMC) algorithms. As in MCMC, we first reduce counting problems to sampling from a sequence of marginal distributions. For the latter task, we introduce a method called coupling towards the past that can, in logarithmic time, evaluate one or a constant number of variables from a stationary Markov chain state. Since there are at most logarithmic random choices, this leads to very simple derandomisation. We provide two applications of this framework, namely efficient deterministic approximate counting algorithms for hypergraph independent sets and hypergraph colourings, under local lemma type conditions matching, up to lower order factors, their state-of-the-art randomised counterparts.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00120"}, {"primary_key": "1139503", "vector": [], "sparse_vector": [], "title": "Simultaneous Auctions are Approximately Revenue-Optimal for Subadditive Bidders.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study revenue maximization in multi-item auctions, where bidders have subadditive valuations over independent items [48]. Providing a simple mechanism that is approximately revenue-optimal in this setting is a major open problem in mechanism design [20]. In this paper, we present the first simple mechanism whose revenue is at least a constant fraction of the optimal revenue in multi-item auctions with subadditive bidders. Our mechanism is a simultaneous auction that incorporates either a personalized entry fee or a personalized reserve price per item. We prove that for any simultaneous auction that satisfies c-efficiency– a new property we propose, its revenue is at least an $O(c)$-approximation to the optimal revenue. We further show that both the simultaneous first-price and the simultaneous all-pay auction are $\\frac{1}{2}$-efficient. Providing revenue guarantees for non-truthful simple mechanisms, e.g., simultaneous auctions, in multi-dimensional environments has been recognized by <PERSON><PERSON><PERSON> et al. [47] as an important open question. Prior to our result, the only such revenue guarantees are due to <PERSON><PERSON><PERSON><PERSON> et al. [30] for bidders who have additive valuations over independent items. Our result significantly extends the revenue guarantees of these non-truthful simple auctions to settings where bidders have combinatorial valuations.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00017"}, {"primary_key": "1139504", "vector": [], "sparse_vector": [], "title": "Work-Efficient Parallel Derandomization I: Chernoff-like Concentrations via Pairwise Independence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We present a novel technique for work-efficient parallel derandomization, for algorithms that rely on the concentration of measure bounds such as <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and Bernstein inequalities. Our method increases the algorithm's computational work and depth by only polylogarithmic factors. Before our work, the only known method to obtain parallel derandomization with such strong concentrations was by the results of [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>'89; <PERSON> and <PERSON> FOCS'89], which perform a binary search in a k-wise independent space for $k=\\operatorname{poly}(\\log n)$. However, that method blows up the computational work by a high poly $(n)$ factor and does not yield work-efficient parallel algorithms. Their method was an extension of the approach of [Luby FOCS'88], which gave a work-efficient derandomization but was limited to algorithms analyzed with only pairwise independence. Pushing the method from pairwise to the higher k-wise analysis resulted in the $\\operatorname{poly}(n)$ factor computational work blow-up. Our work can be viewed as an alternative extension from the pairwise case, which yields the desired strong concentrations while retaining work efficiency up to logarithmic factors. Our approach works by casting the problem of determining the random variables as an iterative process with poly $(\\log n)$ iterations, where different iterations have independent randomness. This is done so that for the desired concentrations, we need only pairwise independence inside each iteration. In particular, we model each binary random variable as a result of a gradual random walk, and our method shows that the desired Chernoff-like concentrations about the endpoints of these walks can be boiled down to some pairwise analysis on the steps of these random walks in each iteration (while having independence across iterations). Hence, we can fix the randomness of each iteration efficiently before proceeding to the next.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00094"}, {"primary_key": "1139505", "vector": [], "sparse_vector": [], "title": "Weighted Pseudorandom Generators via Inverse Analysis of Random Walks and Shortcutting.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A weighted pseudorandom generator (WPRG) is a generalization of a pseudorandom generator (PRG) in which, roughly speaking, probabilities are replaced with weights that are permitted to be positive or negative. We present new explicit constructions of WPRGs that fool certain classes of standard-order read-once branching programs. In particular, our WPRGs fool width-3 programs, constant-width regular programs, and unbounded-width permutation programs with a single accepting vertex. In all three cases, the seed length is $\\widetilde{O}(\\log n \\cdot \\sqrt{\\log (1 / \\varepsilon)}+\\log (1 / \\varepsilon))$, where n is the length of the program and $\\varepsilon$ is the error of the WPRG. For comparison, for all three of these models, the best explicit unweighted PRGs known have seed length $\\widetilde{O}(\\log n$. $\\log (1 / \\varepsilon)$) (Meka, Reingold, and Tal STOC 2019; <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> SICOMP 2014; Ho<PERSON>, Pyne, and Vadhan ITCS 2021). Our WPRG seed length is superior when $\\varepsilon$ is small. For the case of unbounded-width permutation programs, <PERSON><PERSON> and <PERSON><PERSON><PERSON> previously constructed a WPRG with a seed length that is similar to ours (CCC 2021), but their seed length has an extra additive $\\log ^{3 / 2} n$ term, so our WPRG is superior when $\\varepsilon \\gg 1 / n$. Our results are based on a new, general framework for error reduction. Our framework builds on the remarkable recent work by Ahmadinejad, Kelner, Murtagh, Peebles, Sidford, and Vadhan (FOCS 2020) that gave a near-logarithmic space algorithm for estimating random walk probabilities in Eulerian digraphs with high precision. Our framework centers around the \"inverse analysis\" of random walks and a key combinatorial structure termed \"shortcut graphs.\" Using our new framework and the recent notion of singular value approximation (Ahmadinejad, Peebles, Pyne, Sidford, and Vadhan arXiv 2023), we also present an alternative, simpler proof of Ahmadinejad, Kelner, Murtagh, Peebles, Sidford, and Vadhan's main theorem. Compared to the original proof, our new proof avoids much of the sophisticated machinery that was imported from recent work on fast Laplacian solvers.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00072"}, {"primary_key": "1139506", "vector": [], "sparse_vector": [], "title": "Polynomial-Time Pseudodeterministic Construction of Primes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Ren", "<PERSON><PERSON>"], "summary": "A randomized algorithm for a search problem is pseudodeterministic if it produces a fixed canonical solution to the search problem with high probability. In their seminal work on the topic, <PERSON><PERSON> and <PERSON>was<PERSON> [1] posed as their main open problem whether prime numbers can be pseudodeterministically constructed in polynomial time. We provide a positive solution to this question in the infinitely-often regime. In more detail, we give an unconditional polynomial-time randomized algorithm B such that, for infinitely many values of $n, B\\left(1^{n}\\right)$ outputs a canonical n-bit prime $p_{n}$ with high probability. More generally, we prove that for every dense property Q of strings that can be decided in polynomial time, there is an infinitely-often pseudodeterministic polynomial-time construction of strings satisfying Q. This improves upon a subexponential-time construction of <PERSON> and <PERSON><PERSON> [2]. Our construction uses several new ideas, including a novel bootstrapping technique for pseudodeterministic constructions, and a quantitative optimization of the uniform hardness-randomness framework of Chen and Tell [3], using a variant of the Shaltiel-Umans generator [4].", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00074"}, {"primary_key": "1139507", "vector": [], "sparse_vector": [], "title": "Stability and Replicability in Learning.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Replicability is essential in science as it allows us to validate and verify research findings. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> ('22) recently initiated the study of replicability in machine learning. A learning algorithm is replicable if it typically produces the same output when applied on two i.i.d. inputs using the same internal randomness. We study a variant of replicability that does not involve fixing the randomness. An algorithm satisfies this form of replicability if it typically produces the same output when applied on two i.i.d. inputs (without fixing the internal randomness). This variant is called global stability and was introduced by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> ('20) in the context of differential privacy. <PERSON><PERSON><PERSON><PERSON><PERSON> et al. showed how to boost any replicable algorithm so that it produces the same output with probability arbitrarily close to 1. In contrast, we demonstrate that for numerous learning tasks, global stability can only be accomplished weakly, where the same output is produced only with probability bounded away from 1. To overcome this limitation, we introduce the concept of list replicability, which is equivalent to global stability. Moreover, we prove that list replicability can be boosted so that it is achieved with probability arbitrarily close to 1. We also describe basic relations between standard learningtheoretic complexity measures and list replicable numbers. Our results, in addition, imply that besides trivial cases, replicable algorithms (in the sense of <PERSON><PERSON><PERSON><PERSON><PERSON> et al.) must be randomized. The proof of the impossibility result is based on a topological fixed-point theorem. For every algorithm, we are able to locate a \"hard input distribution by applying the <PERSON><PERSON><PERSON><PERSON><PERSON> theorem in a related topological setting. The equivalence between global stability and list replicability is algorithmic.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00148"}, {"primary_key": "1139508", "vector": [], "sparse_vector": [], "title": "Interior-point methods on manifolds: theory and applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Interior-point methods offer a highly versatile framework for convex optimization that is effective in theory and practice. A key notion in their theory is that of a self-concordant barrier. We give a suitable generalization of self-concordance to Riemannian manifolds and show that it gives the same structural results and guarantees as in the Euclidean setting, in particular local quadratic convergence of <PERSON>'s method. We analyze a path-following method for optimizing compatible objectives over a convex domain for which one has a self-concordant barrier, and obtain the standard complexity guarantees as in the Euclidean setting. We provide general constructions of barriers, and show that on the space of positive-definite matrices and other symmetric spaces, the squared distance to a point is self-concordant. To demonstrate the versatility of our framework, we give algorithms with state-of-the-art complexity guarantees for the general class of scaling and non-commutative optimization problems, which have been of much recent interest, and we provide the first algorithms for efficiently finding high-precision solutions for computing minimal enclosing balls and geometric medians in non-positive curvature.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00123"}, {"primary_key": "1139509", "vector": [], "sparse_vector": [], "title": "The Price of Explainability for Clustering.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Given a set of points in d-dimensional space, an explainable clustering is one where the clusters are specified by a tree of axis-aligned threshold cuts. <PERSON><PERSON><PERSON> et al. (ICML 2020) posed the question of the price of explainability: the worst-case ratio between the cost of the best explainable clusterings to that of the best clusterings.We show that the price of explainability for k medians is at most $1+H_{k-1}$; in fact, we show that the popular Random Thresholds algorithm has exactly this price of explainability, matching the known lower bound constructions. We complement our tight analysis of this particular algorithm by constructing instances where the price of explainability (using any algorithm) is at least $(1-o(1)) \\ln k$, showing that our result is best possible, up to lower-order terms. We also improve the price of explainability for the k-means problem to $O(k \\ln \\ln k)$ from the previous $O(k \\ln k)$, considerably closing the gap to the lower bounds of $\\Omega(k)$. Finally, we study the algorithmic question of finding the best explainable clustering: We show that explainable k medians and k-means cannot be approximated better than $O(\\ln k)$, under standard complexity-theoretic conjectures. This essentially settles the approximability of explainable k-medians and leaves open the intriguing possibility to get significantly better approximation algorithms for k-means than its price of explainability.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00067"}, {"primary_key": "1139510", "vector": [], "sparse_vector": [], "title": "Derandomization vs Refutation: A Unified Framework for Characterizing Derandomization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We establish an equivalence between two algorithmic tasks: derandomization, the deterministic simulation of probabilistic algorithms; and refutation, the deterministic construction of inputs on which a given probabilistic algorithm fails to compute a certain hard function. We prove that refuting low-space probabilistic streaming algorithms which attempt to compute functions $f \\in \\mathcal{F P}$ is equivalent to proving that $\\operatorname{pr} \\mathcal{B P} \\mathcal{P}=\\operatorname{pr} \\mathcal{P}$, even in cases where a lower bound for f against such streaming algorithms (without a refuter) is already unconditionally known. We also demonstrate the generality of our connection between refutation and derandomization, by establishing connections between refuting classes of constant-depth circuits of sublinear size and derandomizing constant-depth circuits of polynomial size with threshold gates (i.e., $\\mathcal{T C}^{0}$). Our connection generalizes and strengthens recent work on the characterization of derandomization. In particular, the refuter framework allows to directly compare several recent works to each other and to our work, as well as to chart a path for further progress. Along the way, we also improve the targeted hitting-set generator of <PERSON> and <PERSON> (FOCS 2021), showing that its translation of hardness to pseudorandomness scales down to $\\mathcal{T C}^{0}$.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00062"}, {"primary_key": "1139511", "vector": [], "sparse_vector": [], "title": "Planar Disjoint Paths, Treewidth, and Kernels.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the PLANAR DISJOINT PATHS problem, one is given an undirected planar graph with a set of k vertex pairs $\\left(s_{i}, t_{i}\\right)$ and the task is to find k pairwise vertex-disjoint paths such that the i-th path connects $s_{i}$ to $t_{i}$. We study the problem through the lens of kernelization, aiming at efficiently reducing the input size in terms of a parameter. We show that PLANAR DISJOINT PATHS does not admit a polynomial kernel when parameterized by k unless coNP $\\subseteq \\mathrm{NP} /$ poly, resolving an open problem by [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Ye<PERSON>, ESA'09]. Moreover, we rule out the existence of a polynomial Turing kernel unless the WKhierarchy collapses. Our reduction carries over to the setting of edge-disjoint paths, where the kernelization status remained open even in general graphs. On the positive side, we present a polynomial kernel for PLANAR DISJOINT PATHS parameterized by $k+\\mathrm{tw}$, where tw denotes the treewidth of the input graph. As a consequence of both our results, we rule out the possibility of a polynomialtime (Turing) treewidth reduction to $t w=k^{\\mathcal{O}(1)}$ under the same assumptions. To the best of our knowledge, this is the first hardness result of this kind. Finally, combining our kernel with the known techniques [<PERSON>, <PERSON>, <PERSON>, <PERSON>shtanov, <PERSON>urabh, Thilikos, JCTB'17; Schrijver, SICOMP'94] yields an alternative (and arguably simpler) proof that PLANAR DISJOINT PATHS can be solved in time $2^{\\mathcal{O}\\left(k^{2}\\right)} \\cdot n^{\\mathcal{O}(1)}$, matching the result of [Lokshtanov, Misra, Pilipczuk, Saurabh, Zehavi, STOC'20].", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00044"}, {"primary_key": "1139512", "vector": [], "sparse_vector": [], "title": "Quartic Samples Suffice for Fourier Interpolation.", "authors": ["<PERSON>", "Baocheng Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of interpolating a noisy Fourier-sparse signal in the time duration $[0, T]$ from noisy samples in the same range, where the ground truth signal can be any k-Fourier-sparse signal with band-limit $[-F, F]$. Our main result is an efficient Fourier Interpolation algorithm that improves the previous best algorithm by [<PERSON>, <PERSON>, <PERSON>, and <PERSON>, FOCS 2016] in the following three aspects:•The sample complexity is improved from $\\widetilde{O}\\left(k^{51}\\right)$ to $\\widetilde{O}\\left(k^{4}\\right)$.•The time complexity is improved from $\\widetilde{O}\\left(k^{10 \\omega+40}\\right)$ to $\\widetilde{O}\\left(k^{4 \\omega}\\right)$.•The output sparsity is improved from $\\widetilde{O}\\left(k^{10}\\right)$ to $\\widetilde{O}\\left(k^{4}\\right)$. Here, $\\omega$ denotes the exponent of fast matrix multiplication. The state-of-the-art sample complexity of this problem is $\\sim k^{4}$, but was only known to be achieved by an exponential-time algorithm. Our algorithm uses the same number of samples but has a polynomial runtime, laying the groundwork for an efficient Fourier Interpolation algorithm.The centerpiece of our algorithm is a new spectral analysis tool-the Signal Equivalent Method-which utilizes the structure of Fourier signals to establish nearly-optimal energy properties, and is the key for efficient and accurate frequency estimation. We use this method, along with a new sufficient condition for frequency recovery (a new high SNR band condition), to design a cheap algorithm for estimating \"significant\" frequencies within a narrow range. Together with a signal estimation algorithm, we obtain a new Fourier Interpolation algorithm for reconstructing the ground-truth signal.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00087"}, {"primary_key": "1139513", "vector": [], "sparse_vector": [], "title": "Tight Time-Space Lower Bounds for Constant-Pass Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In his breakthrough paper, <PERSON><PERSON> showed that any parity learning algorithm requires either quadratic memory or an exponential number of samples [FOCS'16, JACM'19]. A line of work that followed extended this result to a large class of learning problems. Until recently, all these results considered learning in the streaming model, where each sample is drawn independently, and the learner is allowed a single pass over the stream of samples. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> [CCC'19] considered a stronger model, allowing multiple passes over the stream. In the 2-pass model, they showed that learning parities of size n requires either a memory of size $n^{1.5}$ or at least $2^{\\sqrt{n}}$ samples. (Their result also generalizes to other learning problems.) In this work, for any constant q, we prove tight memory-sample lower bounds for any parity learning algorithm that makes q passes over the stream of samples. We show that such a learner requires either $\\Omega\\left(n^{2}\\right)$ memory size or at least $2^{\\Omega(n)}$ samples. Beyond establishing a tight lower bound, this is the first nontrivial lower bound for q-pass learning for any $q \\geq 3$. Similar to prior work, our results extend to any learning problem with many nearly-orthogonal concepts.We complement the lower bound with an upper bound, showing that parity learning with q passes can be done efficiently with $O\\left(n^{2} / \\log q\\right)$ memory.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00070"}, {"primary_key": "1139514", "vector": [], "sparse_vector": [], "title": "HDX Condensers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "More than twenty years ago, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> gave the first (and up to date only) explicit construction of a bipartite expander with almost full combinatorial expansion. The construction incorporates zig-zag ideas together with extractor technology, and is rather complicated. We give an alternative construction that builds upon recent constructions of hyper-regular, high-dimensional expanders. The new construction is, in our opinion, simple and elegant.Beyond demonstrating a new, surprising, and intriguing, application of high-dimensional expanders, the construction employs totally new ideas which we hope may lead to progress on the still remaining open problems in the area.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00100"}, {"primary_key": "1139515", "vector": [], "sparse_vector": [], "title": "A New Approach to Post-Quantum Non-Malleability.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We provide the first constant-round construction of post-quantum non-malleable commitments under the minimal assumption that post-quantum one-way functions exist. We achieve the standard notion of non-malleability with respect to commitments. Prior constructions required $\\Omega\\left(\\log ^{*} \\lambda\\right)$ rounds under the same assumption. We achieve our results through a new technique for constant-round non-malleable commitments which is easier to use in the post-quantum setting. The technique also yields an almost elementary proof of security for constant-round non-malleable commitments in the classical setting, which may be of independent interest. When combined with existing work, our results yield the first constant-round quantum-secure multiparty computation for both classical and quantum functionalities in the plain model, under the polynomial hardness of quantum fully-homomorphic encryption and quantum learning with errors.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00041"}, {"primary_key": "1139516", "vector": [], "sparse_vector": [], "title": "Parameterized Approximation Schemes for Clustering with General Norm Objectives.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Jaroslaw Byrka", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper considers the well-studied algorithmic regime of designing a $(1+\\epsilon)$-approximation algorithm for a k-clustering problem that runs in time $f(k,\\epsilon)poly(n)$ (sometimes called an efficient parameterized approximation scheme or EPAS for short 1 ). Notable results of this kind include EPASes in the high-dimensional Euclidean setting for k-center [<PERSON><PERSON><PERSON>, Har-<PERSON>, Indyk; STOC'02] as well as k-median, and k-means [<PERSON>, <PERSON>; J. <PERSON> 2010]. Our main contribution is a clean and simple EPAS that settles more than ten clustering problems (across multiple well-studied objectives as well as metric spaces) and unifies well-known EPASes. More specifically, our algorithm gives EPASes in the following settings:•Clustering objectives: k-means, k-center, k-median, priority k-center, $\\ell$-centrum, ordered k-median, socially fair k-median (aka robust k-median), or any other objective that can be formulated as minimizing a monotone (not necessarily symmetric!) norm of the distances of the points from the solution (generalizing the symmetric formulation introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON> [STOC'19]).•Metric spaces: Continuous high-dimensional Euclidean spaces, metrics of bounded doubling dimension, bounded treewidth metrics, and planar metrics. Prior to our results, EPASes were only known for vanilla clustering objectives (k-means, k-median, and k-center) and each such algorithm is tailored to work for the specific input metric and clustering objective (e.g., EPASes for k means and k-center in $\\mathbb{R}^{d}$ are conceptually very different). In contrast, our algorithmic framework is applicable to a wide range of well-studied objective functions in a uniform way, and is (almost) entirely oblivious to any specific metric structures and yet is able to effectively exploit those unknown structures. In particular, our algorithm is not based on the (metric- and objective-specific) technique of coresets. Key to our analysis is a new concept that we call bounded $\\epsilon$-scatter dimension—an intrinsic complexity measure of a metric space that is a relaxation of the standard notion of bounded doubling dimension(often used as a source of algorithmic tractability for geometric problems). Our main technical result shows that two conditions are essentially sufficient for our algorithm to yield an EPAS on the input metric M for any clustering objective:(i)The objective is described by a monotone norm, and(ii)the $\\epsilon$-scatter dimension of M is upper bounded by a function of $\\epsilon$. 1 Quick remarks: (i) An EPAS is not comparable to polynomial time approximation schemes (PTAS), (ii) before the term EPAS was invented some researchers call this type of approximation schemes a PTAS or simply an approximation scheme (in clustering, it is often assumed that k is small) [1], [2], and (iii) both EPAS and PTAS are implied by the existence of efficient polynomial time approximation schemes (EPTAS).", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00085"}, {"primary_key": "1139517", "vector": [], "sparse_vector": [], "title": "A proof that Reed-Muller codes achieve Shannon capacity on symmetric channels.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In 1948, <PERSON> used a probabilistic argument to show that there exist codes achieving a maximal rate defined by the channel capacity. In 1954, <PERSON> and <PERSON> introduced a simple deterministic code construction, conjectured shortly after to achieve channel capacity. Major progress was made towards establishing this conjecture over the last decades, with various branches of discrete mathematics involved such as combinatorial bounds, sharp thresholds, hypercontractivity, additive combinatorics and polarization theory. In particular, the special case of the erasure channel was settled by <PERSON><PERSON><PERSON> at al., relying on <PERSON><PERSON><PERSON><PERSON>'s sharp threshold theorem for symmetric monotone properties. The main case of error channels remained however unsettled, due in particular to the property being non-monotone and the lack of techniques to obtain fast local error decay up to capacity, despite the notable vanishing bound on the local error from <PERSON><PERSON><PERSON>fister. This paper closes the conjecture's proof. The main ingredient is a new recursive boosting framework for coding, where codewords are decoded by aggregating restrictions on a 'subspace-sunflower' structure, analogous to the structure from Erdős-Rado 1960. The dependencies between the sunflower petals are handled with an $L_{2}$ and $L_{4}$ Boolean Fourier analysis, and a list-decoding argument with a weight enumerator bound from Sberlo-Shpilka is used to control the global error from the local one. For the local error, while monotonicity does not apply, we show that a 'weak threshold' result still holds using solely symmetries. This gives in particular a shortened and tightened argument for the vanishing local error result, and with prior works, it also implies the strong wire-tap secrecy of RM codes on pure-state classical-quantum channels.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00020"}, {"primary_key": "1139518", "vector": [], "sparse_vector": [], "title": "All-Pairs Max-Flow is no Harder than Single-Pair Max-Flow: Gomory-<PERSON> Trees in Almost-Linear Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "A Gomory-Hu tree (also called a cut tree) succinctly represents $(s, t)$ min-cuts (and therefore, $(s, t)$ max-flow values) of all pairs of vertices $s, t$ in an undirected graph. In this paper, we give an $m^{1+o(1)}$-time algorithm for constructing a Gomory-Hu tree for a graph with m edges. This shows that the all-pairs max-flows problem has the same running time as the single-pair max-flow problem, up to a subpolynomial factor. Prior to our work, the best known Gomory-Hu tree algorithm was obtained in recent work by <PERSON><PERSON><PERSON> et al. (FOCS 2022) and requires $\\tilde{O}\\left(n^{2}\\right)$ time for a graph with n vertices. Our result marks a natural culmination of over 60 years of research into the all-pairs maxflows problem that started with <PERSON><PERSON><PERSON> and <PERSON>'s pathbreaking result introducing the Gomory-Hu tree in 1961.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00137"}, {"primary_key": "1139519", "vector": [], "sparse_vector": [], "title": "The minimal canonical form of a tensor network.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Freek Witteveen"], "summary": "Tensor networks have a gauge degree of freedom on the virtual degrees of freedom that are contracted. A canonical form is a choice of fixing this degree of freedom. For matrix product states, choosing a canonical form is a powerful tool, both for theoretical and numerical purposes. On the other hand, for tensor networks in dimension two or greater there is only limited understanding of the gauge symmetry. Here we introduce a new canonical form, the minimal canonical form, which applies to projected entangled pair states (PEPS) in any dimension, and prove a corresponding fundamental theorem. Already for matrix product states this gives a new canonical form, while in higher dimensions it is the first rigorous definition of a canonical form valid for any choice of tensor. We show that two tensors have the same minimal canonical forms if and only if they are gauge equivalent up to taking limits; moreover, this is the case if and only if they give the same quantum state for any geometry. In particular, this implies that the latter problem is decidable - in contrast to the well-known undecidability for equality of PEPS on grids. We also provide rigorous algorithms for computing minimal canonical forms. To achieve this we draw on geometric invariant theory and recent progress in theoretical computer science in non-commutative group optimization.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00027"}, {"primary_key": "1139520", "vector": [], "sparse_vector": [], "title": "Optimal PAC Bounds without Uniform Convergence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In statistical learning theory, determining the sample complexity of realizable binary classification for VC classes was a long-standing open problem. The results of <PERSON> [1] and <PERSON><PERSON><PERSON> [2] established sharp upper bounds in this setting. However, the reliance of their argument on the uniform convergence principle limits its applicability to more general learning settings such as multiclass classification. In this paper, we address this issue by providing optimal high probability risk bounds through a framework that surpasses the limitations of uniform convergence arguments.Our framework converts the leave-one-out error of permutation invariant predictors into high probability risk bounds. As an application, by adapting the one-inclusion graph algorithm of Ha<PERSON>ler, Littlestone, and Warmuth [3], we propose an algorithm that achieves an optimal PAC bound for binary classification. Specifically, our result shows that certain aggregations of one-inclusion graph algorithms are optimal, addressing a variant of a classic question posed by <PERSON><PERSON> [4].We further instantiate our framework in three settings where uniform convergence is provably suboptimal. For multiclass classification, we prove an optimal risk bound that scales with the one-inclusion hypergraph density of the class, addressing the suboptimality of the analysis of <PERSON><PERSON> and <PERSON><PERSON>v-<PERSON> [5]. For partial hypothesis classification, we determine the optimal sample complexity bound, resolving a question posed by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> [6]. For realizable bounded regression with absolute loss, we derive an optimal risk bound that relies on a modified version of the scale-sensitive dimension, refining the results of <PERSON> and <PERSON> [7]. Our rates surpass standard uniform convergence-based results due to the smaller complexity measure in our risk bound.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00071"}, {"primary_key": "1139521", "vector": [], "sparse_vector": [], "title": "Why we couldn&apos;t prove SETH hardness of the Closest Vector Problem for even norms!", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Recent work has shown SETH hardness of CVP in the $\\ell_{p}$ norm for any p that is not an even integer. This result was shown by giving a Karp reduction from k-SAT on n variables to CVP on a lattice of rank n. In this work, we show a barrier towards proving a similar result for C<PERSON> in the $\\ell_{p}$ norm where p is an even integer. We show that for any $c\\gt0$, if for every $k\\gt0$, there exists an efficient reduction that maps a k-SAT instance on n variables to a CVP instance for a lattice of rank at most $n^{c}$ in the Euclidean norm, then coNP $\\subset NP/Poly$. We prove a similar result for CVP for all even norms under a mild additional promise that the ratio of the distance of the target from the lattice and the shortest non-zero vector in the lattice is bounded by $\\exp \\left(n^{O(1)}\\right)$. Furthermore, we show that for any $c\\gt0$, and any even integer p, if for every $k\\gt0$, there exists an efficient reduction that maps a k-SAT instance on n variables to a $SVP_{p}$ instance for a lattice of rank at most $n^{c}$, then coNP $\\subset NP /$ Poly. 1 While prior results have indicated that lattice problems in the $\\ell_{2}$ norm (Euclidean norm) are easier than lattice problems in other norms, this is the first result that shows a separation between these problems. We achieve this by using a result by <PERSON> and <PERSON>kebeek on the impossibility of the existence of a reduction that compresses an arbitrary k-SAT instance into a string of length $\\mathcal{O}\\left(n^{k-\\varepsilon}\\right)$ for any $\\varepsilon\\gt0$. In addition to CVP, we also show that the same result holds for the Subset-Sum problem using similar techniques. 1 The result for SVP does not require any additional promise.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00138"}, {"primary_key": "1139522", "vector": [], "sparse_vector": [], "title": "Singular Value Approximation and Sparsifying Random Walks on Directed Graphs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we introduce a new, spectral notion of approximation between directed graphs, which we call singular value (SV) approximation. SV-approximation is stronger than previous notions of spectral approximation considered in the literature, including spectral approximation of Laplacians for undirected graphs [ST04], standard approximation for directed graphs [CKP + 17], and unit-circle (UC) approximation for directed graphs [AKM + 20]. Further, SV approximation enjoys several useful properties not possessed by previous notions of approximation, e.g., it is preserved under products of randomwalk matrices and bounded matrices. We provide a nearly linear-time algorithm for SV-sparsifying (and hence UC-sparsifying) Eulerian directed graphs, as well as $\\ell$-step random walks on such graphs, for any $\\ell \\leq \\operatorname{poly}(n)$. Combined with the Eulerian scaling algorithms of [CKK + 18], given an arbitrary (not necessarily Eulerian) directed graph and a set S of vertices, we can approximate the stationary probability mass of the $\\left(S, S^{c}\\right)$ cut in an $\\ell$-step random walk to within a multiplicative error of $1 / \\operatorname{polylog}(n)$ and an additive error of $1 / \\operatorname{poly}(n)$ in nearly linear time. As a starting point for these results, we provide a simple black-box reduction from SV-sparsifying Eulerian directed graphs to SV-sparsifying undirected graphs; such a directed-to-undirected reduction was not known for previous notions of spectral approximation.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00054"}, {"primary_key": "1139523", "vector": [], "sparse_vector": [], "title": "Generalizations of Matrix Multiplication can solve the Light Bulb Problem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the light bulb problem, one is given as input vectors $x_{1}, \\ldots, x_{n}, y_{1}, \\ldots, y_{n} \\in\\{-1,1\\}^{d}$ which are all uniformly random. They are all chosen independently except for a planted pair $\\left(x_{i^{*}}, y_{j^{*}}\\right)$ which is chosen to have correlation $\\rho$ for some constant $\\rho\\gt 0$. The goal is to find the planted pair. The light bulb problem was introduced over 30 years ago by <PERSON><PERSON>, and is known to have many applications in data analysis, statistics, and learning theory. The naive algorithm runs in $\\Omega\\left(n^{2}\\right)$ time, and algorithms based on Locality-Sensitive Hashing approach quadratic time as $\\rho \\rightarrow 0$. In 2012, <PERSON><PERSON> gave a breakthrough algorithm running in time $O\\left(n^{(5-\\omega)} /(4-\\omega)\\right)\\lt O\\left(n^{1.615}\\right)$, no matter how small $\\rho\\gt 0$ is, by making use of fast matrix multiplication. This was subsequently refined by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> in 2016 to running time $O\\left(n^{2 \\omega / 3}\\right)\\lt $ $O\\left(n^{1.582}\\right)$, but is essentially the only known approach for this important problem. In this paper, we propose a new approach based on replacing fast matrix multiplication with other variants and generalizations of matrix multiplication, which can be computed faster than matrix multiplication, but which may omit some terms one is supposed to compute, and include additional error terms. Our new approach can make use of a wide class of tensors which previously had no known algorithmic applications, including tensors which arise naturally as intermediate steps in border rank methods and in the Laser method. We further show that our approach can be combined with locality-sensitive hashing to design an algorithm whose running time improves as $\\rho$ gets larger. To our knowledge, this is the first algorithm which combines fast matrix multiplication with hashing for the light bulb problem or any closest pair problem, and it leads to faster algorithms for small $\\rho\\gt 0$. We then focus on tensors for \"multiplying\" $2 \\times 2$ matrices; using such small tensors is typically required for practical algorithms. In this setting, the best prior algorithm, using Strassen's algorithm for matrix multiplication, yields a running time of only $O\\left(n^{1.872}\\right)$. We introduce a new such low-rank tensor we call $T_{2112}$, which has omissions and errors compared to matrix multiplication, and using it, we design a new algorithm for the light bulb problem which runs in time $O\\left(n^{1.797}\\right)$. We also explain why we are optimistic that this approach could yield asymptotically faster algorithms for the light bulb problem.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00090"}, {"primary_key": "1139524", "vector": [], "sparse_vector": [], "title": "Faster high-accuracy log-concave sampling via algorithmic warm starts.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "It is a fundamental problem to understand the complexity of high-accuracy sampling from a strongly log-concave density $\\pi$ on $\\mathbb{R}^{d}$. Indeed, in practice, high-accuracy samplers such as the Metropolis-adjusted Langevin algorithm (MALA) remain the de facto gold standard; and in theory, via the proximal sampler reduction, it is understood that such samplers are key for sampling even beyond log-concavity (in particular, for sampling under isoperimetric assumptions).This paper improves the dimension dependence of this sampling problem to $\\widetilde{O}\\left(d^{1 / 2}\\right)$, whereas the previous best result for MALA was $\\widetilde{O}(d)$. This closes the long line of work on the complexity of MALA, and moreover leads to state-of-the-art guarantees for high-accuracy sampling under strong log-concavity and beyond (thanks to the aforementioned reduction).Our starting point is that the complexity of MALA improves to $\\widetilde{O}\\left(d^{1 / 2}\\right)$, but only under a warm start (an initialization with constant Rényi divergence w.r.t. $\\pi$). Previous algorithms for finding a warm start took $O(d)$ time and thus dominated the computational effort of sampling. Our main technical contribution resolves this gap by establishing the first $\\widetilde{O}\\left(d^{1 / 2}\\right)$ Rényi mixing rates for the discretized underdamped Langevin diffusion. For this, we develop new differential-privacy-inspired techniques based on Rényi divergences with Orlicz-<PERSON>serstein shifts, which allow us to sidestep longstanding challenges for proving fast convergence of hypocoercive differential equations.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00134"}, {"primary_key": "1139525", "vector": [], "sparse_vector": [], "title": "Sub-quadratic (1+ϵ)-approximate Euclidean Spanners, with Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We study graph spanners for point-set in the high-dimensional Euclidean space. On the one hand, we prove that spanners with stretch $\\lt \\sqrt{2}$ and subquadratic size are not possible, even if we add Steiner points. On the other hand, if we add extra nodes to the graph (non-metric Steiner points), then we can obtain $(1+\\epsilon)$-approximate spanners of subquadratic size. We show how to construct a spanner of size $n^{2-\\Omega\\left(\\epsilon^{3}\\right)}$, as well as a directed version of the spanner of size $n^{2-\\Omega\\left(\\epsilon^{2}\\right)}$. We use our directed spanner to obtain an algorithm for computing $(1+\\epsilon)$-approximation to Earth-Mover Distance (optimal transport) between two sets of size n in time $n^{2-\\Omega\\left(\\epsilon^{2}\\right)}$.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00014"}, {"primary_key": "1139526", "vector": [], "sparse_vector": [], "title": "Advisor-Verifier-Prover Games and the Hardness of Information Theoretic Cryptography.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "A major open problem in information-theoretic cryptography is to obtain a super-polynomial lower bound for the communication complexity of basic cryptographic tasks. This question is wide open even for very powerful non-interactive primitives such as private information retrieval (or locally-decodable codes), general secret sharing schemes, conditional disclosure of secrets, and fully-decomposable randomized encoding (or garbling schemes). In fact, for all these primitives we do not even have super-linear lower bounds. Furthermore, it is unknown how to relate these questions to each other or to other complexity-theoretic questions.In this note, we relate all these questions to the classical topic of query/space trade-offs, lifted to the setting of interactive proof systems. Specifically, we consider the following Advisor-Verifier-Prover (AVP) game: First, a function f is given to the advisor who computes an advice a. Next, an input x is given to the verifier and to the prover who claims that $f(x) \\quad =1.$ The verifier should check this claim via a single round of interaction based on the private advice a and without having any additional information on f. We focus on the case where the prover is laconic and communicates only a constant number of bits, and, mostly restrict the attention to the simplest, purely information-theoretic setting, where all parties are allowed to be computationally unbounded. The goal is to minimize the total communication complexity which is dominated by the length of the advice plus the length of the verifier's query.As our main result, we show that a super-polynomial lower bound for AVPs implies a super-polynomial lower bound for a wide range of information-theoretic cryptographic tasks. In particular, we present a communication-efficient transformation from any of the above primitives into an AVP protocol. Interestingly, each primitive induces some additional property over the resulting protocol. Thus AVP games form a new common yardstick that highlights the differences between all the above primitives.Equipped with this view, we revisit the existing (somewhat weak) lower bounds for the above primitives, and show that many of these lower bounds can be unified by proving a single counting-based lower bound on the communication of AVPs, whereas some techniques are inherently limited to specific domains. The latter is shown by proving the first polynomial separations between the complexity of secret-sharing schemes and conditional disclosure of secrets and between the complexity of randomized encodings and conditional disclosure of secrets.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00039"}, {"primary_key": "1139527", "vector": [], "sparse_vector": [], "title": "IOPs with Inverse Polynomial Soundness Error.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "E<PERSON>"], "summary": "We show that every language in NP has an Interactive Oracle Proof (IOP) with inverse polynomial soundness error and small query complexity. This achieves parameters that surpass all previously known PCPs and IOPs. Specifically, we construct an IOP with perfect completeness, soundness error $1 / n$, round complexity $O(\\log \\log n)$, proof length poly $(n)$ over an alphabet of size $O(n)$, and query complexity $O(\\log \\log n)$. This is a step forward in the quest to establish the sliding-scale conjecture for IOPs (which would additionally require query complexity $O(1))$. Our main technical contribution is a high-soundness small-query proximity test for the Reed-Solomon code. We construct an IOP of proximity for Reed-Solomon codes, over a field $\\mathbb{F}$ with evaluation domain L and degree d, with perfect completeness, soundness error (roughly) $\\max \\{1-\\delta, O(\\rho^{1 / 4})\\}$ for $\\delta$-far functions, round complexity $O(\\log \\log d)$, proof length $O(|L| / \\rho)$ over $\\mathbb{F}$, and query complexity $O(\\log \\log d)$; here $\\rho=(d+1) /|L|$ is the code rate. En route, we obtain a new high-soundness proximity test for bivariate Reed-Muller codes.The IOP for NP is then obtained via a high-soundness reduction from NP to Reed-Solomon proximity testing with rate $\\rho=1 / \\operatorname{poly}(n)$ and distance $\\delta=1-1 / \\operatorname{poly}(n)$ (and applying our proximity test). Our constructions are direct and efficient, and hold the potential for practical realizations that would improve the state-of-the-art in real-world applications of IOPs.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00050"}, {"primary_key": "1139528", "vector": [], "sparse_vector": [], "title": "Hidden Permutations to the Rescue: Multi-Pass Streaming Lower Bounds for Approximate Matchings.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove that any semi-streaming algorithm for $(1+\\varepsilon)$ approximation of maximum bipartite matching requires \\begin{equation*}\\Omega\\left(\\frac{\\log (1 / \\varepsilon)}{\\log (1 / \\beta)}\\right)\\end{equation*} passes, where $\\beta \\in(0,1)$ is the largest parameter so that an n-vertex graph with $n^{\\beta}$ edge-disjoint induced matchings of size $\\Theta(n)$ exist (such graphs are referred to as Ruzsa-Szemerédi graphs). Currently, it is known that \\begin{equation*}\\Omega\\left(\\frac{1}{\\log \\log n}\\right) \\leqslant \\beta \\leqslant 1-\\Theta\\left(\\frac{\\log ^{*} n}{\\log n}\\right)\\end{equation*} and closing this huge gap between upper and lower bounds has remained a notoriously difficult problem in combinatorics.Under the plausible hypothesis that $\\beta=\\Omega(1)$, our lower bound result provides the first pass-approximation lower bound for (small) constant approximation of matchings in the semi-streaming model, a longstanding open question in the graph streaming literature.Our techniques are based on analyzing communication protocols for compressing (hidden) permutations. Prior work in this context relied on reducing such problems to Boolean domain and analyzing them via tools like XOR Lemmas and Fourier analysis on Boolean hypercube. In contrast, our main technical contribution is a hardness amplification result for permutations through concatenation in place of prior XOR Lemmas. This result is proven by analyzing permutations directly via simple tools from group representation theory combined with detailed information-theoretic arguments, and can be of independent interest.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00058"}, {"primary_key": "1139529", "vector": [], "sparse_vector": [], "title": "Exponential quantum speedup in simulating coupled classical oscillators*.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We study the problem of simulating the time evolution of a system of 2 n classical coupled oscillators (e.g., 2 n balls connected by springs) on a quantum computer. We map <PERSON>'s equation for harmonic potentials to <PERSON><PERSON><PERSON><PERSON><PERSON>'s equation, such that the amplitudes of an $\\mathcal{O}(n)$-qubit quantum state encode the momenta and displacements of the 2 n classical oscillators. Given oracle access to the masses and spring constants, we describe a quantum algorithm with query and time complexity poly (n) that solves this problem when certain parameters are polynomially bounded and the initial state is easy to prepare. As an example application, we apply our quantum algorithm to efficiently estimate the normalized kinetic energy of an oscillator at any time. We then show that any classical algorithm solving the same problem must make $2^{\\Omega(n)}$ queries to the oracle and we also show that when the oracles are instantiated by poly (n)-size circuits, the problem is BQP-complete. Thus, our approach solves a potentially practical application with an exponential speedup over classical computers.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00030"}, {"primary_key": "1139530", "vector": [], "sparse_vector": [], "title": "Krylov Methods are (nearly) Optimal for Low-Rank Approximation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of rank-1 low-rank approximation (LRA) in the matrix-vector product model under various Schatten norms:\\begin{equation*}\\min _{\\|u\\|_{2}=1}\\left\\|A\\left(I-u u^{\\top}\\right)\\right\\|_{\\mathcal{S}_{p}}\\end{equation*}where $\\|M\\|_{\\mathcal{S}_{p}}$ denotes the $\\ell_{p}$ norm of the singular values of M. Given $\\varepsilon\\gt 0$, our goal is to output a unit vector v such that \\begin{equation*}\\left\\|A\\left(I-v v^{\\top}\\right)\\right\\|_{\\mathcal{S}_{p}} \\leqslant\\left(1+\\varepsilon\\right) \\min _{\\|u\\|_{2}=1}\\left\\|A\\left(I-u u^{\\top}\\right)\\right\\|_{\\mathcal{S}_{p}}\\end{equation*}Our main result shows that Krylov methods (nearly) achieve the information-theoretically optimal 1 number of matrix-vector products for Spectral $(p=\\infty)$, Frobenius $(p=2)$ and Nuclear $(p=1)$ LRA. In particular, for Spectral LRA, we show that any algorithm requires $\\Omega\\left(\\log (n) / \\varepsilon^{1 / 2}\\right)$ matrix-vector products, exactly matching the upper bound obtained by Krylov methods [40]. Our lower bound addresses Open Question 1 in [59], providing evidence for the lack of progress on algorithms for Spectral LRA and resolves Open Question 1.2 in [5]. Next, we show that for any fixed constant p, i.e. $1 \\leqslant p=O(1)$, there is an upper bound of $O\\left(\\log (1 / \\varepsilon) / \\varepsilon^{1 / 3}\\right)$ matrix-vector products, implying that the complexity does not grow as a function of input size. This improves the $O\\left(\\log (n / \\varepsilon) / \\varepsilon^{1 / 3}\\right)$ bound recently obtained in [5], and matches their $\\Omega\\left(1 / \\varepsilon^{1 / 3}\\right)$ lower bound, to a $\\log (1 / \\varepsilon)$ factor. 1 For Spectral LRA, the upper and lower bounds match up to a fixed universal constant. For Frobenius and Nuclear LRA, they match up to a $\\log (1 / \\varepsilon)$ factor.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00128"}, {"primary_key": "1139531", "vector": [], "sparse_vector": [], "title": "Extracting Randomness from Samplable Distributions, Revisited.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>-<PERSON>ed", "<PERSON><PERSON>"], "summary": "Randomness extractors provide a generic way of converting sources of randomness that are merely unpredictable into almost uniformly random bits. While in general, deterministic randomness extraction is impossible, it is possible if the source has some structural constraints.While much of the literature on deterministic extraction has focused on sources with strong independence properties, a natural class where deterministic extraction is possible is sources that can sampled by a polynomial size circuit, <PERSON> [SIAM J Comp'86]. <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [FOCS'00] explicitly constructed deterministic randomness extractors for this class of sources, assuming very strong circuit lower bounds.We suggest that there is perhaps an even more reasonable model of natural sources of randomness than <PERSON>'s: sources sampled by polynomial size quantum circuits. Under a suitable circuit lower bound, we show that <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s extractor indeed works for this class.Along the way, we substantially improve their analysis in the classical case, showing that a circuit lower bound against NP-circuits suffice in the classical case (as opposed to a lower bounds on $\\Sigma_{5}$-circuits, as shown by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>). Moreover, we show that under this assumption, it is possible to handle sources sampled by postselecting circuits (a variant of nondeterministic circuits). We show that this model is sufficient to capture randomness extraction in the presence of efficiently computable leakage.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00092"}, {"primary_key": "1139532", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON> Comes to Cryptomania: On Interactive Kolmogorov Complexity and Key-Agreement.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Only a handful candidates for computational assumptions that imply secure key-agreement protocols (KA) are known, and even fewer are believed to be quantum safe. In this paper, we present a new hardness assumption-the worst-case hardness of a promise problem related to an interactive version of Kolmogorov Complexity. Roughly speaking, the promise problem requires telling apart tuples of strings $(\\pi, x, y)$ with relatively (w.r.t. $\\mathrm{K}(\\pi)$) low time-bounded Interactive Kolmogorov Complexity $\\left(\\mathrm{IK}^{t}\\right)$, and those with relatively high Kolmogorov complexity, given the promise that $\\mathrm{K}^{t}(x \\mid y)\\lt s, \\mathrm{~K}^{t}(y \\mid x)\\lt s$ and $s=\\log n$, and where $\\mathrm{IK}^{t}(\\pi ; x ; y)$ is defined as the length of the shortest pair of t-bounded TMs $(A, B)$ such that the interaction of $(A, B)$ lead to the transcript $\\pi$ and the respective outputs $x, y$. We demonstrate that when t is some polynomial, then not only does this hardness assumption imply the existence of KA, but it is also necessary for the existence of secure KA. As such, it yields the first natural hardness assumption characterizing the existence of key-agreement protocols. We additionally show that when the threshold s is bigger (e.g., $s=55 \\log n$), then the (worst-case) hardness of this problem instead characterizes the existence of one-way functions (OWFs). As such, our work also clarifies exactly what it would take to base KA on the existence of OWFs, and demonstrates that this question boils down to demonstrating a worst-case reduction between two closely related promise problems.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00034"}, {"primary_key": "1139533", "vector": [], "sparse_vector": [], "title": "Local Computation Algorithms for Maximum Matching: New Lower Bounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>via<PERSON>"], "summary": "We study local computation algorithms (LCA) for maximum matching. An LCA does not return its output entirely, but reveals parts of it upon query. For matchings, each query is a vertex v; the LCA should return whether v is matched—and if so to which neighbor—while spending a small time per query. In this paper, we prove that any LCA that computes a matching that is at most an additive of $\\epsilon n$ smaller than the maximum matching in n-vertex graphs of maximum degree $\\Delta$ must take at least $\\Delta^{\\Omega(1 / \\varepsilon)}$ time. This comes close to the existing upper bounds that take $(\\Delta / \\epsilon)^{O\\left(1 / \\epsilon^{2}\\right)} \\operatorname{polylog}(n)$ time. In terms of sublinear time algorithms, our techniques imply that any algorithm that estimates the size of maximum matching up to an additive error of $\\epsilon n$ must take $\\Delta^{\\Omega(1 / \\epsilon)}$ time. This negatively resolves a decade old open problem of the area (see Open Problem 39 of sublinear.info) on whether such estimates can be achieved in $\\operatorname{poly}(\\Delta / \\epsilon)$ time.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00143"}, {"primary_key": "1139534", "vector": [], "sparse_vector": [], "title": "Locally Uniform Hashing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hashing is a common technique used in data processing, with a strong impact on the time and resources spent on computation. Hashing also affects the applicability of theoretical results that often assume access to (unrealistic) uniform/fully-random hash functions. In this paper, we are concerned with designing hash functions that are practical and come with strong theoretical guarantees on their performance.To this end, we present tornado tabulation hashing, which is simple, fast, and exhibits a certain full, local randomness property that provably makes diverse algorithms perform almost as if (abstract) fully-random hashing was used. For example, this includes classic linear probing, the widely used HyperLogLog algorithm of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> [AOFA'97] for counting distinct elements, and the one-permutation hashing of <PERSON>, <PERSON>, and <PERSON> [NIPS'12] for large-scale machine learning. We also provide a very efficient solution for the classical problem of obtaining fully-random hashing on a fixed (but unknown to the hash function) set of n keys using O(n) space. As a consequence, we get more efficient implementations of the splitting trick of <PERSON><PERSON><PERSON><PERSON>binger and Rink [ICALP'09] and the succinct space uniform hashing of <PERSON><PERSON> and <PERSON>gh [SICOMP'08].Tornado tabulation hashing is based on a simple method to systematically break dependencies in tabulation-based hashing techniques.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00089"}, {"primary_key": "1139535", "vector": [], "sparse_vector": [], "title": "Chasing Positive Bodies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Thatchaphol <PERSON>"], "summary": "We study the problem of chasing positive bodies in $\\ell_{1}$: given a sequence of bodies $K_{t}=\\left\\{x^{t} \\in \\mathbb{R}_{+}^{n} \\mid C^{t} x^{t} \\geq 1, P^{t} x^{t} \\leq 1\\right\\}$ revealed online, where $C^{t}$ and $P^{t}$ are nonnegative matrices, the goal is to (approximately) maintain a point $x_{t} \\in K_{t}$ such that $\\sum_{t}\\left\\|x_{t}-x_{t-1}\\right\\|_{1}$ is minimized. This captures the fully-dynamic low-recourse variant of any problem that can be expressed as a mixed packing-covering linear program and thus also the fractional version of many central problems in dynamic algorithms such as set cover, load balancing, hyperedge orientation, minimum spanning tree, and matching.We give an $O(\\log d)$-competitive algorithm for this problem, where d is the maximum row sparsity of any matrix $C^{t}$. This bypasses and improves exponentially over the lower bound of $\\sqrt{n}$ known for general convex bodies. Our algorithm is based on iterated information projections, and, in contrast to general convex body chasing algorithms, is entirely memoryless.We also show how to round our solution dynamically to obtain the first fully dynamic algorithms with competitive recourse for all the stated problems above; i.e. their recourse is less than the recourse of every other algorithm on every update sequence, up to polylogarithmic factors. This is a significantly stronger notion than the notion of absolute recourse in the dynamic algorithms literature.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00103"}, {"primary_key": "1139536", "vector": [], "sparse_vector": [], "title": "Dynamic (1+ϵ)-Approximate Matching Si<PERSON> in Truly Sublinear Update Time.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Thatchaphol <PERSON>"], "summary": "We show a fully dynamic algorithm for maintaining $(1+\\epsilon)$-approximate size of maximum matching of the graph with n vertices and m edges using $m^{0.5-\\Omega_{\\epsilon}(1)}$ update time. This is the first polynomial improvement over the long-standing $O(n)$ update time, which can be trivially obtained by periodic recomputation. Thus, we resolve the value version of a major open question of the dynamic graph algorithms literature (see, e.g., [<PERSON> and <PERSON> FOCS'13], [<PERSON> and <PERSON>OD<PERSON>'16], [<PERSON><PERSON><PERSON><PERSON> and <PERSON>na SODA'22]). Our key technical component is the first sublinear algorithm for $(1, \\epsilon n)$-approximate maximum matching with sublinear running time on dense graphs. All previous algorithms suffered a multiplicative approximation factor of at least 1.499 or assumed that the graph has a very small maximum degree.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00095"}, {"primary_key": "1139537", "vector": [], "sparse_vector": [], "title": "A d1/2+o(1) Monotonicity Tester for Boolean Functions on d-Dimensional Hypergrids.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Monotonicity testing of Boolean functions on the hypergrid, $f:[n]^{d} \\rightarrow\\{0,1\\}$, is a classic topic in property testing. Determining the non-adaptive complexity of this problem is an important open question. For arbitrary n, [Black-<PERSON><PERSON><PERSON>, SODA 2020] describe a tester with query complexity $\\widetilde{O}\\left(\\varepsilon^{-4 / 3} d^{5 / 6}\\right)$. This complexity is independent of n, but has a suboptimal dependence on d. Recently, [<PERSON><PERSON><PERSON><PERSON>-<PERSON>-<PERSON>, ITCS 2023] and [<PERSON><PERSON><PERSON><PERSON>-<PERSON>dh<PERSON>, STOC 2023] describe $\\widetilde{O}\\left(\\varepsilon^{-2} n^{3} \\sqrt{d}\\right)$ and $\\widetilde{O}\\left(\\varepsilon^{-2} n \\sqrt{d}\\right)$-query testers, respectively. These testers have an almost optimal dependence on d, but a suboptimal polynomial dependence on n. In this paper, we describe a non-adaptive, onesided monotonicity tester with query complexity $O\\left(\\varepsilon^{-2} d^{1 / 2+o(1)}\\right)$, independent of n. Up to the $d^{o(1)}$. factors, our result resolves the non-adaptive complexity of monotonicity testing for Boolean functions on hypergrids. The independence of n yields a non-adaptive, one-sided $O\\left(\\varepsilon^{-2} d^{1 / 2+o(1)}\\right)$-query monotonicity tester for Boolean functions $f: \\mathbb{R}^{d} \\rightarrow\\{0,1\\}$ associated with an arbitrary product measure.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00110"}, {"primary_key": "1139538", "vector": [], "sparse_vector": [], "title": "Testing Graph Properties with the Container Method.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We establish nearly optimal sample complexity bounds for testing the $\\rho$-clique property in the dense graph model. Specifically, we show that it is possible to distinguish graphs on n vertices that have a $\\rho n$-clique from graphs for which at least $\\epsilon n^{2}$ edges must be added to form a $\\rho n$-clique by sampling and inspecting a random subgraph on only $\\tilde{O}\\left(\\rho^{3} / \\epsilon^{2}\\right)$ vertices. We also establish new sample complexity bounds for $\\epsilon$-testing k-colorability. In this case, we show that a sampled subgraph on $\\tilde{O}(k / \\epsilon)$ vertices suffices to distinguish k-colorable graphs from those for which any k-coloring of the vertices causes at least $\\epsilon n^{2}$ edges to be monochromatic. The new bounds for testing the $\\rho$-clique and k-colorability properties are both obtained via new extensions of the graph container method. This method has been an effective tool for tackling various problems in graph theory and combinatorics. Our results demonstrate that it is also a powerful tool for the analysis of property testing algorithms.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00109"}, {"primary_key": "1139539", "vector": [], "sparse_vector": [], "title": "A strong composition theorem for junta complexity and the boosting of property testers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove a strong composition theorem for junta complexity and show how such theorems can be used to generically boost the performance of property testers.The $\\varepsilon$-approximate junta complexity of a function f is the smallest integer r such that f is $\\varepsilon$-close to a function that depends only on r variables. A strong composition theorem states that if f has large $\\varepsilon$-approximate junta complexity, then $g \\circ f$ has even larger $\\varepsilon^{\\prime}$-approximate junta complexity, even for $\\varepsilon^{\\prime} \\gg \\varepsilon$. We develop a fairly complete understanding of this behavior, proving that the junta complexity of $g \\circ f$ is characterized by that of f along with the multivariate noise sensitivity of g. For the important case of symmetric functions g, we relate their multivariate noise sensitivity to the simpler and well-studied case of univariate noise sensitivity.We then show how strong composition theorems yield boosting algorithms for property testers: with a strong composition theorem for any class of functions, a large-distance tester for that class is immediately upgraded into one for small distances. Combining our contributions yields a booster for junta testers, and with it new implications for junta testing. This is the first boosting-type result in property testing, and we hope that the connection to composition theorems adds compelling motivation to the study of both topics.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00107"}, {"primary_key": "1139540", "vector": [], "sparse_vector": [], "title": "Sampling from the Potts model at low temperatures via Swendsen-Wang dynamics.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Sampling from the q-state ferromagnetic Potts model is a fundamental question in statistical physics, probability theory, and theoretical computer science. On general graphs, this problem is computationally hard, and this hardness holds at arbitrarily low temperatures. At the same time, in recent years, there has been significant progress showing the existence of low-temperature sampling algorithms in various specific families of graphs. Our aim in this paper is to understand the minimal structural properties of general graphs that enable polynomial-time sampling from the q-state ferromagnetic Potts model at low temperatures. We study this problem from the perspective of the widely-used Swendsen-Wang dynamics and the closely related random-cluster dynamics. These are non-local Markov chains that have long been believed to converge rapidly to equilibrium at low temperatures in many graphs. However, the hardness of the sampling problem likely indicates that this is not even the case for all bounded degree graphs. Our results demonstrate that a key graph property behind fast or slow convergence time for these dynamics is whether the independent edge-percolation on the graph admits a strongly supercritical phase. By this, we mean that at large $p\\lt 1$, it has a large linear-sized component, and the graph complement of that component is comprised of only small components Specifically, we prove that such a condition implies fast mixing of the Swendsen-Wang and random-cluster dynamics on two general families of bounded-degree graphs: (a) graphs of at most stretched-exponential volume growth and (b) locally treelike graphs. In the other direction, we show that, even among graphs in those families, these Markov chains can converge exponentially slowly at arbitrarily low temperatures if the edge-percolation condition does not hold. In the process, we develop new tools for the analysis of non-local Markov chains, including a framework to bound the speed of disagreement propagation in the presence of long-range correlations, an understanding of spatial mixing properties on trees with random boundary conditions, and an analysis of burn-in phases at low temperatures.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00122"}, {"primary_key": "1139541", "vector": [], "sparse_vector": [], "title": "Folklore Sampling is Optimal for Exact Hopsets: Confirming the √n Barrier.", "authors": ["<PERSON>", "<PERSON>"], "summary": "For a graph G, a D-diameter-reducing exact hopset is a small set of additional edges H that, when added to G, maintains its graph metric but guarantees that all node pairs have a shortest path in $G \\cup H$ using at most D edges. A shortcut set is the analogous concept for reachability rather than distances. These objects have been studied since the early '90s, due to applications in parallel, distributed, dynamic, and streaming graph algorithms.For most of their history, the state-of-the-art construction for either object was a simple folklore algorithm, based on randomly sampling nodes to hit long paths in the graph. However, recent breakthroughs of <PERSON><PERSON> and Parter [SODA '22] and <PERSON> and <PERSON> [SODA '23] have finally improved over the folklore algorithm for shortcut sets and for $(1+\\varepsilon)$-approximate hopsets. For either object, it is now known that one can use $O(n)$ hop-edges to reduce diameter to $\\widetilde{O}(n^{1 / 3})$, improving over the folklore diameter bound of $\\widetilde{O}(n^{1 / 2})$. The only setting in which folklore sampling remains unimproved is for exact hopsets. Can these improvements be continued?We settle this question negatively by constructing graphs on which any exact hopset of $O(n)$ edges has diameter $\\widetilde{\\Omega}(n^{1 / 2})$. This improves on the previous lower bound of $\\Omega(n^{1 / 3})$ by <PERSON><PERSON> and <PERSON><PERSON> [<PERSON>OC<PERSON> '22]. Using similar ideas, we also polynomially improve the current lower bounds for shortcut sets, constructing graphs on which any shortcut set of $O(n)$ edges reduces diameter to $\\widetilde{\\Omega}(n^{1 / 4})$. This improves on the previous lower bound of $\\Omega(n^{1 / 6})$ by Huang and Pettie [SIAM J. Disc. Math. '18]. We also extend our constructions to provide lower bounds against $O(p)$-size exact hopsets and shortcut sets for other values of p; in particular, we show that folklore sampling is near-optimal for exact hopsets in the entire range of parameters $p \\in[1, n^{2}]$.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00046"}, {"primary_key": "1139542", "vector": [], "sparse_vector": [], "title": "Bridge Girth: A Unifying Notion in Network Design.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A classic 1993 paper by <PERSON><PERSON><PERSON><PERSON> et al. proved a tight reduction from spanners, emulators, and distance oracles to the extremal function $\\gamma$ of high-girth graphs. This paper initiated a large body of work in network design, in which problems are attacked by reduction to $\\gamma$ or the analogous extremal function for other girth concepts. In this paper, we introduce and study a new girth concept that we call the bridge girth of path systems, and we show that it can be used to significantly expand and improve this web of connections between girth problems and network design. We prove two kinds of results:•We write the maximum possible size of an n-node, p-path system with bridge girth $\\gt k$ as $\\beta(n, p, k)$, and we write a certain variant for \"ordered\" path systems as $\\beta^{*}(n, p, k)$. We identify several arguments in the literature that implicitly show upper or lower bounds on $\\beta, \\beta^{*}$, and we provide some polynomial improvements to these bounds. In particular, we construct a tight lower bound for $\\beta(n, p, 2)$, and we polynomially improve the upper bounds for $\\beta(n, p, 4)$ and $\\beta^{*}(n, p, \\infty)$.•We show that many state-of-the-art results in network design can be recovered or improved via black-box reductions to $\\beta$ or $\\beta^{*}$. Examples include bounds for distance/reachability preservers, exact hopsets, shortcut sets, the flow-cut gaps for directed multicut and sparsest cut, an integrality gap for directed Steiner forest.We believe that the concept of bridge girth can lead to a stronger and more organized map of the research area. Towards this, we leave many open problems related to both bridge girth reductions and extremal bounds on the size of path systems with high bridge girth.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00043"}, {"primary_key": "1139543", "vector": [], "sparse_vector": [], "title": "The Vector Balancing Constant for Zonotopes.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The vector balancing constant $\\operatorname{vb}(K, Q)$ of two symmetric convex bodies $K, Q$ is the minimum $r \\geq 0$ so that any number of vectors from K can be balanced into an r scaling of Q. A question raised by <PERSON><PERSON><PERSON><PERSON> is whether for any zonotope $K \\subseteq \\mathbb{R}^{d}$ one has $\\operatorname{vb}(K, K) \\lesssim \\sqrt{d}$. Intuitively, this asks whether a natural geometric generalization of <PERSON>'s Theorem (for which $K=B_{\\infty}^{d}$) holds. We prove that for any zonotope $K \\subseteq \\mathbb{R}^{d}$ one has $\\operatorname{vb}(K, K) \\lesssim \\sqrt{d} \\log \\log \\log d$. Our main technical contribution is a tight lower bound on the Gaussian measure of any section of a normalized zonotope, generalizing <PERSON><PERSON><PERSON>'s Theorem for cubes. We also prove that for two different normalized zonotopes K and Q one has $\\operatorname{vb}(K, Q) \\lesssim \\sqrt{d \\log d}$. All the bounds are constructive and the corresponding colorings can be computed in polynomial time.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00077"}, {"primary_key": "1139544", "vector": [], "sparse_vector": [], "title": "Separating MAX 2-AND, MAX DI-CUT and MAX CUT.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Assuming the Unique Games Conjecture (UGC), the best approximation ratio that can be obtained in polynomial time for the MAX CUT problem is $\\alpha_{\\text {CUT}} \\simeq 0.87856$, obtained by the celebrated SDP-based approximation algorithm of <PERSON><PERSON><PERSON> and <PERSON>. Currently, the best approximation algorithm for MAX DI-CUT, i.e., the MAX CUT problem in directed graphs, achieves a ratio of about 0.87401, leaving open the question whether MAX DI-CUT can be approximated as well as MAX CUT. We obtain a slightly improved algorithm for MAX DI-CUT and a new UG-Chardness result for it, showing that $0.87446 \\leq \\alpha_{\\text {DI-CUT}} \\leq 0.87461$, where $\\alpha_{\\text {DI-CUT}}$ is the best approximation ratio that can be obtained in polynomial time for MAX DI-CUT under UGC. The new upper bound separates MAX DI-CUT from MAX CUT, i.e., shows that MAX DI-CUT cannot be approximated as well as MAX CUT, resolving a question raised by <PERSON><PERSON> and <PERSON><PERSON><PERSON>. A natural generalization of MAX DI-CUT is the MAX 2-AND problem in which each constraint is of the form $z_{1} \\wedge {z_{2}}$, where $z_{1}$ and ${z_{2}}$ are literals, i.e., variables or their negations. (In MAX DI-CUT each constraint is of the form $\\bar{x}_{1} \\wedge {x_{2}}$, where $x_{1}$ and ${x_{2}}$ are variables.) Austrin separated MAX 2-AND from MAX CUT by showing that $\\alpha_{2 \\mathrm{AND}} \\leq 0.87435$ and conjectured that MAX 2-AND and MAX DI-CUT have the same approximation ratio. Our new lower bound on MAX DI-CUT refutes this conjecture, completing the separation of the three problems MAX 2-AND, MAX DI-CUT and MAX CUT. We also obtain a new lower bound for MAX 2-AND showing that $0.87414 \\leq \\alpha_{2 \\text {AND}} \\leq 0.87435$. Our upper bound on MAXDI-CUT is achieved via a simple analytical proof. The new lower bounds on MAX DI-CUT and MAX 2-AND, i.e., the new approximation algorithms, use experimentally-discovered distributions of rounding functions which are then verified via computer-assisted proofs. 1 1 Code for the project: https://github.com/jbrakensiek/max-dicut", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00023"}, {"primary_key": "1139545", "vector": [], "sparse_vector": [], "title": "A Deterministic Almost-Linear Time Algorithm for Minimum-Cost Flow.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We give a deterministic $m^{1+o(1)}$ time algorithm that computes exact maximum flows and minimum-cost flows on directed graphs with m edges and polynomially bounded integral demands, costs, and capacities. As a consequence, we obtain the first running time improvement for deterministic algorithms that compute maximum-flow in graphs with polynomial bounded capacities since the work of <PERSON><PERSON><PERSON> [<PERSON><PERSON> '98].Our algorithm builds on the framework of Chen-<PERSON> [FOCS '22] that computes an optimal flow by computing a sequence of $m^{1+o(1)}$-approximate undirected minimum-ratio cycles. We develop a deterministic dynamic graph data-structure to compute such a sequence of minimum-ratio cycles in an amortized $m^{o(1)}$ time per edge update. Our key technical contributions are deterministic analogues of the vertex sparsification and edge sparsification components of the data-structure from <PERSON> et al. For the vertex sparsification component, we give a method to avoid the randomness in <PERSON> et al. which involved sampling random trees to recurse on. For the edge sparsification component, we design a deterministic algorithm that maintains an embedding of a dynamic graph into a sparse spanner. We also show how our dynamic spanner can be applied to give a deterministic data structure that maintains a fully dynamic low-stretch spanning tree on graphs with polynomially bounded edge lengths, with subpolynomial average stretch and subpolynomial amortized time per edge update.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00037"}, {"primary_key": "1139546", "vector": [], "sparse_vector": [], "title": "Deterministic Fully Dynamic SSSP and More.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present the first non-trivial fully dynamic algorithm maintaining exact single-source distances in unweighted graphs. This resolves an open problem stated by <PERSON><PERSON> [COCOON 2005] and <PERSON> and <PERSON><PERSON> [FOCS 2019]. Previous fully dynamic single-source distances data structures were all approximate, but so far, non-trivial dynamic algorithms for the exact setting could only be ruled out for polynomially weighted graphs (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, [FOCS 2014]). The exact unweighted case remained the main case for which neither a subquadratic dynamic algorithm nor a quadratic lower bound was known.Our dynamic algorithm works on directed graphs and is deterministic, and can report a single-source shortest paths tree in subquadratic time as well. Thus we also obtain the first deterministic fully dynamic data structure for reachability (transitive closure) with subquadratic update and query time. This answers an open problem of <PERSON>, Nan<PERSON><PERSON>, and Sara<PERSON>rak [FOCS 2019]. Finally, using the same framework we obtain the first fully dynamic data structure maintaining all-pairs $(1+\\epsilon)$-approximate distances within non-trivial sub-$n^{\\omega}$ worst-case update time while supporting optimal-time approximate shortest path reporting at the same time. This data structure is also deterministic and therefore implies the first known non-trivial deterministic worst-case bound for recomputing the transitive closure of a digraph.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00142"}, {"primary_key": "1139547", "vector": [], "sparse_vector": [], "title": "Faster High Accuracy Multi-Commodity Flow from Single-Commodity Techniques.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Since the development of efficient linear program solvers in the 80s, all major improvements for solving multi-commodity flows to high accuracy came from improvements to general linear program solvers. This differs from the single commodity problem (e.g. maximum flow) where all recent improvements also rely on graph specific techniques such as graph decompositions or the Laplacian paradigm. This phenomenon sparked research to understand why these graph techniques are unlikely to help for multi-commodity flow. [<PERSON><PERSON><PERSON> and <PERSON>'17] reduced solving multi-commodity Laplacians to general linear systems and [<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> ICALP'22] showed that general linear programs can be reduced to 2-commodity flow. However, the reductions create sparse graph instances, so improvement to multi-commodity flows on denser graphs might exist. We show that one can indeed speed up multi-commodity flow algorithms on non-sparse graphs using graph techniques from single-commodity flow algorithms. This is the first improvement to high accuracy multi-commodity flow algorithms that does not just stem from improvements to general linear program solvers. In particular, using graph data structures from recent min-cost flow algorithm by [<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and Wang <PERSON>'21] based on the celebrated expander decomposition framework, we show that 2-commodity flow on an n-vertex m-edge graph can be solved deterministically in $\\widetilde{O}\\left(\\sqrt{m} n^{\\omega-1 / 2}\\right)$ time for current bounds on fast matrix multiplication $\\omega \\approx 2.372$, improving upon the previous fastest algorithms with $\\widetilde{O}\\left(m^{\\omega}\\right)$ [<PERSON>, <PERSON>, and <PERSON> STOC'19] and $\\widetilde{O}\\left(\\sqrt{m} n^{2}\\right)$ [Kapoor and Vaidya;96] time complexity. For general k commodities, our algorithm runs in $\\widetilde{O}\\left(k^{2.5} \\sqrt{m} n^{\\omega-1 / 2}\\right)$ time.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00036"}, {"primary_key": "1139548", "vector": [], "sparse_vector": [], "title": "Parallel Repetition for the GHZ Game: Exponential Decay.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that the value of the n-fold repeated GHZ game is at most $2^{-\\Omega(n)}$, improving upon the polynomial bound established by <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Our result is established via a reduction to approximate subgroup type questions from additive combinatorics.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00080"}, {"primary_key": "1139549", "vector": [], "sparse_vector": [], "title": "Algorithmic Decorrelation and Planted Clique in Dependent Random Graphs: The Case of Extra Triangles.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We aim to understand the extent to which the noise distribution in a planted signal-plus-noise problem impacts its computational complexity. To that end, we consider the planted clique and planted dense subgraph problems, but in a different ambient graph. Instead of Erdős-Rényi $G(n, p)$, which has independent edges, we take the ambient graph to be the random graph with triangles (RGT) obtained by adding triangles to $G(n, p)$. We show that the RGT can be efficiently mapped to the corresponding $G(n, p)$, and moreover, that the planted clique (or dense subgraph) is approximately preserved under this mapping. This constitutes the first average-case reduction transforming dependent noise to independent noise. Together with the easier direction of mapping the ambient graph from Erdős-Rényi to RGT, our results yield a strong equivalence between models. In order to prove our results, we develop a new general framework for reasoning about the validity of average-case reductions based on low sensitivity to perturbations.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00132"}, {"primary_key": "1139550", "vector": [], "sparse_vector": [], "title": "Negative-Weight Single-Source Shortest Paths in Near-Linear Time: Now Faster!", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this work we revisit the fundamental Single-Source Shortest Paths (SSSP) problem with possibly negative edge weights. A recent breakthrough result by <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> established a near-linear $O\\left(m \\log ^{8}(n) \\log (W)\\right)$-time algorithm for negative-weight SSSP, where W is an upper bound on the magnitude of the smallest negative-weight edge. In this work we improve the running time to $O\\left(m \\log ^{2}(n) \\log (n W) \\log \\log n\\right)$, which is an improvement by nearly six log-factors. Some of these log-factors are easy to shave (e.g. replacing the priority queue used in <PERSON><PERSON><PERSON>'s algorithm), while others are significantly more involved (e.g. to find negative cycles we design an algorithm reminiscent of noisy binary search and analyze it with drift analysis). As side results, we obtain an algorithm to compute the minimum cycle mean in the same running time as well as a new construction for computing Low-Diameter Decompositions in directed graphs.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00038"}, {"primary_key": "1139551", "vector": [], "sparse_vector": [], "title": "Constant-Factor Approximation Algorithms for Convex Cover and Hidden Set in a Simple Polygon.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Given a simple polygon P, the minimum convex cover problem seeks to cover P with the fewest convex polygons that lie within P. The maximum hidden set problem seeks to place within P a maximum cardinality set of points no two of which see each other. We give constant factor approximation algorithms for both problems. Previously, the best approximation factor for the minimum convex cover was logarithmic; for the maximum hidden set problem, no approximation algorithm was known.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00083"}, {"primary_key": "1139552", "vector": [], "sparse_vector": [], "title": "One Tree to Rule Them All: Poly-Logarithmic Universal Steiner Tree.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A spanning tree T of graph G is a $\\rho$-approximate universal Steiner tree (UST) for root vertex r if, for any subset of vertices S containing r, the cost of the minimal subgraph of T connecting S is within a $\\rho$ factor of the minimum cost tree connecting S in <PERSON><PERSON> et al. (FOCS 2012) showed that every graph admits $2^{O(\\sqrt{\\log n})}$-approximate USTs by showing that USTs are equivalent to strong sparse partition hierarchies (up to poly-logs). Further, they posed poly-logarithmic USTs and strong sparse partition hierarchies as open questions.We settle these open questions by giving polynomial-time algorithms for computing both $O\\left(\\log ^{7} n\\right)$-approximate USTs and poly-logarithmic strong sparse partition hierarchies. We reduce the existence of these objects to the previously studied cluster aggregation problem and a class of well-separated point sets which we call dangling nets. For graphs with constant doubling dimension or constant pathwidth we obtain improved bounds by deriving $O(\\log n)$-approximate USTs and $O(1)$ strong sparse partition hierarchies. Our doubling dimension result is tight up to second order terms.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00012"}, {"primary_key": "1139553", "vector": [], "sparse_vector": [], "title": "The Full Landscape of Robust Mean Testing: Sharp Separations between Oblivious and Adaptive Contamination.", "authors": ["Clément L. <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the question of Gaussian mean testing, a fundamental task in high-dimensional distribution testing and signal processing, subject to adversarial corruptions of the samples. We focus on the relative power of different adversaries, and show that, in contrast to the common wisdom in robust statistics, there exists a strict separation between adaptive adversaries (strong contamination) and oblivious ones (weak contamination) for this task. Specifically, we resolve both the information-theoretic and computational landscapes for robust mean testing. In the exponential-time setting, we establish the tight sample complexity of testing $\\mathcal{N}(0, I)$ against $\\mathcal{N}(\\alpha v, I)$, where $\\|v\\|_{2}=1$, with an $\\varepsilon$-fraction of adversarial corruptions, to be $\\tilde{\\Theta}\\left(\\max \\left(\\frac{\\sqrt{d}}{\\alpha^{2}}, \\frac{d \\varepsilon^{3}}{\\alpha^{4}}, \\min \\left(\\frac{d^{2 / 3} \\varepsilon^{2 / 3}}{\\alpha^{8 / 3}}, \\frac{d \\varepsilon}{\\alpha^{2}}\\right)\\right)\\right)$ while the complexity against adaptive adversaries is $\\tilde{\\Theta}\\left(\\max \\left(\\frac{\\sqrt{d}}{\\alpha^{2}}, \\frac{d \\varepsilon^{2}}{\\alpha^{4}}\\right)\\right)$ which is strictly worse for a large range of vanishing $\\varepsilon, \\alpha$. To the best of our knowledge, ours is the first separation in sample complexity between the strong and weak contamination models. In the polynomial-time setting, we close a gap in the literature by providing a polynomial-time algorithm against adaptive adversaries achieving the above sample complexity $\\tilde{\\Theta}\\left(\\max \\left(\\sqrt{d} / \\alpha^{2}, d \\varepsilon^{2} / \\alpha^{4}\\right)\\right)$, and a low-degree lower bound (which complements an existing reduction from planted clique) suggesting that all efficient algorithms require this many samples, even in the oblivious-adversary setting.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00133"}, {"primary_key": "1139554", "vector": [], "sparse_vector": [], "title": "Canonical decompositions of 3-connected graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We offer a new structural basis for the theory of 3-connected graphs, providing a unique decomposition of every such graph into parts that are either quasi 4-connected, wheels, or obtained from a biclique by turning one side into a triangle. Our construction is explicit, canonical, and has the following applications: we obtain a new theorem characterising all <PERSON><PERSON><PERSON> graphs as either essentially 4-connected, cycles, or complete graphs on at most four vertices, and we provide an automatic proof of <PERSON><PERSON>'s wheel theorem.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00115"}, {"primary_key": "1139555", "vector": [], "sparse_vector": [], "title": "ReSQueing Parallel and Private Stochastic Convex Optimization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We introduce a new tool for stochastic convex optimization (SCO): a Reweighted Stochastic Query (ReSQue) estimator for the gradient of a function convolved with a (Gaussian) probability density. Combining ReSQue with recent advances in ball oracle acceleration [CJJ+20], [ACJ+21], we develop algorithms achieving state-of-the-art complexities for SCO in parallel and private settings. For a SCO objective constrained to the unit ball in $\\mathbb{R}^{d}$, we obtain the following results (up to polylogarithmic factors).1)We give a parallel algorithm obtaining optimization error $\\epsilon_{\\text {opt} }$ with $d^{1 / 3} \\epsilon_{\\text {opt} }^{-2 / 3}$ gradient oracle query depth and $d^{1 / 3} \\epsilon_{\\text {opt} }^{-2 / 3}+\\epsilon_{\\text {opt} }^{-2}$ gradient queries in total, assuming access to a bounded-variance stochastic gradient estimator. For $\\epsilon_{\\text {opt} } \\in\\left[d^{-1}, d^{-1 / 4}\\right]$, our algorithm matches the state-of-the-art oracle depth of [BJL+19] while maintaining the optimal total work of stochastic gradient descent.2)Given n samples of Lipschitz loss functions, prior works [BFTT19], [BFGT20], [AFKT21], [KLL21] established that if $n \\gt rsim d \\epsilon_{\\mathrm{dp}}^{-2},\\left(\\epsilon_{\\mathrm{dp}}, \\delta\\right)$-differential privacy is attained at no asymptotic cost to the SCO utility. However, these prior works all required a superlinear number of gradient queries. We close this gap for sufficiently large $n \\gt rsim d^{2} \\epsilon_{{\\bf d p}}^{-3}$, by using ReSQue to design an algorithm with near-linear gradient query complexity in this regime.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00124"}, {"primary_key": "1139556", "vector": [], "sparse_vector": [], "title": "Optimal Algorithms for Bounded Weighted Edit Distance.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The edit distance (also known as Levenshtein distance) of two strings is the minimum number of insertions, deletions, and substitutions of characters needed to transform one string into the other. The textbook dynamic-programming algorithm computes the edit distance of two length- n strings in $\\mathcal{O}\\left(n^{2}\\right)$ time, which is optimal up to subpolynomial factors assuming the Strong Exponential Time Hypothesis (SETH). An established way of circumventing this hardness is to consider the bounded setting, where the running time is parameterized by the edit distance k. A celebrated algorithm by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (JCSS'88) achieves a running time of $\\mathcal{O}\\left(n+k^{2}\\right)$, which is optimal as a function of n and k (again, up to subpolynmial factors and assuming SETH).While the theory community thoroughly studied the Levenshtein distance, most practical applications rely on a more general weighted edit distance, where each edit has a weight depending on its type and the involved characters from the alphabet $\\Sigma$. This is formalized through a weight function $w: \\Sigma \\cup\\{\\varepsilon\\} \\times \\Sigma \\cup\\{\\varepsilon\\} \\rightarrow \\mathbb{R}$ normalized so that $w(a \\mapsto a)=0$ for $a \\in \\Sigma \\cup\\{\\varepsilon\\}$ and $w(a \\mapsto b) \\geq 1$ for $a, b \\in \\Sigma \\cup\\{\\varepsilon\\}$ with $a \\neq b$; the goal is to find an alignment of the two strings minimizing the total weight of edits. The classic $\\mathcal{O}\\left(n^{2}\\right)$-time algorithm supports this setting seamlessly, but for many decades just a straightforward $\\mathcal{O}(n k)$-time solution was known for the bounded version of the weighted edit distance problem. Only very recently, Das, Gilbert, Hajiaghayi, Kociumaka, and Saha (STOC'23) gave the first non-trivial algorithm, achieving a time complexity of $\\mathcal{O}\\left(n+k^{5}\\right)$. While this running time is linear for $k \\leq n^{1 / 5}$, it is still very far from $\\mathcal{O}\\left(n+k^{2}\\right)$-the bound achievable in the unweighted setting. This is unsatisfactory, especially given the lack of any compelling evidence that the weighted version is inherently harder.In this paper, we essentially close this gap by showing both an improved $\\widetilde{\\mathcal{O}}\\left(n+\\sqrt{n k^{3}}\\right)$-time algorithm and, more surprisingly, a matching lower bound: Conditioned on the All-Pairs Shortest Paths (APSP) hypothesis, the running time of our solution is optimal for $\\sqrt{n} \\leq k \\leq n$ (up to subpolynomial factors). In particular, this is the first separation between the complexity of the weighted and unweighted edit distance problems.Just like the Landau-Vishkin algorithm, our algorithm can be adapted to a wide variety of settings, such as when the input is given in a compressed representation. This is because, independently of the string length n, our procedure takes $\\widetilde{\\mathcal{O}}\\left(k^{3}\\right)$ time assuming that the equality of any two substrings can be tested in $\\widetilde{\\mathcal{O}}(1)$ time.Consistently with the previous work, our algorithm relies on the observation that strings with a rich structure of low-weight alignments must contain highly repetitive substrings. Nevertheless, achieving the optimal running time requires multiple new insights. We capture the right notion of repetitiveness using a tailor-made compressibility measure that we call self-edit distance. Our divide-and-conquer algorithm reduces the computation of weighted edit distance to several subproblems involving substrings of small self-edit distance and, at the same time, distributes the budget for edit weights among these subproblems. We then exploit the repetitive structure of the underlying substrings using state-of-the-art results for multiple-source shortest paths in planar graphs (Klein, SODA'05).As a stepping stone for our conditional lower bound, we study a dynamic problem of maintaining two strings subject to updates (substitutions of characters) and weighted edit distance queries. We significantly extend the construction of Abboud and Dahlgaard (FOCS'16), originally for dynamic shortest paths in planar graphs, to show that a sequence of n updates and $q \\leq n$ queries cannot be handled much faster than in $\\mathcal{O}\\left(n^{2} \\sqrt{q}\\right)$ time. We then compose the snapshots of the dynamic strings to derive hardness of the static problem in the bounded setting.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00135"}, {"primary_key": "1139557", "vector": [], "sparse_vector": [], "title": "Faster Algorithms for Text-to-Pattern Hamming Distances.", "authors": ["<PERSON>", "<PERSON>", "Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON> Xu"], "summary": "We study the classic Text-to-Pattern Hamming Distances problem: given a pattern P of length m and a text T of length n, both over a polynomial-size alphabet, compute the Hamming distance between P and $T[i \\ldots i+m-1]$ for every shift i, under the standard Word-RAM model with $\\Theta(\\log n)$-bit words.•We provide an $O(n \\sqrt{m})$ time Las Vegas randomized algorithm for this problem, beating the decades-old $O(n \\sqrt{m \\log m})$ running time [<PERSON><PERSON>, SICOMP 1987]. We also obtain a deterministic algorithm, with a slightly higher $O\\left(n \\sqrt{m}(\\log m \\log \\log m)^{1 / 4}\\right)$ running time. Our randomized algorithm extends to the k-bounded setting, with running time $O\\left(n+\\frac{n k}{\\sqrt{m}}\\right)$, removing all the extra logarithmic factors from earlier algorithms [<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, ICALP 2018; Chan, Golan, Kociuma<PERSON>, Kopelowitz and Porat, STOC 2020].•For the $(1+\\varepsilon)$-approximate version of Text-to-Pattern Hamming Distances, we give an $\\widetilde{O}\\left(\\varepsilon^{-0.93} n\\right)$ time Monte Carlo randomized algorithm (where $\\widetilde{O}$ hides poly-logarithmic factors), beating the previous $\\widetilde{O}\\left(\\varepsilon^{-1} n\\right)$ running time [Kopelowitz and Porat, FOCS 2015; Kopelowitz and Porat, SOSA 2018].Our approximation algorithm exploits a connection with 3SUM, and uses a combination of Fredman's trick, equality matrix product, and random sampling; in particular, we obtain new results on approximate counting versions of 3 SUM and Exact Triangle, which may be of independent interest. Our exact algorithms use a novel combination of hashing, bit-packed FFT, and recursion; in particular, we obtain a faster algorithm for computing the sumset of two integer sets, in the regime when the universe size is close to quadratic in the number of elements. We also prove a fine-grained equivalence between the exact Text-to-Pattern Hamming Distances problem and a range-restricted, counting version of 3 SUM.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00136"}, {"primary_key": "1139558", "vector": [], "sparse_vector": [], "title": "Covering Planar Metrics (and Beyond): O(1) Trees Suffice.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While research on the geometry of planar graphs has been active in the past decades, many properties of planar metrics remain mysterious. This paper studies a fundamental aspect of the planar graph geometry: covering planar metrics by a small collection of simpler metrics. Specifically, a tree cover of a metric space $(X, \\delta)$ is a collection of trees, so that every pair of points u and v in X has a low-distortion path in at least one of the trees.The celebrated \"Dumbbell Theorem\" [ADM + 95] states that any low-dimensional Euclidean space admits a tree cover with $O(1)$ trees and distortion $1+\\varepsilon$, for any fixed $\\varepsilon \\in(0,1)$. This result has found numerous algorithmic applications, and has been generalized to the wider family of doubling metrics [BFN19]. Does the same result hold for planar metrics? A positive answer would add another evidence to the well-observed connection between Euclidean/doubling metrics and planar metrics.In this work, we answer this fundamental question affirmatively. Specifically, we show that for any given fixed $\\varepsilon \\in(0,1)$, any planar metric can be covered by $O(1)$ trees with distortion $1+\\varepsilon$. Our result for planar metrics follows from a rather general framework: First we reduce the problem to constructing tree covers with additive distortion. Then we introduce the notion of shortcut partition, and draw connection between shortcut partition and additive tree cover. Finally we prove the existence of shortcut partition for any planar metric, using new insights regarding the grid-like structure of planar graphs. To demonstrate the power of our framework:•We establish additional tree cover results beyond planar metrics; in particular, we present an $O(1)$-size tree cover with distortion $1+\\varepsilon$ for bounded treewidth metrics;•We obtain several algorithmic applications in planar graphs from our tree cover. The grid-like structure is a technical contribution that we believe is of independent interest. We showcase its applicability beyond tree cover by constructing a simpler and better embedding of planar graphs into $O(1)$-treewidth graphs with small additive distortion, resolving an open problem in this line of research.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00139"}, {"primary_key": "1139559", "vector": [], "sparse_vector": [], "title": "Triplet Reconstruction and all other Phylogenetic CSPs are Approximation Resistant.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the natural problem of Triplet Reconstruction (also known as Rooted Triplets Consistency or Triplet Clustering), originally motivated by applications in computational biology and relational databases (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, 1981) [2]: given n datapoints, we want to embed them onto the n leaves of a rooted binary tree (also known as a hierarchical clustering, or ultrametric embedding) such that a given set of m triplet constraints is satisfied. A triplet constraint $i j \\mid k$ for points $i, j, k$ indicates that \"$i, j$ are more closely related to each other than to k,\" (in terms of distances $d(i, j) \\leq d(i, k)$ and $d(i, j) \\leq d(j, k)$) and we say that a tree satisfies the triplet $i j \\mid k$ if the distance in the tree between $i, j$ is smaller than the distance between $i, k$ (or $j, k$). Among all possible trees with n leaves, can we efficiently find one that satisfies a large fraction of the m given triplets?<PERSON><PERSON> et al. (1981) [2] studied the decision version and gave an elegant polynomial-time algorithm that determines whether or not there exists a tree that satisfies all of the m constraints. Moreover, it is straightforward to see that a random binary tree achieves a constant $\\frac{1}{3}$-approximation, since there are only 3 distinct triplets $i j|k, i k| j, j k \\mid i$ (each will be satisfied w.p. $\\frac{1}{3}$). Unfortunately, despite more than four decades of research by various communities, there is no better approximation algorithm for this basic Triplet Reconstruction problem.Our main theorem-which captures Triplet Reconstruction as a special case-is a general hardness of approximation result about Constraint Satisfaction Problems (CSPs) over infinite domains (CSPs where instead of boolean values $\\{0,1\\}$ or a fixed-size domain, the variables can be mapped to any of the n leaves of a tree). Specifically, we prove that assuming the Unique Games Conjecture [57], Triplet Reconstruction and more generally, every Constraint Satisfaction Problem (CSP) over hierarchies is approximation resistant, i.e., there is no polynomial-time algorithm that does asymptotically better than a biased random assignment.Our result settles the approximability not only for Triplet Reconstruction, but for many interesting problems that have been studied by various scientific communities such as the popular Quartet Reconstruction and Subtree/Supertree Aggregation Problems. More broadly, our result significantly extends the list of approximation resistant predicates by pointing to a large new family of hard problems over hierarchies. Our main theorem is a generalization of Guruswami, Håstad, Manokaran, Raghavendra, and Charikar (2011) [36], who showed that ordering CSPs (CSPs over permutations of n elements, e.g., Max Acyclic Subgraph, Betweenness, Non-Betweenness) are approximation resistant. The main challenge in our analyses stems from the fact that trees have topology (in contrast to permutations and ordering CSPs) and it is the tree topology that determines whether a given constraint on the variables is satisfied or not. As a byproduct, we also present some of the first CSPs where their approximation resistance is proved against biased random assignments, instead of uniformly random assignments.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00024"}, {"primary_key": "1139560", "vector": [], "sparse_vector": [], "title": "Uniqueness and Rapid Mixing in the Bipartite Hardcore Model (extended abstract).", "authors": ["<PERSON><PERSON>", "Jingcheng Liu", "<PERSON><PERSON><PERSON>"], "summary": "We characterize the uniqueness condition in the hardcore model for bipartite graphs with degree bounds only on one side, and provide a nearly linear time sampling algorithm that works up to the uniqueness threshold. We show that the uniqueness threshold for bipartite graph has almost the same form of the tree uniqueness threshold for general graphs, except with degree bounds only on one side of the bipartition. The hardcore model is originated in statistical physics for modeling equilibrium of lattice gas. Combinatorially, it can also be seen as a weighted enumeration of independent sets. Counting the number of independent sets in a bipartite graph (#BIS) is a central open problem in approximate counting. Compared to the same problem in a general graph, surprising tractable regime have been identified that are believed to be hard in general. This is made possible by two lines of algorithmic approach: the high-temperature algorithms starting from <PERSON> and <PERSON> (STOC 2015), and the low-temperature algorithms starting from <PERSON><PERSON>, <PERSON>, and <PERSON> (STOC 2019).In this work, we study the limit of these algorithms in the high-temperature case. Our characterization of the uniqueness condition is obtained by proving decay of correlations for arguably the best possible regime, which involves locating fixpoints of multivariate iterative rational maps and showing their contraction. Interestingly, we are able to show that a regime that was considered \"low-temperature\" is actually well within the uniqueness (high-temperature) regime. We also give a nearly linear time sampling algorithm based on simulating field dynamics only on one side of the bipartite graph that works up to the uniqueness threshold. Our algorithm is very different from the original high-temperature algorithm of <PERSON> and <PERSON> (STOC 2015), and it makes use of a connection between correlation decay and spectral independence of <PERSON>ov chains. Along the way, we also build an explicit connection between the very recent developments of negative-fields stochastic localization schemes and field dynamics. Last but not the least, we are able to show that the standard Glauber dynamics on both side of the bipartite graph mixes in polynomial time up to the uniqueness. Remarkably, this is a model where both the total influence and the spectral radius of the adjacency matrix can be unbounded, yet we are able to prove mixing time bounds through the framework of spectral independence.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00121"}, {"primary_key": "1139561", "vector": [], "sparse_vector": [], "title": "When Does Adaptivity Help for Quantum State Learning?", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the classic question of state tomography: given copies of an unknown quantum state $\\rho \\in \\mathbb{C}^{d \\times d}$, output $\\widehat{\\rho}$ which is close to $\\rho$ in some sense, e.g. trace distance or fidelity. When one is allowed to make coherent measurements entangled across all copies, $\\Theta\\left(d^{2} / \\varepsilon^{2}\\right)$ copies are necessary and sufficient to get trace distance $\\varepsilon$ [18], [29]. Unfortunately, the protocols achieving this rate incur large quantum memory overheads that preclude implementation on near-term devices. On the other hand, the best known protocol using incoherent (single-copy) measurements uses $O\\left(d^{3} / \\varepsilon^{2}\\right)$ copies [24], and multiple papers have posed it as an open question to understand whether or not this rate is tight [6], [18]. In this work, we fully resolve this question, by showing that any protocol using incoherent measurements, even if they are chosen adaptively, requires $\\Omega\\left(d^{3} / \\varepsilon^{2}\\right)$ copies, matching the upper bound of [24]. We do so by a new proof technique which directly bounds the \"tilt\" of the posterior distribution after measurements, which yields a surprisingly short proof of our lower bound, and which we believe may be of independent interest. While this implies that adaptivity does not help for tomography with respect to trace distance, we show that it actually does help for tomography with respect to infidelity. We give an adaptive algorithm that outputs a state which is $\\gamma$-close in infidelity to $\\rho$ using only $\\widetilde{O}\\left(d^{3} / \\gamma\\right)$ copies, which is optimal for incoherent measurements. In contrast, it is known [18] that any nonadaptive algorithm requires $\\Omega\\left(d^{3} / \\gamma^{2}\\right)$ copies. While it is folklore that in 2 dimensions, one can achieve a scaling of $O(1 / \\gamma)$, to the best of our knowledge, our algorithm is the first to achieve the optimal rate in all dimensions.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00029"}, {"primary_key": "1139562", "vector": [], "sparse_vector": [], "title": "Strong Spatial Mixing for Colorings on Trees and its Algorithmic Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Strong spatial mixing (SSM) is an important quantitative notion of correlation decay for Gibbs distributions arising in statistical physics, probability theory, and theoretical computer science. A longstanding conjecture is that the uniform distribution on proper q-colorings on a $\\Delta$ regular tree exhibits SSM whenever $q \\geq \\Delta+1$. Moreover, it is widely believed that as long as SSM holds on bounded-degree trees with q colors, one would obtain an efficient sampler for q-colorings on all bounded-degree graphs via simple Markov chain algorithms. It is surprising that such a basic question is still open, even on trees, but then again it also highlights how much we still have to learn about random colorings. In this paper, we show the following: (1)For any $\\Delta \\geq 3$, SSM holds for random q-colorings on trees of maximum degree $\\Delta$ whenever $q \\geq \\Delta+3$. Thus we almost fully resolve the aforementioned conjecture. Our result substantially improves upon the previously best bound which requires $q \\geq 1.59 \\Delta+\\gamma^{*}$ for an absolute constant $\\gamma^{*}\\gt0$.(2)For any $\\Delta \\geq 3$ and $g=\\Omega_{\\Delta}(1)$, we establish optimal mixing of the Glauber dynamics for q-colorings on graphs of maximum degree $\\Delta$ and girth g whenever $q \\geq \\Delta+3$. Our approach is based on a new general reduction from spectral independence on large-girth graphs to SSM on trees that is of independent interest. Using the same techniques, we also prove near-optimal bounds on weak spatial mixing (WSM), a closely-related notion to SSM, for the antiferromagnetic Potts model on trees.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00053"}, {"primary_key": "1139563", "vector": [], "sparse_vector": [], "title": "Memory-Query Tradeoffs for Randomized Convex Optimization.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We show that any randomized first-order algorithm which minimizes a d-dimensional, 1-Lipschitz convex function over the unit ball must either use $\\Omega\\left(d^{2-\\delta}\\right)$ bits of memory or make $\\Omega\\left(d^{1+\\delta / 6-o(1)}\\right)$ queries, for any constant $\\delta \\in(0,1)$ and when the precision $\\epsilon$ is quasipolynomially small in d. Our result implies that cutting plane methods, which use $\\tilde{O}\\left(d^{2}\\right)$ bits of memory and $\\tilde{O}(d)$ queries, are Pareto-optimal among randomized first-order algorithms, and quadratic memory is required to achieve optimal query complexity for convex optimization.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00086"}, {"primary_key": "1139564", "vector": [], "sparse_vector": [], "title": "New Lower Bounds for Adaptive Tolerant Junta Testing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove a $k^{-\\Omega\\left(\\log \\left(\\varepsilon_{2}-\\varepsilon_{1}\\right)\\right)}$ lower bound for adap- tively testing whether a Boolean function is $\\varepsilon_{1}$-close to or $\\varepsilon_{2}-$ far from k-juntas. Our results provide the first superpolynomial separation between tolerant and non-tolerant testing for a natural property of boolean functions under the adaptive setting. Furthermore, our techniques generalize to show that adaptively testing whether a function is $\\varepsilon_{1}$-close to a k-junta or $\\varepsilon_{2}$-far from $(k+o(k))$-juntas cannot be done with poly $(k,\\left(\\varepsilon_{2}-\\varepsilon_{1}\\right)^{-1})$ queries. This is in contrast to an algorithm by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> [CCC 2021] which uses poly $(k,\\left(\\varepsilon_{2}-\\varepsilon_{1}\\right)^{-1})$ queries to test whether a function is $\\varepsilon_{1}$-close to a k-junta or $\\varepsilon_{2}$-far from $O(k /\\left(\\varepsilon_{2}-\\varepsilon_{1}\\right)^{2})$-juntas", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00108"}, {"primary_key": "1139565", "vector": [], "sparse_vector": [], "title": "Query lower bounds for log-concave sampling.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Log-concave sampling has witnessed remarkable algorithmic advances in recent years, but the corresponding problem of proving $\\lt$bold$\\gt$lower bounds$\\lt$/bold$\\gt$ for this task has remained elusive, with lower bounds previously known only in dimension one. In this work, we establish the following query lower bounds: (1) sampling from strongly log-concave and log-smooth distributions in dimension $d \\geq 2$ requires $\\Omega(\\log \\kappa)$ queries, which is sharp in any constant dimension, and (2) sampling from Gaussians in dimension d (hence also from general logconcave and log-smooth distributions in dimension d) requires $\\widetilde{\\Omega}(\\min (\\sqrt{\\kappa} \\log d, d))$ queries, which is nearly sharp for the class of Gaussians. Here $\\kappa$ denotes the condition number of the target distribution. Our proofs rely upon (1) a multiscale construction inspired by work on the <PERSON><PERSON><PERSON> conjecture in geometric measure theory, and (2) a novel reduction that demonstrates that block Krylov algorithms are optimal for this problem, as well as connections to lower bound techniques based on Wishart matrices developed in the matrix-vector query literature.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00131"}, {"primary_key": "1139566", "vector": [], "sparse_vector": [], "title": "ABE for Circuits with poly (λ) -sized Keys from LWE.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>e"], "summary": "We present a key-policy attribute-based encryption (ABE) scheme for circuits based on the Learning With Errors (LWE) assumption whose key size is independent of the circuit depth. Our result constitutes the first improvement for ABE for circuits from LWE in almost a decade, given by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> (STOC 2013) and <PERSON><PERSON>, et al. (EUROCRYPT 2014) – we reduce the key size in the latter from poly(depth $,\\lambda)$ to poly $(\\lambda)$. The starting point of our construction is a recent ABE scheme of Li, Lin, and Lu<PERSON> (TCC 2022), which achieves poly $(\\lambda)$ key size but requires pairings and generic bilinear groups in addition to LWE; we introduce new lattice techniques to eliminate the additional requirements.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00032"}, {"primary_key": "1139567", "vector": [], "sparse_vector": [], "title": "Handling Correlated Rounding Error via Preclustering: A 1.73-approximation for Correlation Clustering.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider the classic correlation clustering problem: Given a complete graph where edges are labelled either + or −, the goal is to find a partition of the vertices that minimizes the sum of the +edges across parts plus the sum of the −edges within parts. Recently, <PERSON>, <PERSON> and <PERSON> [CLN22] gave a 1.995-approximation for the problem using the Sherali-<PERSON> hierarchy, hence beating the integrality gap of 2 of the classic linear program. We significantly improve upon this result by providing a 1.73-approximation for the problem. Our approach brings together a new preprocessing of correlation clustering instances that enables a new LP formulation which combined with the algorithm from [CLN22] yields the improved bound.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00065"}, {"primary_key": "1139568", "vector": [], "sparse_vector": [], "title": "Planar and Minor-Free Metrics Embed into Metrics of Polylogarithmic Treewidth with Expected Multiplicative Distortion Arbitrarily Close to 1.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove that there is a randomized polynomialtime algorithm that given an edge-weighted graph G excluding a fixed-minor Q on n vertices and an accuracy parameter $\\varepsilon\\gt$ 0, constructs an edge-weighted graph H and an embedding $\\eta: V(G) \\rightarrow V(H)$ with the following properties:•For any constant size Q, the treewidth of H is polynomial in $\\varepsilon^{-1}, \\log n$, and the logarithm of the stretch of the distance metric in G.•The expected multiplicative distortion is $(1+\\varepsilon)$: for every pair of vertices $u, v$ of G, we have $\\operatorname{dist}_{H}(\\eta(u), \\eta(v)) \\geqslant \\operatorname{dist}_{G}(u, v)$ always and $\\mathbb{E}\\left[\\operatorname{dist}_{H}(\\eta(u), \\eta(v))\\right] \\leqslant(1+\\varepsilon) \\operatorname{dist}_{G}(u, v)$. Our embedding is the first to achieve polylogarithmic treewidth of the host graph and comes close to the lower bound by <PERSON> and <PERSON><PERSON>, who showed that any embedding of a planar graph with $\\mathcal{O}(1)$ expected distortion requires the host graph to have treewidth $\\Omega(\\log n)$. It also provides a unified framework for obtaining randomized quasi-polynomial-time approximation schemes for a variety of problems including network design, clustering or routing problems, in minor-free metrics where the optimization goal is the sum of selected distances. Applications include the capacitated vehicle routing problem, and capacitated clustering problems.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00140"}, {"primary_key": "1139569", "vector": [], "sparse_vector": [], "title": "Deterministic Clustering in High Dimensional Spaces: Sketches and Approximation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In all state-of-the-art sketching and coreset techniques for clustering, as well as in the best known fixed-parameter tractable approximation algorithms, randomness plays a key role. For the classic k-median and k-means problems, there are no known deterministic dimensionality reduction procedure or coreset construction that avoid an exponential dependency on the input dimension d, the precision parameter ϵ-1 or k. Furthermore, there is no coreset construction that succeeds with probability 1-1/n and whose size does not depend on the number of input points, n. This has led researchers in the area to ask what is the power of randomness for clustering sketches [Feldman WIREs Data Mining Knowl. Discov'20].Similarly, the best approximation ratio achievable deterministically without a complexity exponential in the dimension are 1+√2 for k-median [<PERSON>, <PERSON>, <PERSON>, <PERSON>, STOC'22] and 6.12903 for k-means [<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Venkat, Inf. Process. Lett.'22]. Those are the best results, even when allowing a complexity FPT in the number of clusters k: this stands in sharp contrast with the (1+ϵ)-approximation achievable in that case, when allowing randomization.In this paper, we provide deterministic sketches constructions for clustering, whose size bounds are close to the best-known randomized ones. We show how to compute a dimension reduction onto ϵ-O(1) log k dimensions in time kO(ϵ-O(1)+log log k) poly (n d), and how to build a coreset of size O(k2 log 3 k ϵ-O(1)) in time 2ϵO(1) k log 3 k+kO(ϵ-O(1)+log log k) poly (n d). In the case where k is small, this answers an open question of [Feldman WIDM'20] and [Munteanu and Schwiegelshohn, Künstliche Intell. '18] on whether it is possible to efficiently compute coresets deterministically.We also construct a deterministic algorithm for computing (1+ ϵ)-approximation to k-median and k-means in high dimensional Euclidean spaces in time 2k2 log 3 k/ϵO(1) poly (n d), close to the best randomized complexity of 2(k/ϵ)O(1) nd (see [Kumar, Sabharwal, Sen, JACM 10] and [Bhattacharya, Jaiswal, Kumar, TCS'18]).Furthermore, our new insights on sketches also yield a randomized coreset construction that uses uniform sampling, that immediately improves over the recent results of [Braverman et al. FOCS '22] by a factor k.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00066"}, {"primary_key": "1139570", "vector": [], "sparse_vector": [], "title": "Streaming Euclidean k-median and k-means with o(log n) Space.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider the classic Euclidean k-median and k-means objective on data streams, where the goal is to provide a $(1+\\varepsilon)$-approximation to the optimal k-median or k-means solution, while using as little memory as possible. Over the last 20 years, clustering in data streams has received a tremendous amount of attention and has been the test-bed for a large variety of new techniques, including coresets, the merge-and-reduce framework, bicriteria approximation, sensitivity sampling, and so on. Despite this intense effort to obtain smaller sketches for these problems, all known techniques require storing at least $\\Omega(\\log (n \\Delta))$ words of memory, where n is size of the input and $\\Delta$ is the aspect ratio. A natural question is if one can beat this logarithmic dependence on n and $\\Delta$. In this paper, we break this barrier by first giving an insertion-only streaming algorithm that achieves a $(1+\\varepsilon)$-approximation to the more general $(k, z)$-clustering problem, using $\\tilde{\\mathcal{O}}\\left(\\frac{d k}{\\varepsilon^{2}}\\right) \\cdot\\left(2^{z \\log z}\\right) \\cdot \\min \\left(\\frac{1}{\\varepsilon^{z}}, k\\right) \\cdot \\operatorname{poly}(\\log \\log (n \\Delta))$ words of memory. Our techniques can also be used to achieve two-pass algorithms for k-median and k-means clustering on dynamic streams using $\\tilde{\\mathcal{O}}\\left(\\frac{1}{\\varepsilon^{2}}\\right) \\cdot \\operatorname{poly}(d, k, \\log \\log (n \\Delta))$ words of memory.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00057"}, {"primary_key": "1139571", "vector": [], "sparse_vector": [], "title": "Graph Colouring Is Hard on Average for Polynomial Calculus and Nullstellensatz.", "authors": ["<PERSON>", "Susanna F. de Rezende", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We prove that polynomial calculus (and hence also Nullstellen<PERSON>z) over any field requires linear degree to refute that sparse random regular graphs, as well as sparse Erdős-Rényi random graphs, are 3-colourable. Using the known relation between size and degree for polynomial calculus proofs, this implies strongly exponential lower bounds on proof size", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00007"}, {"primary_key": "1139572", "vector": [], "sparse_vector": [], "title": "A Randomized Algorithm for Single-Source Shortest Path on Undirected Real-Weighted Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xinkai Shu", "<PERSON><PERSON>"], "summary": "In undirected graphs with real non-negative weights, we give a new randomized algorithm for the single-source shortest path (SSSP) problem with running time $O(m \\sqrt{\\log n \\cdot \\log \\log n})$ in the comparison-addition model. This is the first algorithm to break the $O(m+n \\log n)$ time bound for real-weighted sparse graphs by <PERSON><PERSON><PERSON>'s algorithm with Fibonacci heaps. Previous undirected nonnegative SSSP algorithms give time bound of $O(m \\alpha(m, n)+ \\min \\{n \\log n, n \\log \\log r\\})$ in comparison-addition model, where $\\alpha$ is the inverse-Ackermann function and r is the ratio of the maximum-to-minimum edge weight [<PERSON><PERSON> & <PERSON> 2005], and linear time for integer edge weights in RAM model [<PERSON><PERSON> 1999]. Note that there is a proposed complexity lower bound of $\\Omega(m+\\min \\{n \\log n, n \\log \\log r\\})$ for hierarchy-based algorithms for undirected real-weighted SSSP [<PERSON> & <PERSON> 2005], but our algorithm does not obey the properties required for that lower bound. As a non-hierarchybased approach, our algorithm shows great advantage with much simpler structure, and is much easier to implement.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00035"}, {"primary_key": "1139573", "vector": [], "sparse_vector": [], "title": "Faster Matrix Multiplication via Asymmetric Hashing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Fast matrix multiplication is one of the most fundamental problems in algorithm research. The exponent of the optimal time complexity of matrix multiplication is usually denoted by $\\omega$. This paper discusses new ideas for improving the laser method for fast matrix multiplication. We observe that the analysis of higher powers of the Coppersmith-Winograd tensor [Coppersmith & Winograd 1990] incurs a \"combination loss\", and we partially compensate for it using an asymmetric version of CW's hashing method. By analyzing the eighth power of the CW tensor, we give a new bound of $\\omega/\\lt2.371866$, which improves the previous best bound of $\\omega/\\lt2.372860$ [Alman & Vassilevska Williams 2020]. Our result breaks the lower bound of 2.3725 in [Ambainis, Filmus & Le Gall 2015] because of the new method for analyzing component (constituent) tensors.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00130"}, {"primary_key": "1139574", "vector": [], "sparse_vector": [], "title": "Proof of the Clustered Hadwiger Conjecture.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON><PERSON>'s Conjecture asserts that every $K_{h}$-minor-free graph is properly $(h-1)$-colourable. We prove the following improper analogue of <PERSON><PERSON><PERSON>'s Conjecture: for fixed h, every $K_{h}$-minor-free graph is $(h-1)$-colourable with monochromatic components of bounded size. The number of colours is best possible regardless of the size of monochromatic components. It solves an open problem of <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON> [SIAM J. Disc. Math. 2015], and concludes a line of research initiated in 2007. Similarly, for fixed $t \\geqslant s$, we show that every $K_{s, t}$-minor-free graph is $(s+1)$-colourable with monochromatic components of bounded size. The number of colours is best possible, solving an open problem of <PERSON> and <PERSON> [J. London Math. Soc. 2018]. We actually prove a single theorem from which both of the above results are immediate corollaries. For an excluded apex minor, we strengthen the result as follows: for fixed $t \\geqslant s \\geqslant 3$, and for any fixed apex graph X, every $K_{s, t}$-subgraph-free X-minor-free graph is $(s+1)$-colourable with monochromatic components of bounded size. The number of colours is again best possible.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00116"}, {"primary_key": "1139575", "vector": [], "sparse_vector": [], "title": "Constant Approximation for Private Interdependent Valuations.", "authors": ["Alon Eden", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The celebrated model of auctions with interdependent valuations, introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON> in 1982, has been studied almost exclusively under private signals $s_{1}, \\ldots, s_{n}$ of the n bidders and public valuation functions $v_{i}\\left(s_{1}, \\ldots, s_{n}\\right)$. Recent work in TCS has shown that this setting admits a constant approximation to the optimal social welfare if the valuations satisfy a natural property called submodularity over signals (SOS). More recently, <PERSON> et al. (2022) have extended the analysis of interdependent valuations to include settings with private signals and private valuations, and established $O\\left(\\log ^{2} n\\right)$-approximation for SOS valuations. In this paper we show that this setting admits a constant factor approximation, settling the open question raised by <PERSON> et al. (2022).", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00018"}, {"primary_key": "1139576", "vector": [], "sparse_vector": [], "title": "Path-Reporting Distance Oracles with Logarithmic Stretch and Size O(n log log n).", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Given an n-vertex undirected graph $G=(V, E, w)$ and a parameter $k \\geq 1$, a path-reporting distance oracle (or PRDO) is a data structure of size $S(n, k)$, that given a query $(u, v) \\in V^{2}$, returns an $f(k)$-approximate shortest $u-v$ path P in G within time $q(k)+O(|P|)$. Here $S(n, k), f(k)$ and $q(k)$ are arbitrary (hopefully slowly-growing) functions. A distance oracle that only returns an approximate estimate $\\hat{d}(u, v)$ of the distance $d_{G}(u, v)$ between the queried vertices is called a nonpath-reporting distance oracle.A landmark PRDO due to <PERSON><PERSON> and <PERSON><PERSON> [56] has $S(n, k)=O\\left(k \\cdot n^{1+\\frac{1}{k}}\\right), f(k)=2 k-1$ and $q(k)=O(k)$. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [59] devised an improved query algorithm for this oracle with $q(k)=O(\\log k)$. The size of this oracle is $\\Omega(n \\log n)$ for all k. <PERSON> and <PERSON><PERSON> [30] devised a PRDO with $S(n, k)=O\\left(\\log k \\cdot n^{1+\\frac{1}{k}}\\right), f(k)=O\\left(k^{\\log _{4 / 3} 7}\\right)$ and $q(k)=O(\\log k)$. Neiman and Shabat [46] recently devised an improved PRDO with $S(n, k)=O\\left(n^{1+\\frac{1}{k}}\\right), f(k)=O\\left(k^{\\log _{4 / 3} 4}\\right)$ and $q(k)=O(\\log k)$. These oracles (of [30], [46]) can be much sparser than $O(n \\log n)$ (the oracle of [46] can have linear size), but their stretch is polynomially larger than the optimal bound of $2 k-1$. On the other hand, a long line of non-pathreporting distance oracles culminated in a celebrated result by Chechik [14], in which $S(n, k)=O\\left(n^{1+\\frac{1}{k}}\\right), f(k)=2 k-1$ and $q(k)=O(1)$.In this paper we make a dramatic progress in bridging the gap between path-reporting and non-path-reporting distance oracles. In particular, we devise a PRDO with size $S(n, k)=$ $O\\left(\\left[\\frac{k \\cdot \\log \\log n}{\\log n}\\right] \\cdot n^{1+\\frac{1}{k}}\\right)$, stretch $f(k)=O(k)$ and query time $q(k)=O\\left(\\log \\left\\lceil\\frac{k \\cdot \\log \\log n}{\\log n}\\right\\rceil\\right)$. As $\\left\\lceil\\frac{k \\cdot \\log \\log n}{\\log n}\\right\\rceil=O(\\log k)$ for $k \\leq \\log n$, its size is always at most $O\\left(\\log k \\cdot n^{1+\\frac{1}{k}}\\right)$, and its query time is $O(\\log \\log k)$. Moreover, for $k=O\\left(\\frac{\\log n}{\\log \\log n}\\right)$, we have $\\left[\\frac{k \\cdot \\log \\log n}{\\log n}\\right]=O(1)$, i.e., $S(n, k)=O\\left(n^{1+\\frac{1}{k}}\\right), f(k)=O(k)$, and $q(k)=O(1)$. For $k=\\Theta(\\log n)$, our oracle has size $O(n \\log \\log n)$, stretch $O(\\log n)$ and query time $O\\left(\\log ^{(3)} n\\right)$. We can also have linear size $O(n)$, stretch $O(\\log n \\cdot \\log \\log n)$ and query time $O\\left(\\log ^{(3)} n\\right)$.These trade-offs exhibit polynomial improvement in stretch over the PRDOs of [30], [46]. For $k=\\Omega\\left(\\frac{\\log n}{\\log \\log n}\\right)$, our tradeoffs also strictly improve the long-standing bounds of [56], [59].Our results on PRDOs are based on novel constructions of approximate distance preservers, that we devise in this paper. Specifically, we show that for any $\\epsilon gt 0$, any $k=1,2, \\ldots$, and any graph $G=(V, E, w)$ and a collection $\\mathcal{P}$ of p vertex pairs, there exists a $(1+\\epsilon)$-approximate preserver for $G, \\mathcal{P}$ with $O\\left(\\gamma(\\epsilon, k) \\cdot p+n \\log k+n^{1+\\frac{1}{k}}\\right)$ edges, where $\\gamma(\\epsilon, k)=$ $\\left(\\frac{\\log k}{\\epsilon}\\right)^{O(\\log k)}$. These new preservers are significantly sparser than the previous state-of-the-art approximate preservers due to Kogan and Parter [41].", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00141"}, {"primary_key": "1139577", "vector": [], "sparse_vector": [], "title": "A deterministic near-linear time approximation scheme for geometric transportation.", "authors": ["<PERSON>", "Jiashuai Lu"], "summary": "Given a set of points $P=\\left(P^{+} \\sqcup P^{-}\\right) \\subset \\mathbb{R}^{d}$ for some constant d and a supply function $\\mu: P \\rightarrow \\mathbb{R}$ such that $\\mu(p)\\gt$ $0 \\forall p \\in P^{+}, \\mu(p)\\lt 0 \\forall p \\in P^{-}$, and $\\sum_{p \\in P} \\mu(p)=0$, the geometric transportation problem asks one to find a transportation map $\\tau: P^{+} \\times P^{-} \\rightarrow \\mathbb{R}_{\\geq 0}$ such that $\\sum_{q \\in P^{-}} \\tau(p, q)=\\mu(p) \\forall p \\in P^{+}$, $\\sum_{p \\in P^{+}} \\tau(p, q)=-\\mu(q) \\forall q \\in P^{-}$, and the weighted sum of Euclidean distances for the pairs $\\sum_{(p, q) \\in P^{+} \\times P^{-}} \\tau(p, q) \\cdot\\|q-p\\|_{2}$ is minimized. We present the first deterministic algorithm that computes, in near-linear time, a transportation map whose cost is within a $(1+\\varepsilon)$ factor of optimal. More precisely, our algorithm runs in $O\\left(n \\varepsilon^{-(d+2)} \\log ^{5} n \\log \\log n\\right)$ time for any constant $\\varepsilon>0$. While a randomized $n \\varepsilon^{-O(d)} \\log ^{O(d)} n$ time algorithm for this problem was discovered in the last few years, all previously known deterministic $(1+\\varepsilon)$-approximation algorithms run in $\\Omega\\left(n^{3 / 2}\\right)$ time. A similar situation existed for geometric bipartite matching, the special case of geometric transportation where all supplies are unit, until a deterministic $n \\varepsilon^{-O(d)} \\log ^{O(d)} n$ time $(1+\\varepsilon)$-approximation algorithm was presented at STOC 2022. Surprisingly, our result is not only a generalization of the bipartite matching one to arbitrary instances of geometric transportation, but it also reduces the running time for all previously known $(1+\\varepsilon)$-approximation algorithms, randomized or deterministic, even for geometric bipartite matching. In particular, we give the first $(1+\\varepsilon)$-approximate deterministic algorithm for geometric bipartite matching and the first $(1+\\varepsilon)$ approximate deterministic or randomized algorithm for geometric transportation with no dependence on d in the exponent of the running time's polylog. As an additional application of our main ideas, we also give the first randomized near-linear $O\\left(\\varepsilon^{-2} m \\log ^{O(1)} n\\right)$ time $(1+\\varepsilon)$-approximation algorithm for the uncapacitated minimum cost flow (transshipment) problem in undirected graphs with arbitrary real edge costs.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00078"}, {"primary_key": "1139578", "vector": [], "sparse_vector": [], "title": "On Symmetric Factorizations of Hankel Matrices.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present two conjectures regarding the running time of computing symmetric factorizations for a Hankel matrix H and its inverse $\\mathrm{H}^{-1}$ as BB* under fixed-point arithmetic. If solved, these would result in a faster-than-matrix-multiplication algorithm for solving sparse poly-conditioned linear programming problems, a fundamental problem in optimization and theoretical computer science. To justify our proposed conjectures and running times, we show weaker results of computing decompositions of the form BB* – CC* for Hankel matrices and their inverses with the same running time.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00127"}, {"primary_key": "1139579", "vector": [], "sparse_vector": [], "title": "The Bit Complexity of Efficient Continuous Optimization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Santosh S<PERSON>"], "summary": "We analyze the bit complexity of efficient algorithms for fundamental optimization problems, such as linear regression, p-norm regression, and linear programming (LP). State-of-the-art algorithms are iterative, and in terms of the number of arithmetic operations, they match the current time complexity of multiplying two n-by-n matrices (up to polylogarithmic factors). However, previous work has typically assumed infinite precision arithmetic, and due to complicated inverse maintenance techniques, the actual running times of these algorithms are unknown. To settle the running time and bit complexity of these algorithms, we demonstrate that a core common subroutine, known as inverse maintenance, is backward-stable. Additionally, we show that iterative approaches for solving constrained weighted regression problems can be accomplished with bounded-error preconditioners. Specifically, we prove that linear programs can be solved approximately in matrix multiplication time multiplied by polylog factors that depend on the condition number $\\kappa$ of the matrix and the inner and outer radius of the LP problem. p-norm regression can be solved approximately in matrix multiplication time multiplied by polylog factors in $\\kappa$. Lastly, linear regression can be solved approximately in input-sparsity time multiplied by polylog factors in $\\kappa$. Furthermore, we present results for achieving lower than matrix multiplication time for p-norm regression by utilizing faster solvers for sparse linear systems.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00125"}, {"primary_key": "1139580", "vector": [], "sparse_vector": [], "title": "Towards Separating Computational and Statistical Differential Privacy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Computational differential privacy (CDP) is a natural relaxation of the standard notion of (statistical) differential privacy (SDP) proposed by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (CRYPTO 2008) and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (CRYPTO 2009). In contrast to SDP, CDP only requires privacy guarantees to hold against computationally-bounded adversaries rather than computationally-unbounded statistical adversaries. Despite the question being raised explicitly in several works (e.g., <PERSON><PERSON>, <PERSON>, and <PERSON>, TCC 2016), it has remained tantalizingly open whether there is any task achievable with the CDP notion but not the SDP notion. Even a candidate such task is unknown. Indeed, it is even unclear what the truth could be!In this work, we give the first construction of a task achievable with the CDP notion but not the SDP notion, under the following strong but plausible cryptographic assumptions:•Non-Interactive Witness Indistinguishable Proofs,•Laconic Collision-Resistant Keyless Hash Functions,•Differing-Inputs Obfuscation for Public-Coin Samplers.In particular, we construct a task for which there exists an $\\varepsilon$-CDP mechanism with $\\varepsilon=O(1)$ achieving $1-o(1)$ utility, but any $(\\varepsilon, \\delta)$-SDP mechanism, including computationally-unbounded ones, that achieves a constant utility must use either a super-constant $\\varepsilon$ or an inverse-polynomially large $\\delta$.To prove this, we introduce a new approach for showing that a mechanism satisfies CDP: first we show that a mechanism is \"private\" against a certain class of decision tree adversaries, and then we use cryptographic constructions to \"lift\" this into privacy against computationally bounded adversaries. We believe this approach could be useful to devise further tasks separating CDP from SDP.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00042"}, {"primary_key": "1139581", "vector": [], "sparse_vector": [], "title": "Fast Numerical Multivariate Multipoint Evaluation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Si<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We design nearly-linear time numerical algorithms for the problem of multivariate multipoint evaluation over the fields of rational, real and complex numbers. We consider both exact and approximate versions of the algorithm. The input to the algorithms are (1) coefficients of an m-variate polynomial f with degree d in each variable, and (2) points $\\mathbf{a}_{1}, \\ldots, \\mathbf{a}_{N}$ each of whose coordinate has absolute value bounded by one. Approximate version: Given additionally an accuracy parameter t, the algorithm computes rational numbers $\\beta_{1}, \\ldots, \\beta_{N}$ such that $\\left|f\\left(\\mathbf{a}_{i}\\right)-\\beta_{i}\\right| \\leq 1 / 2^{t}$ for all i, and has a running time of $\\left(\\left(N m+d^{m}\\right) t\\right)^{1+o(1)}$ for all m and all sufficiently large d. Exact version (when over rationals): Given additionally a bound s on the bit-complexity of all the rational numbers in the input and output, the algorithm computes the rational numbers $f\\left(\\mathbf{a}_{1}\\right), \\ldots, f\\left(\\mathbf{a}_{N}\\right)$, in time $\\left(\\left(N m+d^{m}\\right) s\\right)^{1+o(1)}$ for all m and all sufficiently large d. Our results also naturally extend to the case when the input is over the field of real or complex numbers under an appropriate standard model of representation of field elements in such fields.Prior to this work, a nearly-linear time algorithm for multivariate multipoint evaluation (exact or approximate) over any infinite field appears to be known only for the case of univariate polynomials, and was discovered in a recent work of Moroz [Proc. 62nd FOCS, 2021]. In this work, we extend this result from the univariate to the multivariate setting. However, our algorithm is based on ideas that seem to be conceptually different from those of Moroz [Proc. 62nd FOCS, 2021] and crucially relies on a recent algorithm of Bhargava, Ghosh, Guo, Kumar & Umans [Proc. 63rd FOCS, 2022] for multivariate multipoint evaluation over finite fields, and known efficient algorithms for the problems of rational number reconstruction and fast Chinese remaindering in computational number theory.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00088"}, {"primary_key": "1139582", "vector": [], "sparse_vector": [], "title": "On Lifting Integrality Gaps to SSEH Hardness for Globally Constrained CSPs.", "authors": ["Suprovat Ghoshal", "<PERSON><PERSON><PERSON><PERSON> Lee"], "summary": "A $\\mu$-constrained Boolean MAX-CSP $(\\psi)$ instance is a Boolean Max-CSP instance on predicate $\\psi:\\{0,1\\}^{r} \\rightarrow\\{0,1\\}$ where the objective is to find a labeling of relative weight exactly $\\mu$ that maximizes the fraction of satisfied constraints. In this work, we study the approximability of constrained Boolean Max-CSPs via SDP hierarchies by relating the integrality gap of $\\operatorname{Max}-\\operatorname{CSP}(\\psi)$ to its $\\mu$-dependent approximation curve. Formally, assuming the Small-Set Expansion Hypothesis, we show that it is NP-hard to approximate $\\mu$-constrained instances of $\\operatorname{MAX}-\\operatorname{CSP}(\\psi)$ up to factor $\\operatorname{Gap}_{\\ell, \\mu}(\\psi) / \\log (1 / \\mu)^{2}$ (ignoring factors depending on r) for any $\\ell \\geq \\ell(\\mu, r)$. Here, $\\operatorname{Gap}_{\\ell, \\mu}(\\psi)$ is the optimal integrality gap of $\\ell$-round Lasserre relaxation for $\\mu$-constrained $\\operatorname{MAX}-\\operatorname{CSP}(\\psi)$ instances. Our results are derived by combining the framework of Raghavendra [STOC 2008] along with more recent advances in rounding Lasserre relaxations and reductions from the Small-Set Expansion (SSE) problem. A crucial component of our reduction is a novel way of composing generic bias-dependent dictatorship tests with SSE, which could be of independent interest.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00009"}, {"primary_key": "1139583", "vector": [], "sparse_vector": [], "title": "Fourier Growth of Communication Protocols for XOR Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The level-k $\\ell_{1}$-Fourier weight of a Boolean function refers to the sum of absolute values of its level-k Fourier coefficients. Fourier growth refers to the growth of these weights as k grows. It has been extensively studied for various computational models, and bounds on the Fourier growth, even for the first few levels, have proven useful in learning theory, circuit lower bounds, pseudorandomness, and quantum-classical separations.In this work, we investigate the Fourier growth of certain functions that naturally arise from communication protocols for XOR functions (partial functions evaluated on the bitwise XOR of the inputs x and y to <PERSON> and <PERSON>). If a protocol $\\mathcal C$ computes an XOR function, then $\\mathcal{C}(x, y)$ is a function of the parity $x \\oplus y$. This motivates us to analyze the XOR-fiber of the communication protocol $\\mathcal{C}$, defined as $h(z):=\\mathbb{E}_{\\boldsymbol{x}, \\boldsymbol{y}}[\\mathcal{C}(\\boldsymbol{x}, \\boldsymbol{y}) \\mid \\boldsymbol{x} \\oplus \\boldsymbol{y}=z]$.We present improved Fourier growth bounds for the XOR-fibers of randomized protocols that communicate d bits. For the first level, we show a tight $O(\\sqrt{d})$ bound and obtain a new coin theorem, as well as an alternative proof for the tight randomized communication lower bound for the Gap-Hamming problem. For the second level, we show an $d^{3 / 2} \\cdot \\operatorname{polylog}(n)$ bound, which improves the previous $O\\left(d^{2}\\right)$ bound by Girish, Raz, and Tal (ITCS 2021) and implies a polynomial improvement on the randomized communication lower bound for the XOR-lift of the Forrelation problem, which extends the quantum-classical gap for this problem.Our analysis is based on a new way of adaptively partitioning a relatively large set in Gaussian space to control its moments in all directions. We achieve this via martingale arguments and allowing protocols to transmit real values. We also show a connection between Fourier growth and lifting theorems with constant-sized gadgets as a potential approach to prove optimal bounds for the second level and beyond.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00047"}, {"primary_key": "1139584", "vector": [], "sparse_vector": [], "title": "From Grassmannian to Simplicial High-Dimensional Expanders.", "authors": ["<PERSON>"], "summary": "In this paper, we present a new construction of simplicial complexes of subpolynomial degree with arbitrarily good local spectral expansion. Previously, the only known high-dimensional expanders (HDXs) with arbitrarily good expansion and less than polynomial degree were based on one of two constructions, namely Rama<PERSON>jan complexes and coset complexes. In contrast, our construction is a <PERSON>ayley complex over an abelian group, with <PERSON><PERSON><PERSON> generating set given by a Grassmannian HDX.Our construction is in part motivated by a coding-theoretic interpretation of Grassmannian HDXs that we present, which provides a formal connection between Grassmannian HDXs, simplicial HDXs, and LDPC codes. We apply this interpretation to prove a general characterization of the 1-homology groups of our C<PERSON>ley simplicial complexes. Using this result, we construct simplicial complexes on N vertices with arbitrarily good local expansion for which the dimension of the 1-homology group grows as the squared logarithm of N. No prior constructions in the literature have been shown to achieve as large a 1-homology group.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00099"}, {"primary_key": "1139585", "vector": [], "sparse_vector": [], "title": "Top-Down Lower Bounds for Depth-Four Circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a top-down lower-bound method for depth-4 boolean circuits. In particular, we give a new proof of the well-known result that the parity function requires depth-4 circuits of size exponential in $n^{1 / 3}$. Our proof is an application of robust sunflowers and block unpredictability.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00063"}, {"primary_key": "1139586", "vector": [], "sparse_vector": [], "title": "Sparse Submodular Function Minimization.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper we study the problem of minimizing a submodular function $f: 2^{V} \\rightarrow \\mathbb{R}$ that is guaranteed to have a k-sparse minimizer. We give a deterministic algorithm that computes an additive $\\epsilon$-approximate minimizer of such f in $\\widetilde{O}(\\operatorname{poly}(k) \\log (|f| / \\epsilon))$ parallel depth using a polynomial number of queries to an evaluation oracle of f, where $|f|=\\max_{S \\subseteq V}|f(S)|$. Further, we give a randomized algorithm that computes an exact minimizer of f with high probability using $\\widetilde{O}(|V| \\cdot \\operatorname{poly}(k))$ queries and polynomial time. When $k=\\widetilde{O}(1)$, our algorithms use either nearly-constant parallel depth or a nearly-linear number of evaluation oracle queries. All previous algorithms for this problem either use $\\Omega(|V|)$ parallel depth or $\\Omega\\left(|V|^{2}\\right)$ queries. In contrast to state-of-the-art weakly-polynomial and strongly-polynomial time algorithms for SFM, our algorithms use first-order optimization methods, e.g., mirror descent and follow the regularized leader. We introduce what we call sparse dual certificates, which encode information on the structure of sparse minimizers, and both our parallel and sequential algorithms provide new algorithmic tools for allowing first-order optimization methods to efficiently compute them. Correspondingly, our algorithm does not invoke fast matrix multiplication or general linear system solvers and in this sense is more combinatorial than previous state-of-the-art methods.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00126"}, {"primary_key": "1139587", "vector": [], "sparse_vector": [], "title": "Online Ordinal Problems: Optimality of Comparison-based Algorithms and their Cardinal Complexity.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider ordinal online problems, i.e., tasks that only require pairwise comparisons between elements of the input. A classic example is the secretary problem and the game of googol, as well as its multiple combinatorial extensions such as $(J, K)$-secretary, 2-sided game of googol, ordinal-competitive matroid secretary. A natural approach to these tasks is to use ordinal online algorithms that at each step only consider relative ranking among the arrived elements, without looking at the numerical values of the input. We formally study the question of how cardinal algorithms (that can use numerical values of the input) can improve upon ordinal algorithms. We give first a universal construction of the input distribution for any ordinal online problem, such that the advantage of any cardinal algorithm over the ordinal algorithms is at most $1+\\varepsilon$ for arbitrary small $\\varepsilon\\gt 0$. This implies that lower bounds from [<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> 2014], [<PERSON><PERSON> and <PERSON>, SODA 2023] hold not only against any ordinal algorithm, but also against any online algorithm. Another immediate corollary is that cardinal algorithms are no better than ordinal algorithms in the matroid secretary problem with ordinal-competitive objective of [Soto, Turkiel<PERSON>ub, Verdugo, MOR 2021]. However, the value range of the input elements in our construction is huge: $N=$ $O\\left(\\frac{n^{3} \\cdot n ! \\cdot n !}{\\varepsilon}\\right) \\uparrow \\uparrow(n-1)$ (tower of exponents) for an input sequence of length n. As a second result, we identify a class of natural ordinal problems and find cardinal algorithm with a matching advantage of $1+\\Omega\\left(\\frac{1}{\\log (c) N}\\right)$, where $\\log^{(c)} N=\\log \\log \\ldots \\log N$ with c iterative logs and c is an arbitrary constant $c \\leq n-2$. This suggests that for relatively small input numerical values N the cardinal algorithms may be significantly better than the ordinal algorithms on the ordinal tasks, which are typically assumed to be almost indistinguishable prior to our work. This observation leads to a natural complexity measure (we dub it cardinal complexity) for any given ordinal online task: the minimum size $N(\\varepsilon)$ of different numerical values in the input such the advantage of cardinal over ordinal algorithms is at most $1+\\varepsilon$ for any given $\\varepsilon\\gt 0$. As a third result, we show that the game of googol has much lower cardinal complexity of $N=O\\left(\\left(\\frac{n}{\\varepsilon}\\right)^{n}\\right)$.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00113"}, {"primary_key": "1139588", "vector": [], "sparse_vector": [], "title": "Compressing CFI Graphs and Lower Bounds for the Weisfeiler-Leman Refinements.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The k-dimensional Weisfeiler-Leman (k-WL) algorithm is a simple combinatorial algorithm that was originally designed as a graph isomorphism heuristic. It naturally finds applications in <PERSON><PERSON>'s quasipolynomial-time isomorphism algorithm, practical isomorphism solvers, and algebraic graph theory. However, it also has surprising connections to other areas such as logic, proof complexity, combinatorial optimization, and machine learning. The algorithm iteratively computes a coloring of the k-tuples of vertices of a graph. Since <PERSON><PERSON><PERSON>'s linear lower bound [ICALP 2001], it has been an open question whether there is a super-linear lower bound for the iteration number for k-WL on graphs. We answer this question affirmatively, establishing an $\\Omega\\left(n^{k / 2}\\right)$-lower bound for all k.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00052"}, {"primary_key": "1139589", "vector": [], "sparse_vector": [], "title": "Tight Space Lower Bound for Pseudo-Deterministic Approximate Counting.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We investigate one of the most basic problems in streaming algorithms: approximating the number of elements in the stream. Famously, [Mor78] gave a randomized algorithm achieving a constant-factor approximation error for streams of length at most N in space $O(\\log\\log N)$. We investigate the pseudo-deterministic complexity of the problem and prove a tight $\\Omega(\\log N)$ lower bound, thus resolving a problem of [GGMW20].", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00091"}, {"primary_key": "1139590", "vector": [], "sparse_vector": [], "title": "Randomly Punctured Reed-Solomon Codes Achieve the List Decoding Capacity over Polynomial-Size Alphabets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper shows that, with high probability, randomly punctured Reed-Solomon codes over fields of polynomial size achieve the list decoding capacity. More specifically, we prove that for any $\\varepsilon \\gt 0$ and $R \\in(0,1)$, with high probability, randomly punctured Reed-Solomon codes of block length n and rate R are $(1-R-\\varepsilon, O(1 / \\varepsilon))$ list decodable over alphabets of size at least $2^{\\text {poly }(1 / \\varepsilon)} n^{2}$. This extends the recent breakthrough of Brakensiek, Gopi, and Makam (STOC 2023) that randomly punctured Reed-Solomon codes over fields of exponential size attain the generalized Singleton bound of Shangguan and Tam<PERSON> (STOC 2020).", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00019"}, {"primary_key": "1139591", "vector": [], "sparse_vector": [], "title": "Efficient Algorithms for Semirandom Planted CSPs at the Refutation Threshold.", "authors": ["<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present an efficient algorithm to solve semirandom planted instances of any Boolean constraint satisfaction problem (CSP). The semirandom model is a hybrid between worst case and average case input models, where the input is generated by (1) choosing an arbitrary planted assignment $x^{*}$, (2) choosing an arbitrary clause structure, and (3) choosing literal negations for each clause from an arbitrary distribution \"shifted by $x^{*}$\" so that $x^{*}$ satisfies each constraint. For an n variable semirandom planted instance of a k-arity CSP, our algorithm runs in polynomial time and outputs an assignment that satisfies all but a $o(1)$-fraction of constraints, provided that the instance has at least $\\tilde{O}\\left(n^{k / 2}\\right)$ constraints. This matches, up to ${\\mathrm {polylog}} (n)$ factors, the clause threshold for algorithms that solve fully random planted CSPs [23], as well as algorithms that refute random and semirandom CSPs [1], [4]. Our result shows that despite having worst case clause structure, the randomness in the literal patterns makes semirandom planted CSPs significantly easier than worst case, where analogous results require $O\\left(n^{k}\\right)$ constraints [7], [26]. Perhaps surprisingly, our algorithm follows a significantly different conceptual framework when compared to the recent resolution of semirandom CSP refutation. This turns out to be inherent and, at a technical level, can be attributed to the need for relative spectral approximation of certain random matrices — reminiscent of the classical spectral sparsification — which ensures that an SDP can certify the uniqueness of the planted assignment. In contrast, in the refutation setting, it suffices to obtain a weaker guarantee of absolute upper bounds on the spectral norm of related matrices.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00026"}, {"primary_key": "1139592", "vector": [], "sparse_vector": [], "title": "Query-optimal estimation of unitary channels in diamond distance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Ryan <PERSON>&<PERSON>;Donnell", "<PERSON><PERSON>"], "summary": "We consider process tomography for unitary quantum channels. Given access to an unknown unitary channel acting on a d-dimensional qudit, we aim to output a classical description of a unitary that is $\\varepsilon$-close to the unknown unitary in diamond norm. We design an algorithm achieving error $\\varepsilon$ using $O\\left(\\mathrm{~d}^{2} / \\varepsilon\\right)$ applications of the unknown channel and only one qudit. This improves over prior results, which use $O\\left(\\mathrm{~d}^{3} / \\varepsilon^{2}\\right)$ [via standard process tomography] or $O\\left(\\mathrm{~d}^{2.5} / \\varepsilon\\right)$ [Yang, Renner, and Chiribella, PRL 2020] applications. To show this result, we introduce a simple technique to \"bootstrap\" an algorithm that can produce constant-error estimates to one that can produce $\\varepsilon$-error estimates with the Heisenberg scaling. Finally, we prove a complementary lower bound showing that estimation requires $\\Omega\\left(\\mathrm{d}^{2} / \\varepsilon\\right)$ applications, even with access to the inverse or controlled versions of the unknown unitary. This shows that our algorithm has both optimal query complexity and optimal space complexity.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00028"}, {"primary_key": "1139593", "vector": [], "sparse_vector": [], "title": "On small-depth Frege proofs for PHP.", "authors": ["<PERSON>"], "summary": "We study Frege proofs for the one-to-one graph Pigeon Hole Principle defined on the $n \\times n$ grid where n is odd. We are interested in the case where each formula in the proof is a depth d formula in the basis given by $\\wedge, \\vee$, and $\\neg$. We prove that in this situation the proof needs to be of size exponential in $n^{\\Omega(1 / d)}$. If we restrict the size of each line in the proof to be of size M then the number of lines needed is exponential in $n /(\\log M)^{O(d)}$. The main technical component of the proofs is to design a new family of random restrictions and to prove the appropriate switching lemmas.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00010"}, {"primary_key": "1139594", "vector": [], "sparse_vector": [], "title": "Doubley-Efficient Interactive Proofs for Distribution Properties.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Suppose we have access to a small number of samples from an unknown distribution, and would like learn facts about the distribution. An untrusted data server claims to have studied the distribution and makes assertions about its properties. Can the untrusted data server prove that its assertions are approximately correct? Can a short efficiently verifiable proof be generated in polynomial time? We study doubly-efficient interactive proof systems that can be used to verify properties of an unknown distribution over a domain $[N]$. On top of efficient verification, our focus is on proofs that the honest prover can generate in polynomial time. More generally, the complexity of generating the proof should be as close as possible to the complexity of simply running a standalone analysis to determine whether the distribution has the property. Our main result is a new 2-message doubly-efficient interactive proof protocol for verifying any label-invariant distribution property (any property that is invariant to relabeling of the elements in the domain of the distribution). The sample complexity, communication complexity and verifier runtime are all $\\widetilde{O}(\\sqrt{N})$. The proof can be generated in quasi-linear $\\widetilde{O}(N)$ time and sample complexities (the runtimes of the verifier and the honest prover hold under a mild assumption about the property's computational complexity). This improves on prior work, where constructing the proof required super-polynomial time [<PERSON> and <PERSON>, STOC 2022]. Our new proof system is directly applicable to proving (and verifying) several natural and widely-studied properties, such as a distribution's support size, its Shannon entropy, and its distance from the uniform distribution. For these (and many other) properties, the runtime and sample complexities for generating the proof are within polylog $(N)$ factors of the complexities for simply determining whether the property holds.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00049"}, {"primary_key": "1139595", "vector": [], "sparse_vector": [], "title": "Learning in Pessiland via Inductive Inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Pessiland is one of Impagliazzo's five possible worlds in which NP is hard on average, yet no one-way function exists. This world is considered the most pessimistic because it offers neither algorithmic nor cryptographic benefits.In this paper, we develop a unified framework for constructing strong learning algorithms under the nonexistence of a one-way function, indicating a positive aspect of Pessiland. Using our framework, we improve the learning algorithm for adaptively changing distributions, which was introduced by <PERSON><PERSON> and <PERSON> (ICML'06). Although the previous learner assumes the knowledge of underlying distributions, our learner is universal, i.e., does not assume any knowledge on distributions, and has better sample complexity. We also employ our framework to construct a strong agnostic learner with optimal sample complexity, which improves the previous PAC learner of <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> (Crypto'93). Our learning algorithms are worst-case algorithms that run in exponential time with respect to computational depth, and as a by-product, we present the first characterization of the existence of a one-way function by the worst-case hardness of some promise problem in AM. As a corollary of our results, we establish the robustness of average-case learning, that is, the equivalence among various average-case learning tasks, such as (strong and weak) agnostic learning, learning adaptively changing distributions with respect to arbitrary unknown distributions, and weak learning with membership queries with respect to the uniform distribution.Our framework is based on the theory of <PERSON><PERSON>'s inductive inference and the universal extrapolation algorithm of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> (FOCS'90). Conceptually, the framework demonstrates that Pessiland is, in fact, a wonderland for machine learning in which various learning tasks can be efficiently solved by the generic algorithm of universal extrapolation.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00033"}, {"primary_key": "1139596", "vector": [], "sparse_vector": [], "title": "Envy-Free Cake-Cutting for Four Agents.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>via<PERSON>"], "summary": "In the envy-free cake-cutting problem we are given a resource, usually called a cake and represented as the $[0,1]$ interval, and a set of n agents with heterogeneous preferences over pieces of the cake. The goal is to divide the cake among the n agents such that no agent is envious of any other agent. Even under a very general preferences model, this fundamental fair division problem is known to always admit an exact solution where each agent obtains a connected piece of the cake; we study the complexity of finding an approximate solution, i.e., a connected $\\varepsilon$-envy-free allocation. For monotone valuations of cake pieces, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (2012) gave an efficient (poly $(\\log (1 / \\varepsilon))$ queries) algorithm for three agents and posed the open problem of four (or more) monotone agents. Even for the special case of additive valuations, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2022) conjectured an $\\Omega(1 / \\varepsilon)$ lower bound on the number of queries for four agents. We provide the first efficient algorithm for finding a connected $\\varepsilon$-envy-free allocation with four monotone agents. We also prove that as soon as valuations are allowed to be non-monotone, the problem becomes hard: it becomes PPAD-hard, requires poly $(1 / \\varepsilon)$ queries in the black-box model, and even poly $(1 / \\varepsilon)$ communication complexity. This constitutes, to the best of our knowledge, the first intractability result for any version of the cake-cutting problem in the communication complexity model.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00015"}, {"primary_key": "1139597", "vector": [], "sparse_vector": [], "title": "Attribute-Based Encryption for Circuits of Unbounded Depth from Lattices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Although we have known about fully homomorphic encryption (FHE) from circular security assumptions for over a decade [<PERSON><PERSON>, FOCS '10; <PERSON><PERSON><PERSON><PERSON><PERSON>, STOC '11], there is still a significant gap in understanding related homomorphic primitives supporting all unrestricted polynomial-size computations. One prominent example is attribute-based encryption (ABE). The state-of-the-art constructions, relying on the hardness of learning with errors (LWE) [<PERSON><PERSON><PERSON><PERSON><PERSON>, STOC '13; <PERSON><PERSON> et al., Eurocrypt '14], only accommodate circuits up to all predetermined depth, akin to leveled homomorphic encryption. In addition, their components (master public key, secret keys, and ciphertexts) have sizes polynomial in the maximum circuit depth. Even in the simpler setting where a single key is published (or a single circuit is involved), the depth dependency persists, showing up in constructions of 1-key ABE and related primitives, including laconic function evaluation (LFE), 1-key functional encryption (FE), and reusable garbling schemes. So far, the only approach of eliminating depth dependency relies on indistinguishability obfuscation. Intriguingly, for over a decade, it has remained unclear whether the circular security assumptions empowering FHE can similarly benefit ABE. In this work, we introduce new lattice-based techniques to overcome the depth-dependency limitations: •Relying on a circular security assumption, we construct LFE, 1-key FE, 1-key ABE, and reusable garbling schemes capable of evaluating circuits of unbounded depth and size.•Based on the evasive circular LWE assumption, a stronger variant of the recently proposed evasive LWE assumption [Wee, Eurocrypt '22; Tsabary, Crypto '22], we construct a full-fledged ABE scheme for circuits of unbounded depth and size. Our constructions eliminate the multiplicative overheads polynomial in depth from previous constructions. Our LFE, 1key FE, and reusable garbling schemes achieve almost optimal succinctness. Their ciphertexts and input encodings are proportional in length to the input, while function digest, secret keys, and garbled circuits maintain a constant size independent of circuit parameters. Our ABE schemes offer short components, with master public key and ciphertext sizes linear in the attribute length and secret key being constant-size.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00031"}, {"primary_key": "1139598", "vector": [], "sparse_vector": [], "title": "SAT Reduces to the Minimum Circuit Size Problem with a Random Oracle.", "authors": ["<PERSON><PERSON>"], "summary": "The Minimum Circuit Size Problem (MCSP) is the task of deciding, given the truth table of a Boolean function f and a size parameter s, whether there is a circuit computing f of size at most s. It has been an open question since <PERSON>'s seminal work on NP-completeness (1973) whether MCSP is NP-complete. This question has drawn further interest in light of recent connections between MCSP, learning theory, average-case complexity, and cryptography. We show that, with probability one, there is a black-box $\\mathrm{P} /$ poly (as well as a $\\mathrm{PO}^{\\mathcal{O}}$) many-one reduction from (unrelativized) SAT to MCSP on circuits with access to a random oracle $\\mathcal{O}$. This resolves an open question of <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> (STOC 2023) who conjectured the existence of such a reduction. Two important ingredients in our proof are 1) a relaxation of symmetry of information that we call pseudo symmetry of information and 2) a subroutine of the reduction that essentially is a cryptographic proof of work. Our reduction yields additive hardness of approximation that is optimal up to a constant factor and extends to a variety of other metacomplexity problems, including computing time-bounded Kolmogorov complexity $\\left(\\mathrm{K}^{t}\\right)$. Applying the random oracle heuristic from cryptography, where one heuristically \"instantiates\" $\\mathcal{O}$ with a real-world cryptographic hash function, we get a plethora of candidate deterministic polynomial-time many-one reductions from SAT to MCSP and $K^{t}$ in the standard unrelativized world. To our knowledge, no candidate reduction from SAT to MCSP or $\\mathrm{K}^{t}$ was known previously. Moreover, the hardness of approximation in these candidate reductions would imply the NP-hardness of the gap version of $\\mathrm{K}^{t}$ that Hirahara (FOCS 2018) shows has a non-black-box worst-case to average-case reduction. Intriguingly, as a consequence we get that the existence of sufficiently \"unstructured\" functions implies that a problem with a known (non-black-box) worst-case to average-case reduction is NP-complete.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00048"}, {"primary_key": "1139599", "vector": [], "sparse_vector": [], "title": "Optimal mixing of the down-up walk on independent sets of a given size.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Thuy-<PERSON>ng <PERSON>"], "summary": "Let G be a graph on n vertices of maximum degree $\\Delta$. We show that, for any $\\delta\\gt0$, the down-up walk on independent sets of size $k \\leq(1-\\delta) \\alpha_{c}(\\Delta) n$ mixes in time $O_{\\Delta, \\delta}(k \\log n)$, thereby resolving a conjecture of <PERSON> and <PERSON> in an optimal form. Here, $\\alpha_{c}(\\Delta) n$ is the NP-hardness threshold for the problem of counting independent sets of a given size in a graph on n vertices of maximum degree $\\Delta$. Our mixing time has optimal dependence on $k, n$ for the entire range of k; previously, even polynomial mixing was not known. In fact, for $k=\\Omega_{\\Delta}(n)$ in this range, we establish a log-Sobolev inequality with optimal constant $\\Omega_{\\Delta, \\delta}(1 / n)$.At the heart of our proof are three new ingredients, which may be of independent interest. The first is a method for lifting $\\ell_{\\infty}$-independence from a suitable distribution on the discrete cube—in this case, the hard-core model—to the slice by proving stability of an Edgeworth expansion using a multivariate zero-free region for the base distribution. The second is a generalization of the Lee<PERSON><PERSON><PERSON> induction to prove log-Sobolev inequalities for distributions on the slice with considerably less symmetry than the uniform distribution. The third is a sharp decomposition-type result which provides a lossless comparison between the Dirichlet form of the original Markov chain and that of the so-called projected chain in the presence of a contractive coupling.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00101"}, {"primary_key": "1139600", "vector": [], "sparse_vector": [], "title": "Sparsifying Sums of Norms.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Abstract-For any norms $N_{1}, \\ldots, N_{m}$ on $\\mathbb{R}^{n}$ and $N(x):= N_{1}(x)+\\cdots+N_{m}(x)$, we show there is a sparsified norm $\\tilde{N}(x)= w_{1} N_{1}(x)+\\cdots+w_{m} N_{m}(x)$ such that $|N(x)-\\tilde{N}(x)| \\leqslant \\varepsilon N(x)$ for all $x \\in \\mathbb{R}^{n}$, where $w_{1}, \\ldots, w_{m}$ are non-negative weights, of which only $O\\left(\\varepsilon^{-2} n \\log (n / \\varepsilon)(\\log n)^{2.5}\\right)$ are non-zero. Additionally, we show that such weights can be found with high probability in time $O\\left(m(\\log n)^{O(1)}+\\right.$ poly $\\left.(n)\\right) T$, where T is the time required to evaluate a norm $N_{i}(x)$, assuming that $N(x)$ is poly $(n)$ equivalent to the Euclidean norm. This immediately yields analogous statements for sparsifying sums of symmetric submodular functions. More generally, we show how to sparsify sums of p th powers of norms when the sum is p-uniformly smooth. 1", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00119"}, {"primary_key": "1139601", "vector": [], "sparse_vector": [], "title": "List Decoding of Tanner and Expander Amplified Codes from Distance Certificates.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We develop new list decoding algorithms for Tanner codes and distance-amplified codes based on bipartite spectral expanders. We show that proofs exhibiting lower bounds on the minimum distance of these codes can be used as certificates discoverable by relaxations in the Sum-of-Squares (SoS) semi-definite programming hierarchy. Combining these certificates with certain entropic proxies to ensure that the solutions to the relaxations cover the entire list, then leads to algorithms for list decoding several families of codes up to the Johnson bound. We prove the following results:- We show that the LDPC Tanner codes of Zémor [IEEE Trans. Inf. Theory 2001] with alphabet size q, block-length n and distance $\\delta$, based on an expander graph with degree d, can be list-decoded up to distance $\\mathcal{J}_{q}(\\delta)-\\varepsilon$ in time $n^{O_{d, q}\\left(1 / \\varepsilon^{4}\\right)}$, where $\\mathcal{J}_{q}(\\delta)$ denotes the Johnson bound.- We show that the codes obtained via the expander-based distance amplification procedure of Al<PERSON>, <PERSON>s and Luby [FOCS 1995] can be list-decoded close to the Johnson bound using the SoS hierarchy, by reducing the list decoding problem to unique decoding of the base code. In particular, starting from any base code unique-decodable up to distance $\\delta$, one can obtain near-MDS codes with rate R and distance $1-R-\\varepsilon$, list-decodable up to the Johnson bound in time $n^{O_{\\varepsilon, \\delta}(1)}$.- We show that the locally testable codes of Dinur et al. [STOC 2022] with alphabet size q, block-length n and distance $\\delta$ based on a square Cayley complex with generator sets of size d, can be list-decoded up to distance $\\mathcal{J}_{q}(\\delta)-\\varepsilon$ in time $n^{O_{d, q}\\left(1 / \\varepsilon^{4}\\right)}$, where $\\mathcal{J}_{q}(\\delta)$ denotes the Johnson bound.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00102"}, {"primary_key": "1139602", "vector": [], "sparse_vector": [], "title": "Beyond Moments: <PERSON><PERSON><PERSON> Learning Affine Transformations with Asymptotically Optimal Error.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Santosh S<PERSON>"], "summary": "We present a polynomial-time algorithm for robustly learning an unknown affine transformation of the standard hypercube from samples, an important and well-studied setting for independent component analysis (ICA). Specifically, given an $\\varepsilon$-corrupted sample from a distribution D obtained by applying an unknown affine transformation $x \\rightarrow A x+b$ to the uniform distribution on a d-dimensional hypercube $[-1,1]^{d}$, our algorithm constructs $\\widehat{A}, \\hat{b}$ such that the total variation distance of the distribution $\\widehat{D}$ from D is $O(\\varepsilon)$ using poly $(d)$ time and samples. Total variation distance is the information-theoretically strongest possible notion of distance in our setting and our recovery guarantees in this distance are optimal up to the absolute constant factor multiplying $\\varepsilon$. In particular, if the rows of A are normalized to be unit length, our total variation distance guarantee implies a bound on the sum of the $\\ell_{2}$ distances between the row vectors of A and $A^{\\prime}, \\sum_{i=1}^{d}\\left\\|a_{(i)}-\\hat{a}_{(i)}\\right\\|_{2}=O(\\varepsilon)$. In contrast, the strongest known prior results only yield an $\\varepsilon^{O(1)}$ (relative) bound on the distance between individual $a_{i}$'s and their estimates and translate into an $O\\left(d \\varepsilon^{O(1)}\\right)$ bound on the total variation distance.Prior algorithms for this problem rely on implementing standard approaches [12] for ICA based on the classical method of moments [18], [32] combined with robust moment estimators. We prove that any approach that relies on method of moments must provably fail to obtain a dimension independent bound on the total error $\\sum_{i}\\left\\|a_{(i)}-\\hat{a}_{(i)}\\right\\|_{2}$ (and consequently, also in total variation distance). Our key innovation is a new approach to ICA (even to outlier-free ICA) that circumvents the difficulties in the classical method of moments and instead relies on a new geometric certificate of correctness of an affine transformation. Our algorithm, Robust Gradient Descent, is based on a new method that iteratively improves its estimate of the unknown affine transformation whenever the requirements of the certificate are not met.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00147"}, {"primary_key": "1139603", "vector": [], "sparse_vector": [], "title": "The Complexity of Dynamic Least-Squares Regression.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We settle the complexity of dynamic least-squares regression (LSR), where rows and labels $\\left(\\mathbf{A}^{(t)}, \\mathbf{b}^{(t)}\\right)$ can be adaptively inserted and/or deleted, and the goal is to efficiently maintain an $\\epsilon$-approximate solution to $\\min _{\\mathbf{x}^{(t)}}\\left\\|\\mathbf{A}^{(t)} \\mathbf{x}^{(t)}-\\mathbf{b}^{(t)}\\right\\|_{2}$ for all $t \\in[T]$. We prove sharp separations $\\left(d^{2-o(1)}\\right.$ vs. $\\left.\\sim d\\right)$ between the amortized update time of: (i) Fully vs. Partially dynamic 0.01-LSR; (ii) High vs. low-accuracy LSR in the partially-dynamic (insertion-only) setting.Our lower bounds follow from a gap-amplification reduction–reminiscent of iterative refinement-from the exact version of the Online Matrix Vector Conjecture (OMv) [HKNS15], to constant approximate OMv over the reals, where the i-th online product $\\mathrm{Hv}^{(i)}$ only needs to be computed to 0.1 -relative error. All previous fine-grained reductions from OMv to its approximate versions only show hardness for inverse polynomial approximation $\\epsilon=$ $n^{-\\omega(1)}$ (additive or multiplicative). This result is of independent interest in fine-grained complexity and for the investigation of the OMv Conjecture, which is still widely open.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00097"}, {"primary_key": "1139604", "vector": [], "sparse_vector": [], "title": "Computing linear sections of varieties: quantum entanglement, tensor decompositions and beyond.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of finding elements in the intersection of an arbitrary conic variety in $\\mathbb{F}^{n}$ with a given linear subspace (where $\\mathbb{F}$ can be the real or complex field). This problem captures a rich family of algorithmic problems under different choices of the variety. The special case of the variety consisting of rank-1 matrices already has strong connections to central problems in different areas like quantum information theory and tensor decompositions. This problem is known to be NP-hard in the worst case, even for the variety of rank-1 matrices.In this work, we propose and analyze an algorithm for solving this problem. Surprisingly, despite the above hardness results we show that our algorithm solves this problem efficiently for \"typical\" subspaces. Here, the subspace $\\mathcal{U} \\subseteq \\mathbb{F}^{n}$ is chosen generically of a certain dimension, potentially with some generic elements of the variety contained in it. Our main result is a guarantee that our algorithm recovers all the elements of $\\mathcal{U}$ that lie in the variety, under some mild non-degeneracy assumptions on the variety. As corollaries, we obtain the following new results:•Polynomial time algorithms for several entangled subspaces problems in quantum entanglement, including determining r-entanglement, complete entanglement, and genuine entanglement of a subspace. While all of these problems are NP-hard in the worst case, our algorithm solves them in polynomial time for generic subspaces of dimension up to a constant multiple of the maximum possible.•Uniqueness results and polynomial time algorithmic guarantees for generic instances of a broad class of low-rank decomposition problems that go beyond tensor decompositions. Here, we recover a decomposition of the form $\\sum_{i=1}^{R} v_{i} \\otimes w_{i}$, where the $v_{i}$ are elements of the given variety $\\mathcal{X}$. This implies new uniqueness results and genericity guarantees even in the special case of tensor decompositions.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00079"}, {"primary_key": "1139605", "vector": [], "sparse_vector": [], "title": "Directed Acyclic Outerplanar Graphs Have Constant Stack Number.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The stack number of a directed acyclic graph G is the minimum k for which there is a topological ordering of G and a k-coloring of the edges such that no two edges of the same color cross, i.e., have alternating endpoints along the topological ordering. We prove that the stack number of directed acyclic outerplanar graphs is bounded by a constant, which gives a positive answer to a conjecture by <PERSON>, <PERSON> and <PERSON> [SIAM <PERSON>, 1999]. As an immediate consequence, this shows that all upward outerplanar graphs have constant stack number, answering a question by <PERSON><PERSON> et al. [GD 2021] and thereby making significant progress towards the problem for general upward planar graphs originating from <PERSON><PERSON><PERSON> and <PERSON> [Order, 1989]. As our main tool we develop the novel technique of directed H-partitions, which might be of independent interest.We complement the bounded stack number for directed acyclic outerplanar graphs by constructing a family of directed acyclic 2-trees that have unbounded stack number, thereby refuting a conjecture by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [GD 2023].", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00118"}, {"primary_key": "1139606", "vector": [], "sparse_vector": [], "title": "Pseudorandom Hashing for Space-bounded Computation with Applications in Streaming.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We revisit <PERSON><PERSON>'s classical pseudorandom generator (PRG) for space-bounded computation (STOC 1990) and its applications in streaming algorithms. We describe a new generator, HashPRG, that can be thought of as a symmetric version of <PERSON><PERSON>'s generator over larger alphabets. Our generator allows a trade-off between seed length and the time needed to compute a given block of the generator's output. HashPRG can be used to obtain derandomizations with much better update time and without sacrificing space for a large number of data stream algorithms, for example:•<PERSON><PERSON>'s $F_{p}$ estimation algorithm for constant $p \\gt 2$ (ICASSP, 2017) assumes a random oracle, but achieves optimal space and constant update time. Using HashPRG's time-space trade-off we eliminate the random oracle assumption while preserving the other properties. Previously no time-optimal derandomization was known. Using similar techniques, we give an algorithm for a relaxed version of $\\ell_{p}$ sampling in a turnstile stream. Both of our algorithms use $\\tilde{O}\\left(d^{1-2 / p}\\right)$ bits of space and have $O(1)$ update time.•For $0\\lt p\\lt2$, the $1 \\pm \\varepsilon$ approximate $F_{p}$ estimation algorithm of <PERSON> et al., (STOC, 2011) uses an optimal $O\\left(\\varepsilon^{-2} \\log d\\right)$ bits of space but has an update time of $O\\left(\\log ^{2}(1 / \\varepsilon) \\log \\log (1 / \\varepsilon)\\right)$. Using HashPRG, we show that if $1 / \\sqrt{d} \\leq \\varepsilon \\leq 1 / d^{c}$ for an arbitrarily small constant $c \\gt 0$, then we can obtain a $1 \\pm \\varepsilon$ approximate $F_{p}$ estimation algorithm that uses the optimal $O\\left(\\varepsilon^{-2} \\log d\\right)$ bits of space and has an update time of $O(\\log d)$ in the Word RAM model, which is more than a quadratic improvement in the update time. We obtain similar improvements for entropy estimation.•CountSketch, with the fine-grained error analysis of Minton and Price (SODA, 2014). For derandomization, they suggested a direct application of Nisan's generator, yielding a logarithmic multiplicative space overhead. With HashPRG we obtain an efficient derandomization yielding the same asymptotic space as when assuming a random oracle. Our ability to obtain a time-efficient derandomization makes crucial use of HashPRG's symmetry. We also give the first derandomization of a recent private version of CountSketch.For a d-dimensional vector x being updated in a turnstile stream, we show that $\\|x\\|_{\\infty}$ can be estimated up to an additive error of $\\varepsilon\\|x\\|_{2}$ using $O\\left(\\varepsilon^{-2} \\log (1 / \\varepsilon) \\log d\\right)$ bits of space. Additionally, the update time of this algorithm is $O(\\log 1 / \\varepsilon)$ in the Word RAM model. We show that the space complexity of this algorithm is optimal up to constant factors. However, for vectors x with $\\|x\\|_{\\infty}=\\Theta\\left(\\|x\\|_{2}\\right)$, we show that the lower bound can be broken by giving an algorithm that uses $O\\left(\\varepsilon^{-2} \\log d\\right)$ bits of space which approximates $\\|x\\|_{\\infty}$ up to an additive error of $\\varepsilon\\|x\\|_{2}$. We use our aforementioned derandomization of the CountSketch data structure to obtain this algorithm, and using the time-space trade off of HashPRG, we show that the update time of this algorithm is also $O(\\log 1 / \\varepsilon)$ in the Word RAM model.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00093"}, {"primary_key": "1139607", "vector": [], "sparse_vector": [], "title": "Sensitivity and Dynamic Distance Oracles via Generic Matrices and Frobenius Form.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Algebraic techniques have had an important impact on graph algorithms so far. Porting them, e.g., the matrix inverse, into the dynamic regime improved best-known bounds for various dynamic graph problems. In this paper, we develop new algorithms for another cornerstone algebraic primitive, the Frobenius normal form (FNF). We apply our developments to dynamic and fault-tolerant exact distance oracle problems on directed graphs.For generic matrices A over a finite field accompanied by an FNF, we show (1) an efficient data structure for querying submatrices of the first $k \\geq 1$ powers of A, and (2) a near-optimal algorithm updating the FNF explicitly under rank-1 updates.By representing an unweighted digraph using a generic matrix over a sufficiently large field (obtained by random sampling) and leveraging the developed FNF toolbox, we obtain:•a conditionally optimal distance sensitivity oracle (DSO) in the case of single-edge or single-vertex failures, providing a partial answer to the open question of <PERSON><PERSON> and <PERSON> [ICALP 2021],•a multiple-failures DSO improving upon the state of the art (vd. Brand and <PERSON> [FOCS 2019]) wrt. both preprocessing and query time,•improved dynamic distance oracles in the case of single-edge updates,•a dynamic distance oracle supporting vertex updates, i.e., changing all edges incident to a single vertex, in $\\widetilde{O}\\left(n^{2}\\right)$ worst-case time and distance queries in $\\widetilde{O}(n)$ time.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00106"}, {"primary_key": "1139608", "vector": [], "sparse_vector": [], "title": "Strong Bounds for 3-Progressions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that for some constant $\\beta\\gt0$, any subset A of integers $\\{1, \\ldots, N\\}$ of size at least $2^{-O\\left((\\log N)^{\\beta}\\right)} \\cdot N$ contains a non-trivial three-term arithmetic progression. Previously, three-term arithmetic progressions were known to exist only for sets of size at least $N /(\\log N)^{1+c}$ for a constant $c\\gt0$.Our approach is first to develop new analytic techniques for addressing some related questions in the finite-field setting and then to apply some analogous variants of these same techniques, suitably adapted for the more complicated setting of integers.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00059"}, {"primary_key": "1139609", "vector": [], "sparse_vector": [], "title": "Matrix Completion in Almost-Verification Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We give a new framework for solving the fundamental problem of low-rank matrix completion, i.e., approximating a rank- r matrix $\\mathbf{M} \\in \\mathbb{R}^{m \\times n}$ (where $m \\geq n$) from random observations. First, we provide an algorithm which completes M on $99 \\%$ of rows and columns under no further assumptions on M from $\\approx m r$ samples and using $\\approx m r^{2}$ time. Then, assuming the row and column spans of M satisfy additional regularity properties, we show how to boost this partial completion guarantee to a full matrix completion algorithm by aggregating solutions to regression problems involving the observations. In the well-studied setting where <PERSON> has incoherent row and column spans, our algorithms complete M to high precision from $m r^{2+o(1)}$ observations in $m r^{3+o(1)}$ time (omitting logarithmic factors in problem parameters), improving upon the prior state-of-the-art [JN15] which used $\\approx m r^{5}$ samples and $\\approx m r^{7}$ time. Under an assumption on the row and column spans of M we introduce (which is satisfied by random subspaces with high probability), our sample complexity improves to an almost information-theoretically optimal $m r^{1+o(1)}$, and our runtime improves to $m r^{2+o(1)}$. Our runtimes have the appealing property of matching the best known runtime to verify that a rankr decomposition $\\mathrm{UV}^{\\top}$ agrees with the sampled observations. We also provide robust variants of our algorithms that, given random observations from $\\mathrm{M}+\\mathrm{N}$ with $\\|\\mathrm{N}\\|_{\\mathrm{F}} \\leq \\Delta$, complete M to Frobenius norm distance $\\approx r^{1.5} \\Delta$ in the same runtimes as the noiseless setting. Prior noisy matrix completion algorithms [CP10] only guaranteed a distance of $\\approx \\sqrt{n} \\Delta$.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00129"}, {"primary_key": "1139610", "vector": [], "sparse_vector": [], "title": "Collapsing the Hierarchy of Compressed Data Structures: Suffix Arrays in Optimal Compressed Space.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The last two decades have witnessed a dramatic increase in the amount of highly repetitive datasets consisting of sequential data (strings, texts). Processing these massive amounts of data using conventional data structures is infeasible. This fueled the development of compressed text indexes, which efficiently answer various queries on a given text, typically in polylogarithmic time, while occupying space proportional to the compressed representation of the text. There exist numerous structures supporting queries ranging from simple \"local\" queries, such as random access, through more complex ones, including longest common extension (LCE) queries, to the most powerful queries, such as the suffix array (SA) functionality. Alongside the rich repertoire of queries followed a detailed study of the trade-off between the size and functionality of compressed indexes (see: Navarro; ACM Comput. Surv. 2021). It is widely accepted that this hierarchy of structures tells a simple story: the more powerful the queries, the more space is needed. On the one hand, random access, the most basic query, can be supported using $\\mathcal{O}\\left(\\delta \\log \\frac{n \\log \\sigma}{\\delta \\log n}\\right)$ space (where n is the length of the text, $\\sigma$ is the alphabet size, and $\\delta$ is the text's substring complexity), which is known to be the asymptotically smallest space sufficient to represent any string with parameters $n, \\sigma$, and $\\delta$ (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>; IEEE Trans. Inf. Theory 2023). The other end of the hierarchy is occupied by indexes supporting the suffix array queries. The currently smallest one takes $\\mathcal{O}\\left(r \\log \\frac{n}{r}\\right)$ space, where $r \\geq \\delta$ is the number of runs in the Burrows-Wheeler Transform of the text (Gagie, Navarro, and Prezza; J. ACM 2020). We present a new compressed index, referred to as $\\delta$ SA, that supports the powerful SA functionality and needs only $\\mathcal{O}\\left(\\delta \\log \\frac{n \\log \\sigma}{\\delta \\log n}\\right)$ space. This collapses the hierarchy of compressed data structures into a single point: The space required to represent the text is simultaneously sufficient to efficiently support the full SA functionality. Since suffix array queries are the most widely utilized queries in string processing and data compression, our result immediately improves the space complexity of dozens of algorithms, which can now be executed in $\\delta$-optimal compressed space. The $\\delta$-SA supports both suffix array and inverse suffix array queries in $\\mathcal{O}\\left(\\log ^{4+\\epsilon} n\\right)$ time (where $\\epsilon \\gt 0$ is any predefined constant). Our second main result is an $\\mathcal{O}(\\delta$ polylog $n)$-time construction of the $\\delta$-SA from the Lempel-Ziv (LZ77) parsing of the text. This is the first algorithm that builds an SA index in compressed time, i.e., time nearly linear in the compressed input size. For highly repetitive texts, this is up to exponentially faster than the previously best algorithm, which builds an $\\mathcal{O}\\left(r \\log \\frac{n}{r}\\right)$-size index in $\\mathcal{O}(\\sqrt{\\delta n}$ polylog $n)$ time. To obtain our results, we develop numerous new techniques of independent interest. This includes deterministic restricted recompression, $\\delta$-compressed string synchronizing sets, and their construction in compressed time. We also improve many other auxiliary data structures; e.g., we show the first $\\mathcal{O}\\left(\\delta \\log \\frac{n \\log \\sigma}{\\delta \\log n}\\right)$-size index for LCE queries along with its efficient construction from the LZ77 parsing.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00114"}, {"primary_key": "1139611", "vector": [], "sparse_vector": [], "title": "Slicing all Edges of an n-cube Requires n2/3 Hyperplanes.", "authors": ["<PERSON><PERSON>"], "summary": "Consider the n-cube graph with vertices $\\{-1,1\\}^{n}$ and edges connecting vertices with Hamming distance 1. How many hyperplanes in $\\mathbb{R}^{n}$ are needed in order to dissect all edges? We show that at least $\\widetilde{\\Omega}(n^{2/3})$ are needed, which improves the previous bound of $\\Omega(n^{0.51})$ by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00117"}, {"primary_key": "1139612", "vector": [], "sparse_vector": [], "title": "Thin Trees for Laminar Families.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the laminar-constrained spanning tree problem, the goal is to find a minimum-cost spanning tree which respects upper bounds on the number of times each cut in a given laminar family is crossed. This generalizes the well-studied degree-bounded spanning tree problem, as well as a previously studied setting where a chain of cuts is given. We give the first constant-factor approximation algorithm; in particular we show how to obtain a multiplicative violation of the crossing bounds of less than 22 while losing less than a factor of 5 in terms of cost. Our result compares to the natural $L P$ relaxation. As a consequence, our results show that given a k-edge-connected graph and a laminar family $\\mathcal{L} \\subseteq 2^{V}$ of cuts, there exists a spanning tree which contains only an $O(1 / k)$ fraction of the edges across every cut in $\\mathcal{L}$. This can be viewed as progress towards the Thin Tree Conjecture, which (in a strong form) states that this guarantee can be obtained for all cuts simultaneously.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00011"}, {"primary_key": "1139613", "vector": [], "sparse_vector": [], "title": "Properly learning decision trees with queries is NP-hard.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We prove that it is NP-hard to properly PAC learn decision trees with queries, resolving a longstanding open problem in learning theory (<PERSON><PERSON><PERSON>y 1993; <PERSON><PERSON><PERSON><PERSON> 1999; <PERSON><PERSON><PERSON> 2002; <PERSON><PERSON><PERSON> 2016). While there has been a long line of work, dating back to (<PERSON> 1988), establishing the hardness of properly learning decision trees from random examples, the more challenging setting of query learners necessitates different techniques and there were no previous lower bounds. En route to our main result, we simplify and strengthen the best known lower bounds for a different problem of Decision Tree Minimization (Zantema<PERSON> 2000; <PERSON><PERSON>g 2003).On a technical level, we introduce the notion of hardness distillation, which we study for decision tree complexity but can be considered for any complexity measure: for a function that requires large decision trees, we give a general method for identifying a small set of inputs that is responsible for its complexity. Our technique even rules out query learners that are allowed constant error. This contrasts with existing lower bounds for the setting of random examples which only hold for inverse-polynomial error.Our result, taken together with a recent almost-polynomial time query algorithm for properly learning decision trees under the uniform distribution (<PERSON> 2022), demonstrates the dramatic impact of distributional assumptions on the problem.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00146"}, {"primary_key": "1139614", "vector": [], "sparse_vector": [], "title": "Approximating Edit Distance in the Fully Dynamic Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The edit distance is a fundamental measure of sequence similarity, defined as the minimum number of character insertions, deletions, and substitutions needed to transform one string into the other. Given two strings of length at most n, a simple dynamic programming computes their edit distance exactly in $\\mathcal{O}\\left(n^{2}\\right)$ time, which is also the best possible (up to subpolynomial factors) assuming the Strong Exponential Time Hypothesis (SETH). The last few decades have seen tremendous progress in edit distance approximation, where the runtime has been brought down to subquadratic, to near-linear, and even to sublinear at the cost of approximation. In this paper, we study the dynamic edit distance problem where the strings change dynamically as the characters are substituted, inserted, or deleted over time. Each change may happen at any location of either of the two strings. The goal is to maintain the (exact or approximate) edit distance of such dynamic strings while minimizing the update time. The exact edit distance can be maintained in $\\mathcal{O}\\left(n \\log ^{2} n\\right)$ time per update (<PERSON><PERSON><PERSON><PERSON><PERSON>, Kociumaka, Mozes; 2020), which is again tight assuming SETH. Unfortunately, even with the unprecedented progress in edit distance approximation in the static setting, strikingly little is known regarding dynamic edit distance approximation. Utilizing the best near-linear-time (<PERSON><PERSON>, <PERSON>; 2020) and sublinear-time (<PERSON>, <PERSON>, <PERSON>, <PERSON>; 2022) approximation algorithm, an old exact algorithm (<PERSON><PERSON> and <PERSON>; 1988), and a generic dynamic strings implementation (<PERSON><PERSON><PERSON>, <PERSON>dar, <PERSON>rig; 1996), it is possible to achieve an $\\mathcal{O}\\left(n^{c}\\right)$-approximation in $n^{0.5-c+o(1)}$ update time for any constant $c \\in\\left[0, \\frac{1}{6}\\right]$. Improving upon this trade-off, characterized by the approximation-ratio and update-time product $n^{0.5+o(1)}$, remains wide open. The contribution of this work is a dynamic $n^{o(1)}$-approximation algorithm with amortized expected update time of $n^{o(1)}$. In other words, we bring the approximation-ratio and update-time product down to $n^{o(1)}$, which is also the best possible with the current state of the art in static algorithms. Our solution utilizes an elegant framework of precision sampling trees for edit distance approximation (Andoni, Krauthgamer, Onak; 2010). We show how to dynamically maintain precision sampling trees, which comes with significant nontriviality and can be an independent tool of interest for further development in dynamic string algorithms.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00098"}, {"primary_key": "1139615", "vector": [], "sparse_vector": [], "title": "Dynamic treewidth.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present a data structure that for a dynamic graph G that is updated by edge insertions and deletions, maintains a tree decomposition of G of width at most $6 k+5$ under the promise that the treewidth of G never grows above k. The amortized update time is $\\mathcal{O}_{k}\\left(2^{\\sqrt{\\log n} \\log \\log n}\\right)$, where n is the vertex count of G and the $\\mathcal{O}_{k}(\\cdot)$ notation hides factors depending on k. In addition, we also obtain the dynamic variant of <PERSON><PERSON><PERSON><PERSON>'s Theorem: for any fixed property $\\varphi$ expressible in the CMSO 2 logic, the data structure can maintain whether G satisfies $\\varphi$ within the same time complexity bounds. To a large extent, this answers a question posed by <PERSON><PERSON><PERSON><PERSON> [WG 1993].", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00105"}, {"primary_key": "1139616", "vector": [], "sparse_vector": [], "title": "Improved Approximations for Vector Bin Packing via Iterative Randomized Rounding.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study the d-DIMENSIONAL VECTOR BIN PACKING ($d \\mathbf{V B P})$ problem, a generalization of BIN PACKING with central applications in resource allocation and scheduling. In $d \\mathrm{VBP}$, we are given a set of items, each of which is characterized by a d-dimensional volume vector; the objective is to partition the items into a minimum number of subsets (bins), such that the total volume of items in each subset is at most 1 in each dimension. Our main result is an asymptotic approximation algorithm for d VBP that yields a ratio of $(1+\\ln d-\\chi(d)+\\varepsilon)$ for all $d \\in \\mathbb{N}$ and any $\\varepsilon\\gt0$; here, $\\chi(d)$ is some strictly positive function. This improves upon the best known asymptotic ratio of $(1+\\ln d+\\varepsilon)$ due to <PERSON>sal, Caprar<PERSON> and <PERSON><PERSON><PERSON> (SICOMP 2010) for any $d\\gt3$. By slightly modifying our algorithm to include an initial matching phase and applying a tighter analysis, we obtain an asymptotic approximation ratio of $\\left(\\frac{4}{3}+\\varepsilon\\right)$ for the special case of $d=2$, thus substantially improving the previous best ratio of $\\left(\\frac{3}{2}+\\varepsilon\\right)$ due to <PERSON><PERSON>, <PERSON> and <PERSON> (SODA 2016). Our algorithm iteratively solves a configuration LP relaxation for the residual instance (from previous iterations) and samples a small number of configurations based on the solution for the configuration LP. While iterative rounding was already used by Karmarkar and Karp (FOCS 1982) to establish their celebrated result for classic (one-dimensional) BIN PACKING, iterative randomized rounding is used here for the first time in the context of (VECTOR) BIN PACKING. Our results show that iterative randomized rounding is a powerful tool for approximating d VBP, leading to simple algorithms with improved approximation guarantees.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00084"}, {"primary_key": "1139617", "vector": [], "sparse_vector": [], "title": "Lips<PERSON>tz Continuous Algorithms for Graph Problems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Graph algorithms are widely used for decision making and knowledge discovery. To ensure their effectiveness, it is essential that their output remains stable even when subjected to small perturbations to the input because frequent output changes can result in costly decisions, reduced user trust, potential security concerns, and lack of replicability. In this study, we consider the Lipschitz continuity of algorithms as a stability measure and initiate a systematic study of the Lipschitz continuity of algorithms for (weighted) graph problems.Depending on how we embed the output solution to a metric space, we can think of several Lipschitzness notions. We mainly consider the one that is invariant under scaling of weights, and we provide Lipschitz continuous algorithms and lower bounds for the minimum spanning tree problem, the shortest path problem, and the maximum weight matching problem. In particular, our shortest path algorithm is obtained by first designing an algorithm for unweighted graphs that are robust against edge contractions and then applying it to the unweighted graph constructed from the original weighted graph.Then, we consider another Lipschitzness notion induced by a natural mapping from the output solution to its characteristic vector. It turns out that no Lipschitz continuous algorithm exists for this Lipschitz notion, and we instead design algorithms with bounded pointwise Lipschitz constants for the minimum spanning tree problem and the maximum weight bipartite matching problem. Our algorithm for the latter problem is based on an LP relaxation with entropy regularization.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00051"}, {"primary_key": "1139618", "vector": [], "sparse_vector": [], "title": "Strongly History-Independent Storage Allocation: New Upper and Lower Bounds.", "authors": ["<PERSON>"], "summary": "A data structure is said to be strongly history independent if its state is fully determined by its current set of elements (and random bits). One of the most basic questions that strongly history-independent algorithms face is storage allocation: given a set S of up to $(1-\\epsilon) n+1$ elements, assign them to distinct positions in an array of size n. If we ask that the allocation be strongly history independent, then what is the optimal asymptotic cost of performing an insertion or deletion? On the upper-bound side, <PERSON> et al. (ICALP '22) showed how to achieve expected cost $O\\left(1+\\log \\epsilon^{-1}\\right)$. In this paper, we offer a nearly matching lower bound of $\\tilde{\\Omega}\\left(\\log \\epsilon^{-1}\\right)$. As corollaries, we get nearly tight lower bounds for strongly history-independent hashing (STOC '01, FOCS '07, ICALP' 08) and for the so-called memoryless worker-task assignment problem (SSS '17, ICALP '20, ICALP '22). Next we consider the problem of partitioning an array of size n among many items of different sizes (and whose cumulative sizes are at most $(1-\\epsilon) n+1)$. In STOC '01, <PERSON><PERSON> and <PERSON><PERSON> gave a weakly history-independent solution to this problem with logarithmic overhead (and with $\\epsilon=1 / 2$); they posed as an open question whether one could hope to do better. We give a new construction that achieves $O(1)$ expected overhead, also for $\\epsilon=$ $1 / 2$, and that is strongly history independent. Generalizing to $\\epsilon\\lt 1 / 2$, our solution achieves expected overhead $O\\left(1+\\log \\epsilon^{-1}\\right)$ for insertion/deletions of objects whose sizes are at most $O\\left(\\epsilon^{4} n\\right)$.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00111"}, {"primary_key": "1139619", "vector": [], "sparse_vector": [], "title": "Agnostic proper learning of monotone functions: beyond the black-box correction barrier.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We give the first agnostic, efficient, proper learning algorithm for monotone Boolean functions. Given $2^{\\tilde{O}(\\sqrt{n} / \\varepsilon)}$ uniformly random examples of an unknown function $f:\\{ \\pm 1\\}^{n} \\rightarrow\\{ \\pm 1\\}$, our algorithm outputs a hypothesis $g:\\{ \\pm 1\\}^{n} \\rightarrow\\{ \\pm 1\\}$ that is monotone and (opt $+\\varepsilon$)-close to f, where opt is the distance from f to the closest monotone function. The running time of the algorithm (and consequently the size and evaluation time of the hypothesis) is also $2^{\\tilde{O}(\\sqrt{n} / \\varepsilon)}$, nearly matching the lower bound of [13]. We also give an algorithm for estimating up to additive error $\\varepsilon$ the distance of an unknown function f to monotone using a run-time of $2^{\\tilde{O}(\\sqrt{n} / \\varepsilon)}$. Previously, for both of these problems, sample-efficient algorithms were known, but these algorithms were not run-time efficient. Our work thus closes this gap in our knowledge between the run-time and sample complexity.This work builds upon the improper learning algorithm of [17] and the proper semiagnostic learning algorithm of [40], which obtains a non-monotone Boolean-valued hypothesis, then \"corrects\" it to monotone using query-efficient local computation algorithms on graphs. This black-box correction approach can achieve no error better than 2 opt $+\\varepsilon$ information-theoretically; we bypass this barrier bya)augmenting the improper learner with a convex optimization step, andb)learning and correcting a real-valued function before rounding its values to Boolean. Our real-valued correction algorithm solves the \"poset sorting\" problem of [40] for functions over general posets with non-Boolean labels.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00068"}, {"primary_key": "1139620", "vector": [], "sparse_vector": [], "title": "Super-Logarithmic Lower Bounds for Dynamic Graph Problems.", "authors": ["<PERSON><PERSON>", "Huacheng Yu"], "summary": "In this work, we prove a $\\tilde{\\Omega}(\\lg^{3/2} n)$ unconditional lower bound on the maximum of the query time and update time for dynamic data structures supporting reachability queries in n-node directed acyclic graphs under edge insertions. This is the first super-logarithmic lower bound for any natural graph problem. In proving the lower bound, we also make novel contributions to the state-of-the-art data structure lower bound techniques that we hope may lead to further progress in proving lower bounds.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00096"}, {"primary_key": "1139621", "vector": [], "sparse_vector": [], "title": "Optimal Fault-Tolerant Spanners in Euclidean and Doubling Metrics: Breaking the Ω (log n) Lightness Barrier.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "An essential requirement of spanners in many applications is to be fault-tolerant: a $(1+\\epsilon)$-spanner of a metric space is called (vertex) f-fault-tolerant $(f-F T)$ if it remains a $(1+\\epsilon)$-spanner (for the non-faulty points) when up to f faulty points are removed from the spanner. Fault-tolerant (FT) spanners for Euclidean and doubling metrics have been extensively studied since the 90 s. For low-dimensional Euclidean metrics, <PERSON><PERSON><PERSON><PERSON> and <PERSON> in SoCG'03 [CZ03] showed that the optimal guarantees $O(f n), O(f)$ and $O\\left(f^{2}\\right)$ on the size, degree and lightness of f-FT spanners can be achieved via a greedy algorithm, which naïvely runs in $O\\left(n^{3}\\right) \\cdot 2^{O(f)}$ time.$^{1}$ An earlier construction, by <PERSON><PERSON><PERSON><PERSON> et al. [LNS98] from STOC'98, has a faster running time of $O(n \\log n)+n 2^{O(f)}$, but has a slack of $2^{\\Omega(f)}$ in all the three involved parameters. The question of whether the optimal bounds of [CZ03] can be achieved via a fast construction has remained elusive, with the lightness parameter being the bottleneck: Any construction (other than [CZ03]) has lightness either $2^{\\Omega(f)}$ or $\\Omega(\\log n)$. Moreover, in the wider family of doubling metrics, it is not even clear whether there exists an f FT spanner with lightness that depends solely on f (even exponentially): all existing constructions have lightness $\\Omega(\\log n)$ since they are built on the net-tree spanner, which is induced by a hierarchical net-tree of lightness $\\Omega(\\log n)$. In this paper we settle in the affirmative these longstanding open questions. Specifically, we design a construction of f-FT spanners that is optimal with respect to all the involved parameters (size, degree, lightness and running time): For any n-point doubling metric, any $\\epsilon\\gt0$, and any integer $1 \\le \\log f n\\le + {nfn}-)$, an2, our construction provides,-spanner with within time $O(n$ size $O (fn)$, degree $O(f)$ and lightness $O(f^{2})$. To break the $\\Omega (\\log n)$ lightness barrier, we introduce a new geometric object — the light net-forest. Like the net-tree, the light net-forest is induced by a hierarchy of nets. However, to ensure small lightness, the light net-forest is inherently less \"well-connected\" than the net-tree, which, in turn, makes the task of achieving fault-tolerance significantly more challenging. Further, to achieve the optimal degree (and size) together with optimal lightness, and to do so within the optimal running time — we overcome several highly nontrivial technical challenges.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00013"}, {"primary_key": "1139622", "vector": [], "sparse_vector": [], "title": "Two Source Extractors for Asymptotically Optimal Entropy, and (Many) More.", "authors": ["<PERSON><PERSON>"], "summary": "A long line of work in the past two decades or so established close connections between several different pseudorandom objects and applications, including seeded or seedless non-malleable extractors, two source extractors, (bipartite) Ramsey graphs, privacy amplification protocols with an active adversary, non-malleable codes and many more. These connections essentially show that an asymptotically optimal construction of one central object will lead to asymptotically optimal solutions to all the others. However, despite considerable effort, previous works can get close but still lack one final step to achieve truly asymptotically optimal constructions.In this paper we provide the last missing link, thus simultaneously achieving explicit, asymptotically optimal constructions and solutions for various well studied extractors and applications, that have been the subjects of long lines of research. Our results include:•Asymptotically optimal seeded non-malleable extractors, which in turn give two source extractors for asymptotically optimal min-entropy of $O(\\log ~n)$, explicit constructions of K-Ramsey graphs on N vertices with $K=\\log ^{O(1)} N$, and truly optimal privacy amplification protocols with an active adversary.•Two source non-malleable extractors and affine non-malleable extractors for some linear min-entropy with exponentially small error, which in turn give the first explicit construction of non-malleable codes against 2-split state tampering and affine tampering with constant rate and exponentially small error.•Explicit extractors for affine sources, sumset sources, inter-leaved sources, and small space sources that achieve asymptotically optimal min-entropy of $O(\\log ~n)$ or $2s+O(\\log ~n)$ (for space s sources).•An explicit function that requires strongly linear read once branching programs of size $2^{n-O(\\log ~n)}$, which is optimal up to the constant in $O(\\cdot)$. Previously, even for standard read once branching programs, the best known size lower bound for an explicit function is $2^{n-O\\left(\\log ^{2} n\\right)}$.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00075"}, {"primary_key": "1139623", "vector": [], "sparse_vector": [], "title": "Dynamic &quot;Succincter&quot;.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huacheng Yu", "<PERSON><PERSON><PERSON>"], "summary": "Augmented B-trees (aB-trees) are a broad class of data structures. The seminal work \"succincter\" by <PERSON><PERSON><PERSON><PERSON><PERSON> [1] showed that any aB-tree can be stored using only two bits of redundancy, while supporting queries to the tree in time proportional to its depth. It has been a versatile building block for constructing succinct data structures, including rank/select data structures, dictionaries, locally decodable arithmetic coding, storing balanced parenthesis, etc.In this paper, we show how to \"dynamize\" an aB-tree. Our main result is the design of dynamic aB-trees (daB-trees) with branching factor two using only three bits of redundancy (with the help of lookup tables that are of negligible size in applications), while supporting updates and queries in time polynomial in its depth. As an application, we present a dynamic rank/select data structure for n-bit arrays, also known as a dynamic fully indexable dictionary (FID) [2]. It supports updates and queries in $O(\\log n / \\log \\log n)$ time, and when the array has m ones, the \\begin{equation*}\\log \\begin{pmatrix}n \\\\m\\end{pmatrix}+On / 2^{\\log 0.199} n\\end{equation*}bits. Note that the update and query times are optimal even without space constraints due to a lower bound by <PERSON><PERSON> and <PERSON><PERSON> [3]. Prior to our work, no dynamic FID with near-optimal update and query times and redundancy $o(n / \\log n)$ was known. We further show that a dynamic sequence supporting insertions, deletions and rank/select queries can be maintained in (optimal) $O(\\log n / \\log \\log n)$ time and with $O\\left(n \\cdot \\operatorname{poly} \\log \\log n / \\log ^{2} n\\right)$ bits of redundancy.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00104"}, {"primary_key": "1139624", "vector": [], "sparse_vector": [], "title": "Tight Cell-Probe Lower Bounds for Dynamic Succinct Dictionaries.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huacheng Yu", "<PERSON><PERSON><PERSON>"], "summary": "A dictionary data structure maintains a set of at most n keys from the universe $[U]$ under key insertions and deletions, such that given a query $x \\in[U]$, it returns if x is in the set. Some variants also store values associated to the keys such that given a query x, the value associated to x is returned when x is in the set.This fundamental data structure problem has been studied for six decades since the introduction of hash tables in 1953. A hash table occupies $O(n \\log U)$ bits of space with constant time per operation in expectation. There has been a vast literature on improving its time and space usage. The state-of-the-art dictionary by <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and Liu [1] has space consumption close to the information-theoretic optimum, using a total of \\begin{equation*}\\log \\begin{pmatrix} U \\\\ n \\end{pmatrix}+On\\log ^{\\left(k\\right)} n\\end{equation*} bits, while supporting all operations in $O(k)$ time, for any parameter $k \\leq \\log ^{*} n$. The term $O\\left(\\log ^{(k)} n\\right)=O(\\underbrace{\\log \\cdots \\log n})$ is referred to as the wasted bits per key.In this paper, we prove a matching cell-probe lower bound: For $U=n^{1+\\Theta(1)}$, any dictionary with $O\\left(\\log ^{(k)} n\\right)$ wasted bits per key must have expected operational time $\\Omega(k)$, in the cell-probe model with word-size $w=\\Theta(\\log U)$. Furthermore, if a dictionary stores values of $\\Theta(\\log U)$ bits, we show that regardless of the query time, it must have $\\Omega(k)$ expected update time. It is worth noting that this is the first cell-probe lower bound on the trade-off between space and update time for general data structures.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00112"}, {"primary_key": "1139625", "vector": [], "sparse_vector": [], "title": "Improved Hardness of Approximating k-Clique under ETH.", "authors": ["Bing<PERSON> Lin", "<PERSON><PERSON><PERSON>", "Yican Sun", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we prove that assuming the exponential time hypothesis (ETH), there is no $f(k) \\cdot n^{k^{o(1 / \\log \\log k)}}$-time algorithm that can decide whether an n-vertex graph contains a clique of size k or contains no clique of size $k / 2$, and no FPT algorithm can decide whether an input graph has a clique of size k or no clique of size $k / f(k)$, where $f(k)$ is some function in $k^{1-o(1)}$. Our results significantly improve the previous works [1], [2]. The crux of our proof is a framework to construct gap-producing reductions for the k-CLIQUE problem. More precisely, we show that given an error-correcting code $C: \\Sigma_{1}^{k} \\rightarrow \\Sigma_{2}^{k^{\\prime}}$ that is locally testable and smooth locally decodable in the parallel setting, one can construct a reduction which on input a graph G outputs a graph $G^{\\prime}$ in $\\left(k^{\\prime}\\right)^{O(1)} \\cdot n^{O\\left(\\log \\left|\\Sigma_{2}\\right| / \\log \\left|\\Sigma_{1}\\right|\\right)}$ time such•if G has a clique of size k, then $G^{\\prime}$ has a clique of size K, where $K=\\left(k^{\\prime}\\right)^{O(1)}$.•if G has no clique of size k, then $G^{\\prime}$ has no clique of size $(1-\\varepsilon) \\cdot K$ for some constant $\\varepsilon \\in(0,1)$.We then construct such a code with $k^{\\prime}=k^{\\Theta(\\log \\log k)}$ and $\\left|\\Sigma_{2}\\right|=\\left|\\Sigma_{1}\\right|^{k^{0.54}}$, establishing the hardness result above. Our code generalizes the derivative code [3] into the case with a super constant order of derivatives.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00025"}, {"primary_key": "1139626", "vector": [], "sparse_vector": [], "title": "Streaming Lower Bounds and Asymmetric Set-Disjointness.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Frequency estimation in data streams is one of the classical problems in streaming algorithms. Following much research, there are now almost matching upper and lower bounds for the trade-off needed between the number of samples and the space complexity of the algorithm, when the data streams are adversarial. However, in the case where the data stream is given in a random order, or is stochastic, only weaker lower bounds exist. In this work we close this gap, up to logarithmic factors. In order to do so we consider the needle problem, which is a natural hard problem for frequency estimation studied in (<PERSON><PERSON> et al. 2008, <PERSON> et al. 2016). Here, the goal is to distinguish between two distributions over data streams with t samples. The first is uniform over a large enough domain. The second is a planted model; a secret \"needle\" is uniformly chosen, and then each element in the stream equals the needle with probability p, and otherwise is uniformly chosen from the domain. It is simple to design streaming algorithms that distinguish the distributions using space $s \\approx 1 /\\left(p^{2} t\\right)$. It was unclear if this is tight, as the existing lower bounds are weaker. We close this gap and show that the trade-off is near optimal, up to a logarithmic factor. Our proof builds and extends classical connections between streaming algorithms and communication complexity, concretely multi-party unique set-disjointness. We introduce two new ingredients that allow us to prove sharp bounds. The first is a lower bound for an asymmetric version of multi-party unique set-disjointness, where players receive input sets of different sizes, and where the communication of each player is normalized relative to their input length. The second is a combinatorial technique that allows to sample needles in the planted model by first sampling intervals, and then sampling a uniform needle in each interval.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00056"}, {"primary_key": "1139627", "vector": [], "sparse_vector": [], "title": "Toward Better Depth Lower Bounds: A KRW-like theorem for Strong Composition.", "authors": ["Or Meir"], "summary": "One of the major open problems in complexity theory is proving super-logarithmic lower bounds on the depth of circuits (i.e., $P \\nsubseteq NC^{1}$). <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (Computational Complexity 5(3/4), 1995) suggested approaching this problem by proving that depth complexity of a composition of functions $f \\diamond g$ is roughly the sum of the depth complexities of f and g. They showed that the validity of this conjecture would imply that $\\mathbf{P} \\nsubseteq \\mathbf{N C}^{1}$. The intuition that underlies the KRW conjecture is that the composition $f \\diamond g$ should behave like a \"direct-sum problem\", in a certain sense, and therefore the depth complexity of $f \\diamond g$ should be the sum of the individual depth complexities. Nevertheless, there are two obstacles toward turning this intuition into a proof: first, we do not know how to prove that $f \\diamond g$ must behave like a direct-sum problem; second, we do not know how to prove that the complexity of the latter direct-sum problem is indeed the sum of the individual complexities. In this work, we focus on the second obstacle. To this end, we study a notion called \"strong composition\", which is the same as $f \\diamond g$ except that it is forced to behave like a direct-sum problem. We prove a variant of the KRW conjecture for strong composition, thus overcoming the above second obstacle. This result demonstrates that the first obstacle above is the crucial barrier toward resolving the KRW conjecture. Along the way, we develop some general techniques that might be of independent interest.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00064"}, {"primary_key": "1139628", "vector": [], "sparse_vector": [], "title": "Traversing combinatorial 0/1-polytopes via optimization.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we present a new framework that exploits combinatorial optimization for efficiently generating a large variety of combinatorial objects based on graphs, matroids, posets and polytopes. Our method relies on a simple and versatile algorithm for computing a Hamilton path on the skeleton of any 0/1-polytope $\\operatorname{conv}(X)$, where $X \\subseteq\\{0,1\\}^{n}$. The algorithm uses as a black box any algorithm that solves a variant of the classical linear optimization problem $\\min \\{w \\cdot x \\mid x \\in X\\}$, and the resulting delay, i.e., the running time per visited vertex on the Hamilton path, is only by a factor of $\\log n$ larger than the running time of the optimization algorithm. When X encodes a particular class of combinatorial objects, then traversing the skeleton of the polytope $\\operatorname{conv}(X)$ along a Hamilton path corresponds to listing the combinatorial objects by local change operations, i.e., we obtain Gray code listings. As concrete results of our general framework, we obtain efficient algorithms for generating all (c-optimal) bases and independent sets in a matroid; (c-optimal) spanning trees, forests, matchings, maximum matchings, and c-optimal matchings in a general graph; vertex covers, minimum vertex covers, c-optimal vertex covers, stable sets, maximum stable sets and c-optimal stable sets in a bipartite graph; as well as antichains, maximum antichains, c-optimal antichains, and c-optimal ideals of a poset. Specifically, the delay and space required by these algorithms are polynomial in the size of the matroid ground set, graph, or poset, respectively. Furthermore, all of these listings correspond to Hamilton paths on the corresponding combinatorial polytopes, namely the base polytope, matching polytope, vertex cover polytope, stable set polytope, chain polytope and order polytope, respectively. As another corollary from our framework, we obtain an $\\mathcal{O}\\left(t_{\\text{LP}} \\log n\\right)$ delay algorithm for the vertex enumeration problem on 0/1-polytopes $\\left\\{x \\in \\mathbb{R}^{n} \\mid A x \\leq b\\right\\}$, where $A \\in \\mathbb{R}^{m \\times n}$ and $b \\in \\mathbb{R}^{m}$, and $t_{\\text{LP}}$ is the time needed to solve the linear program $\\min \\{w \\cdot x \\mid A x \\leq b\\}$. This improves upon the 25-year old $\\mathcal{O}\\left(t_{\\text{LP}} n\\right)$ delay algorithm due to Bussieck and Lübbecke.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00076"}, {"primary_key": "1139629", "vector": [], "sparse_vector": [], "title": "stateQIP = statePSPACE.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Complexity theory traditionally studies the hardness of solving classical computational problems. In the quantum setting, it is also natural to consider a different notion of complexity, namely the complexity of physically preparing a certain quantum state. We study the relation between two such state complexity classes: statePSPACE, which contains states that can be generated by space-uniform polynomial-space quantum circuits, and stateQIP, which contains states that a polynomialtime quantum verifier can generate by interacting with an all-powerful untrusted quantum prover. The latter class was recently introduced by <PERSON><PERSON> and <PERSON><PERSON> (ITCS 2022), who proved that statePSPACE $\\subseteq$ stateQIP.Our main result is the reverse inclusion, stateQIP $\\subseteq$ statePSPACE, thereby establishing equality of the two classes and providing a natural state-complexity analogue to the celebrated QIP = PSPACE theorem of <PERSON>, et al. (J. <PERSON> 2011). To prove this, we develop a polynomial-space quantum algorithm for solving a large class of exponentially large \"PSPACE-computable\" semidefinite programs (SDPs), which also prepares an optimiser encoded in a quantum state. Our SDP solver relies on recent blockencoding techniques from quantum algorithms, demonstrating that these techniques are also useful for complexity theory.Using similar techniques, we also show that optimal prover strategies for general quantum interactive protocols can be implemented in quantum polynomial space. We prove this by studying an algorithmic version of <PERSON><PERSON><PERSON>'s theorem and establishing an upper bound on the complexity of implementing <PERSON>lman<PERSON> transformations.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00082"}, {"primary_key": "1139630", "vector": [], "sparse_vector": [], "title": "Optimal Testing of Generalized Reed-Muller Codes in Fewer Queries.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "A local tester for an error correcting code $C\\subseteq\\Sigma^{n}$ is a tester that makes Q oracle queries to a given word $w\\in\\bar{\\Sigma}^{n}$ and decides to accept or reject the word w. An optimal local tester is a local tester that has the additional properties of completeness and optimal soundness. By completeness, we mean that the tester must accept with probability 1 if $w\\in C$. By optimal soundness, we mean that if the tester accepts with probability at least $ 1-\\varepsilon$ (where $\\varepsilon$ is small), then it must be the case that w is $O(\\varepsilon/Q)$-close to some codeword $c\\in C$ in Hamming distance. We show that Generalized Reed-Muller codes admit optimal testers with $Q=(C_{p}q)^{\\lceil\\frac{d+1}{q-1}\\rceil+O(1)}$ queries for $C_{p}=(2p-1)^{\\frac{1}{p-1}}$. Here, for a prime power $q=p^{k}$, the Generalized Reed-Muller code, $\\operatorname{RM}[n, q, d]$, consists of the evaluations of all n-variate degree d polynomials over $\\mathbb{F}_{q}$. As $p,q$, and d go to infinity, Q matches the known lower bound of $q^{\\frac{d+1}{q-1}}$ up to a multiplicative factor of 1. Previously, no tester achieving this query complexity was known, and the best known testers due to Haramaty, Shpilka and Sudan [21] (which is optimal) and due to Ron-Zewi and Sudan [33](which was not known to be optimal) both required $q^{\\lceil\\frac{d+1}{q-q/p}\\rceil}$ queries. Our tester achieves query complexity which is polynomially better than by a power of $p/(p-1)$, which is nearly the best query complexity possible for generalized Reed-Muller codes. The tester we analyze is constructed using the same framework of Ron-Zewi and Sudan, and in fact our analysis shows that their tester is optimal as well. More generally, our methods allow us to prove that a wide class of testers, which follow the form of the Ron-Zewi and Sudan tester, are optimal. This result applies to testers for all affine-invariant codes (which are not necessarily generalized Reed-Muller codes).", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00022"}, {"primary_key": "1139631", "vector": [], "sparse_vector": [], "title": "Bounding the Quantum Value of Compiled Nonlocal Games: From CHSH to BQP Verification.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We present a step towards the goal of producing a general cryptographic 'compilation' procedure which can translate any entangled nonlocal game into a single-prover interactive protocol while preserving quantum completeness and soundness, using cryptography to simulate the separation between the provers. A candidate for such a procedure was introduced by <PERSON><PERSON> et al. (STOC '23), who defined a black-box cryptographic compilation procedure that applies to any nonlocal game and showed that it preserves classical value. In this work, we make progress towards a full understanding of the quantum value of the single-prover protocols that result from applying the <PERSON><PERSON> et al. compilation procedure to entangled games. For the special case of CHSH, we prove that the Tsirelson bound holds under the compilation procedure introduced by <PERSON><PERSON> et al., and we also recover a strong version of the 'rigidity' property that makes CHSH so useful. As an application, we give a single-prover cryptographically sound classical verification protocol for BQP, and we prove its soundness using our CHSH rigidity analysis. Our protocol replicates the functionality of <PERSON><PERSON><PERSON>'s protocol (FOCS '18) but with two advantages: (1) the protocol is conceptually intuitive and requires fewer bespoke ingredients, and the soundness analysis is simpler and directly follows the analysis of the nonlocal case, and (2) the soundness analysis does not explicitly use the assumption of a TCF or an adaptive hardcore bit, and only requires QFHE as a black box (though currently the only known constructions of QFHE use TCFs).", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00081"}, {"primary_key": "1139632", "vector": [], "sparse_vector": [], "title": "Explicit orthogonal and unitary designs.", "authors": ["Ryan <PERSON>&<PERSON>;Donnell", "<PERSON><PERSON>", "<PERSON>"], "summary": "We give a strongly explicit construction of ϵ approximate k-designs for the orthogonal group O(N) and the unitary group U(N), for $N=2^{n}$. Our designs are of cardinality $\\operatorname{poly}(N^{k}/\\epsilon)$ (equivalently, they have seed length $O(nk+\\log(1/\\epsilon)))$; up to the polynomial, this matches the number of design elements used by the construction consisting of completely random matrices.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00073"}, {"primary_key": "1139633", "vector": [], "sparse_vector": [], "title": "Convergence of Approximate and Packet Routing Equilibria to Nash Flows Over Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We consider a dynamic model of traffic that has received a lot of attention in the past few years. Infinitesimally small agents aim to travel from a source to a destination as quickly as possible. Flow patterns vary over time, and congestion effects are modeled via queues, which form based on the deterministic queueing model whenever the inflow into a link exceeds its capacity.Are equilibria in this model meaningful as a prediction of traffic behavior? For this to be the case, a certain notion of stability under ongoing perturbations is needed. Real traffic consists of discrete, atomic \"packets\", rather than being a continuous flow of non-atomic agents. Users may not choose an absolutely quickest route available, if there are multiple routes with very similar travel times. We would hope that in both these situations - a discrete packet model, with packet size going to 0, and $\\varepsilon$-equilibria, with $\\varepsilon$ - going to 0 - equilibria converge to dynamic equilibria in the flow over time model. No such convergence results were known.We show that such a convergence result does hold in single-commodity instances for both of these settings, in a unified way. More precisely, we introduce a notion of \"strict\" $\\varepsilon$-equilibria, and show that these must converge to the exact dynamic equilibrium in the limit as $\\varepsilon \\rightarrow 0$. We then show that results for the two settings mentioned can be deduced from this with only moderate further technical effort.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00016"}, {"primary_key": "1139634", "vector": [], "sparse_vector": [], "title": "Secure Computation Meets Distributed Universal Optimality.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We present a new algorithmic approach to distributed secure algorithms that is based on combining two independent lines of research: secure computation and distributed universal optimality. Our end result provides round-efficient distributed algorithms that protect the privacy of the graph vertices against a (possibly large) coalition of semi-honest adversaries.Secure Computation: The notion of perfect privacy dates back to Yao [FOCS '82], and has been extensively addressed by the Cryptographic community over the years. Most of the prior work considers the Multi-Party-Communication (MPC) model, in which the parties are fully connected. Considerably less is known on the (round) complexity of secure algorithms for general graphs, especially under the classical message passing models, such as the CONGEST model. For any biconnected D-diameter graph, a recent line of works [Parter and Yogev, SODA 19, ICALP 19, PODC 19] presented a simulation result that protects against a single semi-honest corruption, by paying an overhead of $D \\cdot \\mathsf{poly}(\\Delta)$ CONGEST rounds, where $\\Delta$ is the maximum degree. Due to an inherent structural barrier, the generalization of the current framework to handling f corruptions, provably leads to an overhead of $O(\\Delta D)^{\\Theta(f)}$ rounds. This can also be shown to be tight for the class of store-and-forward algorithms 1 . 1 In which nodes can only propagate messages as atomic units, without the ability to mix multiple messages together.Secure Computation & Universal Optimality. We present an improved framework for secure computation which bypasses the current exponential in f barrier. For every graph $G=(V, E)$ with vertex-connectivity $\\widetilde{\\Omega}(f)$, our simulation provides a round overhead of $\\mathsf{poly}(\\Delta) \\cdot \\widehat{O}(\\mathbf{S Q}(G))$. 2 The graph measure $\\mathbf{S Q}(G)$ (Shortcut Quality) captures the universal optimal complexity of many network optimization tasks in the (non-secure) CONGEST model, as demonstrated in a recent breakthrough result of [Haeupler, Wajc and Zuzic, STOC 2021]. We are hopeful that the extended graph-theoretic machinery provided in this paper will find further applications in secure computation and beyond. 2 The notation $\\widehat{O}(\\cdot)$ hides $2^{O(\\sqrt{\\log n})}$ factors.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00144"}, {"primary_key": "1139635", "vector": [], "sparse_vector": [], "title": "Near Optimal Memory-<PERSON><PERSON> for Online Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>via<PERSON>"], "summary": "In the experts problem, on each of T days, an agent needs to follow the advice of one of n \"experts\". After each day, the loss associated with each expert's advice is revealed. A fundamental result in learning theory says that the agent can achieve vanishing regret, i.e. their cumulative loss is within $o(T)$ of the cumulative loss of the best-in-hindsight expert. Can the agent perform well without sufficient space to remember all the experts? We extend a nascent line of research on this question in two directions: 1) We give a new algorithm against the oblivious adversary, improving over the memory-regret tradeoff obtained by [PZ23], and nearly matching the lower bound of [SWXZ22]. 2) We also consider an adaptive adversary who can observe past experts chosen by the agent. In this setting we give both a new algorithm and a novel lower bound, proving that roughly $\\sqrt{n}$ memory is both necessary and sufficient for obtaining $o(T)$ regret.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00069"}, {"primary_key": "1139636", "vector": [], "sparse_vector": [], "title": "Certified Hardness vs. Randomness for Log-Space.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Let $\\mathcal{L}$ be a language that can be decided in linear space and let $\\epsilon \\gt 0$ be any constant. Let $\\mathcal{A}$ be the exponential hardness assumption that for every n, membership in $\\mathcal{L}$ for inputs of length n cannot be decided by circuits of size smaller than $2^{\\epsilon n}$. We prove that for every function $f:\\{0,1\\}^{*} \\rightarrow\\{0,1\\}$, computable by a randomized logspace algorithm R, there exists a deterministic logspace algorithm D (attempting to compute f), such that on every input x of length n, the algorithm D outputs one of the following:1)The correct value $f(x)$.2)The string: \"I am unable to compute $f(x)$ because the hardness assumption $\\mathcal{A}$ is false\", followed by a (provenly correct) circuit of size smaller than $2^{\\epsilon n^{\\prime}}$ for membership in $\\mathcal{L}$ for inputs of length $n^{\\prime}$, for some $n^{\\prime}=\\Theta(\\log n)$; that is, a circuit that refutes $\\mathcal{A}$. Moreover, D is explicitly constructed, given R.We note that previous works on the hardness-versus-randomness paradigm give derandomized algorithms that rely blindly on the hardness assumption. If the hardness assumption is false, the algorithms may output incorrect values, and thus a user cannot trust that an output given by the algorithm is correct. Instead, our algorithm D verifies the computation so that it never outputs an incorrect value. Thus, if D outputs a value for $f(x)$, that value is certified to be correct. Moreover, if D does not output a value for $f(x)$, it alerts that the hardness assumption was found to be false, and refutes the assumption.Our next result is a universal derandomizer for BPL (the class of problems solvable by bounded-error randomized logspace algorithms) 1 : We give a deterministic algorithm U that takes as an input a randomized logspace algorithm R and an input x and simulates the computation of R on x, deteriministically. Under the widely believed assumption $\\mathbf{BPL}=\\mathbf{L}$, the space used by U is at most $C_{R} \\cdot \\log n$ (where $C_{R}$ is a constant depending on R). Moreover, for every constant $c \\geq 1$, if $\\operatorname{BPL} \\subseteq \\operatorname{SPACE}\\left[(\\log (n))^{c}\\right]$ then the space used by U is at most $C_{R} \\cdot(\\log (n))^{c}$.Finally, we prove that if optimal hitting sets for ordered branching programs exist then there is a deterministic logspace algorithm that, given a black-box access to an ordered branching program B of size n, estimates the probability that B accepts on a uniformly random input. This extends the result of (Cheng and Hoza CCC 2020), who proved that an optimal hitting set implies a white-box two-sided derandomization. 1 Our result is stated and proved for promise-BPL, but we ignore this difference in the abstract.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00061"}, {"primary_key": "1139637", "vector": [], "sparse_vector": [], "title": "The Subspace Flatness Conjecture and Faster Integer Programming.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In a seminal paper, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (1988) considered a quantity $\\mu_{K L}(\\Lambda, K)$ which denotes the best volume-based lower bound on the covering radius $\\mu(\\Lambda, K)$ of a convex body K with respect to a lattice $\\Lambda$. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proved that $\\mu(\\Lambda, K) \\leq n \\cdot \\mu_{K L}(\\Lambda, K)$ and the Subspace Flatness Conjecture by <PERSON><PERSON> (2012) claims a $O(\\log (2 n))$ factor suffices, which would match the lower bound from the work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. We settle this conjecture up to a constant in the exponent by proving that $\\mu(\\Lambda, K) \\leq$ $O\\left(\\log ^{3}(2 n)\\right) \\cdot \\mu_{K L}(\\Lambda, K)$. Our proof is based on the Reverse Minkowski Theorem due to <PERSON><PERSON> and <PERSON><PERSON> (2017). Following the work of <PERSON><PERSON> <PERSON>(2012,2019)$, we obtain a $(\\log (2 n))^{O(n)}$-time randomized algorithm to solve integer programs in n variables. Another implication of our main result is a near-optimal flatness constant of $O\\left(n \\log ^{3}(2 n)\\right)$.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00060"}, {"primary_key": "1139638", "vector": [], "sparse_vector": [], "title": "C<PERSON> Is Hard on Average for <PERSON><PERSON>-<PERSON>.", "authors": ["Susanna F. de Rezende", "<PERSON>", "<PERSON><PERSON>"], "summary": "We prove that unary <PERSON><PERSON><PERSON><PERSON><PERSON> requires proofs of size $n^{\\Omega(d)}$ to rule out the existence of an $n^{\\Theta(1)}$-clique in Erdős-Rényi random graphs whose maximum clique is of size $d \\leq 2 \\log n$. This lower bound is tight up to the multiplicative constant in the exponent. We obtain this result by introducing a technique inspired by pseudo-calibration which may be of independent interest. The technique involves defining a measure on monomials that precisely captures the contribution of a monomial to a refutation. This measure intuitively captures progress and should have further applications in proof complexity.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00008"}, {"primary_key": "1139639", "vector": [], "sparse_vector": [], "title": "<PERSON> and <PERSON><PERSON><PERSON><PERSON> Meet <PERSON>: List-Decoding Explicit Nearly-Optimal Binary Codes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give an efficient algorithm for list-decoding the binary code by <PERSON><PERSON><PERSON><PERSON><PERSON> (STOC 2017) to the Johnson Bound. <PERSON><PERSON><PERSON><PERSON><PERSON>'s code has distance $\\frac{1-\\varepsilon}{2}$ and rate $\\Omega\\left(\\varepsilon^{2+o(1)}\\right)$ and thus it almost achieves the <PERSON><PERSON><PERSON><PERSON><PERSON> bound. Johnson bound states that such codes are combinatorially list decodable upto $\\frac{1-\\rho}{2}-$ fraction of errors as long as $\\rho \\geq \\sqrt{\\varepsilon}$. We give a polynomial time decoding algorithm that nearly achieves this bound. Thus our result implies the only known binary code that simultaneously nearly achieves both the Gilbert-<PERSON><PERSON><PERSON>ov and the Johnson bounds. Our decoding algorithm is based on semidefinite programming hierarchies and includes a new rounding step which might be of independent interest.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00021"}, {"primary_key": "1139640", "vector": [], "sparse_vector": [], "title": "On Pseudolinear Codes for Correcting Adversarial Errors.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We consider error-correction coding schemes for adversarial wiretap channels (AWTCs) in which the channel can a) read a fraction of the codeword bits up to a bound r and b) flip a fraction of the bits up to a bound p. The channel can freely choose the locations of the bit reads and bit flips via a process with unbounded computational power. Codes for the AWTC are of broad interest in the area of information security, as they can provide data resiliency in settings where an attacker has limited access to a storage or transmission medium. We investigate a family of non-linear codes known as pseudolinear codes, which were first proposed by <PERSON><PERSON><PERSON> and <PERSON><PERSON> (FOCS 2001) for constructing list-decodable codes independent of the AWTC setting. Unlike general non-linear codes, pseudolinear codes admit efficient encoders and have succinct representations. We focus on unique decoding and show that random pseudolinear codes can achieve rates up to the binary symmetric channel (BSC) capacity $1-H_{2}(p)$ for any $p, r$ in the less noisy region: $p\\lt1/2$ and $r\\lt1-H_{2}(p)$ where $H_{2}(\\cdot)$ is the binary entropy function. Thus, pseudolinear codes are the first known optimal-rate binary code family for the less noisy AWTC that admit efficient encoders. The above result can be viewed as a derandomization result of random general codes in the AWTC setting, which in turn opens new avenues for applying derandomization techniques to randomized constructions of AWTC codes. Our proof applies a novel concentration inequality for sums of random variables with limited independence which may be of interest as an analysis tool more generally.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00040"}, {"primary_key": "1139641", "vector": [], "sparse_vector": [], "title": "Distribution of the threshold for the symmetric perceptron.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We derive an explicit distribution for the threshold sequence of the symmetric binary perceptron with Gaussian disorder, proving that the critical window is of constant width.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00145"}, {"primary_key": "1139642", "vector": [], "sparse_vector": [], "title": "Improved Streaming Algorithms for Maximum Directed Cut via Smoothed Snapshots.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Madhu <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We give an $\\widetilde{O}(\\sqrt{n})$-space single-pass 0.483-approximation streaming algorithm for estimating the maximum directed cut size (Max-DICUT) in a directed graph on n vertices. This improves over an $O(\\log n)$-space $4 / 9\\lt 0.45$ approximation algorithm due to <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (FOCS 2020), which was known to be optimal for $o(\\sqrt{n})$-space algorithms. Max-DICUT is a special case of a constraint satisfaction problem (CSP). In this broader context, we give the first CSP for which algorithms with $\\widetilde{O}(\\sqrt{n})$- space can provably outperform $o(\\sqrt{n})$- space algorithms. The key technical contribution of our work is development of the notions of a first-order snapshot of a (directed) graph and of estimates of such snapshots. These snapshots can be used to simulate certain (non-streaming) Max-DICUT algorithms, including the \"oblivious\" algorithms introduced by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (Algorithmica, 2015), who showed that one such algorithm Previous work of the authors (SODA 2023) studied the restricted case of bounded-degree graphs, and observed that in this setting, it is straightforward to estimate the snapshot with $\\ell_{1}$ errors and this suffices to simulate oblivious algorithms. But for unbounded-degree graphs, even defining an achievable and sufficient notion of estimation is subtle. We describe a new notion of snapshot estimation and prove its sufficiency using careful smoothing techniques, and then develop an algorithm which sketches such an estimate via a delicate process of intertwined vertex- and edge-subsampling. Prior to our work, the only streaming algorithms for any CSP on general instances were based on generalizations of the $O(\\log n)$-space algorithm for Max-DICUT, and can roughly be characterized as based on \"zeroth\" order snapshots. Our work thus opens the possibility of a new class of algorithms for approximating CSPs by demonstrating that more sophisticated snapshots can outperform cruder ones in the case of Max-DICUT.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00055"}, {"primary_key": "1139643", "vector": [], "sparse_vector": [], "title": "Flip-width: <PERSON><PERSON> and <PERSON><PERSON> on dense graphs.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "We define new graph parameters, called flip-width, that generalize treewidth, degeneracy, and generalized coloring numbers for sparse graphs, and clique-width and twin-width for dense graphs. The flip-width parameters are defined using variants of the <PERSON><PERSON> and <PERSON><PERSON> game, in which the robber has speed bounded by a fixed constant $r \\in \\mathbb{N} \\cup\\{\\infty\\}$, and the cops perform flips (or perturbations) of the considered graph. We then propose a new notion of tameness of a graph class, called bounded flip-width, which is a dense counterpart of classes of bounded expansion of <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, and includes classes of bounded twin-width of <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. This unifies Sparsity Theory and Twin-width Theory, for the first time providing a common language for studying the central notions of the two theories, such as weak coloring numbers and twin-width - corresponding to winning strategies of one player - or dense shallow minors, rich divisions, or well-linked sets, corresponding to winning strategies of the other player. To demonstrate the robustness of the introduced notions, we prove that boundedness of flip-width is preserved by first-order interpretations, or transductions, generalizing previous results concerning classes of bounded expansion and bounded twin-width. We also show that the considered notions are amenable to algorithms, by providing an algorithm approximating the flip-width of a given graph, which runs in slice-wise polynomial time (XP) in the size of the graph. Finally, we propose a more general notion of tameness, called almost bounded flip-width, which is a dense counterpart of nowhere dense classes. We conjecture, and provide evidence, that classes with almost bounded flip-width coincide with monadically dependent (or monadically NIP) classes, introduced by Shelah in model theory. We also provide evidence that classes of almost bounded flip-width characterise the hereditary graph classes for which the model-checking problem is fixed-parameter tractable, which is of central importance in structural and algorithmic graph theory.", "published": "2023-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS57990.2023.00045"}]