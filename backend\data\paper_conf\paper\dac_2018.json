[{"primary_key": "3388996", "vector": [], "sparse_vector": [], "title": "Cross-layer fault-space pruning for hardware-assisted fault injection.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "With shrinking structure sizes, soft-error mitigation has become a major challenge in the design and certification of safety-critical embedded systems. Their robustness is quantified by extensive fault-injection campaigns, which on hardware level can nevertheless cover only a tiny part of the fault space.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196019"}, {"primary_key": "3388997", "vector": [], "sparse_vector": [], "title": "FastGC: accelerate garbage collection via an efficient copyback-based data migration in SSDs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xie"], "summary": "Copyback is an advanced command contributing to accelerating data migration in garbage collection (GC). Unfortunately, detecting copyback feasibility (whether copyback can be carried out with assurable reliability) against data corruption in the traditional copyback-based GC causes an expensive performance penalty. This paper first explores copyback error characteristics on real NAND flash chips, then proposes a fast garbage collection scheme called FastGC. It utilizes copyback error characteristics to efficiently detect copyback feasibility of data instead of transferring out all valid data for detecting. Experiment results in the SSDsim show the proposed FastGC greatly promotes write response time and read response time by up to 44.2% and 66.3% respectively, compared to the traditional copyback-based GC.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196051"}, {"primary_key": "3388998", "vector": [], "sparse_vector": [], "title": "PEP: proactive checkpointing for efficient preemption on GPUs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The demand for multitasking GPUs increases whenever the GPU may be shared by multiple applications, either spatially or temporally. This requires that GPUs can be preempted and switch context to a new application while already executing one. Unlike CPUs, context switching in GPUs is prohibitively expensive due to the large context states to swap out. There have been a number of efforts on reducing the overhead of preemption, through reducing the context sizes or overlapping context switching with execution. All those techniques are reactive approaches, meaning that context switching occurs when the preemption request arrives.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196091"}, {"primary_key": "3388999", "vector": [], "sparse_vector": [], "title": "RAIN: a tool for reliability assessment of interconnect networks - physics to software.", "authors": ["<PERSON>", "Malgorzata Marek-Sadowska"], "summary": "In this paper, we study the main interconnect aging processes: electromigration, thermomigration and stress migration and propose comprehensive yet compact models for transient and steady states based on hydrostatic stress evolution. Our model can be expressed in terms of voltages only which abstracts away the hydrostatic stress. The model also explains some experimental observations, introduces temperature-dependent <PERSON><PERSON><PERSON>'s length criterion and a new time-to-failure formula replacing <PERSON>'s empirical model. A tool is developed based on the proposed model which assesses reliability of multi-segment complex interconnect networks. Experimental results obtained on IBM benchmarks validate the model.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196099"}, {"primary_key": "3389000", "vector": [], "sparse_vector": [], "title": "A novel 3D DRAM memory cube architecture for space applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The first mainstream products in 3D IC design are memory devices where multiple memory tiers are horizontally integrated to offer manifold improvements compared with their 2D counterparts. Unfortunately, none of these existing 3D memory cubes are ready for harsh space environments. This paper presents a new memory cube architecture for space, based on vertical integration of Commercial-Off-The-Shelf (COTS), 3D stacked, DRAM memory devices with a custom Radiation-Hardened-By-Design (RHBD) controller offering high memory capacity, robust reliability and low latency. Validation and evaluation of the ASIC controller will be conducted prior to tape-out on a custom FPGA-based emulator platform integrating the 3D-stack.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195978"}, {"primary_key": "3389001", "vector": [], "sparse_vector": [], "title": "LEMAX: learning-based energy consumption minimization in approximate computing with quality guarantee.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Approximate computing aims to trade accuracy for energy efficiency. Various approximate methods have been proposed in the literature that demonstrate the effectiveness of relaxing accuracy requirements in a specific unit. This provides a basis for exploring simultaneous use of multiple approximate units to improve efficiency under guarantees on quality of results. In this paper, we explore the effect of combining multiple approximate units on the energy consumption and identify the best setting that minimizes energy consumption under a quality constraint. Our approach also enables changes in unit configurations throughout the program. To do this effectively, we need a method to examine the combined impact of multiple approximate units on the output quality, and configure individual units accordingly. To solve this problem, we propose LEMAX that uses gradient descent approach to identify the best configuration of the individual approximate units for a given program. We evaluate the efficacy of LEMAX in minimizing the energy consumption of several machine learning applications with varying size (i.e., number of operations) under different quality constraints. Our evaluation shows that the configuration provided by LEMAX for a system with multiple approximate units improves the energy consumption by on average, 97.7%, 83.12%, and 73.95% for quality loss of 5%, 2% and 0.5%, respectively, compared to configurations obtained for a system with a single approximate resource.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196069"}, {"primary_key": "3389002", "vector": [], "sparse_vector": [], "title": "PIMA-logic: a novel processing-in-memory architecture for highly flexible and energy-efficient logic computation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, we propose PIMA-Logic, as a novel Processing-in-Memory Architecture for highly flexible and efficient Logic computation. Insteadof integrating complex logic units in cost-sensitive memory, PIMA-Logic exploits a hardware-friendly approach to implement Boolean logic functions between operands either located in the same row or the same column within entire memory arrays. Furthermore, it can efficiently process more complex logic functions between multiple operands to further reduce the latency and power-hungry data movement. The proposed architecture is developed based on Spin Orbit Torque Magnetic Random Access Memory (SOT-MRAM) array and it can simultaneously work as a non-volatile memory and a reconfigurable in-memory logic. The device-to-architecture co-simulation results show that PIMA-Logic can achieve up to 56% and 31.6% improvements with respect to overall energy and delay on combinational logic benchmarks compared to recent Pinatubo architecture. We further implement an in-memory data encryption engine based on PIMA-Logic as a case study. With AES application, it shows 77.2% and 21% lower energy consumption compared to CMOS-ASIC and recent RIMPA implementation, respectively.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196092"}, {"primary_key": "3389003", "vector": [], "sparse_vector": [], "title": "CMP-PIM: an energy-efficient comparator-based processing-in-memory neural network accelerator.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> He", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper, an energy-efficient and high-speed comparator-based processing-in-memory accelerator (CMP-PIM) is proposed to efficiently execute a novel hardware-oriented comparator-based deep neural network called CMPNET. Inspired by local binary pattern feature extraction method combined with depthwise separable convolution, we first modify the existing Convolutional Neural Network (CNN) algorithm by replacing the computationally-intensive multiplications in convolution layers with more efficient and less complex comparison and addition. Then, we propose a CMP-PIM that employs parallel computational memory sub-array as a fundamental processing unit based on SOT-MRAM. We compare CMP-PIM accelerator performance on different data-sets with recent CNN accelerator designs. With the close inference accuracy on SVHN data-set, CMP-PIM can get ∼ 94× and 3× better energy efficiency compared to CNN and Local Binary CNN (LBCNN), respectively. Besides, it achieves 4.3× speed-up compared to CNN-baseline with identical network configuration.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196009"}, {"primary_key": "3389004", "vector": [], "sparse_vector": [], "title": "Enabling a new era of brain-inspired computing: energy-efficient spiking neural network with ring topology.", "authors": ["<PERSON><PERSON>", "Jialing Li", "<PERSON><PERSON>", "<PERSON>"], "summary": "The reservoir computing, an emerging computing paradigm, has proven its benefit to multifarious applications. In this work, we successfully designed and fabricated an analog delayed feedback reservoir (DFR) chip. Measurement results demonstrate its rich dynamic behaviors and high energy efficiency. System performance, as well as the robustness, are evaluated. The application of video frame recognition is investigated using a hybrid neural network, which employs the multilayer perceptron (MLP) training model as the readout layer of our designed DFR system, and yields 98% classification accuracy. Compared to results of using the MLP training only, our hybrid training model exhibits much higher recognition rate and accuracy.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196044"}, {"primary_key": "3389005", "vector": [], "sparse_vector": [], "title": "Coding approach for low-power 3D interconnects.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Through-silicon vias (TSVs) in 3D ICs show a significant power consumption, which can be reduced using coding techniques. This work presents an approach which reduces the TSV power consumption by a signal-aware bit assignment which includes inversions to exploit the MOS effect. The approach causes no overhead and results in a guaranteed reduction of the overall power consumption. An analysis of our technique shows a reduction in the TSV power consumption by up to 48 % for real correlated data streams (e.g. image sensor), and 11 % for low-power encoded random data streams.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196010"}, {"primary_key": "3389006", "vector": [], "sparse_vector": [], "title": "Side-channel security of superscalar CPUs: evaluating the impact of micro-architectural features.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Side-channel attacks are performed on increasingly complex targets, starting to threaten superscalar CPUs supporting a complete operating system. The difficulty of both assessing the vulnerability of a device to them, and validating the effectiveness of countermeasures is increasing as a consequence. In this work we prove that assessing the side-channel vulnerability of a software implementation running on a CPU should take into account the microarchitectural features of the CPU itself. We characterize the impact of microarchitectural features and prove the effectiveness of such an approach attacking a dual-core superscalar CPU.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196112"}, {"primary_key": "3389007", "vector": [], "sparse_vector": [], "title": "Thermal-aware optimizations of reRAM-based neuromorphic computing systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "ReRAM-based systems are attractive implementation alternatives for neuromorphic computing because of their high speed and low design cost. In this work, we investigate the impact of temperature on the ReRAM-based neuromorphic architectures and show how varying temperatures have a negative impact on the computation accuracy. We first classify ReRAM crossbar cells based on their temperature and identify effective neural network weights that have large impacts on network outputs. Then, we propose a novel temperature-aware training and mapping scheme to prevent the effective weights from being mapped to hot cells to restore the system accuracy. Evaluation results for a two-layer neural network show that our scheme can improve the system accuracy by up to 39.2%.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196128"}, {"primary_key": "3389008", "vector": [], "sparse_vector": [], "title": "DWE: decrypting learning with errors with errors.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Learning with Errors (LWE) problem is a novel foundation of a variety of cryptographic applications, including quantumly-secure public-key encryption, digital signature, and fully homomorphic encryption. In this work, we propose an approximate decryption technique for LWE-based cryptosystems. Based on the fact that the decryption process for such systems is inherently approximate, we apply hardware-based approximate computing techniques. Rigorous experiments have shown that the proposed technique simultaneously achieved 1.3x (resp., 2.5x) speed increase, 2.06x (resp., 7.89x) area reduction, 20.5% (resp., 4x) of power reduction, and an average of 27.1% (resp., 65.6%) ciphertext size reduction for public-key encryption scheme (resp., a state-of-the-art fully homomorphic encryption scheme).", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196032"}, {"primary_key": "3389009", "vector": [], "sparse_vector": [], "title": "Approximate on-the-fly coarse-grained reconfigurable acceleration for general-purpose applications.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Approximate functional unit designs have the potential to reduce power consumption significantly compared to their precise counterparts; however, few works have investigated composing them to build generic accelerators. In this work, we do a design-space exploration of state-of-the-art approximate designs, propose a flow for designing approximate coarse-grained reconfigurable arrays (CGRAs), and discuss compilation and runtime reconfiguration issues. We compare the energy savings of precise and approximate reconfigurable acceleration and show that the latter can provide up to 50% additional power savings under a 10% quality loss constraint for the applications in the AxBench suite.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195993"}, {"primary_key": "3389010", "vector": [], "sparse_vector": [], "title": "Long live TIME: improving lifetime for training-in-memory engines by structured gradient sparsification.", "authors": ["<PERSON>ai", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Xi<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deeper and larger Neural Networks (NNs) have made breakthroughs in many fields. While conventional CMOS-based computing platforms are hard to achieve higher energy efficiency. RRAM-based systems provide a promising solution to build efficient Training-In-Memory Engines (TIME). While the endurance of RRAM cells is limited, it's a severe issue as the weights of NN always need to be updated for thousands to millions of times during training. Gradient sparsification can address this problem by dropping off most of the smaller gradients but introduce unacceptable computation cost. We proposed an effective framework, SGS-ARS, including Structured Gradient Sparsification (SGS) and Aging-aware Row Swapping (ARS) scheme, to guarantee write balance across whole RRAM crossbars and prolong the lifetime of TIME. Our experiments demonstrate that 356× lifetime extension is achieved when TIME is programmed to train ResNet-50 on Imagenet dataset with our SGS-ARS framework.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196071"}, {"primary_key": "3389011", "vector": [], "sparse_vector": [], "title": "Reconciling remote attestation and safety-critical operation on simple IoT devices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Remote attestation (RA) is a means of malware detection, typically realized as an interaction between a trusted verifier and a potentially compromised remote device (prover). RA is especially relevant for low-end embedded devices that are incapable of protecting themselves against malware infection. Most current RA techniques require on-demand and uninterruptible (atomic) operation. The former fails to detect transient malware that enters and leaves between successive RA instances; the latter involves performing potentially time-consuming computation over prover's memory and/or storage, which can be harmful to the device's safety-critical functionality and general availability. However, relaxing either on-demand or atomic RA operation is tricky and prone to vulnerabilities. This paper identifies some issues that arise in reconciling requirements of safety-critical operation with those of secure remote attestation, including detection of transient and self-relocating malware. It also investigates mitigation techniques, including periodic self-measurements as well as interruptible attestation modality that involves shuffled memory traversals and various memory locking mechanisms.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199853"}, {"primary_key": "3389012", "vector": [], "sparse_vector": [], "title": "Subutai: distributed synchronization primitives in NoC interfaces for legacy parallel-applications.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Parallel applications are essential for efficiently using the computational power of a Multiprocessor System-on-Chip (MPSoC). Unfortunately, these applications do not scale effortlessly with the number of cores because of synchronization operations that take away valuable computational time and restrict the parallelization gains. Moreover, synchronization is also a bottleneck due to sequential access to shared memory. We address this issue and introduce \"Subutai\", a hardware/software (HW/SW) architecture designed to distribute essential synchronization mechanisms over the Network-on-Chip (NoC). It includes Network Interfaces (NIs), drivers and a custom library of a NoC-based MPSoC architecture that speeds up the essential synchronization primitives of any legacy parallel application. Besides, we provide a fast simulation tool for parallel applications and a HW architecture of the NI. Experimental results with PARSEC benchmark show an average application speedup of 2.05 compared to the same architecture running legacy SW solutions for 36% overhead of HW architecture.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196124"}, {"primary_key": "3389013", "vector": [], "sparse_vector": [], "title": "Test cost reduction for X-value elimination by scan slice correlation analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "X-values in test output responses corrupt an output response compaction and can cause a fault coverage loss. X-Masking and X-Canceling MISR methods have been suggested to eliminate X-values, however, there are control data volume and test time overhead issues. These issues become significant as the complexity and the density of the circuits increase. This paper proposes a method to eliminate X's by applying a scan slice granularity X-value correlation analysis. The proposed method exploits scan slice correlation analysis, determines unique control data for the scan slice groups sharing the same control data, and applies them for each scan slice. Hence, the volume of control data can be significantly reduced. The simulation results demonstrate that the proposed method achieves greater control data and test time reduction compared to the conventional methods, without loss of fault coverage.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196127"}, {"primary_key": "3389014", "vector": [], "sparse_vector": [], "title": "GPU obfuscation: attack and defense strategies.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Conventional attacks against existing logic obfuscation techniques rely on the presence of an activated hardware for analysis. In reality, obtaining such activated chips may not always be practical, especially if the on-chip test structures are disabled. In this paper, we develop an iterative SAT formulation based attack strategy for deobfuscating many-core GPU hardware without any requirement of an activated chip. Our experiments on a real testbed using NVIDIA's SASSIFI framework reveal that more than 95% of the application runs on such an approximately unlocked GPU result in correct outcomes with 95% confidence-level and 5% confidence-interval. To counter the proposed attack, we develop a Cache Locking countermeasure which significantly degrades the performance of GPGPU applications for a wrong cache-key.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196058"}, {"primary_key": "3389015", "vector": [], "sparse_vector": [], "title": "A security vulnerability analysis of SoCFPGA architectures.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "SoCFPGAs or FPGAs integrated on the same die with chip multi processors have made it to the market in the past years. In this article we analyse various security loopholes, existing precautions and countermeasures in these architectures. We consider Intel Cyclone/Arria devices and Xilinx Zynq/Ultrascale devices. We present an attacker model and we highlight three different types of attacks namely direct memory attacks, cache timing attacks, and rowhammer attacks that can be used on inadequately protected systems. We present and compare existing security mechanisms in this architectures, and their shortfalls. We present real life example of these attacks and further countermeasures to secure systems based on SoCFPGAs.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195979"}, {"primary_key": "3389016", "vector": [], "sparse_vector": [], "title": "Enabling union page cache to boost file access performance of NVRAM-based storage device.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Due to the fast access performance, byte-addressability, and non-volatility of non-volatile random access memory (NVRAM), NVRAM has emerged as a popular candidate for the design of memory/storage systems on mobile computing systems. For example, the latest 3D xPoint memory could be a kind of NVRAM with much longer life expectancy than NAND flash and could ease the possible endurance issue. When NVRAM is considered as both main memory and storage in mobile computing systems, existing page cache mechanisms introduce too many unnecessary data movements between main memory and storage. To resolve this issue, we propose the concept of \"union page cache,\" which jointly manages data of the page cache in both main memory and storage. To realize this concept, a partial page cache strategy is designed to consider both main memory and storage as its management space and to eliminate unnecessary data movements between main memory and storage without sacrificing the data consistency of file systems. Experimental results show that the proposed strategy can boost the file accessing performance upto 85.62% when using PCM as a case study.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196045"}, {"primary_key": "3389017", "vector": [], "sparse_vector": [], "title": "Exploring the programmability for deep learning processors: from architecture to tensorization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hong<PERSON>", "Chuanjin Richard <PERSON>"], "summary": "This paper presents an instruction and Fabric Programmable Neuron Array (iFPNA) architecture, its 28nm CMOS chip prototype, and a compiler for the acceleration of a variety of deep learning neural networks (DNNs) including convolutional neural networks (CNNs), recurrent neural networks (RNNs), and fully connected (FC) networks on chip. The iFPNA architecture combines instruction-level programmability as in an Instruction Set Architecture (ISA) with logic-level reconfigurability as in a Field-Programmable Gate Array (FPGA) in a sliced structure for scalability. Four data flow models, namely weight stationary, input stationary, row stationary and tunnel stationary, are described as the abstraction of various DNN data and computational dependence. The iFPNA compiler partitions a large-size DNN to smaller networks, each being mapped to, optimized and code generated for, the underlying iFPNA processor using one or a mixture of the four data-flow models. Experimental results have shown that state-of-art large-size CNNs, RNNs, and FC networks can be mapped to the iFPNA processor achieving the near ASIC performance.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196049"}, {"primary_key": "3389018", "vector": [], "sparse_vector": [], "title": "Packet pump: overcoming network bottleneck in on-chip interconnects for GPGPUs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In order to fully exploit GPGPU's parallel processing power, on-chip interconnects need to provide bandwidth efficient data communication. GPGPUs exhibit a many-to-few-to-many traffic pattern which makes the memory controller connected routers the network bottleneck. Inefficient design of conventional routers causes long queues of packets blocked at memory controllers and thus greatly constrained the network bandwidth. In this work, we employ heterogeneous design techniques and propose a novel decoupled architecture for routers connected with memory controllers. To further improve performance, we propose techniques called Injection Virtual Circuit and Memory-aware Adaptive Routing. We show that our scheme can effectively eliminate NoC bottleneck and improve performance by 78% on average.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196087"}, {"primary_key": "3389019", "vector": [], "sparse_vector": [], "title": "An architecture-agnostic integer linear programming approach to CGRA mapping.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Coarse-grained reconfigurable architectures (CGRAs) have gained traction as a potential solution to implement accelerators for compute-intensive kernels, particularly in domains requiring hardware programmability. Architecture and CAD for CGRAs are tightly intertwined, with many prior works having combined architectures and tools. In this work, we present an architecture-agnostic integer linear programming (ILP) approach for CGRA mapping, integrated within an open-source CGRA architecture evaluation framework. The mapper accepts an application and an architecture description as input and can generate an optimal mapping, if indeed mapping is feasible. An experimental study demonstrates its effectiveness over a range of CGRA architectures.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195986"}, {"primary_key": "3389020", "vector": [], "sparse_vector": [], "title": "SOTERIA: exploiting process variations to enhance hardware security with photonic NoC architectures.", "authors": ["<PERSON> Chittamuru", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Photonic networks-on-chip (PNoCs) enable high bandwidth on-chip data transfers by using photonic waveguides capable of dense-wave-length-division-multiplexing (DWDM) for signal traversal and microring resonators (MRs) for signal modulation. A Hardware Trojan in a PNoC can manipulate the electrical driving circuit of its MRs to cause the MRs to snoop data from the neighboring wavelength channels in a shared photonic waveguide. This introduces a serious security threat. This paper presents a novel framework called SOTERIA† that utilizes process variation based authentication signatures along with architecture-level enhancements to protect data in PNoC architectures from snooping attacks. Evaluation results indicate that our approach can significantly enhance the hardware security in DWDM-based PNoCs with minimal overheads of up to 10.6% in average latency and of up to 13.3% in energy-delay-product (EDP).", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196118"}, {"primary_key": "3389021", "vector": [], "sparse_vector": [], "title": "Content addressable memory based binarized neural network accelerator using time-domain signal processing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jongsun Park"], "summary": "Binarized neural network (BNN) is one of the most promising solution for low-cost convolutional neural network acceleration. Since BNN is based on binarized bit-level operations, there exist great opportunities to reduce power-hungry data transfers and complex arithmetic operations. In this paper, we propose a content addressable memory (CAM) based BNN accelerator. By using time-domain signal processing, the huge convolution operations of BNN can be effectively replaced to the CAM search operation. In addition, thanks to fully parallel search of CAM, the parallel convolution operations for non-overlapped filtering window is enabled for high throughput data processing. To verify the effectiveness of the proposed CAM based BNN accelerator, the convolutional layer of LeNet-5 model has been implemented using 65nm CMOS technology. The implementation results show that the proposed BNN accelerator achieves 9.4% and 38.5% of area and energy savings, respectively. The parallel convolution operation of the proposed approach also shows 2.4x improved processing time.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196014"}, {"primary_key": "3389022", "vector": [], "sparse_vector": [], "title": "PlanarONoC: concurrent placement and routing considering crossing minimization for optical networks-on-chip.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Optical networks-on-chips (ONoCs) have become a promising solution for the on-chip communication of multi-and many-core systems to provide superior communication bandwidths, efficiency in power consumption, and latency performance compared to electronic NoCs. Serving as the critical part of ONoCs, an optical router composed of waveguides and photonic switching elements (PSEs) routes signals between two hubs or between a hub and a memory controller. Many studies focus on developing efficient architectures of optical routers, while their physical implementation that can seriously deteriorate the quality of the architectures is rarely addressed. The existing automatic place-and-route tools suffer from considerable insertion loss due to many waveguide crossings outside of PSEs, which leads to huge power consumption of laser sources. By observing that the logic schemes of most optical routers are actually planar, we develop a concurrent PSE placement and waveguide routing flow, called PlanarONoC, that guarantees optimal solutions in terms of crossings for planar logic schemes. Experimental results show that the proposed flow reduces the maximum insertion loss by 37% on average, guarantees no waveguide crossing outside of PSEs, and performs much more efficient compared to the state-of-the-art work.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196093"}, {"primary_key": "3389023", "vector": [], "sparse_vector": [], "title": "LEAD: learning-enabled energy-aware dynamic voltage/frequency scaling in NoCs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Network on Chips (NoCs) are the interconnect fabric of choice for multicore processors due to their superiority over traditional buses and crossbars in terms of scalability. While NoC's offer several advantages, they still suffer from high static and dynamic power consumption. Dynamic Voltage and Frequency Scaling (DVFS) is a popular technique that allows dynamic energy to be saved, but it can potentially lead to loss in throughput. In this paper, we propose LEAD - Learning-enabled Energy-Aware Dynamic voltage/frequency scaling for NoC architectures wherein we use machine learning techniques to enable energy-performance trade-offs at reduced overhead cost. LEAD enables a proactive energy management strategy that relies on an offline trained regression model and provides a wide variety of voltage/frequency pairs (modes). LEAD groups each router and the router's outgoing links locally into the same V/F domain, allowing energy management at a finer granularity without additional timing complications and overhead. Our simulation results using PARSEC and Splash-2 benchmarks on a 4 × 4 concentrated mesh architecture show an average dynamic energy savings of 17% with a minimal loss of 4% in throughput and no latency increase.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196068"}, {"primary_key": "3389024", "vector": [], "sparse_vector": [], "title": "Automated accelerator generation and optimization with composable, parallel and pipeline architecture.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "CPU-FPGA heterogeneous architectures feature flexible acceleration of many workloads to advance computational capabilities and energy efficiency in today's datacenters. This advantage, however, is often overshadowed by the poor programmability of FPGAs. Although recent advances in high-level synthesis (HLS) significantly improve the FPGA programmability, it still leaves programmers facing the challenge of identifying the optimal design configuration in a tremendous design space. In this paper we propose the composable, parallel and pipeline (CPP) microarchitecture as an accelerator design template to substantially reduce the design space. Also, by introducing the CPP analytical model to capture the performance-resource trade-offs, we achieve efficient, analytical-based design space exploration. Furthermore, we develop the AutoAccel framework to automate the entire accelerator generation process. Our experiments show that the AutoAccel-generated accelerators outperform their corresponding software implementations by an average of 72x for a broad class of computation kernels.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195999"}, {"primary_key": "3389025", "vector": [], "sparse_vector": [], "title": "A collaborative defense against wear out attacks in non-volatile processors.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "While the Internet of Things (IoT) keeps advancing, its full adoption is continually blocked by power delivery problems. One promising solution is Non-Volatile (NV) processors, which harvest energy for themselves and employ a NV memory hierarchy. This allows them to perform computations when power is available, checkpoint and hibernate when power is scarce, and resume their work at a later time. However, utilizing NV memory creates new security vulnerabilities in the form of wear out attacks in the register file. This paper explores the dangers of this design oversight and proposes a mitigation strategy that takes advantage of the unique properties and operating characteristics of NV processors. The proposed defense integrates the power management unit and a two-level register rotation approach, which improves NV processor endurance by 30.1x in attack situations and an average of 7.1x in standard workloads.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196825"}, {"primary_key": "3389026", "vector": [], "sparse_vector": [], "title": "Efficient computation of ECO patch functions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Engineering Change Orders (ECO) modify a synthesized netlist after its specification has changed. ECO is divided into two major tasks: finding target signals whose functions should be updated and synthesizing the patch that produces the desired change. This paper proposes an efficient SAT-based solution for the second task: resource-aware computation of multi-output patch functions. The solution is based on several new algorithms and outperforms the top three winners of the 2017 ICCAD CAD Contest (Problem A).", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196039"}, {"primary_key": "3389027", "vector": [], "sparse_vector": [], "title": "VRL-DRAM: improving DRAM performance via variable refresh latency.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A DRAM chip requires periodic refresh operations to prevent data loss due to charge leakage in DRAM cells. Refresh operations incur significant performance overhead as a DRAM bank/rank becomes unavailable to service access requests while being refreshed. In this work, our goal is to reduce the performance overhead of DRAM refresh by reducing the latency of a refresh operation. We observe that a significant number of DRAM cells can retain their data for longer than the worst-case refresh period of 64ms. Such cells do not always need to be fully refreshed; a low-latency partial refresh is sufficient for them.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196136"}, {"primary_key": "3389028", "vector": [], "sparse_vector": [], "title": "RAMP: resource-aware mapping for CGRAs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Coarse-grained reconfigurable array (CGRA) is a promising solution that can accelerate even non-parallel loops. Acceleration achieved through CGRAs critically depends on the goodness of mapping (of loop operations onto the PEs of CGRA), and in particular, the compiler's ability to route the dependencies among operations. Previous works have explored several mechanisms to route data dependencies, including, routing through other PEs, registers, memory, and even re-computation. All these routing options change the graph to be mapped onto PEs (often by adding new operations), and without re-scheduling, it may be impossible to map the new graph. However, existing techniques explore these routing options inside the Place and Route (P&R) phase of the compilation process, which is performed after the scheduling step. As a result, they either may not achieve the mapping or obtain poor results. Our method RAMP, explicitly and intelligently explores the various routing options, before the scheduling step, and makes improve the mapping-ability and mapping quality. Evaluating top performance-critical loops of MiBench benchmarks over 12 architectural configurations, we find that RAMP is able to accelerate loops by 23× over sequential execution, achieving a geomean speedup of 2.13× over state-of-the-art.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196101"}, {"primary_key": "3389029", "vector": [], "sparse_vector": [], "title": "DrAcc: a DRAM based accelerator for accurate CNN inference.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern Convolutional Neural Networks (CNNs) are computation and memory intensive. Thus it is crucial to develop hardware accelerators to achieve high performance as well as power/energy-efficiency on resource limited embedded systems. DRAM-based CNN accelerators exhibit great potentials but face inference accuracy and area overhead challenges.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196029"}, {"primary_key": "3389030", "vector": [], "sparse_vector": [], "title": "Modelling multicore contention on the AURIXTM TC27x.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Multicores are becoming ubiquitous in automotive. Yet, the expected benefits on integration are challenged by multicore contention concerns on timing V&V. Worst-case execution time (WCET) estimates are required as early as possible in the software development, to enable prompt detection of timing misbehavior. Factoring in multicore contention necessarily builds on conservative assumptions on interference, independent of co-runners load on shared hardware. We propose a contention model for automotive multicores that balances time-composability with tightness by exploiting available information on contenders. We tailor the model to the AURIX TC27x and provide tight WCET estimates using information from performance monitors and software configurations.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196077"}, {"primary_key": "3389032", "vector": [], "sparse_vector": [], "title": "On-chip deep neural network storage with multi-level eNVM.", "authors": ["<PERSON>", "<PERSON>", "Lillian Pentecost", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "One of the biggest performance bottlenecks of today's neural network (NN) accelerators is off-chip memory accesses [11]. In this paper, we propose a method to use multi-level, embedded nonvolatile memory (eNVM) to eliminate all off-chip weight accesses. The use of multi-level memory cells increases the probability of faults. Therefore, we co-design the weights and memories such that their properties complement each other and the faults result in no noticeable NN accuracy loss. In the extreme case, the weights in fully connected layers can be stored using a single transistor. With weight pruning and clustering, we show our technique reduces the memory area by over an order of magnitude compared to an SRAM baseline. In the case of VGG16 (130M weights), we are able to store all the weights in 4.9 mm2, well within the area allocated to SRAM in modern NN accelerators [6].", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196083"}, {"primary_key": "3389033", "vector": [], "sparse_vector": [], "title": "Extracting data parallelism in non-stencil kernel computing by optimally coloring folded memory conflict graph.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Irregular memory access pattern in non-stencil kernel computing renders the well-known hyperplane- [1], lattice- [2], or tessellation-based [3] HLS techniques ineffective. We develop an elegant yet effective technique that synthesizes memory-optimal architecture from high level software code in order to maximize application-specific data parallelism. Our basic idea is to exploit graph structures embedded in data access pattern and computation structure in order to perform the memory banking that maximizes parallel memory accesses while conserving both hardware and energy consumption. Specifically, we priority color a weighted conflict graph generated from folding the fundamental conflict graph to maximize memory conflict reduction. Most interestingly, our graph-based methodology enables a straightforward tradeoff between the number of memory banks and minimizing memory conflicts.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196088"}, {"primary_key": "3389034", "vector": [], "sparse_vector": [], "title": "Compiler-guided instruction-level clock scheduling for timing speculative processors.", "authors": ["Yuan<PERSON> Fan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Despite the significant promise that circuit-level timing speculation has for enabling operation in marginal conditions, overheads associated with recovery prove to be a serious drawback. We show that fine-grained clock adjustment guided by the compiler can be used to stretch and shrink the clock to maximize benefits of timing speculation and reduce the overheads associated with recovery. We present a formulation for compiler-driven clock scheduling and explore the benefits in several scenarios. Our results show that there are significant opportunities to exploit timing slack when there are appropriate channels for the compiler to select clock period at cycle-level.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196013"}, {"primary_key": "3389035", "vector": [], "sparse_vector": [], "title": "Obstacle-avoiding open-net connector with precise shortest distance estimation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "At the end of digital integrated circuit (IC) design flow, some nets may still be left open due to engineering change order (ECO). Resolving these opens could be quite challenging for some huge nets such as power ground nets because of a large number of obstacles and greatly distributed net components. Existing studies on multilayer obstacle-avoiding rectilinear Steiner trees may not be applicable to solve this problem because they assume the pins of an input net is a set of points, while the discrete net components in this problem can be regarded as a set of rectilinear pins. In this paper, we develop an efficient open-net connector that can deal with rectilinear pins. The proposed algorithm flow minimizes the total connection cost based on precise estimation of the shortest distance between each pair of rectilinear net components with the presence of complex obstacles. Experimental results show that the proposed flow can outperform the top three teams of 2017 CAD Contest at ICCAD in terms of total connection cost or runtime efficiency.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196081"}, {"primary_key": "3389036", "vector": [], "sparse_vector": [], "title": "Similarity-aware spectral sparsification by edge filtering.", "authors": ["<PERSON><PERSON>"], "summary": "In recent years, spectral graph sparsification techniques that can compute ultra-sparse graph proxies have been extensively studied for accelerating various numerical and graph-related applications. Prior nearly-linear-time spectral sparsification methods first extract low-stretch spanning tree from the original graph to form the backbone of the sparsifier, and then recover small portions of spectrally-critical off-tree edges to the spanning tree to significantly improve the approximation quality. However, it is not clear how many off-tree edges should be recovered for achieving a desired spectral similarity level within the sparsifier. Motivated by recent graph signal processing techniques, this paper proposes a similarity-aware spectral graph sparsification framework that leverages efficient spectral off-tree edge embedding and filtering schemes to construct spectral sparsifiers with guaranteed spectral similarity (relative condition number) level. An iterative graph densification scheme is introduced to facilitate efficient and effective filtering of off-tree edges for highly ill-conditioned problems. The proposed method has been validated using various kinds of graphs obtained from public domain sparse matrix collections relevant to VLSI CAD, finite element analysis, as well as social and data networks frequently studied in many machine learning and data mining applications.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196114"}, {"primary_key": "3389037", "vector": [], "sparse_vector": [], "title": "IAfinder: identifying potential implicit assumptions to facilitate validation in medical cyber-physical system.", "authors": ["<PERSON><PERSON>heng Fu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "According to the U.S. Food and Drug Administration (FDA) medical device recall database, medical device recalls are at an all-time high. One of the major causes of the recalls is due to implicit assumptions of which either the medical device operating environment does not match, or the device operators are not aware of. In this paper, we present IAFinder (Implicit Assumption Finder), a tool that uses data mining techniques to automatically extract invariants from design models implemented with statecharts. By identifying invariants that are not explicitly specified in the design models, we are able to find implicit assumptions and better facilitate domain experts to validate them and make the validated implicit assumptions explicit. We use a cardiac arrest statechart model as a case study to illustrate the usage of IAFinder in identifying implicit assumptions.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196062"}, {"primary_key": "3389038", "vector": [], "sparse_vector": [], "title": "Active forwarding: eliminate IOMMU address translation for accelerator-rich architectures.", "authors": ["<PERSON><PERSON>eh<PERSON><PERSON>", "Po<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Accelerator-rich architectures employ IOMMUs to support unified virtual address, but researches show that they fail to meet the performance and energy requirements of accelerators. Instead of optimizing the speed/energy of IOMMU address translation, this work tackles the issue from a new perspective, eliminating the need for translation with an active forwarding (AF) mechanism that forwards input data of accelerators directly from the CPU cache to the scratchpad memory of the accelerator. Results show that on average, AF can provide 8% performance improvement compared to the state-of-the-art mechanism, hostPageWalk, and reduce 22.1% accelerator power.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195984"}, {"primary_key": "3389039", "vector": [], "sparse_vector": [], "title": "Electro-magnetic analysis of GPU-based AES implementation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this work, for the first time, we investigate Electro-Magnetic (EM) attacks on GPU-based AES implementation. In detail, we first sample EM traces using a delicate trigger; then, we build a heuristic leakage model and a novel leakage model to exploit the simultaneous EM leakages in parallel scenarios. After that, we evaluate the effectiveness of EM attacks on GPU-based AES implementation. Our evaluation results show that GPU-based AES implementation is vulnerable to EM attacks. This work also suggests that GPU-based AES implementation needs to be protected against EM attacks in real scenarios.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196042"}, {"primary_key": "3389041", "vector": [], "sparse_vector": [], "title": "Inducing local timing fault through EM injection.", "authors": ["<PERSON><PERSON>", "Bilgiday Yuce", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Electromagnetic fault injection (EMFI) is an efficient class of physical attacks that can compromise the immunity of secure cryptographic algorithms. Despite successful EMFI attacks, the effects of electromagnetic injection (EM) on a processor are not well understood. This paper presents a bottom-up analysis of EMFI effects on a RISC microprocessor. We study these effects at three levels: at the wire-level, at the chip-network level, and at the gate-level considering parameters such as EM-injection location and timing. We conclude that EMFI induces local timing errors implying current timing attack detection and prevention techniques can be adapted to overcome EMFI.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196064"}, {"primary_key": "3389042", "vector": [], "sparse_vector": [], "title": "Runtime adjustment of IoT system-on-chips for minimum energy operation.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Energy-constrained Systems-on-Chips (SoC) are becoming major components of many emerging applications, especially in the Internet of Things (IoT) domain. Although the best energy efficiency is achieved when the SoC operates in the near-threshold region, the best operating point for maximum energy efficiency could vary depending on operating temperature, workload, and the power-gating state (power modes) of various SoC components at runtime. This paper presents a lightweight machine-learning based scheme to predict and tune the SoC to the most energy efficient supply voltage at the firmware level during runtime, considering the impacts of temperature variation and power-gating of SoC components while meeting the performance and reliability requirements. Simulation results indicate that the proposed method can determine the most energy efficient supply voltage of a circuit with high-accuracy (RMSE = 7mV), while considering the runtime performance and reliability constraints.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196108"}, {"primary_key": "3389043", "vector": [], "sparse_vector": [], "title": "Specification-driven automated conformance checking for virtual prototype and post-silicon designs.", "authors": ["Haifeng Gu", "Mingsong Chen", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Due to the increasing complexity of System-on-Chip (SoC) design, how to ensure that silicon implementations conform to their high-level specifications is becoming a major challenge. To address this problem, we propose a novel specification-driven conformance checking approach that can automatically identify inconsistencies between different levels of designs. By extending SystemRDL specifications, our approach enables the generation of high-level Formal Device Models (FDMs) that specify access behaviors of interface registers triggered by driver requests. Based on the symbolic execution of the generated FDMs with the same driver requests to virtual/silicon devices, our approach can efficiently check whether the designs of an SoC at different levels exhibit unexpected behaviors that are not modeled in the given specification. Experiments on two industrial network adapters demonstrate the effectiveness of our approach in troubleshooting bugs caused by inconsistencies in both virtual and post-silicon prototypes.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196119"}, {"primary_key": "3389044", "vector": [], "sparse_vector": [], "title": "SRAM based opportunistic energy efficiency improvement in dual-supply near-threshold processors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Energy-efficient microprocessors are essential for a wide range of applications. While near-threshold computing is a promising technique to improve energy efficiency, optimal supply demands from logic core and on-chip memory are conflicting. In this paper, we perform reliability analysis of 6T SRAM and discover imbalanced minimum voltage requirements between read and write operations. We leverage this imbalance property in near-threshold processors equipped with voltage boosting capability by proposing an opportunistic dual-supply switching scheme with a write aggregation buffer. Our results show that proposed technique improves energy efficiency by more than 18% with approximate 8.54% performance speed-up.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196121"}, {"primary_key": "3389045", "vector": [], "sparse_vector": [], "title": "STAFF: online learning with stabilized adaptive forgetting factor and feature selection algorithm.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Dynamic resource management techniques rely on power consumption and performance models to optimize the operating frequency and utilization of processing elements, such as CPU and GPU. Despite the importance of these decisions, many existing approaches rely on fixed power and performance models that are learned offline. However, offline models cannot guarantee accuracy when workloads differ significantly from the training available at design time. This paper presents an online learning framework (STAFF) that constructs adaptive run-time models for stationary and non-stationary workloads. STAFF is the first framework that (1) guarantees stability while quickly adapting to workload changes, (2) performs online feature selection with linear complexity, and (3) adapts to new model coefficients by employing adaptively varying forgetting factor, all at the same time. Experiments on an Intel® Coreh™ i5 6th generation platform demonstrate up to 6× improvement in the performance prediction accuracy compared to existing techniques.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196122"}, {"primary_key": "3389046", "vector": [], "sparse_vector": [], "title": "SAT based exact synthesis using DAG topology families.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "SAT based exact synthesis is a powerful technique, with applications in logic optimization, technology mapping, and synthesis for emerging technologies. However, its runtime behavior can be unpredictable and slow. In this paper, we propose to add a new type of constraint based on families of DAG topologies. Such families restrict the search space considerably and let us partition the synthesis problem in a natural way. Our approach shows significant reductions in runtime as compared to state-of-the-art implementations, by up to 63.43%. Moreover, our implementation has significantly fewer timeouts compared to baseline and reference implementations, and reduces this number by up to 61%. In fact, our topology based implementation dominates the others with respect to the number of solved instances: given a runtime bound, it solves at least as many instances as any other implementation.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196111"}, {"primary_key": "3389047", "vector": [], "sparse_vector": [], "title": "Resource-aware partitioned scheduling for heterogeneous multicore real-time systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Da<PERSON> Zhu"], "summary": "Heterogeneous multicore processors have become popular computing engines for modern embedded real-time systems recently. However, there is rather limited research on the scheduling of real-time tasks running on heterogeneous multicore systems with shared resources. Note that, different partitionings of tasks upon heterogeneous cores can affect the synchronization overheads of tasks (and thus the system schedulability). Focusing on the partitioned-EDF scheduling and resource access protocol MSRP (Multiprocessor Stack Resource Policy), this paper proposes an effective synchronization aware task partitioning algorithm for heterogeneous multicores (SATPA-HM). Several resource-oriented heuristics are exploited to tighten the bound on the synchronization costs of tasks through dynamic task prioritization and to find an appropriate core for each task that can minimize the system utilization increment. The simulation results show that our proposed SA-TPA-HM scheme can achieve higher acceptance ratio (e.g., 60% more), when compared to the existing schemes designed for homogeneous multicores.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196103"}, {"primary_key": "3389048", "vector": [], "sparse_vector": [], "title": "Bandwidth-efficient deep learning.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Deep learning algorithms are achieving increasingly higher prediction accuracy on many machine learning tasks. However, applying brute-force programming to data demands a huge amount of machine power to perform training and inference, and a huge amount of manpower to design the neural network models, which is inefficient. In this paper, we provide techniques to solve these bottlenecks: saving memory bandwidth for inference by model compression, saving networking bandwidth for training by gradient compression, and saving engineer bandwidth for model design by using AI to automate the design of models.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199847"}, {"primary_key": "3389050", "vector": [], "sparse_vector": [], "title": "BLASYS: approximate logic synthesis using boolean matrix factorization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Approximate computing is an emerging paradigm where design accuracy can be traded off for benefits in design metrics such as design area, power consumption or circuit complexity. In this work, we present a novel paradigm to synthesize approximate circuits using Boolean matrix factorization (BMF). In our methodology the truth table of a sub-circuit of the design is approximated using BMF to a controllable approximation degree, and the results of the factorization are used to synthesize a less complex subcircuit. To scale our technique to large circuits, we devise a circuit decomposition method and a subcircuit design-space exploration technique to identify the best order for subcircuit approximations. Our method leads to a smooth trade-off between accuracy and full circuit complexity as measured by design area and power consumption. Using an industrial strength design flow, we extensively evaluate our methodology on a number of testcases, where we demonstrate that the proposed methodology can achieve up to 63% in power savings, while introducing an average relative error of 5%. We also compare our work to previous works in Boolean circuit synthesis and demonstrate significant improvements in design metrics for same accuracy targets.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196001"}, {"primary_key": "3389051", "vector": [], "sparse_vector": [], "title": "Exact algorithms for delay-bounded steiner arborescences.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Rectilinear Steiner arborescences under linear delay constraints play an important role for buffering. We present exact algorithms for either minimizing the total length subject to delay constraints, or minimizing the total length plus the (weighted) absolute total negative slack.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196048"}, {"primary_key": "3389052", "vector": [], "sparse_vector": [], "title": "TRIG: hardware accelerator for inference-based applications and experimental demonstration using carbon nanotube FETs.", "authors": ["Gage Hills", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "H.<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The energy efficiency demands of future abundant-data applications, e.g., those which use inference-based techniques to classify large amounts of data, exceed the capabilities of digital systems today. Field-effect transistors (FETs) built using nanotechnologies, such as carbon nanotubes (CNTs), can improve energy efficiency significantly. However, carbon nanotube FETs (CNFETs) are subject to process variations inherent to CNTs: variations in CNT type (semiconductor or metallic), CNT density, or CNT diameter, to name a few. These CNT variations can degrade CNFET benefits at advanced technology nodes. One path to overcome CNT variations is to co-optimize CNT processing and CNFET circuit design; however, the required CNT process advancements have not been achieved experimentally. We present a new design approach (TRIG, Technique for Reducing errors using Iterative Gray code) to overcome process variations in hardware accelerators targeting inference-based applications that use serial matrix operations (serial: accumulated over at least 2 clock cycles). We demonstrate that TRIG can retain the major energy efficiency benefits (quantified using Energy Delay Product or EDP) of CNFETs despite CNT variations that exist in today's CNFET fabrication - without requiring further CNT processing improvements to overcome CNT variations. As a case study, we analyze the effectiveness of TRIG for a binary neural network hardware accelerator that classifies images. Despite CNT variations that exist today, TRIG can maintain 99% (90%) of projected EDP benefits of CNFET digital circuits for 90% (99%) image classification accuracy target. We also demonstrate experimentally fabricated CNFET circuits to compute scalar product (a common matrix operation, also called dot product), with and without TRIG: TRIG reduces the mean difference between the expected result (no errors) and the experimentally computed result by 30× in the presence of CNT variations, shown experimentally.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196132"}, {"primary_key": "3389053", "vector": [], "sparse_vector": [], "title": "Achieving defect-free multilevel 3D flash memories with one-shot program design.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To store the desired data on MLC and TLC flash memories, the conventional programming strategies need to divide a fixed range of threshold voltage (Vt) window into several parts. The narrowly partitioned Vt window in turn limits the design of programming strategy and becomes the main reason to cause flash-memory defects, i.e., the longer read/write latency and worse data reliability. This motivates this work to explore the innovative programming design for solving the flash-memory defects. Thus, to achieve the defect-free 3D NAND flash memory, this paper presents and realizes a one-shot program design to significantly eliminate the negative impacts caused by conventional programming strategies. The proposed one-shot program design includes two strategies, i.e., prophetic and classification programming, for MLC flash memories, and the idea is extended to TLC flash memories. The measurement results show that it can accelerate programming speed by 31x and reduce RBER by 1000x for the MLC flash memory, and it can broaden the available window of threshold voltage up to 5.1x for the TLC flash memory.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195982"}, {"primary_key": "3389054", "vector": [], "sparse_vector": [], "title": "Proactive channel adjustment to improve polar code capability for flash storage devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "With the low encoding/decoding complexity and the high error correction capability, polar code with the support of list-decoding and cyclic redundancy check can outperform LDPC code in the area of data communication. Thus, it also draws a lot of attentions on how to adopt and enable polar codes in storage applications. However, the code construction and encoding length limitation issues obstruct the adoption of polar codes in flash storage devices. To enable polar codes in flash storage devices, we propose a proactive channel adjustment design to extend the effective time of a code construction to improve the error correction capability of polar codes. This design pro-actively tunes the quality of the critical flash cells to maintain the correctness of the code construction and relax the constraint of the encoding length limitation, so that polar codes can be enabled in flash storage devices. A series of experiments demonstrates that the proposed design can effectively improve the error correction capability of polar codes in flash storage devices.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196095"}, {"primary_key": "3389056", "vector": [], "sparse_vector": [], "title": "HFMV: hybridizing formal methods and machine learning for verification of analog and mixed-signal circuits.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With increasing design complexity and robustness requirement, analog and mixed-signal (AMS) verification manifests itself as a key bottleneck. While formal methods and machine learning have been proposed for AMS verification, these two techniques suffer from their own limitations, with the former being specifically limited by scalability and the latter by the inherent uncertainty in learning-based models. We present a new direction in AMS verification by proposing a hybrid formal/machine-learning verification technique (HFMV) to combine the best of the two worlds. HFMV adds formalism on the top of a probabilistic learning model while providing a sense of coverage for extremely rare failure detection. HFMV intelligently and iteratively reduces uncertainty of the learning model by a proposed formally-guided active learning strategy and discovers potential rare failure regions in complex high-dimensional parameter spaces. It leads to reliable failure prediction in the case of a failing circuit, or a high-confidence pass decision in the case of a good circuit. We demonstrate that HFMV is able to employ a modest amount of data to identify hard-to-find rare failures which are completely missed by state-of-the-art sampling methods even with high volume sampling data.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196059"}, {"primary_key": "3389057", "vector": [], "sparse_vector": [], "title": "Reverse engineering convolutional neural networks through side-channel information leaks.", "authors": ["<PERSON>z<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A convolutional neural network (CNN) model represents a crucial piece of intellectual property in many applications. Revealing its structure or weights would leak confidential information. In this paper we present novel reverse-engineering attacks on CNNs running on a hardware accelerator, where an adversary can feed inputs to the accelerator and observe the resulting off-chip memory accesses. Our study shows that even with data encryption, the adversary can infer the underlying network structure by exploiting the memory and timing side-channels. We further identify the information leakage on the values of weights when a CNN accelerator performs dynamic zero pruning for off-chip memory accesses. Overall, this work reveals the importance of hiding off-chip memory access pattern to truly protect confidential CNN models.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196105"}, {"primary_key": "3389058", "vector": [], "sparse_vector": [], "title": "Using imprecise computing for improved non-preemptive real-time scheduling.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Sachin <PERSON>", "<PERSON>"], "summary": "Conventional hard real-time scheduling is often overly pessimistic due to the worst case execution time estimation. The pessimism can be mitigated by exploiting imprecise computing in applications where occasional small errors are acceptable. This leverage was previously investigated for preemptive scheduling. We study how to make use of imprecise computing in uniprocessor non-preemptive real-time scheduling, which is known to be more difficult than its preemptive counterpart. Several heuristic algorithms are developed for periodic tasks with independent or cumulative errors due to imprecision. Simulation results show that the proposed techniques can significantly improve task schedulability and achieve desired accuracy-schedulability tradeoff. The benefit is further confirmed by a prototyping implementation in Linux system.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196134"}, {"primary_key": "3389059", "vector": [], "sparse_vector": [], "title": "Formal security verification of concurrent firmware in SoCs using instruction-level abstraction for hardware.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Formal security verification of firmware interacting with hardware in modern Systems-on-Chip (SoCs) is a critical research problem. This faces the following challenges: (1) design complexity and heterogeneity, (2) semantics gaps between software and hardware, (3) concurrency between firmware/hardware and between Intellectual Property Blocks (IPs), and (4) expensive bit-precise reasoning. In this paper, we present a co-verification methodology to address these challenges. We model hardware using the Instruction-Level Abstraction (ILA), capturing firmware-visible behavior at the architecture level. This enables integrating hardware behavior with firmware in each IP into a single thread. The co-verification with multiple firmware across IPs is formulated as a multi-threaded program verification problem, for which we leverage software verification techniques. We also propose an optimization using abstraction to prevent expensive bit-precise reasoning. The evaluation of our methodology on an industry SoC Secure Boot design demonstrates its applicability in SoC security verification.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196055"}, {"primary_key": "3389060", "vector": [], "sparse_vector": [], "title": "RADAR: a 3D-reRAM based DNA alignment accelerator architecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Next Generation Sequencing (NGS) technology has become an indispensable tool for studying genomics, resulting in an exponentially growth of biological data. Booming data volume demands significant computational resources and creates challenges for 'Sequence Alignment', which is the most fundamental application in bioinformatics. Consequently, many researchers exploit both software and hardware methods to accelerate the most widely used sequence alignment algorithm - Basic Local Alignment Search Tool (BLAST). However, prior work suffers from moving huge DNA databases from the storage to computational units. Such data movement is both time and energy consuming.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196098"}, {"primary_key": "3389061", "vector": [], "sparse_vector": [], "title": "MAXelerator: FPGA accelerator for privacy preserving multiply-accumulate (MAC) on cloud servers.", "authors": ["Siam <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents MAXelerator, the first hardware accelerator for privacy-preserving machine learning (ML) on cloud servers. Cloud-based ML is being increasingly employed in various data sensitive scenarios. While it enhances both efficiency and quality of the service, it also raises concern about privacy of the users' data. We create a practical privacy-preserving solution for matrix-based ML on cloud servers. We show that for the majority of the ML applications, the privacy-sensitive computation boils down to either matrix multiplication, which is a repetition of Multiply-Accumulate (MAC) or the MAC itself. We design an FPGA architecture for privacy-preserving MAC to accelerate the ML computation based on the well known Secure Function Evaluation protocol named <PERSON>'s Garbled Circuit. MAXelerator demonstrates up to 57× improvement in throughput per core compared to the fastest existing GC framework. We corroborate the effectiveness of the accelerator with real-world case studies in privacy-sensitive scenarios.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196074"}, {"primary_key": "3389062", "vector": [], "sparse_vector": [], "title": "Hierarchical hyperdimensional computing for energy efficient classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Deqian Kong", "<PERSON><PERSON>"], "summary": "Brain-inspired Hyperdimensional (HD) computing emulates cognition tasks by computing with hypervectors rather than traditional numerical values. In HD, an encoder maps inputs to high dimensional vectors (hypervectors) and combines them to generate a model for each existing class. During inference, HD performs the task of reasoning by looking for similarities of the input hypervector and each pre-stored class hypervector However, there is not a unique encoding in HD which can perfectly map inputs to hypervectors. This results in low HD classification accuracy over complex tasks such as speech recognition. In this paper we propose MHD, a multi-encoder hierarchical classifier, which enables HD to take full advantages of multiple encoders without increasing the cost of classification. MHD consists of two HD stages: a main stage and a decider stage. The main stage makes use of multiple classifiers with different encoders to classify a wide range of input data. Each classifier in the main stage can trade between efficiency and accuracy by dynamically varying the hypervectors' dimensions. The decider stage, located before the main stage, learns the difficulty of the input data and selects an encoder within the main stage that will provide the maximum accuracy, while also maximizing the efficiency of the classification task. We test the accuracy/efficiency of the proposed MHD on speech recognition application. Our evaluation shows that MHD can provide a 6.6× improvement in energy efficiency and a 6.3× speedup, as compared to baseline single level HD.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196060"}, {"primary_key": "3389063", "vector": [], "sparse_vector": [], "title": "Compensated-DNN: energy efficient low-precision deep neural networks by compensating quantization errors.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Jung<PERSON><PERSON> Choi", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) represent the state-of-the-art in many Artificial Intelligence (AI) tasks involving images, videos, text, and natural language. Their ubiquitous adoption is limited by the high computation and storage requirements of DNNs, especially for energy-constrained inference tasks at the edge using wearable and IoT devices. One promising approach to alleviate the computational challenges is implementing DNNs using low-precision fixed point (<16 bits) representation. However, the quantization error inherent in any Fixed Point (FxP) implementation limits the choice of bit-widths to maintain application-level accuracy. Prior efforts recommend increasing the network size and/or re-training the DNN to minimize loss due to quantization, albeit with limited success.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196012"}, {"primary_key": "3389064", "vector": [], "sparse_vector": [], "title": "Dynamic vehicle software with AUTOCONT.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Future automotive software needs to deal with an increasing level of dynamicity, reasoned by the wish for connected driving, software updates, and dynamic feature activation. Such functionalities cannot be properly realized with today's classic AUTOSAR development approach, since it relies on the static configuration of all software units at build time.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196035"}, {"primary_key": "3389065", "vector": [], "sparse_vector": [], "title": "Calibrating process variation at system level with in-situ low-precision transfer learning for analog neural network processors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Process Variation (PV) may cause accuracy loss of the analog neural network (ANN) processors, and make it hard to be scaled down, as well as feasibility degrading. This paper first analyses the impact of PV on the performance of ANN chips. Then proposes an in-situ transfer learning method at system level to reduce PV's influence with low-precision back-propagation. Simulation results show the proposed method could increase 50% tolerance of operating point drift and 70% ∼ 100% tolerance of mismatch with less than 1% accuracy loss of benchmarks. It also reduces 66.7% memories and has about 50× energy-efficiency improvement of multiplication in the learning stage, compared with the conventional full-precision (32bit float) training system.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196004"}, {"primary_key": "3389066", "vector": [], "sparse_vector": [], "title": "Mamba: closing the performance gap in productive hardware development frameworks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Modern high-level languages bring compelling productivity benefits to hardware design and verification. For example, hardware generation and simulation frameworks (HGSFs) use a single \"host\" language for parameterization, static elaboration, test bench generation, behavioral modeling, and simulation. Unfortunately, HGSFs often suffer from slow simulator performance which undermines their potential productivity benefits. In this paper, we introduce Mamba, a new Python-based HGSF that co-optimizes both the framework and a general-purpose just-in-time compiler. We conduct a quantitative comparison of Mamba vs. traditional and emerging hardware development frameworks across both simple and complex designs. Our results suggest Mamba is able to match the performance of commercial Verilog simulators and is 10× faster than existing HGSFs while still maintaining the productivity of using a high-level language in hardware design.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196073"}, {"primary_key": "3389067", "vector": [], "sparse_vector": [], "title": "Context-aware dataflow adaptation technique for low-power multi-core embedded systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Today's embedded systems operate under increasingly dynamic conditions. First, computational workloads can be either fluctuating or adjustable. Moreover, as many devices are battery-powered, it is common to have runtime power management technique, which results in dynamic power budget. This paper presents a design methodology for multi-core systems, based on dataflow specification, that can deal with various contexts. We optimize the original dataflow considering various working conditions, then, autonomously adapt it to a pre-defined optimal form in response to context changes. We show the effectiveness of the proposed technique with a real-life case study and synthetic benchmarks.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196015"}, {"primary_key": "3389068", "vector": [], "sparse_vector": [], "title": "Reducing time and effort in IC implementation: a roadmap of challenges and solutions.", "authors": ["<PERSON>"], "summary": "To reduce time and effort in IC implementation, fundamental challenges must be solved. First, the need for (expensive) humans must be removed wherever possible. Humans are skilled at predicting downstream flow failures, evaluating key early decisions such as RTL floorplanning, and deciding tool/flow options to apply to a given design. Achieving human-quality prediction, evaluation and decision-making will require new machine learning-centric models of both tools and designs. Second, to reduce design schedule, focus must return to the long-held dream of single-pass design. Future design tools and flows that never require iteration (i.e., that never fail, but without undue conservatism) demand new paradigms and core algorithms for parallel, cloud-based design automation. Third, learning-based models of tools and flows must continually improve with additional design experiences. Therefore, the EDA and design ecosystem must develop new infrastructure for ML model development and sharing.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199854"}, {"primary_key": "3389069", "vector": [], "sparse_vector": [], "title": "Approximation-aware coordinated power/performance management for heterogeneous multi-cores.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Run-time resource management of heterogeneous multi-core systems is challenging due to i) dynamic workloads, that often result in ii) conflicting knob actuation decisions, which potentially iii) compromise on performance for thermal safety. We present a runtime resource management strategy for performance guarantees under power constraints using functionally approximate kernels that exploit accuracy-performance trade-offs within error resilient applications. Our controller integrates approximation with power knobs - DVFS, CPU quota, task migration - in coordinated manner to make performance-aware decisions on power management under variable workloads. Experimental results on Odroid XU3 show the effectiveness of this strategy in meeting performance requirements without power violations compared to existing solutions.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195994"}, {"primary_key": "3389070", "vector": [], "sparse_vector": [], "title": "NNsim: fast performance estimation based on sampled simulation of GPGPU kernels for neural networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Existent GPU simulators are too slow to use for neural networks implemented in GPUs. For fast performance estimation, we propose a novel hybrid method of analytical performance modeling and sampled simulation of GPUs. By taking full advantage of repeated computation of neural networks, three sampling techniques are devised: Inter-Kernel sampling, Intra-Kernel sampling, and Streaming Multiprocessor sampling. The key technique is to estimate the average IPC through sampled simulation, considering the effect of the warp scheduler and memory access contention. Compared with GPGPU-Sim, the proposed technique reduces the simulation time by up to 450 times with less than 5.0% of accuracy loss.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196079"}, {"primary_key": "3389072", "vector": [], "sparse_vector": [], "title": "Dynamic management of key states for reinforcement learning-assisted garbage collection to reduce long tail latency in SSD.", "authors": ["<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Garbage collection (GC) is one of main causes of the long-tail latency problem in storage systems. Long-tail latency due to GC is more than 100 times greater than the average latency at the 99th percentile. Therefore, due to such a long tail latency, real-time systems and quality-critical systems cannot meet the system requirements. In this study, we propose a novel key state management technique of reinforcement learning-assisted garbage collection. The purpose of this study is to dynamically manage key states from a significant number of state candidates. Dynamic management enables us to utilize suitable and frequently recurring key states at a small area cost since the full states do not have to be managed. The experimental results show that the proposed technique reduces by 22--25% the long-tail latency compared to a state-of-the-art scheme with real-world workloads.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196034"}, {"primary_key": "3389073", "vector": [], "sparse_vector": [], "title": "Dnestmap: mapping deeply-nested loops on ultra-low power CGRAs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Coarse-Grained Reconfigurable Arrays (CGRAs) provide high performance, energy-efficient execution of the innermost loops of an application. Most real-world applications, however, comprise of deeply-nested loops with complex and often irregular control flow structures that cannot be mapped to CGRAs by existing compilers. This leads to excessive data transfer costs as the execution continuously alternates between the outer loop-nests on the host processor and the innermost loop on the CGRA accelerator. Moreover, ultra-low power CGRAs can only include limited on-chip memory to cache the configuration bitstreams and need frequent swapping of configurations in the presence of multiple innermost loops. We introduce DNestMap, a partitioning and mapping tool for CGRAs, that can judiciously extract the most beneficial code segments of multiple deeply-nested loops and effectively cache them together statically in the configuration memory through spatio-temporal partitioning. DNestMap achieves 1.58X performance improvement compared to dynamic caching of configuration contexts of the innermost loops in the CGRAs with limited on-chip memory.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196027"}, {"primary_key": "3389074", "vector": [], "sparse_vector": [], "title": "A modular digital VLSI flow for high-productivity SoC design.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Yan<PERSON> Zhang", "<PERSON>"], "summary": "A high-productivity digital VLSI flow for designing complex SoCs is presented. The flow includes high-level synthesis tools, an object-oriented library of synthesizable SystemC and C++ components, and a modular VLSI physical design approach based on fine-grained globally asynchronous locally synchronous (GALS) clocking. The flow was demonstrated on a 16nm FinFET testchip targeting machine learning and computer vision.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199846"}, {"primary_key": "3389075", "vector": [], "sparse_vector": [], "title": "Aging-constrained performance optimization for multi cores.", "authors": ["<PERSON><PERSON>dr", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Circuit aging has become a dire design concern and hence it is considered a primary design constraint. Current practice to cope with this problem is to apply (too) conservative means.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195985"}, {"primary_key": "3389076", "vector": [], "sparse_vector": [], "title": "CamPUF: physically unclonable function based on CMOS image sensor fixed pattern noise.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Physically unclonable functions (PUFs) have proved to be an effective measure for secure device authentication and key generation. We propose a novel PUF design, named CamPUF, based on commercial off-the-shelf CMOS image sensors, which are ubiquitously available in almost all mobile devices. The inherent process mismatch between pixel sensors and readout circuits in an image sensor manifests as unique fixed pattern noise (FPN) in the image. We exploit FPN caused by dark signal non-uniformity (DSNU) as the basis for implementing the PUF. DSNU can be extracted only from dark images that are not shared with others, and only the legitimate user can obtain it with full control of the image sensor. Compared to other FPN components that can be extracted from shared images, DSNU facilitates more secure and usable device authentication. We present an efficient and reliable key generation procedure for use in wireless low-power devices. We implement CamPUF on Google Nexus 5X and Nexus 5 and evaluate the uniqueness and robustness of the keys, as well as its security against counterfeiting. We demonstrate that it discriminates legitimate and illegitimate authentication attempts without confusion.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196005"}, {"primary_key": "3389077", "vector": [], "sparse_vector": [], "title": "Optimized I/O determinism for emerging NVM-based NVMe SSD in an enterprise system.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Non-volatile memory express (NVMe) over peripheral component interconnect express (PCIe) has been adopted in the storage system to provide low latency and high throughput. NVMe allows a host system to reduce latency because it offers a high parallel operation and optimized command processing flow. In addition, an introduction of emerging non-volatile memory (NVM) significantly reduces the solid state drive (SSD) latency. The latency reduction in the host system and SSD makes a relative ratio of PCIe fabric latency to total I/O latency considerably grow. Therefore, this paper proposes a novel I/O optimization method using the PCIe feature, virtual channel. Unlike conventional approaches with the same priority data path, based on SSD's internal latency, an emerging NVM-based NVMe SSD with the proposed architecture selects a prioritized virtual channel to provide deterministic I/O latency. Experimental results show that the proposed method with phase-change memory (PCM) SSD improves I/O determinism by processing 45 ∼ 74% more commands within the predictable I/O latency than a conventional PCM SSD.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196085"}, {"primary_key": "3389078", "vector": [], "sparse_vector": [], "title": "Optimized selection of wireless network topologies and components via efficient pruning of feasible paths.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON>"], "summary": "We address the design space exploration of wireless networks to jointly select topology and component sizing. We formulate the exploration problem as an optimized mapping problem, where network elements are associated with components from pre-defined libraries to minimize a cost function under correctness guarantees. We express a rich set of system requirements as mixed integer linear constraints over path variables, denoting the presence or absence of paths between network nodes, and propose an algorithm for efficient, compact encoding of feasible paths that can reduce by orders of magnitude the complexity of the optimization problem. We incorporate our methods in a system-level design space exploration toolbox and evaluate their effectiveness on design examples from data collection and localization networks.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196086"}, {"primary_key": "3389079", "vector": [], "sparse_vector": [], "title": "Design and architectural co-optimization of monolithic 3D liquid state machine-based neuromorphic processor.", "authors": ["<PERSON>", "<PERSON>", "Yingyezhe Jin", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A liquid state machine (LSM) is a powerful recurrent spiking neural network shown to be effective in various learning tasks including speech recognition. In this work, we investigate design and architectural co-optimization to further improve the area-energy efficiency of LSM-based speech recognition processors with monolithic 3D IC (M3D) technology. We conduct fine-grained tier partitioning, where individual neurons are folded, and explore the impact of shared memory architecture and synaptic model complexity on the power-performance-area-accuracy (PPAA) benefit of M3D LSM-based speech recognition. In training and classification tasks using spoken English letters, we obtain up to 70.0% PPAA savings over 2D ICs.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196024"}, {"primary_key": "3389080", "vector": [], "sparse_vector": [], "title": "Co-design of deep neural nets and neural net accelerators for embedded vision applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Deep Learning is arguably the most rapidly evolving research area in recent years. As a result it is not surprising that the design of state-of-the-art deep neural net models proceeds without much consideration of the latest hardware targets, and the design of neural net accelerators proceeds without much consideration of the characteristics of the latest deep neural net models. Nevertheless, in this paper we show that there are significant improvements available if deep neural net models and neural net accelerators are co-designed.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199849"}, {"primary_key": "3389081", "vector": [], "sparse_vector": [], "title": "Hypernel: a hardware-assisted framework for kernel protection without nested paging.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Junmo Park", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Large OS kernels always suffer from attacks due to their numerous inherent vulnerabilities. To protect the kernel, hypervisors have been employed by many security solutions. However, relying on a hypervisor has a detrimental impact on the system performance due mainly to nested paging. In this paper, we present Hypernel, a security framework combining hardware and software components to address this problem. Hypersec, the software component, provides an isolated execution environment for security solutions, and the hardware monitor component enables a word-granularity monitoring capability on the kernel memory. Our evaluation shows that Hypernel efficiently fulfills the role of a security framework, while imposing mere 3.1% of runtime overhead on the system.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196061"}, {"primary_key": "3389082", "vector": [], "sparse_vector": [], "title": "Routability-driven and fence-aware legalization for mixed-cell-height circuits.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>. <PERSON>", "<PERSON><PERSON>"], "summary": "Placement is one of the most critical stages in the physical synthesis flow. Circuits with increasing numbers of cells of multi-row height have brought challenges to traditional placers on efficiency and effectiveness. Furthermore, constraints on fence region and routability (e.g., edge spacing, pin access/short) should be considered, besides providing an overlap-free solution close to the global placement (GP) solution and fulfilling the power and ground (P/G) alignments. In this paper, we propose a legalization method for mixed-cell-height circuits by a window-based cell insertion technique and two post-processing network-flow-based optimizations. Compared with the champion of the IC/CAD 2017 Contest, our algorithm achieves 18% and 12% less average and maximum displacement respectively as well as significantly fewer routability violations. Comparing our algorithm with the state-of-the-art algorithms on this problem, there is a 9% improvement in total displacement with 20% less running time.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196107"}, {"primary_key": "3389083", "vector": [], "sparse_vector": [], "title": "Dadu-P: a scalable accelerator for robot motion planning in a dynamic environment.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As a critical operation in robotics, motion planning consumes lots of time and energy, especially in a dynamic environment. Through approaches based on general-purpose processors, it is hard to get a valid planning in real time. We present an accelerator to speed up collision detection, which costs over 90% of the computation time in motion planning. Via the octree-based roadmap representation, the accelerator can be reconfigured online and support large roadmaps. We in addition propose an effective algorithm to update the roadmap in a dynamic environment, together with a batched incremental processing approach to reduce the complexity of collision detection. Experimental results show that our accelerator achieves 26.5X speedup than an existing CPU-based approach. With the incremental approach, the performance further improves by 10X while the solution quality is degraded by 10% only.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196020"}, {"primary_key": "3389084", "vector": [], "sparse_vector": [], "title": "Duet: an OLED &amp; GPU co-management scheme for dynamic resolution adaptation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The increasingly high display resolution of mobile devices imposes a further burden on energy consumption. Existing schemes manage either OLED or GPU power to save energy. This paper presents the design, algorithm, and implementation of a co-managing scheme called Duet, which automatically trades off perceptual quality for energy efficiency in accordance with static and dynamic visual acuity when users interact with mobile applications. The results of experiments conducted on a commercial smartphone with popular interactive apps show that Duet saves more energy while retaining better visual quality, compared with a joint scheme that simultaneously uses dynamic pixel disabling and dynamic resolution scaling to save OLED and GPU energy in isolation.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196031"}, {"primary_key": "3389085", "vector": [], "sparse_vector": [], "title": "LCP: a layer clusters paralleling mapping method for accelerating inception and residual networks on FPGA.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xiangyu Li", "<PERSON><PERSON><PERSON>"], "summary": "Deep convolutional neural networks (DCNNs) have been widely used in various AI applications. Inception and Residual are two promising structures adopted in many important modern DCNN models, including AlphaGo Zero's model. These structures allow considerably increasing the depth and width of the network to improve accuracy, without increasing the computational budget or the difficulty of convergence. Various accelerators for DCNNs have been proposed based on FPGA platform because it has advantages of high performance, good power efficiency, and fast development round, etc. However, previous FPGA mapping methods cannot fully adapt to the different data localities among layers and other characteristics of Inception and Residual, which leads to a under-utilization of FPGA resources. We propose LCP, a Layer Clusters Paralleling mapping method to classify the layers into clusters based on their differences of parameters and data localities, and then accelerate them in different partitions of FPGA. We evaluate our mapping method by implementing Inception/Residual modules from GoogLeNet [8] and ResNet-50 [4] on Xilinx VC709 (Virtex 690T) FPGA. The results show that the proposed method fully utilizes resources and achieves up to 4.03× performance than the baseline and 2.00× performance than the state-of-the-art methods.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196067"}, {"primary_key": "3389087", "vector": [], "sparse_vector": [], "title": "Towards accurate and high-speed spiking neuromorphic systems with data quantization-aware deep networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) have gained immense success in cognitive applications and greatly pushed today's artificial intelligence forward. The biggest challenge in executing DNNs is their extremely data-extensive computations. The computing efficiency in speed and energy is constrained when traditional computing platforms are employed in such computational hungry executions. Spiking neuromorphic computing (SNC) has been widely investigated in deep networks implementation own to their high efficiency in computation and communication. However, weights and signals of DNNs are required to be quantized when deploying the DNNs on the SNC, which results in unacceptable accuracy loss. %However, the system accuracy is limited by quantizing data directly in deep networks deployment. Previous works mainly focus on weights discretize while inter-layer signals are mainly neglected. In this work, we propose to represent DNNs with fixed integer inter-layer signals and fixed-point weights while holding good accuracy. We implement the proposed DNNs on the memristor-based SNC system as a deployment example. With 4-bit data representation, our results show that the accuracy loss can be controlled within 0.02% (2.3%) on MNIST (CIFAR-10). Compared with the 8-bit dynamic fixed-point DNNs, our system can achieve more than 9.8x speedup, 89.1% energy saving, and 30% area saving.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196131"}, {"primary_key": "3389088", "vector": [], "sparse_vector": [], "title": "Design-for-testability for continuous-flow microfluidic biochips.", "authors": ["<PERSON><PERSON> Liu", "<PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Flow-based microfluidic biochips are gaining traction in the microfluidics community since they enable efficient and low-cost biochemical experiments. These highly integrated lab-on-a-chip systems, however, suffer from manufacturing defects, which cause some chips to malfunction. To test biochips after manufacturing, air pressure is applied to input ports of a chip and predetermined test vectors are used to change the states of microvalves in the chip. Pressure meters are connected to the output ports to measure pressure values, which are compared with expected values to detect errors. To reduce the cost of the test platform, the number of pressure sources and meters should be reduced. We propose a design-for-testability (DFT) technique that enables a test procedure with only a single pressure source and a single pressure meter. Furthermore, the valves inserted for DFT share control channels with valves in the original chip so that no additional control signals are required. Simulation results demonstrate that this technique can generate efficient chip architectures for single-source single-meter test in all experiment cases successfully to reduce test cost, while the performance of these chips in executing applications is still maintained.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196025"}, {"primary_key": "3389089", "vector": [], "sparse_vector": [], "title": "DeepN-JPEG: a deep neural network favorable JPEG-based image compression framework.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "As one of most fascinating machine learning techniques, deep neural network (DNN) has demonstrated excellent performance in various intelligent tasks such as image classification. DNN achieves such performance, to a large extent, by performing expensive training over huge volumes of training data. To reduce the data storage and transfer overhead in smart resource-limited Internet-of-Thing (IoT) systems, effective data compression is a \"must-have\" feature before transferring real-time produced dataset for training or classification. While there have been many well-known image compression approaches (such as JPEG), we for the first time find that a human-visual based image compression approach such as JPEG compression is not an optimized solution for DNN systems, especially with high compression ratios. To this end, we develop an image compression framework tailored for DNN applications, named \"DeepN-JPEG\", to embrace the nature of deep cascaded information process mechanism of DNN architecture. Extensive experiments, based on \"ImageNet\" dataset with various state-of-the-art DNNs, show that \"DeepN-JPEG\" can achieve ∼ 3.5× higher compression rate over the popular JPEG solution while maintaining the same accuracy level for image recognition, demonstrating its great potential of storage and power efficiency in DNN-based smart IoT system design.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196022"}, {"primary_key": "3389090", "vector": [], "sparse_vector": [], "title": "Parallelizing SRAM arrays with customized bit-cell for binary neural networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Recent advances in deep neural networks (DNNs) have shown Binary Neural Networks (BNNs) are able to provide a reasonable accuracy on various image datasets with a significant reduction in computation and memory cost. In this paper, we explore two BNNs: hybrid BNN (HBNN) and XNOR-BNN, where the weights are binarized to +1/-1 while the neuron activations are binarized to 1/0 and +1/-1 respectively. Two SRAM bit cell designs are proposed, namely, 6T SRAM for HBNN and customized 8T SRAM for XNOR-BNN. In our design, the high-precision multiply-and-accumulate (MAC) is replaced by bitwise multiplication for HBNN or XNOR for XNOR-BNN plus bit-counting operations. To parallelize the weighted sum operation, we activate multiple word lines in the SRAM array simultaneously and digitize the analog voltage developed along the bit line by a multi-level sense amplifier (MLSA). In order to partition the large matrices in DNNs, we investigate the impact of sensing bit-levels of MLSA on the accuracy degradation for different sub-array sizes and propose using the nonlinear quantization technique to mitigate the accuracy degradation. With 64×64 sub-array size and 3-bit MLSA, HBNN and XNOR-BNN architectures can minimize the accuracy degradation to 2.37% and 0.88%, respectively, for an inspired VGG-16 network on the CIFAR-10 dataset. Design space exploration of SRAM based synaptic architectures with the conventional row-by-row access scheme and our proposed parallel access scheme are also performed, showing significant benefits in the area, latency and energy-efficiency. Finally, we have successfully taped-out and validated the proposed HBNN and XNOR-BNN designs in TSMC 65 nm process with measured silicon data, achieving energy-efficiency >100 TOPS/W for HBNN and >50 TOPS/W for XNOR-BNN.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196089"}, {"primary_key": "3389091", "vector": [], "sparse_vector": [], "title": "OPERON: optical-electrical power-efficient route synthesis for on-chip signals.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "As VLSI technology scales to deep sub-micron, optical interconnect becomes an attractive alternative for on-chip communication. The traditional optical routing works mainly optimize the path loss, and few works explicitly exploit the optical-electrical co-design of on-chip interconnects. To overcome these limitations, we present an efficient framework that directs the hybrid optical and electrical routes with a global view of power optimization. In this framework, on-chip signal bits are processed as hyper nets; the combination of optical and electrical routes are designed for hyper nets; then a formulation is given to find the appropriate solution of each hyper net and follows a speed-up algorithm; a min-cost max-flow network is utilized to reduce the consumed optical waveguides. Experimental results demonstrate the effectiveness of the proposed framework.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196084"}, {"primary_key": "3389092", "vector": [], "sparse_vector": [], "title": "SpWA: an efficient sparse winograd convolutional neural networks accelerator on FPGAs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "FPGAs have been an efficient accelerator for CNN inference due to its high performance, flexibility, and energy-efficiency. To improve the performance of CNNs on FPGAs, fast algorithms and sparse methods emerge as the most attractive alternatives, which can effectively reduce the complexity of CNNs. Using fast algorithms, the feature maps are transformed to special domain to reduce the arithmetic complexity. On the other hand, compressing CNN models by pruning the unimportant connections reduces both storage and arithmetic complexity.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196120"}, {"primary_key": "3389093", "vector": [], "sparse_vector": [], "title": "WB-trees: a meshed tree representation for FinFET analog layout designs.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The emerging design requirements with the FinFET technology, along with traditional geometrical constraints, make the FinFET-based analog placement even more challenging. Previous works can handle only partial FinFET-induced design constraints because some new constraints are intrinsically different from the traditional ones; as a result, directly extending previous methods to handle FinFET-induced constraints would incur solution quality degradation and runtime overhead. To remedy these disadvantages, we present a new hybrid graph (meshed tree) representation of a window mesh and CB-trees (namely, WB-trees) and a new placement flow with effective and efficient schemes to simultaneously handle FinFET-based design constraints and traditional ones. Experimental results based on industrial designs with various constraints show that our placer outperforms published works in both solution quality and runtime.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196137"}, {"primary_key": "3389094", "vector": [], "sparse_vector": [], "title": "COSAT: congestion, obstacle, and slew aware tree construction for multiple power domain design.", "authors": ["<PERSON>en<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Slew fixing, which ensures correct signal propagation, is essential during timing closure of IC design flow. Conventionally, gate sizing, Vt swapping, or buffer insertion is adopted to locally fix the slew violation on a single gate. Nevertheless, when slew violations are caused by congestion, obstacles, or excessive loadings (e.g., high-fanout nets or long wires), only smart buffering with a global view can fix them. Therefore, in this paper, we propose congestion, obstacle, and slew aware buffered tree construction for excessive loading nets in modern multiple power domain designs. We iteratively cluster sinks into groups by diamond covering and construct Steiner minimal trees. We globally maintain a congestion and obstacle grid map to guide fast grid routing to locate buffers, while avoiding congested regions and obstacles without timing degradation. Our experiments are conducted on seven industrial smartphone designs with TSMC 16/10nm process. Compared with the conventional buffer insertion approach (widely adopted by commercial tools), the minimal chain based approach can reduce 17% buffer count, decrease 14% leakage, and achieve 44% runtime speedup, but incur unwanted timing, design rule, power rule, and routing violations. Our approach can reduce 18% buffer count, decrease 21% leakage, and achieve 92% runtime speedup, while significantly reducing timing, design rule, power rule, and routing short violations. Our results show that our approach is promising for slew fixing on excessive loading nets in modern multiple domain designs.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196016"}, {"primary_key": "3389095", "vector": [], "sparse_vector": [], "title": "Noise-aware DVFS transition sequence optimization for battery-powered IoT devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Low power system-on-chips (SoCs) are now at the heart of Internet-of-Things (IoT) devices, which are well known for their bursty workloads and limited energy storage --- usually in the form of tiny batteries. To ensure battery lifetime, DVFS has become an essential technique in such SoC chips. With continuously decreasing supply level, noise margins in these devices are already being squeezed. During DVFS transition, large current that accompanies the clock speed transition runs into or out of clock networks in a few clock cycles, and induces large Ldi/dt noise, thereby stressing the power delivery network (PDN). Due to the limited area and cost target, adding additional decap to mitigate such noise is usually challenging. A common approach is to gradually introduce/remove the additional clock cycles to increase or reduce the clock frequency in steps, a.k.a., clock skipping. However, such a technique may increase DVFS transition time, and still cannot guarantee minimal noise. In this work, we propose a new noise-aware DVFS sequence optimization technique by formulating a mixed 0/1 programming to resolve the problems of clock skipping sequence optimization. Moreover, the method is also extended to schedule extensive wake-up activities on different clock domains for the same purpose. The results show that we are able to achieve minimal-noise sequence within desired transition time with 53% noise reduction and save more than 15--17% power compared with the traditional approach.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196080"}, {"primary_key": "3389096", "vector": [], "sparse_vector": [], "title": "Multi-objective bayesian optimization for analog/RF circuit synthesis.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Chang<PERSON> Yan", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, a novel multi-objective Bayesian optimization method is proposed for the sizing of analog/RF circuits. The proposed approach follows the framework of Bayesian optimization to balance the exploitation and exploration. Gaussian processes (GP) are used as the online surrogate models for the multiple objective functions. The lower confidence bound (LCB) functions are taken as the acquisition functions to select the data point with best Pareto-dominance and diversity. A modified non-dominated sorting based evolutionary multi-objective algorithm is proposed to find the Pareto Front (PF) of the multiple LCB functions, and the next simulation point is chosen from the PF of the multiple LCB functions. Compared with the multi-objective evolutionary algorithms (MOEA) and the state-of-the-art online surrogate model based circuit optimization method, our method can better approximate the Pareto Front while significantly reduce the number of circuit simulations.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196078"}, {"primary_key": "3389097", "vector": [], "sparse_vector": [], "title": "An ultra-low energy internally analog, externally digital vector-matrix multiplier based on NOR flash memory technology.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Vector-matrix multiplication (VMM) is a core operation in many signal and data processing algorithms. Previous work showed that analog multipliers based on nonvolatile memories have superior energy efficiency as compared to digital counterparts at low-to-medium computing precision. In this paper, we propose extremely energy efficient analog mode VMM circuit with digital input/output interface and configurable precision. Similar to some previous work, the computation is performed by gate-coupled circuit utilizing embedded floating gate (FG) memories. The main novelty of our approach is an ultra-low power sensing circuitry, which is designed based on translinear Gilbert cell in topological combination with a floating resistor and a low-gain amplifier. Additionally, the digital-to-analog input conversion is merged with VMM, while current-mode algorithmic analog-to-digital circuit is employed at the circuit backend. Such implementations of conversion and sensing allow for circuit operation entirely in a current domain, resulting in high performance and energy efficiency. For example, post-layout simulation results for 400×400 5-bit VMM circuit designed in 55 nm process with embedded NOR flash memory, show up to 400 MHz operation, 1.68 POps/J energy efficiency, and 39.45 TOps/mm2 computing throughput. Moreover, the circuit is robust against process-voltage-temperature variations, in part due to inclusion of additional FG cells that are utilized for offset compensation.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195989"}, {"primary_key": "3389098", "vector": [], "sparse_vector": [], "title": "An efficient timestamp-based monitoring approach to test timing constraints of cyber-physical systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Formal specifications on temporal behavior of Cyber-Physical Systems (CPS) is essential for verification of performance and safety. Existing solutions for verifying the satisfaction of temporal constraints on a CPS are compute and resource intensive since they require buffering signals from the CPS prior to constraint checking. We present an online approach, based on Timestamp Temporal Logic (TTL), for monitoring the timing constraints in CPS. The approach reduces the computation and memory requirements by processing the timestamps of pertinent events reducing the need to capture the full data set from the signal sampling. The signal buffer size bears a geometric relationship to the dimension of the signal vector, the time interval being considered, and the sampling resolution. Since monitoring logic is typically implemented on Field Programmable Gate Arrays (FPGAs) for efficient monitoring of multiple signals simultaneously, the space required to store the buffered data becomes the limiting resource. The monitoring logic, for the timing constraints on the Flying Paster (a printing application requiring synchronization between two motors), is illustrated in this paper to demonstrate a geometric reduction in memory and computational resources in the realization of an online monitor.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196130"}, {"primary_key": "3389099", "vector": [], "sparse_vector": [], "title": "Revisiting context-based authentication in IoT.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>. <PERSON>"], "summary": "The emergence of IoT poses new challenges towards solutions for authenticating numerous very heterogeneous IoT devices to their respective trust domains. Using passwords or pre-defined keys have drawbacks that limit their use in IoT scenarios. Recent works propose to use contextual information about ambient physical properties of devices' surroundings as a shared secret to mutually authenticate devices that are co-located, e.g., the same room. In this paper, we analyze these context-based authentication solutions with regard to their security and requirements on context quality. We quantify their achievable security based on empirical real-world data from context measurements in typical IoT environments.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196106"}, {"primary_key": "3389100", "vector": [], "sparse_vector": [], "title": "Measurement-based cache representativeness on multipath programs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Autonomous vehicles in embedded real-time systems increase critical-software size and complexity whose performance needs are covered with high-performance hardware features like caches, which however hampers obtaining WCET estimates that hold valid for all program execution paths. This requires assessing that all cache layouts have been properly factored in the WCET process. For measurement-based timing analysis, the most common analysis method, we provide a solution to achieve cache representativeness and full path coverage: we create a modified program for analysis purposes where cache impact is upper-bounded across any path, and derive the minimum number of runs required to capture in the test campaign cache layouts resulting in high execution times.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196075"}, {"primary_key": "3389101", "vector": [], "sparse_vector": [], "title": "Canonical computation without canonical representation.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "A representation of a Boolean function is canonical if, given a variable order, only one instance of the representation is possible for the function. A computation is canonical if the result depends only on the Boolean function and a variable order, and does not depend on how the function is represented and how the computation is implemented.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196006"}, {"primary_key": "3389102", "vector": [], "sparse_vector": [], "title": "Enhancing workload-dependent voltage scaling for energy-efficient ultra-low-power embedded systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Ultra-low-power (ULP) chipsets are in higher demand than ever due to the proliferation of ULP embedded systems to support growing applications like the Internet of Things (IoT), wearables and sensor networks. Since ULP systems are also cost constrained, they tend to employ general purpose processors (GPPs) rather than more energy-efficient ASICs, even though they typically run a single application for the lifetime of the system. Prior work showed that it is possible to reduce the operating voltage and thus the power of such systems without reducing the frequency, since the fixed software stack of a system typically only exercises a subset of a processor's paths, and unexercised paths need not meet timing constraints for the system to work correctly. In this context, we find additional scope for power reduction by intelligently optimizing the processor design based on the system's application-specific activity characteristics to allow an even lower safe operating voltage. We demonstrate automated techniques that maximize the application-specific voltage reduction for a system, resulting in 35% additional power savings, on average, compared to the application-specific minimum voltage before optimization and 48% total power savings compared to the original design at nominal voltage.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196046"}, {"primary_key": "3389103", "vector": [], "sparse_vector": [], "title": "PULP-HD: accelerating brain-inspired high-dimensional computing on a parallel ultra-low power platform.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Computing with high-dimensional (HD) vectors, also referred to as hypervectors, is a brain-inspired alternative to computing with scalars. Key properties of HD computing include a well-defined set of arithmetic operations on hypervectors, generality, scalability, robustness, fast learning, and ubiquitous parallel operations. HD computing is about manipulating and comparing large patterns---binary hypervectors with 10,000 dimensions---making its efficient realization on minimalistic ultra-low-power platforms challenging. This paper describes HD computing's acceleration and its optimization of memory accesses and operations on a silicon prototype of the PULPv3 4-core platform (1.5 mm2, 2 mW), surpassing the state-of-the-art classification accuracy (on average 92.4%) with simultaneous 3.7× end-to-end speed-up and 2× energy saving compared to its single-core execution. We further explore the scalability of our accelerator by increasing the number of inputs and classification window on a new generation of the PULP architecture featuring bit-manipulation instruction extensions and larger number of 8 cores. These together enable a near ideal speed-up of 18.4× compared to the single-core PULPv3.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196096"}, {"primary_key": "3389104", "vector": [], "sparse_vector": [], "title": "Cross-layer dependency analysis with timing dependence graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present Non-interference Analysis as a model-based method to automatically reveal, track and analyze end-to-end timing dependencies as part of a cross-layer dependency analysis in complex systems. Based on revealed timing dependencies of functional cause-effect chains, this method enables an automated FMEA inspection of timing behavior of individual functions. In consequence, this method can support safety-critical design processes w.r.t. the technical safety concept as mandated by safety standards such as ISO 26262. Our case-study from a state-of-the-art automated research vehicle and synthetic experiments confirm the applicability and scaleability of the proposed method.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196018"}, {"primary_key": "3389105", "vector": [], "sparse_vector": [], "title": "Automated interpretation and reduction of in-vehicle network traces at a large scale.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In modern vehicles, high communication complexity requires cost-effective integration tests such as data-driven system verification with in-vehicle network traces. With the growing amount of traces, distributable Big Data solutions for analyses become essential to inspect massive amounts of traces. Such traces need to be processed systematically using automated procedures, as manual steps become infeasible due to loading and processing times in existing tools. Further, trace analyses require multiple domains to verify the system in terms of different aspects (e.g., specific functions) and thus, require solutions that can be parameterized towards respective domains. Existing solutions are not able to process such trace amounts in a flexible and automated manner. To overcome this, we introduce a fully automated and parallelizable end-to-end preprocessing framework that allows to analyze massive in-vehicle network traces. Being parameterized per domain, trace data is systematically reduced and extended with domain knowledge, yielding a representation targeted towards domain-specific system analyses. We show that our approach outperforms existing solutions in terms of execution time and extensibility by evaluating our approach on three real-world data sets from the automotive industry.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196000"}, {"primary_key": "3389106", "vector": [], "sparse_vector": [], "title": "Edge-cloud collaborative processing for intelligent internet of things: a case study on smart surveillance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Limited processing power and memory prevent realization of state of the art algorithms on the edge level. Offloading computations to the cloud comes with tradeoffs as compression techniques employed to conserve transmission bandwidth and energy adversely impact accuracy of the algorithm. In this paper, we propose collaborative processing to actively guide the output of the sensor to improve performance on the end application. We apply this methodology to smart surveillance specifically the task of object detection from video. Perceptual quality and object detection performance is characterized and improved under a variety of channel conditions.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196036"}, {"primary_key": "3389107", "vector": [], "sparse_vector": [], "title": "Semi-automatic safety analysis and optimization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The complexity of safety-critical E/E-systems within the automotive domain are continuously increasing. At the same time, functional safety standards such as the ISO 26262 prescribe analysis methods like the Fault Tree Analysis (FTA) and Failure Mode and Effects Analysis (FMEA). Currently, these analysis methods are mainly performed manually and are often not consistent with an evolving system model.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199857"}, {"primary_key": "3389108", "vector": [], "sparse_vector": [], "title": "A measurement system for capacitive PUF-based security enclosures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Battery-backed security enclosures that are permanently monitored for penetration and tampering are common solutions for providing physical integrity to multi-chip embedded systems. This paper presents a well-tailored measurement system for a batteryless PUF-based capacitive enclosure. The key is derived from the PUF and encrypts the underlying system. We present a system concept for combined enclosure integrity verification and PUF evaluation. The system performs differential capacitive measurements inside the enclosure by applying stimulus signals with a 180° phase shift that isolate the local variation in the femtofarad range. The analog circuitry and corresponding digital signal processing chain perform precise PUF digitization, using a microcontroller-based digital lock-in amplifier. The system's measurement range is approximately ±73 fF, the conversion time per PUF node is less than 0.6 ms, and the raw data shows a measurement noise of 0.3 fF. This is the base for a high-entropy key generation while enabling a short system startup time. The system is scalable to the enclosure size and has been experimentally verified to extract information from 128 PUF nodes, using a system prototype. The results show that our concept forms a cornerstone of a novel batteryless PUF-based security enclosure.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195976"}, {"primary_key": "3389109", "vector": [], "sparse_vector": [], "title": "Employing classification-based algorithms for general-purpose approximate computing.", "authors": ["Geraldo F. Oliveira", "Larissa Rozales Gonçalves", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Approximate computing has recently reemerged as a design solution for additional performance and energy improvements at the cost of output quality. In this paper, we propose using a tree-based classification algorithm as an approximation tool for general-purpose applications. We show that, without any hardware support, completely implemented in software, our approach can improve performance by up to 4x (1.95x on average) and reduce EDP by up to 19x (4.04 on average) when compared to precise executions. Besides that, in some cases, our software-based mechanism can even outperform traditional hardware-based Neural Network's state-of-the-art designs.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196043"}, {"primary_key": "3389110", "vector": [], "sparse_vector": [], "title": "Application level hardware tracing for scaling post-silicon debug.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Flavio M. de Paula", "<PERSON><PERSON><PERSON>"], "summary": "We present a method for selecting trace messages for post-silicon validation of Systems-on-a-Chips (SoCs) with diverse usage scenarios. We model specifications of interacting flows in typical applications. Our method optimizes trace buffer utilization and flow specification coverage. We present debugging and root cause analysis of subtle bugs in the industry scale OpenSPARC T2 processor. We demonstrate that this scale is beyond the capacity of current tracing approaches. We achieve trace buffer utilization of 98.96% with a flow specification coverage of 94.3% (average). We localize bugs to 21.11% (average) of the potential root causes in our large-scale debugging effort.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195992"}, {"primary_key": "3389111", "vector": [], "sparse_vector": [], "title": "CASTLE: compression architecture for secure low latency, low energy, high endurance NVMs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "CASTLE is a Compression-based main memory Architecture realizing a read-decrypt-free (i.e., write-only) Secure solution for low laTency, Low Energy, high endurance non-volatile memories (NVMs). CASTLE integrates pattern-based data compression and incomplete data mapping (i.e., expansion coding) to improve NVM energy, latency, and lifetime without impacting the security guarantees of the underlying security architecture. System-level simulations of a TLC RRAM architecture show that CASTLE reduces memory energy by 19% and write latency by 38.7%, and improves lifetime by 1.8× over state-of-the-art solutions for NVM security.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196007"}, {"primary_key": "3389112", "vector": [], "sparse_vector": [], "title": "OFTL: ordering-aware FTL for maximizing performance of the journaling file system.", "authors": ["Daekyu Park", "<PERSON><PERSON><PERSON>", "Young Ik Eom"], "summary": "Journaling of ext4 file system employs two FLUSH commands to make their data durable, even though the FLUSH is more expensive than the ordinary write operations. In this paper, to halve the number of FLUSH commands, we propose an efficient FTL, called OFTL, that completely ensures the write order of the journal data blocks in storage-level. For performance comparison, we implemented our OFTL on Jasmine OpenSSD and measured its performance with three different workloads. Our experimental results show that OFTL outperforms the up-to-date FTLs on the existing journaling modes by up to 1.97 times.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196082"}, {"primary_key": "3389113", "vector": [], "sparse_vector": [], "title": "Power-based side-channel instruction-level disassembler.", "authors": ["Jungmin Park", "<PERSON><PERSON>", "<PERSON><PERSON>", "Domenic Forte", "<PERSON>"], "summary": "Modern embedded computing devices are vulnerable against malware and software piracy due to insufficient security scrutiny and the complications of continuous patching. To detect malicious activity as well as protecting the integrity of executable software, it is necessary to monitor the operation of such devices. In this paper, we propose a disassembler based on power-based side-channel to analyze the real-time operation of embedded systems at instruction-level granularity. The proposed disassembler obtains templates from an original device (e.g., IoT home security system, smart thermostat, etc.) and utilizes machine learning algorithms to uniquely identify instructions executed on the device. The feature selection using Kullback-Leibler (KL) divergence and the dimensional reduction using PCA in the time-frequency domain are proposed to increase the identification accuracy. Moreover, a hierarchical classification framework is proposed to reduce the computational complexity associated with large instruction sets. In addition, covariate shifts caused by different environmental measurements and device-to-device variations are minimized by our covariate shift adaptation technique. We implement this disassembler on an AVR 8-bit microcontroller. Experimental results demonstrate that our proposed disassembler can recognize test instructions including register names with a success rate no lower than 99.03% with quadratic discriminant analysis (QDA).", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196094"}, {"primary_key": "3389114", "vector": [], "sparse_vector": [], "title": "QoS-aware stochastic power management for many-cores.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>dr", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A many-core processor can execute hundreds of multi-threaded tasks in parallel on its 100s - 1000s of processing cores. When deployed in a Quality of Service (QoS)-based system, the many-core must execute a task at a target QoS. The amount of processing required by the task for the QoS varies over the task's lifetime. Accordingly, Dynamic Voltage and Frequency Scaling (DVFS) allows the many-core to deliver precise amount of processing required to meet the task QoS guarantee while conserving power. Still, a global control is necessitated to ensure that the many-core overall does not exceed its power budget.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196097"}, {"primary_key": "3389115", "vector": [], "sparse_vector": [], "title": "Raise your game for split manufacturing: restoring the true functionality through BEOL.", "authors": ["Satwik <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Split manufacturing (SM) seeks to protect against piracy of intellectual property (IP) in chip designs. Here we propose a scheme to manipulate both placement and routing in an intertwined manner, thereby increasing the resilience of SM layouts. Key stages of our scheme are to (partially) randomize a design, place and route the erroneous netlist, and restore the original design by re-routing the BEOL. Based on state-of-the-art proximity attacks, we demonstrate that our scheme notably excels over the prior art (i.e., 0% correct connection rates). Our scheme induces controllable PPA overheads and lowers commercial cost (the latter by splitting at higher layers).", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196100"}, {"primary_key": "3389116", "vector": [], "sparse_vector": [], "title": "Analog placement with current flow and symmetry constraints using PCP-SP.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Po-Cheng Pan", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern analog placement techniques require consideration of current path and symmetry constraints. The symmetry pairs can be efficiently packed using the symmetry island configurations, but not all these configurations result in minimum gate interconnection, which can impact the overall circuit routing and performance. This paper proposes the first work that reformulates this problem considering all of them together in the form of Parallel Current Path (PCP) constraints. Then a placement algorithm satisfying these constraints is formulated to reduce a vast search space via efficient sequence pair manipulation. Experimental results show that this formulation and algorithm can satisfy all the constraints in a more tightly packed configuration, resulting in lesser routing length, reduced parasitics and thus better post-layout performance.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195990"}, {"primary_key": "3389117", "vector": [], "sparse_vector": [], "title": "A general graph based pessimism reduction framework for design optimization of timing closure.", "authors": ["<PERSON><PERSON>", "Chang<PERSON> Yan", "Chunyang Feng", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper, we develop a general pessimism reduction framework for design optimization of timing closure. Although the modified graph based timing analysis (mGBA) slack model can be readily formulated into a quadratic programming problem with constraints, the realistic difficulty is the size of the problem. A critical path selection scheme, a uniform sampling method with the sparse characteristics of the optimal solution, and a stochastic conjugate gradient method are proposed to accelerate the optimization solver. This modified GBA is embedded into design optimization of timing closure. Experimental results show that the proposed solver can achieve 13.82x speedup than gradient descent method with similar accuracy. With mGBA, the optimization of timing closure can achieve a better performance on area, leakage power, buffer counts.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195973"}, {"primary_key": "3389118", "vector": [], "sparse_vector": [], "title": "TAO: techniques for algorithm-level obfuscation during high-level synthesis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Intellectual Property (IP) theft costs semiconductor design companies billions of dollars every year. Unauthorized IP copies start from reverse engineering the given chip. Existing techniques to protect against IP theft aim to hide the IC's functionality, but focus on manipulating the HDL descriptions. We propose TAO as a comprehensive solution based on high-level synthesis to raise the abstraction level and apply algorithmic obfuscation automatically. TAO includes several transformations that make the component hard to reverse engineer during chip fabrication, while a key is later inserted to unlock the functionality. Finally, this is a promising approach to obfuscate large-scale designs despite the hardware overhead needed to implement the obfuscation.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196126"}, {"primary_key": "3389119", "vector": [], "sparse_vector": [], "title": "Accurate processor-level wirelength distribution model for technology pathfinding using a modernized interpretation of rent&apos;s rule.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Faithful system-level modeling is vital to design and technology pathfinding, and requires accurate representation of interconnects. In this study, <PERSON><PERSON>'s rule is modernized to cater to advanced technology and design, and applied to derive a priori wirelength distribution models. Furthermore, a priori interconnect branching models are proposed to capture design constraints and their handling by the Electronic-Design-Automation tools. These interconnect branching models are embedded into the wirelength distribution models and validated against a suite of state-of-the-art commercial designs across technology nodes. Novel design-specific critical-path models are presented which capture trends in technology and microarchitecture, providing a reliable framework for future technology and design benchmarking.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195980"}, {"primary_key": "3389120", "vector": [], "sparse_vector": [], "title": "Dyhard-DNN: even more DNN acceleration with dynamic hardware reconfiguration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Alper Buyuktosunoglu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep Neural Networks (DNNs) have demonstrated their utility across a wide range of input data types, usable across diverse computing substrates, from edge devices to datacenters. This broad utility has resulted in myriad hardware accelerator architectures. However, DNNs exhibit significant heterogeneity in their computational characteristics, e.g., feature and kernel dimensions, and dramatic variances in computational intensity, even between adjacent layers in one DNN. Consequently, accelerators with static hardware parameters run sub-optimally and leave energy-efficiency margins unclaimed. We propose DyHard-DNNs, where accelerator microarchitectural parameters are dynamically reconfigured during DNN execution to significantly improve metrics of interest. We demonstrate the effectiveness of this approach on a configurable SIMD 2D systolic array and show a 15--65% performance improvement (at iso-power) and 25--90% energy improvement (at iso-latency) over the best static configuration in six mainstream DNN workloads.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196033"}, {"primary_key": "3389121", "vector": [], "sparse_vector": [], "title": "Atomlayer: a universal reRAM-based CNN accelerator with atomic layer computation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hai <PERSON>"], "summary": "Although ReRAM-based convolutional neural network (CNN) accelerators have been widely studied, state-of-the-art solutions suffer from either incapability of training (e.g., ISSAC [1]) or inefficiency of inference (e.g., PipeLayer [2]) due to the pipeline design. In this work, we propose Atom<PERSON>ayer---a universal ReRAM-based accelerator to support both efficient CNN training and inference. AtomLayer uses the atomic layer computation which processes only one network layer each time to eliminate the pipeline related issues such as long latency, pipeline bubbles and large on-chip buffer overhead. For further optimization, we use a unique filter mapping and a data reuse system to minimize the cost of layer switching and DRAM access. Our experimental results show that AtomLayer can achieve higher power efficiency than ISSAC in inference (1.1×) and <PERSON><PERSON><PERSON><PERSON><PERSON> in training (1.6×), respectively, meanwhile reducing the footprint by 15×.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195998"}, {"primary_key": "3389122", "vector": [], "sparse_vector": [], "title": "PARM: power supply noise aware resource management for NoC based multicore systems in the dark silicon era.", "authors": ["<PERSON>en<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Reliability is a major concern in chip multi-processors (CMPs) due to shrinking technology and low operating voltages. Today's processors designed at sub-10nm technology nodes have high device densities and fast switching frequencies that cause fluctuations in supply voltage (Vdd) and ground networks, which can adversely affect the execution of applications running on them. In this paper, we propose a novel runtime framework to reduce the power supply noise (PSN) in cores and routers at runtime. Experimental results for 7nm FinFET process nodes show that our framework not only achieves up to 4.5× reduction in PSN, and up to 34.3% improvement in application performance, but also manages to map up to 38% more applications when the CMP is oversubscribed, compared to the state-of-the-art.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196090"}, {"primary_key": "3389123", "vector": [], "sparse_vector": [], "title": "Protecting the supply chain for automotives and IoTs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Rosario Cammarota"], "summary": "Modern automotive systems and IoT devices are designed through a highly complex, globalized, and potentially untrustworthy supply chain. Each player in this supply chain may (1) introduce sensitive information and data (collectively termed \"assets\") that must be protected from other players in the supply chain, and (2) have controlled access to assets introduced by other players. Furthermore, some players in the supply chain may be malicious. It is imperative to protect the device and any sensitive assets in it from being compromised or unknowingly disclosed by such entities. A key --- and sometimes overlooked --- component of security architecture of modern electronic systems entails managing security in the face of supply chain challenges. In this paper we discuss some security challenges in automotive and IoT systems arising from supply chain complexity, and the state of the practice in this area.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199851"}, {"primary_key": "3389124", "vector": [], "sparse_vector": [], "title": "Ares: a framework for quantifying the resilience of deep neural networks.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Lillian Pentecost", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "As the use of deep neural networks continues to grow, so does the fraction of compute cycles devoted to their execution. This has led the CAD and architecture communities to devote considerable attention to building DNN hardware. Despite these efforts, the fault tolerance of DNNs has generally been overlooked. This paper is the first to conduct a large-scale, empirical study of DNN resilience. Motivated by the inherent algorithmic resilience of DNNs, we are interested in understanding the relationship between fault rate and model accuracy. To do so, we present Ares: a light-weight, DNN-specific fault injection framework validated within 12% of real hardware. We find that DNN fault tolerance varies by orders of magnitude with respect to model, layer type, and structure.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195997"}, {"primary_key": "3389125", "vector": [], "sparse_vector": [], "title": "Architecture decomposition in system synthesis of heterogeneous many-core systems.", "authors": ["Vale<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Determining feasible application mappings for Design Space Exploration (DSE) and run-time embedding is a challenge for modern many-core systems. The underlying NP-complete system-synthesis problem faces tremendously complex problem instances due to the hundreds of heterogeneous processing elements, their communication infrastructure, and the resulting number of mapping possibilities. Thus, we propose to employ a search-space splitting (SSS) technique using architecture decomposition to increase the performance of existing design-time and run-time synthesis approaches. The technique first restricts the search for application embeddings to selected sub-architectures at substantially reduced complexity; therefore, the complete architecture needs to be searched only in case no embedding is found on any sub-system. Furthermore, we introduce a basic learning mechanism to detect promising sub-architectures and subsequently restrict the search to those. We exemplify the SSS for a SAT-based and a problem-specific backtracking-based system synthesis as part of DSE for NoC-based many-core systems. Experimental results show drastically reduced execution times (≈ 15--50 x on a 24×24 architecture) and an enhanced quality of the embedding, since less mappings (≈20--40 x, compared to the non-decomposing procedures) need to be discarded due to a timeout.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195995"}, {"primary_key": "3389126", "vector": [], "sparse_vector": [], "title": "Locality aware memory assignment and tiling.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "With the trend toward specialization, an efficient memory-path design is vital to capitalize customization in data-path. A monolithic memory hierarchy is often highly inefficient for irregular applications, traditionally targeted for CPUs. New approaches and tools are required to offer application-specific memory customization combining the benefits of cache and scratchpad memory simultaneously.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196070"}, {"primary_key": "3389127", "vector": [], "sparse_vector": [], "title": "Extensive evaluation of programming models and ISAs impact on multicore soft error reliability.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "To take advantage of the performance enhancements provided by multicore processors, new instruction set architectures (ISAs) and parallel programming libraries have been investigated across multiple industrial segments. This paper investigates the impact of parallelization libraries and distinct ISAs on the soft error reliability of two multicore ARM processor models (i.e., Cortex-A9 and Cortex-A72), running Linux Kernel and benchmarks with up to 87 billion instructions. An extensive soft error evaluation with more than 1.2 million simulation hours, considering ARMv7 and ARMv8 ISAs and the NAS Parallel Benchmark (NPB) suite is presented.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196050"}, {"primary_key": "3389128", "vector": [], "sparse_vector": [], "title": "Deepsecure: scalable provably-secure deep learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents DeepSecure, the an scalable and provably secure Deep Learning (DL) framework that is built upon automated design, efficient logic synthesis, and optimization methodologies. DeepSecure targets scenarios in which neither of the involved parties including the cloud servers that hold the DL model parameters or the delegating clients who own the data is willing to reveal their information. Our framework is the first to empower accurate and scalable DL analysis of data generated by distributed clients without sacrificing the security to maintain efficiency. The secure DL computation in DeepSecure is performed using Yao's Garbled Circuit (GC) protocol. We devise GC-optimized realization of various components used in DL. Our optimized implementation achieves up to 58-fold higher throughput per sample compared with the best prior solution. In addition to the optimized GC realization, we introduce a set of novel low-overhead pre-processing techniques which further reduce the GC overall runtime in the context of DL. Our extensive evaluations demonstrate up to two orders-of-magnitude additional runtime improvement achieved as a result of our pre-processing methodology.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196023"}, {"primary_key": "3389129", "vector": [], "sparse_vector": [], "title": "Efficient reinforcement learning for automating human decision-making in SoC design.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The exponential growth in PVT corners due to <PERSON>'s law scaling, and the increasing demand for consumer applications and longer battery life in mobile devices, has ushered in significant cost and power-related challenges for designing and productizing mobile chips within a predictable schedule. Two main reasons for this are the reliance on human decision-making to achieve the desired performance within the target area and power budget, and significant increases in complexity of the human decision-making space. The problem is that to-date human design experience has not been replaced by design automation tools, and tasks requiring experience of past designs are still being performed manually.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199855"}, {"primary_key": "3389130", "vector": [], "sparse_vector": [], "title": "Ensemble learning for effective run-time hardware-based malware detection: a comprehensive analysis and classification.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Man<PERSON>j P. D.", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Malware detection at the hardware level has emerged recently as a promising solution to improve the security of computing systems. Hardware-based malware detectors take advantage of Machine Learning (ML) classifiers to detect pattern of malicious applications at run-time. These ML classifiers are trained using low-level features such as processor Hardware Performance Counters (HPCs) data which are captured at run-time to appropriately represent the application behaviour. Recent studies show the potential of standard ML-based classifiers for detecting malware using analysis of large number of microarchitectural events, more than the very limited number of HPC registers available in today's microprocessors which varies from 2 to 8. This results in executing the application more than once to collect the required data, which in turn makes the solution less practical for effective run-time malware detection. Our results show a clear trade-off between the performance of standard ML classifiers and the number and diversity of HPCs available in modern microprocessors. This paper proposes a machine learning-based solution to break this trade-off to realize effective run-time detection of malware. We propose ensemble learning techniques to improve the performance of the hardware-based malware detectors despite using a very small number of microarchitectural events that are captured at run-time by existing HPCs, eliminating the need to run an application several times. For this purpose, eight robust machine learning models and two well-known ensemble learning classifiers applied on all studied ML models (sixteen in total) are implemented for malware detection and precisely compared and characterized in terms of detection accuracy, robustness, performance (accuracy×robustness), and hardware overheads. The experimental results show that the proposed ensemble learning-based malware detection with just 2 HPCs using ensemble technique outperforms standard classifiers with 8 HPCs by up to 17%. In addition, it can match the robustness and performance of standard ML-based detectors with 16 HPCs while using only 4 HPCs allowing effective run-time detection of malware.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196047"}, {"primary_key": "3389131", "vector": [], "sparse_vector": [], "title": "Response-time analysis of DAG tasks supporting heterogeneous computing.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Hardware platforms are evolving towards parallel and heterogeneous architectures to overcome the increasing necessity of more performance in the real-time domain. Parallel programming models are fundamental to exploit the performance capabilities of these architectures. This paper proposes a novel response time analysis (RTA) for verifying the schedulability of DAG tasks supporting heterogeneous computing. It analyzes the impact of executing part of the DAG in the accelerator device. As a result, the response time upper bound of the system is more precise than the one provided by currently existing RTA targeting homogeneous architectures.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196104"}, {"primary_key": "3389132", "vector": [], "sparse_vector": [], "title": "Loom: exploiting weight and activation precisions to accelerate convolutional neural networks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Loom (LM), a hardware inference accelerator for Convolutional Neural Networks (CNNs) is presented. In LM every bit of data precision that can be saved translates to proportional performance gains. For both weights and activations LM exploits profile-derived per layer precisions. However, at runtime LM further trims activation precisions at a much smaller than a layer granularity. On average, across several image classification CNNs and for a configuration that can perform the equivalent of 128 16b × 16b multiply-accumulate operations per cycle LM outperforms a state-of-the-art bit-parallel accelerator [3] by 3.19× without any loss in accuracy while being 2.59× more energy efficient. LM can trade-off accuracy for additional improvements in execution performance and energy efficiency and compares favorably to an accelerator that targeted only activation precisions.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196072"}, {"primary_key": "3389133", "vector": [], "sparse_vector": [], "title": "A fast and robust failure analysis of memory circuits using adaptive importance sampling method.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Performance failure has become a growing concern for the robustness and reliability of memory circuits. It is challenging to accurately estimate the extremely small failure probability when failed samples are distributed in multiple disjoint failure regions. In this paper, we develop an adaptive importance sampling (AIS) method. AIS has several iterations of sampling region adjustments, while existing methods pre-decide a static sampling distribution. By iteratively searching for failure regions, AIS may lead to better efficiency and accuracy. This is validated by our experiments. For SRAM cell with single failure region, AIS uses 5-10X fewer samples and reaches better accuracy when compared to several recent methods. For sense amplifier circuit with multiple failure regions, AIS is 4369X faster than MC without compromising accuracy, while other methods fail to cover all failure regions in our experiment.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195972"}, {"primary_key": "3389134", "vector": [], "sparse_vector": [], "title": "DPS: dynamic precision scaling for stochastic computing-based deep neural networks.", "authors": ["Hyeon Uk Sim", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Stochastic computing (SC) is a promising technique with advantages such as low-cost, low-power, and error-resilience. However so far SC-based CNN (convolutional neural network) accelerators have been kept to relatively small CNNs only, primarily due to the inherent precision disadvantage of SC. At the same time, previous SC architectures do not exploit the dynamic precision capability, which can be crucial in providing efficiency as well as flexibility in SC-CNN implementations. In this paper we present a DPS (dynamic precision scaling) SC-CNN that is able to exploit dynamic precision with very low overhead, along with the design methodology for it. Our experimental results demonstrate that our DPS SC-CNN is highly efficient and accurate up to ImageNet-targeting CNNs, and show efficiency improvements over conventional digital designs ranging in 50~100% in operations-per-area depending on the DNN and the application scenario, while losing less than 1% in recognition accuracy.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196028"}, {"primary_key": "3389135", "vector": [], "sparse_vector": [], "title": "SARA: self-aware resource allocation for heterogeneous MPSoCs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In modern heterogeneous MPSoCs, the management of shared memory resources is crucial in delivering end-to-end QoS. Previous frameworks have either focused on singular QoS targets or the allocation of partitionable resources among CPU applications at relatively slow timescales. However, heterogeneous MPSoCs typically require instant response from the memory system where most resources cannot be partitioned. Moreover, the health of different cores in a heterogeneous MPSoC is often measured by diverse performance objectives. In this work, we propose the Self-Aware Resource Allocation (SARA) framework for heterogeneous MPSoCs. Priority-based adaptation allows cores to use different target performance and self-monitor their own intrinsic health. In response, the system allocates non-partitionable resources based on priorities. The proposed framework meets a diverse range of QoS demands from heterogeneous cores.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196110"}, {"primary_key": "3389136", "vector": [], "sparse_vector": [], "title": "Efficient batch statistical error estimation for iterative multi-level approximate logic synthesis.", "authors": ["Sanbao Su", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Approximate computing is an emerging energy-efficient paradigm for error-resilient applications. Approximate logic synthesis (ALS) is an important field of it. To improve the existing ALS flows, one key issue is to derive a more accurate and efficient batch error estimation technique for all approximate transformations under consideration. In this work, we propose a novel batch error estimation method based on Monte Carlo simulation and local change propagation. It is generally applicable to any statistical error measurement such as error rate and average error magnitude. We applied the technique to an existing state-of-the-art ALS approach and demonstrated its effectiveness in deriving better approximate circuits.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196038"}, {"primary_key": "3389137", "vector": [], "sparse_vector": [], "title": "Closed yet open DRAM: achieving low latency and high performance in DRAM memory systems.", "authors": ["<PERSON><PERSON><PERSON>rama<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sreenivas Subramoney", "<PERSON><PERSON>", "<PERSON>"], "summary": "DRAM memory access is a critical performance bottleneck. To access one cache block, an entire row needs to be sensed and amplified, data restored into the bitcells and the bitlines precharged, incurring high latency. Isolating the bitlines and sense amplifiers after activation enables reads and precharges to happen in parallel. However, there are challenges in achieving this isolation. We tackle these challenges and propose an effective scheme, simultaneous read and precharge (SRP), to isolate the sense amplifiers and bitlines and serve reads and precharges in parallel. Our detailed architecture and circuit simulations demonstrate that our simultaneous read and precharge (SRP) mechanism is able to achieve an 8.6% performance benefit over baseline, while reducing sense amplifier idle power by 30%, as compared to prior work, over a wide range of workloads.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196008"}, {"primary_key": "3389138", "vector": [], "sparse_vector": [], "title": "ACME: advanced counter mode encryption for secure non-volatile memories.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Modern computing systems that integrate emerging non-volatile memories (NVMs) are vulnerable to classical security threats to data confidentiality (e.g., stolen DIMM and bus snooping attacks) as well as new security threats to system availability (e.g., denial of memory service (DoMS) attacks). Although counter mode encryption (CME) secures NVM-based main memories against confidentiality attacks, counter sizing is critical to balance tradeoffs between memory overhead, system performance, and re-encryption frequency (i.e., system availability). Furthermore, CME is particularly vulnerable to DoMS attacks, where a malicious application can severely impact memory availability by forcing frequent full memory re-encryption. This paper proposes Advanced Counter Mode Encryption, i.e., ACME, a low overhead CME-based main memory encryption solution to realize the twin security goals of confidentiality and availability in NVM-based main memories. At its core, ACME integrates counter write leveling (CWL) to reduce the frequency of full memory re-encryption while preserving the security properties of the underlying CME. Our evaluations on a phase change memory (PCM) architecture using SPEC CPU2006 benchmarks show that for a system availability of 99.999%, ACME not only requires 50% lower counter overhead, but also improves system performance by 20% in comparison to classical CME. When subject to a DoMS attack in the form of an unprivileged Linux process that sidesteps all levels of cache to constantly write to the same memory address to precipitate counter overflow, the ACME-based system provides 99.9% system availability in contrast to a classical CME-based system that is rendered non-operational.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195983"}, {"primary_key": "3389139", "vector": [], "sparse_vector": [], "title": "STASH: security architecture for smart hybrid memories.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Whereas emerging non-volatile memories (NVMs) are low power, dense, scalable alternatives to DRAM, the high latency and low endurance of these NVMs limit the feasibility of NVM-only memory systems. Smart hybrid memories (SHMs) that integrate NVM, DRAM, and on-module processor logic are an efficient means to bridge the latency and endurance gaps between NVM-only and DRAM-only memory systems. However, these SHMs are vulnerable to data confidentiality and integrity attacks that can be executed on the unsecure NVM, DRAM, and/or memory buses. STASH is the first comprehensive end-to-end SecuriTy Architecture for SHMs that integrates (i) counter mode encryption for data confidentiality, (ii) low overhead page-level Merkle Tree (MT) authentication for data integrity, (iii) recovery-compatible MT updates to withstand power/system failures, and (iv) page-migration-friendly security meta-data management. For security guarantees equivalent to the closest state-of-the-art security solution extensible to SHMs, STASH reduces memory overhead by 12.7 ×, increases system performance by 65%, and improves NVM lifetime by 2.5 ×.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196123"}, {"primary_key": "3389140", "vector": [], "sparse_vector": [], "title": "A machine learning framework to identify detailed routing short violations from a placed netlist.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Xu", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Detecting and preventing routing violations has become a critical issue in physical design, especially in the early stages. Lack of correlation between global and detailed routing congestion estimations and the long runtime required to frequently consult a global router adds to the problem. In this paper, we propose a machine learning framework to predict detailed routing short violations from a placed netlist. Factors contributing to routing violations are determined and a supervised neural network model is implemented to detect these violations. Experimental results show that the proposed method is able to predict on average 90% of the shorts with only 7% false alarms and considerably reduced computational time.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195975"}, {"primary_key": "3389141", "vector": [], "sparse_vector": [], "title": "A machine learning based hard fault recuperation model for approximate hardware accelerators.", "authors": ["<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>"], "summary": "Continuous pursuit of higher performance and energy efficiency has led to heterogeneous SoC that contains multiple dedicated hardware accelerators. These accelerators exploit the inherent parallelism of tasks and are often tolerant to inaccuracies in their outputs, e.g. image and digital signal processing applications. At the same time, permanent faults are escalating due to process scaling and power restrictions, leading to erroneous outputs. To address this issue, in this paper, we propose a low-cost, universal fault-recovery/repair method that utilizes supervised machine learning techniques to ameliorate the effect of permanent fault(s) in hardware accelerators that can tolerate inexact outputs. The proposed compensation model does not require any information about the accelerator and is highly scalable with low area overhead. Experimental results show, the proposed method improves the accuracy by 50% and decreases the overall mean error rate by 90% with an area overhead of 5% compared to execution without fault compensation.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195974"}, {"primary_key": "3389142", "vector": [], "sparse_vector": [], "title": "Tamper-resistant pin-constrained digital microfluidic biochips.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Digital microfluidic biochips (DMFBs)---an emerging technology that implements bioassays through manipulation of discrete fluid droplets---are vulnerable to actuation tampering attacks, where a malicious adversary modifies control signals for the purposes of manipulating results or causing denial-of-service. Such attacks leverage the highly programmable nature of DMFBs. However, practical DMFBs often employ a technique called pin mapping to reduce control pin count while simultaneously reducing the degrees of freedom available for droplet manipulation. Attempts to control specific electrodes as part of an attack cannot be made without inadvertently actuating other electrodes on-chip, which makes the tampering evident. This paper explores this tamper-resistance property of pin mapping in detail. We derive relevant security metrics, evaluate the tamper-resistance of several existing pin mapping algorithms, and propose a new security-aware pin mapper with superior tamper-resistance as compared to prior work.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196125"}, {"primary_key": "3389143", "vector": [], "sparse_vector": [], "title": "Basejump STL: systemverilog needs a standard template library for hardware design.", "authors": ["<PERSON>"], "summary": "We propose a Standard Template Library (STL) for synthesizeable SystemVerilog that sharply reduces the time required to design digital circuits. We overview the principles that underly the design of the open-source BaseJump STL, including light-weight latency-insensitive interfaces that yield fast microarchitectures and low bug density; thin handshaking rules; fast porting of hardened chip regions across nodes; pervasive parameterization and specialization, and static error checking. We suggest extensions to SystemVerilog that will make it a more functional design language, and discuss our validation, including with the DARPA CRAFT-sponsored 16nm TSMC Celerity SoC with 511 RISC-V cores and 385M transistors. 80% of the modules for the design were instantiated directly from BaseJump STL, reducing verification time, accelerating development, and showing the promise of the approach.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199848"}, {"primary_key": "3389144", "vector": [], "sparse_vector": [], "title": "Soft-FET: phase transition material assisted soft switching field effect transistor for supply voltage droop mitigation.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Phase Transition Material (PTM) assisted novel soft switching transistor architecture named \"Soft-FET\" is proposed for supply voltage droop mitigation. By utilizing the abrupt phase transition mechanism in PTMs, the proposed Soft-FET achieves soft switching of the gate input of a logic gate resulting in reduced peak switching current as well as steep current variations (di/dt). In addition, the Soft-FET incurs lower delay penalty across a wide voltage range compared to various baseline Complementary Metal Oxide Semiconductor (CMOS) logic gate variants for the same peak current. We perform a detailed PTM parameter optimization for optimum Soft-FET performance. Soft-FETs when used as power gates achieve ∼20mV lower supply droop and when used as an I/O buffer achieves 46% lower ground bounce with 8.8% improved energy efficiency.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196117"}, {"primary_key": "3389145", "vector": [], "sparse_vector": [], "title": "Cache side-channel attacks and time-predictability in high-performance critical real-time systems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Embedded computers control an increasing number of systems directly interacting with humans, while also manage more and more personal or sensitive information. As a result, both safety and security are becoming ubiquitous requirements in embedded computers, and automotive is not an exception to that. In this paper we analyze time-predictability (as an example of safety concern) and side-channel attacks (as an example of security issue) in cache memories. While injecting randomization in cache timing-behavior addresses each of those concerns separately, we show that randomization solutions for time-predictability do not protect against side-channel attacks and vice-versa. We then propose a randomization solution to achieve both safety and security goals.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196003"}, {"primary_key": "3389146", "vector": [], "sparse_vector": [], "title": "Ultralow power acoustic feature-scoring using gaussian I-V transistors.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper discusses energy-efficient acoustic feature-scoring using transistors with Gaussian-shaped Ids-Vgs. Acoustic feature-scoring is a critical step in speech recognition tasks such as speaker recognition. Suited to the transistor, we discuss a novel acoustic model based on the harmonic mean of Gaussian functions, yielding a much-simplified physical implementation. Compared to digital CMOS, the discussed implementation reduces energy dissipation of a 13-dimensional mixture function by 45×. For a test-case recognizing among seven speakers, the design reduces power dissipation by 7.8× than digital CMOS. Exploiting the simplified mixture function, we present algorithmic overdesigning for resiliency against hardware imperfections.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196133"}, {"primary_key": "3389147", "vector": [], "sparse_vector": [], "title": "Brook auto: high-level certification-friendly programming for GPU-powered automotive systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern automotive systems require increased performance to implement Advanced Driving Assistance Systems (ADAS). GPU-powered platforms are promising candidates for such computational tasks, however current low-level programming models challenge the accelerator software certification process, while they limit the hardware selection to a fraction of the available platforms. In this paper we present Brook Auto, a high-level programming language for automotive GPU systems which removes these limitations. We describe the challenges and solutions we faced in its implementation, as well as a complete evaluation in terms of performance and productivity, which shows the effectiveness of our method.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196002"}, {"primary_key": "3389148", "vector": [], "sparse_vector": [], "title": "Columba S: a scalable co-layout design automation tool for microfluidic large-scale integration.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Tsung-<PERSON>", "<PERSON><PERSON>"], "summary": "Microfluidic large-scale integration (mLSI) is a promising platform for high-throughput biological applications. Design automation for mLSI has made much progress in recent years. Columba and its succeeding work Columba 2.0 proposed a mathematical modeling method that enables automatic design of manufacturing-ready chips within minutes. However, current approaches suffer from a huge computation load when the designs become larger. Thus, in this work, we propose Columba S with a focus on scalability. Columba S applies a new architectural framework and a straight channel routing discipline, and synthesizes multiplexers for efficient and reconfigurable valve control. Experiments show that Columba S is able to generate mLSI designs with more than 200 functional units within three minutes, which enables the design of a platform for large and complex applications.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196011"}, {"primary_key": "3389149", "vector": [], "sparse_vector": [], "title": "Reasoning about safety of learning-enabled components in autonomous cyber-physical systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Jyotirmoy V<PERSON>"], "summary": "We present a simulation-based approach for generating barrier certificate functions for safety verification of cyber-physical systems (CPS) that contain neural network-based controllers. A linear programming solver is utilized to find a candidate generator function from a set of simulation traces obtained by randomly selecting initial states for the CPS model. A level set of the generator function is then selected to act as a barrier certificate for the system, meaning it demonstrates that no unsafe system states are reachable from a given set of initial states. The barrier certificate properties are verified with an SMT solver. This approach is demonstrated on a case study in which a Dubins car model of an autonomous vehicle is controlled by a neural network to follow a given path.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199852"}, {"primary_key": "3389150", "vector": [], "sparse_vector": [], "title": "SMApproxlib: library of FPGA-based approximate multipliers.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The main focus of the existing approximate arithmetic circuits has been on ASIC-based designs. However, due to the architectural differences between ASICs and FPGAs, comparable performance gains cannot be achieved for FPGA-based systems by using the approximations defined, particularly for ASIC-based systems. This paper exploits the structure of the 6-input lookup tables and associated carry chains of modern FPGAs to define a methodology for designing approximate multipliers optimized for FPGA-based systems. Using our presented methodology, we present SMApproxLib, an open source library of approximate multipliers with different bit-widths, output accuracies and performance gains. Being the first open source library of FPGA-based approximate multipliers, SMAp-proxLib can serve as a benchmark for designing and comparing future FPGA-based approximate arithmetic circuits.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196115"}, {"primary_key": "3389151", "vector": [], "sparse_vector": [], "title": "Area-optimized low-latency approximate multipliers for FPGA-based hardware accelerators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The architectural differences between ASICs and FPGAs limit the effective performance gains achievable by the application of ASIC-based approximation principles for FPGA-based reconfigurable computing systems. This paper presents a novel approximate multiplier architecture customized towards the FPGA-based fabrics, an efficient design methodology, and an open-source library. Our designs provide higher area, latency and energy gains along with better output accuracy than those offered by the state-of-the-art ASIC-based approximate multipliers. Moreover, compared to the multiplier IP offered by the Xilinx Vivado, our proposed design achieves up to 30%, 53%, and 67% gains in terms of area, latency, and energy, respectively, while incurring an insignificant accuracy loss (on average, below 1% average relative error). Our library of approximate multipliers is open-source and available online at https://cfaed.tudresden.de/pd-downloads to fuel further research and development in this area, and thereby enabling a new research direction for the FPGA community.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195996"}, {"primary_key": "3389152", "vector": [], "sparse_vector": [], "title": "LAWN: boosting the performance of NVMM file system through reducing write amplification.", "authors": ["<PERSON><PERSON> Wang", "<PERSON><PERSON><PERSON>"], "summary": "Byte-addressable non-volatile memories can be used with DRAM to build a hybrid memory system of volatile/non-volatile main memory (NVMM). NVMM file systems demand consistency techniques such as logging and copy-on-write to guarantee data consistency in case of system crashes. However, conventional consistency techniques may incur write amplification that severely degrades the file system performance. In this paper, we propose LAWN (logless, alternate writing for NVMM), a novel approach that achieves data consistency and significantly improves performance via reducing write amplification. Our evaluation reveals that LAWN boosts the performance of a state-of-the-art NVMM file system by up to 12.0×.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196066"}, {"primary_key": "3389153", "vector": [], "sparse_vector": [], "title": "Minimizing write amplification to enhance lifetime of large-page flash-memory storage devices.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Due to the decreasing endurance of flash chips, the lifetime of flash drives has become a critical issue. To resolve this issue, various techniques such as wear-leveling and error correction code have been proposed to reduce the bit error rates of flash storage devices. In contrast to these techniques, we observe that minimizing write amplification is another promising direction to enhance the lifetime of a flash storage device. However, the development trend of large-page flash memory exacerbates the write amplification issue. In this work, we present a compression-based management design to deal with compressed data updates and internal fragmentation in flash pages. Thus, it can minimize write amplification by only updating the modified part of flash pages with the support of data reduction techniques; and the reduced write amplification degree is more significant when the flash page size becomes larger due to the development trend. This design is orthogonal to wear-leveling and error correction techniques and thus can cooperate with them to further enhance the lifetime of a flash device. Based on a series of experiments, the results demonstrate that the proposed design can effectively improve the lifetime of a flash storage device by reducing write amplification.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196076"}, {"primary_key": "3389154", "vector": [], "sparse_vector": [], "title": "SNrram: an efficient sparse neural network computation architecture based on resistive random-access memory.", "authors": ["<PERSON><PERSON><PERSON>", "Yu <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Wang", "<PERSON>"], "summary": "The sparsity in the deep neural networks can be leveraged by methods such as pruning and compression to help the efficient deployment of large-scale deep neural networks onto hardware platforms, such as GPU or FPGA, for better performance and power efficiency. However, for RRAM crossbar-based architectures, the study of efficient methods to consider the network sparsity is still in the early stage. In this study, we propose SNrram, an efficient sparse neural network computation architecture using RRAM, by exploiting the sparsity in both weights and activation. SNrram stores nontrivial weights and organizes them to eliminate zero-value multiplications for better resource utilization. Experimental results show that SNrram can save RRAM resources by 69.8%, reduce the power consumption by 35.9%, and speed up by 2.49× on popular deep learning benchmarks, compared to a state-of-the-art RRAM-based neural network accelerator.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196116"}, {"primary_key": "3389155", "vector": [], "sparse_vector": [], "title": "Efficient multi-layer obstacle-avoiding region-to-region rectilinear steiner tree construction.", "authors": ["Run-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hsiang<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As Engineering Change Order (ECO) has attracted substantial attention in modern VLSI design, the open net problem, which aims at constructing a shortest obstacle-avoiding path to reconnect the net shapes in an open net, becomes more critical in the ECO stage. This paper addresses a multi-layer obstacle-avoiding region-to-region Steiner minimal tree (SMT) construction problem that connects all net shapes by edges on a layer or vias between layers, and avoids running through any obstacle with a minimal total cost. Existing multi-layer obstacle-avoiding SMT algorithms consider pin-to-pin connections instead of region-to-region ones, which would limit the solution quality due to its lacking region information. In this paper, we present an efficient algorithm based on our new multi-layer obstacle-avoiding region-to-region spanning graph to solve the addressed problem, which guarantees to find an optimal solution for a net connecting two regions on a single layer. Experimental results show that our algorithm outperforms all the participating routers of the 2017 CAD Contest at ICCAD in both solution quality and runtime.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196040"}, {"primary_key": "3389156", "vector": [], "sparse_vector": [], "title": "ACED: a hardware library for generating DSP systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Designers translate DSP algorithms into application-specific hardware via primitives composed in various ways for different architectural realizations. Despite sharing underlying algorithms and hardware constructs, designs are often difficult to reuse, leading to redeveloping/reverifying conceptually similar instances. Hardware generators are attractive solutions for effectively balancing fine-grained control of implementation details with simple, retargetable hardware descriptions. This work presents ACED, a hardware library for generating DSP systems. It extends the Chisel hardware construction language and FIRRTL compiler and operates on three principles: zero-cost abstraction, unobtrusive downstream optimization/specialization promoting generator reusability, and unified, portable systems modeling and verification.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195981"}, {"primary_key": "3389157", "vector": [], "sparse_vector": [], "title": "Runtime monitoring for safety of intelligent vehicles.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Advanced driver-assistance systems (ADAS), autonomous driving, and connectivity have enabled a range of new features, but also made automotive design more complex than ever. Formal verification can be applied to establish functional correctness, but its scalability is limited due to the sheer complexity of a modern automotive system. To manage high complexity and limited development resources, one alternative is to apply runtime monitoring techniques to detect when the system transitions into an unsafe state (i.e., one where it violates a critical safety requirement). In this paper, we report on our experience integrating runtime monitoring into a development workflow and present practical design considerations on languages and tools from an industrial perspective. Using signal temporal logic (STL) [12] and the Breach [6] monitoring tool, we perform a case study showing how monitoring can be used to detect undesirable interactions between two ADAS features called Cooperative Pile-up Mitigation System (CPMS) and False-Start Prevention System (FPS). This is an initial step to utilize runtime monitoring to achieve high assurance in the design of intelligent vehicles.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3199856"}, {"primary_key": "3389158", "vector": [], "sparse_vector": [], "title": "Wear leveling for crossbar resistive memory.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Resistive Memory (ReRAM) is an emerging non-volatile memory technology that has many advantages over conventional DRAM. ReRAM crossbar has the smallest 4F2 planar cell size and thus is widely adopted for constructing dense memory with large capacity. However, ReRAM crossbar suffers from large sneaky currents and IR drop. To ensure write reliability, ReRAM write drivers choose larger than ideal write voltages, which over-SET/over-RESET many cells at runtime and lead to severely degraded chip lifetime.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196138"}, {"primary_key": "3389159", "vector": [], "sparse_vector": [], "title": "Formal micro-architectural analysis of on-chip ring networks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "In the realm of Multi-Processors System-on-Chip (MPSoC's), the Network-on-Chip (NoC) connecting all system components plays a crucial role in the overall correctness and performance of the system. Recent papers have proposed several ring based NoC solutions. The description and analysis of these micro-architectures are informal in nature. Text is used to argue about, e.g., deadlock freedom. For the first time, this paper proposes an environment for the formal modelling and analysis of such ring architectures with an emphasis on liveness properties. Our contribution includes a language to model ring micro-architectures, invariant generation techniques, deadlock freedom verification, and the application of our approach on a realistic case-study. The analysis reveals a possible deadlock not mentioned in the original paper.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196054"}, {"primary_key": "3389160", "vector": [], "sparse_vector": [], "title": "FMMU: a hardware-accelerated flash map management unit for scalable performance of flash-based SSDs.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Address translation is increasingly a performance bottleneck in flash-based SSDs (solid state drives). We propose a hardware-accelerated flash map management unit called FMMU to speed up the address translation. The FMMU operates in a non-blocking manner without any involvement of software and uses in-cache linked list to enhance the performance of flush operations. Simulation study shows that the FMMU improves IOPS (I/O operations per second) by 135 % on average when compared against software-only approaches. The results also show that the performance gap widens as the number of flash chips/buses and the host interface speed increase.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196053"}, {"primary_key": "3389161", "vector": [], "sparse_vector": [], "title": "Improving runtime performance of deduplication system with host-managed SMR storage drives.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Due to the cost consideration for data storage, high-areal-density shingled-magnetic-recording (SMR) drives and data deduplication techniques are getting popular in many data storage services for the improvement of profit per storage unit. However, naively applying deduplication techniques upon SMR drives may dramatically downgrade the runtime performance of data storage services, because of the time-consuming SMR space reclamation processes. This work advocates a vertical integration solution by jointly managing the host-managed SMR drives with deduplication system, in order to essentially relieve the time-consuming SMR space reclamation issue. The proposed design was evaluated by a series of realistic deduplication workloads with encouraging results.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196063"}, {"primary_key": "3389162", "vector": [], "sparse_vector": [], "title": "Efficient winograd-based convolution kernel implementation on edge devices.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The implementation of Convolutional Neural Networks on edge Internet of Things (IoT) devices is a significant programming challenge, due to the limited computational resources and the real-time requirements of modern applications. This work focuses on the efficient implementation of the Winograd convolution, based on a set of application-independent and Winograd-specific software techniques for improving the utilization of the edge devices computational resources. The proposed techniques were evaluated in Intel/Movidius Myriad2 platform, using 4 CNNs of various computational requirements. The results show significant performance improvements, up to 54%, over other convolution algorithms.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196041"}, {"primary_key": "3389163", "vector": [], "sparse_vector": [], "title": "Data prediction for response flows in packet processing cache.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>i <PERSON>"], "summary": "We propose a technique to reduce compulsory misses of packet processing cache (PPC), which largely affects both throughput and energy of core routers. Rather than prefetching data, our technique called response prediction cache (RPC) speculatively stores predicted data into PPC without additional access to the low-throughput and power-consuming memory (i.e., TCAM). RPC predicts the data related to a response flow at the arrival of the corresponding request flow, based on the request-response model of internet communications. RPC can improve the cache miss rate, throughput, and energy-efficiency of PPC systems by 15.3%, 17.9%, and 17.8%, respectively.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196021"}, {"primary_key": "3389164", "vector": [], "sparse_vector": [], "title": "A neuromorphic design using chaotic mott memristor with relaxation oscillation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> (<PERSON>) <PERSON>"], "summary": "The recent proposed nanoscale Mott memristor features negative differential resistance and chaotic dynamics. This work proposes a novel neuromorphic computing system that utilizes Mott memristors to simplify peripheral circuitry. According to the analytic description of chaotic dynamics and relaxation oscillation, we carefully tune the working point of Mott memristors to balance the chaotic behavior weighing testing accuracy and training efficiency. Compared with conventional designs, the proposed design accelerates the training by 1.893× averagely and saves 27.68% and 43.32% power consumption with 36.67% and 26.75% less area for single-layer and two-layer perceptrons, respectively.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195977"}, {"primary_key": "3389165", "vector": [], "sparse_vector": [], "title": "GAN-OPC: mask optimization with lithography-guided generative adversarial nets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>. <PERSON>"], "summary": "Mask optimization has been a critical problem in the VLSI design flow due to the mismatch between the lithography system and the continuously shrinking feature sizes. Optical proximity correction (OPC) is one of the prevailing resolution enhancement techniques (RETs) that can significantly improve mask printability. However, in advanced technology nodes, the mask optimization process consumes more and more computational resources. In this paper, we develop a generative adversarial network (GAN) model to achieve better mask optimization performance. We first develop an OPC-oriented GAN flow that can learn target-mask mapping from the improved architecture and objectives, which leads to satisfactory mask optimization results. To facilitate the training process and ensure better convergence, we also propose a pre-training procedure that jointly trains the neural network with inverse lithography technique (ILT). At convergence, the generative network is able to create quasi-optimal masks for given target circuit patterns and fewer normal OPC steps are required to generate high quality masks. Experimental results show that our flow can facilitate the mask optimization process as well as ensure a better printability.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196056"}, {"primary_key": "3389166", "vector": [], "sparse_vector": [], "title": "Reducing the overhead of authenticated memory encryption using delta encoding and ECC memory.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Data stored in an off-chip memory, such as DRAM or non-volatile main memory, can potentially be extracted or tampered by an attacker with physical access to a device. Protecting such attacks requires storing message authentication codes and counters - which incur a 22% storage overhead. In this work, we propose techniques for reducing these overheads.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196102"}, {"primary_key": "3389167", "vector": [], "sparse_vector": [], "title": "DSA-friendly detailed routing considering double patterning and DSA template assignments.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As integrated circuit technology nodes continue to shrink, dense via distribution becomes a severe challenge, requiring multiple masks to avoid spacing violations in via layers. Meanwhile, the directed self-assembly (DSA) technique shows a great promise in via printing by employing feasible guiding templates. Combining DSA with double patterning lithography can significantly reduce the number of masks for via layers. In this paper, we propose a detailed routing algorithm considering DSA with DPL based on a conflict and compatibility graph model. A net planning algorithm is developed to reduce via-dense areas and determines a prerouting nets order, while the graph model is employed to capture the feature of DSA and DPL to better guide detailed routing. Besides, DSA grouping is performed for critical vias during detailed routing to avoid attracting more vias inserted in surrounding grids to reduce via-spacing violations. Experimental results demonstrate that our routing algorithm can effectively minimize the number of via spacing violations, with an even smaller total via count.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196030"}, {"primary_key": "3389168", "vector": [], "sparse_vector": [], "title": "S2FA: an accelerator automation framework for heterogeneous computing in datacenters.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Big data analytics using the JVM-based MapReduce framework has become a popular approach to address the explosive growth of data sizes. Adopting FPGAs in datacenters as accelerators to improve performance and energy efficiency also attracts increasing attention. However, the integration of FPGAs into such JVM-based frameworks raises the challenge of poor programmability. Programmers must not only rewrite Java/Scala programs to C/C++ or OpenCL, but, to achieve high performance, they must also take into consideration the intricacies of FPGAs. To address this challenge, we present S2FA (Spark-to-FPGA-Accelerator), an automation framework that generates FPGA accelerator designs from Apache Spark programs written in Scala. S2FA bridges the semantic gap between object-oriented languages and HLS C while achieving high performance using learning-based design space exploration. Evaluation results show that our generated FPGA designs achieve up to 49.9× performance improvement for several machine learning applications compared to their corresponding implementations on the JVM.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196109"}, {"primary_key": "3389169", "vector": [], "sparse_vector": [], "title": "Developing synthesis flows without human knowledge.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Design flows are the explicit combinations of design transformations, primarily involved in synthesis, placement and routing processes, to accomplish the design of Integrated Circuits (ICs) and System-on-Chip (SoC). Mostly, the flows are developed based on the knowledge of the experts. However, due to the large search space of design flows and the increasing design complexity, developing Intellectual Property (IP)-specific synthesis flows providing high Quality of Result (QoR) is extremely challenging. This work presents a fully autonomous framework that artificially produces design-specific synthesis flows without human guidance and baseline flows, using Convolutional Neural Network (CNN). The demonstrations are made by successfully designing logic synthesis flows of three large scaled designs.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196026"}, {"primary_key": "3389170", "vector": [], "sparse_vector": [], "title": "It&apos;s hammer time: how to attack (rowhammer-based) DRAM-PUFs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Physically Unclonable Functions (PUFs) are still considered promising technology as building blocks in cryptographic protocols. While most PUFs require dedicated circuitry, recent research leverages DRAM hardware for PUFs due to its intrinsic properties and wide deployment. Recently, a new memory-based PUF was proposed that utilizes the infamous Rowhammer effect in DRAM. In this paper, we show two remote attacks on DRAM-based PUFs. First, a DoS attack that exploits the Rowhammer effect to manipulate PUF responses. Second, a modeling attack that predicts PUF responses by observing few challenge-response pairs. Our results indicate that DRAM may not be suitable for PUFs.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196065"}, {"primary_key": "3389171", "vector": [], "sparse_vector": [], "title": "An efficient Bayesian yield estimation method for high dimensional and high sigma SRAM circuits.", "authors": ["<PERSON><PERSON>", "Chang<PERSON> Yan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With increasing dimension of variation space and computational intensive circuit simulation, accurate and fast yield estimation of realistic SRAM chip remains a significant and complicated challenge. In this paper, du Experiment results show that the proposed method has an almost constant time complexity as the dimension increases, and gains 6x speedup over the state-of-the-art method in the 485D cases.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195987"}, {"primary_key": "3389172", "vector": [], "sparse_vector": [], "title": "Sign-magnitude SC: getting 10X accuracy for free in stochastic computing for deep neural networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hyeon Uk Sim", "<PERSON><PERSON><PERSON>"], "summary": "Stochastic computing (SC) is a promising computing paradigm for applications with low precision requirement, stringent cost and power restriction. One known problem with SC, however, is the low accuracy especially with multiplication. In this paper we propose a simple, yet very effective solution to the low-accuracy SC-multiplication problem, which is critical in many applications such as deep neural networks (DNNs). Our solution is based on an old concept of sign-magnitude, which, when applied to SC, has unique advantages. Our experimental results using multiple DNN applications demonstrate that our technique can improve the efficiency of SC-based DNNs by about 32X in terms of latency over using bipolar SC, with very little area overhead (about 1%).", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196113"}, {"primary_key": "3389173", "vector": [], "sparse_vector": [], "title": "Virtualsync: timing optimization by synchronizing logic waves with sequential and combinational components as delay units.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In digital circuit designs, sequential components such as flip-flops are used to synchronize signal propagations. Logic computations are aligned at and thus isolated by flip-flop stages. Although this fully synchronous style can reduce design efforts significantly, it may affect circuit performance negatively, because sequential components can only introduce delays into signal propagations instead of accelerating them. In this paper, we propose a new timing model, VirtualSync, in which signals, specially those along critical paths, are allowed to propagate through several sequential stages without flip-flops. Timing constraints are still satisfied at the boundary of the optimized circuit to maintain a consistent interface with existing designs. By removing clock-to-q delays and setup time requirements of lip-lops on critical paths, the performance of a circuit can be pushed even beyond the limit of traditional sequential designs. Experimental results demonstrate that circuit performance can be improved by up to 11.5% (average 3.1%) compared with that after thorough sizing and retiming, while the increase of area is still negligible.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196135"}, {"primary_key": "3389174", "vector": [], "sparse_vector": [], "title": "Cost-aware patch generation for multi-target function rectification of engineering change orders.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The increasing system complexity makes engineering change order (ECO) mostly inevitable and a common practice in integrated circuit design. Despite extensive research being made, prior methods are not effectively applicable to instances where rectification is to be done by simultaneously fixing multiple target points using intermediate signals. Moreover, how to efficiently generate low-cost patch functions is rarely addressed. These challenges are posed as a problem in the 2017 ICCAD CAD Contest. Based on Boolean satisfiability and interpolation, we propose a sound and complete algorithm for resource-aware patch generation of multi-target ECO. Experiments show our high quality results compared to other winning teams in the contest.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196017"}, {"primary_key": "3389175", "vector": [], "sparse_vector": [], "title": "Analysis of security of split manufacturing using machine learning.", "authors": ["<PERSON><PERSON>", "Jonathon Crandall <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This work is the first to analyze the security of split manufacturing using machine learning, based on data collected from layouts provided by industry, with 8 routing metal layers, and significant variation in wire size and routing congestion across the layers. We consider many types of layout features for machine learning including those obtained from placement, routing, and cell sizes. For the top split layer, we demonstrate dramatically better results in proximity attack compared to a recent prior work. We analyze the ranking of the features used by machine learning and show the importance of how features vary when moving to the lower layers. Since the runtime of our basic machine learning becomes prohibitively large for lower layers, we propose novel techniques to make it scalable with little sacrifice in effectiveness of the attack.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195991"}, {"primary_key": "3389176", "vector": [], "sparse_vector": [], "title": "Thundervolt: enabling aggressive voltage underscaling and timing error resilience for energy efficient deep learning accelerators.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Hardware accelerators are being increasingly deployed to boost the performance and energy efficiency of deep neural network (DNN) inference. In this paper we propose Thundervolt, a new framework that enables aggressive voltage underscaling of high-performance DNN accelerators without compromising classification accuracy even in the presence of high timing error rates. Using post-synthesis timing simulations of a DNN accelerator modeled on the Google TPU, we show that Thundervolt enables between 34%-57% energy savings on state-of-the-art speech and image recognition benchmarks with less than 1% loss in classification accuracy and no performance loss. Further, we show that Thundervolt is synergistic with and can further increase the energy efficiency of commonly used run-time DNN pruning techniques like Zero-Skip.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196129"}, {"primary_key": "3389177", "vector": [], "sparse_vector": [], "title": "FLOSS: FLOw sensitive scheduling on mobile platforms.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Today's mobile platforms have grown in sophistication to run a wide variety of frame-based applications. To deliver better QoS and energy efficiency, these applications utilize multi-flow execution, which exploits hardware-level parallelism across participating accelerators in the SoC. Our study shows that multi-flow execution increases memory pressure, and motivates us to propose a rate-based memory-scheduling scheme, called FLOSS, that considers a flow, individual frames of a flow, and any sharing of IPs across concurrent flows to schedule memory requests. Experimental results indicate that FLOSS provides 12% QoS improvement over baseline FR-FCFS scheme, and outperforms two QoS-aware schemes in multi-flow execution scenarios.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196052"}, {"primary_key": "3389178", "vector": [], "sparse_vector": [], "title": "An efficient kernel transformation architecture for binary- and ternary-weight neural network inference.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "While deep convolutional neural networks (CNNs) have emerged as the driving force of a wide range of domains, their computationally and memory intensive natures hinder the further deployment in mobile and embedded applications. Recently, CNNs with low-precision parameters have attracted much research attention. Among them, multiplier-free binary- and ternary-weight CNNs are reported to be of comparable recognition accuracy with full-precision networks, and have been employed to improve the hardware efficiency. However, even with the weights constrained to binary and ternary values, large-scale CNNs still require billions of operations in a single forward propagation pass.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3195988"}, {"primary_key": "3389179", "vector": [], "sparse_vector": [], "title": "Generalized augmented lagrangian and its applications to VLSI global placement.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Global placement dominates the circuit placement process in its solution quality and efficiency. With increasing design complexity and various design constraints, it is desirable to develop an efficient, high-quality global placement algorithm for modern large-scale circuit designs. In this paper, we first analyze the properties of four nonlinear optimization methods (the quadratic penalty method, the <PERSON><PERSON><PERSON> multiplier method, and two augmented Lagrangian methods) for global placement, and then develop a generalized augmented Lagrangian method to solve this problem. Our proposed method preserves the advantages of the quadratic penalty method and the augmented Lagrangian method, and provides a smooth progress from the quadratic penalty method to the augmented Lagrangian method. We prove that the proposed generalized augmented Lagrangian method is globally convergent for the original global placement problem, even with different constraints. Compared with the other four popular optimization methods, experimental results show that our method achieves the best quality and is robust for handling different objectives. In particular, our generalized augmented Lagrangian formulation is theoretically sound and can solve generic large-scale constrained nonlinear optimization problems, which are widely used in many fields.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196057"}, {"primary_key": "3389180", "vector": [], "sparse_vector": [], "title": "Efficient and reliable power delivery in voltage-stacked manycore system with hybrid charge-recycling regulators.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Yazhou Zu", "<PERSON>", "<PERSON><PERSON>"], "summary": "Voltage stacking (VS) fundamentally improves power delivery efficiency (PDE) by series-stacking multiple voltage domains to eliminate explicit step-down voltage conversion and reduce energy loss along the power delivery path. However, it suffers from aggravated supply noise, preventing its adoption in mainstream computing systems. In this paper, we investigate a practical approach to enabling efficient and reliable power delivery in voltage-stacked manycore systems that can ensure worst-case supply noise reliability without excessive costly over-design. We start by developing an analytical model to capture the essential noise behaviors in VS. It allows us to identify dominant noise contributor and derive the worst-case conditions. With this in-depth understanding, we propose a hybrid voltage regulation solution to effectively mitigate noise with worst-case guarantees. When evaluated with real-world benchmarks, our solution can achieve 93.8% power delivery efficiency, an improvement of 13.9% over the conventional baseline.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": "10.1145/3195970.3196037"}, {"primary_key": "3529301", "vector": [], "sparse_vector": [], "title": "Proceedings of the 55th Annual Design Automation Conference, DAC 2018, San Francisco, CA, USA, June 24-29, 2018", "authors": [], "summary": "No abstract available.", "published": "2018-01-01", "category": "dac", "pdf_url": "", "sub_summary": "", "source": "dac", "doi": ""}]