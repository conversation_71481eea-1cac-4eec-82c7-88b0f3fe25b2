[{"primary_key": "3767118", "vector": [], "sparse_vector": [], "title": "Learning Multi-Item Auctions with (or without) <PERSON><PERSON>.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We provide algorithms that learn simple auctions whose revenue is approximately optimal in multi-item multi-bidder settings, for a wide range of bidder valuations including unit-demand, additive, constrained additive, XOS, and subadditive. We obtain our learning results in two settings. The first is the commonly studied setting where sample access to the bidders' distributions over valuations is given, for both regular distributions and arbitrary distributions with bounded support. Here, our algorithms require polynomially many samples in the number of items and bidders. The second is a more general max-min learning setting that we introduce, where we are given \"approximate distributions,\" and we seek to compute a mechanism whose revenue is approximately optimal simultaneously for all \"true distributions\" that are close to the ones we were given. These results are more general in that they imply the sample-based results, and are also applicable in settings where we have no sample access to the underlying distributions but have estimated them indirectly via market research or by observation of bidder behavior in previously run, potentially non-truthful auctions. All our results hold for valuation distributions satisfying the standard (and necessary) independence-across-items property. They also generalize and improve upon recent works of <PERSON><PERSON> and <PERSON> [25] and <PERSON><PERSON><PERSON><PERSON> and Rough<PERSON>en [32], which have provided algorithms that learn approximately optimal multi-item mechanisms in more restricted settings with additive, subadditive and unit-demand valuations using sample access to distributions. We generalize these results to the complete unit-demand, additive, and XOS setting, to i.i.d. subadditive bidders, and to the max-min setting. Our results are enabled by new uniform convergence bounds for hypotheses classes under product measures. Our bounds result in exponential savings in sample complexity compared to bounds derived by bounding the VC dimension and are of independent interest.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.54"}, {"primary_key": "3767119", "vector": [], "sparse_vector": [], "title": "The Ising Partition Function: Zeros and Deterministic Approximation.", "authors": ["Jingcheng Liu", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study the problem of approximating the partition function of the ferromagnetic Ising model in graphs and hypergraphs. Our first result is a deterministic approximation scheme (an FPTAS) for the partition function in bounded degree graphs that is valid over the entire range of parameters β (the interaction) and λ (the external field), except for the case |λ| = 1 (the \"zero-field\" case). A randomized algorithm (FPRAS) for all graphs, and all β, λ, has long been known. Unlike most other deterministic approximation algorithms for problems in statistical physics and counting, our algorithm does not rely on the \"decay of correlations\" property. Rather, we exploit and extend machinery developed recently by <PERSON><PERSON><PERSON>, and <PERSON> and <PERSON>, based on the location of the complex zeros of the partition function, which can be seen as an algorithmic realization of the classical Lee-Yang approach to phase transitions. Our approach extends to the more general setting of the <PERSON><PERSON> model on hypergraphs of bounded degree and edge size, where no previous algorithms (even randomized) were known for a wide range of parameters. In order to achieve this extension, we establish a tight version of the Lee-Yang theorem for the Ising model on hypergraphs, improving a classical result of <PERSON> and <PERSON>.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.95"}, {"primary_key": "3767120", "vector": [], "sparse_vector": [], "title": "On Learning Mixtures of Well-Separated Gaussians.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the problem of efficiently learning mixtures of a large number of spherical Gaussians, when the components of the mixture are well separated. In the most basic form of this problem, we are given samples from a uniform mixture of k standard spherical Gaussians with means μ 1 , . . . , μ k ∈ ℝ d , and the goal is to estimate the means up to accuracy δ using poly(k, d, 1/δ) samples. In this work, we study the following question: what is the minimum separation needed between the means for solving this task? The best known algorithm due to <PERSON><PERSON><PERSON><PERSON> and <PERSON> [JCS<PERSON> 2004] requires a separation of roughly min{k, d}1/4. On the other hand, <PERSON><PERSON><PERSON> and <PERSON><PERSON> [FOCS 2010] showed that with separation o(1), exponentially many samples are required. We address the significant gap between these two bounds, by showing the following results.; We show that with separation o(√(log k)), superpolynomially many samples are required. In fact, this holds even when the k means of the Gaussians are picked at random in d = O(log k) dimensions.; We show that with separation Ω(√(log k)), picked at random in d = O(log k) dimensions. poly(k, d, 1/δ) samples suffice. Notice that the bound on the separation is independent of δ. This result is based on a new and efficient \"accuracy boosting\" algorithm that takes as input coarse estimates of the true means and in time (and samples) poly(k, d, 1/δ) outputs estimates of the means up to arbitrarily good accuracy δ assuming the separation between the means is Ω(min{√(log k), √d}) (independently of δ). The idea of the algorithm is to iteratively solve a \"diagonally dominant\" system of non-linear equations. We also (1) present a computationally efficient algorithm in d = O(1) dimensions with only Ω(√d) separation, and (2) extend our results to the case that components might have different weights and variances. These results together essentially characterize the optimal order of separation between components that is needed to learn a mixture of k spherical Gaussians with polynomial samples.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.17"}, {"primary_key": "3767121", "vector": [], "sparse_vector": [], "title": "Fine-Grained Complexity of Analyzing Compressed Data: Quantifying Improvements over Decompress-and-Solve.", "authors": ["<PERSON>", "Arturs Backurs", "<PERSON>", "<PERSON>"], "summary": "Can we analyze data without decompressing it? As our data keeps growing, understanding the time complexity of problems on compressed inputs, rather than in convenient uncompressed forms, becomes more and more relevant. Suppose we are given a compression of size n of data that originally has size N, and we want to solve a problem with time complexity T(·). The naive strategy of \"decompress-and-solve\" gives time T(N), whereas \"the gold standard\" is time T(n): to analyze the compression as efficiently as if the original data was small. We restrict our attention to data in the form of a string (text, files, genomes, etc.) and study the most ubiquitous tasks. While the challenge might seem to depend heavily on the specific compression scheme, most methods of practical relevance (Lempel-Ziv-family, dictionary methods, and others) can be unified under the elegant notion of Grammar-Compressions. A vast literature, across many disciplines, established this as an influential notion for Algorithm design. We introduce a direly needed framework for proving (conditional) lower bounds in this field, allowing us to assess whether decompress-and-solve can be improved, and by how much. Our main results are: (1) The O(nN√(log N/n)) bound for LCS and the O(min{N log N, nM}) bound for Pattern Matching with Wildcards are optimal up to N o(1) factors, under the Strong Exponential Time Hypothesis. (Here, M denotes the uncompressed length of the compressed pattern.) (2) Decompress-and-solve is essentially optimal for ContextFree Grammar Parsing and RNA Folding, under the k-Clique conjecture. (3) We give an algorithm showing that decompress-and-solve is not optimal for Disjointness.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.26"}, {"primary_key": "3767122", "vector": [], "sparse_vector": [], "title": "Distributed PCP Theorems for Hardness of Approximation in P.", "authors": ["<PERSON>", "<PERSON>via<PERSON>", "<PERSON><PERSON>"], "summary": "We present a new distributed model of probabilistically checkable proofs (PCP). A satisfying assignment x ∈ {0, 1} n to a CNF formula φ is shared between two parties, where <PERSON> knows x 1 , ... , x n/2 , <PERSON> knows x n/2+1 , . . . , xn, and both parties know φ. The goal is to have <PERSON> and <PERSON> jointly write a PCP that x satisfies φ, while exchanging little or no information. Unfortunately, this model as-is does not allow for nontrivial query complexity. Instead, we focus on a non-deterministic variant, where the players are helped by <PERSON>, a third party who knows all of x. Using our framework, we obtain, for the first time, PCP-like reductions from the Strong Exponential Time Hypothesis (SETH) to approximation problems in P. In particular, under SETH we show that there are no trulysubquadratic approximation algorithms for Maximum Inner Product over {0, 1}-vectors, LCS Closest Pair over permutations, Approximate Partial Match, Approximate Regular Expression Matching, and Diameter in Product Metric. All our inapproximability factors are nearly-tight. In particular, for the first three problems we obtain nearly-polynomial factors of 2 (log n) 1-o(1) ; only (1+o(1))-factor lower bounds (under SETH) were known before. As an additional feature of our reduction, we obtain new SETH lower bounds for the exact \"monochromatic\" Closest Pair problem in the Euclidean, Manhattan, and Hamming metrics.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.12"}, {"primary_key": "3767123", "vector": [], "sparse_vector": [], "title": "Optimal Las Vegas Locality Sensitive Data Structures.", "authors": ["<PERSON>"], "summary": "We show that approximate similarity (near neighbour) search can be solved in high dimensions with performance matching state of the art (data independent) Locality Sensitive Hashing, but with a guarantee of no false negatives. Specifically we give two data structures for common problems. For c-approximate near neighbour in Hamming space, for which we get query time dn^{1/c+o(1)} and space dn^{1+1/c+o(1)} matching that of [<PERSON><PERSON> and <PERSON>, 1998] and answering a long standing open question from [<PERSON><PERSON>, 2000a] and [<PERSON>, 2016] in the affirmative. For (s1, s2)-approximate Jaccard similarity we get query time d^2n^{ρ+o(1)} and space d^2n^{1+ρ+o(1), ρ= [log (1+s1)/(2s1)]/[log (1+s2)/(2s2)], when sets have equal size, matching the performance of [<PERSON> and <PERSON>, 2017].We use space partitions as in classic LSH, but construct these using a combination of brute force, tensoring and splitter functions à la [<PERSON> et al., 1995]. We also show two dimensionality reduction lemmas with 1-sided error.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.91"}, {"primary_key": "3767124", "vector": [], "sparse_vector": [], "title": "Better Guarantees for k-Means and Euclidean k-Median by Primal-Dual Algorithms.", "authors": ["<PERSON>", "<PERSON><PERSON>Fard", "<PERSON><PERSON>", "<PERSON>"], "summary": "Clustering is a classic topic in optimization with k-means being one of the most fundamental such problems. In the absence of any restrictions on the input, the best known algorithm for k-means with a provable guarantee is a simple local search heuristic yielding an approximation guarantee of 9 + ε, a ratio that is known to be tight with respect to such methods. We overcome this barrier by presenting a new primal-dual approach that allows us to (1) exploit the geometric structure of k-means and (2) to satisfy the hard constraint that at most k clusters are selected without deteriorating the approximation guarantee. Our main result is a 6.357-approximation algorithm with respect to the standard LP relaxation. Our techniques are quite general and we also show improved guarantees for the general version of k-means where the underlying metric is not required to be Euclidean and for k-median in Euclidean metrics.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.15"}, {"primary_key": "3767125", "vector": [], "sparse_vector": [], "title": "First Efficient Convergence for Streaming k-PCA: A Global, Gap-Free, and Near-Optimal Rate.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>zhi Li"], "summary": "We study streaming principal component analysis (PCA), that is to find, in O(dk) space, the top k eigenvectors of a d × d hidden matrix Σ with online vectors drawn from covariance matrix Σ. We provide global convergence for <PERSON><PERSON>'s algorithm which is popularly used in practice but lacks theoretical understanding for k > 1. We also provide a modified variant Oja ++ that runs even faster than <PERSON><PERSON>'s. Our results match the information theoretic lower bound in terms of dependency on error, on eigengap, on rank k, and on dimension d, up to poly-log factors. In addition, our convergence rate can be made gap-free, that is proportional to the approximation error and independent of the eigengap. In contrast, for general rank k, before our work (1) it was open to design any algorithm with efficient global convergence rate; and (2) it was open to design any algorithm with (even local) gap-free convergence rate in O(dk) space.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.51"}, {"primary_key": "3767126", "vector": [], "sparse_vector": [], "title": "Much Faster Algorithms for Matrix Scaling.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>zhi Li", "<PERSON>", "<PERSON><PERSON>"], "summary": "We develop several efficient algorithms for the classical Matrix Scaling problem, which is used in many diverse areas, from preconditioning linear systems to approximation of the permanent. On an input n×n matrix A, this problem asks to find diagonal (scaling) matrices X and Y (if they exist), so that XAY ε-approximates a doubly stochastic matrix, or more generally a matrix with prescribed row and column sums. We address the general scaling problem as well as some important special cases. In particular, if A has m nonzero entries, and if there exist X and Y with polynomially large entries such that XAY is doubly stochastic, then we can solve the problem in total complexity Õ(m + n 4/3 ). This greatly improves on the best known previous results, which were either Õ(n 4 ) or O(mn 1/2 /ε). Our algorithms are based on tailor-made first and second order techniques, combined with other recent advances in continuous optimization, which may be of independent interest for solving similar problems.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.87"}, {"primary_key": "3767127", "vector": [], "sparse_vector": [], "title": "Testing Hereditary Properties of Ordered Graphs and Matrices.", "authors": ["Noga Alon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We consider properties of edge-colored vertex-ordered graphs - graphs with a totally ordered vertex set and a finite set of possible edge colors - showing that any hereditary property of such graphs is strongly testable, i.e., testable with a constant number of queries. We also explain how the proof can be adapted to show that any hereditary property of two-dimensional matrices over a finite alphabet (where row and column order is not ignored) is strongly testable. The first result generalizes the result of <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [FOCS'05; SICOMP'08], who showed that any hereditary graph property (without vertex order) is strongly testable. The second result answers and generalizes a conjecture of <PERSON><PERSON>, <PERSON> and <PERSON> [SICOMP'07] concerning testing of matrix properties. The testability is proved by establishing a removal lemma for vertex-ordered graphs. It states that if such a graph is far enough from satisfying a certain hereditary property, then most of its induced vertex-ordered subgraphs on a certain (large enough) constant number of vertices do not satisfy the property as well. The proof bridges the gap between techniques related to the regularity lemma, used in the long chain of papers investigating graph testing, and string testing techniques. Along the way we develop a Ramsey-type lemma for multipartite graphs with \"undesirable\" edges, stating that one can find a Ramsey-type structure in such a graph, in which the density of the undesirable edges is not much higher than the density of those edges in the graph.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.83"}, {"primary_key": "3767128", "vector": [], "sparse_vector": [], "title": "Optimal Compression of Approximate Inner Products and Dimension Reduction.", "authors": ["Noga Alon", "Bo&apos;az Klartag"], "summary": "Let X be a set of n points of norm at most 1 in the Euclidean space R^k, and suppose ≥0. An ≥-distance sketch for X is a data structure that, given any two points of X enables one to recover the square of the (Euclidean) distance between them up to an additive} error of ≥. Let f(n,k,≥) denote the minimum possible number of bits of such a sketch. Here we determine f(n,k,≥) up to a constant factor for all n ≥ k ≥ 1 and all ≥ ≥ \\frac{1}{n^{0.49}}. Our proof is algorithmic, and provides an efficient algorithm for computing a sketch of size O(f(n,k,≥)/n) for each point, so that the square of the distance between any two points can be computed from their sketches up to an additive error of ≥ in time linear in the length of the sketches. We also discuss the case of smaller ≥2/√ n and obtain some new results about dimension reduction in this range. In particular, we show that for any such ≥ and any k ≤ t=\\frac{\\log (2+≥^2 n)}{≥^2} there are configurations of n points in R^k that cannot be embedded in R^{ℓ} for ℓ", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.65"}, {"primary_key": "3767129", "vector": [], "sparse_vector": [], "title": "Simply Exponential Approximation of the Permanent of Positive Semidefinite Matrices.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We design a deterministic polynomial time cn approximation algorithm for the permanent of positive semidefinite matrices where c = γ+1 ≃ 4:84. We write a natural convex relaxation and show that its optimum solution gives a cn approximation of the permanent. We further show that this factor is asymptotically tight by constructing a family of positive semidefinite matrices. We also show that our result implies an approximate version of the permanent-ontop conjecture, which was recently refuted in its original form; we show that the permanent is within a cn factor of the top eigenvalue of the Schur power matrix.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.89"}, {"primary_key": "3767130", "vector": [], "sparse_vector": [], "title": "Quantum SDP-Solvers: Better Upper and Lower Bounds.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON> and <PERSON><PERSON><PERSON> recently gave quantum algorithms for approximately solving semidefinite programs, which in some regimes are faster than the best-possible classical algorithms in terms of the dimension n of the problem and the number m of constraints, but worse in terms of various other parameters. In this paper we improve their algorithms in several ways, getting better dependence on those other parameters. To this end we develop new techniques for quantum algorithms, for instance a general way to efficiently implement smooth functions of sparse Hamiltonians, and a generalized minimum-finding procedure.We also show limits on this approach to quantum SDP-solvers, for instance for combinatorial optimizations problems that have a lot of symmetry. Finally, we prove some general lower bounds showing that in the worst case, the complexity of every quantum LP-solver (and hence also SDP-solver) has to scale linearly with mn when m is approximately n, which is the same as classical.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.44"}, {"primary_key": "3767131", "vector": [], "sparse_vector": [], "title": "Exponentially-Hard Gap-CSP and Local PRG via Local Hardcore Functions.", "authors": ["<PERSON>"], "summary": "The gap-ETH assumption (<PERSON><PERSON> 2016; <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> 2016) asserts that it is exponentially-hard to distinguish between a satisfiable 3-CNF formula and a 3-CNF formula which is at most 0.99-satisfiable. We show that this assumption follows from the exponential hardness of finding a satisfying assignment for smooth 3-CNFs. Here smoothness means that the number of satisfying assignments is not much smaller than the number of almost-satisfying assignments. We further show that the latter (smooth-ETH) assumption follows from the exponential hardness of solving constraint satisfaction problems over well-studied distributions, and, more generally, from the existence of any exponentially-hard locally-computable one-way function. This confirms a conjecture of <PERSON><PERSON> (ECCC 2016). We also prove an analogous result in the cryptographic setting. Namely, we show that the existence of exponentially-hard locally-computable pseudorandom generator with linear stretch (el-PRG) follows from the existence of an exponentially-hard locally-computable almost regular one-way functions.None of the above assumptions (gap-ETH and el-PRG) was previously known to follow from the hardness of a search problem. Our results are based on a new construction of general (GL-type) hardcore functions that, for any exponentially-hard one-way function, output linearly many hardcore bits, can be locally computed, and consume only a linear amount of random bits. We also show that such hardcore functions have several other useful applications in cryptography and complexity theory.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.82"}, {"primary_key": "3767132", "vector": [], "sparse_vector": [], "title": "Weighted k-Server Bounds via Combinatorial Dichotomies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "The weighted k-server problem is a natural generalization of the k-server problem where each server has a different weight. We consider the problem on uniform metrics, which corresponds to a natural generalization of paging. Our main result is a doubly exponential lower bound on the competitive ratio of any deterministic online algorithm, that essentially matches the known upper bounds for the problem and closes a large and long-standing gap. The lower bound is based on relating the weighted k-server problem to a certain combinatorial problem and proving a Ramsey-theoretic lower bound for it. This combinatorial connection also reveals several structural properties of low cost feasible solutions to serve a sequence of requests. We use this to show that the generalized Work Function Algorithm achieves an almost optimum competitive ratio, and to obtain new refined upper bounds on the competitive ratio for the case of d different weight classes.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.52"}, {"primary_key": "3767133", "vector": [], "sparse_vector": [], "title": "Generalized Uniformity Testing.", "authors": ["<PERSON><PERSON><PERSON>", "Clément L. <PERSON>"], "summary": "In this work, we revisit the problem of uniformity testing of discrete probability distributions. A fundamental problem in distribution testing, testing uniformity over a known domain has been addressed over a significant line of works, and is by now fully understood. The complexity of deciding whether an unknown distribution is uniform over its unknown (and arbitrary) support, however, is much less clear. Yet, this task arises as soon as no prior knowledge on the domain is available, or whenever the samples originate from an unknown and unstructured universe.In this work, we introduce and study this generalized uniformity testing question, and establish nearly tight upper and lower bound showing that - quite surprisingly - its sample complexity significantly differs from the known-domain case. Moreover, our algorithm is intrinsically adaptive, in contrast to the overwhelming majority of known distribution testing algorithms.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.86"}, {"primary_key": "3767134", "vector": [], "sparse_vector": [], "title": "On the Quantitative Hardness of CVP.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON>"], "summary": "For odd integers p ≥ 1 (and p = ∞), we show that the Closest Vector Problem in the ℓ p norm (CVP p ) over rank n lattices cannot be solved in 2 (1-ε)n time for any constant ε > 0 unless the Strong Exponential Time Hypothesis (SETH) fails. We then extend this result to \"almost all\" values of p ≥ 1, not including the even integers. This comes tantalizingly close to settling the quantitative time complexity of the important special case of CVP 2 (i.e., CVP in the Euclidean norm), for which a 2 n+o(n) -time algorithm is known. In particular, our result applies for any p = p(n) ≠ 2 that approaches 2 as n → ∞. We also show a similar SETH-hardness result for SVP ∞ ; hardness of approximating CVP p to within some constant factor under the so-called Gap-ETH assumption; and other hardness results for CVP p and CVPP p for any 1 ≤ p <; ∞ under different assumptions.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.11"}, {"primary_key": "3767135", "vector": [], "sparse_vector": [], "title": "Removing Depth-Order Cycles among Triangles: An Efficient Algorithm Generating Triangular Fragments.", "authors": ["<PERSON>"], "summary": "More than 25 years ago, inspired by applications in computer graphics, <PERSON><PERSON><PERSON> et al. (FOCS 1991) studied the following question: Is it possible to cut any set of n lines or other objects in R 3 into a subquadratic number of fragments such that the resulting fragments admit a depth order? They managed to prove an O(n 9/4 ) bound on the number of fragments, but only for the very special case of bipartite weavings of lines. Since then only little progress was made, until a recent breakthrough by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (STOC 2016) who showed that O(n 3/2 polylog n) fragments suffice for any set of lines. In a follow-up paper <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> (SODA 2017) proved an O(n 3/2+ε ) bound for triangles, but their method uses high-degree algebraic arcs to perform the cuts. Hence, the resulting pieces have curved boundaries. Moreover, their method uses polynomial partitions, for which currently no algorithm is known. Thus the most natural version of the problem is still wide open: Is it possible to cut any collection of n disjoint triangles in R3 into a subquadratic number of triangular fragments that admit a depth order? And if so, can we compute the cuts efficiently? We answer this question by presenting an algorithm that cuts any set of n disjoint triangles in R3 into O(n 7/4 polylog n) triangular fragments that admit a depth order. The running time of our algorithm is O(n 3.69 ). We also prove a refined bound that depends on the number, K, of intersections between the projections of the triangle edges onto the xy-plane: we show that O(n 1+ε + n 1/4 K 3/4 polylog n) fragments suffice to obtain a depth order. This result extends to xy-monotone surface patches bounded by a constant number of bounded-degree algebraic arcs in general position, constituting the first subquadratic bound for surface patches. Finally, as a byproduct of our approach we obtain a faster algorithm to cut a set of lines into O(n 3/2 polylog n) fragments that admit a depth order. Our algorithm for lines runs in O(n 5.38 ) time, while the previous algorithm uses O(n 8.77 ) time.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.33"}, {"primary_key": "3767136", "vector": [], "sparse_vector": [], "title": "Weak <PERSON>, Polynomial Folds and Approximate Optimization over the Sphere.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>en<PERSON><PERSON>wami", "<PERSON><PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON>"], "summary": "We consider the following basic problem: given an n-variate degree-d homogeneous polynomial f with real coefficients, compute a unit vector x in R̂n that maximizes abs(f(x)). Besides its fundamental nature, this problem arises in diverse contexts ranging from tensor and operator norms to graph expansion to quantum information theory. The homogeneous degree-2 case is efficiently solvable as it corresponds to computing the spectral norm of an associated matrix, but the higher degree case is NP-hard. We give approximation algorithms for this problem that offer a trade-off between the approximation ratio and running time: in n̂O(q) time, we get an approximation within factor (O(n)/q)̂(d/2-1) for arbitrary polynomials, (O(n)/q)̂(d/4-1/2) for polynomials with non-negative coefficients, and (m /q)̂(1/2) for sparse polynomials with m monomials. The approximation guarantees are with respect to the optimum of the level-q sum-of-squares (SoS) SDP relaxation of the problem (though our algorithms do not rely on actually solving the SDP). Known polynomial time algorithms for this problem rely on \"decoupling lemmas.\" Such tools are not capable of offering a trade-off like our results as they blow up the number of variables by a factor equal to the degree. We develop new decoupling tools that are more efficient in the number of variables at the expense of less structure in the output polynomials. This enables us to harness the benefits of higher level SoS relaxations. Our decoupling methods also work with \"folded polynomials,\" which are polynomials with polynomials as coefficients. This allows us to exploit easy substructures (such as quadratics) by considering them as coefficients in our algorithms. We complement our algorithmic results with some polynomially large integrality gaps for d-levels of the SoS relaxation. For general polynomials this follows from known results for random polynomials, which yield a gap of Omega(n)̂(d/4-1/2). For polynomials with non-negative coefficients, we prove an Omega(n̂(1/6) /polylogs) gap for the degree-4 case, based on a novel distribution of 4-uniform hypergraphs. We establish an n̂Omega(d) gap for general degree-d, albeit for a slightly weaker (but still very natural) relaxation. Toward this, we give a method to lift a level-4 solution matrix M to a higher level solution, under a mild technical condition on M. From a structural perspective, our work yields worst-case convergence results on the performance of the sum-of-squareshierarchy for polynomial optimization. Despite the popularity of SoS in this context, such results were previously only known for the case of q = Omega(n).", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.97"}, {"primary_key": "3767137", "vector": [], "sparse_vector": [], "title": "Minor-Free Graphs Have Light Spanners.", "authors": ["Glencora Borradaile", "<PERSON>", "<PERSON><PERSON>"], "summary": "We show that every H-minor-free graph has a light (1+≥ilon)-spanner, resolving an open problem of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and proving a conjecture of <PERSON><PERSON><PERSON> and <PERSON> \\cite{GH12}. Our lightness bound is \\[O\\left(\\frac{\\sigma_H}{≥ilon^3}\\log \\frac{1}{≥ilon}\\right)\\] where \\sigma_H = |V(H)|√{\\log |V(H)|} is the sparsity coefficient of H-minor-free graphs. That is, it has a practical dependency on the size of the minor H. Our result also implies that the polynomial time approximation scheme (PTAS) for the Travelling Salesperson Problem (TSP) in H-minor-free graphs by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> is an efficient PTAS whose running time is 2^{O_H\\left(\\frac{1}{≥ilon^4}\\log \\frac{1}{≥ilon}\\right)}n^{O(1)} where O_<PERSON> ignores dependencies on the size of H. Our techniques significantly deviate from existing lines of research on spanners for H-minor-free graphs, but build upon the work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for spanners of general graphs[6].", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.76"}, {"primary_key": "3767138", "vector": [], "sparse_vector": [], "title": "On the Power of Statistical Zero Knowledge.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We examine the power of statistical zero knowledge proofs (captured by the complexity class SZK) and their variants. First, we give the strongest known relativized evidence that SZK contains hard problems, by exhibiting an oracle relative to which SZK (indeed, even NISZK) is not contained in the class UPP, containing those problems solvable by randomized algorithms with unbounded error. This answers an open question of <PERSON><PERSON><PERSON> from 2002. Second, we \"lift\" this oracle separation to the setting of communication complexity, thereby answering a question of <PERSON><PERSON> et al. (ICALP 2016). Third, we give relativized evidence that perfect zero knowledge proofs (captured by the class PZK) are weaker than general zero knowledge proofs. Specifically, we exhibit oracles which separate SZK from PZK, NISZK from NIPZK and PZK from coPZK. The first of these results answers a question raised in 1991 by <PERSON><PERSON> and <PERSON> (Information and Computation), and the second answers a question of <PERSON> and <PERSON> (2016). We also describe additional applications of these results outside of structural complexity. The technical core of our results is a stronger hardness amplification theorem for approximate degree, which roughly says that composing the gapped-majority function with any function of high approximate degree yields a function with high threshold degree.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.71"}, {"primary_key": "3767139", "vector": [], "sparse_vector": [], "title": "Quantum Speed-Ups for Solving Semidefinite Programs.", "authors": ["Fernando G. S. L. Brandão", "<PERSON><PERSON><PERSON>"], "summary": "We give a quantum algorithm for solving semidefinite programs (SDPs). It has worst-case running time n 1/2 m 1/2 s 2 poly(log(n), log(m), R, r, 1/δ), with n and s the dimension and row-sparsity of the input matrices, respectively, m the number of constraints, δ the accuracy of the solution, and R, r upper bounds on the size of the optimal primal and dual solutions, respectively. This gives a square-root unconditional speed-up over any classical method for solving SDPs both in n and m. We prove the algorithm cannot be substantially improved (in terms of n and m) giving a Ω(n 1/2 + m 2 ) quantum lower bound for solving semidefinite programs with constant s, R, r and δ. The quantum algorithm is constructed by a combination of quantum Gibbs sampling and the multiplicative weight method. In particular it is based on a classical algorithm of <PERSON><PERSON><PERSON> and <PERSON><PERSON> for approximately solving SDPs. We present a modification of their algorithm to eliminate the need for solving an inner linear program which may be of independent interest.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.45"}, {"primary_key": "3767140", "vector": [], "sparse_vector": [], "title": "A Rounds vs. Communication Tradeoff for Multi-Party Set Disjointness.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the set disjointess problem, we have k players, each with a private input X i ⊆ [n], and the goal is for the players to determine whether or not their sets have a global intersection. The players communicate over a shared blackboard, and we charge them for each bit that they write on the board. We study the trade-off between the number of interaction rounds we allow the players, and the total number of bits they must send to solve set disjointness. We show that if R rounds of interaction are allowed, the communication cost is Ω̃(nk 1/R /R 4 ), which is nearly tight. We also leverage our proof to show that wellfare maximization with unit demand bidders cannot be solved efficiently in a small number of rounds: here, we have k players bidding on n items, and the goal is to find a matching between items and player that bid on them which approximately maximizes the total number of items assigned. It was previously shown by <PERSON><PERSON> et. al. that Ω(log log k) rounds of interaction are required to find an assignment which achieves a constant approximation to the maximum-wellfare assignment, even if each player is allowed to write n ϵ(R) bits on the board in each round, where ϵ(R) = exp(-R). We improve this lower bound to Ω(log k/log log k), which is known to be tight up to a log log k factor.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.22"}, {"primary_key": "3767141", "vector": [], "sparse_vector": [], "title": "A Dichotomy for Regular Expression Membership Testing.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We study regular expression membership testing: Given a regular expression of size m and a string of size n, decide whether the string is in the language described by the regular expression. Its classic O(nm) algorithm is one of the big success stories of the 70s, which allowed pattern matching to develop into the standard tool that it is today.Many special cases of pattern matching have been studied that can be solved faster than in quadratic time. However, a systematic study of tractable cases was made possible only recently, with the first conditional lower bounds reported by <PERSON><PERSON> and <PERSON>k [FOCS16]. Restricted to any type of homogeneous regular expressions of depth 2 or 3, they either presented a near-linear time algorithm or a quadratic conditional lower bound, with one exception known as the Word Break problem.In this paper we complete their work as follows:• We present two almost-linear time algorithms that generalize all known almost-linear time algorithms for special cases of regular expression membership testing.• We classify all types, except for the Word Break problem, into almost-linear time or quadratic time assuming the Strong Exponential Time Hypothesis. This extends the classification from depth 2 and 3 to any constant depth.• For the Word Break problem we give an improved O(nm1/3 + m) algorithm. Surprisingly, we also prove a matching conditional lower bound for combinatorial algorithms. This establishes Word Break as the only intermediate problem.In total, we prove matching upper and lower bounds for any type of bounded-depth homogeneous regular expressions, which yields a full dichotomy for regular expression member-ship testing.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.36"}, {"primary_key": "3767142", "vector": [], "sparse_vector": [], "title": "A Dichotomy Theorem for Nonuniform CSPs.", "authors": ["<PERSON>"], "summary": "In a non-uniform Constraint Satisfaction problem CSP(Γ), where Γ is a set of relations on a unite set A, the goal is to und an assignment of values to variables subject to constraints imposed on speciued sets of variables using the relations from Γ. The Dichotomy Conjecture for the non-uniform CSP states that for every constraint language Γ the problem CSP(Γ) is either solvable in polynomial time or is NP-complete. It was proposed by <PERSON><PERSON> and <PERSON><PERSON><PERSON> in their seminal 1993 paper. In this paper we confirm the Dichotomy Conjecture.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.37"}, {"primary_key": "3767143", "vector": [], "sparse_vector": [], "title": "A Nearly Optimal Lower Bound on the Approximate Degree of AC0.", "authors": ["<PERSON>", "<PERSON>"], "summary": "The approximate degree of a Boolean function f : {-1, 1} n → {-1, 1} is the least degree of a real polynomial that approximates f pointwise to error at most 1/3. We introduce a generic method for increasing the approximate degree of a given function, while preserving its computability by constant-depth circuits. Specifically, we show how to transform any Boolean function f with approximate degree d into a function F on O(n · polylog(n)) variables with approximate degree at least D = Ω(n 1/3 · d 2/3 ). In particular, if d = n 1-Ω(1) , then D is polynomially larger than d. Moreover, if f is computed by a constant-depth polynomial-size Boolean circuit, then so is F. By recursively applying our transformation, for any constant δ > 0 we exhibit an AC 0 function of approximate degree Ω(n 1-δ ). This improves over the best previous lower bound of Ω(n 2/3 ) due to <PERSON><PERSON> and <PERSON> (<PERSON><PERSON> 2004), and nearly matches the trivial upper bound of n that holds for any function. Our lower bounds also apply to (quasipolynomial-size) DNFs of polylogarithmic width. We describe several applications of these results. We give: · For any constant δ > 0, an Ω(n 1-δ ) lower bound on the quantum communication complexity of a function in AC 0 . · A Boolean function f with approximate degree at least C(f) 2-o(1) , where C(f) is the certificate complexity of f. This separation is optimal up to the o(1) term in the exponent. · Improved secret sharing schemes with reconstruction procedures in AC 0 .", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.10"}, {"primary_key": "3767144", "vector": [], "sparse_vector": [], "title": "From Gap-ETH to FPT-Inapproximability: Clique, Dominating Set, and More.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Bundit Laekhanukit", "<PERSON><PERSON>", "Danupon <PERSON>", "<PERSON>"], "summary": "We consider questions that arise from the intersection between the areas of approximation algorithms, subexponential-time algorithms, and fixed-parameter tractable algorithms. The questions, which have been asked several times (e.g., [1], [2], [3]) are whether there is a non-trivial FPT-approximation algorithm for the Maximum Clique (Clique) and Minimum Dominating Set (DomSet) problems parameterized by the size of the optimal solution. In particular, letting OPT be the optimum and N be the size of the input, is there an algorithm that runs in t(OPT) poly(N) time and outputs a solution of size f(OPT), for any functions t and f that are independent of N (for Clique, we want f(OPT) = ω(1))? In this paper, we show that both Clique and DomSet admit no non-trivial FPT-approximation algorithm, i.e., there is no o(OPT)-FPT-approximation algorithm for Clique and no f(OPT)-FPT-approximation algorithm for DomSet, for any function f (e.g., this holds even if f is an exponential or the <PERSON><PERSON>mann function). In fact, our results imply something even stronger: The best way to solve C<PERSON> and DomSet, even approximately, is to essentially enumerate all possibilities. Our results hold under the Gap Exponential Time Hypothesis (GapETH) [4], [5], which states that no 2 o(n) -time algorithm can distinguish between a satisfiable 3SAT formula and one which is not even (1 - ε)-satisfiable for some constant ε > 0. Besides Clique and DomSet, we also rule out non-trivial FPT-approximation for Maximum Balanced Biclique, the problem of finding maximum subgraphs with hereditary properties (e.g., Maximum Induced Planar Subgraph), and Maximum Induced Matching in bipartite graphs. Previously only exact versions of these problems were known to be W[1]-hard [6], [7], [8]. Additionally, we rule out k o(1) -FPT-approximation algorithm for Densest k-Subgraph although this ratio does not yet match the trivial O(k)-approximation algorithm. To the best of our knowledge, prior results only rule out constant factor approximation for Clique [9], [10] and log 1/4+ε (OPT) approximation for DomSet for any constant ε > 0 [11]. Our result on Clique significantly improves on [9], [10]. However, our result on DomSet is incomparable to [11] since their results hold under ETH while our results hold under Gap-ETH, which is a stronger assumption.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.74"}, {"primary_key": "3767145", "vector": [], "sparse_vector": [], "title": "A Time Hierarchy Theorem for the LOCAL Model.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The celebrated Time Hierarchy Theorem for Turing machines states, informally, that more problems can be solved given more time. The extent to which a time hierarchy-type theorem holds in the classic distributed LOCAL model has been open for many years. In particular, it is consistent with previous results that all natural problems in the LOCAL model can be classified according to a small constant number of complexities, such as O(1), O(log* n), O(log n), 2^{O(sqrt{log n}), etc.In this paper we establish the first time hierarchy theorem for the LOCAL model and prove that several gaps exist in the LOCAL time hierarchy. Our main results are as follows:• We define an infinite set of simple coloring problems called Hierarchical 2½-Coloring. A correctly colored graph can be confirmed by simply checking the neighborhood of each vertex, so this problem fits into the class of locally checkable labeling (LCL) problems. However, the complexity of the k-level Hierarchical 2½-Coloring problem is Θ(n^{1/k}), for positive integer k. The upper and lower bounds hold for both general graphs and trees, and for both randomized and deterministic algorithms.• Consider any LCL problem on bounded degree trees. We prove an automatic-speedup theorem that states that any randomized n^{o(1)}-time algorithm solving the LCL can be transformed into a deterministic O(log n)-time algorithm. Together with a previous result, this establishes that on trees, there are no natural deterministic complexities in the ranges ω(log* n)—o(log n) or ω(log n)—n^{o(1)}.• We expose a gap in the randomized time hierarchy on general graphs. Roughly speaking, any randomized algorithm that solves an LCL problem in sublogarithmic time can be sped up to run in O(T_{LLL}) time, which is the complexity of the distributed Lovasz local lemma problem, currently known to be Ω(log log n) and 2^{O(sqrt{log log n})} on bounded degree graphs.Finally, we revisit Naor and Stockmeyers characterization of O(1)-time LOCAL algorithms for LCL problems (as order-invariant w.r.t. vertex IDs) and calculate the complexity gaps that are directly implied by their proof. For n-rings we see a ω(1)—o(log* n) complexity gap, for (sqrt{n} × √{n})-tori an ω(1)—o(sqrt{log* n}) gap, and for bounded degree trees and general graphs, an ω(1)—o(log(log* n)) complexity gap.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.23"}, {"primary_key": "3767146", "vector": [], "sparse_vector": [], "title": "Hashing-Based-Estimators for Kernel Density in High Dimensions.", "authors": ["<PERSON>", "Paris Siminelakis"], "summary": "Given a set of points P ⊂ ℝ d and a kernel k, the Kernel Density Estimate at a point x ∈ ℝ d is defined as KDE P (x) = 1/|P| Σy ∈P k(x, y). We study the problem of designing a data structure that given a data set P and a kernel function, returns approximations to the kernel density of a query point in sublinear time. We introduce a class of unbiased estimators for kernel density implemented through locality-sensitive hashing, and give general theorems bounding the variance of such estimators. These estimators give rise to efficient data structures for estimating the kernel density in high dimensions for a variety of commonly used kernels. Our work is the first to provide data-structures with theoretical guarantees that improve upon simple random sampling in high dimensions.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.99"}, {"primary_key": "3767147", "vector": [], "sparse_vector": [], "title": "Approximating the Held-Karp Bound for Metric TSP in Nearly-Linear Time.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We give a nearly linear-time randomized approximation scheme for the Held-Ka<PERSON> bound [22] for Metric-TSP. Formally, given an undirected edge-weighted graph G = (V, ε) on m edges and ε > 0, the algorithm outputs in O(m log 4 n/ε 2 ) time, with high probability, a (1 + ε)-approximation to the Held-Ka<PERSON> bound on the Metric-TSP instance induced by the shortest path metric on G. The algorithm can also be used to output a corresponding solution to the Subtour Elimination LP. We substantially improve upon the O(m 2 log 2 (m)/ε 2 ) running time achieved previously by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.78"}, {"primary_key": "3767148", "vector": [], "sparse_vector": [], "title": "Boolean Unateness Testing with Õ(n3/4) Adaptive Queries.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We give an adaptive algorithm that tests whether an unknown Boolean function f : {0, 1} n → {0, 1} is unate (i.e. every variable of f is either non-decreasing or non-increasing) or ε-far from unate with one-sided error and Õ(n 3/4 /ϵ 2 ) many queries. This improves on the best adaptive O(n/ϵ)-query algorithm from <PERSON><PERSON><PERSON>, Cha<PERSON>ty, Pallavoor, <PERSON><PERSON><PERSON><PERSON> and Se<PERSON><PERSON><PERSON> [1] when 1/ϵ 1/4 . Combined with the Ω̃(n)query lower bound for non-adaptive algorithms with one-sided error of [2], [3], we conclude that adaptivity helps for the testing of unateness with one-sided error. A crucial component of our algorithm is a new subroutine for finding bi-chromatic edges in the Boolean hypercube called adaptive edge search.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.85"}, {"primary_key": "3767149", "vector": [], "sparse_vector": [], "title": "Fast and Compact Exact Distance Oracle for Planar Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For a given a graph, a distance oracle is a data structure that answers distance queries between pairs of vertices. We introduce an O(n 5/3 )-space distance oracle which answers exact distance queries in O(log n) time for n-vertex planar edge-weighted digraphs. All previous distance oracles for planar graphs with truly subquadratic space (i.e., space O(n 2-ϵ) for some constant ϵ > 0) either required query time polynomial in n or could only answer approximate distance queries. Furthermore, we show how to trade-off time and space: for any S ≥ n 3/2 , we show how to obtain an S-space distance oracle that answers queries in time O( n 5/2/S3/2 logn). This is a polynomial improvement over the previous planar distance oracles with o(n 1/4 ) query time.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.93"}, {"primary_key": "3767150", "vector": [], "sparse_vector": [], "title": "On the Local Structure of Stable Clustering Instances.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study the classic k-median and k-means clustering objectives in the beyond-worst-case scenario. We consider three well-studied notions of structured data that aim at characterizing real-world inputs: Distribution Stability (introduced by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, FOCS 2010); Spectral Separability (introduced by <PERSON> and <PERSON>, FOCS 2010); Perturbation Resilience (introduced by <PERSON><PERSON><PERSON> and <PERSON><PERSON>, ICS 2010). We prove structural results showing that inputs satisfying at least one of the conditions are inherently local. Namely, for any such input, any local optimum is close both in term of structure and in term of objective value to the global optima. As a corollary we obtain that the widely-used Local Search algorithm has strong performance guarantees for both the tasks of recovering the underlying optimal clustering and obtaining a clustering of small cost. This is a significant step toward understanding the success of local search heuristics in clustering applications.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.14"}, {"primary_key": "3767151", "vector": [], "sparse_vector": [], "title": "Matrix Scaling and Balancing via Box Constrained Newton&apos;s Method and Interior Point Methods.", "authors": ["<PERSON>", "Aleksander Madry", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper 1 , we study matrix scaling and balancing, which are fundamental problems in scientific computing, with a long line of work on them that dates back to the 1960s. We provide algorithms for both these problems that, ignoring logarithmic factors involving the dimension of the input matrix and the size of its entries, both run in time Õ(m log κ log 2 (1/ε)) where ε is the amount of error we are willing to tolerate. Here, κ represents the ratio between the largest and the smallest entries of the optimal scalings. This implies that our algorithms run in nearly-linear time whenever κ is quasi-polynomial, which includes, in particular, the case of strictly positive matrices. We complement our results by providing a separate algorithm that uses an interior-point method and runs in time Õ(m 3/2 log(1/ε)). In order to establish these results, we develop a new secondorder optimization framework that enables us to treat both problems in a unified and principled manner. This framework identifies a certain generalization of linear system solving that we can use to efficiently minimize a broad class of functions, which we call second-order robust. We then show that in the context of the specific functions capturing matrix scaling and balancing, we can leverage and generalize the work on Laplacian system solving to make the algorithms obtained via this framework very efficient.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.88"}, {"primary_key": "3767152", "vector": [], "sparse_vector": [], "title": "Fast Similarity Sketching.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the Similarity Sketching problem: Given a universe [u] = {0,..., u-1} we want a random function S mapping subsets A of [u] into vectors S(A) of size t, such that similarity is preserved. More precisely: Given subsets A,B of [u], define X_i = [S(A)[i] = S(B)[i]] and X = sum_{i in [t]} X_i. We want to have E[X] = t*J(A,B), where J(A,B) = |A intersect B|/|A union B| and furthermore to have strong concentration guarantees (i.e. Chernoff-style bounds) for X. This is a fundamental problem which has found numerous applications in data mining, large-scale classification, computer vision, similarity search, etc. via the classic MinHash algorithm. The vectors S(A) are also called sketches. The seminal t x MinHash algorithm uses t random hash functions h_1,..., h_t, and stores (min_{a in A} h_1(A),..., min_{a in A} h_t(A)) as the sketch of A. The main drawback of MinHash is, however, its O(t*|A|) running time, and finding a sketch with similar properties and faster running time has been the subject of several papers. Addressing this, <PERSON> et al. [NIPS12] introduced one permutation hashing (OPH), which creates a sketch of size t in O(t + |A|) time, but with the drawback that possibly some of the t entries are empty when |A| = O(t). One could argue that sketching is not necessary in this case, however the desire in most applications is to have one sketching procedure that works for sets of all sizes. Therefore, filling out these empty entries is the subject of several follow-up papers initiated by Shrivastava and Li [ICML14]. However, these densification schemes fail to provide good concentration bounds exactly in the case |A| = O(t), where they are needed. In this paper we present a new sketch which obtains essentially the best of both worlds. That is, a fast O(t log t + |A|) expected running time while getting the same strong concentration bounds as MinHash. Our new sketch can be seen as a mix between sampling with replacement and sampling without replacement. We demonstrate the power of our new sketch by considering popular applications in large-scale classification with linear SVM as introduced by Li et al. [NIPS11] as well as approximate similarity search using the LSH framework of Indyk and Motwani [STOC98]. In particular, for the j_1, j_2-approximate similarity search problem on a collection of n sets we obtain a data-structure with space usage O(n^{1+rho} + sum_{A in C} |A|) and O(n^rho * log n + |Q|) expected time for querying a set Q compared to a O(n^rho * log n * |Q|) expected query time of the classic result of Indyk and Motwani.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.67"}, {"primary_key": "3767153", "vector": [], "sparse_vector": [], "title": "Statistical Query Lower Bounds for Robust Estimation of High-Dimensional Gaussians and Gaussian Mixtures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We describe a general technique that yields the first Statistical Query lower bounds for a range of fundamental high-dimensional learning problems involving Gaussian distributions. Our main results are for the problems of (1) learning Gaussian mixture models (GMMs), and (2) robust (agnostic) learning of a single unknown Gaussian distribution. For each of these problems, we show a super-polynomial gap between the (information-theoretic) sample complexity and the computational complexity of any Statistical Query algorithm for the problem. Statistical Query (SQ) algorithms are a class of algorithms that are only allowed to query expectations of functions of the distribution rather than directly access samples. This class of algorithms is quite broad: a wide range of known algorithmic techniques in machine learning are known to be implementable using SQs. Moreover, for the unsupervised learning problems studied in this paper, all known algorithms with non-trivial performance guarantees are SQ or are easily implementable using SQs. Our SQ lower bound for Problem (1) is qualitatively matched by known learning algorithms for GMMs. At a conceptual level, this result implies that - as far as SQ algorithms are concerned - the computational complexity of learning GMMs is inherently exponential in the dimension of the latent space - even though there is no such information-theoretic barrier. Our lower bound for Problem (2) implies that the accuracy of the robust learning algorithm in [29] is essentially best possible among all polynomial-time SQ algorithms. On the positive side, we also give a new (SQ) learning algorithm for Problem (2) achieving the information-theoretically optimal accuracy, up to a constant factor, whose running time essentially matches our lower bound. Our algorithm relies on a filtering technique generalizing [29] that removes outliers based on higher-order tensors. Our SQ lower bounds are attained via a unified moment-matching technique that is useful in other contexts and may be of broader interest. Our technique yields nearly-tight lower bounds for a number of related unsupervised estimation problems. Specifically, for the problems of (3) robust covariance estimation in spectral norm, and (4) robust sparse mean estimation, we establish a quadratic statistical- computational tradeoff for SQ algorithms, matching known upper bounds. Finally, our technique can be used to obtain tight sample complexity lower bounds for high-dimensional testing problems. Specifically, for the classical problem of robustly testing an unknown mean (known covariance) Gaussian, our technique implies an information-theoretic sample lower bound that scales linearly in the dimension. Our sample lower bound matches the sample complexity of the corresponding robust learning problem and separates the sample complexity of robust testing from standard (non-robust) testing. This separation is surprising because such a gap does not exist for the corresponding learning problem.problem.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.16"}, {"primary_key": "3767154", "vector": [], "sparse_vector": [], "title": "High Dimensional Expanders Imply Agreement Expanders.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that high dimensional expanders imply derandomized direct product tests, with a number of subsets that is linear in the size of the universe. Direct product tests belong to a family of tests called agreement tests that are important components in PCP constructions and include, for example, low degree tests such as line vs. line and plane vs. plane. For a generic hypergraph, we introduce the notion of agreement expansion, which captures the usefulness of the hypergraph for an agreement test. We show that explicit bounded degree agreement expanders exist, based on Ramanujan complexes.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.94"}, {"primary_key": "3767155", "vector": [], "sparse_vector": [], "title": "Oracle-Efficient Online Learning and Auction Design.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the design of computationally efficient online learning algorithms in an adversarial setting in which the learner has access to an offline optimization oracle. We present an algorithm called Generalized Followthe- Perturbed-Leader and provide conditions under which it is oracle-efficient while achieving vanishing regret. Our results make significant progress on an open problem raised by <PERSON><PERSON> and <PERSON><PERSON> [1], who showed that oracle-efficient algorithms do not exist in full generality and asked whether one can identify conditions under which oracle-efficient online learning may be possible. Our auction-design framework considers an auctioneer learning an optimal auction for a sequence of adversarially selected valuations with the goal of achieving revenue that is almost as good as the optimal auction in hindsight, among a class of auctions. We give oracle-efficient learning results for: (1) VCG auctions with bidder-specific reserves in singleparameter settings, (2) envy-free item-pricing auctions in multiitem settings, and (3) the level auctions of Morgenstern and Roughgarden [2] for single-item settings. The last result leads to an approximation of the overall optimal Myerson auction when bidders' valuations are drawn according to a fast-mixing Markov process, extending prior work that only gave such guarantees for the i.i.d. setting.We also derive various extensions, including: (1) oracleefficient algorithms for the contextual learning setting in which the learner has access to side information (such as bidder demographics), (2) learning with approximate oracles such as those based on Maximal-in-Range algorithms, and (3) no-regret bidding algorithms in simultaneous auctions, which resolve an open problem of Das<PERSON><PERSON>s and Syrgkanis [3].", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.55"}, {"primary_key": "3767156", "vector": [], "sparse_vector": [], "title": "Prophet Inequalities Made Easy: Stochastic Optimization by Pricing Non-Stochastic Inputs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present a general framework for stochastic online maximization problems with combinatorial feasibility constraints. The framework establishes prophet inequalities by constructing price-based online approximation algorithms, a natural extension of threshold algorithms for settings beyond binary selection. Our analysis takes the form of an extension theorem: we derive sufficient conditions on prices when all weights are known in advance, then prove that the resulting approximation guarantees extend directly to stochastic settings. Our framework unifies and simplifies much of the existing literature on prophet inequalities and posted price mechanisms, and is used to derive new and improved results for combinatorial markets (with and without complements), multi-dimensional matroids, and sparse packing problems. Finally, we highlight a surprising connection between the smoothness framework for bounding the price of anarchy of mechanisms and our framework, and show that many smooth mechanisms can be recast as posted price mechanisms with comparable performance guarantees.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.56"}, {"primary_key": "3767157", "vector": [], "sparse_vector": [], "title": "Determinant-Preserving Sparsification of SDDM Matrices with Applications to Counting and Sampling Spanning Trees.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We show variants of spectral sparsification routines can preserve the total spanning tree counts of graphs, which by <PERSON><PERSON><PERSON>'s matrix-tree theorem, is equivalent to determinant of a graph Laplacian minor, or equivalently, of any SDDM matrix. Our analyses utilizes this combinatorial connection to bridge between statistical leverage scores/effective resistances and the analysis of random graphs by [<PERSON><PERSON>, Combinatorics, Probability and Computing `94]. This leads to a routine that in quadratic time, sparsifies a graph down to about n1.5 edges in ways that preserve both the determinant and the distribution of spanning trees (provided the sparsified graph is viewed as a random object). Extending this algorithm to work with <PERSON>hur complements and approximate Cholesky factorizations leads to algorithms for counting and sampling spanning trees which are nearly optimal for dense graphs. We give an algorithm that computes a (1±δ) approximation to the determinant of any SDDM matrix with constant probability in about n 2 δ -2 time. This is the first routine for graphs that outperforms general-purpose routines for computing determinants of arbitrary matrices. We also give an algorithm that generates in about n 2 δ -2 time a spanning tree of a weighted undirected graph from a distribution with total variation distance of δ from the w-uniform distribution.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.90"}, {"primary_key": "3767158", "vector": [], "sparse_vector": [], "title": "Subdeterminant Maximization via Nonconvex Relaxations and Anti-Concentration.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON>", "Nisheeth K. <PERSON>"], "summary": "Several fundamental problems that arise in optimization and computer science can be cast as follows: Given vectors v 1 , ..., v m ∈ ℝ d and a constraint family B ⊆ 2 [m] , find a set S ∈ B that maximizes the squared volume of the simplex spanned by the vectors in S. A motivating example is the ubiquitous data-summarization problem in machine learning and information retrieval where one is given a collection of feature vectors that represent data such as documents or images. The volume of a collection of vectors is used as a measure of their diversity, and partition or matroid constraints over [m] are imposed in order to ensure resource or fairness constraints. Even with a simple cardinality constraint (B = ( r [m] )), the r problem becomes NP-hard and has received much attention starting with a result by <PERSON><PERSON><PERSON><PERSON> [1] who gave an r O(r) approximation algorithm for this problem. Recently, <PERSON><PERSON><PERSON> and <PERSON> [2] presented a convex program and showed how it can be used to estimate the value of the most diverse set when there are multiple cardinality constraints (i.e., when B corresponds to a partition matroid). Their proof of the integrality gap of the convex program relied on an inequality by <PERSON><PERSON><PERSON><PERSON> [3], and was recently extended to regular matroids [4], [5]. The question of whether these estimation algorithms can be converted into the more useful approximation algorithms - that also output a set - remained open. The main contribution of this paper is to give the first approximation algorithms for both partition and regular matroids. We present novel formulations for the subdeterminant maximization problem for these matroids; this reduces them to the problem of finding a point that maximizes the absolute value of a nonconvex function over a Cartesian product of probability simplices. The technical core of our results is a new anti-concentration inequality for dependent random variables that arise from these functions which allows us to relate the optimal value of these nonconvex functions to their value at a random point. Unlike prior work on the constrained subdeterminant maximization problem, our proofs do not rely on real-stability or convexity and could be of independent interest both in algorithms and complexity where anti-concentration phenomena has recently been deployed.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.98"}, {"primary_key": "3767159", "vector": [], "sparse_vector": [], "title": "Local Hamiltonians Whose Ground States Are Hard to Approximate.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Ground states of local Hamiltonians can be generally highly entangled: any quantum circuit that generates them (even approximately) must be sufficiently deep to allow coupling (entanglement) between any pair of qubits. Until now this property was not known to be \"robust\" - the marginals of such states to a subset of the qubits containing all but a small constant fraction of them may be only locally entangled, and hence approximable by shallow quantum circuits. In this work we construct a family of 16-local Hamiltonians for which any 1-10^{-9} fraction of qubits of any ground state must be highly entangled. This provides evidence that quantum entanglement is not very fragile, and perhaps our intuition about its instability is an artifact of considering local Hamiltonians which are not only local but spatially local. Formally, it provides positive evidence for two wide-open conjectures in condensed-matter physics and quantum complexity theory which are the qLDPC conjecture, positing the existence of \"good\" quantum LDPC codes, and the NLTS conjecture due to <PERSON> and <PERSON> positing the existence of local Hamiltonians in which any low-energy state is highly-entangled. Our Hamiltonian is based on applying the hypergraph product by <PERSON><PERSON> and <PERSON><PERSON><PERSON> to a classical locally testable code. A key tool in our proof is a new lower bound on the vertex expansion of the output of low-depth quantum circuits, which may be of independent interest.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.46"}, {"primary_key": "3767160", "vector": [], "sparse_vector": [], "title": "Deterministic Distributed Edge-Coloring via Hypergraph Maximal Matching.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We present a deterministic distributed algorithm that computes a (2Δ-1)-edge-coloring, or even list-edge-coloring, in any n-node graph with maximum degree Δ, in O(log 8 Δ·log n) rounds. This answers one of the long-standing open questions of distributed graph algorithms} from the late 1980s, which asked for a polylogarithmic-time algorithm. See, e.g., Open Problem 4 in the Distributed Graph Coloring book of <PERSON><PERSON><PERSON><PERSON> and Elkin. The previous best round complexities were 2 O(√(log n) by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [STOC'92] and Õ(√(Δ)) + O(log* n) by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> [FOCS'16]. A corollary of our deterministic list-edge-coloring also improves the randomized complexity of (2Δ-1)-edge-coloring to poly(log log n) rounds. The key technical ingredient is a deterministic distributed algorithm for hypergraph maximal matching, which we believe will be of interest beyond this result. In any hypergraph of rank r - where each hyperedge has at most r vertices - with n nodes and maximum degree Δ, this algorithm computes a maximal matching in O(r 5 log 6+log r Δ·log n) rounds. This hypergraph matching algorithm and its extensions also lead to a number of other results. In particular, we obtain a polylogarithmic-time deterministic distributed maximal independent set (MIS) algorithm for graphs with bounded neighborhood independence, hence answering Open Problem 5 of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> book, a ((log Δ/ε) O(log 1/ε) )-round deterministic algorithm for (1+ε)-approximation of maximum matching, and a quasi-polylogarithmic-time deterministic distributed algorithm for orienting λ-arboricity graphs with out-degree at most ⌈(1+ε)λ⌉, for any constant ε>0, hence partially answering Open Problem 10 of Barenboim and Elkin's book.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.25"}, {"primary_key": "3767161", "vector": [], "sparse_vector": [], "title": "Random Θ(log n)-CNFs Are Hard for Cutting Planes.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The random k-SAT model is the most important and well-studied distribution over k-SAT instances. It is closely connected to statistical physics and is a benchmark for satisfiability algorithms. We show that when k = Θ(log n), any Cutting Planes refutation for random k-SAT requires exponential size in the interesting regime where the number of clauses guarantees that the formula is unsatisfiable with high probability.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.19"}, {"primary_key": "3767162", "vector": [], "sparse_vector": [], "title": "Approximating Geometric Knapsack via L-Packings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study the two-dimensional geometric knapsack problem (2DK) in which we are given a set of n axis-aligned rectangular items, each one with an associated profit, and an axis-aligned square knapsack. The goal is to find a (non-overlapping) packing of a maximum profit subset of items inside the knapsack (without rotating items). The best-known polynomial-time approximation factor for this problem (even just in the cardinality case) is 2+ ε [<PERSON><PERSON> and <PERSON>, SODA 2004]. In this paper we break the 2 approximation barrier, achieving a polynomialtime 17/9 + ε <; 1.89 approximation, which improves to 558/325 + ε <; 1.72 in the cardinality case. Essentially all prior work on 2DK approximation packs items inside a constant number of rectangular containers, where items inside each container are packed using a simple greedy strategy. We deviate for the first time from this setting: we show that there exists a large profit solution where items are packed inside a constant number of containers plus one L-shaped region at the boundary of the knapsack which contains items that are high and narrow and items that are wide and thin. The items of these two types possibly interact in a complex manner at the corner of the L. The above structural result is not enough however: the best-known approximation ratio for the subproblem in the L-shaped region is 2 + ε (obtained via a trivial reduction to one-dimensional knapsack by considering tall or wide items only). Indeed this is one of the simplest special settings of the problem for which this is the best known approximation factor. As a second major, and the main algorithmic contribution of this paper, we present a PTAS for this case. We believe that this will turn out to be useful in future work in geometric packing problems. We also consider the variant of the problem with rotations (2DKR), where items can be rotated by 90 degrees. Also in this case the best-known polynomial-time approximation factor (even for the cardinality case) is 2+ε[Jansen and Zhang, SODA 2004]. Exploiting part of the machinery developed for 2DK plus a few additional ideas, we obtain a polynomial-time 3/2 + ε-approximation for 2DKR, which improves to 4/3 + ε in the cardinality case.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.32"}, {"primary_key": "3767163", "vector": [], "sparse_vector": [], "title": "Garbled Protocols and Two-Round MPC from Bilinear Maps.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper, we initiate the study of garbled protocols - a generalization of <PERSON>'s garbled circuits construction to distributed protocols. More specifically, in a garbled protocol construction, each party can independently generate a garbled protocol component along with pairs of input labels. Additionally, it generates an encoding of its input. The evaluation procedure takes as input the set of all garbled protocol components and the labels corresponding to the input encodings of all parties and outputs the entire transcript of the distributed protocol. We provide constructions for garbling arbitrary protocols based on standard computational assumptions on bilinear maps (in the common random string model). Next, using garbled protocols we obtain a general compiler that compresses any arbitrary round multiparty secure computation protocol into a two-round UC secure protocol. Previously, two-round multiparty secure computation protocols were only known assuming witness encryption or learning-with errors. Benefiting from our generic approach we also obtain protocols (i) for the setting of random access machines (RAM programs) while keeping communication and computational costs proportional to running times, while (ii) making only a black-box use of the underlying group, eliminating the need for any expensive non-black-box group operations. Our results are obtained by a simple but powerful extension of the non-interactive zero-knowledge proof system of Groth, Ostrovsky and Sahai [Journal of ACM, 2012].", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.60"}, {"primary_key": "3767164", "vector": [], "sparse_vector": [], "title": "On Preparing Ground States of Gapped Hamiltonians: An Efficient Quantum Lovász Local Lemma.", "authors": ["<PERSON><PERSON><PERSON>", "Or Sattath"], "summary": "A frustration-free local Hamiltonian has the property that its ground state minimises the energy of all local terms simultaneously. In general, even deciding whether a Hamiltonian is frustration-free is a hard task, as it is closely related to the QMA 1 -complete quantum satisfiability problem (QSAT) - the quantum analogue of SAT, which is the archetypal NP-complete problem in classical computer science. This connection shows that the frustration-free property is not only relevant to physics but also to computer science. The Quantum Lovasz Local Lemma (QLLL) provides a sufficient condition for frustration-freeness. Is there an efficient way to prepare a frustration-free state under the conditions of the QLLL? Previous results showed that the answer is positive if all local terms commute. These works were based on <PERSON><PERSON>'s \"compression argument\" which was the original analysis technique of the celebrated resampling algorithm. We generalise and simplify the \"compression argument\", so that it provides a simplified version of the previous quantum results, and improves on some classical results as well. More importantly, we improve on the previous constructive results by designing an algorithm that works efficiently for non-commuting terms as well, assuming that the system is \"uniformly\" gapped, by which we mean that the system and all its subsystems have an inverse polynomial energy gap. Similarly to the previous results, our algorithm has the charming feature that it uses only local measurement operations corresponding to the local Hamiltonian terms.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.47"}, {"primary_key": "3767165", "vector": [], "sparse_vector": [], "title": "Query-to-Communication Lifting for BPP.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "For any n-bit boolean function f, we show that the randomized communication complexity of the composed function f o g n , where g is an index gadget, is characterized by the randomized decision tree complexity of f. In particular, this means that many query complexity separations involving randomized models (e.g., classical vs. quantum) automatically imply analogous separations in communication complexity.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.21"}, {"primary_key": "3767166", "vector": [], "sparse_vector": [], "title": "Lockable Obfuscation.", "authors": ["<PERSON><PERSON><PERSON>", "Venkata Koppula", "<PERSON>"], "summary": "In this paper we introduce the notion of lockable obfuscation. In a lockable obfuscation scheme there exists an obfuscation algorithm Obf that takes as input a security parameter, a program P, a message msg and lock value lck and outputs an obfuscated program oP. One can evaluate the obfuscated program oP on any input x where the output of evaluation is the message msg if P(x) = lck and otherwise receives a rejecting symbol. We proceed to provide a construction of lockable obfuscation and prove it secure under the Learning with Errors (LWE) assumption. Notably, our proof only requires LWE with polynomial hardness and does not require complexity leveraging. We follow this by describing multiple applications of lockable obfuscation. First, we show how to transform any attribute-based encryption (ABE) scheme into one in which the attributes used to encrypt the message are hidden from any user that is not authorized to decrypt the message. (Such a system is also know as predicate encryption with one-sided security.) The only previous construction due to <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> is based off of a specific ABE scheme of Boneh. By enabling the transformation of any ABE scheme we can inherent different forms and features of the underlying scheme such as: multi-authority, adaptive security from polynomial hardness, regular language policies, etc. We also show applications of lockable obfuscation to separation and uninstantiability results. We first show how to create new separation results in circular encryption that were previously based on indistinguishability obfuscation. This results in new separation results from learning with error including a public key bit encryption scheme that it IND-CPA secure and not circular secure. The tool of lockable obfuscation allows these constructions to be almost immediately realized by translation from previous indistinguishability obfuscation based constructions. In a similar vein we provide random oracle uninstantiability results of the Fujisaki-Okamoto transformation (and related transformations) from the lockable obfuscation combined with fully homomorphic encryption. Again, we take advantage that previous work used indistinguishability obfuscation that obfuscated programs in a form that could easily be translated to lockable obfuscation.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.62"}, {"primary_key": "3767167", "vector": [], "sparse_vector": [], "title": "On Small-<PERSON><PERSON><PERSON> <PERSON><PERSON>ofs for Tseitin for Grids.", "authors": ["<PERSON>"], "summary": "We prove a lower bound on the size of a small depth Frege refutation of the T<PERSON><PERSON> contradiction on the grid. We conclude that polynomial size such refutations must use formulas of almost logarithmic depth.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.18"}, {"primary_key": "3767168", "vector": [], "sparse_vector": [], "title": "Variable-Version Lovász Local Lemma: Beyond Shearer&apos;s Bound.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A tight criterion under which the abstract version Lovász Local Lemma (abstract-LLL) holds was given by <PERSON><PERSON> [41] decades ago. However, little is known about that of the variable version LLL (variable-LLL) where events are generated by independent random variables, though variable- LLL naturally models and is enough for almost all applications of LLL. We introduce a necessary and sufficient criterion for variable-LLL, in terms of the probabilities of the events and the event-variable graph specifying the dependency among the events. Based on this new criterion, we obtain boundaries for two families of event-variable graphs, namely, cyclic and treelike bigraphs. These are the first two non-trivial cases where the variable-LLL boundary is fully determined. As a byproduct, we also provide a universal constructive method to find a set of events whose union has the maximum probability, given the probability vector and the event-variable graph.Though it is #P-hard in general to determine variable- LLL boundaries, we can to some extent decide whether a gap exists between a variable-LLL boundary and the corresponding abstract-LLL boundary. In particular, we show that the gap existence can be decided without solving <PERSON><PERSON>'s conditions or checking our variable-LLL criterion. Equipped with this powerful theorem, we show that there is no gap if the base graph of the event-variable graph is a tree, while gap appears if the base graph has an induced cycle of length at least 4. The problem is almost completely solved except when the base graph has only 3-cliques, in which case we also get partial solutions.A set of reduction rules are established that facilitate to infer gap existence of a event-variable graph from known ones. As an application, various event-variable graphs, in particular combinatorial ones, are shown to be gapful/gapless.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.48"}, {"primary_key": "3767169", "vector": [], "sparse_vector": [], "title": "Local List Recovery of High-Rate Tensor Codes &amp; Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this work, we give the first construction of high-rate locally list-recoverable codes. List-recovery has been an extremely useful building block in coding theory, and our motivation is to use these codes as such a building block. In particular, our construction gives the first capacity-achieving locally list-decodable codes (over constant-sized alphabet); the first capacity achieving globally list-decodable codes with nearly linear time list decoding algorithm (once more, over constant-sized alphabet); and a randomized construction of binary codes on the <PERSON><PERSON><PERSON><PERSON><PERSON> bound that can be uniquely decoded in near-linear-time, with higher rate than was previously known. Our techniques are actually quite simple, and are inspired by an approach of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> (Siam Journal on Computing, 2011) for list-decoding tensor codes. We show that tensor powers of (globally) list-recoverable codes are `approximately' locally list-recoverable, and that the `approximately' modifier may be removed by pre-encoding the message with a suitable locally decodable code. Instantiating this with known constructions of high-rate globally list-recoverable codes and high-rate locally decodable codes finishes the construction.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.27"}, {"primary_key": "3767170", "vector": [], "sparse_vector": [], "title": "The Power of Sum-of-Squares for Detecting Hidden Structures.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We study planted problems-finding hidden structures in random noisy inputs-through the lens of the sum-of-squares semidefinite programming hierarchy (SoS). This family of powerful semidefinite programs has recently yielded many new algorithms for planted problems, often achieving the best known polynomial-time guarantees in terms of accuracy of recovered solutions and robustness to noise. One theme in recent work is the design of spectral algorithms which match the guarantees of SoS algorithms for planted problems. Classical spectral algorithms are often unable to accomplish this: the twist in these new spectral algorithms is the use of spectral structure of matrices whose entries are low-degree polynomials of the input variables. We prove that for a wide class of planted problems, including refuting random constraint satisfaction problems, tensor and sparse PCA, densest-ksubgraph, community detection in stochastic block models, planted clique, and others, eigenvalues of degree-d matrix polynomials are as powerful as SoS semidefinite programs of degree d. For such problems it is therefore always possible to match the guarantees of SoS without solving a large semidefinite program. Using related ideas on SoS algorithms and lowdegree matrix polynomials (and inspired by recent work on SoS and the planted clique problem [BHK + 16]), we prove a new SoS lower bound for the tensor PCA problem.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.72"}, {"primary_key": "3767171", "vector": [], "sparse_vector": [], "title": "Efficient Bayesian Estimation from Few Samples: Community Detection and Related Problems.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We propose an efficient meta-algorithm for Bayesian inference problems based on low-degree polynomials, semidefinite programming, and tensor decomposition. The algorithm is inspired by recent lower bound constructions for sum-of-squares and related to the method of moments. Our focus is on sample complexity bounds that are as tight as possible (up to additive lower-order terms) and often achieve statistical thresholds or conjectured computational thresholds. Our algorithm recovers the best known bounds for partial recovery in the stochastic block model, a widely-studied class of inference problems for community detection in graphs. We obtain the first partial recovery guarantees for the mixed-membership stochastic block model (<PERSON><PERSON><PERSON> et el.) for constant average degree-up to what we conjecture to be the computational threshold for this model. We show that our algorithm exhibits a sharp computational threshold for the stochastic block model with multiple communities beyond the Kesten-Stigum bound-giving evidence that this task may require exponential time. The basic strategy of our algorithm is strikingly simple: we compute the best-possible low-degree approximation for the moments of the posterior distribution of the parameters and use a robust tensor decomposition algorithm to recover the parameters from these approximate posterior moments.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.42"}, {"primary_key": "3767172", "vector": [], "sparse_vector": [], "title": "Random Formulas, Monotone Circuits, and Interpolation.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We prove new lower bounds on the sizes of proofs in the Cutting Plane proof system, using a concept that we call unsatisfiability certificate. This approach is, essentially, equivalent to the well-known feasible interpolation method, but is applicable to CNF formulas that do not seem suitable for interpolation. Specifically, we prove exponential lower bounds for random k-CNFs, where k is the logarithm of the number of variables, and for the Weak Bit Pigeon Hole Principle. Furthermore, we prove a monotone variant of a hypothesis of Feige [12]. We give a superpolynomial lower bound on monotone real circuits that approximately decide the satisfiability of k-CNFs, where k = ω(1). For k ≈ log n, the lower bound is exponential.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.20"}, {"primary_key": "3767173", "vector": [], "sparse_vector": [], "title": "Distributed Exact Weighted All-Pairs Shortest Paths in Õ(n5/4) Rounds.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Danupon <PERSON>", "Thatchaphol <PERSON>"], "summary": "We study computing all-pairs shortest paths (APSP) on distributed networks (the CONGEST model). The goal is for every node in the (weighted) network to know the distance from every other node using communication. The problem admits (1+o(1))-approximation Õ(n)-time algorithms [2], [3], which are matched with Ω(n)-time lower bounds [3], [4], [5] 1 . No ω(n) lower bound or o(m) upper bound were known for exact computation. In this paper, we present an Õ(n 5/4 )-time randomized (Las Vegas) algorithm for exact weighted APSP; this provides the first improvement over the naive O(m)-time algorithm when the network is not so sparse. Our result also holds for the case where edge weights are asymmetric (a.k.a. the directed case where communication is bidirectional). Our techniques also yield an Õ(n 3/4 k 1/2 + n)-time algorithm for the k-source shortest paths problem where we want every node to know distances from k sources; this improves <PERSON><PERSON>'s recent bound [6] when k = ω̃(n 1/4 ). We achieve the above results by developing distributed algorithms on top of the classic scaling technique, which we believe is used for the first time for distributed shortest paths computation. One new algorithm which might be of an independent interest is for the reversed r-sink shortest paths problem, where we want every of r sinks to know its distances from all other nodes, given that every node already knows its distance to every sink. We show an Õ(n√r)-time algorithm for this problem. Another new algorithm is called short range extension, where we show that in Õ(n√h) time the knowledge about distances can be \"extended\" for additional h hops. For this, we use weight rounding to introduce small additive errors which can be later fixed.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.24"}, {"primary_key": "3767174", "vector": [], "sparse_vector": [], "title": "A Characterization of Testable Hypergraph Properties.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We provide a combinatorial characterization of all testable properties of k-graphs (i.e. k-uniform hypergraphs). Here, a k-graph property P is testable if there is a randomized algorithm which makes a bounded number of edge queries and distinguishes with probability 2/3 between k-graphs that satisfy P and those that are far from satisfying P. For the 2-graph case, such a combinatorial characterization was obtained by <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON>. Our results for the k-graph setting are in contrast to those of <PERSON> and <PERSON>, who showed that for the somewhat stronger concept of local repairability, the testability results for graphs do not extend to the 3-graph setting.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.84"}, {"primary_key": "3767175", "vector": [], "sparse_vector": [], "title": "Robust Polynomial Regression up to the Information Theoretic Limit.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We consider the problem of robust polynomial regression, where one receives samples that are usually within a small additive error of a target polynomial, but have a chance of being arbitrary adversarial outliers. Previously, it was known how to efficiently estimate the target polynomial only when the outlier probability was subconstant in the degree of the target polynomial. We give an algorithm that works for the entire feasible range of outlier probabilities, while simultaneously improving other parameters of the problem. We complement our algorithm, which gives a factor 2 approximation, with impossibility results that show, for example, that a 1.09 approximation is impossible even with infinitely many samples.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.43"}, {"primary_key": "3767176", "vector": [], "sparse_vector": [], "title": "Active Classification with Comparison Queries.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We study an extension of active learning in which the learning algorithm may ask the annotator to compare the distances of two examples from the boundary of their label-class. For example, in a recommendation system application (say for restaurants), the annotator may be asked whether she liked or disliked a specific restaurant (a label query); or which one of two restaurants did she like more (a comparison query). We focus on the class of half spaces, and show that under natural assumptions, such as large margin or bounded bit-description of the input examples, it is possible to reveal all the labels of a sample of size n using approximately O(log n) queries. This implies an exponential improvement over classical active learning, where only label queries are allowed. We complement these results by showing that if any of these assumptions is removed then, in the worst case, Ω(n) queries are required. Our results follow from a new general framework of active learning with additional queries. We identify a combinatorial dimension, called the inference dimension, that captures the query complexity when each additional query is determined by O(1) examples (such as comparison queries, each of which is determined by the two compared examples). Our results for half spaces follow by bounding the inference dimension in the cases discussed above.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.40"}, {"primary_key": "3767177", "vector": [], "sparse_vector": [], "title": "The Independence Number of the Birkhoff Polytope Graph, and Applications to Maximally Recoverable Codes.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Maximally recoverable codes are codes designed for distributed storage which combine quick recovery from single node failure and optimal recovery from catastrophic failure. <PERSON><PERSON><PERSON> et al [SODA 2017] studied the alphabet size needed for such codes in grid topologies and gave a combinatorial characterization for it. Consider a labeling of the edges of the complete bipartite graph K n,n with labels coming from F 2 d , that satisfies the following condition: for any simple cycle, the sum of the labels over its edges is nonzero. The minimal d where this is possible controls the alphabet size needed for maximally recoverable codes in n × n grid topologies. Prior to the current work, it was known that d is between log(n) 2 and n log n. We improve both bounds and show that d is linear in n. The upper bound is a recursive construction which beats the random construction. The lower bound follows by first relating the problem to the independence number of the <PERSON><PERSON>hoff polytope graph, and then providing tight bounds for it using the representation theory of the symmetric group.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.31"}, {"primary_key": "3767178", "vector": [], "sparse_vector": [], "title": "Sample Efficient Estimation and Recovery in Sparse FFT via Isolation on Average.", "authors": ["<PERSON>"], "summary": "The problem of computing the Fourier Transform of a signal whose spectrum is dominated by a small number k of frequencies quickly and using a small number of samples of the signal in time domain (the Sparse FFT problem) has received significant attention recently. It is known how to approximately compute the k-sparse Fourier transform in ≈ k log 2 n time [<PERSON><PERSON><PERSON> et al'STOC'12], or using the optimal number O(k log n) of samples [<PERSON><PERSON> et al'FOCS'14] in time domain, or come within (log log n) O(1) factors of both these bounds simultaneously, but no algorithm achieving the optimal O(k log n) bound in sublinear time is known. At a high level, sublinear time Sparse FFT algorithms operate by `hashing' the spectrum of the input signal into ≈ k `buckets', identifying frequencies that are `isolated' in their buckets, subtracting them from the signal and repeating until the entire signal is recovered. The notion of `isolation' in a `bucket', inspired by applications of hashing in sparse recovery with arbitrary linear measurements, has been the main tool in the analysis of Fourier hashing schemes in the literature. However, Fourier hashing schemes, which are implemented via filtering, tend to be `noisy' in the sense that a frequency that hashes into a bucket contributes a non-negligible amount to neighboring buckets. This leakage to neighboring buckets makes identification and estimation challenging, and the standard analysis based on isolation becomes difficult to use without losing ω(1) factors in sample complexity. In this paper we propose a new technique for analysing noisy hashing schemes that arise in Sparse FFT, which we refer to as isolation on average. We apply this technique to two problems in Sparse FFT: estimating the values of a list of frequencies using few samples and computing Sparse FFT itself, achieving sample-optimal results in k log O(1) n time for both. We feel that our approach will likely be of interest in designing Fourier sampling schemes for more general settings (e.g. model based Sparse FFT).", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.66"}, {"primary_key": "3767179", "vector": [], "sparse_vector": [], "title": "Optimal Lower Bounds for Universal Relation, and for Samplers and Finding Duplicates in Streams.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "In the communication problem UR (universal relation) [25], <PERSON> and <PERSON> respectively receive x, y ∈ {0, 1} n with the promise that x ≠ y. The last player to receive a message must output an index i such that x i ≠ y i . We prove that the randomized one-way communication complexity of this problem in the public coin model is exactly Θ(min{n, log(1/δ) log 2 (n/log(1/δ) )}) for failure probability δ. Our lower bound holds even if promised support(y) ⊂ support(x). As a corollary, we obtain optimal lower bounds for ℓ p -sampling in strict turnstile streams for 0 ≤ p n at all points in the stream. We give two different proofs of our main result. The first proof demonstrates that any algorithm A solving sampling problems in turnstile streams in low memory can be used to encode subsets of [n] of certain sizes into a number of bits below the information theoretic minimum. Our encoder makes adaptive queries to A throughout its execution, but done carefully so as to not violate correctness. This is accomplished by injecting random noise into the encoder's interactions with A, which is loosely motivated by techniques in differential privacy. Our correctness analysis involves understanding the ability of A to correctly answer adaptive queries which have positive but bounded mutual information with A's internal randomness, and may be of independent interest in the newly emerging area of adaptive data analysis with a theoretical computer science lens. Our second proof is via a novel randomized reduction from Augmented Indexing [30] which needs to interact with A adaptively. To handle the adaptivity we identify certain likely interaction patterns and union bound over them to guarantee correct interaction on all of them. To guarantee correctness, it is important that the interaction hides some of its randomness from A in the reduction.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.50"}, {"primary_key": "3767180", "vector": [], "sparse_vector": [], "title": "Faster (and Still Pretty Simple) Unbiased Estimators for Network (Un)reliability.", "authors": ["<PERSON>"], "summary": "Consider the problem of estimating the (un)reliability of an n-vertex graph when edges fail with probability p. We show that the Recursive Contraction Algorithms for minimum cuts, essentially unchanged and running in n 2+o(1) time, yields an unbiased estimator of constant relative variance (and thus an FPRAS with the same time bound) whenever p c -2 . For larger p, we show that reliable graphs-where failures are rare so seemingly harder to find-effectively act like small graphs and can thus be analyzed quickly. Combining these ideas gives us an unbiased estimator for unreliability running in Õ(n 2.78 ) time, an improvement on the previous Õ(n 3 ) time bound.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.75"}, {"primary_key": "3767181", "vector": [], "sparse_vector": [], "title": "Polylogarithmic Approximation for Minimum Planarization (Almost).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In the minimum planarization problem, given some n-vertex graph, the goal is to find a set of vertices of minimum cardinality whose removal leaves a planar graph. This is a fundamental problem in topological graph theory. We present a log O(1) n-approximation algorithm for this problem on general graphs with running time n O(log n/log log n) . We also obtain a O(n ε )-approximation with running time n O(1/ε) for any arbitrarily small constant ε > 0. Prior to our work, no non-trivial algorithm was known for this problem on general graphs, and the best known result even on graphs of bounded degree was a n Ω(1) -approximation [1]. As an immediate corollary, we also obtain improved approximation algorithms for the crossing number problem on graphs of bounded degree. Specifically, we obtain O(n 1/2+ε )approximation and n 1/ 2 log O(1) n-approximation algorithms in time n O(1/ε) and n O(log n/log log n) respectively. The previously best-known result was a polynomial-time n 9/10 log O(1) n-approximation algorithm [2]. Our algorithm introduces several new tools including an efficient grid-minor construction for apex graphs, and a new method for computing irrelevant vertices. Analogues of these tools were previously available only for exact algorithms. Our work gives efficient implementations of these ideas in the setting of approximation algorithms, which could be of independent interest.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.77"}, {"primary_key": "3767182", "vector": [], "sparse_vector": [], "title": "How to Achieve Non-Malleability in One or Two Rounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Non-malleable commitments, introduced by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> (STOC 1991), are a fundamental cryptographic primitive, and their round complexity has been a subject of great interest. And yet, the goal of achieving non-malleable commitments with only one or two rounds has been elusive. <PERSON> (TCC 2013) captured this difficulty by proving important impossibility results regarding two-round non-malleable commitments. This led to the widespread belief that achieving two-round nonmalleable commitments was impossible from standard assumptions. We show that this belief was false. Indeed, we obtain the following positive results: We construct two-message non-malleable commitments satisfying non-malleability with respect to commitment, based on standard sub-exponential assumptions, namely: sub-exponential one-way permutations, sub-exponential ZAPs, and sub-exponential DDH. Furthermore, our protocol is public-coin.; We obtain two-message private-coin non-malleable commitments with respect to commitment, assuming only sub-exponential DDH or QR or Nth-residuosity.; We bootstrap the above protocols (under the same assumptions) to obtain two round constant boundedconcurrent non-malleable commitments. In the simultaneous message model, we obtain unbounded concurrent non-malleability in two rounds.; In the simultaneous messages model, we obtain oneround non-malleable commitments, with unbounded concurrent security with respect to opening, under standard sub-exponential assumptions.; This implies non-interactive non-malleable commitments with respect to opening, in a restricted model with a broadcast channel, and a-priori bounded polynomially many parties such that every party is aware of every other party in the system. To the best of our knowledge, this is the first protocol to achieve completely non-interactive non-malleability in any plain model setting from standard assumptions.; As an application of this result, in the simultaneous exchange model, we obtain two-round multi-party pseudorandom coin-flipping.; We construct two-message zero-knowledge arguments with super-polynomial strong simulation (SPSS-ZK), which also serve as an important tool for our constructions of non-malleable commitments.; In order to obtain our results, we develop several techniques that may be of independent interest.; We give the first two-round black-box rewinding strategy based on standard sub-exponential assumptions, in the plain model.;- We also give a two-round tag amplification technique for non-malleable commitments, that amplifies a 4-tag scheme to a scheme for all tags, while relying on sub-exponential DDH. This includes a more efficient alternative to the DDN encoding.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.58"}, {"primary_key": "3767183", "vector": [], "sparse_vector": [], "title": "Learning Graphical Models Using Multiplicative Weights.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We give a simple, multiplicative-weight update algorithm for learning undirected graphical models or Markov random fields (MRFs). The approach is new, and for the well-studied case of Ising models or Boltzmann machines we obtain an algorithm that uses a nearly optimal number of samples and has running time Õ(n 2 ) (where n is the dimension), subsuming and improving on all prior work. Additionally, we give the first efficient algorithm for learning Ising models over non-binary alphabets. Our main application is an algorithm for learning the structure of t-wise MRFs with nearly-optimal sample complexity (up to polynomial losses in necessary terms that depend on the weights) and running time that is n O(t) . In addition, given n O(t) samples, we can also learn the parameters of the model and generate a hypothesis that is close in statistical distance to the true MRF. All prior work runs in time n Ω(d) for graphs of bounded degree d and does not generate a hypothesis close in statistical distance even for t = 3. We observe that our runtime has the correct dependence on n and t assuming the hardness of learning sparse parities with noise. Our algorithm- the Sparsitron- is easy to implement (has only one parameter) and holds in the on-line setting. Its analysis applies a regret bound from <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>s classic Hedge algorithm. It also gives the first solution to the problem of learning sparse Generalized Linear Models (GLMs).", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.39"}, {"primary_key": "3767184", "vector": [], "sparse_vector": [], "title": "White-Box vs. Black-Box Complexity of Search Problems: Ramsey and Graph Property Testing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "E<PERSON>"], "summary": "Ramsey theory assures us that in any graph there is a clique or independent set of a certain size, roughly logarithmic in the graph size. But how difficult is it to find the clique or independent set? If the graph is given explicitly, then it is possible to do so while examining a linear number of edges. If the graph is given by a black-box, where to figure out whether a certain edge exists the box should be queried, then a large number of queries must be issued. But what if one is given a program or circuit for computing the existence of an edge? This problem was raised by <PERSON> and Goldberg and <PERSON> in the context of TFNP, search problems with a guaranteed solution. We examine the relationship between black-box complexity and white-box complexity for search problems with guaranteed solution such as the above Ramsey problem. We show that under the assumption that collision resistant hash function exist (which follows from the hardness of problems such as factoring, discrete-log and learning with errors) the white-box Ramsey problem is hard and this is true even if one is looking for a much smaller clique or independent set than the theorem guarantees. In general, one cannot hope to translate all black-box hardness for TFNP into white-box hardness: we show this by adapting results concerning the random oracle methodology and the impossibility of instantiating it. Another model we consider is the succinct black-box, where there is a known upper bound on the size of the black-box (but no limit on the computation time). In this case we show that for all TFNP problems there is an upper bound on the number of queries proportional to the description size of the box times the solution size. On the other hand, for promise problems this is not the case. Finally, we consider the complexity of graph property testing in the white-box model. We show a property which is hard to test even when one is given the program for computing the graph. The hard property is whether the graph is a two-source extractor.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.63"}, {"primary_key": "3767185", "vector": [], "sparse_vector": [], "title": "Hardness Results for Structured Linear Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We show that if the nearly-linear time solvers for Laplacian matrices and their generalizations can be extended to solve just slightly larger families of linear systems, then they can be used to quickly solve all systems of linear equations over the reals. This result can be viewed either positively or negatively: either we will develop nearly-linear time algorithms for solving all systems of linear equations over the reals, or progress on the families we can solve in nearly-linear time will soon halt.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.69"}, {"primary_key": "3767186", "vector": [], "sparse_vector": [], "title": "Optimality of the Johnson-Lindenstrauss Lemma.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For any d, n ≥ 2 and 1/(min{n, d})0.4999 < ϵ < 1, we show the existence of a set of n vectors X ⊂ Rd such that any embedding f : X → Rm satisfying ∀x, y ∈ X, (1-ϵ)-x-y-22 ≤ -f(x)-f(y)-22 ≤ (1+ϵ)-x-y-22 must have m = Ω(ϵ -2 lg n). This lower bound matches the upper bound given by the <PERSON> lemma [JL84]. Furthermore, our lower bound holds for nearly the full range of ϵ of interest, since there is always an isometric embedding into dimension min{d, n} (either the identity map, or projection onto span(X)). Previously such a lower bound was only known to hold against linear maps f, and not for such a wide range of parameters ϵ, n, d [LN16]. The best previously known lower bound for general f was m = Ω(ϵ -2 lg n/ lg(1/ϵ)) [Wel74], [Alo03], which is suboptimal for any ϵ = o(1).", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.64"}, {"primary_key": "3767187", "vector": [], "sparse_vector": [], "title": "Eldan&apos;s Stochastic Localization and the KLS Hyperplane Conjecture: An Improved Lower Bound for Expansion.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "We show that the KLS constant for n-dimensional isotropic logconcavemeasures is O(n^{1/4}), improving on the current best bound ofO(n^{1/3}√{\\log n}). As corollaries we obtain the same improvedbound on the thin-shell estimate, <PERSON><PERSON><PERSON>\\e constant and Lipschitzconcentration constant and an alternative proof of this bound forthe isotropic constant; it also follows that the ball walk for samplingfrom an isotropic logconcave density in \\R^{n} converges in O^{*}(n^{2.5})steps from a warm start.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.96"}, {"primary_key": "3767188", "vector": [], "sparse_vector": [], "title": "Scheduling to Minimize Total Weighted Completion Time via Time-Indexed Linear Programming Relaxations.", "authors": ["<PERSON>"], "summary": "We study approximation algorithms for scheduling problems with the objective of minimizing total weighted completion time, under identical and related machine models with job precedence constraints. We give algorithms that improve upon many previous 15 to 20-year-old state-of-art results. A major theme in these results is the use of time-indexed linear programming relaxations. These are natural relaxations for their respective problems, but surprisingly are not studied in the literature.We also consider the scheduling problem of minimizing total weighted completion time on unrelated machines. The recent breakthrough result of [<PERSON><PERSON><PERSON><PERSON><PERSON>, STOC 2016] gave a (1.5-c)-approximation for the problem, based on some lift-and-project SDP relaxation. Our main result is that a (1.5 - c)-approximation can also be achieved using a natural and considerably simpler time-indexed LP relaxation for the problem. We hope this relaxation can provide new insights into the problem.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.34"}, {"primary_key": "3767189", "vector": [], "sparse_vector": [], "title": "Linear Algebraic Analogues of the Graph Isomorphism Problem and the Erdős-Rényi Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "A classical difficult isomorphism testing problem is to test isomorphism of p-groups of class 2 and exponent p in time polynomial in the group order. It is known that this problem can be reduced to solving the alternating matrix space isometry problem over a finite field in time polynomial in the underlying vector space size. We propose a venue of attack for the latter problem by viewing it as a linear algebraic analogue of the graph isomorphism problem. This viewpoint leads us to explore the possibility of transferring techniques for graph isomorphism to this long-believed bottleneck case of group isomorphism. In 1970's, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> presented the first average-case efficient graph isomorphism testing algorithm (SIAM J Computing, 1980). Inspired by that algorithm, we devise an average-case efficient algorithm for the alternating matrix space isometry problem over a key range of parameters, in a random model of alternating matrix spaces in vein of the Erdõ<PERSON>-<PERSON><PERSON> model of random graphs. For this, we develop a linear algebraic analogue of the classical individualisation technique, a technique belonging to a set of combinatorial techniques that has been critical for the progress on the worstcase time complexity for graph isomorphism, but was missing in the group isomorphism context. This algorithm also enables us to improve <PERSON><PERSON>'s 57-year-old lower bound on the number of p-groups (Proc. of the LMS, 1960). We finally show that <PERSON><PERSON>' dynamic programming technique for graph isomorphism (<PERSON><PERSON> 1999) can be adapted to slightly improve the worstcase time complexity of the alternating matrix space isometry problem in a certain range of parameters. Most notable progress on the worst-case time complexity of graph isomorphism, including <PERSON><PERSON>'s recent breakthrough (ST<PERSON> 2016) and <PERSON><PERSON> and <PERSON><PERSON>' previous record (STOC 1983), has relied on both group theoretic and combinatorial techniques. By developing a linear algebraic analogue of the individualisation technique and demonstrating its usefulness in the average-case setting, the main result opens up the possibility of adapting that strategy for graph isomorphism to this hard instance of group isomorphism. The linear algebraic Erdõs-Rényi model is of independent interest and may deserve further study.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.49"}, {"primary_key": "3767190", "vector": [], "sparse_vector": [], "title": "Two-Round and Non-Interactive Concurrent Non-Malleable Commitments from Time-Lock Puzzles.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Non-malleable commitments are a fundamental cryptographic tool for preventing against (concurrent) man-in-the-middle attacks. Since their invention by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> in 1991, the round-complexity of non-malleable commitments has been extensively studied, leading up to constant-round concurrent non-malleable commitments based only on one-way functions, and even 3-round concurrent non-malleable commitments based on subexponential one-way functions. But constructions of two-round, or non-interactive, nonmalleable commitments have so far remained elusive; the only known construction relied on a strong and non-falsifiable assumption with a non-malleability flavor. Additionally, a recent result by Pass shows the impossibility of basing two-round non-malleable commitments on falsifiable assumptions using a polynomial-time black-box security reduction. In this work, we show how to overcome this impossibility, using super-polynomial-time hardness assumptions. Our main result demonstrates the existence of a two-round concurrent non-malleable commitment based on subexponential \"standard-type\" assumptions-notably, assuming the existence of the following primitives (all with subexponential security): (1) non-interactive commitments, (2) ZAPs (i.e., 2-round witness indistinguishable proofs), (3) collision-resistant hash functions, and (4) a \"weak\" time-lock puzzle. Primitives (1),(2),(3) can be based on e.g., the discrete log assumption and the RSA assumption. Time-lock puzzles-puzzles that can be solved by \"brute-force\" in time 2t, but cannot be solved significantly faster even using parallel computers-were proposed by Rivest, Shamir, and Wagner in 1996, and have been quite extensively studied since; the most popular instantiation relies on the assumption that 2t repeated squarings mod N = pq require \"roughly\" 2t parallel time. Our notion of a \"weak\" time-lock puzzle, requires only that the puzzle cannot be solved in parallel time 2 t ϵ (and thus we only need to rely on the relatively mild assumption that there are no huge improvements in the parallel complexity of repeated squaring algorithms). We additionally show that if replacing assumption (2) for a non-interactive witness indistinguishable proof (NIWI), and (3) for a uniform collision-resistant hash function, then a non-interactive (i.e., one-message) version of our protocol satisfies concurrent non-malleability w.r.t. uniform attackers.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.59"}, {"primary_key": "3767191", "vector": [], "sparse_vector": [], "title": "Derandomization Beyond Connectivity: Undirected Laplacian Systems in Nearly Logarithmic Space.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We give a deterministic Õ(log n)-space algorithm for approximately solving linear systems given by Laplacians of undirected graphs, and consequently also approximating hitting times, commute times, and escape probabilities for undirected graphs. Previously, such systems were known to be solvable by randomized algorithms using O(log n) space (<PERSON><PERSON>, <PERSON>all, and <PERSON><PERSON>, 2017) and hence by deterministic algorithms using O(log 3/2 n) space (<PERSON><PERSON> and <PERSON>, FOCS 1995 and JCSS 1999). Our algorithm combines ideas from time-efficient Laplacian solvers (<PERSON><PERSON><PERSON> and <PERSON><PERSON>, STOC `04; <PERSON><PERSON> and <PERSON><PERSON><PERSON>, STOC `14) with ideas used to show that UNDIRECTED S-T CONNECTIVITY is in deterministic logspace (<PERSON>ing<PERSON>, STOC `05 and JACM `08; <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, RANDOM `05).", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.79"}, {"primary_key": "3767192", "vector": [], "sparse_vector": [], "title": "Sublinear Time Low-Rank Approximation of Positive Semidefinite Matrices.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We show how to compute a relative-error low-rank approximation to any positive semidefinite (PSD) matrix in sublinear time, i.e., for any n x n PSD matrix A, in Õ(n · poly(k/ε)) time we output a rank-k matrix B, in factored form, for which ||A - B|| 2 F ≤ (1 + ε)||A - A k || F 2 , where Ak is the best rank-k approximation to A. When k and 1/ε are not too large compared to the sparsity of A, our algorithm does not need to read all entries of the matrix. Hence, we significantly improve upon previous nnz(A) time algorithms based on oblivious subspace embeddings, and bypass an nnz(A) time lower bound for general matrices (where nnz(A) denotes the number of non-zero entries in the matrix). We prove time lower bounds for low-rank approximation of PSD matrices, showing that our algorithm is close to optimal. Finally, we extend our techniques to give sublinear time algorithms for lowrank approximation of A in the (often stronger) spectral norm metric ||A - B|| 2 2 and for ridge regression on PSD matrices.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.68"}, {"primary_key": "3767193", "vector": [], "sparse_vector": [], "title": "Dynamic Minimum Spanning Forest with Subpolynomial Worst-Case Update Time.", "authors": ["Danupon <PERSON>", "Thatchaphol <PERSON>", "<PERSON><PERSON>"], "summary": "We present a Las Vegas algorithm for dynamically maintaining a minimum spanning forest of an nnode graph undergoing edge insertions and deletions. Our algorithm guarantees an O(n o(1) ) worst-case update time with high probability. This significantly improves the two recent Las Vegas algorithms by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [2] with update time O(n 0.5-ε ) for some constant ε > 0 and, independently, by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [3] with update time O(n 0.494 ) (the latter works only for maintaining a spanning forest). Our result is obtained by identifying the common framework that both two previous algorithms rely on, and then improve and combine the ideas from both works. There are two main algorithmic components of the framework that are newly improved and critical for obtaining our result. First, we improve the update time from O(n 0.5-ε ) in [2] to O(n o(1) ) for decrementally removing all low-conductance cuts in an expander undergoing edge deletions. Second, by revisiting the \"contraction technique\" by <PERSON><PERSON><PERSON> and <PERSON> [4] and <PERSON> et al. [5], we show a new approach for maintaining a minimum spanning forest in connected graphs with very few (at most (1 + o(1))n) edges. This significantly improves the previous approach in [2], [3] which is based on <PERSON><PERSON>'s 2-dimensional topology tree [6] and illustrates a new application to this old technique.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.92"}, {"primary_key": "3767194", "vector": [], "sparse_vector": [], "title": "An Input Sensitive Online Algorithm for the Metric Bipartite Matching Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a novel input sensitive analysis of a deterministic online algorithm [1] for the minimum metric bipartite matching problem. We show that, in the adversarial model, for any metric space M and a set of n servers S, the competitive ratio of this algorithm is O(μ M (S) log 2 n); here μ M (S) is the maximum ratio of the traveling salesman tour and the diameter of any subset of S. It is straight-forward to show that any algorithm, even with complete knowledge of M and S, will have a competitive ratio of Ω(μ M (S)). So, the performance of this algorithm is sensitive to the input and near-optimal for any given S and M. As consequences, we also achieve the following results: 1) If S is a set of points on a line, then μ M (S) = Θ(1) and the competitive ratio is O(log 2 n), and, 2) If S is a set of points spanning a subspace with doubling dimension d, then μ M (S) = O(n 1-1/d ) and the competitive ratio is O(n 1-1/d log 2 n). Prior to this result, the previous best-known algorithm for the line metric has a competitive ratio of O(n 0.59 ) and requires both S and the request set R to be on a line. There is also an O(log n) competitive algorithm in the weaker oblivious adversary model. To obtain our results, we partition the requests into well-separated clusters and replace each cluster with a small and a large weighted ball; the weight of a ball is the number of requests in the cluster. We show that the cost of the online matching can be expressed as the sum of the weight times radius of the smaller balls. We also show that the cost of edges of the optimal matching inside each larger ball can be shown to be proportional to the weight times the radius of the larger ball. We then use a simple variant of the well-known Vitali's covering lemma to relate the radii of these balls and obtain the competitive ratio.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.53"}, {"primary_key": "3767195", "vector": [], "sparse_vector": [], "title": "Short Presburger Arithmetic Is Hard.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We study the computational complexity of short sentences in Presburger arithmetic (SHORT-PA). Here by \"short\" we mean sentences with a bounded number of variables, quantifiers, inequalities and Boolean operations; the input consists only of the integer coefficients involved in the linear inequalities. We prove that satisfiability of SHORT-PA sentences with m+2 alternating quantifiers is Σ m P -complete or Π m P -complete, when the first quantifier is ∃ or ∀, respectively. Counting versions and restricted systems are also analyzed.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.13"}, {"primary_key": "3767196", "vector": [], "sparse_vector": [], "title": "Average-Case Reconstruction for the Deletion Channel: Subpolynomially Many Traces Suffice.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "The deletion channel takes as input a bit string x ∈ {0, 1} n , and deletes each bit independently with probability q, yielding a shorter string. The trace reconstruction problem is to recover an unknown string x from many independent outputs (called \"traces\") of the deletion channel applied to x. We show that if x is drawn uniformly at random and q O(log1/2 n) traces suffice to reconstruct x with high probability. The previous best bound, established in 2008 by <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [1], uses n O(1) traces and only applies for q less than a smaller threshold (it seems that q <; 0.07 is needed). Our algorithm combines several ideas: 1) an alignment scheme for \"greedily\" fitting the output of the deletion channel as a subsequence of the input; 2) a version of the idea of \"anchoring\" used in [1]; and 3) complex analysis techniques from recent work of <PERSON><PERSON><PERSON> and <PERSON><PERSON> [2] and <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> [3].", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.29"}, {"primary_key": "3767197", "vector": [], "sparse_vector": [], "title": "A Time-Space Lower Bound for a Large Class of Learning Problems.", "authors": ["<PERSON><PERSON>"], "summary": "We prove a general memory-samples lower bound that applies for a large class of learning problems and shows that for every problem in that class, any learning algorithm requires either a memory of quadratic size or an exponential number of samples. Our result is stated in terms of the norm of the matrix that corresponds to the learning problem. Let X, A be two finite sets. A matrix M : A × X → {-1, 1} corresponds to the following learning problem: An unknown element x ∈ X was chosen uniformly at random. A learner tries to learn x from a stream of samples, (a 1 , b 1 ), (a 2 , b 2 ) ..., where for every i, a i ∈ A is chosen uniformly at random and b i = M(ai, x). Let σ max be the largest singular value of M and note that always σ max ≤ |A| 1/2 · |X| 1/2 . We show that if σ max ≤ |A| 1/2 · |X| 1/2-ε , then any learning algorithm for the corresponding learning problem requires either a memory of size at least Ω ((εn) 2 ) or at least 2 Ω(εn) samples, where n = log 2 |X|. As a special case, this gives a new proof for the memory-samples lower bound for parity learning [14].", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.73"}, {"primary_key": "3767198", "vector": [], "sparse_vector": [], "title": "Fast &amp; Space-Efficient Approximations of Language Edit Distance and RNA Folding: An Amnesic Dynamic Programming Approach.", "authors": ["<PERSON><PERSON>"], "summary": "Dynamic programming is a basic, and one of the most systematic techniques for developing polynomial time algorithms with overwhelming applications. However, it often suffers from having high running time and space complexity due to (a) maintaining a table of solutions for a large number of sub-instances, and (b) combining/comparing these solutions to successively solve larger sub-instances. In this paper, we consider a canonical cubic time and quadratic space dynamic programming, and show how improvements in both its time and space uses are possible. As a result, we obtain fast small-space approximation algorithms for the fundamental problems of context free grammar recognition (the basic computer science problem of parsing), the language edit distance (a significant generalization of string edit distance and parsing), and RNA folding (a classical problem in bioinformatics). For these problems, ours are the first algorithms that break the cubic-time barrier of any combinatorial algorithm, and quadratic-space barrier of \"any\" algorithm significantly improving upon their long-standing space and time complexities. Our technique applies to many other problems as well including string edit distance computation, and finding longest increasing subsequence. Our improvements come from directly grinding the dynamic programming and looking through the lens of language edit distance which generalizes both context free grammar recognition, and RNA folding. From known conditional lower bound results, neither of these problems can have an exact combinatorial algorithm (one that does not use fast matrix multiplication) running in truly subcubic time. Moreover, for language edit distance such an algorithm cannot exist even when nontrivial multiplicative approximation is allowed. We overcome this hurdle by designing an additive-approximation algorithm that for any parameter k > 0, uses O(nk log n) space and O(n 2 k log n) time and provides an additive O(nk log n)approximation. In particular, in Õ(n) 1 space and Õ(n 2 ) time it can solve deterministically whether a string belongs to a context free grammar, or ϵ-far from it for any constant ϵ > 0. We also improve the above results to obtain an algorithm that outputs an ϵ · n-additive approximation to the above problems with space complexity O(n2/3 log n). The space complexity remains sublinear in n, as long as ϵ = o(n -1 / 4 ). Moreover, we provide the first MapReduce and streaming algorithms for them with multiple passes and sublinear space complexity.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.35"}, {"primary_key": "3767199", "vector": [], "sparse_vector": [], "title": "Deterministic Search for CNF Satisfying Assignments in Almost Polynomial Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We consider the fundamental derandomization problem of deterministically finding a satisfying assignment to a CNF formula that has many satisfying assignments. We give a deterministic algorithm which, given an n-variable poly(n)-clause CNF formula F that has at least ε2 n satisfying assignments, runs in time n(Õ(log log n) 2 ) for ε ≥ 1/polylog(n) and outputs a satisfying assignment of F. Prior to our work the fastest known algorithm for this problem was simply to enumerate over all seeds of a pseudorandom generator for CNFs; using the best known PRGs for CNFs [DETT10], this takes time n Ω̃(log n) even for constant ε. Our approach is based on a new general framework relating deterministic search and deterministic approximate counting, which we believe may find further applications.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.80"}, {"primary_key": "3767200", "vector": [], "sparse_vector": [], "title": "Fooling Intersections of Low-Weight Halfspaces.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A weight-t halfspace is a Boolean function f(x) = sign(w 1 x 1 + ⋯ + w n x n - θ) where each w i is an integer in {-t, . . . , t}. We give an explicit pseudorandom generator that δ-fools any intersection of k weight-t halfspaces with seed length poly(log n, log k, t, 1/δ). In particular, our result gives an explicit PRG that fools any intersection of any quasipoly(n) number of halfspaces of any polylog(n) weight to any 1/polylog(n) accuracy using seed length polylog(n). Prior to this work no explicit PRG with non-trivial seed length was known even for fooling intersections of n weight-1 halfspaces to constant accuracy. The analysis of our PRG fuses techniques from two different lines of work on unconditional pseudorandomness for different kinds of Boolean functions. We extend the approach of <PERSON><PERSON><PERSON>, <PERSON> and Me<PERSON> [HKM12] for fooling intersections of regular halfspaces, and combine this approach with results of <PERSON><PERSON> [Baz07] and <PERSON><PERSON><PERSON><PERSON> [Raz09] on bounded independence fooling CNF formulas. Our analysis introduces new couplingbased ingredients into the standard Lindeberg method for establishing quantitative central limit theorems and associated pseudorandomness results.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.81"}, {"primary_key": "3767201", "vector": [], "sparse_vector": [], "title": "Optimal Interactive Coding for Insertions, Deletions, and Substitutions.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Interactive coding, pioneered by <PERSON><PERSON><PERSON> (FOCS 92, STOC 93), is concerned with making communication protocols resilient to adversarial noise. The canonical model allows the adversary to alter a small constant fraction of symbols, chosen at the adversarys discretion, as they pass through the communication channel. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (2015) proposed a far-reaching generalization of this model, whereby the adversary can additionally manipulate the channel by removing and inserting symbols. They showed how to faithfully simulate any protocol in this model with corruption rate up to 1/18, using a constant-size alphabet and a constant-factor overhead in communication. We give an optimal simulation of any protocol in this generalized model of substitutions, insertions, and deletions, tolerating a corruption rate up to 1/4 while keeping the alphabet to a constant size and the communication overhead to a constant factor. Our corruption tolerance matches an impossibility result for corruption rate 1/4 which holds even for substitutions alone (<PERSON><PERSON> and <PERSON>, STOC 11).", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.30"}, {"primary_key": "3767202", "vector": [], "sparse_vector": [], "title": "Tight Lower Bounds for Differentially Private Selection.", "authors": ["<PERSON>", "<PERSON>"], "summary": "A pervasive task in the differential privacy literature is to select the k items of \"highest quality\" out of a set of d items, where the quality of each item depends on a sensitive dataset that must be protected. Variants of this task arise naturally in fundamental problems like feature selection and hypothesis testing, and also as subroutines for many sophisticated differentially private algorithms. The standard approaches to these tasks-repeated use of the exponential mechanism or the sparse vector technique-approximately solve this problem given a dataset of n = O(√k log d) samples. We provide a tight lower bound for some very simple variants of the private selection problem. Our lower bound shows that a sample of size n = Ω(√k log d) is required even to achieve a very minimal accuracy guarantee. Our results are based on an extension of the fingerprinting method to sparse selection problems. Previously, the fingerprinting method has been used to provide tight lower bounds for answering an entire set of d queries, but often only some much smaller set of k queries are relevant. Our extension allows us to prove lower bounds that depend on both the number of relevant queries and the total number of queries.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.57"}, {"primary_key": "3767203", "vector": [], "sparse_vector": [], "title": "The Matching Problem in General Graphs Is in Quasi-NC.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show that the perfect matching problem in general graphs is in Quasi-NC. That is, we give a deterministic parallel algorithm which runs in $O(\\log^3 n)$ time on $n^{O(\\log^2 n)}$ processors. The result is obtained by a derandomization of the Isolation Lemma for perfect matchings, which was introduced in the classic paper by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON> [1987] to obtain a Randomized NC algorithm. Our proof extends the framework of <PERSON>, <PERSON> and <PERSON> [2016], who proved the analogous result in the special case of bipartite graphs. Compared to that setting, several new ingredients are needed due to the significantly more complex structure of perfect matchings in general graphs. In particular, our proof heavily relies on the laminar structure of the faces of the perfect matching polytope.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.70"}, {"primary_key": "3767204", "vector": [], "sparse_vector": [], "title": "Optimal Repair of Reed-Solomon Codes: Achieving the Cut-Set Bound.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The repair problem for an (n, k) error-correcting code calls for recovery of an unavailable coordinate of the codeword by downloading as little information as possible from a subset of the remaining coordinates. Using the terminology motivated by coding in distributed storage, we attempt to repair a failed node by accessing information stored on d helper nodes, where k ≤ d ≤ n - 1, and using as little repair bandwidth as possible to recover the lost information. By the so-called cut-set bound (<PERSON> et al., 2010), the repair bandwidth of an (n,k = n - r) MDS code using d helper nodes is at least dl/(d + 1 - k), where l is the size of the node. A number of constructions of MDS array codes have been shown to meet this bound with equality. In a related but separate line of work, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2016) studied repair of Reed-Solomon (RS) codes, showing that it is possible to perform repair using a smaller bandwidth than under the trivial approach. At the same time, their work as well as follow-up papers stopped short of constructing RS codes (or any scalar MDS codes) that meet the cut-set bound with equality, which has been an open problem in coding theory. In this work we present a solution to this problem, constructing RS codes of length n over the field of size q l , l = exp((1 + o(1))n log n) that meet the cut-set bound. We also prove an almost matching lower bound on l, showing that super-exponential scaling is both necessary and sufficient for achieving the cut-set bound using linear repair schemes. More precisely, we prove that for scalar MDS codes (including the RS codes) to meet this bound, the sub-packetization l must satisfy l ≥ exp((1 + o(1))k log k).", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.28"}, {"primary_key": "3767205", "vector": [], "sparse_vector": [], "title": "Capacity of Neural Networks for Lifelong Learning of Composable Tasks.", "authors": ["<PERSON>"], "summary": "We investigate neural circuits in the exacting setting that (i) the acquisition of a piece of knowledge can occur from a single interaction, (ii) the result of each such interaction is a rapidly evaluatable subcircuit, (iii) hundreds of thousands of such subcircuits can be acquired in sequence without substantially degrading the earlier ones, and (iv) recall can be in the form of a rapid evaluation of a composition of subcircuits that have been so acquired at arbitrary different earlier times.We develop a complexity theory, in terms of asymptotically matching upper and lower bounds, on the capacity of a neural network for executing, in this setting, the following action, which we call {\\it association}: Each action sets up a subcircuit so that the excitation of a chosen set of neurons A will in future cause the excitation of another chosen set B.% As model of computation we consider the neuroidal model, a fully distributed model in which the quantitative resources n, the neuron numbers, d, the number of other neurons each neuron is connected to, and k, the inverse of the maximum synaptic strength, are all accounted for.A succession of experiences, possibly over a lifetime, results in the realization of a complex set of subcircuits. The composability requirement constrains the model to ensure that, for each association as realized by a subcircuit, the excitation in the triggering set of neurons A is quantitatively similar to that in the triggered set B, and also that the unintended excitation in the rest of the system is negligible. These requirements ensure that chains of associations can be triggeredWe first analyze what we call the Basic Mechanism, which uses only direct connections between neurons in the triggering set A and the target set B. We consider random networks of n neurons with expected number d of connections to and from each. We show that in the composable context capacity growth is limited by d 2 , a severe limitation if the network is sparse, as it is in cortex. We go on to study the Expansive Mechanism, that additionally uses intermediate relay neurons which have high synaptic weights. For this mechanism we show that the capacity can grow as dn, to within logarithmic factors. From these two results it follows that in the composable regime, for the realistic cortical estimate of d=n 1/2 , superlinear capacity of order n 3/2 in terms of the neuron numbers can be realized by the Expansive Mechanism, instead of the linear order n to which the Basic Mechanism is limited. More generally, for both mechanisms, we establish matching upper and lower bounds on capacity in terms of the parameters n, d, and the inverse maximum synaptic strength k.The results as stated above assume that in a set of associations, a target B can be triggered by at most one set A. It can be shown that the capacities are similar if the number m of As that can trigger a B is greater than one but small, but become severely constrained if m exceeds a certain threshold.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.41"}, {"primary_key": "3767206", "vector": [], "sparse_vector": [], "title": "Obfuscating Compute-and-Compare Programs under LWE.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We show how to obfuscate a large and expressive class of programs, which we call compute-and-compare programs, under the learning-with-errors (LWE) assumption. Each such program CC[f, y] is parametrized by an arbitrary polynomial-time computable function f along with a target value y and we define CC[f,y](x) to output 1 if f(x) = y and 0 otherwise. In other words, the program performs an arbitrary computation f and then compares its output against a target y. Our obfuscator satisfies distributional virtual-blackbox security, which guarantees that the obfuscated program does not reveal any partial information about the function f or the target value y, as long as they are chosen from some distribution where y has sufficient pseudo-entropy given f. We also extend our result to multi-bit compute-and-compare programs MBCC[f, y, z](x) which output a message z if f(x) = y. Compute-and-compare programs are powerful enough to capture many interesting obfuscation tasks as special cases. This includes obfuscating conjunctions, and therefore we improve on the prior work of <PERSON><PERSON><PERSON><PERSON> et al. (ITCS '16) which constructed a conjunction obfuscator under a non-standard \"entropic\" ring-LWE assumption, while here we obfuscate a significantly broader class of programs under standard LWE. We show that our obfuscator has several interesting applications. For example, we can take any encryption scheme and publish an obfuscated plaintext equality tester that allows users to check whether a ciphertext decrypts to some target value y; as long as y has sufficient pseudo-entropy this will not harm semantic security. We can also use our obfuscator to generically upgrade attribute-based encryption to predicate encryption with one-sided attribute-hiding security, and to upgrade witness encryption to indistinguishability obfuscation which is secure for all \"null circuits\". Furthermore, we show that our obfuscator gives new circular-security counterexamples for public-key bit encryption and for unbounded length key cycles. Our result uses the graph-induced multi-linear maps of Gentry, Gorbunov and Halevi (TCC '15), but only in a carefully restricted manner which is provably secure under LWE. Our technique is inspired by ideas introduced in a recent work of Goyal, Koppula and Waters (EUROCRYPT '17) in a seemingly unrelated context.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.61"}, {"primary_key": "3767207", "vector": [], "sparse_vector": [], "title": "A Proof of CSP Dichotomy Conjecture.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many natural combinatorial problems can be expressed as constraint satisfaction problems. This class of problems is known to be NP-complete in general, but certain restrictions on the form of the constraints can ensure tractability. The standard way to parametrize interesting subclasses of the constraint satisfaction problem is via finite constraint languages. The main problem is to classify those subclasses that are solvable in polynomial time and those that are NP-complete. It was conjectured that if a core of a constraint language has a weak near unanimity polymorphism then the corresponding constraint satisfaction problem is tractable, otherwise it is NP-complete.In the paper we present an algorithm that solves Constraint Satisfaction Problem in polynomial time for constraint languages having a weak near unanimity polymorphism, which proves the remaining part of the conjecture.", "published": "2017-01-01", "category": "focs", "pdf_url": "", "sub_summary": "", "source": "focs", "doi": "10.1109/FOCS.2017.38"}]