[{"primary_key": "2100652", "vector": [], "sparse_vector": [], "title": "A Security Framework for Distributed Ledgers.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In the past few years blockchains have been a major focus for security research, resulting in significant progress in the design, formalization, and analysis of blockchain protocols. However, the more general class of distributed ledgers, which includes not just blockchains but also prominent non-blockchain protocols, such as Corda and OmniLedger, cannot be covered by the state-of-the-art in the security literature yet. These distributed ledgers often break with traditional blockchain paradigms, such as block structures to store data, system-wide consensus, or global consistency. In this paper, we close this gap by proposing the first framework for defining and analyzing the security of general distributed ledgers, with an ideal distributed ledger functionality, called Fledger, at the core of our contribution. This functionality covers not only classical blockchains but also non-blockchain distributed ledgers in a unified way. To illustrate Fledger, we first show that the prominent ideal block-chain functionalities Gledger and GPL realize (suitable instantiations of) Fledger, which captures their security properties. This implies that their respective implementations, including Bitcoin, Ouroboros Genesis, and Ouroboros Crypsinous, realize Fledger as well. Secondly, we demonstrate that Fledger is capable of precisely modeling also non-blockchain distributed ledgers by performing the first formal security analysis of such a distributed ledger, namely the prominent Corda protocol. Due to the wide spread use of Corda in industry, in particular the financial sector, this analysis is of independent interest. These results also illustrate that <PERSON><PERSON><PERSON> not just generalizes the modular treatment of blockchains to distributed ledgers, but moreover helps to unify existing results.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485362"}, {"primary_key": "2100653", "vector": [], "sparse_vector": [], "title": "Continuous Release of Data Streams under both Centralized and Local Differential Privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study the problem of publishing a stream of real-valued data satisfying differential privacy (DP). One major challenge is that the maximal possible value in the stream can be quite large, leading to enormous DP noise and bad utility. To reduce the maximal value and noise, one way is to estimate a threshold so that values above it can be truncated. The intuition is that, in many scenarios, only a few values are large; thus truncation does not change the original data much. We develop such a method that finds a suitable threshold with DP. Given the threshold, we then propose an online hierarchical method and several post-processing techniques.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484750"}, {"primary_key": "2100654", "vector": [], "sparse_vector": [], "title": "Hardware Support to Improve Fuzzing Performance and Precision.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Coverage-guided fuzzing is considered one of the most efficient bug-finding techniques, given its number of bugs reported. However, coverage tracing provided by existing software-based approaches, such as source instrumentation and dynamic binary translation, can incur large overhead. Hindered by the significantly lowered execution speed, it also becomes less beneficial to improve coverage feedback by incorporating additional execution states.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484573"}, {"primary_key": "2100655", "vector": [], "sparse_vector": [], "title": "Differentially Private Sparse Vectors with Low Error, Optimal Space, and Fast Access.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Representing a sparse histogram, or more generally a sparse vector, is a fundamental task in differential privacy. An ideal solution would use space close to information-theoretical lower bounds, have an error distribution that depends optimally on the desired privacy level, and allow fast random access to entries in the vector. However, existing approaches have only achieved two of these three goals. In this paper we introduce the Approximate Laplace Projection (ALP) mechanism for approximating k-sparse vectors. This mechanism is shown to simultaneously have information-theoretically optimal space (up to constant factors), fast access to vector entries, and error of the same magnitude as the Laplace-mechanism applied to dense vectors. A key new technique is a unary representation of small integers, which we show to be robust against \"randomized response'' noise. This representation is combined with hashing, in the spirit of Bloom filters, to obtain a space-efficient, differentially private representation. Our theoretical performance bounds are complemented by simulations which show that the constant factors on the main performance parameters are quite small, suggesting practicality of the technique.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484735"}, {"primary_key": "2100656", "vector": [], "sparse_vector": [], "title": "DetectorGuard: Provably Securing Object Detectors against Localized Patch Hiding Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "State-of-the-art object detectors are vulnerable to localized patch hiding attacks, where an adversary introduces a small adversarial patch to make detectors miss the detection of salient objects. The patch attacker can carry out a physical-world attack by printing and attaching an adversarial patch to the victim object; thus, it imposes a challenge for the safe deployment of object detectors. In this paper, we propose DetectorGuard as the first general framework for building provably robust object detectors against localized patch hiding attacks. DetectorGuard is inspired by recent advancements in robust image classification research; we ask: can we adapt robust image classifiers for robust object detection? Unfortunately, due to their task difference, an object detector naively adapted from a robust image classifier 1) may not necessarily be robust in the adversarial setting or 2) even maintain decent performance in the clean setting. To address these two issues and build a high-performance robust object detector, we propose an objectness explaining strategy: we adapt a robust image classifier to predict objectness (i.e., the probability of an object being present) for every image location and then explain each objectness using the bounding boxes predicted by a conventional object detector. If all objectness is well explained, we output the predictions made by the conventional object detector; otherwise, we issue an attack alert. Notably, our objectness explaining strategy enables provable robustness for \"free\": 1) in the adversarial setting, we formally prove the end-to-end robustness of DetectorGuard on certified objects, i.e., it either detects the object or triggers an alert, against any patch hiding attacker within our threat model; 2) in the clean setting, we have almost the same performance as state-of-the-art object detectors. Our evaluation on the PASCAL VOC, MS COCO, and KITTI datasets further demonstrates that DetectorGuard achieves the first provable robustness against localized patch hiding attacks at a negligible cost (< 1%) of clean performance.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484757"}, {"primary_key": "2100657", "vector": [], "sparse_vector": [], "title": "The Effect of Google Search on Software Security: Unobtrusive Security Interventions via Content Re-ranking.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Google Search is where most developers start their Web journey looking for code examples to reuse. It is highly likely that code that is linked to the top results will be among those candidates that find their way into production software. However, as a large amount of secure and insecure code has been identified on the Web, the question arises how the providing webpages are ranked by Google and whether the ranking has an effect on software security. We investigate how secure and insecure cryptographic code examples from Stack Overflow are ranked by Google Search. Our results show that insecure code ends up in the top results and is clicked on more often. There is at least a 22.8% chance that one out of the top three Google Search results leads to insecure code. We introduce security-based re-ranking, where the rank of Google Search is updated based on the security and relevance of the provided source code in the results. We tested our re-ranking approach and compared it to Google's original ranking in an online developer study. Participants that used our modified search engine to look for help online submitted more secure and functional results, with statistical significance. In contrast to prior work on helping developers to write secure code, security-based re-ranking completely eradicates the requirement for any action performed by developers. Our intervention remains completely invisible, and therefore the probability of adoption is greatly increased. We believe security-based re-ranking allows Internet-wide improvement of code security and prevents the far-reaching spread of insecure code found on the Web.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484763"}, {"primary_key": "2100658", "vector": [], "sparse_vector": [], "title": "CapSpeaker: Injecting Voices to Microphones via Capacitors.", "authors": ["Xiaoyu Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu"], "summary": "Voice assistants can be manipulated by various malicious voice commands, yet existing attacks require a nearby speaker to play the attack commands. In this paper, we show that even when no speakers are available, we can play malicious commands by utilizing the capacitors inside electronic devices, i.e., we convert capacitors into speakers and call it CapSpeaker. Essentially, capacitors can emit acoustic noises due to the inverse piezoelectric effect, i.e., varying the voltage across a capacitor can make it vibrate and thus emit acoustic noises. Forcing capacitors to play malicious voice commands is challenging because (1) the frequency responses of capacitors as speakers have poor performance in the range of audible voices, and (2) we have no direct control over the voltage across capacitors to manipulate their emitting sounds. To overcome the challenges, we use a PWM-based modulation scheme to embed the malicious audio onto a high-frequency carrier, e.g., above 20 kHz, and we create malware that can induce the right voltage across the capacitors such that CapSpeaker plays the chosen malicious commands. We conducted extensive experiments with 2 LED lamps (a modified one and a commercial one) and 5 victim devices (iPhone 4s, iPad mini 5, Huawei Nova 5i, etc.). Evaluation results demonstrate that CapSpeaker is feasible at a distance up to 10.5 cm, triggering a smartphone to receive voice commands, e.g., \"open the door''.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485389"}, {"primary_key": "2100659", "vector": [], "sparse_vector": [], "title": "MPC-Friendly Commitments for Publicly Verifiable Covert Security.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We address the problem of efficiently verifying a commitment in a two-party computation. This addresses the scenario where a party P1 commits to a value x to be used in a subsequent secure computation with another party P2 that wants to receive assurance that P<PERSON> did not cheat, i.e. that x was indeed the value inputted into the secure computation. Our constructions operate in the publicly verifiable covert (PVC) security model, which is a relaxation of the malicious model of MPC, appropriate in settings where P1 faces a reputational harm if caught cheating.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485375"}, {"primary_key": "2100661", "vector": [], "sparse_vector": [], "title": "Themis: Ambiguity-Aware Network Intrusion Detection based on Symbolic Model Comparison.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON>"], "summary": "Network intrusion detection systems (NIDS) can be evaded by carefully crafted packets that exploit implementation-level discrepancies between how they are processed on the NIDS and at the endhosts. These discrepancies arise due to the plethora of endhost implementations and evolutions thereof. It is prohibitive to proactively employ a large set of implementations at the NIDS and check incoming packets against all of those. Hence, NIDS typically choose simplified implementations that attempt to approximate and generalize across the different endhost implementations. Unfortunately, this solution is fundamentally flawed since such approximations are bound to have discrepancies with some endhost implementations. In this paper, we develop a lightweight system Themis, which empowers the NIDS in identifying these discrepancies and reactively forking its connection states when any packets with \"ambiguities\" are encountered. Specifically, Themis incorporates an offline phase in which it extracts models from various popular implementations using symbolic execution. During runtime, it maintains a nondeterministic finite automaton to keep track of the states for each possible implementation. Our extensive evaluations show that Themis is extremely effective and can detect all evasion attacks known to date, while consuming extremely low overhead. En route, we also discovered multiple previously unknown discrepancies that can be exploited to bypass current NIDS.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484762"}, {"primary_key": "2100662", "vector": [], "sparse_vector": [], "title": "The Exact Security of BIP32 Wallets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In many cryptocurrencies, the problem of key management has become one of the most fundamental security challenges. Typically, keys are kept in designated schemes called wallets, whose main purpose is to store these keys securely. One such system is the BIP32 wallet (Bitcoin Improvement Proposal 32), which since its introduction in 2012 has been adopted by countless Bitcoin users and is one of the most frequently used wallet system today. Surprisingly, very little is known about the concrete security properties offered by this system. In this work, we propose the first formal analysis of the BIP32 system in its entirety and without any modification. Building on the recent work of <PERSON> et al. (CCS '19), we put forth a formal model for hierarchical deterministic wallet systems (such as BIP32) and give a security reduction in this model from the existential unforgeability of the ECDSA signature algorithm that is used in BIP32. We conclude by giving concrete security parameter estimates achieved by the BIP32 standard, and show that by moving to an alternative key derivation method we can achieve a tighter reduction offering an additional 20 bits of security (111 vs. 91 bits of security) at no additional costs.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484807"}, {"primary_key": "2100663", "vector": [], "sparse_vector": [], "title": "Revisiting Hybrid Private Information Retrieval.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Private Information Retrieval (PIR) allows a client to request entries from a public database held by k servers without revealing any information about the requested data to the servers. PIR is classified into two classes: (i) Multi-server PIR protocols where the request is split among k≥2 non-colluding servers, and Single-server PIR protocols where exactly k=1 server holds the database while the query is protected via certain computational hardness assumptions.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485346"}, {"primary_key": "2100664", "vector": [], "sparse_vector": [], "title": "Rusted Anchors: A National Client-Side View of Hidden Root CAs in the Web PKI Ecosystem.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Lu", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "HTTPS secures communications in the web and heavily relies on the Web PKI for authentication. In the Web PKI, Certificate Authorities (CAs) are organizations that provide trust and issue digital certificates. Web clients rely on public root stores maintained by operating systems or browsers, with hundreds of audited CAs as trust anchors. However, as reported by security incidents, hidden root CAs beyond the public root programs have been imported into local root stores, which allows adversaries to gain trust from web clients.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484768"}, {"primary_key": "2100666", "vector": [], "sparse_vector": [], "title": "AI-Lancet: Locating Error-inducing Neurons to Optimize Neural Networks.", "authors": ["<PERSON><PERSON>", "Hong <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep neural network (DNN) has been widely utilized in many areas due to its increasingly high accuracy. However, DNN models could also produce wrong outputs due to internal errors, which may lead to severe security issues. Unlike fixing bugs in traditional computer software, tracing the errors in DNN models and fixing them are much more difficult due to the uninterpretability of DNN. In this paper, we present a novel and systematic approach to trace and fix the errors in deep learning models. In particular, we locate the error-inducing neurons that play a leading role in the erroneous output. With the knowledge of error-inducing neurons, we propose two methods to fix the errors: the neuron-flip and the neuron-fine-tuning. We evaluate our approach using five different training datasets and seven different model architectures. The experimental results demonstrate its efficacy in different application scenarios, including backdoor removal and general defects fixing.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484818"}, {"primary_key": "2100667", "vector": [], "sparse_vector": [], "title": "I Can See the Light: Attacks on Autonomous Vehicles Using Invisible Lights.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "The camera is one of the most important sensors for an autonomous vehicle (AV) to perform Environment Perception and Simultaneous Localization and Mapping (SLAM). To secure the camera, current autonomous vehicles not only utilize the data gathered from multiple sensors (e.g., Camera, Ultrasonic Sensor, Radar, or LiDAR) for environment perception and SLAM but also require the human driver to always realize the driving situation, which can effectively defend against previous attack approaches (i.e., creating visible fake objects or introducing perturbations to the camera by using advanced deep learning techniques). Different from their work, in this paper, we in-depth investigate the features of Infrared light and introduce a new security challenge called I-Can-See-the-Light- Attack (ICSL Attack) that can alter environment perception results and introduce SLAM errors to the AV. Specifically, we found that the invisible infrared lights (IR light) can successfully trigger the image sensor while human eyes cannot perceive IR lights. Moreover, the IR light appears magenta color in the camera, which triggers different pixels from the ambient visible light and can be selected as key points during the AV's SLAM process. By leveraging these features, we explore to i) generate invisible traffic lights, ii) create fake invisible objects, iii) ruin the in-car user experience, and iv) introduce SLAM errors to the AV. We implement the ICSL Attack by using off-the-shelf IR light sources and conduct an extensive evaluation on Tesla Model 3 and an enterprise-level autonomous driving platform under various environments and settings. We demonstrate the effectiveness of the ICSL Attack and prove that current autonomous vehicle companies have not yet considered the ICSL Attack, which introduces severe security issues. To secure the AV, by exploring unique features of the IR light, we propose a software-based detection module to defend against the ICSL Attack.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484766"}, {"primary_key": "2100670", "vector": [], "sparse_vector": [], "title": "Automated Privacy Policy Annotation with Information Highlighting Made Practical Using Deep Representations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Ülkü Meteriz-Yildiran", "<PERSON>"], "summary": "The privacy policy statements are the primary mean for service providers to inform Internet users about their data collection and use practices, although they often are long and lack a specific structure. In this work, we introduce TLDR, a pipeline that employs various deep representation techniques for normalizing policies through learning and modeling, and an automated ensemble classifier for privacy policy classification. TLDR advances the state-of-the-art by (i) categorizing policy contents into nine privacy policy categories with high accuracy, (ii) detecting missing information in privacy policies, and (iii) significantly reducing policy reading time and improving understandability by users.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485335"}, {"primary_key": "2100671", "vector": [], "sparse_vector": [], "title": "Aion: Enabling Open Systems through Strong Availability Guarantees for Enclaves.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Embedded Trusted Execution Environments (TEEs) can provide strong security for software in the IoT or in critical control systems. Approaches to combine this security with real-time and availability guarantees are currently missing. In this paper we present Aion, a configurable security architecture that provides a notion of guaranteed real-time execution for dynamically loaded enclaves. We implement preemptive multitasking and restricted atomicity on top of strong enclave software isolation and attestation. Our approach allows the hardware to enforce confidentiality and integrity protections, while a decoupled small enclaved scheduler software component can enforce availability and guarantee strict deadlines of a bounded number of protected applications, without necessarily introducing a notion of priorities amongst these applications. We implement a prototype on a light-weight TEE processor and provide a case study. Our implementation can guarantee that protected applications can handle interrupts and make progress with deterministic activation latencies, even in the presence of a strong adversary with arbitrary code execution capabilities.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484782"}, {"primary_key": "2100672", "vector": [], "sparse_vector": [], "title": "Machine-checked ZKP for NP relations: Formally Verified Security Proofs and Implementations of MPC-in-the-Head.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "MPC-in-the-Head (MitH) is a general framework that enables constructing efficient zero-knowledge (ZK) protocols for NP relations from secure multiparty computation (MPC) protocols. In this paper we present the first machine-checked implementations of MitH.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484771"}, {"primary_key": "2100673", "vector": [], "sparse_vector": [], "title": "Modular Design of Secure Group Messaging Protocols and the Security of MLS.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Messaging Layer Security (MLS) project is an IETF effort aiming to establish an industry-wide standard for secure group messaging (SGM). Its development is supported by several major secure-messaging providers (with a combined user base in the billions) and a growing body of academic research. MLS has evolved over many iterations to become a complex, non-trivial, yet relatively ad-hoc cryptographic protocol. In an effort to tame its complexity and build confidence in its security, past analyses of MLS have restricted themselves to sub-protocols of MLS---most prominently a type of sub-protocol embodying so-called continuous group key agreement (CGKA). However, to date the task of proving or even defining the security of the full MLS protocol has been left open. In this work, we fill in this missing piece. First, we formally capture the security of SGM protocols by defining a corresponding security game, which is parametrized by a safety predicate that characterizes the exact level of security achieved by a construction. Then, we cast MLS as an SGM protocol, showing how to modularly build it from the following three main components (and some additional standard cryptographic primitives) in a black-box fashion: (a) CGKA, (b) forward-secure group AEAD (FS-GAEAD), which is a new primitive and roughly corresponds to an \"epoch'' of group messaging, and (c) a so-called PRF-PRNG, which is a two-input hash function that is a pseudorandom function (resp.\\ generator with input) in its first (resp.\\ second) input. Crucially, the security predicate for the SGM security of MLS can be expressed purely as a function of the security predicates of the underlying primitives, which allows to swap out any of the components and immediately obtain a security statement for the resulting SGM construction. Furthermore, we provide instantiations of all component primitives, in particular of CGKA with MLS's TreeKEM sub-protocol (which we prove adaptively secure) and of FS-GAEAD with a novel construction (which has already been adopted by MLS). Along the way we introduce a collection of new techniques, primitives, and results with applications to other SGM protocols and beyond. For example, we extend the Generalized Selective Decryption proof technique (which is central in CGKA literature) and prove adaptive security for another (practical) more secure CGKA protocol called RTreeKEM (Alwen et al.,\\ CRYPTO '20). The modularity of our approach immediately yields a corollary characterizing the security of an SGM construction using RTreeKEM.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484820"}, {"primary_key": "2100675", "vector": [], "sparse_vector": [], "title": "De-identification of Unstructured Clinical Texts from Sequence to Sequence Perspective.", "authors": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this work, we propose a novel problem formulation for de-identification of unstructured clinical text. We formulate the de-identification problem as a sequence to sequence learning problem instead of a token classification problem. Our approach is inspired by the recent state-of -the-art performance of sequence to sequence learning models for named entity recognition. Early experimentation of our proposed approach achieved 98.91% recall rate on i2b2 dataset. This performance is comparable to current state-of-the-art models for unstructured clinical text de-identification.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485354"}, {"primary_key": "2100676", "vector": [], "sparse_vector": [], "title": "Secure Graph Analysis at Scale.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present a highly-scalable secure computation of graph algorithms, which hides all information about the topology of the graph or other input values associated with nodes or edges. The setting is where all nodes and edges of the graph are secret-shared between multiple servers, and a secure computation protocol is run between these servers. While the method is general, we demonstrate it in a 3-server setting with an honest majority, with either semi-honest security or full security. A major technical contribution of our work is replacing the usage of secure sort protocols with secure shuffles, which are much more efficient. Full security against malicious behavior is achieved by adding an efficient verification for the shuffle operation, and computing circuits using fully secure protocols. We demonstrate the applicability of this technology by implementing two major algorithms: computing breadth-first search (BFS), which is also useful for contact tracing on private contact graphs, and computing maximal independent set (MIS). We implement both algorithms, with both semi-honest and full security, and run them within seconds on graphs of millions of elements.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484560"}, {"primary_key": "2100677", "vector": [], "sparse_vector": [], "title": "Oblivious Linear Group Actions and Applications.", "authors": ["Nuttapong Attrapadung", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this paper we propose efficient two-party protocols for obliviously applying a (possibly random) linear group action to a data set. Our protocols capture various applications such as oblivious shuffles, circular shifts, matrix multiplications, to name just a few. A notable feature enjoyed by our protocols, is that they admit a round-optimal (more precisely, one-round) online computation phase, once an input-independent off-line computation phase has been completed. Our oblivious shuffle is the first to achieve a round-optimal online phase. The most efficient instantiations of our protocols are obtained in the so-called client-aided client-server setting, where the offline phase is run by a semi-honest input party (client) who will then distribute the generated correlated randomness to the computing parties (servers). When comparing the total running time to the previous best two-party oblivious shuffle protocol by <PERSON> et al. (Asiacrypt 2020), our shuffle protocol in this client-aided setting is up to 105 times and 152 times faster, in the LAN and WAN setting, respectively. We additionally show how the <PERSON> et al. protocol (which is a standard two-party protocol) can be modified to leverage the advantages of the client-aided setting, but show that, even doing so, our scheme is still two times faster in the online phase and 1.34 times faster in total on average.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484584"}, {"primary_key": "2100678", "vector": [], "sparse_vector": [], "title": "Robust Adversarial Attacks Against DNN-Based Wireless Communication Systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "There is significant enthusiasm for the employment of Deep Neural Networks (DNNs) for important tasks in major wireless communication systems: channel estimation and decoding in orthogonal frequency division multiplexing (OFDM) systems, end-to-end autoencoder system design, radio signal classification, and signal authentication. Unfortunately, DNNs can be susceptible to adversarial examples, potentially making such wireless systems fragile and vulnerable to attack. In this work, by designing robust adversarial examples that meet key criteria, we perform a comprehensive study of the threats facing DNN-based wireless systems. We model the problem of adversarial wireless perturbations as an optimization problem that incorporates domain constraints specific to different wireless systems. This allows us to generate wireless adversarial perturbations that can be applied to wireless signals on-the-fly (i.e., with no need to know the target signals a priori), are undetectable from natural wireless noise, and are robust against removal. We show that even in the presence of significant defense mechanisms deployed by the communicating parties, our attack performs significantly better compared to existing attacks against DNN-based wireless systems. In particular, the results demonstrate that even when employing well-considered defenses, DNN-based wireless communication systems are vulnerable to adversarial attacks and call into question the employment of DNNs for a number of tasks in robust wireless communication.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484777"}, {"primary_key": "2100679", "vector": [], "sparse_vector": [], "title": "Demo: Large Scale Analysis on Vulnerability Remediation in Open-source JavaScript Projects.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Given the widespread prevalence of vulnerabilities, remediation is a critical phase that every software project has to go through. When comparing the studies on understanding the security vulnerabilities in software, such as vulnerability discovery and patterns, there is a lack of studies on the vulnerability remediation phase. To address this, we have done a timeline analysis for 130 of the most dependent upon open source projects written in JavaScript language, hosted on GitHub to understand the nature and the lifetime of the vulnerabilities in those projects. We used a static code analyzer on 501K commits from the repositories to identify commits that introduced new vulnerabilities to the code and fixed existing vulnerabilities in the code. In 90% of the projects, we identified that a commit that fixed an existing vulnerability had introduced one or more new vulnerabilities into the code. On average, 16% of the commits intended to fix vulnerabilities have introduced one or more new vulnerabilities from the analyzed projects. We also found that 18% of the total vulnerabilities found in those projects have originated from a commit meant to fix an existing vulnerability, and 78% of those vulnerabilities could have been avoided of introduction if the developers were to use proper internal testing. Here, we demonstrate Sequza, a visualization tool to help organizations detect such instances at the earliest possible.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485357"}, {"primary_key": "2100680", "vector": [], "sparse_vector": [], "title": "EasyPQC: Verifying Post-Quantum Cryptography.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "EasyCrypt is a formal verification tool used extensively for formalizing concrete security proofs of cryptographic constructions. However, the EasyCrypt formal logics consider only classical at- tackers, which means that post-quantum security proofs cannot be formalized and machine-checked with this tool. In this paper we prove that a natural extension of the EasyCrypt core logics permits capturing a wide class of post-quantum cryptography proofs, settling a question raised by (<PERSON><PERSON><PERSON>, POPL 2019). Leveraging our positive result, we implement EasyPQC, an extension of EasyCrypt for post-quantum security proofs, and use EasyPQC to verify post- quantum security of three classic constructions: PRF-based MAC, Full Domain Hash and GPV08 identity-based encryption.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484567"}, {"primary_key": "2100681", "vector": [], "sparse_vector": [], "title": "Mechanized Proofs of Adversarial Complexity and Application to Universal Composability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we enhance the EasyCrypt proof assistant to reason about computational complexity of adversaries. The key technical tool is a Hoare logic for reasoning about computational complexity (execution time and oracle calls) of adversarial computations. Our Hoare logic is built on top of the module system used by EasyCrypt for modeling adversaries. We prove that our logic is sound w.r.t. the semantics of EasyCrypt programs --- we also provide full semantics for the EasyCrypt module system, which was previously lacking.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484548"}, {"primary_key": "2100682", "vector": [], "sparse_vector": [], "title": "Structured Leakage and Applications to Cryptographic Constant-Time and Cost.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many security properties of interest are captured by instrumented semantics that model the functional behavior and the leakage of programs. For several important properties, including cryptographic constant-time (CCT), leakage models are sufficiently abstract that one can define instrumented semantics for high-level and low-level programs. One important goal is then to relate leakage of source programs and leakage of their compilation---this can be used, e.g., to prove preservation of CCT. To simplify this task, we put forward the idea of structured leakage. In contrast to the usual modeling of leakage as a sequence of observations, structured leakage is tightly coupled with the operational semantics of programs. This coupling greatly simplifies the definition of leakage transformers that map the leakage of source programs to leakage of their compilation and yields more precise statements about the preservation of security properties. We illustrate our methods on the Jasmin compiler and prove preservation results for two policies of interest: CCT and cost.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484761"}, {"primary_key": "2100683", "vector": [], "sparse_vector": [], "title": "Verifying Table-Based Elections.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Verifiability is a key requirement for electronic voting. However, the use of cryptographic techniques to achieve it usually requires specialist knowledge to understand; hence voters cannot easily assess the validity of such arguments themselves. To address this, solutions have been proposed using simple tables and checks, which require only simple verification steps with almost no cryptography.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484555"}, {"primary_key": "2100684", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON><PERSON><PERSON> to Brie: Efficient Zero-Knowledge Proofs for Mixed-Mode Arithmetic and Z2k.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Zero-knowledge proofs are highly flexible cryptographic protocols that are an important building block for many secure systems. Typically, these are defined with respect to statements that are formulated as arithmetic operations over a fixed finite field. This inflexibility is a disadvantage when it comes to complex programs, as some fields are more amenable to express certain operations than others. At the same time, there do not seem to be many proofs with a programming model similar to those found in modern computer architectures that perform arithmetic with 32 or 64 bit integers. In this work, we present solutions to both of these problems. First, we show how to efficiently check consistency of secret values between different instances of zero-knowledge protocols based on the commit-and-prove paradigm. This allows a protocol user to easily switch to the most efficient representation for a given task. To achieve this, we modify the extended doubly-authenticated bits (edabits) approach by <PERSON>s<PERSON>der<PERSON> et al. (Crypto 2020), originally developed for MPC, and optimize it for the zero-knowledge setting. As an application of our consistency check, we also introduce protocols for efficiently verifying truncations and comparisons of shared values both modulo a large prime p and modulo 2k. Finally, we complement our conversion protocols with new protocols for verifying arithmetic statements in Z2k. Here, we build upon recent interactive proof systems based on information-theoretic MACs and vector oblivious linear evaluation (VOLE), and show how this paradigm can be adapted to the ring setting. In particular, we show that supporting such modular operations natively in a proof system can be almost as efficient as proofs over large fields or bits, and this also easily plugs into our framework for zero-knowledge conversions.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484812"}, {"primary_key": "2100685", "vector": [], "sparse_vector": [], "title": "Packet Scheduling with Optional Client Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Existing network switches implement scheduling disciplines such as FIFO or deficit round robin that provide good utilization or fairness across flows, but do so at the expense of leaking a variety of information via timing side channels. To address this privacy breach, we propose a new scheduling mechanism for switches called indifferent-first scheduling (IFS). A salient aspect of IFS is that it provides privacy (a notion of strong isolation) to clients that opt-in, while preserving the (good) performance and utilization of FIFO or round robin for clients that are satisfied with the status quo. Such a hybrid scheduling mechanism addresses the main drawback of prior proposals such as time-division multiple access (TDMA) that provide strong isolation at the cost of low utilization and increased packet latency for all clients. We identify limitations of modern programmable switches which inhibit an implementation of IFS without compromising its privacy guarantees, and show that a version of IFS with full security can be implemented at line rate in the recently proposed push-in-first-out (PIFO) queuing architecture.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485371"}, {"primary_key": "2100686", "vector": [], "sparse_vector": [], "title": "Fuzzy Message Detection.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Many privacy-preserving protocols employ a primitive that allows a sender to \"flag\" a message to a recipient's public key, such that only the recipient (who possesses the corresponding secret key) can detect that the message is intended for their use. Examples of such protocols include anonymous messaging, privacy-preserving payments, and anonymous tracing. A limitation of the existing techniques is that recipients cannot easily outsource the detection of messages to a remote server, without revealing to the server the exact set of matching messages. In this work we propose a new class of cryptographic primitives called \\em fuzzy message detection schemes. These schemes allow a recipient to derive a specialized message detection key that can identify correct messages, while also incorrectly identifying non-matching messages with a specific and chosen false positive rate p. This allows recipients to outsource detection work to an untrustworthy server, without revealing precisely which messages belong to the receiver. We show how to construct these schemes under a variety of assumptions; describe several applications of the new technique; and show that our schemes are efficient enough to use in real applications.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484545"}, {"primary_key": "2100687", "vector": [], "sparse_vector": [], "title": "An In-Depth Symbolic Security Analysis of the ACME Standard.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ACME certificate issuance and management protocol, standardized as IETF RFC 8555, is an essential element of the web public key infrastructure (PKI). It has been used by Let's Encrypt and other certification authorities to issue over a billion certificates, and a majority of HTTPS connections are now secured with certificates issued through ACME. Despite its importance, however, the security of ACME has not been studied at the same level of depth as other protocol standards like TLS 1.3 or OAuth. Prior formal analyses of ACME only considered the cryptographic core of early draft versions of ACME, ignoring many security-critical low-level details that play a major role in the 100 page RFC, such as recursive data structures, long-running sessions with asynchronous sub-protocols, and the issuance for certificates that cover multiple domains.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484588"}, {"primary_key": "2100688", "vector": [], "sparse_vector": [], "title": "RandPiper - Reconfiguration-Friendly Random Beacons with Quadratic Communication.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "A random beacon provides a continuous public source of randomness and its applications range from public lotteries to zero-knowledge proofs. Existing random beacon protocols sacrifice either the fault tolerance or the communication complexity for security, or ease of reconfigurability. This work overcomes the challenges with the existing works through a novel communication efficient combination of state machine replication and (Publicly) Verifiable Secret Sharing (PVSS/VSS).", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484574"}, {"primary_key": "2100690", "vector": [], "sparse_vector": [], "title": "Side-Channel Attacks on Query-Based Data Anonymization.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "A longstanding problem in computer privacy is that of data anonymization. One common approach is to present a query interface to analysts, and anonymize on a query-by-query basis. In practice, this approach often uses a standard database back end, and presents the query semantics of the database to the analyst.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484751"}, {"primary_key": "2100691", "vector": [], "sparse_vector": [], "title": "εpsolute: Efficiently Querying Databases While Providing Differential Privacy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>;<PERSON>"], "summary": "As organizations struggle with processing vast amounts of information, outsourcing sensitive data to third parties becomes a necessity. To protect the data, various cryptographic techniques are used in outsourced database systems to ensure data privacy, while allowing efficient querying. A rich collection of attacks on such systems has emerged. Even with strong cryptography, just communication volume or access pattern is enough for an adversary to succeed.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484786"}, {"primary_key": "2100692", "vector": [], "sparse_vector": [], "title": "Secure Multi-party Computation of Differentially Private Heavy Hitters.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Private learning of top-k, i.e., the k most frequent values also called heavy hitters, is a common industry scenario: Companies want to privately learn, e.g., frequently typed new words to improve suggestions on mobile devices, often used browser settings, telemetry data of frequent crashes, heavily shared articles, etc.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484557"}, {"primary_key": "2100693", "vector": [], "sparse_vector": [], "title": "Constantine: Automatic Side-Channel Resistance Using Efficient Control and Data Flow Linearization.", "authors": ["<PERSON>", "Daniele Cono D&apos;Elia", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In the era of microarchitectural side channels, vendors scramble to deploy mitigations for transient execution attacks, but leave traditional side-channel attacks against sensitive software (e.g., crypto programs) to be fixed by developers by means of constant-time programming (i.e., absence of secret-dependent code/data patterns). Unfortunately, writing constant-time code by hand is hard, as evidenced by the many flaws discovered in production side channel-resistant code. Prior efforts to automatically transform programs into constant-time equivalents offer limited security or compatibility guarantees, hindering their applicability to real-world software.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484583"}, {"primary_key": "2100695", "vector": [], "sparse_vector": [], "title": "PARASITE: PAssword Recovery Attack against Srp Implementations in ThE wild.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Protocols for password-based authenticated key exchange (PAKE) allow two users sharing only a short, low-entropy password to establish a secure session with a cryptographically strong key. The challenge in designing such protocols is that they must resist offline dictionary attacks in which an attacker exhaustively enumerates the dictionary of likely passwords in an attempt to match the used password. In this paper, we study the resilience of one particular PAKE against these attacks. Indeed, we focus on the Secure Remote Password (SRP) protocol that was designed by T. Wu in 1998. Despite its lack of formal security proof, SRP has become a de-facto standard. For more than 20 years, many projects have turned towards SRP for their authentication solution, thanks to the availability of open-source implementations with no restrictive licenses. Of particular interest, we mention the Stanford reference implementation (in C and Java) and the OpenSSL one (in C).", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484563"}, {"primary_key": "2100696", "vector": [], "sparse_vector": [], "title": "Evaluating Resilience of Domains in PKI.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Domain Validation of PKI, allows to verify ownership over domains and poses the basis for cryptography. A number of recent attacks led to efforts to enhance the security of domain validation by improving the resilience of the vantage points used by the certificate authorities.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485356"}, {"primary_key": "2100697", "vector": [], "sparse_vector": [], "title": "One Glitch to Rule Them All: Fault Injection Attacks Against AMD&apos;s Secure Encrypted Virtualization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "AMD Secure Encrypted Virtualization (SEV) offers protection mechanisms for virtual machines in untrusted environments through memory and register encryption. To separate security-sensitive operations from software executing on the main x86 cores, SEV leverages the AMD Secure Processor (AMD-SP). This paper introduces a new approach to attack SEV-protected virtual machines (VMs) by targeting the AMD-SP. We present a voltage glitching attack that allows an attacker to execute custom payloads on the AMD-SPs of all microarchitectures that support SEV currently on the market (Zen 1, Zen 2, and Zen 3). The presented methods allow us to deploy a custom SEV firmware on the AMD-SP, which enables an adversary to decrypt a VM's memory. Furthermore, using our approach, we can extract endorsement keys of SEV-enabled CPUs, which allows us to fake attestation reports or to pose as a valid target for VM migration without requiring physical access to the target host. Moreover, we reverse-engineered the Versioned Chip Endorsement Key (VCEK) mechanism introduced with SEV Secure Nested Paging (SEV-SNP). The VCEK binds the endorsement keys to the firmware version of TCB components relevant for SEV. Building on the ability to extract the endorsement keys, we show how to derive valid VCEKs for arbitrary firmware versions. With our findings, we prove that SEV cannot adequately protect confidential data in cloud environments from insider attackers, such as rogue administrators, on currently available CPUs.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484779"}, {"primary_key": "2100698", "vector": [], "sparse_vector": [], "title": "Consistency Analysis of Data-Usage Purposes in Mobile Apps.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Junbum Shin"], "summary": "While privacy laws and regulations require apps and services to disclose the purposes of their data collection to the users (i.e., why do they collect my data?), the data usage in an app's actual behavior does not always comply with the purposes stated in its privacy policy. Automated techniques have been proposed to analyze apps' privacy policies and their execution behavior, but they often overlooked the purposes of the apps' data collection, use and sharing. To mitigate this oversight, we propose PurPliance, an automated system that detects the inconsistencies between the data-usage purposes stated in a natural language privacy policy and those of the actual execution behavior of an Android app. PurPliance analyzes the predicate-argument structure of policy sentences and classifies the extracted purpose clauses into a taxonomy of data purposes. Purposes of actual data usage are inferred from network data traffic. We propose a formal model to represent and verify the data usage purposes in the extracted privacy statements and data flows to detect policy contradictions in a privacy policy and flow-to-policy inconsistencies between network data flows and privacy statements. Our evaluation results of end-to-end contradiction detection have shown PurPliance to improve detection precision from 19% to 95% and recall from 10% to 50% compared to a state-of-the-art method. Our analysis of 23.1k Android apps has also shown PurPliance to detect contradictions in 18.14% of privacy policies and flow-to-policy inconsistencies in 69.66% of apps, indicating the prevalence of inconsistencies of data practices in mobile apps.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484536"}, {"primary_key": "2100703", "vector": [], "sparse_vector": [], "title": "Efficient Linear Multiparty PSI and Extensions to Circuit/Quorum PSI.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Multiparty Private Set Intersection (mPSI), enables n parties, each holding private sets (each of size m) to securely compute the intersection of these private sets. While several protocols are known for this task, the only concretely efficient protocol is due to the work of <PERSON><PERSON><PERSON> et al. (KMPRT, CCS 2017), who gave a semi-honest secure protocol with communication complexity O(nmtƛ), where t < n is the number of corrupt parties and ƛ is the security parameter. In this work, we make the following contributions:", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484591"}, {"primary_key": "2100704", "vector": [], "sparse_vector": [], "title": "When Machine Unlearning Jeopardizes Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "The right to be forgotten states that a data owner has the right to erase their data from an entity storing it. In the context of machine learning (ML), the right to be forgotten requires an ML model owner to remove the data owner's data from the training set used to build the ML model, a process known as machine unlearning. While originally designed to protect the privacy of the data owner, we argue that machine unlearning may leave some imprint of the data in the ML model and thus create unintended privacy risks. In this paper, we perform the first study on investigating the unintended information leakage caused by machine unlearning. We propose a novel membership inference attack that leverages the different outputs of an ML model's two versions to infer whether a target sample is part of the training set of the original model but out of the training set of the corresponding unlearned model. Our experiments demonstrate that the proposed membership inference attack achieves strong performance. More importantly, we show that our attack in multiple cases outperforms the classical membership inference attack on the original ML model, which indicates that machine unlearning can have counterproductive effects on privacy. We notice that the privacy degradation is especially significant for well-generalized ML models where classical membership inference does not perform well. We further investigate four mechanisms to mitigate the newly discovered privacy risks and show that releasing the predicted label only, temperature scaling, and differential privacy are effective. We believe that our results can help improve privacy protection in practical implementations of machine unlearning. Our code is available at https://github.com/MinChen00/UnlearningLeaks.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484756"}, {"primary_key": "2100705", "vector": [], "sparse_vector": [], "title": "Learning Security Classifiers with Verified Global Robustness Properties.", "authors": ["<PERSON><PERSON><PERSON> Chen", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Many recent works have proposed methods to train classifiers with local robustness properties, which can provably eliminate classes of evasion attacks for most inputs, but not all inputs. Since data distribution shift is very common in security applications, e.g., often observed for malware detection, local robustness cannot guarantee that the property holds for unseen inputs at the time of deploying the classifier. Therefore, it is more desirable to enforce global robustness properties that hold for all inputs, which is strictly stronger than local robustness. In this paper, we present a framework and tools for training classifiers that satisfy global robustness properties. We define new notions of global robustness that are more suitable for security classifiers. We design a novel booster-fixer training framework to enforce global robustness properties. We structure our classifier as an ensemble of logic rules and design a new verifier to verify the properties. In our training algorithm, the booster increases the classifier's capacity, and the fixer enforces verified global robustness properties following counterexample guided inductive synthesis. We show that we can train classifiers to satisfy different global robustness properties for three security datasets, and even multiple properties at the same time, with modest impact on the classifier's performance. For example, we train a Twitter spam account classifier to satisfy five global robustness properties, with 5.4% decrease in true positive rate, and 0.1% increase in false positive rate, compared to a baseline XGBoost model that doesn't satisfy any property.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484776"}, {"primary_key": "2100707", "vector": [], "sparse_vector": [], "title": "FakeWake: Understanding and Mitigating Fake Wake-up Words of Voice Assistants.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Xu"], "summary": "In the area of Internet of Things (IoT), voice assistants have become an important interface to operate smart speakers, smartphones, and even automobiles. To save power and protect user privacy, voice assistants send commands to the cloud only if a small set of preregistered wake-up words are detected. However, voice assistants are shown to be vulnerable to the FakeWake phenomena, whereby they are inadvertently triggered by innocent-sounding fuzzy words. In this paper, we present a systematic investigation of the FakeWake phenomena from three aspects. To start with, we design the first fuzzy word generator to automatically and efficiently produce fuzzy words instead of searching through a swarm of audio materials.We manage to generate 965 fuzzy words covering 8 most popular English and Chinese smart speakers. To explain the causes underlying the FakeWake phenomena, we construct an interpretable tree-based decision model, which reveals phonetic features that contribute to false acceptance of fuzzy words by wake-up word detectors. Finally, we propose remedies to mitigate the effect of FakeWake. The results show that the strengthened models are not only resilient to fuzzy words but also achieve better overall performance on original training datasets.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485365"}, {"primary_key": "2100708", "vector": [], "sparse_vector": [], "title": "On Adoptability and Use Case Exploration of Threat Modeling for Mobile Communication Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "As the attack surface and the number of security incidents in mobile communication networks increase, a common language for threat intelligence gathering and sharing among different parties becomes essential. We addressed this by developing the Bhadra framework [4], a domain-specific conceptual framework that captures adversarial behaviors in end-to-end communication over the mobile networks in our previous work. Nevertheless, the acceptance or adoptability of the framework by the mobile communications industry is still unclear. In this work, we built a threat modeling tool as a companion for Bhadra and conduct a user study with industry experts to evaluate the framework's usefulness and explore its potential use cases besides threat modeling and sharing. Our preliminary results indicate that the mobile communication industry would benefit from a threat modeling framework with a companion tool and its use cases, making it a potential candidate to integrate within work processes.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485348"}, {"primary_key": "2100709", "vector": [], "sparse_vector": [], "title": "Indistinguishability Prevents Scheduler Side Channels in Real-Time Systems.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Debopa<PERSON>", "<PERSON><PERSON>"], "summary": "Scheduler side-channels can leak critical information in real-time systems, thus posing serious threats to many safety-critical applications. The main culprit is the inherent determinism in the runtime timing behavior of such systems, e.g., the (expected) periodic behavior of critical tasks. In this paper, we introduce the notion of \"schedule indistinguishability/\", inspired by work in differential privacy, that introduces diversity into the schedules of such systems while offering analyzable security guarantees. We achieve this by adding a sufficiently large (controlled) noise to the task schedules in order to break their deterministic execution patterns. An \"epsilon-Scheduler\" then implements schedule indistinguishability in real-time Linux. We evaluate our system using two real applications: (a) an autonomous rover running on a real hardware platform (Raspberry Pi) and (b) a video streaming application that sends data across large geographic distances. Our results show that the epsilon-Scheduler offers better protection against scheduler side-channel attacks in real-time systems while still maintaining good performance and quality-of-service(QoS) requirements.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484769"}, {"primary_key": "2100710", "vector": [], "sparse_vector": [], "title": "SyzGen: Automated Generation of Syscall Specification of Closed-Source macOS Drivers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Kernel drivers are a critical part of the attack surface since they constitute a large fraction of kernel codebase and oftentimes lack proper vetting, especially for those closed-source ones. Unfortunately, the complex input structure and unknown relationships/dependencies among interfaces make them very challenging to understand. Thus, security analysts primarily rely on manual audit for interface recovery to generate meaningful fuzzing test cases. In this paper, we present SyzGen, a first attempt to automate the generation of syscall specifications for closed-source macOS drivers and facilitate interface-aware fuzzing. We leverage two insights to overcome the challenges of binary analysis: (1) iterative refinement of syscall knowledge and (2) extraction and extrapolation of dependencies from a small number of execution traces. We evaluated our approach on 25 targets. The results show that SyzGen can effectively produce high-quality specifications, leading to 34 bugs, including one that attackers can exploit to escalate privilege, and 2 CVEs to date.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484564"}, {"primary_key": "2100713", "vector": [], "sparse_vector": [], "title": "Compressed Oblivious Encoding for Homomorphically Encrypted Search.", "authors": ["<PERSON><PERSON>", "<PERSON>-<PERSON>ed", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Fully homomorphic encryption (FHE) enables a simple, attractive framework for secure search. Compared to other secure search systems, no costly setup procedure is necessary; it is sufficient for the client merely to upload the encrypted database to the server. Confidentiality is provided because the server works only on the encrypted query and records. While the search functionality is enabled by the full homomorphism of the encryption scheme. For this reason, researchers have been paying increasing attention to this problem. Since <PERSON><PERSON><PERSON> et al. (CCS 2018) presented a framework for secure search on FHE encrypted data and gave a working implementation called SPiRiT, several more efficient realizations have been proposed. In this paper, we identify the main bottlenecks of this framework and show how to significantly improve the performance of FHE-base secure search. In particular, To retrieve l matching items, the existing framework needs to repeat the protocol l times sequentially. In our new framework, all matching items are retrieved in parallel in a single protocol execution. The most recent work by <PERSON> et al. (CCS 2020) requires O(n) multiplications to compute the first matching index. Our solution requires no homomorphic multiplication, instead using only additions and scalar multiplications to encode all matching indices. Our implementation and experiments show that to fetch 16 matching records, our system gives an 1800X speed-up over the state of the art in fetching the query results resulting in a 26X speed-up for the full search functionality.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484792"}, {"primary_key": "2100716", "vector": [], "sparse_vector": [], "title": "Amortized Threshold Symmetric-key Encryption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Gaddam", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Threshold cryptography enables cryptographic operations while keeping the secret keys distributed at all times. <PERSON><PERSON><PERSON> et al. (CCS'18) propose a framework for Distributed Symmetric-key Encryption (DiSE). They introduce a new notion of Threshold Symmetric-key Encryption (TSE), in that encryption and decryption are performed by interacting with a threshold number of servers. However, the necessity for interaction on each invocation limits performance when encrypting large datasets, incurring heavy computation and communication on the servers.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485256"}, {"primary_key": "2100717", "vector": [], "sparse_vector": [], "title": "A One-Pass Distributed and Private Sketch for Kernel Sums with Applications to Machine Learning at Scale.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Differential privacy is a compelling privacy definition that explains the privacy-utility tradeoff via formal, provable guarantees. In machine learning, we often wish to release a function over a dataset while preserving differential privacy. Although there are general algorithms to solve this problem for any function, such methods can require hours to days to run on moderately sized datasets. As a result, most private algorithms address task-dependent functions for specific applications. In this work, we propose a general purpose private sketch, or small summary of the dataset, that supports machine learning tasks such as regression, classification, density estimation, and more. Our sketch is ideal for large-scale distributed settings because it is simple to implement, mergeable, and can be created with a one-pass streaming algorithm. At the heart of our proposal is the reduction of many machine learning objectives to kernel sums. Our sketch estimates these sums using randomized contingency tables that are indexed with locality-sensitive hashing. Existing alternatives for kernel sum estimation scale poorly, often exponentially slower with an increase in dimensions. In contrast, our sketch can quickly run on large high-dimensional datasets, such as the 65 million node Friendster graph, in a single pass that takes less than 20 minutes, which is otherwise infeasible with any known alternative. Exhaustive experiments show that the privacy-utility tradeoff of our method is competitive with existing algorithms, but at an order-of-magnitude smaller computational cost. We expect that our sketch will be practically useful for differential privacy in distributed, large-scale machine learning settings.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485255"}, {"primary_key": "2100718", "vector": [], "sparse_vector": [], "title": "Labeled PSI from Homomorphic Encryption with Reduced Computation and Communication.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is known that fully homomorphic encryption (FHE) can be used to build efficient (labeled) Private Set Intersection protocols in the unbalanced setting, where one of the sets is much larger than the other~(<PERSON> et al. (CCS'17, CCS'18)). In this paper we demonstrate multiple algorithmic improvements upon these works. In particular, our protocol has an asymptotically better computation cost, requiring only O(√|X| ) homomorphic multiplications, and communication complexity sublinear in the larger set size|X|. We demonstrate that our protocol is significantly better than that of <PERSON> et al. (CCS'18) for many practical parameters, especially in terms of online communication cost. For example, when intersecting $228 and 2048 item sets, our protocol reduces the online computation time by more than 71% and communication by more than 63%. When intersecting 224 and 4096 item sets, our protocol reduces the online computation time by 27% and communication by 63%. Our comparison to other state-of-the-art unbalanced PSI protocols shows that our protocol has the best total communication complexity when |X| ≥ 224. For labeled PSI our protocol also outperforms <PERSON> et al. (CCS'18). When intersecting 220 and 256 item sets, with the larger set having associated 288-byte labels, our protocol reduces the online computation time by more than 67% and communication by 34%. Finally, we demonstrate a modification that results in nearly constant communication cost in the larger set size |X|, but impractically high computation complexity on today's CPUs. For example, to intersect a 210-item set with sets of size 222, 224, or 226, our proof-of-concept implementation requires only 0.76 MB of online communication, which is more than a 24-fold improvement over <PERSON> et al. (CCS'18).", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484760"}, {"primary_key": "2100720", "vector": [], "sparse_vector": [], "title": "Wireless Charging Power Side-Channel Attacks.", "authors": ["Alexander <PERSON> La Cour", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This paper shows that today's wireless charging interface is vulnerable to power side-channel attacks; a smartphone charging wirelessly leaks private information about its activity to the wireless charger (charging transmitter). We present a website fingerprinting attack through the wireless charging side-channel for both iOS and Android devices. The attack monitors the current drawn by the wireless charging transmitter while 20 webpages from the Alexa top sites list are loaded on a charging smartphone. We implement a classifier that correctly identifies unlabeled current traces with an accuracy of 87% on average for an iPhone 11 and 95% on average for a Google Pixel 4. This represents a considerable security threat because wireless charging does not require any user permission if the phone is within the range of a charging transmitter. To the best of our knowledge, this work represents the first to introduce and demonstrate a power side-channel attack through wireless charging. Additionally, this study compares the wireless charging side-channel with the wired USB charging power side-channel, showing that they are comparable. We find that the performance of the attack deteriorates as the contents of websites change over time. Furthermore, we discover that the amount of information leakage through both wireless and wired charging interfaces heavily depends on the battery level; minimal information is leaked at low battery levels.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484733"}, {"primary_key": "2100721", "vector": [], "sparse_vector": [], "title": "SmashEx: Smashing SGX Enclaves Using Exceptions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Exceptions are a commodity hardware functionality which is central to multi-tasking OSes as well as event-driven user applications. Normally, the OS assists the user application by lifting the semantics of exceptions received from hardware to program-friendly user signals and exception handling interfaces. However, can exception handlers work securely in user enclaves, such as those enabled by Intel SGX, where the OS is not trusted by the enclave code?", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484821"}, {"primary_key": "2100722", "vector": [], "sparse_vector": [], "title": "&quot;I need a better description&quot;: An Investigation Into User Expectations For Differential Privacy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Despite recent widespread deployment of differential privacy, relatively little is known about what users think of differential privacy. In this work, we seek to explore users' privacy expectations related to differential privacy. Specifically, we investigate (1) whether users care about the protections afforded by differential privacy, and (2) whether they are therefore more willing to share their data with differentially private systems. Further, we attempt to understand (3) users' privacy expectations of the differentially private systems they may encounter in practice and (4) their willingness to share data in such systems. To answer these questions, we use a series of rigorously conducted surveys (n=2424). We find that users care about the kinds of information leaks against which differential privacy protects and are more willing to share their private information when the risks of these leaks are less likely to happen. Additionally, we find that the ways in which differential privacy is described in-the-wild haphazardly set users' privacy expectations, which can be misleading depending on the deployment. We synthesize our results into a framework for understanding a user's willingness to share information with differentially private systems, which takes into account the interaction between the user's prior privacy concerns and how differential privacy is described.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485252"}, {"primary_key": "2100725", "vector": [], "sparse_vector": [], "title": "Let&apos;s Downgrade Let&apos;s Encrypt.", "authors": ["<PERSON><PERSON>xiang Dai", "<PERSON><PERSON>", "<PERSON>"], "summary": "Following the recent off-path attacks against PKI, Let's Encrypt deployed in 2020 domain validation from multiple vantage points to ensure security even against the stronger on-path MitM adversaries. The idea behind such distributed domain validation is that even if the adversary can hijack traffic of some vantage points, it will not be able to intercept traffic of all the vantage points to all the nameservers in a domain.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484815"}, {"primary_key": "2100726", "vector": [], "sparse_vector": [], "title": "Facilitating Vulnerability Assessment through PoC Migration.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recent research shows that, even for vulnerability reports archived by MITRE/NIST, they usually contain incomplete information about the software's vulnerable versions, making users of under-reported vulnerable versions at risk. In this work, we address this problem by introducing a fuzzing-based method. Technically, this approach first collects the crashing trace on the reference version of the software. Then, it utilizes the trace to guide the mutation of the PoC input so that the target version could follow the trace similar to the one observed on the reference version. Under the mutated input, we argue that the target version's execution could have a higher chance of triggering the bug and demonstrating the vulnerability's existence. We implement this idea as an automated tool, named VulScope. Using 30 real-world CVEs on 470 versions of software, VulScope is demonstrated to introduce no false positives and only 7.9% false negatives while migrating PoC from one version to another. Besides, we also compare our method with two representative fuzzing tools AFL and AFLGO. We find VulScope outperforms both of these existing techniques while taking the task of PoC migration. Finally, by using VulScope, we identify 330 versions of software that MITRE/NIST fails to report as vulnerable.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484594"}, {"primary_key": "2100727", "vector": [], "sparse_vector": [], "title": "Asynchronous Data Dissemination and its Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we introduce the problem of Asynchronous Data Dissemination (ADD). Intuitively, an ADD protocol disseminates a message to all honest nodes in an asynchronous network, given that at least t+1 honest nodes initially hold the message where t is the maximum number of malicious nodes. We design a simple and efficient ADD protocol for n parties that is information-theoretically secure, tolerates up to one-third malicious nodes, and has a communication cost of O(n|M|+n2) for disseminating a message M. We then use our ADD protocol to improve many important primitives in cryptography and distributed computing. For asynchronous reliable broadcast (RBC), assuming collision-resistant hash functions, we give a RBC protocol with communication cost O(n|M| + κ n2) where κ is the size of the hash function output. This improves over the prior best scheme with communication cost O(n|M| + κ n2 łog n) under the same setting. Our improved RBC protocol immediately improves the communication cost of asynchronous atomic broadcast and Asynchronous Distributed Key Generation (ADKG) protocols. We also use our improved RBC protocol along with additional new techniques to improve the communication cost of Asynchronous Verifiable Secret Sharing (AVSS), Asynchronous Complete Secret Sharing (ACSS), and dual-threshold ACSS from O(κ n2 łog n) to O(κ n2) without using any trusted setup.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484808"}, {"primary_key": "2100728", "vector": [], "sparse_vector": [], "title": "On Re-engineering the X.509 PKI with Executable Specification for Better Implementation Guarantees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>"], "summary": "The X.509 Public-Key Infrastructure (PKI) standard is widely used as a scalable and flexible authentication mechanism. Flaws in X.509 implementations can make relying applications susceptible to impersonation attacks or interoperability issues. In practice, many libraries implementing X.509 have been shown to suffer from flaws that are due to noncompliance with the standard. Developing a compliant implementation is especially hindered by the design complexity, ambiguities, or under-specifications in the standard written in natural languages. In this paper, we set out to alleviate this unsatisfactory state of affairs by re-engineering and formalizing a widely used fragment of the X.509 standard specification, and then using it to develop a high-assurance implementation. Our X.509 specification re-engineering effort is guided by the principle of decoupling the syntactic requirements from the semantic requirements. For formalizing the syntactic requirements of X.509 standard, we observe that a restricted fragment of attribute grammar is sufficient. In contrast, for precisely capturing the semantic requirements imposed on the most-widely used X.509 features, we use quantifier-free first-order logic (QFFOL). Interestingly, using QFFOL results in an executable specification that can be efficiently enforced by an SMT solver. We use these and other insights to develop a high-assurance X.509 implementation named CERES. A comparison of CERES with 3 mainstream libraries (i.e., mbedTLS, OpenSSL, and GnuTLS) based on 2 million real certificate chains and 2 million synthetic certificate chains shows that CERES rightfully rejects malformed and invalid certificates.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484793"}, {"primary_key": "2100729", "vector": [], "sparse_vector": [], "title": "Hiding the Lengths of Encrypted Messages via Gaussian Padding.", "authors": ["<PERSON>"], "summary": "Secure network protocols like TLS, QUIC, SSH and IPsec allow for additional padding to be used during encryption in order to hide message lengths. While it is impossible to conceal message lengths completely, without drastically degrading efficiency, such mechanisms aim at causing as much frustration as possible to the prospective attacker. However, none of the protocol specifications provide any guidance on how to select the length of this padding. Several works have highlighted how the leakage of message lengths can be exploited in attacks, but the converse problem of how to best defend against such attacks remains relatively understudied. We make this the focus of our work and present a formal treatment of length hiding security in a general setting. Prior work by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> suggested that sampling the padding length uniformly at random already achieves the best possible security. However we show that this is only true in the limited setting where only a single ciphertext is available to the adversary. If multiple ciphertexts are available to the adversary, then sampling the padding length according to a Gaussian distribution yields quantifiably better security for the same overhead. In fact, in this setting, uniformly random padding turns out to be among the worst possible choices. We confirm experimentally the superior performance of Gaussian padding over uniform padding in the context of the CRIME/BREACH attack.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484590"}, {"primary_key": "2100730", "vector": [], "sparse_vector": [], "title": "The Security of ChaCha20-Poly1305 in the Multi-User Setting.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "The ChaCha20-Poly1305 AEAD scheme is being increasingly widely deployed in practice. Practitioners need proven security bounds in order to set data limits and rekeying intervals for the scheme. But the formal security analysis of ChaCha20-Poly1305 currently lags behind that of AES-GCM. The only extant analysis (<PERSON>cter, 2014) contains a flaw and is only for the single-user setting. We rectify this situation. We prove a multi-user security bound on the AEAD security of ChaCha20-Poly1305 and establish the tightness of each term in our bound through matching attacks. We show how our bound differs both qualitatively and quantitatively from the known bounds for AES-GCM, highlighting how subtle design choices lead to distinctive security properties. We translate our bound to the nonce-randomized setting employed in TLS 1.3 and elsewhere, and we additionally improve the corresponding security bounds for GCM. Finally, we provide a simple yet stronger variant of ChaCha20-Poly1305 that addresses the deficiencies highlighted by our analysis.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484814"}, {"primary_key": "2100732", "vector": [], "sparse_vector": [], "title": "DEMO: A Secure Voting System for Score Based Elections.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "<PERSON><PERSON> et al. recently proposed a secure voting protocol for score-based elections, where independent talliers perform the tallying procedure. The protocol offers perfect ballot secrecy: it outputs the identity of the winner(s), but keeps all other information secret, even from the talliers. This high level of privacy, which may encourage voters to vote truthfully, and the protocol's extremely lightweight nature, make it a most adequate and powerful tool for democracies of any size. We have implemented that system and in this work we describe the system's components - election administrators, voters and talliers - and its operation. Our implementation is in Python and is open source. We view this demo as an essential step towards convincing decision makers in communities that practice score-based elections to adopt it as their election platform.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485343"}, {"primary_key": "2100735", "vector": [], "sparse_vector": [], "title": "This Sneaky <PERSON> to the Android Ad Market: Misusing Mobile Sensors for Stealthy Data Exfiltration.", "authors": ["<PERSON><PERSON><PERSON>", "Serafeim Moustakas", "Lichao Sun", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Mobile sensors have transformed how users interact with modern smartphones and enhance their overall experience. However, the absence of sufficient access control for protecting these sensors enables a plethora of threats. As prior work has shown, malicious apps and sites can deploy a wide range of attacks that use data captured from sensors. Unfortunately, as we demonstrate, in the modern app ecosystem where most apps fetch and render third-party web content, attackers can use ads for delivering attacks. In this paper, we introduce a novel attack vector that misuses the advertising ecosystem for delivering sophisticated and stealthy attacks that leverage mobile sensors. These attacks do not depend on any special app permissions or specific user actions, and affect all Android apps that contain in-app advertisements due to the improper access control of sensor data in WebView. We outline how motion sensor data can be used to infer users' sensitive touch input (e.g., credit card information) in two distinct attack scenarios, namely intra-app and inter-app data exfiltration. While the former targets the app displaying the ad, the latter affects every other Android app running on the device. To make matters worse, we have uncovered serious flaws in Android's app isolation, life cycle management, and access control mechanisms that enable persistent data exfiltration even after the app showing the ad is moved to the background or terminated by the user. Furthermore, as in-app ads can \"piggyback\" on the permissions intended for the app's core functionality, they can also obtain information from protected sensors such as the camera, microphone and GPS. To provide a comprehensive assessment of this emerging threat, we conduct a large-scale, end-to-end, dynamic analysis of ads shown in apps available in the official Android Play Store. Our study reveals that ads in the wild are already accessing and leaking data obtained from motion sensors, thus highlighting the need for stricter access control policies and isolation mechanisms.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485366"}, {"primary_key": "2100737", "vector": [], "sparse_vector": [], "title": "APECS: A Distributed Access Control Framework for Pervasive Edge Computing Services.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Edge Computing is a new computing paradigm where applications operate at the network edge, providing low-latency services with augmented user and data privacy. A desirable goal for edge computing is pervasiveness, that is, enabling any capable and authorized entity at the edge to provide desired edge services--pervasive edge computing (PEC). However, efficient access control of users receiving services and edge servers handling user data, without sacrificing performance is a challenge. Current solutions, based on \"always-on\" authentication servers in the cloud, negate the latency benefits of services at the edge and also do not preserve user and data privacy. In this paper, we present APECS, an advanced access control framework for PEC, which allows legitimate users to utilize any available edge services without need for communication beyond the network edge. The APECS framework leverages multi-authority attribute-based encryption to create a federated authority, which delegates the authentication and authorization tasks to semi-trusted edge servers, thus eliminating the need for an \"always-on\" authentication server in the cloud. Additionally, APECS prevents access to encrypted content by unauthorized edge servers. We analyze and prove the security of APECS in the Universal Composability framework and provide experimental results on the GENI testbed to demonstrate the scalability and effectiveness of APECS.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484804"}, {"primary_key": "2100740", "vector": [], "sparse_vector": [], "title": "AHEAD: Adaptive Hierarchical Decomposition for Range Query under Local Differential Privacy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Shouling Ji", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "For protecting users' private data, local differential privacy (LDP) has been leveraged to provide the privacy-preserving range query, thus supporting further statistical analysis. However, existing LDP-based range query approaches are limited by their properties, ie, collecting user data according to a pre-defined structure. These static frameworks would incur excessive noise added to the aggregated data especially in the low privacy budget setting. In this work, we propose an Adaptive Hierarchical Decomposition (AHEAD) protocol, which adaptively and dynamically controls the built tree structure, so that the injected noise is well controlled for maintaining high utility. Furthermore, we derive a guideline for properly choosing parameters for AHEAD so that the overall utility can be consistently competitive while rigorously satisfying LDP. Leveraging multiple real and synthetic datasets, we extensively show the effectiveness of AHEAD in both low and high dimensional range query scenarios, as well as its advantages over the state-of-the-art methods. In addition, we provide a series of useful observations for deploying \\myahead in practice.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485668"}, {"primary_key": "2100741", "vector": [], "sparse_vector": [], "title": "Cert-RNN: Towards Certifying the Robustness of Recurrent Neural Networks.", "authors": ["<PERSON><PERSON><PERSON>", "Shouling Ji", "<PERSON><PERSON><PERSON>", "<PERSON>", "Jinfeng Li", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Certifiable robustness, the functionality of verifying whether the given region surrounding a data point admits any adversarial example, provides guaranteed security for neural networks deployed in adversarial environments. A plethora of work has been proposed to certify the robustness of feed-forward networks, e.g., FCNs and CNNs. Yet, most existing methods cannot be directly applied to recurrent neural networks (RNNs), due to their sequential inputs and unique operations.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484538"}, {"primary_key": "2100742", "vector": [], "sparse_vector": [], "title": "Faster Lattice-Based KEMs via a Generic Fujisaki-Okamoto Transform Using Prefix Hashing.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Constructing an efficient CCA-secure KEM is generally done by first constructing a passively-secure PKE scheme, and then applying the Fujisaki-Okamoto (FO) transformation. The original FO transformation was designed to offer security in a single user setting. A stronger notion, known as multi-user security, considers the attacker's advantage in breaking one of many user's ciphertexts. <PERSON><PERSON> et al. (EUROCRYPT 2000) showed that standard single user security implies multi-user security with a multiplicative tightness gap equivalent to the number of users.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484819"}, {"primary_key": "2100744", "vector": [], "sparse_vector": [], "title": "Pseudo-Randomness and the Crystal Ball.", "authors": ["<PERSON>"], "summary": "The last decade has witnessed the emergence of algorithmic fairness as a new frontier in the application of theoretical computer science to problems of societal concern. The delay between academic investigation and industrial rhetoric acknowledging the concern has been surprisingly brief. This alacrity has positive and negative consequences, to wit, opportunity for quick adoption of technology and pressure for quick fixes.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3482790"}, {"primary_key": "2100746", "vector": [], "sparse_vector": [], "title": "Dissecting Residual APIs in Custom Android ROMs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Many classic software vulnerabilities (e.g., Heartbleed) are rooted in unused code. In this work, we aim to understand whether unused Android functionality may similarly open unnecessary attack opportunities. Our study focuses on OEM-introduced APIs, which are added and removed erratically through different device models and releases. This instability contributes to the production of bloated custom APIs, some of which may not even be used on a particular device. We call such unused APIs Residuals.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485374"}, {"primary_key": "2100750", "vector": [], "sparse_vector": [], "title": "POSTER: ReAvatar: Virtual Reality De-anonymization Attack Through Correlating Movement Signatures.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Virtual reality (VR) is on the precipice of entering mainstream entertainment with devices equipped with a multitude of sensing, tracking, and internet capabilities that can reshape the current infotainment industry such as online gaming or conferences with novel features. With VR techniques, the online gamer or conference attendances could choose to keep their identity anonymous by easily altering their appearances (i.e., avatars). However, in this study, we present ReAvatar, a novel de-anonymization attack that identifies users by their virtual avatar via a correlation in specific recorded movements. Using 3D pose estimation, we train a sophisticated machine learning model with user movement data recorded while performing a set of movements in real life and then again with their avatars. We then map correlations between these two sets of movement data using a bespoke agglomerative clustering algorithm and establish relationship between the user's virtual and real-life identity. ReAvatar achieves 89.60% accuracy in detecting a unique user among multiple avatars. The security and privacy implications of this paper will be foundational for users and researchers alike that explore the realm of virtual reality.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485345"}, {"primary_key": "2100752", "vector": [], "sparse_vector": [], "title": "Zero Knowledge Static Program Analysis.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Static program analysis tools can automatically prove many useful properties of programs. However, using static analysis to prove to a third party that a program satisfies a property requires revealing the program's source code. We introduce the concept of zero-knowledge static analysis, in which the prover constructs a zero-knowledge proof about the outcome of the static analysis without revealing the program. We present novel zero-knowledge proof schemes for intra- and inter-procedural abstract interpretation. Our schemes are significantly more efficient than the naive translation of the corresponding static analysis algorithms using existing schemes. We evaluate our approach empirically on real and synthetic programs; with a pairing-based zero knowledge proof scheme as the backend, we are able to prove the control flow analysis on a 2,000-line program in 1,738s. The proof is only 128 bytes and the verification time is 1.4ms. With a transparent zero knowledge proof scheme based on discrete-log, we generate the proof for the tainting analysis on a 12,800-line program in 406 seconds, the proof size is 282 kilobytes, and the verification time is 66 seconds.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484795"}, {"primary_key": "2100753", "vector": [], "sparse_vector": [], "title": "DoubleX: Statically Detecting Vulnerable Data Flows in Browser Extensions at Scale.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Browser extensions are popular to enhance users' browsing experience. By design, they have access to security- and privacy-critical APIs to perform tasks that web applications cannot traditionally do. Even though web pages and extensions are isolated, they can communicate through messages. Specifically, a vulnerable extension can receive messages from another extension or web page, under the control of an attacker. Thus, these communication channels are a way for a malicious actor to elevate their privileges to the capabilities of an extension, which can lead to, e.g., universal cross-site scripting or sensitive user data exfiltration. To automatically detect such security and privacy threats in benign-but-buggy extensions, we propose our static analyzer DoubleX. DoubleX defines an Extension Dependence Graph (EDG), which abstracts extension code with control and data flows, pointer analysis, and models the message interactions within and outside of an extension. This way, we can leverage this graph to track and detect suspicious data flows between external actors and sensitive APIs in browser extensions. We evaluated DoubleX on 154,484 Chrome extensions, where it flags 278 extensions as having a suspicious data flow. Overall, we could verify that 89% of these flows can be influenced by external actors (i.e., an attacker). Based on our threat model, we subsequently demonstrate exploitability for 184 extensions. Finally, we evaluated DoubleX on a labeled vulnerable extension set, where it accurately detects almost 93% of known flaws.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484745"}, {"primary_key": "2100754", "vector": [], "sparse_vector": [], "title": "Snipuzz: Black-box Fuzzing of IoT Firmware via Message Snippet Inference.", "authors": ["<PERSON><PERSON><PERSON>", "Ruoxi Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Dong<PERSON> Liu", "Surya Nepal", "<PERSON>"], "summary": "The proliferation of Internet of Things (IoT) devices has made people's lives more convenient, but it has also raised many security concerns. Due to the difficulty of obtaining and emulating IoT firmware, in the absence of internal execution information, black-box fuzzing of IoT devices has become a viable option. However, existing black-box fuzzers cannot form effective mutation optimization mechanisms to guide their testing processes, mainly due to the lack of feedback. In addition, because of the prevalent use of various and non-standard communication message formats in IoT devices, it is difficult or even impossible to apply existing grammar-based fuzzing strategies. Therefore, an efficient fuzzing approach with syntax inference is required in the IoT fuzzing domain.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484543"}, {"primary_key": "2100755", "vector": [], "sparse_vector": [], "title": "On the (In)Security of ElGamal in OpenPGP.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Roughly four decades ago, <PERSON><PERSON> put forward what is today one of the most widely known and best understood public key encryption schemes. ElGamal encryption has been used in many different contexts, chiefly among them by the OpenPGP standard. Despite its simplicity, or perhaps because of it, in reality there is a large degree of ambiguity on several key aspects of the cipher. Each library in the OpenPGP ecosystem seems to have implemented a slightly different \"flavour\" of ElGamal encryption. While --taken in isolation-- each implementation may be secure, we reveal that in the interoperable world of OpenPGP, unforeseen cross-configuration attacks become possible. Concretely, we propose different such attacks and show their practical efficacy by recovering plaintexts and even secret keys.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485257"}, {"primary_key": "2100756", "vector": [], "sparse_vector": [], "title": "A Formally Verified Configuration for Hardware Security Modules in the Cloud.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Flaminia L. Luccio"], "summary": "Hardware Security Modules (HSMs) are trusted machines that perform sensitive operations in critical ecosystems. They are usually required by law in financial and government digital services. The most important feature of an HSM is its ability to store sensitive credentials and cryptographic keys inside a tamper-resistant hardware, so that every operation is done internally through a suitable API, and such sensitive data are never exposed outside the device. HSMs are now conveniently provided in the cloud, meaning that the physical machines are remotely hosted by some provider and customers can access them through a standard API. The property of keeping sensitive data inside the device is even more important in this setting as a vulnerable application might expose the full API to an attacker. Unfortunately, in the last 20+ years a multitude of practical API-level attacks have been found and proved feasible in real devices. The latest version of PKCS#11, the most popular standard API for HSMs, does not address these issues leaving all the flaws possible. In this paper, we propose the first secure HSM configuration that does not require any restriction or modification of the PKCS#11 API and is suitable to cloud HSM solutions, where compliance to the standard API is of paramount importance. The configuration relies on a careful separation of roles among the different HSM users so that known API flaws are not exploitable by any attacker taking control of the application. We prove the correctness of the configuration by providing a formal model in the state-of-the-art <PERSON><PERSON><PERSON> prover and we show how to implement the configuration in a real cloud HSM solution.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484785"}, {"primary_key": "2100757", "vector": [], "sparse_vector": [], "title": "Constant-Overhead Zero-Knowledge for RAM Programs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Chen<PERSON>"], "summary": "We show a constant-overhead interactive zero-knowledge (ZK) proof system for RAM programs, that is, a ZK proof in which the communication complexity as well as the running times of the prover and verifier scale linearly in the size of the memory N and the running time T of the underlying RAM program. Besides yielding an asymptotic improvement of prior work, our implementation gives concrete performance improvements for RAM-based ZK proofs. In particular, our implementation supports ZK proofs of private read/write accesses to 64~MB of memory (224 32-bit words) using only 34~bytes of communication per access, a more than 80x improvement compared to the recent BubbleRAM protocol. We also design a lightweight RISC CPU that can efficiently emulate the MIPS-I instruction set, and for which our ZK proof communicates only ~320 bytes per cycle, more than 10x less than the BubbleRAM CPU. In a 100 Mbps network, we can perform zero-knowledge executions of our CPU (with 64~MB of main memory and 4~MB of program memory) at a clock rate of 6.6 KHz.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484800"}, {"primary_key": "2100760", "vector": [], "sparse_vector": [], "title": "CPscan: Detecting Bugs Caused by Code Pruning in IoT Kernels.", "authors": ["Lirong Fu", "Shouling Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "To reduce the development costs, IoT vendors tend to construct IoT kernels by customizing the Linux kernel. Code pruning is common in this customization process. However, due to the intrinsic complexity of the Linux kernel and the lack of long-term effective maintenance, IoT vendors may mistakenly delete necessary security operations in the pruning process, which leads to various bugs such as memory leakage and NULL pointer dereference. Yet detecting bugs caused by code pruning in IoT kernels is difficult. Specifically, (1) a significant structural change makes precisely locating the deleted security operations (DSO ) difficult, and (2) inferring the security impact of a DSO is not trivial since it requires complex semantic understanding, including the developing logic and the context of the corresponding IoT kernel.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484738"}, {"primary_key": "2100761", "vector": [], "sparse_vector": [], "title": "Realtime Robust Malicious Traffic Detection via Frequency Domain Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Machine learning (ML) based malicious traffic detection is an emerging security paradigm, particularly for zero-day attack detection, which is complementary to existing rule based detection. However, the existing ML based detection achieves low detection accuracy and low throughput incurred by inefficient traffic features extraction. Thus, they cannot detect attacks in realtime, especially in high throughput networks. Particularly, these detection systems similar to the existing rule based detection can be easily evaded by sophisticated attacks. To this end, we propose <PERSON><PERSON>per, a realtime ML based malicious traffic detection system that achieves both high accuracy and high throughput by utilizing frequency domain features. It utilizes sequential information represented by the frequency domain features to achieve bounded information loss, which ensures high detection accuracy, and meanwhile constrains the scale of features to achieve high detection throughput. In particular, attackers cannot easily interfere with the frequency domain features and thus <PERSON>hisper is robust against various evasion attacks. Our experiments with 42 types of attacks demonstrate that, compared with the state-of-the-art systems, <PERSON><PERSON><PERSON> can accurately detect various sophisticated and stealthy attacks, achieving at most 18.36% improvement of AUC, while achieving two orders of magnitude throughput. Even under various evasion attacks, <PERSON><PERSON><PERSON> is still able to maintain around 90% detection accuracy.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484585"}, {"primary_key": "2100762", "vector": [], "sparse_vector": [], "title": "C3PO: Large-Scale Study Of Covert Monitoring of C&amp;C Servers via Over-Permissioned Protocol Infiltration.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Current techniques to monitor botnets towards disruption or takedown are likely to result in inaccurate data gathered about the botnet or be detected by C&C orchestrators. Seeking a covert and scalable solution, we look to an evolving pattern in modern malware that integrates standardized over-permissioned protocols, exposing privileged access to C&C servers. We implement techniques to detect and exploit these protocols from over-permissioned bots toward covert C&C server monitoring. Our empirical study of 200k malware captured since 2006 revealed 62,202 over-permissioned bots (nearly 1 in 3) and 443,905 C&C monitoring capabilities, with a steady increase of over-permissioned protocol use over the last 15 years. Due to their ubiquity, we conclude that even though over-permissioned protocols allow for C&C server infiltration, the efficiency and ease of use they provide continue to make them prevalent in the malware operational landscape. This paper presents C3PO, a pipeline that enables our study and empowers incident responders to automatically identify over-permissioned protocols, infiltration vectors to spoof bot-to-C&C communication, and C&C monitoring capabilities that guide covert monitoring post infiltration. Our findings suggest the over-permissioned protocol weakness provides a scalable approach to covertly monitor C&C servers, which is a fundamental enabler of botnet disruptions and takedowns.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484537"}, {"primary_key": "2100765", "vector": [], "sparse_vector": [], "title": "HyperFuzzer: An Efficient Hybrid Fuzzer for Virtual CPUs.", "authors": ["Xinyang Ge", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Weidong Cui"], "summary": "In this cloud computing era, the security of hypervisors is critical to the overall security of the cloud. In particular, the security of CPU virtualization in hypervisors is paramount because it is implemented in the most privileged CPU mode. Blackbox and graybox fuzzing are limited to finding shallow virtual CPU bugs due to its huge search space. Whitebox fuzzing can be used for systematic analysis of CPU virtualization, but existing implementations rely on slow hardware emulators to enable dynamic symbolic execution.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484748"}, {"primary_key": "2100766", "vector": [], "sparse_vector": [], "title": "On the Rényi Differential Privacy of the Shuffle Model.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "The central question studied in this paper is Rényi Differential Privacy (RDP) guarantees for general discrete local randomizers in the shuffle privacy model. In the shuffle model, each of the n clients randomizes its response using a local differentially private (LDP) mechanism and the untrusted server only receives a random permutation (shuffle) of the client responses without association to each client. The principal result in this paper is the first direct RDP bounds for general discrete local randomization in the shuffle privacy model, and we develop new analysis techniques for deriving our results which could be of independent interest. In applications, such an RDP guarantee is most useful when we use it for composing several private interactions. We numerically demonstrate that, for important regimes, with composition our bound yields an improvement in privacy guarantee by a factor of $8\\times$ over the state-of-the-art approximate Differential Privacy (DP) guarantee (with standard composition) for shuffle models. Moreover, combining with Poisson subsampling, our result leads to at least $10\\times$ improvement over subsampled approximate DP with standard composition.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484794"}, {"primary_key": "2100767", "vector": [], "sparse_vector": [], "title": "Solver-Aided Constant-Time Hardware Verification.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We present Xenon, a solver-aided, interactive method for formally verifying that Verilog hardware executes in constant-time. Xenon scales to realistic hardware designs by drastically reducing the effort needed to localize the root cause of verification failures via a new notion of constant-time counterexamples, which Xenon uses to synthesize a minimal set of secrecy assumptions in an interactive verification loop. To reduce verification time Xenon exploits modularity in Verilog code via module summaries, thereby avoiding duplicate work across multiple module instantiations. We show how Xenon's assumption synthesis and summaries enable us to verify different kinds of circuits, including a highly modular AES- 256 implementation where modularity cuts verification from six hours to under three seconds, and the ScarVside-channel hardened RISC-V micro-controller whose size exceeds previously verified designs by an order of magnitude. In a small study, we also find that Xenon helps non-expert users complete verification tasks correctly and faster than previous state-of-art tools.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484810"}, {"primary_key": "2100768", "vector": [], "sparse_vector": [], "title": "The Invisible Shadow: How Security Cameras Leak Private Activities.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "This paper presents a new privacy threat, the Invisible Infrared Shadow Attack (IRSA), which leverages the inconspicuous infrared (IR) light emitted by indoor security cameras, to reveal in-home human activities behind opaque curtains. The key observation is that the in-home IR light source can project invisible shadows on the window curtains, which can be captured by an attacker outside using an IR-capable camera. The major challenge for IRSA lies in the shadow deformation caused by a variety of environmental factors involving the IR source position and curtain shape, which distorts the body contour. A two-stage attack scheme is proposed to circumvent the challenge. Specifically, a DeShaNet model performs accurate shadow keypoint detection through multi-dimension feature fusion. Then a scene constructor maps the 2D shadow keypoints to 3D human skeletons by iteratively reproducing the on-site shadow projection process in a virtual Unity 3D environment. Through comprehensive evaluation, we show that the proposed attack scheme can be successfully launched to recover 3D skeleton of the victims, even under severe shadow deformation. Finally, we propose potential defense mechanisms against the IRSA.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484741"}, {"primary_key": "2100769", "vector": [], "sparse_vector": [], "title": "<PERSON><PERSON>, Test, Execute: Adversarial Tactics in Amplification DDoS Attacks.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Amplification attacks generate an enormous flood of unwanted traffic towards a victim and are generated with the help of open, unsecured services, to which an adversary sends spoofed service requests that trigger large answer volumes to a victim. However, the actual execution of the packet flood is only one of the activities necessary for a successful attack. Adversaries need, for example, to develop attack tools, select open services to abuse, test them, and adapt the attacks if necessary, each of which can be implemented in myriad ways. Thus, to understand the entire ecosystem and how adversaries work, we need to look at the entire chain of activities.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484747"}, {"primary_key": "2100770", "vector": [], "sparse_vector": [], "title": "The Return of Eratosthenes: Secure Generation of RSA Moduli using Distributed Sieving.", "authors": ["<PERSON><PERSON><PERSON> Guilhem", "Elef<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Secure multiparty generation of an RSA biprime is a challenging task, which increasingly receives attention, due to the numerous privacy-preserving applications that require it. In this work, we construct a new protocol for the RSA biprime generation task, secure against a malicious adversary, who can corrupt any subset of protocol participants. Our protocol is designed with generic multiparty computation (MPC), making it both platform-independent and allowing for weaker security models to be assumed (e.g., honest majority), should the application scenario require it. By carefully \"postponing\" the check of possible inconsistencies in the shares provided by malicious adversaries, we achieve noteworthy efficiency improvements. Concretely, we are able to produce additive sharings of the prime candidates, from multiplicative sharings via a semi-honest multiplication, without degrading the overall (active) security of our protocol. This is the core of our sieving technique, increasing the probability of our protocol sampling a biprime. Similarly, we perform the first biprimality test, requiring several repetitions, without checking input share consistency, and perform the more costly consistency check only in case of success of the Jacobi symbol based biprimality test. Moreover, we propose a protocol to convert an additive sharing over a ring, into an additive sharing over the integers. Besides being a necessary sub-protocol for the RSA biprime generation, this conversion protocol is of independent interest. The cost analysis of our protocol demonstrated that our approach improves the current state-of-the-art (Chen et al.-Crypto 2020), in terms of communication efficiency. Concretely, for the two-party case with malicious security, and primes of 2048bits, our protocol improves communication by a factor of ~37.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484754"}, {"primary_key": "2100771", "vector": [], "sparse_vector": [], "title": "Limbo: Efficient Zero-knowledge MPCitH-based Arguments.", "authors": ["<PERSON><PERSON><PERSON> Guilhem", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "This work introduces a new interactive oracle proof system based on the MPC-in-the-Head paradigm. To improve concrete efficiency and offer flexibility between computation time and communication size, a generic proof construction based on multi-round MPC protocols is proposed, instantiated with a specific protocol and implemented and compared to similar proof systems.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484595"}, {"primary_key": "2100773", "vector": [], "sparse_vector": [], "title": "DroneKey: A Drone-Aided Group-Key Generation Scheme for Large-Scale IoT Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The Internet of Things (IoT) networks are finding massive applications in mission-critical contexts. A group key is needed to encrypt and authenticate broadcast/multicast messages commonly seen in large-scale wireless networks. In this paper, we propose DroneKey, a novel drone-aided PHY-based Group-Key Generation (GKG) scheme for large-scale IoT networks. In DroneKey, a drone is dispatched to fly along random 3D trajectories and keep broadcasting standard wireless signals to refresh the group keys in the whole network. Every IoT device receives the broadcast signals from which to extract the Channel State Information (CSI) stream which captures the dynamic variations of the individual wireless channel between the IoT device and the drone. DroneKey explores a deep-learning approach to extract the hidden correlation among the CSI streams to establish a common group key. We thoroughly evaluate DroneKey with a prototype in both indoor and outdoor environments. We show that DroneKey can achieve a high key-generation rate of 89.5 bit/sec for 10 devices in contrast to 40 bit/sec in the state-of-art prior work. In addition, DroneKey is much more scalable and can support 100 devices in contrast to 10 nodes in the state-of-art prior work with comparable key-generate rates.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484789"}, {"primary_key": "2100774", "vector": [], "sparse_vector": [], "title": "DeepAID: Interpreting and Improving Deep Learning-based Anomaly Detection in Security Applications.", "authors": ["Dongqi Han", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Xingang Shi", "<PERSON><PERSON>"], "summary": "Unsupervised Deep Learning (DL) techniques have been widely used in various security-related anomaly detection applications, owing to the great promise of being able to detect unforeseen threats and superior performance provided by Deep Neural Networks (DNN). However, the lack of interpretability creates key barriers to the adoption of DL models in practice. Unfortunately, existing interpretation approaches are proposed for supervised learning models and/or non-security domains, which are unadaptable for unsupervised DL models and fail to satisfy special requirements in security domains. In this paper, we propose DeepAID, a general framework aiming to (1) interpret DL-based anomaly detection systems in security domains, and (2) improve the practicality of these systems based on the interpretations. We first propose a novel interpretation method for unsupervised DNNs by formulating and solving well-designed optimization problems with special constraints for security domains. Then, we provide several applications based on our Interpreter as well as a model-based extension Distiller to improve security systems by solving domain-specific problems. We apply DeepAID over three types of security-related anomaly detection systems and extensively evaluate our Interpreter with representative prior works. Experimental results show that DeepAID can provide high-quality interpretations for unsupervised DL models while meeting the special requirements of security domains. We also provide several use cases to show that DeepAID can help security operators to understand model decisions, diagnose system mistakes, give feedback to models, and reduce false positives.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484589"}, {"primary_key": "2100775", "vector": [], "sparse_vector": [], "title": "With a Little Help from My Friends: Constructing Practical Anonymous Credentials.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Anonymous credentials (ACs) are a powerful cryptographic tool for the secure use of digital services, when simultaneously aiming for strong privacy guarantees of users combined with strong authentication guarantees for providers of services. They allow users to selectively prove possession of attributes encoded in a credential without revealing any other meaningful information about themselves. While there is a significant body of research on AC systems, modern use-cases of ACs such as mobile applications come with various requirements not sufficiently considered so far. These include preventing the sharing of credentials and coping with resource constraints of the platforms (e.g., smart cards such as SIM cards in smartphones). Such aspects are typically out of scope of AC constructions, and, thus AC systems that can be considered entirely practical have been elusive so far.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484582"}, {"primary_key": "2100776", "vector": [], "sparse_vector": [], "title": "It&apos;s Not What It Looks Like: Manipulating Perceptual Hashing based Applications.", "authors": ["<PERSON><PERSON>", "Licheng Luo", "<PERSON>", "<PERSON>"], "summary": "Perceptual hashing is widely used to search or match similar images for digital forensics and cybercrime study. Unfortunately, the robustness of perceptual hashing algorithms is not well understood in these contexts. In this paper, we examine the robustness of perceptual hashing and its dependent security applications both experimentally and empirically. We first develop a series of attack algorithms to subvert perceptual hashing based image search. This is done by generating attack images that effectively enlarge the hash distance to the original image while introducing minimal visual changes. To make the attack practical, we design the attack algorithms under a black-box setting, augmented with novel designs (e.g., grayscale initialization) to improve the attack efficiency and transferability. We then evaluate our attack against the standard pHash as well as its robust variant using three different datasets. After confirming the attack effectiveness experimentally, we then empirically test against real-world reverse image search engines including TinEye, Google, Microsoft Bing, and Yandex. We find that our attack is highly successful on TinEye and Bing, and is moderately successful on Google and Yandex. Based on our findings, we discuss possible countermeasures and recommendations.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484559"}, {"primary_key": "2100778", "vector": [], "sparse_vector": [], "title": "A Concrete Treatment of Efficient Continuous Group Key Agreement via Multi-Recipient PKEs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Continuous group key agreements (CGKAs) are a class of protocols that can provide strong security guarantees to secure group messaging protocols such as Signal and MLS. Protection against device compromise is provided by commit messages: at a regular rate, each group member may refresh their key material by uploading a commit message, which is then downloaded and processed by all the other members. In practice, propagating commit messages dominates the bandwidth consumption of existing CGKAs.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484817"}, {"primary_key": "2100779", "vector": [], "sparse_vector": [], "title": "Learning to Explore Paths for Symbolic Execution.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Symbolic execution is a powerful technique that can generate tests steering program execution into desired paths. However, the scalability of symbolic execution is often limited by path explosion, i.e., the number of symbolic states representing the paths under exploration quickly explodes as execution goes on. Therefore, the effectiveness of symbolic execution engines hinges on the ability to select and explore the right symbolic states.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484813"}, {"primary_key": "2100780", "vector": [], "sparse_vector": [], "title": "SoFi: Reflection-Augmented Fuzzing for JavaScript Engines.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yuekang Li", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wenchang Shi", "<PERSON>"], "summary": "JavaScript engines have been shown prone to security vulnerabilities, which can lead to serious consequences due to their popularity. Fuzzing is an effective testing technique to discover vulnerabilities. The main challenge of fuzzing JavaScript engines is to generate syntactically and semantically valid inputs such that deep functionalities can be explored. However, due to the dynamic nature of JavaScript and the special features of different engines, it is quite challenging to generate semantically meaningful test inputs.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484823"}, {"primary_key": "2100781", "vector": [], "sparse_vector": [], "title": "Quantifying and Mitigating Privacy Risks of Contrastive Learning.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Data is the key factor to drive the development of machine learning (ML) during the past decade. However, high-quality data, in particular labeled data, is often hard and expensive to collect. To leverage large-scale unlabeled data, self-supervised learning, represented by contrastive learning, is introduced. The objective of contrastive learning is to map different views derived from a training sample (e.g., through data augmentation) closer in their representation space, while different views derived from different samples more distant. In this way, a contrastive model learns to generate informative representations for data samples, which are then used to perform downstream ML tasks. Recent research has shown that machine learning models are vulnerable to various privacy attacks. However, most of the current efforts concentrate on models trained with supervised learning. Meanwhile, data samples' informative representations learned with contrastive learning may cause severe privacy risks as well. In this paper, we perform the first privacy analysis of contrastive learning through the lens of membership inference and attribute inference. Our experimental results show that contrastive models trained on image datasets are less vulnerable to membership inference attacks but more vulnerable to attribute inference attacks compared to supervised models. The former is due to the fact that contrastive models are less prone to overfitting, while the latter is caused by contrastive models' capability of representing data samples expressively. To remedy this situation, we propose the first privacy-preserving contrastive learning mechanism, <PERSON><PERSON>, relying on adversarial training. Empirical results show that <PERSON><PERSON> can successfully mitigate attribute inference risks for contrastive models while maintaining their membership privacy and model utility.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484571"}, {"primary_key": "2100782", "vector": [], "sparse_vector": [], "title": "Feature-Indistinguishable Attack to Circumvent Trapdoor-Enabled Defense.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Hu"], "summary": "Deep neural networks (DNNs) are vulnerable to adversarial attacks. A great effort has been directed to developing effective defenses against adversarial attacks and finding vulnerabilities of proposed defenses. A recently proposed defense called Trapdoor-enabled Detection (TeD) deliberately injects trapdoors into DNN models to trap and detect adversarial examples targeting categories protected by TeD. TeD can effectively detect existing state-of-the-art adversarial attacks. In this paper, we propose a novel black-box adversarial attack on TeD, called Feature-Indistinguishable Attack (FIA). It circumvents TeD by crafting adversarial examples indistinguishable in the feature (i.e., neuron-activation) space from benign examples in the target category. To achieve this goal, FIA jointly minimizes the distance to the expectation of feature representations of benign samples in the target category and maximizes the distances to positive adversarial examples generated to query TeD in the preparation phase. A constraint is used to ensure that the feature vector of a generated adversarial example is within the distribution of feature vectors of benign examples in the target category. Our extensive empirical evaluation with different configurations and variants of TeD indicates that our proposed FIA can effectively circumvent TeD. FIA opens a door for developing much more powerful adversarial attacks. The FIA code is available at: https://github.com/CGCL-codes/FeatureIndistinguishableAttack.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485378"}, {"primary_key": "2100783", "vector": [], "sparse_vector": [], "title": "One Hot Garbling.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Garbled Circuit (GC) is the main practical 2PC technique, yet despite great interest in its performance, GC notoriously resists improvement. Essentially, we only know how to evaluate GC functions gate-by-gate using encrypted truth tables; given input labels, the GC evaluator decrypts the corresponding output label. Interactive protocols enjoy more sophisticated techniques. For example, we can expose to a party a (masked) private value. The party can then perform useful local computation and feed the resulting cleartext value back into the MPC. Such techniques are not known to work for GC. We show that it is, in fact, possible to improve GC efficiency, while keeping its round complexity, by exposing masked private values to the evaluator. %without introducing rounds of communication. Our improvements use garbled one-hot encodings of values. By using this encoding we improve a number of interesting functions, e.g., matrix multiplication, integer multiplication, field element multiplication, field inverses and AES S-Boxes, integer exponents, and more. We systematize our approach by providing a framework for designing such GC modules. Our constructions are concretely efficient. E.g., we improve binary matrix multiplication inside GC by more than 6x in terms of communication and by more than 4x in terms of WAN wall-clock time. Our improvement circumvents an important GC lower bound and may open GC to further improvement.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484764"}, {"primary_key": "2100784", "vector": [], "sparse_vector": [], "title": "Predictive Cipher-Suite Negotiation for Boosting Deployment of New Ciphers.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deployment of strong cryptographic ciphers for DNSSEC is essential for long term security of DNS. Unfortunately, due to the hurdles involved in adoption of new ciphers coupled with the limping deployment of DNSSEC, most domains use the weak RSA-1024 cipher.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485349"}, {"primary_key": "2100786", "vector": [], "sparse_vector": [], "title": "PPE Circuits for Rational Polynomials.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Pairings are a powerful algebraic setting for realizing cryptographic functionalities. One challenge for cryptographers who design pairing systems is that the complexity of many systems in terms of the number of group elements and equations to verify has been steadily increasing over the past decade and is approaching the point of being unwieldy. To combat this challenge, multiple independent works have utilized computers to help with the system design. One common design task that researchers seek to automate is summarized as follows: given a description of a set of trusted elements T (e.g., a public key) and a set of untrusted elements U (e.g., a signature), automatically generate an algorithm that verifies U with respect to T using the pairing and group operations. To date, none of the prior automation works for this task have support for solutions with rational polynomials in the exponents despite many pairing constructions employing them (e.g., <PERSON><PERSON>-<PERSON><PERSON> signatures, Gentry's IBE, Dodis-Yampolskiy VRF). We demonstrate how to support this essential class of pairing systems for automated exploration. Specifically, we present a solution for automatically generating a verification algorithm with novel support for rational polynomials. The class of verification algorithms we consider in this work is called PPE Circuits (introduced in [HVW20]). Intuitively, a PPE Circuit is a circuit supporting pairing and group operations, which can test whether a set of elements U verifies with respect to a set of elements T. We provide a formalization of the problem, an algorithm for searching for a PPE Circuit supporting rational polynomials, a software implementation, and a detailed performance evaluation. Our implementation was tested on over three dozen schemes, including over ten test cases that our tool can handle, but prior tools could not. For all test cases where a PPE Circuit exists, the tool produced a solution in three minutes or less.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484562"}, {"primary_key": "2100787", "vector": [], "sparse_vector": [], "title": "New Directions in Automated Traffic Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Despite the use of machine learning for many network traffic analysis tasks in security, from application identification to intrusion detection, the aspects of the machine learning pipeline that ultimately determine the performance of the model -- feature selection and representation, model selection, and parameter tuning -- remain manual and painstaking. This paper presents a method to automate many aspects of traffic analysis, making it easier to apply machine learning techniques to a wider variety of traffic analysis tasks. We introduce nPrint, a tool that generates a unified packet representation that is amenable for representation learning and model training. We integrate nPrint with automated machine learning (AutoML), resulting in nPrintML, a public system that largely eliminates feature extraction and model tuning for a wide variety of traffic analysis tasks. We have evaluated nPrintML on eight separate traffic analysis tasks and released nPrint and nPrintML to enable future work to extend these methods.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484758"}, {"primary_key": "2100788", "vector": [], "sparse_vector": [], "title": "POSTER: A Tough Nut to Crack: Attempting to Break Modulation Obfuscation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Despite being primarily developed for spectrum management, sharing, and enforcement in civilian and military applications, modulation classification can be exploited by an adversary to threaten user privacy (e.g., via traffic analysis), or launch jamming and spoofing attacks. Several existing works study how an adversary can still classify the user traffic despite obfuscation techniques at upper layers, but little work has been done on how an adversary can classify the \"modulation scheme'' when it is obfuscated at the physical layer. In this respect, we aim to study how to break the state-of-the-art modulation obfuscation schemes by applying various machine learning (ML) methods. Our preliminary results show that common ML techniques perform poorly in correctly classifying an obfuscated modulation scheme except for the random forest method (with a score as much as twice the other techniques we consider), providing insights on why other techniques, e.g., deep learning, might be more promising for finding underlying correlations.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485344"}, {"primary_key": "2100789", "vector": [], "sparse_vector": [], "title": "Membership Inference Attacks against GANs by Leveraging Over-representation Regions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Generative adversarial networks (GANs) have made unprecedented performance in image synthesis and play a key role in various downstream applications of computer vision. However, GAN models trained on sensitive data also pose a distinct threat to privacy. In this poster, we present a novel over-representation based membership inference attack. Unlike prior attacks against GANs which focus on the overall metrics, such as the attack accuracy, our attack aims to make inference from the high-precision perspective, which allows the adversary to concentrate on inferring a sample as a member confidently. Initial experimental results demonstrate that the adversary can achieve a high precision attack even if the overall attack accuracy is about 50% for a well-trained GAN model. Our work will raise awareness of the importance of precision when GAN owners evaluate the privacy risks of their models.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485338"}, {"primary_key": "2100790", "vector": [], "sparse_vector": [], "title": "TableGAN-MCA: Evaluating Membership Collisions of GAN-Synthesized Tabular Data Releasing.", "authors": ["Aoting Hu", "<PERSON><PERSON><PERSON>", "Zhigang Lu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Generative Adversarial Networks (GAN)-synthesized table publishing lets people privately learn insights without access to the private table. However, existing studies on Membership Inference (MI) Attacks show promising results on disclosing membership of training datasets of GAN-synthesized tables. Different from those works focusing on discovering membership of a given data point, in this paper, we propose a novel Membership Collision Attack against GANs (TableGAN-MCA), which allows an adversary given only synthetic entries randomly sampled from a black-box generator to recover partial GAN training data. Namely, a GAN-synthesized table immune to state-of-the-art MI attacks is vulnerable to the TableGAN-MCA. The success of TableGAN-MCA is boosted by an observation that GAN-synthesized tables potentially collide with the training data of the generator.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485251"}, {"primary_key": "2100791", "vector": [], "sparse_vector": [], "title": "All your Credentials are Belong to Us: On Insecure WPA2-Enterprise Configurations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> <PERSON>"], "summary": "In this paper, we perform the first multifaceted measurement study to investigate the widespread insecure practices employed by tertiary education institutes (TEIs) around the globe when offering WPA2-Enterprise Wi-Fi services. The security of such services critically hinges on two aspects: (1) the connection configuration on the client-side; and (2) the TLS setup on the authentication servers. Weaknesses in either can leave users susceptible to credential theft. Typically, TEIs prescribe to their users either manual instructions or pre-configured profiles (e.g., eduroam CAT). For studying the security of configurations, we present a framework in which each configuration is mapped to an abstract security label drawn from a strict partially ordered set. We first used this framework to evaluate the configurations supported by the user interfaces (UIs) of mainstream operating systems (OSs), and discovered many design weaknesses. We then considered 7045 TEIs in 54 countries/regions, and collected 7275 configuration instructions from 2061 TEIs. Our analysis showed that majority of these instructions lead to insecure configurations, and nearly 86% of those TEIs can suffer from credential thefts on at least one OS. We also analyzed a large corpus of pre-configured eduroam CAT profiles and discovered several misconfiguration issues that can negatively impact security. Finally, we evaluated the TLS parameters used by authentication servers of thousands of TEIs and discovered perilous practices, such as the use of expired certificates, deprecated versions of TLS, weak signature algorithms, and suspected cases of private key reuse among TEIs. Our long list of findings have been responsibly disclosed to the relevant stakeholders, many of which have already been positively acknowledged.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484569"}, {"primary_key": "2100792", "vector": [], "sparse_vector": [], "title": "COINN: Crypto/ML Codesign for Oblivious Inference via Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We introduce COINN - an efficient, accurate, and scalable framework for oblivious deep neural network (DNN) inference in the two-party setting. In our system, DNN inference is performed without revealing the client's private inputs to the server or revealing server's proprietary DNN weights to the client. To speedup the secure inference while maintaining a high accuracy, we make three interlinked innovations in the plaintext and ciphertext domains: (i) we develop a new domain-specific low-bit quantization scheme tailored for high-efficiency ciphertext computation, (ii) we construct novel techniques for increasing data re-use in secure matrix multiplication allowing us to gain significant performance boosts through factored operations, and (iii) we propose customized cryptographic protocols that complement our optimized DNNs in the ciphertext domain. By co-optimization of the aforesaid components, COINN brings an unprecedented level of efficiency to the setting of oblivious DNN inference, achieving an end-to-end runtime speedup of 4.7×14.4× over the state-of-the-art. We demonstrate the scalability of our proposed methods by optimizing complex DNNs with over 100 layers and performing oblivious inference in the Billion-operation regime for the challenging ImageNet dataset. Our framework is available at https://github.com/ACESLabUCSD/COINN.git.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484797"}, {"primary_key": "2100793", "vector": [], "sparse_vector": [], "title": "Noncompliance as Deviant Behavior: An Automated Black-box Noncompliance Checker for 4G LTE Cellular Devices.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The paper focuses on developing an automated black-box testing approach called DIKEUE that checks 4G Long Term Evolution (LTE) control-plane protocol implementations in commercial-off-the-shelf (COTS) cellular devices (also, User Equipments or UEs) for noncompliance with the standard. Unlike prior noncompliance checking approaches which rely on property-guided testing, DIKEUE adopts a property-agnostic, differential testing approach, which leverages the existence of many different control-plane protocol implementations in COTS UEs. DIKEUE uses deviant behavior observed during differential analysis of pairwise COTS UEs as a proxy for identifying noncompliance instances. For deviant behavior identification, DIKEUE first uses black-box automata learning, specialized for 4G LTE control-plane protocols, to extract input-output finite state machine (FSM) for a given UE. It then reduces the identification of deviant behavior in two extracted FSMs as a model checking problem. We applied DIKEUE in checking noncompliance in 14 COTS UEs from 5 vendors and identified 15 new deviant behavior as well as 2 previous implementation issues. Among them, 11 are exploitable whereas 3 can cause potential interoperability issues.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485388"}, {"primary_key": "2100797", "vector": [], "sparse_vector": [], "title": "Shorter and Faster Post-Quantum Designated-Verifier zkSNARKs from Lattices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Zero-knowledge succinct arguments of knowledge (zkSNARKs) enable efficient privacy-preserving proofs of membership for general NP languages. Our focus in this work is on post-quantum zkSNARKs, with a focus on minimizing proof size. Currently, there is a 1000x gap in the proof size between the best pre-quantum constructions and the best post-quantum ones. Here, we develop and implement new lattice-based zkSNARKs in the designated-verifier preprocessing model. With our construction, after an initial preprocessing step, a proof for an NP relation of size 2^20 is just over 16 KB. Our proofs are 10.3x shorter than previous post-quantum zkSNARKs for general NP languages. Compared to previous lattice-based zkSNARKs (also in the designated-verifier preprocessing model), we obtain a 42x reduction in proof size and a 60x reduction in the prover's running time, all while achieving a much higher level of soundness. Compared to the shortest pre-quantum zkSNARKs by <PERSON><PERSON> (Eurocrypt 2016), the proof size in our lattice-based construction is 131x longer, but both the prover and the verifier are faster (by 1.2x and 2.8x, respectively). Our construction follows the general blueprint of <PERSON><PERSON><PERSON> et al. (TCC 2013) and <PERSON><PERSON> et al. (Eurocrypt 2017) of combining a linear probabilistically checkable proof (linear PCP) together with a linear-only vector encryption scheme. We develop a concretely-efficient lattice-based instantiation of this compiler by considering quadratic extension fields of moderate characteristic and using linear-only vector encryption over rank-2 module lattices.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484572"}, {"primary_key": "2100798", "vector": [], "sparse_vector": [], "title": "VIP: Safeguard Value Invariant Property for Thwarting Critical Memory Corruption Attacks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Jang", "Chang<PERSON><PERSON> Min"], "summary": "Most modern software attacks are rooted in memory corruption vulnerabilities, which are capable of altering security-sensitive data (e.g., function pointers) to unintended values. This paper introduces a new security property, the Value Invariant Property (VIP), and HyperSpace, our prototype that enforces VIP on security-sensitive data. HyperSpace safeguards the integrity of \"data values\" instead of enforcing control/data flow, allowing for low runtime overhead, yet defeating critical attacks effectively. We implement four representative security policies including Control Flow Integrity (VIP-CFI), Code Pointer Integrity (VIP-CPI), Virtual function Table protection (VIP-VTPtr), and heap metadata protection based on HyperSpace. We evaluate HyperSpace with SPEC CPU2006 benchmarks and real-world applications (NGINX and PostgreSQL) and test how HyperSpace defeats memory corruption-based attacks, including three real-world exploits and six attacks that bypass existing defenses (COOP, heap exploits, etc.). Our experimental evaluation shows that HyperSpace successfully stops all these attacks with low runtime overhead: 0.88% and 6.18% average performance overhead for VIP-CFI and VIP-CPI, respectively, and overall approximately 13.18% memory overhead with VIP-CPI in SPEC CPU2006.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485376"}, {"primary_key": "2100800", "vector": [], "sparse_vector": [], "title": "T-Reqs: HTTP Request Smuggling with Differential Fuzzing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "HTTP Request Smuggling (HRS) is an attack that exploits the HTTP processing discrepancies between two servers deployed in a proxy-origin configuration, allowing attackers to smuggle hidden requests through the proxy. While this idea is not new, HRS is soaring in popularity due to recently revealed novel exploitation techniques and real-life abuse scenarios. In this work, we step back from the highly-specific exploits hogging the spotlight, and present the first work that systematically explores HRS within a scientific framework. We design an experiment infrastructure powered by a novel grammar-based differential fuzzer, test 10 popular server/proxy/CDN technologies in combinations, identify pairs that result in processing discrepancies, and discover exploits that lead to HRS. Our experiment reveals previously unknown ways to manipulate HTTP requests for exploitation, and for the first time documents the server pairs prone to HRS.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485384"}, {"primary_key": "2100802", "vector": [], "sparse_vector": [], "title": "Subpopulation Data Poisoning Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Machine learning systems are deployed in critical settings, but they might fail in unexpected ways, impacting the accuracy of their predictions. Poisoning attacks against machine learning induce adversarial modification of data used by a machine learning algorithm to selectively change its output when it is deployed. In this work, we introduce a novel data poisoning attack called a subpopulation attack, which is particularly relevant when datasets are large and diverse. We design a modular framework for subpopulation attacks, instantiate it with different building blocks, and show that the attacks are effective for a variety of datasets and machine learning models. We further optimize the attacks in continuous domains using influence functions and gradient optimization methods. Compared to existing backdoor poisoning attacks, subpopulation attacks have the advantage of inducing misclassification in naturally distributed data points at inference time, making the attacks extremely stealthy. We also show that our attack strategy can be used to improve upon existing targeted attacks. We prove that, under some assumptions, subpopulation attacks are impossible to defend against, and empirically demonstrate the limitations of existing defenses against our attacks, highlighting the difficulty of protecting machine learning against this threat.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485368"}, {"primary_key": "2100803", "vector": [], "sparse_vector": [], "title": "Generalized Proof of Liabilities.", "authors": ["Yan Ji", "<PERSON><PERSON>"], "summary": "Proof of liabilities (PoL) allows a prover to prove his/her liabilities to a group of verifiers. This is a cryptographic primitive once used only for proving financial solvency but is also applicable to domains outside finance, including transparent and private donations, new algorithms for disapproval voting and publicly verifiable official reports such as COVID-19 daily cases. These applications share a common nature in incentives: it's not in the prover's interest to increase his/her total liabilities. We generalize PoL for these applications by attempting for the first time to standardize the goals it should achieve from security, privacy and efficiency perspectives. We also propose DAPOL+, a concrete PoL scheme extending the state-of-the-art DAPOL protocol but providing provable security and privacy, with benchmark results demonstrating its practicality. In addition, we explore techniques to provide additional features that might be desired in different applications of PoL and measure the asymptotic probability of failure.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484802"}, {"primary_key": "2100804", "vector": [], "sparse_vector": [], "title": "Who&apos;s In Control? On Security Risks of Disjointed IoT Device Management Channels.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang", "Deqing Zou", "<PERSON>"], "summary": "An IoT device today can be managed through different channels, e.g., by its device manufacturer's app, or third-party channels such as Apple's Home app, or a smart speaker. Supporting each channel is a management framework integrated in the device and provided by different parties. For example, a device that integrates Apple HomeKit framework can be managed by Apple Home app. We call the management framework of this kind, including all its device- and cloud-side components, a device management channel (DMC). 4 third-party DMCs are widely integrated in today's IoT devices along with the device manufacturer's own DMC: HomeKit, Zigbee/Z-Wave compatible DMC, and smart-speaker Seamless DMC. Each of these DMCs is a standalone system that has full mandate on the device; however, if their security policies and control are not aligned, consequences can be serious, allowing a malicious user to utilize one DMC to bypass the security control imposed by the device owner on another DMC. We call such a problem Chaotic Device Management (Codema).", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484592"}, {"primary_key": "2100805", "vector": [], "sparse_vector": [], "title": "Igor: Crash Deduplication Through Root-Cause Clustering.", "authors": ["<PERSON><PERSON><PERSON> Jiang", "<PERSON><PERSON><PERSON>", "<PERSON>", "Chaojing Tang", "<PERSON>", "<PERSON>"], "summary": "Fuzzing has emerged as the most effective bug-finding technique. The output of a fuzzer is a set of proof-of-concept (PoC) test cases for all observed \"unique'' crashes. It costs developers substantial efforts to analyze each crashing test case. This, mostly manual, process has lead to the number of reported crashes out-pacing the number of bug fixes. Automatic crash deduplication techniques, which mostly rely on coverage profiles and stack hashes, are supposed to alleviate these pressures. However, these techniques both inflate actual bug counts and falsely conflate unrelated bugs. This hinders, rather than helps, developers, and calls for more accurate techniques.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485364"}, {"primary_key": "2100806", "vector": [], "sparse_vector": [], "title": "ECMO: Peripheral Transplantation to Rehost Embedded Linux Kernels.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Dynamic analysis based on the full-system emulator QEMU is widely used for various purposes.However, it is challenging to run firmware images of embedded devices in QEMU, especially the process to boot the Linux kernel (we call this process rehosting the Linux kernel in this paper). That's because embedded devices usually use different system-on-chips (SoCs) from multiple vendors and only a limited number of SoCs are currently supported in QEMU.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484753"}, {"primary_key": "2100807", "vector": [], "sparse_vector": [], "title": "Periscope: A Keystroke Inference Attack Using Human Coupled Electromagnetic Emanations.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This study presents Periscope, a novel side-channel attack that exploits human-coupled electromagnetic (EM) emanations from touchscreens to infer sensitive inputs on a mobile device. Periscope is motivated by the observation that finger movement over the touchscreen leads to time-varying coupling between these two. Consequently, it impacts the screen's EM emanations that can be picked up by a remote sensory device. We intend to map between EM measurements and finger movements to recover the inputs. As the significant technical contribution of this work, we build an analytic model that outputs finger movement trajectories based on given EM readings. Our approach does not need a large amount of labeled dataset for offline model training, but instead a couple of samples to parameterize the user-specific analytic model. We implement Periscope with simple electronic components and conduct a suite of experiments to validate this attack's impact. Experimental results show that Periscope achieves a recovery rate over 6-digit PINs of 56.2% from a distance of 90 cm. Periscope is robust against environment dynamics and can well adapt to different device models and setting contexts.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484549"}, {"primary_key": "2100808", "vector": [], "sparse_vector": [], "title": "Human and Organizational Factors in Public Key Certificate Authority Failures.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Public Key Infrastructure (PKI) is the foundation of secure and trusted transactions across the Internet. Public key certificates are issued and validated by Certificate Authorities (CAs), which have their trust-of-anchor certificates in Root Program Operators' stores. These CAs provide certificates that attest to the integrity of the ownership of domain names on the web and enable secure communications. Each year hundreds of certificates are by these verified and trusted Certificate Authorities issued in error. In this research, we complied and classified certificate incident reports documented on Bugzilla, a web-based bug tracking system where such instances are reported. We focus on the 210 incident reports from the last year; we compare this pandemic period to trends from previous years. Our data show that the frequency of Certificate Authority non-compliance is a consistence source of vulnerability in the PKI ecosystem. The evaluation of reasons for the misissuance illustrate the role of one-off human failures, systematic interaction flaws leading to repeated incidents, and evidence of perverse incentives leading to misissuance.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485360"}, {"primary_key": "2100812", "vector": [], "sparse_vector": [], "title": "Meteor: Cryptographically Secure Steganography for Realistic Distributions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Aviel D<PERSON> Rubin"], "summary": "Despite a long history of research and wide-spread applications to censorship resistant systems, practical steganographic systems capable of embedding messages into realistic communication distributions, like text, do not exist. We identify two primary impediments to deploying universal steganography: (1) prior work leaves the difficult problem of finding samplers for non-trivial distributions unaddressed, and (2) prior constructions have impractical minimum entropy requirements. We investigate using generative models as steganographic samplers, as they represent the best known technique for approximating human communication. Additionally, we study methods to overcome the entropy requirement, including evaluating existing techniques and designing a new steganographic protocol, called Meteor. The resulting protocols are provably indistinguishable from honest model output and represent an important step towards practical steganographic communication for mundane communication channels. We implement Meteor and evaluate it on multiple computation environments with multiple generative models.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484550"}, {"primary_key": "2100813", "vector": [], "sparse_vector": [], "title": "Revisiting Fuzzy Signatures: Towards a More Risk-Free Cryptographic Authentication System based on Biometrics.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Biometric authentication is one of the promising alternatives to standard password-based authentication offering better usability and security. In this work, we revisit the biometric authentication based on fuzzy signatures introduced by <PERSON><PERSON><PERSON> et al. (ACNS'15, IJIS'19). These are special types of digital signatures where the secret signing key can be a ''fuzzy'' data such as user's biometrics. Compared to other cryptographically secure biometric authentications as those relying on fuzzy extractors, the fuzzy signature-based scheme provides a more attractive security guarantee. However, despite their potential values, fuzzy signatures have not attracted much attention owing to their theory-oriented presentations in all prior works. For instance, the discussion on the practical feasibility of the assumptions (such as the entropy of user biometrics), which the security of fuzzy signatures hinges on, is completely missing.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484586"}, {"primary_key": "2100814", "vector": [], "sparse_vector": [], "title": "Mining in Logarithmic Space.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Blockchains maintain two types of data: Application data and consensus data. Towards long-term blockchain scalability, both of these must be pruned. While a large body of literature has explored the pruning of application data (UTXOs, account balances, and contract state), little has been said about the permanent pruning of consensus data (block headers). We present a protocol which allows pruning the blockchain by garbage collecting old blocks as they become unnecessary. These blocks can simply be discarded and are no longer stored by any miner. We show that all miners can be light miners with no harm to security. Our protocol is based on the notion of superblocks, blocks that have achieved an unusually high difficulty. We leverage them to represent underlying proof-of-work without ever illustrating it, storing it, or transmitting it. After our pruning is applied, the storage and communication requirements for consensus data are reduced exponentially. We develop new probabilistic mathematical methods to analyze our protocol in the random oracle model. We prove our protocol is both secure and succinct under an uninterrupted honest majority assumption for 1/3 adversaries. Our protocol is the first to achieve always secure, always succinct, and online Non-Interactive Proofs of Proof-of-Work, all necessary components for a logarithmic space mining scheme. Our work has applications beyond mining and also constitutes an improvement in state-of-the-art superlight clients and cross-chain bridges.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484784"}, {"primary_key": "2100815", "vector": [], "sparse_vector": [], "title": "Are we done yet? Our Journey to Fight against Memory-safety Bugs.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Memory-safety issues have been a long-standing concern of the security practitioners. According to Microsoft and Google, memory-safety bugs still represent 70% of the exploited vulnerabilities in complex, real-world programs like OSes and Web browsers. However, it doesn't mean that academics and practitioners haven't tried hard to alleviate the problem. Advances in automatic techniques like fuzzing and sanitizers revolutionize the way we tame the memory safety bugs, but the increasing volume of new software simply outpaces the adoption rate of these promising new techniques, setting the legacy programs aside.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3482788"}, {"primary_key": "2100816", "vector": [], "sparse_vector": [], "title": "MANIAC: A Man-Machine Collaborative System for Classifying Malware Author Groups.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "In this demo, we show MANIAC, a MAN-machIne collaborative system for malware Author Classification. It is developed to fight a number of author groups who have been generating lots of new malwares by sharing source code within a group and exploiting evasive schemes such as polymorphism and metamorphism. Notably, MANIAC allows users to intervene in the model's classification of malware authors with high uncertainty. It also provides effective interfaces and visualizations with users to achieve maximum classification accuracy with minimum human labor.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485355"}, {"primary_key": "2100817", "vector": [], "sparse_vector": [], "title": "XSinator.com: From a Formal Model to the Automatic Evaluation of Cross-Site Leaks in Web Browsers.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Cross-Site Leaks (XS-Leaks) describe a client-side bug that allows an attacker to collect side-channel information from a cross-origin HTTP resource. They are a significant threat to Internet privacy since simply visiting a web page may reveal if the victim is a drug addict or leak a sexual orientation. Numerous different attack vectors, as well as mitigation strategies, have been proposed, but a clear and systematic understanding of XS-Leak' root causes is still missing. Recently, <PERSON><PERSON><PERSON><PERSON> et al. gave a first overview of XS-Leak at NDSS 2020. We build on their work by presenting the first formal model for XS-Leaks. Our comprehensive analysis of known XS-Leaks reveals that all of them fit into this new model. With the help of this formal approach, we (1) systematically searched for new XS-Leak attack classes, (2) implemented XSinator.com, a tool to automatically evaluate if a given web browser is vulnerable to XS-Leaks, and (3) systematically evaluated mitigations for XS-Leaks. We found 14 new attack classes, evaluated the resilience of 56 different browser/OS combinations against a total of 34 XS-Leaks, and propose a completely novel methodology to mitigate XS-Leaks.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484739"}, {"primary_key": "2100818", "vector": [], "sparse_vector": [], "title": "Private Hierarchical Clustering in Federated Networks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Analyzing structural properties of social networks, such as identifying their clusters or finding their central nodes, has many applications. However, these applications are not supported by federated social networks that allow users to store their social contacts locally on their end devices. In the federated regime, users want access to personalized services while also keeping their social contacts private. In this paper, we take a step towards enabling analytics on federated networks with differential privacy guarantees about protecting the user's social contacts. Specifically, we present the first work to compute hierarchical cluster trees using local differential privacy. Our algorithms for computing them are novel and come with theoretical bounds on the quality of the trees learned. Empirically, our differentially private algorithms learn trees that are of comparable quality (with at most about 10% utility loss) to the trees obtained from the non-private algorithms, while having reasonable privacy (0.5 łeq ε łeq 2). Private hierarchical cluster trees enable new application setups where a service provider can query the community structure around a target user without having their social contacts. We show the utility of such queries by redesigning two state-of-the-art social recommendation algorithms for the federated social network setup. Our recommendation algorithms significantly outperform the baselines that do not use social contacts.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484822"}, {"primary_key": "2100820", "vector": [], "sparse_vector": [], "title": "Catching Transparent Phish: Analyzing and Detecting MITM Phishing Toolkits.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ov", "<PERSON>"], "summary": "For over a decade, phishing toolkits have been helping attackers automate and streamline their phishing campaigns. Man-in-the- Middle (MITM) phishing toolkits are the latest evolution in this space, where toolkits act as malicious reverse proxy servers of online services, mirroring live content to users while extracting cre- dentials and session cookies in transit. These tools further reduce the work required by attackers, automate the harvesting of 2FA- authenticated sessions, and substantially increase the believability of phishing web pages.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484765"}, {"primary_key": "2100821", "vector": [], "sparse_vector": [], "title": "CyberBunker 2.0 - A Domain and Traffic Perspective on a Bulletproof Hoster.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "In September 2019, 600 armed German cops seized the physical premise of a Bulletproof Hoster (BPH) referred to as CyberBunker 2.0. The hoster resided in a decommissioned NATO bunker and advertised to host everything but child porn and anything related to terrorism while keeping servers online no matter what. While the anatomy, economics and interconnection-level characteristics of BPHs are studied, their traffic characteristics are unknown. In this poster, we present the first analysis of domains, web pages, and traffic captured at a major tier-1 ISP and a large IXP at the time when the CyberBunker was in operation. Our study sheds light on traffic characteristics of a BPH in operation. We show that a traditional BGP-based BPH identification approach cannot detect the CyberBunker, but find characteristics from a domain and traffic perspective that can add to future identification approaches.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485352"}, {"primary_key": "2100829", "vector": [], "sparse_vector": [], "title": "Usable User Authentication on a Smartwatch using Vibration.", "authors": ["<PERSON><PERSON><PERSON> Lee", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Smartwatches have come into wide use in recent years, and a number of smartwatch applications that improve convenience and user health are being developed and introduced constantly. Moreover, the latest smartwatches are now designed to operate without their paired smartphones, and as such, it is necessary for smartwatches to independently authenticate users. In these current devices, personal identification numbers (PIN) or patterns are entered to authenticate users, but these methods require inconvenient interaction for the user and are not highly secure. Particularly relevant to smartwatch technology, even user authentication based on biometric information needs either special sensors capable of measuring biometric information or user interaction. In this paper, we propose a usable method for user authentication on smartwatches without additional devices. Based on the fact that vibration is absorbed, reflected, and propagated differently according to the physical structure of each human body, our method is designed as a challenge-response scheme, in which the challenge is a random sequence of multiple vibration types that are already built into current smartwatches. The responses to vibrations are measured by the default gyroscope and accelerometer sensors in smartwatches. Moreover, our method is the first working model for commercial smartwatch models with low specifications when vibrating and measuring responses. We evaluated our method using a commercial smartwatch, and the results show that our method is able to authenticate a user with an equal error rate (EER) of 1.37%.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484553"}, {"primary_key": "2100830", "vector": [], "sparse_vector": [], "title": "How Does Blockchain Security Dictate Blockchain Implementation?", "authors": ["<PERSON>", "<PERSON>"], "summary": "Blockchain protocols come with a variety of security guarantees. For example, BFT-inspired protocols such as Algorand tend to be secure in the partially synchronous setting, while longest chain protocols like Bitcoin will normally require stronger synchronicity to be secure. Another fundamental distinction, directly relevant to scalability solutions such as sharding, is whether or not a single untrusted user is able to point to certificates, which provide incontrovertible proof of block confirmation. Algorand produces such certificates, while Bitcoin does not. Are these properties accidental? Or are they inherent consequences of the paradigm of protocol design? Our aim in this paper is to understand what, fundamentally, governs the nature of security for permissionless blockchain protocols. Using the framework developed in [12], we prove general results showing that these questions relate directly to properties of the user selection process, i.e. the method (such as proof-of-work or proof-of-stake) which is used to select users with the task of updating state. Our results suffice to establish, for example, that the production of certificates is impossible for proof-of-work protocols, but is automatic for standard forms of proof-of-stake protocols. As a byproduct of our work, we also define a number of security notions and identify the equivalences and inequivalences among them.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484752"}, {"primary_key": "2100831", "vector": [], "sparse_vector": [], "title": "Robust Detection of Machine-induced Audio Attacks in Intelligent Audio Systems with Microphone Array.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "With the popularity of intelligent audio systems in recent years, their vulnerabilities have become an increasing public concern. Existing studies have designed a set of machine-induced audio attacks, such as replay attacks, synthesis attacks, hidden voice commands, inaudible attacks, and audio adversarial examples, which could expose users to serious security and privacy threats. To defend against these attacks, existing efforts have been treating them individually. While they have yielded reasonably good performance in certain cases, they can hardly be combined into an all-in-one solution to be deployed on the audio systems in practice. Additionally, modern intelligent audio devices, such as Amazon Echo and Apple HomePod, usually come equipped with microphone arrays for far-field voice recognition and noise reduction. Existing defense strategies have been focusing on single- and dual-channel audio, while only few studies have explored using multi-channel microphone array for defending specific types of audio attack. Motivated by the lack of systematic research on defending miscellaneous audio attacks and the potential benefits of multi-channel audio, this paper builds a holistic solution for detecting machine-induced audio attacks leveraging multi-channel microphone arrays on modern intelligent audio systems. Specifically, we utilize magnitude and phase spectrograms of multi-channel audio to extract spatial information and leverage a deep learning model to detect the fundamental difference between human speech and adversarial audio generated by the playback machines. Moreover, we adopt an unsupervised domain adaptation training framework to further improve the model's generalizability in new acoustic environments. Evaluation is conducted under various settings on a public multi-channel replay attack dataset and a self-collected multi-channel audio attack dataset involving 5 types of advanced audio attacks. The results show that our method can achieve an equal error rate (EER) as low as 6.6% in detecting a variety of machine-induced attacks. Even in new acoustic environments, our method can still achieve an EER as low as 8.8%.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484755"}, {"primary_key": "2100832", "vector": [], "sparse_vector": [], "title": "Hidden Backdoors in Human-Centric Language Models.", "authors": ["<PERSON><PERSON><PERSON> Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jialiang Lu"], "summary": "Natural language processing (NLP) systems have been proven to be vulnerable to backdoor attacks, whereby hidden features (backdoors) are trained into a language model and may only be activated by specific inputs (called triggers), to trick the model into producing unexpected behaviors. In this paper, we create covert and natural triggers for textual backdoor attacks, hidden backdoors, where triggers can fool both modern language models and human inspection. We deploy our hidden backdoors through two state-of-the-art trigger embedding methods. The first approach via homograph replacement, embeds the trigger into deep neural networks through the visual spoofing of lookalike characters replacement. The second approach uses subtle differences between text generated by language models and real natural text to produce trigger sentences with correct grammar and high fluency. We demonstrate that the proposed hidden backdoors can be effective across three downstream security-critical NLP tasks, representative of modern human-centric NLP systems, including toxic comment detection, neural machine translation (NMT), and question answering (QA). Our two hidden backdoor attacks can achieve an Attack Success Rate (ASR) of at least 97% with an injection rate of only 3% in toxic comment detection, 95.1% ASR in NMT with less than 0.5% injected data, and finally 91.12% ASR against QA updated with only 27 poisoning data samples on a model previously trained with 92,024 samples (0.029%). We are able to demonstrate the adversary's high success rate of attacks, while maintaining functionality for regular users, with triggers inconspicuous by the human administrators.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484576"}, {"primary_key": "2100833", "vector": [], "sparse_vector": [], "title": "Deterrence of Intelligent DDoS via Multi-Hop Traffic Divergence.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zhizheng Lv", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We devise a simple, provably effective, and readily usable deterrence against intelligent, unknown DDoS threats: Demotivate adversaries to launch attacks via multi-hop traffic divergence. This new strategy is motivated by the fact that existing defenses almost always lag behind numerous emerging DDoS threats and evolving intelligent attack strategies. The root cause is if adversaries are smart and adaptive, no single-hop defenses (including optimal ones) can perfectly differentiate unknown DDoS and legitimate traffic. Instead, we formulate intelligent DDoS as a game between attackers and defenders, and prove how multi-hop traffic divergence helps bypass this dilemma by reversing the asymmetry between attackers and defenders. This insight results in EID, an Economical Intelligent DDoS Demotivation protocol. EID combines local weak (yet divergent) filters to provably null attack gains without knowing exploited vulnerabilities or attack strategies. It incentivizes multi-hop defenders to cooperate with boosted local service availability. EID is resilient to traffic dynamics and manipulations. It is readily deployable with random-drop filters in real networks today. Our experiments over a 49.8 TB dataset from a department at the Tsinghua campus network validate EID's viability against rational and irrational DDoS with negligible costs.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484737"}, {"primary_key": "2100834", "vector": [], "sparse_vector": [], "title": "PalmTree: Learning an Assembly Language Model for Instruction Embedding.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning has demonstrated its strengths in numerous binary analysis tasks, including function boundary detection, binary code search, function prototype inference, value set analysis, etc. When applying deep learning to binary analysis tasks, we need to decide what input should be fed into the neural network model. More specifically, we need to answer how to represent an instruction in a fixed-length vector. The idea of automatically learning instruction representations is intriguing, but the existing schemes fail to capture the unique characteristics of disassembly. These schemes ignore the complex intra-instruction structures and mainly rely on control flow in which the contextual information is noisy and can be influenced by compiler optimizations. In this paper, we propose to pre-train an assembly language model called PalmTree for generating general-purpose instruction embeddings by conducting self-supervised training on large-scale unlabeled binary corpora. PalmTree utilizes three pre-training tasks to capture various characteristics of assembly language. These training tasks overcome the problems in existing schemes, thus can help to generate high-quality representations. We conduct both intrinsic and extrinsic evaluations, and compare PalmTree with other instruction embedding schemes. PalmTree has the best performance for intrinsic metrics, and outperforms the other instruction embedding schemes for all downstream tasks.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484587"}, {"primary_key": "2100835", "vector": [], "sparse_vector": [], "title": "MirChecker: Detecting Bugs in Rust Programs via Static Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "Jin<PERSON> Wang", "Mingshen Sun", "<PERSON>"], "summary": "Safe system programming is often a crucial requirement due to its critical role in system software engineering. Conventional low-level programming languages such as C and assembly are efficient, but their inherent unsafe nature makes it undesirable for security-critical scenarios. Recently, Rust has become a promising alternative for safe system-level programming. While giving programmers fine-grained hardware control, its strong type system enforces many security properties including memory safety. However, Rust's security guarantee is not a silver bullet. Runtime crashes and memory-safety errors still harass Rust developers, causing damaging exploitable vulnerabilities, as reported by numerous studies.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484541"}, {"primary_key": "2100836", "vector": [], "sparse_vector": [], "title": "DETER: Denial of Ethereum Txpool sERvices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "On an Ethereum node, txpool (a.k.a. mempool) is a buffer storing unconfirmed transactions and controls what downstream services can see, such as mining and transaction propagation. This work presents the first security study on Ethereum txpool designs.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485369"}, {"primary_key": "2100837", "vector": [], "sparse_vector": [], "title": "TSS: Transformation-Specific Smoothing for Robustness Certification.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "As machine learning (ML) systems become pervasive, safeguarding their security is critical. However, recently it has been demonstrated that motivated adversaries are able to mislead ML systems by perturbing test data using semantic transformations. While there exists a rich body of research providing provable robustness guarantees for ML models against Lp bounded adversarial perturbations, guarantees against semantic perturbations remain largely underexplored. In this paper, we provide TSS-a unified framework for certifying ML robustness against general adversarial semantic transformations. First, depending on the properties of each transformation, we divide common transformations into two categories, namely resolvable (e.g., Gaussian blur) and differentially resolvable (e.g., rotation) transformations. For the former, we propose transformation-specific randomized smoothing strategies and obtain strong robustness certification. The latter category covers transformations that involve interpolation errors, and we propose a novel approach based on stratified sampling to certify the robustness. Our framework TSS leverages these certification strategies and combines with consistency-enhanced training to provide rigorous certification of robustness. We conduct extensive experiments on over ten types of challenging semantic transformations and show that TSS significantly outperforms the state of the art. Moreover, to the best of our knowledge, TSS is the first approach that achieves nontrivial certified robustness on the large-scale ImageNet dataset. For instance, our framework achieves 30.4% certified robust accuracy against rotation attack (within ±30°) on ImageNet. Moreover, to consider a broader range of transformations, we show TSS is also robust against adaptive attacks and unforeseen image corruptions such as CIFAR-10-C and ImageNet-C.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485258"}, {"primary_key": "2100838", "vector": [], "sparse_vector": [], "title": "Chronos: Timing Interference as a New Attack Vector on Autonomous Cyber-physical Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Timing property plays a vital role in the Cyber-Physical System(CPS) due to its interaction with the physical world. The smooth operation of these robotic systems often relies on an accurate and timely perception and actuation of the physical world. In this poster, we demonstrated a unique new class of attack, Chronos, that exploits timing interference to cause system destabilization in cyber-physical systems. Using a compromised non-privileged non-critical task on the system, we launch timing interference attacks on both drone and autonomous vehicle platforms. Through both open-loop and close-loop testing on the end-to-end stack, we showed that the timing attack could lead to complete loss of control of the autonomous system, crashing them onto the surroundings when there is no software vulnerability. To further understand this novel attack vector, we perform preliminary investigations on the localization component of these two platforms, because they both make use of well-known simultaneous localization and mapping (SLAM) algorithms that depend on timing-sensitive multimodal data from different sensors. Building on the insights from the case study, we present our formulation of the timing attack surface and highlight future directions.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485350"}, {"primary_key": "2100839", "vector": [], "sparse_vector": [], "title": "ZKCPlus: Optimized Fair-exchange Protocol Supporting Practical and Flexible Data Exchange.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Yuguang Hu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Devising a fair-exchange protocol for digital goods has been an appealing line of research in the past decades. The Zero-Knowledge Contingent Payment (ZKCP) protocol first achieves fair exchange in a trustless manner with the aid of the Bitcoin network and zero-knowledge proofs. However, it incurs setup issues and substantial proving overhead, and has difficulties handling complicated validation of large-scale data. In this paper, we propose an improved solution ZKCPlus for practical and flexible fair exchange. ZKCPlus incorporates a new commit-and-prove non-interactive zero-knowledge (CP-NIZK) argument of knowledge under standard discrete logarithmic assumption, which is prover-efficient for data-parallel computations. With this argument we avoid the setup issues of ZKCP and reduce seller's proving overhead, more importantly enable the protocol to handle complicated data validations. We have implemented a prototype of ZKCPlus and built several applications atop it. We rework a ZKCP's classic application of trading sudoku solutions, and ZKCPlus achieves 21-67 times improvement in seller efficiency than ZKCP, with only milliseconds of setup time and 1 MB public parameters. In particular, our CP-NIZK argument shows an order of magnitude higher proving efficiency than the zkSNARK adopted by ZKCP. We also built a realistic application of trading trained CNN models. For a 3-layer CNN containing 8,620 parameters, it takes less than 1 second to prove and verify an inference computation, and also about 1 second to deliver the parameters, which is very promising for practical use.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484558"}, {"primary_key": "2100840", "vector": [], "sparse_vector": [], "title": "Membership Leakage in Label-Only Exposures.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Machine learning (ML) has been widely adopted in various privacy-critical applications, e.g., face recognition and medical image analysis. However, recent research has shown that ML models are vulnerable to attacks against their training data. Membership inference is one major attack in this domain: Given a data sample and model, an adversary aims to determine whether the sample is part of the model's training set. Existing membership inference attacks leverage the confidence scores returned by the model as their inputs (score-based attacks). However, these attacks can be easily mitigated if the model only exposes the predicted label, i.e., the final model decision. In this paper, we propose decision-based membership inference attacks and demonstrate that label-only exposures are also vulnerable to membership leakage. In particular, we develop two types of decision-based attacks, namely transfer attack and boundary attack. Empirical evaluation shows that our decision-based attacks can achieve remarkable performance, and even outperform the previous score-based attacks in some cases. We further present new insights on the success of membership inference based on quantitative and qualitative analysis, i.e., member samples of a model are more distant to the model's decision boundary than non-member samples. Finally, we evaluate multiple defense mechanisms against our decision-based attacks and show that our two types of attacks can bypass most of these defenses.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484575"}, {"primary_key": "2100841", "vector": [], "sparse_vector": [], "title": "CrossLine: Breaking &quot;Security-by-Crash&quot; based Memory Isolation in AMD SEV.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "AMD's Secure Encrypted Virtualization (SEV) is an emerging security feature of modern AMD processors that allows virtual machines to run with encrypted memory and perform confidential computing even with an untrusted hypervisor. This paper first demystifies SEV's improper use of address space identifier (ASID) for controlling accesses of a VM to encrypted memory pages, cache lines, and TLB entries. We then present the CROSSLINE attacks, a novel class of attacks against SEV that allow the adversary to launch an attacker VM and change its ASID to that of the victim <PERSON><PERSON> to impersonate the victim. We present two variants of CROSSLINE attacks: CROSSLINE V1 decrypts victim's page tables or any memory blocks conforming to the format of a page table entry; CROSSLINE V2 constructs encryption and decryption oracles by executing instructions of the victim VM. We discuss the applicability of CROSSLINE attacks on AMD's SEV, SEV-ES, and SEV-SNP processors.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485253"}, {"primary_key": "2100844", "vector": [], "sparse_vector": [], "title": "EncoderMI: Membership Inference against Pre-trained Encoders in Contrastive Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Jia", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Given a set of unlabeled images or (image, text) pairs, contrastive learning aims to pre-train an image encoder that can be used as a feature extractor for many downstream tasks. In this work, we propose EncoderMI, the first membership inference method against image encoders pre-trained by contrastive learning. In particular, given an input and a black-box access to an image encoder, EncoderMI aims to infer whether the input is in the training dataset of the image encoder. EncoderMI can be used 1) by a data owner to audit whether its (public) data was used to pre-train an image encoder without its authorization or 2) by an attacker to compromise privacy of the training data when it is private/sensitive. Our EncoderMI exploits the overfitting of the image encoder towards its training data. In particular, an overfitted image encoder is more likely to output more (or less) similar feature vectors for two augmented versions of an input in (or not in) its training dataset. We evaluate EncoderMI on image encoders pre-trained on multiple datasets by ourselves as well as the Contrastive Language-Image Pre-training (CLIP) image encoder, which is pre-trained on 400 million (image, text) pairs collected from the Internet and released by OpenAI. Our results show that EncoderMI can achieve high accuracy, precision, and recall. We also explore a countermeasure against EncoderMI via preventing overfitting through early stopping. Our results show that it achieves trade-offs between accuracy of EncoderMI and utility of the image encoder, i.e., it can reduce the accuracy of EncoderMI, but it also incurs classification accuracy loss of the downstream classifiers built based on the image encoder.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484749"}, {"primary_key": "2100845", "vector": [], "sparse_vector": [], "title": "Detecting Missed Security Operations Through Differential Checking of Object-based Similar Paths.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shouling Ji", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Missing a security operation such as a bound check has been a major cause of security-critical bugs. Automatically checking whether the code misses a security operation in large programs is challenging since it has to understand whether the security operation is indeed necessary in the context. Recent methods typically employ cross-checking to identify deviations as security bugs, which collects functionally similar program slices and infers missed security operations through majority-voting. An inherent limitation of such approaches is that they heavily rely on a substantial number of similar code pieces to enable cross-checking. In practice, many code pieces are unique, and thus we may be unable to find adequate similar code snippets to utilize cross-checking.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485373"}, {"primary_key": "2100846", "vector": [], "sparse_vector": [], "title": "zkCNN: Zero Knowledge Proofs for Convolutional Neural Network Predictions and Accuracy.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning techniques with neural networks are developing prominently in recent years and have been deployed in numerous applications. Despite their great success, in many scenarios it is important for the users to validate that the inferences are truly computed by legitimate neural networks with high accuracy, which is referred to as the integrity of machine learning predictions. To address this issue, in this paper, we propose zkCNN, a zero knowledge proof scheme for convolutional neural networks (CNN). The scheme allows the owner of the CNN model to prove to others that the prediction of a data sample is indeed calculated by the model, without leaking any information about the model itself. Our scheme can also be generalized to prove the accuracy of a secret CNN model on a public dataset.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485379"}, {"primary_key": "2100848", "vector": [], "sparse_vector": [], "title": "Honest-but-Curious Nets: Sensitive Attributes of Private Inputs Can Be Secretly Coded into the Classifiers&apos; Outputs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "It is known that deep neural networks, trained for the classification of non-sensitive target attributes, can reveal sensitive attributes of their input data through internal representations extracted by the classifier. We take a step forward and show that deep classifiers can be trained to secretly encode a sensitive attribute of their input data into the classifier's outputs for the target attribute, at inference time. Our proposed attack works even if users have a full white-box view of the classifier, can keep all internal representations hidden, and only release the classifier's estimations for the target attribute. We introduce an information-theoretical formulation for such attacks and present efficient empirical implementations for training honest-but-curious (HBC) classifiers: classifiers that can be accurate in predicting their target attribute, but can also exploit their outputs to secretly encode a sensitive attribute. Our work highlights a vulnerability that can be exploited by malicious machine learning service providers to attack their user's privacy in several seemingly safe scenarios; such as encrypted inferences, computations at the edge, or private knowledge distillation. Experimental results on several attributes in two face-image datasets show that a semi-trusted server can train classifiers that are not only perfectly honest but also accurately curious. We conclude by showing the difficulties in distinguishing between standard and HBC classifiers, discussing challenges in defending against this vulnerability of deep classifiers, and enumerating related open directions for future studies.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484533"}, {"primary_key": "2100849", "vector": [], "sparse_vector": [], "title": "DNS Cache Poisoning Attack: Resurrections with Side Channels.", "authors": ["<PERSON><PERSON>", "Xin&apos;an Zhou", "<PERSON><PERSON><PERSON>"], "summary": "DNS is one of the fundamental and ancient protocols on the Internet that supports many network applications and services. Unfortunately, DNS was designed without security in mind and is subject to a variety of serious attacks, one of which is the well-known DNS cache poisoning attack. Over the decades of evolution, it has proven extraordinarily challenging to retrofit strong security features into it. To date, only weaker versions of defenses based on the principle of randomization have been widely deployed, e.g., the randomization of UDP ephemeral port number, making it hard for an off-path attacker to guess the secret. However, as it has been shown recently, such randomness is subject to clever network side channel attacks, which can effectively derandomize the ephemeral port number.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3486219"}, {"primary_key": "2100850", "vector": [], "sparse_vector": [], "title": "Reconstructing with Less: Leakage Abuse Attacks in Two Dimensions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Access and search pattern leakage from range queries are detrimental to the security of encrypted databases, as evidenced by a large body of work on attacks that reconstruct one-dimensional databases. Recently, the first attack from 2D range queries showed that higher-dimensional databases are also in danger (<PERSON><PERSON><PERSON> et al. CCS 2020). Their attack requires the access and search pattern of all possible queries. We present an order reconstruction attack that only depends on access pattern leakage, and empirically show that the order allows the attacker to infer the geometry of the underlying data. Notably, this attack also achieves full database reconstruction when the 1D horizontal and vertical projections of the points are dense. We also give an approximate database reconstruction attack that is distribution-agnostic and works with any sample of queries, given the search pattern and access pattern leakage of those queries, and the order of the database records. Finally, we show how to improve the reconstruction given knowledge of auxiliary information (e.g., the centroid of a related dataset). We support our results with formal analysis and experiments on real-world databases with queries drawn from various distributions.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484552"}, {"primary_key": "2100851", "vector": [], "sparse_vector": [], "title": "A PKI-based Framework for Establishing Efficient MPC Channels.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "The Transport Layer Security (TLS) protocol is a fundamental building block for ensuring security on Internet. It provides an easy to use framework for the purposes of establishing an authenticated and secure channel between two parties that have never physically met. Nevertheless, TLS only provides a simple cryptographic functionality compared to more advanced protocols such as protocols for secure multiparty computation (MPC).", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484806"}, {"primary_key": "2100853", "vector": [], "sparse_vector": [], "title": "Search-Based Local Black-Box Deobfuscation: Understand, Improve and Mi<PERSON>gate (Poster).", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Lima"], "summary": "This presentation is based on the paper \"Search-based Local Blackbox Deobfuscation: Understand Improve and Mitigate'' from the same authors, which has been accepted for publication at ACM CCS 2021.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485337"}, {"primary_key": "2100854", "vector": [], "sparse_vector": [], "title": "Search-Based Local Black-Box Deobfuscation: Understand, Improve and Mitigate.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> Lima"], "summary": "Code obfuscation aims at protecting Intellectual Property and other secrets embedded into software from being retrieved. Recent works leverage advances in artificial intelligence (AI) with the hope of getting blackbox deobfuscators completely immune to standard (whitebox) protection mechanisms. While promising, this new field of AI-based, and more specifically search-based blackbox deobfuscation, is still in its infancy. In this article we deepen the state of search-based blackbox deobfuscation in three key directions: understand the current state-of-the-art, improve over it and design dedicated protection mechanisms. In particular, we define a novel generic framework for search-based blackbox deobfuscation encompassing prior work and highlighting key components; we are the first to point out that the search space underlying code deobfuscation is too unstable for simulation-based methods (e.g., Monte Carlo Tree Search used in prior work) and advocate the use of robust methods such as S-metaheuristics; we propose the new optimized search-based blackbox deobfuscator Xyn<PERSON> which significantly outperforms prior work in terms of success rate (especially with small time budget) while being completely immune to the most recent anti-analysis code obfuscation methods; and finally we propose two novel protections against search-based blackbox deobfuscation, allowing to counter Xyntia powerful attacks.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485250"}, {"primary_key": "2100856", "vector": [], "sparse_vector": [], "title": "POSTER: OS Independent Fuzz Testing of I/O Boundary.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Device drivers tend to be vulnerable to errant/malicious devices because many of them assume that devices always operate correctly. If a device driver is compromised either deliberately or accidentally, this can lead to system failure or give adversaries entire system access. Therefore, testing whether device drivers can handle compromised I/O correctly is important. There are several studies on testing device drivers against I/O attacks or device failures. Previous studies, however, either require source code for testing, lack test efficiency, only support a specific OS, or only target MMIO accesses. In this paper, we present a novel testing framework of device drivers' I/O boundaries. By combining a hypervisor-based fault injection mechanism and coverage-guided fuzzing scheme, our testing framework is not only OS-independent but also efficient and can test closed-source drivers. To get the information needed to test without OS cooperation, we use IOMMU to detect DMA regions and a hardware tracing mechanism to get coverage. We describe the detailed design and the current status.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485359"}, {"primary_key": "2100857", "vector": [], "sparse_vector": [], "title": "POSTER: How Dangerous is My Click? Boosting Website Fingerprinting By Considering Sequences of Webpages.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Website fingerprinting (WFP) is a special case of traffic analysis, where a passive attacker infers information about the content of encrypted and anonymized connections by observing patterns of data flows. Although modern WFP attacks pose a serious threat to online privacy of users, including Tor users, they usually aim to detect single pages only. By ignoring the browsing behavior of users, the attacker excludes valuable information: users visit multiple pages of a single website consecutively, e.g., by following links. In this paper, we propose two novel methods that can take advantage of the consecutive visits of multiple pages to detect websites. We show that two up to three clicks within a site allow attackers to boost the accuracy by more than 20% and to dramatically increase the threat to users' privacy. We argue that WFP defenses have to consider this new dimension of the attack surface.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485347"}, {"primary_key": "2100858", "vector": [], "sparse_vector": [], "title": "Multi-Threshold Byzantine Fault Tolerance.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Classic Byzantine fault tolerant (BFT) protocols are designed for a specific timing model, most often one of the following: synchronous, asynchronous or partially synchronous. It is well known that the timing model and fault tolerance threshold present inherent trade-offs. Synchronous protocols tolerate up to n/2 Byzantine faults, while asynchronous or partially synchronous protocols tolerate only up to n/3 Byzantine faults. In this work, we generalize the fault thresholds of BFT and introduce a new problem called multi-threshold BFT. Multi-threshold BFT has four separate fault thresholds for safety and liveness under synchrony and asynchrony (or partial-synchrony), respectively. Decomposing the fault thresholds in this way allows us to design protocols that provide meaningful fault tolerance under both synchrony and asynchrony (or partial synchrony). We establish tight fault thresholds bounds for multi-threshold BFT and present protocols achieving them. As an example, we show a BFT state machine replication (SMR) protocol that tolerates up to 2n/3 faults for safety under synchrony while tolerating up to n/3 faults for other scenarios (liveness under synchrony as well as safety and liveness under partial synchrony). This is strictly stronger than classic partially synchronous SMR protocols. We also present a general framework to transform known partially synchronous or asynchronous BFT SMR protocols to additionally enjoy the optimal 2n/3 fault tolerance for safety under synchrony.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484554"}, {"primary_key": "2100859", "vector": [], "sparse_vector": [], "title": "A Hard Label Black-box Adversarial Attack Against Graph Neural Networks.", "authors": ["Jiaming Mu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Graph Neural Networks (GNNs) have achieved state-of-the-art performance in various graph structure related tasks such as node classification and graph classification. However, GNNs are vulnerable to adversarial attacks. Existing works mainly focus on attacking GNNs for node classification; nevertheless, the attacks against GNNs for graph classification have not been well explored.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484796"}, {"primary_key": "2100860", "vector": [], "sparse_vector": [], "title": "OnionPIR: Response Efficient Single-Server PIR.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "This paper presents OnionPIR and stateful OnionPIR, two single-server PIR schemes that significantly improve the response size and computation cost over state-of-the-art schemes. OnionPIR scheme utilizes recent advances in somewhat homomorphic encryption (SHE) and carefully composes two lattice-based SHE schemes and homomorphic operations to control the noise growth and response size. Stateful OnionPIR uses a technique based on the homomorphic evaluation of copy networks. OnionPIR achieves a response overhead of just 4.2x over the insecure baseline, in contrast to the 100x response overhead of state-of-the-art schemes. Our stateful OnionPIR scheme improves upon the recent stateful PIR framework of <PERSON> et al. and drastically reduces its response overhead by avoiding downloading the entire database in the offline stage. Compared to stateless OnionPIR, Stateful OnionPIR reduces the computation cost by 1.8~x for different database sizes.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485381"}, {"primary_key": "2100861", "vector": [], "sparse_vector": [], "title": "Enabling Visual Analytics via Alert-driven Attack Graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Attack graphs (AG) are a popular area of research that display all the paths an attacker can exploit to penetrate a network. Existing techniques for AG generation rely heavily on expert input regarding vulnerabilities and network topology. In this work, we advocate the use of AGs that are built directly using the actions observed through intrusion alerts, without prior expert input. We have developed an unsupervised visual analytics system, called SAGE, to learn alert-driven attack graphs. We show how these AGs (i) enable forensic analysis of prior attacks, and (ii) enable proactive defense by providing relevant threat intelligence regarding attacker strategies. We believe that alert-driven AGs can play a key role in AI-enabled cyber threat intelligence as they open up new avenues for attacker strategy analysis whilst reducing analyst workload.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485361"}, {"primary_key": "2100862", "vector": [], "sparse_vector": [], "title": "Optimized Predictive Control for AGC Cyber Resiliency.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Automatic Generation Control (AGC) is used in smart grid systems to maintain the grid's frequency to a nominal value. Cyber-attacks such as time delay and false data injection on the tie-line power flow, frequency measurements, and Area Control Error (ACE) control signals can cause frequency excursion that can trigger load shedding, generators' damage, and blackouts. Therefore, resilience and detection of attacks are of paramount importance in terms of the reliable operation of the grid. In contrast with the previous works that overlook ACE resiliency, this paper proposes an approach for cyber-attack detection and resiliency in the overall AGC process. We propose a state estimation algorithm approach for the AGC system by utilizing prior information based on Gaussian process regression, a non-parametric, Bayesian approach to regression. We evaluate our approach using the PowerWorld simulator based on the three-area New England IEEE 39-bus model. Moreover, we utilize the modified version of the New England ISO load data for the three-area power system to create a more realistic dataset. Our results clearly show that our resilient control system approach can mitigate the system using predictive control and detect the attack with a 100 percent detection rate in a shorter period using prior auxiliary information.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485358"}, {"primary_key": "2100863", "vector": [], "sparse_vector": [], "title": "Same Coverage, Less Bloat: Accelerating Binary-only Fuzzing with Coverage-preserving Coverage-guided Tracing.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Coverage-guided fuzzing's aggressive, high-volume testing has helped reveal tens of thousands of software security flaws. While executing billions of test cases mandates fast code coverage tracing, the nature of binary-only targets leads to reduced tracing performance. A recent advancement in binary fuzzing performance is Coverage-guided Tracing (CGT), which brings orders-of-magnitude gains in throughput by restricting the expense of coverage tracing to only when new coverage is guaranteed. Unfortunately, CGT suits only a basic block coverage granularity -- yet most fuzzers require finer-grain coverage metrics: edge coverage and hit counts. It is this limitation which prohibits nearly all of today's state-of-the-art fuzzers from attaining the performance benefits of CGT. This paper tackles the challenges of adapting CGT to fuzzing's most ubiquitous coverage metrics. We introduce and implement a suite of enhancements that expand CGT's introspection to fuzzing's most common code coverage metrics, while maintaining its orders-of-magnitude speedup over conventional always-on coverage tracing. We evaluate their trade-offs with respect to fuzzing performance and effectiveness across 12 diverse real-world binaries (8 open- and 4 closed-source). On average, our coverage-preserving CGT attains near-identical speed to the present block-coverage-only CGT, UnTracer; and outperforms leading binary- and source-level coverage tracers QEMU, Dyninst, RetroWrite, and AFL-Clang by 2-24x, finding more bugs in less time.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484787"}, {"primary_key": "2100864", "vector": [], "sparse_vector": [], "title": "Glowworm Attack: Optical TEMPEST Sound Recovery via a Device&apos;s Power Indicator LED.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Two main classes of optical TEMPEST attacks against the confidentiality of information processed/delivered by devices have been demonstrated in the past two decades; the first class includes methods for recovering content from monitors, and the second class includes methods for recovering keystrokes from physical and virtual keyboards. In this paper, we identify a new class of optical TEMPEST attacks: recovering sound by analyzing optical emanations from a device's power indicator LED. We analyze the response of the power indicator LED of various devices to sound and show that there is an optical correlation between the sound that is played by connected speakers and the intensity of their power indicator LED due to the facts that: (1) the power indicator LED of various devices is connected directly to the power line, (2) the intensity of a device's power indicator LED is correlative to the power consumption, and (3) many devices lack a dedicated means of countering this phenomenon. Based on our findings, we present the Glowworm attack, an optical TEMPEST attack that can be used by eavesdroppers to recover sound by analyzing optical measurements obtained via an electro-optical sensor directed at the power indicator LED of various devices (e.g., speakers, USB hub splitters, and microcontrollers). We propose an optical-audio transformation (OAT) to recover sound in which we isolate the speech from optical measurements obtained by directing an electro-optical sensor at a device's power indicator LED. Finally, we test the performance of the Glowworm attack in various experimental setups and show that an eavesdropper can apply the attack to recover speech from speakers' power LED indicator with good intelligibility from a distance of 15 meters and with fair intelligibility from 35 meters.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484775"}, {"primary_key": "2100865", "vector": [], "sparse_vector": [], "title": "POSTER: Recovering Songs from a Hanging Light Bulb.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "In this paper, we introduce a novel side-channel attack for eavesdropping sound using an electro-optical sensor. We show how small vibrations of a hanging bulb (in response to sound hitting its surface), can be exploited by eavesdroppers to recover sound. We evaluate our method's performance in a realistic setup and show that our method can be used by eavesdroppers to recover songs from a target room containing the hanging light bulb.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485336"}, {"primary_key": "2100866", "vector": [], "sparse_vector": [], "title": "Simple, Fast Malicious Multiparty Private Set Intersection.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We address the problem of multiparty private set intersection against a malicious adversary. First, we show that when one can assume no collusion amongst corrupted parties then there exists an extremely efficient protocol given only symmetric-key primitives. Second, we present a protocol secure against an adversary corrupting any strict subset of the parties. Our protocol is based on the recently introduced primitives: oblivious programmable PRF (OPPRF) and oblivious key-value store (OKVS).", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484772"}, {"primary_key": "2100868", "vector": [], "sparse_vector": [], "title": "LEAP: Leakage-Abuse Attack on Efficiently Deployable, Efficiently Searchable Encryption with Partially Known Dataset.", "authors": ["Jianting Ning", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jiaming Yuan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Searchable Encryption (SE) enables private queries on encrypted documents. Most existing SE schemes focus on constructing industrial-ready, practical solutions at the expense of information leakages that are considered acceptable. In particular, ShadowCrypt utilizes a cryptographic approach named ''efficiently deployable, efficiently searchable encryption'' (EDESE) that reveals the encrypted dataset and the query tokens among other information. However, recent attacks showed that such leakages can be exploited to (partially) recover the underlying keywords of query tokens under certain assumptions on the attacker's background knowledge.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484540"}, {"primary_key": "2100870", "vector": [], "sparse_vector": [], "title": "Demo: Detecting Third-Party Library Problems with Combined Program Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Third-party libraries ease the software development process and thus have become an integral part of modern software engineering. Unfortunately, they are not usually vetted by human developers and thus are often responsible for introducing bugs, vulnerabilities, or attacks to programs that will eventually reach end-users. In this demonstration, we present a combined static and dynamic program analysis for inferring and enforcing third-party library permissions in server-side JavaScript. This analysis is centered around a RWX permission system across library boundaries. We demonstrate that our tools can detect zero-day vulnerabilities injected into popular libraries and often missed by state-of-the-art tools such as snyk test and npm audit.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485351"}, {"primary_key": "2100871", "vector": [], "sparse_vector": [], "title": "On the TOCTOU Problem in Remote Attestation.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Much attention has been devoted to verifying software integrity of remote embedded (IoT) devices. Many techniques, with different assumptions and security guarantees, have been proposed under the common umbrella of so-called Remote Attestation (RA). Aside from executable's integrity verification, RA serves as a foundation for many security services, such as proofs of memory erasure, system reset, software update, and verification of runtime properties. Prior RA techniques verify the remote device's binary at the time when RA functionality is executed, thus providing no information about the device's binary before current RA execution or between consecutive RA executions. This implies that presence of transient malware (in the form of modified binary) may be undetected. In other words, if transient malware infects a device (by modifying its binary), performs its nefarious tasks, and erases itself before the next attestation, its temporary presence will not be detected. This important problem, called Time-Of-Check-Time-Of-Use ( TOCTOU ), is well-known in the research literature and remains unaddressed in the context of hybrid RA.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484532"}, {"primary_key": "2100873", "vector": [], "sparse_vector": [], "title": "V-Shuttle: Scalable and Semantics-Aware Hypervisor Virtual Device Fuzzing.", "authors": ["Gaoning Pan", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Yongkan<PERSON>", "Shouling Ji", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "With the wide application and deployment of cloud computing in enterprises, virtualization developers and security researchers are paying more attention to cloud computing security. The core component of cloud computing products is the hypervisor, which is also known as the virtual machine monitor (VMM) that can isolate multiple virtual machines in one host machine. However, compromising the hypervisor can lead to virtual machine escape and the elevation of privilege, allowing attackers to gain the permission of code execution in the host. Therefore, the security analysis and vulnerability detection of the hypervisor are critical for cloud computing enterprises. Importantly, virtual devices expose many interfaces to a guest user for communication, making virtual devices the most vulnerable part of a hypervisor. However, applying fuzzing to the virtual devices of a hypervisor is challenging because the data structures transferred by DMA are constructed in a nested form according to protocol specifications. Failure to understand the protocol of the virtual devices will make the fuzzing process stuck in the initial fuzzing stage, resulting in inefficient fuzzing.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484811"}, {"primary_key": "2100874", "vector": [], "sparse_vector": [], "title": "Unleashing the Tiger: Inference Attacks on Split Learning.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We investigate the security of split learning---a novel collaborative machine learning framework that enables peak performance by requiring minimal resource consumption. In the present paper, we expose vulnerabilities of the protocol and demonstrate its inherent insecurity by introducing general attack strategies targeting the reconstruction of clients' private training sets. More prominently, we show that a malicious server can actively hijack the learning process of the distributed model and bring it into an insecure state that enables inference attacks on clients' data. We implement different adaptations of the attack and test them on various datasets as well as within realistic threat scenarios. We demonstrate that our attack can overcome recently proposed defensive techniques aimed at enhancing the security of the split learning protocol. Finally, we also illustrate the protocol's insecurity against malicious clients by extending previously devised attacks for Federated Learning.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485259"}, {"primary_key": "2100875", "vector": [], "sparse_vector": [], "title": "Exorcising <PERSON><PERSON>res with Secure Compilers.", "authors": ["<PERSON>", "<PERSON>"], "summary": "ttackers can access sensitive information of programs by exploiting the side-effects of speculatively-executed instructions using Spectre attacks. To mitigate these attacks, popular compilers deployed a wide range of countermeasures whose security, however, has not been ascertained: while some are believed to be secure, others are known to be insecure and result in vulnerable programs. This paper develops formal foundations for reasoning about the security of these defenses. For this, it proposes a framework of secure compilation criteria that characterise when compilers produce code resistant against Spectre v1 attacks. With this framework, this paper performs a comprehensive security analysis of countermeasures against Spectre v1 attacks implemented in major compilers, deriving the first security proofs of said countermeasures", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484534"}, {"primary_key": "2100876", "vector": [], "sparse_vector": [], "title": "Secure Complaint-Enabled Source-Tracking for Encrypted Messaging.", "authors": ["<PERSON>", "<PERSON><PERSON> E<PERSON>", "<PERSON>"], "summary": "While the end-to-end encryption properties of popular messaging schemes such as Whatsapp, Messenger, and Signal guarantee privacy for users, these properties also make it very difficult for messaging platforms to enforce any sort of content moderation. This can lead to the unchecked spread of malicious content such as misinformation on such platforms. In 2019, <PERSON><PERSON> et al. developed message traceback, which addresses this issue by allowing a messaging platform to recover the path of a forwarded message after a user reports it for malicious content. This paper presents an alternative to message traceback that offers more privacy to users and requires less platform-side storage. We term this approach source-tracking for encrypted messaging schemes. Source-tracking enables messaging platforms to provide the privacy guarantees expected from standard end-to-end encryption, but also helps hold the sources of malicious messages accountable: if malicious content is reported by a user, the source can be identified. We formalize security goals for source-tracking schemes and design and implement two source-tracking schemes with different security and performance tradeoffs.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484539"}, {"primary_key": "2100878", "vector": [], "sparse_vector": [], "title": "Out of Sight, Out of Mind: Detecting Orphaned Web Pages at Internet-Scale.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Security misconfigurations and neglected updates commonly lead to systems being vulnerable. Especially in the context of websites, we often find pages that were forgotten, that is, they were left online after they served their purpose and never updated thereafter. In this paper, we introduce new methodology to detect such forgotten or orphaned web pages. We combine historic data from the Internet Archive with active measurements to identify pages no longer reachable via a path from the index page, yet stay accessible through their specific URL. We show the efficacy of our approach and the real-world relevance of orphaned web-pages by applying it to a sample of 100,000 domains from the Tranco Top 1M.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485367"}, {"primary_key": "2100879", "vector": [], "sparse_vector": [], "title": "Prime+Scope: Overcoming the Observer Effect for High-Precision Cache Contention Attacks.", "authors": ["Antoon Purnal", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Modern processors expose software to information leakage through shared microarchitectural state. One of the most severe leakage channels is cache contention, exploited by attacks referred to as PRIME+PROBE, which can infer fine-grained memory access patterns while placing only limited assumptions on attacker capabilities.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484816"}, {"primary_key": "2100884", "vector": [], "sparse_vector": [], "title": "Compact and Malicious Private Set Intersection for Small Sets.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We describe a protocol for two-party private set intersection (PSI) based on <PERSON><PERSON><PERSON><PERSON><PERSON> key agreement. The protocol is proven secure against malicious parties, in the ideal permutation + random oracle model.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484778"}, {"primary_key": "2100885", "vector": [], "sparse_vector": [], "title": "12 Angry Developers - A Qualitative Study on Developers&apos; Struggles with CSP.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Web has improved our ways of communicating, collaborating, teaching, and entertaining us and our fellow human beings. However, this cornerstone of our modern society is also one of the main targets of attacks, most prominently Cross-Site Scripting (XSS). A correctly crafted Content Security Policy (CSP) is capable of effectively mitigating the effect of those Cross-Site Scripting attacks. However, research has shown that the vast majority of all policies in the wild are trivially bypassable.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484780"}, {"primary_key": "2100886", "vector": [], "sparse_vector": [], "title": "An Ontology-driven Knowledge Graph for Android Malware.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Youngja <PERSON>", "<PERSON><PERSON>"], "summary": "We present MalONT2.0 -- an ontology for malware threat intelligence [4]. New classes (attack patterns, infrastructural resources to enable attacks, malware analysis to incorporate static analysis, and dynamic analysis of binaries) and relations have been added following a broadened scope of core competency questions. MalONT2.0 allows researchers to extensively capture all requisite classes and relations that gather semantic and syntactic characteristics of an android malware attack. This ontology forms the basis for the malware threat intelligence knowledge graph, MalKG, which we exemplify using three different, non-overlapping demonstrations. Malware features have been extracted from openCTI reports on android threat intelligence shared on the Internet and written in the form of unstructured text. Some of these sources are blogs, threat intelligence reports, tweets, and news articles. The smallest unit of information that captures malware features is written as triples comprising head and tail entities, each connected with a relation. In the poster and demonstration, we discuss MalONT2.0 and MalKG.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485353"}, {"primary_key": "2100887", "vector": [], "sparse_vector": [], "title": "Revisiting <PERSON><PERSON><PERSON> Consensus in Asynchronous Networks: A Comprehensive Analysis of Bitcoin Safety and ChainQuality.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The Bitcoin blockchain safety relies on strong network synchrony. Therefore, violating the blockchain safety requires strong adversaries that control a mining pool with 51% hash rate. In this paper, we show that the network synchrony does not hold in the real world Bitcoin network which can be exploited to lower the cost of various attacks that violate the blockchain safety and chain quality. Towards that, first we construct the Bitcoin ideal functionality to formally specify its ideal execution model in a synchronous network. We then develop a large-scale data collection system through which we connect with more than 36K IP addresses of the Bitcoin nodes and identify 359 mining nodes. We contrast the ideal functionality against the real world measurements to expose the network anomalies that can be exploited to optimize the existing attacks. Particularly, we observe a non-uniform block propagation pattern among the mining nodes showing that the Bitcoin network is asynchronous in practice.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484561"}, {"primary_key": "2100888", "vector": [], "sparse_vector": [], "title": "SyncAttack: Double-spending in Bitcoin Without Mining Power.", "authors": ["<PERSON>", "Song<PERSON> Chen", "<PERSON>"], "summary": "The existing Bitcoin security research has mainly followed the security models in [22, 35], which stipulate that an adversary controls some mining power in order to violate the blockchain consistency property (i.e., through a double-spend attack). These models, however, largely overlooked the impact of the realistic network synchronization, which can be manipulated given the permissionless nature of the network. In this paper, we revisit the security of Bitcoin blockchain by incorporating the network synchronization into the security model and evaluating that in practice. Towards this goal, we propose the ideal functionality for the Bitcoin network synchronization and specify bounds on the network outdegree and the block propagation delay in order to preserve the consistency property. By contrasting the ideal functionality against measurements, we find deteriorating network synchronization reported by Bitnodes and a notable churn rate with 10% of the nodes arriving and departing from the network daily.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484568"}, {"primary_key": "2100889", "vector": [], "sparse_vector": [], "title": "<PERSON>&<PERSON><PERSON><PERSON>;t Forget the Stuffing! Revisiting the Security Impact of Typo-Tolerant Password Authentication.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "To enhance the usability of password authentication, typo-tolerant password authentication schemes permit certain deviations in the user-supplied password, to account for common typographical errors yet still allow the user to successfully log in. In prior work, analysis by <PERSON><PERSON><PERSON> et al. demonstrated that typo-tolerance indeed notably improves password usability, yet (surprisingly) does not appear to significantly degrade authentication security. In practice, major web services such as Facebook have employed typo-tolerant password authentication systems. In this paper, we revisit the security impact of typo-tolerant password authentication. We observe that the existing security analysis of such systems considers only password spraying attacks. However, this threat model is incomplete, as password authentication systems must also contend with credential stuffing and tweaking attacks. Factoring in these missing attack vectors, we empirically re-evaluate the security impact of password typo-tolerance using password leak datasets, discovering a significantly larger degradation in security. To mitigate this issue, we explore machine learning classifiers that predict when a password's security is likely affected by typo-tolerance. Our resulting models offer various suitable operating points on the functionality-security tradeoff spectrum, ultimately allowing for partial deployment of typo-tolerant password authentication, preserving its functionality for many users while reducing the security risks.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484791"}, {"primary_key": "2100890", "vector": [], "sparse_vector": [], "title": "Locally Private Graph Neural Networks.", "authors": ["<PERSON><PERSON>", "<PERSON>-<PERSON>"], "summary": "Graph Neural Networks (GNNs) have demonstrated superior performance in learning node representations for various graph inference tasks. However, learning over graph data can raise privacy concerns when nodes represent people or human-related variables that involve sensitive or personal information. In this paper, we study the problem of node data privacy, where graph nodes (e.g., social network users) have potentially sensitive data that is kept private, but they could be beneficial for a central server for training a GNN over the graph. To address this problem, we propose a privacy-preserving, architecture-agnostic GNN learning framework with formal privacy guarantees based on Local Differential Privacy (LDP). Specifically, we develop a locally private mechanism to perturb and compress node features, which the server can efficiently collect to approximate the GNN's neighborhood aggregation step. Furthermore, to improve the accuracy of the estimation, we prepend to the GNN a denoising layer, called KProp, which is based on the multi-hop aggregation of node features. Finally, we propose a robust algorithm for learning with privatized noisy labels, where we again benefit from KProp's denoising capability to increase the accuracy of label inference for node classification. Extensive experiments conducted over real-world datasets demonstrate that our method can maintain a satisfying level of accuracy with low privacy loss.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484565"}, {"primary_key": "2100891", "vector": [], "sparse_vector": [], "title": "WristPrint: Characterizing User Re-identification Risks from Wrist-worn Accelerometry Data.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>iz <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Public release of wrist-worn motion sensor data is growing. They enable and accelerate research in developing new algorithms to passively track daily activities, resulting in improved health and wellness utilities of smartwatches and activity trackers. But, when combined with sensitive attribute inference attack and linkage attack via re-identification of the same user in multiple datasets, undisclosed sensitive attributes can be revealed to unintended organizations with potentially adverse consequences for unsuspecting data contributing users. To guide both users and data collecting researchers, we characterize the re-identification risks inherent in motion sensor data collected from wrist-worn devices in users' natural environment. For this purpose, we use an open-set formulation, train a deep learning architecture with a new loss function, and apply our model to a new data set consisting of 10 weeks of daily sensor wearing by 353 users. We find that re-identification risk increases with an increase in the activity intensity. On average, such risk is 96% for a user when sharing a full day of sensor data.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484799"}, {"primary_key": "2100893", "vector": [], "sparse_vector": [], "title": "POSTER: An Open-Source Framework for Developing Heterogeneous Distributed Enclave Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present an integrated open-source framework to develop, deploy, and use event-driven distributed enclaved applications across heterogeneous Trusted Execution Environments (TEEs). Our framework strives for strong application authenticity and integrity guarantees, and optionally confidentiality and availability, while minimizing the run-time Trusted Computing Base (TCB). For software developers, our framework provides a high level of abstraction over the platform-specific TEE layer that provides isolation, attestation and secure communication amongst distributed application components, allowing developers to focus of application logic. We provide a notion of event-driven programming to develop distributed enclave applications in Rust and C for heterogeneous TEEs, including Intel SGX, ARM TrustZone and the open-source Sancus. This heterogeneity makes our framework uniquely suitable for a broad range of use cases which combine cloud processing, mobile and edge devices, and lightweight sensing and actuation.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485341"}, {"primary_key": "2100897", "vector": [], "sparse_vector": [], "title": "On the Robustness of Domain Constraints.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Machine learning is vulnerable to adversarial examples--inputs designed to cause models to perform poorly. However, it is unclear if adversarial examples represent realistic inputs in the modeled domains. Diverse domains such as networks and phishing have domain constraints--complex relationships between features that an adversary must satisfy for an attack to be realized (in addition to any adversary-specific goals). In this paper, we explore how domain constraints limit adversarial capabilities and how adversaries can adapt their strategies to create realistic (constraint-compliant) examples. In this, we develop techniques to learn domain constraints from data, and show how the learned constraints can be integrated into the adversarial crafting process. We evaluate the efficacy of our approach in network intrusion and phishing datasets and find: (1) up to 82% of adversarial examples produced by state-of-the-art crafting algorithms violate domain constraints, (2) domain constraints are robust to adversarial examples; enforcing constraints yields an increase in model accuracy by up to 34%. We observe not only that adversaries must alter inputs to satisfy domain constraints, but that these constraints make the generation of valid adversarial examples far more challenging.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484570"}, {"primary_key": "2100898", "vector": [], "sparse_vector": [], "title": "MaMIoT: Manipulation of Energy Market Leveraging High Wattage IoT Botnets.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Alvaro <PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "If a trader could predict price changes in the stock market better than other traders, she would make a fortune. Similarly in the electricity market, a trader that could predict changes in the electricity load, and thus electricity prices, would be able to make large profits. Predicting price changes in the electricity market better than other market participants is hard, but in this paper, we show that attackers can manipulate the electricity prices in small but predictable ways, giving them a competitive advantage in the market.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484581"}, {"primary_key": "2100899", "vector": [], "sparse_vector": [], "title": "Rosita++: Automatic Higher-Order Leakage Elimination from Cryptographic Code.", "authors": ["<PERSON><PERSON>", "Lukas<PERSON>lewski", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Side-channel attacks are a major threat to the security of cryptographic implementations, particularly for small devices that are under the physical control of the adversary. While several strategies for protecting against side-channel attacks exist, these often fail in practice due to unintended interactions between values deep within the CPU. To detect and protect from side-channel attacks, several automated tools have recently been proposed; one of their common limitations is that they only support first-order leakage.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485380"}, {"primary_key": "2100900", "vector": [], "sparse_vector": [], "title": "Backdoor Pre-trained Models Can Transfer to All.", "authors": ["<PERSON><PERSON><PERSON>", "Shouling Ji", "<PERSON><PERSON>", "Jinfeng Li", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Pre-trained general-purpose language models have been a dominating component in enabling real-world natural language processing (NLP) applications. However, a pre-trained model with backdoor can be a severe threat to the applications. Most existing backdoor attacks in NLP are conducted in the fine-tuning phase by introducing malicious triggers in the targeted class, thus relying greatly on the prior knowledge of the fine-tuning task. In this paper, we propose a new approach to map the inputs containing triggers directly to a predefined output representation of the pre-trained NLP models, e.g., a predefined output representation for the classification token in BERT, instead of a target label. It can thus introduce backdoor to a wide range of downstream tasks without any prior knowledge. Additionally, in light of the unique properties of triggers in NLP, we propose two new metrics to measure the performance of backdoor attacks in terms of both effectiveness and stealthiness. Our experiments with various types of triggers show that our method is widely applicable to different fine-tuning tasks (classification and named entity recognition) and to different models (such as BERT, XLNet, BART), which poses a severe threat. Furthermore, by collaborating with the popular online model repository Hugging Face, the threat brought by our method has been confirmed. Finally, we analyze the factors that may affect the attack performance and share insights on the causes of the success of our backdoor attack.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485370"}, {"primary_key": "2100901", "vector": [], "sparse_vector": [], "title": "BFT Protocol Forensics.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Byzantine fault-tolerant (BFT) protocols allow a group of replicas to come to consensus even when some of the replicas are Byzantine faulty. There exist multiple BFT protocols to securely tolerate an optimal number of faults t under different network settings. However, if the number of faults f exceeds t then security could be violated. In this paper we mathematically formalize the study of forensic support of BFT protocols: we aim to identify (with cryptographic integrity) as many of the malicious replicas as possible and in as distributed manner as possible. Our main result is that forensic support of BFT protocols depends heavily on minor implementation details that do not affect the protocol's security or complexity. Focusing on popular BFT protocols (PBFT, HotStuff, Algorand) we exactly characterize their forensic support, showing that there exist minor variants of each protocol for which the forensic supports vary widely. We show strong forensic support capability of LibraBFT, the consensus protocol of Diem cryptocurrency; our lightweight forensic module implemented on a Diem client is open-sourced and is under active consideration for deployment in Diem. Finally, we show that all secure BFT protocols designed for 2t+1 replicas communicating over a synchronous network forensic support is inherently nonexistent; this impossibility result holds for all BFT protocols and even if one has access to the states of all replicas (including Byzantine ones).", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484566"}, {"primary_key": "2100902", "vector": [], "sparse_vector": [], "title": "On-device IoT Certificate Revocation Checking with Small Memory and Low Latency.", "authors": ["Xiaofeng Shi", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Allowing a device to verify the digital certificate of another device is an essential requirement and key building block of many security protocols for emerging and future IoT systems that involve device-to-device communication. However, on-device certificate verification is challenging for current devices, mainly because the certificate revocation (CR) checking step costs too much resource on IoT devices and the synchronization of CR status to devices yields a long latency. This paper presents an on-device CR checking system called TinyCR, which achieves 100% accuracy, memory and computation efficiency, low synchronization latency, and low network bandwidth, while being compatible with the current certificate standard. We design a new compact and dynamic data structure called DASS to store and query global CR status on a device in TinyCR. Our implementation shows that TinyCR only costs each device 1.7 MB of memory to track 100 million IoT certificates with 1% revocation rate. Checking the CR status of one certificate spends less than 1 microsecond on a Raspberry Pi 3. TinyCR can also be updated instantly when there are new certificates added or revoked.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484580"}, {"primary_key": "2100904", "vector": [], "sparse_vector": [], "title": "Util: : Lookup: Exploiting Key Decoding in Cryptographic Libraries.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Implementations of cryptographic libraries have been scrutinized for secret-dependent execution behavior exploitable by microarchitectural side-channel attacks. To prevent unintended leakages, most libraries moved to constant-time implementations of cryptographic primitives. There have also been efforts to certify libraries for use in sensitive areas, like Microsoft CNG and Botan, with specific attention to leakage behavior.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484783"}, {"primary_key": "2100906", "vector": [], "sparse_vector": [], "title": "SugarCoat: Programmatically Generating Privacy-Preserving, Web-Compatible Resource Replacements for Content Blocking.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Content blocking systems today exempt thousands of privacy-harming scripts. They do this because blocking these scripts breaks the Web sites that rely on them. In this paper, we address this privacy/functionality trade-off with SugarCoat, a tool that allows filter list authors to automatically patch JavaScript scripts to restrict their access to sensitive data according to a custom privacy policy. We designed SugarCoat to generate resource replacements compatible with existing content blocking tools, including uBlock Origin and the Brave Browser, and evaluate our implementation by automatically replacing scripts exempted by the 6,000+ exception rules in the popular EasyList, EasyPrivacy, and uBlock Origin filter lists. Crawling a sample of pages from the Alexa 10k, we find that SugarCoat preserves the functionality of existing pages-our replacements result in Web-compatibility properties similar to exempting scripts-while providing privacy properties most similar to blocking those scripts. SugarCoat is intended for real-world practical deployment, to protect Web users from privacy harms current tools are unable to protect against. Our design choices emphasize compatibility with existing tools, policy flexibility, and extensibility. SugarCoat is open source and is being integrated into Brave's content blocking tools: an initial set of SugarCoat-generated resource replacements are already shipping to users in the Brave Browser.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484578"}, {"primary_key": "2100907", "vector": [], "sparse_vector": [], "title": "Towards Transparent and Stealthy Android OS Sandboxing via Customizable Container-Based Virtualization.", "authors": ["<PERSON><PERSON>", "Jiang <PERSON>", "<PERSON>", "<PERSON>", "Xuanchen Pan", "Jianming Fu", "<PERSON><PERSON>"], "summary": "A fast-growing demand from smartphone users is mobile virtualization.This technique supports running separate instances of virtual phone environments on the same device. In this way, users can run multiple copies of the same app simultaneously,and they can also run an untrusted app in an isolated virtual phone without causing damages to other apps. Traditional hypervisor-based virtualization is impractical to resource-constrained mobile devices.Recent app-level virtualization efforts suffer from the weak isolation mechanism. In contrast, container-based virtualization offers an isolated virtual environment with superior performance.However, existing Android containers do not meet the anti-evasion requirement for security applications: their designs are inherently incapable of providing transparency or stealthiness.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484544"}, {"primary_key": "2100908", "vector": [], "sparse_vector": [], "title": "Towards Building a Responsible Data Economy.", "authors": ["<PERSON>"], "summary": "Data is a key driver of modern economy and AI/machine learning, however, a lot of this data is sensitive and handling the sensitive data has caused unprecedented challenges for both individuals and businesses. These challenges will only get more severe as we move forward in the digital era. In this talk, I will talk about technologies needed for responsible data use including secure computing, differential privacy, federated learning, as well as blockchain technologies for data rights, and how to combine privacy computing technologies and blockchain to building a platform for a responsible data economy, to enable more responsible use of data that maximizes social welfare & economic efficiency while protecting users' data rights and enable fair distribution of value created from data. I will also talk about new paradigms that this approach enables including decentralized data science and data DAO. I will also discuss new frameworks on data valuation.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3482789"}, {"primary_key": "2100909", "vector": [], "sparse_vector": [], "title": "Understanding and Detecting Mobile Ad Fraud Through the Lens of Invalid Traffic.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Along with gaining popularity of Real-Time Bidding (RTB) based programmatic advertising, the click farm based invalid traffic, which leverages massive real smartphones to carry out large-scale ad fraud campaigns, is becoming one of the major threats against online advertisement. In this study, we take an initial step towards the detection and large-scale measurement of the click farm based invalid traffic. Our study begins with a measurement on the device's features using a real-world labeled dataset, which reveals a series of features distinguishing the fraudulent devices from the benign ones. Based on these features, we develop EvilHunter, a system for detecting fraudulent devices through ad bid request logs with a focus on clustering fraudulent devices. EvilHunter functions by 1) building a classifier to distinguish fraudulent and benign devices; 2) clustering devices based on app usage patterns; and 3) relabeling devices in clusters through majority voting. Evil<PERSON>unter demonstrates 97% precision and 95% recall on a real-world labeled dataset. By investigating a super click farm, we reveal several cheating strategies that are commonly adopted by fraudulent clusters. We further reduce the overhead of EvilHunter and discuss how to deploy the optimized EvilHunter in a real-world system. We are in partnership with a leading ad verification company to integrate EvilHunter into their industrial platform.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484547"}, {"primary_key": "2100910", "vector": [], "sparse_vector": [], "title": "RealSWATT: Remote Software-based Attestation for Embedded Devices under Realtime Constraints.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Smart factories, critical infrastructures, and medical devices largely rely on embedded systems that need to satisfy realtime constraints to complete crucial tasks. Recent studies and reports have revealed that many of these devices suffer from crucial vulnerabilities that can be exploited with fatal consequences. Despite the security and safety-critical role of these devices, they often do not feature state-of-the-art security mechanisms. Moreover, since realtime systems have strict timing requirements, integrating new security mechanisms is not a viable option as they often influence the device's runtime behavior. One solution is to offload security enhancements to a remote instance, the so-called remote attestation.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484788"}, {"primary_key": "2100911", "vector": [], "sparse_vector": [], "title": "Locating the Security Patches for Disclosed OSS Vulnerabilities with Vulnerability-Commit Correlation Ranking.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Mi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Security patches play an important role in defending against the security threats brought by the increasing OSS vulnerabilities. However, the collection of security patches still remains a challenging problem. Existing works mainly adopt a matching-based design that uses auxiliary information in CVE/NVD to reduce the search scope of patch commits. However, our preliminary study shows that these approaches can only cover a small part of disclosed OSS vulnerabilities (about 12%-53%) even with manual assistance.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484593"}, {"primary_key": "2100912", "vector": [], "sparse_vector": [], "title": "Efficient CCA Timed Commitments in Class Groups.", "authors": ["<PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Timed commitments [<PERSON><PERSON> and <PERSON><PERSON>, CRYPTO 2000] are the timed analogue of standard commitments, where the commitment can be non-interactively opened after a pre-specified amount of time passes. Timed commitments have a large spectrum of applications, such as sealed bid auctions, fair contract signing, fair multi-party computation, and cryptocurrency payments. Unfortunately, all practical constructions rely on a (private-coin) trusted setup and do not scale well with the number of participants. These are two severe limiting factors that have hindered the widespread adoption of this primitive.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484773"}, {"primary_key": "2100913", "vector": [], "sparse_vector": [], "title": "OpenSquare: Decentralized Repeated Modular Squaring Service.", "authors": ["<PERSON> <PERSON><PERSON><PERSON>", "Tiantian <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Repeated Modular Squaring is a versatile computational operation that has led to practical constructions of timed-cryptographic primitives like time-lock puzzles (TLP) and verifiable delay functions (VDF) that have a fast growing list of applications. While there is a huge interest for timed-cryptographic primitives in the blockchains area, we find two real-world concerns that need immediate attention towards their large-scale practical adoption: Firstly, the requirement to constantly perform computations seems unrealistic for most of the users. Secondly, choosing the parameters for the bound (T) seems complicated due to the lack of heuristics and experience. We present OpenSquare, a decentralized repeated modular squaring service, that overcomes the above concerns. OpenSquare lets clients outsource their repeated modular squaring computation via smart contracts to any computationally powerful servers that offer computational services for rewards in an unlinkable manner.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484809"}, {"primary_key": "2100917", "vector": [], "sparse_vector": [], "title": "Towards Automated Computational Auditing of mHealth Security and Privacy Regulations.", "authors": ["<PERSON>", "Z<PERSON><PERSON> Yu", "<PERSON><PERSON>"], "summary": "The growing complexity of our regulatory environment presents us with a hard problem: how can we determine if we are compliant with an ever-growing body of regulations? Computational legal auditing may help, as computational tools are exceptionally good at making sense of large amounts of data. In this research, we explore the possibility of creating a computational auditor that checks if mobile health (mHealth) apps satisfy federal security and privacy regulations. In doing so, we find that while it is challenging to convert open-ended, generally applicable, complicated laws into computational principles, the use of non-legal, authoritative, explanatory documents allows for computational operationalization while preserving the open-ended nature of the law. We test our auditor on 182 FDA/CE-approved mHealth apps. Our research suggests that the use of non-legal, authoritative, guidance documents may help with the creation of computational auditors, a promising tool to help us manage our ever-growing regulatory responsibilities.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485342"}, {"primary_key": "2100919", "vector": [], "sparse_vector": [], "title": "Supply-Chain Vulnerability Elimination via Active Learning and Regeneration.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Software supply-chain attacks target components that are integrated into client applications. Such attacks often target widely-used components, with the attack taking place via operations (for example, file system or network accesses) that do not affect those aspects of component behavior that the client observes. We propose new active library learning and regeneration (ALR) techniques for inferring and regenerating the client-observable behavior of software components. Using increasingly sophisticated rounds of exploration, ALR generates inputs, provides these inputs to the component, and observes the resulting outputs to infer a model of the component's behavior as a program in a domain-specific language. We present Harp, an ALR system for string processing components. We apply Harp to successfully infer and regenerate string-processing components written in JavaScript and C/C++. Our results indicate that, in the majority of cases, <PERSON><PERSON> completes the regeneration in less than a minute, remains fully compatible with the original library, and delivers performance indistinguishable from the original library. We also demonstrate that <PERSON>rp can eliminate vulnerabilities associated with libraries targeted in several highly visible security incidents, specifically event-stream, left-pad, and string-compare.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484736"}, {"primary_key": "2100920", "vector": [], "sparse_vector": [], "title": "Preventing Dynamic Library Compromise on Node.js via RWX-Based Privilege Reduction.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Third-party libraries ease the development of large-scale software systems. However, libraries often execute with significantly more privilege than needed to complete their task. Such additional privilege is sometimes exploited at runtime via inputs passed to a library, even when the library itself is not actively malicious. We present Mir, a system addressing dynamic compromise by introducing a fine-grained read-write-execute (RWX) permission model at the boundaries of libraries: every field of every free variable name in the context of an imported library is governed by a permission set. To help specify the permissions given to existing code, <PERSON>'s automated inference generates default permissions by analyzing how libraries are used by their clients. Applied to over 1,000 JavaScript libraries for Node.js, Mir shows practical security (61/63 attacks mitigated), performance (2.1s for static analysis and +1.93% for dynamic enforcement), and compatibility (99.09%) characteristics---and enables a novel quantification of privilege reduction.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484535"}, {"primary_key": "2100924", "vector": [], "sparse_vector": [], "title": "United We Stand: Collaborative Detection and Mitigation of Amplification DDoS Attacks at Scale.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Amplification Distributed Denial of Service (DDoS) attacks' traffic and harm are at an all-time high. To defend against such attacks, distributed attack mitigation platforms, such as traffic scrubbing centers that operate in peering locations, e.g., Internet Exchange Points (IXP), have been deployed in the Internet over the years. These attack mitigation platforms apply sophisticated techniques to detect attacks and drop attack traffic locally, thus, act as sensors of attacks. However, it has not yet been systematically evaluated and reported to what extent coordination of these views by different platforms can lead to more effective mitigation of amplification DDoS attacks. In this paper, we ask the question: \"Is it possible to mitigate more amplification attacks and drop more attack traffic when distributed attack mitigation platforms collaborate?\"", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485385"}, {"primary_key": "2100925", "vector": [], "sparse_vector": [], "title": "Biometrics-Authenticated Key Exchange for Secure Messaging.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Zengpeng Li", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure messaging heavily relies on a session key negotiated by an Authenticated Key Exchange (AKE) protocol. However, existing AKE protocols only verify the existence of a random secret key (corresponding to a certificated public key) stored in the terminal, rather than a legal user who uses the messaging application. In this paper, we propose a Biometrics-Authenticated Key Exchange (BAKE) framework, in which a secret key is derived from a user's biometric characteristics that are not necessary to be stored. To protect the privacy of users' biometric characteristics and realize one-round key exchange, we present an Asymmetric Fuzzy Encapsulation Mechanism (AFEM) to encapsulate messages with a public key derived from a biometric secret key, such that only a similar secret key can decapsulate them. To manifest the practicality, we present two AFEM constructions for two types of biometric secret keys and instantiate them with irises and fingerprints, respectively. We perform security analysis of BAKE and show its performance through extensive experiments.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484746"}, {"primary_key": "2100926", "vector": [], "sparse_vector": [], "title": "The One-Page Setting: A Higher Standard for Evaluating Website Fingerprinting Defenses.", "authors": ["<PERSON>"], "summary": "To defeat Website Fingerprinting (WF) attacks that threaten privacy on anonymity technologies such as Tor, defenses have been proposed and evaluated under the multi-page setting. The multi-page setting was designed as a difficult setting for the attacker and therefore gives too much of an advantage to the defense, allowing weak defenses to show success. We argue that all WF defenses should instead be evaluated under the one-page setting so that the defender needs to meet a higher standard of success.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484790"}, {"primary_key": "2100927", "vector": [], "sparse_vector": [], "title": "DPGen: Automated Program Synthesis for Differential Privacy.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhang"], "summary": "Differential privacy has become a de facto standard for releasing data in a privacy-preserving way. Creating a differentially private algorithm is a process that often starts with a noise-free (non-private) algorithm. The designer then decides where to add noise, and how much of it to add. This can be a non-trivial process -- if not done carefully, the algorithm might either violate differential privacy or have low utility. In this paper, we present DPGen, a program synthesizer that takes in non-private code (without any noise) and automatically synthesizes its differentially private version (with carefully calibrated noise). Under the hood, DPGen uses novel algorithms to automatically generate a sketch program with candidate locations for noise, and then optimize privacy proof and noise scales simultaneously on the sketch program. Moreover, DPGen can synthesize sophisticated mechanisms that adaptively process queries until a specified privacy budget is exhausted. When evaluated on standard benchmarks, DPGen is able to generate differentially private mechanisms that optimize simple utility functions within 120 seconds. It is also powerful enough to synthesize adaptive privacy mechanisms.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484781"}, {"primary_key": "2100928", "vector": [], "sparse_vector": [], "title": "Spinner: Automated Dynamic Command Subsystem Perturbation.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Injection attacks have been a major threat to web applications. Despite the significant effort in thwarting injection attacks, protection against injection attacks remains challenging due to the sophisticated attacks that exploit the existing protection techniques' design and implementation flaws. In this paper, we develop Spinner, a system that provides general protection against input injection attacks, including OS/shell command, SQL, and XXE injection. Instead of focusing on detecting malicious inputs, <PERSON><PERSON> constantly randomizes underlying subsystems so that injected inputs (e.g., commands or SQL queries) that are not properly randomized will not be executed, hence prevented. We revisit the design and implementation choices of previous randomization-based techniques and develop a more robust and practical protection against various sophisticated input injection attacks. To handle complex real-world applications, we develop a bidirectional analysis that combines forward and backward static analysis techniques to identify in-tended commands or SQL queries to ensure the correct execution of the randomized target program. We implement Spinner for the shell command processor and two different database engines(MySQL and SQLite) and in diverse programming languages including C/C++, PHP, JavaScript and Lua. Our evaluation results on 42real-world applications including 27 vulnerable ones show that it effectively prevents a variety of input injection attacks with low runtime overhead (around 5%).", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484577"}, {"primary_key": "2100929", "vector": [], "sparse_vector": [], "title": "Securing Parallel-chain Protocols under Variable Mining Power.", "authors": ["<PERSON><PERSON><PERSON>", "Viswa Virinchi Muppirala", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Several emerging proof-of-work (PoW) blockchain protocols rely on a ''parallel-chain'' architecture for scaling, where instead of a single chain, multiple chains are run in parallel and aggregated. A key requirement of practical PoW blockchains is to adapt to mining power variations over time (Bitcoin's total mining power has increased by a 1014 factor over the decade). In this paper, we consider the design of provably secure parallel-chain protocols which can adapt to such mining power variations.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485254"}, {"primary_key": "2100930", "vector": [], "sparse_vector": [], "title": "Earable Authentication via Acoustic Toothprint.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Earables (ear wearable) are rapidly emerging as a new platform to enable a variety of personal applications. The traditional authentication methods thus become less applicable and inconvenient for earables due to their limited input interface. Earables, however, often feature rich around the head sensing capability that can be leveraged to capture new types of biometrics. In this work, we propose ToothSonic that leverages the toothprint-induced sonic effect produced by a user performing teeth gestures for user authentication. In particular, we design several representative teeth gestures that can produce effective sonic waves carrying the information of the toothprint. To reliably capture the acoustic toothprint, it leverages the occlusion effect of the ear canal and the inward-facing microphone of the earables. It then extracts multi-level acoustic features to represent the intrinsic acoustic toothprint for authentication. The key advantages of ToothSonic are that it is suitable for earables and is resistant to various spoofing attacks as the acoustic toothprint is captured via the private teeth-ear channel of the user that is unknown to others. Our preliminary studies with 20 participants show that ToothSonic achieves 97% accuracy with only three teeth gestures.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485340"}, {"primary_key": "2100931", "vector": [], "sparse_vector": [], "title": "DataLens: Scalable Privacy Preserving Training via Gradient Compression and Aggregation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Recent success of deep neural networks (DNNs) hinges on the availability of large-scale dataset; however, training on such dataset often poses privacy risks for sensitive training information. In this paper, we aim to explore the power of generative models and gradient sparsity, and propose a scalable privacy-preserving generative model DataLens, which is able to generate synthetic data in a differentially private (DP) way given sensitive input data. Thus, it is possible to train models for different down-stream tasks with the generated data while protecting the private information. In particular, we leverage the generative adversarial networks (GAN) and PATE framework to train multiple discriminators as \"teacher\" models, allowing them to vote with their gradient vectors to guarantee privacy.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484579"}, {"primary_key": "2100932", "vector": [], "sparse_vector": [], "title": "Differential Privacy for Directional Data.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Directional data is an important class of data where the magnitudes of the data points are negligible. It naturally occurs in many real-world scenarios: For instance, geographic locations (approximately) lie on a sphere, and periodic data such as time of day, or day of week can be interpreted as points on a circle. Massive amounts of directional data are collected by location-based service platforms such as Google Maps or Foursquare, who depend on mobility data from users' smartphones or wearable devices to enable their analytics and marketing businesses. However, such data is often highly privacy-sensitive and hence demands measures to protect the privacy of the individuals whose data is collected and processed. Starting with the von Mises-Fisher distribution, we therefore propose and analyze two novel privacy mechanisms for directional data by combining directional statistics with differential privacy, which presents the current state-of-the-art for quantifying and limiting information disclosure about individuals. As we will see, our specialized privacy mechanisms achieve a better privacy-utility trade-off than ex post adaptions of established mechanisms to directional data.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484734"}, {"primary_key": "2100934", "vector": [], "sparse_vector": [], "title": "Key Agreement for Decentralized Secure Group Messaging with Strong Security Guarantees.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure group messaging protocols, providing end-to-end encryption for group communication, need to handle mobile devices frequently being offline, group members being added or removed, and the possibility of device compromises during long-lived chat sessions. Existing work targets a centralized network model in which all messages are routed through a single server, which is trusted to provide a consistent total order on updates to the group state. In this paper we adapt secure group messaging for decentralized networks that have no central authority. Servers may still optionally be used, but they are trusted less. We define decentralized continuous group key agreement (DCGKA), a new cryptographic primitive encompassing the core of a decentralized secure group messaging protocol; we give a practical construction of a DCGKA protocol and prove its security; and we describe how to construct a full messaging protocol from DCGKA. In the face of device compromise our protocol achieves forward secrecy and post-compromise security. We evaluate the performance of a prototype implementation, and demonstrate that our protocol has practical efficiency.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484542"}, {"primary_key": "2100935", "vector": [], "sparse_vector": [], "title": "&quot;Hello, It&apos;s Me&quot;: Deep Learning-based Speech Synthesis Attacks in the Real World.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Advances in deep learning have introduced a new wave of voice synthesis tools, capable of producing audio that sounds as if spoken by a target speaker. If successful, such tools in the wrong hands will enable a range of powerful attacks against both humans and software systems (aka machines). This paper documents efforts and findings from a comprehensive experimental study on the impact of deep-learning based speech synthesis attacks on both human listeners and machines such as speaker recognition and voice-signin systems. We find that both humans and machines can be reliably fooled by synthetic speech and that existing defenses against synthesized speech fall short. These findings highlight the need to raise awareness and develop new protections against synthetic speech for both humans and machines.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484742"}, {"primary_key": "2100939", "vector": [], "sparse_vector": [], "title": "An Inside Look into the Practice of Malware Analysis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Malware analysis aims to understand how malicious software carries out actions necessary for a successful attack and identify the possible impacts of the attack. While there has been substantial research focused on malware analysis and it is an important tool for practitioners in industry, the overall malware analysis process used by practitioners has not been studied. As a result, an understanding of common malware analysis workflows and their goals is lacking. A better understanding of these workflows could help identify new research directions that are impactful in practice. In order to better understand malware analysis processes, we present the results of a user study with 21 professional malware analysts with diverse backgrounds who work at 18 different companies. The study focuses on answering three research questions: (1) What are the different objectives of malware analysts in practice?, (2) What comprises a typical professional malware analyst workflow, and (3) When analysts decide to conduct dynamic analysis, what factors do they consider when setting up a dynamic analysis system? Based on participant responses, we propose a taxonomy of malware analysts and identify five common analysis workflows. We also identify challenges that analysts face during the different stages of their workflow. From the results of the study, we propose two potential directions for future research, informed by challenges described by the participants. Finally, we recommend guidelines for developers of malware analysis tools to consider in order to improve the usability of such tools.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484759"}, {"primary_key": "2100940", "vector": [], "sparse_vector": [], "title": "Ghost in the Binder: Binder Transaction Redirection Attacks in Android System Services.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Binder, the main mechanism for Android applications to access system services, adopts a client-server role model in its design, assuming the system service as the server and the application as the client. However, a growing number of scenarios require the system service to act as a Binder client and to send queries to a Binder server possibly instantiated by the application. Departing from this role-reversal possibility, this paper proposes the Binder Transaction Redirection (BiTRe) attacks, where the attacker induces the system service to transact with a customized Binder server and then attacks from the Binder server---an often unprotected direction. We demonstrate the scale of the attack surface by enumerating the utilizable Binder interfaces in BiTRe, and discover that the attack surface grows with the Android release version. In Android 11, more than 70% of the Binder interfaces are affected by or can be utilized in BiTRe. We prove the attacks' feasibility by (1) constructing a prototype system that can automatically generate executable programs to reach a substantial part of the attack surface, and (2) identifying a series of vulnerabilities, which are acknowledged by Google and assigned ten CVEs.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484801"}, {"primary_key": "2100941", "vector": [], "sparse_vector": [], "title": "Warmonger: Inflicting Denial-of-Service via Serverless Functions in the Cloud.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "We debut the Warmonger attack, a novel attack vector that can cause denial-of-service between a serverless computing platform and an external content server. The Warmonger attack exploits the fact that a serverless computing platform shares the same set of egress IPs among all serverless functions, which belong to different users, to access an external content server. As a result, a malicious user on this platform can purposefully misbehave and cause these egress IPs to be blocked by the content server, resulting in a platform-wide denial of service. To validate the Warmonger attack, we ran months-long experiments, collected and analyzed the egress IP usage pattern of four major serverless service providers (SSPs). We also conducted an in-depth evaluation of an attacker's possible moves to inflict an external server and cause IP-blockage. We demonstrate that some SSPs use surprisingly small numbers of egress IPs (as little as only four) and share them among their users, and that the serverless platform provides sufficient leverage for a malicious user to conduct well-known misbehaviors and cause IP-blockage. Our study unveiled a potential security threat on the emerging serverless computing platform, and shed light on potential mitigation approaches.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485372"}, {"primary_key": "2100943", "vector": [], "sparse_vector": [], "title": "Android on PC: On the Security of End-user Android Emulators.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Android emulators today are not only acting as a debugging tool for developers but also serving the massive end-users. These end-user Android emulators have attracted millions of users due to their advantages of running mobile apps on desktops and are especially appealing for mobile game players who demand larger screens and better performance. Besides, they commonly provide some customized assistant functionalities to improve the user experience, such as keyboard mapping and app installation from the host. To implement these services, emulators inevitably introduce communication channels between host OS and Android OS (in the Virtual Machine), thus forming a unique architecture which mobile phone does not have. However, it is unknown whether this architecture brings any new security risks to emulators.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484774"}, {"primary_key": "2100944", "vector": [], "sparse_vector": [], "title": "Chunk-Level Password Guessing: Towards Modeling Refined Password Composition Representations.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Textual password security hinges on the guessing models adopted by attackers, in which a suitable password composition representation is an influential factor. Unfortunately, the conventional models roughly regard a password as a sequence of characters, or natural-language-based words, which are password-irrelevant. Experience shows that passwords exhibit internal and refined patterns, e.g., \"4ever, ing or 2015\", varying significantly among periods and regions. However, the refined representations and their security impacts could not be automatically understood by state-of-the-art guessing models (e.g., <PERSON><PERSON>).", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484743"}, {"primary_key": "2100945", "vector": [], "sparse_vector": [], "title": "Efficient Online-friendly Two-Party ECDSA Signature.", "authors": ["<PERSON><PERSON> Xue", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "summary": "Two-party ECDSA signatures have received much attention due to their widespread deployment in cryptocurrencies. Depending on whether or not the message is required, we could divide two-party signing into two different phases, namely, offline and online. Ideally, the online phase should be made as lightweight as possible. At the same time, the cost of the offline phase should remain similar to that of a normal signature generation. However, the existing two-party protocols of ECDSA are not optimal: either their online phase requires decryption of a ciphertext, or their offline phase needs at least two executions of multiplicative-to-additive conversion which dominates the overall complexity. This paper proposes an online-friendly two-party ECDSA with a lightweight online phase and a single multiplicative-to-additive function in the offline phase. It is constructed by a novel design of a re-sharing of the secret key and a linear sharing of the nonce. Our scheme significantly improves previous protocols based on either oblivious transfer or homomorphic encryption. We implement our scheme and show that it outperforms prior online-friendly schemes (i.e., those have lightweight online cost) by a factor of roughly 2 to 9 in both communication and computation. Furthermore, our two-party scheme could be easily extended to the 2-out-of-n threshold ECDSA.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484803"}, {"primary_key": "2100946", "vector": [], "sparse_vector": [], "title": "Automated Bug Hunting With Data-Driven Symbolic Root Cause Analysis.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "The increasing cost of successful cyberattacks has caused a mindset shift, whereby defenders now employ proactive defenses, namely software bug hunting, alongside existing reactive measures (firewalls, IDS, IPS) to protect systems. Unfortunately the path from hunting bugs to deploying patches remains laborious and expensive, requires human expertise, and still misses serious memory corruptions. Motivated by these challenges, we propose bug hunting using symbolically reconstructed states based on execution traces to achieve better detection and root cause analysis of overflow, use-after-free, double free, and format string bugs across user programs and their imported libraries. We discover that with the right use of widely available hardware processor tracing and partial memory snapshots, powerful symbolic analysis can be used on real-world programs while managing path explosion. Better yet, data can be captured from production deployments of live software on end-host systems transparently, aiding in the analysis of user clients and long-running programs like web servers.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485363"}, {"primary_key": "2100947", "vector": [], "sparse_vector": [], "title": "Validating the Integrity of Audit Logs Against Execution Repartitioning Attacks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Provenance-based causal analysis of audit logs has proven to be an invaluable method of investigating system intrusions. However, it also suffers from dependency explosion, whereby long-running processes accumulate many dependencies that are hard to unravel. Execution unit partitioning addresses this by segmenting dependencies into units of work, such as isolating the events that processed a single HTTP request. Unfortunately, we discover that current designs have a semantic gap problem due to how system calls and application log messages are used to infer complex internal program states. We demonstrate how attackers can modify existing code exploits to control event partitioning, breaking links in the attack and framing innocent users. We also show how our techniques circumvent existing program and log integrity defenses.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484551"}, {"primary_key": "2100948", "vector": [], "sparse_vector": [], "title": "Morpheus: Bringing The (PKCS) One To Meet the Oracle.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "This paper focuses on developing an automatic, black-box testing approach called Morpheus to check the non-compliance of libraries implementing PKCS#1-v1.5 signature verification with the PKCS#1-v1.5 standard. Non-compliance can not only make implementations vulnerable to Bleichenbacher-style RSA signature forgery attacks but also can induce interoperability issues. For checking non-compliance, Morpheus adaptively generates interesting test cases and then takes advantage of an oracle, a formally proven correct implementation of PKCS#1-v1.5 signature standard, to detect non-compliance in an implementation under test. We have used Morpheus to test 45 implementations of PKCS#1-v1.5 signature verification and discovered that 6 of them are susceptible to variants of the Bleichenbacher-style low public exponent RSA signature forgery attack, 1 implementation has a buffer overflow, 33 implementations have incompatibility issues, and 8 implementations have minor leniencies. Our findings have been responsibly disclosed and positively acknowledged by the developers.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485382"}, {"primary_key": "2100951", "vector": [], "sparse_vector": [], "title": "Demons in the Shared Kernel: Abstract Resource Attacks Against OS-level Virtualization.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Shen", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Due to its faster start-up speed and better resource utilization efficiency, OS-level virtualization has been widely adopted and has become a fundamental technology in cloud computing. Compared to hardware virtualization, OS-level virtualization leverages the shared-kernel design to achieve high efficiency and runs multiple user-space instances (a.k.a., containers) on the shared kernel. However, in this paper, we reveal a new attack surface that is intrinsic to OS-level virtualization, affecting Linux, FreeBSD, and Fuchsia. The root cause is that the shared-kernel design in OS-level virtualization results containers in sharing thousands of kernel variables and data structures directly and indirectly. Without exploiting any kernel vulnerabilities, a non-privileged container can easily exhaust the shared kernel variables and data structure instances to cause DoS attacks against other containers. Compared with the physical resources, these kernel variables or data structure instances (termed abstract resources) are more prevalent but under-protected. To show the importance of confining abstract resources, we conduct abstract resource attacks that target different aspects of the OS kernel. The results show that attacking abstract resources is highly practical and critical. We further conduct a systematic analysis to identify vulnerable abstract resources in the Linux kernel, which successfully detects 1,010 abstract resources and 501 of them can be repeatedly consumed dynamically. We also conduct the attacking experiments in the self-deployed shared-kernel container environments on the top 4 cloud vendors. The results show that all environments are vulnerable to abstract resource attacks. We conclude that containing abstract resources is hard and give out multiple strategies for mitigating the risks.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484744"}, {"primary_key": "2100952", "vector": [], "sparse_vector": [], "title": "QuickSilver: Efficient and Affordable Zero-Knowledge Proofs for Circuits and Polynomials over Any Field.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Chen<PERSON>", "<PERSON>"], "summary": "Zero-knowledge (ZK) proofs with an optimal memory footprint have attracted a lot of attention, because such protocols can easily prove very large computation with a small memory requirement. Such ZK protocol only needs O(M) memory for both parties, where M is the memory required to verify the statement in the clear. In this paper, we propose several new constant-round ZK protocols in this setting, which improve the concrete efficiency and, at the same time, enable sublinear amortized communication for circuits with some notion of relaxed uniformity. In the circuit-based model, where the computation is represented as a circuit over a field, our ZK protocol achieves a communication complexity of 1 field element per non-linear gate for any field size while keeping the computation very cheap. We implemented our protocol, which shows extremely high efficiency and affordability. Compared to the previous best-known implementation, we achieve 6x--7x improvement in computation and 3x--7x improvement in communication. When running on intro-level AWS instances, our protocol only needs one US dollar to prove one trillion AND gates (or 2.5 US dollars for one trillion multiplication gates over a 61-bit field). In the setting where part of the computation can be represented as a set of polynomials with a \"degree-separated\" format, we can achieve communication sublinear to the polynomial size: the communication only depends on the total number of distinct variables in all the polynomials and the highest degree of all polynomials, independent of the number of multiplications to compute all polynomials. Using the improved ZK protocol, we can prove matrix multiplication with communication proportional to the input size, rather than the number of multiplications. Proving the multiplication of two 1024 x 1024 matrices, our implementation, with one thread and 1 GB of memory, only needs 10 seconds and communicates 25 MB.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484556"}, {"primary_key": "2100954", "vector": [], "sparse_vector": [], "title": "HardsHeap: A Universal and Extensible Framework for Evaluating Secure Allocators.", "authors": ["Insu Yun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Secure allocators have been extensively studied to mitigate heap vulnerabilities. They employ safe designs and randomized mechanisms to stop or mitigate heap exploitation. Despite extensive research efforts, secure allocators can only be evaluated by with theoretical analysis or pre-defined data sets, which are insufficient to effectively reflect powerful adversaries in the real world.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484740"}, {"primary_key": "2100955", "vector": [], "sparse_vector": [], "title": "You Make Me Tremble: A First Look at Attacks Against Structural Control Systems.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Alvaro <PERSON>"], "summary": "This paper takes a first look at the potential consequences of cyberattacks against structural control systems. We design algorithms and implement them in a testbed and on well-known benchmark models for buildings and bridges. Our results show that attacks to structures equipped with semi-active and active vibration control systems can let the attacker oscillate the building or bridge at the resonance frequency, effectively generating threats to the structure and the people using it. We also implement and test the effectiveness of attack-detection systems.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485386"}, {"primary_key": "2100956", "vector": [], "sparse_vector": [], "title": "Statically Discovering High-Order Taint Style Vulnerabilities in OS Kernels.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Static analysis is known to yield numerous false alarms when used in bug finding, especially for complex vulnerabilities in large code bases like the Linux kernel. One important class of such complex vulnerabilities is what we call \"high-order taint style vulnerability\", where the taint flow from the user input to the vulnerable site crosses the boundary of a single entry function invocation (i.e., syscall). Due to the large scope and high precision requirement, few have attempted to solve the problem. In this paper, we present SUTURE, a highly precise and scalable static analysis tool capable of discovering high-order vulnerabilities in OS kernels. SUTURE employs a novel summary-based high-order taint flow construction approach to efficiently enumerate the cross-entry taint flows, while incorporating multiple innovative enhancements on analysis precision that are unseen in existing tools, resulting in a highly precise inter-procedural flow-, context-, field-, index-, and opportunistically path-sensitive static taint analysis. We apply SUTURE to discover high-order taint vulnerabilities in multiple Android kernels from mainstream vendors (e.g., Google, Samsung, Huawei), the results show that SUTURE can both confirm known high-order vulnerabilities and uncover new ones. So far, SUTURE generates 79 true positive warning groups, of which 19 have been confirmed by the vendors, including a high severity vulnerability rated by Google. SUTURE also achieves a reasonable false positive rate (51.23%) perceived by users of our tool.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484798"}, {"primary_key": "2100958", "vector": [], "sparse_vector": [], "title": "Doubly Efficient Interactive Proofs for General Arithmetic Circuits with Linear Prover Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We propose a new doubly efficient interactive proof protocol for general arithmetic circuits. The protocol generalizes the interactive proof for layered circuits proposed by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> to arbitrary circuits, while preserving the optimal prover complexity that is strictly linear to the size of the circuits. The proof size remains succinct for low depth circuits and the verifier time is sublinear for structured circuits. We then construct a new zero knowledge argument scheme for general arithmetic circuits using our new interactive proof protocol together with polynomial commitments. Our key technique is a new sumcheck equation that reduces a claim about the output of one layer to claims about its input only, instead of claims about all the layers above which inevitably incurs an overhead proportional to the depth of the circuit. We developed efficient algorithms for the prover to run this sumcheck protocol and to combine multiple claims back into one in linear time in the size of the circuit. Not only does our new protocol achieve optimal prover complexity asymptotically, but it is also efficient in practice. Our experiments show that it only takes 0.3 seconds to generate the proof for a circuit with more than 600,000 gates, which is 13 times faster than the original interactive proof protocol on the corresponding layered circuit. The proof size is 208 kilobytes and the verifier time is 66 milliseconds. Our implementation can take general arithmetic circuits directly, without transforming them to layered circuits with a high overhead on the size of the circuit.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484767"}, {"primary_key": "2100959", "vector": [], "sparse_vector": [], "title": "Membership Inference Attacks Against Recommender Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Recently, recommender systems have achieved promising performances and become one of the most widely used web applications. However, recommender systems are often trained on highly sensitive user data, thus potential data leakage from recommender systems may lead to severe privacy problems.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484770"}, {"primary_key": "2100960", "vector": [], "sparse_vector": [], "title": "Reverse Attack: Black-box Attacks on Collaborative Recommendation.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Jiadong Lou", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Collaborative filtering (CF) recommender systems have been extensively developed and widely deployed in various social websites, promoting products or services to the users of interest. Meanwhile, work has been attempted at poisoning attacks to CF recommender systems for distorting the recommend results to reap commercial or personal gains stealthily. While existing poisoning attacks have demonstrated their effectiveness with the offline social datasets, they are impractical when applied to the real setting on online social websites. This paper develops a novel and practical poisoning attack solution toward the CF recommender systems without knowing involved specific algorithms nor historical social data information a priori. Instead of directly attacking the unknown recommender systems, our solution performs certain operations on the social websites to collect a set of sampling data for use in constructing a surrogate model for deeply learning the inherent recommendation patterns. This surrogate model can estimate the item proximities, learned by the recommender systems. By attacking the surrogate model, the corresponding solutions (for availability and target attacks) can be directly migrated to attack the original recommender systems. Extensive experiments validate the generated surrogate model's reproductive capability and demonstrate the effectiveness of our attack upon various CF recommender algorithms.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484805"}, {"primary_key": "2100961", "vector": [], "sparse_vector": [], "title": "Structural Attack against Graph Based Android Malware Detection.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Malware detection techniques achieve great success with deeper insight into the semantics of malware. Among existing detection techniques, function call graph (FCG) based methods achieve promising performance due to their prominent representations of malware's functionalities. Meanwhile, recent adversarial attacks not only perturb feature vectors to deceive classifiers (i.e., feature-space attacks) but also investigate how to generate real evasive malware (i.e., problem-space attacks). However, existing problem-space attacks are limited due to their inconsistent transformations between feature space and problem space.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485387"}, {"primary_key": "2100962", "vector": [], "sparse_vector": [], "title": "Black-box Adversarial Attacks on Commercial Speech Platforms with Minimal Information.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Qingyang Teng", "<PERSON><PERSON>"], "summary": "Adversarial attacks against commercial black-box speech platforms, including cloud speech APIs and voice control devices, have received little attention until recent years. The current \"black-box\" attacks all heavily rely on the knowledge of prediction/confidence scores to craft effective adversarial examples, which can be intuitively defended by service providers without returning these messages. In this paper, we propose two novel adversarial attacks in more practical and rigorous scenarios. For commercial cloud speech APIs, we propose Occam, a decision-only black-box adversarial attack, where only final decisions are available to the adversary. In Occam, we formulate the decision-only AE generation as a discontinuous large-scale global optimization problem, and solve it by adaptively decomposing this complicated problem into a set of sub-problems and cooperatively optimizing each one. Our Occam is a one-size-fits-all approach, which achieves 100% success rates of attacks with an average SNR of 14.23dB, on a wide range of popular speech and speaker recognition APIs, including Google, Alibaba, Microsoft, Tencent, iFlytek, and Jingdong, outperforming the state-of-the-art black-box attacks. For commercial voice control devices, we propose NI-Occam, the first non-interactive physical adversarial attack, where the adversary does not need to query the oracle and has no access to its internal information and training data. We combine adversarial attacks with model inversion attacks, and thus generate the physically-effective audio AEs with high transferability without any interaction with target devices. Our experimental results show that NI-Occam can successfully fool Apple Siri, Microsoft Cortana, Google Assistant, iFlytek and Amazon Echo with an average SRoA of 52% and SNR of 9.65dB, shedding light on non-interactive physical attacks against voice control devices.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485383"}, {"primary_key": "2100963", "vector": [], "sparse_vector": [], "title": "Regression Greybox Fuzzing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "What you change is what you fuzz! In an empirical study of all fuzzer-generated bug reports in OSSFuzz, we found that four in every five bugs have been introduced by recent code changes. That is, 77% of 23k bugs are regressions. For a newly added project, there is usually an initial burst of new reports at 2-3 bugs per day. However, after that initial burst, and after weeding out most of the existing bugs, we still get a constant rate of 3-4 bug reports per week. The constant rate can only be explained by an increasing regression rate. Indeed, the probability that a reported bug is a regression (i.e., we could identify the bug-introducing commit) increases from 20% for the first bug to 92% after a few hundred bug reports. In this paper, we introduce regression greybox fuzzing (RGF) a fuzzing approach that focuses on code that has changed more recently or more often. However, for any active software project, it is impractical to fuzz sufficiently each code commit individually. Instead, we propose to fuzz all commits simultaneously, but code present in more (recent) commits with higher priority. We observe that most code is never changed and relatively old. So, we identify means to strengthen the signal from executed code-of-interest. We also extend the concept of power schedules to the bytes of a seed and introduce Ant Colony Optimization to assign more energy to those bytes which promise to generate more interesting inputs. Our large-scale fuzzing experiment demonstrates the validity of our main hypothesis and the efficiency of regression greybox fuzzing. We conducted our experiments in a reproducible manner within Fuzzbench, an extensible fuzzer evaluation platform. Our experiments involved 3+ CPU-years worth of fuzzing campaigns and 20 bugs in 15 open-source C programs available on OSSFuzz.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484596"}, {"primary_key": "2100964", "vector": [], "sparse_vector": [], "title": "Dissecting <PERSON><PERSON> in the Wild.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Although the use of pay-per-click mechanisms stimulates the prosperity of the mobile advertisement network, fraudulent ad clicks result in huge financial losses for advertisers. Extensive studies identify click fraud according to click/traffic patterns based on dynamic analysis. However, in this study, we identify a novel click fraud, named humanoid attack, which can circumvent existing detection schemes by generating fraudulent clicks with similar patterns to normal clicks. We implement the first tool ClickScanner to detect humanoid attacks on Android apps based on static analysis and variational AutoEncoders (VAEs) with limited knowledge of fraudulent examples. We define novel features to characterize the patterns of humanoid attacks in the apps' bytecode level. ClickScanner builds a data dependency graph (DDG) based on static analysis to extract these key features and form a feature vector. We then propose a classification model only trained on benign datasets to overcome the limited knowledge of humanoid attacks.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3484546"}, {"primary_key": "2100965", "vector": [], "sparse_vector": [], "title": "Can We Use Arbitrary Objects to Attack LiDAR Perception in Autonomous Driving?", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>oa<PERSON>", "Lu <PERSON>", "<PERSON><PERSON>"], "summary": "As an effective way to acquire accurate information about the driving environment, LiDAR perception has been widely adopted in autonomous driving. The state-of-the-art LiDAR perception systems mainly rely on deep neural networks (DNNs) to achieve good performance. However, DNNs have been demonstrated vulnerable to adversarial attacks. Although there are a few works that study adversarial attacks against LiDAR perception systems, these attacks have some limitations in feasibility, flexibility, and stealthiness when being performed in real-world scenarios. In this paper, we investigate an easier way to perform effective adversarial attacks with high flexibility and good stealthiness against LiDAR perception in autonomous driving. Specifically, we propose a novel attack framework based on which the attacker can identify a few adversarial locations in the physical space. By placing arbitrary objects with reflective surface around these locations, the attacker can easily fool the LiDAR perception systems. Extensive experiments are conducted to evaluate the performance of the proposed attack, and the results show that our proposed attack can achieve more than 90% success rate. In addition, our real-world study demonstrates that the proposed attack can be easily performed using only two commercial drones. To the best of our knowledge, this paper presents the first study on the effect of adversarial locations on LiDAR perception models' behaviors, the first investigation on how to attack LiDAR perception systems using arbitrary objects with reflective surface, and the first attack against LiDAR perception systems using commercial drones in physical world. Potential defense strategies are also discussed to mitigate the proposed attacks.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120.3485377"}, {"primary_key": "2253483", "vector": [], "sparse_vector": [], "title": "CCS &apos;21: 2021 ACM SIGSAC Conference on Computer and Communications Security, Virtual Event, Republic of Korea, November 15 - 19, 2021", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "It is our great pleasure to welcome you to the WAHC 2021 - 9th Workshop on Encrypted Computing & Applied Homomorphic Cryptography. WAHC was created in 2013 as a forum to foster discussion of a wide variety of aspects of encrypted computing. This year's workshop continues its tradition of bringing together professionals, researchers and practitioners from industry, government, and academia to discuss the latest progress in topics such as homomorphic encryption, multiparty computation, functional encryption, secure function evaluation, private information retrieval and searchable encryption. The call for papers attracted submissions from Asia, Europe, South America, and the United States. There were 21 submissions this year. Each submission was reviewed by at least three Program Committee members. This year, the submissions were of very high quality, and it was a challenging task to select 4 full papers and 3 demo papers that showcase the wide range of techniques and interests of the community.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3460120"}, {"primary_key": "2253493", "vector": [], "sparse_vector": [], "title": "WAHC &apos;21: Proceedings of the 9th on Workshop on Encrypted Computing &amp; Applied Homomorphic Cryptography, Virtual Event, Korea, 15 November 2021", "authors": [], "summary": "It is our great pleasure to welcome you to the WAHC 2021 - 9th Workshop on Encrypted Computing & Applied Homomorphic Cryptography. WAHC was created in 2013 as a forum to foster discussion of a wide variety of aspects of encrypted computing. This year's workshop continues its tradition of bringing together professionals, researchers and practitioners from industry, government, and academia to discuss the latest progress in topics such as homomorphic encryption, multiparty computation, functional encryption, secure function evaluation, private information retrieval and searchable encryption. The call for papers attracted submissions from Asia, Europe, South America, and the United States. There were 21 submissions this year. Each submission was reviewed by at least three Program Committee members. This year, the submissions were of very high quality, and it was a challenging task to select 4 full papers and 3 demo papers that showcase the wide range of techniques and interests of the community.", "published": "2021-01-01", "category": "ccs", "pdf_url": "", "sub_summary": "", "source": "ccs", "doi": "10.1145/3474366"}]