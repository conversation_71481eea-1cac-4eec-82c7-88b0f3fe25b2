import { Paper } from './class-utils';
import { PiFilePdf, PiCopy, PiInfo } from "react-icons/pi";
import { toast } from 'react-hot-toast';
import { SiGooglescholar } from "react-icons/si";
import { CollectButton } from '../collect/collect';
import { Button } from '../ui/button';
import React, { useState, useRef, useEffect } from 'react';

interface GridPaperProps {
  paper: Paper;
  onPaperClick: (paper: Paper) => void;
  isViewed: boolean;
  index: number;
  isTitleTranslation: boolean;
  onCollect?: (paper: Paper) => void;
  isCollected?: boolean;
}

const handleGoogleScholar = (title: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const searchUrl = `https://scholar.google.com/scholar?q=${encodeURIComponent(title)}`;
    window.open(searchUrl, '_blank', 'noopener,noreferrer');
};

export default function GridPaper({ 
  paper, 
  onPaperClick, 
  isViewed, 
  index, 
  isTitleTranslation,
  onCollect,
  isCollected = false 
}: GridPaperProps) {
    // 右键菜单相关状态
    const [menuVisible, setMenuVisible] = useState(false);
    const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
    const cardRef = useRef<HTMLDivElement>(null);

    // 关闭菜单的全局点击监听
    useEffect(() => {
      const handleClick = () => setMenuVisible(false);
      if (menuVisible) {
        document.addEventListener('click', handleClick);
      }
      return () => document.removeEventListener('click', handleClick);
    }, [menuVisible]);

    // 右键菜单事件
    const handleContextMenu = (e: React.MouseEvent) => {
      e.preventDefault();
      setMenuPosition({ x: e.clientX, y: e.clientY });
      setMenuVisible(true);
    };

    // 复制 PDF 链接
    const handleCopyPdf = async (e: React.MouseEvent) => {
      e.stopPropagation();
      if (paper.pdf_url) {
        await navigator.clipboard.writeText(paper.pdf_url);
        toast.success('PDF链接已复制');
      } else {
        toast.error('无PDF链接');
      }
      setMenuVisible(false);
    };

    // 复制论文信息
    const handleCopyInfo = async (e: React.MouseEvent) => {
      e.stopPropagation();
      const info = `标题：${paper.title}\n年份：${paper.published?.slice(0, 4) || '未知'}\n来源：${paper.source || '未知'}\n摘要：${paper.abstract_summary || paper.summary || '无摘要'}`;
      await navigator.clipboard.writeText(info);
      toast.success('论文信息已复制');
      setMenuVisible(false);
    };

    // 打开 PDF 链接
    const handleOpenPdf = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (paper.pdf_url) {
        window.open(paper.pdf_url, '_blank', 'noopener,noreferrer');
      } else {
        toast.error('无PDF链接');
      }
      setMenuVisible(false);
    };

    return (
    <div 
      ref={cardRef}
      onContextMenu={handleContextMenu}
      key={`${paper?.title ?? ''}-${Math.random()}`}
      className={`border rounded-lg p-4 shadow-md hover:shadow-lg cursor-pointer ${isViewed ? 'bg-gray-100 dark:bg-gray-800' : ''}`}
      onClick={() => onPaperClick(paper)}
    >
    
    {/* 论文标题 */}
    <div className="flex items-start gap-2 mb-2">
        <div className="flex-1">
            {/* 根据 isTitleTranslation 决定显示哪种标题 */}
            {isTitleTranslation && paper?.title_translation ? 
            (<div>
                <a className="text-sm text-gray-500">
                    {index + 1}. {" "}
                </a>
                <a className="text-sm font-bold">
                    {paper?.title_translation || '暂无'} 
                </a>
            </div>) : 
            (<div>
                <a className="text-sm text-gray-500">
                    {index + 1}. {" "}
                </a>
                <a className="text-sm font-bold">
                    {paper?.title || '暂无'}
                </a>
            </div>)}

            <a className="text-gray-500 text-sm block mt-1">
                {/* <span className="inline-block text-xs border border-gray-300 rounded px-1 py-0.5 mr-2">
                        {Math.floor((paper?.distance || 0) * 100)}
                </span> */}
                {paper?.published?.slice(0, 7) || '暂无'} &nbsp;-&nbsp; {paper?.source || 'none'}
            </a>
        </div>

        {/* <div className="flex items-center  gap-2"> */}
        <div className="flex flex-col gap-1">
            {/* 按钮行，将两个按钮放在一起并保持样式一致 */}
            {onCollect && (
              <CollectButton
                paper={paper}
                isCollected={isCollected}
                onCollect={() => onCollect(paper)}
              />
            )}
            
            <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-gray-400 hover:text-gray-900"
                onClick={(e) => handleGoogleScholar(paper?.title || '', e)}
                title="谷歌学术页面"
            >
                <SiGooglescholar size={14} />
            </Button>
            
            {/* <button
                className="text-gray-500 hover:text-gray-700 relative group"
                onClick={(e) => handleCopy(paper, e)}
            >
                <PiCopy size={24} />
                <span className="invisible group-hover:visible absolute -left-24 top-1/2 -translate-y-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                复制论文信息
                </span>
            </button> */}
            {/* <a  
                href={paper?.pdf_url || ''}
                target="_blank"
                rel="noopener noreferrer"
                className="text-red-500 hover:text-red-700 relative group"
                onClick={e => e.stopPropagation()}
            >
                <PiFilePdf size={24} className="dark:text-white" />
                <span className="invisible group-hover:visible absolute -left-20 top-1/2 -translate-y-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                    打开PDF
                </span>
            </a> */}
        </div>
    </div>

    {/* 摘要精简 */}
    {paper?.abstract_summary && (
        <p className="text-gray-600 text-sm mb-2" style={{ textIndent: '2em' }}>
            {paper.abstract_summary}
        </p>
    )}

    {/* 英文摘要 */}
    {paper?.summary && !paper?.abstract_summary && (
        <p className="text-gray-600 text-sm mb-2" style={{ textIndent: '2em' }}>
            {paper?.summary?.slice(0, 200)}...
        </p>
    )}

    {/* 右键菜单 */}
    {menuVisible && (
      <div
        style={{
          position: 'fixed',
          top: menuPosition.y,
          left: menuPosition.x,
          zIndex: 9999,
          background: 'white',
          border: '1px solid #eee',
          borderRadius: 4,
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          minWidth: 120,
          padding: 4,
        }}
      >
        <div
          className="px-3 py-1 hover:bg-gray-100 cursor-pointer text-sm rounded-md flex items-center gap-2"
          onClick={handleOpenPdf}
        >
          <PiFilePdf size={16} />
          打开 PDF
        </div>
        <div
          className="px-3 py-1 hover:bg-gray-100 cursor-pointer text-sm rounded-md flex items-center gap-2"
          onClick={handleCopyPdf}
        >
          <PiCopy size={16} />
          PDF 链接
        </div>
        <div
          className="px-3 py-1 hover:bg-gray-100 cursor-pointer text-sm rounded-md flex items-center gap-2"
          onClick={handleCopyInfo}
        >
          <PiCopy size={16} />
          论文信息
        </div>
      </div>
    )}
    </div>
  );
}