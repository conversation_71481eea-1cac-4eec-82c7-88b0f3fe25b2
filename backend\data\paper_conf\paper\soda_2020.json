[{"primary_key": "2683172", "vector": [], "sparse_vector": [], "title": "Zeros of ferromagnetic 2-spin systems.", "authors": ["<PERSON><PERSON>", "Jingcheng Liu", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Zeros of ferromagnetic 2-spin systems<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.181 - 192Chapter DOI:https://doi.org/10.1137/1.9781611975994.11PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study zeros of the partition functions of ferromagnetic 2-state spin systems in terms of the external field, and obtain new zero-free regions of these systems via a refinement of <PERSON><PERSON>'s and <PERSON><PERSON>'s contraction method. The strength of our results is that they do not depend on the maximum degree of the underlying graph. Via <PERSON><PERSON><PERSON>'s method, we also obtain new efficient and deterministic approximate counting algorithms. When the edge interaction is attractive for both spins, our algorithm outperforms all other methods such as Markov chain Monte Carlo and correlation decay. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.11"}, {"primary_key": "2683173", "vector": [], "sparse_vector": [], "title": "Approximate Maximum Matching in Random Streams.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximate Maximum Matching in Random Streams<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>1773 - 1785Chapter DOI:https://doi.org/10.1137/1.9781611975994.108PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper, we study the problem of finding a maximum matching in the semi-streaming model when edges arrive in a random order. In the semi-streaming model, an algorithm receives a stream of edges and it is allowed to have a memory of Õ(n)1 where n is the number of vertices in the graph. A recent inspiring work by <PERSON><PERSON><PERSON> et al. [1] shows that there exists a streaming algorithm with the approximation ratio of ⅔ that uses Õ(n1.5) memory. However, the memory of their algorithm is much larger than the memory constraint of the semi-streaming algorithms. In this work, we further investigate this problem in the semi-streaming model, and we present simple and clean algorithms for approximating maximum matching in the semi-streaming model. Our main results are as follows. We show that there exists a single-pass deterministic semi-streaming algorithm that finds a approximation of the maximum matching in bipartite graphs using Õ(n) memory. This result significantly outperforms the state-of-the-art result of <PERSON> [12] that finds a 0.539 approximation of the maximum matching using Õ(n) memory. By giving a black-box reduction from finding a matching in general graphs to finding a matching in bipartite graphs, we show there exists a single-pass deterministic semi-streaming algorithm that finds a (≈ 0.545) approximation of the maximum matching in general graphs, improving upon the state-of-art result 0.506 approximation by Gamlath et al. [8]. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.108"}, {"primary_key": "2683174", "vector": [], "sparse_vector": [], "title": "Nearly optimal edge estimation with independent set queries.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Nearly optimal edge estimation with independent set queries<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.2916 - 2935Chapter DOI:https://doi.org/10.1137/1.9781611975994.177PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the problem of estimating the number of edges of an unknown, undirected graph G = ([n], E) with access to an independent set oracle. When queried about a subset S ⊆ [n] of vertices, the independent set oracle answers whether S is an independent set in G or not. Our first main result is an algorithm that computes a (1 + ϵ)-approximation of the number of edges m of the graph using · poly(log n, 1/ϵ) independent set queries. This improves the upper bound of · poly(log n, 1/ε) by <PERSON><PERSON> et al. [3]. Our second main result shows that /polylog(n) independent set queries are necessary, thus establishing that our algorithm is optimal up to a factor of poly(log n, 1/ϵ). Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.177"}, {"primary_key": "2683175", "vector": [], "sparse_vector": [], "title": "Interleaved Caching with Access Graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Interleaved Caching with Access Graphs<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>.1846 - 1858Chapter DOI:https://doi.org/10.1137/1.9781611975994.113PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider a semi-online model for caching in which request sequences are generated by walks on a directed graph, called the access graph. The caching algorithm knows the access graph but not the actual request sequences. We then extend this model to multiple access graphs, where request sequences from the access graphs are interleaved arbitrarily and presented to the caching algorithm. For both these problems, we obtain tight upper and lower bounds on the competitive ratio; our bounds depend on a structural property of the access graph. Our work is motivated by multitasking systems with shared cache, where each task can be abstracted as a directed graph with nodes corresponding to data access and directed edges corresponding to the control flow of the task. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.113"}, {"primary_key": "2683176", "vector": [], "sparse_vector": [], "title": "A Blossom Algorithm for Maximum Edge-Disjoint T-Paths.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A Blossom Algorithm for Maximum Edge-Disjoint T-Paths<PERSON><PERSON><PERSON>wat<PERSON> and <PERSON> and <PERSON>.1933 - 1944Chapter DOI:https://doi.org/10.1137/1.9781611975994.119PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Let G = (V, E) be a multigraph with a set T ⊆ V of terminals. A path in G is called a T-path if its ends are distinct vertices in T and no internal vertices belong to T. In 1978, <PERSON><PERSON> showed a characterization of the maximum number of edge-disjoint T-paths. The original proof was not constructive, and hence it did not suggest an efficient algorithm. In this paper, we provide a combinatorial, deterministic algorithm for finding the maximum number of edge-disjoint T-paths. The algorithm adopts an augmenting path approach. More specifically, we introduce a novel concept of augmenting walks in auxiliary labeled graphs to capture a possible augmentation of the number of edge-disjoint T-paths. To design a search procedure for an augmenting walk, we introduce blossoms analogously to the blossom algorithm of <PERSON><PERSON> (1965) for the matching problem, while it is neither a special case nor a generalization of the present problem. When the search procedure terminates without finding an augmenting walk, the algorithm provides a certificate for the optimality of the current edge-disjoint T-paths. Thus the correctness argument of the algorithm serves as an alternative direct proof of Mader's theorem on edge-disjoint T-paths. The algorithm runs in O(|V| • |E|2) time, which is much faster than the best known deterministic algorithm based on a reduction to the linear matroid parity problem. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.119"}, {"primary_key": "2683178", "vector": [], "sparse_vector": [], "title": "New Algorithms and Lower Bounds for All-Pairs Max-Flow in Undirected Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)New Algorithms and Lower Bounds for All-Pairs Max-Flow in Undirected Graphs<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.48 - 61<PERSON>hapter DOI:https://doi.org/10.1137/1.9781611975994.4PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We investigate the time-complexity of the All-Pairs Max-Flow problem: Given a graph with n nodes and m edges, compute for all pairs of nodes the maximum-flow value between them. If Max-Flow (the version with a given source-sink pair s, t) can be solved in time T(m), then an O(n2) · T(m) is a trivial upper bound. But can we do better? For directed graphs, recent results in fine-grained complexity suggest that this time bound is essentially optimal. In contrast, for undirected graphs with edge capacities, a seminal algorithm of <PERSON><PERSON><PERSON> and <PERSON> (1961) runs in much faster time O(n) • T(m). Under the plausible assumption that Max-Flow can be solved in near-linear time m1+o(1), this half-century old algorithm yields an nm1+o(1) bound. Several other algorithms have been designed through the years, including Õ(mn) time for unit-capacity edges (unconditionally), but none of them break the O(mn) barrier. Meanwhile, no super-linear lower bound was shown for undirected graphs. We design the first hardness reductions for All-Pairs Max-Flow in undirected graphs, giving an essentially optimal lower bound for the node-capacities setting. For edge capacities, our efforts to prove similar lower bounds have failed, but we have discovered a surprising new algorithm that breaks the O(mn) barrier for graphs with unit-capacity edges! Assuming T(m) = m1+o(1), our algorithm runs in time m3/2+o(1) and outputs a cut-equivalent tree (similarly to the Gomory-Hu algorithm). Even with current Max-Flow algorithms we improve state-of-the-art as long as m = O(n5/3−ε). Finally, we explain the lack of lower bounds by proving a non-reducibility result. This result is based on a new quasi-linear time Õ(m) non-deterministic algorithm for constructing a cut-equivalent tree and may be of independent interest. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.4"}, {"primary_key": "2683179", "vector": [], "sparse_vector": [], "title": "A Truthful Cardinal Mechanism for One-Sided Matching.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A Truthful Cardinal Mechanism for One-Sided Matching<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.2096 - 2113Chapter DOI:https://doi.org/10.1137/1.9781611975994.129PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We revisit the well-studied problem of designing mechanisms for one-sided matching markets, where a set of n agents needs to be matched to a set of n heterogeneous items. Each agent i has a value νi,j for each item j, and these values are private information that the agents may misreport if doing so leads to a preferred outcome. Ensuring that the agents have no incentive to misreport requires a careful design of the matching mechanism, and mechanisms proposed in the literature mitigate this issue by eliciting only the ordinal preferences of the agents, i.e., their ranking of the items from most to least preferred. However, the efficiency guarantees of these mechanisms are based only on weak measures that are oblivious to the underlying values. In this paper we achieve stronger performance guarantees by introducing a mechanism that truthfully elicits the full cardinal preferences of the agents, i.e., all of the νi,j values. We evaluate the performance of this mechanism using the much more demanding Nash bargaining solution as a benchmark, and we prove that our mechanism significantly outperforms all ordinal mechanisms (even non-truthful ones). To prove our approximation bounds, we also study the population monotonicity of the Nash bargaining solution in the context of matching markets, providing both upper and lower bounds which are of independent interest. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.129"}, {"primary_key": "2683180", "vector": [], "sparse_vector": [], "title": "Faster p-norm minimizing flows, via smoothed q-norm problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Faster p-norm minimizing flows, via smoothed q-norm problemsD<PERSON>sha Adil and <PERSON><PERSON><PERSON> SachdevaDeeksha Adil and <PERSON><PERSON><PERSON>.892 - 910Chapter DOI:https://doi.org/10.1137/1.9781611975994.54PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present faster high-accuracy algorithms for computing ℓp-norm minimizing flows. On a graph with m edges, our algorithm can compute a (1 + 1/poly(m))-approximate unweighted ℓp-norm minimizing flow with operations, for any p ≥ 2, giving the best bound for all p ≳ 5.24. Combined with the algorithm from the work of <PERSON><PERSON> et al. (SODA '19), we can now compute such flows for any 2 ≤ p ≤ mo(1) in time at most O(m1.24). In comparison, the previous best running time was Ω(m1.33) for large constant p. For p ∼ σ−1 log m, our algorithm computes a (1 + σ)-approximate maximum flow on undirected graphs using m1+o(1)σ−1 operations, matching the current best bound, albeit only for unit-capacity graphs. We also give an algorithm for solving general ℓp-norm regression problems for large p. Our algorithm makes calls to a linear solver. This gives the first high-accuracy algorithm for computing weighted ℓp-norm minimizing flows that runs in time o(m1.5) for some p = mΩ(1). Our key technical contribution is to show that smoothed ℓp-norm problems introduced by Adil et al., are interreducible for different values of p. No such reduction is known for standard ℓp-norm problems. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.54"}, {"primary_key": "2683181", "vector": [], "sparse_vector": [], "title": "A Lower Bound for Jumbled Indexing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In this paper we study lower bounds for a variant of jumbled-indexing problem: given an input string S on an alphabet Σ = {σ1, . . ., σλ}, store it in a data structure such that given frequencies f1, · · ·, fλ, one can find all the substrings S0 of S where the frequency of the character σi is fi, for 1 ≤ i ≤ λ. This is a very interesting and challenging text indexing problem and it has received significant attention lately, both in the string-indexing community as well as in the theory community. It is known that when λ = 2, one can use a linear-size data structure to decide whether there exists a substring that matches the query in constant time [10]. It is also known [1] that for constant λ ≥ 3, then there exists a constant αλ such that it is not possible to achieve both O(n2−αλ) preprocessing time and O(n1−αλ) query time. We study a variant of the problem where the goal is to report all the substrings of S that match a given jumbled-indexing query. Assuming the data structure operates in the pointer machine model, we prove unconditional space lower bounds that also apply to binary alphabets: we show that if the data structure has the query time of O(n0.5−o(1) + k), where k is the output size of the query, then it must consume Ω(n2−o(1)) space. The o(1) term in our lower bound is at most log(10)1n, where log(·) notation denotes the iterative log function (i.e., log(10) n = log log log log log log log log log log n). This result has a number of interesting consequences. First, it shows that reporting all the matches is significantly harder than deciding whether a match exists, at least for the binary alphabet. This was surprising for us since we believed that in order for jumbled-indexing to be difficult, the alphabet size must be at least 3. Second, from a technical point of view, we make connections between this problem and the area of additive combinatorics as well as random walks. Previously, Chan and Lewenstein [6] had shown the connections between this problem and the area of additive combinatorics from an upper bound point of view, however, we use fundamental theorems from additive combinatorics but these tools are quite different from the theorems used in [6]. In our opinion, the fact that additive combinatorics appears in our lower bound approach as well as in the upper bound approach of Chan and Lewenstein [6] is noteworthy and demands further investigation. We believe our results open up a few new venues for research. For example, we believe it is interesting to study whether it is possible to build a data structure that uses O(n) space s.t., it can report all the matches to a jumble-indexing query in O(n2/3 + k) time; this is even interesting for a binary alphabet.", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.36"}, {"primary_key": "2683182", "vector": [], "sparse_vector": [], "title": "Oblivious S<PERSON>ching of High-Degree Polynomial Kernels.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Kernel methods are fundamental tools in machine learning that allow detection of non-linear dependencies between data without explicitly constructing feature vectors in high dimensional spaces. A major disadvantage of kernel methods is their poor scalability: primitives such as kernel PCA or kernel ridge regression generally take prohibitively large quadratic space and (at least) quadratic time, as kernel matrices are usually dense. Some methods for speeding up kernel linear algebra are known, but they all invariably take time exponential in either the dimension of the input point set (e.g., fast multipole methods suffer from the curse of dimensionality) or in the degree of the kernel function.Oblivious sketching has emerged as a powerful approach to speeding up numerical linear algebra over the past decade, but our understanding of oblivious sketching solutions for kernel matrices has remained quite limited, suffering from the aforementioned exponential dependence on input parameters. Our main contribution is a general method for applying sketching solutions developed in numerical linear algebra over the past decade to a tensoring of data points without forming the tensoring explicitly. This leads to the first oblivious sketch for the polynomial kernel with a target dimension that is only polynomially dependent on the degree of the kernel function, as well as the first oblivious sketch for the Gaussian kernel on bounded datasets that does not suffer from an exponential dependence on the dimensionality of input data points.", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.9"}, {"primary_key": "2683183", "vector": [], "sparse_vector": [], "title": "An almost 2-approximation for all-pairs of shortest paths in subquadratic time.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)An almost 2-approximation for all-pairs of shortest paths in subquadratic time<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.1 - 11Chapter DOI:https://doi.org/10.1137/1.9781611975994.1PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Let G = (V, E) be an unweighted undirected graph with n vertices and m edges. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> [FOCS 1996, SICOMP 2000] presented an Õ(n2)-time algorithm that computes estimated distances with a multiplicative approximation of 3. <PERSON><PERSON> and <PERSON><PERSON><PERSON> [WADS 2007] improved the approximation of <PERSON><PERSON> et al. and presented an Õ(n2)-time algorithm that produces for every u, v ϵ V an estimate (u, v) such that: dG(u, v) ≤ (u, v) ≤ 2dG(u, v) + 1. We refer to such an approximation as an (α, β)-approximation, where a is the multiplicative approximation and β is the additive approximation. A prerequisite for an O(n2−ε)-time algorithm, where ε ϵ (0, 1), is a data structure that uses O(n2−δ) space, for some δ ≥ ε, and answers queries in constant time. An O(n2−ε)-time (3, 0)-approximation algorithm became plausible after Thorup and Zwick [STOC 2001, JACM 2005] presented their approximate distance oracles, and in particular an O(n1.5)-space data structure that reports a (3, 0)-approximate distance in O(1) time. Indeed, using Thorup and Zwick distance oracles together with more ideas, Baswana, Gaur, Sen, and Upadhyay [ICALP 2008] improved the running time of Dor et al., and obtained an O(n2−ε) time algorithm, at the cost of introducing also an additive approximation. They presented an algorithm that in Õ(m + n23/12) expected running time constructs an O(n1.5)-space data structure, that in O(1) time reports a (3, 14)-approximate distance. An O(n2−ε)-time (2, 1)-approximation algorithm became plausible only after Pǎtraşcu and Roditty [FOCS 2010, SICOMP 2014] presented an O(n5/3)-space data structure that reports (2, 1)-approximate distances in O(1) time. However, only few years ago, Sommer [ICALP 2016] obtained an Õ(n2) time algorithm that computes a (2, 1)-distance oracle with Õ(n5/3) space. This leads to the following natural question of whether Ω(n2) time is a lower bound for any (3−α, β)-approximation, where α ϵ (0, 1), and β is constant. In this paper we show that this is not the case by presenting an algorithm that for every ε ϵ (0, 1/2) computes in Õ(m) + n2−Ω(ε) time an -space data structure that in O(1/ε) time reports, for every u, v ϵ V, an estimate (u, v) such that: Our result improves, simultaneously, the running time and the multiplicative approximation of the Õ(n2)-time (3, 0)-approximation algorithm of Dor et al. at the cost of introducing also an additive approximation. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.1"}, {"primary_key": "2683184", "vector": [], "sparse_vector": [], "title": "Regular Languages meet Prefix Sorting.", "authors": ["<PERSON><PERSON><PERSON>", "Giovanna D&apos;Agostino", "<PERSON>", "<PERSON>"], "summary": "Indexing strings via prefix (or suffix) sorting is, arguably, one of the most successful algorithmic techniques developed in the last decades. Can indexing be extended to languages? The main contribution of this paper is to initiate the study of the sub-class of regular languages accepted by an automaton whose states can be prefix-sorted. Starting from the recent notion of Wheeler graph [<PERSON><PERSON><PERSON> et al., TCS 2017]-which extends naturally the concept of prefix sorting to labeled graphs-we investigate the properties of Wheeler languages, that is, regular languages admitting an accepting Wheeler finite automaton. Interestingly, we characterize this family as the natural extension of regular languages endowed with the co-lexicographic ordering: when sorted, the strings belonging to a Wheeler language are partitioned into a finite number of co-lexicographic intervals, each formed by elements from a single Myhill-Nerode equivalence class. Moreover: (i) We show that every Wheeler NFA (WNFA) with $n$ states admits an equivalent Wheeler DFA (WDFA) with at most $2n-1-|\\Sigma|$ states that can be computed in $O(n^3)$ time. This is in sharp contrast with general NFAs. (ii) We describe a quadratic algorithm to prefix-sort a proper superset of the WDFAs, a $O(n\\log n)$-time online algorithm to sort acyclic WDFAs, and an optimal linear-time offline algorithm to sort general WDFAs. By contribution (i), our algorithms can also be used to index any WNFA at the moderate price of doubling the automaton's size. (iii) We provide a minimization theorem that characterizes the smallest WDFA recognizing the same language of any input WDFA. The corresponding constructive algorithm runs in optimal linear time in the acyclic case, and in $O(n\\log n)$ time in the general case. (iv) We show how to compute the smallest WDFA equivalent to any acyclic DFA in nearly-optimal time.", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.55"}, {"primary_key": "2683185", "vector": [], "sparse_vector": [], "title": "List Decoding of Direct Sum Codes.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)List Decoding of Direct Sum CodesV<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>.1412 - 1425Chapter DOI:https://doi.org/10.1137/1.9781611975994.85PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider families of codes obtained by \"lifting\" a base code through operations such as k-XOR applied to \"local views\" of codewords of , according to a suitable k-uniform hypergraph. The k-XOR operation yields the direct sum encoding used in works of [Ta-Shma, STOC 2017] and [<PERSON><PERSON> and <PERSON>, FOCS 2017]. We give a general framework for list decoding such lifted codes, as long as the base code admits a unique decoding algorithm, and the hypergraph used for lifting satisfies certain expansion properties. We show that these properties are indeed satisfied by the collection of length k walks on a sufficiently strong expanding graph, and by hypergraphs corresponding to high-dimensional expanders. Instantiating our framework, we obtain list decoding algorithms for direct sum liftings corresponding to the above hypergraph families. Using known connections between direct sum and direct product, we also recover (and strengthen) the recent results of Dinur et al. [SODA 2019] on list decoding for direct product liftings. Our framework relies on relaxations given by the Sum-of-Squares (SOS) SDP hierarchy for solving various constraint satisfaction problems (CSPs). We view the problem of recovering the closest codeword to a given (possibly corrupted) word, as finding the optimal solution to an instance of a CSP. Constraints in the instance correspond to edges of the lifting hypergraph, and the solutions are restricted to lie in the base code . We show that recent algorithms for (approximately) solving CSPs on certain expanding hypergraphs by some of the authors also yield a decoding algorithm for such lifted codes. We extend the framework to list decoding, by requiring the SOS solution to minimize a convex proxy for negative entropy. We show that this ensures a covering property for the SOS solution, and the \"condition and round\" approach used in several SOS algorithms can then be used to recover the required list of codewords. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.85"}, {"primary_key": "2683186", "vector": [], "sparse_vector": [], "title": "Faster Deterministic and Las Vegas Algorithms for Offline Approximate Nearest Neighbors in High Dimensions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Faster Deterministic and Las Vegas Algorithms for Offline Approximate Nearest Neighbors in High DimensionsJosh Alman, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>pp.637 - 649Chapter DOI:https://doi.org/10.1137/1.9781611975994.39PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present a deterministic, truly subquadratic algorithm for offline (1 + ε)-approximate nearest or farthest neighbor search (in particular, the closest pair or diameter problem) in Hamming space in any dimension d ≤ nδ, for a sufficiently small constant δ > 0. The running time of the algorithm is roughly for nearest neighbors, or for farthest. The algorithm follows from a simple combination of expander walks, Chebyshev polynomials, and rectangular matrix multiplication. We also show how to eliminate errors in the previous Monte Carlo randomized algorithm of <PERSON><PERSON>, <PERSON>, and <PERSON> [FOCS'16] for offline approximate nearest or farthest neighbors, and obtain a Las Vegas randomized algorithm with expected running time . Finally, we note a simplification of <PERSON><PERSON>, <PERSON>, and <PERSON>' method and obtain a slightly improved <PERSON> Carlo randomized algorithm with running time . As one application, we obtain improved deterministic and randomized (1 + ε)-approximation algorithms for MAX-SAT. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.39"}, {"primary_key": "2683187", "vector": [], "sparse_vector": [], "title": "Faster Update Time for Turnstile Streaming Algorithms.", "authors": ["<PERSON>", "Huacheng Yu"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Faster Update Time for Turnstile Streaming AlgorithmsJosh Alman and Huacheng YuJosh Alman and Huacheng Yupp.1803 - 1813Chapter DOI:https://doi.org/10.1137/1.9781611975994.110PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper, we present a new algorithm for maintaining linear sketches in turnstile streams with faster update time. As an application, we show that log n Count sketches or CountMin sketches with a constant number of columns (i.e., buckets) can be implicitly maintained in worst-case O(log0.582 n) update time using O(log n) words of space, on a standard word RAM with word-size w = Θ(log n). The exponent 0.582 ≈ 2ω/3 – 1, where ω is the current matrix multiplication exponent. Due to the numerous applications of linear sketches, our algorithm improves the update time for many streaming problems in turnstile streams, in the high success probability setting, without using more space, including ℓ2 norm estimation, ℓ2 heavy hitters, point query with ℓ1 or ℓ2 error, etc. Our algorithm generalizes, with the same update time and space, to maintaining log n linear sketches, where each sketch partitions the coordinates into k 2. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.110"}, {"primary_key": "2683188", "vector": [], "sparse_vector": [], "title": "Efficiency of the floating body as a robust measure of dispersion.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Efficiency of the floating body as a robust measure of dispersion<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON>.364 - 377Chapter DOI:https://doi.org/10.1137/1.9781611975994.22PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Among robust notions of shape, depth and dispersion of a distribution or dataset we have Tukey depth and depth curves, which are essentially the same as the convex floating body in convex geometry. These notions are important because they play the role of multidimensional quantiles and rank statistics. At the same time, they can be difficult to use because they are computationally intractable in general. We develop a theory of algorithmic efficiency for these notions for several broad and relevant families of distributions: symmetric log-concave distributions and certain multivariate stable distributions and power-law distributions. As an example of the power of these results, we show how to solve the Independent Component Analysis problem for power-law distributions, even when the first moment is infinite. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.22"}, {"primary_key": "2683189", "vector": [], "sparse_vector": [], "title": "Parallel Machine Scheduling to Minimize Energy Consumption.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Parallel Machine Scheduling to Minimize Energy Consumption<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.2758 - 2769Chapter DOI:https://doi.org/10.1137/1.9781611975994.168PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given n jobs with release dates, deadlines and processing times we consider the problem of scheduling them on m parallel machines so as to minimize the total energy consumed. Machines can enter a sleep state and they consume no energy in this state. Each machine requires L units of energy to awaken from the sleep state and in its active state the machine can process jobs and consumes a unit of energy per unit time. We allow for preemption and migration of jobs and provide the first constant approximation algorithm for this problem. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.168"}, {"primary_key": "2683190", "vector": [], "sparse_vector": [], "title": "Chasing Convex Bodies with Linear Competitive Ratio.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Chasing Convex Bodies with Linear Competitive RatioC.J<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.1519 - 1524Chapter DOI:https://doi.org/10.1137/1.9781611975994.93PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the problem of chasing convex bodies online: given a sequence of convex bodies Kt ⊆ ℝd the algorithm must respond with points xt ϵ Kt in an on-line fashion (i.e., xt is chosen before Kt+1 is revealed). The objective is to minimize the total distance between successive points in this sequence. Recently, <PERSON><PERSON><PERSON> et al. (STOC 2019) gave a 2O(d)-competitive algorithm for this problem. We give an algorithm that is -competitive for any sequence of length T. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.93"}, {"primary_key": "2683191", "vector": [], "sparse_vector": [], "title": "Optimal Bound on the Combinatorial Complexity of Approximating Polytopes.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Fonseca", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Optimal Bound on the Combinatorial Complexity of Approximating Polytopes<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON>, and <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>.786 - 805Chapter DOI:https://doi.org/10.1137/1.9781611975994.48PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Convex bodies play a fundamental role in geometric computation, and approximating such bodies is often a key ingredient in the design of efficient algorithms. We consider the question of how to succinctly approximate a multidimensional convex body by a polytope. We are given a convex body K of unit diameter in Euclidean d-dimensional space (where d is a constant) along with an error parameter ε > 0. The objective is to determine a polytope of low combinatorial complexity whose Hausdorff distance from K is at most e. By combinatorial complexity we mean the total number of faces of all dimensions of the polytope. In the mid-1970's, a result by <PERSON> showed that O(1/ε(d–1)/2) facets suffice, and <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> presented a similar bound on the number of vertices. While both results match known worst-case lower bounds, obtaining a similar upper bound on the total combinatorial complexity has been open for over 40 years. Recently, we made a first step forward towards this objective, obtaining a suboptimal bound. In this paper, we settle this problem with an asymptotically optimal bound of O(1/ε(d–1)/2). Our result is based on a new relationship between ε-width caps of a convex body and its polar. Using this relationship, we are able to obtain a volume-sensitive bound on the number of approximating caps that are \"essentially different.\" We achieve our result by combining this with a variant of the witness-collector method and a novel variable-width layered construction. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.48"}, {"primary_key": "2683193", "vector": [], "sparse_vector": [], "title": "Shortest Paths in a Hybrid Network Model.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Shortest Paths in a Hybrid Network Model<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>.1280 - 1299Chapter DOI:https://doi.org/10.1137/1.9781611975994.78PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We introduce a communication model for hybrid networks, where nodes have access to two different communication modes: a local mode where (like in traditional networks) communication is only possible between specific pairs of nodes, and a global mode where (like in overlay networks) communication between any pair of nodes is possible. Typically, communication over short-range connections is cheaper and can be done at a much higher rate than communication via the overlay network. Therefore, we are focusing on the LOCAL model for the local connections where nodes can exchange an unbounded amount of information per round. For the global communication we assume the so-called nodecapacitated clique model, where in each round every node can exchange O(log n)-bit messages with O(log n) arbitrary nodes. We explore the impact of hybrid communication on the complexity of distributed algorithms by studying the problem of computing shortest paths in the graph given by the local connections. We present the following results. For the all-pairs shortest paths problem, we show that an exact solution can be computed in time Õ (n2/3), and that approximate solutions can be computed in time but not faster. For the single-source shortest paths problem an exact solution can be computed in time , where SPD denotes the shortest path diameter. Furthermore, a (l + o(1))-approximate solution can be computed in time . Finally, we show that for every constant ε > 0, it is possible to compute an O(1)-approximate solution in time . Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.78"}, {"primary_key": "2683194", "vector": [], "sparse_vector": [], "title": "Improved Inapproximability of Rainbow Coloring.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Improved Inapproximability of Rainbow ColoringPer <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> Au<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1479 - 1495Chapter DOI:https://doi.org/10.1137/1.9781611975994.90PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A rainbow q-coloring of a k-uniform hypergraph is a q-coloring of the vertex set such that every hyperedge contains all q colors. We prove that given a rainbow -colorable k-uniform hypergraph, it is NP-hard to find a normal 2-coloring. Previously, this was only known for rainbow -colorable hypergraphs (<PERSON><PERSON><PERSON> and <PERSON>, SODA 2015). We also study a generalization which we call rainbow (q, p)-coloring, defined as a coloring using q colors such that every hyperedge contains at least p colors. We prove that given a rainbow -colorable k uniform hypergraph, it is NP-hard to find a normal c-coloring for any c = o(k). The proof of our second result relies on two combinatorial theorems. One of the theorems was proved by <PERSON><PERSON><PERSON> (<PERSON><PERSON>, Ser. B 1990) using topological methods and the other theorem we prove using a generalized Borsuk-Ulam theorem. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.90"}, {"primary_key": "2683195", "vector": [], "sparse_vector": [], "title": "Near-optimal Approximate Discrete and Continuous Submodular Function Minimization.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Near-optimal Approximate Discrete and Continuous Submodular Function Minimization<PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.837 - 853Chapter DOI:https://doi.org/10.1137/1.9781611975994.51PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper we provide improved running times and oracle complexities for approximately minimizing a submodular function. Our main result is a randomized algorithm, which given any submodular function defined on n-elements with range [–1, 1], computes an ε-additive approximate minimizer in Õ(n/ε2) oracle evaluations with high probability. This improves over the Õ(n5/3/ε2) oracle evaluation algorithm of <PERSON><PERSON><PERSON><PERSON> et al. (STOC 2017) and the Õ(n3/2/ε2) oracle evaluation algorithm of <PERSON><PERSON><PERSON> et al… Further, we leverage a generalization of this result to obtain efficient algorithms for minimizing a broad class of nonconvex functions. For any function f with domain [0, 1]n that satisfies for all i ≠ j and is L-Lipschitz with respect to the L∞-norm we give an algorithm that computes an ε-additive approximate minimizer with Õ(n · poly(L/ε) function evaluation with high probability. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.51"}, {"primary_key": "2683196", "vector": [], "sparse_vector": [], "title": "Bulow-K<PERSON>perer-Style Results for Welfare Maximization in Two-Sided Markets.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Yannai <PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Bulow-Klemperer-Style Results for Welfare Maximization in Two-Sided Markets<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>pp.2452 - 2471Chapter DOI:https://doi.org/10.1137/1.9781611975994.150PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the problem of welfare (and gains-from-trade) maximization in two-sided markets using simple mechanisms that are prior-independent. The seminal impossibility result of <PERSON><PERSON> and <PERSON> [1983] shows that even for bilateral trade, there is no feasible (individually rational, truthful, and budget balanced) mechanism that has welfare as high as the optimal-yet-infeasible VCG mechanism, which attains maximal welfare but runs a deficit. On the other hand, the optimal feasible mechanism needs to be carefully tailored to the Bayesian prior, and even worse, it is known to be extremely complex, eluding a precise description. In this paper we present Bulow-Klemperer-style results to circumvent these hurdles in double-auction market settings. We suggest using the Buyer Trade Reduction (BTR) mechanism, a variant of McAfee's mechanism, which is feasible and simple (in particular, it is deterministic, truthful, prior-independent, and anonymous). First, in the setting in which the values of the buyers and of the sellers are sampled independently and identically from the same distribution, we show that for any such market of any size, BTR with one additional buyer whose value is sampled from the same distribution has expected welfare at least as high as the optimal-yet-infeasible VCG mechanism in the original market. We then move to a more general setting in which the values of the buyers are sampled from one distribution, and those of the sellers from another, focusing on the case where the buyers' distribution first-order stochastically dominates the sellers' distribution. We present both upper bounds and lower bounds on the number of buyers that, when added, guarantees that BTR in the augmented market achieve welfare at least as high as the optimal in the original market. Our lower bounds extend to a large class of mechanisms, and all of our positive and negative results extend to adding sellers instead of buyers. In addition, we present positive results about the usefulness of pricing at a sample for welfare maximization (and more precisely, for gains-from-trade approximation) in two-sided markets under the above two settings, which to the best of our knowledge are the first sampling results in this context. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.150"}, {"primary_key": "2683197", "vector": [], "sparse_vector": [], "title": "Hierarchical Shape Construction and Complexity for Slidable Polyominoes under Uniform External Forces.", "authors": ["<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Hierarchical Shape Construction and Complexity for Slidable Polyominoes under Uniform External Forces<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.2625 - 2641Chapter DOI:https://doi.org/10.1137/1.9781611975994.160PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Advances in technology have given us the ability to create and manipulate robots for numerous applications at the molecular scale. At this size, fabrication tool limitations motivate the use of simple robots. The individual control of these simple objects can be infeasible. We investigate a model of robot motion planning, based on global external signals, known as the tilt model. Given a board and initial placement of polyominoes, the board may be tilted in any of the 4 cardinal directions, causing all slidable polyominoes to move maximally in the specified direction until blocked. We propose a new hierarchy of shapes and design a single configuration that is strongly universal for any w × h bounded shape within this hierarchy (it can be reconfigured to construct any w × h bounded shape in the hierarchy). This class of shapes constitutes the most general set of buildable shapes in the literature, with most previous work consisting of just the first-level of our hierarchy. We accompany this result with a O(n4 log n)-time algorithm for deciding if a given hole-free shape is a member of the hierarchy. For our second result, we resolve a long-standing open problem within the field: We show that deciding if a given position may be covered by a tile for a given initial board configuration is PSPACEcomplete, even when all movable pieces are 1 × 1 tiles with no glues. We achieve this result by a reduction from Non-deterministic Constraint Logic for a one-player unbounded game. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.160"}, {"primary_key": "2683199", "vector": [], "sparse_vector": [], "title": "Online Probabilistic Metric Embedding: A General Framework for Bypassing Inherent Bounds.", "authors": ["<PERSON><PERSON>", "Nova Fandina", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Online Probabilistic Metric Embedding: A General Framework for Bypassing Inherent <PERSON><PERSON><PERSON><PERSON>, <PERSON> Fandina, and <PERSON><PERSON>, <PERSON> Fandina, and <PERSON><PERSON>.1538 - 1557Chapter DOI:https://doi.org/10.1137/1.9781611975994.95PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Probabilistic metric embedding into trees is a powerful technique for designing online algorithms. The standard approach is to embed the entire underlying metric into a tree metric and then solve the problem on the latter. The overhead in the competitive ratio depends on the expected distortion of the embedding, which is logarithmic in n, the size of the underlying metric. For many online applications, such as online network design problems, it is natural to ask if it is possible to construct such embeddings in an online fashion such that the distortion would be a polylogarithmic function of k, the number of terminals. Our first main contribution is answering this question negatively, exhibiting a lower bound of (log k log ɸ), where ɸ is the aspect ratio of the set of terminals, showing that a simple modification of the probabilistic embedding into trees of Bartal (F<PERSON><PERSON> 1996), which has expected distortion of O(log k log ɸ), is nearly-tight. Unfortunately, this may result in a very bad (polynomial) dependence in terms of k. Our second main contribution is a general framework for bypassing this limitation. We show that for a large class of online problems this online probabilistic embedding can still be used to devise an algorithm with O(min{log k log(kλ), log3 k}) overhead in the competitive ratio, where k is the current number of terminals, and λ is a measure of subadditivity of the cost function, which is at most r, the current number of requests. In particular, this implies the first algorithms with competitive ratio polylog(k) for online subadditive network design (buy-at-bulk network design being a special case), and polylog(k, r) for online group Steiner forest. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.95"}, {"primary_key": "2683200", "vector": [], "sparse_vector": [], "title": "A complexity dichotomy for hitting connected minors on bounded treewidth graphs: the chair and the banner draw the boundary.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Dimitrios M. Thilikos"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A complexity dichotomy for hitting connected minors on bounded treewidth graphs: the chair and the banner draw the boundary<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>pp.951 - 970Chapter DOI:https://doi.org/10.1137/1.9781611975994.57PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract For a fixed connected graph H, the {H}-M-Deletion problem asks, given a graph G, for the minimum number of vertices that intersect all minor models of H in G. It is known that this problem can be solved in time f (tw) · n(1), where tw is the treewidth of G. We determine the asymptotically optimal function f(tw), for each possible choice of H. Namely, we prove that, under the ETH, f(tw) = 2Θ(tw) if H is a contraction of the chair or the banner, and f (tw) = 2Θ(tw·log tw) otherwise. Prior to this work, such a complete characterization was only known when H is a planar graph with at most five vertices. For the upper bounds, we present an algorithm in time 2Θ(tw·log tw)·n(1) for the more general problem where all minor models of connected graphs in a finite family need to be hit. We combine several ingredients such as the machinery of boundaried graphs in dynamic programming via representatives, the Flat Wall Theorem, Bidimensionality, the irrelevant vertex technique, treewidth modulators, and protrusion replacement. In particular, this algorithm vastly generalizes a result of Jansen et al. [SODA 2014] for the particular case = {K5, K3,3}. For the lower bounds, our reductions are based on a generic construction building on the one given by the authors in [IPEC 2018], which uses the framework introduced by Lokshtanov et al. [SODA 2011] to obtain superexponential lower bounds. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.57"}, {"primary_key": "2683201", "vector": [], "sparse_vector": [], "title": "Finding a Bounded-Degree Expander Inside a Dense One.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Finding a Bounded-Degree Expander Inside a Dense One<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>.1320 - 1336Chapter DOI:https://doi.org/10.1137/1.9781611975994.80PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract It follows from the Marcus-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> proof of the Kadison-Singer conjecture that if G = (V, E) is a Δ-regular dense expander then there is an edge-induced subgraph H = (V, Eh) of G of constant maximum degree which is also an expander. As with other consequences of the MSS theorem, it is not clear how one would explicitly construct such a subgraph. We show that such a subgraph (although with quantitatively weaker expansion and near-regularity properties than those predicted by MSS) can be constructed with high probability in linear time, via a simple algorithm. Our algorithm allows a distributed implementation that runs in O(log n) rounds and does O(n) total work with high probability. The analysis of the algorithm is complicated by the complex dependencies that arise between edges and between choices made in different rounds. We sidestep these difficulties by following the combinatorial approach of counting the number of possible random choices of the algorithm which lead to failure. We do so by a compression argument showing that such random choices can be encoded with a non-trivial compression. Our algorithm bears some similarity to the way agents construct a communication graph in a peer-to-peer network, and, in the bipartite case, to the way agents select servers in blockchain protocols. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.80"}, {"primary_key": "2683202", "vector": [], "sparse_vector": [], "title": "Fully Dynamic Matching: Beating 2-Approximation in Δϵ Update Time.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Fully Dynamic Matching: Beating 2-Approximation in Δϵ Update Time<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.2492 - 2508Chapter DOI:https://doi.org/10.1137/1.9781611975994.152PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In fully dynamic graphs, we know how to maintain a 2-approximation of maximum matching extremely fast, that is, in polylogarithmic update time or better. In a sharp contrast and despite extensive studies, all known algorithms that maintain a 2 – Ω(1) approximate matching are much slower. Understanding this gap and, in particular, determining the best possible update time for algorithms providing a better-than-2 approximate matching is a major open question. In this paper, we show that for any constant ϵ > 0, there is a randomized algorithm that with high probability maintains a 2 – Ω(1) approximate maximum matching of a fully-dynamic general graph in worst-case update time O(Δϵ + polylog n), where Δ is the maximum degree. Previously, the fastest fully dynamic matching algorithm providing a better-than-2 approximation had O(m1/4) update-time [<PERSON> and <PERSON>, SODA 2016]. A faster algorithm with update-time O(nϵ) was known, but worked only for maintaining the size (and not the edges) of the matching in bipartite graphs [Bhattacharya, Henzinger, and <PERSON>ongkai, STOC 2016]. Previous chapter Next chapter RelatedDetails Published:2020eISB<PERSON>:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.152"}, {"primary_key": "2683203", "vector": [], "sparse_vector": [], "title": "Cake Cutting on Graphs: A Discrete and Bounded Proportional Protocol.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Cake Cutting on Graphs: A Discrete and Bounded Proportional Protocol<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>.2114 - 2123Chapter DOI:https://doi.org/10.1137/1.9781611975994.130PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The classical cake cutting problem studies how to find fair allocations of a heterogeneous and divisible resource among multiple agents. Two of the most commonly studied fairness concepts in cake cutting are proportionality and envy-freeness. It is well known that a proportional allocation among n agents can be found efficiently via simple protocols [16]. For envy-freeness, in a recent breakthrough, <PERSON> and <PERSON> [5] proposed a discrete and bounded envy-free protocol for any number of players. However, the protocol suffers from high multiple-exponential query complexity and it remains open to find simpler and more efficient envy-free protocols. In this paper we consider a variation of the cake cutting problem by assuming an underlying graph over the agents whose edges describe their acquaintance relationships, and agents evaluate their shares relatively to those of their neighbors. An allocation is called locally proportional if each agent thinks she receives at least the average value over her neighbors. Local proportionality generalizes proportionality and is in an interesting middle ground between proportionality and envy-freeness: its existence is guaranteed by that of an envy-free allocation, but no simple protocol is known to produce such a locally proportional allocation for general graphs. Previous works showed locally proportional protocols for special classes of graphs, and it is listed in both [1] and [8] as an open question to design simple locally proportional protocols for more general classes of graphs. In this paper we completely resolved this open question by presenting a discrete and bounded locally proportional protocol for any given graph. Our protocol has a query complexity of only single exponential, which is significantly smaller than the six towers of n query complexity of the envy-free protocol given in [5]. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.130"}, {"primary_key": "2683204", "vector": [], "sparse_vector": [], "title": "Testing convexity of functions over finite domains.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "We establish new upper and lower bounds on the number of queries required to test convexity of functions over various discrete domains.1.We provide a simplified version of the non-adaptive convexity tester on the line. We re-prove the upper bound in the usual uniform model, and prove an upper bound in the distribution-free setting.2.We show a tight lower bound of queries for testing convexity of functions f: [n] → ℝ on the line. This lower bound applies to both adaptive and non-adaptive algorithms, and matches the upper bound from item 1, showing that adaptivity does not help in this setting.3.Moving to higher dimensions, we consider the case of a stripe [3] × [n]. We construct an adaptive tester for convexity of functions f: [3] × [n] → ℝ with query complexity O(log2 n). We also show that any non-adaptive tester must use queries in this setting. Thus, adaptivity yields an exponential improvement for this problem.4.For functions f: [n]d → ℝ over domains of dimension d ≥ 2, we show a non-adaptive query lower bound .", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.125"}, {"primary_key": "2683205", "vector": [], "sparse_vector": [], "title": "Very fast construction of bounded-degree spanning graphs via the semi-random graph process.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Very fast construction of bounded-degree spanning graphs via the semi-random graph process<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>.718 - 737Chapter DOI:https://doi.org/10.1137/1.9781611975994.44PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Semi-random processes involve an adaptive decision-maker, whose goal is to achieve some predetermined objective in an online randomized environment. They have algorithmic implications in various areas of computer science, as well as connections to biological processes involving decision making. In this paper, we consider a recently proposed semi-random graph process, defined as follows: we start with an empty graph on n vertices, and in each round, the decision-maker, called <PERSON><PERSON><PERSON>, receives a uniformly random vertex v, and must immediately (in an online manner) choose another vertex u, adding the edge {u, v} to the graph. <PERSON><PERSON><PERSON>'s end goal is to make the constructed graph satisfy some predetermined monotone graph property. There are also natural offline and non-adaptive modifications of this setting. We consider the property H of containing a spanning graph H as a subgraph. It was asked by <PERSON><PERSON> whether for every bounded-degree H, <PERSON><PERSON><PERSON> can construct a graph satisfying H with high probability in O(n) rounds. We answer this question positively in a strong sense, showing that any graph with maximum degree Δ can be constructed with high probability in (3Δ/2 + o(Δ))n rounds, where the o(Δ) term tends to zero as Δ → ∞. This is tight (even for the offline case) up to a multiplicative factor of 3 + oΔ(1). Furthermore, for the special case where H is a forest of maximum degree Δ, we show that H can be constructed with high probability in O(log Δ)n rounds. This is tight up to a multiplicative constant, even for the offline setting. Finally, we show a separation between adaptive and non-adaptive strategies, proving a lower bound of on the number of rounds necessary to eliminate all isolated vertices w.h.p. using a non-adaptive strategy. This bound is tight, and in fact there are non-adaptive strategies for constructing a Hamilton cycle or a Kr-factor, which are successful w.h.p. within rounds. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.44"}, {"primary_key": "2683206", "vector": [], "sparse_vector": [], "title": "New (α, β) Spanners and Hopsets.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)New (α, β) Spanners and Hopsets<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and Merav Parterpp.1695 - 1714Chapter DOI:https://doi.org/10.1137/1.9781611975994.104PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract An f (d)-spanner of an unweighted n-vertex graph G = (V, E) is a subgraph H satisfying that distH(u, v) is at most f (distG (u, v)) for every u, v ϵ V. A simple girth argument implies that any f (d)-spanner with O(n1+1/k) edges must satisfy that f (d) / d = Ω(⌈k/d⌉). A matching upper bound (even up to constants) for super-constant values of d is currently known only for d = Ω((logk)log k) as given by the well known (1 + ε, β) spanners of Elkin and Peleg, and its recent improvements by [<PERSON><PERSON><PERSON>, SODA'17], and [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SODA'18]. We present new spanner constructions that achieve a nearly optimal stretch of O(⌈k/d⌉) for any distance value d ϵ [1, k1−o(1)] and d ≥ k1+o(1). We also show more optimized spanner constructions with nearly linear number of edges. Specifically, for every ε ϵ (0, 1), we show the construction of (3 + ε, β) spanners for β = Oε (klog(3+8/ε)) with Õε (n) edges. In addition, we consider the related graph concept of hopsets introduced by [Cohen, J. ACM '00]. Informally, an hopset H is a weighted edge set that, when added to the graph G, allows one to get a path from each node u to a node v with at most β hops (i.e., edges) and length at most α · distG (u, v). We present a new family of (α, β) hopsets with Õ(k · n1+1/k) edges and α · β = O(k). Turning to nearly linear-size hopsets, we show a construction of (3 + ε, β) hopset with Õε(n) edges and hop-bound of β = Oε ((log n)log(3+9/ε)), improving upon the state-of-the-art hop-bound of β = O(log log n)log log n. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.104"}, {"primary_key": "2683207", "vector": [], "sparse_vector": [], "title": "Flushing Without Cascades.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Flushing Without Cascades<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>.650 - 669Chapter DOI:https://doi.org/10.1137/1.9781611975994.40PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Buffer-and-flush is a technique for transforming standard external-memory search trees into write-optimized search trees. In exchange for faster amortized insertions, buffer-and-flush can sometimes significantly increase the latency of operations by causing cascades of flushes. In this paper, we show that flushing cascades are not a fundamental consequence of the buffer-flushing technique, and can be removed entirely using randomization techniques. The underlying implementation of buffer flushing relies on a buffer-eviction strategy at each node in the tree. The ability for the user to select the buffer eviction strategy based on the workload has been shown to be important for performance, both in theory and in practice. In order to support arbitrary buffer-eviction strategies, we introduce the notion of a universal flush, which uses a universal eviction policy that can simulate any other eviction policy. This abstracts away the underlying eviction strategy, even allowing for workload-specific strategies that change dynamically. Our deamortization preserves the amortized throughput of the underlying flushing strategy on all workloads. In particular, with our deamortization and a node cache of size poly-logarithmic in the number of insertions performed on the tree, the amortized insertion cost matches the lower bound of Brodal and Fagerberg. For typical parameters, the lower bound is less than 1 I/O per insertion. For such parameters, our worst-case insertion cost is O(1) I/Os. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.40"}, {"primary_key": "2683208", "vector": [], "sparse_vector": [], "title": "Reconstruction of Depth-4 Multilinear Circuits.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Reconstruction of Depth-4 Multilinear Circuits<PERSON><PERSON><PERSON> Bhargava, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> Bhargava, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>.2144 - 2160Chapter DOI:https://doi.org/10.1137/1.9781611975994.132PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present a deterministic algorithm for reconstructing multilinear ƩпƩп(k) circuits, i.e. multilinear depth-4 circuits with fan-in k at the top + gate. For any fixed k, given black-box access to a polynomial f ϵ 픽[x1, x2, …, xn] computable by a multilinear ƩпƩп(k) circuit of size s, the algorithm runs in time quasi-poly(n, s, |픽|) and outputs a multilinear ƩпƩп(k) circuit of size quasi-poly(n, s) that computes f. Our result solves an open problem posed in [15] (STOC, 2012). Indeed, prior to our work, efficient reconstruction algorithms for multilinear ƩпƩп(k) circuits were known only for the case of k = 2 [15, 52]. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.132"}, {"primary_key": "2683209", "vector": [], "sparse_vector": [], "title": "An Improved Algorithm for Incremental Cycle Detection and Topological Ordering in Sparse Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)An Improved Algorithm for Incremental Cycle Detection and Topological Ordering in Sparse Graphs<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.2509 - 2521Chapter DOI:https://doi.org/10.1137/1.9781611975994.153PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the problem of incremental cycle detection and topological ordering in a directed graph G = (V, E) with |V| = n nodes. In this setting, initially the edge-set E of the graph is empty. Subsequently, at each time-step an edge gets inserted into G. After every edge-insertion, we have to report if the current graph contains a cycle, and as long as the graph remains acyclic, we have to maintain a topological ordering of the node-set V. Let m be the total number of edges that get inserted into G. We present a randomized algorithm for this problem with Õ(m4/3) total expected update time. Our result improves the Õ(m • min(m1/2, n2/3)) total update time bound of [5, 9, 10, 7]. In particular, for m = O(n), our result breaks the longstanding barrier on the total update time. Furthermore, whenever m = o(n3/2), our result improves upon the recently obtained total update time bound of [6]. We note that if m = Ω(n3/2), then the algorithm of [5, 4, 7], which has Õ(n2) total update time, beats the performance of the time algorithm of [6]. It follows that we improve upon the total update time of the algorithm of [6] in the \"interesting\" range of sparsity where m = o(n3/2). Our result also happens to be the first one that breaks the lower bound of [9] on the total update time of any local algorithm for a nontrivial range of sparsity. Specifically, the total update time of our algorithm is whenever . From a technical perspective, we obtain our result by combining the algorithm of [6] with the balanced search framework of [10]. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.153"}, {"primary_key": "2683210", "vector": [], "sparse_vector": [], "title": "Coarse-Grained Complexity for Dynamic Algorithms.", "authors": ["<PERSON><PERSON>", "Danupon <PERSON>", "Thatchaphol <PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Coarse-Grained Complexity for Dynamic Algorithms<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>chaphol Saranu<PERSON>pp.476 - 494Chapter DOI:https://doi.org/10.1137/1.9781611975994.29PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract To date, the only way to argue polynomial lower bounds for dynamic algorithms is via fine-grained complexity arguments. These arguments rely on strong assumptions about specific problems such as the Strong Exponential Time Hypothesis (SETH) and the Online Matrix-Vector Multiplication Conjecture (OMv). While they have led to many exciting discoveries, dynamic algorithms still miss out some benefits and lessons from the traditional \"coarse-grained\" approach that relates together classes of problems such as P and NP. In this paper we initiate the study of coarse-grained complexity theory for dynamic algorithms. Below are among questions that this theory can answer. What if dynamic Orthogonal Vector (OV) is easy in the cell-probe model? A research program for proving polynomial unconditional lower bounds for dynamic OV in the cell-probe model is motivated by the fact that many conditional lower bounds can be shown via reductions from the dynamic OV problem (e.g. [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>OCS 2014]). Since the cell-probe model is more powerful than word RAM and has historically allowed smaller upper bounds (e.g. [Larsen, Williams, SODA 2017; Chakraborty, Kamma, Larsen, STOC 2018]), it might turn out that dynamic OV is easy in the cell-probe model, making this research direction infeasible. Our theory implies that if this is the case, there will be very interesting algorithmic consequences: If dynamic OV can be maintained in polylogarithmic worst-case update time in the cell-probe model, then so are several important dynamic problems such as k-edge connectivity, (1 + ϵ)-approximate mincut, (1 + ϵ)-approximate matching, planar nearest neighbors, Chan's subset union and 3-vs-4 diameter. The same conclusion can be made when we replace dynamic OV by, e.g., subgraph connectivity, single source reachability, Chan's subset union, and 3-vs-4 diameter. Lower bounds for k-edge connectivity via dynamic OV? The ubiquity of reductions from dynamic OV raises a question whether we can prove conditional lower bounds for, e.g., k-edge connectivity, approximate mincut, and approximate matching, via the same approach. Our theory provides a method to refute such possibility (the so-called non-reducibility). In particular, we show that there are no \"efficient\" reductions (in both cell-probe and word RAM models) from dynamic OV to k-edge connectivity under an assumption about the classes of dynamic algorithms whose analogue in the static setting is widely believed. We are not aware of any existing assumptions that can play the same role. (The NSETH of Carmosino et al. [ITCS 2016] is the closest one, but is not enough.) To show similar results for other problems, one only need to develop efficient randomized verification protocols for such problems. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.29"}, {"primary_key": "2683211", "vector": [], "sparse_vector": [], "title": "Finding a latent k-simplex in O* (k · nnz(data)) time via Subset Smoothing.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Finding a latent k–simplex in O* (k · nnz(data)) time via Subset SmoothingChir<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.122 - 140Chapter DOI:https://doi.org/10.1137/1.9781611975994.8PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper we show that the learning problem for a large class of Latent variable models, such as Mixed Membership Stochastic Block Models, Topic Models, and Adversarial Clustering can be posed geometrically as follows: find a latent k— vertex simplex, K in Rd, given n data points, each obtained by perturbing a latent point in K. This problem does not seem to have been addressed. Our main contribution is an efficient algorithm for the geometric problem under deterministic assumptions which naturally hold for the models considered here. We observe that for a suitable r ≤ n, K is close to a data-determined polytope K' (the subset smoothed, polytope) which is the convex hull of the () points, each obtained by averaging an r subset of data points. Our algorithm is simply stated: it optimizes k carefully chosen linear functions over K' to find the k vertices of the latent simplex. The proof of correctness is more involved, drawing on existing and new tools from Numerical Analysis. Our overall runtime of O* (k nnz) is as good as the best times of existing algorithms (modulo O* (1) factor) for the special cases and is better for sparse data which is the norm in Topic Modelling and Mixed Membership models. Some consequences of our algorithm are: Mixed Membership Models and Topic Models: We give the first quasi-input-sparsity time algorithm for parameter estimation for k ϵ O* (1) Adversarial Clustering: In k–means, an adversary is allowed to move many data points from each cluster towards the convex hull of other cluster centers. Our algorithm still estimates cluster centers well. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.8"}, {"primary_key": "2683212", "vector": [], "sparse_vector": [], "title": "Euclidean Bottleneck Bounded-Degree Spanning Tree Ratios.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Euclidean Bottleneck Bounded-Degree Spanning Tree RatiosAhmad BiniazAhmad Biniazpp.826 - 836Chapter DOI:https://doi.org/10.1137/1.9781611975994.50PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Inspired by the seminal works of <PERSON><PERSON><PERSON> et al. (STOC 1994) and <PERSON> (SoCG 2003) we study the bottleneck version of the Euclidean bounded-degree spanning tree problem. A bottleneck spanning tree is a spanning tree whose largest edge-length is minimum, and a bottleneck degree-K spanning tree is a degree-K spanning tree whose largest edge-length is minimum. Let βκ be the supremum ratio of the largest edge-length of the bottleneck degree-K spanning tree to the largest edge-length of the bottleneck spanning tree, over all finite point sets in the Euclidean plane. It is known that β5= 1, and it is easy to verify that , and β4 > 1.175. It is implied by the Hamiltonicity of the cube of the bottleneck spanning tree that β2 ≪ 3. The degree-3 spanning tree algorithm of <PERSON> et al. (STOC 1993) implies that β3 ≪ 2. <PERSON> and <PERSON> (Networks, 68(4):302–314, 2016) showed that . We present the following improved bounds: , and . As a result, we obtain better approximation algorithms for Euclidean bottleneck degree-3 and degree-4 spanning trees. As parts of our proofs of these bounds we present some structural properties of the Euclidean minimum spanning tree which are of independent interest. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.50"}, {"primary_key": "2683214", "vector": [], "sparse_vector": [], "title": "Locally Consistent Parsing for Text Indexing in Small Space.", "authors": ["Or Birenzwige", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Locally Consistent Parsing for Text Indexing in Small SpaceOr <PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.607 - 626Chapter DOI:https://doi.org/10.1137/1.9781611975994.37PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider two closely related problems of text indexing in a sub-linear working space. The first problem is the Sparse Suffix Tree (SST) construction, where a text S is given in read-only memory, along with a set of suffixes B, and the goal is to construct the compressed trie of all these suffixes ordered lexicographically, using only (|B|) words of space. The second problem is the Longest Common Extension (LCE) problem, where again a text S of length n is given in read-only memory with some parameter 1 ≤ τ n, and the goal is to construct a data structure that uses words of space and can compute for any pair of suffixes their longest common prefix length. We show how to use ideas based on the Locally Consistent Parsing technique, that were introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [44], in some nontrivial ways in order to improve the known results for the above problems. We introduce new Las-Vegas and deterministic algorithms for both problems. For the randomized algorithms, we introduce the first Las-Vegas SST construction algorithm that takes (n) time. This is an improvement over the last result of Gawrychowski and Kociumaka [22] who obtained (n) time for Monte Carlo algorithm, and time with hight probability for Las-Vegas algorithm. In addition, we introduce a randomized Las-Vegas construction for a data structure that uses words of space, can be constructed in linear time with high probability and answers LCE queries in (τ) time. For the deterministic algorithms, we introduce an SST construction algorithm that takes time (for |B| = Ω(log n)). This is the first almost linear time, (n · polylog n), deterministic SST construction algorithm, where all previous algorithms take at least time. For the LCE problem, we introduce a data structure that uses words of space and answers LCE queries in time, with (n log τ) construction time (for ). This data structure improves both query time and construction time upon the results of Tanimura et al. [47]. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.37"}, {"primary_key": "2683215", "vector": [], "sparse_vector": [], "title": "Domain Reduction for Monotonicity Testing: A o(d) Tester for Boolean Functions in d-Dimensions.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Domain Reduction for Monotonicity Testing: A o(d) Tester for Boolean Functions in d-DimensionsHadley Black, Deeparnab Chakrabarty, and <PERSON><PERSON>, Deeparnab <PERSON>, and <PERSON><PERSON>.1975 - 1994Chapter DOI:https://doi.org/10.1137/1.9781611975994.122PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We describe a Õ(d5/6)-query monotonicity tester for Boolean functions f: [n]d → {0, 1} on the nhypergrid. This is the first o(d) monotonicity tester with query complexity independent of n. Motivated by this independence of n, we initiate the study of monotonicity testing of measurable Boolean functions f: ℝd → {0, 1} over the continuous domain, where the distance is measured with respect to a product distribution over ℝd. We give a Õ(d5/6)-query monotonicity tester for such functions. Our main technical result is a domain reduction theorem for monotonicity. For any function f: [n]d → {0, 1}, let εf be its distance to monotonicity. Consider the restriction of the function on a random [k]d sub-hypergrid of the original domain. We show that for k = poly(d/εf), the expected distance of the restriction is . Previously, such a result was only known for d = 1 (Berman-Raskhodnikova-Yaroslavtsev, STOC 2014). Our result for testing Boolean functions over [n]d then follows by applying the d5/6 · poly(1/ε log n, log d)-query hypergrid tester of Black-Chakrabarty-Seshadhri (SODA 2018). To obtain the result for testing Boolean functions over ℝd, we use standard measure theoretic tools to reduce monotonicity testing of a measurable function f to monotonicity testing of a discretized version of f over a hypergrid domain [N]d for large, but finite, N (that may depend on f). The independence of N in the hypergrid tester is crucial to getting the final tester over ℝd. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.122"}, {"primary_key": "2683216", "vector": [], "sparse_vector": [], "title": "The Combinatorics of the Longest-Chain Rule: Linear Consistency for Proof-of-Stake Blockchains.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)The Combinatorics of the Longest-Chain Rule: Linear Consistency for Proof-of-Stake BlockchainsErica <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>.1135 - 1154Chapter DOI:https://doi.org/10.1137/1.9781611975994.69PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The blockchain data structure maintained via the longest-chain rule—popularized by Bitcoin—is a powerful algorithmic tool for consensus algorithms. Such algorithms achieve consistency for blocks in the chain as a function of their depth from the end of the chain. While the analysis of Bitcoin guarantees consistency with error 2−k for blocks of depth O(k), the state-of-the-art of proof-of-stake (PoS) blockchains suffers from a quadratic dependence on k: these protocols, exemplified by Our<PERSON><PERSON><PERSON> (Crypto 2017), <PERSON><PERSON><PERSON><PERSON> (Eurocrypt 2018) and <PERSON><PERSON> (Asiacrypt 2017), can only establish that depth Θ(k2) is sufficient. Whether this quadratic gap is an intrinsic limitation of PoS—due to issues such as the nothing-at-stake problem—has been an urgent open question, as deployed PoS blockchains further rely on consistency for protocol correctnes. We give an axiomatic theory of blockchain dynamics that permits rigorous reasoning about the longest-chain rule and achieve, in broad generality, Θ(k) dependence on depth in order to achieve consistency error 2−k In particular, for the first time we show that PoS protocols can match proof-of-work protocols for linear consistency. We analyze the associated stochastic process, give a recursive relation for the critical functionals of this process, and derive tail bounds in both i.i.d. and martingale settings via associated generating functions. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.69"}, {"primary_key": "2683217", "vector": [], "sparse_vector": [], "title": "Shorter Labeling Schemes for Planar Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Shorter Labeling Schemes for Planar Graphs<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.446 - 462Chapter DOI:https://doi.org/10.1137/1.9781611975994.27PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract An adjacency labeling scheme for a given class of graphs is an algorithm that for every graph G from the class, assigns bit strings (labels) to vertices of G so that for any two vertices u, v, whether u and v are adjacent can be determined by a fixed procedure that examines only their labels. It is known that planar graphs with n vertices admit a labeling scheme with labels of bit length (2 + o(1)) log n. In this work we improve this bound by designing a labeling scheme with labels of bit length ( + o(1)) log n. In graph-theoretical terms, this implies an explicit construction of a graph on n4/3+o(1) vertices that contains all planar graphs on n vertices as induced subgraphs, improving the previous best upper bound of n2+o(1). Our scheme generalizes to graphs of bounded Euler genus with the same label length up to a second-order term. All the labels of the input graph can be computed in polynomial time, while adjacency can be decided from the labels in constant time. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.27"}, {"primary_key": "2683218", "vector": [], "sparse_vector": [], "title": "Improved Algorithms for Edit Distance and LCS: Beyond Worst Case.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Improved Algorithms for Edit Distance and LCS: Beyond Worst Case<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.1601 - 1620Chapter DOI:https://doi.org/10.1137/1.9781611975994.99PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Edit distance and longest common subsequence are among the most fundamental problems in combinatorial optimization. Recent developments have proven strong lower bounds against subquadratic time solutions for both problems. Moreover, the best approximation factors for subquadratic time solutions have been limited to 3 for edit distance and super constant for longest common subsequence. Improved approximation algorithms for these problems1 are some of the biggest open questions in combinatorial optimization. In this work, we present improved algorithms for both edit distance and longest common subsequence. The running times are truly subquadratic, though we obtain 1 + o(1) approximate solutions for both problems if the input satisfies a mild condition. In this setting, first, an adversary chooses one of the input strings. Next, this string is perturbed by a random procedure, and then the adversary chooses the second string after observing the perturbed one. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.99"}, {"primary_key": "2683219", "vector": [], "sparse_vector": [], "title": "Competitive Online Search Trees on Trees.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Competitive Online Search Trees on <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>.1878 - 1891Chapter DOI:https://doi.org/10.1137/1.9781611975994.115PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the design of adaptive data structures for searching elements of a tree-structured space. We use a natural generalization of the rotation-based online binary search tree model in which the underlying search space is the set of vertices of a tree. This model is based on a simple structure for decomposing graphs, previously known under several names including elimination trees, vertex rankings, and tubings. The model is equivalent to the classical binary search tree model exactly when the underlying tree is a path. We describe an online O(log log n)-competitive search tree data structure in this model, matching the best known competitive ratio of binary search trees. Our method is inspired by Tango trees, an online binary search tree algorithm, but critically needs several new notions including one which we call Steiner-closed search trees, which may be of independent interest. Moreover our technique is based on a novel use of two levels of decomposition, first from search space to a set of Steiner-closed trees, and secondly from these trees into paths. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.115"}, {"primary_key": "2683220", "vector": [], "sparse_vector": [], "title": "Symmetric Polymorphisms and Efficient Decidability of Promise CSPs.", "authors": ["<PERSON>", "<PERSON>en<PERSON><PERSON>wami"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Symmetric Polymorphisms and Efficient Decidability of Promise CSPsJoshua Brakensiek and Venkatesan GuruswamiJoshua Brakensiek and Venkatesan Guruswamipp.297 - 304Chapter DOI:https://doi.org/10.1137/1.9781611975994.18PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the field of constraint satisfaction problems (CSP), promise CSPs are an exciting new direction of study. In a promise CSP, each constraint comes in two forms: \"strict\" and \"weak,\" and in the associated decision problem one must distinguish between being able to satisfy all the strict constraints versus not being able to satisfy all the weak constraints. The most commonly cited example of a promise CSP is the approximate graph coloring problem—which has recently benefited from multiple breakthroughs [BKO19, WZ19] due to a systematic study of promise CSPs under the lens of \"polymorphisms,\" operations that map tuples in the strict form of each constraint to a tuple in its weak form. In this work, we present a simple algorithm which in polynomial time solves the decision problem for all promise CSPs that admit infinitely many symmetric polymorphisms, that is the coordinates are permutation invariant. This generalizes previous work of the authors [BG19]. We also extend this algorithm to a more general class of block-symmetric polymorphisms. As a corollary, this single algorithm solves all polynomial-time tractable Boolean CSPs simultaneously. These results give a new perspective on Schaefer's classic theorem and shed further light on how symmetries of polymorphisms enable algorithms. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.18"}, {"primary_key": "2683221", "vector": [], "sparse_vector": [], "title": "A Deterministic Linear Program Solver in Current Matrix Multiplication Time.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A Deterministic Linear Program Solver in Current Matrix Multiplication Time<PERSON><PERSON>.259 - 278Chapter DOI:https://doi.org/10.1137/1.9781611975994.16PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Interior point algorithms for solving linear programs have been studied extensively for a long time [e.g. <PERSON> 1984; <PERSON>, Sidford FOCS’14; <PERSON>, <PERSON>, <PERSON>’19]. For linear programs of the form with n variables and d constraints, the generic case d = Ω(n) has recently been settled by <PERSON>, <PERSON> and <PERSON> [STOC’19]. Their algorithm can solve linear programs in Õ(nω log(n/δ)) expected time1, where δ is the relative accuracy. This is essentially optimal as all known linear system solvers require up to O(nω) time for solving Ax = b. However, for the case of deterministic solvers, the best upper bound is <PERSON><PERSON><PERSON>'s 30 years old O(n2.5 log(n/δ)) bound [FOCS’89]. In this paper we show that one can also settle the deterministic setting by derandomizing <PERSON> et al.'s Õ(nω log(n/δ)) time algorithm. This allows for a strict Õ(nω log(n/δ)) time bound, instead of an expected one, and a simplified analysis, reducing the length of their proof of their central path method by roughly half. Derandomizing this algorithm was also an open question asked in Song's PhD Thesis. The main tool to achieve our result is a new data-structure that can maintain the solution to a linear system in subquadratic time. More accurately we are able to maintain in subquadratic time under 2 multiplicative changes to the diagonal matrix U and the vector v. This type of change is common for interior point algorithms. Previous algorithms [e.g. Vaidya STOC’89; Lee, Sidford FOCS’15; Cohen, Lee, Song STOC’19] required Ω(n2) time for this task. In [Cohen, Lee, Song STOC’19] they managed to maintain the matrix in subquadratic time, but multiplying it with a dense vector to solve the linear system still required Ω(n2) time. To improve the complexity of their linear program solver, they restricted the solver to only multiply sparse vectors via a random sampling argument. In comparison, our data-structure maintains the entire product additionally to just the matrix. Interestingly, this can be viewed as a simple modification of Cohen et al.'s data-structure, but it significantly simplifies their analysis of their central path method and makes their whole algorithm deterministic. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.16"}, {"primary_key": "2683222", "vector": [], "sparse_vector": [], "title": "Extended Formulation Lower Bounds for Refuting Random CSPs.", "authors": ["<PERSON>-<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Extended Formulation Lower Bounds for Refuting Random CSPs<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.305 - 324Chapter DOI:https://doi.org/10.1137/1.9781611975994.19PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Random constraint satisfaction problems (CSPs) such as random 3-SAT are conjectured to be computationally intractable. The average case hardness of random 3-SAT and other CSPs has broad and far-reaching implications on problems in approximation, learning theory and cryptography. In this work, we show subexponential lower bounds on the size of linear programming relaxation for refuting random instances of constraint satisfaction problems. Formally, suppose P: {0,1}k → {0,1} is a predicate that supports a t — 1-wise uniform distribution on its satisfying assignments. Consider the distribution of random instances of CSP P with m = Δn constraints. We show that any linear programming extended formulation that can refute instances from this distribution with constant probability must have size at least for all v > 0. For example, this yields a lower bound of size exp(n1/3) for random 3-SAT with a linear number of clauses. We use the technique of pseudocalibration to directly obtain extended formulation lower bounds from the planted distribution. This approach bypasses the need to construct Sherali-Adams integrality gaps in proving general LP lower bounds. As a corollary, one obtains a self-contained proof of subexponential Sherali-Adams LP lowerbounds for these problems. We believe the result sheds light on the technique of pseudocalibration, a promising but conjectural approach to LP/SDP lower bounds. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.19"}, {"primary_key": "2683223", "vector": [], "sparse_vector": [], "title": "Chasing Nested Convex Bodies Nearly Optimally.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Bo&apos;az Klartag", "<PERSON>", "<PERSON>zhi Li", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Chasing Nested Convex Bodies Nearly Optimally<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>.1496 - 1508Chapter DOI:https://doi.org/10.1137/1.9781611975994.91PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The convex body chasing problem, introduced by <PERSON> and <PERSON><PERSON> [FL93], is a competitive analysis problem on any normed vector space. In convex body chasing, for each timestep t ϵ ℕ, a convex body Kt ⊆ ℝd is given as a request, and the player picks a point xt ϵ Kt. The player aims to ensure that the total distance moved is within a bounded ratio of the smallest possible offline solution. In this work, we consider the nested version of the problem, in which the sequence (Kt) must be decreasing. For Euclidean spaces, we consider a memoryless algorithm which moves to the so-called Steiner point, and show that in an appropriate sense it is exactly optimal among memoryless algorithms. For general finite dimensional normed spaces, we combine the Steiner point and our recent algorithm in [ABC+19] to obtain a new algorithm which is nearly optimal for all spaces with p ≥ 1, closing a polynomial gap. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.91"}, {"primary_key": "2683224", "vector": [], "sparse_vector": [], "title": "Counting independent sets in unbalanced bipartite graphs.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Counting independent sets in unbalanced bipartite graphs<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>pp.1456 - 1466Chapter DOI:https://doi.org/10.1137/1.9781611975994.88PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Understanding the complexity of approximately counting the number of weighted or unweighted independent sets in a bipartite graph (#BIS) is a central open problem in the field of approximate counting. Here we consider a subclass of this problem and give an FPTAS for approximating the partition function of the hard-core model for bipartite graphs when there is sufficient imbalance in the degrees or fugacities between the sides (L, R) of the bipartition. This includes, among others, the biregular case when λ = 1 (approximating the number of independent sets of G) and ΔR ≥ 7ΔL log(ΔL). Our approximation algorithm is based on truncating the cluster expansion of a polymer model partition function that expresses the hard-core partition function in terms of deviations from independent sets that are empty on one side of the bipartition. Further consequences of this method for unbalanced bipartite graphs include an efficient sampling algorithm for the hard-core model and zero-freeness results for the partition function with complex fugacities. By utilizing connections between the cluster expansion and joint cumulants of certain random variables, we go beyond previous algorithmic applications of the cluster expansion to prove that the hard-core model exhibits exponential decay of correlations for all graphs and fugacities satisfying our conditions. This illustrates the applicability of statistical mechanics tools to algorithmic problems and refines our understanding of the connections between different methods of approximate counting. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.88"}, {"primary_key": "2683225", "vector": [], "sparse_vector": [], "title": "Learning from satisfying assignments under continuous distributions.", "authors": ["Clément L. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Learning from satisfying assignments under continuous distributionsClément L<PERSON>, <PERSON><PERSON><PERSON>, and Rocco A. ServedioClément L. <PERSON>ne, Anindya De, and Rocco A. Servediopp.82 - 101Chapter DOI:https://doi.org/10.1137/1.9781611975994.6PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract What kinds of functions are learnable from their satisfying assignments? Motivated by this simple question, we extend the framework of [DDS15], which studied the learnability of probability distributions over {0, 1}n defined by the set of satisfying assignments to \"low-complexity\" Boolean functions, to Boolean-valued functions defined over continuous domains. In our learning scenario there is a known \"background distribution\" over ℝn (such as a known normal distribution or a known log-concave distribution) and the learner is given i.i.d. samples drawn from a target distribution f, where f is restricted to the satisfying assignments of an unknown low-complexity Boolean-valued function f. The problem is to learn an approximation ′ of the target distribution f which has small error as measured in total variation distance. We give a range of efficient algorithms and hardness results for this problem, focusing on the case when f is a low-degree polynomial threshold function (PTF). When the background distribution is log-concave, we show that this learning problem is efficiently solvable for degree-1 PTFs (i.e., linear threshold functions) but not for degree-2 PTFs. In contrast, when is a normal distribution, we show that this learning problem is efficiently solvable for degree-2 PTFs but not for degree-4 PTFs. Our hardness results rely on standard assumptions about secure signature schemes. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.6"}, {"primary_key": "2683226", "vector": [], "sparse_vector": [], "title": "Vertex Ordering Problems in Directed Graph Streams.", "authors": ["<PERSON><PERSON>", "P<PERSON><PERSON> Ghos<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Vertex Ordering Problems in Directed Graph Streams<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.1786 - 1802Chapter DOI:https://doi.org/10.1137/1.9781611975994.109PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider directed graph algorithms in a streaming setting, focusing on problems concerning orderings of the vertices. This includes such fundamental problems as topological sorting and acyclicity testing. We also study the related problems of finding a minimum feedback arc set (edges whose removal yields an acyclic graph), and finding a sink vertex. We are interested in both adversarially-ordered and randomly-ordered streams. For arbitrary input graphs with edges ordered adversarially, we show that most of these problems have high space complexity, precluding sublinear-space solutions. Some lower bounds also apply when the stream is randomly ordered: e.g., in our most technical result we show that testing acyclicity in the p-pass random-order model requires roughly n1+1/p space. For other problems, random ordering can make a dramatic difference: e.g., it is possible to find a sink in an acyclic tournament in the onepass random-order model using polylog(n) space whereas under adversarial ordering roughly n1/p space is necessary and sufficient given Θ(p) passes. We also design sublinear algorithms for the feedback arc set problem in tournament graphs; for random graphs; and for randomly ordered streams. In some cases, we give lower bounds establishing that our algorithms are essentially space-optimal. Together, our results complement the much maturer body of work on algorithms for undirected graph streams. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.109"}, {"primary_key": "2683228", "vector": [], "sparse_vector": [], "title": "Multi-transversals for Triangles and the Tuza&apos;s Conjecture.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Pattara Sukprasert", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Multi-transversals for Triangles and the Tuza's Conjecture<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1955 - 1974Chapter DOI:https://doi.org/10.1137/1.9781611975994.121PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper, we study a primal and dual relationship about triangles: For any graph G, let v(G) be the maximum number of edge-disjoint triangles in G, and τ(G) be the minimum subset F of edges such that G \\ F is triangle-free. It is easy to see that v(G) ≤ τ(G) ≤ 3v(G), and in fact, this rather obvious inequality holds for a much more general primal-dual relation between k-hyper matching and covering in hypergraphs. <PERSON><PERSON> conjectured in 1981 that τ(G) ≤ 2v(G), and this question has received attention from various groups of researchers in discrete mathematics, settling various special cases such as planar graphs and generalized to bounded maximum average degree graphs, some cases of minor-free graphs, and very dense graphs. Despite these efforts, the conjecture in general graphs has remained wide open for almost four decades. In this paper, we provide a proof of a non-trivial consequence of the conjecture; that is, for every k ≥ 2, there exist a (multi)-set F ⊆ E(G): |F| ≤ 2kv(G) such that each triangle in G overlaps at least k elements in F. Our result can be seen as a strengthened statement of Krivelevich's result on the fractional version of Tuza's conjecture (and we give some examples illustrating this.) The main technical ingredient of our result is a charging argument, that locally identifies edges in F based on a local view of the packing solution. This idea might be useful in further studying the primal-dual relations in general and the Tuza's conjecture in particular. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.121"}, {"primary_key": "2683232", "vector": [], "sparse_vector": [], "title": "Small Memory Robust Simulation of Client-Server Interactive Protocols over Oblivious Noisy Channels.", "authors": ["T.<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>gon<PERSON> Polychron<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Small Memory Robust Simulation of Client-Server Interactive Protocols over Oblivious Noisy ChannelsT-H<PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.2349 - 2365Chapter DOI:https://doi.org/10.1137/1.9781611975994.144PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We revisit the problem of low-memory robust simulation of interactive protocols over noisy channels. <PERSON><PERSON><PERSON> [FOCS 2014] considered robust simulation of two-party interactive protocols over oblivious, as well as adaptive, noisy channels. Since the simulation does not need to have fixed communication pattern, the achieved communication rates can circumvent the lower bound proved by <PERSON><PERSON> and <PERSON><PERSON> [STOC 2013]. However, a drawback of this approach is that each party needs to remember the whole history of the simulated transcript. In a subsequent manuscript, <PERSON><PERSON><PERSON> and <PERSON><PERSON> considered low-memory simulation. The idea was to view the original protocol as a computational DAG and only the identities of the nodes are saved (as opposed to the whole transcript history) for backtracking to reduce memory usage. In this paper, we consider low-memory robust simulation of more general client-server interactive protocols, in which a leader communicates with other members/servers, who do not communicate among themselves; this setting can be applied to information-theoretic multi-server Private Information Retrieval (PIR) schemes. We propose an information-theoretic technique that converts any correct PIR protocol that assumes reliable channels, into a protocol which is both correct and private in the presence of a noisy channel while keeping the space complexity to a minimum. Despite the huge attention that PIR protocols have received in the literature, the existing works assume that the parties communicate using noiseless channels. Moreover, we observe that the approach of Haeupler and Resch to just save the nodes in the aforementioned DAG without taking the transcript history into account will lead to a correctness issue even for oblivious corruptions. We resolve this issue by saving hashes of prefixes of past transcripts. Departing from the DAG representation also allows us to accommodate scenarios where a party can simulate its part of the protocol without any extra knowledge (such as the DAG representation of the whole protocol). In the the two-party setting, our simulation has the same dependence on the error rate as in the work of Haeupler, and in the client-server setting it also depends on the number of servers. Furthermore, since our approach does not remember the complete transcript history, our current technique can defend only against oblivious corruptions. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.144"}, {"primary_key": "2683233", "vector": [], "sparse_vector": [], "title": "Better Data Structures for Colored Orthogonal Range Reporting.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Better Data Structures for Colored Orthogonal Range ReportingTimothy <PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>pp.627 - 636Chapter DOI:https://doi.org/10.1137/1.9781611975994.38PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Range searching on categorical, or \"colored\", data has been studied extensively for over two decades. In this paper, we obtain the current best results for perhaps the most basic, and most often studied, version of the geometric problem: colored orthogonal range reporting. Given n colored points in two-dimensional space [U]2, we present a data structure with O(n log3/4+ε n) space, for an arbitrarily small constant ε > 0, so that all k distinct colors in any axis-aligned query rectangle can be reported in (optimal) O (log log U + k) time; this is the first method to break the O(n log n) space barrier. In three dimensions, we present a data structure with O(n log9/5+ε n) space and O(log n/ log log n + k) time; this improves the previous space bound of O(n log4 n). Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.38"}, {"primary_key": "2683234", "vector": [], "sparse_vector": [], "title": "Tightening Curves on Surfaces Monotonically with Applications.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Tightening Curves on Surfaces Monotonically with <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.747 - 766Chapter DOI:https://doi.org/10.1137/1.9781611975994.46PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We prove the first polynomial bound on the number of monotonic homotopy moves required to tighten a collection of closed curves on any compact orientable surface, where the number of crossings in the curve is not allowed to increase at any time during the process. The best known upper bound before was exponential, which can be obtained by combining the algorithm of <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> [J. Comb. Theory Ser. B, 1997] together with an exponential upper bound on the number of possible surface maps. To obtain the new upper bound we apply tools from hyperbolic geometry, as well as operations in graph drawing algorithms—the cluster and pipe expansions—to the study of curves on surfaces. As corollaries, we present two efficient algorithms for curves and graphs on surfaces. First, we provide a polynomial-time algorithm to convert any given multicurve on a surface into minimal position. Such an algorithm only existed for single closed curves, and it is known that previous techniques do not generalize to the multicurve case. Second, we provide a polynomial-time algorithm to reduce any k-terminal plane graph (and more generally, surface graph) using degree-1 reductions, series-parallel reductions, and ΔY-transformations for arbitrary integer k. Previous algorithms only existed in the planar setting when k ≤ 4, and all of them rely on extensive case-by-case analysis based on different values of k. Our algorithm makes use of the connection between electrical transformations and homotopy moves, and thus solves the problem in a unified fashion. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.46"}, {"primary_key": "2683236", "vector": [], "sparse_vector": [], "title": "A Little Charity Guarantees Almost Envy-Freeness.", "authors": ["<PERSON><PERSON><PERSON>", "Telike<PERSON><PERSON>", "<PERSON>", "Alk<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A Little Charity Guarantees Almost Envy-Freeness<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>ap<PERSON>.2658 - 2672Chapter DOI:https://doi.org/10.1137/1.9781611975994.162PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Fair division of indivisible goods is a very well-studied problem. The goal of this problem is to distribute m goods to n agents in a “fair” manner, where every agent has a valuation for each subset of goods. We assume general valuations. Envy-freeness is the most extensively studied notion of fairness. However, envy-free allocations do not always exist when goods are indivisible. The notion of fairness we consider here is “envy-freeness up to any good” (EFX) where no agent envies another agent after the removal of any single good from the other agent's bundle. It is not known if such an allocation always exists even when n = 3. We show there is always a partition of the set of goods into n + 1 subsets (X1, …, Xn, P) where for i ϵ [n], Xi is the bundle allocated to agent i and the set P is unallocated (or donated to charity) such that we have: (1)envy-freeness up to any good,(2)no agent values P higher than her own bundle, and(3)fewer than n goods go to charity, i.e., |P| < n (typically m ≫ n). Our proof is constructive. When agents have additive valuations and |P| is large (i.e., when |P| is close to n), our allocation also has a good maximin share (MMS) guarantee. Moreover, a minor variant of our algorithm also shows the existence of an allocation which is 4/7 groupwise maximin share (GMMS): this is a notion of fairness stronger than MMS. This improves upon the current best bound of 1/2 known for an approximate GMMS allocation. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.162"}, {"primary_key": "2683237", "vector": [], "sparse_vector": [], "title": "Dynamic Low-St<PERSON>ch Spanning Trees in Subpolynomial Time.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Dynamic Low-Stretch Spanning Trees in Subpolynomial Time<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.463 - 475Chapter DOI:https://doi.org/10.1137/1.9781611975994.28PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Low-stretch spanning tree has been an important graphtheoretic object, as it is one of the building blocks for fast algorithms that solve symmetrically diagonally dominant linear systems, and a significant line of research has been devoted to finding constructions with optimal average stretch. In a very recent work by <PERSON><PERSON><PERSON> and <PERSON> [STOC 2019], the authors initiated the study of low-stretch spanning trees in the dynamic setting, and they proposed a dynamic algorithm that maintains a spanning tree in amortized update time with subpolynomial stretch in an unweighted graph on n vertices undergoing edge insertions and deletions demanded by an oblivious adversary. Our main results are twofold. First, we substantially improve the update time of <PERSON><PERSON><PERSON> and <PERSON> [STOC 2019] from to a subpolynomial of no(1). Second, we generalize our result to weighted graphs under the decremental setting. As far as we know, this is the first non trivial dynamic algorithm for maintaining low-stretch spanning tree for weighted graphs. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.28"}, {"primary_key": "2683238", "vector": [], "sparse_vector": [], "title": "Fast LP-based Approximations for Geometric Packing and Covering Problems.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Fast LP-based Approximations for Geometric Packing and Covering Problems<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON> Quanrudpp.1019 - 1038Chapter DOI:https://doi.org/10.1137/1.9781611975994.62PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We derive fast approximation schemes for LP relaxations of several well-studied geometric optimization problems that include packing, covering, and mixed packing and covering constraints. Previous work in computational geometry concentrated mainly on the rounding stage to prove approximation bounds, assuming that the underlying LPs can be solved efficiently. This work demonstrates that many of those results can be made to run in nearly linear time. In contrast to prior work on this topic our algorithms handle weights and capacities, side constraints, and also apply to mixed packing and covering problems, in a unified fashion. Our framework relies crucially on the properties of a randomized MWU algorithm of [41]; we demonstrate that it is well-suited for range spaces that admit efficient approximate dynamic data structures for emptiness oracles. Our framework cleanly separates the MWU algorithm for solving the LP from the key geometric data structure primitives, and this enables us to handle side constraints in a simple way. Combined with rounding algorithms that can also be implemented efficiently, we obtain the first near-linear constant factor approximation algorithms for several problems. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.62"}, {"primary_key": "2683239", "vector": [], "sparse_vector": [], "title": "Reconstruction under outliers for Fourier-sparse functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Reconstruction under outliers for Fourier-sparse functions<PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.2010 - 2029Chapter DOI:https://doi.org/10.1137/1.9781611975994.124PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the problem of learning an unknown f with a sparse Fourier spectrum in the presence of outlier noise. In particular, the algorithm has access to a noisy oracle for (an unknown) f such that (i) the Fourier spectrum of f is k-sparse; (ii) at any query point x, the oracle returns y such that with probability 1 – ρ, |y – f (x)| ≤ ε. However, with probability p, the error y – f (x) can be arbitrarily large. We study Fourier sparse functions over both the discrete cube {0, 1}n and the torus [0, 1) and for both these domains, we design efficient algorithms which can tolerate any ρ ε is randomly distributed, we also study the case where the outliers are adversarially located. In particular, we show that over the torus, assuming that the Fourier transform satisfies a certain granularity condition, there is a sample efficient algorithm to tolerate ρ = Ω(1) fraction of outliers and further, that this is not possible without such a granularity condition. Finally, while not the principal thrust, our techniques also allow us non-trivially improve on learning low-degree functions f on the hypercube in the presence of adversarial outlier noise. Our techniques combine a diverse array of tools from compressive sensing, sparse Fourier transform, chaining arguments and complex analysis. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.124"}, {"primary_key": "2683241", "vector": [], "sparse_vector": [], "title": "A Lower Bound on Cycle-Finding in Sparse Digraphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A Lower Bound on Cycle-Finding in Sparse Di<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>.2936 - 2952Chapter DOI:https://doi.org/10.1137/1.9781611975994.178PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the problem of finding a cycle in a sparse directed graph G that is promised to be far from acyclic, meaning that the smallest feedback arc set in G is large. We prove an information-theoretic lower bound, showing that for N-vertex graphs with constant outdegree any algorithm for this problem must make (N5/9) queries to an adjacency list representation of G. In the language of property testing, our result is an (N5/9) lower bound on the query complexity of one-sided algorithms for testing whether sparse digraphs with constant outdegree are far from acyclic. This is the first improvement on the lower bound, implicit in <PERSON><PERSON> and <PERSON> [BR02], which follows from a simple birthday paradox argument. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.178"}, {"primary_key": "2683242", "vector": [], "sparse_vector": [], "title": "Selling Information Through Consulting.", "authors": ["<PERSON><PERSON>", "Haifeng Xu", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Selling Information Through ConsultingYiling Chen, <PERSON><PERSON> Xu, and <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>.2412 - 2431Chapter DOI:https://doi.org/10.1137/1.9781611975994.148PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider a monopoly information holder selling information to a budget-constrained decision maker, who may benefit from the seller's information. The decision maker has a utility function that depends on his action and an uncertain state of the world. The seller and the buyer each observe a private signal regarding the state of the world, which may be correlated with each other. The seller's goal is to sell her private information to the buyer and extract maximum possible revenue, subject to the buyer's budget constraints. We consider three different settings with increasing generality, i.e., the seller's signal and the buyer's signal can be independent, correlated, or follow a general distribution accessed through a black-box sampling oracle. For each setting, we design information selling mechanisms which are both optimal and simple in the sense that they can be naturally interpreted, have succinct representations, and can be efficiently computed. Notably, though the optimal mechanism exhibits slightly increasing complexity as the setting becomes more general, all our mechanisms share the same format of acting as a consultant who recommends the best action to the buyer but uses different and carefully designed payment rules for different settings. Each of our optimal mechanisms can be easily computed by solving a single polynomial-size linear program. This significantly simplifies exponential-size LPs solved by the Ellipsoid method in the previous work, which computes the optimal mechanisms in the same setting but without budget limit. Such simplification is enabled by our new characterizations of the optimal mechanism in the (more realistic) budget-constrained setting. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.148"}, {"primary_key": "2683243", "vector": [], "sparse_vector": [], "title": "Relaxed Locally Correctable Codes with Nearly-Linear Block Length and Constant Query Complexity.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Relaxed Locally Correctable Codes with Nearly-Linear Block Length and Constant Query Complexity<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.1395 - 1411Chapter DOI:https://doi.org/10.1137/1.9781611975994.84PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Locally correctable codes (LCCs) are codes C: Σk → Σn which admit local algorithms that can correct any individual symbol of a corrupted codeword via a minuscule number of queries. One of the central problems in algorithmic coding theory is to construct O(1)-query LCC with minimal block length. Alas, state-of-the-art of such codes requires exponential block length to admit O(1)-query algorithms for local correction, despite much attention during the last two decades. This lack of progress prompted the study of relaxed LCCs, which allow the correction algorithm to abort (but not err) on small fraction of the locations. This relaxation turned out to allow constant-query correction algorithms for codes with polynomial block length. Specifically, prior work showed that there exist O(1)-query relaxed LCCs that achieve nearly-quartic block length n = k4+α, for an arbitrarily small constant α > 0. We construct an O(1)-query relaxed LCC with nearly-linear block length n = k1+α, for an arbitrarily small constant α > 0. This significantly narrows the gap between the lower bound which states that there are no O(1)-query relaxed LCCs with block length n = k1+o(1). In particular, this resolves an open problem raised by Gur, Ramnarayan, and Rothblum (ITCS 2018). Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.84"}, {"primary_key": "2683244", "vector": [], "sparse_vector": [], "title": "Extremal Distances in Directed Graphs: Tight Spanners and Near-Optimal Approximation Algorithms.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>mer Gold"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Extremal Distances in Directed Graphs: Tight Spanners and Near-Optimal Approximation Algorithms<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>pp.495 - 514Chapter DOI:https://doi.org/10.1137/1.9781611975994.30PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given a directed graph G = (V, E) on n vertices and m edges, a subgraph H = (V, Eʹ ⊆ E) is defined to be a t-diameter spanner if the diameter of H is at most t times the diameter of G. We show the existence of (and algorithms to compute) various t-diameter spanners with a sparse set of edges and t < 2, for directed graphs. In addition, we show that our spanner constructions give tight bounds on the number of edges. To the best of our knowledge, our work is the first to focus on the existence of various sparse (with ≪ n2 edges) diameter spanners of stretch < 2, for directed graphs. We also study eccentricity spanner, which is a subgraph that approximately preserves all vertex eccentricities of the original graph. As an application of our eccentricity spanner construction, we obtain the first Õ(m)-time algorithm for computing 2-approximation of vertex eccentricities in general directed graphs. This improves the result of <PERSON><PERSON> et al. [STOC 2018] who gave an time algorithm for this problem, and showed that there is no O(n2−o(1)) time algorithm that achieves approximation better than 2, unless SETH fails; this shows that our approximation factor is essentially tight. Finally, we study extremal distance spanners under dynamic settings. For dynamic diameter spanners, we provide incremental and decremental algorithms with a subquadratic total update time. For dynamic eccentricities and eccentricity spanner, we provide incremental and decremental algorithms with (2+ε)-approximation and O(n1+o(1)) amortized update time. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.30"}, {"primary_key": "2683245", "vector": [], "sparse_vector": [], "title": "Exact computation of a manifold metric, via Lipschitz Embeddings and Shortest Paths on a Graph.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Exact computation of a manifold metric, via Lipschitz Embeddings and Shortest Paths on a Graph<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.411 - 425<PERSON>hapter DOI:https://doi.org/10.1137/1.9781611975994.25PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Data-sensitive metrics adapt distances locally based the density of data points with the goal of aligning distances and some notion of similarity. In this paper, we give the first exact algorithm for computing a data-sensitive metric called the nearest neighbor metric. In fact, we prove the surprising result that a previously published 3-approximation is an exact algorithm. The nearest neighbor metric can be viewed as a special case of a density-based distance used in machine learning, or it can be seen as an example of a manifold metric. Previous computational research on such metrics despaired of computing exact distances on account of the apparent difficulty of minimizing over all continuous paths between a pair of points. We leverage the exact computation of the nearest neighbor metric to compute sparse spanners and persistent homology. We also explore the behavior of the metric built from point sets drawn from an underlying distribution and consider the more general case of inputs that are finite collections of path-connected compact sets. The main results connect several classical theories such as the conformal change of Riemannian metrics, the theory of positive definite functions of <PERSON>hoenberg, and screw function theory of <PERSON>hoenberg and Von Neumann. We also develop some novel proof techniques based on the combination of screw functions and Lipschitz extensions that may be of independent interest. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.25"}, {"primary_key": "2683246", "vector": [], "sparse_vector": [], "title": "Quasi-polynomial time approximation schemes for the Maximum Weight Independent Set Problem in H-free graphs.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Quasi-polynomial time approximation schemes for the Maximum Weight Independent Set Problem in H-free graphs<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>.2260 - 2278Chapter DOI:https://doi.org/10.1137/1.9781611975994.139PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the Maximum Independent Set problem we are asked to find a set of pairwise nonadjacent vertices in a given graph with the maximum possible cardinality. In general graphs, this classical problem is known to be NP-hard and hard to approximate within a factor of n1−ε for any ε > 0. Due to this, investigating the complexity of Maximum Independent Set in various graph classes in hope of finding better tractability results is an active research direction. In H-free graphs, that is, graphs not containing a fixed graph H as an induced subgraph, the problem is known to remain NP-hard and APX-hard whenever H contains a cycle, a vertex of degree at least four, or two vertices of degree at least three in one connected component. For the remaining cases, where every component of H is a path or a subdivided claw, the complexity of Maximum Independent Set remains widely open, with only a handful of polynomial-time solvability results for small graphs H such as P5, P6, the claw, or the fork. We prove that for every such \"possibly tractable\" graph H there exists an algorithm that, given an H-free graph G and an accuracy parameter ε > 0, finds an independent set in G of cardinality within a factor of (1 – ε) of the optimum in time exponential in a polynomial of log | V(G) | and ε−1. That is, we show that for every graph H for which Maximum Independent Set is not known to be APX-hard in H-free graphs, the problem admits a quasi-polynomial time approximation scheme in this graph class. Our algorithm works also in the more general weighted setting, where the input graph is supplied with a weight function on vertices and we are maximizing the total weight of an independent set. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.139"}, {"primary_key": "2683247", "vector": [], "sparse_vector": [], "title": "Approximation Schemes for Capacitated Clustering in Doubling Metrics.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximation Schemes for Capacitated Clustering in Doubling MetricsV<PERSON><PERSON>-<PERSON><PERSON>V<PERSON><PERSON>-Addadpp.2241 - 2259Chapter DOI:https://doi.org/10.1137/1.9781611975994.138PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the classic uniform capacitated k-median and uniform capacitated k-means problems in bounded doubling metrics. We provide the first QPTAS for both problems and the first PTAS for the k-median version for points in ℝ2. This is the first improvement over the bicriteria QPTAS for capacitated k-median in low-dimensional Euclidean space of Arora, Raghavan, Rao [STOC 1998] (1 + ε-approximation, 1 + ε-capacity violation) and arguably the first polynomial-time approximation algorithm for a non-trivial metric. Our result relies on a new structural proposition that applies to any metric space and that may be of interest for developping approximation algorithms for the problem in other metric spaces, such as for example planar or minor-free metrics. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.138"}, {"primary_key": "2683248", "vector": [], "sparse_vector": [], "title": "Instance-Optimality in the Noisy Value-and Comparison-Model.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Instance-Optimality in the Noisy Value-and Comparison-Model<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.2124 - 2143Chapter DOI:https://doi.org/10.1137/1.9781611975994.131PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Motivated by crowdsourced computation, peergrading, and recommendation systems, <PERSON><PERSON>, <PERSON> and <PERSON> [7] studied the query and round complexity of fundamental problems such as finding the maximum (max), finding all elements above a certain value (threshold-ν) or computing the top−k elements (Top-k) in a noisy environment. For example, consider the task of selecting papers for a conference. This task is challenging due to the crowdsourcing nature of peer reviews: the results of reviews are noisy and it is necessary to parallelize the review process as much as possible. We study the noisy value model and the noisy comparison model: In the noisy value model, a reviewer is asked to evaluate a single element: \"What is the value of paper i?\" (e.g., accept). In the noisy comparison model (introduced in the seminal work of <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON> [17]) a reviewer is asked to do a pairwise comparison: \"Is paper i better than paper j?\" In this paper, we introduce new lower bound techniques for these classic problems. In comparison to previous work, our lower bounds are much more fine-grained: they focus on the interplay between round and query complexity and the dependency on the output size. In the setting of conference papers, this translates into a trade-off between number of reviews per paper and the number of review rounds necessary in order find the best 100 papers for the conference. We complement these results with simple algorithms which show that our lower bounds are almost tight. We then go beyond the worst-case and address the question of the importance of knowledge of the instance by providing, for a large range of parameters, instance-optimal algorithms with respect to the query complexity. We complement these results by showing that for some family of instances, no instance-optimal algorithm can exist. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.131"}, {"primary_key": "2683249", "vector": [], "sparse_vector": [], "title": "The rank of sparse random matrices.", "authors": ["<PERSON><PERSON>", "Alperen <PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)The rank of sparse random matrices<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>.579 - 591Chapter DOI:https://doi.org/10.1137/1.9781611975994.35PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We determine the rank of a random matrix A over an arbitrary field with prescribed numbers of non-zero entries in each row and column. As an application we obtain a formula for the rate of low-density parity check codes. This formula vindicates a conjecture of Lelarge [Proc. IEEE Information Theory Workshop 2013]. The proofs are based on coupling arguments and a novel random perturbation, applicable to any matrix, that likely diminishes the number of short linear relations. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.35"}, {"primary_key": "2683250", "vector": [], "sparse_vector": [], "title": "The stable set problem in graphs with bounded genus and bounded odd cycle packing number.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)The stable set problem in graphs with bounded genus and bounded odd cycle packing number<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.2896 - 2915Chapter DOI:https://doi.org/10.1137/1.9781611975994.176PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Consider the family of graphs without k node-disjoint odd cycles, where k is a constant. Determining the complexity of the stable set problem for such graphs G is a long-standing problem. We give a polynomial-time algorithm for the case that G can be further embedded in a (possibly nonorientable) surface of bounded genus. Moreover, we obtain polynomial-size extended formulations for the respective stable set polytopes. To this end, we show that 2-sided odd cycles satisfy the Erdős-Pósa property in graphs embedded in a fixed surface. This extends the fact that odd cycles satisfy the Erdős-Pósa property in graphs embedded in a fixed orientable surface (<PERSON><PERSON> & <PERSON>, 2007). Eventually, our findings allow us to reduce the original problem to the problem of finding a minimum-cost nonnegative integer circulation of a certain homology class, which turns out to be efficiently solvable in our case. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.176"}, {"primary_key": "2683251", "vector": [], "sparse_vector": [], "title": "The Two-Sided Game of Googol and Sample-Based Prophet Inequalities.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)The Two-Sided Game of Googol and Sample-Based Prophet Inequalities<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON>, and <PERSON>.2066 - 2081Chapter DOI:https://doi.org/10.1137/1.9781611975994.127PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The secretary problem or the game of Googol are classic models for online selection problems that have received significant attention in the last five decades. In this paper we consider a variant of the problem and explore its connections to data-driven online selection. Specifically, we are given n cards with arbitrary nonnegative numbers written on both sides. The cards are randomly placed on n consecutive positions on a table, and for each card, the visible side is also selected at random. The player sees the visible side of all cards and wants to select the card with the maximum hidden value. To this end, the player flips the first card, sees its hidden value and decides whether to pick it or drop it and continue with the next card. We study algorithms for two natural objectives. In the first one, similar to the secretary problem, the player wants to maximize the probability of selecting the maximum hidden value. We show that this can be done with probability at least 0.45292. In the second objective, similar to the prophet inequality, the player wants to maximize the expectation of the selected hidden value. Here we show a guarantee of at least 0.63518 with respect to the expected maximum hidden value. Our algorithms result from combining three basic strategies. One is to stop whenever we see a value larger than the initial n visible numbers. The second one is to stop the first time the last flipped card's value is the largest of the currently n visible numbers in the table. And the third one is similar to the latter but to stop it additionally requires that the last flipped value is larger than the value on the other side of its card. We apply our results to the prophet secretary problem with unknown distributions, but with access to a single sample from each distribution. In particular, our guarantee improves upon 1 – 1/e for this problem, which is the currently best known guarantee and only works for the i.i.d. prophet inequality with samples. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.127"}, {"primary_key": "2683252", "vector": [], "sparse_vector": [], "title": "Algorithmic Price Discrimination.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Algorithmic Price DiscriminationR<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>pp.2432 - 2451Chapter DOI:https://doi.org/10.1137/1.9781611975994.149PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider a generalization of the third degree price discrimination problem studied in [4](<PERSON> et al., 2015), where an intermediary between the buyer and the seller can design market segments to maximize any linear combination of consumer surplus and seller revenue. Unlike in [4], we assume that the intermediary only has partial information about the buyer's value. We consider three different models of information, with increasing order of difficulty. In the first model, we assume that the intermediary's information allows him to construct a probability distribution of the buyer's value. Next we consider the sample complexity model, where we assume that the intermediary only sees samples from this distribution. Finally, we consider a bandit online learning model, where the intermediary can only observe past purchasing decisions of the buyer, rather than her exact value. For each of these models, we present algorithms to compute optimal or near optimal market segmentation. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.149"}, {"primary_key": "2683253", "vector": [], "sparse_vector": [], "title": "Individual Sensitivity Preprocessing for Data Privacy.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Individual Sensitivity Preprocessing for Data Privacy<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.528 - 547Chapter DOI:https://doi.org/10.1137/1.9781611975994.32PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The sensitivity metric in differential privacy, which is informally defined as the largest marginal change in output between neighboring databases, is of substantial significance in determining the accuracy of private data analyses. Techniques for improving accuracy when the average sensitivity is much smaller than the worst-case sensitivity have been developed within the differential privacy literature, including tools such as smooth sensitivity, Sample-and-Aggregate, Propose-Test-Release, and Lipschitz extensions. In this work, we provide a new and general Sensitivity-Preprocessing framework for reducing sensitivity, where efficient application gives state-of-the-art accuracy for privately outputting the important statistical metrics median and mean when no underlying assumptions are made about the database. In particular, our framework compares favorably to smooth sensitivity for privately outputting median, in terms of both running time and accuracy. Furthermore, because our framework is a preprocessing step, it can also be complementary to smooth sensitivity and any other private mechanism, where applying both can achieve further gains in accuracy. We additionally introduce a new notion of individual sensitivity and show that it is an important metric in the variant definition of personalized differential privacy. We show that our algorithm can extend to this context and serve as a useful tool for this variant definition and its applications in markets for privacy. Given the effectiveness of our framework in these important statistical metrics, we further investigate its properties and show that: (1) Our construction is conducive to efficient implementation with strong accuracy guarantees, evidenced by an O(n) implementation for median (with presorted data), and O(n2) implementation for more complicated functions such as mean, α-trimmed mean, and variance. (2) Our construction is both NP-hard and also optimal in the general setting (3) Our construction can be extended to higher dimensions, although it incurs accuracy loss that is linear in the dimension. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.32"}, {"primary_key": "2683254", "vector": [], "sparse_vector": [], "title": "Sublinear time approximation of the cost of a metric k-nearest neighbor graph.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Sublinear time approximation of the cost of a metric k-nearest neighbor graph<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>pp.2973 - 2992Chapter DOI:https://doi.org/10.1137/1.9781611975994.180PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Let (X, d) be an n-point metric space. We assume that (X, d) is given in the distance oracle model, that is, X = {1, …, n} and for every pair of points x, y from X we can query their distance d(x, y) in constant time. A k-nearest neighbor (k-NN) graph for (X, d) is a directed graph G = (V, E) that has an edge to each of v's k nearest neighbors. We use cost(G) to denote the sum of edge weights of G. In this paper, we study the problem of approximating cost(G) in sublinear time, when we are given oracle access to the metric space (X, d) that defines G. Our goal is to develop an algorithm that solves this problem faster than the time required to compute G. We first present an algorithm that in Õ∊(n2/k) time with probability at least approximates cost(G) to within a factor of 1 + ∊. Next, we present a more elaborate sublinear algorithm that in time Õϵ(min{nk3/2, n2/k}) computes an estimate of cost(G) that satisfies with probability at least where mst(X) denotes the cost of the minimum spanning tree of (X, d). Further, we complement these results with near matching lower bounds. We show that any algorithm that for a given metric space (X, d) of size n, with probability at least estimates cost(G) to within a 1 + ∊ factor requires Ω(n2/k) time. Similarly, any algorithm that with probability at least estimates cost(G) to within an additive error term ϵ · (mst(X) + cost(X)) requires Ωϵ(min{nk3/2, n2/k}) time. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.180"}, {"primary_key": "2683255", "vector": [], "sparse_vector": [], "title": "On the Learnability of Random Deep Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sreenivas Gollapudi", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)On the Learnability of Random Deep NetworksA<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.398 - 410Chapter DOI:https://doi.org/10.1137/1.9781611975994.24PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper we study the learnability of random deep networks both theoretically and experimentally. On the theoretical front, assuming the statistical query model, we show that the learnability of random deep networks with sign activation drops exponentially with their depths; under plausible conjectures, our results extend to ReLu and sigmoid activations. The core of the arguments is that even for highly correlated inputs, the outputs of deep random networks are near-orthogonal. On the experimental side, we find that the learnability of random networks drops sharply with depth even with the state-of-the-art training methods. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.24"}, {"primary_key": "2683256", "vector": [], "sparse_vector": [], "title": "A Tale of Santa Claus, Hypergraphs and Matroids.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A Tale of Santa Claus, Hypergraphs and Matroid<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.2748 - 2757Chapter DOI:https://doi.org/10.1137/1.9781611975994.167PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A well-known problem in scheduling and approximation algorithms is the Santa Claus problem. Suppose that <PERSON> has a set of gifts, and he wants to distribute them among a set of children so that the least happy child is made as happy as possible. Here, the value that a child i has for a present j is of the form pij ϵ {0, pj}. A polynomial time algorithm by <PERSON><PERSON><PERSON> et al. gives a 12.33-approximation and is based on a modification of <PERSON><PERSON><PERSON>'s hypergraph matching argument. In this paper, we introduce a matroid version of the Santa Claus problem. Our algorithm is also based on <PERSON><PERSON><PERSON>'s augmenting tree, but with the introduction of the matroid structure we solve a more general problem with cleaner methods. Our result can then be used as a blackbox to obtain a (4 + ϵ)-approximation for Santa Claus. This factor also compares against a natural, compact LP for <PERSON> Claus. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.167"}, {"primary_key": "2683257", "vector": [], "sparse_vector": [], "title": "Improved bounds for centered colorings.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Improved bounds for centered colorings<PERSON><PERSON><PERSON><PERSON> Dȩbski, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.2212 - 2226Chapter DOI:https://doi.org/10.1137/1.9781611975994.136PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A vertex coloring φ of a graph G is p-centered if for every connected subgraph H of G either φ uses more than p colors on H or there is a color that appears exactly once on H Centered colorings form one of the families of parameters that allow to capture notions of sparsity of graphs: A class of graphs has bounded expansion if and only if there is a function f such that for every p ≥ 1, every graph in the class admits a p-centered coloring using at most f(p) colors. In this paper, we give upper bounds for the maximum number of colors needed in a p-centered coloring of graphs from several widely studied graph classes. We show that: (1) planar graphs admit p-centered colorings with (p3 log p) colors where the previous bound was (p19); (2) bounded degree graphs admit p-centered colorings with (p) colors while it was conjectured that they may require exponential number of colors in p; (3) graphs avoiding a fixed graph as a topological minor admit p-centered colorings with a polynomial in p number of colors. All these upper bounds imply polynomial algorithms for computing the colorings. Prior to this work there were no non-trivial lower bounds known. We show that: (4) there are graphs of treewidth t that require colors in any p-centered coloring and this bound matches the upper bound; (5) there are planar graphs that require Ω(p2 log p) colors in any p-centered coloring. We also give asymptotically tight bounds for outerplanar graphs and planar graphs of treewidth 3. We prove our results with various proof techniques. The upper bound for planar graphs involves an application of a recent structure theorem while the upper bound for bounded degree graphs comes from the entropy compression method. We lift the result for bounded degree graphs to graphs avoiding a fixed topological minor using the Grohe-Marx structure theorem. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.136"}, {"primary_key": "2683258", "vector": [], "sparse_vector": [], "title": "Approximately counting and sampling small witnesses using a colourful decision oracle.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "In this paper, we prove “black box” results for turning algorithms which decide whether or not a witness exists into algorithms to approximately count the number of witnesses, or to sample from the set of witnesses approximately uniformly, with essentially the same running time. We do so by extending the framework of Dell and Lapinskas (STOC 2018), which covers decision problems that can be expressed as edge detection in bipartite graphs given limited oracle access; our framework covers problems which can be expressed as edge detection in arbitrary k-hypergraphs given limited oracle access. (Simulating this oracle generally corresponds to invoking a decision algorithm.) This includes many key problems in both the fine-grained setting (such as k-SUM, k-OV and weighted k-Clique) and the parameterised setting (such as induced subgraphs of size k or weight-k solutions to CSPs). From an algorithmic standpoint, our results will make the development of new approximate counting algorithms substantially easier; indeed, it already yields a new state-of-the-art algorithm for approximately counting graph motifs, improving on <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (JCSS 2015) unless the input graph is very dense and the desired motif very small. Our k-hypergraph reduction framework generalises and strengthens results in the graph oracle literature due to <PERSON><PERSON> et al. (ITCS 2018) and <PERSON><PERSON><PERSON> et al. (CoRR abs/1808.00691). Read More: https://epubs.siam.org/doi/10.1137/1.9781611975994.135", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.135"}, {"primary_key": "2683259", "vector": [], "sparse_vector": [], "title": "Computing Minimal Persistent Cycles: Polynomial and Hard Cases.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Persistent cycles, especially the minimal ones, are useful geometric features functioning as augmentations for the intervals in the purely topological persistence diagrams (also termed as barcodes). In our earlier work, we showed that computing minimal 1-dimensional persistent cycles (persistent 1-cycles) for finite intervals is NP-hard while the same for infinite intervals is polynomially tractable. In this paper, we address this problem for general dimensions with Z2 coefficients. In addition to proving that it is NP-hard to compute minimal persistent d-cycles (d > 1) for both types of intervals given arbitrary simplicial complexes, we identify two interesting cases which are polynomially tractable. These two cases assume the complex to be a certain generalization of manifolds which we term as weak pseudomanifolds. For finite intervals from the dth persistence diagram of a weak (d + 1)-pseudomanifold, we utilize the fact that persistent cycles of such intervals are null-homologous and reduce the problem to a minimal cut problem. Since the same problem for infinite intervals is NP-hard, we further assume the weak (d + 1)-pseudomanifold to be embedded in Rd+1 so that the complex has a natural dual graph structure and the problem reduces to a minimal cut problem. Experiments with both algorithms on scientific data indicate that the minimal persistent cycles capture various significant features of the data.", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.158"}, {"primary_key": "2683260", "vector": [], "sparse_vector": [], "title": "Parallel Batch-Dynamic Graphs: Algorithms and Lower Bounds.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Parallel Batch-Dynamic Graphs: <PERSON>gor<PERSON><PERSON> and Lower Bounds<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1300 - 1319Chapter DOI:https://doi.org/10.1137/1.9781611975994.79PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper we study the problem of dynamically maintaining graph properties under batches of edge insertions and deletions in the massively parallel model of computation. In this setting, the graph is stored on a number of machines, each having space strongly sublinear with respect to the number of vertices, that is, nϵ for some constant 0 < ϵ < 1. Our goal is to handle batches of updates and queries where the data for each batch fits onto one machine in constant rounds of parallel computation, as well as to reduce the total communication between the machines. This objective corresponds to the gradual buildup of databases over time, while the goal of obtaining constant rounds of communication for problems in the static setting has been elusive for problems as simple as undirected graph connectivity. We give an algorithm for dynamic graph connectivity in this setting with constant communication rounds and communication cost almost linear in terms of the batch size. Our techniques combine a new graph contraction technique, an independent random sample extractor from correlated samples, as well as distributed data structures supporting parallel updates and queries in batches. We also illustrate the power of dynamic algorithms in the MPC model by showing that the batched version of the adaptive connectivity problem is P-complete in the centralized setting, but sub-linear sized batches can be handled in a constant number of rounds. Due to the wide applicability of our approaches, we believe it represents a practically-motivated workaround to the current difficulties in designing more efficient massively parallel static graph algorithms. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.79"}, {"primary_key": "2683261", "vector": [], "sparse_vector": [], "title": "Optimal Orthogonal Drawings of Planar 3-Graphs in Linear Time.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Optimal Orthogonal Drawings of Planar 3-Graphs in Linear TimeWalter <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.806 - 825Chapter DOI:https://doi.org/10.1137/1.9781611975994.49PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract This paper addresses a long standing, widely studied, open question: Given a planar 3-graph G (i.e., a planar graph with vertex degree at most three), what is the best computational upper bound to compute a bend-minimum planar orthogonal drawing of G in the variable embedding setting? In this setting the algorithm can choose among the exponentially many planar embeddings of G the one that leads to an orthogonal drawing with the minimum number of bends. We answer the question by describing a linear-time algorithm that computes a bend-minimum planar orthogonal drawing of G. Also, if G is not K4, the drawing has at most one bend per edge. The existence of an orthogonal drawing Г of a planar 3-graph such that <PERSON> has the minimum number of bends and at most one bend per edge was previously unknown. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.49"}, {"primary_key": "2683262", "vector": [], "sparse_vector": [], "title": "Computing Circle Packing Representations of Planar Graphs.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Computing Circle Packing Representations of Planar Graphs<PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.2860 - 2875Chapter DOI:https://doi.org/10.1137/1.9781611975994.174PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The Circle Packing Theorem states that every planar graph can be represented as the tangency graph of a family of internally-disjoint circles. A well-known generalization is the Primal-Dual Circle Packing Theorem for 3-connected planar graphs. The existence of these representations has widespread applications in theoretical computer science and mathematics; however, the algorithmic aspect has received relatively little attention. In this work, we present an algorithm based on convex optimization for computing a primal-dual circle packing representation of maximal planar graphs, i.e. triangulations. This in turn gives an algorithm for computing a circle packing representation of any planar graph. Both take Õ(n log(R/ε)) expected run-time to produce a solution that is ϵ close to a true representation, where R is the ratio between the maximum and minimum circle radius in the true representation. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.174"}, {"primary_key": "2683263", "vector": [], "sparse_vector": [], "title": "Diameter computation on H-minor free graphs and graphs of bounded (distance) VC-dimension.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "We propose to study unweighted graphs of constant distance VC-dimension as a broad generalization of many graph classes for which we can compute the diameter in truly subquadratic-time. In particular for any fixed $H$, the class of $H$-minor free graphs has distance VC-dimension at most $|V(H)|-1$. Our first main result is that on graphs of distance VC-dimension at most $d$, for any fixed $k$ we can either compute the diameter or conclude that it is larger than $k$ in time $\\tilde{\\cal O}(k\\cdot mn^{1-\\varepsilon_d})$, where $\\varepsilon_d \\in (0;1)$ only depends on $d$. Then as a byproduct of our approach, we get the first truly subquadratic-time algorithm for constant diameter computation on all the nowhere dense graph classes. Finally, we show how to remove the dependency on $k$ for any graph class that excludes a fixed graph $H$ as a minor. More generally, our techniques apply to any graph with constant distance VC-dimension and polynomial expansion. As a result for all such graphs one obtains a truly subquadratic-time algorithm for computing their diameter. Our approach is based on the work of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> who proved the existence of spanning paths with strongly sublinear stabbing number for every hypergraph of constant VC-dimension. We show how to compute such paths efficiently by combining the best known approximation algorithms for the stabbing number problem with a clever use of $\\varepsilon$-nets, region decomposition and other partition techniques.", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.117"}, {"primary_key": "2683264", "vector": [], "sparse_vector": [], "title": "On the Cover of the Rolling Stone.", "authors": ["<PERSON>", "Csaba D. T<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)On the Cover of the Rolling StoneAdrian <PERSON> and <PERSON>sa<PERSON> T<PERSON> and <PERSON>sa<PERSON> Tóthpp.2575 - 2586Chapter DOI:https://doi.org/10.1137/1.9781611975994.157PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We construct a convex polytope of unit diameter that when placed on a horizontal surface on one of its faces, it repeatedly rolls over from one face to another until it comes to rest on some face, far away from its start position: that is, the horizontal distance between the footprints of the start and final faces can be larger than any given threshold. According to the laws of physics, the vertical distance between the center of mass of the polytope and the horizontal surface continuously decreases throughout the entire motion. The speed of the motion is irrelevant. Specifically, if the polytope is manually stopped after each tumble, the motion resumes when released (unless it stands on the final stable face). Moreover, such a polytope can be realized so that (i) it has a unique stable face, and (ii) it is an arbitrary close approximation of a unit ball. As such, this construction gives a positive answer to a question raised by <PERSON> (1969). The arbitrarily large rolling distance property investigated here for the first time raises intriguing questions and opens new avenues for future research. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011Key words:perpetuum mobile, rolling distance, unistable polytope, center of mass, laws of physics", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.157"}, {"primary_key": "2683265", "vector": [], "sparse_vector": [], "title": "Equivalences between triangle and range query problems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Virginia Vassilevska Williams"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Equivalences between triangle and range query problems<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>assilev<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> Vassilevska Williamspp.30 - 47Chapter DOI:https://doi.org/10.1137/1.9781611975994.3PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We define a natural class of range query problems, and prove that all problems within this class have the same time complexity (up to polylogarithmic factors). The equivalence is very general, and even applies to online algorithms. This allows us to obtain new improved algorithms for all of the problems in the class. We then focus on the special case of the problems when the queries are offline and the number of queries is linear. We show that our range query problems are runtime-equivalent (up to polylogarithmic factors) to counting for each edge e in an m-edge graph the number of triangles through e. This natural triangle problem can be solved using the best known triangle counting algorithm, running in (m2ω/(ω + 1) < (m1.41) time. Moreover, if ω = 2, the (m2ω/(ω + 1)) running time is known to be tight (within mo(1) factors) under the 3SUM Hypothesis. In this case, our equivalence settles the complexity of the range query problems. Our problems constitute the first equivalence class with this peculiar running time bound. To better understand the complexity of these problems, we also provide a deeper insight into the family of triangle problems, in particular showing black-box reductions between triangle listing and per-edge triangle detection and counting. As a byproduct of our reductions, we obtain a simple triangle listing algorithm matching the state-of-the-art for all regimes of the number of triangles. We also give some not necessarily tight, but still surprising reductions from variants of matrix products, such as the (min, max)-product. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.3"}, {"primary_key": "2683266", "vector": [], "sparse_vector": [], "title": "The Complexity of Contracts.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>-<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)The Complexity of Contracts<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.2688 - 2707<PERSON>hapter DOI:https://doi.org/10.1137/1.9781611975994.164PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We initiate the study of computing (near-)optimal contracts in succinctly representable principal-agent settings. Here optimality means maximizing the principal's expected payoff over all incentive-compatible contracts—known in economics as \"second-best\" solutions. We also study a natural relaxation to approximately incentive-compatible contracts. We focus on principal-agent settings with succinctly described (and exponentially large) outcome spaces. We show that the computational complexity of computing a near-optimal contract depends fundamentally on the number of agent actions. For settings with a constant number of actions, we present a fully polynomial-time approximation scheme (FPTAS) for the separation oracle of the dual of the problem of minimizing the principal's payment to the agent, and use this subroutine to efficiently compute a δ-incentive-compatible (δ-IC) contract whose expected payoff matches or surpasses that of the optimal IC contract. With an arbitrary number of actions, we prove that the problem is hard to approximate within any constant c. This inapproximability result holds even for δ-IC contracts where δ is a sufficiently rapidly-decaying function of c. On the positive side, we show that simple linear δ-IC contracts with constant δ are sufficient to achieve a constant-factor approximation of the \"first-best\" (full-welfare-extracting) solution, and that such a contract can be computed in polynomial time. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.164"}, {"primary_key": "2683267", "vector": [], "sparse_vector": [], "title": "Baker game and polynomial-time approximation schemes.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Baker game and polynomial-time approximation schemesZdeněk DvořákZdeněk Dvořákpp.2227 - 2240Chapter DOI:https://doi.org/10.1137/1.9781611975994.137PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Baker [1] devised a technique to obtain approximation schemes for many optimization problems restricted to planar graphs; her technique was later extended to more general graph classes. In particular, using the <PERSON>'s technique and the minor structure theorem, <PERSON><PERSON> et al. [5] gave Polynomial-Time Approximation Schemes (PTAS) for all monotone optimization problems expressible in the first-order logic when restricted to a proper minor-closed class of graphs. We define a Baker game formalizing the notion of repeated application of <PERSON>'s technique interspersed with vertex removal, prove that monotone optimization problems expressible in the first-order logic admit PTAS when restricted to graph classes in which the Baker game can be won in a constant number of rounds, and prove without use of the minor structure theorem that all proper minor-closed classes of graphs have this property. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.137"}, {"primary_key": "2683268", "vector": [], "sparse_vector": [], "title": "Faster sublinear approximation of the number of k-cliques in low-arboricity graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Faster sublinear approximation of the number of k-cliques in low-arboricity graphs<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.1467 - 1478Chapter DOI:https://doi.org/10.1137/1.9781611975994.89PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given query access to an undirected graph G, we consider the problem of computing a (1 ± ε)-approximation of the number of k-cliques in G. The standard query model for general graphs allows for degree queries, neighbor queries, and pair queries. Let n be the number of vertices, m be the number of edges, and nk be the number of k-cliques. Previous work by <PERSON>, <PERSON> and <PERSON> (STOC 2018) gives an -time algorithm for this problem (we use O*(·) to suppress poly(log n, 1/ε,kk) dependencies). Moreover, this bound is nearly optimal when the expression is sublinear in the size of the graph. Our motivation is to circumvent this lower bound, by parameterizing the complexity in terms of graph arboricity. The arboricity of G is a measure for the graph density “everywhere”. There is a very rich family of graphs with bounded arboricity, including all minor-closed graph classes (such as planar graphs and graphs with bounded treewidth), bounded degree graphs, preferential attachment graphs and more. We design an algorithm for the class of graphs with arboricity at most α, whose running time is . We also prove a nearly matching lower bound. For all graphs, the arboricity is , so this bound subsumes all previous results on sub-linear clique approximation. As a special case of interest, consider minor-closed families of graphs, which have constant arboricity. Our result implies that for any minor-closed family of graphs, there is a (1 ± ε)-approximation algorithm for nk that has running time . Such a bound was not known even for the special (classic) case of triangle counting in planar graphs. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.89"}, {"primary_key": "2683269", "vector": [], "sparse_vector": [], "title": "Sample Efficient Toeplitz Covariance Estimation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Sample Efficient Toeplitz Covariance EstimationY<PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON>, and <PERSON>.378 - 397Chapter DOI:https://doi.org/10.1137/1.9781611975994.23PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the sample complexity of estimating the covariance matrix T of a distribution over d-dimensional vectors, under the assumption that T is Toeplitz. This assumption arises in many signal processing problems, where the covariance between any two measurements only depends on the time or distance between those measurements. We are interested in estimation strategies that may choose to view only a subset of entries in each vector sample x ∼ , which often equates to reducing hardware and communication requirements in applications ranging from wireless signal processing to advanced imaging. Our goal is to minimize both 1) the number of vector samples drawn from and 2) the number of entries accessed in each sample. We provide some of the first non-asymptotic bounds on these sample complexity measures that exploit T's Toeplitz structure, and by doing so, significantly improve on results for generic covariance matrices. These bounds follow from a novel analysis of classical and widely used estimation algorithms (along with new variants), including methods based on selecting entries from each vector sample according to a so-called sparse ruler. In addition to results that hold for any Toeplitz T, we further study the important setting when T is close to low-rank, which is often the case in practice. We show that methods based on sparse rulers perform even better in this setting, with sample complexity scaling sublinearly in d. Motivated by this, we develop a new estimation strategy that further improves on existing methods in the low-rank case: when T is rank-k or nearly rank-k, it achieves sample complexity depending polynomially on k and only logarithmically on d. Our results utilize tools from random matrix sketching, leverage score based sampling techniques for continuous time signals, and sparse Fourier transform methods. In many cases, we pair our upper bounds with matching or nearly matching lower bounds. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.23"}, {"primary_key": "2683270", "vector": [], "sparse_vector": [], "title": "Differentially Private Release of Synthetic Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Differentially Private Release of Synthetic Graphs<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.560 - 578Chapter DOI:https://doi.org/10.1137/1.9781611975994.34PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We propose a (ϵ, δ)-differentially private mechanism that, given an input graph G with n vertices and m edges, in polynomial time generates a synthetic graph G' approximating all cuts of the input graph up to an additive error of . This is the first construction of differentially private cut approximator that allows additive error o(m) for all m > n logC n. The best known previous results gave additive O(n3/2) error and hence only retained information about the cut structure on very dense graphs. Thus, we are making a notable progress on a promiment problem in differential privacy. We also present lower bounds showing that our utility/privacy trade-off is essentially the best possible if one seeks to get purely additive cut approximations. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.34"}, {"primary_key": "2683271", "vector": [], "sparse_vector": [], "title": "Lossless Prioritized Embeddings.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Lossless Prioritized Embeddings<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>pp.1049 - 1062Chapter DOI:https://doi.org/10.1137/1.9781611975994.64PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given metric spaces (X, d) and (Y, ρ) and an ordering x1,x2,…,xn of (X, d), an embedding f: X → Y is said to have a prioritized distortion α(·), for a function α(·), if for any pair xj,x′ of distinct points in X, the distortion provided by f for this pair is at most a(j). If Y is a normed space, the embedding is said to have prioritized dimension β(·), if f(xj) may have at most β(j) nonzero coordinates. The notion of prioritized embedding was introduced by <PERSON><PERSON><PERSON> and the current authors in [EFN18], where a rather general methodology for constructing such embeddings was developed. Though this methodology enabled [EFN18] to come up with many prioritized embeddings, it typically incurs some loss in the distortion. In other words, in the worst-case, prioritized embeddings obtained via this methodology incur distortion which is at least a constant factor off, compared to the distortion of the classical counterparts of these embeddings. This constant loss is problematic for isometric embeddings. It is also troublesome for Matousek's embedding of general metrics into ℓ∞, which for a parameter k = 1, 2, …, provides distortion 2k–1 and dimension O(k log n·n1/k). In this paper we devise two lossless prioritized embeddings. The first one is an isometric prioritized embedding of tree metrics into with dimension O(log j), matching the worst-case guarantee of O(log n) of the classical embedding of Linial et al. [LLR95]. The second one is a prioritized Matousek's embedding of general metrics into ℓ∞, which for a parameter k = 1,2, …, provides prioritized distortion and dimension O(k log n · n1/k), again matching the worst-case guarantee 2k – 1 in the distortion of the classical Matousek's embedding. We also provide a dimension-prioritized variant of Matousek's embedding. Finally, we devise prioritized embeddings of general metrics into (single) ultra-metric and of general graphs into (single) spanning tree with asymptotically optimal distortion. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.64"}, {"primary_key": "2683272", "vector": [], "sparse_vector": [], "title": "Computational Concentration of Measure: Optimal Bounds, Reductions, and More.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Computational Concentration of Measure: Optimal Bounds, Reductions, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.345 - 363Chapter DOI:https://doi.org/10.1137/1.9781611975994.21PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Product measures of dimension n are known to be \"concentrated\" under Hamming distance. More precisely, for any set S in the product space of probability Pr[S] ≥ ε, a random point in the space, with probability 1 – δ, has a neighbor in S that is different from the original point in only coordinates (and this is optimal). In this work, we obtain the tight computational (algorithmic) version of this result, showing how given a random point and access to an S-membership query oracle, we can find such a close point of Hamming distance in time poly(n, 1/ε, 1/δ). This resolves an open question of [<PERSON><PERSON><PERSON>] who proved a weaker result (that works only for ). As corollaries, we obtain polynomial-time poisoning and (in certain settings) evasion attacks against learning algorithms when the original vulnerabilities have any cryptographically non-negligible probability. We call our algorithm MUCIO (short for \"MUltiplicative Conditional Influence Optimizer\") since proceeding through the coordinates of the product space, it decides to change each coordinate of the given point based on a multiplicative version of the influence of a variable, where the influence is computed conditioned on the value of all previously updated coordinates. MUCIO is an online algorithm in that it decides on the i'th coordinate of the output given only the first i coordinates of the input. It also does not make any convexity assumption about the set S. Motivated by obtaining algorithmic variants of measure concentration in other metric probability spaces, we define a new notion of algorithmic reduction between computational concentration of measure in different probability metric spaces. This notion, whose definition has some subtlety, requires two (inverse) algorithmic mappings one of which is an algorithmic Lipschitz mapping and the other one is an algorithmic coupling connecting the two distributions. As an application, we apply this notion of reduction to obtain computational concentration of measure for high-dimensional Gaussian distributions under the ℓ1 distance. We further prove several extensions to the results above as follows. (1) Generalizing in another dimension, our computational concentration result is also true when the Hamming distance is weighted. (2) As measure concentration is usually proved for concentration around mean, we show how to use our results above to obtain algorithmic concentration for that setting as well. In particular, we prove a computational variant of McDiarmid's inequality, when properly defined. (3) Our result generalizes to discrete random processes (instead of just product distributions), and this generalization leads to new tampering algorithms for collective coin tossing protocols. (4) Finally, we prove exponential lower bounds on the average running time of non-adaptive query algorithms for proving computational concentration for the case of product spaces. Perhaps surprisingly, such lower bound shows any efficient algorithm must query about S-membership of points that are not close to the original point even though we are only interested in finding a close point in S. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.21"}, {"primary_key": "2683273", "vector": [], "sparse_vector": [], "title": "Quasi-popular Matchings, Optimality, and Extended Formulations.", "authors": ["<PERSON>", "Telike<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Quasi-popular Matchings, Optimality, and Extended Formulations<PERSON><PERSON> and Telikepal<PERSON> and Telikepal<PERSON> Ka<PERSON>pp.325 - 344Chapter DOI:https://doi.org/10.1137/1.9781611975994.20PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Let G = (A ∪ B, E) be an instance of the stable marriage problem where every vertex ranks its neighbors in a strict order of preference. A matching M in G is popular if <PERSON> does not lose a head-to-head election against any matching. Popular matchings are a well-studied generalization of stable matchings, introduced with the goal of enlarging the set of admissible solutions, while maintaining a certain level of fairness. Every stable matching is a min-size popular matching. Unfortunately, when there are edge costs, it is NP-hard to find a popular matching of minimum cost – even worse, the min-cost popular matching problem is hard to approximate up to any factor. Let opt be the cost of a min-cost popular matching. Our goal is to efficiently compute a matching of cost at most opt by paying the price of mildly relaxing popularity. Our main positive result is a bi-criteria algorithm that finds in polynomial time a near-popular or \"quasi-popular\" matching of cost at most opt. Key to the algorithm are a number of results for certain polytopes related to matchings. In particular, we give a polynomial-size extended formulation for an integral polytope sandwiched between the popular and quasi-popular matching polytopes. We complement these results by showing that it is NP-hard to find a quasi-popular matching of minimum cost, and that both the popular and quasi-popular matching polytopes have near-exponential extension complexity. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.20"}, {"primary_key": "2683274", "vector": [], "sparse_vector": [], "title": "Embeddability of Simplicial Complexes is Undecidable.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Embeddability of Simplicial Complexes is Undecidable<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.767 - 785Chapter DOI:https://doi.org/10.1137/1.9781611975994.47PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the following decision problem EMBEDk→d in computational topology (where k ≤ d are fixed positive integers): Given a finite simplicial complex K of dimension k, does there exist a (piecewise-linear) embedding of K into ℝd? The special case EMBED1→2 is graph planarity, which is decidable in linear time, as shown by <PERSON><PERSON> and <PERSON><PERSON><PERSON>. In higher dimensions, EMBED2→3 and EMBED3→3 are known to be decidable (as well as NP-hard), and recent results of <PERSON><PERSON><PERSON> et al. in computational homotopy theory, in combination with the classical <PERSON><PERSON><PERSON><PERSON><PERSON> theorem in geometric topology, imply that EMBEDk→d can be solved in polynomial time for any fixed pair (k, d) of dimensions in the so-called metastable range . Here, by contrast, we prove that EMBEDk→d is algorithmically undecidable for almost all pairs of dimensions outside the metastable range, namely for . This almost completely resolves the decidability vs. undecidability of EMBEDk→d in higher dimensions and establishes a sharp dichotomy between polynomial-time solvability and undecidability. Our result complements (and in a wide range of dimensions strengthens) earlier results of Matoušek, Tancer, and the second author, who showed that EMBEDk→d is undecidable for 4 ≤ k ϵ {d – 1, d}, and NP-hard for all remaining pairs (k, d) outside the metastable range and satisfying d ≥ 4. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.47"}, {"primary_key": "2683275", "vector": [], "sparse_vector": [], "title": "A face cover perspective to ℓ1 embeddings of planar graphs.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A face cover perspective to ℓ1 embeddings of planar graphs<PERSON><PERSON><PERSON>.1945 - 1954Chapter DOI:https://doi.org/10.1137/1.9781611975994.120PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract It was conjectured by <PERSON> et al. [Combinatorica04] that every planar graph can be embedded into ℓ1 with constant distortion. However, given an n-vertex weighted planar graph, the best upper bound on the distortion is only , by <PERSON> [SoCG99]. In this paper we study the case where there is a set K of terminals, and the goal is to embed only the terminals into ℓ1 with low distortion. In a seminal paper, <PERSON><PERSON><PERSON> and <PERSON> [J.Comb.Theory81] showed that if all the terminals lie on a single face, they can be embedded isometrically into ℓ1. The more general case, where the set of terminals can be covered by γ faces, was studied by <PERSON> and <PERSON><PERSON><PERSON><PERSON> [STOC09] and <PERSON><PERSON><PERSON> et al. [J.Comb.Theory13]. The state of the art is an upper bound of O(log γ) by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON> [SODA19]. Our contribution is a further improvement on the upper bound to . Since every planar graph has at most O(n) faces, any further improvement on this result, will be a major breakthrough, directly improving upon Rao's long standing upper bound. Moreover, it is well known that the flow-cut gap equals to the distortion of the best embedding into ℓ1. Therefore, our result provides a polynomial time -approximation to the sparsest cut problem on planar graphs, for the case where all the demand pairs can be covered by γ faces. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.120"}, {"primary_key": "2683276", "vector": [], "sparse_vector": [], "title": "Labelings vs. Embeddings: On Distributed Representations of Distances.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Labelings vs. Embeddings: On Distributed Representations of Distances<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.1063 - 1075Chapter DOI:https://doi.org/10.1137/1.9781611975994.65PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We investigate for which metric spaces the performance of distance labeling and of ℓ∞-embeddings differ, and how significant can this difference be. Recall that a distance labeling is a distributed representation of distances in a metric space (X, d), where each point x ∊ X is assigned a succinct label, such that the distance between any two points x, y ∊ X can be approximated given only their labels. A highly structured special case is an embedding into ℓ∞, where each point x ∊ X is assigned a vector f (x) such that ‖f(x)−f (y)‖∞ is approximately d(x, y). The performance of a distance labeling or an ℓ∞-embedding is measured via its distortion and its label-size/dimension. We also study the analogous question for the prioritized versions of these two measures. Here, a priority order π = (x1, …, xn) of the point set X is given, and higher-priority points should have shorter labels. Formally, a distance labeling has prioritized label-size α(.) if every xj has label size at most α(j). Similarly, an embedding f: X → ℓ∞ has prioritized dimension α(·) if f (xj) is non-zero only in the first α(j) coordinates. In addition, we compare these their prioritized measures to their classical (worst-case) versions. We answer these questions in several scenarios, uncovering a surprisingly diverse range of behaviors. First, in some cases labelings and embeddings have very similar worst-case performance, but in other cases there is a huge disparity. However in the prioritized setting, we most often find a strict separation between the performance of labelings and embeddings. And finally, when comparing the classical and prioritized settings, we find that the worst-case bound for label size often \"translates\" to a prioritized one, but also a surprising exception to this rule. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.65"}, {"primary_key": "2683277", "vector": [], "sparse_vector": [], "title": "Approximation Schemes via Width/Weight Trade-offs on Minor-free Graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximation Schemes via Width/Weight Trade-offs on Minor-free GraphsFed<PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.2299 - 2318Chapter DOI:https://doi.org/10.1137/1.9781611975994.141PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper, we prove a new scaling lemma for vertex weighted minor free graphs that allows for a smooth trade-off between the weight of a vertex set S and the treewidth of G — S. More precisely, we show the following. There exists an algorithm that given an H-minor free graph G, a weight function w: V(G) → ℚ+ and integers t and s, runs in polynomial time, and outputs a subset S ⊆ V(G) of weight at most d log n · opt(G, w, t)/s such that the treewidth of G – S is at most c·st. Here, d and c are fixed constants that depend only on H, and opt(G, w, t) is the (unknown) minimum weight of a subset U ⊆ V(G) such that the treewidth of G – U is at most t. This lemma immediately yields the first polynomial-time approximation schemes (PTASes) for WEIGHTED Treewidth-η Vertex Deletion, for η > 2, on graphs of bounded genus and the first PTAS for Weighted Feedback vertex Set on H-minor free graphs. These results effortlessly generalize to include weighted edge deletion problems, to all Weighted Connected Planar -Deletion problems, and finally to quasi polynomial time approximation schemes (QPTASes) for all of these problems on H-minor free graphs. For most of these problems even constant factor approximation algorithms, even on planar graphs, were not previously known. Additionally, using the scaling lemma we subsume, simplify and extend the recent framework of Cohen-Addad et al. [STOC 2016] for turning constant factor approximation algorithms for \"ubiquitous\" problems into PTASes for the same problems on graphs of bounded genus. Specifically, we obtain PTASes for ubiquitous problems without the requirement of having a constant factor approximation. While the statement of the scaling lemma is inspired by an analogous lemma by Cohen-Addad et al. [STOC 2016] for edge contractions on weighted graphs of bounded genus, as well as a scaling lemma by Fomin et al. [SODA 2011] for unweighted graphs, the proof is entirely different. The proof detours via three different linear programming relaxations for the Weighted Treewidth-η Vertex Deletion problems and a strengthening of a recent rounding procedure of Bansal et al. [SODA 2017] enhanced by the classic Klein-Plotkin-Rao Theorem [STOC 1993]. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.141"}, {"primary_key": "2683278", "vector": [], "sparse_vector": [], "title": "Computing and Testing Small Connectivity in Near-Linear Time and Queries via Fast Local Cut Algorithms.", "authors": ["<PERSON>", "Danupon <PERSON>", "<PERSON>", "Thatchaphol <PERSON>", "Sorrachai <PERSON>i"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Computing and Testing Small Connectivity in Near-Linear Time and Queries via Fast Local Cut AlgorithmsSeb<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>chaiSebast<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>ornchaipp.2046 - 2065Chapter DOI:https://doi.org/10.1137/1.9781611975994.126PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Consider the following \"local\" cut-detection problem in a directed graph: We are given a seed vertex x and need to remove at most k edges so that at most v edges can be reached from x (a \"local\" cut) or output ⊥ to indicate that no such cut exists. If we are given query access to the input graph, then this problem can in principle be solved without reading the whole graph and with query complexity depending on k and ν. In this paper we consider a slack variant of this problem where, when such a cut exists, we can output a cut with up to O(kν) edges reachable from x. We present a simple randomized algorithm spending O(k2ν) time and O(kν) queries for the above variant, improving in particular a previous time bound of O(kO(k)ν) by Chechik et al. [SODA'17]. We also extend our algorithm to handle an approximate variant. We demonstrate that these local algorithms are versatile primitives for designing substantially improved algorithms for classic graph problems by providing the following three applications. (Throughout, Õ(T) hides polylog(T).) A randomized algorithm for the classic k-vertex connectivity problem that takes near-linear time when k = O(polylog(n)), namely Õ(m + nk3) time in undirected graphs. Prior to our work, the state of the art for this range of k were linear-time algorithms for k ≤ 3 [Tarjan FOCS'71; Hopcroft, Tarjan SICOMP'73] and a recent algorithm with Õ(m + n4/3k7/3) time [Nanongkai et al., STOC'19]. The story is the same for directed graphs where our Õ(mk2)-time algorithm is near-linear when k = O(polylog(n)). Our techniques also yield an improved approximation scheme. Property testing algorithms for k-edge and -vertex connectivity with query complexities that are near-linear in k, exponentially improving the state-of-the-art. This resolves two open problems, one by Goldreich and Ron [STOC'97] and one by Orenstein and Ron [Theor. Comput. Sci.'11]. A faster algorithm for computing the maximal k-edge connected subgraphs, improving prior work of Chechik et al. [SODA'17]. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.126"}, {"primary_key": "2683279", "vector": [], "sparse_vector": [], "title": "A randomly weighted minimum spanning tree with a random cost constraint.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A randomly weighted minimum spanning tree with a random cost constraint<PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.670 - 689Chapter DOI:https://doi.org/10.1137/1.9781611975994.41PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the minimum spanning tree problem on the complete graph where an edge e has a weight We and a cost Ce, each of which is an independent uniform [0, 1] random variable. There is also a constraint that the spanning tree T must satisfy C(T) ≤ c0. We establish the asymptotic value of the optimum weight via the consideration of a dual problem. The proof is therefore constructive i.e. can be thought of as the analysis of a polynomial time algorithm. We also study the minimum spanning arborescence problem on the complete digraph where an edge e has a weight We and a cost Ce, each of which is an independent uniform [0, 1] random variable. There is also a constraint that the spanning arborescence T must satisfy C(T) ≤ c0. We establish the asymptotic value of the optimum weight via the consideration of a dual problem. The proof is via the analysis of a polynomial time algorithm. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011Key words:Random Minimum Spanning Tree, Random Minimum Spanning Arborescence, Cost Constraint", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.41"}, {"primary_key": "2683280", "vector": [], "sparse_vector": [], "title": "Atomic Embeddability, Clustered Planarity, and Thickenability.", "authors": ["<PERSON><PERSON><PERSON>", "Csaba D. T<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Atomic Embeddability, Clustered Planarity, and Thickenability<PERSON><PERSON><PERSON> and Csaba <PERSON><PERSON><PERSON><PERSON> and Csaba D. Tóthpp.2876 - 2895Chapter DOI:https://doi.org/10.1137/1.9781611975994.175PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the atomic embeddability testing problem, which is a common generalization of clustered planarity (c-planarity, for short) and thickenability testing, and present a polynomial time algorithm for this problem, thereby giving the first polynomial time algorithm for c-planarity. C-planarity was introduced in 1995 by <PERSON>, <PERSON>, and <PERSON> as a variant of graph planarity, in which the vertex set of the input graph is endowed with a hierarchical clustering and we seek an embedding (crossing free drawing) of the graph in the plane that respects the clustering in a certain natural sense. Until now, it has been an open problem whether c-planarity can be tested efficiently, despite relentless efforts. The thickenability problem for simplicial complexes emerged in the topology of manifolds in the 1960s. A 2-dimensional simplicial complex is thickenable if it embeds in some orientable 3-dimensional manifold. Recently, <PERSON><PERSON><PERSON> announced that thickenability can be tested in polynomial time. Our algorithm for atomic embeddability combines ideas from <PERSON>mesin's work with algorithmic tools previously developed for weak embeddability testing. We express our results purely in terms of graphs on surfaces, and rely on the machinery of topological graph theory. Finally we give a polynomial-time reduction from c-planarity to thickenability and show that a slight generalization of atomic embeddability to the setting in which clusters are toroidal graphs is NP-complete. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.175"}, {"primary_key": "2683281", "vector": [], "sparse_vector": [], "title": "Sandwiching random regular graphs between binomial random graphs.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Sandwiching random regular graphs between binomial random graphs<PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>pp.690 - 701Chapter DOI:https://doi.org/10.1137/1.9781611975994.42PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Kim and <PERSON><PERSON> made the following conjecture (Advances in Mathematics, 2004): if d ≫ log n, then the random d-regular graph (n, d) can asymptotically almost surely be \"sandwiched\" between (n, p1) and (n, p2) where p1 and p2 are both (1 + o(1))d/n. They proved this conjecture for log n ≪ d ≪ n1/3−o(1), with a defect in the sandwiching: (n, d) contains (n, p1) perfectly, but is not completely contained in (n, p2). Recently, the embedding (n, p1) ⊆ (n, d) was improved by <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> to d = o(n). In this paper, we prove <PERSON>'s sandwich conjecture, with perfect containment on both sides, for all . For , we prove a weaker version of the sandwich conjecture with p2 approximately equal to (d/n) log n, without any defect. In addition to sandwiching regular graphs, our results cover graphs whose degrees are asymptotically equal. The proofs rely on estimates for the probability that a random factor of a pseudorandom graph contains a given edge, which is of independent interest. As applications, we obtain new results on the properties of random graphs with given near-regular degree sequences, including Hamiltonicity and universality in subgraph containment. We also determine several graph parameters in these random graphs, such as the chromatic number, small subgraph counts, the diameter, and the independence number. We are also able to characterise many phase transitions in edge percolation on these random graphs, such as the threshold for the appearance of a giant component. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.42"}, {"primary_key": "2683282", "vector": [], "sparse_vector": [], "title": "Approximating Nash Social Welfare under Submodular Valuations through (Un)Matchings.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximating Nash Social Welfare under Submodular Valuations through (Un)Matchings<PERSON><PERSON><PERSON>g, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.2673 - 2687Chapter DOI:https://doi.org/10.1137/1.9781611975994.163PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the problem of approximating maximum Nash social welfare (NSW) when allocating m indivisible items among n asymmetric agents with submodular valuations. The NSW is a well-established notion of fairness and efficiency, defined as the weighted geometric mean of agents' valuations. For special cases of the problem with symmetric agents and additive(-like) valuation functions, approximation algorithms have been designed using approaches customized for these specific settings, and they fail to extend to more general settings. Hence, no approximation algorithm with factor independent of m is known either for asymmetric agents with additive valuations or for symmetric agents beyond additive(-like) valuations. In this paper, we extend our understanding of the NSW problem to far more general settings. Our main contribution is two approximation algorithms for asymmetric agents with additive and submodular valuations respectively. Both algorithms are simple to understand and involve non-trivial modifications of a greedy repeated matchings approach. Allocations of high valued items are done separately by un-matching certain items and re-matching them, by processes that are different in both algorithms. We show that these approaches achieve approximation factors of O(n) and O(n log n) for additive and submodular case respectively, which is independent of the number of items. For additive valuations, our algorithm outputs an allocation that also achieves the fairness property of envy-free up to one item (EF1). Furthermore, we show that the NSW problem under submodular valuations is strictly harder than all currently known settings with an factor of the hardness of approximation, even for constantly many agents. For this case, we provide a different approximation algorithm that achieves a factor of , hence resolving it completely. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.163"}, {"primary_key": "2683283", "vector": [], "sparse_vector": [], "title": "Faster Algorithms for Edge Connectivity via Random 2-Out Contractions.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "We provide a simple new randomized contraction approach to the global minimum cut problem for simple undirected graphs. The contractions exploit 2-out edge sampling from each vertex rather than the standard uniform edge sampling. We demonstrate the power of our new approach by obtaining better algorithms for sequential, distributed, and parallel models of computation. Our end results include the following randomized algorithms for computing edge connectivity, with high probability1: • Two sequential algorithms with complexities O(m log n) and O(m + n log3 n). These improve on a long line of developments including a celebrated O(m log3 n) algorithm of Karger [STOC'96] and the state of the art O(m log2 n(log log n)2) algorithm of <PERSON><PERSON><PERSON> et al. [SODA'17]. Moreover, our O(m + n log3 n) algorithm is optimal when m = Ω(n log3 n). • An Õ(n0.8D0.2 + n0.9) round distributed algorithm, where D denotes the graph diameter. This improves substantially on a recent breakthrough of <PERSON><PERSON> et al.[STOC'19], which achieved a round complexity of Õ(n1−1/353D1/353 + n1−1/706), hence providing the first sublinear distributed algorithm for exactly computing the edge connectivity. • The first O(1) round algorithm for the massively parallel computation setting with linear memory per machine.", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.77"}, {"primary_key": "2683284", "vector": [], "sparse_vector": [], "title": "Quasi-Polynomial Algorithms for Submodular Tree Orienteering and Other Directed Network Design Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Quasi-Polynomial Algorithms for Submodular Tree Orienteering and Other Directed Network Design Problems<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>.1039 - 1048Chapter DOI:https://doi.org/10.1137/1.9781611975994.63PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the following general network design problem on directed graphs. The input is an asymmetric metric (V, c), root r* ϵ V, monotone submodular function f: 2V → ℝ+ and budget B. The goal is to find an r*-rooted arborescence T of cost at most B that maximizes f (T). Our main result is a very simple quasi-polynomial time -approximation algorithm for this problem, where k ≤ |V| is the number of vertices in an optimal solution. To the best of our knowledge, this is the first non-trivial approximation ratio for this problem. As a consequence we obtain an -approximation algorithm for directed (polymatroid) Steiner tree in quasi-polynomial time. We also extend our main result to a setting with additional length bounds at vertices, which leads to improved -approximation algorithms for the single-source buy-at-bulk and priority Steiner tree problems. For the usual directed Steiner tree problem, our result matches the best previous approximation ratio [15], but improves significantly on the running time: our algorithm takes time whereas the previous algorithm required time. For polymatroid Steiner tree and single-source buy-at-bulk, our result improves prior approximation ratios by a logarithmic factor. For directed priority Steiner tree, our result seems to be the first non-trivial approximation ratio. Under certain complexity assumptions, our approximation ratios are best possible (up to constant factors). Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.63"}, {"primary_key": "2683285", "vector": [], "sparse_vector": [], "title": "The Directed Flat Wall Theorem.", "authors": ["Archontia <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)The Directed Flat Wall TheoremArchontia <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>.239 - 258Chapter DOI:https://doi.org/10.1137/1.9781611975994.15PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract At the core of the Robertson-Seymour Theory of Graph Minors lies a powerful structure theorem which captures, for any fixed graph H, the common structural features of all the graphs not containing H as a minor [15]. An important step towards this structure theorem is the Flat Wall Theorem [14], which has a lot of algorithmic applications (for example, the minor-testing and the disjoint paths problem with fixed number terminals). In this paper, we prove the directed analogue of this Flat Wall Theorem. Our result builds on the recent Directed Grid Theorem by two of the authors (<PERSON><PERSON> and <PERSON>), and we hope that this is an important and significant step toward the directed structure theorem, as with the case for the undirected graph for the graph minor project. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.15"}, {"primary_key": "2683286", "vector": [], "sparse_vector": [], "title": "Hitting Topological Minor Models in Planar Graphs is Fixed Parameter Tractable.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dimitrios M. Thilikos"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Hitting Topological Minor Models in Planar Graphs is Fixed Parameter TractablePetr <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>pp.931 - 950Chapter DOI:https://doi.org/10.1137/1.9781611975994.56PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract For a finite collection of graphs , the -TM-Deletion problem has as input an n-vertex graph G and an integer k and asks whether there exists a set S ⊆ V(G) with |S| ≤ k such that G\\S does not contain any of the graphs in as a topological minor. We prove that for every such , -TM-Deletion is fixed parameter tractable on planar graphs. In particular, we provide an f(h, k) · n2 algorithm where h is an upper bound to the vertices of the graphs in . Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011Key words:Topological minors, irrelevant vertex technique, treewidth, vertex deletion problems", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.56"}, {"primary_key": "2683287", "vector": [], "sparse_vector": [], "title": "Round Complexity of Common Randomness Generation: The Amortized Setting.", "authors": ["<PERSON>", "Madhu <PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Round Complexity of Common Randomness Generation: The Amortized Setting<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.1076 - 1095Chapter DOI:https://doi.org/10.1137/1.9781611975994.66PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this work we study the effect of rounds of interaction on the common randomness generation (CRG) problem. In the CRG problem, two parties, <PERSON> and <PERSON>, receive samples <PERSON> and <PERSON>, respectively, where (<PERSON>, <PERSON>) are drawn jointly from a source distribution μ. The two parties wish to agree on a common random key consisting of many bits of randomness, by exchanging messages that depend on each party's respective input and the previous messages. In this work we study the amortized version of the problem, i.e., the number of bits of communication needed per random bit output by <PERSON> and <PERSON>, in the limit as the number of bits generated tends to infinity. The amortized version of the CRG problem has been extensively studied in the information theory literature, though very little was known about the effect of interaction on this problem. Recently <PERSON><PERSON><PERSON> et al. (SODA 2019) considered the non-amortized version of the problem (so here the goal of the interaction is to generate a fixed number of random bits): they gave a family of sources μr,n parameterized by r,n ϵ ℕ, such that with r + 2 rounds of communication one can generate n bits of common randomness with this source with O(r log n) communication, whereas with roughly r/2 rounds the communication complexity is Ω(n/ poly log n). Note in particular that their source is designed with the target number of bits in mind and hence the result does not apply to the amortized setting. In this work we strengthen the work of Bafna et al. in two ways: First we show that the results extend to the classical amortized setting. We also reduce the gap between the round complexity in the upper and lower bounds to an additive constant. Specifically we show that for every pair r, n ϵ ℕ the (amortized) communication complexity to generate Ω(n) bits of common randomness from the source μr,n using r + 2 rounds of communication is O(r log n) whereas the amortized communication required to generate the same amount of randomness from r rounds is . Our techniques exploit known connections between information complexity and CRG, and the main novelty is our ability to analyze the information complexity of protocols getting inputs from the source μr,n. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.66"}, {"primary_key": "2683289", "vector": [], "sparse_vector": [], "title": "Improved Local Computation Algorithm for Set Cover via Sparsification.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Improved Local Computation Algorithm for Set Cover via Sparsification<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>.2993 - 3011Chapter DOI:https://doi.org/10.1137/1.9781611975994.181PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We design a Local Computation Algorithm (LCA) for the set cover problem. Given a set system where each set has size at most s and each element is contained in at most t sets, the algorithm reports whether a given set is in some fixed set cover whose expected size is O(log s) times the minimum fractional set cover value. Our algorithm requires sO(log s) tO(log s+log log t)) queries. This result improves upon the application of the reduction of [<PERSON><PERSON><PERSON> and <PERSON>, TCS'07] on the result of [<PERSON><PERSON> et al., SODA'06], which leads to a query complexity of (st) O(log s · log t). To obtain this result, we design a parallel set cover algorithm that admits an efficient simulation in the LCA model by using a sparsification technique introduced in [<PERSON>ha<PERSON><PERSON> and Uitto, SODA'19] for the maximal independent set problem. The parallel algorithm adds a random subset of the sets to the solution in a style similar to the PRAM algorithm of [Berger et al., FOCS'89]. However, our algorithm differs in the way that it never revokes its decisions, which results in a fewer number of adaptive rounds. This requires a novel approximation analysis which might be of independent interest. Previous chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.181"}, {"primary_key": "2683290", "vector": [], "sparse_vector": [], "title": "The Online Submodular Cover Problem.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)The Online Submodular Cover ProblemAnu<PERSON><PERSON> and <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.1525 - 1537Chapter DOI:https://doi.org/10.1137/1.9781611975994.94PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the submodular cover problem, we are given a monotone submodular function f: 2N → ℝ+, and we want to pick the min-cost set S such that f (S) = f (N). This captures the set cover problem when f is a coverage function. Motivated by problems in network monitoring and resource allocation, we consider the submodular cover problem in an online setting. As a concrete example, suppose at each time t, a nonnegative monotone submodular function gt is given to us. We define as the sum of all functions seen so far. We need to maintain a submodular cover of these submodular functions f(1), f(2), … f(T) in an online fashion; i.e., we cannot revoke previous choices. Formally, at each time t we produce a set St ⊆ N such that f(t)(St) = f(t)(N)—i.e., this set St is a cover—such that St–1 ⊆ St, so previously decisions to pick elements cannot be revoked. (We actually allow more general sequences {f(t)} of submodular functions, but this sum-of-simpler-submodular-functions case is useful for concreteness.) We give polylogarithmic competitive algorithms for this online submodular cover problem. The competitive ratio on an input sequence of length T is O(ln n ln(T · fmax/fmin)), where fmax and fmin are the largest and smallest marginals for functions f(t), and |N| = n. For the special case of online set cover, our competitive ratio matches that of Alon et al. [AAA+09], which are best possible for polynomial-time online algorithms unless NP ⊆ BPP [Kor04]. Since existing offline algorithms for submodular cover are based on greedy approaches which seem difficult to implement online, the technical challenge is to (approximately) solve the exponential-sized linear programming relaxation for submodular cover, and to round it, both in the online setting. Moreover, to get our competitiveness bounds, we define a (seemingly new) generalization of mutual information to general submodular functions, which we call mutual coverage; we hope this will be useful in other contexts. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.94"}, {"primary_key": "2683291", "vector": [], "sparse_vector": [], "title": "On the Power of Relaxed Local Decoding Algorithms.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)On the Power of Relaxed Local Decoding AlgorithmsTom Gur and Oded LachishTom Gur and Oded Lachishpp.1377 - 1394Chapter DOI:https://doi.org/10.1137/1.9781611975994.83PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A locally decodable code (LDC) C: {0, 1}k → {0, 1}n is an error correcting code that admits algorithms for recovering individual bits of the message by only querying a few bits of a noisy codeword. LDCs found a myriad of applications both in theory and in practice, ranging from probabilistically checkable proofs to distributed storage. However, despite nearly two decades of extensive study, the best known constructions of LDCs with O(1)-query decoding algorithms have super-polynomial blocklength. The notion of relaxed LDCs is a natural relaxation of LDCs, which aims to bypass the foregoing barrier by requiring local decoding of nearly all individual message bits, yet allowing decoding failure (but not error) on the rest. State of the art constructions of O(1)-query relaxed LDCs achieve blocklength n = O (k1+γ) for an arbitrarily small constant γ. Using algorithmic and combinatorial techniques, we prove an impossibility result, showing that codes with blocklength n = k1+o(1) cannot be relaxed decoded with O(1)-query algorithms. This resolves an open problem raised by Goldreich in 2004. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.83"}, {"primary_key": "2683292", "vector": [], "sparse_vector": [], "title": "Deterministic Algorithms for Decremental Approximate Shortest Paths: Faster and Simpler.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "In the decremental (1 + ∈)-Approximate Single-Source ShortEst Path (Sssp) Problem, We Are Given a Graph G = (V, E) With n = |V |, m = |E|, Undergoing Edge Deletions, and a Distinguished Source s ∈ V , and We Are Asked to Process Edge Deletions Efficiently and Answer Queries for Distance EsTimates Dist gG(s, v) for Each v ∈ V , At Any Stage, Such That Distg(s, v) ≤ Dist gG(s, v) ≤ (1 + ∈)Distg(s, v). In The DecreMental (1 + ∈)-Approximate All-Pairs Shortest Path (Apsp) Problem, We Are Asked to Answer Queries for Distance EstiMates Dist gG(u, v) for Every u, v ∈ V . In This Article, We Consider The Problems for Undirected, Unweighted Graphs. We Present a New Deterministic Algorithm for The DecreMental (1 + ∈)-Approximate Sssp Problem That Takes Total Update Time O(Mn0.5+o(1)). Our Algorithm Improves On The Currently Best Algorithm for Dense Graphs By Chech<PERSON> and <PERSON> [Stoc 2016] With Total Update Time Õ(n2) and The Best Existing Algorithm for Sparse Graphs With Running Time Õ(n1.25 √m) [Soda 2017] Whenever m = O(n1.5−o(1)). In Order to Obtain Our New Algorithm, We Develop SevEral New Techniques Including Improved Decremental Cover Data Structures for Graphs, a More Efficient Notion of The Heavy/Light Decomposition Framework Introduced By Chechik and Bernstein and The First Clustering Technique to Maintain a Dynamic Sparse Emulator In The Deterministic SetTing. As a By-Product, We Also Obtain a New Simple DeterMinistic Algorithm for The Decremental (1 + ∈)-Approximate Apsp Problem With Near-Optimal Total Running Time Õ(Mn/∈) matching the time complexity of the sophisticated but rather involved algorithm by Henzinger, Forster and Nanongkai [FOCS 2013].", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.154"}, {"primary_key": "2683293", "vector": [], "sparse_vector": [], "title": "Decremental SSSP in Weighted Digraphs: Faster and Against an Adaptive Adversary.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Given a dynamic digraph G = (V, E) undergoing edge deletions and given s ∈ V and constant ǫ with 0 < ǫ ≤ 1, we consider the problem of maintaining (1+ǫ)-approximate shortest path distances from s to all vertices in G over the sequence of deletions. <PERSON> and <PERSON><PERSON><PERSON> (<PERSON><PERSON>'81) give a deterministic data structure for the exact version of the problem in unweighted graphs with total update time O(mn). <PERSON><PERSON><PERSON> et al. (STOC'14, ICALP'15) give a Monte Carlo data structure for the approximate version with an improved total update time bound of O(mn0.9+o(1) log W) with better bounds for sufficiently dense and sufficiently sparse graphs; here W is the ratio between the largest and smallest edge weight. A drawback of their data structure and in fact of all previous randomized data structures is that they only work against an oblivious adversary, meaning that the sequence of deletions needs to be fixed in advance. This severely limits its application as a black box inside algorithms. We present the following (1 + ǫ)-approximate data structures: 1. the first data structure is Las Vegas and works against an adaptive adversary; it has total expected update time Õ(m2/3n4/3)1 for unweighted graphs and Õ(m3/4n5/4 log W) for weighted graphs, 2. the second data structure is Las Vegas and assumes an oblivious adversary; it has total expected update time Õ(√mn3/2) for unweighted graphs and Õ(m2/3n4/3 log W) for weighted graphs, 3. the third data structure is <PERSON> and is correct w.h.p. against an oblivious adversary; it has total expected update time Õ((mn)7/8 log W) = Õ(mn3/4 log W). Each of our data structures can report the length of a (1+ǫ)-approximate shortest path from s to any query vertex in constant time at any point during the sequence of updates; if the adversary is oblivious, a query can be extended to also report such a path in time proportional to its length. Our update times are faster than those of Henzinger et al. for all graph densities. For instance, when m = Θ(n2), our second result improves their bound from O(n2+3/4+o(1) log W) to Õ(n2+1/2) in the unweighted setting and to Õ(n2+2/3 log W) in the weighted setting. When m = Θ(n), our third result gives an improvement from O(n1+5/6+o(1) log W) to Õ(n1+3/4 log W). Furthermore, our first data structure is the first to improve on the O(mn) bound of Even and Shiloach for all but the sparsest graphs while still working against an adaptive adversary and works even in weighted graphs; this answers an open problem by Henzinger et al.", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.155"}, {"primary_key": "2683294", "vector": [], "sparse_vector": [], "title": "Fully-Dynamic All-Pairs Shortest Paths: Improved Worst-Case Time and Space Bounds.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Fully-Dynamic All-Pairs Shortest Paths: Improved Worst-Case Time and Space BoundsMaximilian <PERSON><PERSON> and <PERSON>-<PERSON><PERSON>enMaximilian <PERSON> and <PERSON>enpp.2562 - 2574Chapter DOI:https://doi.org/10.1137/1.9781611975994.156PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given a directed weighted graph G = (V, E) undergoing vertex insertions and deletions, the All-Pairs Shortest Paths (APSP) problem asks to maintain a data structure that processes updates efficiently and returns after each update the distance matrix to the current version of G. In two breakthrough results, <PERSON><PERSON> and <PERSON><PERSON>res<PERSON> [STOC'03] presented an algorithm that requires Õ(n2) amortized update time, and <PERSON><PERSON> showed in [STOC'05] that worst-case update time Õ(n2+3/4) can be achieved. In this article, we make substantial progress on the problem. We present the following new results: We present the first deterministic data structure that breaks the Õ(n2+3/4) worst-case update time bound by <PERSON><PERSON> which has been standing for almost 15 years. We improve the worst-case update time to Õ(n2+5/7) = Õ(n2.71) and to Õ(n2+3/5) = Õ(n2.6) for unweighted graphs. We present a simple deterministic algorithm with Õ(n2+3/4) worst-case update time (Õ(n2+2/3) for unweighted graphs), and a simple Las-Vegas algorithm with worst-case update time Õ(n2+2/3) (Õ(n2+1/2) for unweighted graphs) that works against a non-oblivious adversary. Both data structures require space Õ(n2). These are the first exact dynamic algorithms with trulysubcubic update time and space usage. This makes significant progress on an open question posed in multiple articles [COCOON'01, STOC '03, ICALP '04, Encyclopedia of Algorithms '08] and is critical to algorithms in practice [TALG '06] where large space usage is prohibitive. Moreover, they match the worst-case update time of the best previous algorithms and the second algorithm improves upon a Monte-Carlo algorithm in a weaker adversary model with the same running time [SODA '17]. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.156"}, {"primary_key": "2683295", "vector": [], "sparse_vector": [], "title": "The Impacts of Dimensionality, Diffusion, and Directedness on Intrinsic Universality in the abstract Tile Assembly Model.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)The Impacts of Dimensionality, Diffusion, and Directedness on Intrinsic Universality in the abstract Tile Assembly Model<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>, <PERSON>, and <PERSON>.2607 - 2624Chapter DOI:https://doi.org/10.1137/1.9781611975994.159PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper we present a series of results related to mathematical models of self-assembling systems of tiles and the impacts that three diverse properties have on their dynamics. In these self-assembling systems, initially unorganized collections of tiles undergo random motion and can bind together, if they collide and enough of their incident glues match, to form assemblies. Here we greatly expand upon a series of prior results which showed that (1) the abstract Tile Assembly Model (aTAM) is intrinsically universal (FOCS 2012), and (2) the class of directed aTAM systems is not intrinsically universal (FOCS 2016). Intrinsic universality (IU) for a model (or class of systems within a model) means that there is a universal tile set which can be used to simulate an arbitrary system within that model (or class). Furthermore, the simulation must not only produce the same resultant structures, it must also maintain the full dynamics of the systems being simulated and display the same behaviors modulo a scale factor. While the FOCS 2012 result showed that the standard, two-dimensional (2D) aTAM is IU, here we show that this is also the case for the three-dimensional (3D) version. Conversely, the FOCS 2016 result showed that the class of aTAM systems which are directed (a.k.a. deterministic, or confluent) is not IU, meaning that there is no universal simulator which can simulate directed aTAM systems while itself always remaining directed, implying that nondeterminism is fundamentally required for such simulations. Here, however, we show that, in 3D, the class of directed aTAM systems is actually IU, i.e. there is a universal directed simulator for them. This implies that the constraint of tiles binding only in the plane forced the necessity of nondeterminism for the simulation of 2D directed systems. This then leads us to continue to explore the impacts of dimensionality and directedness on simulation of tile-based self-assembling systems by considering the influence of more rigid notions of dimensionality. Namely, we introduce the Planar aTAM, where tiles are not only restricted to binding in the plane, but they are also restricted to traveling within the plane, and we prove that the Planar aTAM is not IU, and prove that the class of directed systems within the Planar aTAM also is not IU. Finally, analogous to the Planar aTAM, we introduce the Spatial aTAM, its 3D counterpart, and prove that the Spatial aTAM is IU. This paper adds to a broad set of results which have been used to classify and compare the relative powers of differing models and classes of self-assembling systems, and also helps to further the understanding of the roles of dimension and nondeterminism on the dynamics of self-assembling systems. Furthermore, to prove our positive results we have not only designed, but also implemented what we believe to be the first IU tile set ever implemented and simulated in any tile assembly model, and have made it, along with a simulator which can demonstrate it, freely available. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.159"}, {"primary_key": "2683296", "vector": [], "sparse_vector": [], "title": "Factors and loose Hamilton cycles in sparse pseudo-random hypergraphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Factors and loose Hamilton cycles in sparse pseudo-random hypergraphs<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.702 - 717Chapter DOI:https://doi.org/10.1137/1.9781611975994.43PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We investigate the emergence of spanning structures in sparse pseudo-random k-uniform hypergraphs, using the following comparatively weak notion of pseudorandomness. A k-uniform hypergraph H on n vertices is called (p, α, ε)-pseudo-random if for all not necessarily disjoint sets A1, …, Ak ⊂ V (H) with |A1|···|Ak| > αnk we have e(A1, …, Ak) = (1 ± ε)p|A1|···|Ak|. For any linear k-uniform F we provide a bound on α = α(n) in terms of p = p(n) and F, such that (under natural divisibility assumptions on n) any (p, α, o(1))-pseudo-random n-vertex H with a mild minimum degree condition contains an F-factor. The approach also enables us to establish the existence of loose Hamilton cycles in sufficiently pseudo-random hypergraphs and all results imply corresponding bounds for stronger notions of hypergraph pseudo-randomness such as jumbledness or large spectral gap. As a consequence of our results, perfect matchings appear at α = o(pk) while loose Hamilton cycles appear at α = o(pk–1). This extends the works of Lenz–Mubayi, and Lenz–Mubayi–Mycroft who studied the analogous problems in the dense setting. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.43"}, {"primary_key": "2683297", "vector": [], "sparse_vector": [], "title": "Finding Perfect Matchings in Dense Hypergraphs.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Finding Perfect Matchings in Dense Hypergraphs<PERSON><PERSON> and <PERSON> and <PERSON>.2366 - 2377Chapter DOI:https://doi.org/10.1137/1.9781611975994.145PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We show that for any integers k ≥ 3 and c ≥ 0 there is a polynomial-time algorithm, that given any n-vertex k-uniform hypergraph H with minimum codegree at least n/k – c, finds either a perfect matching in H or a certificate that no perfect matching exists. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.145"}, {"primary_key": "2683298", "vector": [], "sparse_vector": [], "title": "Adaptive Quantum Simulated Annealing for Bayesian Inference and Estimating Partition Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Adaptive Quantum Simulated Annealing for Bayesian Inference and Estimating Partition Functions<PERSON><PERSON> <PERSON><PERSON> and <PERSON> and <PERSON>.193 - 212Chapter DOI:https://doi.org/10.1137/1.9781611975994.12PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Markov chain Monte Carlo algorithms have important applications in counting problems and in machine learning problems, settings that involve estimating quantities that are difficult to compute exactly. How much can quantum computers speed up classical Markov chain algorithms? In this work we consider the problem of speeding up simulated annealing algorithms, where the stationary distributions of the Markov chains are Gibbs distributions at temperatures specified according to an annealing schedule. We construct a quantum algorithm that both adaptively constructs an annealing schedule and quantum samples at each temperature. Our adaptive annealing schedule roughly matches the length of the best classical adaptive annealing schedules and improves on nonadaptive temperature schedules by roughly a quadratic factor. Our dependence on the Markov chain gap matches other quantum algorithms and is quadratically better than what classical Markov chains achieve. Our algorithm is the first to combine both of these quadratic improvements. Like other quantum walk algorithms, it also improves on classical algorithms by producing \"qsamples\" instead of classical samples. This means preparing quantum states whose amplitudes are the square roots of the target probability distribution. In constructing the annealing schedule we make use of amplitude estimation, and we introduce a method for making amplitude estimation nondestructive at almost no additional cost, a result that may have independent interest. Finally we demonstrate how this quantum simulated annealing algorithm can be applied to the problems of estimating partition functions and Bayesian inference. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.12"}, {"primary_key": "2683299", "vector": [], "sparse_vector": [], "title": "Inference from Auction Prices.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Inference from Auction Prices<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.2472 - 2491Chapter DOI:https://doi.org/10.1137/1.9781611975994.151PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Econometric inference allows an analyst to back out the values of agents in a mechanism from the rules of the mechanism and bids of the agents. This paper gives an algorithm to solve the problem of inferring the values of agents in a dominant-strategy mechanism from: the social choice function implemented by the mechanism and the per-unit prices paid by the agents (the agent bids are not observed). For single-dimensional agents, this inference problem is a multi-dimensional inversion of the payment identity and is feasible only if the payment identity is uniquely invertible. The inversion is unique for single-unit proportional weights social choice functions (common, for example, in bandwidth allocation); and its inverse can be found efficiently. This inversion is not unique for social choice functions that exhibit complementarities. Of independent interest, we extend a result of <PERSON> (1965), that the Nash equilbria of \"concave games\" are unique and pure, to an alternative notion of concavity based on Gale and Nikaido (1965). Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.151"}, {"primary_key": "2683300", "vector": [], "sparse_vector": [], "title": "Combinatorial generation via permutation languages.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Combinatorial generation via permutation languages<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>.1214 - 1225Chapter DOI:https://doi.org/10.1137/1.9781611975994.74PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this work we present a general and versatile algorithmic framework for exhaustively generating a large variety of different combinatorial objects, based on encoding them as permutations. This approach provides a unified view on many known results and allows us to prove many new ones. In particular, we obtain the following four classical Gray codes as special cases: the <PERSON><PERSON> algorithm to generate all permutations of an n-element set by adjacent transpositions; the binary reflected Gray code to generate all n-bit strings by flipping a single bit in each step; the Gray code for generating all n-vertex binary trees by rotations due to <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>; the Gray code for generating all partitions of an n-element ground set by element exchanges due to <PERSON>. We present two distinct applications for our new framework: The first main application is the generation of patternavoiding permutations, yielding new Gray codes for different families of permutations that are characterized by the avoidance of certain classical patterns, (bi)vincular patterns, barred patterns, Bruhat-restricted patterns, mesh patterns, monotone and geometric grid classes, and many others. We thus also obtain new Gray code algorithms for the combinatorial objects that are in bijection to these permutations, in particular for five different types of geometric rectangulations, also known as floorplans, which are divisions of a square into n rectangles subject to certain restrictions. The second main application of our framework are lattice congruences of the weak order on the symmetric group Sn. Recently, Pilaud and Santos realized all those lattice congruences as (n – 1)-dimensional polytopes, called quotientopes, which generalize hypercubes, associahedra, permutahedra etc. Our algorithm generates the equivalence classes of each of those lattice congruences, by producing a Hamilton path on the skeleton of the corresponding quotientope, yielding a constructive proof that each of these highly symmetric graphs is Hamiltonian. We thus also obtain a provable notion of optimality for the Gray codes obtained from our framework: They translate into walks along the edges of a polytope. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.74"}, {"primary_key": "2683301", "vector": [], "sparse_vector": [], "title": "Worst-Case Polylog Incremental SPQR-trees: Embeddings, Planarity, and Triconnectivity.", "authors": ["<PERSON>", "<PERSON>"], "summary": "We show that every labelled planar graph G can be assigned a canonical embedding φ(G), such that for any planar G’ that differs from G by the insertion or deletion of one edge, the number of local changes to the combinatorial embedding needed to get from φ(G) to φ(G’) is (log n). In contrast, there exist embedded graphs where Ω(n) changes are necessary to accommodate one inserted edge. We provide a matching lower bound of Ω(log n) local changes, and although our upper bound is worst-case, our lower bound hold in the amortized case as well. Our proof is based on BC trees and SPQR trees, and we develop pre-split variants of these for general graphs, based on a novel biased heavy-path decomposition, where the structural changes corresponding to edge insertions and deletions in the underlying graph consist of at most (log n) basic operations of a particularly simple form. As a secondary result, we show how to maintain the pre-split trees under edge insertions in the underlying graph deterministically in worst case (log3 n) time. Using this, we obtain deterministic data structures for incremental planarity testing, incremental planar embedding, and incremental triconnectivity, that each have worst case (log3 n) update and query time, answering an open question by <PERSON> and <PERSON><PERSON> from 1998.", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.146"}, {"primary_key": "2683302", "vector": [], "sparse_vector": [], "title": "The Communication Complexity of Set Intersection and Multiple Equality Testing.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "In this paper we explore fundamental problems in randomized communication complexity such as computing Set Intersection on sets of size $k$ and Equality Testing between vectors of length $k$. Sa\\u{g}lam and <PERSON><PERSON><PERSON> and <PERSON> et al. showed that for these types of problems, one can achieve optimal communication volume of $O(k)$ bits, with a randomized protocol that takes $O(\\log^* k)$ rounds. Aside from rounds and communication volume, there is a \\emph{third} parameter of interest, namely the \\emph{error probability} $p_{\\mathrm{err}}$. It is straightforward to show that protocols for Set Intersection or Equality Testing need to send $\\Omega(k + \\log p_{\\mathrm{err}}^{-1})$ bits. Is it possible to simultaneously achieve optimality in all three parameters, namely $O(k + \\log p_{\\mathrm{err}}^{-1})$ communication and $O(\\log^* k)$ rounds? In this paper we prove that there is no universally optimal algorithm, and complement the existing round-communication tradeoffs with a new tradeoff between rounds, communication, and probability of error. In particular: 1. Any protocol for solving Multiple Equality Testing in $r$ rounds with failure probability $2^{-E}$ has communication volume $\\Omega(Ek^{1/r})$. 2. There exists a protocol for solving Multiple Equality Testing in $r + \\log^*(k/E)$ rounds with $O(k + rEk^{1/r})$ communication, thereby essentially matching our lower bound and that of Sa\\u{g}lam and Tardos. Our original motivation for considering $p_{\\mathrm{err}}$ as an independent parameter came from the problem of enumerating triangles in distributed ($\\textsf{CONGEST}$) networks having maximum degree $\\Delta$. We prove that this problem can be solved in $O(\\Delta/\\log n + \\log\\log \\Delta)$ time with high probability $1-1/\\operatorname{poly}(n)$.", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.105"}, {"primary_key": "2683303", "vector": [], "sparse_vector": [], "title": "Efficiently list-edge coloring multigraphs asymptotically optimally.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Efficiently list-edge coloring multigraphs asymptotically optimally<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.2319 - 2336Chapter DOI:https://doi.org/10.1137/1.9781611975994.142PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We give polynomial time algorithms for the seminal results of <PERSON> [19, 20], who showed that the <PERSON>-<PERSON> and List-Coloring conjectures for (list-)edge coloring multigraphs hold asymptotically. <PERSON>'s arguments are based on the probabilistic method and are nonconstructive. Our key insight is that we can combine sophisticated techniques due to <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> [2] for the analysis of local search algorithms with correlation decay properties of the probability spaces on matchings used by <PERSON> in order to construct efficient edge-coloring algorithms. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.142"}, {"primary_key": "2683304", "vector": [], "sparse_vector": [], "title": "Weighted Completion Time Minimization for Unrelated Machines via Iterative Fair Contention Resolution.", "authors": ["Sungjin Im", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Weighted Completion Time Minimization for Unrelated Machines via Iterative Fair Contention ResolutionSungjin Im and <PERSON><PERSON>m and <PERSON><PERSON>.2790 - 2809Chapter DOI:https://doi.org/10.1137/1.9781611975994.170PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We give a 1.488-approximation for the classic scheduling problem of minimizing total weighted completion time on unrelated machines. This is a considerable improvement on the recent breakthrough of (1.5 – 10−7)-approximation (STOC 2016, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) and the follow-up result of (1.5 – 1/6000)-approximation (FOCS 2017, Li). <PERSON><PERSON> et al. introduced a novel rounding scheme yielding strong negative correlations for the first time and applied it to the scheduling problem to obtain their breakthrough, which resolved the open problem if one can beat out the long-standing 1.5-approximation barrier based on independent rounding. Our key technical contribution is in achieving significantly stronger negative correlations via iterative fair contention resolution, which is of independent interest. Previously, <PERSON><PERSON> et al. obtained strong negative correlations via a variant of pipage type rounding and <PERSON> used it as a black box. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.170"}, {"primary_key": "2683305", "vector": [], "sparse_vector": [], "title": "Composable Core-sets for Determinant Maximization Problems via Spectral Spanners.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Composable Core-sets for Determinant Maximization Problems via Spectral Spanners<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1675 - 1694Chapter DOI:https://doi.org/10.1137/1.9781611975994.103PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study a generalization of classical combinatorial graph spanners to the spectral setting. Given a set of vectors V ⊆ ℝd, we say a set U ⊆ V is an α-spectral kspanner, for k ≤ d, if for all v ϵ V there is a probability distribution μv supported on U such that where for two matrices A, B ϵ ℝd×d we write iff the sum of the bottom d – k + 1 eigenvalues of B – A is nonnegative. In particular, iff . We show that any set V has an Õ(k)-spectral spanner of size Õ(k) and this bound is almost optimal in the worst case. We use spectral spanners to study composable coresets for spectral problems. We show that for many objective functions one can use a spectral spanner, independent of the underlying function, as a core-set and obtain almost optimal composable core-sets. For example, for the k-determinant maximization problem, we obtain an Õ(k)k-composable core-set, and we show that this is almost optimal in the worst case. Our algorithm is a spectral analogue of the classical greedy algorithm for finding (combinatorial) spanners in graphs. We expect that our spanners find many other applications in distributed or parallel models of computation. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.103"}, {"primary_key": "2683307", "vector": [], "sparse_vector": [], "title": "Optimal Space-Depth Trade-Off of CNOT Circuits in Quantum Logic Synthesis.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Sun", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Optimal Space-Depth Trade-Off of CNOT Circuits in Quantum Logic Synthesis<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.213 - 229Chapter DOI:https://doi.org/10.1137/1.9781611975994.13PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Due to the decoherence of the state-of-the-art physical implementations of quantum computers, it is essential to parallelize the quantum circuits to reduce their depth. Two decades ago, <PERSON> and <PERSON> [1] demonstrated that additional qubits (or ancillae) could be used to design \"shallow\" parallel circuits for quantum operators. They proved that any n-qubit CNOT circuit could be parallelized to O(log n) depth, with O(n2) ancillae. However, the near-term quantum technologies can only support limited amount of qubits, making space-depth trade-off a fundamental research subject for quantum-circuit synthesis. In this work, we establish an asymptotically optimal space-depth trade-off for the design of CNOT circuits. We prove that for any m ≥ 0, any n-qubit CNOT circuit can be parallelized to depth, with m ancillae. We show that this bound is tight by a counting argument, and further show that even with arbitrary two-qubit quantum gates to approximate CNOT circuits, the depth lower bound still meets our construction, illustrating the robustness of our result. Our work improves upon two previous results, one by Moore and Nilsson [1] for O(log n)-depth quantum synthesis, and one by Patel, Markov, and Hayes [2] for m =0: for the former, we reduce the need for ancillae by a factor of log2 n by showing that m = O(n2 / log2 n) additional qubits — which is asymptotically optimal — suffice to build O(log n)-depth, O(n2 / log n)-size CNOT circuits; for the later, we reduce the depth by a factor of n to the asymptotically optimal bound . Our results can be directly extended to stabilizer circuits using an earlier result by Aaronson and Gottesman [3]. In addition, we provide relevant hardness evidence for synthesis optimization of CNOT circuits in term of both size and depth. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.13"}, {"primary_key": "2683308", "vector": [], "sparse_vector": [], "title": "Spherical Discrepancy Minimization and Algorithmic Lower Bounds for Covering the Sphere.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Spherical Discrepancy Minimization and Algorithmic Lower Bounds for Covering the S<PERSON><PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.874 - 891Chapter DOI:https://doi.org/10.1137/1.9781611975994.53PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Inspired by the boolean discrepancy problem, we study the following optimization problem which we term Spherical Discrepancy: given m unit vectors υ1, …,υm, find another unit vector x that minimizes maxi 〈x, υi〉. We show that Spherical Discrepancy is APX-hard and develop a multiplicative weights-based algorithm that achieves optimal worst-case error bounds up to lower order terms. We use our algorithm to give the first non-trivial lower bounds for the problem of covering a hypersphere by hyperspherical caps of uniform volume at least . We accomplish this by proving a related covering bound in Gaussian space and showing that in this large cap regime the bound transfers to spherical space. Up to a log factor, our lower bounds match known upper bounds in the large cap regime. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.53"}, {"primary_key": "2683309", "vector": [], "sparse_vector": [], "title": "Exponential Separations in Local Differential Privacy.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Exponential Separations in Local Differential PrivacyM<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.515 - 527Chapter DOI:https://doi.org/10.1137/1.9781611975994.31PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We prove a general connection between the communication complexity of two-player games and the sample complexity of their multi-player locally private analogues. We use this connection to prove sample complexity lower bounds for locally differentially private protocols as straightforward corollaries of results from communication complexity. In particular, we 1) use a communication lower bound for the hidden layers problem to prove an exponential sample complexity separation between sequentially and fully interactive locally private protocols, and 2) use a communication lower bound for the pointer chasing problem to prove an exponential sample complexity separation between k-round and (k + 1)-round sequentially interactive locally private protocols, for every k. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.31"}, {"primary_key": "2683310", "vector": [], "sparse_vector": [], "title": "Adaptive Shivers Sort: An Alternative Sorting Algorithm.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Adaptive Shivers Sort: An Alternative Sorting AlgorithmVincent <PERSON>.1639 - 1654Chapter DOI:https://doi.org/10.1137/1.9781611975994.101PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We present a new sorting algorithm, called adaptive ShiversSort, that exploits the existence of monotonic runs for sorting efficiently partially sorted data. This algorithm is a variant of the well-known algorithm TimSort, which is the sorting algorithm used in standard libraries of programming languages such as Python or Java (for non-primitive types). More precisely, adaptive ShiversSort is a so-called k-aware merge-sort algorithm, a class that was introduced by <PERSON><PERSON> and K<PERSON>p that captures \"TimSort-like\" algorithms. In this article, we prove that, although adaptive ShiversSort is simple to implement and differs only slightly from TimSort, its computational cost, in number of comparisons performed, is optimal within the class of natural merge-sort algorithms, up to a small additive linear term: this makes adaptive ShiversSort the first k-aware algorithm to benefit from this property, which is also a 33% improvement over <PERSON>Sort's worst-case. This suggests that adaptive ShiversSort could be a strong contender for being used instead of TimSort. Then, we investigate the optimality of k-aware algorithms: we give lower and upper bounds on the best approximation factors of such algorithms, compared to optimal stable natural merge-sort algorithms. In particular, we design generalisations of adaptive ShiversSort whose computational costs are optimal up to arbitrarily small multiplicative factors. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.101"}, {"primary_key": "2683311", "vector": [], "sparse_vector": [], "title": "Quantifying the Burden of Exploration and the Unfairness of Free Riding.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Quantifying the Burden of Exploration and the Unfairness of Free RidingChr<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.1892 - 1904Chapter DOI:https://doi.org/10.1137/1.9781611975994.116PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the multi-armed bandit setting with a twist. Rather than having just one decision maker deciding which arm to pull in each round, we have n different decision makers (agents). In the simple stochastic setting, we show that a \"free-riding\" agent observing another \"self-reliant\" agent can achieve just O(1) regret, as opposed to the regret lower bound of Ω(log t) when one decision maker is playing in isolation. This result holds whenever the self-reliant agent's strategy satisfies either one of two assumptions: (1) each arm is pulled at least γ ln t times in expectation for a constant γ that we compute, or (2) the self-reliant agent achieves o(t) realized regret with high probability. Both of these assumptions are satisfied by standard zero-regret algorithms. Under the second assumption, we further show that the free rider only needs to observe the number of times each arm is pulled by the self-reliant agent, and not the rewards realized. In the linear contextual setting, each arm has a distribution over parameter vectors, each agent has a context vector, and the reward realized when an agent pulls an arm is the inner product of that agent's context vector with a parameter vector sampled from the pulled arm's distribution. We show that the free rider can achieve O(1) regret in this setting whenever the free rider's context is a small (in L2-norm) linear combination of other agents' contexts and all other agents pull each arm Ω(log t) times with high probability. Again, this condition on the self-reliant players is satisfied by standard zero-regret algorithms like UCB. We also prove a number of lower bounds. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.116"}, {"primary_key": "2683312", "vector": [], "sparse_vector": [], "title": "Even maps, the <PERSON> number and representations of graphs.", "authors": ["V<PERSON>j<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Even maps, the <PERSON> number and representations of graphs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Kaluẑa and <PERSON> Kaluẑa and <PERSON>pp.2642 - 2657Chapter DOI:https://doi.org/10.1137/1.9781611975994.161PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract <PERSON> and <PERSON><PERSON><PERSON><PERSON> introduced a graph parameter σ, which coincides with the more famous <PERSON> graph parameter µ for small values. However, the definition of σ is much more geometric/topological directly reflecting embeddability properties of the graph. They proved µ(G) ≤ σ(G) + 2 and conjectured µ(G) ≤ σ(G) for any graph G. We confirm this conjecture. As far as we know, this is the first topological upper bound on µ(G) which is, in general, tight. Equality between µ and σ does not hold in general as <PERSON> and <PERSON><PERSON> showed that there is a graph G with µ(G) ≤ 18 and σ(G) ≥ 20. We show that the gap appears on much smaller values, namely, we exhibit a graph H for which µ(H) ≤ 7 and σ(H) ≥ 8. We also prove that, in general, the gap can be large: The incidence graphs Hq of finite projective planes of order q satisfy µ(Hq) ϵ O(q3/2) and σ(Hq) ≥ q2. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.161"}, {"primary_key": "2683313", "vector": [], "sparse_vector": [], "title": "Competitive Analysis with a <PERSON><PERSON> and the Secretary Problem.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Competitive Analysis with a <PERSON><PERSON> and the Secretary <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.2082 - 2095Chapter DOI:https://doi.org/10.1137/1.9781611975994.128PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We extend the standard online worst-case model to accommodate past experience which is available to the online player in many practical scenarios. We do this by revealing a random sample of the adversarial input to the online player ahead of time. The online player competes with the expected optimal value on the part of the input that arrives online. Our model bridges between existing online stochastic models (e.g., items are drawn i.i.d. from a distribution) and the online worst-case model. We also extend in a similar manner (by revealing a sample) the online random-order model. We study the classical secretary problem in our new models. In the worst-case model we present a simple online algorithm with optimal competitive-ratio for any sample size. In the random-order model, we also give a simple online algorithm with an almost tight competitive-ratio for small sample sizes. Interestingly, we prove that for a large enough sample, no algorithm can be simultaneously optimal both in the worst-cast and random-order models. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.128"}, {"primary_key": "2683314", "vector": [], "sparse_vector": [], "title": "Fast and Space Efficient Spectral Sparsification in Dynamic Streams.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Fast and Space Efficient Spectral Sparsification in Dynamic Streams<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.1814 - 1833Chapter DOI:https://doi.org/10.1137/1.9781611975994.111PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In this paper, we resolve the complexity problem of spectral graph sparcification in dynamic streams up to polylogarithmic factors. Using a linear sketch we design a streaming algorithm that uses Õ(n) space, and with high probability, recovers a spectral sparsifier from the sketch in Õ(n) time.1 Prior results either achieved near optimal Õ(n) space, but Ω(n2) recovery time [<PERSON><PERSON><PERSON> et al. '14], or ran in o(n2) time, but used polynomially suboptimal space [<PERSON><PERSON> et al '13]. Our main technical contribution is a novel method for recovering graph edges with high effective resistance from a linear sketch. We show how to do so in nearly linear time by 'bucketing' vertices of the input graph into clusters using a coarse approximation to the graph's effective resistance metric. A second main contribution is a new pseudorandom generator (PRG) for linear sketching algorithms. Constructed from a locally computable randomness extractor, our PRG stretches a seed of Õ(n) random bits polynomially in length with just logO(1) n run-time cost per evaluation. This improves on Nisan's commonly used PRG, which in our setting would require Õ(n) time per evaluation. Our faster PRG is essential to simultaneously achieving near optimal space and time complexity. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.111"}, {"primary_key": "2683315", "vector": [], "sparse_vector": [], "title": "Space Efficient Approximation to Maximum Matching Size from Uniform Edge Samples.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>Fard", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Space Efficient Approximation to Maximum Matching Size from Uniform Edge <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.1753 - 1772Chapter DOI:https://doi.org/10.1137/1.9781611975994.107PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given a source of iid samples of edges of an input graph G with n vertices and m edges, how many samples does one need to compute a constant factor approximation to the maximum matching size in G? Moreover, is it possible to obtain such an estimate in a small amount of space? We show that this problem cannot be solved using a nontrivially sublinear (in m) number of samples: m1−o(1) samples are needed. On the other hand, O(log2 n) bits of space suffice to compute an estimate. Our main technical tool is a new peeling type algorithm for matching and its local simulation. We show that a delicate balance between exploration depth and sampling rate allows our simulation to not lose precision over a logarithmic number of levels of recursion and achieve a constant factor approximation. Our algorithm also yields a constant factor approximate local computation algorithm (LCA) for matching with O(d log n) exploration starting from any vertex. Previous approaches were based on local simulations of randomized greedy, which take O(d) time in expectation over the starting vertex or edge (Yoshida et al'09, Onak et al'12), and could not achieve a better than d2 run-time. Interestingly, we also show that unlike our algorithm, the local simulation of randomized greedy that is the basis of the most efficient prior results does take (d2) ≫ O(d log n) time for a worst case edge even for . Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.107"}, {"primary_key": "2683317", "vector": [], "sparse_vector": [], "title": "A nearly 5/3-approximation FPT Algorithm for Min-k-Cut.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Bing<PERSON> Lin"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A nearly 5/3-approximation FPT Algorithm for Min-k-Cut<PERSON>en<PERSON><PERSON> and <PERSON>kai LinKen-<PERSON><PERSON> and Bing<PERSON> Linpp.990 - 999Chapter DOI:https://doi.org/10.1137/1.9781611975994.59PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given an edged-weighted graph G, the min-k-cut problem asks for a set of edges with minimum total weight whose removal breaks the graph G into at least k connected components. It is well-known that the greedy algorithm can find a (2 – 2/k)-approximation of the min-k-cut in polynomial time. Assuming the Small Set Expansion Hypothesis (SSEH), no polynomial time algorithm can achieve an approximation ratio better than two [9]. Recently, <PERSON>, <PERSON> and <PERSON> [5] gave a 1.9997-approximation FPT algorithm for the min-k-cut parameterized by k. They also improved this approximation ratio to 1.81 [4]. We generalize their proof techniques and show that the min-k-cut has a nearly 5/3-approximation FPT algorithm. Our proof is self-contained and much shorter than that of <PERSON>, <PERSON> and <PERSON>. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.59"}, {"primary_key": "2683318", "vector": [], "sparse_vector": [], "title": "A New Lower Bound on Hadwiger-Debrunner Numbers in the Plane.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A New Lower Bound on Hadwiger-Debrunner Numbers in the PlaneChaya Keller and S<PERSON><PERSON> and <PERSON><PERSON><PERSON>.1155 - 1169Chapter DOI:https://doi.org/10.1137/1.9781611975994.70PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A family of sets F is said to satisfy the (p, q) property if among any p sets in F some q have a non-empty intersection. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (1957) conjectured that for any p ≥ q ≥ d +1 there exists an integer c = HDd(p,q), such that any finite family of convex sets in ℝd that satisfies the (p, q) property can be pierced by at most c points. In a celebrated result from 1992, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> proved the conjecture. However, obtaining sharp bounds on HDd(p,q), known as 'the Hadwiger-Debrunner numbers', is still a major open problem in discrete and computational geometry. The best currently known upper bound on the <PERSON><PERSON><PERSON>-<PERSON><PERSON>runner numbers in the plane is (for any σ > 0 and p ≥ q ≥ q0(δ)), obtained by combining results of <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> (SODA 2017) and of <PERSON> (FOCS 2018). The best lower bound is , obtained by <PERSON>ukh, <PERSON>ou<PERSON>ek and <PERSON>vasch more than 10 years ago. In this paper we improve the lower bound significantly by showing that . Furthermore, the bound is obtained by a family of lines and is tight for all families that have a bounded VC-dimension. Unlike previous bounds on the Hadwiger-Debrunner numbers, which mainly used the weak epsilon-net theorem, our bound stems from a surprising connection of the (p, q) problem to an old problem of Erdős on points in general position in the plane. We use a novel construction for Erdős' problem, obtained recently by Balogh and Solymosi using the hypergraph container method, to get the lower bound on HD2(p,3). We then generalize the bound to HD2(p, q) for q ≥ 3. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.70"}, {"primary_key": "2683319", "vector": [], "sparse_vector": [], "title": "Hyperbolic intersection graphs and (quasi)-polynomial time.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Hyperbolic intersection graphs and (quasi)-polynomial time<PERSON><PERSON><PERSON>k<PERSON>ludi-Bakpp.1621 - 1638Chapter DOI:https://doi.org/10.1137/1.9781611975994.100PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study unit ball graphs (and, more generally, so-called noisy uniform ball graphs) in d-dimensional hyperbolic space, which we denote by ℍd. Using a new separator theorem, we show that unit ball graphs in ℍd enjoy similar properties as their Euclidean counterparts, but in one dimension lower: many standard graph problems, such as Independent Set, Dominating Set, Steiner Tree, and Hamiltonian Cycle can be solved in 2O(n1–1/(d–1)) time for any fixed d ≫ 3, while the same problems need 2O(n1–1/d) time in ℝd. We also show that these algorithms in ℍd are optimal up to constant factors in the exponent under ETH. This drop in dimension has the largest impact in ℍ2, where we introduce a new technique to bound the treewidth of noisy uniform disk graphs. The bounds yield quasi-polynomial (nO(log n)) algorithms for all of the studied problems, while in the case of Hamiltonian Cycle and 3-Coloring we even get polynomial time algorithms. Furthermore, if the underlying noisy disks in ℍ2 have constant maximum degree, then all studied problems can be solved in polynomial time. This contrasts with the fact that these problems require time under ETH in constant maximum degree Euclidean unit disk graphs. Finally, we complement our quasi-polynomial algorithm for Independent Set in noisy uniform disk graphs with a matching nΩ(log n) lower bound under ETH. This shows that the hyperbolic plane is a potential source of NP-intermediate problems. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.100"}, {"primary_key": "2683320", "vector": [], "sparse_vector": [], "title": "Complexity and Parametric Computation of Equilibria in Atomic Splittable Congestion Games via Weighted Block Laplacians.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Complexity and Parametric Computation of Equilibria in Atomic Splittable Congestion Games via Weighted Block LaplaciansMax Klimm and <PERSON> Klimm and <PERSON>.2728 - 2747Chapter DOI:https://doi.org/10.1137/1.9781611975994.166PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We settle the complexity of computing an equilibrium in atomic splittable congestion games with player-specific affine cost functions le,i(x) = ae,ix + be,i showing that it is PPAD-complete. To prove that the problem is contained in PPAD, we develop a homotopy method that traces an equilibrium for varying flow demands of the players. A key technique is to describe the evolution of the equilibrium locally by a novel block Laplacian matrix. This leads to a path following formulation where states correspond to supports that are feasible for some demands and neighboring supports are feasible for increased or decreased flow demands. A closer investigation of the block Laplacian system allows to orient the states giving rise to unique predecessor and successor states thus putting the problem into PPAD. For the PPAD-hardness, we reduce from computing an approximate equilibrium of a bimatrix win-lose game. As a byproduct of our reduction we further show that computing a multiclass Wardrop equilibrium with class-dependent affine cost functions is PPAD-complete as well. As a byproduct of our PPAD-completeness proof, we obtain an algorithm that computes all equilibria parametrized by the players' flow demands. For player-specific costs, this computation may require several increases and decreases of the demands leading to an algorithm that runs in polynomial space but exponential time. For player-independent costs only demand increases are necessary. If the coefficients be,i are in general position, this yields an algorithm computing all equilibria as a function of the flow demand running in time polynomial in the output. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.166"}, {"primary_key": "2683321", "vector": [], "sparse_vector": [], "title": "Dominantly Truthful Multi-task Peer Prediction with a Constant Number of Tasks.", "authors": ["Yuqing Kong"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Dominantly Truthful Multi-task Peer Prediction with a Constant Number of TasksYuqing KongYuqing Kongpp.2398 - 2411Chapter DOI:https://doi.org/10.1137/1.9781611975994.147PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the setting where participants are asked multiple similar possibly subjective multi-choice questions (e.g. Do you like Panda Express? Y/N; do you like Chick-fil-A? Y/N), a series of peer prediction mechanisms are designed to incentivize honest reports and some of them achieve dominantly truthfulness: truth-telling is a dominant strategy and strictly dominate other \"non-permutation strategy\" with some mild conditions. However, a major issue hinders the practical usage of those mechanisms: they require the participants to perform an infinite number of tasks. When the participants perform a finite number of tasks, these mechanisms only achieve approximated dominant truthfulness. The existence of a dominantly truthful multi-task peer prediction mechanism that only requires a finite number of tasks remains to be an open question that may have a negative result, even with full prior knowledge. This paper answers this open question by proposing a new mechanism, Determinant based Mutual Information Mechanism (DMI-Mechanism), that is dominantly truthful when the number of tasks is ≥ 2C. C is the number of choices for each question (C = 2 for binary-choice questions). DMI-Mechanism also pays truth-telling higher than any strategy profile and strictly higher than uninformative strategy profiles (informed truthfulness). In addition to the truthfulness properties, DMI-Mechanism is also easy to implement since it does not require any prior knowledge (detail-free) and only requires ≥ 2 participants. The core of DMI-Mechanism is a novel information measure, Determinant based Mutual Information (DMI). DMI generalizes Shannon's mutual information and the square of DMI has a simple unbiased estimator. In addition to incentivizing honest reports, DMI-Mechanism can also be transferred into an information evaluation rule that identifies high-quality information without verification when there are ≥ 3 participants. To the best of our knowledge, DMI-Mechanism is both the first detail-free informed-truthful mechanism and the first dominantly truthful mechanism that works for a finite number of tasks, not to say a small constant number of tasks. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.147"}, {"primary_key": "2683322", "vector": [], "sparse_vector": [], "title": "Ultimate greedy approximation of independent sets in subcubic graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Ultimate greedy approximation of independent sets in subcubic graphs<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>, and <PERSON>.1436 - 1455Chapter DOI:https://doi.org/10.1137/1.9781611975994.87PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the approximability of the maximum size independent set (MIS) problem in bounded degree graphs. This is one of the most classic and widely studied NP-hard optimization problems. It is known for its inherent hardness of approximation. We focus on the well known minimum degree greedy algorithm for this problem. This algorithm iteratively chooses a minimum degree vertex in the graph, adds it to the solution and removes its neighbors, until the remaining graph is empty. The approximation ratios of this algorithm have been very widely studied, where it is augmented with an advice that tells the greedy which minimum degree vertex to choose if it is not unique. Our main contribution is a new mathematical theory for the design of such greedy algorithms with efficiently computable advice and for the analysis of their approximation ratios. With this new theory we obtain the ultimate approximation ratio of 5/4 for greedy on graphs with maximum degree 3, which completely solves the open problem from the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (1995). Our algorithm is the fastest currently known algorithm with this approximation ratio on such graphs. We also obtain a simple and short proof of the (D+2)/3-approximation ratio of any greedy on graphs with maximum degree D, the result proved previously by <PERSON>dórs<PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1994). We almost match this ratio by showing a lower bound of (D+1)/3 on the ratio of any greedy algorithm that can use any advice. We apply our new algorithm to the minimum vertex cover problem on graphs with maximum degree 3 to obtain a substantially faster 6/5-approximation algorithm than the one currently known. We complement our positive, upper bound results with negative, lower bound results which prove that the problem of designing good advice for greedy is computationally hard and even hard to approximate on various classes of graphs. These results significantly improve on such previously known hardness results. Moreover, these results suggest that obtaining the upper bound results on the design and analysis of greedy advice is non-trivial. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.87"}, {"primary_key": "2683323", "vector": [], "sparse_vector": [], "title": "Faster Deterministic Distributed Coloring Through Recursive List Coloring.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Faster Deterministic Distributed Coloring Through Recursive List Coloring<PERSON><PERSON><PERSON>pp.1244 - 1259Chapter DOI:https://doi.org/10.1137/1.9781611975994.76PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We provide novel deterministic distributed vertex coloring algorithms. As our main result, we give a deterministic distributed algorithm to compute a (Δ + 1)-coloring of an n-node graph with maximum degree rounds. For graphs with arboricity a, we obtain a deterministic distributed algorithm to compute a (2 + o(1))a-coloring in time . Further, for graphs with bounded neighborhood independence, we show that a (Δ + 1)-coloring can be computed more efficiently in time . This in particular implies that also a (2Δ – 1)-edge coloring can be computed deterministically in rounds, which improves the best known time bound for small values of Δ. All results even hold for the list coloring variants of the problems. As a consequence, we also obtain an improved deterministic n-round algorithm for Δ-coloring non-complete graphs with maximum degree Δ ≥ 3. Most of our algorithms only require messages of O(log n) bits (including the (Δ + 1)-vertex coloring algorithms). Our main technical contribution is a recursive deterministic distributed list coloring algorithm to solve list coloring problems with lists of size Δ1+o(1). Given some list coloring problem and an orientation of the edges, we show how to recursively divide the global color space into smaller subspaces, assign one of the subspaces to each node of the graph, and compute a new edge orientation such that for each node, the list size to out-degree ratio degrades at most by a constant factor on each recursion level. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.76"}, {"primary_key": "2683324", "vector": [], "sparse_vector": [], "title": "Hierarchy-Based Algorithms for Minimizing Makespan under Precedence and Communication Constraints.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Hierarchy-Based Algorithms for Minimizing Makespan under Precedence and Communication Constraints<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.2770 - 2789Chapter DOI:https://doi.org/10.1137/1.9781611975994.169PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the classic problem of scheduling jobs with precedence constraints on a set of identical machines to minimize the makespan objective function. Understanding the exact approximability of the problem when the number of machines is a constant is a well-known question in scheduling theory. Indeed, an outstanding open problem from the classic book of <PERSON><PERSON><PERSON> and <PERSON> [9] asks whether this problem is NP-hard even in the case of 3 machines and unit-length jobs. In a recent breakthrough, <PERSON><PERSON> and <PERSON> [24] gave a (1 + ϵ)-approximation algorithm, which runs in nearly quasi-polynomial time, for the case when job have unit lengths. However, a substantially more difficult case where jobs have arbitrary processing lengths has remained open. We make progress on this more general problem. We show that there exists a (1 + ϵ)-approximation algorithm (with similar running time as that of [24]) for the nonmigratory setting: when every job has to be scheduled entirely on a single machine, but within a machine the job need not be scheduled during consecutive time steps. Further, we also show that our algorithmic framework generalizes to another classic scenario where, along with the precedence constraints, the jobs also have communication delay constraints. Both of these fundamental problems are highly relevant to the practice of datacenter scheduling. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.169"}, {"primary_key": "2683325", "vector": [], "sparse_vector": [], "title": "Achieving Optimal Backlog in the Vanilla Multi-Processor Cup Game.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Achieving Optimal Backlog in the Vanilla Multi-Processor Cup GameWilliam <PERSON>William Kuszmaulpp.1558 - 1577Chapter DOI:https://doi.org/10.1137/1.9781611975994.96PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In each step of the p-processor cup game on n cups, a filler distributes up to p units of water among the cups, subject only to the constraint that no cup receives more than 1 unit of water; an emptier then removes up to 1 unit of water from each of p cups. Designing strategies for the emptier that minimize backlog (i.e., the height of the fullest cup) is important for applications in processor scheduling, buffer management in networks, quality of service guarantees, and deamortization. We prove that the greedy algorithm (i.e., the empty-from-fullest-cups algorithm) achieves backlog O(log n) for any p ≥ 1. This resolves a long-standing open problem for p > 1, and is asymptotically optimal as long as n ≥ 2p. If the filler is an oblivious adversary, then we prove that there is a randomized emptying algorithm that achieve backlog O(log p + log log n) with probability 1 – 2− polylog(n) for 2polylog(n) steps. This is known to be asymptotically optimal when n is sufficiently large relative to p. The analysis of the randomized algorithm can also be reinterpreted as a smoothed analysis of the deterministic greedy algorithm. Previously, the only known bound on backlog for p > 1, and the only known randomized guarantees for any p (including when p = 1), required the use of resource augmentation, meaning that the filler can only distribute at most p(1 – ϵ) units of water in each step, and that the emptier is then permitted to remove 1 + δ units of water from each of p cups, for some ϵ, δ > 0. w Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.96"}, {"primary_key": "2683326", "vector": [], "sparse_vector": [], "title": "Packing LPs are Hard to Solve Accurately, Assuming Linear Equations are Hard.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Packing LPs are Hard to Solve Accurately, Assuming Linear Equations are <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.279 - 296Chapter DOI:https://doi.org/10.1137/1.9781611975994.17PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the complexity of approximately solving packing linear programs. In the Real RAM model, it is known how to solve packing LPs with N non-zeros in time Õ(N/ϵ). We investigate whether the ϵ dependence in the running time can be improved. Our first main result relates the difficulty of this problem to hardness assumptions for solving dense linear equations. We show that, in the Real RAM model, unless linear equations in matrices n × n with condition number O(n10) can be solved to ϵ accuracy faster than Õ(n2.01 log(1/ϵ)), no algorithm (1−ϵ)-approximately solves a O(n)×O(n) packing LPs (where N = O(n2)) in time Õ(n2ϵ−0.0003). It would be surprising to solve linear equations in the Real RAM model this fast, as we currently cannot solve them faster than Õ(nω), where ω denotes the exponent in the running time for matrix multiplication in the Real RAM model (and equivalently matrix inversion). The current best bound on this exponent is roughly ω ≤ 2.372. Note, however, that a fast solver for linear equations does not directly imply faster matrix multiplication. But, our reduction shows that if fast and accurate packing LP solvers exist, then either linear equations can be solved much faster than matrix multiplication or the matrix multiplication constant is very close to 2. Instantiating the same reduction with different parameters, we show that unless linear equations in matrices with condition number O(n1.5) can be solved to ϵ accuracy faster than Õ(n2.372 log(1/ϵ)), no algorithm (1 – ϵ)-approximately solves packing LPs in time Õ(n2ϵ−0.067). Thus smaller improvements in the exponent for ϵ in the running time of Packing LP solvers also imply improvements in the current state-of-the-art for solving linear equations. Our second main result relates the difficulty of approximately solving packing linear programs to hardness assumptions for solving sparse linear equations: In the Real RAM model, unless well-conditioned sparse systems of linear equations can be solved faster than Õ((no. non-zeros of matrix) ), no algorithm (1 – ϵ)-approximately solves packing LPs with N non-zeros in time Õ(Nϵ−0.165). This running time of Õ((no. non-zeros of matrix) ) is obtained by the classical Conjugate Gradient algorithm by a standard analysis. Our reduction implies that if sufficiently good packing LP solvers exist, then this long-standing best-known bound on the running time for solving well-conditioned systems of linear equations is sub-optimal1. While we prove results in the Real RAM model, our condition number assumptions ensure that our results can be translated to fixed point arithmetic with (log n)O(1) bits per number. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.17"}, {"primary_key": "2683327", "vector": [], "sparse_vector": [], "title": "Lower Bounds for Oblivious Near-Neighbor Search.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "We prove an Ω(d lg n/(lg lg n)2) lower bound on the dynamic cell-probe complexity of statistically oblivious approximate-near-neighbor search (ANN) over the d-dimensional Hamming cube. For the natural setting of d = Θ(lg n), our result implies an lower bound, which is a quadratic improvement over the highest (non-oblivious) cell-probe lower bound for ANN. This is the first super-logarithmic unconditional lower bound for ANN against general (non black-box) data structures. We also show that any oblivious static data structure for decomposable search problems (like ANN) can be obliviously dynamized with O(lg n) overhead in update and query time, strengthening a classic result of <PERSON> and <PERSON> (Algorithmic<PERSON>, 1980).", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.68"}, {"primary_key": "2683328", "vector": [], "sparse_vector": [], "title": "Online Scheduling via Learned Weights.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Online Scheduling via Learned Weights<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>.1859 - 1877Chapter DOI:https://doi.org/10.1137/1.9781611975994.114PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Online algorithms are a hallmark of worst case optimization under uncertainty. On the other hand, in practice, the input is often far from worst case, and has some predictable characteristics. A recent line of work has shown how to use machine learned predictions to circumvent strong lower bounds on competitive ratios in classic online problems such as ski rental and caching. We study how predictive techniques can be used to break through worst case barriers in online scheduling. The makespan minimization problem with restricted assignments is a classic problem in online scheduling theory. Worst case analysis of this problem gives Ω(log m) lower bounds on the competitive ratio in the online setting. We identify a robust quantity that can be predicted and then used to guide online algorithms to achieve better performance. Our predictions are compact in size, having dimension linear in the number of machines, and can be learned using standard off the shelf methods. The performance guarantees of our algorithms depend on the accuracy of the predictions, given predictions with error η, we show how to construct O(log η) competitive fractional assignments. We then give an online algorithm that rounds any fractional assignment into an integral schedule. Our algorithm is O((log log m)3)-competitive and we give a nearly matching Ω(log log m) lower bound for online rounding algorithms.1 Altogether, we give algorithms that, equipped with predictions with error η, achieve O(log η (log log m)3) competitive ratios, breaking the Ω(log m) lower bound even for moderately accurate predictions. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.114"}, {"primary_key": "2683329", "vector": [], "sparse_vector": [], "title": "A PTAS for subset TSP in minor-free graphs.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A PTAS for subset TSP in minor-free graphsHung LeHung Lepp.2279 - 2298Chapter DOI:https://doi.org/10.1137/1.9781611975994.140PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We give the first PTAS for the subset Traveling Salesperson Problem (TSP) in H-minor-free graphs. This resolves a long standing open problem in a long line of work on designing PTASes for TSP in minor-closed families initiated by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> in FOCS'95. The main technical ingredient in our PTAS is a construction of a nearly light subset (1 + ϵ)-spanner for any given edge-weighted H-minor-free graph. This construction is based on a necessary and sufficient condition given by sparse spanner oracles: light subset spanners exist if and only if sparse spanner oracles exist. This relationship allows us to obtain two new results: An (1 + ε)-spanner with lightness O(ϵ−d+2) for any doubling metric of constant dimension d. This improves the earlier lightness bound ϵ−O(d) obtained by <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [14]. An (1 + ϵ)-spanner with sublinear lightness for any metric of constant correlation dimension. Previously, no spanner with non-trivial lightness was known. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.140"}, {"primary_key": "2683330", "vector": [], "sparse_vector": [], "title": "A Tight Analysis of Greedy Yields Subexponential Time Approximation for Uniform Decision Tree.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A Tight Analysis of Greedy Yields Subexponential Time Approximation for Uniform Decision TreeRay Li, <PERSON>, and <PERSON>, <PERSON>, and <PERSON>.102 - 121Chapter DOI:https://doi.org/10.1137/1.9781611975994.7PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Decision Tree is a classic formulation of active learning: given n hypotheses with nonnegative weights summing to 1 and a set of tests that each partition the hypotheses, output a decision tree using the provided tests that uniquely identifies each hypothesis and has minimum (weighted) average depth. Previous works showed that the greedy algorithm achieves a O(log n) approximation ratio for this problem and it is NP-hard beat a O(log n) approximation, settling the complexity of the problem. However, for Uniform Decision Tree, i.e. Decision Tree with uniform weights, the story is more subtle. The greedy algorithm's O(log n) approximation ratio was the best known, but the largest approximation ratio known to be NP-hard is 4 – ε. We prove that the greedy algorithm gives a approximation for Uniform Decision Tree, where COPT is the cost of the optimal tree and show this is best possible for the greedy algorithm. As a corollary, we resolve a conjecture of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> [20]. Our results also hold for instances of DecisioN Tree whose weights are not too far from uniform. Leveraging this result, for all α ϵ (0, 1), we exhibit a approximation algorithm to Uniform Decision Tree running in subexponential time . As a corollary, achieving any super-constant approximation ratio on Uniform Decision Tree is not NP-hard, assuming the Exponential Time Hypothesis. This work therefore adds approximating Uniform Decision Tree to a small list of natural problems that have subexponential time algorithms but no known polynomial time algorithms. Like the analysis of the greedy algorithm, our analysis of the subexponential time algorithm gives similar approximation guarantees even for slightly nonuniform weights. A key technical contribution of our work is showing a connection between greedy algorithms for Uniform Decision Tree and for Min Sum Set Cover. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.7"}, {"primary_key": "2683331", "vector": [], "sparse_vector": [], "title": "Detecting Feedback Vertex Sets of Size k in O*(2.7k) Time.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Detecting Feedback Vertex Sets of Size k in O*(2.7k) <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.971 - 989Chapter DOI:https://doi.org/10.1137/1.9781611975994.58PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the Feedback Vertex Set problem, one is given an undirected graph G and an integer k, and one needs to determine whether there exists a set of k vertices that intersects all cycles of G (a so-called feedback vertex set). Feedback Vertex Set is one of the most central problems in parameterized complexity: It served as an excellent test bed for many important algorithmic techniques in the field such as Iterative Compression [<PERSON> et al. (JCSS'06)], Randomized Branching [<PERSON> et al. (J. Artif. Intell. Res'00)] and Cut&Count [<PERSON><PERSON> et al. (FOCS'11)]. In particular, there has been a long race for the smallest dependence f (k) in run times of the type O*(f (k)), where the O* notation omits factors polynomial in n. This race seemed to be run in 2011, when a randomized O*(3k) time algorithm based on Cut&Count was introduced. In this work, we show the contrary and give a O*(2.7k) time randomized algorithm. Our algorithm combines all mentioned techniques with substantial new ideas: First, we show that, given a feedback vertex set of size k of bounded average degree, a tree decomposition of width (1 – Ω(1))k can be found in polynomial time. Second, we give a randomized branching strategy inspired by the one from [Becker et al. (J. Artif. Intell. Res'00)] to reduce to the aforementioned bounded average degree setting. Third, we obtain significant run time improvements by employing fast matrix multiplication. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.58"}, {"primary_key": "2683332", "vector": [], "sparse_vector": [], "title": "Tight Bounds for the Subspace Sketch Problem with Applications.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Tight Bounds for the Subspace Sketch Problem with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>pp.1655 - 1674<PERSON>hapter DOI:https://doi.org/10.1137/1.9781611975994.102PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the subspace sketch problem one is given an n × d matrix A with O(log(nd)) bit entries, and would like to compress it in an arbitrary way to build a small space data structure Qp, so that for any given x ϵ ℝd, with probability at least 2/3, one has Qp(x) = (1 ± ε)||Ax||p, where p ≥ 0 and the randomness is over the construction of Qp. The central question is: How many bits are necessary to store Qp? This problem has applications to the communication of approximating the number of non-zeros in a matrix product, the size of coresets in projective clustering, the memory of streaming algorithms for regression in the row-update model, and embedding subspaces of Lp in functional analysis. A major open question is the dependence on the approximation factor ε. We show if p ≥ 0 is not a positive even integer and d = Ω(log(1/ε)), then (ε−2 · d) bits are necessary. On the other hand, if p is a positive even integer, then there is an upper bound of O(dp log(nd)) bits independent of ε. Our results are optimal up to logarithmic factors, and show in particular that one cannot compress A to O(d) \"directions\" ν1, …,νo(d), such that for any x, ||Ax||1 can be well-approximated from 〈ν1, x〉, …, 〈νO(d), x〉. Our lower bound rules out arbitrary functions of these inner products (and in fact arbitrary data structures built from A), and thus rules out the possibility of a singular value decomposition for ℓ1 in a very strong sense. Indeed, as ε → 0, for p = 1 the space complexity becomes arbitrarily large, while for p = 2 it is at most O(d2 log(nd)). As corollaries of our main lower bound, we obtain new lower bounds for a wide range of applications, including the above, which in many cases are optimal. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.102"}, {"primary_key": "2683333", "vector": [], "sparse_vector": [], "title": "Nearly Optimal Planar k Nearest Neighbors Queries under General Distance Functions.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Nearly Optimal Planar k Nearest Neighbors Queries under General Distance FunctionsChih-Hung <PERSON>-<PERSON> Liupp.2842 - 2859Chapter DOI:https://doi.org/10.1137/1.9781611975994.173PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the k nearest neighbors problem in the plane for general, convex, pairwise disjoint sites of constant description complexity such as line segments, disks, and quadrilaterals and with respect to a general family of distance functions including the Lp-norms and additively weighted Euclidean distances. For point sites in the Euclidean metric, after four decades of effort, an optimal data structure has recently been developed with O(n) space, O(log n + k) query time, and O(n log n) preprocessing time [1, 17]. We develop a static data structure for the general setting with nearly optimal O(n log log n) space, the optimal O(log n + k) query time, and expected O(n polylog n) preprocessing time. The O(n log log n) space approaches the linear space, whose achievability is still unknown with the optimal query time, and improves the so far best O(n(log2 n)(log log n)2) space of <PERSON><PERSON> et al.'s work [12]. Our dynamic version (that allows insertions and deletions of sites) also reduces the space of <PERSON> et al.'s work [29] from O(n log3 n) to O(n log n) while keeping O(log2 n + k) query time and O(polylog n) update time, thus improving many applications such as dynamic bichromatic closest pair and dynamic minimum spanning tree in general planar metric, and shortest path tree and dynamic connectivity in disk intersection graphs. To obtain these progresses, we devise shallow cuttings of linear size for general distance functions. Shallow cuttings are a key technique to deal with the k nearest neighbors problem for point sites in the Euclidean metric. Agarwal et al. [4] already designed linear-size shallow cuttings for general distance functions, but their shallow cuttings could not be applied to the k nearest neighbors problem. Recently, Kaplan et al. [29] constructed shallow cuttings that are feasible for the k nearest neighbors problem, while the size of their shallow cuttings has an extra double logarithmic factor. Our innovation is a new random sampling technique for the analysis of geometric structures. While our shallow cuttings seem, to some extent, merely a simple transformation of Agarwal et al.'s [4], the analysis requires our new technique to attain the linear size. Since our new technique provides a new way to develop and analyze geometric algorithms, we believe it is of independent interest. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.173"}, {"primary_key": "2683334", "vector": [], "sparse_vector": [], "title": "2-Approximating Feedback Vertex Set in Tournaments.", "authors": ["<PERSON>", "Pranaben<PERSON> Mi<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)2-Approximating Feedback Vertex Set in Tournaments<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> Saurabhpp.1010 - 1018Chapter DOI:https://doi.org/10.1137/1.9781611975994.61PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A tournament is a directed graph T such that every pair of vertices is connected by an arc. A feedback vertex set is a set S of vertices in T such that T – S is acyclic. We consider the Feedback Vertex Set problem in tournaments. Here the input is a tournament T and a weight function w: V(T) → ℕ and the task is to find a feedback vertex set S in T minimizing w(S) = ΣvϵSw(v). Rounding optimal solutions to the natural LP-relaxation of this problem yields a simple 3-approximation algorithm. This has been improved to 2.5 by <PERSON><PERSON> et al. [SICOMP 2000], and subsequently to 7/3 by <PERSON><PERSON> et al. [ESA 2016]. In this paper we give the first polynomial time factor 2 approximation algorithm for this problem. Assuming the Unique Games conjecture, this is the best possible approximation ratio achievable in polynomial time. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.61"}, {"primary_key": "2683335", "vector": [], "sparse_vector": [], "title": "Parameterized Complexity and Approximability of Directed Odd Cycle Transversal.", "authors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Parameterized Complexity and Approximability of Directed Odd Cycle Transversal<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>.2181 - 2200Chapter DOI:https://doi.org/10.1137/1.9781611975994.134PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A directed odd cycle transversal of a directed graph (digraph) D is a vertex set S that intersects every odd directed cycle of D. In the Directed Odd Cycle Transversal (DOCT) problem, the input consists of a digraph D and an integer k. The objective is to determine whether there exists a directed odd cycle transversal of D of size at most k. In this paper, we settle the parameterized complexity of DOCT when parameterized by the solution size k by showing that DOCT does not admit an algorithm with running time unless FPT = W[1]. On the positive side, we give a factor 2 fixed-parameter approximation (FPT approximation) algorithm for the problem. More precisely, our algorithm takes as input D and k, runs in time , and either concludes that D does not have a directed odd cycle transversal of size at most k, or produces a solution of size at most 2k. Finally, assuming gap-ETH, we show that there exists an ϵ > 0 such that DOCT does not admit a factor (1 + ϵ) FPT-approximation algorithm. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.134"}, {"primary_key": "2683336", "vector": [], "sparse_vector": [], "title": "Tight Running Time Lower Bounds for Strong Inapproximability of Maximum k-Coverage, Unique Set Cover and Related Problems (via t-Wise Agreement Testing Theorem).", "authors": ["<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Tight Running Time Lower Bounds for Strong Inapproximability of Maximum k-Coverage, Unique Set Cover and Related Problems (via t-Wise Agreement Testing Theorem)Pasin ManurangsiPasin Man<PERSON>ngsipp.62 - 81Chapter DOI:https://doi.org/10.1137/1.9781611975994.5PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We show, assuming the (randomized) Gap Exponential Time Hypothesis (Gap-ETH), that the following tasks cannot be done in T(k) · No(k)-time for any function T where N denote the input size: ()-approximation for Max k-Coverage for any constant ɛ > 0, ()-approximation for k-Median (in general metrics) for any constant ɛ > 0. ()-approximation for k-Mean (in general metrics) for any constant ɛ > 0. Any constant factor approximation for k-Unique Set Cover, k-Nearest Codeword Problem and k-Closest Vector Problem. (1 + δ)-approximation for k-Minimum Distance Problem and k-Shortest Vector Problem for some δ > 0. Since all problems considered here can be trivially solved in NO(k) time, our running time lower bounds are tight up to a constant factor in the exponent. In terms of approximation ratios, Max k-Coverage is well-known to admit polynomial-time ()-approximation algorithms, and, recently, it was shown that k-Median and k-Median are approximable to within factors of () and () respectively in FPT time [20]; hence, our inapproximability ratios are also tight for these three problems. For the remaining problems, no non-trivial FPT approximation algorithms are known. The starting point of all our hardness results is the Label Cover problem (with projection constraints). We show that Label Cover cannot be approximated to within any constant factor in T(k) · No(k) time, where N and k denote the size of the input and the number of nodes on the side with the larger alphabet respectively. With this hardness, the above results follow immediately from known reductions. The hardness of Label Cover is in turn shown via a t-wise agreement testing theorem of the following form: given local boolean functions f1, …,fk on domains S1, …, Sk ⊆ [n], if random t functions “weakly agree” with sufficiently large probability, then we can find a global boolean function g: [n] → {0, 1} that “mostly agrees” with “many” of the local functions. We prove such a statement in the regime where S1, …, Sk are “random-looking” sets of size Θ(n/k). Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.5"}, {"primary_key": "2683337", "vector": [], "sparse_vector": [], "title": "Navigating an Infinite Space with Unreliable Movements.", "authors": ["<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Navigating an Infinite Space with Unreliable <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>.1170 - 1179Chapter DOI:https://doi.org/10.1137/1.9781611975994.71PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider a search problem on a 2-dimensional infinite grid with a single mobile agent. The goal of the agent is to find her way home, which is located in a grid cell chosen by an adversary. Initially, the agent is provided with an infinite sequence of instructions, that dictate the movements performed by the agent. Each instruction corresponds to a movement to an adjacent grid cell and the set of instructions can be a function of the initial locations of the agent and home. The challenge of our problem stems from faults in the movements made by the agent. In every step, with some constant probability 0 ≤ p ≤ 1, the agent performs a random movement instead of following the current instruction. This paper provides two results on this problem. First, we show that for some values of p, there does not exist any set of instructions that guide the agent home in finite expected time. Second, we complement this impossibility result with an algorithm that, for sufficiently small values of p, yields a finite expected hitting time for home. In particular, we show that for any p < 1, our approach gives a hitting rate that decays polynomially as a function of time. In that sense, our approach is far superior to a standard random walk in terms of hitting time. The main contribution and take-home message of this paper is to show that, for some value of 0.01139 … < p < 0.6554 …, there exists a phase transition on the solvability of the problem. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.71"}, {"primary_key": "2683338", "vector": [], "sparse_vector": [], "title": "How to aggregate Top-lists: Approximation algorithms via scores and average ranks.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)How to aggregate Top-lists: Approximation algorithms via scores and average ranks<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.2810 - 2822Chapter DOI:https://doi.org/10.1137/1.9781611975994.171PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract A top-list is a possibly incomplete ranking of elements: only a subset of the elements are ranked, with all unranked elements tied for last. Top-list aggregation, a generalization of the well-known rank aggregation problem, takes as input a collection of top-lists and aggregates them into a single complete ranking, aiming to minimize the number of upsets (pairs ranked in opposite order in the input and in the output). In this paper, we give simple approximation algorithms for top-list aggregation. We generalize the footrule algorithm for rank aggregation (which minimizes <PERSON><PERSON><PERSON>'s footrule distance), yielding a simple 2-approximation algorithm for top-list aggregation. <PERSON><PERSON>'s RepeatChoice algorithm for bucket-orders aggregation yields a 2-approximation algorithm for top-list aggregation. Using inspiration from approval voting, we define the score of an element as the frequency with which it is ranked, i.e. appears in an input top-list. We reinterpret RepeatChoice for top-list aggregation as a randomized algorithm using variables whose expectations correspond to score and to the average rank of an element given that it is ranked. Using average ranks, we generalize and analyze Borda's algorithm for rank aggregation. We observe that the natural generalization is not a constant approximation. We design a simple 2-phase variant of the Generalized Borda's algorithm, roughly sorting by scores and breaking ties by average ranks, yielding another simple constant-approximation algorithm for top-list aggregation. We then design another 2-phase variant in which in order to break ties we use, as a black box, the Mathieu-Schudy PTAS for rank aggregation, yielding a PTAS for top-list aggregation. This solves an open problem posed by Ailon. Finally, in the special case in which all input lists have length at most k, we design another simple 2-phase algorithm based on sorting by scores, and prove that it is an EPTAS – the complexity is (n log n) when k = o(log n). Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.171"}, {"primary_key": "2683339", "vector": [], "sparse_vector": [], "title": "A New Algorithm for the Robust Semi-random Independent Set Problem.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A New Algorithm for the Robust Semi-random Independent Set <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>pp.738 - 746Chapter DOI:https://doi.org/10.1137/1.9781611975994.45PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study the independent set problem in a semi-random model proposed by <PERSON><PERSON> and <PERSON><PERSON>. This model selects a graph with a planted independent set of size k and then allows an adversary to modify a large fraction of edges: the subgraph induced by the complement of the independent set can be modified arbitrarily, and the adversary may add (but not delete) edges from the independent set to its complement. In particular, the adversary can create a graph in which the initial planted independent set is not the largest independent set. <PERSON><PERSON> and <PERSON><PERSON> presented a randomized algorithm, which with high probability recovers an independent set of size at least k (which may not be the planted one) when k = an where a is a constant, and the probability of a random edge p > (1 + ϵ) ln n/αn. We give a new deterministic algorithm in the Feige-Kilian model that finds an independent set of size at least .99k provided that the planted set has size k = Ω(n2/3/p1/3), and finds a list of independent sets, one of which is the planted one provided that k = Ω(n2/3/p). This improves on the algorithm of Feige and Kilian by working for smaller k if p = Ω(1/n1/3), and improves on an algorithm of Steinhardt by working for slightly smaller k and by working against a stronger adversarial model. The ability to find a good approximation of the largest independent set is new when p < ln n/k. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.45"}, {"primary_key": "2683340", "vector": [], "sparse_vector": [], "title": "Edge Expansion and Spectral Gap of Nonnegative Matrices.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Edge Expansion and Spectral Gap of Nonnegative Matrices<PERSON><PERSON><PERSON> <PERSON><PERSON> and <PERSON> and <PERSON>pp.1200 - 12113Chapter DOI:https://doi.org/10.1137/1.9781611975994.73PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The classic graphical Cheeger inequalities state that if M is an n × n symmetric doubly stochastic matrix, then where is the edge expansion of M, and λ2(M) is the second largest eigenvalue of M. We study the relationship between φ(A) and the spectral gap 1 – Re λ2(A) for any doubly stochastic matrix A (not necessarily symmetric), where λ2(A) is a nontrivial eigenvalue of A with maximum real part. <PERSON><PERSON><PERSON> showed that the upper bound on φ(A) is unaffected, i.e., . With regards to the lower bound on φ(A), there are known constructions with indicating that at least a mild dependence on n is necessary to lower bound φ(A). In our first result, we provide an exponentially better construction of n × n doubly stochastic matrices An, for which In fact, all nontrivial eigenvalues of our matrices are 0, even though the matrices are highly nonexpanding. We further show that this bound is in the correct range (up to the exponent of n), by showing that for any doubly stochastic matrix A, As a consequence, unlike the symmetric case, there is a (necessary) loss of a factor of in lower bounding φ by the spectral gap in the nonsymmetric setting. Our second result extends these bounds to general matrices R with nonnegative entries, to obtain a two-sided gapped refinement of the Perron-Frobenius theorem. Recall from the Perron-Frobenius theorem that for such R, there is a nonnegative eigenvalue r such that all eigenvalues of R lie within the closed disk of radius r about 0. Further, if R is irreducible, which means φ(R) > 0 (for suitably defined φ), then r is positive and all other eigenvalues lie within the open disk, so (with eigenvalues sorted by real part), Re λ2(R) < r. An extension of Fiedler's result provides an upper bound and our result provides the corresponding lower bound on φ(R) in terms of r – Re λ2(R), obtaining a two-sided quantitative version of the Perron-Frobenius theorem. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.73"}, {"primary_key": "2683341", "vector": [], "sparse_vector": [], "title": "X-Ramanujan graphs.", "authors": ["<PERSON><PERSON><PERSON>", "Ryan <PERSON>&<PERSON>;Donnell"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)X-Ramanujan graphs<PERSON><PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.1226 - 1243Chapter DOI:https://doi.org/10.1137/1.9781611975994.75PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Let X be an infinite graph of bounded degree; e.g., the Cayley graph of a free product of finite groups. If G is a finite graph covered by X, it is said to be X-Ramanujan if its second-largest eigenvalue λ2(G) is at most the spectral radius ρ(X) of X, and more generally k-quasi-X-Ramanujan if λk (G) is at most ρ(X). In case X is the infinite Δ-regular tree, this reduces to the well known notion of a finite Δ-regular graph being Ramanujan. Inspired by the Interlacing Polynomials method of <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, we show the existence of infinitely many k-quasi-X-Ramanujan graphs for a variety of infinite X. In particular, X need not be a tree; our analysis is applicable whenever X is what we call an additive product graph. This additive product is a new construction of an infinite graph A1 ⌖ ··· ⌖ Ac from finite \"atom\" graphs A1, …,Ac over a common vertex set. It generalizes the notion of the free product graph A1 * · · · * Ac when the atoms Aj are vertex-transitive, and it generalizes the notion of the universal covering tree when the atoms Aj are single-edge graphs. Key to our analysis is a new graph polynomial α(A1,…,Ac; x) that we call the additive characteristic polynomial. It generalizes the well known matching polynomial μ(G; x) in case the atoms Aj are the single edges of G, and it generalizes the r-characteristic polynomial introduced in [Rav16, LR18]. We show that α(A1, …, Ac; x) is real-rooted, and all of its roots have magnitude at most ρ(A1 ⌖ ··· ⌖ Ac). This last fact is proven by generalizing Godsil's notion of treelike walks on a graph G to a notion of freelike walks on a collection of atoms A1, …, Ac. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.75"}, {"primary_key": "2683342", "vector": [], "sparse_vector": [], "title": "The Power of Distributed Verifiers in Interactive Proofs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "E<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)The Power of Distributed Verifiers in Interactive ProofsMoni Naor, Merav Parter, and Eylon YogevMoni Naor, Merav Parter, and Eylon Yogevpp.1096 - 115Chapter DOI:https://doi.org/10.1137/1.9781611975994.67PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We explore the power of interactive proofs with a distributed verifier. In this setting, the verifier consists of n nodes and a graph G that defines their communication pattern. The prover is a single entity that communicates with all nodes by short messages. The goal is to verify that the graph G belongs to some language in a small number of rounds, and with small communication bound, i.e., the proof size. This interactive model was introduced by <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (PODC 2018) as a generalization of noninteractive distributed proofs. They demonstrated the power of interaction in this setting by constructing protocols for problems as Graph Symmetry and Graph Non-Isomorphism – both of which require proofs of Ω(n2)-bits without interaction. In this work, we provide a new general framework for distributed interactive proofs that allows one to translate standard interactive protocols (i.e., with a centralized verifier) to ones where the verifier is distributed with a proof size that depends on the computational complexity of the verification algorithm run by the centralized verifier. We show the following: Every (centralized) computation performed in time O(n) on a RAM can be translated into three-round distributed interactive protocol with O(log n) proof size. This implies that many graph problems for sparse graphs have succinct proofs (e.g., testing planarity). Every (centralized) computation implemented by either a small space or by uniform NC circuit can be translated into a distributed protocol with O(1) rounds and O(log n) bits proof size for the low space case and polylog(n) many rounds and proof size for NC. We show that for Graph Non-Isomorphism, one of the striking demonstrations of the power of interaction, there is a 4-round protocol with O(log n) proof size, improving upon the O(n log n) proof size of Kol et al. For many problems, we show how to reduce proof size below the seemingly natural barrier of log n. By employing our RAM compiler, we get a 5-round protocol with proof size O (log log n) for a family of problems including Fixed Automorphism, Clique and Leader Election (for the latter two problems we actually get O(1) proof size). Finally, we discuss how to make these proofs noninteractive arguments via random oracles. Our compilers capture many natural problems and demonstrate the difficulty in showing lower bounds in these regimes. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.67"}, {"primary_key": "2683343", "vector": [], "sparse_vector": [], "title": "On Decoding <PERSON><PERSON><PERSON>-Schulman Tree Codes.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)On Decoding Cohen-<PERSON><PERSON>-Schulman Tree CodesAnand <PERSON> and <PERSON> and <PERSON>.1337 - 1356Chapter DOI:https://doi.org/10.1137/1.9781611975994.81PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Tree codes, introduced by <PERSON><PERSON><PERSON> [26, 27], are combinatorial structures essential to coding for interactive communication. An infinite family of tree codes with both rate and distance bounded by positive constants is called asymptotically good. Rate being constant is equivalent to the alphabet size being constant. <PERSON><PERSON><PERSON> proved that there are asymptotically good tree code families, yet their explicit construction remains an outstanding open problem. In a major breakthrough, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> [12] constructed explicit tree code families with constant distance, but over an alphabet polylogarithmic in the length. Our main result is a randomized polynomial time decoding algorithm for these codes making novel use of the polynomial method. The number of errors corrected scales roughly as the block length to the three-fourths power, falling short of the constant fraction error correction guaranteed by the constant distance. We further present number theoretic variants of Cohen-<PERSON><PERSON>-<PERSON> codes, all correcting a constant fraction of errors with polylogarithmic alphabet size. Towards efficiently correcting close to a constant fraction of errors, we propose a speculative convex optimization approach inspired by compressed sensing. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.81"}, {"primary_key": "2683344", "vector": [], "sparse_vector": [], "title": "Linear rankwidth meets stability.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Linear rankwidth meets <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>.1180 - 1199Chapter DOI:https://doi.org/10.1137/1.9781611975994.72PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Classes with bounded rankwidth are MSO-transductions of trees and classes with bounded linear rankwidth are MSO-transductions of paths. These results show a strong link between the properties of these graph classes considered from the point of view of structural graph theory and from the point of view of finite model theory. We take both views on classes with bounded linear rankwidth and prove structural and model theoretic properties of these classes: 1) Graphs with linear rankwidth at most r are linearly χ-bounded. Actually, they have bounded c-chromatic number, meaning that they can be colored with f (r) colors, each color inducing a cograph. 2) Based on a Ramsey-like argument, we prove for every proper hereditary family of graphs (like cographs) that there is a class with bounded rankwidth that does not have the property that graphs in it can be colored by a bounded number of colors, each inducing a subgraph in . 3) For a class with bounded linear rankwidth the following conditions are equivalent: a) is stable, b) excludes some half-graph as a semi-induced subgraph, c) is a first-order transduction of a class with bounded pathwidth. These results open the perspective to study classes admitting low linear rankwidth covers. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.72"}, {"primary_key": "2683345", "vector": [], "sparse_vector": [], "title": "A 4 + ε approximation for k-connected subgraphs.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A 4 + ε approximation for k-connected subgraphs<PERSON>ee<PERSON> <PERSON>utov<PERSON>eev Nutovpp.1000 - 1009Chapter DOI:https://doi.org/10.1137/1.9781611975994.60PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We obtain approximation ratio for the (undirected) k-Connected Subgraph problem, where is the largest integer such that 2ℓ–1k2ℓ+1 ≤ n. For large values of n this improves the ratio 6 of <PERSON><PERSON><PERSON> and <PERSON><PERSON>gh [4] when n ≥ k3 (the case ℓ = 1). Our result implies an fpt-approximation ratio 4 + ε that matches (up to the \"+ε\" term) the best known ratio 4 for k = 6, 7 for both the general and the easier augmentation versions of the problem. Similar results are shown for the problem of covering an arbitrary crossing supermodular biset function. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.60"}, {"primary_key": "2683346", "vector": [], "sparse_vector": [], "title": "Fine-grained complexity of graph homomorphism problem for bounded-treewidth graphs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Fine-grained complexity of graph homomorphism problem for bounded-treewidth graphs<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>żewskiKarolina Okrasa and <PERSON>we<PERSON>.1578 - 1590Chapter DOI:https://doi.org/10.1137/1.9781611975994.97PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract For graphs G and H, a homomorphism from G to H is an edge-preserving mapping from the vertex set of G to the vertex set of H. For a fixed graph H, by Hom(H) we denote the computational problem which asks whether a given graph G admits a homomorphism to H. If H is a complete graph with k vertices, then Hom(H) is equivalent to the k-Coloring problem, so graph homomorphisms can be seen as generalizations of colorings. It is known that Hom(H) is polynomial-time solvable if H is bipartite or has a vertex with a loop, and NP-complete otherwise [<PERSON> and <PERSON><PERSON><PERSON><PERSON>, JCTB 1990]. In this paper we are interested in the complexity of the problem, parameterized by the treewidth of the input graph G. If G has n vertices and is given along with its tree decomposition of width tw(G), then the problem can be solved in time |V(H)|tw(G) · , using a straightforward dynamic programming. We explore whether this bound can be improved. We show that if H is a projective core, then the existence of such a faster algorithm is unlikely: assuming the Strong Exponential Time Hypothesis (SETH), the Hom(H) problem cannot be solved in time (|V(H)| – ε)tw(G) · , for any ε > 0. This result provides a full complexity characterization for a large class of graphs H, as almost all graphs are projective cores. We also notice that the naive algorithm can be improved for some graphs H, and show a complexity classification for all graphs H, assuming two conjectures from algebraic graph theory. In particular, there are no known graphs H which are not covered by our result. In order to prove our results, we bring together some tools and techniques from algebra and from fine-grained complexity. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.97"}, {"primary_key": "2683347", "vector": [], "sparse_vector": [], "title": "Approximating the Distance to Monotonicity of Boolean Functions.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Approximating the Distance to Monotonicity of Boolean Functions<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.1995 - 2009Chapter DOI:https://doi.org/10.1137/1.9781611975994.123PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We design a nonadaptive algorithm that, given a Boolean function f: {0, 1}n → {0, 1} which is α-far from monotone, makes poly(n, 1/α) queries and returns an estimate that, with high probability, is an -approximation to the distance of f to monotonicity. Furthermore, we show that for any constant k > 0, approximating the distance to monotonicity up to n1/2−k-factor requires nonadaptive queries, thereby ruling out a poly(n, 1/α)-query nonadaptive algorithm for such approximations. This answers a question of <PERSON><PERSON><PERSON><PERSON> (Property Testing Review, 2014) for the case of nonadaptive algorithms. Approximating the distance to a property is closely related to tolerantly testing that property. Our lower bound stands in contrast to standard (non-tolerant) testing of monotonicity that can be done nonadaptively with queries. We obtain our lower bound by proving an analogous bound for erasure-resilient testers. An α-erasure-resilient tester for a desired property gets oracle access to a function that has at most an α fraction of values erased. The tester has to accept (with probability at least 2/3) if the erasures can be filled in to ensure that the resulting function has the property and to reject (with probability at least 2/3) if every completion of erasures results in a function that is ε-far from having the property. Our method yields the same lower bounds for unateness and being a k-junta. These lower bounds improve exponentially on the existing lower bounds for these properties. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.123"}, {"primary_key": "2683348", "vector": [], "sparse_vector": [], "title": "Robust Clustering Oracle and Local Reconstructor of Cluster Structure of Graphs.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Robust Clustering Oracle and Local Reconstructor of Cluster Structure of GraphsPan PengPan Pengpp.2953 - 2972Chapter DOI:https://doi.org/10.1137/1.9781611975994.179PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We develop sublinear time algorithms for analyzing the cluster structure of graphs with noisy partial information. A graph G with maximum degree at most d is called (k, φin, φout)-clusterable, if it can be partitioned into at most k parts, such that each part has inner conductance at least φin and outer conductance at most φout, where d is assumed to be constant. A graph G is called to be an ∊-perturbation of a (k,φin, φout)-clusterable graph if there is partition of G with at most k parts (called clusters), such that one can insert/delete at most ϵdn intra-cluster edges to make it a (k,φin,φout)-clusterable graph. We are given query access to the adjacency list of such a graph. We show that one can construct in time a robust clustering oracle for a bounded-degree graph G that is an ∊-perturbation of a -clusterable graph. Using such an oracle, a typical clustering query (e.g., IsOutlier(s), SameCluster(s, t)) can be answered in time and the answers are consistent with a partition of G in which all but vertices belong to a good cluster, i.e., a set with inner conductance at least , and outer conductance . We also develop a local reconstruction algorithm that takes as input a graph as above, and on any query vertex v, outputs all its neighbors in the reconstructed graph G', which is guaranteed to be -clusterable (with slightly boosting degree bound). The number of edges changed is at most . Furthermore, the algorithm runs in time (per query) and can answer consistently with the same G′ for any sequence of queries it gets. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.179"}, {"primary_key": "2683350", "vector": [], "sparse_vector": [], "title": "List Decodable Learning via Sum of Squares.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)List Decodable Learning via Sum of Squares<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.161 - 180Chapter DOI:https://doi.org/10.1137/1.9781611975994.10PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the list-decodable learning setup, an overwhelming majority (say a 1 – β-fraction) of the input data consists of outliers and the goal of an algorithm is to output a small list of hypotheses such that one of them agrees with inliers. We devise list decodable learning algorithms for the problem of linear regression using the sum of squares SDP hierarchy. In the list-decodable linear regression problem, we are given labelled examples {(Xi, yi)}iϵ[n] containing a subset S of βN inliers {Xi}iϵs that are drawn i.i.d. from standard Gaussian distribution N(0, I) in ℝd, where the corresponding labels yi are well-approximated by a linear function . We devise an algorithm that outputs a list of linear functions such that there exists some ϵ that is close to . This yields the first algorithm for linear regression in a list-decodable setting. Our results hold for a general distribution of examples whose concentration and anti-concentration properties can be certified by low degree sum-of-squares proofs. In an independent and concurrent work, Karmalkar et al. [KKK19] also obtain an algorithm for list-decodable linear regression using the Sum-of-Squares SDP hierarchy. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.10"}, {"primary_key": "2683351", "vector": [], "sparse_vector": [], "title": "Linear Size Sparsifier and the Geometry of the Operator Norm Ball.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Linear Size Sparsifier and the Geometry of the Operator Nor<PERSON> BallV<PERSON> and <PERSON> and <PERSON>.2337 - 2348Chapter DOI:https://doi.org/10.1137/1.9781611975994.143PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The Matrix Spencer Conjecture asks whether given n symmetric matrices in ℝn×n with eigenvalues in [–1, 1] one can always find signs so that their signed sum has singular values bounded by . The standard approach in discrepancy requires proving that the convex body of all good fractional signings is large enough. However, this question has remained wide open due to the lack of tools to certify measure lower bounds for rather small non-polyhedral convex sets. A seminal result by <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> from 2008 shows that any undirected graph admits a linear size spectral sparsifier. Again, one can define a convex body of all good fractional signings. We can indeed prove that this body is close to most of the Gaussian measure. This implies that a discrepancy algorithm by the second author can be used to sample a linear size sparsifer. In contrast to previous methods, we require only a logarithmic number of sampling phases. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.143"}, {"primary_key": "2683352", "vector": [], "sparse_vector": [], "title": "Near-Optimal Bounds for Online Caching with Machine Learned Advice.", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Near-Optimal Bounds for Online Caching with Machine Learned AdviceDhruv RohatgiDhruv Rohatgipp.1834 - 1845Chapter DOI:https://doi.org/10.1137/1.9781611975994.112PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the model of online caching with machine learned advice, introduced by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, the goal is to solve the caching problem with an online algorithm that has access to next-arrival predictions: when each input element arrives, the algorithm is given a prediction of the next time when the element will reappear. The traditional model for online caching suffers from an Ω(log k) competitive ratio lower bound (on a cache of size k). In contrast, the augmented model admits algorithms which beat this lower bound when the predictions have low error, and asymptotically match the lower bound when the predictions have high error, even if the algorithms are oblivious to the prediction error. In particular, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> showed that there is a prediction-augmented caching algorithm with a competitive ratio of when the overall ℓ1 prediction error is bounded by η, and OPT is the cost of the optimal offline algorithm. The dependence on k in the competitive ratio is optimal, but the dependence on η/opt may be far from optimal. In this work, we make progress towards closing this gap. Our contributions are twofold. First, we provide an improved algorithm with a competitive ratio of O(1 + min((η/opt)/k, 1) log k). Second, we provide a lower bound of Ω(log min((η/opt)/(k log k), k)). Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.112"}, {"primary_key": "2683353", "vector": [], "sparse_vector": [], "title": "Counting and Finding Homomorphisms is Universal for Parameterized Complexity Theory.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Counting and Finding Homomorphisms is Universal for Parameterized Complexity Theory<PERSON><PERSON><PERSON> and <PERSON> and <PERSON>.2161 - 2180Chapter DOI:https://doi.org/10.1137/1.9781611975994.133PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Counting homomorphisms from a graph H into another graph G is a fundamental problem of (parameterized) counting complexity theory. In this work, we study the case where both graphs H and G stem from given classes of graphs: H ϵ and G ϵ . By this, we combine the structurally restricted version of this problem (where the class = ┬ is the set of all graphs), with the language-restricted version (where the class = ┬ is the set of all graphs). The structurally restricted version allows an exhaustive complexity classification for classes : Either we can count all homomorphisms in polynomial time (if the treewidth of is bounded), or the problem becomes #W[1]-hard [<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>h.Comp.Sci'04]. In contrast, in this work, we show that the combined view most likely does not admit such a complexity dichotomy. Our main result is a construction based on Kneser graphs that associates every problem P in #W[1] with two classes of graphs and such that the problem P is equivalent to the problem #Hom( → ) of counting homomorphisms from a graph in to a graph in . In view of Ladner's seminal work on the existence of NP-intermediate problems [J.ACM'75] and its adaptations to the parameterized setting, a classification of the class #W[1] in fixed-parameter tractable and #W[1]-complete cases is unlikely. Hence, obtaining a complete classification for the problem #Hom( → ) seems unlikely. Further, our proofs easily adapt to W[1] and the problem of deciding whether a homomorphism between graphs exists. In search of complexity dichotomies, we hence turn to special graph classes. Those classes include line graphs, claw-free graphs, perfect graphs, and combinations thereof, and F-colorable graphs for fixed graphs F. As a special case, we obtain an easy proof of the parameterized intractability result of the problem of counting k-matchings in bipartite graphs. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011Key words:Parameterized complexity theory, counting problems, graph homomorphisms, Kneser graphs", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.133"}, {"primary_key": "2683354", "vector": [], "sparse_vector": [], "title": "Reducing approximate Longest Common Subsequence to approximate Edit Distance.", "authors": ["<PERSON>via<PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Reducing approximate Longest Common Subsequence to approximate Edit DistanceAviad <PERSON> and <PERSON> and <PERSON>.1591 - 1600Chapter DOI:https://doi.org/10.1137/1.9781611975994.98PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Given a pair of n-character strings, the problems of computing their Longest Common Subsequence and Edit Distance have been extensively studied for decades. For exact algorithms, LCS and Edit Distance (with character insertions and deletions) are equivalent; the state of the art running time is (almost) quadratic in n, and this is tight under plausible fine-grained complexity assumptions. But for approximation algorithms the picture is different: there is a long line of works with improved approximation factors for Edit Distance, but for LCS (with binary strings) only a trivial 1/2-approximation was known. In this work we give a reduction from approximate LCS to approximate Edit Distance, yielding the first efficient (1/2 + ϵ)-approximation algorithm for LCS for some constant ϵ > 0. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.98"}, {"primary_key": "2683355", "vector": [], "sparse_vector": [], "title": "On the Performance of Reed-Muller Codes with respect to Random Errors and Erasures.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)On the Performance of Reed-Muller Codes with respect to Random Errors and Erasures<PERSON><PERSON> and <PERSON> and <PERSON>.1357 - 1376Chapter DOI:https://doi.org/10.1137/1.9781611975994.82PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract This work proves new results on the ability of binary Reed-Muller codes to decode from random errors and erasures. Specifically, we prove that RM codes with m variables and degree γm, for some explicit constant γ achieve capacity for random erasures (i.e. for the binary erasure channel) and for random errors (for the binary symmetric channel). Earlier, it was known that RM codes achieve capacity for the binary symmetric channel for degrees r = o(m). For the binary erasure channel it was known that RM codes achieve capacity for degree . Thus, our results provide a new range of parameters for which RM achieve capacity for these two well studied channels. In addition, our results imply that for every ϵ > 0 (in fact we can get up to RM codes of degree r < (1/2 – ϵ)m can correct a fraction of 1 – o(1) random erasures with high probability. We also show that, information theoretically, such codes can handle a fraction of random errors with high probability. For example, given noisy evaluations of a degree 0.499m polynomial, it is possible to interpolate it even if a random 0. 499 fraction of the evaluations were corrupted, with high probability. While the o(1) terms are not the correct ones to ensure capacity, these results show that RM codes of rates up to 1/poly(log n) (where n = 2m is the block length) are is some sense as good as capacity achieving codes. We obtain these results by proving improved bounds on the weight distribution of Reed-Muller codes of high degrees. Namely, given weight β ϵ (0, 1) we prove an upper bound on the number of codewords of relative weight at most β. We obtain new results in two different settings: for weights β < 1/2 and for weights that are close to 1/2. Our results for weights close to 1/2 also answer an open problem posed by Beame et al. [10]. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.82"}, {"primary_key": "2683356", "vector": [], "sparse_vector": [], "title": "Chasing Convex Bodies Optimally.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Chasing Convex Bodies OptimallyMark SellkeMark Sellkepp.1509 - 1518Chapter DOI:https://doi.org/10.1137/1.9781611975994.92PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In the chasing convex bodies problem, an online player receives a request sequence of N convex sets K1, …, Kn contained in a normed space ℝd. The player starts at x0 ϵ ℝd, and after observing each Kn picks a new point xn ϵ Kn. At each step the player pays a movement cost of ||xn – xn–1||. The player aims to maintain a constant competitive ratio against the minimum cost possible in hindsight, i.e. knowing all requests in advance. The existence of a finite competitive ratio for convex body chasing was first conjectured in 1991 by <PERSON> and <PERSON><PERSON> in [FL93]. This conjecture was recently resolved in [BLLS19] which proved an exponential 2O(d) upper bound on the competitive ratio. In this paper, we drastically improve the exponential upper bound. We give an algorithm achieving competitive ratio d for arbitrary normed spaces, which is exactly tight for ℓ∞ In Euclidean space, our algorithm achieves nearly optimal competitive ratio , compared to a lower bound of . Our approach extends the recent work [BKL +20] which chases nested convex bodies using the classical Steiner point of a convex body. We define the functional Steiner point of a convex function and apply it to the work function to obtain our algorithm. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.92"}, {"primary_key": "2683357", "vector": [], "sparse_vector": [], "title": "Locally Private k-Means Clustering.", "authors": ["<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Locally Private k-Means ClusteringUri StemmerUri Stemmerpp.548 - 559Chapter DOI:https://doi.org/10.1137/1.9781611975994.33PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We design a new algorithm for the Euclidean k-means problem that operates in the local model of differential privacy. Unlike in the non-private literature, differentially private algorithms for the k-means incur both additive and multiplicative errors. Our algorithm significantly reduces the additive error while keeping the multiplicative error the same as in previous state-of-the-art results. Specifically, on a database of size n, our algorithm guarantees O(1) multiplicative error and ≈ n1/2+α additive error for an arbitrarily small constant a > 0. All previous algorithms in the local model had additive error ≈ n2/3+a. We show that the additive error we obtain is almost optimal in terms of its dependency in the database size n. Specifically, we give a simple lower bound showing that every locally-private algorithm for the k-means must have additive error at least ≈ . Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.33"}, {"primary_key": "2683358", "vector": [], "sparse_vector": [], "title": "The Communication Complexity of Optimization.", "authors": ["Santosh S<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)The Communication Complexity of Optimization<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>.1733 - 1752Chapter DOI:https://doi.org/10.1137/1.9781611975994.106PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We consider the communication complexity of a number of distributed optimization problems. We start with the problem of solving a linear system. Suppose there is a coordinator together with s servers P1, …, Ps, the i-th of which holds a subset A(i) x = b(i) of ni constraints of a linear system in d variables, and the coordinator would like to output an x ϵ ℝd for which A(i) x = b(i) for i = 1, …, s. We assume each coefficient of each constraint is specified using L bits. We first resolve the randomized and deterministic communication complexity in the point-to-point model of communication, showing it is (d2 L + sd) and (sd2L), respectively. We obtain similar results for the blackboard communication model. As a result of independent interest, we show the probability a random matrix with integer entries in {–2L, …, 2L} is invertible is 1–2−Θ(dL), whereas previously only 1 – 2−Θ(d) was known. When there is no solution to the linear system, a natural alternative is to find the solution minimizing the ℓp loss, which is the ℓp regression problem. While this problem has been studied, we give improved upper or lower bounds for every value of p ≥ 1. One takeaway message is that sampling and sketching techniques, which are commonly used in earlier work on distributed optimization, are neither optimal in the dependence on d nor on the dependence on the approximation ε, thus motivating new techniques from optimization to solve these problems. Towards this end, we consider the communication complexity of optimization tasks which generalize linear systems, such as linear, semi-definite, and convex programming. For linear programming, we first resolve the communication complexity when d is constant, showing it is (sL) in the point-to-point model. For general d and in the point-to-point model, we show an Õ(sd3L) upper bound and an (d2 L + sd) lower bound. In fact, we show if one perturbs the coefficients randomly by numbers as small as 2−Θ(L), then the upper bound is Õ(sd2L) + poly(dL), and so this bound holds for almost all linear programs. Our study motivates understanding the bit complexity of linear programming, which is related to the running time in the unit cost RAM model with words of O(log(nd)) bits, and we give the fastest known algorithms for linear programming in this model. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.106"}, {"primary_key": "2683359", "vector": [], "sparse_vector": [], "title": "How to Store a Random Walk.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Huacheng Yu"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)How to Store a Random WalkEmanuele Viola, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> YuEmanuel<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> Yupp.426 - 445Chapter DOI:https://doi.org/10.1137/1.9781611975994.26PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Motivated by storage applications, we study the following data structure problem: an encoder wishes to store a collection of jointly-distributed files : = (X1, X2, …, Xn) ∼ µ which are correlated (Hµ() ≤ Σi Hµ(Xi)), using as little (expected) memory as possible, such that each individual file Xi can be recovered quickly with few (ideally constant) memory accesses. In the case of independent random files, a dramatic result by <PERSON><PERSON><PERSON><PERSON><PERSON> (FOCS'08) and subsequently by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (STOC'10) shows that it is possible to store using just a constant number of extra bits beyond the information-theoretic minimum space, while at the same time decoding each Xi in constant time. However, in the (realistic) case where the files are correlated, much weaker results are known, requiring at least Ω(n/poly lg n) extra bits for constant decoding time, even for \"simple\" joint distributions µ. We focus on the natural case of compressing Markov chains, i.e., storing a length-n random walk on any (possibly directed) graph G. Denoting by κ(G, n) the number of length-n walks on G, we show that there is a succinct data structure storing a random walk using lg2 κ(G, n) + O(lg n) bits of space, such that any vertex along the walk can be decoded in O(1) time on a word-RAM. If the graph is strongly connected (e.g., undirected), the space can be improved to only lg2 k(G, n) + 5 extra bits. For the harder task of matching the point-wise optimal space of the walk, i.e., the empirical entropy , we present a data structure with O(1) extra bits at the price of O(lg n) decoding time, and show that any improvement on this would lead to an improved solution on the long-standing Dictionary problem. All of our data structures support the online version of the problem with constant update and query time. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.26"}, {"primary_key": "2683360", "vector": [], "sparse_vector": [], "title": "Connectivity of Triangulation Flip Graphs in the Plane (Part I: Edge Flips).", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Connectivity of Triangulation Flip Graphs in the Plane (Part I: Edge Flips)<PERSON><PERSON> and Emo WelzlUli Wagne and Emo Welzlpp.2823 - 2841Chapter DOI:https://doi.org/10.1137/1.9781611975994.172PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract In a straight-line embedded triangulation of a point set P in the plane, removing an inner edge and—provided the resulting quadrilateral is convex—adding the other diagonal is called an edge flip. The (edge) flip graph has all triangulations as vertices, and a pair of triangulations is adjacent if they can be obtained from each other by an edge flip. The goal of this paper is to contribute to a better understanding of the flip graph, with an emphasis on its connectivity. For sets in general position, it is known that every triangulation allows at least edge flips (a tight bound) which gives the minimum degree of any flip graph for n points. We show that for every point set P in general position, the flip graph is at least -vertex connected. Somewhat more strongly, we show that the vertex connectivity equals the minimum degree occurring in the flip graph, i.e. the minimum number of flippable edges in any triangulation of P, provided P is large enough. Finally, we exhibit some of the geometry of the flip graph by showing that the flip graph can be covered by 1-skeletons of polytopes of dimension (products of associahedra). A corresponding result ((n – 3)-vertex connectedness) can be shown for the bistellar flip graph of partial triangulations, i.e. the set of all triangulations of subsets of P which contain all extreme points of P. This will be treated separately in a second part. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011Key words:triangulation, flip graph, graph connectivity, associahedron, subdivision, convex decomposition, flippable edge, simultaneously flippable edges, pseudo-simultaneously flippable edges, flip complex", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.172"}, {"primary_key": "2683361", "vector": [], "sparse_vector": [], "title": "Normalizers and permutational isomorphisms in simply-exponential time.", "authors": ["<PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Normalizers and permutational isomorphisms in simply-exponential timeDaniel WiebkingDaniel Wiebkingpp.230 - 238Chapter DOI:https://doi.org/10.1137/1.9781611975994.14PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We show that normalizers and permutational isomorphisms of permutation groups given by generating sets can be computed in time simply exponential in the degree of the groups. The result is obtained by exploiting canonical forms for permutation groups (up to permutational isomorphism). Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.14"}, {"primary_key": "2683362", "vector": [], "sparse_vector": [], "title": "Truly Subcubic Min-Plus Product for Less Structured Matrices, with Applications.", "authors": ["Virginia Vassilevska Williams", "<PERSON><PERSON><PERSON> Xu"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Truly Subcubic Min-Plus Product for Less Structured Matrices, with ApplicationsVirgin<PERSON> and <PERSON><PERSON><PERSON>gin<PERSON> and <PERSON><PERSON><PERSON>.12 - 29Chapter DOI:https://doi.org/10.1137/1.9781611975994.2PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract The All-Pairs Shortest Paths (APSP) problem is one of the most basic problems in computer science. The fastest known algorithms for APSP in n-node graphs run in n3−o(1) time, and it is a big open problem whether a truly subcubic, O(n3−ε) for ε > 0 time algorithm exists for APSP. The Min-Plus product of two n × n matrices is known to be equivalent to APSP, where the optimal running times of the two problems differ by at most a constant factor. A natural way to approach understanding the complexity of APSP is thus understanding what structure (if any) is needed to solve Min-Plus product in truly subcubic time. The goal of this paper is to get truly subcubic algorithms for Min-Plus product for less structured inputs than what was previously known, and to apply them to versions of APSP and other problems. The results are as follows: (1)Our main result is the first truly subcubic algorithm for the Min-Plus product of two n × n matrices A and B with polylog n bit integer entries, where B has a partitioning into nε × nε blocks (for any ε > 0) where each block is at most nδ-far (for δ 0 for this problem can be used to solve Boolean matrix multiplication combinatorially in truly subcubic time. We give the first O(n1–5−ε) time for ε > 0 algorithm for this batch range mode problem, showing that the hardness is indeed constrained to combinatorial algorithms.(4)Our final application is to the Maximum Subarray problem: given an n × n integer matrix, find the contiguous subarray of maximum entry sum. We show that Maximum Subarray can be solved in truly subcubic, O(n3−ε) (for ε > 0) time, as long as every entry of the input matrix is no larger than O(n0.62) in absolute value. This is the first truly subcubic algorithm for an interesting case of Maximum Subarray. The Maximum Subarray problem with arbitrary integer entries is known to be subcubically equivalent to APSP, in that a truly subcubic, O(n3−ε) time algorithm for ε > 0 for one problem would imply a truly subcubic algorithm for the other. Because of this it is believed that Maximum Subarray does not admit truly subcubic algorithms, without a restriction on the inputs. We also improve all the known conditional hardness results for the d-dimensional variant of Maximum Subarray, showing that many of the known algorithms are likely tight. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.2"}, {"primary_key": "2683363", "vector": [], "sparse_vector": [], "title": "Improved hardness for H-colourings of G-colourable graphs.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We present new results on approximate colourings of graphs and, more generally, approximate H-colourings and promise constraint satisfaction problems. First, we show NP-hardness of colouring $k$-colourable graphs with $\\binom{k}{\\lfloor k/2\\rfloor}-1$ colours for every $k\\geq 4$. This improves the result of <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\\v{s}al [STOC'19], who gave NP-hardness of colouring $k$-colourable graphs with $2k-1$ colours for $k\\geq 3$, and the result of <PERSON> [APPROX-RANDOM'13], who gave NP-hardness of colouring $k$-colourable graphs with $2^{k^{1/3}}$ colours for sufficiently large $k$. Thus, for $k\\geq 4$, we improve from known linear/sub-exponential gaps to exponential gaps. Second, we show that the topology of the box complex of H alone determines whether H-colouring of G-colourable graphs is NP-hard for all (non-bipartite, H-colourable) G. This formalises the topological intuition behind the result of <PERSON><PERSON><PERSON> and <PERSON><PERSON>\\v{s}al [FOCS'19] that 3-colouring of G-colourable graphs is NP-hard for all (3-colourable, non-bipartite) G. We use this technique to establish NP-hardness of H-colouring of G-colourable graphs for H that include but go beyond $K_3$, including square-free graphs and circular cliques (leaving $K_4$ and larger cliques open). Underlying all of our proofs is a very general observation that adjoint functors give reductions between promise constraint satisfaction problems.", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.86"}, {"primary_key": "2683366", "vector": [], "sparse_vector": [], "title": "On the Tractability of Public Persuasion with No Externalities.", "authors": ["Haifeng Xu"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)On the Tractability of Public Persuasion with No ExternalitiesHaifeng XuHaifeng Xupp.2708 - 2727Chapter DOI:https://doi.org/10.1137/1.9781611975994.165PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Persuasion studies how a principal can influence agents' decisions via strategic information revelation — often described as a signaling scheme — in order to yield the most desirable equilibrium outcome. A basic question that has attracted much recent attention is how to compute the optimal public signaling scheme, a.k.a., public persuasion, which is motivated by various applications including auction design, routing, voting, marketing, queuing, etc. Unfortunately, most algorithmic studies in this space exhibit quite negative results and are rifle with computational intractability. Given such background, this paper seeks to understand when public persuasion is tractable and how tractable it can be. We focus on a fundamental multi-agent persuasion model introduced by <PERSON><PERSON> and <PERSON> [3]: many agents, no inter-agent externalities and binary agent actions, and identify well-motivated circumstances under which efficient algorithms are possible. En route, we also develop new algorithmic techniques and demonstrate that they can be applicable to other public persuasion problems or even beyond. We start by proving that optimal public persuasion in our model is fixed parameter tractable. Our main result here builds on an interesting connection to a basic question in combinatorial geometry: how many cells can n hyperplanes divide ℝd into? We use this connection to show a new characterization of public persuasion, which then enables efficient algorithm design. Second, we relax agent incentives and show that optimal public persuasion admits a bi-criteria PTAS for the widely studied class of monotone submodular objectives, and this approximation is tight. To prove this result, we establish an intriguing \"noise stability\" property of submodular functions which strictly generalizes the key result of Cheraghchi et al. [15], originally motivated by applications of learning submodular functions and differential privacy. Finally, motivated by automated application of persuasion, we consider relaxing the equilibrium concept of the model to coarse correlated equilibrium. Here, using a sophisticated primal-dual analysis, we prove that optimal public persuasion admits an efficient algorithm if and only if the combinatorial problem of maximizing the sender's objective minus any linear function can be solved efficiently, thus establishing their polynomial-time equivalence. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.165"}, {"primary_key": "2683367", "vector": [], "sparse_vector": [], "title": "A Strongly Polynomial Algorithm for Finding a Shortest Non-zero Path in Group-Labeled Graphs.", "authors": ["<PERSON><PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)A Strongly Polynomial Algorithm for Finding a Shortest Non-zero Path in Group-Labeled GraphsY<PERSON><PERSON> YamaguchiY<PERSON> Yamaguchipp.1923 - 1932Chapter DOI:https://doi.org/10.1137/1.9781611975994.118PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract We study a constrained shortest path problem in group-labeled graphs with nonnegative edge length, called the shortest non-zero path problem. Depending on the group in question, this problem includes two types of tractable variants in undirected graphs: one is the parity-constrained shortest path/cycle problem, and the other is computing a shortest noncontractible cycle in surface-embedded graphs. For the shortest non-zero path problem with respect to finite abelian groups, <PERSON><PERSON> and <PERSON><PERSON><PERSON> (2017) proposed a randomized, pseudopolynomial algorithm via permanent computation. For a slightly more general class of groups, <PERSON><PERSON><PERSON> (2016) showed a reduction of the problem to the weighted linear matroid parity problem. In particular, some cases are solved in strongly polynomial time via the reduction with the aid of a deterministic, polynomial algorithm for the weighted linear matroid parity problem developed by <PERSON><PERSON><PERSON> and <PERSON> (2017), which generalizes a well-known fact that the parity-constrained shortest path problem is solved via weighted matching. In this paper, as the first general solution independent of the group, we present a rather simple, deterministic, and strongly polynomial algorithm for the shortest non-zero path problem. The algorithm is based on Dijkstra's algorithm for the unconstrained shortest path problem and Edmonds' blossom shrinking technique in matching algorithms, and clarifies a common tractable feature behind the parity and topological constraints in the shortest path/cycle problem. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.118"}, {"primary_key": "2683369", "vector": [], "sparse_vector": [], "title": "Sticky Brownian Rounding and its Applications to Constraint Satisfaction Problems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Aleksan<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Previous chapter Next chapter Full AccessProceedings Proceedings of the 2020 ACM-SIAM Symposium on Discrete Algorithms (SODA)Sticky Brownian Rounding and its Applications to Constraint Satisfaction Problems<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.854 - 873Chapter DOI:https://doi.org/10.1137/1.9781611975994.52PDFBibTexSections ToolsAdd to favoritesExport CitationTrack CitationsEmail SectionsAboutAbstract Semi-definite programming is a powerful tool in the design and analysis of approximation algorithms for combinatorial optimization problems. In particular, the random hyperplane rounding method of <PERSON><PERSON><PERSON> and <PERSON> [23] has been extensively studied for more than two decades, resulting in various extensions to the original technique and beautiful algorithms for a wide range of applications. Despite the fact that this approach yields tight approximation guarantees for some problems, e.g., Max-Cut, for many others, e.g., Max-SAT and Max-DiCut, the tight approximation ratio is still unknown. One of the main reasons for this is the fact that very few techniques for rounding semi-definite relaxations are known. In this work, we present a new general and simple method for rounding semi-definite programs, based on Brownian motion. Our approach is inspired by recent results in algorithmic discrepancy theory. We develop and present tools for analyzing our new rounding algorithms, utilizing mathematical machinery from the theory of Brownian motion, complex analysis, and partial differential equations. Focusing on constraint satisfaction problems, we apply our method to several classical problems, including Max-Cut, Max-2SAT, and Max-DiCut, and derive new algorithms that are competitive with the best known results. To illustrate the versatility and general applicability of our approach, we give new approximation algorithms for the Max-Cut problem with side constraints that crucially utilizes measure concentration results for the Sticky Brownian Motion, a feature missing from hyperplane rounding and its generalizations. Previous chapter Next chapter RelatedDetails Published:2020eISBN:978-1-61197-599-4 https://doi.org/10.1137/1.9781611975994Book Series Name:ProceedingsBook Code:PRDA20Book Pages:xxii + 3011", "published": "2020-01-01", "category": "soda", "pdf_url": "", "sub_summary": "", "source": "soda", "doi": "10.1137/1.9781611975994.52"}]