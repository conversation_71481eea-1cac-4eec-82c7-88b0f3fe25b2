[{"primary_key": "1135737", "vector": [], "sparse_vector": [], "title": "Making Dynamic Page Coalescing Effective on Virtualized Clouds.", "authors": ["Weiwei Jia", "<PERSON><PERSON>", "Jianchen Shan", "<PERSON><PERSON>"], "summary": "Using huge pages has become a mainstream method to reduce address translation overhead for big memory workloads in modern computer systems. To create huge pages, system software usually uses page coalescing methods to dynamically combine contiguous base pages. Though page coalescing methods help effectively reduce address translation overhead on native systems, as the paper shows, their effectiveness is substantially undermined on virtualized platforms.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567487"}, {"primary_key": "1135738", "vector": [], "sparse_vector": [], "title": "Morty: Scaling Concurrency Control with Re-Execution.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Serializable systems often perform poorly under high contention. In this work, we analyze this performance limitation through a novel take on conflict windows. Through the lens of these windows, we develop a new concurrency control technique that leverages transaction re-execution to improve throughput scalability under high contention. Our system, Morty, achieves up to 1.7x-96x the throughput of state-of-the-art systems, with similar or better latency.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567500"}, {"primary_key": "1135739", "vector": [], "sparse_vector": [], "title": "OLPart: Online Learning based Resource Partitioning for Colocating Multiple Latency-Critical Jobs on Commodity Computers.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Colocating multiple jobs on the same server has been a commonly used approach for improving resource utilization in cloud environments. However, performance interference due to the contention over shared resources makes resource partitioning an important research problem. Partitioning multiple resources coordinately is particularly challenging when multiple latency-critical (LC) jobs are colocated with best-effort (BE) jobs, since the QoS needs to be protected for all the LC jobs. So far, this problem is not well-addressed in the literatures.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567490"}, {"primary_key": "1135740", "vector": [], "sparse_vector": [], "title": "CFS: Scaling Metadata Service for Distributed File System via Pruned Scope of Critical Sections.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yan Sun", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "There is a fundamental tension between metadata scalability and POSIX semantics within distributed file systems. The bottleneck lies in the coordination, mainly locking, used for ensuring strong metadata consistency, namely, atomicity and isolation. CFS is a scalable, fully POSIX-compliant distributed file system that eliminates the metadata management bottleneck via pruning the scope of critical sections for reduced locking overhead. First, CFS adopts a tiered metadata organization to scale file attributes and the remaining namespace hierarchies independently with appropriate partitioning and indexing methods, eliminating cross-shard distributed coordination. Second, it further scales up the single metadata shard performance by single-shard atomic primitives, shortening the metadata requests' lifespan and removing spurious conflicts. Third, CFS drops the metadata proxy layer but employs the light-weight, scalable client-side metadata resolving. CFS has been running in the production environment of Baidu AI Cloud for three years. Our evaluation with a 50-node cluster and microbenchmarks shows that CFS simultaneously improves the throughput of baselines like HopsFS and InfiniFS by 1.76--75.82× and 1.22--4.10×, and reduces their average latency by up to 91.71% and 54.54%, respectively. Under cases with higher contention and larger directories, CFS' throughput benefits expand by one order of magnitude. For three real-world workloads with data accesses, CFS introduces 1.62--2.55× end-to-end throughput speedups and 35.06--62.47% tail latency reductions over InfiniFS.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587443"}, {"primary_key": "1135741", "vector": [], "sparse_vector": [], "title": "Unikernel Linux (UKL).", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "This paper presents Unikernel Linux (UKL), a path toward integrating unikernel optimization techniques in Linux, a general purpose operating system. UKL adds a configuration option to Linux allowing for a single, optimized process to link with the kernel directly, and run at supervisor privilege. This UKL process does not require application source code modification, only a re-link with our, slightly modified, Linux kernel and glibc. Unmodified applications show modest performance gains out of the box, and developers can further optimize applications for more significant gains (e.g. 26% throughput improvement for Redis). UKL retains support for co-running multiple user level processes capable of communicating with the UKL process using standard IPC. UKL preserves Linux's battle-tested codebase, community, and ecosystem of tools, applications, and hardware support. UKL runs both on bare-metal and virtual servers and supports multi-core execution. The changes to the Linux kernel are modest (1250 LOC).", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587458"}, {"primary_key": "1135742", "vector": [], "sparse_vector": [], "title": "With Great Freedom Comes Great Opportunity: Rethinking Resource Allocation for Serverless Functions.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Current serverless offerings give users limited flexibility for configuring the resources allocated to their function invocations. This simplifies the interface for users to deploy server-less computations but creates deployments that are resource inefficient. In this paper, we take a principled approach to the problem of resource allocation for serverless functions, analyzing the effects of automating this choice in a way that leads to the best combination of performance and cost. In particular, we systematically explore the opportunities that come with decoupling memory and CPU resource allocations and also enabling the use of different VM types, and we find a rich trade-off space between performance and cost. The provider can use this in a number of ways, e.g., exposing all these parameters to the user; eliding preferences for performance and cost from users and simply offer the same performance with lower cost; or exposing a small number of choices for users to trade performance for cost.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567506"}, {"primary_key": "1135743", "vector": [], "sparse_vector": [], "title": "REFL: Resource-Efficient Federated Learning.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Federated Learning (FL) enables distributed training by learners using local data, thereby enhancing privacy and reducing communication. However, it presents numerous challenges relating to the heterogeneity of the data distribution, device capabilities, and participant availability as deployments scale, which can impact both model convergence and bias. Existing FL schemes use random participant selection to improve fairness; however, this can result in inefficient use of resources and lower quality training. In this work, we systematically address the question of resource efficiency in FL, showing the benefits of intelligent participant selection, and incorporation of updates from straggling participants. We demonstrate how these factors enable resource efficiency while also improving trained model quality.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567485"}, {"primary_key": "1135744", "vector": [], "sparse_vector": [], "title": "Palette Load Balancing: Locality Hints for Serverless Functions.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "summary": "Function-as-a-Service (FaaS) serverless computing enables a simple programming model with almost unbounded elasticity. Unfortunately, current FaaS platforms achieve this flexibility at the cost of lower performance for data-intensive applications compared to a serverful deployment. The ability to have computation close to data is a key missing feature. We introduce Palette load balancing, which offers FaaS applications a simple mechanism to express locality to the platform, through hints we term \"colors\". <PERSON>lette maintains the serverless nature of the service - users are still not allocating resources - while allowing the platform to place successive invocations related to each other on the same executing node. We compare a prototype of the Palette load balancer to a state-of-the-art locality-oblivious load balancer on representative examples of three applications. For a serverless web application with a local cache, <PERSON><PERSON> improves the hit ratio by 6x. For a serverless version of Dask, <PERSON><PERSON> improves run times by 46% and 40% on Task Bench and TPC-H, respectively. On a serverless version of NumS, <PERSON><PERSON> improves run times by 37%. These improvements largely bridge the gap to serverful implementation of the same systems.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567496"}, {"primary_key": "1135747", "vector": [], "sparse_vector": [], "title": "Groundhog: Efficient Request Isolation in FaaS.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Security is a core responsibility for Function-as-a-Service (FaaS) providers. The prevailing approach isolates concurrent executions of functions in separate containers. However, successive invocations of the same function commonly reuse the runtime state of a previous invocation in order to avoid container cold-start delays. Although efficient, this container reuse has security implications for functions that are invoked on behalf of differently privileged users or administrative domains: bugs in a function's implementation --- or a third-party library/runtime it depends on --- may leak private data from one invocation of the function to a subsequent one.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567503"}, {"primary_key": "1135750", "vector": [], "sparse_vector": [], "title": "R2C: AOCR-Resilient Diversity with Reactive and Reflective Camouflage.", "authors": ["<PERSON>", "<PERSON>"], "summary": "Address-oblivious code reuse, AOCR for short, poses a substantial security risk, as it remains unchallenged. If neglected, adversaries have a reliable way to attack systems, offering an operational and profitable strategy. AOCR's authors conclude that software diversity cannot mitigate AOCR, because it exposes fundamental limits to diversification.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587439"}, {"primary_key": "1135753", "vector": [], "sparse_vector": [], "title": "Foxhound: Server-Grade Observability for Network-Augmented Applications.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Theophilus <PERSON>"], "summary": "There is a growing move to offload functionality, e.g., TCP or key-value stores, into programmable networks - either on SmartNICs or programmable switches. While offloading promises significant performance boosts, these programmable devices often provide little visibility into their performance. Moreover, many existing tools for analyzing and debugging performance problems, e.g., distributed tracing, do not extend into these devices.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567502"}, {"primary_key": "1135758", "vector": [], "sparse_vector": [], "title": "Aggregate VM: Why Reduce or Evict VM&apos;s Resources When You Can Borrow Them From Other Nodes?", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Hardware resource fragmentation is a common issue in data centers. Traditional solutions based on migration or overcommitment are unacceptably slow, and modern commercial or research solutions like Spot VM may reduce or evict VM's resources anytime. We propose an alternative solution that does not suffer from these drawbacks, the Aggregate VM. We introduce a new distributed hypervisor design, the resource-borrowing hypervisor, which creates Aggregate VMs: distributed VMs that temporarily aggregate fragmented resources belonging to different host machines, which require mobility of virtual CPUs, memory and IO devices. We implement a prototype, FragVisor, which runs guest software transparently. We also propose minimal modifications to the guest OS that can enable significant performance gains. We evaluate FragVisor over a set of microbenchmarks and IaaS-style real applications. Although Aggregate VMs are not a perfect fit for every type of applications, some workloads enjoy significant speedups compared to overcommitted scenarios (up to 3.9x with 4 distributed vCPUs). We further demonstrate that FragVisor is faster than a state-of-the-art competitor, GiantVM (up to 2.5x).", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587452"}, {"primary_key": "1135765", "vector": [], "sparse_vector": [], "title": "Mumak: Efficient and Black-Box Bug Detection for Persistent Memory.", "authors": ["João <PERSON>", "<PERSON>", "<PERSON>"], "summary": "The advent of Persistent Memory (PM) opens the door to novel application designs that explore its performance and durability benefits. However, there is no free lunch, and to program PM applications, developers need to be aware of potential inconsistent application state upon machine or application crashes. To overcome this difficulty, several tools have been proposed to detect the presence of the so-called crash-consistency bugs. While these are effective in detecting a variety of bugs, they present several key limitations, namely relying on application-specific semantics, requiring the programmer to manually annotate the program or modify the PM library, and relying on techniques with poor scalability, making them impractical for production code.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587447"}, {"primary_key": "1135767", "vector": [], "sparse_vector": [], "title": "Diablo: A Benchmark Suite for Blockchains.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "With the recent advent of blockchains, we have witnessed a plethora of blockchain proposals. These proposals range from using work to using time, storage or stake in order to select blocks to be appended to the chain. As a drawback it makes it difficult for the application developer to choose the right blockchain to support their applications. In particular, the scalability and performance one can obtain from a specific blockchain is typically unknown. The claimed results are often obtained in isolation by the developers of the blockchain themselves. The experimental conditions corresponding to these results are generally missing and the lack of details make these results irreproducible.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567482"}, {"primary_key": "1135769", "vector": [], "sparse_vector": [], "title": "Dissecting BFT Consensus: In Trusted Components we Trust!", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "The growing interest in reliable multi-party applications has fostered widespread adoption of Byzantine Fault-Tolerant (bft) consensus protocols. Existing bft protocols need f more replicas than Paxos-style protocols to prevent equivocation attacks. trust-bft protocols seek to minimize this cost by making use of trusted components at replicas.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587455"}, {"primary_key": "1135771", "vector": [], "sparse_vector": [], "title": "TEA: A General-Purpose Temporal Graph Random Walk Engine.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Chang<PERSON> He", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Many real-world graphs are temporal in nature, where the temporal information indicates when a particular edge is changed (e.g., edge insertion and deletion). Performing random walks on such temporal graphs is of paramount value. The state-of-the-art sampling strategies are tailored for conventional static graphs and thus cannot effectively tackle the dynamic nature of temporal graphs due to several significant efficiency challenges, i.e., high sampling complexity, gigantic index space, and poor programmability.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567491"}, {"primary_key": "1135774", "vector": [], "sparse_vector": [], "title": "Accelerating Graph Mining Systems with Subgraph Morphing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Graph mining applications analyze the structural properties of large graphs. These applications are computationally expensive because finding structural patterns requires checking subgraph isomorphism, which is NP-complete. This paper exploits the sub-structural similarities across different patterns by employing Subgraph Morphing to accurately infer the results for a given set of patterns from the results of a completely different set of patterns that are less expensive to compute. To enable Subgraph Morphing in practice, we develop efficient query transformation techniques as well as automatic result conversion strategies for different application scenarios. We have implemented Subgraph Morphing in four state-of-the-art graph mining and subgraph matching systems: Peregrine, AutoMine/- GraphZero, GraphPi, and BigJoin; a thorough evaluation demonstrates that Subgraph Morphing improves the performance of these four systems by 34×, 10×, 18×, and 13×, respectively.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567489"}, {"primary_key": "1135776", "vector": [], "sparse_vector": [], "title": "Fast and Efficient Model Serving Using Multi-GPUs with Direct-Host-Access.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "As deep learning (DL) inference has been widely adopted for building user-facing applications in many domains, it is increasingly important for DL inference servers to achieve high throughput while preserving bounded latency. DL inference requests can be immediately served if the corresponding model is already in the GPU memory. Otherwise, it needs to load the model from host to GPU, adding a significant delay to inference. This paper proposes DeepPlan to minimize inference latency while provisioning DL models from host to GPU in server environments. First, we take advantage of the direct-host-access facility provided by commodity GPUs, allowing access to particular layers of models in the host memory directly from GPU without loading. Second, we parallelize model transmission across multiple GPUs to reduce the time for loading models from host to GPU. We show that a single inference can achieve a 1.94× speedup compared with the state-of-the-art pipelining approach for BERT-Base. When deploying multiple BERT, RoBERTa, and GPT-2 instances on a DL inference serving system, DeepPlan shows a significant performance improvement compared to the pipelining technique and stable 99% tail latency.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567508"}, {"primary_key": "1135777", "vector": [], "sparse_vector": [], "title": "Diagnosing Kernel Concurrency Failures with AITIA.", "authors": ["<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> Lee", "Insik Shin", "<PERSON><PERSON>"], "summary": "Kernel concurrency failures are notoriously difficult to identify and diagnose their fundamental reason, the root cause. Kernel concurrency bugs frequently involve challenging patterns such as multi-variable races, data races with asynchronous kernel threads, and pervasive benign races. We perform an in-depth study of real-world kernel concurrency bugs and elicit three requirements: comprehensiveness, pattern-agnostic, and conciseness.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567486"}, {"primary_key": "1135778", "vector": [], "sparse_vector": [], "title": "Saba: Rethinking Datacenter Network Allocation from Application&apos;s Perspective.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Today's datacenter workloads increasingly comprise distributed data-intensive applications, including data analytics, graph processing, and machine-learning training. These applications are bandwidth-hungry and often congest the datacenter network, resulting in poor network performance, which hurts application completion time. Efforts made to address this problem generally aim to achieve max-min fairness at the flow or application level. We observe that splitting the bandwidth equally among workloads is sub-optimal for aggregate application-level performance because various workloads exhibit different sensitivity to network bandwidth: for some workloads, even a small reduction in the available bandwidth yields a significant increase in completion time; for others, the completion time is largely insensitive to the available bandwidth.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587450"}, {"primary_key": "1135779", "vector": [], "sparse_vector": [], "title": "All-Flash Array Key-Value Cache for Large Objects.", "authors": ["<PERSON><PERSON><PERSON>", "Jinwook Bae", "Minjeong Yuk", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "We present BigKV, a key-value cache specifically designed for caching large objects in an all-flash array (AFA). The design of BigKV is centered around the unique property of a cache: since it contains a copy of the data, exact bookkeeping of what is in the cache is not critical for correctness. By ignoring hash collisions, approximating metadata information, and allowing data loss from failures, BigKV significantly increases the cache hit ratio and keeps more useful objects in the system. Experiments on a real AFA show that our design increases the throughput by 3.1× on average and reduces the average and tail latency by 57% and 81%, respectively.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567509"}, {"primary_key": "1135782", "vector": [], "sparse_vector": [], "title": "Chipmunk: Investigating Crash-Consistency in Persistent-Memory File Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Om Saran K. R. E.", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "We present Chipmunk, a new framework to test persistent-memory (PM) file systems for crash-consistency bugs. Using Chipmunk, we discovered 23 new bugs across five PM file systems; most bugs have been confirmed and fixed by developers. The discovered bugs have serious consequences, including making the file system un-mountable or breaking rename atomicity. We present a detailed study of the bugs found using Chipmunk and discuss important lessons learned for designing and testing PM file systems.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567498"}, {"primary_key": "1135783", "vector": [], "sparse_vector": [], "title": "FlowKV: A Semantic-Aware Store for Large-Scale State Management of Stream Processing Engines.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jinsol Park", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Taegeon Um", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "We propose FlowKV, a persistent store tailored for large-scale state management of streaming applications. Unlike existing KV stores, FlowKV leverages information from stream processing engines by taking a principled approach toward exploiting information about how and when the applications access data. FlowKV categorizes data access patterns of window operations according to how window boundaries are set and how tuples inside a window are aggregated, and deploys customized in-memory and on-disk data structures optimized for each pattern. In addition, FlowKV takes window metadata as explicit arguments of read and write methods to predict the moment when a window is read, and then loads the tuples of windows in batches from storage ahead of time. Using the NEXMark benchmark as workload, our experiments show that Apache Flink on FlowKV outperforms Flink on RocksDB or Faster with up to 4.12× throughput gain.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567493"}, {"primary_key": "1135784", "vector": [], "sparse_vector": [], "title": "OFence: Pairing Barriers to Find Concurrency Bugs in the Linux Kernel.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Knowing which functions may execute concurrently is key to finding concurrency-related bugs. Existing tools infer the possibility of concurrency using dynamic analysis or by pairing functions that use the same locks. Code that relies on more relaxed concurrency controls is, by and large, out of the reach of existing concurrency-related bug-tracking tools.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567504"}, {"primary_key": "1135785", "vector": [], "sparse_vector": [], "title": "ICE: Collaborating Memory and Process Management for User Experience on Resource-limited Mobile Devices.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Mobile devices with limited resources are prevalent as they have a relatively low price. Providing a good user experience with limited resources has been a big challenge. This paper found that foreground applications are often unexpectedly interfered by background applications' memory activities. Improving user experience on resource-limited mobile devices calls for a strong collaboration between memory and process management. This paper proposes a framework, Ice, to optimize the user experience on resource-limited mobile devices. With Ice, processes that will cause frequent refaults in the background are identified and frozen accordingly. The frozen application will be thawed when memory condition allows. Evaluation of resource-limited mobile devices demonstrates that the user experience is effectively improved with Ice. Specifically, Ice boosts the frame rate by 1.57x on average over the state-of-the-art.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567497"}, {"primary_key": "1135786", "vector": [], "sparse_vector": [], "title": "A2TP: Aggregator-aware In-network Aggregation for Multi-tenant Learning.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jingling Liu", "<PERSON><PERSON><PERSON>"], "summary": "Distributed Machine Learning (DML) techniques are widely used to accelerate the training of large-scale machine learning models. However, during training iterations, gradients need to be frequently aggregated across multiple workers, resulting in communication bottleneck. To reduce the communication overhead of DML, several In-Network Aggregation (INA) protocols are proposed to reduce the volume of aggregation traffic by offloading aggregation functions into switches, thus alleviating network bottlenecks. Nevertheless, these protocols couple the congestion control of in-switch aggregator resources and link bandwidth resources, together with the straggler-oblivious manner in aggregator allocation, leading to low aggregation efficiency. To solve the above problem, we propose an Aggregatoraware in-network Aggregation Transmission Protocol (A2TP), which adopts two congestion windows to decouple the congestion control of two resources and combines with the straggling estimation scheme to efficiently allocate aggregator resources according to the straggler degree for multiple jobs, eliminating the impact of straggler jobs on the overall aggregation process. We implement A2TP at P4-programmable switch and a kernel bypass protocol stack at the end-host. The evaluation results show that A2TP reduces the training time by up to 66% than the state-of-the-art INA protocols in real-world benchmark models.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587436"}, {"primary_key": "1135787", "vector": [], "sparse_vector": [], "title": "Lyra: Elastic Scheduling for Deep Learning Clusters.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Organizations often build separate training and inference clusters for deep learning, and use separate schedulers to manage them. This leads to problems for both: inference clusters have low utilization when the traffic load is low; training jobs often experience long queuing due to a lack of resources. We introduce Lyra, a new cluster scheduler to address these problems. Lyra introduces capacity loaning to loan idle inference servers for training jobs. It further exploits elastic scaling that scales a training job's resource allocation to better utilize loaned servers. Capacity loaning and elastic scaling create new challenges to cluster management. When the loaned servers need to be returned, we need to minimize job preemptions; when more GPUs become available, we need to allocate them to elastic jobs and minimize the job completion time (JCT). Lyra addresses these combinatorial problems with principled heuristics. It introduces the notion of server preemption cost, which it greedily reduces during server reclaiming. It further relies on the JCT reduction value defined for each additional worker of an elastic job to solve the scheduling problem as a multiple-choice knapsack problem. Prototype implementation on a 64-GPU testbed and large-scale simulation with 15-day traces of over 50,000 production jobs show that Lyra brings 1.53x and 1.48x reductions in average queuing time and JCT, and improves cluster usage by up to 25%.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587445"}, {"primary_key": "1135788", "vector": [], "sparse_vector": [], "title": "RIO: Order-Preserving and CPU-Efficient Remote Storage Access.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Modern NVMe SSDs and RDMA networks provide dramatically higher bandwidth and concurrency. Existing networked storage systems (e.g., NVMe over Fabrics) fail to fully exploit these new devices due to inefficient storage ordering guarantees. Severe synchronous execution for storage order in these systems stalls the CPU and I/O devices and lowers the CPU and I/O performance efficiency of the storage system. We present Rio, a new approach to the storage order of remote storage access. The key insight in Rio is that the layered design of the software stack, along with the concurrent and asynchronous network and storage devices, makes the storage stack conceptually similar to the CPU pipeline. Inspired by the CPU pipeline that executes out-of-order and commits in-order, Rio introduces the I/O pipeline that allows internal out-of-order and asynchronous execution for ordered write requests while offering intact external storage order to applications. Together with merging consecutive ordered requests, these design decisions make for write throughput and CPU efficiency close to that of orderless requests. We implement Rio in Linux NVMe over RDMA stack, and further build a file system named RioFS atop Rio. Evaluations show that Rio outperforms Linux NVMe over RDMA and a state-of-the-art storage stack named Horae by two orders of magnitude and 4.9 times on average in terms of throughput of ordered write requests, respectively. RioFS increases the throughput of RocksDB by 1.9 times and 1.5 times on average, against Ext4 and HoraeFS, respectively.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567495"}, {"primary_key": "1135789", "vector": [], "sparse_vector": [], "title": "FlexPass: A Case for Flexible Credit-based Transport for Datacenter Networks.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Cho", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Han"], "summary": "Proactive transports explicitly allocate bandwidth to each sender with credits which schedule packet transmission. While promising, existing proactive solutions share a stringent deployment requirement; they assume the perfect control of every link and packet in the network. However, the assumption breaks in practice because new transports are usually deployed gradually over time and legacy traffic always coexists. In this paper, we present FlexPass, a credit-based transport that takes deployment flexibility as a first-class citizen. FlexPass uses a novel combination of network and end-host designs to solve the problem of co-existence and gradual deployment. FlexPass leverages a proactive control loop to send credit-scheduled packets and a complementary reactive control loop to send unscheduled packets to utilize the spare bandwidth. Finally, FlexPass prevents queue buildups of both scheduled and unscheduled packets, and recovers lost packets efficiently. Our evaluation on the testbed shows that FlexPass maintains co-existence with legacy transports (DCTCP), while preserving the high-performance properties of the proactive transport. In large-scale simulations, we show that FlexPass delivers the best incremental benefits during the gradual deployment. We find traffic upgraded to FlexPass benefits from the bounded queue and reduced flow completion time by up to 44% compared to the legacy traffic, while minimizing the side-effect on the legacy flows.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587453"}, {"primary_key": "1135791", "vector": [], "sparse_vector": [], "title": "Understanding and Optimizing Workloads for Unified Resource Management in Large Cloud Platforms.", "authors": ["Chengzhi Lu", "<PERSON><PERSON><PERSON>", "Kejiang Ye", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "To fully utilize computing resources, cloud providers such as Google and Alibaba choose to co-locate online services with batch processing applications in their data centers. By implementing unified resource management policies, different types of complex computing jobs request resources in a consistent way, which can help data centers achieve global optimal scheduling and provide computing power with higher quality. To understand this new scheduling paradigm, in this paper, we first present an in-depth study of Alibaba's unified scheduling workloads. Our study focuses on the characterization of resource utilization, the application running performance, and scheduling scalability. We observe that although computing resources are significantly over-committed under unified scheduling, the resource utilization in Alibaba data centers is still low. In addition, existing resource usage predictors tend to make severe overestimations. At the same time, tasks within the same application behave fairly consistently, and the running performance of tasks can be well-profiled with respect to resource contention on the corresponding physical host.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587437"}, {"primary_key": "1135793", "vector": [], "sparse_vector": [], "title": "Nephele: Extending Virtualization Environments for Cloning Unikernel-based VMs.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Unikernels gained an increasing interest in the recent years because they provide efficient resource allocation and high performance for cloud services by bundling the application with a minimal set of OS services in a guest VM. Although a unikernel is by design small and lightweight, fleets of unikernels based on the same image are not necessarily more efficient than containers because the latter can rely upon OS primitives for sharing memory. Futhermore, porting POSIX applications on top of unikernels brings a new challenge: what does fork() mean in the world of unikernels where there is memory isolation within a VM? Lacking fork() support significantly reduces the applicability of unikernels in popular cloud applications.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587454"}, {"primary_key": "1135796", "vector": [], "sparse_vector": [], "title": "DRAMHiT: A Hash Table Architected for the Speed of DRAM.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "summary": "Despite decades of innovation, existing hash tables fail to achieve peak performance on modern hardware. Built around a relatively simple computation, i.e., a hash function, which in most cases takes only a handful of CPU cycles, hash tables should only be limited by the throughput of the memory subsystem. Unfortunately, due to the inherently random memory access pattern and the contention across multiple threads, existing hash tables spend most of their time waiting for the memory subsystem to serve cache misses and coherence requests.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587457"}, {"primary_key": "1135798", "vector": [], "sparse_vector": [], "title": "Omni-Paxos: Breaking the Barriers of Partial Connectivity.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Paris Carbone"], "summary": "Omni-Paxos is a system for state machine replication that is completely resilient to partial network partitions, a major source of service disruptions in recent years. Omni-Paxos achieves its resilience through a decoupled design that separates the execution and state of leader election from log replication. The leader election builds on the concept of quorum-connected servers, with the sole focus on connectivity. Additionally, by decoupling reconfiguration from log replication, Omni-Paxos provides flexible and parallel log migration that improves the performance and robustness of reconfiguration. Our evaluation showcases two benefits over state-of-the-art protocols: (1) guaranteed recovery in at most four election timeouts under extreme partial network partitions, and (2) up to 8x shorter reconfiguration periods with 46% less I/O at the leader.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587441"}, {"primary_key": "1135800", "vector": [], "sparse_vector": [], "title": "Pocket: M<PERSON> Serving from the Edge.", "authors": ["Misun Park", "<PERSON><PERSON>", "<PERSON> Gavrilovska"], "summary": "One of the major challenges in serving ML applications is the resource pressure introduced by the underlying ML frameworks. This becomes a bigger problem at resource-constrained, multi-tenant edge server locations, where it is necessary to scale to a larger number of clients with a fixed resource envelope. Naive approaches which simply minimize the resource budget allocation of each application result in performance degradation that voids the benefits expected from operating at the edge.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587459"}, {"primary_key": "1135801", "vector": [], "sparse_vector": [], "title": "Safe and Practical GPU Computation in TrustZone.", "authors": ["Heejin Park", "<PERSON>"], "summary": "For mobile devices, it is compelling to run sensitive GPU computation within a TrustZone trusted execution environment (TEE). To minimize GPU software deployed in TEE, the replay approach is promising: record CPU/GPU interactions on a full GPU stack outside the TEE; replay the interactions inside the TEE without the GPU stack. A key dilemma is that the recording process must both (1) occur in a safe environment and (2) access the exact GPU models to be used for replay. To this end, we present a novel recording architecture called GR-T: a mobile device possessing the GPU hardware collaborates with a GPU-less cloud service which runs the GPU software; the two parties exercise the GPU hardware/software jointly for recording. To overcome the resultant network delays, GR-T contributes optimizations: register access deferral, speculation, and meta-only synchronization. These techniques reduce the recording delay by 20x, from hundreds of seconds to tens of seconds. Replay-based GPU computation incurs 25% lower delays compared to native execution outside TEE. The code is available at https://github.com/bakhi/GPUReplay.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567483"}, {"primary_key": "1135803", "vector": [], "sparse_vector": [], "title": "FrozenHot Cache: Rethinking Cache Management for Modern Hardware.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Yang", "Jun<PERSON> Zhang", "<PERSON>", "<PERSON>song Ma", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Caching is crucial for accelerating data access, employed as a ubiquitous design in modern systems at many parts of computer systems. With increasing core count, and shrinking latency gap between cache and modern storage devices, hit-path scalability becomes increasingly critical. However, existing production in-memory caches often use list-based management with promotion on each cache hit, which requires extensive locking and poses a significant overhead for scaling beyond a few cores. Moreover, existing techniques for improving scalability either (1) only focus on the indexing structure and do not improve cache management scalability, or (2) sacrifice efficiency or miss-path scalability.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587446"}, {"primary_key": "1135811", "vector": [], "sparse_vector": [], "title": "NearPM: A Near-Data Processing System for Storage-Class Applications.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Persistent Memory (PM) technologies enable both fast memory access and recovery in case of a failure. To ensure crash-consistent behavior, programs need to enforce persist ordering and employ mechanisms that introduce additional data movements such as logging, checkpointing, and shadow-paging. The emerging near-data processing (NDP) architectures can effectively reduce this overhead. In this work, we propose NearPM, a near-data processor that accelerates common, primitive operations that are crucial to crash consistency. Using these primitives, NearPM accelerates commonly-used crash-consistency mechanisms. NearPM further reduces the synchronization overheads between the NDP and the CPU by handling ordering near memory. We propose Partitioned Persist Ordering (PPO) that ensures a correct persist ordering between CPU and NDP devices, as well as among multiple NDP devices. We prototype NearPM on an FPGA platform. NearPM executes the data-intensive operations of crash-consistency mechanisms with correct ordering guarantees, while the rest of the program runs on the CPU. We evaluate nine PM workloads, each implemented in three crash consistency mechanisms: logging, checkpointing, and shadow paging. Overall, NearPM achieves 4.3 -- 9.8× speedup in the NDP-offloaded operations and 1.22 -- 1.35× speedup in the whole applications.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587456"}, {"primary_key": "1135812", "vector": [], "sparse_vector": [], "title": "vTMM: Tiered Memory Management for Virtual Machines.", "authors": ["<PERSON>", "Chuandong Li", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "The memory demand of virtual machines (VMs) is increasing, while the traditional DRAM-only memory system has limited capacity and high power consumption. The tiered memory system can effectively expand the memory capacity and increase the cost efficiency. Virtualization introduces new challenges for memory tiering, specifically enforcing performance isolation, minimizing context switching, and providing resource overcommit. However, none of the state-of-the-art designs consider virtualization and thus address these challenges; we observe that a VM with tiered memory incurs up to a 2× slowdown compared to a DRAM-only VM.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587449"}, {"primary_key": "1135817", "vector": [], "sparse_vector": [], "title": "WAFFLE: Exposing Memory Ordering Bugs Efficiently with Active Delay Injection.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Concurrency bugs are difficult to detect, reproduce, and diagnose, as they manifest under rare timing conditions. Recently, active delay injection has proven efficient for exposing one such type of bug --- thread-safety violations --- with low overhead, high coverage, and minimal code analysis. However, how to efficiently apply active delay injection to broader classes of concurrency bugs is still an open question.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567507"}, {"primary_key": "1135818", "vector": [], "sparse_vector": [], "title": "Fail through the Cracks: Cross-System Interaction Failures in Modern Cloud Systems.", "authors": ["<PERSON><PERSON>", "Chaitanya Bhandari", "<PERSON><PERSON>", "<PERSON>", "Shuyang Ji", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Modern cloud systems are orchestrations of independent and interacting (sub-)systems, each specializing in important services (e.g., data processing, storage, resource management, etc.). Hence, cloud system reliability is affected not only by the reliability of each individual system, but also by the interplay between these systems. We observe that many recent production incidents of cloud systems are manifested through interactions across the system boundaries. However, there is a lack of systematic understanding of this emerging mode of failures, which we term as cross-system interaction failures (or CSI failures). This hinders the development of better design, integration practices, and new tooling.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587448"}, {"primary_key": "1135820", "vector": [], "sparse_vector": [], "title": "MariusGNN: Resource-Efficient Out-of-Core Training of Graph Neural Networks.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "We study training of Graph Neural Networks (GNNs) for large-scale graphs. We revisit the premise of using distributed training for billion-scale graphs and show that for graphs that fit in main memory or the SSD of a single machine, out-of-core pipelined training with a single GPU can outperform state-of-the-art (SoTA) multi-GPU solutions. We introduce MariusGNN, the first system that utilizes the entire storage hierarchy---including disk---for GNN training. MariusGNN introduces a series of data organization and algorithmic contributions that 1) minimize the end-to-end time required for training and 2) ensure that models learned with disk-based training exhibit accuracy similar to those fully trained in memory. We evaluate MariusGNN against SoTA systems for learning GNN models and find that single-GPU training in MariusGNN achieves the same level of accuracy up to 8× faster than multi-GPU training in these systems, thus, introducing an order of magnitude monetary cost reduction. MariusGNN is open-sourced at www.marius-project.org.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567501"}, {"primary_key": "1135821", "vector": [], "sparse_vector": [], "title": "Integrating Non-Volatile Main Memory in a Deterministic Database.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Deterministic databases provide strong serializability while avoiding concurrency-control related aborts by establishing a serial ordering of transactions before their execution. Recent work has shown that they can also handle skewed and contended workloads effectively. These properties are achieved by batching transactions in epochs and then executing the transactions within an epoch concurrently and deterministically. However, the predetermined serial ordering of transactions makes these databases more vulnerable to long-latency transactions. As a result, they have mainly been designed as main-memory databases, which limits the size of the datasets that can be supported.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567494"}, {"primary_key": "1135822", "vector": [], "sparse_vector": [], "title": "Tabi: An Efficient Multi-Level Inference System for Large Language Models.", "authors": ["<PERSON><PERSON>", "<PERSON>", "Haisheng Tan", "<PERSON><PERSON>"], "summary": "Today's trend of building ever larger language models (LLMs), while pushing the performance of natural language processing, adds significant latency to the inference stage. We observe that due to the diminishing returns of adding parameters to LLMs, a smaller model could make the same prediction as a costly LLM for a majority of queries. Based on this observation, we design Tabi, an inference system with a multi-level inference engine that serves queries using small models and optional LLMs for demanding applications. Tabi is optimized for discriminative models (i.e., not generative LLMs) in a serving framework. Tabi uses the calibrated confidence score to decide whether to return the accurate results of small models extremely fast or re-route them to LLMs. For re-routed queries, it uses attention-based word pruning and weighted ensemble techniques to offset the system overhead and accuracy loss. We implement and evaluate <PERSON><PERSON> with multiple tasks and models. Our result shows that Tabi achieves 21%-40% average latency reduction (with comparable tail latency) over the state-of-the-art while meeting LLM-grade high accuracy targets.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587438"}, {"primary_key": "1135823", "vector": [], "sparse_vector": [], "title": "Model Checking Guided Testing for Distributed Systems.", "authors": ["<PERSON>", "Wensheng Dou", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "summary": "Distributed systems have become the backbone of cloud computing. Incorrect system designs and implementations can greatly impair the reliability of distributed systems. Although a distributed system design modelled in the formal specification can be verified by formal model checking, it is still challenging to figure out whether its corresponding implementation conforms to the verified specification. An incorrect system implementation can violate its verified specification, and causes intricate bugs.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587442"}, {"primary_key": "1135824", "vector": [], "sparse_vector": [], "title": "Hi-Speed DNN Training with <PERSON><PERSON><PERSON><PERSON>: Unleashing the Full Potential of Gradient Compression with Near-Optimal Usage Strategies.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>"], "summary": "Gradient compression (GC) is a promising approach to addressing the communication bottleneck in distributed deep learning (DDL). It saves the communication time, but also incurs additional computation overheads. The training throughput of compression-enabled DDL is determined by the compression strategy, including whether to compress each tensor, the type of compute resources (e.g., CPUs or GPUs) for compression, the communication schemes for compressed tensor, and so on. However, it is challenging to find the optimal compression strategy for applying GC to DDL because of the intricate interactions among tensors. To fully unleash the benefits of GC, two questions must be addressed: 1) How to express any compression strategies and the corresponding interactions among tensors of any DDL training job? 2) How to quickly select a near-optimal compression strategy?", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567505"}, {"primary_key": "1135825", "vector": [], "sparse_vector": [], "title": "Egeria: Efficient DNN Training with Knowledge-Guided Layer Freezing.", "authors": ["<PERSON><PERSON>", "Decang Sun", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Training deep neural networks (DNNs) is time-consuming. While most existing solutions try to overlap/schedule computation and communication for efficient training, this paper goes one step further by skipping computing and communication through DNN layer freezing. Our key insight is that the training progress of internal DNN layers differs significantly, and front layers often become well-trained much earlier than deep layers. To explore this, we first introduce the notion of training plasticity to quantify the training progress of internal DNN layers. Then we design Egeria, a knowledge-guided DNN training system that employs semantic knowledge from a reference model to accurately evaluate individual layers' training plasticity and safely freeze the converged ones, saving their corresponding backward computation and communication. Our reference model is generated on the fly using quantization techniques and runs forward operations asynchronously on available CPUs to minimize the overhead. In addition, Egeria caches the intermediate outputs of the frozen layers with prefetching to further skip the forward computation. Our implementation and testbed experiments with popular vision and language models show that Egeria achieves 19%-43% training speedup w.r.t. the state-of-the-art without sacrificing accuracy.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587451"}, {"primary_key": "1135827", "vector": [], "sparse_vector": [], "title": "LogGrep: Fast and Cheap Cloud Log Storage by Exploiting both Static and Runtime Patterns.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Tingtao Sun", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "In cloud systems, near-line logs are mainly used for debugging, which means they prefer a low query latency for a better user experience, and like any other logs, they also prefer a low overall cost including storage cost to store compressed logs and computation cost to compress logs and execute queries.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567484"}, {"primary_key": "1135829", "vector": [], "sparse_vector": [], "title": "Effective Performance Issue Diagnosis with Value-Assisted Cost Profiling.", "authors": ["<PERSON><PERSON>", "Yigong Hu", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "summary": "Diagnosing performance issues is often difficult, especially when they occur only during some program executions. Profilers can help with performance debugging, but are ineffective when the most costly functions are not the root causes of performance issues. To address this problem, we introduce a new profiling methodology, value-assisted cost profiling, and a tool vProf. Our insight is that capturing the values of variables can greatly help diagnose performance issues. vProf continuously records values while profiling normal and buggy program executions. It identifies anomalies in the values and the functions where they occur to pinpoint the real root causes of performance issues. Using a set of 15 real-world performance bugs in four widely used applications, we show that vProf is effective at diagnosing all of the issues while other state-of-the-art tools diagnose only a few of them. We further use vProf to diagnose longstanding performance issues in these applications that have been unresolved for over four years.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587444"}, {"primary_key": "1135832", "vector": [], "sparse_vector": [], "title": "ALT: Breaking the Wall between Data Layout and Loop Optimizations for Deep Learning Compilation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>peng Dai", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Deep learning models rely on highly optimized tensor libraries for efficient inference on heterogeneous hardware. Current deep compilers typically predetermine layouts of tensors and then optimize loops of operators. However, such unidirectional and one-off workflow strictly separates graph-level optimization and operator-level optimization into different system layers, missing opportunities for unified tuning.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587440"}, {"primary_key": "1135834", "vector": [], "sparse_vector": [], "title": "DyTIS: A Dynamic Dataset Targeted Index Structure Simultaneously Efficient for Search, Insert, and Scan.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>chan Yun", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Many datasets in real life are complex and dynamic, that is, their key densities are varied over the whole key space and their key distributions change over time. It is challenging for an index structure to efficiently support all key operations for data management, in particular, search, insert, and scan, for such dynamic datasets. In this paper, we present DyTIS (Dynamic dataset Targeted Index Structure), an index that targets dynamic datasets. DyTIS, though based on the structure of Extendible hashing, leverages the CDF of the key distribution of a dataset, and learns and adjusts its structure as the dataset grows. The key novelty behind DyTIS is to group keys by the natural key order and maintain keys in sorted order in each bucket to support scan operations within a hash index. We also define what we refer to as a dynamic dataset and propose a means to quantify its dynamic characteristics. Our experimental results show that DyTIS provides higher performance than the state-of-the-art learned index for the dynamic datasets considered.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587434"}, {"primary_key": "1135835", "vector": [], "sparse_vector": [], "title": "Efficient and Safe I/O Operations for Intermittent Systems.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Bashima Islam", "<PERSON>", "<PERSON><PERSON><PERSON>"], "summary": "Task-based intermittent software systems always re-execute peripheral input/output (I/O) operations upon power failures since tasks have all-or-nothing semantics. Re-executed I/O wastes significant time and energy and risks memory inconsistency. This paper presents EaseIO, a new task-based intermittent system that remedies these problems. EaseIO programming interface introduces re-execution semantics for I/O operations to facilitate safe and efficient I/O management for intermittent applications. EaseIO compiler front-end considers the programmer-annotated I/O re-execution semantics to preserve the task's energy efficiency and idem-potency. EaseIO runtime introduces regional privatization to eliminate memory inconsistency caused by idempotence bugs. Our evaluation shows that EaseIO reduces the wasted useful I/O work by up to 3× and total execution time by up to 44% by avoiding 76% of the redundant I/O operations, as compared to the state-of-the-art approaches for intermittent computing. Moreover, for the first time, EaseIO ensures memory consistency during DMA-based I/O operations.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3587435"}, {"primary_key": "1135836", "vector": [], "sparse_vector": [], "title": "DiLOS: Do Not Trade Compatibility for Performance in Memory Disaggregation.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jin<PERSON>ung Oh", "<PERSON>", "<PERSON><PERSON>"], "summary": "Memory disaggregation has replaced the landscape of dat-acenters by physically separating compute and memory nodes, achieving improved utilization. As early efforts, kernel paging-based approaches offer transparent virtual memory abstraction for remote memory with paging schemes but suffer from expensive page fault handling. This paper revisits the paging-based approaches and challenges their performance in paging schemes. We posit that the overhead of the paging-based approaches is not a fundamental limitation. We propose DiLOS, a new library operating system (LibOS) specialized for paging-based memory disaggregation. We have revamped the page fault handler to get away with the swap cache and incorporated known techniques in our prefetcher, page manager, and communication module for performance optimization. Furthermore, we provide APIs to augment the LibOS with application semantics. We present two app-aware guides, app-aware prefetching and bandwidth-reducing memory allocator in DiLOS. Through extensive evaluation of microbenchmarks and applications, we demonstrate that DiLOS outperforms the state-of-the-art kernel paging-based system (Fastswap) up to 2.24× and a recent user-level system (AIFM) 1.54× on a real-world data analytic workload.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567488"}, {"primary_key": "1135838", "vector": [], "sparse_vector": [], "title": "Viper: A Fast Snapshot Isolation Checker.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "summary": "Snapshot isolation (SI) is supported by most commercial databases and is widely used by applications. However, checking SI today---given a set of transactions, checking if they obey SI---is either slow or gives up soundness.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567492"}, {"primary_key": "1135839", "vector": [], "sparse_vector": [], "title": "SiloD: A Co-design of Caching and Scheduling for Deep Learning Clusters.", "authors": ["<PERSON>yu <PERSON>", "Zhenhua Han", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Qianxi Zhang", "Binyang Li", "<PERSON><PERSON> Yang", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "summary": "Deep learning training on cloud platforms usually follows the tradition of the separation of storage and computing. The training executes on a compute cluster equipped with GPUs/TPUs while reading data from a separate cluster hosting the storage service. To alleviate the potential bottleneck, a training cluster usually leverages its local storage as a cache to reduce the remote IO from the storage cluster. However, existing deep learning schedulers do not manage storage resources thus fail to consider the diverse caching effects across different training jobs. This could degrade scheduling quality significantly.", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326.3567499"}, {"primary_key": "1276801", "vector": [], "sparse_vector": [], "title": "Proceedings of the Eighteenth European Conference on Computer Systems, EuroSys 2023, Rome, Italy, May 8-12, 2023", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "summary": "Diagnosing performance issues is often difficult, especially when they occur only during some program executions. Profilers can help with performance debugging, but are ineffective when the most costly functions are not the root causes of performance ...", "published": "2023-01-01", "category": "eurosys", "pdf_url": "", "sub_summary": "", "source": "eurosys", "doi": "10.1145/3552326"}]